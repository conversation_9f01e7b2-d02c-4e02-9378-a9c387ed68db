{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng-lts/api';\n\nfunction AccordionTab_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.header, \" \");\n  }\n}\n\nfunction AccordionTab_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AccordionTab_ng_content_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1, [\"*ngIf\", \"hasHeaderFacet\"]);\n  }\n}\n\nfunction AccordionTab_ng_container_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AccordionTab_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_10_ng_container_1_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.contentTemplate);\n  }\n}\n\nconst _c0 = [\"*\", [[\"p-header\"]]];\n\nconst _c1 = function (a0) {\n  return {\n    \"p-accordion-tab-active\": a0\n  };\n};\n\nconst _c2 = function (a0, a1) {\n  return {\n    \"p-highlight\": a0,\n    \"p-disabled\": a1\n  };\n};\n\nconst _c3 = function (a0) {\n  return {\n    transitionParams: a0\n  };\n};\n\nconst _c4 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nconst _c5 = function (a1) {\n  return {\n    value: \"hidden\",\n    params: a1\n  };\n};\n\nconst _c6 = [\"*\", \"p-header\"];\nconst _c7 = [\"*\"];\nlet idx = 0;\n\nclass AccordionTab {\n  constructor(accordion, changeDetector) {\n    this.changeDetector = changeDetector;\n    this.cache = true;\n    this.selectedChange = new EventEmitter();\n    this.transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    this.id = `p-accordiontab-${idx++}`;\n    this.accordion = accordion;\n  }\n\n  get selected() {\n    return this._selected;\n  }\n\n  set selected(val) {\n    this._selected = val;\n\n    if (!this.loaded) {\n      if (this._selected && this.cache) {\n        this.loaded = true;\n      }\n\n      this.changeDetector.detectChanges();\n    }\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  toggle(event) {\n    if (this.disabled) {\n      return false;\n    }\n\n    let index = this.findTabIndex();\n\n    if (this.selected) {\n      this.selected = false;\n      this.accordion.onClose.emit({\n        originalEvent: event,\n        index: index\n      });\n    } else {\n      if (!this.accordion.multiple) {\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n          if (this.accordion.tabs[i].selected) {\n            this.accordion.tabs[i].selected = false;\n            this.accordion.tabs[i].selectedChange.emit(false);\n            this.accordion.tabs[i].changeDetector.markForCheck();\n          }\n        }\n      }\n\n      this.selected = true;\n      this.loaded = true;\n      this.accordion.onOpen.emit({\n        originalEvent: event,\n        index: index\n      });\n    }\n\n    this.selectedChange.emit(this.selected);\n    this.accordion.updateActiveIndex();\n    this.changeDetector.markForCheck();\n    event.preventDefault();\n  }\n\n  findTabIndex() {\n    let index = -1;\n\n    for (var i = 0; i < this.accordion.tabs.length; i++) {\n      if (this.accordion.tabs[i] == this) {\n        index = i;\n        break;\n      }\n    }\n\n    return index;\n  }\n\n  get hasHeaderFacet() {\n    return this.headerFacet && this.headerFacet.length > 0;\n  }\n\n  onKeydown(event) {\n    if (event.which === 32 || event.which === 13) {\n      this.toggle(event);\n      event.preventDefault();\n    }\n  }\n\n  ngOnDestroy() {\n    this.accordion.tabs.splice(this.findTabIndex(), 1);\n  }\n\n}\n\nAccordionTab.ɵfac = function AccordionTab_Factory(t) {\n  return new (t || AccordionTab)(i0.ɵɵdirectiveInject(forwardRef(() => Accordion)), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nAccordionTab.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: AccordionTab,\n  selectors: [[\"p-accordionTab\"]],\n  contentQueries: function AccordionTab_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, Header, 4);\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    header: \"header\",\n    disabled: \"disabled\",\n    cache: \"cache\",\n    transitionOptions: \"transitionOptions\",\n    selected: \"selected\"\n  },\n  outputs: {\n    selectedChange: \"selectedChange\"\n  },\n  ngContentSelectors: _c6,\n  decls: 11,\n  vars: 28,\n  consts: [[1, \"p-accordion-tab\", 3, \"ngClass\"], [1, \"p-accordion-header\", 3, \"ngClass\"], [\"role\", \"tab\", 1, \"p-accordion-header-link\", 3, \"click\", \"keydown\"], [1, \"p-accordion-toggle-icon\", 3, \"ngClass\"], [\"class\", \"p-accordion-header-text\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [4, \"ngIf\"], [\"role\", \"region\", 1, \"p-toggleable-content\"], [1, \"p-accordion-content\"], [1, \"p-accordion-header-text\"]],\n  template: function AccordionTab_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelementStart(1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"a\", 2);\n      i0.ɵɵlistener(\"click\", function AccordionTab_Template_a_click_2_listener($event) {\n        return ctx.toggle($event);\n      })(\"keydown\", function AccordionTab_Template_a_keydown_2_listener($event) {\n        return ctx.onKeydown($event);\n      });\n      i0.ɵɵelement(3, \"span\", 3);\n      i0.ɵɵtemplate(4, AccordionTab_span_4_Template, 2, 1, \"span\", 4);\n      i0.ɵɵtemplate(5, AccordionTab_ng_container_5_Template, 1, 0, \"ng-container\", 5);\n      i0.ɵɵtemplate(6, AccordionTab_ng_content_6_Template, 1, 0, \"ng-content\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 7);\n      i0.ɵɵelementStart(8, \"div\", 8);\n      i0.ɵɵprojection(9);\n      i0.ɵɵtemplate(10, AccordionTab_ng_container_10_Template, 2, 1, \"ng-container\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c1, ctx.selected));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(17, _c2, ctx.selected, ctx.disabled));\n      i0.ɵɵadvance(1);\n      i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : 0)(\"id\", ctx.id)(\"aria-controls\", ctx.id + \"-content\")(\"aria-expanded\", ctx.selected);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", ctx.selected ? ctx.accordion.collapseIcon : ctx.accordion.expandIcon);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.hasHeaderFacet);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.headerTemplate);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.hasHeaderFacet);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"@tabContent\", ctx.selected ? i0.ɵɵpureFunction1(22, _c4, i0.ɵɵpureFunction1(20, _c3, ctx.transitionOptions)) : i0.ɵɵpureFunction1(26, _c5, i0.ɵɵpureFunction1(24, _c3, ctx.transitionOptions)));\n      i0.ɵɵattribute(\"id\", ctx.id + \"-content\")(\"aria-hidden\", !ctx.selected)(\"aria-labelledby\", ctx.id);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.contentTemplate && (ctx.cache ? ctx.loaded : ctx.selected));\n    }\n  },\n  directives: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet],\n  styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('tabContent', [state('hidden', style({\n      height: '0',\n      overflow: 'hidden'\n    })), state('visible', style({\n      height: '*'\n    })), transition('visible <=> hidden', [style({\n      overflow: 'hidden'\n    }), animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccordionTab, [{\n    type: Component,\n    args: [{\n      selector: 'p-accordionTab',\n      template: `\n        <div class=\"p-accordion-tab\" [ngClass]=\"{'p-accordion-tab-active': selected}\">\n            <div class=\"p-accordion-header\" [ngClass]=\"{'p-highlight': selected, 'p-disabled': disabled}\">\n                <a role=\"tab\" class=\"p-accordion-header-link\" (click)=\"toggle($event)\" (keydown)=\"onKeydown($event)\" [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"id\" [attr.aria-controls]=\"id + '-content'\" [attr.aria-expanded]=\"selected\">\n                    <span class=\"p-accordion-toggle-icon\" [ngClass]=\"selected ? accordion.collapseIcon : accordion.expandIcon\"></span>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{header}}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div [attr.id]=\"id + '-content'\" class=\"p-toggleable-content\" [@tabContent]=\"selected ? {value: 'visible', params: {transitionParams: transitionOptions}} : {value: 'hidden', params: {transitionParams: transitionOptions}}\"\n                role=\"region\" [attr.aria-hidden]=\"!selected\" [attr.aria-labelledby]=\"id\">\n                <div class=\"p-accordion-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n      animations: [trigger('tabContent', [state('hidden', style({\n        height: '0',\n        overflow: 'hidden'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [style({\n        overflow: 'hidden'\n      }), animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      styleUrls: ['./accordion.css'],\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => Accordion)]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    header: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    cache: [{\n      type: Input\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    headerFacet: [{\n      type: ContentChildren,\n      args: [Header]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    selected: [{\n      type: Input\n    }]\n  });\n})();\n\nclass Accordion {\n  constructor(el, changeDetector) {\n    this.el = el;\n    this.changeDetector = changeDetector;\n    this.onClose = new EventEmitter();\n    this.onOpen = new EventEmitter();\n    this.expandIcon = 'pi pi-fw pi-chevron-right';\n    this.collapseIcon = 'pi pi-fw pi-chevron-down';\n    this.activeIndexChange = new EventEmitter();\n    this.tabs = [];\n  }\n\n  ngAfterContentInit() {\n    this.initTabs();\n    this.tabListSubscription = this.tabList.changes.subscribe(_ => {\n      this.initTabs();\n    });\n  }\n\n  initTabs() {\n    this.tabs = this.tabList.toArray();\n    this.updateSelectionState();\n    this.changeDetector.markForCheck();\n  }\n\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n\n  get activeIndex() {\n    return this._activeIndex;\n  }\n\n  set activeIndex(val) {\n    this._activeIndex = val;\n\n    if (this.preventActiveIndexPropagation) {\n      this.preventActiveIndexPropagation = false;\n      return;\n    }\n\n    this.updateSelectionState();\n  }\n\n  updateSelectionState() {\n    if (this.tabs && this.tabs.length && this._activeIndex != null) {\n      for (let i = 0; i < this.tabs.length; i++) {\n        let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n        let changed = selected !== this.tabs[i].selected;\n\n        if (changed) {\n          this.tabs[i].selected = selected;\n          this.tabs[i].selectedChange.emit(selected);\n          this.tabs[i].changeDetector.markForCheck();\n        }\n      }\n    }\n  }\n\n  updateActiveIndex() {\n    let index = this.multiple ? [] : null;\n    this.tabs.forEach((tab, i) => {\n      if (tab.selected) {\n        if (this.multiple) {\n          index.push(i);\n        } else {\n          index = i;\n          return;\n        }\n      }\n    });\n    this.preventActiveIndexPropagation = true;\n    this.activeIndexChange.emit(index);\n  }\n\n  ngOnDestroy() {\n    if (this.tabListSubscription) {\n      this.tabListSubscription.unsubscribe();\n    }\n  }\n\n}\n\nAccordion.ɵfac = function Accordion_Factory(t) {\n  return new (t || Accordion)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nAccordion.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Accordion,\n  selectors: [[\"p-accordion\"]],\n  contentQueries: function Accordion_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, AccordionTab, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabList = _t);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    multiple: \"multiple\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    expandIcon: \"expandIcon\",\n    collapseIcon: \"collapseIcon\",\n    activeIndex: \"activeIndex\"\n  },\n  outputs: {\n    onClose: \"onClose\",\n    onOpen: \"onOpen\",\n    activeIndexChange: \"activeIndexChange\"\n  },\n  ngContentSelectors: _c7,\n  decls: 2,\n  vars: 4,\n  consts: [[\"role\", \"tablist\", 3, \"ngClass\", \"ngStyle\"]],\n  template: function Accordion_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", \"p-accordion p-component\")(\"ngStyle\", ctx.style);\n    }\n  },\n  directives: [i1.NgClass, i1.NgStyle],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Accordion, [{\n    type: Component,\n    args: [{\n      selector: 'p-accordion',\n      template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"tablist\">\n            <ng-content></ng-content>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    multiple: [{\n      type: Input\n    }],\n    onClose: [{\n      type: Output\n    }],\n    onOpen: [{\n      type: Output\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    expandIcon: [{\n      type: Input\n    }],\n    collapseIcon: [{\n      type: Input\n    }],\n    activeIndexChange: [{\n      type: Output\n    }],\n    tabList: [{\n      type: ContentChildren,\n      args: [AccordionTab]\n    }],\n    activeIndex: [{\n      type: Input\n    }]\n  });\n})();\n\nclass AccordionModule {}\n\nAccordionModule.ɵfac = function AccordionModule_Factory(t) {\n  return new (t || AccordionModule)();\n};\n\nAccordionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AccordionModule\n});\nAccordionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule], SharedModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccordionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Accordion, AccordionTab, SharedModule],\n      declarations: [Accordion, AccordionTab]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Accordion, AccordionModule, AccordionTab };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/primeng-lts/fesm2015/primeng-lts-accordion.js"], "names": ["i0", "EventEmitter", "forwardRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ContentChildren", "NgModule", "trigger", "state", "style", "transition", "animate", "i1", "CommonModule", "Header", "PrimeTemplate", "SharedModule", "idx", "AccordionTab", "constructor", "accordion", "changeDetector", "cache", "<PERSON><PERSON><PERSON><PERSON>", "transitionOptions", "id", "selected", "_selected", "val", "loaded", "detectChanges", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "contentTemplate", "template", "headerTemplate", "toggle", "event", "disabled", "index", "findTabIndex", "onClose", "emit", "originalEvent", "multiple", "i", "tabs", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onOpen", "updateActiveIndex", "preventDefault", "hasHeaderFacet", "headerFacet", "onKeydown", "which", "ngOnDestroy", "splice", "ɵfac", "Accordion", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "height", "overflow", "type", "args", "selector", "animations", "changeDetection", "OnPush", "encapsulation", "None", "styleUrls", "host", "undefined", "decorators", "header", "el", "expandIcon", "collapseIcon", "activeIndexChange", "initTabs", "tabListSubscription", "tabList", "changes", "subscribe", "_", "toArray", "updateSelectionState", "getBlockableElement", "nativeElement", "children", "activeIndex", "_activeIndex", "preventActiveIndexPropagation", "includes", "changed", "tab", "push", "unsubscribe", "ElementRef", "NgStyle", "styleClass", "AccordionModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,UAAvB,EAAmCC,SAAnC,EAA8CC,uBAA9C,EAAuEC,iBAAvE,EAA0FC,MAA1F,EAAkGC,KAAlG,EAAyGC,MAAzG,EAAiHC,eAAjH,EAAkIC,QAAlI,QAAkJ,eAAlJ;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,MAAT,EAAiBC,aAAjB,EAAgCC,YAAhC,QAAoD,iBAApD;;;;AA0F+FpB,IAAAA,EAO3E,6B;AAP2EA,IAAAA,EAQvE,U;AARuEA,IAAAA,EAS3E,e;;;;mBAT2EA,E;AAAAA,IAAAA,EAQvE,a;AARuEA,IAAAA,EAQvE,4C;;;;;;AARuEA,IAAAA,EAU3E,sB;;;;;;AAV2EA,IAAAA,EAW3E,gD;;;;;;AAX2EA,IAAAA,EAmBvE,sB;;;;;;AAnBuEA,IAAAA,EAkB3E,2B;AAlB2EA,IAAAA,EAmBvE,6F;AAnBuEA,IAAAA,EAoB3E,wB;;;;mBApB2EA,E;AAAAA,IAAAA,EAmBxD,a;AAnBwDA,IAAAA,EAmBxD,uD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3GvC,IAAIqB,GAAG,GAAG,CAAV;;AACA,MAAMC,YAAN,CAAmB;AACfC,EAAAA,WAAW,CAACC,SAAD,EAAYC,cAAZ,EAA4B;AACnC,SAAKA,cAAL,GAAsBA,cAAtB;AACA,SAAKC,KAAL,GAAa,IAAb;AACA,SAAKC,cAAL,GAAsB,IAAI1B,YAAJ,EAAtB;AACA,SAAK2B,iBAAL,GAAyB,sCAAzB;AACA,SAAKC,EAAL,GAAW,kBAAiBR,GAAG,EAAG,EAAlC;AACA,SAAKG,SAAL,GAAiBA,SAAjB;AACH;;AACW,MAARM,QAAQ,GAAG;AACX,WAAO,KAAKC,SAAZ;AACH;;AACW,MAARD,QAAQ,CAACE,GAAD,EAAM;AACd,SAAKD,SAAL,GAAiBC,GAAjB;;AACA,QAAI,CAAC,KAAKC,MAAV,EAAkB;AACd,UAAI,KAAKF,SAAL,IAAkB,KAAKL,KAA3B,EAAkC;AAC9B,aAAKO,MAAL,GAAc,IAAd;AACH;;AACD,WAAKR,cAAL,CAAoBS,aAApB;AACH;AACJ;;AACDC,EAAAA,kBAAkB,GAAG;AACjB,SAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;AAC7B,cAAQA,IAAI,CAACC,OAAL,EAAR;AACI,aAAK,SAAL;AACI,eAAKC,eAAL,GAAuBF,IAAI,CAACG,QAA5B;AACA;;AACJ,aAAK,QAAL;AACI,eAAKC,cAAL,GAAsBJ,IAAI,CAACG,QAA3B;AACA;;AACJ;AACI,eAAKD,eAAL,GAAuBF,IAAI,CAACG,QAA5B;AACA;AATR;AAWH,KAZD;AAaH;;AACDE,EAAAA,MAAM,CAACC,KAAD,EAAQ;AACV,QAAI,KAAKC,QAAT,EAAmB;AACf,aAAO,KAAP;AACH;;AACD,QAAIC,KAAK,GAAG,KAAKC,YAAL,EAAZ;;AACA,QAAI,KAAKjB,QAAT,EAAmB;AACf,WAAKA,QAAL,GAAgB,KAAhB;AACA,WAAKN,SAAL,CAAewB,OAAf,CAAuBC,IAAvB,CAA4B;AAAEC,QAAAA,aAAa,EAAEN,KAAjB;AAAwBE,QAAAA,KAAK,EAAEA;AAA/B,OAA5B;AACH,KAHD,MAIK;AACD,UAAI,CAAC,KAAKtB,SAAL,CAAe2B,QAApB,EAA8B;AAC1B,aAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5B,SAAL,CAAe6B,IAAf,CAAoBC,MAAxC,EAAgDF,CAAC,EAAjD,EAAqD;AACjD,cAAI,KAAK5B,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,EAAuBtB,QAA3B,EAAqC;AACjC,iBAAKN,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,EAAuBtB,QAAvB,GAAkC,KAAlC;AACA,iBAAKN,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,EAAuBzB,cAAvB,CAAsCsB,IAAtC,CAA2C,KAA3C;AACA,iBAAKzB,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,EAAuB3B,cAAvB,CAAsC8B,YAAtC;AACH;AACJ;AACJ;;AACD,WAAKzB,QAAL,GAAgB,IAAhB;AACA,WAAKG,MAAL,GAAc,IAAd;AACA,WAAKT,SAAL,CAAegC,MAAf,CAAsBP,IAAtB,CAA2B;AAAEC,QAAAA,aAAa,EAAEN,KAAjB;AAAwBE,QAAAA,KAAK,EAAEA;AAA/B,OAA3B;AACH;;AACD,SAAKnB,cAAL,CAAoBsB,IAApB,CAAyB,KAAKnB,QAA9B;AACA,SAAKN,SAAL,CAAeiC,iBAAf;AACA,SAAKhC,cAAL,CAAoB8B,YAApB;AACAX,IAAAA,KAAK,CAACc,cAAN;AACH;;AACDX,EAAAA,YAAY,GAAG;AACX,QAAID,KAAK,GAAG,CAAC,CAAb;;AACA,SAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5B,SAAL,CAAe6B,IAAf,CAAoBC,MAAxC,EAAgDF,CAAC,EAAjD,EAAqD;AACjD,UAAI,KAAK5B,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,KAA0B,IAA9B,EAAoC;AAChCN,QAAAA,KAAK,GAAGM,CAAR;AACA;AACH;AACJ;;AACD,WAAON,KAAP;AACH;;AACiB,MAAda,cAAc,GAAG;AACjB,WAAO,KAAKC,WAAL,IAAoB,KAAKA,WAAL,CAAiBN,MAAjB,GAA0B,CAArD;AACH;;AACDO,EAAAA,SAAS,CAACjB,KAAD,EAAQ;AACb,QAAIA,KAAK,CAACkB,KAAN,KAAgB,EAAhB,IAAsBlB,KAAK,CAACkB,KAAN,KAAgB,EAA1C,EAA8C;AAC1C,WAAKnB,MAAL,CAAYC,KAAZ;AACAA,MAAAA,KAAK,CAACc,cAAN;AACH;AACJ;;AACDK,EAAAA,WAAW,GAAG;AACV,SAAKvC,SAAL,CAAe6B,IAAf,CAAoBW,MAApB,CAA2B,KAAKjB,YAAL,EAA3B,EAAgD,CAAhD;AACH;;AArFc;;AAuFnBzB,YAAY,CAAC2C,IAAb;AAAA,mBAAyG3C,YAAzG,EAA+FtB,EAA/F,mBAAuIE,UAAU,CAAC,MAAMgE,SAAP,CAAjJ,GAA+FlE,EAA/F,mBAA+KA,EAAE,CAACmE,iBAAlL;AAAA;;AACA7C,YAAY,CAAC8C,IAAb,kBAD+FpE,EAC/F;AAAA,QAA6FsB,YAA7F;AAAA;AAAA;AAAA;AAD+FtB,MAAAA,EAC/F,0BAAmZkB,MAAnZ;AAD+FlB,MAAAA,EAC/F,0BAAqcmB,aAArc;AAAA;;AAAA;AAAA;;AAD+FnB,MAAAA,EAC/F,qBAD+FA,EAC/F;AAD+FA,MAAAA,EAC/F,qBAD+FA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAD+FA,MAAAA,EAC/F;AAD+FA,MAAAA,EAEvF,4BADR;AAD+FA,MAAAA,EAGnF,4BAFZ;AAD+FA,MAAAA,EAI/E,0BAHhB;AAD+FA,MAAAA,EAIjC;AAAA,eAAS,kBAAT;AAAA;AAAA,eAAoC,qBAApC;AAAA,QAH9D;AAD+FA,MAAAA,EAM3E,wBALpB;AAD+FA,MAAAA,EAO3E,6DANpB;AAD+FA,MAAAA,EAU3E,6EATpB;AAD+FA,MAAAA,EAW3E,yEAVpB;AAD+FA,MAAAA,EAY/E,eAXhB;AAD+FA,MAAAA,EAanF,eAZZ;AAD+FA,MAAAA,EAcnF,4BAbZ;AAD+FA,MAAAA,EAgB/E,4BAfhB;AAD+FA,MAAAA,EAiB3E,gBAhBpB;AAD+FA,MAAAA,EAkB3E,+EAjBpB;AAD+FA,MAAAA,EAqB/E,eApBhB;AAD+FA,MAAAA,EAsBnF,eArBZ;AAD+FA,MAAAA,EAuBvF,eAtBR;AAAA;;AAAA;AAD+FA,MAAAA,EAE1D,uBAF0DA,EAE1D,wCADrC;AAD+FA,MAAAA,EAGnD,aAF5C;AAD+FA,MAAAA,EAGnD,uBAHmDA,EAGnD,sDAF5C;AAD+FA,MAAAA,EAIsB,aAHrH;AAD+FA,MAAAA,EAIsB,oIAHrH;AAD+FA,MAAAA,EAMrC,aAL1D;AAD+FA,MAAAA,EAMrC,4FAL1D;AAD+FA,MAAAA,EAOpC,aAN3D;AAD+FA,MAAAA,EAOpC,wCAN3D;AAD+FA,MAAAA,EAU5D,aATnC;AAD+FA,MAAAA,EAU5D,mDATnC;AAD+FA,MAAAA,EAW5C,aAVnD;AAD+FA,MAAAA,EAW5C,uCAVnD;AAD+FA,MAAAA,EAcrB,aAb1E;AAD+FA,MAAAA,EAcrB,0CAdqBA,EAcrB,0BAdqBA,EAcrB,oDAdqBA,EAcrB,0BAdqBA,EAcrB,kDAb1E;AAD+FA,MAAAA,EAc9E,gGAbjB;AAD+FA,MAAAA,EAkB5D,aAjBnC;AAD+FA,MAAAA,EAkB5D,mFAjBnC;AAAA;AAAA;AAAA,eAuB8UgB,EAAE,CAACqD,OAvBjV,EAuByZrD,EAAE,CAACsD,IAvB5Z,EAuB0etD,EAAE,CAACuD,gBAvB7e;AAAA;AAAA;AAAA;AAAA,eAuBumB,CAC/lB5D,OAAO,CAAC,YAAD,EAAe,CAClBC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;AAClB2D,MAAAA,MAAM,EAAE,GADU;AAElBC,MAAAA,QAAQ,EAAE;AAFQ,KAAD,CAAhB,CADa,EAKlB7D,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;AACnB2D,MAAAA,MAAM,EAAE;AADW,KAAD,CAAjB,CALa,EAQlB1D,UAAU,CAAC,oBAAD,EAAuB,CAACD,KAAK,CAAC;AAAE4D,MAAAA,QAAQ,EAAE;AAAZ,KAAD,CAAN,EAAgC1D,OAAO,CAAC,sBAAD,CAAvC,CAAvB,CARQ,EASlBD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CATQ,CAAf,CADwlB;AAvBvmB;AAAA;AAAA;;AAoCA;AAAA,qDArC+Ff,EAqC/F,mBAA2FsB,YAA3F,EAAqH,CAAC;AAC1GoD,IAAAA,IAAI,EAAEvE,SADoG;AAE1GwE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,gBADX;AAECnC,MAAAA,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAzBmB;AA0BCoC,MAAAA,UAAU,EAAE,CACRlE,OAAO,CAAC,YAAD,EAAe,CAClBC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;AAClB2D,QAAAA,MAAM,EAAE,GADU;AAElBC,QAAAA,QAAQ,EAAE;AAFQ,OAAD,CAAhB,CADa,EAKlB7D,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;AACnB2D,QAAAA,MAAM,EAAE;AADW,OAAD,CAAjB,CALa,EAQlB1D,UAAU,CAAC,oBAAD,EAAuB,CAACD,KAAK,CAAC;AAAE4D,QAAAA,QAAQ,EAAE;AAAZ,OAAD,CAAN,EAAgC1D,OAAO,CAAC,sBAAD,CAAvC,CAAvB,CARQ,EASlBD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CATQ,CAAf,CADC,CA1Bb;AAuCC+D,MAAAA,eAAe,EAAE1E,uBAAuB,CAAC2E,MAvC1C;AAwCCC,MAAAA,aAAa,EAAE3E,iBAAiB,CAAC4E,IAxClC;AAyCCC,MAAAA,SAAS,EAAE,CAAC,iBAAD,CAzCZ;AA0CCC,MAAAA,IAAI,EAAE;AACF,iBAAS;AADP;AA1CP,KAAD;AAFoG,GAAD,CAArH,EAgD4B,YAAY;AAAE,WAAO,CAAC;AAAET,MAAAA,IAAI,EAAEU,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DX,QAAAA,IAAI,EAAEpE,MADwD;AAE9DqE,QAAAA,IAAI,EAAE,CAACzE,UAAU,CAAC,MAAMgE,SAAP,CAAX;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEQ,MAAAA,IAAI,EAAE1E,EAAE,CAACmE;AAAX,KAH2B,CAAP;AAGc,GAnDxD,EAmD0E;AAAEmB,IAAAA,MAAM,EAAE,CAAC;AACrEZ,MAAAA,IAAI,EAAEnE;AAD+D,KAAD,CAAV;AAE1DsC,IAAAA,QAAQ,EAAE,CAAC;AACX6B,MAAAA,IAAI,EAAEnE;AADK,KAAD,CAFgD;AAI1DmB,IAAAA,KAAK,EAAE,CAAC;AACRgD,MAAAA,IAAI,EAAEnE;AADE,KAAD,CAJmD;AAM1DoB,IAAAA,cAAc,EAAE,CAAC;AACjB+C,MAAAA,IAAI,EAAElE;AADW,KAAD,CAN0C;AAQ1DoB,IAAAA,iBAAiB,EAAE,CAAC;AACpB8C,MAAAA,IAAI,EAAEnE;AADc,KAAD,CARuC;AAU1DqD,IAAAA,WAAW,EAAE,CAAC;AACdc,MAAAA,IAAI,EAAEjE,eADQ;AAEdkE,MAAAA,IAAI,EAAE,CAACzD,MAAD;AAFQ,KAAD,CAV6C;AAa1DkB,IAAAA,SAAS,EAAE,CAAC;AACZsC,MAAAA,IAAI,EAAEjE,eADM;AAEZkE,MAAAA,IAAI,EAAE,CAACxD,aAAD;AAFM,KAAD,CAb+C;AAgB1DW,IAAAA,QAAQ,EAAE,CAAC;AACX4C,MAAAA,IAAI,EAAEnE;AADK,KAAD;AAhBgD,GAnD1E;AAAA;;AAsEA,MAAM2D,SAAN,CAAgB;AACZ3C,EAAAA,WAAW,CAACgE,EAAD,EAAK9D,cAAL,EAAqB;AAC5B,SAAK8D,EAAL,GAAUA,EAAV;AACA,SAAK9D,cAAL,GAAsBA,cAAtB;AACA,SAAKuB,OAAL,GAAe,IAAI/C,YAAJ,EAAf;AACA,SAAKuD,MAAL,GAAc,IAAIvD,YAAJ,EAAd;AACA,SAAKuF,UAAL,GAAkB,2BAAlB;AACA,SAAKC,YAAL,GAAoB,0BAApB;AACA,SAAKC,iBAAL,GAAyB,IAAIzF,YAAJ,EAAzB;AACA,SAAKoD,IAAL,GAAY,EAAZ;AACH;;AACDlB,EAAAA,kBAAkB,GAAG;AACjB,SAAKwD,QAAL;AACA,SAAKC,mBAAL,GAA2B,KAAKC,OAAL,CAAaC,OAAb,CAAqBC,SAArB,CAA+BC,CAAC,IAAI;AAC3D,WAAKL,QAAL;AACH,KAF0B,CAA3B;AAGH;;AACDA,EAAAA,QAAQ,GAAG;AACP,SAAKtC,IAAL,GAAY,KAAKwC,OAAL,CAAaI,OAAb,EAAZ;AACA,SAAKC,oBAAL;AACA,SAAKzE,cAAL,CAAoB8B,YAApB;AACH;;AACD4C,EAAAA,mBAAmB,GAAG;AAClB,WAAO,KAAKZ,EAAL,CAAQa,aAAR,CAAsBC,QAAtB,CAA+B,CAA/B,CAAP;AACH;;AACc,MAAXC,WAAW,GAAG;AACd,WAAO,KAAKC,YAAZ;AACH;;AACc,MAAXD,WAAW,CAACtE,GAAD,EAAM;AACjB,SAAKuE,YAAL,GAAoBvE,GAApB;;AACA,QAAI,KAAKwE,6BAAT,EAAwC;AACpC,WAAKA,6BAAL,GAAqC,KAArC;AACA;AACH;;AACD,SAAKN,oBAAL;AACH;;AACDA,EAAAA,oBAAoB,GAAG;AACnB,QAAI,KAAK7C,IAAL,IAAa,KAAKA,IAAL,CAAUC,MAAvB,IAAiC,KAAKiD,YAAL,IAAqB,IAA1D,EAAgE;AAC5D,WAAK,IAAInD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,IAAL,CAAUC,MAA9B,EAAsCF,CAAC,EAAvC,EAA2C;AACvC,YAAItB,QAAQ,GAAG,KAAKqB,QAAL,GAAgB,KAAKoD,YAAL,CAAkBE,QAAlB,CAA2BrD,CAA3B,CAAhB,GAAiDA,CAAC,KAAK,KAAKmD,YAA3E;AACA,YAAIG,OAAO,GAAG5E,QAAQ,KAAK,KAAKuB,IAAL,CAAUD,CAAV,EAAatB,QAAxC;;AACA,YAAI4E,OAAJ,EAAa;AACT,eAAKrD,IAAL,CAAUD,CAAV,EAAatB,QAAb,GAAwBA,QAAxB;AACA,eAAKuB,IAAL,CAAUD,CAAV,EAAazB,cAAb,CAA4BsB,IAA5B,CAAiCnB,QAAjC;AACA,eAAKuB,IAAL,CAAUD,CAAV,EAAa3B,cAAb,CAA4B8B,YAA5B;AACH;AACJ;AACJ;AACJ;;AACDE,EAAAA,iBAAiB,GAAG;AAChB,QAAIX,KAAK,GAAG,KAAKK,QAAL,GAAgB,EAAhB,GAAqB,IAAjC;AACA,SAAKE,IAAL,CAAUhB,OAAV,CAAkB,CAACsE,GAAD,EAAMvD,CAAN,KAAY;AAC1B,UAAIuD,GAAG,CAAC7E,QAAR,EAAkB;AACd,YAAI,KAAKqB,QAAT,EAAmB;AACfL,UAAAA,KAAK,CAAC8D,IAAN,CAAWxD,CAAX;AACH,SAFD,MAGK;AACDN,UAAAA,KAAK,GAAGM,CAAR;AACA;AACH;AACJ;AACJ,KAVD;AAWA,SAAKoD,6BAAL,GAAqC,IAArC;AACA,SAAKd,iBAAL,CAAuBzC,IAAvB,CAA4BH,KAA5B;AACH;;AACDiB,EAAAA,WAAW,GAAG;AACV,QAAI,KAAK6B,mBAAT,EAA8B;AAC1B,WAAKA,mBAAL,CAAyBiB,WAAzB;AACH;AACJ;;AArEW;;AAuEhB3C,SAAS,CAACD,IAAV;AAAA,mBAAsGC,SAAtG,EAlL+FlE,EAkL/F,mBAAiIA,EAAE,CAAC8G,UAApI,GAlL+F9G,EAkL/F,mBAA2JA,EAAE,CAACmE,iBAA9J;AAAA;;AACAD,SAAS,CAACE,IAAV,kBAnL+FpE,EAmL/F;AAAA,QAA0FkE,SAA1F;AAAA;AAAA;AAAA;AAnL+FlE,MAAAA,EAmL/F,0BAAgdsB,YAAhd;AAAA;;AAAA;AAAA;;AAnL+FtB,MAAAA,EAmL/F,qBAnL+FA,EAmL/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAnL+FA,MAAAA,EAmL/F;AAnL+FA,MAAAA,EAoLvF,4BADR;AAnL+FA,MAAAA,EAqLnF,gBAFZ;AAnL+FA,MAAAA,EAsLvF,eAHR;AAAA;;AAAA;AAnL+FA,MAAAA,EAoL1B,2BADrE;AAnL+FA,MAAAA,EAoLlF,uEADb;AAAA;AAAA;AAAA,eAI4CgB,EAAE,CAACqD,OAJ/C,EAIuHrD,EAAE,CAAC+F,OAJ1H;AAAA;AAAA;AAAA;;AAKA;AAAA,qDAxL+F/G,EAwL/F,mBAA2FkE,SAA3F,EAAkH,CAAC;AACvGQ,IAAAA,IAAI,EAAEvE,SADiG;AAEvGwE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,aADX;AAECnC,MAAAA,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,KANmB;AAOCqC,MAAAA,eAAe,EAAE1E,uBAAuB,CAAC2E,MAP1C;AAQCI,MAAAA,IAAI,EAAE;AACF,iBAAS;AADP;AARP,KAAD;AAFiG,GAAD,CAAlH,EAc4B,YAAY;AAAE,WAAO,CAAC;AAAET,MAAAA,IAAI,EAAE1E,EAAE,CAAC8G;AAAX,KAAD,EAA0B;AAAEpC,MAAAA,IAAI,EAAE1E,EAAE,CAACmE;AAAX,KAA1B,CAAP;AAAmE,GAd7G,EAc+H;AAAEhB,IAAAA,QAAQ,EAAE,CAAC;AAC5HuB,MAAAA,IAAI,EAAEnE;AADsH,KAAD,CAAZ;AAE/GyC,IAAAA,OAAO,EAAE,CAAC;AACV0B,MAAAA,IAAI,EAAElE;AADI,KAAD,CAFsG;AAI/GgD,IAAAA,MAAM,EAAE,CAAC;AACTkB,MAAAA,IAAI,EAAElE;AADG,KAAD,CAJuG;AAM/GK,IAAAA,KAAK,EAAE,CAAC;AACR6D,MAAAA,IAAI,EAAEnE;AADE,KAAD,CANwG;AAQ/GyG,IAAAA,UAAU,EAAE,CAAC;AACbtC,MAAAA,IAAI,EAAEnE;AADO,KAAD,CARmG;AAU/GiF,IAAAA,UAAU,EAAE,CAAC;AACbd,MAAAA,IAAI,EAAEnE;AADO,KAAD,CAVmG;AAY/GkF,IAAAA,YAAY,EAAE,CAAC;AACff,MAAAA,IAAI,EAAEnE;AADS,KAAD,CAZiG;AAc/GmF,IAAAA,iBAAiB,EAAE,CAAC;AACpBhB,MAAAA,IAAI,EAAElE;AADc,KAAD,CAd4F;AAgB/GqF,IAAAA,OAAO,EAAE,CAAC;AACVnB,MAAAA,IAAI,EAAEjE,eADI;AAEVkE,MAAAA,IAAI,EAAE,CAACrD,YAAD;AAFI,KAAD,CAhBsG;AAmB/GgF,IAAAA,WAAW,EAAE,CAAC;AACd5B,MAAAA,IAAI,EAAEnE;AADQ,KAAD;AAnBkG,GAd/H;AAAA;;AAoCA,MAAM0G,eAAN,CAAsB;;AAEtBA,eAAe,CAAChD,IAAhB;AAAA,mBAA4GgD,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBA/N+FlH,EA+N/F;AAAA,QAA6GiH;AAA7G;AACAA,eAAe,CAACE,IAAhB,kBAhO+FnH,EAgO/F;AAAA,YAAwI,CAACiB,YAAD,CAAxI,EAAwJG,YAAxJ;AAAA;;AACA;AAAA,qDAjO+FpB,EAiO/F,mBAA2FiH,eAA3F,EAAwH,CAAC;AAC7GvC,IAAAA,IAAI,EAAEhE,QADuG;AAE7GiE,IAAAA,IAAI,EAAE,CAAC;AACCyC,MAAAA,OAAO,EAAE,CAACnG,YAAD,CADV;AAECoG,MAAAA,OAAO,EAAE,CAACnD,SAAD,EAAY5C,YAAZ,EAA0BF,YAA1B,CAFV;AAGCkG,MAAAA,YAAY,EAAE,CAACpD,SAAD,EAAY5C,YAAZ;AAHf,KAAD;AAFuG,GAAD,CAAxH;AAAA;AASA;AACA;AACA;;;AAEA,SAAS4C,SAAT,EAAoB+C,eAApB,EAAqC3F,YAArC", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng-lts/api';\n\nlet idx = 0;\nclass AccordionTab {\n    constructor(accordion, changeDetector) {\n        this.changeDetector = changeDetector;\n        this.cache = true;\n        this.selectedChange = new EventEmitter();\n        this.transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n        this.id = `p-accordiontab-${idx++}`;\n        this.accordion = accordion;\n    }\n    get selected() {\n        return this._selected;\n    }\n    set selected(val) {\n        this._selected = val;\n        if (!this.loaded) {\n            if (this._selected && this.cache) {\n                this.loaded = true;\n            }\n            this.changeDetector.detectChanges();\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    toggle(event) {\n        if (this.disabled) {\n            return false;\n        }\n        let index = this.findTabIndex();\n        if (this.selected) {\n            this.selected = false;\n            this.accordion.onClose.emit({ originalEvent: event, index: index });\n        }\n        else {\n            if (!this.accordion.multiple) {\n                for (var i = 0; i < this.accordion.tabs.length; i++) {\n                    if (this.accordion.tabs[i].selected) {\n                        this.accordion.tabs[i].selected = false;\n                        this.accordion.tabs[i].selectedChange.emit(false);\n                        this.accordion.tabs[i].changeDetector.markForCheck();\n                    }\n                }\n            }\n            this.selected = true;\n            this.loaded = true;\n            this.accordion.onOpen.emit({ originalEvent: event, index: index });\n        }\n        this.selectedChange.emit(this.selected);\n        this.accordion.updateActiveIndex();\n        this.changeDetector.markForCheck();\n        event.preventDefault();\n    }\n    findTabIndex() {\n        let index = -1;\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n            if (this.accordion.tabs[i] == this) {\n                index = i;\n                break;\n            }\n        }\n        return index;\n    }\n    get hasHeaderFacet() {\n        return this.headerFacet && this.headerFacet.length > 0;\n    }\n    onKeydown(event) {\n        if (event.which === 32 || event.which === 13) {\n            this.toggle(event);\n            event.preventDefault();\n        }\n    }\n    ngOnDestroy() {\n        this.accordion.tabs.splice(this.findTabIndex(), 1);\n    }\n}\nAccordionTab.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: AccordionTab, deps: [{ token: forwardRef(() => Accordion) }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nAccordionTab.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.0.5\", type: AccordionTab, selector: \"p-accordionTab\", inputs: { header: \"header\", disabled: \"disabled\", cache: \"cache\", transitionOptions: \"transitionOptions\", selected: \"selected\" }, outputs: { selectedChange: \"selectedChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"headerFacet\", predicate: Header }, { propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div class=\"p-accordion-tab\" [ngClass]=\"{'p-accordion-tab-active': selected}\">\n            <div class=\"p-accordion-header\" [ngClass]=\"{'p-highlight': selected, 'p-disabled': disabled}\">\n                <a role=\"tab\" class=\"p-accordion-header-link\" (click)=\"toggle($event)\" (keydown)=\"onKeydown($event)\" [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"id\" [attr.aria-controls]=\"id + '-content'\" [attr.aria-expanded]=\"selected\">\n                    <span class=\"p-accordion-toggle-icon\" [ngClass]=\"selected ? accordion.collapseIcon : accordion.expandIcon\"></span>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{header}}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div [attr.id]=\"id + '-content'\" class=\"p-toggleable-content\" [@tabContent]=\"selected ? {value: 'visible', params: {transitionParams: transitionOptions}} : {value: 'hidden', params: {transitionParams: transitionOptions}}\"\n                role=\"region\" [attr.aria-hidden]=\"!selected\" [attr.aria-labelledby]=\"id\">\n                <div class=\"p-accordion-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `, isInline: true, styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}\"], directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }], animations: [\n        trigger('tabContent', [\n            state('hidden', style({\n                height: '0',\n                overflow: 'hidden'\n            })),\n            state('visible', style({\n                height: '*'\n            })),\n            transition('visible <=> hidden', [style({ overflow: 'hidden' }), animate('{{transitionParams}}')]),\n            transition('void => *', animate(0))\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: AccordionTab, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-accordionTab',\n                    template: `\n        <div class=\"p-accordion-tab\" [ngClass]=\"{'p-accordion-tab-active': selected}\">\n            <div class=\"p-accordion-header\" [ngClass]=\"{'p-highlight': selected, 'p-disabled': disabled}\">\n                <a role=\"tab\" class=\"p-accordion-header-link\" (click)=\"toggle($event)\" (keydown)=\"onKeydown($event)\" [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"id\" [attr.aria-controls]=\"id + '-content'\" [attr.aria-expanded]=\"selected\">\n                    <span class=\"p-accordion-toggle-icon\" [ngClass]=\"selected ? accordion.collapseIcon : accordion.expandIcon\"></span>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{header}}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div [attr.id]=\"id + '-content'\" class=\"p-toggleable-content\" [@tabContent]=\"selected ? {value: 'visible', params: {transitionParams: transitionOptions}} : {value: 'hidden', params: {transitionParams: transitionOptions}}\"\n                role=\"region\" [attr.aria-hidden]=\"!selected\" [attr.aria-labelledby]=\"id\">\n                <div class=\"p-accordion-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n                    animations: [\n                        trigger('tabContent', [\n                            state('hidden', style({\n                                height: '0',\n                                overflow: 'hidden'\n                            })),\n                            state('visible', style({\n                                height: '*'\n                            })),\n                            transition('visible <=> hidden', [style({ overflow: 'hidden' }), animate('{{transitionParams}}')]),\n                            transition('void => *', animate(0))\n                        ])\n                    ],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    styleUrls: ['./accordion.css'],\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => Accordion)]\n                }] }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { header: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], cache: [{\n                type: Input\n            }], selectedChange: [{\n                type: Output\n            }], transitionOptions: [{\n                type: Input\n            }], headerFacet: [{\n                type: ContentChildren,\n                args: [Header]\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], selected: [{\n                type: Input\n            }] } });\nclass Accordion {\n    constructor(el, changeDetector) {\n        this.el = el;\n        this.changeDetector = changeDetector;\n        this.onClose = new EventEmitter();\n        this.onOpen = new EventEmitter();\n        this.expandIcon = 'pi pi-fw pi-chevron-right';\n        this.collapseIcon = 'pi pi-fw pi-chevron-down';\n        this.activeIndexChange = new EventEmitter();\n        this.tabs = [];\n    }\n    ngAfterContentInit() {\n        this.initTabs();\n        this.tabListSubscription = this.tabList.changes.subscribe(_ => {\n            this.initTabs();\n        });\n    }\n    initTabs() {\n        this.tabs = this.tabList.toArray();\n        this.updateSelectionState();\n        this.changeDetector.markForCheck();\n    }\n    getBlockableElement() {\n        return this.el.nativeElement.children[0];\n    }\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    set activeIndex(val) {\n        this._activeIndex = val;\n        if (this.preventActiveIndexPropagation) {\n            this.preventActiveIndexPropagation = false;\n            return;\n        }\n        this.updateSelectionState();\n    }\n    updateSelectionState() {\n        if (this.tabs && this.tabs.length && this._activeIndex != null) {\n            for (let i = 0; i < this.tabs.length; i++) {\n                let selected = this.multiple ? this._activeIndex.includes(i) : (i === this._activeIndex);\n                let changed = selected !== this.tabs[i].selected;\n                if (changed) {\n                    this.tabs[i].selected = selected;\n                    this.tabs[i].selectedChange.emit(selected);\n                    this.tabs[i].changeDetector.markForCheck();\n                }\n            }\n        }\n    }\n    updateActiveIndex() {\n        let index = this.multiple ? [] : null;\n        this.tabs.forEach((tab, i) => {\n            if (tab.selected) {\n                if (this.multiple) {\n                    index.push(i);\n                }\n                else {\n                    index = i;\n                    return;\n                }\n            }\n        });\n        this.preventActiveIndexPropagation = true;\n        this.activeIndexChange.emit(index);\n    }\n    ngOnDestroy() {\n        if (this.tabListSubscription) {\n            this.tabListSubscription.unsubscribe();\n        }\n    }\n}\nAccordion.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: Accordion, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nAccordion.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.0.5\", type: Accordion, selector: \"p-accordion\", inputs: { multiple: \"multiple\", style: \"style\", styleClass: \"styleClass\", expandIcon: \"expandIcon\", collapseIcon: \"collapseIcon\", activeIndex: \"activeIndex\" }, outputs: { onClose: \"onClose\", onOpen: \"onOpen\", activeIndexChange: \"activeIndexChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"tabList\", predicate: AccordionTab }], ngImport: i0, template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"tablist\">\n            <ng-content></ng-content>\n        </div>\n    `, isInline: true, directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: Accordion, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-accordion',\n                    template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"tablist\">\n            <ng-content></ng-content>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { multiple: [{\n                type: Input\n            }], onClose: [{\n                type: Output\n            }], onOpen: [{\n                type: Output\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], expandIcon: [{\n                type: Input\n            }], collapseIcon: [{\n                type: Input\n            }], activeIndexChange: [{\n                type: Output\n            }], tabList: [{\n                type: ContentChildren,\n                args: [AccordionTab]\n            }], activeIndex: [{\n                type: Input\n            }] } });\nclass AccordionModule {\n}\nAccordionModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: AccordionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAccordionModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: AccordionModule, declarations: [Accordion, AccordionTab], imports: [CommonModule], exports: [Accordion, AccordionTab, SharedModule] });\nAccordionModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: AccordionModule, imports: [[CommonModule], SharedModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: AccordionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Accordion, AccordionTab, SharedModule],\n                    declarations: [Accordion, AccordionTab]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Accordion, AccordionModule, AccordionTab };\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period = 0, scheduler = asyncScheduler) {\n  if (period < 0) {\n    period = 0;\n  }\n\n  return timer(period, period, scheduler);\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/observable/interval.js"], "names": ["asyncScheduler", "timer", "interval", "period", "scheduler"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,QAAT,CAAkBC,MAAM,GAAG,CAA3B,EAA8BC,SAAS,GAAGJ,cAA1C,EAA0D;AAC7D,MAAIG,MAAM,GAAG,CAAb,EAAgB;AACZA,IAAAA,MAAM,GAAG,CAAT;AACH;;AACD,SAAOF,KAAK,CAACE,MAAD,EAASA,MAAT,EAAiBC,SAAjB,CAAZ;AACH", "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { timer } from './timer';\nexport function interval(period = 0, scheduler = asyncScheduler) {\n    if (period < 0) {\n        period = 0;\n    }\n    return timer(period, period, scheduler);\n}\n"]}, "metadata": {}, "sourceType": "module"}
import { Component, OnInit } from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormArray,
  FormControl,
  AbstractControl,
  ValidatorFn,
  ValidationErrors,
} from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
import { FileValidator } from 'src/app/shared/validators/fileValidator.validator';

function wordLimitValidator(maxWords: number) {
  return (control: AbstractControl): { [key: string]: any } | null => {
    if (control.value) {
      const words = control?.value.trim().split(/\s+/);
      if (words.length > maxWords) {
        console.log('maxwords', maxWords);
        return { wordLimitExceeded: true, wordCount: words.length };
      }
    }
    return null;
  };
}
@Component({  
  selector: 'app-add-edit-sector',
  templateUrl: './add-edit-sector.component.html',
  styleUrls: ['./add-edit-sector.component.scss']
})

export class AddEditSectorComponent implements OnInit {
  imageSrc: string | ArrayBuffer | null;
  addSectorForm: FormGroup;
  p: number = 1;
  imageName: any;
  showForm = false;
  term: string;
  title = 'Add New';
  isReadonly = false;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  selectedRecord: any;
  sectorId: any;
  sectorData: any;
  constructor( private router:Router,private formBuilder: FormBuilder,
    private dataTransferService : DataTransferService,
    private toastr:ToastrService,
    private activeRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private ngxSpinnerService: NgxSpinnerService) {

    const state=this.router.getCurrentNavigation()?.extras.state;
    if(state){
      (this.sectorId=state?.sectorId);
      (this.title=state?.title);
      (this.sectorData=state?.sector);
      console.log("Sector Data",this.sectorData);
      
    }else{
      this.router.navigate([`actions/sectors`]);
    }

    this.addSectorForm = this.formBuilder.group({
      IN_name: ['', [Validators.required,wordLimitValidator(20)]],
      IN_dp: [null, Validators.required],
      IN_description: ['',[Validators.required, wordLimitValidator(200)]],
      IN_createdBy: ['', Validators.required] // Assuming this is required as per your form
    });
   }

  ngOnInit(): void {
    if(this.title=='Edit'){
    this.addSectorForm.patchValue({
      IN_name: this.sectorData.IN_name,
      // IN_dp: this.sectorData.IN_dp,
      IN_description: this.sectorData.IN_description,
      IN_createdBy: this.sectorData.IN_createdBy,
    });
  }
  }
  onFileSelected(event: any) {        
    let selectedFile = event.target.files[0];

    if (event.target.files.length === 0) {
      // Reset both imageName and imageSrc when no file is selected
      this.imageName = null;
      this.imageSrc = null;
      return;
    }

    const newFileName = FileValidator.addTimestamp(selectedFile.name);
    this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });

    if(this.imageName){
      const formControl=this.addSectorForm.get('IN_dp');
      formControl?.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));
      formControl?.updateValueAndValidity();
    }

    const fileType = this.imageName.type.split('/')[0];
    const fileExtension = this.imageName.name.split('.').pop()?.toLowerCase();
    if (fileType !== 'image' || fileExtension === 'svg') {
      event.target.value = '';
      this.toastr.info('Please select an image file (excluding SVG).');
      this.imageName = null;
      this.imageSrc = null;
      return;
    }
    
  if (this.imageName && fileType== 'image') {
    const reader = new FileReader();
    reader.onload = (e) => {
      this.imageSrc = e.target?.result as string | ArrayBuffer;
    };
    reader.readAsDataURL(this.imageName);
  } else {
    this.imageSrc = null; // Reset imageSrc if no file selected
  }
    console.log('imageName', this.imageName);
  }

  uploadLogoUrl(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.imageName) {
        return reject("Image Not Provide");  
      }
      console.log('image', this.imageName);
      this.dataTransferService.uploadurl(this.imageName).subscribe(
        (res: any) => {
          resolve(res); 
        },
        (error: any) => {
          reject(error);  // Reject the promise with the error
        }
      );
    });
  }
  
  async insertSectorData() {  
    try {
      if (this.title == 'Edit') {
        this.addSectorForm.value.IN_id = this.sectorId;
        if (this.imageName) {
          await this.uploadLogoUrl();
          const fileUrl = this.baseUrl + this.imageName.name;
          this.addSectorForm.value.IN_dp = fileUrl;
        } else {
          this.addSectorForm.value.IN_dp = this.sectorData.IN_dp;
        }
        console.log("Data for edit : ", this.addSectorForm.value);
  
        const res: any = await this.dataTransferService.updateIndustryData(this.addSectorForm.value).toPromise();
        this.ngxSpinnerService.hide('globalSpinner');
        if (res.statusCode == 200) {
          this.toastr.success("Sector updated successfully.");
          this.router.navigate([`actions/sectors`]);
        } else {
          this.toastr.error("Something went wrong.");
          console.error('Unable to add role. Status:', res.status);
        }
      } else {
        if (this.addSectorForm.invalid) {
          this.toastr.info('', 'Please fill all required fields');
          return;
        } else {
          await this.uploadLogoUrl();
          const fileUrl = this.baseUrl + this.imageName.name;
          const data = {
            IN_name: this.addSectorForm.value.IN_name,
            IN_description: this.addSectorForm.value.IN_description,
            IN_createdBy: this.addSectorForm.value.IN_createdBy,
            IN_dp: fileUrl,
          };
          console.log('ADD SECTOR DATA', data);
          this.ngxSpinnerService.show('globalSpinner');
  
          const res: any = await this.dataTransferService.insertIndustryData(data).toPromise();
          this.ngxSpinnerService.hide('globalSpinner');
          if (res.statusCode == 200) {
            console.log('Sector posted successfully:', res);
            this.toastr.success('Sector added successfully.');
            this.getAllSectorsData();
            this.router.navigate(['/actions/sectors']);
          } else {
            this.toastr.error('', 'Something Went Wrong');
          }
        }
      }
    } catch (error) {
      this.ngxSpinnerService.hide('globalSpinner');
      console.error('Error:', error);
      this.toastr.error('Something went wrong.');
    }
  }
  

  getAllSectorsData(){
    this.dataTransferService.getIndustryData().subscribe({
      next:(res:any)=>{
        if (res.status === 200) {
        } else {
          console.error('Failed to fetch role. Status:', res.status);
        }
      }
    })
  }

  

}

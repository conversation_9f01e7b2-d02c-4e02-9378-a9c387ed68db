<!-- your-component.component.html -->
<app-sidebar>
    <div class="content-wrapper">

        <div  class="container-scroll">  
            <div class="card-header card-title bg-primary rounded-top text-white text-center mb-0">App User</div>
            <div class="card-body" style="background-color: #FFFFFF;">
              <div class="row justify-content-center">
                <div class="col-md-3 mb-4">
                  <div class="card border-info custom-shadow">
                    <div class="card-body text-center">
                      <div class="card-title mb-2">User Name</div>
                      <hr class="divider">
                      <p class="card-text">{{userName}}</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-4">
                  <div class="card border-info custom-shadow">
                    <div class="card-body text-center">
                      <div class="card-title mb-2">User Email</div>
                      <hr class="divider">
                      <p class="card-text">{{userEmail}}</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-4">
                  <div class="card border-info custom-shadow">
                    <div class="card-body text-center">
                      <div class="card-title mb-2">Registration Source</div>
                      <hr class="divider img-divider">
                      <div class="card-text">
                        <img class="img-size" ngbTooltip="Email" *ngIf="registrationSource == 1"
                          src="./assets/logos/Email-logo.jpeg" alt="Email">
                        <img class="img-size" ngbTooltip="Google" *ngIf="registrationSource == 2"
                          src="./assets/logos/Google-logo.jpeg" alt="Google">
                        <img class="img-size" ngbTooltip="Facebook" *ngIf="registrationSource == 3"
                          src="./assets/logos/Facebook-logo.png" alt="Facebook">
                        <img class="img-size" ngbTooltip="Apple" *ngIf="registrationSource == 4"
                          src="./assets/logos/Apple-logo.jpg" alt="Apple">
                        <img class="img-size" ngbTooltip="LinkedIn" *ngIf="registrationSource == 5"
                          src="./assets/logos/Linkedin-logo.png" alt="LinkedIn">
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3 mb-4">
                  <div class="card border-info custom-shadow">
                    <div class="card-body text-center">
                      <div class="card-title mb-2"> Created Date </div>
                      <hr class="divider">
                      <p class="card-text">{{createdDate}}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row justify-content-center mt-4">
                <div class="col-md-6 text-center">
                  <div class="row">
                    <div class="col-md-6 mb-2">
                      <button class="btn btn-block" data-toggle="modal" data-target="#myModal"
                        [ngClass]="{'btn-success': activeStatus == '0', 'btn-danger': activeStatus == '1'}">
                        {{ activeStatus=='1'?'Deactivate':'Activate' }}
                      </button>
                    </div>
                    <div class="col-md-6 mb-2">
                      <button class="btn btn-block btn-light" routerLink="/actions/app-users">Cancel</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
      
          <div id="myModal" class="modal fade" role="dialog">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h3>Confirmation !</h3>
                  <button type="button" class="close" data-dismiss="modal" style="color: white;">&times;</button>
                </div>
      
                <div class="modal-body text-center">
                  <i class="fa-solid fa-user"  [ngStyle]="{'color': activeStatus == '1' ? 'red' : 'green'}"></i>
                  <h3 class="mb-3 mt-1"> <strong>Are you sure?</strong></h3>
                  <p> Do you really want to {{activeStatus=='1'?'deactivate':'activate'}} this user?</p>
                </div>
      
                <div class="modal-footer text-center">
                  <button (click)="updateActiveStatus(activeStatus == '1' ? '0' : '1')" class="btn" [ngClass]="activeStatus == '1'?'btn-danger':'btn-success'"
                    data-dismiss="modal">{{activeStatus=='1'?'Deactivate':'Activate'}}</button>
                  <a href="" class="btn btn-primary" data-dismiss="modal">Cancel</a>
                </div>
      
              </div>
            </div>
          </div>

        <!-- <div class="row"> 
            <div class="col-12 grid-margin stretch-card">
                <div class="card">
                    <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">{{title}}
                    </div>
                    <div class="card-body">
                         -->

                        <!-- <form *ngIf="title=='Identity'" [formGroup]="userForm">

                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label for="U_gender">Gender</label>
                                    <input type="text" class="form-control" id="U_gender" formControlName="U_gender"
                                    [value]="userForm.get('U_gender')?.value | camelCase"   readonly>
                                </div>
                                <div class="form-group col-lg-6">
                                    <label for="U_ethnicity">Ethnicity</label>
                                    <input type="text" class="form-control" id="U_ethnicity"
                                        formControlName="U_ethnicity" [value]="userForm.get('U_ethnicity')?.value | camelCase" readonly>
                                </div>
                            </div>

                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label for="U_religionTitle">Religion</label>
                                    <input type="text" class="form-control" id="U_religionTitle"
                                        formControlName="U_religionTitle" [value]="userForm.get('U_religionTitle')?.value | camelCase" readonly>
                                </div>

                                <div class="form-group col-lg-6">
                                    <label for="U_sexualOrientation">Sexual Orientation</label>
                                    <input type="text" class="form-control" id="U_sexualOrientation"
                                        formControlName="U_sexualOrientation" [value]="userForm.get('U_sexualOrientation')?.value | camelCase" readonly>
                                </div>
                            </div>

                            <div class="row">


                                <div class="form-group col-lg-6">
                                    <label for="U_institute">University</label>
                                    <input type="text" class="form-control" id="U_institute"
                                        formControlName="U_institute" [value]="userForm.get('U_institute')?.value | camelCase" readonly>
                                </div>
                                <div class="form-group col-lg-6">
                                    <label for="U_education">Degree</label>
                                    <input type="text" class="form-control" id="U_education"
                                        formControlName="U_education" [value]="userForm.get('U_education')?.value | camelCase" readonly>
                                </div>
                            </div>

                            <div class="row">

                                <div class="form-group col-lg-6">
                                    <label for="U_isDisability">Has A Disability ?</label>
                                    <input type="text" class="form-control" id="U_isDisability"
                                        formControlName="U_isDisability" readonly>
                                </div>
                                <div class="form-group col-lg-6">
                                    <label for="U_postcode">Postcode</label>
                                    <input type="text" class="form-control" id="U_postcode" formControlName="U_postcode"
                                        readonly>
                                </div>
                            </div>

                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label for="U_first_generation">Is This First Generation ?</label>
                                    <input type="text" class="form-control" id="U_first_generation"
                                        formControlName="U_first_generation" readonly>
                                </div>
                                <div class="form-group col-lg-6">
                                    <label for="U_freeMeal">Was Entitled To Free School Meals</label>
                                    <input type="text" class="form-control" id="U_freeMeal" formControlName="U_freeMeal"
                                        readonly>
                                </div>

                            </div>

                            <div class="text-center">
                                <button class="btn btn-light" routerLink="/actions/app-users">Back</button>
                            </div>
                        </form> -->

                        <!-- <form *ngIf="title=='Preferences'" [formGroup]="userForm">

                            <div class="row">
                        <div class="form-group col-lg-6">
                            <label for="Liked_Activities">Liked Activities</label>
                            <div class="tag-input-container" aria-readonly>
                                <div class="tag-box">
                                    <div *ngFor="let tag of likedActivities" class="tag">
                                        {{ tag }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group col-lg-6">
                            <label for="Disliked_Activities">Disliked Activities</label>
                            <div class="tag-input-container">
                                <div class="tag-box">
                                    <div *ngFor="let tag of dislikedActivities" class="tag">
                                        {{ tag }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                       <div class="row"> 
                        <div class="form-group col-lg-6">
                            <label for="Do_Not_Want_To_Work">Do Not Want To Work</label>
                            <div class="tag-input-container">
                                <div class="tag-box">
                                    <div *ngFor="let tag of doNotWantToWork" class="tag">
                                        {{ tag }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group col-lg-6">
                            <label for="Minimum_Salary_Expected" >Minimum Salary Expected</label>
                            <div class="tag-input-container">
                                <div class="tag-box">
                                    <div *ngFor="let tag of minimumSalaryExpected" class="tag">
                                        {{ tag }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                        
                        <div class="row">
                        <div class="form-group col-lg-6">
                            <label for="Minimum_Salary_Expected" >Liked Sectors</label>
                            <div class="tag-input-container">
                                <div class="tag-box">
                                    <div *ngFor="let tag of LikedSector" class="tag">
                                        {{ tag }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                        <div class="text-center">
                            <button class="btn btn-light" routerLink="/actions/app-users">Back</button>
                        </div>
                        
                        </form> -->
                    <!-- </div>
                </div>
            </div>
        </div> -->
    </div>
</app-sidebar>
<app-sidebar>
  <div class="content-wrapper">

    <div class="container-scroll"> <!--Form To Add New User -->
      <div class="card-header card-title bg-primary rounded-top text-white text-center mb-0">{{title}} Sharer</div>
      <div class="card-body" style="background-color: hsl(0, 0%, 100%);">
        <form [formGroup]="addNewAppUserForm">

          <div  class="section shadow-lg py-3 px-2"> 
            <h4 class="heading shadow-sm">Sharer Details</h4>

            <div class="row mx-3 pt-2">
              <div *ngIf="this.title==='Add'" class="form-group col-lg-6 pl-4">
                <label class="required-field label" for="U_name">Sharer Name</label>
                <input type="text" id="U_name" formControlName="U_name" class="form-control"
                  placeholder="Enter User Name">
              </div>

              <div *ngIf="this.title==='Add'" class="form-group col-lg-6 pl-4">
                <label class="required-field label" for="U_email">Sharer Email</label>
                <input type="email" id="U_email" formControlName="U_email" class="form-control"
                  placeholder="Enter User Email" autocomplete="new-password">
                <div
                  *ngIf="addNewAppUserForm.get('U_email')?.invalid && (addNewAppUserForm.get('U_email')?.dirty || addNewAppUserForm.get('U_email')?.touched)"
                  class="text-danger">
                  <div *ngIf="addNewAppUserForm.get('U_email')?.errors?.required">Email is required.</div>
                  <div *ngIf="addNewAppUserForm.controls['U_email'].hasError('invalidEmail')">Invalid email format.</div>
                </div>
              </div>


              <div *ngIf="this.title==='Add'" class="form-group col-lg-6 pl-4">
                <label class="required-field label" for="U_password">Sharer Password</label>
                <input type="password" id="U_password" formControlName="U_password" class="form-control"
                  placeholder="Enter password" autocomplete="new-password">
                <i class="fa fa-eye position-absolute toggle-password" #togglePasswordIcon
                  (click)="togglePasswordVisibility(togglePasswordIcon, 'U_password')"></i>
                  <div *ngIf="addNewAppUserForm.get('U_password')?.invalid && (addNewAppUserForm.get('U_password')?.dirty || addNewAppUserForm.get('U_password')?.touched)"
                  class="text-danger">
                  <div *ngIf="addNewAppUserForm.get('U_password')?.errors?.required">Password is required.</div>
                  <div *ngIf="addNewAppUserForm.get('U_password')?.errors?.minlength">Password must be at least 8 characters long.</div>
                  <div *ngIf="addNewAppUserForm.get('U_password')?.errors?.pattern">Password must contain at least:
                    <ul>
                      <li>One uppercase letter</li>
                      <li>One lowercase letter</li>
                      <li>One number</li>
                      <li>One special character</li>
                    </ul>
                  </div>
                </div>            
              </div>

              <div class="form-group col-lg-6 pl-4">
                <label class="label" for="U_dp">Sharer DP</label>
                <div class="logo-input-container">
                  <input type="file" class="form-control form-control-sm" formControlName="U_dp"
                    (change)="onFileSelected($event, 'U_dp')" [readonly]="isReadonly" accept="image/*">
                  <img *ngIf="imageSrc" [src]="imageSrc" alt="Sharer DP" class="img-preview">
                </div>
                <div *ngIf="addNewAppUserForm.get('U_dp')?.errors?.fileSizeValidator" class="text-danger">
                  The file size exceeds the 2 MB limit. Please select a smaller file.
                </div>
                <div *ngIf="addNewAppUserForm.get('U_dp')?.errors?.fileAspectRatioValidator" class="text-danger">
                  The image must have a 1:1 aspect ratio. Please select a square image.
                </div>                
                <a *ngIf="!isCropperVisible && imageName && !addNewAppUserForm.get('U_dp')?.errors?.fileSizeValidator" class="btn-custom-small" (click)="showCropper('U_dp')">Edit DP</a>
              </div>

            </div>

            <div *ngIf="isCropperVisible&&!addNewAppUserForm.get('U_dp')?.errors?.fileSizeValidator"  class="row mx-4 mb-5">
              <div class="image-cropper text-center col-lg-12">
                <h5 class="py-2">Resize Image</h5>
                <image-cropper class="custom-image-cropper my-2"
                [imageChangedEvent]="imgChangeEvt"
                [aspectRatio]="1/1"
                [maintainAspectRatio]="true"
                format="png"
                (imageCropped)="cropImg($event,'U_dp')"
                (imageLoaded)="imgLoad()"
                (cropperReady)="initCropper()"
                (loadImageFailed)="imgFailed()">
                </image-cropper> 
                <button class="btn btn-success btn-sm m-2" (click)="saveCroppedImage('U_dp')">Save</button>
                <button class="btn btn-secondary btn-sm m-2" (click)="hideCropper('U_dp')">Cancel</button>
              </div>

               <!-- <div class="image-cropper text-center col-lg-5">
                <h5 class="py-2">Preview</h5>
                <img class="my-2" height="350" width="100%" [src]="imageSrc" alt="Preview">
              </div>  -->

              </div>

              <div *ngIf="this.title==='Add'" class="row mx-3">
              <div *ngIf="!this.addNewAppUserForm.get('U_profileAnonymous')?.value" class="form-group col-lg-6 pl-4">
                <div class="form-check ml-1">
                  <input type="checkbox" formControlName="U_isExpert" id="U_isExpert">
                  <label class="label ml-2 mt-2" for="U_isExpert">
                    Make this an expert sharer
                  </label>
                </div>
              </div>

              <div *ngIf="!this.addNewAppUserForm.get('U_isExpert')?.value" class="form-group col-lg-6 pl-4">
                <div class="form-check ml-1">
                  <input (change)="anonymousCheck()" type="checkbox" formControlName="U_profileAnonymous" id="U_profileAnonymous">
                  <label class="label ml-2 mt-2" for="U_profileAnonymous">
                    Make this an anonymous sharer
                  </label>
                </div>
              </div>
            </div>
            

            <div *ngIf="(this.addNewAppUserForm.get('U_profileAnonymous')?.value)"  class="row mx-3">
              <div class="form-group col-lg-6 pl-4">
                <label class="label" for="U_aliasName">Alias Name</label>
                <input type="text" id="U_aliasName" formControlName="U_aliasName" class="form-control"
                  placeholder="Enter User Name">
              </div>
              
              <div class="form-group col-lg-6 pl-4">
                <label class="label" for="U_aliasDp">Alias DP</label>
                <div class="logo-input-container">
                  <input type="file" class="form-control form-control-sm" formControlName="U_aliasDp"
                    (change)="onFileSelected($event, 'U_aliasDp');" [readonly]="isReadonly" accept="image/*">
                  <img *ngIf="aliasImageSrc" [src]="aliasImageSrc" alt="Alias DP Image" class="img-preview">
                </div>
                <div *ngIf="addNewAppUserForm.get('U_aliasDp')?.errors?.fileSizeValidator" class="text-danger">
                  The file size exceeds the 2 MB limit. Please select a smaller file.
                </div>
                <div *ngIf="addNewAppUserForm.get('U_aliasDp')?.errors?.fileAspectRatioValidator" class="text-danger">
                  The image must have a 1:1 aspect ratio. Please select a square image.
                </div>
                <a *ngIf="!isAliasCropperVisible && aliasImageName && !addNewAppUserForm.get('U_aliasDp')?.errors?.fileSizeValidator" class="btn-custom-small" (click)="showCropper('U_aliasDp')">Edit DP</a>
             
              </div>
            </div>    
            
            <div *ngIf="isAliasCropperVisible&&!addNewAppUserForm.get('U_aliasDp')?.errors?.fileSizeValidator" class="cropper-container" class="row mx-4 mb-5">
              <div class="image-cropper text-center col-lg-12">
                <h5 class="py-2">Resize Image</h5>
                <image-cropper class="custom-image-cropper my-2"
                [imageChangedEvent]="aliasImgChangeEvt"
                [aspectRatio]="1/1"
                [maintainAspectRatio]="true"
                format="png"
                (imageCropped)="cropImg($event,'U_aliasDp')"
                (imageLoaded)="imgLoad()"
                (cropperReady)="initCropper()"
                (loadImageFailed)="imgFailed()">
                </image-cropper> 
                <button class="btn btn-success btn-sm m-2" (click)="saveCroppedImage('U_aliasDp')">Save</button>
                <button class="btn btn-secondary btn-sm m-2" (click)="hideCropper('U_aliasDp')">Cancel</button>
              </div>

               <!-- <div class="image-cropper text-center col-lg-5">
                <h5 class="py-2">Preview</h5>
                <img class="my-2" height="350" width="100%" [src]="imageSrc" alt="Preview">
              </div>  -->

              </div>

              <div *ngIf="title=='Edit'" class="text-center mt-5">
                <button (click)="updateUserDetails()" class="btn btn-primary mr-2 px-5 my-2 submit-btn">Save</button>
                <button class="btn btn-light px-5" routerLink="/actions/app-users">Back</button>
              </div>

          </div>


          <div class="section shadow-lg py-3 px-2 mt-4"> <!-- Preferences -->
            <h4 class="heading shadow-sm">Preferences</h4>
            <!-- <hr> -->
            <div class="form-group pl-4 mt-3">
              <label for="QUA_quiz_options" class="required-field label ml-4">Activities They Do a Lot In Their
                Role</label>
              <div class="d-flex flex-wrap">
                <div *ngFor="let activity of ActivitiesYouLike" class="tag"
                  [class.selected]="isItemSelected(activity, selectedActivities,'QUA_quiz_options')"
                  (click)="toggleSelection(activity, selectedActivities, 'QUA_quiz_options',false,'Preferences')">
                  {{ activity.option_title }}
                  <span class="check-mark">✔</span>
                </div>
                <!-- <div *ngIf="addNewAppUserForm.get('QUA_quiz_options')?.invalid && (addNewAppUserForm.get('QUA_quiz_options')?.dirty || addNewAppUserForm.get('QUA_quiz_options')?.touched)" class="text-danger">
                  <div *ngIf="addNewAppUserForm.get('QUA_quiz_options')?.errors?.required">Activities They Like is required.</div>
              </div> -->
              </div>
            </div>
            <hr>


            <div class="form-group pl-4 mt-4">
              <label for="LI_contentId" class="required-field label ml-4">Sectors</label>
              <div class="d-flex flex-wrap">
                <div *ngFor="let sector of LikedSectors" class="tag"
                  [class.selected]="isItemSelected(sector, selectedSectors,'LI_contentId')"
                  (click)="toggleSelection(sector, selectedSectors, 'LI_contentId',false,'Sectors')">
                  {{ sector.IN_name }} <span class="check-mark">✔</span>
                </div>
              </div>
            </div>
            <hr>

            <div class="form-group pl-4 mt-4">
              <label for="QUA_quiz_options" class="label ml-4">Their Typical Work Type</label>
              <div class="d-flex flex-wrap">
                <div *ngFor="let type of WorkType" class="tag"
                  [class.selected]="isItemSelected(type, selectedWorkTypes,'QUA_quiz_options')"
                  (click)="toggleSelection(type, selectedWorkTypes, 'QUA_quiz_options',false,'WorkType')">
                  {{ type.option_title |camelCase }}
                  <span class="check-mark">✔</span>
                </div>
              </div>
            </div>

            <div *ngIf="title=='Edit'" class="text-center mt-5">
              <button (click)="updateUserPreferences()" class="btn btn-primary mr-2 px-5 my-2 submit-btn">Save</button>
              <button class="btn btn-light px-5" routerLink="/actions/app-users">Back</button>
            </div>

          </div>

          <div class="section shadow-lg py-3 px-2  mt-4"> <!-- Identity -->
            <h4 class="heading shadow-sm">Identity</h4>

            <div class="form-check form-switch pt-2">
              <label class="form-check-label" for="flexSwitchCheckDefault">Activate the toggle below if you want this
                info displayed on your profile</label>
              <input class="form-check-input form-check-input-head mr-4" checked disabled type="checkbox"
                id="flexSwitchCheckDefault">
            </div>

            <hr>


            <!-- <div class="form-group pl-4 mt-4">
              <div class="form-check form-switch">
                <label for="U_genderId" class="required-field form-check-label label">Gender</label>
                <input class="form-check-input" type="checkbox" id="U_genderId" formControlName="U_gender_Toggle">
              </div>
              <div class="d-flex flex-wrap">
                <div *ngFor="let data of Genders" class="tag"
                  [class.selected]="isItemSelectedForIdentity(data, selectedGender, true, 'Gender')"
                  (click)="toggleSelection(data, selectedGender, 'U_genderId', true,'Gender')">
                  {{ data.GE_title }}
                  <span class="check-mark">✔</span>
                </div>
              </div>
            </div> -->

            <!-- Gender Section -->
            <div class="form-group pl-4 mt-4">
              <div class="form-check form-switch">
                <label for="U_genderId" class="required-field form-check-label label">Gender</label>
                <input class="form-check-input" type="checkbox" id="U_genderId" formControlName="U_gender_Toggle">
              </div>
              <div class="d-flex flex-wrap">
                <div *ngFor="let data of Genders" class="tag"
                  [class.selected]="isItemSelectedForIdentity(data, selectedGender, true, 'Gender')"
                  (click)="toggleSelection(data, selectedGender, 'U_genderId', true, 'Gender')">
                  {{ data.GE_title }}
                  <span class="check-mark">✔</span>
                </div>
              </div>
            </div>

            <hr>

            <div class="form-group pl-4 mt-4">
              <div class="form-check form-switch">
                <label for="U_ethinicityId" class="required-field label form-check-label">Ethnicity</label>
                <input class="form-check-input" type="checkbox" id="U_ethinicityId"
                  formControlName="U_ethinicity_Toggle">
              </div>
              <div class="d-flex flex-wrap">
                <div *ngFor="let data of Ethnicities" class="tag"
                  [class.selected]="isItemSelectedForIdentity(data, selectedEthnicity, true,'Ethnicity')"
                  (click)="toggleSelection(data, selectedEthnicity, 'U_ethinicityId', true,'Ethnicity')">
                  {{ data.ET_title }}
                  <span class="check-mark">✔</span>
                </div>
              </div>
            </div>
            <hr>

            <div class="form-group pl-4 mt-4">
              <div class="form-check form-switch">
                <label for="U_religionId" class="required-field label form-check-label">Religion</label>
                <input class="form-check-input" type="checkbox" id="U_religionId" formControlName="U_religion_Toggle">
              </div>
              <div class="d-flex flex-wrap">
                <div *ngFor="let data of Religions" class="tag"
                  [class.selected]="isItemSelectedForIdentity(data, selectedReligion, true,'Religion')"
                  (click)="toggleSelection(data, selectedReligion, 'U_religionId', true,'Religion')">
                  {{ data.RE_title }}
                  <span class="check-mark">✔</span>
                </div>
              </div>
            </div>
            <hr>

            <div class="form-group pl-4 mt-4">
              <div class="form-check form-switch">
                <label for="U_sexualOrientationId" class="required-field label form-check-label">Sexual
                  Orientation</label>
                <input class="form-check-input" type="checkbox" id="U_sexualOrientationId"
                  formControlName="U_sexuality_Toggle">
              </div>
              <div class="d-flex flex-wrap">
                <div *ngFor="let data of SexualOrientation" class="tag"
                  [class.selected]="isItemSelectedForIdentity(data, selectedSexualOrientation, true, 'Sexual-Orientation')"
                  (click)="toggleSelection(data, selectedSexualOrientation, 'U_sexualOrientationId', true,'Sexual-Orientation')">
                  {{ data.SO_title }}
                  <span class="check-mark">✔</span>
                </div>
              </div>
            </div>
            <hr>

            <div class="form-group pl-4 mt-4">
              <div class="form-check form-switch">
                <label for="U_isDisability" class="required-field label form-check-label">Has A
                  Disability ?</label>
                <input class="form-check-input" type="checkbox" id="U_isDisability"
                  formControlName="U_disability_Toggle">
              </div>
              <div class="d-flex flex-wrap">
                <div *ngFor="let type of hasADisability" class="tag"
                  [class.selected]="isItemSelectedForIdentity(type, selectedDisability,true,'U_isDisability')"
                  (click)="toggleSelection(type, selectedDisability, 'U_isDisability', true,'Disability')">
                  {{ type.AM_title }}
                  <span class="check-mark">✔</span>
                </div>
              </div>
            </div>
            <hr>

            <div class="form-group pl-4 mt-4">
              <div class="form-check form-switch">
                <label for="U_first_generation" class="required-field label form-check-label">Is This First Generation
                  ?</label>
                <input class="form-check-input" type="checkbox" id="U_first_generation"
                  formControlName="U_first_generation_Toggle">
              </div>
              <div class="d-flex flex-wrap">
                <div *ngFor="let type of isThisGeneration" class="tag"
                  [class.selected]="isItemSelectedForIdentity(type, selectedGeneration,true,'U_first_generation')"
                  (click)="toggleSelection(type, selectedGeneration, 'U_first_generation', true,'Generation')">
                  {{ type.AM_title }}
                  <span class="check-mark">✔</span>
                </div>
              </div>
            </div>
            <hr>


            <div class="form-group pl-4 mt-4">
              <label for="U_freeMeal" class="required-field label ml-4">Was
                Entitled To Free School Meals ?</label>
              <input class="form-check-input" type="checkbox" id="U_freeMeal_Toggle"
                formControlName="U_freeMeal_Toggle">
              <div class="d-flex flex-wrap">
                <div *ngFor="let type of FreeMeal" class="tag"
                  [class.selected]="isItemSelectedForIdentity(type, selectedFreeMeal,true,'U_freeMeal')"
                  (click)="toggleSelection(type, selectedFreeMeal, 'U_freeMeal', true,'FreeSchoolMeals')">
                  {{ type.AM_title }}
                  <span class="check-mark">✔</span>
                </div>
              </div>
            </div>
            <hr>

            <div class="form-group col-lg-7 pl-4 col-md-6 col-sm-12 mb-3">
              <label for="U_institute" class="label universityLabel ml-4">University</label>
              <!-- <a (click)="showAddUniversityDegreeModal('University')" class="addNewBtn">Add New +</a>  -->
              <div class="autocomplete-container autocomplete-container-institute ml-4">
                <div class="tag-input-wrapper">
                  <div class="selected-options" *ngIf="showTag && addNewAppUserForm.get('U_institute')?.value">
                    <div class="selected-option">
                      {{ getMenuTitle(addNewAppUserForm.get('U_institute')?.value, 'U_institute') }}
                      <button type="button" class="remove-selected-option"
                        (click)="removeMenu('U_institute', addNewAppUserForm.get('U_institute')?.value)">
                        &times;
                      </button>
                    </div>
                  </div>
                  <input type="text" class="form-control tag-input form-control-sm"
                    [formControl]="universitySearchControl" (input)="filterOptions('U_institute')"
                    placeholder="Type to search..." required (click)="showDropdown = true">
                </div>
                <div class="dropdown-list" *ngIf="showDropdown && filteredUniversityOptions.length > 0">
                  <ul>
                    <li *ngFor="let menu of filteredUniversityOptions" (click)="addMenu(menu, 'U_institute')">
                      {{ menu.INS_title }}
                    </li>
                  </ul>
                </div>
                <div class="no-options-message" *ngIf="showDropdown && filteredUniversityOptions.length === 0">
                  No matching universities found.
                </div>
              </div>
            </div>


            <hr>

            <div class="form-group col-lg-7 pl-4 col-md-6 col-sm-12 mb-3">
              <label for="U_education" class="label ml-4">Degree</label><a
                (click)="showAddUniversityDegreeModal('Degree')" class="addNewBtn">Add New +</a>
              <div class="autocomplete-container autocomplete-container-education ml-4">
                <div class="tag-input-wrapper">
                  <div class="selected-options"
                    *ngIf="showTagOfEducation && addNewAppUserForm.get('U_education')?.value">
                    <div class="selected-option">
                      {{ getMenuTitle(addNewAppUserForm.get('U_education')?.value, 'U_education') }}
                      <button type="button" class="remove-selected-option"
                        (click)="removeMenu('U_education', addNewAppUserForm.get('U_education')?.value)">
                        &times;
                      </button>
                    </div>
                  </div>
                  <input type="text" class="form-control tag-input form-control-sm" [formControl]="degreeSearchControl"
                    (input)="filterOptions('U_education')" placeholder="Type to search..." required
                    (click)="showDropdownOfEducation = true">
                </div>
                <div class="dropdown-list" *ngIf="showDropdownOfEducation && filteredDegreeOptions.length > 0">
                  <ul>
                    <li *ngFor="let menu of filteredDegreeOptions" (click)="addMenu(menu, 'U_education')">
                      {{ menu.ED_name }}
                    </li>
                  </ul>
                </div>
                <div class="no-options-message" *ngIf="showDropdownOfEducation && filteredDegreeOptions.length === 0">
                  No matching universities found.
                </div>
              </div>
            </div>

            <div class="add-degree-modal modal" id="add-degree-modal">
              <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                  <div class="modal-header">
                    <h4 class="modal-title fs-5" id="degreeBackdropLabel">Add New Degree</h4>
                    <button type="button" (click)="hideAddUniversityDegreeModal('Degree')" class="close"
                      data-dismiss="add-degree-modal" style="color: white;">&times;</button>
                  </div>
                  <div class="modal-body">
                    <input type="text" id="ED_name" class="form-control" placeholder="Enter New Degree Name">
                  </div>
                  <div class="modal-footer">
                    <button type="button" (click)="addDegree()" class="btn btn-primary"
                      data-bs-dismiss="add-degree-modal">Add</button>
                    <button type="button" (click)="hideAddUniversityDegreeModal('Degree')" class="btn btn-secondary"
                      data-bs-dismiss="add-degree-modal">Cancel</button>
                  </div>
                </div>
              </div>
            </div>
            <hr>

            <div class="form-group col-lg-7 pl-4 col-md-6 col-sm-12 mb-3">
              <label for="U_postcode" class="label ml-4">Postcode</label>
              <input class="form-check-input" type="checkbox" id="U_postcode_Toggle"
                formControlName="U_postcode_Toggle">
              <div class="autocomplete-container autocomplete-container-education ml-4">
                <div class="tag-input-wrapper">
                  <div *ngIf="selectedPostcode" class="selected-options">
                    <div class="selected-option">
                      {{ selectedPostcode }}
                      <button type="button" class="remove-selected-option" (click)="removeSelectedPostcode()">
                        &times;
                      </button>
                    </div>
                  </div>
                  <input type="text" class="form-control tag-input form-control-sm"
                    [formControl]="postcodeSearchControl" (input)="filterPostcodes()" placeholder="Type to search..."
                    required (click)="showPostcodeDropdown = true">
                </div>
                <div class="dropdown-list" *ngIf="showPostcodeDropdown && filteredPostcodes.length > 0">
                  <ul>
                    <li *ngFor="let postcode of filteredPostcodes" (click)="selectPostcode(postcode)">
                      {{ postcode }}
                    </li>
                  </ul>
                </div>
                <!-- <div class="no-options-message" *ngIf="showPostcodeDropdown && filteredPostcodes.length === 0">
      No matching postcodes found.
    </div> -->
              </div>
            </div>


            <div *ngIf="title!=='Edit'" class="text-center mt-5">
              <button (click)="createNewAppUser()" class="btn btn-primary mr-2 px-5 submit-btn">Submit</button>
              <button class="btn btn-light px-5" routerLink="/actions/app-users">Cancel</button>
            </div>

            <div *ngIf="title=='Edit'" class="text-center mt-5">
              <button (click)="updateUserIdentity()" class="btn btn-primary mr-2 px-5 submit-btn">Save</button>
              <button class="btn btn-light px-5" routerLink="/actions/app-users">Back</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</app-sidebar>



<div class="add-university-modal modal" id="add-university-modal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title fs-5" id="universityBackdropLabel">Add New University</h4>
        <button type="button" (click)="hideAddUniversityDegreeModal('University')" class="close"
          data-dismiss="add-university-modal" style="color: white;">&times;</button>
      </div>
      <div class="modal-body">
        <input type="text" id="INS_title" class="form-control" placeholder="Enter New University Name">
      </div>
      <div class="modal-footer">
        <button type="button" (click)="addUniversity()" class="btn btn-primary"
          data-bs-dismiss="add-university-modal">Add</button>
        <button type="button" (click)="hideAddUniversityDegreeModal('University')" class="btn btn-secondary"
          data-bs-dismiss="add-university-modal">Cancel</button>
      </div>
    </div>
  </div>
</div>

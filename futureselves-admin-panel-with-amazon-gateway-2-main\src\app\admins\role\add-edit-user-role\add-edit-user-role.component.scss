.form-check-inline .form-check-input {
    display: inline-block;
    width: auto;
    margin-right: 5px; /* Adjust margin as needed */
}

.form-check-inline .form-check-label {
    display: inline-block;
    margin-left: 3px /* Reset margin */

}
.tag-input-container {
    display: flex;
    align-items: flex-start; /* Align items at the top */
    border: 1px solid #ced4da; /* Add border to the container */
    border-radius: 5px; /* Add border-radius for rounded corners */
    padding: 5px; /* Add some padding for spacing */
    overflow-x: auto; /* Enable horizontal scrolling */
    overflow-y: hidden; /* Enable horizontal scrolling */
    height: 50px;
  }
  
  .tag-input-container:focus-within{
    outline: 1px solid rgba(0, 123, 255, 0.65); /* Apply blue outline */
    transition: outline 0.3s ease; /* Smooth transition */
  
  }
  
  .tag-box {
    display: inline-flex; /* Display tags inline */
    flex-wrap: nowrap; /* Prevent tags from wrapping to the next line */
    gap: 5px; /* Add gap between tags */
  }
  
  .tag {
    background-color: #4B49AC;
    color: #fff;
    padding: 2px 5px;
    margin-top: 2px;
    margin-bottom: 2px;
    border-radius: 10px;
    display: flex; /* Use flexbox to align items */
    align-items: center;
    max-height: auto;
    white-space: nowrap; /* Prevent tag content from wrapping */
    overflow: hidden; /* Hide overflowing content */
  }
  
  .tag .close {
    margin-left: 5px;
    margin-top: 2px;
    margin-bottom: 2px;
    cursor: pointer;
    border: none;
    outline: none;
    color: #fff;
  }
  
  .tag-input {
    border: none;
    outline: none;
    margin:2px 5px 2px 3px; /* Push input box to the right side */
    max-height: 30px;
    min-width: 150px; /* Set minimum width for input box */
  }
  
  .tag-input:focus-within{
    border: none;
    outline: none;
  }
  
  /* For WebKit based browsers (e.g., Chrome, Safari) */
  .tag-input-container::-webkit-scrollbar {
    width: 4px; /* Set the width of the scrollbar */
  }
  
  .tag-input-container::-webkit-scrollbar-thumb {
    background-color: #888; /* Set the color of the scrollbar thumb */
    border-radius: 4px; /* Set the border-radius of the scrollbar thumb */
  }
  
  /* For Firefox */
  .tag-input-container {
    scrollbar-width: thin; /* Set the width of the scrollbar */
  }
  
  .tag-input-container::-webkit-scrollbar-thumb {
    background-color: #888; /* Set the color of the scrollbar thumb */
    border-radius: 4px; /* Set the border-radius of the scrollbar thumb */
  }

input[type="text"],
select {
  height: 50px; /* Set desired height */
}
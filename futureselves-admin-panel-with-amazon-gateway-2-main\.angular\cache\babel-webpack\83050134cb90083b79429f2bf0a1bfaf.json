{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Injectable, Optional, Inject, Directive, Output, Input, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-brower because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\n\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY\n});\n/** @docs-private */\n\nfunction DIR_DOCUMENT_FACTORY() {\n  return inject(DOCUMENT);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\n\n\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\n\nfunction _resolveDirectionality(rawValue) {\n  const value = rawValue?.toLowerCase() || '';\n\n  if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\n\n\nclass Directionality {\n  constructor(_document) {\n    /** The current 'ltr' or 'rtl' value. */\n    this.value = 'ltr';\n    /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n\n    this.change = new EventEmitter();\n\n    if (_document) {\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n    }\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n\n}\n\nDirectionality.ɵfac = function Directionality_Factory(t) {\n  return new (t || Directionality)(i0.ɵɵinject(DIR_DOCUMENT, 8));\n};\n\nDirectionality.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Directionality,\n  factory: Directionality.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Directionality, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DIR_DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\n\n\nclass Dir {\n  constructor() {\n    /** Normalized direction that accounts for invalid/unsupported values. */\n    this._dir = 'ltr';\n    /** Whether the `value` has been set to its initial value. */\n\n    this._isInitialized = false;\n    /** Event emitted when the direction changes. */\n\n    this.change = new EventEmitter();\n  }\n  /** @docs-private */\n\n\n  get dir() {\n    return this._dir;\n  }\n\n  set dir(value) {\n    const previousValue = this._dir; // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n    // whereas the browser does it based on the content of the element. Since doing so based\n    // on the content can be expensive, for now we're doing the simpler matching.\n\n    this._dir = _resolveDirectionality(value);\n    this._rawDir = value;\n\n    if (previousValue !== this._dir && this._isInitialized) {\n      this.change.emit(this._dir);\n    }\n  }\n  /** Current layout direction of the element. */\n\n\n  get value() {\n    return this.dir;\n  }\n  /** Initialize once default value has been set. */\n\n\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    this.change.complete();\n  }\n\n}\n\nDir.ɵfac = function Dir_Factory(t) {\n  return new (t || Dir)();\n};\n\nDir.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: Dir,\n  selectors: [[\"\", \"dir\", \"\"]],\n  hostVars: 1,\n  hostBindings: function Dir_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"dir\", ctx._rawDir);\n    }\n  },\n  inputs: {\n    dir: \"dir\"\n  },\n  outputs: {\n    change: \"dirChange\"\n  },\n  exportAs: [\"dir\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: Directionality,\n    useExisting: Dir\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dir, [{\n    type: Directive,\n    args: [{\n      selector: '[dir]',\n      providers: [{\n        provide: Directionality,\n        useExisting: Dir\n      }],\n      host: {\n        '[attr.dir]': '_rawDir'\n      },\n      exportAs: 'dir'\n    }]\n  }], null, {\n    change: [{\n      type: Output,\n      args: ['dirChange']\n    }],\n    dir: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass BidiModule {}\n\nBidiModule.ɵfac = function BidiModule_Factory(t) {\n  return new (t || BidiModule)();\n};\n\nBidiModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BidiModule\n});\nBidiModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BidiModule, [{\n    type: NgModule,\n    args: [{\n      exports: [Dir],\n      declarations: [Dir]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BidiModule, DIR_DOCUMENT, Dir, Directionality };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/cdk/fesm2020/bidi.mjs"], "names": ["i0", "InjectionToken", "inject", "EventEmitter", "Injectable", "Optional", "Inject", "Directive", "Output", "Input", "NgModule", "DOCUMENT", "DIR_DOCUMENT", "providedIn", "factory", "DIR_DOCUMENT_FACTORY", "RTL_LOCALE_PATTERN", "_resolveDirectionality", "rawValue", "value", "toLowerCase", "navigator", "language", "test", "Directionality", "constructor", "_document", "change", "bodyDir", "body", "dir", "htmlDir", "documentElement", "ngOnDestroy", "complete", "ɵfac", "ɵprov", "type", "args", "undefined", "decorators", "<PERSON><PERSON>", "_dir", "_isInitialized", "previousValue", "_rawDir", "emit", "ngAfterContentInit", "ɵdir", "provide", "useExisting", "selector", "providers", "host", "exportAs", "BidiModule", "ɵmod", "ɵinj", "exports", "declarations"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,MAAzB,EAAiCC,YAAjC,EAA+CC,UAA/C,EAA2DC,QAA3D,EAAqEC,MAArE,EAA6EC,SAA7E,EAAwFC,MAAxF,EAAgGC,KAAhG,EAAuGC,QAAvG,QAAuH,eAAvH;AACA,SAASC,QAAT,QAAyB,iBAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,YAAY,GAAG,IAAIX,cAAJ,CAAmB,aAAnB,EAAkC;AACnDY,EAAAA,UAAU,EAAE,MADuC;AAEnDC,EAAAA,OAAO,EAAEC;AAF0C,CAAlC,CAArB;AAIA;;AACA,SAASA,oBAAT,GAAgC;AAC5B,SAAOb,MAAM,CAACS,QAAD,CAAb;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMK,kBAAkB,GAAG,oHAA3B;AACA;;AACA,SAASC,sBAAT,CAAgCC,QAAhC,EAA0C;AACtC,QAAMC,KAAK,GAAGD,QAAQ,EAAEE,WAAV,MAA2B,EAAzC;;AACA,MAAID,KAAK,KAAK,MAAV,IAAoB,OAAOE,SAAP,KAAqB,WAAzC,IAAwDA,SAAS,EAAEC,QAAvE,EAAiF;AAC7E,WAAON,kBAAkB,CAACO,IAAnB,CAAwBF,SAAS,CAACC,QAAlC,IAA8C,KAA9C,GAAsD,KAA7D;AACH;;AACD,SAAOH,KAAK,KAAK,KAAV,GAAkB,KAAlB,GAA0B,KAAjC;AACH;AACD;AACA;AACA;AACA;;;AACA,MAAMK,cAAN,CAAqB;AACjBC,EAAAA,WAAW,CAACC,SAAD,EAAY;AACnB;AACA,SAAKP,KAAL,GAAa,KAAb;AACA;;AACA,SAAKQ,MAAL,GAAc,IAAIxB,YAAJ,EAAd;;AACA,QAAIuB,SAAJ,EAAe;AACX,YAAME,OAAO,GAAGF,SAAS,CAACG,IAAV,GAAiBH,SAAS,CAACG,IAAV,CAAeC,GAAhC,GAAsC,IAAtD;AACA,YAAMC,OAAO,GAAGL,SAAS,CAACM,eAAV,GAA4BN,SAAS,CAACM,eAAV,CAA0BF,GAAtD,GAA4D,IAA5E;AACA,WAAKX,KAAL,GAAaF,sBAAsB,CAACW,OAAO,IAAIG,OAAX,IAAsB,KAAvB,CAAnC;AACH;AACJ;;AACDE,EAAAA,WAAW,GAAG;AACV,SAAKN,MAAL,CAAYO,QAAZ;AACH;;AAdgB;;AAgBrBV,cAAc,CAACW,IAAf;AAAA,mBAA2GX,cAA3G,EAAiGxB,EAAjG,UAA2IY,YAA3I;AAAA;;AACAY,cAAc,CAACY,KAAf,kBADiGpC,EACjG;AAAA,SAA+GwB,cAA/G;AAAA,WAA+GA,cAA/G;AAAA,cAA2I;AAA3I;;AACA;AAAA,qDAFiGxB,EAEjG,mBAA2FwB,cAA3F,EAAuH,CAAC;AAC5Ga,IAAAA,IAAI,EAAEjC,UADsG;AAE5GkC,IAAAA,IAAI,EAAE,CAAC;AAAEzB,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFsG,GAAD,CAAvH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEwB,MAAAA,IAAI,EAAEE,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DH,QAAAA,IAAI,EAAEhC;AADwD,OAAD,EAE9D;AACCgC,QAAAA,IAAI,EAAE/B,MADP;AAECgC,QAAAA,IAAI,EAAE,CAAC1B,YAAD;AAFP,OAF8D;AAA/B,KAAD,CAAP;AAKlB,GARxB;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM6B,GAAN,CAAU;AACNhB,EAAAA,WAAW,GAAG;AACV;AACA,SAAKiB,IAAL,GAAY,KAAZ;AACA;;AACA,SAAKC,cAAL,GAAsB,KAAtB;AACA;;AACA,SAAKhB,MAAL,GAAc,IAAIxB,YAAJ,EAAd;AACH;AACD;;;AACO,MAAH2B,GAAG,GAAG;AACN,WAAO,KAAKY,IAAZ;AACH;;AACM,MAAHZ,GAAG,CAACX,KAAD,EAAQ;AACX,UAAMyB,aAAa,GAAG,KAAKF,IAA3B,CADW,CAEX;AACA;AACA;;AACA,SAAKA,IAAL,GAAYzB,sBAAsB,CAACE,KAAD,CAAlC;AACA,SAAK0B,OAAL,GAAe1B,KAAf;;AACA,QAAIyB,aAAa,KAAK,KAAKF,IAAvB,IAA+B,KAAKC,cAAxC,EAAwD;AACpD,WAAKhB,MAAL,CAAYmB,IAAZ,CAAiB,KAAKJ,IAAtB;AACH;AACJ;AACD;;;AACS,MAALvB,KAAK,GAAG;AACR,WAAO,KAAKW,GAAZ;AACH;AACD;;;AACAiB,EAAAA,kBAAkB,GAAG;AACjB,SAAKJ,cAAL,GAAsB,IAAtB;AACH;;AACDV,EAAAA,WAAW,GAAG;AACV,SAAKN,MAAL,CAAYO,QAAZ;AACH;;AAlCK;;AAoCVO,GAAG,CAACN,IAAJ;AAAA,mBAAgGM,GAAhG;AAAA;;AACAA,GAAG,CAACO,IAAJ,kBA9DiGhD,EA8DjG;AAAA,QAAoFyC,GAApF;AAAA;AAAA;AAAA;AAAA;AA9DiGzC,MAAAA,EA8DjG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA9DiGA,EA8DjG,oBAAkO,CAAC;AAAEiD,IAAAA,OAAO,EAAEzB,cAAX;AAA2B0B,IAAAA,WAAW,EAAET;AAAxC,GAAD,CAAlO;AAAA;;AACA;AAAA,qDA/DiGzC,EA+DjG,mBAA2FyC,GAA3F,EAA4G,CAAC;AACjGJ,IAAAA,IAAI,EAAE9B,SAD2F;AAEjG+B,IAAAA,IAAI,EAAE,CAAC;AACCa,MAAAA,QAAQ,EAAE,OADX;AAECC,MAAAA,SAAS,EAAE,CAAC;AAAEH,QAAAA,OAAO,EAAEzB,cAAX;AAA2B0B,QAAAA,WAAW,EAAET;AAAxC,OAAD,CAFZ;AAGCY,MAAAA,IAAI,EAAE;AAAE,sBAAc;AAAhB,OAHP;AAICC,MAAAA,QAAQ,EAAE;AAJX,KAAD;AAF2F,GAAD,CAA5G,QAQ4B;AAAE3B,IAAAA,MAAM,EAAE,CAAC;AACvBU,MAAAA,IAAI,EAAE7B,MADiB;AAEvB8B,MAAAA,IAAI,EAAE,CAAC,WAAD;AAFiB,KAAD,CAAV;AAGZR,IAAAA,GAAG,EAAE,CAAC;AACNO,MAAAA,IAAI,EAAE5B;AADA,KAAD;AAHO,GAR5B;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM8C,UAAN,CAAiB;;AAEjBA,UAAU,CAACpB,IAAX;AAAA,mBAAuGoB,UAAvG;AAAA;;AACAA,UAAU,CAACC,IAAX,kBAxFiGxD,EAwFjG;AAAA,QAAwGuD;AAAxG;AACAA,UAAU,CAACE,IAAX,kBAzFiGzD,EAyFjG;;AACA;AAAA,qDA1FiGA,EA0FjG,mBAA2FuD,UAA3F,EAAmH,CAAC;AACxGlB,IAAAA,IAAI,EAAE3B,QADkG;AAExG4B,IAAAA,IAAI,EAAE,CAAC;AACCoB,MAAAA,OAAO,EAAE,CAACjB,GAAD,CADV;AAECkB,MAAAA,YAAY,EAAE,CAAClB,GAAD;AAFf,KAAD;AAFkG,GAAD,CAAnH;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASc,UAAT,EAAqB3C,YAArB,EAAmC6B,GAAnC,EAAwCjB,cAAxC", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, Injectable, Optional, Inject, Directive, Output, Input, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-brower because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n    providedIn: 'root',\n    factory: DIR_DOCUMENT_FACTORY,\n});\n/** @docs-private */\nfunction DIR_DOCUMENT_FACTORY() {\n    return inject(DOCUMENT);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n    const value = rawValue?.toLowerCase() || '';\n    if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n        return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n    }\n    return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n    constructor(_document) {\n        /** The current 'ltr' or 'rtl' value. */\n        this.value = 'ltr';\n        /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n        this.change = new EventEmitter();\n        if (_document) {\n            const bodyDir = _document.body ? _document.body.dir : null;\n            const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n            this.value = _resolveDirectionality(bodyDir || htmlDir || 'ltr');\n        }\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n}\nDirectionality.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: Directionality, deps: [{ token: DIR_DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nDirectionality.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: Directionality, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: Directionality, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DIR_DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nclass Dir {\n    constructor() {\n        /** Normalized direction that accounts for invalid/unsupported values. */\n        this._dir = 'ltr';\n        /** Whether the `value` has been set to its initial value. */\n        this._isInitialized = false;\n        /** Event emitted when the direction changes. */\n        this.change = new EventEmitter();\n    }\n    /** @docs-private */\n    get dir() {\n        return this._dir;\n    }\n    set dir(value) {\n        const previousValue = this._dir;\n        // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n        // whereas the browser does it based on the content of the element. Since doing so based\n        // on the content can be expensive, for now we're doing the simpler matching.\n        this._dir = _resolveDirectionality(value);\n        this._rawDir = value;\n        if (previousValue !== this._dir && this._isInitialized) {\n            this.change.emit(this._dir);\n        }\n    }\n    /** Current layout direction of the element. */\n    get value() {\n        return this.dir;\n    }\n    /** Initialize once default value has been set. */\n    ngAfterContentInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        this.change.complete();\n    }\n}\nDir.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: Dir, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nDir.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: Dir, selector: \"[dir]\", inputs: { dir: \"dir\" }, outputs: { change: \"dirChange\" }, host: { properties: { \"attr.dir\": \"_rawDir\" } }, providers: [{ provide: Directionality, useExisting: Dir }], exportAs: [\"dir\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: Dir, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[dir]',\n                    providers: [{ provide: Directionality, useExisting: Dir }],\n                    host: { '[attr.dir]': '_rawDir' },\n                    exportAs: 'dir',\n                }]\n        }], propDecorators: { change: [{\n                type: Output,\n                args: ['dirChange']\n            }], dir: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass BidiModule {\n}\nBidiModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: BidiModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBidiModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: BidiModule, declarations: [Dir], exports: [Dir] });\nBidiModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: BidiModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: BidiModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [Dir],\n                    declarations: [Dir],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BidiModule, DIR_DOCUMENT, Dir, Directionality };\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, NgModule, InjectionToken, Injectable, Inject, SecurityContext, Component, HostBinding, HostListener } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { Subject } from 'rxjs';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/platform-browser';\nconst _c0 = [\"toast-component\", \"\"];\n\nfunction Toast_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function Toast_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return ctx_r5.remove();\n    });\n    i0.ɵɵelementStart(1, \"span\", 6);\n    i0.ɵɵtext(2, \"\\xD7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction Toast_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"[\", ctx_r7.duplicatesCount + 1, \"]\");\n  }\n}\n\nfunction Toast_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, Toast_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.titleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\n\nfunction Toast_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.options.messageClass);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.message, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction Toast_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.options.messageClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r3.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\n\nfunction Toast_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.width + \"%\");\n  }\n}\n\nfunction ToastNoAnimation_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function ToastNoAnimation_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return ctx_r5.remove();\n    });\n    i0.ɵɵelementStart(1, \"span\", 6);\n    i0.ɵɵtext(2, \"\\xD7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ToastNoAnimation_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"[\", ctx_r7.duplicatesCount + 1, \"]\");\n  }\n}\n\nfunction ToastNoAnimation_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ToastNoAnimation_div_1_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.options.titleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.duplicatesCount);\n  }\n}\n\nfunction ToastNoAnimation_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.options.messageClass);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.message, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction ToastNoAnimation_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.options.messageClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r3.message);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.message, \" \");\n  }\n}\n\nfunction ToastNoAnimation_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r4.width + \"%\");\n  }\n}\n\nclass ToastContainerDirective {\n  constructor(el) {\n    this.el = el;\n  }\n\n  getContainerElement() {\n    return this.el.nativeElement;\n  }\n\n}\n\nToastContainerDirective.ɵfac = function ToastContainerDirective_Factory(t) {\n  return new (t || ToastContainerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nToastContainerDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: ToastContainerDirective,\n  selectors: [[\"\", \"toastContainer\", \"\"]],\n  exportAs: [\"toastContainer\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastContainerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[toastContainer]',\n      exportAs: 'toastContainer'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n\nclass ToastContainerModule {}\n\nToastContainerModule.ɵfac = function ToastContainerModule_Factory(t) {\n  return new (t || ToastContainerModule)();\n};\n\nToastContainerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ToastContainerModule\n});\nToastContainerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastContainerModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [ToastContainerDirective],\n      exports: [ToastContainerDirective]\n    }]\n  }], null, null);\n})();\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\n\n\nclass ComponentPortal {\n  constructor(component, injector) {\n    this.component = component;\n    this.injector = injector;\n  }\n  /** Attach this portal to a host. */\n\n\n  attach(host, newestOnTop) {\n    this._attachedHost = host;\n    return host.attach(this, newestOnTop);\n  }\n  /** Detach this portal from its host */\n\n\n  detach() {\n    const host = this._attachedHost;\n\n    if (host) {\n      this._attachedHost = undefined;\n      return host.detach();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n\n\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalHost reference without performing `attach()`. This is used directly by\n   * the PortalHost when it is performing an `attach()` or `detach()`.\n   */\n\n\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n\n}\n/**\n * Partial implementation of PortalHost that only deals with attaching a\n * ComponentPortal\n */\n\n\nclass BasePortalHost {\n  attach(portal, newestOnTop) {\n    this._attachedPortal = portal;\n    return this.attachComponentPortal(portal, newestOnTop);\n  }\n\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost();\n    }\n\n    this._attachedPortal = undefined;\n\n    if (this._disposeFn) {\n      this._disposeFn();\n\n      this._disposeFn = undefined;\n    }\n  }\n\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n\n}\n/**\n * Everything a toast needs to launch\n */\n\n\nclass ToastPackage {\n  constructor(toastId, config, message, title, toastType, toastRef) {\n    this.toastId = toastId;\n    this.config = config;\n    this.message = message;\n    this.title = title;\n    this.toastType = toastType;\n    this.toastRef = toastRef;\n    this._onTap = new Subject();\n    this._onAction = new Subject();\n    this.toastRef.afterClosed().subscribe(() => {\n      this._onAction.complete();\n\n      this._onTap.complete();\n    });\n  }\n  /** Fired on click */\n\n\n  triggerTap() {\n    this._onTap.next();\n\n    if (this.config.tapToDismiss) {\n      this._onTap.complete();\n    }\n  }\n\n  onTap() {\n    return this._onTap.asObservable();\n  }\n  /** available for use in custom toast */\n\n\n  triggerAction(action) {\n    this._onAction.next(action);\n  }\n\n  onAction() {\n    return this._onAction.asObservable();\n  }\n\n}\n\nconst DefaultNoComponentGlobalConfig = {\n  maxOpened: 0,\n  autoDismiss: false,\n  newestOnTop: true,\n  preventDuplicates: false,\n  countDuplicates: false,\n  resetTimeoutOnDuplicate: false,\n  includeTitleDuplicates: false,\n  iconClasses: {\n    error: 'toast-error',\n    info: 'toast-info',\n    success: 'toast-success',\n    warning: 'toast-warning'\n  },\n  // Individual\n  closeButton: false,\n  disableTimeOut: false,\n  timeOut: 5000,\n  extendedTimeOut: 1000,\n  enableHtml: false,\n  progressBar: false,\n  toastClass: 'ngx-toastr',\n  positionClass: 'toast-top-right',\n  titleClass: 'toast-title',\n  messageClass: 'toast-message',\n  easing: 'ease-in',\n  easeTime: 300,\n  tapToDismiss: true,\n  onActivateTick: false,\n  progressAnimation: 'decreasing',\n  payload: null\n};\nconst TOAST_CONFIG = new InjectionToken('ToastConfig');\n/**\n * Reference to a toast opened via the Toastr service.\n */\n\nclass ToastRef {\n  constructor(_overlayRef) {\n    this._overlayRef = _overlayRef;\n    /** Count of duplicates of this toast */\n\n    this.duplicatesCount = 0;\n    /** Subject for notifying the user that the toast has finished closing. */\n\n    this._afterClosed = new Subject();\n    /** triggered when toast is activated */\n\n    this._activate = new Subject();\n    /** notifies the toast that it should close before the timeout */\n\n    this._manualClose = new Subject();\n    /** notifies the toast that it should reset the timeouts */\n\n    this._resetTimeout = new Subject();\n    /** notifies the toast that it should count a duplicate toast */\n\n    this._countDuplicate = new Subject();\n  }\n\n  manualClose() {\n    this._manualClose.next();\n\n    this._manualClose.complete();\n  }\n\n  manualClosed() {\n    return this._manualClose.asObservable();\n  }\n\n  timeoutReset() {\n    return this._resetTimeout.asObservable();\n  }\n\n  countDuplicate() {\n    return this._countDuplicate.asObservable();\n  }\n  /**\n   * Close the toast.\n   */\n\n\n  close() {\n    this._overlayRef.detach();\n\n    this._afterClosed.next();\n\n    this._manualClose.next();\n\n    this._afterClosed.complete();\n\n    this._manualClose.complete();\n\n    this._activate.complete();\n\n    this._resetTimeout.complete();\n\n    this._countDuplicate.complete();\n  }\n  /** Gets an observable that is notified when the toast is finished closing. */\n\n\n  afterClosed() {\n    return this._afterClosed.asObservable();\n  }\n\n  isInactive() {\n    return this._activate.isStopped;\n  }\n\n  activate() {\n    this._activate.next();\n\n    this._activate.complete();\n  }\n  /** Gets an observable that is notified when the toast has started opening. */\n\n\n  afterActivate() {\n    return this._activate.asObservable();\n  }\n  /** Reset the toast timouts and count duplicates */\n\n\n  onDuplicate(resetTimeout, countDuplicate) {\n    if (resetTimeout) {\n      this._resetTimeout.next();\n    }\n\n    if (countDuplicate) {\n      this._countDuplicate.next(++this.duplicatesCount);\n    }\n  }\n\n}\n/** Custom injector type specifically for instantiating components with a toast. */\n\n\nclass ToastInjector {\n  constructor(_toastPackage, _parentInjector) {\n    this._toastPackage = _toastPackage;\n    this._parentInjector = _parentInjector;\n  }\n\n  get(token, notFoundValue, flags) {\n    if (token === ToastPackage) {\n      return this._toastPackage;\n    }\n\n    return this._parentInjector.get(token, notFoundValue, flags);\n  }\n\n}\n/**\n * A PortalHost for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n *\n * This is the only part of the portal core that directly touches the DOM.\n */\n\n\nclass DomPortalHost extends BasePortalHost {\n  constructor(_hostDomElement, _componentFactoryResolver, _appRef) {\n    super();\n    this._hostDomElement = _hostDomElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   */\n\n\n  attachComponentPortal(portal, newestOnTop) {\n    const componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n\n    let componentRef; // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the ChangeDetector for that component to the application (which\n    // happens automatically when using a ViewContainer).\n\n    componentRef = componentFactory.create(portal.injector); // When creating a component outside of a ViewContainer, we need to manually register\n    // its ChangeDetector with the application. This API is unfortunately not yet published\n    // in Angular core. The change detector must also be deregistered when the component\n    // is destroyed to prevent memory leaks.\n\n    this._appRef.attachView(componentRef.hostView);\n\n    this.setDisposeFn(() => {\n      this._appRef.detachView(componentRef.hostView);\n\n      componentRef.destroy();\n    }); // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n\n    if (newestOnTop) {\n      this._hostDomElement.insertBefore(this._getComponentRootNode(componentRef), this._hostDomElement.firstChild);\n    } else {\n      this._hostDomElement.appendChild(this._getComponentRootNode(componentRef));\n    }\n\n    return componentRef;\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n\n\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n\n}\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\n\n\nclass OverlayRef {\n  constructor(_portalHost) {\n    this._portalHost = _portalHost;\n  }\n\n  attach(portal, newestOnTop = true) {\n    return this._portalHost.attach(portal, newestOnTop);\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns Resolves when the overlay has been detached.\n   */\n\n\n  detach() {\n    return this._portalHost.detach();\n  }\n\n}\n/** Container inside which all toasts will render. */\n\n\nclass OverlayContainer {\n  constructor(_document) {\n    this._document = _document;\n  }\n\n  ngOnDestroy() {\n    if (this._containerElement && this._containerElement.parentNode) {\n      this._containerElement.parentNode.removeChild(this._containerElement);\n    }\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time  it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n\n\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body\n   * and 'aria-live=\"polite\"'\n   */\n\n\n  _createContainer() {\n    const container = this._document.createElement('div');\n\n    container.classList.add('overlay-container');\n    container.setAttribute('aria-live', 'polite');\n\n    this._document.body.appendChild(container);\n\n    this._containerElement = container;\n  }\n\n}\n\nOverlayContainer.ɵfac = function OverlayContainer_Factory(t) {\n  return new (t || OverlayContainer)(i0.ɵɵinject(DOCUMENT));\n};\n\nOverlayContainer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayContainer,\n  factory: OverlayContainer.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/* eslint-disable @typescript-eslint/no-non-null-assertion */\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalHost, so any kind of Portal can be loaded into one.\n */\n\n\nclass Overlay {\n  constructor(_overlayContainer, _componentFactoryResolver, _appRef, _document) {\n    this._overlayContainer = _overlayContainer;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._document = _document; // Namespace panes by overlay container\n\n    this._paneElements = new Map();\n  }\n  /**\n   * Creates an overlay.\n   * @returns A reference to the created overlay.\n   */\n\n\n  create(positionClass, overlayContainer) {\n    // get existing pane if possible\n    return this._createOverlayRef(this.getPaneElement(positionClass, overlayContainer));\n  }\n\n  getPaneElement(positionClass = '', overlayContainer) {\n    if (!this._paneElements.get(overlayContainer)) {\n      this._paneElements.set(overlayContainer, {});\n    }\n\n    if (!this._paneElements.get(overlayContainer)[positionClass]) {\n      this._paneElements.get(overlayContainer)[positionClass] = this._createPaneElement(positionClass, overlayContainer);\n    }\n\n    return this._paneElements.get(overlayContainer)[positionClass];\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n\n\n  _createPaneElement(positionClass, overlayContainer) {\n    const pane = this._document.createElement('div');\n\n    pane.id = 'toast-container';\n    pane.classList.add(positionClass);\n    pane.classList.add('toast-container');\n\n    if (!overlayContainer) {\n      this._overlayContainer.getContainerElement().appendChild(pane);\n    } else {\n      overlayContainer.getContainerElement().appendChild(pane);\n    }\n\n    return pane;\n  }\n  /**\n   * Create a DomPortalHost into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal host.\n   * @returns A portal host for the given DOM element.\n   */\n\n\n  _createPortalHost(pane) {\n    return new DomPortalHost(pane, this._componentFactoryResolver, this._appRef);\n  }\n  /**\n   * Creates an OverlayRef for an overlay in the given DOM element.\n   * @param pane DOM element for the overlay\n   */\n\n\n  _createOverlayRef(pane) {\n    return new OverlayRef(this._createPortalHost(pane));\n  }\n\n}\n\nOverlay.ɵfac = function Overlay_Factory(t) {\n  return new (t || Overlay)(i0.ɵɵinject(OverlayContainer), i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(i0.ApplicationRef), i0.ɵɵinject(DOCUMENT));\n};\n\nOverlay.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Overlay,\n  factory: Overlay.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: OverlayContainer\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ApplicationRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nclass ToastrService {\n  constructor(token, overlay, _injector, sanitizer, ngZone) {\n    this.overlay = overlay;\n    this._injector = _injector;\n    this.sanitizer = sanitizer;\n    this.ngZone = ngZone;\n    this.currentlyActive = 0;\n    this.toasts = [];\n    this.index = 0;\n    this.toastrConfig = { ...token.default,\n      ...token.config\n    };\n\n    if (token.config.iconClasses) {\n      this.toastrConfig.iconClasses = { ...token.default.iconClasses,\n        ...token.config.iconClasses\n      };\n    }\n  }\n  /** show toast */\n\n\n  show(message, title, override = {}, type = '') {\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show successful toast */\n\n\n  success(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.success || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show error toast */\n\n\n  error(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.error || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show info toast */\n\n\n  info(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.info || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /** show warning toast */\n\n\n  warning(message, title, override = {}) {\n    const type = this.toastrConfig.iconClasses.warning || '';\n    return this._preBuildNotification(type, message, title, this.applyConfig(override));\n  }\n  /**\n   * Remove all or a single toast by id\n   */\n\n\n  clear(toastId) {\n    // Call every toastRef manualClose function\n    for (const toast of this.toasts) {\n      if (toastId !== undefined) {\n        if (toast.toastId === toastId) {\n          toast.toastRef.manualClose();\n          return;\n        }\n      } else {\n        toast.toastRef.manualClose();\n      }\n    }\n  }\n  /**\n   * Remove and destroy a single toast by id\n   */\n\n\n  remove(toastId) {\n    const found = this._findToast(toastId);\n\n    if (!found) {\n      return false;\n    }\n\n    found.activeToast.toastRef.close();\n    this.toasts.splice(found.index, 1);\n    this.currentlyActive = this.currentlyActive - 1;\n\n    if (!this.toastrConfig.maxOpened || !this.toasts.length) {\n      return false;\n    }\n\n    if (this.currentlyActive < this.toastrConfig.maxOpened && this.toasts[this.currentlyActive]) {\n      const p = this.toasts[this.currentlyActive].toastRef;\n\n      if (!p.isInactive()) {\n        this.currentlyActive = this.currentlyActive + 1;\n        p.activate();\n      }\n    }\n\n    return true;\n  }\n  /**\n   * Determines if toast message is already shown\n   */\n\n\n  findDuplicate(title = '', message = '', resetOnDuplicate, countDuplicates) {\n    const {\n      includeTitleDuplicates\n    } = this.toastrConfig;\n\n    for (const toast of this.toasts) {\n      const hasDuplicateTitle = includeTitleDuplicates && toast.title === title;\n\n      if ((!includeTitleDuplicates || hasDuplicateTitle) && toast.message === message) {\n        toast.toastRef.onDuplicate(resetOnDuplicate, countDuplicates);\n        return toast;\n      }\n    }\n\n    return null;\n  }\n  /** create a clone of global config and apply individual settings */\n\n\n  applyConfig(override = {}) {\n    return { ...this.toastrConfig,\n      ...override\n    };\n  }\n  /**\n   * Find toast object by id\n   */\n\n\n  _findToast(toastId) {\n    for (let i = 0; i < this.toasts.length; i++) {\n      if (this.toasts[i].toastId === toastId) {\n        return {\n          index: i,\n          activeToast: this.toasts[i]\n        };\n      }\n    }\n\n    return null;\n  }\n  /**\n   * Determines the need to run inside angular's zone then builds the toast\n   */\n\n\n  _preBuildNotification(toastType, message, title, config) {\n    if (config.onActivateTick) {\n      return this.ngZone.run(() => this._buildNotification(toastType, message, title, config));\n    }\n\n    return this._buildNotification(toastType, message, title, config);\n  }\n  /**\n   * Creates and attaches toast data to component\n   * returns the active toast, or in case preventDuplicates is enabled the original/non-duplicate active toast.\n   */\n\n\n  _buildNotification(toastType, message, title, config) {\n    if (!config.toastComponent) {\n      throw new Error('toastComponent required');\n    } // max opened and auto dismiss = true\n    // if timeout = 0 resetting it would result in setting this.hideTime = Date.now(). Hence, we only want to reset timeout if there is\n    // a timeout at all\n\n\n    const duplicate = this.findDuplicate(title, message, this.toastrConfig.resetTimeoutOnDuplicate && config.timeOut > 0, this.toastrConfig.countDuplicates);\n\n    if ((this.toastrConfig.includeTitleDuplicates && title || message) && this.toastrConfig.preventDuplicates && duplicate !== null) {\n      return duplicate;\n    }\n\n    this.previousToastMessage = message;\n    let keepInactive = false;\n\n    if (this.toastrConfig.maxOpened && this.currentlyActive >= this.toastrConfig.maxOpened) {\n      keepInactive = true;\n\n      if (this.toastrConfig.autoDismiss) {\n        this.clear(this.toasts[0].toastId);\n      }\n    }\n\n    const overlayRef = this.overlay.create(config.positionClass, this.overlayContainer);\n    this.index = this.index + 1;\n    let sanitizedMessage = message;\n\n    if (message && config.enableHtml) {\n      sanitizedMessage = this.sanitizer.sanitize(SecurityContext.HTML, message);\n    }\n\n    const toastRef = new ToastRef(overlayRef);\n    const toastPackage = new ToastPackage(this.index, config, sanitizedMessage, title, toastType, toastRef);\n    const toastInjector = new ToastInjector(toastPackage, this._injector);\n    const component = new ComponentPortal(config.toastComponent, toastInjector);\n    const portal = overlayRef.attach(component, this.toastrConfig.newestOnTop);\n    toastRef.componentInstance = portal.instance;\n    const ins = {\n      toastId: this.index,\n      title: title || '',\n      message: message || '',\n      toastRef,\n      onShown: toastRef.afterActivate(),\n      onHidden: toastRef.afterClosed(),\n      onTap: toastPackage.onTap(),\n      onAction: toastPackage.onAction(),\n      portal\n    };\n\n    if (!keepInactive) {\n      this.currentlyActive = this.currentlyActive + 1;\n      setTimeout(() => {\n        ins.toastRef.activate();\n      });\n    }\n\n    this.toasts.push(ins);\n    return ins;\n  }\n\n}\n\nToastrService.ɵfac = function ToastrService_Factory(t) {\n  return new (t || ToastrService)(i0.ɵɵinject(TOAST_CONFIG), i0.ɵɵinject(Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(i0.NgZone));\n};\n\nToastrService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ToastrService,\n  factory: ToastrService.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastrService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [TOAST_CONFIG]\n      }]\n    }, {\n      type: Overlay\n    }, {\n      type: i0.Injector\n    }, {\n      type: i2.DomSanitizer\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n\nclass Toast {\n  constructor(toastrService, toastPackage, ngZone) {\n    this.toastrService = toastrService;\n    this.toastPackage = toastPackage;\n    this.ngZone = ngZone;\n    /** width of progress bar */\n\n    this.width = -1;\n    /** a combination of toast type and options.toastClass */\n\n    this.toastClasses = '';\n    /** controls animation */\n\n    this.state = {\n      value: 'inactive',\n      params: {\n        easeTime: this.toastPackage.config.easeTime,\n        easing: 'ease-in'\n      }\n    };\n    this.message = toastPackage.message;\n    this.title = toastPackage.title;\n    this.options = toastPackage.config;\n    this.originalTimeout = toastPackage.config.timeOut;\n    this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n    this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n      this.activateToast();\n    });\n    this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n      this.remove();\n    });\n    this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n      this.resetTimeout();\n    });\n    this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n      this.duplicatesCount = count;\n    });\n  }\n  /** hides component when waiting to be displayed */\n\n\n  get displayStyle() {\n    if (this.state.value === 'inactive') {\n      return 'none';\n    }\n\n    return;\n  }\n\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    this.sub1.unsubscribe();\n    this.sub2.unsubscribe();\n    this.sub3.unsubscribe();\n    clearInterval(this.intervalId);\n    clearTimeout(this.timeout);\n  }\n  /**\n   * activates toast and sets timeout\n   */\n\n\n  activateToast() {\n    this.state = { ...this.state,\n      value: 'active'\n    };\n\n    if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n      this.outsideTimeout(() => this.remove(), this.options.timeOut);\n      this.hideTime = new Date().getTime() + this.options.timeOut;\n\n      if (this.options.progressBar) {\n        this.outsideInterval(() => this.updateProgress(), 10);\n      }\n    }\n  }\n  /**\n   * updates progress bar width\n   */\n\n\n  updateProgress() {\n    if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n      return;\n    }\n\n    const now = new Date().getTime();\n    const remaining = this.hideTime - now;\n    this.width = remaining / this.options.timeOut * 100;\n\n    if (this.options.progressAnimation === 'increasing') {\n      this.width = 100 - this.width;\n    }\n\n    if (this.width <= 0) {\n      this.width = 0;\n    }\n\n    if (this.width >= 100) {\n      this.width = 100;\n    }\n  }\n\n  resetTimeout() {\n    clearTimeout(this.timeout);\n    clearInterval(this.intervalId);\n    this.state = { ...this.state,\n      value: 'active'\n    };\n    this.outsideTimeout(() => this.remove(), this.originalTimeout);\n    this.options.timeOut = this.originalTimeout;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width = -1;\n\n    if (this.options.progressBar) {\n      this.outsideInterval(() => this.updateProgress(), 10);\n    }\n  }\n  /**\n   * tells toastrService to remove this toast after animation time\n   */\n\n\n  remove() {\n    if (this.state.value === 'removed') {\n      return;\n    }\n\n    clearTimeout(this.timeout);\n    this.state = { ...this.state,\n      value: 'removed'\n    };\n    this.outsideTimeout(() => this.toastrService.remove(this.toastPackage.toastId), +this.toastPackage.config.easeTime);\n  }\n\n  tapToast() {\n    if (this.state.value === 'removed') {\n      return;\n    }\n\n    this.toastPackage.triggerTap();\n\n    if (this.options.tapToDismiss) {\n      this.remove();\n    }\n  }\n\n  stickAround() {\n    if (this.state.value === 'removed') {\n      return;\n    }\n\n    clearTimeout(this.timeout);\n    this.options.timeOut = 0;\n    this.hideTime = 0; // disable progressBar\n\n    clearInterval(this.intervalId);\n    this.width = 0;\n  }\n\n  delayedHideToast() {\n    if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state.value === 'removed') {\n      return;\n    }\n\n    this.outsideTimeout(() => this.remove(), this.options.extendedTimeOut);\n    this.options.timeOut = this.options.extendedTimeOut;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width = -1;\n\n    if (this.options.progressBar) {\n      this.outsideInterval(() => this.updateProgress(), 10);\n    }\n  }\n\n  outsideTimeout(func, timeout) {\n    if (this.ngZone) {\n      this.ngZone.runOutsideAngular(() => this.timeout = setTimeout(() => this.runInsideAngular(func), timeout));\n    } else {\n      this.timeout = setTimeout(() => func(), timeout);\n    }\n  }\n\n  outsideInterval(func, timeout) {\n    if (this.ngZone) {\n      this.ngZone.runOutsideAngular(() => this.intervalId = setInterval(() => this.runInsideAngular(func), timeout));\n    } else {\n      this.intervalId = setInterval(() => func(), timeout);\n    }\n  }\n\n  runInsideAngular(func) {\n    if (this.ngZone) {\n      this.ngZone.run(() => func());\n    } else {\n      func();\n    }\n  }\n\n}\n\nToast.ɵfac = function Toast_Factory(t) {\n  return new (t || Toast)(i0.ɵɵdirectiveInject(ToastrService), i0.ɵɵdirectiveInject(ToastPackage), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nToast.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Toast,\n  selectors: [[\"\", \"toast-component\", \"\"]],\n  hostVars: 5,\n  hostBindings: function Toast_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function Toast_click_HostBindingHandler() {\n        return ctx.tapToast();\n      })(\"mouseenter\", function Toast_mouseenter_HostBindingHandler() {\n        return ctx.stickAround();\n      })(\"mouseleave\", function Toast_mouseleave_HostBindingHandler() {\n        return ctx.delayedHideToast();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵsyntheticHostProperty(\"@flyInOut\", ctx.state);\n      i0.ɵɵclassMap(ctx.toastClasses);\n      i0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n    }\n  },\n  attrs: _c0,\n  decls: 5,\n  vars: 5,\n  consts: [[\"type\", \"button\", \"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alert\", 3, \"innerHTML\"], [\"role\", \"alert\"], [1, \"toast-progress\"]],\n  template: function Toast_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, Toast_button_0_Template, 3, 0, \"button\", 0);\n      i0.ɵɵtemplate(1, Toast_div_1_Template, 3, 5, \"div\", 1);\n      i0.ɵɵtemplate(2, Toast_div_2_Template, 1, 3, \"div\", 2);\n      i0.ɵɵtemplate(3, Toast_div_3_Template, 2, 4, \"div\", 3);\n      i0.ɵɵtemplate(4, Toast_div_4_Template, 2, 2, \"div\", 4);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n    }\n  },\n  directives: [i3.NgIf],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('flyInOut', [state('inactive', style({\n      opacity: 0\n    })), state('active', style({\n      opacity: 1\n    })), state('removed', style({\n      opacity: 0\n    })), transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')), transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))])]\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Toast, [{\n    type: Component,\n    args: [{\n      selector: '[toast-component]',\n      template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `,\n      animations: [trigger('flyInOut', [state('inactive', style({\n        opacity: 0\n      })), state('active', style({\n        opacity: 1\n      })), state('removed', style({\n        opacity: 0\n      })), transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')), transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))])],\n      preserveWhitespaces: false\n    }]\n  }], function () {\n    return [{\n      type: ToastrService\n    }, {\n      type: ToastPackage\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    toastClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    state: [{\n      type: HostBinding,\n      args: ['@flyInOut']\n    }],\n    displayStyle: [{\n      type: HostBinding,\n      args: ['style.display']\n    }],\n    tapToast: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    stickAround: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    delayedHideToast: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\n\nconst DefaultGlobalConfig = { ...DefaultNoComponentGlobalConfig,\n  toastComponent: Toast\n};\n\nclass ToastrModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastrModule,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n\n}\n\nToastrModule.ɵfac = function ToastrModule_Factory(t) {\n  return new (t || ToastrModule)();\n};\n\nToastrModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ToastrModule\n});\nToastrModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastrModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [Toast],\n      exports: [Toast],\n      entryComponents: [Toast]\n    }]\n  }], null, null);\n})();\n\nclass ToastrComponentlessModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastrModule,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultNoComponentGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n\n}\n\nToastrComponentlessModule.ɵfac = function ToastrComponentlessModule_Factory(t) {\n  return new (t || ToastrComponentlessModule)();\n};\n\nToastrComponentlessModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ToastrComponentlessModule\n});\nToastrComponentlessModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastrComponentlessModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule]\n    }]\n  }], null, null);\n})();\n\nclass ToastNoAnimation {\n  constructor(toastrService, toastPackage, appRef) {\n    this.toastrService = toastrService;\n    this.toastPackage = toastPackage;\n    this.appRef = appRef;\n    /** width of progress bar */\n\n    this.width = -1;\n    /** a combination of toast type and options.toastClass */\n\n    this.toastClasses = '';\n    /** controls animation */\n\n    this.state = 'inactive';\n    this.message = toastPackage.message;\n    this.title = toastPackage.title;\n    this.options = toastPackage.config;\n    this.originalTimeout = toastPackage.config.timeOut;\n    this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n    this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n      this.activateToast();\n    });\n    this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n      this.remove();\n    });\n    this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n      this.resetTimeout();\n    });\n    this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n      this.duplicatesCount = count;\n    });\n  }\n  /** hides component when waiting to be displayed */\n\n\n  get displayStyle() {\n    if (this.state === 'inactive') {\n      return 'none';\n    }\n  }\n\n  ngOnDestroy() {\n    this.sub.unsubscribe();\n    this.sub1.unsubscribe();\n    this.sub2.unsubscribe();\n    this.sub3.unsubscribe();\n    clearInterval(this.intervalId);\n    clearTimeout(this.timeout);\n  }\n  /**\n   * activates toast and sets timeout\n   */\n\n\n  activateToast() {\n    this.state = 'active';\n\n    if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n      this.timeout = setTimeout(() => {\n        this.remove();\n      }, this.options.timeOut);\n      this.hideTime = new Date().getTime() + this.options.timeOut;\n\n      if (this.options.progressBar) {\n        this.intervalId = setInterval(() => this.updateProgress(), 10);\n      }\n    }\n\n    if (this.options.onActivateTick) {\n      this.appRef.tick();\n    }\n  }\n  /**\n   * updates progress bar width\n   */\n\n\n  updateProgress() {\n    if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n      return;\n    }\n\n    const now = new Date().getTime();\n    const remaining = this.hideTime - now;\n    this.width = remaining / this.options.timeOut * 100;\n\n    if (this.options.progressAnimation === 'increasing') {\n      this.width = 100 - this.width;\n    }\n\n    if (this.width <= 0) {\n      this.width = 0;\n    }\n\n    if (this.width >= 100) {\n      this.width = 100;\n    }\n  }\n\n  resetTimeout() {\n    clearTimeout(this.timeout);\n    clearInterval(this.intervalId);\n    this.state = 'active';\n    this.options.timeOut = this.originalTimeout;\n    this.timeout = setTimeout(() => this.remove(), this.originalTimeout);\n    this.hideTime = new Date().getTime() + (this.originalTimeout || 0);\n    this.width = -1;\n\n    if (this.options.progressBar) {\n      this.intervalId = setInterval(() => this.updateProgress(), 10);\n    }\n  }\n  /**\n   * tells toastrService to remove this toast after animation time\n   */\n\n\n  remove() {\n    if (this.state === 'removed') {\n      return;\n    }\n\n    clearTimeout(this.timeout);\n    this.state = 'removed';\n    this.timeout = setTimeout(() => this.toastrService.remove(this.toastPackage.toastId));\n  }\n\n  tapToast() {\n    if (this.state === 'removed') {\n      return;\n    }\n\n    this.toastPackage.triggerTap();\n\n    if (this.options.tapToDismiss) {\n      this.remove();\n    }\n  }\n\n  stickAround() {\n    if (this.state === 'removed') {\n      return;\n    }\n\n    clearTimeout(this.timeout);\n    this.options.timeOut = 0;\n    this.hideTime = 0; // disable progressBar\n\n    clearInterval(this.intervalId);\n    this.width = 0;\n  }\n\n  delayedHideToast() {\n    if (this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut' || this.options.extendedTimeOut === 0 || this.state === 'removed') {\n      return;\n    }\n\n    this.timeout = setTimeout(() => this.remove(), this.options.extendedTimeOut);\n    this.options.timeOut = this.options.extendedTimeOut;\n    this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n    this.width = -1;\n\n    if (this.options.progressBar) {\n      this.intervalId = setInterval(() => this.updateProgress(), 10);\n    }\n  }\n\n}\n\nToastNoAnimation.ɵfac = function ToastNoAnimation_Factory(t) {\n  return new (t || ToastNoAnimation)(i0.ɵɵdirectiveInject(ToastrService), i0.ɵɵdirectiveInject(ToastPackage), i0.ɵɵdirectiveInject(i0.ApplicationRef));\n};\n\nToastNoAnimation.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ToastNoAnimation,\n  selectors: [[\"\", \"toast-component\", \"\"]],\n  hostVars: 4,\n  hostBindings: function ToastNoAnimation_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function ToastNoAnimation_click_HostBindingHandler() {\n        return ctx.tapToast();\n      })(\"mouseenter\", function ToastNoAnimation_mouseenter_HostBindingHandler() {\n        return ctx.stickAround();\n      })(\"mouseleave\", function ToastNoAnimation_mouseleave_HostBindingHandler() {\n        return ctx.delayedHideToast();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.toastClasses);\n      i0.ɵɵstyleProp(\"display\", ctx.displayStyle);\n    }\n  },\n  attrs: _c0,\n  decls: 5,\n  vars: 5,\n  consts: [[\"type\", \"button\", \"class\", \"toast-close-button\", \"aria-label\", \"Close\", 3, \"click\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", \"innerHTML\", 4, \"ngIf\"], [\"role\", \"alert\", 3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"toast-close-button\", 3, \"click\"], [\"aria-hidden\", \"true\"], [\"role\", \"alert\", 3, \"innerHTML\"], [\"role\", \"alert\"], [1, \"toast-progress\"]],\n  template: function ToastNoAnimation_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, ToastNoAnimation_button_0_Template, 3, 0, \"button\", 0);\n      i0.ɵɵtemplate(1, ToastNoAnimation_div_1_Template, 3, 5, \"div\", 1);\n      i0.ɵɵtemplate(2, ToastNoAnimation_div_2_Template, 1, 3, \"div\", 2);\n      i0.ɵɵtemplate(3, ToastNoAnimation_div_3_Template, 2, 4, \"div\", 3);\n      i0.ɵɵtemplate(4, ToastNoAnimation_div_4_Template, 2, 2, \"div\", 4);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.options.closeButton);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.message && ctx.options.enableHtml);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.message && !ctx.options.enableHtml);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.options.progressBar);\n    }\n  },\n  directives: [i3.NgIf],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastNoAnimation, [{\n    type: Component,\n    args: [{\n      selector: '[toast-component]',\n      template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `\n    }]\n  }], function () {\n    return [{\n      type: ToastrService\n    }, {\n      type: ToastPackage\n    }, {\n      type: i0.ApplicationRef\n    }];\n  }, {\n    toastClasses: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    displayStyle: [{\n      type: HostBinding,\n      args: ['style.display']\n    }],\n    tapToast: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    stickAround: [{\n      type: HostListener,\n      args: ['mouseenter']\n    }],\n    delayedHideToast: [{\n      type: HostListener,\n      args: ['mouseleave']\n    }]\n  });\n})();\n\nconst DefaultNoAnimationsGlobalConfig = { ...DefaultNoComponentGlobalConfig,\n  toastComponent: ToastNoAnimation\n};\n\nclass ToastNoAnimationModule {\n  static forRoot(config = {}) {\n    return {\n      ngModule: ToastNoAnimationModule,\n      providers: [{\n        provide: TOAST_CONFIG,\n        useValue: {\n          default: DefaultNoAnimationsGlobalConfig,\n          config\n        }\n      }]\n    };\n  }\n\n}\n\nToastNoAnimationModule.ɵfac = function ToastNoAnimationModule_Factory(t) {\n  return new (t || ToastNoAnimationModule)();\n};\n\nToastNoAnimationModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ToastNoAnimationModule\n});\nToastNoAnimationModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToastNoAnimationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [ToastNoAnimation],\n      exports: [ToastNoAnimation],\n      entryComponents: [ToastNoAnimation]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BasePortalHost, ComponentPortal, DefaultGlobalConfig, DefaultNoAnimationsGlobalConfig, DefaultNoComponentGlobalConfig, Overlay, OverlayContainer, OverlayRef, TOAST_CONFIG, Toast, ToastContainerDirective, ToastContainerModule, ToastInjector, ToastNoAnimation, ToastNoAnimationModule, ToastPackage, ToastRef, ToastrComponentlessModule, ToastrModule, ToastrService };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/ngx-toastr/fesm2020/ngx-toastr.mjs"], "names": ["i0", "Directive", "NgModule", "InjectionToken", "Injectable", "Inject", "SecurityContext", "Component", "HostBinding", "HostListener", "trigger", "state", "style", "transition", "animate", "Subject", "i3", "DOCUMENT", "CommonModule", "i2", "ToastContainerDirective", "constructor", "el", "getContainerElement", "nativeElement", "ɵfac", "ElementRef", "ɵdir", "type", "args", "selector", "exportAs", "ToastContainerModule", "ɵmod", "ɵinj", "declarations", "exports", "ComponentPortal", "component", "injector", "attach", "host", "newestOnTop", "_attachedHost", "detach", "undefined", "isAttached", "setAttachedHost", "BasePortalHost", "portal", "_attachedPortal", "attachComponentPortal", "_disposeFn", "setDisposeFn", "fn", "ToastPackage", "toastId", "config", "message", "title", "toastType", "toastRef", "_onTap", "_onAction", "afterClosed", "subscribe", "complete", "triggerTap", "next", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onTap", "asObservable", "triggerAction", "action", "onAction", "DefaultNoComponentGlobalConfig", "maxOpened", "autoDismiss", "preventDuplicates", "countDuplicates", "resetTimeoutOnDuplicate", "includeTitleDuplicates", "iconClasses", "error", "info", "success", "warning", "closeButton", "disableTimeOut", "timeOut", "extendedTimeOut", "enableHtml", "progressBar", "toastClass", "positionClass", "titleClass", "messageClass", "easing", "easeTime", "onActivateTick", "progressAnimation", "payload", "TOAST_CONFIG", "ToastRef", "_overlayRef", "duplicatesCount", "_afterClosed", "_activate", "_manualClose", "_resetTimeout", "_countDuplicate", "manualClose", "manualClosed", "timeoutReset", "countDuplicate", "close", "isInactive", "isStopped", "activate", "afterActivate", "onDuplicate", "resetTimeout", "ToastInjector", "_toastPackage", "_parentInjector", "get", "token", "notFoundValue", "flags", "DomPortalHost", "_hostDomElement", "_componentFactoryResolver", "_appRef", "componentFactory", "resolveComponentFactory", "componentRef", "create", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "detach<PERSON>iew", "destroy", "insertBefore", "_getComponentRootNode", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "rootNodes", "OverlayRef", "_portalHost", "OverlayContainer", "_document", "ngOnDestroy", "_containerElement", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_createContainer", "container", "createElement", "classList", "add", "setAttribute", "body", "ɵprov", "providedIn", "decorators", "Overlay", "_overlayContainer", "_paneElements", "Map", "overlayContainer", "_createOverlayRef", "getPaneElement", "set", "_createPaneElement", "pane", "id", "_createPortalHost", "ComponentFactoryResolver", "ApplicationRef", "ToastrService", "overlay", "_injector", "sanitizer", "ngZone", "currentlyActive", "toasts", "index", "toastrConfig", "default", "show", "override", "_preBuildNotification", "applyConfig", "clear", "toast", "remove", "found", "_findToast", "activeToast", "splice", "length", "p", "findDuplicate", "resetOnDuplicate", "hasDuplicateTitle", "i", "run", "_buildNotification", "toastComponent", "Error", "duplicate", "previousToastMessage", "keepInactive", "overlayRef", "sanitizedMessage", "sanitize", "HTML", "toastPackage", "toastInjector", "componentInstance", "instance", "ins", "onShown", "onHidden", "setTimeout", "push", "Injector", "Dom<PERSON><PERSON><PERSON>zer", "NgZone", "Toast", "toastrService", "width", "toastClasses", "value", "params", "options", "originalTimeout", "sub", "activateToast", "sub1", "sub2", "sub3", "count", "displayStyle", "unsubscribe", "clearInterval", "intervalId", "clearTimeout", "timeout", "outsideTimeout", "hideTime", "Date", "getTime", "outsideInterval", "updateProgress", "now", "remaining", "tapToast", "stickAround", "delayedHideToast", "func", "runOutsideAngular", "runInsideAngular", "setInterval", "ɵcmp", "NgIf", "opacity", "template", "animations", "preserveWhitespaces", "DefaultGlobalConfig", "ToastrModule", "forRoot", "ngModule", "providers", "provide", "useValue", "imports", "entryComponents", "ToastrComponentlessModule", "ToastNoAnimation", "appRef", "tick", "DefaultNoAnimationsGlobalConfig", "ToastNoAnimationModule"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,QAApB,EAA8BC,cAA9B,EAA8CC,UAA9C,EAA0DC,MAA1D,EAAkEC,eAAlE,EAAmFC,SAAnF,EAA8FC,WAA9F,EAA2GC,YAA3G,QAA+H,eAA/H;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,SAASC,OAAT,QAAwB,MAAxB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,2BAApB;;;;;gBAU0GnB,E;;AAAAA,IAAAA,EAgxBxG,+B;AAhxBwGA,IAAAA,EAgxBpE;AAhxBoEA,MAAAA,EAgxBpE;AAAA,qBAhxBoEA,EAgxBpE;AAAA,aAAS,eAAT;AAAA,M;AAhxBoEA,IAAAA,EAixBtG,6B;AAjxBsGA,IAAAA,EAixB7E,kB;AAjxB6EA,IAAAA,EAixBtE,e;AAjxBsEA,IAAAA,EAkxBxG,e;;;;;;AAlxBwGA,IAAAA,EAoxB1F,2B;AApxB0FA,IAAAA,EAoxBpD,U;AApxBoDA,IAAAA,EAoxBzB,wB;;;;mBApxByBA,E;AAAAA,IAAAA,EAoxBpD,a;AApxBoDA,IAAAA,EAoxBpD,yD;;;;;;AApxBoDA,IAAAA,EAmxBxG,yB;AAnxBwGA,IAAAA,EAoxBtG,U;AApxBsGA,IAAAA,EAoxB1F,4E;AApxB0FA,IAAAA,EAqxBxG,e;;;;mBArxBwGA,E;AAAAA,IAAAA,EAmxBrF,sC;AAnxBqFA,IAAAA,EAmxBxD,wC;AAnxBwDA,IAAAA,EAoxBtG,a;AApxBsGA,IAAAA,EAoxBtG,2C;AApxBsGA,IAAAA,EAoxB3E,a;AApxB2EA,IAAAA,EAoxB3E,2C;;;;;;AApxB2EA,IAAAA,EAsxBxG,uB;;;;mBAtxBwGA,E;AAAAA,IAAAA,EAuxBtG,wC;AAvxBsGA,IAAAA,EAuxBvE,yCAvxBuEA,EAuxBvE,gB;;;;;;AAvxBuEA,IAAAA,EAyxBxG,4B;AAzxBwGA,IAAAA,EA2xBtG,U;AA3xBsGA,IAAAA,EA4xBxG,e;;;;mBA5xBwGA,E;AAAAA,IAAAA,EA0xBtG,wC;AA1xBsGA,IAAAA,EA0xBvE,0C;AA1xBuEA,IAAAA,EA2xBtG,a;AA3xBsGA,IAAAA,EA2xBtG,6C;;;;;;AA3xBsGA,IAAAA,EA6xBxG,yB;AA7xBwGA,IAAAA,EA8xBtG,uB;AA9xBsGA,IAAAA,EA+xBxG,e;;;;mBA/xBwGA,E;AAAAA,IAAAA,EA8xB1E,a;AA9xB0EA,IAAAA,EA8xB1E,yC;;;;;;gBA9xB0EA,E;;AAAAA,IAAAA,EAqiCxG,+B;AAriCwGA,IAAAA,EAqiCpE;AAriCoEA,MAAAA,EAqiCpE;AAAA,qBAriCoEA,EAqiCpE;AAAA,aAAS,eAAT;AAAA,M;AAriCoEA,IAAAA,EAsiCtG,6B;AAtiCsGA,IAAAA,EAsiC7E,kB;AAtiC6EA,IAAAA,EAsiCtE,e;AAtiCsEA,IAAAA,EAuiCxG,e;;;;;;AAviCwGA,IAAAA,EAyiC1F,2B;AAziC0FA,IAAAA,EAyiCpD,U;AAziCoDA,IAAAA,EAyiCzB,wB;;;;mBAziCyBA,E;AAAAA,IAAAA,EAyiCpD,a;AAziCoDA,IAAAA,EAyiCpD,yD;;;;;;AAziCoDA,IAAAA,EAwiCxG,yB;AAxiCwGA,IAAAA,EAyiCtG,U;AAziCsGA,IAAAA,EAyiC1F,uF;AAziC0FA,IAAAA,EA0iCxG,e;;;;mBA1iCwGA,E;AAAAA,IAAAA,EAwiCrF,sC;AAxiCqFA,IAAAA,EAwiCxD,wC;AAxiCwDA,IAAAA,EAyiCtG,a;AAziCsGA,IAAAA,EAyiCtG,2C;AAziCsGA,IAAAA,EAyiC3E,a;AAziC2EA,IAAAA,EAyiC3E,2C;;;;;;AAziC2EA,IAAAA,EA2iCxG,uB;;;;mBA3iCwGA,E;AAAAA,IAAAA,EA4iCtG,wC;AA5iCsGA,IAAAA,EA4iCvE,yCA5iCuEA,EA4iCvE,gB;;;;;;AA5iCuEA,IAAAA,EA8iCxG,4B;AA9iCwGA,IAAAA,EAgjCtG,U;AAhjCsGA,IAAAA,EAijCxG,e;;;;mBAjjCwGA,E;AAAAA,IAAAA,EA+iCtG,wC;AA/iCsGA,IAAAA,EA+iCvE,0C;AA/iCuEA,IAAAA,EAgjCtG,a;AAhjCsGA,IAAAA,EAgjCtG,6C;;;;;;AAhjCsGA,IAAAA,EAkjCxG,yB;AAljCwGA,IAAAA,EAmjCtG,uB;AAnjCsGA,IAAAA,EAojCxG,e;;;;mBApjCwGA,E;AAAAA,IAAAA,EAmjC1E,a;AAnjC0EA,IAAAA,EAmjC1E,yC;;;;AA3jChC,MAAMoB,uBAAN,CAA8B;AAC1BC,EAAAA,WAAW,CAACC,EAAD,EAAK;AACZ,SAAKA,EAAL,GAAUA,EAAV;AACH;;AACDC,EAAAA,mBAAmB,GAAG;AAClB,WAAO,KAAKD,EAAL,CAAQE,aAAf;AACH;;AANyB;;AAQ9BJ,uBAAuB,CAACK,IAAxB;AAAA,mBAAoHL,uBAApH,EAA0GpB,EAA1G,mBAA6JA,EAAE,CAAC0B,UAAhK;AAAA;;AACAN,uBAAuB,CAACO,IAAxB,kBAD0G3B,EAC1G;AAAA,QAAwGoB,uBAAxG;AAAA;AAAA;AAAA;;AACA;AAAA,qDAF0GpB,EAE1G,mBAA2FoB,uBAA3F,EAAgI,CAAC;AACrHQ,IAAAA,IAAI,EAAE3B,SAD+G;AAErH4B,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,kBADX;AAECC,MAAAA,QAAQ,EAAE;AAFX,KAAD;AAF+G,GAAD,CAAhI,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAEH,MAAAA,IAAI,EAAE5B,EAAE,CAAC0B;AAAX,KAAD,CAAP;AAAmC,GAN7E;AAAA;;AAOA,MAAMM,oBAAN,CAA2B;;AAE3BA,oBAAoB,CAACP,IAArB;AAAA,mBAAiHO,oBAAjH;AAAA;;AACAA,oBAAoB,CAACC,IAArB,kBAZ0GjC,EAY1G;AAAA,QAAkHgC;AAAlH;AACAA,oBAAoB,CAACE,IAArB,kBAb0GlC,EAa1G;;AACA;AAAA,qDAd0GA,EAc1G,mBAA2FgC,oBAA3F,EAA6H,CAAC;AAClHJ,IAAAA,IAAI,EAAE1B,QAD4G;AAElH2B,IAAAA,IAAI,EAAE,CAAC;AACCM,MAAAA,YAAY,EAAE,CAACf,uBAAD,CADf;AAECgB,MAAAA,OAAO,EAAE,CAAChB,uBAAD;AAFV,KAAD;AAF4G,GAAD,CAA7H;AAAA;AAQA;AACA;AACA;;;AACA,MAAMiB,eAAN,CAAsB;AAClBhB,EAAAA,WAAW,CAACiB,SAAD,EAAYC,QAAZ,EAAsB;AAC7B,SAAKD,SAAL,GAAiBA,SAAjB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACH;AACD;;;AACAC,EAAAA,MAAM,CAACC,IAAD,EAAOC,WAAP,EAAoB;AACtB,SAAKC,aAAL,GAAqBF,IAArB;AACA,WAAOA,IAAI,CAACD,MAAL,CAAY,IAAZ,EAAkBE,WAAlB,CAAP;AACH;AACD;;;AACAE,EAAAA,MAAM,GAAG;AACL,UAAMH,IAAI,GAAG,KAAKE,aAAlB;;AACA,QAAIF,IAAJ,EAAU;AACN,WAAKE,aAAL,GAAqBE,SAArB;AACA,aAAOJ,IAAI,CAACG,MAAL,EAAP;AACH;AACJ;AACD;;;AACc,MAAVE,UAAU,GAAG;AACb,WAAO,KAAKH,aAAL,IAAsB,IAA7B;AACH;AACD;AACJ;AACA;AACA;;;AACII,EAAAA,eAAe,CAACN,IAAD,EAAO;AAClB,SAAKE,aAAL,GAAqBF,IAArB;AACH;;AA5BiB;AA8BtB;AACA;AACA;AACA;;;AACA,MAAMO,cAAN,CAAqB;AACjBR,EAAAA,MAAM,CAACS,MAAD,EAASP,WAAT,EAAsB;AACxB,SAAKQ,eAAL,GAAuBD,MAAvB;AACA,WAAO,KAAKE,qBAAL,CAA2BF,MAA3B,EAAmCP,WAAnC,CAAP;AACH;;AACDE,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKM,eAAT,EAA0B;AACtB,WAAKA,eAAL,CAAqBH,eAArB;AACH;;AACD,SAAKG,eAAL,GAAuBL,SAAvB;;AACA,QAAI,KAAKO,UAAT,EAAqB;AACjB,WAAKA,UAAL;;AACA,WAAKA,UAAL,GAAkBP,SAAlB;AACH;AACJ;;AACDQ,EAAAA,YAAY,CAACC,EAAD,EAAK;AACb,SAAKF,UAAL,GAAkBE,EAAlB;AACH;;AAjBgB;AAoBrB;AACA;AACA;;;AACA,MAAMC,YAAN,CAAmB;AACflC,EAAAA,WAAW,CAACmC,OAAD,EAAUC,MAAV,EAAkBC,OAAlB,EAA2BC,KAA3B,EAAkCC,SAAlC,EAA6CC,QAA7C,EAAuD;AAC9D,SAAKL,OAAL,GAAeA,OAAf;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,MAAL,GAAc,IAAI/C,OAAJ,EAAd;AACA,SAAKgD,SAAL,GAAiB,IAAIhD,OAAJ,EAAjB;AACA,SAAK8C,QAAL,CAAcG,WAAd,GAA4BC,SAA5B,CAAsC,MAAM;AACxC,WAAKF,SAAL,CAAeG,QAAf;;AACA,WAAKJ,MAAL,CAAYI,QAAZ;AACH,KAHD;AAIH;AACD;;;AACAC,EAAAA,UAAU,GAAG;AACT,SAAKL,MAAL,CAAYM,IAAZ;;AACA,QAAI,KAAKX,MAAL,CAAYY,YAAhB,EAA8B;AAC1B,WAAKP,MAAL,CAAYI,QAAZ;AACH;AACJ;;AACDI,EAAAA,KAAK,GAAG;AACJ,WAAO,KAAKR,MAAL,CAAYS,YAAZ,EAAP;AACH;AACD;;;AACAC,EAAAA,aAAa,CAACC,MAAD,EAAS;AAClB,SAAKV,SAAL,CAAeK,IAAf,CAAoBK,MAApB;AACH;;AACDC,EAAAA,QAAQ,GAAG;AACP,WAAO,KAAKX,SAAL,CAAeQ,YAAf,EAAP;AACH;;AA/Bc;;AAiCnB,MAAMI,8BAA8B,GAAG;AACnCC,EAAAA,SAAS,EAAE,CADwB;AAEnCC,EAAAA,WAAW,EAAE,KAFsB;AAGnCnC,EAAAA,WAAW,EAAE,IAHsB;AAInCoC,EAAAA,iBAAiB,EAAE,KAJgB;AAKnCC,EAAAA,eAAe,EAAE,KALkB;AAMnCC,EAAAA,uBAAuB,EAAE,KANU;AAOnCC,EAAAA,sBAAsB,EAAE,KAPW;AAQnCC,EAAAA,WAAW,EAAE;AACTC,IAAAA,KAAK,EAAE,aADE;AAETC,IAAAA,IAAI,EAAE,YAFG;AAGTC,IAAAA,OAAO,EAAE,eAHA;AAITC,IAAAA,OAAO,EAAE;AAJA,GARsB;AAcnC;AACAC,EAAAA,WAAW,EAAE,KAfsB;AAgBnCC,EAAAA,cAAc,EAAE,KAhBmB;AAiBnCC,EAAAA,OAAO,EAAE,IAjB0B;AAkBnCC,EAAAA,eAAe,EAAE,IAlBkB;AAmBnCC,EAAAA,UAAU,EAAE,KAnBuB;AAoBnCC,EAAAA,WAAW,EAAE,KApBsB;AAqBnCC,EAAAA,UAAU,EAAE,YArBuB;AAsBnCC,EAAAA,aAAa,EAAE,iBAtBoB;AAuBnCC,EAAAA,UAAU,EAAE,aAvBuB;AAwBnCC,EAAAA,YAAY,EAAE,eAxBqB;AAyBnCC,EAAAA,MAAM,EAAE,SAzB2B;AA0BnCC,EAAAA,QAAQ,EAAE,GA1ByB;AA2BnC7B,EAAAA,YAAY,EAAE,IA3BqB;AA4BnC8B,EAAAA,cAAc,EAAE,KA5BmB;AA6BnCC,EAAAA,iBAAiB,EAAE,YA7BgB;AA8BnCC,EAAAA,OAAO,EAAE;AA9B0B,CAAvC;AAgCA,MAAMC,YAAY,GAAG,IAAInG,cAAJ,CAAmB,aAAnB,CAArB;AAEA;AACA;AACA;;AACA,MAAMoG,QAAN,CAAe;AACXlF,EAAAA,WAAW,CAACmF,WAAD,EAAc;AACrB,SAAKA,WAAL,GAAmBA,WAAnB;AACA;;AACA,SAAKC,eAAL,GAAuB,CAAvB;AACA;;AACA,SAAKC,YAAL,GAAoB,IAAI3F,OAAJ,EAApB;AACA;;AACA,SAAK4F,SAAL,GAAiB,IAAI5F,OAAJ,EAAjB;AACA;;AACA,SAAK6F,YAAL,GAAoB,IAAI7F,OAAJ,EAApB;AACA;;AACA,SAAK8F,aAAL,GAAqB,IAAI9F,OAAJ,EAArB;AACA;;AACA,SAAK+F,eAAL,GAAuB,IAAI/F,OAAJ,EAAvB;AACH;;AACDgG,EAAAA,WAAW,GAAG;AACV,SAAKH,YAAL,CAAkBxC,IAAlB;;AACA,SAAKwC,YAAL,CAAkB1C,QAAlB;AACH;;AACD8C,EAAAA,YAAY,GAAG;AACX,WAAO,KAAKJ,YAAL,CAAkBrC,YAAlB,EAAP;AACH;;AACD0C,EAAAA,YAAY,GAAG;AACX,WAAO,KAAKJ,aAAL,CAAmBtC,YAAnB,EAAP;AACH;;AACD2C,EAAAA,cAAc,GAAG;AACb,WAAO,KAAKJ,eAAL,CAAqBvC,YAArB,EAAP;AACH;AACD;AACJ;AACA;;;AACI4C,EAAAA,KAAK,GAAG;AACJ,SAAKX,WAAL,CAAiB5D,MAAjB;;AACA,SAAK8D,YAAL,CAAkBtC,IAAlB;;AACA,SAAKwC,YAAL,CAAkBxC,IAAlB;;AACA,SAAKsC,YAAL,CAAkBxC,QAAlB;;AACA,SAAK0C,YAAL,CAAkB1C,QAAlB;;AACA,SAAKyC,SAAL,CAAezC,QAAf;;AACA,SAAK2C,aAAL,CAAmB3C,QAAnB;;AACA,SAAK4C,eAAL,CAAqB5C,QAArB;AACH;AACD;;;AACAF,EAAAA,WAAW,GAAG;AACV,WAAO,KAAK0C,YAAL,CAAkBnC,YAAlB,EAAP;AACH;;AACD6C,EAAAA,UAAU,GAAG;AACT,WAAO,KAAKT,SAAL,CAAeU,SAAtB;AACH;;AACDC,EAAAA,QAAQ,GAAG;AACP,SAAKX,SAAL,CAAevC,IAAf;;AACA,SAAKuC,SAAL,CAAezC,QAAf;AACH;AACD;;;AACAqD,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAKZ,SAAL,CAAepC,YAAf,EAAP;AACH;AACD;;;AACAiD,EAAAA,WAAW,CAACC,YAAD,EAAeP,cAAf,EAA+B;AACtC,QAAIO,YAAJ,EAAkB;AACd,WAAKZ,aAAL,CAAmBzC,IAAnB;AACH;;AACD,QAAI8C,cAAJ,EAAoB;AAChB,WAAKJ,eAAL,CAAqB1C,IAArB,CAA0B,EAAE,KAAKqC,eAAjC;AACH;AACJ;;AAjEU;AAmEf;;;AACA,MAAMiB,aAAN,CAAoB;AAChBrG,EAAAA,WAAW,CAACsG,aAAD,EAAgBC,eAAhB,EAAiC;AACxC,SAAKD,aAAL,GAAqBA,aAArB;AACA,SAAKC,eAAL,GAAuBA,eAAvB;AACH;;AACDC,EAAAA,GAAG,CAACC,KAAD,EAAQC,aAAR,EAAuBC,KAAvB,EAA8B;AAC7B,QAAIF,KAAK,KAAKvE,YAAd,EAA4B;AACxB,aAAO,KAAKoE,aAAZ;AACH;;AACD,WAAO,KAAKC,eAAL,CAAqBC,GAArB,CAAyBC,KAAzB,EAAgCC,aAAhC,EAA+CC,KAA/C,CAAP;AACH;;AAVe;AAapB;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,aAAN,SAA4BjF,cAA5B,CAA2C;AACvC3B,EAAAA,WAAW,CAAC6G,eAAD,EAAkBC,yBAAlB,EAA6CC,OAA7C,EAAsD;AAC7D;AACA,SAAKF,eAAL,GAAuBA,eAAvB;AACA,SAAKC,yBAAL,GAAiCA,yBAAjC;AACA,SAAKC,OAAL,GAAeA,OAAf;AACH;AACD;AACJ;AACA;AACA;;;AACIjF,EAAAA,qBAAqB,CAACF,MAAD,EAASP,WAAT,EAAsB;AACvC,UAAM2F,gBAAgB,GAAG,KAAKF,yBAAL,CAA+BG,uBAA/B,CAAuDrF,MAAM,CAACX,SAA9D,CAAzB;;AACA,QAAIiG,YAAJ,CAFuC,CAGvC;AACA;AACA;AACA;AACA;;AACAA,IAAAA,YAAY,GAAGF,gBAAgB,CAACG,MAAjB,CAAwBvF,MAAM,CAACV,QAA/B,CAAf,CARuC,CASvC;AACA;AACA;AACA;;AACA,SAAK6F,OAAL,CAAaK,UAAb,CAAwBF,YAAY,CAACG,QAArC;;AACA,SAAKrF,YAAL,CAAkB,MAAM;AACpB,WAAK+E,OAAL,CAAaO,UAAb,CAAwBJ,YAAY,CAACG,QAArC;;AACAH,MAAAA,YAAY,CAACK,OAAb;AACH,KAHD,EAduC,CAkBvC;AACA;;AACA,QAAIlG,WAAJ,EAAiB;AACb,WAAKwF,eAAL,CAAqBW,YAArB,CAAkC,KAAKC,qBAAL,CAA2BP,YAA3B,CAAlC,EAA4E,KAAKL,eAAL,CAAqBa,UAAjG;AACH,KAFD,MAGK;AACD,WAAKb,eAAL,CAAqBc,WAArB,CAAiC,KAAKF,qBAAL,CAA2BP,YAA3B,CAAjC;AACH;;AACD,WAAOA,YAAP;AACH;AACD;;;AACAO,EAAAA,qBAAqB,CAACP,YAAD,EAAe;AAChC,WAAOA,YAAY,CAACG,QAAb,CAAsBO,SAAtB,CAAgC,CAAhC,CAAP;AACH;;AA1CsC;AA6C3C;AACA;AACA;AACA;;;AACA,MAAMC,UAAN,CAAiB;AACb7H,EAAAA,WAAW,CAAC8H,WAAD,EAAc;AACrB,SAAKA,WAAL,GAAmBA,WAAnB;AACH;;AACD3G,EAAAA,MAAM,CAACS,MAAD,EAASP,WAAW,GAAG,IAAvB,EAA6B;AAC/B,WAAO,KAAKyG,WAAL,CAAiB3G,MAAjB,CAAwBS,MAAxB,EAAgCP,WAAhC,CAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIE,EAAAA,MAAM,GAAG;AACL,WAAO,KAAKuG,WAAL,CAAiBvG,MAAjB,EAAP;AACH;;AAbY;AAgBjB;;;AACA,MAAMwG,gBAAN,CAAuB;AACnB/H,EAAAA,WAAW,CAACgI,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;;AACDC,EAAAA,WAAW,GAAG;AACV,QAAI,KAAKC,iBAAL,IAA0B,KAAKA,iBAAL,CAAuBC,UAArD,EAAiE;AAC7D,WAAKD,iBAAL,CAAuBC,UAAvB,CAAkCC,WAAlC,CAA8C,KAAKF,iBAAnD;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIhI,EAAAA,mBAAmB,GAAG;AAClB,QAAI,CAAC,KAAKgI,iBAAV,EAA6B;AACzB,WAAKG,gBAAL;AACH;;AACD,WAAO,KAAKH,iBAAZ;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIG,EAAAA,gBAAgB,GAAG;AACf,UAAMC,SAAS,GAAG,KAAKN,SAAL,CAAeO,aAAf,CAA6B,KAA7B,CAAlB;;AACAD,IAAAA,SAAS,CAACE,SAAV,CAAoBC,GAApB,CAAwB,mBAAxB;AACAH,IAAAA,SAAS,CAACI,YAAV,CAAuB,WAAvB,EAAoC,QAApC;;AACA,SAAKV,SAAL,CAAeW,IAAf,CAAoBhB,WAApB,CAAgCW,SAAhC;;AACA,SAAKJ,iBAAL,GAAyBI,SAAzB;AACH;;AAhCkB;;AAkCvBP,gBAAgB,CAAC3H,IAAjB;AAAA,mBAA6G2H,gBAA7G,EAnV0GpJ,EAmV1G,UAA+IiB,QAA/I;AAAA;;AACAmI,gBAAgB,CAACa,KAAjB,kBApV0GjK,EAoV1G;AAAA,SAAiHoJ,gBAAjH;AAAA,WAAiHA,gBAAjH;AAAA,cAA+I;AAA/I;;AACA;AAAA,qDArV0GpJ,EAqV1G,mBAA2FoJ,gBAA3F,EAAyH,CAAC;AAC9GxH,IAAAA,IAAI,EAAExB,UADwG;AAE9GyB,IAAAA,IAAI,EAAE,CAAC;AAAEqI,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFwG,GAAD,CAAzH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEtI,MAAAA,IAAI,EAAEiB,SAAR;AAAmBsH,MAAAA,UAAU,EAAE,CAAC;AAC9DvI,QAAAA,IAAI,EAAEvB,MADwD;AAE9DwB,QAAAA,IAAI,EAAE,CAACZ,QAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GANxB;AAAA;AAQA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmJ,OAAN,CAAc;AACV/I,EAAAA,WAAW,CAACgJ,iBAAD,EAAoBlC,yBAApB,EAA+CC,OAA/C,EAAwDiB,SAAxD,EAAmE;AAC1E,SAAKgB,iBAAL,GAAyBA,iBAAzB;AACA,SAAKlC,yBAAL,GAAiCA,yBAAjC;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKiB,SAAL,GAAiBA,SAAjB,CAJ0E,CAK1E;;AACA,SAAKiB,aAAL,GAAqB,IAAIC,GAAJ,EAArB;AACH;AACD;AACJ;AACA;AACA;;;AACI/B,EAAAA,MAAM,CAAC1C,aAAD,EAAgB0E,gBAAhB,EAAkC;AACpC;AACA,WAAO,KAAKC,iBAAL,CAAuB,KAAKC,cAAL,CAAoB5E,aAApB,EAAmC0E,gBAAnC,CAAvB,CAAP;AACH;;AACDE,EAAAA,cAAc,CAAC5E,aAAa,GAAG,EAAjB,EAAqB0E,gBAArB,EAAuC;AACjD,QAAI,CAAC,KAAKF,aAAL,CAAmBzC,GAAnB,CAAuB2C,gBAAvB,CAAL,EAA+C;AAC3C,WAAKF,aAAL,CAAmBK,GAAnB,CAAuBH,gBAAvB,EAAyC,EAAzC;AACH;;AACD,QAAI,CAAC,KAAKF,aAAL,CAAmBzC,GAAnB,CAAuB2C,gBAAvB,EAAyC1E,aAAzC,CAAL,EAA8D;AAC1D,WAAKwE,aAAL,CAAmBzC,GAAnB,CAAuB2C,gBAAvB,EAAyC1E,aAAzC,IAA0D,KAAK8E,kBAAL,CAAwB9E,aAAxB,EAAuC0E,gBAAvC,CAA1D;AACH;;AACD,WAAO,KAAKF,aAAL,CAAmBzC,GAAnB,CAAuB2C,gBAAvB,EAAyC1E,aAAzC,CAAP;AACH;AACD;AACJ;AACA;AACA;;;AACI8E,EAAAA,kBAAkB,CAAC9E,aAAD,EAAgB0E,gBAAhB,EAAkC;AAChD,UAAMK,IAAI,GAAG,KAAKxB,SAAL,CAAeO,aAAf,CAA6B,KAA7B,CAAb;;AACAiB,IAAAA,IAAI,CAACC,EAAL,GAAU,iBAAV;AACAD,IAAAA,IAAI,CAAChB,SAAL,CAAeC,GAAf,CAAmBhE,aAAnB;AACA+E,IAAAA,IAAI,CAAChB,SAAL,CAAeC,GAAf,CAAmB,iBAAnB;;AACA,QAAI,CAACU,gBAAL,EAAuB;AACnB,WAAKH,iBAAL,CAAuB9I,mBAAvB,GAA6CyH,WAA7C,CAAyD6B,IAAzD;AACH,KAFD,MAGK;AACDL,MAAAA,gBAAgB,CAACjJ,mBAAjB,GAAuCyH,WAAvC,CAAmD6B,IAAnD;AACH;;AACD,WAAOA,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIE,EAAAA,iBAAiB,CAACF,IAAD,EAAO;AACpB,WAAO,IAAI5C,aAAJ,CAAkB4C,IAAlB,EAAwB,KAAK1C,yBAA7B,EAAwD,KAAKC,OAA7D,CAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIqC,EAAAA,iBAAiB,CAACI,IAAD,EAAO;AACpB,WAAO,IAAI3B,UAAJ,CAAe,KAAK6B,iBAAL,CAAuBF,IAAvB,CAAf,CAAP;AACH;;AAzDS;;AA2DdT,OAAO,CAAC3I,IAAR;AAAA,mBAAoG2I,OAApG,EAja0GpK,EAia1G,UAA6HoJ,gBAA7H,GAja0GpJ,EAia1G,UAA0JA,EAAE,CAACgL,wBAA7J,GAja0GhL,EAia1G,UAAkMA,EAAE,CAACiL,cAArM,GAja0GjL,EAia1G,UAAgOiB,QAAhO;AAAA;;AACAmJ,OAAO,CAACH,KAAR,kBAla0GjK,EAka1G;AAAA,SAAwGoK,OAAxG;AAAA,WAAwGA,OAAxG;AAAA,cAA6H;AAA7H;;AACA;AAAA,qDAna0GpK,EAma1G,mBAA2FoK,OAA3F,EAAgH,CAAC;AACrGxI,IAAAA,IAAI,EAAExB,UAD+F;AAErGyB,IAAAA,IAAI,EAAE,CAAC;AAAEqI,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF+F,GAAD,CAAhH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEtI,MAAAA,IAAI,EAAEwH;AAAR,KAAD,EAA6B;AAAExH,MAAAA,IAAI,EAAE5B,EAAE,CAACgL;AAAX,KAA7B,EAAoE;AAAEpJ,MAAAA,IAAI,EAAE5B,EAAE,CAACiL;AAAX,KAApE,EAAiG;AAAErJ,MAAAA,IAAI,EAAEiB,SAAR;AAAmBsH,MAAAA,UAAU,EAAE,CAAC;AAC9JvI,QAAAA,IAAI,EAAEvB,MADwJ;AAE9JwB,QAAAA,IAAI,EAAE,CAACZ,QAAD;AAFwJ,OAAD;AAA/B,KAAjG,CAAP;AAGlB,GANxB;AAAA;;AAQA,MAAMiK,aAAN,CAAoB;AAChB7J,EAAAA,WAAW,CAACyG,KAAD,EAAQqD,OAAR,EAAiBC,SAAjB,EAA4BC,SAA5B,EAAuCC,MAAvC,EAA+C;AACtD,SAAKH,OAAL,GAAeA,OAAf;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,eAAL,GAAuB,CAAvB;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,KAAL,GAAa,CAAb;AACA,SAAKC,YAAL,GAAoB,EAChB,GAAG5D,KAAK,CAAC6D,OADO;AAEhB,SAAG7D,KAAK,CAACrE;AAFO,KAApB;;AAIA,QAAIqE,KAAK,CAACrE,MAAN,CAAayB,WAAjB,EAA8B;AAC1B,WAAKwG,YAAL,CAAkBxG,WAAlB,GAAgC,EAC5B,GAAG4C,KAAK,CAAC6D,OAAN,CAAczG,WADW;AAE5B,WAAG4C,KAAK,CAACrE,MAAN,CAAayB;AAFY,OAAhC;AAIH;AACJ;AACD;;;AACA0G,EAAAA,IAAI,CAAClI,OAAD,EAAUC,KAAV,EAAiBkI,QAAQ,GAAG,EAA5B,EAAgCjK,IAAI,GAAG,EAAvC,EAA2C;AAC3C,WAAO,KAAKkK,qBAAL,CAA2BlK,IAA3B,EAAiC8B,OAAjC,EAA0CC,KAA1C,EAAiD,KAAKoI,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;AACH;AACD;;;AACAxG,EAAAA,OAAO,CAAC3B,OAAD,EAAUC,KAAV,EAAiBkI,QAAQ,GAAG,EAA5B,EAAgC;AACnC,UAAMjK,IAAI,GAAG,KAAK8J,YAAL,CAAkBxG,WAAlB,CAA8BG,OAA9B,IAAyC,EAAtD;AACA,WAAO,KAAKyG,qBAAL,CAA2BlK,IAA3B,EAAiC8B,OAAjC,EAA0CC,KAA1C,EAAiD,KAAKoI,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;AACH;AACD;;;AACA1G,EAAAA,KAAK,CAACzB,OAAD,EAAUC,KAAV,EAAiBkI,QAAQ,GAAG,EAA5B,EAAgC;AACjC,UAAMjK,IAAI,GAAG,KAAK8J,YAAL,CAAkBxG,WAAlB,CAA8BC,KAA9B,IAAuC,EAApD;AACA,WAAO,KAAK2G,qBAAL,CAA2BlK,IAA3B,EAAiC8B,OAAjC,EAA0CC,KAA1C,EAAiD,KAAKoI,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;AACH;AACD;;;AACAzG,EAAAA,IAAI,CAAC1B,OAAD,EAAUC,KAAV,EAAiBkI,QAAQ,GAAG,EAA5B,EAAgC;AAChC,UAAMjK,IAAI,GAAG,KAAK8J,YAAL,CAAkBxG,WAAlB,CAA8BE,IAA9B,IAAsC,EAAnD;AACA,WAAO,KAAK0G,qBAAL,CAA2BlK,IAA3B,EAAiC8B,OAAjC,EAA0CC,KAA1C,EAAiD,KAAKoI,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;AACH;AACD;;;AACAvG,EAAAA,OAAO,CAAC5B,OAAD,EAAUC,KAAV,EAAiBkI,QAAQ,GAAG,EAA5B,EAAgC;AACnC,UAAMjK,IAAI,GAAG,KAAK8J,YAAL,CAAkBxG,WAAlB,CAA8BI,OAA9B,IAAyC,EAAtD;AACA,WAAO,KAAKwG,qBAAL,CAA2BlK,IAA3B,EAAiC8B,OAAjC,EAA0CC,KAA1C,EAAiD,KAAKoI,WAAL,CAAiBF,QAAjB,CAAjD,CAAP;AACH;AACD;AACJ;AACA;;;AACIG,EAAAA,KAAK,CAACxI,OAAD,EAAU;AACX;AACA,SAAK,MAAMyI,KAAX,IAAoB,KAAKT,MAAzB,EAAiC;AAC7B,UAAIhI,OAAO,KAAKX,SAAhB,EAA2B;AACvB,YAAIoJ,KAAK,CAACzI,OAAN,KAAkBA,OAAtB,EAA+B;AAC3ByI,UAAAA,KAAK,CAACpI,QAAN,CAAekD,WAAf;AACA;AACH;AACJ,OALD,MAMK;AACDkF,QAAAA,KAAK,CAACpI,QAAN,CAAekD,WAAf;AACH;AACJ;AACJ;AACD;AACJ;AACA;;;AACImF,EAAAA,MAAM,CAAC1I,OAAD,EAAU;AACZ,UAAM2I,KAAK,GAAG,KAAKC,UAAL,CAAgB5I,OAAhB,CAAd;;AACA,QAAI,CAAC2I,KAAL,EAAY;AACR,aAAO,KAAP;AACH;;AACDA,IAAAA,KAAK,CAACE,WAAN,CAAkBxI,QAAlB,CAA2BsD,KAA3B;AACA,SAAKqE,MAAL,CAAYc,MAAZ,CAAmBH,KAAK,CAACV,KAAzB,EAAgC,CAAhC;AACA,SAAKF,eAAL,GAAuB,KAAKA,eAAL,GAAuB,CAA9C;;AACA,QAAI,CAAC,KAAKG,YAAL,CAAkB9G,SAAnB,IAAgC,CAAC,KAAK4G,MAAL,CAAYe,MAAjD,EAAyD;AACrD,aAAO,KAAP;AACH;;AACD,QAAI,KAAKhB,eAAL,GAAuB,KAAKG,YAAL,CAAkB9G,SAAzC,IAAsD,KAAK4G,MAAL,CAAY,KAAKD,eAAjB,CAA1D,EAA6F;AACzF,YAAMiB,CAAC,GAAG,KAAKhB,MAAL,CAAY,KAAKD,eAAjB,EAAkC1H,QAA5C;;AACA,UAAI,CAAC2I,CAAC,CAACpF,UAAF,EAAL,EAAqB;AACjB,aAAKmE,eAAL,GAAuB,KAAKA,eAAL,GAAuB,CAA9C;AACAiB,QAAAA,CAAC,CAAClF,QAAF;AACH;AACJ;;AACD,WAAO,IAAP;AACH;AACD;AACJ;AACA;;;AACImF,EAAAA,aAAa,CAAC9I,KAAK,GAAG,EAAT,EAAaD,OAAO,GAAG,EAAvB,EAA2BgJ,gBAA3B,EAA6C3H,eAA7C,EAA8D;AACvE,UAAM;AAAEE,MAAAA;AAAF,QAA6B,KAAKyG,YAAxC;;AACA,SAAK,MAAMO,KAAX,IAAoB,KAAKT,MAAzB,EAAiC;AAC7B,YAAMmB,iBAAiB,GAAG1H,sBAAsB,IAAIgH,KAAK,CAACtI,KAAN,KAAgBA,KAApE;;AACA,UAAI,CAAC,CAACsB,sBAAD,IAA2B0H,iBAA5B,KAAkDV,KAAK,CAACvI,OAAN,KAAkBA,OAAxE,EAAiF;AAC7EuI,QAAAA,KAAK,CAACpI,QAAN,CAAe2D,WAAf,CAA2BkF,gBAA3B,EAA6C3H,eAA7C;AACA,eAAOkH,KAAP;AACH;AACJ;;AACD,WAAO,IAAP;AACH;AACD;;;AACAF,EAAAA,WAAW,CAACF,QAAQ,GAAG,EAAZ,EAAgB;AACvB,WAAO,EAAE,GAAG,KAAKH,YAAV;AAAwB,SAAGG;AAA3B,KAAP;AACH;AACD;AACJ;AACA;;;AACIO,EAAAA,UAAU,CAAC5I,OAAD,EAAU;AAChB,SAAK,IAAIoJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKpB,MAAL,CAAYe,MAAhC,EAAwCK,CAAC,EAAzC,EAA6C;AACzC,UAAI,KAAKpB,MAAL,CAAYoB,CAAZ,EAAepJ,OAAf,KAA2BA,OAA/B,EAAwC;AACpC,eAAO;AAAEiI,UAAAA,KAAK,EAAEmB,CAAT;AAAYP,UAAAA,WAAW,EAAE,KAAKb,MAAL,CAAYoB,CAAZ;AAAzB,SAAP;AACH;AACJ;;AACD,WAAO,IAAP;AACH;AACD;AACJ;AACA;;;AACId,EAAAA,qBAAqB,CAAClI,SAAD,EAAYF,OAAZ,EAAqBC,KAArB,EAA4BF,MAA5B,EAAoC;AACrD,QAAIA,MAAM,CAAC0C,cAAX,EAA2B;AACvB,aAAO,KAAKmF,MAAL,CAAYuB,GAAZ,CAAgB,MAAM,KAAKC,kBAAL,CAAwBlJ,SAAxB,EAAmCF,OAAnC,EAA4CC,KAA5C,EAAmDF,MAAnD,CAAtB,CAAP;AACH;;AACD,WAAO,KAAKqJ,kBAAL,CAAwBlJ,SAAxB,EAAmCF,OAAnC,EAA4CC,KAA5C,EAAmDF,MAAnD,CAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIqJ,EAAAA,kBAAkB,CAAClJ,SAAD,EAAYF,OAAZ,EAAqBC,KAArB,EAA4BF,MAA5B,EAAoC;AAClD,QAAI,CAACA,MAAM,CAACsJ,cAAZ,EAA4B;AACxB,YAAM,IAAIC,KAAJ,CAAU,yBAAV,CAAN;AACH,KAHiD,CAIlD;AACA;AACA;;;AACA,UAAMC,SAAS,GAAG,KAAKR,aAAL,CAAmB9I,KAAnB,EAA0BD,OAA1B,EAAmC,KAAKgI,YAAL,CAAkB1G,uBAAlB,IAA6CvB,MAAM,CAACgC,OAAP,GAAiB,CAAjG,EAAoG,KAAKiG,YAAL,CAAkB3G,eAAtH,CAAlB;;AACA,QAAI,CAAE,KAAK2G,YAAL,CAAkBzG,sBAAlB,IAA4CtB,KAA7C,IAAuDD,OAAxD,KACA,KAAKgI,YAAL,CAAkB5G,iBADlB,IAEAmI,SAAS,KAAK,IAFlB,EAEwB;AACpB,aAAOA,SAAP;AACH;;AACD,SAAKC,oBAAL,GAA4BxJ,OAA5B;AACA,QAAIyJ,YAAY,GAAG,KAAnB;;AACA,QAAI,KAAKzB,YAAL,CAAkB9G,SAAlB,IAA+B,KAAK2G,eAAL,IAAwB,KAAKG,YAAL,CAAkB9G,SAA7E,EAAwF;AACpFuI,MAAAA,YAAY,GAAG,IAAf;;AACA,UAAI,KAAKzB,YAAL,CAAkB7G,WAAtB,EAAmC;AAC/B,aAAKmH,KAAL,CAAW,KAAKR,MAAL,CAAY,CAAZ,EAAehI,OAA1B;AACH;AACJ;;AACD,UAAM4J,UAAU,GAAG,KAAKjC,OAAL,CAAa3C,MAAb,CAAoB/E,MAAM,CAACqC,aAA3B,EAA0C,KAAK0E,gBAA/C,CAAnB;AACA,SAAKiB,KAAL,GAAa,KAAKA,KAAL,GAAa,CAA1B;AACA,QAAI4B,gBAAgB,GAAG3J,OAAvB;;AACA,QAAIA,OAAO,IAAID,MAAM,CAACkC,UAAtB,EAAkC;AAC9B0H,MAAAA,gBAAgB,GAAG,KAAKhC,SAAL,CAAeiC,QAAf,CAAwBhN,eAAe,CAACiN,IAAxC,EAA8C7J,OAA9C,CAAnB;AACH;;AACD,UAAMG,QAAQ,GAAG,IAAI0C,QAAJ,CAAa6G,UAAb,CAAjB;AACA,UAAMI,YAAY,GAAG,IAAIjK,YAAJ,CAAiB,KAAKkI,KAAtB,EAA6BhI,MAA7B,EAAqC4J,gBAArC,EAAuD1J,KAAvD,EAA8DC,SAA9D,EAAyEC,QAAzE,CAArB;AACA,UAAM4J,aAAa,GAAG,IAAI/F,aAAJ,CAAkB8F,YAAlB,EAAgC,KAAKpC,SAArC,CAAtB;AACA,UAAM9I,SAAS,GAAG,IAAID,eAAJ,CAAoBoB,MAAM,CAACsJ,cAA3B,EAA2CU,aAA3C,CAAlB;AACA,UAAMxK,MAAM,GAAGmK,UAAU,CAAC5K,MAAX,CAAkBF,SAAlB,EAA6B,KAAKoJ,YAAL,CAAkBhJ,WAA/C,CAAf;AACAmB,IAAAA,QAAQ,CAAC6J,iBAAT,GAA6BzK,MAAM,CAAC0K,QAApC;AACA,UAAMC,GAAG,GAAG;AACRpK,MAAAA,OAAO,EAAE,KAAKiI,KADN;AAER9H,MAAAA,KAAK,EAAEA,KAAK,IAAI,EAFR;AAGRD,MAAAA,OAAO,EAAEA,OAAO,IAAI,EAHZ;AAIRG,MAAAA,QAJQ;AAKRgK,MAAAA,OAAO,EAAEhK,QAAQ,CAAC0D,aAAT,EALD;AAMRuG,MAAAA,QAAQ,EAAEjK,QAAQ,CAACG,WAAT,EANF;AAORM,MAAAA,KAAK,EAAEkJ,YAAY,CAAClJ,KAAb,EAPC;AAQRI,MAAAA,QAAQ,EAAE8I,YAAY,CAAC9I,QAAb,EARF;AASRzB,MAAAA;AATQ,KAAZ;;AAWA,QAAI,CAACkK,YAAL,EAAmB;AACf,WAAK5B,eAAL,GAAuB,KAAKA,eAAL,GAAuB,CAA9C;AACAwC,MAAAA,UAAU,CAAC,MAAM;AACbH,QAAAA,GAAG,CAAC/J,QAAJ,CAAayD,QAAb;AACH,OAFS,CAAV;AAGH;;AACD,SAAKkE,MAAL,CAAYwC,IAAZ,CAAiBJ,GAAjB;AACA,WAAOA,GAAP;AACH;;AAlLe;;AAoLpB1C,aAAa,CAACzJ,IAAd;AAAA,mBAA0GyJ,aAA1G,EA/lB0GlL,EA+lB1G,UAAyIsG,YAAzI,GA/lB0GtG,EA+lB1G,UAAkKoK,OAAlK,GA/lB0GpK,EA+lB1G,UAAsLA,EAAE,CAACiO,QAAzL,GA/lB0GjO,EA+lB1G,UAA8MmB,EAAE,CAAC+M,YAAjN,GA/lB0GlO,EA+lB1G,UAA0OA,EAAE,CAACmO,MAA7O;AAAA;;AACAjD,aAAa,CAACjB,KAAd,kBAhmB0GjK,EAgmB1G;AAAA,SAA8GkL,aAA9G;AAAA,WAA8GA,aAA9G;AAAA,cAAyI;AAAzI;;AACA;AAAA,qDAjmB0GlL,EAimB1G,mBAA2FkL,aAA3F,EAAsH,CAAC;AAC3GtJ,IAAAA,IAAI,EAAExB,UADqG;AAE3GyB,IAAAA,IAAI,EAAE,CAAC;AAAEqI,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFqG,GAAD,CAAtH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEtI,MAAAA,IAAI,EAAEiB,SAAR;AAAmBsH,MAAAA,UAAU,EAAE,CAAC;AAC9DvI,QAAAA,IAAI,EAAEvB,MADwD;AAE9DwB,QAAAA,IAAI,EAAE,CAACyE,YAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAE1E,MAAAA,IAAI,EAAEwI;AAAR,KAH2B,EAGR;AAAExI,MAAAA,IAAI,EAAE5B,EAAE,CAACiO;AAAX,KAHQ,EAGe;AAAErM,MAAAA,IAAI,EAAET,EAAE,CAAC+M;AAAX,KAHf,EAG0C;AAAEtM,MAAAA,IAAI,EAAE5B,EAAE,CAACmO;AAAX,KAH1C,CAAP;AAGwE,GANlH;AAAA;;AAQA,MAAMC,KAAN,CAAY;AACR/M,EAAAA,WAAW,CAACgN,aAAD,EAAgBb,YAAhB,EAA8BlC,MAA9B,EAAsC;AAC7C,SAAK+C,aAAL,GAAqBA,aAArB;AACA,SAAKb,YAAL,GAAoBA,YAApB;AACA,SAAKlC,MAAL,GAAcA,MAAd;AACA;;AACA,SAAKgD,KAAL,GAAa,CAAC,CAAd;AACA;;AACA,SAAKC,YAAL,GAAoB,EAApB;AACA;;AACA,SAAK5N,KAAL,GAAa;AACT6N,MAAAA,KAAK,EAAE,UADE;AAETC,MAAAA,MAAM,EAAE;AACJvI,QAAAA,QAAQ,EAAE,KAAKsH,YAAL,CAAkB/J,MAAlB,CAAyByC,QAD/B;AAEJD,QAAAA,MAAM,EAAE;AAFJ;AAFC,KAAb;AAOA,SAAKvC,OAAL,GAAe8J,YAAY,CAAC9J,OAA5B;AACA,SAAKC,KAAL,GAAa6J,YAAY,CAAC7J,KAA1B;AACA,SAAK+K,OAAL,GAAelB,YAAY,CAAC/J,MAA5B;AACA,SAAKkL,eAAL,GAAuBnB,YAAY,CAAC/J,MAAb,CAAoBgC,OAA3C;AACA,SAAK8I,YAAL,GAAqB,GAAEf,YAAY,CAAC5J,SAAU,IAAG4J,YAAY,CAAC/J,MAAb,CAAoBoC,UAAW,EAAhF;AACA,SAAK+I,GAAL,GAAWpB,YAAY,CAAC3J,QAAb,CAAsB0D,aAAtB,GAAsCtD,SAAtC,CAAgD,MAAM;AAC7D,WAAK4K,aAAL;AACH,KAFU,CAAX;AAGA,SAAKC,IAAL,GAAYtB,YAAY,CAAC3J,QAAb,CAAsBmD,YAAtB,GAAqC/C,SAArC,CAA+C,MAAM;AAC7D,WAAKiI,MAAL;AACH,KAFW,CAAZ;AAGA,SAAK6C,IAAL,GAAYvB,YAAY,CAAC3J,QAAb,CAAsBoD,YAAtB,GAAqChD,SAArC,CAA+C,MAAM;AAC7D,WAAKwD,YAAL;AACH,KAFW,CAAZ;AAGA,SAAKuH,IAAL,GAAYxB,YAAY,CAAC3J,QAAb,CAAsBqD,cAAtB,GAAuCjD,SAAvC,CAAiDgL,KAAK,IAAI;AAClE,WAAKxI,eAAL,GAAuBwI,KAAvB;AACH,KAFW,CAAZ;AAGH;AACD;;;AACgB,MAAZC,YAAY,GAAG;AACf,QAAI,KAAKvO,KAAL,CAAW6N,KAAX,KAAqB,UAAzB,EAAqC;AACjC,aAAO,MAAP;AACH;;AACD;AACH;;AACDlF,EAAAA,WAAW,GAAG;AACV,SAAKsF,GAAL,CAASO,WAAT;AACA,SAAKL,IAAL,CAAUK,WAAV;AACA,SAAKJ,IAAL,CAAUI,WAAV;AACA,SAAKH,IAAL,CAAUG,WAAV;AACAC,IAAAA,aAAa,CAAC,KAAKC,UAAN,CAAb;AACAC,IAAAA,YAAY,CAAC,KAAKC,OAAN,CAAZ;AACH;AACD;AACJ;AACA;;;AACIV,EAAAA,aAAa,GAAG;AACZ,SAAKlO,KAAL,GAAa,EAAE,GAAG,KAAKA,KAAV;AAAiB6N,MAAAA,KAAK,EAAE;AAAxB,KAAb;;AACA,QAAI,EAAE,KAAKE,OAAL,CAAalJ,cAAb,KAAgC,IAAhC,IAAwC,KAAKkJ,OAAL,CAAalJ,cAAb,KAAgC,SAA1E,KAAwF,KAAKkJ,OAAL,CAAajJ,OAAzG,EAAkH;AAC9G,WAAK+J,cAAL,CAAoB,MAAM,KAAKtD,MAAL,EAA1B,EAAyC,KAAKwC,OAAL,CAAajJ,OAAtD;AACA,WAAKgK,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,KAAKjB,OAAL,CAAajJ,OAApD;;AACA,UAAI,KAAKiJ,OAAL,CAAa9I,WAAjB,EAA8B;AAC1B,aAAKgK,eAAL,CAAqB,MAAM,KAAKC,cAAL,EAA3B,EAAkD,EAAlD;AACH;AACJ;AACJ;AACD;AACJ;AACA;;;AACIA,EAAAA,cAAc,GAAG;AACb,QAAI,KAAKvB,KAAL,KAAe,CAAf,IAAoB,KAAKA,KAAL,KAAe,GAAnC,IAA0C,CAAC,KAAKI,OAAL,CAAajJ,OAA5D,EAAqE;AACjE;AACH;;AACD,UAAMqK,GAAG,GAAG,IAAIJ,IAAJ,GAAWC,OAAX,EAAZ;AACA,UAAMI,SAAS,GAAG,KAAKN,QAAL,GAAgBK,GAAlC;AACA,SAAKxB,KAAL,GAAcyB,SAAS,GAAG,KAAKrB,OAAL,CAAajJ,OAA1B,GAAqC,GAAlD;;AACA,QAAI,KAAKiJ,OAAL,CAAatI,iBAAb,KAAmC,YAAvC,EAAqD;AACjD,WAAKkI,KAAL,GAAa,MAAM,KAAKA,KAAxB;AACH;;AACD,QAAI,KAAKA,KAAL,IAAc,CAAlB,EAAqB;AACjB,WAAKA,KAAL,GAAa,CAAb;AACH;;AACD,QAAI,KAAKA,KAAL,IAAc,GAAlB,EAAuB;AACnB,WAAKA,KAAL,GAAa,GAAb;AACH;AACJ;;AACD7G,EAAAA,YAAY,GAAG;AACX6H,IAAAA,YAAY,CAAC,KAAKC,OAAN,CAAZ;AACAH,IAAAA,aAAa,CAAC,KAAKC,UAAN,CAAb;AACA,SAAK1O,KAAL,GAAa,EAAE,GAAG,KAAKA,KAAV;AAAiB6N,MAAAA,KAAK,EAAE;AAAxB,KAAb;AACA,SAAKgB,cAAL,CAAoB,MAAM,KAAKtD,MAAL,EAA1B,EAAyC,KAAKyC,eAA9C;AACA,SAAKD,OAAL,CAAajJ,OAAb,GAAuB,KAAKkJ,eAA5B;AACA,SAAKc,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,MAAwB,KAAKjB,OAAL,CAAajJ,OAAb,IAAwB,CAAhD,CAAhB;AACA,SAAK6I,KAAL,GAAa,CAAC,CAAd;;AACA,QAAI,KAAKI,OAAL,CAAa9I,WAAjB,EAA8B;AAC1B,WAAKgK,eAAL,CAAqB,MAAM,KAAKC,cAAL,EAA3B,EAAkD,EAAlD;AACH;AACJ;AACD;AACJ;AACA;;;AACI3D,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKvL,KAAL,CAAW6N,KAAX,KAAqB,SAAzB,EAAoC;AAChC;AACH;;AACDc,IAAAA,YAAY,CAAC,KAAKC,OAAN,CAAZ;AACA,SAAK5O,KAAL,GAAa,EAAE,GAAG,KAAKA,KAAV;AAAiB6N,MAAAA,KAAK,EAAE;AAAxB,KAAb;AACA,SAAKgB,cAAL,CAAoB,MAAM,KAAKnB,aAAL,CAAmBnC,MAAnB,CAA0B,KAAKsB,YAAL,CAAkBhK,OAA5C,CAA1B,EAAgF,CAAC,KAAKgK,YAAL,CAAkB/J,MAAlB,CAAyByC,QAA1G;AACH;;AACD8J,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKrP,KAAL,CAAW6N,KAAX,KAAqB,SAAzB,EAAoC;AAChC;AACH;;AACD,SAAKhB,YAAL,CAAkBrJ,UAAlB;;AACA,QAAI,KAAKuK,OAAL,CAAarK,YAAjB,EAA+B;AAC3B,WAAK6H,MAAL;AACH;AACJ;;AACD+D,EAAAA,WAAW,GAAG;AACV,QAAI,KAAKtP,KAAL,CAAW6N,KAAX,KAAqB,SAAzB,EAAoC;AAChC;AACH;;AACDc,IAAAA,YAAY,CAAC,KAAKC,OAAN,CAAZ;AACA,SAAKb,OAAL,CAAajJ,OAAb,GAAuB,CAAvB;AACA,SAAKgK,QAAL,GAAgB,CAAhB,CANU,CAOV;;AACAL,IAAAA,aAAa,CAAC,KAAKC,UAAN,CAAb;AACA,SAAKf,KAAL,GAAa,CAAb;AACH;;AACD4B,EAAAA,gBAAgB,GAAG;AACf,QAAK,KAAKxB,OAAL,CAAalJ,cAAb,KAAgC,IAAhC,IAAwC,KAAKkJ,OAAL,CAAalJ,cAAb,KAAgC,iBAAzE,IACA,KAAKkJ,OAAL,CAAahJ,eAAb,KAAiC,CADjC,IAEA,KAAK/E,KAAL,CAAW6N,KAAX,KAAqB,SAFzB,EAEoC;AAChC;AACH;;AACD,SAAKgB,cAAL,CAAoB,MAAM,KAAKtD,MAAL,EAA1B,EAAyC,KAAKwC,OAAL,CAAahJ,eAAtD;AACA,SAAKgJ,OAAL,CAAajJ,OAAb,GAAuB,KAAKiJ,OAAL,CAAahJ,eAApC;AACA,SAAK+J,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,MAAwB,KAAKjB,OAAL,CAAajJ,OAAb,IAAwB,CAAhD,CAAhB;AACA,SAAK6I,KAAL,GAAa,CAAC,CAAd;;AACA,QAAI,KAAKI,OAAL,CAAa9I,WAAjB,EAA8B;AAC1B,WAAKgK,eAAL,CAAqB,MAAM,KAAKC,cAAL,EAA3B,EAAkD,EAAlD;AACH;AACJ;;AACDL,EAAAA,cAAc,CAACW,IAAD,EAAOZ,OAAP,EAAgB;AAC1B,QAAI,KAAKjE,MAAT,EAAiB;AACb,WAAKA,MAAL,CAAY8E,iBAAZ,CAA8B,MAAO,KAAKb,OAAL,GAAexB,UAAU,CAAC,MAAM,KAAKsC,gBAAL,CAAsBF,IAAtB,CAAP,EAAoCZ,OAApC,CAA9D;AACH,KAFD,MAGK;AACD,WAAKA,OAAL,GAAexB,UAAU,CAAC,MAAMoC,IAAI,EAAX,EAAeZ,OAAf,CAAzB;AACH;AACJ;;AACDK,EAAAA,eAAe,CAACO,IAAD,EAAOZ,OAAP,EAAgB;AAC3B,QAAI,KAAKjE,MAAT,EAAiB;AACb,WAAKA,MAAL,CAAY8E,iBAAZ,CAA8B,MAAO,KAAKf,UAAL,GAAkBiB,WAAW,CAAC,MAAM,KAAKD,gBAAL,CAAsBF,IAAtB,CAAP,EAAoCZ,OAApC,CAAlE;AACH,KAFD,MAGK;AACD,WAAKF,UAAL,GAAkBiB,WAAW,CAAC,MAAMH,IAAI,EAAX,EAAeZ,OAAf,CAA7B;AACH;AACJ;;AACDc,EAAAA,gBAAgB,CAACF,IAAD,EAAO;AACnB,QAAI,KAAK7E,MAAT,EAAiB;AACb,WAAKA,MAAL,CAAYuB,GAAZ,CAAgB,MAAMsD,IAAI,EAA1B;AACH,KAFD,MAGK;AACDA,MAAAA,IAAI;AACP;AACJ;;AAnKO;;AAqKZ/B,KAAK,CAAC3M,IAAN;AAAA,mBAAkG2M,KAAlG,EA9wB0GpO,EA8wB1G,mBAAyHkL,aAAzH,GA9wB0GlL,EA8wB1G,mBAAmJuD,YAAnJ,GA9wB0GvD,EA8wB1G,mBAA4KA,EAAE,CAACmO,MAA/K;AAAA;;AACAC,KAAK,CAACmC,IAAN,kBA/wB0GvQ,EA+wB1G;AAAA,QAAsFoO,KAAtF;AAAA;AAAA;AAAA;AAAA;AA/wB0GpO,MAAAA,EA+wB1G;AAAA,eAAsF,cAAtF;AAAA;AAAA,eAAsF,iBAAtF;AAAA;AAAA,eAAsF,sBAAtF;AAAA;AAAA;;AAAA;AA/wB0GA,MAAAA,EA+wB1G;AA/wB0GA,MAAAA,EA+wB1G;AA/wB0GA,MAAAA,EA+wB1G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA/wB0GA,MAAAA,EAgxBxG,0DADF;AA/wB0GA,MAAAA,EAmxBxG,oDAJF;AA/wB0GA,MAAAA,EAsxBxG,oDAPF;AA/wB0GA,MAAAA,EAyxBxG,oDAVF;AA/wB0GA,MAAAA,EA6xBxG,oDAdF;AAAA;;AAAA;AA/wB0GA,MAAAA,EAgxB/F,4CADX;AA/wB0GA,MAAAA,EAmxBlG,aAJR;AA/wB0GA,MAAAA,EAmxBlG,8BAJR;AA/wB0GA,MAAAA,EAsxBlG,aAPR;AA/wB0GA,MAAAA,EAsxBlG,0DAPR;AA/wB0GA,MAAAA,EAyxBlG,aAVR;AA/wB0GA,MAAAA,EAyxBlG,2DAVR;AA/wB0GA,MAAAA,EA6xBlG,aAdR;AA/wB0GA,MAAAA,EA6xBlG,4CAdR;AAAA;AAAA;AAAA,eAiB0CgB,EAAE,CAACwP,IAjB7C;AAAA;AAAA;AAAA,eAiBgI,CACxH9P,OAAO,CAAC,UAAD,EAAa,CAChBC,KAAK,CAAC,UAAD,EAAaC,KAAK,CAAC;AAAE6P,MAAAA,OAAO,EAAE;AAAX,KAAD,CAAlB,CADW,EAEhB9P,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;AAAE6P,MAAAA,OAAO,EAAE;AAAX,KAAD,CAAhB,CAFW,EAGhB9P,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;AAAE6P,MAAAA,OAAO,EAAE;AAAX,KAAD,CAAjB,CAHW,EAIhB5P,UAAU,CAAC,oBAAD,EAAuBC,OAAO,CAAC,+BAAD,CAA9B,CAJM,EAKhBD,UAAU,CAAC,mBAAD,EAAsBC,OAAO,CAAC,+BAAD,CAA7B,CALM,CAAb,CADiH;AAjBhI;AAAA;;AA0BA;AAAA,qDAzyB0Gd,EAyyB1G,mBAA2FoO,KAA3F,EAA8G,CAAC;AACnGxM,IAAAA,IAAI,EAAErB,SAD6F;AAEnGsB,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,mBADX;AAEC4O,MAAAA,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAnBmB;AAoBCC,MAAAA,UAAU,EAAE,CACRjQ,OAAO,CAAC,UAAD,EAAa,CAChBC,KAAK,CAAC,UAAD,EAAaC,KAAK,CAAC;AAAE6P,QAAAA,OAAO,EAAE;AAAX,OAAD,CAAlB,CADW,EAEhB9P,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;AAAE6P,QAAAA,OAAO,EAAE;AAAX,OAAD,CAAhB,CAFW,EAGhB9P,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;AAAE6P,QAAAA,OAAO,EAAE;AAAX,OAAD,CAAjB,CAHW,EAIhB5P,UAAU,CAAC,oBAAD,EAAuBC,OAAO,CAAC,+BAAD,CAA9B,CAJM,EAKhBD,UAAU,CAAC,mBAAD,EAAsBC,OAAO,CAAC,+BAAD,CAA7B,CALM,CAAb,CADC,CApBb;AA6BC8P,MAAAA,mBAAmB,EAAE;AA7BtB,KAAD;AAF6F,GAAD,CAA9G,EAiC4B,YAAY;AAAE,WAAO,CAAC;AAAEhP,MAAAA,IAAI,EAAEsJ;AAAR,KAAD,EAA0B;AAAEtJ,MAAAA,IAAI,EAAE2B;AAAR,KAA1B,EAAkD;AAAE3B,MAAAA,IAAI,EAAE5B,EAAE,CAACmO;AAAX,KAAlD,CAAP;AAAgF,GAjC1H,EAiC4I;AAAEI,IAAAA,YAAY,EAAE,CAAC;AAC7I3M,MAAAA,IAAI,EAAEpB,WADuI;AAE7IqB,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFuI,KAAD,CAAhB;AAG5HlB,IAAAA,KAAK,EAAE,CAAC;AACRiB,MAAAA,IAAI,EAAEpB,WADE;AAERqB,MAAAA,IAAI,EAAE,CAAC,WAAD;AAFE,KAAD,CAHqH;AAM5HqN,IAAAA,YAAY,EAAE,CAAC;AACftN,MAAAA,IAAI,EAAEpB,WADS;AAEfqB,MAAAA,IAAI,EAAE,CAAC,eAAD;AAFS,KAAD,CAN8G;AAS5HmO,IAAAA,QAAQ,EAAE,CAAC;AACXpO,MAAAA,IAAI,EAAEnB,YADK;AAEXoB,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFK,KAAD,CATkH;AAY5HoO,IAAAA,WAAW,EAAE,CAAC;AACdrO,MAAAA,IAAI,EAAEnB,YADQ;AAEdoB,MAAAA,IAAI,EAAE,CAAC,YAAD;AAFQ,KAAD,CAZ+G;AAe5HqO,IAAAA,gBAAgB,EAAE,CAAC;AACnBtO,MAAAA,IAAI,EAAEnB,YADa;AAEnBoB,MAAAA,IAAI,EAAE,CAAC,YAAD;AAFa,KAAD;AAf0G,GAjC5I;AAAA;;AAqDA,MAAMgP,mBAAmB,GAAG,EACxB,GAAGlM,8BADqB;AAExBoI,EAAAA,cAAc,EAAEqB;AAFQ,CAA5B;;AAIA,MAAM0C,YAAN,CAAmB;AACD,SAAPC,OAAO,CAACtN,MAAM,GAAG,EAAV,EAAc;AACxB,WAAO;AACHuN,MAAAA,QAAQ,EAAEF,YADP;AAEHG,MAAAA,SAAS,EAAE,CACP;AACIC,QAAAA,OAAO,EAAE5K,YADb;AAEI6K,QAAAA,QAAQ,EAAE;AACNxF,UAAAA,OAAO,EAAEkF,mBADH;AAENpN,UAAAA;AAFM;AAFd,OADO;AAFR,KAAP;AAYH;;AAdc;;AAgBnBqN,YAAY,CAACrP,IAAb;AAAA,mBAAyGqP,YAAzG;AAAA;;AACAA,YAAY,CAAC7O,IAAb,kBAn3B0GjC,EAm3B1G;AAAA,QAA0G8Q;AAA1G;AACAA,YAAY,CAAC5O,IAAb,kBAp3B0GlC,EAo3B1G;AAAA,YAAkI,CAACkB,YAAD,CAAlI;AAAA;;AACA;AAAA,qDAr3B0GlB,EAq3B1G,mBAA2F8Q,YAA3F,EAAqH,CAAC;AAC1GlP,IAAAA,IAAI,EAAE1B,QADoG;AAE1G2B,IAAAA,IAAI,EAAE,CAAC;AACCuP,MAAAA,OAAO,EAAE,CAAClQ,YAAD,CADV;AAECiB,MAAAA,YAAY,EAAE,CAACiM,KAAD,CAFf;AAGChM,MAAAA,OAAO,EAAE,CAACgM,KAAD,CAHV;AAICiD,MAAAA,eAAe,EAAE,CAACjD,KAAD;AAJlB,KAAD;AAFoG,GAAD,CAArH;AAAA;;AASA,MAAMkD,yBAAN,CAAgC;AACd,SAAPP,OAAO,CAACtN,MAAM,GAAG,EAAV,EAAc;AACxB,WAAO;AACHuN,MAAAA,QAAQ,EAAEF,YADP;AAEHG,MAAAA,SAAS,EAAE,CACP;AACIC,QAAAA,OAAO,EAAE5K,YADb;AAEI6K,QAAAA,QAAQ,EAAE;AACNxF,UAAAA,OAAO,EAAEhH,8BADH;AAENlB,UAAAA;AAFM;AAFd,OADO;AAFR,KAAP;AAYH;;AAd2B;;AAgBhC6N,yBAAyB,CAAC7P,IAA1B;AAAA,mBAAsH6P,yBAAtH;AAAA;;AACAA,yBAAyB,CAACrP,IAA1B,kBA/4B0GjC,EA+4B1G;AAAA,QAAuHsR;AAAvH;AACAA,yBAAyB,CAACpP,IAA1B,kBAh5B0GlC,EAg5B1G;AAAA,YAA4J,CAACkB,YAAD,CAA5J;AAAA;;AACA;AAAA,qDAj5B0GlB,EAi5B1G,mBAA2FsR,yBAA3F,EAAkI,CAAC;AACvH1P,IAAAA,IAAI,EAAE1B,QADiH;AAEvH2B,IAAAA,IAAI,EAAE,CAAC;AACCuP,MAAAA,OAAO,EAAE,CAAClQ,YAAD;AADV,KAAD;AAFiH,GAAD,CAAlI;AAAA;;AAOA,MAAMqQ,gBAAN,CAAuB;AACnBlQ,EAAAA,WAAW,CAACgN,aAAD,EAAgBb,YAAhB,EAA8BgE,MAA9B,EAAsC;AAC7C,SAAKnD,aAAL,GAAqBA,aAArB;AACA,SAAKb,YAAL,GAAoBA,YAApB;AACA,SAAKgE,MAAL,GAAcA,MAAd;AACA;;AACA,SAAKlD,KAAL,GAAa,CAAC,CAAd;AACA;;AACA,SAAKC,YAAL,GAAoB,EAApB;AACA;;AACA,SAAK5N,KAAL,GAAa,UAAb;AACA,SAAK+C,OAAL,GAAe8J,YAAY,CAAC9J,OAA5B;AACA,SAAKC,KAAL,GAAa6J,YAAY,CAAC7J,KAA1B;AACA,SAAK+K,OAAL,GAAelB,YAAY,CAAC/J,MAA5B;AACA,SAAKkL,eAAL,GAAuBnB,YAAY,CAAC/J,MAAb,CAAoBgC,OAA3C;AACA,SAAK8I,YAAL,GAAqB,GAAEf,YAAY,CAAC5J,SAAU,IAAG4J,YAAY,CAAC/J,MAAb,CAAoBoC,UAAW,EAAhF;AACA,SAAK+I,GAAL,GAAWpB,YAAY,CAAC3J,QAAb,CAAsB0D,aAAtB,GAAsCtD,SAAtC,CAAgD,MAAM;AAC7D,WAAK4K,aAAL;AACH,KAFU,CAAX;AAGA,SAAKC,IAAL,GAAYtB,YAAY,CAAC3J,QAAb,CAAsBmD,YAAtB,GAAqC/C,SAArC,CAA+C,MAAM;AAC7D,WAAKiI,MAAL;AACH,KAFW,CAAZ;AAGA,SAAK6C,IAAL,GAAYvB,YAAY,CAAC3J,QAAb,CAAsBoD,YAAtB,GAAqChD,SAArC,CAA+C,MAAM;AAC7D,WAAKwD,YAAL;AACH,KAFW,CAAZ;AAGA,SAAKuH,IAAL,GAAYxB,YAAY,CAAC3J,QAAb,CAAsBqD,cAAtB,GAAuCjD,SAAvC,CAAiDgL,KAAK,IAAI;AAClE,WAAKxI,eAAL,GAAuBwI,KAAvB;AACH,KAFW,CAAZ;AAGH;AACD;;;AACgB,MAAZC,YAAY,GAAG;AACf,QAAI,KAAKvO,KAAL,KAAe,UAAnB,EAA+B;AAC3B,aAAO,MAAP;AACH;AACJ;;AACD2I,EAAAA,WAAW,GAAG;AACV,SAAKsF,GAAL,CAASO,WAAT;AACA,SAAKL,IAAL,CAAUK,WAAV;AACA,SAAKJ,IAAL,CAAUI,WAAV;AACA,SAAKH,IAAL,CAAUG,WAAV;AACAC,IAAAA,aAAa,CAAC,KAAKC,UAAN,CAAb;AACAC,IAAAA,YAAY,CAAC,KAAKC,OAAN,CAAZ;AACH;AACD;AACJ;AACA;;;AACIV,EAAAA,aAAa,GAAG;AACZ,SAAKlO,KAAL,GAAa,QAAb;;AACA,QAAI,EAAE,KAAK+N,OAAL,CAAalJ,cAAb,KAAgC,IAAhC,IAAwC,KAAKkJ,OAAL,CAAalJ,cAAb,KAAgC,SAA1E,KAAwF,KAAKkJ,OAAL,CAAajJ,OAAzG,EAAkH;AAC9G,WAAK8J,OAAL,GAAexB,UAAU,CAAC,MAAM;AAC5B,aAAK7B,MAAL;AACH,OAFwB,EAEtB,KAAKwC,OAAL,CAAajJ,OAFS,CAAzB;AAGA,WAAKgK,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,KAAuB,KAAKjB,OAAL,CAAajJ,OAApD;;AACA,UAAI,KAAKiJ,OAAL,CAAa9I,WAAjB,EAA8B;AAC1B,aAAKyJ,UAAL,GAAkBiB,WAAW,CAAC,MAAM,KAAKT,cAAL,EAAP,EAA8B,EAA9B,CAA7B;AACH;AACJ;;AACD,QAAI,KAAKnB,OAAL,CAAavI,cAAjB,EAAiC;AAC7B,WAAKqL,MAAL,CAAYC,IAAZ;AACH;AACJ;AACD;AACJ;AACA;;;AACI5B,EAAAA,cAAc,GAAG;AACb,QAAI,KAAKvB,KAAL,KAAe,CAAf,IAAoB,KAAKA,KAAL,KAAe,GAAnC,IAA0C,CAAC,KAAKI,OAAL,CAAajJ,OAA5D,EAAqE;AACjE;AACH;;AACD,UAAMqK,GAAG,GAAG,IAAIJ,IAAJ,GAAWC,OAAX,EAAZ;AACA,UAAMI,SAAS,GAAG,KAAKN,QAAL,GAAgBK,GAAlC;AACA,SAAKxB,KAAL,GAAcyB,SAAS,GAAG,KAAKrB,OAAL,CAAajJ,OAA1B,GAAqC,GAAlD;;AACA,QAAI,KAAKiJ,OAAL,CAAatI,iBAAb,KAAmC,YAAvC,EAAqD;AACjD,WAAKkI,KAAL,GAAa,MAAM,KAAKA,KAAxB;AACH;;AACD,QAAI,KAAKA,KAAL,IAAc,CAAlB,EAAqB;AACjB,WAAKA,KAAL,GAAa,CAAb;AACH;;AACD,QAAI,KAAKA,KAAL,IAAc,GAAlB,EAAuB;AACnB,WAAKA,KAAL,GAAa,GAAb;AACH;AACJ;;AACD7G,EAAAA,YAAY,GAAG;AACX6H,IAAAA,YAAY,CAAC,KAAKC,OAAN,CAAZ;AACAH,IAAAA,aAAa,CAAC,KAAKC,UAAN,CAAb;AACA,SAAK1O,KAAL,GAAa,QAAb;AACA,SAAK+N,OAAL,CAAajJ,OAAb,GAAuB,KAAKkJ,eAA5B;AACA,SAAKY,OAAL,GAAexB,UAAU,CAAC,MAAM,KAAK7B,MAAL,EAAP,EAAsB,KAAKyC,eAA3B,CAAzB;AACA,SAAKc,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,MAAwB,KAAKhB,eAAL,IAAwB,CAAhD,CAAhB;AACA,SAAKL,KAAL,GAAa,CAAC,CAAd;;AACA,QAAI,KAAKI,OAAL,CAAa9I,WAAjB,EAA8B;AAC1B,WAAKyJ,UAAL,GAAkBiB,WAAW,CAAC,MAAM,KAAKT,cAAL,EAAP,EAA8B,EAA9B,CAA7B;AACH;AACJ;AACD;AACJ;AACA;;;AACI3D,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKvL,KAAL,KAAe,SAAnB,EAA8B;AAC1B;AACH;;AACD2O,IAAAA,YAAY,CAAC,KAAKC,OAAN,CAAZ;AACA,SAAK5O,KAAL,GAAa,SAAb;AACA,SAAK4O,OAAL,GAAexB,UAAU,CAAC,MAAM,KAAKM,aAAL,CAAmBnC,MAAnB,CAA0B,KAAKsB,YAAL,CAAkBhK,OAA5C,CAAP,CAAzB;AACH;;AACDwM,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKrP,KAAL,KAAe,SAAnB,EAA8B;AAC1B;AACH;;AACD,SAAK6M,YAAL,CAAkBrJ,UAAlB;;AACA,QAAI,KAAKuK,OAAL,CAAarK,YAAjB,EAA+B;AAC3B,WAAK6H,MAAL;AACH;AACJ;;AACD+D,EAAAA,WAAW,GAAG;AACV,QAAI,KAAKtP,KAAL,KAAe,SAAnB,EAA8B;AAC1B;AACH;;AACD2O,IAAAA,YAAY,CAAC,KAAKC,OAAN,CAAZ;AACA,SAAKb,OAAL,CAAajJ,OAAb,GAAuB,CAAvB;AACA,SAAKgK,QAAL,GAAgB,CAAhB,CANU,CAOV;;AACAL,IAAAA,aAAa,CAAC,KAAKC,UAAN,CAAb;AACA,SAAKf,KAAL,GAAa,CAAb;AACH;;AACD4B,EAAAA,gBAAgB,GAAG;AACf,QAAK,KAAKxB,OAAL,CAAalJ,cAAb,KAAgC,IAAhC,IAAwC,KAAKkJ,OAAL,CAAalJ,cAAb,KAAgC,iBAAzE,IACA,KAAKkJ,OAAL,CAAahJ,eAAb,KAAiC,CADjC,IAEA,KAAK/E,KAAL,KAAe,SAFnB,EAE8B;AAC1B;AACH;;AACD,SAAK4O,OAAL,GAAexB,UAAU,CAAC,MAAM,KAAK7B,MAAL,EAAP,EAAsB,KAAKwC,OAAL,CAAahJ,eAAnC,CAAzB;AACA,SAAKgJ,OAAL,CAAajJ,OAAb,GAAuB,KAAKiJ,OAAL,CAAahJ,eAApC;AACA,SAAK+J,QAAL,GAAgB,IAAIC,IAAJ,GAAWC,OAAX,MAAwB,KAAKjB,OAAL,CAAajJ,OAAb,IAAwB,CAAhD,CAAhB;AACA,SAAK6I,KAAL,GAAa,CAAC,CAAd;;AACA,QAAI,KAAKI,OAAL,CAAa9I,WAAjB,EAA8B;AAC1B,WAAKyJ,UAAL,GAAkBiB,WAAW,CAAC,MAAM,KAAKT,cAAL,EAAP,EAA8B,EAA9B,CAA7B;AACH;AACJ;;AAzIkB;;AA2IvB0B,gBAAgB,CAAC9P,IAAjB;AAAA,mBAA6G8P,gBAA7G,EAniC0GvR,EAmiC1G,mBAA+IkL,aAA/I,GAniC0GlL,EAmiC1G,mBAAyKuD,YAAzK,GAniC0GvD,EAmiC1G,mBAAkMA,EAAE,CAACiL,cAArM;AAAA;;AACAsG,gBAAgB,CAAChB,IAAjB,kBApiC0GvQ,EAoiC1G;AAAA,QAAiGuR,gBAAjG;AAAA;AAAA;AAAA;AAAA;AApiC0GvR,MAAAA,EAoiC1G;AAAA,eAAiG,cAAjG;AAAA;AAAA,eAAiG,iBAAjG;AAAA;AAAA,eAAiG,sBAAjG;AAAA;AAAA;;AAAA;AApiC0GA,MAAAA,EAoiC1G;AApiC0GA,MAAAA,EAoiC1G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AApiC0GA,MAAAA,EAqiCxG,qEADF;AApiC0GA,MAAAA,EAwiCxG,+DAJF;AApiC0GA,MAAAA,EA2iCxG,+DAPF;AApiC0GA,MAAAA,EA8iCxG,+DAVF;AApiC0GA,MAAAA,EAkjCxG,+DAdF;AAAA;;AAAA;AApiC0GA,MAAAA,EAqiC/F,4CADX;AApiC0GA,MAAAA,EAwiClG,aAJR;AApiC0GA,MAAAA,EAwiClG,8BAJR;AApiC0GA,MAAAA,EA2iClG,aAPR;AApiC0GA,MAAAA,EA2iClG,0DAPR;AApiC0GA,MAAAA,EA8iClG,aAVR;AApiC0GA,MAAAA,EA8iClG,2DAVR;AApiC0GA,MAAAA,EAkjClG,aAdR;AApiC0GA,MAAAA,EAkjClG,4CAdR;AAAA;AAAA;AAAA,eAiB0CgB,EAAE,CAACwP,IAjB7C;AAAA;AAAA;;AAkBA;AAAA,qDAtjC0GxQ,EAsjC1G,mBAA2FuR,gBAA3F,EAAyH,CAAC;AAC9G3P,IAAAA,IAAI,EAAErB,SADwG;AAE9GsB,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,mBADX;AAEC4O,MAAAA,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBmB,KAAD;AAFwG,GAAD,CAAzH,EAuB4B,YAAY;AAAE,WAAO,CAAC;AAAE9O,MAAAA,IAAI,EAAEsJ;AAAR,KAAD,EAA0B;AAAEtJ,MAAAA,IAAI,EAAE2B;AAAR,KAA1B,EAAkD;AAAE3B,MAAAA,IAAI,EAAE5B,EAAE,CAACiL;AAAX,KAAlD,CAAP;AAAwF,GAvBlI,EAuBoJ;AAAEsD,IAAAA,YAAY,EAAE,CAAC;AACrJ3M,MAAAA,IAAI,EAAEpB,WAD+I;AAErJqB,MAAAA,IAAI,EAAE,CAAC,OAAD;AAF+I,KAAD,CAAhB;AAGpIqN,IAAAA,YAAY,EAAE,CAAC;AACftN,MAAAA,IAAI,EAAEpB,WADS;AAEfqB,MAAAA,IAAI,EAAE,CAAC,eAAD;AAFS,KAAD,CAHsH;AAMpImO,IAAAA,QAAQ,EAAE,CAAC;AACXpO,MAAAA,IAAI,EAAEnB,YADK;AAEXoB,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFK,KAAD,CAN0H;AASpIoO,IAAAA,WAAW,EAAE,CAAC;AACdrO,MAAAA,IAAI,EAAEnB,YADQ;AAEdoB,MAAAA,IAAI,EAAE,CAAC,YAAD;AAFQ,KAAD,CATuH;AAYpIqO,IAAAA,gBAAgB,EAAE,CAAC;AACnBtO,MAAAA,IAAI,EAAEnB,YADa;AAEnBoB,MAAAA,IAAI,EAAE,CAAC,YAAD;AAFa,KAAD;AAZkH,GAvBpJ;AAAA;;AAuCA,MAAM6P,+BAA+B,GAAG,EACpC,GAAG/M,8BADiC;AAEpCoI,EAAAA,cAAc,EAAEwE;AAFoB,CAAxC;;AAIA,MAAMI,sBAAN,CAA6B;AACX,SAAPZ,OAAO,CAACtN,MAAM,GAAG,EAAV,EAAc;AACxB,WAAO;AACHuN,MAAAA,QAAQ,EAAEW,sBADP;AAEHV,MAAAA,SAAS,EAAE,CACP;AACIC,QAAAA,OAAO,EAAE5K,YADb;AAEI6K,QAAAA,QAAQ,EAAE;AACNxF,UAAAA,OAAO,EAAE+F,+BADH;AAENjO,UAAAA;AAFM;AAFd,OADO;AAFR,KAAP;AAYH;;AAdwB;;AAgB7BkO,sBAAsB,CAAClQ,IAAvB;AAAA,mBAAmHkQ,sBAAnH;AAAA;;AACAA,sBAAsB,CAAC1P,IAAvB,kBAlnC0GjC,EAknC1G;AAAA,QAAoH2R;AAApH;AACAA,sBAAsB,CAACzP,IAAvB,kBAnnC0GlC,EAmnC1G;AAAA,YAAsJ,CAACkB,YAAD,CAAtJ;AAAA;;AACA;AAAA,qDApnC0GlB,EAonC1G,mBAA2F2R,sBAA3F,EAA+H,CAAC;AACpH/P,IAAAA,IAAI,EAAE1B,QAD8G;AAEpH2B,IAAAA,IAAI,EAAE,CAAC;AACCuP,MAAAA,OAAO,EAAE,CAAClQ,YAAD,CADV;AAECiB,MAAAA,YAAY,EAAE,CAACoP,gBAAD,CAFf;AAGCnP,MAAAA,OAAO,EAAE,CAACmP,gBAAD,CAHV;AAICF,MAAAA,eAAe,EAAE,CAACE,gBAAD;AAJlB,KAAD;AAF8G,GAAD,CAA/H;AAAA;AAUA;AACA;AACA;;;AAEA,SAASvO,cAAT,EAAyBX,eAAzB,EAA0CwO,mBAA1C,EAA+Da,+BAA/D,EAAgG/M,8BAAhG,EAAgIyF,OAAhI,EAAyIhB,gBAAzI,EAA2JF,UAA3J,EAAuK5C,YAAvK,EAAqL8H,KAArL,EAA4LhN,uBAA5L,EAAqNY,oBAArN,EAA2O0F,aAA3O,EAA0P6J,gBAA1P,EAA4QI,sBAA5Q,EAAoSpO,YAApS,EAAkTgD,QAAlT,EAA4T+K,yBAA5T,EAAuVR,YAAvV,EAAqW5F,aAArW", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, NgModule, InjectionToken, Injectable, Inject, SecurityContext, Component, HostBinding, HostListener } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { Subject } from 'rxjs';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/platform-browser';\n\nclass ToastContainerDirective {\n    constructor(el) {\n        this.el = el;\n    }\n    getContainerElement() {\n        return this.el.nativeElement;\n    }\n}\nToastContainerDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastContainerDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nToastContainerDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.2\", type: ToastContainerDirective, selector: \"[toastContainer]\", exportAs: [\"toastContainer\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastContainerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[toastContainer]',\n                    exportAs: 'toastContainer',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\nclass ToastContainerModule {\n}\nToastContainerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastContainerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nToastContainerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastContainerModule, declarations: [ToastContainerDirective], exports: [ToastContainerDirective] });\nToastContainerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastContainerModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastContainerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [ToastContainerDirective],\n                    exports: [ToastContainerDirective],\n                }]\n        }] });\n\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal {\n    constructor(component, injector) {\n        this.component = component;\n        this.injector = injector;\n    }\n    /** Attach this portal to a host. */\n    attach(host, newestOnTop) {\n        this._attachedHost = host;\n        return host.attach(this, newestOnTop);\n    }\n    /** Detach this portal from its host */\n    detach() {\n        const host = this._attachedHost;\n        if (host) {\n            this._attachedHost = undefined;\n            return host.detach();\n        }\n    }\n    /** Whether this portal is attached to a host. */\n    get isAttached() {\n        return this._attachedHost != null;\n    }\n    /**\n     * Sets the PortalHost reference without performing `attach()`. This is used directly by\n     * the PortalHost when it is performing an `attach()` or `detach()`.\n     */\n    setAttachedHost(host) {\n        this._attachedHost = host;\n    }\n}\n/**\n * Partial implementation of PortalHost that only deals with attaching a\n * ComponentPortal\n */\nclass BasePortalHost {\n    attach(portal, newestOnTop) {\n        this._attachedPortal = portal;\n        return this.attachComponentPortal(portal, newestOnTop);\n    }\n    detach() {\n        if (this._attachedPortal) {\n            this._attachedPortal.setAttachedHost();\n        }\n        this._attachedPortal = undefined;\n        if (this._disposeFn) {\n            this._disposeFn();\n            this._disposeFn = undefined;\n        }\n    }\n    setDisposeFn(fn) {\n        this._disposeFn = fn;\n    }\n}\n\n/**\n * Everything a toast needs to launch\n */\nclass ToastPackage {\n    constructor(toastId, config, message, title, toastType, toastRef) {\n        this.toastId = toastId;\n        this.config = config;\n        this.message = message;\n        this.title = title;\n        this.toastType = toastType;\n        this.toastRef = toastRef;\n        this._onTap = new Subject();\n        this._onAction = new Subject();\n        this.toastRef.afterClosed().subscribe(() => {\n            this._onAction.complete();\n            this._onTap.complete();\n        });\n    }\n    /** Fired on click */\n    triggerTap() {\n        this._onTap.next();\n        if (this.config.tapToDismiss) {\n            this._onTap.complete();\n        }\n    }\n    onTap() {\n        return this._onTap.asObservable();\n    }\n    /** available for use in custom toast */\n    triggerAction(action) {\n        this._onAction.next(action);\n    }\n    onAction() {\n        return this._onAction.asObservable();\n    }\n}\nconst DefaultNoComponentGlobalConfig = {\n    maxOpened: 0,\n    autoDismiss: false,\n    newestOnTop: true,\n    preventDuplicates: false,\n    countDuplicates: false,\n    resetTimeoutOnDuplicate: false,\n    includeTitleDuplicates: false,\n    iconClasses: {\n        error: 'toast-error',\n        info: 'toast-info',\n        success: 'toast-success',\n        warning: 'toast-warning',\n    },\n    // Individual\n    closeButton: false,\n    disableTimeOut: false,\n    timeOut: 5000,\n    extendedTimeOut: 1000,\n    enableHtml: false,\n    progressBar: false,\n    toastClass: 'ngx-toastr',\n    positionClass: 'toast-top-right',\n    titleClass: 'toast-title',\n    messageClass: 'toast-message',\n    easing: 'ease-in',\n    easeTime: 300,\n    tapToDismiss: true,\n    onActivateTick: false,\n    progressAnimation: 'decreasing',\n    payload: null\n};\nconst TOAST_CONFIG = new InjectionToken('ToastConfig');\n\n/**\n * Reference to a toast opened via the Toastr service.\n */\nclass ToastRef {\n    constructor(_overlayRef) {\n        this._overlayRef = _overlayRef;\n        /** Count of duplicates of this toast */\n        this.duplicatesCount = 0;\n        /** Subject for notifying the user that the toast has finished closing. */\n        this._afterClosed = new Subject();\n        /** triggered when toast is activated */\n        this._activate = new Subject();\n        /** notifies the toast that it should close before the timeout */\n        this._manualClose = new Subject();\n        /** notifies the toast that it should reset the timeouts */\n        this._resetTimeout = new Subject();\n        /** notifies the toast that it should count a duplicate toast */\n        this._countDuplicate = new Subject();\n    }\n    manualClose() {\n        this._manualClose.next();\n        this._manualClose.complete();\n    }\n    manualClosed() {\n        return this._manualClose.asObservable();\n    }\n    timeoutReset() {\n        return this._resetTimeout.asObservable();\n    }\n    countDuplicate() {\n        return this._countDuplicate.asObservable();\n    }\n    /**\n     * Close the toast.\n     */\n    close() {\n        this._overlayRef.detach();\n        this._afterClosed.next();\n        this._manualClose.next();\n        this._afterClosed.complete();\n        this._manualClose.complete();\n        this._activate.complete();\n        this._resetTimeout.complete();\n        this._countDuplicate.complete();\n    }\n    /** Gets an observable that is notified when the toast is finished closing. */\n    afterClosed() {\n        return this._afterClosed.asObservable();\n    }\n    isInactive() {\n        return this._activate.isStopped;\n    }\n    activate() {\n        this._activate.next();\n        this._activate.complete();\n    }\n    /** Gets an observable that is notified when the toast has started opening. */\n    afterActivate() {\n        return this._activate.asObservable();\n    }\n    /** Reset the toast timouts and count duplicates */\n    onDuplicate(resetTimeout, countDuplicate) {\n        if (resetTimeout) {\n            this._resetTimeout.next();\n        }\n        if (countDuplicate) {\n            this._countDuplicate.next(++this.duplicatesCount);\n        }\n    }\n}\n/** Custom injector type specifically for instantiating components with a toast. */\nclass ToastInjector {\n    constructor(_toastPackage, _parentInjector) {\n        this._toastPackage = _toastPackage;\n        this._parentInjector = _parentInjector;\n    }\n    get(token, notFoundValue, flags) {\n        if (token === ToastPackage) {\n            return this._toastPackage;\n        }\n        return this._parentInjector.get(token, notFoundValue, flags);\n    }\n}\n\n/**\n * A PortalHost for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n *\n * This is the only part of the portal core that directly touches the DOM.\n */\nclass DomPortalHost extends BasePortalHost {\n    constructor(_hostDomElement, _componentFactoryResolver, _appRef) {\n        super();\n        this._hostDomElement = _hostDomElement;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n    }\n    /**\n     * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n     * @param portal Portal to be attached\n     */\n    attachComponentPortal(portal, newestOnTop) {\n        const componentFactory = this._componentFactoryResolver.resolveComponentFactory(portal.component);\n        let componentRef;\n        // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n        // for the component (in terms of Angular's component tree, not rendering).\n        // When the ViewContainerRef is missing, we use the factory to create the component directly\n        // and then manually attach the ChangeDetector for that component to the application (which\n        // happens automatically when using a ViewContainer).\n        componentRef = componentFactory.create(portal.injector);\n        // When creating a component outside of a ViewContainer, we need to manually register\n        // its ChangeDetector with the application. This API is unfortunately not yet published\n        // in Angular core. The change detector must also be deregistered when the component\n        // is destroyed to prevent memory leaks.\n        this._appRef.attachView(componentRef.hostView);\n        this.setDisposeFn(() => {\n            this._appRef.detachView(componentRef.hostView);\n            componentRef.destroy();\n        });\n        // At this point the component has been instantiated, so we move it to the location in the DOM\n        // where we want it to be rendered.\n        if (newestOnTop) {\n            this._hostDomElement.insertBefore(this._getComponentRootNode(componentRef), this._hostDomElement.firstChild);\n        }\n        else {\n            this._hostDomElement.appendChild(this._getComponentRootNode(componentRef));\n        }\n        return componentRef;\n    }\n    /** Gets the root HTMLElement for an instantiated component. */\n    _getComponentRootNode(componentRef) {\n        return componentRef.hostView.rootNodes[0];\n    }\n}\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    constructor(_portalHost) {\n        this._portalHost = _portalHost;\n    }\n    attach(portal, newestOnTop = true) {\n        return this._portalHost.attach(portal, newestOnTop);\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns Resolves when the overlay has been detached.\n     */\n    detach() {\n        return this._portalHost.detach();\n    }\n}\n\n/** Container inside which all toasts will render. */\nclass OverlayContainer {\n    constructor(_document) {\n        this._document = _document;\n    }\n    ngOnDestroy() {\n        if (this._containerElement && this._containerElement.parentNode) {\n            this._containerElement.parentNode.removeChild(this._containerElement);\n        }\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time  it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body\n     * and 'aria-live=\"polite\"'\n     */\n    _createContainer() {\n        const container = this._document.createElement('div');\n        container.classList.add('overlay-container');\n        container.setAttribute('aria-live', 'polite');\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n}\nOverlayContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: OverlayContainer, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayContainer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: OverlayContainer, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/* eslint-disable @typescript-eslint/no-non-null-assertion */\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalHost, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    constructor(_overlayContainer, _componentFactoryResolver, _appRef, _document) {\n        this._overlayContainer = _overlayContainer;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n        this._document = _document;\n        // Namespace panes by overlay container\n        this._paneElements = new Map();\n    }\n    /**\n     * Creates an overlay.\n     * @returns A reference to the created overlay.\n     */\n    create(positionClass, overlayContainer) {\n        // get existing pane if possible\n        return this._createOverlayRef(this.getPaneElement(positionClass, overlayContainer));\n    }\n    getPaneElement(positionClass = '', overlayContainer) {\n        if (!this._paneElements.get(overlayContainer)) {\n            this._paneElements.set(overlayContainer, {});\n        }\n        if (!this._paneElements.get(overlayContainer)[positionClass]) {\n            this._paneElements.get(overlayContainer)[positionClass] = this._createPaneElement(positionClass, overlayContainer);\n        }\n        return this._paneElements.get(overlayContainer)[positionClass];\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(positionClass, overlayContainer) {\n        const pane = this._document.createElement('div');\n        pane.id = 'toast-container';\n        pane.classList.add(positionClass);\n        pane.classList.add('toast-container');\n        if (!overlayContainer) {\n            this._overlayContainer.getContainerElement().appendChild(pane);\n        }\n        else {\n            overlayContainer.getContainerElement().appendChild(pane);\n        }\n        return pane;\n    }\n    /**\n     * Create a DomPortalHost into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal host.\n     * @returns A portal host for the given DOM element.\n     */\n    _createPortalHost(pane) {\n        return new DomPortalHost(pane, this._componentFactoryResolver, this._appRef);\n    }\n    /**\n     * Creates an OverlayRef for an overlay in the given DOM element.\n     * @param pane DOM element for the overlay\n     */\n    _createOverlayRef(pane) {\n        return new OverlayRef(this._createPortalHost(pane));\n    }\n}\nOverlay.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: Overlay, deps: [{ token: OverlayContainer }, { token: i0.ComponentFactoryResolver }, { token: i0.ApplicationRef }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlay.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: Overlay, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: OverlayContainer }, { type: i0.ComponentFactoryResolver }, { type: i0.ApplicationRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\nclass ToastrService {\n    constructor(token, overlay, _injector, sanitizer, ngZone) {\n        this.overlay = overlay;\n        this._injector = _injector;\n        this.sanitizer = sanitizer;\n        this.ngZone = ngZone;\n        this.currentlyActive = 0;\n        this.toasts = [];\n        this.index = 0;\n        this.toastrConfig = {\n            ...token.default,\n            ...token.config,\n        };\n        if (token.config.iconClasses) {\n            this.toastrConfig.iconClasses = {\n                ...token.default.iconClasses,\n                ...token.config.iconClasses,\n            };\n        }\n    }\n    /** show toast */\n    show(message, title, override = {}, type = '') {\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show successful toast */\n    success(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.success || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show error toast */\n    error(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.error || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show info toast */\n    info(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.info || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /** show warning toast */\n    warning(message, title, override = {}) {\n        const type = this.toastrConfig.iconClasses.warning || '';\n        return this._preBuildNotification(type, message, title, this.applyConfig(override));\n    }\n    /**\n     * Remove all or a single toast by id\n     */\n    clear(toastId) {\n        // Call every toastRef manualClose function\n        for (const toast of this.toasts) {\n            if (toastId !== undefined) {\n                if (toast.toastId === toastId) {\n                    toast.toastRef.manualClose();\n                    return;\n                }\n            }\n            else {\n                toast.toastRef.manualClose();\n            }\n        }\n    }\n    /**\n     * Remove and destroy a single toast by id\n     */\n    remove(toastId) {\n        const found = this._findToast(toastId);\n        if (!found) {\n            return false;\n        }\n        found.activeToast.toastRef.close();\n        this.toasts.splice(found.index, 1);\n        this.currentlyActive = this.currentlyActive - 1;\n        if (!this.toastrConfig.maxOpened || !this.toasts.length) {\n            return false;\n        }\n        if (this.currentlyActive < this.toastrConfig.maxOpened && this.toasts[this.currentlyActive]) {\n            const p = this.toasts[this.currentlyActive].toastRef;\n            if (!p.isInactive()) {\n                this.currentlyActive = this.currentlyActive + 1;\n                p.activate();\n            }\n        }\n        return true;\n    }\n    /**\n     * Determines if toast message is already shown\n     */\n    findDuplicate(title = '', message = '', resetOnDuplicate, countDuplicates) {\n        const { includeTitleDuplicates } = this.toastrConfig;\n        for (const toast of this.toasts) {\n            const hasDuplicateTitle = includeTitleDuplicates && toast.title === title;\n            if ((!includeTitleDuplicates || hasDuplicateTitle) && toast.message === message) {\n                toast.toastRef.onDuplicate(resetOnDuplicate, countDuplicates);\n                return toast;\n            }\n        }\n        return null;\n    }\n    /** create a clone of global config and apply individual settings */\n    applyConfig(override = {}) {\n        return { ...this.toastrConfig, ...override };\n    }\n    /**\n     * Find toast object by id\n     */\n    _findToast(toastId) {\n        for (let i = 0; i < this.toasts.length; i++) {\n            if (this.toasts[i].toastId === toastId) {\n                return { index: i, activeToast: this.toasts[i] };\n            }\n        }\n        return null;\n    }\n    /**\n     * Determines the need to run inside angular's zone then builds the toast\n     */\n    _preBuildNotification(toastType, message, title, config) {\n        if (config.onActivateTick) {\n            return this.ngZone.run(() => this._buildNotification(toastType, message, title, config));\n        }\n        return this._buildNotification(toastType, message, title, config);\n    }\n    /**\n     * Creates and attaches toast data to component\n     * returns the active toast, or in case preventDuplicates is enabled the original/non-duplicate active toast.\n     */\n    _buildNotification(toastType, message, title, config) {\n        if (!config.toastComponent) {\n            throw new Error('toastComponent required');\n        }\n        // max opened and auto dismiss = true\n        // if timeout = 0 resetting it would result in setting this.hideTime = Date.now(). Hence, we only want to reset timeout if there is\n        // a timeout at all\n        const duplicate = this.findDuplicate(title, message, this.toastrConfig.resetTimeoutOnDuplicate && config.timeOut > 0, this.toastrConfig.countDuplicates);\n        if (((this.toastrConfig.includeTitleDuplicates && title) || message) &&\n            this.toastrConfig.preventDuplicates &&\n            duplicate !== null) {\n            return duplicate;\n        }\n        this.previousToastMessage = message;\n        let keepInactive = false;\n        if (this.toastrConfig.maxOpened && this.currentlyActive >= this.toastrConfig.maxOpened) {\n            keepInactive = true;\n            if (this.toastrConfig.autoDismiss) {\n                this.clear(this.toasts[0].toastId);\n            }\n        }\n        const overlayRef = this.overlay.create(config.positionClass, this.overlayContainer);\n        this.index = this.index + 1;\n        let sanitizedMessage = message;\n        if (message && config.enableHtml) {\n            sanitizedMessage = this.sanitizer.sanitize(SecurityContext.HTML, message);\n        }\n        const toastRef = new ToastRef(overlayRef);\n        const toastPackage = new ToastPackage(this.index, config, sanitizedMessage, title, toastType, toastRef);\n        const toastInjector = new ToastInjector(toastPackage, this._injector);\n        const component = new ComponentPortal(config.toastComponent, toastInjector);\n        const portal = overlayRef.attach(component, this.toastrConfig.newestOnTop);\n        toastRef.componentInstance = portal.instance;\n        const ins = {\n            toastId: this.index,\n            title: title || '',\n            message: message || '',\n            toastRef,\n            onShown: toastRef.afterActivate(),\n            onHidden: toastRef.afterClosed(),\n            onTap: toastPackage.onTap(),\n            onAction: toastPackage.onAction(),\n            portal,\n        };\n        if (!keepInactive) {\n            this.currentlyActive = this.currentlyActive + 1;\n            setTimeout(() => {\n                ins.toastRef.activate();\n            });\n        }\n        this.toasts.push(ins);\n        return ins;\n    }\n}\nToastrService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrService, deps: [{ token: TOAST_CONFIG }, { token: Overlay }, { token: i0.Injector }, { token: i2.DomSanitizer }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nToastrService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TOAST_CONFIG]\n                }] }, { type: Overlay }, { type: i0.Injector }, { type: i2.DomSanitizer }, { type: i0.NgZone }]; } });\n\nclass Toast {\n    constructor(toastrService, toastPackage, ngZone) {\n        this.toastrService = toastrService;\n        this.toastPackage = toastPackage;\n        this.ngZone = ngZone;\n        /** width of progress bar */\n        this.width = -1;\n        /** a combination of toast type and options.toastClass */\n        this.toastClasses = '';\n        /** controls animation */\n        this.state = {\n            value: 'inactive',\n            params: {\n                easeTime: this.toastPackage.config.easeTime,\n                easing: 'ease-in'\n            }\n        };\n        this.message = toastPackage.message;\n        this.title = toastPackage.title;\n        this.options = toastPackage.config;\n        this.originalTimeout = toastPackage.config.timeOut;\n        this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n        this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n            this.activateToast();\n        });\n        this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n            this.remove();\n        });\n        this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n            this.resetTimeout();\n        });\n        this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n            this.duplicatesCount = count;\n        });\n    }\n    /** hides component when waiting to be displayed */\n    get displayStyle() {\n        if (this.state.value === 'inactive') {\n            return 'none';\n        }\n        return;\n    }\n    ngOnDestroy() {\n        this.sub.unsubscribe();\n        this.sub1.unsubscribe();\n        this.sub2.unsubscribe();\n        this.sub3.unsubscribe();\n        clearInterval(this.intervalId);\n        clearTimeout(this.timeout);\n    }\n    /**\n     * activates toast and sets timeout\n     */\n    activateToast() {\n        this.state = { ...this.state, value: 'active' };\n        if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n            this.outsideTimeout(() => this.remove(), this.options.timeOut);\n            this.hideTime = new Date().getTime() + this.options.timeOut;\n            if (this.options.progressBar) {\n                this.outsideInterval(() => this.updateProgress(), 10);\n            }\n        }\n    }\n    /**\n     * updates progress bar width\n     */\n    updateProgress() {\n        if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n            return;\n        }\n        const now = new Date().getTime();\n        const remaining = this.hideTime - now;\n        this.width = (remaining / this.options.timeOut) * 100;\n        if (this.options.progressAnimation === 'increasing') {\n            this.width = 100 - this.width;\n        }\n        if (this.width <= 0) {\n            this.width = 0;\n        }\n        if (this.width >= 100) {\n            this.width = 100;\n        }\n    }\n    resetTimeout() {\n        clearTimeout(this.timeout);\n        clearInterval(this.intervalId);\n        this.state = { ...this.state, value: 'active' };\n        this.outsideTimeout(() => this.remove(), this.originalTimeout);\n        this.options.timeOut = this.originalTimeout;\n        this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n        this.width = -1;\n        if (this.options.progressBar) {\n            this.outsideInterval(() => this.updateProgress(), 10);\n        }\n    }\n    /**\n     * tells toastrService to remove this toast after animation time\n     */\n    remove() {\n        if (this.state.value === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.state = { ...this.state, value: 'removed' };\n        this.outsideTimeout(() => this.toastrService.remove(this.toastPackage.toastId), +this.toastPackage.config.easeTime);\n    }\n    tapToast() {\n        if (this.state.value === 'removed') {\n            return;\n        }\n        this.toastPackage.triggerTap();\n        if (this.options.tapToDismiss) {\n            this.remove();\n        }\n    }\n    stickAround() {\n        if (this.state.value === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.options.timeOut = 0;\n        this.hideTime = 0;\n        // disable progressBar\n        clearInterval(this.intervalId);\n        this.width = 0;\n    }\n    delayedHideToast() {\n        if ((this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut') ||\n            this.options.extendedTimeOut === 0 ||\n            this.state.value === 'removed') {\n            return;\n        }\n        this.outsideTimeout(() => this.remove(), this.options.extendedTimeOut);\n        this.options.timeOut = this.options.extendedTimeOut;\n        this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n        this.width = -1;\n        if (this.options.progressBar) {\n            this.outsideInterval(() => this.updateProgress(), 10);\n        }\n    }\n    outsideTimeout(func, timeout) {\n        if (this.ngZone) {\n            this.ngZone.runOutsideAngular(() => (this.timeout = setTimeout(() => this.runInsideAngular(func), timeout)));\n        }\n        else {\n            this.timeout = setTimeout(() => func(), timeout);\n        }\n    }\n    outsideInterval(func, timeout) {\n        if (this.ngZone) {\n            this.ngZone.runOutsideAngular(() => (this.intervalId = setInterval(() => this.runInsideAngular(func), timeout)));\n        }\n        else {\n            this.intervalId = setInterval(() => func(), timeout);\n        }\n    }\n    runInsideAngular(func) {\n        if (this.ngZone) {\n            this.ngZone.run(() => func());\n        }\n        else {\n            func();\n        }\n    }\n}\nToast.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: Toast, deps: [{ token: ToastrService }, { token: ToastPackage }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\nToast.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.2\", type: Toast, selector: \"[toast-component]\", host: { listeners: { \"click\": \"tapToast()\", \"mouseenter\": \"stickAround()\", \"mouseleave\": \"delayedHideToast()\" }, properties: { \"class\": \"this.toastClasses\", \"@flyInOut\": \"this.state\", \"style.display\": \"this.displayStyle\" } }, ngImport: i0, template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `, isInline: true, directives: [{ type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], animations: [\n        trigger('flyInOut', [\n            state('inactive', style({ opacity: 0 })),\n            state('active', style({ opacity: 1 })),\n            state('removed', style({ opacity: 0 })),\n            transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')),\n            transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))\n        ])\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: Toast, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[toast-component]',\n                    template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `,\n                    animations: [\n                        trigger('flyInOut', [\n                            state('inactive', style({ opacity: 0 })),\n                            state('active', style({ opacity: 1 })),\n                            state('removed', style({ opacity: 0 })),\n                            transition('inactive => active', animate('{{ easeTime }}ms {{ easing }}')),\n                            transition('active => removed', animate('{{ easeTime }}ms {{ easing }}'))\n                        ])\n                    ],\n                    preserveWhitespaces: false\n                }]\n        }], ctorParameters: function () { return [{ type: ToastrService }, { type: ToastPackage }, { type: i0.NgZone }]; }, propDecorators: { toastClasses: [{\n                type: HostBinding,\n                args: ['class']\n            }], state: [{\n                type: HostBinding,\n                args: ['@flyInOut']\n            }], displayStyle: [{\n                type: HostBinding,\n                args: ['style.display']\n            }], tapToast: [{\n                type: HostListener,\n                args: ['click']\n            }], stickAround: [{\n                type: HostListener,\n                args: ['mouseenter']\n            }], delayedHideToast: [{\n                type: HostListener,\n                args: ['mouseleave']\n            }] } });\n\nconst DefaultGlobalConfig = {\n    ...DefaultNoComponentGlobalConfig,\n    toastComponent: Toast,\n};\nclass ToastrModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: ToastrModule,\n            providers: [\n                {\n                    provide: TOAST_CONFIG,\n                    useValue: {\n                        default: DefaultGlobalConfig,\n                        config,\n                    },\n                },\n            ],\n        };\n    }\n}\nToastrModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nToastrModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrModule, declarations: [Toast], imports: [CommonModule], exports: [Toast] });\nToastrModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrModule, imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    declarations: [Toast],\n                    exports: [Toast],\n                    entryComponents: [Toast],\n                }]\n        }] });\nclass ToastrComponentlessModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: ToastrModule,\n            providers: [\n                {\n                    provide: TOAST_CONFIG,\n                    useValue: {\n                        default: DefaultNoComponentGlobalConfig,\n                        config,\n                    },\n                },\n            ],\n        };\n    }\n}\nToastrComponentlessModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrComponentlessModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nToastrComponentlessModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrComponentlessModule, imports: [CommonModule] });\nToastrComponentlessModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrComponentlessModule, imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastrComponentlessModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                }]\n        }] });\n\nclass ToastNoAnimation {\n    constructor(toastrService, toastPackage, appRef) {\n        this.toastrService = toastrService;\n        this.toastPackage = toastPackage;\n        this.appRef = appRef;\n        /** width of progress bar */\n        this.width = -1;\n        /** a combination of toast type and options.toastClass */\n        this.toastClasses = '';\n        /** controls animation */\n        this.state = 'inactive';\n        this.message = toastPackage.message;\n        this.title = toastPackage.title;\n        this.options = toastPackage.config;\n        this.originalTimeout = toastPackage.config.timeOut;\n        this.toastClasses = `${toastPackage.toastType} ${toastPackage.config.toastClass}`;\n        this.sub = toastPackage.toastRef.afterActivate().subscribe(() => {\n            this.activateToast();\n        });\n        this.sub1 = toastPackage.toastRef.manualClosed().subscribe(() => {\n            this.remove();\n        });\n        this.sub2 = toastPackage.toastRef.timeoutReset().subscribe(() => {\n            this.resetTimeout();\n        });\n        this.sub3 = toastPackage.toastRef.countDuplicate().subscribe(count => {\n            this.duplicatesCount = count;\n        });\n    }\n    /** hides component when waiting to be displayed */\n    get displayStyle() {\n        if (this.state === 'inactive') {\n            return 'none';\n        }\n    }\n    ngOnDestroy() {\n        this.sub.unsubscribe();\n        this.sub1.unsubscribe();\n        this.sub2.unsubscribe();\n        this.sub3.unsubscribe();\n        clearInterval(this.intervalId);\n        clearTimeout(this.timeout);\n    }\n    /**\n     * activates toast and sets timeout\n     */\n    activateToast() {\n        this.state = 'active';\n        if (!(this.options.disableTimeOut === true || this.options.disableTimeOut === 'timeOut') && this.options.timeOut) {\n            this.timeout = setTimeout(() => {\n                this.remove();\n            }, this.options.timeOut);\n            this.hideTime = new Date().getTime() + this.options.timeOut;\n            if (this.options.progressBar) {\n                this.intervalId = setInterval(() => this.updateProgress(), 10);\n            }\n        }\n        if (this.options.onActivateTick) {\n            this.appRef.tick();\n        }\n    }\n    /**\n     * updates progress bar width\n     */\n    updateProgress() {\n        if (this.width === 0 || this.width === 100 || !this.options.timeOut) {\n            return;\n        }\n        const now = new Date().getTime();\n        const remaining = this.hideTime - now;\n        this.width = (remaining / this.options.timeOut) * 100;\n        if (this.options.progressAnimation === 'increasing') {\n            this.width = 100 - this.width;\n        }\n        if (this.width <= 0) {\n            this.width = 0;\n        }\n        if (this.width >= 100) {\n            this.width = 100;\n        }\n    }\n    resetTimeout() {\n        clearTimeout(this.timeout);\n        clearInterval(this.intervalId);\n        this.state = 'active';\n        this.options.timeOut = this.originalTimeout;\n        this.timeout = setTimeout(() => this.remove(), this.originalTimeout);\n        this.hideTime = new Date().getTime() + (this.originalTimeout || 0);\n        this.width = -1;\n        if (this.options.progressBar) {\n            this.intervalId = setInterval(() => this.updateProgress(), 10);\n        }\n    }\n    /**\n     * tells toastrService to remove this toast after animation time\n     */\n    remove() {\n        if (this.state === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.state = 'removed';\n        this.timeout = setTimeout(() => this.toastrService.remove(this.toastPackage.toastId));\n    }\n    tapToast() {\n        if (this.state === 'removed') {\n            return;\n        }\n        this.toastPackage.triggerTap();\n        if (this.options.tapToDismiss) {\n            this.remove();\n        }\n    }\n    stickAround() {\n        if (this.state === 'removed') {\n            return;\n        }\n        clearTimeout(this.timeout);\n        this.options.timeOut = 0;\n        this.hideTime = 0;\n        // disable progressBar\n        clearInterval(this.intervalId);\n        this.width = 0;\n    }\n    delayedHideToast() {\n        if ((this.options.disableTimeOut === true || this.options.disableTimeOut === 'extendedTimeOut') ||\n            this.options.extendedTimeOut === 0 ||\n            this.state === 'removed') {\n            return;\n        }\n        this.timeout = setTimeout(() => this.remove(), this.options.extendedTimeOut);\n        this.options.timeOut = this.options.extendedTimeOut;\n        this.hideTime = new Date().getTime() + (this.options.timeOut || 0);\n        this.width = -1;\n        if (this.options.progressBar) {\n            this.intervalId = setInterval(() => this.updateProgress(), 10);\n        }\n    }\n}\nToastNoAnimation.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastNoAnimation, deps: [{ token: ToastrService }, { token: ToastPackage }, { token: i0.ApplicationRef }], target: i0.ɵɵFactoryTarget.Component });\nToastNoAnimation.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.2\", type: ToastNoAnimation, selector: \"[toast-component]\", host: { listeners: { \"click\": \"tapToast()\", \"mouseenter\": \"stickAround()\", \"mouseleave\": \"delayedHideToast()\" }, properties: { \"class\": \"this.toastClasses\", \"style.display\": \"this.displayStyle\" } }, ngImport: i0, template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `, isInline: true, directives: [{ type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastNoAnimation, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[toast-component]',\n                    template: `\n  <button *ngIf=\"options.closeButton\" (click)=\"remove()\" type=\"button\" class=\"toast-close-button\" aria-label=\"Close\">\n    <span aria-hidden=\"true\">&times;</span>\n  </button>\n  <div *ngIf=\"title\" [class]=\"options.titleClass\" [attr.aria-label]=\"title\">\n    {{ title }} <ng-container *ngIf=\"duplicatesCount\">[{{ duplicatesCount + 1 }}]</ng-container>\n  </div>\n  <div *ngIf=\"message && options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [innerHTML]=\"message\">\n  </div>\n  <div *ngIf=\"message && !options.enableHtml\" role=\"alert\"\n    [class]=\"options.messageClass\" [attr.aria-label]=\"message\">\n    {{ message }}\n  </div>\n  <div *ngIf=\"options.progressBar\">\n    <div class=\"toast-progress\" [style.width]=\"width + '%'\"></div>\n  </div>\n  `,\n                }]\n        }], ctorParameters: function () { return [{ type: ToastrService }, { type: ToastPackage }, { type: i0.ApplicationRef }]; }, propDecorators: { toastClasses: [{\n                type: HostBinding,\n                args: ['class']\n            }], displayStyle: [{\n                type: HostBinding,\n                args: ['style.display']\n            }], tapToast: [{\n                type: HostListener,\n                args: ['click']\n            }], stickAround: [{\n                type: HostListener,\n                args: ['mouseenter']\n            }], delayedHideToast: [{\n                type: HostListener,\n                args: ['mouseleave']\n            }] } });\nconst DefaultNoAnimationsGlobalConfig = {\n    ...DefaultNoComponentGlobalConfig,\n    toastComponent: ToastNoAnimation,\n};\nclass ToastNoAnimationModule {\n    static forRoot(config = {}) {\n        return {\n            ngModule: ToastNoAnimationModule,\n            providers: [\n                {\n                    provide: TOAST_CONFIG,\n                    useValue: {\n                        default: DefaultNoAnimationsGlobalConfig,\n                        config,\n                    },\n                },\n            ],\n        };\n    }\n}\nToastNoAnimationModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastNoAnimationModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nToastNoAnimationModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastNoAnimationModule, declarations: [ToastNoAnimation], imports: [CommonModule], exports: [ToastNoAnimation] });\nToastNoAnimationModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastNoAnimationModule, imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.2\", ngImport: i0, type: ToastNoAnimationModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    declarations: [ToastNoAnimation],\n                    exports: [ToastNoAnimation],\n                    entryComponents: [ToastNoAnimation],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, ComponentPortal, DefaultGlobalConfig, DefaultNoAnimationsGlobalConfig, DefaultNoComponentGlobalConfig, Overlay, OverlayContainer, OverlayRef, TOAST_CONFIG, Toast, ToastContainerDirective, ToastContainerModule, ToastInjector, ToastNoAnimation, ToastNoAnimationModule, ToastPackage, ToastRef, ToastrComponentlessModule, ToastrModule, ToastrService };\n"]}, "metadata": {}, "sourceType": "module"}
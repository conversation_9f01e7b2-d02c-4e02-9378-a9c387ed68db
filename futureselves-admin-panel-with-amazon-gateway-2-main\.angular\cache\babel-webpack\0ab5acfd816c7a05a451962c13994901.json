{"ast": null, "code": "import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** The injection token used to specify the virtual scrolling strategy. */\n\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\n\nclass FixedSizeVirtualScrollStrategy {\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize, minBufferPx, maxBufferPx) {\n    this._scrolledIndexChange = new Subject();\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n    this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n    /** The attached viewport. */\n\n    this._viewport = null;\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n\n\n  attach(viewport) {\n    this._viewport = viewport;\n\n    this._updateTotalContentSize();\n\n    this._updateRenderedRange();\n  }\n  /** Detaches this scroll strategy from the currently attached viewport. */\n\n\n  detach() {\n    this._scrolledIndexChange.complete();\n\n    this._viewport = null;\n  }\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n\n\n  updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n\n    this._updateTotalContentSize();\n\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onContentRendered() {\n    /* no-op */\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n\n\n  scrollToIndex(index, behavior) {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n  /** Update the viewport's total content size. */\n\n\n  _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n  /** Update the viewport's rendered range. */\n\n\n  _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n\n    const renderedRange = this._viewport.getRenderedRange();\n\n    const newRange = {\n      start: renderedRange.start,\n      end: renderedRange.end\n    };\n\n    const viewportSize = this._viewport.getViewportSize();\n\n    const dataLength = this._viewport.getDataLength();\n\n    let scrollOffset = this._viewport.measureScrollOffset(); // Prevent NaN as result when dividing by zero.\n\n\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0; // If user scrolls to the bottom of the list and data changes to a smaller list\n\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems)); // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n        }\n      }\n    }\n\n    this._viewport.setRenderedRange(newRange);\n\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\n\n\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n  return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\n\n\nclass CdkFixedSizeVirtualScroll {\n  constructor() {\n    this._itemSize = 20;\n    this._minBufferPx = 100;\n    this._maxBufferPx = 200;\n    /** The scroll strategy used by this directive. */\n\n    this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n  /** The size of the items in the list (in pixels). */\n\n\n  get itemSize() {\n    return this._itemSize;\n  }\n\n  set itemSize(value) {\n    this._itemSize = coerceNumberProperty(value);\n  }\n  /**\n   * The minimum amount of buffer rendered beyond the viewport (in pixels).\n   * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n   */\n\n\n  get minBufferPx() {\n    return this._minBufferPx;\n  }\n\n  set minBufferPx(value) {\n    this._minBufferPx = coerceNumberProperty(value);\n  }\n  /**\n   * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n   */\n\n\n  get maxBufferPx() {\n    return this._maxBufferPx;\n  }\n\n  set maxBufferPx(value) {\n    this._maxBufferPx = coerceNumberProperty(value);\n  }\n\n  ngOnChanges() {\n    this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n\n}\n\nCdkFixedSizeVirtualScroll.ɵfac = function CdkFixedSizeVirtualScroll_Factory(t) {\n  return new (t || CdkFixedSizeVirtualScroll)();\n};\n\nCdkFixedSizeVirtualScroll.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkFixedSizeVirtualScroll,\n  selectors: [[\"cdk-virtual-scroll-viewport\", \"itemSize\", \"\"]],\n  inputs: {\n    itemSize: \"itemSize\",\n    minBufferPx: \"minBufferPx\",\n    maxBufferPx: \"maxBufferPx\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: VIRTUAL_SCROLL_STRATEGY,\n    useFactory: _fixedSizeVirtualScrollStrategyFactory,\n    deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n  }]), i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFixedSizeVirtualScroll, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[itemSize]',\n      providers: [{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _fixedSizeVirtualScrollStrategyFactory,\n        deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n      }]\n    }]\n  }], null, {\n    itemSize: [{\n      type: Input\n    }],\n    minBufferPx: [{\n      type: Input\n    }],\n    maxBufferPx: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Time in ms to throttle the scrolling events by default. */\n\n\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\n\nclass ScrollDispatcher {\n  constructor(_ngZone, _platform, document) {\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n\n    this._scrolled = new Subject();\n    /** Keeps track of the global `scroll` and `resize` subscriptions. */\n\n    this._globalSubscription = null;\n    /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n\n    this._scrolledCount = 0;\n    /**\n     * Map of all the scrollable references that are registered with the service and their\n     * scroll event subscriptions.\n     */\n\n    this.scrollContainers = new Map();\n    this._document = document;\n  }\n  /**\n   * Registers a scrollable instance with the service and listens for its scrolled events. When the\n   * scrollable is scrolled, the service emits the event to its scrolled observable.\n   * @param scrollable Scrollable instance to be registered.\n   */\n\n\n  register(scrollable) {\n    if (!this.scrollContainers.has(scrollable)) {\n      this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n    }\n  }\n  /**\n   * Deregisters a Scrollable reference and unsubscribes from its scroll event observable.\n   * @param scrollable Scrollable instance to be deregistered.\n   */\n\n\n  deregister(scrollable) {\n    const scrollableReference = this.scrollContainers.get(scrollable);\n\n    if (scrollableReference) {\n      scrollableReference.unsubscribe();\n      this.scrollContainers.delete(scrollable);\n    }\n  }\n  /**\n   * Returns an observable that emits an event whenever any of the registered Scrollable\n   * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n   * to override the default \"throttle\" time.\n   *\n   * **Note:** in order to avoid hitting change detection for every scroll event,\n   * all of the events emitted from this stream will be run outside the Angular zone.\n   * If you need to update any data bindings as a result of a scroll event, you have\n   * to run the callback using `NgZone.run`.\n   */\n\n\n  scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n    if (!this._platform.isBrowser) {\n      return of();\n    }\n\n    return new Observable(observer => {\n      if (!this._globalSubscription) {\n        this._addGlobalListener();\n      } // In the case of a 0ms delay, use an observable without auditTime\n      // since it does add a perceptible delay in processing overhead.\n\n\n      const subscription = auditTimeInMs > 0 ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) : this._scrolled.subscribe(observer);\n      this._scrolledCount++;\n      return () => {\n        subscription.unsubscribe();\n        this._scrolledCount--;\n\n        if (!this._scrolledCount) {\n          this._removeGlobalListener();\n        }\n      };\n    });\n  }\n\n  ngOnDestroy() {\n    this._removeGlobalListener();\n\n    this.scrollContainers.forEach((_, container) => this.deregister(container));\n\n    this._scrolled.complete();\n  }\n  /**\n   * Returns an observable that emits whenever any of the\n   * scrollable ancestors of an element are scrolled.\n   * @param elementOrElementRef Element whose ancestors to listen for.\n   * @param auditTimeInMs Time to throttle the scroll events.\n   */\n\n\n  ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n    const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n    return this.scrolled(auditTimeInMs).pipe(filter(target => {\n      return !target || ancestors.indexOf(target) > -1;\n    }));\n  }\n  /** Returns all registered Scrollables that contain the provided element. */\n\n\n  getAncestorScrollContainers(elementOrElementRef) {\n    const scrollingContainers = [];\n    this.scrollContainers.forEach((_subscription, scrollable) => {\n      if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n        scrollingContainers.push(scrollable);\n      }\n    });\n    return scrollingContainers;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Returns true if the element is contained within the provided Scrollable. */\n\n\n  _scrollableContainsElement(scrollable, elementOrElementRef) {\n    let element = coerceElement(elementOrElementRef);\n    let scrollableElement = scrollable.getElementRef().nativeElement; // Traverse through the element parents until we reach null, checking if any of the elements\n    // are the scrollable's element.\n\n    do {\n      if (element == scrollableElement) {\n        return true;\n      }\n    } while (element = element.parentElement);\n\n    return false;\n  }\n  /** Sets up the global scroll listeners. */\n\n\n  _addGlobalListener() {\n    this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n      const window = this._getWindow();\n\n      return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n    });\n  }\n  /** Cleans up the global scroll listener. */\n\n\n  _removeGlobalListener() {\n    if (this._globalSubscription) {\n      this._globalSubscription.unsubscribe();\n\n      this._globalSubscription = null;\n    }\n  }\n\n}\n\nScrollDispatcher.ɵfac = function ScrollDispatcher_Factory(t) {\n  return new (t || ScrollDispatcher)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT, 8));\n};\n\nScrollDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ScrollDispatcher,\n  factory: ScrollDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\n\n\nclass CdkScrollable {\n  constructor(elementRef, scrollDispatcher, ngZone, dir) {\n    this.elementRef = elementRef;\n    this.scrollDispatcher = scrollDispatcher;\n    this.ngZone = ngZone;\n    this.dir = dir;\n    this._destroyed = new Subject();\n    this._elementScrolled = new Observable(observer => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n  }\n\n  ngOnInit() {\n    this.scrollDispatcher.register(this);\n  }\n\n  ngOnDestroy() {\n    this.scrollDispatcher.deregister(this);\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n  }\n  /** Returns observable that emits when a scroll event is fired on the host element. */\n\n\n  elementScrolled() {\n    return this._elementScrolled;\n  }\n  /** Gets the ElementRef for the viewport. */\n\n\n  getElementRef() {\n    return this.elementRef;\n  }\n  /**\n   * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n   * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param options specified the offsets to scroll to.\n   */\n\n\n  scrollTo(options) {\n    const el = this.elementRef.nativeElement;\n    const isRtl = this.dir && this.dir.value == 'rtl'; // Rewrite start & end offsets as right or left offsets.\n\n    if (options.left == null) {\n      options.left = isRtl ? options.end : options.start;\n    }\n\n    if (options.right == null) {\n      options.right = isRtl ? options.start : options.end;\n    } // Rewrite the bottom offset as a top offset.\n\n\n    if (options.bottom != null) {\n      options.top = el.scrollHeight - el.clientHeight - options.bottom;\n    } // Rewrite the right offset as a left offset.\n\n\n    if (isRtl && getRtlScrollAxisType() != 0\n    /* NORMAL */\n    ) {\n      if (options.left != null) {\n        options.right = el.scrollWidth - el.clientWidth - options.left;\n      }\n\n      if (getRtlScrollAxisType() == 2\n      /* INVERTED */\n      ) {\n        options.left = options.right;\n      } else if (getRtlScrollAxisType() == 1\n      /* NEGATED */\n      ) {\n        options.left = options.right ? -options.right : options.right;\n      }\n    } else {\n      if (options.right != null) {\n        options.left = el.scrollWidth - el.clientWidth - options.right;\n      }\n    }\n\n    this._applyScrollToOptions(options);\n  }\n\n  _applyScrollToOptions(options) {\n    const el = this.elementRef.nativeElement;\n\n    if (supportsScrollBehavior()) {\n      el.scrollTo(options);\n    } else {\n      if (options.top != null) {\n        el.scrollTop = options.top;\n      }\n\n      if (options.left != null) {\n        el.scrollLeft = options.left;\n      }\n    }\n  }\n  /**\n   * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n   * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n   * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param from The edge to measure from.\n   */\n\n\n  measureScrollOffset(from) {\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const el = this.elementRef.nativeElement;\n\n    if (from == 'top') {\n      return el.scrollTop;\n    }\n\n    if (from == 'bottom') {\n      return el.scrollHeight - el.clientHeight - el.scrollTop;\n    } // Rewrite start & end as left or right offsets.\n\n\n    const isRtl = this.dir && this.dir.value == 'rtl';\n\n    if (from == 'start') {\n      from = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      from = isRtl ? LEFT : RIGHT;\n    }\n\n    if (isRtl && getRtlScrollAxisType() == 2\n    /* INVERTED */\n    ) {\n      // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      } else {\n        return el.scrollLeft;\n      }\n    } else if (isRtl && getRtlScrollAxisType() == 1\n    /* NEGATED */\n    ) {\n      // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft + el.scrollWidth - el.clientWidth;\n      } else {\n        return -el.scrollLeft;\n      }\n    } else {\n      // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n      // (scrollWidth - clientWidth) when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft;\n      } else {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      }\n    }\n  }\n\n}\n\nCdkScrollable.ɵfac = function CdkScrollable_Factory(t) {\n  return new (t || CdkScrollable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n};\n\nCdkScrollable.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkScrollable,\n  selectors: [[\"\", \"cdk-scrollable\", \"\"], [\"\", \"cdkScrollable\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollable, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-scrollable], [cdkScrollable]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Time in ms to throttle the resize events by default. */\n\n\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\n\nclass ViewportRuler {\n  constructor(_platform, ngZone, document) {\n    this._platform = _platform;\n    /** Stream of viewport change events. */\n\n    this._change = new Subject();\n    /** Event listener that will be used to handle the viewport change events. */\n\n    this._changeListener = event => {\n      this._change.next(event);\n    };\n\n    this._document = document;\n    ngZone.runOutsideAngular(() => {\n      if (_platform.isBrowser) {\n        const window = this._getWindow(); // Note that bind the events ourselves, rather than going through something like RxJS's\n        // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n\n\n        window.addEventListener('resize', this._changeListener);\n        window.addEventListener('orientationchange', this._changeListener);\n      } // Clear the cached position so that the viewport is re-measured next time it is required.\n      // We don't need to keep track of the subscription, because it is completed on destroy.\n\n\n      this.change().subscribe(() => this._viewportSize = null);\n    });\n  }\n\n  ngOnDestroy() {\n    if (this._platform.isBrowser) {\n      const window = this._getWindow();\n\n      window.removeEventListener('resize', this._changeListener);\n      window.removeEventListener('orientationchange', this._changeListener);\n    }\n\n    this._change.complete();\n  }\n  /** Returns the viewport's width and height. */\n\n\n  getViewportSize() {\n    if (!this._viewportSize) {\n      this._updateViewportSize();\n    }\n\n    const output = {\n      width: this._viewportSize.width,\n      height: this._viewportSize.height\n    }; // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n\n    if (!this._platform.isBrowser) {\n      this._viewportSize = null;\n    }\n\n    return output;\n  }\n  /** Gets a ClientRect for the viewport's bounds. */\n\n\n  getViewportRect() {\n    // Use the document element's bounding rect rather than the window scroll properties\n    // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n    // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n    // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n    // can disagree when the page is pinch-zoomed (on devices that support touch).\n    // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n    // We use the documentElement instead of the body because, by default (without a css reset)\n    // browsers typically give the document body an 8px margin, which is not included in\n    // getBoundingClientRect().\n    const scrollPosition = this.getViewportScrollPosition();\n    const {\n      width,\n      height\n    } = this.getViewportSize();\n    return {\n      top: scrollPosition.top,\n      left: scrollPosition.left,\n      bottom: scrollPosition.top + height,\n      right: scrollPosition.left + width,\n      height,\n      width\n    };\n  }\n  /** Gets the (top, left) scroll position of the viewport. */\n\n\n  getViewportScrollPosition() {\n    // While we can get a reference to the fake document\n    // during SSR, it doesn't have getBoundingClientRect.\n    if (!this._platform.isBrowser) {\n      return {\n        top: 0,\n        left: 0\n      };\n    } // The top-left-corner of the viewport is determined by the scroll position of the document\n    // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n    // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n    // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n    // `document.documentElement` works consistently, where the `top` and `left` values will\n    // equal negative the scroll position.\n\n\n    const document = this._document;\n\n    const window = this._getWindow();\n\n    const documentElement = document.documentElement;\n    const documentRect = documentElement.getBoundingClientRect();\n    const top = -documentRect.top || document.body.scrollTop || window.scrollY || documentElement.scrollTop || 0;\n    const left = -documentRect.left || document.body.scrollLeft || window.scrollX || documentElement.scrollLeft || 0;\n    return {\n      top,\n      left\n    };\n  }\n  /**\n   * Returns a stream that emits whenever the size of the viewport changes.\n   * This stream emits outside of the Angular zone.\n   * @param throttleTime Time in milliseconds to throttle the stream.\n   */\n\n\n  change(throttleTime = DEFAULT_RESIZE_TIME) {\n    return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Updates the cached viewport size. */\n\n\n  _updateViewportSize() {\n    const window = this._getWindow();\n\n    this._viewportSize = this._platform.isBrowser ? {\n      width: window.innerWidth,\n      height: window.innerHeight\n    } : {\n      width: 0,\n      height: 0\n    };\n  }\n\n}\n\nViewportRuler.ɵfac = function ViewportRuler_Factory(t) {\n  return new (t || ViewportRuler)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT, 8));\n};\n\nViewportRuler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ViewportRuler,\n  factory: ViewportRuler.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ViewportRuler, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Checks if the given ranges are equal. */\n\n\nfunction rangesEqual(r1, r2) {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\n\n\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\n\nclass CdkVirtualScrollViewport extends CdkScrollable {\n  constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler) {\n    super(elementRef, scrollDispatcher, ngZone, dir);\n    this.elementRef = elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._scrollStrategy = _scrollStrategy;\n    /** Emits when the viewport is detached from a CdkVirtualForOf. */\n\n    this._detachedSubject = new Subject();\n    /** Emits when the rendered range changes. */\n\n    this._renderedRangeSubject = new Subject();\n    this._orientation = 'vertical';\n    this._appendOnly = false; // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n    // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n    // depending on how the strategy calculates the scrolled index, it may come at a cost to\n    // performance.\n\n    /** Emits when the index of the first element visible in the viewport changes. */\n\n    this.scrolledIndexChange = new Observable(observer => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n    /** A stream that emits whenever the rendered range changes. */\n\n    this.renderedRangeStream = this._renderedRangeSubject;\n    /**\n     * The total size of all content (in pixels), including content that is not currently rendered.\n     */\n\n    this._totalContentSize = 0;\n    /** A string representing the `style.width` property value to be used for the spacer element. */\n\n    this._totalContentWidth = '';\n    /** A string representing the `style.height` property value to be used for the spacer element. */\n\n    this._totalContentHeight = '';\n    /** The currently rendered range of indices. */\n\n    this._renderedRange = {\n      start: 0,\n      end: 0\n    };\n    /** The length of the data bound to this viewport (in number of items). */\n\n    this._dataLength = 0;\n    /** The size of the viewport (in pixels). */\n\n    this._viewportSize = 0;\n    /** The last rendered content offset that was set. */\n\n    this._renderedContentOffset = 0;\n    /**\n     * Whether the last rendered content offset was to the end of the content (and therefore needs to\n     * be rewritten as an offset to the start of the content).\n     */\n\n    this._renderedContentOffsetNeedsRewrite = false;\n    /** Whether there is a pending change detection cycle. */\n\n    this._isChangeDetectionPending = false;\n    /** A list of functions to run after the next change detection cycle. */\n\n    this._runAfterChangeDetection = [];\n    /** Subscription to changes in the viewport size. */\n\n    this._viewportChanges = Subscription.EMPTY;\n\n    if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n    }\n\n    this._viewportChanges = viewportRuler.change().subscribe(() => {\n      this.checkViewportSize();\n    });\n  }\n  /** The direction the viewport scrolls. */\n\n\n  get orientation() {\n    return this._orientation;\n  }\n\n  set orientation(orientation) {\n    if (this._orientation !== orientation) {\n      this._orientation = orientation;\n\n      this._calculateSpacerSize();\n    }\n  }\n  /**\n   * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n   * will be removed.\n   */\n\n\n  get appendOnly() {\n    return this._appendOnly;\n  }\n\n  set appendOnly(value) {\n    this._appendOnly = coerceBooleanProperty(value);\n  }\n\n  ngOnInit() {\n    super.ngOnInit(); // It's still too early to measure the viewport at this point. Deferring with a promise allows\n    // the Viewport to be rendered with the correct size before we measure. We run this outside the\n    // zone to avoid causing more change detection cycles. We handle the change detection loop\n    // ourselves instead.\n\n    this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n      this._measureViewportSize();\n\n      this._scrollStrategy.attach(this);\n\n      this.elementScrolled().pipe( // Start off with a fake scroll event so we properly detect our initial position.\n      startWith(null), // Collect multiple events into one until the next animation frame. This way if\n      // there are multiple scroll events in the same frame we only need to recheck\n      // our layout once.\n      auditTime(0, SCROLL_SCHEDULER)).subscribe(() => this._scrollStrategy.onContentScrolled());\n\n      this._markChangeDetectionNeeded();\n    }));\n  }\n\n  ngOnDestroy() {\n    this.detach();\n\n    this._scrollStrategy.detach(); // Complete all subjects\n\n\n    this._renderedRangeSubject.complete();\n\n    this._detachedSubject.complete();\n\n    this._viewportChanges.unsubscribe();\n\n    super.ngOnDestroy();\n  }\n  /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n\n\n  attach(forOf) {\n    if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CdkVirtualScrollViewport is already attached.');\n    } // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n    // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n    // change detection loop ourselves.\n\n\n    this.ngZone.runOutsideAngular(() => {\n      this._forOf = forOf;\n\n      this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n        const newLength = data.length;\n\n        if (newLength !== this._dataLength) {\n          this._dataLength = newLength;\n\n          this._scrollStrategy.onDataLengthChanged();\n        }\n\n        this._doChangeDetection();\n      });\n    });\n  }\n  /** Detaches the current `CdkVirtualForOf`. */\n\n\n  detach() {\n    this._forOf = null;\n\n    this._detachedSubject.next();\n  }\n  /** Gets the length of the data bound to this viewport (in number of items). */\n\n\n  getDataLength() {\n    return this._dataLength;\n  }\n  /** Gets the size of the viewport (in pixels). */\n\n\n  getViewportSize() {\n    return this._viewportSize;\n  } // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n  // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n  // setting it to something else, but its error prone and should probably be split into\n  // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n\n  /** Get the current rendered range of items. */\n\n\n  getRenderedRange() {\n    return this._renderedRange;\n  }\n  /**\n   * Sets the total size of all content (in pixels), including content that is not currently\n   * rendered.\n   */\n\n\n  setTotalContentSize(size) {\n    if (this._totalContentSize !== size) {\n      this._totalContentSize = size;\n\n      this._calculateSpacerSize();\n\n      this._markChangeDetectionNeeded();\n    }\n  }\n  /** Sets the currently rendered range of indices. */\n\n\n  setRenderedRange(range) {\n    if (!rangesEqual(this._renderedRange, range)) {\n      if (this.appendOnly) {\n        range = {\n          start: 0,\n          end: Math.max(this._renderedRange.end, range.end)\n        };\n      }\n\n      this._renderedRangeSubject.next(this._renderedRange = range);\n\n      this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n    }\n  }\n  /**\n   * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n   */\n\n\n  getOffsetToRenderedContentStart() {\n    return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n  }\n  /**\n   * Sets the offset from the start of the viewport to either the start or end of the rendered data\n   * (in pixels).\n   */\n\n\n  setRenderedContentOffset(offset, to = 'to-start') {\n    // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n    // in the negative direction.\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    const isHorizontal = this.orientation == 'horizontal';\n    const axis = isHorizontal ? 'X' : 'Y';\n    const axisDirection = isHorizontal && isRtl ? -1 : 1;\n    let transform = `translate${axis}(${Number(axisDirection * offset)}px)`; // in appendOnly, we always start from the top\n\n    offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n    this._renderedContentOffset = offset;\n\n    if (to === 'to-end') {\n      transform += ` translate${axis}(-100%)`; // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n      // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n      // expand upward).\n\n      this._renderedContentOffsetNeedsRewrite = true;\n    }\n\n    if (this._renderedContentTransform != transform) {\n      // We know this value is safe because we parse `offset` with `Number()` before passing it\n      // into the string.\n      this._renderedContentTransform = transform;\n\n      this._markChangeDetectionNeeded(() => {\n        if (this._renderedContentOffsetNeedsRewrite) {\n          this._renderedContentOffset -= this.measureRenderedContentSize();\n          this._renderedContentOffsetNeedsRewrite = false;\n          this.setRenderedContentOffset(this._renderedContentOffset);\n        } else {\n          this._scrollStrategy.onRenderedOffsetChanged();\n        }\n      });\n    }\n  }\n  /**\n   * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n   * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n   * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n   * @param offset The offset to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n\n\n  scrollToOffset(offset, behavior = 'auto') {\n    const options = {\n      behavior\n    };\n\n    if (this.orientation === 'horizontal') {\n      options.start = offset;\n    } else {\n      options.top = offset;\n    }\n\n    this.scrollTo(options);\n  }\n  /**\n   * Scrolls to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n\n\n  scrollToIndex(index, behavior = 'auto') {\n    this._scrollStrategy.scrollToIndex(index, behavior);\n  }\n  /**\n   * Gets the current scroll offset from the start of the viewport (in pixels).\n   * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n   *     in horizontal mode.\n   */\n\n\n  measureScrollOffset(from) {\n    return from ? super.measureScrollOffset(from) : super.measureScrollOffset(this.orientation === 'horizontal' ? 'start' : 'top');\n  }\n  /** Measure the combined size of all of the rendered items. */\n\n\n  measureRenderedContentSize() {\n    const contentEl = this._contentWrapper.nativeElement;\n    return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n  }\n  /**\n   * Measure the total combined size of the given range. Throws if the range includes items that are\n   * not rendered.\n   */\n\n\n  measureRangeSize(range) {\n    if (!this._forOf) {\n      return 0;\n    }\n\n    return this._forOf.measureRangeSize(range, this.orientation);\n  }\n  /** Update the viewport dimensions and re-render. */\n\n\n  checkViewportSize() {\n    // TODO: Cleanup later when add logic for handling content resize\n    this._measureViewportSize();\n\n    this._scrollStrategy.onDataLengthChanged();\n  }\n  /** Measure the viewport size. */\n\n\n  _measureViewportSize() {\n    const viewportEl = this.elementRef.nativeElement;\n    this._viewportSize = this.orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n  }\n  /** Queue up change detection to run. */\n\n\n  _markChangeDetectionNeeded(runAfter) {\n    if (runAfter) {\n      this._runAfterChangeDetection.push(runAfter);\n    } // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n    // properties sequentially we only have to run `_doChangeDetection` once at the end.\n\n\n    if (!this._isChangeDetectionPending) {\n      this._isChangeDetectionPending = true;\n      this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n        this._doChangeDetection();\n      }));\n    }\n  }\n  /** Run change detection. */\n\n\n  _doChangeDetection() {\n    this._isChangeDetectionPending = false; // Apply the content transform. The transform can't be set via an Angular binding because\n    // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n    // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n    // the `Number` function first to coerce it to a numeric value.\n\n    this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform; // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n    // from the root, since the repeated items are content projected in. Calling `detectChanges`\n    // instead does not properly check the projected content.\n\n    this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n    const runAfterChangeDetection = this._runAfterChangeDetection;\n    this._runAfterChangeDetection = [];\n\n    for (const fn of runAfterChangeDetection) {\n      fn();\n    }\n  }\n  /** Calculates the `style.width` and `style.height` for the spacer element. */\n\n\n  _calculateSpacerSize() {\n    this._totalContentHeight = this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n    this._totalContentWidth = this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n  }\n\n}\n\nCdkVirtualScrollViewport.ɵfac = function CdkVirtualScrollViewport_Factory(t) {\n  return new (t || CdkVirtualScrollViewport)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(VIRTUAL_SCROLL_STRATEGY, 8), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(ViewportRuler));\n};\n\nCdkVirtualScrollViewport.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CdkVirtualScrollViewport,\n  selectors: [[\"cdk-virtual-scroll-viewport\"]],\n  viewQuery: function CdkVirtualScrollViewport_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentWrapper = _t.first);\n    }\n  },\n  hostAttrs: [1, \"cdk-virtual-scroll-viewport\"],\n  hostVars: 4,\n  hostBindings: function CdkVirtualScrollViewport_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cdk-virtual-scroll-orientation-horizontal\", ctx.orientation === \"horizontal\")(\"cdk-virtual-scroll-orientation-vertical\", ctx.orientation !== \"horizontal\");\n    }\n  },\n  inputs: {\n    orientation: \"orientation\",\n    appendOnly: \"appendOnly\"\n  },\n  outputs: {\n    scrolledIndexChange: \"scrolledIndexChange\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkScrollable,\n    useExisting: CdkVirtualScrollViewport\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 4,\n  consts: [[1, \"cdk-virtual-scroll-content-wrapper\"], [\"contentWrapper\", \"\"], [1, \"cdk-virtual-scroll-spacer\"]],\n  template: function CdkVirtualScrollViewport_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(3, \"div\", 2);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleProp(\"width\", ctx._totalContentWidth)(\"height\", ctx._totalContentHeight);\n    }\n  },\n  styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0}[dir=rtl] .cdk-virtual-scroll-spacer{right:0;left:auto;transform-origin:100% 0}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollViewport, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport',\n      host: {\n        'class': 'cdk-virtual-scroll-viewport',\n        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: CdkVirtualScrollViewport\n      }],\n      template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\",\n      styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0}[dir=rtl] .cdk-virtual-scroll-spacer{right:0;left:auto;transform-origin:100% 0}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [VIRTUAL_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: ViewportRuler\n    }];\n  }, {\n    orientation: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input\n    }],\n    scrolledIndexChange: [{\n      type: Output\n    }],\n    _contentWrapper: [{\n      type: ViewChild,\n      args: ['contentWrapper', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\n\n\nfunction getOffset(orientation, direction, node) {\n  const el = node;\n\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n\n  const rect = el.getBoundingClientRect();\n\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\n\n\nclass CdkVirtualForOf {\n  constructor(\n  /** The view container to add items to. */\n  _viewContainerRef,\n  /** The template to use when stamping out new items. */\n  _template,\n  /** The set of available differs. */\n  _differs,\n  /** The strategy used to render items in the virtual scroll viewport. */\n  _viewRepeater,\n  /** The virtual scrolling viewport that these items are being rendered in. */\n  _viewport, ngZone) {\n    this._viewContainerRef = _viewContainerRef;\n    this._template = _template;\n    this._differs = _differs;\n    this._viewRepeater = _viewRepeater;\n    this._viewport = _viewport;\n    /** Emits when the rendered view of the data changes. */\n\n    this.viewChange = new Subject();\n    /** Subject that emits when a new DataSource instance is given. */\n\n    this._dataSourceChanges = new Subject();\n    /** Emits whenever the data in the current DataSource changes. */\n\n    this.dataStream = this._dataSourceChanges.pipe( // Start off with null `DataSource`.\n    startWith(null), // Bundle up the previous and current data sources so we can work with both.\n    pairwise(), // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), // Replay the last emitted data when someone subscribes.\n    shareReplay(1));\n    /** The differ used to calculate changes to the data. */\n\n    this._differ = null;\n    /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n\n    this._needsUpdate = false;\n    this._destroyed = new Subject();\n    this.dataStream.subscribe(data => {\n      this._data = data;\n\n      this._onRenderedDataChange();\n    });\n\n    this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n      this._renderedRange = range;\n\n      if (this.viewChange.observers.length) {\n        ngZone.run(() => this.viewChange.next(this._renderedRange));\n      }\n\n      this._onRenderedDataChange();\n    });\n\n    this._viewport.attach(this);\n  }\n  /** The DataSource to display. */\n\n\n  get cdkVirtualForOf() {\n    return this._cdkVirtualForOf;\n  }\n\n  set cdkVirtualForOf(value) {\n    this._cdkVirtualForOf = value;\n\n    if (isDataSource(value)) {\n      this._dataSourceChanges.next(value);\n    } else {\n      // If value is an an NgIterable, convert it to an array.\n      this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n    }\n  }\n  /**\n   * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n   * the item and produces a value to be used as the item's identity when tracking changes.\n   */\n\n\n  get cdkVirtualForTrackBy() {\n    return this._cdkVirtualForTrackBy;\n  }\n\n  set cdkVirtualForTrackBy(fn) {\n    this._needsUpdate = true;\n    this._cdkVirtualForTrackBy = fn ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item) : undefined;\n  }\n  /** The template used to stamp out new elements. */\n\n\n  set cdkVirtualForTemplate(value) {\n    if (value) {\n      this._needsUpdate = true;\n      this._template = value;\n    }\n  }\n  /**\n   * The size of the cache used to store templates that are not being used for re-use later.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n   */\n\n\n  get cdkVirtualForTemplateCacheSize() {\n    return this._viewRepeater.viewCacheSize;\n  }\n\n  set cdkVirtualForTemplateCacheSize(size) {\n    this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n  }\n  /**\n   * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n   * in the specified range. Throws an error if the range includes items that are not currently\n   * rendered.\n   */\n\n\n  measureRangeSize(range, orientation) {\n    if (range.start >= range.end) {\n      return 0;\n    }\n\n    if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Error: attempted to measure an item that isn't rendered.`);\n    } // The index into the list of rendered views for the first item in the range.\n\n\n    const renderedStartIndex = range.start - this._renderedRange.start; // The length of the range we're measuring.\n\n    const rangeLen = range.end - range.start; // Loop over all the views, find the first and land node and compute the size by subtracting\n    // the top of the first node from the bottom of the last one.\n\n    let firstNode;\n    let lastNode; // Find the first node by starting from the beginning and going forwards.\n\n    for (let i = 0; i < rangeLen; i++) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n\n      if (view && view.rootNodes.length) {\n        firstNode = lastNode = view.rootNodes[0];\n        break;\n      }\n    } // Find the last node by starting from the end and going backwards.\n\n\n    for (let i = rangeLen - 1; i > -1; i--) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n\n      if (view && view.rootNodes.length) {\n        lastNode = view.rootNodes[view.rootNodes.length - 1];\n        break;\n      }\n    }\n\n    return firstNode && lastNode ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode) : 0;\n  }\n\n  ngDoCheck() {\n    if (this._differ && this._needsUpdate) {\n      // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n      // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n      // changing (need to do this diff).\n      const changes = this._differ.diff(this._renderedItems);\n\n      if (!changes) {\n        this._updateContext();\n      } else {\n        this._applyChanges(changes);\n      }\n\n      this._needsUpdate = false;\n    }\n  }\n\n  ngOnDestroy() {\n    this._viewport.detach();\n\n    this._dataSourceChanges.next(undefined);\n\n    this._dataSourceChanges.complete();\n\n    this.viewChange.complete();\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n\n    this._viewRepeater.detach();\n  }\n  /** React to scroll state changes in the viewport. */\n\n\n  _onRenderedDataChange() {\n    if (!this._renderedRange) {\n      return;\n    }\n\n    this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n\n    if (!this._differ) {\n      // Use a wrapper function for the `trackBy` so any new values are\n      // picked up automatically without having to recreate the differ.\n      this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n        return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n      });\n    }\n\n    this._needsUpdate = true;\n  }\n  /** Swap out one `DataSource` for another. */\n\n\n  _changeDataSource(oldDs, newDs) {\n    if (oldDs) {\n      oldDs.disconnect(this);\n    }\n\n    this._needsUpdate = true;\n    return newDs ? newDs.connect(this) : of();\n  }\n  /** Update the `CdkVirtualForOfContext` for all views. */\n\n\n  _updateContext() {\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n\n      this._updateComputedContextProperties(view.context);\n\n      view.detectChanges();\n    }\n  }\n  /** Apply changes to the DOM. */\n\n\n  _applyChanges(changes) {\n    this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item); // Update $implicit for any items that had an identity change.\n\n\n    changes.forEachIdentityChange(record => {\n      const view = this._viewContainerRef.get(record.currentIndex);\n\n      view.context.$implicit = record.item;\n    }); // Update the context variables on all items.\n\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n\n      this._updateComputedContextProperties(view.context);\n    }\n  }\n  /** Update the computed properties on the `CdkVirtualForOfContext`. */\n\n\n  _updateComputedContextProperties(context) {\n    context.first = context.index === 0;\n    context.last = context.index === context.count - 1;\n    context.even = context.index % 2 === 0;\n    context.odd = !context.even;\n  }\n\n  _getEmbeddedViewArgs(record, index) {\n    // Note that it's important that we insert the item directly at the proper index,\n    // rather than inserting it and the moving it in place, because if there's a directive\n    // on the same node that injects the `ViewContainerRef`, Angular will insert another\n    // comment node which can throw off the move when it's being repeated for all items.\n    return {\n      templateRef: this._template,\n      context: {\n        $implicit: record.item,\n        // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n        // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n        cdkVirtualForOf: this._cdkVirtualForOf,\n        index: -1,\n        count: -1,\n        first: false,\n        last: false,\n        odd: false,\n        even: false\n      },\n      index\n    };\n  }\n\n}\n\nCdkVirtualForOf.ɵfac = function CdkVirtualForOf_Factory(t) {\n  return new (t || CdkVirtualForOf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(_VIEW_REPEATER_STRATEGY), i0.ɵɵdirectiveInject(CdkVirtualScrollViewport, 4), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nCdkVirtualForOf.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkVirtualForOf,\n  selectors: [[\"\", \"cdkVirtualFor\", \"\", \"cdkVirtualForOf\", \"\"]],\n  inputs: {\n    cdkVirtualForOf: \"cdkVirtualForOf\",\n    cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\",\n    cdkVirtualForTemplate: \"cdkVirtualForTemplate\",\n    cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _RecycleViewRepeaterStrategy\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualForOf, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualFor][cdkVirtualForOf]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.TemplateRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: i2$1._RecycleViewRepeaterStrategy,\n      decorators: [{\n        type: Inject,\n        args: [_VIEW_REPEATER_STRATEGY]\n      }]\n    }, {\n      type: CdkVirtualScrollViewport,\n      decorators: [{\n        type: SkipSelf\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    cdkVirtualForOf: [{\n      type: Input\n    }],\n    cdkVirtualForTrackBy: [{\n      type: Input\n    }],\n    cdkVirtualForTemplate: [{\n      type: Input\n    }],\n    cdkVirtualForTemplateCacheSize: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass CdkScrollableModule {}\n\nCdkScrollableModule.ɵfac = function CdkScrollableModule_Factory(t) {\n  return new (t || CdkScrollableModule)();\n};\n\nCdkScrollableModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CdkScrollableModule\n});\nCdkScrollableModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollableModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkScrollable],\n      declarations: [CdkScrollable]\n    }]\n  }], null, null);\n})();\n/**\n * @docs-primary-export\n */\n\n\nclass ScrollingModule {}\n\nScrollingModule.ɵfac = function ScrollingModule_Factory(t) {\n  return new (t || ScrollingModule)();\n};\n\nScrollingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ScrollingModule\n});\nScrollingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[BidiModule, CdkScrollableModule], BidiModule, CdkScrollableModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, CdkScrollableModule],\n      exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport],\n      declarations: [CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/cdk/fesm2020/scrolling.mjs"], "names": ["coerceNumberProperty", "coerceElement", "coerceBooleanProperty", "i0", "InjectionToken", "forwardRef", "Directive", "Input", "Injectable", "Optional", "Inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Output", "ViewChild", "SkipSelf", "NgModule", "Subject", "of", "Observable", "fromEvent", "animationFrameScheduler", "asapScheduler", "Subscription", "isObservable", "distinctUntilChanged", "auditTime", "filter", "takeUntil", "startWith", "pairwise", "switchMap", "shareReplay", "i1", "getRtlScrollAxisType", "supportsScrollBehavior", "DOCUMENT", "i2", "BidiModule", "i2$1", "isDataSource", "ArrayDataSource", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "VIRTUAL_SCROLL_STRATEGY", "FixedSizeVirtualScrollStrategy", "constructor", "itemSize", "minBufferPx", "maxBufferPx", "_scrolledIndexChange", "scrolledIndexChange", "pipe", "_viewport", "_itemSize", "_minBufferPx", "_maxBufferPx", "attach", "viewport", "_updateTotalContentSize", "_updateRenderedRange", "detach", "complete", "updateItemAndBufferSize", "ngDevMode", "Error", "onContentScrolled", "onDataLengthChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "onRenderedOffsetChanged", "scrollToIndex", "index", "behavior", "scrollToOffset", "setTotalContentSize", "getDataLength", "renderedRange", "getRenderedRange", "newRange", "start", "end", "viewportSize", "getViewportSize", "dataLength", "scrollOffset", "measureScrollOffset", "firstVisibleIndex", "maxVisibleItems", "Math", "ceil", "newVisibleIndex", "max", "min", "floor", "startBuffer", "expandStart", "end<PERSON><PERSON><PERSON>", "expandEnd", "setR<PERSON>edRange", "setRenderedContentOffset", "next", "_fixedSizeVirtualScrollStrategyFactory", "fixedSizeDir", "_scrollStrategy", "CdkFixedSizeVirtualScroll", "value", "ngOnChanges", "ɵfac", "ɵdir", "provide", "useFactory", "deps", "type", "args", "selector", "providers", "DEFAULT_SCROLL_TIME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ngZone", "_platform", "document", "_scrolled", "_globalSubscription", "_scrolledCount", "scrollContainers", "Map", "_document", "register", "scrollable", "has", "set", "elementScrolled", "subscribe", "deregister", "scrollableReference", "get", "unsubscribe", "delete", "scrolled", "auditTimeInMs", "<PERSON><PERSON><PERSON><PERSON>", "observer", "_addGlobalListener", "subscription", "_removeGlobalListener", "ngOnDestroy", "for<PERSON>ach", "_", "container", "ancestorScrolled", "elementOrElementRef", "ancestors", "getAncestorScrollContainers", "target", "indexOf", "scrollingContainers", "_subscription", "_scrollableContainsElement", "push", "_getWindow", "defaultView", "window", "element", "scrollableElement", "getElementRef", "nativeElement", "parentElement", "runOutsideAngular", "NgZone", "Platform", "ɵprov", "providedIn", "undefined", "decorators", "CdkScrollable", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "dir", "_destroyed", "_elementScrolled", "ngOnInit", "scrollTo", "options", "el", "isRtl", "left", "right", "bottom", "top", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "_applyScrollToOptions", "scrollTop", "scrollLeft", "from", "LEFT", "RIGHT", "ElementRef", "Directionality", "DEFAULT_RESIZE_TIME", "ViewportRuler", "_change", "_changeListener", "event", "addEventListener", "change", "_viewportSize", "removeEventListener", "_updateViewportSize", "output", "width", "height", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "documentElement", "documentRect", "getBoundingClientRect", "body", "scrollY", "scrollX", "throttleTime", "innerWidth", "innerHeight", "rangesEqual", "r1", "r2", "SCROLL_SCHEDULER", "requestAnimationFrame", "CdkVirtualScrollViewport", "_changeDetectorRef", "viewportRuler", "_detachedSubject", "_renderedRangeSubject", "_orientation", "_appendOnly", "Promise", "resolve", "then", "run", "renderedRangeStream", "_totalContentSize", "_totalContentWidth", "_totalContentHeight", "_rendered<PERSON><PERSON>e", "_dataLength", "_renderedContentOffset", "_renderedContentOffsetNeedsRewrite", "_isChangeDetectionPending", "_runAfterChangeDetection", "_viewportChanges", "EMPTY", "checkViewportSize", "orientation", "_calculateSpacerSize", "appendOnly", "_measureViewportSize", "_markChangeDetectionNeeded", "forOf", "_forOf", "dataStream", "data", "<PERSON><PERSON><PERSON><PERSON>", "length", "_doChangeDetection", "size", "range", "getOffsetToRenderedContentStart", "offset", "to", "isHorizontal", "axis", "axisDirection", "transform", "Number", "_renderedContentTransform", "measureRenderedContentSize", "contentEl", "_contentWrapper", "offsetWidth", "offsetHeight", "measureRangeSize", "viewportEl", "runAfter", "style", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "runAfterChangeDetection", "fn", "ChangeDetectorRef", "ɵcmp", "useExisting", "host", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "static", "getOffset", "direction", "node", "rect", "CdkVirtualForOf", "_viewContainerRef", "_template", "_differs", "_view<PERSON><PERSON><PERSON>er", "viewChange", "_dataSourceChanges", "prev", "cur", "_changeDataSource", "_differ", "_needsUpdate", "_data", "_onRenderedDataChange", "observers", "cdkVirtualForOf", "_cdkVirtualForOf", "Array", "cdkVirtualForTrackBy", "_cdkVirtualForTrackBy", "item", "cdkVirtualForTemplate", "cdkVirtualForTemplateCacheSize", "viewCacheSize", "renderedStartIndex", "rangeLen", "firstNode", "lastNode", "i", "view", "rootNodes", "ngDoCheck", "changes", "diff", "_renderedItems", "_updateContext", "_applyChanges", "slice", "find", "create", "oldDs", "newDs", "disconnect", "connect", "count", "context", "_updateComputedContextProperties", "detectChanges", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "forEachIdentityChange", "$implicit", "first", "last", "even", "odd", "templateRef", "ViewContainerRef", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "useClass", "CdkScrollableModule", "ɵmod", "ɵinj", "exports", "declarations", "ScrollingModule", "imports"], "mappings": "AAAA,SAASA,oBAAT,EAA+BC,aAA/B,EAA8CC,qBAA9C,QAA2E,uBAA3E;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,UAAzB,EAAqCC,SAArC,EAAgDC,KAAhD,EAAuDC,UAAvD,EAAmEC,QAAnE,EAA6EC,MAA7E,EAAqFC,SAArF,EAAgGC,iBAAhG,EAAmHC,uBAAnH,EAA4IC,MAA5I,EAAoJC,SAApJ,EAA+JC,QAA/J,EAAyKC,QAAzK,QAAyL,eAAzL;AACA,SAASC,OAAT,EAAkBC,EAAlB,EAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,uBAA7C,EAAsEC,aAAtE,EAAqFC,YAArF,EAAmGC,YAAnG,QAAuH,MAAvH;AACA,SAASC,oBAAT,EAA+BC,SAA/B,EAA0CC,MAA1C,EAAkDC,SAAlD,EAA6DC,SAA7D,EAAwEC,QAAxE,EAAkFC,SAAlF,EAA6FC,WAA7F,QAAgH,gBAAhH;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,oBAAT,EAA+BC,sBAA/B,QAA6D,uBAA7D;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,OAAO,KAAKC,IAAZ,MAAsB,0BAAtB;AACA,SAASC,YAAT,EAAuBC,eAAvB,EAAwCC,uBAAxC,EAAiEC,4BAAjE,QAAqG,0BAArG;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;;AACA,MAAMC,uBAAuB,GAAG,IAAIzC,cAAJ,CAAmB,yBAAnB,CAAhC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAM0C,8BAAN,CAAqC;AACjC;AACJ;AACA;AACA;AACA;AACIC,EAAAA,WAAW,CAACC,QAAD,EAAWC,WAAX,EAAwBC,WAAxB,EAAqC;AAC5C,SAAKC,oBAAL,GAA4B,IAAIjC,OAAJ,EAA5B;AACA;;AACA,SAAKkC,mBAAL,GAA2B,KAAKD,oBAAL,CAA0BE,IAA1B,CAA+B3B,oBAAoB,EAAnD,CAA3B;AACA;;AACA,SAAK4B,SAAL,GAAiB,IAAjB;AACA,SAAKC,SAAL,GAAiBP,QAAjB;AACA,SAAKQ,YAAL,GAAoBP,WAApB;AACA,SAAKQ,YAAL,GAAoBP,WAApB;AACH;AACD;AACJ;AACA;AACA;;;AACIQ,EAAAA,MAAM,CAACC,QAAD,EAAW;AACb,SAAKL,SAAL,GAAiBK,QAAjB;;AACA,SAAKC,uBAAL;;AACA,SAAKC,oBAAL;AACH;AACD;;;AACAC,EAAAA,MAAM,GAAG;AACL,SAAKX,oBAAL,CAA0BY,QAA1B;;AACA,SAAKT,SAAL,GAAiB,IAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIU,EAAAA,uBAAuB,CAAChB,QAAD,EAAWC,WAAX,EAAwBC,WAAxB,EAAqC;AACxD,QAAIA,WAAW,GAAGD,WAAd,KAA8B,OAAOgB,SAAP,KAAqB,WAArB,IAAoCA,SAAlE,CAAJ,EAAkF;AAC9E,YAAMC,KAAK,CAAC,8EAAD,CAAX;AACH;;AACD,SAAKX,SAAL,GAAiBP,QAAjB;AACA,SAAKQ,YAAL,GAAoBP,WAApB;AACA,SAAKQ,YAAL,GAAoBP,WAApB;;AACA,SAAKU,uBAAL;;AACA,SAAKC,oBAAL;AACH;AACD;;;AACAM,EAAAA,iBAAiB,GAAG;AAChB,SAAKN,oBAAL;AACH;AACD;;;AACAO,EAAAA,mBAAmB,GAAG;AAClB,SAAKR,uBAAL;;AACA,SAAKC,oBAAL;AACH;AACD;;;AACAQ,EAAAA,iBAAiB,GAAG;AAChB;AACH;AACD;;;AACAC,EAAAA,uBAAuB,GAAG;AACtB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,aAAa,CAACC,KAAD,EAAQC,QAAR,EAAkB;AAC3B,QAAI,KAAKnB,SAAT,EAAoB;AAChB,WAAKA,SAAL,CAAeoB,cAAf,CAA8BF,KAAK,GAAG,KAAKjB,SAA3C,EAAsDkB,QAAtD;AACH;AACJ;AACD;;;AACAb,EAAAA,uBAAuB,GAAG;AACtB,QAAI,CAAC,KAAKN,SAAV,EAAqB;AACjB;AACH;;AACD,SAAKA,SAAL,CAAeqB,mBAAf,CAAmC,KAAKrB,SAAL,CAAesB,aAAf,KAAiC,KAAKrB,SAAzE;AACH;AACD;;;AACAM,EAAAA,oBAAoB,GAAG;AACnB,QAAI,CAAC,KAAKP,SAAV,EAAqB;AACjB;AACH;;AACD,UAAMuB,aAAa,GAAG,KAAKvB,SAAL,CAAewB,gBAAf,EAAtB;;AACA,UAAMC,QAAQ,GAAG;AAAEC,MAAAA,KAAK,EAAEH,aAAa,CAACG,KAAvB;AAA8BC,MAAAA,GAAG,EAAEJ,aAAa,CAACI;AAAjD,KAAjB;;AACA,UAAMC,YAAY,GAAG,KAAK5B,SAAL,CAAe6B,eAAf,EAArB;;AACA,UAAMC,UAAU,GAAG,KAAK9B,SAAL,CAAesB,aAAf,EAAnB;;AACA,QAAIS,YAAY,GAAG,KAAK/B,SAAL,CAAegC,mBAAf,EAAnB,CARmB,CASnB;;;AACA,QAAIC,iBAAiB,GAAG,KAAKhC,SAAL,GAAiB,CAAjB,GAAqB8B,YAAY,GAAG,KAAK9B,SAAzC,GAAqD,CAA7E,CAVmB,CAWnB;;AACA,QAAIwB,QAAQ,CAACE,GAAT,GAAeG,UAAnB,EAA+B;AAC3B;AACA,YAAMI,eAAe,GAAGC,IAAI,CAACC,IAAL,CAAUR,YAAY,GAAG,KAAK3B,SAA9B,CAAxB;AACA,YAAMoC,eAAe,GAAGF,IAAI,CAACG,GAAL,CAAS,CAAT,EAAYH,IAAI,CAACI,GAAL,CAASN,iBAAT,EAA4BH,UAAU,GAAGI,eAAzC,CAAZ,CAAxB,CAH2B,CAI3B;AACA;;AACA,UAAID,iBAAiB,IAAII,eAAzB,EAA0C;AACtCJ,QAAAA,iBAAiB,GAAGI,eAApB;AACAN,QAAAA,YAAY,GAAGM,eAAe,GAAG,KAAKpC,SAAtC;AACAwB,QAAAA,QAAQ,CAACC,KAAT,GAAiBS,IAAI,CAACK,KAAL,CAAWP,iBAAX,CAAjB;AACH;;AACDR,MAAAA,QAAQ,CAACE,GAAT,GAAeQ,IAAI,CAACG,GAAL,CAAS,CAAT,EAAYH,IAAI,CAACI,GAAL,CAAST,UAAT,EAAqBL,QAAQ,CAACC,KAAT,GAAiBQ,eAAtC,CAAZ,CAAf;AACH;;AACD,UAAMO,WAAW,GAAGV,YAAY,GAAGN,QAAQ,CAACC,KAAT,GAAiB,KAAKzB,SAAzD;;AACA,QAAIwC,WAAW,GAAG,KAAKvC,YAAnB,IAAmCuB,QAAQ,CAACC,KAAT,IAAkB,CAAzD,EAA4D;AACxD,YAAMgB,WAAW,GAAGP,IAAI,CAACC,IAAL,CAAU,CAAC,KAAKjC,YAAL,GAAoBsC,WAArB,IAAoC,KAAKxC,SAAnD,CAApB;AACAwB,MAAAA,QAAQ,CAACC,KAAT,GAAiBS,IAAI,CAACG,GAAL,CAAS,CAAT,EAAYb,QAAQ,CAACC,KAAT,GAAiBgB,WAA7B,CAAjB;AACAjB,MAAAA,QAAQ,CAACE,GAAT,GAAeQ,IAAI,CAACI,GAAL,CAAST,UAAT,EAAqBK,IAAI,CAACC,IAAL,CAAUH,iBAAiB,GAAG,CAACL,YAAY,GAAG,KAAK1B,YAArB,IAAqC,KAAKD,SAAxE,CAArB,CAAf;AACH,KAJD,MAKK;AACD,YAAM0C,SAAS,GAAGlB,QAAQ,CAACE,GAAT,GAAe,KAAK1B,SAApB,IAAiC8B,YAAY,GAAGH,YAAhD,CAAlB;;AACA,UAAIe,SAAS,GAAG,KAAKzC,YAAjB,IAAiCuB,QAAQ,CAACE,GAAT,IAAgBG,UAArD,EAAiE;AAC7D,cAAMc,SAAS,GAAGT,IAAI,CAACC,IAAL,CAAU,CAAC,KAAKjC,YAAL,GAAoBwC,SAArB,IAAkC,KAAK1C,SAAjD,CAAlB;;AACA,YAAI2C,SAAS,GAAG,CAAhB,EAAmB;AACfnB,UAAAA,QAAQ,CAACE,GAAT,GAAeQ,IAAI,CAACI,GAAL,CAAST,UAAT,EAAqBL,QAAQ,CAACE,GAAT,GAAeiB,SAApC,CAAf;AACAnB,UAAAA,QAAQ,CAACC,KAAT,GAAiBS,IAAI,CAACG,GAAL,CAAS,CAAT,EAAYH,IAAI,CAACK,KAAL,CAAWP,iBAAiB,GAAG,KAAK/B,YAAL,GAAoB,KAAKD,SAAxD,CAAZ,CAAjB;AACH;AACJ;AACJ;;AACD,SAAKD,SAAL,CAAe6C,gBAAf,CAAgCpB,QAAhC;;AACA,SAAKzB,SAAL,CAAe8C,wBAAf,CAAwC,KAAK7C,SAAL,GAAiBwB,QAAQ,CAACC,KAAlE;;AACA,SAAK7B,oBAAL,CAA0BkD,IAA1B,CAA+BZ,IAAI,CAACK,KAAL,CAAWP,iBAAX,CAA/B;AACH;;AA7HgC;AA+HrC;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASe,sCAAT,CAAgDC,YAAhD,EAA8D;AAC1D,SAAOA,YAAY,CAACC,eAApB;AACH;AACD;;;AACA,MAAMC,yBAAN,CAAgC;AAC5B1D,EAAAA,WAAW,GAAG;AACV,SAAKQ,SAAL,GAAiB,EAAjB;AACA,SAAKC,YAAL,GAAoB,GAApB;AACA,SAAKC,YAAL,GAAoB,GAApB;AACA;;AACA,SAAK+C,eAAL,GAAuB,IAAI1D,8BAAJ,CAAmC,KAAKE,QAAxC,EAAkD,KAAKC,WAAvD,EAAoE,KAAKC,WAAzE,CAAvB;AACH;AACD;;;AACY,MAARF,QAAQ,GAAG;AACX,WAAO,KAAKO,SAAZ;AACH;;AACW,MAARP,QAAQ,CAAC0D,KAAD,EAAQ;AAChB,SAAKnD,SAAL,GAAiBvD,oBAAoB,CAAC0G,KAAD,CAArC;AACH;AACD;AACJ;AACA;AACA;;;AACmB,MAAXzD,WAAW,GAAG;AACd,WAAO,KAAKO,YAAZ;AACH;;AACc,MAAXP,WAAW,CAACyD,KAAD,EAAQ;AACnB,SAAKlD,YAAL,GAAoBxD,oBAAoB,CAAC0G,KAAD,CAAxC;AACH;AACD;AACJ;AACA;;;AACmB,MAAXxD,WAAW,GAAG;AACd,WAAO,KAAKO,YAAZ;AACH;;AACc,MAAXP,WAAW,CAACwD,KAAD,EAAQ;AACnB,SAAKjD,YAAL,GAAoBzD,oBAAoB,CAAC0G,KAAD,CAAxC;AACH;;AACDC,EAAAA,WAAW,GAAG;AACV,SAAKH,eAAL,CAAqBxC,uBAArB,CAA6C,KAAKhB,QAAlD,EAA4D,KAAKC,WAAjE,EAA8E,KAAKC,WAAnF;AACH;;AApC2B;;AAsChCuD,yBAAyB,CAACG,IAA1B;AAAA,mBAAsHH,yBAAtH;AAAA;;AACAA,yBAAyB,CAACI,IAA1B,kBAD4G1G,EAC5G;AAAA,QAA0GsG,yBAA1G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAD4GtG,EAC5G,oBAA6R,CACrR;AACI2G,IAAAA,OAAO,EAAEjE,uBADb;AAEIkE,IAAAA,UAAU,EAAET,sCAFhB;AAGIU,IAAAA,IAAI,EAAE,CAAC3G,UAAU,CAAC,MAAMoG,yBAAP,CAAX;AAHV,GADqR,CAA7R,GAD4GtG,EAC5G;AAAA;;AAOA;AAAA,qDAR4GA,EAQ5G,mBAA2FsG,yBAA3F,EAAkI,CAAC;AACvHQ,IAAAA,IAAI,EAAE3G,SADiH;AAEvH4G,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,uCADX;AAECC,MAAAA,SAAS,EAAE,CACP;AACIN,QAAAA,OAAO,EAAEjE,uBADb;AAEIkE,QAAAA,UAAU,EAAET,sCAFhB;AAGIU,QAAAA,IAAI,EAAE,CAAC3G,UAAU,CAAC,MAAMoG,yBAAP,CAAX;AAHV,OADO;AAFZ,KAAD;AAFiH,GAAD,CAAlI,QAY4B;AAAEzD,IAAAA,QAAQ,EAAE,CAAC;AACzBiE,MAAAA,IAAI,EAAE1G;AADmB,KAAD,CAAZ;AAEZ0C,IAAAA,WAAW,EAAE,CAAC;AACdgE,MAAAA,IAAI,EAAE1G;AADQ,KAAD,CAFD;AAIZ2C,IAAAA,WAAW,EAAE,CAAC;AACd+D,MAAAA,IAAI,EAAE1G;AADQ,KAAD;AAJD,GAZ5B;AAAA;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM8G,mBAAmB,GAAG,EAA5B;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAN,CAAuB;AACnBvE,EAAAA,WAAW,CAACwE,OAAD,EAAUC,SAAV,EAAqBC,QAArB,EAA+B;AACtC,SAAKF,OAAL,GAAeA,OAAf;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA;;AACA,SAAKE,SAAL,GAAiB,IAAIxG,OAAJ,EAAjB;AACA;;AACA,SAAKyG,mBAAL,GAA2B,IAA3B;AACA;;AACA,SAAKC,cAAL,GAAsB,CAAtB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,gBAAL,GAAwB,IAAIC,GAAJ,EAAxB;AACA,SAAKC,SAAL,GAAiBN,QAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIO,EAAAA,QAAQ,CAACC,UAAD,EAAa;AACjB,QAAI,CAAC,KAAKJ,gBAAL,CAAsBK,GAAtB,CAA0BD,UAA1B,CAAL,EAA4C;AACxC,WAAKJ,gBAAL,CAAsBM,GAAtB,CAA0BF,UAA1B,EAAsCA,UAAU,CAACG,eAAX,GAA6BC,SAA7B,CAAuC,MAAM,KAAKX,SAAL,CAAerB,IAAf,CAAoB4B,UAApB,CAA7C,CAAtC;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIK,EAAAA,UAAU,CAACL,UAAD,EAAa;AACnB,UAAMM,mBAAmB,GAAG,KAAKV,gBAAL,CAAsBW,GAAtB,CAA0BP,UAA1B,CAA5B;;AACA,QAAIM,mBAAJ,EAAyB;AACrBA,MAAAA,mBAAmB,CAACE,WAApB;AACA,WAAKZ,gBAAL,CAAsBa,MAAtB,CAA6BT,UAA7B;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIU,EAAAA,QAAQ,CAACC,aAAa,GAAGvB,mBAAjB,EAAsC;AAC1C,QAAI,CAAC,KAAKG,SAAL,CAAeqB,SAApB,EAA+B;AAC3B,aAAO1H,EAAE,EAAT;AACH;;AACD,WAAO,IAAIC,UAAJ,CAAgB0H,QAAD,IAAc;AAChC,UAAI,CAAC,KAAKnB,mBAAV,EAA+B;AAC3B,aAAKoB,kBAAL;AACH,OAH+B,CAIhC;AACA;;;AACA,YAAMC,YAAY,GAAGJ,aAAa,GAAG,CAAhB,GACf,KAAKlB,SAAL,CAAerE,IAAf,CAAoB1B,SAAS,CAACiH,aAAD,CAA7B,EAA8CP,SAA9C,CAAwDS,QAAxD,CADe,GAEf,KAAKpB,SAAL,CAAeW,SAAf,CAAyBS,QAAzB,CAFN;AAGA,WAAKlB,cAAL;AACA,aAAO,MAAM;AACToB,QAAAA,YAAY,CAACP,WAAb;AACA,aAAKb,cAAL;;AACA,YAAI,CAAC,KAAKA,cAAV,EAA0B;AACtB,eAAKqB,qBAAL;AACH;AACJ,OAND;AAOH,KAjBM,CAAP;AAkBH;;AACDC,EAAAA,WAAW,GAAG;AACV,SAAKD,qBAAL;;AACA,SAAKpB,gBAAL,CAAsBsB,OAAtB,CAA8B,CAACC,CAAD,EAAIC,SAAJ,KAAkB,KAAKf,UAAL,CAAgBe,SAAhB,CAAhD;;AACA,SAAK3B,SAAL,CAAe3D,QAAf;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIuF,EAAAA,gBAAgB,CAACC,mBAAD,EAAsBX,aAAtB,EAAqC;AACjD,UAAMY,SAAS,GAAG,KAAKC,2BAAL,CAAiCF,mBAAjC,CAAlB;AACA,WAAO,KAAKZ,QAAL,CAAcC,aAAd,EAA6BvF,IAA7B,CAAkCzB,MAAM,CAAC8H,MAAM,IAAI;AACtD,aAAO,CAACA,MAAD,IAAWF,SAAS,CAACG,OAAV,CAAkBD,MAAlB,IAA4B,CAAC,CAA/C;AACH,KAF8C,CAAxC,CAAP;AAGH;AACD;;;AACAD,EAAAA,2BAA2B,CAACF,mBAAD,EAAsB;AAC7C,UAAMK,mBAAmB,GAAG,EAA5B;AACA,SAAK/B,gBAAL,CAAsBsB,OAAtB,CAA8B,CAACU,aAAD,EAAgB5B,UAAhB,KAA+B;AACzD,UAAI,KAAK6B,0BAAL,CAAgC7B,UAAhC,EAA4CsB,mBAA5C,CAAJ,EAAsE;AAClEK,QAAAA,mBAAmB,CAACG,IAApB,CAAyB9B,UAAzB;AACH;AACJ,KAJD;AAKA,WAAO2B,mBAAP;AACH;AACD;;;AACAI,EAAAA,UAAU,GAAG;AACT,WAAO,KAAKjC,SAAL,CAAekC,WAAf,IAA8BC,MAArC;AACH;AACD;;;AACAJ,EAAAA,0BAA0B,CAAC7B,UAAD,EAAasB,mBAAb,EAAkC;AACxD,QAAIY,OAAO,GAAGlK,aAAa,CAACsJ,mBAAD,CAA3B;AACA,QAAIa,iBAAiB,GAAGnC,UAAU,CAACoC,aAAX,GAA2BC,aAAnD,CAFwD,CAGxD;AACA;;AACA,OAAG;AACC,UAAIH,OAAO,IAAIC,iBAAf,EAAkC;AAC9B,eAAO,IAAP;AACH;AACJ,KAJD,QAIUD,OAAO,GAAGA,OAAO,CAACI,aAJ5B;;AAKA,WAAO,KAAP;AACH;AACD;;;AACAxB,EAAAA,kBAAkB,GAAG;AACjB,SAAKpB,mBAAL,GAA2B,KAAKJ,OAAL,CAAaiD,iBAAb,CAA+B,MAAM;AAC5D,YAAMN,MAAM,GAAG,KAAKF,UAAL,EAAf;;AACA,aAAO3I,SAAS,CAAC6I,MAAM,CAACzC,QAAR,EAAkB,QAAlB,CAAT,CAAqCY,SAArC,CAA+C,MAAM,KAAKX,SAAL,CAAerB,IAAf,EAArD,CAAP;AACH,KAH0B,CAA3B;AAIH;AACD;;;AACA4C,EAAAA,qBAAqB,GAAG;AACpB,QAAI,KAAKtB,mBAAT,EAA8B;AAC1B,WAAKA,mBAAL,CAAyBc,WAAzB;;AACA,WAAKd,mBAAL,GAA2B,IAA3B;AACH;AACJ;;AAhIkB;;AAkIvBL,gBAAgB,CAACV,IAAjB;AAAA,mBAA6GU,gBAA7G,EA3K4GnH,EA2K5G,UAA+IA,EAAE,CAACsK,MAAlJ,GA3K4GtK,EA2K5G,UAAqK+B,EAAE,CAACwI,QAAxK,GA3K4GvK,EA2K5G,UAA6LkC,QAA7L;AAAA;;AACAiF,gBAAgB,CAACqD,KAAjB,kBA5K4GxK,EA4K5G;AAAA,SAAiHmH,gBAAjH;AAAA,WAAiHA,gBAAjH;AAAA,cAA+I;AAA/I;;AACA;AAAA,qDA7K4GnH,EA6K5G,mBAA2FmH,gBAA3F,EAAyH,CAAC;AAC9GL,IAAAA,IAAI,EAAEzG,UADwG;AAE9G0G,IAAAA,IAAI,EAAE,CAAC;AAAE0D,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFwG,GAAD,CAAzH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE3D,MAAAA,IAAI,EAAE9G,EAAE,CAACsK;AAAX,KAAD,EAAsB;AAAExD,MAAAA,IAAI,EAAE/E,EAAE,CAACwI;AAAX,KAAtB,EAA6C;AAAEzD,MAAAA,IAAI,EAAE4D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC1G7D,QAAAA,IAAI,EAAExG;AADoG,OAAD,EAE1G;AACCwG,QAAAA,IAAI,EAAEvG,MADP;AAECwG,QAAAA,IAAI,EAAE,CAAC7E,QAAD;AAFP,OAF0G;AAA/B,KAA7C,CAAP;AAKlB,GARxB;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0I,aAAN,CAAoB;AAChBhI,EAAAA,WAAW,CAACiI,UAAD,EAAaC,gBAAb,EAA+BC,MAA/B,EAAuCC,GAAvC,EAA4C;AACnD,SAAKH,UAAL,GAAkBA,UAAlB;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,GAAL,GAAWA,GAAX;AACA,SAAKC,UAAL,GAAkB,IAAIlK,OAAJ,EAAlB;AACA,SAAKmK,gBAAL,GAAwB,IAAIjK,UAAJ,CAAgB0H,QAAD,IAAc,KAAKoC,MAAL,CAAYV,iBAAZ,CAA8B,MAAMnJ,SAAS,CAAC,KAAK2J,UAAL,CAAgBV,aAAjB,EAAgC,QAAhC,CAAT,CACpFjH,IADoF,CAC/ExB,SAAS,CAAC,KAAKuJ,UAAN,CADsE,EAEpF/C,SAFoF,CAE1ES,QAF0E,CAApC,CAA7B,CAAxB;AAGH;;AACDwC,EAAAA,QAAQ,GAAG;AACP,SAAKL,gBAAL,CAAsBjD,QAAtB,CAA+B,IAA/B;AACH;;AACDkB,EAAAA,WAAW,GAAG;AACV,SAAK+B,gBAAL,CAAsB3C,UAAtB,CAAiC,IAAjC;;AACA,SAAK8C,UAAL,CAAgB/E,IAAhB;;AACA,SAAK+E,UAAL,CAAgBrH,QAAhB;AACH;AACD;;;AACAqE,EAAAA,eAAe,GAAG;AACd,WAAO,KAAKiD,gBAAZ;AACH;AACD;;;AACAhB,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAKW,UAAZ;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIO,EAAAA,QAAQ,CAACC,OAAD,EAAU;AACd,UAAMC,EAAE,GAAG,KAAKT,UAAL,CAAgBV,aAA3B;AACA,UAAMoB,KAAK,GAAG,KAAKP,GAAL,IAAY,KAAKA,GAAL,CAASzE,KAAT,IAAkB,KAA5C,CAFc,CAGd;;AACA,QAAI8E,OAAO,CAACG,IAAR,IAAgB,IAApB,EAA0B;AACtBH,MAAAA,OAAO,CAACG,IAAR,GAAeD,KAAK,GAAGF,OAAO,CAACvG,GAAX,GAAiBuG,OAAO,CAACxG,KAA7C;AACH;;AACD,QAAIwG,OAAO,CAACI,KAAR,IAAiB,IAArB,EAA2B;AACvBJ,MAAAA,OAAO,CAACI,KAAR,GAAgBF,KAAK,GAAGF,OAAO,CAACxG,KAAX,GAAmBwG,OAAO,CAACvG,GAAhD;AACH,KATa,CAUd;;;AACA,QAAIuG,OAAO,CAACK,MAAR,IAAkB,IAAtB,EAA4B;AACxBL,MAAAA,OAAO,CAACM,GAAR,GACIL,EAAE,CAACM,YAAH,GAAkBN,EAAE,CAACO,YAArB,GAAoCR,OAAO,CAACK,MADhD;AAEH,KAda,CAed;;;AACA,QAAIH,KAAK,IAAIvJ,oBAAoB,MAAM;AAAE;AAAzC,MAAuD;AACnD,UAAIqJ,OAAO,CAACG,IAAR,IAAgB,IAApB,EAA0B;AACtBH,QAAAA,OAAO,CAACI,KAAR,GACIH,EAAE,CAACQ,WAAH,GAAiBR,EAAE,CAACS,WAApB,GAAkCV,OAAO,CAACG,IAD9C;AAEH;;AACD,UAAIxJ,oBAAoB,MAAM;AAAE;AAAhC,QAAgD;AAC5CqJ,QAAAA,OAAO,CAACG,IAAR,GAAeH,OAAO,CAACI,KAAvB;AACH,OAFD,MAGK,IAAIzJ,oBAAoB,MAAM;AAAE;AAAhC,QAA+C;AAChDqJ,QAAAA,OAAO,CAACG,IAAR,GAAeH,OAAO,CAACI,KAAR,GAAgB,CAACJ,OAAO,CAACI,KAAzB,GAAiCJ,OAAO,CAACI,KAAxD;AACH;AACJ,KAXD,MAYK;AACD,UAAIJ,OAAO,CAACI,KAAR,IAAiB,IAArB,EAA2B;AACvBJ,QAAAA,OAAO,CAACG,IAAR,GACIF,EAAE,CAACQ,WAAH,GAAiBR,EAAE,CAACS,WAApB,GAAkCV,OAAO,CAACI,KAD9C;AAEH;AACJ;;AACD,SAAKO,qBAAL,CAA2BX,OAA3B;AACH;;AACDW,EAAAA,qBAAqB,CAACX,OAAD,EAAU;AAC3B,UAAMC,EAAE,GAAG,KAAKT,UAAL,CAAgBV,aAA3B;;AACA,QAAIlI,sBAAsB,EAA1B,EAA8B;AAC1BqJ,MAAAA,EAAE,CAACF,QAAH,CAAYC,OAAZ;AACH,KAFD,MAGK;AACD,UAAIA,OAAO,CAACM,GAAR,IAAe,IAAnB,EAAyB;AACrBL,QAAAA,EAAE,CAACW,SAAH,GAAeZ,OAAO,CAACM,GAAvB;AACH;;AACD,UAAIN,OAAO,CAACG,IAAR,IAAgB,IAApB,EAA0B;AACtBF,QAAAA,EAAE,CAACY,UAAH,GAAgBb,OAAO,CAACG,IAAxB;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIrG,EAAAA,mBAAmB,CAACgH,IAAD,EAAO;AACtB,UAAMC,IAAI,GAAG,MAAb;AACA,UAAMC,KAAK,GAAG,OAAd;AACA,UAAMf,EAAE,GAAG,KAAKT,UAAL,CAAgBV,aAA3B;;AACA,QAAIgC,IAAI,IAAI,KAAZ,EAAmB;AACf,aAAOb,EAAE,CAACW,SAAV;AACH;;AACD,QAAIE,IAAI,IAAI,QAAZ,EAAsB;AAClB,aAAOb,EAAE,CAACM,YAAH,GAAkBN,EAAE,CAACO,YAArB,GAAoCP,EAAE,CAACW,SAA9C;AACH,KATqB,CAUtB;;;AACA,UAAMV,KAAK,GAAG,KAAKP,GAAL,IAAY,KAAKA,GAAL,CAASzE,KAAT,IAAkB,KAA5C;;AACA,QAAI4F,IAAI,IAAI,OAAZ,EAAqB;AACjBA,MAAAA,IAAI,GAAGZ,KAAK,GAAGc,KAAH,GAAWD,IAAvB;AACH,KAFD,MAGK,IAAID,IAAI,IAAI,KAAZ,EAAmB;AACpBA,MAAAA,IAAI,GAAGZ,KAAK,GAAGa,IAAH,GAAUC,KAAtB;AACH;;AACD,QAAId,KAAK,IAAIvJ,oBAAoB,MAAM;AAAE;AAAzC,MAAyD;AACrD;AACA;AACA,UAAImK,IAAI,IAAIC,IAAZ,EAAkB;AACd,eAAOd,EAAE,CAACQ,WAAH,GAAiBR,EAAE,CAACS,WAApB,GAAkCT,EAAE,CAACY,UAA5C;AACH,OAFD,MAGK;AACD,eAAOZ,EAAE,CAACY,UAAV;AACH;AACJ,KATD,MAUK,IAAIX,KAAK,IAAIvJ,oBAAoB,MAAM;AAAE;AAAzC,MAAwD;AACzD;AACA;AACA,UAAImK,IAAI,IAAIC,IAAZ,EAAkB;AACd,eAAOd,EAAE,CAACY,UAAH,GAAgBZ,EAAE,CAACQ,WAAnB,GAAiCR,EAAE,CAACS,WAA3C;AACH,OAFD,MAGK;AACD,eAAO,CAACT,EAAE,CAACY,UAAX;AACH;AACJ,KATI,MAUA;AACD;AACA;AACA,UAAIC,IAAI,IAAIC,IAAZ,EAAkB;AACd,eAAOd,EAAE,CAACY,UAAV;AACH,OAFD,MAGK;AACD,eAAOZ,EAAE,CAACQ,WAAH,GAAiBR,EAAE,CAACS,WAApB,GAAkCT,EAAE,CAACY,UAA5C;AACH;AACJ;AACJ;;AA9Ie;;AAgJpBtB,aAAa,CAACnE,IAAd;AAAA,mBAA0GmE,aAA1G,EAnV4G5K,EAmV5G,mBAAyIA,EAAE,CAACsM,UAA5I,GAnV4GtM,EAmV5G,mBAAmKmH,gBAAnK,GAnV4GnH,EAmV5G,mBAAgMA,EAAE,CAACsK,MAAnM,GAnV4GtK,EAmV5G,mBAAsNmC,EAAE,CAACoK,cAAzN;AAAA;;AACA3B,aAAa,CAAClE,IAAd,kBApV4G1G,EAoV5G;AAAA,QAA8F4K,aAA9F;AAAA;AAAA;;AACA;AAAA,qDArV4G5K,EAqV5G,mBAA2F4K,aAA3F,EAAsH,CAAC;AAC3G9D,IAAAA,IAAI,EAAE3G,SADqG;AAE3G4G,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE;AADX,KAAD;AAFqG,GAAD,CAAtH,EAK4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAE9G,EAAE,CAACsM;AAAX,KAAD,EAA0B;AAAExF,MAAAA,IAAI,EAAEK;AAAR,KAA1B,EAAsD;AAAEL,MAAAA,IAAI,EAAE9G,EAAE,CAACsK;AAAX,KAAtD,EAA2E;AAAExD,MAAAA,IAAI,EAAE3E,EAAE,CAACoK,cAAX;AAA2B5B,MAAAA,UAAU,EAAE,CAAC;AAChJ7D,QAAAA,IAAI,EAAExG;AAD0I,OAAD;AAAvC,KAA3E,CAAP;AAElB,GAPxB;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMkM,mBAAmB,GAAG,EAA5B;AACA;AACA;AACA;AACA;;AACA,MAAMC,aAAN,CAAoB;AAChB7J,EAAAA,WAAW,CAACyE,SAAD,EAAY0D,MAAZ,EAAoBzD,QAApB,EAA8B;AACrC,SAAKD,SAAL,GAAiBA,SAAjB;AACA;;AACA,SAAKqF,OAAL,GAAe,IAAI3L,OAAJ,EAAf;AACA;;AACA,SAAK4L,eAAL,GAAwBC,KAAD,IAAW;AAC9B,WAAKF,OAAL,CAAaxG,IAAb,CAAkB0G,KAAlB;AACH,KAFD;;AAGA,SAAKhF,SAAL,GAAiBN,QAAjB;AACAyD,IAAAA,MAAM,CAACV,iBAAP,CAAyB,MAAM;AAC3B,UAAIhD,SAAS,CAACqB,SAAd,EAAyB;AACrB,cAAMqB,MAAM,GAAG,KAAKF,UAAL,EAAf,CADqB,CAErB;AACA;;;AACAE,QAAAA,MAAM,CAAC8C,gBAAP,CAAwB,QAAxB,EAAkC,KAAKF,eAAvC;AACA5C,QAAAA,MAAM,CAAC8C,gBAAP,CAAwB,mBAAxB,EAA6C,KAAKF,eAAlD;AACH,OAP0B,CAQ3B;AACA;;;AACA,WAAKG,MAAL,GAAc5E,SAAd,CAAwB,MAAO,KAAK6E,aAAL,GAAqB,IAApD;AACH,KAXD;AAYH;;AACDhE,EAAAA,WAAW,GAAG;AACV,QAAI,KAAK1B,SAAL,CAAeqB,SAAnB,EAA8B;AAC1B,YAAMqB,MAAM,GAAG,KAAKF,UAAL,EAAf;;AACAE,MAAAA,MAAM,CAACiD,mBAAP,CAA2B,QAA3B,EAAqC,KAAKL,eAA1C;AACA5C,MAAAA,MAAM,CAACiD,mBAAP,CAA2B,mBAA3B,EAAgD,KAAKL,eAArD;AACH;;AACD,SAAKD,OAAL,CAAa9I,QAAb;AACH;AACD;;;AACAoB,EAAAA,eAAe,GAAG;AACd,QAAI,CAAC,KAAK+H,aAAV,EAAyB;AACrB,WAAKE,mBAAL;AACH;;AACD,UAAMC,MAAM,GAAG;AAAEC,MAAAA,KAAK,EAAE,KAAKJ,aAAL,CAAmBI,KAA5B;AAAmCC,MAAAA,MAAM,EAAE,KAAKL,aAAL,CAAmBK;AAA9D,KAAf,CAJc,CAKd;;AACA,QAAI,CAAC,KAAK/F,SAAL,CAAeqB,SAApB,EAA+B;AAC3B,WAAKqE,aAAL,GAAqB,IAArB;AACH;;AACD,WAAOG,MAAP;AACH;AACD;;;AACAG,EAAAA,eAAe,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAMC,cAAc,GAAG,KAAKC,yBAAL,EAAvB;AACA,UAAM;AAAEJ,MAAAA,KAAF;AAASC,MAAAA;AAAT,QAAoB,KAAKpI,eAAL,EAA1B;AACA,WAAO;AACH2G,MAAAA,GAAG,EAAE2B,cAAc,CAAC3B,GADjB;AAEHH,MAAAA,IAAI,EAAE8B,cAAc,CAAC9B,IAFlB;AAGHE,MAAAA,MAAM,EAAE4B,cAAc,CAAC3B,GAAf,GAAqByB,MAH1B;AAIH3B,MAAAA,KAAK,EAAE6B,cAAc,CAAC9B,IAAf,GAAsB2B,KAJ1B;AAKHC,MAAAA,MALG;AAMHD,MAAAA;AANG,KAAP;AAQH;AACD;;;AACAI,EAAAA,yBAAyB,GAAG;AACxB;AACA;AACA,QAAI,CAAC,KAAKlG,SAAL,CAAeqB,SAApB,EAA+B;AAC3B,aAAO;AAAEiD,QAAAA,GAAG,EAAE,CAAP;AAAUH,QAAAA,IAAI,EAAE;AAAhB,OAAP;AACH,KALuB,CAMxB;AACA;AACA;AACA;AACA;AACA;;;AACA,UAAMlE,QAAQ,GAAG,KAAKM,SAAtB;;AACA,UAAMmC,MAAM,GAAG,KAAKF,UAAL,EAAf;;AACA,UAAM2D,eAAe,GAAGlG,QAAQ,CAACkG,eAAjC;AACA,UAAMC,YAAY,GAAGD,eAAe,CAACE,qBAAhB,EAArB;AACA,UAAM/B,GAAG,GAAG,CAAC8B,YAAY,CAAC9B,GAAd,IACRrE,QAAQ,CAACqG,IAAT,CAAc1B,SADN,IAERlC,MAAM,CAAC6D,OAFC,IAGRJ,eAAe,CAACvB,SAHR,IAIR,CAJJ;AAKA,UAAMT,IAAI,GAAG,CAACiC,YAAY,CAACjC,IAAd,IACTlE,QAAQ,CAACqG,IAAT,CAAczB,UADL,IAETnC,MAAM,CAAC8D,OAFE,IAGTL,eAAe,CAACtB,UAHP,IAIT,CAJJ;AAKA,WAAO;AAAEP,MAAAA,GAAF;AAAOH,MAAAA;AAAP,KAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIsB,EAAAA,MAAM,CAACgB,YAAY,GAAGtB,mBAAhB,EAAqC;AACvC,WAAOsB,YAAY,GAAG,CAAf,GAAmB,KAAKpB,OAAL,CAAaxJ,IAAb,CAAkB1B,SAAS,CAACsM,YAAD,CAA3B,CAAnB,GAAgE,KAAKpB,OAA5E;AACH;AACD;;;AACA7C,EAAAA,UAAU,GAAG;AACT,WAAO,KAAKjC,SAAL,CAAekC,WAAf,IAA8BC,MAArC;AACH;AACD;;;AACAkD,EAAAA,mBAAmB,GAAG;AAClB,UAAMlD,MAAM,GAAG,KAAKF,UAAL,EAAf;;AACA,SAAKkD,aAAL,GAAqB,KAAK1F,SAAL,CAAeqB,SAAf,GACf;AAAEyE,MAAAA,KAAK,EAAEpD,MAAM,CAACgE,UAAhB;AAA4BX,MAAAA,MAAM,EAAErD,MAAM,CAACiE;AAA3C,KADe,GAEf;AAAEb,MAAAA,KAAK,EAAE,CAAT;AAAYC,MAAAA,MAAM,EAAE;AAApB,KAFN;AAGH;;AAhHe;;AAkHpBX,aAAa,CAAChG,IAAd;AAAA,mBAA0GgG,aAA1G,EA7d4GzM,EA6d5G,UAAyI+B,EAAE,CAACwI,QAA5I,GA7d4GvK,EA6d5G,UAAiKA,EAAE,CAACsK,MAApK,GA7d4GtK,EA6d5G,UAAuLkC,QAAvL;AAAA;;AACAuK,aAAa,CAACjC,KAAd,kBA9d4GxK,EA8d5G;AAAA,SAA8GyM,aAA9G;AAAA,WAA8GA,aAA9G;AAAA,cAAyI;AAAzI;;AACA;AAAA,qDA/d4GzM,EA+d5G,mBAA2FyM,aAA3F,EAAsH,CAAC;AAC3G3F,IAAAA,IAAI,EAAEzG,UADqG;AAE3G0G,IAAAA,IAAI,EAAE,CAAC;AAAE0D,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFqG,GAAD,CAAtH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE3D,MAAAA,IAAI,EAAE/E,EAAE,CAACwI;AAAX,KAAD,EAAwB;AAAEzD,MAAAA,IAAI,EAAE9G,EAAE,CAACsK;AAAX,KAAxB,EAA6C;AAAExD,MAAAA,IAAI,EAAE4D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC1G7D,QAAAA,IAAI,EAAExG;AADoG,OAAD,EAE1G;AACCwG,QAAAA,IAAI,EAAEvG,MADP;AAECwG,QAAAA,IAAI,EAAE,CAAC7E,QAAD;AAFP,OAF0G;AAA/B,KAA7C,CAAP;AAKlB,GARxB;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAAS+L,WAAT,CAAqBC,EAArB,EAAyBC,EAAzB,EAA6B;AACzB,SAAOD,EAAE,CAACrJ,KAAH,IAAYsJ,EAAE,CAACtJ,KAAf,IAAwBqJ,EAAE,CAACpJ,GAAH,IAAUqJ,EAAE,CAACrJ,GAA5C;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,MAAMsJ,gBAAgB,GAAG,OAAOC,qBAAP,KAAiC,WAAjC,GAA+ClN,uBAA/C,GAAyEC,aAAlG;AACA;;AACA,MAAMkN,wBAAN,SAAuC1D,aAAvC,CAAqD;AACjDhI,EAAAA,WAAW,CAACiI,UAAD,EAAa0D,kBAAb,EAAiCxD,MAAjC,EAAyC1E,eAAzC,EAA0D2E,GAA1D,EAA+DF,gBAA/D,EAAiF0D,aAAjF,EAAgG;AACvG,UAAM3D,UAAN,EAAkBC,gBAAlB,EAAoCC,MAApC,EAA4CC,GAA5C;AACA,SAAKH,UAAL,GAAkBA,UAAlB;AACA,SAAK0D,kBAAL,GAA0BA,kBAA1B;AACA,SAAKlI,eAAL,GAAuBA,eAAvB;AACA;;AACA,SAAKoI,gBAAL,GAAwB,IAAI1N,OAAJ,EAAxB;AACA;;AACA,SAAK2N,qBAAL,GAA6B,IAAI3N,OAAJ,EAA7B;AACA,SAAK4N,YAAL,GAAoB,UAApB;AACA,SAAKC,WAAL,GAAmB,KAAnB,CAVuG,CAWvG;AACA;AACA;AACA;;AACA;;AACA,SAAK3L,mBAAL,GAA2B,IAAIhC,UAAJ,CAAgB0H,QAAD,IAAc,KAAKtC,eAAL,CAAqBpD,mBAArB,CAAyCiF,SAAzC,CAAmD7D,KAAK,IAAIwK,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM,KAAKhE,MAAL,CAAYiE,GAAZ,CAAgB,MAAMrG,QAAQ,CAACzC,IAAT,CAAc7B,KAAd,CAAtB,CAA7B,CAA5D,CAA7B,CAA3B;AACA;;AACA,SAAK4K,mBAAL,GAA2B,KAAKP,qBAAhC;AACA;AACR;AACA;;AACQ,SAAKQ,iBAAL,GAAyB,CAAzB;AACA;;AACA,SAAKC,kBAAL,GAA0B,EAA1B;AACA;;AACA,SAAKC,mBAAL,GAA2B,EAA3B;AACA;;AACA,SAAKC,cAAL,GAAsB;AAAExK,MAAAA,KAAK,EAAE,CAAT;AAAYC,MAAAA,GAAG,EAAE;AAAjB,KAAtB;AACA;;AACA,SAAKwK,WAAL,GAAmB,CAAnB;AACA;;AACA,SAAKvC,aAAL,GAAqB,CAArB;AACA;;AACA,SAAKwC,sBAAL,GAA8B,CAA9B;AACA;AACR;AACA;AACA;;AACQ,SAAKC,kCAAL,GAA0C,KAA1C;AACA;;AACA,SAAKC,yBAAL,GAAiC,KAAjC;AACA;;AACA,SAAKC,wBAAL,GAAgC,EAAhC;AACA;;AACA,SAAKC,gBAAL,GAAwBtO,YAAY,CAACuO,KAArC;;AACA,QAAI,CAACvJ,eAAD,KAAqB,OAAOvC,SAAP,KAAqB,WAArB,IAAoCA,SAAzD,CAAJ,EAAyE;AACrE,YAAMC,KAAK,CAAC,gFAAD,CAAX;AACH;;AACD,SAAK4L,gBAAL,GAAwBnB,aAAa,CAAC1B,MAAd,GAAuB5E,SAAvB,CAAiC,MAAM;AAC3D,WAAK2H,iBAAL;AACH,KAFuB,CAAxB;AAGH;AACD;;;AACe,MAAXC,WAAW,GAAG;AACd,WAAO,KAAKnB,YAAZ;AACH;;AACc,MAAXmB,WAAW,CAACA,WAAD,EAAc;AACzB,QAAI,KAAKnB,YAAL,KAAsBmB,WAA1B,EAAuC;AACnC,WAAKnB,YAAL,GAAoBmB,WAApB;;AACA,WAAKC,oBAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACkB,MAAVC,UAAU,GAAG;AACb,WAAO,KAAKpB,WAAZ;AACH;;AACa,MAAVoB,UAAU,CAACzJ,KAAD,EAAQ;AAClB,SAAKqI,WAAL,GAAmB7O,qBAAqB,CAACwG,KAAD,CAAxC;AACH;;AACD4E,EAAAA,QAAQ,GAAG;AACP,UAAMA,QAAN,GADO,CAEP;AACA;AACA;AACA;;AACA,SAAKJ,MAAL,CAAYV,iBAAZ,CAA8B,MAAMwE,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AAC7D,WAAKkB,oBAAL;;AACA,WAAK5J,eAAL,CAAqB9C,MAArB,CAA4B,IAA5B;;AACA,WAAK0E,eAAL,GACK/E,IADL,EAEA;AACAvB,MAAAA,SAAS,CAAC,IAAD,CAHT,EAIA;AACA;AACA;AACAH,MAAAA,SAAS,CAAC,CAAD,EAAI4M,gBAAJ,CAPT,EAQKlG,SARL,CAQe,MAAM,KAAK7B,eAAL,CAAqBrC,iBAArB,EARrB;;AASA,WAAKkM,0BAAL;AACH,KAbmC,CAApC;AAcH;;AACDnH,EAAAA,WAAW,GAAG;AACV,SAAKpF,MAAL;;AACA,SAAK0C,eAAL,CAAqB1C,MAArB,GAFU,CAGV;;;AACA,SAAK+K,qBAAL,CAA2B9K,QAA3B;;AACA,SAAK6K,gBAAL,CAAsB7K,QAAtB;;AACA,SAAK+L,gBAAL,CAAsBrH,WAAtB;;AACA,UAAMS,WAAN;AACH;AACD;;;AACAxF,EAAAA,MAAM,CAAC4M,KAAD,EAAQ;AACV,QAAI,KAAKC,MAAL,KAAgB,OAAOtM,SAAP,KAAqB,WAArB,IAAoCA,SAApD,CAAJ,EAAoE;AAChE,YAAMC,KAAK,CAAC,+CAAD,CAAX;AACH,KAHS,CAIV;AACA;AACA;;;AACA,SAAKgH,MAAL,CAAYV,iBAAZ,CAA8B,MAAM;AAChC,WAAK+F,MAAL,GAAcD,KAAd;;AACA,WAAKC,MAAL,CAAYC,UAAZ,CAAuBnN,IAAvB,CAA4BxB,SAAS,CAAC,KAAK+M,gBAAN,CAArC,EAA8DvG,SAA9D,CAAwEoI,IAAI,IAAI;AAC5E,cAAMC,SAAS,GAAGD,IAAI,CAACE,MAAvB;;AACA,YAAID,SAAS,KAAK,KAAKjB,WAAvB,EAAoC;AAChC,eAAKA,WAAL,GAAmBiB,SAAnB;;AACA,eAAKlK,eAAL,CAAqBpC,mBAArB;AACH;;AACD,aAAKwM,kBAAL;AACH,OAPD;AAQH,KAVD;AAWH;AACD;;;AACA9M,EAAAA,MAAM,GAAG;AACL,SAAKyM,MAAL,GAAc,IAAd;;AACA,SAAK3B,gBAAL,CAAsBvI,IAAtB;AACH;AACD;;;AACAzB,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAK6K,WAAZ;AACH;AACD;;;AACAtK,EAAAA,eAAe,GAAG;AACd,WAAO,KAAK+H,aAAZ;AACH,GAxIgD,CAyIjD;AACA;AACA;AACA;;AACA;;;AACApI,EAAAA,gBAAgB,GAAG;AACf,WAAO,KAAK0K,cAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACI7K,EAAAA,mBAAmB,CAACkM,IAAD,EAAO;AACtB,QAAI,KAAKxB,iBAAL,KAA2BwB,IAA/B,EAAqC;AACjC,WAAKxB,iBAAL,GAAyBwB,IAAzB;;AACA,WAAKX,oBAAL;;AACA,WAAKG,0BAAL;AACH;AACJ;AACD;;;AACAlK,EAAAA,gBAAgB,CAAC2K,KAAD,EAAQ;AACpB,QAAI,CAAC1C,WAAW,CAAC,KAAKoB,cAAN,EAAsBsB,KAAtB,CAAhB,EAA8C;AAC1C,UAAI,KAAKX,UAAT,EAAqB;AACjBW,QAAAA,KAAK,GAAG;AAAE9L,UAAAA,KAAK,EAAE,CAAT;AAAYC,UAAAA,GAAG,EAAEQ,IAAI,CAACG,GAAL,CAAS,KAAK4J,cAAL,CAAoBvK,GAA7B,EAAkC6L,KAAK,CAAC7L,GAAxC;AAAjB,SAAR;AACH;;AACD,WAAK4J,qBAAL,CAA2BxI,IAA3B,CAAiC,KAAKmJ,cAAL,GAAsBsB,KAAvD;;AACA,WAAKT,0BAAL,CAAgC,MAAM,KAAK7J,eAAL,CAAqBnC,iBAArB,EAAtC;AACH;AACJ;AACD;AACJ;AACA;;;AACI0M,EAAAA,+BAA+B,GAAG;AAC9B,WAAO,KAAKpB,kCAAL,GAA0C,IAA1C,GAAiD,KAAKD,sBAA7D;AACH;AACD;AACJ;AACA;AACA;;;AACItJ,EAAAA,wBAAwB,CAAC4K,MAAD,EAASC,EAAE,GAAG,UAAd,EAA0B;AAC9C;AACA;AACA,UAAMvF,KAAK,GAAG,KAAKP,GAAL,IAAY,KAAKA,GAAL,CAASzE,KAAT,IAAkB,KAA5C;AACA,UAAMwK,YAAY,GAAG,KAAKjB,WAAL,IAAoB,YAAzC;AACA,UAAMkB,IAAI,GAAGD,YAAY,GAAG,GAAH,GAAS,GAAlC;AACA,UAAME,aAAa,GAAGF,YAAY,IAAIxF,KAAhB,GAAwB,CAAC,CAAzB,GAA6B,CAAnD;AACA,QAAI2F,SAAS,GAAI,YAAWF,IAAK,IAAGG,MAAM,CAACF,aAAa,GAAGJ,MAAjB,CAAyB,KAAnE,CAP8C,CAQ9C;;AACAA,IAAAA,MAAM,GAAG,KAAKb,UAAL,IAAmBc,EAAE,KAAK,UAA1B,GAAuC,CAAvC,GAA2CD,MAApD;AACA,SAAKtB,sBAAL,GAA8BsB,MAA9B;;AACA,QAAIC,EAAE,KAAK,QAAX,EAAqB;AACjBI,MAAAA,SAAS,IAAK,aAAYF,IAAK,SAA/B,CADiB,CAEjB;AACA;AACA;;AACA,WAAKxB,kCAAL,GAA0C,IAA1C;AACH;;AACD,QAAI,KAAK4B,yBAAL,IAAkCF,SAAtC,EAAiD;AAC7C;AACA;AACA,WAAKE,yBAAL,GAAiCF,SAAjC;;AACA,WAAKhB,0BAAL,CAAgC,MAAM;AAClC,YAAI,KAAKV,kCAAT,EAA6C;AACzC,eAAKD,sBAAL,IAA+B,KAAK8B,0BAAL,EAA/B;AACA,eAAK7B,kCAAL,GAA0C,KAA1C;AACA,eAAKvJ,wBAAL,CAA8B,KAAKsJ,sBAAnC;AACH,SAJD,MAKK;AACD,eAAKlJ,eAAL,CAAqBlC,uBAArB;AACH;AACJ,OATD;AAUH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACII,EAAAA,cAAc,CAACsM,MAAD,EAASvM,QAAQ,GAAG,MAApB,EAA4B;AACtC,UAAM+G,OAAO,GAAG;AAAE/G,MAAAA;AAAF,KAAhB;;AACA,QAAI,KAAKwL,WAAL,KAAqB,YAAzB,EAAuC;AACnCzE,MAAAA,OAAO,CAACxG,KAAR,GAAgBgM,MAAhB;AACH,KAFD,MAGK;AACDxF,MAAAA,OAAO,CAACM,GAAR,GAAckF,MAAd;AACH;;AACD,SAAKzF,QAAL,CAAcC,OAAd;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIjH,EAAAA,aAAa,CAACC,KAAD,EAAQC,QAAQ,GAAG,MAAnB,EAA2B;AACpC,SAAK+B,eAAL,CAAqBjC,aAArB,CAAmCC,KAAnC,EAA0CC,QAA1C;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIa,EAAAA,mBAAmB,CAACgH,IAAD,EAAO;AACtB,WAAOA,IAAI,GACL,MAAMhH,mBAAN,CAA0BgH,IAA1B,CADK,GAEL,MAAMhH,mBAAN,CAA0B,KAAK2K,WAAL,KAAqB,YAArB,GAAoC,OAApC,GAA8C,KAAxE,CAFN;AAGH;AACD;;;AACAuB,EAAAA,0BAA0B,GAAG;AACzB,UAAMC,SAAS,GAAG,KAAKC,eAAL,CAAqBpH,aAAvC;AACA,WAAO,KAAK2F,WAAL,KAAqB,YAArB,GAAoCwB,SAAS,CAACE,WAA9C,GAA4DF,SAAS,CAACG,YAA7E;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,gBAAgB,CAACf,KAAD,EAAQ;AACpB,QAAI,CAAC,KAAKP,MAAV,EAAkB;AACd,aAAO,CAAP;AACH;;AACD,WAAO,KAAKA,MAAL,CAAYsB,gBAAZ,CAA6Bf,KAA7B,EAAoC,KAAKb,WAAzC,CAAP;AACH;AACD;;;AACAD,EAAAA,iBAAiB,GAAG;AAChB;AACA,SAAKI,oBAAL;;AACA,SAAK5J,eAAL,CAAqBpC,mBAArB;AACH;AACD;;;AACAgM,EAAAA,oBAAoB,GAAG;AACnB,UAAM0B,UAAU,GAAG,KAAK9G,UAAL,CAAgBV,aAAnC;AACA,SAAK4C,aAAL,GACI,KAAK+C,WAAL,KAAqB,YAArB,GAAoC6B,UAAU,CAAC5F,WAA/C,GAA6D4F,UAAU,CAAC9F,YAD5E;AAEH;AACD;;;AACAqE,EAAAA,0BAA0B,CAAC0B,QAAD,EAAW;AACjC,QAAIA,QAAJ,EAAc;AACV,WAAKlC,wBAAL,CAA8B9F,IAA9B,CAAmCgI,QAAnC;AACH,KAHgC,CAIjC;AACA;;;AACA,QAAI,CAAC,KAAKnC,yBAAV,EAAqC;AACjC,WAAKA,yBAAL,GAAiC,IAAjC;AACA,WAAK1E,MAAL,CAAYV,iBAAZ,CAA8B,MAAMwE,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AAC7D,aAAK0B,kBAAL;AACH,OAFmC,CAApC;AAGH;AACJ;AACD;;;AACAA,EAAAA,kBAAkB,GAAG;AACjB,SAAKhB,yBAAL,GAAiC,KAAjC,CADiB,CAEjB;AACA;AACA;AACA;;AACA,SAAK8B,eAAL,CAAqBpH,aAArB,CAAmC0H,KAAnC,CAAyCX,SAAzC,GAAqD,KAAKE,yBAA1D,CANiB,CAOjB;AACA;AACA;;AACA,SAAKrG,MAAL,CAAYiE,GAAZ,CAAgB,MAAM,KAAKT,kBAAL,CAAwBuD,YAAxB,EAAtB;AACA,UAAMC,uBAAuB,GAAG,KAAKrC,wBAArC;AACA,SAAKA,wBAAL,GAAgC,EAAhC;;AACA,SAAK,MAAMsC,EAAX,IAAiBD,uBAAjB,EAA0C;AACtCC,MAAAA,EAAE;AACL;AACJ;AACD;;;AACAjC,EAAAA,oBAAoB,GAAG;AACnB,SAAKX,mBAAL,GACI,KAAKU,WAAL,KAAqB,YAArB,GAAoC,EAApC,GAA0C,GAAE,KAAKZ,iBAAkB,IADvE;AAEA,SAAKC,kBAAL,GACI,KAAKW,WAAL,KAAqB,YAArB,GAAqC,GAAE,KAAKZ,iBAAkB,IAA9D,GAAoE,EADxE;AAEH;;AAtTgD;;AAwTrDZ,wBAAwB,CAAC7H,IAAzB;AAAA,mBAAqH6H,wBAArH,EAnzB4GtO,EAmzB5G,mBAA+JA,EAAE,CAACsM,UAAlK,GAnzB4GtM,EAmzB5G,mBAAyLA,EAAE,CAACiS,iBAA5L,GAnzB4GjS,EAmzB5G,mBAA0NA,EAAE,CAACsK,MAA7N,GAnzB4GtK,EAmzB5G,mBAAgP0C,uBAAhP,MAnzB4G1C,EAmzB5G,mBAAoSmC,EAAE,CAACoK,cAAvS,MAnzB4GvM,EAmzB5G,mBAAkVmH,gBAAlV,GAnzB4GnH,EAmzB5G,mBAA+WyM,aAA/W;AAAA;;AACA6B,wBAAwB,CAAC4D,IAAzB,kBApzB4GlS,EAozB5G;AAAA,QAAyGsO,wBAAzG;AAAA;AAAA;AAAA;AApzB4GtO,MAAAA,EAozB5G;AAAA;;AAAA;AAAA;;AApzB4GA,MAAAA,EAozB5G,qBApzB4GA,EAozB5G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AApzB4GA,MAAAA,EAozB5G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aApzB4GA,EAozB5G,oBAAmiB,CAC3hB;AACI2G,IAAAA,OAAO,EAAEiE,aADb;AAEIuH,IAAAA,WAAW,EAAE7D;AAFjB,GAD2hB,CAAniB,GApzB4GtO,EAozB5G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AApzB4GA,MAAAA,EAozB5G;AApzB4GA,MAAAA,EAyzBkM,+BAL9S;AApzB4GA,MAAAA,EAyzBwQ,gBALpX;AApzB4GA,MAAAA,EAyzBmS,eAL/Y;AApzB4GA,MAAAA,EAyzB6d,uBALzkB;AAAA;;AAAA;AApzB4GA,MAAAA,EAyzB4gB,aALxnB;AApzB4GA,MAAAA,EAyzB4gB,gFALxnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAMA;AAAA,qDA1zB4GA,EA0zB5G,mBAA2FsO,wBAA3F,EAAiI,CAAC;AACtHxH,IAAAA,IAAI,EAAEtG,SADgH;AAEtHuG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE,6BAAZ;AAA2CoL,MAAAA,IAAI,EAAE;AAC5C,iBAAS,6BADmC;AAE5C,6DAAqD,8BAFT;AAG5C,2DAAmD;AAHP,OAAjD;AAIIC,MAAAA,aAAa,EAAE5R,iBAAiB,CAAC6R,IAJrC;AAI2CC,MAAAA,eAAe,EAAE7R,uBAAuB,CAAC8R,MAJpF;AAI4FvL,MAAAA,SAAS,EAAE,CAClG;AACIN,QAAAA,OAAO,EAAEiE,aADb;AAEIuH,QAAAA,WAAW,EAAE7D;AAFjB,OADkG,CAJvG;AASImE,MAAAA,QAAQ,EAAE,shBATd;AASsiBC,MAAAA,MAAM,EAAE,CAAC,6sDAAD;AAT9iB,KAAD;AAFgH,GAAD,CAAjI,EAY4B,YAAY;AAAE,WAAO,CAAC;AAAE5L,MAAAA,IAAI,EAAE9G,EAAE,CAACsM;AAAX,KAAD,EAA0B;AAAExF,MAAAA,IAAI,EAAE9G,EAAE,CAACiS;AAAX,KAA1B,EAA0D;AAAEnL,MAAAA,IAAI,EAAE9G,EAAE,CAACsK;AAAX,KAA1D,EAA+E;AAAExD,MAAAA,IAAI,EAAE4D,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC5I7D,QAAAA,IAAI,EAAExG;AADsI,OAAD,EAE5I;AACCwG,QAAAA,IAAI,EAAEvG,MADP;AAECwG,QAAAA,IAAI,EAAE,CAACrE,uBAAD;AAFP,OAF4I;AAA/B,KAA/E,EAK3B;AAAEoE,MAAAA,IAAI,EAAE3E,EAAE,CAACoK,cAAX;AAA2B5B,MAAAA,UAAU,EAAE,CAAC;AAC1C7D,QAAAA,IAAI,EAAExG;AADoC,OAAD;AAAvC,KAL2B,EAO3B;AAAEwG,MAAAA,IAAI,EAAEK;AAAR,KAP2B,EAOC;AAAEL,MAAAA,IAAI,EAAE2F;AAAR,KAPD,CAAP;AAOmC,GAnB7E,EAmB+F;AAAEqD,IAAAA,WAAW,EAAE,CAAC;AAC/FhJ,MAAAA,IAAI,EAAE1G;AADyF,KAAD,CAAf;AAE/E4P,IAAAA,UAAU,EAAE,CAAC;AACblJ,MAAAA,IAAI,EAAE1G;AADO,KAAD,CAFmE;AAI/E6C,IAAAA,mBAAmB,EAAE,CAAC;AACtB6D,MAAAA,IAAI,EAAEnG;AADgB,KAAD,CAJ0D;AAM/E4Q,IAAAA,eAAe,EAAE,CAAC;AAClBzK,MAAAA,IAAI,EAAElG,SADY;AAElBmG,MAAAA,IAAI,EAAE,CAAC,gBAAD,EAAmB;AAAE4L,QAAAA,MAAM,EAAE;AAAV,OAAnB;AAFY,KAAD;AAN8D,GAnB/F;AAAA;AA8BA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASC,SAAT,CAAmB9C,WAAnB,EAAgC+C,SAAhC,EAA2CC,IAA3C,EAAiD;AAC7C,QAAMxH,EAAE,GAAGwH,IAAX;;AACA,MAAI,CAACxH,EAAE,CAACoC,qBAAR,EAA+B;AAC3B,WAAO,CAAP;AACH;;AACD,QAAMqF,IAAI,GAAGzH,EAAE,CAACoC,qBAAH,EAAb;;AACA,MAAIoC,WAAW,KAAK,YAApB,EAAkC;AAC9B,WAAO+C,SAAS,KAAK,OAAd,GAAwBE,IAAI,CAACvH,IAA7B,GAAoCuH,IAAI,CAACtH,KAAhD;AACH;;AACD,SAAOoH,SAAS,KAAK,OAAd,GAAwBE,IAAI,CAACpH,GAA7B,GAAmCoH,IAAI,CAACrH,MAA/C;AACH;AACD;AACA;AACA;AACA;;;AACA,MAAMsH,eAAN,CAAsB;AAClBpQ,EAAAA,WAAW;AACX;AACAqQ,EAAAA,iBAFW;AAGX;AACAC,EAAAA,SAJW;AAKX;AACAC,EAAAA,QANW;AAOX;AACAC,EAAAA,aARW;AASX;AACAjQ,EAAAA,SAVW,EAUA4H,MAVA,EAUQ;AACf,SAAKkI,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,aAAL,GAAqBA,aAArB;AACA,SAAKjQ,SAAL,GAAiBA,SAAjB;AACA;;AACA,SAAKkQ,UAAL,GAAkB,IAAItS,OAAJ,EAAlB;AACA;;AACA,SAAKuS,kBAAL,GAA0B,IAAIvS,OAAJ,EAA1B;AACA;;AACA,SAAKsP,UAAL,GAAkB,KAAKiD,kBAAL,CAAwBpQ,IAAxB,EAClB;AACAvB,IAAAA,SAAS,CAAC,IAAD,CAFS,EAGlB;AACAC,IAAAA,QAAQ,EAJU,EAKlB;AACA;AACA;AACAC,IAAAA,SAAS,CAAC,CAAC,CAAC0R,IAAD,EAAOC,GAAP,CAAD,KAAiB,KAAKC,iBAAL,CAAuBF,IAAvB,EAA6BC,GAA7B,CAAlB,CARS,EASlB;AACA1R,IAAAA,WAAW,CAAC,CAAD,CAVO,CAAlB;AAWA;;AACA,SAAK4R,OAAL,GAAe,IAAf;AACA;;AACA,SAAKC,YAAL,GAAoB,KAApB;AACA,SAAK1I,UAAL,GAAkB,IAAIlK,OAAJ,EAAlB;AACA,SAAKsP,UAAL,CAAgBnI,SAAhB,CAA0BoI,IAAI,IAAI;AAC9B,WAAKsD,KAAL,GAAatD,IAAb;;AACA,WAAKuD,qBAAL;AACH,KAHD;;AAIA,SAAK1Q,SAAL,CAAe8L,mBAAf,CAAmC/L,IAAnC,CAAwCxB,SAAS,CAAC,KAAKuJ,UAAN,CAAjD,EAAoE/C,SAApE,CAA8EyI,KAAK,IAAI;AACnF,WAAKtB,cAAL,GAAsBsB,KAAtB;;AACA,UAAI,KAAK0C,UAAL,CAAgBS,SAAhB,CAA0BtD,MAA9B,EAAsC;AAClCzF,QAAAA,MAAM,CAACiE,GAAP,CAAW,MAAM,KAAKqE,UAAL,CAAgBnN,IAAhB,CAAqB,KAAKmJ,cAA1B,CAAjB;AACH;;AACD,WAAKwE,qBAAL;AACH,KAND;;AAOA,SAAK1Q,SAAL,CAAeI,MAAf,CAAsB,IAAtB;AACH;AACD;;;AACmB,MAAfwQ,eAAe,GAAG;AAClB,WAAO,KAAKC,gBAAZ;AACH;;AACkB,MAAfD,eAAe,CAACxN,KAAD,EAAQ;AACvB,SAAKyN,gBAAL,GAAwBzN,KAAxB;;AACA,QAAIjE,YAAY,CAACiE,KAAD,CAAhB,EAAyB;AACrB,WAAK+M,kBAAL,CAAwBpN,IAAxB,CAA6BK,KAA7B;AACH,KAFD,MAGK;AACD;AACA,WAAK+M,kBAAL,CAAwBpN,IAAxB,CAA6B,IAAI3D,eAAJ,CAAoBjB,YAAY,CAACiF,KAAD,CAAZ,GAAsBA,KAAtB,GAA8B0N,KAAK,CAAC9H,IAAN,CAAW5F,KAAK,IAAI,EAApB,CAAlD,CAA7B;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AAC4B,MAApB2N,oBAAoB,GAAG;AACvB,WAAO,KAAKC,qBAAZ;AACH;;AACuB,MAApBD,oBAAoB,CAAClC,EAAD,EAAK;AACzB,SAAK2B,YAAL,GAAoB,IAApB;AACA,SAAKQ,qBAAL,GAA6BnC,EAAE,GACzB,CAAC3N,KAAD,EAAQ+P,IAAR,KAAiBpC,EAAE,CAAC3N,KAAK,IAAI,KAAKgL,cAAL,GAAsB,KAAKA,cAAL,CAAoBxK,KAA1C,GAAkD,CAAtD,CAAN,EAAgEuP,IAAhE,CADM,GAEzB1J,SAFN;AAGH;AACD;;;AACyB,MAArB2J,qBAAqB,CAAC9N,KAAD,EAAQ;AAC7B,QAAIA,KAAJ,EAAW;AACP,WAAKoN,YAAL,GAAoB,IAApB;AACA,WAAKT,SAAL,GAAiB3M,KAAjB;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACsC,MAA9B+N,8BAA8B,GAAG;AACjC,WAAO,KAAKlB,aAAL,CAAmBmB,aAA1B;AACH;;AACiC,MAA9BD,8BAA8B,CAAC5D,IAAD,EAAO;AACrC,SAAK0C,aAAL,CAAmBmB,aAAnB,GAAmC1U,oBAAoB,CAAC6Q,IAAD,CAAvD;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIgB,EAAAA,gBAAgB,CAACf,KAAD,EAAQb,WAAR,EAAqB;AACjC,QAAIa,KAAK,CAAC9L,KAAN,IAAe8L,KAAK,CAAC7L,GAAzB,EAA8B;AAC1B,aAAO,CAAP;AACH;;AACD,QAAI,CAAC6L,KAAK,CAAC9L,KAAN,GAAc,KAAKwK,cAAL,CAAoBxK,KAAlC,IAA2C8L,KAAK,CAAC7L,GAAN,GAAY,KAAKuK,cAAL,CAAoBvK,GAA5E,MACC,OAAOhB,SAAP,KAAqB,WAArB,IAAoCA,SADrC,CAAJ,EACqD;AACjD,YAAMC,KAAK,CAAE,0DAAF,CAAX;AACH,KAPgC,CAQjC;;;AACA,UAAMyQ,kBAAkB,GAAG7D,KAAK,CAAC9L,KAAN,GAAc,KAAKwK,cAAL,CAAoBxK,KAA7D,CATiC,CAUjC;;AACA,UAAM4P,QAAQ,GAAG9D,KAAK,CAAC7L,GAAN,GAAY6L,KAAK,CAAC9L,KAAnC,CAXiC,CAYjC;AACA;;AACA,QAAI6P,SAAJ;AACA,QAAIC,QAAJ,CAfiC,CAgBjC;;AACA,SAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,QAApB,EAA8BG,CAAC,EAA/B,EAAmC;AAC/B,YAAMC,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2BuM,CAAC,GAAGJ,kBAA/B,CAAb;;AACA,UAAIK,IAAI,IAAIA,IAAI,CAACC,SAAL,CAAetE,MAA3B,EAAmC;AAC/BkE,QAAAA,SAAS,GAAGC,QAAQ,GAAGE,IAAI,CAACC,SAAL,CAAe,CAAf,CAAvB;AACA;AACH;AACJ,KAvBgC,CAwBjC;;;AACA,SAAK,IAAIF,CAAC,GAAGH,QAAQ,GAAG,CAAxB,EAA2BG,CAAC,GAAG,CAAC,CAAhC,EAAmCA,CAAC,EAApC,EAAwC;AACpC,YAAMC,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2BuM,CAAC,GAAGJ,kBAA/B,CAAb;;AACA,UAAIK,IAAI,IAAIA,IAAI,CAACC,SAAL,CAAetE,MAA3B,EAAmC;AAC/BmE,QAAAA,QAAQ,GAAGE,IAAI,CAACC,SAAL,CAAeD,IAAI,CAACC,SAAL,CAAetE,MAAf,GAAwB,CAAvC,CAAX;AACA;AACH;AACJ;;AACD,WAAOkE,SAAS,IAAIC,QAAb,GACD/B,SAAS,CAAC9C,WAAD,EAAc,KAAd,EAAqB6E,QAArB,CAAT,GAA0C/B,SAAS,CAAC9C,WAAD,EAAc,OAAd,EAAuB4E,SAAvB,CADlD,GAED,CAFN;AAGH;;AACDK,EAAAA,SAAS,GAAG;AACR,QAAI,KAAKrB,OAAL,IAAgB,KAAKC,YAAzB,EAAuC;AACnC;AACA;AACA;AACA,YAAMqB,OAAO,GAAG,KAAKtB,OAAL,CAAauB,IAAb,CAAkB,KAAKC,cAAvB,CAAhB;;AACA,UAAI,CAACF,OAAL,EAAc;AACV,aAAKG,cAAL;AACH,OAFD,MAGK;AACD,aAAKC,aAAL,CAAmBJ,OAAnB;AACH;;AACD,WAAKrB,YAAL,GAAoB,KAApB;AACH;AACJ;;AACD5K,EAAAA,WAAW,GAAG;AACV,SAAK5F,SAAL,CAAeQ,MAAf;;AACA,SAAK2P,kBAAL,CAAwBpN,IAAxB,CAA6BwE,SAA7B;;AACA,SAAK4I,kBAAL,CAAwB1P,QAAxB;;AACA,SAAKyP,UAAL,CAAgBzP,QAAhB;;AACA,SAAKqH,UAAL,CAAgB/E,IAAhB;;AACA,SAAK+E,UAAL,CAAgBrH,QAAhB;;AACA,SAAKwP,aAAL,CAAmBzP,MAAnB;AACH;AACD;;;AACAkQ,EAAAA,qBAAqB,GAAG;AACpB,QAAI,CAAC,KAAKxE,cAAV,EAA0B;AACtB;AACH;;AACD,SAAK6F,cAAL,GAAsB,KAAKtB,KAAL,CAAWyB,KAAX,CAAiB,KAAKhG,cAAL,CAAoBxK,KAArC,EAA4C,KAAKwK,cAAL,CAAoBvK,GAAhE,CAAtB;;AACA,QAAI,CAAC,KAAK4O,OAAV,EAAmB;AACf;AACA;AACA,WAAKA,OAAL,GAAe,KAAKP,QAAL,CAAcmC,IAAd,CAAmB,KAAKJ,cAAxB,EAAwCK,MAAxC,CAA+C,CAAClR,KAAD,EAAQ+P,IAAR,KAAiB;AAC3E,eAAO,KAAKF,oBAAL,GAA4B,KAAKA,oBAAL,CAA0B7P,KAA1B,EAAiC+P,IAAjC,CAA5B,GAAqEA,IAA5E;AACH,OAFc,CAAf;AAGH;;AACD,SAAKT,YAAL,GAAoB,IAApB;AACH;AACD;;;AACAF,EAAAA,iBAAiB,CAAC+B,KAAD,EAAQC,KAAR,EAAe;AAC5B,QAAID,KAAJ,EAAW;AACPA,MAAAA,KAAK,CAACE,UAAN,CAAiB,IAAjB;AACH;;AACD,SAAK/B,YAAL,GAAoB,IAApB;AACA,WAAO8B,KAAK,GAAGA,KAAK,CAACE,OAAN,CAAc,IAAd,CAAH,GAAyB3U,EAAE,EAAvC;AACH;AACD;;;AACAmU,EAAAA,cAAc,GAAG;AACb,UAAMS,KAAK,GAAG,KAAKhC,KAAL,CAAWpD,MAAzB;AACA,QAAIoE,CAAC,GAAG,KAAK3B,iBAAL,CAAuBzC,MAA/B;;AACA,WAAOoE,CAAC,EAAR,EAAY;AACR,YAAMC,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2BuM,CAA3B,CAAb;;AACAC,MAAAA,IAAI,CAACgB,OAAL,CAAaxR,KAAb,GAAqB,KAAKgL,cAAL,CAAoBxK,KAApB,GAA4B+P,CAAjD;AACAC,MAAAA,IAAI,CAACgB,OAAL,CAAaD,KAAb,GAAqBA,KAArB;;AACA,WAAKE,gCAAL,CAAsCjB,IAAI,CAACgB,OAA3C;;AACAhB,MAAAA,IAAI,CAACkB,aAAL;AACH;AACJ;AACD;;;AACAX,EAAAA,aAAa,CAACJ,OAAD,EAAU;AACnB,SAAK5B,aAAL,CAAmB4C,YAAnB,CAAgChB,OAAhC,EAAyC,KAAK/B,iBAA9C,EAAiE,CAACgD,MAAD,EAASC,sBAAT,EAAiCC,YAAjC,KAAkD,KAAKC,oBAAL,CAA0BH,MAA1B,EAAkCE,YAAlC,CAAnH,EAAoKF,MAAM,IAAIA,MAAM,CAAC7B,IAArL,EADmB,CAEnB;;;AACAY,IAAAA,OAAO,CAACqB,qBAAR,CAA+BJ,MAAD,IAAY;AACtC,YAAMpB,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2B4N,MAAM,CAACE,YAAlC,CAAb;;AACAtB,MAAAA,IAAI,CAACgB,OAAL,CAAaS,SAAb,GAAyBL,MAAM,CAAC7B,IAAhC;AACH,KAHD,EAHmB,CAOnB;;AACA,UAAMwB,KAAK,GAAG,KAAKhC,KAAL,CAAWpD,MAAzB;AACA,QAAIoE,CAAC,GAAG,KAAK3B,iBAAL,CAAuBzC,MAA/B;;AACA,WAAOoE,CAAC,EAAR,EAAY;AACR,YAAMC,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2BuM,CAA3B,CAAb;;AACAC,MAAAA,IAAI,CAACgB,OAAL,CAAaxR,KAAb,GAAqB,KAAKgL,cAAL,CAAoBxK,KAApB,GAA4B+P,CAAjD;AACAC,MAAAA,IAAI,CAACgB,OAAL,CAAaD,KAAb,GAAqBA,KAArB;;AACA,WAAKE,gCAAL,CAAsCjB,IAAI,CAACgB,OAA3C;AACH;AACJ;AACD;;;AACAC,EAAAA,gCAAgC,CAACD,OAAD,EAAU;AACtCA,IAAAA,OAAO,CAACU,KAAR,GAAgBV,OAAO,CAACxR,KAAR,KAAkB,CAAlC;AACAwR,IAAAA,OAAO,CAACW,IAAR,GAAeX,OAAO,CAACxR,KAAR,KAAkBwR,OAAO,CAACD,KAAR,GAAgB,CAAjD;AACAC,IAAAA,OAAO,CAACY,IAAR,GAAeZ,OAAO,CAACxR,KAAR,GAAgB,CAAhB,KAAsB,CAArC;AACAwR,IAAAA,OAAO,CAACa,GAAR,GAAc,CAACb,OAAO,CAACY,IAAvB;AACH;;AACDL,EAAAA,oBAAoB,CAACH,MAAD,EAAS5R,KAAT,EAAgB;AAChC;AACA;AACA;AACA;AACA,WAAO;AACHsS,MAAAA,WAAW,EAAE,KAAKzD,SADf;AAEH2C,MAAAA,OAAO,EAAE;AACLS,QAAAA,SAAS,EAAEL,MAAM,CAAC7B,IADb;AAEL;AACA;AACAL,QAAAA,eAAe,EAAE,KAAKC,gBAJjB;AAKL3P,QAAAA,KAAK,EAAE,CAAC,CALH;AAMLuR,QAAAA,KAAK,EAAE,CAAC,CANH;AAOLW,QAAAA,KAAK,EAAE,KAPF;AAQLC,QAAAA,IAAI,EAAE,KARD;AASLE,QAAAA,GAAG,EAAE,KATA;AAULD,QAAAA,IAAI,EAAE;AAVD,OAFN;AAcHpS,MAAAA;AAdG,KAAP;AAgBH;;AAjPiB;;AAmPtB2O,eAAe,CAACvM,IAAhB;AAAA,mBAA4GuM,eAA5G,EAlmC4GhT,EAkmC5G,mBAA6IA,EAAE,CAAC4W,gBAAhJ,GAlmC4G5W,EAkmC5G,mBAA6KA,EAAE,CAAC6W,WAAhL,GAlmC4G7W,EAkmC5G,mBAAwMA,EAAE,CAAC8W,eAA3M,GAlmC4G9W,EAkmC5G,mBAAuOwC,uBAAvO,GAlmC4GxC,EAkmC5G,mBAA2QsO,wBAA3Q,MAlmC4GtO,EAkmC5G,mBAAgUA,EAAE,CAACsK,MAAnU;AAAA;;AACA0I,eAAe,CAACtM,IAAhB,kBAnmC4G1G,EAmmC5G;AAAA,QAAgGgT,eAAhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAnmC4GhT,EAmmC5G,oBAA0X,CAAC;AAAE2G,IAAAA,OAAO,EAAEnE,uBAAX;AAAoCuU,IAAAA,QAAQ,EAAEtU;AAA9C,GAAD,CAA1X;AAAA;;AACA;AAAA,qDApmC4GzC,EAomC5G,mBAA2FgT,eAA3F,EAAwH,CAAC;AAC7GlM,IAAAA,IAAI,EAAE3G,SADuG;AAE7G4G,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,kCADX;AAECC,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAEnE,uBAAX;AAAoCuU,QAAAA,QAAQ,EAAEtU;AAA9C,OAAD;AAFZ,KAAD;AAFuG,GAAD,CAAxH,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAEqE,MAAAA,IAAI,EAAE9G,EAAE,CAAC4W;AAAX,KAAD,EAAgC;AAAE9P,MAAAA,IAAI,EAAE9G,EAAE,CAAC6W;AAAX,KAAhC,EAA0D;AAAE/P,MAAAA,IAAI,EAAE9G,EAAE,CAAC8W;AAAX,KAA1D,EAAwF;AAAEhQ,MAAAA,IAAI,EAAEzE,IAAI,CAACI,4BAAb;AAA2CkI,MAAAA,UAAU,EAAE,CAAC;AAC7K7D,QAAAA,IAAI,EAAEvG,MADuK;AAE7KwG,QAAAA,IAAI,EAAE,CAACvE,uBAAD;AAFuK,OAAD;AAAvD,KAAxF,EAG3B;AAAEsE,MAAAA,IAAI,EAAEwH,wBAAR;AAAkC3D,MAAAA,UAAU,EAAE,CAAC;AACjD7D,QAAAA,IAAI,EAAEjG;AAD2C,OAAD;AAA9C,KAH2B,EAK3B;AAAEiG,MAAAA,IAAI,EAAE9G,EAAE,CAACsK;AAAX,KAL2B,CAAP;AAKG,GAX7C,EAW+D;AAAEyJ,IAAAA,eAAe,EAAE,CAAC;AACnEjN,MAAAA,IAAI,EAAE1G;AAD6D,KAAD,CAAnB;AAE/C8T,IAAAA,oBAAoB,EAAE,CAAC;AACvBpN,MAAAA,IAAI,EAAE1G;AADiB,KAAD,CAFyB;AAI/CiU,IAAAA,qBAAqB,EAAE,CAAC;AACxBvN,MAAAA,IAAI,EAAE1G;AADkB,KAAD,CAJwB;AAM/CkU,IAAAA,8BAA8B,EAAE,CAAC;AACjCxN,MAAAA,IAAI,EAAE1G;AAD2B,KAAD;AANe,GAX/D;AAAA;AAqBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM4W,mBAAN,CAA0B;;AAE1BA,mBAAmB,CAACvQ,IAApB;AAAA,mBAAgHuQ,mBAAhH;AAAA;;AACAA,mBAAmB,CAACC,IAApB,kBAnoC4GjX,EAmoC5G;AAAA,QAAiHgX;AAAjH;AACAA,mBAAmB,CAACE,IAApB,kBApoC4GlX,EAooC5G;;AACA;AAAA,qDAroC4GA,EAqoC5G,mBAA2FgX,mBAA3F,EAA4H,CAAC;AACjHlQ,IAAAA,IAAI,EAAEhG,QAD2G;AAEjHiG,IAAAA,IAAI,EAAE,CAAC;AACCoQ,MAAAA,OAAO,EAAE,CAACvM,aAAD,CADV;AAECwM,MAAAA,YAAY,EAAE,CAACxM,aAAD;AAFf,KAAD;AAF2G,GAAD,CAA5H;AAAA;AAOA;AACA;AACA;;;AACA,MAAMyM,eAAN,CAAsB;;AAEtBA,eAAe,CAAC5Q,IAAhB;AAAA,mBAA4G4Q,eAA5G;AAAA;;AACAA,eAAe,CAACJ,IAAhB,kBAlpC4GjX,EAkpC5G;AAAA,QAA6GqX;AAA7G;AAGAA,eAAe,CAACH,IAAhB,kBArpC4GlX,EAqpC5G;AAAA,YAAwI,CAACoC,UAAD,EAAa4U,mBAAb,CAAxI,EAA2K5U,UAA3K,EAAuL4U,mBAAvL;AAAA;;AACA;AAAA,qDAtpC4GhX,EAspC5G,mBAA2FqX,eAA3F,EAAwH,CAAC;AAC7GvQ,IAAAA,IAAI,EAAEhG,QADuG;AAE7GiG,IAAAA,IAAI,EAAE,CAAC;AACCuQ,MAAAA,OAAO,EAAE,CAAClV,UAAD,EAAa4U,mBAAb,CADV;AAECG,MAAAA,OAAO,EAAE,CACL/U,UADK,EAEL4U,mBAFK,EAGL1Q,yBAHK,EAIL0M,eAJK,EAKL1E,wBALK,CAFV;AASC8I,MAAAA,YAAY,EAAE,CAAC9Q,yBAAD,EAA4B0M,eAA5B,EAA6C1E,wBAA7C;AATf,KAAD;AAFuG,GAAD,CAAxH;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAShI,yBAAT,EAAoCsE,aAApC,EAAmDoM,mBAAnD,EAAwEhE,eAAxE,EAAyF1E,wBAAzF,EAAmH9B,mBAAnH,EAAwItF,mBAAxI,EAA6JvE,8BAA7J,EAA6LwE,gBAA7L,EAA+MkQ,eAA/M,EAAgO3U,uBAAhO,EAAyP+J,aAAzP,EAAwQtG,sCAAxQ", "sourcesContent": ["import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n    /**\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    constructor(itemSize, minBufferPx, maxBufferPx) {\n        this._scrolledIndexChange = new Subject();\n        /** @docs-private Implemented as part of VirtualScrollStrategy. */\n        this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n        /** The attached viewport. */\n        this._viewport = null;\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n    }\n    /**\n     * Attaches this scroll strategy to a viewport.\n     * @param viewport The viewport to attach this strategy to.\n     */\n    attach(viewport) {\n        this._viewport = viewport;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** Detaches this scroll strategy from the currently attached viewport. */\n    detach() {\n        this._scrolledIndexChange.complete();\n        this._viewport = null;\n    }\n    /**\n     * Update the item size and buffer size.\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n        if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n        }\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentScrolled() {\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onDataLengthChanged() {\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentRendered() {\n        /* no-op */\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onRenderedOffsetChanged() {\n        /* no-op */\n    }\n    /**\n     * Scroll to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling.\n     */\n    scrollToIndex(index, behavior) {\n        if (this._viewport) {\n            this._viewport.scrollToOffset(index * this._itemSize, behavior);\n        }\n    }\n    /** Update the viewport's total content size. */\n    _updateTotalContentSize() {\n        if (!this._viewport) {\n            return;\n        }\n        this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n    }\n    /** Update the viewport's rendered range. */\n    _updateRenderedRange() {\n        if (!this._viewport) {\n            return;\n        }\n        const renderedRange = this._viewport.getRenderedRange();\n        const newRange = { start: renderedRange.start, end: renderedRange.end };\n        const viewportSize = this._viewport.getViewportSize();\n        const dataLength = this._viewport.getDataLength();\n        let scrollOffset = this._viewport.measureScrollOffset();\n        // Prevent NaN as result when dividing by zero.\n        let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n        // If user scrolls to the bottom of the list and data changes to a smaller list\n        if (newRange.end > dataLength) {\n            // We have to recalculate the first visible index based on new data length and viewport size.\n            const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n            const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n            // If first visible index changed we must update scroll offset to handle start/end buffers\n            // Current range must also be adjusted to cover the new position (bottom of new list).\n            if (firstVisibleIndex != newVisibleIndex) {\n                firstVisibleIndex = newVisibleIndex;\n                scrollOffset = newVisibleIndex * this._itemSize;\n                newRange.start = Math.floor(firstVisibleIndex);\n            }\n            newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n        }\n        const startBuffer = scrollOffset - newRange.start * this._itemSize;\n        if (startBuffer < this._minBufferPx && newRange.start != 0) {\n            const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n            newRange.start = Math.max(0, newRange.start - expandStart);\n            newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n        }\n        else {\n            const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n            if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n                const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n                if (expandEnd > 0) {\n                    newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n                    newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n                }\n            }\n        }\n        this._viewport.setRenderedRange(newRange);\n        this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n        this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n    }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n    return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n    constructor() {\n        this._itemSize = 20;\n        this._minBufferPx = 100;\n        this._maxBufferPx = 200;\n        /** The scroll strategy used by this directive. */\n        this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(value) {\n        this._itemSize = coerceNumberProperty(value);\n    }\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n        return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n        this._minBufferPx = coerceNumberProperty(value);\n    }\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n        return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n        this._maxBufferPx = coerceNumberProperty(value);\n    }\n    ngOnChanges() {\n        this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n}\nCdkFixedSizeVirtualScroll.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nCdkFixedSizeVirtualScroll.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkFixedSizeVirtualScroll, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: { itemSize: \"itemSize\", minBufferPx: \"minBufferPx\", maxBufferPx: \"maxBufferPx\" }, providers: [\n        {\n            provide: VIRTUAL_SCROLL_STRATEGY,\n            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n        },\n    ], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[itemSize]',\n                    providers: [\n                        {\n                            provide: VIRTUAL_SCROLL_STRATEGY,\n                            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n                        },\n                    ],\n                }]\n        }], propDecorators: { itemSize: [{\n                type: Input\n            }], minBufferPx: [{\n                type: Input\n            }], maxBufferPx: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n    constructor(_ngZone, _platform, document) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n        this._scrolled = new Subject();\n        /** Keeps track of the global `scroll` and `resize` subscriptions. */\n        this._globalSubscription = null;\n        /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n        this._scrolledCount = 0;\n        /**\n         * Map of all the scrollable references that are registered with the service and their\n         * scroll event subscriptions.\n         */\n        this.scrollContainers = new Map();\n        this._document = document;\n    }\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n        if (!this.scrollContainers.has(scrollable)) {\n            this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n        }\n    }\n    /**\n     * Deregisters a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n        const scrollableReference = this.scrollContainers.get(scrollable);\n        if (scrollableReference) {\n            scrollableReference.unsubscribe();\n            this.scrollContainers.delete(scrollable);\n        }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n        if (!this._platform.isBrowser) {\n            return of();\n        }\n        return new Observable((observer) => {\n            if (!this._globalSubscription) {\n                this._addGlobalListener();\n            }\n            // In the case of a 0ms delay, use an observable without auditTime\n            // since it does add a perceptible delay in processing overhead.\n            const subscription = auditTimeInMs > 0\n                ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n                : this._scrolled.subscribe(observer);\n            this._scrolledCount++;\n            return () => {\n                subscription.unsubscribe();\n                this._scrolledCount--;\n                if (!this._scrolledCount) {\n                    this._removeGlobalListener();\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        this._removeGlobalListener();\n        this.scrollContainers.forEach((_, container) => this.deregister(container));\n        this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n        const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n        return this.scrolled(auditTimeInMs).pipe(filter(target => {\n            return !target || ancestors.indexOf(target) > -1;\n        }));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n        const scrollingContainers = [];\n        this.scrollContainers.forEach((_subscription, scrollable) => {\n            if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n                scrollingContainers.push(scrollable);\n            }\n        });\n        return scrollingContainers;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n        let element = coerceElement(elementOrElementRef);\n        let scrollableElement = scrollable.getElementRef().nativeElement;\n        // Traverse through the element parents until we reach null, checking if any of the elements\n        // are the scrollable's element.\n        do {\n            if (element == scrollableElement) {\n                return true;\n            }\n        } while ((element = element.parentElement));\n        return false;\n    }\n    /** Sets up the global scroll listeners. */\n    _addGlobalListener() {\n        this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n            const window = this._getWindow();\n            return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n        });\n    }\n    /** Cleans up the global scroll listener. */\n    _removeGlobalListener() {\n        if (this._globalSubscription) {\n            this._globalSubscription.unsubscribe();\n            this._globalSubscription = null;\n        }\n    }\n}\nScrollDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollDispatcher, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nScrollDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        this.elementRef = elementRef;\n        this.scrollDispatcher = scrollDispatcher;\n        this.ngZone = ngZone;\n        this.dir = dir;\n        this._destroyed = new Subject();\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll')\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(observer)));\n    }\n    ngOnInit() {\n        this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n        this.scrollDispatcher.deregister(this);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n        return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n        return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n        const el = this.elementRef.nativeElement;\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        // Rewrite start & end offsets as right or left offsets.\n        if (options.left == null) {\n            options.left = isRtl ? options.end : options.start;\n        }\n        if (options.right == null) {\n            options.right = isRtl ? options.start : options.end;\n        }\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top =\n                el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl && getRtlScrollAxisType() != 0 /* NORMAL */) {\n            if (options.left != null) {\n                options.right =\n                    el.scrollWidth - el.clientWidth - options.left;\n            }\n            if (getRtlScrollAxisType() == 2 /* INVERTED */) {\n                options.left = options.right;\n            }\n            else if (getRtlScrollAxisType() == 1 /* NEGATED */) {\n                options.left = options.right ? -options.right : options.right;\n            }\n        }\n        else {\n            if (options.right != null) {\n                options.left =\n                    el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n        const el = this.elementRef.nativeElement;\n        if (supportsScrollBehavior()) {\n            el.scrollTo(options);\n        }\n        else {\n            if (options.top != null) {\n                el.scrollTop = options.top;\n            }\n            if (options.left != null) {\n                el.scrollLeft = options.left;\n            }\n        }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const el = this.elementRef.nativeElement;\n        if (from == 'top') {\n            return el.scrollTop;\n        }\n        if (from == 'bottom') {\n            return el.scrollHeight - el.clientHeight - el.scrollTop;\n        }\n        // Rewrite start & end as left or right offsets.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        if (from == 'start') {\n            from = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            from = isRtl ? LEFT : RIGHT;\n        }\n        if (isRtl && getRtlScrollAxisType() == 2 /* INVERTED */) {\n            // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n            else {\n                return el.scrollLeft;\n            }\n        }\n        else if (isRtl && getRtlScrollAxisType() == 1 /* NEGATED */) {\n            // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft + el.scrollWidth - el.clientWidth;\n            }\n            else {\n                return -el.scrollLeft;\n            }\n        }\n        else {\n            // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n            // (scrollWidth - clientWidth) when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft;\n            }\n            else {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n        }\n    }\n}\nCdkScrollable.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkScrollable.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-scrollable], [cdkScrollable]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n    constructor(_platform, ngZone, document) {\n        this._platform = _platform;\n        /** Stream of viewport change events. */\n        this._change = new Subject();\n        /** Event listener that will be used to handle the viewport change events. */\n        this._changeListener = (event) => {\n            this._change.next(event);\n        };\n        this._document = document;\n        ngZone.runOutsideAngular(() => {\n            if (_platform.isBrowser) {\n                const window = this._getWindow();\n                // Note that bind the events ourselves, rather than going through something like RxJS's\n                // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n                window.addEventListener('resize', this._changeListener);\n                window.addEventListener('orientationchange', this._changeListener);\n            }\n            // Clear the cached position so that the viewport is re-measured next time it is required.\n            // We don't need to keep track of the subscription, because it is completed on destroy.\n            this.change().subscribe(() => (this._viewportSize = null));\n        });\n    }\n    ngOnDestroy() {\n        if (this._platform.isBrowser) {\n            const window = this._getWindow();\n            window.removeEventListener('resize', this._changeListener);\n            window.removeEventListener('orientationchange', this._changeListener);\n        }\n        this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n        if (!this._viewportSize) {\n            this._updateViewportSize();\n        }\n        const output = { width: this._viewportSize.width, height: this._viewportSize.height };\n        // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n        if (!this._platform.isBrowser) {\n            this._viewportSize = null;\n        }\n        return output;\n    }\n    /** Gets a ClientRect for the viewport's bounds. */\n    getViewportRect() {\n        // Use the document element's bounding rect rather than the window scroll properties\n        // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n        // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n        // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n        // can disagree when the page is pinch-zoomed (on devices that support touch).\n        // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n        // We use the documentElement instead of the body because, by default (without a css reset)\n        // browsers typically give the document body an 8px margin, which is not included in\n        // getBoundingClientRect().\n        const scrollPosition = this.getViewportScrollPosition();\n        const { width, height } = this.getViewportSize();\n        return {\n            top: scrollPosition.top,\n            left: scrollPosition.left,\n            bottom: scrollPosition.top + height,\n            right: scrollPosition.left + width,\n            height,\n            width,\n        };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n        // While we can get a reference to the fake document\n        // during SSR, it doesn't have getBoundingClientRect.\n        if (!this._platform.isBrowser) {\n            return { top: 0, left: 0 };\n        }\n        // The top-left-corner of the viewport is determined by the scroll position of the document\n        // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n        // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n        // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n        // `document.documentElement` works consistently, where the `top` and `left` values will\n        // equal negative the scroll position.\n        const document = this._document;\n        const window = this._getWindow();\n        const documentElement = document.documentElement;\n        const documentRect = documentElement.getBoundingClientRect();\n        const top = -documentRect.top ||\n            document.body.scrollTop ||\n            window.scrollY ||\n            documentElement.scrollTop ||\n            0;\n        const left = -documentRect.left ||\n            document.body.scrollLeft ||\n            window.scrollX ||\n            documentElement.scrollLeft ||\n            0;\n        return { top, left };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n        return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n        const window = this._getWindow();\n        this._viewportSize = this._platform.isBrowser\n            ? { width: window.innerWidth, height: window.innerHeight }\n            : { width: 0, height: 0 };\n    }\n}\nViewportRuler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ViewportRuler, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nViewportRuler.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ViewportRuler, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ViewportRuler, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n    return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkScrollable {\n    constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n        this.elementRef = elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollStrategy = _scrollStrategy;\n        /** Emits when the viewport is detached from a CdkVirtualForOf. */\n        this._detachedSubject = new Subject();\n        /** Emits when the rendered range changes. */\n        this._renderedRangeSubject = new Subject();\n        this._orientation = 'vertical';\n        this._appendOnly = false;\n        // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n        // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n        // depending on how the strategy calculates the scrolled index, it may come at a cost to\n        // performance.\n        /** Emits when the index of the first element visible in the viewport changes. */\n        this.scrolledIndexChange = new Observable((observer) => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n        /** A stream that emits whenever the rendered range changes. */\n        this.renderedRangeStream = this._renderedRangeSubject;\n        /**\n         * The total size of all content (in pixels), including content that is not currently rendered.\n         */\n        this._totalContentSize = 0;\n        /** A string representing the `style.width` property value to be used for the spacer element. */\n        this._totalContentWidth = '';\n        /** A string representing the `style.height` property value to be used for the spacer element. */\n        this._totalContentHeight = '';\n        /** The currently rendered range of indices. */\n        this._renderedRange = { start: 0, end: 0 };\n        /** The length of the data bound to this viewport (in number of items). */\n        this._dataLength = 0;\n        /** The size of the viewport (in pixels). */\n        this._viewportSize = 0;\n        /** The last rendered content offset that was set. */\n        this._renderedContentOffset = 0;\n        /**\n         * Whether the last rendered content offset was to the end of the content (and therefore needs to\n         * be rewritten as an offset to the start of the content).\n         */\n        this._renderedContentOffsetNeedsRewrite = false;\n        /** Whether there is a pending change detection cycle. */\n        this._isChangeDetectionPending = false;\n        /** A list of functions to run after the next change detection cycle. */\n        this._runAfterChangeDetection = [];\n        /** Subscription to changes in the viewport size. */\n        this._viewportChanges = Subscription.EMPTY;\n        if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n        }\n        this._viewportChanges = viewportRuler.change().subscribe(() => {\n            this.checkViewportSize();\n        });\n    }\n    /** The direction the viewport scrolls. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(orientation) {\n        if (this._orientation !== orientation) {\n            this._orientation = orientation;\n            this._calculateSpacerSize();\n        }\n    }\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    get appendOnly() {\n        return this._appendOnly;\n    }\n    set appendOnly(value) {\n        this._appendOnly = coerceBooleanProperty(value);\n    }\n    ngOnInit() {\n        super.ngOnInit();\n        // It's still too early to measure the viewport at this point. Deferring with a promise allows\n        // the Viewport to be rendered with the correct size before we measure. We run this outside the\n        // zone to avoid causing more change detection cycles. We handle the change detection loop\n        // ourselves instead.\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n            this._measureViewportSize();\n            this._scrollStrategy.attach(this);\n            this.elementScrolled()\n                .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null), \n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER))\n                .subscribe(() => this._scrollStrategy.onContentScrolled());\n            this._markChangeDetectionNeeded();\n        }));\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._scrollStrategy.detach();\n        // Complete all subjects\n        this._renderedRangeSubject.complete();\n        this._detachedSubject.complete();\n        this._viewportChanges.unsubscribe();\n        super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n        if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CdkVirtualScrollViewport is already attached.');\n        }\n        // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n        // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n        // change detection loop ourselves.\n        this.ngZone.runOutsideAngular(() => {\n            this._forOf = forOf;\n            this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n                const newLength = data.length;\n                if (newLength !== this._dataLength) {\n                    this._dataLength = newLength;\n                    this._scrollStrategy.onDataLengthChanged();\n                }\n                this._doChangeDetection();\n            });\n        });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n        this._forOf = null;\n        this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n        return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n        return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n        return this._renderedRange;\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n        if (this._totalContentSize !== size) {\n            this._totalContentSize = size;\n            this._calculateSpacerSize();\n            this._markChangeDetectionNeeded();\n        }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n        if (!rangesEqual(this._renderedRange, range)) {\n            if (this.appendOnly) {\n                range = { start: 0, end: Math.max(this._renderedRange.end, range.end) };\n            }\n            this._renderedRangeSubject.next((this._renderedRange = range));\n            this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n        }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n        return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n        // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n        // in the negative direction.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        const isHorizontal = this.orientation == 'horizontal';\n        const axis = isHorizontal ? 'X' : 'Y';\n        const axisDirection = isHorizontal && isRtl ? -1 : 1;\n        let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n        // in appendOnly, we always start from the top\n        offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n        this._renderedContentOffset = offset;\n        if (to === 'to-end') {\n            transform += ` translate${axis}(-100%)`;\n            // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n            // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n            // expand upward).\n            this._renderedContentOffsetNeedsRewrite = true;\n        }\n        if (this._renderedContentTransform != transform) {\n            // We know this value is safe because we parse `offset` with `Number()` before passing it\n            // into the string.\n            this._renderedContentTransform = transform;\n            this._markChangeDetectionNeeded(() => {\n                if (this._renderedContentOffsetNeedsRewrite) {\n                    this._renderedContentOffset -= this.measureRenderedContentSize();\n                    this._renderedContentOffsetNeedsRewrite = false;\n                    this.setRenderedContentOffset(this._renderedContentOffset);\n                }\n                else {\n                    this._scrollStrategy.onRenderedOffsetChanged();\n                }\n            });\n        }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n        const options = { behavior };\n        if (this.orientation === 'horizontal') {\n            options.start = offset;\n        }\n        else {\n            options.top = offset;\n        }\n        this.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n        this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the viewport (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n        return from\n            ? super.measureScrollOffset(from)\n            : super.measureScrollOffset(this.orientation === 'horizontal' ? 'start' : 'top');\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n        const contentEl = this._contentWrapper.nativeElement;\n        return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n        if (!this._forOf) {\n            return 0;\n        }\n        return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n        // TODO: Cleanup later when add logic for handling content resize\n        this._measureViewportSize();\n        this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n        const viewportEl = this.elementRef.nativeElement;\n        this._viewportSize =\n            this.orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n        if (runAfter) {\n            this._runAfterChangeDetection.push(runAfter);\n        }\n        // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n        // properties sequentially we only have to run `_doChangeDetection` once at the end.\n        if (!this._isChangeDetectionPending) {\n            this._isChangeDetectionPending = true;\n            this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n                this._doChangeDetection();\n            }));\n        }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n        this._isChangeDetectionPending = false;\n        // Apply the content transform. The transform can't be set via an Angular binding because\n        // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n        // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n        // the `Number` function first to coerce it to a numeric value.\n        this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n        // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n        // from the root, since the repeated items are content projected in. Calling `detectChanges`\n        // instead does not properly check the projected content.\n        this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n        const runAfterChangeDetection = this._runAfterChangeDetection;\n        this._runAfterChangeDetection = [];\n        for (const fn of runAfterChangeDetection) {\n            fn();\n        }\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n        this._totalContentHeight =\n            this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n        this._totalContentWidth =\n            this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n    }\n}\nCdkVirtualScrollViewport.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkVirtualScrollViewport, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: VIRTUAL_SCROLL_STRATEGY, optional: true }, { token: i2.Directionality, optional: true }, { token: ScrollDispatcher }, { token: ViewportRuler }], target: i0.ɵɵFactoryTarget.Component });\nCdkVirtualScrollViewport.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkVirtualScrollViewport, selector: \"cdk-virtual-scroll-viewport\", inputs: { orientation: \"orientation\", appendOnly: \"appendOnly\" }, outputs: { scrolledIndexChange: \"scrolledIndexChange\" }, host: { properties: { \"class.cdk-virtual-scroll-orientation-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.cdk-virtual-scroll-orientation-vertical\": \"orientation !== \\\"horizontal\\\"\" }, classAttribute: \"cdk-virtual-scroll-viewport\" }, providers: [\n        {\n            provide: CdkScrollable,\n            useExisting: CdkVirtualScrollViewport,\n        },\n    ], viewQueries: [{ propertyName: \"_contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0}[dir=rtl] .cdk-virtual-scroll-spacer{right:0;left:auto;transform-origin:100% 0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkVirtualScrollViewport, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-virtual-scroll-viewport', host: {\n                        'class': 'cdk-virtual-scroll-viewport',\n                        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: CdkVirtualScrollViewport,\n                        },\n                    ], template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0}[dir=rtl] .cdk-virtual-scroll-spacer{right:0;left:auto;transform-origin:100% 0}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [VIRTUAL_SCROLL_STRATEGY]\n                }] }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: ScrollDispatcher }, { type: ViewportRuler }]; }, propDecorators: { orientation: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input\n            }], scrolledIndexChange: [{\n                type: Output\n            }], _contentWrapper: [{\n                type: ViewChild,\n                args: ['contentWrapper', { static: true }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n    const el = node;\n    if (!el.getBoundingClientRect) {\n        return 0;\n    }\n    const rect = el.getBoundingClientRect();\n    if (orientation === 'horizontal') {\n        return direction === 'start' ? rect.left : rect.right;\n    }\n    return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n    constructor(\n    /** The view container to add items to. */\n    _viewContainerRef, \n    /** The template to use when stamping out new items. */\n    _template, \n    /** The set of available differs. */\n    _differs, \n    /** The strategy used to render items in the virtual scroll viewport. */\n    _viewRepeater, \n    /** The virtual scrolling viewport that these items are being rendered in. */\n    _viewport, ngZone) {\n        this._viewContainerRef = _viewContainerRef;\n        this._template = _template;\n        this._differs = _differs;\n        this._viewRepeater = _viewRepeater;\n        this._viewport = _viewport;\n        /** Emits when the rendered view of the data changes. */\n        this.viewChange = new Subject();\n        /** Subject that emits when a new DataSource instance is given. */\n        this._dataSourceChanges = new Subject();\n        /** Emits whenever the data in the current DataSource changes. */\n        this.dataStream = this._dataSourceChanges.pipe(\n        // Start off with null `DataSource`.\n        startWith(null), \n        // Bundle up the previous and current data sources so we can work with both.\n        pairwise(), \n        // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n        // new one, passing back a stream of data changes which we run through `switchMap` to give\n        // us a data stream that emits the latest data from whatever the current `DataSource` is.\n        switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), \n        // Replay the last emitted data when someone subscribes.\n        shareReplay(1));\n        /** The differ used to calculate changes to the data. */\n        this._differ = null;\n        /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n        this._needsUpdate = false;\n        this._destroyed = new Subject();\n        this.dataStream.subscribe(data => {\n            this._data = data;\n            this._onRenderedDataChange();\n        });\n        this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n            this._renderedRange = range;\n            if (this.viewChange.observers.length) {\n                ngZone.run(() => this.viewChange.next(this._renderedRange));\n            }\n            this._onRenderedDataChange();\n        });\n        this._viewport.attach(this);\n    }\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n        return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n        this._cdkVirtualForOf = value;\n        if (isDataSource(value)) {\n            this._dataSourceChanges.next(value);\n        }\n        else {\n            // If value is an an NgIterable, convert it to an array.\n            this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n        }\n    }\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n        return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n        this._needsUpdate = true;\n        this._cdkVirtualForTrackBy = fn\n            ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n            : undefined;\n    }\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n        if (value) {\n            this._needsUpdate = true;\n            this._template = value;\n        }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n        return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n        this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n        if (range.start >= range.end) {\n            return 0;\n        }\n        if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Error: attempted to measure an item that isn't rendered.`);\n        }\n        // The index into the list of rendered views for the first item in the range.\n        const renderedStartIndex = range.start - this._renderedRange.start;\n        // The length of the range we're measuring.\n        const rangeLen = range.end - range.start;\n        // Loop over all the views, find the first and land node and compute the size by subtracting\n        // the top of the first node from the bottom of the last one.\n        let firstNode;\n        let lastNode;\n        // Find the first node by starting from the beginning and going forwards.\n        for (let i = 0; i < rangeLen; i++) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                firstNode = lastNode = view.rootNodes[0];\n                break;\n            }\n        }\n        // Find the last node by starting from the end and going backwards.\n        for (let i = rangeLen - 1; i > -1; i--) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                lastNode = view.rootNodes[view.rootNodes.length - 1];\n                break;\n            }\n        }\n        return firstNode && lastNode\n            ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n            : 0;\n    }\n    ngDoCheck() {\n        if (this._differ && this._needsUpdate) {\n            // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n            // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n            // changing (need to do this diff).\n            const changes = this._differ.diff(this._renderedItems);\n            if (!changes) {\n                this._updateContext();\n            }\n            else {\n                this._applyChanges(changes);\n            }\n            this._needsUpdate = false;\n        }\n    }\n    ngOnDestroy() {\n        this._viewport.detach();\n        this._dataSourceChanges.next(undefined);\n        this._dataSourceChanges.complete();\n        this.viewChange.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n        if (!this._renderedRange) {\n            return;\n        }\n        this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n        if (!this._differ) {\n            // Use a wrapper function for the `trackBy` so any new values are\n            // picked up automatically without having to recreate the differ.\n            this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n                return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n            });\n        }\n        this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n        if (oldDs) {\n            oldDs.disconnect(this);\n        }\n        this._needsUpdate = true;\n        return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n            view.detectChanges();\n        }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n        this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n        // Update $implicit for any items that had an identity change.\n        changes.forEachIdentityChange((record) => {\n            const view = this._viewContainerRef.get(record.currentIndex);\n            view.context.$implicit = record.item;\n        });\n        // Update the context variables on all items.\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n        }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n        context.first = context.index === 0;\n        context.last = context.index === context.count - 1;\n        context.even = context.index % 2 === 0;\n        context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n        // Note that it's important that we insert the item directly at the proper index,\n        // rather than inserting it and the moving it in place, because if there's a directive\n        // on the same node that injects the `ViewContainerRef`, Angular will insert another\n        // comment node which can throw off the move when it's being repeated for all items.\n        return {\n            templateRef: this._template,\n            context: {\n                $implicit: record.item,\n                // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n                // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n                cdkVirtualForOf: this._cdkVirtualForOf,\n                index: -1,\n                count: -1,\n                first: false,\n                last: false,\n                odd: false,\n                even: false,\n            },\n            index,\n        };\n    }\n}\nCdkVirtualForOf.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkVirtualForOf, deps: [{ token: i0.ViewContainerRef }, { token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: _VIEW_REPEATER_STRATEGY }, { token: CdkVirtualScrollViewport, skipSelf: true }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nCdkVirtualForOf.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkVirtualForOf, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: { cdkVirtualForOf: \"cdkVirtualForOf\", cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\", cdkVirtualForTemplate: \"cdkVirtualForTemplate\", cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\" }, providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkVirtualForOf, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualFor][cdkVirtualForOf]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ViewContainerRef }, { type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: i2$1._RecycleViewRepeaterStrategy, decorators: [{\n                    type: Inject,\n                    args: [_VIEW_REPEATER_STRATEGY]\n                }] }, { type: CdkVirtualScrollViewport, decorators: [{\n                    type: SkipSelf\n                }] }, { type: i0.NgZone }]; }, propDecorators: { cdkVirtualForOf: [{\n                type: Input\n            }], cdkVirtualForTrackBy: [{\n                type: Input\n            }], cdkVirtualForTemplate: [{\n                type: Input\n            }], cdkVirtualForTemplateCacheSize: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass CdkScrollableModule {\n}\nCdkScrollableModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkScrollableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nCdkScrollableModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkScrollableModule, declarations: [CdkScrollable], exports: [CdkScrollable] });\nCdkScrollableModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkScrollableModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkScrollableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkScrollable],\n                    declarations: [CdkScrollable],\n                }]\n        }] });\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n}\nScrollingModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nScrollingModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollingModule, declarations: [CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport], imports: [BidiModule, CdkScrollableModule], exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll,\n        CdkVirtualForOf,\n        CdkVirtualScrollViewport] });\nScrollingModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollingModule, imports: [[BidiModule, CdkScrollableModule], BidiModule, CdkScrollableModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, CdkScrollableModule],\n                    exports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollViewport,\n                    ],\n                    declarations: [CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n"]}, "metadata": {}, "sourceType": "module"}
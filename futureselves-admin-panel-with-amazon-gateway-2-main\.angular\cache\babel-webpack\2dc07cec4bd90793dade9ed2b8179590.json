{"ast": null, "code": "import { ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceStringArray } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, TemplateRef, Directive, Inject, ViewChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, forwardRef, Optional, Host, NgModule } from '@angular/core';\nimport { mixinDisableRipple, MAT_OPTION_PARENT_COMPONENT, MAT_OPTGROUP, MatOption, MatOptionSelectionChange, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport { Subscription, Subject, defer, merge, of, fromEvent } from 'rxjs';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i2$1 from '@angular/cdk/bidi';\nimport { hasModifierKey, ESCAPE, ENTER, UP_ARROW, DOWN_ARROW, TAB } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD } from '@angular/material/form-field';\nimport { startWith, switchMap, take, filter, map, tap, delay } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Autocomplete IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\n\nconst _c0 = [\"panel\"];\n\nfunction MatAutocomplete_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0, 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const formFieldId_r1 = ctx.id;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r0.id)(\"ngClass\", ctx_r0._classList);\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel || null)(\"aria-labelledby\", ctx_r0._getPanelAriaLabelledby(formFieldId_r1));\n  }\n}\n\nconst _c1 = [\"*\"];\nlet _uniqueAutocompleteIdCounter = 0;\n/** Event object that is emitted when an autocomplete option is selected. */\n\nclass MatAutocompleteSelectedEvent {\n  constructor(\n  /** Reference to the autocomplete panel that emitted the event. */\n  source,\n  /** Option that was selected. */\n  option) {\n    this.source = source;\n    this.option = option;\n  }\n\n} // Boilerplate for applying mixins to MatAutocomplete.\n\n/** @docs-private */\n\n\nconst _MatAutocompleteMixinBase = mixinDisableRipple(class {});\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\n\n\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n  providedIn: 'root',\n  factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    autoActiveFirstOption: false\n  };\n}\n/** Base class with all of the `MatAutocomplete` functionality. */\n\n\nclass _MatAutocompleteBase extends _MatAutocompleteMixinBase {\n  constructor(_changeDetectorRef, _elementRef, defaults, platform) {\n    super();\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    this._activeOptionChanges = Subscription.EMPTY;\n    /** Whether the autocomplete panel should be visible, depending on option length. */\n\n    this.showPanel = false;\n    this._isOpen = false;\n    /** Function that maps an option's control value to its display value in the trigger. */\n\n    this.displayWith = null;\n    /** Event that is emitted whenever an option from the list is selected. */\n\n    this.optionSelected = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is opened. */\n\n    this.opened = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is closed. */\n\n    this.closed = new EventEmitter();\n    /** Emits whenever an option is activated. */\n\n    this.optionActivated = new EventEmitter();\n    this._classList = {};\n    /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n\n    this.id = `mat-autocomplete-${_uniqueAutocompleteIdCounter++}`; // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n    // Safari using VoiceOver. We should occasionally check back to see whether the bug\n    // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n    // option altogether.\n\n    this.inertGroups = platform?.SAFARI || false;\n    this._autoActiveFirstOption = !!defaults.autoActiveFirstOption;\n  }\n  /** Whether the autocomplete panel is open. */\n\n\n  get isOpen() {\n    return this._isOpen && this.showPanel;\n  }\n  /**\n   * Whether the first option should be highlighted when the autocomplete panel is opened.\n   * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n   */\n\n\n  get autoActiveFirstOption() {\n    return this._autoActiveFirstOption;\n  }\n\n  set autoActiveFirstOption(value) {\n    this._autoActiveFirstOption = coerceBooleanProperty(value);\n  }\n  /**\n   * Takes classes set on the host mat-autocomplete element and applies them to the panel\n   * inside the overlay container to allow for easy styling.\n   */\n\n\n  set classList(value) {\n    if (value && value.length) {\n      this._classList = coerceStringArray(value).reduce((classList, className) => {\n        classList[className] = true;\n        return classList;\n      }, {});\n    } else {\n      this._classList = {};\n    }\n\n    this._setVisibilityClasses(this._classList);\n\n    this._elementRef.nativeElement.className = '';\n  }\n\n  ngAfterContentInit() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withWrap();\n    this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n      if (this.isOpen) {\n        this.optionActivated.emit({\n          source: this,\n          option: this.options.toArray()[index] || null\n        });\n      }\n    }); // Set the initial visibility state.\n\n    this._setVisibility();\n  }\n\n  ngOnDestroy() {\n    this._activeOptionChanges.unsubscribe();\n  }\n  /**\n   * Sets the panel scrollTop. This allows us to manually scroll to display options\n   * above or below the fold, as they are not actually being focused when active.\n   */\n\n\n  _setScrollTop(scrollTop) {\n    if (this.panel) {\n      this.panel.nativeElement.scrollTop = scrollTop;\n    }\n  }\n  /** Returns the panel's scrollTop. */\n\n\n  _getScrollTop() {\n    return this.panel ? this.panel.nativeElement.scrollTop : 0;\n  }\n  /** Panel should hide itself when the option list is empty. */\n\n\n  _setVisibility() {\n    this.showPanel = !!this.options.length;\n\n    this._setVisibilityClasses(this._classList);\n\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits the `select` event. */\n\n\n  _emitSelectEvent(option) {\n    const event = new MatAutocompleteSelectedEvent(this, option);\n    this.optionSelected.emit(event);\n  }\n  /** Gets the aria-labelledby for the autocomplete panel. */\n\n\n  _getPanelAriaLabelledby(labelId) {\n    if (this.ariaLabel) {\n      return null;\n    }\n\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Sets the autocomplete visibility classes on a classlist based on the panel is visible. */\n\n\n  _setVisibilityClasses(classList) {\n    classList[this._visibleClass] = this.showPanel;\n    classList[this._hiddenClass] = !this.showPanel;\n  }\n\n}\n\n_MatAutocompleteBase.ɵfac = function _MatAutocompleteBase_Factory(t) {\n  return new (t || _MatAutocompleteBase)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i1.Platform));\n};\n\n_MatAutocompleteBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAutocompleteBase,\n  viewQuery: function _MatAutocompleteBase_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TemplateRef, 7);\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n    }\n  },\n  inputs: {\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    displayWith: \"displayWith\",\n    autoActiveFirstOption: \"autoActiveFirstOption\",\n    panelWidth: \"panelWidth\",\n    classList: [\"class\", \"classList\"]\n  },\n  outputs: {\n    optionSelected: \"optionSelected\",\n    opened: \"opened\",\n    closed: \"closed\",\n    optionActivated: \"optionActivated\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatAutocompleteBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i1.Platform\n    }];\n  }, {\n    template: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    displayWith: [{\n      type: Input\n    }],\n    autoActiveFirstOption: [{\n      type: Input\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    optionSelected: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    closed: [{\n      type: Output\n    }],\n    optionActivated: [{\n      type: Output\n    }],\n    classList: [{\n      type: Input,\n      args: ['class']\n    }]\n  });\n})();\n\nclass MatAutocomplete extends _MatAutocompleteBase {\n  constructor() {\n    super(...arguments);\n    this._visibleClass = 'mat-autocomplete-visible';\n    this._hiddenClass = 'mat-autocomplete-hidden';\n  }\n\n}\n\nMatAutocomplete.ɵfac = /* @__PURE__ */function () {\n  let ɵMatAutocomplete_BaseFactory;\n  return function MatAutocomplete_Factory(t) {\n    return (ɵMatAutocomplete_BaseFactory || (ɵMatAutocomplete_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocomplete)))(t || MatAutocomplete);\n  };\n}();\n\nMatAutocomplete.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatAutocomplete,\n  selectors: [[\"mat-autocomplete\"]],\n  contentQueries: function MatAutocomplete_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-autocomplete\"],\n  inputs: {\n    disableRipple: \"disableRipple\"\n  },\n  exportAs: [\"matAutocomplete\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_OPTION_PARENT_COMPONENT,\n    useExisting: MatAutocomplete\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 1,\n  vars: 0,\n  consts: [[\"role\", \"listbox\", 1, \"mat-autocomplete-panel\", 3, \"id\", \"ngClass\"], [\"panel\", \"\"]],\n  template: function MatAutocomplete_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, MatAutocomplete_ng_template_0_Template, 3, 4, \"ng-template\");\n    }\n  },\n  directives: [i2.NgClass],\n  styles: [\".mat-autocomplete-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;visibility:hidden;max-width:none;max-height:256px;position:relative;width:100%;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.mat-autocomplete-panel.mat-autocomplete-visible{visibility:visible}.mat-autocomplete-panel.mat-autocomplete-hidden{visibility:hidden}.mat-autocomplete-panel-above .mat-autocomplete-panel{border-radius:0;border-top-left-radius:4px;border-top-right-radius:4px}.mat-autocomplete-panel .mat-divider-horizontal{margin-top:-1px}.cdk-high-contrast-active .mat-autocomplete-panel{outline:solid 1px}mat-autocomplete{display:none}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocomplete, [{\n    type: Component,\n    args: [{\n      selector: 'mat-autocomplete',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      exportAs: 'matAutocomplete',\n      inputs: ['disableRipple'],\n      host: {\n        'class': 'mat-autocomplete'\n      },\n      providers: [{\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatAutocomplete\n      }],\n      template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div class=\\\"mat-autocomplete-panel\\\"\\n       role=\\\"listbox\\\"\\n       [id]=\\\"id\\\"\\n       [attr.aria-label]=\\\"ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n       [ngClass]=\\\"_classList\\\"\\n       #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\".mat-autocomplete-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;visibility:hidden;max-width:none;max-height:256px;position:relative;width:100%;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.mat-autocomplete-panel.mat-autocomplete-visible{visibility:visible}.mat-autocomplete-panel.mat-autocomplete-hidden{visibility:hidden}.mat-autocomplete-panel-above .mat-autocomplete-panel{border-radius:0;border-top-left-radius:4px;border-top-right-radius:4px}.mat-autocomplete-panel .mat-divider-horizontal{margin-top:-1px}.cdk-high-contrast-active .mat-autocomplete-panel{outline:solid 1px}mat-autocomplete{display:none}\\n\"]\n    }]\n  }], null, {\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Base class containing all of the functionality for `MatAutocompleteOrigin`. */\n\n\nclass _MatAutocompleteOriginBase {\n  constructor(\n  /** Reference to the element on which the directive is applied. */\n  elementRef) {\n    this.elementRef = elementRef;\n  }\n\n}\n\n_MatAutocompleteOriginBase.ɵfac = function _MatAutocompleteOriginBase_Factory(t) {\n  return new (t || _MatAutocompleteOriginBase)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\n_MatAutocompleteOriginBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAutocompleteOriginBase\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatAutocompleteOriginBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\n\n\nclass MatAutocompleteOrigin extends _MatAutocompleteOriginBase {}\n\nMatAutocompleteOrigin.ɵfac = /* @__PURE__ */function () {\n  let ɵMatAutocompleteOrigin_BaseFactory;\n  return function MatAutocompleteOrigin_Factory(t) {\n    return (ɵMatAutocompleteOrigin_BaseFactory || (ɵMatAutocompleteOrigin_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocompleteOrigin)))(t || MatAutocompleteOrigin);\n  };\n}();\n\nMatAutocompleteOrigin.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatAutocompleteOrigin,\n  selectors: [[\"\", \"matAutocompleteOrigin\", \"\"]],\n  exportAs: [\"matAutocompleteOrigin\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[matAutocompleteOrigin]',\n      exportAs: 'matAutocompleteOrigin'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\n\n\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy');\n/** @docs-private */\n\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\n\n\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY\n};\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\n\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatAutocompleteTrigger),\n  multi: true\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\n\nfunction getMatAutocompleteMissingPanelError() {\n  return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' + 'Make sure that the id passed to the `matAutocomplete` is correct and that ' + \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\n\n\nclass _MatAutocompleteTriggerBase {\n  constructor(_element, _overlay, _viewContainerRef, _zone, _changeDetectorRef, scrollStrategy, _dir, _formField, _document, _viewportRuler, _defaults) {\n    this._element = _element;\n    this._overlay = _overlay;\n    this._viewContainerRef = _viewContainerRef;\n    this._zone = _zone;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._dir = _dir;\n    this._formField = _formField;\n    this._document = _document;\n    this._viewportRuler = _viewportRuler;\n    this._defaults = _defaults;\n    this._componentDestroyed = false;\n    this._autocompleteDisabled = false;\n    /** Whether or not the label state is being overridden. */\n\n    this._manuallyFloatingLabel = false;\n    /** Subscription to viewport size changes. */\n\n    this._viewportSubscription = Subscription.EMPTY;\n    /**\n     * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n     * closed autocomplete from being reopened if the user switches to another browser tab and then\n     * comes back.\n     */\n\n    this._canOpenOnNextFocus = true;\n    /** Stream of keyboard events that can close the panel. */\n\n    this._closeKeyEventStream = new Subject();\n    /**\n     * Event handler for when the window is blurred. Needs to be an\n     * arrow function in order to preserve the context.\n     */\n\n    this._windowBlurHandler = () => {\n      // If the user blurred the window while the autocomplete is focused, it means that it'll be\n      // refocused when they come back. In this case we want to skip the first focus event, if the\n      // pane was closed, in order to avoid reopening it unintentionally.\n      this._canOpenOnNextFocus = this._document.activeElement !== this._element.nativeElement || this.panelOpen;\n    };\n    /** `View -> model callback called when value changes` */\n\n\n    this._onChange = () => {};\n    /** `View -> model callback called when autocomplete has been touched` */\n\n\n    this._onTouched = () => {};\n    /**\n     * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n     * will render the panel underneath the trigger if there is enough space for it to fit in\n     * the viewport, otherwise the panel will be shown above it. If the position is set to\n     * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n     * whether it fits completely in the viewport.\n     */\n\n\n    this.position = 'auto';\n    /**\n     * `autocomplete` attribute to be set on the input element.\n     * @docs-private\n     */\n\n    this.autocompleteAttribute = 'off';\n    this._overlayAttached = false;\n    /** Stream of changes to the selection state of the autocomplete options. */\n\n    this.optionSelections = defer(() => {\n      const options = this.autocomplete ? this.autocomplete.options : null;\n\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      } // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n      // Return a stream that we'll replace with the real one once everything is in place.\n\n\n      return this._zone.onStable.pipe(take(1), switchMap(() => this.optionSelections));\n    });\n    this._scrollStrategy = scrollStrategy;\n  }\n  /**\n   * Whether the autocomplete is disabled. When disabled, the element will\n   * act as a regular input and the user won't be able to open the panel.\n   */\n\n\n  get autocompleteDisabled() {\n    return this._autocompleteDisabled;\n  }\n\n  set autocompleteDisabled(value) {\n    this._autocompleteDisabled = coerceBooleanProperty(value);\n  }\n\n  ngAfterViewInit() {\n    const window = this._getWindow();\n\n    if (typeof window !== 'undefined') {\n      this._zone.runOutsideAngular(() => window.addEventListener('blur', this._windowBlurHandler));\n    }\n  }\n\n  ngOnChanges(changes) {\n    if (changes['position'] && this._positionStrategy) {\n      this._setStrategyPositions(this._positionStrategy);\n\n      if (this.panelOpen) {\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    const window = this._getWindow();\n\n    if (typeof window !== 'undefined') {\n      window.removeEventListener('blur', this._windowBlurHandler);\n    }\n\n    this._viewportSubscription.unsubscribe();\n\n    this._componentDestroyed = true;\n\n    this._destroyPanel();\n\n    this._closeKeyEventStream.complete();\n  }\n  /** Whether or not the autocomplete panel is open. */\n\n\n  get panelOpen() {\n    return this._overlayAttached && this.autocomplete.showPanel;\n  }\n  /** Opens the autocomplete suggestion panel. */\n\n\n  openPanel() {\n    this._attachOverlay();\n\n    this._floatLabel();\n  }\n  /** Closes the autocomplete suggestion panel. */\n\n\n  closePanel() {\n    this._resetLabel();\n\n    if (!this._overlayAttached) {\n      return;\n    }\n\n    if (this.panelOpen) {\n      // Only emit if the panel was visible.\n      // The `NgZone.onStable` always emits outside of the Angular zone,\n      // so all the subscriptions from `_subscribeToClosingActions()` are also outside of the Angular zone.\n      // We should manually run in Angular zone to update UI after panel closing.\n      this._zone.run(() => {\n        this.autocomplete.closed.emit();\n      });\n    }\n\n    this.autocomplete._isOpen = this._overlayAttached = false;\n\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n\n      this._closingActionsSubscription.unsubscribe();\n    } // Note that in some cases this can end up being called after the component is destroyed.\n    // Add a check to ensure that we don't try to run change detection on a destroyed view.\n\n\n    if (!this._componentDestroyed) {\n      // We need to trigger change detection manually, because\n      // `fromEvent` doesn't seem to do it at the proper time.\n      // This ensures that the label is reset when the\n      // user clicks outside.\n      this._changeDetectorRef.detectChanges();\n    }\n  }\n  /**\n   * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n   * within the viewport.\n   */\n\n\n  updatePosition() {\n    if (this._overlayAttached) {\n      this._overlayRef.updatePosition();\n    }\n  }\n  /**\n   * A stream of actions that should close the autocomplete panel, including\n   * when an option is selected, on blur, and when TAB is pressed.\n   */\n\n\n  get panelClosingActions() {\n    return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached)) : of()).pipe( // Normalize the output so we return a consistent type.\n    map(event => event instanceof MatOptionSelectionChange ? event : null));\n  }\n  /** The currently active option, coerced to MatOption type. */\n\n\n  get activeOption() {\n    if (this.autocomplete && this.autocomplete._keyManager) {\n      return this.autocomplete._keyManager.activeItem;\n    }\n\n    return null;\n  }\n  /** Stream of clicks outside of the autocomplete panel. */\n\n\n  _getOutsideClickStream() {\n    return merge(fromEvent(this._document, 'click'), fromEvent(this._document, 'auxclick'), fromEvent(this._document, 'touchend')).pipe(filter(event => {\n      // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n      // fall back to check the first element in the path of the click event.\n      const clickTarget = _getEventTarget(event);\n\n      const formField = this._formField ? this._formField._elementRef.nativeElement : null;\n      const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n      return this._overlayAttached && clickTarget !== this._element.nativeElement && // Normally focus moves inside `mousedown` so this condition will almost always be\n      // true. Its main purpose is to handle the case where the input is focused from an\n      // outside click which propagates up to the `body` listener within the same sequence\n      // and causes the panel to close immediately (see #3106).\n      this._document.activeElement !== this._element.nativeElement && (!formField || !formField.contains(clickTarget)) && (!customOrigin || !customOrigin.contains(clickTarget)) && !!this._overlayRef && !this._overlayRef.overlayElement.contains(clickTarget);\n    }));\n  } // Implemented as part of ControlValueAccessor.\n\n\n  writeValue(value) {\n    Promise.resolve().then(() => this._setTriggerValue(value));\n  } // Implemented as part of ControlValueAccessor.\n\n\n  registerOnChange(fn) {\n    this._onChange = fn;\n  } // Implemented as part of ControlValueAccessor.\n\n\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  } // Implemented as part of ControlValueAccessor.\n\n\n  setDisabledState(isDisabled) {\n    this._element.nativeElement.disabled = isDisabled;\n  }\n\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const hasModifier = hasModifierKey(event); // Prevent the default action on all escape key presses. This is here primarily to bring IE\n    // in line with other browsers. By default, pressing escape on IE will cause it to revert\n    // the input value to the one that it had on focus, however it won't dispatch any events\n    // which means that the model value will be out of sync with the view.\n\n    if (keyCode === ESCAPE && !hasModifier) {\n      event.preventDefault();\n    }\n\n    if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n      this.activeOption._selectViaInteraction();\n\n      this._resetActiveItem();\n\n      event.preventDefault();\n    } else if (this.autocomplete) {\n      const prevActiveItem = this.autocomplete._keyManager.activeItem;\n      const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n\n      if (keyCode === TAB || isArrowKey && !hasModifier && this.panelOpen) {\n        this.autocomplete._keyManager.onKeydown(event);\n      } else if (isArrowKey && this._canOpen()) {\n        this.openPanel();\n      }\n\n      if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n        this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n      }\n    }\n  }\n\n  _handleInput(event) {\n    let target = event.target;\n    let value = target.value; // Based on `NumberValueAccessor` from forms.\n\n    if (target.type === 'number') {\n      value = value == '' ? null : parseFloat(value);\n    } // If the input has a placeholder, IE will fire the `input` event on page load,\n    // focus and blur, in addition to when the user actually changed the value. To\n    // filter out all of the extra events, we save the value on focus and between\n    // `input` events, and we check whether it changed.\n    // See: https://connect.microsoft.com/IE/feedback/details/885747/\n\n\n    if (this._previousValue !== value) {\n      this._previousValue = value;\n\n      this._onChange(value);\n\n      if (this._canOpen() && this._document.activeElement === event.target) {\n        this.openPanel();\n      }\n    }\n  }\n\n  _handleFocus() {\n    if (!this._canOpenOnNextFocus) {\n      this._canOpenOnNextFocus = true;\n    } else if (this._canOpen()) {\n      this._previousValue = this._element.nativeElement.value;\n\n      this._attachOverlay();\n\n      this._floatLabel(true);\n    }\n  }\n\n  _handleClick() {\n    if (this._canOpen() && !this.panelOpen) {\n      this.openPanel();\n    }\n  }\n  /**\n   * In \"auto\" mode, the label will animate down as soon as focus is lost.\n   * This causes the value to jump when selecting an option with the mouse.\n   * This method manually floats the label until the panel can be closed.\n   * @param shouldAnimate Whether the label should be animated when it is floated.\n   */\n\n\n  _floatLabel(shouldAnimate = false) {\n    if (this._formField && this._formField.floatLabel === 'auto') {\n      if (shouldAnimate) {\n        this._formField._animateAndLockLabel();\n      } else {\n        this._formField.floatLabel = 'always';\n      }\n\n      this._manuallyFloatingLabel = true;\n    }\n  }\n  /** If the label has been manually elevated, return it to its normal state. */\n\n\n  _resetLabel() {\n    if (this._manuallyFloatingLabel) {\n      this._formField.floatLabel = 'auto';\n      this._manuallyFloatingLabel = false;\n    }\n  }\n  /**\n   * This method listens to a stream of panel closing actions and resets the\n   * stream every time the option list changes.\n   */\n\n\n  _subscribeToClosingActions() {\n    const firstStable = this._zone.onStable.pipe(take(1));\n\n    const optionChanges = this.autocomplete.options.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()), // Defer emitting to the stream until the next tick, because changing\n    // bindings in here will cause \"changed after checked\" errors.\n    delay(0)); // When the zone is stable initially, and when the option list changes...\n\n    return merge(firstStable, optionChanges).pipe( // create a new stream of panelClosingActions, replacing any previous streams\n    // that were created, and flatten it so our stream only emits closing events...\n    switchMap(() => {\n      // The `NgZone.onStable` always emits outside of the Angular zone, thus we have to re-enter\n      // the Angular zone. This will lead to change detection being called outside of the Angular\n      // zone and the `autocomplete.opened` will also emit outside of the Angular.\n      this._zone.run(() => {\n        const wasOpen = this.panelOpen;\n\n        this._resetActiveItem();\n\n        this.autocomplete._setVisibility();\n\n        this._changeDetectorRef.detectChanges();\n\n        if (this.panelOpen) {\n          this._overlayRef.updatePosition(); // If the `panelOpen` state changed, we need to make sure to emit the `opened`\n          // event, because we may not have emitted it when the panel was attached. This\n          // can happen if the users opens the panel and there are no options, but the\n          // options come in slightly later or as a result of the value changing.\n\n\n          if (wasOpen !== this.panelOpen) {\n            this.autocomplete.opened.emit();\n          }\n        }\n      });\n\n      return this.panelClosingActions;\n    }), // when the first closing event occurs...\n    take(1)) // set the value, close the panel, and complete.\n    .subscribe(event => this._setValueAndClose(event));\n  }\n  /** Destroys the autocomplete suggestion panel. */\n\n\n  _destroyPanel() {\n    if (this._overlayRef) {\n      this.closePanel();\n\n      this._overlayRef.dispose();\n\n      this._overlayRef = null;\n    }\n  }\n\n  _setTriggerValue(value) {\n    const toDisplay = this.autocomplete && this.autocomplete.displayWith ? this.autocomplete.displayWith(value) : value; // Simply falling back to an empty string if the display value is falsy does not work properly.\n    // The display value can also be the number zero and shouldn't fall back to an empty string.\n\n    const inputValue = toDisplay != null ? toDisplay : ''; // If it's used within a `MatFormField`, we should set it through the property so it can go\n    // through change detection.\n\n    if (this._formField) {\n      this._formField._control.value = inputValue;\n    } else {\n      this._element.nativeElement.value = inputValue;\n    }\n\n    this._previousValue = inputValue;\n  }\n  /**\n   * This method closes the panel, and if a value is specified, also sets the associated\n   * control to that value. It will also mark the control as dirty if this interaction\n   * stemmed from the user.\n   */\n\n\n  _setValueAndClose(event) {\n    const source = event && event.source;\n\n    if (source) {\n      this._clearPreviousSelectedOption(source);\n\n      this._setTriggerValue(source.value);\n\n      this._onChange(source.value);\n\n      this.autocomplete._emitSelectEvent(source);\n\n      this._element.nativeElement.focus();\n    }\n\n    this.closePanel();\n  }\n  /**\n   * Clear any previous selected option and emit a selection change event for this option\n   */\n\n\n  _clearPreviousSelectedOption(skip) {\n    this.autocomplete.options.forEach(option => {\n      if (option !== skip && option.selected) {\n        option.deselect();\n      }\n    });\n  }\n\n  _attachOverlay() {\n    if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatAutocompleteMissingPanelError();\n    }\n\n    let overlayRef = this._overlayRef;\n\n    if (!overlayRef) {\n      this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n        id: this._formField?.getLabelId()\n      });\n      overlayRef = this._overlay.create(this._getOverlayConfig());\n      this._overlayRef = overlayRef; // Use the `keydownEvents` in order to take advantage of\n      // the overlay event targeting provided by the CDK overlay.\n\n      overlayRef.keydownEvents().subscribe(event => {\n        // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n        // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n        if (event.keyCode === ESCAPE && !hasModifierKey(event) || event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey')) {\n          this._closeKeyEventStream.next();\n\n          this._resetActiveItem(); // We need to stop propagation, otherwise the event will eventually\n          // reach the input itself and cause the overlay to be reopened.\n\n\n          event.stopPropagation();\n          event.preventDefault();\n        }\n      });\n      this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n        if (this.panelOpen && overlayRef) {\n          overlayRef.updateSize({\n            width: this._getPanelWidth()\n          });\n        }\n      });\n    } else {\n      // Update the trigger, panel width and direction, in case anything has changed.\n      this._positionStrategy.setOrigin(this._getConnectedElement());\n\n      overlayRef.updateSize({\n        width: this._getPanelWidth()\n      });\n    }\n\n    if (overlayRef && !overlayRef.hasAttached()) {\n      overlayRef.attach(this._portal);\n      this._closingActionsSubscription = this._subscribeToClosingActions();\n    }\n\n    const wasOpen = this.panelOpen;\n\n    this.autocomplete._setVisibility();\n\n    this.autocomplete._isOpen = this._overlayAttached = true; // We need to do an extra `panelOpen` check in here, because the\n    // autocomplete won't be shown if there are no options.\n\n    if (this.panelOpen && wasOpen !== this.panelOpen) {\n      this.autocomplete.opened.emit();\n    }\n  }\n\n  _getOverlayConfig() {\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPosition(),\n      scrollStrategy: this._scrollStrategy(),\n      width: this._getPanelWidth(),\n      direction: this._dir,\n      panelClass: this._defaults?.overlayPanelClass\n    });\n  }\n\n  _getOverlayPosition() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getConnectedElement()).withFlexibleDimensions(false).withPush(false);\n\n    this._setStrategyPositions(strategy);\n\n    this._positionStrategy = strategy;\n    return strategy;\n  }\n  /** Sets the positions on a position strategy based on the directive's input state. */\n\n\n  _setStrategyPositions(positionStrategy) {\n    // Note that we provide horizontal fallback positions, even though by default the dropdown\n    // width matches the input, because consumers can override the width. See #18854.\n    const belowPositions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }]; // The overlay edge connected to the trigger should have squared corners, while\n    // the opposite end has rounded corners. We apply a CSS class to swap the\n    // border-radius based on the overlay position.\n\n    const panelClass = this._aboveClass;\n    const abovePositions = [{\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass\n    }];\n    let positions;\n\n    if (this.position === 'above') {\n      positions = abovePositions;\n    } else if (this.position === 'below') {\n      positions = belowPositions;\n    } else {\n      positions = [...belowPositions, ...abovePositions];\n    }\n\n    positionStrategy.withPositions(positions);\n  }\n\n  _getConnectedElement() {\n    if (this.connectedTo) {\n      return this.connectedTo.elementRef;\n    }\n\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n  }\n\n  _getPanelWidth() {\n    return this.autocomplete.panelWidth || this._getHostWidth();\n  }\n  /** Returns the width of the input element, so the panel width can match it. */\n\n\n  _getHostWidth() {\n    return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n  }\n  /**\n   * Resets the active item to -1 so arrow events will activate the\n   * correct options, or to 0 if the consumer opted into it.\n   */\n\n\n  _resetActiveItem() {\n    const autocomplete = this.autocomplete;\n\n    if (autocomplete.autoActiveFirstOption) {\n      // Note that we go through `setFirstItemActive`, rather than `setActiveItem(0)`, because\n      // the former will find the next enabled option, if the first one is disabled.\n      autocomplete._keyManager.setFirstItemActive();\n    } else {\n      autocomplete._keyManager.setActiveItem(-1);\n    }\n  }\n  /** Determines whether the panel can be opened. */\n\n\n  _canOpen() {\n    const element = this._element.nativeElement;\n    return !element.readOnly && !element.disabled && !this._autocompleteDisabled;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n  _getWindow() {\n    return this._document?.defaultView || window;\n  }\n  /** Scrolls to a particular option in the list. */\n\n\n  _scrollToOption(index) {\n    // Given that we are not actually focusing active options, we must manually adjust scroll\n    // to reveal options below the fold. First, we find the offset of the option from the top\n    // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n    // the panel height + the option height, so the active option will be just visible at the\n    // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n    // will become the offset. If that offset is visible within the panel already, the scrollTop is\n    // not adjusted.\n    const autocomplete = this.autocomplete;\n\n    const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n\n    if (index === 0 && labelCount === 1) {\n      // If we've got one group label before the option and we're at the top option,\n      // scroll the list to the top. This is better UX than scrolling the list to the\n      // top of the option, because it allows the user to read the top group's label.\n      autocomplete._setScrollTop(0);\n    } else if (autocomplete.panel) {\n      const option = autocomplete.options.toArray()[index];\n\n      if (option) {\n        const element = option._getHostElement();\n\n        const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n\n        autocomplete._setScrollTop(newScrollPosition);\n      }\n    }\n  }\n\n}\n\n_MatAutocompleteTriggerBase.ɵfac = function _MatAutocompleteTriggerBase_Factory(t) {\n  return new (t || _MatAutocompleteTriggerBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i2$1.Directionality, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 9), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(i3.ViewportRuler), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, 8));\n};\n\n_MatAutocompleteTriggerBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatAutocompleteTriggerBase,\n  inputs: {\n    autocomplete: [\"matAutocomplete\", \"autocomplete\"],\n    position: [\"matAutocompletePosition\", \"position\"],\n    connectedTo: [\"matAutocompleteConnectedTo\", \"connectedTo\"],\n    autocompleteAttribute: [\"autocomplete\", \"autocompleteAttribute\"],\n    autocompleteDisabled: [\"matAutocompleteDisabled\", \"autocompleteDisabled\"]\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatAutocompleteTriggerBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1$1.Overlay\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i2$1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i4.MatFormField,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_FORM_FIELD]\n      }, {\n        type: Host\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i3.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    autocomplete: [{\n      type: Input,\n      args: ['matAutocomplete']\n    }],\n    position: [{\n      type: Input,\n      args: ['matAutocompletePosition']\n    }],\n    connectedTo: [{\n      type: Input,\n      args: ['matAutocompleteConnectedTo']\n    }],\n    autocompleteAttribute: [{\n      type: Input,\n      args: ['autocomplete']\n    }],\n    autocompleteDisabled: [{\n      type: Input,\n      args: ['matAutocompleteDisabled']\n    }]\n  });\n})();\n\nclass MatAutocompleteTrigger extends _MatAutocompleteTriggerBase {\n  constructor() {\n    super(...arguments);\n    this._aboveClass = 'mat-autocomplete-panel-above';\n  }\n\n}\n\nMatAutocompleteTrigger.ɵfac = /* @__PURE__ */function () {\n  let ɵMatAutocompleteTrigger_BaseFactory;\n  return function MatAutocompleteTrigger_Factory(t) {\n    return (ɵMatAutocompleteTrigger_BaseFactory || (ɵMatAutocompleteTrigger_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocompleteTrigger)))(t || MatAutocompleteTrigger);\n  };\n}();\n\nMatAutocompleteTrigger.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatAutocompleteTrigger,\n  selectors: [[\"input\", \"matAutocomplete\", \"\"], [\"textarea\", \"matAutocomplete\", \"\"]],\n  hostAttrs: [1, \"mat-autocomplete-trigger\"],\n  hostVars: 7,\n  hostBindings: function MatAutocompleteTrigger_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focusin\", function MatAutocompleteTrigger_focusin_HostBindingHandler() {\n        return ctx._handleFocus();\n      })(\"blur\", function MatAutocompleteTrigger_blur_HostBindingHandler() {\n        return ctx._onTouched();\n      })(\"input\", function MatAutocompleteTrigger_input_HostBindingHandler($event) {\n        return ctx._handleInput($event);\n      })(\"keydown\", function MatAutocompleteTrigger_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"click\", function MatAutocompleteTrigger_click_HostBindingHandler() {\n        return ctx._handleClick();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"autocomplete\", ctx.autocompleteAttribute)(\"role\", ctx.autocompleteDisabled ? null : \"combobox\")(\"aria-autocomplete\", ctx.autocompleteDisabled ? null : \"list\")(\"aria-activedescendant\", ctx.panelOpen && ctx.activeOption ? ctx.activeOption.id : null)(\"aria-expanded\", ctx.autocompleteDisabled ? null : ctx.panelOpen.toString())(\"aria-owns\", ctx.autocompleteDisabled || !ctx.panelOpen ? null : ctx.autocomplete == null ? null : ctx.autocomplete.id)(\"aria-haspopup\", ctx.autocompleteDisabled ? null : \"listbox\");\n    }\n  },\n  exportAs: [\"matAutocompleteTrigger\"],\n  features: [i0.ɵɵProvidersFeature([MAT_AUTOCOMPLETE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n      host: {\n        'class': 'mat-autocomplete-trigger',\n        '[attr.autocomplete]': 'autocompleteAttribute',\n        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n        '[attr.aria-owns]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n        // a little earlier. This avoids issues where IE delays the focusing of the input.\n        '(focusin)': '_handleFocus()',\n        '(blur)': '_onTouched()',\n        '(input)': '_handleInput($event)',\n        '(keydown)': '_handleKeydown($event)',\n        '(click)': '_handleClick()'\n      },\n      exportAs: 'matAutocompleteTrigger',\n      providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatAutocompleteModule {}\n\nMatAutocompleteModule.ɵfac = function MatAutocompleteModule_Factory(t) {\n  return new (t || MatAutocompleteModule)();\n};\n\nMatAutocompleteModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatAutocompleteModule\n});\nMatAutocompleteModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n  imports: [[OverlayModule, MatOptionModule, MatCommonModule, CommonModule], CdkScrollableModule, MatOptionModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule],\n      exports: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin, CdkScrollableModule, MatOptionModule, MatCommonModule],\n      declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n      providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, _MatAutocompleteBase, _MatAutocompleteOriginBase, _MatAutocompleteTriggerBase, getMatAutocompleteMissingPanelError };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/material/fesm2020/autocomplete.mjs"], "names": ["ActiveDescendantKeyManager", "coerceBooleanProperty", "coerce<PERSON><PERSON><PERSON><PERSON><PERSON>", "i1", "_getEventTarget", "i0", "InjectionToken", "EventEmitter", "TemplateRef", "Directive", "Inject", "ViewChild", "Input", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "forwardRef", "Optional", "Host", "NgModule", "mixinDisableRipple", "MAT_OPTION_PARENT_COMPONENT", "MAT_OPTGROUP", "MatOption", "MatOptionSelectionChange", "_countGroupLabelsBeforeOption", "_getOptionScrollPosition", "MatOptionModule", "MatCommonModule", "Subscription", "Subject", "defer", "merge", "of", "fromEvent", "i2", "DOCUMENT", "CommonModule", "i1$1", "Overlay", "OverlayConfig", "OverlayModule", "i3", "CdkScrollableModule", "i2$1", "hasModifierKey", "ESCAPE", "ENTER", "UP_ARROW", "DOWN_ARROW", "TAB", "TemplatePortal", "NG_VALUE_ACCESSOR", "i4", "MAT_FORM_FIELD", "startWith", "switchMap", "take", "filter", "map", "tap", "delay", "_uniqueAutocompleteIdCounter", "MatAutocompleteSelectedEvent", "constructor", "source", "option", "_MatAutocompleteMixinBase", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY", "autoActiveFirstOption", "_MatAutocompleteBase", "_changeDetectorRef", "_elementRef", "defaults", "platform", "_activeOptionChanges", "EMPTY", "showPanel", "_isOpen", "displayWith", "optionSelected", "opened", "closed", "optionActivated", "_classList", "id", "inertGroups", "SAFARI", "_autoActiveFirstOption", "isOpen", "value", "classList", "length", "reduce", "className", "_setVisibilityClasses", "nativeElement", "ngAfterContentInit", "_keyManager", "options", "withWrap", "change", "subscribe", "index", "emit", "toArray", "_setVisibility", "ngOnDestroy", "unsubscribe", "_setScrollTop", "scrollTop", "panel", "_getScrollTop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_emitSelectEvent", "event", "_getPanelAriaLabe<PERSON>by", "labelId", "aria<PERSON><PERSON><PERSON>", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_visibleClass", "_hiddenClass", "ɵfac", "ChangeDetectorRef", "ElementRef", "Platform", "ɵdir", "type", "undefined", "decorators", "args", "template", "static", "panelWidth", "MatAutocomplete", "arguments", "ɵcmp", "provide", "useExisting", "Ng<PERSON><PERSON>", "selector", "encapsulation", "None", "changeDetection", "OnPush", "exportAs", "inputs", "host", "providers", "styles", "optionGroups", "descendants", "_MatAutocompleteOriginBase", "elementRef", "MatAutocompleteOrigin", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "MAT_AUTOCOMPLETE_VALUE_ACCESSOR", "MatAutocompleteTrigger", "multi", "getMatAutocompleteMissingPanelError", "Error", "_MatAutocompleteTriggerBase", "_element", "_overlay", "_viewContainerRef", "_zone", "scrollStrategy", "_dir", "_formField", "_document", "_viewportRuler", "_defaults", "_componentDestroyed", "_autocompleteDisabled", "_manuallyFloatingLabel", "_viewportSubscription", "_canOpenOnNextFocus", "_closeKeyEventStream", "_windowBlurHandler", "activeElement", "panelOpen", "_onChange", "_onTouched", "position", "autocompleteAttribute", "_overlayAttached", "optionSelections", "autocomplete", "changes", "pipe", "onSelectionChange", "onStable", "_scrollStrategy", "autocompleteDisabled", "ngAfterViewInit", "window", "_getWindow", "runOutsideAngular", "addEventListener", "ngOnChanges", "_positionStrategy", "_setStrategyPositions", "_overlayRef", "updatePosition", "removeEventListener", "_destroyPanel", "complete", "openPanel", "_attachOverlay", "_floatLabel", "closePanel", "_resetLabel", "run", "has<PERSON>tta<PERSON>", "detach", "_closingActionsSubscription", "detectChanges", "panelClosingActions", "tabOut", "_getOutsideClickStream", "detachments", "activeOption", "activeItem", "clickTarget", "formField", "customOrigin", "connectedTo", "contains", "overlayElement", "writeValue", "Promise", "resolve", "then", "_setTriggerValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "disabled", "_handleKeydown", "keyCode", "hasModifier", "preventDefault", "_selectViaInteraction", "_resetActiveItem", "prevActiveItem", "isArrowKey", "onKeydown", "_canOpen", "_scrollToOption", "activeItemIndex", "_handleInput", "target", "parseFloat", "_previousValue", "_handleFocus", "_handleClick", "shouldAnimate", "floatLabel", "_animateAndLockLabel", "_subscribeToClosingActions", "firstStable", "optionChanges", "reapplyLastPosition", "was<PERSON><PERSON>", "_setValueAndClose", "dispose", "toDisplay", "inputValue", "_control", "_clearPreviousSelectedOption", "focus", "skip", "for<PERSON>ach", "selected", "deselect", "ngDevMode", "overlayRef", "_portal", "getLabelId", "create", "_getOverlayConfig", "keydownEvents", "next", "stopPropagation", "updateSize", "width", "_get<PERSON><PERSON><PERSON><PERSON>idth", "<PERSON><PERSON><PERSON><PERSON>", "_getConnectedElement", "attach", "positionStrategy", "_getOverlayPosition", "direction", "panelClass", "overlayPanelClass", "strategy", "flexibleConnectedTo", "withFlexibleDimensions", "with<PERSON><PERSON>", "belowPositions", "originX", "originY", "overlayX", "overlayY", "_aboveClass", "abovePositions", "positions", "withPositions", "getConnectedOverlayOrigin", "_getHostWidth", "getBoundingClientRect", "setFirstItemActive", "setActiveItem", "element", "readOnly", "defaultView", "labelCount", "_getHostElement", "newScrollPosition", "offsetTop", "offsetHeight", "ViewContainerRef", "NgZone", "Directionality", "ViewportRuler", "MatFormField", "MatAutocompleteModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "mappings": "AAAA,SAASA,0BAAT,QAA2C,mBAA3C;AACA,SAASC,qBAAT,EAAgCC,iBAAhC,QAAyD,uBAAzD;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,eAAT,QAAgC,uBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,YAAzB,EAAuCC,WAAvC,EAAoDC,SAApD,EAA+DC,MAA/D,EAAuEC,SAAvE,EAAkFC,KAAlF,EAAyFC,MAAzF,EAAiGC,SAAjG,EAA4GC,iBAA5G,EAA+HC,uBAA/H,EAAwJC,eAAxJ,EAAyKC,UAAzK,EAAqLC,QAArL,EAA+LC,IAA/L,EAAqMC,QAArM,QAAqN,eAArN;AACA,SAASC,kBAAT,EAA6BC,2BAA7B,EAA0DC,YAA1D,EAAwEC,SAAxE,EAAmFC,wBAAnF,EAA6GC,6BAA7G,EAA4IC,wBAA5I,EAAsKC,eAAtK,EAAuLC,eAAvL,QAA8M,wBAA9M;AACA,SAASC,YAAT,EAAuBC,OAAvB,EAAgCC,KAAhC,EAAuCC,KAAvC,EAA8CC,EAA9C,EAAkDC,SAAlD,QAAmE,MAAnE;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,IAAZ,MAAsB,sBAAtB;AACA,SAASC,OAAT,EAAkBC,aAAlB,EAAiCC,aAAjC,QAAsD,sBAAtD;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,mBAAT,QAAoC,wBAApC;AACA,OAAO,KAAKC,IAAZ,MAAsB,mBAAtB;AACA,SAASC,cAAT,EAAyBC,MAAzB,EAAiCC,KAAjC,EAAwCC,QAAxC,EAAkDC,UAAlD,EAA8DC,GAA9D,QAAyE,uBAAzE;AACA,SAASC,cAAT,QAA+B,qBAA/B;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,OAAO,KAAKC,EAAZ,MAAoB,8BAApB;AACA,SAASC,cAAT,QAA+B,8BAA/B;AACA,SAASC,SAAT,EAAoBC,SAApB,EAA+BC,IAA/B,EAAqCC,MAArC,EAA6CC,GAA7C,EAAkDC,GAAlD,EAAuDC,KAAvD,QAAoE,gBAApE;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;;;;AA0IuG1D,IAAAA,EA6C6e,+B;AA7C7eA,IAAAA,EA6C8uB,gB;AA7C9uBA,IAAAA,EA6C2wB,e;;;;;mBA7C3wBA,E;AAAAA,IAAAA,EA6CojB,0D;AA7CpjBA,IAAAA,EA6CwkB,uH;;;;;AAtL/qB,IAAI2D,4BAA4B,GAAG,CAAnC;AACA;;AACA,MAAMC,4BAAN,CAAmC;AAC/BC,EAAAA,WAAW;AACX;AACAC,EAAAA,MAFW;AAGX;AACAC,EAAAA,MAJW,EAIH;AACJ,SAAKD,MAAL,GAAcA,MAAd;AACA,SAAKC,MAAL,GAAcA,MAAd;AACH;;AAR8B,C,CAUnC;;AACA;;;AACA,MAAMC,yBAAyB,GAAG/C,kBAAkB,CAAC,MAAM,EAAP,CAApD;AAEA;;;AACA,MAAMgD,gCAAgC,GAAG,IAAIhE,cAAJ,CAAmB,kCAAnB,EAAuD;AAC5FiE,EAAAA,UAAU,EAAE,MADgF;AAE5FC,EAAAA,OAAO,EAAEC;AAFmF,CAAvD,CAAzC;AAIA;;AACA,SAASA,wCAAT,GAAoD;AAChD,SAAO;AAAEC,IAAAA,qBAAqB,EAAE;AAAzB,GAAP;AACH;AACD;;;AACA,MAAMC,oBAAN,SAAmCN,yBAAnC,CAA6D;AACzDH,EAAAA,WAAW,CAACU,kBAAD,EAAqBC,WAArB,EAAkCC,QAAlC,EAA4CC,QAA5C,EAAsD;AAC7D;AACA,SAAKH,kBAAL,GAA0BA,kBAA1B;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKG,oBAAL,GAA4BjD,YAAY,CAACkD,KAAzC;AACA;;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,OAAL,GAAe,KAAf;AACA;;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA;;AACA,SAAKC,cAAL,GAAsB,IAAI9E,YAAJ,EAAtB;AACA;;AACA,SAAK+E,MAAL,GAAc,IAAI/E,YAAJ,EAAd;AACA;;AACA,SAAKgF,MAAL,GAAc,IAAIhF,YAAJ,EAAd;AACA;;AACA,SAAKiF,eAAL,GAAuB,IAAIjF,YAAJ,EAAvB;AACA,SAAKkF,UAAL,GAAkB,EAAlB;AACA;;AACA,SAAKC,EAAL,GAAW,oBAAmB1B,4BAA4B,EAAG,EAA7D,CApB6D,CAqB7D;AACA;AACA;AACA;;AACA,SAAK2B,WAAL,GAAmBZ,QAAQ,EAAEa,MAAV,IAAoB,KAAvC;AACA,SAAKC,sBAAL,GAA8B,CAAC,CAACf,QAAQ,CAACJ,qBAAzC;AACH;AACD;;;AACU,MAANoB,MAAM,GAAG;AACT,WAAO,KAAKX,OAAL,IAAgB,KAAKD,SAA5B;AACH;AACD;AACJ;AACA;AACA;;;AAC6B,MAArBR,qBAAqB,GAAG;AACxB,WAAO,KAAKmB,sBAAZ;AACH;;AACwB,MAArBnB,qBAAqB,CAACqB,KAAD,EAAQ;AAC7B,SAAKF,sBAAL,GAA8B5F,qBAAqB,CAAC8F,KAAD,CAAnD;AACH;AACD;AACJ;AACA;AACA;;;AACiB,MAATC,SAAS,CAACD,KAAD,EAAQ;AACjB,QAAIA,KAAK,IAAIA,KAAK,CAACE,MAAnB,EAA2B;AACvB,WAAKR,UAAL,GAAkBvF,iBAAiB,CAAC6F,KAAD,CAAjB,CAAyBG,MAAzB,CAAgC,CAACF,SAAD,EAAYG,SAAZ,KAA0B;AACxEH,QAAAA,SAAS,CAACG,SAAD,CAAT,GAAuB,IAAvB;AACA,eAAOH,SAAP;AACH,OAHiB,EAGf,EAHe,CAAlB;AAIH,KALD,MAMK;AACD,WAAKP,UAAL,GAAkB,EAAlB;AACH;;AACD,SAAKW,qBAAL,CAA2B,KAAKX,UAAhC;;AACA,SAAKZ,WAAL,CAAiBwB,aAAjB,CAA+BF,SAA/B,GAA2C,EAA3C;AACH;;AACDG,EAAAA,kBAAkB,GAAG;AACjB,SAAKC,WAAL,GAAmB,IAAIvG,0BAAJ,CAA+B,KAAKwG,OAApC,EAA6CC,QAA7C,EAAnB;AACA,SAAKzB,oBAAL,GAA4B,KAAKuB,WAAL,CAAiBG,MAAjB,CAAwBC,SAAxB,CAAkCC,KAAK,IAAI;AACnE,UAAI,KAAKd,MAAT,EAAiB;AACb,aAAKN,eAAL,CAAqBqB,IAArB,CAA0B;AAAE1C,UAAAA,MAAM,EAAE,IAAV;AAAgBC,UAAAA,MAAM,EAAE,KAAKoC,OAAL,CAAaM,OAAb,GAAuBF,KAAvB,KAAiC;AAAzD,SAA1B;AACH;AACJ,KAJ2B,CAA5B,CAFiB,CAOjB;;AACA,SAAKG,cAAL;AACH;;AACDC,EAAAA,WAAW,GAAG;AACV,SAAKhC,oBAAL,CAA0BiC,WAA1B;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,aAAa,CAACC,SAAD,EAAY;AACrB,QAAI,KAAKC,KAAT,EAAgB;AACZ,WAAKA,KAAL,CAAWf,aAAX,CAAyBc,SAAzB,GAAqCA,SAArC;AACH;AACJ;AACD;;;AACAE,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAKD,KAAL,GAAa,KAAKA,KAAL,CAAWf,aAAX,CAAyBc,SAAtC,GAAkD,CAAzD;AACH;AACD;;;AACAJ,EAAAA,cAAc,GAAG;AACb,SAAK7B,SAAL,GAAiB,CAAC,CAAC,KAAKsB,OAAL,CAAaP,MAAhC;;AACA,SAAKG,qBAAL,CAA2B,KAAKX,UAAhC;;AACA,SAAKb,kBAAL,CAAwB0C,YAAxB;AACH;AACD;;;AACAC,EAAAA,gBAAgB,CAACnD,MAAD,EAAS;AACrB,UAAMoD,KAAK,GAAG,IAAIvD,4BAAJ,CAAiC,IAAjC,EAAuCG,MAAvC,CAAd;AACA,SAAKiB,cAAL,CAAoBwB,IAApB,CAAyBW,KAAzB;AACH;AACD;;;AACAC,EAAAA,uBAAuB,CAACC,OAAD,EAAU;AAC7B,QAAI,KAAKC,SAAT,EAAoB;AAChB,aAAO,IAAP;AACH;;AACD,UAAMC,eAAe,GAAGF,OAAO,GAAGA,OAAO,GAAG,GAAb,GAAmB,EAAlD;AACA,WAAO,KAAKG,cAAL,GAAsBD,eAAe,GAAG,KAAKC,cAA7C,GAA8DH,OAArE;AACH;AACD;;;AACAtB,EAAAA,qBAAqB,CAACJ,SAAD,EAAY;AAC7BA,IAAAA,SAAS,CAAC,KAAK8B,aAAN,CAAT,GAAgC,KAAK5C,SAArC;AACAc,IAAAA,SAAS,CAAC,KAAK+B,YAAN,CAAT,GAA+B,CAAC,KAAK7C,SAArC;AACH;;AA7GwD;;AA+G7DP,oBAAoB,CAACqD,IAArB;AAAA,mBAAiHrD,oBAAjH,EAAuGtE,EAAvG,mBAAuJA,EAAE,CAAC4H,iBAA1J,GAAuG5H,EAAvG,mBAAwLA,EAAE,CAAC6H,UAA3L,GAAuG7H,EAAvG,mBAAkNiE,gCAAlN,GAAuGjE,EAAvG,mBAA+PF,EAAE,CAACgI,QAAlQ;AAAA;;AACAxD,oBAAoB,CAACyD,IAArB,kBADuG/H,EACvG;AAAA,QAAqGsE,oBAArG;AAAA;AAAA;AADuGtE,MAAAA,EACvG,aAAwiBG,WAAxiB;AADuGH,MAAAA,EACvG;AAAA;;AAAA;AAAA;;AADuGA,MAAAA,EACvG,qBADuGA,EACvG;AADuGA,MAAAA,EACvG,qBADuGA,EACvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aADuGA,EACvG;AAAA;;AACA;AAAA,qDAFuGA,EAEvG,mBAA2FsE,oBAA3F,EAA6H,CAAC;AAClH0D,IAAAA,IAAI,EAAE5H;AAD4G,GAAD,CAA7H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE4H,MAAAA,IAAI,EAAEhI,EAAE,CAAC4H;AAAX,KAAD,EAAiC;AAAEI,MAAAA,IAAI,EAAEhI,EAAE,CAAC6H;AAAX,KAAjC,EAA0D;AAAEG,MAAAA,IAAI,EAAEC,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACvHF,QAAAA,IAAI,EAAE3H,MADiH;AAEvH8H,QAAAA,IAAI,EAAE,CAAClE,gCAAD;AAFiH,OAAD;AAA/B,KAA1D,EAG3B;AAAE+D,MAAAA,IAAI,EAAElI,EAAE,CAACgI;AAAX,KAH2B,CAAP;AAGK,GAL/C,EAKiE;AAAEM,IAAAA,QAAQ,EAAE,CAAC;AAC9DJ,MAAAA,IAAI,EAAE1H,SADwD;AAE9D6H,MAAAA,IAAI,EAAE,CAAChI,WAAD,EAAc;AAAEkI,QAAAA,MAAM,EAAE;AAAV,OAAd;AAFwD,KAAD,CAAZ;AAGjDtB,IAAAA,KAAK,EAAE,CAAC;AACRiB,MAAAA,IAAI,EAAE1H,SADE;AAER6H,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFE,KAAD,CAH0C;AAMjDb,IAAAA,SAAS,EAAE,CAAC;AACZU,MAAAA,IAAI,EAAEzH,KADM;AAEZ4H,MAAAA,IAAI,EAAE,CAAC,YAAD;AAFM,KAAD,CANsC;AASjDX,IAAAA,cAAc,EAAE,CAAC;AACjBQ,MAAAA,IAAI,EAAEzH,KADW;AAEjB4H,MAAAA,IAAI,EAAE,CAAC,iBAAD;AAFW,KAAD,CATiC;AAYjDpD,IAAAA,WAAW,EAAE,CAAC;AACdiD,MAAAA,IAAI,EAAEzH;AADQ,KAAD,CAZoC;AAcjD8D,IAAAA,qBAAqB,EAAE,CAAC;AACxB2D,MAAAA,IAAI,EAAEzH;AADkB,KAAD,CAd0B;AAgBjD+H,IAAAA,UAAU,EAAE,CAAC;AACbN,MAAAA,IAAI,EAAEzH;AADO,KAAD,CAhBqC;AAkBjDyE,IAAAA,cAAc,EAAE,CAAC;AACjBgD,MAAAA,IAAI,EAAExH;AADW,KAAD,CAlBiC;AAoBjDyE,IAAAA,MAAM,EAAE,CAAC;AACT+C,MAAAA,IAAI,EAAExH;AADG,KAAD,CApByC;AAsBjD0E,IAAAA,MAAM,EAAE,CAAC;AACT8C,MAAAA,IAAI,EAAExH;AADG,KAAD,CAtByC;AAwBjD2E,IAAAA,eAAe,EAAE,CAAC;AAClB6C,MAAAA,IAAI,EAAExH;AADY,KAAD,CAxBgC;AA0BjDmF,IAAAA,SAAS,EAAE,CAAC;AACZqC,MAAAA,IAAI,EAAEzH,KADM;AAEZ4H,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFM,KAAD;AA1BsC,GALjE;AAAA;;AAmCA,MAAMI,eAAN,SAA8BjE,oBAA9B,CAAmD;AAC/CT,EAAAA,WAAW,GAAG;AACV,UAAM,GAAG2E,SAAT;AACA,SAAKf,aAAL,GAAqB,0BAArB;AACA,SAAKC,YAAL,GAAoB,yBAApB;AACH;;AAL8C;;AAOnDa,eAAe,CAACZ,IAAhB;AAAA;AAAA;AAAA,4EA5CuG3H,EA4CvG,uBAA4GuI,eAA5G,SAA4GA,eAA5G;AAAA;AAAA;;AACAA,eAAe,CAACE,IAAhB,kBA7CuGzI,EA6CvG;AAAA,QAAgGuI,eAAhG;AAAA;AAAA;AAAA;AA7CuGvI,MAAAA,EA6CvG,0BAAmXmB,YAAnX;AA7CuGnB,MAAAA,EA6CvG,0BAA4boB,SAA5b;AAAA;;AAAA;AAAA;;AA7CuGpB,MAAAA,EA6CvG,qBA7CuGA,EA6CvG;AA7CuGA,MAAAA,EA6CvG,qBA7CuGA,EA6CvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA7CuGA,EA6CvG,oBAAoP,CAAC;AAAE0I,IAAAA,OAAO,EAAExH,2BAAX;AAAwCyH,IAAAA,WAAW,EAAEJ;AAArD,GAAD,CAApP,GA7CuGvI,EA6CvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA7CuGA,MAAAA,EA6CvG;AA7CuGA,MAAAA,EA6Cqc,2EAA5iB;AAAA;AAAA;AAAA,eAA0kDgC,EAAE,CAAC4G,OAA7kD;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDA9CuG5I,EA8CvG,mBAA2FuI,eAA3F,EAAwH,CAAC;AAC7GP,IAAAA,IAAI,EAAEvH,SADuG;AAE7G0H,IAAAA,IAAI,EAAE,CAAC;AAAEU,MAAAA,QAAQ,EAAE,kBAAZ;AAAgCC,MAAAA,aAAa,EAAEpI,iBAAiB,CAACqI,IAAjE;AAAuEC,MAAAA,eAAe,EAAErI,uBAAuB,CAACsI,MAAhH;AAAwHC,MAAAA,QAAQ,EAAE,iBAAlI;AAAqJC,MAAAA,MAAM,EAAE,CAAC,eAAD,CAA7J;AAAgLC,MAAAA,IAAI,EAAE;AACjL,iBAAS;AADwK,OAAtL;AAEIC,MAAAA,SAAS,EAAE,CAAC;AAAEX,QAAAA,OAAO,EAAExH,2BAAX;AAAwCyH,QAAAA,WAAW,EAAEJ;AAArD,OAAD,CAFf;AAEyFH,MAAAA,QAAQ,EAAE,gWAFnG;AAEqckB,MAAAA,MAAM,EAAE,CAAC,4pBAAD;AAF7c,KAAD;AAFuG,GAAD,CAAxH,QAK4B;AAAEC,IAAAA,YAAY,EAAE,CAAC;AAC7BvB,MAAAA,IAAI,EAAEpH,eADuB;AAE7BuH,MAAAA,IAAI,EAAE,CAAChH,YAAD,EAAe;AAAEqI,QAAAA,WAAW,EAAE;AAAf,OAAf;AAFuB,KAAD,CAAhB;AAGZrD,IAAAA,OAAO,EAAE,CAAC;AACV6B,MAAAA,IAAI,EAAEpH,eADI;AAEVuH,MAAAA,IAAI,EAAE,CAAC/G,SAAD,EAAY;AAAEoI,QAAAA,WAAW,EAAE;AAAf,OAAZ;AAFI,KAAD;AAHG,GAL5B;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,0BAAN,CAAiC;AAC7B5F,EAAAA,WAAW;AACX;AACA6F,EAAAA,UAFW,EAEC;AACR,SAAKA,UAAL,GAAkBA,UAAlB;AACH;;AAL4B;;AAOjCD,0BAA0B,CAAC9B,IAA3B;AAAA,mBAAuH8B,0BAAvH,EA1EuGzJ,EA0EvG,mBAAmKA,EAAE,CAAC6H,UAAtK;AAAA;;AACA4B,0BAA0B,CAAC1B,IAA3B,kBA3EuG/H,EA2EvG;AAAA,QAA2GyJ;AAA3G;;AACA;AAAA,qDA5EuGzJ,EA4EvG,mBAA2FyJ,0BAA3F,EAAmI,CAAC;AACxHzB,IAAAA,IAAI,EAAE5H;AADkH,GAAD,CAAnI,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE4H,MAAAA,IAAI,EAAEhI,EAAE,CAAC6H;AAAX,KAAD,CAAP;AAAmC,GAF7E;AAAA;AAGA;AACA;AACA;AACA;;;AACA,MAAM8B,qBAAN,SAAoCF,0BAApC,CAA+D;;AAE/DE,qBAAqB,CAAChC,IAAtB;AAAA;AAAA;AAAA,wFArFuG3H,EAqFvG,uBAAkH2J,qBAAlH,SAAkHA,qBAAlH;AAAA;AAAA;;AACAA,qBAAqB,CAAC5B,IAAtB,kBAtFuG/H,EAsFvG;AAAA,QAAsG2J,qBAAtG;AAAA;AAAA;AAAA,aAtFuG3J,EAsFvG;AAAA;;AACA;AAAA,qDAvFuGA,EAuFvG,mBAA2F2J,qBAA3F,EAA8H,CAAC;AACnH3B,IAAAA,IAAI,EAAE5H,SAD6G;AAEnH+H,IAAAA,IAAI,EAAE,CAAC;AACCU,MAAAA,QAAQ,EAAE,yBADX;AAECK,MAAAA,QAAQ,EAAE;AAFX,KAAD;AAF6G,GAAD,CAA9H;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMU,gCAAgC,GAAG,IAAI3J,cAAJ,CAAmB,kCAAnB,CAAzC;AACA;;AACA,SAAS4J,wCAAT,CAAkDC,OAAlD,EAA2D;AACvD,SAAO,MAAMA,OAAO,CAACC,gBAAR,CAAyBC,UAAzB,EAAb;AACH;AACD;;;AACA,MAAMC,iDAAiD,GAAG;AACtDvB,EAAAA,OAAO,EAAEkB,gCAD6C;AAEtDM,EAAAA,IAAI,EAAE,CAAC9H,OAAD,CAFgD;AAGtD+H,EAAAA,UAAU,EAAEN;AAH0C,CAA1D;AAKA;AACA;AACA;AACA;;AACA,MAAMO,+BAA+B,GAAG;AACpC1B,EAAAA,OAAO,EAAEzF,iBAD2B;AAEpC0F,EAAAA,WAAW,EAAE9H,UAAU,CAAC,MAAMwJ,sBAAP,CAFa;AAGpCC,EAAAA,KAAK,EAAE;AAH6B,CAAxC;AAKA;AACA;AACA;AACA;;AACA,SAASC,mCAAT,GAA+C;AAC3C,SAAOC,KAAK,CAAC,qEACT,4EADS,GAET,iEAFQ,CAAZ;AAGH;AACD;;;AACA,MAAMC,2BAAN,CAAkC;AAC9B5G,EAAAA,WAAW,CAAC6G,QAAD,EAAWC,QAAX,EAAqBC,iBAArB,EAAwCC,KAAxC,EAA+CtG,kBAA/C,EAAmEuG,cAAnE,EAAmFC,IAAnF,EAAyFC,UAAzF,EAAqGC,SAArG,EAAgHC,cAAhH,EAAgIC,SAAhI,EAA2I;AAClJ,SAAKT,QAAL,GAAgBA,QAAhB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKtG,kBAAL,GAA0BA,kBAA1B;AACA,SAAKwG,IAAL,GAAYA,IAAZ;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,cAAL,GAAsBA,cAAtB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,mBAAL,GAA2B,KAA3B;AACA,SAAKC,qBAAL,GAA6B,KAA7B;AACA;;AACA,SAAKC,sBAAL,GAA8B,KAA9B;AACA;;AACA,SAAKC,qBAAL,GAA6B7J,YAAY,CAACkD,KAA1C;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAK4G,mBAAL,GAA2B,IAA3B;AACA;;AACA,SAAKC,oBAAL,GAA4B,IAAI9J,OAAJ,EAA5B;AACA;AACR;AACA;AACA;;AACQ,SAAK+J,kBAAL,GAA0B,MAAM;AAC5B;AACA;AACA;AACA,WAAKF,mBAAL,GACI,KAAKP,SAAL,CAAeU,aAAf,KAAiC,KAAKjB,QAAL,CAAc1E,aAA/C,IAAgE,KAAK4F,SADzE;AAEH,KAND;AAOA;;;AACA,SAAKC,SAAL,GAAiB,MAAM,CAAG,CAA1B;AACA;;;AACA,SAAKC,UAAL,GAAkB,MAAM,CAAG,CAA3B;AACA;AACR;AACA;AACA;AACA;AACA;AACA;;;AACQ,SAAKC,QAAL,GAAgB,MAAhB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,qBAAL,GAA6B,KAA7B;AACA,SAAKC,gBAAL,GAAwB,KAAxB;AACA;;AACA,SAAKC,gBAAL,GAAwBtK,KAAK,CAAC,MAAM;AAChC,YAAMuE,OAAO,GAAG,KAAKgG,YAAL,GAAoB,KAAKA,YAAL,CAAkBhG,OAAtC,GAAgD,IAAhE;;AACA,UAAIA,OAAJ,EAAa;AACT,eAAOA,OAAO,CAACiG,OAAR,CAAgBC,IAAhB,CAAqBjJ,SAAS,CAAC+C,OAAD,CAA9B,EAAyC9C,SAAS,CAAC,MAAMxB,KAAK,CAAC,GAAGsE,OAAO,CAAC3C,GAAR,CAAYO,MAAM,IAAIA,MAAM,CAACuI,iBAA7B,CAAJ,CAAZ,CAAlD,CAAP;AACH,OAJ+B,CAKhC;AACA;;;AACA,aAAO,KAAKzB,KAAL,CAAW0B,QAAX,CAAoBF,IAApB,CAAyB/I,IAAI,CAAC,CAAD,CAA7B,EAAkCD,SAAS,CAAC,MAAM,KAAK6I,gBAAZ,CAA3C,CAAP;AACH,KAR4B,CAA7B;AASA,SAAKM,eAAL,GAAuB1B,cAAvB;AACH;AACD;AACJ;AACA;AACA;;;AAC4B,MAApB2B,oBAAoB,GAAG;AACvB,WAAO,KAAKpB,qBAAZ;AACH;;AACuB,MAApBoB,oBAAoB,CAAC/G,KAAD,EAAQ;AAC5B,SAAK2F,qBAAL,GAA6BzL,qBAAqB,CAAC8F,KAAD,CAAlD;AACH;;AACDgH,EAAAA,eAAe,GAAG;AACd,UAAMC,MAAM,GAAG,KAAKC,UAAL,EAAf;;AACA,QAAI,OAAOD,MAAP,KAAkB,WAAtB,EAAmC;AAC/B,WAAK9B,KAAL,CAAWgC,iBAAX,CAA6B,MAAMF,MAAM,CAACG,gBAAP,CAAwB,MAAxB,EAAgC,KAAKpB,kBAArC,CAAnC;AACH;AACJ;;AACDqB,EAAAA,WAAW,CAACX,OAAD,EAAU;AACjB,QAAIA,OAAO,CAAC,UAAD,CAAP,IAAuB,KAAKY,iBAAhC,EAAmD;AAC/C,WAAKC,qBAAL,CAA2B,KAAKD,iBAAhC;;AACA,UAAI,KAAKpB,SAAT,EAAoB;AAChB,aAAKsB,WAAL,CAAiBC,cAAjB;AACH;AACJ;AACJ;;AACDxG,EAAAA,WAAW,GAAG;AACV,UAAMgG,MAAM,GAAG,KAAKC,UAAL,EAAf;;AACA,QAAI,OAAOD,MAAP,KAAkB,WAAtB,EAAmC;AAC/BA,MAAAA,MAAM,CAACS,mBAAP,CAA2B,MAA3B,EAAmC,KAAK1B,kBAAxC;AACH;;AACD,SAAKH,qBAAL,CAA2B3E,WAA3B;;AACA,SAAKwE,mBAAL,GAA2B,IAA3B;;AACA,SAAKiC,aAAL;;AACA,SAAK5B,oBAAL,CAA0B6B,QAA1B;AACH;AACD;;;AACa,MAAT1B,SAAS,GAAG;AACZ,WAAO,KAAKK,gBAAL,IAAyB,KAAKE,YAAL,CAAkBtH,SAAlD;AACH;AACD;;;AACA0I,EAAAA,SAAS,GAAG;AACR,SAAKC,cAAL;;AACA,SAAKC,WAAL;AACH;AACD;;;AACAC,EAAAA,UAAU,GAAG;AACT,SAAKC,WAAL;;AACA,QAAI,CAAC,KAAK1B,gBAAV,EAA4B;AACxB;AACH;;AACD,QAAI,KAAKL,SAAT,EAAoB;AAChB;AACA;AACA;AACA;AACA,WAAKf,KAAL,CAAW+C,GAAX,CAAe,MAAM;AACjB,aAAKzB,YAAL,CAAkBjH,MAAlB,CAAyBsB,IAAzB;AACH,OAFD;AAGH;;AACD,SAAK2F,YAAL,CAAkBrH,OAAlB,GAA4B,KAAKmH,gBAAL,GAAwB,KAApD;;AACA,QAAI,KAAKiB,WAAL,IAAoB,KAAKA,WAAL,CAAiBW,WAAjB,EAAxB,EAAwD;AACpD,WAAKX,WAAL,CAAiBY,MAAjB;;AACA,WAAKC,2BAAL,CAAiCnH,WAAjC;AACH,KAlBQ,CAmBT;AACA;;;AACA,QAAI,CAAC,KAAKwE,mBAAV,EAA+B;AAC3B;AACA;AACA;AACA;AACA,WAAK7G,kBAAL,CAAwByJ,aAAxB;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIb,EAAAA,cAAc,GAAG;AACb,QAAI,KAAKlB,gBAAT,EAA2B;AACvB,WAAKiB,WAAL,CAAiBC,cAAjB;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AAC2B,MAAnBc,mBAAmB,GAAG;AACtB,WAAOpM,KAAK,CAAC,KAAKqK,gBAAN,EAAwB,KAAKC,YAAL,CAAkBjG,WAAlB,CAA8BgI,MAA9B,CAAqC7B,IAArC,CAA0C9I,MAAM,CAAC,MAAM,KAAK0I,gBAAZ,CAAhD,CAAxB,EAAwG,KAAKR,oBAA7G,EAAmI,KAAK0C,sBAAL,EAAnI,EAAkK,KAAKjB,WAAL,GACxK,KAAKA,WAAL,CAAiBkB,WAAjB,GAA+B/B,IAA/B,CAAoC9I,MAAM,CAAC,MAAM,KAAK0I,gBAAZ,CAA1C,CADwK,GAExKnK,EAAE,EAFI,CAAL,CAEKuK,IAFL,EAGP;AACA7I,IAAAA,GAAG,CAAC2D,KAAK,IAAKA,KAAK,YAAY9F,wBAAjB,GAA4C8F,KAA5C,GAAoD,IAA/D,CAJI,CAAP;AAKH;AACD;;;AACgB,MAAZkH,YAAY,GAAG;AACf,QAAI,KAAKlC,YAAL,IAAqB,KAAKA,YAAL,CAAkBjG,WAA3C,EAAwD;AACpD,aAAO,KAAKiG,YAAL,CAAkBjG,WAAlB,CAA8BoI,UAArC;AACH;;AACD,WAAO,IAAP;AACH;AACD;;;AACAH,EAAAA,sBAAsB,GAAG;AACrB,WAAOtM,KAAK,CAACE,SAAS,CAAC,KAAKkJ,SAAN,EAAiB,OAAjB,CAAV,EAAqClJ,SAAS,CAAC,KAAKkJ,SAAN,EAAiB,UAAjB,CAA9C,EAA4ElJ,SAAS,CAAC,KAAKkJ,SAAN,EAAiB,UAAjB,CAArF,CAAL,CAAwHoB,IAAxH,CAA6H9I,MAAM,CAAC4D,KAAK,IAAI;AAChJ;AACA;AACA,YAAMoH,WAAW,GAAGxO,eAAe,CAACoH,KAAD,CAAnC;;AACA,YAAMqH,SAAS,GAAG,KAAKxD,UAAL,GAAkB,KAAKA,UAAL,CAAgBxG,WAAhB,CAA4BwB,aAA9C,GAA8D,IAAhF;AACA,YAAMyI,YAAY,GAAG,KAAKC,WAAL,GAAmB,KAAKA,WAAL,CAAiBhF,UAAjB,CAA4B1D,aAA/C,GAA+D,IAApF;AACA,aAAQ,KAAKiG,gBAAL,IACJsC,WAAW,KAAK,KAAK7D,QAAL,CAAc1E,aAD1B,IAEJ;AACA;AACA;AACA;AACA,WAAKiF,SAAL,CAAeU,aAAf,KAAiC,KAAKjB,QAAL,CAAc1E,aAN3C,KAOH,CAACwI,SAAD,IAAc,CAACA,SAAS,CAACG,QAAV,CAAmBJ,WAAnB,CAPZ,MAQH,CAACE,YAAD,IAAiB,CAACA,YAAY,CAACE,QAAb,CAAsBJ,WAAtB,CARf,KASJ,CAAC,CAAC,KAAKrB,WATH,IAUJ,CAAC,KAAKA,WAAL,CAAiB0B,cAAjB,CAAgCD,QAAhC,CAAyCJ,WAAzC,CAVL;AAWH,KAjByI,CAAnI,CAAP;AAkBH,GA3L6B,CA4L9B;;;AACAM,EAAAA,UAAU,CAACnJ,KAAD,EAAQ;AACdoJ,IAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM,KAAKC,gBAAL,CAAsBvJ,KAAtB,CAA7B;AACH,GA/L6B,CAgM9B;;;AACAwJ,EAAAA,gBAAgB,CAACC,EAAD,EAAK;AACjB,SAAKtD,SAAL,GAAiBsD,EAAjB;AACH,GAnM6B,CAoM9B;;;AACAC,EAAAA,iBAAiB,CAACD,EAAD,EAAK;AAClB,SAAKrD,UAAL,GAAkBqD,EAAlB;AACH,GAvM6B,CAwM9B;;;AACAE,EAAAA,gBAAgB,CAACC,UAAD,EAAa;AACzB,SAAK5E,QAAL,CAAc1E,aAAd,CAA4BuJ,QAA5B,GAAuCD,UAAvC;AACH;;AACDE,EAAAA,cAAc,CAACrI,KAAD,EAAQ;AAClB,UAAMsI,OAAO,GAAGtI,KAAK,CAACsI,OAAtB;AACA,UAAMC,WAAW,GAAGhN,cAAc,CAACyE,KAAD,CAAlC,CAFkB,CAGlB;AACA;AACA;AACA;;AACA,QAAIsI,OAAO,KAAK9M,MAAZ,IAAsB,CAAC+M,WAA3B,EAAwC;AACpCvI,MAAAA,KAAK,CAACwI,cAAN;AACH;;AACD,QAAI,KAAKtB,YAAL,IAAqBoB,OAAO,KAAK7M,KAAjC,IAA0C,KAAKgJ,SAA/C,IAA4D,CAAC8D,WAAjE,EAA8E;AAC1E,WAAKrB,YAAL,CAAkBuB,qBAAlB;;AACA,WAAKC,gBAAL;;AACA1I,MAAAA,KAAK,CAACwI,cAAN;AACH,KAJD,MAKK,IAAI,KAAKxD,YAAT,EAAuB;AACxB,YAAM2D,cAAc,GAAG,KAAK3D,YAAL,CAAkBjG,WAAlB,CAA8BoI,UAArD;AACA,YAAMyB,UAAU,GAAGN,OAAO,KAAK5M,QAAZ,IAAwB4M,OAAO,KAAK3M,UAAvD;;AACA,UAAI2M,OAAO,KAAK1M,GAAZ,IAAoBgN,UAAU,IAAI,CAACL,WAAf,IAA8B,KAAK9D,SAA3D,EAAuE;AACnE,aAAKO,YAAL,CAAkBjG,WAAlB,CAA8B8J,SAA9B,CAAwC7I,KAAxC;AACH,OAFD,MAGK,IAAI4I,UAAU,IAAI,KAAKE,QAAL,EAAlB,EAAmC;AACpC,aAAK1C,SAAL;AACH;;AACD,UAAIwC,UAAU,IAAI,KAAK5D,YAAL,CAAkBjG,WAAlB,CAA8BoI,UAA9B,KAA6CwB,cAA/D,EAA+E;AAC3E,aAAKI,eAAL,CAAqB,KAAK/D,YAAL,CAAkBjG,WAAlB,CAA8BiK,eAA9B,IAAiD,CAAtE;AACH;AACJ;AACJ;;AACDC,EAAAA,YAAY,CAACjJ,KAAD,EAAQ;AAChB,QAAIkJ,MAAM,GAAGlJ,KAAK,CAACkJ,MAAnB;AACA,QAAI3K,KAAK,GAAG2K,MAAM,CAAC3K,KAAnB,CAFgB,CAGhB;;AACA,QAAI2K,MAAM,CAACrI,IAAP,KAAgB,QAApB,EAA8B;AAC1BtC,MAAAA,KAAK,GAAGA,KAAK,IAAI,EAAT,GAAc,IAAd,GAAqB4K,UAAU,CAAC5K,KAAD,CAAvC;AACH,KANe,CAOhB;AACA;AACA;AACA;AACA;;;AACA,QAAI,KAAK6K,cAAL,KAAwB7K,KAA5B,EAAmC;AAC/B,WAAK6K,cAAL,GAAsB7K,KAAtB;;AACA,WAAKmG,SAAL,CAAenG,KAAf;;AACA,UAAI,KAAKuK,QAAL,MAAmB,KAAKhF,SAAL,CAAeU,aAAf,KAAiCxE,KAAK,CAACkJ,MAA9D,EAAsE;AAClE,aAAK9C,SAAL;AACH;AACJ;AACJ;;AACDiD,EAAAA,YAAY,GAAG;AACX,QAAI,CAAC,KAAKhF,mBAAV,EAA+B;AAC3B,WAAKA,mBAAL,GAA2B,IAA3B;AACH,KAFD,MAGK,IAAI,KAAKyE,QAAL,EAAJ,EAAqB;AACtB,WAAKM,cAAL,GAAsB,KAAK7F,QAAL,CAAc1E,aAAd,CAA4BN,KAAlD;;AACA,WAAK8H,cAAL;;AACA,WAAKC,WAAL,CAAiB,IAAjB;AACH;AACJ;;AACDgD,EAAAA,YAAY,GAAG;AACX,QAAI,KAAKR,QAAL,MAAmB,CAAC,KAAKrE,SAA7B,EAAwC;AACpC,WAAK2B,SAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIE,EAAAA,WAAW,CAACiD,aAAa,GAAG,KAAjB,EAAwB;AAC/B,QAAI,KAAK1F,UAAL,IAAmB,KAAKA,UAAL,CAAgB2F,UAAhB,KAA+B,MAAtD,EAA8D;AAC1D,UAAID,aAAJ,EAAmB;AACf,aAAK1F,UAAL,CAAgB4F,oBAAhB;AACH,OAFD,MAGK;AACD,aAAK5F,UAAL,CAAgB2F,UAAhB,GAA6B,QAA7B;AACH;;AACD,WAAKrF,sBAAL,GAA8B,IAA9B;AACH;AACJ;AACD;;;AACAqC,EAAAA,WAAW,GAAG;AACV,QAAI,KAAKrC,sBAAT,EAAiC;AAC7B,WAAKN,UAAL,CAAgB2F,UAAhB,GAA6B,MAA7B;AACA,WAAKrF,sBAAL,GAA8B,KAA9B;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIuF,EAAAA,0BAA0B,GAAG;AACzB,UAAMC,WAAW,GAAG,KAAKjG,KAAL,CAAW0B,QAAX,CAAoBF,IAApB,CAAyB/I,IAAI,CAAC,CAAD,CAA7B,CAApB;;AACA,UAAMyN,aAAa,GAAG,KAAK5E,YAAL,CAAkBhG,OAAlB,CAA0BiG,OAA1B,CAAkCC,IAAlC,CAAuC5I,GAAG,CAAC,MAAM,KAAKuJ,iBAAL,CAAuBgE,mBAAvB,EAAP,CAA1C,EACtB;AACA;AACAtN,IAAAA,KAAK,CAAC,CAAD,CAHiB,CAAtB,CAFyB,CAMzB;;AACA,WAAQ7B,KAAK,CAACiP,WAAD,EAAcC,aAAd,CAAL,CACH1E,IADG,EAER;AACA;AACAhJ,IAAAA,SAAS,CAAC,MAAM;AACZ;AACA;AACA;AACA,WAAKwH,KAAL,CAAW+C,GAAX,CAAe,MAAM;AACjB,cAAMqD,OAAO,GAAG,KAAKrF,SAArB;;AACA,aAAKiE,gBAAL;;AACA,aAAK1D,YAAL,CAAkBzF,cAAlB;;AACA,aAAKnC,kBAAL,CAAwByJ,aAAxB;;AACA,YAAI,KAAKpC,SAAT,EAAoB;AAChB,eAAKsB,WAAL,CAAiBC,cAAjB,GADgB,CAEhB;AACA;AACA;AACA;;;AACA,cAAI8D,OAAO,KAAK,KAAKrF,SAArB,EAAgC;AAC5B,iBAAKO,YAAL,CAAkBlH,MAAlB,CAAyBuB,IAAzB;AACH;AACJ;AACJ,OAfD;;AAgBA,aAAO,KAAKyH,mBAAZ;AACH,KArBQ,CAJD,EA0BR;AACA3K,IAAAA,IAAI,CAAC,CAAD,CA3BI,EA4BJ;AA5BI,KA6BHgD,SA7BG,CA6BOa,KAAK,IAAI,KAAK+J,iBAAL,CAAuB/J,KAAvB,CA7BhB,CAAR;AA8BH;AACD;;;AACAkG,EAAAA,aAAa,GAAG;AACZ,QAAI,KAAKH,WAAT,EAAsB;AAClB,WAAKQ,UAAL;;AACA,WAAKR,WAAL,CAAiBiE,OAAjB;;AACA,WAAKjE,WAAL,GAAmB,IAAnB;AACH;AACJ;;AACD+B,EAAAA,gBAAgB,CAACvJ,KAAD,EAAQ;AACpB,UAAM0L,SAAS,GAAG,KAAKjF,YAAL,IAAqB,KAAKA,YAAL,CAAkBpH,WAAvC,GACZ,KAAKoH,YAAL,CAAkBpH,WAAlB,CAA8BW,KAA9B,CADY,GAEZA,KAFN,CADoB,CAIpB;AACA;;AACA,UAAM2L,UAAU,GAAGD,SAAS,IAAI,IAAb,GAAoBA,SAApB,GAAgC,EAAnD,CANoB,CAOpB;AACA;;AACA,QAAI,KAAKpG,UAAT,EAAqB;AACjB,WAAKA,UAAL,CAAgBsG,QAAhB,CAAyB5L,KAAzB,GAAiC2L,UAAjC;AACH,KAFD,MAGK;AACD,WAAK3G,QAAL,CAAc1E,aAAd,CAA4BN,KAA5B,GAAoC2L,UAApC;AACH;;AACD,SAAKd,cAAL,GAAsBc,UAAtB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIH,EAAAA,iBAAiB,CAAC/J,KAAD,EAAQ;AACrB,UAAMrD,MAAM,GAAGqD,KAAK,IAAIA,KAAK,CAACrD,MAA9B;;AACA,QAAIA,MAAJ,EAAY;AACR,WAAKyN,4BAAL,CAAkCzN,MAAlC;;AACA,WAAKmL,gBAAL,CAAsBnL,MAAM,CAAC4B,KAA7B;;AACA,WAAKmG,SAAL,CAAe/H,MAAM,CAAC4B,KAAtB;;AACA,WAAKyG,YAAL,CAAkBjF,gBAAlB,CAAmCpD,MAAnC;;AACA,WAAK4G,QAAL,CAAc1E,aAAd,CAA4BwL,KAA5B;AACH;;AACD,SAAK9D,UAAL;AACH;AACD;AACJ;AACA;;;AACI6D,EAAAA,4BAA4B,CAACE,IAAD,EAAO;AAC/B,SAAKtF,YAAL,CAAkBhG,OAAlB,CAA0BuL,OAA1B,CAAkC3N,MAAM,IAAI;AACxC,UAAIA,MAAM,KAAK0N,IAAX,IAAmB1N,MAAM,CAAC4N,QAA9B,EAAwC;AACpC5N,QAAAA,MAAM,CAAC6N,QAAP;AACH;AACJ,KAJD;AAKH;;AACDpE,EAAAA,cAAc,GAAG;AACb,QAAI,CAAC,KAAKrB,YAAN,KAAuB,OAAO0F,SAAP,KAAqB,WAArB,IAAoCA,SAA3D,CAAJ,EAA2E;AACvE,YAAMtH,mCAAmC,EAAzC;AACH;;AACD,QAAIuH,UAAU,GAAG,KAAK5E,WAAtB;;AACA,QAAI,CAAC4E,UAAL,EAAiB;AACb,WAAKC,OAAL,GAAe,IAAI/O,cAAJ,CAAmB,KAAKmJ,YAAL,CAAkB/D,QAArC,EAA+C,KAAKwC,iBAApD,EAAuE;AAClFvF,QAAAA,EAAE,EAAE,KAAK2F,UAAL,EAAiBgH,UAAjB;AAD8E,OAAvE,CAAf;AAGAF,MAAAA,UAAU,GAAG,KAAKnH,QAAL,CAAcsH,MAAd,CAAqB,KAAKC,iBAAL,EAArB,CAAb;AACA,WAAKhF,WAAL,GAAmB4E,UAAnB,CALa,CAMb;AACA;;AACAA,MAAAA,UAAU,CAACK,aAAX,GAA2B7L,SAA3B,CAAqCa,KAAK,IAAI;AAC1C;AACA;AACA,YAAKA,KAAK,CAACsI,OAAN,KAAkB9M,MAAlB,IAA4B,CAACD,cAAc,CAACyE,KAAD,CAA5C,IACCA,KAAK,CAACsI,OAAN,KAAkB5M,QAAlB,IAA8BH,cAAc,CAACyE,KAAD,EAAQ,QAAR,CADjD,EACqE;AACjE,eAAKsE,oBAAL,CAA0B2G,IAA1B;;AACA,eAAKvC,gBAAL,GAFiE,CAGjE;AACA;;;AACA1I,UAAAA,KAAK,CAACkL,eAAN;AACAlL,UAAAA,KAAK,CAACwI,cAAN;AACH;AACJ,OAZD;AAaA,WAAKpE,qBAAL,GAA6B,KAAKL,cAAL,CAAoB7E,MAApB,GAA6BC,SAA7B,CAAuC,MAAM;AACtE,YAAI,KAAKsF,SAAL,IAAkBkG,UAAtB,EAAkC;AAC9BA,UAAAA,UAAU,CAACQ,UAAX,CAAsB;AAAEC,YAAAA,KAAK,EAAE,KAAKC,cAAL;AAAT,WAAtB;AACH;AACJ,OAJ4B,CAA7B;AAKH,KA1BD,MA2BK;AACD;AACA,WAAKxF,iBAAL,CAAuByF,SAAvB,CAAiC,KAAKC,oBAAL,EAAjC;;AACAZ,MAAAA,UAAU,CAACQ,UAAX,CAAsB;AAAEC,QAAAA,KAAK,EAAE,KAAKC,cAAL;AAAT,OAAtB;AACH;;AACD,QAAIV,UAAU,IAAI,CAACA,UAAU,CAACjE,WAAX,EAAnB,EAA6C;AACzCiE,MAAAA,UAAU,CAACa,MAAX,CAAkB,KAAKZ,OAAvB;AACA,WAAKhE,2BAAL,GAAmC,KAAK8C,0BAAL,EAAnC;AACH;;AACD,UAAMI,OAAO,GAAG,KAAKrF,SAArB;;AACA,SAAKO,YAAL,CAAkBzF,cAAlB;;AACA,SAAKyF,YAAL,CAAkBrH,OAAlB,GAA4B,KAAKmH,gBAAL,GAAwB,IAApD,CA3Ca,CA4Cb;AACA;;AACA,QAAI,KAAKL,SAAL,IAAkBqF,OAAO,KAAK,KAAKrF,SAAvC,EAAkD;AAC9C,WAAKO,YAAL,CAAkBlH,MAAlB,CAAyBuB,IAAzB;AACH;AACJ;;AACD0L,EAAAA,iBAAiB,GAAG;AAChB,WAAO,IAAI7P,aAAJ,CAAkB;AACrBuQ,MAAAA,gBAAgB,EAAE,KAAKC,mBAAL,EADG;AAErB/H,MAAAA,cAAc,EAAE,KAAK0B,eAAL,EAFK;AAGrB+F,MAAAA,KAAK,EAAE,KAAKC,cAAL,EAHc;AAIrBM,MAAAA,SAAS,EAAE,KAAK/H,IAJK;AAKrBgI,MAAAA,UAAU,EAAE,KAAK5H,SAAL,EAAgB6H;AALP,KAAlB,CAAP;AAOH;;AACDH,EAAAA,mBAAmB,GAAG;AAClB,UAAMI,QAAQ,GAAG,KAAKtI,QAAL,CACZoB,QADY,GAEZmH,mBAFY,CAEQ,KAAKR,oBAAL,EAFR,EAGZS,sBAHY,CAGW,KAHX,EAIZC,QAJY,CAIH,KAJG,CAAjB;;AAKA,SAAKnG,qBAAL,CAA2BgG,QAA3B;;AACA,SAAKjG,iBAAL,GAAyBiG,QAAzB;AACA,WAAOA,QAAP;AACH;AACD;;;AACAhG,EAAAA,qBAAqB,CAAC2F,gBAAD,EAAmB;AACpC;AACA;AACA,UAAMS,cAAc,GAAG,CACnB;AAAEC,MAAAA,OAAO,EAAE,OAAX;AAAoBC,MAAAA,OAAO,EAAE,QAA7B;AAAuCC,MAAAA,QAAQ,EAAE,OAAjD;AAA0DC,MAAAA,QAAQ,EAAE;AAApE,KADmB,EAEnB;AAAEH,MAAAA,OAAO,EAAE,KAAX;AAAkBC,MAAAA,OAAO,EAAE,QAA3B;AAAqCC,MAAAA,QAAQ,EAAE,KAA/C;AAAsDC,MAAAA,QAAQ,EAAE;AAAhE,KAFmB,CAAvB,CAHoC,CAOpC;AACA;AACA;;AACA,UAAMV,UAAU,GAAG,KAAKW,WAAxB;AACA,UAAMC,cAAc,GAAG,CACnB;AAAEL,MAAAA,OAAO,EAAE,OAAX;AAAoBC,MAAAA,OAAO,EAAE,KAA7B;AAAoCC,MAAAA,QAAQ,EAAE,OAA9C;AAAuDC,MAAAA,QAAQ,EAAE,QAAjE;AAA2EV,MAAAA;AAA3E,KADmB,EAEnB;AAAEO,MAAAA,OAAO,EAAE,KAAX;AAAkBC,MAAAA,OAAO,EAAE,KAA3B;AAAkCC,MAAAA,QAAQ,EAAE,KAA5C;AAAmDC,MAAAA,QAAQ,EAAE,QAA7D;AAAuEV,MAAAA;AAAvE,KAFmB,CAAvB;AAIA,QAAIa,SAAJ;;AACA,QAAI,KAAK7H,QAAL,KAAkB,OAAtB,EAA+B;AAC3B6H,MAAAA,SAAS,GAAGD,cAAZ;AACH,KAFD,MAGK,IAAI,KAAK5H,QAAL,KAAkB,OAAtB,EAA+B;AAChC6H,MAAAA,SAAS,GAAGP,cAAZ;AACH,KAFI,MAGA;AACDO,MAAAA,SAAS,GAAG,CAAC,GAAGP,cAAJ,EAAoB,GAAGM,cAAvB,CAAZ;AACH;;AACDf,IAAAA,gBAAgB,CAACiB,aAAjB,CAA+BD,SAA/B;AACH;;AACDlB,EAAAA,oBAAoB,GAAG;AACnB,QAAI,KAAKhE,WAAT,EAAsB;AAClB,aAAO,KAAKA,WAAL,CAAiBhF,UAAxB;AACH;;AACD,WAAO,KAAKsB,UAAL,GAAkB,KAAKA,UAAL,CAAgB8I,yBAAhB,EAAlB,GAAgE,KAAKpJ,QAA5E;AACH;;AACD8H,EAAAA,cAAc,GAAG;AACb,WAAO,KAAKrG,YAAL,CAAkB7D,UAAlB,IAAgC,KAAKyL,aAAL,EAAvC;AACH;AACD;;;AACAA,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAKrB,oBAAL,GAA4B1M,aAA5B,CAA0CgO,qBAA1C,GAAkEzB,KAAzE;AACH;AACD;AACJ;AACA;AACA;;;AACI1C,EAAAA,gBAAgB,GAAG;AACf,UAAM1D,YAAY,GAAG,KAAKA,YAA1B;;AACA,QAAIA,YAAY,CAAC9H,qBAAjB,EAAwC;AACpC;AACA;AACA8H,MAAAA,YAAY,CAACjG,WAAb,CAAyB+N,kBAAzB;AACH,KAJD,MAKK;AACD9H,MAAAA,YAAY,CAACjG,WAAb,CAAyBgO,aAAzB,CAAuC,CAAC,CAAxC;AACH;AACJ;AACD;;;AACAjE,EAAAA,QAAQ,GAAG;AACP,UAAMkE,OAAO,GAAG,KAAKzJ,QAAL,CAAc1E,aAA9B;AACA,WAAO,CAACmO,OAAO,CAACC,QAAT,IAAqB,CAACD,OAAO,CAAC5E,QAA9B,IAA0C,CAAC,KAAKlE,qBAAvD;AACH;AACD;;;AACAuB,EAAAA,UAAU,GAAG;AACT,WAAO,KAAK3B,SAAL,EAAgBoJ,WAAhB,IAA+B1H,MAAtC;AACH;AACD;;;AACAuD,EAAAA,eAAe,CAAC3J,KAAD,EAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAM4F,YAAY,GAAG,KAAKA,YAA1B;;AACA,UAAMmI,UAAU,GAAGhT,6BAA6B,CAACiF,KAAD,EAAQ4F,YAAY,CAAChG,OAArB,EAA8BgG,YAAY,CAAC5C,YAA3C,CAAhD;;AACA,QAAIhD,KAAK,KAAK,CAAV,IAAe+N,UAAU,KAAK,CAAlC,EAAqC;AACjC;AACA;AACA;AACAnI,MAAAA,YAAY,CAACtF,aAAb,CAA2B,CAA3B;AACH,KALD,MAMK,IAAIsF,YAAY,CAACpF,KAAjB,EAAwB;AACzB,YAAMhD,MAAM,GAAGoI,YAAY,CAAChG,OAAb,CAAqBM,OAArB,GAA+BF,KAA/B,CAAf;;AACA,UAAIxC,MAAJ,EAAY;AACR,cAAMoQ,OAAO,GAAGpQ,MAAM,CAACwQ,eAAP,EAAhB;;AACA,cAAMC,iBAAiB,GAAGjT,wBAAwB,CAAC4S,OAAO,CAACM,SAAT,EAAoBN,OAAO,CAACO,YAA5B,EAA0CvI,YAAY,CAACnF,aAAb,EAA1C,EAAwEmF,YAAY,CAACpF,KAAb,CAAmBf,aAAnB,CAAiC0O,YAAzG,CAAlD;;AACAvI,QAAAA,YAAY,CAACtF,aAAb,CAA2B2N,iBAA3B;AACH;AACJ;AACJ;;AAhiB6B;;AAkiBlC/J,2BAA2B,CAAC9C,IAA5B;AAAA,mBAAwH8C,2BAAxH,EAvqBuGzK,EAuqBvG,mBAAqKA,EAAE,CAAC6H,UAAxK,GAvqBuG7H,EAuqBvG,mBAA+LmC,IAAI,CAACC,OAApM,GAvqBuGpC,EAuqBvG,mBAAwNA,EAAE,CAAC2U,gBAA3N,GAvqBuG3U,EAuqBvG,mBAAwPA,EAAE,CAAC4U,MAA3P,GAvqBuG5U,EAuqBvG,mBAA8QA,EAAE,CAAC4H,iBAAjR,GAvqBuG5H,EAuqBvG,mBAA+S4J,gCAA/S,GAvqBuG5J,EAuqBvG,mBAA4VyC,IAAI,CAACoS,cAAjW,MAvqBuG7U,EAuqBvG,mBAA4YmD,cAA5Y,MAvqBuGnD,EAuqBvG,mBAAmciC,QAAnc,MAvqBuGjC,EAuqBvG,mBAAweuC,EAAE,CAACuS,aAA3e,GAvqBuG9U,EAuqBvG,mBAAqgBiE,gCAArgB;AAAA;;AACAwG,2BAA2B,CAAC1C,IAA5B,kBAxqBuG/H,EAwqBvG;AAAA,QAA4GyK,2BAA5G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAxqBuGzK,EAwqBvG;AAAA;;AACA;AAAA,qDAzqBuGA,EAyqBvG,mBAA2FyK,2BAA3F,EAAoI,CAAC;AACzHzC,IAAAA,IAAI,EAAE5H;AADmH,GAAD,CAApI,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE4H,MAAAA,IAAI,EAAEhI,EAAE,CAAC6H;AAAX,KAAD,EAA0B;AAAEG,MAAAA,IAAI,EAAE7F,IAAI,CAACC;AAAb,KAA1B,EAAkD;AAAE4F,MAAAA,IAAI,EAAEhI,EAAE,CAAC2U;AAAX,KAAlD,EAAiF;AAAE3M,MAAAA,IAAI,EAAEhI,EAAE,CAAC4U;AAAX,KAAjF,EAAsG;AAAE5M,MAAAA,IAAI,EAAEhI,EAAE,CAAC4H;AAAX,KAAtG,EAAsI;AAAEI,MAAAA,IAAI,EAAEC,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACnMF,QAAAA,IAAI,EAAE3H,MAD6L;AAEnM8H,QAAAA,IAAI,EAAE,CAACyB,gCAAD;AAF6L,OAAD;AAA/B,KAAtI,EAG3B;AAAE5B,MAAAA,IAAI,EAAEvF,IAAI,CAACoS,cAAb;AAA6B3M,MAAAA,UAAU,EAAE,CAAC;AAC5CF,QAAAA,IAAI,EAAElH;AADsC,OAAD;AAAzC,KAH2B,EAK3B;AAAEkH,MAAAA,IAAI,EAAE9E,EAAE,CAAC6R,YAAX;AAAyB7M,MAAAA,UAAU,EAAE,CAAC;AACxCF,QAAAA,IAAI,EAAElH;AADkC,OAAD,EAExC;AACCkH,QAAAA,IAAI,EAAE3H,MADP;AAEC8H,QAAAA,IAAI,EAAE,CAAChF,cAAD;AAFP,OAFwC,EAKxC;AACC6E,QAAAA,IAAI,EAAEjH;AADP,OALwC;AAArC,KAL2B,EAY3B;AAAEiH,MAAAA,IAAI,EAAEC,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAClCF,QAAAA,IAAI,EAAElH;AAD4B,OAAD,EAElC;AACCkH,QAAAA,IAAI,EAAE3H,MADP;AAEC8H,QAAAA,IAAI,EAAE,CAAClG,QAAD;AAFP,OAFkC;AAA/B,KAZ2B,EAiB3B;AAAE+F,MAAAA,IAAI,EAAEzF,EAAE,CAACuS;AAAX,KAjB2B,EAiBC;AAAE9M,MAAAA,IAAI,EAAEC,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DF,QAAAA,IAAI,EAAElH;AADwD,OAAD,EAE9D;AACCkH,QAAAA,IAAI,EAAE3H,MADP;AAEC8H,QAAAA,IAAI,EAAE,CAAClE,gCAAD;AAFP,OAF8D;AAA/B,KAjBD,CAAP;AAsBlB,GAxBxB,EAwB0C;AAAEkI,IAAAA,YAAY,EAAE,CAAC;AAC3CnE,MAAAA,IAAI,EAAEzH,KADqC;AAE3C4H,MAAAA,IAAI,EAAE,CAAC,iBAAD;AAFqC,KAAD,CAAhB;AAG1B4D,IAAAA,QAAQ,EAAE,CAAC;AACX/D,MAAAA,IAAI,EAAEzH,KADK;AAEX4H,MAAAA,IAAI,EAAE,CAAC,yBAAD;AAFK,KAAD,CAHgB;AAM1BuG,IAAAA,WAAW,EAAE,CAAC;AACd1G,MAAAA,IAAI,EAAEzH,KADQ;AAEd4H,MAAAA,IAAI,EAAE,CAAC,4BAAD;AAFQ,KAAD,CANa;AAS1B6D,IAAAA,qBAAqB,EAAE,CAAC;AACxBhE,MAAAA,IAAI,EAAEzH,KADkB;AAExB4H,MAAAA,IAAI,EAAE,CAAC,cAAD;AAFkB,KAAD,CATG;AAY1BsE,IAAAA,oBAAoB,EAAE,CAAC;AACvBzE,MAAAA,IAAI,EAAEzH,KADiB;AAEvB4H,MAAAA,IAAI,EAAE,CAAC,yBAAD;AAFiB,KAAD;AAZI,GAxB1C;AAAA;;AAwCA,MAAMkC,sBAAN,SAAqCI,2BAArC,CAAiE;AAC7D5G,EAAAA,WAAW,GAAG;AACV,UAAM,GAAG2E,SAAT;AACA,SAAKkL,WAAL,GAAmB,8BAAnB;AACH;;AAJ4D;;AAMjErJ,sBAAsB,CAAC1C,IAAvB;AAAA;AAAA;AAAA,0FAvtBuG3H,EAutBvG,uBAAmHqK,sBAAnH,SAAmHA,sBAAnH;AAAA;AAAA;;AACAA,sBAAsB,CAACtC,IAAvB,kBAxtBuG/H,EAwtBvG;AAAA,QAAuGqK,sBAAvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAxtBuGrK,MAAAA,EAwtBvG;AAAA,eAAuG,kBAAvG;AAAA;AAAA,eAAuG,gBAAvG;AAAA;AAAA,eAAuG,wBAAvG;AAAA;AAAA,eAAuG,0BAAvG;AAAA;AAAA,eAAuG,kBAAvG;AAAA;AAAA;;AAAA;AAxtBuGA,MAAAA,EAwtBvG;AAAA;AAAA;AAAA;AAAA,aAxtBuGA,EAwtBvG,oBAA05B,CAACoK,+BAAD,CAA15B,GAxtBuGpK,EAwtBvG;AAAA;;AACA;AAAA,qDAztBuGA,EAytBvG,mBAA2FqK,sBAA3F,EAA+H,CAAC;AACpHrC,IAAAA,IAAI,EAAE5H,SAD8G;AAEpH+H,IAAAA,IAAI,EAAE,CAAC;AACCU,MAAAA,QAAQ,EAAG,mDADZ;AAECO,MAAAA,IAAI,EAAE;AACF,iBAAS,0BADP;AAEF,+BAAuB,uBAFrB;AAGF,uBAAe,0CAHb;AAIF,oCAA4B,sCAJ1B;AAKF,wCAAgC,sDAL9B;AAMF,gCAAwB,oDANtB;AAOF,4BAAoB,gEAPlB;AAQF,gCAAwB,yCARtB;AASF;AACA;AACA,qBAAa,gBAXX;AAYF,kBAAU,cAZR;AAaF,mBAAW,sBAbT;AAcF,qBAAa,wBAdX;AAeF,mBAAW;AAfT,OAFP;AAmBCF,MAAAA,QAAQ,EAAE,wBAnBX;AAoBCG,MAAAA,SAAS,EAAE,CAACe,+BAAD;AApBZ,KAAD;AAF8G,GAAD,CAA/H;AAAA;AA0BA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM4K,qBAAN,CAA4B;;AAE5BA,qBAAqB,CAACrN,IAAtB;AAAA,mBAAkHqN,qBAAlH;AAAA;;AACAA,qBAAqB,CAACC,IAAtB,kBA7vBuGjV,EA6vBvG;AAAA,QAAmHgV;AAAnH;AAMAA,qBAAqB,CAACE,IAAtB,kBAnwBuGlV,EAmwBvG;AAAA,aAAqJ,CAACiK,iDAAD,CAArJ;AAAA,YAAoN,CAAC3H,aAAD,EAAgBd,eAAhB,EAAiCC,eAAjC,EAAkDS,YAAlD,CAApN,EAAqRM,mBAArR,EACQhB,eADR,EAEQC,eAFR;AAAA;;AAGA;AAAA,qDAtwBuGzB,EAswBvG,mBAA2FgV,qBAA3F,EAA8H,CAAC;AACnHhN,IAAAA,IAAI,EAAEhH,QAD6G;AAEnHmH,IAAAA,IAAI,EAAE,CAAC;AACCgN,MAAAA,OAAO,EAAE,CAAC7S,aAAD,EAAgBd,eAAhB,EAAiCC,eAAjC,EAAkDS,YAAlD,CADV;AAECkT,MAAAA,OAAO,EAAE,CACL7M,eADK,EAEL8B,sBAFK,EAGLV,qBAHK,EAILnH,mBAJK,EAKLhB,eALK,EAMLC,eANK,CAFV;AAUC4T,MAAAA,YAAY,EAAE,CAAC9M,eAAD,EAAkB8B,sBAAlB,EAA0CV,qBAA1C,CAVf;AAWCN,MAAAA,SAAS,EAAE,CAACY,iDAAD;AAXZ,KAAD;AAF6G,GAAD,CAA9H;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAShG,gCAAT,EAA2CG,wCAA3C,EAAqFwF,gCAArF,EAAuHC,wCAAvH,EAAiKI,iDAAjK,EAAoNG,+BAApN,EAAqP7B,eAArP,EAAsQyM,qBAAtQ,EAA6RrL,qBAA7R,EAAoT/F,4BAApT,EAAkVyG,sBAAlV,EAA0W/F,oBAA1W,EAAgYmF,0BAAhY,EAA4ZgB,2BAA5Z,EAAybF,mCAAzb", "sourcesContent": ["import { ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceStringArray } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, TemplateRef, Directive, Inject, ViewChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, forwardRef, Optional, Host, NgModule } from '@angular/core';\nimport { mixinDisableRipple, MAT_OPTION_PARENT_COMPONENT, MAT_OPTGROUP, MatOption, MatOptionSelectionChange, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport { Subscription, Subject, defer, merge, of, fromEvent } from 'rxjs';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i2$1 from '@angular/cdk/bidi';\nimport { hasModifierKey, ESCAPE, ENTER, UP_ARROW, DOWN_ARROW, TAB } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD } from '@angular/material/form-field';\nimport { startWith, switchMap, take, filter, map, tap, delay } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Autocomplete IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueAutocompleteIdCounter = 0;\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n    constructor(\n    /** Reference to the autocomplete panel that emitted the event. */\n    source, \n    /** Option that was selected. */\n    option) {\n        this.source = source;\n        this.option = option;\n    }\n}\n// Boilerplate for applying mixins to MatAutocomplete.\n/** @docs-private */\nconst _MatAutocompleteMixinBase = mixinDisableRipple(class {\n});\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n    providedIn: 'root',\n    factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n    return { autoActiveFirstOption: false };\n}\n/** Base class with all of the `MatAutocomplete` functionality. */\nclass _MatAutocompleteBase extends _MatAutocompleteMixinBase {\n    constructor(_changeDetectorRef, _elementRef, defaults, platform) {\n        super();\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        this._activeOptionChanges = Subscription.EMPTY;\n        /** Whether the autocomplete panel should be visible, depending on option length. */\n        this.showPanel = false;\n        this._isOpen = false;\n        /** Function that maps an option's control value to its display value in the trigger. */\n        this.displayWith = null;\n        /** Event that is emitted whenever an option from the list is selected. */\n        this.optionSelected = new EventEmitter();\n        /** Event that is emitted when the autocomplete panel is opened. */\n        this.opened = new EventEmitter();\n        /** Event that is emitted when the autocomplete panel is closed. */\n        this.closed = new EventEmitter();\n        /** Emits whenever an option is activated. */\n        this.optionActivated = new EventEmitter();\n        this._classList = {};\n        /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n        this.id = `mat-autocomplete-${_uniqueAutocompleteIdCounter++}`;\n        // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n        // Safari using VoiceOver. We should occasionally check back to see whether the bug\n        // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n        // option altogether.\n        this.inertGroups = platform?.SAFARI || false;\n        this._autoActiveFirstOption = !!defaults.autoActiveFirstOption;\n    }\n    /** Whether the autocomplete panel is open. */\n    get isOpen() {\n        return this._isOpen && this.showPanel;\n    }\n    /**\n     * Whether the first option should be highlighted when the autocomplete panel is opened.\n     * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n     */\n    get autoActiveFirstOption() {\n        return this._autoActiveFirstOption;\n    }\n    set autoActiveFirstOption(value) {\n        this._autoActiveFirstOption = coerceBooleanProperty(value);\n    }\n    /**\n     * Takes classes set on the host mat-autocomplete element and applies them to the panel\n     * inside the overlay container to allow for easy styling.\n     */\n    set classList(value) {\n        if (value && value.length) {\n            this._classList = coerceStringArray(value).reduce((classList, className) => {\n                classList[className] = true;\n                return classList;\n            }, {});\n        }\n        else {\n            this._classList = {};\n        }\n        this._setVisibilityClasses(this._classList);\n        this._elementRef.nativeElement.className = '';\n    }\n    ngAfterContentInit() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options).withWrap();\n        this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n            if (this.isOpen) {\n                this.optionActivated.emit({ source: this, option: this.options.toArray()[index] || null });\n            }\n        });\n        // Set the initial visibility state.\n        this._setVisibility();\n    }\n    ngOnDestroy() {\n        this._activeOptionChanges.unsubscribe();\n    }\n    /**\n     * Sets the panel scrollTop. This allows us to manually scroll to display options\n     * above or below the fold, as they are not actually being focused when active.\n     */\n    _setScrollTop(scrollTop) {\n        if (this.panel) {\n            this.panel.nativeElement.scrollTop = scrollTop;\n        }\n    }\n    /** Returns the panel's scrollTop. */\n    _getScrollTop() {\n        return this.panel ? this.panel.nativeElement.scrollTop : 0;\n    }\n    /** Panel should hide itself when the option list is empty. */\n    _setVisibility() {\n        this.showPanel = !!this.options.length;\n        this._setVisibilityClasses(this._classList);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits the `select` event. */\n    _emitSelectEvent(option) {\n        const event = new MatAutocompleteSelectedEvent(this, option);\n        this.optionSelected.emit(event);\n    }\n    /** Gets the aria-labelledby for the autocomplete panel. */\n    _getPanelAriaLabelledby(labelId) {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Sets the autocomplete visibility classes on a classlist based on the panel is visible. */\n    _setVisibilityClasses(classList) {\n        classList[this._visibleClass] = this.showPanel;\n        classList[this._hiddenClass] = !this.showPanel;\n    }\n}\n_MatAutocompleteBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatAutocompleteBase, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS }, { token: i1.Platform }], target: i0.ɵɵFactoryTarget.Directive });\n_MatAutocompleteBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: _MatAutocompleteBase, inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], displayWith: \"displayWith\", autoActiveFirstOption: \"autoActiveFirstOption\", panelWidth: \"panelWidth\", classList: [\"class\", \"classList\"] }, outputs: { optionSelected: \"optionSelected\", opened: \"opened\", closed: \"closed\", optionActivated: \"optionActivated\" }, viewQueries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true, static: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatAutocompleteBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n                }] }, { type: i1.Platform }]; }, propDecorators: { template: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], displayWith: [{\n                type: Input\n            }], autoActiveFirstOption: [{\n                type: Input\n            }], panelWidth: [{\n                type: Input\n            }], optionSelected: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], closed: [{\n                type: Output\n            }], optionActivated: [{\n                type: Output\n            }], classList: [{\n                type: Input,\n                args: ['class']\n            }] } });\nclass MatAutocomplete extends _MatAutocompleteBase {\n    constructor() {\n        super(...arguments);\n        this._visibleClass = 'mat-autocomplete-visible';\n        this._hiddenClass = 'mat-autocomplete-hidden';\n    }\n}\nMatAutocomplete.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocomplete, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatAutocomplete.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatAutocomplete, selector: \"mat-autocomplete\", inputs: { disableRipple: \"disableRipple\" }, host: { classAttribute: \"mat-autocomplete\" }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], queries: [{ propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }], exportAs: [\"matAutocomplete\"], usesInheritance: true, ngImport: i0, template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div class=\\\"mat-autocomplete-panel\\\"\\n       role=\\\"listbox\\\"\\n       [id]=\\\"id\\\"\\n       [attr.aria-label]=\\\"ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n       [ngClass]=\\\"_classList\\\"\\n       #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-autocomplete-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;visibility:hidden;max-width:none;max-height:256px;position:relative;width:100%;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.mat-autocomplete-panel.mat-autocomplete-visible{visibility:visible}.mat-autocomplete-panel.mat-autocomplete-hidden{visibility:hidden}.mat-autocomplete-panel-above .mat-autocomplete-panel{border-radius:0;border-top-left-radius:4px;border-top-right-radius:4px}.mat-autocomplete-panel .mat-divider-horizontal{margin-top:-1px}.cdk-high-contrast-active .mat-autocomplete-panel{outline:solid 1px}mat-autocomplete{display:none}\\n\"], directives: [{ type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocomplete, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-autocomplete', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, exportAs: 'matAutocomplete', inputs: ['disableRipple'], host: {\n                        'class': 'mat-autocomplete',\n                    }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div class=\\\"mat-autocomplete-panel\\\"\\n       role=\\\"listbox\\\"\\n       [id]=\\\"id\\\"\\n       [attr.aria-label]=\\\"ariaLabel || null\\\"\\n       [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n       [ngClass]=\\\"_classList\\\"\\n       #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-autocomplete-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;visibility:hidden;max-width:none;max-height:256px;position:relative;width:100%;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.mat-autocomplete-panel.mat-autocomplete-visible{visibility:visible}.mat-autocomplete-panel.mat-autocomplete-hidden{visibility:hidden}.mat-autocomplete-panel-above .mat-autocomplete-panel{border-radius:0;border-top-left-radius:4px;border-top-right-radius:4px}.mat-autocomplete-panel .mat-divider-horizontal{margin-top:-1px}.cdk-high-contrast-active .mat-autocomplete-panel{outline:solid 1px}mat-autocomplete{display:none}\\n\"] }]\n        }], propDecorators: { optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Base class containing all of the functionality for `MatAutocompleteOrigin`. */\nclass _MatAutocompleteOriginBase {\n    constructor(\n    /** Reference to the element on which the directive is applied. */\n    elementRef) {\n        this.elementRef = elementRef;\n    }\n}\n_MatAutocompleteOriginBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatAutocompleteOriginBase, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n_MatAutocompleteOriginBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: _MatAutocompleteOriginBase, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatAutocompleteOriginBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nclass MatAutocompleteOrigin extends _MatAutocompleteOriginBase {\n}\nMatAutocompleteOrigin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocompleteOrigin, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatAutocompleteOrigin.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatAutocompleteOrigin, selector: \"[matAutocompleteOrigin]\", exportAs: [\"matAutocompleteOrigin\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocompleteOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matAutocompleteOrigin]',\n                    exportAs: 'matAutocompleteOrigin',\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy');\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY,\n};\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatAutocompleteTrigger),\n    multi: true,\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n    return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' +\n        'Make sure that the id passed to the `matAutocomplete` is correct and that ' +\n        \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nclass _MatAutocompleteTriggerBase {\n    constructor(_element, _overlay, _viewContainerRef, _zone, _changeDetectorRef, scrollStrategy, _dir, _formField, _document, _viewportRuler, _defaults) {\n        this._element = _element;\n        this._overlay = _overlay;\n        this._viewContainerRef = _viewContainerRef;\n        this._zone = _zone;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._dir = _dir;\n        this._formField = _formField;\n        this._document = _document;\n        this._viewportRuler = _viewportRuler;\n        this._defaults = _defaults;\n        this._componentDestroyed = false;\n        this._autocompleteDisabled = false;\n        /** Whether or not the label state is being overridden. */\n        this._manuallyFloatingLabel = false;\n        /** Subscription to viewport size changes. */\n        this._viewportSubscription = Subscription.EMPTY;\n        /**\n         * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n         * closed autocomplete from being reopened if the user switches to another browser tab and then\n         * comes back.\n         */\n        this._canOpenOnNextFocus = true;\n        /** Stream of keyboard events that can close the panel. */\n        this._closeKeyEventStream = new Subject();\n        /**\n         * Event handler for when the window is blurred. Needs to be an\n         * arrow function in order to preserve the context.\n         */\n        this._windowBlurHandler = () => {\n            // If the user blurred the window while the autocomplete is focused, it means that it'll be\n            // refocused when they come back. In this case we want to skip the first focus event, if the\n            // pane was closed, in order to avoid reopening it unintentionally.\n            this._canOpenOnNextFocus =\n                this._document.activeElement !== this._element.nativeElement || this.panelOpen;\n        };\n        /** `View -> model callback called when value changes` */\n        this._onChange = () => { };\n        /** `View -> model callback called when autocomplete has been touched` */\n        this._onTouched = () => { };\n        /**\n         * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n         * will render the panel underneath the trigger if there is enough space for it to fit in\n         * the viewport, otherwise the panel will be shown above it. If the position is set to\n         * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n         * whether it fits completely in the viewport.\n         */\n        this.position = 'auto';\n        /**\n         * `autocomplete` attribute to be set on the input element.\n         * @docs-private\n         */\n        this.autocompleteAttribute = 'off';\n        this._overlayAttached = false;\n        /** Stream of changes to the selection state of the autocomplete options. */\n        this.optionSelections = defer(() => {\n            const options = this.autocomplete ? this.autocomplete.options : null;\n            if (options) {\n                return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n            }\n            // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n            // Return a stream that we'll replace with the real one once everything is in place.\n            return this._zone.onStable.pipe(take(1), switchMap(() => this.optionSelections));\n        });\n        this._scrollStrategy = scrollStrategy;\n    }\n    /**\n     * Whether the autocomplete is disabled. When disabled, the element will\n     * act as a regular input and the user won't be able to open the panel.\n     */\n    get autocompleteDisabled() {\n        return this._autocompleteDisabled;\n    }\n    set autocompleteDisabled(value) {\n        this._autocompleteDisabled = coerceBooleanProperty(value);\n    }\n    ngAfterViewInit() {\n        const window = this._getWindow();\n        if (typeof window !== 'undefined') {\n            this._zone.runOutsideAngular(() => window.addEventListener('blur', this._windowBlurHandler));\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes['position'] && this._positionStrategy) {\n            this._setStrategyPositions(this._positionStrategy);\n            if (this.panelOpen) {\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    ngOnDestroy() {\n        const window = this._getWindow();\n        if (typeof window !== 'undefined') {\n            window.removeEventListener('blur', this._windowBlurHandler);\n        }\n        this._viewportSubscription.unsubscribe();\n        this._componentDestroyed = true;\n        this._destroyPanel();\n        this._closeKeyEventStream.complete();\n    }\n    /** Whether or not the autocomplete panel is open. */\n    get panelOpen() {\n        return this._overlayAttached && this.autocomplete.showPanel;\n    }\n    /** Opens the autocomplete suggestion panel. */\n    openPanel() {\n        this._attachOverlay();\n        this._floatLabel();\n    }\n    /** Closes the autocomplete suggestion panel. */\n    closePanel() {\n        this._resetLabel();\n        if (!this._overlayAttached) {\n            return;\n        }\n        if (this.panelOpen) {\n            // Only emit if the panel was visible.\n            // The `NgZone.onStable` always emits outside of the Angular zone,\n            // so all the subscriptions from `_subscribeToClosingActions()` are also outside of the Angular zone.\n            // We should manually run in Angular zone to update UI after panel closing.\n            this._zone.run(() => {\n                this.autocomplete.closed.emit();\n            });\n        }\n        this.autocomplete._isOpen = this._overlayAttached = false;\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n            this._closingActionsSubscription.unsubscribe();\n        }\n        // Note that in some cases this can end up being called after the component is destroyed.\n        // Add a check to ensure that we don't try to run change detection on a destroyed view.\n        if (!this._componentDestroyed) {\n            // We need to trigger change detection manually, because\n            // `fromEvent` doesn't seem to do it at the proper time.\n            // This ensures that the label is reset when the\n            // user clicks outside.\n            this._changeDetectorRef.detectChanges();\n        }\n    }\n    /**\n     * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n     * within the viewport.\n     */\n    updatePosition() {\n        if (this._overlayAttached) {\n            this._overlayRef.updatePosition();\n        }\n    }\n    /**\n     * A stream of actions that should close the autocomplete panel, including\n     * when an option is selected, on blur, and when TAB is pressed.\n     */\n    get panelClosingActions() {\n        return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef\n            ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached))\n            : of()).pipe(\n        // Normalize the output so we return a consistent type.\n        map(event => (event instanceof MatOptionSelectionChange ? event : null)));\n    }\n    /** The currently active option, coerced to MatOption type. */\n    get activeOption() {\n        if (this.autocomplete && this.autocomplete._keyManager) {\n            return this.autocomplete._keyManager.activeItem;\n        }\n        return null;\n    }\n    /** Stream of clicks outside of the autocomplete panel. */\n    _getOutsideClickStream() {\n        return merge(fromEvent(this._document, 'click'), fromEvent(this._document, 'auxclick'), fromEvent(this._document, 'touchend')).pipe(filter(event => {\n            // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n            // fall back to check the first element in the path of the click event.\n            const clickTarget = _getEventTarget(event);\n            const formField = this._formField ? this._formField._elementRef.nativeElement : null;\n            const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n            return (this._overlayAttached &&\n                clickTarget !== this._element.nativeElement &&\n                // Normally focus moves inside `mousedown` so this condition will almost always be\n                // true. Its main purpose is to handle the case where the input is focused from an\n                // outside click which propagates up to the `body` listener within the same sequence\n                // and causes the panel to close immediately (see #3106).\n                this._document.activeElement !== this._element.nativeElement &&\n                (!formField || !formField.contains(clickTarget)) &&\n                (!customOrigin || !customOrigin.contains(clickTarget)) &&\n                !!this._overlayRef &&\n                !this._overlayRef.overlayElement.contains(clickTarget));\n        }));\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        Promise.resolve().then(() => this._setTriggerValue(value));\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this._element.nativeElement.disabled = isDisabled;\n    }\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const hasModifier = hasModifierKey(event);\n        // Prevent the default action on all escape key presses. This is here primarily to bring IE\n        // in line with other browsers. By default, pressing escape on IE will cause it to revert\n        // the input value to the one that it had on focus, however it won't dispatch any events\n        // which means that the model value will be out of sync with the view.\n        if (keyCode === ESCAPE && !hasModifier) {\n            event.preventDefault();\n        }\n        if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n            this.activeOption._selectViaInteraction();\n            this._resetActiveItem();\n            event.preventDefault();\n        }\n        else if (this.autocomplete) {\n            const prevActiveItem = this.autocomplete._keyManager.activeItem;\n            const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n            if (keyCode === TAB || (isArrowKey && !hasModifier && this.panelOpen)) {\n                this.autocomplete._keyManager.onKeydown(event);\n            }\n            else if (isArrowKey && this._canOpen()) {\n                this.openPanel();\n            }\n            if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n                this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n            }\n        }\n    }\n    _handleInput(event) {\n        let target = event.target;\n        let value = target.value;\n        // Based on `NumberValueAccessor` from forms.\n        if (target.type === 'number') {\n            value = value == '' ? null : parseFloat(value);\n        }\n        // If the input has a placeholder, IE will fire the `input` event on page load,\n        // focus and blur, in addition to when the user actually changed the value. To\n        // filter out all of the extra events, we save the value on focus and between\n        // `input` events, and we check whether it changed.\n        // See: https://connect.microsoft.com/IE/feedback/details/885747/\n        if (this._previousValue !== value) {\n            this._previousValue = value;\n            this._onChange(value);\n            if (this._canOpen() && this._document.activeElement === event.target) {\n                this.openPanel();\n            }\n        }\n    }\n    _handleFocus() {\n        if (!this._canOpenOnNextFocus) {\n            this._canOpenOnNextFocus = true;\n        }\n        else if (this._canOpen()) {\n            this._previousValue = this._element.nativeElement.value;\n            this._attachOverlay();\n            this._floatLabel(true);\n        }\n    }\n    _handleClick() {\n        if (this._canOpen() && !this.panelOpen) {\n            this.openPanel();\n        }\n    }\n    /**\n     * In \"auto\" mode, the label will animate down as soon as focus is lost.\n     * This causes the value to jump when selecting an option with the mouse.\n     * This method manually floats the label until the panel can be closed.\n     * @param shouldAnimate Whether the label should be animated when it is floated.\n     */\n    _floatLabel(shouldAnimate = false) {\n        if (this._formField && this._formField.floatLabel === 'auto') {\n            if (shouldAnimate) {\n                this._formField._animateAndLockLabel();\n            }\n            else {\n                this._formField.floatLabel = 'always';\n            }\n            this._manuallyFloatingLabel = true;\n        }\n    }\n    /** If the label has been manually elevated, return it to its normal state. */\n    _resetLabel() {\n        if (this._manuallyFloatingLabel) {\n            this._formField.floatLabel = 'auto';\n            this._manuallyFloatingLabel = false;\n        }\n    }\n    /**\n     * This method listens to a stream of panel closing actions and resets the\n     * stream every time the option list changes.\n     */\n    _subscribeToClosingActions() {\n        const firstStable = this._zone.onStable.pipe(take(1));\n        const optionChanges = this.autocomplete.options.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()), \n        // Defer emitting to the stream until the next tick, because changing\n        // bindings in here will cause \"changed after checked\" errors.\n        delay(0));\n        // When the zone is stable initially, and when the option list changes...\n        return (merge(firstStable, optionChanges)\n            .pipe(\n        // create a new stream of panelClosingActions, replacing any previous streams\n        // that were created, and flatten it so our stream only emits closing events...\n        switchMap(() => {\n            // The `NgZone.onStable` always emits outside of the Angular zone, thus we have to re-enter\n            // the Angular zone. This will lead to change detection being called outside of the Angular\n            // zone and the `autocomplete.opened` will also emit outside of the Angular.\n            this._zone.run(() => {\n                const wasOpen = this.panelOpen;\n                this._resetActiveItem();\n                this.autocomplete._setVisibility();\n                this._changeDetectorRef.detectChanges();\n                if (this.panelOpen) {\n                    this._overlayRef.updatePosition();\n                    // If the `panelOpen` state changed, we need to make sure to emit the `opened`\n                    // event, because we may not have emitted it when the panel was attached. This\n                    // can happen if the users opens the panel and there are no options, but the\n                    // options come in slightly later or as a result of the value changing.\n                    if (wasOpen !== this.panelOpen) {\n                        this.autocomplete.opened.emit();\n                    }\n                }\n            });\n            return this.panelClosingActions;\n        }), \n        // when the first closing event occurs...\n        take(1))\n            // set the value, close the panel, and complete.\n            .subscribe(event => this._setValueAndClose(event)));\n    }\n    /** Destroys the autocomplete suggestion panel. */\n    _destroyPanel() {\n        if (this._overlayRef) {\n            this.closePanel();\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n    }\n    _setTriggerValue(value) {\n        const toDisplay = this.autocomplete && this.autocomplete.displayWith\n            ? this.autocomplete.displayWith(value)\n            : value;\n        // Simply falling back to an empty string if the display value is falsy does not work properly.\n        // The display value can also be the number zero and shouldn't fall back to an empty string.\n        const inputValue = toDisplay != null ? toDisplay : '';\n        // If it's used within a `MatFormField`, we should set it through the property so it can go\n        // through change detection.\n        if (this._formField) {\n            this._formField._control.value = inputValue;\n        }\n        else {\n            this._element.nativeElement.value = inputValue;\n        }\n        this._previousValue = inputValue;\n    }\n    /**\n     * This method closes the panel, and if a value is specified, also sets the associated\n     * control to that value. It will also mark the control as dirty if this interaction\n     * stemmed from the user.\n     */\n    _setValueAndClose(event) {\n        const source = event && event.source;\n        if (source) {\n            this._clearPreviousSelectedOption(source);\n            this._setTriggerValue(source.value);\n            this._onChange(source.value);\n            this.autocomplete._emitSelectEvent(source);\n            this._element.nativeElement.focus();\n        }\n        this.closePanel();\n    }\n    /**\n     * Clear any previous selected option and emit a selection change event for this option\n     */\n    _clearPreviousSelectedOption(skip) {\n        this.autocomplete.options.forEach(option => {\n            if (option !== skip && option.selected) {\n                option.deselect();\n            }\n        });\n    }\n    _attachOverlay() {\n        if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatAutocompleteMissingPanelError();\n        }\n        let overlayRef = this._overlayRef;\n        if (!overlayRef) {\n            this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n                id: this._formField?.getLabelId(),\n            });\n            overlayRef = this._overlay.create(this._getOverlayConfig());\n            this._overlayRef = overlayRef;\n            // Use the `keydownEvents` in order to take advantage of\n            // the overlay event targeting provided by the CDK overlay.\n            overlayRef.keydownEvents().subscribe(event => {\n                // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n                // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n                if ((event.keyCode === ESCAPE && !hasModifierKey(event)) ||\n                    (event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey'))) {\n                    this._closeKeyEventStream.next();\n                    this._resetActiveItem();\n                    // We need to stop propagation, otherwise the event will eventually\n                    // reach the input itself and cause the overlay to be reopened.\n                    event.stopPropagation();\n                    event.preventDefault();\n                }\n            });\n            this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n                if (this.panelOpen && overlayRef) {\n                    overlayRef.updateSize({ width: this._getPanelWidth() });\n                }\n            });\n        }\n        else {\n            // Update the trigger, panel width and direction, in case anything has changed.\n            this._positionStrategy.setOrigin(this._getConnectedElement());\n            overlayRef.updateSize({ width: this._getPanelWidth() });\n        }\n        if (overlayRef && !overlayRef.hasAttached()) {\n            overlayRef.attach(this._portal);\n            this._closingActionsSubscription = this._subscribeToClosingActions();\n        }\n        const wasOpen = this.panelOpen;\n        this.autocomplete._setVisibility();\n        this.autocomplete._isOpen = this._overlayAttached = true;\n        // We need to do an extra `panelOpen` check in here, because the\n        // autocomplete won't be shown if there are no options.\n        if (this.panelOpen && wasOpen !== this.panelOpen) {\n            this.autocomplete.opened.emit();\n        }\n    }\n    _getOverlayConfig() {\n        return new OverlayConfig({\n            positionStrategy: this._getOverlayPosition(),\n            scrollStrategy: this._scrollStrategy(),\n            width: this._getPanelWidth(),\n            direction: this._dir,\n            panelClass: this._defaults?.overlayPanelClass,\n        });\n    }\n    _getOverlayPosition() {\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._getConnectedElement())\n            .withFlexibleDimensions(false)\n            .withPush(false);\n        this._setStrategyPositions(strategy);\n        this._positionStrategy = strategy;\n        return strategy;\n    }\n    /** Sets the positions on a position strategy based on the directive's input state. */\n    _setStrategyPositions(positionStrategy) {\n        // Note that we provide horizontal fallback positions, even though by default the dropdown\n        // width matches the input, because consumers can override the width. See #18854.\n        const belowPositions = [\n            { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n            { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n        ];\n        // The overlay edge connected to the trigger should have squared corners, while\n        // the opposite end has rounded corners. We apply a CSS class to swap the\n        // border-radius based on the overlay position.\n        const panelClass = this._aboveClass;\n        const abovePositions = [\n            { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom', panelClass },\n            { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom', panelClass },\n        ];\n        let positions;\n        if (this.position === 'above') {\n            positions = abovePositions;\n        }\n        else if (this.position === 'below') {\n            positions = belowPositions;\n        }\n        else {\n            positions = [...belowPositions, ...abovePositions];\n        }\n        positionStrategy.withPositions(positions);\n    }\n    _getConnectedElement() {\n        if (this.connectedTo) {\n            return this.connectedTo.elementRef;\n        }\n        return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n    }\n    _getPanelWidth() {\n        return this.autocomplete.panelWidth || this._getHostWidth();\n    }\n    /** Returns the width of the input element, so the panel width can match it. */\n    _getHostWidth() {\n        return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n    }\n    /**\n     * Resets the active item to -1 so arrow events will activate the\n     * correct options, or to 0 if the consumer opted into it.\n     */\n    _resetActiveItem() {\n        const autocomplete = this.autocomplete;\n        if (autocomplete.autoActiveFirstOption) {\n            // Note that we go through `setFirstItemActive`, rather than `setActiveItem(0)`, because\n            // the former will find the next enabled option, if the first one is disabled.\n            autocomplete._keyManager.setFirstItemActive();\n        }\n        else {\n            autocomplete._keyManager.setActiveItem(-1);\n        }\n    }\n    /** Determines whether the panel can be opened. */\n    _canOpen() {\n        const element = this._element.nativeElement;\n        return !element.readOnly && !element.disabled && !this._autocompleteDisabled;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document?.defaultView || window;\n    }\n    /** Scrolls to a particular option in the list. */\n    _scrollToOption(index) {\n        // Given that we are not actually focusing active options, we must manually adjust scroll\n        // to reveal options below the fold. First, we find the offset of the option from the top\n        // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n        // the panel height + the option height, so the active option will be just visible at the\n        // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n        // will become the offset. If that offset is visible within the panel already, the scrollTop is\n        // not adjusted.\n        const autocomplete = this.autocomplete;\n        const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n        if (index === 0 && labelCount === 1) {\n            // If we've got one group label before the option and we're at the top option,\n            // scroll the list to the top. This is better UX than scrolling the list to the\n            // top of the option, because it allows the user to read the top group's label.\n            autocomplete._setScrollTop(0);\n        }\n        else if (autocomplete.panel) {\n            const option = autocomplete.options.toArray()[index];\n            if (option) {\n                const element = option._getHostElement();\n                const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n                autocomplete._setScrollTop(newScrollPosition);\n            }\n        }\n    }\n}\n_MatAutocompleteTriggerBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatAutocompleteTriggerBase, deps: [{ token: i0.ElementRef }, { token: i1$1.Overlay }, { token: i0.ViewContainerRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: MAT_AUTOCOMPLETE_SCROLL_STRATEGY }, { token: i2$1.Directionality, optional: true }, { token: MAT_FORM_FIELD, host: true, optional: true }, { token: DOCUMENT, optional: true }, { token: i3.ViewportRuler }, { token: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatAutocompleteTriggerBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: _MatAutocompleteTriggerBase, inputs: { autocomplete: [\"matAutocomplete\", \"autocomplete\"], position: [\"matAutocompletePosition\", \"position\"], connectedTo: [\"matAutocompleteConnectedTo\", \"connectedTo\"], autocompleteAttribute: [\"autocomplete\", \"autocompleteAttribute\"], autocompleteDisabled: [\"matAutocompleteDisabled\", \"autocompleteDisabled\"] }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatAutocompleteTriggerBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1$1.Overlay }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY]\n                }] }, { type: i2$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }, {\n                    type: Host\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i3.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { autocomplete: [{\n                type: Input,\n                args: ['matAutocomplete']\n            }], position: [{\n                type: Input,\n                args: ['matAutocompletePosition']\n            }], connectedTo: [{\n                type: Input,\n                args: ['matAutocompleteConnectedTo']\n            }], autocompleteAttribute: [{\n                type: Input,\n                args: ['autocomplete']\n            }], autocompleteDisabled: [{\n                type: Input,\n                args: ['matAutocompleteDisabled']\n            }] } });\nclass MatAutocompleteTrigger extends _MatAutocompleteTriggerBase {\n    constructor() {\n        super(...arguments);\n        this._aboveClass = 'mat-autocomplete-panel-above';\n    }\n}\nMatAutocompleteTrigger.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocompleteTrigger, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nMatAutocompleteTrigger.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatAutocompleteTrigger, selector: \"input[matAutocomplete], textarea[matAutocomplete]\", host: { listeners: { \"focusin\": \"_handleFocus()\", \"blur\": \"_onTouched()\", \"input\": \"_handleInput($event)\", \"keydown\": \"_handleKeydown($event)\", \"click\": \"_handleClick()\" }, properties: { \"attr.autocomplete\": \"autocompleteAttribute\", \"attr.role\": \"autocompleteDisabled ? null : \\\"combobox\\\"\", \"attr.aria-autocomplete\": \"autocompleteDisabled ? null : \\\"list\\\"\", \"attr.aria-activedescendant\": \"(panelOpen && activeOption) ? activeOption.id : null\", \"attr.aria-expanded\": \"autocompleteDisabled ? null : panelOpen.toString()\", \"attr.aria-owns\": \"(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id\", \"attr.aria-haspopup\": \"autocompleteDisabled ? null : \\\"listbox\\\"\" }, classAttribute: \"mat-autocomplete-trigger\" }, providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR], exportAs: [\"matAutocompleteTrigger\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocompleteTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n                    host: {\n                        'class': 'mat-autocomplete-trigger',\n                        '[attr.autocomplete]': 'autocompleteAttribute',\n                        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n                        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n                        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n                        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n                        '[attr.aria-owns]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n                        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n                        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n                        // a little earlier. This avoids issues where IE delays the focusing of the input.\n                        '(focusin)': '_handleFocus()',\n                        '(blur)': '_onTouched()',\n                        '(input)': '_handleInput($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(click)': '_handleClick()',\n                    },\n                    exportAs: 'matAutocompleteTrigger',\n                    providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatAutocompleteModule {\n}\nMatAutocompleteModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocompleteModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatAutocompleteModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocompleteModule, declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin], imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule], exports: [MatAutocomplete,\n        MatAutocompleteTrigger,\n        MatAutocompleteOrigin,\n        CdkScrollableModule,\n        MatOptionModule,\n        MatCommonModule] });\nMatAutocompleteModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocompleteModule, providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [[OverlayModule, MatOptionModule, MatCommonModule, CommonModule], CdkScrollableModule,\n        MatOptionModule,\n        MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatAutocompleteModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule],\n                    exports: [\n                        MatAutocomplete,\n                        MatAutocompleteTrigger,\n                        MatAutocompleteOrigin,\n                        CdkScrollableModule,\n                        MatOptionModule,\n                        MatCommonModule,\n                    ],\n                    declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n                    providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, _MatAutocompleteBase, _MatAutocompleteOriginBase, _MatAutocompleteTriggerBase, getMatAutocompleteMissingPanelError };\n"]}, "metadata": {}, "sourceType": "module"}
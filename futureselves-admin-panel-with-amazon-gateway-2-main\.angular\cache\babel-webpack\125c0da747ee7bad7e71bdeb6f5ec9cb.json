{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { FormControl, Validators } from '@angular/forms';\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"ngx-spinner\";\nimport * as i7 from \"../../../../sidebar.component\";\nimport * as i8 from \"@angular/common\";\n\nfunction AddEditRoleComponent_div_14_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 48);\n  }\n\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r19.imageSrc, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction AddEditRoleComponent_div_14_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditRoleComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"label\", 43);\n    i0.ɵɵtext(2, \"Role Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44);\n    i0.ɵɵelementStart(4, \"input\", 45);\n    i0.ɵɵlistener(\"change\", function AddEditRoleComponent_div_14_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return ctx_r21.onFileSelected($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, AddEditRoleComponent_div_14_img_5_Template, 1, 1, \"img\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AddEditRoleComponent_div_14_div_6_Template, 2, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"readonly\", ctx_r0.isReadonly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.imageSrc);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.addRoleForm.get(\"RO_dp\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors.fileSizeValidator);\n  }\n}\n\nfunction AddEditRoleComponent_div_15_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 53);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditRoleComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"label\", 50);\n    i0.ɵɵtext(2, \"Role Image\");\n    i0.ɵɵtemplate(3, AddEditRoleComponent_div_15_span_3_Template, 2, 0, \"span\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 52);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isReadonly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"readonly\", ctx_r1.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_input_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 54);\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_20_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return ctx_r24.onEnterKey($event, \"RO_References\");\n    })(\"keydown\", function AddEditRoleComponent_input_20_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return ctx_r26.onBackspaceKey($event, \"RO_References\");\n    })(\"blur\", function AddEditRoleComponent_input_20_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return ctx_r27.onInputBlur($event, \"RO_References\");\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r2.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_div_22_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_22_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const i_r29 = i0.ɵɵnextContext().index;\n      const ctx_r31 = i0.ɵɵnextContext();\n      return ctx_r31.removeTagForField(i_r29, \"RO_References\");\n    });\n    i0.ɵɵtext(1, \"x\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"view-mode\": a0\n  };\n};\n\nfunction AddEditRoleComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_22_span_2_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r28 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r3.title === \"View\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r28, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_input_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 54);\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_27_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return ctx_r34.onEnterKey($event, \"RO_Skills_and_Knowledge\");\n    })(\"keydown\", function AddEditRoleComponent_input_27_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return ctx_r36.onBackspaceKey($event, \"RO_Skills_and_Knowledge\");\n    })(\"blur\", function AddEditRoleComponent_input_27_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return ctx_r37.onInputBlur($event, \"RO_Skills_and_Knowledge\");\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r4.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_div_29_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_29_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const i_r39 = i0.ɵɵnextContext().index;\n      const ctx_r41 = i0.ɵɵnextContext();\n      return ctx_r41.removeTagForField(i_r39, \"RO_Skills_and_Knowledge\");\n    });\n    i0.ɵɵtext(1, \"x\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditRoleComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_29_span_2_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r38 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r5.title === \"View\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r38, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_input_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 54);\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_34_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return ctx_r44.onEnterKey($event, \"RO_Place_of_Work\");\n    })(\"keydown\", function AddEditRoleComponent_input_34_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return ctx_r46.onBackspaceKey($event, \"RO_Place_of_Work\");\n    })(\"blur\", function AddEditRoleComponent_input_34_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return ctx_r47.onInputBlur($event, \"RO_Place_of_Work\");\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r6.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_div_36_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_36_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r53);\n      const i_r49 = i0.ɵɵnextContext().index;\n      const ctx_r51 = i0.ɵɵnextContext();\n      return ctx_r51.removeTagForField(i_r49, \"RO_Place_of_Work\");\n    });\n    i0.ɵɵtext(1, \"x\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditRoleComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_36_span_2_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r48 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r7.title === \"View\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r48, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_input_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 54);\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_41_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r54 = i0.ɵɵnextContext();\n      return ctx_r54.onEnterKey($event, \"RO_Key_Insights\");\n    })(\"keydown\", function AddEditRoleComponent_input_41_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return ctx_r56.onBackspaceKey($event, \"RO_Key_Insights\");\n    })(\"blur\", function AddEditRoleComponent_input_41_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return ctx_r57.onInputBlur($event, \"RO_Key_Insights\");\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r8.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_div_43_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_43_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r63);\n      const i_r59 = i0.ɵɵnextContext().index;\n      const ctx_r61 = i0.ɵɵnextContext();\n      return ctx_r61.removeTagForField(i_r59, \"RO_Key_Insights\");\n    });\n    i0.ɵɵtext(1, \"x\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditRoleComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_43_span_2_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r58 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r9.title === \"View\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r58, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_input_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 54);\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_49_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return ctx_r64.onEnterKey($event, \"RO_Salary\");\n    })(\"keydown\", function AddEditRoleComponent_input_49_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return ctx_r66.onBackspaceKey($event, \"RO_Salary\");\n    })(\"blur\", function AddEditRoleComponent_input_49_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return ctx_r67.onInputBlur($event, \"RO_Salary\");\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r10.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_div_51_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_51_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const i_r69 = i0.ɵɵnextContext().index;\n      const ctx_r71 = i0.ɵɵnextContext();\n      return ctx_r71.removeTagForField(i_r69, \"RO_Salary\");\n    });\n    i0.ɵɵtext(1, \"x\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditRoleComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_51_span_2_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r68 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r11.title === \"View\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r68, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"label\", 58);\n    i0.ɵɵtext(2, \"Expected Starting Salary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 59);\n    i0.ɵɵlistener(\"change\", function AddEditRoleComponent_div_52_Template_select_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return ctx_r74.updateSalaryRange($event);\n    });\n    i0.ɵɵelementStart(4, \"option\", 60);\n    i0.ɵɵtext(5, \"Select a salary range\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"option\", 61);\n    i0.ɵɵtext(7, \"Less than 25k\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"option\", 62);\n    i0.ɵɵtext(9, \"25k - 30k\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 63);\n    i0.ɵɵtext(11, \"30k - 35k\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 64);\n    i0.ɵɵtext(13, \"More than 35k\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r12.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"label\", 58);\n    i0.ɵɵtext(2, \"Salary Range\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 65);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r13.salaryRange)(\"readonly\", ctx_r13.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_input_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 54);\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_58_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return ctx_r76.onEnterKey($event, \"RO_Daily_Tasks\");\n    })(\"keydown\", function AddEditRoleComponent_input_58_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return ctx_r78.onBackspaceKey($event, \"RO_Daily_Tasks\");\n    })(\"blur\", function AddEditRoleComponent_input_58_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r77);\n      const ctx_r79 = i0.ɵɵnextContext();\n      return ctx_r79.onInputBlur($event, \"RO_Daily_Tasks\");\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"readonly\", ctx_r14.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_div_60_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r85 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_60_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r85);\n      const i_r81 = i0.ɵɵnextContext().index;\n      const ctx_r83 = i0.ɵɵnextContext();\n      return ctx_r83.removeTagForField(i_r81, \"RO_Daily_Tasks\");\n    });\n    i0.ɵɵtext(1, \"x\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditRoleComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_60_span_2_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r80 = ctx.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r15.title === \"View\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r80, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_select_66_option_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const activity_r87 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", activity_r87.QUO_title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(activity_r87.QUO_title);\n  }\n}\n\nfunction AddEditRoleComponent_select_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"select\", 66);\n    i0.ɵɵlistener(\"change\", function AddEditRoleComponent_select_66_Template_select_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r88 = i0.ɵɵnextContext();\n      return ctx_r88.addExpectedActivity($event);\n    })(\"keydown\", function AddEditRoleComponent_select_66_Template_select_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r90 = i0.ɵɵnextContext();\n      return ctx_r90.onBackspaceKey($event, \"RO_Expected_Activities\");\n    });\n    i0.ɵɵelementStart(1, \"option\", 67);\n    i0.ɵɵtext(2, \"Add New +\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AddEditRoleComponent_select_66_option_3_Template, 2, 2, \"option\", 68);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r16.isReadonly);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.expectedActivitiesList);\n  }\n}\n\nfunction AddEditRoleComponent_div_68_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_68_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r96);\n      const i_r92 = i0.ɵɵnextContext().index;\n      const ctx_r94 = i0.ɵɵnextContext();\n      return ctx_r94.removeTagForField(i_r92, \"RO_Expected_Activities\");\n    });\n    i0.ɵɵtext(1, \"x\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditRoleComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_68_span_2_Template, 2, 0, \"span\", 56);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const tag_r91 = ctx.$implicit;\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r17.title === \"View\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", tag_r91, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r17.isReadonly);\n  }\n}\n\nfunction AddEditRoleComponent_button_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 69);\n    i0.ɵɵtext(1, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport class AddEditRoleComponent {\n  constructor(formBuilder, dataTransferService, toastr, router, activeRoute, httpClient, ngxSpinnerService) {\n    var _a;\n\n    this.formBuilder = formBuilder;\n    this.dataTransferService = dataTransferService;\n    this.toastr = toastr;\n    this.router = router;\n    this.activeRoute = activeRoute;\n    this.httpClient = httpClient;\n    this.ngxSpinnerService = ngxSpinnerService;\n    this.p = 1;\n    this.showForm = false;\n    this.title = 'Add New';\n    this.isReadonly = false;\n    this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\n    const state = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras.state;\n\n    if (state) {\n      console.log('Role from state : ', state.role);\n      this.sectorId = state.sectorId;\n      this.title = state.title;\n      this.isReadonly = state.isReadonly;\n      this.role = state.role;\n      this.roleId = state.roleId;\n    }\n\n    this.addRoleForm = this.formBuilder.group({\n      RO_title: ['', Validators.required],\n      RO_dp: [null, Validators.required],\n      RO_References: this.formBuilder.array([], Validators.required),\n      RO_Skills_and_Knowledge: this.formBuilder.array([], Validators.required),\n      RO_Place_of_Work: this.formBuilder.array([], Validators.required),\n      RO_Key_Insights: this.formBuilder.array([], Validators.required),\n      RO_Salary: this.formBuilder.array([], Validators.required),\n      RO_Salary_from: ['', Validators.required],\n      RO_Salary_to: ['', Validators.required],\n      RO_Daily_Tasks: this.formBuilder.array([], Validators.required),\n      RO_Expected_Activities: this.formBuilder.array([], Validators.required),\n      RO_Expected_to_on_the_weekends: ['', Validators.required],\n      RO_Expected_to_work_beyond_nine_to_five: ['', Validators.required],\n      RO_Expected_to_work_internationally: ['', Validators.required],\n      RO_Expected_to_work_in_the_office: ['', Validators.required],\n      RO_description: ['', Validators.required],\n      RO_IndustryId: [''],\n      RO_isAccept: [false]\n    });\n  }\n\n  ngOnInit() {\n    var _a, _b, _c, _d, _e;\n\n    this.getAllExpctedActivities();\n    this.getAllRoleBySectorId(this.sectorId);\n    this.sectorId = this.sectorId;\n\n    if (this.title == 'View') {\n      (_a = this.addRoleForm.get('RO_Expected_to_on_the_weekends')) === null || _a === void 0 ? void 0 : _a.disable();\n      (_b = this.addRoleForm.get('RO_Expected_to_work_beyond_nine_to_five')) === null || _b === void 0 ? void 0 : _b.disable();\n      (_c = this.addRoleForm.get('RO_Expected_to_work_internationally')) === null || _c === void 0 ? void 0 : _c.disable();\n      (_d = this.addRoleForm.get('RO_Expected_to_work_in_the_office')) === null || _d === void 0 ? void 0 : _d.disable();\n      (_e = this.addRoleForm.get('RO_isAccept')) === null || _e === void 0 ? void 0 : _e.disable();\n    }\n\n    if (!this.sectorId) {\n      this.router.navigate([`actions/sectors/roles`]);\n    }\n\n    ;\n\n    if (this.role) {\n      console.log(\"Role Data :\", this.role);\n      this.setRange();\n      this.patchArrayValues(this.role.RO_References, 'RO_References');\n      this.patchArrayValues(this.role.RO_Expected_Activities, 'RO_Expected_Activities');\n      this.patchArrayValues(this.role.RO_Salary, 'RO_Salary');\n      this.patchArrayValues(this.role.RO_Daily_Tasks, 'RO_Daily_Tasks');\n      this.patchArrayValues(this.role.RO_Skills_and_Knowledge, 'RO_Skills_and_Knowledge');\n      this.patchArrayValues(this.role.RO_Place_of_Work, 'RO_Place_of_Work');\n      this.patchArrayValues(this.role.RO_Key_Insights, 'RO_Key_Insights');\n      this.addRoleForm.patchValue({\n        RO_isAccept: this.role.RO_isAccept == 'true' ? true : false\n      });\n      this.addRoleForm.patchValue(this.role);\n    }\n  }\n\n  setRange() {\n    if (this.role.RO_Salary_from == \"0\" && this.role.RO_Salary_to == \"25\") {\n      this.salaryRange = \"<25k\";\n    } else if (this.role.RO_Salary_from == \"25\" && this.role.RO_Salary_to == \"30\") {\n      this.salaryRange = \"25-30k\";\n    } else if (this.role.RO_Salary_from == \"30\" && this.role.RO_Salary_to == \"35\") {\n      this.salaryRange = \"30-35k\";\n    } else if (this.role.RO_Salary_from == \"35\" && this.role.RO_Salary_to == \"100\") {\n      this.salaryRange = \"35k+\";\n    }\n  }\n\n  patchArrayValues(values, controlName) {\n    if (values && values.length > 0) {\n      const formArray = this.addRoleForm.get(controlName);\n      values.forEach(value => {\n        formArray.push(this.formBuilder.control(value));\n      });\n    }\n  }\n\n  get f() {\n    return this.addRoleForm.controls;\n  }\n\n  onFileSelected(event) {\n    var _a;\n\n    let selectedFile = event.target.files[0];\n\n    if (event.target.files.length === 0) {\n      // Reset both imageName and imageSrc when no file is selected\n      this.imageName = null;\n      this.imageSrc = null;\n      return;\n    }\n\n    const newFileName = FileValidator.addTimestamp(selectedFile.name);\n    this.imageName = new File([selectedFile], newFileName, {\n      type: selectedFile.type\n    });\n\n    if (this.imageName) {\n      const formControl = this.addRoleForm.get('RO_dp');\n      formControl === null || formControl === void 0 ? void 0 : formControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\n      formControl === null || formControl === void 0 ? void 0 : formControl.updateValueAndValidity();\n    }\n\n    const fileType = this.imageName.type.split('/')[0];\n    const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n\n    if (fileType !== 'image' || fileExtension === 'svg') {\n      event.target.value = '';\n      this.toastr.info('Please select an image file (excluding SVG).');\n      this.imageName = null;\n      this.imageSrc = null;\n      return;\n    }\n\n    if (this.imageName && fileType == 'image') {\n      const reader = new FileReader();\n\n      reader.onload = e => {\n        var _a;\n\n        this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\n      };\n\n      reader.readAsDataURL(this.imageName);\n    } else {\n      this.imageSrc = null; // Reset imageSrc if no file selected\n    }\n\n    console.log('imageName', this.imageName);\n  }\n\n  uploadLogoUrl() {\n    return new Promise((resolve, reject) => {\n      if (!this.imageName) {\n        return resolve(null);\n      }\n\n      console.log('image', this.imageName);\n      this.dataTransferService.uploadurl(this.imageName).subscribe(res => {\n        resolve(res);\n      }, error => {\n        reject(error);\n      });\n    });\n  }\n\n  addRole() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this.title == 'Edit') {\n          _this.addRoleForm.value.RO_IndustryId = _this.sectorId;\n          _this.addRoleForm.value.RO_id = _this.roleId;\n\n          if (_this.imageName) {\n            yield _this.uploadLogoUrl();\n            const fileUrl = _this.baseUrl + _this.imageName.name;\n            _this.addRoleForm.value.RO_dp = fileUrl;\n          } else {\n            _this.addRoleForm.value.RO_dp = _this.role.RO_dp;\n          }\n\n          console.log(\"Role edit data : \", _this.addRoleForm.value);\n\n          _this.ngxSpinnerService.show('globalSpinner');\n\n          const res = yield _this.dataTransferService.updateRoleData(_this.addRoleForm.value).toPromise();\n\n          _this.ngxSpinnerService.hide('globalSpinner');\n\n          if (res.statusCode == 200) {\n            _this.toastr.success(\"Role updated successfully.\");\n\n            const state = {\n              sectorId: _this.sectorId\n            };\n\n            _this.router.navigate([`actions/sectors/roles`], {\n              state\n            });\n\n            _this.dataTransferService.getAllRoleBySectorId(_this.sectorId);\n          } else {\n            _this.toastr.error(\"Something went wrong.\");\n\n            console.error('Unable to update role. Status:', res.status);\n          }\n        } else {\n          if (_this.addRoleForm.invalid) {\n            console.log(\"this.addRoleForm.value\", _this.addRoleForm.value);\n\n            _this.toastr.info(\"Please fill all required fields\");\n\n            return;\n          } else {\n            _this.ngxSpinnerService.show('globalSpinner');\n\n            yield _this.uploadLogoUrl();\n            const fileUrl = _this.baseUrl + _this.imageName.name;\n            _this.addRoleForm.value.RO_dp = fileUrl;\n            _this.addRoleForm.value.RO_IndustryId = _this.sectorId;\n            console.log(\"Add role post data : \", _this.addRoleForm.value);\n            const res = yield _this.dataTransferService.insertRoleData(_this.addRoleForm.value).toPromise();\n\n            _this.ngxSpinnerService.hide('globalSpinner');\n\n            if (res.statusCode == 200) {\n              _this.toastr.success(\"Role added successfully.\");\n\n              const state = {\n                sectorId: _this.sectorId\n              };\n\n              _this.router.navigate([`actions/sectors/roles`], {\n                state\n              });\n\n              _this.dataTransferService.getAllRoleBySectorId(_this.sectorId);\n            } else {\n              _this.toastr.error(\"Something went wrong.\");\n\n              console.error('Unable to add role. Status:', res.status);\n            }\n          }\n        }\n      } catch (error) {\n        _this.ngxSpinnerService.hide('globalSpinner');\n\n        console.error('Error:', error);\n\n        _this.toastr.error(\"Something went wrong.\");\n      }\n    })();\n  }\n\n  addRoleCnlBtn() {\n    const state = {\n      sectorId: this.sectorId\n    };\n    this.router.navigate([`actions/sectors/roles`], {\n      state\n    });\n  }\n\n  addTag(tag, formArrayName) {\n    if (tag.trim() !== '') {\n      const formArray = this.addRoleForm.get(formArrayName);\n      formArray.insert(0, new FormControl(tag.trim())); // Insert at the beginning\n    }\n  }\n\n  removeTagForField(index, formArrayName) {\n    const formArray = this.addRoleForm.get(formArrayName);\n    formArray.removeAt(index);\n  }\n\n  onEnterKey(event, controlName) {\n    if (event.key === 'Enter' || event.key === 'Tab') {\n      if (event.key === 'Enter') {\n        event.preventDefault();\n      }\n\n      const inputElement = event.target;\n      const tag = inputElement.value.trim();\n\n      if (tag !== '') {\n        this.addTag(tag, controlName);\n        inputElement.value = ''; // Clear the input field after adding the tag\n      }\n    } // if (event.key === 'Tab') {\n    //   const inputElement = event.target as HTMLInputElement;\n    //   const tag = inputElement.value.trim();\n    //   if (tag !== '') {\n    //     this.addTag(tag, controlName);\n    //     inputElement.value = ''; // Clear the input field after adding the tag\n    //   }\n    // }\n\n  }\n\n  onInputBlur(event, controlName) {\n    const inputElement = event.target; // Type assertion\n\n    const trimmedTag = inputElement.value.trim();\n\n    if (trimmedTag !== '') {\n      this.addTag(trimmedTag, controlName);\n      inputElement.value = '';\n    }\n  }\n\n  onBackspaceKey(event, formArrayName) {\n    if (event.key === 'Delete') {\n      const inputElement = event.target;\n\n      if (inputElement.value === '' && !event.shiftKey) {\n        event.preventDefault();\n        this.removeLastTag(formArrayName);\n      }\n    }\n  } // Method to remove the last tag from the specified form array\n\n\n  removeLastTag(formArrayName) {\n    const formArray = this.addRoleForm.get(formArrayName);\n\n    if (formArray.length > 0) {\n      formArray.removeAt(0); // Remove the first tag\n    }\n  }\n\n  addExpectedActivity(event) {\n    const activity = event.target.value;\n\n    if (activity.trim() !== '') {\n      const formArray = this.addRoleForm.get('RO_Expected_Activities');\n      const isDuplicate = formArray.controls.some(control => control.value === activity);\n\n      if (!isDuplicate) {\n        formArray.insert(0, new FormControl(activity.trim()));\n      } else {\n        console.warn('Activity already selected');\n        this.toastr.info('Activity already selected');\n      } // Reset the value of the select element\n\n\n      event.target.value = '';\n    }\n  }\n\n  getAllRoleBySectorId(sectorId) {\n    console.log(\"sectorId : \", sectorId);\n    this.dataTransferService.getAllRoleBySectorId(sectorId).subscribe({\n      next: res => {\n        if (res.statusCode === 200) {\n          this.RoleList = res.data;\n          console.log(\"RoleList : \", this.RoleList);\n        } else {\n          this.ngxSpinnerService.hide('globalSpinner');\n          console.error('Failed to fetch role. Status:', res.status);\n        }\n      },\n      error: error => {\n        console.error('Error occurred while fetching roles:', error);\n      }\n    });\n  }\n\n  getAllExpctedActivities() {\n    this.dataTransferService.getAllExpctedActivities().subscribe({\n      next: res => {\n        if (res.statuscode === 200) {\n          console.log(\"expectedActivitiesList res\", res);\n          this.expectedActivitiesList = res.filteredData[0].quiz_options;\n          console.log(\"expectedActivitiesList : \", this.expectedActivitiesList);\n        } else {\n          console.error('Failed to fetch role. Status:', res.status);\n        }\n      }\n    });\n  }\n\n  updateSalaryRange(range) {\n    const selectedRange = range.target.value;\n\n    if (selectedRange === '<25k') {\n      this.addRoleForm.patchValue({\n        RO_Salary_from: \"0\",\n        RO_Salary_to: \"25000\"\n      });\n    } else if (selectedRange === '25-30k') {\n      this.addRoleForm.patchValue({\n        RO_Salary_from: \"25000\",\n        RO_Salary_to: \"30000\"\n      });\n    } else if (selectedRange === '30-35k') {\n      this.addRoleForm.patchValue({\n        RO_Salary_from: \"30000\",\n        RO_Salary_to: \"35000\"\n      });\n    } else if (selectedRange === '35k+') {\n      this.addRoleForm.patchValue({\n        RO_Salary_from: \"35000\",\n        RO_Salary_to: \"100000\"\n      });\n    }\n  }\n\n}\n\nAddEditRoleComponent.ɵfac = function AddEditRoleComponent_Factory(t) {\n  return new (t || AddEditRoleComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.NgxSpinnerService));\n};\n\nAddEditRoleComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AddEditRoleComponent,\n  selectors: [[\"app-add-edit-role\"]],\n  decls: 128,\n  vars: 30,\n  consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\", \"col-lg-4\"], [\"for\", \"RO_title\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"RO_title\", \"required\", \"\", \"placeholder\", \"Enter title\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"class\", \"form-group col-lg-4\", 4, \"ngIf\"], [\"for\", \"RO_References\", 1, \"required-field\"], [1, \"tag-input-container\"], [\"type\", \"text\", \"class\", \"tag-input form-control form-control-sm\", \"placeholder\", \"Add New +\", \"required\", \"\", 3, \"readonly\", \"keydown\", \"blur\", 4, \"ngIf\"], [1, \"tag-box\"], [\"class\", \"tag\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"RO_Skills_and_Knowledge\", 1, \"required-field\"], [\"for\", \"RO_Place_of_Work\", 1, \"required-field\"], [\"for\", \"RO_Key_Insights\", 1, \"required-field\"], [\"for\", \"RO_Salary\", 1, \"required-field\"], [\"for\", \"RO_Daily_Tasks\", 1, \"required-field\"], [\"for\", \"RO_Expected_Activities\", 1, \"required-field\"], [\"class\", \"form-control tag-input form-control-sm\", \"required\", \"\", 3, \"disabled\", \"change\", \"keydown\", 4, \"ngIf\"], [\"for\", \"RO_Expected_to_on_the_weekends\", 1, \"required-field\"], [\"formControlName\", \"RO_Expected_to_on_the_weekends\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"value\", \"\", \"disabled\", \"\", \"selected\", \"\"], [\"value\", \"Yes\"], [\"value\", \"No\"], [\"for\", \"RO_Expected_to_work_beyond_nine_to_five\", 1, \"required-field\"], [\"formControlName\", \"RO_Expected_to_work_beyond_nine_to_five\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"for\", \"RO_Expected_to_work_internationally\", 1, \"required-field\"], [\"formControlName\", \"RO_Expected_to_work_internationally\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"for\", \"RO_Expected_to_work_in_the_office\", 1, \"required-field\"], [\"formControlName\", \"RO_Expected_to_work_in_the_office\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"for\", \"RO_isAccept\", 1, \"required-field\"], [\"formControlName\", \"RO_isAccept\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"value\", \"\", \"disabled\", \"\"], [3, \"value\"], [\"for\", \"RO_description\", 1, \"required-field\"], [\"formControlName\", \"RO_description\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Role description \", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [1, \"text-center\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [1, \"btn\", \"btn-light\", 3, \"click\"], [\"for\", \"RO_dp\", 1, \"required-field\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"RO_dp\", \"required\", \"\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Role Image\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"alt\", \"Role Image\", 1, \"img-preview\", 3, \"src\"], [1, \"warning\"], [\"for\", \"RO_dp\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"RO_dp\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [1, \"text-danger\"], [\"type\", \"text\", \"placeholder\", \"Add New +\", \"required\", \"\", 1, \"tag-input\", \"form-control\", \"form-control-sm\", 3, \"readonly\", \"keydown\", \"blur\"], [1, \"tag\", 3, \"ngClass\"], [\"class\", \"close\", 3, \"click\", 4, \"ngIf\"], [1, \"close\", 3, \"click\"], [\"for\", \"salaryRange\", 1, \"required-field\"], [\"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"change\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [\"value\", \"<25k\"], [\"value\", \"25-30k\"], [\"value\", \"30-35k\"], [\"value\", \"35k+\"], [\"type\", \"text\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"value\", \"readonly\"], [\"required\", \"\", 1, \"form-control\", \"tag-input\", \"form-control-sm\", 3, \"disabled\", \"change\", \"keydown\"], [\"selected\", \"\", \"disabled\", \"\", \"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]],\n  template: function AddEditRoleComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"app-sidebar\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵtext(6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵelementStart(8, \"form\", 6);\n      i0.ɵɵlistener(\"ngSubmit\", function AddEditRoleComponent_Template_form_ngSubmit_8_listener() {\n        return ctx.addRole();\n      });\n      i0.ɵɵelementStart(9, \"div\", 1);\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵelementStart(11, \"label\", 8);\n      i0.ɵɵtext(12, \"Role Title\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(13, \"input\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(14, AddEditRoleComponent_div_14_Template, 7, 3, \"div\", 10);\n      i0.ɵɵtemplate(15, AddEditRoleComponent_div_15_Template, 5, 2, \"div\", 10);\n      i0.ɵɵelementStart(16, \"div\", 7);\n      i0.ɵɵelementStart(17, \"label\", 11);\n      i0.ɵɵtext(18, \"Role Resources\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"div\", 12);\n      i0.ɵɵtemplate(20, AddEditRoleComponent_input_20_Template, 1, 1, \"input\", 13);\n      i0.ɵɵelementStart(21, \"div\", 14);\n      i0.ɵɵtemplate(22, AddEditRoleComponent_div_22_Template, 3, 5, \"div\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(23, \"div\", 7);\n      i0.ɵɵelementStart(24, \"label\", 16);\n      i0.ɵɵtext(25, \"Skills And Knowledge\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(26, \"div\", 12);\n      i0.ɵɵtemplate(27, AddEditRoleComponent_input_27_Template, 1, 1, \"input\", 13);\n      i0.ɵɵelementStart(28, \"div\", 14);\n      i0.ɵɵtemplate(29, AddEditRoleComponent_div_29_Template, 3, 5, \"div\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"div\", 7);\n      i0.ɵɵelementStart(31, \"label\", 17);\n      i0.ɵɵtext(32, \"Place Of Work\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"div\", 12);\n      i0.ɵɵtemplate(34, AddEditRoleComponent_input_34_Template, 1, 1, \"input\", 13);\n      i0.ɵɵelementStart(35, \"div\", 14);\n      i0.ɵɵtemplate(36, AddEditRoleComponent_div_36_Template, 3, 5, \"div\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"div\", 7);\n      i0.ɵɵelementStart(38, \"label\", 18);\n      i0.ɵɵtext(39, \"Role Key Highlights\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"div\", 12);\n      i0.ɵɵtemplate(41, AddEditRoleComponent_input_41_Template, 1, 1, \"input\", 13);\n      i0.ɵɵelementStart(42, \"div\", 14);\n      i0.ɵɵtemplate(43, AddEditRoleComponent_div_43_Template, 3, 5, \"div\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(44, \"div\", 1);\n      i0.ɵɵelementStart(45, \"div\", 7);\n      i0.ɵɵelementStart(46, \"label\", 19);\n      i0.ɵɵtext(47, \"Role Salary\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(48, \"div\", 12);\n      i0.ɵɵtemplate(49, AddEditRoleComponent_input_49_Template, 1, 1, \"input\", 13);\n      i0.ɵɵelementStart(50, \"div\", 14);\n      i0.ɵɵtemplate(51, AddEditRoleComponent_div_51_Template, 3, 5, \"div\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(52, AddEditRoleComponent_div_52_Template, 14, 1, \"div\", 10);\n      i0.ɵɵtemplate(53, AddEditRoleComponent_div_53_Template, 4, 2, \"div\", 10);\n      i0.ɵɵelementStart(54, \"div\", 7);\n      i0.ɵɵelementStart(55, \"label\", 20);\n      i0.ɵɵtext(56, \"Role Daily Tasks\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(57, \"div\", 12);\n      i0.ɵɵtemplate(58, AddEditRoleComponent_input_58_Template, 1, 1, \"input\", 13);\n      i0.ɵɵelementStart(59, \"div\", 14);\n      i0.ɵɵtemplate(60, AddEditRoleComponent_div_60_Template, 3, 5, \"div\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(61, \"div\", 1);\n      i0.ɵɵelementStart(62, \"div\", 7);\n      i0.ɵɵelementStart(63, \"label\", 21);\n      i0.ɵɵtext(64, \"Role Expected Activities\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(65, \"div\", 12);\n      i0.ɵɵtemplate(66, AddEditRoleComponent_select_66_Template, 4, 2, \"select\", 22);\n      i0.ɵɵelementStart(67, \"div\", 14);\n      i0.ɵɵtemplate(68, AddEditRoleComponent_div_68_Template, 3, 5, \"div\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(69, \"div\", 7);\n      i0.ɵɵelementStart(70, \"label\", 23);\n      i0.ɵɵtext(71, \"Expected To On The Weekends\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(72, \"select\", 24);\n      i0.ɵɵelementStart(73, \"option\", 25);\n      i0.ɵɵtext(74, \"Expected to on the weekends\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(75, \"option\", 26);\n      i0.ɵɵtext(76, \"Yes\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(77, \"option\", 27);\n      i0.ɵɵtext(78, \"No\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(79, \"div\", 7);\n      i0.ɵɵelementStart(80, \"label\", 28);\n      i0.ɵɵtext(81, \"Expected To Work Beyond Nine To Five\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(82, \"select\", 29);\n      i0.ɵɵelementStart(83, \"option\", 25);\n      i0.ɵɵtext(84, \"Expected to work beyond nine to five\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(85, \"option\", 26);\n      i0.ɵɵtext(86, \"Yes\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(87, \"option\", 27);\n      i0.ɵɵtext(88, \"No\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(89, \"div\", 1);\n      i0.ɵɵelementStart(90, \"div\", 7);\n      i0.ɵɵelementStart(91, \"label\", 30);\n      i0.ɵɵtext(92, \"Expected To Work Internationally\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(93, \"select\", 31);\n      i0.ɵɵelementStart(94, \"option\", 25);\n      i0.ɵɵtext(95, \"Expected to work internationally\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(96, \"option\", 26);\n      i0.ɵɵtext(97, \"Yes\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(98, \"option\", 27);\n      i0.ɵɵtext(99, \"No\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(100, \"div\", 7);\n      i0.ɵɵelementStart(101, \"label\", 32);\n      i0.ɵɵtext(102, \"Expected to Work In The Office\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(103, \"select\", 33);\n      i0.ɵɵelementStart(104, \"option\", 25);\n      i0.ɵɵtext(105, \"Expected to work in the office\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(106, \"option\", 26);\n      i0.ɵɵtext(107, \"Yes\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(108, \"option\", 27);\n      i0.ɵɵtext(109, \"No\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(110, \"div\", 7);\n      i0.ɵɵelementStart(111, \"label\", 34);\n      i0.ɵɵtext(112, \"Role Status\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(113, \"select\", 35);\n      i0.ɵɵelementStart(114, \"option\", 36);\n      i0.ɵɵtext(115, \"Please select a status\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(116, \"option\", 37);\n      i0.ɵɵtext(117, \"Approve\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(118, \"option\", 37);\n      i0.ɵɵtext(119, \"Not Approve\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(120, \"div\", 7);\n      i0.ɵɵelementStart(121, \"label\", 38);\n      i0.ɵɵtext(122, \"Role Description\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(123, \"textarea\", 39);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(124, \"div\", 40);\n      i0.ɵɵtemplate(125, AddEditRoleComponent_button_125_Template, 2, 0, \"button\", 41);\n      i0.ɵɵelementStart(126, \"button\", 42);\n      i0.ɵɵlistener(\"click\", function AddEditRoleComponent_Template_button_click_126_listener() {\n        return ctx.addRoleCnlBtn();\n      });\n      i0.ɵɵtext(127, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      let tmp_6_0;\n      let tmp_8_0;\n      let tmp_10_0;\n      let tmp_12_0;\n      let tmp_14_0;\n      let tmp_18_0;\n      let tmp_20_0;\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Role\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.addRoleForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isReadonly);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", (tmp_6_0 = ctx.addRoleForm.get(\"RO_References\")) == null ? null : tmp_6_0.value);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", (tmp_8_0 = ctx.addRoleForm.get(\"RO_Skills_and_Knowledge\")) == null ? null : tmp_8_0.value);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", (tmp_10_0 = ctx.addRoleForm.get(\"RO_Place_of_Work\")) == null ? null : tmp_10_0.value);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", (tmp_12_0 = ctx.addRoleForm.get(\"RO_Key_Insights\")) == null ? null : tmp_12_0.value);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", (tmp_14_0 = ctx.addRoleForm.get(\"RO_Salary\")) == null ? null : tmp_14_0.value);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isReadonly);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", (tmp_18_0 = ctx.addRoleForm.get(\"RO_Daily_Tasks\")) == null ? null : tmp_18_0.value);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", (tmp_20_0 = ctx.addRoleForm.get(\"RO_Expected_Activities\")) == null ? null : tmp_20_0.value);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"value\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"value\", false);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n    }\n  },\n  directives: [i7.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.RequiredValidator, i8.NgIf, i8.NgForOf, i1.SelectControlValueAccessor, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i8.NgClass],\n  styles: [\".tag-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  \\n  border: 1px solid #ced4da;\\n  \\n  border-radius: 5px;\\n  \\n  padding: 5px;\\n  \\n  overflow-x: auto;\\n  \\n  overflow-y: hidden;\\n  \\n  height: 50px;\\n}\\n\\n.tag.view-mode[_ngcontent-%COMP%] {\\n  background-color: #e2e4e7;\\n  color: #495057;\\n}\\n\\n.tag-input-container[_ngcontent-%COMP%]:focus-within {\\n  outline: 1px solid rgba(0, 123, 255, 0.65);\\n  \\n  transition: outline 0.3s ease;\\n  \\n}\\n\\n.tag-box[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  \\n  flex-wrap: nowrap;\\n  \\n  grid-gap: 5px;\\n  gap: 5px;\\n  \\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  background-color: #4B49AC;\\n  color: #fff;\\n  padding: 2px 5px;\\n  margin-top: 2px;\\n  margin-bottom: 2px;\\n  border-radius: 10px;\\n  display: flex;\\n  \\n  align-items: center;\\n  max-height: auto;\\n  white-space: nowrap;\\n  \\n  overflow: hidden;\\n  \\n}\\n\\n.tag[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  margin-left: 5px;\\n  margin-top: 2px;\\n  margin-bottom: 2px;\\n  cursor: pointer;\\n  border: none;\\n  outline: none;\\n  color: #fff;\\n}\\n\\n.tag-input[_ngcontent-%COMP%] {\\n  border: none;\\n  outline: none;\\n  margin: 2px 5px 2px 3px;\\n  \\n  max-height: 30px;\\n  min-width: 150px;\\n  \\n}\\n\\n.tag-input[_ngcontent-%COMP%]:focus-within {\\n  border: none;\\n  outline: none;\\n}\\n\\n\\n\\n.tag-input-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n  \\n}\\n\\n.tag-input-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #888;\\n  \\n  border-radius: 4px;\\n  \\n}\\n\\n\\n\\n.tag-input-container[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  \\n}\\n\\n.tag-input-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #888;\\n  \\n  border-radius: 4px;\\n  \\n}\\n\\ninput[type=text][_ngcontent-%COMP%], input[type=file][_ngcontent-%COMP%], input[type=number][_ngcontent-%COMP%], select[_ngcontent-%COMP%] {\\n  height: 50px;\\n  \\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4uXFwuLlxcLi5cXFNlY3RvcnMlMjBhbmQlMjByb2xlc1xcQWxsJTIwUm9sZXNcXGFkZC1lZGl0LXJvbGVcXGFkZC1lZGl0LXJvbGUuY29tcG9uZW50LnNjc3MiLCJhZGQtZWRpdC1yb2xlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQXlCLDJCQUFBO0VBQ3pCLHlCQUFBO0VBQTJCLGdDQUFBO0VBQzNCLGtCQUFBO0VBQW9CLDBDQUFBO0VBQ3BCLFlBQUE7RUFBYyxpQ0FBQTtFQUNkLGdCQUFBO0VBQWtCLGdDQUFBO0VBQ2xCLGtCQUFBO0VBQW9CLGdDQUFBO0VBQ3BCLFlBQUE7QUNPRjs7QURIQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQ01GOztBREhBO0VBQ0UsMENBQUE7RUFBNEMsdUJBQUE7RUFDNUMsNkJBQUE7RUFBK0Isc0JBQUE7QUNRakM7O0FESkE7RUFDRSxvQkFBQTtFQUFzQix3QkFBQTtFQUN0QixpQkFBQTtFQUFtQixnREFBQTtFQUNuQixhQUFBO0VBQUEsUUFBQTtFQUFVLHlCQUFBO0FDVVo7O0FEUEE7RUFDRSx5QkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUFlLCtCQUFBO0VBQ2YsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0VBQXFCLHNDQUFBO0VBQ3JCLGdCQUFBO0VBQWtCLDZCQUFBO0FDYXBCOztBRFZBO0VBQ0UsZ0JBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxXQUFBO0FDYUY7O0FEVkE7RUFDRSxZQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQXdCLHFDQUFBO0VBQ3hCLGdCQUFBO0VBQ0EsZ0JBQUE7RUFBa0Isb0NBQUE7QUNlcEI7O0FEWkE7RUFDRSxZQUFBO0VBQ0EsYUFBQTtBQ2VGOztBRFpBLHFEQUFBOztBQUNBO0VBQ0UsVUFBQTtFQUFZLG1DQUFBO0FDZ0JkOztBRGJBO0VBQ0Usc0JBQUE7RUFBd0IseUNBQUE7RUFDeEIsa0JBQUE7RUFBb0IsaURBQUE7QUNrQnRCOztBRGZBLGdCQUFBOztBQUNBO0VBQ0UscUJBQUE7RUFBdUIsbUNBQUE7QUNtQnpCOztBRGhCQTtFQUNFLHNCQUFBO0VBQXdCLHlDQUFBO0VBQ3hCLGtCQUFBO0VBQW9CLGlEQUFBO0FDcUJ0Qjs7QURsQkE7Ozs7RUFJRSxZQUFBO0VBQWMsdUJBQUE7QUNzQmhCIiwiZmlsZSI6ImFkZC1lZGl0LXJvbGUuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIudGFnLWlucHV0LWNvbnRhaW5lciB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsgLyogQWxpZ24gaXRlbXMgYXQgdGhlIHRvcCAqL1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNjZWQ0ZGE7IC8qIEFkZCBib3JkZXIgdG8gdGhlIGNvbnRhaW5lciAqL1xyXG4gIGJvcmRlci1yYWRpdXM6IDVweDsgLyogQWRkIGJvcmRlci1yYWRpdXMgZm9yIHJvdW5kZWQgY29ybmVycyAqL1xyXG4gIHBhZGRpbmc6IDVweDsgLyogQWRkIHNvbWUgcGFkZGluZyBmb3Igc3BhY2luZyAqL1xyXG4gIG92ZXJmbG93LXg6IGF1dG87IC8qIEVuYWJsZSBob3Jpem9udGFsIHNjcm9sbGluZyAqL1xyXG4gIG92ZXJmbG93LXk6IGhpZGRlbjsgLyogRW5hYmxlIGhvcml6b250YWwgc2Nyb2xsaW5nICovXHJcbiAgaGVpZ2h0OiA1MHB4O1xyXG59XHJcblxyXG5cclxuLnRhZy52aWV3LW1vZGUge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNlMmU0ZTc7XHJcbiAgY29sb3I6ICM0OTUwNTc7XHJcbn1cclxuXHJcbi50YWctaW5wdXQtY29udGFpbmVyOmZvY3VzLXdpdGhpbntcclxuICBvdXRsaW5lOiAxcHggc29saWQgcmdiYSgwLCAxMjMsIDI1NSwgMC42NSk7IC8qIEFwcGx5IGJsdWUgb3V0bGluZSAqL1xyXG4gIHRyYW5zaXRpb246IG91dGxpbmUgMC4zcyBlYXNlOyAvKiBTbW9vdGggdHJhbnNpdGlvbiAqL1xyXG5cclxufVxyXG5cclxuLnRhZy1ib3gge1xyXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4OyAvKiBEaXNwbGF5IHRhZ3MgaW5saW5lICovXHJcbiAgZmxleC13cmFwOiBub3dyYXA7IC8qIFByZXZlbnQgdGFncyBmcm9tIHdyYXBwaW5nIHRvIHRoZSBuZXh0IGxpbmUgKi9cclxuICBnYXA6IDVweDsgLyogQWRkIGdhcCBiZXR3ZWVuIHRhZ3MgKi9cclxufVxyXG5cclxuLnRhZyB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzRCNDlBQztcclxuICBjb2xvcjogI2ZmZjtcclxuICBwYWRkaW5nOiAycHggNXB4O1xyXG4gIG1hcmdpbi10b3A6IDJweDtcclxuICBtYXJnaW4tYm90dG9tOiAycHg7XHJcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcclxuICBkaXNwbGF5OiBmbGV4OyAvKiBVc2UgZmxleGJveCB0byBhbGlnbiBpdGVtcyAqL1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgbWF4LWhlaWdodDogYXV0bztcclxuICB3aGl0ZS1zcGFjZTogbm93cmFwOyAvKiBQcmV2ZW50IHRhZyBjb250ZW50IGZyb20gd3JhcHBpbmcgKi9cclxuICBvdmVyZmxvdzogaGlkZGVuOyAvKiBIaWRlIG92ZXJmbG93aW5nIGNvbnRlbnQgKi9cclxufVxyXG5cclxuLnRhZyAuY2xvc2Uge1xyXG4gIG1hcmdpbi1sZWZ0OiA1cHg7XHJcbiAgbWFyZ2luLXRvcDogMnB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDJweDtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIG91dGxpbmU6IG5vbmU7XHJcbiAgY29sb3I6ICNmZmY7XHJcbn1cclxuXHJcbi50YWctaW5wdXQge1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICBvdXRsaW5lOiBub25lO1xyXG4gIG1hcmdpbjoycHggNXB4IDJweCAzcHg7IC8qIFB1c2ggaW5wdXQgYm94IHRvIHRoZSByaWdodCBzaWRlICovXHJcbiAgbWF4LWhlaWdodDogMzBweDtcclxuICBtaW4td2lkdGg6IDE1MHB4OyAvKiBTZXQgbWluaW11bSB3aWR0aCBmb3IgaW5wdXQgYm94ICovXHJcbn1cclxuXHJcbi50YWctaW5wdXQ6Zm9jdXMtd2l0aGlue1xyXG4gIGJvcmRlcjogbm9uZTtcclxuICBvdXRsaW5lOiBub25lO1xyXG59XHJcblxyXG4vKiBGb3IgV2ViS2l0IGJhc2VkIGJyb3dzZXJzIChlLmcuLCBDaHJvbWUsIFNhZmFyaSkgKi9cclxuLnRhZy1pbnB1dC1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyIHtcclxuICB3aWR0aDogNHB4OyAvKiBTZXQgdGhlIHdpZHRoIG9mIHRoZSBzY3JvbGxiYXIgKi9cclxufVxyXG5cclxuLnRhZy1pbnB1dC1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjODg4OyAvKiBTZXQgdGhlIGNvbG9yIG9mIHRoZSBzY3JvbGxiYXIgdGh1bWIgKi9cclxuICBib3JkZXItcmFkaXVzOiA0cHg7IC8qIFNldCB0aGUgYm9yZGVyLXJhZGl1cyBvZiB0aGUgc2Nyb2xsYmFyIHRodW1iICovXHJcbn1cclxuXHJcbi8qIEZvciBGaXJlZm94ICovXHJcbi50YWctaW5wdXQtY29udGFpbmVyIHtcclxuICBzY3JvbGxiYXItd2lkdGg6IHRoaW47IC8qIFNldCB0aGUgd2lkdGggb2YgdGhlIHNjcm9sbGJhciAqL1xyXG59XHJcblxyXG4udGFnLWlucHV0LWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICM4ODg7IC8qIFNldCB0aGUgY29sb3Igb2YgdGhlIHNjcm9sbGJhciB0aHVtYiAqL1xyXG4gIGJvcmRlci1yYWRpdXM6IDRweDsgLyogU2V0IHRoZSBib3JkZXItcmFkaXVzIG9mIHRoZSBzY3JvbGxiYXIgdGh1bWIgKi9cclxufVxyXG5cclxuaW5wdXRbdHlwZT1cInRleHRcIl0sXHJcbmlucHV0W3R5cGU9XCJmaWxlXCJdLFxyXG5pbnB1dFt0eXBlPVwibnVtYmVyXCJdLFxyXG5zZWxlY3Qge1xyXG4gIGhlaWdodDogNTBweDsgLyogU2V0IGRlc2lyZWQgaGVpZ2h0ICovXHJcbiAgXHJcbn1cclxuIiwiLnRhZy1pbnB1dC1jb250YWluZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgLyogQWxpZ24gaXRlbXMgYXQgdGhlIHRvcCAqL1xuICBib3JkZXI6IDFweCBzb2xpZCAjY2VkNGRhO1xuICAvKiBBZGQgYm9yZGVyIHRvIHRoZSBjb250YWluZXIgKi9cbiAgYm9yZGVyLXJhZGl1czogNXB4O1xuICAvKiBBZGQgYm9yZGVyLXJhZGl1cyBmb3Igcm91bmRlZCBjb3JuZXJzICovXG4gIHBhZGRpbmc6IDVweDtcbiAgLyogQWRkIHNvbWUgcGFkZGluZyBmb3Igc3BhY2luZyAqL1xuICBvdmVyZmxvdy14OiBhdXRvO1xuICAvKiBFbmFibGUgaG9yaXpvbnRhbCBzY3JvbGxpbmcgKi9cbiAgb3ZlcmZsb3cteTogaGlkZGVuO1xuICAvKiBFbmFibGUgaG9yaXpvbnRhbCBzY3JvbGxpbmcgKi9cbiAgaGVpZ2h0OiA1MHB4O1xufVxuXG4udGFnLnZpZXctbW9kZSB7XG4gIGJhY2tncm91bmQtY29sb3I6ICNlMmU0ZTc7XG4gIGNvbG9yOiAjNDk1MDU3O1xufVxuXG4udGFnLWlucHV0LWNvbnRhaW5lcjpmb2N1cy13aXRoaW4ge1xuICBvdXRsaW5lOiAxcHggc29saWQgcmdiYSgwLCAxMjMsIDI1NSwgMC42NSk7XG4gIC8qIEFwcGx5IGJsdWUgb3V0bGluZSAqL1xuICB0cmFuc2l0aW9uOiBvdXRsaW5lIDAuM3MgZWFzZTtcbiAgLyogU21vb3RoIHRyYW5zaXRpb24gKi9cbn1cblxuLnRhZy1ib3gge1xuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbiAgLyogRGlzcGxheSB0YWdzIGlubGluZSAqL1xuICBmbGV4LXdyYXA6IG5vd3JhcDtcbiAgLyogUHJldmVudCB0YWdzIGZyb20gd3JhcHBpbmcgdG8gdGhlIG5leHQgbGluZSAqL1xuICBnYXA6IDVweDtcbiAgLyogQWRkIGdhcCBiZXR3ZWVuIHRhZ3MgKi9cbn1cblxuLnRhZyB7XG4gIGJhY2tncm91bmQtY29sb3I6ICM0QjQ5QUM7XG4gIGNvbG9yOiAjZmZmO1xuICBwYWRkaW5nOiAycHggNXB4O1xuICBtYXJnaW4tdG9wOiAycHg7XG4gIG1hcmdpbi1ib3R0b206IDJweDtcbiAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgZGlzcGxheTogZmxleDtcbiAgLyogVXNlIGZsZXhib3ggdG8gYWxpZ24gaXRlbXMgKi9cbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgbWF4LWhlaWdodDogYXV0bztcbiAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgLyogUHJldmVudCB0YWcgY29udGVudCBmcm9tIHdyYXBwaW5nICovXG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIC8qIEhpZGUgb3ZlcmZsb3dpbmcgY29udGVudCAqL1xufVxuXG4udGFnIC5jbG9zZSB7XG4gIG1hcmdpbi1sZWZ0OiA1cHg7XG4gIG1hcmdpbi10b3A6IDJweDtcbiAgbWFyZ2luLWJvdHRvbTogMnB4O1xuICBjdXJzb3I6IHBvaW50ZXI7XG4gIGJvcmRlcjogbm9uZTtcbiAgb3V0bGluZTogbm9uZTtcbiAgY29sb3I6ICNmZmY7XG59XG5cbi50YWctaW5wdXQge1xuICBib3JkZXI6IG5vbmU7XG4gIG91dGxpbmU6IG5vbmU7XG4gIG1hcmdpbjogMnB4IDVweCAycHggM3B4O1xuICAvKiBQdXNoIGlucHV0IGJveCB0byB0aGUgcmlnaHQgc2lkZSAqL1xuICBtYXgtaGVpZ2h0OiAzMHB4O1xuICBtaW4td2lkdGg6IDE1MHB4O1xuICAvKiBTZXQgbWluaW11bSB3aWR0aCBmb3IgaW5wdXQgYm94ICovXG59XG5cbi50YWctaW5wdXQ6Zm9jdXMtd2l0aGluIHtcbiAgYm9yZGVyOiBub25lO1xuICBvdXRsaW5lOiBub25lO1xufVxuXG4vKiBGb3IgV2ViS2l0IGJhc2VkIGJyb3dzZXJzIChlLmcuLCBDaHJvbWUsIFNhZmFyaSkgKi9cbi50YWctaW5wdXQtY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhciB7XG4gIHdpZHRoOiA0cHg7XG4gIC8qIFNldCB0aGUgd2lkdGggb2YgdGhlIHNjcm9sbGJhciAqL1xufVxuXG4udGFnLWlucHV0LWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjODg4O1xuICAvKiBTZXQgdGhlIGNvbG9yIG9mIHRoZSBzY3JvbGxiYXIgdGh1bWIgKi9cbiAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAvKiBTZXQgdGhlIGJvcmRlci1yYWRpdXMgb2YgdGhlIHNjcm9sbGJhciB0aHVtYiAqL1xufVxuXG4vKiBGb3IgRmlyZWZveCAqL1xuLnRhZy1pbnB1dC1jb250YWluZXIge1xuICBzY3JvbGxiYXItd2lkdGg6IHRoaW47XG4gIC8qIFNldCB0aGUgd2lkdGggb2YgdGhlIHNjcm9sbGJhciAqL1xufVxuXG4udGFnLWlucHV0LWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjODg4O1xuICAvKiBTZXQgdGhlIGNvbG9yIG9mIHRoZSBzY3JvbGxiYXIgdGh1bWIgKi9cbiAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAvKiBTZXQgdGhlIGJvcmRlci1yYWRpdXMgb2YgdGhlIHNjcm9sbGJhciB0aHVtYiAqL1xufVxuXG5pbnB1dFt0eXBlPXRleHRdLFxuaW5wdXRbdHlwZT1maWxlXSxcbmlucHV0W3R5cGU9bnVtYmVyXSxcbnNlbGVjdCB7XG4gIGhlaWdodDogNTBweDtcbiAgLyogU2V0IGRlc2lyZWQgaGVpZ2h0ICovXG59Il19 */\"]\n});", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/src/app/shared/sidebar/actions/Sectors and roles/All Roles/add-edit-role/add-edit-role.component.ts"], "names": ["FormControl", "Validators", "FileValidator", "i0", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "i8", "AddEditRoleComponent_div_14_img_5_Template", "rf", "ctx", "ɵɵelement", "ctx_r19", "ɵɵnextContext", "ɵɵproperty", "imageSrc", "ɵɵsanitizeUrl", "AddEditRoleComponent_div_14_div_6_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "AddEditRoleComponent_div_14_Template", "_r22", "ɵɵgetCurrentView", "ɵɵlistener", "AddEditRoleComponent_div_14_Template_input_change_4_listener", "$event", "ɵɵrestoreView", "ctx_r21", "onFileSelected", "ɵɵtemplate", "ctx_r0", "tmp_2_0", "ɵɵadvance", "is<PERSON><PERSON><PERSON>ly", "addRoleForm", "get", "errors", "fileSizeValidator", "AddEditRoleComponent_div_15_span_3_Template", "AddEditRoleComponent_div_15_Template", "ctx_r1", "AddEditRoleComponent_input_20_Template", "_r25", "AddEditRoleComponent_input_20_Template_input_keydown_0_listener", "ctx_r24", "onEnterKey", "ctx_r26", "onBackspaceKey", "AddEditRoleComponent_input_20_Template_input_blur_0_listener", "ctx_r27", "onInputBlur", "ctx_r2", "AddEditRoleComponent_div_22_span_2_Template", "_r33", "AddEditRoleComponent_div_22_span_2_Template_span_click_0_listener", "i_r29", "index", "ctx_r31", "removeTagForField", "_c0", "a0", "AddEditRoleComponent_div_22_Template", "tag_r28", "$implicit", "ctx_r3", "ɵɵpureFunction1", "title", "ɵɵtextInterpolate1", "AddEditRoleComponent_input_27_Template", "_r35", "AddEditRoleComponent_input_27_Template_input_keydown_0_listener", "ctx_r34", "ctx_r36", "AddEditRoleComponent_input_27_Template_input_blur_0_listener", "ctx_r37", "ctx_r4", "AddEditRoleComponent_div_29_span_2_Template", "_r43", "AddEditRoleComponent_div_29_span_2_Template_span_click_0_listener", "i_r39", "ctx_r41", "AddEditRoleComponent_div_29_Template", "tag_r38", "ctx_r5", "AddEditRoleComponent_input_34_Template", "_r45", "AddEditRoleComponent_input_34_Template_input_keydown_0_listener", "ctx_r44", "ctx_r46", "AddEditRoleComponent_input_34_Template_input_blur_0_listener", "ctx_r47", "ctx_r6", "AddEditRoleComponent_div_36_span_2_Template", "_r53", "AddEditRoleComponent_div_36_span_2_Template_span_click_0_listener", "i_r49", "ctx_r51", "AddEditRoleComponent_div_36_Template", "tag_r48", "ctx_r7", "AddEditRoleComponent_input_41_Template", "_r55", "AddEditRoleComponent_input_41_Template_input_keydown_0_listener", "ctx_r54", "ctx_r56", "AddEditRoleComponent_input_41_Template_input_blur_0_listener", "ctx_r57", "ctx_r8", "AddEditRoleComponent_div_43_span_2_Template", "_r63", "AddEditRoleComponent_div_43_span_2_Template_span_click_0_listener", "i_r59", "ctx_r61", "AddEditRoleComponent_div_43_Template", "tag_r58", "ctx_r9", "AddEditRoleComponent_input_49_Template", "_r65", "AddEditRoleComponent_input_49_Template_input_keydown_0_listener", "ctx_r64", "ctx_r66", "AddEditRoleComponent_input_49_Template_input_blur_0_listener", "ctx_r67", "ctx_r10", "AddEditRoleComponent_div_51_span_2_Template", "_r73", "AddEditRoleComponent_div_51_span_2_Template_span_click_0_listener", "i_r69", "ctx_r71", "AddEditRoleComponent_div_51_Template", "tag_r68", "ctx_r11", "AddEditRoleComponent_div_52_Template", "_r75", "AddEditRoleComponent_div_52_Template_select_change_3_listener", "ctx_r74", "updateSalaryRange", "ctx_r12", "AddEditRoleComponent_div_53_Template", "ctx_r13", "salaryRange", "AddEditRoleComponent_input_58_Template", "_r77", "AddEditRoleComponent_input_58_Template_input_keydown_0_listener", "ctx_r76", "ctx_r78", "AddEditRoleComponent_input_58_Template_input_blur_0_listener", "ctx_r79", "ctx_r14", "AddEditRoleComponent_div_60_span_2_Template", "_r85", "AddEditRoleComponent_div_60_span_2_Template_span_click_0_listener", "i_r81", "ctx_r83", "AddEditRoleComponent_div_60_Template", "tag_r80", "ctx_r15", "AddEditRoleComponent_select_66_option_3_Template", "activity_r87", "QUO_title", "ɵɵtextInterpolate", "AddEditRoleComponent_select_66_Template", "_r89", "AddEditRoleComponent_select_66_Template_select_change_0_listener", "ctx_r88", "addExpectedActivity", "AddEditRoleComponent_select_66_Template_select_keydown_0_listener", "ctx_r90", "ctx_r16", "expectedActivitiesList", "AddEditRoleComponent_div_68_span_2_Template", "_r96", "AddEditRoleComponent_div_68_span_2_Template_span_click_0_listener", "i_r92", "ctx_r94", "AddEditRoleComponent_div_68_Template", "tag_r91", "ctx_r17", "AddEditRoleComponent_button_125_Template", "AddEditRoleComponent", "constructor", "formBuilder", "dataTransferService", "toastr", "router", "activeRoute", "httpClient", "ngxSpinnerService", "_a", "p", "showForm", "baseUrl", "state", "getCurrentNavigation", "extras", "console", "log", "role", "sectorId", "roleId", "group", "RO_title", "required", "RO_dp", "RO_References", "array", "RO_Skills_and_Knowledge", "RO_Place_of_Work", "RO_Key_Insights", "RO_<PERSON>", "RO_<PERSON><PERSON>_from", "RO_<PERSON><PERSON>_to", "RO_Daily_Tasks", "RO_Expected_Activities", "RO_Expected_to_on_the_weekends", "RO_Expected_to_work_beyond_nine_to_five", "RO_Expected_to_work_internationally", "RO_Expected_to_work_in_the_office", "RO_description", "RO_IndustryId", "RO_isAccept", "ngOnInit", "_b", "_c", "_d", "_e", "getAllExpctedActivities", "getAllRoleBySectorId", "disable", "navigate", "setRang<PERSON>", "patchArrayValues", "patchValue", "values", "controlName", "length", "formArray", "for<PERSON>ach", "value", "push", "control", "f", "controls", "event", "selectedFile", "target", "files", "imageName", "newFileName", "addTimestamp", "name", "File", "type", "formControl", "setValidators", "updateValueAndValidity", "fileType", "split", "fileExtension", "pop", "toLowerCase", "info", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "uploadLogoUrl", "Promise", "resolve", "reject", "uploadurl", "subscribe", "res", "error", "addRole", "RO_id", "fileUrl", "show", "updateRoleData", "to<PERSON>romise", "hide", "statusCode", "success", "status", "invalid", "insertRoleData", "addRoleCnlBtn", "addTag", "tag", "formArrayName", "trim", "insert", "removeAt", "key", "preventDefault", "inputElement", "trimmedTag", "shift<PERSON>ey", "removeLastTag", "activity", "isDuplicate", "some", "warn", "next", "RoleList", "data", "statuscode", "filteredData", "quiz_options", "range", "<PERSON><PERSON><PERSON><PERSON>", "ɵfac", "AddEditRoleComponent_Factory", "t", "ɵɵdirectiveInject", "FormBuilder", "DataTransferService", "ToastrService", "Router", "ActivatedRoute", "HttpClient", "NgxSpinnerService", "ɵcmp", "ɵɵdefineComponent", "selectors", "decls", "vars", "consts", "template", "AddEditRoleComponent_Template", "AddEditRoleComponent_Template_form_ngSubmit_8_listener", "AddEditRoleComponent_Template_button_click_126_listener", "tmp_6_0", "tmp_8_0", "tmp_10_0", "tmp_12_0", "tmp_14_0", "tmp_18_0", "tmp_20_0", "directives", "SidebarComponent", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "DefaultValueAccessor", "NgControlStatus", "FormControlName", "RequiredValidator", "NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SelectControlValueAccessor", "NgSelectOption", "ɵNgSelectMultipleOption", "Ng<PERSON><PERSON>", "styles"], "mappings": ";AAAA,SAASA,WAAT,EAAsBC,UAAtB,QAAwC,gBAAxC;AACA,SAASC,aAAT,QAA8B,mDAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+CAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,YAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,sBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+BAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;;AACA,SAASC,0CAAT,CAAoDC,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvEV,IAAAA,EAAE,CAACY,SAAH,CAAa,CAAb,EAAgB,KAAhB,EAAuB,EAAvB;AACH;;AAAC,MAAIF,EAAE,GAAG,CAAT,EAAY;AACV,UAAMG,OAAO,GAAGb,EAAE,CAACc,aAAH,CAAiB,CAAjB,CAAhB;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,KAAd,EAAqBF,OAAO,CAACG,QAA7B,EAAuChB,EAAE,CAACiB,aAA1C;AACH;AAAE;;AACH,SAASC,0CAAT,CAAoDR,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,uEAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8CZ,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMa,IAAI,GAAGvB,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,YAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAnB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,QAAd,EAAwB,SAASC,4DAAT,CAAsEC,MAAtE,EAA8E;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiBL,IAAjB;AAAwB,YAAMM,OAAO,GAAG7B,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOe,OAAO,CAACC,cAAR,CAAuBH,MAAvB,CAAP;AAAwC,KAA5M;AACA3B,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiBtB,0CAAjB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,KAAnE,EAA0E,EAA1E;AACAT,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiBb,0CAAjB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,KAAnE,EAA0E,EAA1E;AACAlB,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMsB,MAAM,GAAGhC,EAAE,CAACc,aAAH,EAAf;AACA,QAAImB,OAAJ;AACAjC,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BiB,MAAM,CAACG,UAAjC;AACAnC,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsBiB,MAAM,CAAChB,QAA7B;AACAhB,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACkB,OAAO,GAAGD,MAAM,CAACI,WAAP,CAAmBC,GAAnB,CAAuB,OAAvB,CAAX,KAA+C,IAA/C,GAAsD,IAAtD,GAA6DJ,OAAO,CAACK,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCL,OAAO,CAACK,MAAR,CAAeC,iBAAlI;AACH;AAAE;;AACH,SAASC,2CAAT,CAAqD9B,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,GAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,SAASoB,oCAAT,CAA8C/B,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,YAAb;AACApB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiBS,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,MAApE,EAA4E,EAA5E;AACAxC,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACY,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAZ,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMgC,MAAM,GAAG1C,EAAE,CAACc,aAAH,EAAf;AACAd,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAAC2B,MAAM,CAACP,UAA9B;AACAnC,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0B2B,MAAM,CAACP,UAAjC;AACH;AAAE;;AACH,SAASQ,sCAAT,CAAgDjC,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnE,UAAMkC,IAAI,GAAG5C,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,SAAd,EAAyB,SAASoB,+DAAT,CAAyElB,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiBgB,IAAjB;AAAwB,YAAME,OAAO,GAAG9C,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOgC,OAAO,CAACC,UAAR,CAAmBpB,MAAnB,EAA2B,eAA3B,CAAP;AAAqD,KAA7N,EAA+N,SAA/N,EAA0O,SAASkB,+DAAT,CAAyElB,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiBgB,IAAjB;AAAwB,YAAMI,OAAO,GAAGhD,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOkC,OAAO,CAACC,cAAR,CAAuBtB,MAAvB,EAA+B,eAA/B,CAAP;AAAyD,KAAlb,EAAob,MAApb,EAA4b,SAASuB,4DAAT,CAAsEvB,MAAtE,EAA8E;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiBgB,IAAjB;AAAwB,YAAMO,OAAO,GAAGnD,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOqC,OAAO,CAACC,WAAR,CAAoBzB,MAApB,EAA4B,eAA5B,CAAP;AAAsD,KAA9nB;AACA3B,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM2C,MAAM,GAAGrD,EAAE,CAACc,aAAH,EAAf;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BsC,MAAM,CAAClB,UAAjC;AACH;AAAE;;AACH,SAASmB,2CAAT,CAAqD5C,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxE,UAAM6C,IAAI,GAAGvD,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,OAAd,EAAuB,SAAS+B,iEAAT,GAA6E;AAAExD,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2B,IAAjB;AAAwB,YAAME,KAAK,GAAGzD,EAAE,CAACc,aAAH,GAAmB4C,KAAjC;AAAwC,YAAMC,OAAO,GAAG3D,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO6C,OAAO,CAACC,iBAAR,CAA0BH,KAA1B,EAAiC,eAAjC,CAAP;AAA2D,KAArQ;AACAzD,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,GAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,MAAMwC,GAAG,GAAG,UAAUC,EAAV,EAAc;AAAE,SAAO;AAAE,iBAAaA;AAAf,GAAP;AAA6B,CAAzD;;AACA,SAASC,oCAAT,CAA8CrD,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV;AACApB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiBuB,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,MAApE,EAA4E,EAA5E;AACAtD,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMsD,OAAO,GAAGrD,GAAG,CAACsD,SAApB;AACA,UAAMC,MAAM,GAAGlE,EAAE,CAACc,aAAH,EAAf;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyBf,EAAE,CAACmE,eAAH,CAAmB,CAAnB,EAAsBN,GAAtB,EAA2BK,MAAM,CAACE,KAAP,KAAiB,MAA5C,CAAzB;AACApE,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACqE,kBAAH,CAAsB,GAAtB,EAA2BL,OAA3B,EAAoC,GAApC;AACAhE,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACmD,MAAM,CAAC/B,UAA9B;AACH;AAAE;;AACH,SAASmC,sCAAT,CAAgD5D,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnE,UAAM6D,IAAI,GAAGvE,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,SAAd,EAAyB,SAAS+C,+DAAT,CAAyE7C,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2C,IAAjB;AAAwB,YAAME,OAAO,GAAGzE,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO2D,OAAO,CAAC1B,UAAR,CAAmBpB,MAAnB,EAA2B,yBAA3B,CAAP;AAA+D,KAAvO,EAAyO,SAAzO,EAAoP,SAAS6C,+DAAT,CAAyE7C,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2C,IAAjB;AAAwB,YAAMG,OAAO,GAAG1E,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO4D,OAAO,CAACzB,cAAR,CAAuBtB,MAAvB,EAA+B,yBAA/B,CAAP;AAAmE,KAAtc,EAAwc,MAAxc,EAAgd,SAASgD,4DAAT,CAAsEhD,MAAtE,EAA8E;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2C,IAAjB;AAAwB,YAAMK,OAAO,GAAG5E,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO8D,OAAO,CAACxB,WAAR,CAAoBzB,MAApB,EAA4B,yBAA5B,CAAP;AAAgE,KAA5pB;AACA3B,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmE,MAAM,GAAG7E,EAAE,CAACc,aAAH,EAAf;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0B8D,MAAM,CAAC1C,UAAjC;AACH;AAAE;;AACH,SAAS2C,2CAAT,CAAqDpE,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxE,UAAMqE,IAAI,GAAG/E,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,OAAd,EAAuB,SAASuD,iEAAT,GAA6E;AAAEhF,MAAAA,EAAE,CAAC4B,aAAH,CAAiBmD,IAAjB;AAAwB,YAAME,KAAK,GAAGjF,EAAE,CAACc,aAAH,GAAmB4C,KAAjC;AAAwC,YAAMwB,OAAO,GAAGlF,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOoE,OAAO,CAACtB,iBAAR,CAA0BqB,KAA1B,EAAiC,yBAAjC,CAAP;AAAqE,KAA/Q;AACAjF,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,GAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,SAAS8D,oCAAT,CAA8CzE,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV;AACApB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiB+C,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,MAApE,EAA4E,EAA5E;AACA9E,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM0E,OAAO,GAAGzE,GAAG,CAACsD,SAApB;AACA,UAAMoB,MAAM,GAAGrF,EAAE,CAACc,aAAH,EAAf;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyBf,EAAE,CAACmE,eAAH,CAAmB,CAAnB,EAAsBN,GAAtB,EAA2BwB,MAAM,CAACjB,KAAP,KAAiB,MAA5C,CAAzB;AACApE,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACqE,kBAAH,CAAsB,GAAtB,EAA2Be,OAA3B,EAAoC,GAApC;AACApF,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACsE,MAAM,CAAClD,UAA9B;AACH;AAAE;;AACH,SAASmD,sCAAT,CAAgD5E,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnE,UAAM6E,IAAI,GAAGvF,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,SAAd,EAAyB,SAAS+D,+DAAT,CAAyE7D,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2D,IAAjB;AAAwB,YAAME,OAAO,GAAGzF,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO2E,OAAO,CAAC1C,UAAR,CAAmBpB,MAAnB,EAA2B,kBAA3B,CAAP;AAAwD,KAAhO,EAAkO,SAAlO,EAA6O,SAAS6D,+DAAT,CAAyE7D,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2D,IAAjB;AAAwB,YAAMG,OAAO,GAAG1F,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO4E,OAAO,CAACzC,cAAR,CAAuBtB,MAAvB,EAA+B,kBAA/B,CAAP;AAA4D,KAAxb,EAA0b,MAA1b,EAAkc,SAASgE,4DAAT,CAAsEhE,MAAtE,EAA8E;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2D,IAAjB;AAAwB,YAAMK,OAAO,GAAG5F,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO8E,OAAO,CAACxC,WAAR,CAAoBzB,MAApB,EAA4B,kBAA5B,CAAP;AAAyD,KAAvoB;AACA3B,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmF,MAAM,GAAG7F,EAAE,CAACc,aAAH,EAAf;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0B8E,MAAM,CAAC1D,UAAjC;AACH;AAAE;;AACH,SAAS2D,2CAAT,CAAqDpF,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxE,UAAMqF,IAAI,GAAG/F,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,OAAd,EAAuB,SAASuE,iEAAT,GAA6E;AAAEhG,MAAAA,EAAE,CAAC4B,aAAH,CAAiBmE,IAAjB;AAAwB,YAAME,KAAK,GAAGjG,EAAE,CAACc,aAAH,GAAmB4C,KAAjC;AAAwC,YAAMwC,OAAO,GAAGlG,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOoF,OAAO,CAACtC,iBAAR,CAA0BqC,KAA1B,EAAiC,kBAAjC,CAAP;AAA8D,KAAxQ;AACAjG,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,GAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,SAAS8E,oCAAT,CAA8CzF,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV;AACApB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiB+D,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,MAApE,EAA4E,EAA5E;AACA9F,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM0F,OAAO,GAAGzF,GAAG,CAACsD,SAApB;AACA,UAAMoC,MAAM,GAAGrG,EAAE,CAACc,aAAH,EAAf;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyBf,EAAE,CAACmE,eAAH,CAAmB,CAAnB,EAAsBN,GAAtB,EAA2BwC,MAAM,CAACjC,KAAP,KAAiB,MAA5C,CAAzB;AACApE,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACqE,kBAAH,CAAsB,GAAtB,EAA2B+B,OAA3B,EAAoC,GAApC;AACApG,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACsF,MAAM,CAAClE,UAA9B;AACH;AAAE;;AACH,SAASmE,sCAAT,CAAgD5F,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnE,UAAM6F,IAAI,GAAGvG,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,SAAd,EAAyB,SAAS+E,+DAAT,CAAyE7E,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2E,IAAjB;AAAwB,YAAME,OAAO,GAAGzG,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO2F,OAAO,CAAC1D,UAAR,CAAmBpB,MAAnB,EAA2B,iBAA3B,CAAP;AAAuD,KAA/N,EAAiO,SAAjO,EAA4O,SAAS6E,+DAAT,CAAyE7E,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2E,IAAjB;AAAwB,YAAMG,OAAO,GAAG1G,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO4F,OAAO,CAACzD,cAAR,CAAuBtB,MAAvB,EAA+B,iBAA/B,CAAP;AAA2D,KAAtb,EAAwb,MAAxb,EAAgc,SAASgF,4DAAT,CAAsEhF,MAAtE,EAA8E;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2E,IAAjB;AAAwB,YAAMK,OAAO,GAAG5G,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO8F,OAAO,CAACxD,WAAR,CAAoBzB,MAApB,EAA4B,iBAA5B,CAAP;AAAwD,KAApoB;AACA3B,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmG,MAAM,GAAG7G,EAAE,CAACc,aAAH,EAAf;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0B8F,MAAM,CAAC1E,UAAjC;AACH;AAAE;;AACH,SAAS2E,2CAAT,CAAqDpG,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxE,UAAMqG,IAAI,GAAG/G,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,OAAd,EAAuB,SAASuF,iEAAT,GAA6E;AAAEhH,MAAAA,EAAE,CAAC4B,aAAH,CAAiBmF,IAAjB;AAAwB,YAAME,KAAK,GAAGjH,EAAE,CAACc,aAAH,GAAmB4C,KAAjC;AAAwC,YAAMwD,OAAO,GAAGlH,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOoG,OAAO,CAACtD,iBAAR,CAA0BqD,KAA1B,EAAiC,iBAAjC,CAAP;AAA6D,KAAvQ;AACAjH,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,GAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,SAAS8F,oCAAT,CAA8CzG,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV;AACApB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiB+E,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,MAApE,EAA4E,EAA5E;AACA9G,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM0G,OAAO,GAAGzG,GAAG,CAACsD,SAApB;AACA,UAAMoD,MAAM,GAAGrH,EAAE,CAACc,aAAH,EAAf;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyBf,EAAE,CAACmE,eAAH,CAAmB,CAAnB,EAAsBN,GAAtB,EAA2BwD,MAAM,CAACjD,KAAP,KAAiB,MAA5C,CAAzB;AACApE,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACqE,kBAAH,CAAsB,GAAtB,EAA2B+C,OAA3B,EAAoC,GAApC;AACApH,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACsG,MAAM,CAAClF,UAA9B;AACH;AAAE;;AACH,SAASmF,sCAAT,CAAgD5G,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnE,UAAM6G,IAAI,GAAGvH,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,SAAd,EAAyB,SAAS+F,+DAAT,CAAyE7F,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2F,IAAjB;AAAwB,YAAME,OAAO,GAAGzH,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO2G,OAAO,CAAC1E,UAAR,CAAmBpB,MAAnB,EAA2B,WAA3B,CAAP;AAAiD,KAAzN,EAA2N,SAA3N,EAAsO,SAAS6F,+DAAT,CAAyE7F,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2F,IAAjB;AAAwB,YAAMG,OAAO,GAAG1H,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO4G,OAAO,CAACzE,cAAR,CAAuBtB,MAAvB,EAA+B,WAA/B,CAAP;AAAqD,KAA1a,EAA4a,MAA5a,EAAob,SAASgG,4DAAT,CAAsEhG,MAAtE,EAA8E;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2F,IAAjB;AAAwB,YAAMK,OAAO,GAAG5H,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO8G,OAAO,CAACxE,WAAR,CAAoBzB,MAApB,EAA4B,WAA5B,CAAP;AAAkD,KAAlnB;AACA3B,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmH,OAAO,GAAG7H,EAAE,CAACc,aAAH,EAAhB;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0B8G,OAAO,CAAC1F,UAAlC;AACH;AAAE;;AACH,SAAS2F,2CAAT,CAAqDpH,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxE,UAAMqH,IAAI,GAAG/H,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,OAAd,EAAuB,SAASuG,iEAAT,GAA6E;AAAEhI,MAAAA,EAAE,CAAC4B,aAAH,CAAiBmG,IAAjB;AAAwB,YAAME,KAAK,GAAGjI,EAAE,CAACc,aAAH,GAAmB4C,KAAjC;AAAwC,YAAMwE,OAAO,GAAGlI,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOoH,OAAO,CAACtE,iBAAR,CAA0BqE,KAA1B,EAAiC,WAAjC,CAAP;AAAuD,KAAjQ;AACAjI,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,GAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,SAAS8G,oCAAT,CAA8CzH,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV;AACApB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiB+F,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,MAApE,EAA4E,EAA5E;AACA9H,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM0H,OAAO,GAAGzH,GAAG,CAACsD,SAApB;AACA,UAAMoE,OAAO,GAAGrI,EAAE,CAACc,aAAH,EAAhB;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyBf,EAAE,CAACmE,eAAH,CAAmB,CAAnB,EAAsBN,GAAtB,EAA2BwE,OAAO,CAACjE,KAAR,KAAkB,MAA7C,CAAzB;AACApE,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACqE,kBAAH,CAAsB,GAAtB,EAA2B+D,OAA3B,EAAoC,GAApC;AACApI,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACsH,OAAO,CAAClG,UAA/B;AACH;AAAE;;AACH,SAASmG,oCAAT,CAA8C5H,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAM6H,IAAI,GAAGvI,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,0BAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,QAAd,EAAwB,SAAS+G,6DAAT,CAAuE7G,MAAvE,EAA+E;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiB2G,IAAjB;AAAwB,YAAME,OAAO,GAAGzI,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO2H,OAAO,CAACC,iBAAR,CAA0B/G,MAA1B,CAAP;AAA2C,KAAhN;AACA3B,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,uBAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,eAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,WAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,WAAd;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,eAAd;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMiI,OAAO,GAAG3I,EAAE,CAACc,aAAH,EAAhB;AACAd,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0B4H,OAAO,CAACxG,UAAlC;AACH;AAAE;;AACH,SAASyG,oCAAT,CAA8ClI,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,cAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAACY,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAZ,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmI,OAAO,GAAG7I,EAAE,CAACc,aAAH,EAAhB;AACAd,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,OAAd,EAAuB8H,OAAO,CAACC,WAA/B,EAA4C,UAA5C,EAAwDD,OAAO,CAAC1G,UAAhE;AACH;AAAE;;AACH,SAAS4G,sCAAT,CAAgDrI,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnE,UAAMsI,IAAI,GAAGhJ,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,SAAd,EAAyB,SAASwH,+DAAT,CAAyEtH,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiBoH,IAAjB;AAAwB,YAAME,OAAO,GAAGlJ,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOoI,OAAO,CAACnG,UAAR,CAAmBpB,MAAnB,EAA2B,gBAA3B,CAAP;AAAsD,KAA9N,EAAgO,SAAhO,EAA2O,SAASsH,+DAAT,CAAyEtH,MAAzE,EAAiF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiBoH,IAAjB;AAAwB,YAAMG,OAAO,GAAGnJ,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOqI,OAAO,CAAClG,cAAR,CAAuBtB,MAAvB,EAA+B,gBAA/B,CAAP;AAA0D,KAApb,EAAsb,MAAtb,EAA8b,SAASyH,4DAAT,CAAsEzH,MAAtE,EAA8E;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiBoH,IAAjB;AAAwB,YAAMK,OAAO,GAAGrJ,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOuI,OAAO,CAACjG,WAAR,CAAoBzB,MAApB,EAA4B,gBAA5B,CAAP;AAAuD,KAAjoB;AACA3B,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM4I,OAAO,GAAGtJ,EAAE,CAACc,aAAH,EAAhB;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BuI,OAAO,CAACnH,UAAlC;AACH;AAAE;;AACH,SAASoH,2CAAT,CAAqD7I,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxE,UAAM8I,IAAI,GAAGxJ,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,OAAd,EAAuB,SAASgI,iEAAT,GAA6E;AAAEzJ,MAAAA,EAAE,CAAC4B,aAAH,CAAiB4H,IAAjB;AAAwB,YAAME,KAAK,GAAG1J,EAAE,CAACc,aAAH,GAAmB4C,KAAjC;AAAwC,YAAMiG,OAAO,GAAG3J,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO6I,OAAO,CAAC/F,iBAAR,CAA0B8F,KAA1B,EAAiC,gBAAjC,CAAP;AAA4D,KAAtQ;AACA1J,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,GAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,SAASuI,oCAAT,CAA8ClJ,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV;AACApB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiBwH,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,MAApE,EAA4E,EAA5E;AACAvJ,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmJ,OAAO,GAAGlJ,GAAG,CAACsD,SAApB;AACA,UAAM6F,OAAO,GAAG9J,EAAE,CAACc,aAAH,EAAhB;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyBf,EAAE,CAACmE,eAAH,CAAmB,CAAnB,EAAsBN,GAAtB,EAA2BiG,OAAO,CAAC1F,KAAR,KAAkB,MAA7C,CAAzB;AACApE,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACqE,kBAAH,CAAsB,GAAtB,EAA2BwF,OAA3B,EAAoC,GAApC;AACA7J,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAAC+I,OAAO,CAAC3H,UAA/B;AACH;AAAE;;AACH,SAAS4H,gDAAT,CAA0DrJ,EAA1D,EAA8DC,GAA9D,EAAmE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC7EV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMsJ,YAAY,GAAGrJ,GAAG,CAACsD,SAAzB;AACAjE,IAAAA,EAAE,CAACe,UAAH,CAAc,OAAd,EAAuBiJ,YAAY,CAACC,SAApC;AACAjK,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACkK,iBAAH,CAAqBF,YAAY,CAACC,SAAlC;AACH;AAAE;;AACH,SAASE,uCAAT,CAAiDzJ,EAAjD,EAAqDC,GAArD,EAA0D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACpE,UAAM0J,IAAI,GAAGpK,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,QAAd,EAAwB,SAAS4I,gEAAT,CAA0E1I,MAA1E,EAAkF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiBwI,IAAjB;AAAwB,YAAME,OAAO,GAAGtK,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOwJ,OAAO,CAACC,mBAAR,CAA4B5I,MAA5B,CAAP;AAA6C,KAArN,EAAuN,SAAvN,EAAkO,SAAS6I,iEAAT,CAA2E7I,MAA3E,EAAmF;AAAE3B,MAAAA,EAAE,CAAC4B,aAAH,CAAiBwI,IAAjB;AAAwB,YAAMK,OAAO,GAAGzK,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAO2J,OAAO,CAACxH,cAAR,CAAuBtB,MAAvB,EAA+B,wBAA/B,CAAP;AAAkE,KAArb;AACA3B,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,WAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACArB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiBgI,gDAAjB,EAAmE,CAAnE,EAAsE,CAAtE,EAAyE,QAAzE,EAAmF,EAAnF;AACA/J,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMgK,OAAO,GAAG1K,EAAE,CAACc,aAAH,EAAhB;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0B2J,OAAO,CAACvI,UAAlC;AACAnC,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyB2J,OAAO,CAACC,sBAAjC;AACH;AAAE;;AACH,SAASC,2CAAT,CAAqDlK,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxE,UAAMmK,IAAI,GAAG7K,EAAE,CAACwB,gBAAH,EAAb;;AACAxB,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAnB,IAAAA,EAAE,CAACyB,UAAH,CAAc,OAAd,EAAuB,SAASqJ,iEAAT,GAA6E;AAAE9K,MAAAA,EAAE,CAAC4B,aAAH,CAAiBiJ,IAAjB;AAAwB,YAAME,KAAK,GAAG/K,EAAE,CAACc,aAAH,GAAmB4C,KAAjC;AAAwC,YAAMsH,OAAO,GAAGhL,EAAE,CAACc,aAAH,EAAhB;AAAoC,aAAOkK,OAAO,CAACpH,iBAAR,CAA0BmH,KAA1B,EAAiC,wBAAjC,CAAP;AAAoE,KAA9Q;AACA/K,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,GAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,SAAS4J,oCAAT,CAA8CvK,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV;AACApB,IAAAA,EAAE,CAAC+B,UAAH,CAAc,CAAd,EAAiB6I,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,MAApE,EAA4E,EAA5E;AACA5K,IAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMwK,OAAO,GAAGvK,GAAG,CAACsD,SAApB;AACA,UAAMkH,OAAO,GAAGnL,EAAE,CAACc,aAAH,EAAhB;AACAd,IAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyBf,EAAE,CAACmE,eAAH,CAAmB,CAAnB,EAAsBN,GAAtB,EAA2BsH,OAAO,CAAC/G,KAAR,KAAkB,MAA7C,CAAzB;AACApE,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACqE,kBAAH,CAAsB,GAAtB,EAA2B6G,OAA3B,EAAoC,GAApC;AACAlL,IAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,IAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACoK,OAAO,CAAChJ,UAA/B;AACH;AAAE;;AACH,SAASiJ,wCAAT,CAAkD1K,EAAlD,EAAsDC,GAAtD,EAA2D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACrEV,IAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAnB,IAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV,EAAa,MAAb;AACApB,IAAAA,EAAE,CAACqB,YAAH;AACH;AAAE;;AACH,OAAO,MAAMgK,oBAAN,CAA2B;AAC9BC,EAAAA,WAAW,CAACC,WAAD,EAAcC,mBAAd,EAAmCC,MAAnC,EAA2CC,MAA3C,EAAmDC,WAAnD,EAAgEC,UAAhE,EAA4EC,iBAA5E,EAA+F;AACtG,QAAIC,EAAJ;;AACA,SAAKP,WAAL,GAAmBA,WAAnB;AACA,SAAKC,mBAAL,GAA2BA,mBAA3B;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKE,CAAL,GAAS,CAAT;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAK5H,KAAL,GAAa,SAAb;AACA,SAAKjC,UAAL,GAAkB,KAAlB;AACA,SAAK8J,OAAL,GAAe,4CAAf;AACA,UAAMC,KAAK,GAAG,CAACJ,EAAE,GAAG,KAAKJ,MAAL,CAAYS,oBAAZ,EAAN,MAA8C,IAA9C,IAAsDL,EAAE,KAAK,KAAK,CAAlE,GAAsE,KAAK,CAA3E,GAA+EA,EAAE,CAACM,MAAH,CAAUF,KAAvG;;AACA,QAAIA,KAAJ,EAAW;AACPG,MAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkCJ,KAAK,CAACK,IAAxC;AACC,WAAKC,QAAL,GAAgBN,KAAK,CAACM,QAAvB;AACC,WAAKpI,KAAL,GAAa8H,KAAK,CAAC9H,KAApB;AACC,WAAKjC,UAAL,GAAkB+J,KAAK,CAAC/J,UAAzB;AACC,WAAKoK,IAAL,GAAYL,KAAK,CAACK,IAAnB;AACC,WAAKE,MAAL,GAAcP,KAAK,CAACO,MAArB;AACH;;AACD,SAAKrK,WAAL,GAAmB,KAAKmJ,WAAL,CAAiBmB,KAAjB,CAAuB;AACtCC,MAAAA,QAAQ,EAAE,CAAC,EAAD,EAAK7M,UAAU,CAAC8M,QAAhB,CAD4B;AAEtCC,MAAAA,KAAK,EAAE,CAAC,IAAD,EAAO/M,UAAU,CAAC8M,QAAlB,CAF+B;AAGtCE,MAAAA,aAAa,EAAE,KAAKvB,WAAL,CAAiBwB,KAAjB,CAAuB,EAAvB,EAA2BjN,UAAU,CAAC8M,QAAtC,CAHuB;AAItCI,MAAAA,uBAAuB,EAAE,KAAKzB,WAAL,CAAiBwB,KAAjB,CAAuB,EAAvB,EAA2BjN,UAAU,CAAC8M,QAAtC,CAJa;AAKtCK,MAAAA,gBAAgB,EAAE,KAAK1B,WAAL,CAAiBwB,KAAjB,CAAuB,EAAvB,EAA2BjN,UAAU,CAAC8M,QAAtC,CALoB;AAMtCM,MAAAA,eAAe,EAAE,KAAK3B,WAAL,CAAiBwB,KAAjB,CAAuB,EAAvB,EAA2BjN,UAAU,CAAC8M,QAAtC,CANqB;AAOtCO,MAAAA,SAAS,EAAE,KAAK5B,WAAL,CAAiBwB,KAAjB,CAAuB,EAAvB,EAA2BjN,UAAU,CAAC8M,QAAtC,CAP2B;AAQtCQ,MAAAA,cAAc,EAAE,CAAC,EAAD,EAAKtN,UAAU,CAAC8M,QAAhB,CARsB;AAStCS,MAAAA,YAAY,EAAE,CAAC,EAAD,EAAKvN,UAAU,CAAC8M,QAAhB,CATwB;AAUtCU,MAAAA,cAAc,EAAE,KAAK/B,WAAL,CAAiBwB,KAAjB,CAAuB,EAAvB,EAA2BjN,UAAU,CAAC8M,QAAtC,CAVsB;AAWtCW,MAAAA,sBAAsB,EAAE,KAAKhC,WAAL,CAAiBwB,KAAjB,CAAuB,EAAvB,EAA2BjN,UAAU,CAAC8M,QAAtC,CAXc;AAYtCY,MAAAA,8BAA8B,EAAE,CAAC,EAAD,EAAK1N,UAAU,CAAC8M,QAAhB,CAZM;AAatCa,MAAAA,uCAAuC,EAAE,CAAC,EAAD,EAAK3N,UAAU,CAAC8M,QAAhB,CAbH;AActCc,MAAAA,mCAAmC,EAAE,CAAC,EAAD,EAAK5N,UAAU,CAAC8M,QAAhB,CAdC;AAetCe,MAAAA,iCAAiC,EAAE,CAAC,EAAD,EAAK7N,UAAU,CAAC8M,QAAhB,CAfG;AAgBtCgB,MAAAA,cAAc,EAAE,CAAC,EAAD,EAAK9N,UAAU,CAAC8M,QAAhB,CAhBsB;AAiBtCiB,MAAAA,aAAa,EAAE,CAAC,EAAD,CAjBuB;AAkBtCC,MAAAA,WAAW,EAAE,CAAC,KAAD;AAlByB,KAAvB,CAAnB;AAoBH;;AACDC,EAAAA,QAAQ,GAAG;AACP,QAAIjC,EAAJ,EAAQkC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB;;AACA,SAAKC,uBAAL;AACA,SAAKC,oBAAL,CAA0B,KAAK7B,QAA/B;AACA,SAAKA,QAAL,GAAgB,KAAKA,QAArB;;AACA,QAAI,KAAKpI,KAAL,IAAc,MAAlB,EAA0B;AACtB,OAAC0H,EAAE,GAAG,KAAK1J,WAAL,CAAiBC,GAAjB,CAAqB,gCAArB,CAAN,MAAkE,IAAlE,IAA0EyJ,EAAE,KAAK,KAAK,CAAtF,GAA0F,KAAK,CAA/F,GAAmGA,EAAE,CAACwC,OAAH,EAAnG;AACA,OAACN,EAAE,GAAG,KAAK5L,WAAL,CAAiBC,GAAjB,CAAqB,yCAArB,CAAN,MAA2E,IAA3E,IAAmF2L,EAAE,KAAK,KAAK,CAA/F,GAAmG,KAAK,CAAxG,GAA4GA,EAAE,CAACM,OAAH,EAA5G;AACA,OAACL,EAAE,GAAG,KAAK7L,WAAL,CAAiBC,GAAjB,CAAqB,qCAArB,CAAN,MAAuE,IAAvE,IAA+E4L,EAAE,KAAK,KAAK,CAA3F,GAA+F,KAAK,CAApG,GAAwGA,EAAE,CAACK,OAAH,EAAxG;AACA,OAACJ,EAAE,GAAG,KAAK9L,WAAL,CAAiBC,GAAjB,CAAqB,mCAArB,CAAN,MAAqE,IAArE,IAA6E6L,EAAE,KAAK,KAAK,CAAzF,GAA6F,KAAK,CAAlG,GAAsGA,EAAE,CAACI,OAAH,EAAtG;AACA,OAACH,EAAE,GAAG,KAAK/L,WAAL,CAAiBC,GAAjB,CAAqB,aAArB,CAAN,MAA+C,IAA/C,IAAuD8L,EAAE,KAAK,KAAK,CAAnE,GAAuE,KAAK,CAA5E,GAAgFA,EAAE,CAACG,OAAH,EAAhF;AACH;;AACD,QAAI,CAAC,KAAK9B,QAAV,EAAoB;AAChB,WAAKd,MAAL,CAAY6C,QAAZ,CAAqB,CAAE,uBAAF,CAArB;AACH;;AACD;;AACA,QAAI,KAAKhC,IAAT,EAAe;AACXF,MAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2B,KAAKC,IAAhC;AACA,WAAKiC,QAAL;AACA,WAAKC,gBAAL,CAAsB,KAAKlC,IAAL,CAAUO,aAAhC,EAA+C,eAA/C;AACA,WAAK2B,gBAAL,CAAsB,KAAKlC,IAAL,CAAUgB,sBAAhC,EAAwD,wBAAxD;AACA,WAAKkB,gBAAL,CAAsB,KAAKlC,IAAL,CAAUY,SAAhC,EAA2C,WAA3C;AACA,WAAKsB,gBAAL,CAAsB,KAAKlC,IAAL,CAAUe,cAAhC,EAAgD,gBAAhD;AACA,WAAKmB,gBAAL,CAAsB,KAAKlC,IAAL,CAAUS,uBAAhC,EAAyD,yBAAzD;AACA,WAAKyB,gBAAL,CAAsB,KAAKlC,IAAL,CAAUU,gBAAhC,EAAkD,kBAAlD;AACA,WAAKwB,gBAAL,CAAsB,KAAKlC,IAAL,CAAUW,eAAhC,EAAiD,iBAAjD;AACA,WAAK9K,WAAL,CAAiBsM,UAAjB,CAA4B;AACxBZ,QAAAA,WAAW,EAAE,KAAKvB,IAAL,CAAUuB,WAAV,IAAyB,MAAzB,GAAkC,IAAlC,GAAyC;AAD9B,OAA5B;AAGA,WAAK1L,WAAL,CAAiBsM,UAAjB,CAA4B,KAAKnC,IAAjC;AACH;AACJ;;AACDiC,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKjC,IAAL,CAAUa,cAAV,IAA4B,GAA5B,IAAmC,KAAKb,IAAL,CAAUc,YAAV,IAA0B,IAAjE,EAAuE;AACnE,WAAKvE,WAAL,GAAmB,MAAnB;AACH,KAFD,MAGK,IAAI,KAAKyD,IAAL,CAAUa,cAAV,IAA4B,IAA5B,IAAoC,KAAKb,IAAL,CAAUc,YAAV,IAA0B,IAAlE,EAAwE;AACzE,WAAKvE,WAAL,GAAmB,QAAnB;AACH,KAFI,MAGA,IAAI,KAAKyD,IAAL,CAAUa,cAAV,IAA4B,IAA5B,IAAoC,KAAKb,IAAL,CAAUc,YAAV,IAA0B,IAAlE,EAAwE;AACzE,WAAKvE,WAAL,GAAmB,QAAnB;AACH,KAFI,MAGA,IAAI,KAAKyD,IAAL,CAAUa,cAAV,IAA4B,IAA5B,IAAoC,KAAKb,IAAL,CAAUc,YAAV,IAA0B,KAAlE,EAAyE;AAC1E,WAAKvE,WAAL,GAAmB,MAAnB;AACH;AACJ;;AACD2F,EAAAA,gBAAgB,CAACE,MAAD,EAASC,WAAT,EAAsB;AAClC,QAAID,MAAM,IAAIA,MAAM,CAACE,MAAP,GAAgB,CAA9B,EAAiC;AAC7B,YAAMC,SAAS,GAAG,KAAK1M,WAAL,CAAiBC,GAAjB,CAAqBuM,WAArB,CAAlB;AACAD,MAAAA,MAAM,CAACI,OAAP,CAAeC,KAAK,IAAI;AACpBF,QAAAA,SAAS,CAACG,IAAV,CAAe,KAAK1D,WAAL,CAAiB2D,OAAjB,CAAyBF,KAAzB,CAAf;AACH,OAFD;AAGH;AACJ;;AACI,MAADG,CAAC,GAAG;AACJ,WAAO,KAAK/M,WAAL,CAAiBgN,QAAxB;AACH;;AACDtN,EAAAA,cAAc,CAACuN,KAAD,EAAQ;AAClB,QAAIvD,EAAJ;;AACA,QAAIwD,YAAY,GAAGD,KAAK,CAACE,MAAN,CAAaC,KAAb,CAAmB,CAAnB,CAAnB;;AACA,QAAIH,KAAK,CAACE,MAAN,CAAaC,KAAb,CAAmBX,MAAnB,KAA8B,CAAlC,EAAqC;AACjC;AACA,WAAKY,SAAL,GAAiB,IAAjB;AACA,WAAKzO,QAAL,GAAgB,IAAhB;AACA;AACH;;AACD,UAAM0O,WAAW,GAAG3P,aAAa,CAAC4P,YAAd,CAA2BL,YAAY,CAACM,IAAxC,CAApB;AACA,SAAKH,SAAL,GAAiB,IAAII,IAAJ,CAAS,CAACP,YAAD,CAAT,EAAyBI,WAAzB,EAAsC;AAAEI,MAAAA,IAAI,EAAER,YAAY,CAACQ;AAArB,KAAtC,CAAjB;;AACA,QAAI,KAAKL,SAAT,EAAoB;AAChB,YAAMM,WAAW,GAAG,KAAK3N,WAAL,CAAiBC,GAAjB,CAAqB,OAArB,CAApB;AACA0N,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACC,aAAZ,CAA0BjQ,aAAa,CAACwC,iBAAd,CAAgC,IAAhC,EAAsC,KAAKkN,SAA3C,CAA1B,CAA1D;AACAM,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACE,sBAAZ,EAA1D;AACH;;AACD,UAAMC,QAAQ,GAAG,KAAKT,SAAL,CAAeK,IAAf,CAAoBK,KAApB,CAA0B,GAA1B,EAA+B,CAA/B,CAAjB;AACA,UAAMC,aAAa,GAAG,CAACtE,EAAE,GAAG,KAAK2D,SAAL,CAAeG,IAAf,CAAoBO,KAApB,CAA0B,GAA1B,EAA+BE,GAA/B,EAAN,MAAgD,IAAhD,IAAwDvE,EAAE,KAAK,KAAK,CAApE,GAAwE,KAAK,CAA7E,GAAiFA,EAAE,CAACwE,WAAH,EAAvG;;AACA,QAAIJ,QAAQ,KAAK,OAAb,IAAwBE,aAAa,KAAK,KAA9C,EAAqD;AACjDf,MAAAA,KAAK,CAACE,MAAN,CAAaP,KAAb,GAAqB,EAArB;AACA,WAAKvD,MAAL,CAAY8E,IAAZ,CAAiB,8CAAjB;AACA,WAAKd,SAAL,GAAiB,IAAjB;AACA,WAAKzO,QAAL,GAAgB,IAAhB;AACA;AACH;;AACD,QAAI,KAAKyO,SAAL,IAAkBS,QAAQ,IAAI,OAAlC,EAA2C;AACvC,YAAMM,MAAM,GAAG,IAAIC,UAAJ,EAAf;;AACAD,MAAAA,MAAM,CAACE,MAAP,GAAiBC,CAAD,IAAO;AACnB,YAAI7E,EAAJ;;AACA,aAAK9K,QAAL,GAAgB,CAAC8K,EAAE,GAAG6E,CAAC,CAACpB,MAAR,MAAoB,IAApB,IAA4BzD,EAAE,KAAK,KAAK,CAAxC,GAA4C,KAAK,CAAjD,GAAqDA,EAAE,CAAC8E,MAAxE;AACH,OAHD;;AAIAJ,MAAAA,MAAM,CAACK,aAAP,CAAqB,KAAKpB,SAA1B;AACH,KAPD,MAQK;AACD,WAAKzO,QAAL,GAAgB,IAAhB,CADC,CACqB;AACzB;;AACDqL,IAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAKmD,SAA9B;AACH;;AACDqB,EAAAA,aAAa,GAAG;AACZ,WAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,UAAI,CAAC,KAAKxB,SAAV,EAAqB;AACjB,eAAOuB,OAAO,CAAC,IAAD,CAAd;AACH;;AACD3E,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqB,KAAKmD,SAA1B;AACA,WAAKjE,mBAAL,CAAyB0F,SAAzB,CAAmC,KAAKzB,SAAxC,EAAmD0B,SAAnD,CAA8DC,GAAD,IAAS;AAClEJ,QAAAA,OAAO,CAACI,GAAD,CAAP;AACH,OAFD,EAEIC,KAAD,IAAW;AACVJ,QAAAA,MAAM,CAACI,KAAD,CAAN;AACH,OAJD;AAKH,KAVM,CAAP;AAWH;;AACKC,EAAAA,OAAO,GAAG;AAAA;;AAAA;AACZ,UAAI;AACA,YAAI,KAAI,CAAClN,KAAL,IAAc,MAAlB,EAA0B;AACtB,UAAA,KAAI,CAAChC,WAAL,CAAiB4M,KAAjB,CAAuBnB,aAAvB,GAAuC,KAAI,CAACrB,QAA5C;AACA,UAAA,KAAI,CAACpK,WAAL,CAAiB4M,KAAjB,CAAuBuC,KAAvB,GAA+B,KAAI,CAAC9E,MAApC;;AACA,cAAI,KAAI,CAACgD,SAAT,EAAoB;AAChB,kBAAM,KAAI,CAACqB,aAAL,EAAN;AACA,kBAAMU,OAAO,GAAG,KAAI,CAACvF,OAAL,GAAe,KAAI,CAACwD,SAAL,CAAeG,IAA9C;AACA,YAAA,KAAI,CAACxN,WAAL,CAAiB4M,KAAjB,CAAuBnC,KAAvB,GAA+B2E,OAA/B;AACH,WAJD,MAKK;AACD,YAAA,KAAI,CAACpP,WAAL,CAAiB4M,KAAjB,CAAuBnC,KAAvB,GAA+B,KAAI,CAACN,IAAL,CAAUM,KAAzC;AACH;;AACDR,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiC,KAAI,CAAClK,WAAL,CAAiB4M,KAAlD;;AACA,UAAA,KAAI,CAACnD,iBAAL,CAAuB4F,IAAvB,CAA4B,eAA5B;;AACA,gBAAML,GAAG,SAAS,KAAI,CAAC5F,mBAAL,CAAyBkG,cAAzB,CAAwC,KAAI,CAACtP,WAAL,CAAiB4M,KAAzD,EAAgE2C,SAAhE,EAAlB;;AACA,UAAA,KAAI,CAAC9F,iBAAL,CAAuB+F,IAAvB,CAA4B,eAA5B;;AACA,cAAIR,GAAG,CAACS,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,YAAA,KAAI,CAACpG,MAAL,CAAYqG,OAAZ,CAAoB,4BAApB;;AACA,kBAAM5F,KAAK,GAAG;AAAEM,cAAAA,QAAQ,EAAE,KAAI,CAACA;AAAjB,aAAd;;AACA,YAAA,KAAI,CAACd,MAAL,CAAY6C,QAAZ,CAAqB,CAAE,uBAAF,CAArB,EAAgD;AAAErC,cAAAA;AAAF,aAAhD;;AACA,YAAA,KAAI,CAACV,mBAAL,CAAyB6C,oBAAzB,CAA8C,KAAI,CAAC7B,QAAnD;AACH,WALD,MAMK;AACD,YAAA,KAAI,CAACf,MAAL,CAAY4F,KAAZ,CAAkB,uBAAlB;;AACAhF,YAAAA,OAAO,CAACgF,KAAR,CAAc,gCAAd,EAAgDD,GAAG,CAACW,MAApD;AACH;AACJ,SAzBD,MA0BK;AACD,cAAI,KAAI,CAAC3P,WAAL,CAAiB4P,OAArB,EAA8B;AAC1B3F,YAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsC,KAAI,CAAClK,WAAL,CAAiB4M,KAAvD;;AACA,YAAA,KAAI,CAACvD,MAAL,CAAY8E,IAAZ,CAAiB,iCAAjB;;AACA;AACH,WAJD,MAKK;AACD,YAAA,KAAI,CAAC1E,iBAAL,CAAuB4F,IAAvB,CAA4B,eAA5B;;AACA,kBAAM,KAAI,CAACX,aAAL,EAAN;AACA,kBAAMU,OAAO,GAAG,KAAI,CAACvF,OAAL,GAAe,KAAI,CAACwD,SAAL,CAAeG,IAA9C;AACA,YAAA,KAAI,CAACxN,WAAL,CAAiB4M,KAAjB,CAAuBnC,KAAvB,GAA+B2E,OAA/B;AACA,YAAA,KAAI,CAACpP,WAAL,CAAiB4M,KAAjB,CAAuBnB,aAAvB,GAAuC,KAAI,CAACrB,QAA5C;AACAH,YAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqC,KAAI,CAAClK,WAAL,CAAiB4M,KAAtD;AACA,kBAAMoC,GAAG,SAAS,KAAI,CAAC5F,mBAAL,CAAyByG,cAAzB,CAAwC,KAAI,CAAC7P,WAAL,CAAiB4M,KAAzD,EAAgE2C,SAAhE,EAAlB;;AACA,YAAA,KAAI,CAAC9F,iBAAL,CAAuB+F,IAAvB,CAA4B,eAA5B;;AACA,gBAAIR,GAAG,CAACS,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,cAAA,KAAI,CAACpG,MAAL,CAAYqG,OAAZ,CAAoB,0BAApB;;AACA,oBAAM5F,KAAK,GAAG;AAAEM,gBAAAA,QAAQ,EAAE,KAAI,CAACA;AAAjB,eAAd;;AACA,cAAA,KAAI,CAACd,MAAL,CAAY6C,QAAZ,CAAqB,CAAE,uBAAF,CAArB,EAAgD;AAAErC,gBAAAA;AAAF,eAAhD;;AACA,cAAA,KAAI,CAACV,mBAAL,CAAyB6C,oBAAzB,CAA8C,KAAI,CAAC7B,QAAnD;AACH,aALD,MAMK;AACD,cAAA,KAAI,CAACf,MAAL,CAAY4F,KAAZ,CAAkB,uBAAlB;;AACAhF,cAAAA,OAAO,CAACgF,KAAR,CAAc,6BAAd,EAA6CD,GAAG,CAACW,MAAjD;AACH;AACJ;AACJ;AACJ,OAtDD,CAuDA,OAAOV,KAAP,EAAc;AACV,QAAA,KAAI,CAACxF,iBAAL,CAAuB+F,IAAvB,CAA4B,eAA5B;;AACAvF,QAAAA,OAAO,CAACgF,KAAR,CAAc,QAAd,EAAwBA,KAAxB;;AACA,QAAA,KAAI,CAAC5F,MAAL,CAAY4F,KAAZ,CAAkB,uBAAlB;AACH;AA5DW;AA6Df;;AACDa,EAAAA,aAAa,GAAG;AACZ,UAAMhG,KAAK,GAAG;AACVM,MAAAA,QAAQ,EAAE,KAAKA;AADL,KAAd;AAGA,SAAKd,MAAL,CAAY6C,QAAZ,CAAqB,CAAE,uBAAF,CAArB,EAAgD;AAAErC,MAAAA;AAAF,KAAhD;AACH;;AACDiG,EAAAA,MAAM,CAACC,GAAD,EAAMC,aAAN,EAAqB;AACvB,QAAID,GAAG,CAACE,IAAJ,OAAe,EAAnB,EAAuB;AACnB,YAAMxD,SAAS,GAAG,KAAK1M,WAAL,CAAiBC,GAAjB,CAAqBgQ,aAArB,CAAlB;AACAvD,MAAAA,SAAS,CAACyD,MAAV,CAAiB,CAAjB,EAAoB,IAAI1S,WAAJ,CAAgBuS,GAAG,CAACE,IAAJ,EAAhB,CAApB,EAFmB,CAE+B;AACrD;AACJ;;AACD1O,EAAAA,iBAAiB,CAACF,KAAD,EAAQ2O,aAAR,EAAuB;AACpC,UAAMvD,SAAS,GAAG,KAAK1M,WAAL,CAAiBC,GAAjB,CAAqBgQ,aAArB,CAAlB;AACAvD,IAAAA,SAAS,CAAC0D,QAAV,CAAmB9O,KAAnB;AACH;;AACDX,EAAAA,UAAU,CAACsM,KAAD,EAAQT,WAAR,EAAqB;AAC3B,QAAIS,KAAK,CAACoD,GAAN,KAAc,OAAd,IAAyBpD,KAAK,CAACoD,GAAN,KAAc,KAA3C,EAAkD;AAC9C,UAAIpD,KAAK,CAACoD,GAAN,KAAc,OAAlB,EAA2B;AACvBpD,QAAAA,KAAK,CAACqD,cAAN;AACH;;AACD,YAAMC,YAAY,GAAGtD,KAAK,CAACE,MAA3B;AACA,YAAM6C,GAAG,GAAGO,YAAY,CAAC3D,KAAb,CAAmBsD,IAAnB,EAAZ;;AACA,UAAIF,GAAG,KAAK,EAAZ,EAAgB;AACZ,aAAKD,MAAL,CAAYC,GAAZ,EAAiBxD,WAAjB;AACA+D,QAAAA,YAAY,CAAC3D,KAAb,GAAqB,EAArB,CAFY,CAEa;AAC5B;AACJ,KAX0B,CAY3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACH;;AACD5L,EAAAA,WAAW,CAACiM,KAAD,EAAQT,WAAR,EAAqB;AAC5B,UAAM+D,YAAY,GAAGtD,KAAK,CAACE,MAA3B,CAD4B,CACO;;AACnC,UAAMqD,UAAU,GAAGD,YAAY,CAAC3D,KAAb,CAAmBsD,IAAnB,EAAnB;;AACA,QAAIM,UAAU,KAAK,EAAnB,EAAuB;AACnB,WAAKT,MAAL,CAAYS,UAAZ,EAAwBhE,WAAxB;AACA+D,MAAAA,YAAY,CAAC3D,KAAb,GAAqB,EAArB;AACH;AACJ;;AACD/L,EAAAA,cAAc,CAACoM,KAAD,EAAQgD,aAAR,EAAuB;AACjC,QAAIhD,KAAK,CAACoD,GAAN,KAAc,QAAlB,EAA4B;AACxB,YAAME,YAAY,GAAGtD,KAAK,CAACE,MAA3B;;AACA,UAAIoD,YAAY,CAAC3D,KAAb,KAAuB,EAAvB,IAA6B,CAACK,KAAK,CAACwD,QAAxC,EAAkD;AAC9CxD,QAAAA,KAAK,CAACqD,cAAN;AACA,aAAKI,aAAL,CAAmBT,aAAnB;AACH;AACJ;AACJ,GA5Q6B,CA6Q9B;;;AACAS,EAAAA,aAAa,CAACT,aAAD,EAAgB;AACzB,UAAMvD,SAAS,GAAG,KAAK1M,WAAL,CAAiBC,GAAjB,CAAqBgQ,aAArB,CAAlB;;AACA,QAAIvD,SAAS,CAACD,MAAV,GAAmB,CAAvB,EAA0B;AACtBC,MAAAA,SAAS,CAAC0D,QAAV,CAAmB,CAAnB,EADsB,CACC;AAC1B;AACJ;;AACDjI,EAAAA,mBAAmB,CAAC8E,KAAD,EAAQ;AACvB,UAAM0D,QAAQ,GAAG1D,KAAK,CAACE,MAAN,CAAaP,KAA9B;;AACA,QAAI+D,QAAQ,CAACT,IAAT,OAAoB,EAAxB,EAA4B;AACxB,YAAMxD,SAAS,GAAG,KAAK1M,WAAL,CAAiBC,GAAjB,CAAqB,wBAArB,CAAlB;AACA,YAAM2Q,WAAW,GAAGlE,SAAS,CAACM,QAAV,CAAmB6D,IAAnB,CAAwB/D,OAAO,IAAIA,OAAO,CAACF,KAAR,KAAkB+D,QAArD,CAApB;;AACA,UAAI,CAACC,WAAL,EAAkB;AACdlE,QAAAA,SAAS,CAACyD,MAAV,CAAiB,CAAjB,EAAoB,IAAI1S,WAAJ,CAAgBkT,QAAQ,CAACT,IAAT,EAAhB,CAApB;AACH,OAFD,MAGK;AACDjG,QAAAA,OAAO,CAAC6G,IAAR,CAAa,2BAAb;AACA,aAAKzH,MAAL,CAAY8E,IAAZ,CAAiB,2BAAjB;AACH,OATuB,CAUxB;;;AACAlB,MAAAA,KAAK,CAACE,MAAN,CAAaP,KAAb,GAAqB,EAArB;AACH;AACJ;;AACDX,EAAAA,oBAAoB,CAAC7B,QAAD,EAAW;AAC3BH,IAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BE,QAA3B;AACA,SAAKhB,mBAAL,CAAyB6C,oBAAzB,CAA8C7B,QAA9C,EAAwD2E,SAAxD,CAAkE;AAC9DgC,MAAAA,IAAI,EAAG/B,GAAD,IAAS;AACX,YAAIA,GAAG,CAACS,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,eAAKuB,QAAL,GAAgBhC,GAAG,CAACiC,IAApB;AACAhH,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2B,KAAK8G,QAAhC;AACH,SAHD,MAIK;AACD,eAAKvH,iBAAL,CAAuB+F,IAAvB,CAA4B,eAA5B;AACAvF,UAAAA,OAAO,CAACgF,KAAR,CAAc,+BAAd,EAA+CD,GAAG,CAACW,MAAnD;AACH;AACJ,OAV6D;AAW9DV,MAAAA,KAAK,EAAGA,KAAD,IAAW;AACdhF,QAAAA,OAAO,CAACgF,KAAR,CAAc,sCAAd,EAAsDA,KAAtD;AACH;AAb6D,KAAlE;AAeH;;AACDjD,EAAAA,uBAAuB,GAAG;AACtB,SAAK5C,mBAAL,CAAyB4C,uBAAzB,GAAmD+C,SAAnD,CAA6D;AACzDgC,MAAAA,IAAI,EAAG/B,GAAD,IAAS;AACX,YAAIA,GAAG,CAACkC,UAAJ,KAAmB,GAAvB,EAA4B;AACxBjH,UAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0C8E,GAA1C;AACA,eAAKzG,sBAAL,GAA8ByG,GAAG,CAACmC,YAAJ,CAAiB,CAAjB,EAAoBC,YAAlD;AACAnH,UAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyC,KAAK3B,sBAA9C;AACH,SAJD,MAKK;AACD0B,UAAAA,OAAO,CAACgF,KAAR,CAAc,+BAAd,EAA+CD,GAAG,CAACW,MAAnD;AACH;AACJ;AAVwD,KAA7D;AAYH;;AACDrJ,EAAAA,iBAAiB,CAAC+K,KAAD,EAAQ;AACrB,UAAMC,aAAa,GAAGD,KAAK,CAAClE,MAAN,CAAaP,KAAnC;;AACA,QAAI0E,aAAa,KAAK,MAAtB,EAA8B;AAC1B,WAAKtR,WAAL,CAAiBsM,UAAjB,CAA4B;AACxBtB,QAAAA,cAAc,EAAE,GADQ;AAExBC,QAAAA,YAAY,EAAE;AAFU,OAA5B;AAIH,KALD,MAMK,IAAIqG,aAAa,KAAK,QAAtB,EAAgC;AACjC,WAAKtR,WAAL,CAAiBsM,UAAjB,CAA4B;AACxBtB,QAAAA,cAAc,EAAE,OADQ;AAExBC,QAAAA,YAAY,EAAE;AAFU,OAA5B;AAIH,KALI,MAMA,IAAIqG,aAAa,KAAK,QAAtB,EAAgC;AACjC,WAAKtR,WAAL,CAAiBsM,UAAjB,CAA4B;AACxBtB,QAAAA,cAAc,EAAE,OADQ;AAExBC,QAAAA,YAAY,EAAE;AAFU,OAA5B;AAIH,KALI,MAMA,IAAIqG,aAAa,KAAK,MAAtB,EAA8B;AAC/B,WAAKtR,WAAL,CAAiBsM,UAAjB,CAA4B;AACxBtB,QAAAA,cAAc,EAAE,OADQ;AAExBC,QAAAA,YAAY,EAAE;AAFU,OAA5B;AAIH;AACJ;;AA9V6B;;AAgWlChC,oBAAoB,CAACsI,IAArB,GAA4B,SAASC,4BAAT,CAAsCC,CAAtC,EAAyC;AAAE,SAAO,KAAKA,CAAC,IAAIxI,oBAAV,EAAgCrL,EAAE,CAAC8T,iBAAH,CAAqB7T,EAAE,CAAC8T,WAAxB,CAAhC,EAAsE/T,EAAE,CAAC8T,iBAAH,CAAqB5T,EAAE,CAAC8T,mBAAxB,CAAtE,EAAoHhU,EAAE,CAAC8T,iBAAH,CAAqB3T,EAAE,CAAC8T,aAAxB,CAApH,EAA4JjU,EAAE,CAAC8T,iBAAH,CAAqB1T,EAAE,CAAC8T,MAAxB,CAA5J,EAA6LlU,EAAE,CAAC8T,iBAAH,CAAqB1T,EAAE,CAAC+T,cAAxB,CAA7L,EAAsOnU,EAAE,CAAC8T,iBAAH,CAAqBzT,EAAE,CAAC+T,UAAxB,CAAtO,EAA2QpU,EAAE,CAAC8T,iBAAH,CAAqBxT,EAAE,CAAC+T,iBAAxB,CAA3Q,CAAP;AAAgU,CAAvY;;AACAhJ,oBAAoB,CAACiJ,IAArB,GAA4B,aAActU,EAAE,CAACuU,iBAAH,CAAqB;AAAEzE,EAAAA,IAAI,EAAEzE,oBAAR;AAA8BmJ,EAAAA,SAAS,EAAE,CAAC,CAAC,mBAAD,CAAD,CAAzC;AAAkEC,EAAAA,KAAK,EAAE,GAAzE;AAA8EC,EAAAA,IAAI,EAAE,EAApF;AAAwFC,EAAAA,MAAM,EAAE,CAAC,CAAC,CAAD,EAAI,iBAAJ,CAAD,EAAyB,CAAC,CAAD,EAAI,KAAJ,CAAzB,EAAqC,CAAC,CAAD,EAAI,QAAJ,EAAc,aAAd,EAA6B,cAA7B,CAArC,EAAmF,CAAC,CAAD,EAAI,MAAJ,CAAnF,EAAgG,CAAC,CAAD,EAAI,aAAJ,EAAmB,YAAnB,EAAiC,YAAjC,EAA+C,aAA/C,EAA8D,YAA9D,EAA4E,aAA5E,EAA2F,MAA3F,CAAhG,EAAoM,CAAC,CAAD,EAAI,WAAJ,CAApM,EAAsN,CAAC,CAAD,EAAI,cAAJ,EAAoB,CAApB,EAAuB,WAAvB,EAAoC,UAApC,CAAtN,EAAuQ,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAAvQ,EAAsS,CAAC,KAAD,EAAQ,UAAR,EAAoB,CAApB,EAAuB,gBAAvB,CAAtS,EAAgV,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,UAApC,EAAgD,UAAhD,EAA4D,EAA5D,EAAgE,aAAhE,EAA+E,aAA/E,EAA8F,CAA9F,EAAiG,cAAjG,EAAiH,iBAAjH,EAAoI,CAApI,EAAuI,UAAvI,CAAhV,EAAoe,CAAC,OAAD,EAAU,qBAAV,EAAiC,CAAjC,EAAoC,MAApC,CAApe,EAAihB,CAAC,KAAD,EAAQ,eAAR,EAAyB,CAAzB,EAA4B,gBAA5B,CAAjhB,EAAgkB,CAAC,CAAD,EAAI,qBAAJ,CAAhkB,EAA4lB,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAjB,EAA0B,wCAA1B,EAAoE,aAApE,EAAmF,WAAnF,EAAgG,UAAhG,EAA4G,EAA5G,EAAgH,CAAhH,EAAmH,UAAnH,EAA+H,SAA/H,EAA0I,MAA1I,EAAkJ,CAAlJ,EAAqJ,MAArJ,CAA5lB,EAA0vB,CAAC,CAAD,EAAI,SAAJ,CAA1vB,EAA0wB,CAAC,OAAD,EAAU,KAAV,EAAiB,CAAjB,EAAoB,SAApB,EAA+B,CAA/B,EAAkC,OAAlC,EAA2C,SAA3C,CAA1wB,EAAi0B,CAAC,KAAD,EAAQ,yBAAR,EAAmC,CAAnC,EAAsC,gBAAtC,CAAj0B,EAA03B,CAAC,KAAD,EAAQ,kBAAR,EAA4B,CAA5B,EAA+B,gBAA/B,CAA13B,EAA46B,CAAC,KAAD,EAAQ,iBAAR,EAA2B,CAA3B,EAA8B,gBAA9B,CAA56B,EAA69B,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,gBAAxB,CAA79B,EAAwgC,CAAC,KAAD,EAAQ,gBAAR,EAA0B,CAA1B,EAA6B,gBAA7B,CAAxgC,EAAwjC,CAAC,KAAD,EAAQ,wBAAR,EAAkC,CAAlC,EAAqC,gBAArC,CAAxjC,EAAgnC,CAAC,OAAD,EAAU,wCAAV,EAAoD,UAApD,EAAgE,EAAhE,EAAoE,CAApE,EAAuE,UAAvE,EAAmF,QAAnF,EAA6F,SAA7F,EAAwG,CAAxG,EAA2G,MAA3G,CAAhnC,EAAouC,CAAC,KAAD,EAAQ,gCAAR,EAA0C,CAA1C,EAA6C,gBAA7C,CAApuC,EAAoyC,CAAC,iBAAD,EAAoB,gCAApB,EAAsD,UAAtD,EAAkE,EAAlE,EAAsE,CAAtE,EAAyE,cAAzE,EAAyF,iBAAzF,EAA4G,CAA5G,EAA+G,UAA/G,CAApyC,EAAg6C,CAAC,OAAD,EAAU,EAAV,EAAc,UAAd,EAA0B,EAA1B,EAA8B,UAA9B,EAA0C,EAA1C,CAAh6C,EAA+8C,CAAC,OAAD,EAAU,KAAV,CAA/8C,EAAi+C,CAAC,OAAD,EAAU,IAAV,CAAj+C,EAAk/C,CAAC,KAAD,EAAQ,yCAAR,EAAmD,CAAnD,EAAsD,gBAAtD,CAAl/C,EAA2jD,CAAC,iBAAD,EAAoB,yCAApB,EAA+D,UAA/D,EAA2E,EAA3E,EAA+E,CAA/E,EAAkF,cAAlF,EAAkG,iBAAlG,EAAqH,CAArH,EAAwH,UAAxH,CAA3jD,EAAgsD,CAAC,KAAD,EAAQ,qCAAR,EAA+C,CAA/C,EAAkD,gBAAlD,CAAhsD,EAAqwD,CAAC,iBAAD,EAAoB,qCAApB,EAA2D,UAA3D,EAAuE,EAAvE,EAA2E,CAA3E,EAA8E,cAA9E,EAA8F,iBAA9F,EAAiH,CAAjH,EAAoH,UAApH,CAArwD,EAAs4D,CAAC,KAAD,EAAQ,mCAAR,EAA6C,CAA7C,EAAgD,gBAAhD,CAAt4D,EAAy8D,CAAC,iBAAD,EAAoB,mCAApB,EAAyD,UAAzD,EAAqE,EAArE,EAAyE,CAAzE,EAA4E,cAA5E,EAA4F,iBAA5F,EAA+G,CAA/G,EAAkH,UAAlH,CAAz8D,EAAwkE,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,gBAA1B,CAAxkE,EAAqnE,CAAC,iBAAD,EAAoB,aAApB,EAAmC,UAAnC,EAA+C,EAA/C,EAAmD,CAAnD,EAAsD,cAAtD,EAAsE,iBAAtE,EAAyF,CAAzF,EAA4F,UAA5F,CAArnE,EAA8tE,CAAC,OAAD,EAAU,EAAV,EAAc,UAAd,EAA0B,EAA1B,CAA9tE,EAA6vE,CAAC,CAAD,EAAI,OAAJ,CAA7vE,EAA2wE,CAAC,KAAD,EAAQ,gBAAR,EAA0B,CAA1B,EAA6B,gBAA7B,CAA3wE,EAA2zE,CAAC,iBAAD,EAAoB,gBAApB,EAAsC,UAAtC,EAAkD,EAAlD,EAAsD,MAAtD,EAA8D,GAA9D,EAAmE,MAAnE,EAA2E,IAA3E,EAAiF,aAAjF,EAAgG,mBAAhG,EAAqH,CAArH,EAAwH,cAAxH,EAAwI,iBAAxI,EAA2J,CAA3J,EAA8J,UAA9J,CAA3zE,EAAs+E,CAAC,CAAD,EAAI,aAAJ,CAAt+E,EAA0/E,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,sBAA5B,EAAoD,CAApD,EAAuD,MAAvD,CAA1/E,EAA0jF,CAAC,CAAD,EAAI,KAAJ,EAAW,WAAX,EAAwB,CAAxB,EAA2B,OAA3B,CAA1jF,EAA+lF,CAAC,KAAD,EAAQ,OAAR,EAAiB,CAAjB,EAAoB,gBAApB,CAA/lF,EAAsoF,CAAC,CAAD,EAAI,sBAAJ,CAAtoF,EAAmqF,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,OAApC,EAA6C,UAA7C,EAAyD,EAAzD,EAA6D,QAA7D,EAAuE,SAAvE,EAAkF,CAAlF,EAAqF,cAArF,EAAqG,iBAArG,EAAwH,CAAxH,EAA2H,UAA3H,EAAuI,QAAvI,CAAnqF,EAAqzF,CAAC,KAAD,EAAQ,YAAR,EAAsB,OAAtB,EAA+B,aAA/B,EAA8C,CAA9C,EAAiD,KAAjD,EAAwD,CAAxD,EAA2D,MAA3D,CAArzF,EAAy3F,CAAC,OAAD,EAAU,SAAV,EAAqB,CAArB,EAAwB,MAAxB,CAAz3F,EAA05F,CAAC,KAAD,EAAQ,YAAR,EAAsB,CAAtB,EAAyB,aAAzB,EAAwC,CAAxC,EAA2C,KAA3C,CAA15F,EAA68F,CAAC,CAAD,EAAI,SAAJ,CAA78F,EAA69F,CAAC,KAAD,EAAQ,OAAR,CAA79F,EAA++F,CAAC,OAAD,EAAU,aAAV,EAAyB,CAAzB,EAA4B,MAA5B,CAA/+F,EAAohG,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,OAApC,EAA6C,UAA7C,EAAyD,EAAzD,EAA6D,CAA7D,EAAgE,cAAhE,EAAgF,iBAAhF,EAAmG,CAAnG,EAAsG,UAAtG,CAAphG,EAAuoG,CAAC,CAAD,EAAI,aAAJ,CAAvoG,EAA2pG,CAAC,MAAD,EAAS,MAAT,EAAiB,aAAjB,EAAgC,WAAhC,EAA6C,UAA7C,EAAyD,EAAzD,EAA6D,CAA7D,EAAgE,WAAhE,EAA6E,cAA7E,EAA6F,iBAA7F,EAAgH,CAAhH,EAAmH,UAAnH,EAA+H,SAA/H,EAA0I,MAA1I,CAA3pG,EAA8yG,CAAC,CAAD,EAAI,KAAJ,EAAW,CAAX,EAAc,SAAd,CAA9yG,EAAw0G,CAAC,OAAD,EAAU,OAAV,EAAmB,CAAnB,EAAsB,OAAtB,EAA+B,CAA/B,EAAkC,MAAlC,CAAx0G,EAAm3G,CAAC,CAAD,EAAI,OAAJ,EAAa,CAAb,EAAgB,OAAhB,CAAn3G,EAA64G,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,gBAA1B,CAA74G,EAA07G,CAAC,UAAD,EAAa,EAAb,EAAiB,CAAjB,EAAoB,cAApB,EAAoC,iBAApC,EAAuD,CAAvD,EAA0D,UAA1D,EAAsE,QAAtE,CAA17G,EAA2gH,CAAC,OAAD,EAAU,EAAV,EAAc,UAAd,EAA0B,EAA1B,EAA8B,UAA9B,EAA0C,EAA1C,CAA3gH,EAA0jH,CAAC,OAAD,EAAU,MAAV,CAA1jH,EAA6kH,CAAC,OAAD,EAAU,QAAV,CAA7kH,EAAkmH,CAAC,OAAD,EAAU,QAAV,CAAlmH,EAAunH,CAAC,OAAD,EAAU,MAAV,CAAvnH,EAA0oH,CAAC,MAAD,EAAS,MAAT,EAAiB,UAAjB,EAA6B,EAA7B,EAAiC,CAAjC,EAAoC,cAApC,EAAoD,iBAApD,EAAuE,CAAvE,EAA0E,OAA1E,EAAmF,UAAnF,CAA1oH,EAA0uH,CAAC,UAAD,EAAa,EAAb,EAAiB,CAAjB,EAAoB,cAApB,EAAoC,WAApC,EAAiD,iBAAjD,EAAoE,CAApE,EAAuE,UAAvE,EAAmF,QAAnF,EAA6F,SAA7F,CAA1uH,EAAm1H,CAAC,UAAD,EAAa,EAAb,EAAiB,UAAjB,EAA6B,EAA7B,EAAiC,OAAjC,EAA0C,EAA1C,CAAn1H,EAAk4H,CAAC,CAAD,EAAI,OAAJ,EAAa,CAAb,EAAgB,OAAhB,EAAyB,SAAzB,CAAl4H,EAAu6H,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,MAA5C,CAAv6H,CAAhG;AAA6jIC,EAAAA,QAAQ,EAAE,SAASC,6BAAT,CAAuCnU,EAAvC,EAA2CC,GAA3C,EAAgD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AAC5rIV,MAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,aAArB;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,CAAV;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACyB,UAAH,CAAc,UAAd,EAA0B,SAASqT,sDAAT,GAAkE;AAAE,eAAOnU,GAAG,CAAC2Q,OAAJ,EAAP;AAAuB,OAArH;AACAtR,MAAAA,EAAE,CAACmB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,YAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACY,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,CAA1B;AACAZ,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBT,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAtB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBU,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAzC,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,gBAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBY,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,OAAhE,EAAyE,EAAzE;AACA3C,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBgC,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA/D,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,sBAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBuC,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,OAAhE,EAAyE,EAAzE;AACAtE,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBoD,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAnF,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,eAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBuD,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,OAAhE,EAAyE,EAAzE;AACAtF,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBoE,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAnG,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,qBAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBuE,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,OAAhE,EAAyE,EAAzE;AACAtG,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBoF,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAnH,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,aAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBuF,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,OAAhE,EAAyE,EAAzE;AACAtH,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBoG,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAnI,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBuG,oCAAlB,EAAwD,EAAxD,EAA4D,CAA5D,EAA+D,KAA/D,EAAsE,EAAtE;AACAtI,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkB6G,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA5I,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,kBAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBgH,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,OAAhE,EAAyE,EAAzE;AACA/I,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkB6H,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA5J,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,0BAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBoI,uCAAlB,EAA2D,CAA3D,EAA8D,CAA9D,EAAiE,QAAjE,EAA2E,EAA3E;AACAnK,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,EAAd,EAAkBkJ,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAjL,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,6BAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,6BAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,KAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,IAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,sCAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,sCAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,KAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,IAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,kCAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,kCAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,KAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,EAAV,EAAc,IAAd;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,CAA9B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,gCAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,gCAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,KAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,IAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,CAA9B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,aAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,wBAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,SAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,aAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,CAA9B;AACAnB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAnB,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,kBAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACY,SAAH,CAAa,GAAb,EAAkB,UAAlB,EAA8B,EAA9B;AACAZ,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAnB,MAAAA,EAAE,CAAC+B,UAAH,CAAc,GAAd,EAAmBqJ,wCAAnB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,QAAnE,EAA6E,EAA7E;AACApL,MAAAA,EAAE,CAACmB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAnB,MAAAA,EAAE,CAACyB,UAAH,CAAc,OAAd,EAAuB,SAASsT,uDAAT,GAAmE;AAAE,eAAOpU,GAAG,CAACuR,aAAJ,EAAP;AAA6B,OAAzH;AACAlS,MAAAA,EAAE,CAACoB,MAAH,CAAU,GAAV,EAAe,QAAf;AACApB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACArB,MAAAA,EAAE,CAACqB,YAAH;AACH;;AAAC,QAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAIsU,OAAJ;AACA,UAAIC,OAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACAtV,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACqE,kBAAH,CAAsB,EAAtB,EAA0B1D,GAAG,CAACyD,KAA9B,EAAqC,OAArC;AACApE,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,WAAd,EAA2BJ,GAAG,CAACyB,WAA/B;AACApC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACwB,UAA9B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsBJ,GAAG,CAACwB,UAA1B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyB,CAACiU,OAAO,GAAGrU,GAAG,CAACyB,WAAJ,CAAgBC,GAAhB,CAAoB,eAApB,CAAX,KAAoD,IAApD,GAA2D,IAA3D,GAAkE2S,OAAO,CAAChG,KAAnG;AACAhP,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyB,CAACkU,OAAO,GAAGtU,GAAG,CAACyB,WAAJ,CAAgBC,GAAhB,CAAoB,yBAApB,CAAX,KAA8D,IAA9D,GAAqE,IAArE,GAA4E4S,OAAO,CAACjG,KAA7G;AACAhP,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyB,CAACmU,QAAQ,GAAGvU,GAAG,CAACyB,WAAJ,CAAgBC,GAAhB,CAAoB,kBAApB,CAAZ,KAAwD,IAAxD,GAA+D,IAA/D,GAAsE6S,QAAQ,CAAClG,KAAxG;AACAhP,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyB,CAACoU,QAAQ,GAAGxU,GAAG,CAACyB,WAAJ,CAAgBC,GAAhB,CAAoB,iBAApB,CAAZ,KAAuD,IAAvD,GAA8D,IAA9D,GAAqE8S,QAAQ,CAACnG,KAAvG;AACAhP,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyB,CAACqU,QAAQ,GAAGzU,GAAG,CAACyB,WAAJ,CAAgBC,GAAhB,CAAoB,WAApB,CAAZ,KAAiD,IAAjD,GAAwD,IAAxD,GAA+D+S,QAAQ,CAACpG,KAAjG;AACAhP,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsBJ,GAAG,CAACwB,UAA1B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyB,CAACsU,QAAQ,GAAG1U,GAAG,CAACyB,WAAJ,CAAgBC,GAAhB,CAAoB,gBAApB,CAAZ,KAAsD,IAAtD,GAA6D,IAA7D,GAAoEgT,QAAQ,CAACrG,KAAtG;AACAhP,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,SAAd,EAAyB,CAACuU,QAAQ,GAAG3U,GAAG,CAACyB,WAAJ,CAAgBC,GAAhB,CAAoB,wBAApB,CAAZ,KAA8D,IAA9D,GAAqE,IAArE,GAA4EiT,QAAQ,CAACtG,KAA9G;AACAhP,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACwB,UAA9B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,EAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACwB,UAA9B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,EAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACwB,UAA9B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,EAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACwB,UAA9B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,EAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACwB,UAA9B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,OAAd,EAAuB,IAAvB;AACAf,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,OAAd,EAAuB,KAAvB;AACAf,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACwB,UAA9B;AACAnC,MAAAA,EAAE,CAACkC,SAAH,CAAa,CAAb;AACAlC,MAAAA,EAAE,CAACe,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACwB,UAA3B;AACH;AAAE,GAnRwD;AAmRtDoT,EAAAA,UAAU,EAAE,CAAChV,EAAE,CAACiV,gBAAJ,EAAsBvV,EAAE,CAACwV,aAAzB,EAAwCxV,EAAE,CAACyV,oBAA3C,EAAiEzV,EAAE,CAAC0V,kBAApE,EAAwF1V,EAAE,CAAC2V,oBAA3F,EAAiH3V,EAAE,CAAC4V,eAApH,EAAqI5V,EAAE,CAAC6V,eAAxI,EAAyJ7V,EAAE,CAAC8V,iBAA5J,EAA+KvV,EAAE,CAACwV,IAAlL,EAAwLxV,EAAE,CAACyV,OAA3L,EAAoMhW,EAAE,CAACiW,0BAAvM,EAAmOjW,EAAE,CAACkW,cAAtO,EAAsPlW,EAAE,CAACmW,uBAAzP,EAAkR5V,EAAE,CAAC6V,OAArR,CAnR0C;AAmRqPC,EAAAA,MAAM,EAAE,CAAC,ynUAAD;AAnR7P,CAArB,CAA1C", "sourcesContent": ["import { FormControl, Validators } from '@angular/forms';\r\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"@angular/forms\";\r\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\r\nimport * as i3 from \"ngx-toastr\";\r\nimport * as i4 from \"@angular/router\";\r\nimport * as i5 from \"@angular/common/http\";\r\nimport * as i6 from \"ngx-spinner\";\r\nimport * as i7 from \"../../../../sidebar.component\";\r\nimport * as i8 from \"@angular/common\";\r\nfunction AddEditRoleComponent_div_14_img_5_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"img\", 48);\r\n} if (rf & 2) {\r\n    const ctx_r19 = i0.ɵɵnextContext(2);\r\n    i0.ɵɵproperty(\"src\", ctx_r19.imageSrc, i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction AddEditRoleComponent_div_14_div_6_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 49);\r\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditRoleComponent_div_14_Template(rf, ctx) { if (rf & 1) {\r\n    const _r22 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 7);\r\n    i0.ɵɵelementStart(1, \"label\", 43);\r\n    i0.ɵɵtext(2, \"Role Image\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(3, \"div\", 44);\r\n    i0.ɵɵelementStart(4, \"input\", 45);\r\n    i0.ɵɵlistener(\"change\", function AddEditRoleComponent_div_14_Template_input_change_4_listener($event) { i0.ɵɵrestoreView(_r22); const ctx_r21 = i0.ɵɵnextContext(); return ctx_r21.onFileSelected($event); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(5, AddEditRoleComponent_div_14_img_5_Template, 1, 1, \"img\", 46);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(6, AddEditRoleComponent_div_14_div_6_Template, 2, 0, \"div\", 47);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r0 = i0.ɵɵnextContext();\r\n    let tmp_2_0;\r\n    i0.ɵɵadvance(4);\r\n    i0.ɵɵproperty(\"readonly\", ctx_r0.isReadonly);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.imageSrc);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.addRoleForm.get(\"RO_dp\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors.fileSizeValidator);\r\n} }\r\nfunction AddEditRoleComponent_div_15_span_3_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"span\", 53);\r\n    i0.ɵɵtext(1, \"*\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditRoleComponent_div_15_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 7);\r\n    i0.ɵɵelementStart(1, \"label\", 50);\r\n    i0.ɵɵtext(2, \"Role Image\");\r\n    i0.ɵɵtemplate(3, AddEditRoleComponent_div_15_span_3_Template, 2, 0, \"span\", 51);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(4, \"input\", 52);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r1 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isReadonly);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"readonly\", ctx_r1.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_input_20_Template(rf, ctx) { if (rf & 1) {\r\n    const _r25 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"input\", 54);\r\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_20_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r25); const ctx_r24 = i0.ɵɵnextContext(); return ctx_r24.onEnterKey($event, \"RO_References\"); })(\"keydown\", function AddEditRoleComponent_input_20_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r25); const ctx_r26 = i0.ɵɵnextContext(); return ctx_r26.onBackspaceKey($event, \"RO_References\"); })(\"blur\", function AddEditRoleComponent_input_20_Template_input_blur_0_listener($event) { i0.ɵɵrestoreView(_r25); const ctx_r27 = i0.ɵɵnextContext(); return ctx_r27.onInputBlur($event, \"RO_References\"); });\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r2 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"readonly\", ctx_r2.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_div_22_span_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r33 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"span\", 57);\r\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_22_span_2_Template_span_click_0_listener() { i0.ɵɵrestoreView(_r33); const i_r29 = i0.ɵɵnextContext().index; const ctx_r31 = i0.ɵɵnextContext(); return ctx_r31.removeTagForField(i_r29, \"RO_References\"); });\r\n    i0.ɵɵtext(1, \"x\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nconst _c0 = function (a0) { return { \"view-mode\": a0 }; };\r\nfunction AddEditRoleComponent_div_22_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 55);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_22_span_2_Template, 2, 0, \"span\", 56);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const tag_r28 = ctx.$implicit;\r\n    const ctx_r3 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r3.title === \"View\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", tag_r28, \" \");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_input_27_Template(rf, ctx) { if (rf & 1) {\r\n    const _r35 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"input\", 54);\r\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_27_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r35); const ctx_r34 = i0.ɵɵnextContext(); return ctx_r34.onEnterKey($event, \"RO_Skills_and_Knowledge\"); })(\"keydown\", function AddEditRoleComponent_input_27_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r35); const ctx_r36 = i0.ɵɵnextContext(); return ctx_r36.onBackspaceKey($event, \"RO_Skills_and_Knowledge\"); })(\"blur\", function AddEditRoleComponent_input_27_Template_input_blur_0_listener($event) { i0.ɵɵrestoreView(_r35); const ctx_r37 = i0.ɵɵnextContext(); return ctx_r37.onInputBlur($event, \"RO_Skills_and_Knowledge\"); });\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r4 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"readonly\", ctx_r4.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_div_29_span_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r43 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"span\", 57);\r\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_29_span_2_Template_span_click_0_listener() { i0.ɵɵrestoreView(_r43); const i_r39 = i0.ɵɵnextContext().index; const ctx_r41 = i0.ɵɵnextContext(); return ctx_r41.removeTagForField(i_r39, \"RO_Skills_and_Knowledge\"); });\r\n    i0.ɵɵtext(1, \"x\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditRoleComponent_div_29_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 55);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_29_span_2_Template, 2, 0, \"span\", 56);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const tag_r38 = ctx.$implicit;\r\n    const ctx_r5 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r5.title === \"View\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", tag_r38, \" \");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_input_34_Template(rf, ctx) { if (rf & 1) {\r\n    const _r45 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"input\", 54);\r\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_34_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r45); const ctx_r44 = i0.ɵɵnextContext(); return ctx_r44.onEnterKey($event, \"RO_Place_of_Work\"); })(\"keydown\", function AddEditRoleComponent_input_34_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r45); const ctx_r46 = i0.ɵɵnextContext(); return ctx_r46.onBackspaceKey($event, \"RO_Place_of_Work\"); })(\"blur\", function AddEditRoleComponent_input_34_Template_input_blur_0_listener($event) { i0.ɵɵrestoreView(_r45); const ctx_r47 = i0.ɵɵnextContext(); return ctx_r47.onInputBlur($event, \"RO_Place_of_Work\"); });\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r6 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"readonly\", ctx_r6.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_div_36_span_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r53 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"span\", 57);\r\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_36_span_2_Template_span_click_0_listener() { i0.ɵɵrestoreView(_r53); const i_r49 = i0.ɵɵnextContext().index; const ctx_r51 = i0.ɵɵnextContext(); return ctx_r51.removeTagForField(i_r49, \"RO_Place_of_Work\"); });\r\n    i0.ɵɵtext(1, \"x\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditRoleComponent_div_36_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 55);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_36_span_2_Template, 2, 0, \"span\", 56);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const tag_r48 = ctx.$implicit;\r\n    const ctx_r7 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r7.title === \"View\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", tag_r48, \" \");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_input_41_Template(rf, ctx) { if (rf & 1) {\r\n    const _r55 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"input\", 54);\r\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_41_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r55); const ctx_r54 = i0.ɵɵnextContext(); return ctx_r54.onEnterKey($event, \"RO_Key_Insights\"); })(\"keydown\", function AddEditRoleComponent_input_41_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r55); const ctx_r56 = i0.ɵɵnextContext(); return ctx_r56.onBackspaceKey($event, \"RO_Key_Insights\"); })(\"blur\", function AddEditRoleComponent_input_41_Template_input_blur_0_listener($event) { i0.ɵɵrestoreView(_r55); const ctx_r57 = i0.ɵɵnextContext(); return ctx_r57.onInputBlur($event, \"RO_Key_Insights\"); });\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r8 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"readonly\", ctx_r8.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_div_43_span_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r63 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"span\", 57);\r\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_43_span_2_Template_span_click_0_listener() { i0.ɵɵrestoreView(_r63); const i_r59 = i0.ɵɵnextContext().index; const ctx_r61 = i0.ɵɵnextContext(); return ctx_r61.removeTagForField(i_r59, \"RO_Key_Insights\"); });\r\n    i0.ɵɵtext(1, \"x\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditRoleComponent_div_43_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 55);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_43_span_2_Template, 2, 0, \"span\", 56);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const tag_r58 = ctx.$implicit;\r\n    const ctx_r9 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r9.title === \"View\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", tag_r58, \" \");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_input_49_Template(rf, ctx) { if (rf & 1) {\r\n    const _r65 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"input\", 54);\r\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_49_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r65); const ctx_r64 = i0.ɵɵnextContext(); return ctx_r64.onEnterKey($event, \"RO_Salary\"); })(\"keydown\", function AddEditRoleComponent_input_49_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r65); const ctx_r66 = i0.ɵɵnextContext(); return ctx_r66.onBackspaceKey($event, \"RO_Salary\"); })(\"blur\", function AddEditRoleComponent_input_49_Template_input_blur_0_listener($event) { i0.ɵɵrestoreView(_r65); const ctx_r67 = i0.ɵɵnextContext(); return ctx_r67.onInputBlur($event, \"RO_Salary\"); });\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r10 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"readonly\", ctx_r10.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_div_51_span_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r73 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"span\", 57);\r\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_51_span_2_Template_span_click_0_listener() { i0.ɵɵrestoreView(_r73); const i_r69 = i0.ɵɵnextContext().index; const ctx_r71 = i0.ɵɵnextContext(); return ctx_r71.removeTagForField(i_r69, \"RO_Salary\"); });\r\n    i0.ɵɵtext(1, \"x\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditRoleComponent_div_51_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 55);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_51_span_2_Template, 2, 0, \"span\", 56);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const tag_r68 = ctx.$implicit;\r\n    const ctx_r11 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r11.title === \"View\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", tag_r68, \" \");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !ctx_r11.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_div_52_Template(rf, ctx) { if (rf & 1) {\r\n    const _r75 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 7);\r\n    i0.ɵɵelementStart(1, \"label\", 58);\r\n    i0.ɵɵtext(2, \"Expected Starting Salary\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(3, \"select\", 59);\r\n    i0.ɵɵlistener(\"change\", function AddEditRoleComponent_div_52_Template_select_change_3_listener($event) { i0.ɵɵrestoreView(_r75); const ctx_r74 = i0.ɵɵnextContext(); return ctx_r74.updateSalaryRange($event); });\r\n    i0.ɵɵelementStart(4, \"option\", 60);\r\n    i0.ɵɵtext(5, \"Select a salary range\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(6, \"option\", 61);\r\n    i0.ɵɵtext(7, \"Less than 25k\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(8, \"option\", 62);\r\n    i0.ɵɵtext(9, \"25k - 30k\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(10, \"option\", 63);\r\n    i0.ɵɵtext(11, \"30k - 35k\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(12, \"option\", 64);\r\n    i0.ɵɵtext(13, \"More than 35k\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r12 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"disabled\", ctx_r12.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_div_53_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 7);\r\n    i0.ɵɵelementStart(1, \"label\", 58);\r\n    i0.ɵɵtext(2, \"Salary Range\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(3, \"input\", 65);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r13 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"value\", ctx_r13.salaryRange)(\"readonly\", ctx_r13.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_input_58_Template(rf, ctx) { if (rf & 1) {\r\n    const _r77 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"input\", 54);\r\n    i0.ɵɵlistener(\"keydown\", function AddEditRoleComponent_input_58_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r77); const ctx_r76 = i0.ɵɵnextContext(); return ctx_r76.onEnterKey($event, \"RO_Daily_Tasks\"); })(\"keydown\", function AddEditRoleComponent_input_58_Template_input_keydown_0_listener($event) { i0.ɵɵrestoreView(_r77); const ctx_r78 = i0.ɵɵnextContext(); return ctx_r78.onBackspaceKey($event, \"RO_Daily_Tasks\"); })(\"blur\", function AddEditRoleComponent_input_58_Template_input_blur_0_listener($event) { i0.ɵɵrestoreView(_r77); const ctx_r79 = i0.ɵɵnextContext(); return ctx_r79.onInputBlur($event, \"RO_Daily_Tasks\"); });\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r14 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"readonly\", ctx_r14.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_div_60_span_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r85 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"span\", 57);\r\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_60_span_2_Template_span_click_0_listener() { i0.ɵɵrestoreView(_r85); const i_r81 = i0.ɵɵnextContext().index; const ctx_r83 = i0.ɵɵnextContext(); return ctx_r83.removeTagForField(i_r81, \"RO_Daily_Tasks\"); });\r\n    i0.ɵɵtext(1, \"x\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditRoleComponent_div_60_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 55);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_60_span_2_Template, 2, 0, \"span\", 56);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const tag_r80 = ctx.$implicit;\r\n    const ctx_r15 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r15.title === \"View\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", tag_r80, \" \");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_select_66_option_3_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"option\", 37);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const activity_r87 = ctx.$implicit;\r\n    i0.ɵɵproperty(\"value\", activity_r87.QUO_title);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate(activity_r87.QUO_title);\r\n} }\r\nfunction AddEditRoleComponent_select_66_Template(rf, ctx) { if (rf & 1) {\r\n    const _r89 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"select\", 66);\r\n    i0.ɵɵlistener(\"change\", function AddEditRoleComponent_select_66_Template_select_change_0_listener($event) { i0.ɵɵrestoreView(_r89); const ctx_r88 = i0.ɵɵnextContext(); return ctx_r88.addExpectedActivity($event); })(\"keydown\", function AddEditRoleComponent_select_66_Template_select_keydown_0_listener($event) { i0.ɵɵrestoreView(_r89); const ctx_r90 = i0.ɵɵnextContext(); return ctx_r90.onBackspaceKey($event, \"RO_Expected_Activities\"); });\r\n    i0.ɵɵelementStart(1, \"option\", 67);\r\n    i0.ɵɵtext(2, \"Add New +\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(3, AddEditRoleComponent_select_66_option_3_Template, 2, 2, \"option\", 68);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r16 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"disabled\", ctx_r16.isReadonly);\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.expectedActivitiesList);\r\n} }\r\nfunction AddEditRoleComponent_div_68_span_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r96 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"span\", 57);\r\n    i0.ɵɵlistener(\"click\", function AddEditRoleComponent_div_68_span_2_Template_span_click_0_listener() { i0.ɵɵrestoreView(_r96); const i_r92 = i0.ɵɵnextContext().index; const ctx_r94 = i0.ɵɵnextContext(); return ctx_r94.removeTagForField(i_r92, \"RO_Expected_Activities\"); });\r\n    i0.ɵɵtext(1, \"x\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditRoleComponent_div_68_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 55);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵtemplate(2, AddEditRoleComponent_div_68_span_2_Template, 2, 0, \"span\", 56);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const tag_r91 = ctx.$implicit;\r\n    const ctx_r17 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r17.title === \"View\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", tag_r91, \" \");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !ctx_r17.isReadonly);\r\n} }\r\nfunction AddEditRoleComponent_button_125_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"button\", 69);\r\n    i0.ɵɵtext(1, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nexport class AddEditRoleComponent {\r\n    constructor(formBuilder, dataTransferService, toastr, router, activeRoute, httpClient, ngxSpinnerService) {\r\n        var _a;\r\n        this.formBuilder = formBuilder;\r\n        this.dataTransferService = dataTransferService;\r\n        this.toastr = toastr;\r\n        this.router = router;\r\n        this.activeRoute = activeRoute;\r\n        this.httpClient = httpClient;\r\n        this.ngxSpinnerService = ngxSpinnerService;\r\n        this.p = 1;\r\n        this.showForm = false;\r\n        this.title = 'Add New';\r\n        this.isReadonly = false;\r\n        this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\r\n        const state = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras.state;\r\n        if (state) {\r\n            console.log('Role from state : ', state.role);\r\n            (this.sectorId = state.sectorId);\r\n            (this.title = state.title);\r\n            (this.isReadonly = state.isReadonly);\r\n            (this.role = state.role);\r\n            (this.roleId = state.roleId);\r\n        }\r\n        this.addRoleForm = this.formBuilder.group({\r\n            RO_title: ['', Validators.required],\r\n            RO_dp: [null, Validators.required],\r\n            RO_References: this.formBuilder.array([], Validators.required),\r\n            RO_Skills_and_Knowledge: this.formBuilder.array([], Validators.required),\r\n            RO_Place_of_Work: this.formBuilder.array([], Validators.required),\r\n            RO_Key_Insights: this.formBuilder.array([], Validators.required),\r\n            RO_Salary: this.formBuilder.array([], Validators.required),\r\n            RO_Salary_from: ['', Validators.required],\r\n            RO_Salary_to: ['', Validators.required],\r\n            RO_Daily_Tasks: this.formBuilder.array([], Validators.required),\r\n            RO_Expected_Activities: this.formBuilder.array([], Validators.required),\r\n            RO_Expected_to_on_the_weekends: ['', Validators.required],\r\n            RO_Expected_to_work_beyond_nine_to_five: ['', Validators.required],\r\n            RO_Expected_to_work_internationally: ['', Validators.required],\r\n            RO_Expected_to_work_in_the_office: ['', Validators.required],\r\n            RO_description: ['', Validators.required],\r\n            RO_IndustryId: [''],\r\n            RO_isAccept: [false]\r\n        });\r\n    }\r\n    ngOnInit() {\r\n        var _a, _b, _c, _d, _e;\r\n        this.getAllExpctedActivities();\r\n        this.getAllRoleBySectorId(this.sectorId);\r\n        this.sectorId = this.sectorId;\r\n        if (this.title == 'View') {\r\n            (_a = this.addRoleForm.get('RO_Expected_to_on_the_weekends')) === null || _a === void 0 ? void 0 : _a.disable();\r\n            (_b = this.addRoleForm.get('RO_Expected_to_work_beyond_nine_to_five')) === null || _b === void 0 ? void 0 : _b.disable();\r\n            (_c = this.addRoleForm.get('RO_Expected_to_work_internationally')) === null || _c === void 0 ? void 0 : _c.disable();\r\n            (_d = this.addRoleForm.get('RO_Expected_to_work_in_the_office')) === null || _d === void 0 ? void 0 : _d.disable();\r\n            (_e = this.addRoleForm.get('RO_isAccept')) === null || _e === void 0 ? void 0 : _e.disable();\r\n        }\r\n        if (!this.sectorId) {\r\n            this.router.navigate([`actions/sectors/roles`]);\r\n        }\r\n        ;\r\n        if (this.role) {\r\n            console.log(\"Role Data :\", this.role);\r\n            this.setRange();\r\n            this.patchArrayValues(this.role.RO_References, 'RO_References');\r\n            this.patchArrayValues(this.role.RO_Expected_Activities, 'RO_Expected_Activities');\r\n            this.patchArrayValues(this.role.RO_Salary, 'RO_Salary');\r\n            this.patchArrayValues(this.role.RO_Daily_Tasks, 'RO_Daily_Tasks');\r\n            this.patchArrayValues(this.role.RO_Skills_and_Knowledge, 'RO_Skills_and_Knowledge');\r\n            this.patchArrayValues(this.role.RO_Place_of_Work, 'RO_Place_of_Work');\r\n            this.patchArrayValues(this.role.RO_Key_Insights, 'RO_Key_Insights');\r\n            this.addRoleForm.patchValue({\r\n                RO_isAccept: this.role.RO_isAccept == 'true' ? true : false,\r\n            });\r\n            this.addRoleForm.patchValue(this.role);\r\n        }\r\n    }\r\n    setRange() {\r\n        if (this.role.RO_Salary_from == \"0\" && this.role.RO_Salary_to == \"25\") {\r\n            this.salaryRange = \"<25k\";\r\n        }\r\n        else if (this.role.RO_Salary_from == \"25\" && this.role.RO_Salary_to == \"30\") {\r\n            this.salaryRange = \"25-30k\";\r\n        }\r\n        else if (this.role.RO_Salary_from == \"30\" && this.role.RO_Salary_to == \"35\") {\r\n            this.salaryRange = \"30-35k\";\r\n        }\r\n        else if (this.role.RO_Salary_from == \"35\" && this.role.RO_Salary_to == \"100\") {\r\n            this.salaryRange = \"35k+\";\r\n        }\r\n    }\r\n    patchArrayValues(values, controlName) {\r\n        if (values && values.length > 0) {\r\n            const formArray = this.addRoleForm.get(controlName);\r\n            values.forEach(value => {\r\n                formArray.push(this.formBuilder.control(value));\r\n            });\r\n        }\r\n    }\r\n    get f() {\r\n        return this.addRoleForm.controls;\r\n    }\r\n    onFileSelected(event) {\r\n        var _a;\r\n        let selectedFile = event.target.files[0];\r\n        if (event.target.files.length === 0) {\r\n            // Reset both imageName and imageSrc when no file is selected\r\n            this.imageName = null;\r\n            this.imageSrc = null;\r\n            return;\r\n        }\r\n        const newFileName = FileValidator.addTimestamp(selectedFile.name);\r\n        this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });\r\n        if (this.imageName) {\r\n            const formControl = this.addRoleForm.get('RO_dp');\r\n            formControl === null || formControl === void 0 ? void 0 : formControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\r\n            formControl === null || formControl === void 0 ? void 0 : formControl.updateValueAndValidity();\r\n        }\r\n        const fileType = this.imageName.type.split('/')[0];\r\n        const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\r\n        if (fileType !== 'image' || fileExtension === 'svg') {\r\n            event.target.value = '';\r\n            this.toastr.info('Please select an image file (excluding SVG).');\r\n            this.imageName = null;\r\n            this.imageSrc = null;\r\n            return;\r\n        }\r\n        if (this.imageName && fileType == 'image') {\r\n            const reader = new FileReader();\r\n            reader.onload = (e) => {\r\n                var _a;\r\n                this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\r\n            };\r\n            reader.readAsDataURL(this.imageName);\r\n        }\r\n        else {\r\n            this.imageSrc = null; // Reset imageSrc if no file selected\r\n        }\r\n        console.log('imageName', this.imageName);\r\n    }\r\n    uploadLogoUrl() {\r\n        return new Promise((resolve, reject) => {\r\n            if (!this.imageName) {\r\n                return resolve(null);\r\n            }\r\n            console.log('image', this.imageName);\r\n            this.dataTransferService.uploadurl(this.imageName).subscribe((res) => {\r\n                resolve(res);\r\n            }, (error) => {\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n    async addRole() {\r\n        try {\r\n            if (this.title == 'Edit') {\r\n                this.addRoleForm.value.RO_IndustryId = this.sectorId;\r\n                this.addRoleForm.value.RO_id = this.roleId;\r\n                if (this.imageName) {\r\n                    await this.uploadLogoUrl();\r\n                    const fileUrl = this.baseUrl + this.imageName.name;\r\n                    this.addRoleForm.value.RO_dp = fileUrl;\r\n                }\r\n                else {\r\n                    this.addRoleForm.value.RO_dp = this.role.RO_dp;\r\n                }\r\n                console.log(\"Role edit data : \", this.addRoleForm.value);\r\n                this.ngxSpinnerService.show('globalSpinner');\r\n                const res = await this.dataTransferService.updateRoleData(this.addRoleForm.value).toPromise();\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                if (res.statusCode == 200) {\r\n                    this.toastr.success(\"Role updated successfully.\");\r\n                    const state = { sectorId: this.sectorId };\r\n                    this.router.navigate([`actions/sectors/roles`], { state });\r\n                    this.dataTransferService.getAllRoleBySectorId(this.sectorId);\r\n                }\r\n                else {\r\n                    this.toastr.error(\"Something went wrong.\");\r\n                    console.error('Unable to update role. Status:', res.status);\r\n                }\r\n            }\r\n            else {\r\n                if (this.addRoleForm.invalid) {\r\n                    console.log(\"this.addRoleForm.value\", this.addRoleForm.value);\r\n                    this.toastr.info(\"Please fill all required fields\");\r\n                    return;\r\n                }\r\n                else {\r\n                    this.ngxSpinnerService.show('globalSpinner');\r\n                    await this.uploadLogoUrl();\r\n                    const fileUrl = this.baseUrl + this.imageName.name;\r\n                    this.addRoleForm.value.RO_dp = fileUrl;\r\n                    this.addRoleForm.value.RO_IndustryId = this.sectorId;\r\n                    console.log(\"Add role post data : \", this.addRoleForm.value);\r\n                    const res = await this.dataTransferService.insertRoleData(this.addRoleForm.value).toPromise();\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    if (res.statusCode == 200) {\r\n                        this.toastr.success(\"Role added successfully.\");\r\n                        const state = { sectorId: this.sectorId };\r\n                        this.router.navigate([`actions/sectors/roles`], { state });\r\n                        this.dataTransferService.getAllRoleBySectorId(this.sectorId);\r\n                    }\r\n                    else {\r\n                        this.toastr.error(\"Something went wrong.\");\r\n                        console.error('Unable to add role. Status:', res.status);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        catch (error) {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            console.error('Error:', error);\r\n            this.toastr.error(\"Something went wrong.\");\r\n        }\r\n    }\r\n    addRoleCnlBtn() {\r\n        const state = {\r\n            sectorId: this.sectorId\r\n        };\r\n        this.router.navigate([`actions/sectors/roles`], { state });\r\n    }\r\n    addTag(tag, formArrayName) {\r\n        if (tag.trim() !== '') {\r\n            const formArray = this.addRoleForm.get(formArrayName);\r\n            formArray.insert(0, new FormControl(tag.trim())); // Insert at the beginning\r\n        }\r\n    }\r\n    removeTagForField(index, formArrayName) {\r\n        const formArray = this.addRoleForm.get(formArrayName);\r\n        formArray.removeAt(index);\r\n    }\r\n    onEnterKey(event, controlName) {\r\n        if (event.key === 'Enter' || event.key === 'Tab') {\r\n            if (event.key === 'Enter') {\r\n                event.preventDefault();\r\n            }\r\n            const inputElement = event.target;\r\n            const tag = inputElement.value.trim();\r\n            if (tag !== '') {\r\n                this.addTag(tag, controlName);\r\n                inputElement.value = ''; // Clear the input field after adding the tag\r\n            }\r\n        }\r\n        // if (event.key === 'Tab') {\r\n        //   const inputElement = event.target as HTMLInputElement;\r\n        //   const tag = inputElement.value.trim();\r\n        //   if (tag !== '') {\r\n        //     this.addTag(tag, controlName);\r\n        //     inputElement.value = ''; // Clear the input field after adding the tag\r\n        //   }\r\n        // }\r\n    }\r\n    onInputBlur(event, controlName) {\r\n        const inputElement = event.target; // Type assertion\r\n        const trimmedTag = inputElement.value.trim();\r\n        if (trimmedTag !== '') {\r\n            this.addTag(trimmedTag, controlName);\r\n            inputElement.value = '';\r\n        }\r\n    }\r\n    onBackspaceKey(event, formArrayName) {\r\n        if (event.key === 'Delete') {\r\n            const inputElement = event.target;\r\n            if (inputElement.value === '' && !event.shiftKey) {\r\n                event.preventDefault();\r\n                this.removeLastTag(formArrayName);\r\n            }\r\n        }\r\n    }\r\n    // Method to remove the last tag from the specified form array\r\n    removeLastTag(formArrayName) {\r\n        const formArray = this.addRoleForm.get(formArrayName);\r\n        if (formArray.length > 0) {\r\n            formArray.removeAt(0); // Remove the first tag\r\n        }\r\n    }\r\n    addExpectedActivity(event) {\r\n        const activity = event.target.value;\r\n        if (activity.trim() !== '') {\r\n            const formArray = this.addRoleForm.get('RO_Expected_Activities');\r\n            const isDuplicate = formArray.controls.some(control => control.value === activity);\r\n            if (!isDuplicate) {\r\n                formArray.insert(0, new FormControl(activity.trim()));\r\n            }\r\n            else {\r\n                console.warn('Activity already selected');\r\n                this.toastr.info('Activity already selected');\r\n            }\r\n            // Reset the value of the select element\r\n            event.target.value = '';\r\n        }\r\n    }\r\n    getAllRoleBySectorId(sectorId) {\r\n        console.log(\"sectorId : \", sectorId);\r\n        this.dataTransferService.getAllRoleBySectorId(sectorId).subscribe({\r\n            next: (res) => {\r\n                if (res.statusCode === 200) {\r\n                    this.RoleList = res.data;\r\n                    console.log(\"RoleList : \", this.RoleList);\r\n                }\r\n                else {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    console.error('Failed to fetch role. Status:', res.status);\r\n                }\r\n            },\r\n            error: (error) => {\r\n                console.error('Error occurred while fetching roles:', error);\r\n            },\r\n        });\r\n    }\r\n    getAllExpctedActivities() {\r\n        this.dataTransferService.getAllExpctedActivities().subscribe({\r\n            next: (res) => {\r\n                if (res.statuscode === 200) {\r\n                    console.log(\"expectedActivitiesList res\", res);\r\n                    this.expectedActivitiesList = res.filteredData[0].quiz_options;\r\n                    console.log(\"expectedActivitiesList : \", this.expectedActivitiesList);\r\n                }\r\n                else {\r\n                    console.error('Failed to fetch role. Status:', res.status);\r\n                }\r\n            }\r\n        });\r\n    }\r\n    updateSalaryRange(range) {\r\n        const selectedRange = range.target.value;\r\n        if (selectedRange === '<25k') {\r\n            this.addRoleForm.patchValue({\r\n                RO_Salary_from: \"0\",\r\n                RO_Salary_to: \"25000\"\r\n            });\r\n        }\r\n        else if (selectedRange === '25-30k') {\r\n            this.addRoleForm.patchValue({\r\n                RO_Salary_from: \"25000\",\r\n                RO_Salary_to: \"30000\"\r\n            });\r\n        }\r\n        else if (selectedRange === '30-35k') {\r\n            this.addRoleForm.patchValue({\r\n                RO_Salary_from: \"30000\",\r\n                RO_Salary_to: \"35000\"\r\n            });\r\n        }\r\n        else if (selectedRange === '35k+') {\r\n            this.addRoleForm.patchValue({\r\n                RO_Salary_from: \"35000\",\r\n                RO_Salary_to: \"100000\"\r\n            });\r\n        }\r\n    }\r\n}\r\nAddEditRoleComponent.ɵfac = function AddEditRoleComponent_Factory(t) { return new (t || AddEditRoleComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.NgxSpinnerService)); };\r\nAddEditRoleComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: AddEditRoleComponent, selectors: [[\"app-add-edit-role\"]], decls: 128, vars: 30, consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\", \"col-lg-4\"], [\"for\", \"RO_title\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"RO_title\", \"required\", \"\", \"placeholder\", \"Enter title\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"class\", \"form-group col-lg-4\", 4, \"ngIf\"], [\"for\", \"RO_References\", 1, \"required-field\"], [1, \"tag-input-container\"], [\"type\", \"text\", \"class\", \"tag-input form-control form-control-sm\", \"placeholder\", \"Add New +\", \"required\", \"\", 3, \"readonly\", \"keydown\", \"blur\", 4, \"ngIf\"], [1, \"tag-box\"], [\"class\", \"tag\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"RO_Skills_and_Knowledge\", 1, \"required-field\"], [\"for\", \"RO_Place_of_Work\", 1, \"required-field\"], [\"for\", \"RO_Key_Insights\", 1, \"required-field\"], [\"for\", \"RO_Salary\", 1, \"required-field\"], [\"for\", \"RO_Daily_Tasks\", 1, \"required-field\"], [\"for\", \"RO_Expected_Activities\", 1, \"required-field\"], [\"class\", \"form-control tag-input form-control-sm\", \"required\", \"\", 3, \"disabled\", \"change\", \"keydown\", 4, \"ngIf\"], [\"for\", \"RO_Expected_to_on_the_weekends\", 1, \"required-field\"], [\"formControlName\", \"RO_Expected_to_on_the_weekends\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"value\", \"\", \"disabled\", \"\", \"selected\", \"\"], [\"value\", \"Yes\"], [\"value\", \"No\"], [\"for\", \"RO_Expected_to_work_beyond_nine_to_five\", 1, \"required-field\"], [\"formControlName\", \"RO_Expected_to_work_beyond_nine_to_five\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"for\", \"RO_Expected_to_work_internationally\", 1, \"required-field\"], [\"formControlName\", \"RO_Expected_to_work_internationally\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"for\", \"RO_Expected_to_work_in_the_office\", 1, \"required-field\"], [\"formControlName\", \"RO_Expected_to_work_in_the_office\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"for\", \"RO_isAccept\", 1, \"required-field\"], [\"formControlName\", \"RO_isAccept\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"value\", \"\", \"disabled\", \"\"], [3, \"value\"], [\"for\", \"RO_description\", 1, \"required-field\"], [\"formControlName\", \"RO_description\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Role description \", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [1, \"text-center\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [1, \"btn\", \"btn-light\", 3, \"click\"], [\"for\", \"RO_dp\", 1, \"required-field\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"RO_dp\", \"required\", \"\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Role Image\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"alt\", \"Role Image\", 1, \"img-preview\", 3, \"src\"], [1, \"warning\"], [\"for\", \"RO_dp\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"RO_dp\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [1, \"text-danger\"], [\"type\", \"text\", \"placeholder\", \"Add New +\", \"required\", \"\", 1, \"tag-input\", \"form-control\", \"form-control-sm\", 3, \"readonly\", \"keydown\", \"blur\"], [1, \"tag\", 3, \"ngClass\"], [\"class\", \"close\", 3, \"click\", 4, \"ngIf\"], [1, \"close\", 3, \"click\"], [\"for\", \"salaryRange\", 1, \"required-field\"], [\"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\", \"change\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [\"value\", \"<25k\"], [\"value\", \"25-30k\"], [\"value\", \"30-35k\"], [\"value\", \"35k+\"], [\"type\", \"text\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"value\", \"readonly\"], [\"required\", \"\", 1, \"form-control\", \"tag-input\", \"form-control-sm\", 3, \"disabled\", \"change\", \"keydown\"], [\"selected\", \"\", \"disabled\", \"\", \"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]], template: function AddEditRoleComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵelementStart(0, \"app-sidebar\");\r\n        i0.ɵɵelementStart(1, \"div\", 0);\r\n        i0.ɵɵelementStart(2, \"div\", 1);\r\n        i0.ɵɵelementStart(3, \"div\", 2);\r\n        i0.ɵɵelementStart(4, \"div\", 3);\r\n        i0.ɵɵelementStart(5, \"div\", 4);\r\n        i0.ɵɵtext(6);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(7, \"div\", 5);\r\n        i0.ɵɵelementStart(8, \"form\", 6);\r\n        i0.ɵɵlistener(\"ngSubmit\", function AddEditRoleComponent_Template_form_ngSubmit_8_listener() { return ctx.addRole(); });\r\n        i0.ɵɵelementStart(9, \"div\", 1);\r\n        i0.ɵɵelementStart(10, \"div\", 7);\r\n        i0.ɵɵelementStart(11, \"label\", 8);\r\n        i0.ɵɵtext(12, \"Role Title\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(13, \"input\", 9);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(14, AddEditRoleComponent_div_14_Template, 7, 3, \"div\", 10);\r\n        i0.ɵɵtemplate(15, AddEditRoleComponent_div_15_Template, 5, 2, \"div\", 10);\r\n        i0.ɵɵelementStart(16, \"div\", 7);\r\n        i0.ɵɵelementStart(17, \"label\", 11);\r\n        i0.ɵɵtext(18, \"Role Resources\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(19, \"div\", 12);\r\n        i0.ɵɵtemplate(20, AddEditRoleComponent_input_20_Template, 1, 1, \"input\", 13);\r\n        i0.ɵɵelementStart(21, \"div\", 14);\r\n        i0.ɵɵtemplate(22, AddEditRoleComponent_div_22_Template, 3, 5, \"div\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(23, \"div\", 7);\r\n        i0.ɵɵelementStart(24, \"label\", 16);\r\n        i0.ɵɵtext(25, \"Skills And Knowledge\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(26, \"div\", 12);\r\n        i0.ɵɵtemplate(27, AddEditRoleComponent_input_27_Template, 1, 1, \"input\", 13);\r\n        i0.ɵɵelementStart(28, \"div\", 14);\r\n        i0.ɵɵtemplate(29, AddEditRoleComponent_div_29_Template, 3, 5, \"div\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(30, \"div\", 7);\r\n        i0.ɵɵelementStart(31, \"label\", 17);\r\n        i0.ɵɵtext(32, \"Place Of Work\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(33, \"div\", 12);\r\n        i0.ɵɵtemplate(34, AddEditRoleComponent_input_34_Template, 1, 1, \"input\", 13);\r\n        i0.ɵɵelementStart(35, \"div\", 14);\r\n        i0.ɵɵtemplate(36, AddEditRoleComponent_div_36_Template, 3, 5, \"div\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(37, \"div\", 7);\r\n        i0.ɵɵelementStart(38, \"label\", 18);\r\n        i0.ɵɵtext(39, \"Role Key Highlights\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(40, \"div\", 12);\r\n        i0.ɵɵtemplate(41, AddEditRoleComponent_input_41_Template, 1, 1, \"input\", 13);\r\n        i0.ɵɵelementStart(42, \"div\", 14);\r\n        i0.ɵɵtemplate(43, AddEditRoleComponent_div_43_Template, 3, 5, \"div\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(44, \"div\", 1);\r\n        i0.ɵɵelementStart(45, \"div\", 7);\r\n        i0.ɵɵelementStart(46, \"label\", 19);\r\n        i0.ɵɵtext(47, \"Role Salary\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(48, \"div\", 12);\r\n        i0.ɵɵtemplate(49, AddEditRoleComponent_input_49_Template, 1, 1, \"input\", 13);\r\n        i0.ɵɵelementStart(50, \"div\", 14);\r\n        i0.ɵɵtemplate(51, AddEditRoleComponent_div_51_Template, 3, 5, \"div\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(52, AddEditRoleComponent_div_52_Template, 14, 1, \"div\", 10);\r\n        i0.ɵɵtemplate(53, AddEditRoleComponent_div_53_Template, 4, 2, \"div\", 10);\r\n        i0.ɵɵelementStart(54, \"div\", 7);\r\n        i0.ɵɵelementStart(55, \"label\", 20);\r\n        i0.ɵɵtext(56, \"Role Daily Tasks\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(57, \"div\", 12);\r\n        i0.ɵɵtemplate(58, AddEditRoleComponent_input_58_Template, 1, 1, \"input\", 13);\r\n        i0.ɵɵelementStart(59, \"div\", 14);\r\n        i0.ɵɵtemplate(60, AddEditRoleComponent_div_60_Template, 3, 5, \"div\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(61, \"div\", 1);\r\n        i0.ɵɵelementStart(62, \"div\", 7);\r\n        i0.ɵɵelementStart(63, \"label\", 21);\r\n        i0.ɵɵtext(64, \"Role Expected Activities\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(65, \"div\", 12);\r\n        i0.ɵɵtemplate(66, AddEditRoleComponent_select_66_Template, 4, 2, \"select\", 22);\r\n        i0.ɵɵelementStart(67, \"div\", 14);\r\n        i0.ɵɵtemplate(68, AddEditRoleComponent_div_68_Template, 3, 5, \"div\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(69, \"div\", 7);\r\n        i0.ɵɵelementStart(70, \"label\", 23);\r\n        i0.ɵɵtext(71, \"Expected To On The Weekends\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(72, \"select\", 24);\r\n        i0.ɵɵelementStart(73, \"option\", 25);\r\n        i0.ɵɵtext(74, \"Expected to on the weekends\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(75, \"option\", 26);\r\n        i0.ɵɵtext(76, \"Yes\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(77, \"option\", 27);\r\n        i0.ɵɵtext(78, \"No\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(79, \"div\", 7);\r\n        i0.ɵɵelementStart(80, \"label\", 28);\r\n        i0.ɵɵtext(81, \"Expected To Work Beyond Nine To Five\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(82, \"select\", 29);\r\n        i0.ɵɵelementStart(83, \"option\", 25);\r\n        i0.ɵɵtext(84, \"Expected to work beyond nine to five\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(85, \"option\", 26);\r\n        i0.ɵɵtext(86, \"Yes\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(87, \"option\", 27);\r\n        i0.ɵɵtext(88, \"No\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(89, \"div\", 1);\r\n        i0.ɵɵelementStart(90, \"div\", 7);\r\n        i0.ɵɵelementStart(91, \"label\", 30);\r\n        i0.ɵɵtext(92, \"Expected To Work Internationally\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(93, \"select\", 31);\r\n        i0.ɵɵelementStart(94, \"option\", 25);\r\n        i0.ɵɵtext(95, \"Expected to work internationally\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(96, \"option\", 26);\r\n        i0.ɵɵtext(97, \"Yes\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(98, \"option\", 27);\r\n        i0.ɵɵtext(99, \"No\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(100, \"div\", 7);\r\n        i0.ɵɵelementStart(101, \"label\", 32);\r\n        i0.ɵɵtext(102, \"Expected to Work In The Office\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(103, \"select\", 33);\r\n        i0.ɵɵelementStart(104, \"option\", 25);\r\n        i0.ɵɵtext(105, \"Expected to work in the office\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(106, \"option\", 26);\r\n        i0.ɵɵtext(107, \"Yes\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(108, \"option\", 27);\r\n        i0.ɵɵtext(109, \"No\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(110, \"div\", 7);\r\n        i0.ɵɵelementStart(111, \"label\", 34);\r\n        i0.ɵɵtext(112, \"Role Status\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(113, \"select\", 35);\r\n        i0.ɵɵelementStart(114, \"option\", 36);\r\n        i0.ɵɵtext(115, \"Please select a status\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(116, \"option\", 37);\r\n        i0.ɵɵtext(117, \"Approve\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(118, \"option\", 37);\r\n        i0.ɵɵtext(119, \"Not Approve\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(120, \"div\", 7);\r\n        i0.ɵɵelementStart(121, \"label\", 38);\r\n        i0.ɵɵtext(122, \"Role Description\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(123, \"textarea\", 39);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(124, \"div\", 40);\r\n        i0.ɵɵtemplate(125, AddEditRoleComponent_button_125_Template, 2, 0, \"button\", 41);\r\n        i0.ɵɵelementStart(126, \"button\", 42);\r\n        i0.ɵɵlistener(\"click\", function AddEditRoleComponent_Template_button_click_126_listener() { return ctx.addRoleCnlBtn(); });\r\n        i0.ɵɵtext(127, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n    } if (rf & 2) {\r\n        let tmp_6_0;\r\n        let tmp_8_0;\r\n        let tmp_10_0;\r\n        let tmp_12_0;\r\n        let tmp_14_0;\r\n        let tmp_18_0;\r\n        let tmp_20_0;\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Role\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"formGroup\", ctx.addRoleForm);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.isReadonly);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngForOf\", (tmp_6_0 = ctx.addRoleForm.get(\"RO_References\")) == null ? null : tmp_6_0.value);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngForOf\", (tmp_8_0 = ctx.addRoleForm.get(\"RO_Skills_and_Knowledge\")) == null ? null : tmp_8_0.value);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngForOf\", (tmp_10_0 = ctx.addRoleForm.get(\"RO_Place_of_Work\")) == null ? null : tmp_10_0.value);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngForOf\", (tmp_12_0 = ctx.addRoleForm.get(\"RO_Key_Insights\")) == null ? null : tmp_12_0.value);\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngForOf\", (tmp_14_0 = ctx.addRoleForm.get(\"RO_Salary\")) == null ? null : tmp_14_0.value);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.isReadonly);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngForOf\", (tmp_18_0 = ctx.addRoleForm.get(\"RO_Daily_Tasks\")) == null ? null : tmp_18_0.value);\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngForOf\", (tmp_20_0 = ctx.addRoleForm.get(\"RO_Expected_Activities\")) == null ? null : tmp_20_0.value);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\r\n        i0.ɵɵadvance(10);\r\n        i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\r\n        i0.ɵɵadvance(11);\r\n        i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\r\n        i0.ɵɵadvance(10);\r\n        i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\r\n        i0.ɵɵadvance(10);\r\n        i0.ɵɵproperty(\"disabled\", ctx.isReadonly);\r\n        i0.ɵɵadvance(3);\r\n        i0.ɵɵproperty(\"value\", true);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"value\", false);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n    } }, directives: [i7.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.RequiredValidator, i8.NgIf, i8.NgForOf, i1.SelectControlValueAccessor, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i8.NgClass], styles: [\".tag-input-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  \\n  border: 1px solid #ced4da;\\n  \\n  border-radius: 5px;\\n  \\n  padding: 5px;\\n  \\n  overflow-x: auto;\\n  \\n  overflow-y: hidden;\\n  \\n  height: 50px;\\n}\\n\\n.tag.view-mode[_ngcontent-%COMP%] {\\n  background-color: #e2e4e7;\\n  color: #495057;\\n}\\n\\n.tag-input-container[_ngcontent-%COMP%]:focus-within {\\n  outline: 1px solid rgba(0, 123, 255, 0.65);\\n  \\n  transition: outline 0.3s ease;\\n  \\n}\\n\\n.tag-box[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  \\n  flex-wrap: nowrap;\\n  \\n  grid-gap: 5px;\\n  gap: 5px;\\n  \\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  background-color: #4B49AC;\\n  color: #fff;\\n  padding: 2px 5px;\\n  margin-top: 2px;\\n  margin-bottom: 2px;\\n  border-radius: 10px;\\n  display: flex;\\n  \\n  align-items: center;\\n  max-height: auto;\\n  white-space: nowrap;\\n  \\n  overflow: hidden;\\n  \\n}\\n\\n.tag[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%] {\\n  margin-left: 5px;\\n  margin-top: 2px;\\n  margin-bottom: 2px;\\n  cursor: pointer;\\n  border: none;\\n  outline: none;\\n  color: #fff;\\n}\\n\\n.tag-input[_ngcontent-%COMP%] {\\n  border: none;\\n  outline: none;\\n  margin: 2px 5px 2px 3px;\\n  \\n  max-height: 30px;\\n  min-width: 150px;\\n  \\n}\\n\\n.tag-input[_ngcontent-%COMP%]:focus-within {\\n  border: none;\\n  outline: none;\\n}\\n\\n\\n\\n.tag-input-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n  \\n}\\n\\n.tag-input-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #888;\\n  \\n  border-radius: 4px;\\n  \\n}\\n\\n\\n\\n.tag-input-container[_ngcontent-%COMP%] {\\n  scrollbar-width: thin;\\n  \\n}\\n\\n.tag-input-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: #888;\\n  \\n  border-radius: 4px;\\n  \\n}\\n\\ninput[type=text][_ngcontent-%COMP%], input[type=file][_ngcontent-%COMP%], input[type=number][_ngcontent-%COMP%], select[_ngcontent-%COMP%] {\\n  height: 50px;\\n  \\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"] });\r\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { Validators } from '@angular/forms';\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"src/app/shared/services/data-transfer.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"ngx-spinner\";\nimport * as i7 from \"../../../../sidebar.component\";\nimport * as i8 from \"@angular/common\";\n\nfunction AddEditSectorComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" Error: Word Limit Exceeded! (20 Words) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditSectorComponent_img_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 22);\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.imageSrc, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction AddEditSectorComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditSectorComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" Error: Word Limit exceeded!(200 Words) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditSectorComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"label\", 23);\n    i0.ɵɵtext(2, \"Creator Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 24);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"readonly\", ctx_r4.isReadonly);\n  }\n}\n\nfunction AddEditSectorComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵtext(1, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction wordLimitValidator(maxWords) {\n  return control => {\n    if (control.value) {\n      const words = control === null || control === void 0 ? void 0 : control.value.trim().split(/\\s+/);\n\n      if (words.length > maxWords) {\n        console.log('maxwords', maxWords);\n        return {\n          wordLimitExceeded: true,\n          wordCount: words.length\n        };\n      }\n    }\n\n    return null;\n  };\n}\n\nexport class AddEditSectorComponent {\n  constructor(router, formBuilder, dataTransferService, toastr, activeRoute, httpClient, ngxSpinnerService) {\n    var _a;\n\n    this.router = router;\n    this.formBuilder = formBuilder;\n    this.dataTransferService = dataTransferService;\n    this.toastr = toastr;\n    this.activeRoute = activeRoute;\n    this.httpClient = httpClient;\n    this.ngxSpinnerService = ngxSpinnerService;\n    this.p = 1;\n    this.showForm = false;\n    this.title = 'Add New';\n    this.isReadonly = false;\n    this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\n    const state = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras.state;\n\n    if (state) {\n      this.sectorId = state === null || state === void 0 ? void 0 : state.sectorId;\n      this.title = state === null || state === void 0 ? void 0 : state.title;\n      this.sectorData = state === null || state === void 0 ? void 0 : state.sector;\n      console.log(\"Sector Data\", this.sectorData);\n    } else {\n      this.router.navigate([`actions/sectors`]);\n    }\n\n    this.addSectorForm = this.formBuilder.group({\n      IN_name: ['', [Validators.required, wordLimitValidator(20)]],\n      IN_dp: [null, Validators.required],\n      IN_description: ['', [Validators.required, wordLimitValidator(200)]],\n      IN_createdBy: ['', Validators.required] // Assuming this is required as per your form\n\n    });\n  }\n\n  ngOnInit() {\n    if (this.title == 'Edit') {\n      this.addSectorForm.patchValue({\n        IN_name: this.sectorData.IN_name,\n        // IN_dp: this.sectorData.IN_dp,\n        IN_description: this.sectorData.IN_description,\n        IN_createdBy: this.sectorData.IN_createdBy\n      });\n    }\n  }\n\n  onFileSelected(event) {\n    var _a;\n\n    let selectedFile = event.target.files[0];\n\n    if (event.target.files.length === 0) {\n      // Reset both imageName and imageSrc when no file is selected\n      this.imageName = null;\n      this.imageSrc = null;\n      return;\n    }\n\n    const newFileName = FileValidator.addTimestamp(selectedFile.name);\n    this.imageName = new File([selectedFile], newFileName, {\n      type: selectedFile.type\n    });\n\n    if (this.imageName) {\n      const formControl = this.addSectorForm.get('IN_dp');\n      formControl === null || formControl === void 0 ? void 0 : formControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\n      formControl === null || formControl === void 0 ? void 0 : formControl.updateValueAndValidity();\n    }\n\n    const fileType = this.imageName.type.split('/')[0];\n    const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n\n    if (fileType !== 'image' || fileExtension === 'svg') {\n      event.target.value = '';\n      this.toastr.info('Please select an image file (excluding SVG).');\n      this.imageName = null;\n      this.imageSrc = null;\n      return;\n    }\n\n    if (this.imageName && fileType == 'image') {\n      const reader = new FileReader();\n\n      reader.onload = e => {\n        var _a;\n\n        this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\n      };\n\n      reader.readAsDataURL(this.imageName);\n    } else {\n      this.imageSrc = null; // Reset imageSrc if no file selected\n    }\n\n    console.log('imageName', this.imageName);\n  }\n\n  uploadLogoUrl() {\n    return new Promise((resolve, reject) => {\n      if (!this.imageName) {\n        return reject(\"Image Not Provide\");\n      }\n\n      console.log('image', this.imageName);\n      this.dataTransferService.uploadurl(this.imageName).subscribe(res => {\n        resolve(res);\n      }, error => {\n        reject(error); // Reject the promise with the error\n      });\n    });\n  }\n\n  insertSectorData() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        if (_this.title == 'Edit') {\n          _this.addSectorForm.value.IN_id = _this.sectorId;\n\n          if (_this.imageName) {\n            yield _this.uploadLogoUrl();\n            const fileUrl = _this.baseUrl + _this.imageName.name;\n            _this.addSectorForm.value.IN_dp = fileUrl;\n          } else {\n            _this.addSectorForm.value.IN_dp = _this.sectorData.IN_dp;\n          }\n\n          console.log(\"Data for edit : \", _this.addSectorForm.value);\n          const res = yield _this.dataTransferService.updateIndustryData(_this.addSectorForm.value).toPromise();\n\n          _this.ngxSpinnerService.hide('globalSpinner');\n\n          if (res.statusCode == 200) {\n            _this.toastr.success(\"Sector updated successfully.\");\n\n            _this.router.navigate([`actions/sectors`]);\n          } else {\n            _this.toastr.error(\"Something went wrong.\");\n\n            console.error('Unable to add role. Status:', res.status);\n          }\n        } else {\n          if (_this.addSectorForm.invalid) {\n            _this.toastr.info('', 'Please fill all required fields');\n\n            return;\n          } else {\n            yield _this.uploadLogoUrl();\n            const fileUrl = _this.baseUrl + _this.imageName.name;\n            const data = {\n              IN_name: _this.addSectorForm.value.IN_name,\n              IN_description: _this.addSectorForm.value.IN_description,\n              IN_createdBy: _this.addSectorForm.value.IN_createdBy,\n              IN_dp: fileUrl\n            };\n            console.log('ADD SECTOR DATA', data);\n\n            _this.ngxSpinnerService.show('globalSpinner');\n\n            const res = yield _this.dataTransferService.insertIndustryData(data).toPromise();\n\n            _this.ngxSpinnerService.hide('globalSpinner');\n\n            if (res.statusCode == 200) {\n              console.log('Sector posted successfully:', res);\n\n              _this.toastr.success('Sector added successfully.');\n\n              _this.getAllSectorsData();\n\n              _this.router.navigate(['/actions/sectors']);\n            } else {\n              _this.toastr.error('', 'Something Went Wrong');\n            }\n          }\n        }\n      } catch (error) {\n        _this.ngxSpinnerService.hide('globalSpinner');\n\n        console.error('Error:', error);\n\n        _this.toastr.error('Something went wrong.');\n      }\n    })();\n  }\n\n  getAllSectorsData() {\n    this.dataTransferService.getIndustryData().subscribe({\n      next: res => {\n        if (res.status === 200) {} else {\n          console.error('Failed to fetch role. Status:', res.status);\n        }\n      }\n    });\n  }\n\n}\n\nAddEditSectorComponent.ɵfac = function AddEditSectorComponent_Factory(t) {\n  return new (t || AddEditSectorComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.DataTransferService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.NgxSpinnerService));\n};\n\nAddEditSectorComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AddEditSectorComponent,\n  selectors: [[\"app-add-edit-sector\"]],\n  decls: 33,\n  vars: 11,\n  consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\", \"col-lg-6\"], [\"for\", \"IN_name\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"IN_name\", \"required\", \"\", \"placeholder\", \"Enter Sector Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"for\", \"IN_dp\", 1, \"required-field\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"IN_dp\", \"required\", \"\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Sector Image\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"for\", \"IN_description\", 1, \"required-field\"], [\"formControlName\", \"IN_description\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Enter Sector Description\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"class\", \"form-group col-lg-6\", 4, \"ngIf\"], [1, \"text-center\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [\"routerLink\", \"/actions/sectors\", 1, \"btn\", \"btn-light\"], [1, \"warning\"], [\"alt\", \"Sector Image\", 1, \"img-preview\", 3, \"src\"], [\"for\", \"IN_createdBy\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"IN_createdBy\", \"required\", \"\", \"placeholder\", \"Enter Creator Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]],\n  template: function AddEditSectorComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"app-sidebar\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵtext(6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵelementStart(8, \"form\", 6);\n      i0.ɵɵlistener(\"ngSubmit\", function AddEditSectorComponent_Template_form_ngSubmit_8_listener() {\n        return ctx.insertSectorData();\n      });\n      i0.ɵɵelementStart(9, \"div\", 1);\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵelementStart(11, \"label\", 8);\n      i0.ɵɵtext(12, \"Sector Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(13, \"input\", 9);\n      i0.ɵɵtemplate(14, AddEditSectorComponent_div_14_Template, 2, 0, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"div\", 7);\n      i0.ɵɵelementStart(16, \"label\", 11);\n      i0.ɵɵtext(17, \"Sector Image\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 12);\n      i0.ɵɵelementStart(19, \"input\", 13);\n      i0.ɵɵlistener(\"change\", function AddEditSectorComponent_Template_input_change_19_listener($event) {\n        return ctx.onFileSelected($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(20, AddEditSectorComponent_img_20_Template, 1, 1, \"img\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(21, AddEditSectorComponent_div_21_Template, 2, 0, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"div\", 1);\n      i0.ɵɵelementStart(23, \"div\", 7);\n      i0.ɵɵelementStart(24, \"label\", 15);\n      i0.ɵɵtext(25, \"Sector Description\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(26, \"textarea\", 16);\n      i0.ɵɵtemplate(27, AddEditSectorComponent_div_27_Template, 2, 0, \"div\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(28, AddEditSectorComponent_div_28_Template, 4, 1, \"div\", 17);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(29, \"div\", 18);\n      i0.ɵɵtemplate(30, AddEditSectorComponent_button_30_Template, 2, 0, \"button\", 19);\n      i0.ɵɵelementStart(31, \"button\", 20);\n      i0.ɵɵtext(32, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      let tmp_3_0;\n      let tmp_6_0;\n      let tmp_8_0;\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Sector\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.addSectorForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.addSectorForm.get(\"IN_name\")) == null ? null : tmp_3_0.errors) && ((tmp_3_0 = ctx.addSectorForm.get(\"IN_name\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors.wordLimitExceeded));\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.imageSrc);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.addSectorForm.get(\"IN_dp\")) == null ? null : tmp_6_0.errors == null ? null : tmp_6_0.errors.fileSizeValidator);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.addSectorForm.get(\"IN_description\")) == null ? null : tmp_8_0.errors) && ((tmp_8_0 = ctx.addSectorForm.get(\"IN_description\")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors.wordLimitExceeded));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title == \"Add New\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n    }\n  },\n  directives: [i7.SidebarComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlName, i2.RequiredValidator, i8.NgIf, i1.RouterLink],\n  styles: [\".imageNameBox[_ngcontent-%COMP%] {\\n  position: absolute;\\n  transform: translateY(-50%);\\n  top: 50%;\\n  right: 80px;\\n  max-width: 400px;\\n  \\n  max-height: 36px;\\n  background-color: white;\\n  outline: none;\\n  border: none;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4uXFwuLlxcLi5cXFNlY3RvcnMlMjBhbmQlMjByb2xlc1xcQWxsJTIwU2VjdG9yc1xcYWRkLWVkaXQtc2VjdG9yXFxhZGQtZWRpdC1zZWN0b3IuY29tcG9uZW50LnNjc3MiLCJhZGQtZWRpdC1zZWN0b3IuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUU7RUFDRSxrQkFBQTtFQUNBLDJCQUFBO0VBQ0EsUUFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUFrQixtQ0FBQTtFQUNsQixnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtFQUNBLFlBQUE7QUNFSiIsImZpbGUiOiJhZGQtZWRpdC1zZWN0b3IuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIgIC5pbWFnZU5hbWVCb3h7XHJcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XHJcbiAgICB0b3A6IDUwJTtcclxuICAgIHJpZ2h0OiA4MHB4OyBcclxuICAgIG1heC13aWR0aDogNDAwcHg7IC8qIEFkanVzdCB0aGUgbWF4LXdpZHRoIGFzIG5lZWRlZCAqL1xyXG4gICAgbWF4LWhlaWdodDogMzZweDsgXHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcclxuICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICBib3JkZXI6IG5vbmU7XHJcbiAgfVxyXG5cclxuICBcclxuXHJcbiAiLCIuaW1hZ2VOYW1lQm94IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XG4gIHRvcDogNTAlO1xuICByaWdodDogODBweDtcbiAgbWF4LXdpZHRoOiA0MDBweDtcbiAgLyogQWRqdXN0IHRoZSBtYXgtd2lkdGggYXMgbmVlZGVkICovXG4gIG1heC1oZWlnaHQ6IDM2cHg7XG4gIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xuICBvdXRsaW5lOiBub25lO1xuICBib3JkZXI6IG5vbmU7XG59Il19 */\"]\n});", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/src/app/shared/sidebar/actions/Sectors and roles/All Sectors/add-edit-sector/add-edit-sector.component.ts"], "names": ["Validators", "FileValidator", "i0", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "i8", "AddEditSectorComponent_div_14_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "AddEditSectorComponent_img_20_Template", "ɵɵelement", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "imageSrc", "ɵɵsanitizeUrl", "AddEditSectorComponent_div_21_Template", "AddEditSectorComponent_div_27_Template", "AddEditSectorComponent_div_28_Template", "ctx_r4", "ɵɵadvance", "is<PERSON><PERSON><PERSON>ly", "AddEditSectorComponent_button_30_Template", "wordLimitValidator", "max<PERSON><PERSON>s", "control", "value", "words", "trim", "split", "length", "console", "log", "wordLimitExceeded", "wordCount", "AddEditSectorComponent", "constructor", "router", "formBuilder", "dataTransferService", "toastr", "activeRoute", "httpClient", "ngxSpinnerService", "_a", "p", "showForm", "title", "baseUrl", "state", "getCurrentNavigation", "extras", "sectorId", "sectorData", "sector", "navigate", "addSectorForm", "group", "IN_name", "required", "IN_dp", "IN_description", "IN_createdBy", "ngOnInit", "patchValue", "onFileSelected", "event", "selectedFile", "target", "files", "imageName", "newFileName", "addTimestamp", "name", "File", "type", "formControl", "get", "setValidators", "fileSizeValidator", "updateValueAndValidity", "fileType", "fileExtension", "pop", "toLowerCase", "info", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "uploadLogoUrl", "Promise", "resolve", "reject", "uploadurl", "subscribe", "res", "error", "insertSectorData", "IN_id", "fileUrl", "updateIndustryData", "to<PERSON>romise", "hide", "statusCode", "success", "status", "invalid", "data", "show", "insertIndustryData", "getAllSectorsData", "getIndustryData", "next", "ɵfac", "AddEditSectorComponent_Factory", "t", "ɵɵdirectiveInject", "Router", "FormBuilder", "DataTransferService", "ToastrService", "ActivatedRoute", "HttpClient", "NgxSpinnerService", "ɵcmp", "ɵɵdefineComponent", "selectors", "decls", "vars", "consts", "template", "AddEditSectorComponent_Template", "ɵɵlistener", "AddEditSectorComponent_Template_form_ngSubmit_8_listener", "ɵɵtemplate", "AddEditSectorComponent_Template_input_change_19_listener", "$event", "tmp_3_0", "tmp_6_0", "tmp_8_0", "ɵɵtextInterpolate1", "errors", "directives", "SidebarComponent", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "DefaultValueAccessor", "NgControlStatus", "FormControlName", "RequiredValidator", "NgIf", "RouterLink", "styles"], "mappings": ";AAAA,SAASA,UAAT,QAA4B,gBAA5B;AACA,SAASC,aAAT,QAA8B,mDAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+CAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,YAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,sBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+BAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;;AACA,SAASC,sCAAT,CAAgDC,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnEV,IAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAZ,IAAAA,EAAE,CAACa,MAAH,CAAU,CAAV,EAAa,0CAAb;AACAb,IAAAA,EAAE,CAACc,YAAH;AACH;AAAE;;AACH,SAASC,sCAAT,CAAgDL,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnEV,IAAAA,EAAE,CAACgB,SAAH,CAAa,CAAb,EAAgB,KAAhB,EAAuB,EAAvB;AACH;;AAAC,MAAIN,EAAE,GAAG,CAAT,EAAY;AACV,UAAMO,MAAM,GAAGjB,EAAE,CAACkB,aAAH,EAAf;AACAlB,IAAAA,EAAE,CAACmB,UAAH,CAAc,KAAd,EAAqBF,MAAM,CAACG,QAA5B,EAAsCpB,EAAE,CAACqB,aAAzC;AACH;AAAE;;AACH,SAASC,sCAAT,CAAgDZ,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnEV,IAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAZ,IAAAA,EAAE,CAACa,MAAH,CAAU,CAAV,EAAa,uEAAb;AACAb,IAAAA,EAAE,CAACc,YAAH;AACH;AAAE;;AACH,SAASS,sCAAT,CAAgDb,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnEV,IAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAZ,IAAAA,EAAE,CAACa,MAAH,CAAU,CAAV,EAAa,0CAAb;AACAb,IAAAA,EAAE,CAACc,YAAH;AACH;AAAE;;AACH,SAASU,sCAAT,CAAgDd,EAAhD,EAAoDC,GAApD,EAAyD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnEV,IAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,IAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAZ,IAAAA,EAAE,CAACa,MAAH,CAAU,CAAV,EAAa,cAAb;AACAb,IAAAA,EAAE,CAACc,YAAH;AACAd,IAAAA,EAAE,CAACgB,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAhB,IAAAA,EAAE,CAACc,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMe,MAAM,GAAGzB,EAAE,CAACkB,aAAH,EAAf;AACAlB,IAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,IAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0BM,MAAM,CAACE,UAAjC;AACH;AAAE;;AACH,SAASC,yCAAT,CAAmDlB,EAAnD,EAAuDC,GAAvD,EAA4D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACtEV,IAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAZ,IAAAA,EAAE,CAACa,MAAH,CAAU,CAAV,EAAa,MAAb;AACAb,IAAAA,EAAE,CAACc,YAAH;AACH;AAAE;;AACH,SAASe,kBAAT,CAA4BC,QAA5B,EAAsC;AAClC,SAAQC,OAAD,IAAa;AAChB,QAAIA,OAAO,CAACC,KAAZ,EAAmB;AACf,YAAMC,KAAK,GAAGF,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACC,KAAR,CAAcE,IAAd,GAAqBC,KAArB,CAA2B,KAA3B,CAAhE;;AACA,UAAIF,KAAK,CAACG,MAAN,GAAeN,QAAnB,EAA6B;AACzBO,QAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwBR,QAAxB;AACA,eAAO;AAAES,UAAAA,iBAAiB,EAAE,IAArB;AAA2BC,UAAAA,SAAS,EAAEP,KAAK,CAACG;AAA5C,SAAP;AACH;AACJ;;AACD,WAAO,IAAP;AACH,GATD;AAUH;;AACD,OAAO,MAAMK,sBAAN,CAA6B;AAChCC,EAAAA,WAAW,CAACC,MAAD,EAASC,WAAT,EAAsBC,mBAAtB,EAA2CC,MAA3C,EAAmDC,WAAnD,EAAgEC,UAAhE,EAA4EC,iBAA5E,EAA+F;AACtG,QAAIC,EAAJ;;AACA,SAAKP,MAAL,GAAcA,MAAd;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,mBAAL,GAA2BA,mBAA3B;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKE,CAAL,GAAS,CAAT;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKC,KAAL,GAAa,SAAb;AACA,SAAK1B,UAAL,GAAkB,KAAlB;AACA,SAAK2B,OAAL,GAAe,4CAAf;AACA,UAAMC,KAAK,GAAG,CAACL,EAAE,GAAG,KAAKP,MAAL,CAAYa,oBAAZ,EAAN,MAA8C,IAA9C,IAAsDN,EAAE,KAAK,KAAK,CAAlE,GAAsE,KAAK,CAA3E,GAA+EA,EAAE,CAACO,MAAH,CAAUF,KAAvG;;AACA,QAAIA,KAAJ,EAAW;AACN,WAAKG,QAAL,GAAgBH,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACG,QAArE;AACC,WAAKL,KAAL,GAAaE,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACF,KAAlE;AACC,WAAKM,UAAL,GAAkBJ,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACK,MAAvE;AACAvB,MAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2B,KAAKqB,UAAhC;AACH,KALD,MAMK;AACD,WAAKhB,MAAL,CAAYkB,QAAZ,CAAqB,CAAE,iBAAF,CAArB;AACH;;AACD,SAAKC,aAAL,GAAqB,KAAKlB,WAAL,CAAiBmB,KAAjB,CAAuB;AACxCC,MAAAA,OAAO,EAAE,CAAC,EAAD,EAAK,CAAClE,UAAU,CAACmE,QAAZ,EAAsBpC,kBAAkB,CAAC,EAAD,CAAxC,CAAL,CAD+B;AAExCqC,MAAAA,KAAK,EAAE,CAAC,IAAD,EAAOpE,UAAU,CAACmE,QAAlB,CAFiC;AAGxCE,MAAAA,cAAc,EAAE,CAAC,EAAD,EAAK,CAACrE,UAAU,CAACmE,QAAZ,EAAsBpC,kBAAkB,CAAC,GAAD,CAAxC,CAAL,CAHwB;AAIxCuC,MAAAA,YAAY,EAAE,CAAC,EAAD,EAAKtE,UAAU,CAACmE,QAAhB,CAJ0B,CAIA;;AAJA,KAAvB,CAArB;AAMH;;AACDI,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKhB,KAAL,IAAc,MAAlB,EAA0B;AACtB,WAAKS,aAAL,CAAmBQ,UAAnB,CAA8B;AAC1BN,QAAAA,OAAO,EAAE,KAAKL,UAAL,CAAgBK,OADC;AAE1B;AACAG,QAAAA,cAAc,EAAE,KAAKR,UAAL,CAAgBQ,cAHN;AAI1BC,QAAAA,YAAY,EAAE,KAAKT,UAAL,CAAgBS;AAJJ,OAA9B;AAMH;AACJ;;AACDG,EAAAA,cAAc,CAACC,KAAD,EAAQ;AAClB,QAAItB,EAAJ;;AACA,QAAIuB,YAAY,GAAGD,KAAK,CAACE,MAAN,CAAaC,KAAb,CAAmB,CAAnB,CAAnB;;AACA,QAAIH,KAAK,CAACE,MAAN,CAAaC,KAAb,CAAmBvC,MAAnB,KAA8B,CAAlC,EAAqC;AACjC;AACA,WAAKwC,SAAL,GAAiB,IAAjB;AACA,WAAKxD,QAAL,GAAgB,IAAhB;AACA;AACH;;AACD,UAAMyD,WAAW,GAAG9E,aAAa,CAAC+E,YAAd,CAA2BL,YAAY,CAACM,IAAxC,CAApB;AACA,SAAKH,SAAL,GAAiB,IAAII,IAAJ,CAAS,CAACP,YAAD,CAAT,EAAyBI,WAAzB,EAAsC;AAAEI,MAAAA,IAAI,EAAER,YAAY,CAACQ;AAArB,KAAtC,CAAjB;;AACA,QAAI,KAAKL,SAAT,EAAoB;AAChB,YAAMM,WAAW,GAAG,KAAKpB,aAAL,CAAmBqB,GAAnB,CAAuB,OAAvB,CAApB;AACAD,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACE,aAAZ,CAA0BrF,aAAa,CAACsF,iBAAd,CAAgC,IAAhC,EAAsC,KAAKT,SAA3C,CAA1B,CAA1D;AACAM,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACI,sBAAZ,EAA1D;AACH;;AACD,UAAMC,QAAQ,GAAG,KAAKX,SAAL,CAAeK,IAAf,CAAoB9C,KAApB,CAA0B,GAA1B,EAA+B,CAA/B,CAAjB;AACA,UAAMqD,aAAa,GAAG,CAACtC,EAAE,GAAG,KAAK0B,SAAL,CAAeG,IAAf,CAAoB5C,KAApB,CAA0B,GAA1B,EAA+BsD,GAA/B,EAAN,MAAgD,IAAhD,IAAwDvC,EAAE,KAAK,KAAK,CAApE,GAAwE,KAAK,CAA7E,GAAiFA,EAAE,CAACwC,WAAH,EAAvG;;AACA,QAAIH,QAAQ,KAAK,OAAb,IAAwBC,aAAa,KAAK,KAA9C,EAAqD;AACjDhB,MAAAA,KAAK,CAACE,MAAN,CAAa1C,KAAb,GAAqB,EAArB;AACA,WAAKc,MAAL,CAAY6C,IAAZ,CAAiB,8CAAjB;AACA,WAAKf,SAAL,GAAiB,IAAjB;AACA,WAAKxD,QAAL,GAAgB,IAAhB;AACA;AACH;;AACD,QAAI,KAAKwD,SAAL,IAAkBW,QAAQ,IAAI,OAAlC,EAA2C;AACvC,YAAMK,MAAM,GAAG,IAAIC,UAAJ,EAAf;;AACAD,MAAAA,MAAM,CAACE,MAAP,GAAiBC,CAAD,IAAO;AACnB,YAAI7C,EAAJ;;AACA,aAAK9B,QAAL,GAAgB,CAAC8B,EAAE,GAAG6C,CAAC,CAACrB,MAAR,MAAoB,IAApB,IAA4BxB,EAAE,KAAK,KAAK,CAAxC,GAA4C,KAAK,CAAjD,GAAqDA,EAAE,CAAC8C,MAAxE;AACH,OAHD;;AAIAJ,MAAAA,MAAM,CAACK,aAAP,CAAqB,KAAKrB,SAA1B;AACH,KAPD,MAQK;AACD,WAAKxD,QAAL,GAAgB,IAAhB,CADC,CACqB;AACzB;;AACDiB,IAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAKsC,SAA9B;AACH;;AACDsB,EAAAA,aAAa,GAAG;AACZ,WAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,UAAI,CAAC,KAAKzB,SAAV,EAAqB;AACjB,eAAOyB,MAAM,CAAC,mBAAD,CAAb;AACH;;AACDhE,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqB,KAAKsC,SAA1B;AACA,WAAK/B,mBAAL,CAAyByD,SAAzB,CAAmC,KAAK1B,SAAxC,EAAmD2B,SAAnD,CAA8DC,GAAD,IAAS;AAClEJ,QAAAA,OAAO,CAACI,GAAD,CAAP;AACH,OAFD,EAEIC,KAAD,IAAW;AACVJ,QAAAA,MAAM,CAACI,KAAD,CAAN,CADU,CACK;AAClB,OAJD;AAKH,KAVM,CAAP;AAWH;;AACKC,EAAAA,gBAAgB,GAAG;AAAA;;AAAA;AACrB,UAAI;AACA,YAAI,KAAI,CAACrD,KAAL,IAAc,MAAlB,EAA0B;AACtB,UAAA,KAAI,CAACS,aAAL,CAAmB9B,KAAnB,CAAyB2E,KAAzB,GAAiC,KAAI,CAACjD,QAAtC;;AACA,cAAI,KAAI,CAACkB,SAAT,EAAoB;AAChB,kBAAM,KAAI,CAACsB,aAAL,EAAN;AACA,kBAAMU,OAAO,GAAG,KAAI,CAACtD,OAAL,GAAe,KAAI,CAACsB,SAAL,CAAeG,IAA9C;AACA,YAAA,KAAI,CAACjB,aAAL,CAAmB9B,KAAnB,CAAyBkC,KAAzB,GAAiC0C,OAAjC;AACH,WAJD,MAKK;AACD,YAAA,KAAI,CAAC9C,aAAL,CAAmB9B,KAAnB,CAAyBkC,KAAzB,GAAiC,KAAI,CAACP,UAAL,CAAgBO,KAAjD;AACH;;AACD7B,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgC,KAAI,CAACwB,aAAL,CAAmB9B,KAAnD;AACA,gBAAMwE,GAAG,SAAS,KAAI,CAAC3D,mBAAL,CAAyBgE,kBAAzB,CAA4C,KAAI,CAAC/C,aAAL,CAAmB9B,KAA/D,EAAsE8E,SAAtE,EAAlB;;AACA,UAAA,KAAI,CAAC7D,iBAAL,CAAuB8D,IAAvB,CAA4B,eAA5B;;AACA,cAAIP,GAAG,CAACQ,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,YAAA,KAAI,CAAClE,MAAL,CAAYmE,OAAZ,CAAoB,8BAApB;;AACA,YAAA,KAAI,CAACtE,MAAL,CAAYkB,QAAZ,CAAqB,CAAE,iBAAF,CAArB;AACH,WAHD,MAIK;AACD,YAAA,KAAI,CAACf,MAAL,CAAY2D,KAAZ,CAAkB,uBAAlB;;AACApE,YAAAA,OAAO,CAACoE,KAAR,CAAc,6BAAd,EAA6CD,GAAG,CAACU,MAAjD;AACH;AACJ,SArBD,MAsBK;AACD,cAAI,KAAI,CAACpD,aAAL,CAAmBqD,OAAvB,EAAgC;AAC5B,YAAA,KAAI,CAACrE,MAAL,CAAY6C,IAAZ,CAAiB,EAAjB,EAAqB,iCAArB;;AACA;AACH,WAHD,MAIK;AACD,kBAAM,KAAI,CAACO,aAAL,EAAN;AACA,kBAAMU,OAAO,GAAG,KAAI,CAACtD,OAAL,GAAe,KAAI,CAACsB,SAAL,CAAeG,IAA9C;AACA,kBAAMqC,IAAI,GAAG;AACTpD,cAAAA,OAAO,EAAE,KAAI,CAACF,aAAL,CAAmB9B,KAAnB,CAAyBgC,OADzB;AAETG,cAAAA,cAAc,EAAE,KAAI,CAACL,aAAL,CAAmB9B,KAAnB,CAAyBmC,cAFhC;AAGTC,cAAAA,YAAY,EAAE,KAAI,CAACN,aAAL,CAAmB9B,KAAnB,CAAyBoC,YAH9B;AAITF,cAAAA,KAAK,EAAE0C;AAJE,aAAb;AAMAvE,YAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B8E,IAA/B;;AACA,YAAA,KAAI,CAACnE,iBAAL,CAAuBoE,IAAvB,CAA4B,eAA5B;;AACA,kBAAMb,GAAG,SAAS,KAAI,CAAC3D,mBAAL,CAAyByE,kBAAzB,CAA4CF,IAA5C,EAAkDN,SAAlD,EAAlB;;AACA,YAAA,KAAI,CAAC7D,iBAAL,CAAuB8D,IAAvB,CAA4B,eAA5B;;AACA,gBAAIP,GAAG,CAACQ,UAAJ,IAAkB,GAAtB,EAA2B;AACvB3E,cAAAA,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2CkE,GAA3C;;AACA,cAAA,KAAI,CAAC1D,MAAL,CAAYmE,OAAZ,CAAoB,4BAApB;;AACA,cAAA,KAAI,CAACM,iBAAL;;AACA,cAAA,KAAI,CAAC5E,MAAL,CAAYkB,QAAZ,CAAqB,CAAC,kBAAD,CAArB;AACH,aALD,MAMK;AACD,cAAA,KAAI,CAACf,MAAL,CAAY2D,KAAZ,CAAkB,EAAlB,EAAsB,sBAAtB;AACH;AACJ;AACJ;AACJ,OApDD,CAqDA,OAAOA,KAAP,EAAc;AACV,QAAA,KAAI,CAACxD,iBAAL,CAAuB8D,IAAvB,CAA4B,eAA5B;;AACA1E,QAAAA,OAAO,CAACoE,KAAR,CAAc,QAAd,EAAwBA,KAAxB;;AACA,QAAA,KAAI,CAAC3D,MAAL,CAAY2D,KAAZ,CAAkB,uBAAlB;AACH;AA1DoB;AA2DxB;;AACDc,EAAAA,iBAAiB,GAAG;AAChB,SAAK1E,mBAAL,CAAyB2E,eAAzB,GAA2CjB,SAA3C,CAAqD;AACjDkB,MAAAA,IAAI,EAAGjB,GAAD,IAAS;AACX,YAAIA,GAAG,CAACU,MAAJ,KAAe,GAAnB,EAAwB,CACvB,CADD,MAEK;AACD7E,UAAAA,OAAO,CAACoE,KAAR,CAAc,+BAAd,EAA+CD,GAAG,CAACU,MAAnD;AACH;AACJ;AAPgD,KAArD;AASH;;AAnK+B;;AAqKpCzE,sBAAsB,CAACiF,IAAvB,GAA8B,SAASC,8BAAT,CAAwCC,CAAxC,EAA2C;AAAE,SAAO,KAAKA,CAAC,IAAInF,sBAAV,EAAkCzC,EAAE,CAAC6H,iBAAH,CAAqB5H,EAAE,CAAC6H,MAAxB,CAAlC,EAAmE9H,EAAE,CAAC6H,iBAAH,CAAqB3H,EAAE,CAAC6H,WAAxB,CAAnE,EAAyG/H,EAAE,CAAC6H,iBAAH,CAAqB1H,EAAE,CAAC6H,mBAAxB,CAAzG,EAAuJhI,EAAE,CAAC6H,iBAAH,CAAqBzH,EAAE,CAAC6H,aAAxB,CAAvJ,EAA+LjI,EAAE,CAAC6H,iBAAH,CAAqB5H,EAAE,CAACiI,cAAxB,CAA/L,EAAwOlI,EAAE,CAAC6H,iBAAH,CAAqBxH,EAAE,CAAC8H,UAAxB,CAAxO,EAA6QnI,EAAE,CAAC6H,iBAAH,CAAqBvH,EAAE,CAAC8H,iBAAxB,CAA7Q,CAAP;AAAkU,CAA7Y;;AACA3F,sBAAsB,CAAC4F,IAAvB,GAA8B,aAAcrI,EAAE,CAACsI,iBAAH,CAAqB;AAAErD,EAAAA,IAAI,EAAExC,sBAAR;AAAgC8F,EAAAA,SAAS,EAAE,CAAC,CAAC,qBAAD,CAAD,CAA3C;AAAsEC,EAAAA,KAAK,EAAE,EAA7E;AAAiFC,EAAAA,IAAI,EAAE,EAAvF;AAA2FC,EAAAA,MAAM,EAAE,CAAC,CAAC,CAAD,EAAI,iBAAJ,CAAD,EAAyB,CAAC,CAAD,EAAI,KAAJ,CAAzB,EAAqC,CAAC,CAAD,EAAI,QAAJ,EAAc,aAAd,EAA6B,cAA7B,CAArC,EAAmF,CAAC,CAAD,EAAI,MAAJ,CAAnF,EAAgG,CAAC,CAAD,EAAI,aAAJ,EAAmB,YAAnB,EAAiC,YAAjC,EAA+C,aAA/C,EAA8D,YAA9D,EAA4E,aAA5E,EAA2F,MAA3F,CAAhG,EAAoM,CAAC,CAAD,EAAI,WAAJ,CAApM,EAAsN,CAAC,CAAD,EAAI,cAAJ,EAAoB,CAApB,EAAuB,WAAvB,EAAoC,UAApC,CAAtN,EAAuQ,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAAvQ,EAAsS,CAAC,KAAD,EAAQ,SAAR,EAAmB,CAAnB,EAAsB,gBAAtB,CAAtS,EAA+U,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,SAApC,EAA+C,UAA/C,EAA2D,EAA3D,EAA+D,aAA/D,EAA8E,mBAA9E,EAAmG,CAAnG,EAAsG,cAAtG,EAAsH,iBAAtH,EAAyI,CAAzI,EAA4I,UAA5I,CAA/U,EAAwe,CAAC,OAAD,EAAU,SAAV,EAAqB,CAArB,EAAwB,MAAxB,CAAxe,EAAygB,CAAC,KAAD,EAAQ,OAAR,EAAiB,CAAjB,EAAoB,gBAApB,CAAzgB,EAAgjB,CAAC,CAAD,EAAI,sBAAJ,CAAhjB,EAA6kB,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,OAApC,EAA6C,UAA7C,EAAyD,EAAzD,EAA6D,QAA7D,EAAuE,SAAvE,EAAkF,CAAlF,EAAqF,cAArF,EAAqG,iBAArG,EAAwH,CAAxH,EAA2H,UAA3H,EAAuI,QAAvI,CAA7kB,EAA+tB,CAAC,KAAD,EAAQ,cAAR,EAAwB,OAAxB,EAAiC,aAAjC,EAAgD,CAAhD,EAAmD,KAAnD,EAA0D,CAA1D,EAA6D,MAA7D,CAA/tB,EAAqyB,CAAC,KAAD,EAAQ,gBAAR,EAA0B,CAA1B,EAA6B,gBAA7B,CAAryB,EAAq1B,CAAC,iBAAD,EAAoB,gBAApB,EAAsC,UAAtC,EAAkD,EAAlD,EAAsD,MAAtD,EAA8D,GAA9D,EAAmE,MAAnE,EAA2E,IAA3E,EAAiF,aAAjF,EAAgG,0BAAhG,EAA4H,CAA5H,EAA+H,cAA/H,EAA+I,iBAA/I,EAAkK,CAAlK,EAAqK,UAArK,CAAr1B,EAAugC,CAAC,OAAD,EAAU,qBAAV,EAAiC,CAAjC,EAAoC,MAApC,CAAvgC,EAAojC,CAAC,CAAD,EAAI,aAAJ,CAApjC,EAAwkC,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,sBAA5B,EAAoD,CAApD,EAAuD,MAAvD,CAAxkC,EAAwoC,CAAC,YAAD,EAAe,kBAAf,EAAmC,CAAnC,EAAsC,KAAtC,EAA6C,WAA7C,CAAxoC,EAAmsC,CAAC,CAAD,EAAI,SAAJ,CAAnsC,EAAmtC,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,aAA3B,EAA0C,CAA1C,EAA6C,KAA7C,CAAntC,EAAwwC,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,gBAA3B,CAAxwC,EAAszC,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,cAApC,EAAoD,UAApD,EAAgE,EAAhE,EAAoE,aAApE,EAAmF,oBAAnF,EAAyG,CAAzG,EAA4G,cAA5G,EAA4H,iBAA5H,EAA+I,CAA/I,EAAkJ,UAAlJ,CAAtzC,EAAq9C,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,MAA5C,CAAr9C,CAAnG;AAA8mDC,EAAAA,QAAQ,EAAE,SAASC,+BAAT,CAAyClI,EAAzC,EAA6CC,GAA7C,EAAkD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AACjvDV,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,aAArB;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,CAAV;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,CAA7B;AACAZ,MAAAA,EAAE,CAAC6I,UAAH,CAAc,UAAd,EAA0B,SAASC,wDAAT,GAAoE;AAAE,eAAOnI,GAAG,CAAC+F,gBAAJ,EAAP;AAAgC,OAAhI;AACA1G,MAAAA,EAAE,CAACY,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,aAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACgB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,CAA1B;AACAhB,MAAAA,EAAE,CAAC+I,UAAH,CAAc,EAAd,EAAkBtI,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACAT,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,cAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAZ,MAAAA,EAAE,CAAC6I,UAAH,CAAc,QAAd,EAAwB,SAASG,wDAAT,CAAkEC,MAAlE,EAA0E;AAAE,eAAOtI,GAAG,CAAC4D,cAAJ,CAAmB0E,MAAnB,CAAP;AAAoC,OAAxI;AACAjJ,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAAC+I,UAAH,CAAc,EAAd,EAAkBhI,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACAf,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAAC+I,UAAH,CAAc,EAAd,EAAkBzH,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACAtB,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAZ,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,oBAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACgB,SAAH,CAAa,EAAb,EAAiB,UAAjB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAAC+I,UAAH,CAAc,EAAd,EAAkBxH,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACAvB,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAAC+I,UAAH,CAAc,EAAd,EAAkBvH,sCAAlB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACAxB,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAZ,MAAAA,EAAE,CAAC+I,UAAH,CAAc,EAAd,EAAkBnH,yCAAlB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,QAAnE,EAA6E,EAA7E;AACA5B,MAAAA,EAAE,CAACY,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAZ,MAAAA,EAAE,CAACa,MAAH,CAAU,EAAV,EAAc,QAAd;AACAb,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACAd,MAAAA,EAAE,CAACc,YAAH;AACH;;AAAC,QAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAIwI,OAAJ;AACA,UAAIC,OAAJ;AACA,UAAIC,OAAJ;AACApJ,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACqJ,kBAAH,CAAsB,EAAtB,EAA0B1I,GAAG,CAAC0C,KAA9B,EAAqC,SAArC;AACArD,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,WAAd,EAA2BR,GAAG,CAACmD,aAA/B;AACA9D,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0BR,GAAG,CAACgB,UAA9B;AACA3B,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsB,CAAC,CAAC+H,OAAO,GAAGvI,GAAG,CAACmD,aAAJ,CAAkBqB,GAAlB,CAAsB,SAAtB,CAAX,KAAgD,IAAhD,GAAuD,IAAvD,GAA8D+D,OAAO,CAACI,MAAvE,MAAmF,CAACJ,OAAO,GAAGvI,GAAG,CAACmD,aAAJ,CAAkBqB,GAAlB,CAAsB,SAAtB,CAAX,KAAgD,IAAhD,GAAuD,IAAvD,GAA8D+D,OAAO,CAACI,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCJ,OAAO,CAACI,MAAR,CAAe/G,iBAAhM,CAAtB;AACAvC,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0BR,GAAG,CAACgB,UAA9B;AACA3B,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsBR,GAAG,CAACS,QAA1B;AACApB,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsB,CAACgI,OAAO,GAAGxI,GAAG,CAACmD,aAAJ,CAAkBqB,GAAlB,CAAsB,OAAtB,CAAX,KAA8C,IAA9C,GAAqD,IAArD,GAA4DgE,OAAO,CAACG,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCH,OAAO,CAACG,MAAR,CAAejE,iBAAjI;AACArF,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0BR,GAAG,CAACgB,UAA9B;AACA3B,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsB,CAAC,CAACiI,OAAO,GAAGzI,GAAG,CAACmD,aAAJ,CAAkBqB,GAAlB,CAAsB,gBAAtB,CAAX,KAAuD,IAAvD,GAA8D,IAA9D,GAAqEiE,OAAO,CAACE,MAA9E,MAA0F,CAACF,OAAO,GAAGzI,GAAG,CAACmD,aAAJ,CAAkBqB,GAAlB,CAAsB,gBAAtB,CAAX,KAAuD,IAAvD,GAA8D,IAA9D,GAAqEiE,OAAO,CAACE,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCF,OAAO,CAACE,MAAR,CAAe/G,iBAA9M,CAAtB;AACAvC,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsBR,GAAG,CAAC0C,KAAJ,IAAa,SAAnC;AACArD,MAAAA,EAAE,CAAC0B,SAAH,CAAa,CAAb;AACA1B,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsB,CAACR,GAAG,CAACgB,UAA3B;AACH;AAAE,GAlF0D;AAkFxD4H,EAAAA,UAAU,EAAE,CAAChJ,EAAE,CAACiJ,gBAAJ,EAAsBtJ,EAAE,CAACuJ,aAAzB,EAAwCvJ,EAAE,CAACwJ,oBAA3C,EAAiExJ,EAAE,CAACyJ,kBAApE,EAAwFzJ,EAAE,CAAC0J,oBAA3F,EAAiH1J,EAAE,CAAC2J,eAApH,EAAqI3J,EAAE,CAAC4J,eAAxI,EAAyJ5J,EAAE,CAAC6J,iBAA5J,EAA+KvJ,EAAE,CAACwJ,IAAlL,EAAwL/J,EAAE,CAACgK,UAA3L,CAlF4C;AAkF4JC,EAAAA,MAAM,EAAE,CAAC,u+CAAD;AAlFpK,CAArB,CAA5C", "sourcesContent": ["import { Validators, } from '@angular/forms';\r\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"@angular/router\";\r\nimport * as i2 from \"@angular/forms\";\r\nimport * as i3 from \"src/app/shared/services/data-transfer.service\";\r\nimport * as i4 from \"ngx-toastr\";\r\nimport * as i5 from \"@angular/common/http\";\r\nimport * as i6 from \"ngx-spinner\";\r\nimport * as i7 from \"../../../../sidebar.component\";\r\nimport * as i8 from \"@angular/common\";\r\nfunction AddEditSectorComponent_div_14_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 21);\r\n    i0.ɵɵtext(1, \" Error: Word Limit Exceeded! (20 Words) \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditSectorComponent_img_20_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"img\", 22);\r\n} if (rf & 2) {\r\n    const ctx_r1 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"src\", ctx_r1.imageSrc, i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction AddEditSectorComponent_div_21_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 21);\r\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditSectorComponent_div_27_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 21);\r\n    i0.ɵɵtext(1, \" Error: Word Limit exceeded!(200 Words) \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditSectorComponent_div_28_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 7);\r\n    i0.ɵɵelementStart(1, \"label\", 23);\r\n    i0.ɵɵtext(2, \"Creator Name\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(3, \"input\", 24);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r4 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"readonly\", ctx_r4.isReadonly);\r\n} }\r\nfunction AddEditSectorComponent_button_30_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"button\", 25);\r\n    i0.ɵɵtext(1, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction wordLimitValidator(maxWords) {\r\n    return (control) => {\r\n        if (control.value) {\r\n            const words = control === null || control === void 0 ? void 0 : control.value.trim().split(/\\s+/);\r\n            if (words.length > maxWords) {\r\n                console.log('maxwords', maxWords);\r\n                return { wordLimitExceeded: true, wordCount: words.length };\r\n            }\r\n        }\r\n        return null;\r\n    };\r\n}\r\nexport class AddEditSectorComponent {\r\n    constructor(router, formBuilder, dataTransferService, toastr, activeRoute, httpClient, ngxSpinnerService) {\r\n        var _a;\r\n        this.router = router;\r\n        this.formBuilder = formBuilder;\r\n        this.dataTransferService = dataTransferService;\r\n        this.toastr = toastr;\r\n        this.activeRoute = activeRoute;\r\n        this.httpClient = httpClient;\r\n        this.ngxSpinnerService = ngxSpinnerService;\r\n        this.p = 1;\r\n        this.showForm = false;\r\n        this.title = 'Add New';\r\n        this.isReadonly = false;\r\n        this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\r\n        const state = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras.state;\r\n        if (state) {\r\n            (this.sectorId = state === null || state === void 0 ? void 0 : state.sectorId);\r\n            (this.title = state === null || state === void 0 ? void 0 : state.title);\r\n            (this.sectorData = state === null || state === void 0 ? void 0 : state.sector);\r\n            console.log(\"Sector Data\", this.sectorData);\r\n        }\r\n        else {\r\n            this.router.navigate([`actions/sectors`]);\r\n        }\r\n        this.addSectorForm = this.formBuilder.group({\r\n            IN_name: ['', [Validators.required, wordLimitValidator(20)]],\r\n            IN_dp: [null, Validators.required],\r\n            IN_description: ['', [Validators.required, wordLimitValidator(200)]],\r\n            IN_createdBy: ['', Validators.required] // Assuming this is required as per your form\r\n        });\r\n    }\r\n    ngOnInit() {\r\n        if (this.title == 'Edit') {\r\n            this.addSectorForm.patchValue({\r\n                IN_name: this.sectorData.IN_name,\r\n                // IN_dp: this.sectorData.IN_dp,\r\n                IN_description: this.sectorData.IN_description,\r\n                IN_createdBy: this.sectorData.IN_createdBy,\r\n            });\r\n        }\r\n    }\r\n    onFileSelected(event) {\r\n        var _a;\r\n        let selectedFile = event.target.files[0];\r\n        if (event.target.files.length === 0) {\r\n            // Reset both imageName and imageSrc when no file is selected\r\n            this.imageName = null;\r\n            this.imageSrc = null;\r\n            return;\r\n        }\r\n        const newFileName = FileValidator.addTimestamp(selectedFile.name);\r\n        this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });\r\n        if (this.imageName) {\r\n            const formControl = this.addSectorForm.get('IN_dp');\r\n            formControl === null || formControl === void 0 ? void 0 : formControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\r\n            formControl === null || formControl === void 0 ? void 0 : formControl.updateValueAndValidity();\r\n        }\r\n        const fileType = this.imageName.type.split('/')[0];\r\n        const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\r\n        if (fileType !== 'image' || fileExtension === 'svg') {\r\n            event.target.value = '';\r\n            this.toastr.info('Please select an image file (excluding SVG).');\r\n            this.imageName = null;\r\n            this.imageSrc = null;\r\n            return;\r\n        }\r\n        if (this.imageName && fileType == 'image') {\r\n            const reader = new FileReader();\r\n            reader.onload = (e) => {\r\n                var _a;\r\n                this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\r\n            };\r\n            reader.readAsDataURL(this.imageName);\r\n        }\r\n        else {\r\n            this.imageSrc = null; // Reset imageSrc if no file selected\r\n        }\r\n        console.log('imageName', this.imageName);\r\n    }\r\n    uploadLogoUrl() {\r\n        return new Promise((resolve, reject) => {\r\n            if (!this.imageName) {\r\n                return reject(\"Image Not Provide\");\r\n            }\r\n            console.log('image', this.imageName);\r\n            this.dataTransferService.uploadurl(this.imageName).subscribe((res) => {\r\n                resolve(res);\r\n            }, (error) => {\r\n                reject(error); // Reject the promise with the error\r\n            });\r\n        });\r\n    }\r\n    async insertSectorData() {\r\n        try {\r\n            if (this.title == 'Edit') {\r\n                this.addSectorForm.value.IN_id = this.sectorId;\r\n                if (this.imageName) {\r\n                    await this.uploadLogoUrl();\r\n                    const fileUrl = this.baseUrl + this.imageName.name;\r\n                    this.addSectorForm.value.IN_dp = fileUrl;\r\n                }\r\n                else {\r\n                    this.addSectorForm.value.IN_dp = this.sectorData.IN_dp;\r\n                }\r\n                console.log(\"Data for edit : \", this.addSectorForm.value);\r\n                const res = await this.dataTransferService.updateIndustryData(this.addSectorForm.value).toPromise();\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                if (res.statusCode == 200) {\r\n                    this.toastr.success(\"Sector updated successfully.\");\r\n                    this.router.navigate([`actions/sectors`]);\r\n                }\r\n                else {\r\n                    this.toastr.error(\"Something went wrong.\");\r\n                    console.error('Unable to add role. Status:', res.status);\r\n                }\r\n            }\r\n            else {\r\n                if (this.addSectorForm.invalid) {\r\n                    this.toastr.info('', 'Please fill all required fields');\r\n                    return;\r\n                }\r\n                else {\r\n                    await this.uploadLogoUrl();\r\n                    const fileUrl = this.baseUrl + this.imageName.name;\r\n                    const data = {\r\n                        IN_name: this.addSectorForm.value.IN_name,\r\n                        IN_description: this.addSectorForm.value.IN_description,\r\n                        IN_createdBy: this.addSectorForm.value.IN_createdBy,\r\n                        IN_dp: fileUrl,\r\n                    };\r\n                    console.log('ADD SECTOR DATA', data);\r\n                    this.ngxSpinnerService.show('globalSpinner');\r\n                    const res = await this.dataTransferService.insertIndustryData(data).toPromise();\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    if (res.statusCode == 200) {\r\n                        console.log('Sector posted successfully:', res);\r\n                        this.toastr.success('Sector added successfully.');\r\n                        this.getAllSectorsData();\r\n                        this.router.navigate(['/actions/sectors']);\r\n                    }\r\n                    else {\r\n                        this.toastr.error('', 'Something Went Wrong');\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        catch (error) {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            console.error('Error:', error);\r\n            this.toastr.error('Something went wrong.');\r\n        }\r\n    }\r\n    getAllSectorsData() {\r\n        this.dataTransferService.getIndustryData().subscribe({\r\n            next: (res) => {\r\n                if (res.status === 200) {\r\n                }\r\n                else {\r\n                    console.error('Failed to fetch role. Status:', res.status);\r\n                }\r\n            }\r\n        });\r\n    }\r\n}\r\nAddEditSectorComponent.ɵfac = function AddEditSectorComponent_Factory(t) { return new (t || AddEditSectorComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.DataTransferService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.NgxSpinnerService)); };\r\nAddEditSectorComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: AddEditSectorComponent, selectors: [[\"app-add-edit-sector\"]], decls: 33, vars: 11, consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\", \"col-lg-6\"], [\"for\", \"IN_name\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"IN_name\", \"required\", \"\", \"placeholder\", \"Enter Sector Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"for\", \"IN_dp\", 1, \"required-field\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"IN_dp\", \"required\", \"\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Sector Image\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"for\", \"IN_description\", 1, \"required-field\"], [\"formControlName\", \"IN_description\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Enter Sector Description\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"class\", \"form-group col-lg-6\", 4, \"ngIf\"], [1, \"text-center\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [\"routerLink\", \"/actions/sectors\", 1, \"btn\", \"btn-light\"], [1, \"warning\"], [\"alt\", \"Sector Image\", 1, \"img-preview\", 3, \"src\"], [\"for\", \"IN_createdBy\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"IN_createdBy\", \"required\", \"\", \"placeholder\", \"Enter Creator Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]], template: function AddEditSectorComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵelementStart(0, \"app-sidebar\");\r\n        i0.ɵɵelementStart(1, \"div\", 0);\r\n        i0.ɵɵelementStart(2, \"div\", 1);\r\n        i0.ɵɵelementStart(3, \"div\", 2);\r\n        i0.ɵɵelementStart(4, \"div\", 3);\r\n        i0.ɵɵelementStart(5, \"div\", 4);\r\n        i0.ɵɵtext(6);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(7, \"div\", 5);\r\n        i0.ɵɵelementStart(8, \"form\", 6);\r\n        i0.ɵɵlistener(\"ngSubmit\", function AddEditSectorComponent_Template_form_ngSubmit_8_listener() { return ctx.insertSectorData(); });\r\n        i0.ɵɵelementStart(9, \"div\", 1);\r\n        i0.ɵɵelementStart(10, \"div\", 7);\r\n        i0.ɵɵelementStart(11, \"label\", 8);\r\n        i0.ɵɵtext(12, \"Sector Name\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(13, \"input\", 9);\r\n        i0.ɵɵtemplate(14, AddEditSectorComponent_div_14_Template, 2, 0, \"div\", 10);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(15, \"div\", 7);\r\n        i0.ɵɵelementStart(16, \"label\", 11);\r\n        i0.ɵɵtext(17, \"Sector Image\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(18, \"div\", 12);\r\n        i0.ɵɵelementStart(19, \"input\", 13);\r\n        i0.ɵɵlistener(\"change\", function AddEditSectorComponent_Template_input_change_19_listener($event) { return ctx.onFileSelected($event); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(20, AddEditSectorComponent_img_20_Template, 1, 1, \"img\", 14);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(21, AddEditSectorComponent_div_21_Template, 2, 0, \"div\", 10);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(22, \"div\", 1);\r\n        i0.ɵɵelementStart(23, \"div\", 7);\r\n        i0.ɵɵelementStart(24, \"label\", 15);\r\n        i0.ɵɵtext(25, \"Sector Description\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(26, \"textarea\", 16);\r\n        i0.ɵɵtemplate(27, AddEditSectorComponent_div_27_Template, 2, 0, \"div\", 10);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(28, AddEditSectorComponent_div_28_Template, 4, 1, \"div\", 17);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(29, \"div\", 18);\r\n        i0.ɵɵtemplate(30, AddEditSectorComponent_button_30_Template, 2, 0, \"button\", 19);\r\n        i0.ɵɵelementStart(31, \"button\", 20);\r\n        i0.ɵɵtext(32, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n    } if (rf & 2) {\r\n        let tmp_3_0;\r\n        let tmp_6_0;\r\n        let tmp_8_0;\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Sector\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"formGroup\", ctx.addSectorForm);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.addSectorForm.get(\"IN_name\")) == null ? null : tmp_3_0.errors) && ((tmp_3_0 = ctx.addSectorForm.get(\"IN_name\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors.wordLimitExceeded));\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.imageSrc);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.addSectorForm.get(\"IN_dp\")) == null ? null : tmp_6_0.errors == null ? null : tmp_6_0.errors.fileSizeValidator);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.addSectorForm.get(\"IN_description\")) == null ? null : tmp_8_0.errors) && ((tmp_8_0 = ctx.addSectorForm.get(\"IN_description\")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors.wordLimitExceeded));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title == \"Add New\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n    } }, directives: [i7.SidebarComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlName, i2.RequiredValidator, i8.NgIf, i1.RouterLink], styles: [\".imageNameBox[_ngcontent-%COMP%] {\\n  position: absolute;\\n  transform: translateY(-50%);\\n  top: 50%;\\n  right: 80px;\\n  max-width: 400px;\\n  \\n  max-height: 36px;\\n  background-color: white;\\n  outline: none;\\n  border: none;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4uXFwuLlxcLi5cXFNlY3RvcnMlMjBhbmQlMjByb2xlc1xcQWxsJTIwU2VjdG9yc1xcYWRkLWVkaXQtc2VjdG9yXFxhZGQtZWRpdC1zZWN0b3IuY29tcG9uZW50LnNjc3MiLCJhZGQtZWRpdC1zZWN0b3IuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUU7RUFDRSxrQkFBQTtFQUNBLDJCQUFBO0VBQ0EsUUFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUFrQixtQ0FBQTtFQUNsQixnQkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtFQUNBLFlBQUE7QUNFSiIsImZpbGUiOiJhZGQtZWRpdC1zZWN0b3IuY29tcG9uZW50LnNjc3MiLCJzb3VyY2VzQ29udGVudCI6WyIgIC5pbWFnZU5hbWVCb3h7XHJcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XHJcbiAgICB0b3A6IDUwJTtcclxuICAgIHJpZ2h0OiA4MHB4OyBcclxuICAgIG1heC13aWR0aDogNDAwcHg7IC8qIEFkanVzdCB0aGUgbWF4LXdpZHRoIGFzIG5lZWRlZCAqL1xyXG4gICAgbWF4LWhlaWdodDogMzZweDsgXHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcclxuICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICBib3JkZXI6IG5vbmU7XHJcbiAgfVxyXG5cclxuICBcclxuXHJcbiAiLCIuaW1hZ2VOYW1lQm94IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XG4gIHRvcDogNTAlO1xuICByaWdodDogODBweDtcbiAgbWF4LXdpZHRoOiA0MDBweDtcbiAgLyogQWRqdXN0IHRoZSBtYXgtd2lkdGggYXMgbmVlZGVkICovXG4gIG1heC1oZWlnaHQ6IDM2cHg7XG4gIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xuICBvdXRsaW5lOiBub25lO1xuICBib3JkZXI6IG5vbmU7XG59Il19 */\"] });\r\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { defaultThrottleConfig, throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config = defaultThrottleConfig) {\n  const duration$ = timer(duration, scheduler);\n  return throttle(() => duration$, config);\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/operators/throttleTime.js"], "names": ["asyncScheduler", "defaultThrottleConfig", "throttle", "timer", "throttleTime", "duration", "scheduler", "config", "duration$"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,qBAAT,EAAgCC,QAAhC,QAAgD,YAAhD;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,OAAO,SAASC,YAAT,CAAsBC,QAAtB,EAAgCC,SAAS,GAAGN,cAA5C,EAA4DO,MAAM,GAAGN,qBAArE,EAA4F;AAC/F,QAAMO,SAAS,GAAGL,KAAK,CAACE,QAAD,EAAWC,SAAX,CAAvB;AACA,SAAOJ,QAAQ,CAAC,MAAMM,SAAP,EAAkBD,MAAlB,CAAf;AACH", "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { defaultThrottleConfig, throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler = asyncScheduler, config = defaultThrottleConfig) {\n    const duration$ = timer(duration, scheduler);\n    return throttle(() => duration$, config);\n}\n"]}, "metadata": {}, "sourceType": "module"}
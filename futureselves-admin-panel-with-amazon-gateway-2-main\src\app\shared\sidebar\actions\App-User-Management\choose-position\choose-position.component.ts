import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  AbstractControl,
  ValidatorFn,
} from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
import { formatDate } from '@angular/common';

@Component({
  selector: 'app-choose-position',
  templateUrl: './choose-position.component.html',
  styleUrls: ['./choose-position.component.scss'],
})
export class ChoosePositionComponent implements OnInit {
  @ViewChild('staticBackdrop', { static: true }) staticBackdrop!: ElementRef;
  rolesArray: any;
  isCurrentlyWorking: boolean = false;
  sectorsArray: any;
  showNewPositionForm: boolean = false;
  minDate: string;
  sectorID: any;
  companyLogo: any;
  RoleList: any;
  industrySelected: boolean = false;
  roleId: Promise<unknown>;
  minStartDate: string;
  invalidDates: Set<string> = new Set<string>();
  JobList: any;
  checkMinDate: string;
  addNewPositionForm: FormGroup;
  U_id: any;
  PositionData: any;
  U_isSharer: any;
  U_name: any;
  isExpert: boolean;
  newPositionData: any;
  expertRole: any;
  expertSector: any;

  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private domSanitizer: DomSanitizer,
    private router: Router,
    private http: HttpClient,
    private ngxSpinnerService: NgxSpinnerService,
    private route: ActivatedRoute
  ) {
    // this.minDate = new Date().toISOString().split('T')[0];
    // this.checkMinDate = this.minDate;

    this.addNewPositionForm = this.formBuilder.group({
      UI_industryId: ['', Validators.required],
      UI_roleId: [{ value: '', disabled: true }, [Validators.required,Validators.maxLength(50)]],
      UI_startDate: ['', Validators.required],
      UI_endDate: ['', Validators.required],
    });

    this.route.queryParams.subscribe((params) => {
      if (params) {
        this.U_id = params['U_id'];
        const isExpert = params['U_isExpert'];
        if(isExpert=='false'){
        this.isExpert=false;
        }
        if(isExpert=='true'){
          this.isExpert=true;
        }
        console.log(typeof this.isExpert,this.isExpert); // Output: string

        console.log('this.U_id', this.U_id, 'this.isExpert', this.isExpert);
      } else {
        this.router.navigate(['/actions/app-users']);
      }
    });
  }

  ngOnInit(): void {
    this.getindustry();
    this.getAllPosition(this.U_id).then(() => {
      this.checkUserType();
    });
  }

  checkUserType() {
    if (!this.isExpert) {
      this.addNewPositionForm.get('UI_startDate')?.valueChanges.subscribe(() => {
        this.updateStartDateMin();
      });
    } else if (!this.PositionData) {
      this.showModal();
      this.addNewPositionForm.get('UI_roleId')?.enable();
    }
  }
  

  updateStartDateMin(): void {
    const startDate = this.addNewPositionForm.get('UI_startDate')?.value;
    if (startDate) {
      const minStartDate = new Date(startDate);
      minStartDate.setDate(minStartDate.getDate() + 1); // Add one day to the start date
      this.minStartDate = minStartDate.toISOString().split('T')[0];

      const endDateControl = this.addNewPositionForm.get('UI_endDate');
      if (!this.isCurrentlyWorking) {
        endDateControl?.enable();
      }
      endDateControl?.setValidators([Validators.required]);
      endDateControl?.updateValueAndValidity();

      if (
        endDateControl?.value &&
        new Date(endDateControl.value) < minStartDate
      ) {
        endDateControl.setValue(null);
      }

      this.checkInvalidDate('UI_endDate');
    }
  }

  checkInvalidDate(controlName: string): void {
    const control = this.addNewPositionForm.get(controlName);
    if (control && control.value && isNaN(Date.parse(control.value))) {
      this.invalidDates.add(controlName);
    } else {
      this.invalidDates.delete(controlName);
    }
  }

  addNewPosition() {
    this.showNewPositionForm = true;
  }

  hideForm() {
    this.showNewPositionForm = false;
    this.industrySelected = false;
    this.addNewPositionForm.reset();
    this.addNewPositionForm.get('UI_roleId')?.disable();
  }

  savePosition() {
    if (this.isExpert) {
      this.addNewPositionForm.get('UI_startDate')?.clearValidators();
      this.addNewPositionForm.get('UI_startDate')?.updateValueAndValidity();
      this.addNewPositionForm.get('UI_endDate')?.clearValidators();
      this.addNewPositionForm.get('UI_endDate')?.updateValueAndValidity();

      this.newPositionData = {
        U_id: this.U_id,
        UI_industryId: this.addNewPositionForm.value.UI_industryId,
        UI_roleId: this.addNewPositionForm.value.UI_roleId,
        UI_expertId: 'expert',
      };
    } else {
      this.newPositionData = {
        U_id: this.U_id,
        UI_industryId: this.addNewPositionForm.value.UI_industryId,
        UI_roleId: this.addNewPositionForm.value.UI_roleId,
        UI_startDate: this.addNewPositionForm.value.UI_startDate,
        UI_endDate: this.addNewPositionForm.value.UI_endDate
          ? this.addNewPositionForm.value.UI_endDate
          : '',
      };
    }
    if (this.addNewPositionForm.valid) {
      console.log('New Position:', this.newPositionData);
      this.dataTransferService.savePosition(this.newPositionData).subscribe(
        (res: any) => {
          if (res.statusCode === 200) {
            this.toastr.success('Position saved successfully.');
            this.hideForm();
            this.getAllPosition(this.U_id);
            this.hideModal()
          } else {
            this.toastr.info('Failed to save the position.');
          }
        },
        (error: any) => {
          console.error('Error:', error);
          this.toastr.error('An error occurred while saving the position.');
        }
      );
    } else {
      Object.keys(this.addNewPositionForm.controls).forEach(name => {
        const control = this.addNewPositionForm.get(name);
        if (control?.invalid) {
          console.log(`Invalid control: ${name}, Errors:`, control.errors);
        }
      });
      this.toastr.info('Please fill all required fields correctly');
      return;

    }
  }

  viewCard(position?: any) {
    const state = {
      positionData:this.isExpert? this.PositionData[0] : position,
      U_isSharer: this.U_isSharer,
      U_name: this.U_name,
    };

    this.router.navigate(
      ['/actions/app-users/choose-position/add-new-insight'],
      {
        queryParams: { U_id: this.U_id,U_isExpert:this.isExpert },
        state: state,
      }
    );
  }

  getAllPosition(U_id: any): Promise<any> {
    this.ngxSpinnerService.show('globalSpinner');
    return new Promise((resolve, reject) => {
      this.dataTransferService.getAllPosition(U_id).subscribe(
        (res: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          if (res.statusCode === 200) {
            this.PositionData = res.data;
            this.U_isSharer = res.U_isSharer;
            this.U_name = res.U_name;

            if (this.isExpert && this.PositionData.length > 0) {
              this.expertSector = this.PositionData[0].IN_name;  
              this.expertRole = this.PositionData[0].UI_roleId; 
            }

            console.log('Position : ', res);
            resolve(res); // Resolve the promise with the response
          } else {
            this.toastr.error('Unable to fetch data');
            reject(new Error('Unable to fetch data')); 
          }
        },
        (error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          if(error.status === 404){
            this.toastr.info('No existing positions found.');
            console.log('404 Not Found error');
            resolve("No position found")
          } else {
            this.toastr.error('Unable to fetch data.');
            console.log('Error', error);
            reject(error)
          }
        }
      );
    });
}

  

  formatDate(date: number | string): string {
    let formattedDate: string;

    if (typeof date === 'number') {
      formattedDate = formatDate(new Date(date * 1000), 'MM-dd-yyyy', 'en-US');
    } else {
      // If date is already in a string format, assume it's a valid date string
      formattedDate = formatDate(new Date(date), 'MM-dd-yyyy', 'en-US');
    }

    return formattedDate;
  }

  getindustry(): void {
    this.dataTransferService.getIndustryData().subscribe(
      (res: any) => {
        if (res.status === 200) {
          this.sectorsArray = res.data;
          console.log('this.sectorsArray', this.sectorsArray);
        } else {
          console.error('Failed to fetch industry data:', res);
        }
      },
      (error: any) => {
        console.error('Error fetching industry data:', error);
      }
    );
  }

  onCurrentlyWorkingChange(event: Event): void {
    this.isCurrentlyWorking = (event.target as HTMLInputElement).checked;
    const endDateControl = this.addNewPositionForm.get('UI_endDate');

    if (this.isCurrentlyWorking) {
      endDateControl?.disable();
      endDateControl?.reset();
    } else {
      endDateControl?.enable();
    }
  }

  getAllRole(CO_sectorId: any) {
    console.log('sectorId : ', CO_sectorId);

    const excludedRoleIds = this.PositionData?.map((position: any) => position?.UI_roleId);

    this.dataTransferService.getAllRoleBySectorId(CO_sectorId).subscribe({
        next: (res: any) => {
            if (res.statusCode === 200) {
                this.rolesArray = res.data?.filter((role: any) => !excludedRoleIds?.includes(role?.RO_id));
            } else {
                console.error('Failed to fetch role. Status:', res.status);
            }
        },
        error: (error: any) => {
            this.ngxSpinnerService.hide('globalSpinner');
            console.error('Error occurred while fetching roles:', error);
        },
    });
}


  onChangeIndustry(event: any) {
    const CO_sectorId = event.target.value;
    this.industrySelected = event.target.value !== '';
    this.addNewPositionForm.get('UI_roleId')?.enable();
    this.getAllRole(CO_sectorId);
  }

  onSelectOtherDropdown(event: Event) {
    if (!this.industrySelected) {
      event.preventDefault();
      this.toastr.info('Please select a sector first.');
    }
  }

  showModal() {
    const modal = document.getElementById('staticBackdrop');
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal() {
    const modal = document.getElementById('staticBackdrop');
    if (modal != null) {
      modal.style.display = 'none';
    }
  }
}

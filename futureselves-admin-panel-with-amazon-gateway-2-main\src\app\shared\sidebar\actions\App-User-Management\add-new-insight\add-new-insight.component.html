<app-sidebar>
    <div class="content-wrapper">
        <div class="col-12 grid-margin stretch-card">
            <div class="card">
                <div class="card-header card-title bg-primary rounded-top text-white text-center mb-0">
                    Which Question Would You Like To Answer?
                </div>
                <div class="card-body">
                    <div class="row mb-4 text-center shadow-sm py-1">
                        <div class="col-md-4 info-card">
                            <div class="card p-2 bg-light border">
                                <h5 class="mb-1"><strong>Sector Name</strong></h5>
                                <hr class="divider">
                                <p class="mb-0">{{IN_name}}</p>
                            </div>
                        </div>
                        <div class="col-md-4 info-card">
                            <div class="card p-2 bg-light border">
                                <h5 class="mb-1"><strong>Role Name</strong></h5>
                                <hr class="divider">
                                <p class="mb-0">{{RO_title}}</p>
                            </div>
                        </div>
                        <div class="col-md-4 info-card">
                            <div class="card p-2 bg-light border">
                                <h5 class="mb-1"><strong>{{U_isSharer=="1"?"Sharer":"Seeker"}} Name</strong></h5>
                                <hr class="divider">
                                <p class="mb-0">{{U_name}}</p>
                            </div>
                        </div>
                    </div>
                    <!-- <hr> -->

                  <div class="questions-container">
                    <div class="col-lg-12 main" *ngFor="let question of questionData">
                        <div class="card position-card shadow-sm py-3 px-4 mb-3" [ngClass]="{'inactive-card': question.QU_status === 0}">
                            <div class="d-flex align-items-center">
                                <div class="question" >{{ question.QU_title }}</div>
                                <div class="ml-auto">
                                    <button *ngIf="question.QU_status === 1 && !isPlaying(question)" class="btn btn-sm btn-outline-primary mr-2" (click)="playAudio(question)">
                                        <i class="fas fa-play icon"></i>
                                    </button>
                                    <button *ngIf="question.QU_status === 1 && isPlaying(question)" class="btn btn-sm btn-outline-primary mr-2" (click)="pauseAudio(question)">
                                        <i class="fas fa-pause icon"></i>
                                    </button>
                                    <button *ngIf="question.QU_status === 1" (click)="showModal(question, 'edit')" class="btn btn-sm mr-2 btn-outline-primary">
                                        <i class="fas fa-edit icon"></i>
                                    </button>
                                    <button *ngIf="question.QU_status === 0" (click)="showModal(question, 'add')" class="btn btn-sm mr-2 btn-outline-primary mr-2">
                                        <i class="fas fa-plus icon"></i>
                                    </button>
                                </div>
                            </div>
                            <audio id="audioPlayer-{{ question.QU_id }}" [src]="question.AN_recordLink"></audio>
                        </div>
                    </div>
                    </div>  
        
                    <div class="text-center mt-2">
                        <button class="btn btn-light mr-2" (click)="goToChoosePosition()">Back</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</app-sidebar>


<div class="modal" id="staticBackdrop">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title fs-5" id="staticBackdropLabel">Upload Insight</h4>
            </div>
            <form [formGroup]="insightForm" (ngSubmit)="onSubmit()">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="AN_recordLink" [ngClass]="{ 'required-field': title !== 'edit' }">
                            <strong>Upload Here</strong>
                        </label>
                        <input type="file" #audioInput id="AN_recordLink" class="form-control form-control-sm"
                            formControlName="AN_recordLink" required (change)="onAudioSelected($event);"
                            accept="audio/*">
                        <div class="mt-1 px-2" *ngIf="title === 'edit'">
                            <small><strong>Existing Insight Name: </strong> {{ trimedAudio }}</small><br>
                            <small *ngIf="previousAudioDuration"><strong>Existing Insight Duration: </strong>{{
                                previousAudioDuration }}</small>
                        </div>
                        <div *ngIf="insightForm.get('AN_recordLink')?.errors?.fileSizeValidator" class="text-danger">
                            The file size exceeds the 10 MB limit. Please select a smaller file.
                        </div>                                
                    </div>
                    <div *ngIf="title !== 'edit'" class="form-group">
                        <label for="A_title"><strong>Tags</strong> (Optional)</label>
                        <div class="tag-input-container">
                          <div class="tag-box">
                            <div *ngFor="let tag of insightForm.get('A_title')?.value; let i = index"  [ngClass]="{'view-mode': title === 'View'}" class="tag">
                              {{ tag }}
                              <span  class="close" (click)="removeTagForField(i, 'A_title')">x</span>
                            </div>
                          </div>
                          <input type="text" (keydown)="onEnterKey($event, 'A_title')" (keydown)="onBackspaceKey($event, 'A_title')" (blur)="onInputBlur($event, 'A_title')" class="tag-input form-control form-control-sm" placeholder="Enter Tag...">
                        </div>
                      </div>

                    <hr>
                    <div *ngIf="!isExpert" class="form-group mb-0 pb-0">
                        <div class="form-check form-switch">
                          <input class="form-check-input" type="checkbox" id="AN_ispublic"
                                 formControlName="AN_ispublic" [checked]="insightForm.get('AN_ispublic')?.value">
                          <label class="form-check-label" for="AN_ispublic">Make This Insight Public</label>
                        </div>
                        <!-- <div *ngIf="!insightForm.get('AN_ispublic')?.value"
                             class="form-check form-switch mt-2 mb-0 pb-0">
                          <input class="form-check-input" type="checkbox" id="AN_isanonimise"
                                 formControlName="AN_isanonimise" [checked]="insightForm.get('AN_isanonimise')?.value">
                          <label class="form-check-label" for="AN_isanonimise">Make This Insight Anonymous</label>
                        </div> -->
                      </div>
                    </div>

                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Save</button>
                    <button type="button" class="btn btn-light" (click)="hideModal()">Cancel</button>
                </div>
            </form>


        </div>
    </div>
</div>
  // .scrollable-container {
  //   max-height: 285px;
  //   display: block;
  //   overflow-y: scroll !important;
  // }

  .table thead th {
    position: sticky;
    top: 0;
    background: white; 
    z-index: 1; 
  }
  
  .table {
    width: 100%; 
    border-collapse: collapse; 
  }
  
  .scrollable-container-right{
    max-height: 430px; 
    overflow-y: scroll !important;
    display: block;
  }
  

.content-wrapper {
  overflow: visible; /* Ensure parent containers don't have overflow: hidden */
  position: relative;
}

.audioControl{
  width: 190px;
}
.custom-radius-btn {
  border-radius: 12px; /* Change this value to whatever you need */
}


.custom-ng-select {
  width: 100%; 
}

.ng-select {
  min-width: 250px; 
}

.ng-select .ng-select-container {
  font-size: small;
}


@media (max-width: 1398px) {
  .ng-select {
    min-width: 100px; 
  }
}


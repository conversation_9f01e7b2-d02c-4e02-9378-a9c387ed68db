{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { timeout } from './timeout';\nexport function timeoutWith(due, withObservable, scheduler) {\n  let first;\n  let each;\n\n  let _with;\n\n  scheduler = scheduler !== null && scheduler !== void 0 ? scheduler : async;\n\n  if (isValidDate(due)) {\n    first = due;\n  } else if (typeof due === 'number') {\n    each = due;\n  }\n\n  if (withObservable) {\n    _with = () => withObservable;\n  } else {\n    throw new TypeError('No observable provided to switch to');\n  }\n\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n\n  return timeout({\n    first,\n    each,\n    scheduler,\n    with: _with\n  });\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/operators/timeoutWith.js"], "names": ["async", "isValidDate", "timeout", "timeoutWith", "due", "withObservable", "scheduler", "first", "each", "_with", "TypeError", "with"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,WAAT,QAA4B,gBAA5B;AACA,SAASC,OAAT,QAAwB,WAAxB;AACA,OAAO,SAASC,WAAT,CAAqBC,GAArB,EAA0BC,cAA1B,EAA0CC,SAA1C,EAAqD;AACxD,MAAIC,KAAJ;AACA,MAAIC,IAAJ;;AACA,MAAIC,KAAJ;;AACAH,EAAAA,SAAS,GAAGA,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6CA,SAA7C,GAAyDN,KAArE;;AACA,MAAIC,WAAW,CAACG,GAAD,CAAf,EAAsB;AAClBG,IAAAA,KAAK,GAAGH,GAAR;AACH,GAFD,MAGK,IAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;AAC9BI,IAAAA,IAAI,GAAGJ,GAAP;AACH;;AACD,MAAIC,cAAJ,EAAoB;AAChBI,IAAAA,KAAK,GAAG,MAAMJ,cAAd;AACH,GAFD,MAGK;AACD,UAAM,IAAIK,SAAJ,CAAc,qCAAd,CAAN;AACH;;AACD,MAAIH,KAAK,IAAI,IAAT,IAAiBC,IAAI,IAAI,IAA7B,EAAmC;AAC/B,UAAM,IAAIE,SAAJ,CAAc,sBAAd,CAAN;AACH;;AACD,SAAOR,OAAO,CAAC;AACXK,IAAAA,KADW;AAEXC,IAAAA,IAFW;AAGXF,IAAAA,SAHW;AAIXK,IAAAA,IAAI,EAAEF;AAJK,GAAD,CAAd;AAMH", "sourcesContent": ["import { async } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { timeout } from './timeout';\nexport function timeoutWith(due, withObservable, scheduler) {\n    let first;\n    let each;\n    let _with;\n    scheduler = scheduler !== null && scheduler !== void 0 ? scheduler : async;\n    if (isValidDate(due)) {\n        first = due;\n    }\n    else if (typeof due === 'number') {\n        each = due;\n    }\n    if (withObservable) {\n        _with = () => withObservable;\n    }\n    else {\n        throw new TypeError('No observable provided to switch to');\n    }\n    if (first == null && each == null) {\n        throw new TypeError('No timeout provided.');\n    }\n    return timeout({\n        first,\n        each,\n        scheduler,\n        with: _with,\n    });\n}\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Pipe, Component, ChangeDetectionStrategy, Input, ViewChild, HostListener, NgModule } from '@angular/core';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/platform-browser';\nconst _c0 = [\"overlay\"];\n\nfunction NgxSpinnerComponent_div_0_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n}\n\nfunction NgxSpinnerComponent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, NgxSpinnerComponent_div_0_div_2_div_1_Template, 1, 0, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r2.spinner.class);\n    i0.ɵɵstyleProp(\"color\", ctx_r2.spinner.color);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.spinner.divArray);\n  }\n}\n\nfunction NgxSpinnerComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n    i0.ɵɵpipe(1, \"safeHtml\");\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(1, 1, ctx_r3.template), i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction NgxSpinnerComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵtemplate(2, NgxSpinnerComponent_div_0_div_2_Template, 2, 5, \"div\", 3);\n    i0.ɵɵtemplate(3, NgxSpinnerComponent_div_0_div_3_Template, 2, 3, \"div\", 4);\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵprojection(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r0.spinner.bdColor)(\"z-index\", ctx_r0.spinner.zIndex)(\"position\", ctx_r0.spinner.fullScreen ? \"fixed\" : \"absolute\");\n    i0.ɵɵproperty(\"@.disabled\", ctx_r0.disableAnimation)(\"@fadeIn\", \"in\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.template);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.template);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"z-index\", ctx_r0.spinner.zIndex);\n  }\n}\n\nconst _c1 = [\"*\"];\nconst LOADERS = {\n  'ball-8bits': 16,\n  'ball-atom': 4,\n  'ball-beat': 3,\n  'ball-circus': 5,\n  'ball-climbing-dot': 4,\n  'ball-clip-rotate': 1,\n  'ball-clip-rotate-multiple': 2,\n  'ball-clip-rotate-pulse': 2,\n  'ball-elastic-dots': 5,\n  'ball-fall': 3,\n  'ball-fussion': 4,\n  'ball-grid-beat': 9,\n  'ball-grid-pulse': 9,\n  'ball-newton-cradle': 4,\n  'ball-pulse': 3,\n  'ball-pulse-rise': 5,\n  'ball-pulse-sync': 3,\n  'ball-rotate': 1,\n  'ball-running-dots': 5,\n  'ball-scale': 1,\n  'ball-scale-multiple': 3,\n  'ball-scale-pulse': 2,\n  'ball-scale-ripple': 1,\n  'ball-scale-ripple-multiple': 3,\n  'ball-spin': 8,\n  'ball-spin-clockwise': 8,\n  'ball-spin-clockwise-fade': 8,\n  'ball-spin-clockwise-fade-rotating': 8,\n  'ball-spin-fade': 8,\n  'ball-spin-fade-rotating': 8,\n  'ball-spin-rotate': 2,\n  'ball-square-clockwise-spin': 8,\n  'ball-square-spin': 8,\n  'ball-triangle-path': 3,\n  'ball-zig-zag': 2,\n  'ball-zig-zag-deflect': 2,\n  'cog': 1,\n  'cube-transition': 2,\n  'fire': 3,\n  'line-scale': 5,\n  'line-scale-party': 5,\n  'line-scale-pulse-out': 5,\n  'line-scale-pulse-out-rapid': 5,\n  'line-spin-clockwise-fade': 8,\n  'line-spin-clockwise-fade-rotating': 8,\n  'line-spin-fade': 8,\n  'line-spin-fade-rotating': 8,\n  'pacman': 6,\n  'square-jelly-box': 2,\n  'square-loader': 1,\n  'square-spin': 1,\n  'timer': 1,\n  'triangle-skew-spin': 1\n};\nconst DEFAULTS = {\n  BD_COLOR: 'rgba(51,51,51,0.8)',\n  SPINNER_COLOR: '#fff',\n  Z_INDEX: 99999\n};\nconst PRIMARY_SPINNER = 'primary';\n\nclass NgxSpinner {\n  constructor(init) {\n    Object.assign(this, init);\n  }\n\n  static create(init) {\n    if (init?.type == null || init.type.length === 0) {\n      console.warn(`[ngx-spinner]: Property \"type\" is missed. Please, provide animation type to <ngx-spinner> component\n        and ensure css is added to angular.json file`);\n    }\n\n    return new NgxSpinner(init);\n  }\n\n}\n\nclass NgxSpinnerService {\n  /**\n   * Creates an instance of NgxSpinnerService.\n   * @memberof NgxSpinnerService\n   */\n  constructor() {\n    /**\n     * Spinner observable\n     *\n     * @memberof NgxSpinnerService\n     */\n    // private spinnerObservable = new ReplaySubject<NgxSpinner>(1);\n    this.spinnerObservable = new BehaviorSubject(null);\n  }\n  /**\n  * Get subscription of desired spinner\n  * @memberof NgxSpinnerService\n  **/\n\n\n  getSpinner(name) {\n    return this.spinnerObservable.asObservable().pipe(filter(x => x && x.name === name));\n  }\n  /**\n   * To show spinner\n   *\n   * @memberof NgxSpinnerService\n   */\n\n\n  show(name = PRIMARY_SPINNER, spinner) {\n    return new Promise((resolve, _reject) => {\n      setTimeout(() => {\n        if (spinner && Object.keys(spinner).length) {\n          spinner['name'] = name;\n          this.spinnerObservable.next(new NgxSpinner({ ...spinner,\n            show: true\n          }));\n          resolve(true);\n        } else {\n          this.spinnerObservable.next(new NgxSpinner({\n            name,\n            show: true\n          }));\n          resolve(true);\n        }\n      }, 10);\n    });\n  }\n  /**\n  * To hide spinner\n  *\n  * @memberof NgxSpinnerService\n  */\n\n\n  hide(name = PRIMARY_SPINNER, debounce = 10) {\n    return new Promise((resolve, _reject) => {\n      setTimeout(() => {\n        this.spinnerObservable.next(new NgxSpinner({\n          name,\n          show: false\n        }));\n        resolve(true);\n      }, debounce);\n    });\n  }\n\n}\n\nNgxSpinnerService.ɵfac = function NgxSpinnerService_Factory(t) {\n  return new (t || NgxSpinnerService)();\n};\n\nNgxSpinnerService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxSpinnerService,\n  factory: NgxSpinnerService.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxSpinnerService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\nclass SafeHtmlPipe {\n  constructor(_sanitizer) {\n    this._sanitizer = _sanitizer;\n  }\n\n  transform(v) {\n    if (v) {\n      return this._sanitizer.bypassSecurityTrustHtml(v);\n    }\n  }\n\n}\n\nSafeHtmlPipe.ɵfac = function SafeHtmlPipe_Factory(t) {\n  return new (t || SafeHtmlPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n};\n\nSafeHtmlPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"safeHtml\",\n  type: SafeHtmlPipe,\n  pure: true\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SafeHtmlPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'safeHtml'\n    }]\n  }], function () {\n    return [{\n      type: i1.DomSanitizer\n    }];\n  }, null);\n})();\n\nclass NgxSpinnerComponent {\n  /**\n   * Creates an instance of NgxSpinnerComponent.\n   *\n   * @memberof NgxSpinnerComponent\n   */\n  constructor(spinnerService, changeDetector, elementRef) {\n    this.spinnerService = spinnerService;\n    this.changeDetector = changeDetector;\n    this.elementRef = elementRef;\n    /**\n     * To enable/disable animation\n     *\n     * @type {boolean}\n     * @memberof NgxSpinnerComponent\n     */\n\n    this.disableAnimation = false;\n    /**\n     * Spinner Object\n     *\n     * @memberof NgxSpinnerComponent\n     */\n\n    this.spinner = new NgxSpinner();\n    /**\n     * Unsubscribe from spinner's observable\n     *\n     * @memberof NgxSpinnerComponent\n    **/\n\n    this.ngUnsubscribe = new Subject();\n    /**\n     * To set default ngx-spinner options\n     *\n     * @memberof NgxSpinnerComponent\n     */\n\n    this.setDefaultOptions = () => {\n      this.spinner = NgxSpinner.create({\n        name: this.name,\n        bdColor: this.bdColor,\n        size: this.size,\n        color: this.color,\n        type: this.type,\n        fullScreen: this.fullScreen,\n        divArray: this.divArray,\n        divCount: this.divCount,\n        show: this.show,\n        zIndex: this.zIndex,\n        template: this.template,\n        showSpinner: this.showSpinner\n      });\n    };\n\n    this.bdColor = DEFAULTS.BD_COLOR;\n    this.zIndex = DEFAULTS.Z_INDEX;\n    this.color = DEFAULTS.SPINNER_COLOR;\n    this.size = 'large';\n    this.fullScreen = true;\n    this.name = PRIMARY_SPINNER;\n    this.template = null;\n    this.showSpinner = false;\n    this.divArray = [];\n    this.divCount = 0;\n    this.show = false;\n  }\n\n  handleKeyboardEvent(event) {\n    if (this.spinnerDOM && this.spinnerDOM.nativeElement) {\n      if (this.fullScreen || !this.fullScreen && this.isSpinnerZone(event.target)) {\n        event.returnValue = false;\n        event.preventDefault();\n      }\n    }\n  }\n\n  initObservable() {\n    this.spinnerService.getSpinner(this.name).pipe(takeUntil(this.ngUnsubscribe)).subscribe(spinner => {\n      this.setDefaultOptions();\n      Object.assign(this.spinner, spinner);\n\n      if (spinner.show) {\n        this.onInputChange();\n      }\n\n      this.changeDetector.detectChanges();\n    });\n  }\n  /**\n   * Initialization method\n   *\n   * @memberof NgxSpinnerComponent\n   */\n\n\n  ngOnInit() {\n    this.setDefaultOptions();\n    this.initObservable();\n  }\n  /**\n   * To check event triggers inside the Spinner Zone\n   *\n   * @param {*} element\n   * @returns {boolean}\n   * @memberof NgxSpinnerComponent\n   */\n\n\n  isSpinnerZone(element) {\n    if (element === this.elementRef.nativeElement.parentElement) {\n      return true;\n    }\n\n    return element.parentNode && this.isSpinnerZone(element.parentNode);\n  }\n  /**\n   * On changes event for input variables\n   *\n   * @memberof NgxSpinnerComponent\n   */\n\n\n  ngOnChanges(changes) {\n    for (const propName in changes) {\n      if (propName) {\n        const changedProp = changes[propName];\n\n        if (changedProp.isFirstChange()) {\n          return;\n        } else if (typeof changedProp.currentValue !== 'undefined' && changedProp.currentValue !== changedProp.previousValue) {\n          if (changedProp.currentValue !== '') {\n            this.spinner[propName] = changedProp.currentValue;\n\n            if (propName === 'showSpinner') {\n              if (changedProp.currentValue) {\n                this.spinnerService.show(this.spinner.name, this.spinner);\n              } else {\n                this.spinnerService.hide(this.spinner.name);\n              }\n            }\n\n            if (propName === 'name') {\n              this.initObservable();\n            }\n          }\n        }\n      }\n    }\n  }\n  /**\n   * To get class for spinner\n   *\n   * @memberof NgxSpinnerComponent\n   */\n\n\n  getClass(type, size) {\n    this.spinner.divCount = LOADERS[type];\n    this.spinner.divArray = Array(this.spinner.divCount).fill(0).map((_, i) => i);\n    let sizeClass = '';\n\n    switch (size.toLowerCase()) {\n      case 'small':\n        sizeClass = 'la-sm';\n        break;\n\n      case 'medium':\n        sizeClass = 'la-2x';\n        break;\n\n      case 'large':\n        sizeClass = 'la-3x';\n        break;\n\n      default:\n        break;\n    }\n\n    return 'la-' + type + ' ' + sizeClass;\n  }\n  /**\n   * Check if input variables have changed\n   *\n   * @memberof NgxSpinnerComponent\n   */\n\n\n  onInputChange() {\n    this.spinner.class = this.getClass(this.spinner.type, this.spinner.size);\n  }\n  /**\n   * Component destroy event\n   *\n   * @memberof NgxSpinnerComponent\n   */\n\n\n  ngOnDestroy() {\n    this.ngUnsubscribe.next();\n    this.ngUnsubscribe.complete();\n  }\n\n}\n\nNgxSpinnerComponent.ɵfac = function NgxSpinnerComponent_Factory(t) {\n  return new (t || NgxSpinnerComponent)(i0.ɵɵdirectiveInject(NgxSpinnerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nNgxSpinnerComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgxSpinnerComponent,\n  selectors: [[\"ngx-spinner\"]],\n  viewQuery: function NgxSpinnerComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.spinnerDOM = _t.first);\n    }\n  },\n  hostBindings: function NgxSpinnerComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function NgxSpinnerComponent_keydown_HostBindingHandler($event) {\n        return ctx.handleKeyboardEvent($event);\n      }, false, i0.ɵɵresolveDocument);\n    }\n  },\n  inputs: {\n    bdColor: \"bdColor\",\n    size: \"size\",\n    color: \"color\",\n    type: \"type\",\n    fullScreen: \"fullScreen\",\n    name: \"name\",\n    zIndex: \"zIndex\",\n    template: \"template\",\n    showSpinner: \"showSpinner\",\n    disableAnimation: \"disableAnimation\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c1,\n  decls: 1,\n  vars: 1,\n  consts: [[\"class\", \"ngx-spinner-overlay\", 3, \"background-color\", \"z-index\", \"position\", 4, \"ngIf\"], [1, \"ngx-spinner-overlay\"], [\"overlay\", \"\"], [3, \"class\", \"color\", 4, \"ngIf\"], [3, \"innerHTML\", 4, \"ngIf\"], [1, \"loading-text\"], [4, \"ngFor\", \"ngForOf\"], [3, \"innerHTML\"]],\n  template: function NgxSpinnerComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, NgxSpinnerComponent_div_0_Template, 6, 12, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.spinner.show);\n    }\n  },\n  directives: [i2.NgIf, i2.NgForOf],\n  pipes: [SafeHtmlPipe],\n  styles: [\".ngx-spinner-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%}.ngx-spinner-overlay[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]:not(.loading-text){top:50%;left:50%;margin:0;position:absolute;transform:translate(-50%,-50%)}.loading-text[_ngcontent-%COMP%]{position:absolute;top:60%;left:50%;transform:translate(-50%,-60%)}\"],\n  data: {\n    animation: [trigger('fadeIn', [state('in', style({\n      opacity: 1\n    })), transition(':enter', [style({\n      opacity: 0\n    }), animate(300)]), transition(':leave', animate(200, style({\n      opacity: 0\n    })))])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxSpinnerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ngx-spinner',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [trigger('fadeIn', [state('in', style({\n        opacity: 1\n      })), transition(':enter', [style({\n        opacity: 0\n      }), animate(300)]), transition(':leave', animate(200, style({\n        opacity: 0\n      })))])],\n      template: \"<div [@.disabled]=\\\"disableAnimation\\\" [@fadeIn]=\\\"'in'\\\" *ngIf=\\\"spinner.show\\\" class=\\\"ngx-spinner-overlay\\\"\\n  [style.background-color]=\\\"spinner.bdColor\\\" [style.z-index]=\\\"spinner.zIndex\\\"\\n  [style.position]=\\\"spinner.fullScreen ? 'fixed' : 'absolute'\\\" #overlay>\\n  <div *ngIf=\\\"!template\\\" [class]=\\\"spinner.class\\\" [style.color]=\\\"spinner.color\\\">\\n    <div *ngFor=\\\"let index of spinner.divArray\\\"></div>\\n  </div>\\n  <div *ngIf=\\\"template\\\" [innerHTML]=\\\"template | safeHtml\\\"></div>\\n  <div class=\\\"loading-text\\\" [style.z-index]=\\\"spinner.zIndex\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</div>\",\n      styles: [\".ngx-spinner-overlay{position:fixed;top:0;left:0;width:100%;height:100%}.ngx-spinner-overlay>div:not(.loading-text){top:50%;left:50%;margin:0;position:absolute;transform:translate(-50%,-50%)}.loading-text{position:absolute;top:60%;left:50%;transform:translate(-50%,-60%)}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: NgxSpinnerService\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    bdColor: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    fullScreen: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    zIndex: [{\n      type: Input\n    }],\n    template: [{\n      type: Input\n    }],\n    showSpinner: [{\n      type: Input\n    }],\n    disableAnimation: [{\n      type: Input\n    }],\n    spinnerDOM: [{\n      type: ViewChild,\n      args: ['overlay']\n    }],\n    handleKeyboardEvent: [{\n      type: HostListener,\n      args: ['document:keydown', ['$event']]\n    }]\n  });\n})();\n\nclass NgxSpinnerModule {}\n\nNgxSpinnerModule.ɵfac = function NgxSpinnerModule_Factory(t) {\n  return new (t || NgxSpinnerModule)();\n};\n\nNgxSpinnerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxSpinnerModule\n});\nNgxSpinnerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [NgxSpinnerComponent, SafeHtmlPipe],\n      exports: [NgxSpinnerComponent]\n    }]\n  }], null, null);\n})();\n/*\n * Public API Surface of ngx-spinner\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { DEFAULTS, LOADERS, NgxSpinner, NgxSpinnerComponent, NgxSpinnerModule, NgxSpinnerService, PRIMARY_SPINNER };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/ngx-spinner/fesm2020/ngx-spinner.mjs"], "names": ["i0", "Injectable", "<PERSON><PERSON>", "Component", "ChangeDetectionStrategy", "Input", "ViewChild", "HostListener", "NgModule", "BehaviorSubject", "Subject", "filter", "takeUntil", "trigger", "state", "style", "transition", "animate", "i2", "CommonModule", "i1", "LOADERS", "DEFAULTS", "BD_COLOR", "SPINNER_COLOR", "Z_INDEX", "PRIMARY_SPINNER", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "init", "Object", "assign", "create", "type", "length", "console", "warn", "NgxSpinnerService", "spinnerObservable", "getSpinner", "name", "asObservable", "pipe", "x", "show", "spinner", "Promise", "resolve", "_reject", "setTimeout", "keys", "next", "hide", "debounce", "ɵfac", "ɵprov", "args", "providedIn", "SafeHtmlPipe", "_sanitizer", "transform", "v", "bypassSecurityTrustHtml", "Dom<PERSON><PERSON><PERSON>zer", "ɵpipe", "NgxSpinnerComponent", "spinnerService", "changeDetector", "elementRef", "disableAnimation", "ngUnsubscribe", "setDefaultOptions", "bdColor", "size", "color", "fullScreen", "divArray", "divCount", "zIndex", "template", "showSpinner", "handleKeyboardEvent", "event", "spinnerDOM", "nativeElement", "isSpinnerZone", "target", "returnValue", "preventDefault", "initObservable", "subscribe", "onInputChange", "detectChanges", "ngOnInit", "element", "parentElement", "parentNode", "ngOnChanges", "changes", "propName", "changedProp", "isFirstChange", "currentValue", "previousValue", "getClass", "Array", "fill", "map", "_", "i", "sizeClass", "toLowerCase", "class", "ngOnDestroy", "complete", "ChangeDetectorRef", "ElementRef", "ɵcmp", "NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opacity", "selector", "changeDetection", "OnPush", "animations", "styles", "NgxSpinnerModule", "ɵmod", "ɵinj", "imports", "declarations", "exports"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,IAArB,EAA2BC,SAA3B,EAAsCC,uBAAtC,EAA+DC,KAA/D,EAAsEC,SAAtE,EAAiFC,YAAjF,EAA+FC,QAA/F,QAA+G,eAA/G;AACA,SAASC,eAAT,EAA0BC,OAA1B,QAAyC,MAAzC;AACA,SAASC,MAAT,EAAiBC,SAAjB,QAAkC,gBAAlC;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,2BAApB;;;;;AAmIoGpB,IAAAA,EA8Mu1B,oB;;;;;;AA9Mv1BA,IAAAA,EA8M8vB,yB;AA9M9vBA,IAAAA,EA8Mu1B,8E;AA9Mv1BA,IAAAA,EA8M+4B,e;;;;mBA9M/4BA,E;AAAAA,IAAAA,EA8MuxB,iC;AA9MvxBA,IAAAA,EA8MizB,2C;AA9MjzBA,IAAAA,EA8M+2B,a;AA9M/2BA,IAAAA,EA8M+2B,+C;;;;;;AA9M/2BA,IAAAA,EA8My5B,uB;AA9Mz5BA,IAAAA,E;;;;mBAAAA,E;AAAAA,IAAAA,EA8Mi7B,yBA9Mj7BA,EA8Mi7B,qCA9Mj7BA,EA8Mi7B,gB;;;;;;AA9Mj7BA,IAAAA,EA8M6e,+B;AA9M7eA,IAAAA,EA8M8vB,wE;AA9M9vBA,IAAAA,EA8My5B,wE;AA9Mz5BA,IAAAA,EA8M+9B,4B;AA9M/9BA,IAAAA,EA8MoiC,gB;AA9MpiCA,IAAAA,EA8MikC,e;AA9MjkCA,IAAAA,EA8MykC,e;;;;mBA9MzkCA,E;AAAAA,IAAAA,EA8M+lB,wJ;AA9M/lBA,IAAAA,EA8Mkf,mE;AA9MlfA,IAAAA,EA8MowB,a;AA9MpwBA,IAAAA,EA8MowB,qC;AA9MpwBA,IAAAA,EA8M+5B,a;AA9M/5BA,IAAAA,EA8M+5B,oC;AA9M/5BA,IAAAA,EA8M2/B,a;AA9M3/BA,IAAAA,EA8M2/B,8C;;;;;AA/U/lC,MAAMqB,OAAO,GAAG;AACZ,gBAAc,EADF;AAEZ,eAAa,CAFD;AAGZ,eAAa,CAHD;AAIZ,iBAAe,CAJH;AAKZ,uBAAqB,CALT;AAMZ,sBAAoB,CANR;AAOZ,+BAA6B,CAPjB;AAQZ,4BAA0B,CARd;AASZ,uBAAqB,CATT;AAUZ,eAAa,CAVD;AAWZ,kBAAgB,CAXJ;AAYZ,oBAAkB,CAZN;AAaZ,qBAAmB,CAbP;AAcZ,wBAAsB,CAdV;AAeZ,gBAAc,CAfF;AAgBZ,qBAAmB,CAhBP;AAiBZ,qBAAmB,CAjBP;AAkBZ,iBAAe,CAlBH;AAmBZ,uBAAqB,CAnBT;AAoBZ,gBAAc,CApBF;AAqBZ,yBAAuB,CArBX;AAsBZ,sBAAoB,CAtBR;AAuBZ,uBAAqB,CAvBT;AAwBZ,gCAA8B,CAxBlB;AAyBZ,eAAa,CAzBD;AA0BZ,yBAAuB,CA1BX;AA2BZ,8BAA4B,CA3BhB;AA4BZ,uCAAqC,CA5BzB;AA6BZ,oBAAkB,CA7BN;AA8BZ,6BAA2B,CA9Bf;AA+BZ,sBAAoB,CA/BR;AAgCZ,gCAA8B,CAhClB;AAiCZ,sBAAoB,CAjCR;AAkCZ,wBAAsB,CAlCV;AAmCZ,kBAAgB,CAnCJ;AAoCZ,0BAAwB,CApCZ;AAqCZ,SAAO,CArCK;AAsCZ,qBAAmB,CAtCP;AAuCZ,UAAQ,CAvCI;AAwCZ,gBAAc,CAxCF;AAyCZ,sBAAoB,CAzCR;AA0CZ,0BAAwB,CA1CZ;AA2CZ,gCAA8B,CA3ClB;AA4CZ,8BAA4B,CA5ChB;AA6CZ,uCAAqC,CA7CzB;AA8CZ,oBAAkB,CA9CN;AA+CZ,6BAA2B,CA/Cf;AAgDZ,YAAU,CAhDE;AAiDZ,sBAAoB,CAjDR;AAkDZ,mBAAiB,CAlDL;AAmDZ,iBAAe,CAnDH;AAoDZ,WAAS,CApDG;AAqDZ,wBAAsB;AArDV,CAAhB;AAuDA,MAAMC,QAAQ,GAAG;AACbC,EAAAA,QAAQ,EAAE,oBADG;AAEbC,EAAAA,aAAa,EAAE,MAFF;AAGbC,EAAAA,OAAO,EAAE;AAHI,CAAjB;AAKA,MAAMC,eAAe,GAAG,SAAxB;;AACA,MAAMC,UAAN,CAAiB;AACbC,EAAAA,WAAW,CAACC,IAAD,EAAO;AACdC,IAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBF,IAApB;AACH;;AACY,SAANG,MAAM,CAACH,IAAD,EAAO;AAChB,QAAIA,IAAI,EAAEI,IAAN,IAAc,IAAd,IAAsBJ,IAAI,CAACI,IAAL,CAAUC,MAAV,KAAqB,CAA/C,EAAkD;AAC9CC,MAAAA,OAAO,CAACC,IAAR,CAAc;AAC1B,qDADY;AAEH;;AACD,WAAO,IAAIT,UAAJ,CAAeE,IAAf,CAAP;AACH;;AAVY;;AAajB,MAAMQ,iBAAN,CAAwB;AACpB;AACJ;AACA;AACA;AACIT,EAAAA,WAAW,GAAG;AACV;AACR;AACA;AACA;AACA;AACQ;AACA,SAAKU,iBAAL,GAAyB,IAAI7B,eAAJ,CAAoB,IAApB,CAAzB;AACH;AACD;AACJ;AACA;AACA;;;AACI8B,EAAAA,UAAU,CAACC,IAAD,EAAO;AACb,WAAO,KAAKF,iBAAL,CAAuBG,YAAvB,GAAsCC,IAAtC,CAA2C/B,MAAM,CAAEgC,CAAD,IAAOA,CAAC,IAAIA,CAAC,CAACH,IAAF,KAAWA,IAAxB,CAAjD,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACII,EAAAA,IAAI,CAACJ,IAAI,GAAGd,eAAR,EAAyBmB,OAAzB,EAAkC;AAClC,WAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,OAAV,KAAsB;AACrCC,MAAAA,UAAU,CAAC,MAAM;AACb,YAAIJ,OAAO,IAAIf,MAAM,CAACoB,IAAP,CAAYL,OAAZ,EAAqBX,MAApC,EAA4C;AACxCW,UAAAA,OAAO,CAAC,MAAD,CAAP,GAAkBL,IAAlB;AACA,eAAKF,iBAAL,CAAuBa,IAAvB,CAA4B,IAAIxB,UAAJ,CAAe,EAAE,GAAGkB,OAAL;AAAcD,YAAAA,IAAI,EAAE;AAApB,WAAf,CAA5B;AACAG,UAAAA,OAAO,CAAC,IAAD,CAAP;AACH,SAJD,MAKK;AACD,eAAKT,iBAAL,CAAuBa,IAAvB,CAA4B,IAAIxB,UAAJ,CAAe;AAAEa,YAAAA,IAAF;AAAQI,YAAAA,IAAI,EAAE;AAAd,WAAf,CAA5B;AACAG,UAAAA,OAAO,CAAC,IAAD,CAAP;AACH;AACJ,OAVS,EAUP,EAVO,CAAV;AAWH,KAZM,CAAP;AAaH;AACD;AACJ;AACA;AACA;AACA;;;AACIK,EAAAA,IAAI,CAACZ,IAAI,GAAGd,eAAR,EAAyB2B,QAAQ,GAAG,EAApC,EAAwC;AACxC,WAAO,IAAIP,OAAJ,CAAY,CAACC,OAAD,EAAUC,OAAV,KAAsB;AACrCC,MAAAA,UAAU,CAAC,MAAM;AACb,aAAKX,iBAAL,CAAuBa,IAAvB,CAA4B,IAAIxB,UAAJ,CAAe;AAAEa,UAAAA,IAAF;AAAQI,UAAAA,IAAI,EAAE;AAAd,SAAf,CAA5B;AACAG,QAAAA,OAAO,CAAC,IAAD,CAAP;AACH,OAHS,EAGPM,QAHO,CAAV;AAIH,KALM,CAAP;AAMH;;AArDmB;;AAuDxBhB,iBAAiB,CAACiB,IAAlB;AAAA,mBAA8GjB,iBAA9G;AAAA;;AACAA,iBAAiB,CAACkB,KAAlB,kBADoGvD,EACpG;AAAA,SAAkHqC,iBAAlH;AAAA,WAAkHA,iBAAlH;AAAA,cAAiJ;AAAjJ;;AACA;AAAA,qDAFoGrC,EAEpG,mBAA2FqC,iBAA3F,EAA0H,CAAC;AAC/GJ,IAAAA,IAAI,EAAEhC,UADyG;AAE/GuD,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,UAAU,EAAE;AADb,KAAD;AAFyG,GAAD,CAA1H,EAK4B,YAAY;AAAE,WAAO,EAAP;AAAY,GALtD;AAAA;;AAOA,MAAMC,YAAN,CAAmB;AACf9B,EAAAA,WAAW,CAAC+B,UAAD,EAAa;AACpB,SAAKA,UAAL,GAAkBA,UAAlB;AACH;;AACDC,EAAAA,SAAS,CAACC,CAAD,EAAI;AACT,QAAIA,CAAJ,EAAO;AACH,aAAO,KAAKF,UAAL,CAAgBG,uBAAhB,CAAwCD,CAAxC,CAAP;AACH;AACJ;;AARc;;AAUnBH,YAAY,CAACJ,IAAb;AAAA,mBAAyGI,YAAzG,EAnBoG1D,EAmBpG,mBAAuIoB,EAAE,CAAC2C,YAA1I;AAAA;;AACAL,YAAY,CAACM,KAAb,kBApBoGhE,EAoBpG;AAAA;AAAA,QAAuG0D,YAAvG;AAAA;AAAA;;AACA;AAAA,qDArBoG1D,EAqBpG,mBAA2F0D,YAA3F,EAAqH,CAAC;AAC1GzB,IAAAA,IAAI,EAAE/B,IADoG;AAE1GsD,IAAAA,IAAI,EAAE,CAAC;AACChB,MAAAA,IAAI,EAAE;AADP,KAAD;AAFoG,GAAD,CAArH,EAK4B,YAAY;AAAE,WAAO,CAAC;AAAEP,MAAAA,IAAI,EAAEb,EAAE,CAAC2C;AAAX,KAAD,CAAP;AAAqC,GAL/E;AAAA;;AAOA,MAAME,mBAAN,CAA0B;AACtB;AACJ;AACA;AACA;AACA;AACIrC,EAAAA,WAAW,CAACsC,cAAD,EAAiBC,cAAjB,EAAiCC,UAAjC,EAA6C;AACpD,SAAKF,cAAL,GAAsBA,cAAtB;AACA,SAAKC,cAAL,GAAsBA,cAAtB;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACA;AACR;AACA;AACA;AACA;AACA;;AACQ,SAAKC,gBAAL,GAAwB,KAAxB;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAKxB,OAAL,GAAe,IAAIlB,UAAJ,EAAf;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAK2C,aAAL,GAAqB,IAAI5D,OAAJ,EAArB;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAK6D,iBAAL,GAAyB,MAAM;AAC3B,WAAK1B,OAAL,GAAelB,UAAU,CAACK,MAAX,CAAkB;AAC7BQ,QAAAA,IAAI,EAAE,KAAKA,IADkB;AAE7BgC,QAAAA,OAAO,EAAE,KAAKA,OAFe;AAG7BC,QAAAA,IAAI,EAAE,KAAKA,IAHkB;AAI7BC,QAAAA,KAAK,EAAE,KAAKA,KAJiB;AAK7BzC,QAAAA,IAAI,EAAE,KAAKA,IALkB;AAM7B0C,QAAAA,UAAU,EAAE,KAAKA,UANY;AAO7BC,QAAAA,QAAQ,EAAE,KAAKA,QAPc;AAQ7BC,QAAAA,QAAQ,EAAE,KAAKA,QARc;AAS7BjC,QAAAA,IAAI,EAAE,KAAKA,IATkB;AAU7BkC,QAAAA,MAAM,EAAE,KAAKA,MAVgB;AAW7BC,QAAAA,QAAQ,EAAE,KAAKA,QAXc;AAY7BC,QAAAA,WAAW,EAAE,KAAKA;AAZW,OAAlB,CAAf;AAcH,KAfD;;AAgBA,SAAKR,OAAL,GAAelD,QAAQ,CAACC,QAAxB;AACA,SAAKuD,MAAL,GAAcxD,QAAQ,CAACG,OAAvB;AACA,SAAKiD,KAAL,GAAapD,QAAQ,CAACE,aAAtB;AACA,SAAKiD,IAAL,GAAY,OAAZ;AACA,SAAKE,UAAL,GAAkB,IAAlB;AACA,SAAKnC,IAAL,GAAYd,eAAZ;AACA,SAAKqD,QAAL,GAAgB,IAAhB;AACA,SAAKC,WAAL,GAAmB,KAAnB;AACA,SAAKJ,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKjC,IAAL,GAAY,KAAZ;AACH;;AACDqC,EAAAA,mBAAmB,CAACC,KAAD,EAAQ;AACvB,QAAI,KAAKC,UAAL,IAAmB,KAAKA,UAAL,CAAgBC,aAAvC,EAAsD;AAClD,UAAI,KAAKT,UAAL,IAAoB,CAAC,KAAKA,UAAN,IAAoB,KAAKU,aAAL,CAAmBH,KAAK,CAACI,MAAzB,CAA5C,EAA+E;AAC3EJ,QAAAA,KAAK,CAACK,WAAN,GAAoB,KAApB;AACAL,QAAAA,KAAK,CAACM,cAAN;AACH;AACJ;AACJ;;AACDC,EAAAA,cAAc,GAAG;AACb,SAAKvB,cAAL,CAAoB3B,UAApB,CAA+B,KAAKC,IAApC,EACKE,IADL,CACU9B,SAAS,CAAC,KAAK0D,aAAN,CADnB,EAEKoB,SAFL,CAEgB7C,OAAD,IAAa;AACxB,WAAK0B,iBAAL;AACAzC,MAAAA,MAAM,CAACC,MAAP,CAAc,KAAKc,OAAnB,EAA4BA,OAA5B;;AACA,UAAIA,OAAO,CAACD,IAAZ,EAAkB;AACd,aAAK+C,aAAL;AACH;;AACD,WAAKxB,cAAL,CAAoByB,aAApB;AACH,KATD;AAUH;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,QAAQ,GAAG;AACP,SAAKtB,iBAAL;AACA,SAAKkB,cAAL;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIJ,EAAAA,aAAa,CAACS,OAAD,EAAU;AACnB,QAAIA,OAAO,KAAK,KAAK1B,UAAL,CAAgBgB,aAAhB,CAA8BW,aAA9C,EAA6D;AACzD,aAAO,IAAP;AACH;;AACD,WAAOD,OAAO,CAACE,UAAR,IAAsB,KAAKX,aAAL,CAAmBS,OAAO,CAACE,UAA3B,CAA7B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,SAAK,MAAMC,QAAX,IAAuBD,OAAvB,EAAgC;AAC5B,UAAIC,QAAJ,EAAc;AACV,cAAMC,WAAW,GAAGF,OAAO,CAACC,QAAD,CAA3B;;AACA,YAAIC,WAAW,CAACC,aAAZ,EAAJ,EAAiC;AAC7B;AACH,SAFD,MAGK,IAAI,OAAOD,WAAW,CAACE,YAAnB,KAAoC,WAApC,IAAmDF,WAAW,CAACE,YAAZ,KAA6BF,WAAW,CAACG,aAAhG,EAA+G;AAChH,cAAIH,WAAW,CAACE,YAAZ,KAA6B,EAAjC,EAAqC;AACjC,iBAAKzD,OAAL,CAAasD,QAAb,IAAyBC,WAAW,CAACE,YAArC;;AACA,gBAAIH,QAAQ,KAAK,aAAjB,EAAgC;AAC5B,kBAAIC,WAAW,CAACE,YAAhB,EAA8B;AAC1B,qBAAKpC,cAAL,CAAoBtB,IAApB,CAAyB,KAAKC,OAAL,CAAaL,IAAtC,EAA4C,KAAKK,OAAjD;AACH,eAFD,MAGK;AACD,qBAAKqB,cAAL,CAAoBd,IAApB,CAAyB,KAAKP,OAAL,CAAaL,IAAtC;AACH;AACJ;;AACD,gBAAI2D,QAAQ,KAAK,MAAjB,EAAyB;AACrB,mBAAKV,cAAL;AACH;AACJ;AACJ;AACJ;AACJ;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACIe,EAAAA,QAAQ,CAACvE,IAAD,EAAOwC,IAAP,EAAa;AACjB,SAAK5B,OAAL,CAAagC,QAAb,GAAwBxD,OAAO,CAACY,IAAD,CAA/B;AACA,SAAKY,OAAL,CAAa+B,QAAb,GAAwB6B,KAAK,CAAC,KAAK5D,OAAL,CAAagC,QAAd,CAAL,CAA6B6B,IAA7B,CAAkC,CAAlC,EAAqCC,GAArC,CAAyC,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAnD,CAAxB;AACA,QAAIC,SAAS,GAAG,EAAhB;;AACA,YAAQrC,IAAI,CAACsC,WAAL,EAAR;AACI,WAAK,OAAL;AACID,QAAAA,SAAS,GAAG,OAAZ;AACA;;AACJ,WAAK,QAAL;AACIA,QAAAA,SAAS,GAAG,OAAZ;AACA;;AACJ,WAAK,OAAL;AACIA,QAAAA,SAAS,GAAG,OAAZ;AACA;;AACJ;AACI;AAXR;;AAaA,WAAO,QAAQ7E,IAAR,GAAe,GAAf,GAAqB6E,SAA5B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACInB,EAAAA,aAAa,GAAG;AACZ,SAAK9C,OAAL,CAAamE,KAAb,GAAqB,KAAKR,QAAL,CAAc,KAAK3D,OAAL,CAAaZ,IAA3B,EAAiC,KAAKY,OAAL,CAAa4B,IAA9C,CAArB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIwC,EAAAA,WAAW,GAAG;AACV,SAAK3C,aAAL,CAAmBnB,IAAnB;AACA,SAAKmB,aAAL,CAAmB4C,QAAnB;AACH;;AA/KqB;;AAiL1BjD,mBAAmB,CAACX,IAApB;AAAA,mBAAgHW,mBAAhH,EA7MoGjE,EA6MpG,mBAAqJqC,iBAArJ,GA7MoGrC,EA6MpG,mBAAmLA,EAAE,CAACmH,iBAAtL,GA7MoGnH,EA6MpG,mBAAoNA,EAAE,CAACoH,UAAvN;AAAA;;AACAnD,mBAAmB,CAACoD,IAApB,kBA9MoGrH,EA8MpG;AAAA,QAAoGiE,mBAApG;AAAA;AAAA;AAAA;AA9MoGjE,MAAAA,EA8MpG;AAAA;;AAAA;AAAA;;AA9MoGA,MAAAA,EA8MpG,qBA9MoGA,EA8MpG;AAAA;AAAA;AAAA;AAAA;AA9MoGA,MAAAA,EA8MpG;AAAA,eAAoG,+BAApG;AAAA,gBA9MoGA,EA8MpG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA9MoGA,EA8MpG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA9MoGA,MAAAA,EA8MpG;AA9MoGA,MAAAA,EA8M6e,mEAAjlB;AAAA;;AAAA;AA9MoGA,MAAAA,EA8MwiB,qCAA5oB;AAAA;AAAA;AAAA,eAA0+CkB,EAAE,CAACoG,IAA7+C,EAA2jDpG,EAAE,CAACqG,OAA9jD;AAAA,UAAmrD7D,YAAnrD;AAAA;AAAA;AAAA,eAA+sD,CACvsD7C,OAAO,CAAC,QAAD,EAAW,CACdC,KAAK,CAAC,IAAD,EAAOC,KAAK,CAAC;AAAEyG,MAAAA,OAAO,EAAE;AAAX,KAAD,CAAZ,CADS,EAEdxG,UAAU,CAAC,QAAD,EAAW,CACjBD,KAAK,CAAC;AAAEyG,MAAAA,OAAO,EAAE;AAAX,KAAD,CADY,EAEjBvG,OAAO,CAAC,GAAD,CAFU,CAAX,CAFI,EAMdD,UAAU,CAAC,QAAD,EAAWC,OAAO,CAAC,GAAD,EAAMF,KAAK,CAAC;AAAEyG,MAAAA,OAAO,EAAE;AAAX,KAAD,CAAX,CAAlB,CANI,CAAX,CADgsD;AAA/sD;AAAA;AAAA;;AAUA;AAAA,qDAxNoGxH,EAwNpG,mBAA2FiE,mBAA3F,EAA4H,CAAC;AACjHhC,IAAAA,IAAI,EAAE9B,SAD2G;AAEjHqD,IAAAA,IAAI,EAAE,CAAC;AAAEiE,MAAAA,QAAQ,EAAE,aAAZ;AAA2BC,MAAAA,eAAe,EAAEtH,uBAAuB,CAACuH,MAApE;AAA4EC,MAAAA,UAAU,EAAE,CACnF/G,OAAO,CAAC,QAAD,EAAW,CACdC,KAAK,CAAC,IAAD,EAAOC,KAAK,CAAC;AAAEyG,QAAAA,OAAO,EAAE;AAAX,OAAD,CAAZ,CADS,EAEdxG,UAAU,CAAC,QAAD,EAAW,CACjBD,KAAK,CAAC;AAAEyG,QAAAA,OAAO,EAAE;AAAX,OAAD,CADY,EAEjBvG,OAAO,CAAC,GAAD,CAFU,CAAX,CAFI,EAMdD,UAAU,CAAC,QAAD,EAAWC,OAAO,CAAC,GAAD,EAAMF,KAAK,CAAC;AAAEyG,QAAAA,OAAO,EAAE;AAAX,OAAD,CAAX,CAAlB,CANI,CAAX,CAD4E,CAAxF;AASIzC,MAAAA,QAAQ,EAAE,omBATd;AASonB8C,MAAAA,MAAM,EAAE,CAAC,mRAAD;AAT5nB,KAAD;AAF2G,GAAD,CAA5H,EAY4B,YAAY;AAAE,WAAO,CAAC;AAAE5F,MAAAA,IAAI,EAAEI;AAAR,KAAD,EAA8B;AAAEJ,MAAAA,IAAI,EAAEjC,EAAE,CAACmH;AAAX,KAA9B,EAA8D;AAAElF,MAAAA,IAAI,EAAEjC,EAAE,CAACoH;AAAX,KAA9D,CAAP;AAAgG,GAZ1I,EAY4J;AAAE5C,IAAAA,OAAO,EAAE,CAAC;AACxJvC,MAAAA,IAAI,EAAE5B;AADkJ,KAAD,CAAX;AAE5IoE,IAAAA,IAAI,EAAE,CAAC;AACPxC,MAAAA,IAAI,EAAE5B;AADC,KAAD,CAFsI;AAI5IqE,IAAAA,KAAK,EAAE,CAAC;AACRzC,MAAAA,IAAI,EAAE5B;AADE,KAAD,CAJqI;AAM5I4B,IAAAA,IAAI,EAAE,CAAC;AACPA,MAAAA,IAAI,EAAE5B;AADC,KAAD,CANsI;AAQ5IsE,IAAAA,UAAU,EAAE,CAAC;AACb1C,MAAAA,IAAI,EAAE5B;AADO,KAAD,CARgI;AAU5ImC,IAAAA,IAAI,EAAE,CAAC;AACPP,MAAAA,IAAI,EAAE5B;AADC,KAAD,CAVsI;AAY5IyE,IAAAA,MAAM,EAAE,CAAC;AACT7C,MAAAA,IAAI,EAAE5B;AADG,KAAD,CAZoI;AAc5I0E,IAAAA,QAAQ,EAAE,CAAC;AACX9C,MAAAA,IAAI,EAAE5B;AADK,KAAD,CAdkI;AAgB5I2E,IAAAA,WAAW,EAAE,CAAC;AACd/C,MAAAA,IAAI,EAAE5B;AADQ,KAAD,CAhB+H;AAkB5IgE,IAAAA,gBAAgB,EAAE,CAAC;AACnBpC,MAAAA,IAAI,EAAE5B;AADa,KAAD,CAlB0H;AAoB5I8E,IAAAA,UAAU,EAAE,CAAC;AACblD,MAAAA,IAAI,EAAE3B,SADO;AAEbkD,MAAAA,IAAI,EAAE,CAAC,SAAD;AAFO,KAAD,CApBgI;AAuB5IyB,IAAAA,mBAAmB,EAAE,CAAC;AACtBhD,MAAAA,IAAI,EAAE1B,YADgB;AAEtBiD,MAAAA,IAAI,EAAE,CAAC,kBAAD,EAAqB,CAAC,QAAD,CAArB;AAFgB,KAAD;AAvBuH,GAZ5J;AAAA;;AAwCA,MAAMsE,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAACxE,IAAjB;AAAA,mBAA6GwE,gBAA7G;AAAA;;AACAA,gBAAgB,CAACC,IAAjB,kBAnQoG/H,EAmQpG;AAAA,QAA8G8H;AAA9G;AACAA,gBAAgB,CAACE,IAAjB,kBApQoGhI,EAoQpG;AAAA,YAA0I,CAC9HmB,YAD8H,CAA1I;AAAA;;AAGA;AAAA,qDAvQoGnB,EAuQpG,mBAA2F8H,gBAA3F,EAAyH,CAAC;AAC9G7F,IAAAA,IAAI,EAAEzB,QADwG;AAE9GgD,IAAAA,IAAI,EAAE,CAAC;AACCyE,MAAAA,OAAO,EAAE,CACL9G,YADK,CADV;AAIC+G,MAAAA,YAAY,EAAE,CAACjE,mBAAD,EAAsBP,YAAtB,CAJf;AAKCyE,MAAAA,OAAO,EAAE,CAAClE,mBAAD;AALV,KAAD;AAFwG,GAAD,CAAzH;AAAA;AAWA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS3C,QAAT,EAAmBD,OAAnB,EAA4BM,UAA5B,EAAwCsC,mBAAxC,EAA6D6D,gBAA7D,EAA+EzF,iBAA/E,EAAkGX,eAAlG", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Pipe, Component, ChangeDetectionStrategy, Input, ViewChild, HostListener, NgModule } from '@angular/core';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport { filter, takeUntil } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/platform-browser';\n\nconst LOADERS = {\n    'ball-8bits': 16,\n    'ball-atom': 4,\n    'ball-beat': 3,\n    'ball-circus': 5,\n    'ball-climbing-dot': 4,\n    'ball-clip-rotate': 1,\n    'ball-clip-rotate-multiple': 2,\n    'ball-clip-rotate-pulse': 2,\n    'ball-elastic-dots': 5,\n    'ball-fall': 3,\n    'ball-fussion': 4,\n    'ball-grid-beat': 9,\n    'ball-grid-pulse': 9,\n    'ball-newton-cradle': 4,\n    'ball-pulse': 3,\n    'ball-pulse-rise': 5,\n    'ball-pulse-sync': 3,\n    'ball-rotate': 1,\n    'ball-running-dots': 5,\n    'ball-scale': 1,\n    'ball-scale-multiple': 3,\n    'ball-scale-pulse': 2,\n    'ball-scale-ripple': 1,\n    'ball-scale-ripple-multiple': 3,\n    'ball-spin': 8,\n    'ball-spin-clockwise': 8,\n    'ball-spin-clockwise-fade': 8,\n    'ball-spin-clockwise-fade-rotating': 8,\n    'ball-spin-fade': 8,\n    'ball-spin-fade-rotating': 8,\n    'ball-spin-rotate': 2,\n    'ball-square-clockwise-spin': 8,\n    'ball-square-spin': 8,\n    'ball-triangle-path': 3,\n    'ball-zig-zag': 2,\n    'ball-zig-zag-deflect': 2,\n    'cog': 1,\n    'cube-transition': 2,\n    'fire': 3,\n    'line-scale': 5,\n    'line-scale-party': 5,\n    'line-scale-pulse-out': 5,\n    'line-scale-pulse-out-rapid': 5,\n    'line-spin-clockwise-fade': 8,\n    'line-spin-clockwise-fade-rotating': 8,\n    'line-spin-fade': 8,\n    'line-spin-fade-rotating': 8,\n    'pacman': 6,\n    'square-jelly-box': 2,\n    'square-loader': 1,\n    'square-spin': 1,\n    'timer': 1,\n    'triangle-skew-spin': 1\n};\nconst DEFAULTS = {\n    BD_COLOR: 'rgba(51,51,51,0.8)',\n    SPINNER_COLOR: '#fff',\n    Z_INDEX: 99999,\n};\nconst PRIMARY_SPINNER = 'primary';\nclass NgxSpinner {\n    constructor(init) {\n        Object.assign(this, init);\n    }\n    static create(init) {\n        if (init?.type == null || init.type.length === 0) {\n            console.warn(`[ngx-spinner]: Property \"type\" is missed. Please, provide animation type to <ngx-spinner> component\n        and ensure css is added to angular.json file`);\n        }\n        return new NgxSpinner(init);\n    }\n}\n\nclass NgxSpinnerService {\n    /**\n     * Creates an instance of NgxSpinnerService.\n     * @memberof NgxSpinnerService\n     */\n    constructor() {\n        /**\n         * Spinner observable\n         *\n         * @memberof NgxSpinnerService\n         */\n        // private spinnerObservable = new ReplaySubject<NgxSpinner>(1);\n        this.spinnerObservable = new BehaviorSubject(null);\n    }\n    /**\n    * Get subscription of desired spinner\n    * @memberof NgxSpinnerService\n    **/\n    getSpinner(name) {\n        return this.spinnerObservable.asObservable().pipe(filter((x) => x && x.name === name));\n    }\n    /**\n     * To show spinner\n     *\n     * @memberof NgxSpinnerService\n     */\n    show(name = PRIMARY_SPINNER, spinner) {\n        return new Promise((resolve, _reject) => {\n            setTimeout(() => {\n                if (spinner && Object.keys(spinner).length) {\n                    spinner['name'] = name;\n                    this.spinnerObservable.next(new NgxSpinner({ ...spinner, show: true }));\n                    resolve(true);\n                }\n                else {\n                    this.spinnerObservable.next(new NgxSpinner({ name, show: true }));\n                    resolve(true);\n                }\n            }, 10);\n        });\n    }\n    /**\n    * To hide spinner\n    *\n    * @memberof NgxSpinnerService\n    */\n    hide(name = PRIMARY_SPINNER, debounce = 10) {\n        return new Promise((resolve, _reject) => {\n            setTimeout(() => {\n                this.spinnerObservable.next(new NgxSpinner({ name, show: false }));\n                resolve(true);\n            }, debounce);\n        });\n    }\n}\nNgxSpinnerService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NgxSpinnerService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nNgxSpinnerService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NgxSpinnerService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NgxSpinnerService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: function () { return []; } });\n\nclass SafeHtmlPipe {\n    constructor(_sanitizer) {\n        this._sanitizer = _sanitizer;\n    }\n    transform(v) {\n        if (v) {\n            return this._sanitizer.bypassSecurityTrustHtml(v);\n        }\n    }\n}\nSafeHtmlPipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: SafeHtmlPipe, deps: [{ token: i1.DomSanitizer }], target: i0.ɵɵFactoryTarget.Pipe });\nSafeHtmlPipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: SafeHtmlPipe, name: \"safeHtml\" });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: SafeHtmlPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'safeHtml'\n                }]\n        }], ctorParameters: function () { return [{ type: i1.DomSanitizer }]; } });\n\nclass NgxSpinnerComponent {\n    /**\n     * Creates an instance of NgxSpinnerComponent.\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    constructor(spinnerService, changeDetector, elementRef) {\n        this.spinnerService = spinnerService;\n        this.changeDetector = changeDetector;\n        this.elementRef = elementRef;\n        /**\n         * To enable/disable animation\n         *\n         * @type {boolean}\n         * @memberof NgxSpinnerComponent\n         */\n        this.disableAnimation = false;\n        /**\n         * Spinner Object\n         *\n         * @memberof NgxSpinnerComponent\n         */\n        this.spinner = new NgxSpinner();\n        /**\n         * Unsubscribe from spinner's observable\n         *\n         * @memberof NgxSpinnerComponent\n        **/\n        this.ngUnsubscribe = new Subject();\n        /**\n         * To set default ngx-spinner options\n         *\n         * @memberof NgxSpinnerComponent\n         */\n        this.setDefaultOptions = () => {\n            this.spinner = NgxSpinner.create({\n                name: this.name,\n                bdColor: this.bdColor,\n                size: this.size,\n                color: this.color,\n                type: this.type,\n                fullScreen: this.fullScreen,\n                divArray: this.divArray,\n                divCount: this.divCount,\n                show: this.show,\n                zIndex: this.zIndex,\n                template: this.template,\n                showSpinner: this.showSpinner\n            });\n        };\n        this.bdColor = DEFAULTS.BD_COLOR;\n        this.zIndex = DEFAULTS.Z_INDEX;\n        this.color = DEFAULTS.SPINNER_COLOR;\n        this.size = 'large';\n        this.fullScreen = true;\n        this.name = PRIMARY_SPINNER;\n        this.template = null;\n        this.showSpinner = false;\n        this.divArray = [];\n        this.divCount = 0;\n        this.show = false;\n    }\n    handleKeyboardEvent(event) {\n        if (this.spinnerDOM && this.spinnerDOM.nativeElement) {\n            if (this.fullScreen || (!this.fullScreen && this.isSpinnerZone(event.target))) {\n                event.returnValue = false;\n                event.preventDefault();\n            }\n        }\n    }\n    initObservable() {\n        this.spinnerService.getSpinner(this.name)\n            .pipe(takeUntil(this.ngUnsubscribe))\n            .subscribe((spinner) => {\n            this.setDefaultOptions();\n            Object.assign(this.spinner, spinner);\n            if (spinner.show) {\n                this.onInputChange();\n            }\n            this.changeDetector.detectChanges();\n        });\n    }\n    /**\n     * Initialization method\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    ngOnInit() {\n        this.setDefaultOptions();\n        this.initObservable();\n    }\n    /**\n     * To check event triggers inside the Spinner Zone\n     *\n     * @param {*} element\n     * @returns {boolean}\n     * @memberof NgxSpinnerComponent\n     */\n    isSpinnerZone(element) {\n        if (element === this.elementRef.nativeElement.parentElement) {\n            return true;\n        }\n        return element.parentNode && this.isSpinnerZone(element.parentNode);\n    }\n    /**\n     * On changes event for input variables\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    ngOnChanges(changes) {\n        for (const propName in changes) {\n            if (propName) {\n                const changedProp = changes[propName];\n                if (changedProp.isFirstChange()) {\n                    return;\n                }\n                else if (typeof changedProp.currentValue !== 'undefined' && changedProp.currentValue !== changedProp.previousValue) {\n                    if (changedProp.currentValue !== '') {\n                        this.spinner[propName] = changedProp.currentValue;\n                        if (propName === 'showSpinner') {\n                            if (changedProp.currentValue) {\n                                this.spinnerService.show(this.spinner.name, this.spinner);\n                            }\n                            else {\n                                this.spinnerService.hide(this.spinner.name);\n                            }\n                        }\n                        if (propName === 'name') {\n                            this.initObservable();\n                        }\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * To get class for spinner\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    getClass(type, size) {\n        this.spinner.divCount = LOADERS[type];\n        this.spinner.divArray = Array(this.spinner.divCount).fill(0).map((_, i) => i);\n        let sizeClass = '';\n        switch (size.toLowerCase()) {\n            case 'small':\n                sizeClass = 'la-sm';\n                break;\n            case 'medium':\n                sizeClass = 'la-2x';\n                break;\n            case 'large':\n                sizeClass = 'la-3x';\n                break;\n            default:\n                break;\n        }\n        return 'la-' + type + ' ' + sizeClass;\n    }\n    /**\n     * Check if input variables have changed\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    onInputChange() {\n        this.spinner.class = this.getClass(this.spinner.type, this.spinner.size);\n    }\n    /**\n     * Component destroy event\n     *\n     * @memberof NgxSpinnerComponent\n     */\n    ngOnDestroy() {\n        this.ngUnsubscribe.next();\n        this.ngUnsubscribe.complete();\n    }\n}\nNgxSpinnerComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NgxSpinnerComponent, deps: [{ token: NgxSpinnerService }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nNgxSpinnerComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.3\", type: NgxSpinnerComponent, selector: \"ngx-spinner\", inputs: { bdColor: \"bdColor\", size: \"size\", color: \"color\", type: \"type\", fullScreen: \"fullScreen\", name: \"name\", zIndex: \"zIndex\", template: \"template\", showSpinner: \"showSpinner\", disableAnimation: \"disableAnimation\" }, host: { listeners: { \"document:keydown\": \"handleKeyboardEvent($event)\" } }, viewQueries: [{ propertyName: \"spinnerDOM\", first: true, predicate: [\"overlay\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<div [@.disabled]=\\\"disableAnimation\\\" [@fadeIn]=\\\"'in'\\\" *ngIf=\\\"spinner.show\\\" class=\\\"ngx-spinner-overlay\\\"\\n  [style.background-color]=\\\"spinner.bdColor\\\" [style.z-index]=\\\"spinner.zIndex\\\"\\n  [style.position]=\\\"spinner.fullScreen ? 'fixed' : 'absolute'\\\" #overlay>\\n  <div *ngIf=\\\"!template\\\" [class]=\\\"spinner.class\\\" [style.color]=\\\"spinner.color\\\">\\n    <div *ngFor=\\\"let index of spinner.divArray\\\"></div>\\n  </div>\\n  <div *ngIf=\\\"template\\\" [innerHTML]=\\\"template | safeHtml\\\"></div>\\n  <div class=\\\"loading-text\\\" [style.z-index]=\\\"spinner.zIndex\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</div>\", styles: [\".ngx-spinner-overlay{position:fixed;top:0;left:0;width:100%;height:100%}.ngx-spinner-overlay>div:not(.loading-text){top:50%;left:50%;margin:0;position:absolute;transform:translate(-50%,-50%)}.loading-text{position:absolute;top:60%;left:50%;transform:translate(-50%,-60%)}\\n\"], directives: [{ type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], pipes: { \"safeHtml\": SafeHtmlPipe }, animations: [\n        trigger('fadeIn', [\n            state('in', style({ opacity: 1 })),\n            transition(':enter', [\n                style({ opacity: 0 }),\n                animate(300)\n            ]),\n            transition(':leave', animate(200, style({ opacity: 0 })))\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NgxSpinnerComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ngx-spinner', changeDetection: ChangeDetectionStrategy.OnPush, animations: [\n                        trigger('fadeIn', [\n                            state('in', style({ opacity: 1 })),\n                            transition(':enter', [\n                                style({ opacity: 0 }),\n                                animate(300)\n                            ]),\n                            transition(':leave', animate(200, style({ opacity: 0 })))\n                        ])\n                    ], template: \"<div [@.disabled]=\\\"disableAnimation\\\" [@fadeIn]=\\\"'in'\\\" *ngIf=\\\"spinner.show\\\" class=\\\"ngx-spinner-overlay\\\"\\n  [style.background-color]=\\\"spinner.bdColor\\\" [style.z-index]=\\\"spinner.zIndex\\\"\\n  [style.position]=\\\"spinner.fullScreen ? 'fixed' : 'absolute'\\\" #overlay>\\n  <div *ngIf=\\\"!template\\\" [class]=\\\"spinner.class\\\" [style.color]=\\\"spinner.color\\\">\\n    <div *ngFor=\\\"let index of spinner.divArray\\\"></div>\\n  </div>\\n  <div *ngIf=\\\"template\\\" [innerHTML]=\\\"template | safeHtml\\\"></div>\\n  <div class=\\\"loading-text\\\" [style.z-index]=\\\"spinner.zIndex\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</div>\", styles: [\".ngx-spinner-overlay{position:fixed;top:0;left:0;width:100%;height:100%}.ngx-spinner-overlay>div:not(.loading-text){top:50%;left:50%;margin:0;position:absolute;transform:translate(-50%,-50%)}.loading-text{position:absolute;top:60%;left:50%;transform:translate(-50%,-60%)}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: NgxSpinnerService }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }]; }, propDecorators: { bdColor: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], fullScreen: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], zIndex: [{\n                type: Input\n            }], template: [{\n                type: Input\n            }], showSpinner: [{\n                type: Input\n            }], disableAnimation: [{\n                type: Input\n            }], spinnerDOM: [{\n                type: ViewChild,\n                args: ['overlay']\n            }], handleKeyboardEvent: [{\n                type: HostListener,\n                args: ['document:keydown', ['$event']]\n            }] } });\n\nclass NgxSpinnerModule {\n}\nNgxSpinnerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NgxSpinnerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNgxSpinnerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NgxSpinnerModule, declarations: [NgxSpinnerComponent, SafeHtmlPipe], imports: [CommonModule], exports: [NgxSpinnerComponent] });\nNgxSpinnerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NgxSpinnerModule, imports: [[\n            CommonModule\n        ]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NgxSpinnerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule\n                    ],\n                    declarations: [NgxSpinnerComponent, SafeHtmlPipe],\n                    exports: [NgxSpinnerComponent]\n                }]\n        }] });\n\n/*\n * Public API Surface of ngx-spinner\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DEFAULTS, LOADERS, NgxSpinner, NgxSpinnerComponent, NgxSpinnerModule, NgxSpinnerService, PRIMARY_SPINNER };\n"]}, "metadata": {}, "sourceType": "module"}
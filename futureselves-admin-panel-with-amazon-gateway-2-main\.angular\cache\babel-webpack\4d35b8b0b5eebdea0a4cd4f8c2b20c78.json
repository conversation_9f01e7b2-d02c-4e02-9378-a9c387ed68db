{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { Validators } from '@angular/forms';\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\nimport * as i3 from \"ngx-spinner\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../../../sidebar.component\";\nimport * as i8 from \"ngx-mask\";\nimport * as i9 from \"ngx-image-cropper\";\nconst _c0 = [\"audioInput\"];\nconst _c1 = [\"audioPlayer\"];\n\nfunction AddEmployersComponent_img_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 53);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.imageSrc, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction AddEmployersComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEmployersComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \" The image must have a 1:1 aspect ratio. Please select a square image. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEmployersComponent_a_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 55);\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_a_22_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return ctx_r12.showCropper(\"CO_logo\");\n    });\n    i0.ɵɵtext(1, \"Edit Logo\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEmployersComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelementStart(1, \"div\", 57);\n    i0.ɵɵelementStart(2, \"h5\", 58);\n    i0.ɵɵtext(3, \"Resize Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"image-cropper\", 59);\n    i0.ɵɵlistener(\"imageCropped\", function AddEmployersComponent_div_23_Template_image_cropper_imageCropped_4_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return ctx_r14.cropImg($event);\n    })(\"imageLoaded\", function AddEmployersComponent_div_23_Template_image_cropper_imageLoaded_4_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return ctx_r16.imgLoad();\n    })(\"cropperReady\", function AddEmployersComponent_div_23_Template_image_cropper_cropperReady_4_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return ctx_r17.initCropper();\n    })(\"loadImageFailed\", function AddEmployersComponent_div_23_Template_image_cropper_loadImageFailed_4_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return ctx_r18.imgFailed();\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_div_23_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return ctx_r19.saveCroppedImage(\"CO_logo\");\n    });\n    i0.ɵɵtext(6, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_div_23_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return ctx_r20.hideCropper(\"CO_logo\");\n    });\n    i0.ɵɵtext(8, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"imageChangedEvent\", ctx_r4.imgChangeEvt)(\"aspectRatio\", 1 / 1)(\"maintainAspectRatio\", true);\n  }\n}\n\nfunction AddEmployersComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelementStart(1, \"label\", 62);\n    i0.ɵɵtext(2, \"Specify Other Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 63);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEmployersComponent_option_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const sector_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", sector_r21.IN_id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(sector_r21.IN_name);\n  }\n}\n\nfunction AddEmployersComponent_div_60_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Website is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEmployersComponent_div_60_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid URL starting with http:// or https://. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEmployersComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, AddEmployersComponent_div_60_div_1_Template, 2, 0, \"div\", 65);\n    i0.ɵɵtemplate(2, AddEmployersComponent_div_60_div_2_Template, 2, 0, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r7.addNewCompanyForm.get(\"CO_website\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r7.addNewCompanyForm.get(\"CO_website\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.pattern);\n  }\n}\n\nfunction AddEmployersComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \" Error: Character limit exceeded! (500 Characters) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c2 = function (a0, a1) {\n  return {\n    \"btn-outline-primary\": a0,\n    \"btn-outline-secondary\": a1\n  };\n};\n\nfunction AddEmployersComponent_div_91_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_div_91_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const i_r25 = i0.ɵɵnextContext().index;\n      const ctx_r29 = i0.ɵɵnextContext();\n      return ctx_r29.toggleAudio(i_r25);\n    });\n    i0.ɵɵelement(1, \"i\", 85);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r25 = i0.ɵɵnextContext().index;\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c2, !ctx_r26.isPlaying[i_r25], ctx_r26.isPlaying[i_r25]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r26.isPlaying[i_r25] ? \"fa-pause\" : \"fa-play\");\n  }\n}\n\nfunction AddEmployersComponent_div_91_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_div_91_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const i_r25 = i0.ɵɵnextContext().index;\n      const ctx_r33 = i0.ɵɵnextContext();\n      return ctx_r33.removeInsight(i_r25);\n    });\n    i0.ɵɵelement(1, \"i\", 87);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEmployersComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵelementStart(1, \"div\", 67);\n    i0.ɵɵelementStart(2, \"div\", 68);\n    i0.ɵɵelementStart(3, \"div\", 69);\n    i0.ɵɵelementStart(4, \"label\", 70);\n    i0.ɵɵtext(5, \"Insight Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 69);\n    i0.ɵɵelementStart(8, \"label\", 72);\n    i0.ɵɵtext(9, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 69);\n    i0.ɵɵelementStart(12, \"label\", 74);\n    i0.ɵɵtext(13, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 69);\n    i0.ɵɵelementStart(16, \"label\", 76);\n    i0.ɵɵtext(17, \"Upload Insight\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 77);\n    i0.ɵɵlistener(\"change\", function AddEmployersComponent_div_91_Template_input_change_18_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r37);\n      const i_r25 = restoredCtx.index;\n      const ctx_r36 = i0.ɵɵnextContext();\n      return ctx_r36.onAudioSelected($event, i_r25);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 78);\n    i0.ɵɵtemplate(20, AddEmployersComponent_div_91_button_20_Template, 2, 5, \"button\", 79);\n    i0.ɵɵtemplate(21, AddEmployersComponent_div_91_button_21_Template, 2, 0, \"button\", 80);\n    i0.ɵɵelementStart(22, \"audio\", 81, 82);\n    i0.ɵɵelement(24, \"source\", 83);\n    i0.ɵɵtext(25, \" Your browser does not support the audio element. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r25 = ctx.index;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroupName\", i_r25);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.audioUrls[i_r25]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.insightFormArray.length > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r9.audioUrls[i_r25], i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction AddEmployersComponent_button_92_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_button_92_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return ctx_r38.addInsight();\n    });\n    i0.ɵɵtext(1, \"Add Insight \");\n    i0.ɵɵelement(2, \"i\", 89);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEmployersComponent_button_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵtext(1, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n} // function wordLimitValidator(maxWords: number) {\n//   return (control: AbstractControl): { [key: string]: any } | null => {\n//     if (control.value) {\n//       const words = control.value.trim().split(/\\s+/);\n//       if (words.length > maxWords) {\n//         console.log('maxwords', maxWords);\n//         return { wordLimitExceeded: true, wordCount: words.length };\n//       }\n//     }\n//     return null;\n//   };\n// }\n// function nonNegativeValidator(\n//   control: FormControl\n// ): { [key: string]: boolean } | null {\n//   console.log('Control : ', control);\n//   const value = control.value;\n//   if (value < 0 || value > 24) {\n//     return { negativeValue: true };\n//   }\n//   return null;\n// }\n\n\nexport class AddEmployersComponent {\n  constructor(formBuilder, dataTransferService, ngxSpinnerService, router, toastr, datePipe, route) {\n    var _a, _b;\n\n    this.formBuilder = formBuilder;\n    this.dataTransferService = dataTransferService;\n    this.ngxSpinnerService = ngxSpinnerService;\n    this.router = router;\n    this.toastr = toastr;\n    this.datePipe = datePipe;\n    this.route = route;\n    this.p = 1;\n    this.hideHeader = false;\n    this.viewInsight = true;\n    this.showForm = false;\n    this.submitted = false;\n    this.title = 'Add New';\n    this.isReadonly = false;\n    this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\n    this.imagedp = null; // Assuming this is used to display the uploaded image\n\n    this.invalidDates = new Set();\n    this.audioFiles = [];\n    this.characterCount = 0;\n    this.isPlaying = []; // Array to track playback state\n\n    this.audioUrls = []; // Array to store audio URLs\n\n    this.showOtherTypeInput = false;\n    this.existingAudioNames = [];\n    this.audioFileUrls = [];\n    this.isCropperVisible = false;\n    this.imgChangeEvt = \"\";\n    this.addNewCompanyForm = this.formBuilder.group({\n      CO_companyName: ['', [Validators.required]],\n      CO_logo: [null, [Validators.required]],\n      CO_location: ['', [Validators.required]],\n      CO_type: ['', [Validators.required]],\n      CO_otherType: [''],\n      CO_founded: ['', [Validators.required]],\n      CO_sectorId: ['', [Validators.required]],\n      CO_about: ['', [Validators.required, Validators.maxLength(200)]],\n      CO_website: ['', [Validators.required, Validators.pattern('https?://.+')]],\n      CO_size: ['', [Validators.required]],\n      CO_HrInsights: this.formBuilder.array([this.createInsight()])\n    });\n    this.route.queryParams.subscribe(params => {\n      if (params) {\n        this.CO_id = params['CO_id'];\n        console.log(this.CO_id);\n      }\n    });\n    const state = (_b = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras) === null || _b === void 0 ? void 0 : _b.state; //State values\n\n    if (state) {\n      this.title = state.title;\n      this.employerData = state.employerData;\n      console.log(\"employerData: \", this.employerData);\n    } else {\n      this.router.navigate([`actions/employer-opportunities`]);\n    }\n  }\n\n  ngOnInit() {\n    this.getSectorTitles();\n\n    if (this.title === 'Edit') {\n      this.getCompanyById(this.CO_id);\n    }\n  }\n\n  getCompanyById(companyId) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const res = yield _this.dataTransferService.getCompanyById(companyId).toPromise();\n        _this.employerData = res;\n        console.log(\"this.employerData\", res);\n\n        _this.populateForm(_this.employerData);\n      } catch (error) {\n        console.log(\"Error\", error);\n\n        _this.toastr.error(\"Unable to fetch data\");\n      }\n    })();\n  }\n\n  populateForm(data) {\n    var _a, _b, _c;\n\n    console.log(\"Data to patch to edit\", data);\n    const logoControl = this.addNewCompanyForm.get('CO_logo');\n\n    if (logoControl) {\n      logoControl.clearValidators();\n      logoControl.updateValueAndValidity();\n    } else {\n      console.log(\"CO_logo control not found\");\n    } // this.ExistingLogo = data?.CO_logo.substring(\n    //   data.CO_logo.lastIndexOf('/') + 1\n    // );\n\n\n    this.addNewCompanyForm.patchValue({\n      CO_companyName: data.CO_companyName,\n      CO_location: data.CO_location,\n      CO_type: data.CO_type,\n      CO_founded: data.CO_founded,\n      CO_sectorId: data.CO_sectorId,\n      CO_about: data.CO_about,\n      CO_website: data.CO_website,\n      CO_size: data.CO_size,\n      CO_id: data.CO_id\n    }); // Define the valid options\n\n    const validOptions = ['Private Limited Company(LTD)', 'Public Limited Company(PLC)', 'Partnership', 'Non-Profit Organization'];\n\n    if (validOptions.includes(data.CO_type)) {\n      (_a = this.addNewCompanyForm.get('CO_type')) === null || _a === void 0 ? void 0 : _a.setValue(data.CO_type);\n      this.showOtherTypeInput = false;\n    } else {\n      (_b = this.addNewCompanyForm.get('CO_type')) === null || _b === void 0 ? void 0 : _b.setValue('Other');\n      this.showOtherTypeInput = true; // Show the 'Other' input\n\n      (_c = this.addNewCompanyForm.get('CO_otherType')) === null || _c === void 0 ? void 0 : _c.setValue(data.CO_type);\n    }\n\n    this.imageSrc = data.CO_logo;\n\n    if (data.CO_HrInsights) {\n      const hrInsightsArray = this.addNewCompanyForm.get('CO_HrInsights');\n      hrInsightsArray.clear();\n      data.CO_HrInsights.forEach(insight => {\n        const insightFormGroup = this.formBuilder.group({\n          HRI_title: [insight.HRI_title, [Validators.required]],\n          HRI_name: [insight.HRI_name, [Validators.required]],\n          HRI_position: [insight.HRI_position, [Validators.required]],\n          HRI_link: [insight.HRI_link]\n        });\n        hrInsightsArray.push(insightFormGroup);\n      }); // Initialize audio URLs and playback states\n      // this.audioUrls = data.CO_HrInsights.map((insight: any) => insight.HRI_link);\n      // this.isPlaying = new Array(data.CO_HrInsights.length).fill(false);\n\n      this.audioUrls = data.CO_HrInsights.map(insight => insight.HRI_link);\n      this.isPlaying = new Array(data.CO_HrInsights.length).fill(false);\n      this.existingAudioNames = data === null || data === void 0 ? void 0 : data.CO_HrInsights.map(insight => {\n        var _a;\n\n        return (_a = insight === null || insight === void 0 ? void 0 : insight.HRI_link) === null || _a === void 0 ? void 0 : _a.substring(insight.HRI_link.lastIndexOf('/') + 1);\n      });\n    }\n  }\n\n  get insightFormArray() {\n    return this.addNewCompanyForm.get('CO_HrInsights');\n  }\n\n  onTextChange(event) {\n    var _a, _b;\n\n    const textarea = event.target;\n    this.characterCount = textarea.value.length;\n\n    if (this.characterCount > 500) {\n      (_a = this.addNewCompanyForm.get('CO_about')) === null || _a === void 0 ? void 0 : _a.setErrors({\n        maxlength: true\n      });\n    } else {\n      (_b = this.addNewCompanyForm.get('CO_about')) === null || _b === void 0 ? void 0 : _b.setErrors(null);\n    }\n  }\n\n  createInsight() {\n    return this.formBuilder.group({\n      HRI_title: ['', [Validators.required]],\n      HRI_name: ['', [Validators.required]],\n      HRI_position: ['', [Validators.required]],\n      HRI_link: [null]\n    });\n    this.isPlaying.push(false); // Initialize playback state for new row\n\n    this.audioUrls.push(''); // Initialize empty URL for new row\n  }\n\n  addInsight() {\n    this.insightFormArray.push(this.createInsight());\n  }\n\n  removeInsight(index) {\n    this.insightFormArray.removeAt(index);\n    this.audioUrls.splice(index, 1); // Remove URL for deleted row\n\n    this.isPlaying.splice(index, 1); // Remove playback state for deleted row\n\n    this.audioFiles.splice(index, 1); // Remove file object for deleted row\n  }\n\n  toggleAudio(index) {\n    const audioElements = this.audioPlayers.toArray();\n    const audioElement = audioElements[index].nativeElement; // Stop all other audio\n\n    this.stopAllAudio(index); // Toggle play/pause for the current audio element\n\n    if (this.isPlaying[index]) {\n      audioElement.pause();\n    } else {\n      audioElement.src = this.audioUrls[index];\n      audioElement.play();\n    } // Update the playback state\n\n\n    this.isPlaying[index] = !this.isPlaying[index];\n  }\n\n  stopAllAudio(currentIndex) {\n    this.audioPlayers.forEach((audioPlayer, index) => {\n      if (index !== currentIndex) {\n        audioPlayer.nativeElement.pause();\n        this.isPlaying[index] = false;\n      }\n    });\n  }\n\n  stopAudio(index) {\n    const audioElements = this.audioPlayers.toArray();\n    const audioElement = audioElements[index].nativeElement;\n    audioElement.pause();\n    audioElement.currentTime = 0; // Reset to the beginning\n\n    this.isPlaying[index] = false;\n  }\n\n  onFileSelected(event) {\n    var _a;\n\n    let selectedFile = event.target.files[0]; //to preview image and take file in imageName to pass in upload api\n\n    if (event.target.files.length === 0) {\n      // Reset both imageName and imageSrc when no file is selected\n      this.imageName = null;\n      this.imageSrc = null;\n      return;\n    }\n\n    const newFileName = FileValidator.addTimestamp(selectedFile.name);\n    this.imageName = new File([selectedFile], newFileName, {\n      type: selectedFile.type\n    });\n\n    if (this.imageName) {\n      const formControl = this.addNewCompanyForm.get('CO_logo');\n      formControl === null || formControl === void 0 ? void 0 : formControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\n      formControl === null || formControl === void 0 ? void 0 : formControl.updateValueAndValidity();\n    }\n\n    const fileType = this.imageName.type.split('/')[0];\n    const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n\n    if (fileType !== 'image' || fileExtension === 'svg') {\n      event.target.value = '';\n      this.toastr.info('Please select an image file (excluding SVG).');\n      this.imageName = null;\n      this.imageSrc = null;\n      return;\n    }\n\n    if (this.imageName && fileType == 'image') {\n      const reader = new FileReader();\n      const img = new Image();\n\n      reader.onload = e => {\n        var _a, _b, _c;\n\n        this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\n\n        if (!((_c = (_b = this.addNewCompanyForm.get('CO_logo')) === null || _b === void 0 ? void 0 : _b.errors) === null || _c === void 0 ? void 0 : _c.fileSizeValidator)) {\n          this.checkAspectRatio(img, 'CO_logo');\n          this.imgChangeEvt = {\n            target: {\n              files: [this.imageName]\n            }\n          };\n        }\n      };\n\n      ;\n      reader.readAsDataURL(this.imageName);\n    } else {\n      this.imageSrc = null; // Reset imageSrc if no file selected\n    }\n\n    console.log('imageName', this.imageName);\n  }\n\n  checkAspectRatio(image, controlName) {\n    const aspectRatio = image.width / image.height;\n    const control = this.addNewCompanyForm.get(controlName);\n\n    if (aspectRatio !== 1) {\n      // this.toastr.warning('The image must have a 1:1 aspect ratio. Please select a square image.', 'Invalid Aspect Ratio');\n      control === null || control === void 0 ? void 0 : control.setErrors({\n        fileAspectRatioValidator: true\n      });\n    } else {\n      control === null || control === void 0 ? void 0 : control.setErrors(null);\n    }\n  }\n\n  uploadLogoUrl() {\n    return new Promise((resolve, reject) => {\n      if (!this.imageName) {\n        reject('Please select an image.');\n        return;\n      }\n\n      console.log('Uploading image:', this.imageName);\n      this.dataTransferService.uploadurl(this.imageName).subscribe(res => {\n        const fileUrl = this.baseUrl + this.imageName.name; // Ensure you're concatenating correctly\n\n        resolve(fileUrl);\n      }, error => {\n        reject('Error uploading image: ' + error.message || error);\n      });\n    });\n  }\n\n  onAudioSelected(event, index) {\n    let audiofile;\n    const selectedFile = event.target.files[0];\n\n    if (selectedFile) {\n      const newFileName = FileValidator.addTimestamp(selectedFile.name);\n      audiofile = new File([selectedFile], newFileName, {\n        type: selectedFile.type\n      });\n    } else {\n      audiofile = null;\n    }\n\n    if (!audiofile) {\n      this.audioUrls[index] = '';\n      this.stopAudio(index);\n      return;\n    }\n\n    const audiofileType = audiofile.type.split('/')[0];\n\n    if (audiofileType !== 'audio') {\n      event.target.value = ''; // Clear the input\n\n      this.toastr.info('Please select an audio file.');\n      return;\n    } // Store the file object in the array for later upload\n\n\n    this.audioFiles[index] = audiofile;\n    console.log('Audio file :', audiofile);\n    const reader = new FileReader();\n\n    reader.onload = () => {\n      // Store audio URL\n      this.audioUrls[index] = reader.result;\n    };\n\n    reader.onerror = () => {\n      console.error('Error reading audio file');\n      this.toastr.error('Error reading audio file.');\n    };\n\n    reader.readAsDataURL(audiofile);\n  }\n\n  uploadAudioFiles(files) {\n    const uploadPromises = files.map((file, index) => this.uploadSingleAudioFile(file, index));\n    return Promise.all(uploadPromises);\n  } // Updated uploadSingleAudioFile function\n\n\n  uploadSingleAudioFile(file, index) {\n    return new Promise((resolve, reject) => {\n      this.dataTransferService.uploadurl(file).subscribe(res => {\n        console.log('Upload successful', file.name);\n        const fileUrl = this.baseUrl + file.name;\n        this.audioUrls[index] = fileUrl;\n        resolve(fileUrl);\n      }, error => {\n        console.error('Upload error', error);\n        this.toastr.error('Failed to upload audio file');\n        reject(error);\n      });\n    });\n  } // uploadAudioFiles(files: File[]): Promise<string[]> {\n  //   const uploadPromises = files.map(file => this.uploadSingleAudioFile(file));\n  //   return Promise.all(uploadPromises);\n  // }\n  // uploadSingleAudioFile(file: File): Promise<string> {\n  //   return new Promise((resolve, reject) => {\n  //     this.dataTransferService.uploadurl(file).subscribe(\n  //       (res: any) => {\n  //         console.log('Upload successful', file.name);\n  //         const fileUrl = this.baseUrl + file.name;\n  //         resolve(fileUrl);\n  //       },\n  //       (error: any) => {\n  //         console.error('Upload error', error);\n  //         this.toastr.error('Failed to upload audio file');\n  //         reject(error);\n  //       }\n  //     );\n  //   });\n  // }\n\n\n  onTypeChange(event) {\n    const selectElement = event.target;\n    this.showOtherTypeInput = selectElement.value === 'Other';\n  }\n\n  onSubmit() {\n    if (this.title === 'Edit') {\n      this.updateEmployer();\n    } else {\n      this.addEmployer();\n    }\n  }\n\n  updateEmployer() {\n    var _a, _b, _c;\n\n    if (!((_b = (_a = this.addNewCompanyForm.get('CO_logo')) === null || _a === void 0 ? void 0 : _a.errors) === null || _b === void 0 ? void 0 : _b.fileAspectRatioValidator)) {\n      const logoControl = this.addNewCompanyForm.get('CO_logo');\n\n      if (logoControl) {\n        logoControl.clearValidators();\n        logoControl.updateValueAndValidity();\n        console.log(\"Removed required validator from CO_logo\");\n      } else {\n        console.log(\"CO_logo control not found\");\n      }\n\n      if (this.addNewCompanyForm.invalid) {\n        Object.keys(this.addNewCompanyForm.controls).forEach(name => {\n          const control = this.addNewCompanyForm.get(name);\n\n          if (control === null || control === void 0 ? void 0 : control.invalid) {\n            console.log(`Invalid control: ${name}, Errors:`, control.errors);\n          }\n        });\n        this.toastr.info('Please fill all required fields correctly');\n        return;\n      }\n\n      const uploadLogo = ((_c = this.addNewCompanyForm.get('CO_logo')) === null || _c === void 0 ? void 0 : _c.value) ? this.uploadLogoUrl() : Promise.resolve(this.employerData.CO_logo);\n      this.ngxSpinnerService.show('globalSpinner');\n      uploadLogo.then(fileUrl => {\n        return this.uploadAudioFiles(this.audioFiles).then(audioUrls => {\n          var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n\n          console.log(\"Uploaded audio files:\", this.audioFiles);\n          this.insightFormArray.controls.forEach((control, index) => {\n            var _a;\n\n            (_a = control.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(this.audioUrls[index]);\n          });\n          this.dataToPost = {\n            CO_id: this.employerData.CO_id,\n            CO_companyName: (_a = this.addNewCompanyForm.get('CO_companyName')) === null || _a === void 0 ? void 0 : _a.value,\n            CO_logo: fileUrl,\n            CO_location: (_b = this.addNewCompanyForm.get('CO_location')) === null || _b === void 0 ? void 0 : _b.value,\n            CO_type: ((_c = this.addNewCompanyForm.get('CO_type')) === null || _c === void 0 ? void 0 : _c.value) === 'Other' ? (_d = this.addNewCompanyForm.get('CO_otherType')) === null || _d === void 0 ? void 0 : _d.value : (_e = this.addNewCompanyForm.get('CO_type')) === null || _e === void 0 ? void 0 : _e.value,\n            CO_founded: (_f = this.addNewCompanyForm.get('CO_founded')) === null || _f === void 0 ? void 0 : _f.value,\n            CO_sectorId: (_g = this.addNewCompanyForm.get('CO_sectorId')) === null || _g === void 0 ? void 0 : _g.value,\n            CO_about: (_h = this.addNewCompanyForm.get('CO_about')) === null || _h === void 0 ? void 0 : _h.value,\n            CO_website: (_j = this.addNewCompanyForm.get('CO_website')) === null || _j === void 0 ? void 0 : _j.value,\n            CO_size: (_k = this.addNewCompanyForm.get('CO_size')) === null || _k === void 0 ? void 0 : _k.value,\n            CO_HrInsights: (_l = this.addNewCompanyForm.get('CO_HrInsights')) === null || _l === void 0 ? void 0 : _l.value\n          };\n          console.log(\"Data to update...\", this.dataToPost);\n          return this.dataTransferService.updateEmployer(this.dataToPost).toPromise();\n        });\n      }).then(res => {\n        this.ngxSpinnerService.hide('globalSpinner');\n\n        if (res.statusCode === 200) {\n          this.toastr.success('Employer updated successfully.');\n          this.getAllCompany();\n          this.router.navigate(['actions/employer-opportunities']);\n        } else {\n          this.toastr.error('Something went wrong.');\n        }\n      }).catch(error => {\n        this.ngxSpinnerService.hide('globalSpinner');\n        const errorMessage = error === 'Audio file upload failed.' ? error : 'Error updating employer.';\n        this.toastr.error(errorMessage);\n      });\n    } else {\n      this.toastr.info(\"Please resize the image\");\n    }\n  }\n\n  addEmployer() {\n    console.log('Formdata', this.addNewCompanyForm.value);\n\n    if (this.addNewCompanyForm.invalid) {\n      this.toastr.info('Please fill all required fields');\n      return;\n    }\n\n    this.ngxSpinnerService.show('globalSpinner');\n    this.uploadLogoUrl().then(() => {\n      const fileUrl = this.baseUrl + this.imageName.name; // Construct the file URL as needed\n      // Upload all audio files and get their URLs\n\n      this.uploadAudioFiles(this.audioFiles).then(audioUrls => {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\n\n        console.log(\"this.audioFiles\", this.audioFiles);\n        this.insightFormArray.controls.forEach((control, index) => {\n          var _a;\n\n          const group = control;\n          (_a = group.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(audioUrls[index]); // Patch the URL from the array\n        }); // Manually construct the data object\n\n        const data = {\n          CO_companyName: (_a = this.addNewCompanyForm.get('CO_companyName')) === null || _a === void 0 ? void 0 : _a.value,\n          CO_logo: fileUrl,\n          CO_location: (_b = this.addNewCompanyForm.get('CO_location')) === null || _b === void 0 ? void 0 : _b.value,\n          CO_type: ((_c = this.addNewCompanyForm.get('CO_type')) === null || _c === void 0 ? void 0 : _c.value) === 'Other' ? (_d = this.addNewCompanyForm.get('CO_otherType')) === null || _d === void 0 ? void 0 : _d.value : (_e = this.addNewCompanyForm.get('CO_type')) === null || _e === void 0 ? void 0 : _e.value,\n          CO_founded: (_f = this.addNewCompanyForm.get('CO_founded')) === null || _f === void 0 ? void 0 : _f.value,\n          CO_sectorId: (_g = this.addNewCompanyForm.get('CO_sectorId')) === null || _g === void 0 ? void 0 : _g.value,\n          CO_about: (_h = this.addNewCompanyForm.get('CO_about')) === null || _h === void 0 ? void 0 : _h.value,\n          CO_website: (_j = this.addNewCompanyForm.get('CO_website')) === null || _j === void 0 ? void 0 : _j.value,\n          CO_size: (_k = this.addNewCompanyForm.get('CO_size')) === null || _k === void 0 ? void 0 : _k.value,\n          CO_HrInsights: (_l = this.addNewCompanyForm.get('CO_HrInsights')) === null || _l === void 0 ? void 0 : _l.value\n        };\n        console.log('ADD Employer DATA', data);\n        this.dataTransferService.addEmployer(data).subscribe(res => {\n          if (res.statusCode == 201) {\n            this.ngxSpinnerService.hide('globalSpinner');\n            console.log('Data posted successfully:', res);\n            this.toastr.success('Employer added successfully.');\n            this.getAllCompany();\n            this.router.navigate(['actions/employer-opportunities']);\n            console.log(\"Success\");\n          } else {\n            this.ngxSpinnerService.hide('globalSpinner');\n            this.toastr.error('Something Went Wrong');\n          }\n        }, error => {\n          this.ngxSpinnerService.hide('globalSpinner');\n          console.error('Error posting data:', error);\n          this.toastr.error('Something Went Wrong');\n        });\n      }).catch(error => {\n        this.ngxSpinnerService.hide('globalSpinner');\n        console.error('Error uploading audio files:', error);\n        this.toastr.error('Audio file upload failed');\n      });\n    }).catch(error => {\n      this.ngxSpinnerService.hide('globalSpinner');\n      console.error('Error uploading logo:', error);\n      this.toastr.error('Logo upload failed');\n    });\n  }\n\n  getAllCompany() {\n    this.dataTransferService.getAllCompany().subscribe({\n      next: res => {\n        if (res.statusCode === 200) {\n          this.CompanyList = res.data;\n          this.ngxSpinnerService.hide('globalSpinner');\n          console.log('GetAllCompany', this.CompanyList);\n        } else {\n          this.ngxSpinnerService.hide('globalSpinner');\n          console.error('Failed to fetch companies. Status:', res.status);\n        }\n      },\n      error: error => {\n        this.ngxSpinnerService.hide('globalSpinner');\n        console.error('Error occurred while fetching companies:', error);\n      }\n    });\n  }\n\n  getSectorTitles() {\n    // In Add company form - To sector dropdown\n    this.dataTransferService.getSectorTitles().subscribe(res => {\n      if (res.statusCode = 200) {\n        this.SectorList = res.data;\n        console.log('Sectors', this.SectorList);\n      } else {\n        console.error('Failed to fetch sectors. Status:', res.status);\n      }\n    }, error => {\n      this.ngxSpinnerService.hide('globalSpinner');\n      console.log(\"error\", error);\n    });\n  }\n\n  showCropper(controlName) {\n    this.isCropperVisible = true;\n  }\n\n  hideCropper(controlName) {\n    this.isCropperVisible = false;\n  }\n\n  cropImg(e) {\n    this.imageSrc = e.base64;\n  }\n\n  initCropper() {}\n\n  imgLoad() {}\n\n  imgFailed() {\n    this.toastr.error(\"Image Failed to show\");\n  }\n\n  saveCroppedImage(controlName) {\n    const addTimestamp = fileName => {\n      const currentTimestamp = new Date().getTime();\n      const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');\n      const extension = fileName.split('.').pop();\n      const cleanedName = nameWithoutExtension.replace(/_\\d{13}$/, '');\n      return `${cleanedName}_${currentTimestamp}.${extension}`;\n    };\n\n    if (this.imageSrc && this.imageName) {\n      // Convert cropped image to Blob\n      const blob = this.dataURItoBlob(this.imageSrc); // Add or replace timestamp in image name\n\n      const newFileName = addTimestamp(this.imageName.name);\n      this.imageName = new File([blob], newFileName, {\n        type: this.imageName.type\n      });\n      console.log(\"Cropped Image\", this.imageName); // Update imgChangeEvt with the new cropped image\n\n      this.imgChangeEvt = {\n        target: {\n          files: [this.imageName]\n        }\n      };\n      const fileControl = this.addNewCompanyForm.get('CO_logo');\n      fileControl === null || fileControl === void 0 ? void 0 : fileControl.clearValidators();\n      fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\n      this.hideCropper('CO_logo');\n    }\n  }\n\n  dataURItoBlob(dataURI) {\n    const byteString = atob(dataURI.split(',')[1]);\n    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];\n    const arrayBuffer = new ArrayBuffer(byteString.length);\n    const uint8Array = new Uint8Array(arrayBuffer);\n\n    for (let i = 0; i < byteString.length; i++) {\n      uint8Array[i] = byteString.charCodeAt(i);\n    }\n\n    return new Blob([uint8Array], {\n      type: mimeString\n    });\n  }\n\n}\n\nAddEmployersComponent.ɵfac = function AddEmployersComponent_Factory(t) {\n  return new (t || AddEmployersComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.NgxSpinnerService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.DatePipe), i0.ɵɵdirectiveInject(i4.ActivatedRoute));\n};\n\nAddEmployersComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AddEmployersComponent,\n  selectors: [[\"app-add-employers\"]],\n  viewQuery: function AddEmployersComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioInput = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioPlayers = _t);\n    }\n  },\n  decls: 97,\n  vars: 21,\n  consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [\"enctype\", \"multipart/form-data\", 1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\", \"col-lg-4\"], [\"for\", \"CO_companyName\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"CO_companyName\", \"required\", \"\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"CO_logo\", 1, \"required-field\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"CO_logo\", \"required\", \"\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Employer Logo\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"class\", \"btn-custom-small\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"cropper-container  col-lg-12\", 4, \"ngIf\"], [\"for\", \"CO_location\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"CO_location\", \"required\", \"\", \"placeholder\", \"e.g. London, UK\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"CO_type\", 1, \"required-field\"], [\"id\", \"CO_type\", \"formControlName\", \"CO_type\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"value\", \"\", \"disabled\", \"\", \"selected\", \"\"], [\"value\", \"Private Limited Company(LTD)\"], [\"value\", \"Public Limited Company(PLC)\"], [\"value\", \"Partnership\"], [\"value\", \"Non-Profit Organization\"], [\"value\", \"Other\"], [\"class\", \"form-group col-lg-4\", 4, \"ngIf\"], [\"for\", \"CO_founded\", 1, \"required-field\"], [\"type\", \"text\", \"mask\", \"0000\", \"formControlName\", \"CO_founded\", \"required\", \"\", \"placeholder\", \"Enter Year Only\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"CO_sectorId\", 1, \"required-field\"], [\"id\", \"companySector\", \"formControlName\", \"CO_sectorId\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"CO_website\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"CO_website\", \"required\", \"\", \"placeholder\", \"Enter URL\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"CO_size\", 1, \"required-field\"], [\"id\", \"CO_size\", \"formControlName\", \"CO_size\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"value\", \"1-9\"], [\"value\", \"10-49\"], [\"value\", \"50-499\"], [\"value\", \"500-999\"], [\"value\", \"1,000-4,999\"], [\"value\", \"5,000+\"], [\"for\", \"CO_about\", 1, \"required-field\"], [\"id\", \"CO_about\", \"formControlName\", \"CO_about\", \"required\", \"\", \"rows\", \"3\", \"cols\", \"50\", \"placeholder\", \"Enter About\", \"maxlength\", \"200\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"input\"], [1, \"character-count\"], [\"formArrayName\", \"CO_HrInsights\"], [1, \"required-field\", \"mb-3\", \"py-2\"], [\"class\", \"row mb-3\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"class\", \"btn btn-sm add-insight-btn btn-outline-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [\"type\", \"button\", \"routerLink\", \"/actions/employer-opportunities\", 1, \"btn\", \"btn-light\"], [\"alt\", \"Employer Logo\", 1, \"img-preview\", 3, \"src\"], [1, \"warning\"], [1, \"btn-custom-small\", 3, \"click\"], [1, \"cropper-container\", \"col-lg-12\"], [1, \"image-cropper\", \"mb-2\", \"text-center\"], [1, \"py-2\"], [\"format\", \"png\", 1, \"custom-image-cropper\", \"my-2\", 3, \"imageChangedEvent\", \"aspectRatio\", \"maintainAspectRatio\", \"imageCropped\", \"imageLoaded\", \"cropperReady\", \"loadImageFailed\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"m-2\", 3, \"click\"], [\"for\", \"CO_otherType\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"CO_otherType\", \"placeholder\", \"Enter Type\", 1, \"form-control\", \"form-control-sm\"], [3, \"value\"], [4, \"ngIf\"], [1, \"row\", \"mb-3\", 3, \"formGroupName\"], [1, \"col-lg-11\"], [1, \"row\", \"mr-0\"], [1, \"form-group\", \"col-lg-3\"], [\"for\", \"HRI_title\", 1, \"required-field\", \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_title\", \"required\", \"\", \"placeholder\", \"Enter Title\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"HRI_name\", 1, \"required-field\", \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_name\", \"required\", \"\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"HRI_position\", 1, \"required-field\", \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_position\", \"required\", \"\", \"placeholder\", \"Enter Position\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"link\", 1, \"required-field\", \"subtitle\"], [\"type\", \"file\", \"id\", \"link\", \"accept\", \"audio/*\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [1, \"col-lg-1\", \"px-0\", \"d-flex\", \"align-items-center\", \"btns\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm mr-2\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [\"controls\", \"\", 2, \"display\", \"none\"], [\"audioPlayer\", \"\"], [\"type\", \"audio/mpeg\", 3, \"src\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"mr-2\", 3, \"ngClass\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-minus\", \"icon\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"add-insight-btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"icon\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]],\n  template: function AddEmployersComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"app-sidebar\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵtext(6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵelementStart(8, \"form\", 6);\n      i0.ɵɵlistener(\"ngSubmit\", function AddEmployersComponent_Template_form_ngSubmit_8_listener() {\n        return ctx.onSubmit();\n      });\n      i0.ɵɵelementStart(9, \"div\", 1);\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵelementStart(11, \"label\", 8);\n      i0.ɵɵtext(12, \"Employer Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(13, \"input\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"div\", 7);\n      i0.ɵɵelementStart(15, \"label\", 10);\n      i0.ɵɵtext(16, \"Employer Logo\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 11);\n      i0.ɵɵelementStart(18, \"input\", 12);\n      i0.ɵɵlistener(\"change\", function AddEmployersComponent_Template_input_change_18_listener($event) {\n        return ctx.onFileSelected($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(19, AddEmployersComponent_img_19_Template, 1, 1, \"img\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(20, AddEmployersComponent_div_20_Template, 2, 0, \"div\", 14);\n      i0.ɵɵtemplate(21, AddEmployersComponent_div_21_Template, 2, 0, \"div\", 14);\n      i0.ɵɵtemplate(22, AddEmployersComponent_a_22_Template, 2, 0, \"a\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(23, AddEmployersComponent_div_23_Template, 9, 3, \"div\", 16);\n      i0.ɵɵelementStart(24, \"div\", 7);\n      i0.ɵɵelementStart(25, \"label\", 17);\n      i0.ɵɵtext(26, \"Employer Location\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(27, \"input\", 18);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"div\", 7);\n      i0.ɵɵelementStart(29, \"label\", 19);\n      i0.ɵɵtext(30, \"Employer Type\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"select\", 20);\n      i0.ɵɵlistener(\"change\", function AddEmployersComponent_Template_select_change_31_listener($event) {\n        return ctx.onTypeChange($event);\n      });\n      i0.ɵɵelementStart(32, \"option\", 21);\n      i0.ɵɵtext(33, \"Please select type\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"option\", 22);\n      i0.ɵɵtext(35, \"Private Limited Company(LTD)\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(36, \"option\", 23);\n      i0.ɵɵtext(37, \"Public Limited Company(PLC)\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"option\", 24);\n      i0.ɵɵtext(39, \"Partnership\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"option\", 25);\n      i0.ɵɵtext(41, \"Non-Profit Organization\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"option\", 26);\n      i0.ɵɵtext(43, \"Other\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(44, AddEmployersComponent_div_44_Template, 4, 0, \"div\", 27);\n      i0.ɵɵelementStart(45, \"div\", 7);\n      i0.ɵɵelementStart(46, \"label\", 28);\n      i0.ɵɵtext(47, \"Founded\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(48, \"input\", 29);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"div\", 7);\n      i0.ɵɵelementStart(50, \"label\", 30);\n      i0.ɵɵtext(51, \"Employer Sector\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(52, \"select\", 31);\n      i0.ɵɵelementStart(53, \"option\", 21);\n      i0.ɵɵtext(54, \"Please select sector\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(55, AddEmployersComponent_option_55_Template, 2, 2, \"option\", 32);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(56, \"div\", 7);\n      i0.ɵɵelementStart(57, \"label\", 33);\n      i0.ɵɵtext(58, \"Website\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(59, \"input\", 34);\n      i0.ɵɵtemplate(60, AddEmployersComponent_div_60_Template, 3, 2, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(61, \"div\", 7);\n      i0.ɵɵelementStart(62, \"label\", 35);\n      i0.ɵɵtext(63, \"Employer Size\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(64, \"select\", 36);\n      i0.ɵɵelementStart(65, \"option\", 21);\n      i0.ɵɵtext(66, \"Please select size\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(67, \"option\", 37);\n      i0.ɵɵtext(68, \" 1-9 Employee \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(69, \"option\", 38);\n      i0.ɵɵtext(70, \"10-49 Employee \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(71, \"option\", 39);\n      i0.ɵɵtext(72, \"50-499 Employee\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(73, \"option\", 40);\n      i0.ɵɵtext(74, \"500-999 Employee \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(75, \"option\", 41);\n      i0.ɵɵtext(76, \" 1,000-4,999 Employee\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(77, \"option\", 42);\n      i0.ɵɵtext(78, \" 5,000+ Employees\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(79, \"div\", 7);\n      i0.ɵɵelementStart(80, \"label\", 43);\n      i0.ɵɵtext(81, \"About\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(82, \"textarea\", 44);\n      i0.ɵɵlistener(\"input\", function AddEmployersComponent_Template_textarea_input_82_listener($event) {\n        return ctx.onTextChange($event);\n      });\n      i0.ɵɵtext(83, \"                          \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(84, \"div\", 45);\n      i0.ɵɵtext(85);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(86, AddEmployersComponent_div_86_Template, 2, 0, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(87, \"hr\");\n      i0.ɵɵelementStart(88, \"div\", 46);\n      i0.ɵɵelementStart(89, \"h6\", 47);\n      i0.ɵɵtext(90, \"HR / Hiring Manager\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(91, AddEmployersComponent_div_91_Template, 26, 4, \"div\", 48);\n      i0.ɵɵtemplate(92, AddEmployersComponent_button_92_Template, 3, 0, \"button\", 49);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(93, \"div\", 50);\n      i0.ɵɵtemplate(94, AddEmployersComponent_button_94_Template, 2, 0, \"button\", 51);\n      i0.ɵɵelementStart(95, \"button\", 52);\n      i0.ɵɵtext(96, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      let tmp_5_0;\n      let tmp_6_0;\n      let tmp_7_0;\n      let tmp_8_0;\n      let tmp_14_0;\n      let tmp_17_0;\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Employer\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.addNewCompanyForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.imageSrc);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.addNewCompanyForm.get(\"CO_logo\")) == null ? null : tmp_5_0.errors == null ? null : tmp_5_0.errors.fileSizeValidator);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.addNewCompanyForm.get(\"CO_logo\")) == null ? null : tmp_6_0.errors == null ? null : tmp_6_0.errors.fileAspectRatioValidator);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isCropperVisible && ctx.imageName && !((tmp_7_0 = ctx.addNewCompanyForm.get(\"CO_logo\")) == null ? null : tmp_7_0.errors == null ? null : tmp_7_0.errors.fileSizeValidator));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isCropperVisible && !((tmp_8_0 = ctx.addNewCompanyForm.get(\"CO_logo\")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors.fileSizeValidator));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(17);\n      i0.ɵɵproperty(\"ngIf\", ctx.showOtherTypeInput);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngForOf\", ctx.SectorList);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.addNewCompanyForm.get(\"CO_website\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.addNewCompanyForm.get(\"CO_website\")) == null ? null : tmp_14_0.touched));\n      i0.ɵɵadvance(22);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate1(\" \", ctx.characterCount, \"/500 \");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx.addNewCompanyForm.get(\"CO_about\")) == null ? null : tmp_17_0.errors) && ((tmp_17_0 = ctx.addNewCompanyForm.get(\"CO_about\")) == null ? null : tmp_17_0.errors == null ? null : tmp_17_0.errors.maxlength));\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngForOf\", ctx.insightFormArray.controls);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.insightFormArray.length < 3);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n    }\n  },\n  directives: [i7.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.RequiredValidator, i6.NgIf, i1.SelectControlValueAccessor, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i8.NgxMaskDirective, i6.NgForOf, i1.MaxLengthValidator, i1.FormArrayName, i4.RouterLink, i9.ImageCropperComponent, i1.FormGroupName, i6.NgClass],\n  styles: [\".footer[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n\\n.head-exst[_ngcontent-%COMP%] {\\n  width: 100vh;\\n}\\n\\n.fa-plus[_ngcontent-%COMP%] {\\n  font-size: small;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  color: rgba(81, 80, 80, 0.856) !important;\\n}\\n\\n.character-count[_ngcontent-%COMP%] {\\n  font-size: smaller;\\n  margin-top: 2px;\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n\\n.suggestion[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1224px) {\\n  .insight-btn[_ngcontent-%COMP%] {\\n    padding: 4px 5px;\\n  }\\n\\n  .fas[_ngcontent-%COMP%] {\\n    font-size: small;\\n  }\\n}\\n\\n@media screen and (min-width: 320px) and (max-width: 768px) {\\n  .btns[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: center;\\n    margin-bottom: 20px;\\n  }\\n\\n  .add-insight-btn[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n}\\n\\n.existing-logo[_ngcontent-%COMP%] {\\n  max-width: 200px !important;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  vertical-align: middle;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFkZC1lbXBsb3llcnMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0U7RUFDQSx1QkFBQTtBQUFGOztBQUVFO0VBQ0UsWUFBQTtBQUNKOztBQUdDO0VBQ0MsZ0JBQUE7QUFBRjs7QUFHQztFQUNDLHlDQUFBO0FBQUY7O0FBR0M7RUFDRyxrQkFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0EseUJBQUE7QUFBSjs7QUFHQztFQUNDLGVBQUE7QUFBRjs7QUFLQTtFQUNFO0lBQ0UsZ0JBQUE7RUFGRjs7RUFLQTtJQUNFLGdCQUFBO0VBRkY7QUFDRjs7QUFLQTtFQUNBO0lBQ0UsYUFBQTtJQUNBLHVCQUFBO0lBQ0EsbUJBQUE7RUFIQTs7RUFNRjtJQUNFLG1CQUFBO0VBSEE7QUFDRjs7QUFNQTtFQUNFLDJCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0Esc0JBQUE7QUFKRiIsImZpbGUiOiJhZGQtZW1wbG95ZXJzLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbiAgLmZvb3RlcntcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcclxuICB9XHJcbiAgLmhlYWQtZXhzdHtcclxuICAgIHdpZHRoOiAxMDB2aDtcclxuICB9XHJcblxyXG4gIFxyXG4gLmZhLXBsdXN7XHJcbiAgZm9udC1zaXplOiBzbWFsbDtcclxuIH1cclxuXHJcbiAuc3VidGl0bGV7XHJcbiAgY29sb3I6IHJnYmEoODEsIDgwLCA4MCwgMC44NTYpICFpbXBvcnRhbnQ7XHJcbiB9XHJcblxyXG4gLmNoYXJhY3Rlci1jb3VudHtcclxuICAgIGZvbnQtc2l6ZTogc21hbGxlcjtcclxuICAgIG1hcmdpbi10b3A6IDJweDtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xyXG4gfVxyXG5cclxuIC5zdWdnZXN0aW9ue1xyXG4gIGZvbnQtc2l6ZTogMTJweDtcclxuIH1cclxuXHJcblxyXG5cclxuQG1lZGlhIHNjcmVlbiBhbmQgKG1pbi13aWR0aDogOTkycHgpIGFuZCAobWF4LXdpZHRoOiAxMjI0cHgpIHtcclxuICAuaW5zaWdodC1idG57XHJcbiAgICBwYWRkaW5nOiA0cHggNXB4O1xyXG4gIH1cclxuXHJcbiAgLmZhc3tcclxuICAgIGZvbnQtc2l6ZTogc21hbGw7XHJcbiAgfVxyXG59XHJcblxyXG5AbWVkaWEgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAzMjBweCkgYW5kIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbi5idG5ze1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxufVxyXG5cclxuLmFkZC1pbnNpZ2h0LWJ0bntcclxuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG59XHJcbn1cclxuXHJcbi5leGlzdGluZy1sb2dvIHtcclxuICBtYXgtd2lkdGg6IDIwMHB4ICFpbXBvcnRhbnQ7IFxyXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7IFxyXG4gIG92ZXJmbG93OiBoaWRkZW47IFxyXG4gIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOyBcclxuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOyBcclxufVxyXG4iXX0= */\"]\n});", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/src/app/shared/sidebar/actions/Employer-Opportunities/Employer/add-employers/add-employers.component.ts"], "names": ["Validators", "FileValidator", "i0", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "i8", "i9", "_c0", "_c1", "AddEmployersComponent_img_19_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "imageSrc", "ɵɵsanitizeUrl", "AddEmployersComponent_div_20_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "AddEmployersComponent_div_21_Template", "AddEmployersComponent_a_22_Template", "_r13", "ɵɵgetCurrentView", "ɵɵlistener", "AddEmployersComponent_a_22_Template_a_click_0_listener", "ɵɵrestoreView", "ctx_r12", "showCropper", "AddEmployersComponent_div_23_Template", "_r15", "AddEmployersComponent_div_23_Template_image_cropper_imageCropped_4_listener", "$event", "ctx_r14", "cropImg", "AddEmployersComponent_div_23_Template_image_cropper_imageLoaded_4_listener", "ctx_r16", "imgLoad", "AddEmployersComponent_div_23_Template_image_cropper_cropperReady_4_listener", "ctx_r17", "initCropper", "AddEmployersComponent_div_23_Template_image_cropper_loadImageFailed_4_listener", "ctx_r18", "imgFailed", "AddEmployersComponent_div_23_Template_button_click_5_listener", "ctx_r19", "saveCroppedImage", "AddEmployersComponent_div_23_Template_button_click_7_listener", "ctx_r20", "hideCropper", "ctx_r4", "ɵɵadvance", "imgChangeEvt", "AddEmployersComponent_div_44_Template", "AddEmployersComponent_option_55_Template", "sector_r21", "$implicit", "IN_id", "ɵɵtextInterpolate", "IN_name", "AddEmployersComponent_div_60_div_1_Template", "AddEmployersComponent_div_60_div_2_Template", "AddEmployersComponent_div_60_Template", "ɵɵtemplate", "ctx_r7", "tmp_0_0", "tmp_1_0", "addNewCompanyForm", "get", "errors", "required", "pattern", "AddEmployersComponent_div_86_Template", "_c2", "a0", "a1", "AddEmployersComponent_div_91_button_20_Template", "_r31", "AddEmployersComponent_div_91_button_20_Template_button_click_0_listener", "i_r25", "index", "ctx_r29", "toggleAudio", "ctx_r26", "ɵɵpureFunction2", "isPlaying", "AddEmployersComponent_div_91_button_21_Template", "_r35", "AddEmployersComponent_div_91_button_21_Template_button_click_0_listener", "ctx_r33", "removeInsight", "AddEmployersComponent_div_91_Template", "_r37", "AddEmployersComponent_div_91_Template_input_change_18_listener", "restoredCtx", "ctx_r36", "onAudioSelected", "ctx_r9", "audioUrls", "insightFormArray", "length", "AddEmployersComponent_button_92_Template", "_r39", "AddEmployersComponent_button_92_Template_button_click_0_listener", "ctx_r38", "addInsight", "AddEmployersComponent_button_94_Template", "AddEmployersComponent", "constructor", "formBuilder", "dataTransferService", "ngxSpinnerService", "router", "toastr", "datePipe", "route", "_a", "_b", "p", "<PERSON><PERSON>ead<PERSON>", "viewInsight", "showForm", "submitted", "title", "is<PERSON><PERSON><PERSON>ly", "baseUrl", "imagedp", "invalidDates", "Set", "audioFiles", "characterCount", "showOtherTypeInput", "existingAudioNames", "audioFileUrls", "isCropperVisible", "group", "CO_companyName", "CO_logo", "CO_location", "CO_type", "CO_otherType", "CO_founded", "CO_sectorId", "CO_about", "max<PERSON><PERSON><PERSON>", "CO_website", "CO_size", "CO_HrInsights", "array", "createInsight", "queryParams", "subscribe", "params", "CO_id", "console", "log", "state", "getCurrentNavigation", "extras", "employerData", "navigate", "ngOnInit", "getSectorTitles", "getCompanyById", "companyId", "res", "to<PERSON>romise", "populateForm", "error", "data", "_c", "logoControl", "clearValidators", "updateValueAndValidity", "patchValue", "validOptions", "includes", "setValue", "hrInsightsArray", "clear", "for<PERSON>ach", "insight", "insightFormGroup", "HRI_title", "HRI_name", "HRI_position", "HRI_link", "push", "map", "Array", "fill", "substring", "lastIndexOf", "onTextChange", "event", "textarea", "target", "value", "setErrors", "maxlength", "removeAt", "splice", "audioElements", "audioPlayers", "toArray", "audioElement", "nativeElement", "stopAllAudio", "pause", "src", "play", "currentIndex", "audioPlayer", "stopAudio", "currentTime", "onFileSelected", "selectedFile", "files", "imageName", "newFileName", "addTimestamp", "name", "File", "type", "formControl", "setValidators", "fileSizeValidator", "fileType", "split", "fileExtension", "pop", "toLowerCase", "info", "reader", "FileReader", "img", "Image", "onload", "e", "result", "checkAspectRatio", "readAsDataURL", "image", "controlName", "aspectRatio", "width", "height", "control", "fileAspectRatioValidator", "uploadLogoUrl", "Promise", "resolve", "reject", "uploadurl", "fileUrl", "message", "audiofile", "audiofileType", "onerror", "uploadAudioFiles", "uploadPromises", "file", "uploadSingleAudioFile", "all", "onTypeChange", "selectElement", "onSubmit", "updateEmployer", "addEmployer", "invalid", "Object", "keys", "controls", "uploadLogo", "show", "then", "_d", "_e", "_f", "_g", "_h", "_j", "_k", "_l", "dataToPost", "hide", "statusCode", "success", "getAllCompany", "catch", "errorMessage", "next", "CompanyList", "status", "SectorList", "base64", "fileName", "currentTimestamp", "Date", "getTime", "nameWithoutExtension", "slice", "join", "extension", "cleanedName", "replace", "blob", "dataURItoBlob", "fileControl", "dataURI", "byteString", "atob", "mimeString", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uint8Array", "Uint8Array", "i", "charCodeAt", "Blob", "ɵfac", "AddEmployersComponent_Factory", "t", "ɵɵdirectiveInject", "FormBuilder", "DataTransferService", "NgxSpinnerService", "Router", "ToastrService", "DatePipe", "ActivatedRoute", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "AddEmployersComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "audioInput", "first", "decls", "vars", "consts", "template", "AddEmployersComponent_Template", "AddEmployersComponent_Template_form_ngSubmit_8_listener", "AddEmployersComponent_Template_input_change_18_listener", "AddEmployersComponent_Template_select_change_31_listener", "AddEmployersComponent_Template_textarea_input_82_listener", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_14_0", "tmp_17_0", "ɵɵtextInterpolate1", "touched", "directives", "SidebarComponent", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "DefaultValueAccessor", "NgControlStatus", "FormControlName", "RequiredValidator", "NgIf", "SelectControlValueAccessor", "NgSelectOption", "ɵNgSelectMultipleOption", "NgxMaskDirective", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MaxLengthValidator", "FormArrayName", "RouterLink", "ImageCropperComponent", "FormGroupName", "Ng<PERSON><PERSON>", "styles"], "mappings": ";AAAA,SAASA,UAAT,QAA2B,gBAA3B;AACA,SAASC,aAAT,QAA8B,mDAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+CAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,YAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+BAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,UAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,MAAMC,GAAG,GAAG,CAAC,YAAD,CAAZ;AACA,MAAMC,GAAG,GAAG,CAAC,aAAD,CAAZ;;AACA,SAASC,qCAAT,CAA+CC,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEb,IAAAA,EAAE,CAACe,SAAH,CAAa,CAAb,EAAgB,KAAhB,EAAuB,EAAvB;AACH;;AAAC,MAAIF,EAAE,GAAG,CAAT,EAAY;AACV,UAAMG,MAAM,GAAGhB,EAAE,CAACiB,aAAH,EAAf;AACAjB,IAAAA,EAAE,CAACkB,UAAH,CAAc,KAAd,EAAqBF,MAAM,CAACG,QAA5B,EAAsCnB,EAAE,CAACoB,aAAzC;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+CR,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEb,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,uEAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+CZ,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEb,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,yEAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASE,mCAAT,CAA6Cb,EAA7C,EAAiDC,GAAjD,EAAsD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAChE,UAAMc,IAAI,GAAG3B,EAAE,CAAC4B,gBAAH,EAAb;;AACA5B,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,GAArB,EAA0B,EAA1B;AACAtB,IAAAA,EAAE,CAAC6B,UAAH,CAAc,OAAd,EAAuB,SAASC,sDAAT,GAAkE;AAAE9B,MAAAA,EAAE,CAAC+B,aAAH,CAAiBJ,IAAjB;AAAwB,YAAMK,OAAO,GAAGhC,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAOe,OAAO,CAACC,WAAR,CAAoB,SAApB,CAAP;AAAwC,KAA/L;AACAjC,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,WAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASU,qCAAT,CAA+CrB,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMsB,IAAI,GAAGnC,EAAE,CAAC4B,gBAAH,EAAb;;AACA5B,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,IAArB,EAA2B,EAA3B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,cAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,eAArB,EAAsC,EAAtC;AACAtB,IAAAA,EAAE,CAAC6B,UAAH,CAAc,cAAd,EAA8B,SAASO,2EAAT,CAAqFC,MAArF,EAA6F;AAAErC,MAAAA,EAAE,CAAC+B,aAAH,CAAiBI,IAAjB;AAAwB,YAAMG,OAAO,GAAGtC,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAOqB,OAAO,CAACC,OAAR,CAAgBF,MAAhB,CAAP;AAAiC,KAA1N,EAA4N,aAA5N,EAA2O,SAASG,0EAAT,GAAsF;AAAExC,MAAAA,EAAE,CAAC+B,aAAH,CAAiBI,IAAjB;AAAwB,YAAMM,OAAO,GAAGzC,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAOwB,OAAO,CAACC,OAAR,EAAP;AAA2B,KAA1Z,EAA4Z,cAA5Z,EAA4a,SAASC,2EAAT,GAAuF;AAAE3C,MAAAA,EAAE,CAAC+B,aAAH,CAAiBI,IAAjB;AAAwB,YAAMS,OAAO,GAAG5C,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAO2B,OAAO,CAACC,WAAR,EAAP;AAA+B,KAAhmB,EAAkmB,iBAAlmB,EAAqnB,SAASC,8EAAT,GAA0F;AAAE9C,MAAAA,EAAE,CAAC+B,aAAH,CAAiBI,IAAjB;AAAwB,YAAMY,OAAO,GAAG/C,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAO8B,OAAO,CAACC,SAAR,EAAP;AAA6B,KAA1yB;AACAhD,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAAC6B,UAAH,CAAc,OAAd,EAAuB,SAASoB,6DAAT,GAAyE;AAAEjD,MAAAA,EAAE,CAAC+B,aAAH,CAAiBI,IAAjB;AAAwB,YAAMe,OAAO,GAAGlD,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAOiC,OAAO,CAACC,gBAAR,CAAyB,SAAzB,CAAP;AAA6C,KAA3M;AACAnD,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,MAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAAC6B,UAAH,CAAc,OAAd,EAAuB,SAASuB,6DAAT,GAAyE;AAAEpD,MAAAA,EAAE,CAAC+B,aAAH,CAAiBI,IAAjB;AAAwB,YAAMkB,OAAO,GAAGrD,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAOoC,OAAO,CAACC,WAAR,CAAoB,SAApB,CAAP;AAAwC,KAAtM;AACAtD,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,QAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM0C,MAAM,GAAGvD,EAAE,CAACiB,aAAH,EAAf;AACAjB,IAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,IAAAA,EAAE,CAACkB,UAAH,CAAc,mBAAd,EAAmCqC,MAAM,CAACE,YAA1C,EAAwD,aAAxD,EAAuE,IAAI,CAA3E,EAA8E,qBAA9E,EAAqG,IAArG;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+C7C,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEb,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,oBAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACe,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAf,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASmC,wCAAT,CAAkD9C,EAAlD,EAAsDC,GAAtD,EAA2D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACrEb,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM+C,UAAU,GAAG9C,GAAG,CAAC+C,SAAvB;AACA7D,IAAAA,EAAE,CAACkB,UAAH,CAAc,OAAd,EAAuB0C,UAAU,CAACE,KAAlC;AACA9D,IAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,IAAAA,EAAE,CAAC+D,iBAAH,CAAqBH,UAAU,CAACI,OAAhC;AACH;AAAE;;AACH,SAASC,2CAAT,CAAqDpD,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEb,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,sBAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAAS0C,2CAAT,CAAqDrD,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEb,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,+DAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAAS2C,qCAAT,CAA+CtD,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEb,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACoE,UAAH,CAAc,CAAd,EAAiBH,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,KAApE,EAA2E,EAA3E;AACAjE,IAAAA,EAAE,CAACoE,UAAH,CAAc,CAAd,EAAiBF,2CAAjB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,KAApE,EAA2E,EAA3E;AACAlE,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMwD,MAAM,GAAGrE,EAAE,CAACiB,aAAH,EAAf;AACA,QAAIqD,OAAJ;AACA,QAAIC,OAAJ;AACAvE,IAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,IAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsB,CAACoD,OAAO,GAAGD,MAAM,CAACG,iBAAP,CAAyBC,GAAzB,CAA6B,YAA7B,CAAX,KAA0D,IAA1D,GAAiE,IAAjE,GAAwEH,OAAO,CAACI,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCJ,OAAO,CAACI,MAAR,CAAeC,QAA7I;AACA3E,IAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,IAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsB,CAACqD,OAAO,GAAGF,MAAM,CAACG,iBAAP,CAAyBC,GAAzB,CAA6B,YAA7B,CAAX,KAA0D,IAA1D,GAAiE,IAAjE,GAAwEF,OAAO,CAACG,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCH,OAAO,CAACG,MAAR,CAAeE,OAA7I;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+ChE,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEb,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,qDAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,MAAMsD,GAAG,GAAG,UAAUC,EAAV,EAAcC,EAAd,EAAkB;AAAE,SAAO;AAAE,2BAAuBD,EAAzB;AAA6B,6BAAyBC;AAAtD,GAAP;AAAoE,CAApG;;AACA,SAASC,+CAAT,CAAyDpE,EAAzD,EAA6DC,GAA7D,EAAkE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC5E,UAAMqE,IAAI,GAAGlF,EAAE,CAAC4B,gBAAH,EAAb;;AACA5B,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAAC6B,UAAH,CAAc,OAAd,EAAuB,SAASsD,uEAAT,GAAmF;AAAEnF,MAAAA,EAAE,CAAC+B,aAAH,CAAiBmD,IAAjB;AAAwB,YAAME,KAAK,GAAGpF,EAAE,CAACiB,aAAH,GAAmBoE,KAAjC;AAAwC,YAAMC,OAAO,GAAGtF,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAOqE,OAAO,CAACC,WAAR,CAAoBH,KAApB,CAAP;AAAoC,KAApP;AACApF,IAAAA,EAAE,CAACe,SAAH,CAAa,CAAb,EAAgB,GAAhB,EAAqB,EAArB;AACAf,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMuE,KAAK,GAAGpF,EAAE,CAACiB,aAAH,GAAmBoE,KAAjC;AACA,UAAMG,OAAO,GAAGxF,EAAE,CAACiB,aAAH,EAAhB;AACAjB,IAAAA,EAAE,CAACkB,UAAH,CAAc,SAAd,EAAyBlB,EAAE,CAACyF,eAAH,CAAmB,CAAnB,EAAsBX,GAAtB,EAA2B,CAACU,OAAO,CAACE,SAAR,CAAkBN,KAAlB,CAA5B,EAAsDI,OAAO,CAACE,SAAR,CAAkBN,KAAlB,CAAtD,CAAzB;AACApF,IAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,IAAAA,EAAE,CAACkB,UAAH,CAAc,SAAd,EAAyBsE,OAAO,CAACE,SAAR,CAAkBN,KAAlB,IAA2B,UAA3B,GAAwC,SAAjE;AACH;AAAE;;AACH,SAASO,+CAAT,CAAyD9E,EAAzD,EAA6DC,GAA7D,EAAkE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC5E,UAAM+E,IAAI,GAAG5F,EAAE,CAAC4B,gBAAH,EAAb;;AACA5B,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAAC6B,UAAH,CAAc,OAAd,EAAuB,SAASgE,uEAAT,GAAmF;AAAE7F,MAAAA,EAAE,CAAC+B,aAAH,CAAiB6D,IAAjB;AAAwB,YAAMR,KAAK,GAAGpF,EAAE,CAACiB,aAAH,GAAmBoE,KAAjC;AAAwC,YAAMS,OAAO,GAAG9F,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAO6E,OAAO,CAACC,aAAR,CAAsBX,KAAtB,CAAP;AAAsC,KAAtP;AACApF,IAAAA,EAAE,CAACe,SAAH,CAAa,CAAb,EAAgB,GAAhB,EAAqB,EAArB;AACAf,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASwE,qCAAT,CAA+CnF,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMoF,IAAI,GAAGjG,EAAE,CAAC4B,gBAAH,EAAb;;AACA5B,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,eAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACe,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAf,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,MAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACe,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAf,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,UAAd;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACe,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAf,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAtB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,gBAAd;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAAC6B,UAAH,CAAc,QAAd,EAAwB,SAASqE,8DAAT,CAAwE7D,MAAxE,EAAgF;AAAE,YAAM8D,WAAW,GAAGnG,EAAE,CAAC+B,aAAH,CAAiBkE,IAAjB,CAApB;AAA4C,YAAMb,KAAK,GAAGe,WAAW,CAACd,KAA1B;AAAiC,YAAMe,OAAO,GAAGpG,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAOmF,OAAO,CAACC,eAAR,CAAwBhE,MAAxB,EAAgC+C,KAAhC,CAAP;AAAgD,KAA3Q;AACApF,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAtB,IAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkBa,+CAAlB,EAAmE,CAAnE,EAAsE,CAAtE,EAAyE,QAAzE,EAAmF,EAAnF;AACAjF,IAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkBuB,+CAAlB,EAAmE,CAAnE,EAAsE,CAAtE,EAAyE,QAAzE,EAAmF,EAAnF;AACA3F,IAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B,EAAmC,EAAnC;AACAtB,IAAAA,EAAE,CAACe,SAAH,CAAa,EAAb,EAAiB,QAAjB,EAA2B,EAA3B;AACAf,IAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,oDAAd;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMuE,KAAK,GAAGtE,GAAG,CAACuE,KAAlB;AACA,UAAMiB,MAAM,GAAGtG,EAAE,CAACiB,aAAH,EAAf;AACAjB,IAAAA,EAAE,CAACkB,UAAH,CAAc,eAAd,EAA+BkE,KAA/B;AACApF,IAAAA,EAAE,CAACwD,SAAH,CAAa,EAAb;AACAxD,IAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsBoF,MAAM,CAACC,SAAP,CAAiBnB,KAAjB,CAAtB;AACApF,IAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,IAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsBoF,MAAM,CAACE,gBAAP,CAAwBC,MAAxB,GAAiC,CAAvD;AACAzG,IAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,IAAAA,EAAE,CAACkB,UAAH,CAAc,KAAd,EAAqBoF,MAAM,CAACC,SAAP,CAAiBnB,KAAjB,CAArB,EAA8CpF,EAAE,CAACoB,aAAjD;AACH;AAAE;;AACH,SAASsF,wCAAT,CAAkD7F,EAAlD,EAAsDC,GAAtD,EAA2D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACrE,UAAM8F,IAAI,GAAG3G,EAAE,CAAC4B,gBAAH,EAAb;;AACA5B,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAAC6B,UAAH,CAAc,OAAd,EAAuB,SAAS+E,gEAAT,GAA4E;AAAE5G,MAAAA,EAAE,CAAC+B,aAAH,CAAiB4E,IAAjB;AAAwB,YAAME,OAAO,GAAG7G,EAAE,CAACiB,aAAH,EAAhB;AAAoC,aAAO4F,OAAO,CAACC,UAAR,EAAP;AAA8B,KAA/L;AACA9G,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,cAAb;AACAvB,IAAAA,EAAE,CAACe,SAAH,CAAa,CAAb,EAAgB,GAAhB,EAAqB,EAArB;AACAf,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASuF,wCAAT,CAAkDlG,EAAlD,EAAsDC,GAAtD,EAA2D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACrEb,IAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAtB,IAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV,EAAa,MAAb;AACAvB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE,C,CACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,OAAO,MAAMwF,qBAAN,CAA4B;AAC/BC,EAAAA,WAAW,CAACC,WAAD,EAAcC,mBAAd,EAAmCC,iBAAnC,EAAsDC,MAAtD,EAA8DC,MAA9D,EAAsEC,QAAtE,EAAgFC,KAAhF,EAAuF;AAC9F,QAAIC,EAAJ,EAAQC,EAAR;;AACA,SAAKR,WAAL,GAAmBA,WAAnB;AACA,SAAKC,mBAAL,GAA2BA,mBAA3B;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKG,CAAL,GAAS,CAAT;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,KAAL,GAAa,SAAb;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAKC,OAAL,GAAe,4CAAf;AACA,SAAKC,OAAL,GAAe,IAAf,CAjB8F,CAiBzE;;AACrB,SAAKC,YAAL,GAAoB,IAAIC,GAAJ,EAApB;AACA,SAAKC,UAAL,GAAkB,EAAlB;AACA,SAAKC,cAAL,GAAsB,CAAtB;AACA,SAAK7C,SAAL,GAAiB,EAAjB,CArB8F,CAqBzE;;AACrB,SAAKa,SAAL,GAAiB,EAAjB,CAtB8F,CAsBzE;;AACrB,SAAKiC,kBAAL,GAA0B,KAA1B;AACA,SAAKC,kBAAL,GAA0B,EAA1B;AACA,SAAKC,aAAL,GAAqB,EAArB;AACA,SAAKC,gBAAL,GAAwB,KAAxB;AACA,SAAKlF,YAAL,GAAoB,EAApB;AACA,SAAKe,iBAAL,GAAyB,KAAK0C,WAAL,CAAiB0B,KAAjB,CAAuB;AAC5CC,MAAAA,cAAc,EAAE,CAAC,EAAD,EAAK,CAAC/I,UAAU,CAAC6E,QAAZ,CAAL,CAD4B;AAE5CmE,MAAAA,OAAO,EAAE,CAAC,IAAD,EAAO,CAAChJ,UAAU,CAAC6E,QAAZ,CAAP,CAFmC;AAG5CoE,MAAAA,WAAW,EAAE,CAAC,EAAD,EAAK,CAACjJ,UAAU,CAAC6E,QAAZ,CAAL,CAH+B;AAI5CqE,MAAAA,OAAO,EAAE,CAAC,EAAD,EAAK,CAAClJ,UAAU,CAAC6E,QAAZ,CAAL,CAJmC;AAK5CsE,MAAAA,YAAY,EAAE,CAAC,EAAD,CAL8B;AAM5CC,MAAAA,UAAU,EAAE,CAAC,EAAD,EAAK,CAACpJ,UAAU,CAAC6E,QAAZ,CAAL,CANgC;AAO5CwE,MAAAA,WAAW,EAAE,CAAC,EAAD,EAAK,CAACrJ,UAAU,CAAC6E,QAAZ,CAAL,CAP+B;AAQ5CyE,MAAAA,QAAQ,EAAE,CAAC,EAAD,EAAK,CAACtJ,UAAU,CAAC6E,QAAZ,EAAsB7E,UAAU,CAACuJ,SAAX,CAAqB,GAArB,CAAtB,CAAL,CARkC;AAS5CC,MAAAA,UAAU,EAAE,CAAC,EAAD,EAAK,CAACxJ,UAAU,CAAC6E,QAAZ,EAAsB7E,UAAU,CAAC8E,OAAX,CAAmB,aAAnB,CAAtB,CAAL,CATgC;AAU5C2E,MAAAA,OAAO,EAAE,CAAC,EAAD,EAAK,CAACzJ,UAAU,CAAC6E,QAAZ,CAAL,CAVmC;AAW5C6E,MAAAA,aAAa,EAAE,KAAKtC,WAAL,CAAiBuC,KAAjB,CAAuB,CAAC,KAAKC,aAAL,EAAD,CAAvB;AAX6B,KAAvB,CAAzB;AAaA,SAAKlC,KAAL,CAAWmC,WAAX,CAAuBC,SAAvB,CAAiCC,MAAM,IAAI;AACvC,UAAIA,MAAJ,EAAY;AACR,aAAKC,KAAL,GAAaD,MAAM,CAAC,OAAD,CAAnB;AACAE,QAAAA,OAAO,CAACC,GAAR,CAAY,KAAKF,KAAjB;AACH;AACJ,KALD;AAMA,UAAMG,KAAK,GAAG,CAACvC,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKJ,MAAL,CAAY6C,oBAAZ,EAAN,MAA8C,IAA9C,IAAsDzC,EAAE,KAAK,KAAK,CAAlE,GAAsE,KAAK,CAA3E,GAA+EA,EAAE,CAAC0C,MAAxF,MAAoG,IAApG,IAA4GzC,EAAE,KAAK,KAAK,CAAxH,GAA4H,KAAK,CAAjI,GAAqIA,EAAE,CAACuC,KAAtJ,CA/C8F,CA+C+D;;AAC7J,QAAIA,KAAJ,EAAW;AACN,WAAKjC,KAAL,GAAaiC,KAAK,CAACjC,KAApB;AACC,WAAKoC,YAAL,GAAoBH,KAAK,CAACG,YAA3B;AACAL,MAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,KAAKI,YAAnC;AACH,KAJD,MAKK;AACD,WAAK/C,MAAL,CAAYgD,QAAZ,CAAqB,CAAE,gCAAF,CAArB;AACH;AACJ;;AACDC,EAAAA,QAAQ,GAAG;AACP,SAAKC,eAAL;;AACA,QAAI,KAAKvC,KAAL,KAAe,MAAnB,EAA2B;AACvB,WAAKwC,cAAL,CAAoB,KAAKV,KAAzB;AACH;AACJ;;AACKU,EAAAA,cAAc,CAACC,SAAD,EAAY;AAAA;;AAAA;AAC5B,UAAI;AACA,cAAMC,GAAG,SAAS,KAAI,CAACvD,mBAAL,CAAyBqD,cAAzB,CAAwCC,SAAxC,EAAmDE,SAAnD,EAAlB;AACA,QAAA,KAAI,CAACP,YAAL,GAAoBM,GAApB;AACAX,QAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCU,GAAjC;;AACA,QAAA,KAAI,CAACE,YAAL,CAAkB,KAAI,CAACR,YAAvB;AACH,OALD,CAMA,OAAOS,KAAP,EAAc;AACVd,QAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBa,KAArB;;AACA,QAAA,KAAI,CAACvD,MAAL,CAAYuD,KAAZ,CAAkB,sBAAlB;AACH;AAV2B;AAW/B;;AACDD,EAAAA,YAAY,CAACE,IAAD,EAAO;AACf,QAAIrD,EAAJ,EAAQC,EAAR,EAAYqD,EAAZ;;AACAhB,IAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCc,IAArC;AACA,UAAME,WAAW,GAAG,KAAKxG,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAApB;;AACA,QAAIuG,WAAJ,EAAiB;AACbA,MAAAA,WAAW,CAACC,eAAZ;AACAD,MAAAA,WAAW,CAACE,sBAAZ;AACH,KAHD,MAIK;AACDnB,MAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ;AACH,KAVc,CAWf;AACA;AACA;;;AACA,SAAKxF,iBAAL,CAAuB2G,UAAvB,CAAkC;AAC9BtC,MAAAA,cAAc,EAAEiC,IAAI,CAACjC,cADS;AAE9BE,MAAAA,WAAW,EAAE+B,IAAI,CAAC/B,WAFY;AAG9BC,MAAAA,OAAO,EAAE8B,IAAI,CAAC9B,OAHgB;AAI9BE,MAAAA,UAAU,EAAE4B,IAAI,CAAC5B,UAJa;AAK9BC,MAAAA,WAAW,EAAE2B,IAAI,CAAC3B,WALY;AAM9BC,MAAAA,QAAQ,EAAE0B,IAAI,CAAC1B,QANe;AAO9BE,MAAAA,UAAU,EAAEwB,IAAI,CAACxB,UAPa;AAQ9BC,MAAAA,OAAO,EAAEuB,IAAI,CAACvB,OARgB;AAS9BO,MAAAA,KAAK,EAAEgB,IAAI,CAAChB;AATkB,KAAlC,EAde,CAyBf;;AACA,UAAMsB,YAAY,GAAG,CACjB,8BADiB,EAEjB,6BAFiB,EAGjB,aAHiB,EAIjB,yBAJiB,CAArB;;AAMA,QAAIA,YAAY,CAACC,QAAb,CAAsBP,IAAI,CAAC9B,OAA3B,CAAJ,EAAyC;AACrC,OAACvB,EAAE,GAAG,KAAKjD,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDgD,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAAC6D,QAAH,CAAYR,IAAI,CAAC9B,OAAjB,CAAlF;AACA,WAAKR,kBAAL,GAA0B,KAA1B;AACH,KAHD,MAIK;AACD,OAACd,EAAE,GAAG,KAAKlD,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDiD,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAAC4D,QAAH,CAAY,OAAZ,CAAlF;AACA,WAAK9C,kBAAL,GAA0B,IAA1B,CAFC,CAE+B;;AAChC,OAACuC,EAAE,GAAG,KAAKvG,iBAAL,CAAuBC,GAAvB,CAA2B,cAA3B,CAAN,MAAsD,IAAtD,IAA8DsG,EAAE,KAAK,KAAK,CAA1E,GAA8E,KAAK,CAAnF,GAAuFA,EAAE,CAACO,QAAH,CAAYR,IAAI,CAAC9B,OAAjB,CAAvF;AACH;;AACD,SAAK7H,QAAL,GAAgB2J,IAAI,CAAChC,OAArB;;AACA,QAAIgC,IAAI,CAACtB,aAAT,EAAwB;AACpB,YAAM+B,eAAe,GAAG,KAAK/G,iBAAL,CAAuBC,GAAvB,CAA2B,eAA3B,CAAxB;AACA8G,MAAAA,eAAe,CAACC,KAAhB;AACAV,MAAAA,IAAI,CAACtB,aAAL,CAAmBiC,OAAnB,CAA4BC,OAAD,IAAa;AACpC,cAAMC,gBAAgB,GAAG,KAAKzE,WAAL,CAAiB0B,KAAjB,CAAuB;AAC5CgD,UAAAA,SAAS,EAAE,CAACF,OAAO,CAACE,SAAT,EAAoB,CAAC9L,UAAU,CAAC6E,QAAZ,CAApB,CADiC;AAE5CkH,UAAAA,QAAQ,EAAE,CAACH,OAAO,CAACG,QAAT,EAAmB,CAAC/L,UAAU,CAAC6E,QAAZ,CAAnB,CAFkC;AAG5CmH,UAAAA,YAAY,EAAE,CAACJ,OAAO,CAACI,YAAT,EAAuB,CAAChM,UAAU,CAAC6E,QAAZ,CAAvB,CAH8B;AAI5CoH,UAAAA,QAAQ,EAAE,CAACL,OAAO,CAACK,QAAT;AAJkC,SAAvB,CAAzB;AAMAR,QAAAA,eAAe,CAACS,IAAhB,CAAqBL,gBAArB;AACH,OARD,EAHoB,CAYpB;AACA;AACA;;AACA,WAAKpF,SAAL,GAAiBuE,IAAI,CAACtB,aAAL,CAAmByC,GAAnB,CAAwBP,OAAD,IAAaA,OAAO,CAACK,QAA5C,CAAjB;AACA,WAAKrG,SAAL,GAAiB,IAAIwG,KAAJ,CAAUpB,IAAI,CAACtB,aAAL,CAAmB/C,MAA7B,EAAqC0F,IAArC,CAA0C,KAA1C,CAAjB;AACA,WAAK1D,kBAAL,GAA0BqC,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACtB,aAAL,CAAmByC,GAAnB,CAAwBP,OAAD,IAAa;AAAE,YAAIjE,EAAJ;;AAAQ,eAAO,CAACA,EAAE,GAAGiE,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACK,QAAhE,MAA8E,IAA9E,IAAsFtE,EAAE,KAAK,KAAK,CAAlG,GAAsG,KAAK,CAA3G,GAA+GA,EAAE,CAAC2E,SAAH,CAAaV,OAAO,CAACK,QAAR,CAAiBM,WAAjB,CAA6B,GAA7B,IAAoC,CAAjD,CAAtH;AAA4K,OAA1N,CAAtE;AACH;AACJ;;AACmB,MAAhB7F,gBAAgB,GAAG;AACnB,WAAO,KAAKhC,iBAAL,CAAuBC,GAAvB,CAA2B,eAA3B,CAAP;AACH;;AACD6H,EAAAA,YAAY,CAACC,KAAD,EAAQ;AAChB,QAAI9E,EAAJ,EAAQC,EAAR;;AACA,UAAM8E,QAAQ,GAAGD,KAAK,CAACE,MAAvB;AACA,SAAKlE,cAAL,GAAsBiE,QAAQ,CAACE,KAAT,CAAejG,MAArC;;AACA,QAAI,KAAK8B,cAAL,GAAsB,GAA1B,EAA+B;AAC3B,OAACd,EAAE,GAAG,KAAKjD,iBAAL,CAAuBC,GAAvB,CAA2B,UAA3B,CAAN,MAAkD,IAAlD,IAA0DgD,EAAE,KAAK,KAAK,CAAtE,GAA0E,KAAK,CAA/E,GAAmFA,EAAE,CAACkF,SAAH,CAAa;AAAEC,QAAAA,SAAS,EAAE;AAAb,OAAb,CAAnF;AACH,KAFD,MAGK;AACD,OAAClF,EAAE,GAAG,KAAKlD,iBAAL,CAAuBC,GAAvB,CAA2B,UAA3B,CAAN,MAAkD,IAAlD,IAA0DiD,EAAE,KAAK,KAAK,CAAtE,GAA0E,KAAK,CAA/E,GAAmFA,EAAE,CAACiF,SAAH,CAAa,IAAb,CAAnF;AACH;AACJ;;AACDjD,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAKxC,WAAL,CAAiB0B,KAAjB,CAAuB;AAC1BgD,MAAAA,SAAS,EAAE,CAAC,EAAD,EAAK,CAAC9L,UAAU,CAAC6E,QAAZ,CAAL,CADe;AAE1BkH,MAAAA,QAAQ,EAAE,CAAC,EAAD,EAAK,CAAC/L,UAAU,CAAC6E,QAAZ,CAAL,CAFgB;AAG1BmH,MAAAA,YAAY,EAAE,CAAC,EAAD,EAAK,CAAChM,UAAU,CAAC6E,QAAZ,CAAL,CAHY;AAI1BoH,MAAAA,QAAQ,EAAE,CAAC,IAAD;AAJgB,KAAvB,CAAP;AAMA,SAAKrG,SAAL,CAAesG,IAAf,CAAoB,KAApB,EAPY,CAOgB;;AAC5B,SAAKzF,SAAL,CAAeyF,IAAf,CAAoB,EAApB,EARY,CAQa;AAC5B;;AACDlF,EAAAA,UAAU,GAAG;AACT,SAAKN,gBAAL,CAAsBwF,IAAtB,CAA2B,KAAKtC,aAAL,EAA3B;AACH;;AACD3D,EAAAA,aAAa,CAACV,KAAD,EAAQ;AACjB,SAAKmB,gBAAL,CAAsBqG,QAAtB,CAA+BxH,KAA/B;AACA,SAAKkB,SAAL,CAAeuG,MAAf,CAAsBzH,KAAtB,EAA6B,CAA7B,EAFiB,CAEgB;;AACjC,SAAKK,SAAL,CAAeoH,MAAf,CAAsBzH,KAAtB,EAA6B,CAA7B,EAHiB,CAGgB;;AACjC,SAAKiD,UAAL,CAAgBwE,MAAhB,CAAuBzH,KAAvB,EAA8B,CAA9B,EAJiB,CAIiB;AACrC;;AACDE,EAAAA,WAAW,CAACF,KAAD,EAAQ;AACf,UAAM0H,aAAa,GAAG,KAAKC,YAAL,CAAkBC,OAAlB,EAAtB;AACA,UAAMC,YAAY,GAAGH,aAAa,CAAC1H,KAAD,CAAb,CAAqB8H,aAA1C,CAFe,CAGf;;AACA,SAAKC,YAAL,CAAkB/H,KAAlB,EAJe,CAKf;;AACA,QAAI,KAAKK,SAAL,CAAeL,KAAf,CAAJ,EAA2B;AACvB6H,MAAAA,YAAY,CAACG,KAAb;AACH,KAFD,MAGK;AACDH,MAAAA,YAAY,CAACI,GAAb,GAAmB,KAAK/G,SAAL,CAAelB,KAAf,CAAnB;AACA6H,MAAAA,YAAY,CAACK,IAAb;AACH,KAZc,CAaf;;;AACA,SAAK7H,SAAL,CAAeL,KAAf,IAAwB,CAAC,KAAKK,SAAL,CAAeL,KAAf,CAAzB;AACH;;AACD+H,EAAAA,YAAY,CAACI,YAAD,EAAe;AACvB,SAAKR,YAAL,CAAkBvB,OAAlB,CAA0B,CAACgC,WAAD,EAAcpI,KAAd,KAAwB;AAC9C,UAAIA,KAAK,KAAKmI,YAAd,EAA4B;AACxBC,QAAAA,WAAW,CAACN,aAAZ,CAA0BE,KAA1B;AACA,aAAK3H,SAAL,CAAeL,KAAf,IAAwB,KAAxB;AACH;AACJ,KALD;AAMH;;AACDqI,EAAAA,SAAS,CAACrI,KAAD,EAAQ;AACb,UAAM0H,aAAa,GAAG,KAAKC,YAAL,CAAkBC,OAAlB,EAAtB;AACA,UAAMC,YAAY,GAAGH,aAAa,CAAC1H,KAAD,CAAb,CAAqB8H,aAA1C;AACAD,IAAAA,YAAY,CAACG,KAAb;AACAH,IAAAA,YAAY,CAACS,WAAb,GAA2B,CAA3B,CAJa,CAIiB;;AAC9B,SAAKjI,SAAL,CAAeL,KAAf,IAAwB,KAAxB;AACH;;AACDuI,EAAAA,cAAc,CAACrB,KAAD,EAAQ;AAClB,QAAI9E,EAAJ;;AACA,QAAIoG,YAAY,GAAGtB,KAAK,CAACE,MAAN,CAAaqB,KAAb,CAAmB,CAAnB,CAAnB,CAFkB,CAGlB;;AACA,QAAIvB,KAAK,CAACE,MAAN,CAAaqB,KAAb,CAAmBrH,MAAnB,KAA8B,CAAlC,EAAqC;AACjC;AACA,WAAKsH,SAAL,GAAiB,IAAjB;AACA,WAAK5M,QAAL,GAAgB,IAAhB;AACA;AACH;;AACD,UAAM6M,WAAW,GAAGjO,aAAa,CAACkO,YAAd,CAA2BJ,YAAY,CAACK,IAAxC,CAApB;AACA,SAAKH,SAAL,GAAiB,IAAII,IAAJ,CAAS,CAACN,YAAD,CAAT,EAAyBG,WAAzB,EAAsC;AAAEI,MAAAA,IAAI,EAAEP,YAAY,CAACO;AAArB,KAAtC,CAAjB;;AACA,QAAI,KAAKL,SAAT,EAAoB;AAChB,YAAMM,WAAW,GAAG,KAAK7J,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAApB;AACA4J,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACC,aAAZ,CAA0BvO,aAAa,CAACwO,iBAAd,CAAgC,IAAhC,EAAsC,KAAKR,SAA3C,CAA1B,CAA1D;AACAM,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACnD,sBAAZ,EAA1D;AACH;;AACD,UAAMsD,QAAQ,GAAG,KAAKT,SAAL,CAAeK,IAAf,CAAoBK,KAApB,CAA0B,GAA1B,EAA+B,CAA/B,CAAjB;AACA,UAAMC,aAAa,GAAG,CAACjH,EAAE,GAAG,KAAKsG,SAAL,CAAeG,IAAf,CAAoBO,KAApB,CAA0B,GAA1B,EAA+BE,GAA/B,EAAN,MAAgD,IAAhD,IAAwDlH,EAAE,KAAK,KAAK,CAApE,GAAwE,KAAK,CAA7E,GAAiFA,EAAE,CAACmH,WAAH,EAAvG;;AACA,QAAIJ,QAAQ,KAAK,OAAb,IAAwBE,aAAa,KAAK,KAA9C,EAAqD;AACjDnC,MAAAA,KAAK,CAACE,MAAN,CAAaC,KAAb,GAAqB,EAArB;AACA,WAAKpF,MAAL,CAAYuH,IAAZ,CAAiB,8CAAjB;AACA,WAAKd,SAAL,GAAiB,IAAjB;AACA,WAAK5M,QAAL,GAAgB,IAAhB;AACA;AACH;;AACD,QAAI,KAAK4M,SAAL,IAAkBS,QAAQ,IAAI,OAAlC,EAA2C;AACvC,YAAMM,MAAM,GAAG,IAAIC,UAAJ,EAAf;AACA,YAAMC,GAAG,GAAG,IAAIC,KAAJ,EAAZ;;AACAH,MAAAA,MAAM,CAACI,MAAP,GAAiBC,CAAD,IAAO;AACnB,YAAI1H,EAAJ,EAAQC,EAAR,EAAYqD,EAAZ;;AACA,aAAK5J,QAAL,GAAgB,CAACsG,EAAE,GAAG0H,CAAC,CAAC1C,MAAR,MAAoB,IAApB,IAA4BhF,EAAE,KAAK,KAAK,CAAxC,GAA4C,KAAK,CAAjD,GAAqDA,EAAE,CAAC2H,MAAxE;;AACA,YAAI,EAAE,CAACrE,EAAE,GAAG,CAACrD,EAAE,GAAG,KAAKlD,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDiD,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAAChD,MAA3F,MAAuG,IAAvG,IAA+GqG,EAAE,KAAK,KAAK,CAA3H,GAA+H,KAAK,CAApI,GAAwIA,EAAE,CAACwD,iBAA7I,CAAJ,EAAqK;AACjK,eAAKc,gBAAL,CAAsBL,GAAtB,EAA2B,SAA3B;AACA,eAAKvL,YAAL,GAAoB;AAAEgJ,YAAAA,MAAM,EAAE;AAAEqB,cAAAA,KAAK,EAAE,CAAC,KAAKC,SAAN;AAAT;AAAV,WAApB;AACH;AACJ,OAPD;;AAQA;AACAe,MAAAA,MAAM,CAACQ,aAAP,CAAqB,KAAKvB,SAA1B;AACH,KAbD,MAcK;AACD,WAAK5M,QAAL,GAAgB,IAAhB,CADC,CACqB;AACzB;;AACD4I,IAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAK+D,SAA9B;AACH;;AACDsB,EAAAA,gBAAgB,CAACE,KAAD,EAAQC,WAAR,EAAqB;AACjC,UAAMC,WAAW,GAAGF,KAAK,CAACG,KAAN,GAAcH,KAAK,CAACI,MAAxC;AACA,UAAMC,OAAO,GAAG,KAAKpL,iBAAL,CAAuBC,GAAvB,CAA2B+K,WAA3B,CAAhB;;AACA,QAAIC,WAAW,KAAK,CAApB,EAAuB;AACnB;AACAG,MAAAA,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACjD,SAAR,CAAkB;AAAEkD,QAAAA,wBAAwB,EAAE;AAA5B,OAAlB,CAAlD;AACH,KAHD,MAIK;AACDD,MAAAA,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACjD,SAAR,CAAkB,IAAlB,CAAlD;AACH;AACJ;;AACDmD,EAAAA,aAAa,GAAG;AACZ,WAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,UAAI,CAAC,KAAKlC,SAAV,EAAqB;AACjBkC,QAAAA,MAAM,CAAC,yBAAD,CAAN;AACA;AACH;;AACDlG,MAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgC,KAAK+D,SAArC;AACA,WAAK5G,mBAAL,CAAyB+I,SAAzB,CAAmC,KAAKnC,SAAxC,EAAmDnE,SAAnD,CAA8Dc,GAAD,IAAS;AAClE,cAAMyF,OAAO,GAAG,KAAKjI,OAAL,GAAe,KAAK6F,SAAL,CAAeG,IAA9C,CADkE,CACd;;AACpD8B,QAAAA,OAAO,CAACG,OAAD,CAAP;AACH,OAHD,EAGItF,KAAD,IAAW;AACVoF,QAAAA,MAAM,CAAC,4BAA4BpF,KAAK,CAACuF,OAAlC,IAA6CvF,KAA9C,CAAN;AACH,OALD;AAMH,KAZM,CAAP;AAaH;;AACDxE,EAAAA,eAAe,CAACkG,KAAD,EAAQlH,KAAR,EAAe;AAC1B,QAAIgL,SAAJ;AACA,UAAMxC,YAAY,GAAGtB,KAAK,CAACE,MAAN,CAAaqB,KAAb,CAAmB,CAAnB,CAArB;;AACA,QAAID,YAAJ,EAAkB;AACd,YAAMG,WAAW,GAAGjO,aAAa,CAACkO,YAAd,CAA2BJ,YAAY,CAACK,IAAxC,CAApB;AACAmC,MAAAA,SAAS,GAAG,IAAIlC,IAAJ,CAAS,CAACN,YAAD,CAAT,EAAyBG,WAAzB,EAAsC;AAAEI,QAAAA,IAAI,EAAEP,YAAY,CAACO;AAArB,OAAtC,CAAZ;AACH,KAHD,MAIK;AACDiC,MAAAA,SAAS,GAAG,IAAZ;AACH;;AACD,QAAI,CAACA,SAAL,EAAgB;AACZ,WAAK9J,SAAL,CAAelB,KAAf,IAAwB,EAAxB;AACA,WAAKqI,SAAL,CAAerI,KAAf;AACA;AACH;;AACD,UAAMiL,aAAa,GAAGD,SAAS,CAACjC,IAAV,CAAeK,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAtB;;AACA,QAAI6B,aAAa,KAAK,OAAtB,EAA+B;AAC3B/D,MAAAA,KAAK,CAACE,MAAN,CAAaC,KAAb,GAAqB,EAArB,CAD2B,CACF;;AACzB,WAAKpF,MAAL,CAAYuH,IAAZ,CAAiB,8BAAjB;AACA;AACH,KApByB,CAqB1B;;;AACA,SAAKvG,UAAL,CAAgBjD,KAAhB,IAAyBgL,SAAzB;AACAtG,IAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BqG,SAA5B;AACA,UAAMvB,MAAM,GAAG,IAAIC,UAAJ,EAAf;;AACAD,IAAAA,MAAM,CAACI,MAAP,GAAgB,MAAM;AAClB;AACA,WAAK3I,SAAL,CAAelB,KAAf,IAAwByJ,MAAM,CAACM,MAA/B;AACH,KAHD;;AAIAN,IAAAA,MAAM,CAACyB,OAAP,GAAiB,MAAM;AACnBxG,MAAAA,OAAO,CAACc,KAAR,CAAc,0BAAd;AACA,WAAKvD,MAAL,CAAYuD,KAAZ,CAAkB,2BAAlB;AACH,KAHD;;AAIAiE,IAAAA,MAAM,CAACQ,aAAP,CAAqBe,SAArB;AACH;;AACDG,EAAAA,gBAAgB,CAAC1C,KAAD,EAAQ;AACpB,UAAM2C,cAAc,GAAG3C,KAAK,CAAC7B,GAAN,CAAU,CAACyE,IAAD,EAAOrL,KAAP,KAAiB,KAAKsL,qBAAL,CAA2BD,IAA3B,EAAiCrL,KAAjC,CAA3B,CAAvB;AACA,WAAO0K,OAAO,CAACa,GAAR,CAAYH,cAAZ,CAAP;AACH,GAvT8B,CAwT/B;;;AACAE,EAAAA,qBAAqB,CAACD,IAAD,EAAOrL,KAAP,EAAc;AAC/B,WAAO,IAAI0K,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,WAAK9I,mBAAL,CAAyB+I,SAAzB,CAAmCQ,IAAnC,EAAyC9G,SAAzC,CAAoDc,GAAD,IAAS;AACxDX,QAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiC0G,IAAI,CAACxC,IAAtC;AACA,cAAMiC,OAAO,GAAG,KAAKjI,OAAL,GAAewI,IAAI,CAACxC,IAApC;AACA,aAAK3H,SAAL,CAAelB,KAAf,IAAwB8K,OAAxB;AACAH,QAAAA,OAAO,CAACG,OAAD,CAAP;AACH,OALD,EAKItF,KAAD,IAAW;AACVd,QAAAA,OAAO,CAACc,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,aAAKvD,MAAL,CAAYuD,KAAZ,CAAkB,6BAAlB;AACAoF,QAAAA,MAAM,CAACpF,KAAD,CAAN;AACH,OATD;AAUH,KAXM,CAAP;AAYH,GAtU8B,CAuU/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAgG,EAAAA,YAAY,CAACtE,KAAD,EAAQ;AAChB,UAAMuE,aAAa,GAAGvE,KAAK,CAACE,MAA5B;AACA,SAAKjE,kBAAL,GAA0BsI,aAAa,CAACpE,KAAd,KAAwB,OAAlD;AACH;;AACDqE,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAK/I,KAAL,KAAe,MAAnB,EAA2B;AACvB,WAAKgJ,cAAL;AACH,KAFD,MAGK;AACD,WAAKC,WAAL;AACH;AACJ;;AACDD,EAAAA,cAAc,GAAG;AACb,QAAIvJ,EAAJ,EAAQC,EAAR,EAAYqD,EAAZ;;AACA,QAAI,EAAE,CAACrD,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKjD,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDgD,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAAC/C,MAA3F,MAAuG,IAAvG,IAA+GgD,EAAE,KAAK,KAAK,CAA3H,GAA+H,KAAK,CAApI,GAAwIA,EAAE,CAACmI,wBAA7I,CAAJ,EAA4K;AACxK,YAAM7E,WAAW,GAAG,KAAKxG,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAApB;;AACA,UAAIuG,WAAJ,EAAiB;AACbA,QAAAA,WAAW,CAACC,eAAZ;AACAD,QAAAA,WAAW,CAACE,sBAAZ;AACAnB,QAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ;AACH,OAJD,MAKK;AACDD,QAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ;AACH;;AACD,UAAI,KAAKxF,iBAAL,CAAuB0M,OAA3B,EAAoC;AAChCC,QAAAA,MAAM,CAACC,IAAP,CAAY,KAAK5M,iBAAL,CAAuB6M,QAAnC,EAA6C5F,OAA7C,CAAqDyC,IAAI,IAAI;AACzD,gBAAM0B,OAAO,GAAG,KAAKpL,iBAAL,CAAuBC,GAAvB,CAA2ByJ,IAA3B,CAAhB;;AACA,cAAI0B,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACsB,OAA9D,EAAuE;AACnEnH,YAAAA,OAAO,CAACC,GAAR,CAAa,oBAAmBkE,IAAK,WAArC,EAAiD0B,OAAO,CAAClL,MAAzD;AACH;AACJ,SALD;AAMA,aAAK4C,MAAL,CAAYuH,IAAZ,CAAiB,2CAAjB;AACA;AACH;;AACD,YAAMyC,UAAU,GAAG,CAAC,CAACvG,EAAE,GAAG,KAAKvG,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDsG,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAAC2B,KAAtF,IAA+F,KAAKoD,aAAL,EAA/F,GAAsHC,OAAO,CAACC,OAAR,CAAgB,KAAK5F,YAAL,CAAkBtB,OAAlC,CAAzI;AACA,WAAK1B,iBAAL,CAAuBmK,IAAvB,CAA4B,eAA5B;AACAD,MAAAA,UAAU,CAACE,IAAX,CAAiBrB,OAAD,IAAa;AACzB,eAAO,KAAKK,gBAAL,CAAsB,KAAKlI,UAA3B,EAAuCkJ,IAAvC,CAA6CjL,SAAD,IAAe;AAC9D,cAAIkB,EAAJ,EAAQC,EAAR,EAAYqD,EAAZ,EAAgB0G,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CC,EAA5C;;AACAjI,UAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqC,KAAK1B,UAA1C;AACA,eAAK9B,gBAAL,CAAsB6K,QAAtB,CAA+B5F,OAA/B,CAAuC,CAACmE,OAAD,EAAUvK,KAAV,KAAoB;AACvD,gBAAIoC,EAAJ;;AACA,aAACA,EAAE,GAAGmI,OAAO,CAACnL,GAAR,CAAY,UAAZ,CAAN,MAAmC,IAAnC,IAA2CgD,EAAE,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,EAAE,CAAC6D,QAAH,CAAY,KAAK/E,SAAL,CAAelB,KAAf,CAAZ,CAApE;AACH,WAHD;AAIA,eAAK4M,UAAL,GAAkB;AACdnI,YAAAA,KAAK,EAAE,KAAKM,YAAL,CAAkBN,KADX;AAEdjB,YAAAA,cAAc,EAAE,CAACpB,EAAE,GAAG,KAAKjD,iBAAL,CAAuBC,GAAvB,CAA2B,gBAA3B,CAAN,MAAwD,IAAxD,IAAgEgD,EAAE,KAAK,KAAK,CAA5E,GAAgF,KAAK,CAArF,GAAyFA,EAAE,CAACiF,KAF9F;AAGd5D,YAAAA,OAAO,EAAEqH,OAHK;AAIdpH,YAAAA,WAAW,EAAE,CAACrB,EAAE,GAAG,KAAKlD,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6DiD,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAACgF,KAJxF;AAKd1D,YAAAA,OAAO,EAAE,CAAC,CAAC+B,EAAE,GAAG,KAAKvG,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDsG,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAAC2B,KAAtF,MAAiG,OAAjG,GAA2G,CAAC+E,EAAE,GAAG,KAAKjN,iBAAL,CAAuBC,GAAvB,CAA2B,cAA3B,CAAN,MAAsD,IAAtD,IAA8DgN,EAAE,KAAK,KAAK,CAA1E,GAA8E,KAAK,CAAnF,GAAuFA,EAAE,CAAC/E,KAArM,GAA6M,CAACgF,EAAE,GAAG,KAAKlN,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDiN,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAAChF,KAL7R;AAMdxD,YAAAA,UAAU,EAAE,CAACyI,EAAE,GAAG,KAAKnN,iBAAL,CAAuBC,GAAvB,CAA2B,YAA3B,CAAN,MAAoD,IAApD,IAA4DkN,EAAE,KAAK,KAAK,CAAxE,GAA4E,KAAK,CAAjF,GAAqFA,EAAE,CAACjF,KANtF;AAOdvD,YAAAA,WAAW,EAAE,CAACyI,EAAE,GAAG,KAAKpN,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6DmN,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAAClF,KAPxF;AAQdtD,YAAAA,QAAQ,EAAE,CAACyI,EAAE,GAAG,KAAKrN,iBAAL,CAAuBC,GAAvB,CAA2B,UAA3B,CAAN,MAAkD,IAAlD,IAA0DoN,EAAE,KAAK,KAAK,CAAtE,GAA0E,KAAK,CAA/E,GAAmFA,EAAE,CAACnF,KARlF;AASdpD,YAAAA,UAAU,EAAE,CAACwI,EAAE,GAAG,KAAKtN,iBAAL,CAAuBC,GAAvB,CAA2B,YAA3B,CAAN,MAAoD,IAApD,IAA4DqN,EAAE,KAAK,KAAK,CAAxE,GAA4E,KAAK,CAAjF,GAAqFA,EAAE,CAACpF,KATtF;AAUdnD,YAAAA,OAAO,EAAE,CAACwI,EAAE,GAAG,KAAKvN,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDsN,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAACrF,KAVhF;AAWdlD,YAAAA,aAAa,EAAE,CAACwI,EAAE,GAAG,KAAKxN,iBAAL,CAAuBC,GAAvB,CAA2B,eAA3B,CAAN,MAAuD,IAAvD,IAA+DuN,EAAE,KAAK,KAAK,CAA3E,GAA+E,KAAK,CAApF,GAAwFA,EAAE,CAACtF;AAX5F,WAAlB;AAaA3C,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiC,KAAKiI,UAAtC;AACA,iBAAO,KAAK9K,mBAAL,CAAyB6J,cAAzB,CAAwC,KAAKiB,UAA7C,EAAyDtH,SAAzD,EAAP;AACH,SAtBM,CAAP;AAuBH,OAxBD,EAwBG6G,IAxBH,CAwBS9G,GAAD,IAAS;AACb,aAAKtD,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;;AACA,YAAIxH,GAAG,CAACyH,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,eAAK7K,MAAL,CAAY8K,OAAZ,CAAoB,gCAApB;AACA,eAAKC,aAAL;AACA,eAAKhL,MAAL,CAAYgD,QAAZ,CAAqB,CAAC,gCAAD,CAArB;AACH,SAJD,MAKK;AACD,eAAK/C,MAAL,CAAYuD,KAAZ,CAAkB,uBAAlB;AACH;AACJ,OAlCD,EAkCGyH,KAlCH,CAkCUzH,KAAD,IAAW;AAChB,aAAKzD,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACA,cAAMK,YAAY,GAAG1H,KAAK,KAAK,2BAAV,GAAwCA,KAAxC,GAAgD,0BAArE;AACA,aAAKvD,MAAL,CAAYuD,KAAZ,CAAkB0H,YAAlB;AACH,OAtCD;AAuCH,KA7DD,MA8DK;AACD,WAAKjL,MAAL,CAAYuH,IAAZ,CAAiB,yBAAjB;AACH;AACJ;;AACDoC,EAAAA,WAAW,GAAG;AACVlH,IAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwB,KAAKxF,iBAAL,CAAuBkI,KAA/C;;AACA,QAAI,KAAKlI,iBAAL,CAAuB0M,OAA3B,EAAoC;AAChC,WAAK5J,MAAL,CAAYuH,IAAZ,CAAiB,iCAAjB;AACA;AACH;;AACD,SAAKzH,iBAAL,CAAuBmK,IAAvB,CAA4B,eAA5B;AACA,SAAKzB,aAAL,GAAqB0B,IAArB,CAA0B,MAAM;AAC5B,YAAMrB,OAAO,GAAG,KAAKjI,OAAL,GAAe,KAAK6F,SAAL,CAAeG,IAA9C,CAD4B,CACwB;AACpD;;AACA,WAAKsC,gBAAL,CAAsB,KAAKlI,UAA3B,EAAuCkJ,IAAvC,CAA6CjL,SAAD,IAAe;AACvD,YAAIkB,EAAJ,EAAQC,EAAR,EAAYqD,EAAZ,EAAgB0G,EAAhB,EAAoBC,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CC,EAA5C;;AACAjI,QAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B,KAAK1B,UAApC;AACA,aAAK9B,gBAAL,CAAsB6K,QAAtB,CAA+B5F,OAA/B,CAAuC,CAACmE,OAAD,EAAUvK,KAAV,KAAoB;AACvD,cAAIoC,EAAJ;;AACA,gBAAMmB,KAAK,GAAGgH,OAAd;AACA,WAACnI,EAAE,GAAGmB,KAAK,CAACnE,GAAN,CAAU,UAAV,CAAN,MAAiC,IAAjC,IAAyCgD,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAAC6D,QAAH,CAAY/E,SAAS,CAAClB,KAAD,CAArB,CAAlE,CAHuD,CAG0C;AACpG,SAJD,EAHuD,CAQvD;;AACA,cAAMyF,IAAI,GAAG;AACTjC,UAAAA,cAAc,EAAE,CAACpB,EAAE,GAAG,KAAKjD,iBAAL,CAAuBC,GAAvB,CAA2B,gBAA3B,CAAN,MAAwD,IAAxD,IAAgEgD,EAAE,KAAK,KAAK,CAA5E,GAAgF,KAAK,CAArF,GAAyFA,EAAE,CAACiF,KADnG;AAET5D,UAAAA,OAAO,EAAEqH,OAFA;AAGTpH,UAAAA,WAAW,EAAE,CAACrB,EAAE,GAAG,KAAKlD,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6DiD,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAACgF,KAH7F;AAIT1D,UAAAA,OAAO,EAAE,CAAC,CAAC+B,EAAE,GAAG,KAAKvG,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDsG,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAAC2B,KAAtF,MAAiG,OAAjG,GAA2G,CAAC+E,EAAE,GAAG,KAAKjN,iBAAL,CAAuBC,GAAvB,CAA2B,cAA3B,CAAN,MAAsD,IAAtD,IAA8DgN,EAAE,KAAK,KAAK,CAA1E,GAA8E,KAAK,CAAnF,GAAuFA,EAAE,CAAC/E,KAArM,GAA6M,CAACgF,EAAE,GAAG,KAAKlN,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDiN,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAAChF,KAJlS;AAKTxD,UAAAA,UAAU,EAAE,CAACyI,EAAE,GAAG,KAAKnN,iBAAL,CAAuBC,GAAvB,CAA2B,YAA3B,CAAN,MAAoD,IAApD,IAA4DkN,EAAE,KAAK,KAAK,CAAxE,GAA4E,KAAK,CAAjF,GAAqFA,EAAE,CAACjF,KAL3F;AAMTvD,UAAAA,WAAW,EAAE,CAACyI,EAAE,GAAG,KAAKpN,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6DmN,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAAClF,KAN7F;AAOTtD,UAAAA,QAAQ,EAAE,CAACyI,EAAE,GAAG,KAAKrN,iBAAL,CAAuBC,GAAvB,CAA2B,UAA3B,CAAN,MAAkD,IAAlD,IAA0DoN,EAAE,KAAK,KAAK,CAAtE,GAA0E,KAAK,CAA/E,GAAmFA,EAAE,CAACnF,KAPvF;AAQTpD,UAAAA,UAAU,EAAE,CAACwI,EAAE,GAAG,KAAKtN,iBAAL,CAAuBC,GAAvB,CAA2B,YAA3B,CAAN,MAAoD,IAApD,IAA4DqN,EAAE,KAAK,KAAK,CAAxE,GAA4E,KAAK,CAAjF,GAAqFA,EAAE,CAACpF,KAR3F;AASTnD,UAAAA,OAAO,EAAE,CAACwI,EAAE,GAAG,KAAKvN,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAAN,MAAiD,IAAjD,IAAyDsN,EAAE,KAAK,KAAK,CAArE,GAAyE,KAAK,CAA9E,GAAkFA,EAAE,CAACrF,KATrF;AAUTlD,UAAAA,aAAa,EAAE,CAACwI,EAAE,GAAG,KAAKxN,iBAAL,CAAuBC,GAAvB,CAA2B,eAA3B,CAAN,MAAuD,IAAvD,IAA+DuN,EAAE,KAAK,KAAK,CAA3E,GAA+E,KAAK,CAApF,GAAwFA,EAAE,CAACtF;AAVjG,SAAb;AAYA3C,QAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCc,IAAjC;AACA,aAAK3D,mBAAL,CAAyB8J,WAAzB,CAAqCnG,IAArC,EAA2ClB,SAA3C,CAAsDc,GAAD,IAAS;AAC1D,cAAIA,GAAG,CAACyH,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,iBAAK/K,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACAnI,YAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyCU,GAAzC;AACA,iBAAKpD,MAAL,CAAY8K,OAAZ,CAAoB,8BAApB;AACA,iBAAKC,aAAL;AACA,iBAAKhL,MAAL,CAAYgD,QAAZ,CAAqB,CAAC,gCAAD,CAArB;AACAN,YAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ;AACH,WAPD,MAQK;AACD,iBAAK5C,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACA,iBAAK5K,MAAL,CAAYuD,KAAZ,CAAkB,sBAAlB;AACH;AACJ,SAbD,EAaIA,KAAD,IAAW;AACV,eAAKzD,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACAnI,UAAAA,OAAO,CAACc,KAAR,CAAc,qBAAd,EAAqCA,KAArC;AACA,eAAKvD,MAAL,CAAYuD,KAAZ,CAAkB,sBAAlB;AACH,SAjBD;AAkBH,OAxCD,EAwCGyH,KAxCH,CAwCUzH,KAAD,IAAW;AAChB,aAAKzD,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACAnI,QAAAA,OAAO,CAACc,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;AACA,aAAKvD,MAAL,CAAYuD,KAAZ,CAAkB,0BAAlB;AACH,OA5CD;AA6CH,KAhDD,EAgDGyH,KAhDH,CAgDUzH,KAAD,IAAW;AAChB,WAAKzD,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACAnI,MAAAA,OAAO,CAACc,KAAR,CAAc,uBAAd,EAAuCA,KAAvC;AACA,WAAKvD,MAAL,CAAYuD,KAAZ,CAAkB,oBAAlB;AACH,KApDD;AAqDH;;AACDwH,EAAAA,aAAa,GAAG;AACZ,SAAKlL,mBAAL,CAAyBkL,aAAzB,GAAyCzI,SAAzC,CAAmD;AAC/C4I,MAAAA,IAAI,EAAG9H,GAAD,IAAS;AACX,YAAIA,GAAG,CAACyH,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,eAAKM,WAAL,GAAmB/H,GAAG,CAACI,IAAvB;AACA,eAAK1D,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACAnI,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6B,KAAKyI,WAAlC;AACH,SAJD,MAKK;AACD,eAAKrL,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACAnI,UAAAA,OAAO,CAACc,KAAR,CAAc,oCAAd,EAAoDH,GAAG,CAACgI,MAAxD;AACH;AACJ,OAX8C;AAY/C7H,MAAAA,KAAK,EAAGA,KAAD,IAAW;AACd,aAAKzD,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACAnI,QAAAA,OAAO,CAACc,KAAR,CAAc,0CAAd,EAA0DA,KAA1D;AACH;AAf8C,KAAnD;AAiBH;;AACDN,EAAAA,eAAe,GAAG;AACd;AACA,SAAKpD,mBAAL,CAAyBoD,eAAzB,GAA2CX,SAA3C,CAAsDc,GAAD,IAAS;AAC1D,UAAKA,GAAG,CAACyH,UAAJ,GAAiB,GAAtB,EAA4B;AACxB,aAAKQ,UAAL,GAAkBjI,GAAG,CAACI,IAAtB;AACAf,QAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB,KAAK2I,UAA5B;AACH,OAHD,MAIK;AACD5I,QAAAA,OAAO,CAACc,KAAR,CAAc,kCAAd,EAAkDH,GAAG,CAACgI,MAAtD;AACH;AACJ,KARD,EAQI7H,KAAD,IAAW;AACV,WAAKzD,iBAAL,CAAuB8K,IAAvB,CAA4B,eAA5B;AACAnI,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBa,KAArB;AACH,KAXD;AAYH;;AACD5I,EAAAA,WAAW,CAACuN,WAAD,EAAc;AACrB,SAAK7G,gBAAL,GAAwB,IAAxB;AACH;;AACDrF,EAAAA,WAAW,CAACkM,WAAD,EAAc;AACrB,SAAK7G,gBAAL,GAAwB,KAAxB;AACH;;AACDpG,EAAAA,OAAO,CAAC4M,CAAD,EAAI;AACP,SAAKhO,QAAL,GAAgBgO,CAAC,CAACyD,MAAlB;AACH;;AACD/P,EAAAA,WAAW,GAAG,CACb;;AACDH,EAAAA,OAAO,GAAG,CACT;;AACDM,EAAAA,SAAS,GAAG;AACR,SAAKsE,MAAL,CAAYuD,KAAZ,CAAkB,sBAAlB;AACH;;AACD1H,EAAAA,gBAAgB,CAACqM,WAAD,EAAc;AAC1B,UAAMvB,YAAY,GAAI4E,QAAD,IAAc;AAC/B,YAAMC,gBAAgB,GAAG,IAAIC,IAAJ,GAAWC,OAAX,EAAzB;AACA,YAAMC,oBAAoB,GAAGJ,QAAQ,CAACpE,KAAT,CAAe,GAAf,EAAoByE,KAApB,CAA0B,CAA1B,EAA6B,CAAC,CAA9B,EAAiCC,IAAjC,CAAsC,GAAtC,CAA7B;AACA,YAAMC,SAAS,GAAGP,QAAQ,CAACpE,KAAT,CAAe,GAAf,EAAoBE,GAApB,EAAlB;AACA,YAAM0E,WAAW,GAAGJ,oBAAoB,CAACK,OAArB,CAA6B,UAA7B,EAAyC,EAAzC,CAApB;AACA,aAAQ,GAAED,WAAY,IAAGP,gBAAiB,IAAGM,SAAU,EAAvD;AACH,KAND;;AAOA,QAAI,KAAKjS,QAAL,IAAiB,KAAK4M,SAA1B,EAAqC;AACjC;AACA,YAAMwF,IAAI,GAAG,KAAKC,aAAL,CAAmB,KAAKrS,QAAxB,CAAb,CAFiC,CAGjC;;AACA,YAAM6M,WAAW,GAAGC,YAAY,CAAC,KAAKF,SAAL,CAAeG,IAAhB,CAAhC;AACA,WAAKH,SAAL,GAAiB,IAAII,IAAJ,CAAS,CAACoF,IAAD,CAAT,EAAiBvF,WAAjB,EAA8B;AAAEI,QAAAA,IAAI,EAAE,KAAKL,SAAL,CAAeK;AAAvB,OAA9B,CAAjB;AACArE,MAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6B,KAAK+D,SAAlC,EANiC,CAOjC;;AACA,WAAKtK,YAAL,GAAoB;AAAEgJ,QAAAA,MAAM,EAAE;AAAEqB,UAAAA,KAAK,EAAE,CAAC,KAAKC,SAAN;AAAT;AAAV,OAApB;AACA,YAAM0F,WAAW,GAAG,KAAKjP,iBAAL,CAAuBC,GAAvB,CAA2B,SAA3B,CAApB;AACAgP,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACxI,eAAZ,EAA1D;AACAwI,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACvI,sBAAZ,EAA1D;AACA,WAAK5H,WAAL,CAAiB,SAAjB;AACH;AACJ;;AACDkQ,EAAAA,aAAa,CAACE,OAAD,EAAU;AACnB,UAAMC,UAAU,GAAGC,IAAI,CAACF,OAAO,CAACjF,KAAR,CAAc,GAAd,EAAmB,CAAnB,CAAD,CAAvB;AACA,UAAMoF,UAAU,GAAGH,OAAO,CAACjF,KAAR,CAAc,GAAd,EAAmB,CAAnB,EAAsBA,KAAtB,CAA4B,GAA5B,EAAiC,CAAjC,EAAoCA,KAApC,CAA0C,GAA1C,EAA+C,CAA/C,CAAnB;AACA,UAAMqF,WAAW,GAAG,IAAIC,WAAJ,CAAgBJ,UAAU,CAAClN,MAA3B,CAApB;AACA,UAAMuN,UAAU,GAAG,IAAIC,UAAJ,CAAeH,WAAf,CAAnB;;AACA,SAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,UAAU,CAAClN,MAA/B,EAAuCyN,CAAC,EAAxC,EAA4C;AACxCF,MAAAA,UAAU,CAACE,CAAD,CAAV,GAAgBP,UAAU,CAACQ,UAAX,CAAsBD,CAAtB,CAAhB;AACH;;AACD,WAAO,IAAIE,IAAJ,CAAS,CAACJ,UAAD,CAAT,EAAuB;AAAE5F,MAAAA,IAAI,EAAEyF;AAAR,KAAvB,CAAP;AACH;;AA1jB8B;;AA4jBnC7M,qBAAqB,CAACqN,IAAtB,GAA6B,SAASC,6BAAT,CAAuCC,CAAvC,EAA0C;AAAE,SAAO,KAAKA,CAAC,IAAIvN,qBAAV,EAAiChH,EAAE,CAACwU,iBAAH,CAAqBvU,EAAE,CAACwU,WAAxB,CAAjC,EAAuEzU,EAAE,CAACwU,iBAAH,CAAqBtU,EAAE,CAACwU,mBAAxB,CAAvE,EAAqH1U,EAAE,CAACwU,iBAAH,CAAqBrU,EAAE,CAACwU,iBAAxB,CAArH,EAAiK3U,EAAE,CAACwU,iBAAH,CAAqBpU,EAAE,CAACwU,MAAxB,CAAjK,EAAkM5U,EAAE,CAACwU,iBAAH,CAAqBnU,EAAE,CAACwU,aAAxB,CAAlM,EAA0O7U,EAAE,CAACwU,iBAAH,CAAqBlU,EAAE,CAACwU,QAAxB,CAA1O,EAA6Q9U,EAAE,CAACwU,iBAAH,CAAqBpU,EAAE,CAAC2U,cAAxB,CAA7Q,CAAP;AAA+T,CAAxY;;AACA/N,qBAAqB,CAACgO,IAAtB,GAA6B,aAAchV,EAAE,CAACiV,iBAAH,CAAqB;AAAE7G,EAAAA,IAAI,EAAEpH,qBAAR;AAA+BkO,EAAAA,SAAS,EAAE,CAAC,CAAC,mBAAD,CAAD,CAA1C;AAAmEC,EAAAA,SAAS,EAAE,SAASC,2BAAT,CAAqCvU,EAArC,EAAyCC,GAAzC,EAA8C;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AAClMb,MAAAA,EAAE,CAACqV,WAAH,CAAe3U,GAAf,EAAoB,CAApB;AACAV,MAAAA,EAAE,CAACqV,WAAH,CAAe1U,GAAf,EAAoB,CAApB;AACH;;AAAC,QAAIE,EAAE,GAAG,CAAT,EAAY;AACV,UAAIyU,EAAJ;;AACAtV,MAAAA,EAAE,CAACuV,cAAH,CAAkBD,EAAE,GAAGtV,EAAE,CAACwV,WAAH,EAAvB,MAA6C1U,GAAG,CAAC2U,UAAJ,GAAiBH,EAAE,CAACI,KAAjE;AACA1V,MAAAA,EAAE,CAACuV,cAAH,CAAkBD,EAAE,GAAGtV,EAAE,CAACwV,WAAH,EAAvB,MAA6C1U,GAAG,CAACkM,YAAJ,GAAmBsI,EAAhE;AACH;AAAE,GAPyD;AAOvDK,EAAAA,KAAK,EAAE,EAPgD;AAO5CC,EAAAA,IAAI,EAAE,EAPsC;AAOlCC,EAAAA,MAAM,EAAE,CAAC,CAAC,CAAD,EAAI,iBAAJ,CAAD,EAAyB,CAAC,CAAD,EAAI,KAAJ,CAAzB,EAAqC,CAAC,CAAD,EAAI,QAAJ,EAAc,aAAd,EAA6B,cAA7B,CAArC,EAAmF,CAAC,CAAD,EAAI,MAAJ,CAAnF,EAAgG,CAAC,CAAD,EAAI,aAAJ,EAAmB,YAAnB,EAAiC,YAAjC,EAA+C,aAA/C,EAA8D,YAA9D,EAA4E,aAA5E,EAA2F,MAA3F,CAAhG,EAAoM,CAAC,CAAD,EAAI,WAAJ,CAApM,EAAsN,CAAC,SAAD,EAAY,qBAAZ,EAAmC,CAAnC,EAAsC,cAAtC,EAAsD,CAAtD,EAAyD,WAAzD,EAAsE,UAAtE,CAAtN,EAAyS,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAAzS,EAAwU,CAAC,KAAD,EAAQ,gBAAR,EAA0B,CAA1B,EAA6B,gBAA7B,CAAxU,EAAwX,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,gBAApC,EAAsD,UAAtD,EAAkE,EAAlE,EAAsE,aAAtE,EAAqF,YAArF,EAAmG,CAAnG,EAAsG,cAAtG,EAAsH,iBAAtH,EAAyI,CAAzI,EAA4I,UAA5I,CAAxX,EAAihB,CAAC,KAAD,EAAQ,SAAR,EAAmB,CAAnB,EAAsB,gBAAtB,CAAjhB,EAA0jB,CAAC,CAAD,EAAI,sBAAJ,CAA1jB,EAAulB,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,SAApC,EAA+C,UAA/C,EAA2D,EAA3D,EAA+D,QAA/D,EAAyE,SAAzE,EAAoF,CAApF,EAAuF,cAAvF,EAAuG,iBAAvG,EAA0H,CAA1H,EAA6H,UAA7H,EAAyI,QAAzI,CAAvlB,EAA2uB,CAAC,KAAD,EAAQ,eAAR,EAAyB,OAAzB,EAAkC,aAAlC,EAAiD,CAAjD,EAAoD,KAApD,EAA2D,CAA3D,EAA8D,MAA9D,CAA3uB,EAAkzB,CAAC,OAAD,EAAU,SAAV,EAAqB,CAArB,EAAwB,MAAxB,CAAlzB,EAAm1B,CAAC,OAAD,EAAU,kBAAV,EAA8B,CAA9B,EAAiC,OAAjC,EAA0C,CAA1C,EAA6C,MAA7C,CAAn1B,EAAy4B,CAAC,OAAD,EAAU,8BAAV,EAA0C,CAA1C,EAA6C,MAA7C,CAAz4B,EAA+7B,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,gBAA1B,CAA/7B,EAA4+B,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,aAApC,EAAmD,UAAnD,EAA+D,EAA/D,EAAmE,aAAnE,EAAkF,iBAAlF,EAAqG,CAArG,EAAwG,cAAxG,EAAwH,iBAAxH,EAA2I,CAA3I,EAA8I,UAA9I,CAA5+B,EAAuoC,CAAC,KAAD,EAAQ,SAAR,EAAmB,CAAnB,EAAsB,gBAAtB,CAAvoC,EAAgrC,CAAC,IAAD,EAAO,SAAP,EAAkB,iBAAlB,EAAqC,SAArC,EAAgD,UAAhD,EAA4D,EAA5D,EAAgE,CAAhE,EAAmE,cAAnE,EAAmF,iBAAnF,EAAsG,CAAtG,EAAyG,QAAzG,CAAhrC,EAAoyC,CAAC,OAAD,EAAU,EAAV,EAAc,UAAd,EAA0B,EAA1B,EAA8B,UAA9B,EAA0C,EAA1C,CAApyC,EAAm1C,CAAC,OAAD,EAAU,8BAAV,CAAn1C,EAA83C,CAAC,OAAD,EAAU,6BAAV,CAA93C,EAAw6C,CAAC,OAAD,EAAU,aAAV,CAAx6C,EAAk8C,CAAC,OAAD,EAAU,yBAAV,CAAl8C,EAAw+C,CAAC,OAAD,EAAU,OAAV,CAAx+C,EAA4/C,CAAC,OAAD,EAAU,qBAAV,EAAiC,CAAjC,EAAoC,MAApC,CAA5/C,EAAyiD,CAAC,KAAD,EAAQ,YAAR,EAAsB,CAAtB,EAAyB,gBAAzB,CAAziD,EAAqlD,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,EAAyB,MAAzB,EAAiC,iBAAjC,EAAoD,YAApD,EAAkE,UAAlE,EAA8E,EAA9E,EAAkF,aAAlF,EAAiG,iBAAjG,EAAoH,CAApH,EAAuH,cAAvH,EAAuI,iBAAvI,EAA0J,CAA1J,EAA6J,UAA7J,CAArlD,EAA+vD,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,gBAA1B,CAA/vD,EAA4yD,CAAC,IAAD,EAAO,eAAP,EAAwB,iBAAxB,EAA2C,aAA3C,EAA0D,UAA1D,EAAsE,EAAtE,EAA0E,CAA1E,EAA6E,cAA7E,EAA6F,iBAA7F,CAA5yD,EAA65D,CAAC,CAAD,EAAI,OAAJ,EAAa,CAAb,EAAgB,OAAhB,EAAyB,SAAzB,CAA75D,EAAk8D,CAAC,KAAD,EAAQ,YAAR,EAAsB,CAAtB,EAAyB,gBAAzB,CAAl8D,EAA8+D,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,YAApC,EAAkD,UAAlD,EAA8D,EAA9D,EAAkE,aAAlE,EAAiF,WAAjF,EAA8F,CAA9F,EAAiG,cAAjG,EAAiH,iBAAjH,EAAoI,CAApI,EAAuI,UAAvI,CAA9+D,EAAkoE,CAAC,KAAD,EAAQ,SAAR,EAAmB,CAAnB,EAAsB,gBAAtB,CAAloE,EAA2qE,CAAC,IAAD,EAAO,SAAP,EAAkB,iBAAlB,EAAqC,SAArC,EAAgD,UAAhD,EAA4D,EAA5D,EAAgE,CAAhE,EAAmE,cAAnE,EAAmF,iBAAnF,CAA3qE,EAAkxE,CAAC,OAAD,EAAU,KAAV,CAAlxE,EAAoyE,CAAC,OAAD,EAAU,OAAV,CAApyE,EAAwzE,CAAC,OAAD,EAAU,QAAV,CAAxzE,EAA60E,CAAC,OAAD,EAAU,SAAV,CAA70E,EAAm2E,CAAC,OAAD,EAAU,aAAV,CAAn2E,EAA63E,CAAC,OAAD,EAAU,QAAV,CAA73E,EAAk5E,CAAC,KAAD,EAAQ,UAAR,EAAoB,CAApB,EAAuB,gBAAvB,CAAl5E,EAA47E,CAAC,IAAD,EAAO,UAAP,EAAmB,iBAAnB,EAAsC,UAAtC,EAAkD,UAAlD,EAA8D,EAA9D,EAAkE,MAAlE,EAA0E,GAA1E,EAA+E,MAA/E,EAAuF,IAAvF,EAA6F,aAA7F,EAA4G,aAA5G,EAA2H,WAA3H,EAAwI,KAAxI,EAA+I,CAA/I,EAAkJ,cAAlJ,EAAkK,iBAAlK,EAAqL,CAArL,EAAwL,UAAxL,EAAoM,OAApM,CAA57E,EAA0oF,CAAC,CAAD,EAAI,iBAAJ,CAA1oF,EAAkqF,CAAC,eAAD,EAAkB,eAAlB,CAAlqF,EAAssF,CAAC,CAAD,EAAI,gBAAJ,EAAsB,MAAtB,EAA8B,MAA9B,CAAtsF,EAA6uF,CAAC,OAAD,EAAU,UAAV,EAAsB,CAAtB,EAAyB,eAAzB,EAA0C,CAA1C,EAA6C,OAA7C,EAAsD,SAAtD,CAA7uF,EAA+yF,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,gDAA5B,EAA8E,CAA9E,EAAiF,OAAjF,EAA0F,CAA1F,EAA6F,MAA7F,CAA/yF,EAAq5F,CAAC,CAAD,EAAI,aAAJ,EAAmB,MAAnB,CAAr5F,EAAi7F,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,sBAA5B,EAAoD,CAApD,EAAuD,MAAvD,CAAj7F,EAAi/F,CAAC,MAAD,EAAS,QAAT,EAAmB,YAAnB,EAAiC,iCAAjC,EAAoE,CAApE,EAAuE,KAAvE,EAA8E,WAA9E,CAAj/F,EAA6kG,CAAC,KAAD,EAAQ,eAAR,EAAyB,CAAzB,EAA4B,aAA5B,EAA2C,CAA3C,EAA8C,KAA9C,CAA7kG,EAAmoG,CAAC,CAAD,EAAI,SAAJ,CAAnoG,EAAmpG,CAAC,CAAD,EAAI,kBAAJ,EAAwB,CAAxB,EAA2B,OAA3B,CAAnpG,EAAwrG,CAAC,CAAD,EAAI,mBAAJ,EAAyB,WAAzB,CAAxrG,EAA+tG,CAAC,CAAD,EAAI,eAAJ,EAAqB,MAArB,EAA6B,aAA7B,CAA/tG,EAA4wG,CAAC,CAAD,EAAI,MAAJ,CAA5wG,EAAyxG,CAAC,QAAD,EAAW,KAAX,EAAkB,CAAlB,EAAqB,sBAArB,EAA6C,MAA7C,EAAqD,CAArD,EAAwD,mBAAxD,EAA6E,aAA7E,EAA4F,qBAA5F,EAAmH,cAAnH,EAAmI,aAAnI,EAAkJ,cAAlJ,EAAkK,iBAAlK,CAAzxG,EAA+8G,CAAC,CAAD,EAAI,KAAJ,EAAW,aAAX,EAA0B,QAA1B,EAAoC,KAApC,EAA2C,CAA3C,EAA8C,OAA9C,CAA/8G,EAAugH,CAAC,CAAD,EAAI,KAAJ,EAAW,eAAX,EAA4B,QAA5B,EAAsC,KAAtC,EAA6C,CAA7C,EAAgD,OAAhD,CAAvgH,EAAikH,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,gBAA3B,CAAjkH,EAA+mH,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,cAApC,EAAoD,aAApD,EAAmE,YAAnE,EAAiF,CAAjF,EAAoF,cAApF,EAAoG,iBAApG,CAA/mH,EAAuuH,CAAC,CAAD,EAAI,OAAJ,CAAvuH,EAAqvH,CAAC,CAAD,EAAI,MAAJ,CAArvH,EAAkwH,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,EAAmB,CAAnB,EAAsB,eAAtB,CAAlwH,EAA0yH,CAAC,CAAD,EAAI,WAAJ,CAA1yH,EAA4zH,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,CAA5zH,EAAg1H,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAAh1H,EAA+2H,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,gBAAxB,EAA0C,UAA1C,CAA/2H,EAAs6H,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,WAApC,EAAiD,UAAjD,EAA6D,EAA7D,EAAiE,aAAjE,EAAgF,aAAhF,EAA+F,CAA/F,EAAkG,cAAlG,EAAkH,iBAAlH,CAAt6H,EAA4iI,CAAC,KAAD,EAAQ,UAAR,EAAoB,CAApB,EAAuB,gBAAvB,EAAyC,UAAzC,CAA5iI,EAAkmI,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,UAApC,EAAgD,UAAhD,EAA4D,EAA5D,EAAgE,aAAhE,EAA+E,YAA/E,EAA6F,CAA7F,EAAgG,cAAhG,EAAgH,iBAAhH,CAAlmI,EAAsuI,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,gBAA3B,EAA6C,UAA7C,CAAtuI,EAAgyI,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,cAApC,EAAoD,UAApD,EAAgE,EAAhE,EAAoE,aAApE,EAAmF,gBAAnF,EAAqG,CAArG,EAAwG,cAAxG,EAAwH,iBAAxH,CAAhyI,EAA46I,CAAC,KAAD,EAAQ,MAAR,EAAgB,CAAhB,EAAmB,gBAAnB,EAAqC,UAArC,CAA56I,EAA89I,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,QAA/B,EAAyC,SAAzC,EAAoD,CAApD,EAAuD,cAAvD,EAAuE,iBAAvE,EAA0F,CAA1F,EAA6F,QAA7F,CAA99I,EAAskJ,CAAC,CAAD,EAAI,UAAJ,EAAgB,MAAhB,EAAwB,QAAxB,EAAkC,oBAAlC,EAAwD,MAAxD,CAAtkJ,EAAuoJ,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,6BAA5B,EAA2D,CAA3D,EAA8D,SAA9D,EAAyE,OAAzE,EAAkF,CAAlF,EAAqF,MAArF,CAAvoJ,EAAquJ,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,2CAA5B,EAAyE,CAAzE,EAA4E,OAA5E,EAAqF,CAArF,EAAwF,MAAxF,CAAruJ,EAAs0J,CAAC,UAAD,EAAa,EAAb,EAAiB,CAAjB,EAAoB,SAApB,EAA+B,MAA/B,CAAt0J,EAA82J,CAAC,aAAD,EAAgB,EAAhB,CAA92J,EAAm4J,CAAC,MAAD,EAAS,YAAT,EAAuB,CAAvB,EAA0B,KAA1B,CAAn4J,EAAq6J,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,QAA5C,EAAsD,MAAtD,EAA8D,CAA9D,EAAiE,SAAjE,EAA4E,OAA5E,CAAr6J,EAA2/J,CAAC,CAAD,EAAI,KAAJ,EAAW,CAAX,EAAc,SAAd,CAA3/J,EAAqhK,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,QAA5C,EAAsD,oBAAtD,EAA4E,CAA5E,EAA+E,OAA/E,CAArhK,EAA8mK,CAAC,CAAD,EAAI,KAAJ,EAAW,UAAX,EAAuB,MAAvB,CAA9mK,EAA8oK,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,QAA7B,EAAuC,iBAAvC,EAA0D,qBAA1D,EAAiF,CAAjF,EAAoF,OAApF,CAA9oK,EAA4uK,CAAC,CAAD,EAAI,KAAJ,EAAW,SAAX,EAAsB,MAAtB,CAA5uK,EAA2wK,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,MAA5C,CAA3wK,CAP0B;AAOuyKC,EAAAA,QAAQ,EAAE,SAASC,8BAAT,CAAwClV,EAAxC,EAA4CC,GAA5C,EAAiD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AACx6Kb,MAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,aAArB;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,CAAV;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAAC6B,UAAH,CAAc,UAAd,EAA0B,SAASmU,uDAAT,GAAmE;AAAE,eAAOlV,GAAG,CAACiQ,QAAJ,EAAP;AAAwB,OAAvH;AACA/Q,MAAAA,EAAE,CAACsB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,eAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACe,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,CAA1B;AACAf,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,eAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,MAAAA,EAAE,CAAC6B,UAAH,CAAc,QAAd,EAAwB,SAASoU,uDAAT,CAAiE5T,MAAjE,EAAyE;AAAE,eAAOvB,GAAG,CAAC8M,cAAJ,CAAmBvL,MAAnB,CAAP;AAAoC,OAAvI;AACArC,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkBxD,qCAAlB,EAAyD,CAAzD,EAA4D,CAA5D,EAA+D,KAA/D,EAAsE,EAAtE;AACAZ,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkB/C,qCAAlB,EAAyD,CAAzD,EAA4D,CAA5D,EAA+D,KAA/D,EAAsE,EAAtE;AACArB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkB3C,qCAAlB,EAAyD,CAAzD,EAA4D,CAA5D,EAA+D,KAA/D,EAAsE,EAAtE;AACAzB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkB1C,mCAAlB,EAAuD,CAAvD,EAA0D,CAA1D,EAA6D,GAA7D,EAAkE,EAAlE;AACA1B,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkBlC,qCAAlB,EAAyD,CAAzD,EAA4D,CAA5D,EAA+D,KAA/D,EAAsE,EAAtE;AACAlC,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,mBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACe,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAf,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,eAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAAC6B,UAAH,CAAc,QAAd,EAAwB,SAASqU,wDAAT,CAAkE7T,MAAlE,EAA0E;AAAE,eAAOvB,GAAG,CAAC+P,YAAJ,CAAiBxO,MAAjB,CAAP;AAAkC,OAAtI;AACArC,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,oBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,8BAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,6BAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,aAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,yBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,OAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkBV,qCAAlB,EAAyD,CAAzD,EAA4D,CAA5D,EAA+D,KAA/D,EAAsE,EAAtE;AACA1D,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,SAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACe,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAf,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,iBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,sBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkBT,wCAAlB,EAA4D,CAA5D,EAA+D,CAA/D,EAAkE,QAAlE,EAA4E,EAA5E;AACA3D,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,SAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACe,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAf,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkBD,qCAAlB,EAAyD,CAAzD,EAA4D,CAA5D,EAA+D,KAA/D,EAAsE,EAAtE;AACAnE,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,eAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,oBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,gBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,iBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,iBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,mBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,uBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,mBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,OAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,UAAtB,EAAkC,EAAlC;AACAtB,MAAAA,EAAE,CAAC6B,UAAH,CAAc,OAAd,EAAuB,SAASsU,yDAAT,CAAmE9T,MAAnE,EAA2E;AAAE,eAAOvB,GAAG,CAACwL,YAAJ,CAAiBjK,MAAjB,CAAP;AAAkC,OAAtI;AACArC,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,4BAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkBS,qCAAlB,EAAyD,CAAzD,EAA4D,CAA5D,EAA+D,KAA/D,EAAsE,EAAtE;AACA7E,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACe,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAf,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAtB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,IAAtB,EAA4B,EAA5B;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,qBAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkB4B,qCAAlB,EAAyD,EAAzD,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACAhG,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkBsC,wCAAlB,EAA4D,CAA5D,EAA+D,CAA/D,EAAkE,QAAlE,EAA4E,EAA5E;AACA1G,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAtB,MAAAA,EAAE,CAACoE,UAAH,CAAc,EAAd,EAAkB2C,wCAAlB,EAA4D,CAA5D,EAA+D,CAA/D,EAAkE,QAAlE,EAA4E,EAA5E;AACA/G,MAAAA,EAAE,CAACsB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAtB,MAAAA,EAAE,CAACuB,MAAH,CAAU,EAAV,EAAc,QAAd;AACAvB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,QAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAIuV,OAAJ;AACA,UAAIC,OAAJ;AACA,UAAIC,OAAJ;AACA,UAAIC,OAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACAzW,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAAC0W,kBAAH,CAAsB,EAAtB,EAA0B5V,GAAG,CAACkH,KAA9B,EAAqC,WAArC;AACAhI,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,WAAd,EAA2BJ,GAAG,CAAC0D,iBAA/B;AACAxE,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACmH,UAA9B;AACAjI,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACmH,UAA9B;AACAjI,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsBJ,GAAG,CAACK,QAA1B;AACAnB,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsB,CAACkV,OAAO,GAAGtV,GAAG,CAAC0D,iBAAJ,CAAsBC,GAAtB,CAA0B,SAA1B,CAAX,KAAoD,IAApD,GAA2D,IAA3D,GAAkE2R,OAAO,CAAC1R,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgC0R,OAAO,CAAC1R,MAAR,CAAe6J,iBAAvI;AACAvO,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsB,CAACmV,OAAO,GAAGvV,GAAG,CAAC0D,iBAAJ,CAAsBC,GAAtB,CAA0B,SAA1B,CAAX,KAAoD,IAApD,GAA2D,IAA3D,GAAkE4R,OAAO,CAAC3R,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgC2R,OAAO,CAAC3R,MAAR,CAAemL,wBAAvI;AACA7P,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAAC6H,gBAAL,IAAyB7H,GAAG,CAACiN,SAA7B,IAA0C,EAAE,CAACuI,OAAO,GAAGxV,GAAG,CAAC0D,iBAAJ,CAAsBC,GAAtB,CAA0B,SAA1B,CAAX,KAAoD,IAApD,GAA2D,IAA3D,GAAkE6R,OAAO,CAAC5R,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgC4R,OAAO,CAAC5R,MAAR,CAAe6J,iBAAnH,CAAhE;AACAvO,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsBJ,GAAG,CAAC6H,gBAAJ,IAAwB,EAAE,CAAC4N,OAAO,GAAGzV,GAAG,CAAC0D,iBAAJ,CAAsBC,GAAtB,CAA0B,SAA1B,CAAX,KAAoD,IAApD,GAA2D,IAA3D,GAAkE8R,OAAO,CAAC7R,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgC6R,OAAO,CAAC7R,MAAR,CAAe6J,iBAAnH,CAA9C;AACAvO,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACmH,UAA9B;AACAjI,MAAAA,EAAE,CAACwD,SAAH,CAAa,EAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsBJ,GAAG,CAAC0H,kBAA1B;AACAxI,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACmH,UAA9B;AACAjI,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,SAAd,EAAyBJ,GAAG,CAAC6R,UAA7B;AACA3S,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACmH,UAA9B;AACAjI,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsB,CAAC,CAACsV,QAAQ,GAAG1V,GAAG,CAAC0D,iBAAJ,CAAsBC,GAAtB,CAA0B,YAA1B,CAAZ,KAAwD,IAAxD,GAA+D,IAA/D,GAAsE+R,QAAQ,CAACtF,OAAhF,MAA6F,CAACsF,QAAQ,GAAG1V,GAAG,CAAC0D,iBAAJ,CAAsBC,GAAtB,CAA0B,YAA1B,CAAZ,KAAwD,IAAxD,GAA+D,IAA/D,GAAsE+R,QAAQ,CAACG,OAA5K,CAAtB;AACA3W,MAAAA,EAAE,CAACwD,SAAH,CAAa,EAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAACmH,UAA9B;AACAjI,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAAC0W,kBAAH,CAAsB,GAAtB,EAA2B5V,GAAG,CAACyH,cAA/B,EAA+C,OAA/C;AACAvI,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsB,CAAC,CAACuV,QAAQ,GAAG3V,GAAG,CAAC0D,iBAAJ,CAAsBC,GAAtB,CAA0B,UAA1B,CAAZ,KAAsD,IAAtD,GAA6D,IAA7D,GAAoEgS,QAAQ,CAAC/R,MAA9E,MAA0F,CAAC+R,QAAQ,GAAG3V,GAAG,CAAC0D,iBAAJ,CAAsBC,GAAtB,CAA0B,UAA1B,CAAZ,KAAsD,IAAtD,GAA6D,IAA7D,GAAoEgS,QAAQ,CAAC/R,MAAT,IAAmB,IAAnB,GAA0B,IAA1B,GAAiC+R,QAAQ,CAAC/R,MAAT,CAAgBkI,SAA/M,CAAtB;AACA5M,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,SAAd,EAAyBJ,GAAG,CAAC0F,gBAAJ,CAAqB6K,QAA9C;AACArR,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsBJ,GAAG,CAAC0F,gBAAJ,CAAqBC,MAArB,GAA8B,CAApD;AACAzG,MAAAA,EAAE,CAACwD,SAAH,CAAa,CAAb;AACAxD,MAAAA,EAAE,CAACkB,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAACmH,UAA3B;AACH;AAAE,GAlNyD;AAkNvD2O,EAAAA,UAAU,EAAE,CAACrW,EAAE,CAACsW,gBAAJ,EAAsB5W,EAAE,CAAC6W,aAAzB,EAAwC7W,EAAE,CAAC8W,oBAA3C,EAAiE9W,EAAE,CAAC+W,kBAApE,EAAwF/W,EAAE,CAACgX,oBAA3F,EAAiHhX,EAAE,CAACiX,eAApH,EAAqIjX,EAAE,CAACkX,eAAxI,EAAyJlX,EAAE,CAACmX,iBAA5J,EAA+K9W,EAAE,CAAC+W,IAAlL,EAAwLpX,EAAE,CAACqX,0BAA3L,EAAuNrX,EAAE,CAACsX,cAA1N,EAA0OtX,EAAE,CAACuX,uBAA7O,EAAsQhX,EAAE,CAACiX,gBAAzQ,EAA2RnX,EAAE,CAACoX,OAA9R,EAAuSzX,EAAE,CAAC0X,kBAA1S,EAA8T1X,EAAE,CAAC2X,aAAjU,EAAgVxX,EAAE,CAACyX,UAAnV,EAA+VpX,EAAE,CAACqX,qBAAlW,EAAyX7X,EAAE,CAAC8X,aAA5X,EAA2YzX,EAAE,CAAC0X,OAA9Y,CAlN2C;AAkN6WC,EAAAA,MAAM,EAAE,CAAC,qiGAAD;AAlNrX,CAArB,CAA3C", "sourcesContent": ["import { Validators } from '@angular/forms';\r\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"@angular/forms\";\r\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\r\nimport * as i3 from \"ngx-spinner\";\r\nimport * as i4 from \"@angular/router\";\r\nimport * as i5 from \"ngx-toastr\";\r\nimport * as i6 from \"@angular/common\";\r\nimport * as i7 from \"../../../../sidebar.component\";\r\nimport * as i8 from \"ngx-mask\";\r\nimport * as i9 from \"ngx-image-cropper\";\r\nconst _c0 = [\"audioInput\"];\r\nconst _c1 = [\"audioPlayer\"];\r\nfunction AddEmployersComponent_img_19_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"img\", 53);\r\n} if (rf & 2) {\r\n    const ctx_r0 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"src\", ctx_r0.imageSrc, i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction AddEmployersComponent_div_20_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 54);\r\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEmployersComponent_div_21_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 54);\r\n    i0.ɵɵtext(1, \" The image must have a 1:1 aspect ratio. Please select a square image. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEmployersComponent_a_22_Template(rf, ctx) { if (rf & 1) {\r\n    const _r13 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"a\", 55);\r\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_a_22_Template_a_click_0_listener() { i0.ɵɵrestoreView(_r13); const ctx_r12 = i0.ɵɵnextContext(); return ctx_r12.showCropper(\"CO_logo\"); });\r\n    i0.ɵɵtext(1, \"Edit Logo\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEmployersComponent_div_23_Template(rf, ctx) { if (rf & 1) {\r\n    const _r15 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 56);\r\n    i0.ɵɵelementStart(1, \"div\", 57);\r\n    i0.ɵɵelementStart(2, \"h5\", 58);\r\n    i0.ɵɵtext(3, \"Resize Image\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(4, \"image-cropper\", 59);\r\n    i0.ɵɵlistener(\"imageCropped\", function AddEmployersComponent_div_23_Template_image_cropper_imageCropped_4_listener($event) { i0.ɵɵrestoreView(_r15); const ctx_r14 = i0.ɵɵnextContext(); return ctx_r14.cropImg($event); })(\"imageLoaded\", function AddEmployersComponent_div_23_Template_image_cropper_imageLoaded_4_listener() { i0.ɵɵrestoreView(_r15); const ctx_r16 = i0.ɵɵnextContext(); return ctx_r16.imgLoad(); })(\"cropperReady\", function AddEmployersComponent_div_23_Template_image_cropper_cropperReady_4_listener() { i0.ɵɵrestoreView(_r15); const ctx_r17 = i0.ɵɵnextContext(); return ctx_r17.initCropper(); })(\"loadImageFailed\", function AddEmployersComponent_div_23_Template_image_cropper_loadImageFailed_4_listener() { i0.ɵɵrestoreView(_r15); const ctx_r18 = i0.ɵɵnextContext(); return ctx_r18.imgFailed(); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(5, \"button\", 60);\r\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_div_23_Template_button_click_5_listener() { i0.ɵɵrestoreView(_r15); const ctx_r19 = i0.ɵɵnextContext(); return ctx_r19.saveCroppedImage(\"CO_logo\"); });\r\n    i0.ɵɵtext(6, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(7, \"button\", 61);\r\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_div_23_Template_button_click_7_listener() { i0.ɵɵrestoreView(_r15); const ctx_r20 = i0.ɵɵnextContext(); return ctx_r20.hideCropper(\"CO_logo\"); });\r\n    i0.ɵɵtext(8, \"Cancel\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r4 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(4);\r\n    i0.ɵɵproperty(\"imageChangedEvent\", ctx_r4.imgChangeEvt)(\"aspectRatio\", 1 / 1)(\"maintainAspectRatio\", true);\r\n} }\r\nfunction AddEmployersComponent_div_44_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 7);\r\n    i0.ɵɵelementStart(1, \"label\", 62);\r\n    i0.ɵɵtext(2, \"Specify Other Type\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(3, \"input\", 63);\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEmployersComponent_option_55_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"option\", 64);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const sector_r21 = ctx.$implicit;\r\n    i0.ɵɵproperty(\"value\", sector_r21.IN_id);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate(sector_r21.IN_name);\r\n} }\r\nfunction AddEmployersComponent_div_60_div_1_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \"Website is required.\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEmployersComponent_div_60_div_2_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \" Please enter a valid URL starting with http:// or https://. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEmployersComponent_div_60_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 54);\r\n    i0.ɵɵtemplate(1, AddEmployersComponent_div_60_div_1_Template, 2, 0, \"div\", 65);\r\n    i0.ɵɵtemplate(2, AddEmployersComponent_div_60_div_2_Template, 2, 0, \"div\", 65);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r7 = i0.ɵɵnextContext();\r\n    let tmp_0_0;\r\n    let tmp_1_0;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r7.addNewCompanyForm.get(\"CO_website\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r7.addNewCompanyForm.get(\"CO_website\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.pattern);\r\n} }\r\nfunction AddEmployersComponent_div_86_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 54);\r\n    i0.ɵɵtext(1, \" Error: Character limit exceeded! (500 Characters) \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nconst _c2 = function (a0, a1) { return { \"btn-outline-primary\": a0, \"btn-outline-secondary\": a1 }; };\r\nfunction AddEmployersComponent_div_91_button_20_Template(rf, ctx) { if (rf & 1) {\r\n    const _r31 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"button\", 84);\r\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_div_91_button_20_Template_button_click_0_listener() { i0.ɵɵrestoreView(_r31); const i_r25 = i0.ɵɵnextContext().index; const ctx_r29 = i0.ɵɵnextContext(); return ctx_r29.toggleAudio(i_r25); });\r\n    i0.ɵɵelement(1, \"i\", 85);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const i_r25 = i0.ɵɵnextContext().index;\r\n    const ctx_r26 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c2, !ctx_r26.isPlaying[i_r25], ctx_r26.isPlaying[i_r25]));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngClass\", ctx_r26.isPlaying[i_r25] ? \"fa-pause\" : \"fa-play\");\r\n} }\r\nfunction AddEmployersComponent_div_91_button_21_Template(rf, ctx) { if (rf & 1) {\r\n    const _r35 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"button\", 86);\r\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_div_91_button_21_Template_button_click_0_listener() { i0.ɵɵrestoreView(_r35); const i_r25 = i0.ɵɵnextContext().index; const ctx_r33 = i0.ɵɵnextContext(); return ctx_r33.removeInsight(i_r25); });\r\n    i0.ɵɵelement(1, \"i\", 87);\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEmployersComponent_div_91_Template(rf, ctx) { if (rf & 1) {\r\n    const _r37 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 66);\r\n    i0.ɵɵelementStart(1, \"div\", 67);\r\n    i0.ɵɵelementStart(2, \"div\", 68);\r\n    i0.ɵɵelementStart(3, \"div\", 69);\r\n    i0.ɵɵelementStart(4, \"label\", 70);\r\n    i0.ɵɵtext(5, \"Insight Title\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(6, \"input\", 71);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(7, \"div\", 69);\r\n    i0.ɵɵelementStart(8, \"label\", 72);\r\n    i0.ɵɵtext(9, \"Name\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(10, \"input\", 73);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(11, \"div\", 69);\r\n    i0.ɵɵelementStart(12, \"label\", 74);\r\n    i0.ɵɵtext(13, \"Position\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(14, \"input\", 75);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(15, \"div\", 69);\r\n    i0.ɵɵelementStart(16, \"label\", 76);\r\n    i0.ɵɵtext(17, \"Upload Insight\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(18, \"input\", 77);\r\n    i0.ɵɵlistener(\"change\", function AddEmployersComponent_div_91_Template_input_change_18_listener($event) { const restoredCtx = i0.ɵɵrestoreView(_r37); const i_r25 = restoredCtx.index; const ctx_r36 = i0.ɵɵnextContext(); return ctx_r36.onAudioSelected($event, i_r25); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(19, \"div\", 78);\r\n    i0.ɵɵtemplate(20, AddEmployersComponent_div_91_button_20_Template, 2, 5, \"button\", 79);\r\n    i0.ɵɵtemplate(21, AddEmployersComponent_div_91_button_21_Template, 2, 0, \"button\", 80);\r\n    i0.ɵɵelementStart(22, \"audio\", 81, 82);\r\n    i0.ɵɵelement(24, \"source\", 83);\r\n    i0.ɵɵtext(25, \" Your browser does not support the audio element. \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const i_r25 = ctx.index;\r\n    const ctx_r9 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"formGroupName\", i_r25);\r\n    i0.ɵɵadvance(20);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.audioUrls[i_r25]);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.insightFormArray.length > 1);\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"src\", ctx_r9.audioUrls[i_r25], i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction AddEmployersComponent_button_92_Template(rf, ctx) { if (rf & 1) {\r\n    const _r39 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"button\", 88);\r\n    i0.ɵɵlistener(\"click\", function AddEmployersComponent_button_92_Template_button_click_0_listener() { i0.ɵɵrestoreView(_r39); const ctx_r38 = i0.ɵɵnextContext(); return ctx_r38.addInsight(); });\r\n    i0.ɵɵtext(1, \"Add Insight \");\r\n    i0.ɵɵelement(2, \"i\", 89);\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEmployersComponent_button_94_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"button\", 90);\r\n    i0.ɵɵtext(1, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\n// function wordLimitValidator(maxWords: number) {\r\n//   return (control: AbstractControl): { [key: string]: any } | null => {\r\n//     if (control.value) {\r\n//       const words = control.value.trim().split(/\\s+/);\r\n//       if (words.length > maxWords) {\r\n//         console.log('maxwords', maxWords);\r\n//         return { wordLimitExceeded: true, wordCount: words.length };\r\n//       }\r\n//     }\r\n//     return null;\r\n//   };\r\n// }\r\n// function nonNegativeValidator(\r\n//   control: FormControl\r\n// ): { [key: string]: boolean } | null {\r\n//   console.log('Control : ', control);\r\n//   const value = control.value;\r\n//   if (value < 0 || value > 24) {\r\n//     return { negativeValue: true };\r\n//   }\r\n//   return null;\r\n// }\r\nexport class AddEmployersComponent {\r\n    constructor(formBuilder, dataTransferService, ngxSpinnerService, router, toastr, datePipe, route) {\r\n        var _a, _b;\r\n        this.formBuilder = formBuilder;\r\n        this.dataTransferService = dataTransferService;\r\n        this.ngxSpinnerService = ngxSpinnerService;\r\n        this.router = router;\r\n        this.toastr = toastr;\r\n        this.datePipe = datePipe;\r\n        this.route = route;\r\n        this.p = 1;\r\n        this.hideHeader = false;\r\n        this.viewInsight = true;\r\n        this.showForm = false;\r\n        this.submitted = false;\r\n        this.title = 'Add New';\r\n        this.isReadonly = false;\r\n        this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\r\n        this.imagedp = null; // Assuming this is used to display the uploaded image\r\n        this.invalidDates = new Set();\r\n        this.audioFiles = [];\r\n        this.characterCount = 0;\r\n        this.isPlaying = []; // Array to track playback state\r\n        this.audioUrls = []; // Array to store audio URLs\r\n        this.showOtherTypeInput = false;\r\n        this.existingAudioNames = [];\r\n        this.audioFileUrls = [];\r\n        this.isCropperVisible = false;\r\n        this.imgChangeEvt = \"\";\r\n        this.addNewCompanyForm = this.formBuilder.group({\r\n            CO_companyName: ['', [Validators.required]],\r\n            CO_logo: [null, [Validators.required]],\r\n            CO_location: ['', [Validators.required]],\r\n            CO_type: ['', [Validators.required]],\r\n            CO_otherType: [''],\r\n            CO_founded: ['', [Validators.required]],\r\n            CO_sectorId: ['', [Validators.required]],\r\n            CO_about: ['', [Validators.required, Validators.maxLength(200)]],\r\n            CO_website: ['', [Validators.required, Validators.pattern('https?://.+')]],\r\n            CO_size: ['', [Validators.required]],\r\n            CO_HrInsights: this.formBuilder.array([this.createInsight()])\r\n        });\r\n        this.route.queryParams.subscribe(params => {\r\n            if (params) {\r\n                this.CO_id = params['CO_id'];\r\n                console.log(this.CO_id);\r\n            }\r\n        });\r\n        const state = (_b = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras) === null || _b === void 0 ? void 0 : _b.state; //State values\r\n        if (state) {\r\n            (this.title = state.title);\r\n            (this.employerData = state.employerData);\r\n            console.log(\"employerData: \", this.employerData);\r\n        }\r\n        else {\r\n            this.router.navigate([`actions/employer-opportunities`]);\r\n        }\r\n    }\r\n    ngOnInit() {\r\n        this.getSectorTitles();\r\n        if (this.title === 'Edit') {\r\n            this.getCompanyById(this.CO_id);\r\n        }\r\n    }\r\n    async getCompanyById(companyId) {\r\n        try {\r\n            const res = await this.dataTransferService.getCompanyById(companyId).toPromise();\r\n            this.employerData = res;\r\n            console.log(\"this.employerData\", res);\r\n            this.populateForm(this.employerData);\r\n        }\r\n        catch (error) {\r\n            console.log(\"Error\", error);\r\n            this.toastr.error(\"Unable to fetch data\");\r\n        }\r\n    }\r\n    populateForm(data) {\r\n        var _a, _b, _c;\r\n        console.log(\"Data to patch to edit\", data);\r\n        const logoControl = this.addNewCompanyForm.get('CO_logo');\r\n        if (logoControl) {\r\n            logoControl.clearValidators();\r\n            logoControl.updateValueAndValidity();\r\n        }\r\n        else {\r\n            console.log(\"CO_logo control not found\");\r\n        }\r\n        // this.ExistingLogo = data?.CO_logo.substring(\r\n        //   data.CO_logo.lastIndexOf('/') + 1\r\n        // );\r\n        this.addNewCompanyForm.patchValue({\r\n            CO_companyName: data.CO_companyName,\r\n            CO_location: data.CO_location,\r\n            CO_type: data.CO_type,\r\n            CO_founded: data.CO_founded,\r\n            CO_sectorId: data.CO_sectorId,\r\n            CO_about: data.CO_about,\r\n            CO_website: data.CO_website,\r\n            CO_size: data.CO_size,\r\n            CO_id: data.CO_id\r\n        });\r\n        // Define the valid options\r\n        const validOptions = [\r\n            'Private Limited Company(LTD)',\r\n            'Public Limited Company(PLC)',\r\n            'Partnership',\r\n            'Non-Profit Organization'\r\n        ];\r\n        if (validOptions.includes(data.CO_type)) {\r\n            (_a = this.addNewCompanyForm.get('CO_type')) === null || _a === void 0 ? void 0 : _a.setValue(data.CO_type);\r\n            this.showOtherTypeInput = false;\r\n        }\r\n        else {\r\n            (_b = this.addNewCompanyForm.get('CO_type')) === null || _b === void 0 ? void 0 : _b.setValue('Other');\r\n            this.showOtherTypeInput = true; // Show the 'Other' input\r\n            (_c = this.addNewCompanyForm.get('CO_otherType')) === null || _c === void 0 ? void 0 : _c.setValue(data.CO_type);\r\n        }\r\n        this.imageSrc = data.CO_logo;\r\n        if (data.CO_HrInsights) {\r\n            const hrInsightsArray = this.addNewCompanyForm.get('CO_HrInsights');\r\n            hrInsightsArray.clear();\r\n            data.CO_HrInsights.forEach((insight) => {\r\n                const insightFormGroup = this.formBuilder.group({\r\n                    HRI_title: [insight.HRI_title, [Validators.required]],\r\n                    HRI_name: [insight.HRI_name, [Validators.required]],\r\n                    HRI_position: [insight.HRI_position, [Validators.required]],\r\n                    HRI_link: [insight.HRI_link]\r\n                });\r\n                hrInsightsArray.push(insightFormGroup);\r\n            });\r\n            // Initialize audio URLs and playback states\r\n            // this.audioUrls = data.CO_HrInsights.map((insight: any) => insight.HRI_link);\r\n            // this.isPlaying = new Array(data.CO_HrInsights.length).fill(false);\r\n            this.audioUrls = data.CO_HrInsights.map((insight) => insight.HRI_link);\r\n            this.isPlaying = new Array(data.CO_HrInsights.length).fill(false);\r\n            this.existingAudioNames = data === null || data === void 0 ? void 0 : data.CO_HrInsights.map((insight) => { var _a; return (_a = insight === null || insight === void 0 ? void 0 : insight.HRI_link) === null || _a === void 0 ? void 0 : _a.substring(insight.HRI_link.lastIndexOf('/') + 1); });\r\n        }\r\n    }\r\n    get insightFormArray() {\r\n        return this.addNewCompanyForm.get('CO_HrInsights');\r\n    }\r\n    onTextChange(event) {\r\n        var _a, _b;\r\n        const textarea = event.target;\r\n        this.characterCount = textarea.value.length;\r\n        if (this.characterCount > 500) {\r\n            (_a = this.addNewCompanyForm.get('CO_about')) === null || _a === void 0 ? void 0 : _a.setErrors({ maxlength: true });\r\n        }\r\n        else {\r\n            (_b = this.addNewCompanyForm.get('CO_about')) === null || _b === void 0 ? void 0 : _b.setErrors(null);\r\n        }\r\n    }\r\n    createInsight() {\r\n        return this.formBuilder.group({\r\n            HRI_title: ['', [Validators.required]],\r\n            HRI_name: ['', [Validators.required]],\r\n            HRI_position: ['', [Validators.required]],\r\n            HRI_link: [null]\r\n        });\r\n        this.isPlaying.push(false); // Initialize playback state for new row\r\n        this.audioUrls.push(''); // Initialize empty URL for new row\r\n    }\r\n    addInsight() {\r\n        this.insightFormArray.push(this.createInsight());\r\n    }\r\n    removeInsight(index) {\r\n        this.insightFormArray.removeAt(index);\r\n        this.audioUrls.splice(index, 1); // Remove URL for deleted row\r\n        this.isPlaying.splice(index, 1); // Remove playback state for deleted row\r\n        this.audioFiles.splice(index, 1); // Remove file object for deleted row\r\n    }\r\n    toggleAudio(index) {\r\n        const audioElements = this.audioPlayers.toArray();\r\n        const audioElement = audioElements[index].nativeElement;\r\n        // Stop all other audio\r\n        this.stopAllAudio(index);\r\n        // Toggle play/pause for the current audio element\r\n        if (this.isPlaying[index]) {\r\n            audioElement.pause();\r\n        }\r\n        else {\r\n            audioElement.src = this.audioUrls[index];\r\n            audioElement.play();\r\n        }\r\n        // Update the playback state\r\n        this.isPlaying[index] = !this.isPlaying[index];\r\n    }\r\n    stopAllAudio(currentIndex) {\r\n        this.audioPlayers.forEach((audioPlayer, index) => {\r\n            if (index !== currentIndex) {\r\n                audioPlayer.nativeElement.pause();\r\n                this.isPlaying[index] = false;\r\n            }\r\n        });\r\n    }\r\n    stopAudio(index) {\r\n        const audioElements = this.audioPlayers.toArray();\r\n        const audioElement = audioElements[index].nativeElement;\r\n        audioElement.pause();\r\n        audioElement.currentTime = 0; // Reset to the beginning\r\n        this.isPlaying[index] = false;\r\n    }\r\n    onFileSelected(event) {\r\n        var _a;\r\n        let selectedFile = event.target.files[0];\r\n        //to preview image and take file in imageName to pass in upload api\r\n        if (event.target.files.length === 0) {\r\n            // Reset both imageName and imageSrc when no file is selected\r\n            this.imageName = null;\r\n            this.imageSrc = null;\r\n            return;\r\n        }\r\n        const newFileName = FileValidator.addTimestamp(selectedFile.name);\r\n        this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });\r\n        if (this.imageName) {\r\n            const formControl = this.addNewCompanyForm.get('CO_logo');\r\n            formControl === null || formControl === void 0 ? void 0 : formControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\r\n            formControl === null || formControl === void 0 ? void 0 : formControl.updateValueAndValidity();\r\n        }\r\n        const fileType = this.imageName.type.split('/')[0];\r\n        const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\r\n        if (fileType !== 'image' || fileExtension === 'svg') {\r\n            event.target.value = '';\r\n            this.toastr.info('Please select an image file (excluding SVG).');\r\n            this.imageName = null;\r\n            this.imageSrc = null;\r\n            return;\r\n        }\r\n        if (this.imageName && fileType == 'image') {\r\n            const reader = new FileReader();\r\n            const img = new Image();\r\n            reader.onload = (e) => {\r\n                var _a, _b, _c;\r\n                this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\r\n                if (!((_c = (_b = this.addNewCompanyForm.get('CO_logo')) === null || _b === void 0 ? void 0 : _b.errors) === null || _c === void 0 ? void 0 : _c.fileSizeValidator)) {\r\n                    this.checkAspectRatio(img, 'CO_logo');\r\n                    this.imgChangeEvt = { target: { files: [this.imageName] } };\r\n                }\r\n            };\r\n            ;\r\n            reader.readAsDataURL(this.imageName);\r\n        }\r\n        else {\r\n            this.imageSrc = null; // Reset imageSrc if no file selected\r\n        }\r\n        console.log('imageName', this.imageName);\r\n    }\r\n    checkAspectRatio(image, controlName) {\r\n        const aspectRatio = image.width / image.height;\r\n        const control = this.addNewCompanyForm.get(controlName);\r\n        if (aspectRatio !== 1) {\r\n            // this.toastr.warning('The image must have a 1:1 aspect ratio. Please select a square image.', 'Invalid Aspect Ratio');\r\n            control === null || control === void 0 ? void 0 : control.setErrors({ fileAspectRatioValidator: true });\r\n        }\r\n        else {\r\n            control === null || control === void 0 ? void 0 : control.setErrors(null);\r\n        }\r\n    }\r\n    uploadLogoUrl() {\r\n        return new Promise((resolve, reject) => {\r\n            if (!this.imageName) {\r\n                reject('Please select an image.');\r\n                return;\r\n            }\r\n            console.log('Uploading image:', this.imageName);\r\n            this.dataTransferService.uploadurl(this.imageName).subscribe((res) => {\r\n                const fileUrl = this.baseUrl + this.imageName.name; // Ensure you're concatenating correctly\r\n                resolve(fileUrl);\r\n            }, (error) => {\r\n                reject('Error uploading image: ' + error.message || error);\r\n            });\r\n        });\r\n    }\r\n    onAudioSelected(event, index) {\r\n        let audiofile;\r\n        const selectedFile = event.target.files[0];\r\n        if (selectedFile) {\r\n            const newFileName = FileValidator.addTimestamp(selectedFile.name);\r\n            audiofile = new File([selectedFile], newFileName, { type: selectedFile.type });\r\n        }\r\n        else {\r\n            audiofile = null;\r\n        }\r\n        if (!audiofile) {\r\n            this.audioUrls[index] = '';\r\n            this.stopAudio(index);\r\n            return;\r\n        }\r\n        const audiofileType = audiofile.type.split('/')[0];\r\n        if (audiofileType !== 'audio') {\r\n            event.target.value = ''; // Clear the input\r\n            this.toastr.info('Please select an audio file.');\r\n            return;\r\n        }\r\n        // Store the file object in the array for later upload\r\n        this.audioFiles[index] = audiofile;\r\n        console.log('Audio file :', audiofile);\r\n        const reader = new FileReader();\r\n        reader.onload = () => {\r\n            // Store audio URL\r\n            this.audioUrls[index] = reader.result;\r\n        };\r\n        reader.onerror = () => {\r\n            console.error('Error reading audio file');\r\n            this.toastr.error('Error reading audio file.');\r\n        };\r\n        reader.readAsDataURL(audiofile);\r\n    }\r\n    uploadAudioFiles(files) {\r\n        const uploadPromises = files.map((file, index) => this.uploadSingleAudioFile(file, index));\r\n        return Promise.all(uploadPromises);\r\n    }\r\n    // Updated uploadSingleAudioFile function\r\n    uploadSingleAudioFile(file, index) {\r\n        return new Promise((resolve, reject) => {\r\n            this.dataTransferService.uploadurl(file).subscribe((res) => {\r\n                console.log('Upload successful', file.name);\r\n                const fileUrl = this.baseUrl + file.name;\r\n                this.audioUrls[index] = fileUrl;\r\n                resolve(fileUrl);\r\n            }, (error) => {\r\n                console.error('Upload error', error);\r\n                this.toastr.error('Failed to upload audio file');\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n    // uploadAudioFiles(files: File[]): Promise<string[]> {\r\n    //   const uploadPromises = files.map(file => this.uploadSingleAudioFile(file));\r\n    //   return Promise.all(uploadPromises);\r\n    // }\r\n    // uploadSingleAudioFile(file: File): Promise<string> {\r\n    //   return new Promise((resolve, reject) => {\r\n    //     this.dataTransferService.uploadurl(file).subscribe(\r\n    //       (res: any) => {\r\n    //         console.log('Upload successful', file.name);\r\n    //         const fileUrl = this.baseUrl + file.name;\r\n    //         resolve(fileUrl);\r\n    //       },\r\n    //       (error: any) => {\r\n    //         console.error('Upload error', error);\r\n    //         this.toastr.error('Failed to upload audio file');\r\n    //         reject(error);\r\n    //       }\r\n    //     );\r\n    //   });\r\n    // }\r\n    onTypeChange(event) {\r\n        const selectElement = event.target;\r\n        this.showOtherTypeInput = selectElement.value === 'Other';\r\n    }\r\n    onSubmit() {\r\n        if (this.title === 'Edit') {\r\n            this.updateEmployer();\r\n        }\r\n        else {\r\n            this.addEmployer();\r\n        }\r\n    }\r\n    updateEmployer() {\r\n        var _a, _b, _c;\r\n        if (!((_b = (_a = this.addNewCompanyForm.get('CO_logo')) === null || _a === void 0 ? void 0 : _a.errors) === null || _b === void 0 ? void 0 : _b.fileAspectRatioValidator)) {\r\n            const logoControl = this.addNewCompanyForm.get('CO_logo');\r\n            if (logoControl) {\r\n                logoControl.clearValidators();\r\n                logoControl.updateValueAndValidity();\r\n                console.log(\"Removed required validator from CO_logo\");\r\n            }\r\n            else {\r\n                console.log(\"CO_logo control not found\");\r\n            }\r\n            if (this.addNewCompanyForm.invalid) {\r\n                Object.keys(this.addNewCompanyForm.controls).forEach(name => {\r\n                    const control = this.addNewCompanyForm.get(name);\r\n                    if (control === null || control === void 0 ? void 0 : control.invalid) {\r\n                        console.log(`Invalid control: ${name}, Errors:`, control.errors);\r\n                    }\r\n                });\r\n                this.toastr.info('Please fill all required fields correctly');\r\n                return;\r\n            }\r\n            const uploadLogo = ((_c = this.addNewCompanyForm.get('CO_logo')) === null || _c === void 0 ? void 0 : _c.value) ? this.uploadLogoUrl() : Promise.resolve(this.employerData.CO_logo);\r\n            this.ngxSpinnerService.show('globalSpinner');\r\n            uploadLogo.then((fileUrl) => {\r\n                return this.uploadAudioFiles(this.audioFiles).then((audioUrls) => {\r\n                    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\r\n                    console.log(\"Uploaded audio files:\", this.audioFiles);\r\n                    this.insightFormArray.controls.forEach((control, index) => {\r\n                        var _a;\r\n                        (_a = control.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(this.audioUrls[index]);\r\n                    });\r\n                    this.dataToPost = {\r\n                        CO_id: this.employerData.CO_id,\r\n                        CO_companyName: (_a = this.addNewCompanyForm.get('CO_companyName')) === null || _a === void 0 ? void 0 : _a.value,\r\n                        CO_logo: fileUrl,\r\n                        CO_location: (_b = this.addNewCompanyForm.get('CO_location')) === null || _b === void 0 ? void 0 : _b.value,\r\n                        CO_type: ((_c = this.addNewCompanyForm.get('CO_type')) === null || _c === void 0 ? void 0 : _c.value) === 'Other' ? (_d = this.addNewCompanyForm.get('CO_otherType')) === null || _d === void 0 ? void 0 : _d.value : (_e = this.addNewCompanyForm.get('CO_type')) === null || _e === void 0 ? void 0 : _e.value,\r\n                        CO_founded: (_f = this.addNewCompanyForm.get('CO_founded')) === null || _f === void 0 ? void 0 : _f.value,\r\n                        CO_sectorId: (_g = this.addNewCompanyForm.get('CO_sectorId')) === null || _g === void 0 ? void 0 : _g.value,\r\n                        CO_about: (_h = this.addNewCompanyForm.get('CO_about')) === null || _h === void 0 ? void 0 : _h.value,\r\n                        CO_website: (_j = this.addNewCompanyForm.get('CO_website')) === null || _j === void 0 ? void 0 : _j.value,\r\n                        CO_size: (_k = this.addNewCompanyForm.get('CO_size')) === null || _k === void 0 ? void 0 : _k.value,\r\n                        CO_HrInsights: (_l = this.addNewCompanyForm.get('CO_HrInsights')) === null || _l === void 0 ? void 0 : _l.value\r\n                    };\r\n                    console.log(\"Data to update...\", this.dataToPost);\r\n                    return this.dataTransferService.updateEmployer(this.dataToPost).toPromise();\r\n                });\r\n            }).then((res) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                if (res.statusCode === 200) {\r\n                    this.toastr.success('Employer updated successfully.');\r\n                    this.getAllCompany();\r\n                    this.router.navigate(['actions/employer-opportunities']);\r\n                }\r\n                else {\r\n                    this.toastr.error('Something went wrong.');\r\n                }\r\n            }).catch((error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                const errorMessage = error === 'Audio file upload failed.' ? error : 'Error updating employer.';\r\n                this.toastr.error(errorMessage);\r\n            });\r\n        }\r\n        else {\r\n            this.toastr.info(\"Please resize the image\");\r\n        }\r\n    }\r\n    addEmployer() {\r\n        console.log('Formdata', this.addNewCompanyForm.value);\r\n        if (this.addNewCompanyForm.invalid) {\r\n            this.toastr.info('Please fill all required fields');\r\n            return;\r\n        }\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        this.uploadLogoUrl().then(() => {\r\n            const fileUrl = this.baseUrl + this.imageName.name; // Construct the file URL as needed\r\n            // Upload all audio files and get their URLs\r\n            this.uploadAudioFiles(this.audioFiles).then((audioUrls) => {\r\n                var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;\r\n                console.log(\"this.audioFiles\", this.audioFiles);\r\n                this.insightFormArray.controls.forEach((control, index) => {\r\n                    var _a;\r\n                    const group = control;\r\n                    (_a = group.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(audioUrls[index]); // Patch the URL from the array\r\n                });\r\n                // Manually construct the data object\r\n                const data = {\r\n                    CO_companyName: (_a = this.addNewCompanyForm.get('CO_companyName')) === null || _a === void 0 ? void 0 : _a.value,\r\n                    CO_logo: fileUrl,\r\n                    CO_location: (_b = this.addNewCompanyForm.get('CO_location')) === null || _b === void 0 ? void 0 : _b.value,\r\n                    CO_type: ((_c = this.addNewCompanyForm.get('CO_type')) === null || _c === void 0 ? void 0 : _c.value) === 'Other' ? (_d = this.addNewCompanyForm.get('CO_otherType')) === null || _d === void 0 ? void 0 : _d.value : (_e = this.addNewCompanyForm.get('CO_type')) === null || _e === void 0 ? void 0 : _e.value,\r\n                    CO_founded: (_f = this.addNewCompanyForm.get('CO_founded')) === null || _f === void 0 ? void 0 : _f.value,\r\n                    CO_sectorId: (_g = this.addNewCompanyForm.get('CO_sectorId')) === null || _g === void 0 ? void 0 : _g.value,\r\n                    CO_about: (_h = this.addNewCompanyForm.get('CO_about')) === null || _h === void 0 ? void 0 : _h.value,\r\n                    CO_website: (_j = this.addNewCompanyForm.get('CO_website')) === null || _j === void 0 ? void 0 : _j.value,\r\n                    CO_size: (_k = this.addNewCompanyForm.get('CO_size')) === null || _k === void 0 ? void 0 : _k.value,\r\n                    CO_HrInsights: (_l = this.addNewCompanyForm.get('CO_HrInsights')) === null || _l === void 0 ? void 0 : _l.value\r\n                };\r\n                console.log('ADD Employer DATA', data);\r\n                this.dataTransferService.addEmployer(data).subscribe((res) => {\r\n                    if (res.statusCode == 201) {\r\n                        this.ngxSpinnerService.hide('globalSpinner');\r\n                        console.log('Data posted successfully:', res);\r\n                        this.toastr.success('Employer added successfully.');\r\n                        this.getAllCompany();\r\n                        this.router.navigate(['actions/employer-opportunities']);\r\n                        console.log(\"Success\");\r\n                    }\r\n                    else {\r\n                        this.ngxSpinnerService.hide('globalSpinner');\r\n                        this.toastr.error('Something Went Wrong');\r\n                    }\r\n                }, (error) => {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    console.error('Error posting data:', error);\r\n                    this.toastr.error('Something Went Wrong');\r\n                });\r\n            }).catch((error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                console.error('Error uploading audio files:', error);\r\n                this.toastr.error('Audio file upload failed');\r\n            });\r\n        }).catch((error) => {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            console.error('Error uploading logo:', error);\r\n            this.toastr.error('Logo upload failed');\r\n        });\r\n    }\r\n    getAllCompany() {\r\n        this.dataTransferService.getAllCompany().subscribe({\r\n            next: (res) => {\r\n                if (res.statusCode === 200) {\r\n                    this.CompanyList = res.data;\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    console.log('GetAllCompany', this.CompanyList);\r\n                }\r\n                else {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    console.error('Failed to fetch companies. Status:', res.status);\r\n                }\r\n            },\r\n            error: (error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                console.error('Error occurred while fetching companies:', error);\r\n            },\r\n        });\r\n    }\r\n    getSectorTitles() {\r\n        // In Add company form - To sector dropdown\r\n        this.dataTransferService.getSectorTitles().subscribe((res) => {\r\n            if ((res.statusCode = 200)) {\r\n                this.SectorList = res.data;\r\n                console.log('Sectors', this.SectorList);\r\n            }\r\n            else {\r\n                console.error('Failed to fetch sectors. Status:', res.status);\r\n            }\r\n        }, (error) => {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            console.log(\"error\", error);\r\n        });\r\n    }\r\n    showCropper(controlName) {\r\n        this.isCropperVisible = true;\r\n    }\r\n    hideCropper(controlName) {\r\n        this.isCropperVisible = false;\r\n    }\r\n    cropImg(e) {\r\n        this.imageSrc = e.base64;\r\n    }\r\n    initCropper() {\r\n    }\r\n    imgLoad() {\r\n    }\r\n    imgFailed() {\r\n        this.toastr.error(\"Image Failed to show\");\r\n    }\r\n    saveCroppedImage(controlName) {\r\n        const addTimestamp = (fileName) => {\r\n            const currentTimestamp = new Date().getTime();\r\n            const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');\r\n            const extension = fileName.split('.').pop();\r\n            const cleanedName = nameWithoutExtension.replace(/_\\d{13}$/, '');\r\n            return `${cleanedName}_${currentTimestamp}.${extension}`;\r\n        };\r\n        if (this.imageSrc && this.imageName) {\r\n            // Convert cropped image to Blob\r\n            const blob = this.dataURItoBlob(this.imageSrc);\r\n            // Add or replace timestamp in image name\r\n            const newFileName = addTimestamp(this.imageName.name);\r\n            this.imageName = new File([blob], newFileName, { type: this.imageName.type });\r\n            console.log(\"Cropped Image\", this.imageName);\r\n            // Update imgChangeEvt with the new cropped image\r\n            this.imgChangeEvt = { target: { files: [this.imageName] } };\r\n            const fileControl = this.addNewCompanyForm.get('CO_logo');\r\n            fileControl === null || fileControl === void 0 ? void 0 : fileControl.clearValidators();\r\n            fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\r\n            this.hideCropper('CO_logo');\r\n        }\r\n    }\r\n    dataURItoBlob(dataURI) {\r\n        const byteString = atob(dataURI.split(',')[1]);\r\n        const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];\r\n        const arrayBuffer = new ArrayBuffer(byteString.length);\r\n        const uint8Array = new Uint8Array(arrayBuffer);\r\n        for (let i = 0; i < byteString.length; i++) {\r\n            uint8Array[i] = byteString.charCodeAt(i);\r\n        }\r\n        return new Blob([uint8Array], { type: mimeString });\r\n    }\r\n}\r\nAddEmployersComponent.ɵfac = function AddEmployersComponent_Factory(t) { return new (t || AddEmployersComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.NgxSpinnerService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.DatePipe), i0.ɵɵdirectiveInject(i4.ActivatedRoute)); };\r\nAddEmployersComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: AddEmployersComponent, selectors: [[\"app-add-employers\"]], viewQuery: function AddEmployersComponent_Query(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵviewQuery(_c0, 5);\r\n        i0.ɵɵviewQuery(_c1, 5);\r\n    } if (rf & 2) {\r\n        let _t;\r\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioInput = _t.first);\r\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioPlayers = _t);\r\n    } }, decls: 97, vars: 21, consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [\"enctype\", \"multipart/form-data\", 1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\", \"col-lg-4\"], [\"for\", \"CO_companyName\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"CO_companyName\", \"required\", \"\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"CO_logo\", 1, \"required-field\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"CO_logo\", \"required\", \"\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Employer Logo\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"class\", \"btn-custom-small\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"cropper-container  col-lg-12\", 4, \"ngIf\"], [\"for\", \"CO_location\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"CO_location\", \"required\", \"\", \"placeholder\", \"e.g. London, UK\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"CO_type\", 1, \"required-field\"], [\"id\", \"CO_type\", \"formControlName\", \"CO_type\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"value\", \"\", \"disabled\", \"\", \"selected\", \"\"], [\"value\", \"Private Limited Company(LTD)\"], [\"value\", \"Public Limited Company(PLC)\"], [\"value\", \"Partnership\"], [\"value\", \"Non-Profit Organization\"], [\"value\", \"Other\"], [\"class\", \"form-group col-lg-4\", 4, \"ngIf\"], [\"for\", \"CO_founded\", 1, \"required-field\"], [\"type\", \"text\", \"mask\", \"0000\", \"formControlName\", \"CO_founded\", \"required\", \"\", \"placeholder\", \"Enter Year Only\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"CO_sectorId\", 1, \"required-field\"], [\"id\", \"companySector\", \"formControlName\", \"CO_sectorId\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"CO_website\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"CO_website\", \"required\", \"\", \"placeholder\", \"Enter URL\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"CO_size\", 1, \"required-field\"], [\"id\", \"CO_size\", \"formControlName\", \"CO_size\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"value\", \"1-9\"], [\"value\", \"10-49\"], [\"value\", \"50-499\"], [\"value\", \"500-999\"], [\"value\", \"1,000-4,999\"], [\"value\", \"5,000+\"], [\"for\", \"CO_about\", 1, \"required-field\"], [\"id\", \"CO_about\", \"formControlName\", \"CO_about\", \"required\", \"\", \"rows\", \"3\", \"cols\", \"50\", \"placeholder\", \"Enter About\", \"maxlength\", \"200\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"input\"], [1, \"character-count\"], [\"formArrayName\", \"CO_HrInsights\"], [1, \"required-field\", \"mb-3\", \"py-2\"], [\"class\", \"row mb-3\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"class\", \"btn btn-sm add-insight-btn btn-outline-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [\"type\", \"button\", \"routerLink\", \"/actions/employer-opportunities\", 1, \"btn\", \"btn-light\"], [\"alt\", \"Employer Logo\", 1, \"img-preview\", 3, \"src\"], [1, \"warning\"], [1, \"btn-custom-small\", 3, \"click\"], [1, \"cropper-container\", \"col-lg-12\"], [1, \"image-cropper\", \"mb-2\", \"text-center\"], [1, \"py-2\"], [\"format\", \"png\", 1, \"custom-image-cropper\", \"my-2\", 3, \"imageChangedEvent\", \"aspectRatio\", \"maintainAspectRatio\", \"imageCropped\", \"imageLoaded\", \"cropperReady\", \"loadImageFailed\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"m-2\", 3, \"click\"], [\"for\", \"CO_otherType\", 1, \"required-field\"], [\"type\", \"text\", \"formControlName\", \"CO_otherType\", \"placeholder\", \"Enter Type\", 1, \"form-control\", \"form-control-sm\"], [3, \"value\"], [4, \"ngIf\"], [1, \"row\", \"mb-3\", 3, \"formGroupName\"], [1, \"col-lg-11\"], [1, \"row\", \"mr-0\"], [1, \"form-group\", \"col-lg-3\"], [\"for\", \"HRI_title\", 1, \"required-field\", \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_title\", \"required\", \"\", \"placeholder\", \"Enter Title\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"HRI_name\", 1, \"required-field\", \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_name\", \"required\", \"\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"HRI_position\", 1, \"required-field\", \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_position\", \"required\", \"\", \"placeholder\", \"Enter Position\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"link\", 1, \"required-field\", \"subtitle\"], [\"type\", \"file\", \"id\", \"link\", \"accept\", \"audio/*\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [1, \"col-lg-1\", \"px-0\", \"d-flex\", \"align-items-center\", \"btns\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm mr-2\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [\"controls\", \"\", 2, \"display\", \"none\"], [\"audioPlayer\", \"\"], [\"type\", \"audio/mpeg\", 3, \"src\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"mr-2\", 3, \"ngClass\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-minus\", \"icon\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"add-insight-btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"icon\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]], template: function AddEmployersComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵelementStart(0, \"app-sidebar\");\r\n        i0.ɵɵelementStart(1, \"div\", 0);\r\n        i0.ɵɵelementStart(2, \"div\", 1);\r\n        i0.ɵɵelementStart(3, \"div\", 2);\r\n        i0.ɵɵelementStart(4, \"div\", 3);\r\n        i0.ɵɵelementStart(5, \"div\", 4);\r\n        i0.ɵɵtext(6);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(7, \"div\", 5);\r\n        i0.ɵɵelementStart(8, \"form\", 6);\r\n        i0.ɵɵlistener(\"ngSubmit\", function AddEmployersComponent_Template_form_ngSubmit_8_listener() { return ctx.onSubmit(); });\r\n        i0.ɵɵelementStart(9, \"div\", 1);\r\n        i0.ɵɵelementStart(10, \"div\", 7);\r\n        i0.ɵɵelementStart(11, \"label\", 8);\r\n        i0.ɵɵtext(12, \"Employer Name\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(13, \"input\", 9);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(14, \"div\", 7);\r\n        i0.ɵɵelementStart(15, \"label\", 10);\r\n        i0.ɵɵtext(16, \"Employer Logo\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(17, \"div\", 11);\r\n        i0.ɵɵelementStart(18, \"input\", 12);\r\n        i0.ɵɵlistener(\"change\", function AddEmployersComponent_Template_input_change_18_listener($event) { return ctx.onFileSelected($event); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(19, AddEmployersComponent_img_19_Template, 1, 1, \"img\", 13);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(20, AddEmployersComponent_div_20_Template, 2, 0, \"div\", 14);\r\n        i0.ɵɵtemplate(21, AddEmployersComponent_div_21_Template, 2, 0, \"div\", 14);\r\n        i0.ɵɵtemplate(22, AddEmployersComponent_a_22_Template, 2, 0, \"a\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(23, AddEmployersComponent_div_23_Template, 9, 3, \"div\", 16);\r\n        i0.ɵɵelementStart(24, \"div\", 7);\r\n        i0.ɵɵelementStart(25, \"label\", 17);\r\n        i0.ɵɵtext(26, \"Employer Location\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(27, \"input\", 18);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(28, \"div\", 7);\r\n        i0.ɵɵelementStart(29, \"label\", 19);\r\n        i0.ɵɵtext(30, \"Employer Type\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(31, \"select\", 20);\r\n        i0.ɵɵlistener(\"change\", function AddEmployersComponent_Template_select_change_31_listener($event) { return ctx.onTypeChange($event); });\r\n        i0.ɵɵelementStart(32, \"option\", 21);\r\n        i0.ɵɵtext(33, \"Please select type\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(34, \"option\", 22);\r\n        i0.ɵɵtext(35, \"Private Limited Company(LTD)\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(36, \"option\", 23);\r\n        i0.ɵɵtext(37, \"Public Limited Company(PLC)\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(38, \"option\", 24);\r\n        i0.ɵɵtext(39, \"Partnership\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(40, \"option\", 25);\r\n        i0.ɵɵtext(41, \"Non-Profit Organization\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(42, \"option\", 26);\r\n        i0.ɵɵtext(43, \"Other\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(44, AddEmployersComponent_div_44_Template, 4, 0, \"div\", 27);\r\n        i0.ɵɵelementStart(45, \"div\", 7);\r\n        i0.ɵɵelementStart(46, \"label\", 28);\r\n        i0.ɵɵtext(47, \"Founded\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(48, \"input\", 29);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(49, \"div\", 7);\r\n        i0.ɵɵelementStart(50, \"label\", 30);\r\n        i0.ɵɵtext(51, \"Employer Sector\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(52, \"select\", 31);\r\n        i0.ɵɵelementStart(53, \"option\", 21);\r\n        i0.ɵɵtext(54, \"Please select sector\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(55, AddEmployersComponent_option_55_Template, 2, 2, \"option\", 32);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(56, \"div\", 7);\r\n        i0.ɵɵelementStart(57, \"label\", 33);\r\n        i0.ɵɵtext(58, \"Website\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(59, \"input\", 34);\r\n        i0.ɵɵtemplate(60, AddEmployersComponent_div_60_Template, 3, 2, \"div\", 14);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(61, \"div\", 7);\r\n        i0.ɵɵelementStart(62, \"label\", 35);\r\n        i0.ɵɵtext(63, \"Employer Size\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(64, \"select\", 36);\r\n        i0.ɵɵelementStart(65, \"option\", 21);\r\n        i0.ɵɵtext(66, \"Please select size\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(67, \"option\", 37);\r\n        i0.ɵɵtext(68, \" 1-9 Employee \");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(69, \"option\", 38);\r\n        i0.ɵɵtext(70, \"10-49 Employee \");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(71, \"option\", 39);\r\n        i0.ɵɵtext(72, \"50-499 Employee\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(73, \"option\", 40);\r\n        i0.ɵɵtext(74, \"500-999 Employee \");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(75, \"option\", 41);\r\n        i0.ɵɵtext(76, \" 1,000-4,999 Employee\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(77, \"option\", 42);\r\n        i0.ɵɵtext(78, \" 5,000+ Employees\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(79, \"div\", 7);\r\n        i0.ɵɵelementStart(80, \"label\", 43);\r\n        i0.ɵɵtext(81, \"About\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(82, \"textarea\", 44);\r\n        i0.ɵɵlistener(\"input\", function AddEmployersComponent_Template_textarea_input_82_listener($event) { return ctx.onTextChange($event); });\r\n        i0.ɵɵtext(83, \"                          \");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(84, \"div\", 45);\r\n        i0.ɵɵtext(85);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(86, AddEmployersComponent_div_86_Template, 2, 0, \"div\", 14);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(87, \"hr\");\r\n        i0.ɵɵelementStart(88, \"div\", 46);\r\n        i0.ɵɵelementStart(89, \"h6\", 47);\r\n        i0.ɵɵtext(90, \"HR / Hiring Manager\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(91, AddEmployersComponent_div_91_Template, 26, 4, \"div\", 48);\r\n        i0.ɵɵtemplate(92, AddEmployersComponent_button_92_Template, 3, 0, \"button\", 49);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(93, \"div\", 50);\r\n        i0.ɵɵtemplate(94, AddEmployersComponent_button_94_Template, 2, 0, \"button\", 51);\r\n        i0.ɵɵelementStart(95, \"button\", 52);\r\n        i0.ɵɵtext(96, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n    } if (rf & 2) {\r\n        let tmp_5_0;\r\n        let tmp_6_0;\r\n        let tmp_7_0;\r\n        let tmp_8_0;\r\n        let tmp_14_0;\r\n        let tmp_17_0;\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Employer\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"formGroup\", ctx.addNewCompanyForm);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.imageSrc);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.addNewCompanyForm.get(\"CO_logo\")) == null ? null : tmp_5_0.errors == null ? null : tmp_5_0.errors.fileSizeValidator);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx.addNewCompanyForm.get(\"CO_logo\")) == null ? null : tmp_6_0.errors == null ? null : tmp_6_0.errors.fileAspectRatioValidator);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isCropperVisible && ctx.imageName && !((tmp_7_0 = ctx.addNewCompanyForm.get(\"CO_logo\")) == null ? null : tmp_7_0.errors == null ? null : tmp_7_0.errors.fileSizeValidator));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.isCropperVisible && !((tmp_8_0 = ctx.addNewCompanyForm.get(\"CO_logo\")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors.fileSizeValidator));\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(17);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showOtherTypeInput);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(7);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.SectorList);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.addNewCompanyForm.get(\"CO_website\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.addNewCompanyForm.get(\"CO_website\")) == null ? null : tmp_14_0.touched));\r\n        i0.ɵɵadvance(22);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(3);\r\n        i0.ɵɵtextInterpolate1(\" \", ctx.characterCount, \"/500 \");\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx.addNewCompanyForm.get(\"CO_about\")) == null ? null : tmp_17_0.errors) && ((tmp_17_0 = ctx.addNewCompanyForm.get(\"CO_about\")) == null ? null : tmp_17_0.errors == null ? null : tmp_17_0.errors.maxlength));\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.insightFormArray.controls);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.insightFormArray.length < 3);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n    } }, directives: [i7.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.RequiredValidator, i6.NgIf, i1.SelectControlValueAccessor, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i8.NgxMaskDirective, i6.NgForOf, i1.MaxLengthValidator, i1.FormArrayName, i4.RouterLink, i9.ImageCropperComponent, i1.FormGroupName, i6.NgClass], styles: [\".footer[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n\\n.head-exst[_ngcontent-%COMP%] {\\n  width: 100vh;\\n}\\n\\n.fa-plus[_ngcontent-%COMP%] {\\n  font-size: small;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  color: rgba(81, 80, 80, 0.856) !important;\\n}\\n\\n.character-count[_ngcontent-%COMP%] {\\n  font-size: smaller;\\n  margin-top: 2px;\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n\\n.suggestion[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1224px) {\\n  .insight-btn[_ngcontent-%COMP%] {\\n    padding: 4px 5px;\\n  }\\n\\n  .fas[_ngcontent-%COMP%] {\\n    font-size: small;\\n  }\\n}\\n\\n@media screen and (min-width: 320px) and (max-width: 768px) {\\n  .btns[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: center;\\n    margin-bottom: 20px;\\n  }\\n\\n  .add-insight-btn[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n}\\n\\n.existing-logo[_ngcontent-%COMP%] {\\n  max-width: 200px !important;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  vertical-align: middle;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFkZC1lbXBsb3llcnMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0U7RUFDQSx1QkFBQTtBQUFGOztBQUVFO0VBQ0UsWUFBQTtBQUNKOztBQUdDO0VBQ0MsZ0JBQUE7QUFBRjs7QUFHQztFQUNDLHlDQUFBO0FBQUY7O0FBR0M7RUFDRyxrQkFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0EseUJBQUE7QUFBSjs7QUFHQztFQUNDLGVBQUE7QUFBRjs7QUFLQTtFQUNFO0lBQ0UsZ0JBQUE7RUFGRjs7RUFLQTtJQUNFLGdCQUFBO0VBRkY7QUFDRjs7QUFLQTtFQUNBO0lBQ0UsYUFBQTtJQUNBLHVCQUFBO0lBQ0EsbUJBQUE7RUFIQTs7RUFNRjtJQUNFLG1CQUFBO0VBSEE7QUFDRjs7QUFNQTtFQUNFLDJCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHVCQUFBO0VBQ0Esc0JBQUE7QUFKRiIsImZpbGUiOiJhZGQtZW1wbG95ZXJzLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiXHJcbiAgLmZvb3RlcntcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcclxuICB9XHJcbiAgLmhlYWQtZXhzdHtcclxuICAgIHdpZHRoOiAxMDB2aDtcclxuICB9XHJcblxyXG4gIFxyXG4gLmZhLXBsdXN7XHJcbiAgZm9udC1zaXplOiBzbWFsbDtcclxuIH1cclxuXHJcbiAuc3VidGl0bGV7XHJcbiAgY29sb3I6IHJnYmEoODEsIDgwLCA4MCwgMC44NTYpICFpbXBvcnRhbnQ7XHJcbiB9XHJcblxyXG4gLmNoYXJhY3Rlci1jb3VudHtcclxuICAgIGZvbnQtc2l6ZTogc21hbGxlcjtcclxuICAgIG1hcmdpbi10b3A6IDJweDtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xyXG4gfVxyXG5cclxuIC5zdWdnZXN0aW9ue1xyXG4gIGZvbnQtc2l6ZTogMTJweDtcclxuIH1cclxuXHJcblxyXG5cclxuQG1lZGlhIHNjcmVlbiBhbmQgKG1pbi13aWR0aDogOTkycHgpIGFuZCAobWF4LXdpZHRoOiAxMjI0cHgpIHtcclxuICAuaW5zaWdodC1idG57XHJcbiAgICBwYWRkaW5nOiA0cHggNXB4O1xyXG4gIH1cclxuXHJcbiAgLmZhc3tcclxuICAgIGZvbnQtc2l6ZTogc21hbGw7XHJcbiAgfVxyXG59XHJcblxyXG5AbWVkaWEgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAzMjBweCkgYW5kIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbi5idG5ze1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxufVxyXG5cclxuLmFkZC1pbnNpZ2h0LWJ0bntcclxuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG59XHJcbn1cclxuXHJcbi5leGlzdGluZy1sb2dvIHtcclxuICBtYXgtd2lkdGg6IDIwMHB4ICFpbXBvcnRhbnQ7IFxyXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7IFxyXG4gIG92ZXJmbG93OiBoaWRkZW47IFxyXG4gIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOyBcclxuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOyBcclxufVxyXG4iXX0= */\"] });\r\n"]}, "metadata": {}, "sourceType": "module"}
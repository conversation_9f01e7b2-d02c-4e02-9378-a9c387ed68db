{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, QueryList, Directive, Input, InjectionToken, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, BehaviorSubject, of } from 'rxjs';\nimport { hasModifierKey, A, Z, ZERO, NINE, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceElement } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** IDs are delimited by an empty space, as per the spec. */\n\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\n\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n\n  if (ids.some(existingId => existingId.trim() == id.trim())) {\n    return;\n  }\n\n  ids.push(id.trim());\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\n\n\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  const filteredIds = ids.filter(val => val != id.trim());\n\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\n\n\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  return (el.getAttribute(attr) || '').match(/\\S+/g) || [];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\n\n\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\n\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\n\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\n\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\n\nclass AriaDescriber {\n  constructor(_document,\n  /**\n   * @deprecated To be turned into a required parameter.\n   * @breaking-change 14.0.0\n   */\n  _platform) {\n    this._platform = _platform;\n    /** Map of all registered message elements that have been placed into the document. */\n\n    this._messageRegistry = new Map();\n    /** Container for all registered messages. */\n\n    this._messagesContainer = null;\n    /** Unique ID for the service. */\n\n    this._id = `${nextId++}`;\n    this._document = _document;\n  }\n\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n\n    const key = getKey(message, role);\n\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message);\n\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n\n    const key = getKey(message, role);\n\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    } // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n\n\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n\n\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n\n\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n\n    setMessageId(messageElement);\n    messageElement.textContent = message;\n\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n\n    this._createMessagesContainer();\n\n    this._messagesContainer.appendChild(messageElement);\n\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n\n\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n\n\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n\n    const containerClassName = 'cdk-describedby-message-container';\n\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n\n    const messagesContainer = this._document.createElement('div'); // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n\n\n    messagesContainer.style.visibility = 'hidden'; // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden'); // @breaking-change 14.0.0 Remove null check for `_platform`.\n\n    if (this._platform && !this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n\n    this._document.body.appendChild(messagesContainer);\n\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n\n\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n\n\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key); // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n\n\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n\n\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n\n\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n\n    const registeredMessage = this._messageRegistry.get(key);\n\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n\n\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label'); // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n\n\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n\n}\n\nAriaDescriber.ɵfac = function AriaDescriber_Factory(t) {\n  return new (t || AriaDescriber)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.Platform));\n};\n\nAriaDescriber.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AriaDescriber,\n  factory: AriaDescriber.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1.Platform\n    }];\n  }, null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\n\n\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\n\n\nfunction setMessageId(element) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${nextId++}`;\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\n\n\nclass ListKeyManager {\n  constructor(_items) {\n    this._items = _items;\n    this._activeItemIndex = -1;\n    this._activeItem = null;\n    this._wrap = false;\n    this._letterKeyStream = new Subject();\n    this._typeaheadSubscription = Subscription.EMPTY;\n    this._vertical = true;\n    this._allowedModifierKeys = [];\n    this._homeAndEnd = false;\n    /**\n     * Predicate function that can be used to check whether an item should be skipped\n     * by the key manager. By default, disabled items are skipped.\n     */\n\n    this._skipPredicateFn = item => item.disabled; // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n\n\n    this._pressedLetters = [];\n    /**\n     * Stream that emits any time the TAB key is pressed, so components can react\n     * when focus is shifted off of the list.\n     */\n\n    this.tabOut = new Subject();\n    /** Stream that emits whenever the active item of the list manager changes. */\n\n    this.change = new Subject(); // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n\n    if (_items instanceof QueryList) {\n      _items.changes.subscribe(newItems => {\n        if (this._activeItem) {\n          const itemArray = newItems.toArray();\n          const newIndex = itemArray.indexOf(this._activeItem);\n\n          if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n            this._activeItemIndex = newIndex;\n          }\n        }\n      });\n    }\n  }\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n\n\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n\n\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n\n\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n\n\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n\n\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n\n\n  withTypeAhead(debounceInterval = 200) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && this._items.length && this._items.some(item => typeof item.getLabel !== 'function')) {\n      throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n\n    this._typeaheadSubscription.unsubscribe(); // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n\n\n    this._typeaheadSubscription = this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(debounceInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join(''))).subscribe(inputString => {\n      const items = this._getItemsArray(); // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n\n\n      for (let i = 1; i < items.length + 1; i++) {\n        const index = (this._activeItemIndex + i) % items.length;\n        const item = items[index];\n\n        if (!this._skipPredicateFn(item) && item.getLabel().toUpperCase().trim().indexOf(inputString) === 0) {\n          this.setActiveItem(index);\n          break;\n        }\n      }\n\n      this._pressedLetters = [];\n    });\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n\n\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem;\n    this.updateActiveItem(item);\n\n    if (this._activeItem !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n\n\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n          // otherwise fall back to resolving alphanumeric characters via the keyCode.\n          if (event.key && event.key.length === 1) {\n            this._letterKeyStream.next(event.key.toLocaleUpperCase());\n          } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n            this._letterKeyStream.next(String.fromCharCode(keyCode));\n          }\n        } // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n\n\n        return;\n    }\n\n    this._pressedLetters = [];\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n\n\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n\n\n  get activeItem() {\n    return this._activeItem;\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n\n\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Sets the active item to the first enabled item in the list. */\n\n\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n\n\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._items.length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n\n\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n\n\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index]; // Explicitly check for `null` and `undefined` because other falsy values are valid.\n\n    this._activeItem = activeItem == null ? null : activeItem;\n    this._activeItemIndex = index;\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n\n\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n\n\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n\n\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n\n\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n\n    if (!items[index]) {\n      return;\n    }\n\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n\n      if (!items[index]) {\n        return;\n      }\n    }\n\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n\n\n  _getItemsArray() {\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n\n    super.setActiveItem(index);\n\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass FocusKeyManager extends ListKeyManager {\n  constructor() {\n    super(...arguments);\n    this._origin = 'program';\n  }\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n\n\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n\n  setActiveItem(item) {\n    super.setActiveItem(item);\n\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Configuration for the isFocusable method.\n */\n\n\nclass IsFocusableConfig {\n  constructor() {\n    /**\n     * Whether to count an element as focusable even if it is not currently visible.\n     */\n    this.ignoreVisibility = false;\n  }\n\n} // The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\n\n\nclass InteractivityChecker {\n  constructor(_platform) {\n    this._platform = _platform;\n  }\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n\n\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n\n\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n\n\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n\n    const frameElement = getFrameElement(getWindow(element));\n\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      } // Browsers disable tabbing to an element inside of an invisible frame.\n\n\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    } // In iOS, the browser only considers some specific elements as tabbable.\n\n\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      } // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n\n\n      return tabIndexValue !== -1;\n    }\n\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      } // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n\n\n      if (tabIndexValue !== null) {\n        return true;\n      } // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n\n\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n\n\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n\n}\n\nInteractivityChecker.ɵfac = function InteractivityChecker_Factory(t) {\n  return new (t || InteractivityChecker)(i0.ɵɵinject(i1.Platform));\n};\n\nInteractivityChecker.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InteractivityChecker,\n  factory: InteractivityChecker.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }];\n  }, null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\n\n\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\n\n\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\n\n\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\n\n\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\n\n\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\n\n\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\n\n\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\n\n\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\n\n\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  } // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n\n\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\n\n\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\n\n\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\n\n\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n *\n * @deprecated Use `ConfigurableFocusTrap` instead.\n * @breaking-change 11.0.0\n */\n\n\nclass FocusTrap {\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._hasAttached = false; // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n\n    this.startAnchorListener = () => this.focusLastTabbableElement();\n\n    this.endAnchorListener = () => this.focusFirstTabbableElement();\n\n    this._enabled = true;\n\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Whether the focus trap is active. */\n\n\n  get enabled() {\n    return this._enabled;\n  }\n\n  set enabled(value) {\n    this._enabled = value;\n\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n\n\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n\n\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n\n      this._hasAttached = true;\n    }\n\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n\n\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n\n\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n\n\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n\n\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n\n\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      } // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n\n\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n\n      redirectToElement.focus(options);\n      return true;\n    }\n\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n\n\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n\n\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n\n\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n\n\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n\n    const children = root.children;\n\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n\n\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    } // Iterate in reverse DOM order.\n\n\n    const children = root.children;\n\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n\n    return null;\n  }\n  /** Creates an anchor element. */\n\n\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n\n\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n\n\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n\n\n  _executeOnStable(fn) {\n    if (this._ngZone.isStable) {\n      fn();\n    } else {\n      this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n    }\n  }\n\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n * @deprecated Use `ConfigurableFocusTrapFactory` instead.\n * @breaking-change 11.0.0\n */\n\n\nclass FocusTrapFactory {\n  constructor(_checker, _ngZone, _document) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n\n\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n  }\n\n}\n\nFocusTrapFactory.ɵfac = function FocusTrapFactory_Factory(t) {\n  return new (t || FocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n};\n\nFocusTrapFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FocusTrapFactory,\n  factory: FocusTrapFactory.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/** Directive for trapping focus within a region. */\n\n\nclass CdkTrapFocus {\n  constructor(_elementRef, _focusTrapFactory,\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 13.0.0\n   */\n  _document) {\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n\n    this._previouslyFocusedElement = null;\n    this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n  }\n  /** Whether the focus trap is active. */\n\n\n  get enabled() {\n    return this.focusTrap.enabled;\n  }\n\n  set enabled(value) {\n    this.focusTrap.enabled = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n\n\n  get autoCapture() {\n    return this._autoCapture;\n  }\n\n  set autoCapture(value) {\n    this._autoCapture = coerceBooleanProperty(value);\n  }\n\n  ngOnDestroy() {\n    this.focusTrap.destroy(); // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n\n      this._previouslyFocusedElement = null;\n    }\n  }\n\n  ngAfterContentInit() {\n    this.focusTrap.attachAnchors();\n\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n\n  ngDoCheck() {\n    if (!this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap.focusInitialElementWhenReady();\n  }\n\n}\n\nCdkTrapFocus.ɵfac = function CdkTrapFocus_Factory(t) {\n  return new (t || CdkTrapFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT));\n};\n\nCdkTrapFocus.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkTrapFocus,\n  selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n  inputs: {\n    enabled: [\"cdkTrapFocus\", \"enabled\"],\n    autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\"]\n  },\n  exportAs: [\"cdkTrapFocus\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: FocusTrapFactory\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    enabled: [{\n      type: Input,\n      args: ['cdkTrapFocus']\n    }],\n    autoCapture: [{\n      type: Input,\n      args: ['cdkTrapFocusAutoCapture']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\n\n\nclass ConfigurableFocusTrap extends FocusTrap {\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config) {\n    super(_element, _checker, _ngZone, _document, config.defer);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n\n    this._focusTrapManager.register(this);\n  }\n  /** Whether the FocusTrap is enabled. */\n\n\n  get enabled() {\n    return this._enabled;\n  }\n\n  set enabled(value) {\n    this._enabled = value;\n\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n\n\n  destroy() {\n    this._focusTrapManager.deregister(this);\n\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n\n\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n\n\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n\n    this.toggleAnchors(false);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** The injection token used to specify the inert strategy. */\n\n\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\n\nclass EventListenerFocusTrapInertStrategy {\n  constructor() {\n    /** Focus event handler. */\n    this._listener = null;\n  }\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n\n\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n\n    this._listener = e => this._trapFocus(focusTrap, e);\n\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n\n\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n\n\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element; // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\n\n\nclass FocusTrapManager {\n  constructor() {\n    // A stack of the FocusTraps on the page. Only the FocusTrap at the\n    // top of the stack is active.\n    this._focusTrapStack = [];\n  }\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n\n\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n\n    stack.push(focusTrap);\n\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n\n\n  deregister(focusTrap) {\n    focusTrap._disable();\n\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n\n    if (i !== -1) {\n      stack.splice(i, 1);\n\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n\n}\n\nFocusTrapManager.ɵfac = function FocusTrapManager_Factory(t) {\n  return new (t || FocusTrapManager)();\n};\n\nFocusTrapManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FocusTrapManager,\n  factory: FocusTrapManager.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Factory that allows easy instantiation of configurable focus traps. */\n\n\nclass ConfigurableFocusTrapFactory {\n  constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._focusTrapManager = _focusTrapManager;\n    this._document = _document; // TODO split up the strategies into different modules, similar to DateAdapter.\n\n    this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject);\n  }\n\n}\n\nConfigurableFocusTrapFactory.ɵfac = function ConfigurableFocusTrapFactory_Factory(t) {\n  return new (t || ConfigurableFocusTrapFactory)(i0.ɵɵinject(InteractivityChecker), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(FocusTrapManager), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(FOCUS_TRAP_INERT_STRATEGY, 8));\n};\n\nConfigurableFocusTrapFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ConfigurableFocusTrapFactory,\n  factory: ConfigurableFocusTrapFactory.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: InteractivityChecker\n    }, {\n      type: i0.NgZone\n    }, {\n      type: FocusTrapManager\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [FOCUS_TRAP_INERT_STRATEGY]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\n\n\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when both `offsetX` and `offsetY` are\n  // zero or `event.buttons` is zero, depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `offsetX` and `offsetY` work on Chrome, but fail on Firefox.\n  // Note that there's an edge case where the user could click the 0x0 spot of the\n  // screen themselves, but that is unlikely to contain interactive elements.\n  return event.buttons === 0 || event.offsetX === 0 && event.offsetY === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\n\n\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0]; // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\n\n\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\n\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\n\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\n\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\n\nclass InputModalityDetector {\n  constructor(_platform, ngZone, document, options) {\n    this._platform = _platform;\n    /**\n     * The most recently detected input modality event target. Is null if no input modality has been\n     * detected or if the associated event target is null for some unknown reason.\n     */\n\n    this._mostRecentTarget = null;\n    /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n\n    this._modality = new BehaviorSubject(null);\n    /**\n     * The timestamp of the last touch input modality. Used to determine whether mousedown events\n     * should be attributed to mouse or touch.\n     */\n\n    this._lastTouchMs = 0;\n    /**\n     * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n     * bound.\n     */\n\n    this._onKeydown = event => {\n      // If this is one of the keys we should ignore, then ignore it and don't update the input\n      // modality to keyboard.\n      if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n        return;\n      }\n\n      this._modality.next('keyboard');\n\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n\n\n    this._onMousedown = event => {\n      // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n      // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n      // after the previous touch event.\n      if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n        return;\n      } // Fake mousedown events are fired by some screen readers when controls are activated by the\n      // screen reader. Attribute them to keyboard input modality.\n\n\n      this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n    /**\n     * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n     * gets bound.\n     */\n\n\n    this._onTouchstart = event => {\n      // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n      // events are fired. Again, attribute to keyboard input modality.\n      if (isFakeTouchstartFromScreenReader(event)) {\n        this._modality.next('keyboard');\n\n        return;\n      } // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n      // triggered via mouse vs touch.\n\n\n      this._lastTouchMs = Date.now();\n\n      this._modality.next('touch');\n\n      this._mostRecentTarget = _getEventTarget(event);\n    };\n\n    this._options = { ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    }; // Skip the first emission as it's null.\n\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged()); // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n\n    if (_platform.isBrowser) {\n      ngZone.runOutsideAngular(() => {\n        document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n        document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n        document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n      });\n    }\n  }\n  /** The most recently detected input modality. */\n\n\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n\n  ngOnDestroy() {\n    this._modality.complete();\n\n    if (this._platform.isBrowser) {\n      document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n      document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n      document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n    }\n  }\n\n}\n\nInputModalityDetector.ɵfac = function InputModalityDetector_Factory(t) {\n  return new (t || InputModalityDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(INPUT_MODALITY_DETECTOR_OPTIONS, 8));\n};\n\nInputModalityDetector.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InputModalityDetector,\n  factory: InputModalityDetector.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/** @docs-private */\n\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\n\n\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass LiveAnnouncer {\n  constructor(elementToken, _ngZone, _document, _defaultOptions) {\n    this._ngZone = _ngZone;\n    this._defaultOptions = _defaultOptions; // We inject the live element and document as `any` because the constructor signature cannot\n    // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n    // a class decorator causes TypeScript to preserve the constructor signature types.\n\n    this._document = _document;\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n\n    this.clear();\n    clearTimeout(this._previousTimeout);\n\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    } // TODO: ensure changing the politeness works on all environments we support.\n\n\n    this._liveElement.setAttribute('aria-live', politeness); // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n\n\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n\n        this._currentResolve();\n\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n\n\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n\n    const previousElements = this._document.getElementsByClassName(elementClass);\n\n    const liveEl = this._document.createElement('div'); // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n\n\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n\n    this._document.body.appendChild(liveEl);\n\n    return liveEl;\n  }\n\n}\n\nLiveAnnouncer.ɵfac = function LiveAnnouncer_Factory(t) {\n  return new (t || LiveAnnouncer)(i0.ɵɵinject(LIVE_ANNOUNCER_ELEMENT_TOKEN, 8), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, 8));\n};\n\nLiveAnnouncer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: LiveAnnouncer,\n  factory: LiveAnnouncer.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\n\n\nclass CdkAriaLive {\n  constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n    this._elementRef = _elementRef;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._contentObserver = _contentObserver;\n    this._ngZone = _ngZone;\n    this._politeness = 'polite';\n  }\n  /** The aria-live politeness level to use when announcing messages. */\n\n\n  get politeness() {\n    return this._politeness;\n  }\n\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent; // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness);\n\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n\n}\n\nCdkAriaLive.ɵfac = function CdkAriaLive_Factory(t) {\n  return new (t || CdkAriaLive)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(LiveAnnouncer), i0.ɵɵdirectiveInject(i1$1.ContentObserver), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nCdkAriaLive.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkAriaLive,\n  selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n  inputs: {\n    politeness: [\"cdkAriaLive\", \"politeness\"]\n  },\n  exportAs: [\"cdkAriaLive\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: LiveAnnouncer\n    }, {\n      type: i1$1.ContentObserver\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** InjectionToken for FocusMonitorOptions. */\n\n\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\n\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\n\nclass FocusMonitor {\n  constructor(_ngZone, _platform, _inputModalityDetector,\n  /** @breaking-change 11.0.0 make document required */\n  document, options) {\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    this._inputModalityDetector = _inputModalityDetector;\n    /** The focus origin that the next focus event is a result of. */\n\n    this._origin = null;\n    /** Whether the window has just been focused. */\n\n    this._windowFocused = false;\n    /**\n     * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n     * focus events to touch interactions requires special logic.\n     */\n\n    this._originFromTouchInteraction = false;\n    /** Map of elements being monitored to their info. */\n\n    this._elementInfo = new Map();\n    /** The number of elements currently being monitored. */\n\n    this._monitoredElementCount = 0;\n    /**\n     * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n     * as well as the number of monitored elements that they contain. We have to treat focus/blur\n     * handlers differently from the rest of the events, because the browser won't emit events\n     * to the document when focus moves inside of a shadow root.\n     */\n\n    this._rootNodeFocusListenerCount = new Map();\n    /**\n     * Event listener for `focus` events on the window.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n\n    this._windowFocusListener = () => {\n      // Make a note of when the window regains focus, so we can\n      // restore the origin info for the focused element.\n      this._windowFocused = true;\n      this._windowFocusTimeoutId = window.setTimeout(() => this._windowFocused = false);\n    };\n    /** Subject for stopping our InputModalityDetector subscription. */\n\n\n    this._stopInputModalityDetector = new Subject();\n    /**\n     * Event listener for `focus` and 'blur' events on the document.\n     * Needs to be an arrow function in order to preserve the context when it gets bound.\n     */\n\n    this._rootNodeFocusAndBlurListener = event => {\n      const target = _getEventTarget(event);\n\n      const handler = event.type === 'focus' ? this._onFocus : this._onBlur; // We need to walk up the ancestor chain in order to support `checkChildren`.\n\n      for (let element = target; element; element = element.parentElement) {\n        handler.call(this, event, element);\n      }\n    };\n\n    this._document = document;\n    this._detectionMode = options?.detectionMode || 0\n    /* IMMEDIATE */\n    ;\n  }\n\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element); // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      return of(null);\n    } // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n\n\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n\n    const cachedInfo = this._elementInfo.get(nativeElement); // Check if we're already monitoring this element.\n\n\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n\n      return cachedInfo.subject;\n    } // Create monitored element info.\n\n\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n\n    this._elementInfo.set(nativeElement, info);\n\n    this._registerGlobalListeners(info);\n\n    return info.subject;\n  }\n\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n\n    const elementInfo = this._elementInfo.get(nativeElement);\n\n    if (elementInfo) {\n      elementInfo.subject.complete();\n\n      this._setClasses(nativeElement);\n\n      this._elementInfo.delete(nativeElement);\n\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n\n    const focusedElement = this._getDocument().activeElement; // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n\n\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin); // `focus` isn't available on the server\n\n\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n\n\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n  _getWindow() {\n    const doc = this._getDocument();\n\n    return doc.defaultView || window;\n  }\n\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    } // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n\n\n    return this._windowFocused && this._lastFocusOrigin ? this._lastFocusOrigin : 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n\n\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === 1\n    /* EVENTUAL */\n    || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n\n\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n\n\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction; // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n\n      if (this._detectionMode === 0\n      /* IMMEDIATE */\n      ) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n\n\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n\n    const focusEventTarget = _getEventTarget(event);\n\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n\n\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n\n    this._setClasses(element);\n\n    this._emitOrigin(elementInfo.subject, null);\n  }\n\n  _emitOrigin(subject, origin) {\n    this._ngZone.run(() => subject.next(origin));\n  }\n\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1); // Register global listeners when first element is monitored.\n\n\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n\n        window.addEventListener('focus', this._windowFocusListener);\n      }); // The InputModalityDetector is also just a collection of global listeners.\n\n\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true\n        /* isFromInteraction */\n        );\n      });\n    }\n  }\n\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    } // Unregister global listeners when last element is unmonitored.\n\n\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n\n      window.removeEventListener('focus', this._windowFocusListener); // Equivalently, stop our InputModalityDetector subscription.\n\n      this._stopInputModalityDetector.next(); // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n\n\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n\n\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n\n    this._emitOrigin(elementInfo.subject, origin);\n\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n\n\n  _getClosestElementsInfo(element) {\n    const results = [];\n\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n\n    return results;\n  }\n\n}\n\nFocusMonitor.ɵfac = function FocusMonitor_Factory(t) {\n  return new (t || FocusMonitor)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(InputModalityDetector), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(FOCUS_MONITOR_DEFAULT_OPTIONS, 8));\n};\n\nFocusMonitor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FocusMonitor,\n  factory: FocusMonitor.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i1.Platform\n    }, {\n      type: InputModalityDetector\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\n\n\nclass CdkMonitorFocus {\n  constructor(_elementRef, _focusMonitor) {\n    this._elementRef = _elementRef;\n    this._focusMonitor = _focusMonitor;\n    this.cdkFocusChange = new EventEmitter();\n  }\n\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => this.cdkFocusChange.emit(origin));\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n\n}\n\nCdkMonitorFocus.ɵfac = function CdkMonitorFocus_Factory(t) {\n  return new (t || CdkMonitorFocus)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(FocusMonitor));\n};\n\nCdkMonitorFocus.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkMonitorFocus,\n  selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n  outputs: {\n    cdkFocusChange: \"cdkFocusChange\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: FocusMonitor\n    }];\n  }, {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\n\n\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\n\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\n\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\n\nclass HighContrastModeDetector {\n  constructor(_platform, document) {\n    this._platform = _platform;\n    this._document = document;\n  }\n  /** Gets the current high-contrast-mode for the page. */\n\n\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return 0\n      /* NONE */\n      ;\n    } // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n\n\n    const testElement = this._document.createElement('div');\n\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n\n    this._document.body.appendChild(testElement); // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n\n\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n\n    switch (computedColor) {\n      case 'rgb(0,0,0)':\n        return 2\n        /* WHITE_ON_BLACK */\n        ;\n\n      case 'rgb(255,255,255)':\n        return 1\n        /* BLACK_ON_WHITE */\n        ;\n    }\n\n    return 0\n    /* NONE */\n    ;\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n\n\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList; // IE11 doesn't support `classList` operations with multiple arguments\n\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS);\n      bodyClasses.remove(BLACK_ON_WHITE_CSS_CLASS);\n      bodyClasses.remove(WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n\n      if (mode === 1\n      /* BLACK_ON_WHITE */\n      ) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS);\n        bodyClasses.add(BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === 2\n      /* WHITE_ON_BLACK */\n      ) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS);\n        bodyClasses.add(WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n\n}\n\nHighContrastModeDetector.ɵfac = function HighContrastModeDetector_Factory(t) {\n  return new (t || HighContrastModeDetector)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT));\n};\n\nHighContrastModeDetector.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HighContrastModeDetector,\n  factory: HighContrastModeDetector.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass A11yModule {\n  constructor(highContrastModeDetector) {\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n  }\n\n}\n\nA11yModule.ɵfac = function A11yModule_Factory(t) {\n  return new (t || A11yModule)(i0.ɵɵinject(HighContrastModeDetector));\n};\n\nA11yModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: A11yModule\n});\nA11yModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[ObserversModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule],\n      declarations: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], function () {\n    return [{\n      type: HighContrastModeDetector\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusTrap, FocusTrapFactory, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/cdk/fesm2020/a11y.mjs"], "names": ["DOCUMENT", "i0", "Injectable", "Inject", "QueryList", "Directive", "Input", "InjectionToken", "Optional", "EventEmitter", "Output", "NgModule", "i1", "_getFocusedElementPierceShadowDom", "normalizePassiveListenerOptions", "_getEventTarget", "_getShadowRoot", "Subject", "Subscription", "BehaviorSubject", "of", "hasModifierKey", "A", "Z", "ZERO", "NINE", "END", "HOME", "LEFT_ARROW", "RIGHT_ARROW", "UP_ARROW", "DOWN_ARROW", "TAB", "ALT", "CONTROL", "MAC_META", "META", "SHIFT", "tap", "debounceTime", "filter", "map", "take", "skip", "distinctUntilChanged", "takeUntil", "coerceBooleanProperty", "coerceElement", "i1$1", "ObserversModule", "ID_DELIMITER", "addAriaReferencedId", "el", "attr", "id", "ids", "getAriaReferenceIds", "some", "existingId", "trim", "push", "setAttribute", "join", "removeAriaReferencedId", "filteredIds", "val", "length", "removeAttribute", "getAttribute", "match", "MESSAGES_CONTAINER_ID", "CDK_DESCRIBEDBY_ID_PREFIX", "CDK_DESCRIBEDBY_HOST_ATTRIBUTE", "nextId", "AriaDescriber", "constructor", "_document", "_platform", "_messageRegistry", "Map", "_messagesContainer", "_id", "describe", "hostElement", "message", "role", "_canBeDescribed", "key", "<PERSON><PERSON><PERSON>", "setMessageId", "set", "messageElement", "referenceCount", "has", "_createMessageElement", "_isElementDescribedByMessage", "_addMessageReference", "removeDescription", "_isElementNode", "_removeMessageReference", "registeredMessage", "get", "_deleteMessageElement", "childNodes", "remove", "ngOnDestroy", "describedE<PERSON>s", "querySelectorAll", "i", "_removeCdkDescribedByReferenceIds", "clear", "createElement", "textContent", "_createMessagesContainer", "append<PERSON><PERSON><PERSON>", "delete", "containerClassName", "serverContainers", "messagesContainer", "style", "visibility", "classList", "add", "<PERSON><PERSON><PERSON><PERSON>", "body", "element", "originalReferenceIds", "indexOf", "referenceIds", "messageId", "trimmedMessage", "aria<PERSON><PERSON><PERSON>", "nodeType", "ELEMENT_NODE", "ɵfac", "Platform", "ɵprov", "type", "args", "providedIn", "undefined", "decorators", "ListKeyManager", "_items", "_activeItemIndex", "_activeItem", "_wrap", "_letterKeyStream", "_typeaheadSubscription", "EMPTY", "_vertical", "_allowedModifierKeys", "_homeAndEnd", "_skipPredicateFn", "item", "disabled", "_pressedLetters", "tabOut", "change", "changes", "subscribe", "newItems", "itemArray", "toArray", "newIndex", "skipPredicate", "predicate", "withWrap", "shouldWrap", "withVerticalOrientation", "enabled", "withHorizontalOrientation", "direction", "_horizontal", "withAllowedModifierKeys", "keys", "withTypeAhead", "debounceInterval", "ngDevMode", "get<PERSON><PERSON><PERSON>", "Error", "unsubscribe", "pipe", "letter", "inputString", "items", "_getItemsArray", "index", "toUpperCase", "setActiveItem", "withHomeAndEnd", "previousActiveItem", "updateActiveItem", "next", "onKeydown", "event", "keyCode", "modifiers", "isModifierAllowed", "every", "modifier", "setNextItemActive", "setPreviousItemActive", "setFirstItemActive", "setLastItemActive", "toLocaleUpperCase", "String", "fromCharCode", "preventDefault", "activeItemIndex", "activeItem", "isTyping", "_setActiveItemByIndex", "_setActiveItemByDelta", "delta", "_setActiveInWrapMode", "_setActiveInDefaultMode", "fallback<PERSON><PERSON><PERSON>", "ActiveDescendantKeyManager", "setInactiveStyles", "setActiveStyles", "FocusKeyManager", "arguments", "_origin", "setFocusOrigin", "origin", "focus", "IsFocusableConfig", "ignoreVisibility", "InteractivityChecker", "isDisabled", "hasAttribute", "isVisible", "hasGeometry", "getComputedStyle", "isTabbable", "frameElement", "getFrameElement", "getWindow", "getTabIndexValue", "nodeName", "toLowerCase", "tabIndexValue", "WEBKIT", "IOS", "isPotentiallyTabbableIOS", "FIREFOX", "tabIndex", "isFocusable", "config", "isPotentiallyFocusable", "window", "offsetWidth", "offsetHeight", "getClientRects", "isNativeFormElement", "isHiddenInput", "isInputElement", "isAnchorWithHref", "isAnchorElement", "hasValidTabIndex", "isNaN", "parseInt", "inputType", "node", "ownerDocument", "defaultView", "FocusTrap", "_element", "_checker", "_ngZone", "deferAnchors", "_hasAttached", "startAnchorListener", "focusLastTabbableElement", "endAnchorListener", "focusFirstTabbableElement", "_enabled", "attachAnchors", "value", "_startAnchor", "_endAnchor", "_toggleAnchorTabIndex", "destroy", "startAnchor", "endAnchor", "removeEventListener", "runOutsideAngular", "_createAnchor", "addEventListener", "parentNode", "insertBefore", "nextS<PERSON>ling", "focusInitialElementWhenReady", "options", "Promise", "resolve", "_executeOnStable", "focusInitialElement", "focusFirstTabbableElementWhenReady", "focusLastTabbableElementWhenReady", "_getRegionBoundary", "bound", "markers", "console", "warn", "_getFirstTabbableElement", "_getLastTabbableElement", "redirectToElement", "querySelector", "focus<PERSON><PERSON><PERSON><PERSON>", "has<PERSON>tta<PERSON>", "root", "children", "tabbable<PERSON><PERSON><PERSON>", "anchor", "isEnabled", "toggleAnchors", "fn", "isStable", "onStable", "FocusTrapFactory", "create", "deferCaptureElements", "NgZone", "CdkTrapFocus", "_elementRef", "_focusTrapFactory", "_previouslyFocusedElement", "focusTrap", "nativeElement", "autoCapture", "_autoCapture", "ngAfterContentInit", "_captureFocus", "ngDoCheck", "ngOnChanges", "autoCaptureChange", "firstChange", "ElementRef", "ɵdir", "selector", "exportAs", "ConfigurableFocusTrap", "_focusTrapManager", "_inertStrategy", "defer", "register", "deregister", "_enable", "preventFocus", "_disable", "allowFocus", "FOCUS_TRAP_INERT_STRATEGY", "EventListenerFocusTrapInertStrategy", "_listener", "e", "_trapFocus", "target", "focusTrapRoot", "contains", "closest", "setTimeout", "activeElement", "FocusTrapManager", "_focusTrapStack", "ft", "stack", "splice", "ConfigurableFocusTrapFactory", "configObject", "isFakeMousedownFromScreenReader", "buttons", "offsetX", "offsetY", "isFakeTouchstartFromScreenReader", "touch", "touches", "changedTouches", "identifier", "radiusX", "radiusY", "INPUT_MODALITY_DETECTOR_OPTIONS", "INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "TOUCH_BUFFER_MS", "modalityEventListenerOptions", "passive", "capture", "InputModalityDetector", "ngZone", "document", "_mostRecentTarget", "_modality", "_lastTouchMs", "_onKeydown", "_options", "_onMousedown", "Date", "now", "_onTouchstart", "modalityDetected", "modalityChanged", "mostRecentModality", "complete", "Document", "LIVE_ANNOUNCER_ELEMENT_TOKEN", "factory", "LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY", "LIVE_ANNOUNCER_DEFAULT_OPTIONS", "LiveAnnouncer", "elementToken", "_defaultOptions", "_liveElement", "_createLiveElement", "announce", "defaultOptions", "politeness", "duration", "clearTimeout", "_previousTimeout", "_currentPromise", "_currentResolve", "elementClass", "previousElements", "getElementsByClassName", "liveEl", "CdkAriaLive", "_liveAnnouncer", "_contentObserver", "_politeness", "_subscription", "observe", "elementText", "_previousAnnouncedText", "ContentObserver", "FOCUS_MONITOR_DEFAULT_OPTIONS", "captureEventListenerOptions", "FocusMonitor", "_inputModalityDetector", "_windowFocused", "_originFromTouchInteraction", "_elementInfo", "_monitoredElementCount", "_rootNodeFocusListenerCount", "_windowFocusListener", "_windowFocusTimeoutId", "_stopInputModalityDetector", "_rootNodeFocusAndBlurListener", "handler", "_onFocus", "_onBlur", "parentElement", "call", "_detectionMode", "detectionMode", "monitor", "check<PERSON><PERSON><PERSON><PERSON>", "rootNode", "_getDocument", "cachedInfo", "subject", "info", "_registerGlobalListeners", "stopMonitoring", "elementInfo", "_setClasses", "_removeGlobalListeners", "focusVia", "focusedElement", "_getClosestElementsInfo", "for<PERSON>ach", "currentElement", "_originChanged", "_set<PERSON><PERSON><PERSON>", "_info", "_getWindow", "doc", "_getFocus<PERSON><PERSON>in", "focusEventTarget", "_shouldBeAttributedToTouch", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toggle", "isFromInteraction", "_originTimeoutId", "ms", "relatedTarget", "Node", "_emit<PERSON><PERSON>in", "run", "rootNodeFocusListeners", "modality", "results", "CdkMonitorFocus", "_focusMonitor", "cdkFocusChange", "ngAfterViewInit", "_monitorSubscription", "emit", "BLACK_ON_WHITE_CSS_CLASS", "WHITE_ON_BLACK_CSS_CLASS", "HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS", "HighContrastModeDetector", "getHighContrastMode", "testElement", "backgroundColor", "position", "documentWindow", "computedStyle", "computedColor", "replace", "_applyBodyHighContrastModeCssClasses", "_hasCheckedHighContrastMode", "bodyClasses", "mode", "A11yModule", "highContrastModeDetector", "ɵmod", "ɵinj", "imports", "declarations", "exports"], "mappings": "AAAA,SAASA,QAAT,QAAyB,iBAAzB;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,MAArB,EAA6BC,SAA7B,EAAwCC,SAAxC,EAAmDC,KAAnD,EAA0DC,cAA1D,EAA0EC,QAA1E,EAAoFC,YAApF,EAAkGC,MAAlG,EAA0GC,QAA1G,QAA0H,eAA1H;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,iCAAT,EAA4CC,+BAA5C,EAA6EC,eAA7E,EAA8FC,cAA9F,QAAoH,uBAApH;AACA,SAASC,OAAT,EAAkBC,YAAlB,EAAgCC,eAAhC,EAAiDC,EAAjD,QAA2D,MAA3D;AACA,SAASC,cAAT,EAAyBC,CAAzB,EAA4BC,CAA5B,EAA+BC,IAA/B,EAAqCC,IAArC,EAA2CC,GAA3C,EAAgDC,IAAhD,EAAsDC,UAAtD,EAAkEC,WAAlE,EAA+EC,QAA/E,EAAyFC,UAAzF,EAAqGC,GAArG,EAA0GC,GAA1G,EAA+GC,OAA/G,EAAwHC,QAAxH,EAAkIC,IAAlI,EAAwIC,KAAxI,QAAqJ,uBAArJ;AACA,SAASC,GAAT,EAAcC,YAAd,EAA4BC,MAA5B,EAAoCC,GAApC,EAAyCC,IAAzC,EAA+CC,IAA/C,EAAqDC,oBAArD,EAA2EC,SAA3E,QAA4F,gBAA5F;AACA,SAASC,qBAAT,EAAgCC,aAAhC,QAAqD,uBAArD;AACA,OAAO,KAAKC,IAAZ,MAAsB,wBAAtB;AACA,SAASC,eAAT,QAAgC,wBAAhC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMC,YAAY,GAAG,GAArB;AACA;AACA;AACA;AACA;;AACA,SAASC,mBAAT,CAA6BC,EAA7B,EAAiCC,IAAjC,EAAuCC,EAAvC,EAA2C;AACvC,QAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAD,EAAKC,IAAL,CAA/B;;AACA,MAAIE,GAAG,CAACE,IAAJ,CAASC,UAAU,IAAIA,UAAU,CAACC,IAAX,MAAqBL,EAAE,CAACK,IAAH,EAA5C,CAAJ,EAA4D;AACxD;AACH;;AACDJ,EAAAA,GAAG,CAACK,IAAJ,CAASN,EAAE,CAACK,IAAH,EAAT;AACAP,EAAAA,EAAE,CAACS,YAAH,CAAgBR,IAAhB,EAAsBE,GAAG,CAACO,IAAJ,CAASZ,YAAT,CAAtB;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASa,sBAAT,CAAgCX,EAAhC,EAAoCC,IAApC,EAA0CC,EAA1C,EAA8C;AAC1C,QAAMC,GAAG,GAAGC,mBAAmB,CAACJ,EAAD,EAAKC,IAAL,CAA/B;AACA,QAAMW,WAAW,GAAGT,GAAG,CAACf,MAAJ,CAAWyB,GAAG,IAAIA,GAAG,IAAIX,EAAE,CAACK,IAAH,EAAzB,CAApB;;AACA,MAAIK,WAAW,CAACE,MAAhB,EAAwB;AACpBd,IAAAA,EAAE,CAACS,YAAH,CAAgBR,IAAhB,EAAsBW,WAAW,CAACF,IAAZ,CAAiBZ,YAAjB,CAAtB;AACH,GAFD,MAGK;AACDE,IAAAA,EAAE,CAACe,eAAH,CAAmBd,IAAnB;AACH;AACJ;AACD;AACA;AACA;AACA;;;AACA,SAASG,mBAAT,CAA6BJ,EAA7B,EAAiCC,IAAjC,EAAuC;AACnC;AACA,SAAO,CAACD,EAAE,CAACgB,YAAH,CAAgBf,IAAhB,KAAyB,EAA1B,EAA8BgB,KAA9B,CAAoC,MAApC,KAA+C,EAAtD;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,qBAAqB,GAAG,mCAA9B;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,yBAAyB,GAAG,yBAAlC;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,8BAA8B,GAAG,sBAAvC;AACA;;AACA,IAAIC,MAAM,GAAG,CAAb;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,aAAN,CAAoB;AAChBC,EAAAA,WAAW,CAACC,SAAD;AACX;AACJ;AACA;AACA;AACIC,EAAAA,SALW,EAKA;AACP,SAAKA,SAAL,GAAiBA,SAAjB;AACA;;AACA,SAAKC,gBAAL,GAAwB,IAAIC,GAAJ,EAAxB;AACA;;AACA,SAAKC,kBAAL,GAA0B,IAA1B;AACA;;AACA,SAAKC,GAAL,GAAY,GAAER,MAAM,EAAG,EAAvB;AACA,SAAKG,SAAL,GAAiBA,SAAjB;AACH;;AACDM,EAAAA,QAAQ,CAACC,WAAD,EAAcC,OAAd,EAAuBC,IAAvB,EAA6B;AACjC,QAAI,CAAC,KAAKC,eAAL,CAAqBH,WAArB,EAAkCC,OAAlC,CAAL,EAAiD;AAC7C;AACH;;AACD,UAAMG,GAAG,GAAGC,MAAM,CAACJ,OAAD,EAAUC,IAAV,CAAlB;;AACA,QAAI,OAAOD,OAAP,KAAmB,QAAvB,EAAiC;AAC7B;AACAK,MAAAA,YAAY,CAACL,OAAD,CAAZ;;AACA,WAAKN,gBAAL,CAAsBY,GAAtB,CAA0BH,GAA1B,EAA+B;AAAEI,QAAAA,cAAc,EAAEP,OAAlB;AAA2BQ,QAAAA,cAAc,EAAE;AAA3C,OAA/B;AACH,KAJD,MAKK,IAAI,CAAC,KAAKd,gBAAL,CAAsBe,GAAtB,CAA0BN,GAA1B,CAAL,EAAqC;AACtC,WAAKO,qBAAL,CAA2BV,OAA3B,EAAoCC,IAApC;AACH;;AACD,QAAI,CAAC,KAAKU,4BAAL,CAAkCZ,WAAlC,EAA+CI,GAA/C,CAAL,EAA0D;AACtD,WAAKS,oBAAL,CAA0Bb,WAA1B,EAAuCI,GAAvC;AACH;AACJ;;AACDU,EAAAA,iBAAiB,CAACd,WAAD,EAAcC,OAAd,EAAuBC,IAAvB,EAA6B;AAC1C,QAAI,CAACD,OAAD,IAAY,CAAC,KAAKc,cAAL,CAAoBf,WAApB,CAAjB,EAAmD;AAC/C;AACH;;AACD,UAAMI,GAAG,GAAGC,MAAM,CAACJ,OAAD,EAAUC,IAAV,CAAlB;;AACA,QAAI,KAAKU,4BAAL,CAAkCZ,WAAlC,EAA+CI,GAA/C,CAAJ,EAAyD;AACrD,WAAKY,uBAAL,CAA6BhB,WAA7B,EAA0CI,GAA1C;AACH,KAPyC,CAQ1C;AACA;;;AACA,QAAI,OAAOH,OAAP,KAAmB,QAAvB,EAAiC;AAC7B,YAAMgB,iBAAiB,GAAG,KAAKtB,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,CAA1B;;AACA,UAAIa,iBAAiB,IAAIA,iBAAiB,CAACR,cAAlB,KAAqC,CAA9D,EAAiE;AAC7D,aAAKU,qBAAL,CAA2Bf,GAA3B;AACH;AACJ;;AACD,QAAI,KAAKP,kBAAL,EAAyBuB,UAAzB,CAAoCrC,MAApC,KAA+C,CAAnD,EAAsD;AAClD,WAAKc,kBAAL,CAAwBwB,MAAxB;;AACA,WAAKxB,kBAAL,GAA0B,IAA1B;AACH;AACJ;AACD;;;AACAyB,EAAAA,WAAW,GAAG;AACV,UAAMC,iBAAiB,GAAG,KAAK9B,SAAL,CAAe+B,gBAAf,CAAiC,IAAGnC,8BAA+B,KAAI,KAAKS,GAAI,IAAhF,CAA1B;;AACA,SAAK,IAAI2B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,iBAAiB,CAACxC,MAAtC,EAA8C0C,CAAC,EAA/C,EAAmD;AAC/C,WAAKC,iCAAL,CAAuCH,iBAAiB,CAACE,CAAD,CAAxD;;AACAF,MAAAA,iBAAiB,CAACE,CAAD,CAAjB,CAAqBzC,eAArB,CAAqCK,8BAArC;AACH;;AACD,SAAKQ,kBAAL,EAAyBwB,MAAzB;AACA,SAAKxB,kBAAL,GAA0B,IAA1B;;AACA,SAAKF,gBAAL,CAAsBgC,KAAtB;AACH;AACD;AACJ;AACA;AACA;;;AACIhB,EAAAA,qBAAqB,CAACV,OAAD,EAAUC,IAAV,EAAgB;AACjC,UAAMM,cAAc,GAAG,KAAKf,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAAvB;;AACAtB,IAAAA,YAAY,CAACE,cAAD,CAAZ;AACAA,IAAAA,cAAc,CAACqB,WAAf,GAA6B5B,OAA7B;;AACA,QAAIC,IAAJ,EAAU;AACNM,MAAAA,cAAc,CAAC9B,YAAf,CAA4B,MAA5B,EAAoCwB,IAApC;AACH;;AACD,SAAK4B,wBAAL;;AACA,SAAKjC,kBAAL,CAAwBkC,WAAxB,CAAoCvB,cAApC;;AACA,SAAKb,gBAAL,CAAsBY,GAAtB,CAA0BF,MAAM,CAACJ,OAAD,EAAUC,IAAV,CAAhC,EAAiD;AAAEM,MAAAA,cAAF;AAAkBC,MAAAA,cAAc,EAAE;AAAlC,KAAjD;AACH;AACD;;;AACAU,EAAAA,qBAAqB,CAACf,GAAD,EAAM;AACvB,SAAKT,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,GAAgCI,cAAhC,EAAgDa,MAAhD;;AACA,SAAK1B,gBAAL,CAAsBqC,MAAtB,CAA6B5B,GAA7B;AACH;AACD;;;AACA0B,EAAAA,wBAAwB,GAAG;AACvB,QAAI,KAAKjC,kBAAT,EAA6B;AACzB;AACH;;AACD,UAAMoC,kBAAkB,GAAG,mCAA3B;;AACA,UAAMC,gBAAgB,GAAG,KAAKzC,SAAL,CAAe+B,gBAAf,CAAiC,IAAGS,kBAAmB,qBAAvD,CAAzB;;AACA,SAAK,IAAIR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGS,gBAAgB,CAACnD,MAArC,EAA6C0C,CAAC,EAA9C,EAAkD;AAC9C;AACA;AACA;AACA;AACAS,MAAAA,gBAAgB,CAACT,CAAD,CAAhB,CAAoBJ,MAApB;AACH;;AACD,UAAMc,iBAAiB,GAAG,KAAK1C,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAA1B,CAbuB,CAcvB;AACA;AACA;AACA;;;AACAO,IAAAA,iBAAiB,CAACC,KAAlB,CAAwBC,UAAxB,GAAqC,QAArC,CAlBuB,CAmBvB;AACA;;AACAF,IAAAA,iBAAiB,CAACG,SAAlB,CAA4BC,GAA5B,CAAgCN,kBAAhC;AACAE,IAAAA,iBAAiB,CAACG,SAAlB,CAA4BC,GAA5B,CAAgC,qBAAhC,EAtBuB,CAuBvB;;AACA,QAAI,KAAK7C,SAAL,IAAkB,CAAC,KAAKA,SAAL,CAAe8C,SAAtC,EAAiD;AAC7CL,MAAAA,iBAAiB,CAACzD,YAAlB,CAA+B,UAA/B,EAA2C,QAA3C;AACH;;AACD,SAAKe,SAAL,CAAegD,IAAf,CAAoBV,WAApB,CAAgCI,iBAAhC;;AACA,SAAKtC,kBAAL,GAA0BsC,iBAA1B;AACH;AACD;;;AACAT,EAAAA,iCAAiC,CAACgB,OAAD,EAAU;AACvC;AACA,UAAMC,oBAAoB,GAAGtE,mBAAmB,CAACqE,OAAD,EAAU,kBAAV,CAAnB,CAAiDrF,MAAjD,CAAwDc,EAAE,IAAIA,EAAE,CAACyE,OAAH,CAAWxD,yBAAX,KAAyC,CAAvG,CAA7B;AACAsD,IAAAA,OAAO,CAAChE,YAAR,CAAqB,kBAArB,EAAyCiE,oBAAoB,CAAChE,IAArB,CAA0B,GAA1B,CAAzC;AACH;AACD;AACJ;AACA;AACA;;;AACIkC,EAAAA,oBAAoB,CAAC6B,OAAD,EAAUtC,GAAV,EAAe;AAC/B,UAAMa,iBAAiB,GAAG,KAAKtB,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,CAA1B,CAD+B,CAE/B;AACA;;;AACApC,IAAAA,mBAAmB,CAAC0E,OAAD,EAAU,kBAAV,EAA8BzB,iBAAiB,CAACT,cAAlB,CAAiCrC,EAA/D,CAAnB;AACAuE,IAAAA,OAAO,CAAChE,YAAR,CAAqBW,8BAArB,EAAqD,KAAKS,GAA1D;AACAmB,IAAAA,iBAAiB,CAACR,cAAlB;AACH;AACD;AACJ;AACA;AACA;;;AACIO,EAAAA,uBAAuB,CAAC0B,OAAD,EAAUtC,GAAV,EAAe;AAClC,UAAMa,iBAAiB,GAAG,KAAKtB,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,CAA1B;;AACAa,IAAAA,iBAAiB,CAACR,cAAlB;AACA7B,IAAAA,sBAAsB,CAAC8D,OAAD,EAAU,kBAAV,EAA8BzB,iBAAiB,CAACT,cAAlB,CAAiCrC,EAA/D,CAAtB;AACAuE,IAAAA,OAAO,CAAC1D,eAAR,CAAwBK,8BAAxB;AACH;AACD;;;AACAuB,EAAAA,4BAA4B,CAAC8B,OAAD,EAAUtC,GAAV,EAAe;AACvC,UAAMyC,YAAY,GAAGxE,mBAAmB,CAACqE,OAAD,EAAU,kBAAV,CAAxC;;AACA,UAAMzB,iBAAiB,GAAG,KAAKtB,gBAAL,CAAsBuB,GAAtB,CAA0Bd,GAA1B,CAA1B;;AACA,UAAM0C,SAAS,GAAG7B,iBAAiB,IAAIA,iBAAiB,CAACT,cAAlB,CAAiCrC,EAAxE;AACA,WAAO,CAAC,CAAC2E,SAAF,IAAeD,YAAY,CAACD,OAAb,CAAqBE,SAArB,KAAmC,CAAC,CAA1D;AACH;AACD;;;AACA3C,EAAAA,eAAe,CAACuC,OAAD,EAAUzC,OAAV,EAAmB;AAC9B,QAAI,CAAC,KAAKc,cAAL,CAAoB2B,OAApB,CAAL,EAAmC;AAC/B,aAAO,KAAP;AACH;;AACD,QAAIzC,OAAO,IAAI,OAAOA,OAAP,KAAmB,QAAlC,EAA4C;AACxC;AACA;AACA;AACA,aAAO,IAAP;AACH;;AACD,UAAM8C,cAAc,GAAG9C,OAAO,IAAI,IAAX,GAAkB,EAAlB,GAAwB,GAAEA,OAAQ,EAAX,CAAazB,IAAb,EAA9C;AACA,UAAMwE,SAAS,GAAGN,OAAO,CAACzD,YAAR,CAAqB,YAArB,CAAlB,CAX8B,CAY9B;AACA;;AACA,WAAO8D,cAAc,GAAG,CAACC,SAAD,IAAcA,SAAS,CAACxE,IAAV,OAAqBuE,cAAtC,GAAuD,KAA5E;AACH;AACD;;;AACAhC,EAAAA,cAAc,CAAC2B,OAAD,EAAU;AACpB,WAAOA,OAAO,CAACO,QAAR,KAAqB,KAAKxD,SAAL,CAAeyD,YAA3C;AACH;;AA3Ke;;AA6KpB3D,aAAa,CAAC4D,IAAd;AAAA,mBAA0G5D,aAA1G,EAAgGzE,EAAhG,UAAyID,QAAzI,GAAgGC,EAAhG,UAA8JW,EAAE,CAAC2H,QAAjK;AAAA;;AACA7D,aAAa,CAAC8D,KAAd,kBADgGvI,EAChG;AAAA,SAA8GyE,aAA9G;AAAA,WAA8GA,aAA9G;AAAA,cAAyI;AAAzI;;AACA;AAAA,qDAFgGzE,EAEhG,mBAA2FyE,aAA3F,EAAsH,CAAC;AAC3G+D,IAAAA,IAAI,EAAEvI,UADqG;AAE3GwI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFqG,GAAD,CAAtH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DJ,QAAAA,IAAI,EAAEtI,MADwD;AAE9DuI,QAAAA,IAAI,EAAE,CAAC1I,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEyI,MAAAA,IAAI,EAAE7H,EAAE,CAAC2H;AAAX,KAH2B,CAAP;AAGK,GAN/C;AAAA;AAOA;;;AACA,SAAS/C,MAAT,CAAgBJ,OAAhB,EAAyBC,IAAzB,EAA+B;AAC3B,SAAO,OAAOD,OAAP,KAAmB,QAAnB,GAA+B,GAAEC,IAAI,IAAI,EAAG,IAAGD,OAAQ,EAAvD,GAA2DA,OAAlE;AACH;AACD;;;AACA,SAASK,YAAT,CAAsBoC,OAAtB,EAA+B;AAC3B,MAAI,CAACA,OAAO,CAACvE,EAAb,EAAiB;AACbuE,IAAAA,OAAO,CAACvE,EAAR,GAAc,GAAEiB,yBAA0B,IAAGE,MAAM,EAAG,EAAtD;AACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMqE,cAAN,CAAqB;AACjBnE,EAAAA,WAAW,CAACoE,MAAD,EAAS;AAChB,SAAKA,MAAL,GAAcA,MAAd;AACA,SAAKC,gBAAL,GAAwB,CAAC,CAAzB;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,KAAL,GAAa,KAAb;AACA,SAAKC,gBAAL,GAAwB,IAAIlI,OAAJ,EAAxB;AACA,SAAKmI,sBAAL,GAA8BlI,YAAY,CAACmI,KAA3C;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,oBAAL,GAA4B,EAA5B;AACA,SAAKC,WAAL,GAAmB,KAAnB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,gBAAL,GAAyBC,IAAD,IAAUA,IAAI,CAACC,QAAvC,CAdgB,CAehB;;;AACA,SAAKC,eAAL,GAAuB,EAAvB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,MAAL,GAAc,IAAI5I,OAAJ,EAAd;AACA;;AACA,SAAK6I,MAAL,GAAc,IAAI7I,OAAJ,EAAd,CAvBgB,CAwBhB;AACA;AACA;;AACA,QAAI8H,MAAM,YAAY3I,SAAtB,EAAiC;AAC7B2I,MAAAA,MAAM,CAACgB,OAAP,CAAeC,SAAf,CAA0BC,QAAD,IAAc;AACnC,YAAI,KAAKhB,WAAT,EAAsB;AAClB,gBAAMiB,SAAS,GAAGD,QAAQ,CAACE,OAAT,EAAlB;AACA,gBAAMC,QAAQ,GAAGF,SAAS,CAACnC,OAAV,CAAkB,KAAKkB,WAAvB,CAAjB;;AACA,cAAImB,QAAQ,GAAG,CAAC,CAAZ,IAAiBA,QAAQ,KAAK,KAAKpB,gBAAvC,EAAyD;AACrD,iBAAKA,gBAAL,GAAwBoB,QAAxB;AACH;AACJ;AACJ,OARD;AASH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,aAAa,CAACC,SAAD,EAAY;AACrB,SAAKb,gBAAL,GAAwBa,SAAxB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,QAAQ,CAACC,UAAU,GAAG,IAAd,EAAoB;AACxB,SAAKtB,KAAL,GAAasB,UAAb;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,uBAAuB,CAACC,OAAO,GAAG,IAAX,EAAiB;AACpC,SAAKpB,SAAL,GAAiBoB,OAAjB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,yBAAyB,CAACC,SAAD,EAAY;AACjC,SAAKC,WAAL,GAAmBD,SAAnB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIE,EAAAA,uBAAuB,CAACC,IAAD,EAAO;AAC1B,SAAKxB,oBAAL,GAA4BwB,IAA5B;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,aAAa,CAACC,gBAAgB,GAAG,GAApB,EAAyB;AAClC,QAAI,CAAC,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KACA,KAAKnC,MAAL,CAAY7E,MADZ,IAEA,KAAK6E,MAAL,CAAYtF,IAAZ,CAAiBiG,IAAI,IAAI,OAAOA,IAAI,CAACyB,QAAZ,KAAyB,UAAlD,CAFJ,EAEmE;AAC/D,YAAMC,KAAK,CAAC,8EAAD,CAAX;AACH;;AACD,SAAKhC,sBAAL,CAA4BiC,WAA5B,GANkC,CAOlC;AACA;AACA;;;AACA,SAAKjC,sBAAL,GAA8B,KAAKD,gBAAL,CACzBmC,IADyB,CACpBhJ,GAAG,CAACiJ,MAAM,IAAI,KAAK3B,eAAL,CAAqBhG,IAArB,CAA0B2H,MAA1B,CAAX,CADiB,EAC8BhJ,YAAY,CAAC0I,gBAAD,CAD1C,EAC8DzI,MAAM,CAAC,MAAM,KAAKoH,eAAL,CAAqB1F,MAArB,GAA8B,CAArC,CADpE,EAC6GzB,GAAG,CAAC,MAAM,KAAKmH,eAAL,CAAqB9F,IAArB,CAA0B,EAA1B,CAAP,CADhH,EAEzBkG,SAFyB,CAEfwB,WAAW,IAAI;AAC1B,YAAMC,KAAK,GAAG,KAAKC,cAAL,EAAd,CAD0B,CAE1B;AACA;;;AACA,WAAK,IAAI9E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6E,KAAK,CAACvH,MAAN,GAAe,CAAnC,EAAsC0C,CAAC,EAAvC,EAA2C;AACvC,cAAM+E,KAAK,GAAG,CAAC,KAAK3C,gBAAL,GAAwBpC,CAAzB,IAA8B6E,KAAK,CAACvH,MAAlD;AACA,cAAMwF,IAAI,GAAG+B,KAAK,CAACE,KAAD,CAAlB;;AACA,YAAI,CAAC,KAAKlC,gBAAL,CAAsBC,IAAtB,CAAD,IACAA,IAAI,CAACyB,QAAL,GAAgBS,WAAhB,GAA8BjI,IAA9B,GAAqCoE,OAArC,CAA6CyD,WAA7C,MAA8D,CADlE,EACqE;AACjE,eAAKK,aAAL,CAAmBF,KAAnB;AACA;AACH;AACJ;;AACD,WAAK/B,eAAL,GAAuB,EAAvB;AACH,KAhB6B,CAA9B;AAiBA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIkC,EAAAA,cAAc,CAACpB,OAAO,GAAG,IAAX,EAAiB;AAC3B,SAAKlB,WAAL,GAAmBkB,OAAnB;AACA,WAAO,IAAP;AACH;;AACDmB,EAAAA,aAAa,CAACnC,IAAD,EAAO;AAChB,UAAMqC,kBAAkB,GAAG,KAAK9C,WAAhC;AACA,SAAK+C,gBAAL,CAAsBtC,IAAtB;;AACA,QAAI,KAAKT,WAAL,KAAqB8C,kBAAzB,EAA6C;AACzC,WAAKjC,MAAL,CAAYmC,IAAZ,CAAiB,KAAKjD,gBAAtB;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIkD,EAAAA,SAAS,CAACC,KAAD,EAAQ;AACb,UAAMC,OAAO,GAAGD,KAAK,CAACC,OAAtB;AACA,UAAMC,SAAS,GAAG,CAAC,QAAD,EAAW,SAAX,EAAsB,SAAtB,EAAiC,UAAjC,CAAlB;AACA,UAAMC,iBAAiB,GAAGD,SAAS,CAACE,KAAV,CAAgBC,QAAQ,IAAI;AAClD,aAAO,CAACL,KAAK,CAACK,QAAD,CAAN,IAAoB,KAAKjD,oBAAL,CAA0BxB,OAA1B,CAAkCyE,QAAlC,IAA8C,CAAC,CAA1E;AACH,KAFyB,CAA1B;;AAGA,YAAQJ,OAAR;AACI,WAAKpK,GAAL;AACI,aAAK6H,MAAL,CAAYoC,IAAZ;AACA;;AACJ,WAAKlK,UAAL;AACI,YAAI,KAAKuH,SAAL,IAAkBgD,iBAAtB,EAAyC;AACrC,eAAKG,iBAAL;AACA;AACH,SAHD,MAIK;AACD;AACH;;AACL,WAAK3K,QAAL;AACI,YAAI,KAAKwH,SAAL,IAAkBgD,iBAAtB,EAAyC;AACrC,eAAKI,qBAAL;AACA;AACH,SAHD,MAIK;AACD;AACH;;AACL,WAAK7K,WAAL;AACI,YAAI,KAAKgJ,WAAL,IAAoByB,iBAAxB,EAA2C;AACvC,eAAKzB,WAAL,KAAqB,KAArB,GAA6B,KAAK6B,qBAAL,EAA7B,GAA4D,KAAKD,iBAAL,EAA5D;AACA;AACH,SAHD,MAIK;AACD;AACH;;AACL,WAAK7K,UAAL;AACI,YAAI,KAAKiJ,WAAL,IAAoByB,iBAAxB,EAA2C;AACvC,eAAKzB,WAAL,KAAqB,KAArB,GAA6B,KAAK4B,iBAAL,EAA7B,GAAwD,KAAKC,qBAAL,EAAxD;AACA;AACH,SAHD,MAIK;AACD;AACH;;AACL,WAAK/K,IAAL;AACI,YAAI,KAAK6H,WAAL,IAAoB8C,iBAAxB,EAA2C;AACvC,eAAKK,kBAAL;AACA;AACH,SAHD,MAIK;AACD;AACH;;AACL,WAAKjL,GAAL;AACI,YAAI,KAAK8H,WAAL,IAAoB8C,iBAAxB,EAA2C;AACvC,eAAKM,iBAAL;AACA;AACH,SAHD,MAIK;AACD;AACH;;AACL;AACI,YAAIN,iBAAiB,IAAIjL,cAAc,CAAC8K,KAAD,EAAQ,UAAR,CAAvC,EAA4D;AACxD;AACA;AACA,cAAIA,KAAK,CAAC5G,GAAN,IAAa4G,KAAK,CAAC5G,GAAN,CAAUrB,MAAV,KAAqB,CAAtC,EAAyC;AACrC,iBAAKiF,gBAAL,CAAsB8C,IAAtB,CAA2BE,KAAK,CAAC5G,GAAN,CAAUsH,iBAAV,EAA3B;AACH,WAFD,MAGK,IAAKT,OAAO,IAAI9K,CAAX,IAAgB8K,OAAO,IAAI7K,CAA5B,IAAmC6K,OAAO,IAAI5K,IAAX,IAAmB4K,OAAO,IAAI3K,IAArE,EAA4E;AAC7E,iBAAK0H,gBAAL,CAAsB8C,IAAtB,CAA2Ba,MAAM,CAACC,YAAP,CAAoBX,OAApB,CAA3B;AACH;AACJ,SAVL,CAWI;AACA;;;AACA;AAjER;;AAmEA,SAAKxC,eAAL,GAAuB,EAAvB;AACAuC,IAAAA,KAAK,CAACa,cAAN;AACH;AACD;;;AACmB,MAAfC,eAAe,GAAG;AAClB,WAAO,KAAKjE,gBAAZ;AACH;AACD;;;AACc,MAAVkE,UAAU,GAAG;AACb,WAAO,KAAKjE,WAAZ;AACH;AACD;;;AACAkE,EAAAA,QAAQ,GAAG;AACP,WAAO,KAAKvD,eAAL,CAAqB1F,MAArB,GAA8B,CAArC;AACH;AACD;;;AACAyI,EAAAA,kBAAkB,GAAG;AACjB,SAAKS,qBAAL,CAA2B,CAA3B,EAA8B,CAA9B;AACH;AACD;;;AACAR,EAAAA,iBAAiB,GAAG;AAChB,SAAKQ,qBAAL,CAA2B,KAAKrE,MAAL,CAAY7E,MAAZ,GAAqB,CAAhD,EAAmD,CAAC,CAApD;AACH;AACD;;;AACAuI,EAAAA,iBAAiB,GAAG;AAChB,SAAKzD,gBAAL,GAAwB,CAAxB,GAA4B,KAAK2D,kBAAL,EAA5B,GAAwD,KAAKU,qBAAL,CAA2B,CAA3B,CAAxD;AACH;AACD;;;AACAX,EAAAA,qBAAqB,GAAG;AACpB,SAAK1D,gBAAL,GAAwB,CAAxB,IAA6B,KAAKE,KAAlC,GACM,KAAK0D,iBAAL,EADN,GAEM,KAAKS,qBAAL,CAA2B,CAAC,CAA5B,CAFN;AAGH;;AACDrB,EAAAA,gBAAgB,CAACtC,IAAD,EAAO;AACnB,UAAMQ,SAAS,GAAG,KAAKwB,cAAL,EAAlB;;AACA,UAAMC,KAAK,GAAG,OAAOjC,IAAP,KAAgB,QAAhB,GAA2BA,IAA3B,GAAkCQ,SAAS,CAACnC,OAAV,CAAkB2B,IAAlB,CAAhD;AACA,UAAMwD,UAAU,GAAGhD,SAAS,CAACyB,KAAD,CAA5B,CAHmB,CAInB;;AACA,SAAK1C,WAAL,GAAmBiE,UAAU,IAAI,IAAd,GAAqB,IAArB,GAA4BA,UAA/C;AACA,SAAKlE,gBAAL,GAAwB2C,KAAxB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACI0B,EAAAA,qBAAqB,CAACC,KAAD,EAAQ;AACzB,SAAKpE,KAAL,GAAa,KAAKqE,oBAAL,CAA0BD,KAA1B,CAAb,GAAgD,KAAKE,uBAAL,CAA6BF,KAA7B,CAAhD;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,oBAAoB,CAACD,KAAD,EAAQ;AACxB,UAAM7B,KAAK,GAAG,KAAKC,cAAL,EAAd;;AACA,SAAK,IAAI9E,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI6E,KAAK,CAACvH,MAA3B,EAAmC0C,CAAC,EAApC,EAAwC;AACpC,YAAM+E,KAAK,GAAG,CAAC,KAAK3C,gBAAL,GAAwBsE,KAAK,GAAG1G,CAAhC,GAAoC6E,KAAK,CAACvH,MAA3C,IAAqDuH,KAAK,CAACvH,MAAzE;AACA,YAAMwF,IAAI,GAAG+B,KAAK,CAACE,KAAD,CAAlB;;AACA,UAAI,CAAC,KAAKlC,gBAAL,CAAsBC,IAAtB,CAAL,EAAkC;AAC9B,aAAKmC,aAAL,CAAmBF,KAAnB;AACA;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACI6B,EAAAA,uBAAuB,CAACF,KAAD,EAAQ;AAC3B,SAAKF,qBAAL,CAA2B,KAAKpE,gBAAL,GAAwBsE,KAAnD,EAA0DA,KAA1D;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIF,EAAAA,qBAAqB,CAACzB,KAAD,EAAQ8B,aAAR,EAAuB;AACxC,UAAMhC,KAAK,GAAG,KAAKC,cAAL,EAAd;;AACA,QAAI,CAACD,KAAK,CAACE,KAAD,CAAV,EAAmB;AACf;AACH;;AACD,WAAO,KAAKlC,gBAAL,CAAsBgC,KAAK,CAACE,KAAD,CAA3B,CAAP,EAA4C;AACxCA,MAAAA,KAAK,IAAI8B,aAAT;;AACA,UAAI,CAAChC,KAAK,CAACE,KAAD,CAAV,EAAmB;AACf;AACH;AACJ;;AACD,SAAKE,aAAL,CAAmBF,KAAnB;AACH;AACD;;;AACAD,EAAAA,cAAc,GAAG;AACb,WAAO,KAAK3C,MAAL,YAAuB3I,SAAvB,GAAmC,KAAK2I,MAAL,CAAYoB,OAAZ,EAAnC,GAA2D,KAAKpB,MAAvE;AACH;;AA/SgB;AAkTrB;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM2E,0BAAN,SAAyC5E,cAAzC,CAAwD;AACpD+C,EAAAA,aAAa,CAACF,KAAD,EAAQ;AACjB,QAAI,KAAKuB,UAAT,EAAqB;AACjB,WAAKA,UAAL,CAAgBS,iBAAhB;AACH;;AACD,UAAM9B,aAAN,CAAoBF,KAApB;;AACA,QAAI,KAAKuB,UAAT,EAAqB;AACjB,WAAKA,UAAL,CAAgBU,eAAhB;AACH;AACJ;;AATmD;AAYxD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,eAAN,SAA8B/E,cAA9B,CAA6C;AACzCnE,EAAAA,WAAW,GAAG;AACV,UAAM,GAAGmJ,SAAT;AACA,SAAKC,OAAL,GAAe,SAAf;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,cAAc,CAACC,MAAD,EAAS;AACnB,SAAKF,OAAL,GAAeE,MAAf;AACA,WAAO,IAAP;AACH;;AACDpC,EAAAA,aAAa,CAACnC,IAAD,EAAO;AAChB,UAAMmC,aAAN,CAAoBnC,IAApB;;AACA,QAAI,KAAKwD,UAAT,EAAqB;AACjB,WAAKA,UAAL,CAAgBgB,KAAhB,CAAsB,KAAKH,OAA3B;AACH;AACJ;;AAlBwC;AAqB7C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMI,iBAAN,CAAwB;AACpBxJ,EAAAA,WAAW,GAAG;AACV;AACR;AACA;AACQ,SAAKyJ,gBAAL,GAAwB,KAAxB;AACH;;AANmB,C,CAQxB;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMC,oBAAN,CAA2B;AACvB1J,EAAAA,WAAW,CAACE,SAAD,EAAY;AACnB,SAAKA,SAAL,GAAiBA,SAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIyJ,EAAAA,UAAU,CAACzG,OAAD,EAAU;AAChB;AACA;AACA,WAAOA,OAAO,CAAC0G,YAAR,CAAqB,UAArB,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,SAAS,CAAC3G,OAAD,EAAU;AACf,WAAO4G,WAAW,CAAC5G,OAAD,CAAX,IAAwB6G,gBAAgB,CAAC7G,OAAD,CAAhB,CAA0BL,UAA1B,KAAyC,SAAxE;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACImH,EAAAA,UAAU,CAAC9G,OAAD,EAAU;AAChB;AACA,QAAI,CAAC,KAAKhD,SAAL,CAAe8C,SAApB,EAA+B;AAC3B,aAAO,KAAP;AACH;;AACD,UAAMiH,YAAY,GAAGC,eAAe,CAACC,SAAS,CAACjH,OAAD,CAAV,CAApC;;AACA,QAAI+G,YAAJ,EAAkB;AACd;AACA,UAAIG,gBAAgB,CAACH,YAAD,CAAhB,KAAmC,CAAC,CAAxC,EAA2C;AACvC,eAAO,KAAP;AACH,OAJa,CAKd;;;AACA,UAAI,CAAC,KAAKJ,SAAL,CAAeI,YAAf,CAAL,EAAmC;AAC/B,eAAO,KAAP;AACH;AACJ;;AACD,QAAII,QAAQ,GAAGnH,OAAO,CAACmH,QAAR,CAAiBC,WAAjB,EAAf;AACA,QAAIC,aAAa,GAAGH,gBAAgB,CAAClH,OAAD,CAApC;;AACA,QAAIA,OAAO,CAAC0G,YAAR,CAAqB,iBAArB,CAAJ,EAA6C;AACzC,aAAOW,aAAa,KAAK,CAAC,CAA1B;AACH;;AACD,QAAIF,QAAQ,KAAK,QAAb,IAAyBA,QAAQ,KAAK,QAA1C,EAAoD;AAChD;AACA;AACA;AACA,aAAO,KAAP;AACH,KA1Be,CA2BhB;;;AACA,QAAI,KAAKnK,SAAL,CAAesK,MAAf,IAAyB,KAAKtK,SAAL,CAAeuK,GAAxC,IAA+C,CAACC,wBAAwB,CAACxH,OAAD,CAA5E,EAAuF;AACnF,aAAO,KAAP;AACH;;AACD,QAAImH,QAAQ,KAAK,OAAjB,EAA0B;AACtB;AACA;AACA,UAAI,CAACnH,OAAO,CAAC0G,YAAR,CAAqB,UAArB,CAAL,EAAuC;AACnC,eAAO,KAAP;AACH,OALqB,CAMtB;AACA;;;AACA,aAAOW,aAAa,KAAK,CAAC,CAA1B;AACH;;AACD,QAAIF,QAAQ,KAAK,OAAjB,EAA0B;AACtB;AACA;AACA;AACA;AACA,UAAIE,aAAa,KAAK,CAAC,CAAvB,EAA0B;AACtB,eAAO,KAAP;AACH,OAPqB,CAQtB;AACA;;;AACA,UAAIA,aAAa,KAAK,IAAtB,EAA4B;AACxB,eAAO,IAAP;AACH,OAZqB,CAatB;AACA;AACA;;;AACA,aAAO,KAAKrK,SAAL,CAAeyK,OAAf,IAA0BzH,OAAO,CAAC0G,YAAR,CAAqB,UAArB,CAAjC;AACH;;AACD,WAAO1G,OAAO,CAAC0H,QAAR,IAAoB,CAA3B;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,WAAW,CAAC3H,OAAD,EAAU4H,MAAV,EAAkB;AACzB;AACA;AACA,WAAQC,sBAAsB,CAAC7H,OAAD,CAAtB,IACJ,CAAC,KAAKyG,UAAL,CAAgBzG,OAAhB,CADG,KAEH4H,MAAM,EAAErB,gBAAR,IAA4B,KAAKI,SAAL,CAAe3G,OAAf,CAFzB,CAAR;AAGH;;AA3GsB;;AA6G3BwG,oBAAoB,CAAC/F,IAArB;AAAA,mBAAiH+F,oBAAjH,EAtgBgGpO,EAsgBhG,UAAuJW,EAAE,CAAC2H,QAA1J;AAAA;;AACA8F,oBAAoB,CAAC7F,KAArB,kBAvgBgGvI,EAugBhG;AAAA,SAAqHoO,oBAArH;AAAA,WAAqHA,oBAArH;AAAA,cAAuJ;AAAvJ;;AACA;AAAA,qDAxgBgGpO,EAwgBhG,mBAA2FoO,oBAA3F,EAA6H,CAAC;AAClH5F,IAAAA,IAAI,EAAEvI,UAD4G;AAElHwI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF4G,GAAD,CAA7H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAE7H,EAAE,CAAC2H;AAAX,KAAD,CAAP;AAAiC,GAH3E;AAAA;AAIA;AACA;AACA;AACA;AACA;;;AACA,SAASsG,eAAT,CAAyBc,MAAzB,EAAiC;AAC7B,MAAI;AACA,WAAOA,MAAM,CAACf,YAAd;AACH,GAFD,CAGA,MAAM;AACF,WAAO,IAAP;AACH;AACJ;AACD;;;AACA,SAASH,WAAT,CAAqB5G,OAArB,EAA8B;AAC1B;AACA;AACA,SAAO,CAAC,EAAEA,OAAO,CAAC+H,WAAR,IACN/H,OAAO,CAACgI,YADF,IAEL,OAAOhI,OAAO,CAACiI,cAAf,KAAkC,UAAlC,IAAgDjI,OAAO,CAACiI,cAAR,GAAyB5L,MAFtE,CAAR;AAGH;AACD;;;AACA,SAAS6L,mBAAT,CAA6BlI,OAA7B,EAAsC;AAClC,MAAImH,QAAQ,GAAGnH,OAAO,CAACmH,QAAR,CAAiBC,WAAjB,EAAf;AACA,SAAQD,QAAQ,KAAK,OAAb,IACJA,QAAQ,KAAK,QADT,IAEJA,QAAQ,KAAK,QAFT,IAGJA,QAAQ,KAAK,UAHjB;AAIH;AACD;;;AACA,SAASgB,aAAT,CAAuBnI,OAAvB,EAAgC;AAC5B,SAAOoI,cAAc,CAACpI,OAAD,CAAd,IAA2BA,OAAO,CAACY,IAAR,IAAgB,QAAlD;AACH;AACD;;;AACA,SAASyH,gBAAT,CAA0BrI,OAA1B,EAAmC;AAC/B,SAAOsI,eAAe,CAACtI,OAAD,CAAf,IAA4BA,OAAO,CAAC0G,YAAR,CAAqB,MAArB,CAAnC;AACH;AACD;;;AACA,SAAS0B,cAAT,CAAwBpI,OAAxB,EAAiC;AAC7B,SAAOA,OAAO,CAACmH,QAAR,CAAiBC,WAAjB,MAAkC,OAAzC;AACH;AACD;;;AACA,SAASkB,eAAT,CAAyBtI,OAAzB,EAAkC;AAC9B,SAAOA,OAAO,CAACmH,QAAR,CAAiBC,WAAjB,MAAkC,GAAzC;AACH;AACD;;;AACA,SAASmB,gBAAT,CAA0BvI,OAA1B,EAAmC;AAC/B,MAAI,CAACA,OAAO,CAAC0G,YAAR,CAAqB,UAArB,CAAD,IAAqC1G,OAAO,CAAC0H,QAAR,KAAqB3G,SAA9D,EAAyE;AACrE,WAAO,KAAP;AACH;;AACD,MAAI2G,QAAQ,GAAG1H,OAAO,CAACzD,YAAR,CAAqB,UAArB,CAAf;AACA,SAAO,CAAC,EAAEmL,QAAQ,IAAI,CAACc,KAAK,CAACC,QAAQ,CAACf,QAAD,EAAW,EAAX,CAAT,CAApB,CAAR;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASR,gBAAT,CAA0BlH,OAA1B,EAAmC;AAC/B,MAAI,CAACuI,gBAAgB,CAACvI,OAAD,CAArB,EAAgC;AAC5B,WAAO,IAAP;AACH,GAH8B,CAI/B;;;AACA,QAAM0H,QAAQ,GAAGe,QAAQ,CAACzI,OAAO,CAACzD,YAAR,CAAqB,UAArB,KAAoC,EAArC,EAAyC,EAAzC,CAAzB;AACA,SAAOiM,KAAK,CAACd,QAAD,CAAL,GAAkB,CAAC,CAAnB,GAAuBA,QAA9B;AACH;AACD;;;AACA,SAASF,wBAAT,CAAkCxH,OAAlC,EAA2C;AACvC,MAAImH,QAAQ,GAAGnH,OAAO,CAACmH,QAAR,CAAiBC,WAAjB,EAAf;AACA,MAAIsB,SAAS,GAAGvB,QAAQ,KAAK,OAAb,IAAwBnH,OAAO,CAACY,IAAhD;AACA,SAAQ8H,SAAS,KAAK,MAAd,IACJA,SAAS,KAAK,UADV,IAEJvB,QAAQ,KAAK,QAFT,IAGJA,QAAQ,KAAK,UAHjB;AAIH;AACD;AACA;AACA;AACA;;;AACA,SAASU,sBAAT,CAAgC7H,OAAhC,EAAyC;AACrC;AACA,MAAImI,aAAa,CAACnI,OAAD,CAAjB,EAA4B;AACxB,WAAO,KAAP;AACH;;AACD,SAAQkI,mBAAmB,CAAClI,OAAD,CAAnB,IACJqI,gBAAgB,CAACrI,OAAD,CADZ,IAEJA,OAAO,CAAC0G,YAAR,CAAqB,iBAArB,CAFI,IAGJ6B,gBAAgB,CAACvI,OAAD,CAHpB;AAIH;AACD;;;AACA,SAASiH,SAAT,CAAmB0B,IAAnB,EAAyB;AACrB;AACA,SAAQA,IAAI,CAACC,aAAL,IAAsBD,IAAI,CAACC,aAAL,CAAmBC,WAA1C,IAA0Df,MAAjE;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgB,SAAN,CAAgB;AACZhM,EAAAA,WAAW,CAACiM,QAAD,EAAWC,QAAX,EAAqBC,OAArB,EAA8BlM,SAA9B,EAAyCmM,YAAY,GAAG,KAAxD,EAA+D;AACtE,SAAKH,QAAL,GAAgBA,QAAhB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKlM,SAAL,GAAiBA,SAAjB;AACA,SAAKoM,YAAL,GAAoB,KAApB,CALsE,CAMtE;;AACA,SAAKC,mBAAL,GAA2B,MAAM,KAAKC,wBAAL,EAAjC;;AACA,SAAKC,iBAAL,GAAyB,MAAM,KAAKC,yBAAL,EAA/B;;AACA,SAAKC,QAAL,GAAgB,IAAhB;;AACA,QAAI,CAACN,YAAL,EAAmB;AACf,WAAKO,aAAL;AACH;AACJ;AACD;;;AACW,MAAP5G,OAAO,GAAG;AACV,WAAO,KAAK2G,QAAZ;AACH;;AACU,MAAP3G,OAAO,CAAC6G,KAAD,EAAQ;AACf,SAAKF,QAAL,GAAgBE,KAAhB;;AACA,QAAI,KAAKC,YAAL,IAAqB,KAAKC,UAA9B,EAA0C;AACtC,WAAKC,qBAAL,CAA2BH,KAA3B,EAAkC,KAAKC,YAAvC;;AACA,WAAKE,qBAAL,CAA2BH,KAA3B,EAAkC,KAAKE,UAAvC;AACH;AACJ;AACD;;;AACAE,EAAAA,OAAO,GAAG;AACN,UAAMC,WAAW,GAAG,KAAKJ,YAAzB;AACA,UAAMK,SAAS,GAAG,KAAKJ,UAAvB;;AACA,QAAIG,WAAJ,EAAiB;AACbA,MAAAA,WAAW,CAACE,mBAAZ,CAAgC,OAAhC,EAAyC,KAAKb,mBAA9C;AACAW,MAAAA,WAAW,CAACpL,MAAZ;AACH;;AACD,QAAIqL,SAAJ,EAAe;AACXA,MAAAA,SAAS,CAACC,mBAAV,CAA8B,OAA9B,EAAuC,KAAKX,iBAA5C;AACAU,MAAAA,SAAS,CAACrL,MAAV;AACH;;AACD,SAAKgL,YAAL,GAAoB,KAAKC,UAAL,GAAkB,IAAtC;AACA,SAAKT,YAAL,GAAoB,KAApB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIM,EAAAA,aAAa,GAAG;AACZ;AACA,QAAI,KAAKN,YAAT,EAAuB;AACnB,aAAO,IAAP;AACH;;AACD,SAAKF,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;AACjC,UAAI,CAAC,KAAKP,YAAV,EAAwB;AACpB,aAAKA,YAAL,GAAoB,KAAKQ,aAAL,EAApB;;AACA,aAAKR,YAAL,CAAkBS,gBAAlB,CAAmC,OAAnC,EAA4C,KAAKhB,mBAAjD;AACH;;AACD,UAAI,CAAC,KAAKQ,UAAV,EAAsB;AAClB,aAAKA,UAAL,GAAkB,KAAKO,aAAL,EAAlB;;AACA,aAAKP,UAAL,CAAgBQ,gBAAhB,CAAiC,OAAjC,EAA0C,KAAKd,iBAA/C;AACH;AACJ,KATD;;AAUA,QAAI,KAAKP,QAAL,CAAcsB,UAAlB,EAA8B;AAC1B,WAAKtB,QAAL,CAAcsB,UAAd,CAAyBC,YAAzB,CAAsC,KAAKX,YAA3C,EAAyD,KAAKZ,QAA9D;;AACA,WAAKA,QAAL,CAAcsB,UAAd,CAAyBC,YAAzB,CAAsC,KAAKV,UAA3C,EAAuD,KAAKb,QAAL,CAAcwB,WAArE;;AACA,WAAKpB,YAAL,GAAoB,IAApB;AACH;;AACD,WAAO,KAAKA,YAAZ;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIqB,EAAAA,4BAA4B,CAACC,OAAD,EAAU;AAClC,WAAO,IAAIC,OAAJ,CAAYC,OAAO,IAAI;AAC1B,WAAKC,gBAAL,CAAsB,MAAMD,OAAO,CAAC,KAAKE,mBAAL,CAAyBJ,OAAzB,CAAD,CAAnC;AACH,KAFM,CAAP;AAGH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIK,EAAAA,kCAAkC,CAACL,OAAD,EAAU;AACxC,WAAO,IAAIC,OAAJ,CAAYC,OAAO,IAAI;AAC1B,WAAKC,gBAAL,CAAsB,MAAMD,OAAO,CAAC,KAAKpB,yBAAL,CAA+BkB,OAA/B,CAAD,CAAnC;AACH,KAFM,CAAP;AAGH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIM,EAAAA,iCAAiC,CAACN,OAAD,EAAU;AACvC,WAAO,IAAIC,OAAJ,CAAYC,OAAO,IAAI;AAC1B,WAAKC,gBAAL,CAAsB,MAAMD,OAAO,CAAC,KAAKtB,wBAAL,CAA8BoB,OAA9B,CAAD,CAAnC;AACH,KAFM,CAAP;AAGH;AACD;AACJ;AACA;AACA;AACA;;;AACIO,EAAAA,kBAAkB,CAACC,KAAD,EAAQ;AACtB;AACA,UAAMC,OAAO,GAAG,KAAKnC,QAAL,CAAcjK,gBAAd,CAAgC,qBAAoBmM,KAAM,KAA3B,GAAmC,kBAAiBA,KAAM,KAA1D,GAAkE,cAAaA,KAAM,GAApH,CAAhB;;AACA,QAAI,OAAO5H,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,WAAK,IAAItE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmM,OAAO,CAAC7O,MAA5B,EAAoC0C,CAAC,EAArC,EAAyC;AACrC;AACA,YAAImM,OAAO,CAACnM,CAAD,CAAP,CAAW2H,YAAX,CAAyB,aAAYuE,KAAM,EAA3C,CAAJ,EAAmD;AAC/CE,UAAAA,OAAO,CAACC,IAAR,CAAc,gDAA+CH,KAAM,KAAtD,GACR,sBAAqBA,KAAM,4BADnB,GAER,qCAFL,EAE2CC,OAAO,CAACnM,CAAD,CAFlD;AAGH,SAJD,MAKK,IAAImM,OAAO,CAACnM,CAAD,CAAP,CAAW2H,YAAX,CAAyB,oBAAmBuE,KAAM,EAAlD,CAAJ,EAA0D;AAC3DE,UAAAA,OAAO,CAACC,IAAR,CAAc,uDAAsDH,KAAM,KAA7D,GACR,sBAAqBA,KAAM,sCADnB,GAER,2BAFL,EAEiCC,OAAO,CAACnM,CAAD,CAFxC;AAGH;AACJ;AACJ;;AACD,QAAIkM,KAAK,IAAI,OAAb,EAAsB;AAClB,aAAOC,OAAO,CAAC7O,MAAR,GAAiB6O,OAAO,CAAC,CAAD,CAAxB,GAA8B,KAAKG,wBAAL,CAA8B,KAAKtC,QAAnC,CAArC;AACH;;AACD,WAAOmC,OAAO,CAAC7O,MAAR,GACD6O,OAAO,CAACA,OAAO,CAAC7O,MAAR,GAAiB,CAAlB,CADN,GAED,KAAKiP,uBAAL,CAA6B,KAAKvC,QAAlC,CAFN;AAGH;AACD;AACJ;AACA;AACA;;;AACI8B,EAAAA,mBAAmB,CAACJ,OAAD,EAAU;AACzB;AACA,UAAMc,iBAAiB,GAAG,KAAKxC,QAAL,CAAcyC,aAAd,CAA6B,uBAAD,GAA2B,mBAAvD,CAA1B;;AACA,QAAID,iBAAJ,EAAuB;AACnB;AACA,UAAI,CAAC,OAAOlI,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KACAkI,iBAAiB,CAAC7E,YAAlB,CAAgC,mBAAhC,CADJ,EACyD;AACrDyE,QAAAA,OAAO,CAACC,IAAR,CAAc,yDAAD,GACR,0DADQ,GAER,0BAFL,EAEgCG,iBAFhC;AAGH,OAPkB,CAQnB;AACA;;;AACA,UAAI,CAAC,OAAOlI,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KACA,CAAC,KAAK2F,QAAL,CAAcrB,WAAd,CAA0B4D,iBAA1B,CADL,EACmD;AAC/CJ,QAAAA,OAAO,CAACC,IAAR,CAAc,wDAAd,EAAuEG,iBAAvE;AACH;;AACD,UAAI,CAAC,KAAKvC,QAAL,CAAcrB,WAAd,CAA0B4D,iBAA1B,CAAL,EAAmD;AAC/C,cAAME,cAAc,GAAG,KAAKJ,wBAAL,CAA8BE,iBAA9B,CAAvB;;AACAE,QAAAA,cAAc,EAAEpF,KAAhB,CAAsBoE,OAAtB;AACA,eAAO,CAAC,CAACgB,cAAT;AACH;;AACDF,MAAAA,iBAAiB,CAAClF,KAAlB,CAAwBoE,OAAxB;AACA,aAAO,IAAP;AACH;;AACD,WAAO,KAAKlB,yBAAL,CAA+BkB,OAA/B,CAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIlB,EAAAA,yBAAyB,CAACkB,OAAD,EAAU;AAC/B,UAAMc,iBAAiB,GAAG,KAAKP,kBAAL,CAAwB,OAAxB,CAA1B;;AACA,QAAIO,iBAAJ,EAAuB;AACnBA,MAAAA,iBAAiB,CAAClF,KAAlB,CAAwBoE,OAAxB;AACH;;AACD,WAAO,CAAC,CAACc,iBAAT;AACH;AACD;AACJ;AACA;AACA;;;AACIlC,EAAAA,wBAAwB,CAACoB,OAAD,EAAU;AAC9B,UAAMc,iBAAiB,GAAG,KAAKP,kBAAL,CAAwB,KAAxB,CAA1B;;AACA,QAAIO,iBAAJ,EAAuB;AACnBA,MAAAA,iBAAiB,CAAClF,KAAlB,CAAwBoE,OAAxB;AACH;;AACD,WAAO,CAAC,CAACc,iBAAT;AACH;AACD;AACJ;AACA;;;AACIG,EAAAA,WAAW,GAAG;AACV,WAAO,KAAKvC,YAAZ;AACH;AACD;;;AACAkC,EAAAA,wBAAwB,CAACM,IAAD,EAAO;AAC3B,QAAI,KAAK3C,QAAL,CAAcrB,WAAd,CAA0BgE,IAA1B,KAAmC,KAAK3C,QAAL,CAAclC,UAAd,CAAyB6E,IAAzB,CAAvC,EAAuE;AACnE,aAAOA,IAAP;AACH;;AACD,UAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAtB;;AACA,SAAK,IAAI7M,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6M,QAAQ,CAACvP,MAA7B,EAAqC0C,CAAC,EAAtC,EAA0C;AACtC,YAAM8M,aAAa,GAAGD,QAAQ,CAAC7M,CAAD,CAAR,CAAYwB,QAAZ,KAAyB,KAAKxD,SAAL,CAAeyD,YAAxC,GAChB,KAAK6K,wBAAL,CAA8BO,QAAQ,CAAC7M,CAAD,CAAtC,CADgB,GAEhB,IAFN;;AAGA,UAAI8M,aAAJ,EAAmB;AACf,eAAOA,aAAP;AACH;AACJ;;AACD,WAAO,IAAP;AACH;AACD;;;AACAP,EAAAA,uBAAuB,CAACK,IAAD,EAAO;AAC1B,QAAI,KAAK3C,QAAL,CAAcrB,WAAd,CAA0BgE,IAA1B,KAAmC,KAAK3C,QAAL,CAAclC,UAAd,CAAyB6E,IAAzB,CAAvC,EAAuE;AACnE,aAAOA,IAAP;AACH,KAHyB,CAI1B;;;AACA,UAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAtB;;AACA,SAAK,IAAI7M,CAAC,GAAG6M,QAAQ,CAACvP,MAAT,GAAkB,CAA/B,EAAkC0C,CAAC,IAAI,CAAvC,EAA0CA,CAAC,EAA3C,EAA+C;AAC3C,YAAM8M,aAAa,GAAGD,QAAQ,CAAC7M,CAAD,CAAR,CAAYwB,QAAZ,KAAyB,KAAKxD,SAAL,CAAeyD,YAAxC,GAChB,KAAK8K,uBAAL,CAA6BM,QAAQ,CAAC7M,CAAD,CAArC,CADgB,GAEhB,IAFN;;AAGA,UAAI8M,aAAJ,EAAmB;AACf,eAAOA,aAAP;AACH;AACJ;;AACD,WAAO,IAAP;AACH;AACD;;;AACA1B,EAAAA,aAAa,GAAG;AACZ,UAAM2B,MAAM,GAAG,KAAK/O,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAAf;;AACA,SAAK2K,qBAAL,CAA2B,KAAKL,QAAhC,EAA0CsC,MAA1C;;AACAA,IAAAA,MAAM,CAAClM,SAAP,CAAiBC,GAAjB,CAAqB,qBAArB;AACAiM,IAAAA,MAAM,CAAClM,SAAP,CAAiBC,GAAjB,CAAqB,uBAArB;AACAiM,IAAAA,MAAM,CAAC9P,YAAP,CAAoB,aAApB,EAAmC,MAAnC;AACA,WAAO8P,MAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIjC,EAAAA,qBAAqB,CAACkC,SAAD,EAAYD,MAAZ,EAAoB;AACrC;AACA;AACAC,IAAAA,SAAS,GAAGD,MAAM,CAAC9P,YAAP,CAAoB,UAApB,EAAgC,GAAhC,CAAH,GAA0C8P,MAAM,CAACxP,eAAP,CAAuB,UAAvB,CAAnD;AACH;AACD;AACJ;AACA;AACA;;;AACI0P,EAAAA,aAAa,CAACnJ,OAAD,EAAU;AACnB,QAAI,KAAK8G,YAAL,IAAqB,KAAKC,UAA9B,EAA0C;AACtC,WAAKC,qBAAL,CAA2BhH,OAA3B,EAAoC,KAAK8G,YAAzC;;AACA,WAAKE,qBAAL,CAA2BhH,OAA3B,EAAoC,KAAK+G,UAAzC;AACH;AACJ;AACD;;;AACAgB,EAAAA,gBAAgB,CAACqB,EAAD,EAAK;AACjB,QAAI,KAAKhD,OAAL,CAAaiD,QAAjB,EAA2B;AACvBD,MAAAA,EAAE;AACL,KAFD,MAGK;AACD,WAAKhD,OAAL,CAAakD,QAAb,CAAsB1I,IAAtB,CAA2B5I,IAAI,CAAC,CAAD,CAA/B,EAAoCsH,SAApC,CAA8C8J,EAA9C;AACH;AACJ;;AApQW;AAsQhB;AACA;AACA;AACA;AACA;;;AACA,MAAMG,gBAAN,CAAuB;AACnBtP,EAAAA,WAAW,CAACkM,QAAD,EAAWC,OAAX,EAAoBlM,SAApB,EAA+B;AACtC,SAAKiM,QAAL,GAAgBA,QAAhB;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKlM,SAAL,GAAiBA,SAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIsP,EAAAA,MAAM,CAACrM,OAAD,EAAUsM,oBAAoB,GAAG,KAAjC,EAAwC;AAC1C,WAAO,IAAIxD,SAAJ,CAAc9I,OAAd,EAAuB,KAAKgJ,QAA5B,EAAsC,KAAKC,OAA3C,EAAoD,KAAKlM,SAAzD,EAAoEuP,oBAApE,CAAP;AACH;;AAfkB;;AAiBvBF,gBAAgB,CAAC3L,IAAjB;AAAA,mBAA6G2L,gBAA7G,EAv5BgGhU,EAu5BhG,UAA+IoO,oBAA/I,GAv5BgGpO,EAu5BhG,UAAgLA,EAAE,CAACmU,MAAnL,GAv5BgGnU,EAu5BhG,UAAsMD,QAAtM;AAAA;;AACAiU,gBAAgB,CAACzL,KAAjB,kBAx5BgGvI,EAw5BhG;AAAA,SAAiHgU,gBAAjH;AAAA,WAAiHA,gBAAjH;AAAA,cAA+I;AAA/I;;AACA;AAAA,qDAz5BgGhU,EAy5BhG,mBAA2FgU,gBAA3F,EAAyH,CAAC;AAC9GxL,IAAAA,IAAI,EAAEvI,UADwG;AAE9GwI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFwG,GAAD,CAAzH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAE4F;AAAR,KAAD,EAAiC;AAAE5F,MAAAA,IAAI,EAAExI,EAAE,CAACmU;AAAX,KAAjC,EAAsD;AAAE3L,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACnHJ,QAAAA,IAAI,EAAEtI,MAD6G;AAEnHuI,QAAAA,IAAI,EAAE,CAAC1I,QAAD;AAF6G,OAAD;AAA/B,KAAtD,CAAP;AAGlB,GANxB;AAAA;AAOA;;;AACA,MAAMqU,YAAN,CAAmB;AACf1P,EAAAA,WAAW,CAAC2P,WAAD,EAAcC,iBAAd;AACX;AACJ;AACA;AACA;AACI3P,EAAAA,SALW,EAKA;AACP,SAAK0P,WAAL,GAAmBA,WAAnB;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA;;AACA,SAAKC,yBAAL,GAAiC,IAAjC;AACA,SAAKC,SAAL,GAAiB,KAAKF,iBAAL,CAAuBL,MAAvB,CAA8B,KAAKI,WAAL,CAAiBI,aAA/C,EAA8D,IAA9D,CAAjB;AACH;AACD;;;AACW,MAAPhK,OAAO,GAAG;AACV,WAAO,KAAK+J,SAAL,CAAe/J,OAAtB;AACH;;AACU,MAAPA,OAAO,CAAC6G,KAAD,EAAQ;AACf,SAAKkD,SAAL,CAAe/J,OAAf,GAAyB5H,qBAAqB,CAACyO,KAAD,CAA9C;AACH;AACD;AACJ;AACA;AACA;;;AACmB,MAAXoD,WAAW,GAAG;AACd,WAAO,KAAKC,YAAZ;AACH;;AACc,MAAXD,WAAW,CAACpD,KAAD,EAAQ;AACnB,SAAKqD,YAAL,GAAoB9R,qBAAqB,CAACyO,KAAD,CAAzC;AACH;;AACD9K,EAAAA,WAAW,GAAG;AACV,SAAKgO,SAAL,CAAe9C,OAAf,GADU,CAEV;AACA;;AACA,QAAI,KAAK6C,yBAAT,EAAoC;AAChC,WAAKA,yBAAL,CAA+BtG,KAA/B;;AACA,WAAKsG,yBAAL,GAAiC,IAAjC;AACH;AACJ;;AACDK,EAAAA,kBAAkB,GAAG;AACjB,SAAKJ,SAAL,CAAenD,aAAf;;AACA,QAAI,KAAKqD,WAAT,EAAsB;AAClB,WAAKG,aAAL;AACH;AACJ;;AACDC,EAAAA,SAAS,GAAG;AACR,QAAI,CAAC,KAAKN,SAAL,CAAelB,WAAf,EAAL,EAAmC;AAC/B,WAAKkB,SAAL,CAAenD,aAAf;AACH;AACJ;;AACD0D,EAAAA,WAAW,CAACjL,OAAD,EAAU;AACjB,UAAMkL,iBAAiB,GAAGlL,OAAO,CAAC,aAAD,CAAjC;;AACA,QAAIkL,iBAAiB,IACjB,CAACA,iBAAiB,CAACC,WADnB,IAEA,KAAKP,WAFL,IAGA,KAAKF,SAAL,CAAelB,WAAf,EAHJ,EAGkC;AAC9B,WAAKuB,aAAL;AACH;AACJ;;AACDA,EAAAA,aAAa,GAAG;AACZ,SAAKN,yBAAL,GAAiC3T,iCAAiC,EAAlE;AACA,SAAK4T,SAAL,CAAepC,4BAAf;AACH;;AA9Dc;;AAgEnBgC,YAAY,CAAC/L,IAAb;AAAA,mBAAyG+L,YAAzG,EAj+BgGpU,EAi+BhG,mBAAuIA,EAAE,CAACkV,UAA1I,GAj+BgGlV,EAi+BhG,mBAAiKgU,gBAAjK,GAj+BgGhU,EAi+BhG,mBAA8LD,QAA9L;AAAA;;AACAqU,YAAY,CAACe,IAAb,kBAl+BgGnV,EAk+BhG;AAAA,QAA6FoU,YAA7F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAl+BgGpU,EAk+BhG;AAAA;;AACA;AAAA,qDAn+BgGA,EAm+BhG,mBAA2FoU,YAA3F,EAAqH,CAAC;AAC1G5L,IAAAA,IAAI,EAAEpI,SADoG;AAE1GqI,IAAAA,IAAI,EAAE,CAAC;AACC2M,MAAAA,QAAQ,EAAE,gBADX;AAECC,MAAAA,QAAQ,EAAE;AAFX,KAAD;AAFoG,GAAD,CAArH,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAE7M,MAAAA,IAAI,EAAExI,EAAE,CAACkV;AAAX,KAAD,EAA0B;AAAE1M,MAAAA,IAAI,EAAEwL;AAAR,KAA1B,EAAsD;AAAExL,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACnHJ,QAAAA,IAAI,EAAEtI,MAD6G;AAEnHuI,QAAAA,IAAI,EAAE,CAAC1I,QAAD;AAF6G,OAAD;AAA/B,KAAtD,CAAP;AAGlB,GATxB,EAS0C;AAAE0K,IAAAA,OAAO,EAAE,CAAC;AACtCjC,MAAAA,IAAI,EAAEnI,KADgC;AAEtCoI,MAAAA,IAAI,EAAE,CAAC,cAAD;AAFgC,KAAD,CAAX;AAG1BiM,IAAAA,WAAW,EAAE,CAAC;AACdlM,MAAAA,IAAI,EAAEnI,KADQ;AAEdoI,MAAAA,IAAI,EAAE,CAAC,yBAAD;AAFQ,KAAD;AAHa,GAT1C;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM6M,qBAAN,SAAoC5E,SAApC,CAA8C;AAC1ChM,EAAAA,WAAW,CAACiM,QAAD,EAAWC,QAAX,EAAqBC,OAArB,EAA8BlM,SAA9B,EAAyC4Q,iBAAzC,EAA4DC,cAA5D,EAA4EhG,MAA5E,EAAoF;AAC3F,UAAMmB,QAAN,EAAgBC,QAAhB,EAA0BC,OAA1B,EAAmClM,SAAnC,EAA8C6K,MAAM,CAACiG,KAArD;AACA,SAAKF,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,cAAL,GAAsBA,cAAtB;;AACA,SAAKD,iBAAL,CAAuBG,QAAvB,CAAgC,IAAhC;AACH;AACD;;;AACW,MAAPjL,OAAO,GAAG;AACV,WAAO,KAAK2G,QAAZ;AACH;;AACU,MAAP3G,OAAO,CAAC6G,KAAD,EAAQ;AACf,SAAKF,QAAL,GAAgBE,KAAhB;;AACA,QAAI,KAAKF,QAAT,EAAmB;AACf,WAAKmE,iBAAL,CAAuBG,QAAvB,CAAgC,IAAhC;AACH,KAFD,MAGK;AACD,WAAKH,iBAAL,CAAuBI,UAAvB,CAAkC,IAAlC;AACH;AACJ;AACD;;;AACAjE,EAAAA,OAAO,GAAG;AACN,SAAK6D,iBAAL,CAAuBI,UAAvB,CAAkC,IAAlC;;AACA,UAAMjE,OAAN;AACH;AACD;;;AACAkE,EAAAA,OAAO,GAAG;AACN,SAAKJ,cAAL,CAAoBK,YAApB,CAAiC,IAAjC;;AACA,SAAKjC,aAAL,CAAmB,IAAnB;AACH;AACD;;;AACAkC,EAAAA,QAAQ,GAAG;AACP,SAAKN,cAAL,CAAoBO,UAApB,CAA+B,IAA/B;;AACA,SAAKnC,aAAL,CAAmB,KAAnB;AACH;;AAlCyC;AAqC9C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMoC,yBAAyB,GAAG,IAAI1V,cAAJ,CAAmB,2BAAnB,CAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,MAAM2V,mCAAN,CAA0C;AACtCvR,EAAAA,WAAW,GAAG;AACV;AACA,SAAKwR,SAAL,GAAiB,IAAjB;AACH;AACD;;;AACAL,EAAAA,YAAY,CAACrB,SAAD,EAAY;AACpB;AACA,QAAI,KAAK0B,SAAT,EAAoB;AAChB1B,MAAAA,SAAS,CAAC7P,SAAV,CAAoBkN,mBAApB,CAAwC,OAAxC,EAAiD,KAAKqE,SAAtD,EAAiE,IAAjE;AACH;;AACD,SAAKA,SAAL,GAAkBC,CAAD,IAAO,KAAKC,UAAL,CAAgB5B,SAAhB,EAA2B2B,CAA3B,CAAxB;;AACA3B,IAAAA,SAAS,CAAC3D,OAAV,CAAkBiB,iBAAlB,CAAoC,MAAM;AACtC0C,MAAAA,SAAS,CAAC7P,SAAV,CAAoBqN,gBAApB,CAAqC,OAArC,EAA8C,KAAKkE,SAAnD,EAA8D,IAA9D;AACH,KAFD;AAGH;AACD;;;AACAH,EAAAA,UAAU,CAACvB,SAAD,EAAY;AAClB,QAAI,CAAC,KAAK0B,SAAV,EAAqB;AACjB;AACH;;AACD1B,IAAAA,SAAS,CAAC7P,SAAV,CAAoBkN,mBAApB,CAAwC,OAAxC,EAAiD,KAAKqE,SAAtD,EAAiE,IAAjE;;AACA,SAAKA,SAAL,GAAiB,IAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIE,EAAAA,UAAU,CAAC5B,SAAD,EAAYtI,KAAZ,EAAmB;AACzB,UAAMmK,MAAM,GAAGnK,KAAK,CAACmK,MAArB;AACA,UAAMC,aAAa,GAAG9B,SAAS,CAAC7D,QAAhC,CAFyB,CAGzB;AACA;;AACA,QAAI0F,MAAM,IAAI,CAACC,aAAa,CAACC,QAAd,CAAuBF,MAAvB,CAAX,IAA6C,CAACA,MAAM,CAACG,OAAP,GAAiB,sBAAjB,CAAlD,EAA4F;AACxF;AACA;AACA;AACAC,MAAAA,UAAU,CAAC,MAAM;AACb;AACA,YAAIjC,SAAS,CAAC/J,OAAV,IAAqB,CAAC6L,aAAa,CAACC,QAAd,CAAuB/B,SAAS,CAAC7P,SAAV,CAAoB+R,aAA3C,CAA1B,EAAqF;AACjFlC,UAAAA,SAAS,CAACrD,yBAAV;AACH;AACJ,OALS,CAAV;AAMH;AACJ;;AA/CqC;AAkD1C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMwF,gBAAN,CAAuB;AACnBjS,EAAAA,WAAW,GAAG;AACV;AACA;AACA,SAAKkS,eAAL,GAAuB,EAAvB;AACH;AACD;AACJ;AACA;AACA;;;AACIlB,EAAAA,QAAQ,CAAClB,SAAD,EAAY;AAChB;AACA,SAAKoC,eAAL,GAAuB,KAAKA,eAAL,CAAqBrU,MAArB,CAA4BsU,EAAE,IAAIA,EAAE,KAAKrC,SAAzC,CAAvB;AACA,QAAIsC,KAAK,GAAG,KAAKF,eAAjB;;AACA,QAAIE,KAAK,CAAC7S,MAAV,EAAkB;AACd6S,MAAAA,KAAK,CAACA,KAAK,CAAC7S,MAAN,GAAe,CAAhB,CAAL,CAAwB6R,QAAxB;AACH;;AACDgB,IAAAA,KAAK,CAACnT,IAAN,CAAW6Q,SAAX;;AACAA,IAAAA,SAAS,CAACoB,OAAV;AACH;AACD;AACJ;AACA;AACA;;;AACID,EAAAA,UAAU,CAACnB,SAAD,EAAY;AAClBA,IAAAA,SAAS,CAACsB,QAAV;;AACA,UAAMgB,KAAK,GAAG,KAAKF,eAAnB;AACA,UAAMjQ,CAAC,GAAGmQ,KAAK,CAAChP,OAAN,CAAc0M,SAAd,CAAV;;AACA,QAAI7N,CAAC,KAAK,CAAC,CAAX,EAAc;AACVmQ,MAAAA,KAAK,CAACC,MAAN,CAAapQ,CAAb,EAAgB,CAAhB;;AACA,UAAImQ,KAAK,CAAC7S,MAAV,EAAkB;AACd6S,QAAAA,KAAK,CAACA,KAAK,CAAC7S,MAAN,GAAe,CAAhB,CAAL,CAAwB2R,OAAxB;AACH;AACJ;AACJ;;AAlCkB;;AAoCvBe,gBAAgB,CAACtO,IAAjB;AAAA,mBAA6GsO,gBAA7G;AAAA;;AACAA,gBAAgB,CAACpO,KAAjB,kBAlqCgGvI,EAkqChG;AAAA,SAAiH2W,gBAAjH;AAAA,WAAiHA,gBAAjH;AAAA,cAA+I;AAA/I;;AACA;AAAA,qDAnqCgG3W,EAmqChG,mBAA2F2W,gBAA3F,EAAyH,CAAC;AAC9GnO,IAAAA,IAAI,EAAEvI,UADwG;AAE9GwI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFwG,GAAD,CAAzH;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMsO,4BAAN,CAAmC;AAC/BtS,EAAAA,WAAW,CAACkM,QAAD,EAAWC,OAAX,EAAoB0E,iBAApB,EAAuC5Q,SAAvC,EAAkD6Q,cAAlD,EAAkE;AACzE,SAAK5E,QAAL,GAAgBA,QAAhB;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAK0E,iBAAL,GAAyBA,iBAAzB;AACA,SAAK5Q,SAAL,GAAiBA,SAAjB,CAJyE,CAKzE;;AACA,SAAK6Q,cAAL,GAAsBA,cAAc,IAAI,IAAIS,mCAAJ,EAAxC;AACH;;AACDhC,EAAAA,MAAM,CAACrM,OAAD,EAAU4H,MAAM,GAAG;AAAEiG,IAAAA,KAAK,EAAE;AAAT,GAAnB,EAAqC;AACvC,QAAIwB,YAAJ;;AACA,QAAI,OAAOzH,MAAP,KAAkB,SAAtB,EAAiC;AAC7ByH,MAAAA,YAAY,GAAG;AAAExB,QAAAA,KAAK,EAAEjG;AAAT,OAAf;AACH,KAFD,MAGK;AACDyH,MAAAA,YAAY,GAAGzH,MAAf;AACH;;AACD,WAAO,IAAI8F,qBAAJ,CAA0B1N,OAA1B,EAAmC,KAAKgJ,QAAxC,EAAkD,KAAKC,OAAvD,EAAgE,KAAKlM,SAArE,EAAgF,KAAK4Q,iBAArF,EAAwG,KAAKC,cAA7G,EAA6HyB,YAA7H,CAAP;AACH;;AAlB8B;;AAoBnCD,4BAA4B,CAAC3O,IAA7B;AAAA,mBAAyH2O,4BAAzH,EApsCgGhX,EAosChG,UAAuKoO,oBAAvK,GApsCgGpO,EAosChG,UAAwMA,EAAE,CAACmU,MAA3M,GApsCgGnU,EAosChG,UAA8N2W,gBAA9N,GApsCgG3W,EAosChG,UAA2PD,QAA3P,GApsCgGC,EAosChG,UAAgRgW,yBAAhR;AAAA;;AACAgB,4BAA4B,CAACzO,KAA7B,kBArsCgGvI,EAqsChG;AAAA,SAA6HgX,4BAA7H;AAAA,WAA6HA,4BAA7H;AAAA,cAAuK;AAAvK;;AACA;AAAA,qDAtsCgGhX,EAssChG,mBAA2FgX,4BAA3F,EAAqI,CAAC;AAC1HxO,IAAAA,IAAI,EAAEvI,UADoH;AAE1HwI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFoH,GAAD,CAArI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAE4F;AAAR,KAAD,EAAiC;AAAE5F,MAAAA,IAAI,EAAExI,EAAE,CAACmU;AAAX,KAAjC,EAAsD;AAAE3L,MAAAA,IAAI,EAAEmO;AAAR,KAAtD,EAAkF;AAAEnO,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC/IJ,QAAAA,IAAI,EAAEtI,MADyI;AAE/IuI,QAAAA,IAAI,EAAE,CAAC1I,QAAD;AAFyI,OAAD;AAA/B,KAAlF,EAG3B;AAAEyI,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAClCJ,QAAAA,IAAI,EAAEjI;AAD4B,OAAD,EAElC;AACCiI,QAAAA,IAAI,EAAEtI,MADP;AAECuI,QAAAA,IAAI,EAAE,CAACuN,yBAAD;AAFP,OAFkC;AAA/B,KAH2B,CAAP;AAQlB,GAXxB;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASkB,+BAAT,CAAyChL,KAAzC,EAAgD;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAOA,KAAK,CAACiL,OAAN,KAAkB,CAAlB,IAAwBjL,KAAK,CAACkL,OAAN,KAAkB,CAAlB,IAAuBlL,KAAK,CAACmL,OAAN,KAAkB,CAAxE;AACH;AACD;;;AACA,SAASC,gCAAT,CAA0CpL,KAA1C,EAAiD;AAC7C,QAAMqL,KAAK,GAAIrL,KAAK,CAACsL,OAAN,IAAiBtL,KAAK,CAACsL,OAAN,CAAc,CAAd,CAAlB,IAAwCtL,KAAK,CAACuL,cAAN,IAAwBvL,KAAK,CAACuL,cAAN,CAAqB,CAArB,CAA9E,CAD6C,CAE7C;AACA;AACA;AACA;;AACA,SAAQ,CAAC,CAACF,KAAF,IACJA,KAAK,CAACG,UAAN,KAAqB,CAAC,CADlB,KAEHH,KAAK,CAACI,OAAN,IAAiB,IAAjB,IAAyBJ,KAAK,CAACI,OAAN,KAAkB,CAFxC,MAGHJ,KAAK,CAACK,OAAN,IAAiB,IAAjB,IAAyBL,KAAK,CAACK,OAAN,KAAkB,CAHxC,CAAR;AAIH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMC,+BAA+B,GAAG,IAAIvX,cAAJ,CAAmB,qCAAnB,CAAxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMwX,uCAAuC,GAAG;AAC5CC,EAAAA,UAAU,EAAE,CAAC/V,GAAD,EAAMC,OAAN,EAAeC,QAAf,EAAyBC,IAAzB,EAA+BC,KAA/B;AADgC,CAAhD;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM4V,eAAe,GAAG,GAAxB;AACA;AACA;AACA;AACA;;AACA,MAAMC,4BAA4B,GAAGpX,+BAA+B,CAAC;AACjEqX,EAAAA,OAAO,EAAE,IADwD;AAEjEC,EAAAA,OAAO,EAAE;AAFwD,CAAD,CAApE;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAN,CAA4B;AACxB1T,EAAAA,WAAW,CAACE,SAAD,EAAYyT,MAAZ,EAAoBC,QAApB,EAA8BjG,OAA9B,EAAuC;AAC9C,SAAKzN,SAAL,GAAiBA,SAAjB;AACA;AACR;AACA;AACA;;AACQ,SAAK2T,iBAAL,GAAyB,IAAzB;AACA;;AACA,SAAKC,SAAL,GAAiB,IAAItX,eAAJ,CAAoB,IAApB,CAAjB;AACA;AACR;AACA;AACA;;AACQ,SAAKuX,YAAL,GAAoB,CAApB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,UAAL,GAAmBxM,KAAD,IAAW;AACzB;AACA;AACA,UAAI,KAAKyM,QAAL,EAAeZ,UAAf,EAA2BvU,IAA3B,CAAgC2I,OAAO,IAAIA,OAAO,KAAKD,KAAK,CAACC,OAA7D,CAAJ,EAA2E;AACvE;AACH;;AACD,WAAKqM,SAAL,CAAexM,IAAf,CAAoB,UAApB;;AACA,WAAKuM,iBAAL,GAAyBzX,eAAe,CAACoL,KAAD,CAAxC;AACH,KARD;AASA;AACR;AACA;AACA;;;AACQ,SAAK0M,YAAL,GAAqB1M,KAAD,IAAW;AAC3B;AACA;AACA;AACA,UAAI2M,IAAI,CAACC,GAAL,KAAa,KAAKL,YAAlB,GAAiCT,eAArC,EAAsD;AAClD;AACH,OAN0B,CAO3B;AACA;;;AACA,WAAKQ,SAAL,CAAexM,IAAf,CAAoBkL,+BAA+B,CAAChL,KAAD,CAA/B,GAAyC,UAAzC,GAAsD,OAA1E;;AACA,WAAKqM,iBAAL,GAAyBzX,eAAe,CAACoL,KAAD,CAAxC;AACH,KAXD;AAYA;AACR;AACA;AACA;;;AACQ,SAAK6M,aAAL,GAAsB7M,KAAD,IAAW;AAC5B;AACA;AACA,UAAIoL,gCAAgC,CAACpL,KAAD,CAApC,EAA6C;AACzC,aAAKsM,SAAL,CAAexM,IAAf,CAAoB,UAApB;;AACA;AACH,OAN2B,CAO5B;AACA;;;AACA,WAAKyM,YAAL,GAAoBI,IAAI,CAACC,GAAL,EAApB;;AACA,WAAKN,SAAL,CAAexM,IAAf,CAAoB,OAApB;;AACA,WAAKuM,iBAAL,GAAyBzX,eAAe,CAACoL,KAAD,CAAxC;AACH,KAZD;;AAaA,SAAKyM,QAAL,GAAgB,EACZ,GAAGb,uCADS;AAEZ,SAAGzF;AAFS,KAAhB,CA5D8C,CAgE9C;;AACA,SAAK2G,gBAAL,GAAwB,KAAKR,SAAL,CAAenN,IAAf,CAAoB3I,IAAI,CAAC,CAAD,CAAxB,CAAxB;AACA,SAAKuW,eAAL,GAAuB,KAAKD,gBAAL,CAAsB3N,IAAtB,CAA2B1I,oBAAoB,EAA/C,CAAvB,CAlE8C,CAmE9C;AACA;;AACA,QAAIiC,SAAS,CAAC8C,SAAd,EAAyB;AACrB2Q,MAAAA,MAAM,CAACvG,iBAAP,CAAyB,MAAM;AAC3BwG,QAAAA,QAAQ,CAACtG,gBAAT,CAA0B,SAA1B,EAAqC,KAAK0G,UAA1C,EAAsDT,4BAAtD;AACAK,QAAAA,QAAQ,CAACtG,gBAAT,CAA0B,WAA1B,EAAuC,KAAK4G,YAA5C,EAA0DX,4BAA1D;AACAK,QAAAA,QAAQ,CAACtG,gBAAT,CAA0B,YAA1B,EAAwC,KAAK+G,aAA7C,EAA4Dd,4BAA5D;AACH,OAJD;AAKH;AACJ;AACD;;;AACsB,MAAlBiB,kBAAkB,GAAG;AACrB,WAAO,KAAKV,SAAL,CAAelH,KAAtB;AACH;;AACD9K,EAAAA,WAAW,GAAG;AACV,SAAKgS,SAAL,CAAeW,QAAf;;AACA,QAAI,KAAKvU,SAAL,CAAe8C,SAAnB,EAA8B;AAC1B4Q,MAAAA,QAAQ,CAACzG,mBAAT,CAA6B,SAA7B,EAAwC,KAAK6G,UAA7C,EAAyDT,4BAAzD;AACAK,MAAAA,QAAQ,CAACzG,mBAAT,CAA6B,WAA7B,EAA0C,KAAK+G,YAA/C,EAA6DX,4BAA7D;AACAK,MAAAA,QAAQ,CAACzG,mBAAT,CAA6B,YAA7B,EAA2C,KAAKkH,aAAhD,EAA+Dd,4BAA/D;AACH;AACJ;;AAzFuB;;AA2F5BG,qBAAqB,CAAC/P,IAAtB;AAAA,mBAAkH+P,qBAAlH,EA14CgGpY,EA04ChG,UAAyJW,EAAE,CAAC2H,QAA5J,GA14CgGtI,EA04ChG,UAAiLA,EAAE,CAACmU,MAApL,GA14CgGnU,EA04ChG,UAAuMD,QAAvM,GA14CgGC,EA04ChG,UAA4N6X,+BAA5N;AAAA;;AACAO,qBAAqB,CAAC7P,KAAtB,kBA34CgGvI,EA24ChG;AAAA,SAAsHoY,qBAAtH;AAAA,WAAsHA,qBAAtH;AAAA,cAAyJ;AAAzJ;;AACA;AAAA,qDA54CgGpY,EA44ChG,mBAA2FoY,qBAA3F,EAA8H,CAAC;AACnH5P,IAAAA,IAAI,EAAEvI,UAD6G;AAEnHwI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF6G,GAAD,CAA9H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAE7H,EAAE,CAAC2H;AAAX,KAAD,EAAwB;AAAEE,MAAAA,IAAI,EAAExI,EAAE,CAACmU;AAAX,KAAxB,EAA6C;AAAE3L,MAAAA,IAAI,EAAE4Q,QAAR;AAAkBxQ,MAAAA,UAAU,EAAE,CAAC;AACzGJ,QAAAA,IAAI,EAAEtI,MADmG;AAEzGuI,QAAAA,IAAI,EAAE,CAAC1I,QAAD;AAFmG,OAAD;AAA9B,KAA7C,EAG3B;AAAEyI,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAClCJ,QAAAA,IAAI,EAAEjI;AAD4B,OAAD,EAElC;AACCiI,QAAAA,IAAI,EAAEtI,MADP;AAECuI,QAAAA,IAAI,EAAE,CAACoP,+BAAD;AAFP,OAFkC;AAA/B,KAH2B,CAAP;AAQlB,GAXxB;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMwB,4BAA4B,GAAG,IAAI/Y,cAAJ,CAAmB,sBAAnB,EAA2C;AAC5EoI,EAAAA,UAAU,EAAE,MADgE;AAE5E4Q,EAAAA,OAAO,EAAEC;AAFmE,CAA3C,CAArC;AAIA;;AACA,SAASA,oCAAT,GAAgD;AAC5C,SAAO,IAAP;AACH;AACD;;;AACA,MAAMC,8BAA8B,GAAG,IAAIlZ,cAAJ,CAAmB,gCAAnB,CAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMmZ,aAAN,CAAoB;AAChB/U,EAAAA,WAAW,CAACgV,YAAD,EAAe7I,OAAf,EAAwBlM,SAAxB,EAAmCgV,eAAnC,EAAoD;AAC3D,SAAK9I,OAAL,GAAeA,OAAf;AACA,SAAK8I,eAAL,GAAuBA,eAAvB,CAF2D,CAG3D;AACA;AACA;;AACA,SAAKhV,SAAL,GAAiBA,SAAjB;AACA,SAAKiV,YAAL,GAAoBF,YAAY,IAAI,KAAKG,kBAAL,EAApC;AACH;;AACDC,EAAAA,QAAQ,CAAC3U,OAAD,EAAU,GAAGsD,IAAb,EAAmB;AACvB,UAAMsR,cAAc,GAAG,KAAKJ,eAA5B;AACA,QAAIK,UAAJ;AACA,QAAIC,QAAJ;;AACA,QAAIxR,IAAI,CAACxE,MAAL,KAAgB,CAAhB,IAAqB,OAAOwE,IAAI,CAAC,CAAD,CAAX,KAAmB,QAA5C,EAAsD;AAClDwR,MAAAA,QAAQ,GAAGxR,IAAI,CAAC,CAAD,CAAf;AACH,KAFD,MAGK;AACD,OAACuR,UAAD,EAAaC,QAAb,IAAyBxR,IAAzB;AACH;;AACD,SAAK5B,KAAL;AACAqT,IAAAA,YAAY,CAAC,KAAKC,gBAAN,CAAZ;;AACA,QAAI,CAACH,UAAL,EAAiB;AACbA,MAAAA,UAAU,GACND,cAAc,IAAIA,cAAc,CAACC,UAAjC,GAA8CD,cAAc,CAACC,UAA7D,GAA0E,QAD9E;AAEH;;AACD,QAAIC,QAAQ,IAAI,IAAZ,IAAoBF,cAAxB,EAAwC;AACpCE,MAAAA,QAAQ,GAAGF,cAAc,CAACE,QAA1B;AACH,KAlBsB,CAmBvB;;;AACA,SAAKL,YAAL,CAAkBhW,YAAlB,CAA+B,WAA/B,EAA4CoW,UAA5C,EApBuB,CAqBvB;AACA;AACA;AACA;AACA;;;AACA,WAAO,KAAKnJ,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;AACxC,UAAI,CAAC,KAAKsI,eAAV,EAA2B;AACvB,aAAKA,eAAL,GAAuB,IAAI9H,OAAJ,CAAYC,OAAO,IAAK,KAAK8H,eAAL,GAAuB9H,OAA/C,CAAvB;AACH;;AACD2H,MAAAA,YAAY,CAAC,KAAKC,gBAAN,CAAZ;AACA,WAAKA,gBAAL,GAAwB1D,UAAU,CAAC,MAAM;AACrC,aAAKmD,YAAL,CAAkB7S,WAAlB,GAAgC5B,OAAhC;;AACA,YAAI,OAAO8U,QAAP,KAAoB,QAAxB,EAAkC;AAC9B,eAAKE,gBAAL,GAAwB1D,UAAU,CAAC,MAAM,KAAK5P,KAAL,EAAP,EAAqBoT,QAArB,CAAlC;AACH;;AACD,aAAKI,eAAL;;AACA,aAAKD,eAAL,GAAuB,KAAKC,eAAL,GAAuB1R,SAA9C;AACH,OAPiC,EAO/B,GAP+B,CAAlC;AAQA,aAAO,KAAKyR,eAAZ;AACH,KAdM,CAAP;AAeH;AACD;AACJ;AACA;AACA;AACA;;;AACIvT,EAAAA,KAAK,GAAG;AACJ,QAAI,KAAK+S,YAAT,EAAuB;AACnB,WAAKA,YAAL,CAAkB7S,WAAlB,GAAgC,EAAhC;AACH;AACJ;;AACDP,EAAAA,WAAW,GAAG;AACV0T,IAAAA,YAAY,CAAC,KAAKC,gBAAN,CAAZ;AACA,SAAKP,YAAL,EAAmBrT,MAAnB;AACA,SAAKqT,YAAL,GAAoB,IAApB;AACA,SAAKS,eAAL;AACA,SAAKD,eAAL,GAAuB,KAAKC,eAAL,GAAuB1R,SAA9C;AACH;;AACDkR,EAAAA,kBAAkB,GAAG;AACjB,UAAMS,YAAY,GAAG,4BAArB;;AACA,UAAMC,gBAAgB,GAAG,KAAK5V,SAAL,CAAe6V,sBAAf,CAAsCF,YAAtC,CAAzB;;AACA,UAAMG,MAAM,GAAG,KAAK9V,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAAf,CAHiB,CAIjB;;;AACA,SAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4T,gBAAgB,CAACtW,MAArC,EAA6C0C,CAAC,EAA9C,EAAkD;AAC9C4T,MAAAA,gBAAgB,CAAC5T,CAAD,CAAhB,CAAoBJ,MAApB;AACH;;AACDkU,IAAAA,MAAM,CAACjT,SAAP,CAAiBC,GAAjB,CAAqB6S,YAArB;AACAG,IAAAA,MAAM,CAACjT,SAAP,CAAiBC,GAAjB,CAAqB,qBAArB;AACAgT,IAAAA,MAAM,CAAC7W,YAAP,CAAoB,aAApB,EAAmC,MAAnC;AACA6W,IAAAA,MAAM,CAAC7W,YAAP,CAAoB,WAApB,EAAiC,QAAjC;;AACA,SAAKe,SAAL,CAAegD,IAAf,CAAoBV,WAApB,CAAgCwT,MAAhC;;AACA,WAAOA,MAAP;AACH;;AAnFe;;AAqFpBhB,aAAa,CAACpR,IAAd;AAAA,mBAA0GoR,aAA1G,EAvgDgGzZ,EAugDhG,UAAyIqZ,4BAAzI,MAvgDgGrZ,EAugDhG,UAAkMA,EAAE,CAACmU,MAArM,GAvgDgGnU,EAugDhG,UAAwND,QAAxN,GAvgDgGC,EAugDhG,UAA6OwZ,8BAA7O;AAAA;;AACAC,aAAa,CAAClR,KAAd,kBAxgDgGvI,EAwgDhG;AAAA,SAA8GyZ,aAA9G;AAAA,WAA8GA,aAA9G;AAAA,cAAyI;AAAzI;;AACA;AAAA,qDAzgDgGzZ,EAygDhG,mBAA2FyZ,aAA3F,EAAsH,CAAC;AAC3GjR,IAAAA,IAAI,EAAEvI,UADqG;AAE3GwI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFqG,GAAD,CAAtH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DJ,QAAAA,IAAI,EAAEjI;AADwD,OAAD,EAE9D;AACCiI,QAAAA,IAAI,EAAEtI,MADP;AAECuI,QAAAA,IAAI,EAAE,CAAC4Q,4BAAD;AAFP,OAF8D;AAA/B,KAAD,EAK3B;AAAE7Q,MAAAA,IAAI,EAAExI,EAAE,CAACmU;AAAX,KAL2B,EAKN;AAAE3L,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACvDJ,QAAAA,IAAI,EAAEtI,MADiD;AAEvDuI,QAAAA,IAAI,EAAE,CAAC1I,QAAD;AAFiD,OAAD;AAA/B,KALM,EAQ3B;AAAEyI,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAClCJ,QAAAA,IAAI,EAAEjI;AAD4B,OAAD,EAElC;AACCiI,QAAAA,IAAI,EAAEtI,MADP;AAECuI,QAAAA,IAAI,EAAE,CAAC+Q,8BAAD;AAFP,OAFkC;AAA/B,KAR2B,CAAP;AAalB,GAhBxB;AAAA;AAiBA;AACA;AACA;AACA;;;AACA,MAAMkB,WAAN,CAAkB;AACdhW,EAAAA,WAAW,CAAC2P,WAAD,EAAcsG,cAAd,EAA8BC,gBAA9B,EAAgD/J,OAAhD,EAAyD;AAChE,SAAKwD,WAAL,GAAmBA,WAAnB;AACA,SAAKsG,cAAL,GAAsBA,cAAtB;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAK/J,OAAL,GAAeA,OAAf;AACA,SAAKgK,WAAL,GAAmB,QAAnB;AACH;AACD;;;AACc,MAAVb,UAAU,GAAG;AACb,WAAO,KAAKa,WAAZ;AACH;;AACa,MAAVb,UAAU,CAAC1I,KAAD,EAAQ;AAClB,SAAKuJ,WAAL,GAAmBvJ,KAAK,KAAK,KAAV,IAAmBA,KAAK,KAAK,WAA7B,GAA2CA,KAA3C,GAAmD,QAAtE;;AACA,QAAI,KAAKuJ,WAAL,KAAqB,KAAzB,EAAgC;AAC5B,UAAI,KAAKC,aAAT,EAAwB;AACpB,aAAKA,aAAL,CAAmB1P,WAAnB;;AACA,aAAK0P,aAAL,GAAqB,IAArB;AACH;AACJ,KALD,MAMK,IAAI,CAAC,KAAKA,aAAV,EAAyB;AAC1B,WAAKA,aAAL,GAAqB,KAAKjK,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;AACtD,eAAO,KAAK8I,gBAAL,CAAsBG,OAAtB,CAA8B,KAAK1G,WAAnC,EAAgDtK,SAAhD,CAA0D,MAAM;AACnE;AACA,gBAAMiR,WAAW,GAAG,KAAK3G,WAAL,CAAiBI,aAAjB,CAA+B1N,WAAnD,CAFmE,CAGnE;AACA;;AACA,cAAIiU,WAAW,KAAK,KAAKC,sBAAzB,EAAiD;AAC7C,iBAAKN,cAAL,CAAoBb,QAApB,CAA6BkB,WAA7B,EAA0C,KAAKH,WAA/C;;AACA,iBAAKI,sBAAL,GAA8BD,WAA9B;AACH;AACJ,SATM,CAAP;AAUH,OAXoB,CAArB;AAYH;AACJ;;AACDxU,EAAAA,WAAW,GAAG;AACV,QAAI,KAAKsU,aAAT,EAAwB;AACpB,WAAKA,aAAL,CAAmB1P,WAAnB;AACH;AACJ;;AAvCa;;AAyClBsP,WAAW,CAACrS,IAAZ;AAAA,mBAAwGqS,WAAxG,EAvkDgG1a,EAukDhG,mBAAqIA,EAAE,CAACkV,UAAxI,GAvkDgGlV,EAukDhG,mBAA+JyZ,aAA/J,GAvkDgGzZ,EAukDhG,mBAAyL+C,IAAI,CAACmY,eAA9L,GAvkDgGlb,EAukDhG,mBAA0NA,EAAE,CAACmU,MAA7N;AAAA;;AACAuG,WAAW,CAACvF,IAAZ,kBAxkDgGnV,EAwkDhG;AAAA,QAA4F0a,WAA5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAzkDgG1a,EAykDhG,mBAA2F0a,WAA3F,EAAoH,CAAC;AACzGlS,IAAAA,IAAI,EAAEpI,SADmG;AAEzGqI,IAAAA,IAAI,EAAE,CAAC;AACC2M,MAAAA,QAAQ,EAAE,eADX;AAECC,MAAAA,QAAQ,EAAE;AAFX,KAAD;AAFmG,GAAD,CAApH,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAE7M,MAAAA,IAAI,EAAExI,EAAE,CAACkV;AAAX,KAAD,EAA0B;AAAE1M,MAAAA,IAAI,EAAEiR;AAAR,KAA1B,EAAmD;AAAEjR,MAAAA,IAAI,EAAEzF,IAAI,CAACmY;AAAb,KAAnD,EAAmF;AAAE1S,MAAAA,IAAI,EAAExI,EAAE,CAACmU;AAAX,KAAnF,CAAP;AAAiH,GAN3J,EAM6K;AAAE6F,IAAAA,UAAU,EAAE,CAAC;AAC5KxR,MAAAA,IAAI,EAAEnI,KADsK;AAE5KoI,MAAAA,IAAI,EAAE,CAAC,aAAD;AAFsK,KAAD;AAAd,GAN7K;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM0S,6BAA6B,GAAG,IAAI7a,cAAJ,CAAmB,mCAAnB,CAAtC;AACA;AACA;AACA;AACA;;AACA,MAAM8a,2BAA2B,GAAGva,+BAA+B,CAAC;AAChEqX,EAAAA,OAAO,EAAE,IADuD;AAEhEC,EAAAA,OAAO,EAAE;AAFuD,CAAD,CAAnE;AAIA;;AACA,MAAMkD,YAAN,CAAmB;AACf3W,EAAAA,WAAW,CAACmM,OAAD,EAAUjM,SAAV,EAAqB0W,sBAArB;AACX;AACAhD,EAAAA,QAFW,EAEDjG,OAFC,EAEQ;AACf,SAAKxB,OAAL,GAAeA,OAAf;AACA,SAAKjM,SAAL,GAAiBA,SAAjB;AACA,SAAK0W,sBAAL,GAA8BA,sBAA9B;AACA;;AACA,SAAKxN,OAAL,GAAe,IAAf;AACA;;AACA,SAAKyN,cAAL,GAAsB,KAAtB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,2BAAL,GAAmC,KAAnC;AACA;;AACA,SAAKC,YAAL,GAAoB,IAAI3W,GAAJ,EAApB;AACA;;AACA,SAAK4W,sBAAL,GAA8B,CAA9B;AACA;AACR;AACA;AACA;AACA;AACA;;AACQ,SAAKC,2BAAL,GAAmC,IAAI7W,GAAJ,EAAnC;AACA;AACR;AACA;AACA;;AACQ,SAAK8W,oBAAL,GAA4B,MAAM;AAC9B;AACA;AACA,WAAKL,cAAL,GAAsB,IAAtB;AACA,WAAKM,qBAAL,GAA6BnM,MAAM,CAAC+G,UAAP,CAAkB,MAAO,KAAK8E,cAAL,GAAsB,KAA/C,CAA7B;AACH,KALD;AAMA;;;AACA,SAAKO,0BAAL,GAAkC,IAAI9a,OAAJ,EAAlC;AACA;AACR;AACA;AACA;;AACQ,SAAK+a,6BAAL,GAAsC7P,KAAD,IAAW;AAC5C,YAAMmK,MAAM,GAAGvV,eAAe,CAACoL,KAAD,CAA9B;;AACA,YAAM8P,OAAO,GAAG9P,KAAK,CAAC1D,IAAN,KAAe,OAAf,GAAyB,KAAKyT,QAA9B,GAAyC,KAAKC,OAA9D,CAF4C,CAG5C;;AACA,WAAK,IAAItU,OAAO,GAAGyO,MAAnB,EAA2BzO,OAA3B,EAAoCA,OAAO,GAAGA,OAAO,CAACuU,aAAtD,EAAqE;AACjEH,QAAAA,OAAO,CAACI,IAAR,CAAa,IAAb,EAAmBlQ,KAAnB,EAA0BtE,OAA1B;AACH;AACJ,KAPD;;AAQA,SAAKjD,SAAL,GAAiB2T,QAAjB;AACA,SAAK+D,cAAL,GAAsBhK,OAAO,EAAEiK,aAAT,IAA0B;AAAE;AAAlD;AACH;;AACDC,EAAAA,OAAO,CAAC3U,OAAD,EAAU4U,aAAa,GAAG,KAA1B,EAAiC;AACpC,UAAM/H,aAAa,GAAG3R,aAAa,CAAC8E,OAAD,CAAnC,CADoC,CAEpC;;AACA,QAAI,CAAC,KAAKhD,SAAL,CAAe8C,SAAhB,IAA6B+M,aAAa,CAACtM,QAAd,KAA2B,CAA5D,EAA+D;AAC3D,aAAOhH,EAAE,CAAC,IAAD,CAAT;AACH,KALmC,CAMpC;AACA;AACA;;;AACA,UAAMsb,QAAQ,GAAG1b,cAAc,CAAC0T,aAAD,CAAd,IAAiC,KAAKiI,YAAL,EAAlD;;AACA,UAAMC,UAAU,GAAG,KAAKlB,YAAL,CAAkBrV,GAAlB,CAAsBqO,aAAtB,CAAnB,CAVoC,CAWpC;;;AACA,QAAIkI,UAAJ,EAAgB;AACZ,UAAIH,aAAJ,EAAmB;AACf;AACA;AACA;AACAG,QAAAA,UAAU,CAACH,aAAX,GAA2B,IAA3B;AACH;;AACD,aAAOG,UAAU,CAACC,OAAlB;AACH,KApBmC,CAqBpC;;;AACA,UAAMC,IAAI,GAAG;AACTL,MAAAA,aAAa,EAAEA,aADN;AAETI,MAAAA,OAAO,EAAE,IAAI5b,OAAJ,EAFA;AAGTyb,MAAAA;AAHS,KAAb;;AAKA,SAAKhB,YAAL,CAAkBhW,GAAlB,CAAsBgP,aAAtB,EAAqCoI,IAArC;;AACA,SAAKC,wBAAL,CAA8BD,IAA9B;;AACA,WAAOA,IAAI,CAACD,OAAZ;AACH;;AACDG,EAAAA,cAAc,CAACnV,OAAD,EAAU;AACpB,UAAM6M,aAAa,GAAG3R,aAAa,CAAC8E,OAAD,CAAnC;;AACA,UAAMoV,WAAW,GAAG,KAAKvB,YAAL,CAAkBrV,GAAlB,CAAsBqO,aAAtB,CAApB;;AACA,QAAIuI,WAAJ,EAAiB;AACbA,MAAAA,WAAW,CAACJ,OAAZ,CAAoBzD,QAApB;;AACA,WAAK8D,WAAL,CAAiBxI,aAAjB;;AACA,WAAKgH,YAAL,CAAkBvU,MAAlB,CAAyBuN,aAAzB;;AACA,WAAKyI,sBAAL,CAA4BF,WAA5B;AACH;AACJ;;AACDG,EAAAA,QAAQ,CAACvV,OAAD,EAAUoG,MAAV,EAAkBqE,OAAlB,EAA2B;AAC/B,UAAMoC,aAAa,GAAG3R,aAAa,CAAC8E,OAAD,CAAnC;;AACA,UAAMwV,cAAc,GAAG,KAAKV,YAAL,GAAoBhG,aAA3C,CAF+B,CAG/B;AACA;AACA;;;AACA,QAAIjC,aAAa,KAAK2I,cAAtB,EAAsC;AAClC,WAAKC,uBAAL,CAA6B5I,aAA7B,EAA4C6I,OAA5C,CAAoD,CAAC,CAACC,cAAD,EAAiBV,IAAjB,CAAD,KAA4B,KAAKW,cAAL,CAAoBD,cAApB,EAAoCvP,MAApC,EAA4C6O,IAA5C,CAAhF;AACH,KAFD,MAGK;AACD,WAAKY,UAAL,CAAgBzP,MAAhB,EADC,CAED;;;AACA,UAAI,OAAOyG,aAAa,CAACxG,KAArB,KAA+B,UAAnC,EAA+C;AAC3CwG,QAAAA,aAAa,CAACxG,KAAd,CAAoBoE,OAApB;AACH;AACJ;AACJ;;AACD7L,EAAAA,WAAW,GAAG;AACV,SAAKiV,YAAL,CAAkB6B,OAAlB,CAA0B,CAACI,KAAD,EAAQ9V,OAAR,KAAoB,KAAKmV,cAAL,CAAoBnV,OAApB,CAA9C;AACH;AACD;;;AACA8U,EAAAA,YAAY,GAAG;AACX,WAAO,KAAK/X,SAAL,IAAkB2T,QAAzB;AACH;AACD;;;AACAqF,EAAAA,UAAU,GAAG;AACT,UAAMC,GAAG,GAAG,KAAKlB,YAAL,EAAZ;;AACA,WAAOkB,GAAG,CAACnN,WAAJ,IAAmBf,MAA1B;AACH;;AACDmO,EAAAA,eAAe,CAACC,gBAAD,EAAmB;AAC9B,QAAI,KAAKhQ,OAAT,EAAkB;AACd;AACA;AACA,UAAI,KAAK0N,2BAAT,EAAsC;AAClC,eAAO,KAAKuC,0BAAL,CAAgCD,gBAAhC,IAAoD,OAApD,GAA8D,SAArE;AACH,OAFD,MAGK;AACD,eAAO,KAAKhQ,OAAZ;AACH;AACJ,KAV6B,CAW9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAAO,KAAKyN,cAAL,IAAuB,KAAKyC,gBAA5B,GAA+C,KAAKA,gBAApD,GAAuE,SAA9E;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACID,EAAAA,0BAA0B,CAACD,gBAAD,EAAmB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAQ,KAAKzB,cAAL,KAAwB;AAAE;AAA1B,OACJ,CAAC,CAACyB,gBAAgB,EAAEvH,QAAlB,CAA2B,KAAK+E,sBAAL,CAA4B/C,iBAAvD,CADN;AAEH;AACD;AACJ;AACA;AACA;AACA;;;AACI0E,EAAAA,WAAW,CAACrV,OAAD,EAAUoG,MAAV,EAAkB;AACzBpG,IAAAA,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,aAAzB,EAAwC,CAAC,CAACjQ,MAA1C;AACApG,IAAAA,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,mBAAzB,EAA8CjQ,MAAM,KAAK,OAAzD;AACApG,IAAAA,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,sBAAzB,EAAiDjQ,MAAM,KAAK,UAA5D;AACApG,IAAAA,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,mBAAzB,EAA8CjQ,MAAM,KAAK,OAAzD;AACApG,IAAAA,OAAO,CAACJ,SAAR,CAAkByW,MAAlB,CAAyB,qBAAzB,EAAgDjQ,MAAM,KAAK,SAA3D;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIyP,EAAAA,UAAU,CAACzP,MAAD,EAASkQ,iBAAiB,GAAG,KAA7B,EAAoC;AAC1C,SAAKrN,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;AACjC,WAAKhE,OAAL,GAAeE,MAAf;AACA,WAAKwN,2BAAL,GAAmCxN,MAAM,KAAK,OAAX,IAAsBkQ,iBAAzD,CAFiC,CAGjC;AACA;AACA;AACA;AACA;;AACA,UAAI,KAAK7B,cAAL,KAAwB;AAAE;AAA9B,QAA+C;AAC3CnC,QAAAA,YAAY,CAAC,KAAKiE,gBAAN,CAAZ;AACA,cAAMC,EAAE,GAAG,KAAK5C,2BAAL,GAAmCxD,eAAnC,GAAqD,CAAhE;AACA,aAAKmG,gBAAL,GAAwB1H,UAAU,CAAC,MAAO,KAAK3I,OAAL,GAAe,IAAvB,EAA8BsQ,EAA9B,CAAlC;AACH;AACJ,KAbD;AAcH;AACD;AACJ;AACA;AACA;AACA;;;AACInC,EAAAA,QAAQ,CAAC/P,KAAD,EAAQtE,OAAR,EAAiB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,UAAMoV,WAAW,GAAG,KAAKvB,YAAL,CAAkBrV,GAAlB,CAAsBwB,OAAtB,CAApB;;AACA,UAAMkW,gBAAgB,GAAGhd,eAAe,CAACoL,KAAD,CAAxC;;AACA,QAAI,CAAC8Q,WAAD,IAAiB,CAACA,WAAW,CAACR,aAAb,IAA8B5U,OAAO,KAAKkW,gBAA/D,EAAkF;AAC9E;AACH;;AACD,SAAKN,cAAL,CAAoB5V,OAApB,EAA6B,KAAKiW,eAAL,CAAqBC,gBAArB,CAA7B,EAAqEd,WAArE;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACId,EAAAA,OAAO,CAAChQ,KAAD,EAAQtE,OAAR,EAAiB;AACpB;AACA;AACA,UAAMoV,WAAW,GAAG,KAAKvB,YAAL,CAAkBrV,GAAlB,CAAsBwB,OAAtB,CAApB;;AACA,QAAI,CAACoV,WAAD,IACCA,WAAW,CAACR,aAAZ,IACGtQ,KAAK,CAACmS,aAAN,YAA+BC,IADlC,IAEG1W,OAAO,CAAC2O,QAAR,CAAiBrK,KAAK,CAACmS,aAAvB,CAHR,EAGgD;AAC5C;AACH;;AACD,SAAKpB,WAAL,CAAiBrV,OAAjB;;AACA,SAAK2W,WAAL,CAAiBvB,WAAW,CAACJ,OAA7B,EAAsC,IAAtC;AACH;;AACD2B,EAAAA,WAAW,CAAC3B,OAAD,EAAU5O,MAAV,EAAkB;AACzB,SAAK6C,OAAL,CAAa2N,GAAb,CAAiB,MAAM5B,OAAO,CAAC5Q,IAAR,CAAagC,MAAb,CAAvB;AACH;;AACD8O,EAAAA,wBAAwB,CAACE,WAAD,EAAc;AAClC,QAAI,CAAC,KAAKpY,SAAL,CAAe8C,SAApB,EAA+B;AAC3B;AACH;;AACD,UAAM+U,QAAQ,GAAGO,WAAW,CAACP,QAA7B;AACA,UAAMgC,sBAAsB,GAAG,KAAK9C,2BAAL,CAAiCvV,GAAjC,CAAqCqW,QAArC,KAAkD,CAAjF;;AACA,QAAI,CAACgC,sBAAL,EAA6B;AACzB,WAAK5N,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;AACjC2K,QAAAA,QAAQ,CAACzK,gBAAT,CAA0B,OAA1B,EAAmC,KAAK+J,6BAAxC,EAAuEX,2BAAvE;AACAqB,QAAAA,QAAQ,CAACzK,gBAAT,CAA0B,MAA1B,EAAkC,KAAK+J,6BAAvC,EAAsEX,2BAAtE;AACH,OAHD;AAIH;;AACD,SAAKO,2BAAL,CAAiClW,GAAjC,CAAqCgX,QAArC,EAA+CgC,sBAAsB,GAAG,CAAxE,EAZkC,CAalC;;;AACA,QAAI,EAAE,KAAK/C,sBAAP,KAAkC,CAAtC,EAAyC;AACrC;AACA;AACA,WAAK7K,OAAL,CAAaiB,iBAAb,CAA+B,MAAM;AACjC,cAAMpC,MAAM,GAAG,KAAKiO,UAAL,EAAf;;AACAjO,QAAAA,MAAM,CAACsC,gBAAP,CAAwB,OAAxB,EAAiC,KAAK4J,oBAAtC;AACH,OAHD,EAHqC,CAOrC;;;AACA,WAAKN,sBAAL,CAA4BtC,gBAA5B,CACK3N,IADL,CACUzI,SAAS,CAAC,KAAKkZ,0BAAN,CADnB,EAEK/R,SAFL,CAEe2U,QAAQ,IAAI;AACvB,aAAKjB,UAAL,CAAgBiB,QAAhB,EAA0B;AAAK;AAA/B;AACH,OAJD;AAKH;AACJ;;AACDxB,EAAAA,sBAAsB,CAACF,WAAD,EAAc;AAChC,UAAMP,QAAQ,GAAGO,WAAW,CAACP,QAA7B;;AACA,QAAI,KAAKd,2BAAL,CAAiC/V,GAAjC,CAAqC6W,QAArC,CAAJ,EAAoD;AAChD,YAAMgC,sBAAsB,GAAG,KAAK9C,2BAAL,CAAiCvV,GAAjC,CAAqCqW,QAArC,CAA/B;;AACA,UAAIgC,sBAAsB,GAAG,CAA7B,EAAgC;AAC5B,aAAK9C,2BAAL,CAAiClW,GAAjC,CAAqCgX,QAArC,EAA+CgC,sBAAsB,GAAG,CAAxE;AACH,OAFD,MAGK;AACDhC,QAAAA,QAAQ,CAAC5K,mBAAT,CAA6B,OAA7B,EAAsC,KAAKkK,6BAA3C,EAA0EX,2BAA1E;AACAqB,QAAAA,QAAQ,CAAC5K,mBAAT,CAA6B,MAA7B,EAAqC,KAAKkK,6BAA1C,EAAyEX,2BAAzE;;AACA,aAAKO,2BAAL,CAAiCzU,MAAjC,CAAwCuV,QAAxC;AACH;AACJ,KAZ+B,CAahC;;;AACA,QAAI,CAAC,GAAE,KAAKf,sBAAZ,EAAoC;AAChC,YAAMhM,MAAM,GAAG,KAAKiO,UAAL,EAAf;;AACAjO,MAAAA,MAAM,CAACmC,mBAAP,CAA2B,OAA3B,EAAoC,KAAK+J,oBAAzC,EAFgC,CAGhC;;AACA,WAAKE,0BAAL,CAAgC9P,IAAhC,GAJgC,CAKhC;;;AACAkO,MAAAA,YAAY,CAAC,KAAK2B,qBAAN,CAAZ;AACA3B,MAAAA,YAAY,CAAC,KAAKiE,gBAAN,CAAZ;AACH;AACJ;AACD;;;AACAX,EAAAA,cAAc,CAAC5V,OAAD,EAAUoG,MAAV,EAAkBgP,WAAlB,EAA+B;AACzC,SAAKC,WAAL,CAAiBrV,OAAjB,EAA0BoG,MAA1B;;AACA,SAAKuQ,WAAL,CAAiBvB,WAAW,CAACJ,OAA7B,EAAsC5O,MAAtC;;AACA,SAAKgQ,gBAAL,GAAwBhQ,MAAxB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIqP,EAAAA,uBAAuB,CAACzV,OAAD,EAAU;AAC7B,UAAM+W,OAAO,GAAG,EAAhB;;AACA,SAAKlD,YAAL,CAAkB6B,OAAlB,CAA0B,CAACT,IAAD,EAAOU,cAAP,KAA0B;AAChD,UAAIA,cAAc,KAAK3V,OAAnB,IAA+BiV,IAAI,CAACL,aAAL,IAAsBe,cAAc,CAAChH,QAAf,CAAwB3O,OAAxB,CAAzD,EAA4F;AACxF+W,QAAAA,OAAO,CAAChb,IAAR,CAAa,CAAC4Z,cAAD,EAAiBV,IAAjB,CAAb;AACH;AACJ,KAJD;;AAKA,WAAO8B,OAAP;AACH;;AA3Tc;;AA6TnBtD,YAAY,CAAChT,IAAb;AAAA,mBAAyGgT,YAAzG,EAn6DgGrb,EAm6DhG,UAAuIA,EAAE,CAACmU,MAA1I,GAn6DgGnU,EAm6DhG,UAA6JW,EAAE,CAAC2H,QAAhK,GAn6DgGtI,EAm6DhG,UAAqLoY,qBAArL,GAn6DgGpY,EAm6DhG,UAAuND,QAAvN,MAn6DgGC,EAm6DhG,UAA4Pmb,6BAA5P;AAAA;;AACAE,YAAY,CAAC9S,KAAb,kBAp6DgGvI,EAo6DhG;AAAA,SAA6Gqb,YAA7G;AAAA,WAA6GA,YAA7G;AAAA,cAAuI;AAAvI;;AACA;AAAA,qDAr6DgGrb,EAq6DhG,mBAA2Fqb,YAA3F,EAAqH,CAAC;AAC1G7S,IAAAA,IAAI,EAAEvI,UADoG;AAE1GwI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFoG,GAAD,CAArH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAExI,EAAE,CAACmU;AAAX,KAAD,EAAsB;AAAE3L,MAAAA,IAAI,EAAE7H,EAAE,CAAC2H;AAAX,KAAtB,EAA6C;AAAEE,MAAAA,IAAI,EAAE4P;AAAR,KAA7C,EAA8E;AAAE5P,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC3IJ,QAAAA,IAAI,EAAEjI;AADqI,OAAD,EAE3I;AACCiI,QAAAA,IAAI,EAAEtI,MADP;AAECuI,QAAAA,IAAI,EAAE,CAAC1I,QAAD;AAFP,OAF2I;AAA/B,KAA9E,EAK3B;AAAEyI,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAClCJ,QAAAA,IAAI,EAAEjI;AAD4B,OAAD,EAElC;AACCiI,QAAAA,IAAI,EAAEtI,MADP;AAECuI,QAAAA,IAAI,EAAE,CAAC0S,6BAAD;AAFP,OAFkC;AAA/B,KAL2B,CAAP;AAUlB,GAbxB;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMyD,eAAN,CAAsB;AAClBla,EAAAA,WAAW,CAAC2P,WAAD,EAAcwK,aAAd,EAA6B;AACpC,SAAKxK,WAAL,GAAmBA,WAAnB;AACA,SAAKwK,aAAL,GAAqBA,aAArB;AACA,SAAKC,cAAL,GAAsB,IAAIte,YAAJ,EAAtB;AACH;;AACDue,EAAAA,eAAe,GAAG;AACd,UAAMnX,OAAO,GAAG,KAAKyM,WAAL,CAAiBI,aAAjC;AACA,SAAKuK,oBAAL,GAA4B,KAAKH,aAAL,CACvBtC,OADuB,CACf3U,OADe,EACNA,OAAO,CAACO,QAAR,KAAqB,CAArB,IAA0BP,OAAO,CAAC0G,YAAR,CAAqB,wBAArB,CADpB,EAEvBvE,SAFuB,CAEbiE,MAAM,IAAI,KAAK8Q,cAAL,CAAoBG,IAApB,CAAyBjR,MAAzB,CAFG,CAA5B;AAGH;;AACDxH,EAAAA,WAAW,GAAG;AACV,SAAKqY,aAAL,CAAmB9B,cAAnB,CAAkC,KAAK1I,WAAvC;;AACA,QAAI,KAAK2K,oBAAT,EAA+B;AAC3B,WAAKA,oBAAL,CAA0B5T,WAA1B;AACH;AACJ;;AAjBiB;;AAmBtBwT,eAAe,CAACvW,IAAhB;AAAA,mBAA4GuW,eAA5G,EA/8DgG5e,EA+8DhG,mBAA6IA,EAAE,CAACkV,UAAhJ,GA/8DgGlV,EA+8DhG,mBAAuKqb,YAAvK;AAAA;;AACAuD,eAAe,CAACzJ,IAAhB,kBAh9DgGnV,EAg9DhG;AAAA,QAAgG4e,eAAhG;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAj9DgG5e,EAi9DhG,mBAA2F4e,eAA3F,EAAwH,CAAC;AAC7GpW,IAAAA,IAAI,EAAEpI,SADuG;AAE7GqI,IAAAA,IAAI,EAAE,CAAC;AACC2M,MAAAA,QAAQ,EAAE;AADX,KAAD;AAFuG,GAAD,CAAxH,EAK4B,YAAY;AAAE,WAAO,CAAC;AAAE5M,MAAAA,IAAI,EAAExI,EAAE,CAACkV;AAAX,KAAD,EAA0B;AAAE1M,MAAAA,IAAI,EAAE6S;AAAR,KAA1B,CAAP;AAA2D,GALrG,EAKuH;AAAEyD,IAAAA,cAAc,EAAE,CAAC;AAC1HtW,MAAAA,IAAI,EAAE/H;AADoH,KAAD;AAAlB,GALvH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMye,wBAAwB,GAAG,kCAAjC;AACA;;AACA,MAAMC,wBAAwB,GAAG,kCAAjC;AACA;;AACA,MAAMC,mCAAmC,GAAG,0BAA5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,wBAAN,CAA+B;AAC3B3a,EAAAA,WAAW,CAACE,SAAD,EAAY0T,QAAZ,EAAsB;AAC7B,SAAK1T,SAAL,GAAiBA,SAAjB;AACA,SAAKD,SAAL,GAAiB2T,QAAjB;AACH;AACD;;;AACAgH,EAAAA,mBAAmB,GAAG;AAClB,QAAI,CAAC,KAAK1a,SAAL,CAAe8C,SAApB,EAA+B;AAC3B,aAAO;AAAE;AAAT;AACH,KAHiB,CAIlB;AACA;AACA;;;AACA,UAAM6X,WAAW,GAAG,KAAK5a,SAAL,CAAemC,aAAf,CAA6B,KAA7B,CAApB;;AACAyY,IAAAA,WAAW,CAACjY,KAAZ,CAAkBkY,eAAlB,GAAoC,YAApC;AACAD,IAAAA,WAAW,CAACjY,KAAZ,CAAkBmY,QAAlB,GAA6B,UAA7B;;AACA,SAAK9a,SAAL,CAAegD,IAAf,CAAoBV,WAApB,CAAgCsY,WAAhC,EAVkB,CAWlB;AACA;AACA;AACA;;;AACA,UAAMG,cAAc,GAAG,KAAK/a,SAAL,CAAe8L,WAAf,IAA8Bf,MAArD;AACA,UAAMiQ,aAAa,GAAGD,cAAc,IAAIA,cAAc,CAACjR,gBAAjC,GAChBiR,cAAc,CAACjR,gBAAf,CAAgC8Q,WAAhC,CADgB,GAEhB,IAFN;AAGA,UAAMK,aAAa,GAAG,CAAED,aAAa,IAAIA,aAAa,CAACH,eAAhC,IAAoD,EAArD,EAAyDK,OAAzD,CAAiE,IAAjE,EAAuE,EAAvE,CAAtB;AACAN,IAAAA,WAAW,CAAChZ,MAAZ;;AACA,YAAQqZ,aAAR;AACI,WAAK,YAAL;AACI,eAAO;AAAE;AAAT;;AACJ,WAAK,kBAAL;AACI,eAAO;AAAE;AAAT;AAJR;;AAMA,WAAO;AAAE;AAAT;AACH;AACD;;;AACAE,EAAAA,oCAAoC,GAAG;AACnC,QAAI,CAAC,KAAKC,2BAAN,IAAqC,KAAKnb,SAAL,CAAe8C,SAApD,IAAiE,KAAK/C,SAAL,CAAegD,IAApF,EAA0F;AACtF,YAAMqY,WAAW,GAAG,KAAKrb,SAAL,CAAegD,IAAf,CAAoBH,SAAxC,CADsF,CAEtF;;AACAwY,MAAAA,WAAW,CAACzZ,MAAZ,CAAmB6Y,mCAAnB;AACAY,MAAAA,WAAW,CAACzZ,MAAZ,CAAmB2Y,wBAAnB;AACAc,MAAAA,WAAW,CAACzZ,MAAZ,CAAmB4Y,wBAAnB;AACA,WAAKY,2BAAL,GAAmC,IAAnC;AACA,YAAME,IAAI,GAAG,KAAKX,mBAAL,EAAb;;AACA,UAAIW,IAAI,KAAK;AAAE;AAAf,QAAqC;AACjCD,QAAAA,WAAW,CAACvY,GAAZ,CAAgB2X,mCAAhB;AACAY,QAAAA,WAAW,CAACvY,GAAZ,CAAgByX,wBAAhB;AACH,OAHD,MAIK,IAAIe,IAAI,KAAK;AAAE;AAAf,QAAqC;AACtCD,QAAAA,WAAW,CAACvY,GAAZ,CAAgB2X,mCAAhB;AACAY,QAAAA,WAAW,CAACvY,GAAZ,CAAgB0X,wBAAhB;AACH;AACJ;AACJ;;AAtD0B;;AAwD/BE,wBAAwB,CAAChX,IAAzB;AAAA,mBAAqHgX,wBAArH,EA1iEgGrf,EA0iEhG,UAA+JW,EAAE,CAAC2H,QAAlK,GA1iEgGtI,EA0iEhG,UAAuLD,QAAvL;AAAA;;AACAsf,wBAAwB,CAAC9W,KAAzB,kBA3iEgGvI,EA2iEhG;AAAA,SAAyHqf,wBAAzH;AAAA,WAAyHA,wBAAzH;AAAA,cAA+J;AAA/J;;AACA;AAAA,qDA5iEgGrf,EA4iEhG,mBAA2Fqf,wBAA3F,EAAiI,CAAC;AACtH7W,IAAAA,IAAI,EAAEvI,UADgH;AAEtHwI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFgH,GAAD,CAAjI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAE7H,EAAE,CAAC2H;AAAX,KAAD,EAAwB;AAAEE,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACrFJ,QAAAA,IAAI,EAAEtI,MAD+E;AAErFuI,QAAAA,IAAI,EAAE,CAAC1I,QAAD;AAF+E,OAAD;AAA/B,KAAxB,CAAP;AAGlB,GANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmgB,UAAN,CAAiB;AACbxb,EAAAA,WAAW,CAACyb,wBAAD,EAA2B;AAClCA,IAAAA,wBAAwB,CAACL,oCAAzB;AACH;;AAHY;;AAKjBI,UAAU,CAAC7X,IAAX;AAAA,mBAAuG6X,UAAvG,EAhkEgGlgB,EAgkEhG,UAAmIqf,wBAAnI;AAAA;;AACAa,UAAU,CAACE,IAAX,kBAjkEgGpgB,EAikEhG;AAAA,QAAwGkgB;AAAxG;AACAA,UAAU,CAACG,IAAX,kBAlkEgGrgB,EAkkEhG;AAAA,YAA8H,CAACgD,eAAD,CAA9H;AAAA;;AACA;AAAA,qDAnkEgGhD,EAmkEhG,mBAA2FkgB,UAA3F,EAAmH,CAAC;AACxG1X,IAAAA,IAAI,EAAE9H,QADkG;AAExG+H,IAAAA,IAAI,EAAE,CAAC;AACC6X,MAAAA,OAAO,EAAE,CAACtd,eAAD,CADV;AAECud,MAAAA,YAAY,EAAE,CAAC7F,WAAD,EAActG,YAAd,EAA4BwK,eAA5B,CAFf;AAGC4B,MAAAA,OAAO,EAAE,CAAC9F,WAAD,EAActG,YAAd,EAA4BwK,eAA5B;AAHV,KAAD;AAFkG,GAAD,CAAnH,EAO4B,YAAY;AAAE,WAAO,CAAC;AAAEpW,MAAAA,IAAI,EAAE6W;AAAR,KAAD,CAAP;AAA8C,GAPxF;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASa,UAAT,EAAqBzS,0BAArB,EAAiDhJ,aAAjD,EAAgEF,8BAAhE,EAAgGD,yBAAhG,EAA2HoW,WAA3H,EAAwIkE,eAAxI,EAAyJxK,YAAzJ,EAAuKkB,qBAAvK,EAA8L0B,4BAA9L,EAA4Nf,mCAA5N,EAAiQkF,6BAAjQ,EAAgSnF,yBAAhS,EAA2TpI,eAA3T,EAA4UyN,YAA5U,EAA0V3K,SAA1V,EAAqWsD,gBAArW,EAAuXqL,wBAAvX,EAAiZvH,uCAAjZ,EAA0bD,+BAA1b,EAA2dO,qBAA3d,EAAkfhK,oBAAlf,EAAwgBF,iBAAxgB,EAA2hBsL,8BAA3hB,EAA2jBH,4BAA3jB,EAAylBE,oCAAzlB,EAA+nB1Q,cAA/nB,EAA+oB4Q,aAA/oB,EAA8pBpV,qBAA9pB,EAAqrB6S,+BAArrB,EAAstBI,gCAAttB", "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, QueryList, Directive, Input, InjectionToken, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getFocusedElementPierceShadowDom, normalizePassiveListenerOptions, _getEventTarget, _getShadowRoot } from '@angular/cdk/platform';\nimport { Subject, Subscription, BehaviorSubject, of } from 'rxjs';\nimport { hasModifierKey, A, Z, ZERO, NINE, END, HOME, LEFT_ARROW, RIGHT_ARROW, UP_ARROW, DOWN_ARROW, TAB, ALT, CONTROL, MAC_META, META, SHIFT } from '@angular/cdk/keycodes';\nimport { tap, debounceTime, filter, map, take, skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceElement } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    if (ids.some(existingId => existingId.trim() == id.trim())) {\n        return;\n    }\n    ids.push(id.trim());\n    el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n    const ids = getAriaReferenceIds(el, attr);\n    const filteredIds = ids.filter(val => val != id.trim());\n    if (filteredIds.length) {\n        el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n    }\n    else {\n        el.removeAttribute(attr);\n    }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n    // Get string array of all individual ids (whitespace delimited) in the attribute value\n    return (el.getAttribute(attr) || '').match(/\\S+/g) || [];\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n    constructor(_document, \n    /**\n     * @deprecated To be turned into a required parameter.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n        this._platform = _platform;\n        /** Map of all registered message elements that have been placed into the document. */\n        this._messageRegistry = new Map();\n        /** Container for all registered messages. */\n        this._messagesContainer = null;\n        /** Unique ID for the service. */\n        this._id = `${nextId++}`;\n        this._document = _document;\n    }\n    describe(hostElement, message, role) {\n        if (!this._canBeDescribed(hostElement, message)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (typeof message !== 'string') {\n            // We need to ensure that the element has an ID.\n            setMessageId(message);\n            this._messageRegistry.set(key, { messageElement: message, referenceCount: 0 });\n        }\n        else if (!this._messageRegistry.has(key)) {\n            this._createMessageElement(message, role);\n        }\n        if (!this._isElementDescribedByMessage(hostElement, key)) {\n            this._addMessageReference(hostElement, key);\n        }\n    }\n    removeDescription(hostElement, message, role) {\n        if (!message || !this._isElementNode(hostElement)) {\n            return;\n        }\n        const key = getKey(message, role);\n        if (this._isElementDescribedByMessage(hostElement, key)) {\n            this._removeMessageReference(hostElement, key);\n        }\n        // If the message is a string, it means that it's one that we created for the\n        // consumer so we can remove it safely, otherwise we should leave it in place.\n        if (typeof message === 'string') {\n            const registeredMessage = this._messageRegistry.get(key);\n            if (registeredMessage && registeredMessage.referenceCount === 0) {\n                this._deleteMessageElement(key);\n            }\n        }\n        if (this._messagesContainer?.childNodes.length === 0) {\n            this._messagesContainer.remove();\n            this._messagesContainer = null;\n        }\n    }\n    /** Unregisters all created message elements and removes the message container. */\n    ngOnDestroy() {\n        const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n        for (let i = 0; i < describedElements.length; i++) {\n            this._removeCdkDescribedByReferenceIds(describedElements[i]);\n            describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n        }\n        this._messagesContainer?.remove();\n        this._messagesContainer = null;\n        this._messageRegistry.clear();\n    }\n    /**\n     * Creates a new element in the visually hidden message container element with the message\n     * as its content and adds it to the message registry.\n     */\n    _createMessageElement(message, role) {\n        const messageElement = this._document.createElement('div');\n        setMessageId(messageElement);\n        messageElement.textContent = message;\n        if (role) {\n            messageElement.setAttribute('role', role);\n        }\n        this._createMessagesContainer();\n        this._messagesContainer.appendChild(messageElement);\n        this._messageRegistry.set(getKey(message, role), { messageElement, referenceCount: 0 });\n    }\n    /** Deletes the message element from the global messages container. */\n    _deleteMessageElement(key) {\n        this._messageRegistry.get(key)?.messageElement?.remove();\n        this._messageRegistry.delete(key);\n    }\n    /** Creates the global container for all aria-describedby messages. */\n    _createMessagesContainer() {\n        if (this._messagesContainer) {\n            return;\n        }\n        const containerClassName = 'cdk-describedby-message-container';\n        const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n        for (let i = 0; i < serverContainers.length; i++) {\n            // When going from the server to the client, we may end up in a situation where there's\n            // already a container on the page, but we don't have a reference to it. Clear the\n            // old container so we don't get duplicates. Doing this, instead of emptying the previous\n            // container, should be slightly faster.\n            serverContainers[i].remove();\n        }\n        const messagesContainer = this._document.createElement('div');\n        // We add `visibility: hidden` in order to prevent text in this container from\n        // being searchable by the browser's Ctrl + F functionality.\n        // Screen-readers will still read the description for elements with aria-describedby even\n        // when the description element is not visible.\n        messagesContainer.style.visibility = 'hidden';\n        // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n        // the description element doesn't impact page layout.\n        messagesContainer.classList.add(containerClassName);\n        messagesContainer.classList.add('cdk-visually-hidden');\n        // @breaking-change 14.0.0 Remove null check for `_platform`.\n        if (this._platform && !this._platform.isBrowser) {\n            messagesContainer.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(messagesContainer);\n        this._messagesContainer = messagesContainer;\n    }\n    /** Removes all cdk-describedby messages that are hosted through the element. */\n    _removeCdkDescribedByReferenceIds(element) {\n        // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n        const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n        element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n    }\n    /**\n     * Adds a message reference to the element using aria-describedby and increments the registered\n     * message's reference count.\n     */\n    _addMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        // Add the aria-describedby reference and set the\n        // describedby_host attribute to mark the element.\n        addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n        registeredMessage.referenceCount++;\n    }\n    /**\n     * Removes a message reference from the element using aria-describedby\n     * and decrements the registered message's reference count.\n     */\n    _removeMessageReference(element, key) {\n        const registeredMessage = this._messageRegistry.get(key);\n        registeredMessage.referenceCount--;\n        removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n        element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    /** Returns true if the element has been described by the provided message ID. */\n    _isElementDescribedByMessage(element, key) {\n        const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n        const registeredMessage = this._messageRegistry.get(key);\n        const messageId = registeredMessage && registeredMessage.messageElement.id;\n        return !!messageId && referenceIds.indexOf(messageId) != -1;\n    }\n    /** Determines whether a message can be described on a particular element. */\n    _canBeDescribed(element, message) {\n        if (!this._isElementNode(element)) {\n            return false;\n        }\n        if (message && typeof message === 'object') {\n            // We'd have to make some assumptions about the description element's text, if the consumer\n            // passed in an element. Assume that if an element is passed in, the consumer has verified\n            // that it can be used as a description.\n            return true;\n        }\n        const trimmedMessage = message == null ? '' : `${message}`.trim();\n        const ariaLabel = element.getAttribute('aria-label');\n        // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n        // element, because screen readers will end up reading out the same text twice in a row.\n        return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n    }\n    /** Checks whether a node is an Element node. */\n    _isElementNode(element) {\n        return element.nodeType === this._document.ELEMENT_NODE;\n    }\n}\nAriaDescriber.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: AriaDescriber, deps: [{ token: DOCUMENT }, { token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nAriaDescriber.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: AriaDescriber, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: AriaDescriber, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.Platform }]; } });\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n    return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element) {\n    if (!element.id) {\n        element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${nextId++}`;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n    constructor(_items) {\n        this._items = _items;\n        this._activeItemIndex = -1;\n        this._activeItem = null;\n        this._wrap = false;\n        this._letterKeyStream = new Subject();\n        this._typeaheadSubscription = Subscription.EMPTY;\n        this._vertical = true;\n        this._allowedModifierKeys = [];\n        this._homeAndEnd = false;\n        /**\n         * Predicate function that can be used to check whether an item should be skipped\n         * by the key manager. By default, disabled items are skipped.\n         */\n        this._skipPredicateFn = (item) => item.disabled;\n        // Buffer for the letters that the user has pressed when the typeahead option is turned on.\n        this._pressedLetters = [];\n        /**\n         * Stream that emits any time the TAB key is pressed, so components can react\n         * when focus is shifted off of the list.\n         */\n        this.tabOut = new Subject();\n        /** Stream that emits whenever the active item of the list manager changes. */\n        this.change = new Subject();\n        // We allow for the items to be an array because, in some cases, the consumer may\n        // not have access to a QueryList of the items they want to manage (e.g. when the\n        // items aren't being collected via `ViewChildren` or `ContentChildren`).\n        if (_items instanceof QueryList) {\n            _items.changes.subscribe((newItems) => {\n                if (this._activeItem) {\n                    const itemArray = newItems.toArray();\n                    const newIndex = itemArray.indexOf(this._activeItem);\n                    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n                        this._activeItemIndex = newIndex;\n                    }\n                }\n            });\n        }\n    }\n    /**\n     * Sets the predicate function that determines which items should be skipped by the\n     * list key manager.\n     * @param predicate Function that determines whether the given item should be skipped.\n     */\n    skipPredicate(predicate) {\n        this._skipPredicateFn = predicate;\n        return this;\n    }\n    /**\n     * Configures wrapping mode, which determines whether the active item will wrap to\n     * the other end of list when there are no more items in the given direction.\n     * @param shouldWrap Whether the list should wrap when reaching the end.\n     */\n    withWrap(shouldWrap = true) {\n        this._wrap = shouldWrap;\n        return this;\n    }\n    /**\n     * Configures whether the key manager should be able to move the selection vertically.\n     * @param enabled Whether vertical selection should be enabled.\n     */\n    withVerticalOrientation(enabled = true) {\n        this._vertical = enabled;\n        return this;\n    }\n    /**\n     * Configures the key manager to move the selection horizontally.\n     * Passing in `null` will disable horizontal movement.\n     * @param direction Direction in which the selection can be moved.\n     */\n    withHorizontalOrientation(direction) {\n        this._horizontal = direction;\n        return this;\n    }\n    /**\n     * Modifier keys which are allowed to be held down and whose default actions will be prevented\n     * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n     */\n    withAllowedModifierKeys(keys) {\n        this._allowedModifierKeys = keys;\n        return this;\n    }\n    /**\n     * Turns on typeahead mode which allows users to set the active item by typing.\n     * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n     */\n    withTypeAhead(debounceInterval = 200) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this._items.length &&\n            this._items.some(item => typeof item.getLabel !== 'function')) {\n            throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n        }\n        this._typeaheadSubscription.unsubscribe();\n        // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n        // and convert those letters back into a string. Afterwards find the first item that starts\n        // with that string and select it.\n        this._typeaheadSubscription = this._letterKeyStream\n            .pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(debounceInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('')))\n            .subscribe(inputString => {\n            const items = this._getItemsArray();\n            // Start at 1 because we want to start searching at the item immediately\n            // following the current active item.\n            for (let i = 1; i < items.length + 1; i++) {\n                const index = (this._activeItemIndex + i) % items.length;\n                const item = items[index];\n                if (!this._skipPredicateFn(item) &&\n                    item.getLabel().toUpperCase().trim().indexOf(inputString) === 0) {\n                    this.setActiveItem(index);\n                    break;\n                }\n            }\n            this._pressedLetters = [];\n        });\n        return this;\n    }\n    /**\n     * Configures the key manager to activate the first and last items\n     * respectively when the Home or End key is pressed.\n     * @param enabled Whether pressing the Home or End key activates the first/last item.\n     */\n    withHomeAndEnd(enabled = true) {\n        this._homeAndEnd = enabled;\n        return this;\n    }\n    setActiveItem(item) {\n        const previousActiveItem = this._activeItem;\n        this.updateActiveItem(item);\n        if (this._activeItem !== previousActiveItem) {\n            this.change.next(this._activeItemIndex);\n        }\n    }\n    /**\n     * Sets the active item depending on the key event passed in.\n     * @param event Keyboard event to be used for determining which element should be active.\n     */\n    onKeydown(event) {\n        const keyCode = event.keyCode;\n        const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n        const isModifierAllowed = modifiers.every(modifier => {\n            return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n        });\n        switch (keyCode) {\n            case TAB:\n                this.tabOut.next();\n                return;\n            case DOWN_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case UP_ARROW:\n                if (this._vertical && isModifierAllowed) {\n                    this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case RIGHT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case LEFT_ARROW:\n                if (this._horizontal && isModifierAllowed) {\n                    this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case HOME:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setFirstItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            case END:\n                if (this._homeAndEnd && isModifierAllowed) {\n                    this.setLastItemActive();\n                    break;\n                }\n                else {\n                    return;\n                }\n            default:\n                if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n                    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n                    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n                    if (event.key && event.key.length === 1) {\n                        this._letterKeyStream.next(event.key.toLocaleUpperCase());\n                    }\n                    else if ((keyCode >= A && keyCode <= Z) || (keyCode >= ZERO && keyCode <= NINE)) {\n                        this._letterKeyStream.next(String.fromCharCode(keyCode));\n                    }\n                }\n                // Note that we return here, in order to avoid preventing\n                // the default action of non-navigational keys.\n                return;\n        }\n        this._pressedLetters = [];\n        event.preventDefault();\n    }\n    /** Index of the currently active item. */\n    get activeItemIndex() {\n        return this._activeItemIndex;\n    }\n    /** The active item. */\n    get activeItem() {\n        return this._activeItem;\n    }\n    /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n    isTyping() {\n        return this._pressedLetters.length > 0;\n    }\n    /** Sets the active item to the first enabled item in the list. */\n    setFirstItemActive() {\n        this._setActiveItemByIndex(0, 1);\n    }\n    /** Sets the active item to the last enabled item in the list. */\n    setLastItemActive() {\n        this._setActiveItemByIndex(this._items.length - 1, -1);\n    }\n    /** Sets the active item to the next enabled item in the list. */\n    setNextItemActive() {\n        this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n    }\n    /** Sets the active item to a previous enabled item in the list. */\n    setPreviousItemActive() {\n        this._activeItemIndex < 0 && this._wrap\n            ? this.setLastItemActive()\n            : this._setActiveItemByDelta(-1);\n    }\n    updateActiveItem(item) {\n        const itemArray = this._getItemsArray();\n        const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n        const activeItem = itemArray[index];\n        // Explicitly check for `null` and `undefined` because other falsy values are valid.\n        this._activeItem = activeItem == null ? null : activeItem;\n        this._activeItemIndex = index;\n    }\n    /**\n     * This method sets the active item, given a list of items and the delta between the\n     * currently active item and the new active item. It will calculate differently\n     * depending on whether wrap mode is turned on.\n     */\n    _setActiveItemByDelta(delta) {\n        this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n    }\n    /**\n     * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n     * down the list until it finds an item that is not disabled, and it will wrap if it\n     * encounters either end of the list.\n     */\n    _setActiveInWrapMode(delta) {\n        const items = this._getItemsArray();\n        for (let i = 1; i <= items.length; i++) {\n            const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n            const item = items[index];\n            if (!this._skipPredicateFn(item)) {\n                this.setActiveItem(index);\n                return;\n            }\n        }\n    }\n    /**\n     * Sets the active item properly given the default mode. In other words, it will\n     * continue to move down the list until it finds an item that is not disabled. If\n     * it encounters either end of the list, it will stop and not wrap.\n     */\n    _setActiveInDefaultMode(delta) {\n        this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n    }\n    /**\n     * Sets the active item to the first enabled item starting at the index specified. If the\n     * item is disabled, it will move in the fallbackDelta direction until it either\n     * finds an enabled item or encounters the end of the list.\n     */\n    _setActiveItemByIndex(index, fallbackDelta) {\n        const items = this._getItemsArray();\n        if (!items[index]) {\n            return;\n        }\n        while (this._skipPredicateFn(items[index])) {\n            index += fallbackDelta;\n            if (!items[index]) {\n                return;\n            }\n        }\n        this.setActiveItem(index);\n    }\n    /** Returns the items as an array. */\n    _getItemsArray() {\n        return this._items instanceof QueryList ? this._items.toArray() : this._items;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ActiveDescendantKeyManager extends ListKeyManager {\n    setActiveItem(index) {\n        if (this.activeItem) {\n            this.activeItem.setInactiveStyles();\n        }\n        super.setActiveItem(index);\n        if (this.activeItem) {\n            this.activeItem.setActiveStyles();\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass FocusKeyManager extends ListKeyManager {\n    constructor() {\n        super(...arguments);\n        this._origin = 'program';\n    }\n    /**\n     * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n     * @param origin Focus origin to be used when focusing items.\n     */\n    setFocusOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    setActiveItem(item) {\n        super.setActiveItem(item);\n        if (this.activeItem) {\n            this.activeItem.focus(this._origin);\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n    constructor() {\n        /**\n         * Whether to count an element as focusable even if it is not currently visible.\n         */\n        this.ignoreVisibility = false;\n    }\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether is is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n    constructor(_platform) {\n        this._platform = _platform;\n    }\n    /**\n     * Gets whether an element is disabled.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is disabled.\n     */\n    isDisabled(element) {\n        // This does not capture some cases, such as a non-form control with a disabled attribute or\n        // a form control inside of a disabled form, but should capture the most common cases.\n        return element.hasAttribute('disabled');\n    }\n    /**\n     * Gets whether an element is visible for the purposes of interactivity.\n     *\n     * This will capture states like `display: none` and `visibility: hidden`, but not things like\n     * being clipped by an `overflow: hidden` parent or being outside the viewport.\n     *\n     * @returns Whether the element is visible.\n     */\n    isVisible(element) {\n        return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n    }\n    /**\n     * Gets whether an element can be reached via Tab key.\n     * Assumes that the element has already been checked with isFocusable.\n     *\n     * @param element Element to be checked.\n     * @returns Whether the element is tabbable.\n     */\n    isTabbable(element) {\n        // Nothing is tabbable on the server 😎\n        if (!this._platform.isBrowser) {\n            return false;\n        }\n        const frameElement = getFrameElement(getWindow(element));\n        if (frameElement) {\n            // Frame elements inherit their tabindex onto all child elements.\n            if (getTabIndexValue(frameElement) === -1) {\n                return false;\n            }\n            // Browsers disable tabbing to an element inside of an invisible frame.\n            if (!this.isVisible(frameElement)) {\n                return false;\n            }\n        }\n        let nodeName = element.nodeName.toLowerCase();\n        let tabIndexValue = getTabIndexValue(element);\n        if (element.hasAttribute('contenteditable')) {\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'iframe' || nodeName === 'object') {\n            // The frame or object's content may be tabbable depending on the content, but it's\n            // not possibly to reliably detect the content of the frames. We always consider such\n            // elements as non-tabbable.\n            return false;\n        }\n        // In iOS, the browser only considers some specific elements as tabbable.\n        if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n            return false;\n        }\n        if (nodeName === 'audio') {\n            // Audio elements without controls enabled are never tabbable, regardless\n            // of the tabindex attribute explicitly being set.\n            if (!element.hasAttribute('controls')) {\n                return false;\n            }\n            // Audio elements with controls are by default tabbable unless the\n            // tabindex attribute is set to `-1` explicitly.\n            return tabIndexValue !== -1;\n        }\n        if (nodeName === 'video') {\n            // For all video elements, if the tabindex attribute is set to `-1`, the video\n            // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n            // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n            // tabindex attribute is the source of truth here.\n            if (tabIndexValue === -1) {\n                return false;\n            }\n            // If the tabindex is explicitly set, and not `-1` (as per check before), the\n            // video element is always tabbable (regardless of whether it has controls or not).\n            if (tabIndexValue !== null) {\n                return true;\n            }\n            // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n            // has controls enabled. Firefox is special as videos are always tabbable regardless\n            // of whether there are controls or not.\n            return this._platform.FIREFOX || element.hasAttribute('controls');\n        }\n        return element.tabIndex >= 0;\n    }\n    /**\n     * Gets whether an element can be focused by the user.\n     *\n     * @param element Element to be checked.\n     * @param config The config object with options to customize this method's behavior\n     * @returns Whether the element is focusable.\n     */\n    isFocusable(element, config) {\n        // Perform checks in order of left to most expensive.\n        // Again, naive approach that does not capture many edge cases and browser quirks.\n        return (isPotentiallyFocusable(element) &&\n            !this.isDisabled(element) &&\n            (config?.ignoreVisibility || this.isVisible(element)));\n    }\n}\nInteractivityChecker.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: InteractivityChecker, deps: [{ token: i1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nInteractivityChecker.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: InteractivityChecker, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: InteractivityChecker, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }]; } });\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n    try {\n        return window.frameElement;\n    }\n    catch {\n        return null;\n    }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n    // Use logic from jQuery to check for an invisible element.\n    // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n    return !!(element.offsetWidth ||\n        element.offsetHeight ||\n        (typeof element.getClientRects === 'function' && element.getClientRects().length));\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    return (nodeName === 'input' ||\n        nodeName === 'select' ||\n        nodeName === 'button' ||\n        nodeName === 'textarea');\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n    return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n    return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n    return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n    return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n    if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n        return false;\n    }\n    let tabIndex = element.getAttribute('tabindex');\n    return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n    if (!hasValidTabIndex(element)) {\n        return null;\n    }\n    // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n    const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n    return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n    let nodeName = element.nodeName.toLowerCase();\n    let inputType = nodeName === 'input' && element.type;\n    return (inputType === 'text' ||\n        inputType === 'password' ||\n        nodeName === 'select' ||\n        nodeName === 'textarea');\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n    // Inputs are potentially focusable *unless* they're type=\"hidden\".\n    if (isHiddenInput(element)) {\n        return false;\n    }\n    return (isNativeFormElement(element) ||\n        isAnchorWithHref(element) ||\n        element.hasAttribute('contenteditable') ||\n        hasValidTabIndex(element));\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n    // ownerDocument is null if `node` itself *is* a document.\n    return (node.ownerDocument && node.ownerDocument.defaultView) || window;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n *\n * @deprecated Use `ConfigurableFocusTrap` instead.\n * @breaking-change 11.0.0\n */\nclass FocusTrap {\n    constructor(_element, _checker, _ngZone, _document, deferAnchors = false) {\n        this._element = _element;\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._hasAttached = false;\n        // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n        this.startAnchorListener = () => this.focusLastTabbableElement();\n        this.endAnchorListener = () => this.focusFirstTabbableElement();\n        this._enabled = true;\n        if (!deferAnchors) {\n            this.attachAnchors();\n        }\n    }\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(value, this._startAnchor);\n            this._toggleAnchorTabIndex(value, this._endAnchor);\n        }\n    }\n    /** Destroys the focus trap by cleaning up the anchors. */\n    destroy() {\n        const startAnchor = this._startAnchor;\n        const endAnchor = this._endAnchor;\n        if (startAnchor) {\n            startAnchor.removeEventListener('focus', this.startAnchorListener);\n            startAnchor.remove();\n        }\n        if (endAnchor) {\n            endAnchor.removeEventListener('focus', this.endAnchorListener);\n            endAnchor.remove();\n        }\n        this._startAnchor = this._endAnchor = null;\n        this._hasAttached = false;\n    }\n    /**\n     * Inserts the anchors into the DOM. This is usually done automatically\n     * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n     * @returns Whether the focus trap managed to attach successfully. This may not be the case\n     * if the target element isn't currently in the DOM.\n     */\n    attachAnchors() {\n        // If we're not on the browser, there can be no focus to trap.\n        if (this._hasAttached) {\n            return true;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            if (!this._startAnchor) {\n                this._startAnchor = this._createAnchor();\n                this._startAnchor.addEventListener('focus', this.startAnchorListener);\n            }\n            if (!this._endAnchor) {\n                this._endAnchor = this._createAnchor();\n                this._endAnchor.addEventListener('focus', this.endAnchorListener);\n            }\n        });\n        if (this._element.parentNode) {\n            this._element.parentNode.insertBefore(this._startAnchor, this._element);\n            this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n            this._hasAttached = true;\n        }\n        return this._hasAttached;\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses the first tabbable element.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusInitialElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the first tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusFirstTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n        });\n    }\n    /**\n     * Waits for the zone to stabilize, then focuses\n     * the last tabbable element within the focus trap region.\n     * @returns Returns a promise that resolves with a boolean, depending\n     * on whether focus was moved successfully.\n     */\n    focusLastTabbableElementWhenReady(options) {\n        return new Promise(resolve => {\n            this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n        });\n    }\n    /**\n     * Get the specified boundary element of the trapped region.\n     * @param bound The boundary to get (start or end of trapped region).\n     * @returns The boundary element.\n     */\n    _getRegionBoundary(bound) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            for (let i = 0; i < markers.length; i++) {\n                // @breaking-change 8.0.0\n                if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated ` +\n                        `attribute will be removed in 8.0.0.`, markers[i]);\n                }\n                else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n                    console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` +\n                        `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` +\n                        `will be removed in 8.0.0.`, markers[i]);\n                }\n            }\n        }\n        if (bound == 'start') {\n            return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n        }\n        return markers.length\n            ? markers[markers.length - 1]\n            : this._getLastTabbableElement(this._element);\n    }\n    /**\n     * Focuses the element that should be focused when the focus trap is initialized.\n     * @returns Whether focus was moved successfully.\n     */\n    focusInitialElement(options) {\n        // Contains the deprecated version of selector, for temporary backwards comparability.\n        const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n        if (redirectToElement) {\n            // @breaking-change 8.0.0\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n                console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` +\n                    `use 'cdkFocusInitial' instead. The deprecated attribute ` +\n                    `will be removed in 8.0.0`, redirectToElement);\n            }\n            // Warn the consumer if the element they've pointed to\n            // isn't focusable, when not in production mode.\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                !this._checker.isFocusable(redirectToElement)) {\n                console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n            }\n            if (!this._checker.isFocusable(redirectToElement)) {\n                const focusableChild = this._getFirstTabbableElement(redirectToElement);\n                focusableChild?.focus(options);\n                return !!focusableChild;\n            }\n            redirectToElement.focus(options);\n            return true;\n        }\n        return this.focusFirstTabbableElement(options);\n    }\n    /**\n     * Focuses the first tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusFirstTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('start');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Focuses the last tabbable element within the focus trap region.\n     * @returns Whether focus was moved successfully.\n     */\n    focusLastTabbableElement(options) {\n        const redirectToElement = this._getRegionBoundary('end');\n        if (redirectToElement) {\n            redirectToElement.focus(options);\n        }\n        return !!redirectToElement;\n    }\n    /**\n     * Checks whether the focus trap has successfully been attached.\n     */\n    hasAttached() {\n        return this._hasAttached;\n    }\n    /** Get the first tabbable element from a DOM subtree (inclusive). */\n    _getFirstTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        const children = root.children;\n        for (let i = 0; i < children.length; i++) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getFirstTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Get the last tabbable element from a DOM subtree (inclusive). */\n    _getLastTabbableElement(root) {\n        if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n            return root;\n        }\n        // Iterate in reverse DOM order.\n        const children = root.children;\n        for (let i = children.length - 1; i >= 0; i--) {\n            const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE\n                ? this._getLastTabbableElement(children[i])\n                : null;\n            if (tabbableChild) {\n                return tabbableChild;\n            }\n        }\n        return null;\n    }\n    /** Creates an anchor element. */\n    _createAnchor() {\n        const anchor = this._document.createElement('div');\n        this._toggleAnchorTabIndex(this._enabled, anchor);\n        anchor.classList.add('cdk-visually-hidden');\n        anchor.classList.add('cdk-focus-trap-anchor');\n        anchor.setAttribute('aria-hidden', 'true');\n        return anchor;\n    }\n    /**\n     * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n     * @param isEnabled Whether the focus trap is enabled.\n     * @param anchor Anchor on which to toggle the tabindex.\n     */\n    _toggleAnchorTabIndex(isEnabled, anchor) {\n        // Remove the tabindex completely, rather than setting it to -1, because if the\n        // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n        isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n    }\n    /**\n     * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n     * @param enabled: Whether the anchors should trap Tab.\n     */\n    toggleAnchors(enabled) {\n        if (this._startAnchor && this._endAnchor) {\n            this._toggleAnchorTabIndex(enabled, this._startAnchor);\n            this._toggleAnchorTabIndex(enabled, this._endAnchor);\n        }\n    }\n    /** Executes a function when the zone is stable. */\n    _executeOnStable(fn) {\n        if (this._ngZone.isStable) {\n            fn();\n        }\n        else {\n            this._ngZone.onStable.pipe(take(1)).subscribe(fn);\n        }\n    }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n * @deprecated Use `ConfigurableFocusTrapFactory` instead.\n * @breaking-change 11.0.0\n */\nclass FocusTrapFactory {\n    constructor(_checker, _ngZone, _document) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._document = _document;\n    }\n    /**\n     * Creates a focus-trapped region around the given element.\n     * @param element The element around which focus will be trapped.\n     * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n     *     manually by the user.\n     * @returns The created focus trap instance.\n     */\n    create(element, deferCaptureElements = false) {\n        return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements);\n    }\n}\nFocusTrapFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nFocusTrapFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FocusTrapFactory, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n    constructor(_elementRef, _focusTrapFactory, \n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 13.0.0\n     */\n    _document) {\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n        this._previouslyFocusedElement = null;\n        this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n    /** Whether the focus trap is active. */\n    get enabled() {\n        return this.focusTrap.enabled;\n    }\n    set enabled(value) {\n        this.focusTrap.enabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the directive should automatically move focus into the trapped region upon\n     * initialization and return focus to the previous activeElement upon destruction.\n     */\n    get autoCapture() {\n        return this._autoCapture;\n    }\n    set autoCapture(value) {\n        this._autoCapture = coerceBooleanProperty(value);\n    }\n    ngOnDestroy() {\n        this.focusTrap.destroy();\n        // If we stored a previously focused element when using autoCapture, return focus to that\n        // element now that the trapped region is being destroyed.\n        if (this._previouslyFocusedElement) {\n            this._previouslyFocusedElement.focus();\n            this._previouslyFocusedElement = null;\n        }\n    }\n    ngAfterContentInit() {\n        this.focusTrap.attachAnchors();\n        if (this.autoCapture) {\n            this._captureFocus();\n        }\n    }\n    ngDoCheck() {\n        if (!this.focusTrap.hasAttached()) {\n            this.focusTrap.attachAnchors();\n        }\n    }\n    ngOnChanges(changes) {\n        const autoCaptureChange = changes['autoCapture'];\n        if (autoCaptureChange &&\n            !autoCaptureChange.firstChange &&\n            this.autoCapture &&\n            this.focusTrap.hasAttached()) {\n            this._captureFocus();\n        }\n    }\n    _captureFocus() {\n        this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n        this.focusTrap.focusInitialElementWhenReady();\n    }\n}\nCdkTrapFocus.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkTrapFocus, deps: [{ token: i0.ElementRef }, { token: FocusTrapFactory }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nCdkTrapFocus.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkTrapFocus, selector: \"[cdkTrapFocus]\", inputs: { enabled: [\"cdkTrapFocus\", \"enabled\"], autoCapture: [\"cdkTrapFocusAutoCapture\", \"autoCapture\"] }, exportAs: [\"cdkTrapFocus\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkTrapFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkTrapFocus]',\n                    exportAs: 'cdkTrapFocus',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { enabled: [{\n                type: Input,\n                args: ['cdkTrapFocus']\n            }], autoCapture: [{\n                type: Input,\n                args: ['cdkTrapFocusAutoCapture']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n    constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config) {\n        super(_element, _checker, _ngZone, _document, config.defer);\n        this._focusTrapManager = _focusTrapManager;\n        this._inertStrategy = _inertStrategy;\n        this._focusTrapManager.register(this);\n    }\n    /** Whether the FocusTrap is enabled. */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        this._enabled = value;\n        if (this._enabled) {\n            this._focusTrapManager.register(this);\n        }\n        else {\n            this._focusTrapManager.deregister(this);\n        }\n    }\n    /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n    destroy() {\n        this._focusTrapManager.deregister(this);\n        super.destroy();\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _enable() {\n        this._inertStrategy.preventFocus(this);\n        this.toggleAnchors(true);\n    }\n    /** @docs-private Implemented as part of ManagedFocusTrap. */\n    _disable() {\n        this._inertStrategy.allowFocus(this);\n        this.toggleAnchors(false);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n    constructor() {\n        /** Focus event handler. */\n        this._listener = null;\n    }\n    /** Adds a document event listener that keeps focus inside the FocusTrap. */\n    preventFocus(focusTrap) {\n        // Ensure there's only one listener per document\n        if (this._listener) {\n            focusTrap._document.removeEventListener('focus', this._listener, true);\n        }\n        this._listener = (e) => this._trapFocus(focusTrap, e);\n        focusTrap._ngZone.runOutsideAngular(() => {\n            focusTrap._document.addEventListener('focus', this._listener, true);\n        });\n    }\n    /** Removes the event listener added in preventFocus. */\n    allowFocus(focusTrap) {\n        if (!this._listener) {\n            return;\n        }\n        focusTrap._document.removeEventListener('focus', this._listener, true);\n        this._listener = null;\n    }\n    /**\n     * Refocuses the first element in the FocusTrap if the focus event target was outside\n     * the FocusTrap.\n     *\n     * This is an event listener callback. The event listener is added in runOutsideAngular,\n     * so all this code runs outside Angular as well.\n     */\n    _trapFocus(focusTrap, event) {\n        const target = event.target;\n        const focusTrapRoot = focusTrap._element;\n        // Don't refocus if target was in an overlay, because the overlay might be associated\n        // with an element inside the FocusTrap, ex. mat-select.\n        if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n            // Some legacy FocusTrap usages have logic that focuses some element on the page\n            // just before FocusTrap is destroyed. For backwards compatibility, wait\n            // to be sure FocusTrap is still enabled before refocusing.\n            setTimeout(() => {\n                // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n                if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n                    focusTrap.focusFirstTabbableElement();\n                }\n            });\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n    constructor() {\n        // A stack of the FocusTraps on the page. Only the FocusTrap at the\n        // top of the stack is active.\n        this._focusTrapStack = [];\n    }\n    /**\n     * Disables the FocusTrap at the top of the stack, and then pushes\n     * the new FocusTrap onto the stack.\n     */\n    register(focusTrap) {\n        // Dedupe focusTraps that register multiple times.\n        this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n        let stack = this._focusTrapStack;\n        if (stack.length) {\n            stack[stack.length - 1]._disable();\n        }\n        stack.push(focusTrap);\n        focusTrap._enable();\n    }\n    /**\n     * Removes the FocusTrap from the stack, and activates the\n     * FocusTrap that is the new top of the stack.\n     */\n    deregister(focusTrap) {\n        focusTrap._disable();\n        const stack = this._focusTrapStack;\n        const i = stack.indexOf(focusTrap);\n        if (i !== -1) {\n            stack.splice(i, 1);\n            if (stack.length) {\n                stack[stack.length - 1]._enable();\n            }\n        }\n    }\n}\nFocusTrapManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FocusTrapManager, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nFocusTrapManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FocusTrapManager, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FocusTrapManager, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n    constructor(_checker, _ngZone, _focusTrapManager, _document, _inertStrategy) {\n        this._checker = _checker;\n        this._ngZone = _ngZone;\n        this._focusTrapManager = _focusTrapManager;\n        this._document = _document;\n        // TODO split up the strategies into different modules, similar to DateAdapter.\n        this._inertStrategy = _inertStrategy || new EventListenerFocusTrapInertStrategy();\n    }\n    create(element, config = { defer: false }) {\n        let configObject;\n        if (typeof config === 'boolean') {\n            configObject = { defer: config };\n        }\n        else {\n            configObject = config;\n        }\n        return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject);\n    }\n}\nConfigurableFocusTrapFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, deps: [{ token: InteractivityChecker }, { token: i0.NgZone }, { token: FocusTrapManager }, { token: DOCUMENT }, { token: FOCUS_TRAP_INERT_STRATEGY, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nConfigurableFocusTrapFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ConfigurableFocusTrapFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: InteractivityChecker }, { type: i0.NgZone }, { type: FocusTrapManager }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_TRAP_INERT_STRATEGY]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n    // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n    // a clickable element. We can distinguish these events when both `offsetX` and `offsetY` are\n    // zero or `event.buttons` is zero, depending on the browser:\n    // - `event.buttons` works on Firefox, but fails on Chrome.\n    // - `offsetX` and `offsetY` work on Chrome, but fail on Firefox.\n    // Note that there's an edge case where the user could click the 0x0 spot of the\n    // screen themselves, but that is unlikely to contain interactive elements.\n    return event.buttons === 0 || (event.offsetX === 0 && event.offsetY === 0);\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n    const touch = (event.touches && event.touches[0]) || (event.changedTouches && event.changedTouches[0]);\n    // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n    // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n    // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n    // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n    return (!!touch &&\n        touch.identifier === -1 &&\n        (touch.radiusX == null || touch.radiusX === 1) &&\n        (touch.radiusY == null || touch.radiusY === 1));\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n    ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT],\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n    constructor(_platform, ngZone, document, options) {\n        this._platform = _platform;\n        /**\n         * The most recently detected input modality event target. Is null if no input modality has been\n         * detected or if the associated event target is null for some unknown reason.\n         */\n        this._mostRecentTarget = null;\n        /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n        this._modality = new BehaviorSubject(null);\n        /**\n         * The timestamp of the last touch input modality. Used to determine whether mousedown events\n         * should be attributed to mouse or touch.\n         */\n        this._lastTouchMs = 0;\n        /**\n         * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n         * bound.\n         */\n        this._onKeydown = (event) => {\n            // If this is one of the keys we should ignore, then ignore it and don't update the input\n            // modality to keyboard.\n            if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n                return;\n            }\n            this._modality.next('keyboard');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onMousedown = (event) => {\n            // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n            // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n            // after the previous touch event.\n            if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n                return;\n            }\n            // Fake mousedown events are fired by some screen readers when controls are activated by the\n            // screen reader. Attribute them to keyboard input modality.\n            this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        /**\n         * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n         * gets bound.\n         */\n        this._onTouchstart = (event) => {\n            // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n            // events are fired. Again, attribute to keyboard input modality.\n            if (isFakeTouchstartFromScreenReader(event)) {\n                this._modality.next('keyboard');\n                return;\n            }\n            // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n            // triggered via mouse vs touch.\n            this._lastTouchMs = Date.now();\n            this._modality.next('touch');\n            this._mostRecentTarget = _getEventTarget(event);\n        };\n        this._options = {\n            ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n            ...options,\n        };\n        // Skip the first emission as it's null.\n        this.modalityDetected = this._modality.pipe(skip(1));\n        this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n        // If we're not in a browser, this service should do nothing, as there's no relevant input\n        // modality to detect.\n        if (_platform.isBrowser) {\n            ngZone.runOutsideAngular(() => {\n                document.addEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n                document.addEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n                document.addEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n            });\n        }\n    }\n    /** The most recently detected input modality. */\n    get mostRecentModality() {\n        return this._modality.value;\n    }\n    ngOnDestroy() {\n        this._modality.complete();\n        if (this._platform.isBrowser) {\n            document.removeEventListener('keydown', this._onKeydown, modalityEventListenerOptions);\n            document.removeEventListener('mousedown', this._onMousedown, modalityEventListenerOptions);\n            document.removeEventListener('touchstart', this._onTouchstart, modalityEventListenerOptions);\n        }\n    }\n}\nInputModalityDetector.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: InputModalityDetector, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT }, { token: INPUT_MODALITY_DETECTOR_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nInputModalityDetector.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: InputModalityDetector, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: InputModalityDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: i0.NgZone }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [INPUT_MODALITY_DETECTOR_OPTIONS]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n    providedIn: 'root',\n    factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY,\n});\n/** @docs-private */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n    return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass LiveAnnouncer {\n    constructor(elementToken, _ngZone, _document, _defaultOptions) {\n        this._ngZone = _ngZone;\n        this._defaultOptions = _defaultOptions;\n        // We inject the live element and document as `any` because the constructor signature cannot\n        // reference browser globals (HTMLElement, Document) on non-browser environments, since having\n        // a class decorator causes TypeScript to preserve the constructor signature types.\n        this._document = _document;\n        this._liveElement = elementToken || this._createLiveElement();\n    }\n    announce(message, ...args) {\n        const defaultOptions = this._defaultOptions;\n        let politeness;\n        let duration;\n        if (args.length === 1 && typeof args[0] === 'number') {\n            duration = args[0];\n        }\n        else {\n            [politeness, duration] = args;\n        }\n        this.clear();\n        clearTimeout(this._previousTimeout);\n        if (!politeness) {\n            politeness =\n                defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n        }\n        if (duration == null && defaultOptions) {\n            duration = defaultOptions.duration;\n        }\n        // TODO: ensure changing the politeness works on all environments we support.\n        this._liveElement.setAttribute('aria-live', politeness);\n        // This 100ms timeout is necessary for some browser + screen-reader combinations:\n        // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n        // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n        //   second time without clearing and then using a non-zero delay.\n        // (using JAWS 17 at time of this writing).\n        return this._ngZone.runOutsideAngular(() => {\n            if (!this._currentPromise) {\n                this._currentPromise = new Promise(resolve => (this._currentResolve = resolve));\n            }\n            clearTimeout(this._previousTimeout);\n            this._previousTimeout = setTimeout(() => {\n                this._liveElement.textContent = message;\n                if (typeof duration === 'number') {\n                    this._previousTimeout = setTimeout(() => this.clear(), duration);\n                }\n                this._currentResolve();\n                this._currentPromise = this._currentResolve = undefined;\n            }, 100);\n            return this._currentPromise;\n        });\n    }\n    /**\n     * Clears the current text from the announcer element. Can be used to prevent\n     * screen readers from reading the text out again while the user is going\n     * through the page landmarks.\n     */\n    clear() {\n        if (this._liveElement) {\n            this._liveElement.textContent = '';\n        }\n    }\n    ngOnDestroy() {\n        clearTimeout(this._previousTimeout);\n        this._liveElement?.remove();\n        this._liveElement = null;\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n    }\n    _createLiveElement() {\n        const elementClass = 'cdk-live-announcer-element';\n        const previousElements = this._document.getElementsByClassName(elementClass);\n        const liveEl = this._document.createElement('div');\n        // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n        for (let i = 0; i < previousElements.length; i++) {\n            previousElements[i].remove();\n        }\n        liveEl.classList.add(elementClass);\n        liveEl.classList.add('cdk-visually-hidden');\n        liveEl.setAttribute('aria-atomic', 'true');\n        liveEl.setAttribute('aria-live', 'polite');\n        this._document.body.appendChild(liveEl);\n        return liveEl;\n    }\n}\nLiveAnnouncer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: LiveAnnouncer, deps: [{ token: LIVE_ANNOUNCER_ELEMENT_TOKEN, optional: true }, { token: i0.NgZone }, { token: DOCUMENT }, { token: LIVE_ANNOUNCER_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nLiveAnnouncer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: LiveAnnouncer, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: LiveAnnouncer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_ELEMENT_TOKEN]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LIVE_ANNOUNCER_DEFAULT_OPTIONS]\n                }] }]; } });\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n    constructor(_elementRef, _liveAnnouncer, _contentObserver, _ngZone) {\n        this._elementRef = _elementRef;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._contentObserver = _contentObserver;\n        this._ngZone = _ngZone;\n        this._politeness = 'polite';\n    }\n    /** The aria-live politeness level to use when announcing messages. */\n    get politeness() {\n        return this._politeness;\n    }\n    set politeness(value) {\n        this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n        if (this._politeness === 'off') {\n            if (this._subscription) {\n                this._subscription.unsubscribe();\n                this._subscription = null;\n            }\n        }\n        else if (!this._subscription) {\n            this._subscription = this._ngZone.runOutsideAngular(() => {\n                return this._contentObserver.observe(this._elementRef).subscribe(() => {\n                    // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n                    const elementText = this._elementRef.nativeElement.textContent;\n                    // The `MutationObserver` fires also for attribute\n                    // changes which we don't want to announce.\n                    if (elementText !== this._previousAnnouncedText) {\n                        this._liveAnnouncer.announce(elementText, this._politeness);\n                        this._previousAnnouncedText = elementText;\n                    }\n                });\n            });\n        }\n    }\n    ngOnDestroy() {\n        if (this._subscription) {\n            this._subscription.unsubscribe();\n        }\n    }\n}\nCdkAriaLive.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkAriaLive, deps: [{ token: i0.ElementRef }, { token: LiveAnnouncer }, { token: i1$1.ContentObserver }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nCdkAriaLive.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkAriaLive, selector: \"[cdkAriaLive]\", inputs: { politeness: [\"cdkAriaLive\", \"politeness\"] }, exportAs: [\"cdkAriaLive\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkAriaLive, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAriaLive]',\n                    exportAs: 'cdkAriaLive',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: LiveAnnouncer }, { type: i1$1.ContentObserver }, { type: i0.NgZone }]; }, propDecorators: { politeness: [{\n                type: Input,\n                args: ['cdkAriaLive']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n    passive: true,\n    capture: true,\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n    constructor(_ngZone, _platform, _inputModalityDetector, \n    /** @breaking-change 11.0.0 make document required */\n    document, options) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._inputModalityDetector = _inputModalityDetector;\n        /** The focus origin that the next focus event is a result of. */\n        this._origin = null;\n        /** Whether the window has just been focused. */\n        this._windowFocused = false;\n        /**\n         * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n         * focus events to touch interactions requires special logic.\n         */\n        this._originFromTouchInteraction = false;\n        /** Map of elements being monitored to their info. */\n        this._elementInfo = new Map();\n        /** The number of elements currently being monitored. */\n        this._monitoredElementCount = 0;\n        /**\n         * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n         * as well as the number of monitored elements that they contain. We have to treat focus/blur\n         * handlers differently from the rest of the events, because the browser won't emit events\n         * to the document when focus moves inside of a shadow root.\n         */\n        this._rootNodeFocusListenerCount = new Map();\n        /**\n         * Event listener for `focus` events on the window.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._windowFocusListener = () => {\n            // Make a note of when the window regains focus, so we can\n            // restore the origin info for the focused element.\n            this._windowFocused = true;\n            this._windowFocusTimeoutId = window.setTimeout(() => (this._windowFocused = false));\n        };\n        /** Subject for stopping our InputModalityDetector subscription. */\n        this._stopInputModalityDetector = new Subject();\n        /**\n         * Event listener for `focus` and 'blur' events on the document.\n         * Needs to be an arrow function in order to preserve the context when it gets bound.\n         */\n        this._rootNodeFocusAndBlurListener = (event) => {\n            const target = _getEventTarget(event);\n            const handler = event.type === 'focus' ? this._onFocus : this._onBlur;\n            // We need to walk up the ancestor chain in order to support `checkChildren`.\n            for (let element = target; element; element = element.parentElement) {\n                handler.call(this, event, element);\n            }\n        };\n        this._document = document;\n        this._detectionMode = options?.detectionMode || 0 /* IMMEDIATE */;\n    }\n    monitor(element, checkChildren = false) {\n        const nativeElement = coerceElement(element);\n        // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n        if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n            return of(null);\n        }\n        // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n        // the shadow root, rather than the `document`, because the browser won't emit focus events\n        // to the `document`, if focus is moving within the same shadow root.\n        const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n        const cachedInfo = this._elementInfo.get(nativeElement);\n        // Check if we're already monitoring this element.\n        if (cachedInfo) {\n            if (checkChildren) {\n                // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n                // observers into ones that behave as if `checkChildren` was turned on. We need a more\n                // robust solution.\n                cachedInfo.checkChildren = true;\n            }\n            return cachedInfo.subject;\n        }\n        // Create monitored element info.\n        const info = {\n            checkChildren: checkChildren,\n            subject: new Subject(),\n            rootNode,\n        };\n        this._elementInfo.set(nativeElement, info);\n        this._registerGlobalListeners(info);\n        return info.subject;\n    }\n    stopMonitoring(element) {\n        const nativeElement = coerceElement(element);\n        const elementInfo = this._elementInfo.get(nativeElement);\n        if (elementInfo) {\n            elementInfo.subject.complete();\n            this._setClasses(nativeElement);\n            this._elementInfo.delete(nativeElement);\n            this._removeGlobalListeners(elementInfo);\n        }\n    }\n    focusVia(element, origin, options) {\n        const nativeElement = coerceElement(element);\n        const focusedElement = this._getDocument().activeElement;\n        // If the element is focused already, calling `focus` again won't trigger the event listener\n        // which means that the focus classes won't be updated. If that's the case, update the classes\n        // directly without waiting for an event.\n        if (nativeElement === focusedElement) {\n            this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n        }\n        else {\n            this._setOrigin(origin);\n            // `focus` isn't available on the server\n            if (typeof nativeElement.focus === 'function') {\n                nativeElement.focus(options);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    _getFocusOrigin(focusEventTarget) {\n        if (this._origin) {\n            // If the origin was realized via a touch interaction, we need to perform additional checks\n            // to determine whether the focus origin should be attributed to touch or program.\n            if (this._originFromTouchInteraction) {\n                return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n            }\n            else {\n                return this._origin;\n            }\n        }\n        // If the window has just regained focus, we can restore the most recent origin from before the\n        // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n        // focus. This typically means one of two things happened:\n        //\n        // 1) The element was programmatically focused, or\n        // 2) The element was focused via screen reader navigation (which generally doesn't fire\n        //    events).\n        //\n        // Because we can't distinguish between these two cases, we default to setting `program`.\n        return this._windowFocused && this._lastFocusOrigin ? this._lastFocusOrigin : 'program';\n    }\n    /**\n     * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n     * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n     * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n     * event was directly caused by the touch interaction or (2) the focus event was caused by a\n     * subsequent programmatic focus call triggered by the touch interaction.\n     * @param focusEventTarget The target of the focus event under examination.\n     */\n    _shouldBeAttributedToTouch(focusEventTarget) {\n        // Please note that this check is not perfect. Consider the following edge case:\n        //\n        // <div #parent tabindex=\"0\">\n        //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n        // </div>\n        //\n        // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n        // #child, #parent is programmatically focused. This code will attribute the focus to touch\n        // instead of program. This is a relatively minor edge-case that can be worked around by using\n        // focusVia(parent, 'program') to focus #parent.\n        return (this._detectionMode === 1 /* EVENTUAL */ ||\n            !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget));\n    }\n    /**\n     * Sets the focus classes on the element based on the given focus origin.\n     * @param element The element to update the classes on.\n     * @param origin The focus origin.\n     */\n    _setClasses(element, origin) {\n        element.classList.toggle('cdk-focused', !!origin);\n        element.classList.toggle('cdk-touch-focused', origin === 'touch');\n        element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n        element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n        element.classList.toggle('cdk-program-focused', origin === 'program');\n    }\n    /**\n     * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n     * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n     * the origin being set.\n     * @param origin The origin to set.\n     * @param isFromInteraction Whether we are setting the origin from an interaction event.\n     */\n    _setOrigin(origin, isFromInteraction = false) {\n        this._ngZone.runOutsideAngular(() => {\n            this._origin = origin;\n            this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n            // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n            // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n            // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n            // a touch event because when a touch event is fired, the associated focus event isn't yet in\n            // the event queue. Before doing so, clear any pending timeouts.\n            if (this._detectionMode === 0 /* IMMEDIATE */) {\n                clearTimeout(this._originTimeoutId);\n                const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n                this._originTimeoutId = setTimeout(() => (this._origin = null), ms);\n            }\n        });\n    }\n    /**\n     * Handles focus events on a registered element.\n     * @param event The focus event.\n     * @param element The monitored element.\n     */\n    _onFocus(event, element) {\n        // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n        // focus event affecting the monitored element. If we want to use the origin of the first event\n        // instead we should check for the cdk-focused class here and return if the element already has\n        // it. (This only matters for elements that have includesChildren = true).\n        // If we are not counting child-element-focus as focused, make sure that the event target is the\n        // monitored element itself.\n        const elementInfo = this._elementInfo.get(element);\n        const focusEventTarget = _getEventTarget(event);\n        if (!elementInfo || (!elementInfo.checkChildren && element !== focusEventTarget)) {\n            return;\n        }\n        this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n    }\n    /**\n     * Handles blur events on a registered element.\n     * @param event The blur event.\n     * @param element The monitored element.\n     */\n    _onBlur(event, element) {\n        // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n        // order to focus another child of the monitored element.\n        const elementInfo = this._elementInfo.get(element);\n        if (!elementInfo ||\n            (elementInfo.checkChildren &&\n                event.relatedTarget instanceof Node &&\n                element.contains(event.relatedTarget))) {\n            return;\n        }\n        this._setClasses(element);\n        this._emitOrigin(elementInfo.subject, null);\n    }\n    _emitOrigin(subject, origin) {\n        this._ngZone.run(() => subject.next(origin));\n    }\n    _registerGlobalListeners(elementInfo) {\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        const rootNode = elementInfo.rootNode;\n        const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n        if (!rootNodeFocusListeners) {\n            this._ngZone.runOutsideAngular(() => {\n                rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n            });\n        }\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n        // Register global listeners when first element is monitored.\n        if (++this._monitoredElementCount === 1) {\n            // Note: we listen to events in the capture phase so we\n            // can detect them even if the user stops propagation.\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                window.addEventListener('focus', this._windowFocusListener);\n            });\n            // The InputModalityDetector is also just a collection of global listeners.\n            this._inputModalityDetector.modalityDetected\n                .pipe(takeUntil(this._stopInputModalityDetector))\n                .subscribe(modality => {\n                this._setOrigin(modality, true /* isFromInteraction */);\n            });\n        }\n    }\n    _removeGlobalListeners(elementInfo) {\n        const rootNode = elementInfo.rootNode;\n        if (this._rootNodeFocusListenerCount.has(rootNode)) {\n            const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n            if (rootNodeFocusListeners > 1) {\n                this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n            }\n            else {\n                rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n                this._rootNodeFocusListenerCount.delete(rootNode);\n            }\n        }\n        // Unregister global listeners when last element is unmonitored.\n        if (!--this._monitoredElementCount) {\n            const window = this._getWindow();\n            window.removeEventListener('focus', this._windowFocusListener);\n            // Equivalently, stop our InputModalityDetector subscription.\n            this._stopInputModalityDetector.next();\n            // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n            clearTimeout(this._windowFocusTimeoutId);\n            clearTimeout(this._originTimeoutId);\n        }\n    }\n    /** Updates all the state on an element once its focus origin has changed. */\n    _originChanged(element, origin, elementInfo) {\n        this._setClasses(element, origin);\n        this._emitOrigin(elementInfo.subject, origin);\n        this._lastFocusOrigin = origin;\n    }\n    /**\n     * Collects the `MonitoredElementInfo` of a particular element and\n     * all of its ancestors that have enabled `checkChildren`.\n     * @param element Element from which to start the search.\n     */\n    _getClosestElementsInfo(element) {\n        const results = [];\n        this._elementInfo.forEach((info, currentElement) => {\n            if (currentElement === element || (info.checkChildren && currentElement.contains(element))) {\n                results.push([currentElement, info]);\n            }\n        });\n        return results;\n    }\n}\nFocusMonitor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FocusMonitor, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: InputModalityDetector }, { token: DOCUMENT, optional: true }, { token: FOCUS_MONITOR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nFocusMonitor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FocusMonitor, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FocusMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i1.Platform }, { type: InputModalityDetector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FOCUS_MONITOR_DEFAULT_OPTIONS]\n                }] }]; } });\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n    constructor(_elementRef, _focusMonitor) {\n        this._elementRef = _elementRef;\n        this._focusMonitor = _focusMonitor;\n        this.cdkFocusChange = new EventEmitter();\n    }\n    ngAfterViewInit() {\n        const element = this._elementRef.nativeElement;\n        this._monitorSubscription = this._focusMonitor\n            .monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus'))\n            .subscribe(origin => this.cdkFocusChange.emit(origin));\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        if (this._monitorSubscription) {\n            this._monitorSubscription.unsubscribe();\n        }\n    }\n}\nCdkMonitorFocus.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkMonitorFocus, deps: [{ token: i0.ElementRef }, { token: FocusMonitor }], target: i0.ɵɵFactoryTarget.Directive });\nCdkMonitorFocus.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkMonitorFocus, selector: \"[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]\", outputs: { cdkFocusChange: \"cdkFocusChange\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkMonitorFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: FocusMonitor }]; }, propDecorators: { cdkFocusChange: [{\n                type: Output\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n    constructor(_platform, document) {\n        this._platform = _platform;\n        this._document = document;\n    }\n    /** Gets the current high-contrast-mode for the page. */\n    getHighContrastMode() {\n        if (!this._platform.isBrowser) {\n            return 0 /* NONE */;\n        }\n        // Create a test element with an arbitrary background-color that is neither black nor\n        // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n        // appending the test element to the DOM does not affect layout by absolutely positioning it\n        const testElement = this._document.createElement('div');\n        testElement.style.backgroundColor = 'rgb(1,2,3)';\n        testElement.style.position = 'absolute';\n        this._document.body.appendChild(testElement);\n        // Get the computed style for the background color, collapsing spaces to normalize between\n        // browsers. Once we get this color, we no longer need the test element. Access the `window`\n        // via the document so we can fake it in tests. Note that we have extra null checks, because\n        // this logic will likely run during app bootstrap and throwing can break the entire app.\n        const documentWindow = this._document.defaultView || window;\n        const computedStyle = documentWindow && documentWindow.getComputedStyle\n            ? documentWindow.getComputedStyle(testElement)\n            : null;\n        const computedColor = ((computedStyle && computedStyle.backgroundColor) || '').replace(/ /g, '');\n        testElement.remove();\n        switch (computedColor) {\n            case 'rgb(0,0,0)':\n                return 2 /* WHITE_ON_BLACK */;\n            case 'rgb(255,255,255)':\n                return 1 /* BLACK_ON_WHITE */;\n        }\n        return 0 /* NONE */;\n    }\n    /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n    _applyBodyHighContrastModeCssClasses() {\n        if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n            const bodyClasses = this._document.body.classList;\n            // IE11 doesn't support `classList` operations with multiple arguments\n            bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS);\n            bodyClasses.remove(BLACK_ON_WHITE_CSS_CLASS);\n            bodyClasses.remove(WHITE_ON_BLACK_CSS_CLASS);\n            this._hasCheckedHighContrastMode = true;\n            const mode = this.getHighContrastMode();\n            if (mode === 1 /* BLACK_ON_WHITE */) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS);\n                bodyClasses.add(BLACK_ON_WHITE_CSS_CLASS);\n            }\n            else if (mode === 2 /* WHITE_ON_BLACK */) {\n                bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS);\n                bodyClasses.add(WHITE_ON_BLACK_CSS_CLASS);\n            }\n        }\n    }\n}\nHighContrastModeDetector.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: HighContrastModeDetector, deps: [{ token: i1.Platform }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nHighContrastModeDetector.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: HighContrastModeDetector, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: HighContrastModeDetector, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass A11yModule {\n    constructor(highContrastModeDetector) {\n        highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n    }\n}\nA11yModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: A11yModule, deps: [{ token: HighContrastModeDetector }], target: i0.ɵɵFactoryTarget.NgModule });\nA11yModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: A11yModule, declarations: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus], imports: [ObserversModule], exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus] });\nA11yModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: A11yModule, imports: [[ObserversModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: A11yModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [ObserversModule],\n                    declarations: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n                }]\n        }], ctorParameters: function () { return [{ type: HighContrastModeDetector }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { A11yModule, ActiveDescendantKeyManager, AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, CdkAriaLive, CdkMonitorFocus, CdkTrapFocus, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_MONITOR_DEFAULT_OPTIONS, FOCUS_TRAP_INERT_STRATEGY, FocusKeyManager, FocusMonitor, FocusTrap, FocusTrapFactory, HighContrastModeDetector, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, INPUT_MODALITY_DETECTOR_OPTIONS, InputModalityDetector, InteractivityChecker, IsFocusableConfig, LIVE_ANNOUNCER_DEFAULT_OPTIONS, LIVE_ANNOUNCER_ELEMENT_TOKEN, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, ListKeyManager, LiveAnnouncer, MESSAGES_CONTAINER_ID, isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader };\n"]}, "metadata": {}, "sourceType": "module"}
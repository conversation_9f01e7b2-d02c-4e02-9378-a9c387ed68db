{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Version, InjectionToken, NgModule, Optional, Inject, inject, LOCALE_ID, Injectable, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, EventEmitter, Output } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { _isTestEnvironment, normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Subject, Observable } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Current version of Angular Material. */\n\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\n\nfunction MatOption_mat_pseudo_checkbox_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 4);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\")(\"disabled\", ctx_r0.disabled);\n  }\n}\n\nfunction MatOption_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r1.group.label, \")\");\n  }\n}\n\nconst _c2 = [\"*\"];\nconst VERSION = new Version('13.3.9');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @docs-private */\n\nclass AnimationCurves {}\n\nAnimationCurves.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\nAnimationCurves.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\nAnimationCurves.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\nAnimationCurves.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n/** @docs-private */\n\nclass AnimationDurations {}\n\nAnimationDurations.COMPLEX = '375ms';\nAnimationDurations.ENTERING = '225ms';\nAnimationDurations.EXITING = '195ms';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @docs-private */\n\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n  return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\n\n\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: MATERIAL_SANITY_CHECKS_FACTORY\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\n\nclass MatCommonModule {\n  constructor(highContrastModeDetector, _sanityChecks, _document) {\n    this._sanityChecks = _sanityChecks;\n    this._document = _document;\n    /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n\n    this._hasDoneGlobalChecks = false; // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n\n    highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n\n    if (!this._hasDoneGlobalChecks) {\n      this._hasDoneGlobalChecks = true;\n\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        if (this._checkIsEnabled('doctype')) {\n          _checkDoctypeIsDefined(this._document);\n        }\n\n        if (this._checkIsEnabled('theme')) {\n          _checkThemeIsPresent(this._document);\n        }\n\n        if (this._checkIsEnabled('version')) {\n          _checkCdkVersionMatch();\n        }\n      }\n    }\n  }\n  /** Gets whether a specific sanity check is enabled. */\n\n\n  _checkIsEnabled(name) {\n    if (_isTestEnvironment()) {\n      return false;\n    }\n\n    if (typeof this._sanityChecks === 'boolean') {\n      return this._sanityChecks;\n    }\n\n    return !!this._sanityChecks[name];\n  }\n\n}\n\nMatCommonModule.ɵfac = function MatCommonModule_Factory(t) {\n  return new (t || MatCommonModule)(i0.ɵɵinject(i1.HighContrastModeDetector), i0.ɵɵinject(MATERIAL_SANITY_CHECKS, 8), i0.ɵɵinject(DOCUMENT));\n};\n\nMatCommonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatCommonModule\n});\nMatCommonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[BidiModule], BidiModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule],\n      exports: [BidiModule]\n    }]\n  }], function () {\n    return [{\n      type: i1.HighContrastModeDetector\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MATERIAL_SANITY_CHECKS]\n      }]\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/** Checks that the page has a doctype. */\n\n\nfunction _checkDoctypeIsDefined(doc) {\n  if (!doc.doctype) {\n    console.warn('Current document does not have a doctype. This may cause ' + 'some Angular Material components not to behave as expected.');\n  }\n}\n/** Checks that a theme has been included. */\n\n\nfunction _checkThemeIsPresent(doc) {\n  // We need to assert that the `body` is defined, because these checks run very early\n  // and the `body` won't be defined if the consumer put their scripts in the `head`.\n  if (!doc.body || typeof getComputedStyle !== 'function') {\n    return;\n  }\n\n  const testElement = doc.createElement('div');\n  testElement.classList.add('mat-theme-loaded-marker');\n  doc.body.appendChild(testElement);\n  const computedStyle = getComputedStyle(testElement); // In some situations the computed style of the test element can be null. For example in\n  // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n  // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n\n  if (computedStyle && computedStyle.display !== 'none') {\n    console.warn('Could not find Angular Material core theme. Most Material ' + 'components may not work as expected. For more info refer ' + 'to the theming guide: https://material.angular.io/guide/theming');\n  }\n\n  testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\n\n\nfunction _checkCdkVersionMatch() {\n  if (VERSION.full !== VERSION$1.full) {\n    console.warn('The Angular Material version (' + VERSION.full + ') does not match ' + 'the Angular CDK version (' + VERSION$1.full + ').\\n' + 'Please ensure the versions of these two packages exactly match.');\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinDisabled(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this._disabled = false;\n    }\n\n    get disabled() {\n      return this._disabled;\n    }\n\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinColor(base, defaultColor) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this.defaultColor = defaultColor; // Set the default color that can be specified from the mixin.\n\n      this.color = defaultColor;\n    }\n\n    get color() {\n      return this._color;\n    }\n\n    set color(value) {\n      const colorPalette = value || this.defaultColor;\n\n      if (colorPalette !== this._color) {\n        if (this._color) {\n          this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n        }\n\n        if (colorPalette) {\n          this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n        }\n\n        this._color = colorPalette;\n      }\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinDisableRipple(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this._disableRipple = false;\n    }\n    /** Whether the ripple effect is disabled or not. */\n\n\n    get disableRipple() {\n      return this._disableRipple;\n    }\n\n    set disableRipple(value) {\n      this._disableRipple = coerceBooleanProperty(value);\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      this._tabIndex = defaultTabIndex;\n      this.defaultTabIndex = defaultTabIndex;\n    }\n\n    get tabIndex() {\n      return this.disabled ? -1 : this._tabIndex;\n    }\n\n    set tabIndex(value) {\n      // If the specified tabIndex value is null or undefined, fall back to the default value.\n      this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction mixinErrorState(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args); // This class member exists as an interop with `MatFormFieldControl` which expects\n      // a public `stateChanges` observable to emit whenever the form field should be updated.\n      // The description is not specifically mentioning the error state, as classes using this\n      // mixin can/should emit an event in other cases too.\n\n      /** Emits whenever the component state changes. */\n\n      this.stateChanges = new Subject();\n      /** Whether the component is in an error state. */\n\n      this.errorState = false;\n    }\n    /** Updates the error state based on the provided error state matcher. */\n\n\n    updateErrorState() {\n      const oldState = this.errorState;\n      const parent = this._parentFormGroup || this._parentForm;\n      const matcher = this.errorStateMatcher || this._defaultErrorStateMatcher;\n      const control = this.ngControl ? this.ngControl.control : null;\n      const newState = matcher.isErrorState(control, parent);\n\n      if (newState !== oldState) {\n        this.errorState = newState;\n        this.stateChanges.next();\n      }\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Mixin to augment a directive with an initialized property that will emits when ngOnInit ends. */\n\n\nfunction mixinInitialized(base) {\n  return class extends base {\n    constructor(...args) {\n      super(...args);\n      /** Whether this directive has been marked as initialized. */\n\n      this._isInitialized = false;\n      /**\n       * List of subscribers that subscribed before the directive was initialized. Should be notified\n       * during _markInitialized. Set to null after pending subscribers are notified, and should\n       * not expect to be populated after.\n       */\n\n      this._pendingSubscribers = [];\n      /**\n       * Observable stream that emits when the directive initializes. If already initialized, the\n       * subscriber is stored to be notified once _markInitialized is called.\n       */\n\n      this.initialized = new Observable(subscriber => {\n        // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n        // when _markInitialized is called.\n        if (this._isInitialized) {\n          this._notifySubscriber(subscriber);\n        } else {\n          this._pendingSubscribers.push(subscriber);\n        }\n      });\n    }\n    /**\n     * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n     * of ngOnInit.\n     * @docs-private\n     */\n\n\n    _markInitialized() {\n      if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('This directive has already been marked as initialized and ' + 'should not be called twice.');\n      }\n\n      this._isInitialized = true;\n\n      this._pendingSubscribers.forEach(this._notifySubscriber);\n\n      this._pendingSubscribers = null;\n    }\n    /** Emits and completes the subscriber stream (should only emit once). */\n\n\n    _notifySubscriber(subscriber) {\n      subscriber.next();\n      subscriber.complete();\n    }\n\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\n\n\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY\n});\n/** @docs-private */\n\nfunction MAT_DATE_LOCALE_FACTORY() {\n  return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\n\n\nclass DateAdapter {\n  constructor() {\n    this._localeChanges = new Subject();\n    /** A stream that emits when the locale changes. */\n\n    this.localeChanges = this._localeChanges;\n  }\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n\n\n  getValidDateOrNull(obj) {\n    return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n  }\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n\n\n  deserialize(value) {\n    if (value == null || this.isDateInstance(value) && this.isValid(value)) {\n      return value;\n    }\n\n    return this.invalid();\n  }\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n\n\n  setLocale(locale) {\n    this.locale = locale;\n\n    this._localeChanges.next();\n  }\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n\n\n  compareDate(first, second) {\n    return this.getYear(first) - this.getYear(second) || this.getMonth(first) - this.getMonth(second) || this.getDate(first) - this.getDate(second);\n  }\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n\n\n  sameDate(first, second) {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n\n      return firstValid == secondValid;\n    }\n\n    return first == second;\n  }\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n\n\n  clampDate(date, min, max) {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n\n    return date;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\n\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\n\nfunction range(length, valueFunction) {\n  const valuesArray = Array(length);\n\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n\n  return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\n\n\nclass NativeDateAdapter extends DateAdapter {\n  constructor(matDateLocale,\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 14.0.0\n   */\n  _platform) {\n    super();\n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n\n    this.useUtcForDisplay = false;\n    super.setLocale(matDateLocale);\n  }\n\n  getYear(date) {\n    return date.getFullYear();\n  }\n\n  getMonth(date) {\n    return date.getMonth();\n  }\n\n  getDate(date) {\n    return date.getDate();\n  }\n\n  getDayOfWeek(date) {\n    return date.getDay();\n  }\n\n  getMonthNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      month: style,\n      timeZone: 'utc'\n    });\n    return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n  }\n\n  getDateNames() {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      day: 'numeric',\n      timeZone: 'utc'\n    });\n    return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n\n  getDayOfWeekNames(style) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      weekday: style,\n      timeZone: 'utc'\n    });\n    return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n\n  getYearName(date) {\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      year: 'numeric',\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n\n  getFirstDayOfWeek() {\n    // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n    return 0;\n  }\n\n  getNumDaysInMonth(date) {\n    return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n  }\n\n  clone(date) {\n    return new Date(date.getTime());\n  }\n\n  createDate(year, month, date) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Check for invalid month and date (except upper bound on date which we have to check after\n      // creating the Date).\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n\n    let result = this._createDateWithOverflow(year, month, date); // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n\n\n    if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n\n    return result;\n  }\n\n  today() {\n    return new Date();\n  }\n\n  parse(value) {\n    // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n    // parameters.\n    if (typeof value == 'number') {\n      return new Date(value);\n    }\n\n    return value ? new Date(Date.parse(value)) : null;\n  }\n\n  format(date, displayFormat) {\n    if (!this.isValid(date)) {\n      throw Error('NativeDateAdapter: Cannot format invalid date.');\n    }\n\n    const dtf = new Intl.DateTimeFormat(this.locale, { ...displayFormat,\n      timeZone: 'utc'\n    });\n    return this._format(dtf, date);\n  }\n\n  addCalendarYears(date, years) {\n    return this.addCalendarMonths(date, years * 12);\n  }\n\n  addCalendarMonths(date, months) {\n    let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date)); // It's possible to wind up in the wrong month if the original month has more days than the new\n    // month. In this case we want to go to the last day of the desired month.\n    // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n    // guarantee this.\n\n\n    if (this.getMonth(newDate) != ((this.getMonth(date) + months) % 12 + 12) % 12) {\n      newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n    }\n\n    return newDate;\n  }\n\n  addCalendarDays(date, days) {\n    return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n  }\n\n  toIso8601(date) {\n    return [date.getUTCFullYear(), this._2digit(date.getUTCMonth() + 1), this._2digit(date.getUTCDate())].join('-');\n  }\n  /**\n   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n   * invalid date for all other values.\n   */\n\n\n  deserialize(value) {\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      } // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n      // string is the right format first.\n\n\n      if (ISO_8601_REGEX.test(value)) {\n        let date = new Date(value);\n\n        if (this.isValid(date)) {\n          return date;\n        }\n      }\n    }\n\n    return super.deserialize(value);\n  }\n\n  isDateInstance(obj) {\n    return obj instanceof Date;\n  }\n\n  isValid(date) {\n    return !isNaN(date.getTime());\n  }\n\n  invalid() {\n    return new Date(NaN);\n  }\n  /** Creates a date but allows the month and date to overflow. */\n\n\n  _createDateWithOverflow(year, month, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setFullYear` and `setHours` instead.\n    const d = new Date();\n    d.setFullYear(year, month, date);\n    d.setHours(0, 0, 0, 0);\n    return d;\n  }\n  /**\n   * Pads a number to make it two digits.\n   * @param n The number to pad.\n   * @returns The padded number.\n   */\n\n\n  _2digit(n) {\n    return ('00' + n).slice(-2);\n  }\n  /**\n   * When converting Date object to string, javascript built-in functions may return wrong\n   * results because it applies its internal DST rules. The DST rules around the world change\n   * very frequently, and the current valid rule is not always valid in previous years though.\n   * We work around this problem building a new Date object which has its internal UTC\n   * representation with the local date and time.\n   * @param dtf Intl.DateTimeFormat object, containg the desired string format. It must have\n   *    timeZone set to 'utc' to work fine.\n   * @param date Date from which we want to get the string representation according to dtf\n   * @returns A Date object with its UTC representation based on the passed in date info\n   */\n\n\n  _format(dtf, date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n    const d = new Date();\n    d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n    d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    return dtf.format(d);\n  }\n\n}\n\nNativeDateAdapter.ɵfac = function NativeDateAdapter_Factory(t) {\n  return new (t || NativeDateAdapter)(i0.ɵɵinject(MAT_DATE_LOCALE, 8), i0.ɵɵinject(i1$1.Platform));\n};\n\nNativeDateAdapter.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NativeDateAdapter,\n  factory: NativeDateAdapter.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateAdapter, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_DATE_LOCALE]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst MAT_NATIVE_DATE_FORMATS = {\n  parse: {\n    dateInput: null\n  },\n  display: {\n    dateInput: {\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric'\n    },\n    monthYearLabel: {\n      year: 'numeric',\n      month: 'short'\n    },\n    dateA11yLabel: {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    },\n    monthYearA11yLabel: {\n      year: 'numeric',\n      month: 'long'\n    }\n  }\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass NativeDateModule {}\n\nNativeDateModule.ɵfac = function NativeDateModule_Factory(t) {\n  return new (t || NativeDateModule)();\n};\n\nNativeDateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NativeDateModule\n});\nNativeDateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: DateAdapter,\n    useClass: NativeDateAdapter\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeDateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: DateAdapter,\n        useClass: NativeDateAdapter\n      }]\n    }]\n  }], null, null);\n})();\n\nclass MatNativeDateModule {}\n\nMatNativeDateModule.ɵfac = function MatNativeDateModule_Factory(t) {\n  return new (t || MatNativeDateModule)();\n};\n\nMatNativeDateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatNativeDateModule\n});\nMatNativeDateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: MAT_DATE_FORMATS,\n    useValue: MAT_NATIVE_DATE_FORMATS\n  }],\n  imports: [[NativeDateModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatNativeDateModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NativeDateModule],\n      providers: [{\n        provide: MAT_DATE_FORMATS,\n        useValue: MAT_NATIVE_DATE_FORMATS\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Error state matcher that matches when a control is invalid and dirty. */\n\n\nclass ShowOnDirtyErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.dirty || form && form.submitted));\n  }\n\n}\n\nShowOnDirtyErrorStateMatcher.ɵfac = function ShowOnDirtyErrorStateMatcher_Factory(t) {\n  return new (t || ShowOnDirtyErrorStateMatcher)();\n};\n\nShowOnDirtyErrorStateMatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ShowOnDirtyErrorStateMatcher,\n  factory: ShowOnDirtyErrorStateMatcher.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ShowOnDirtyErrorStateMatcher, [{\n    type: Injectable\n  }], null, null);\n})();\n/** Provider that defines how form controls behave with regards to displaying error messages. */\n\n\nclass ErrorStateMatcher {\n  isErrorState(control, form) {\n    return !!(control && control.invalid && (control.touched || form && form.submitted));\n  }\n\n}\n\nErrorStateMatcher.ɵfac = function ErrorStateMatcher_Factory(t) {\n  return new (t || ErrorStateMatcher)();\n};\n\nErrorStateMatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ErrorStateMatcher,\n  factory: ErrorStateMatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ErrorStateMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\n\n\nclass MatLine {}\n\nMatLine.ɵfac = function MatLine_Factory(t) {\n  return new (t || MatLine)();\n};\n\nMatLine.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatLine,\n  selectors: [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]],\n  hostAttrs: [1, \"mat-line\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLine, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-line], [matLine]',\n      host: {\n        'class': 'mat-line'\n      }\n    }]\n  }], null, null);\n})();\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\n\n\nfunction setLines(lines, element, prefix = 'mat') {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by Angular when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({\n    length\n  }) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n/** Adds or removes a class from an element. */\n\n\nfunction setClass(element, className, isAdd) {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\n\nclass MatLineModule {}\n\nMatLineModule.ɵfac = function MatLineModule_Factory(t) {\n  return new (t || MatLineModule)();\n};\n\nMatLineModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatLineModule\n});\nMatLineModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[MatCommonModule], MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLineModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatLine, MatCommonModule],\n      declarations: [MatLine]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Reference to a previously launched ripple element.\n */\n\n\nclass RippleRef {\n  constructor(_renderer,\n  /** Reference to the ripple HTML element. */\n  element,\n  /** Ripple configuration used for the ripple. */\n  config) {\n    this._renderer = _renderer;\n    this.element = element;\n    this.config = config;\n    /** Current state of the ripple. */\n\n    this.state = 3\n    /* HIDDEN */\n    ;\n  }\n  /** Fades out the ripple element. */\n\n\n  fadeOut() {\n    this._renderer.fadeOutRipple(this);\n  }\n\n} // TODO: import these values from `@material/ripple` eventually.\n\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\n\n\nconst defaultRippleAnimationConfig = {\n  enterDuration: 225,\n  exitDuration: 150\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\n\nconst ignoreMouseEventsTimeout = 800;\n/** Options that apply to all the event listeners that are bound by the ripple renderer. */\n\nconst passiveEventOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/** Events that signal that the pointer is down. */\n\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\n\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\n\nclass RippleRenderer {\n  constructor(_target, _ngZone, elementOrElementRef, platform) {\n    this._target = _target;\n    this._ngZone = _ngZone;\n    /** Whether the pointer is currently down or not. */\n\n    this._isPointerDown = false;\n    /** Set of currently active ripple references. */\n\n    this._activeRipples = new Set();\n    /** Whether pointer-up event listeners have been registered. */\n\n    this._pointerUpEventsRegistered = false; // Only do anything if we're on the browser.\n\n    if (platform.isBrowser) {\n      this._containerElement = coerceElement(elementOrElementRef);\n    }\n  }\n  /**\n   * Fades in a ripple at the given coordinates.\n   * @param x Coordinate within the element, along the X axis at which to start the ripple.\n   * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n   * @param config Extra ripple options.\n   */\n\n\n  fadeInRipple(x, y, config = {}) {\n    const containerRect = this._containerRect = this._containerRect || this._containerElement.getBoundingClientRect();\n\n    const animationConfig = { ...defaultRippleAnimationConfig,\n      ...config.animation\n    };\n\n    if (config.centered) {\n      x = containerRect.left + containerRect.width / 2;\n      y = containerRect.top + containerRect.height / 2;\n    }\n\n    const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n    const offsetX = x - containerRect.left;\n    const offsetY = y - containerRect.top;\n    const duration = animationConfig.enterDuration;\n    const ripple = document.createElement('div');\n    ripple.classList.add('mat-ripple-element');\n    ripple.style.left = `${offsetX - radius}px`;\n    ripple.style.top = `${offsetY - radius}px`;\n    ripple.style.height = `${radius * 2}px`;\n    ripple.style.width = `${radius * 2}px`; // If a custom color has been specified, set it as inline style. If no color is\n    // set, the default color will be applied through the ripple theme styles.\n\n    if (config.color != null) {\n      ripple.style.backgroundColor = config.color;\n    }\n\n    ripple.style.transitionDuration = `${duration}ms`;\n\n    this._containerElement.appendChild(ripple); // By default the browser does not recalculate the styles of dynamically created\n    // ripple elements. This is critical because then the `scale` would not animate properly.\n\n\n    enforceStyleRecalculation(ripple);\n    ripple.style.transform = 'scale(1)'; // Exposed reference to the ripple that will be returned.\n\n    const rippleRef = new RippleRef(this, ripple, config);\n    rippleRef.state = 0\n    /* FADING_IN */\n    ; // Add the ripple reference to the list of all active ripples.\n\n    this._activeRipples.add(rippleRef);\n\n    if (!config.persistent) {\n      this._mostRecentTransientRipple = rippleRef;\n    } // Wait for the ripple element to be completely faded in.\n    // Once it's faded in, the ripple can be hidden immediately if the mouse is released.\n\n\n    this._runTimeoutOutsideZone(() => {\n      const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n      rippleRef.state = 1\n      /* VISIBLE */\n      ; // When the timer runs out while the user has kept their pointer down, we want to\n      // keep only the persistent ripples and the latest transient ripple. We do this,\n      // because we don't want stacked transient ripples to appear after their enter\n      // animation has finished.\n\n      if (!config.persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n        rippleRef.fadeOut();\n      }\n    }, duration);\n\n    return rippleRef;\n  }\n  /** Fades out a ripple reference. */\n\n\n  fadeOutRipple(rippleRef) {\n    const wasActive = this._activeRipples.delete(rippleRef);\n\n    if (rippleRef === this._mostRecentTransientRipple) {\n      this._mostRecentTransientRipple = null;\n    } // Clear out the cached bounding rect if we have no more ripples.\n\n\n    if (!this._activeRipples.size) {\n      this._containerRect = null;\n    } // For ripples that are not active anymore, don't re-run the fade-out animation.\n\n\n    if (!wasActive) {\n      return;\n    }\n\n    const rippleEl = rippleRef.element;\n    const animationConfig = { ...defaultRippleAnimationConfig,\n      ...rippleRef.config.animation\n    };\n    rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n    rippleEl.style.opacity = '0';\n    rippleRef.state = 2\n    /* FADING_OUT */\n    ; // Once the ripple faded out, the ripple can be safely removed from the DOM.\n\n    this._runTimeoutOutsideZone(() => {\n      rippleRef.state = 3\n      /* HIDDEN */\n      ;\n      rippleEl.remove();\n    }, animationConfig.exitDuration);\n  }\n  /** Fades out all currently active ripples. */\n\n\n  fadeOutAll() {\n    this._activeRipples.forEach(ripple => ripple.fadeOut());\n  }\n  /** Fades out all currently active non-persistent ripples. */\n\n\n  fadeOutAllNonPersistent() {\n    this._activeRipples.forEach(ripple => {\n      if (!ripple.config.persistent) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Sets up the trigger event listeners */\n\n\n  setupTriggerEvents(elementOrElementRef) {\n    const element = coerceElement(elementOrElementRef);\n\n    if (!element || element === this._triggerElement) {\n      return;\n    } // Remove all previously registered event listeners from the trigger element.\n\n\n    this._removeTriggerEvents();\n\n    this._triggerElement = element;\n\n    this._registerEvents(pointerDownEvents);\n  }\n  /**\n   * Handles all registered events.\n   * @docs-private\n   */\n\n\n  handleEvent(event) {\n    if (event.type === 'mousedown') {\n      this._onMousedown(event);\n    } else if (event.type === 'touchstart') {\n      this._onTouchStart(event);\n    } else {\n      this._onPointerUp();\n    } // If pointer-up events haven't been registered yet, do so now.\n    // We do this on-demand in order to reduce the total number of event listeners\n    // registered by the ripples, which speeds up the rendering time for large UIs.\n\n\n    if (!this._pointerUpEventsRegistered) {\n      this._registerEvents(pointerUpEvents);\n\n      this._pointerUpEventsRegistered = true;\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using mouse. */\n\n\n  _onMousedown(event) {\n    // Screen readers will fire fake mouse events for space/enter. Skip launching a\n    // ripple in this case for consistency with the non-screen-reader experience.\n    const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n    const isSyntheticEvent = this._lastTouchStartEvent && Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n\n    if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n      this._isPointerDown = true;\n      this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n    }\n  }\n  /** Function being called whenever the trigger is being pressed using touch. */\n\n\n  _onTouchStart(event) {\n    if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n      // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n      // events will launch a second ripple if we don't ignore mouse events for a specific\n      // time after a touchstart event.\n      this._lastTouchStartEvent = Date.now();\n      this._isPointerDown = true; // Use `changedTouches` so we skip any touches where the user put\n      // their finger down, but used another finger to tap the element again.\n\n      const touches = event.changedTouches;\n\n      for (let i = 0; i < touches.length; i++) {\n        this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n      }\n    }\n  }\n  /** Function being called whenever the trigger is being released. */\n\n\n  _onPointerUp() {\n    if (!this._isPointerDown) {\n      return;\n    }\n\n    this._isPointerDown = false; // Fade-out all ripples that are visible and not persistent.\n\n    this._activeRipples.forEach(ripple => {\n      // By default, only ripples that are completely visible will fade out on pointer release.\n      // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n      const isVisible = ripple.state === 1\n      /* VISIBLE */\n      || ripple.config.terminateOnPointerUp && ripple.state === 0\n      /* FADING_IN */\n      ;\n\n      if (!ripple.config.persistent && isVisible) {\n        ripple.fadeOut();\n      }\n    });\n  }\n  /** Runs a timeout outside of the Angular zone to avoid triggering the change detection. */\n\n\n  _runTimeoutOutsideZone(fn, delay = 0) {\n    this._ngZone.runOutsideAngular(() => setTimeout(fn, delay));\n  }\n  /** Registers event listeners for a given list of events. */\n\n\n  _registerEvents(eventTypes) {\n    this._ngZone.runOutsideAngular(() => {\n      eventTypes.forEach(type => {\n        this._triggerElement.addEventListener(type, this, passiveEventOptions);\n      });\n    });\n  }\n  /** Removes previously registered event listeners from the trigger element. */\n\n\n  _removeTriggerEvents() {\n    if (this._triggerElement) {\n      pointerDownEvents.forEach(type => {\n        this._triggerElement.removeEventListener(type, this, passiveEventOptions);\n      });\n\n      if (this._pointerUpEventsRegistered) {\n        pointerUpEvents.forEach(type => {\n          this._triggerElement.removeEventListener(type, this, passiveEventOptions);\n        });\n      }\n    }\n  }\n\n}\n/** Enforces a style recalculation of a DOM element by computing its styles. */\n\n\nfunction enforceStyleRecalculation(element) {\n  // Enforce a style recalculation by calling `getComputedStyle` and accessing any property.\n  // Calling `getPropertyValue` is important to let optimizers know that this is not a noop.\n  // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n  window.getComputedStyle(element).getPropertyValue('opacity');\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\n\n\nfunction distanceToFurthestCorner(x, y, rect) {\n  const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n  const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n  return Math.sqrt(distX * distX + distY * distY);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token that can be used to specify the global ripple options. */\n\n\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\n\nclass MatRipple {\n  constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n    this._elementRef = _elementRef;\n    this._animationMode = _animationMode;\n    /**\n     * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n     * will be the distance from the center of the ripple to the furthest corner of the host element's\n     * bounding rectangle.\n     */\n\n    this.radius = 0;\n    this._disabled = false;\n    /** Whether ripple directive is initialized and the input bindings are set. */\n\n    this._isInitialized = false;\n    this._globalOptions = globalOptions || {};\n    this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n  }\n  /**\n   * Whether click events will not trigger the ripple. Ripples can be still launched manually\n   * by using the `launch()` method.\n   */\n\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    if (value) {\n      this.fadeOutAllNonPersistent();\n    }\n\n    this._disabled = value;\n\n    this._setupTriggerEventsIfEnabled();\n  }\n  /**\n   * The element that triggers the ripple when click events are received.\n   * Defaults to the directive's host element.\n   */\n\n\n  get trigger() {\n    return this._trigger || this._elementRef.nativeElement;\n  }\n\n  set trigger(trigger) {\n    this._trigger = trigger;\n\n    this._setupTriggerEventsIfEnabled();\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n\n    this._setupTriggerEventsIfEnabled();\n  }\n\n  ngOnDestroy() {\n    this._rippleRenderer._removeTriggerEvents();\n  }\n  /** Fades out all currently showing ripple elements. */\n\n\n  fadeOutAll() {\n    this._rippleRenderer.fadeOutAll();\n  }\n  /** Fades out all currently showing non-persistent ripple elements. */\n\n\n  fadeOutAllNonPersistent() {\n    this._rippleRenderer.fadeOutAllNonPersistent();\n  }\n  /**\n   * Ripple configuration from the directive's input values.\n   * @docs-private Implemented as part of RippleTarget\n   */\n\n\n  get rippleConfig() {\n    return {\n      centered: this.centered,\n      radius: this.radius,\n      color: this.color,\n      animation: { ...this._globalOptions.animation,\n        ...(this._animationMode === 'NoopAnimations' ? {\n          enterDuration: 0,\n          exitDuration: 0\n        } : {}),\n        ...this.animation\n      },\n      terminateOnPointerUp: this._globalOptions.terminateOnPointerUp\n    };\n  }\n  /**\n   * Whether ripples on pointer-down are disabled or not.\n   * @docs-private Implemented as part of RippleTarget\n   */\n\n\n  get rippleDisabled() {\n    return this.disabled || !!this._globalOptions.disabled;\n  }\n  /** Sets up the trigger event listeners if ripples are enabled. */\n\n\n  _setupTriggerEventsIfEnabled() {\n    if (!this.disabled && this._isInitialized) {\n      this._rippleRenderer.setupTriggerEvents(this.trigger);\n    }\n  }\n  /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n\n\n  launch(configOrX, y = 0, config) {\n    if (typeof configOrX === 'number') {\n      return this._rippleRenderer.fadeInRipple(configOrX, y, { ...this.rippleConfig,\n        ...config\n      });\n    } else {\n      return this._rippleRenderer.fadeInRipple(0, 0, { ...this.rippleConfig,\n        ...configOrX\n      });\n    }\n  }\n\n}\n\nMatRipple.ɵfac = function MatRipple_Factory(t) {\n  return new (t || MatRipple)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatRipple.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatRipple,\n  selectors: [[\"\", \"mat-ripple\", \"\"], [\"\", \"matRipple\", \"\"]],\n  hostAttrs: [1, \"mat-ripple\"],\n  hostVars: 2,\n  hostBindings: function MatRipple_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-ripple-unbounded\", ctx.unbounded);\n    }\n  },\n  inputs: {\n    color: [\"matRippleColor\", \"color\"],\n    unbounded: [\"matRippleUnbounded\", \"unbounded\"],\n    centered: [\"matRippleCentered\", \"centered\"],\n    radius: [\"matRippleRadius\", \"radius\"],\n    animation: [\"matRippleAnimation\", \"animation\"],\n    disabled: [\"matRippleDisabled\", \"disabled\"],\n    trigger: [\"matRippleTrigger\", \"trigger\"]\n  },\n  exportAs: [\"matRipple\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRipple, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-ripple], [matRipple]',\n      exportAs: 'matRipple',\n      host: {\n        'class': 'mat-ripple',\n        '[class.mat-ripple-unbounded]': 'unbounded'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    color: [{\n      type: Input,\n      args: ['matRippleColor']\n    }],\n    unbounded: [{\n      type: Input,\n      args: ['matRippleUnbounded']\n    }],\n    centered: [{\n      type: Input,\n      args: ['matRippleCentered']\n    }],\n    radius: [{\n      type: Input,\n      args: ['matRippleRadius']\n    }],\n    animation: [{\n      type: Input,\n      args: ['matRippleAnimation']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matRippleDisabled']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['matRippleTrigger']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatRippleModule {}\n\nMatRippleModule.ɵfac = function MatRippleModule_Factory(t) {\n  return new (t || MatRippleModule)();\n};\n\nMatRippleModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatRippleModule\n});\nMatRippleModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[MatCommonModule], MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatRipple, MatCommonModule],\n      declarations: [MatRipple]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\n\n\nclass MatPseudoCheckbox {\n  constructor(_animationMode) {\n    this._animationMode = _animationMode;\n    /** Display state of the checkbox. */\n\n    this.state = 'unchecked';\n    /** Whether the checkbox is disabled. */\n\n    this.disabled = false;\n  }\n\n}\n\nMatPseudoCheckbox.ɵfac = function MatPseudoCheckbox_Factory(t) {\n  return new (t || MatPseudoCheckbox)(i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatPseudoCheckbox.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatPseudoCheckbox,\n  selectors: [[\"mat-pseudo-checkbox\"]],\n  hostAttrs: [1, \"mat-pseudo-checkbox\"],\n  hostVars: 8,\n  hostBindings: function MatPseudoCheckbox_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-pseudo-checkbox-indeterminate\", ctx.state === \"indeterminate\")(\"mat-pseudo-checkbox-checked\", ctx.state === \"checked\")(\"mat-pseudo-checkbox-disabled\", ctx.disabled)(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n    }\n  },\n  inputs: {\n    state: \"state\",\n    disabled: \"disabled\"\n  },\n  decls: 0,\n  vars: 0,\n  template: function MatPseudoCheckbox_Template(rf, ctx) {},\n  styles: [\".mat-pseudo-checkbox{width:16px;height:16px;border:2px solid;border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox.mat-pseudo-checkbox-indeterminate{border-color:transparent}._mat-animation-noopable.mat-pseudo-checkbox{transition:none;animation:none}._mat-animation-noopable.mat-pseudo-checkbox::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{top:5px;left:1px;width:10px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{top:2.4px;left:1px;width:8px;height:3px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckbox, [{\n    type: Component,\n    args: [{\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'mat-pseudo-checkbox',\n      template: '',\n      host: {\n        'class': 'mat-pseudo-checkbox',\n        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"'\n      },\n      styles: [\".mat-pseudo-checkbox{width:16px;height:16px;border:2px solid;border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox.mat-pseudo-checkbox-indeterminate{border-color:transparent}._mat-animation-noopable.mat-pseudo-checkbox{transition:none;animation:none}._mat-animation-noopable.mat-pseudo-checkbox::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{top:5px;left:1px;width:10px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{top:2.4px;left:1px;width:8px;height:3px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    state: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatPseudoCheckboxModule {}\n\nMatPseudoCheckboxModule.ɵfac = function MatPseudoCheckboxModule_Factory(t) {\n  return new (t || MatPseudoCheckboxModule)();\n};\n\nMatPseudoCheckboxModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatPseudoCheckboxModule\n});\nMatPseudoCheckboxModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[MatCommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPseudoCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: [MatPseudoCheckbox],\n      declarations: [MatPseudoCheckbox]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token used to provide the parent component to options.\n */\n\n\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Boilerplate for applying mixins to MatOptgroup.\n\n/** @docs-private */\n\nconst _MatOptgroupMixinBase = mixinDisabled(class {}); // Counter for unique group ids.\n\n\nlet _uniqueOptgroupIdCounter = 0;\n\nclass _MatOptgroupBase extends _MatOptgroupMixinBase {\n  constructor(parent) {\n    super();\n    /** Unique id for the underlying label. */\n\n    this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n    this._inert = parent?.inertGroups ?? false;\n  }\n\n}\n\n_MatOptgroupBase.ɵfac = function _MatOptgroupBase_Factory(t) {\n  return new (t || _MatOptgroupBase)(i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8));\n};\n\n_MatOptgroupBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatOptgroupBase,\n  inputs: {\n    label: \"label\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatOptgroupBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_OPTION_PARENT_COMPONENT]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    label: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\n\n\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\n\nclass MatOptgroup extends _MatOptgroupBase {}\n\nMatOptgroup.ɵfac = /* @__PURE__ */function () {\n  let ɵMatOptgroup_BaseFactory;\n  return function MatOptgroup_Factory(t) {\n    return (ɵMatOptgroup_BaseFactory || (ɵMatOptgroup_BaseFactory = i0.ɵɵgetInheritedFactory(MatOptgroup)))(t || MatOptgroup);\n  };\n}();\n\nMatOptgroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatOptgroup,\n  selectors: [[\"mat-optgroup\"]],\n  hostAttrs: [1, \"mat-optgroup\"],\n  hostVars: 5,\n  hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n      i0.ɵɵclassProp(\"mat-optgroup-disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\"\n  },\n  exportAs: [\"matOptgroup\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_OPTGROUP,\n    useExisting: MatOptgroup\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 2,\n  consts: [[\"aria-hidden\", \"true\", 1, \"mat-optgroup-label\", 3, \"id\"]],\n  template: function MatOptgroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵelementStart(0, \"span\", 0);\n      i0.ɵɵtext(1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵprojection(3, 1);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"id\", ctx._labelId);\n      i0.ɵɵadvance(1);\n      i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n    }\n  },\n  styles: [\".mat-optgroup-label{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup-label[disabled]{cursor:default}[dir=rtl] .mat-optgroup-label{text-align:right}.mat-optgroup-label .mat-icon{margin-right:16px;vertical-align:middle}.mat-optgroup-label .mat-icon svg{vertical-align:top}[dir=rtl] .mat-optgroup-label .mat-icon{margin-left:16px;margin-right:0}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptgroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-optgroup',\n      exportAs: 'matOptgroup',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      inputs: ['disabled'],\n      host: {\n        'class': 'mat-optgroup',\n        '[attr.role]': '_inert ? null : \"group\"',\n        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n        '[attr.aria-labelledby]': '_inert ? null : _labelId',\n        '[class.mat-optgroup-disabled]': 'disabled'\n      },\n      providers: [{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }],\n      template: \"<span class=\\\"mat-optgroup-label\\\" aria-hidden=\\\"true\\\" [id]=\\\"_labelId\\\">{{ label }} <ng-content></ng-content></span>\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\",\n      styles: [\".mat-optgroup-label{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup-label[disabled]{cursor:default}[dir=rtl] .mat-optgroup-label{text-align:right}.mat-optgroup-label .mat-icon{margin-right:16px;vertical-align:middle}.mat-optgroup-label .mat-icon svg{vertical-align:top}[dir=rtl] .mat-optgroup-label .mat-icon{margin-left:16px;margin-right:0}\\n\"]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\n\n\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\n\nclass MatOptionSelectionChange {\n  constructor(\n  /** Reference to the option that emitted the event. */\n  source,\n  /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n\n}\n\nclass _MatOptionBase {\n  constructor(_element, _changeDetectorRef, _parent, group) {\n    this._element = _element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._parent = _parent;\n    this.group = group;\n    this._selected = false;\n    this._active = false;\n    this._disabled = false;\n    this._mostRecentViewValue = '';\n    /** The unique ID of the option. */\n\n    this.id = `mat-option-${_uniqueIdCounter++}`;\n    /** Event emitted when the option is selected or deselected. */\n    // tslint:disable-next-line:no-output-on-prefix\n\n    this.onSelectionChange = new EventEmitter();\n    /** Emits when the state of the option changes and any parents have to be notified. */\n\n    this._stateChanges = new Subject();\n  }\n  /** Whether the wrapping component is in multiple selection mode. */\n\n\n  get multiple() {\n    return this._parent && this._parent.multiple;\n  }\n  /** Whether or not the option is currently selected. */\n\n\n  get selected() {\n    return this._selected;\n  }\n  /** Whether the option is disabled. */\n\n\n  get disabled() {\n    return this.group && this.group.disabled || this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  /** Whether ripples for the option are disabled. */\n\n\n  get disableRipple() {\n    return !!(this._parent && this._parent.disableRipple);\n  }\n  /**\n   * Whether or not the option is currently active and ready to be selected.\n   * An active option displays styles as if it is focused, but the\n   * focus is actually retained somewhere else. This comes in handy\n   * for components like autocomplete where focus must remain on the input.\n   */\n\n\n  get active() {\n    return this._active;\n  }\n  /**\n   * The displayed value of the option. It is necessary to show the selected option in the\n   * select's trigger.\n   */\n\n\n  get viewValue() {\n    // TODO(kara): Add input property alternative for node envs.\n    return (this._getHostElement().textContent || '').trim();\n  }\n  /** Selects the option. */\n\n\n  select() {\n    if (!this._selected) {\n      this._selected = true;\n\n      this._changeDetectorRef.markForCheck();\n\n      this._emitSelectionChangeEvent();\n    }\n  }\n  /** Deselects the option. */\n\n\n  deselect() {\n    if (this._selected) {\n      this._selected = false;\n\n      this._changeDetectorRef.markForCheck();\n\n      this._emitSelectionChangeEvent();\n    }\n  }\n  /** Sets focus onto this option. */\n\n\n  focus(_origin, options) {\n    // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n    // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n    const element = this._getHostElement();\n\n    if (typeof element.focus === 'function') {\n      element.focus(options);\n    }\n  }\n  /**\n   * This method sets display styles on the option to make it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n\n\n  setActiveStyles() {\n    if (!this._active) {\n      this._active = true;\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method removes display styles on the option that made it appear\n   * active. This is used by the ActiveDescendantKeyManager so key\n   * events will display the proper options as active on arrow key events.\n   */\n\n\n  setInactiveStyles() {\n    if (this._active) {\n      this._active = false;\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n\n\n  getLabel() {\n    return this.viewValue;\n  }\n  /** Ensures the option is selected when activated from the keyboard. */\n\n\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n      this._selectViaInteraction(); // Prevent the page from scrolling down and form submits.\n\n\n      event.preventDefault();\n    }\n  }\n  /**\n   * `Selects the option while indicating the selection came from the user. Used to\n   * determine if the select's view -> model callback should be invoked.`\n   */\n\n\n  _selectViaInteraction() {\n    if (!this.disabled) {\n      this._selected = this.multiple ? !this._selected : true;\n\n      this._changeDetectorRef.markForCheck();\n\n      this._emitSelectionChangeEvent(true);\n    }\n  }\n  /**\n   * Gets the `aria-selected` value for the option. We explicitly omit the `aria-selected`\n   * attribute from single-selection, unselected options. Including the `aria-selected=\"false\"`\n   * attributes adds a significant amount of noise to screen-reader users without providing useful\n   * information.\n   */\n\n\n  _getAriaSelected() {\n    return this.selected || (this.multiple ? false : null);\n  }\n  /** Returns the correct tabindex for the option depending on disabled state. */\n\n\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Gets the host DOM element. */\n\n\n  _getHostElement() {\n    return this._element.nativeElement;\n  }\n\n  ngAfterViewChecked() {\n    // Since parent components could be using the option's label to display the selected values\n    // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n    // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n    // relatively cheap, however we still limit them only to selected options in order to avoid\n    // hitting the DOM too often.\n    if (this._selected) {\n      const viewValue = this.viewValue;\n\n      if (viewValue !== this._mostRecentViewValue) {\n        this._mostRecentViewValue = viewValue;\n\n        this._stateChanges.next();\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n  /** Emits the selection change event. */\n\n\n  _emitSelectionChangeEvent(isUserInput = false) {\n    this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n  }\n\n}\n\n_MatOptionBase.ɵfac = function _MatOptionBase_Factory(t) {\n  i0.ɵɵinvalidFactory();\n};\n\n_MatOptionBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatOptionBase,\n  inputs: {\n    value: \"value\",\n    id: \"id\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    onSelectionChange: \"onSelectionChange\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatOptionBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined\n    }, {\n      type: _MatOptgroupBase\n    }];\n  }, {\n    value: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    onSelectionChange: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Single option inside of a `<mat-select>` element.\n */\n\n\nclass MatOption extends _MatOptionBase {\n  constructor(element, changeDetectorRef, parent, group) {\n    super(element, changeDetectorRef, parent, group);\n  }\n\n}\n\nMatOption.ɵfac = function MatOption_Factory(t) {\n  return new (t || MatOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_OPTION_PARENT_COMPONENT, 8), i0.ɵɵdirectiveInject(MAT_OPTGROUP, 8));\n};\n\nMatOption.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatOption,\n  selectors: [[\"mat-option\"]],\n  hostAttrs: [\"role\", \"option\", 1, \"mat-option\", \"mat-focus-indicator\"],\n  hostVars: 12,\n  hostBindings: function MatOption_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n        return ctx._selectViaInteraction();\n      })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n      i0.ɵɵattribute(\"tabindex\", ctx._getTabIndex())(\"aria-selected\", ctx._getAriaSelected())(\"aria-disabled\", ctx.disabled.toString());\n      i0.ɵɵclassProp(\"mat-selected\", ctx.selected)(\"mat-option-multiple\", ctx.multiple)(\"mat-active\", ctx.active)(\"mat-option-disabled\", ctx.disabled);\n    }\n  },\n  exportAs: [\"matOption\"],\n  features: [i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c2,\n  decls: 5,\n  vars: 4,\n  consts: [[\"class\", \"mat-option-pseudo-checkbox\", 3, \"state\", \"disabled\", 4, \"ngIf\"], [1, \"mat-option-text\"], [\"class\", \"cdk-visually-hidden\", 4, \"ngIf\"], [\"mat-ripple\", \"\", 1, \"mat-option-ripple\", 3, \"matRippleTrigger\", \"matRippleDisabled\"], [1, \"mat-option-pseudo-checkbox\", 3, \"state\", \"disabled\"], [1, \"cdk-visually-hidden\"]],\n  template: function MatOption_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, MatOption_mat_pseudo_checkbox_0_Template, 1, 2, \"mat-pseudo-checkbox\", 0);\n      i0.ɵɵelementStart(1, \"span\", 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(3, MatOption_span_3_Template, 2, 1, \"span\", 2);\n      i0.ɵɵelement(4, \"div\", 3);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.group && ctx.group._inert);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n    }\n  },\n  directives: [i3.NgIf, MatRipple, MatPseudoCheckbox],\n  styles: [\".mat-option{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;position:relative;cursor:pointer;outline:none;display:flex;flex-direction:row;max-width:100%;box-sizing:border-box;align-items:center;-webkit-tap-highlight-color:transparent}.mat-option[disabled]{cursor:default}[dir=rtl] .mat-option{text-align:right}.mat-option .mat-icon{margin-right:16px;vertical-align:middle}.mat-option .mat-icon svg{vertical-align:top}[dir=rtl] .mat-option .mat-icon{margin-left:16px;margin-right:0}.mat-option[aria-disabled=true]{-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:32px}[dir=rtl] .mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:16px;padding-right:32px}.cdk-high-contrast-active .mat-option{margin:0 1px}.cdk-high-contrast-active .mat-option.mat-active{border:solid 1px currentColor;margin:0}.cdk-high-contrast-active .mat-option[aria-disabled=true]{opacity:.5}.mat-option-text{display:inline-block;flex-grow:1;overflow:hidden;text-overflow:ellipsis}.mat-option .mat-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-option-pseudo-checkbox{margin-right:8px}[dir=rtl] .mat-option-pseudo-checkbox{margin-left:8px;margin-right:0}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-option',\n      exportAs: 'matOption',\n      host: {\n        'role': 'option',\n        '[attr.tabindex]': '_getTabIndex()',\n        '[class.mat-selected]': 'selected',\n        '[class.mat-option-multiple]': 'multiple',\n        '[class.mat-active]': 'active',\n        '[id]': 'id',\n        '[attr.aria-selected]': '_getAriaSelected()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[class.mat-option-disabled]': 'disabled',\n        '(click)': '_selectViaInteraction()',\n        '(keydown)': '_handleKeydown($event)',\n        'class': 'mat-option mat-focus-indicator'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<mat-pseudo-checkbox *ngIf=\\\"multiple\\\" class=\\\"mat-option-pseudo-checkbox\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\" [disabled]=\\\"disabled\\\"></mat-pseudo-checkbox>\\n\\n<span class=\\\"mat-option-text\\\"><ng-content></ng-content></span>\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n<span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"group && group._inert\\\">({{ group.label }})</span>\\n\\n<div class=\\\"mat-option-ripple\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\"\\n     [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\",\n      styles: [\".mat-option{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;position:relative;cursor:pointer;outline:none;display:flex;flex-direction:row;max-width:100%;box-sizing:border-box;align-items:center;-webkit-tap-highlight-color:transparent}.mat-option[disabled]{cursor:default}[dir=rtl] .mat-option{text-align:right}.mat-option .mat-icon{margin-right:16px;vertical-align:middle}.mat-option .mat-icon svg{vertical-align:top}[dir=rtl] .mat-option .mat-icon{margin-left:16px;margin-right:0}.mat-option[aria-disabled=true]{-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:32px}[dir=rtl] .mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:16px;padding-right:32px}.cdk-high-contrast-active .mat-option{margin:0 1px}.cdk-high-contrast-active .mat-option.mat-active{border:solid 1px currentColor;margin:0}.cdk-high-contrast-active .mat-option[aria-disabled=true]{opacity:.5}.mat-option-text{display:inline-block;flex-grow:1;overflow:hidden;text-overflow:ellipsis}.mat-option .mat-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-option-pseudo-checkbox{margin-right:8px}[dir=rtl] .mat-option-pseudo-checkbox{margin-left:8px;margin-right:0}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_OPTION_PARENT_COMPONENT]\n      }]\n    }, {\n      type: MatOptgroup,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_OPTGROUP]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\n\n\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n\n    return groupCounter;\n  }\n\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\n\n\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n\n  return currentScrollPosition;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatOptionModule {}\n\nMatOptionModule.ɵfac = function MatOptionModule_Factory(t) {\n  return new (t || MatOptionModule)();\n};\n\nMatOptionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatOptionModule\n});\nMatOptionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatOptionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule],\n      exports: [MatOption, MatOptgroup],\n      declarations: [MatOption, MatOptgroup]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, ShowOnDirtyErrorStateMatcher, VERSION, _MatOptgroupBase, _MatOptionBase, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, setLines };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/material/fesm2020/core.mjs"], "names": ["i0", "Version", "InjectionToken", "NgModule", "Optional", "Inject", "inject", "LOCALE_ID", "Injectable", "Directive", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "EventEmitter", "Output", "i1", "isFakeMousedownFromScreenReader", "isFakeTouchstartFromScreenReader", "BidiModule", "VERSION", "VERSION$1", "i3", "DOCUMENT", "CommonModule", "i1$1", "_isTestEnvironment", "normalizePassiveListenerOptions", "coerceBooleanProperty", "coerceNumberProperty", "coerceElement", "Subject", "Observable", "startWith", "ANIMATION_MODULE_TYPE", "ENTER", "SPACE", "hasModifierKey", "AnimationCurves", "STANDARD_CURVE", "DECELERATION_CURVE", "ACCELERATION_CURVE", "SHARP_CURVE", "AnimationDurations", "COMPLEX", "ENTERING", "EXITING", "MATERIAL_SANITY_CHECKS_FACTORY", "MATERIAL_SANITY_CHECKS", "providedIn", "factory", "MatCommonModule", "constructor", "highContrastModeDetector", "_<PERSON><PERSON><PERSON><PERSON>", "_document", "_hasDoneGlobalChecks", "_applyBodyHighContrastModeCssClasses", "ngDevMode", "_checkIsEnabled", "_checkDoctypeIsDefined", "_checkThemeIsPresent", "_checkCdkVersionMatch", "name", "ɵfac", "HighContrastModeDetector", "ɵmod", "ɵinj", "type", "args", "imports", "exports", "undefined", "decorators", "Document", "doc", "doctype", "console", "warn", "body", "getComputedStyle", "testElement", "createElement", "classList", "add", "append<PERSON><PERSON><PERSON>", "computedStyle", "display", "remove", "full", "mixinDisabled", "base", "_disabled", "disabled", "value", "mixinColor", "defaultColor", "color", "_color", "colorPalette", "_elementRef", "nativeElement", "mixinDisableRipple", "_disableRipple", "disable<PERSON><PERSON><PERSON>", "mixinTabIndex", "defaultTabIndex", "_tabIndex", "tabIndex", "mixinErrorState", "stateChanges", "errorState", "updateErrorState", "oldState", "parent", "_parentFormGroup", "_parentForm", "matcher", "errorStateMatcher", "_defaultErrorStateMatcher", "control", "ngControl", "newState", "isErrorState", "next", "mixinInitialized", "_isInitialized", "_pendingSubscribers", "initialized", "subscriber", "_notifySubscriber", "push", "_markInitialized", "Error", "for<PERSON>ach", "complete", "MAT_DATE_LOCALE", "MAT_DATE_LOCALE_FACTORY", "DateAdapter", "_localeChanges", "localeChanges", "getValidDateOrNull", "obj", "isDateInstance", "<PERSON><PERSON><PERSON><PERSON>", "deserialize", "invalid", "setLocale", "locale", "compareDate", "first", "second", "getYear", "getMonth", "getDate", "sameDate", "firstValid", "second<PERSON><PERSON><PERSON>", "clampDate", "date", "min", "max", "MAT_DATE_FORMATS", "ISO_8601_REGEX", "range", "length", "valueFunction", "valuesArray", "Array", "i", "NativeDateAdapter", "matDateLocale", "_platform", "useUtcForDisplay", "getFullYear", "getDayOfWeek", "getDay", "getMonthNames", "style", "dtf", "Intl", "DateTimeFormat", "month", "timeZone", "_format", "Date", "getDateNames", "day", "getDayOfWeekNames", "weekday", "getYearName", "year", "getFirstDayOfWeek", "getNumDaysInMonth", "_createDateWithOverflow", "clone", "getTime", "createDate", "result", "today", "parse", "format", "displayFormat", "addCalendarYears", "years", "addCalendarMonths", "months", "newDate", "addCalendarDays", "days", "toIso8601", "getUTCFullYear", "_2digit", "getUTCMonth", "getUTCDate", "join", "test", "isNaN", "NaN", "d", "setFullYear", "setHours", "n", "slice", "setUTCFullYear", "setUTCHours", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "Platform", "ɵprov", "MAT_NATIVE_DATE_FORMATS", "dateInput", "month<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateA11yLabel", "monthYearA11yLabel", "NativeDateModule", "provide", "useClass", "providers", "MatNativeDateModule", "useValue", "ShowOnDirtyErrorStateMatcher", "form", "dirty", "submitted", "ErrorStateMatcher", "touched", "MatLine", "ɵdir", "selector", "host", "setLines", "lines", "element", "prefix", "changes", "pipe", "subscribe", "setClass", "className", "isAdd", "toggle", "MatLineModule", "declarations", "RippleRef", "_renderer", "config", "state", "fadeOut", "fadeOutRipple", "defaultRippleAnimationConfig", "enterDuration", "exitDuration", "ignoreMouseEventsTimeout", "passiveEventOptions", "passive", "pointerDownEvents", "pointerUpEvents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_target", "_ngZone", "elementOrElementRef", "platform", "_isPointerDown", "_activeRipples", "Set", "_pointerUpEventsRegistered", "<PERSON><PERSON><PERSON><PERSON>", "_containerElement", "fadeInRipple", "x", "y", "containerRect", "_containerRect", "getBoundingClientRect", "animationConfig", "animation", "centered", "left", "width", "top", "height", "radius", "distanceToFurthestCorner", "offsetX", "offsetY", "duration", "ripple", "document", "backgroundColor", "transitionDuration", "enforceStyleRecalculation", "transform", "rippleRef", "persistent", "_mostRecentTransientRipple", "_runTimeoutOutsideZone", "isMostRecentTransientRipple", "wasActive", "delete", "size", "rippleEl", "opacity", "fadeOutAll", "fadeOutAllNonPersistent", "setupTriggerEvents", "_triggerElement", "_removeTriggerEvents", "_registerEvents", "handleEvent", "event", "_onMousedown", "_onTouchStart", "_onPointerUp", "isFakeMousedown", "isSyntheticEvent", "_lastTouchStartEvent", "now", "rippleDisabled", "clientX", "clientY", "rippleConfig", "touches", "changedTouches", "isVisible", "terminateOnPointerUp", "fn", "delay", "runOutsideAngular", "setTimeout", "eventTypes", "addEventListener", "removeEventListener", "window", "getPropertyValue", "rect", "distX", "Math", "abs", "right", "distY", "bottom", "sqrt", "MAT_RIPPLE_GLOBAL_OPTIONS", "<PERSON><PERSON><PERSON><PERSON>", "ngZone", "globalOptions", "_animationMode", "_globalOptions", "_ripple<PERSON><PERSON>er", "_setupTriggerEventsIfEnabled", "trigger", "_trigger", "ngOnInit", "ngOnDestroy", "launch", "configOrX", "ElementRef", "NgZone", "exportAs", "unbounded", "MatRippleModule", "MatPseudoCheckbox", "ɵcmp", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "MatPseudoCheckboxModule", "MAT_OPTION_PARENT_COMPONENT", "_MatOptgroupMixinBase", "_uniqueOptgroupIdCounter", "_MatOptgroupBase", "_labelId", "_inert", "inertGroups", "label", "MAT_OPTGROUP", "MatOptgroup", "useExisting", "inputs", "_uniqueIdCounter", "MatOptionSelectionChange", "source", "isUserInput", "_MatOptionBase", "_element", "_changeDetectorRef", "_parent", "group", "_selected", "_active", "_mostRecentViewValue", "id", "onSelectionChange", "_stateChanges", "multiple", "selected", "active", "viewValue", "_getHostElement", "textContent", "trim", "select", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_emitSelectionChangeEvent", "deselect", "focus", "_origin", "options", "setActiveStyles", "setInactiveStyles", "get<PERSON><PERSON><PERSON>", "_handleKeydown", "keyCode", "_selectViaInteraction", "preventDefault", "_getAriaSelected", "_getTabIndex", "ngAfterViewChecked", "emit", "ChangeDetectorRef", "MatOption", "changeDetectorRef", "NgIf", "_countGroupLabelsBeforeOption", "optionIndex", "optionGroups", "optionsArray", "toArray", "groups", "groupCounter", "_getOptionScrollPosition", "optionOffset", "optionHeight", "currentScrollPosition", "panelHeight", "MatOptionModule"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,OAAT,EAAkBC,cAAlB,EAAkCC,QAAlC,EAA4CC,QAA5C,EAAsDC,MAAtD,EAA8DC,MAA9D,EAAsEC,SAAtE,EAAiFC,UAAjF,EAA6FC,SAA7F,EAAwGC,KAAxG,EAA+GC,SAA/G,EAA0HC,iBAA1H,EAA6IC,uBAA7I,EAAsKC,YAAtK,EAAoLC,MAApL,QAAkM,eAAlM;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,+BAAT,EAA0CC,gCAA1C,QAAkF,mBAAlF;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,SAASC,OAAO,IAAIC,SAApB,QAAqC,cAArC;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;AACA,OAAO,KAAKC,IAAZ,MAAsB,uBAAtB;AACA,SAASC,kBAAT,EAA6BC,+BAA7B,QAAoE,uBAApE;AACA,SAASC,qBAAT,EAAgCC,oBAAhC,EAAsDC,aAAtD,QAA2E,uBAA3E;AACA,SAASC,OAAT,EAAkBC,UAAlB,QAAoC,MAApC;AACA,SAASC,SAAT,QAA0B,gBAA1B;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,KAAT,EAAgBC,KAAhB,EAAuBC,cAAvB,QAA6C,uBAA7C;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;;;;;AAiFkGrC,IAAAA,EA2hDokB,uC;;;;mBA3hDpkBA,E;AAAAA,IAAAA,EA2hDspB,4F;;;;;;AA3hDtpBA,IAAAA,EA2hDw4B,6B;AA3hDx4BA,IAAAA,EA2hD48B,U;AA3hD58BA,IAAAA,EA2hD+9B,e;;;;mBA3hD/9BA,E;AAAAA,IAAAA,EA2hD48B,a;AA3hD58BA,IAAAA,EA2hD48B,iD;;;;;AA3mD9iC,MAAMoB,OAAO,GAAG,IAAInB,OAAJ,CAAY,QAAZ,CAAhB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMqC,eAAN,CAAsB;;AAEtBA,eAAe,CAACC,cAAhB,GAAiC,6BAAjC;AACAD,eAAe,CAACE,kBAAhB,GAAqC,6BAArC;AACAF,eAAe,CAACG,kBAAhB,GAAqC,2BAArC;AACAH,eAAe,CAACI,WAAhB,GAA8B,6BAA9B;AACA;;AACA,MAAMC,kBAAN,CAAyB;;AAEzBA,kBAAkB,CAACC,OAAnB,GAA6B,OAA7B;AACAD,kBAAkB,CAACE,QAAnB,GAA8B,OAA9B;AACAF,kBAAkB,CAACG,OAAnB,GAA6B,OAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,SAASC,8BAAT,GAA0C;AACtC,SAAO,IAAP;AACH;AACD;;;AACA,MAAMC,sBAAsB,GAAG,IAAI9C,cAAJ,CAAmB,mBAAnB,EAAwC;AACnE+C,EAAAA,UAAU,EAAE,MADuD;AAEnEC,EAAAA,OAAO,EAAEH;AAF0D,CAAxC,CAA/B;AAIA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMI,eAAN,CAAsB;AAClBC,EAAAA,WAAW,CAACC,wBAAD,EAA2BC,aAA3B,EAA0CC,SAA1C,EAAqD;AAC5D,SAAKD,aAAL,GAAqBA,aAArB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA;;AACA,SAAKC,oBAAL,GAA4B,KAA5B,CAJ4D,CAK5D;AACA;;AACAH,IAAAA,wBAAwB,CAACI,oCAAzB;;AACA,QAAI,CAAC,KAAKD,oBAAV,EAAgC;AAC5B,WAAKA,oBAAL,GAA4B,IAA5B;;AACA,UAAI,OAAOE,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,YAAI,KAAKC,eAAL,CAAqB,SAArB,CAAJ,EAAqC;AACjCC,UAAAA,sBAAsB,CAAC,KAAKL,SAAN,CAAtB;AACH;;AACD,YAAI,KAAKI,eAAL,CAAqB,OAArB,CAAJ,EAAmC;AAC/BE,UAAAA,oBAAoB,CAAC,KAAKN,SAAN,CAApB;AACH;;AACD,YAAI,KAAKI,eAAL,CAAqB,SAArB,CAAJ,EAAqC;AACjCG,UAAAA,qBAAqB;AACxB;AACJ;AACJ;AACJ;AACD;;;AACAH,EAAAA,eAAe,CAACI,IAAD,EAAO;AAClB,QAAIrC,kBAAkB,EAAtB,EAA0B;AACtB,aAAO,KAAP;AACH;;AACD,QAAI,OAAO,KAAK4B,aAAZ,KAA8B,SAAlC,EAA6C;AACzC,aAAO,KAAKA,aAAZ;AACH;;AACD,WAAO,CAAC,CAAC,KAAKA,aAAL,CAAmBS,IAAnB,CAAT;AACH;;AAjCiB;;AAmCtBZ,eAAe,CAACa,IAAhB;AAAA,mBAA4Gb,eAA5G,EAAkGnD,EAAlG,UAA6IgB,EAAE,CAACiD,wBAAhJ,GAAkGjE,EAAlG,UAAqLgD,sBAArL,MAAkGhD,EAAlG,UAAwOuB,QAAxO;AAAA;;AACA4B,eAAe,CAACe,IAAhB,kBADkGlE,EAClG;AAAA,QAA6GmD;AAA7G;AACAA,eAAe,CAACgB,IAAhB,kBAFkGnE,EAElG;AAAA,YAAwI,CAACmB,UAAD,CAAxI,EAAsJA,UAAtJ;AAAA;;AACA;AAAA,qDAHkGnB,EAGlG,mBAA2FmD,eAA3F,EAAwH,CAAC;AAC7GiB,IAAAA,IAAI,EAAEjE,QADuG;AAE7GkE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,OAAO,EAAE,CAACnD,UAAD,CADV;AAECoD,MAAAA,OAAO,EAAE,CAACpD,UAAD;AAFV,KAAD;AAFuG,GAAD,CAAxH,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAEiD,MAAAA,IAAI,EAAEpD,EAAE,CAACiD;AAAX,KAAD,EAAwC;AAAEG,MAAAA,IAAI,EAAEI,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACrGL,QAAAA,IAAI,EAAEhE;AAD+F,OAAD,EAErG;AACCgE,QAAAA,IAAI,EAAE/D,MADP;AAECgE,QAAAA,IAAI,EAAE,CAACrB,sBAAD;AAFP,OAFqG;AAA/B,KAAxC,EAK3B;AAAEoB,MAAAA,IAAI,EAAEM,QAAR;AAAkBD,MAAAA,UAAU,EAAE,CAAC;AACjCL,QAAAA,IAAI,EAAE/D,MAD2B;AAEjCgE,QAAAA,IAAI,EAAE,CAAC9C,QAAD;AAF2B,OAAD;AAA9B,KAL2B,CAAP;AAQlB,GAdxB;AAAA;AAeA;;;AACA,SAASqC,sBAAT,CAAgCe,GAAhC,EAAqC;AACjC,MAAI,CAACA,GAAG,CAACC,OAAT,EAAkB;AACdC,IAAAA,OAAO,CAACC,IAAR,CAAa,8DACT,6DADJ;AAEH;AACJ;AACD;;;AACA,SAASjB,oBAAT,CAA8Bc,GAA9B,EAAmC;AAC/B;AACA;AACA,MAAI,CAACA,GAAG,CAACI,IAAL,IAAa,OAAOC,gBAAP,KAA4B,UAA7C,EAAyD;AACrD;AACH;;AACD,QAAMC,WAAW,GAAGN,GAAG,CAACO,aAAJ,CAAkB,KAAlB,CAApB;AACAD,EAAAA,WAAW,CAACE,SAAZ,CAAsBC,GAAtB,CAA0B,yBAA1B;AACAT,EAAAA,GAAG,CAACI,IAAJ,CAASM,WAAT,CAAqBJ,WAArB;AACA,QAAMK,aAAa,GAAGN,gBAAgB,CAACC,WAAD,CAAtC,CAT+B,CAU/B;AACA;AACA;;AACA,MAAIK,aAAa,IAAIA,aAAa,CAACC,OAAd,KAA0B,MAA/C,EAAuD;AACnDV,IAAAA,OAAO,CAACC,IAAR,CAAa,+DACT,2DADS,GAET,iEAFJ;AAGH;;AACDG,EAAAA,WAAW,CAACO,MAAZ;AACH;AACD;;;AACA,SAAS1B,qBAAT,GAAiC;AAC7B,MAAI1C,OAAO,CAACqE,IAAR,KAAiBpE,SAAS,CAACoE,IAA/B,EAAqC;AACjCZ,IAAAA,OAAO,CAACC,IAAR,CAAa,mCACT1D,OAAO,CAACqE,IADC,GAET,mBAFS,GAGT,2BAHS,GAITpE,SAAS,CAACoE,IAJD,GAKT,MALS,GAMT,iEANJ;AAOH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,aAAT,CAAuBC,IAAvB,EAA6B;AACzB,SAAO,cAAcA,IAAd,CAAmB;AACtBvC,IAAAA,WAAW,CAAC,GAAGiB,IAAJ,EAAU;AACjB,YAAM,GAAGA,IAAT;AACA,WAAKuB,SAAL,GAAiB,KAAjB;AACH;;AACW,QAARC,QAAQ,GAAG;AACX,aAAO,KAAKD,SAAZ;AACH;;AACW,QAARC,QAAQ,CAACC,KAAD,EAAQ;AAChB,WAAKF,SAAL,GAAiBhE,qBAAqB,CAACkE,KAAD,CAAtC;AACH;;AAVqB,GAA1B;AAYH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,UAAT,CAAoBJ,IAApB,EAA0BK,YAA1B,EAAwC;AACpC,SAAO,cAAcL,IAAd,CAAmB;AACtBvC,IAAAA,WAAW,CAAC,GAAGiB,IAAJ,EAAU;AACjB,YAAM,GAAGA,IAAT;AACA,WAAK2B,YAAL,GAAoBA,YAApB,CAFiB,CAGjB;;AACA,WAAKC,KAAL,GAAaD,YAAb;AACH;;AACQ,QAALC,KAAK,GAAG;AACR,aAAO,KAAKC,MAAZ;AACH;;AACQ,QAALD,KAAK,CAACH,KAAD,EAAQ;AACb,YAAMK,YAAY,GAAGL,KAAK,IAAI,KAAKE,YAAnC;;AACA,UAAIG,YAAY,KAAK,KAAKD,MAA1B,EAAkC;AAC9B,YAAI,KAAKA,MAAT,EAAiB;AACb,eAAKE,WAAL,CAAiBC,aAAjB,CAA+BlB,SAA/B,CAAyCK,MAAzC,CAAiD,OAAM,KAAKU,MAAO,EAAnE;AACH;;AACD,YAAIC,YAAJ,EAAkB;AACd,eAAKC,WAAL,CAAiBC,aAAjB,CAA+BlB,SAA/B,CAAyCC,GAAzC,CAA8C,OAAMe,YAAa,EAAjE;AACH;;AACD,aAAKD,MAAL,GAAcC,YAAd;AACH;AACJ;;AArBqB,GAA1B;AAuBH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,kBAAT,CAA4BX,IAA5B,EAAkC;AAC9B,SAAO,cAAcA,IAAd,CAAmB;AACtBvC,IAAAA,WAAW,CAAC,GAAGiB,IAAJ,EAAU;AACjB,YAAM,GAAGA,IAAT;AACA,WAAKkC,cAAL,GAAsB,KAAtB;AACH;AACD;;;AACiB,QAAbC,aAAa,GAAG;AAChB,aAAO,KAAKD,cAAZ;AACH;;AACgB,QAAbC,aAAa,CAACV,KAAD,EAAQ;AACrB,WAAKS,cAAL,GAAsB3E,qBAAqB,CAACkE,KAAD,CAA3C;AACH;;AAXqB,GAA1B;AAaH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASW,aAAT,CAAuBd,IAAvB,EAA6Be,eAAe,GAAG,CAA/C,EAAkD;AAC9C,SAAO,cAAcf,IAAd,CAAmB;AACtBvC,IAAAA,WAAW,CAAC,GAAGiB,IAAJ,EAAU;AACjB,YAAM,GAAGA,IAAT;AACA,WAAKsC,SAAL,GAAiBD,eAAjB;AACA,WAAKA,eAAL,GAAuBA,eAAvB;AACH;;AACW,QAARE,QAAQ,GAAG;AACX,aAAO,KAAKf,QAAL,GAAgB,CAAC,CAAjB,GAAqB,KAAKc,SAAjC;AACH;;AACW,QAARC,QAAQ,CAACd,KAAD,EAAQ;AAChB;AACA,WAAKa,SAAL,GAAiBb,KAAK,IAAI,IAAT,GAAgBjE,oBAAoB,CAACiE,KAAD,CAApC,GAA8C,KAAKY,eAApE;AACH;;AAZqB,GAA1B;AAcH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,eAAT,CAAyBlB,IAAzB,EAA+B;AAC3B,SAAO,cAAcA,IAAd,CAAmB;AACtBvC,IAAAA,WAAW,CAAC,GAAGiB,IAAJ,EAAU;AACjB,YAAM,GAAGA,IAAT,EADiB,CAEjB;AACA;AACA;AACA;;AACA;;AACA,WAAKyC,YAAL,GAAoB,IAAI/E,OAAJ,EAApB;AACA;;AACA,WAAKgF,UAAL,GAAkB,KAAlB;AACH;AACD;;;AACAC,IAAAA,gBAAgB,GAAG;AACf,YAAMC,QAAQ,GAAG,KAAKF,UAAtB;AACA,YAAMG,MAAM,GAAG,KAAKC,gBAAL,IAAyB,KAAKC,WAA7C;AACA,YAAMC,OAAO,GAAG,KAAKC,iBAAL,IAA0B,KAAKC,yBAA/C;AACA,YAAMC,OAAO,GAAG,KAAKC,SAAL,GAAiB,KAAKA,SAAL,CAAeD,OAAhC,GAA0C,IAA1D;AACA,YAAME,QAAQ,GAAGL,OAAO,CAACM,YAAR,CAAqBH,OAArB,EAA8BN,MAA9B,CAAjB;;AACA,UAAIQ,QAAQ,KAAKT,QAAjB,EAA2B;AACvB,aAAKF,UAAL,GAAkBW,QAAlB;AACA,aAAKZ,YAAL,CAAkBc,IAAlB;AACH;AACJ;;AAvBqB,GAA1B;AAyBH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASC,gBAAT,CAA0BlC,IAA1B,EAAgC;AAC5B,SAAO,cAAcA,IAAd,CAAmB;AACtBvC,IAAAA,WAAW,CAAC,GAAGiB,IAAJ,EAAU;AACjB,YAAM,GAAGA,IAAT;AACA;;AACA,WAAKyD,cAAL,GAAsB,KAAtB;AACA;AACZ;AACA;AACA;AACA;;AACY,WAAKC,mBAAL,GAA2B,EAA3B;AACA;AACZ;AACA;AACA;;AACY,WAAKC,WAAL,GAAmB,IAAIhG,UAAJ,CAAeiG,UAAU,IAAI;AAC5C;AACA;AACA,YAAI,KAAKH,cAAT,EAAyB;AACrB,eAAKI,iBAAL,CAAuBD,UAAvB;AACH,SAFD,MAGK;AACD,eAAKF,mBAAL,CAAyBI,IAAzB,CAA8BF,UAA9B;AACH;AACJ,OATkB,CAAnB;AAUH;AACD;AACR;AACA;AACA;AACA;;;AACQG,IAAAA,gBAAgB,GAAG;AACf,UAAI,KAAKN,cAAL,KAAwB,OAAOpE,SAAP,KAAqB,WAArB,IAAoCA,SAA5D,CAAJ,EAA4E;AACxE,cAAM2E,KAAK,CAAC,+DACR,6BADO,CAAX;AAEH;;AACD,WAAKP,cAAL,GAAsB,IAAtB;;AACA,WAAKC,mBAAL,CAAyBO,OAAzB,CAAiC,KAAKJ,iBAAtC;;AACA,WAAKH,mBAAL,GAA2B,IAA3B;AACH;AACD;;;AACAG,IAAAA,iBAAiB,CAACD,UAAD,EAAa;AAC1BA,MAAAA,UAAU,CAACL,IAAX;AACAK,MAAAA,UAAU,CAACM,QAAX;AACH;;AA5CqB,GAA1B;AA8CH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,eAAe,GAAG,IAAItI,cAAJ,CAAmB,iBAAnB,EAAsC;AAC1D+C,EAAAA,UAAU,EAAE,MAD8C;AAE1DC,EAAAA,OAAO,EAAEuF;AAFiD,CAAtC,CAAxB;AAIA;;AACA,SAASA,uBAAT,GAAmC;AAC/B,SAAOnI,MAAM,CAACC,SAAD,CAAb;AACH;AACD;;;AACA,MAAMmI,WAAN,CAAkB;AACdtF,EAAAA,WAAW,GAAG;AACV,SAAKuF,cAAL,GAAsB,IAAI5G,OAAJ,EAAtB;AACA;;AACA,SAAK6G,aAAL,GAAqB,KAAKD,cAA1B;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIE,EAAAA,kBAAkB,CAACC,GAAD,EAAM;AACpB,WAAO,KAAKC,cAAL,CAAoBD,GAApB,KAA4B,KAAKE,OAAL,CAAaF,GAAb,CAA5B,GAAgDA,GAAhD,GAAsD,IAA7D;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIG,EAAAA,WAAW,CAACnD,KAAD,EAAQ;AACf,QAAIA,KAAK,IAAI,IAAT,IAAkB,KAAKiD,cAAL,CAAoBjD,KAApB,KAA8B,KAAKkD,OAAL,CAAalD,KAAb,CAApD,EAA0E;AACtE,aAAOA,KAAP;AACH;;AACD,WAAO,KAAKoD,OAAL,EAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,SAAS,CAACC,MAAD,EAAS;AACd,SAAKA,MAAL,GAAcA,MAAd;;AACA,SAAKT,cAAL,CAAoBf,IAApB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIyB,EAAAA,WAAW,CAACC,KAAD,EAAQC,MAAR,EAAgB;AACvB,WAAQ,KAAKC,OAAL,CAAaF,KAAb,IAAsB,KAAKE,OAAL,CAAaD,MAAb,CAAtB,IACJ,KAAKE,QAAL,CAAcH,KAAd,IAAuB,KAAKG,QAAL,CAAcF,MAAd,CADnB,IAEJ,KAAKG,OAAL,CAAaJ,KAAb,IAAsB,KAAKI,OAAL,CAAaH,MAAb,CAF1B;AAGH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACII,EAAAA,QAAQ,CAACL,KAAD,EAAQC,MAAR,EAAgB;AACpB,QAAID,KAAK,IAAIC,MAAb,EAAqB;AACjB,UAAIK,UAAU,GAAG,KAAKZ,OAAL,CAAaM,KAAb,CAAjB;AACA,UAAIO,WAAW,GAAG,KAAKb,OAAL,CAAaO,MAAb,CAAlB;;AACA,UAAIK,UAAU,IAAIC,WAAlB,EAA+B;AAC3B,eAAO,CAAC,KAAKR,WAAL,CAAiBC,KAAjB,EAAwBC,MAAxB,CAAR;AACH;;AACD,aAAOK,UAAU,IAAIC,WAArB;AACH;;AACD,WAAOP,KAAK,IAAIC,MAAhB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIO,EAAAA,SAAS,CAACC,IAAD,EAAOC,GAAP,EAAYC,GAAZ,EAAiB;AACtB,QAAID,GAAG,IAAI,KAAKX,WAAL,CAAiBU,IAAjB,EAAuBC,GAAvB,IAA8B,CAAzC,EAA4C;AACxC,aAAOA,GAAP;AACH;;AACD,QAAIC,GAAG,IAAI,KAAKZ,WAAL,CAAiBU,IAAjB,EAAuBE,GAAvB,IAA8B,CAAzC,EAA4C;AACxC,aAAOA,GAAP;AACH;;AACD,WAAOF,IAAP;AACH;;AAvFa;AA0FlB;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMG,gBAAgB,GAAG,IAAIhK,cAAJ,CAAmB,kBAAnB,CAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMiK,cAAc,GAAG,oFAAvB;AACA;;AACA,SAASC,KAAT,CAAeC,MAAf,EAAuBC,aAAvB,EAAsC;AAClC,QAAMC,WAAW,GAAGC,KAAK,CAACH,MAAD,CAAzB;;AACA,OAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,MAApB,EAA4BI,CAAC,EAA7B,EAAiC;AAC7BF,IAAAA,WAAW,CAACE,CAAD,CAAX,GAAiBH,aAAa,CAACG,CAAD,CAA9B;AACH;;AACD,SAAOF,WAAP;AACH;AACD;;;AACA,MAAMG,iBAAN,SAAgChC,WAAhC,CAA4C;AACxCtF,EAAAA,WAAW,CAACuH,aAAD;AACX;AACJ;AACA;AACA;AACIC,EAAAA,SALW,EAKA;AACP;AACA;AACR;AACA;AACA;;AACQ,SAAKC,gBAAL,GAAwB,KAAxB;AACA,UAAM1B,SAAN,CAAgBwB,aAAhB;AACH;;AACDnB,EAAAA,OAAO,CAACO,IAAD,EAAO;AACV,WAAOA,IAAI,CAACe,WAAL,EAAP;AACH;;AACDrB,EAAAA,QAAQ,CAACM,IAAD,EAAO;AACX,WAAOA,IAAI,CAACN,QAAL,EAAP;AACH;;AACDC,EAAAA,OAAO,CAACK,IAAD,EAAO;AACV,WAAOA,IAAI,CAACL,OAAL,EAAP;AACH;;AACDqB,EAAAA,YAAY,CAAChB,IAAD,EAAO;AACf,WAAOA,IAAI,CAACiB,MAAL,EAAP;AACH;;AACDC,EAAAA,aAAa,CAACC,KAAD,EAAQ;AACjB,UAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAT,CAAwB,KAAKjC,MAA7B,EAAqC;AAAEkC,MAAAA,KAAK,EAAEJ,KAAT;AAAgBK,MAAAA,QAAQ,EAAE;AAA1B,KAArC,CAAZ;AACA,WAAOnB,KAAK,CAAC,EAAD,EAAKK,CAAC,IAAI,KAAKe,OAAL,CAAaL,GAAb,EAAkB,IAAIM,IAAJ,CAAS,IAAT,EAAehB,CAAf,EAAkB,CAAlB,CAAlB,CAAV,CAAZ;AACH;;AACDiB,EAAAA,YAAY,GAAG;AACX,UAAMP,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAT,CAAwB,KAAKjC,MAA7B,EAAqC;AAAEuC,MAAAA,GAAG,EAAE,SAAP;AAAkBJ,MAAAA,QAAQ,EAAE;AAA5B,KAArC,CAAZ;AACA,WAAOnB,KAAK,CAAC,EAAD,EAAKK,CAAC,IAAI,KAAKe,OAAL,CAAaL,GAAb,EAAkB,IAAIM,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkBhB,CAAC,GAAG,CAAtB,CAAlB,CAAV,CAAZ;AACH;;AACDmB,EAAAA,iBAAiB,CAACV,KAAD,EAAQ;AACrB,UAAMC,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAT,CAAwB,KAAKjC,MAA7B,EAAqC;AAAEyC,MAAAA,OAAO,EAAEX,KAAX;AAAkBK,MAAAA,QAAQ,EAAE;AAA5B,KAArC,CAAZ;AACA,WAAOnB,KAAK,CAAC,CAAD,EAAIK,CAAC,IAAI,KAAKe,OAAL,CAAaL,GAAb,EAAkB,IAAIM,IAAJ,CAAS,IAAT,EAAe,CAAf,EAAkBhB,CAAC,GAAG,CAAtB,CAAlB,CAAT,CAAZ;AACH;;AACDqB,EAAAA,WAAW,CAAC/B,IAAD,EAAO;AACd,UAAMoB,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAT,CAAwB,KAAKjC,MAA7B,EAAqC;AAAE2C,MAAAA,IAAI,EAAE,SAAR;AAAmBR,MAAAA,QAAQ,EAAE;AAA7B,KAArC,CAAZ;AACA,WAAO,KAAKC,OAAL,CAAaL,GAAb,EAAkBpB,IAAlB,CAAP;AACH;;AACDiC,EAAAA,iBAAiB,GAAG;AAChB;AACA,WAAO,CAAP;AACH;;AACDC,EAAAA,iBAAiB,CAAClC,IAAD,EAAO;AACpB,WAAO,KAAKL,OAAL,CAAa,KAAKwC,uBAAL,CAA6B,KAAK1C,OAAL,CAAaO,IAAb,CAA7B,EAAiD,KAAKN,QAAL,CAAcM,IAAd,IAAsB,CAAvE,EAA0E,CAA1E,CAAb,CAAP;AACH;;AACDoC,EAAAA,KAAK,CAACpC,IAAD,EAAO;AACR,WAAO,IAAI0B,IAAJ,CAAS1B,IAAI,CAACqC,OAAL,EAAT,CAAP;AACH;;AACDC,EAAAA,UAAU,CAACN,IAAD,EAAOT,KAAP,EAAcvB,IAAd,EAAoB;AAC1B,QAAI,OAAOrG,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C;AACA;AACA,UAAI4H,KAAK,GAAG,CAAR,IAAaA,KAAK,GAAG,EAAzB,EAA6B;AACzB,cAAMjD,KAAK,CAAE,wBAAuBiD,KAAM,4CAA/B,CAAX;AACH;;AACD,UAAIvB,IAAI,GAAG,CAAX,EAAc;AACV,cAAM1B,KAAK,CAAE,iBAAgB0B,IAAK,mCAAvB,CAAX;AACH;AACJ;;AACD,QAAIuC,MAAM,GAAG,KAAKJ,uBAAL,CAA6BH,IAA7B,EAAmCT,KAAnC,EAA0CvB,IAA1C,CAAb,CAX0B,CAY1B;;;AACA,QAAIuC,MAAM,CAAC7C,QAAP,MAAqB6B,KAArB,KAA+B,OAAO5H,SAAP,KAAqB,WAArB,IAAoCA,SAAnE,CAAJ,EAAmF;AAC/E,YAAM2E,KAAK,CAAE,iBAAgB0B,IAAK,2BAA0BuB,KAAM,IAAvD,CAAX;AACH;;AACD,WAAOgB,MAAP;AACH;;AACDC,EAAAA,KAAK,GAAG;AACJ,WAAO,IAAId,IAAJ,EAAP;AACH;;AACDe,EAAAA,KAAK,CAAC1G,KAAD,EAAQ;AACT;AACA;AACA,QAAI,OAAOA,KAAP,IAAgB,QAApB,EAA8B;AAC1B,aAAO,IAAI2F,IAAJ,CAAS3F,KAAT,CAAP;AACH;;AACD,WAAOA,KAAK,GAAG,IAAI2F,IAAJ,CAASA,IAAI,CAACe,KAAL,CAAW1G,KAAX,CAAT,CAAH,GAAiC,IAA7C;AACH;;AACD2G,EAAAA,MAAM,CAAC1C,IAAD,EAAO2C,aAAP,EAAsB;AACxB,QAAI,CAAC,KAAK1D,OAAL,CAAae,IAAb,CAAL,EAAyB;AACrB,YAAM1B,KAAK,CAAC,gDAAD,CAAX;AACH;;AACD,UAAM8C,GAAG,GAAG,IAAIC,IAAI,CAACC,cAAT,CAAwB,KAAKjC,MAA7B,EAAqC,EAAE,GAAGsD,aAAL;AAAoBnB,MAAAA,QAAQ,EAAE;AAA9B,KAArC,CAAZ;AACA,WAAO,KAAKC,OAAL,CAAaL,GAAb,EAAkBpB,IAAlB,CAAP;AACH;;AACD4C,EAAAA,gBAAgB,CAAC5C,IAAD,EAAO6C,KAAP,EAAc;AAC1B,WAAO,KAAKC,iBAAL,CAAuB9C,IAAvB,EAA6B6C,KAAK,GAAG,EAArC,CAAP;AACH;;AACDC,EAAAA,iBAAiB,CAAC9C,IAAD,EAAO+C,MAAP,EAAe;AAC5B,QAAIC,OAAO,GAAG,KAAKb,uBAAL,CAA6B,KAAK1C,OAAL,CAAaO,IAAb,CAA7B,EAAiD,KAAKN,QAAL,CAAcM,IAAd,IAAsB+C,MAAvE,EAA+E,KAAKpD,OAAL,CAAaK,IAAb,CAA/E,CAAd,CAD4B,CAE5B;AACA;AACA;AACA;;;AACA,QAAI,KAAKN,QAAL,CAAcsD,OAAd,KAA0B,CAAE,CAAC,KAAKtD,QAAL,CAAcM,IAAd,IAAsB+C,MAAvB,IAAiC,EAAlC,GAAwC,EAAzC,IAA+C,EAA7E,EAAiF;AAC7EC,MAAAA,OAAO,GAAG,KAAKb,uBAAL,CAA6B,KAAK1C,OAAL,CAAauD,OAAb,CAA7B,EAAoD,KAAKtD,QAAL,CAAcsD,OAAd,CAApD,EAA4E,CAA5E,CAAV;AACH;;AACD,WAAOA,OAAP;AACH;;AACDC,EAAAA,eAAe,CAACjD,IAAD,EAAOkD,IAAP,EAAa;AACxB,WAAO,KAAKf,uBAAL,CAA6B,KAAK1C,OAAL,CAAaO,IAAb,CAA7B,EAAiD,KAAKN,QAAL,CAAcM,IAAd,CAAjD,EAAsE,KAAKL,OAAL,CAAaK,IAAb,IAAqBkD,IAA3F,CAAP;AACH;;AACDC,EAAAA,SAAS,CAACnD,IAAD,EAAO;AACZ,WAAO,CACHA,IAAI,CAACoD,cAAL,EADG,EAEH,KAAKC,OAAL,CAAarD,IAAI,CAACsD,WAAL,KAAqB,CAAlC,CAFG,EAGH,KAAKD,OAAL,CAAarD,IAAI,CAACuD,UAAL,EAAb,CAHG,EAILC,IAJK,CAIA,GAJA,CAAP;AAKH;AACD;AACJ;AACA;AACA;AACA;;;AACItE,EAAAA,WAAW,CAACnD,KAAD,EAAQ;AACf,QAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;AAC3B,UAAI,CAACA,KAAL,EAAY;AACR,eAAO,IAAP;AACH,OAH0B,CAI3B;AACA;;;AACA,UAAIqE,cAAc,CAACqD,IAAf,CAAoB1H,KAApB,CAAJ,EAAgC;AAC5B,YAAIiE,IAAI,GAAG,IAAI0B,IAAJ,CAAS3F,KAAT,CAAX;;AACA,YAAI,KAAKkD,OAAL,CAAae,IAAb,CAAJ,EAAwB;AACpB,iBAAOA,IAAP;AACH;AACJ;AACJ;;AACD,WAAO,MAAMd,WAAN,CAAkBnD,KAAlB,CAAP;AACH;;AACDiD,EAAAA,cAAc,CAACD,GAAD,EAAM;AAChB,WAAOA,GAAG,YAAY2C,IAAtB;AACH;;AACDzC,EAAAA,OAAO,CAACe,IAAD,EAAO;AACV,WAAO,CAAC0D,KAAK,CAAC1D,IAAI,CAACqC,OAAL,EAAD,CAAb;AACH;;AACDlD,EAAAA,OAAO,GAAG;AACN,WAAO,IAAIuC,IAAJ,CAASiC,GAAT,CAAP;AACH;AACD;;;AACAxB,EAAAA,uBAAuB,CAACH,IAAD,EAAOT,KAAP,EAAcvB,IAAd,EAAoB;AACvC;AACA;AACA,UAAM4D,CAAC,GAAG,IAAIlC,IAAJ,EAAV;AACAkC,IAAAA,CAAC,CAACC,WAAF,CAAc7B,IAAd,EAAoBT,KAApB,EAA2BvB,IAA3B;AACA4D,IAAAA,CAAC,CAACE,QAAF,CAAW,CAAX,EAAc,CAAd,EAAiB,CAAjB,EAAoB,CAApB;AACA,WAAOF,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIP,EAAAA,OAAO,CAACU,CAAD,EAAI;AACP,WAAO,CAAC,OAAOA,CAAR,EAAWC,KAAX,CAAiB,CAAC,CAAlB,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIvC,EAAAA,OAAO,CAACL,GAAD,EAAMpB,IAAN,EAAY;AACf;AACA;AACA,UAAM4D,CAAC,GAAG,IAAIlC,IAAJ,EAAV;AACAkC,IAAAA,CAAC,CAACK,cAAF,CAAiBjE,IAAI,CAACe,WAAL,EAAjB,EAAqCf,IAAI,CAACN,QAAL,EAArC,EAAsDM,IAAI,CAACL,OAAL,EAAtD;AACAiE,IAAAA,CAAC,CAACM,WAAF,CAAclE,IAAI,CAACmE,QAAL,EAAd,EAA+BnE,IAAI,CAACoE,UAAL,EAA/B,EAAkDpE,IAAI,CAACqE,UAAL,EAAlD,EAAqErE,IAAI,CAACsE,eAAL,EAArE;AACA,WAAOlD,GAAG,CAACsB,MAAJ,CAAWkB,CAAX,CAAP;AACH;;AAlLuC;;AAoL5CjD,iBAAiB,CAAC1G,IAAlB;AAAA,mBAA8G0G,iBAA9G,EAnkBkG1K,EAmkBlG,UAAiJwI,eAAjJ,MAnkBkGxI,EAmkBlG,UAA6LyB,IAAI,CAAC6M,QAAlM;AAAA;;AACA5D,iBAAiB,CAAC6D,KAAlB,kBApkBkGvO,EAokBlG;AAAA,SAAkH0K,iBAAlH;AAAA,WAAkHA,iBAAlH;AAAA;;AACA;AAAA,qDArkBkG1K,EAqkBlG,mBAA2F0K,iBAA3F,EAA0H,CAAC;AAC/GtG,IAAAA,IAAI,EAAE5D;AADyG,GAAD,CAA1H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE4D,MAAAA,IAAI,EAAEI,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DL,QAAAA,IAAI,EAAEhE;AADwD,OAAD,EAE9D;AACCgE,QAAAA,IAAI,EAAE/D,MADP;AAECgE,QAAAA,IAAI,EAAE,CAACmE,eAAD;AAFP,OAF8D;AAA/B,KAAD,EAK3B;AAAEpE,MAAAA,IAAI,EAAE3C,IAAI,CAAC6M;AAAb,KAL2B,CAAP;AAKO,GAPjD;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAME,uBAAuB,GAAG;AAC5BhC,EAAAA,KAAK,EAAE;AACHiC,IAAAA,SAAS,EAAE;AADR,GADqB;AAI5BlJ,EAAAA,OAAO,EAAE;AACLkJ,IAAAA,SAAS,EAAE;AAAE1C,MAAAA,IAAI,EAAE,SAAR;AAAmBT,MAAAA,KAAK,EAAE,SAA1B;AAAqCK,MAAAA,GAAG,EAAE;AAA1C,KADN;AAEL+C,IAAAA,cAAc,EAAE;AAAE3C,MAAAA,IAAI,EAAE,SAAR;AAAmBT,MAAAA,KAAK,EAAE;AAA1B,KAFX;AAGLqD,IAAAA,aAAa,EAAE;AAAE5C,MAAAA,IAAI,EAAE,SAAR;AAAmBT,MAAAA,KAAK,EAAE,MAA1B;AAAkCK,MAAAA,GAAG,EAAE;AAAvC,KAHV;AAILiD,IAAAA,kBAAkB,EAAE;AAAE7C,MAAAA,IAAI,EAAE,SAAR;AAAmBT,MAAAA,KAAK,EAAE;AAA1B;AAJf;AAJmB,CAAhC;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMuD,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAAC7K,IAAjB;AAAA,mBAA6G6K,gBAA7G;AAAA;;AACAA,gBAAgB,CAAC3K,IAAjB,kBA3mBkGlE,EA2mBlG;AAAA,QAA8G6O;AAA9G;AACAA,gBAAgB,CAAC1K,IAAjB,kBA5mBkGnE,EA4mBlG;AAAA,aAA2I,CAAC;AAAE8O,IAAAA,OAAO,EAAEpG,WAAX;AAAwBqG,IAAAA,QAAQ,EAAErE;AAAlC,GAAD;AAA3I;;AACA;AAAA,qDA7mBkG1K,EA6mBlG,mBAA2F6O,gBAA3F,EAAyH,CAAC;AAC9GzK,IAAAA,IAAI,EAAEjE,QADwG;AAE9GkE,IAAAA,IAAI,EAAE,CAAC;AACC2K,MAAAA,SAAS,EAAE,CAAC;AAAEF,QAAAA,OAAO,EAAEpG,WAAX;AAAwBqG,QAAAA,QAAQ,EAAErE;AAAlC,OAAD;AADZ,KAAD;AAFwG,GAAD,CAAzH;AAAA;;AAMA,MAAMuE,mBAAN,CAA0B;;AAE1BA,mBAAmB,CAACjL,IAApB;AAAA,mBAAgHiL,mBAAhH;AAAA;;AACAA,mBAAmB,CAAC/K,IAApB,kBAtnBkGlE,EAsnBlG;AAAA,QAAiHiP;AAAjH;AACAA,mBAAmB,CAAC9K,IAApB,kBAvnBkGnE,EAunBlG;AAAA,aAAiJ,CAAC;AAAE8O,IAAAA,OAAO,EAAE5E,gBAAX;AAA6BgF,IAAAA,QAAQ,EAAEV;AAAvC,GAAD,CAAjJ;AAAA,YAA+N,CAACK,gBAAD,CAA/N;AAAA;;AACA;AAAA,qDAxnBkG7O,EAwnBlG,mBAA2FiP,mBAA3F,EAA4H,CAAC;AACjH7K,IAAAA,IAAI,EAAEjE,QAD2G;AAEjHkE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,OAAO,EAAE,CAACuK,gBAAD,CADV;AAECG,MAAAA,SAAS,EAAE,CAAC;AAAEF,QAAAA,OAAO,EAAE5E,gBAAX;AAA6BgF,QAAAA,QAAQ,EAAEV;AAAvC,OAAD;AAFZ,KAAD;AAF2G,GAAD,CAA5H;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMW,4BAAN,CAAmC;AAC/BxH,EAAAA,YAAY,CAACH,OAAD,EAAU4H,IAAV,EAAgB;AACxB,WAAO,CAAC,EAAE5H,OAAO,IAAIA,OAAO,CAAC0B,OAAnB,KAA+B1B,OAAO,CAAC6H,KAAR,IAAkBD,IAAI,IAAIA,IAAI,CAACE,SAA9D,CAAF,CAAR;AACH;;AAH8B;;AAKnCH,4BAA4B,CAACnL,IAA7B;AAAA,mBAAyHmL,4BAAzH;AAAA;;AACAA,4BAA4B,CAACZ,KAA7B,kBA9oBkGvO,EA8oBlG;AAAA,SAA6HmP,4BAA7H;AAAA,WAA6HA,4BAA7H;AAAA;;AACA;AAAA,qDA/oBkGnP,EA+oBlG,mBAA2FmP,4BAA3F,EAAqI,CAAC;AAC1H/K,IAAAA,IAAI,EAAE5D;AADoH,GAAD,CAArI;AAAA;AAGA;;;AACA,MAAM+O,iBAAN,CAAwB;AACpB5H,EAAAA,YAAY,CAACH,OAAD,EAAU4H,IAAV,EAAgB;AACxB,WAAO,CAAC,EAAE5H,OAAO,IAAIA,OAAO,CAAC0B,OAAnB,KAA+B1B,OAAO,CAACgI,OAAR,IAAoBJ,IAAI,IAAIA,IAAI,CAACE,SAAhE,CAAF,CAAR;AACH;;AAHmB;;AAKxBC,iBAAiB,CAACvL,IAAlB;AAAA,mBAA8GuL,iBAA9G;AAAA;;AACAA,iBAAiB,CAAChB,KAAlB,kBAzpBkGvO,EAypBlG;AAAA,SAAkHuP,iBAAlH;AAAA,WAAkHA,iBAAlH;AAAA,cAAiJ;AAAjJ;;AACA;AAAA,qDA1pBkGvP,EA0pBlG,mBAA2FuP,iBAA3F,EAA0H,CAAC;AAC/GnL,IAAAA,IAAI,EAAE5D,UADyG;AAE/G6D,IAAAA,IAAI,EAAE,CAAC;AAAEpB,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFyG,GAAD,CAA1H;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMwM,OAAN,CAAc;;AAEdA,OAAO,CAACzL,IAAR;AAAA,mBAAoGyL,OAApG;AAAA;;AACAA,OAAO,CAACC,IAAR,kBA9qBkG1P,EA8qBlG;AAAA,QAAwFyP,OAAxF;AAAA;AAAA;AAAA;;AACA;AAAA,qDA/qBkGzP,EA+qBlG,mBAA2FyP,OAA3F,EAAgH,CAAC;AACrGrL,IAAAA,IAAI,EAAE3D,SAD+F;AAErG4D,IAAAA,IAAI,EAAE,CAAC;AACCsL,MAAAA,QAAQ,EAAE,uBADX;AAECC,MAAAA,IAAI,EAAE;AAAE,iBAAS;AAAX;AAFP,KAAD;AAF+F,GAAD,CAAhH;AAAA;AAOA;AACA;AACA;AACA;;;AACA,SAASC,QAAT,CAAkBC,KAAlB,EAAyBC,OAAzB,EAAkCC,MAAM,GAAG,KAA3C,EAAkD;AAC9C;AACA;AACAF,EAAAA,KAAK,CAACG,OAAN,CAAcC,IAAd,CAAmBjO,SAAS,CAAC6N,KAAD,CAA5B,EAAqCK,SAArC,CAA+C,CAAC;AAAE9F,IAAAA;AAAF,GAAD,KAAgB;AAC3D+F,IAAAA,QAAQ,CAACL,OAAD,EAAW,GAAEC,MAAO,SAApB,EAA8B,KAA9B,CAAR;AACAI,IAAAA,QAAQ,CAACL,OAAD,EAAW,GAAEC,MAAO,SAApB,EAA8B,KAA9B,CAAR;AACAI,IAAAA,QAAQ,CAACL,OAAD,EAAW,GAAEC,MAAO,aAApB,EAAkC,KAAlC,CAAR;;AACA,QAAI3F,MAAM,KAAK,CAAX,IAAgBA,MAAM,KAAK,CAA/B,EAAkC;AAC9B+F,MAAAA,QAAQ,CAACL,OAAD,EAAW,GAAEC,MAAO,IAAG3F,MAAO,OAA9B,EAAsC,IAAtC,CAAR;AACH,KAFD,MAGK,IAAIA,MAAM,GAAG,CAAb,EAAgB;AACjB+F,MAAAA,QAAQ,CAACL,OAAD,EAAW,GAAEC,MAAO,aAApB,EAAkC,IAAlC,CAAR;AACH;AACJ,GAVD;AAWH;AACD;;;AACA,SAASI,QAAT,CAAkBL,OAAlB,EAA2BM,SAA3B,EAAsCC,KAAtC,EAA6C;AACzCP,EAAAA,OAAO,CAAC1J,aAAR,CAAsBlB,SAAtB,CAAgCoL,MAAhC,CAAuCF,SAAvC,EAAkDC,KAAlD;AACH;;AACD,MAAME,aAAN,CAAoB;;AAEpBA,aAAa,CAACxM,IAAd;AAAA,mBAA0GwM,aAA1G;AAAA;;AACAA,aAAa,CAACtM,IAAd,kBAhtBkGlE,EAgtBlG;AAAA,QAA2GwQ;AAA3G;AACAA,aAAa,CAACrM,IAAd,kBAjtBkGnE,EAitBlG;AAAA,YAAoI,CAACmD,eAAD,CAApI,EAAuJA,eAAvJ;AAAA;;AACA;AAAA,qDAltBkGnD,EAktBlG,mBAA2FwQ,aAA3F,EAAsH,CAAC;AAC3GpM,IAAAA,IAAI,EAAEjE,QADqG;AAE3GkE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,OAAO,EAAE,CAACnB,eAAD,CADV;AAECoB,MAAAA,OAAO,EAAE,CAACkL,OAAD,EAAUtM,eAAV,CAFV;AAGCsN,MAAAA,YAAY,EAAE,CAAChB,OAAD;AAHf,KAAD;AAFqG,GAAD,CAAtH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMiB,SAAN,CAAgB;AACZtN,EAAAA,WAAW,CAACuN,SAAD;AACX;AACAZ,EAAAA,OAFW;AAGX;AACAa,EAAAA,MAJW,EAIH;AACJ,SAAKD,SAAL,GAAiBA,SAAjB;AACA,SAAKZ,OAAL,GAAeA,OAAf;AACA,SAAKa,MAAL,GAAcA,MAAd;AACA;;AACA,SAAKC,KAAL,GAAa;AAAE;AAAf;AACH;AACD;;;AACAC,EAAAA,OAAO,GAAG;AACN,SAAKH,SAAL,CAAeI,aAAf,CAA6B,IAA7B;AACH;;AAfW,C,CAkBhB;;AACA;AACA;AACA;AACA;;;AACA,MAAMC,4BAA4B,GAAG;AACjCC,EAAAA,aAAa,EAAE,GADkB;AAEjCC,EAAAA,YAAY,EAAE;AAFmB,CAArC;AAIA;AACA;AACA;AACA;;AACA,MAAMC,wBAAwB,GAAG,GAAjC;AACA;;AACA,MAAMC,mBAAmB,GAAGzP,+BAA+B,CAAC;AAAE0P,EAAAA,OAAO,EAAE;AAAX,CAAD,CAA3D;AACA;;AACA,MAAMC,iBAAiB,GAAG,CAAC,WAAD,EAAc,YAAd,CAA1B;AACA;;AACA,MAAMC,eAAe,GAAG,CAAC,SAAD,EAAY,YAAZ,EAA0B,UAA1B,EAAsC,aAAtC,CAAxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,cAAN,CAAqB;AACjBpO,EAAAA,WAAW,CAACqO,OAAD,EAAUC,OAAV,EAAmBC,mBAAnB,EAAwCC,QAAxC,EAAkD;AACzD,SAAKH,OAAL,GAAeA,OAAf;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA;;AACA,SAAKG,cAAL,GAAsB,KAAtB;AACA;;AACA,SAAKC,cAAL,GAAsB,IAAIC,GAAJ,EAAtB;AACA;;AACA,SAAKC,0BAAL,GAAkC,KAAlC,CARyD,CASzD;;AACA,QAAIJ,QAAQ,CAACK,SAAb,EAAwB;AACpB,WAAKC,iBAAL,GAAyBpQ,aAAa,CAAC6P,mBAAD,CAAtC;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIQ,EAAAA,YAAY,CAACC,CAAD,EAAIC,CAAJ,EAAOzB,MAAM,GAAG,EAAhB,EAAoB;AAC5B,UAAM0B,aAAa,GAAI,KAAKC,cAAL,GACnB,KAAKA,cAAL,IAAuB,KAAKL,iBAAL,CAAuBM,qBAAvB,EAD3B;;AAEA,UAAMC,eAAe,GAAG,EAAE,GAAGzB,4BAAL;AAAmC,SAAGJ,MAAM,CAAC8B;AAA7C,KAAxB;;AACA,QAAI9B,MAAM,CAAC+B,QAAX,EAAqB;AACjBP,MAAAA,CAAC,GAAGE,aAAa,CAACM,IAAd,GAAqBN,aAAa,CAACO,KAAd,GAAsB,CAA/C;AACAR,MAAAA,CAAC,GAAGC,aAAa,CAACQ,GAAd,GAAoBR,aAAa,CAACS,MAAd,GAAuB,CAA/C;AACH;;AACD,UAAMC,MAAM,GAAGpC,MAAM,CAACoC,MAAP,IAAiBC,wBAAwB,CAACb,CAAD,EAAIC,CAAJ,EAAOC,aAAP,CAAxD;AACA,UAAMY,OAAO,GAAGd,CAAC,GAAGE,aAAa,CAACM,IAAlC;AACA,UAAMO,OAAO,GAAGd,CAAC,GAAGC,aAAa,CAACQ,GAAlC;AACA,UAAMM,QAAQ,GAAGX,eAAe,CAACxB,aAAjC;AACA,UAAMoC,MAAM,GAAGC,QAAQ,CAACpO,aAAT,CAAuB,KAAvB,CAAf;AACAmO,IAAAA,MAAM,CAAClO,SAAP,CAAiBC,GAAjB,CAAqB,oBAArB;AACAiO,IAAAA,MAAM,CAACnI,KAAP,CAAa0H,IAAb,GAAqB,GAAEM,OAAO,GAAGF,MAAO,IAAxC;AACAK,IAAAA,MAAM,CAACnI,KAAP,CAAa4H,GAAb,GAAoB,GAAEK,OAAO,GAAGH,MAAO,IAAvC;AACAK,IAAAA,MAAM,CAACnI,KAAP,CAAa6H,MAAb,GAAuB,GAAEC,MAAM,GAAG,CAAE,IAApC;AACAK,IAAAA,MAAM,CAACnI,KAAP,CAAa2H,KAAb,GAAsB,GAAEG,MAAM,GAAG,CAAE,IAAnC,CAjB4B,CAkB5B;AACA;;AACA,QAAIpC,MAAM,CAAC3K,KAAP,IAAgB,IAApB,EAA0B;AACtBoN,MAAAA,MAAM,CAACnI,KAAP,CAAaqI,eAAb,GAA+B3C,MAAM,CAAC3K,KAAtC;AACH;;AACDoN,IAAAA,MAAM,CAACnI,KAAP,CAAasI,kBAAb,GAAmC,GAAEJ,QAAS,IAA9C;;AACA,SAAKlB,iBAAL,CAAuB7M,WAAvB,CAAmCgO,MAAnC,EAxB4B,CAyB5B;AACA;;;AACAI,IAAAA,yBAAyB,CAACJ,MAAD,CAAzB;AACAA,IAAAA,MAAM,CAACnI,KAAP,CAAawI,SAAb,GAAyB,UAAzB,CA5B4B,CA6B5B;;AACA,UAAMC,SAAS,GAAG,IAAIjD,SAAJ,CAAc,IAAd,EAAoB2C,MAApB,EAA4BzC,MAA5B,CAAlB;AACA+C,IAAAA,SAAS,CAAC9C,KAAV,GAAkB;AAAE;AAApB,KA/B4B,CAgC5B;;AACA,SAAKiB,cAAL,CAAoB1M,GAApB,CAAwBuO,SAAxB;;AACA,QAAI,CAAC/C,MAAM,CAACgD,UAAZ,EAAwB;AACpB,WAAKC,0BAAL,GAAkCF,SAAlC;AACH,KApC2B,CAqC5B;AACA;;;AACA,SAAKG,sBAAL,CAA4B,MAAM;AAC9B,YAAMC,2BAA2B,GAAGJ,SAAS,KAAK,KAAKE,0BAAvD;AACAF,MAAAA,SAAS,CAAC9C,KAAV,GAAkB;AAAE;AAApB,OAF8B,CAG9B;AACA;AACA;AACA;;AACA,UAAI,CAACD,MAAM,CAACgD,UAAR,KAAuB,CAACG,2BAAD,IAAgC,CAAC,KAAKlC,cAA7D,CAAJ,EAAkF;AAC9E8B,QAAAA,SAAS,CAAC7C,OAAV;AACH;AACJ,KAVD,EAUGsC,QAVH;;AAWA,WAAOO,SAAP;AACH;AACD;;;AACA5C,EAAAA,aAAa,CAAC4C,SAAD,EAAY;AACrB,UAAMK,SAAS,GAAG,KAAKlC,cAAL,CAAoBmC,MAApB,CAA2BN,SAA3B,CAAlB;;AACA,QAAIA,SAAS,KAAK,KAAKE,0BAAvB,EAAmD;AAC/C,WAAKA,0BAAL,GAAkC,IAAlC;AACH,KAJoB,CAKrB;;;AACA,QAAI,CAAC,KAAK/B,cAAL,CAAoBoC,IAAzB,EAA+B;AAC3B,WAAK3B,cAAL,GAAsB,IAAtB;AACH,KARoB,CASrB;;;AACA,QAAI,CAACyB,SAAL,EAAgB;AACZ;AACH;;AACD,UAAMG,QAAQ,GAAGR,SAAS,CAAC5D,OAA3B;AACA,UAAM0C,eAAe,GAAG,EAAE,GAAGzB,4BAAL;AAAmC,SAAG2C,SAAS,CAAC/C,MAAV,CAAiB8B;AAAvD,KAAxB;AACAyB,IAAAA,QAAQ,CAACjJ,KAAT,CAAesI,kBAAf,GAAqC,GAAEf,eAAe,CAACvB,YAAa,IAApE;AACAiD,IAAAA,QAAQ,CAACjJ,KAAT,CAAekJ,OAAf,GAAyB,GAAzB;AACAT,IAAAA,SAAS,CAAC9C,KAAV,GAAkB;AAAE;AAApB,KAjBqB,CAkBrB;;AACA,SAAKiD,sBAAL,CAA4B,MAAM;AAC9BH,MAAAA,SAAS,CAAC9C,KAAV,GAAkB;AAAE;AAApB;AACAsD,MAAAA,QAAQ,CAAC3O,MAAT;AACH,KAHD,EAGGiN,eAAe,CAACvB,YAHnB;AAIH;AACD;;;AACAmD,EAAAA,UAAU,GAAG;AACT,SAAKvC,cAAL,CAAoBxJ,OAApB,CAA4B+K,MAAM,IAAIA,MAAM,CAACvC,OAAP,EAAtC;AACH;AACD;;;AACAwD,EAAAA,uBAAuB,GAAG;AACtB,SAAKxC,cAAL,CAAoBxJ,OAApB,CAA4B+K,MAAM,IAAI;AAClC,UAAI,CAACA,MAAM,CAACzC,MAAP,CAAcgD,UAAnB,EAA+B;AAC3BP,QAAAA,MAAM,CAACvC,OAAP;AACH;AACJ,KAJD;AAKH;AACD;;;AACAyD,EAAAA,kBAAkB,CAAC5C,mBAAD,EAAsB;AACpC,UAAM5B,OAAO,GAAGjO,aAAa,CAAC6P,mBAAD,CAA7B;;AACA,QAAI,CAAC5B,OAAD,IAAYA,OAAO,KAAK,KAAKyE,eAAjC,EAAkD;AAC9C;AACH,KAJmC,CAKpC;;;AACA,SAAKC,oBAAL;;AACA,SAAKD,eAAL,GAAuBzE,OAAvB;;AACA,SAAK2E,eAAL,CAAqBpD,iBAArB;AACH;AACD;AACJ;AACA;AACA;;;AACIqD,EAAAA,WAAW,CAACC,KAAD,EAAQ;AACf,QAAIA,KAAK,CAACxQ,IAAN,KAAe,WAAnB,EAAgC;AAC5B,WAAKyQ,YAAL,CAAkBD,KAAlB;AACH,KAFD,MAGK,IAAIA,KAAK,CAACxQ,IAAN,KAAe,YAAnB,EAAiC;AAClC,WAAK0Q,aAAL,CAAmBF,KAAnB;AACH,KAFI,MAGA;AACD,WAAKG,YAAL;AACH,KATc,CAUf;AACA;AACA;;;AACA,QAAI,CAAC,KAAK/C,0BAAV,EAAsC;AAClC,WAAK0C,eAAL,CAAqBnD,eAArB;;AACA,WAAKS,0BAAL,GAAkC,IAAlC;AACH;AACJ;AACD;;;AACA6C,EAAAA,YAAY,CAACD,KAAD,EAAQ;AAChB;AACA;AACA,UAAMI,eAAe,GAAG/T,+BAA+B,CAAC2T,KAAD,CAAvD;AACA,UAAMK,gBAAgB,GAAG,KAAKC,oBAAL,IACrBzJ,IAAI,CAAC0J,GAAL,KAAa,KAAKD,oBAAL,GAA4B/D,wBAD7C;;AAEA,QAAI,CAAC,KAAKM,OAAL,CAAa2D,cAAd,IAAgC,CAACJ,eAAjC,IAAoD,CAACC,gBAAzD,EAA2E;AACvE,WAAKpD,cAAL,GAAsB,IAAtB;AACA,WAAKM,YAAL,CAAkByC,KAAK,CAACS,OAAxB,EAAiCT,KAAK,CAACU,OAAvC,EAAgD,KAAK7D,OAAL,CAAa8D,YAA7D;AACH;AACJ;AACD;;;AACAT,EAAAA,aAAa,CAACF,KAAD,EAAQ;AACjB,QAAI,CAAC,KAAKnD,OAAL,CAAa2D,cAAd,IAAgC,CAAClU,gCAAgC,CAAC0T,KAAD,CAArE,EAA8E;AAC1E;AACA;AACA;AACA,WAAKM,oBAAL,GAA4BzJ,IAAI,CAAC0J,GAAL,EAA5B;AACA,WAAKtD,cAAL,GAAsB,IAAtB,CAL0E,CAM1E;AACA;;AACA,YAAM2D,OAAO,GAAGZ,KAAK,CAACa,cAAtB;;AACA,WAAK,IAAIhL,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+K,OAAO,CAACnL,MAA5B,EAAoCI,CAAC,EAArC,EAAyC;AACrC,aAAK0H,YAAL,CAAkBqD,OAAO,CAAC/K,CAAD,CAAP,CAAW4K,OAA7B,EAAsCG,OAAO,CAAC/K,CAAD,CAAP,CAAW6K,OAAjD,EAA0D,KAAK7D,OAAL,CAAa8D,YAAvE;AACH;AACJ;AACJ;AACD;;;AACAR,EAAAA,YAAY,GAAG;AACX,QAAI,CAAC,KAAKlD,cAAV,EAA0B;AACtB;AACH;;AACD,SAAKA,cAAL,GAAsB,KAAtB,CAJW,CAKX;;AACA,SAAKC,cAAL,CAAoBxJ,OAApB,CAA4B+K,MAAM,IAAI;AAClC;AACA;AACA,YAAMqC,SAAS,GAAGrC,MAAM,CAACxC,KAAP,KAAiB;AAAE;AAAnB,SACbwC,MAAM,CAACzC,MAAP,CAAc+E,oBAAd,IAAsCtC,MAAM,CAACxC,KAAP,KAAiB;AAAE;AAD9D;;AAEA,UAAI,CAACwC,MAAM,CAACzC,MAAP,CAAcgD,UAAf,IAA6B8B,SAAjC,EAA4C;AACxCrC,QAAAA,MAAM,CAACvC,OAAP;AACH;AACJ,KARD;AASH;AACD;;;AACAgD,EAAAA,sBAAsB,CAAC8B,EAAD,EAAKC,KAAK,GAAG,CAAb,EAAgB;AAClC,SAAKnE,OAAL,CAAaoE,iBAAb,CAA+B,MAAMC,UAAU,CAACH,EAAD,EAAKC,KAAL,CAA/C;AACH;AACD;;;AACAnB,EAAAA,eAAe,CAACsB,UAAD,EAAa;AACxB,SAAKtE,OAAL,CAAaoE,iBAAb,CAA+B,MAAM;AACjCE,MAAAA,UAAU,CAAC1N,OAAX,CAAmBlE,IAAI,IAAI;AACvB,aAAKoQ,eAAL,CAAqByB,gBAArB,CAAsC7R,IAAtC,EAA4C,IAA5C,EAAkDgN,mBAAlD;AACH,OAFD;AAGH,KAJD;AAKH;AACD;;;AACAqD,EAAAA,oBAAoB,GAAG;AACnB,QAAI,KAAKD,eAAT,EAA0B;AACtBlD,MAAAA,iBAAiB,CAAChJ,OAAlB,CAA0BlE,IAAI,IAAI;AAC9B,aAAKoQ,eAAL,CAAqB0B,mBAArB,CAAyC9R,IAAzC,EAA+C,IAA/C,EAAqDgN,mBAArD;AACH,OAFD;;AAGA,UAAI,KAAKY,0BAAT,EAAqC;AACjCT,QAAAA,eAAe,CAACjJ,OAAhB,CAAwBlE,IAAI,IAAI;AAC5B,eAAKoQ,eAAL,CAAqB0B,mBAArB,CAAyC9R,IAAzC,EAA+C,IAA/C,EAAqDgN,mBAArD;AACH,SAFD;AAGH;AACJ;AACJ;;AApNgB;AAsNrB;;;AACA,SAASqC,yBAAT,CAAmC1D,OAAnC,EAA4C;AACxC;AACA;AACA;AACAoG,EAAAA,MAAM,CAACnR,gBAAP,CAAwB+K,OAAxB,EAAiCqG,gBAAjC,CAAkD,SAAlD;AACH;AACD;AACA;AACA;;;AACA,SAASnD,wBAAT,CAAkCb,CAAlC,EAAqCC,CAArC,EAAwCgE,IAAxC,EAA8C;AAC1C,QAAMC,KAAK,GAAGC,IAAI,CAACtM,GAAL,CAASsM,IAAI,CAACC,GAAL,CAASpE,CAAC,GAAGiE,IAAI,CAACzD,IAAlB,CAAT,EAAkC2D,IAAI,CAACC,GAAL,CAASpE,CAAC,GAAGiE,IAAI,CAACI,KAAlB,CAAlC,CAAd;AACA,QAAMC,KAAK,GAAGH,IAAI,CAACtM,GAAL,CAASsM,IAAI,CAACC,GAAL,CAASnE,CAAC,GAAGgE,IAAI,CAACvD,GAAlB,CAAT,EAAiCyD,IAAI,CAACC,GAAL,CAASnE,CAAC,GAAGgE,IAAI,CAACM,MAAlB,CAAjC,CAAd;AACA,SAAOJ,IAAI,CAACK,IAAL,CAAUN,KAAK,GAAGA,KAAR,GAAgBI,KAAK,GAAGA,KAAlC,CAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMG,yBAAyB,GAAG,IAAI3W,cAAJ,CAAmB,2BAAnB,CAAlC;;AACA,MAAM4W,SAAN,CAAgB;AACZ1T,EAAAA,WAAW,CAACgD,WAAD,EAAc2Q,MAAd,EAAsBnF,QAAtB,EAAgCoF,aAAhC,EAA+CC,cAA/C,EAA+D;AACtE,SAAK7Q,WAAL,GAAmBA,WAAnB;AACA,SAAK6Q,cAAL,GAAsBA,cAAtB;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAKjE,MAAL,GAAc,CAAd;AACA,SAAKpN,SAAL,GAAiB,KAAjB;AACA;;AACA,SAAKkC,cAAL,GAAsB,KAAtB;AACA,SAAKoP,cAAL,GAAsBF,aAAa,IAAI,EAAvC;AACA,SAAKG,eAAL,GAAuB,IAAI3F,cAAJ,CAAmB,IAAnB,EAAyBuF,MAAzB,EAAiC3Q,WAAjC,EAA8CwL,QAA9C,CAAvB;AACH;AACD;AACJ;AACA;AACA;;;AACgB,MAAR/L,QAAQ,GAAG;AACX,WAAO,KAAKD,SAAZ;AACH;;AACW,MAARC,QAAQ,CAACC,KAAD,EAAQ;AAChB,QAAIA,KAAJ,EAAW;AACP,WAAKwO,uBAAL;AACH;;AACD,SAAK1O,SAAL,GAAiBE,KAAjB;;AACA,SAAKsR,4BAAL;AACH;AACD;AACJ;AACA;AACA;;;AACe,MAAPC,OAAO,GAAG;AACV,WAAO,KAAKC,QAAL,IAAiB,KAAKlR,WAAL,CAAiBC,aAAzC;AACH;;AACU,MAAPgR,OAAO,CAACA,OAAD,EAAU;AACjB,SAAKC,QAAL,GAAgBD,OAAhB;;AACA,SAAKD,4BAAL;AACH;;AACDG,EAAAA,QAAQ,GAAG;AACP,SAAKzP,cAAL,GAAsB,IAAtB;;AACA,SAAKsP,4BAAL;AACH;;AACDI,EAAAA,WAAW,GAAG;AACV,SAAKL,eAAL,CAAqB1C,oBAArB;AACH;AACD;;;AACAJ,EAAAA,UAAU,GAAG;AACT,SAAK8C,eAAL,CAAqB9C,UAArB;AACH;AACD;;;AACAC,EAAAA,uBAAuB,GAAG;AACtB,SAAK6C,eAAL,CAAqB7C,uBAArB;AACH;AACD;AACJ;AACA;AACA;;;AACoB,MAAZiB,YAAY,GAAG;AACf,WAAO;AACH5C,MAAAA,QAAQ,EAAE,KAAKA,QADZ;AAEHK,MAAAA,MAAM,EAAE,KAAKA,MAFV;AAGH/M,MAAAA,KAAK,EAAE,KAAKA,KAHT;AAIHyM,MAAAA,SAAS,EAAE,EACP,GAAG,KAAKwE,cAAL,CAAoBxE,SADhB;AAEP,YAAI,KAAKuE,cAAL,KAAwB,gBAAxB,GAA2C;AAAEhG,UAAAA,aAAa,EAAE,CAAjB;AAAoBC,UAAAA,YAAY,EAAE;AAAlC,SAA3C,GAAmF,EAAvF,CAFO;AAGP,WAAG,KAAKwB;AAHD,OAJR;AASHiD,MAAAA,oBAAoB,EAAE,KAAKuB,cAAL,CAAoBvB;AATvC,KAAP;AAWH;AACD;AACJ;AACA;AACA;;;AACsB,MAAdP,cAAc,GAAG;AACjB,WAAO,KAAKvP,QAAL,IAAiB,CAAC,CAAC,KAAKqR,cAAL,CAAoBrR,QAA9C;AACH;AACD;;;AACAuR,EAAAA,4BAA4B,GAAG;AAC3B,QAAI,CAAC,KAAKvR,QAAN,IAAkB,KAAKiC,cAA3B,EAA2C;AACvC,WAAKqP,eAAL,CAAqB5C,kBAArB,CAAwC,KAAK8C,OAA7C;AACH;AACJ;AACD;;;AACAI,EAAAA,MAAM,CAACC,SAAD,EAAYrF,CAAC,GAAG,CAAhB,EAAmBzB,MAAnB,EAA2B;AAC7B,QAAI,OAAO8G,SAAP,KAAqB,QAAzB,EAAmC;AAC/B,aAAO,KAAKP,eAAL,CAAqBhF,YAArB,CAAkCuF,SAAlC,EAA6CrF,CAA7C,EAAgD,EAAE,GAAG,KAAKkD,YAAV;AAAwB,WAAG3E;AAA3B,OAAhD,CAAP;AACH,KAFD,MAGK;AACD,aAAO,KAAKuG,eAAL,CAAqBhF,YAArB,CAAkC,CAAlC,EAAqC,CAArC,EAAwC,EAAE,GAAG,KAAKoD,YAAV;AAAwB,WAAGmC;AAA3B,OAAxC,CAAP;AACH;AACJ;;AA9FW;;AAgGhBZ,SAAS,CAAC9S,IAAV;AAAA,mBAAsG8S,SAAtG,EAjmCkG9W,EAimClG,mBAAiIA,EAAE,CAAC2X,UAApI,GAjmCkG3X,EAimClG,mBAA2JA,EAAE,CAAC4X,MAA9J,GAjmCkG5X,EAimClG,mBAAiLyB,IAAI,CAAC6M,QAAtL,GAjmCkGtO,EAimClG,mBAA2M6W,yBAA3M,MAjmCkG7W,EAimClG,mBAAiQkC,qBAAjQ;AAAA;;AACA4U,SAAS,CAACpH,IAAV,kBAlmCkG1P,EAkmClG;AAAA,QAA0F8W,SAA1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAlmCkG9W,MAAAA,EAkmClG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAnmCkGA,EAmmClG,mBAA2F8W,SAA3F,EAAkH,CAAC;AACvG1S,IAAAA,IAAI,EAAE3D,SADiG;AAEvG4D,IAAAA,IAAI,EAAE,CAAC;AACCsL,MAAAA,QAAQ,EAAE,2BADX;AAECkI,MAAAA,QAAQ,EAAE,WAFX;AAGCjI,MAAAA,IAAI,EAAE;AACF,iBAAS,YADP;AAEF,wCAAgC;AAF9B;AAHP,KAAD;AAFiG,GAAD,CAAlH,EAU4B,YAAY;AAAE,WAAO,CAAC;AAAExL,MAAAA,IAAI,EAAEpE,EAAE,CAAC2X;AAAX,KAAD,EAA0B;AAAEvT,MAAAA,IAAI,EAAEpE,EAAE,CAAC4X;AAAX,KAA1B,EAA+C;AAAExT,MAAAA,IAAI,EAAE3C,IAAI,CAAC6M;AAAb,KAA/C,EAAwE;AAAElK,MAAAA,IAAI,EAAEI,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACrIL,QAAAA,IAAI,EAAEhE;AAD+H,OAAD,EAErI;AACCgE,QAAAA,IAAI,EAAE/D,MADP;AAECgE,QAAAA,IAAI,EAAE,CAACwS,yBAAD;AAFP,OAFqI;AAA/B,KAAxE,EAK3B;AAAEzS,MAAAA,IAAI,EAAEI,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAClCL,QAAAA,IAAI,EAAEhE;AAD4B,OAAD,EAElC;AACCgE,QAAAA,IAAI,EAAE/D,MADP;AAECgE,QAAAA,IAAI,EAAE,CAACnC,qBAAD;AAFP,OAFkC;AAA/B,KAL2B,CAAP;AAUlB,GApBxB,EAoB0C;AAAE+D,IAAAA,KAAK,EAAE,CAAC;AACpC7B,MAAAA,IAAI,EAAE1D,KAD8B;AAEpC2D,MAAAA,IAAI,EAAE,CAAC,gBAAD;AAF8B,KAAD,CAAT;AAG1ByT,IAAAA,SAAS,EAAE,CAAC;AACZ1T,MAAAA,IAAI,EAAE1D,KADM;AAEZ2D,MAAAA,IAAI,EAAE,CAAC,oBAAD;AAFM,KAAD,CAHe;AAM1BsO,IAAAA,QAAQ,EAAE,CAAC;AACXvO,MAAAA,IAAI,EAAE1D,KADK;AAEX2D,MAAAA,IAAI,EAAE,CAAC,mBAAD;AAFK,KAAD,CANgB;AAS1B2O,IAAAA,MAAM,EAAE,CAAC;AACT5O,MAAAA,IAAI,EAAE1D,KADG;AAET2D,MAAAA,IAAI,EAAE,CAAC,iBAAD;AAFG,KAAD,CATkB;AAY1BqO,IAAAA,SAAS,EAAE,CAAC;AACZtO,MAAAA,IAAI,EAAE1D,KADM;AAEZ2D,MAAAA,IAAI,EAAE,CAAC,oBAAD;AAFM,KAAD,CAZe;AAe1BwB,IAAAA,QAAQ,EAAE,CAAC;AACXzB,MAAAA,IAAI,EAAE1D,KADK;AAEX2D,MAAAA,IAAI,EAAE,CAAC,mBAAD;AAFK,KAAD,CAfgB;AAkB1BgT,IAAAA,OAAO,EAAE,CAAC;AACVjT,MAAAA,IAAI,EAAE1D,KADI;AAEV2D,MAAAA,IAAI,EAAE,CAAC,kBAAD;AAFI,KAAD;AAlBiB,GApB1C;AAAA;AA2CA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0T,eAAN,CAAsB;;AAEtBA,eAAe,CAAC/T,IAAhB;AAAA,mBAA4G+T,eAA5G;AAAA;;AACAA,eAAe,CAAC7T,IAAhB,kBAxpCkGlE,EAwpClG;AAAA,QAA6G+X;AAA7G;AACAA,eAAe,CAAC5T,IAAhB,kBAzpCkGnE,EAypClG;AAAA,YAAwI,CAACmD,eAAD,CAAxI,EAA2JA,eAA3J;AAAA;;AACA;AAAA,qDA1pCkGnD,EA0pClG,mBAA2F+X,eAA3F,EAAwH,CAAC;AAC7G3T,IAAAA,IAAI,EAAEjE,QADuG;AAE7GkE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,OAAO,EAAE,CAACnB,eAAD,CADV;AAECoB,MAAAA,OAAO,EAAE,CAACuS,SAAD,EAAY3T,eAAZ,CAFV;AAGCsN,MAAAA,YAAY,EAAE,CAACqG,SAAD;AAHf,KAAD;AAFuG,GAAD,CAAxH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMkB,iBAAN,CAAwB;AACpB5U,EAAAA,WAAW,CAAC6T,cAAD,EAAiB;AACxB,SAAKA,cAAL,GAAsBA,cAAtB;AACA;;AACA,SAAKpG,KAAL,GAAa,WAAb;AACA;;AACA,SAAKhL,QAAL,GAAgB,KAAhB;AACH;;AAPmB;;AASxBmS,iBAAiB,CAAChU,IAAlB;AAAA,mBAA8GgU,iBAA9G,EAhsCkGhY,EAgsClG,mBAAiJkC,qBAAjJ;AAAA;;AACA8V,iBAAiB,CAACC,IAAlB,kBAjsCkGjY,EAisClG;AAAA,QAAkGgY,iBAAlG;AAAA;AAAA;AAAA;AAAA;AAAA;AAjsCkGhY,MAAAA,EAisClG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAlsCkGA,EAksClG,mBAA2FgY,iBAA3F,EAA0H,CAAC;AAC/G5T,IAAAA,IAAI,EAAEzD,SADyG;AAE/G0D,IAAAA,IAAI,EAAE,CAAC;AAAE6T,MAAAA,aAAa,EAAEtX,iBAAiB,CAACuX,IAAnC;AAAyCC,MAAAA,eAAe,EAAEvX,uBAAuB,CAACwX,MAAlF;AAA0F1I,MAAAA,QAAQ,EAAE,qBAApG;AAA2H2I,MAAAA,QAAQ,EAAE,EAArI;AAAyI1I,MAAAA,IAAI,EAAE;AAC1I,iBAAS,qBADiI;AAE1I,qDAA6C,2BAF6F;AAG1I,+CAAuC,qBAHmG;AAI1I,gDAAwC,UAJkG;AAK1I,2CAAmC;AALuG,OAA/I;AAMI2I,MAAAA,MAAM,EAAE,CAAC,whCAAD;AANZ,KAAD;AAFyG,GAAD,CAA1H,EAS4B,YAAY;AAAE,WAAO,CAAC;AAAEnU,MAAAA,IAAI,EAAEI,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DL,QAAAA,IAAI,EAAEhE;AADwD,OAAD,EAE9D;AACCgE,QAAAA,IAAI,EAAE/D,MADP;AAECgE,QAAAA,IAAI,EAAE,CAACnC,qBAAD;AAFP,OAF8D;AAA/B,KAAD,CAAP;AAKlB,GAdxB,EAc0C;AAAE2O,IAAAA,KAAK,EAAE,CAAC;AACpCzM,MAAAA,IAAI,EAAE1D;AAD8B,KAAD,CAAT;AAE1BmF,IAAAA,QAAQ,EAAE,CAAC;AACXzB,MAAAA,IAAI,EAAE1D;AADK,KAAD;AAFgB,GAd1C;AAAA;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM8X,uBAAN,CAA8B;;AAE9BA,uBAAuB,CAACxU,IAAxB;AAAA,mBAAoHwU,uBAApH;AAAA;;AACAA,uBAAuB,CAACtU,IAAxB,kBAhuCkGlE,EAguClG;AAAA,QAAqHwY;AAArH;AACAA,uBAAuB,CAACrU,IAAxB,kBAjuCkGnE,EAiuClG;AAAA,YAAwJ,CAACmD,eAAD,CAAxJ;AAAA;;AACA;AAAA,qDAluCkGnD,EAkuClG,mBAA2FwY,uBAA3F,EAAgI,CAAC;AACrHpU,IAAAA,IAAI,EAAEjE,QAD+G;AAErHkE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,OAAO,EAAE,CAACnB,eAAD,CADV;AAECoB,MAAAA,OAAO,EAAE,CAACyT,iBAAD,CAFV;AAGCvH,MAAAA,YAAY,EAAE,CAACuH,iBAAD;AAHf,KAAD;AAF+G,GAAD,CAAhI;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMS,2BAA2B,GAAG,IAAIvY,cAAJ,CAAmB,6BAAnB,CAApC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMwY,qBAAqB,GAAGhT,aAAa,CAAC,MAAM,EAAP,CAA3C,C,CAEA;;;AACA,IAAIiT,wBAAwB,GAAG,CAA/B;;AACA,MAAMC,gBAAN,SAA+BF,qBAA/B,CAAqD;AACjDtV,EAAAA,WAAW,CAAC8D,MAAD,EAAS;AAChB;AACA;;AACA,SAAK2R,QAAL,GAAiB,sBAAqBF,wBAAwB,EAAG,EAAjE;AACA,SAAKG,MAAL,GAAc5R,MAAM,EAAE6R,WAAR,IAAuB,KAArC;AACH;;AANgD;;AAQrDH,gBAAgB,CAAC5U,IAAjB;AAAA,mBAA6G4U,gBAA7G,EAvyCkG5Y,EAuyClG,mBAA+IyY,2BAA/I;AAAA;;AACAG,gBAAgB,CAAClJ,IAAjB,kBAxyCkG1P,EAwyClG;AAAA,QAAiG4Y,gBAAjG;AAAA;AAAA;AAAA;AAAA,aAxyCkG5Y,EAwyClG;AAAA;;AACA;AAAA,qDAzyCkGA,EAyyClG,mBAA2F4Y,gBAA3F,EAAyH,CAAC;AAC9GxU,IAAAA,IAAI,EAAE3D;AADwG,GAAD,CAAzH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE2D,MAAAA,IAAI,EAAEI,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DL,QAAAA,IAAI,EAAE/D,MADwD;AAE9DgE,QAAAA,IAAI,EAAE,CAACoU,2BAAD;AAFwD,OAAD,EAG9D;AACCrU,QAAAA,IAAI,EAAEhE;AADP,OAH8D;AAA/B,KAAD,CAAP;AAKlB,GAPxB,EAO0C;AAAE4Y,IAAAA,KAAK,EAAE,CAAC;AACpC5U,MAAAA,IAAI,EAAE1D;AAD8B,KAAD;AAAT,GAP1C;AAAA;AAUA;AACA;AACA;AACA;AACA;;;AACA,MAAMuY,YAAY,GAAG,IAAI/Y,cAAJ,CAAmB,aAAnB,CAArB;AACA;AACA;AACA;;AACA,MAAMgZ,WAAN,SAA0BN,gBAA1B,CAA2C;;AAE3CM,WAAW,CAAClV,IAAZ;AAAA;AAAA;AAAA,oEA9zCkGhE,EA8zClG,uBAAwGkZ,WAAxG,SAAwGA,WAAxG;AAAA;AAAA;;AACAA,WAAW,CAACjB,IAAZ,kBA/zCkGjY,EA+zClG;AAAA,QAA4FkZ,WAA5F;AAAA;AAAA;AAAA;AAAA;AAAA;AA/zCkGlZ,MAAAA,EA+zClG;AA/zCkGA,MAAAA,EA+zClG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA/zCkGA,EA+zClG,oBAAgb,CAAC;AAAE8O,IAAAA,OAAO,EAAEmK,YAAX;AAAyBE,IAAAA,WAAW,EAAED;AAAtC,GAAD,CAAhb,GA/zCkGlZ,EA+zClG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA/zCkGA,MAAAA,EA+zClG;AA/zCkGA,MAAAA,EA+zCgd,6BAAljB;AA/zCkGA,MAAAA,EA+zC0hB,UAA5nB;AA/zCkGA,MAAAA,EA+zCsiB,gBAAxoB;AA/zCkGA,MAAAA,EA+zC+jB,eAAjqB;AA/zCkGA,MAAAA,EA+zCwkB,mBAA1qB;AAAA;;AAAA;AA/zCkGA,MAAAA,EA+zCwgB,+BAA1mB;AA/zCkGA,MAAAA,EA+zC0hB,aAA5nB;AA/zCkGA,MAAAA,EA+zC0hB,uCAA5nB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAh0CkGA,EAg0ClG,mBAA2FkZ,WAA3F,EAAoH,CAAC;AACzG9U,IAAAA,IAAI,EAAEzD,SADmG;AAEzG0D,IAAAA,IAAI,EAAE,CAAC;AAAEsL,MAAAA,QAAQ,EAAE,cAAZ;AAA4BkI,MAAAA,QAAQ,EAAE,aAAtC;AAAqDK,MAAAA,aAAa,EAAEtX,iBAAiB,CAACuX,IAAtF;AAA4FC,MAAAA,eAAe,EAAEvX,uBAAuB,CAACwX,MAArI;AAA6Ie,MAAAA,MAAM,EAAE,CAAC,UAAD,CAArJ;AAAmKxJ,MAAAA,IAAI,EAAE;AACpK,iBAAS,cAD2J;AAEpK,uBAAe,yBAFqJ;AAGpK,gCAAwB,qCAH4I;AAIpK,kCAA0B,0BAJ0I;AAKpK,yCAAiC;AALmI,OAAzK;AAMIZ,MAAAA,SAAS,EAAE,CAAC;AAAEF,QAAAA,OAAO,EAAEmK,YAAX;AAAyBE,QAAAA,WAAW,EAAED;AAAtC,OAAD,CANf;AAMsEZ,MAAAA,QAAQ,EAAE,yLANhF;AAM2QC,MAAAA,MAAM,EAAE,CAAC,whBAAD;AANnR,KAAD;AAFmG,GAAD,CAApH;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,IAAIc,gBAAgB,GAAG,CAAvB;AACA;;AACA,MAAMC,wBAAN,CAA+B;AAC3BlW,EAAAA,WAAW;AACX;AACAmW,EAAAA,MAFW;AAGX;AACAC,EAAAA,WAAW,GAAG,KAJH,EAIU;AACjB,SAAKD,MAAL,GAAcA,MAAd;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACH;;AAR0B;;AAU/B,MAAMC,cAAN,CAAqB;AACjBrW,EAAAA,WAAW,CAACsW,QAAD,EAAWC,kBAAX,EAA+BC,OAA/B,EAAwCC,KAAxC,EAA+C;AACtD,SAAKH,QAAL,GAAgBA,QAAhB;AACA,SAAKC,kBAAL,GAA0BA,kBAA1B;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,OAAL,GAAe,KAAf;AACA,SAAKnU,SAAL,GAAiB,KAAjB;AACA,SAAKoU,oBAAL,GAA4B,EAA5B;AACA;;AACA,SAAKC,EAAL,GAAW,cAAaZ,gBAAgB,EAAG,EAA3C;AACA;AACA;;AACA,SAAKa,iBAAL,GAAyB,IAAIpZ,YAAJ,EAAzB;AACA;;AACA,SAAKqZ,aAAL,GAAqB,IAAIpY,OAAJ,EAArB;AACH;AACD;;;AACY,MAARqY,QAAQ,GAAG;AACX,WAAO,KAAKR,OAAL,IAAgB,KAAKA,OAAL,CAAaQ,QAApC;AACH;AACD;;;AACY,MAARC,QAAQ,GAAG;AACX,WAAO,KAAKP,SAAZ;AACH;AACD;;;AACY,MAARjU,QAAQ,GAAG;AACX,WAAQ,KAAKgU,KAAL,IAAc,KAAKA,KAAL,CAAWhU,QAA1B,IAAuC,KAAKD,SAAnD;AACH;;AACW,MAARC,QAAQ,CAACC,KAAD,EAAQ;AAChB,SAAKF,SAAL,GAAiBhE,qBAAqB,CAACkE,KAAD,CAAtC;AACH;AACD;;;AACiB,MAAbU,aAAa,GAAG;AAChB,WAAO,CAAC,EAAE,KAAKoT,OAAL,IAAgB,KAAKA,OAAL,CAAapT,aAA/B,CAAR;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACc,MAAN8T,MAAM,GAAG;AACT,WAAO,KAAKP,OAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACiB,MAATQ,SAAS,GAAG;AACZ;AACA,WAAO,CAAC,KAAKC,eAAL,GAAuBC,WAAvB,IAAsC,EAAvC,EAA2CC,IAA3C,EAAP;AACH;AACD;;;AACAC,EAAAA,MAAM,GAAG;AACL,QAAI,CAAC,KAAKb,SAAV,EAAqB;AACjB,WAAKA,SAAL,GAAiB,IAAjB;;AACA,WAAKH,kBAAL,CAAwBiB,YAAxB;;AACA,WAAKC,yBAAL;AACH;AACJ;AACD;;;AACAC,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKhB,SAAT,EAAoB;AAChB,WAAKA,SAAL,GAAiB,KAAjB;;AACA,WAAKH,kBAAL,CAAwBiB,YAAxB;;AACA,WAAKC,yBAAL;AACH;AACJ;AACD;;;AACAE,EAAAA,KAAK,CAACC,OAAD,EAAUC,OAAV,EAAmB;AACpB;AACA;AACA,UAAMlL,OAAO,GAAG,KAAKyK,eAAL,EAAhB;;AACA,QAAI,OAAOzK,OAAO,CAACgL,KAAf,KAAyB,UAA7B,EAAyC;AACrChL,MAAAA,OAAO,CAACgL,KAAR,CAAcE,OAAd;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,eAAe,GAAG;AACd,QAAI,CAAC,KAAKnB,OAAV,EAAmB;AACf,WAAKA,OAAL,GAAe,IAAf;;AACA,WAAKJ,kBAAL,CAAwBiB,YAAxB;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACIO,EAAAA,iBAAiB,GAAG;AAChB,QAAI,KAAKpB,OAAT,EAAkB;AACd,WAAKA,OAAL,GAAe,KAAf;;AACA,WAAKJ,kBAAL,CAAwBiB,YAAxB;AACH;AACJ;AACD;;;AACAQ,EAAAA,QAAQ,GAAG;AACP,WAAO,KAAKb,SAAZ;AACH;AACD;;;AACAc,EAAAA,cAAc,CAACzG,KAAD,EAAQ;AAClB,QAAI,CAACA,KAAK,CAAC0G,OAAN,KAAkBnZ,KAAlB,IAA2ByS,KAAK,CAAC0G,OAAN,KAAkBlZ,KAA9C,KAAwD,CAACC,cAAc,CAACuS,KAAD,CAA3E,EAAoF;AAChF,WAAK2G,qBAAL,GADgF,CAEhF;;;AACA3G,MAAAA,KAAK,CAAC4G,cAAN;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACID,EAAAA,qBAAqB,GAAG;AACpB,QAAI,CAAC,KAAK1V,QAAV,EAAoB;AAChB,WAAKiU,SAAL,GAAiB,KAAKM,QAAL,GAAgB,CAAC,KAAKN,SAAtB,GAAkC,IAAnD;;AACA,WAAKH,kBAAL,CAAwBiB,YAAxB;;AACA,WAAKC,yBAAL,CAA+B,IAA/B;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIY,EAAAA,gBAAgB,GAAG;AACf,WAAO,KAAKpB,QAAL,KAAkB,KAAKD,QAAL,GAAgB,KAAhB,GAAwB,IAA1C,CAAP;AACH;AACD;;;AACAsB,EAAAA,YAAY,GAAG;AACX,WAAO,KAAK7V,QAAL,GAAgB,IAAhB,GAAuB,GAA9B;AACH;AACD;;;AACA2U,EAAAA,eAAe,GAAG;AACd,WAAO,KAAKd,QAAL,CAAcrT,aAArB;AACH;;AACDsV,EAAAA,kBAAkB,GAAG;AACjB;AACA;AACA;AACA;AACA;AACA,QAAI,KAAK7B,SAAT,EAAoB;AAChB,YAAMS,SAAS,GAAG,KAAKA,SAAvB;;AACA,UAAIA,SAAS,KAAK,KAAKP,oBAAvB,EAA6C;AACzC,aAAKA,oBAAL,GAA4BO,SAA5B;;AACA,aAAKJ,aAAL,CAAmBvS,IAAnB;AACH;AACJ;AACJ;;AACD4P,EAAAA,WAAW,GAAG;AACV,SAAK2C,aAAL,CAAmB5R,QAAnB;AACH;AACD;;;AACAsS,EAAAA,yBAAyB,CAACrB,WAAW,GAAG,KAAf,EAAsB;AAC3C,SAAKU,iBAAL,CAAuB0B,IAAvB,CAA4B,IAAItC,wBAAJ,CAA6B,IAA7B,EAAmCE,WAAnC,CAA5B;AACH;;AAjKgB;;AAmKrBC,cAAc,CAACzV,IAAf;AArgDkGhE,EAAAA,EAqgDlG;AAAA;;AACAyZ,cAAc,CAAC/J,IAAf,kBAtgDkG1P,EAsgDlG;AAAA,QAA+FyZ,cAA/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAvgDkGzZ,EAugDlG,mBAA2FyZ,cAA3F,EAAuH,CAAC;AAC5GrV,IAAAA,IAAI,EAAE3D;AADsG,GAAD,CAAvH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE2D,MAAAA,IAAI,EAAEpE,EAAE,CAAC2X;AAAX,KAAD,EAA0B;AAAEvT,MAAAA,IAAI,EAAEpE,EAAE,CAAC6b;AAAX,KAA1B,EAA0D;AAAEzX,MAAAA,IAAI,EAAEI;AAAR,KAA1D,EAA+E;AAAEJ,MAAAA,IAAI,EAAEwU;AAAR,KAA/E,CAAP;AAAoH,GAF9J,EAEgL;AAAE9S,IAAAA,KAAK,EAAE,CAAC;AAC1K1B,MAAAA,IAAI,EAAE1D;AADoK,KAAD,CAAT;AAEhKuZ,IAAAA,EAAE,EAAE,CAAC;AACL7V,MAAAA,IAAI,EAAE1D;AADD,KAAD,CAF4J;AAIhKmF,IAAAA,QAAQ,EAAE,CAAC;AACXzB,MAAAA,IAAI,EAAE1D;AADK,KAAD,CAJsJ;AAMhKwZ,IAAAA,iBAAiB,EAAE,CAAC;AACpB9V,MAAAA,IAAI,EAAErD;AADc,KAAD;AAN6I,GAFhL;AAAA;AAWA;AACA;AACA;;;AACA,MAAM+a,SAAN,SAAwBrC,cAAxB,CAAuC;AACnCrW,EAAAA,WAAW,CAAC2M,OAAD,EAAUgM,iBAAV,EAA6B7U,MAA7B,EAAqC2S,KAArC,EAA4C;AACnD,UAAM9J,OAAN,EAAegM,iBAAf,EAAkC7U,MAAlC,EAA0C2S,KAA1C;AACH;;AAHkC;;AAKvCiC,SAAS,CAAC9X,IAAV;AAAA,mBAAsG8X,SAAtG,EA1hDkG9b,EA0hDlG,mBAAiIA,EAAE,CAAC2X,UAApI,GA1hDkG3X,EA0hDlG,mBAA2JA,EAAE,CAAC6b,iBAA9J,GA1hDkG7b,EA0hDlG,mBAA4LyY,2BAA5L,MA1hDkGzY,EA0hDlG,mBAAoPiZ,YAApP;AAAA;;AACA6C,SAAS,CAAC7D,IAAV,kBA3hDkGjY,EA2hDlG;AAAA,QAA0F8b,SAA1F;AAAA;AAAA,sBAA2J,QAA3J;AAAA;AAAA;AAAA;AA3hDkG9b,MAAAA,EA2hDlG;AAAA,eAA0F,2BAA1F;AAAA;AAAA,eAA0F,0BAA1F;AAAA;AAAA;;AAAA;AA3hDkGA,MAAAA,EA2hDlG;AA3hDkGA,MAAAA,EA2hDlG;AA3hDkGA,MAAAA,EA2hDlG;AAAA;AAAA;AAAA;AAAA,aA3hDkGA,EA2hDlG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA3hDkGA,MAAAA,EA2hDlG;AA3hDkGA,MAAAA,EA2hDokB,wFAAtqB;AA3hDkGA,MAAAA,EA2hDuvB,6BAAz1B;AA3hDkGA,MAAAA,EA2hDuxB,gBAAz3B;AA3hDkGA,MAAAA,EA2hDgzB,eAAl5B;AA3hDkGA,MAAAA,EA2hDw4B,0DAA1+B;AA3hDkGA,MAAAA,EA2hD0+B,uBAA5kC;AAAA;;AAAA;AA3hDkGA,MAAAA,EA2hD0lB,iCAA5rB;AA3hDkGA,MAAAA,EA2hD66B,aAA/gC;AA3hDkGA,MAAAA,EA2hD66B,kDAA/gC;AA3hDkGA,MAAAA,EA2hD4hC,aAA9nC;AA3hDkGA,MAAAA,EA2hD4hC,8GAA9nC;AAAA;AAAA;AAAA,eAAqtFsB,EAAE,CAAC0a,IAAxtF,EAAsyFlF,SAAtyF,EAA0mFkB,iBAA1mF;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDA5hDkGhY,EA4hDlG,mBAA2F8b,SAA3F,EAAkH,CAAC;AACvG1X,IAAAA,IAAI,EAAEzD,SADiG;AAEvG0D,IAAAA,IAAI,EAAE,CAAC;AAAEsL,MAAAA,QAAQ,EAAE,YAAZ;AAA0BkI,MAAAA,QAAQ,EAAE,WAApC;AAAiDjI,MAAAA,IAAI,EAAE;AAClD,gBAAQ,QAD0C;AAElD,2BAAmB,gBAF+B;AAGlD,gCAAwB,UAH0B;AAIlD,uCAA+B,UAJmB;AAKlD,8BAAsB,QAL4B;AAMlD,gBAAQ,IAN0C;AAOlD,gCAAwB,oBAP0B;AAQlD,gCAAwB,qBAR0B;AASlD,uCAA+B,UATmB;AAUlD,mBAAW,yBAVuC;AAWlD,qBAAa,wBAXqC;AAYlD,iBAAS;AAZyC,OAAvD;AAaIsI,MAAAA,aAAa,EAAEtX,iBAAiB,CAACuX,IAbrC;AAa2CC,MAAAA,eAAe,EAAEvX,uBAAuB,CAACwX,MAbpF;AAa4FC,MAAAA,QAAQ,EAAE,qkBAbtG;AAa6qBC,MAAAA,MAAM,EAAE,CAAC,61CAAD;AAbrrB,KAAD;AAFiG,GAAD,CAAlH,EAgB4B,YAAY;AAAE,WAAO,CAAC;AAAEnU,MAAAA,IAAI,EAAEpE,EAAE,CAAC2X;AAAX,KAAD,EAA0B;AAAEvT,MAAAA,IAAI,EAAEpE,EAAE,CAAC6b;AAAX,KAA1B,EAA0D;AAAEzX,MAAAA,IAAI,EAAEI,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACvHL,QAAAA,IAAI,EAAEhE;AADiH,OAAD,EAEvH;AACCgE,QAAAA,IAAI,EAAE/D,MADP;AAECgE,QAAAA,IAAI,EAAE,CAACoU,2BAAD;AAFP,OAFuH;AAA/B,KAA1D,EAK3B;AAAErU,MAAAA,IAAI,EAAE8U,WAAR;AAAqBzU,MAAAA,UAAU,EAAE,CAAC;AACpCL,QAAAA,IAAI,EAAEhE;AAD8B,OAAD,EAEpC;AACCgE,QAAAA,IAAI,EAAE/D,MADP;AAECgE,QAAAA,IAAI,EAAE,CAAC4U,YAAD;AAFP,OAFoC;AAAjC,KAL2B,CAAP;AAUlB,GA1BxB;AAAA;AA2BA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASgD,6BAAT,CAAuCC,WAAvC,EAAoDjB,OAApD,EAA6DkB,YAA7D,EAA2E;AACvE,MAAIA,YAAY,CAAC9R,MAAjB,EAAyB;AACrB,QAAI+R,YAAY,GAAGnB,OAAO,CAACoB,OAAR,EAAnB;AACA,QAAIC,MAAM,GAAGH,YAAY,CAACE,OAAb,EAAb;AACA,QAAIE,YAAY,GAAG,CAAnB;;AACA,SAAK,IAAI9R,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyR,WAAW,GAAG,CAAlC,EAAqCzR,CAAC,EAAtC,EAA0C;AACtC,UAAI2R,YAAY,CAAC3R,CAAD,CAAZ,CAAgBoP,KAAhB,IAAyBuC,YAAY,CAAC3R,CAAD,CAAZ,CAAgBoP,KAAhB,KAA0ByC,MAAM,CAACC,YAAD,CAA7D,EAA6E;AACzEA,QAAAA,YAAY;AACf;AACJ;;AACD,WAAOA,YAAP;AACH;;AACD,SAAO,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,wBAAT,CAAkCC,YAAlC,EAAgDC,YAAhD,EAA8DC,qBAA9D,EAAqFC,WAArF,EAAkG;AAC9F,MAAIH,YAAY,GAAGE,qBAAnB,EAA0C;AACtC,WAAOF,YAAP;AACH;;AACD,MAAIA,YAAY,GAAGC,YAAf,GAA8BC,qBAAqB,GAAGC,WAA1D,EAAuE;AACnE,WAAOrG,IAAI,CAACtM,GAAL,CAAS,CAAT,EAAYwS,YAAY,GAAGG,WAAf,GAA6BF,YAAzC,CAAP;AACH;;AACD,SAAOC,qBAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAME,eAAN,CAAsB;;AAEtBA,eAAe,CAAC7Y,IAAhB;AAAA,mBAA4G6Y,eAA5G;AAAA;;AACAA,eAAe,CAAC3Y,IAAhB,kBAxmDkGlE,EAwmDlG;AAAA,QAA6G6c;AAA7G;AACAA,eAAe,CAAC1Y,IAAhB,kBAzmDkGnE,EAymDlG;AAAA,YAAwI,CAAC+X,eAAD,EAAkBvW,YAAlB,EAAgC2B,eAAhC,EAAiDqV,uBAAjD,CAAxI;AAAA;;AACA;AAAA,qDA1mDkGxY,EA0mDlG,mBAA2F6c,eAA3F,EAAwH,CAAC;AAC7GzY,IAAAA,IAAI,EAAEjE,QADuG;AAE7GkE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,OAAO,EAAE,CAACyT,eAAD,EAAkBvW,YAAlB,EAAgC2B,eAAhC,EAAiDqV,uBAAjD,CADV;AAECjU,MAAAA,OAAO,EAAE,CAACuX,SAAD,EAAY5C,WAAZ,CAFV;AAGCzI,MAAAA,YAAY,EAAE,CAACqL,SAAD,EAAY5C,WAAZ;AAHf,KAAD;AAFuG,GAAD,CAAxH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS5W,eAAT,EAA0BK,kBAA1B,EAA8C+F,WAA9C,EAA2D6G,iBAA3D,EAA8EvM,sBAA9E,EAAsGkH,gBAAtG,EAAwH1B,eAAxH,EAAyIC,uBAAzI,EAAkK+F,uBAAlK,EAA2LyK,YAA3L,EAAyMR,2BAAzM,EAAsO5B,yBAAtO,EAAiQ1T,eAAjQ,EAAkRsM,OAAlR,EAA2Re,aAA3R,EAA0SvB,mBAA1S,EAA+TiK,WAA/T,EAA4U4C,SAA5U,EAAuVe,eAAvV,EAAwWvD,wBAAxW,EAAkYtB,iBAAlY,EAAqZQ,uBAArZ,EAA8a1B,SAA9a,EAAybiB,eAAzb,EAA0crN,iBAA1c,EAA6dmE,gBAA7d,EAA+e6B,SAA/e,EAA0fc,cAA1f,EAA0gBrC,4BAA1gB,EAAwiB/N,OAAxiB,EAAijBwX,gBAAjjB,EAAmkBa,cAAnkB,EAAmlBwC,6BAAnlB,EAAknBO,wBAAlnB,EAA4oBxL,4BAA5oB,EAA0qBjL,UAA1qB,EAAsrBO,kBAAtrB,EAA0sBZ,aAA1sB,EAAytBmB,eAAztB,EAA0uBgB,gBAA1uB,EAA4vBpB,aAA5vB,EAA2wBoJ,QAA3wB", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Version, InjectionToken, NgModule, Optional, Inject, inject, LOCALE_ID, Injectable, Directive, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, EventEmitter, Output } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { isFakeMousedownFromScreenReader, isFakeTouchstartFromScreenReader } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { VERSION as VERSION$1 } from '@angular/cdk';\nimport * as i3 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { _isTestEnvironment, normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport { Subject, Observable } from 'rxjs';\nimport { startWith } from 'rxjs/operators';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { ENTER, SPACE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Current version of Angular Material. */\nconst VERSION = new Version('13.3.9');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nclass AnimationCurves {\n}\nAnimationCurves.STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\nAnimationCurves.DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\nAnimationCurves.ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\nAnimationCurves.SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n/** @docs-private */\nclass AnimationDurations {\n}\nAnimationDurations.COMPLEX = '375ms';\nAnimationDurations.ENTERING = '225ms';\nAnimationDurations.EXITING = '195ms';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nfunction MATERIAL_SANITY_CHECKS_FACTORY() {\n    return true;\n}\n/** Injection token that configures whether the Material sanity checks are enabled. */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n    providedIn: 'root',\n    factory: MATERIAL_SANITY_CHECKS_FACTORY,\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n */\nclass MatCommonModule {\n    constructor(highContrastModeDetector, _sanityChecks, _document) {\n        this._sanityChecks = _sanityChecks;\n        this._document = _document;\n        /** Whether we've done the global sanity checks (e.g. a theme is loaded, there is a doctype). */\n        this._hasDoneGlobalChecks = false;\n        // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n        // in MatCommonModule.\n        highContrastModeDetector._applyBodyHighContrastModeCssClasses();\n        if (!this._hasDoneGlobalChecks) {\n            this._hasDoneGlobalChecks = true;\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                if (this._checkIsEnabled('doctype')) {\n                    _checkDoctypeIsDefined(this._document);\n                }\n                if (this._checkIsEnabled('theme')) {\n                    _checkThemeIsPresent(this._document);\n                }\n                if (this._checkIsEnabled('version')) {\n                    _checkCdkVersionMatch();\n                }\n            }\n        }\n    }\n    /** Gets whether a specific sanity check is enabled. */\n    _checkIsEnabled(name) {\n        if (_isTestEnvironment()) {\n            return false;\n        }\n        if (typeof this._sanityChecks === 'boolean') {\n            return this._sanityChecks;\n        }\n        return !!this._sanityChecks[name];\n    }\n}\nMatCommonModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatCommonModule, deps: [{ token: i1.HighContrastModeDetector }, { token: MATERIAL_SANITY_CHECKS, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.NgModule });\nMatCommonModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatCommonModule, imports: [BidiModule], exports: [BidiModule] });\nMatCommonModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatCommonModule, imports: [[BidiModule], BidiModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatCommonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule],\n                    exports: [BidiModule],\n                }]\n        }], ctorParameters: function () { return [{ type: i1.HighContrastModeDetector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MATERIAL_SANITY_CHECKS]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/** Checks that the page has a doctype. */\nfunction _checkDoctypeIsDefined(doc) {\n    if (!doc.doctype) {\n        console.warn('Current document does not have a doctype. This may cause ' +\n            'some Angular Material components not to behave as expected.');\n    }\n}\n/** Checks that a theme has been included. */\nfunction _checkThemeIsPresent(doc) {\n    // We need to assert that the `body` is defined, because these checks run very early\n    // and the `body` won't be defined if the consumer put their scripts in the `head`.\n    if (!doc.body || typeof getComputedStyle !== 'function') {\n        return;\n    }\n    const testElement = doc.createElement('div');\n    testElement.classList.add('mat-theme-loaded-marker');\n    doc.body.appendChild(testElement);\n    const computedStyle = getComputedStyle(testElement);\n    // In some situations the computed style of the test element can be null. For example in\n    // Firefox, the computed style is null if an application is running inside of a hidden iframe.\n    // See: https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n    if (computedStyle && computedStyle.display !== 'none') {\n        console.warn('Could not find Angular Material core theme. Most Material ' +\n            'components may not work as expected. For more info refer ' +\n            'to the theming guide: https://material.angular.io/guide/theming');\n    }\n    testElement.remove();\n}\n/** Checks whether the Material version matches the CDK version. */\nfunction _checkCdkVersionMatch() {\n    if (VERSION.full !== VERSION$1.full) {\n        console.warn('The Angular Material version (' +\n            VERSION.full +\n            ') does not match ' +\n            'the Angular CDK version (' +\n            VERSION$1.full +\n            ').\\n' +\n            'Please ensure the versions of these two packages exactly match.');\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinDisabled(base) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            this._disabled = false;\n        }\n        get disabled() {\n            return this._disabled;\n        }\n        set disabled(value) {\n            this._disabled = coerceBooleanProperty(value);\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinColor(base, defaultColor) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            this.defaultColor = defaultColor;\n            // Set the default color that can be specified from the mixin.\n            this.color = defaultColor;\n        }\n        get color() {\n            return this._color;\n        }\n        set color(value) {\n            const colorPalette = value || this.defaultColor;\n            if (colorPalette !== this._color) {\n                if (this._color) {\n                    this._elementRef.nativeElement.classList.remove(`mat-${this._color}`);\n                }\n                if (colorPalette) {\n                    this._elementRef.nativeElement.classList.add(`mat-${colorPalette}`);\n                }\n                this._color = colorPalette;\n            }\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinDisableRipple(base) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            this._disableRipple = false;\n        }\n        /** Whether the ripple effect is disabled or not. */\n        get disableRipple() {\n            return this._disableRipple;\n        }\n        set disableRipple(value) {\n            this._disableRipple = coerceBooleanProperty(value);\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinTabIndex(base, defaultTabIndex = 0) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            this._tabIndex = defaultTabIndex;\n            this.defaultTabIndex = defaultTabIndex;\n        }\n        get tabIndex() {\n            return this.disabled ? -1 : this._tabIndex;\n        }\n        set tabIndex(value) {\n            // If the specified tabIndex value is null or undefined, fall back to the default value.\n            this._tabIndex = value != null ? coerceNumberProperty(value) : this.defaultTabIndex;\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction mixinErrorState(base) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            // This class member exists as an interop with `MatFormFieldControl` which expects\n            // a public `stateChanges` observable to emit whenever the form field should be updated.\n            // The description is not specifically mentioning the error state, as classes using this\n            // mixin can/should emit an event in other cases too.\n            /** Emits whenever the component state changes. */\n            this.stateChanges = new Subject();\n            /** Whether the component is in an error state. */\n            this.errorState = false;\n        }\n        /** Updates the error state based on the provided error state matcher. */\n        updateErrorState() {\n            const oldState = this.errorState;\n            const parent = this._parentFormGroup || this._parentForm;\n            const matcher = this.errorStateMatcher || this._defaultErrorStateMatcher;\n            const control = this.ngControl ? this.ngControl.control : null;\n            const newState = matcher.isErrorState(control, parent);\n            if (newState !== oldState) {\n                this.errorState = newState;\n                this.stateChanges.next();\n            }\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Mixin to augment a directive with an initialized property that will emits when ngOnInit ends. */\nfunction mixinInitialized(base) {\n    return class extends base {\n        constructor(...args) {\n            super(...args);\n            /** Whether this directive has been marked as initialized. */\n            this._isInitialized = false;\n            /**\n             * List of subscribers that subscribed before the directive was initialized. Should be notified\n             * during _markInitialized. Set to null after pending subscribers are notified, and should\n             * not expect to be populated after.\n             */\n            this._pendingSubscribers = [];\n            /**\n             * Observable stream that emits when the directive initializes. If already initialized, the\n             * subscriber is stored to be notified once _markInitialized is called.\n             */\n            this.initialized = new Observable(subscriber => {\n                // If initialized, immediately notify the subscriber. Otherwise store the subscriber to notify\n                // when _markInitialized is called.\n                if (this._isInitialized) {\n                    this._notifySubscriber(subscriber);\n                }\n                else {\n                    this._pendingSubscribers.push(subscriber);\n                }\n            });\n        }\n        /**\n         * Marks the state as initialized and notifies pending subscribers. Should be called at the end\n         * of ngOnInit.\n         * @docs-private\n         */\n        _markInitialized() {\n            if (this._isInitialized && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('This directive has already been marked as initialized and ' +\n                    'should not be called twice.');\n            }\n            this._isInitialized = true;\n            this._pendingSubscribers.forEach(this._notifySubscriber);\n            this._pendingSubscribers = null;\n        }\n        /** Emits and completes the subscriber stream (should only emit once). */\n        _notifySubscriber(subscriber) {\n            subscriber.next();\n            subscriber.complete();\n        }\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n    providedIn: 'root',\n    factory: MAT_DATE_LOCALE_FACTORY,\n});\n/** @docs-private */\nfunction MAT_DATE_LOCALE_FACTORY() {\n    return inject(LOCALE_ID);\n}\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n    constructor() {\n        this._localeChanges = new Subject();\n        /** A stream that emits when the locale changes. */\n        this.localeChanges = this._localeChanges;\n    }\n    /**\n     * Given a potential date object, returns that same date object if it is\n     * a valid date, or `null` if it's not a valid date.\n     * @param obj The object to check.\n     * @returns A date or `null`.\n     */\n    getValidDateOrNull(obj) {\n        return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n    }\n    /**\n     * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n     * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n     * string). The default implementation does not allow any deserialization, it simply checks that\n     * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n     * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n     * support passing values from your backend directly to these properties by overriding this method\n     * to also deserialize the format used by your backend.\n     * @param value The value to be deserialized into a date object.\n     * @returns The deserialized date object, either a valid date, null if the value can be\n     *     deserialized into a null date (e.g. the empty string), or an invalid date.\n     */\n    deserialize(value) {\n        if (value == null || (this.isDateInstance(value) && this.isValid(value))) {\n            return value;\n        }\n        return this.invalid();\n    }\n    /**\n     * Sets the locale used for all dates.\n     * @param locale The new locale.\n     */\n    setLocale(locale) {\n        this.locale = locale;\n        this._localeChanges.next();\n    }\n    /**\n     * Compares two dates.\n     * @param first The first date to compare.\n     * @param second The second date to compare.\n     * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n     *     a number greater than 0 if the first date is later.\n     */\n    compareDate(first, second) {\n        return (this.getYear(first) - this.getYear(second) ||\n            this.getMonth(first) - this.getMonth(second) ||\n            this.getDate(first) - this.getDate(second));\n    }\n    /**\n     * Checks if two dates are equal.\n     * @param first The first date to check.\n     * @param second The second date to check.\n     * @returns Whether the two dates are equal.\n     *     Null dates are considered equal to other null dates.\n     */\n    sameDate(first, second) {\n        if (first && second) {\n            let firstValid = this.isValid(first);\n            let secondValid = this.isValid(second);\n            if (firstValid && secondValid) {\n                return !this.compareDate(first, second);\n            }\n            return firstValid == secondValid;\n        }\n        return first == second;\n    }\n    /**\n     * Clamp the given date between min and max dates.\n     * @param date The date to clamp.\n     * @param min The minimum value to allow. If null or omitted no min is enforced.\n     * @param max The maximum value to allow. If null or omitted no max is enforced.\n     * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n     *     otherwise `date`.\n     */\n    clampDate(date, min, max) {\n        if (min && this.compareDate(date, min) < 0) {\n            return min;\n        }\n        if (max && this.compareDate(date, max) > 0) {\n            return max;\n        }\n        return date;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings an with out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX = /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n/** Creates an array and fills it with values. */\nfunction range(length, valueFunction) {\n    const valuesArray = Array(length);\n    for (let i = 0; i < length; i++) {\n        valuesArray[i] = valueFunction(i);\n    }\n    return valuesArray;\n}\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\nclass NativeDateAdapter extends DateAdapter {\n    constructor(matDateLocale, \n    /**\n     * @deprecated No longer being used. To be removed.\n     * @breaking-change 14.0.0\n     */\n    _platform) {\n        super();\n        /**\n         * @deprecated No longer being used. To be removed.\n         * @breaking-change 14.0.0\n         */\n        this.useUtcForDisplay = false;\n        super.setLocale(matDateLocale);\n    }\n    getYear(date) {\n        return date.getFullYear();\n    }\n    getMonth(date) {\n        return date.getMonth();\n    }\n    getDate(date) {\n        return date.getDate();\n    }\n    getDayOfWeek(date) {\n        return date.getDay();\n    }\n    getMonthNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { month: style, timeZone: 'utc' });\n        return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n    }\n    getDateNames() {\n        const dtf = new Intl.DateTimeFormat(this.locale, { day: 'numeric', timeZone: 'utc' });\n        return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getDayOfWeekNames(style) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { weekday: style, timeZone: 'utc' });\n        return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n    }\n    getYearName(date) {\n        const dtf = new Intl.DateTimeFormat(this.locale, { year: 'numeric', timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    getFirstDayOfWeek() {\n        // We can't tell using native JS Date what the first day of the week is, we default to Sunday.\n        return 0;\n    }\n    getNumDaysInMonth(date) {\n        return this.getDate(this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0));\n    }\n    clone(date) {\n        return new Date(date.getTime());\n    }\n    createDate(year, month, date) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            // Check for invalid month and date (except upper bound on date which we have to check after\n            // creating the Date).\n            if (month < 0 || month > 11) {\n                throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n            }\n            if (date < 1) {\n                throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n            }\n        }\n        let result = this._createDateWithOverflow(year, month, date);\n        // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n        if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n        }\n        return result;\n    }\n    today() {\n        return new Date();\n    }\n    parse(value) {\n        // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n        // parameters.\n        if (typeof value == 'number') {\n            return new Date(value);\n        }\n        return value ? new Date(Date.parse(value)) : null;\n    }\n    format(date, displayFormat) {\n        if (!this.isValid(date)) {\n            throw Error('NativeDateAdapter: Cannot format invalid date.');\n        }\n        const dtf = new Intl.DateTimeFormat(this.locale, { ...displayFormat, timeZone: 'utc' });\n        return this._format(dtf, date);\n    }\n    addCalendarYears(date, years) {\n        return this.addCalendarMonths(date, years * 12);\n    }\n    addCalendarMonths(date, months) {\n        let newDate = this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + months, this.getDate(date));\n        // It's possible to wind up in the wrong month if the original month has more days than the new\n        // month. In this case we want to go to the last day of the desired month.\n        // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n        // guarantee this.\n        if (this.getMonth(newDate) != (((this.getMonth(date) + months) % 12) + 12) % 12) {\n            newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n        }\n        return newDate;\n    }\n    addCalendarDays(date, days) {\n        return this._createDateWithOverflow(this.getYear(date), this.getMonth(date), this.getDate(date) + days);\n    }\n    toIso8601(date) {\n        return [\n            date.getUTCFullYear(),\n            this._2digit(date.getUTCMonth() + 1),\n            this._2digit(date.getUTCDate()),\n        ].join('-');\n    }\n    /**\n     * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n     * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n     * invalid date for all other values.\n     */\n    deserialize(value) {\n        if (typeof value === 'string') {\n            if (!value) {\n                return null;\n            }\n            // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n            // string is the right format first.\n            if (ISO_8601_REGEX.test(value)) {\n                let date = new Date(value);\n                if (this.isValid(date)) {\n                    return date;\n                }\n            }\n        }\n        return super.deserialize(value);\n    }\n    isDateInstance(obj) {\n        return obj instanceof Date;\n    }\n    isValid(date) {\n        return !isNaN(date.getTime());\n    }\n    invalid() {\n        return new Date(NaN);\n    }\n    /** Creates a date but allows the month and date to overflow. */\n    _createDateWithOverflow(year, month, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setFullYear` and `setHours` instead.\n        const d = new Date();\n        d.setFullYear(year, month, date);\n        d.setHours(0, 0, 0, 0);\n        return d;\n    }\n    /**\n     * Pads a number to make it two digits.\n     * @param n The number to pad.\n     * @returns The padded number.\n     */\n    _2digit(n) {\n        return ('00' + n).slice(-2);\n    }\n    /**\n     * When converting Date object to string, javascript built-in functions may return wrong\n     * results because it applies its internal DST rules. The DST rules around the world change\n     * very frequently, and the current valid rule is not always valid in previous years though.\n     * We work around this problem building a new Date object which has its internal UTC\n     * representation with the local date and time.\n     * @param dtf Intl.DateTimeFormat object, containg the desired string format. It must have\n     *    timeZone set to 'utc' to work fine.\n     * @param date Date from which we want to get the string representation according to dtf\n     * @returns A Date object with its UTC representation based on the passed in date info\n     */\n    _format(dtf, date) {\n        // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n        // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n        const d = new Date();\n        d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n        d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n        return dtf.format(d);\n    }\n}\nNativeDateAdapter.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: NativeDateAdapter, deps: [{ token: MAT_DATE_LOCALE, optional: true }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nNativeDateAdapter.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: NativeDateAdapter });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: NativeDateAdapter, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_DATE_LOCALE]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst MAT_NATIVE_DATE_FORMATS = {\n    parse: {\n        dateInput: null,\n    },\n    display: {\n        dateInput: { year: 'numeric', month: 'numeric', day: 'numeric' },\n        monthYearLabel: { year: 'numeric', month: 'short' },\n        dateA11yLabel: { year: 'numeric', month: 'long', day: 'numeric' },\n        monthYearA11yLabel: { year: 'numeric', month: 'long' },\n    },\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass NativeDateModule {\n}\nNativeDateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: NativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNativeDateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: NativeDateModule });\nNativeDateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: NativeDateModule, providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: NativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [{ provide: DateAdapter, useClass: NativeDateAdapter }],\n                }]\n        }] });\nclass MatNativeDateModule {\n}\nMatNativeDateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatNativeDateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatNativeDateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatNativeDateModule, imports: [NativeDateModule] });\nMatNativeDateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatNativeDateModule, providers: [{ provide: MAT_DATE_FORMATS, useValue: MAT_NATIVE_DATE_FORMATS }], imports: [[NativeDateModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatNativeDateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NativeDateModule],\n                    providers: [{ provide: MAT_DATE_FORMATS, useValue: MAT_NATIVE_DATE_FORMATS }],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Error state matcher that matches when a control is invalid and dirty. */\nclass ShowOnDirtyErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.dirty || (form && form.submitted)));\n    }\n}\nShowOnDirtyErrorStateMatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nShowOnDirtyErrorStateMatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ShowOnDirtyErrorStateMatcher, decorators: [{\n            type: Injectable\n        }] });\n/** Provider that defines how form controls behave with regards to displaying error messages. */\nclass ErrorStateMatcher {\n    isErrorState(control, form) {\n        return !!(control && control.invalid && (control.touched || (form && form.submitted)));\n    }\n}\nErrorStateMatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ErrorStateMatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nErrorStateMatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ErrorStateMatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ErrorStateMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nclass MatLine {\n}\nMatLine.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatLine, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatLine.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatLine, selector: \"[mat-line], [matLine]\", host: { classAttribute: \"mat-line\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatLine, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-line], [matLine]',\n                    host: { 'class': 'mat-line' },\n                }]\n        }] });\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n    // Note: doesn't need to unsubscribe, because `changes`\n    // gets completed by Angular when the view is destroyed.\n    lines.changes.pipe(startWith(lines)).subscribe(({ length }) => {\n        setClass(element, `${prefix}-2-line`, false);\n        setClass(element, `${prefix}-3-line`, false);\n        setClass(element, `${prefix}-multi-line`, false);\n        if (length === 2 || length === 3) {\n            setClass(element, `${prefix}-${length}-line`, true);\n        }\n        else if (length > 3) {\n            setClass(element, `${prefix}-multi-line`, true);\n        }\n    });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n    element.nativeElement.classList.toggle(className, isAdd);\n}\nclass MatLineModule {\n}\nMatLineModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatLineModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatLineModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatLineModule, declarations: [MatLine], imports: [MatCommonModule], exports: [MatLine, MatCommonModule] });\nMatLineModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatLineModule, imports: [[MatCommonModule], MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatLineModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatLine, MatCommonModule],\n                    declarations: [MatLine],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to a previously launched ripple element.\n */\nclass RippleRef {\n    constructor(_renderer, \n    /** Reference to the ripple HTML element. */\n    element, \n    /** Ripple configuration used for the ripple. */\n    config) {\n        this._renderer = _renderer;\n        this.element = element;\n        this.config = config;\n        /** Current state of the ripple. */\n        this.state = 3 /* HIDDEN */;\n    }\n    /** Fades out the ripple element. */\n    fadeOut() {\n        this._renderer.fadeOutRipple(this);\n    }\n}\n\n// TODO: import these values from `@material/ripple` eventually.\n/**\n * Default ripple animation configuration for ripples without an explicit\n * animation config specified.\n */\nconst defaultRippleAnimationConfig = {\n    enterDuration: 225,\n    exitDuration: 150,\n};\n/**\n * Timeout for ignoring mouse events. Mouse events will be temporary ignored after touch\n * events to avoid synthetic mouse events.\n */\nconst ignoreMouseEventsTimeout = 800;\n/** Options that apply to all the event listeners that are bound by the ripple renderer. */\nconst passiveEventOptions = normalizePassiveListenerOptions({ passive: true });\n/** Events that signal that the pointer is down. */\nconst pointerDownEvents = ['mousedown', 'touchstart'];\n/** Events that signal that the pointer is up. */\nconst pointerUpEvents = ['mouseup', 'mouseleave', 'touchend', 'touchcancel'];\n/**\n * Helper service that performs DOM manipulations. Not intended to be used outside this module.\n * The constructor takes a reference to the ripple directive's host element and a map of DOM\n * event handlers to be installed on the element that triggers ripple animations.\n * This will eventually become a custom renderer once Angular support exists.\n * @docs-private\n */\nclass RippleRenderer {\n    constructor(_target, _ngZone, elementOrElementRef, platform) {\n        this._target = _target;\n        this._ngZone = _ngZone;\n        /** Whether the pointer is currently down or not. */\n        this._isPointerDown = false;\n        /** Set of currently active ripple references. */\n        this._activeRipples = new Set();\n        /** Whether pointer-up event listeners have been registered. */\n        this._pointerUpEventsRegistered = false;\n        // Only do anything if we're on the browser.\n        if (platform.isBrowser) {\n            this._containerElement = coerceElement(elementOrElementRef);\n        }\n    }\n    /**\n     * Fades in a ripple at the given coordinates.\n     * @param x Coordinate within the element, along the X axis at which to start the ripple.\n     * @param y Coordinate within the element, along the Y axis at which to start the ripple.\n     * @param config Extra ripple options.\n     */\n    fadeInRipple(x, y, config = {}) {\n        const containerRect = (this._containerRect =\n            this._containerRect || this._containerElement.getBoundingClientRect());\n        const animationConfig = { ...defaultRippleAnimationConfig, ...config.animation };\n        if (config.centered) {\n            x = containerRect.left + containerRect.width / 2;\n            y = containerRect.top + containerRect.height / 2;\n        }\n        const radius = config.radius || distanceToFurthestCorner(x, y, containerRect);\n        const offsetX = x - containerRect.left;\n        const offsetY = y - containerRect.top;\n        const duration = animationConfig.enterDuration;\n        const ripple = document.createElement('div');\n        ripple.classList.add('mat-ripple-element');\n        ripple.style.left = `${offsetX - radius}px`;\n        ripple.style.top = `${offsetY - radius}px`;\n        ripple.style.height = `${radius * 2}px`;\n        ripple.style.width = `${radius * 2}px`;\n        // If a custom color has been specified, set it as inline style. If no color is\n        // set, the default color will be applied through the ripple theme styles.\n        if (config.color != null) {\n            ripple.style.backgroundColor = config.color;\n        }\n        ripple.style.transitionDuration = `${duration}ms`;\n        this._containerElement.appendChild(ripple);\n        // By default the browser does not recalculate the styles of dynamically created\n        // ripple elements. This is critical because then the `scale` would not animate properly.\n        enforceStyleRecalculation(ripple);\n        ripple.style.transform = 'scale(1)';\n        // Exposed reference to the ripple that will be returned.\n        const rippleRef = new RippleRef(this, ripple, config);\n        rippleRef.state = 0 /* FADING_IN */;\n        // Add the ripple reference to the list of all active ripples.\n        this._activeRipples.add(rippleRef);\n        if (!config.persistent) {\n            this._mostRecentTransientRipple = rippleRef;\n        }\n        // Wait for the ripple element to be completely faded in.\n        // Once it's faded in, the ripple can be hidden immediately if the mouse is released.\n        this._runTimeoutOutsideZone(() => {\n            const isMostRecentTransientRipple = rippleRef === this._mostRecentTransientRipple;\n            rippleRef.state = 1 /* VISIBLE */;\n            // When the timer runs out while the user has kept their pointer down, we want to\n            // keep only the persistent ripples and the latest transient ripple. We do this,\n            // because we don't want stacked transient ripples to appear after their enter\n            // animation has finished.\n            if (!config.persistent && (!isMostRecentTransientRipple || !this._isPointerDown)) {\n                rippleRef.fadeOut();\n            }\n        }, duration);\n        return rippleRef;\n    }\n    /** Fades out a ripple reference. */\n    fadeOutRipple(rippleRef) {\n        const wasActive = this._activeRipples.delete(rippleRef);\n        if (rippleRef === this._mostRecentTransientRipple) {\n            this._mostRecentTransientRipple = null;\n        }\n        // Clear out the cached bounding rect if we have no more ripples.\n        if (!this._activeRipples.size) {\n            this._containerRect = null;\n        }\n        // For ripples that are not active anymore, don't re-run the fade-out animation.\n        if (!wasActive) {\n            return;\n        }\n        const rippleEl = rippleRef.element;\n        const animationConfig = { ...defaultRippleAnimationConfig, ...rippleRef.config.animation };\n        rippleEl.style.transitionDuration = `${animationConfig.exitDuration}ms`;\n        rippleEl.style.opacity = '0';\n        rippleRef.state = 2 /* FADING_OUT */;\n        // Once the ripple faded out, the ripple can be safely removed from the DOM.\n        this._runTimeoutOutsideZone(() => {\n            rippleRef.state = 3 /* HIDDEN */;\n            rippleEl.remove();\n        }, animationConfig.exitDuration);\n    }\n    /** Fades out all currently active ripples. */\n    fadeOutAll() {\n        this._activeRipples.forEach(ripple => ripple.fadeOut());\n    }\n    /** Fades out all currently active non-persistent ripples. */\n    fadeOutAllNonPersistent() {\n        this._activeRipples.forEach(ripple => {\n            if (!ripple.config.persistent) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    /** Sets up the trigger event listeners */\n    setupTriggerEvents(elementOrElementRef) {\n        const element = coerceElement(elementOrElementRef);\n        if (!element || element === this._triggerElement) {\n            return;\n        }\n        // Remove all previously registered event listeners from the trigger element.\n        this._removeTriggerEvents();\n        this._triggerElement = element;\n        this._registerEvents(pointerDownEvents);\n    }\n    /**\n     * Handles all registered events.\n     * @docs-private\n     */\n    handleEvent(event) {\n        if (event.type === 'mousedown') {\n            this._onMousedown(event);\n        }\n        else if (event.type === 'touchstart') {\n            this._onTouchStart(event);\n        }\n        else {\n            this._onPointerUp();\n        }\n        // If pointer-up events haven't been registered yet, do so now.\n        // We do this on-demand in order to reduce the total number of event listeners\n        // registered by the ripples, which speeds up the rendering time for large UIs.\n        if (!this._pointerUpEventsRegistered) {\n            this._registerEvents(pointerUpEvents);\n            this._pointerUpEventsRegistered = true;\n        }\n    }\n    /** Function being called whenever the trigger is being pressed using mouse. */\n    _onMousedown(event) {\n        // Screen readers will fire fake mouse events for space/enter. Skip launching a\n        // ripple in this case for consistency with the non-screen-reader experience.\n        const isFakeMousedown = isFakeMousedownFromScreenReader(event);\n        const isSyntheticEvent = this._lastTouchStartEvent &&\n            Date.now() < this._lastTouchStartEvent + ignoreMouseEventsTimeout;\n        if (!this._target.rippleDisabled && !isFakeMousedown && !isSyntheticEvent) {\n            this._isPointerDown = true;\n            this.fadeInRipple(event.clientX, event.clientY, this._target.rippleConfig);\n        }\n    }\n    /** Function being called whenever the trigger is being pressed using touch. */\n    _onTouchStart(event) {\n        if (!this._target.rippleDisabled && !isFakeTouchstartFromScreenReader(event)) {\n            // Some browsers fire mouse events after a `touchstart` event. Those synthetic mouse\n            // events will launch a second ripple if we don't ignore mouse events for a specific\n            // time after a touchstart event.\n            this._lastTouchStartEvent = Date.now();\n            this._isPointerDown = true;\n            // Use `changedTouches` so we skip any touches where the user put\n            // their finger down, but used another finger to tap the element again.\n            const touches = event.changedTouches;\n            for (let i = 0; i < touches.length; i++) {\n                this.fadeInRipple(touches[i].clientX, touches[i].clientY, this._target.rippleConfig);\n            }\n        }\n    }\n    /** Function being called whenever the trigger is being released. */\n    _onPointerUp() {\n        if (!this._isPointerDown) {\n            return;\n        }\n        this._isPointerDown = false;\n        // Fade-out all ripples that are visible and not persistent.\n        this._activeRipples.forEach(ripple => {\n            // By default, only ripples that are completely visible will fade out on pointer release.\n            // If the `terminateOnPointerUp` option is set, ripples that still fade in will also fade out.\n            const isVisible = ripple.state === 1 /* VISIBLE */ ||\n                (ripple.config.terminateOnPointerUp && ripple.state === 0 /* FADING_IN */);\n            if (!ripple.config.persistent && isVisible) {\n                ripple.fadeOut();\n            }\n        });\n    }\n    /** Runs a timeout outside of the Angular zone to avoid triggering the change detection. */\n    _runTimeoutOutsideZone(fn, delay = 0) {\n        this._ngZone.runOutsideAngular(() => setTimeout(fn, delay));\n    }\n    /** Registers event listeners for a given list of events. */\n    _registerEvents(eventTypes) {\n        this._ngZone.runOutsideAngular(() => {\n            eventTypes.forEach(type => {\n                this._triggerElement.addEventListener(type, this, passiveEventOptions);\n            });\n        });\n    }\n    /** Removes previously registered event listeners from the trigger element. */\n    _removeTriggerEvents() {\n        if (this._triggerElement) {\n            pointerDownEvents.forEach(type => {\n                this._triggerElement.removeEventListener(type, this, passiveEventOptions);\n            });\n            if (this._pointerUpEventsRegistered) {\n                pointerUpEvents.forEach(type => {\n                    this._triggerElement.removeEventListener(type, this, passiveEventOptions);\n                });\n            }\n        }\n    }\n}\n/** Enforces a style recalculation of a DOM element by computing its styles. */\nfunction enforceStyleRecalculation(element) {\n    // Enforce a style recalculation by calling `getComputedStyle` and accessing any property.\n    // Calling `getPropertyValue` is important to let optimizers know that this is not a noop.\n    // See: https://gist.github.com/paulirish/5d52fb081b3570c81e3a\n    window.getComputedStyle(element).getPropertyValue('opacity');\n}\n/**\n * Returns the distance from the point (x, y) to the furthest corner of a rectangle.\n */\nfunction distanceToFurthestCorner(x, y, rect) {\n    const distX = Math.max(Math.abs(x - rect.left), Math.abs(x - rect.right));\n    const distY = Math.max(Math.abs(y - rect.top), Math.abs(y - rect.bottom));\n    return Math.sqrt(distX * distX + distY * distY);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token that can be used to specify the global ripple options. */\nconst MAT_RIPPLE_GLOBAL_OPTIONS = new InjectionToken('mat-ripple-global-options');\nclass MatRipple {\n    constructor(_elementRef, ngZone, platform, globalOptions, _animationMode) {\n        this._elementRef = _elementRef;\n        this._animationMode = _animationMode;\n        /**\n         * If set, the radius in pixels of foreground ripples when fully expanded. If unset, the radius\n         * will be the distance from the center of the ripple to the furthest corner of the host element's\n         * bounding rectangle.\n         */\n        this.radius = 0;\n        this._disabled = false;\n        /** Whether ripple directive is initialized and the input bindings are set. */\n        this._isInitialized = false;\n        this._globalOptions = globalOptions || {};\n        this._rippleRenderer = new RippleRenderer(this, ngZone, _elementRef, platform);\n    }\n    /**\n     * Whether click events will not trigger the ripple. Ripples can be still launched manually\n     * by using the `launch()` method.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value) {\n            this.fadeOutAllNonPersistent();\n        }\n        this._disabled = value;\n        this._setupTriggerEventsIfEnabled();\n    }\n    /**\n     * The element that triggers the ripple when click events are received.\n     * Defaults to the directive's host element.\n     */\n    get trigger() {\n        return this._trigger || this._elementRef.nativeElement;\n    }\n    set trigger(trigger) {\n        this._trigger = trigger;\n        this._setupTriggerEventsIfEnabled();\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n        this._setupTriggerEventsIfEnabled();\n    }\n    ngOnDestroy() {\n        this._rippleRenderer._removeTriggerEvents();\n    }\n    /** Fades out all currently showing ripple elements. */\n    fadeOutAll() {\n        this._rippleRenderer.fadeOutAll();\n    }\n    /** Fades out all currently showing non-persistent ripple elements. */\n    fadeOutAllNonPersistent() {\n        this._rippleRenderer.fadeOutAllNonPersistent();\n    }\n    /**\n     * Ripple configuration from the directive's input values.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleConfig() {\n        return {\n            centered: this.centered,\n            radius: this.radius,\n            color: this.color,\n            animation: {\n                ...this._globalOptions.animation,\n                ...(this._animationMode === 'NoopAnimations' ? { enterDuration: 0, exitDuration: 0 } : {}),\n                ...this.animation,\n            },\n            terminateOnPointerUp: this._globalOptions.terminateOnPointerUp,\n        };\n    }\n    /**\n     * Whether ripples on pointer-down are disabled or not.\n     * @docs-private Implemented as part of RippleTarget\n     */\n    get rippleDisabled() {\n        return this.disabled || !!this._globalOptions.disabled;\n    }\n    /** Sets up the trigger event listeners if ripples are enabled. */\n    _setupTriggerEventsIfEnabled() {\n        if (!this.disabled && this._isInitialized) {\n            this._rippleRenderer.setupTriggerEvents(this.trigger);\n        }\n    }\n    /** Launches a manual ripple at the specified coordinated or just by the ripple config. */\n    launch(configOrX, y = 0, config) {\n        if (typeof configOrX === 'number') {\n            return this._rippleRenderer.fadeInRipple(configOrX, y, { ...this.rippleConfig, ...config });\n        }\n        else {\n            return this._rippleRenderer.fadeInRipple(0, 0, { ...this.rippleConfig, ...configOrX });\n        }\n    }\n}\nMatRipple.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatRipple, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1$1.Platform }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatRipple.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: { color: [\"matRippleColor\", \"color\"], unbounded: [\"matRippleUnbounded\", \"unbounded\"], centered: [\"matRippleCentered\", \"centered\"], radius: [\"matRippleRadius\", \"radius\"], animation: [\"matRippleAnimation\", \"animation\"], disabled: [\"matRippleDisabled\", \"disabled\"], trigger: [\"matRippleTrigger\", \"trigger\"] }, host: { properties: { \"class.mat-ripple-unbounded\": \"unbounded\" }, classAttribute: \"mat-ripple\" }, exportAs: [\"matRipple\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatRipple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-ripple], [matRipple]',\n                    exportAs: 'matRipple',\n                    host: {\n                        'class': 'mat-ripple',\n                        '[class.mat-ripple-unbounded]': 'unbounded',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1$1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { color: [{\n                type: Input,\n                args: ['matRippleColor']\n            }], unbounded: [{\n                type: Input,\n                args: ['matRippleUnbounded']\n            }], centered: [{\n                type: Input,\n                args: ['matRippleCentered']\n            }], radius: [{\n                type: Input,\n                args: ['matRippleRadius']\n            }], animation: [{\n                type: Input,\n                args: ['matRippleAnimation']\n            }], disabled: [{\n                type: Input,\n                args: ['matRippleDisabled']\n            }], trigger: [{\n                type: Input,\n                args: ['matRippleTrigger']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatRippleModule {\n}\nMatRippleModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatRippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatRippleModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatRippleModule, declarations: [MatRipple], imports: [MatCommonModule], exports: [MatRipple, MatCommonModule] });\nMatRippleModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatRippleModule, imports: [[MatCommonModule], MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatRippleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatRipple, MatCommonModule],\n                    declarations: [MatRipple],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Component that shows a simplified checkbox without including any kind of \"real\" checkbox.\n * Meant to be used when the checkbox is purely decorative and a large number of them will be\n * included, such as for the options in a multi-select. Uses no SVGs or complex animations.\n * Note that theming is meant to be handled by the parent element, e.g.\n * `mat-primary .mat-pseudo-checkbox`.\n *\n * Note that this component will be completely invisible to screen-reader users. This is *not*\n * interchangeable with `<mat-checkbox>` and should *not* be used if the user would directly\n * interact with the checkbox. The pseudo-checkbox should only be used as an implementation detail\n * of more complex components that appropriately handle selected / checked state.\n * @docs-private\n */\nclass MatPseudoCheckbox {\n    constructor(_animationMode) {\n        this._animationMode = _animationMode;\n        /** Display state of the checkbox. */\n        this.state = 'unchecked';\n        /** Whether the checkbox is disabled. */\n        this.disabled = false;\n    }\n}\nMatPseudoCheckbox.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPseudoCheckbox, deps: [{ token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatPseudoCheckbox.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: { state: \"state\", disabled: \"disabled\" }, host: { properties: { \"class.mat-pseudo-checkbox-indeterminate\": \"state === \\\"indeterminate\\\"\", \"class.mat-pseudo-checkbox-checked\": \"state === \\\"checked\\\"\", \"class.mat-pseudo-checkbox-disabled\": \"disabled\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\" }, classAttribute: \"mat-pseudo-checkbox\" }, ngImport: i0, template: '', isInline: true, styles: [\".mat-pseudo-checkbox{width:16px;height:16px;border:2px solid;border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox.mat-pseudo-checkbox-indeterminate{border-color:transparent}._mat-animation-noopable.mat-pseudo-checkbox{transition:none;animation:none}._mat-animation-noopable.mat-pseudo-checkbox::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{top:5px;left:1px;width:10px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{top:2.4px;left:1px;width:8px;height:3px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPseudoCheckbox, decorators: [{\n            type: Component,\n            args: [{ encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, selector: 'mat-pseudo-checkbox', template: '', host: {\n                        'class': 'mat-pseudo-checkbox',\n                        '[class.mat-pseudo-checkbox-indeterminate]': 'state === \"indeterminate\"',\n                        '[class.mat-pseudo-checkbox-checked]': 'state === \"checked\"',\n                        '[class.mat-pseudo-checkbox-disabled]': 'disabled',\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                    }, styles: [\".mat-pseudo-checkbox{width:16px;height:16px;border:2px solid;border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:\\\"\\\";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox.mat-pseudo-checkbox-indeterminate{border-color:transparent}._mat-animation-noopable.mat-pseudo-checkbox{transition:none;animation:none}._mat-animation-noopable.mat-pseudo-checkbox::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{top:5px;left:1px;width:10px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{top:2.4px;left:1px;width:8px;height:3px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { state: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatPseudoCheckboxModule {\n}\nMatPseudoCheckboxModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPseudoCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatPseudoCheckboxModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPseudoCheckboxModule, declarations: [MatPseudoCheckbox], imports: [MatCommonModule], exports: [MatPseudoCheckbox] });\nMatPseudoCheckboxModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPseudoCheckboxModule, imports: [[MatCommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPseudoCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: [MatPseudoCheckbox],\n                    declarations: [MatPseudoCheckbox],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token used to provide the parent component to options.\n */\nconst MAT_OPTION_PARENT_COMPONENT = new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n// Boilerplate for applying mixins to MatOptgroup.\n/** @docs-private */\nconst _MatOptgroupMixinBase = mixinDisabled(class {\n});\n// Counter for unique group ids.\nlet _uniqueOptgroupIdCounter = 0;\nclass _MatOptgroupBase extends _MatOptgroupMixinBase {\n    constructor(parent) {\n        super();\n        /** Unique id for the underlying label. */\n        this._labelId = `mat-optgroup-label-${_uniqueOptgroupIdCounter++}`;\n        this._inert = parent?.inertGroups ?? false;\n    }\n}\n_MatOptgroupBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatOptgroupBase, deps: [{ token: MAT_OPTION_PARENT_COMPONENT, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatOptgroupBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: _MatOptgroupBase, inputs: { label: \"label\" }, usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatOptgroupBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_OPTION_PARENT_COMPONENT]\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { label: [{\n                type: Input\n            }] } });\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nclass MatOptgroup extends _MatOptgroupBase {\n}\nMatOptgroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatOptgroup, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatOptgroup.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatOptgroup, selector: \"mat-optgroup\", inputs: { disabled: \"disabled\" }, host: { properties: { \"attr.role\": \"_inert ? null : \\\"group\\\"\", \"attr.aria-disabled\": \"_inert ? null : disabled.toString()\", \"attr.aria-labelledby\": \"_inert ? null : _labelId\", \"class.mat-optgroup-disabled\": \"disabled\" }, classAttribute: \"mat-optgroup\" }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], exportAs: [\"matOptgroup\"], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-optgroup-label\\\" aria-hidden=\\\"true\\\" [id]=\\\"_labelId\\\">{{ label }} <ng-content></ng-content></span>\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-optgroup-label{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup-label[disabled]{cursor:default}[dir=rtl] .mat-optgroup-label{text-align:right}.mat-optgroup-label .mat-icon{margin-right:16px;vertical-align:middle}.mat-optgroup-label .mat-icon svg{vertical-align:top}[dir=rtl] .mat-optgroup-label .mat-icon{margin-left:16px;margin-right:0}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatOptgroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-optgroup', exportAs: 'matOptgroup', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, inputs: ['disabled'], host: {\n                        'class': 'mat-optgroup',\n                        '[attr.role]': '_inert ? null : \"group\"',\n                        '[attr.aria-disabled]': '_inert ? null : disabled.toString()',\n                        '[attr.aria-labelledby]': '_inert ? null : _labelId',\n                        '[class.mat-optgroup-disabled]': 'disabled',\n                    }, providers: [{ provide: MAT_OPTGROUP, useExisting: MatOptgroup }], template: \"<span class=\\\"mat-optgroup-label\\\" aria-hidden=\\\"true\\\" [id]=\\\"_labelId\\\">{{ label }} <ng-content></ng-content></span>\\n<ng-content select=\\\"mat-option, ng-container\\\"></ng-content>\\n\", styles: [\".mat-optgroup-label{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup-label[disabled]{cursor:default}[dir=rtl] .mat-optgroup-label{text-align:right}.mat-optgroup-label .mat-icon{margin-right:16px;vertical-align:middle}.mat-optgroup-label .mat-icon svg{vertical-align:top}[dir=rtl] .mat-optgroup-label .mat-icon{margin-left:16px;margin-right:0}\\n\"] }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Option IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueIdCounter = 0;\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n    constructor(\n    /** Reference to the option that emitted the event. */\n    source, \n    /** Whether the change in the option's value was a result of a user action. */\n    isUserInput = false) {\n        this.source = source;\n        this.isUserInput = isUserInput;\n    }\n}\nclass _MatOptionBase {\n    constructor(_element, _changeDetectorRef, _parent, group) {\n        this._element = _element;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._parent = _parent;\n        this.group = group;\n        this._selected = false;\n        this._active = false;\n        this._disabled = false;\n        this._mostRecentViewValue = '';\n        /** The unique ID of the option. */\n        this.id = `mat-option-${_uniqueIdCounter++}`;\n        /** Event emitted when the option is selected or deselected. */\n        // tslint:disable-next-line:no-output-on-prefix\n        this.onSelectionChange = new EventEmitter();\n        /** Emits when the state of the option changes and any parents have to be notified. */\n        this._stateChanges = new Subject();\n    }\n    /** Whether the wrapping component is in multiple selection mode. */\n    get multiple() {\n        return this._parent && this._parent.multiple;\n    }\n    /** Whether or not the option is currently selected. */\n    get selected() {\n        return this._selected;\n    }\n    /** Whether the option is disabled. */\n    get disabled() {\n        return (this.group && this.group.disabled) || this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    /** Whether ripples for the option are disabled. */\n    get disableRipple() {\n        return !!(this._parent && this._parent.disableRipple);\n    }\n    /**\n     * Whether or not the option is currently active and ready to be selected.\n     * An active option displays styles as if it is focused, but the\n     * focus is actually retained somewhere else. This comes in handy\n     * for components like autocomplete where focus must remain on the input.\n     */\n    get active() {\n        return this._active;\n    }\n    /**\n     * The displayed value of the option. It is necessary to show the selected option in the\n     * select's trigger.\n     */\n    get viewValue() {\n        // TODO(kara): Add input property alternative for node envs.\n        return (this._getHostElement().textContent || '').trim();\n    }\n    /** Selects the option. */\n    select() {\n        if (!this._selected) {\n            this._selected = true;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent();\n        }\n    }\n    /** Deselects the option. */\n    deselect() {\n        if (this._selected) {\n            this._selected = false;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent();\n        }\n    }\n    /** Sets focus onto this option. */\n    focus(_origin, options) {\n        // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n        // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n        const element = this._getHostElement();\n        if (typeof element.focus === 'function') {\n            element.focus(options);\n        }\n    }\n    /**\n     * This method sets display styles on the option to make it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setActiveStyles() {\n        if (!this._active) {\n            this._active = true;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * This method removes display styles on the option that made it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setInactiveStyles() {\n        if (this._active) {\n            this._active = false;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n        return this.viewValue;\n    }\n    /** Ensures the option is selected when activated from the keyboard. */\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n            this._selectViaInteraction();\n            // Prevent the page from scrolling down and form submits.\n            event.preventDefault();\n        }\n    }\n    /**\n     * `Selects the option while indicating the selection came from the user. Used to\n     * determine if the select's view -> model callback should be invoked.`\n     */\n    _selectViaInteraction() {\n        if (!this.disabled) {\n            this._selected = this.multiple ? !this._selected : true;\n            this._changeDetectorRef.markForCheck();\n            this._emitSelectionChangeEvent(true);\n        }\n    }\n    /**\n     * Gets the `aria-selected` value for the option. We explicitly omit the `aria-selected`\n     * attribute from single-selection, unselected options. Including the `aria-selected=\"false\"`\n     * attributes adds a significant amount of noise to screen-reader users without providing useful\n     * information.\n     */\n    _getAriaSelected() {\n        return this.selected || (this.multiple ? false : null);\n    }\n    /** Returns the correct tabindex for the option depending on disabled state. */\n    _getTabIndex() {\n        return this.disabled ? '-1' : '0';\n    }\n    /** Gets the host DOM element. */\n    _getHostElement() {\n        return this._element.nativeElement;\n    }\n    ngAfterViewChecked() {\n        // Since parent components could be using the option's label to display the selected values\n        // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n        // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n        // relatively cheap, however we still limit them only to selected options in order to avoid\n        // hitting the DOM too often.\n        if (this._selected) {\n            const viewValue = this.viewValue;\n            if (viewValue !== this._mostRecentViewValue) {\n                this._mostRecentViewValue = viewValue;\n                this._stateChanges.next();\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    /** Emits the selection change event. */\n    _emitSelectionChangeEvent(isUserInput = false) {\n        this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n    }\n}\n_MatOptionBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatOptionBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive });\n_MatOptionBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: _MatOptionBase, inputs: { value: \"value\", id: \"id\", disabled: \"disabled\" }, outputs: { onSelectionChange: \"onSelectionChange\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatOptionBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined }, { type: _MatOptgroupBase }]; }, propDecorators: { value: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], onSelectionChange: [{\n                type: Output\n            }] } });\n/**\n * Single option inside of a `<mat-select>` element.\n */\nclass MatOption extends _MatOptionBase {\n    constructor(element, changeDetectorRef, parent, group) {\n        super(element, changeDetectorRef, parent, group);\n    }\n}\nMatOption.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatOption, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_OPTION_PARENT_COMPONENT, optional: true }, { token: MAT_OPTGROUP, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatOption.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatOption, selector: \"mat-option\", host: { attributes: { \"role\": \"option\" }, listeners: { \"click\": \"_selectViaInteraction()\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.tabindex\": \"_getTabIndex()\", \"class.mat-selected\": \"selected\", \"class.mat-option-multiple\": \"multiple\", \"class.mat-active\": \"active\", \"id\": \"id\", \"attr.aria-selected\": \"_getAriaSelected()\", \"attr.aria-disabled\": \"disabled.toString()\", \"class.mat-option-disabled\": \"disabled\" }, classAttribute: \"mat-option mat-focus-indicator\" }, exportAs: [\"matOption\"], usesInheritance: true, ngImport: i0, template: \"<mat-pseudo-checkbox *ngIf=\\\"multiple\\\" class=\\\"mat-option-pseudo-checkbox\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\" [disabled]=\\\"disabled\\\"></mat-pseudo-checkbox>\\n\\n<span class=\\\"mat-option-text\\\"><ng-content></ng-content></span>\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n<span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"group && group._inert\\\">({{ group.label }})</span>\\n\\n<div class=\\\"mat-option-ripple\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\"\\n     [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-option{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;position:relative;cursor:pointer;outline:none;display:flex;flex-direction:row;max-width:100%;box-sizing:border-box;align-items:center;-webkit-tap-highlight-color:transparent}.mat-option[disabled]{cursor:default}[dir=rtl] .mat-option{text-align:right}.mat-option .mat-icon{margin-right:16px;vertical-align:middle}.mat-option .mat-icon svg{vertical-align:top}[dir=rtl] .mat-option .mat-icon{margin-left:16px;margin-right:0}.mat-option[aria-disabled=true]{-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:32px}[dir=rtl] .mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:16px;padding-right:32px}.cdk-high-contrast-active .mat-option{margin:0 1px}.cdk-high-contrast-active .mat-option.mat-active{border:solid 1px currentColor;margin:0}.cdk-high-contrast-active .mat-option[aria-disabled=true]{opacity:.5}.mat-option-text{display:inline-block;flex-grow:1;overflow:hidden;text-overflow:ellipsis}.mat-option .mat-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-option-pseudo-checkbox{margin-right:8px}[dir=rtl] .mat-option-pseudo-checkbox{margin-left:8px;margin-right:0}\\n\"], components: [{ type: MatPseudoCheckbox, selector: \"mat-pseudo-checkbox\", inputs: [\"state\", \"disabled\"] }], directives: [{ type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-option', exportAs: 'matOption', host: {\n                        'role': 'option',\n                        '[attr.tabindex]': '_getTabIndex()',\n                        '[class.mat-selected]': 'selected',\n                        '[class.mat-option-multiple]': 'multiple',\n                        '[class.mat-active]': 'active',\n                        '[id]': 'id',\n                        '[attr.aria-selected]': '_getAriaSelected()',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[class.mat-option-disabled]': 'disabled',\n                        '(click)': '_selectViaInteraction()',\n                        '(keydown)': '_handleKeydown($event)',\n                        'class': 'mat-option mat-focus-indicator',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<mat-pseudo-checkbox *ngIf=\\\"multiple\\\" class=\\\"mat-option-pseudo-checkbox\\\"\\n    [state]=\\\"selected ? 'checked' : 'unchecked'\\\" [disabled]=\\\"disabled\\\"></mat-pseudo-checkbox>\\n\\n<span class=\\\"mat-option-text\\\"><ng-content></ng-content></span>\\n\\n<!-- See a11y notes inside optgroup.ts for context behind this element. -->\\n<span class=\\\"cdk-visually-hidden\\\" *ngIf=\\\"group && group._inert\\\">({{ group.label }})</span>\\n\\n<div class=\\\"mat-option-ripple\\\" mat-ripple\\n     [matRippleTrigger]=\\\"_getHostElement()\\\"\\n     [matRippleDisabled]=\\\"disabled || disableRipple\\\">\\n</div>\\n\", styles: [\".mat-option{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;line-height:48px;height:48px;padding:0 16px;text-align:left;text-decoration:none;max-width:100%;position:relative;cursor:pointer;outline:none;display:flex;flex-direction:row;max-width:100%;box-sizing:border-box;align-items:center;-webkit-tap-highlight-color:transparent}.mat-option[disabled]{cursor:default}[dir=rtl] .mat-option{text-align:right}.mat-option .mat-icon{margin-right:16px;vertical-align:middle}.mat-option .mat-icon svg{vertical-align:top}[dir=rtl] .mat-option .mat-icon{margin-left:16px;margin-right:0}.mat-option[aria-disabled=true]{-webkit-user-select:none;user-select:none;cursor:default}.mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:32px}[dir=rtl] .mat-optgroup .mat-option:not(.mat-option-multiple){padding-left:16px;padding-right:32px}.cdk-high-contrast-active .mat-option{margin:0 1px}.cdk-high-contrast-active .mat-option.mat-active{border:solid 1px currentColor;margin:0}.cdk-high-contrast-active .mat-option[aria-disabled=true]{opacity:.5}.mat-option-text{display:inline-block;flex-grow:1;overflow:hidden;text-overflow:ellipsis}.mat-option .mat-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-option-pseudo-checkbox{margin-right:8px}[dir=rtl] .mat-option-pseudo-checkbox{margin-left:8px;margin-right:0}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_OPTION_PARENT_COMPONENT]\n                }] }, { type: MatOptgroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_OPTGROUP]\n                }] }]; } });\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n    if (optionGroups.length) {\n        let optionsArray = options.toArray();\n        let groups = optionGroups.toArray();\n        let groupCounter = 0;\n        for (let i = 0; i < optionIndex + 1; i++) {\n            if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n                groupCounter++;\n            }\n        }\n        return groupCounter;\n    }\n    return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n    if (optionOffset < currentScrollPosition) {\n        return optionOffset;\n    }\n    if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n        return Math.max(0, optionOffset - panelHeight + optionHeight);\n    }\n    return currentScrollPosition;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatOptionModule {\n}\nMatOptionModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatOptionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatOptionModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatOptionModule, declarations: [MatOption, MatOptgroup], imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule], exports: [MatOption, MatOptgroup] });\nMatOptionModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatOptionModule, imports: [[MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatOptionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatRippleModule, CommonModule, MatCommonModule, MatPseudoCheckboxModule],\n                    exports: [MatOption, MatOptgroup],\n                    declarations: [MatOption, MatOptgroup],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDurations, DateAdapter, ErrorStateMatcher, MATERIAL_SANITY_CHECKS, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_DATE_LOCALE_FACTORY, MAT_NATIVE_DATE_FORMATS, MAT_OPTGROUP, MAT_OPTION_PARENT_COMPONENT, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatLine, MatLineModule, MatNativeDateModule, MatOptgroup, MatOption, MatOptionModule, MatOptionSelectionChange, MatPseudoCheckbox, MatPseudoCheckboxModule, MatRipple, MatRippleModule, NativeDateAdapter, NativeDateModule, RippleRef, RippleRenderer, ShowOnDirtyErrorStateMatcher, VERSION, _MatOptgroupBase, _MatOptionBase, _countGroupLabelsBeforeOption, _getOptionScrollPosition, defaultRippleAnimationConfig, mixinColor, mixinDisableRipple, mixinDisabled, mixinErrorState, mixinInitialized, mixinTabIndex, setLines };\n"]}, "metadata": {}, "sourceType": "module"}
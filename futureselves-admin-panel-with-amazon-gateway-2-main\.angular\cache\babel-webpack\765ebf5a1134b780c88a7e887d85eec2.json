{"ast": null, "code": "import { Observable } from '../Observable';\nimport { async as asyncScheduler } from '../scheduler/async';\nimport { isScheduler } from '../util/isScheduler';\nimport { isValidDate } from '../util/isDate';\nexport function timer(dueTime = 0, intervalOrScheduler, scheduler = asyncScheduler) {\n  let intervalDuration = -1;\n\n  if (intervalOrScheduler != null) {\n    if (isScheduler(intervalOrScheduler)) {\n      scheduler = intervalOrScheduler;\n    } else {\n      intervalDuration = intervalOrScheduler;\n    }\n  }\n\n  return new Observable(subscriber => {\n    let due = isValidDate(dueTime) ? +dueTime - scheduler.now() : dueTime;\n\n    if (due < 0) {\n      due = 0;\n    }\n\n    let n = 0;\n    return scheduler.schedule(function () {\n      if (!subscriber.closed) {\n        subscriber.next(n++);\n\n        if (0 <= intervalDuration) {\n          this.schedule(undefined, intervalDuration);\n        } else {\n          subscriber.complete();\n        }\n      }\n    }, due);\n  });\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/observable/timer.js"], "names": ["Observable", "async", "asyncScheduler", "isScheduler", "isValidDate", "timer", "dueTime", "intervalOrScheduler", "scheduler", "intervalDuration", "subscriber", "due", "now", "n", "schedule", "closed", "next", "undefined", "complete"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,KAAK,IAAIC,cAAlB,QAAwC,oBAAxC;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,SAASC,WAAT,QAA4B,gBAA5B;AACA,OAAO,SAASC,KAAT,CAAeC,OAAO,GAAG,CAAzB,EAA4BC,mBAA5B,EAAiDC,SAAS,GAAGN,cAA7D,EAA6E;AAChF,MAAIO,gBAAgB,GAAG,CAAC,CAAxB;;AACA,MAAIF,mBAAmB,IAAI,IAA3B,EAAiC;AAC7B,QAAIJ,WAAW,CAACI,mBAAD,CAAf,EAAsC;AAClCC,MAAAA,SAAS,GAAGD,mBAAZ;AACH,KAFD,MAGK;AACDE,MAAAA,gBAAgB,GAAGF,mBAAnB;AACH;AACJ;;AACD,SAAO,IAAIP,UAAJ,CAAgBU,UAAD,IAAgB;AAClC,QAAIC,GAAG,GAAGP,WAAW,CAACE,OAAD,CAAX,GAAuB,CAACA,OAAD,GAAWE,SAAS,CAACI,GAAV,EAAlC,GAAoDN,OAA9D;;AACA,QAAIK,GAAG,GAAG,CAAV,EAAa;AACTA,MAAAA,GAAG,GAAG,CAAN;AACH;;AACD,QAAIE,CAAC,GAAG,CAAR;AACA,WAAOL,SAAS,CAACM,QAAV,CAAmB,YAAY;AAClC,UAAI,CAACJ,UAAU,CAACK,MAAhB,EAAwB;AACpBL,QAAAA,UAAU,CAACM,IAAX,CAAgBH,CAAC,EAAjB;;AACA,YAAI,KAAKJ,gBAAT,EAA2B;AACvB,eAAKK,QAAL,CAAcG,SAAd,EAAyBR,gBAAzB;AACH,SAFD,MAGK;AACDC,UAAAA,UAAU,CAACQ,QAAX;AACH;AACJ;AACJ,KAVM,EAUJP,GAVI,CAAP;AAWH,GAjBM,CAAP;AAkBH", "sourcesContent": ["import { Observable } from '../Observable';\nimport { async as asyncScheduler } from '../scheduler/async';\nimport { isScheduler } from '../util/isScheduler';\nimport { isValidDate } from '../util/isDate';\nexport function timer(dueTime = 0, intervalOrScheduler, scheduler = asyncScheduler) {\n    let intervalDuration = -1;\n    if (intervalOrScheduler != null) {\n        if (isScheduler(intervalOrScheduler)) {\n            scheduler = intervalOrScheduler;\n        }\n        else {\n            intervalDuration = intervalOrScheduler;\n        }\n    }\n    return new Observable((subscriber) => {\n        let due = isValidDate(dueTime) ? +dueTime - scheduler.now() : dueTime;\n        if (due < 0) {\n            due = 0;\n        }\n        let n = 0;\n        return scheduler.schedule(function () {\n            if (!subscriber.closed) {\n                subscriber.next(n++);\n                if (0 <= intervalDuration) {\n                    this.schedule(undefined, intervalDuration);\n                }\n                else {\n                    subscriber.complete();\n                }\n            }\n        }, due);\n    });\n}\n"]}, "metadata": {}, "sourceType": "module"}
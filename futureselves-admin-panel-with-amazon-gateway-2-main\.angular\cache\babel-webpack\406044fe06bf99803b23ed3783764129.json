{"ast": null, "code": "import * as i7 from '@angular/cdk/overlay';\nimport { Overlay, CdkConnectedOverlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i8 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, EventEmitter, Optional, Inject, Self, Attribute, ViewChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, mixinDisabled, mixinErrorState, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, take, filter, map, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\n\nconst _c0 = [\"trigger\"];\nconst _c1 = [\"panel\"];\n\nfunction MatSelect_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.placeholder);\n  }\n}\n\nfunction MatSelect_span_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.triggerValue);\n  }\n}\n\nfunction MatSelect_span_5_ng_content_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 0, [\"*ngSwitchCase\", \"true\"]);\n  }\n}\n\nfunction MatSelect_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtemplate(1, MatSelect_span_5_span_1_Template, 2, 1, \"span\", 10);\n    i0.ɵɵtemplate(2, MatSelect_span_5_ng_content_2_Template, 1, 0, \"ng-content\", 11);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngSwitch\", !!ctx_r3.customTrigger);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitchCase\", true);\n  }\n}\n\nfunction MatSelect_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelementStart(1, \"div\", 14, 15);\n    i0.ɵɵlistener(\"@transformPanel.done\", function MatSelect_ng_template_8_Template_div_animation_transformPanel_done_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return ctx_r8._panelDoneAnimatingStream.next($event.toState);\n    })(\"keydown\", function MatSelect_ng_template_8_Template_div_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return ctx_r10._handleKeydown($event);\n    });\n    i0.ɵɵprojection(3, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@transformPanelWrap\", undefined);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMapInterpolate1(\"mat-select-panel \", ctx_r4._getPanelTheme(), \"\");\n    i0.ɵɵstyleProp(\"transform-origin\", ctx_r4._transformOrigin)(\"font-size\", ctx_r4._triggerFontSize, \"px\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.panelClass)(\"@transformPanel\", ctx_r4.multiple ? \"showing-multiple\" : \"showing\");\n    i0.ɵɵattribute(\"id\", ctx_r4.id + \"-panel\")(\"aria-multiselectable\", ctx_r4.multiple)(\"aria-label\", ctx_r4.ariaLabel || null)(\"aria-labelledby\", ctx_r4._getPanelAriaLabelledby());\n  }\n}\n\nconst _c2 = [[[\"mat-select-trigger\"]], \"*\"];\nconst _c3 = [\"mat-select-trigger\", \"*\"];\nconst matSelectAnimations = {\n  /**\n   * This animation ensures the select's overlay panel animation (transformPanel) is called when\n   * closing the select.\n   * This is needed due to https://github.com/angular/angular/issues/23302\n   */\n  transformPanelWrap: trigger('transformPanelWrap', [transition('* => void', query('@transformPanel', [animateChild()], {\n    optional: true\n  }))]),\n\n  /**\n   * This animation transforms the select's overlay panel on and off the page.\n   *\n   * When the panel is attached to the DOM, it expands its width by the amount of padding, scales it\n   * up to 100% on the Y axis, fades in its border, and translates slightly up and to the\n   * side to ensure the option text correctly overlaps the trigger text.\n   *\n   * When the panel is removed from the DOM, it simply fades out linearly.\n   */\n  transformPanel: trigger('transformPanel', [state('void', style({\n    transform: 'scaleY(0.8)',\n    minWidth: '100%',\n    opacity: 0\n  })), state('showing', style({\n    opacity: 1,\n    minWidth: 'calc(100% + 32px)',\n    transform: 'scaleY(1)'\n  })), state('showing-multiple', style({\n    opacity: 1,\n    minWidth: 'calc(100% + 64px)',\n    transform: 'scaleY(1)'\n  })), transition('void => *', animate('120ms cubic-bezier(0, 0, 0.2, 1)')), transition('* => void', animate('100ms 25ms linear', style({\n    opacity: 0\n  })))])\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\n\nfunction getMatSelectDynamicMultipleError() {\n  return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\n\n\nfunction getMatSelectNonArrayValueError() {\n  return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\n\n\nfunction getMatSelectNonFunctionValueError() {\n  return Error('`compareWith` must be a function.');\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet nextUniqueId = 0;\n/**\n * The following style constants are necessary to save here in order\n * to properly calculate the alignment of the selected option over\n * the trigger element.\n */\n\n/** The max height of the select's overlay panel. */\n\nconst SELECT_PANEL_MAX_HEIGHT = 256;\n/** The panel's padding on the x-axis. */\n\nconst SELECT_PANEL_PADDING_X = 16;\n/** The panel's x axis padding if it is indented (e.g. there is an option group). */\n\nconst SELECT_PANEL_INDENT_PADDING_X = SELECT_PANEL_PADDING_X * 2;\n/** The height of the select items in `em` units. */\n\nconst SELECT_ITEM_HEIGHT_EM = 3; // TODO(josephperrott): Revert to a constant after 2018 spec updates are fully merged.\n\n/**\n * Distance between the panel edge and the option text in\n * multi-selection mode.\n *\n * Calculated as:\n * (SELECT_PANEL_PADDING_X * 1.5) + 16 = 40\n * The padding is multiplied by 1.5 because the checkbox's margin is half the padding.\n * The checkbox width is 16px.\n */\n\nconst SELECT_MULTIPLE_PANEL_PADDING_X = SELECT_PANEL_PADDING_X * 1.5 + 16;\n/**\n * The select panel will only \"fit\" inside the viewport if it is positioned at\n * this value or more away from the viewport boundary.\n */\n\nconst SELECT_PANEL_VIEWPORT_PADDING = 8;\n/** Injection token that determines the scroll handling while a select is open. */\n\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy');\n/** @docs-private */\n\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\n\n\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\n\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n  provide: MAT_SELECT_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/** Change event object that is emitted when the select value has changed. */\n\nclass MatSelectChange {\n  constructor(\n  /** Reference to the select that emitted the change event. */\n  source,\n  /** Current value of the select that emitted the event. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n\n} // Boilerplate for applying mixins to MatSelect.\n\n/** @docs-private */\n\n\nconst _MatSelectMixinBase = mixinDisableRipple(mixinTabIndex(mixinDisabled(mixinErrorState(class {\n  constructor(_elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl) {\n    this._elementRef = _elementRef;\n    this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n    this._parentForm = _parentForm;\n    this._parentFormGroup = _parentFormGroup;\n    this.ngControl = ngControl;\n  }\n\n}))));\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\n\nclass MatSelectTrigger {}\n\nMatSelectTrigger.ɵfac = function MatSelectTrigger_Factory(t) {\n  return new (t || MatSelectTrigger)();\n};\n\nMatSelectTrigger.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatSelectTrigger,\n  selectors: [[\"mat-select-trigger\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_SELECT_TRIGGER,\n    useExisting: MatSelectTrigger\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectTrigger, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-select-trigger',\n      providers: [{\n        provide: MAT_SELECT_TRIGGER,\n        useExisting: MatSelectTrigger\n      }]\n    }]\n  }], null, null);\n})();\n/** Base class with all of the `MatSelect` functionality. */\n\n\nclass _MatSelectBase extends _MatSelectMixinBase {\n  constructor(_viewportRuler, _changeDetectorRef, _ngZone, _defaultErrorStateMatcher, elementRef, _dir, _parentForm, _parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n    super(elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n    this._viewportRuler = _viewportRuler;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._ngZone = _ngZone;\n    this._dir = _dir;\n    this._parentFormField = _parentFormField;\n    this._liveAnnouncer = _liveAnnouncer;\n    this._defaultOptions = _defaultOptions;\n    /** Whether or not the overlay panel is open. */\n\n    this._panelOpen = false;\n    /** Comparison function to specify which option is displayed. Defaults to object equality. */\n\n    this._compareWith = (o1, o2) => o1 === o2;\n    /** Unique id for this input. */\n\n\n    this._uid = `mat-select-${nextUniqueId++}`;\n    /** Current `ariar-labelledby` value for the select trigger. */\n\n    this._triggerAriaLabelledBy = null;\n    /** Emits whenever the component is destroyed. */\n\n    this._destroy = new Subject();\n    /** `View -> model callback called when value changes` */\n\n    this._onChange = () => {};\n    /** `View -> model callback called when select has been touched` */\n\n\n    this._onTouched = () => {};\n    /** ID for the DOM node containing the select's value. */\n\n\n    this._valueId = `mat-select-value-${nextUniqueId++}`;\n    /** Emits when the panel element is finished transforming in. */\n\n    this._panelDoneAnimatingStream = new Subject();\n    this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n    this._focused = false;\n    /** A name for this control that can be used by `mat-form-field`. */\n\n    this.controlType = 'mat-select';\n    this._multiple = false;\n    this._disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n    /** Aria label of the select. */\n\n    this.ariaLabel = '';\n    /** Combined stream of all of the child options' change events. */\n\n    this.optionSelectionChanges = defer(() => {\n      const options = this.options;\n\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n\n      return this._ngZone.onStable.pipe(take(1), switchMap(() => this.optionSelectionChanges));\n    });\n    /** Event emitted when the select panel has been toggled. */\n\n    this.openedChange = new EventEmitter();\n    /** Event emitted when the select has been opened. */\n\n    this._openedStream = this.openedChange.pipe(filter(o => o), map(() => {}));\n    /** Event emitted when the select has been closed. */\n\n    this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => {}));\n    /** Event emitted when the selected value has been changed by the user. */\n\n    this.selectionChange = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the select changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n\n    this.valueChange = new EventEmitter();\n\n    if (this.ngControl) {\n      // Note: we provide the value accessor through here, instead of\n      // the `providers` to avoid running into a circular import.\n      this.ngControl.valueAccessor = this;\n    } // Note that we only want to set this when the defaults pass it in, otherwise it should\n    // stay as `undefined` so that it falls back to the default in the key manager.\n\n\n    if (_defaultOptions?.typeaheadDebounceInterval != null) {\n      this._typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n    }\n\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this._scrollStrategy = this._scrollStrategyFactory();\n    this.tabIndex = parseInt(tabIndex) || 0; // Force setter to be called in case id was not specified.\n\n    this.id = this.id;\n  }\n  /** Whether the select is focused. */\n\n\n  get focused() {\n    return this._focused || this._panelOpen;\n  }\n  /** Placeholder to be shown if no value has been selected. */\n\n\n  get placeholder() {\n    return this._placeholder;\n  }\n\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  /** Whether the component is required. */\n\n\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n    this.stateChanges.next();\n  }\n  /** Whether the user should be allowed to select multiple options. */\n\n\n  get multiple() {\n    return this._multiple;\n  }\n\n  set multiple(value) {\n    if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectDynamicMultipleError();\n    }\n\n    this._multiple = coerceBooleanProperty(value);\n  }\n  /** Whether to center the active option over the trigger. */\n\n\n  get disableOptionCentering() {\n    return this._disableOptionCentering;\n  }\n\n  set disableOptionCentering(value) {\n    this._disableOptionCentering = coerceBooleanProperty(value);\n  }\n  /**\n   * Function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n\n\n  get compareWith() {\n    return this._compareWith;\n  }\n\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatSelectNonFunctionValueError();\n    }\n\n    this._compareWith = fn;\n\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /** Value of the select control. */\n\n\n  get value() {\n    return this._value;\n  }\n\n  set value(newValue) {\n    const hasAssigned = this._assignValue(newValue);\n\n    if (hasAssigned) {\n      this._onChange(newValue);\n    }\n  }\n  /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n\n\n  get typeaheadDebounceInterval() {\n    return this._typeaheadDebounceInterval;\n  }\n\n  set typeaheadDebounceInterval(value) {\n    this._typeaheadDebounceInterval = coerceNumberProperty(value);\n  }\n  /** Unique id of the element. */\n\n\n  get id() {\n    return this._id;\n  }\n\n  set id(value) {\n    this._id = value || this._uid;\n    this.stateChanges.next();\n  }\n\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple);\n    this.stateChanges.next(); // We need `distinctUntilChanged` here, because some browsers will\n    // fire the animation end event twice for the same animation. See:\n    // https://github.com/angular/angular/issues/24084\n\n    this._panelDoneAnimatingStream.pipe(distinctUntilChanged(), takeUntil(this._destroy)).subscribe(() => this._panelDoneAnimating(this.panelOpen));\n  }\n\n  ngAfterContentInit() {\n    this._initKeyManager();\n\n    this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n      event.added.forEach(option => option.select());\n      event.removed.forEach(option => option.deselect());\n    });\n\n    this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n      this._resetOptions();\n\n      this._initializeSelection();\n    });\n  }\n\n  ngDoCheck() {\n    const newAriaLabelledby = this._getTriggerAriaLabelledby();\n\n    const ngControl = this.ngControl; // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n    // is computed as a result of a content query which can cause this binding to trigger a\n    // \"changed after checked\" error.\n\n    if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n      const element = this._elementRef.nativeElement;\n      this._triggerAriaLabelledBy = newAriaLabelledby;\n\n      if (newAriaLabelledby) {\n        element.setAttribute('aria-labelledby', newAriaLabelledby);\n      } else {\n        element.removeAttribute('aria-labelledby');\n      }\n    }\n\n    if (ngControl) {\n      // The disabled state might go out of sync if the form group is swapped out. See #17860.\n      if (this._previousControl !== ngControl.control) {\n        if (this._previousControl !== undefined && ngControl.disabled !== null && ngControl.disabled !== this.disabled) {\n          this.disabled = ngControl.disabled;\n        }\n\n        this._previousControl = ngControl.control;\n      }\n\n      this.updateErrorState();\n    }\n  }\n\n  ngOnChanges(changes) {\n    // Updating the disabled state is handled by `mixinDisabled`, but we need to additionally let\n    // the parent form field know to run change detection when the disabled state changes.\n    if (changes['disabled']) {\n      this.stateChanges.next();\n    }\n\n    if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n      this._keyManager.withTypeAhead(this._typeaheadDebounceInterval);\n    }\n  }\n\n  ngOnDestroy() {\n    this._destroy.next();\n\n    this._destroy.complete();\n\n    this.stateChanges.complete();\n  }\n  /** Toggles the overlay panel open or closed. */\n\n\n  toggle() {\n    this.panelOpen ? this.close() : this.open();\n  }\n  /** Opens the overlay panel. */\n\n\n  open() {\n    if (this._canOpen()) {\n      this._panelOpen = true;\n\n      this._keyManager.withHorizontalOrientation(null);\n\n      this._highlightCorrectOption();\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Closes the overlay panel and focuses the host element. */\n\n\n  close() {\n    if (this._panelOpen) {\n      this._panelOpen = false;\n\n      this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n\n      this._changeDetectorRef.markForCheck();\n\n      this._onTouched();\n    }\n  }\n  /**\n   * Sets the select's value. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param value New value to be written to the model.\n   */\n\n\n  writeValue(value) {\n    this._assignValue(value);\n  }\n  /**\n   * Saves a callback function to be invoked when the select's value\n   * changes from user input. Part of the ControlValueAccessor interface\n   * required to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the value changes.\n   */\n\n\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Saves a callback function to be invoked when the select is blurred\n   * by the user. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param fn Callback to be triggered when the component has been touched.\n   */\n\n\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Disables the select. Part of the ControlValueAccessor interface required\n   * to integrate with Angular's core forms API.\n   *\n   * @param isDisabled Sets whether the component is disabled.\n   */\n\n\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n\n    this._changeDetectorRef.markForCheck();\n\n    this.stateChanges.next();\n  }\n  /** Whether or not the overlay panel is open. */\n\n\n  get panelOpen() {\n    return this._panelOpen;\n  }\n  /** The currently selected option. */\n\n\n  get selected() {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n  /** The value displayed in the trigger. */\n\n\n  get triggerValue() {\n    if (this.empty) {\n      return '';\n    }\n\n    if (this._multiple) {\n      const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n\n      if (this._isRtl()) {\n        selectedOptions.reverse();\n      } // TODO(crisbeto): delimiter should be configurable for proper localization.\n\n\n      return selectedOptions.join(', ');\n    }\n\n    return this._selectionModel.selected[0].viewValue;\n  }\n  /** Whether the element is in RTL mode. */\n\n\n  _isRtl() {\n    return this._dir ? this._dir.value === 'rtl' : false;\n  }\n  /** Handles all keydown events on the select. */\n\n\n  _handleKeydown(event) {\n    if (!this.disabled) {\n      this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n    }\n  }\n  /** Handles keyboard events while the select is closed. */\n\n\n  _handleClosedKeydown(event) {\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW || keyCode === LEFT_ARROW || keyCode === RIGHT_ARROW;\n    const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n    const manager = this._keyManager; // Open the select on ALT + arrow key to match the native <select>\n\n    if (!manager.isTyping() && isOpenKey && !hasModifierKey(event) || (this.multiple || event.altKey) && isArrowKey) {\n      event.preventDefault(); // prevents the page from scrolling down when pressing space\n\n      this.open();\n    } else if (!this.multiple) {\n      const previouslySelectedOption = this.selected;\n      manager.onKeydown(event);\n      const selectedOption = this.selected; // Since the value has changed, we need to announce it ourselves.\n\n      if (selectedOption && previouslySelectedOption !== selectedOption) {\n        // We set a duration on the live announcement, because we want the live element to be\n        // cleared after a while so that users can't navigate to it using the arrow keys.\n        this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n      }\n    }\n  }\n  /** Handles keyboard events when the selected is open. */\n\n\n  _handleOpenKeydown(event) {\n    const manager = this._keyManager;\n    const keyCode = event.keyCode;\n    const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n    const isTyping = manager.isTyping();\n\n    if (isArrowKey && event.altKey) {\n      // Close the select on ALT + arrow key to match the native <select>\n      event.preventDefault();\n      this.close(); // Don't do anything in this case if the user is typing,\n      // because the typing sequence can include the space key.\n    } else if (!isTyping && (keyCode === ENTER || keyCode === SPACE) && manager.activeItem && !hasModifierKey(event)) {\n      event.preventDefault();\n\n      manager.activeItem._selectViaInteraction();\n    } else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n      event.preventDefault();\n      const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n      this.options.forEach(option => {\n        if (!option.disabled) {\n          hasDeselectedOptions ? option.select() : option.deselect();\n        }\n      });\n    } else {\n      const previouslyFocusedIndex = manager.activeItemIndex;\n      manager.onKeydown(event);\n\n      if (this._multiple && isArrowKey && event.shiftKey && manager.activeItem && manager.activeItemIndex !== previouslyFocusedIndex) {\n        manager.activeItem._selectViaInteraction();\n      }\n    }\n  }\n\n  _onFocus() {\n    if (!this.disabled) {\n      this._focused = true;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n   * \"blur\" to the panel when it opens, causing a false positive.\n   */\n\n\n  _onBlur() {\n    this._focused = false;\n\n    if (!this.disabled && !this.panelOpen) {\n      this._onTouched();\n\n      this._changeDetectorRef.markForCheck();\n\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Callback that is invoked when the overlay panel has been attached.\n   */\n\n\n  _onAttached() {\n    this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n      this._changeDetectorRef.detectChanges();\n\n      this._positioningSettled();\n    });\n  }\n  /** Returns the theme to be used on the panel. */\n\n\n  _getPanelTheme() {\n    return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n  }\n  /** Whether the select has a value. */\n\n\n  get empty() {\n    return !this._selectionModel || this._selectionModel.isEmpty();\n  }\n\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl) {\n        this._value = this.ngControl.value;\n      }\n\n      this._setSelectionByValue(this._value);\n\n      this.stateChanges.next();\n    });\n  }\n  /**\n   * Sets the selected option based on a value. If no option can be\n   * found with the designated value, the select trigger is cleared.\n   */\n\n\n  _setSelectionByValue(value) {\n    this._selectionModel.selected.forEach(option => option.setInactiveStyles());\n\n    this._selectionModel.clear();\n\n    if (this.multiple && value) {\n      if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatSelectNonArrayValueError();\n      }\n\n      value.forEach(currentValue => this._selectOptionByValue(currentValue));\n\n      this._sortValues();\n    } else {\n      const correspondingOption = this._selectOptionByValue(value); // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what option the user interacted with last.\n\n\n      if (correspondingOption) {\n        this._keyManager.updateActiveItem(correspondingOption);\n      } else if (!this.panelOpen) {\n        // Otherwise reset the highlighted option. Note that we only want to do this while\n        // closed, because doing it while open can shift the user's focus unnecessarily.\n        this._keyManager.updateActiveItem(-1);\n      }\n    }\n\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Finds and selects and option based on its value.\n   * @returns Option that has the corresponding value.\n   */\n\n\n  _selectOptionByValue(value) {\n    const correspondingOption = this.options.find(option => {\n      // Skip options that are already in the model. This allows us to handle cases\n      // where the same primitive value is selected multiple times.\n      if (this._selectionModel.isSelected(option)) {\n        return false;\n      }\n\n      try {\n        // Treat null as a special reset value.\n        return option.value != null && this._compareWith(option.value, value);\n      } catch (error) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          // Notify developers of errors in their comparator.\n          console.warn(error);\n        }\n\n        return false;\n      }\n    });\n\n    if (correspondingOption) {\n      this._selectionModel.select(correspondingOption);\n    }\n\n    return correspondingOption;\n  }\n  /** Assigns a specific value to the select. Returns whether the value has changed. */\n\n\n  _assignValue(newValue) {\n    // Always re-assign an array, because it might have been mutated.\n    if (newValue !== this._value || this._multiple && Array.isArray(newValue)) {\n      if (this.options) {\n        this._setSelectionByValue(newValue);\n      }\n\n      this._value = newValue;\n      return true;\n    }\n\n    return false;\n  }\n  /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n\n\n  _initKeyManager() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withTypeAhead(this._typeaheadDebounceInterval).withVerticalOrientation().withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr').withHomeAndEnd().withAllowedModifierKeys(['shiftKey']);\n\n    this._keyManager.tabOut.pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this.panelOpen) {\n        // Select the active item when tabbing away. This is consistent with how the native\n        // select behaves. Note that we only want to do this in single selection mode.\n        if (!this.multiple && this._keyManager.activeItem) {\n          this._keyManager.activeItem._selectViaInteraction();\n        } // Restore focus to the trigger before closing. Ensures that the focus\n        // position won't be lost if the user got focus into the overlay.\n\n\n        this.focus();\n        this.close();\n      }\n    });\n\n    this._keyManager.change.pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this._panelOpen && this.panel) {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n      } else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n        this._keyManager.activeItem._selectViaInteraction();\n      }\n    });\n  }\n  /** Drops current option subscriptions and IDs and resets from scratch. */\n\n\n  _resetOptions() {\n    const changedOrDestroyed = merge(this.options.changes, this._destroy);\n    this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n      this._onSelect(event.source, event.isUserInput);\n\n      if (event.isUserInput && !this.multiple && this._panelOpen) {\n        this.close();\n        this.focus();\n      }\n    }); // Listen to changes in the internal state of the options and react accordingly.\n    // Handles cases like the labels of the selected options changing.\n\n    merge(...this.options.map(option => option._stateChanges)).pipe(takeUntil(changedOrDestroyed)).subscribe(() => {\n      this._changeDetectorRef.markForCheck();\n\n      this.stateChanges.next();\n    });\n  }\n  /** Invoked when an option is clicked. */\n\n\n  _onSelect(option, isUserInput) {\n    const wasSelected = this._selectionModel.isSelected(option);\n\n    if (option.value == null && !this._multiple) {\n      option.deselect();\n\n      this._selectionModel.clear();\n\n      if (this.value != null) {\n        this._propagateChanges(option.value);\n      }\n    } else {\n      if (wasSelected !== option.selected) {\n        option.selected ? this._selectionModel.select(option) : this._selectionModel.deselect(option);\n      }\n\n      if (isUserInput) {\n        this._keyManager.setActiveItem(option);\n      }\n\n      if (this.multiple) {\n        this._sortValues();\n\n        if (isUserInput) {\n          // In case the user selected the option with their mouse, we\n          // want to restore focus back to the trigger, in order to\n          // prevent the select keyboard controls from clashing with\n          // the ones from `mat-option`.\n          this.focus();\n        }\n      }\n    }\n\n    if (wasSelected !== this._selectionModel.isSelected(option)) {\n      this._propagateChanges();\n    }\n\n    this.stateChanges.next();\n  }\n  /** Sorts the selected values in the selected based on their order in the panel. */\n\n\n  _sortValues() {\n    if (this.multiple) {\n      const options = this.options.toArray();\n\n      this._selectionModel.sort((a, b) => {\n        return this.sortComparator ? this.sortComparator(a, b, options) : options.indexOf(a) - options.indexOf(b);\n      });\n\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n\n\n  _propagateChanges(fallbackValue) {\n    let valueToEmit = null;\n\n    if (this.multiple) {\n      valueToEmit = this.selected.map(option => option.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n\n    this._value = valueToEmit;\n    this.valueChange.emit(valueToEmit);\n\n    this._onChange(valueToEmit);\n\n    this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Highlights the selected item. If no option is selected, it will highlight\n   * the first item instead.\n   */\n\n\n  _highlightCorrectOption() {\n    if (this._keyManager) {\n      if (this.empty) {\n        this._keyManager.setFirstItemActive();\n      } else {\n        this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n      }\n    }\n  }\n  /** Whether the panel is allowed to open. */\n\n\n  _canOpen() {\n    return !this._panelOpen && !this.disabled && this.options?.length > 0;\n  }\n  /** Focuses the select element. */\n\n\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Gets the aria-labelledby for the select panel. */\n\n\n  _getPanelAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n\n    const labelId = this._parentFormField?.getLabelId();\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Determines the `aria-activedescendant` to be set on the host. */\n\n\n  _getAriaActiveDescendant() {\n    if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n      return this._keyManager.activeItem.id;\n    }\n\n    return null;\n  }\n  /** Gets the aria-labelledby of the select component trigger. */\n\n\n  _getTriggerAriaLabelledby() {\n    if (this.ariaLabel) {\n      return null;\n    }\n\n    const labelId = this._parentFormField?.getLabelId();\n    let value = (labelId ? labelId + ' ' : '') + this._valueId;\n\n    if (this.ariaLabelledby) {\n      value += ' ' + this.ariaLabelledby;\n    }\n\n    return value;\n  }\n  /** Called when the overlay panel is done animating. */\n\n\n  _panelDoneAnimating(isOpen) {\n    this.openedChange.emit(isOpen);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  setDescribedByIds(ids) {\n    this._ariaDescribedby = ids.join(' ');\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  onContainerClick() {\n    this.focus();\n    this.open();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get shouldLabelFloat() {\n    return this._panelOpen || !this.empty || this._focused && !!this._placeholder;\n  }\n\n}\n\n_MatSelectBase.ɵfac = function _MatSelectBase_Factory(t) {\n  return new (t || _MatSelectBase)(i0.ɵɵdirectiveInject(i1.ViewportRuler), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.ErrorStateMatcher), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i4.NgForm, 8), i0.ɵɵdirectiveInject(i4.FormGroupDirective, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8), i0.ɵɵdirectiveInject(i4.NgControl, 10), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_SELECT_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.LiveAnnouncer), i0.ɵɵdirectiveInject(MAT_SELECT_CONFIG, 8));\n};\n\n_MatSelectBase.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MatSelectBase,\n  viewQuery: function _MatSelectBase_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n      i0.ɵɵviewQuery(CdkConnectedOverlay, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trigger = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._overlayDir = _t.first);\n    }\n  },\n  inputs: {\n    panelClass: \"panelClass\",\n    placeholder: \"placeholder\",\n    required: \"required\",\n    multiple: \"multiple\",\n    disableOptionCentering: \"disableOptionCentering\",\n    compareWith: \"compareWith\",\n    value: \"value\",\n    ariaLabel: [\"aria-label\", \"ariaLabel\"],\n    ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n    errorStateMatcher: \"errorStateMatcher\",\n    typeaheadDebounceInterval: \"typeaheadDebounceInterval\",\n    sortComparator: \"sortComparator\",\n    id: \"id\"\n  },\n  outputs: {\n    openedChange: \"openedChange\",\n    _openedStream: \"opened\",\n    _closedStream: \"closed\",\n    selectionChange: \"selectionChange\",\n    valueChange: \"valueChange\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSelectBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i1.ViewportRuler\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.ErrorStateMatcher\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i4.NgForm,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i4.FormGroupDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i6.MatFormField,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_FORM_FIELD]\n      }]\n    }, {\n      type: i4.NgControl,\n      decorators: [{\n        type: Self\n      }, {\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SELECT_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i5.LiveAnnouncer\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_SELECT_CONFIG]\n      }]\n    }];\n  }, {\n    trigger: [{\n      type: ViewChild,\n      args: ['trigger']\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    _overlayDir: [{\n      type: ViewChild,\n      args: [CdkConnectedOverlay]\n    }],\n    panelClass: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }],\n    disableOptionCentering: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    typeaheadDebounceInterval: [{\n      type: Input\n    }],\n    sortComparator: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    openedChange: [{\n      type: Output\n    }],\n    _openedStream: [{\n      type: Output,\n      args: ['opened']\n    }],\n    _closedStream: [{\n      type: Output,\n      args: ['closed']\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }]\n  });\n})();\n\nclass MatSelect extends _MatSelectBase {\n  constructor() {\n    super(...arguments);\n    /** The scroll position of the overlay panel, calculated to center the selected option. */\n\n    this._scrollTop = 0;\n    /** The cached font-size of the trigger element. */\n\n    this._triggerFontSize = 0;\n    /** The value of the select panel's transform-origin property. */\n\n    this._transformOrigin = 'top';\n    /**\n     * The y-offset of the overlay panel in relation to the trigger's top start corner.\n     * This must be adjusted to align the selected option text over the trigger text.\n     * when the panel opens. Will change based on the y-position of the selected option.\n     */\n\n    this._offsetY = 0;\n    this._positions = [{\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'bottom'\n    }];\n  }\n  /**\n   * Calculates the scroll position of the select's overlay panel.\n   *\n   * Attempts to center the selected option in the panel. If the option is\n   * too high or too low in the panel to be scrolled to the center, it clamps the\n   * scroll position to the min or max scroll positions respectively.\n   */\n\n\n  _calculateOverlayScroll(selectedIndex, scrollBuffer, maxScroll) {\n    const itemHeight = this._getItemHeight();\n\n    const optionOffsetFromScrollTop = itemHeight * selectedIndex;\n    const halfOptionHeight = itemHeight / 2; // Starts at the optionOffsetFromScrollTop, which scrolls the option to the top of the\n    // scroll container, then subtracts the scroll buffer to scroll the option down to\n    // the center of the overlay panel. Half the option height must be re-added to the\n    // scrollTop so the option is centered based on its middle, not its top edge.\n\n    const optimalScrollPosition = optionOffsetFromScrollTop - scrollBuffer + halfOptionHeight;\n    return Math.min(Math.max(0, optimalScrollPosition), maxScroll);\n  }\n\n  ngOnInit() {\n    super.ngOnInit();\n\n    this._viewportRuler.change().pipe(takeUntil(this._destroy)).subscribe(() => {\n      if (this.panelOpen) {\n        this._triggerRect = this.trigger.nativeElement.getBoundingClientRect();\n\n        this._changeDetectorRef.markForCheck();\n      }\n    });\n  }\n\n  open() {\n    if (super._canOpen()) {\n      super.open();\n      this._triggerRect = this.trigger.nativeElement.getBoundingClientRect(); // Note: The computed font-size will be a string pixel value (e.g. \"16px\").\n      // `parseInt` ignores the trailing 'px' and converts this to a number.\n\n      this._triggerFontSize = parseInt(getComputedStyle(this.trigger.nativeElement).fontSize || '0');\n\n      this._calculateOverlayPosition(); // Set the font size on the panel element once it exists.\n\n\n      this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n        if (this._triggerFontSize && this._overlayDir.overlayRef && this._overlayDir.overlayRef.overlayElement) {\n          this._overlayDir.overlayRef.overlayElement.style.fontSize = `${this._triggerFontSize}px`;\n        }\n      });\n    }\n  }\n  /** Scrolls the active option into view. */\n\n\n  _scrollOptionIntoView(index) {\n    const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n\n    const itemHeight = this._getItemHeight();\n\n    if (index === 0 && labelCount === 1) {\n      // If we've got one group label before the option and we're at the top option,\n      // scroll the list to the top. This is better UX than scrolling the list to the\n      // top of the option, because it allows the user to read the top group's label.\n      this.panel.nativeElement.scrollTop = 0;\n    } else {\n      this.panel.nativeElement.scrollTop = _getOptionScrollPosition((index + labelCount) * itemHeight, itemHeight, this.panel.nativeElement.scrollTop, SELECT_PANEL_MAX_HEIGHT);\n    }\n  }\n\n  _positioningSettled() {\n    this._calculateOverlayOffsetX();\n\n    this.panel.nativeElement.scrollTop = this._scrollTop;\n  }\n\n  _panelDoneAnimating(isOpen) {\n    if (this.panelOpen) {\n      this._scrollTop = 0;\n    } else {\n      this._overlayDir.offsetX = 0;\n\n      this._changeDetectorRef.markForCheck();\n    }\n\n    super._panelDoneAnimating(isOpen);\n  }\n\n  _getChangeEvent(value) {\n    return new MatSelectChange(this, value);\n  }\n  /**\n   * Sets the x-offset of the overlay panel in relation to the trigger's top start corner.\n   * This must be adjusted to align the selected option text over the trigger text when\n   * the panel opens. Will change based on LTR or RTL text direction. Note that the offset\n   * can't be calculated until the panel has been attached, because we need to know the\n   * content width in order to constrain the panel within the viewport.\n   */\n\n\n  _calculateOverlayOffsetX() {\n    const overlayRect = this._overlayDir.overlayRef.overlayElement.getBoundingClientRect();\n\n    const viewportSize = this._viewportRuler.getViewportSize();\n\n    const isRtl = this._isRtl();\n\n    const paddingWidth = this.multiple ? SELECT_MULTIPLE_PANEL_PADDING_X + SELECT_PANEL_PADDING_X : SELECT_PANEL_PADDING_X * 2;\n    let offsetX; // Adjust the offset, depending on the option padding.\n\n    if (this.multiple) {\n      offsetX = SELECT_MULTIPLE_PANEL_PADDING_X;\n    } else if (this.disableOptionCentering) {\n      offsetX = SELECT_PANEL_PADDING_X;\n    } else {\n      let selected = this._selectionModel.selected[0] || this.options.first;\n      offsetX = selected && selected.group ? SELECT_PANEL_INDENT_PADDING_X : SELECT_PANEL_PADDING_X;\n    } // Invert the offset in LTR.\n\n\n    if (!isRtl) {\n      offsetX *= -1;\n    } // Determine how much the select overflows on each side.\n\n\n    const leftOverflow = 0 - (overlayRect.left + offsetX - (isRtl ? paddingWidth : 0));\n    const rightOverflow = overlayRect.right + offsetX - viewportSize.width + (isRtl ? 0 : paddingWidth); // If the element overflows on either side, reduce the offset to allow it to fit.\n\n    if (leftOverflow > 0) {\n      offsetX += leftOverflow + SELECT_PANEL_VIEWPORT_PADDING;\n    } else if (rightOverflow > 0) {\n      offsetX -= rightOverflow + SELECT_PANEL_VIEWPORT_PADDING;\n    } // Set the offset directly in order to avoid having to go through change detection and\n    // potentially triggering \"changed after it was checked\" errors. Round the value to avoid\n    // blurry content in some browsers.\n\n\n    this._overlayDir.offsetX = Math.round(offsetX);\n\n    this._overlayDir.overlayRef.updatePosition();\n  }\n  /**\n   * Calculates the y-offset of the select's overlay panel in relation to the\n   * top start corner of the trigger. It has to be adjusted in order for the\n   * selected option to be aligned over the trigger when the panel opens.\n   */\n\n\n  _calculateOverlayOffsetY(selectedIndex, scrollBuffer, maxScroll) {\n    const itemHeight = this._getItemHeight();\n\n    const optionHeightAdjustment = (itemHeight - this._triggerRect.height) / 2;\n    const maxOptionsDisplayed = Math.floor(SELECT_PANEL_MAX_HEIGHT / itemHeight);\n    let optionOffsetFromPanelTop; // Disable offset if requested by user by returning 0 as value to offset\n\n    if (this.disableOptionCentering) {\n      return 0;\n    }\n\n    if (this._scrollTop === 0) {\n      optionOffsetFromPanelTop = selectedIndex * itemHeight;\n    } else if (this._scrollTop === maxScroll) {\n      const firstDisplayedIndex = this._getItemCount() - maxOptionsDisplayed;\n      const selectedDisplayIndex = selectedIndex - firstDisplayedIndex; // The first item is partially out of the viewport. Therefore we need to calculate what\n      // portion of it is shown in the viewport and account for it in our offset.\n\n      let partialItemHeight = itemHeight - (this._getItemCount() * itemHeight - SELECT_PANEL_MAX_HEIGHT) % itemHeight; // Because the panel height is longer than the height of the options alone,\n      // there is always extra padding at the top or bottom of the panel. When\n      // scrolled to the very bottom, this padding is at the top of the panel and\n      // must be added to the offset.\n\n      optionOffsetFromPanelTop = selectedDisplayIndex * itemHeight + partialItemHeight;\n    } else {\n      // If the option was scrolled to the middle of the panel using a scroll buffer,\n      // its offset will be the scroll buffer minus the half height that was added to\n      // center it.\n      optionOffsetFromPanelTop = scrollBuffer - itemHeight / 2;\n    } // The final offset is the option's offset from the top, adjusted for the height difference,\n    // multiplied by -1 to ensure that the overlay moves in the correct direction up the page.\n    // The value is rounded to prevent some browsers from blurring the content.\n\n\n    return Math.round(optionOffsetFromPanelTop * -1 - optionHeightAdjustment);\n  }\n  /**\n   * Checks that the attempted overlay position will fit within the viewport.\n   * If it will not fit, tries to adjust the scroll position and the associated\n   * y-offset so the panel can open fully on-screen. If it still won't fit,\n   * sets the offset back to 0 to allow the fallback position to take over.\n   */\n\n\n  _checkOverlayWithinViewport(maxScroll) {\n    const itemHeight = this._getItemHeight();\n\n    const viewportSize = this._viewportRuler.getViewportSize();\n\n    const topSpaceAvailable = this._triggerRect.top - SELECT_PANEL_VIEWPORT_PADDING;\n    const bottomSpaceAvailable = viewportSize.height - this._triggerRect.bottom - SELECT_PANEL_VIEWPORT_PADDING;\n    const panelHeightTop = Math.abs(this._offsetY);\n    const totalPanelHeight = Math.min(this._getItemCount() * itemHeight, SELECT_PANEL_MAX_HEIGHT);\n    const panelHeightBottom = totalPanelHeight - panelHeightTop - this._triggerRect.height;\n\n    if (panelHeightBottom > bottomSpaceAvailable) {\n      this._adjustPanelUp(panelHeightBottom, bottomSpaceAvailable);\n    } else if (panelHeightTop > topSpaceAvailable) {\n      this._adjustPanelDown(panelHeightTop, topSpaceAvailable, maxScroll);\n    } else {\n      this._transformOrigin = this._getOriginBasedOnOption();\n    }\n  }\n  /** Adjusts the overlay panel up to fit in the viewport. */\n\n\n  _adjustPanelUp(panelHeightBottom, bottomSpaceAvailable) {\n    // Browsers ignore fractional scroll offsets, so we need to round.\n    const distanceBelowViewport = Math.round(panelHeightBottom - bottomSpaceAvailable); // Scrolls the panel up by the distance it was extending past the boundary, then\n    // adjusts the offset by that amount to move the panel up into the viewport.\n\n    this._scrollTop -= distanceBelowViewport;\n    this._offsetY -= distanceBelowViewport;\n    this._transformOrigin = this._getOriginBasedOnOption(); // If the panel is scrolled to the very top, it won't be able to fit the panel\n    // by scrolling, so set the offset to 0 to allow the fallback position to take\n    // effect.\n\n    if (this._scrollTop <= 0) {\n      this._scrollTop = 0;\n      this._offsetY = 0;\n      this._transformOrigin = `50% bottom 0px`;\n    }\n  }\n  /** Adjusts the overlay panel down to fit in the viewport. */\n\n\n  _adjustPanelDown(panelHeightTop, topSpaceAvailable, maxScroll) {\n    // Browsers ignore fractional scroll offsets, so we need to round.\n    const distanceAboveViewport = Math.round(panelHeightTop - topSpaceAvailable); // Scrolls the panel down by the distance it was extending past the boundary, then\n    // adjusts the offset by that amount to move the panel down into the viewport.\n\n    this._scrollTop += distanceAboveViewport;\n    this._offsetY += distanceAboveViewport;\n    this._transformOrigin = this._getOriginBasedOnOption(); // If the panel is scrolled to the very bottom, it won't be able to fit the\n    // panel by scrolling, so set the offset to 0 to allow the fallback position\n    // to take effect.\n\n    if (this._scrollTop >= maxScroll) {\n      this._scrollTop = maxScroll;\n      this._offsetY = 0;\n      this._transformOrigin = `50% top 0px`;\n      return;\n    }\n  }\n  /** Calculates the scroll position and x- and y-offsets of the overlay panel. */\n\n\n  _calculateOverlayPosition() {\n    const itemHeight = this._getItemHeight();\n\n    const items = this._getItemCount();\n\n    const panelHeight = Math.min(items * itemHeight, SELECT_PANEL_MAX_HEIGHT);\n    const scrollContainerHeight = items * itemHeight; // The farthest the panel can be scrolled before it hits the bottom\n\n    const maxScroll = scrollContainerHeight - panelHeight; // If no value is selected we open the popup to the first item.\n\n    let selectedOptionOffset;\n\n    if (this.empty) {\n      selectedOptionOffset = 0;\n    } else {\n      selectedOptionOffset = Math.max(this.options.toArray().indexOf(this._selectionModel.selected[0]), 0);\n    }\n\n    selectedOptionOffset += _countGroupLabelsBeforeOption(selectedOptionOffset, this.options, this.optionGroups); // We must maintain a scroll buffer so the selected option will be scrolled to the\n    // center of the overlay panel rather than the top.\n\n    const scrollBuffer = panelHeight / 2;\n    this._scrollTop = this._calculateOverlayScroll(selectedOptionOffset, scrollBuffer, maxScroll);\n    this._offsetY = this._calculateOverlayOffsetY(selectedOptionOffset, scrollBuffer, maxScroll);\n\n    this._checkOverlayWithinViewport(maxScroll);\n  }\n  /** Sets the transform origin point based on the selected option. */\n\n\n  _getOriginBasedOnOption() {\n    const itemHeight = this._getItemHeight();\n\n    const optionHeightAdjustment = (itemHeight - this._triggerRect.height) / 2;\n    const originY = Math.abs(this._offsetY) - optionHeightAdjustment + itemHeight / 2;\n    return `50% ${originY}px 0px`;\n  }\n  /** Calculates the height of the select's options. */\n\n\n  _getItemHeight() {\n    return this._triggerFontSize * SELECT_ITEM_HEIGHT_EM;\n  }\n  /** Calculates the amount of items in the select. This includes options and group labels. */\n\n\n  _getItemCount() {\n    return this.options.length + this.optionGroups.length;\n  }\n\n}\n\nMatSelect.ɵfac = /* @__PURE__ */function () {\n  let ɵMatSelect_BaseFactory;\n  return function MatSelect_Factory(t) {\n    return (ɵMatSelect_BaseFactory || (ɵMatSelect_BaseFactory = i0.ɵɵgetInheritedFactory(MatSelect)))(t || MatSelect);\n  };\n}();\n\nMatSelect.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatSelect,\n  selectors: [[\"mat-select\"]],\n  contentQueries: function MatSelect_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_SELECT_TRIGGER, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customTrigger = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n    }\n  },\n  hostAttrs: [\"role\", \"combobox\", \"aria-autocomplete\", \"none\", \"aria-haspopup\", \"true\", 1, \"mat-select\"],\n  hostVars: 20,\n  hostBindings: function MatSelect_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function MatSelect_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"focus\", function MatSelect_focus_HostBindingHandler() {\n        return ctx._onFocus();\n      })(\"blur\", function MatSelect_blur_HostBindingHandler() {\n        return ctx._onBlur();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.id)(\"tabindex\", ctx.tabIndex)(\"aria-controls\", ctx.panelOpen ? ctx.id + \"-panel\" : null)(\"aria-expanded\", ctx.panelOpen)(\"aria-label\", ctx.ariaLabel || null)(\"aria-required\", ctx.required.toString())(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-describedby\", ctx._ariaDescribedby || null)(\"aria-activedescendant\", ctx._getAriaActiveDescendant());\n      i0.ɵɵclassProp(\"mat-select-disabled\", ctx.disabled)(\"mat-select-invalid\", ctx.errorState)(\"mat-select-required\", ctx.required)(\"mat-select-empty\", ctx.empty)(\"mat-select-multiple\", ctx.multiple);\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    disableRipple: \"disableRipple\",\n    tabIndex: \"tabIndex\"\n  },\n  exportAs: [\"matSelect\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MatFormFieldControl,\n    useExisting: MatSelect\n  }, {\n    provide: MAT_OPTION_PARENT_COMPONENT,\n    useExisting: MatSelect\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c3,\n  decls: 9,\n  vars: 12,\n  consts: [[\"cdk-overlay-origin\", \"\", 1, \"mat-select-trigger\", 3, \"click\"], [\"origin\", \"cdkOverlayOrigin\", \"trigger\", \"\"], [1, \"mat-select-value\", 3, \"ngSwitch\"], [\"class\", \"mat-select-placeholder mat-select-min-line\", 4, \"ngSwitchCase\"], [\"class\", \"mat-select-value-text\", 3, \"ngSwitch\", 4, \"ngSwitchCase\"], [1, \"mat-select-arrow-wrapper\"], [1, \"mat-select-arrow\"], [\"cdk-connected-overlay\", \"\", \"cdkConnectedOverlayLockPosition\", \"\", \"cdkConnectedOverlayHasBackdrop\", \"\", \"cdkConnectedOverlayBackdropClass\", \"cdk-overlay-transparent-backdrop\", 3, \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayOffsetY\", \"backdropClick\", \"attach\", \"detach\"], [1, \"mat-select-placeholder\", \"mat-select-min-line\"], [1, \"mat-select-value-text\", 3, \"ngSwitch\"], [\"class\", \"mat-select-min-line\", 4, \"ngSwitchDefault\"], [4, \"ngSwitchCase\"], [1, \"mat-select-min-line\"], [1, \"mat-select-panel-wrap\"], [\"role\", \"listbox\", \"tabindex\", \"-1\", 3, \"ngClass\", \"keydown\"], [\"panel\", \"\"]],\n  template: function MatSelect_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c2);\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"click\", function MatSelect_Template_div_click_0_listener() {\n        return ctx.toggle();\n      });\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵtemplate(4, MatSelect_span_4_Template, 2, 1, \"span\", 3);\n      i0.ɵɵtemplate(5, MatSelect_span_5_Template, 3, 2, \"span\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"div\", 5);\n      i0.ɵɵelement(7, \"div\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(8, MatSelect_ng_template_8_Template, 4, 14, \"ng-template\", 7);\n      i0.ɵɵlistener(\"backdropClick\", function MatSelect_Template_ng_template_backdropClick_8_listener() {\n        return ctx.close();\n      })(\"attach\", function MatSelect_Template_ng_template_attach_8_listener() {\n        return ctx._onAttached();\n      })(\"detach\", function MatSelect_Template_ng_template_detach_8_listener() {\n        return ctx.close();\n      });\n    }\n\n    if (rf & 2) {\n      const _r0 = i0.ɵɵreference(1);\n\n      i0.ɵɵattribute(\"aria-owns\", ctx.panelOpen ? ctx.id + \"-panel\" : null);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngSwitch\", ctx.empty);\n      i0.ɵɵattribute(\"id\", ctx._valueId);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", true);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", false);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"cdkConnectedOverlayPanelClass\", ctx._overlayPanelClass)(\"cdkConnectedOverlayScrollStrategy\", ctx._scrollStrategy)(\"cdkConnectedOverlayOrigin\", _r0)(\"cdkConnectedOverlayOpen\", ctx.panelOpen)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayMinWidth\", ctx._triggerRect == null ? null : ctx._triggerRect.width)(\"cdkConnectedOverlayOffsetY\", ctx._offsetY);\n    }\n  },\n  directives: [i7.CdkOverlayOrigin, i8.NgSwitch, i8.NgSwitchCase, i7.CdkConnectedOverlay, i8.NgSwitchDefault, i8.NgClass],\n  styles: [\".mat-select{display:inline-block;width:100%;outline:none}.mat-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-select-disabled .mat-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-select-arrow-wrapper{height:16px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-select-arrow-wrapper{transform:translateY(-50%)}.mat-form-field-appearance-outline .mat-select-arrow-wrapper{transform:translateY(-25%)}.mat-form-field-appearance-standard.mat-form-field-has-label .mat-select:not(.mat-select-empty) .mat-select-arrow-wrapper{transform:translateY(-50%)}.mat-form-field-appearance-standard .mat-select.mat-select-empty .mat-select-arrow-wrapper{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable.mat-form-field-appearance-standard .mat-select.mat-select-empty .mat-select-arrow-wrapper{transition:none}.mat-select-arrow{width:0;height:0;border-left:5px solid transparent;border-right:5px solid transparent;border-top:5px solid;margin:0 4px}.mat-form-field.mat-focused .mat-select-arrow{transform:translateX(0)}.mat-select-panel-wrap{flex-basis:100%}.mat-select-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;padding-top:0;padding-bottom:0;max-height:256px;min-width:100%;border-radius:4px;outline:0}.cdk-high-contrast-active .mat-select-panel{outline:solid 1px}.mat-select-panel .mat-optgroup-label,.mat-select-panel .mat-option{font-size:inherit;line-height:3em;height:3em}.mat-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-form-field-flex{cursor:pointer}.mat-form-field-type-mat-select .mat-form-field-label{width:calc(100% - 18px)}.mat-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable .mat-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-select-placeholder{color:transparent;-webkit-text-fill-color:transparent;transition:none;display:block}.mat-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [matSelectAnimations.transformPanelWrap, matSelectAnimations.transformPanel]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelect, [{\n    type: Component,\n    args: [{\n      selector: 'mat-select',\n      exportAs: 'matSelect',\n      inputs: ['disabled', 'disableRipple', 'tabIndex'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'role': 'combobox',\n        'aria-autocomplete': 'none',\n        // TODO(crisbeto): the value for aria-haspopup should be `listbox`, but currently it's difficult\n        // to sync into Google, because of an outdated automated a11y check which flags it as an invalid\n        // value. At some point we should try to switch it back to being `listbox`.\n        'aria-haspopup': 'true',\n        'class': 'mat-select',\n        '[attr.id]': 'id',\n        '[attr.tabindex]': 'tabIndex',\n        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n        '[attr.aria-expanded]': 'panelOpen',\n        '[attr.aria-label]': 'ariaLabel || null',\n        '[attr.aria-required]': 'required.toString()',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-describedby]': '_ariaDescribedby || null',\n        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n        '[class.mat-select-disabled]': 'disabled',\n        '[class.mat-select-invalid]': 'errorState',\n        '[class.mat-select-required]': 'required',\n        '[class.mat-select-empty]': 'empty',\n        '[class.mat-select-multiple]': 'multiple',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': '_onFocus()',\n        '(blur)': '_onBlur()'\n      },\n      animations: [matSelectAnimations.transformPanelWrap, matSelectAnimations.transformPanel],\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatSelect\n      }, {\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatSelect\n      }],\n      template: \"<!--\\n Note that the select trigger element specifies `aria-owns` pointing to the listbox overlay.\\n While aria-owns is not required for the ARIA 1.2 `role=\\\"combobox\\\"` interaction pattern,\\n it fixes an issue with VoiceOver when the select appears inside of an `aria-model=\\\"true\\\"`\\n element (e.g. a dialog). Without this `aria-owns`, the `aria-modal` on a dialog prevents\\n VoiceOver from \\\"seeing\\\" the select's listbox overlay for aria-activedescendant.\\n Using `aria-owns` re-parents the select overlay so that it works again.\\n See https://github.com/angular/components/issues/20694\\n-->\\n<div cdk-overlay-origin\\n     [attr.aria-owns]=\\\"panelOpen ? id + '-panel' : null\\\"\\n     class=\\\"mat-select-trigger\\\"\\n     (click)=\\\"toggle()\\\"\\n     #origin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n  <div class=\\\"mat-select-value\\\" [ngSwitch]=\\\"empty\\\" [attr.id]=\\\"_valueId\\\">\\n    <span class=\\\"mat-select-placeholder mat-select-min-line\\\" *ngSwitchCase=\\\"true\\\">{{placeholder}}</span>\\n    <span class=\\\"mat-select-value-text\\\" *ngSwitchCase=\\\"false\\\" [ngSwitch]=\\\"!!customTrigger\\\">\\n      <span class=\\\"mat-select-min-line\\\" *ngSwitchDefault>{{triggerValue}}</span>\\n      <ng-content select=\\\"mat-select-trigger\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n    </span>\\n  </div>\\n\\n  <div class=\\\"mat-select-arrow-wrapper\\\"><div class=\\\"mat-select-arrow\\\"></div></div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"origin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayMinWidth]=\\\"_triggerRect?.width!\\\"\\n  [cdkConnectedOverlayOffsetY]=\\\"_offsetY\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div class=\\\"mat-select-panel-wrap\\\" [@transformPanelWrap]>\\n    <div\\n      #panel\\n      role=\\\"listbox\\\"\\n      tabindex=\\\"-1\\\"\\n      class=\\\"mat-select-panel {{ _getPanelTheme() }}\\\"\\n      [attr.id]=\\\"id + '-panel'\\\"\\n      [attr.aria-multiselectable]=\\\"multiple\\\"\\n      [attr.aria-label]=\\\"ariaLabel || null\\\"\\n      [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n      [ngClass]=\\\"panelClass\\\"\\n      [@transformPanel]=\\\"multiple ? 'showing-multiple' : 'showing'\\\"\\n      (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n      [style.transformOrigin]=\\\"_transformOrigin\\\"\\n      [style.font-size.px]=\\\"_triggerFontSize\\\"\\n      (keydown)=\\\"_handleKeydown($event)\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\".mat-select{display:inline-block;width:100%;outline:none}.mat-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-select-disabled .mat-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-select-arrow-wrapper{height:16px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-select-arrow-wrapper{transform:translateY(-50%)}.mat-form-field-appearance-outline .mat-select-arrow-wrapper{transform:translateY(-25%)}.mat-form-field-appearance-standard.mat-form-field-has-label .mat-select:not(.mat-select-empty) .mat-select-arrow-wrapper{transform:translateY(-50%)}.mat-form-field-appearance-standard .mat-select.mat-select-empty .mat-select-arrow-wrapper{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable.mat-form-field-appearance-standard .mat-select.mat-select-empty .mat-select-arrow-wrapper{transition:none}.mat-select-arrow{width:0;height:0;border-left:5px solid transparent;border-right:5px solid transparent;border-top:5px solid;margin:0 4px}.mat-form-field.mat-focused .mat-select-arrow{transform:translateX(0)}.mat-select-panel-wrap{flex-basis:100%}.mat-select-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;padding-top:0;padding-bottom:0;max-height:256px;min-width:100%;border-radius:4px;outline:0}.cdk-high-contrast-active .mat-select-panel{outline:solid 1px}.mat-select-panel .mat-optgroup-label,.mat-select-panel .mat-option{font-size:inherit;line-height:3em;height:3em}.mat-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-form-field-flex{cursor:pointer}.mat-form-field-type-mat-select .mat-form-field-label{width:calc(100% - 18px)}.mat-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable .mat-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-select-placeholder{color:transparent;-webkit-text-fill-color:transparent;transition:none;display:block}.mat-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\\n\"]\n    }]\n  }], null, {\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    customTrigger: [{\n      type: ContentChild,\n      args: [MAT_SELECT_TRIGGER]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatSelectModule {}\n\nMatSelectModule.ɵfac = function MatSelectModule_Factory(t) {\n  return new (t || MatSelectModule)();\n};\n\nMatSelectModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatSelectModule\n});\nMatSelectModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n  imports: [[CommonModule, OverlayModule, MatOptionModule, MatCommonModule], CdkScrollableModule, MatFormFieldModule, MatOptionModule, MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSelectModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule],\n      exports: [CdkScrollableModule, MatFormFieldModule, MatSelect, MatSelectTrigger, MatOptionModule, MatCommonModule],\n      declarations: [MatSelect, MatSelectTrigger],\n      providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, _MatSelectBase, matSelectAnimations };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/material/fesm2020/select.mjs"], "names": ["i7", "Overlay", "CdkConnectedOverlay", "OverlayModule", "i8", "CommonModule", "i0", "InjectionToken", "Directive", "EventEmitter", "Optional", "Inject", "Self", "Attribute", "ViewChild", "Input", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "ContentChild", "NgModule", "i2", "mixinDisableRipple", "mixinTabIndex", "mixinDisabled", "mixinErrorState", "_countGroupLabelsBeforeOption", "_getOptionScrollPosition", "MAT_OPTION_PARENT_COMPONENT", "MatOption", "MAT_OPTGROUP", "MatOptionModule", "MatCommonModule", "i6", "MAT_FORM_FIELD", "MatFormFieldControl", "MatFormFieldModule", "i1", "CdkScrollableModule", "i5", "ActiveDescendantKeyManager", "i3", "coerceBooleanProperty", "coerceNumberProperty", "SelectionModel", "DOWN_ARROW", "UP_ARROW", "LEFT_ARROW", "RIGHT_ARROW", "ENTER", "SPACE", "hasModifierKey", "A", "i4", "Validators", "Subject", "defer", "merge", "startWith", "switchMap", "take", "filter", "map", "distinctUntilChanged", "takeUntil", "trigger", "transition", "query", "animate<PERSON><PERSON><PERSON>", "state", "style", "animate", "matSelectAnimations", "transformPanelWrap", "optional", "transformPanel", "transform", "min<PERSON><PERSON><PERSON>", "opacity", "getMatSelectDynamicMultipleError", "Error", "getMatSelectNonArrayValueError", "getMatSelectNonFunctionValueError", "nextUniqueId", "SELECT_PANEL_MAX_HEIGHT", "SELECT_PANEL_PADDING_X", "SELECT_PANEL_INDENT_PADDING_X", "SELECT_ITEM_HEIGHT_EM", "SELECT_MULTIPLE_PANEL_PADDING_X", "SELECT_PANEL_VIEWPORT_PADDING", "MAT_SELECT_SCROLL_STRATEGY", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY", "overlay", "scrollStrategies", "reposition", "MAT_SELECT_CONFIG", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "MatSelectChange", "constructor", "source", "value", "_MatSelectMixinBase", "_elementRef", "_defaultErrorStateMatcher", "_parentForm", "_parentFormGroup", "ngControl", "MAT_SELECT_TRIGGER", "MatSelectTrigger", "ɵfac", "ɵdir", "useExisting", "type", "args", "selector", "providers", "_MatSelectBase", "_viewportRuler", "_changeDetectorRef", "_ngZone", "elementRef", "_dir", "_parentFormField", "tabIndex", "scrollStrategyFactory", "_liveAnnouncer", "_defaultOptions", "_panelOpen", "_compareWith", "o1", "o2", "_uid", "_triggerAriaLabelledBy", "_destroy", "_onChange", "_onTouched", "_valueId", "_panelDoneAnimatingStream", "_overlayPanelClass", "overlayPanelClass", "_focused", "controlType", "_multiple", "_disableOptionCentering", "disableOptionCentering", "aria<PERSON><PERSON><PERSON>", "optionSelectionChanges", "options", "changes", "pipe", "option", "onSelectionChange", "onStable", "openedChange", "_openedStream", "o", "_closedStream", "selectionChange", "valueChange", "valueAccessor", "typeaheadDebounceInterval", "_typeaheadDebounceInterval", "_scrollStrategyFactory", "_scrollStrategy", "parseInt", "id", "focused", "placeholder", "_placeholder", "stateChanges", "next", "required", "_required", "control", "hasValidator", "multiple", "_selectionModel", "ngDevMode", "compareWith", "fn", "_initializeSelection", "_value", "newValue", "hasAssigned", "_assignValue", "_id", "ngOnInit", "subscribe", "_panelDoneAnimating", "panelOpen", "ngAfterContentInit", "_initKeyManager", "changed", "event", "added", "for<PERSON>ach", "select", "removed", "deselect", "_resetOptions", "ngDoCheck", "newAria<PERSON><PERSON><PERSON><PERSON>", "_getTriggerAriaLabe<PERSON>by", "element", "nativeElement", "setAttribute", "removeAttribute", "_previousControl", "undefined", "disabled", "updateErrorState", "ngOnChanges", "_keyManager", "withTypeAhead", "ngOnDestroy", "complete", "toggle", "close", "open", "_canOpen", "withHorizontalOrientation", "_highlightCorrectOption", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_isRtl", "writeValue", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "selected", "triggerValue", "empty", "selectedOptions", "viewValue", "reverse", "join", "_handleKeydown", "_handleOpenKeydown", "_handleClosedKeydown", "keyCode", "isArrowKey", "isOpenKey", "manager", "isTyping", "altKey", "preventDefault", "previouslySelectedOption", "onKeydown", "selectedOption", "announce", "activeItem", "_selectViaInteraction", "ctrl<PERSON>ey", "hasDeselectedOptions", "some", "opt", "previouslyFocusedIndex", "activeItemIndex", "shift<PERSON>ey", "_onFocus", "_onBlur", "_onAttached", "_overlayDir", "positionChange", "detectChanges", "_positioningSettled", "_getPanelTheme", "color", "isEmpty", "Promise", "resolve", "then", "_setSelectionByValue", "setInactiveStyles", "clear", "Array", "isArray", "currentValue", "_selectOptionByValue", "_sortValues", "correspondingOption", "updateActiveItem", "find", "isSelected", "error", "console", "warn", "withVerticalOrientation", "withHomeAndEnd", "withAllowedModifierKeys", "tabOut", "focus", "change", "panel", "_scrollOptionIntoView", "changedOrDestroyed", "_onSelect", "isUserInput", "_stateChanges", "wasSelected", "_propagateChanges", "setActiveItem", "toArray", "sort", "a", "b", "sortComparator", "indexOf", "fallback<PERSON><PERSON><PERSON>", "valueToEmit", "emit", "_getChangeEvent", "setFirstItemActive", "length", "_getPanelAriaLabe<PERSON>by", "labelId", "getLabelId", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getAriaActiveDescendant", "isOpen", "setDescribedByIds", "ids", "_ariaDes<PERSON>by", "onContainerClick", "shouldLabelFloat", "ViewportRuler", "ChangeDetectorRef", "NgZone", "ErrorStateMatcher", "ElementRef", "Directionality", "NgForm", "FormGroupDirective", "NgControl", "LiveAnnouncer", "decorators", "MatFormField", "panelClass", "errorStateMatcher", "MatSelect", "arguments", "_scrollTop", "_triggerFontSize", "_transform<PERSON><PERSON><PERSON>", "_offsetY", "_positions", "originX", "originY", "overlayX", "overlayY", "_calculateOverlayScroll", "selectedIndex", "scrollBuffer", "maxScroll", "itemHeight", "_getItemHeight", "optionOffsetFromScrollTop", "halfOptionHeight", "optimalScrollPosition", "Math", "min", "max", "_triggerRect", "getBoundingClientRect", "getComputedStyle", "fontSize", "_calculateOverlayPosition", "overlayRef", "overlayElement", "index", "labelCount", "optionGroups", "scrollTop", "_calculateOverlayOffsetX", "offsetX", "overlayRect", "viewportSize", "getViewportSize", "isRtl", "paddingWidth", "first", "group", "leftOverflow", "left", "rightOverflow", "right", "width", "round", "updatePosition", "_calculateOverlayOffsetY", "optionHeightAdjustment", "height", "maxOptionsDisplayed", "floor", "optionOffsetFromPanelTop", "firstDisplayedIndex", "_getItemCount", "selectedDisplayIndex", "partialItemHeight", "_checkOverlayWithinViewport", "topSpaceAvailable", "top", "bottomSpaceAvailable", "bottom", "panelHeightTop", "abs", "totalPanelHeight", "panelHeightBottom", "_adjustPanelUp", "_adjustPanelDown", "_getOriginBasedOnOption", "distanceBelowViewport", "distanceAboveViewport", "items", "panelHeight", "scrollContainerHeight", "selectedOptionOffset", "ɵcmp", "CdkOverlayOrigin", "NgSwitch", "NgSwitchCase", "NgSwitchDefault", "Ng<PERSON><PERSON>", "exportAs", "inputs", "encapsulation", "None", "changeDetection", "OnPush", "host", "animations", "template", "styles", "descendants", "customTrigger", "MatSelectModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,sBAApB;AACA,SAASC,OAAT,EAAkBC,mBAAlB,EAAuCC,aAAvC,QAA4D,sBAA5D;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,YAApC,EAAkDC,QAAlD,EAA4DC,MAA5D,EAAoEC,IAApE,EAA0EC,SAA1E,EAAqFC,SAArF,EAAgGC,KAAhG,EAAuGC,MAAvG,EAA+GC,SAA/G,EAA0HC,iBAA1H,EAA6IC,uBAA7I,EAAsKC,eAAtK,EAAuLC,YAAvL,EAAqMC,QAArM,QAAqN,eAArN;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,kBAAT,EAA6BC,aAA7B,EAA4CC,aAA5C,EAA2DC,eAA3D,EAA4EC,6BAA5E,EAA2GC,wBAA3G,EAAqIC,2BAArI,EAAkKC,SAAlK,EAA6KC,YAA7K,EAA2LC,eAA3L,EAA4MC,eAA5M,QAAmO,wBAAnO;AACA,OAAO,KAAKC,EAAZ,MAAoB,8BAApB;AACA,SAASC,cAAT,EAAyBC,mBAAzB,EAA8CC,kBAA9C,QAAwE,8BAAxE;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,mBAAT,QAAoC,wBAApC;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,0BAAT,QAA2C,mBAA3C;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,qBAAT,EAAgCC,oBAAhC,QAA4D,uBAA5D;AACA,SAASC,cAAT,QAA+B,0BAA/B;AACA,SAASC,UAAT,EAAqBC,QAArB,EAA+BC,UAA/B,EAA2CC,WAA3C,EAAwDC,KAAxD,EAA+DC,KAA/D,EAAsEC,cAAtE,EAAsFC,CAAtF,QAA+F,uBAA/F;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,UAAT,QAA2B,gBAA3B;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,QAAsC,MAAtC;AACA,SAASC,SAAT,EAAoBC,SAApB,EAA+BC,IAA/B,EAAqCC,MAArC,EAA6CC,GAA7C,EAAkDC,oBAAlD,EAAwEC,SAAxE,QAAyF,gBAAzF;AACA,SAASC,OAAT,EAAkBC,UAAlB,EAA8BC,KAA9B,EAAqCC,YAArC,EAAmDC,KAAnD,EAA0DC,KAA1D,EAAiEC,OAAjE,QAAgF,qBAAhF;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AA6JmGnE,IAAAA,EAmjC6lC,6B;AAnjC7lCA,IAAAA,EAmjC+qC,U;AAnjC/qCA,IAAAA,EAmjC8rC,e;;;;mBAnjC9rCA,E;AAAAA,IAAAA,EAmjC+qC,a;AAnjC/qCA,IAAAA,EAmjC+qC,sC;;;;;;AAnjC/qCA,IAAAA,EAmjCgzC,8B;AAnjChzCA,IAAAA,EAmjCq2C,U;AAnjCr2CA,IAAAA,EAmjCq3C,e;;;;mBAnjCr3CA,E;AAAAA,IAAAA,EAmjCq2C,a;AAnjCr2CA,IAAAA,EAmjCq2C,uC;;;;;;AAnjCr2CA,IAAAA,EAmjCo4C,8C;;;;;;AAnjCp4CA,IAAAA,EAmjC2sC,6B;AAnjC3sCA,IAAAA,EAmjCgzC,kE;AAnjChzCA,IAAAA,EAmjCo4C,8E;AAnjCp4CA,IAAAA,EAmjCw9C,e;;;;mBAnjCx9CA,E;AAAAA,IAAAA,EAmjCywC,+C;AAnjCzwCA,IAAAA,EAmjC+6C,a;AAnjC/6CA,IAAAA,EAmjC+6C,iC;;;;;;gBAnjC/6CA,E;;AAAAA,IAAAA,EAmjC8rE,6B;AAnjC9rEA,IAAAA,EAmjC+vE,iC;AAnjC/vEA,IAAAA,EAmjCsqF;AAnjCtqFA,MAAAA,EAmjCsqF;AAAA,qBAnjCtqFA,EAmjCsqF;AAAA,aAAyB,qDAAzB;AAAA;AAnjCtqFA,MAAAA,EAmjCsqF;AAAA,sBAnjCtqFA,EAmjCsqF;AAAA,aAAkM,8BAAlM;AAAA,M;AAnjCtqFA,IAAAA,EAmjCy4F,mB;AAnjCz4FA,IAAAA,EAmjCw6F,e;AAnjCx6FA,IAAAA,EAmjCk7F,e;;;;mBAnjCl7FA,E;AAAAA,IAAAA,EAmjCmuE,6C;AAnjCnuEA,IAAAA,EAmjCw0E,a;AAnjCx0EA,IAAAA,EAmjCw0E,yE;AAnjCx0EA,IAAAA,EAmjCuvF,qG;AAnjCvvFA,IAAAA,EAmjC+jF,8G;AAnjC/jFA,IAAAA,EAmjCi4E,8K;;;;;;AA/sCp+E,MAAMoE,mBAAmB,GAAG;AACxB;AACJ;AACA;AACA;AACA;AACIC,EAAAA,kBAAkB,EAAER,OAAO,CAAC,oBAAD,EAAuB,CAC9CC,UAAU,CAAC,WAAD,EAAcC,KAAK,CAAC,iBAAD,EAAoB,CAACC,YAAY,EAAb,CAApB,EAAsC;AAAEM,IAAAA,QAAQ,EAAE;AAAZ,GAAtC,CAAnB,CADoC,CAAvB,CANH;;AASxB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIC,EAAAA,cAAc,EAAEV,OAAO,CAAC,gBAAD,EAAmB,CACtCI,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;AAChBM,IAAAA,SAAS,EAAE,aADK;AAEhBC,IAAAA,QAAQ,EAAE,MAFM;AAGhBC,IAAAA,OAAO,EAAE;AAHO,GAAD,CAAd,CADiC,EAMtCT,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;AACnBQ,IAAAA,OAAO,EAAE,CADU;AAEnBD,IAAAA,QAAQ,EAAE,mBAFS;AAGnBD,IAAAA,SAAS,EAAE;AAHQ,GAAD,CAAjB,CANiC,EAWtCP,KAAK,CAAC,kBAAD,EAAqBC,KAAK,CAAC;AAC5BQ,IAAAA,OAAO,EAAE,CADmB;AAE5BD,IAAAA,QAAQ,EAAE,mBAFkB;AAG5BD,IAAAA,SAAS,EAAE;AAHiB,GAAD,CAA1B,CAXiC,EAgBtCV,UAAU,CAAC,WAAD,EAAcK,OAAO,CAAC,kCAAD,CAArB,CAhB4B,EAiBtCL,UAAU,CAAC,WAAD,EAAcK,OAAO,CAAC,mBAAD,EAAsBD,KAAK,CAAC;AAAEQ,IAAAA,OAAO,EAAE;AAAX,GAAD,CAA3B,CAArB,CAjB4B,CAAnB;AAlBC,CAA5B;AAuCA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,gCAAT,GAA4C;AACxC,SAAOC,KAAK,CAAC,+DAAD,CAAZ;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,8BAAT,GAA0C;AACtC,SAAOD,KAAK,CAAC,oDAAD,CAAZ;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASE,iCAAT,GAA6C;AACzC,SAAOF,KAAK,CAAC,mCAAD,CAAZ;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIG,YAAY,GAAG,CAAnB;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMC,uBAAuB,GAAG,GAAhC;AACA;;AACA,MAAMC,sBAAsB,GAAG,EAA/B;AACA;;AACA,MAAMC,6BAA6B,GAAGD,sBAAsB,GAAG,CAA/D;AACA;;AACA,MAAME,qBAAqB,GAAG,CAA9B,C,CACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,+BAA+B,GAAGH,sBAAsB,GAAG,GAAzB,GAA+B,EAAvE;AACA;AACA;AACA;AACA;;AACA,MAAMI,6BAA6B,GAAG,CAAtC;AACA;;AACA,MAAMC,0BAA0B,GAAG,IAAIrF,cAAJ,CAAmB,4BAAnB,CAAnC;AACA;;AACA,SAASsF,2CAAT,CAAqDC,OAArD,EAA8D;AAC1D,SAAO,MAAMA,OAAO,CAACC,gBAAR,CAAyBC,UAAzB,EAAb;AACH;AACD;;;AACA,MAAMC,iBAAiB,GAAG,IAAI1F,cAAJ,CAAmB,mBAAnB,CAA1B;AACA;;AACA,MAAM2F,mCAAmC,GAAG;AACxCC,EAAAA,OAAO,EAAEP,0BAD+B;AAExCQ,EAAAA,IAAI,EAAE,CAACnG,OAAD,CAFkC;AAGxCoG,EAAAA,UAAU,EAAER;AAH4B,CAA5C;AAKA;;AACA,MAAMS,eAAN,CAAsB;AAClBC,EAAAA,WAAW;AACX;AACAC,EAAAA,MAFW;AAGX;AACAC,EAAAA,KAJW,EAIJ;AACH,SAAKD,MAAL,GAAcA,MAAd;AACA,SAAKC,KAAL,GAAaA,KAAb;AACH;;AARiB,C,CAUtB;;AACA;;;AACA,MAAMC,mBAAmB,GAAGlF,kBAAkB,CAACC,aAAa,CAACC,aAAa,CAACC,eAAe,CAAC,MAAM;AAC7F4E,EAAAA,WAAW,CAACI,WAAD,EAAcC,yBAAd,EAAyCC,WAAzC,EAAsDC,gBAAtD,EAAwEC,SAAxE,EAAmF;AAC1F,SAAKJ,WAAL,GAAmBA,WAAnB;AACA,SAAKC,yBAAL,GAAiCA,yBAAjC;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACH;;AAP4F,CAAP,CAAhB,CAAd,CAAd,CAA9C;AASA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,kBAAkB,GAAG,IAAIzG,cAAJ,CAAmB,kBAAnB,CAA3B;AACA;AACA;AACA;;AACA,MAAM0G,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAACC,IAAjB;AAAA,mBAA6GD,gBAA7G;AAAA;;AACAA,gBAAgB,CAACE,IAAjB,kBADmG7G,EACnG;AAAA,QAAiG2G,gBAAjG;AAAA;AAAA,aADmG3G,EACnG,oBAA8J,CAAC;AAAE6F,IAAAA,OAAO,EAAEa,kBAAX;AAA+BI,IAAAA,WAAW,EAAEH;AAA5C,GAAD,CAA9J;AAAA;;AACA;AAAA,qDAFmG3G,EAEnG,mBAA2F2G,gBAA3F,EAAyH,CAAC;AAC9GI,IAAAA,IAAI,EAAE7G,SADwG;AAE9G8G,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,oBADX;AAECC,MAAAA,SAAS,EAAE,CAAC;AAAErB,QAAAA,OAAO,EAAEa,kBAAX;AAA+BI,QAAAA,WAAW,EAAEH;AAA5C,OAAD;AAFZ,KAAD;AAFwG,GAAD,CAAzH;AAAA;AAOA;;;AACA,MAAMQ,cAAN,SAA6Bf,mBAA7B,CAAiD;AAC7CH,EAAAA,WAAW,CAACmB,cAAD,EAAiBC,kBAAjB,EAAqCC,OAArC,EAA8ChB,yBAA9C,EAAyEiB,UAAzE,EAAqFC,IAArF,EAA2FjB,WAA3F,EAAwGC,gBAAxG,EAA0HiB,gBAA1H,EAA4IhB,SAA5I,EAAuJiB,QAAvJ,EAAiKC,qBAAjK,EAAwLC,cAAxL,EAAwMC,eAAxM,EAAyN;AAChO,UAAMN,UAAN,EAAkBjB,yBAAlB,EAA6CC,WAA7C,EAA0DC,gBAA1D,EAA4EC,SAA5E;AACA,SAAKW,cAAL,GAAsBA,cAAtB;AACA,SAAKC,kBAAL,GAA0BA,kBAA1B;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKE,IAAL,GAAYA,IAAZ;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAKG,cAAL,GAAsBA,cAAtB;AACA,SAAKC,eAAL,GAAuBA,eAAvB;AACA;;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA;;AACA,SAAKC,YAAL,GAAoB,CAACC,EAAD,EAAKC,EAAL,KAAYD,EAAE,KAAKC,EAAvC;AACA;;;AACA,SAAKC,IAAL,GAAa,cAAanD,YAAY,EAAG,EAAzC;AACA;;AACA,SAAKoD,sBAAL,GAA8B,IAA9B;AACA;;AACA,SAAKC,QAAL,GAAgB,IAAIjF,OAAJ,EAAhB;AACA;;AACA,SAAKkF,SAAL,GAAiB,MAAM,CAAG,CAA1B;AACA;;;AACA,SAAKC,UAAL,GAAkB,MAAM,CAAG,CAA3B;AACA;;;AACA,SAAKC,QAAL,GAAiB,oBAAmBxD,YAAY,EAAG,EAAnD;AACA;;AACA,SAAKyD,yBAAL,GAAiC,IAAIrF,OAAJ,EAAjC;AACA,SAAKsF,kBAAL,GAA0B,KAAKZ,eAAL,EAAsBa,iBAAtB,IAA2C,EAArE;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA;;AACA,SAAKC,WAAL,GAAmB,YAAnB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,uBAAL,GAA+B,KAAKjB,eAAL,EAAsBkB,sBAAtB,IAAgD,KAA/E;AACA;;AACA,SAAKC,SAAL,GAAiB,EAAjB;AACA;;AACA,SAAKC,sBAAL,GAA8B7F,KAAK,CAAC,MAAM;AACtC,YAAM8F,OAAO,GAAG,KAAKA,OAArB;;AACA,UAAIA,OAAJ,EAAa;AACT,eAAOA,OAAO,CAACC,OAAR,CAAgBC,IAAhB,CAAqB9F,SAAS,CAAC4F,OAAD,CAA9B,EAAyC3F,SAAS,CAAC,MAAMF,KAAK,CAAC,GAAG6F,OAAO,CAACxF,GAAR,CAAY2F,MAAM,IAAIA,MAAM,CAACC,iBAA7B,CAAJ,CAAZ,CAAlD,CAAP;AACH;;AACD,aAAO,KAAKhC,OAAL,CAAaiC,QAAb,CAAsBH,IAAtB,CAA2B5F,IAAI,CAAC,CAAD,CAA/B,EAAoCD,SAAS,CAAC,MAAM,KAAK0F,sBAAZ,CAA7C,CAAP;AACH,KANkC,CAAnC;AAOA;;AACA,SAAKO,YAAL,GAAoB,IAAIrJ,YAAJ,EAApB;AACA;;AACA,SAAKsJ,aAAL,GAAqB,KAAKD,YAAL,CAAkBJ,IAAlB,CAAuB3F,MAAM,CAACiG,CAAC,IAAIA,CAAN,CAA7B,EAAuChG,GAAG,CAAC,MAAM,CAAG,CAAV,CAA1C,CAArB;AACA;;AACA,SAAKiG,aAAL,GAAqB,KAAKH,YAAL,CAAkBJ,IAAlB,CAAuB3F,MAAM,CAACiG,CAAC,IAAI,CAACA,CAAP,CAA7B,EAAwChG,GAAG,CAAC,MAAM,CAAG,CAAV,CAA3C,CAArB;AACA;;AACA,SAAKkG,eAAL,GAAuB,IAAIzJ,YAAJ,EAAvB;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAK0J,WAAL,GAAmB,IAAI1J,YAAJ,EAAnB;;AACA,QAAI,KAAKsG,SAAT,EAAoB;AAChB;AACA;AACA,WAAKA,SAAL,CAAeqD,aAAf,GAA+B,IAA/B;AACH,KA7D+N,CA8DhO;AACA;;;AACA,QAAIjC,eAAe,EAAEkC,yBAAjB,IAA8C,IAAlD,EAAwD;AACpD,WAAKC,0BAAL,GAAkCnC,eAAe,CAACkC,yBAAlD;AACH;;AACD,SAAKE,sBAAL,GAA8BtC,qBAA9B;AACA,SAAKuC,eAAL,GAAuB,KAAKD,sBAAL,EAAvB;AACA,SAAKvC,QAAL,GAAgByC,QAAQ,CAACzC,QAAD,CAAR,IAAsB,CAAtC,CArEgO,CAsEhO;;AACA,SAAK0C,EAAL,GAAU,KAAKA,EAAf;AACH;AACD;;;AACW,MAAPC,OAAO,GAAG;AACV,WAAO,KAAK1B,QAAL,IAAiB,KAAKb,UAA7B;AACH;AACD;;;AACe,MAAXwC,WAAW,GAAG;AACd,WAAO,KAAKC,YAAZ;AACH;;AACc,MAAXD,WAAW,CAACnE,KAAD,EAAQ;AACnB,SAAKoE,YAAL,GAAoBpE,KAApB;AACA,SAAKqE,YAAL,CAAkBC,IAAlB;AACH;AACD;;;AACY,MAARC,QAAQ,GAAG;AACX,WAAO,KAAKC,SAAL,IAAkB,KAAKlE,SAAL,EAAgBmE,OAAhB,EAAyBC,YAAzB,CAAsC3H,UAAU,CAACwH,QAAjD,CAAlB,IAAgF,KAAvF;AACH;;AACW,MAARA,QAAQ,CAACvE,KAAD,EAAQ;AAChB,SAAKwE,SAAL,GAAiBrI,qBAAqB,CAAC6D,KAAD,CAAtC;AACA,SAAKqE,YAAL,CAAkBC,IAAlB;AACH;AACD;;;AACY,MAARK,QAAQ,GAAG;AACX,WAAO,KAAKjC,SAAZ;AACH;;AACW,MAARiC,QAAQ,CAAC3E,KAAD,EAAQ;AAChB,QAAI,KAAK4E,eAAL,KAAyB,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAA7D,CAAJ,EAA6E;AACzE,YAAMrG,gCAAgC,EAAtC;AACH;;AACD,SAAKkE,SAAL,GAAiBvG,qBAAqB,CAAC6D,KAAD,CAAtC;AACH;AACD;;;AAC0B,MAAtB4C,sBAAsB,GAAG;AACzB,WAAO,KAAKD,uBAAZ;AACH;;AACyB,MAAtBC,sBAAsB,CAAC5C,KAAD,EAAQ;AAC9B,SAAK2C,uBAAL,GAA+BxG,qBAAqB,CAAC6D,KAAD,CAApD;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACmB,MAAX8E,WAAW,GAAG;AACd,WAAO,KAAKlD,YAAZ;AACH;;AACc,MAAXkD,WAAW,CAACC,EAAD,EAAK;AAChB,QAAI,OAAOA,EAAP,KAAc,UAAd,KAA6B,OAAOF,SAAP,KAAqB,WAArB,IAAoCA,SAAjE,CAAJ,EAAiF;AAC7E,YAAMlG,iCAAiC,EAAvC;AACH;;AACD,SAAKiD,YAAL,GAAoBmD,EAApB;;AACA,QAAI,KAAKH,eAAT,EAA0B;AACtB;AACA,WAAKI,oBAAL;AACH;AACJ;AACD;;;AACS,MAALhF,KAAK,GAAG;AACR,WAAO,KAAKiF,MAAZ;AACH;;AACQ,MAALjF,KAAK,CAACkF,QAAD,EAAW;AAChB,UAAMC,WAAW,GAAG,KAAKC,YAAL,CAAkBF,QAAlB,CAApB;;AACA,QAAIC,WAAJ,EAAiB;AACb,WAAKjD,SAAL,CAAegD,QAAf;AACH;AACJ;AACD;;;AAC6B,MAAzBtB,yBAAyB,GAAG;AAC5B,WAAO,KAAKC,0BAAZ;AACH;;AAC4B,MAAzBD,yBAAyB,CAAC5D,KAAD,EAAQ;AACjC,SAAK6D,0BAAL,GAAkCzH,oBAAoB,CAAC4D,KAAD,CAAtD;AACH;AACD;;;AACM,MAAFiE,EAAE,GAAG;AACL,WAAO,KAAKoB,GAAZ;AACH;;AACK,MAAFpB,EAAE,CAACjE,KAAD,EAAQ;AACV,SAAKqF,GAAL,GAAWrF,KAAK,IAAI,KAAK+B,IAAzB;AACA,SAAKsC,YAAL,CAAkBC,IAAlB;AACH;;AACDgB,EAAAA,QAAQ,GAAG;AACP,SAAKV,eAAL,GAAuB,IAAIvI,cAAJ,CAAmB,KAAKsI,QAAxB,CAAvB;AACA,SAAKN,YAAL,CAAkBC,IAAlB,GAFO,CAGP;AACA;AACA;;AACA,SAAKjC,yBAAL,CACKY,IADL,CACUzF,oBAAoB,EAD9B,EACkCC,SAAS,CAAC,KAAKwE,QAAN,CAD3C,EAEKsD,SAFL,CAEe,MAAM,KAAKC,mBAAL,CAAyB,KAAKC,SAA9B,CAFrB;AAGH;;AACDC,EAAAA,kBAAkB,GAAG;AACjB,SAAKC,eAAL;;AACA,SAAKf,eAAL,CAAqBgB,OAArB,CAA6B3C,IAA7B,CAAkCxF,SAAS,CAAC,KAAKwE,QAAN,CAA3C,EAA4DsD,SAA5D,CAAsEM,KAAK,IAAI;AAC3EA,MAAAA,KAAK,CAACC,KAAN,CAAYC,OAAZ,CAAoB7C,MAAM,IAAIA,MAAM,CAAC8C,MAAP,EAA9B;AACAH,MAAAA,KAAK,CAACI,OAAN,CAAcF,OAAd,CAAsB7C,MAAM,IAAIA,MAAM,CAACgD,QAAP,EAAhC;AACH,KAHD;;AAIA,SAAKnD,OAAL,CAAaC,OAAb,CAAqBC,IAArB,CAA0B9F,SAAS,CAAC,IAAD,CAAnC,EAA2CM,SAAS,CAAC,KAAKwE,QAAN,CAApD,EAAqEsD,SAArE,CAA+E,MAAM;AACjF,WAAKY,aAAL;;AACA,WAAKnB,oBAAL;AACH,KAHD;AAIH;;AACDoB,EAAAA,SAAS,GAAG;AACR,UAAMC,iBAAiB,GAAG,KAAKC,yBAAL,EAA1B;;AACA,UAAMhG,SAAS,GAAG,KAAKA,SAAvB,CAFQ,CAGR;AACA;AACA;;AACA,QAAI+F,iBAAiB,KAAK,KAAKrE,sBAA/B,EAAuD;AACnD,YAAMuE,OAAO,GAAG,KAAKrG,WAAL,CAAiBsG,aAAjC;AACA,WAAKxE,sBAAL,GAA8BqE,iBAA9B;;AACA,UAAIA,iBAAJ,EAAuB;AACnBE,QAAAA,OAAO,CAACE,YAAR,CAAqB,iBAArB,EAAwCJ,iBAAxC;AACH,OAFD,MAGK;AACDE,QAAAA,OAAO,CAACG,eAAR,CAAwB,iBAAxB;AACH;AACJ;;AACD,QAAIpG,SAAJ,EAAe;AACX;AACA,UAAI,KAAKqG,gBAAL,KAA0BrG,SAAS,CAACmE,OAAxC,EAAiD;AAC7C,YAAI,KAAKkC,gBAAL,KAA0BC,SAA1B,IACAtG,SAAS,CAACuG,QAAV,KAAuB,IADvB,IAEAvG,SAAS,CAACuG,QAAV,KAAuB,KAAKA,QAFhC,EAE0C;AACtC,eAAKA,QAAL,GAAgBvG,SAAS,CAACuG,QAA1B;AACH;;AACD,aAAKF,gBAAL,GAAwBrG,SAAS,CAACmE,OAAlC;AACH;;AACD,WAAKqC,gBAAL;AACH;AACJ;;AACDC,EAAAA,WAAW,CAAC/D,OAAD,EAAU;AACjB;AACA;AACA,QAAIA,OAAO,CAAC,UAAD,CAAX,EAAyB;AACrB,WAAKqB,YAAL,CAAkBC,IAAlB;AACH;;AACD,QAAItB,OAAO,CAAC,2BAAD,CAAP,IAAwC,KAAKgE,WAAjD,EAA8D;AAC1D,WAAKA,WAAL,CAAiBC,aAAjB,CAA+B,KAAKpD,0BAApC;AACH;AACJ;;AACDqD,EAAAA,WAAW,GAAG;AACV,SAAKjF,QAAL,CAAcqC,IAAd;;AACA,SAAKrC,QAAL,CAAckF,QAAd;;AACA,SAAK9C,YAAL,CAAkB8C,QAAlB;AACH;AACD;;;AACAC,EAAAA,MAAM,GAAG;AACL,SAAK3B,SAAL,GAAiB,KAAK4B,KAAL,EAAjB,GAAgC,KAAKC,IAAL,EAAhC;AACH;AACD;;;AACAA,EAAAA,IAAI,GAAG;AACH,QAAI,KAAKC,QAAL,EAAJ,EAAqB;AACjB,WAAK5F,UAAL,GAAkB,IAAlB;;AACA,WAAKqF,WAAL,CAAiBQ,yBAAjB,CAA2C,IAA3C;;AACA,WAAKC,uBAAL;;AACA,WAAKvG,kBAAL,CAAwBwG,YAAxB;AACH;AACJ;AACD;;;AACAL,EAAAA,KAAK,GAAG;AACJ,QAAI,KAAK1F,UAAT,EAAqB;AACjB,WAAKA,UAAL,GAAkB,KAAlB;;AACA,WAAKqF,WAAL,CAAiBQ,yBAAjB,CAA2C,KAAKG,MAAL,KAAgB,KAAhB,GAAwB,KAAnE;;AACA,WAAKzG,kBAAL,CAAwBwG,YAAxB;;AACA,WAAKvF,UAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIyF,EAAAA,UAAU,CAAC5H,KAAD,EAAQ;AACd,SAAKoF,YAAL,CAAkBpF,KAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI6H,EAAAA,gBAAgB,CAAC9C,EAAD,EAAK;AACjB,SAAK7C,SAAL,GAAiB6C,EAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI+C,EAAAA,iBAAiB,CAAC/C,EAAD,EAAK;AAClB,SAAK5C,UAAL,GAAkB4C,EAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIgD,EAAAA,gBAAgB,CAACC,UAAD,EAAa;AACzB,SAAKnB,QAAL,GAAgBmB,UAAhB;;AACA,SAAK9G,kBAAL,CAAwBwG,YAAxB;;AACA,SAAKrD,YAAL,CAAkBC,IAAlB;AACH;AACD;;;AACa,MAATmB,SAAS,GAAG;AACZ,WAAO,KAAK9D,UAAZ;AACH;AACD;;;AACY,MAARsG,QAAQ,GAAG;AACX,WAAO,KAAKtD,QAAL,GAAgB,KAAKC,eAAL,EAAsBqD,QAAtB,IAAkC,EAAlD,GAAuD,KAAKrD,eAAL,EAAsBqD,QAAtB,CAA+B,CAA/B,CAA9D;AACH;AACD;;;AACgB,MAAZC,YAAY,GAAG;AACf,QAAI,KAAKC,KAAT,EAAgB;AACZ,aAAO,EAAP;AACH;;AACD,QAAI,KAAKzF,SAAT,EAAoB;AAChB,YAAM0F,eAAe,GAAG,KAAKxD,eAAL,CAAqBqD,QAArB,CAA8B1K,GAA9B,CAAkC2F,MAAM,IAAIA,MAAM,CAACmF,SAAnD,CAAxB;;AACA,UAAI,KAAKV,MAAL,EAAJ,EAAmB;AACfS,QAAAA,eAAe,CAACE,OAAhB;AACH,OAJe,CAKhB;;;AACA,aAAOF,eAAe,CAACG,IAAhB,CAAqB,IAArB,CAAP;AACH;;AACD,WAAO,KAAK3D,eAAL,CAAqBqD,QAArB,CAA8B,CAA9B,EAAiCI,SAAxC;AACH;AACD;;;AACAV,EAAAA,MAAM,GAAG;AACL,WAAO,KAAKtG,IAAL,GAAY,KAAKA,IAAL,CAAUrB,KAAV,KAAoB,KAAhC,GAAwC,KAA/C;AACH;AACD;;;AACAwI,EAAAA,cAAc,CAAC3C,KAAD,EAAQ;AAClB,QAAI,CAAC,KAAKgB,QAAV,EAAoB;AAChB,WAAKpB,SAAL,GAAiB,KAAKgD,kBAAL,CAAwB5C,KAAxB,CAAjB,GAAkD,KAAK6C,oBAAL,CAA0B7C,KAA1B,CAAlD;AACH;AACJ;AACD;;;AACA6C,EAAAA,oBAAoB,CAAC7C,KAAD,EAAQ;AACxB,UAAM8C,OAAO,GAAG9C,KAAK,CAAC8C,OAAtB;AACA,UAAMC,UAAU,GAAGD,OAAO,KAAKrM,UAAZ,IACfqM,OAAO,KAAKpM,QADG,IAEfoM,OAAO,KAAKnM,UAFG,IAGfmM,OAAO,KAAKlM,WAHhB;AAIA,UAAMoM,SAAS,GAAGF,OAAO,KAAKjM,KAAZ,IAAqBiM,OAAO,KAAKhM,KAAnD;AACA,UAAMmM,OAAO,GAAG,KAAK9B,WAArB,CAPwB,CAQxB;;AACA,QAAK,CAAC8B,OAAO,CAACC,QAAR,EAAD,IAAuBF,SAAvB,IAAoC,CAACjM,cAAc,CAACiJ,KAAD,CAApD,IACC,CAAC,KAAKlB,QAAL,IAAiBkB,KAAK,CAACmD,MAAxB,KAAmCJ,UADxC,EACqD;AACjD/C,MAAAA,KAAK,CAACoD,cAAN,GADiD,CACzB;;AACxB,WAAK3B,IAAL;AACH,KAJD,MAKK,IAAI,CAAC,KAAK3C,QAAV,EAAoB;AACrB,YAAMuE,wBAAwB,GAAG,KAAKjB,QAAtC;AACAa,MAAAA,OAAO,CAACK,SAAR,CAAkBtD,KAAlB;AACA,YAAMuD,cAAc,GAAG,KAAKnB,QAA5B,CAHqB,CAIrB;;AACA,UAAImB,cAAc,IAAIF,wBAAwB,KAAKE,cAAnD,EAAmE;AAC/D;AACA;AACA,aAAK3H,cAAL,CAAoB4H,QAApB,CAA6BD,cAAc,CAACf,SAA5C,EAAuD,KAAvD;AACH;AACJ;AACJ;AACD;;;AACAI,EAAAA,kBAAkB,CAAC5C,KAAD,EAAQ;AACtB,UAAMiD,OAAO,GAAG,KAAK9B,WAArB;AACA,UAAM2B,OAAO,GAAG9C,KAAK,CAAC8C,OAAtB;AACA,UAAMC,UAAU,GAAGD,OAAO,KAAKrM,UAAZ,IAA0BqM,OAAO,KAAKpM,QAAzD;AACA,UAAMwM,QAAQ,GAAGD,OAAO,CAACC,QAAR,EAAjB;;AACA,QAAIH,UAAU,IAAI/C,KAAK,CAACmD,MAAxB,EAAgC;AAC5B;AACAnD,MAAAA,KAAK,CAACoD,cAAN;AACA,WAAK5B,KAAL,GAH4B,CAI5B;AACA;AACH,KAND,MAOK,IAAI,CAAC0B,QAAD,KACJJ,OAAO,KAAKjM,KAAZ,IAAqBiM,OAAO,KAAKhM,KAD7B,KAELmM,OAAO,CAACQ,UAFH,IAGL,CAAC1M,cAAc,CAACiJ,KAAD,CAHd,EAGuB;AACxBA,MAAAA,KAAK,CAACoD,cAAN;;AACAH,MAAAA,OAAO,CAACQ,UAAR,CAAmBC,qBAAnB;AACH,KANI,MAOA,IAAI,CAACR,QAAD,IAAa,KAAKrG,SAAlB,IAA+BiG,OAAO,KAAK9L,CAA3C,IAAgDgJ,KAAK,CAAC2D,OAA1D,EAAmE;AACpE3D,MAAAA,KAAK,CAACoD,cAAN;AACA,YAAMQ,oBAAoB,GAAG,KAAK1G,OAAL,CAAa2G,IAAb,CAAkBC,GAAG,IAAI,CAACA,GAAG,CAAC9C,QAAL,IAAiB,CAAC8C,GAAG,CAAC1B,QAA/C,CAA7B;AACA,WAAKlF,OAAL,CAAagD,OAAb,CAAqB7C,MAAM,IAAI;AAC3B,YAAI,CAACA,MAAM,CAAC2D,QAAZ,EAAsB;AAClB4C,UAAAA,oBAAoB,GAAGvG,MAAM,CAAC8C,MAAP,EAAH,GAAqB9C,MAAM,CAACgD,QAAP,EAAzC;AACH;AACJ,OAJD;AAKH,KARI,MASA;AACD,YAAM0D,sBAAsB,GAAGd,OAAO,CAACe,eAAvC;AACAf,MAAAA,OAAO,CAACK,SAAR,CAAkBtD,KAAlB;;AACA,UAAI,KAAKnD,SAAL,IACAkG,UADA,IAEA/C,KAAK,CAACiE,QAFN,IAGAhB,OAAO,CAACQ,UAHR,IAIAR,OAAO,CAACe,eAAR,KAA4BD,sBAJhC,EAIwD;AACpDd,QAAAA,OAAO,CAACQ,UAAR,CAAmBC,qBAAnB;AACH;AACJ;AACJ;;AACDQ,EAAAA,QAAQ,GAAG;AACP,QAAI,CAAC,KAAKlD,QAAV,EAAoB;AAChB,WAAKrE,QAAL,GAAgB,IAAhB;AACA,WAAK6B,YAAL,CAAkBC,IAAlB;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACI0F,EAAAA,OAAO,GAAG;AACN,SAAKxH,QAAL,GAAgB,KAAhB;;AACA,QAAI,CAAC,KAAKqE,QAAN,IAAkB,CAAC,KAAKpB,SAA5B,EAAuC;AACnC,WAAKtD,UAAL;;AACA,WAAKjB,kBAAL,CAAwBwG,YAAxB;;AACA,WAAKrD,YAAL,CAAkBC,IAAlB;AACH;AACJ;AACD;AACJ;AACA;;;AACI2F,EAAAA,WAAW,GAAG;AACV,SAAKC,WAAL,CAAiBC,cAAjB,CAAgClH,IAAhC,CAAqC5F,IAAI,CAAC,CAAD,CAAzC,EAA8CkI,SAA9C,CAAwD,MAAM;AAC1D,WAAKrE,kBAAL,CAAwBkJ,aAAxB;;AACA,WAAKC,mBAAL;AACH,KAHD;AAIH;AACD;;;AACAC,EAAAA,cAAc,GAAG;AACb,WAAO,KAAKhJ,gBAAL,GAAyB,OAAM,KAAKA,gBAAL,CAAsBiJ,KAAM,EAA3D,GAA+D,EAAtE;AACH;AACD;;;AACS,MAALpC,KAAK,GAAG;AACR,WAAO,CAAC,KAAKvD,eAAN,IAAyB,KAAKA,eAAL,CAAqB4F,OAArB,EAAhC;AACH;;AACDxF,EAAAA,oBAAoB,GAAG;AACnB;AACA;AACAyF,IAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AACzB,UAAI,KAAKrK,SAAT,EAAoB;AAChB,aAAK2E,MAAL,GAAc,KAAK3E,SAAL,CAAeN,KAA7B;AACH;;AACD,WAAK4K,oBAAL,CAA0B,KAAK3F,MAA/B;;AACA,WAAKZ,YAAL,CAAkBC,IAAlB;AACH,KAND;AAOH;AACD;AACJ;AACA;AACA;;;AACIsG,EAAAA,oBAAoB,CAAC5K,KAAD,EAAQ;AACxB,SAAK4E,eAAL,CAAqBqD,QAArB,CAA8BlC,OAA9B,CAAsC7C,MAAM,IAAIA,MAAM,CAAC2H,iBAAP,EAAhD;;AACA,SAAKjG,eAAL,CAAqBkG,KAArB;;AACA,QAAI,KAAKnG,QAAL,IAAiB3E,KAArB,EAA4B;AACxB,UAAI,CAAC+K,KAAK,CAACC,OAAN,CAAchL,KAAd,CAAD,KAA0B,OAAO6E,SAAP,KAAqB,WAArB,IAAoCA,SAA9D,CAAJ,EAA8E;AAC1E,cAAMnG,8BAA8B,EAApC;AACH;;AACDsB,MAAAA,KAAK,CAAC+F,OAAN,CAAekF,YAAD,IAAkB,KAAKC,oBAAL,CAA0BD,YAA1B,CAAhC;;AACA,WAAKE,WAAL;AACH,KAND,MAOK;AACD,YAAMC,mBAAmB,GAAG,KAAKF,oBAAL,CAA0BlL,KAA1B,CAA5B,CADC,CAED;AACA;;;AACA,UAAIoL,mBAAJ,EAAyB;AACrB,aAAKpE,WAAL,CAAiBqE,gBAAjB,CAAkCD,mBAAlC;AACH,OAFD,MAGK,IAAI,CAAC,KAAK3F,SAAV,EAAqB;AACtB;AACA;AACA,aAAKuB,WAAL,CAAiBqE,gBAAjB,CAAkC,CAAC,CAAnC;AACH;AACJ;;AACD,SAAKnK,kBAAL,CAAwBwG,YAAxB;AACH;AACD;AACJ;AACA;AACA;;;AACIwD,EAAAA,oBAAoB,CAAClL,KAAD,EAAQ;AACxB,UAAMoL,mBAAmB,GAAG,KAAKrI,OAAL,CAAauI,IAAb,CAAmBpI,MAAD,IAAY;AACtD;AACA;AACA,UAAI,KAAK0B,eAAL,CAAqB2G,UAArB,CAAgCrI,MAAhC,CAAJ,EAA6C;AACzC,eAAO,KAAP;AACH;;AACD,UAAI;AACA;AACA,eAAOA,MAAM,CAAClD,KAAP,IAAgB,IAAhB,IAAwB,KAAK4B,YAAL,CAAkBsB,MAAM,CAAClD,KAAzB,EAAgCA,KAAhC,CAA/B;AACH,OAHD,CAIA,OAAOwL,KAAP,EAAc;AACV,YAAI,OAAO3G,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C;AACA4G,UAAAA,OAAO,CAACC,IAAR,CAAaF,KAAb;AACH;;AACD,eAAO,KAAP;AACH;AACJ,KAjB2B,CAA5B;;AAkBA,QAAIJ,mBAAJ,EAAyB;AACrB,WAAKxG,eAAL,CAAqBoB,MAArB,CAA4BoF,mBAA5B;AACH;;AACD,WAAOA,mBAAP;AACH;AACD;;;AACAhG,EAAAA,YAAY,CAACF,QAAD,EAAW;AACnB;AACA,QAAIA,QAAQ,KAAK,KAAKD,MAAlB,IAA6B,KAAKvC,SAAL,IAAkBqI,KAAK,CAACC,OAAN,CAAc9F,QAAd,CAAnD,EAA6E;AACzE,UAAI,KAAKnC,OAAT,EAAkB;AACd,aAAK6H,oBAAL,CAA0B1F,QAA1B;AACH;;AACD,WAAKD,MAAL,GAAcC,QAAd;AACA,aAAO,IAAP;AACH;;AACD,WAAO,KAAP;AACH;AACD;;;AACAS,EAAAA,eAAe,GAAG;AACd,SAAKqB,WAAL,GAAmB,IAAI/K,0BAAJ,CAA+B,KAAK8G,OAApC,EACdkE,aADc,CACA,KAAKpD,0BADL,EAEd8H,uBAFc,GAGdnE,yBAHc,CAGY,KAAKG,MAAL,KAAgB,KAAhB,GAAwB,KAHpC,EAIdiE,cAJc,GAKdC,uBALc,CAKU,CAAC,UAAD,CALV,CAAnB;;AAMA,SAAK7E,WAAL,CAAiB8E,MAAjB,CAAwB7I,IAAxB,CAA6BxF,SAAS,CAAC,KAAKwE,QAAN,CAAtC,EAAuDsD,SAAvD,CAAiE,MAAM;AACnE,UAAI,KAAKE,SAAT,EAAoB;AAChB;AACA;AACA,YAAI,CAAC,KAAKd,QAAN,IAAkB,KAAKqC,WAAL,CAAiBsC,UAAvC,EAAmD;AAC/C,eAAKtC,WAAL,CAAiBsC,UAAjB,CAA4BC,qBAA5B;AACH,SALe,CAMhB;AACA;;;AACA,aAAKwC,KAAL;AACA,aAAK1E,KAAL;AACH;AACJ,KAZD;;AAaA,SAAKL,WAAL,CAAiBgF,MAAjB,CAAwB/I,IAAxB,CAA6BxF,SAAS,CAAC,KAAKwE,QAAN,CAAtC,EAAuDsD,SAAvD,CAAiE,MAAM;AACnE,UAAI,KAAK5D,UAAL,IAAmB,KAAKsK,KAA5B,EAAmC;AAC/B,aAAKC,qBAAL,CAA2B,KAAKlF,WAAL,CAAiB6C,eAAjB,IAAoC,CAA/D;AACH,OAFD,MAGK,IAAI,CAAC,KAAKlI,UAAN,IAAoB,CAAC,KAAKgD,QAA1B,IAAsC,KAAKqC,WAAL,CAAiBsC,UAA3D,EAAuE;AACxE,aAAKtC,WAAL,CAAiBsC,UAAjB,CAA4BC,qBAA5B;AACH;AACJ,KAPD;AAQH;AACD;;;AACApD,EAAAA,aAAa,GAAG;AACZ,UAAMgG,kBAAkB,GAAGjP,KAAK,CAAC,KAAK6F,OAAL,CAAaC,OAAd,EAAuB,KAAKf,QAA5B,CAAhC;AACA,SAAKa,sBAAL,CAA4BG,IAA5B,CAAiCxF,SAAS,CAAC0O,kBAAD,CAA1C,EAAgE5G,SAAhE,CAA0EM,KAAK,IAAI;AAC/E,WAAKuG,SAAL,CAAevG,KAAK,CAAC9F,MAArB,EAA6B8F,KAAK,CAACwG,WAAnC;;AACA,UAAIxG,KAAK,CAACwG,WAAN,IAAqB,CAAC,KAAK1H,QAA3B,IAAuC,KAAKhD,UAAhD,EAA4D;AACxD,aAAK0F,KAAL;AACA,aAAK0E,KAAL;AACH;AACJ,KAND,EAFY,CASZ;AACA;;AACA7O,IAAAA,KAAK,CAAC,GAAG,KAAK6F,OAAL,CAAaxF,GAAb,CAAiB2F,MAAM,IAAIA,MAAM,CAACoJ,aAAlC,CAAJ,CAAL,CACKrJ,IADL,CACUxF,SAAS,CAAC0O,kBAAD,CADnB,EAEK5G,SAFL,CAEe,MAAM;AACjB,WAAKrE,kBAAL,CAAwBwG,YAAxB;;AACA,WAAKrD,YAAL,CAAkBC,IAAlB;AACH,KALD;AAMH;AACD;;;AACA8H,EAAAA,SAAS,CAAClJ,MAAD,EAASmJ,WAAT,EAAsB;AAC3B,UAAME,WAAW,GAAG,KAAK3H,eAAL,CAAqB2G,UAArB,CAAgCrI,MAAhC,CAApB;;AACA,QAAIA,MAAM,CAAClD,KAAP,IAAgB,IAAhB,IAAwB,CAAC,KAAK0C,SAAlC,EAA6C;AACzCQ,MAAAA,MAAM,CAACgD,QAAP;;AACA,WAAKtB,eAAL,CAAqBkG,KAArB;;AACA,UAAI,KAAK9K,KAAL,IAAc,IAAlB,EAAwB;AACpB,aAAKwM,iBAAL,CAAuBtJ,MAAM,CAAClD,KAA9B;AACH;AACJ,KAND,MAOK;AACD,UAAIuM,WAAW,KAAKrJ,MAAM,CAAC+E,QAA3B,EAAqC;AACjC/E,QAAAA,MAAM,CAAC+E,QAAP,GACM,KAAKrD,eAAL,CAAqBoB,MAArB,CAA4B9C,MAA5B,CADN,GAEM,KAAK0B,eAAL,CAAqBsB,QAArB,CAA8BhD,MAA9B,CAFN;AAGH;;AACD,UAAImJ,WAAJ,EAAiB;AACb,aAAKrF,WAAL,CAAiByF,aAAjB,CAA+BvJ,MAA/B;AACH;;AACD,UAAI,KAAKyB,QAAT,EAAmB;AACf,aAAKwG,WAAL;;AACA,YAAIkB,WAAJ,EAAiB;AACb;AACA;AACA;AACA;AACA,eAAKN,KAAL;AACH;AACJ;AACJ;;AACD,QAAIQ,WAAW,KAAK,KAAK3H,eAAL,CAAqB2G,UAArB,CAAgCrI,MAAhC,CAApB,EAA6D;AACzD,WAAKsJ,iBAAL;AACH;;AACD,SAAKnI,YAAL,CAAkBC,IAAlB;AACH;AACD;;;AACA6G,EAAAA,WAAW,GAAG;AACV,QAAI,KAAKxG,QAAT,EAAmB;AACf,YAAM5B,OAAO,GAAG,KAAKA,OAAL,CAAa2J,OAAb,EAAhB;;AACA,WAAK9H,eAAL,CAAqB+H,IAArB,CAA0B,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAChC,eAAO,KAAKC,cAAL,GACD,KAAKA,cAAL,CAAoBF,CAApB,EAAuBC,CAAvB,EAA0B9J,OAA1B,CADC,GAEDA,OAAO,CAACgK,OAAR,CAAgBH,CAAhB,IAAqB7J,OAAO,CAACgK,OAAR,CAAgBF,CAAhB,CAF3B;AAGH,OAJD;;AAKA,WAAKxI,YAAL,CAAkBC,IAAlB;AACH;AACJ;AACD;;;AACAkI,EAAAA,iBAAiB,CAACQ,aAAD,EAAgB;AAC7B,QAAIC,WAAW,GAAG,IAAlB;;AACA,QAAI,KAAKtI,QAAT,EAAmB;AACfsI,MAAAA,WAAW,GAAG,KAAKhF,QAAL,CAAc1K,GAAd,CAAkB2F,MAAM,IAAIA,MAAM,CAAClD,KAAnC,CAAd;AACH,KAFD,MAGK;AACDiN,MAAAA,WAAW,GAAG,KAAKhF,QAAL,GAAgB,KAAKA,QAAL,CAAcjI,KAA9B,GAAsCgN,aAApD;AACH;;AACD,SAAK/H,MAAL,GAAcgI,WAAd;AACA,SAAKvJ,WAAL,CAAiBwJ,IAAjB,CAAsBD,WAAtB;;AACA,SAAK/K,SAAL,CAAe+K,WAAf;;AACA,SAAKxJ,eAAL,CAAqByJ,IAArB,CAA0B,KAAKC,eAAL,CAAqBF,WAArB,CAA1B;;AACA,SAAK/L,kBAAL,CAAwBwG,YAAxB;AACH;AACD;AACJ;AACA;AACA;;;AACID,EAAAA,uBAAuB,GAAG;AACtB,QAAI,KAAKT,WAAT,EAAsB;AAClB,UAAI,KAAKmB,KAAT,EAAgB;AACZ,aAAKnB,WAAL,CAAiBoG,kBAAjB;AACH,OAFD,MAGK;AACD,aAAKpG,WAAL,CAAiByF,aAAjB,CAA+B,KAAK7H,eAAL,CAAqBqD,QAArB,CAA8B,CAA9B,CAA/B;AACH;AACJ;AACJ;AACD;;;AACAV,EAAAA,QAAQ,GAAG;AACP,WAAO,CAAC,KAAK5F,UAAN,IAAoB,CAAC,KAAKkF,QAA1B,IAAsC,KAAK9D,OAAL,EAAcsK,MAAd,GAAuB,CAApE;AACH;AACD;;;AACAtB,EAAAA,KAAK,CAAChJ,OAAD,EAAU;AACX,SAAK7C,WAAL,CAAiBsG,aAAjB,CAA+BuF,KAA/B,CAAqChJ,OAArC;AACH;AACD;;;AACAuK,EAAAA,uBAAuB,GAAG;AACtB,QAAI,KAAKzK,SAAT,EAAoB;AAChB,aAAO,IAAP;AACH;;AACD,UAAM0K,OAAO,GAAG,KAAKjM,gBAAL,EAAuBkM,UAAvB,EAAhB;AACA,UAAMC,eAAe,GAAGF,OAAO,GAAGA,OAAO,GAAG,GAAb,GAAmB,EAAlD;AACA,WAAO,KAAKG,cAAL,GAAsBD,eAAe,GAAG,KAAKC,cAA7C,GAA8DH,OAArE;AACH;AACD;;;AACAI,EAAAA,wBAAwB,GAAG;AACvB,QAAI,KAAKlI,SAAL,IAAkB,KAAKuB,WAAvB,IAAsC,KAAKA,WAAL,CAAiBsC,UAA3D,EAAuE;AACnE,aAAO,KAAKtC,WAAL,CAAiBsC,UAAjB,CAA4BrF,EAAnC;AACH;;AACD,WAAO,IAAP;AACH;AACD;;;AACAqC,EAAAA,yBAAyB,GAAG;AACxB,QAAI,KAAKzD,SAAT,EAAoB;AAChB,aAAO,IAAP;AACH;;AACD,UAAM0K,OAAO,GAAG,KAAKjM,gBAAL,EAAuBkM,UAAvB,EAAhB;AACA,QAAIxN,KAAK,GAAG,CAACuN,OAAO,GAAGA,OAAO,GAAG,GAAb,GAAmB,EAA3B,IAAiC,KAAKnL,QAAlD;;AACA,QAAI,KAAKsL,cAAT,EAAyB;AACrB1N,MAAAA,KAAK,IAAI,MAAM,KAAK0N,cAApB;AACH;;AACD,WAAO1N,KAAP;AACH;AACD;;;AACAwF,EAAAA,mBAAmB,CAACoI,MAAD,EAAS;AACxB,SAAKvK,YAAL,CAAkB6J,IAAlB,CAAuBU,MAAvB;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,iBAAiB,CAACC,GAAD,EAAM;AACnB,SAAKC,gBAAL,GAAwBD,GAAG,CAACvF,IAAJ,CAAS,GAAT,CAAxB;AACH;AACD;AACJ;AACA;AACA;;;AACIyF,EAAAA,gBAAgB,GAAG;AACf,SAAKjC,KAAL;AACA,SAAKzE,IAAL;AACH;AACD;AACJ;AACA;AACA;;;AACwB,MAAhB2G,gBAAgB,GAAG;AACnB,WAAO,KAAKtM,UAAL,IAAmB,CAAC,KAAKwG,KAAzB,IAAmC,KAAK3F,QAAL,IAAiB,CAAC,CAAC,KAAK4B,YAAlE;AACH;;AA3qB4C;;AA6qBjDpD,cAAc,CAACP,IAAf;AAAA,mBAA2GO,cAA3G,EAvrBmGnH,EAurBnG,mBAA2IiC,EAAE,CAACoS,aAA9I,GAvrBmGrU,EAurBnG,mBAAwKA,EAAE,CAACsU,iBAA3K,GAvrBmGtU,EAurBnG,mBAAyMA,EAAE,CAACuU,MAA5M,GAvrBmGvU,EAurBnG,mBAA+NiB,EAAE,CAACuT,iBAAlO,GAvrBmGxU,EAurBnG,mBAAgQA,EAAE,CAACyU,UAAnQ,GAvrBmGzU,EAurBnG,mBAA0RqC,EAAE,CAACqS,cAA7R,MAvrBmG1U,EAurBnG,mBAAwUiD,EAAE,CAAC0R,MAA3U,MAvrBmG3U,EAurBnG,mBAA8WiD,EAAE,CAAC2R,kBAAjX,MAvrBmG5U,EAurBnG,mBAAga8B,cAAha,MAvrBmG9B,EAurBnG,mBAA2ciD,EAAE,CAAC4R,SAA9c,OAvrBmG7U,EAurBnG,mBAAggB,UAAhgB,GAvrBmGA,EAurBnG,mBAAwiBsF,0BAAxiB,GAvrBmGtF,EAurBnG,mBAA+kBmC,EAAE,CAAC2S,aAAllB,GAvrBmG9U,EAurBnG,mBAA4mB2F,iBAA5mB;AAAA;;AACAwB,cAAc,CAACN,IAAf,kBAxrBmG7G,EAwrBnG;AAAA,QAA+FmH,cAA/F;AAAA;AAAA;AAxrBmGnH,MAAAA,EAwrBnG;AAxrBmGA,MAAAA,EAwrBnG;AAxrBmGA,MAAAA,EAwrBnG,aAA86BJ,mBAA96B;AAAA;;AAAA;AAAA;;AAxrBmGI,MAAAA,EAwrBnG,qBAxrBmGA,EAwrBnG;AAxrBmGA,MAAAA,EAwrBnG,qBAxrBmGA,EAwrBnG;AAxrBmGA,MAAAA,EAwrBnG,qBAxrBmGA,EAwrBnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAxrBmGA,EAwrBnG,6BAxrBmGA,EAwrBnG;AAAA;;AACA;AAAA,qDAzrBmGA,EAyrBnG,mBAA2FmH,cAA3F,EAAuH,CAAC;AAC5GJ,IAAAA,IAAI,EAAE7G;AADsG,GAAD,CAAvH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE6G,MAAAA,IAAI,EAAE9E,EAAE,CAACoS;AAAX,KAAD,EAA6B;AAAEtN,MAAAA,IAAI,EAAE/G,EAAE,CAACsU;AAAX,KAA7B,EAA6D;AAAEvN,MAAAA,IAAI,EAAE/G,EAAE,CAACuU;AAAX,KAA7D,EAAkF;AAAExN,MAAAA,IAAI,EAAE9F,EAAE,CAACuT;AAAX,KAAlF,EAAkH;AAAEzN,MAAAA,IAAI,EAAE/G,EAAE,CAACyU;AAAX,KAAlH,EAA2I;AAAE1N,MAAAA,IAAI,EAAE1E,EAAE,CAACqS,cAAX;AAA2BK,MAAAA,UAAU,EAAE,CAAC;AAChNhO,QAAAA,IAAI,EAAE3G;AAD0M,OAAD;AAAvC,KAA3I,EAE3B;AAAE2G,MAAAA,IAAI,EAAE9D,EAAE,CAAC0R,MAAX;AAAmBI,MAAAA,UAAU,EAAE,CAAC;AAClChO,QAAAA,IAAI,EAAE3G;AAD4B,OAAD;AAA/B,KAF2B,EAI3B;AAAE2G,MAAAA,IAAI,EAAE9D,EAAE,CAAC2R,kBAAX;AAA+BG,MAAAA,UAAU,EAAE,CAAC;AAC9ChO,QAAAA,IAAI,EAAE3G;AADwC,OAAD;AAA3C,KAJ2B,EAM3B;AAAE2G,MAAAA,IAAI,EAAElF,EAAE,CAACmT,YAAX;AAAyBD,MAAAA,UAAU,EAAE,CAAC;AACxChO,QAAAA,IAAI,EAAE3G;AADkC,OAAD,EAExC;AACC2G,QAAAA,IAAI,EAAE1G,MADP;AAEC2G,QAAAA,IAAI,EAAE,CAAClF,cAAD;AAFP,OAFwC;AAArC,KAN2B,EAW3B;AAAEiF,MAAAA,IAAI,EAAE9D,EAAE,CAAC4R,SAAX;AAAsBE,MAAAA,UAAU,EAAE,CAAC;AACrChO,QAAAA,IAAI,EAAEzG;AAD+B,OAAD,EAErC;AACCyG,QAAAA,IAAI,EAAE3G;AADP,OAFqC;AAAlC,KAX2B,EAe3B;AAAE2G,MAAAA,IAAI,EAAEgG,SAAR;AAAmBgI,MAAAA,UAAU,EAAE,CAAC;AAClChO,QAAAA,IAAI,EAAExG,SAD4B;AAElCyG,QAAAA,IAAI,EAAE,CAAC,UAAD;AAF4B,OAAD;AAA/B,KAf2B,EAkB3B;AAAED,MAAAA,IAAI,EAAEgG,SAAR;AAAmBgI,MAAAA,UAAU,EAAE,CAAC;AAClChO,QAAAA,IAAI,EAAE1G,MAD4B;AAElC2G,QAAAA,IAAI,EAAE,CAAC1B,0BAAD;AAF4B,OAAD;AAA/B,KAlB2B,EAqB3B;AAAEyB,MAAAA,IAAI,EAAE5E,EAAE,CAAC2S;AAAX,KArB2B,EAqBC;AAAE/N,MAAAA,IAAI,EAAEgG,SAAR;AAAmBgI,MAAAA,UAAU,EAAE,CAAC;AAC9DhO,QAAAA,IAAI,EAAE3G;AADwD,OAAD,EAE9D;AACC2G,QAAAA,IAAI,EAAE1G,MADP;AAEC2G,QAAAA,IAAI,EAAE,CAACrB,iBAAD;AAFP,OAF8D;AAA/B,KArBD,CAAP;AA0BlB,GA5BxB,EA4B0C;AAAE9B,IAAAA,OAAO,EAAE,CAAC;AACtCkD,MAAAA,IAAI,EAAEvG,SADgC;AAEtCwG,MAAAA,IAAI,EAAE,CAAC,SAAD;AAFgC,KAAD,CAAX;AAG1BoL,IAAAA,KAAK,EAAE,CAAC;AACRrL,MAAAA,IAAI,EAAEvG,SADE;AAERwG,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFE,KAAD,CAHmB;AAM1BqJ,IAAAA,WAAW,EAAE,CAAC;AACdtJ,MAAAA,IAAI,EAAEvG,SADQ;AAEdwG,MAAAA,IAAI,EAAE,CAACpH,mBAAD;AAFQ,KAAD,CANa;AAS1BqV,IAAAA,UAAU,EAAE,CAAC;AACblO,MAAAA,IAAI,EAAEtG;AADO,KAAD,CATc;AAW1B6J,IAAAA,WAAW,EAAE,CAAC;AACdvD,MAAAA,IAAI,EAAEtG;AADQ,KAAD,CAXa;AAa1BiK,IAAAA,QAAQ,EAAE,CAAC;AACX3D,MAAAA,IAAI,EAAEtG;AADK,KAAD,CAbgB;AAe1BqK,IAAAA,QAAQ,EAAE,CAAC;AACX/D,MAAAA,IAAI,EAAEtG;AADK,KAAD,CAfgB;AAiB1BsI,IAAAA,sBAAsB,EAAE,CAAC;AACzBhC,MAAAA,IAAI,EAAEtG;AADmB,KAAD,CAjBE;AAmB1BwK,IAAAA,WAAW,EAAE,CAAC;AACdlE,MAAAA,IAAI,EAAEtG;AADQ,KAAD,CAnBa;AAqB1B0F,IAAAA,KAAK,EAAE,CAAC;AACRY,MAAAA,IAAI,EAAEtG;AADE,KAAD,CArBmB;AAuB1BuI,IAAAA,SAAS,EAAE,CAAC;AACZjC,MAAAA,IAAI,EAAEtG,KADM;AAEZuG,MAAAA,IAAI,EAAE,CAAC,YAAD;AAFM,KAAD,CAvBe;AA0B1B6M,IAAAA,cAAc,EAAE,CAAC;AACjB9M,MAAAA,IAAI,EAAEtG,KADW;AAEjBuG,MAAAA,IAAI,EAAE,CAAC,iBAAD;AAFW,KAAD,CA1BU;AA6B1BkO,IAAAA,iBAAiB,EAAE,CAAC;AACpBnO,MAAAA,IAAI,EAAEtG;AADc,KAAD,CA7BO;AA+B1BsJ,IAAAA,yBAAyB,EAAE,CAAC;AAC5BhD,MAAAA,IAAI,EAAEtG;AADsB,KAAD,CA/BD;AAiC1BwS,IAAAA,cAAc,EAAE,CAAC;AACjBlM,MAAAA,IAAI,EAAEtG;AADW,KAAD,CAjCU;AAmC1B2J,IAAAA,EAAE,EAAE,CAAC;AACLrD,MAAAA,IAAI,EAAEtG;AADD,KAAD,CAnCsB;AAqC1B+I,IAAAA,YAAY,EAAE,CAAC;AACfzC,MAAAA,IAAI,EAAErG;AADS,KAAD,CArCY;AAuC1B+I,IAAAA,aAAa,EAAE,CAAC;AAChB1C,MAAAA,IAAI,EAAErG,MADU;AAEhBsG,MAAAA,IAAI,EAAE,CAAC,QAAD;AAFU,KAAD,CAvCW;AA0C1B2C,IAAAA,aAAa,EAAE,CAAC;AAChB5C,MAAAA,IAAI,EAAErG,MADU;AAEhBsG,MAAAA,IAAI,EAAE,CAAC,QAAD;AAFU,KAAD,CA1CW;AA6C1B4C,IAAAA,eAAe,EAAE,CAAC;AAClB7C,MAAAA,IAAI,EAAErG;AADY,KAAD,CA7CS;AA+C1BmJ,IAAAA,WAAW,EAAE,CAAC;AACd9C,MAAAA,IAAI,EAAErG;AADQ,KAAD;AA/Ca,GA5B1C;AAAA;;AA8EA,MAAMyU,SAAN,SAAwBhO,cAAxB,CAAuC;AACnClB,EAAAA,WAAW,GAAG;AACV,UAAM,GAAGmP,SAAT;AACA;;AACA,SAAKC,UAAL,GAAkB,CAAlB;AACA;;AACA,SAAKC,gBAAL,GAAwB,CAAxB;AACA;;AACA,SAAKC,gBAAL,GAAwB,KAAxB;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,UAAL,GAAkB,CACd;AACIC,MAAAA,OAAO,EAAE,OADb;AAEIC,MAAAA,OAAO,EAAE,KAFb;AAGIC,MAAAA,QAAQ,EAAE,OAHd;AAIIC,MAAAA,QAAQ,EAAE;AAJd,KADc,EAOd;AACIH,MAAAA,OAAO,EAAE,OADb;AAEIC,MAAAA,OAAO,EAAE,QAFb;AAGIC,MAAAA,QAAQ,EAAE,OAHd;AAIIC,MAAAA,QAAQ,EAAE;AAJd,KAPc,CAAlB;AAcH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,uBAAuB,CAACC,aAAD,EAAgBC,YAAhB,EAA8BC,SAA9B,EAAyC;AAC5D,UAAMC,UAAU,GAAG,KAAKC,cAAL,EAAnB;;AACA,UAAMC,yBAAyB,GAAGF,UAAU,GAAGH,aAA/C;AACA,UAAMM,gBAAgB,GAAGH,UAAU,GAAG,CAAtC,CAH4D,CAI5D;AACA;AACA;AACA;;AACA,UAAMI,qBAAqB,GAAGF,yBAAyB,GAAGJ,YAA5B,GAA2CK,gBAAzE;AACA,WAAOE,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYH,qBAAZ,CAAT,EAA6CL,SAA7C,CAAP;AACH;;AACDxK,EAAAA,QAAQ,GAAG;AACP,UAAMA,QAAN;;AACA,SAAKrE,cAAL,CACK+K,MADL,GAEK/I,IAFL,CAEUxF,SAAS,CAAC,KAAKwE,QAAN,CAFnB,EAGKsD,SAHL,CAGe,MAAM;AACjB,UAAI,KAAKE,SAAT,EAAoB;AAChB,aAAK8K,YAAL,GAAoB,KAAK7S,OAAL,CAAa8I,aAAb,CAA2BgK,qBAA3B,EAApB;;AACA,aAAKtP,kBAAL,CAAwBwG,YAAxB;AACH;AACJ,KARD;AASH;;AACDJ,EAAAA,IAAI,GAAG;AACH,QAAI,MAAMC,QAAN,EAAJ,EAAsB;AAClB,YAAMD,IAAN;AACA,WAAKiJ,YAAL,GAAoB,KAAK7S,OAAL,CAAa8I,aAAb,CAA2BgK,qBAA3B,EAApB,CAFkB,CAGlB;AACA;;AACA,WAAKrB,gBAAL,GAAwBnL,QAAQ,CAACyM,gBAAgB,CAAC,KAAK/S,OAAL,CAAa8I,aAAd,CAAhB,CAA6CkK,QAA7C,IAAyD,GAA1D,CAAhC;;AACA,WAAKC,yBAAL,GANkB,CAOlB;;;AACA,WAAKxP,OAAL,CAAaiC,QAAb,CAAsBH,IAAtB,CAA2B5F,IAAI,CAAC,CAAD,CAA/B,EAAoCkI,SAApC,CAA8C,MAAM;AAChD,YAAI,KAAK4J,gBAAL,IACA,KAAKjF,WAAL,CAAiB0G,UADjB,IAEA,KAAK1G,WAAL,CAAiB0G,UAAjB,CAA4BC,cAFhC,EAEgD;AAC5C,eAAK3G,WAAL,CAAiB0G,UAAjB,CAA4BC,cAA5B,CAA2C9S,KAA3C,CAAiD2S,QAAjD,GAA6D,GAAE,KAAKvB,gBAAiB,IAArF;AACH;AACJ,OAND;AAOH;AACJ;AACD;;;AACAjD,EAAAA,qBAAqB,CAAC4E,KAAD,EAAQ;AACzB,UAAMC,UAAU,GAAG5V,6BAA6B,CAAC2V,KAAD,EAAQ,KAAK/N,OAAb,EAAsB,KAAKiO,YAA3B,CAAhD;;AACA,UAAMjB,UAAU,GAAG,KAAKC,cAAL,EAAnB;;AACA,QAAIc,KAAK,KAAK,CAAV,IAAeC,UAAU,KAAK,CAAlC,EAAqC;AACjC;AACA;AACA;AACA,WAAK9E,KAAL,CAAWzF,aAAX,CAAyByK,SAAzB,GAAqC,CAArC;AACH,KALD,MAMK;AACD,WAAKhF,KAAL,CAAWzF,aAAX,CAAyByK,SAAzB,GAAqC7V,wBAAwB,CAAC,CAAC0V,KAAK,GAAGC,UAAT,IAAuBhB,UAAxB,EAAoCA,UAApC,EAAgD,KAAK9D,KAAL,CAAWzF,aAAX,CAAyByK,SAAzE,EAAoFpS,uBAApF,CAA7D;AACH;AACJ;;AACDwL,EAAAA,mBAAmB,GAAG;AAClB,SAAK6G,wBAAL;;AACA,SAAKjF,KAAL,CAAWzF,aAAX,CAAyByK,SAAzB,GAAqC,KAAK/B,UAA1C;AACH;;AACD1J,EAAAA,mBAAmB,CAACoI,MAAD,EAAS;AACxB,QAAI,KAAKnI,SAAT,EAAoB;AAChB,WAAKyJ,UAAL,GAAkB,CAAlB;AACH,KAFD,MAGK;AACD,WAAKhF,WAAL,CAAiBiH,OAAjB,GAA2B,CAA3B;;AACA,WAAKjQ,kBAAL,CAAwBwG,YAAxB;AACH;;AACD,UAAMlC,mBAAN,CAA0BoI,MAA1B;AACH;;AACDT,EAAAA,eAAe,CAACnN,KAAD,EAAQ;AACnB,WAAO,IAAIH,eAAJ,CAAoB,IAApB,EAA0BG,KAA1B,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIkR,EAAAA,wBAAwB,GAAG;AACvB,UAAME,WAAW,GAAG,KAAKlH,WAAL,CAAiB0G,UAAjB,CAA4BC,cAA5B,CAA2CL,qBAA3C,EAApB;;AACA,UAAMa,YAAY,GAAG,KAAKpQ,cAAL,CAAoBqQ,eAApB,EAArB;;AACA,UAAMC,KAAK,GAAG,KAAK5J,MAAL,EAAd;;AACA,UAAM6J,YAAY,GAAG,KAAK7M,QAAL,GACf1F,+BAA+B,GAAGH,sBADnB,GAEfA,sBAAsB,GAAG,CAF/B;AAGA,QAAIqS,OAAJ,CAPuB,CAQvB;;AACA,QAAI,KAAKxM,QAAT,EAAmB;AACfwM,MAAAA,OAAO,GAAGlS,+BAAV;AACH,KAFD,MAGK,IAAI,KAAK2D,sBAAT,EAAiC;AAClCuO,MAAAA,OAAO,GAAGrS,sBAAV;AACH,KAFI,MAGA;AACD,UAAImJ,QAAQ,GAAG,KAAKrD,eAAL,CAAqBqD,QAArB,CAA8B,CAA9B,KAAoC,KAAKlF,OAAL,CAAa0O,KAAhE;AACAN,MAAAA,OAAO,GAAGlJ,QAAQ,IAAIA,QAAQ,CAACyJ,KAArB,GAA6B3S,6BAA7B,GAA6DD,sBAAvE;AACH,KAlBsB,CAmBvB;;;AACA,QAAI,CAACyS,KAAL,EAAY;AACRJ,MAAAA,OAAO,IAAI,CAAC,CAAZ;AACH,KAtBsB,CAuBvB;;;AACA,UAAMQ,YAAY,GAAG,KAAKP,WAAW,CAACQ,IAAZ,GAAmBT,OAAnB,IAA8BI,KAAK,GAAGC,YAAH,GAAkB,CAArD,CAAL,CAArB;AACA,UAAMK,aAAa,GAAGT,WAAW,CAACU,KAAZ,GAAoBX,OAApB,GAA8BE,YAAY,CAACU,KAA3C,IAAoDR,KAAK,GAAG,CAAH,GAAOC,YAAhE,CAAtB,CAzBuB,CA0BvB;;AACA,QAAIG,YAAY,GAAG,CAAnB,EAAsB;AAClBR,MAAAA,OAAO,IAAIQ,YAAY,GAAGzS,6BAA1B;AACH,KAFD,MAGK,IAAI2S,aAAa,GAAG,CAApB,EAAuB;AACxBV,MAAAA,OAAO,IAAIU,aAAa,GAAG3S,6BAA3B;AACH,KAhCsB,CAiCvB;AACA;AACA;;;AACA,SAAKgL,WAAL,CAAiBiH,OAAjB,GAA2Bf,IAAI,CAAC4B,KAAL,CAAWb,OAAX,CAA3B;;AACA,SAAKjH,WAAL,CAAiB0G,UAAjB,CAA4BqB,cAA5B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,wBAAwB,CAACtC,aAAD,EAAgBC,YAAhB,EAA8BC,SAA9B,EAAyC;AAC7D,UAAMC,UAAU,GAAG,KAAKC,cAAL,EAAnB;;AACA,UAAMmC,sBAAsB,GAAG,CAACpC,UAAU,GAAG,KAAKQ,YAAL,CAAkB6B,MAAhC,IAA0C,CAAzE;AACA,UAAMC,mBAAmB,GAAGjC,IAAI,CAACkC,KAAL,CAAWzT,uBAAuB,GAAGkR,UAArC,CAA5B;AACA,QAAIwC,wBAAJ,CAJ6D,CAK7D;;AACA,QAAI,KAAK3P,sBAAT,EAAiC;AAC7B,aAAO,CAAP;AACH;;AACD,QAAI,KAAKsM,UAAL,KAAoB,CAAxB,EAA2B;AACvBqD,MAAAA,wBAAwB,GAAG3C,aAAa,GAAGG,UAA3C;AACH,KAFD,MAGK,IAAI,KAAKb,UAAL,KAAoBY,SAAxB,EAAmC;AACpC,YAAM0C,mBAAmB,GAAG,KAAKC,aAAL,KAAuBJ,mBAAnD;AACA,YAAMK,oBAAoB,GAAG9C,aAAa,GAAG4C,mBAA7C,CAFoC,CAGpC;AACA;;AACA,UAAIG,iBAAiB,GAAG5C,UAAU,GAAI,CAAC,KAAK0C,aAAL,KAAuB1C,UAAvB,GAAoClR,uBAArC,IAAgEkR,UAAtG,CALoC,CAMpC;AACA;AACA;AACA;;AACAwC,MAAAA,wBAAwB,GAAGG,oBAAoB,GAAG3C,UAAvB,GAAoC4C,iBAA/D;AACH,KAXI,MAYA;AACD;AACA;AACA;AACAJ,MAAAA,wBAAwB,GAAG1C,YAAY,GAAGE,UAAU,GAAG,CAAvD;AACH,KA7B4D,CA8B7D;AACA;AACA;;;AACA,WAAOK,IAAI,CAAC4B,KAAL,CAAWO,wBAAwB,GAAG,CAAC,CAA5B,GAAgCJ,sBAA3C,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIS,EAAAA,2BAA2B,CAAC9C,SAAD,EAAY;AACnC,UAAMC,UAAU,GAAG,KAAKC,cAAL,EAAnB;;AACA,UAAMqB,YAAY,GAAG,KAAKpQ,cAAL,CAAoBqQ,eAApB,EAArB;;AACA,UAAMuB,iBAAiB,GAAG,KAAKtC,YAAL,CAAkBuC,GAAlB,GAAwB5T,6BAAlD;AACA,UAAM6T,oBAAoB,GAAG1B,YAAY,CAACe,MAAb,GAAsB,KAAK7B,YAAL,CAAkByC,MAAxC,GAAiD9T,6BAA9E;AACA,UAAM+T,cAAc,GAAG7C,IAAI,CAAC8C,GAAL,CAAS,KAAK7D,QAAd,CAAvB;AACA,UAAM8D,gBAAgB,GAAG/C,IAAI,CAACC,GAAL,CAAS,KAAKoC,aAAL,KAAuB1C,UAAhC,EAA4ClR,uBAA5C,CAAzB;AACA,UAAMuU,iBAAiB,GAAGD,gBAAgB,GAAGF,cAAnB,GAAoC,KAAK1C,YAAL,CAAkB6B,MAAhF;;AACA,QAAIgB,iBAAiB,GAAGL,oBAAxB,EAA8C;AAC1C,WAAKM,cAAL,CAAoBD,iBAApB,EAAuCL,oBAAvC;AACH,KAFD,MAGK,IAAIE,cAAc,GAAGJ,iBAArB,EAAwC;AACzC,WAAKS,gBAAL,CAAsBL,cAAtB,EAAsCJ,iBAAtC,EAAyD/C,SAAzD;AACH,KAFI,MAGA;AACD,WAAKV,gBAAL,GAAwB,KAAKmE,uBAAL,EAAxB;AACH;AACJ;AACD;;;AACAF,EAAAA,cAAc,CAACD,iBAAD,EAAoBL,oBAApB,EAA0C;AACpD;AACA,UAAMS,qBAAqB,GAAGpD,IAAI,CAAC4B,KAAL,CAAWoB,iBAAiB,GAAGL,oBAA/B,CAA9B,CAFoD,CAGpD;AACA;;AACA,SAAK7D,UAAL,IAAmBsE,qBAAnB;AACA,SAAKnE,QAAL,IAAiBmE,qBAAjB;AACA,SAAKpE,gBAAL,GAAwB,KAAKmE,uBAAL,EAAxB,CAPoD,CAQpD;AACA;AACA;;AACA,QAAI,KAAKrE,UAAL,IAAmB,CAAvB,EAA0B;AACtB,WAAKA,UAAL,GAAkB,CAAlB;AACA,WAAKG,QAAL,GAAgB,CAAhB;AACA,WAAKD,gBAAL,GAAyB,gBAAzB;AACH;AACJ;AACD;;;AACAkE,EAAAA,gBAAgB,CAACL,cAAD,EAAiBJ,iBAAjB,EAAoC/C,SAApC,EAA+C;AAC3D;AACA,UAAM2D,qBAAqB,GAAGrD,IAAI,CAAC4B,KAAL,CAAWiB,cAAc,GAAGJ,iBAA5B,CAA9B,CAF2D,CAG3D;AACA;;AACA,SAAK3D,UAAL,IAAmBuE,qBAAnB;AACA,SAAKpE,QAAL,IAAiBoE,qBAAjB;AACA,SAAKrE,gBAAL,GAAwB,KAAKmE,uBAAL,EAAxB,CAP2D,CAQ3D;AACA;AACA;;AACA,QAAI,KAAKrE,UAAL,IAAmBY,SAAvB,EAAkC;AAC9B,WAAKZ,UAAL,GAAkBY,SAAlB;AACA,WAAKT,QAAL,GAAgB,CAAhB;AACA,WAAKD,gBAAL,GAAyB,aAAzB;AACA;AACH;AACJ;AACD;;;AACAuB,EAAAA,yBAAyB,GAAG;AACxB,UAAMZ,UAAU,GAAG,KAAKC,cAAL,EAAnB;;AACA,UAAM0D,KAAK,GAAG,KAAKjB,aAAL,EAAd;;AACA,UAAMkB,WAAW,GAAGvD,IAAI,CAACC,GAAL,CAASqD,KAAK,GAAG3D,UAAjB,EAA6BlR,uBAA7B,CAApB;AACA,UAAM+U,qBAAqB,GAAGF,KAAK,GAAG3D,UAAtC,CAJwB,CAKxB;;AACA,UAAMD,SAAS,GAAG8D,qBAAqB,GAAGD,WAA1C,CANwB,CAOxB;;AACA,QAAIE,oBAAJ;;AACA,QAAI,KAAK1L,KAAT,EAAgB;AACZ0L,MAAAA,oBAAoB,GAAG,CAAvB;AACH,KAFD,MAGK;AACDA,MAAAA,oBAAoB,GAAGzD,IAAI,CAACE,GAAL,CAAS,KAAKvN,OAAL,CAAa2J,OAAb,GAAuBK,OAAvB,CAA+B,KAAKnI,eAAL,CAAqBqD,QAArB,CAA8B,CAA9B,CAA/B,CAAT,EAA2E,CAA3E,CAAvB;AACH;;AACD4L,IAAAA,oBAAoB,IAAI1Y,6BAA6B,CAAC0Y,oBAAD,EAAuB,KAAK9Q,OAA5B,EAAqC,KAAKiO,YAA1C,CAArD,CAfwB,CAgBxB;AACA;;AACA,UAAMnB,YAAY,GAAG8D,WAAW,GAAG,CAAnC;AACA,SAAKzE,UAAL,GAAkB,KAAKS,uBAAL,CAA6BkE,oBAA7B,EAAmDhE,YAAnD,EAAiEC,SAAjE,CAAlB;AACA,SAAKT,QAAL,GAAgB,KAAK6C,wBAAL,CAA8B2B,oBAA9B,EAAoDhE,YAApD,EAAkEC,SAAlE,CAAhB;;AACA,SAAK8C,2BAAL,CAAiC9C,SAAjC;AACH;AACD;;;AACAyD,EAAAA,uBAAuB,GAAG;AACtB,UAAMxD,UAAU,GAAG,KAAKC,cAAL,EAAnB;;AACA,UAAMmC,sBAAsB,GAAG,CAACpC,UAAU,GAAG,KAAKQ,YAAL,CAAkB6B,MAAhC,IAA0C,CAAzE;AACA,UAAM5C,OAAO,GAAGY,IAAI,CAAC8C,GAAL,CAAS,KAAK7D,QAAd,IAA0B8C,sBAA1B,GAAmDpC,UAAU,GAAG,CAAhF;AACA,WAAQ,OAAMP,OAAQ,QAAtB;AACH;AACD;;;AACAQ,EAAAA,cAAc,GAAG;AACb,WAAO,KAAKb,gBAAL,GAAwBnQ,qBAA/B;AACH;AACD;;;AACAyT,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAK1P,OAAL,CAAasK,MAAb,GAAsB,KAAK2D,YAAL,CAAkB3D,MAA/C;AACH;;AAtSkC;;AAwSvC2B,SAAS,CAACvO,IAAV;AAAA;AAAA;AAAA,gEA/iCmG5G,EA+iCnG,uBAAsGmV,SAAtG,SAAsGA,SAAtG;AAAA;AAAA;;AACAA,SAAS,CAAC8E,IAAV,kBAhjCmGja,EAgjCnG;AAAA,QAA0FmV,SAA1F;AAAA;AAAA;AAAA;AAhjCmGnV,MAAAA,EAgjCnG,0BAG0E0G,kBAH1E;AAhjCmG1G,MAAAA,EAgjCnG,0BAGyJyB,SAHzJ;AAhjCmGzB,MAAAA,EAgjCnG,0BAGoO0B,YAHpO;AAAA;;AAAA;AAAA;;AAhjCmG1B,MAAAA,EAgjCnG,qBAhjCmGA,EAgjCnG;AAhjCmGA,MAAAA,EAgjCnG,qBAhjCmGA,EAgjCnG;AAhjCmGA,MAAAA,EAgjCnG,qBAhjCmGA,EAgjCnG;AAAA;AAAA;AAAA,sBAAmP,UAAnP,uBAAoR,MAApR,mBAA6S,MAA7S;AAAA;AAAA;AAAA;AAhjCmGA,MAAAA,EAgjCnG;AAAA,eAA0F,0BAA1F;AAAA;AAAA,eAA0F,cAA1F;AAAA;AAAA,eAA0F,aAA1F;AAAA;AAAA;;AAAA;AAhjCmGA,MAAAA,EAgjCnG;AAhjCmGA,MAAAA,EAgjCnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAhjCmGA,EAgjCnG,oBAA6jC,CACrjC;AAAE6F,IAAAA,OAAO,EAAE9D,mBAAX;AAAgC+E,IAAAA,WAAW,EAAEqO;AAA7C,GADqjC,EAErjC;AAAEtP,IAAAA,OAAO,EAAErE,2BAAX;AAAwCsF,IAAAA,WAAW,EAAEqO;AAArD,GAFqjC,CAA7jC,GAhjCmGnV,EAgjCnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAhjCmGA,MAAAA,EAgjCnG;AAhjCmGA,MAAAA,EAmjCm0B,+BAHt6B;AAhjCmGA,MAAAA,EAmjCg8B;AAAA,eAAU,YAAV;AAAA,QAHniC;AAhjCmGA,MAAAA,EAmjC2gC,4BAH9mC;AAhjCmGA,MAAAA,EAmjC6lC,0DAHhsC;AAhjCmGA,MAAAA,EAmjC2sC,0DAH9yC;AAhjCmGA,MAAAA,EAmjCm+C,eAHtkD;AAhjCmGA,MAAAA,EAmjC++C,4BAHllD;AAhjCmGA,MAAAA,EAmjCuhD,uBAH1nD;AAhjCmGA,MAAAA,EAmjC6jD,eAHhqD;AAhjCmGA,MAAAA,EAmjCqkD,eAHxqD;AAhjCmGA,MAAAA,EAmjC+kD,yEAHlrD;AAhjCmGA,MAAAA,EAmjCwmE;AAAA,eAAkB,WAAlB;AAAA;AAAA,eAA0C,iBAA1C;AAAA;AAAA,eAAwE,WAAxE;AAAA,QAH3sE;AAAA;;AAAA;AAAA,kBAhjCmGA,EAgjCnG;;AAhjCmGA,MAAAA,EAmjCi2B,mEAHp8B;AAhjCmGA,MAAAA,EAmjC2iC,aAH9oC;AAhjCmGA,MAAAA,EAmjC2iC,kCAH9oC;AAhjCmGA,MAAAA,EAmjCgkC,gCAHnqC;AAhjCmGA,MAAAA,EAmjCypC,aAH5vC;AAhjCmGA,MAAAA,EAmjCypC,iCAH5vC;AAhjCmGA,MAAAA,EAmjCkvC,aAHr1C;AAhjCmGA,MAAAA,EAmjCkvC,kCAHr1C;AAhjCmGA,MAAAA,EAmjCswD,aAHz2D;AAhjCmGA,MAAAA,EAmjCswD,+XAHz2D;AAAA;AAAA;AAAA,eAG+3KN,EAAE,CAACwa,gBAHl4K,EAGsgLpa,EAAE,CAACqa,QAHzgL,EAG2kLra,EAAE,CAACsa,YAH9kL,EAGytL1a,EAAE,CAACE,mBAH5tL,EAG4pLE,EAAE,CAACua,eAH/pL,EAG8pNva,EAAE,CAACwa,OAHjqN;AAAA;AAAA;AAAA;AAAA,eAG8uN,CAAClW,mBAAmB,CAACC,kBAArB,EAAyCD,mBAAmB,CAACG,cAA7D;AAH9uN;AAAA;AAAA;;AAIA;AAAA,qDApjCmGvE,EAojCnG,mBAA2FmV,SAA3F,EAAkH,CAAC;AACvGpO,IAAAA,IAAI,EAAEpG,SADiG;AAEvGqG,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE,YAAZ;AAA0BsT,MAAAA,QAAQ,EAAE,WAApC;AAAiDC,MAAAA,MAAM,EAAE,CAAC,UAAD,EAAa,eAAb,EAA8B,UAA9B,CAAzD;AAAoGC,MAAAA,aAAa,EAAE7Z,iBAAiB,CAAC8Z,IAArI;AAA2IC,MAAAA,eAAe,EAAE9Z,uBAAuB,CAAC+Z,MAApL;AAA4LC,MAAAA,IAAI,EAAE;AAC7L,gBAAQ,UADqL;AAE7L,6BAAqB,MAFwK;AAG7L;AACA;AACA;AACA,yBAAiB,MAN4K;AAO7L,iBAAS,YAPoL;AAQ7L,qBAAa,IARgL;AAS7L,2BAAmB,UAT0K;AAU7L,gCAAwB,kCAVqK;AAW7L,gCAAwB,WAXqK;AAY7L,6BAAqB,mBAZwK;AAa7L,gCAAwB,qBAbqK;AAc7L,gCAAwB,qBAdqK;AAe7L,+BAAuB,YAfsK;AAgB7L,mCAA2B,0BAhBkK;AAiB7L,wCAAgC,4BAjB6J;AAkB7L,uCAA+B,UAlB8J;AAmB7L,sCAA8B,YAnB+J;AAoB7L,uCAA+B,UApB8J;AAqB7L,oCAA4B,OArBiK;AAsB7L,uCAA+B,UAtB8J;AAuB7L,qBAAa,wBAvBgL;AAwB7L,mBAAW,YAxBkL;AAyB7L,kBAAU;AAzBmL,OAAlM;AA0BIC,MAAAA,UAAU,EAAE,CAAC1W,mBAAmB,CAACC,kBAArB,EAAyCD,mBAAmB,CAACG,cAA7D,CA1BhB;AA0B8F2C,MAAAA,SAAS,EAAE,CACpG;AAAErB,QAAAA,OAAO,EAAE9D,mBAAX;AAAgC+E,QAAAA,WAAW,EAAEqO;AAA7C,OADoG,EAEpG;AAAEtP,QAAAA,OAAO,EAAErE,2BAAX;AAAwCsF,QAAAA,WAAW,EAAEqO;AAArD,OAFoG,CA1BzG;AA6BI4F,MAAAA,QAAQ,EAAE,8tFA7Bd;AA6B8uFC,MAAAA,MAAM,EAAE,CAAC,8yEAAD;AA7BtvF,KAAD;AAFiG,GAAD,CAAlH,QAgC4B;AAAE9R,IAAAA,OAAO,EAAE,CAAC;AACxBnC,MAAAA,IAAI,EAAEjG,eADkB;AAExBkG,MAAAA,IAAI,EAAE,CAACvF,SAAD,EAAY;AAAEwZ,QAAAA,WAAW,EAAE;AAAf,OAAZ;AAFkB,KAAD,CAAX;AAGZ9D,IAAAA,YAAY,EAAE,CAAC;AACfpQ,MAAAA,IAAI,EAAEjG,eADS;AAEfkG,MAAAA,IAAI,EAAE,CAACtF,YAAD,EAAe;AAAEuZ,QAAAA,WAAW,EAAE;AAAf,OAAf;AAFS,KAAD,CAHF;AAMZC,IAAAA,aAAa,EAAE,CAAC;AAChBnU,MAAAA,IAAI,EAAEhG,YADU;AAEhBiG,MAAAA,IAAI,EAAE,CAACN,kBAAD;AAFU,KAAD;AANH,GAhC5B;AAAA;AA2CA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMyU,eAAN,CAAsB;;AAEtBA,eAAe,CAACvU,IAAhB;AAAA,mBAA4GuU,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBAzmCmGpb,EAymCnG;AAAA,QAA6Gmb;AAA7G;AAMAA,eAAe,CAACE,IAAhB,kBA/mCmGrb,EA+mCnG;AAAA,aAAyI,CAAC4F,mCAAD,CAAzI;AAAA,YAA0L,CAAC7F,YAAD,EAAeF,aAAf,EAA8B8B,eAA9B,EAA+CC,eAA/C,CAA1L,EAA2PM,mBAA3P,EACQF,kBADR,EAEQL,eAFR,EAGQC,eAHR;AAAA;;AAIA;AAAA,qDAnnCmG5B,EAmnCnG,mBAA2Fmb,eAA3F,EAAwH,CAAC;AAC7GpU,IAAAA,IAAI,EAAE/F,QADuG;AAE7GgG,IAAAA,IAAI,EAAE,CAAC;AACCsU,MAAAA,OAAO,EAAE,CAACvb,YAAD,EAAeF,aAAf,EAA8B8B,eAA9B,EAA+CC,eAA/C,CADV;AAEC2Z,MAAAA,OAAO,EAAE,CACLrZ,mBADK,EAELF,kBAFK,EAGLmT,SAHK,EAILxO,gBAJK,EAKLhF,eALK,EAMLC,eANK,CAFV;AAUC4Z,MAAAA,YAAY,EAAE,CAACrG,SAAD,EAAYxO,gBAAZ,CAVf;AAWCO,MAAAA,SAAS,EAAE,CAACtB,mCAAD;AAXZ,KAAD;AAFuG,GAAD,CAAxH;AAAA;AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASD,iBAAT,EAA4BL,0BAA5B,EAAwDM,mCAAxD,EAA6FL,2CAA7F,EAA0ImB,kBAA1I,EAA8JyO,SAA9J,EAAyKnP,eAAzK,EAA0LmV,eAA1L,EAA2MxU,gBAA3M,EAA6NQ,cAA7N,EAA6O/C,mBAA7O", "sourcesContent": ["import * as i7 from '@angular/cdk/overlay';\nimport { Overlay, CdkConnectedOverlay, OverlayModule } from '@angular/cdk/overlay';\nimport * as i8 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, EventEmitter, Optional, Inject, Self, Attribute, ViewChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { mixinDisableRipple, mixinTabIndex, mixinDisabled, mixinErrorState, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, take, filter, map, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst matSelectAnimations = {\n    /**\n     * This animation ensures the select's overlay panel animation (transformPanel) is called when\n     * closing the select.\n     * This is needed due to https://github.com/angular/angular/issues/23302\n     */\n    transformPanelWrap: trigger('transformPanelWrap', [\n        transition('* => void', query('@transformPanel', [animateChild()], { optional: true })),\n    ]),\n    /**\n     * This animation transforms the select's overlay panel on and off the page.\n     *\n     * When the panel is attached to the DOM, it expands its width by the amount of padding, scales it\n     * up to 100% on the Y axis, fades in its border, and translates slightly up and to the\n     * side to ensure the option text correctly overlaps the trigger text.\n     *\n     * When the panel is removed from the DOM, it simply fades out linearly.\n     */\n    transformPanel: trigger('transformPanel', [\n        state('void', style({\n            transform: 'scaleY(0.8)',\n            minWidth: '100%',\n            opacity: 0,\n        })),\n        state('showing', style({\n            opacity: 1,\n            minWidth: 'calc(100% + 32px)',\n            transform: 'scaleY(1)',\n        })),\n        state('showing-multiple', style({\n            opacity: 1,\n            minWidth: 'calc(100% + 64px)',\n            transform: 'scaleY(1)',\n        })),\n        transition('void => *', animate('120ms cubic-bezier(0, 0, 0.2, 1)')),\n        transition('* => void', animate('100ms 25ms linear', style({ opacity: 0 }))),\n    ]),\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n    return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n    return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n    return Error('`compareWith` must be a function.');\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet nextUniqueId = 0;\n/**\n * The following style constants are necessary to save here in order\n * to properly calculate the alignment of the selected option over\n * the trigger element.\n */\n/** The max height of the select's overlay panel. */\nconst SELECT_PANEL_MAX_HEIGHT = 256;\n/** The panel's padding on the x-axis. */\nconst SELECT_PANEL_PADDING_X = 16;\n/** The panel's x axis padding if it is indented (e.g. there is an option group). */\nconst SELECT_PANEL_INDENT_PADDING_X = SELECT_PANEL_PADDING_X * 2;\n/** The height of the select items in `em` units. */\nconst SELECT_ITEM_HEIGHT_EM = 3;\n// TODO(josephperrott): Revert to a constant after 2018 spec updates are fully merged.\n/**\n * Distance between the panel edge and the option text in\n * multi-selection mode.\n *\n * Calculated as:\n * (SELECT_PANEL_PADDING_X * 1.5) + 16 = 40\n * The padding is multiplied by 1.5 because the checkbox's margin is half the padding.\n * The checkbox width is 16px.\n */\nconst SELECT_MULTIPLE_PANEL_PADDING_X = SELECT_PANEL_PADDING_X * 1.5 + 16;\n/**\n * The select panel will only \"fit\" inside the viewport if it is positioned at\n * this value or more away from the viewport boundary.\n */\nconst SELECT_PANEL_VIEWPORT_PADDING = 8;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy');\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_SELECT_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n    constructor(\n    /** Reference to the select that emitted the change event. */\n    source, \n    /** Current value of the select that emitted the event. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n// Boilerplate for applying mixins to MatSelect.\n/** @docs-private */\nconst _MatSelectMixinBase = mixinDisableRipple(mixinTabIndex(mixinDisabled(mixinErrorState(class {\n    constructor(_elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl) {\n        this._elementRef = _elementRef;\n        this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n        this._parentForm = _parentForm;\n        this._parentFormGroup = _parentFormGroup;\n        this.ngControl = ngControl;\n    }\n}))));\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n}\nMatSelectTrigger.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSelectTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatSelectTrigger.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatSelectTrigger, selector: \"mat-select-trigger\", providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSelectTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-select-trigger',\n                    providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }],\n                }]\n        }] });\n/** Base class with all of the `MatSelect` functionality. */\nclass _MatSelectBase extends _MatSelectMixinBase {\n    constructor(_viewportRuler, _changeDetectorRef, _ngZone, _defaultErrorStateMatcher, elementRef, _dir, _parentForm, _parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n        super(elementRef, _defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n        this._viewportRuler = _viewportRuler;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._ngZone = _ngZone;\n        this._dir = _dir;\n        this._parentFormField = _parentFormField;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._defaultOptions = _defaultOptions;\n        /** Whether or not the overlay panel is open. */\n        this._panelOpen = false;\n        /** Comparison function to specify which option is displayed. Defaults to object equality. */\n        this._compareWith = (o1, o2) => o1 === o2;\n        /** Unique id for this input. */\n        this._uid = `mat-select-${nextUniqueId++}`;\n        /** Current `ariar-labelledby` value for the select trigger. */\n        this._triggerAriaLabelledBy = null;\n        /** Emits whenever the component is destroyed. */\n        this._destroy = new Subject();\n        /** `View -> model callback called when value changes` */\n        this._onChange = () => { };\n        /** `View -> model callback called when select has been touched` */\n        this._onTouched = () => { };\n        /** ID for the DOM node containing the select's value. */\n        this._valueId = `mat-select-value-${nextUniqueId++}`;\n        /** Emits when the panel element is finished transforming in. */\n        this._panelDoneAnimatingStream = new Subject();\n        this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n        this._focused = false;\n        /** A name for this control that can be used by `mat-form-field`. */\n        this.controlType = 'mat-select';\n        this._multiple = false;\n        this._disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n        /** Aria label of the select. */\n        this.ariaLabel = '';\n        /** Combined stream of all of the child options' change events. */\n        this.optionSelectionChanges = defer(() => {\n            const options = this.options;\n            if (options) {\n                return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n            }\n            return this._ngZone.onStable.pipe(take(1), switchMap(() => this.optionSelectionChanges));\n        });\n        /** Event emitted when the select panel has been toggled. */\n        this.openedChange = new EventEmitter();\n        /** Event emitted when the select has been opened. */\n        this._openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n        /** Event emitted when the select has been closed. */\n        this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n        /** Event emitted when the selected value has been changed by the user. */\n        this.selectionChange = new EventEmitter();\n        /**\n         * Event that emits whenever the raw value of the select changes. This is here primarily\n         * to facilitate the two-way binding for the `value` input.\n         * @docs-private\n         */\n        this.valueChange = new EventEmitter();\n        if (this.ngControl) {\n            // Note: we provide the value accessor through here, instead of\n            // the `providers` to avoid running into a circular import.\n            this.ngControl.valueAccessor = this;\n        }\n        // Note that we only want to set this when the defaults pass it in, otherwise it should\n        // stay as `undefined` so that it falls back to the default in the key manager.\n        if (_defaultOptions?.typeaheadDebounceInterval != null) {\n            this._typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n        }\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this._scrollStrategy = this._scrollStrategyFactory();\n        this.tabIndex = parseInt(tabIndex) || 0;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n    }\n    /** Whether the select is focused. */\n    get focused() {\n        return this._focused || this._panelOpen;\n    }\n    /** Placeholder to be shown if no value has been selected. */\n    get placeholder() {\n        return this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    /** Whether the component is required. */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n        this.stateChanges.next();\n    }\n    /** Whether the user should be allowed to select multiple options. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectDynamicMultipleError();\n        }\n        this._multiple = coerceBooleanProperty(value);\n    }\n    /** Whether to center the active option over the trigger. */\n    get disableOptionCentering() {\n        return this._disableOptionCentering;\n    }\n    set disableOptionCentering(value) {\n        this._disableOptionCentering = coerceBooleanProperty(value);\n    }\n    /**\n     * Function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n        return this._compareWith;\n    }\n    set compareWith(fn) {\n        if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectNonFunctionValueError();\n        }\n        this._compareWith = fn;\n        if (this._selectionModel) {\n            // A different comparator means the selection could change.\n            this._initializeSelection();\n        }\n    }\n    /** Value of the select control. */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        const hasAssigned = this._assignValue(newValue);\n        if (hasAssigned) {\n            this._onChange(newValue);\n        }\n    }\n    /** Time to wait in milliseconds after the last keystroke before moving focus to an item. */\n    get typeaheadDebounceInterval() {\n        return this._typeaheadDebounceInterval;\n    }\n    set typeaheadDebounceInterval(value) {\n        this._typeaheadDebounceInterval = coerceNumberProperty(value);\n    }\n    /** Unique id of the element. */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n        this.stateChanges.next();\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple);\n        this.stateChanges.next();\n        // We need `distinctUntilChanged` here, because some browsers will\n        // fire the animation end event twice for the same animation. See:\n        // https://github.com/angular/angular/issues/24084\n        this._panelDoneAnimatingStream\n            .pipe(distinctUntilChanged(), takeUntil(this._destroy))\n            .subscribe(() => this._panelDoneAnimating(this.panelOpen));\n    }\n    ngAfterContentInit() {\n        this._initKeyManager();\n        this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n            event.added.forEach(option => option.select());\n            event.removed.forEach(option => option.deselect());\n        });\n        this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n            this._resetOptions();\n            this._initializeSelection();\n        });\n    }\n    ngDoCheck() {\n        const newAriaLabelledby = this._getTriggerAriaLabelledby();\n        const ngControl = this.ngControl;\n        // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n        // is computed as a result of a content query which can cause this binding to trigger a\n        // \"changed after checked\" error.\n        if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n            const element = this._elementRef.nativeElement;\n            this._triggerAriaLabelledBy = newAriaLabelledby;\n            if (newAriaLabelledby) {\n                element.setAttribute('aria-labelledby', newAriaLabelledby);\n            }\n            else {\n                element.removeAttribute('aria-labelledby');\n            }\n        }\n        if (ngControl) {\n            // The disabled state might go out of sync if the form group is swapped out. See #17860.\n            if (this._previousControl !== ngControl.control) {\n                if (this._previousControl !== undefined &&\n                    ngControl.disabled !== null &&\n                    ngControl.disabled !== this.disabled) {\n                    this.disabled = ngControl.disabled;\n                }\n                this._previousControl = ngControl.control;\n            }\n            this.updateErrorState();\n        }\n    }\n    ngOnChanges(changes) {\n        // Updating the disabled state is handled by `mixinDisabled`, but we need to additionally let\n        // the parent form field know to run change detection when the disabled state changes.\n        if (changes['disabled']) {\n            this.stateChanges.next();\n        }\n        if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n            this._keyManager.withTypeAhead(this._typeaheadDebounceInterval);\n        }\n    }\n    ngOnDestroy() {\n        this._destroy.next();\n        this._destroy.complete();\n        this.stateChanges.complete();\n    }\n    /** Toggles the overlay panel open or closed. */\n    toggle() {\n        this.panelOpen ? this.close() : this.open();\n    }\n    /** Opens the overlay panel. */\n    open() {\n        if (this._canOpen()) {\n            this._panelOpen = true;\n            this._keyManager.withHorizontalOrientation(null);\n            this._highlightCorrectOption();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Closes the overlay panel and focuses the host element. */\n    close() {\n        if (this._panelOpen) {\n            this._panelOpen = false;\n            this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n            this._changeDetectorRef.markForCheck();\n            this._onTouched();\n        }\n    }\n    /**\n     * Sets the select's value. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param value New value to be written to the model.\n     */\n    writeValue(value) {\n        this._assignValue(value);\n    }\n    /**\n     * Saves a callback function to be invoked when the select's value\n     * changes from user input. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the value changes.\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Saves a callback function to be invoked when the select is blurred\n     * by the user. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the component has been touched.\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Disables the select. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param isDisabled Sets whether the component is disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    /** Whether or not the overlay panel is open. */\n    get panelOpen() {\n        return this._panelOpen;\n    }\n    /** The currently selected option. */\n    get selected() {\n        return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The value displayed in the trigger. */\n    get triggerValue() {\n        if (this.empty) {\n            return '';\n        }\n        if (this._multiple) {\n            const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n            if (this._isRtl()) {\n                selectedOptions.reverse();\n            }\n            // TODO(crisbeto): delimiter should be configurable for proper localization.\n            return selectedOptions.join(', ');\n        }\n        return this._selectionModel.selected[0].viewValue;\n    }\n    /** Whether the element is in RTL mode. */\n    _isRtl() {\n        return this._dir ? this._dir.value === 'rtl' : false;\n    }\n    /** Handles all keydown events on the select. */\n    _handleKeydown(event) {\n        if (!this.disabled) {\n            this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n        }\n    }\n    /** Handles keyboard events while the select is closed. */\n    _handleClosedKeydown(event) {\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW ||\n            keyCode === UP_ARROW ||\n            keyCode === LEFT_ARROW ||\n            keyCode === RIGHT_ARROW;\n        const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n        const manager = this._keyManager;\n        // Open the select on ALT + arrow key to match the native <select>\n        if ((!manager.isTyping() && isOpenKey && !hasModifierKey(event)) ||\n            ((this.multiple || event.altKey) && isArrowKey)) {\n            event.preventDefault(); // prevents the page from scrolling down when pressing space\n            this.open();\n        }\n        else if (!this.multiple) {\n            const previouslySelectedOption = this.selected;\n            manager.onKeydown(event);\n            const selectedOption = this.selected;\n            // Since the value has changed, we need to announce it ourselves.\n            if (selectedOption && previouslySelectedOption !== selectedOption) {\n                // We set a duration on the live announcement, because we want the live element to be\n                // cleared after a while so that users can't navigate to it using the arrow keys.\n                this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n            }\n        }\n    }\n    /** Handles keyboard events when the selected is open. */\n    _handleOpenKeydown(event) {\n        const manager = this._keyManager;\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n        const isTyping = manager.isTyping();\n        if (isArrowKey && event.altKey) {\n            // Close the select on ALT + arrow key to match the native <select>\n            event.preventDefault();\n            this.close();\n            // Don't do anything in this case if the user is typing,\n            // because the typing sequence can include the space key.\n        }\n        else if (!isTyping &&\n            (keyCode === ENTER || keyCode === SPACE) &&\n            manager.activeItem &&\n            !hasModifierKey(event)) {\n            event.preventDefault();\n            manager.activeItem._selectViaInteraction();\n        }\n        else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n            event.preventDefault();\n            const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n            this.options.forEach(option => {\n                if (!option.disabled) {\n                    hasDeselectedOptions ? option.select() : option.deselect();\n                }\n            });\n        }\n        else {\n            const previouslyFocusedIndex = manager.activeItemIndex;\n            manager.onKeydown(event);\n            if (this._multiple &&\n                isArrowKey &&\n                event.shiftKey &&\n                manager.activeItem &&\n                manager.activeItemIndex !== previouslyFocusedIndex) {\n                manager.activeItem._selectViaInteraction();\n            }\n        }\n    }\n    _onFocus() {\n        if (!this.disabled) {\n            this._focused = true;\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n     * \"blur\" to the panel when it opens, causing a false positive.\n     */\n    _onBlur() {\n        this._focused = false;\n        if (!this.disabled && !this.panelOpen) {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Callback that is invoked when the overlay panel has been attached.\n     */\n    _onAttached() {\n        this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n            this._changeDetectorRef.detectChanges();\n            this._positioningSettled();\n        });\n    }\n    /** Returns the theme to be used on the panel. */\n    _getPanelTheme() {\n        return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n    }\n    /** Whether the select has a value. */\n    get empty() {\n        return !this._selectionModel || this._selectionModel.isEmpty();\n    }\n    _initializeSelection() {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n            if (this.ngControl) {\n                this._value = this.ngControl.value;\n            }\n            this._setSelectionByValue(this._value);\n            this.stateChanges.next();\n        });\n    }\n    /**\n     * Sets the selected option based on a value. If no option can be\n     * found with the designated value, the select trigger is cleared.\n     */\n    _setSelectionByValue(value) {\n        this._selectionModel.selected.forEach(option => option.setInactiveStyles());\n        this._selectionModel.clear();\n        if (this.multiple && value) {\n            if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getMatSelectNonArrayValueError();\n            }\n            value.forEach((currentValue) => this._selectOptionByValue(currentValue));\n            this._sortValues();\n        }\n        else {\n            const correspondingOption = this._selectOptionByValue(value);\n            // Shift focus to the active item. Note that we shouldn't do this in multiple\n            // mode, because we don't know what option the user interacted with last.\n            if (correspondingOption) {\n                this._keyManager.updateActiveItem(correspondingOption);\n            }\n            else if (!this.panelOpen) {\n                // Otherwise reset the highlighted option. Note that we only want to do this while\n                // closed, because doing it while open can shift the user's focus unnecessarily.\n                this._keyManager.updateActiveItem(-1);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Finds and selects and option based on its value.\n     * @returns Option that has the corresponding value.\n     */\n    _selectOptionByValue(value) {\n        const correspondingOption = this.options.find((option) => {\n            // Skip options that are already in the model. This allows us to handle cases\n            // where the same primitive value is selected multiple times.\n            if (this._selectionModel.isSelected(option)) {\n                return false;\n            }\n            try {\n                // Treat null as a special reset value.\n                return option.value != null && this._compareWith(option.value, value);\n            }\n            catch (error) {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    // Notify developers of errors in their comparator.\n                    console.warn(error);\n                }\n                return false;\n            }\n        });\n        if (correspondingOption) {\n            this._selectionModel.select(correspondingOption);\n        }\n        return correspondingOption;\n    }\n    /** Assigns a specific value to the select. Returns whether the value has changed. */\n    _assignValue(newValue) {\n        // Always re-assign an array, because it might have been mutated.\n        if (newValue !== this._value || (this._multiple && Array.isArray(newValue))) {\n            if (this.options) {\n                this._setSelectionByValue(newValue);\n            }\n            this._value = newValue;\n            return true;\n        }\n        return false;\n    }\n    /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n    _initKeyManager() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withTypeAhead(this._typeaheadDebounceInterval)\n            .withVerticalOrientation()\n            .withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr')\n            .withHomeAndEnd()\n            .withAllowedModifierKeys(['shiftKey']);\n        this._keyManager.tabOut.pipe(takeUntil(this._destroy)).subscribe(() => {\n            if (this.panelOpen) {\n                // Select the active item when tabbing away. This is consistent with how the native\n                // select behaves. Note that we only want to do this in single selection mode.\n                if (!this.multiple && this._keyManager.activeItem) {\n                    this._keyManager.activeItem._selectViaInteraction();\n                }\n                // Restore focus to the trigger before closing. Ensures that the focus\n                // position won't be lost if the user got focus into the overlay.\n                this.focus();\n                this.close();\n            }\n        });\n        this._keyManager.change.pipe(takeUntil(this._destroy)).subscribe(() => {\n            if (this._panelOpen && this.panel) {\n                this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n            }\n            else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n                this._keyManager.activeItem._selectViaInteraction();\n            }\n        });\n    }\n    /** Drops current option subscriptions and IDs and resets from scratch. */\n    _resetOptions() {\n        const changedOrDestroyed = merge(this.options.changes, this._destroy);\n        this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n            this._onSelect(event.source, event.isUserInput);\n            if (event.isUserInput && !this.multiple && this._panelOpen) {\n                this.close();\n                this.focus();\n            }\n        });\n        // Listen to changes in the internal state of the options and react accordingly.\n        // Handles cases like the labels of the selected options changing.\n        merge(...this.options.map(option => option._stateChanges))\n            .pipe(takeUntil(changedOrDestroyed))\n            .subscribe(() => {\n            this._changeDetectorRef.markForCheck();\n            this.stateChanges.next();\n        });\n    }\n    /** Invoked when an option is clicked. */\n    _onSelect(option, isUserInput) {\n        const wasSelected = this._selectionModel.isSelected(option);\n        if (option.value == null && !this._multiple) {\n            option.deselect();\n            this._selectionModel.clear();\n            if (this.value != null) {\n                this._propagateChanges(option.value);\n            }\n        }\n        else {\n            if (wasSelected !== option.selected) {\n                option.selected\n                    ? this._selectionModel.select(option)\n                    : this._selectionModel.deselect(option);\n            }\n            if (isUserInput) {\n                this._keyManager.setActiveItem(option);\n            }\n            if (this.multiple) {\n                this._sortValues();\n                if (isUserInput) {\n                    // In case the user selected the option with their mouse, we\n                    // want to restore focus back to the trigger, in order to\n                    // prevent the select keyboard controls from clashing with\n                    // the ones from `mat-option`.\n                    this.focus();\n                }\n            }\n        }\n        if (wasSelected !== this._selectionModel.isSelected(option)) {\n            this._propagateChanges();\n        }\n        this.stateChanges.next();\n    }\n    /** Sorts the selected values in the selected based on their order in the panel. */\n    _sortValues() {\n        if (this.multiple) {\n            const options = this.options.toArray();\n            this._selectionModel.sort((a, b) => {\n                return this.sortComparator\n                    ? this.sortComparator(a, b, options)\n                    : options.indexOf(a) - options.indexOf(b);\n            });\n            this.stateChanges.next();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n        let valueToEmit = null;\n        if (this.multiple) {\n            valueToEmit = this.selected.map(option => option.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : fallbackValue;\n        }\n        this._value = valueToEmit;\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Highlights the selected item. If no option is selected, it will highlight\n     * the first item instead.\n     */\n    _highlightCorrectOption() {\n        if (this._keyManager) {\n            if (this.empty) {\n                this._keyManager.setFirstItemActive();\n            }\n            else {\n                this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n            }\n        }\n    }\n    /** Whether the panel is allowed to open. */\n    _canOpen() {\n        return !this._panelOpen && !this.disabled && this.options?.length > 0;\n    }\n    /** Focuses the select element. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Gets the aria-labelledby for the select panel. */\n    _getPanelAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Determines the `aria-activedescendant` to be set on the host. */\n    _getAriaActiveDescendant() {\n        if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n            return this._keyManager.activeItem.id;\n        }\n        return null;\n    }\n    /** Gets the aria-labelledby of the select component trigger. */\n    _getTriggerAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        let value = (labelId ? labelId + ' ' : '') + this._valueId;\n        if (this.ariaLabelledby) {\n            value += ' ' + this.ariaLabelledby;\n        }\n        return value;\n    }\n    /** Called when the overlay panel is done animating. */\n    _panelDoneAnimating(isOpen) {\n        this.openedChange.emit(isOpen);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        this._ariaDescribedby = ids.join(' ');\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        this.focus();\n        this.open();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        return this._panelOpen || !this.empty || (this._focused && !!this._placeholder);\n    }\n}\n_MatSelectBase.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatSelectBase, deps: [{ token: i1.ViewportRuler }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i2.ErrorStateMatcher }, { token: i0.ElementRef }, { token: i3.Directionality, optional: true }, { token: i4.NgForm, optional: true }, { token: i4.FormGroupDirective, optional: true }, { token: MAT_FORM_FIELD, optional: true }, { token: i4.NgControl, optional: true, self: true }, { token: 'tabindex', attribute: true }, { token: MAT_SELECT_SCROLL_STRATEGY }, { token: i5.LiveAnnouncer }, { token: MAT_SELECT_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n_MatSelectBase.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: _MatSelectBase, inputs: { panelClass: \"panelClass\", placeholder: \"placeholder\", required: \"required\", multiple: \"multiple\", disableOptionCentering: \"disableOptionCentering\", compareWith: \"compareWith\", value: \"value\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], errorStateMatcher: \"errorStateMatcher\", typeaheadDebounceInterval: \"typeaheadDebounceInterval\", sortComparator: \"sortComparator\", id: \"id\" }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", _closedStream: \"closed\", selectionChange: \"selectionChange\", valueChange: \"valueChange\" }, viewQueries: [{ propertyName: \"trigger\", first: true, predicate: [\"trigger\"], descendants: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }, { propertyName: \"_overlayDir\", first: true, predicate: CdkConnectedOverlay, descendants: true }], usesInheritance: true, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: _MatSelectBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i1.ViewportRuler }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i2.ErrorStateMatcher }, { type: i0.ElementRef }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.NgForm, decorators: [{\n                    type: Optional\n                }] }, { type: i4.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i6.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }] }, { type: i4.NgControl, decorators: [{\n                    type: Self\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SELECT_SCROLL_STRATEGY]\n                }] }, { type: i5.LiveAnnouncer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SELECT_CONFIG]\n                }] }]; }, propDecorators: { trigger: [{\n                type: ViewChild,\n                args: ['trigger']\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], _overlayDir: [{\n                type: ViewChild,\n                args: [CdkConnectedOverlay]\n            }], panelClass: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], multiple: [{\n                type: Input\n            }], disableOptionCentering: [{\n                type: Input\n            }], compareWith: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], errorStateMatcher: [{\n                type: Input\n            }], typeaheadDebounceInterval: [{\n                type: Input\n            }], sortComparator: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], selectionChange: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }] } });\nclass MatSelect extends _MatSelectBase {\n    constructor() {\n        super(...arguments);\n        /** The scroll position of the overlay panel, calculated to center the selected option. */\n        this._scrollTop = 0;\n        /** The cached font-size of the trigger element. */\n        this._triggerFontSize = 0;\n        /** The value of the select panel's transform-origin property. */\n        this._transformOrigin = 'top';\n        /**\n         * The y-offset of the overlay panel in relation to the trigger's top start corner.\n         * This must be adjusted to align the selected option text over the trigger text.\n         * when the panel opens. Will change based on the y-position of the selected option.\n         */\n        this._offsetY = 0;\n        this._positions = [\n            {\n                originX: 'start',\n                originY: 'top',\n                overlayX: 'start',\n                overlayY: 'top',\n            },\n            {\n                originX: 'start',\n                originY: 'bottom',\n                overlayX: 'start',\n                overlayY: 'bottom',\n            },\n        ];\n    }\n    /**\n     * Calculates the scroll position of the select's overlay panel.\n     *\n     * Attempts to center the selected option in the panel. If the option is\n     * too high or too low in the panel to be scrolled to the center, it clamps the\n     * scroll position to the min or max scroll positions respectively.\n     */\n    _calculateOverlayScroll(selectedIndex, scrollBuffer, maxScroll) {\n        const itemHeight = this._getItemHeight();\n        const optionOffsetFromScrollTop = itemHeight * selectedIndex;\n        const halfOptionHeight = itemHeight / 2;\n        // Starts at the optionOffsetFromScrollTop, which scrolls the option to the top of the\n        // scroll container, then subtracts the scroll buffer to scroll the option down to\n        // the center of the overlay panel. Half the option height must be re-added to the\n        // scrollTop so the option is centered based on its middle, not its top edge.\n        const optimalScrollPosition = optionOffsetFromScrollTop - scrollBuffer + halfOptionHeight;\n        return Math.min(Math.max(0, optimalScrollPosition), maxScroll);\n    }\n    ngOnInit() {\n        super.ngOnInit();\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroy))\n            .subscribe(() => {\n            if (this.panelOpen) {\n                this._triggerRect = this.trigger.nativeElement.getBoundingClientRect();\n                this._changeDetectorRef.markForCheck();\n            }\n        });\n    }\n    open() {\n        if (super._canOpen()) {\n            super.open();\n            this._triggerRect = this.trigger.nativeElement.getBoundingClientRect();\n            // Note: The computed font-size will be a string pixel value (e.g. \"16px\").\n            // `parseInt` ignores the trailing 'px' and converts this to a number.\n            this._triggerFontSize = parseInt(getComputedStyle(this.trigger.nativeElement).fontSize || '0');\n            this._calculateOverlayPosition();\n            // Set the font size on the panel element once it exists.\n            this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n                if (this._triggerFontSize &&\n                    this._overlayDir.overlayRef &&\n                    this._overlayDir.overlayRef.overlayElement) {\n                    this._overlayDir.overlayRef.overlayElement.style.fontSize = `${this._triggerFontSize}px`;\n                }\n            });\n        }\n    }\n    /** Scrolls the active option into view. */\n    _scrollOptionIntoView(index) {\n        const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n        const itemHeight = this._getItemHeight();\n        if (index === 0 && labelCount === 1) {\n            // If we've got one group label before the option and we're at the top option,\n            // scroll the list to the top. This is better UX than scrolling the list to the\n            // top of the option, because it allows the user to read the top group's label.\n            this.panel.nativeElement.scrollTop = 0;\n        }\n        else {\n            this.panel.nativeElement.scrollTop = _getOptionScrollPosition((index + labelCount) * itemHeight, itemHeight, this.panel.nativeElement.scrollTop, SELECT_PANEL_MAX_HEIGHT);\n        }\n    }\n    _positioningSettled() {\n        this._calculateOverlayOffsetX();\n        this.panel.nativeElement.scrollTop = this._scrollTop;\n    }\n    _panelDoneAnimating(isOpen) {\n        if (this.panelOpen) {\n            this._scrollTop = 0;\n        }\n        else {\n            this._overlayDir.offsetX = 0;\n            this._changeDetectorRef.markForCheck();\n        }\n        super._panelDoneAnimating(isOpen);\n    }\n    _getChangeEvent(value) {\n        return new MatSelectChange(this, value);\n    }\n    /**\n     * Sets the x-offset of the overlay panel in relation to the trigger's top start corner.\n     * This must be adjusted to align the selected option text over the trigger text when\n     * the panel opens. Will change based on LTR or RTL text direction. Note that the offset\n     * can't be calculated until the panel has been attached, because we need to know the\n     * content width in order to constrain the panel within the viewport.\n     */\n    _calculateOverlayOffsetX() {\n        const overlayRect = this._overlayDir.overlayRef.overlayElement.getBoundingClientRect();\n        const viewportSize = this._viewportRuler.getViewportSize();\n        const isRtl = this._isRtl();\n        const paddingWidth = this.multiple\n            ? SELECT_MULTIPLE_PANEL_PADDING_X + SELECT_PANEL_PADDING_X\n            : SELECT_PANEL_PADDING_X * 2;\n        let offsetX;\n        // Adjust the offset, depending on the option padding.\n        if (this.multiple) {\n            offsetX = SELECT_MULTIPLE_PANEL_PADDING_X;\n        }\n        else if (this.disableOptionCentering) {\n            offsetX = SELECT_PANEL_PADDING_X;\n        }\n        else {\n            let selected = this._selectionModel.selected[0] || this.options.first;\n            offsetX = selected && selected.group ? SELECT_PANEL_INDENT_PADDING_X : SELECT_PANEL_PADDING_X;\n        }\n        // Invert the offset in LTR.\n        if (!isRtl) {\n            offsetX *= -1;\n        }\n        // Determine how much the select overflows on each side.\n        const leftOverflow = 0 - (overlayRect.left + offsetX - (isRtl ? paddingWidth : 0));\n        const rightOverflow = overlayRect.right + offsetX - viewportSize.width + (isRtl ? 0 : paddingWidth);\n        // If the element overflows on either side, reduce the offset to allow it to fit.\n        if (leftOverflow > 0) {\n            offsetX += leftOverflow + SELECT_PANEL_VIEWPORT_PADDING;\n        }\n        else if (rightOverflow > 0) {\n            offsetX -= rightOverflow + SELECT_PANEL_VIEWPORT_PADDING;\n        }\n        // Set the offset directly in order to avoid having to go through change detection and\n        // potentially triggering \"changed after it was checked\" errors. Round the value to avoid\n        // blurry content in some browsers.\n        this._overlayDir.offsetX = Math.round(offsetX);\n        this._overlayDir.overlayRef.updatePosition();\n    }\n    /**\n     * Calculates the y-offset of the select's overlay panel in relation to the\n     * top start corner of the trigger. It has to be adjusted in order for the\n     * selected option to be aligned over the trigger when the panel opens.\n     */\n    _calculateOverlayOffsetY(selectedIndex, scrollBuffer, maxScroll) {\n        const itemHeight = this._getItemHeight();\n        const optionHeightAdjustment = (itemHeight - this._triggerRect.height) / 2;\n        const maxOptionsDisplayed = Math.floor(SELECT_PANEL_MAX_HEIGHT / itemHeight);\n        let optionOffsetFromPanelTop;\n        // Disable offset if requested by user by returning 0 as value to offset\n        if (this.disableOptionCentering) {\n            return 0;\n        }\n        if (this._scrollTop === 0) {\n            optionOffsetFromPanelTop = selectedIndex * itemHeight;\n        }\n        else if (this._scrollTop === maxScroll) {\n            const firstDisplayedIndex = this._getItemCount() - maxOptionsDisplayed;\n            const selectedDisplayIndex = selectedIndex - firstDisplayedIndex;\n            // The first item is partially out of the viewport. Therefore we need to calculate what\n            // portion of it is shown in the viewport and account for it in our offset.\n            let partialItemHeight = itemHeight - ((this._getItemCount() * itemHeight - SELECT_PANEL_MAX_HEIGHT) % itemHeight);\n            // Because the panel height is longer than the height of the options alone,\n            // there is always extra padding at the top or bottom of the panel. When\n            // scrolled to the very bottom, this padding is at the top of the panel and\n            // must be added to the offset.\n            optionOffsetFromPanelTop = selectedDisplayIndex * itemHeight + partialItemHeight;\n        }\n        else {\n            // If the option was scrolled to the middle of the panel using a scroll buffer,\n            // its offset will be the scroll buffer minus the half height that was added to\n            // center it.\n            optionOffsetFromPanelTop = scrollBuffer - itemHeight / 2;\n        }\n        // The final offset is the option's offset from the top, adjusted for the height difference,\n        // multiplied by -1 to ensure that the overlay moves in the correct direction up the page.\n        // The value is rounded to prevent some browsers from blurring the content.\n        return Math.round(optionOffsetFromPanelTop * -1 - optionHeightAdjustment);\n    }\n    /**\n     * Checks that the attempted overlay position will fit within the viewport.\n     * If it will not fit, tries to adjust the scroll position and the associated\n     * y-offset so the panel can open fully on-screen. If it still won't fit,\n     * sets the offset back to 0 to allow the fallback position to take over.\n     */\n    _checkOverlayWithinViewport(maxScroll) {\n        const itemHeight = this._getItemHeight();\n        const viewportSize = this._viewportRuler.getViewportSize();\n        const topSpaceAvailable = this._triggerRect.top - SELECT_PANEL_VIEWPORT_PADDING;\n        const bottomSpaceAvailable = viewportSize.height - this._triggerRect.bottom - SELECT_PANEL_VIEWPORT_PADDING;\n        const panelHeightTop = Math.abs(this._offsetY);\n        const totalPanelHeight = Math.min(this._getItemCount() * itemHeight, SELECT_PANEL_MAX_HEIGHT);\n        const panelHeightBottom = totalPanelHeight - panelHeightTop - this._triggerRect.height;\n        if (panelHeightBottom > bottomSpaceAvailable) {\n            this._adjustPanelUp(panelHeightBottom, bottomSpaceAvailable);\n        }\n        else if (panelHeightTop > topSpaceAvailable) {\n            this._adjustPanelDown(panelHeightTop, topSpaceAvailable, maxScroll);\n        }\n        else {\n            this._transformOrigin = this._getOriginBasedOnOption();\n        }\n    }\n    /** Adjusts the overlay panel up to fit in the viewport. */\n    _adjustPanelUp(panelHeightBottom, bottomSpaceAvailable) {\n        // Browsers ignore fractional scroll offsets, so we need to round.\n        const distanceBelowViewport = Math.round(panelHeightBottom - bottomSpaceAvailable);\n        // Scrolls the panel up by the distance it was extending past the boundary, then\n        // adjusts the offset by that amount to move the panel up into the viewport.\n        this._scrollTop -= distanceBelowViewport;\n        this._offsetY -= distanceBelowViewport;\n        this._transformOrigin = this._getOriginBasedOnOption();\n        // If the panel is scrolled to the very top, it won't be able to fit the panel\n        // by scrolling, so set the offset to 0 to allow the fallback position to take\n        // effect.\n        if (this._scrollTop <= 0) {\n            this._scrollTop = 0;\n            this._offsetY = 0;\n            this._transformOrigin = `50% bottom 0px`;\n        }\n    }\n    /** Adjusts the overlay panel down to fit in the viewport. */\n    _adjustPanelDown(panelHeightTop, topSpaceAvailable, maxScroll) {\n        // Browsers ignore fractional scroll offsets, so we need to round.\n        const distanceAboveViewport = Math.round(panelHeightTop - topSpaceAvailable);\n        // Scrolls the panel down by the distance it was extending past the boundary, then\n        // adjusts the offset by that amount to move the panel down into the viewport.\n        this._scrollTop += distanceAboveViewport;\n        this._offsetY += distanceAboveViewport;\n        this._transformOrigin = this._getOriginBasedOnOption();\n        // If the panel is scrolled to the very bottom, it won't be able to fit the\n        // panel by scrolling, so set the offset to 0 to allow the fallback position\n        // to take effect.\n        if (this._scrollTop >= maxScroll) {\n            this._scrollTop = maxScroll;\n            this._offsetY = 0;\n            this._transformOrigin = `50% top 0px`;\n            return;\n        }\n    }\n    /** Calculates the scroll position and x- and y-offsets of the overlay panel. */\n    _calculateOverlayPosition() {\n        const itemHeight = this._getItemHeight();\n        const items = this._getItemCount();\n        const panelHeight = Math.min(items * itemHeight, SELECT_PANEL_MAX_HEIGHT);\n        const scrollContainerHeight = items * itemHeight;\n        // The farthest the panel can be scrolled before it hits the bottom\n        const maxScroll = scrollContainerHeight - panelHeight;\n        // If no value is selected we open the popup to the first item.\n        let selectedOptionOffset;\n        if (this.empty) {\n            selectedOptionOffset = 0;\n        }\n        else {\n            selectedOptionOffset = Math.max(this.options.toArray().indexOf(this._selectionModel.selected[0]), 0);\n        }\n        selectedOptionOffset += _countGroupLabelsBeforeOption(selectedOptionOffset, this.options, this.optionGroups);\n        // We must maintain a scroll buffer so the selected option will be scrolled to the\n        // center of the overlay panel rather than the top.\n        const scrollBuffer = panelHeight / 2;\n        this._scrollTop = this._calculateOverlayScroll(selectedOptionOffset, scrollBuffer, maxScroll);\n        this._offsetY = this._calculateOverlayOffsetY(selectedOptionOffset, scrollBuffer, maxScroll);\n        this._checkOverlayWithinViewport(maxScroll);\n    }\n    /** Sets the transform origin point based on the selected option. */\n    _getOriginBasedOnOption() {\n        const itemHeight = this._getItemHeight();\n        const optionHeightAdjustment = (itemHeight - this._triggerRect.height) / 2;\n        const originY = Math.abs(this._offsetY) - optionHeightAdjustment + itemHeight / 2;\n        return `50% ${originY}px 0px`;\n    }\n    /** Calculates the height of the select's options. */\n    _getItemHeight() {\n        return this._triggerFontSize * SELECT_ITEM_HEIGHT_EM;\n    }\n    /** Calculates the amount of items in the select. This includes options and group labels. */\n    _getItemCount() {\n        return this.options.length + this.optionGroups.length;\n    }\n}\nMatSelect.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSelect, deps: null, target: i0.ɵɵFactoryTarget.Component });\nMatSelect.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatSelect, selector: \"mat-select\", inputs: { disabled: \"disabled\", disableRipple: \"disableRipple\", tabIndex: \"tabIndex\" }, host: { attributes: { \"role\": \"combobox\", \"aria-autocomplete\": \"none\", \"aria-haspopup\": \"true\" }, listeners: { \"keydown\": \"_handleKeydown($event)\", \"focus\": \"_onFocus()\", \"blur\": \"_onBlur()\" }, properties: { \"attr.id\": \"id\", \"attr.tabindex\": \"tabIndex\", \"attr.aria-controls\": \"panelOpen ? id + \\\"-panel\\\" : null\", \"attr.aria-expanded\": \"panelOpen\", \"attr.aria-label\": \"ariaLabel || null\", \"attr.aria-required\": \"required.toString()\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"attr.aria-describedby\": \"_ariaDescribedby || null\", \"attr.aria-activedescendant\": \"_getAriaActiveDescendant()\", \"class.mat-select-disabled\": \"disabled\", \"class.mat-select-invalid\": \"errorState\", \"class.mat-select-required\": \"required\", \"class.mat-select-empty\": \"empty\", \"class.mat-select-multiple\": \"multiple\" }, classAttribute: \"mat-select\" }, providers: [\n        { provide: MatFormFieldControl, useExisting: MatSelect },\n        { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n    ], queries: [{ propertyName: \"customTrigger\", first: true, predicate: MAT_SELECT_TRIGGER, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }, { propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }], exportAs: [\"matSelect\"], usesInheritance: true, ngImport: i0, template: \"<!--\\n Note that the select trigger element specifies `aria-owns` pointing to the listbox overlay.\\n While aria-owns is not required for the ARIA 1.2 `role=\\\"combobox\\\"` interaction pattern,\\n it fixes an issue with VoiceOver when the select appears inside of an `aria-model=\\\"true\\\"`\\n element (e.g. a dialog). Without this `aria-owns`, the `aria-modal` on a dialog prevents\\n VoiceOver from \\\"seeing\\\" the select's listbox overlay for aria-activedescendant.\\n Using `aria-owns` re-parents the select overlay so that it works again.\\n See https://github.com/angular/components/issues/20694\\n-->\\n<div cdk-overlay-origin\\n     [attr.aria-owns]=\\\"panelOpen ? id + '-panel' : null\\\"\\n     class=\\\"mat-select-trigger\\\"\\n     (click)=\\\"toggle()\\\"\\n     #origin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n  <div class=\\\"mat-select-value\\\" [ngSwitch]=\\\"empty\\\" [attr.id]=\\\"_valueId\\\">\\n    <span class=\\\"mat-select-placeholder mat-select-min-line\\\" *ngSwitchCase=\\\"true\\\">{{placeholder}}</span>\\n    <span class=\\\"mat-select-value-text\\\" *ngSwitchCase=\\\"false\\\" [ngSwitch]=\\\"!!customTrigger\\\">\\n      <span class=\\\"mat-select-min-line\\\" *ngSwitchDefault>{{triggerValue}}</span>\\n      <ng-content select=\\\"mat-select-trigger\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n    </span>\\n  </div>\\n\\n  <div class=\\\"mat-select-arrow-wrapper\\\"><div class=\\\"mat-select-arrow\\\"></div></div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"origin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayMinWidth]=\\\"_triggerRect?.width!\\\"\\n  [cdkConnectedOverlayOffsetY]=\\\"_offsetY\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div class=\\\"mat-select-panel-wrap\\\" [@transformPanelWrap]>\\n    <div\\n      #panel\\n      role=\\\"listbox\\\"\\n      tabindex=\\\"-1\\\"\\n      class=\\\"mat-select-panel {{ _getPanelTheme() }}\\\"\\n      [attr.id]=\\\"id + '-panel'\\\"\\n      [attr.aria-multiselectable]=\\\"multiple\\\"\\n      [attr.aria-label]=\\\"ariaLabel || null\\\"\\n      [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n      [ngClass]=\\\"panelClass\\\"\\n      [@transformPanel]=\\\"multiple ? 'showing-multiple' : 'showing'\\\"\\n      (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n      [style.transformOrigin]=\\\"_transformOrigin\\\"\\n      [style.font-size.px]=\\\"_triggerFontSize\\\"\\n      (keydown)=\\\"_handleKeydown($event)\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-select{display:inline-block;width:100%;outline:none}.mat-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-select-disabled .mat-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-select-arrow-wrapper{height:16px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-select-arrow-wrapper{transform:translateY(-50%)}.mat-form-field-appearance-outline .mat-select-arrow-wrapper{transform:translateY(-25%)}.mat-form-field-appearance-standard.mat-form-field-has-label .mat-select:not(.mat-select-empty) .mat-select-arrow-wrapper{transform:translateY(-50%)}.mat-form-field-appearance-standard .mat-select.mat-select-empty .mat-select-arrow-wrapper{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable.mat-form-field-appearance-standard .mat-select.mat-select-empty .mat-select-arrow-wrapper{transition:none}.mat-select-arrow{width:0;height:0;border-left:5px solid transparent;border-right:5px solid transparent;border-top:5px solid;margin:0 4px}.mat-form-field.mat-focused .mat-select-arrow{transform:translateX(0)}.mat-select-panel-wrap{flex-basis:100%}.mat-select-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;padding-top:0;padding-bottom:0;max-height:256px;min-width:100%;border-radius:4px;outline:0}.cdk-high-contrast-active .mat-select-panel{outline:solid 1px}.mat-select-panel .mat-optgroup-label,.mat-select-panel .mat-option{font-size:inherit;line-height:3em;height:3em}.mat-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-form-field-flex{cursor:pointer}.mat-form-field-type-mat-select .mat-form-field-label{width:calc(100% - 18px)}.mat-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable .mat-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-select-placeholder{color:transparent;-webkit-text-fill-color:transparent;transition:none;display:block}.mat-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\\n\"], directives: [{ type: i7.CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"] }, { type: i8.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { type: i8.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { type: i8.NgSwitchDefault, selector: \"[ngSwitchDefault]\" }, { type: i7.CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: [\"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPositionStrategy\", \"cdkConnectedOverlayOffsetX\", \"cdkConnectedOverlayOffsetY\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayHeight\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayMinHeight\", \"cdkConnectedOverlayBackdropClass\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayViewportMargin\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayTransformOriginOn\", \"cdkConnectedOverlayHasBackdrop\", \"cdkConnectedOverlayLockPosition\", \"cdkConnectedOverlayFlexibleDimensions\", \"cdkConnectedOverlayGrowAfterOpen\", \"cdkConnectedOverlayPush\"], outputs: [\"backdropClick\", \"positionChange\", \"attach\", \"detach\", \"overlayKeydown\", \"overlayOutsideClick\"], exportAs: [\"cdkConnectedOverlay\"] }, { type: i8.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], animations: [matSelectAnimations.transformPanelWrap, matSelectAnimations.transformPanel], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSelect, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-select', exportAs: 'matSelect', inputs: ['disabled', 'disableRipple', 'tabIndex'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'role': 'combobox',\n                        'aria-autocomplete': 'none',\n                        // TODO(crisbeto): the value for aria-haspopup should be `listbox`, but currently it's difficult\n                        // to sync into Google, because of an outdated automated a11y check which flags it as an invalid\n                        // value. At some point we should try to switch it back to being `listbox`.\n                        'aria-haspopup': 'true',\n                        'class': 'mat-select',\n                        '[attr.id]': 'id',\n                        '[attr.tabindex]': 'tabIndex',\n                        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n                        '[attr.aria-expanded]': 'panelOpen',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.aria-required]': 'required.toString()',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[attr.aria-describedby]': '_ariaDescribedby || null',\n                        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n                        '[class.mat-select-disabled]': 'disabled',\n                        '[class.mat-select-invalid]': 'errorState',\n                        '[class.mat-select-required]': 'required',\n                        '[class.mat-select-empty]': 'empty',\n                        '[class.mat-select-multiple]': 'multiple',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(focus)': '_onFocus()',\n                        '(blur)': '_onBlur()',\n                    }, animations: [matSelectAnimations.transformPanelWrap, matSelectAnimations.transformPanel], providers: [\n                        { provide: MatFormFieldControl, useExisting: MatSelect },\n                        { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n                    ], template: \"<!--\\n Note that the select trigger element specifies `aria-owns` pointing to the listbox overlay.\\n While aria-owns is not required for the ARIA 1.2 `role=\\\"combobox\\\"` interaction pattern,\\n it fixes an issue with VoiceOver when the select appears inside of an `aria-model=\\\"true\\\"`\\n element (e.g. a dialog). Without this `aria-owns`, the `aria-modal` on a dialog prevents\\n VoiceOver from \\\"seeing\\\" the select's listbox overlay for aria-activedescendant.\\n Using `aria-owns` re-parents the select overlay so that it works again.\\n See https://github.com/angular/components/issues/20694\\n-->\\n<div cdk-overlay-origin\\n     [attr.aria-owns]=\\\"panelOpen ? id + '-panel' : null\\\"\\n     class=\\\"mat-select-trigger\\\"\\n     (click)=\\\"toggle()\\\"\\n     #origin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n  <div class=\\\"mat-select-value\\\" [ngSwitch]=\\\"empty\\\" [attr.id]=\\\"_valueId\\\">\\n    <span class=\\\"mat-select-placeholder mat-select-min-line\\\" *ngSwitchCase=\\\"true\\\">{{placeholder}}</span>\\n    <span class=\\\"mat-select-value-text\\\" *ngSwitchCase=\\\"false\\\" [ngSwitch]=\\\"!!customTrigger\\\">\\n      <span class=\\\"mat-select-min-line\\\" *ngSwitchDefault>{{triggerValue}}</span>\\n      <ng-content select=\\\"mat-select-trigger\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n    </span>\\n  </div>\\n\\n  <div class=\\\"mat-select-arrow-wrapper\\\"><div class=\\\"mat-select-arrow\\\"></div></div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"origin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayMinWidth]=\\\"_triggerRect?.width!\\\"\\n  [cdkConnectedOverlayOffsetY]=\\\"_offsetY\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div class=\\\"mat-select-panel-wrap\\\" [@transformPanelWrap]>\\n    <div\\n      #panel\\n      role=\\\"listbox\\\"\\n      tabindex=\\\"-1\\\"\\n      class=\\\"mat-select-panel {{ _getPanelTheme() }}\\\"\\n      [attr.id]=\\\"id + '-panel'\\\"\\n      [attr.aria-multiselectable]=\\\"multiple\\\"\\n      [attr.aria-label]=\\\"ariaLabel || null\\\"\\n      [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n      [ngClass]=\\\"panelClass\\\"\\n      [@transformPanel]=\\\"multiple ? 'showing-multiple' : 'showing'\\\"\\n      (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n      [style.transformOrigin]=\\\"_transformOrigin\\\"\\n      [style.font-size.px]=\\\"_triggerFontSize\\\"\\n      (keydown)=\\\"_handleKeydown($event)\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-select{display:inline-block;width:100%;outline:none}.mat-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-select-disabled .mat-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-select-arrow-wrapper{height:16px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mat-select-arrow-wrapper{transform:translateY(-50%)}.mat-form-field-appearance-outline .mat-select-arrow-wrapper{transform:translateY(-25%)}.mat-form-field-appearance-standard.mat-form-field-has-label .mat-select:not(.mat-select-empty) .mat-select-arrow-wrapper{transform:translateY(-50%)}.mat-form-field-appearance-standard .mat-select.mat-select-empty .mat-select-arrow-wrapper{transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable.mat-form-field-appearance-standard .mat-select.mat-select-empty .mat-select-arrow-wrapper{transition:none}.mat-select-arrow{width:0;height:0;border-left:5px solid transparent;border-right:5px solid transparent;border-top:5px solid;margin:0 4px}.mat-form-field.mat-focused .mat-select-arrow{transform:translateX(0)}.mat-select-panel-wrap{flex-basis:100%}.mat-select-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;padding-top:0;padding-bottom:0;max-height:256px;min-width:100%;border-radius:4px;outline:0}.cdk-high-contrast-active .mat-select-panel{outline:solid 1px}.mat-select-panel .mat-optgroup-label,.mat-select-panel .mat-option{font-size:inherit;line-height:3em;height:3em}.mat-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-form-field-flex{cursor:pointer}.mat-form-field-type-mat-select .mat-form-field-label{width:calc(100% - 18px)}.mat-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}._mat-animation-noopable .mat-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-select-placeholder{color:transparent;-webkit-text-fill-color:transparent;transition:none;display:block}.mat-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}\\n\"] }]\n        }], propDecorators: { options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], customTrigger: [{\n                type: ContentChild,\n                args: [MAT_SELECT_TRIGGER]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatSelectModule {\n}\nMatSelectModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSelectModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatSelectModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSelectModule, declarations: [MatSelect, MatSelectTrigger], imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule], exports: [CdkScrollableModule,\n        MatFormFieldModule,\n        MatSelect,\n        MatSelectTrigger,\n        MatOptionModule,\n        MatCommonModule] });\nMatSelectModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSelectModule, providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER], imports: [[CommonModule, OverlayModule, MatOptionModule, MatCommonModule], CdkScrollableModule,\n        MatFormFieldModule,\n        MatOptionModule,\n        MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSelectModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, OverlayModule, MatOptionModule, MatCommonModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatFormFieldModule,\n                        MatSelect,\n                        MatSelectTrigger,\n                        MatOptionModule,\n                        MatCommonModule,\n                    ],\n                    declarations: [MatSelect, MatSelectTrigger],\n                    providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, _MatSelectBase, matSelectAnimations };\n"]}, "metadata": {}, "sourceType": "module"}
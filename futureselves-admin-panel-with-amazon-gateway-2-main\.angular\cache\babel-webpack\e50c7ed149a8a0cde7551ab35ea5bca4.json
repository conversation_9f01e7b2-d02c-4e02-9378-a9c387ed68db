{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng-lts/dom';\nimport { ZIndexUtils } from 'primeng-lts/utils';\nimport * as i1 from 'primeng-lts/api';\n\nclass Tooltip {\n  constructor(el, zone, config) {\n    this.el = el;\n    this.zone = zone;\n    this.config = config;\n    this.escape = true;\n    this._tooltipOptions = {\n      tooltipPosition: 'right',\n      tooltipEvent: 'hover',\n      appendTo: 'body',\n      tooltipZIndex: 'auto',\n      escape: false,\n      positionTop: 0,\n      positionLeft: 0\n    };\n  }\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(val) {\n    this._disabled = val;\n    this.deactivate();\n  }\n\n  ngAfterViewInit() {\n    this.zone.runOutsideAngular(() => {\n      if (this.getOption('tooltipEvent') === 'hover') {\n        this.mouseEnterListener = this.onMouseEnter.bind(this);\n        this.mouseLeaveListener = this.onMouseLeave.bind(this);\n        this.clickListener = this.onClick.bind(this);\n        this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n        this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n        this.el.nativeElement.addEventListener('click', this.clickListener);\n      } else if (this.getOption('tooltipEvent') === 'focus') {\n        this.focusListener = this.onFocus.bind(this);\n        this.blurListener = this.onBlur.bind(this);\n        this.el.nativeElement.addEventListener('focus', this.focusListener);\n        this.el.nativeElement.addEventListener('blur', this.blurListener);\n      }\n    });\n  }\n\n  ngOnChanges(simpleChange) {\n    if (simpleChange.tooltipPosition) {\n      this.setOption({\n        tooltipPosition: simpleChange.tooltipPosition.currentValue\n      });\n    }\n\n    if (simpleChange.tooltipEvent) {\n      this.setOption({\n        tooltipEvent: simpleChange.tooltipEvent.currentValue\n      });\n    }\n\n    if (simpleChange.appendTo) {\n      this.setOption({\n        appendTo: simpleChange.appendTo.currentValue\n      });\n    }\n\n    if (simpleChange.positionStyle) {\n      this.setOption({\n        positionStyle: simpleChange.positionStyle.currentValue\n      });\n    }\n\n    if (simpleChange.tooltipStyleClass) {\n      this.setOption({\n        tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue\n      });\n    }\n\n    if (simpleChange.tooltipZIndex) {\n      this.setOption({\n        tooltipZIndex: simpleChange.tooltipZIndex.currentValue\n      });\n    }\n\n    if (simpleChange.escape) {\n      this.setOption({\n        escape: simpleChange.escape.currentValue\n      });\n    }\n\n    if (simpleChange.showDelay) {\n      this.setOption({\n        showDelay: simpleChange.showDelay.currentValue\n      });\n    }\n\n    if (simpleChange.hideDelay) {\n      this.setOption({\n        hideDelay: simpleChange.hideDelay.currentValue\n      });\n    }\n\n    if (simpleChange.life) {\n      this.setOption({\n        life: simpleChange.life.currentValue\n      });\n    }\n\n    if (simpleChange.positionTop) {\n      this.setOption({\n        positionTop: simpleChange.positionTop.currentValue\n      });\n    }\n\n    if (simpleChange.positionLeft) {\n      this.setOption({\n        positionLeft: simpleChange.positionLeft.currentValue\n      });\n    }\n\n    if (simpleChange.disabled) {\n      this.setOption({\n        disabled: simpleChange.disabled.currentValue\n      });\n    }\n\n    if (simpleChange.text) {\n      this.setOption({\n        tooltipLabel: simpleChange.text.currentValue\n      });\n\n      if (this.active) {\n        if (simpleChange.text.currentValue) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n\n    if (simpleChange.tooltipOptions) {\n      this._tooltipOptions = Object.assign(Object.assign({}, this._tooltipOptions), simpleChange.tooltipOptions.currentValue);\n      this.deactivate();\n\n      if (this.active) {\n        if (this.getOption('tooltipLabel')) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n  }\n\n  onMouseEnter(e) {\n    if (!this.container && !this.showTimeout) {\n      this.activate();\n    }\n  }\n\n  onMouseLeave(e) {\n    this.deactivate();\n  }\n\n  onFocus(e) {\n    this.activate();\n  }\n\n  onBlur(e) {\n    this.deactivate();\n  }\n\n  onClick(e) {\n    this.deactivate();\n  }\n\n  activate() {\n    this.active = true;\n    this.clearHideTimeout();\n    if (this.getOption('showDelay')) this.showTimeout = setTimeout(() => {\n      this.show();\n    }, this.getOption('showDelay'));else this.show();\n\n    if (this.getOption('life')) {\n      let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n      this.hideTimeout = setTimeout(() => {\n        this.hide();\n      }, duration);\n    }\n  }\n\n  deactivate() {\n    this.active = false;\n    this.clearShowTimeout();\n\n    if (this.getOption('hideDelay')) {\n      this.clearHideTimeout(); //life timeout\n\n      this.hideTimeout = setTimeout(() => {\n        this.hide();\n      }, this.getOption('hideDelay'));\n    } else {\n      this.hide();\n    }\n  }\n\n  create() {\n    if (this.container) {\n      this.clearHideTimeout();\n      this.remove();\n    }\n\n    this.container = document.createElement('div');\n    let tooltipArrow = document.createElement('div');\n    tooltipArrow.className = 'p-tooltip-arrow';\n    this.container.appendChild(tooltipArrow);\n    this.tooltipText = document.createElement('div');\n    this.tooltipText.className = 'p-tooltip-text';\n    this.updateText();\n\n    if (this.getOption('positionStyle')) {\n      this.container.style.position = this.getOption('positionStyle');\n    }\n\n    this.container.appendChild(this.tooltipText);\n    if (this.getOption('appendTo') === 'body') document.body.appendChild(this.container);else if (this.getOption('appendTo') === 'target') DomHandler.appendChild(this.container, this.el.nativeElement);else DomHandler.appendChild(this.container, this.getOption('appendTo'));\n    this.container.style.display = 'inline-block';\n  }\n\n  show() {\n    if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n      return;\n    }\n\n    this.create();\n    this.align();\n    DomHandler.fadeIn(this.container, 250);\n    if (this.getOption('tooltipZIndex') === 'auto') ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);else this.container.style.zIndex = this.getOption('tooltipZIndex');\n    this.bindDocumentResizeListener();\n    this.bindScrollListener();\n  }\n\n  hide() {\n    if (this.getOption('tooltipZIndex') === 'auto') {\n      ZIndexUtils.clear(this.container);\n    }\n\n    this.remove();\n  }\n\n  updateText() {\n    if (this.getOption('escape')) {\n      this.tooltipText.innerHTML = '';\n      this.tooltipText.appendChild(document.createTextNode(this.getOption('tooltipLabel')));\n    } else {\n      this.tooltipText.innerHTML = this.getOption('tooltipLabel');\n    }\n  }\n\n  align() {\n    let position = this.getOption('tooltipPosition');\n\n    switch (position) {\n      case 'top':\n        this.alignTop();\n\n        if (this.isOutOfBounds()) {\n          this.alignBottom();\n\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n\n        break;\n\n      case 'bottom':\n        this.alignBottom();\n\n        if (this.isOutOfBounds()) {\n          this.alignTop();\n\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n\n        break;\n\n      case 'left':\n        this.alignLeft();\n\n        if (this.isOutOfBounds()) {\n          this.alignRight();\n\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n\n        break;\n\n      case 'right':\n        this.alignRight();\n\n        if (this.isOutOfBounds()) {\n          this.alignLeft();\n\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n\n        break;\n    }\n  }\n\n  getHostOffset() {\n    if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n      let offset = this.el.nativeElement.getBoundingClientRect();\n      let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n      let targetTop = offset.top + DomHandler.getWindowScrollTop();\n      return {\n        left: targetLeft,\n        top: targetTop\n      };\n    } else {\n      return {\n        left: 0,\n        top: 0\n      };\n    }\n  }\n\n  alignRight() {\n    this.preAlign('right');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + DomHandler.getOuterWidth(this.el.nativeElement);\n    let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n\n  alignLeft() {\n    this.preAlign('left');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n    let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n\n  alignTop() {\n    this.preAlign('top');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n\n  alignBottom() {\n    this.preAlign('bottom');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n\n  setOption(option) {\n    this._tooltipOptions = Object.assign(Object.assign({}, this._tooltipOptions), option);\n  }\n\n  getOption(option) {\n    return this._tooltipOptions[option];\n  }\n\n  preAlign(position) {\n    this.container.style.left = -999 + 'px';\n    this.container.style.top = -999 + 'px';\n    let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n    this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n  }\n\n  isOutOfBounds() {\n    let offset = this.container.getBoundingClientRect();\n    let targetTop = offset.top;\n    let targetLeft = offset.left;\n    let width = DomHandler.getOuterWidth(this.container);\n    let height = DomHandler.getOuterHeight(this.container);\n    let viewport = DomHandler.getViewport();\n    return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n  }\n\n  onWindowResize(e) {\n    this.hide();\n  }\n\n  bindDocumentResizeListener() {\n    this.zone.runOutsideAngular(() => {\n      this.resizeListener = this.onWindowResize.bind(this);\n      window.addEventListener('resize', this.resizeListener);\n    });\n  }\n\n  unbindDocumentResizeListener() {\n    if (this.resizeListener) {\n      window.removeEventListener('resize', this.resizeListener);\n      this.resizeListener = null;\n    }\n  }\n\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n        if (this.container) {\n          this.hide();\n        }\n      });\n    }\n\n    this.scrollHandler.bindScrollListener();\n  }\n\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n\n  unbindEvents() {\n    if (this.getOption('tooltipEvent') === 'hover') {\n      this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n      this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n      this.el.nativeElement.removeEventListener('click', this.clickListener);\n    } else if (this.getOption('tooltipEvent') === 'focus') {\n      this.el.nativeElement.removeEventListener('focus', this.focusListener);\n      this.el.nativeElement.removeEventListener('blur', this.blurListener);\n    }\n\n    this.unbindDocumentResizeListener();\n  }\n\n  remove() {\n    if (this.container && this.container.parentElement) {\n      if (this.getOption('appendTo') === 'body') document.body.removeChild(this.container);else if (this.getOption('appendTo') === 'target') this.el.nativeElement.removeChild(this.container);else DomHandler.removeChild(this.container, this.getOption('appendTo'));\n    }\n\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.clearTimeouts();\n    this.container = null;\n    this.scrollHandler = null;\n  }\n\n  clearShowTimeout() {\n    if (this.showTimeout) {\n      clearTimeout(this.showTimeout);\n      this.showTimeout = null;\n    }\n  }\n\n  clearHideTimeout() {\n    if (this.hideTimeout) {\n      clearTimeout(this.hideTimeout);\n      this.hideTimeout = null;\n    }\n  }\n\n  clearTimeouts() {\n    this.clearShowTimeout();\n    this.clearHideTimeout();\n  }\n\n  ngOnDestroy() {\n    this.unbindEvents();\n\n    if (this.container) {\n      ZIndexUtils.clear(this.container);\n    }\n\n    this.remove();\n\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n  }\n\n}\n\nTooltip.ɵfac = function Tooltip_Factory(t) {\n  return new (t || Tooltip)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n};\n\nTooltip.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: Tooltip,\n  selectors: [[\"\", \"pTooltip\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    tooltipPosition: \"tooltipPosition\",\n    tooltipEvent: \"tooltipEvent\",\n    appendTo: \"appendTo\",\n    positionStyle: \"positionStyle\",\n    tooltipStyleClass: \"tooltipStyleClass\",\n    tooltipZIndex: \"tooltipZIndex\",\n    escape: \"escape\",\n    showDelay: \"showDelay\",\n    hideDelay: \"hideDelay\",\n    life: \"life\",\n    positionTop: \"positionTop\",\n    positionLeft: \"positionLeft\",\n    text: [\"pTooltip\", \"text\"],\n    disabled: [\"tooltipDisabled\", \"disabled\"],\n    tooltipOptions: \"tooltipOptions\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[pTooltip]',\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.PrimeNGConfig\n    }];\n  }, {\n    tooltipPosition: [{\n      type: Input\n    }],\n    tooltipEvent: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    positionStyle: [{\n      type: Input\n    }],\n    tooltipStyleClass: [{\n      type: Input\n    }],\n    tooltipZIndex: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input\n    }],\n    showDelay: [{\n      type: Input\n    }],\n    hideDelay: [{\n      type: Input\n    }],\n    life: [{\n      type: Input\n    }],\n    positionTop: [{\n      type: Input\n    }],\n    positionLeft: [{\n      type: Input\n    }],\n    text: [{\n      type: Input,\n      args: ['pTooltip']\n    }],\n    disabled: [{\n      type: Input,\n      args: [\"tooltipDisabled\"]\n    }],\n    tooltipOptions: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TooltipModule {}\n\nTooltipModule.ɵfac = function TooltipModule_Factory(t) {\n  return new (t || TooltipModule)();\n};\n\nTooltipModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TooltipModule\n});\nTooltipModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Tooltip],\n      declarations: [Tooltip]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Tooltip, TooltipModule };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/primeng-lts/fesm2015/primeng-lts-tooltip.js"], "names": ["i0", "Directive", "Input", "NgModule", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "ZIndexUtils", "i1", "<PERSON><PERSON><PERSON>", "constructor", "el", "zone", "config", "escape", "_tooltipOptions", "tooltipPosition", "tooltipEvent", "appendTo", "tooltipZIndex", "positionTop", "positionLeft", "disabled", "_disabled", "val", "deactivate", "ngAfterViewInit", "runOutsideAngular", "getOption", "mouseEnterListener", "onMouseEnter", "bind", "mouseLeaveListener", "onMouseLeave", "clickListener", "onClick", "nativeElement", "addEventListener", "focusListener", "onFocus", "blurListener", "onBlur", "ngOnChanges", "simpleChange", "setOption", "currentValue", "positionStyle", "tooltipStyleClass", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "life", "text", "tooltipLabel", "active", "container", "offsetParent", "updateText", "align", "show", "hide", "tooltipOptions", "Object", "assign", "e", "showTimeout", "activate", "clearHideTimeout", "setTimeout", "duration", "hideTimeout", "clearShowTimeout", "create", "remove", "document", "createElement", "tooltipArrow", "className", "append<PERSON><PERSON><PERSON>", "tooltipText", "style", "position", "body", "display", "fadeIn", "set", "zIndex", "tooltip", "bindDocumentResizeListener", "bindScrollListener", "clear", "innerHTML", "createTextNode", "alignTop", "isOutOfBounds", "alignBottom", "alignRight", "alignLeft", "getHostOffset", "offset", "getBoundingClientRect", "targetLeft", "left", "getWindowScrollLeft", "targetTop", "top", "getWindowScrollTop", "preAlign", "hostOffset", "getOuterWidth", "getOuterHeight", "option", "defaultClassName", "width", "height", "viewport", "getViewport", "onWindowResize", "resizeListener", "window", "unbindDocumentResizeListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "unbindScrollListener", "unbindEvents", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeouts", "clearTimeout", "ngOnDestroy", "destroy", "ɵfac", "ElementRef", "NgZone", "PrimeNGConfig", "ɵdir", "type", "args", "selector", "host", "TooltipModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,KAApB,EAA2BC,QAA3B,QAA2C,eAA3C;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,EAAqBC,6BAArB,QAA0D,iBAA1D;AACA,SAASC,WAAT,QAA4B,mBAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;;AAEA,MAAMC,OAAN,CAAc;AACVC,EAAAA,WAAW,CAACC,EAAD,EAAKC,IAAL,EAAWC,MAAX,EAAmB;AAC1B,SAAKF,EAAL,GAAUA,EAAV;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,MAAL,GAAc,IAAd;AACA,SAAKC,eAAL,GAAuB;AACnBC,MAAAA,eAAe,EAAE,OADE;AAEnBC,MAAAA,YAAY,EAAE,OAFK;AAGnBC,MAAAA,QAAQ,EAAE,MAHS;AAInBC,MAAAA,aAAa,EAAE,MAJI;AAKnBL,MAAAA,MAAM,EAAE,KALW;AAMnBM,MAAAA,WAAW,EAAE,CANM;AAOnBC,MAAAA,YAAY,EAAE;AAPK,KAAvB;AASH;;AACW,MAARC,QAAQ,GAAG;AACX,WAAO,KAAKC,SAAZ;AACH;;AACW,MAARD,QAAQ,CAACE,GAAD,EAAM;AACd,SAAKD,SAAL,GAAiBC,GAAjB;AACA,SAAKC,UAAL;AACH;;AACDC,EAAAA,eAAe,GAAG;AACd,SAAKd,IAAL,CAAUe,iBAAV,CAA4B,MAAM;AAC9B,UAAI,KAAKC,SAAL,CAAe,cAAf,MAAmC,OAAvC,EAAgD;AAC5C,aAAKC,kBAAL,GAA0B,KAAKC,YAAL,CAAkBC,IAAlB,CAAuB,IAAvB,CAA1B;AACA,aAAKC,kBAAL,GAA0B,KAAKC,YAAL,CAAkBF,IAAlB,CAAuB,IAAvB,CAA1B;AACA,aAAKG,aAAL,GAAqB,KAAKC,OAAL,CAAaJ,IAAb,CAAkB,IAAlB,CAArB;AACA,aAAKpB,EAAL,CAAQyB,aAAR,CAAsBC,gBAAtB,CAAuC,YAAvC,EAAqD,KAAKR,kBAA1D;AACA,aAAKlB,EAAL,CAAQyB,aAAR,CAAsBC,gBAAtB,CAAuC,YAAvC,EAAqD,KAAKL,kBAA1D;AACA,aAAKrB,EAAL,CAAQyB,aAAR,CAAsBC,gBAAtB,CAAuC,OAAvC,EAAgD,KAAKH,aAArD;AACH,OAPD,MAQK,IAAI,KAAKN,SAAL,CAAe,cAAf,MAAmC,OAAvC,EAAgD;AACjD,aAAKU,aAAL,GAAqB,KAAKC,OAAL,CAAaR,IAAb,CAAkB,IAAlB,CAArB;AACA,aAAKS,YAAL,GAAoB,KAAKC,MAAL,CAAYV,IAAZ,CAAiB,IAAjB,CAApB;AACA,aAAKpB,EAAL,CAAQyB,aAAR,CAAsBC,gBAAtB,CAAuC,OAAvC,EAAgD,KAAKC,aAArD;AACA,aAAK3B,EAAL,CAAQyB,aAAR,CAAsBC,gBAAtB,CAAuC,MAAvC,EAA+C,KAAKG,YAApD;AACH;AACJ,KAfD;AAgBH;;AACDE,EAAAA,WAAW,CAACC,YAAD,EAAe;AACtB,QAAIA,YAAY,CAAC3B,eAAjB,EAAkC;AAC9B,WAAK4B,SAAL,CAAe;AAAE5B,QAAAA,eAAe,EAAE2B,YAAY,CAAC3B,eAAb,CAA6B6B;AAAhD,OAAf;AACH;;AACD,QAAIF,YAAY,CAAC1B,YAAjB,EAA+B;AAC3B,WAAK2B,SAAL,CAAe;AAAE3B,QAAAA,YAAY,EAAE0B,YAAY,CAAC1B,YAAb,CAA0B4B;AAA1C,OAAf;AACH;;AACD,QAAIF,YAAY,CAACzB,QAAjB,EAA2B;AACvB,WAAK0B,SAAL,CAAe;AAAE1B,QAAAA,QAAQ,EAAEyB,YAAY,CAACzB,QAAb,CAAsB2B;AAAlC,OAAf;AACH;;AACD,QAAIF,YAAY,CAACG,aAAjB,EAAgC;AAC5B,WAAKF,SAAL,CAAe;AAAEE,QAAAA,aAAa,EAAEH,YAAY,CAACG,aAAb,CAA2BD;AAA5C,OAAf;AACH;;AACD,QAAIF,YAAY,CAACI,iBAAjB,EAAoC;AAChC,WAAKH,SAAL,CAAe;AAAEG,QAAAA,iBAAiB,EAAEJ,YAAY,CAACI,iBAAb,CAA+BF;AAApD,OAAf;AACH;;AACD,QAAIF,YAAY,CAACxB,aAAjB,EAAgC;AAC5B,WAAKyB,SAAL,CAAe;AAAEzB,QAAAA,aAAa,EAAEwB,YAAY,CAACxB,aAAb,CAA2B0B;AAA5C,OAAf;AACH;;AACD,QAAIF,YAAY,CAAC7B,MAAjB,EAAyB;AACrB,WAAK8B,SAAL,CAAe;AAAE9B,QAAAA,MAAM,EAAE6B,YAAY,CAAC7B,MAAb,CAAoB+B;AAA9B,OAAf;AACH;;AACD,QAAIF,YAAY,CAACK,SAAjB,EAA4B;AACxB,WAAKJ,SAAL,CAAe;AAAEI,QAAAA,SAAS,EAAEL,YAAY,CAACK,SAAb,CAAuBH;AAApC,OAAf;AACH;;AACD,QAAIF,YAAY,CAACM,SAAjB,EAA4B;AACxB,WAAKL,SAAL,CAAe;AAAEK,QAAAA,SAAS,EAAEN,YAAY,CAACM,SAAb,CAAuBJ;AAApC,OAAf;AACH;;AACD,QAAIF,YAAY,CAACO,IAAjB,EAAuB;AACnB,WAAKN,SAAL,CAAe;AAAEM,QAAAA,IAAI,EAAEP,YAAY,CAACO,IAAb,CAAkBL;AAA1B,OAAf;AACH;;AACD,QAAIF,YAAY,CAACvB,WAAjB,EAA8B;AAC1B,WAAKwB,SAAL,CAAe;AAAExB,QAAAA,WAAW,EAAEuB,YAAY,CAACvB,WAAb,CAAyByB;AAAxC,OAAf;AACH;;AACD,QAAIF,YAAY,CAACtB,YAAjB,EAA+B;AAC3B,WAAKuB,SAAL,CAAe;AAAEvB,QAAAA,YAAY,EAAEsB,YAAY,CAACtB,YAAb,CAA0BwB;AAA1C,OAAf;AACH;;AACD,QAAIF,YAAY,CAACrB,QAAjB,EAA2B;AACvB,WAAKsB,SAAL,CAAe;AAAEtB,QAAAA,QAAQ,EAAEqB,YAAY,CAACrB,QAAb,CAAsBuB;AAAlC,OAAf;AACH;;AACD,QAAIF,YAAY,CAACQ,IAAjB,EAAuB;AACnB,WAAKP,SAAL,CAAe;AAAEQ,QAAAA,YAAY,EAAET,YAAY,CAACQ,IAAb,CAAkBN;AAAlC,OAAf;;AACA,UAAI,KAAKQ,MAAT,EAAiB;AACb,YAAIV,YAAY,CAACQ,IAAb,CAAkBN,YAAtB,EAAoC;AAChC,cAAI,KAAKS,SAAL,IAAkB,KAAKA,SAAL,CAAeC,YAArC,EAAmD;AAC/C,iBAAKC,UAAL;AACA,iBAAKC,KAAL;AACH,WAHD,MAIK;AACD,iBAAKC,IAAL;AACH;AACJ,SARD,MASK;AACD,eAAKC,IAAL;AACH;AACJ;AACJ;;AACD,QAAIhB,YAAY,CAACiB,cAAjB,EAAiC;AAC7B,WAAK7C,eAAL,GAAuB8C,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAK/C,eAAvB,CAAd,EAAuD4B,YAAY,CAACiB,cAAb,CAA4Bf,YAAnF,CAAvB;AACA,WAAKpB,UAAL;;AACA,UAAI,KAAK4B,MAAT,EAAiB;AACb,YAAI,KAAKzB,SAAL,CAAe,cAAf,CAAJ,EAAoC;AAChC,cAAI,KAAK0B,SAAL,IAAkB,KAAKA,SAAL,CAAeC,YAArC,EAAmD;AAC/C,iBAAKC,UAAL;AACA,iBAAKC,KAAL;AACH,WAHD,MAIK;AACD,iBAAKC,IAAL;AACH;AACJ,SARD,MASK;AACD,eAAKC,IAAL;AACH;AACJ;AACJ;AACJ;;AACD7B,EAAAA,YAAY,CAACiC,CAAD,EAAI;AACZ,QAAI,CAAC,KAAKT,SAAN,IAAmB,CAAC,KAAKU,WAA7B,EAA0C;AACtC,WAAKC,QAAL;AACH;AACJ;;AACDhC,EAAAA,YAAY,CAAC8B,CAAD,EAAI;AACZ,SAAKtC,UAAL;AACH;;AACDc,EAAAA,OAAO,CAACwB,CAAD,EAAI;AACP,SAAKE,QAAL;AACH;;AACDxB,EAAAA,MAAM,CAACsB,CAAD,EAAI;AACN,SAAKtC,UAAL;AACH;;AACDU,EAAAA,OAAO,CAAC4B,CAAD,EAAI;AACP,SAAKtC,UAAL;AACH;;AACDwC,EAAAA,QAAQ,GAAG;AACP,SAAKZ,MAAL,GAAc,IAAd;AACA,SAAKa,gBAAL;AACA,QAAI,KAAKtC,SAAL,CAAe,WAAf,CAAJ,EACI,KAAKoC,WAAL,GAAmBG,UAAU,CAAC,MAAM;AAAE,WAAKT,IAAL;AAAc,KAAvB,EAAyB,KAAK9B,SAAL,CAAe,WAAf,CAAzB,CAA7B,CADJ,KAGI,KAAK8B,IAAL;;AACJ,QAAI,KAAK9B,SAAL,CAAe,MAAf,CAAJ,EAA4B;AACxB,UAAIwC,QAAQ,GAAG,KAAKxC,SAAL,CAAe,WAAf,IAA8B,KAAKA,SAAL,CAAe,MAAf,IAAyB,KAAKA,SAAL,CAAe,WAAf,CAAvD,GAAqF,KAAKA,SAAL,CAAe,MAAf,CAApG;AACA,WAAKyC,WAAL,GAAmBF,UAAU,CAAC,MAAM;AAAE,aAAKR,IAAL;AAAc,OAAvB,EAAyBS,QAAzB,CAA7B;AACH;AACJ;;AACD3C,EAAAA,UAAU,GAAG;AACT,SAAK4B,MAAL,GAAc,KAAd;AACA,SAAKiB,gBAAL;;AACA,QAAI,KAAK1C,SAAL,CAAe,WAAf,CAAJ,EAAiC;AAC7B,WAAKsC,gBAAL,GAD6B,CACJ;;AACzB,WAAKG,WAAL,GAAmBF,UAAU,CAAC,MAAM;AAAE,aAAKR,IAAL;AAAc,OAAvB,EAAyB,KAAK/B,SAAL,CAAe,WAAf,CAAzB,CAA7B;AACH,KAHD,MAIK;AACD,WAAK+B,IAAL;AACH;AACJ;;AACDY,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKjB,SAAT,EAAoB;AAChB,WAAKY,gBAAL;AACA,WAAKM,MAAL;AACH;;AACD,SAAKlB,SAAL,GAAiBmB,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAjB;AACA,QAAIC,YAAY,GAAGF,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAnB;AACAC,IAAAA,YAAY,CAACC,SAAb,GAAyB,iBAAzB;AACA,SAAKtB,SAAL,CAAeuB,WAAf,CAA2BF,YAA3B;AACA,SAAKG,WAAL,GAAmBL,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAnB;AACA,SAAKI,WAAL,CAAiBF,SAAjB,GAA6B,gBAA7B;AACA,SAAKpB,UAAL;;AACA,QAAI,KAAK5B,SAAL,CAAe,eAAf,CAAJ,EAAqC;AACjC,WAAK0B,SAAL,CAAeyB,KAAf,CAAqBC,QAArB,GAAgC,KAAKpD,SAAL,CAAe,eAAf,CAAhC;AACH;;AACD,SAAK0B,SAAL,CAAeuB,WAAf,CAA2B,KAAKC,WAAhC;AACA,QAAI,KAAKlD,SAAL,CAAe,UAAf,MAA+B,MAAnC,EACI6C,QAAQ,CAACQ,IAAT,CAAcJ,WAAd,CAA0B,KAAKvB,SAA/B,EADJ,KAEK,IAAI,KAAK1B,SAAL,CAAe,UAAf,MAA+B,QAAnC,EACDvB,UAAU,CAACwE,WAAX,CAAuB,KAAKvB,SAA5B,EAAuC,KAAK3C,EAAL,CAAQyB,aAA/C,EADC,KAGD/B,UAAU,CAACwE,WAAX,CAAuB,KAAKvB,SAA5B,EAAuC,KAAK1B,SAAL,CAAe,UAAf,CAAvC;AACJ,SAAK0B,SAAL,CAAeyB,KAAf,CAAqBG,OAArB,GAA+B,cAA/B;AACH;;AACDxB,EAAAA,IAAI,GAAG;AACH,QAAI,CAAC,KAAK9B,SAAL,CAAe,cAAf,CAAD,IAAmC,KAAKA,SAAL,CAAe,UAAf,CAAvC,EAAmE;AAC/D;AACH;;AACD,SAAK2C,MAAL;AACA,SAAKd,KAAL;AACApD,IAAAA,UAAU,CAAC8E,MAAX,CAAkB,KAAK7B,SAAvB,EAAkC,GAAlC;AACA,QAAI,KAAK1B,SAAL,CAAe,eAAf,MAAoC,MAAxC,EACIrB,WAAW,CAAC6E,GAAZ,CAAgB,SAAhB,EAA2B,KAAK9B,SAAhC,EAA2C,KAAKzC,MAAL,CAAYwE,MAAZ,CAAmBC,OAA9D,EADJ,KAGI,KAAKhC,SAAL,CAAeyB,KAAf,CAAqBM,MAArB,GAA8B,KAAKzD,SAAL,CAAe,eAAf,CAA9B;AACJ,SAAK2D,0BAAL;AACA,SAAKC,kBAAL;AACH;;AACD7B,EAAAA,IAAI,GAAG;AACH,QAAI,KAAK/B,SAAL,CAAe,eAAf,MAAoC,MAAxC,EAAgD;AAC5CrB,MAAAA,WAAW,CAACkF,KAAZ,CAAkB,KAAKnC,SAAvB;AACH;;AACD,SAAKkB,MAAL;AACH;;AACDhB,EAAAA,UAAU,GAAG;AACT,QAAI,KAAK5B,SAAL,CAAe,QAAf,CAAJ,EAA8B;AAC1B,WAAKkD,WAAL,CAAiBY,SAAjB,GAA6B,EAA7B;AACA,WAAKZ,WAAL,CAAiBD,WAAjB,CAA6BJ,QAAQ,CAACkB,cAAT,CAAwB,KAAK/D,SAAL,CAAe,cAAf,CAAxB,CAA7B;AACH,KAHD,MAIK;AACD,WAAKkD,WAAL,CAAiBY,SAAjB,GAA6B,KAAK9D,SAAL,CAAe,cAAf,CAA7B;AACH;AACJ;;AACD6B,EAAAA,KAAK,GAAG;AACJ,QAAIuB,QAAQ,GAAG,KAAKpD,SAAL,CAAe,iBAAf,CAAf;;AACA,YAAQoD,QAAR;AACI,WAAK,KAAL;AACI,aAAKY,QAAL;;AACA,YAAI,KAAKC,aAAL,EAAJ,EAA0B;AACtB,eAAKC,WAAL;;AACA,cAAI,KAAKD,aAAL,EAAJ,EAA0B;AACtB,iBAAKE,UAAL;;AACA,gBAAI,KAAKF,aAAL,EAAJ,EAA0B;AACtB,mBAAKG,SAAL;AACH;AACJ;AACJ;;AACD;;AACJ,WAAK,QAAL;AACI,aAAKF,WAAL;;AACA,YAAI,KAAKD,aAAL,EAAJ,EAA0B;AACtB,eAAKD,QAAL;;AACA,cAAI,KAAKC,aAAL,EAAJ,EAA0B;AACtB,iBAAKE,UAAL;;AACA,gBAAI,KAAKF,aAAL,EAAJ,EAA0B;AACtB,mBAAKG,SAAL;AACH;AACJ;AACJ;;AACD;;AACJ,WAAK,MAAL;AACI,aAAKA,SAAL;;AACA,YAAI,KAAKH,aAAL,EAAJ,EAA0B;AACtB,eAAKE,UAAL;;AACA,cAAI,KAAKF,aAAL,EAAJ,EAA0B;AACtB,iBAAKD,QAAL;;AACA,gBAAI,KAAKC,aAAL,EAAJ,EAA0B;AACtB,mBAAKC,WAAL;AACH;AACJ;AACJ;;AACD;;AACJ,WAAK,OAAL;AACI,aAAKC,UAAL;;AACA,YAAI,KAAKF,aAAL,EAAJ,EAA0B;AACtB,eAAKG,SAAL;;AACA,cAAI,KAAKH,aAAL,EAAJ,EAA0B;AACtB,iBAAKD,QAAL;;AACA,gBAAI,KAAKC,aAAL,EAAJ,EAA0B;AACtB,mBAAKC,WAAL;AACH;AACJ;AACJ;;AACD;AAhDR;AAkDH;;AACDG,EAAAA,aAAa,GAAG;AACZ,QAAI,KAAKrE,SAAL,CAAe,UAAf,MAA+B,MAA/B,IAAyC,KAAKA,SAAL,CAAe,UAAf,MAA+B,QAA5E,EAAsF;AAClF,UAAIsE,MAAM,GAAG,KAAKvF,EAAL,CAAQyB,aAAR,CAAsB+D,qBAAtB,EAAb;AACA,UAAIC,UAAU,GAAGF,MAAM,CAACG,IAAP,GAAchG,UAAU,CAACiG,mBAAX,EAA/B;AACA,UAAIC,SAAS,GAAGL,MAAM,CAACM,GAAP,GAAanG,UAAU,CAACoG,kBAAX,EAA7B;AACA,aAAO;AAAEJ,QAAAA,IAAI,EAAED,UAAR;AAAoBI,QAAAA,GAAG,EAAED;AAAzB,OAAP;AACH,KALD,MAMK;AACD,aAAO;AAAEF,QAAAA,IAAI,EAAE,CAAR;AAAWG,QAAAA,GAAG,EAAE;AAAhB,OAAP;AACH;AACJ;;AACDT,EAAAA,UAAU,GAAG;AACT,SAAKW,QAAL,CAAc,OAAd;AACA,QAAIC,UAAU,GAAG,KAAKV,aAAL,EAAjB;AACA,QAAII,IAAI,GAAGM,UAAU,CAACN,IAAX,GAAkBhG,UAAU,CAACuG,aAAX,CAAyB,KAAKjG,EAAL,CAAQyB,aAAjC,CAA7B;AACA,QAAIoE,GAAG,GAAGG,UAAU,CAACH,GAAX,GAAiB,CAACnG,UAAU,CAACwG,cAAX,CAA0B,KAAKlG,EAAL,CAAQyB,aAAlC,IAAmD/B,UAAU,CAACwG,cAAX,CAA0B,KAAKvD,SAA/B,CAApD,IAAiG,CAA5H;AACA,SAAKA,SAAL,CAAeyB,KAAf,CAAqBsB,IAArB,GAA4BA,IAAI,GAAG,KAAKzE,SAAL,CAAe,cAAf,CAAP,GAAwC,IAApE;AACA,SAAK0B,SAAL,CAAeyB,KAAf,CAAqByB,GAArB,GAA2BA,GAAG,GAAG,KAAK5E,SAAL,CAAe,aAAf,CAAN,GAAsC,IAAjE;AACH;;AACDoE,EAAAA,SAAS,GAAG;AACR,SAAKU,QAAL,CAAc,MAAd;AACA,QAAIC,UAAU,GAAG,KAAKV,aAAL,EAAjB;AACA,QAAII,IAAI,GAAGM,UAAU,CAACN,IAAX,GAAkBhG,UAAU,CAACuG,aAAX,CAAyB,KAAKtD,SAA9B,CAA7B;AACA,QAAIkD,GAAG,GAAGG,UAAU,CAACH,GAAX,GAAiB,CAACnG,UAAU,CAACwG,cAAX,CAA0B,KAAKlG,EAAL,CAAQyB,aAAlC,IAAmD/B,UAAU,CAACwG,cAAX,CAA0B,KAAKvD,SAA/B,CAApD,IAAiG,CAA5H;AACA,SAAKA,SAAL,CAAeyB,KAAf,CAAqBsB,IAArB,GAA4BA,IAAI,GAAG,KAAKzE,SAAL,CAAe,cAAf,CAAP,GAAwC,IAApE;AACA,SAAK0B,SAAL,CAAeyB,KAAf,CAAqByB,GAArB,GAA2BA,GAAG,GAAG,KAAK5E,SAAL,CAAe,aAAf,CAAN,GAAsC,IAAjE;AACH;;AACDgE,EAAAA,QAAQ,GAAG;AACP,SAAKc,QAAL,CAAc,KAAd;AACA,QAAIC,UAAU,GAAG,KAAKV,aAAL,EAAjB;AACA,QAAII,IAAI,GAAGM,UAAU,CAACN,IAAX,GAAkB,CAAChG,UAAU,CAACuG,aAAX,CAAyB,KAAKjG,EAAL,CAAQyB,aAAjC,IAAkD/B,UAAU,CAACuG,aAAX,CAAyB,KAAKtD,SAA9B,CAAnD,IAA+F,CAA5H;AACA,QAAIkD,GAAG,GAAGG,UAAU,CAACH,GAAX,GAAiBnG,UAAU,CAACwG,cAAX,CAA0B,KAAKvD,SAA/B,CAA3B;AACA,SAAKA,SAAL,CAAeyB,KAAf,CAAqBsB,IAArB,GAA4BA,IAAI,GAAG,KAAKzE,SAAL,CAAe,cAAf,CAAP,GAAwC,IAApE;AACA,SAAK0B,SAAL,CAAeyB,KAAf,CAAqByB,GAArB,GAA2BA,GAAG,GAAG,KAAK5E,SAAL,CAAe,aAAf,CAAN,GAAsC,IAAjE;AACH;;AACDkE,EAAAA,WAAW,GAAG;AACV,SAAKY,QAAL,CAAc,QAAd;AACA,QAAIC,UAAU,GAAG,KAAKV,aAAL,EAAjB;AACA,QAAII,IAAI,GAAGM,UAAU,CAACN,IAAX,GAAkB,CAAChG,UAAU,CAACuG,aAAX,CAAyB,KAAKjG,EAAL,CAAQyB,aAAjC,IAAkD/B,UAAU,CAACuG,aAAX,CAAyB,KAAKtD,SAA9B,CAAnD,IAA+F,CAA5H;AACA,QAAIkD,GAAG,GAAGG,UAAU,CAACH,GAAX,GAAiBnG,UAAU,CAACwG,cAAX,CAA0B,KAAKlG,EAAL,CAAQyB,aAAlC,CAA3B;AACA,SAAKkB,SAAL,CAAeyB,KAAf,CAAqBsB,IAArB,GAA4BA,IAAI,GAAG,KAAKzE,SAAL,CAAe,cAAf,CAAP,GAAwC,IAApE;AACA,SAAK0B,SAAL,CAAeyB,KAAf,CAAqByB,GAArB,GAA2BA,GAAG,GAAG,KAAK5E,SAAL,CAAe,aAAf,CAAN,GAAsC,IAAjE;AACH;;AACDgB,EAAAA,SAAS,CAACkE,MAAD,EAAS;AACd,SAAK/F,eAAL,GAAuB8C,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAK/C,eAAvB,CAAd,EAAuD+F,MAAvD,CAAvB;AACH;;AACDlF,EAAAA,SAAS,CAACkF,MAAD,EAAS;AACd,WAAO,KAAK/F,eAAL,CAAqB+F,MAArB,CAAP;AACH;;AACDJ,EAAAA,QAAQ,CAAC1B,QAAD,EAAW;AACf,SAAK1B,SAAL,CAAeyB,KAAf,CAAqBsB,IAArB,GAA4B,CAAC,GAAD,GAAO,IAAnC;AACA,SAAK/C,SAAL,CAAeyB,KAAf,CAAqByB,GAArB,GAA2B,CAAC,GAAD,GAAO,IAAlC;AACA,QAAIO,gBAAgB,GAAG,qCAAqC/B,QAA5D;AACA,SAAK1B,SAAL,CAAesB,SAAf,GAA2B,KAAKhD,SAAL,CAAe,mBAAf,IAAsCmF,gBAAgB,GAAG,GAAnB,GAAyB,KAAKnF,SAAL,CAAe,mBAAf,CAA/D,GAAqGmF,gBAAhI;AACH;;AACDlB,EAAAA,aAAa,GAAG;AACZ,QAAIK,MAAM,GAAG,KAAK5C,SAAL,CAAe6C,qBAAf,EAAb;AACA,QAAII,SAAS,GAAGL,MAAM,CAACM,GAAvB;AACA,QAAIJ,UAAU,GAAGF,MAAM,CAACG,IAAxB;AACA,QAAIW,KAAK,GAAG3G,UAAU,CAACuG,aAAX,CAAyB,KAAKtD,SAA9B,CAAZ;AACA,QAAI2D,MAAM,GAAG5G,UAAU,CAACwG,cAAX,CAA0B,KAAKvD,SAA/B,CAAb;AACA,QAAI4D,QAAQ,GAAG7G,UAAU,CAAC8G,WAAX,EAAf;AACA,WAAQf,UAAU,GAAGY,KAAb,GAAqBE,QAAQ,CAACF,KAA/B,IAA0CZ,UAAU,GAAG,CAAvD,IAA8DG,SAAS,GAAG,CAA1E,IAAiFA,SAAS,GAAGU,MAAZ,GAAqBC,QAAQ,CAACD,MAAtH;AACH;;AACDG,EAAAA,cAAc,CAACrD,CAAD,EAAI;AACd,SAAKJ,IAAL;AACH;;AACD4B,EAAAA,0BAA0B,GAAG;AACzB,SAAK3E,IAAL,CAAUe,iBAAV,CAA4B,MAAM;AAC9B,WAAK0F,cAAL,GAAsB,KAAKD,cAAL,CAAoBrF,IAApB,CAAyB,IAAzB,CAAtB;AACAuF,MAAAA,MAAM,CAACjF,gBAAP,CAAwB,QAAxB,EAAkC,KAAKgF,cAAvC;AACH,KAHD;AAIH;;AACDE,EAAAA,4BAA4B,GAAG;AAC3B,QAAI,KAAKF,cAAT,EAAyB;AACrBC,MAAAA,MAAM,CAACE,mBAAP,CAA2B,QAA3B,EAAqC,KAAKH,cAA1C;AACA,WAAKA,cAAL,GAAsB,IAAtB;AACH;AACJ;;AACD7B,EAAAA,kBAAkB,GAAG;AACjB,QAAI,CAAC,KAAKiC,aAAV,EAAyB;AACrB,WAAKA,aAAL,GAAqB,IAAInH,6BAAJ,CAAkC,KAAKK,EAAL,CAAQyB,aAA1C,EAAyD,MAAM;AAChF,YAAI,KAAKkB,SAAT,EAAoB;AAChB,eAAKK,IAAL;AACH;AACJ,OAJoB,CAArB;AAKH;;AACD,SAAK8D,aAAL,CAAmBjC,kBAAnB;AACH;;AACDkC,EAAAA,oBAAoB,GAAG;AACnB,QAAI,KAAKD,aAAT,EAAwB;AACpB,WAAKA,aAAL,CAAmBC,oBAAnB;AACH;AACJ;;AACDC,EAAAA,YAAY,GAAG;AACX,QAAI,KAAK/F,SAAL,CAAe,cAAf,MAAmC,OAAvC,EAAgD;AAC5C,WAAKjB,EAAL,CAAQyB,aAAR,CAAsBoF,mBAAtB,CAA0C,YAA1C,EAAwD,KAAK3F,kBAA7D;AACA,WAAKlB,EAAL,CAAQyB,aAAR,CAAsBoF,mBAAtB,CAA0C,YAA1C,EAAwD,KAAKxF,kBAA7D;AACA,WAAKrB,EAAL,CAAQyB,aAAR,CAAsBoF,mBAAtB,CAA0C,OAA1C,EAAmD,KAAKtF,aAAxD;AACH,KAJD,MAKK,IAAI,KAAKN,SAAL,CAAe,cAAf,MAAmC,OAAvC,EAAgD;AACjD,WAAKjB,EAAL,CAAQyB,aAAR,CAAsBoF,mBAAtB,CAA0C,OAA1C,EAAmD,KAAKlF,aAAxD;AACA,WAAK3B,EAAL,CAAQyB,aAAR,CAAsBoF,mBAAtB,CAA0C,MAA1C,EAAkD,KAAKhF,YAAvD;AACH;;AACD,SAAK+E,4BAAL;AACH;;AACD/C,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKlB,SAAL,IAAkB,KAAKA,SAAL,CAAesE,aAArC,EAAoD;AAChD,UAAI,KAAKhG,SAAL,CAAe,UAAf,MAA+B,MAAnC,EACI6C,QAAQ,CAACQ,IAAT,CAAc4C,WAAd,CAA0B,KAAKvE,SAA/B,EADJ,KAEK,IAAI,KAAK1B,SAAL,CAAe,UAAf,MAA+B,QAAnC,EACD,KAAKjB,EAAL,CAAQyB,aAAR,CAAsByF,WAAtB,CAAkC,KAAKvE,SAAvC,EADC,KAGDjD,UAAU,CAACwH,WAAX,CAAuB,KAAKvE,SAA5B,EAAuC,KAAK1B,SAAL,CAAe,UAAf,CAAvC;AACP;;AACD,SAAK2F,4BAAL;AACA,SAAKG,oBAAL;AACA,SAAKI,aAAL;AACA,SAAKxE,SAAL,GAAiB,IAAjB;AACA,SAAKmE,aAAL,GAAqB,IAArB;AACH;;AACDnD,EAAAA,gBAAgB,GAAG;AACf,QAAI,KAAKN,WAAT,EAAsB;AAClB+D,MAAAA,YAAY,CAAC,KAAK/D,WAAN,CAAZ;AACA,WAAKA,WAAL,GAAmB,IAAnB;AACH;AACJ;;AACDE,EAAAA,gBAAgB,GAAG;AACf,QAAI,KAAKG,WAAT,EAAsB;AAClB0D,MAAAA,YAAY,CAAC,KAAK1D,WAAN,CAAZ;AACA,WAAKA,WAAL,GAAmB,IAAnB;AACH;AACJ;;AACDyD,EAAAA,aAAa,GAAG;AACZ,SAAKxD,gBAAL;AACA,SAAKJ,gBAAL;AACH;;AACD8D,EAAAA,WAAW,GAAG;AACV,SAAKL,YAAL;;AACA,QAAI,KAAKrE,SAAT,EAAoB;AAChB/C,MAAAA,WAAW,CAACkF,KAAZ,CAAkB,KAAKnC,SAAvB;AACH;;AACD,SAAKkB,MAAL;;AACA,QAAI,KAAKiD,aAAT,EAAwB;AACpB,WAAKA,aAAL,CAAmBQ,OAAnB;AACA,WAAKR,aAAL,GAAqB,IAArB;AACH;AACJ;;AA1ZS;;AA4ZdhH,OAAO,CAACyH,IAAR;AAAA,mBAAoGzH,OAApG,EAA0FT,EAA1F,mBAA6HA,EAAE,CAACmI,UAAhI,GAA0FnI,EAA1F,mBAAuJA,EAAE,CAACoI,MAA1J,GAA0FpI,EAA1F,mBAA6KQ,EAAE,CAAC6H,aAAhL;AAAA;;AACA5H,OAAO,CAAC6H,IAAR,kBAD0FtI,EAC1F;AAAA,QAAwFS,OAAxF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAD0FT,EAC1F;AAAA;;AACA;AAAA,qDAF0FA,EAE1F,mBAA2FS,OAA3F,EAAgH,CAAC;AACrG8H,IAAAA,IAAI,EAAEtI,SAD+F;AAErGuI,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,YADX;AAECC,MAAAA,IAAI,EAAE;AACF,iBAAS;AADP;AAFP,KAAD;AAF+F,GAAD,CAAhH,EAQ4B,YAAY;AAAE,WAAO,CAAC;AAAEH,MAAAA,IAAI,EAAEvI,EAAE,CAACmI;AAAX,KAAD,EAA0B;AAAEI,MAAAA,IAAI,EAAEvI,EAAE,CAACoI;AAAX,KAA1B,EAA+C;AAAEG,MAAAA,IAAI,EAAE/H,EAAE,CAAC6H;AAAX,KAA/C,CAAP;AAAoF,GAR9H,EAQgJ;AAAErH,IAAAA,eAAe,EAAE,CAAC;AACpJuH,MAAAA,IAAI,EAAErI;AAD8I,KAAD,CAAnB;AAEhIe,IAAAA,YAAY,EAAE,CAAC;AACfsH,MAAAA,IAAI,EAAErI;AADS,KAAD,CAFkH;AAIhIgB,IAAAA,QAAQ,EAAE,CAAC;AACXqH,MAAAA,IAAI,EAAErI;AADK,KAAD,CAJsH;AAMhI4C,IAAAA,aAAa,EAAE,CAAC;AAChByF,MAAAA,IAAI,EAAErI;AADU,KAAD,CANiH;AAQhI6C,IAAAA,iBAAiB,EAAE,CAAC;AACpBwF,MAAAA,IAAI,EAAErI;AADc,KAAD,CAR6G;AAUhIiB,IAAAA,aAAa,EAAE,CAAC;AAChBoH,MAAAA,IAAI,EAAErI;AADU,KAAD,CAViH;AAYhIY,IAAAA,MAAM,EAAE,CAAC;AACTyH,MAAAA,IAAI,EAAErI;AADG,KAAD,CAZwH;AAchI8C,IAAAA,SAAS,EAAE,CAAC;AACZuF,MAAAA,IAAI,EAAErI;AADM,KAAD,CAdqH;AAgBhI+C,IAAAA,SAAS,EAAE,CAAC;AACZsF,MAAAA,IAAI,EAAErI;AADM,KAAD,CAhBqH;AAkBhIgD,IAAAA,IAAI,EAAE,CAAC;AACPqF,MAAAA,IAAI,EAAErI;AADC,KAAD,CAlB0H;AAoBhIkB,IAAAA,WAAW,EAAE,CAAC;AACdmH,MAAAA,IAAI,EAAErI;AADQ,KAAD,CApBmH;AAsBhImB,IAAAA,YAAY,EAAE,CAAC;AACfkH,MAAAA,IAAI,EAAErI;AADS,KAAD,CAtBkH;AAwBhIiD,IAAAA,IAAI,EAAE,CAAC;AACPoF,MAAAA,IAAI,EAAErI,KADC;AAEPsI,MAAAA,IAAI,EAAE,CAAC,UAAD;AAFC,KAAD,CAxB0H;AA2BhIlH,IAAAA,QAAQ,EAAE,CAAC;AACXiH,MAAAA,IAAI,EAAErI,KADK;AAEXsI,MAAAA,IAAI,EAAE,CAAC,iBAAD;AAFK,KAAD,CA3BsH;AA8BhI5E,IAAAA,cAAc,EAAE,CAAC;AACjB2E,MAAAA,IAAI,EAAErI;AADW,KAAD;AA9BgH,GARhJ;AAAA;;AAyCA,MAAMyI,aAAN,CAAoB;;AAEpBA,aAAa,CAACT,IAAd;AAAA,mBAA0GS,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBA9C0F5I,EA8C1F;AAAA,QAA2G2I;AAA3G;AACAA,aAAa,CAACE,IAAd,kBA/C0F7I,EA+C1F;AAAA,YAAoI,CAACI,YAAD,CAApI;AAAA;;AACA;AAAA,qDAhD0FJ,EAgD1F,mBAA2F2I,aAA3F,EAAsH,CAAC;AAC3GJ,IAAAA,IAAI,EAAEpI,QADqG;AAE3GqI,IAAAA,IAAI,EAAE,CAAC;AACCM,MAAAA,OAAO,EAAE,CAAC1I,YAAD,CADV;AAEC2I,MAAAA,OAAO,EAAE,CAACtI,OAAD,CAFV;AAGCuI,MAAAA,YAAY,EAAE,CAACvI,OAAD;AAHf,KAAD;AAFqG,GAAD,CAAtH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,OAAT,EAAkBkI,aAAlB", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng-lts/dom';\nimport { ZIndexUtils } from 'primeng-lts/utils';\nimport * as i1 from 'primeng-lts/api';\n\nclass Tooltip {\n    constructor(el, zone, config) {\n        this.el = el;\n        this.zone = zone;\n        this.config = config;\n        this.escape = true;\n        this._tooltipOptions = {\n            tooltipPosition: 'right',\n            tooltipEvent: 'hover',\n            appendTo: 'body',\n            tooltipZIndex: 'auto',\n            escape: false,\n            positionTop: 0,\n            positionLeft: 0\n        };\n    }\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(val) {\n        this._disabled = val;\n        this.deactivate();\n    }\n    ngAfterViewInit() {\n        this.zone.runOutsideAngular(() => {\n            if (this.getOption('tooltipEvent') === 'hover') {\n                this.mouseEnterListener = this.onMouseEnter.bind(this);\n                this.mouseLeaveListener = this.onMouseLeave.bind(this);\n                this.clickListener = this.onClick.bind(this);\n                this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n                this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n                this.el.nativeElement.addEventListener('click', this.clickListener);\n            }\n            else if (this.getOption('tooltipEvent') === 'focus') {\n                this.focusListener = this.onFocus.bind(this);\n                this.blurListener = this.onBlur.bind(this);\n                this.el.nativeElement.addEventListener('focus', this.focusListener);\n                this.el.nativeElement.addEventListener('blur', this.blurListener);\n            }\n        });\n    }\n    ngOnChanges(simpleChange) {\n        if (simpleChange.tooltipPosition) {\n            this.setOption({ tooltipPosition: simpleChange.tooltipPosition.currentValue });\n        }\n        if (simpleChange.tooltipEvent) {\n            this.setOption({ tooltipEvent: simpleChange.tooltipEvent.currentValue });\n        }\n        if (simpleChange.appendTo) {\n            this.setOption({ appendTo: simpleChange.appendTo.currentValue });\n        }\n        if (simpleChange.positionStyle) {\n            this.setOption({ positionStyle: simpleChange.positionStyle.currentValue });\n        }\n        if (simpleChange.tooltipStyleClass) {\n            this.setOption({ tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue });\n        }\n        if (simpleChange.tooltipZIndex) {\n            this.setOption({ tooltipZIndex: simpleChange.tooltipZIndex.currentValue });\n        }\n        if (simpleChange.escape) {\n            this.setOption({ escape: simpleChange.escape.currentValue });\n        }\n        if (simpleChange.showDelay) {\n            this.setOption({ showDelay: simpleChange.showDelay.currentValue });\n        }\n        if (simpleChange.hideDelay) {\n            this.setOption({ hideDelay: simpleChange.hideDelay.currentValue });\n        }\n        if (simpleChange.life) {\n            this.setOption({ life: simpleChange.life.currentValue });\n        }\n        if (simpleChange.positionTop) {\n            this.setOption({ positionTop: simpleChange.positionTop.currentValue });\n        }\n        if (simpleChange.positionLeft) {\n            this.setOption({ positionLeft: simpleChange.positionLeft.currentValue });\n        }\n        if (simpleChange.disabled) {\n            this.setOption({ disabled: simpleChange.disabled.currentValue });\n        }\n        if (simpleChange.text) {\n            this.setOption({ tooltipLabel: simpleChange.text.currentValue });\n            if (this.active) {\n                if (simpleChange.text.currentValue) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    }\n                    else {\n                        this.show();\n                    }\n                }\n                else {\n                    this.hide();\n                }\n            }\n        }\n        if (simpleChange.tooltipOptions) {\n            this._tooltipOptions = Object.assign(Object.assign({}, this._tooltipOptions), simpleChange.tooltipOptions.currentValue);\n            this.deactivate();\n            if (this.active) {\n                if (this.getOption('tooltipLabel')) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    }\n                    else {\n                        this.show();\n                    }\n                }\n                else {\n                    this.hide();\n                }\n            }\n        }\n    }\n    onMouseEnter(e) {\n        if (!this.container && !this.showTimeout) {\n            this.activate();\n        }\n    }\n    onMouseLeave(e) {\n        this.deactivate();\n    }\n    onFocus(e) {\n        this.activate();\n    }\n    onBlur(e) {\n        this.deactivate();\n    }\n    onClick(e) {\n        this.deactivate();\n    }\n    activate() {\n        this.active = true;\n        this.clearHideTimeout();\n        if (this.getOption('showDelay'))\n            this.showTimeout = setTimeout(() => { this.show(); }, this.getOption('showDelay'));\n        else\n            this.show();\n        if (this.getOption('life')) {\n            let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n            this.hideTimeout = setTimeout(() => { this.hide(); }, duration);\n        }\n    }\n    deactivate() {\n        this.active = false;\n        this.clearShowTimeout();\n        if (this.getOption('hideDelay')) {\n            this.clearHideTimeout(); //life timeout\n            this.hideTimeout = setTimeout(() => { this.hide(); }, this.getOption('hideDelay'));\n        }\n        else {\n            this.hide();\n        }\n    }\n    create() {\n        if (this.container) {\n            this.clearHideTimeout();\n            this.remove();\n        }\n        this.container = document.createElement('div');\n        let tooltipArrow = document.createElement('div');\n        tooltipArrow.className = 'p-tooltip-arrow';\n        this.container.appendChild(tooltipArrow);\n        this.tooltipText = document.createElement('div');\n        this.tooltipText.className = 'p-tooltip-text';\n        this.updateText();\n        if (this.getOption('positionStyle')) {\n            this.container.style.position = this.getOption('positionStyle');\n        }\n        this.container.appendChild(this.tooltipText);\n        if (this.getOption('appendTo') === 'body')\n            document.body.appendChild(this.container);\n        else if (this.getOption('appendTo') === 'target')\n            DomHandler.appendChild(this.container, this.el.nativeElement);\n        else\n            DomHandler.appendChild(this.container, this.getOption('appendTo'));\n        this.container.style.display = 'inline-block';\n    }\n    show() {\n        if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n            return;\n        }\n        this.create();\n        this.align();\n        DomHandler.fadeIn(this.container, 250);\n        if (this.getOption('tooltipZIndex') === 'auto')\n            ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);\n        else\n            this.container.style.zIndex = this.getOption('tooltipZIndex');\n        this.bindDocumentResizeListener();\n        this.bindScrollListener();\n    }\n    hide() {\n        if (this.getOption('tooltipZIndex') === 'auto') {\n            ZIndexUtils.clear(this.container);\n        }\n        this.remove();\n    }\n    updateText() {\n        if (this.getOption('escape')) {\n            this.tooltipText.innerHTML = '';\n            this.tooltipText.appendChild(document.createTextNode(this.getOption('tooltipLabel')));\n        }\n        else {\n            this.tooltipText.innerHTML = this.getOption('tooltipLabel');\n        }\n    }\n    align() {\n        let position = this.getOption('tooltipPosition');\n        switch (position) {\n            case 'top':\n                this.alignTop();\n                if (this.isOutOfBounds()) {\n                    this.alignBottom();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n            case 'bottom':\n                this.alignBottom();\n                if (this.isOutOfBounds()) {\n                    this.alignTop();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n            case 'left':\n                this.alignLeft();\n                if (this.isOutOfBounds()) {\n                    this.alignRight();\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n            case 'right':\n                this.alignRight();\n                if (this.isOutOfBounds()) {\n                    this.alignLeft();\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n        }\n    }\n    getHostOffset() {\n        if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n            let offset = this.el.nativeElement.getBoundingClientRect();\n            let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n            let targetTop = offset.top + DomHandler.getWindowScrollTop();\n            return { left: targetLeft, top: targetTop };\n        }\n        else {\n            return { left: 0, top: 0 };\n        }\n    }\n    alignRight() {\n        this.preAlign('right');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + DomHandler.getOuterWidth(this.el.nativeElement);\n        let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignLeft() {\n        this.preAlign('left');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n        let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignTop() {\n        this.preAlign('top');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignBottom() {\n        this.preAlign('bottom');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    setOption(option) {\n        this._tooltipOptions = Object.assign(Object.assign({}, this._tooltipOptions), option);\n    }\n    getOption(option) {\n        return this._tooltipOptions[option];\n    }\n    preAlign(position) {\n        this.container.style.left = -999 + 'px';\n        this.container.style.top = -999 + 'px';\n        let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n        this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n    }\n    isOutOfBounds() {\n        let offset = this.container.getBoundingClientRect();\n        let targetTop = offset.top;\n        let targetLeft = offset.left;\n        let width = DomHandler.getOuterWidth(this.container);\n        let height = DomHandler.getOuterHeight(this.container);\n        let viewport = DomHandler.getViewport();\n        return (targetLeft + width > viewport.width) || (targetLeft < 0) || (targetTop < 0) || (targetTop + height > viewport.height);\n    }\n    onWindowResize(e) {\n        this.hide();\n    }\n    bindDocumentResizeListener() {\n        this.zone.runOutsideAngular(() => {\n            this.resizeListener = this.onWindowResize.bind(this);\n            window.addEventListener('resize', this.resizeListener);\n        });\n    }\n    unbindDocumentResizeListener() {\n        if (this.resizeListener) {\n            window.removeEventListener('resize', this.resizeListener);\n            this.resizeListener = null;\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n                if (this.container) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    unbindEvents() {\n        if (this.getOption('tooltipEvent') === 'hover') {\n            this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n            this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n            this.el.nativeElement.removeEventListener('click', this.clickListener);\n        }\n        else if (this.getOption('tooltipEvent') === 'focus') {\n            this.el.nativeElement.removeEventListener('focus', this.focusListener);\n            this.el.nativeElement.removeEventListener('blur', this.blurListener);\n        }\n        this.unbindDocumentResizeListener();\n    }\n    remove() {\n        if (this.container && this.container.parentElement) {\n            if (this.getOption('appendTo') === 'body')\n                document.body.removeChild(this.container);\n            else if (this.getOption('appendTo') === 'target')\n                this.el.nativeElement.removeChild(this.container);\n            else\n                DomHandler.removeChild(this.container, this.getOption('appendTo'));\n        }\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.clearTimeouts();\n        this.container = null;\n        this.scrollHandler = null;\n    }\n    clearShowTimeout() {\n        if (this.showTimeout) {\n            clearTimeout(this.showTimeout);\n            this.showTimeout = null;\n        }\n    }\n    clearHideTimeout() {\n        if (this.hideTimeout) {\n            clearTimeout(this.hideTimeout);\n            this.hideTimeout = null;\n        }\n    }\n    clearTimeouts() {\n        this.clearShowTimeout();\n        this.clearHideTimeout();\n    }\n    ngOnDestroy() {\n        this.unbindEvents();\n        if (this.container) {\n            ZIndexUtils.clear(this.container);\n        }\n        this.remove();\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n    }\n}\nTooltip.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: Tooltip, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Directive });\nTooltip.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.0.5\", type: Tooltip, selector: \"[pTooltip]\", inputs: { tooltipPosition: \"tooltipPosition\", tooltipEvent: \"tooltipEvent\", appendTo: \"appendTo\", positionStyle: \"positionStyle\", tooltipStyleClass: \"tooltipStyleClass\", tooltipZIndex: \"tooltipZIndex\", escape: \"escape\", showDelay: \"showDelay\", hideDelay: \"hideDelay\", life: \"life\", positionTop: \"positionTop\", positionLeft: \"positionLeft\", text: [\"pTooltip\", \"text\"], disabled: [\"tooltipDisabled\", \"disabled\"], tooltipOptions: \"tooltipOptions\" }, host: { classAttribute: \"p-element\" }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: Tooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pTooltip]',\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1.PrimeNGConfig }]; }, propDecorators: { tooltipPosition: [{\n                type: Input\n            }], tooltipEvent: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], positionStyle: [{\n                type: Input\n            }], tooltipStyleClass: [{\n                type: Input\n            }], tooltipZIndex: [{\n                type: Input\n            }], escape: [{\n                type: Input\n            }], showDelay: [{\n                type: Input\n            }], hideDelay: [{\n                type: Input\n            }], life: [{\n                type: Input\n            }], positionTop: [{\n                type: Input\n            }], positionLeft: [{\n                type: Input\n            }], text: [{\n                type: Input,\n                args: ['pTooltip']\n            }], disabled: [{\n                type: Input,\n                args: [\"tooltipDisabled\"]\n            }], tooltipOptions: [{\n                type: Input\n            }] } });\nclass TooltipModule {\n}\nTooltipModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: TooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTooltipModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: TooltipModule, declarations: [Tooltip], imports: [CommonModule], exports: [Tooltip] });\nTooltipModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: TooltipModule, imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: TooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Tooltip],\n                    declarations: [Tooltip]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tooltip, TooltipModule };\n"]}, "metadata": {}, "sourceType": "module"}
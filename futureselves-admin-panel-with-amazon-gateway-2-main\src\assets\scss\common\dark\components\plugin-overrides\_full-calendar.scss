/* Full Calendar */

.fc {
  button {
    @extend .btn;
    @extend .btn-sm;
    height: auto;
    background-image: none;
    text-shadow: none;
    font-weight: 400;
    background-color: theme-color(dark);
    border-color: $border-color;
    text-transform: capitalize;
    color: $fullcalendar-color;
    .fc-icon-right-single-arrow,
    .fc-icon-left-single-arrow {
      &:after {
        color: $fullcalendar-color;
        font: normal normal normal 15px/1 "themify";
      }
    }  
    .fc-icon-right-single-arrow {
      &:after {
        content: "\e649";
      }
    }
    .fc-icon-left-single-arrow {
      &:after {
        content: "\e64a";
      }
    }
  }
  .fc-button-group {
    @extend .btn-group;
    .fc-button {
      background-color: theme-color(dark);
      margin: 0;
      &.fc-state-active {
        color: theme-color(danger);        
      }
    }
  }
  .fc-header-toolbar {
    margin-top: 2rem;
    .fc-center {
      h2 {
        font-size: 1rem;        
      }
    }
    @media (max-width: 767px) {
      .fc-left,
      .fc-right,
      .fc-center {
        float: none;
        margin-bottom: 10px;
        &:after {
          content: "";
          clear: both;
          display: block;
        }
      }
      .fc-left {
        .fc-today-button {
          float: right;
        }
      }
      .fc-right {
        .fc-button-group {
          float: none;
        }
      }
    }
  }
  .fc-widget-header {
    border: 0;
    table {
      tr {
        th {
          border-width: 0 0 1px 0;
          text-align: right;
          padding: 1rem;
          span {
            font-size: 10px;
            text-transform: uppercase;
            color: $fullcalendar-color;
            letter-spacing: 1px;
          }
        }
      }
    }
  }
  &.fc-unthemed {
    .fc-row,
    td {
      border-color: $border-color;
    }
  }
  .fc-content-skeleton {
    table {
      tr {
        td {
          padding: .5rem;
          font-size: .875rem;
        }
      }
    }
  }
  .fc-event {
    border: 0;
    color: $white;
    padding: .5rem;
    &.fc-start {
      background: theme-color(primary); 
      border-radius: 4px;
      border-left: 4px solid theme-color(primary);        
    }
    &.fc-not-start,
    &.fc-not-end {
      background: theme-color(danger);
      border-left: 4px solid theme-color(danger);
      padding-left: .5rem;
    }
    &.fc-not-end {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    .fc-title,
    .fc-time {
      color: $white;
    }
  }
}

.fc-external-events {
  .fc-event {
    background: $card-bg;
    color: $body-color;
    border-width: 0 0 0 4px;
    padding: 16px;
    border-radius: 0;
    margin-bottom: 10px;
    &:nth-child(1) {
      border-color: theme-color(primary);
    }
    &:nth-child(2) {
      border-color: theme-color(danger);
    }
    &:nth-child(3) {
      border-color: theme-color(success);
    }
    &:nth-child(4) {
      border-color: theme-color(info);
    }
    &:nth-child(4) {
      border-color: theme-color(warning);
    }
  }
}
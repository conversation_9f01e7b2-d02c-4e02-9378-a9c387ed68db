<app-sidebar>
    <div class="content-wrapper">
        <div class="row">
            <div class="col-12 grid-margin stretch-card">
                <div class="card">
                    <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">Insight
                        Details</div>
                    <div class="card-body">
                        <form [formGroup]="soundForm" class="forms-sample">
                            <div class="row">
                                <div class="form-group col-lg-4">
                                    <label for="">User</label>
                                    <input type="text" class="form-control form-control-sm" formControlName="U_name"
                                        [readOnly]="true">
                                </div>
                                <div class="form-group col-lg-4">
                                    <label for="">Sector Name
                                    </label>
                                    <select style="color:#646363;" class="form-control form-control-sm" required
                                        formControlName="AN_industryId">
                                        <option *ngFor="let ind of industryList" [value]="ind.IN_id">
                                            {{ind.IN_name}}
                                        </option>
                                    </select>
                                </div>
                                <div class="form-group col-lg-4">
                                    <label for="">Role</label>
                                    <input [readOnly]="true" type="text" class="form-control form-control-sm"
                                        formControlName="RO_title" required>
                                </div>
                                <div class="form-group col-lg-4">
                                    <label for="">Question</label>
                                    <input [readOnly]="true" type="text" class="form-control form-control-sm"
                                        formControlName="AN_title" required>
                                </div>

                                <div class="form-group col-lg-4">
                                    <label for="ANT_tagtitle">Tags</label>
                                    <input [readOnly]="true" type="text" 
                                           class="form-control form-control-sm" 
                                           [value]="Tagtitle">
                                  </div>

                                  <div class="form-group col-lg-4">
                                    <label for="is_annonymous">Is Public?</label>
                                    <input [readOnly]="true" type="text" 
                                           class="form-control form-control-sm" 
                                           [value]="ispublic">
                                  </div>
                                  
                                <div class="form-group col-lg-4">
                                    <!-- <div *ngIf="originalRecordLink" class="annonymous mb-3">
                                    <label for="">Anonymous Link</label><br>
                                    <input type="hidden" class="form-control form-control-sm"
                                         required [readonly]="isReadonly">
                                    <audio class="audioPlayer" controls>
                                        <source src="{{anonymousLink}}" type="audio/ogg">
                                        <source src="{{anonymousLink}}" type="audio/mpeg">
                                    </audio><br>
                                </div>

                                 <div class="original">
                                    <label for="">{{originalRecordLink?'Original Insight Link':'Insight Link'}}</label><br>
                                    <input type="hidden" class="form-control form-control-sm"
                                        formControlName="AN_recordLink" required [readonly]="isReadonly">
                                    <audio controls>
                                        <source src="{{originalRecordLink?originalRecordLink:selectsoundurl}}" type="audio/ogg">
                                        <source src="{{originalRecordLink?originalRecordLink:selectsoundurl}}" type="audio/mpeg">
                                    </audio><br>
                                         
                                Generate Transcription Button -->
                                    <!-- <div *ngIf="this.showButton" class="Gnrt-Transcription-btn">
                                        <button (click)="GenerateTranscriptById()" class="text-center" class="btn btn-primary mr-2 mt-2">{{showRegenerateButton ? 'Regenerate Transcription': 'Generate Transcription'}}</button>
                                    </div> -->

                                <!--Generate Anonymous Insight Button -->
                                    <!-- <div *ngIf="isanonimise==='Yes'&&transcriptionGenerated&&!originalRecordLink" class="Gnrt-Transcription-btn">
                                        <button (click)="showAnonymousModal()" class="text-center" class="btn btn-primary mr-2 mt-2">Generate Anonymous Insight</button>
                                    </div> 
                                -->
                                   
                                <div class="original">
                                    <label for="AN_recordLink">Insight Link</label><br>
                                    <input 
                                        type="hidden" 
                                        class="form-control form-control-sm" 
                                        formControlName="AN_recordLink" 
                                        required 
                                        [readonly]="isReadonly">
                                    <audio controls *ngIf="selectsoundurl">
                                        <source [attr.src]="selectsoundurl" type="audio/ogg">
                                        <source [attr.src]="selectsoundurl" type="audio/mpeg">
                                        Your browser does not support the audio element.
                                    </audio><br>
                                </div>
                                
                            </div>
                                
                                <div class="form-group col-lg-8">
                                    <label for="">Transcription
                                    </label><br>
                                    <textarea class="form-control transcription" style="font-size: 22px;"
                                        id="exampleFormControlTextarea1" rows="8" formControlName="AN_transcription" [readOnly]="isReadonly||anonymousGenerated"></textarea>
                                        <a *ngIf="showApproveBtn&&transcriptionGenerated&&!anonymousGenerated" (click)="changeAction()" class="saveBtn" >{{isReadonly?'Edit':'Save'}}</a>
                                </div>
                            </div>
                            <div class="text-center mt-4">
                                <button *ngIf="showApproveBtn" type="submit" class="btn btn-success" data-toggle="modal"
                                    data-target="#confirmtoapprove">Approve</button>&nbsp;
                                <button type="submit" class="btn btn-danger" data-toggle="modal"
                                    data-target="#myModal">Reject</button>&nbsp;
                                <button class="btn btn-secondary" [ngClass]="{'cncl-btn': showApproveBtn}" (click)="listPage()">Cancel</button>
                            </div>
                            <!--Modal Launch Button-->
                            <!-- <button type="button" class="btn btn-info btn-lg openmodal" data-toggle="modal" data-target="#myModal">Open
Modal</button> -->
                            <div id="confirmtoapprove" class="modal fade" role="dialog">
                                <!--Modal-->
                                <div class="modal-dialog mar-top">
                                    <!--Modal Content-->
                                    <div class="modal-content">
                                        <!-- Modal Header-->
                                        <div class="modal-header">
                                            <h3 style="margin-left: 150px;">Confirmation !</h3>
                                            <!--Close/Cross Button-->
                                            <button type="button" class="close" data-dismiss="modal"
                                                style="color: white;">&times;</button>
                                        </div>
                                        <!-- Modal Body-->
                                        <div class="modal-body text-center">
                                            <!-- <i class="far fa-file-alt fa-4x mb-3 animated rotateIn icon1"></i> -->
                                            <strong>Are you sure you want to approve this Insight ?</strong>
                                        </div>
                                        <div class="modal-footer">
                                            <button class="btn btn-primary" id="left" data-dismiss="modal"
                                                (click)="checkUpdateStatus(selectedRecord,2)">Confirm
                                            </button>
                                            <button class="btn btn-primary" id="right"
                                                data-dismiss="modal">Cancel</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--Division for Modal-->
                           
                            <!-- <div class="form-group col-lg-3">
              <button type="submit" class="btn btn-primary mr-2">Save</button>
            </div>
            <div class="form-group col-lg-3">
              <button class="btn btn-primary mr-2" (click)="showForm=false">Cancel</button>
            </div> -->

                        </form>
                    </div>
                </div>
            </div>
        </div>

            <div class="py-3 px-3" id="certificate" style="position: absolute; top: 0; left: -999999px; visibility: visible;">
              <div class="image text-center mb-2">  
                <img [src]="getBadgeImage(certificateTitle)" alt="Badge" width="80" height="80">
              </div>
              <div class="title text-center">
                <h3 class="font-weight-bold">{{certificateTitle}}</h3>
                <span class="badge badge-pill badge-certificate mt-2 font-weight-bold">gradvisor certified</span>
              </div>
              <div class="name text-center mt-3">
                <span class="p-block mb-1 font-weight-bold">Sharer</span>
                <span class="p-block">{{userName}}</span>
              </div>
              <div class="career text-center mt-3">
                <span class="p-block mb-1 font-weight-bold">Career</span>
                <span class="p-block">{{roleName}} in</span>
                <span class="p-block">{{industryName}}</span>
              </div>
              <div class="date text-center mt-3">
                <span class="p-block mb-1 font-weight-bold">Date Earned</span>
                <span class="p-block">{{dateEarned}}</span>
              </div>
              <div class="msg d-flex justify-content-center mt-3">
                <div class="d-flex justify-content-center">
                  <p class="text-center">{{certificateDescription}}</p>
                </div>
              </div>
              <div class="progress-container text-center mt-3">
                <span class="p-block font-weight-bold mb-2">Your Progress</span>
                <circle-progress class="progress-meter d-flex justify-content-center"
                  [percent]="BA_percentage"
                  [maxPercent]="100"
                  [toFixed]="0"
                  [radius]="30"
                  [space]="-8"
                  [outerStrokeGradient]="true"
                  [outerStrokeWidth]="8"
                  [outerStrokeColor]="'#4882c2'"
                  [outerStrokeGradientStopColor]="'#53a9ff'"
                  [innerStrokeColor]="'#e7e8ea'"
                  [innerStrokeWidth]="8"
                  [showTitle]="true"
                  [showUnits]="true"
                  [showSubtitle]="false"
                  [showBackground]="false"
                  [showInnerStroke]="true"
                  [clockwise]="false"
                  [responsive]="false"
                  [startFromZero]="false"
                  [showZeroOuterStroke]="true"
                ></circle-progress>
              </div>
              <div class="image text-start mt-2">  
                <img src="./assets/images/Orange icon_transperant.png" alt="" width="24" height="27">
                <span class="brandlogo">gradvisor</span>
              </div>   
            </div>
          
          <!-- <div class="Transcription-modal modal" id="Transcription-modal">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title fs-5" id="staticBackdropLabel">Insight Status</h4>
                    </div>
                    <div class="modal-body">
                        <p>You have already generated a transcription, Would you like to regenerate transcription?</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" (click)="GenerateTranscriptById(1)" class="btn btn-primary" data-bs-dismiss="Transcription-modal">Yes</button>
                        <button type="button" (click)="hideModal()" class="btn btn-secondary" data-bs-dismiss="Transcription-modal">No</button>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- <div class="anonymous-warning-modal modal" id="anonymous-warning-modal">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title fs-5" id="anonymousBackdropLabel">Warning !!</h4>
                    </div>
                    <div class="modal-body">
                        <p>Please ensure you have verified the generated transcription. You will not be able to edit the transcription after generating anonymous insight.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" (click)="generateAnnonymousRecording()" class="btn btn-primary" data-bs-dismiss="Transcription-modal">Generate Anonymous Insight</button>
                        <button type="button" (click)="hideAnonymousModal()" class="btn btn-secondary" data-bs-dismiss="Transcription-modal">Verify Transcription</button>
                    </div>
                </div>
            </div>
        </div> -->

    </div>
    </app-sidebar>

    <div id="myModal" class="modal fade" role="dialog">

        <!--Modal-->
        <div class="modal-dialog">

            <!--Modal Content-->
            <div class="modal-content">

                <!-- Modal Header-->
                <div class="modal-header">
                    <h3>Reason For Rejection</h3>

                    <!--Close/Cross Button-->
                    <button type="button" class="close" data-dismiss="modal"
                        style="color: white;">&times;</button>
                </div>

                <!-- Modal Body-->
                <div class="modal-body text-center">
                    <!-- <i class="far fa-file-alt fa-4x mb-3 animated rotateIn icon1"></i> -->
                    <strong>Please Kindly Give Your Reason.</strong>
                    <hr>
                </div>

                <!-- Radio Buttons for Rating-->
                <div class="form-check mb-3">
                    <input name="feedback" type="radio" value="It's irrelevant"
                        (change)="radiovalue($event,'existingReason')">
                    <label class="ml-3">It's irrelevant</label>
                </div>
                <div class="form-check mb-3">
                    <input name="feedback" type="radio" value="It's not right"
                        (change)="radiovalue($event,'existingReason')">
                    <label class="ml-3">It's not right</label>
                </div>
                <div class="form-check mb-3">
                    <input name="feedback" type="radio" value="its inappropriate"
                        (change)="radiovalue($event,'existingReason')">
                    <label class="ml-3">its inappropriate</label>
                </div>
                <div class="form-check mb-4">
                    <input name="feedback" type="radio" (click)="showbox()"
                        value="Other" (change)="radiovalue($event,'Other')">
                    <label class="ml-3">Other</label>
                </div>
                <div *ngIf="showhide" class="mx-4">
                    <textarea type="textarea" class="form-control" placeholder="Other reason"
                        #box (input)="checkWordLimit(box.value)" rows="4"></textarea>
                    <div *ngIf="wordCount > wordLimit" class="text-danger">
                        Word limit exceeded! ({{ wordCount }}/{{ wordLimit }} words)
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button class="btn btn-primary" id="left" data-dismiss="modal"
                        (click)="checkUpdateStatus(selectedRecord,3)">Confirm
                    </button>
                    <button class="btn btn-primary" id="right"
                        data-dismiss="modal">Cancel</button>
                </div>                                                       

            </div>

        </div>

    </div>
<app-sidebar>
    <div class="content-wrapper">
            <div class="row mb-4 mx-2">
                <!-- Add and search bar -->
                <div class="col-lg-3 mb-2 mb-lg-0">
                  <button type="submit" (click)="addRecord()" class="btn btn-primary w-100">Add New Opportunity</button>
                </div>
                <div class="col-lg-6">
                  <div class="input-group">
                    <input type="text" [(ngModel)]="term" class="form-control shadow-sm rounded-start" placeholder="Search here"
                      style="width: 100%;" aria-label="Search now">
                  </div>
                </div>
                <div class="col-lg-3 mb-2 mb-lg-0">
                    <button type="submit" (click)="editEmployer('Edit')" class="btn edit-btn btn-primary w-100">Edit Employer</button>
                  </div>
              </div>  

            

            <div class="row">
            <div class="col-12 grid-margin stretch-card">
                <div class="card">
                    <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">Existing
                        Opportunities</div>
                    <div class="card-body">

                        <form class="forms-sample">
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead class="text-start">
                                        <tr>
                                            
                                            <th>
                                                Title
                                            </th>

                                            <th class="text-center">
                                                Opportunity Type
                                            </th>

                                            <th class="text-center">
                                                Application Deadline
                                            </th>

                                            <th class="text-center">
                                                Start Date
                                            </th>

                                            <th class="text-center">
                                                Mode Of Work
                                            </th>
                                            <th class="text-center">
                                                Action
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-start">
                                        <tr
                                            *ngFor="let opportunity of ExistingOpportunities  | filter: term|paginate: {itemsPerPage:7, currentPage:p}">
                                            <td>{{opportunity.JB_jobTitle | camelCase}}</td>
                                            <td class="text-center">{{opportunity.JB_type===1?'Grad Scheme':'Job'}}</td>
                                            <td class="text-center">{{opportunity.JB_applicationDeadline | date: 'MMM d, y'}}</td>
                                            <td class="text-center">{{opportunity.JB_startDate | date: 'MMM d, y'}}</td>
                                            <td class="text-center">{{opportunity.JB_modeOfWork | camelCase}}</td>
                                            <td class="text-center">
                                                <div class="btn-group" role="group" aria-label="Basic example">
                                                    <button type="button" class="btn btn-primary btn-sm"
                                                        (click)="editOpportunity(opportunity,'Edit')" placement="top"
                                                        ngbTooltip="Update">
                                                        <i class="ti-pencil text-white"></i>
                                                    </button>

                                                    <button type="button" class="btn btn-primary btn-sm"
                                                        (click)="viewOpportunity(opportunity,'View')" placement="top"
                                                        ngbTooltip="View full details">
                                                        <i class="ti-eye"></i>
                                                    </button>

                                                    <button type="button" class="btn btn-primary btn-sm" placement="top"
                                                        (click)="showDeleteModal(opportunity)" ngbTooltip="Delete"
                                                        data-toggle="modal" data-target="#myModal">
                                                        <i class="ti-trash text-white"></i>
                                                    </button>
                                                </div>

                                                
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <pagination-controls (pageChange)="p = $event"
                                    class="ml-1 text-center"></pagination-controls>
                            </div>
                            <div class="text-center footer">
                                <button class="btn btn-light" routerLink="/actions/employer-opportunities">Back</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        </div>
</app-sidebar>


<div id="myModal" class="modal fade" role="dialog">
    <!--Modal-->
    <div class="modal-dialog">
      <!--Modal Content-->
      <div class="modal-content">
        <!-- Modal Header-->
        <div class="modal-header">
          <h3>Delete warning !</h3>
          <!--Close/Cross Button-->
          <button type="button" class="close" data-dismiss="modal"
            style="color: white;">&times;</button>
        </div>
        <!-- Modal Body-->
        <div class="modal-body text-center">
          <i class="ti-trash" style="color: red;"></i>
          <h3> <strong>Are you sure?</strong></h3>
          <p class="mt-3">Do you really want to delete this opportunity?</p>
        </div>

        <!-- Modal Footer-->

        <div class="modal-footer text-center">
            <button type="button"
            class="btn btn-danger px-4"
            (click)="DeleteJobsById()"
            data-dismiss="modal">Delete
        </button>
         <a href="" class="btn btn-primary" data-dismiss="modal">Cancel</a>
        </div>

      </div>

    </div>

  </div>
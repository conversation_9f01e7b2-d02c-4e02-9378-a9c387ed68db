{"ast": null, "code": "/**\n * @license Angular v13.0.3\n * (c) 2010-2021 Google LLC. https://angular.io/\n * License: MIT\n */\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, setTestabilityGetter, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵINJECTOR_SCOPE, NgZone, RendererFactory2, Testability, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, ɵConsole, forwardRef, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵgetSanitizationBypassType, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor() {\n    super(...arguments);\n    this.supportsDOMEvents = true;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n\n/* tslint:disable:requireParameterType no-console */\n\n\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n\n  onAndCancel(el, evt, listener) {\n    el.addEventListener(evt, listener, false); // Needed to follow Dart's subscription semantic, until fix of\n    // https://code.google.com/p/dart/issues/detail?id=17406\n\n    return () => {\n      el.removeEventListener(evt, listener, false);\n    };\n  }\n\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n\n  remove(node) {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  }\n\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n\n  getDefaultDocument() {\n    return document;\n  }\n\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n\n\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n\n    if (target === 'document') {\n      return doc;\n    }\n\n    if (target === 'body') {\n      return doc.body;\n    }\n\n    return null;\n  }\n\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n\n  resetBaseElement() {\n    baseElement = null;\n  }\n\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n\n}\n\nlet baseElement = null;\n\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n} // based on urlUtils.js in AngularJS 1\n\n\nlet urlParsingNode;\n\nfunction relativePath(url) {\n  urlParsingNode = urlParsingNode || document.createElement('a');\n  urlParsingNode.setAttribute('href', url);\n  const pathName = urlParsingNode.pathname;\n  return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\n\n\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\n\nfunction appInitializerFactory(transitionId, document, injector) {\n  return () => {\n    // Wait for all application initializers to be completed before removing the styles set by\n    // the server.\n    injector.get(ApplicationInitStatus).donePromise.then(() => {\n      const dom = ɵgetDOM();\n      const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n\n      for (let i = 0; i < styles.length; i++) {\n        dom.remove(styles[i]);\n      }\n    });\n  };\n}\n\nconst SERVER_TRANSITION_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: appInitializerFactory,\n  deps: [TRANSITION_ID, DOCUMENT, Injector],\n  multi: true\n}];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass BrowserGetTestability {\n  static init() {\n    setTestabilityGetter(new BrowserGetTestability());\n  }\n\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n\n      if (testability == null) {\n        throw new Error('Could not find testability for element.');\n      }\n\n      return testability;\n    };\n\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n\n    const whenAllStable = (callback\n    /** TODO #9100 */\n    ) => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      let didWork = false;\n\n      const decrement = function (didWork_\n      /** TODO #9100 */\n      ) {\n        didWork = didWork || didWork_;\n        count--;\n\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n\n      testabilities.forEach(function (testability\n      /** TODO #9100 */\n      ) {\n        testability.whenStable(decrement);\n      });\n    };\n\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n\n    const t = registry.getTestability(elem);\n\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n\n}\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\n\n\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n\n}\n\nBrowserXhr.ɵfac = function BrowserXhr_Factory(t) {\n  return new (t || BrowserXhr)();\n};\n\nBrowserXhr.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BrowserXhr,\n  factory: BrowserXhr.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\n\n\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\n\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    this._eventNameToPlugin = new Map();\n    plugins.forEach(p => p.manager = this);\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n\n\n  addEventListener(element, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n\n    return plugin.addEventListener(element, eventName, handler);\n  }\n  /**\n   * Registers a global handler for an event in a target view.\n   *\n   * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns A callback function that can be used to remove the handler.\n   * @deprecated No longer being used in Ivy code. To be removed in version 14.\n   */\n\n\n  addGlobalEventListener(target, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n\n    return plugin.addGlobalEventListener(target, eventName, handler);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n\n\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n\n\n  _findPluginFor(eventName) {\n    const plugin = this._eventNameToPlugin.get(eventName);\n\n    if (plugin) {\n      return plugin;\n    }\n\n    const plugins = this._plugins;\n\n    for (let i = 0; i < plugins.length; i++) {\n      const plugin = plugins[i];\n\n      if (plugin.supports(eventName)) {\n        this._eventNameToPlugin.set(eventName, plugin);\n\n        return plugin;\n      }\n    }\n\n    throw new Error(`No event manager plugin found for event ${eventName}`);\n  }\n\n}\n\nEventManager.ɵfac = function EventManager_Factory(t) {\n  return new (t || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n};\n\nEventManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: EventManager,\n  factory: EventManager.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [EVENT_MANAGER_PLUGINS]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n\nclass EventManagerPlugin {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n\n  addGlobalEventListener(element, eventName, handler) {\n    const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n\n    if (!target) {\n      throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n    }\n\n    return this.addEventListener(target, eventName, handler);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass SharedStylesHost {\n  constructor() {\n    /** @internal */\n    this._stylesSet = new Set();\n  }\n\n  addStyles(styles) {\n    const additions = new Set();\n    styles.forEach(style => {\n      if (!this._stylesSet.has(style)) {\n        this._stylesSet.add(style);\n\n        additions.add(style);\n      }\n    });\n    this.onStylesAdded(additions);\n  }\n\n  onStylesAdded(additions) {}\n\n  getAllStyles() {\n    return Array.from(this._stylesSet);\n  }\n\n}\n\nSharedStylesHost.ɵfac = function SharedStylesHost_Factory(t) {\n  return new (t || SharedStylesHost)();\n};\n\nSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: SharedStylesHost,\n  factory: SharedStylesHost.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], null, null);\n})();\n\nclass DomSharedStylesHost extends SharedStylesHost {\n  constructor(_doc) {\n    super();\n    this._doc = _doc; // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n\n    this._hostNodes = new Map();\n\n    this._hostNodes.set(_doc.head, []);\n  }\n\n  _addStylesToHost(styles, host, styleNodes) {\n    styles.forEach(style => {\n      const styleEl = this._doc.createElement('style');\n\n      styleEl.textContent = style;\n      styleNodes.push(host.appendChild(styleEl));\n    });\n  }\n\n  addHost(hostNode) {\n    const styleNodes = [];\n\n    this._addStylesToHost(this._stylesSet, hostNode, styleNodes);\n\n    this._hostNodes.set(hostNode, styleNodes);\n  }\n\n  removeHost(hostNode) {\n    const styleNodes = this._hostNodes.get(hostNode);\n\n    if (styleNodes) {\n      styleNodes.forEach(removeStyle);\n    }\n\n    this._hostNodes.delete(hostNode);\n  }\n\n  onStylesAdded(additions) {\n    this._hostNodes.forEach((styleNodes, hostNode) => {\n      this._addStylesToHost(additions, hostNode, styleNodes);\n    });\n  }\n\n  ngOnDestroy() {\n    this._hostNodes.forEach(styleNodes => styleNodes.forEach(removeStyle));\n  }\n\n}\n\nDomSharedStylesHost.ɵfac = function DomSharedStylesHost_Factory(t) {\n  return new (t || DomSharedStylesHost)(i0.ɵɵinject(DOCUMENT));\n};\n\nDomSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSharedStylesHost,\n  factory: DomSharedStylesHost.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSharedStylesHost, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nfunction removeStyle(styleNode) {\n  ɵgetDOM().remove(styleNode);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nfunction flattenStyles(compId, styles, target) {\n  for (let i = 0; i < styles.length; i++) {\n    let style = styles[i];\n\n    if (Array.isArray(style)) {\n      flattenStyles(compId, style, target);\n    } else {\n      style = style.replace(COMPONENT_REGEX, compId);\n      target.push(style);\n    }\n  }\n\n  return target;\n}\n\nfunction decoratePreventDefault(eventHandler) {\n  // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n  // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n  // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n  // the listener (see below).\n  return event => {\n    // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n    // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n    // can inspect the listener toString contents for the existence of this special token. Because\n    // the token is a string literal, it is ensured to not be modified by compiled code.\n    if (event === '__ngUnwrap__') {\n      return eventHandler;\n    }\n\n    const allowDefaultBehavior = eventHandler(event);\n\n    if (allowDefaultBehavior === false) {\n      // TODO(tbosch): move preventDefault into event plugins...\n      event.preventDefault();\n      event.returnValue = false;\n    }\n\n    return undefined;\n  };\n}\n\nlet hasLoggedNativeEncapsulationWarning = false;\n\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.rendererByCompId = new Map();\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n  }\n\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n\n    switch (type.encapsulation) {\n      case ViewEncapsulation.Emulated:\n        {\n          let renderer = this.rendererByCompId.get(type.id);\n\n          if (!renderer) {\n            renderer = new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type, this.appId);\n            this.rendererByCompId.set(type.id, renderer);\n          }\n\n          renderer.applyToHost(element);\n          return renderer;\n        }\n      // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an enum\n      // value that is not known (but previously was the value for ViewEncapsulation.Native)\n\n      case 1:\n      case ViewEncapsulation.ShadowDom:\n        // TODO(FW-2290): remove the `case 1:` fallback logic and the warning in v12.\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an\n        // enum value that is not known (but previously was the value for\n        // ViewEncapsulation.Native)\n        !hasLoggedNativeEncapsulationWarning && type.encapsulation === 1) {\n          hasLoggedNativeEncapsulationWarning = true;\n          console.warn('ViewEncapsulation.Native is no longer supported. Falling back to ViewEncapsulation.ShadowDom. The fallback will be removed in v12.');\n        }\n\n        return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n\n      default:\n        {\n          if (!this.rendererByCompId.has(type.id)) {\n            const styles = flattenStyles(type.id, type.styles, []);\n            this.sharedStylesHost.addStyles(styles);\n            this.rendererByCompId.set(type.id, this.defaultRenderer);\n          }\n\n          return this.defaultRenderer;\n        }\n    }\n  }\n\n  begin() {}\n\n  end() {}\n\n}\n\nDomRendererFactory2.ɵfac = function DomRendererFactory2_Factory(t) {\n  return new (t || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(DomSharedStylesHost), i0.ɵɵinject(APP_ID));\n};\n\nDomRendererFactory2.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomRendererFactory2,\n  factory: DomRendererFactory2.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: EventManager\n    }, {\n      type: DomSharedStylesHost\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [APP_ID]\n      }]\n    }];\n  }, null);\n})();\n\nclass DefaultDomRenderer2 {\n  constructor(eventManager) {\n    this.eventManager = eventManager;\n    this.data = Object.create(null);\n    this.destroyNode = null;\n  }\n\n  destroy() {}\n\n  createElement(name, namespace) {\n    if (namespace) {\n      // In cases where Ivy (not ViewEngine) is giving us the actual namespace, the look up by key\n      // will result in undefined, so we just return the namespace here.\n      return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n\n    return document.createElement(name);\n  }\n\n  createComment(value) {\n    return document.createComment(value);\n  }\n\n  createText(value) {\n    return document.createTextNode(value);\n  }\n\n  appendChild(parent, newChild) {\n    parent.appendChild(newChild);\n  }\n\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      parent.insertBefore(newChild, refChild);\n    }\n  }\n\n  removeChild(parent, oldChild) {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) : selectorOrNode;\n\n    if (!el) {\n      throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n\n    return el;\n  }\n\n  parentNode(node) {\n    return node.parentNode;\n  }\n\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name; // TODO(FW-811): Ivy may cause issues here because it's passing around\n      // full URIs for namespaces, therefore this lookup will fail.\n\n      const namespaceUri = NAMESPACE_URIS[namespace];\n\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      // TODO(FW-811): Ivy may cause issues here because it's passing around\n      // full URIs for namespaces, therefore this lookup will fail.\n      const namespaceUri = NAMESPACE_URIS[namespace];\n\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        // TODO(FW-811): Since ivy is passing around full URIs for namespaces\n        // this could result in properties like `http://www.w3.org/2000/svg:cx=\"123\"`,\n        // which is wrong.\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.removeProperty(style);\n    } else {\n      // IE requires '' instead of null\n      // see https://github.com/angular/angular/issues/7916\n      el.style[style] = '';\n    }\n  }\n\n  setProperty(el, name, value) {\n    NG_DEV_MODE && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n\n  listen(target, event, callback) {\n    NG_DEV_MODE && checkNoSyntheticProp(event, 'listener');\n\n    if (typeof target === 'string') {\n      return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n    }\n\n    return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n  }\n\n}\n\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\n\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\n\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, appId) {\n    super(eventManager);\n    this.component = component;\n    const styles = flattenStyles(appId + '-' + component.id, component.styles, []);\n    sharedStylesHost.addStyles(styles);\n    this.contentAttr = shimContentAttribute(appId + '-' + component.id);\n    this.hostAttr = shimHostAttribute(appId + '-' + component.id);\n  }\n\n  applyToHost(element) {\n    super.setAttribute(element, this.hostAttr, '');\n  }\n\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n\n}\n\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component) {\n    super(eventManager);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = flattenStyles(component.id, component.styles, []);\n\n    for (let i = 0; i < styles.length; i++) {\n      const styleEl = document.createElement('style');\n      styleEl.textContent = styles[i];\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n\n  removeChild(parent, oldChild) {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  } // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n\n\n  supports(eventName) {\n    return true;\n  }\n\n  addEventListener(element, eventName, handler) {\n    element.addEventListener(eventName, handler, false);\n    return () => this.removeEventListener(element, eventName, handler);\n  }\n\n  removeEventListener(target, eventName, callback) {\n    return target.removeEventListener(eventName, callback);\n  }\n\n}\n\nDomEventsPlugin.ɵfac = function DomEventsPlugin_Factory(t) {\n  return new (t || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\n\nDomEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomEventsPlugin,\n  factory: DomEventsPlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Defines supported modifiers for key events.\n */\n\n\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\nconst DOM_KEY_LOCATION_NUMPAD = 3; // Map to convert some key or keyIdentifier values to what will be returned by getEventKey\n\nconst _keyMap = {\n  // The following values are here for cross-browser compatibility and to match the W3C standard\n  // cf https://www.w3.org/TR/DOM-Level-3-Events-key/\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n}; // There is a bug in Chrome for numeric keypad keys:\n// https://code.google.com/p/chromium/issues/detail?id=155654\n// 1, 2, 3 ... are reported as A, B, C ...\n\nconst _chromeNumKeyPadMap = {\n  'A': '1',\n  'B': '2',\n  'C': '3',\n  'D': '4',\n  'E': '5',\n  'F': '6',\n  'G': '7',\n  'H': '8',\n  'I': '9',\n  'J': '*',\n  'K': '+',\n  'M': '-',\n  'N': '.',\n  'O': '/',\n  '\\x60': '0',\n  '\\x90': 'NumLock'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\n\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\n\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n\n\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n\n\n  addEventListener(element, eventName, handler) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n\n    let fullKey = '';\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    } // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n\n\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n\n  static getEventFullKey(event) {\n    let fullKey = '';\n    let key = getEventKey(event);\n    key = key.toLowerCase();\n\n    if (key === ' ') {\n      key = 'space'; // for readability\n    } else if (key === '.') {\n      key = 'dot'; // because '.' is used as a separator in event names\n    }\n\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName != key) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n\n        if (modifierGetter(event)) {\n          fullKey += modifierName + '.';\n        }\n      }\n    });\n    fullKey += key;\n    return fullKey;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n\n\n  static eventCallback(fullKey, handler, zone) {\n    return (event\n    /** TODO #9100 */\n    ) => {\n      if (KeyEventsPlugin.getEventFullKey(event) === fullKey) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n\n\n  static _normalizeKey(keyName) {\n    // TODO: switch to a Map if the mapping grows too much\n    switch (keyName) {\n      case 'esc':\n        return 'escape';\n\n      default:\n        return keyName;\n    }\n  }\n\n}\n\nKeyEventsPlugin.ɵfac = function KeyEventsPlugin_Factory(t) {\n  return new (t || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\n\nKeyEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: KeyEventsPlugin,\n  factory: KeyEventsPlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nfunction getEventKey(event) {\n  let key = event.key;\n\n  if (key == null) {\n    key = event.keyIdentifier; // keyIdentifier is defined in the old draft of DOM Level 3 Events implemented by Chrome and\n    // Safari cf\n    // https://www.w3.org/TR/2007/WD-DOM-Level-3-Events-20071221/events.html#Events-KeyboardEvents-Interfaces\n\n    if (key == null) {\n      return 'Unidentified';\n    }\n\n    if (key.startsWith('U+')) {\n      key = String.fromCharCode(parseInt(key.substring(2), 16));\n\n      if (event.location === DOM_KEY_LOCATION_NUMPAD && _chromeNumKeyPadMap.hasOwnProperty(key)) {\n        // There is a bug in Chrome for numeric keypad keys:\n        // https://code.google.com/p/chromium/issues/detail?id=155654\n        // 1, 2, 3 ... are reported as A, B, C ...\n        key = _chromeNumKeyPadMap[key];\n      }\n    }\n  }\n\n  return _keyMap[key] || key;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n  BrowserGetTestability.init();\n}\n\nfunction errorHandler() {\n  return new ErrorHandler();\n}\n\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\n\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\n\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT, NgZone, PLATFORM_ID]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: DomRendererFactory2,\n  useClass: DomRendererFactory2,\n  deps: [EventManager, DomSharedStylesHost, APP_ID]\n}, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: SharedStylesHost,\n  useExisting: DomSharedStylesHost\n}, {\n  provide: DomSharedStylesHost,\n  useClass: DomSharedStylesHost,\n  deps: [DOCUMENT]\n}, {\n  provide: Testability,\n  useClass: Testability,\n  deps: [NgZone]\n}, {\n  provide: EventManager,\n  useClass: EventManager,\n  deps: [EVENT_MANAGER_PLUGINS, NgZone]\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\n\nclass BrowserModule {\n  constructor(parentModule) {\n    if (parentModule) {\n      throw new Error(`BrowserModule has already been loaded. If you need access to common directives such as NgIf and NgFor from a lazy loaded module, import CommonModule instead.`);\n    }\n  }\n  /**\n   * Configures a browser-based app to transition from a server-rendered app, if\n   * one is present on the page.\n   *\n   * @param params An object containing an identifier for the app to transition.\n   * The ID must match between the client and server versions of the app.\n   * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n   */\n\n\n  static withServerTransition(params) {\n    return {\n      ngModule: BrowserModule,\n      providers: [{\n        provide: APP_ID,\n        useValue: params.appId\n      }, {\n        provide: TRANSITION_ID,\n        useExisting: APP_ID\n      }, SERVER_TRANSITION_PROVIDERS]\n    };\n  }\n\n}\n\nBrowserModule.ɵfac = function BrowserModule_Factory(t) {\n  return new (t || BrowserModule)(i0.ɵɵinject(BrowserModule, 12));\n};\n\nBrowserModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserModule\n});\nBrowserModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: BROWSER_MODULE_PROVIDERS,\n  imports: [CommonModule, ApplicationModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: BROWSER_MODULE_PROVIDERS,\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], function () {\n    return [{\n      type: BrowserModule,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [BrowserModule]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\n\n\nfunction createMeta() {\n  return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\n\n\nclass Meta {\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = ɵgetDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n\n\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n\n\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n\n\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n\n\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n\n    const list\n    /*NodeList*/\n    = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n\n\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n\n\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n\n\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta); // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n\n\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n\n    const element = this._dom.createElement('meta');\n\n    this._setMetaElementAttributes(meta, element);\n\n    const head = this._doc.getElementsByTagName('head')[0];\n\n    head.appendChild(element);\n    return element;\n  }\n\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n\n}\n\nMeta.ɵfac = function Meta_Factory(t) {\n  return new (t || Meta)(i0.ɵɵinject(DOCUMENT));\n};\n\nMeta.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Meta,\n  factory: function Meta_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = createMeta();\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createMeta,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\n\n\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory to create Title service.\n */\n\nfunction createTitle() {\n  return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\n\n\nclass Title {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n\n\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n\n\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n\n}\n\nTitle.ɵfac = function Title_Factory(t) {\n  return new (t || Title)(i0.ɵɵinject(DOCUMENT));\n};\n\nTitle.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Title,\n  factory: function Title_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = createTitle();\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createTitle,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst CAMEL_CASE_REGEXP = /([A-Z])/g;\nconst DASH_CASE_REGEXP = /-([a-z])/g;\n\nfunction camelCaseToDashCase(input) {\n  return input.replace(CAMEL_CASE_REGEXP, (...m) => '-' + m[1].toLowerCase());\n}\n\nfunction dashCaseToCamelCase(input) {\n  return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\n\n\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst win = typeof window !== 'undefined' && window || {};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\n\n\nclass AngularProfiler {\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  } // tslint:disable:no-console\n\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n\n\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection'; // Profiler is not available in Android browsers without dev tools opened\n\n    const isProfilerAvailable = win.console.profile != null;\n\n    if (record && isProfilerAvailable) {\n      win.console.profile(profileName);\n    }\n\n    const start = performanceNow();\n    let numTicks = 0;\n\n    while (numTicks < 5 || performanceNow() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n\n    const end = performanceNow();\n\n    if (record && isProfilerAvailable) {\n      win.console.profileEnd(profileName);\n    }\n\n    const msPerTick = (end - start) / numTicks;\n    win.console.log(`ran ${numTicks} change detection cycles`);\n    win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n\n}\n\nfunction performanceNow() {\n  return win.performance && win.performance.now ? win.performance.now() : new Date().getTime();\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\n\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\n\n\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction escapeHtml(text) {\n  const escapedText = {\n    '&': '&a;',\n    '\"': '&q;',\n    '\\'': '&s;',\n    '<': '&l;',\n    '>': '&g;'\n  };\n  return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\n\nfunction unescapeHtml(text) {\n  const unescapedText = {\n    '&a;': '&',\n    '&q;': '\"',\n    '&s;': '\\'',\n    '&l;': '<',\n    '&g;': '>'\n  };\n  return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\n\n\nfunction makeStateKey(key) {\n  return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * `TransferState` will be available as an injectable token. To use it import\n * `ServerTransferStateModule` on the server and `BrowserTransferStateModule` on the client.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\n\n\nclass TransferState {\n  constructor() {\n    this.store = {};\n    this.onSerializeCallbacks = {};\n  }\n  /** @internal */\n\n\n  static init(initState) {\n    const transferState = new TransferState();\n    transferState.store = initState;\n    return transferState;\n  }\n  /**\n   * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n   */\n\n\n  get(key, defaultValue) {\n    return this.store[key] !== undefined ? this.store[key] : defaultValue;\n  }\n  /**\n   * Set the value corresponding to a key.\n   */\n\n\n  set(key, value) {\n    this.store[key] = value;\n  }\n  /**\n   * Remove a key from the store.\n   */\n\n\n  remove(key) {\n    delete this.store[key];\n  }\n  /**\n   * Test whether a key exists in the store.\n   */\n\n\n  hasKey(key) {\n    return this.store.hasOwnProperty(key);\n  }\n  /**\n   * Register a callback to provide the value for a key when `toJson` is called.\n   */\n\n\n  onSerialize(key, callback) {\n    this.onSerializeCallbacks[key] = callback;\n  }\n  /**\n   * Serialize the current state of the store to JSON.\n   */\n\n\n  toJson() {\n    // Call the onSerialize callbacks and put those values into the store.\n    for (const key in this.onSerializeCallbacks) {\n      if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n        try {\n          this.store[key] = this.onSerializeCallbacks[key]();\n        } catch (e) {\n          console.warn('Exception in onSerialize callback: ', e);\n        }\n      }\n    }\n\n    return JSON.stringify(this.store);\n  }\n\n}\n\nTransferState.ɵfac = function TransferState_Factory(t) {\n  return new (t || TransferState)();\n};\n\nTransferState.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TransferState,\n  factory: TransferState.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TransferState, [{\n    type: Injectable\n  }], null, null);\n})();\n\nfunction initTransferState(doc, appId) {\n  // Locate the script tag with the JSON data transferred from the server.\n  // The id of the script tag is set to the Angular appId + 'state'.\n  const script = doc.getElementById(appId + '-state');\n  let initialState = {};\n\n  if (script && script.textContent) {\n    try {\n      // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n      initialState = JSON.parse(unescapeHtml(script.textContent));\n    } catch (e) {\n      console.warn('Exception while restoring TransferState for app ' + appId, e);\n    }\n  }\n\n  return TransferState.init(initialState);\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n */\n\n\nclass BrowserTransferStateModule {}\n\nBrowserTransferStateModule.ɵfac = function BrowserTransferStateModule_Factory(t) {\n  return new (t || BrowserTransferStateModule)();\n};\n\nBrowserTransferStateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserTransferStateModule\n});\nBrowserTransferStateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: TransferState,\n    useFactory: initTransferState,\n    deps: [DOCUMENT, APP_ID]\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTransferStateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: TransferState,\n        useFactory: initTransferState,\n        deps: [DOCUMENT, APP_ID]\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\n\n\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n\n\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n\n\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n\n}\n\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n\n  return false;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Supported HammerJS recognizer event names.\n */\n\n\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\n\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\n\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\n\nclass HammerGestureConfig {\n  constructor() {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    this.events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n\n    this.overrides = {};\n  }\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n\n\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n\n    return mc;\n  }\n\n}\n\nHammerGestureConfig.ɵfac = function HammerGestureConfig_Factory(t) {\n  return new (t || HammerGestureConfig)();\n};\n\nHammerGestureConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGestureConfig,\n  factory: HammerGestureConfig.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\n\n\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  constructor(doc, _config, console, loader) {\n    super(doc);\n    this._config = _config;\n    this.console = console;\n    this.loader = loader;\n    this._loaderPromise = null;\n  }\n\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n\n      return false;\n    }\n\n    return true;\n  }\n\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase(); // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || this.loader(); // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n\n      let cancelRegistration = false;\n\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n\n      this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n\n          deregister = () => {};\n\n          return;\n        }\n\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n\n        deregister = () => {};\n      }); // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n\n\n      return () => {\n        deregister();\n      };\n    }\n\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback); // destroy mc to prevent memory leak\n\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n\n}\n\nHammerGesturesPlugin.ɵfac = function HammerGesturesPlugin_Factory(t) {\n  return new (t || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.ɵConsole), i0.ɵɵinject(HAMMER_LOADER, 8));\n};\n\nHammerGesturesPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGesturesPlugin,\n  factory: HammerGesturesPlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: HammerGestureConfig,\n      decorators: [{\n        type: Inject,\n        args: [HAMMER_GESTURE_CONFIG]\n      }]\n    }, {\n      type: i0.ɵConsole\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [HAMMER_LOADER]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\n\n\nclass HammerModule {}\n\nHammerModule.ɵfac = function HammerModule_Factory(t) {\n  return new (t || HammerModule)();\n};\n\nHammerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HammerModule\n});\nHammerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: EVENT_MANAGER_PLUGINS,\n    useClass: HammerGesturesPlugin,\n    multi: true,\n    deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n  }, {\n    provide: HAMMER_GESTURE_CONFIG,\n    useClass: HammerGestureConfig,\n    deps: []\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\n\n\nclass DomSanitizer {}\n\nDomSanitizer.ɵfac = function DomSanitizer_Factory(t) {\n  return new (t || DomSanitizer)();\n};\n\nDomSanitizer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizer,\n  factory: function DomSanitizer_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new (t || DomSanitizer)();\n    } else {\n      r = i0.ɵɵinject(DomSanitizerImpl);\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\n\nfunction domSanitizerImplFactory(injector) {\n  return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\n\nclass DomSanitizerImpl extends DomSanitizer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n\n  sanitize(ctx, value) {\n    if (value == null) return null;\n\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n\n      case SecurityContext.HTML:\n        if (ɵallowSanitizationBypassAndThrow(value, \"HTML\"\n        /* Html */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n\n      case SecurityContext.STYLE:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Style\"\n        /* Style */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        return value;\n\n      case SecurityContext.SCRIPT:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Script\"\n        /* Script */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        throw new Error('unsafe value used in a script context');\n\n      case SecurityContext.URL:\n        const type = ɵgetSanitizationBypassType(value);\n\n        if (ɵallowSanitizationBypassAndThrow(value, \"URL\"\n        /* Url */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        return ɵ_sanitizeUrl(String(value));\n\n      case SecurityContext.RESOURCE_URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\"\n        /* ResourceUrl */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        throw new Error('unsafe value used in a resource URL context (see https://g.co/ng/security#xss)');\n\n      default:\n        throw new Error(`Unexpected SecurityContext ${ctx} (see https://g.co/ng/security#xss)`);\n    }\n  }\n\n  bypassSecurityTrustHtml(value) {\n    return ɵbypassSanitizationTrustHtml(value);\n  }\n\n  bypassSecurityTrustStyle(value) {\n    return ɵbypassSanitizationTrustStyle(value);\n  }\n\n  bypassSecurityTrustScript(value) {\n    return ɵbypassSanitizationTrustScript(value);\n  }\n\n  bypassSecurityTrustUrl(value) {\n    return ɵbypassSanitizationTrustUrl(value);\n  }\n\n  bypassSecurityTrustResourceUrl(value) {\n    return ɵbypassSanitizationTrustResourceUrl(value);\n  }\n\n}\n\nDomSanitizerImpl.ɵfac = function DomSanitizerImpl_Factory(t) {\n  return new (t || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n};\n\nDomSanitizerImpl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizerImpl,\n  factory: function DomSanitizerImpl_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = domSanitizerImplFactory(i0.ɵɵinject(Injector));\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: domSanitizerImplFactory,\n      deps: [Injector]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nconst VERSION = new Version('13.0.3');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, Title, TransferState, VERSION, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/platform-browser/fesm2020/platform-browser.mjs"], "names": ["ɵDomAdapter", "ɵsetRootDomAdapter", "ɵparseCookieValue", "ɵgetDOM", "DOCUMENT", "ɵPLATFORM_BROWSER_ID", "XhrFactory", "CommonModule", "i0", "InjectionToken", "ApplicationInitStatus", "APP_INITIALIZER", "Injector", "setTestabilityGetter", "ɵglobal", "Injectable", "Inject", "ViewEncapsulation", "APP_ID", "RendererStyleFlags2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵsetDocument", "PLATFORM_ID", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "ɵINJECTOR_SCOPE", "NgZone", "RendererFactory2", "Testability", "ApplicationModule", "NgModule", "Optional", "SkipSelf", "ɵɵinject", "ApplicationRef", "ɵConsole", "forwardRef", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "ɵgetSanitizationBypassType", "ɵ_sanitizeUrl", "ɵ_sanitizeHtml", "ɵbypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "Version", "GenericBrowserDomAdapter", "constructor", "arguments", "supportsDOMEvents", "BrowserDomAdapter", "makeCurrent", "onAndCancel", "el", "evt", "listener", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "querySelector", "getAttribute", "urlParsingNode", "url", "setAttribute", "pathName", "pathname", "char<PERSON>t", "TRANSITION_ID", "appInitializerFactory", "transitionId", "injector", "get", "donePromise", "then", "dom", "styles", "querySelectorAll", "i", "length", "SERVER_TRANSITION_PROVIDERS", "provide", "useFactory", "deps", "multi", "BrowserGetTestability", "init", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "Error", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "didWork", "decrement", "didWork_", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "ɵfac", "ɵprov", "type", "EVENT_MANAGER_PLUGINS", "EventManager", "plugins", "_zone", "_eventNameToPlugin", "Map", "p", "manager", "_plugins", "slice", "reverse", "element", "eventName", "handler", "plugin", "_findPluginFor", "addGlobalEventListener", "getZone", "supports", "set", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "SharedStylesHost", "_stylesSet", "Set", "addStyles", "additions", "style", "has", "add", "onStylesAdded", "getAllStyles", "Array", "from", "DomSharedStylesHost", "_hostNodes", "head", "_addStylesToHost", "styleNodes", "styleEl", "textContent", "append<PERSON><PERSON><PERSON>", "addHost", "hostNode", "removeHost", "removeStyle", "delete", "ngOnDestroy", "styleNode", "NAMESPACE_URIS", "COMPONENT_REGEX", "NG_DEV_MODE", "ngDevMode", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "flattenStyles", "compId", "isArray", "decoratePreventDefault", "<PERSON><PERSON><PERSON><PERSON>", "event", "allowDefaultBehavior", "preventDefault", "returnValue", "hasLoggedNativeEncapsulationWarning", "DomRendererFactory2", "eventManager", "sharedStylesHost", "appId", "rendererByCompId", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "Emulated", "renderer", "id", "EmulatedEncapsulationDomRenderer2", "applyToHost", "ShadowDom", "console", "warn", "ShadowDom<PERSON><PERSON><PERSON>", "begin", "end", "data", "Object", "create", "destroyNode", "destroy", "namespace", "createElementNS", "createComment", "value", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttribute", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeProperty", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "component", "contentAttr", "hostAttr", "hostEl", "shadowRoot", "attachShadow", "mode", "nodeOrShadowRoot", "DomEventsPlugin", "MODIFIER_KEYS", "DOM_KEY_LOCATION_NUMPAD", "_keyMap", "_chromeNumKeyPadMap", "MODIFIER_KEY_GETTERS", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "modifierName", "index", "indexOf", "splice", "result", "getEventFullKey", "getEventKey", "modifierGetter", "zone", "runGuarded", "keyName", "keyIdentifier", "startsWith", "String", "fromCharCode", "parseInt", "substring", "location", "hasOwnProperty", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "useValue", "platformBrowser", "BROWSER_MODULE_PROVIDERS", "useClass", "useExisting", "BrowserModule", "parentModule", "withServerTransition", "params", "ngModule", "providers", "ɵmod", "ɵinj", "exports", "createMeta", "Meta", "_dom", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "getTag", "attrSelector", "getTags", "list", "call", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "filter", "_containsAttributes", "getElementsByTagName", "keys", "prop", "_getMetaKeyMap", "attr", "every", "META_KEYS_MAP", "providedIn", "httpEquiv", "createTitle", "Title", "getTitle", "title", "setTitle", "newTitle", "CAMEL_CASE_REGEXP", "DASH_CASE_REGEXP", "camelCaseToDashCase", "input", "m", "dashCaseToCamelCase", "toUpperCase", "exportNgVar", "COMPILED", "ng", "win", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "ref", "appRef", "timeChangeDetection", "config", "record", "profileName", "isProfilerAvailable", "profile", "start", "performanceNow", "tick", "profileEnd", "log", "toFixed", "performance", "now", "Date", "getTime", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "escapeHtml", "text", "escapedText", "s", "unescapeHtml", "unescapedText", "makeStateKey", "TransferState", "store", "onSerializeCallbacks", "initState", "transferState", "defaultValue", "<PERSON><PERSON><PERSON>", "onSerialize", "to<PERSON><PERSON>", "e", "JSON", "stringify", "initTransferState", "script", "getElementById", "initialState", "parse", "BrowserTransferStateModule", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "n", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "events", "overrides", "buildHammer", "mc", "Hammer", "options", "enable", "HammerGesturesPlugin", "_config", "loader", "_loaderPromise", "isCustomEvent", "cancelRegistration", "deregister", "catch", "eventObj", "on", "off", "HammerModule", "Dom<PERSON><PERSON><PERSON>zer", "DomSanitizerImpl", "domSanitizerImplFactory", "sanitize", "ctx", "NONE", "HTML", "toString", "STYLE", "SCRIPT", "URL", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "VERSION", "ɵBrowserDomAdapter", "ɵBrowserGetTestability", "ɵDomEventsPlugin", "ɵDomRendererFactory2", "ɵDomSanitizerImpl", "ɵDomSharedStylesHost", "ɵHammerGesturesPlugin", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ɵKeyEventsPlugin", "ɵNAMESPACE_URIS", "ɵSharedStylesHost", "ɵTRANSITION_ID", "ɵescapeHtml", "ɵflattenStyles", "ɵinitDomAdapter", "ɵshimContentAttribute", "ɵshimHostAttribute"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,SAASA,WAAT,EAAsBC,kBAAtB,EAA0CC,iBAA1C,EAA6DC,OAA7D,EAAsEC,QAAtE,EAAgFC,oBAAhF,EAAsGC,UAAtG,EAAkHC,YAAlH,QAAsI,iBAAtI;AACA,SAASJ,OAAT,QAAwB,iBAAxB;AACA,OAAO,KAAKK,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,qBAAzB,EAAgDC,eAAhD,EAAiEC,QAAjE,EAA2EC,oBAA3E,EAAiGC,OAAjG,EAA0GC,UAA1G,EAAsHC,MAAtH,EAA8HC,iBAA9H,EAAiJC,MAAjJ,EAAyJC,mBAAzJ,EAA8KC,YAA9K,EAA4LC,YAA5L,EAA0MC,WAA1M,EAAuNC,oBAAvN,EAA6OC,qBAA7O,EAAoQC,YAApQ,EAAkRC,eAAlR,EAAmSC,MAAnS,EAA2SC,gBAA3S,EAA6TC,WAA7T,EAA0UC,iBAA1U,EAA6VC,QAA7V,EAAuWC,QAAvW,EAAiXC,QAAjX,EAA2XC,QAA3X,EAAqYC,cAArY,EAAqZC,QAArZ,EAA+ZC,UAA/Z,EAA2aC,eAA3a,EAA4bC,gCAA5b,EAA8dC,gBAA9d,EAAgfC,0BAAhf,EAA4gBC,aAA5gB,EAA2hBC,cAA3hB,EAA2iBC,4BAA3iB,EAAykBC,6BAAzkB,EAAwmBC,8BAAxmB,EAAwoBC,2BAAxoB,EAAqqBC,mCAArqB,EAA0sBC,OAA1sB,QAAytB,eAAztB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,wBAAN,SAAuClD,WAAvC,CAAmD;AAC/CmD,EAAAA,WAAW,GAAG;AACV,UAAM,GAAGC,SAAT;AACA,SAAKC,iBAAL,GAAyB,IAAzB;AACH;;AAJ8C;AAOnD;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,iBAAN,SAAgCJ,wBAAhC,CAAyD;AACnC,SAAXK,WAAW,GAAG;AACjBtD,IAAAA,kBAAkB,CAAC,IAAIqD,iBAAJ,EAAD,CAAlB;AACH;;AACDE,EAAAA,WAAW,CAACC,EAAD,EAAKC,GAAL,EAAUC,QAAV,EAAoB;AAC3BF,IAAAA,EAAE,CAACG,gBAAH,CAAoBF,GAApB,EAAyBC,QAAzB,EAAmC,KAAnC,EAD2B,CAE3B;AACA;;AACA,WAAO,MAAM;AACTF,MAAAA,EAAE,CAACI,mBAAH,CAAuBH,GAAvB,EAA4BC,QAA5B,EAAsC,KAAtC;AACH,KAFD;AAGH;;AACDG,EAAAA,aAAa,CAACL,EAAD,EAAKC,GAAL,EAAU;AACnBD,IAAAA,EAAE,CAACK,aAAH,CAAiBJ,GAAjB;AACH;;AACDK,EAAAA,MAAM,CAACC,IAAD,EAAO;AACT,QAAIA,IAAI,CAACC,UAAT,EAAqB;AACjBD,MAAAA,IAAI,CAACC,UAAL,CAAgBC,WAAhB,CAA4BF,IAA5B;AACH;AACJ;;AACDG,EAAAA,aAAa,CAACC,OAAD,EAAUC,GAAV,EAAe;AACxBA,IAAAA,GAAG,GAAGA,GAAG,IAAI,KAAKC,kBAAL,EAAb;AACA,WAAOD,GAAG,CAACF,aAAJ,CAAkBC,OAAlB,CAAP;AACH;;AACDG,EAAAA,kBAAkB,GAAG;AACjB,WAAOC,QAAQ,CAACC,cAAT,CAAwBC,kBAAxB,CAA2C,WAA3C,CAAP;AACH;;AACDJ,EAAAA,kBAAkB,GAAG;AACjB,WAAOE,QAAP;AACH;;AACDG,EAAAA,aAAa,CAACX,IAAD,EAAO;AAChB,WAAOA,IAAI,CAACY,QAAL,KAAkBC,IAAI,CAACC,YAA9B;AACH;;AACDC,EAAAA,YAAY,CAACf,IAAD,EAAO;AACf,WAAOA,IAAI,YAAYgB,gBAAvB;AACH;AACD;;;AACAC,EAAAA,oBAAoB,CAACZ,GAAD,EAAMa,MAAN,EAAc;AAC9B,QAAIA,MAAM,KAAK,QAAf,EAAyB;AACrB,aAAOC,MAAP;AACH;;AACD,QAAID,MAAM,KAAK,UAAf,EAA2B;AACvB,aAAOb,GAAP;AACH;;AACD,QAAIa,MAAM,KAAK,MAAf,EAAuB;AACnB,aAAOb,GAAG,CAACe,IAAX;AACH;;AACD,WAAO,IAAP;AACH;;AACDC,EAAAA,WAAW,CAAChB,GAAD,EAAM;AACb,UAAMiB,IAAI,GAAGC,kBAAkB,EAA/B;AACA,WAAOD,IAAI,IAAI,IAAR,GAAe,IAAf,GAAsBE,YAAY,CAACF,IAAD,CAAzC;AACH;;AACDG,EAAAA,gBAAgB,GAAG;AACfC,IAAAA,WAAW,GAAG,IAAd;AACH;;AACDC,EAAAA,YAAY,GAAG;AACX,WAAOR,MAAM,CAACS,SAAP,CAAiBC,SAAxB;AACH;;AACDC,EAAAA,SAAS,CAACC,IAAD,EAAO;AACZ,WAAO7F,iBAAiB,CAACsE,QAAQ,CAACwB,MAAV,EAAkBD,IAAlB,CAAxB;AACH;;AA7DoD;;AA+DzD,IAAIL,WAAW,GAAG,IAAlB;;AACA,SAASH,kBAAT,GAA8B;AAC1BG,EAAAA,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,aAAT,CAAuB,MAAvB,CAA7B;AACA,SAAOP,WAAW,GAAGA,WAAW,CAACQ,YAAZ,CAAyB,MAAzB,CAAH,GAAsC,IAAxD;AACH,C,CACD;;;AACA,IAAIC,cAAJ;;AACA,SAASX,YAAT,CAAsBY,GAAtB,EAA2B;AACvBD,EAAAA,cAAc,GAAGA,cAAc,IAAI3B,QAAQ,CAACL,aAAT,CAAuB,GAAvB,CAAnC;AACAgC,EAAAA,cAAc,CAACE,YAAf,CAA4B,MAA5B,EAAoCD,GAApC;AACA,QAAME,QAAQ,GAAGH,cAAc,CAACI,QAAhC;AACA,SAAOD,QAAQ,CAACE,MAAT,CAAgB,CAAhB,MAAuB,GAAvB,GAA6BF,QAA7B,GAAyC,IAAGA,QAAS,EAA5D;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMG,aAAa,GAAG,IAAIhG,cAAJ,CAAmB,eAAnB,CAAtB;;AACA,SAASiG,qBAAT,CAA+BC,YAA/B,EAA6CnC,QAA7C,EAAuDoC,QAAvD,EAAiE;AAC7D,SAAO,MAAM;AACT;AACA;AACAA,IAAAA,QAAQ,CAACC,GAAT,CAAanG,qBAAb,EAAoCoG,WAApC,CAAgDC,IAAhD,CAAqD,MAAM;AACvD,YAAMC,GAAG,GAAG7G,OAAO,EAAnB;AACA,YAAM8G,MAAM,GAAGzC,QAAQ,CAAC0C,gBAAT,CAA2B,wBAAuBP,YAAa,IAA/D,CAAf;;AACA,WAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACpCH,QAAAA,GAAG,CAACjD,MAAJ,CAAWkD,MAAM,CAACE,CAAD,CAAjB;AACH;AACJ,KAND;AAOH,GAVD;AAWH;;AACD,MAAME,2BAA2B,GAAG,CAChC;AACIC,EAAAA,OAAO,EAAE3G,eADb;AAEI4G,EAAAA,UAAU,EAAEb,qBAFhB;AAGIc,EAAAA,IAAI,EAAE,CAACf,aAAD,EAAgBrG,QAAhB,EAA0BQ,QAA1B,CAHV;AAII6G,EAAAA,KAAK,EAAE;AAJX,CADgC,CAApC;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAN,CAA4B;AACb,SAAJC,IAAI,GAAG;AACV9G,IAAAA,oBAAoB,CAAC,IAAI6G,qBAAJ,EAAD,CAApB;AACH;;AACDE,EAAAA,WAAW,CAACC,QAAD,EAAW;AAClB/G,IAAAA,OAAO,CAAC,uBAAD,CAAP,GAAmC,CAACgH,IAAD,EAAOC,eAAe,GAAG,IAAzB,KAAkC;AACjE,YAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAT,CAA+BH,IAA/B,EAAqCC,eAArC,CAApB;;AACA,UAAIC,WAAW,IAAI,IAAnB,EAAyB;AACrB,cAAM,IAAIE,KAAJ,CAAU,yCAAV,CAAN;AACH;;AACD,aAAOF,WAAP;AACH,KAND;;AAOAlH,IAAAA,OAAO,CAAC,4BAAD,CAAP,GAAwC,MAAM+G,QAAQ,CAACM,mBAAT,EAA9C;;AACArH,IAAAA,OAAO,CAAC,2BAAD,CAAP,GAAuC,MAAM+G,QAAQ,CAACO,kBAAT,EAA7C;;AACA,UAAMC,aAAa,GAAG,CAACC;AAAS;AAAV,SAAgC;AAClD,YAAMC,aAAa,GAAGzH,OAAO,CAAC,4BAAD,CAAP,EAAtB;AACA,UAAI0H,KAAK,GAAGD,aAAa,CAACnB,MAA1B;AACA,UAAIqB,OAAO,GAAG,KAAd;;AACA,YAAMC,SAAS,GAAG,UAAUC;AAAS;AAAnB,QAAsC;AACpDF,QAAAA,OAAO,GAAGA,OAAO,IAAIE,QAArB;AACAH,QAAAA,KAAK;;AACL,YAAIA,KAAK,IAAI,CAAb,EAAgB;AACZF,UAAAA,QAAQ,CAACG,OAAD,CAAR;AACH;AACJ,OAND;;AAOAF,MAAAA,aAAa,CAACK,OAAd,CAAsB,UAAUZ;AAAY;AAAtB,QAAyC;AAC3DA,QAAAA,WAAW,CAACa,UAAZ,CAAuBH,SAAvB;AACH,OAFD;AAGH,KAdD;;AAeA,QAAI,CAAC5H,OAAO,CAAC,sBAAD,CAAZ,EAAsC;AAClCA,MAAAA,OAAO,CAAC,sBAAD,CAAP,GAAkC,EAAlC;AACH;;AACDA,IAAAA,OAAO,CAAC,sBAAD,CAAP,CAAgCgI,IAAhC,CAAqCT,aAArC;AACH;;AACDJ,EAAAA,qBAAqB,CAACJ,QAAD,EAAWC,IAAX,EAAiBC,eAAjB,EAAkC;AACnD,QAAID,IAAI,IAAI,IAAZ,EAAkB;AACd,aAAO,IAAP;AACH;;AACD,UAAMiB,CAAC,GAAGlB,QAAQ,CAACmB,cAAT,CAAwBlB,IAAxB,CAAV;;AACA,QAAIiB,CAAC,IAAI,IAAT,EAAe;AACX,aAAOA,CAAP;AACH,KAFD,MAGK,IAAI,CAAChB,eAAL,EAAsB;AACvB,aAAO,IAAP;AACH;;AACD,QAAI5H,OAAO,GAAG4E,YAAV,CAAuB+C,IAAvB,CAAJ,EAAkC;AAC9B,aAAO,KAAKG,qBAAL,CAA2BJ,QAA3B,EAAqCC,IAAI,CAACmB,IAA1C,EAAgD,IAAhD,CAAP;AACH;;AACD,WAAO,KAAKhB,qBAAL,CAA2BJ,QAA3B,EAAqCC,IAAI,CAACoB,aAA1C,EAAyD,IAAzD,CAAP;AACH;;AAjDuB;AAoD5B;AACA;AACA;;;AACA,MAAMC,UAAN,CAAiB;AACbC,EAAAA,KAAK,GAAG;AACJ,WAAO,IAAIC,cAAJ,EAAP;AACH;;AAHY;;AAKjBF,UAAU,CAACG,IAAX;AAAA,mBAAuGH,UAAvG;AAAA;;AACAA,UAAU,CAACI,KAAX,kBAD6F/I,EAC7F;AAAA,SAA2G2I,UAA3G;AAAA,WAA2GA,UAA3G;AAAA;;AACA;AAAA,qDAF6F3I,EAE7F,mBAA2F2I,UAA3F,EAAmH,CAAC;AACxGK,IAAAA,IAAI,EAAEzI;AADkG,GAAD,CAAnH;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0I,qBAAqB,GAAG,IAAIhJ,cAAJ,CAAmB,qBAAnB,CAA9B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMiJ,YAAN,CAAmB;AACf;AACJ;AACA;AACIvG,EAAAA,WAAW,CAACwG,OAAD,EAAUC,KAAV,EAAiB;AACxB,SAAKA,KAAL,GAAaA,KAAb;AACA,SAAKC,kBAAL,GAA0B,IAAIC,GAAJ,EAA1B;AACAH,IAAAA,OAAO,CAACf,OAAR,CAAgBmB,CAAC,IAAIA,CAAC,CAACC,OAAF,GAAY,IAAjC;AACA,SAAKC,QAAL,GAAgBN,OAAO,CAACO,KAAR,GAAgBC,OAAhB,EAAhB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIvG,EAAAA,gBAAgB,CAACwG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;AAC1C,UAAMC,MAAM,GAAG,KAAKC,cAAL,CAAoBH,SAApB,CAAf;;AACA,WAAOE,MAAM,CAAC3G,gBAAP,CAAwBwG,OAAxB,EAAiCC,SAAjC,EAA4CC,OAA5C,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIG,EAAAA,sBAAsB,CAACvF,MAAD,EAASmF,SAAT,EAAoBC,OAApB,EAA6B;AAC/C,UAAMC,MAAM,GAAG,KAAKC,cAAL,CAAoBH,SAApB,CAAf;;AACA,WAAOE,MAAM,CAACE,sBAAP,CAA8BvF,MAA9B,EAAsCmF,SAAtC,EAAiDC,OAAjD,CAAP;AACH;AACD;AACJ;AACA;;;AACII,EAAAA,OAAO,GAAG;AACN,WAAO,KAAKd,KAAZ;AACH;AACD;;;AACAY,EAAAA,cAAc,CAACH,SAAD,EAAY;AACtB,UAAME,MAAM,GAAG,KAAKV,kBAAL,CAAwBhD,GAAxB,CAA4BwD,SAA5B,CAAf;;AACA,QAAIE,MAAJ,EAAY;AACR,aAAOA,MAAP;AACH;;AACD,UAAMZ,OAAO,GAAG,KAAKM,QAArB;;AACA,SAAK,IAAI9C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwC,OAAO,CAACvC,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,YAAMoD,MAAM,GAAGZ,OAAO,CAACxC,CAAD,CAAtB;;AACA,UAAIoD,MAAM,CAACI,QAAP,CAAgBN,SAAhB,CAAJ,EAAgC;AAC5B,aAAKR,kBAAL,CAAwBe,GAAxB,CAA4BP,SAA5B,EAAuCE,MAAvC;;AACA,eAAOA,MAAP;AACH;AACJ;;AACD,UAAM,IAAIrC,KAAJ,CAAW,2CAA0CmC,SAAU,EAA/D,CAAN;AACH;;AA1Dc;;AA4DnBX,YAAY,CAACJ,IAAb;AAAA,mBAAyGI,YAAzG,EArF6FlJ,EAqF7F,UAAuIiJ,qBAAvI,GArF6FjJ,EAqF7F,UAAyKA,EAAE,CAACmB,MAA5K;AAAA;;AACA+H,YAAY,CAACH,KAAb,kBAtF6F/I,EAsF7F;AAAA,SAA6GkJ,YAA7G;AAAA,WAA6GA,YAA7G;AAAA;;AACA;AAAA,qDAvF6FlJ,EAuF7F,mBAA2FkJ,YAA3F,EAAqH,CAAC;AAC1GF,IAAAA,IAAI,EAAEzI;AADoG,GAAD,CAArH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEyI,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DtB,QAAAA,IAAI,EAAExI,MADwD;AAE9D+J,QAAAA,IAAI,EAAE,CAACtB,qBAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAED,MAAAA,IAAI,EAAEhJ,EAAE,CAACmB;AAAX,KAH2B,CAAP;AAGG,GAL7C;AAAA;;AAMA,MAAMqJ,kBAAN,CAAyB;AACrB7H,EAAAA,WAAW,CAAC8H,IAAD,EAAO;AACd,SAAKA,IAAL,GAAYA,IAAZ;AACH;;AACDR,EAAAA,sBAAsB,CAACL,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;AAChD,UAAMpF,MAAM,GAAG/E,OAAO,GAAG8E,oBAAV,CAA+B,KAAKgG,IAApC,EAA0Cb,OAA1C,CAAf;;AACA,QAAI,CAAClF,MAAL,EAAa;AACT,YAAM,IAAIgD,KAAJ,CAAW,4BAA2BhD,MAAO,cAAamF,SAAU,EAApE,CAAN;AACH;;AACD,WAAO,KAAKzG,gBAAL,CAAsBsB,MAAtB,EAA8BmF,SAA9B,EAAyCC,OAAzC,CAAP;AACH;;AAVoB;AAazB;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMY,gBAAN,CAAuB;AACnB/H,EAAAA,WAAW,GAAG;AACV;AACA,SAAKgI,UAAL,GAAkB,IAAIC,GAAJ,EAAlB;AACH;;AACDC,EAAAA,SAAS,CAACpE,MAAD,EAAS;AACd,UAAMqE,SAAS,GAAG,IAAIF,GAAJ,EAAlB;AACAnE,IAAAA,MAAM,CAAC2B,OAAP,CAAe2C,KAAK,IAAI;AACpB,UAAI,CAAC,KAAKJ,UAAL,CAAgBK,GAAhB,CAAoBD,KAApB,CAAL,EAAiC;AAC7B,aAAKJ,UAAL,CAAgBM,GAAhB,CAAoBF,KAApB;;AACAD,QAAAA,SAAS,CAACG,GAAV,CAAcF,KAAd;AACH;AACJ,KALD;AAMA,SAAKG,aAAL,CAAmBJ,SAAnB;AACH;;AACDI,EAAAA,aAAa,CAACJ,SAAD,EAAY,CAAG;;AAC5BK,EAAAA,YAAY,GAAG;AACX,WAAOC,KAAK,CAACC,IAAN,CAAW,KAAKV,UAAhB,CAAP;AACH;;AAlBkB;;AAoBvBD,gBAAgB,CAAC5B,IAAjB;AAAA,mBAA6G4B,gBAA7G;AAAA;;AACAA,gBAAgB,CAAC3B,KAAjB,kBAtI6F/I,EAsI7F;AAAA,SAAiH0K,gBAAjH;AAAA,WAAiHA,gBAAjH;AAAA;;AACA;AAAA,qDAvI6F1K,EAuI7F,mBAA2F0K,gBAA3F,EAAyH,CAAC;AAC9G1B,IAAAA,IAAI,EAAEzI;AADwG,GAAD,CAAzH;AAAA;;AAGA,MAAM+K,mBAAN,SAAkCZ,gBAAlC,CAAmD;AAC/C/H,EAAAA,WAAW,CAAC8H,IAAD,EAAO;AACd;AACA,SAAKA,IAAL,GAAYA,IAAZ,CAFc,CAGd;;AACA,SAAKc,UAAL,GAAkB,IAAIjC,GAAJ,EAAlB;;AACA,SAAKiC,UAAL,CAAgBnB,GAAhB,CAAoBK,IAAI,CAACe,IAAzB,EAA+B,EAA/B;AACH;;AACDC,EAAAA,gBAAgB,CAAChF,MAAD,EAASgC,IAAT,EAAeiD,UAAf,EAA2B;AACvCjF,IAAAA,MAAM,CAAC2B,OAAP,CAAgB2C,KAAD,IAAW;AACtB,YAAMY,OAAO,GAAG,KAAKlB,IAAL,CAAU9G,aAAV,CAAwB,OAAxB,CAAhB;;AACAgI,MAAAA,OAAO,CAACC,WAAR,GAAsBb,KAAtB;AACAW,MAAAA,UAAU,CAACpD,IAAX,CAAgBG,IAAI,CAACoD,WAAL,CAAiBF,OAAjB,CAAhB;AACH,KAJD;AAKH;;AACDG,EAAAA,OAAO,CAACC,QAAD,EAAW;AACd,UAAML,UAAU,GAAG,EAAnB;;AACA,SAAKD,gBAAL,CAAsB,KAAKd,UAA3B,EAAuCoB,QAAvC,EAAiDL,UAAjD;;AACA,SAAKH,UAAL,CAAgBnB,GAAhB,CAAoB2B,QAApB,EAA8BL,UAA9B;AACH;;AACDM,EAAAA,UAAU,CAACD,QAAD,EAAW;AACjB,UAAML,UAAU,GAAG,KAAKH,UAAL,CAAgBlF,GAAhB,CAAoB0F,QAApB,CAAnB;;AACA,QAAIL,UAAJ,EAAgB;AACZA,MAAAA,UAAU,CAACtD,OAAX,CAAmB6D,WAAnB;AACH;;AACD,SAAKV,UAAL,CAAgBW,MAAhB,CAAuBH,QAAvB;AACH;;AACDb,EAAAA,aAAa,CAACJ,SAAD,EAAY;AACrB,SAAKS,UAAL,CAAgBnD,OAAhB,CAAwB,CAACsD,UAAD,EAAaK,QAAb,KAA0B;AAC9C,WAAKN,gBAAL,CAAsBX,SAAtB,EAAiCiB,QAAjC,EAA2CL,UAA3C;AACH,KAFD;AAGH;;AACDS,EAAAA,WAAW,GAAG;AACV,SAAKZ,UAAL,CAAgBnD,OAAhB,CAAwBsD,UAAU,IAAIA,UAAU,CAACtD,OAAX,CAAmB6D,WAAnB,CAAtC;AACH;;AAlC8C;;AAoCnDX,mBAAmB,CAACxC,IAApB;AAAA,mBAAgHwC,mBAAhH,EA9K6FtL,EA8K7F,UAAqJJ,QAArJ;AAAA;;AACA0L,mBAAmB,CAACvC,KAApB,kBA/K6F/I,EA+K7F;AAAA,SAAoHsL,mBAApH;AAAA,WAAoHA,mBAApH;AAAA;;AACA;AAAA,qDAhL6FtL,EAgL7F,mBAA2FsL,mBAA3F,EAA4H,CAAC;AACjHtC,IAAAA,IAAI,EAAEzI;AAD2G,GAAD,CAA5H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEyI,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DtB,QAAAA,IAAI,EAAExI,MADwD;AAE9D+J,QAAAA,IAAI,EAAE,CAAC3K,QAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GALxB;AAAA;;AAMA,SAASqM,WAAT,CAAqBG,SAArB,EAAgC;AAC5BzM,EAAAA,OAAO,GAAG4D,MAAV,CAAiB6I,SAAjB;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,cAAc,GAAG;AACnB,SAAO,4BADY;AAEnB,WAAS,8BAFU;AAGnB,WAAS,8BAHU;AAInB,SAAO,sCAJY;AAKnB,WAAS;AALU,CAAvB;AAOA,MAAMC,eAAe,GAAG,SAAxB;AACA,MAAMC,WAAW,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAAoC,CAAC,CAACA,SAA1D;AACA,MAAMC,kBAAkB,GAAG,QAA3B;AACA,MAAMC,SAAS,GAAI,WAAUD,kBAAmB,EAAhD;AACA,MAAME,YAAY,GAAI,cAAaF,kBAAmB,EAAtD;;AACA,SAASG,oBAAT,CAA8BC,gBAA9B,EAAgD;AAC5C,SAAOF,YAAY,CAACG,OAAb,CAAqBR,eAArB,EAAsCO,gBAAtC,CAAP;AACH;;AACD,SAASE,iBAAT,CAA2BF,gBAA3B,EAA6C;AACzC,SAAOH,SAAS,CAACI,OAAV,CAAkBR,eAAlB,EAAmCO,gBAAnC,CAAP;AACH;;AACD,SAASG,aAAT,CAAuBC,MAAvB,EAA+BxG,MAA/B,EAAuC/B,MAAvC,EAA+C;AAC3C,OAAK,IAAIiC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACpC,QAAIoE,KAAK,GAAGtE,MAAM,CAACE,CAAD,CAAlB;;AACA,QAAIyE,KAAK,CAAC8B,OAAN,CAAcnC,KAAd,CAAJ,EAA0B;AACtBiC,MAAAA,aAAa,CAACC,MAAD,EAASlC,KAAT,EAAgBrG,MAAhB,CAAb;AACH,KAFD,MAGK;AACDqG,MAAAA,KAAK,GAAGA,KAAK,CAAC+B,OAAN,CAAcR,eAAd,EAA+BW,MAA/B,CAAR;AACAvI,MAAAA,MAAM,CAAC4D,IAAP,CAAYyC,KAAZ;AACH;AACJ;;AACD,SAAOrG,MAAP;AACH;;AACD,SAASyI,sBAAT,CAAgCC,YAAhC,EAA8C;AAC1C;AACA;AACA;AACA;AACA,SAAQC,KAAD,IAAW;AACd;AACA;AACA;AACA;AACA,QAAIA,KAAK,KAAK,cAAd,EAA8B;AAC1B,aAAOD,YAAP;AACH;;AACD,UAAME,oBAAoB,GAAGF,YAAY,CAACC,KAAD,CAAzC;;AACA,QAAIC,oBAAoB,KAAK,KAA7B,EAAoC;AAChC;AACAD,MAAAA,KAAK,CAACE,cAAN;AACAF,MAAAA,KAAK,CAACG,WAAN,GAAoB,KAApB;AACH;;AACD,WAAOnD,SAAP;AACH,GAfD;AAgBH;;AACD,IAAIoD,mCAAmC,GAAG,KAA1C;;AACA,MAAMC,mBAAN,CAA0B;AACtB/K,EAAAA,WAAW,CAACgL,YAAD,EAAeC,gBAAf,EAAiCC,KAAjC,EAAwC;AAC/C,SAAKF,YAAL,GAAoBA,YAApB;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,gBAAL,GAAwB,IAAIxE,GAAJ,EAAxB;AACA,SAAKyE,eAAL,GAAuB,IAAIC,mBAAJ,CAAwBL,YAAxB,CAAvB;AACH;;AACDM,EAAAA,cAAc,CAACrE,OAAD,EAAUZ,IAAV,EAAgB;AAC1B,QAAI,CAACY,OAAD,IAAY,CAACZ,IAAjB,EAAuB;AACnB,aAAO,KAAK+E,eAAZ;AACH;;AACD,YAAQ/E,IAAI,CAACkF,aAAb;AACI,WAAKzN,iBAAiB,CAAC0N,QAAvB;AAAiC;AAC7B,cAAIC,QAAQ,GAAG,KAAKN,gBAAL,CAAsBzH,GAAtB,CAA0B2C,IAAI,CAACqF,EAA/B,CAAf;;AACA,cAAI,CAACD,QAAL,EAAe;AACXA,YAAAA,QAAQ,GAAG,IAAIE,iCAAJ,CAAsC,KAAKX,YAA3C,EAAyD,KAAKC,gBAA9D,EAAgF5E,IAAhF,EAAsF,KAAK6E,KAA3F,CAAX;AACA,iBAAKC,gBAAL,CAAsB1D,GAAtB,CAA0BpB,IAAI,CAACqF,EAA/B,EAAmCD,QAAnC;AACH;;AACDA,UAAAA,QAAQ,CAACG,WAAT,CAAqB3E,OAArB;AACA,iBAAOwE,QAAP;AACH;AACD;AACA;;AACA,WAAK,CAAL;AACA,WAAK3N,iBAAiB,CAAC+N,SAAvB;AACI;AACA,YAAI,CAAC,OAAOhC,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KACA;AACA;AACA;AACA,SAACiB,mCAJD,IAIwCzE,IAAI,CAACkF,aAAL,KAAuB,CAJnE,EAIsE;AAClET,UAAAA,mCAAmC,GAAG,IAAtC;AACAgB,UAAAA,OAAO,CAACC,IAAR,CAAa,oIAAb;AACH;;AACD,eAAO,IAAIC,iBAAJ,CAAsB,KAAKhB,YAA3B,EAAyC,KAAKC,gBAA9C,EAAgEhE,OAAhE,EAAyEZ,IAAzE,CAAP;;AACJ;AAAS;AACL,cAAI,CAAC,KAAK8E,gBAAL,CAAsB9C,GAAtB,CAA0BhC,IAAI,CAACqF,EAA/B,CAAL,EAAyC;AACrC,kBAAM5H,MAAM,GAAGuG,aAAa,CAAChE,IAAI,CAACqF,EAAN,EAAUrF,IAAI,CAACvC,MAAf,EAAuB,EAAvB,CAA5B;AACA,iBAAKmH,gBAAL,CAAsB/C,SAAtB,CAAgCpE,MAAhC;AACA,iBAAKqH,gBAAL,CAAsB1D,GAAtB,CAA0BpB,IAAI,CAACqF,EAA/B,EAAmC,KAAKN,eAAxC;AACH;;AACD,iBAAO,KAAKA,eAAZ;AACH;AA/BL;AAiCH;;AACDa,EAAAA,KAAK,GAAG,CAAG;;AACXC,EAAAA,GAAG,GAAG,CAAG;;AA/Ca;;AAiD1BnB,mBAAmB,CAAC5E,IAApB;AAAA,mBAAgH4E,mBAAhH,EAxS6F1N,EAwS7F,UAAqJkJ,YAArJ,GAxS6FlJ,EAwS7F,UAA8KsL,mBAA9K,GAxS6FtL,EAwS7F,UAA8MU,MAA9M;AAAA;;AACAgN,mBAAmB,CAAC3E,KAApB,kBAzS6F/I,EAyS7F;AAAA,SAAoH0N,mBAApH;AAAA,WAAoHA,mBAApH;AAAA;;AACA;AAAA,qDA1S6F1N,EA0S7F,mBAA2F0N,mBAA3F,EAA4H,CAAC;AACjH1E,IAAAA,IAAI,EAAEzI;AAD2G,GAAD,CAA5H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEyI,MAAAA,IAAI,EAAEE;AAAR,KAAD,EAAyB;AAAEF,MAAAA,IAAI,EAAEsC;AAAR,KAAzB,EAAwD;AAAEtC,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACrHtB,QAAAA,IAAI,EAAExI,MAD+G;AAErH+J,QAAAA,IAAI,EAAE,CAAC7J,MAAD;AAF+G,OAAD;AAA/B,KAAxD,CAAP;AAGlB,GALxB;AAAA;;AAMA,MAAMsN,mBAAN,CAA0B;AACtBrL,EAAAA,WAAW,CAACgL,YAAD,EAAe;AACtB,SAAKA,YAAL,GAAoBA,YAApB;AACA,SAAKmB,IAAL,GAAYC,MAAM,CAACC,MAAP,CAAc,IAAd,CAAZ;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACH;;AACDC,EAAAA,OAAO,GAAG,CAAG;;AACbvL,EAAAA,aAAa,CAAC4B,IAAD,EAAO4J,SAAP,EAAkB;AAC3B,QAAIA,SAAJ,EAAe;AACX;AACA;AACA,aAAOnL,QAAQ,CAACoL,eAAT,CAAyB/C,cAAc,CAAC8C,SAAD,CAAd,IAA6BA,SAAtD,EAAiE5J,IAAjE,CAAP;AACH;;AACD,WAAOvB,QAAQ,CAACL,aAAT,CAAuB4B,IAAvB,CAAP;AACH;;AACD8J,EAAAA,aAAa,CAACC,KAAD,EAAQ;AACjB,WAAOtL,QAAQ,CAACqL,aAAT,CAAuBC,KAAvB,CAAP;AACH;;AACDC,EAAAA,UAAU,CAACD,KAAD,EAAQ;AACd,WAAOtL,QAAQ,CAACwL,cAAT,CAAwBF,KAAxB,CAAP;AACH;;AACDzD,EAAAA,WAAW,CAAC4D,MAAD,EAASC,QAAT,EAAmB;AAC1BD,IAAAA,MAAM,CAAC5D,WAAP,CAAmB6D,QAAnB;AACH;;AACDC,EAAAA,YAAY,CAACF,MAAD,EAASC,QAAT,EAAmBE,QAAnB,EAA6B;AACrC,QAAIH,MAAJ,EAAY;AACRA,MAAAA,MAAM,CAACE,YAAP,CAAoBD,QAApB,EAA8BE,QAA9B;AACH;AACJ;;AACDlM,EAAAA,WAAW,CAAC+L,MAAD,EAASI,QAAT,EAAmB;AAC1B,QAAIJ,MAAJ,EAAY;AACRA,MAAAA,MAAM,CAAC/L,WAAP,CAAmBmM,QAAnB;AACH;AACJ;;AACDC,EAAAA,iBAAiB,CAACC,cAAD,EAAiBC,eAAjB,EAAkC;AAC/C,QAAI/M,EAAE,GAAG,OAAO8M,cAAP,KAA0B,QAA1B,GAAqC/L,QAAQ,CAACyB,aAAT,CAAuBsK,cAAvB,CAArC,GACLA,cADJ;;AAEA,QAAI,CAAC9M,EAAL,EAAS;AACL,YAAM,IAAIyE,KAAJ,CAAW,iBAAgBqI,cAAe,8BAA1C,CAAN;AACH;;AACD,QAAI,CAACC,eAAL,EAAsB;AAClB/M,MAAAA,EAAE,CAAC2I,WAAH,GAAiB,EAAjB;AACH;;AACD,WAAO3I,EAAP;AACH;;AACDQ,EAAAA,UAAU,CAACD,IAAD,EAAO;AACb,WAAOA,IAAI,CAACC,UAAZ;AACH;;AACDwM,EAAAA,WAAW,CAACzM,IAAD,EAAO;AACd,WAAOA,IAAI,CAACyM,WAAZ;AACH;;AACDpK,EAAAA,YAAY,CAAC5C,EAAD,EAAKsC,IAAL,EAAW+J,KAAX,EAAkBH,SAAlB,EAA6B;AACrC,QAAIA,SAAJ,EAAe;AACX5J,MAAAA,IAAI,GAAG4J,SAAS,GAAG,GAAZ,GAAkB5J,IAAzB,CADW,CAEX;AACA;;AACA,YAAM2K,YAAY,GAAG7D,cAAc,CAAC8C,SAAD,CAAnC;;AACA,UAAIe,YAAJ,EAAkB;AACdjN,QAAAA,EAAE,CAACkN,cAAH,CAAkBD,YAAlB,EAAgC3K,IAAhC,EAAsC+J,KAAtC;AACH,OAFD,MAGK;AACDrM,QAAAA,EAAE,CAAC4C,YAAH,CAAgBN,IAAhB,EAAsB+J,KAAtB;AACH;AACJ,KAXD,MAYK;AACDrM,MAAAA,EAAE,CAAC4C,YAAH,CAAgBN,IAAhB,EAAsB+J,KAAtB;AACH;AACJ;;AACDc,EAAAA,eAAe,CAACnN,EAAD,EAAKsC,IAAL,EAAW4J,SAAX,EAAsB;AACjC,QAAIA,SAAJ,EAAe;AACX;AACA;AACA,YAAMe,YAAY,GAAG7D,cAAc,CAAC8C,SAAD,CAAnC;;AACA,UAAIe,YAAJ,EAAkB;AACdjN,QAAAA,EAAE,CAACoN,iBAAH,CAAqBH,YAArB,EAAmC3K,IAAnC;AACH,OAFD,MAGK;AACD;AACA;AACA;AACAtC,QAAAA,EAAE,CAACmN,eAAH,CAAoB,GAAEjB,SAAU,IAAG5J,IAAK,EAAxC;AACH;AACJ,KAbD,MAcK;AACDtC,MAAAA,EAAE,CAACmN,eAAH,CAAmB7K,IAAnB;AACH;AACJ;;AACD+K,EAAAA,QAAQ,CAACrN,EAAD,EAAKsC,IAAL,EAAW;AACftC,IAAAA,EAAE,CAACsN,SAAH,CAAatF,GAAb,CAAiB1F,IAAjB;AACH;;AACDiL,EAAAA,WAAW,CAACvN,EAAD,EAAKsC,IAAL,EAAW;AAClBtC,IAAAA,EAAE,CAACsN,SAAH,CAAahN,MAAb,CAAoBgC,IAApB;AACH;;AACDkL,EAAAA,QAAQ,CAACxN,EAAD,EAAK8H,KAAL,EAAYuE,KAAZ,EAAmBoB,KAAnB,EAA0B;AAC9B,QAAIA,KAAK,IAAI/P,mBAAmB,CAACgQ,QAApB,GAA+BhQ,mBAAmB,CAACiQ,SAAvD,CAAT,EAA4E;AACxE3N,MAAAA,EAAE,CAAC8H,KAAH,CAAS8F,WAAT,CAAqB9F,KAArB,EAA4BuE,KAA5B,EAAmCoB,KAAK,GAAG/P,mBAAmB,CAACiQ,SAA5B,GAAwC,WAAxC,GAAsD,EAAzF;AACH,KAFD,MAGK;AACD3N,MAAAA,EAAE,CAAC8H,KAAH,CAASA,KAAT,IAAkBuE,KAAlB;AACH;AACJ;;AACDrD,EAAAA,WAAW,CAAChJ,EAAD,EAAK8H,KAAL,EAAY2F,KAAZ,EAAmB;AAC1B,QAAIA,KAAK,GAAG/P,mBAAmB,CAACgQ,QAAhC,EAA0C;AACtC1N,MAAAA,EAAE,CAAC8H,KAAH,CAAS+F,cAAT,CAAwB/F,KAAxB;AACH,KAFD,MAGK;AACD;AACA;AACA9H,MAAAA,EAAE,CAAC8H,KAAH,CAASA,KAAT,IAAkB,EAAlB;AACH;AACJ;;AACD8F,EAAAA,WAAW,CAAC5N,EAAD,EAAKsC,IAAL,EAAW+J,KAAX,EAAkB;AACzB/C,IAAAA,WAAW,IAAIwE,oBAAoB,CAACxL,IAAD,EAAO,UAAP,CAAnC;AACAtC,IAAAA,EAAE,CAACsC,IAAD,CAAF,GAAW+J,KAAX;AACH;;AACD0B,EAAAA,QAAQ,CAACxN,IAAD,EAAO8L,KAAP,EAAc;AAClB9L,IAAAA,IAAI,CAACyN,SAAL,GAAiB3B,KAAjB;AACH;;AACD4B,EAAAA,MAAM,CAACxM,MAAD,EAAS2I,KAAT,EAAgBvF,QAAhB,EAA0B;AAC5ByE,IAAAA,WAAW,IAAIwE,oBAAoB,CAAC1D,KAAD,EAAQ,UAAR,CAAnC;;AACA,QAAI,OAAO3I,MAAP,KAAkB,QAAtB,EAAgC;AAC5B,aAAO,KAAKiJ,YAAL,CAAkB1D,sBAAlB,CAAyCvF,MAAzC,EAAiD2I,KAAjD,EAAwDF,sBAAsB,CAACrF,QAAD,CAA9E,CAAP;AACH;;AACD,WAAO,KAAK6F,YAAL,CAAkBvK,gBAAlB,CAAmCsB,MAAnC,EAA2C2I,KAA3C,EAAkDF,sBAAsB,CAACrF,QAAD,CAAxE,CAAP;AACH;;AA5HqB;;AA8H1B,MAAMqJ,WAAW,GAAG,CAAC,MAAM,IAAIC,UAAJ,CAAe,CAAf,CAAP,GAApB;;AACA,SAASL,oBAAT,CAA8BxL,IAA9B,EAAoC8L,QAApC,EAA8C;AAC1C,MAAI9L,IAAI,CAAC6L,UAAL,CAAgB,CAAhB,MAAuBD,WAA3B,EAAwC;AACpC,UAAM,IAAIzJ,KAAJ,CAAW,wBAAuB2J,QAAS,IAAG9L,IAAK;AACjE;AACA,qEAAqEA,IAAK,gIAF5D,CAAN;AAGH;AACJ;;AACD,MAAM+I,iCAAN,SAAgDN,mBAAhD,CAAoE;AAChErL,EAAAA,WAAW,CAACgL,YAAD,EAAeC,gBAAf,EAAiC0D,SAAjC,EAA4CzD,KAA5C,EAAmD;AAC1D,UAAMF,YAAN;AACA,SAAK2D,SAAL,GAAiBA,SAAjB;AACA,UAAM7K,MAAM,GAAGuG,aAAa,CAACa,KAAK,GAAG,GAAR,GAAcyD,SAAS,CAACjD,EAAzB,EAA6BiD,SAAS,CAAC7K,MAAvC,EAA+C,EAA/C,CAA5B;AACAmH,IAAAA,gBAAgB,CAAC/C,SAAjB,CAA2BpE,MAA3B;AACA,SAAK8K,WAAL,GAAmB3E,oBAAoB,CAACiB,KAAK,GAAG,GAAR,GAAcyD,SAAS,CAACjD,EAAzB,CAAvC;AACA,SAAKmD,QAAL,GAAgBzE,iBAAiB,CAACc,KAAK,GAAG,GAAR,GAAcyD,SAAS,CAACjD,EAAzB,CAAjC;AACH;;AACDE,EAAAA,WAAW,CAAC3E,OAAD,EAAU;AACjB,UAAM/D,YAAN,CAAmB+D,OAAnB,EAA4B,KAAK4H,QAAjC,EAA2C,EAA3C;AACH;;AACD7N,EAAAA,aAAa,CAAC8L,MAAD,EAASlK,IAAT,EAAe;AACxB,UAAMtC,EAAE,GAAG,MAAMU,aAAN,CAAoB8L,MAApB,EAA4BlK,IAA5B,CAAX;AACA,UAAMM,YAAN,CAAmB5C,EAAnB,EAAuB,KAAKsO,WAA5B,EAAyC,EAAzC;AACA,WAAOtO,EAAP;AACH;;AAhB+D;;AAkBpE,MAAM0L,iBAAN,SAAgCX,mBAAhC,CAAoD;AAChDrL,EAAAA,WAAW,CAACgL,YAAD,EAAeC,gBAAf,EAAiC6D,MAAjC,EAAyCH,SAAzC,EAAoD;AAC3D,UAAM3D,YAAN;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAK6D,MAAL,GAAcA,MAAd;AACA,SAAKC,UAAL,GAAkBD,MAAM,CAACE,YAAP,CAAoB;AAAEC,MAAAA,IAAI,EAAE;AAAR,KAApB,CAAlB;AACA,SAAKhE,gBAAL,CAAsB9B,OAAtB,CAA8B,KAAK4F,UAAnC;AACA,UAAMjL,MAAM,GAAGuG,aAAa,CAACsE,SAAS,CAACjD,EAAX,EAAeiD,SAAS,CAAC7K,MAAzB,EAAiC,EAAjC,CAA5B;;AACA,SAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;AACpC,YAAMgF,OAAO,GAAG3H,QAAQ,CAACL,aAAT,CAAuB,OAAvB,CAAhB;AACAgI,MAAAA,OAAO,CAACC,WAAR,GAAsBnF,MAAM,CAACE,CAAD,CAA5B;AACA,WAAK+K,UAAL,CAAgB7F,WAAhB,CAA4BF,OAA5B;AACH;AACJ;;AACDkG,EAAAA,gBAAgB,CAACrO,IAAD,EAAO;AACnB,WAAOA,IAAI,KAAK,KAAKiO,MAAd,GAAuB,KAAKC,UAA5B,GAAyClO,IAAhD;AACH;;AACD0L,EAAAA,OAAO,GAAG;AACN,SAAKtB,gBAAL,CAAsB5B,UAAtB,CAAiC,KAAK0F,UAAtC;AACH;;AACD7F,EAAAA,WAAW,CAAC4D,MAAD,EAASC,QAAT,EAAmB;AAC1B,WAAO,MAAM7D,WAAN,CAAkB,KAAKgG,gBAAL,CAAsBpC,MAAtB,CAAlB,EAAiDC,QAAjD,CAAP;AACH;;AACDC,EAAAA,YAAY,CAACF,MAAD,EAASC,QAAT,EAAmBE,QAAnB,EAA6B;AACrC,WAAO,MAAMD,YAAN,CAAmB,KAAKkC,gBAAL,CAAsBpC,MAAtB,CAAnB,EAAkDC,QAAlD,EAA4DE,QAA5D,CAAP;AACH;;AACDlM,EAAAA,WAAW,CAAC+L,MAAD,EAASI,QAAT,EAAmB;AAC1B,WAAO,MAAMnM,WAAN,CAAkB,KAAKmO,gBAAL,CAAsBpC,MAAtB,CAAlB,EAAiDI,QAAjD,CAAP;AACH;;AACDpM,EAAAA,UAAU,CAACD,IAAD,EAAO;AACb,WAAO,KAAKqO,gBAAL,CAAsB,MAAMpO,UAAN,CAAiB,KAAKoO,gBAAL,CAAsBrO,IAAtB,CAAjB,CAAtB,CAAP;AACH;;AA/B+C;AAkCpD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMsO,eAAN,SAA8BtH,kBAA9B,CAAiD;AAC7C7H,EAAAA,WAAW,CAACkB,GAAD,EAAM;AACb,UAAMA,GAAN;AACH,GAH4C,CAI7C;AACA;;;AACAsG,EAAAA,QAAQ,CAACN,SAAD,EAAY;AAChB,WAAO,IAAP;AACH;;AACDzG,EAAAA,gBAAgB,CAACwG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;AAC1CF,IAAAA,OAAO,CAACxG,gBAAR,CAAyByG,SAAzB,EAAoCC,OAApC,EAA6C,KAA7C;AACA,WAAO,MAAM,KAAKzG,mBAAL,CAAyBuG,OAAzB,EAAkCC,SAAlC,EAA6CC,OAA7C,CAAb;AACH;;AACDzG,EAAAA,mBAAmB,CAACqB,MAAD,EAASmF,SAAT,EAAoB/B,QAApB,EAA8B;AAC7C,WAAOpD,MAAM,CAACrB,mBAAP,CAA2BwG,SAA3B,EAAsC/B,QAAtC,CAAP;AACH;;AAf4C;;AAiBjDgK,eAAe,CAAChJ,IAAhB;AAAA,mBAA4GgJ,eAA5G,EAlgB6F9R,EAkgB7F,UAA6IJ,QAA7I;AAAA;;AACAkS,eAAe,CAAC/I,KAAhB,kBAngB6F/I,EAmgB7F;AAAA,SAAgH8R,eAAhH;AAAA,WAAgHA,eAAhH;AAAA;;AACA;AAAA,qDApgB6F9R,EAogB7F,mBAA2F8R,eAA3F,EAAwH,CAAC;AAC7G9I,IAAAA,IAAI,EAAEzI;AADuG,GAAD,CAAxH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEyI,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DtB,QAAAA,IAAI,EAAExI,MADwD;AAE9D+J,QAAAA,IAAI,EAAE,CAAC3K,QAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GALxB;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMmS,aAAa,GAAG,CAAC,KAAD,EAAQ,SAAR,EAAmB,MAAnB,EAA2B,OAA3B,CAAtB;AACA,MAAMC,uBAAuB,GAAG,CAAhC,C,CACA;;AACA,MAAMC,OAAO,GAAG;AACZ;AACA;AACA,QAAM,WAHM;AAIZ,QAAM,KAJM;AAKZ,UAAQ,QALI;AAMZ,UAAQ,QANI;AAOZ,SAAO,QAPK;AAQZ,SAAO,QARK;AASZ,UAAQ,WATI;AAUZ,WAAS,YAVG;AAWZ,QAAM,SAXM;AAYZ,UAAQ,WAZI;AAaZ,UAAQ,aAbI;AAcZ,YAAU,YAdE;AAeZ,SAAO;AAfK,CAAhB,C,CAiBA;AACA;AACA;;AACA,MAAMC,mBAAmB,GAAG;AACxB,OAAK,GADmB;AAExB,OAAK,GAFmB;AAGxB,OAAK,GAHmB;AAIxB,OAAK,GAJmB;AAKxB,OAAK,GALmB;AAMxB,OAAK,GANmB;AAOxB,OAAK,GAPmB;AAQxB,OAAK,GARmB;AASxB,OAAK,GATmB;AAUxB,OAAK,GAVmB;AAWxB,OAAK,GAXmB;AAYxB,OAAK,GAZmB;AAaxB,OAAK,GAbmB;AAcxB,OAAK,GAdmB;AAexB,UAAQ,GAfgB;AAgBxB,UAAQ;AAhBgB,CAA5B;AAkBA;AACA;AACA;;AACA,MAAMC,oBAAoB,GAAG;AACzB,SAAQ9E,KAAD,IAAWA,KAAK,CAAC+E,MADC;AAEzB,aAAY/E,KAAD,IAAWA,KAAK,CAACgF,OAFH;AAGzB,UAAShF,KAAD,IAAWA,KAAK,CAACiF,OAHA;AAIzB,WAAUjF,KAAD,IAAWA,KAAK,CAACkF;AAJD,CAA7B;AAMA;AACA;AACA;AACA;;AACA,MAAMC,eAAN,SAA8BhI,kBAA9B,CAAiD;AAC7C;AACJ;AACA;AACA;AACI7H,EAAAA,WAAW,CAACkB,GAAD,EAAM;AACb,UAAMA,GAAN;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIsG,EAAAA,QAAQ,CAACN,SAAD,EAAY;AAChB,WAAO2I,eAAe,CAACC,cAAhB,CAA+B5I,SAA/B,KAA6C,IAApD;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIzG,EAAAA,gBAAgB,CAACwG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;AAC1C,UAAM4I,WAAW,GAAGF,eAAe,CAACC,cAAhB,CAA+B5I,SAA/B,CAApB;AACA,UAAM8I,cAAc,GAAGH,eAAe,CAACI,aAAhB,CAA8BF,WAAW,CAAC,SAAD,CAAzC,EAAsD5I,OAAtD,EAA+D,KAAKN,OAAL,CAAaU,OAAb,EAA/D,CAAvB;AACA,WAAO,KAAKV,OAAL,CAAaU,OAAb,GAAuB2I,iBAAvB,CAAyC,MAAM;AAClD,aAAOlT,OAAO,GAAGqD,WAAV,CAAsB4G,OAAtB,EAA+B8I,WAAW,CAAC,cAAD,CAA1C,EAA4DC,cAA5D,CAAP;AACH,KAFM,CAAP;AAGH;;AACoB,SAAdF,cAAc,CAAC5I,SAAD,EAAY;AAC7B,UAAMiJ,KAAK,GAAGjJ,SAAS,CAACkJ,WAAV,GAAwBC,KAAxB,CAA8B,GAA9B,CAAd;AACA,UAAMC,YAAY,GAAGH,KAAK,CAACI,KAAN,EAArB;;AACA,QAAKJ,KAAK,CAAClM,MAAN,KAAiB,CAAlB,IAAwB,EAAEqM,YAAY,KAAK,SAAjB,IAA8BA,YAAY,KAAK,OAAjD,CAA5B,EAAuF;AACnF,aAAO,IAAP;AACH;;AACD,UAAME,GAAG,GAAGX,eAAe,CAACY,aAAhB,CAA8BN,KAAK,CAACO,GAAN,EAA9B,CAAZ;;AACA,QAAIC,OAAO,GAAG,EAAd;AACAvB,IAAAA,aAAa,CAAC3J,OAAd,CAAsBmL,YAAY,IAAI;AAClC,YAAMC,KAAK,GAAGV,KAAK,CAACW,OAAN,CAAcF,YAAd,CAAd;;AACA,UAAIC,KAAK,GAAG,CAAC,CAAb,EAAgB;AACZV,QAAAA,KAAK,CAACY,MAAN,CAAaF,KAAb,EAAoB,CAApB;AACAF,QAAAA,OAAO,IAAIC,YAAY,GAAG,GAA1B;AACH;AACJ,KAND;AAOAD,IAAAA,OAAO,IAAIH,GAAX;;AACA,QAAIL,KAAK,CAAClM,MAAN,IAAgB,CAAhB,IAAqBuM,GAAG,CAACvM,MAAJ,KAAe,CAAxC,EAA2C;AACvC;AACA,aAAO,IAAP;AACH,KAnB4B,CAoB7B;AACA;AACA;;;AACA,UAAM+M,MAAM,GAAG,EAAf;AACAA,IAAAA,MAAM,CAAC,cAAD,CAAN,GAAyBV,YAAzB;AACAU,IAAAA,MAAM,CAAC,SAAD,CAAN,GAAoBL,OAApB;AACA,WAAOK,MAAP;AACH;;AACqB,SAAfC,eAAe,CAACvG,KAAD,EAAQ;AAC1B,QAAIiG,OAAO,GAAG,EAAd;AACA,QAAIH,GAAG,GAAGU,WAAW,CAACxG,KAAD,CAArB;AACA8F,IAAAA,GAAG,GAAGA,GAAG,CAACJ,WAAJ,EAAN;;AACA,QAAII,GAAG,KAAK,GAAZ,EAAiB;AACbA,MAAAA,GAAG,GAAG,OAAN,CADa,CACE;AAClB,KAFD,MAGK,IAAIA,GAAG,KAAK,GAAZ,EAAiB;AAClBA,MAAAA,GAAG,GAAG,KAAN,CADkB,CACL;AAChB;;AACDpB,IAAAA,aAAa,CAAC3J,OAAd,CAAsBmL,YAAY,IAAI;AAClC,UAAIA,YAAY,IAAIJ,GAApB,EAAyB;AACrB,cAAMW,cAAc,GAAG3B,oBAAoB,CAACoB,YAAD,CAA3C;;AACA,YAAIO,cAAc,CAACzG,KAAD,CAAlB,EAA2B;AACvBiG,UAAAA,OAAO,IAAIC,YAAY,GAAG,GAA1B;AACH;AACJ;AACJ,KAPD;AAQAD,IAAAA,OAAO,IAAIH,GAAX;AACA,WAAOG,OAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACwB,SAAbV,aAAa,CAACU,OAAD,EAAUxJ,OAAV,EAAmBiK,IAAnB,EAAyB;AACzC,WAAO,CAAC1G;AAAM;AAAP,SAA6B;AAChC,UAAImF,eAAe,CAACoB,eAAhB,CAAgCvG,KAAhC,MAA2CiG,OAA/C,EAAwD;AACpDS,QAAAA,IAAI,CAACC,UAAL,CAAgB,MAAMlK,OAAO,CAACuD,KAAD,CAA7B;AACH;AACJ,KAJD;AAKH;AACD;;;AACoB,SAAb+F,aAAa,CAACa,OAAD,EAAU;AAC1B;AACA,YAAQA,OAAR;AACI,WAAK,KAAL;AACI,eAAO,QAAP;;AACJ;AACI,eAAOA,OAAP;AAJR;AAMH;;AAvG4C;;AAyGjDzB,eAAe,CAAC1J,IAAhB;AAAA,mBAA4G0J,eAA5G,EAprB6FxS,EAorB7F,UAA6IJ,QAA7I;AAAA;;AACA4S,eAAe,CAACzJ,KAAhB,kBArrB6F/I,EAqrB7F;AAAA,SAAgHwS,eAAhH;AAAA,WAAgHA,eAAhH;AAAA;;AACA;AAAA,qDAtrB6FxS,EAsrB7F,mBAA2FwS,eAA3F,EAAwH,CAAC;AAC7GxJ,IAAAA,IAAI,EAAEzI;AADuG,GAAD,CAAxH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEyI,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DtB,QAAAA,IAAI,EAAExI,MADwD;AAE9D+J,QAAAA,IAAI,EAAE,CAAC3K,QAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GALxB;AAAA;;AAMA,SAASiU,WAAT,CAAqBxG,KAArB,EAA4B;AACxB,MAAI8F,GAAG,GAAG9F,KAAK,CAAC8F,GAAhB;;AACA,MAAIA,GAAG,IAAI,IAAX,EAAiB;AACbA,IAAAA,GAAG,GAAG9F,KAAK,CAAC6G,aAAZ,CADa,CAEb;AACA;AACA;;AACA,QAAIf,GAAG,IAAI,IAAX,EAAiB;AACb,aAAO,cAAP;AACH;;AACD,QAAIA,GAAG,CAACgB,UAAJ,CAAe,IAAf,CAAJ,EAA0B;AACtBhB,MAAAA,GAAG,GAAGiB,MAAM,CAACC,YAAP,CAAoBC,QAAQ,CAACnB,GAAG,CAACoB,SAAJ,CAAc,CAAd,CAAD,EAAmB,EAAnB,CAA5B,CAAN;;AACA,UAAIlH,KAAK,CAACmH,QAAN,KAAmBxC,uBAAnB,IAA8CE,mBAAmB,CAACuC,cAApB,CAAmCtB,GAAnC,CAAlD,EAA2F;AACvF;AACA;AACA;AACAA,QAAAA,GAAG,GAAGjB,mBAAmB,CAACiB,GAAD,CAAzB;AACH;AACJ;AACJ;;AACD,SAAOlB,OAAO,CAACkB,GAAD,CAAP,IAAgBA,GAAvB;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASuB,cAAT,GAA0B;AACtB5R,EAAAA,iBAAiB,CAACC,WAAlB;AACAmE,EAAAA,qBAAqB,CAACC,IAAtB;AACH;;AACD,SAASwN,YAAT,GAAwB;AACpB,SAAO,IAAI/T,YAAJ,EAAP;AACH;;AACD,SAASgU,SAAT,GAAqB;AACjB;AACA/T,EAAAA,YAAY,CAACmD,QAAD,CAAZ;AACA,SAAOA,QAAP;AACH;;AACD,MAAM6Q,mCAAmC,GAAG,CACxC;AAAE/N,EAAAA,OAAO,EAAEhG,WAAX;AAAwBgU,EAAAA,QAAQ,EAAEjV;AAAlC,CADwC,EAExC;AAAEiH,EAAAA,OAAO,EAAE/F,oBAAX;AAAiC+T,EAAAA,QAAQ,EAAEJ,cAA3C;AAA2DzN,EAAAA,KAAK,EAAE;AAAlE,CAFwC,EAGxC;AAAEH,EAAAA,OAAO,EAAElH,QAAX;AAAqBmH,EAAAA,UAAU,EAAE6N,SAAjC;AAA4C5N,EAAAA,IAAI,EAAE;AAAlD,CAHwC,CAA5C;AAKA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM+N,eAAe,GAAG/T,qBAAqB,CAACC,YAAD,EAAe,SAAf,EAA0B4T,mCAA1B,CAA7C;AACA,MAAMG,wBAAwB,GAAG,CAC7B;AAAElO,EAAAA,OAAO,EAAE5F,eAAX;AAA4B4T,EAAAA,QAAQ,EAAE;AAAtC,CAD6B,EAE7B;AAAEhO,EAAAA,OAAO,EAAElG,YAAX;AAAyBmG,EAAAA,UAAU,EAAE4N,YAArC;AAAmD3N,EAAAA,IAAI,EAAE;AAAzD,CAF6B,EAG7B;AACIF,EAAAA,OAAO,EAAEmC,qBADb;AAEIgM,EAAAA,QAAQ,EAAEnD,eAFd;AAGI7K,EAAAA,KAAK,EAAE,IAHX;AAIID,EAAAA,IAAI,EAAE,CAACpH,QAAD,EAAWuB,MAAX,EAAmBL,WAAnB;AAJV,CAH6B,EAS7B;AAAEgG,EAAAA,OAAO,EAAEmC,qBAAX;AAAkCgM,EAAAA,QAAQ,EAAEzC,eAA5C;AAA6DvL,EAAAA,KAAK,EAAE,IAApE;AAA0ED,EAAAA,IAAI,EAAE,CAACpH,QAAD;AAAhF,CAT6B,EAU7B;AACIkH,EAAAA,OAAO,EAAE4G,mBADb;AAEIuH,EAAAA,QAAQ,EAAEvH,mBAFd;AAGI1G,EAAAA,IAAI,EAAE,CAACkC,YAAD,EAAeoC,mBAAf,EAAoC5K,MAApC;AAHV,CAV6B,EAe7B;AAAEoG,EAAAA,OAAO,EAAE1F,gBAAX;AAA6B8T,EAAAA,WAAW,EAAExH;AAA1C,CAf6B,EAgB7B;AAAE5G,EAAAA,OAAO,EAAE4D,gBAAX;AAA6BwK,EAAAA,WAAW,EAAE5J;AAA1C,CAhB6B,EAiB7B;AAAExE,EAAAA,OAAO,EAAEwE,mBAAX;AAAgC2J,EAAAA,QAAQ,EAAE3J,mBAA1C;AAA+DtE,EAAAA,IAAI,EAAE,CAACpH,QAAD;AAArE,CAjB6B,EAkB7B;AAAEkH,EAAAA,OAAO,EAAEzF,WAAX;AAAwB4T,EAAAA,QAAQ,EAAE5T,WAAlC;AAA+C2F,EAAAA,IAAI,EAAE,CAAC7F,MAAD;AAArD,CAlB6B,EAmB7B;AAAE2F,EAAAA,OAAO,EAAEoC,YAAX;AAAyB+L,EAAAA,QAAQ,EAAE/L,YAAnC;AAAiDlC,EAAAA,IAAI,EAAE,CAACiC,qBAAD,EAAwB9H,MAAxB;AAAvD,CAnB6B,EAoB7B;AAAE2F,EAAAA,OAAO,EAAEhH,UAAX;AAAuBmV,EAAAA,QAAQ,EAAEtM,UAAjC;AAA6C3B,EAAAA,IAAI,EAAE;AAAnD,CApB6B,CAAjC;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMmO,aAAN,CAAoB;AAChBxS,EAAAA,WAAW,CAACyS,YAAD,EAAe;AACtB,QAAIA,YAAJ,EAAkB;AACd,YAAM,IAAI1N,KAAJ,CAAW,+JAAX,CAAN;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC+B,SAApB2N,oBAAoB,CAACC,MAAD,EAAS;AAChC,WAAO;AACHC,MAAAA,QAAQ,EAAEJ,aADP;AAEHK,MAAAA,SAAS,EAAE,CACP;AAAE1O,QAAAA,OAAO,EAAEpG,MAAX;AAAmBoU,QAAAA,QAAQ,EAAEQ,MAAM,CAACzH;AAApC,OADO,EAEP;AAAE/G,QAAAA,OAAO,EAAEb,aAAX;AAA0BiP,QAAAA,WAAW,EAAExU;AAAvC,OAFO,EAGPmG,2BAHO;AAFR,KAAP;AAQH;;AAvBe;;AAyBpBsO,aAAa,CAACrM,IAAd;AAAA,mBAA0GqM,aAA1G,EA1yB6FnV,EA0yB7F,UAAyImV,aAAzI;AAAA;;AACAA,aAAa,CAACM,IAAd,kBA3yB6FzV,EA2yB7F;AAAA,QAA2GmV;AAA3G;AACAA,aAAa,CAACO,IAAd,kBA5yB6F1V,EA4yB7F;AAAA,aAAqIgV,wBAArI;AAAA,YAAyKjV,YAAzK,EAAuLuB,iBAAvL;AAAA;;AACA;AAAA,qDA7yB6FtB,EA6yB7F,mBAA2FmV,aAA3F,EAAsH,CAAC;AAC3GnM,IAAAA,IAAI,EAAEzH,QADqG;AAE3GgJ,IAAAA,IAAI,EAAE,CAAC;AAAEiL,MAAAA,SAAS,EAAER,wBAAb;AAAuCW,MAAAA,OAAO,EAAE,CAAC5V,YAAD,EAAeuB,iBAAf;AAAhD,KAAD;AAFqG,GAAD,CAAtH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE0H,MAAAA,IAAI,EAAEmM,aAAR;AAAuB7K,MAAAA,UAAU,EAAE,CAAC;AAClEtB,QAAAA,IAAI,EAAExH;AAD4D,OAAD,EAElE;AACCwH,QAAAA,IAAI,EAAEvH;AADP,OAFkE,EAIlE;AACCuH,QAAAA,IAAI,EAAExI,MADP;AAEC+J,QAAAA,IAAI,EAAE,CAAC4K,aAAD;AAFP,OAJkE;AAAnC,KAAD,CAAP;AAOlB,GAVxB;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,SAASS,UAAT,GAAsB;AAClB,SAAO,IAAIC,IAAJ,CAASnU,QAAQ,CAAC9B,QAAD,CAAjB,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMiW,IAAN,CAAW;AACPlT,EAAAA,WAAW,CAAC8H,IAAD,EAAO;AACd,SAAKA,IAAL,GAAYA,IAAZ;AACA,SAAKqL,IAAL,GAAYnW,OAAO,EAAnB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIoW,EAAAA,MAAM,CAACC,GAAD,EAAMC,aAAa,GAAG,KAAtB,EAA6B;AAC/B,QAAI,CAACD,GAAL,EACI,OAAO,IAAP;AACJ,WAAO,KAAKE,mBAAL,CAAyBF,GAAzB,EAA8BC,aAA9B,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIE,EAAAA,OAAO,CAACC,IAAD,EAAOH,aAAa,GAAG,KAAvB,EAA8B;AACjC,QAAI,CAACG,IAAL,EACI,OAAO,EAAP;AACJ,WAAOA,IAAI,CAACC,MAAL,CAAY,CAAC1C,MAAD,EAASqC,GAAT,KAAiB;AAChC,UAAIA,GAAJ,EAAS;AACLrC,QAAAA,MAAM,CAACrL,IAAP,CAAY,KAAK4N,mBAAL,CAAyBF,GAAzB,EAA8BC,aAA9B,CAAZ;AACH;;AACD,aAAOtC,MAAP;AACH,KALM,EAKJ,EALI,CAAP;AAMH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACI2C,EAAAA,MAAM,CAACC,YAAD,EAAe;AACjB,QAAI,CAACA,YAAL,EACI,OAAO,IAAP;AACJ,WAAO,KAAK9L,IAAL,CAAUhF,aAAV,CAAyB,QAAO8Q,YAAa,GAA7C,KAAoD,IAA3D;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,OAAO,CAACD,YAAD,EAAe;AAClB,QAAI,CAACA,YAAL,EACI,OAAO,EAAP;;AACJ,UAAME;AAAK;AAAD,MAAgB,KAAKhM,IAAL,CAAU/D,gBAAV,CAA4B,QAAO6P,YAAa,GAAhD,CAA1B;;AACA,WAAOE,IAAI,GAAG,GAAG/M,KAAH,CAASgN,IAAT,CAAcD,IAAd,CAAH,GAAyB,EAApC;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIE,EAAAA,SAAS,CAACX,GAAD,EAAMY,QAAN,EAAgB;AACrB,QAAI,CAACZ,GAAL,EACI,OAAO,IAAP;AACJY,IAAAA,QAAQ,GAAGA,QAAQ,IAAI,KAAKC,cAAL,CAAoBb,GAApB,CAAvB;AACA,UAAMc,IAAI,GAAG,KAAKR,MAAL,CAAYM,QAAZ,CAAb;;AACA,QAAIE,IAAJ,EAAU;AACN,aAAO,KAAKC,yBAAL,CAA+Bf,GAA/B,EAAoCc,IAApC,CAAP;AACH;;AACD,WAAO,KAAKZ,mBAAL,CAAyBF,GAAzB,EAA8B,IAA9B,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIgB,EAAAA,SAAS,CAACT,YAAD,EAAe;AACpB,SAAKU,gBAAL,CAAsB,KAAKX,MAAL,CAAYC,YAAZ,CAAtB;AACH;AACD;AACJ;AACA;AACA;;;AACIU,EAAAA,gBAAgB,CAACH,IAAD,EAAO;AACnB,QAAIA,IAAJ,EAAU;AACN,WAAKhB,IAAL,CAAUvS,MAAV,CAAiBuT,IAAjB;AACH;AACJ;;AACDZ,EAAAA,mBAAmB,CAACY,IAAD,EAAOb,aAAa,GAAG,KAAvB,EAA8B;AAC7C,QAAI,CAACA,aAAL,EAAoB;AAChB,YAAMW,QAAQ,GAAG,KAAKC,cAAL,CAAoBC,IAApB,CAAjB,CADgB,CAEhB;AACA;AACA;;;AACA,YAAMxP,IAAI,GAAG,KAAKkP,OAAL,CAAaI,QAAb,EAAuBM,MAAvB,CAA8B5P,IAAI,IAAI,KAAK6P,mBAAL,CAAyBL,IAAzB,EAA+BxP,IAA/B,CAAtC,EAA4E,CAA5E,CAAb;AACA,UAAIA,IAAI,KAAK+C,SAAb,EACI,OAAO/C,IAAP;AACP;;AACD,UAAMsC,OAAO,GAAG,KAAKkM,IAAL,CAAUnS,aAAV,CAAwB,MAAxB,CAAhB;;AACA,SAAKoT,yBAAL,CAA+BD,IAA/B,EAAqClN,OAArC;;AACA,UAAM4B,IAAI,GAAG,KAAKf,IAAL,CAAU2M,oBAAV,CAA+B,MAA/B,EAAuC,CAAvC,CAAb;;AACA5L,IAAAA,IAAI,CAACK,WAAL,CAAiBjC,OAAjB;AACA,WAAOA,OAAP;AACH;;AACDmN,EAAAA,yBAAyB,CAACf,GAAD,EAAM/S,EAAN,EAAU;AAC/B8L,IAAAA,MAAM,CAACsI,IAAP,CAAYrB,GAAZ,EAAiB5N,OAAjB,CAA0BkP,IAAD,IAAUrU,EAAE,CAAC4C,YAAH,CAAgB,KAAK0R,cAAL,CAAoBD,IAApB,CAAhB,EAA2CtB,GAAG,CAACsB,IAAD,CAA9C,CAAnC;AACA,WAAOrU,EAAP;AACH;;AACD4T,EAAAA,cAAc,CAACb,GAAD,EAAM;AAChB,UAAMwB,IAAI,GAAGxB,GAAG,CAACzQ,IAAJ,GAAW,MAAX,GAAoB,UAAjC;AACA,WAAQ,GAAEiS,IAAK,KAAIxB,GAAG,CAACwB,IAAD,CAAO,GAA7B;AACH;;AACDL,EAAAA,mBAAmB,CAACnB,GAAD,EAAM1O,IAAN,EAAY;AAC3B,WAAOyH,MAAM,CAACsI,IAAP,CAAYrB,GAAZ,EAAiByB,KAAjB,CAAwBtE,GAAD,IAAS7L,IAAI,CAAC5B,YAAL,CAAkB,KAAK6R,cAAL,CAAoBpE,GAApB,CAAlB,MAAgD6C,GAAG,CAAC7C,GAAD,CAAnF,CAAP;AACH;;AACDoE,EAAAA,cAAc,CAACD,IAAD,EAAO;AACjB,WAAOI,aAAa,CAACJ,IAAD,CAAb,IAAuBA,IAA9B;AACH;;AA9HM;;AAgIXzB,IAAI,CAAC/M,IAAL;AAAA,mBAAiG+M,IAAjG,EA59B6F7V,EA49B7F,UAAuHJ,QAAvH;AAAA;;AACAiW,IAAI,CAAC9M,KAAL,kBA79B6F/I,EA69B7F;AAAA,SAAqG6V,IAArG;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA,UAA2ID,UAA3I;AAAA;;AAAA;AAAA;AAAA,cAAuH;AAAvH;;AACA;AAAA,qDA99B6F5V,EA89B7F,mBAA2F6V,IAA3F,EAA6G,CAAC;AAClG7M,IAAAA,IAAI,EAAEzI,UAD4F;AAElGgK,IAAAA,IAAI,EAAE,CAAC;AAAEoN,MAAAA,UAAU,EAAE,MAAd;AAAsB5Q,MAAAA,UAAU,EAAE6O,UAAlC;AAA8C5O,MAAAA,IAAI,EAAE;AAApD,KAAD;AAF4F,GAAD,CAA7G,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEgC,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DtB,QAAAA,IAAI,EAAExI,MADwD;AAE9D+J,QAAAA,IAAI,EAAE,CAAC3K,QAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GANxB;AAAA;AAOA;AACA;AACA;;;AACA,MAAM8X,aAAa,GAAG;AAClBE,EAAAA,SAAS,EAAE;AADO,CAAtB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;AACA,SAASC,WAAT,GAAuB;AACnB,SAAO,IAAIC,KAAJ,CAAUpW,QAAQ,CAAC9B,QAAD,CAAlB,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMkY,KAAN,CAAY;AACRnV,EAAAA,WAAW,CAAC8H,IAAD,EAAO;AACd,SAAKA,IAAL,GAAYA,IAAZ;AACH;AACD;AACJ;AACA;;;AACIsN,EAAAA,QAAQ,GAAG;AACP,WAAO,KAAKtN,IAAL,CAAUuN,KAAjB;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,QAAQ,CAACC,QAAD,EAAW;AACf,SAAKzN,IAAL,CAAUuN,KAAV,GAAkBE,QAAQ,IAAI,EAA9B;AACH;;AAhBO;;AAkBZJ,KAAK,CAAChP,IAAN;AAAA,mBAAkGgP,KAAlG,EArhC6F9X,EAqhC7F,UAAyHJ,QAAzH;AAAA;;AACAkY,KAAK,CAAC/O,KAAN,kBAthC6F/I,EAshC7F;AAAA,SAAsG8X,KAAtG;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA,UAA6ID,WAA7I;AAAA;;AAAA;AAAA;AAAA,cAAyH;AAAzH;;AACA;AAAA,qDAvhC6F7X,EAuhC7F,mBAA2F8X,KAA3F,EAA8G,CAAC;AACnG9O,IAAAA,IAAI,EAAEzI,UAD6F;AAEnGgK,IAAAA,IAAI,EAAE,CAAC;AAAEoN,MAAAA,UAAU,EAAE,MAAd;AAAsB5Q,MAAAA,UAAU,EAAE8Q,WAAlC;AAA+C7Q,MAAAA,IAAI,EAAE;AAArD,KAAD;AAF6F,GAAD,CAA9G,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEgC,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DtB,QAAAA,IAAI,EAAExI,MADwD;AAE9D+J,QAAAA,IAAI,EAAE,CAAC3K,QAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMuY,iBAAiB,GAAG,UAA1B;AACA,MAAMC,gBAAgB,GAAG,WAAzB;;AACA,SAASC,mBAAT,CAA6BC,KAA7B,EAAoC;AAChC,SAAOA,KAAK,CAACxL,OAAN,CAAcqL,iBAAd,EAAiC,CAAC,GAAGI,CAAJ,KAAU,MAAMA,CAAC,CAAC,CAAD,CAAD,CAAKxF,WAAL,EAAjD,CAAP;AACH;;AACD,SAASyF,mBAAT,CAA6BF,KAA7B,EAAoC;AAChC,SAAOA,KAAK,CAACxL,OAAN,CAAcsL,gBAAd,EAAgC,CAAC,GAAGG,CAAJ,KAAUA,CAAC,CAAC,CAAD,CAAD,CAAKE,WAAL,EAA1C,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,WAAT,CAAqBnT,IAArB,EAA2B+J,KAA3B,EAAkC;AAC9B,MAAI,OAAOqJ,QAAP,KAAoB,WAApB,IAAmC,CAACA,QAAxC,EAAkD;AAC9C;AACA;AACA;AACA;AACA,UAAMC,EAAE,GAAGtY,OAAO,CAAC,IAAD,CAAP,GAAgBA,OAAO,CAAC,IAAD,CAAP,IAAiB,EAA5C;AACAsY,IAAAA,EAAE,CAACrT,IAAD,CAAF,GAAW+J,KAAX;AACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMuJ,GAAG,GAAG,OAAOlU,MAAP,KAAkB,WAAlB,IAAiCA,MAAjC,IAA2C,EAAvD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMmU,yBAAN,CAAgC;AAC5BnW,EAAAA,WAAW,CAACoW,SAAD,EAAYC,QAAZ,EAAsB;AAC7B,SAAKD,SAAL,GAAiBA,SAAjB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACH;;AAJ2B;AAMhC;AACA;AACA;AACA;;;AACA,MAAMC,eAAN,CAAsB;AAClBtW,EAAAA,WAAW,CAACuW,GAAD,EAAM;AACb,SAAKC,MAAL,GAAcD,GAAG,CAAC9S,QAAJ,CAAaC,GAAb,CAAiB1E,cAAjB,CAAd;AACH,GAHiB,CAIlB;;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIyX,EAAAA,mBAAmB,CAACC,MAAD,EAAS;AACxB,UAAMC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAAC,QAAD,CAA/B;AACA,UAAME,WAAW,GAAG,kBAApB,CAFwB,CAGxB;;AACA,UAAMC,mBAAmB,GAAGX,GAAG,CAACpK,OAAJ,CAAYgL,OAAZ,IAAuB,IAAnD;;AACA,QAAIH,MAAM,IAAIE,mBAAd,EAAmC;AAC/BX,MAAAA,GAAG,CAACpK,OAAJ,CAAYgL,OAAZ,CAAoBF,WAApB;AACH;;AACD,UAAMG,KAAK,GAAGC,cAAc,EAA5B;AACA,QAAIX,QAAQ,GAAG,CAAf;;AACA,WAAOA,QAAQ,GAAG,CAAX,IAAiBW,cAAc,KAAKD,KAApB,GAA6B,GAApD,EAAyD;AACrD,WAAKP,MAAL,CAAYS,IAAZ;AACAZ,MAAAA,QAAQ;AACX;;AACD,UAAMnK,GAAG,GAAG8K,cAAc,EAA1B;;AACA,QAAIL,MAAM,IAAIE,mBAAd,EAAmC;AAC/BX,MAAAA,GAAG,CAACpK,OAAJ,CAAYoL,UAAZ,CAAuBN,WAAvB;AACH;;AACD,UAAMR,SAAS,GAAG,CAAClK,GAAG,GAAG6K,KAAP,IAAgBV,QAAlC;AACAH,IAAAA,GAAG,CAACpK,OAAJ,CAAYqL,GAAZ,CAAiB,OAAMd,QAAS,0BAAhC;AACAH,IAAAA,GAAG,CAACpK,OAAJ,CAAYqL,GAAZ,CAAiB,GAAEf,SAAS,CAACgB,OAAV,CAAkB,CAAlB,CAAqB,eAAxC;AACA,WAAO,IAAIjB,yBAAJ,CAA8BC,SAA9B,EAAyCC,QAAzC,CAAP;AACH;;AA3CiB;;AA6CtB,SAASW,cAAT,GAA0B;AACtB,SAAOd,GAAG,CAACmB,WAAJ,IAAmBnB,GAAG,CAACmB,WAAJ,CAAgBC,GAAnC,GAAyCpB,GAAG,CAACmB,WAAJ,CAAgBC,GAAhB,EAAzC,GACH,IAAIC,IAAJ,GAAWC,OAAX,EADJ;AAEH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,oBAAoB,GAAG,UAA7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,gBAAT,CAA0BnB,GAA1B,EAA+B;AAC3BR,EAAAA,WAAW,CAAC0B,oBAAD,EAAuB,IAAInB,eAAJ,CAAoBC,GAApB,CAAvB,CAAX;AACA,SAAOA,GAAP;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASoB,iBAAT,GAA6B;AACzB5B,EAAAA,WAAW,CAAC0B,oBAAD,EAAuB,IAAvB,CAAX;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,UAAT,CAAoBC,IAApB,EAA0B;AACtB,QAAMC,WAAW,GAAG;AAChB,SAAK,KADW;AAEhB,SAAK,KAFW;AAGhB,UAAM,KAHU;AAIhB,SAAK,KAJW;AAKhB,SAAK;AALW,GAApB;AAOA,SAAOD,IAAI,CAAC1N,OAAL,CAAa,UAAb,EAAyB4N,CAAC,IAAID,WAAW,CAACC,CAAD,CAAzC,CAAP;AACH;;AACD,SAASC,YAAT,CAAsBH,IAAtB,EAA4B;AACxB,QAAMI,aAAa,GAAG;AAClB,WAAO,GADW;AAElB,WAAO,GAFW;AAGlB,WAAO,IAHW;AAIlB,WAAO,GAJW;AAKlB,WAAO;AALW,GAAtB;AAOA,SAAOJ,IAAI,CAAC1N,OAAL,CAAa,UAAb,EAAyB4N,CAAC,IAAIE,aAAa,CAACF,CAAD,CAA3C,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,YAAT,CAAsB1H,GAAtB,EAA2B;AACvB,SAAOA,GAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM2H,aAAN,CAAoB;AAChBnY,EAAAA,WAAW,GAAG;AACV,SAAKoY,KAAL,GAAa,EAAb;AACA,SAAKC,oBAAL,GAA4B,EAA5B;AACH;AACD;;;AACW,SAAJ7T,IAAI,CAAC8T,SAAD,EAAY;AACnB,UAAMC,aAAa,GAAG,IAAIJ,aAAJ,EAAtB;AACAI,IAAAA,aAAa,CAACH,KAAd,GAAsBE,SAAtB;AACA,WAAOC,aAAP;AACH;AACD;AACJ;AACA;;;AACI7U,EAAAA,GAAG,CAAC8M,GAAD,EAAMgI,YAAN,EAAoB;AACnB,WAAO,KAAKJ,KAAL,CAAW5H,GAAX,MAAoB9I,SAApB,GAAgC,KAAK0Q,KAAL,CAAW5H,GAAX,CAAhC,GAAkDgI,YAAzD;AACH;AACD;AACJ;AACA;;;AACI/Q,EAAAA,GAAG,CAAC+I,GAAD,EAAM7D,KAAN,EAAa;AACZ,SAAKyL,KAAL,CAAW5H,GAAX,IAAkB7D,KAAlB;AACH;AACD;AACJ;AACA;;;AACI/L,EAAAA,MAAM,CAAC4P,GAAD,EAAM;AACR,WAAO,KAAK4H,KAAL,CAAW5H,GAAX,CAAP;AACH;AACD;AACJ;AACA;;;AACIiI,EAAAA,MAAM,CAACjI,GAAD,EAAM;AACR,WAAO,KAAK4H,KAAL,CAAWtG,cAAX,CAA0BtB,GAA1B,CAAP;AACH;AACD;AACJ;AACA;;;AACIkI,EAAAA,WAAW,CAAClI,GAAD,EAAMrL,QAAN,EAAgB;AACvB,SAAKkT,oBAAL,CAA0B7H,GAA1B,IAAiCrL,QAAjC;AACH;AACD;AACJ;AACA;;;AACIwT,EAAAA,MAAM,GAAG;AACL;AACA,SAAK,MAAMnI,GAAX,IAAkB,KAAK6H,oBAAvB,EAA6C;AACzC,UAAI,KAAKA,oBAAL,CAA0BvG,cAA1B,CAAyCtB,GAAzC,CAAJ,EAAmD;AAC/C,YAAI;AACA,eAAK4H,KAAL,CAAW5H,GAAX,IAAkB,KAAK6H,oBAAL,CAA0B7H,GAA1B,GAAlB;AACH,SAFD,CAGA,OAAOoI,CAAP,EAAU;AACN9M,UAAAA,OAAO,CAACC,IAAR,CAAa,qCAAb,EAAoD6M,CAApD;AACH;AACJ;AACJ;;AACD,WAAOC,IAAI,CAACC,SAAL,CAAe,KAAKV,KAApB,CAAP;AACH;;AAzDe;;AA2DpBD,aAAa,CAAChS,IAAd;AAAA,mBAA0GgS,aAA1G;AAAA;;AACAA,aAAa,CAAC/R,KAAd,kBAnyC6F/I,EAmyC7F;AAAA,SAA8G8a,aAA9G;AAAA,WAA8GA,aAA9G;AAAA;;AACA;AAAA,qDApyC6F9a,EAoyC7F,mBAA2F8a,aAA3F,EAAsH,CAAC;AAC3G9R,IAAAA,IAAI,EAAEzI;AADqG,GAAD,CAAtH;AAAA;;AAGA,SAASmb,iBAAT,CAA2B7X,GAA3B,EAAgCgK,KAAhC,EAAuC;AACnC;AACA;AACA,QAAM8N,MAAM,GAAG9X,GAAG,CAAC+X,cAAJ,CAAmB/N,KAAK,GAAG,QAA3B,CAAf;AACA,MAAIgO,YAAY,GAAG,EAAnB;;AACA,MAAIF,MAAM,IAAIA,MAAM,CAAC/P,WAArB,EAAkC;AAC9B,QAAI;AACA;AACAiQ,MAAAA,YAAY,GAAGL,IAAI,CAACM,KAAL,CAAWnB,YAAY,CAACgB,MAAM,CAAC/P,WAAR,CAAvB,CAAf;AACH,KAHD,CAIA,OAAO2P,CAAP,EAAU;AACN9M,MAAAA,OAAO,CAACC,IAAR,CAAa,qDAAqDb,KAAlE,EAAyE0N,CAAzE;AACH;AACJ;;AACD,SAAOT,aAAa,CAAC3T,IAAd,CAAmB0U,YAAnB,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAME,0BAAN,CAAiC;;AAEjCA,0BAA0B,CAACjT,IAA3B;AAAA,mBAAuHiT,0BAAvH;AAAA;;AACAA,0BAA0B,CAACtG,IAA3B,kBAh0C6FzV,EAg0C7F;AAAA,QAAwH+b;AAAxH;AACAA,0BAA0B,CAACrG,IAA3B,kBAj0C6F1V,EAi0C7F;AAAA,aAA+J,CAAC;AAAE8G,IAAAA,OAAO,EAAEgU,aAAX;AAA0B/T,IAAAA,UAAU,EAAE2U,iBAAtC;AAAyD1U,IAAAA,IAAI,EAAE,CAACpH,QAAD,EAAWc,MAAX;AAA/D,GAAD;AAA/J;;AACA;AAAA,qDAl0C6FV,EAk0C7F,mBAA2F+b,0BAA3F,EAAmI,CAAC;AACxH/S,IAAAA,IAAI,EAAEzH,QADkH;AAExHgJ,IAAAA,IAAI,EAAE,CAAC;AACCiL,MAAAA,SAAS,EAAE,CAAC;AAAE1O,QAAAA,OAAO,EAAEgU,aAAX;AAA0B/T,QAAAA,UAAU,EAAE2U,iBAAtC;AAAyD1U,QAAAA,IAAI,EAAE,CAACpH,QAAD,EAAWc,MAAX;AAA/D,OAAD;AADZ,KAAD;AAFkH,GAAD,CAAnI;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMsb,EAAN,CAAS;AACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACc,SAAHC,GAAG,GAAG;AACT,WAAO,MAAM,IAAb;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACc,SAAHC,GAAG,CAACtF,QAAD,EAAW;AACjB,WAAQuF,YAAD,IAAkB;AACrB,aAAOA,YAAY,CAACC,aAAb,IAA8B,IAA9B,GACHC,cAAc,CAACF,YAAY,CAACC,aAAd,EAA6BxF,QAA7B,CADX,GAEH,KAFJ;AAGH,KAJD;AAKH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACoB,SAAT0F,SAAS,CAACtT,IAAD,EAAO;AACnB,WAAQuT,SAAD,IAAeA,SAAS,CAACC,cAAV,CAAyB/I,OAAzB,CAAiCzK,IAAjC,MAA2C,CAAC,CAAlE;AACH;;AArCI;;AAuCT,SAASqT,cAAT,CAAwBI,CAAxB,EAA2B7F,QAA3B,EAAqC;AACjC,MAAIjX,OAAO,GAAGwE,aAAV,CAAwBsY,CAAxB,CAAJ,EAAgC;AAC5B,WAAOA,CAAC,CAACC,OAAF,IAAaD,CAAC,CAACC,OAAF,CAAU9F,QAAV,CAAb,IACH6F,CAAC,CAACE,iBAAF,IAAuBF,CAAC,CAACE,iBAAF,CAAoB/F,QAApB,CADpB,IAEH6F,CAAC,CAACG,qBAAF,IAA2BH,CAAC,CAACG,qBAAF,CAAwBhG,QAAxB,CAF/B;AAGH;;AACD,SAAO,KAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMiG,WAAW,GAAG;AAChB;AACA,SAAO,IAFS;AAGhB,cAAY,IAHI;AAIhB,aAAW,IAJK;AAKhB,YAAU,IALM;AAMhB,eAAa,IANG;AAOhB,aAAW,IAPK;AAQhB,cAAY,IARI;AAShB,WAAS,IATO;AAUhB,aAAW,IAVK;AAWhB;AACA,WAAS,IAZO;AAahB,gBAAc,IAbE;AAchB,eAAa,IAdG;AAehB,cAAY,IAfI;AAgBhB,iBAAe,IAhBC;AAiBhB,aAAW,IAjBK;AAkBhB,cAAY,IAlBI;AAmBhB;AACA,WAAS,IApBO;AAqBhB,aAAW,IArBK;AAsBhB;AACA,YAAU,IAvBM;AAwBhB,iBAAe,IAxBC;AAyBhB,gBAAc,IAzBE;AA0BhB,eAAa,IA1BG;AA2BhB,kBAAgB,IA3BA;AA4BhB;AACA,WAAS,IA7BO;AA8BhB,eAAa,IA9BG;AA+BhB,gBAAc,IA/BE;AAgChB,aAAW,IAhCK;AAiChB,eAAa,IAjCG;AAkChB;AACA,SAAO,IAnCS;AAoChB,eAAa;AApCG,CAApB;AAsCA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAqB,GAAG,IAAI7c,cAAJ,CAAmB,qBAAnB,CAA9B;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM8c,aAAa,GAAG,IAAI9c,cAAJ,CAAmB,cAAnB,CAAtB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM+c,mBAAN,CAA0B;AACtBra,EAAAA,WAAW,GAAG;AACV;AACR;AACA;AACA;AACA;AACQ,SAAKsa,MAAL,GAAc,EAAd;AACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACQ,SAAKC,SAAL,GAAiB,EAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,WAAW,CAACvT,OAAD,EAAU;AACjB,UAAMwT,EAAE,GAAG,IAAIC,MAAJ,CAAWzT,OAAX,EAAoB,KAAK0T,OAAzB,CAAX;AACAF,IAAAA,EAAE,CAAC/W,GAAH,CAAO,OAAP,EAAgB+D,GAAhB,CAAoB;AAAEmT,MAAAA,MAAM,EAAE;AAAV,KAApB;AACAH,IAAAA,EAAE,CAAC/W,GAAH,CAAO,QAAP,EAAiB+D,GAAjB,CAAqB;AAAEmT,MAAAA,MAAM,EAAE;AAAV,KAArB;;AACA,SAAK,MAAM1T,SAAX,IAAwB,KAAKqT,SAA7B,EAAwC;AACpCE,MAAAA,EAAE,CAAC/W,GAAH,CAAOwD,SAAP,EAAkBO,GAAlB,CAAsB,KAAK8S,SAAL,CAAerT,SAAf,CAAtB;AACH;;AACD,WAAOuT,EAAP;AACH;;AAxCqB;;AA0C1BJ,mBAAmB,CAAClU,IAApB;AAAA,mBAAgHkU,mBAAhH;AAAA;;AACAA,mBAAmB,CAACjU,KAApB,kBAn/C6F/I,EAm/C7F;AAAA,SAAoHgd,mBAApH;AAAA,WAAoHA,mBAApH;AAAA;;AACA;AAAA,qDAp/C6Fhd,EAo/C7F,mBAA2Fgd,mBAA3F,EAA4H,CAAC;AACjHhU,IAAAA,IAAI,EAAEzI;AAD2G,GAAD,CAA5H;AAAA;AAGA;AACA;AACA;AACA;AACA;;;AACA,MAAMid,oBAAN,SAAmChT,kBAAnC,CAAsD;AAClD7H,EAAAA,WAAW,CAACkB,GAAD,EAAM4Z,OAAN,EAAehP,OAAf,EAAwBiP,MAAxB,EAAgC;AACvC,UAAM7Z,GAAN;AACA,SAAK4Z,OAAL,GAAeA,OAAf;AACA,SAAKhP,OAAL,GAAeA,OAAf;AACA,SAAKiP,MAAL,GAAcA,MAAd;AACA,SAAKC,cAAL,GAAsB,IAAtB;AACH;;AACDxT,EAAAA,QAAQ,CAACN,SAAD,EAAY;AAChB,QAAI,CAACgT,WAAW,CAACpI,cAAZ,CAA2B5K,SAAS,CAACkJ,WAAV,EAA3B,CAAD,IAAwD,CAAC,KAAK6K,aAAL,CAAmB/T,SAAnB,CAA7D,EAA4F;AACxF,aAAO,KAAP;AACH;;AACD,QAAI,CAAClF,MAAM,CAAC0Y,MAAR,IAAkB,CAAC,KAAKK,MAA5B,EAAoC;AAChC,UAAI,OAAOlR,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,aAAKiC,OAAL,CAAaC,IAAb,CAAmB,QAAO7E,SAAU,mDAAlB,GACb,iDADL;AAEH;;AACD,aAAO,KAAP;AACH;;AACD,WAAO,IAAP;AACH;;AACDzG,EAAAA,gBAAgB,CAACwG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;AAC1C,UAAMiK,IAAI,GAAG,KAAKvK,OAAL,CAAaU,OAAb,EAAb;AACAL,IAAAA,SAAS,GAAGA,SAAS,CAACkJ,WAAV,EAAZ,CAF0C,CAG1C;AACA;;AACA,QAAI,CAACpO,MAAM,CAAC0Y,MAAR,IAAkB,KAAKK,MAA3B,EAAmC;AAC/B,WAAKC,cAAL,GAAsB,KAAKA,cAAL,IAAuB,KAAKD,MAAL,EAA7C,CAD+B,CAE/B;AACA;AACA;;AACA,UAAIG,kBAAkB,GAAG,KAAzB;;AACA,UAAIC,UAAU,GAAG,MAAM;AACnBD,QAAAA,kBAAkB,GAAG,IAArB;AACH,OAFD;;AAGA,WAAKF,cAAL,CACKpX,IADL,CACU,MAAM;AACZ;AACA,YAAI,CAAC5B,MAAM,CAAC0Y,MAAZ,EAAoB;AAChB,cAAI,OAAO7Q,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,iBAAKiC,OAAL,CAAaC,IAAb,CAAmB,mEAAnB;AACH;;AACDoP,UAAAA,UAAU,GAAG,MAAM,CAAG,CAAtB;;AACA;AACH;;AACD,YAAI,CAACD,kBAAL,EAAyB;AACrB;AACA;AACAC,UAAAA,UAAU,GAAG,KAAK1a,gBAAL,CAAsBwG,OAAtB,EAA+BC,SAA/B,EAA0CC,OAA1C,CAAb;AACH;AACJ,OAfD,EAgBKiU,KAhBL,CAgBW,MAAM;AACb,YAAI,OAAOvR,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,eAAKiC,OAAL,CAAaC,IAAb,CAAmB,QAAO7E,SAAU,6CAAlB,GACb,0BADL;AAEH;;AACDiU,QAAAA,UAAU,GAAG,MAAM,CAAG,CAAtB;AACH,OAtBD,EAT+B,CAgC/B;AACA;AACA;;;AACA,aAAO,MAAM;AACTA,QAAAA,UAAU;AACb,OAFD;AAGH;;AACD,WAAO/J,IAAI,CAAClB,iBAAL,CAAuB,MAAM;AAChC;AACA,YAAMuK,EAAE,GAAG,KAAKK,OAAL,CAAaN,WAAb,CAAyBvT,OAAzB,CAAX;;AACA,YAAM9B,QAAQ,GAAG,UAAUkW,QAAV,EAAoB;AACjCjK,QAAAA,IAAI,CAACC,UAAL,CAAgB,YAAY;AACxBlK,UAAAA,OAAO,CAACkU,QAAD,CAAP;AACH,SAFD;AAGH,OAJD;;AAKAZ,MAAAA,EAAE,CAACa,EAAH,CAAMpU,SAAN,EAAiB/B,QAAjB;AACA,aAAO,MAAM;AACTsV,QAAAA,EAAE,CAACc,GAAH,CAAOrU,SAAP,EAAkB/B,QAAlB,EADS,CAET;;AACA,YAAI,OAAOsV,EAAE,CAAClO,OAAV,KAAsB,UAA1B,EAAsC;AAClCkO,UAAAA,EAAE,CAAClO,OAAH;AACH;AACJ,OAND;AAOH,KAhBM,CAAP;AAiBH;;AACD0O,EAAAA,aAAa,CAAC/T,SAAD,EAAY;AACrB,WAAO,KAAK4T,OAAL,CAAaR,MAAb,CAAoBxJ,OAApB,CAA4B5J,SAA5B,IAAyC,CAAC,CAAjD;AACH;;AArFiD;;AAuFtD2T,oBAAoB,CAAC1U,IAArB;AAAA,mBAAiH0U,oBAAjH,EAnlD6Fxd,EAmlD7F,UAAuJJ,QAAvJ,GAnlD6FI,EAmlD7F,UAA4K8c,qBAA5K,GAnlD6F9c,EAmlD7F,UAA8MA,EAAE,CAAC4B,QAAjN,GAnlD6F5B,EAmlD7F,UAAsO+c,aAAtO;AAAA;;AACAS,oBAAoB,CAACzU,KAArB,kBAplD6F/I,EAolD7F;AAAA,SAAqHwd,oBAArH;AAAA,WAAqHA,oBAArH;AAAA;;AACA;AAAA,qDArlD6Fxd,EAqlD7F,mBAA2Fwd,oBAA3F,EAA6H,CAAC;AAClHxU,IAAAA,IAAI,EAAEzI;AAD4G,GAAD,CAA7H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEyI,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DtB,QAAAA,IAAI,EAAExI,MADwD;AAE9D+J,QAAAA,IAAI,EAAE,CAAC3K,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEoJ,MAAAA,IAAI,EAAEgU,mBAAR;AAA6B1S,MAAAA,UAAU,EAAE,CAAC;AAC5CtB,QAAAA,IAAI,EAAExI,MADsC;AAE5C+J,QAAAA,IAAI,EAAE,CAACuS,qBAAD;AAFsC,OAAD;AAAzC,KAH2B,EAM3B;AAAE9T,MAAAA,IAAI,EAAEhJ,EAAE,CAAC4B;AAAX,KAN2B,EAMJ;AAAEoH,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACzDtB,QAAAA,IAAI,EAAExH;AADmD,OAAD,EAEzD;AACCwH,QAAAA,IAAI,EAAExI,MADP;AAEC+J,QAAAA,IAAI,EAAE,CAACwS,aAAD;AAFP,OAFyD;AAA/B,KANI,CAAP;AAWlB,GAbxB;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoB,YAAN,CAAmB;;AAEnBA,YAAY,CAACrV,IAAb;AAAA,mBAAyGqV,YAAzG;AAAA;;AACAA,YAAY,CAAC1I,IAAb,kBAjnD6FzV,EAinD7F;AAAA,QAA0Gme;AAA1G;AACAA,YAAY,CAACzI,IAAb,kBAlnD6F1V,EAknD7F;AAAA,aAAmI,CAC3H;AACI8G,IAAAA,OAAO,EAAEmC,qBADb;AAEIgM,IAAAA,QAAQ,EAAEuI,oBAFd;AAGIvW,IAAAA,KAAK,EAAE,IAHX;AAIID,IAAAA,IAAI,EAAE,CAACpH,QAAD,EAAWkd,qBAAX,EAAkClb,QAAlC,EAA4C,CAAC,IAAIJ,QAAJ,EAAD,EAAiBub,aAAjB,CAA5C;AAJV,GAD2H,EAO3H;AAAEjW,IAAAA,OAAO,EAAEgW,qBAAX;AAAkC7H,IAAAA,QAAQ,EAAE+H,mBAA5C;AAAiEhW,IAAAA,IAAI,EAAE;AAAvE,GAP2H;AAAnI;;AASA;AAAA,qDA3nD6FhH,EA2nD7F,mBAA2Fme,YAA3F,EAAqH,CAAC;AAC1GnV,IAAAA,IAAI,EAAEzH,QADoG;AAE1GgJ,IAAAA,IAAI,EAAE,CAAC;AACCiL,MAAAA,SAAS,EAAE,CACP;AACI1O,QAAAA,OAAO,EAAEmC,qBADb;AAEIgM,QAAAA,QAAQ,EAAEuI,oBAFd;AAGIvW,QAAAA,KAAK,EAAE,IAHX;AAIID,QAAAA,IAAI,EAAE,CAACpH,QAAD,EAAWkd,qBAAX,EAAkClb,QAAlC,EAA4C,CAAC,IAAIJ,QAAJ,EAAD,EAAiBub,aAAjB,CAA5C;AAJV,OADO,EAOP;AAAEjW,QAAAA,OAAO,EAAEgW,qBAAX;AAAkC7H,QAAAA,QAAQ,EAAE+H,mBAA5C;AAAiEhW,QAAAA,IAAI,EAAE;AAAvE,OAPO;AADZ,KAAD;AAFoG,GAAD,CAArH;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoX,YAAN,CAAmB;;AAEnBA,YAAY,CAACtV,IAAb;AAAA,mBAAyGsV,YAAzG;AAAA;;AACAA,YAAY,CAACrV,KAAb,kBAnrD6F/I,EAmrD7F;AAAA,SAA6Goe,YAA7G;AAAA;AAAA;;AAAA;AAAA,oBAA6GA,YAA7G;AAAA;AAAA,UAnrD6Fpe,EAmrD7F,UAA+Lqe,gBAA/L;AAAA;;AAAA;AAAA;AAAA,cAAuI;AAAvI;;AACA;AAAA,qDAprD6Fre,EAorD7F,mBAA2Foe,YAA3F,EAAqH,CAAC;AAC1GpV,IAAAA,IAAI,EAAEzI,UADoG;AAE1GgK,IAAAA,IAAI,EAAE,CAAC;AAAEoN,MAAAA,UAAU,EAAE,MAAd;AAAsBzC,MAAAA,WAAW,EAAErT,UAAU,CAAC,MAAMwc,gBAAP;AAA7C,KAAD;AAFoG,GAAD,CAArH;AAAA;;AAIA,SAASC,uBAAT,CAAiClY,QAAjC,EAA2C;AACvC,SAAO,IAAIiY,gBAAJ,CAAqBjY,QAAQ,CAACC,GAAT,CAAazG,QAAb,CAArB,CAAP;AACH;;AACD,MAAMye,gBAAN,SAA+BD,YAA/B,CAA4C;AACxCzb,EAAAA,WAAW,CAAC8H,IAAD,EAAO;AACd;AACA,SAAKA,IAAL,GAAYA,IAAZ;AACH;;AACD8T,EAAAA,QAAQ,CAACC,GAAD,EAAMlP,KAAN,EAAa;AACjB,QAAIA,KAAK,IAAI,IAAb,EACI,OAAO,IAAP;;AACJ,YAAQkP,GAAR;AACI,WAAK1c,eAAe,CAAC2c,IAArB;AACI,eAAOnP,KAAP;;AACJ,WAAKxN,eAAe,CAAC4c,IAArB;AACI,YAAI3c,gCAAgC,CAACuN,KAAD,EAAQ;AAAO;AAAf,SAApC,EAAgE;AAC5D,iBAAOtN,gBAAgB,CAACsN,KAAD,CAAvB;AACH;;AACD,eAAOnN,cAAc,CAAC,KAAKsI,IAAN,EAAY2J,MAAM,CAAC9E,KAAD,CAAlB,CAAd,CAAyCqP,QAAzC,EAAP;;AACJ,WAAK7c,eAAe,CAAC8c,KAArB;AACI,YAAI7c,gCAAgC,CAACuN,KAAD,EAAQ;AAAQ;AAAhB,SAApC,EAAkE;AAC9D,iBAAOtN,gBAAgB,CAACsN,KAAD,CAAvB;AACH;;AACD,eAAOA,KAAP;;AACJ,WAAKxN,eAAe,CAAC+c,MAArB;AACI,YAAI9c,gCAAgC,CAACuN,KAAD,EAAQ;AAAS;AAAjB,SAApC,EAAoE;AAChE,iBAAOtN,gBAAgB,CAACsN,KAAD,CAAvB;AACH;;AACD,cAAM,IAAI5H,KAAJ,CAAU,uCAAV,CAAN;;AACJ,WAAK5F,eAAe,CAACgd,GAArB;AACI,cAAM9V,IAAI,GAAG/G,0BAA0B,CAACqN,KAAD,CAAvC;;AACA,YAAIvN,gCAAgC,CAACuN,KAAD,EAAQ;AAAM;AAAd,SAApC,EAA8D;AAC1D,iBAAOtN,gBAAgB,CAACsN,KAAD,CAAvB;AACH;;AACD,eAAOpN,aAAa,CAACkS,MAAM,CAAC9E,KAAD,CAAP,CAApB;;AACJ,WAAKxN,eAAe,CAACid,YAArB;AACI,YAAIhd,gCAAgC,CAACuN,KAAD,EAAQ;AAAc;AAAtB,SAApC,EAA8E;AAC1E,iBAAOtN,gBAAgB,CAACsN,KAAD,CAAvB;AACH;;AACD,cAAM,IAAI5H,KAAJ,CAAU,gFAAV,CAAN;;AACJ;AACI,cAAM,IAAIA,KAAJ,CAAW,8BAA6B8W,GAAI,qCAA5C,CAAN;AA9BR;AAgCH;;AACDQ,EAAAA,uBAAuB,CAAC1P,KAAD,EAAQ;AAC3B,WAAOlN,4BAA4B,CAACkN,KAAD,CAAnC;AACH;;AACD2P,EAAAA,wBAAwB,CAAC3P,KAAD,EAAQ;AAC5B,WAAOjN,6BAA6B,CAACiN,KAAD,CAApC;AACH;;AACD4P,EAAAA,yBAAyB,CAAC5P,KAAD,EAAQ;AAC7B,WAAOhN,8BAA8B,CAACgN,KAAD,CAArC;AACH;;AACD6P,EAAAA,sBAAsB,CAAC7P,KAAD,EAAQ;AAC1B,WAAO/M,2BAA2B,CAAC+M,KAAD,CAAlC;AACH;;AACD8P,EAAAA,8BAA8B,CAAC9P,KAAD,EAAQ;AAClC,WAAO9M,mCAAmC,CAAC8M,KAAD,CAA1C;AACH;;AAvDuC;;AAyD5C+O,gBAAgB,CAACvV,IAAjB;AAAA,mBAA6GuV,gBAA7G,EApvD6Fre,EAovD7F,UAA+IJ,QAA/I;AAAA;;AACAye,gBAAgB,CAACtV,KAAjB,kBArvD6F/I,EAqvD7F;AAAA,SAAiHqe,gBAAjH;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA,UAAmKC,uBAAnK,CArvD6Fte,EAqvD7F,UAA4MI,QAA5M;AAAA;;AAAA;AAAA;AAAA,cAA+I;AAA/I;;AACA;AAAA,qDAtvD6FJ,EAsvD7F,mBAA2Fqe,gBAA3F,EAAyH,CAAC;AAC9GrV,IAAAA,IAAI,EAAEzI,UADwG;AAE9GgK,IAAAA,IAAI,EAAE,CAAC;AAAEoN,MAAAA,UAAU,EAAE,MAAd;AAAsB5Q,MAAAA,UAAU,EAAEuX,uBAAlC;AAA2DtX,MAAAA,IAAI,EAAE,CAAC5G,QAAD;AAAjE,KAAD;AAFwG,GAAD,CAAzH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAE4I,MAAAA,IAAI,EAAEqB,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DtB,QAAAA,IAAI,EAAExI,MADwD;AAE9D+J,QAAAA,IAAI,EAAE,CAAC3K,QAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMyf,OAAO,GAAG,IAAI5c,OAAJ,CAAY,QAAZ,CAAhB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS0S,aAAT,EAAwB4G,0BAAxB,EAAoDC,EAApD,EAAwDoC,YAAxD,EAAsEnV,qBAAtE,EAA6FC,YAA7F,EAA2G4T,qBAA3G,EAAkIC,aAAlI,EAAiJC,mBAAjJ,EAAsKmB,YAAtK,EAAoLtI,IAApL,EAA0LiC,KAA1L,EAAiMgD,aAAjM,EAAgNuE,OAAhN,EAAyN/E,iBAAzN,EAA4OD,gBAA5O,EAA8PQ,YAA9P,EAA4Q9F,eAA5Q,EAA6RjS,iBAAiB,IAAIwc,kBAAlT,EAAsUpY,qBAAqB,IAAIqY,sBAA/V,EAAuXzN,eAAe,IAAI0N,gBAA1Y,EAA4Z9R,mBAAmB,IAAI+R,oBAAnb,EAAycpB,gBAAgB,IAAIqB,iBAA7d,EAAgfpU,mBAAmB,IAAIqU,oBAAvgB,EAA6hBnC,oBAAoB,IAAIoC,qBAArjB,EAA4kB/K,mCAAmC,IAAIgL,oCAAnnB,EAAypBrN,eAAe,IAAIsN,gBAA5qB,EAA8rBzT,cAAc,IAAI0T,eAAhtB,EAAiuBrV,gBAAgB,IAAIsV,iBAArvB,EAAwwB/Z,aAAa,IAAIga,cAAzxB,EAAyyB1F,UAAU,IAAI2F,WAAvzB,EAAo0BlT,aAAa,IAAImT,cAAr1B,EAAq2BzL,cAAc,IAAI0L,eAAv3B,EAAw4BxT,oBAAoB,IAAIyT,qBAAh6B,EAAu7BtT,iBAAiB,IAAIuT,kBAA58B", "sourcesContent": ["/**\n * @license Angular v13.0.3\n * (c) 2010-2021 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, setTestabilityGetter, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵINJECTOR_SCOPE, NgZone, RendererFactory2, Testability, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, ɵConsole, forwardRef, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵgetSanitizationBypassType, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n    constructor() {\n        super(...arguments);\n        this.supportsDOMEvents = true;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n    static makeCurrent() {\n        ɵsetRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener) {\n        el.addEventListener(evt, listener, false);\n        // Needed to follow Dart's subscription semantic, until fix of\n        // https://code.google.com/p/dart/issues/detail?id=17406\n        return () => {\n            el.removeEventListener(evt, listener, false);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        if (node.parentNode) {\n            node.parentNode.removeChild(node);\n        }\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return ɵparseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\n// based on urlUtils.js in AngularJS 1\nlet urlParsingNode;\nfunction relativePath(url) {\n    urlParsingNode = urlParsingNode || document.createElement('a');\n    urlParsingNode.setAttribute('href', url);\n    const pathName = urlParsingNode.pathname;\n    return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\nfunction appInitializerFactory(transitionId, document, injector) {\n    return () => {\n        // Wait for all application initializers to be completed before removing the styles set by\n        // the server.\n        injector.get(ApplicationInitStatus).donePromise.then(() => {\n            const dom = ɵgetDOM();\n            const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n            for (let i = 0; i < styles.length; i++) {\n                dom.remove(styles[i]);\n            }\n        });\n    };\n}\nconst SERVER_TRANSITION_PROVIDERS = [\n    {\n        provide: APP_INITIALIZER,\n        useFactory: appInitializerFactory,\n        deps: [TRANSITION_ID, DOCUMENT, Injector],\n        multi: true\n    },\n];\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass BrowserGetTestability {\n    static init() {\n        setTestabilityGetter(new BrowserGetTestability());\n    }\n    addToWindow(registry) {\n        ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new Error('Could not find testability for element.');\n            }\n            return testability;\n        };\n        ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback /** TODO #9100 */) => {\n            const testabilities = ɵglobal['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            let didWork = false;\n            const decrement = function (didWork_ /** TODO #9100 */) {\n                didWork = didWork || didWork_;\n                count--;\n                if (count == 0) {\n                    callback(didWork);\n                }\n            };\n            testabilities.forEach(function (testability /** TODO #9100 */) {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!ɵglobal['frameworkStabilizers']) {\n            ɵglobal['frameworkStabilizers'] = [];\n        }\n        ɵglobal['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (ɵgetDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n}\nBrowserXhr.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nBrowserXhr.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserXhr });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        this._eventNameToPlugin = new Map();\n        plugins.forEach(p => p.manager = this);\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler);\n    }\n    /**\n     * Registers a global handler for an event in a target view.\n     *\n     * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns A callback function that can be used to remove the handler.\n     * @deprecated No longer being used in Ivy code. To be removed in version 14.\n     */\n    addGlobalEventListener(target, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addGlobalEventListener(target, eventName, handler);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        const plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        for (let i = 0; i < plugins.length; i++) {\n            const plugin = plugins[i];\n            if (plugin.supports(eventName)) {\n                this._eventNameToPlugin.set(eventName, plugin);\n                return plugin;\n            }\n        }\n        throw new Error(`No event manager plugin found for event ${eventName}`);\n    }\n}\nEventManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nEventManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: EventManager });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }]; } });\nclass EventManagerPlugin {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    addGlobalEventListener(element, eventName, handler) {\n        const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n        if (!target) {\n            throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n        }\n        return this.addEventListener(target, eventName, handler);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass SharedStylesHost {\n    constructor() {\n        /** @internal */\n        this._stylesSet = new Set();\n    }\n    addStyles(styles) {\n        const additions = new Set();\n        styles.forEach(style => {\n            if (!this._stylesSet.has(style)) {\n                this._stylesSet.add(style);\n                additions.add(style);\n            }\n        });\n        this.onStylesAdded(additions);\n    }\n    onStylesAdded(additions) { }\n    getAllStyles() {\n        return Array.from(this._stylesSet);\n    }\n}\nSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: SharedStylesHost, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: SharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }] });\nclass DomSharedStylesHost extends SharedStylesHost {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n        // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n        this._hostNodes = new Map();\n        this._hostNodes.set(_doc.head, []);\n    }\n    _addStylesToHost(styles, host, styleNodes) {\n        styles.forEach((style) => {\n            const styleEl = this._doc.createElement('style');\n            styleEl.textContent = style;\n            styleNodes.push(host.appendChild(styleEl));\n        });\n    }\n    addHost(hostNode) {\n        const styleNodes = [];\n        this._addStylesToHost(this._stylesSet, hostNode, styleNodes);\n        this._hostNodes.set(hostNode, styleNodes);\n    }\n    removeHost(hostNode) {\n        const styleNodes = this._hostNodes.get(hostNode);\n        if (styleNodes) {\n            styleNodes.forEach(removeStyle);\n        }\n        this._hostNodes.delete(hostNode);\n    }\n    onStylesAdded(additions) {\n        this._hostNodes.forEach((styleNodes, hostNode) => {\n            this._addStylesToHost(additions, hostNode, styleNodes);\n        });\n    }\n    ngOnDestroy() {\n        this._hostNodes.forEach(styleNodes => styleNodes.forEach(removeStyle));\n    }\n}\nDomSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomSharedStylesHost, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomSharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomSharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\nfunction removeStyle(styleNode) {\n    ɵgetDOM().remove(styleNode);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction flattenStyles(compId, styles, target) {\n    for (let i = 0; i < styles.length; i++) {\n        let style = styles[i];\n        if (Array.isArray(style)) {\n            flattenStyles(compId, style, target);\n        }\n        else {\n            style = style.replace(COMPONENT_REGEX, compId);\n            target.push(style);\n        }\n    }\n    return target;\n}\nfunction decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n    // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n    // the listener (see below).\n    return (event) => {\n        // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n        // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n        // can inspect the listener toString contents for the existence of this special token. Because\n        // the token is a string literal, it is ensured to not be modified by compiled code.\n        if (event === '__ngUnwrap__') {\n            return eventHandler;\n        }\n        const allowDefaultBehavior = eventHandler(event);\n        if (allowDefaultBehavior === false) {\n            // TODO(tbosch): move preventDefault into event plugins...\n            event.preventDefault();\n            event.returnValue = false;\n        }\n        return undefined;\n    };\n}\nlet hasLoggedNativeEncapsulationWarning = false;\nclass DomRendererFactory2 {\n    constructor(eventManager, sharedStylesHost, appId) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.rendererByCompId = new Map();\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        switch (type.encapsulation) {\n            case ViewEncapsulation.Emulated: {\n                let renderer = this.rendererByCompId.get(type.id);\n                if (!renderer) {\n                    renderer = new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type, this.appId);\n                    this.rendererByCompId.set(type.id, renderer);\n                }\n                renderer.applyToHost(element);\n                return renderer;\n            }\n            // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an enum\n            // value that is not known (but previously was the value for ViewEncapsulation.Native)\n            case 1:\n            case ViewEncapsulation.ShadowDom:\n                // TODO(FW-2290): remove the `case 1:` fallback logic and the warning in v12.\n                if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an\n                    // enum value that is not known (but previously was the value for\n                    // ViewEncapsulation.Native)\n                    !hasLoggedNativeEncapsulationWarning && type.encapsulation === 1) {\n                    hasLoggedNativeEncapsulationWarning = true;\n                    console.warn('ViewEncapsulation.Native is no longer supported. Falling back to ViewEncapsulation.ShadowDom. The fallback will be removed in v12.');\n                }\n                return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n            default: {\n                if (!this.rendererByCompId.has(type.id)) {\n                    const styles = flattenStyles(type.id, type.styles, []);\n                    this.sharedStylesHost.addStyles(styles);\n                    this.rendererByCompId.set(type.id, this.defaultRenderer);\n                }\n                return this.defaultRenderer;\n            }\n        }\n    }\n    begin() { }\n    end() { }\n}\nDomRendererFactory2.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: DomSharedStylesHost }, { token: APP_ID }], target: i0.ɵɵFactoryTarget.Injectable });\nDomRendererFactory2.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomRendererFactory2 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: EventManager }, { type: DomSharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }]; } });\nclass DefaultDomRenderer2 {\n    constructor(eventManager) {\n        this.eventManager = eventManager;\n        this.data = Object.create(null);\n        this.destroyNode = null;\n    }\n    destroy() { }\n    createElement(name, namespace) {\n        if (namespace) {\n            // In cases where Ivy (not ViewEngine) is giving us the actual namespace, the look up by key\n            // will result in undefined, so we just return the namespace here.\n            return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return document.createElement(name);\n    }\n    createComment(value) {\n        return document.createComment(value);\n    }\n    createText(value) {\n        return document.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        parent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            parent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(parent, oldChild) {\n        if (parent) {\n            parent.removeChild(oldChild);\n        }\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) :\n            selectorOrNode;\n        if (!el) {\n            throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            // TODO(FW-811): Ivy may cause issues here because it's passing around\n            // full URIs for namespaces, therefore this lookup will fail.\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            // TODO(FW-811): Ivy may cause issues here because it's passing around\n            // full URIs for namespaces, therefore this lookup will fail.\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                // TODO(FW-811): Since ivy is passing around full URIs for namespaces\n                // this could result in properties like `http://www.w3.org/2000/svg:cx=\"123\"`,\n                // which is wrong.\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            el.style.removeProperty(style);\n        }\n        else {\n            // IE requires '' instead of null\n            // see https://github.com/angular/angular/issues/7916\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        NG_DEV_MODE && checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback) {\n        NG_DEV_MODE && checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n        }\n        return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, component, appId) {\n        super(eventManager);\n        this.component = component;\n        const styles = flattenStyles(appId + '-' + component.id, component.styles, []);\n        sharedStylesHost.addStyles(styles);\n        this.contentAttr = shimContentAttribute(appId + '-' + component.id);\n        this.hostAttr = shimHostAttribute(appId + '-' + component.id);\n    }\n    applyToHost(element) {\n        super.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, hostEl, component) {\n        super(eventManager);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        const styles = flattenStyles(component.id, component.styles, []);\n        for (let i = 0; i < styles.length; i++) {\n            const styleEl = document.createElement('style');\n            styleEl.textContent = styles[i];\n            this.shadowRoot.appendChild(styleEl);\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(parent, oldChild) {\n        return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        element.addEventListener(eventName, handler, false);\n        return () => this.removeEventListener(element, eventName, handler);\n    }\n    removeEventListener(target, eventName, callback) {\n        return target.removeEventListener(eventName, callback);\n    }\n}\nDomEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\nconst DOM_KEY_LOCATION_NUMPAD = 3;\n// Map to convert some key or keyIdentifier values to what will be returned by getEventKey\nconst _keyMap = {\n    // The following values are here for cross-browser compatibility and to match the W3C standard\n    // cf https://www.w3.org/TR/DOM-Level-3-Events-key/\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS'\n};\n// There is a bug in Chrome for numeric keypad keys:\n// https://code.google.com/p/chromium/issues/detail?id=155654\n// 1, 2, 3 ... are reported as A, B, C ...\nconst _chromeNumKeyPadMap = {\n    'A': '1',\n    'B': '2',\n    'C': '3',\n    'D': '4',\n    'E': '5',\n    'F': '6',\n    'G': '7',\n    'H': '8',\n    'I': '9',\n    'J': '*',\n    'K': '+',\n    'M': '-',\n    'N': '.',\n    'O': '/',\n    '\\x60': '0',\n    '\\x90': 'NumLock'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n        });\n    }\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        MODIFIER_KEYS.forEach(modifierName => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    static getEventFullKey(event) {\n        let fullKey = '';\n        let key = getEventKey(event);\n        key = key.toLowerCase();\n        if (key === ' ') {\n            key = 'space'; // for readability\n        }\n        else if (key === '.') {\n            key = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            if (modifierName != key) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    fullKey += modifierName + '.';\n                }\n            }\n        });\n        fullKey += key;\n        return fullKey;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event /** TODO #9100 */) => {\n            if (KeyEventsPlugin.getEventFullKey(event) === fullKey) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        // TODO: switch to a Map if the mapping grows too much\n        switch (keyName) {\n            case 'esc':\n                return 'escape';\n            default:\n                return keyName;\n        }\n    }\n}\nKeyEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nKeyEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: KeyEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\nfunction getEventKey(event) {\n    let key = event.key;\n    if (key == null) {\n        key = event.keyIdentifier;\n        // keyIdentifier is defined in the old draft of DOM Level 3 Events implemented by Chrome and\n        // Safari cf\n        // https://www.w3.org/TR/2007/WD-DOM-Level-3-Events-20071221/events.html#Events-KeyboardEvents-Interfaces\n        if (key == null) {\n            return 'Unidentified';\n        }\n        if (key.startsWith('U+')) {\n            key = String.fromCharCode(parseInt(key.substring(2), 16));\n            if (event.location === DOM_KEY_LOCATION_NUMPAD && _chromeNumKeyPadMap.hasOwnProperty(key)) {\n                // There is a bug in Chrome for numeric keypad keys:\n                // https://code.google.com/p/chromium/issues/detail?id=155654\n                // 1, 2, 3 ... are reported as A, B, C ...\n                key = _chromeNumKeyPadMap[key];\n            }\n        }\n    }\n    return _keyMap[key] || key;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n    BrowserGetTestability.init();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    ɵsetDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document, deps: [] },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: ɵINJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler, deps: [] },\n    {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT, NgZone, PLATFORM_ID]\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] },\n    {\n        provide: DomRendererFactory2,\n        useClass: DomRendererFactory2,\n        deps: [EventManager, DomSharedStylesHost, APP_ID]\n    },\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: SharedStylesHost, useExisting: DomSharedStylesHost },\n    { provide: DomSharedStylesHost, useClass: DomSharedStylesHost, deps: [DOCUMENT] },\n    { provide: Testability, useClass: Testability, deps: [NgZone] },\n    { provide: EventManager, useClass: EventManager, deps: [EVENT_MANAGER_PLUGINS, NgZone] },\n    { provide: XhrFactory, useClass: BrowserXhr, deps: [] },\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor(parentModule) {\n        if (parentModule) {\n            throw new Error(`BrowserModule has already been loaded. If you need access to common directives such as NgIf and NgFor from a lazy loaded module, import CommonModule instead.`);\n        }\n    }\n    /**\n     * Configures a browser-based app to transition from a server-rendered app, if\n     * one is present on the page.\n     *\n     * @param params An object containing an identifier for the app to transition.\n     * The ID must match between the client and server versions of the app.\n     * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n     */\n    static withServerTransition(params) {\n        return {\n            ngModule: BrowserModule,\n            providers: [\n                { provide: APP_ID, useValue: params.appId },\n                { provide: TRANSITION_ID, useExisting: APP_ID },\n                SERVER_TRANSITION_PROVIDERS,\n            ],\n        };\n    }\n}\nBrowserModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserModule, deps: [{ token: BrowserModule, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] });\nBrowserModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserModule, providers: BROWSER_MODULE_PROVIDERS, imports: [CommonModule, ApplicationModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{ providers: BROWSER_MODULE_PROVIDERS, exports: [CommonModule, ApplicationModule] }]\n        }], ctorParameters: function () { return [{ type: BrowserModule, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [BrowserModule]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\nfunction createMeta() {\n    return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n}\nMeta.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nMeta.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: Meta, providedIn: 'root', useFactory: createMeta, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createMeta, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv'\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Factory to create Title service.\n */\nfunction createTitle() {\n    return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n}\nTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nTitle.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: Title, providedIn: 'root', useFactory: createTitle, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createTitle, deps: [] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst CAMEL_CASE_REGEXP = /([A-Z])/g;\nconst DASH_CASE_REGEXP = /-([a-z])/g;\nfunction camelCaseToDashCase(input) {\n    return input.replace(CAMEL_CASE_REGEXP, (...m) => '-' + m[1].toLowerCase());\n}\nfunction dashCaseToCamelCase(input) {\n    return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n        ng[name] = value;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst win = typeof window !== 'undefined' && window || {};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ChangeDetectionPerfRecord {\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        const isProfilerAvailable = win.console.profile != null;\n        if (record && isProfilerAvailable) {\n            win.console.profile(profileName);\n        }\n        const start = performanceNow();\n        let numTicks = 0;\n        while (numTicks < 5 || (performanceNow() - start) < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performanceNow();\n        if (record && isProfilerAvailable) {\n            win.console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        win.console.log(`ran ${numTicks} change detection cycles`);\n        win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\nfunction performanceNow() {\n    return win.performance && win.performance.now ? win.performance.now() :\n        new Date().getTime();\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction escapeHtml(text) {\n    const escapedText = {\n        '&': '&a;',\n        '\"': '&q;',\n        '\\'': '&s;',\n        '<': '&l;',\n        '>': '&g;',\n    };\n    return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\nfunction unescapeHtml(text) {\n    const unescapedText = {\n        '&a;': '&',\n        '&q;': '\"',\n        '&s;': '\\'',\n        '&l;': '<',\n        '&g;': '>',\n    };\n    return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\nfunction makeStateKey(key) {\n    return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * `TransferState` will be available as an injectable token. To use it import\n * `ServerTransferStateModule` on the server and `BrowserTransferStateModule` on the client.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\nclass TransferState {\n    constructor() {\n        this.store = {};\n        this.onSerializeCallbacks = {};\n    }\n    /** @internal */\n    static init(initState) {\n        const transferState = new TransferState();\n        transferState.store = initState;\n        return transferState;\n    }\n    /**\n     * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n     */\n    get(key, defaultValue) {\n        return this.store[key] !== undefined ? this.store[key] : defaultValue;\n    }\n    /**\n     * Set the value corresponding to a key.\n     */\n    set(key, value) {\n        this.store[key] = value;\n    }\n    /**\n     * Remove a key from the store.\n     */\n    remove(key) {\n        delete this.store[key];\n    }\n    /**\n     * Test whether a key exists in the store.\n     */\n    hasKey(key) {\n        return this.store.hasOwnProperty(key);\n    }\n    /**\n     * Register a callback to provide the value for a key when `toJson` is called.\n     */\n    onSerialize(key, callback) {\n        this.onSerializeCallbacks[key] = callback;\n    }\n    /**\n     * Serialize the current state of the store to JSON.\n     */\n    toJson() {\n        // Call the onSerialize callbacks and put those values into the store.\n        for (const key in this.onSerializeCallbacks) {\n            if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n                try {\n                    this.store[key] = this.onSerializeCallbacks[key]();\n                }\n                catch (e) {\n                    console.warn('Exception in onSerialize callback: ', e);\n                }\n            }\n        }\n        return JSON.stringify(this.store);\n    }\n}\nTransferState.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TransferState, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTransferState.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TransferState });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TransferState, decorators: [{\n            type: Injectable\n        }] });\nfunction initTransferState(doc, appId) {\n    // Locate the script tag with the JSON data transferred from the server.\n    // The id of the script tag is set to the Angular appId + 'state'.\n    const script = doc.getElementById(appId + '-state');\n    let initialState = {};\n    if (script && script.textContent) {\n        try {\n            // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n            initialState = JSON.parse(unescapeHtml(script.textContent));\n        }\n        catch (e) {\n            console.warn('Exception while restoring TransferState for app ' + appId, e);\n        }\n    }\n    return TransferState.init(initialState);\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n */\nclass BrowserTransferStateModule {\n}\nBrowserTransferStateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserTransferStateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserTransferStateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserTransferStateModule });\nBrowserTransferStateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserTransferStateModule, providers: [{ provide: TransferState, useFactory: initTransferState, deps: [DOCUMENT, APP_ID] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserTransferStateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [{ provide: TransferState, useFactory: initTransferState, deps: [DOCUMENT, APP_ID] }],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null ?\n                elementMatches(debugElement.nativeElement, selector) :\n                false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (ɵgetDOM().isElementNode(n)) {\n        return n.matches && n.matches(selector) ||\n            n.msMatchesSelector && n.msMatchesSelector(selector) ||\n            n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n    return false;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n    constructor() {\n        /**\n         * A set of supported event names for gestures to be used in Angular.\n         * Angular supports all built-in recognizers, as listed in\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         */\n        this.events = [];\n        /**\n         * Maps gesture event names to a set of configuration options\n         * that specify overrides to the default values for specific properties.\n         *\n         * The key is a supported event name to be configured,\n         * and the options object contains a set of properties, with override values\n         * to be applied to the named recognizer event.\n         * For example, to disable recognition of the rotate event, specify\n         *  `{\"rotate\": {\"enable\": false}}`.\n         *\n         * Properties that are not present take the HammerJS default values.\n         * For information about which properties are supported for which events,\n         * and their allowed and default values, see\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         *\n         */\n        this.overrides = {};\n    }\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n}\nHammerGestureConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGestureConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerGestureConfig });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    constructor(doc, _config, console, loader) {\n        super(doc);\n        this._config = _config;\n        this.console = console;\n        this.loader = loader;\n        this._loaderPromise = null;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || this.loader();\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            this._loaderPromise\n                .then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            })\n                .catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            });\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n}\nHammerGesturesPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.ɵConsole }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGesturesPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerGesturesPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: HammerGestureConfig, decorators: [{\n                    type: Inject,\n                    args: [HAMMER_GESTURE_CONFIG]\n                }] }, { type: i0.ɵConsole }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }]; } });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\nclass HammerModule {\n}\nHammerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHammerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerModule });\nHammerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerModule, providers: [\n        {\n            provide: EVENT_MANAGER_PLUGINS,\n            useClass: HammerGesturesPlugin,\n            multi: true,\n            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n        },\n        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n                    ]\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n}\nDomSanitizer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(function () { return DomSanitizerImpl; }) });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nfunction domSanitizerImplFactory(injector) {\n    return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\nclass DomSanitizerImpl extends DomSanitizer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* Html */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* Style */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* Script */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error('unsafe value used in a script context');\n            case SecurityContext.URL:\n                const type = ɵgetSanitizationBypassType(value);\n                if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* Url */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* ResourceUrl */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error('unsafe value used in a resource URL context (see https://g.co/ng/security#xss)');\n            default:\n                throw new Error(`Unexpected SecurityContext ${ctx} (see https://g.co/ng/security#xss)`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return ɵbypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return ɵbypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return ɵbypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return ɵbypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n}\nDomSanitizerImpl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizerImpl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [{ token: Injector }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [Injector] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('13.0.3');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, Title, TransferState, VERSION, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };\n"]}, "metadata": {}, "sourceType": "module"}
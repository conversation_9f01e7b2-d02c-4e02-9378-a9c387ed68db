.card.tale-bg {
    background: linear-gradient(to right, #FF6F0B, #FF953F) !important;
    text-align: center;
}

.filter-options{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-option-row{
  display: flex;
  justify-content: end !important;
  margin-bottom: 10px !important;
}

.dropdown{
  width: 300px;
  height: 30px;
  margin-left: 5px;
  border-radius: 5px;
  font-size: 0.9rem;
}


.chart-row{
  width: 100% !important;
  margin-left: 10px;
  margin-right: 10px;
}

.card {
    display: flex;
    justify-content: center; 
    align-items: center;
  }
  
  .card-people-wrapper {
    display: flex;
    justify-content: center; /* Horizontally center */
    align-items: center; /* Vertically center */
    height: 100%; /* Take full height of parent */
  }
  
  .info-card{
    height: 120px;
    background-color: #FF6F0B !important;
    text-align: center;
  }

  .chart-card{
    padding-top: 10px;
    padding-bottom: 10px;
  }

  p-chart{
    width:1080px !important;
  }

  // .card.chart-card {
  //   position: relative;
  //   padding: 20px;
  // }
  
  // .filter-options {
  //   position: absolute;
  //   top: 20px;
  //   right: 80px;
  //   display: flex;
  //   align-items: center;
  // }
  

  // .sector-options {
  //   position: absolute;
  //   top: 20px;
  //   left:100px;
  //   display: flex;
  //   align-items: center;
  // }

  .btn-group {
    display: inline-flex;
}

.btn-group input[type="radio"] {
    display: none;
}

.btn-group .btn {
    padding: 10px 20px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    cursor: pointer;
    margin: 0;
}

.btn-group .btn:hover {
    background-color: #e2e6ea;
}

.btn-group input[type="radio"]:checked + .btn {
    background-color: orange;
    border-color: orange;
    color: white;
}

.btn-group label:first-of-type {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.btn-group label:last-of-type {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.btn-group label + label {
    border-left: none;
}


//   .btn{
//     height: 40px;
//     border-color: black;
//     color: black;
//   }

// .btn:hover{
//   background-color: white;
//   color: black;
//   border-color: black;
// }

 


  @media (max-width: 320px) {
    p-chart{
      width:250px !important;
      padding-top: 20px !important;
    }
    .chart-card{
     margin-top: 10px;
    }
    // .filter-options {
    //   position: absolute;
    //   top: 10px;
    //   right: 0;
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   margin-right: 20%;
    //   margin-top: 30px;
    // }
    .btn-group .btn {
      padding: 5px 5px;
      border: 1px solid #ccc;
      background-color: #f8f9fa;
      cursor: pointer;
      margin: 0;
  }
  .dropdown{
    width: 180px;
    margin-left: 0px;
    margin-bottom: 10px;
  }
  .filter-options{
    display: flex;
    flex-direction: column;
    align-items: center;
  }  

  .dropdown{
    height: 23px;
  }
  }
  
  @media (min-width: 321px) and (max-width: 610px) {
    p-chart{
      width:300px !important;
      overflow-clip-margin: none !important;
      overflow: auto !important;
      font-size: x-small !important;
      padding-top: 20px !important;
    }

    .dropdown{
      height: 23px;
    }

    .chart-card{
      margin-top: 10px;
      overflow-x: auto ;
     }
    //  .filter-options {
    //   position: absolute;
    //   top: 10px;
    //   right: 0;
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   margin-right:25%;
    //   margin-top: 30px;
    // }
    .btn-group .btn {
      padding: 5px 5px;
      border: 1px solid #ccc;
      background-color: #f8f9fa;
      cursor: pointer;
      margin: 0;
  }
  .dropdown{
    width: 180px;
    margin-left: 0px;
    margin-bottom: 10px;
  }
  .filter-options{
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  }

  

@media (min-width: 611px) and (max-width: 767px) {
  p-chart{
    width:550px !important;
  }
  .chart-card{
    margin-top: 10px;
   }
  //  .filter-options {
  //   position: absolute;
  //   top: 10px;
  //   right: 0;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   margin-right: 25%;
  // }
  .btn-group .btn {
    padding: 5px 5px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    cursor: pointer;
    margin: 0;
}

.dropdown{
  width: 180px;
  margin-left: 2px;
}
}

@media (min-width: 768px) and (max-width: 1146px) {
  p-chart{
    width:684px !important;
  }
  // .filter-options {
  //   position: absolute;
  //   top: 10px;
  //   right: 25px;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  // }
  .btn-group .btn {
    padding: 5px 5px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    cursor: pointer;
    margin: 0;
}

.dropdown{
  width: 180px;
  margin-left: 2px;
}

}

@media (min-width: 1146px) and (max-width: 1382px) {
  p-chart{
    width:794px !important;
  }
}

@media (min-width: 1383px) and (max-width: 1440px) {
  p-chart{
    width:1080px !important;
  }
  
}



.position-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: 1rem;
  height: 150px; 
  display: flex;
  flex-direction: column;
  justify-content: center; 
  text-align: center;
  cursor: pointer;
}


.warning{
  font-size: 0.9rem !important;
}

.card-row{
  padding-left: 25px;
  padding-right: 25px;
}

.position-card:hover {
  transform: translateY(-4px);
  box-shadow: 1px 2px 8px 12px rgba(0, 0, 0, 0.15);
}

.position-card .card-body {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center; 
  text-align: center; 
}

.position-card .card-sector,
.position-card .card-role,
.position-card .card-date {
  margin: 0;
}

.position-card .card-sector {
  font-size: 1rem;
  font-weight: bold;
}

.position-card .card-role {
  font-size: 0.9rem;
}

.position-card .card-date {
  font-size: 0.8rem;
}


@media screen and (max-width: 425px) {
  .position-card .card-sector {
    font-size: 0.9rem;
    font-weight: bold;
  }
  
  .position-card .card-role {
    font-size: 0.8rem;
  }
  
  .position-card .card-date {
    font-size: 0.7rem;
  }
  
  .card-row{
    padding-left: 5px;
    padding-right: 5px;
  }
}

@media (min-width: 992px) and (max-width: 1244px) {
  .position-card .card-sector {
    font-size: 0.9rem;
    font-weight: bold;
    line-height: 18px;
  }

  .position-card {
    height: 160px;
  }
  
  .position-card .card-role {
    font-size: 0.8rem;
  }
  
  .position-card .card-date {
    font-size: 0.7rem;
  }

  .card-row{
    padding-left: 5px;
    padding-right: 5px;
  }
  
}

/* Style for the Add New Position card */
.add-new-card {
  // border: 1px dashed #ddd;
  border-radius: 8px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 150px; 
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #f0f0f0;
  border: 2px dashed #cccccc;
  cursor: pointer;

}

.add-new-card:hover {
  transform: translateY(-4px);
  box-shadow: 1px 2px 8px 12px rgba(0, 0, 0, 0.15);
}

.add-icon {
  font-size: 2rem;
  font-weight: bold;
}

.add-text {
  margin-top: 0.5rem;
  font-size: 1rem;
}

.modal {
  background-color: rgba(0, 0, 0, 0.5)
}

{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { FormControl, Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged, switchMap, map, catchError } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"src/app/shared/services/data-transfer.service\";\nimport * as i4 from \"ngx-toastr\";\nimport * as i5 from \"@angular/common/http\";\nimport * as i6 from \"ngx-spinner\";\nimport * as i7 from \"../../../sidebar.component\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"ngx-image-cropper\";\nimport * as i10 from \"../../../../../camel-case.pipe\";\n\nfunction EditAppUserComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelementStart(1, \"label\", 76);\n    i0.ɵɵtext(2, \"Sharer Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 77);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_12_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Email is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_12_div_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Invalid email format.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_12_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵtemplate(1, EditAppUserComponent_div_12_div_4_div_1_Template, 2, 0, \"div\", 81);\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_12_div_4_div_2_Template, 2, 0, \"div\", 81);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r33.addNewAppUserForm.get(\"U_email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.addNewAppUserForm.controls[\"U_email\"].hasError(\"invalidEmail\"));\n  }\n}\n\nfunction EditAppUserComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelementStart(1, \"label\", 78);\n    i0.ɵɵtext(2, \"Sharer Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 79);\n    i0.ɵɵtemplate(4, EditAppUserComponent_div_12_div_4_Template, 3, 2, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r1.addNewAppUserForm.get(\"U_email\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = ctx_r1.addNewAppUserForm.get(\"U_email\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = ctx_r1.addNewAppUserForm.get(\"U_email\")) == null ? null : tmp_0_0.touched)));\n  }\n}\n\nfunction EditAppUserComponent_div_13_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Password is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_13_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Password must be at least 8 characters long.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_13_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Password must contain at least: \");\n    i0.ɵɵelementStart(2, \"ul\");\n    i0.ɵɵelementStart(3, \"li\");\n    i0.ɵɵtext(4, \"One uppercase letter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"li\");\n    i0.ɵɵtext(6, \"One lowercase letter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"li\");\n    i0.ɵɵtext(8, \"One number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"li\");\n    i0.ɵɵtext(10, \"One special character\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵtemplate(1, EditAppUserComponent_div_13_div_6_div_1_Template, 2, 0, \"div\", 81);\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_13_div_6_div_2_Template, 2, 0, \"div\", 81);\n    i0.ɵɵtemplate(3, EditAppUserComponent_div_13_div_6_div_3_Template, 11, 0, \"div\", 81);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r37.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r37.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.minlength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r37.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors.pattern);\n  }\n}\n\nfunction EditAppUserComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelementStart(1, \"label\", 82);\n    i0.ɵɵtext(2, \"Sharer Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 83);\n    i0.ɵɵelementStart(4, \"i\", 84, 85);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_13_Template_i_click_4_listener() {\n      i0.ɵɵrestoreView(_r42);\n\n      const _r36 = i0.ɵɵreference(5);\n\n      const ctx_r41 = i0.ɵɵnextContext();\n      return ctx_r41.togglePasswordVisibility(_r36, \"U_password\");\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, EditAppUserComponent_div_13_div_6_Template, 4, 3, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r2.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = ctx_r2.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = ctx_r2.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_0_0.touched)));\n  }\n}\n\nfunction EditAppUserComponent_img_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 86);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r3.imageSrc, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction EditAppUserComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵtext(1, \" The image must have a 1:1 aspect ratio. Please select a square image. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_a_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 87);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_a_22_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return ctx_r43.showCropper(\"U_dp\");\n    });\n    i0.ɵɵtext(1, \"Edit DP\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵelementStart(1, \"div\", 89);\n    i0.ɵɵelementStart(2, \"h5\", 90);\n    i0.ɵɵtext(3, \"Resize Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"image-cropper\", 91);\n    i0.ɵɵlistener(\"imageCropped\", function EditAppUserComponent_div_23_Template_image_cropper_imageCropped_4_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return ctx_r45.cropImg($event, \"U_dp\");\n    })(\"imageLoaded\", function EditAppUserComponent_div_23_Template_image_cropper_imageLoaded_4_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return ctx_r47.imgLoad();\n    })(\"cropperReady\", function EditAppUserComponent_div_23_Template_image_cropper_cropperReady_4_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return ctx_r48.initCropper();\n    })(\"loadImageFailed\", function EditAppUserComponent_div_23_Template_image_cropper_loadImageFailed_4_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return ctx_r49.imgFailed();\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_23_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return ctx_r50.saveCroppedImage(\"U_dp\");\n    });\n    i0.ɵɵtext(6, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_23_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return ctx_r51.hideCropper(\"U_dp\");\n    });\n    i0.ɵɵtext(8, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"imageChangedEvent\", ctx_r7.imgChangeEvt)(\"aspectRatio\", 1 / 1)(\"maintainAspectRatio\", true);\n  }\n}\n\nfunction EditAppUserComponent_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelementStart(1, \"div\", 95);\n    i0.ɵɵelement(2, \"input\", 96);\n    i0.ɵɵelementStart(3, \"label\", 97);\n    i0.ɵɵtext(4, \" Make this an expert sharer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_24_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelementStart(1, \"div\", 95);\n    i0.ɵɵelementStart(2, \"input\", 98);\n    i0.ɵɵlistener(\"change\", function EditAppUserComponent_div_24_div_2_Template_input_change_2_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return ctx_r54.anonymousCheck();\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 99);\n    i0.ɵɵtext(4, \" Make this an anonymous sharer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵtemplate(1, EditAppUserComponent_div_24_div_1_Template, 5, 0, \"div\", 8);\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_24_div_2_Template, 5, 0, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !((tmp_0_0 = ctx_r8.addNewAppUserForm.get(\"U_profileAnonymous\")) == null ? null : tmp_0_0.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !((tmp_1_0 = ctx_r8.addNewAppUserForm.get(\"U_isExpert\")) == null ? null : tmp_1_0.value));\n  }\n}\n\nfunction EditAppUserComponent_div_25_img_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 105);\n  }\n\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r56.aliasImageSrc, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction EditAppUserComponent_div_25_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_25_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵtext(1, \" The image must have a 1:1 aspect ratio. Please select a square image. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_25_a_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 87);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_25_a_13_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return ctx_r60.showCropper(\"U_aliasDp\");\n    });\n    i0.ɵɵtext(1, \"Edit DP\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵelementStart(1, \"div\", 9);\n    i0.ɵɵelementStart(2, \"label\", 100);\n    i0.ɵɵtext(3, \"Alias Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 9);\n    i0.ɵɵelementStart(6, \"label\", 102);\n    i0.ɵɵtext(7, \"Alias DP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵelementStart(9, \"input\", 103);\n    i0.ɵɵlistener(\"change\", function EditAppUserComponent_div_25_Template_input_change_9_listener($event) {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return ctx_r62.onFileSelected($event, \"U_aliasDp\");\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, EditAppUserComponent_div_25_img_10_Template, 1, 1, \"img\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, EditAppUserComponent_div_25_div_11_Template, 2, 0, \"div\", 14);\n    i0.ɵɵtemplate(12, EditAppUserComponent_div_25_div_12_Template, 2, 0, \"div\", 14);\n    i0.ɵɵtemplate(13, EditAppUserComponent_div_25_a_13_Template, 2, 0, \"a\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"readonly\", ctx_r9.isReadonly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.aliasImageSrc);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r9.addNewAppUserForm.get(\"U_aliasDp\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors.fileSizeValidator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r9.addNewAppUserForm.get(\"U_aliasDp\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors.fileAspectRatioValidator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isAliasCropperVisible && ctx_r9.aliasImageName && !((tmp_4_0 = ctx_r9.addNewAppUserForm.get(\"U_aliasDp\")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors.fileSizeValidator));\n  }\n}\n\nfunction EditAppUserComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵelementStart(1, \"div\", 89);\n    i0.ɵɵelementStart(2, \"h5\", 90);\n    i0.ɵɵtext(3, \"Resize Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"image-cropper\", 91);\n    i0.ɵɵlistener(\"imageCropped\", function EditAppUserComponent_div_26_Template_image_cropper_imageCropped_4_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return ctx_r64.cropImg($event, \"U_aliasDp\");\n    })(\"imageLoaded\", function EditAppUserComponent_div_26_Template_image_cropper_imageLoaded_4_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return ctx_r66.imgLoad();\n    })(\"cropperReady\", function EditAppUserComponent_div_26_Template_image_cropper_cropperReady_4_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return ctx_r67.initCropper();\n    })(\"loadImageFailed\", function EditAppUserComponent_div_26_Template_image_cropper_loadImageFailed_4_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r68 = i0.ɵɵnextContext();\n      return ctx_r68.imgFailed();\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_26_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r69 = i0.ɵɵnextContext();\n      return ctx_r69.saveCroppedImage(\"U_aliasDp\");\n    });\n    i0.ɵɵtext(6, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_26_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r70 = i0.ɵɵnextContext();\n      return ctx_r70.hideCropper(\"U_aliasDp\");\n    });\n    i0.ɵɵtext(8, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"imageChangedEvent\", ctx_r10.aliasImgChangeEvt)(\"aspectRatio\", 1 / 1)(\"maintainAspectRatio\", true);\n  }\n}\n\nfunction EditAppUserComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelementStart(1, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_27_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r71 = i0.ɵɵnextContext();\n      return ctx_r71.updateUserDetails();\n    });\n    i0.ɵɵtext(2, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 108);\n    i0.ɵɵtext(4, \"Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_35_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r75);\n      const activity_r73 = restoredCtx.$implicit;\n      const ctx_r74 = i0.ɵɵnextContext();\n      return ctx_r74.toggleSelection(activity_r73, ctx_r74.selectedActivities, \"QUA_quiz_options\", false, \"Preferences\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 110);\n    i0.ɵɵtext(3, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const activity_r73 = ctx.$implicit;\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r12.isItemSelected(activity_r73, ctx_r12.selectedActivities, \"QUA_quiz_options\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", activity_r73.option_title, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r78 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_41_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r78);\n      const sector_r76 = restoredCtx.$implicit;\n      const ctx_r77 = i0.ɵɵnextContext();\n      return ctx_r77.toggleSelection(sector_r76, ctx_r77.selectedSectors, \"LI_contentId\", false, \"Sectors\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 110);\n    i0.ɵɵtext(3, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const sector_r76 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r13.isItemSelected(sector_r76, ctx_r13.selectedSectors, \"LI_contentId\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", sector_r76.IN_name, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r81 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_47_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r81);\n      const type_r79 = restoredCtx.$implicit;\n      const ctx_r80 = i0.ɵɵnextContext();\n      return ctx_r80.toggleSelection(type_r79, ctx_r80.selectedWorkTypes, \"QUA_quiz_options\", false, \"WorkType\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"camelCase\");\n    i0.ɵɵelementStart(3, \"span\", 110);\n    i0.ɵɵtext(4, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const type_r79 = ctx.$implicit;\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r14.isItemSelected(type_r79, ctx_r14.selectedWorkTypes, \"QUA_quiz_options\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, type_r79.option_title), \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelementStart(1, \"button\", 107);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_48_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return ctx_r82.updateUserPreferences();\n    });\n    i0.ɵɵtext(2, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 108);\n    i0.ɵɵtext(4, \"Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r86 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_63_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r86);\n      const data_r84 = restoredCtx.$implicit;\n      const ctx_r85 = i0.ɵɵnextContext();\n      return ctx_r85.toggleSelection(data_r84, ctx_r85.selectedGender, \"U_genderId\", true, \"Gender\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 110);\n    i0.ɵɵtext(3, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const data_r84 = ctx.$implicit;\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r16.isItemSelectedForIdentity(data_r84, ctx_r16.selectedGender, true, \"Gender\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", data_r84.GE_title, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_71_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r89);\n      const data_r87 = restoredCtx.$implicit;\n      const ctx_r88 = i0.ɵɵnextContext();\n      return ctx_r88.toggleSelection(data_r87, ctx_r88.selectedEthnicity, \"U_ethinicityId\", true, \"Ethnicity\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 110);\n    i0.ɵɵtext(3, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const data_r87 = ctx.$implicit;\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r17.isItemSelectedForIdentity(data_r87, ctx_r17.selectedEthnicity, true, \"Ethnicity\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", data_r87.ET_title, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_79_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const data_r90 = restoredCtx.$implicit;\n      const ctx_r91 = i0.ɵɵnextContext();\n      return ctx_r91.toggleSelection(data_r90, ctx_r91.selectedReligion, \"U_religionId\", true, \"Religion\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 110);\n    i0.ɵɵtext(3, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const data_r90 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r18.isItemSelectedForIdentity(data_r90, ctx_r18.selectedReligion, true, \"Religion\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", data_r90.RE_title, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r95 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_87_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r95);\n      const data_r93 = restoredCtx.$implicit;\n      const ctx_r94 = i0.ɵɵnextContext();\n      return ctx_r94.toggleSelection(data_r93, ctx_r94.selectedSexualOrientation, \"U_sexualOrientationId\", true, \"Sexual-Orientation\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 110);\n    i0.ɵɵtext(3, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const data_r93 = ctx.$implicit;\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r19.isItemSelectedForIdentity(data_r93, ctx_r19.selectedSexualOrientation, true, \"Sexual-Orientation\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", data_r93.SO_title, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r98 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_95_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r98);\n      const type_r96 = restoredCtx.$implicit;\n      const ctx_r97 = i0.ɵɵnextContext();\n      return ctx_r97.toggleSelection(type_r96, ctx_r97.selectedDisability, \"U_isDisability\", true, \"Disability\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 110);\n    i0.ɵɵtext(3, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const type_r96 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r20.isItemSelectedForIdentity(type_r96, ctx_r20.selectedDisability, true, \"U_isDisability\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r96.AM_title, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_103_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r101 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_103_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r101);\n      const type_r99 = restoredCtx.$implicit;\n      const ctx_r100 = i0.ɵɵnextContext();\n      return ctx_r100.toggleSelection(type_r99, ctx_r100.selectedGeneration, \"U_first_generation\", true, \"Generation\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 110);\n    i0.ɵɵtext(3, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const type_r99 = ctx.$implicit;\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r21.isItemSelectedForIdentity(type_r99, ctx_r21.selectedGeneration, true, \"U_first_generation\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r99.AM_title, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_110_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r104 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_110_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r104);\n      const type_r102 = restoredCtx.$implicit;\n      const ctx_r103 = i0.ɵɵnextContext();\n      return ctx_r103.toggleSelection(type_r102, ctx_r103.selectedFreeMeal, \"U_freeMeal\", true, \"FreeSchoolMeals\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 110);\n    i0.ɵɵtext(3, \"\\u2714\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const type_r102 = ctx.$implicit;\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r22.isItemSelectedForIdentity(type_r102, ctx_r22.selectedFreeMeal, true, \"U_freeMeal\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r102.AM_title, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_117_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r106 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 111);\n    i0.ɵɵelementStart(1, \"div\", 112);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_117_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r106);\n      const ctx_r105 = i0.ɵɵnextContext();\n      let tmp_b_0;\n      return ctx_r105.removeMenu(\"U_institute\", (tmp_b_0 = ctx_r105.addNewAppUserForm.get(\"U_institute\")) == null ? null : tmp_b_0.value);\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getMenuTitle((tmp_0_0 = ctx_r23.addNewAppUserForm.get(\"U_institute\")) == null ? null : tmp_0_0.value, \"U_institute\"), \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_119_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r110 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 116);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_119_li_2_Template_li_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r110);\n      const menu_r108 = restoredCtx.$implicit;\n      const ctx_r109 = i0.ɵɵnextContext(2);\n      return ctx_r109.addMenu(menu_r108, \"U_institute\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const menu_r108 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", menu_r108.INS_title, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵelementStart(1, \"ul\");\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_119_li_2_Template, 2, 1, \"li\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r24.filteredUniversityOptions);\n  }\n}\n\nfunction EditAppUserComponent_div_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtext(1, \" No matching universities found. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_129_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r112 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 111);\n    i0.ɵɵelementStart(1, \"div\", 112);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_129_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r112);\n      const ctx_r111 = i0.ɵɵnextContext();\n      let tmp_b_0;\n      return ctx_r111.removeMenu(\"U_education\", (tmp_b_0 = ctx_r111.addNewAppUserForm.get(\"U_education\")) == null ? null : tmp_b_0.value);\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.getMenuTitle((tmp_0_0 = ctx_r26.addNewAppUserForm.get(\"U_education\")) == null ? null : tmp_0_0.value, \"U_education\"), \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_131_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r116 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 116);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_131_li_2_Template_li_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r116);\n      const menu_r114 = restoredCtx.$implicit;\n      const ctx_r115 = i0.ɵɵnextContext(2);\n      return ctx_r115.addMenu(menu_r114, \"U_education\");\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const menu_r114 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", menu_r114.ED_name, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵelementStart(1, \"ul\");\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_131_li_2_Template, 2, 1, \"li\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r27.filteredDegreeOptions);\n  }\n}\n\nfunction EditAppUserComponent_div_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117);\n    i0.ɵɵtext(1, \" No matching universities found. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_155_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r118 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 111);\n    i0.ɵɵelementStart(1, \"div\", 112);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"button\", 113);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_155_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r118);\n      const ctx_r117 = i0.ɵɵnextContext();\n      return ctx_r117.removeSelectedPostcode();\n    });\n    i0.ɵɵtext(4, \" \\u00D7 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r29.selectedPostcode, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_157_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r122 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 116);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_157_li_2_Template_li_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r122);\n      const postcode_r120 = restoredCtx.$implicit;\n      const ctx_r121 = i0.ɵɵnextContext(2);\n      return ctx_r121.selectPostcode(postcode_r120);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const postcode_r120 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", postcode_r120, \" \");\n  }\n}\n\nfunction EditAppUserComponent_div_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵelementStart(1, \"ul\");\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_157_li_2_Template, 2, 1, \"li\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r30.filteredPostcodes);\n  }\n}\n\nfunction EditAppUserComponent_div_158_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r124 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelementStart(1, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_158_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r124);\n      const ctx_r123 = i0.ɵɵnextContext();\n      return ctx_r123.createNewAppUser();\n    });\n    i0.ɵɵtext(2, \"Submit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 108);\n    i0.ɵɵtext(4, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction EditAppUserComponent_div_159_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r126 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelementStart(1, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_159_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r126);\n      const ctx_r125 = i0.ɵɵnextContext();\n      return ctx_r125.updateUserIdentity();\n    });\n    i0.ɵɵtext(2, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 108);\n    i0.ɵɵtext(4, \"Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport class EditAppUserComponent {\n  constructor(router, formBuilder, dataTransferService, toastr, activeRoute, httpClient, ngxSpinnerService, route) {\n    this.router = router;\n    this.formBuilder = formBuilder;\n    this.dataTransferService = dataTransferService;\n    this.toastr = toastr;\n    this.activeRoute = activeRoute;\n    this.httpClient = httpClient;\n    this.ngxSpinnerService = ngxSpinnerService;\n    this.route = route;\n    this.p = 1;\n    this.isReadonly = false;\n    this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\n    this.selectedActivities = [];\n    this.selectedSectors = [];\n    this.selectedWorkTypes = [];\n    this.selectedGender = [];\n    this.selectedEthnicity = [];\n    this.selectedReligion = [];\n    this.selectedDisability = [];\n    this.selectedGeneration = [];\n    this.selectedFreeMeal = [];\n    this.selectedSexualOrientation = [];\n    this.ActivitiesYouLike = [];\n    this.WorkType = [];\n    this.LikedSectors = [];\n    this.Genders = [];\n    this.Ethnicities = [];\n    this.Religions = [];\n    this.SexualOrientation = [];\n    this.Degree = [];\n    this.University = [];\n    this.filteredUniversityOptions = [];\n    this.filteredDegreeOptions = [];\n    this.filteredPostcodes = [];\n    this.showDropdown = false; // Controls visibility of the dropdown list\n\n    this.showPostcodeDropdown = false;\n    this.showPassword = false;\n    this.hasADisability = [{\n      QUO_id: '1',\n      AM_title: 'Yes'\n    }, {\n      QUO_id: '2',\n      AM_title: 'No'\n    }, {\n      QUO_id: '3',\n      AM_title: 'Prefer Not To say'\n    }, {\n      QUO_id: '4',\n      AM_title: 'I Am Not Sure'\n    }];\n    this.FreeMeal = [{\n      QUO_id: '1',\n      AM_title: 'Yes'\n    }, {\n      QUO_id: '2',\n      AM_title: 'No'\n    }, {\n      QUO_id: '3',\n      AM_title: 'Prefer Not To say'\n    } // {\n    //   QUO_id: '4',\n    //   AM_title: 'I Am Not Sure',\n    // },\n    ];\n    this.isThisGeneration = [{\n      QUO_id: '1',\n      AM_title: 'Yes'\n    }, {\n      QUO_id: '2',\n      AM_title: 'No'\n    }, {\n      QUO_id: '3',\n      AM_title: 'Prefer Not To say'\n    } // {\n    //   QUO_id: '4',\n    //   AM_title: 'I Am Not Sure',\n    // },\n    ];\n    this.imgChangeEvt = \"\";\n    this.aliasImgChangeEvt = \"\";\n    this.isCropperVisible = false;\n    this.isAliasCropperVisible = false;\n    this.addNewAppUserForm = this.formBuilder.group({\n      U_name: ['', Validators.required],\n      U_email: ['', [Validators.required, FileValidator.strictEmailValidator]],\n      U_password: ['', [Validators.required, Validators.minLength(8), Validators.pattern(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])[A-Za-z\\d!@#$%^&*(),.?\":{}|<>]{8,}$/)]],\n      U_profilePercentage: ['0'],\n      U_ethinicityId: ['', Validators.required],\n      U_ethinicity_Toggle: [false],\n      U_genderId: ['', Validators.required],\n      U_gender_Toggle: [false],\n      U_isDisability: ['', Validators.required],\n      U_disability_Toggle: [false],\n      U_religionId: ['', Validators.required],\n      U_religion_Toggle: [false],\n      U_sexualOrientationId: ['', Validators.required],\n      U_sexuality_Toggle: [false],\n      U_institute: [''],\n      U_postcode: [''],\n      U_postcode_Toggle: [false],\n      U_first_generation: ['', Validators.required],\n      U_first_generation_Toggle: [false],\n      U_dp: [null],\n      U_freeMeal: ['', Validators.required],\n      U_freeMeal_Toggle: [false],\n      U_education: [''],\n      QUA_quiz_options: this.formBuilder.array([]),\n      LI_contentId: this.formBuilder.array([], Validators.required),\n      U_isExpert: [false],\n      U_profileAnonymous: [false],\n      U_aliasName: [''],\n      U_aliasDp: ['']\n    });\n    this.route.queryParams.subscribe(params => {\n      if (params) {\n        this.U_id = params['U_id'];\n        this.title = params['title'];\n        console.log(this.U_id);\n      } else {\n        this.router.navigate(['/actions/app-users']);\n      }\n    });\n  }\n\n  ngOnInit() {\n    if (this.title === 'Edit') {\n      this.getUserDetailsById(this.U_id).then(res => {\n        this.patchFormData(res);\n      }).catch(error => {\n        console.error(\"Error in fetching user details:\", error);\n      });\n    }\n\n    this.getAllPreferencesData();\n\n    if (this.title !== 'Edit') {\n      this.getIdentityData();\n      this.getindustry();\n      this.getAllUniversity();\n    }\n\n    this.universitySearchControl = new FormControl('');\n    this.degreeSearchControl = new FormControl('');\n    this.postcodeSearchControl = new FormControl('');\n    this.getAllDegree();\n    this.postcodeSearchControl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => this.fetchPostcodes(value))).subscribe(postcodes => {\n      this.filteredPostcodes = postcodes;\n      this.showPostcodeDropdown = postcodes.length > 0;\n    });\n\n    if (!this.addNewAppUserForm.get('U_postcode')) {\n      this.addNewAppUserForm.addControl('U_postcode', new FormControl('', Validators.required));\n    }\n  }\n\n  getUserDetailsById(U_id) {\n    return new Promise((resolve, reject) => {\n      this.dataTransferService.getUserDetailsById(U_id).subscribe(res => {\n        if (res.data) {\n          this.userData = res.data;\n          console.log(\"this.userData\", this.userData);\n          resolve(this.userData); // Resolve with the userData\n        } else {\n          reject('No data found'); // Reject if no data found\n        }\n      }, error => {\n        this.ngxSpinnerService.hide('globalSpinner');\n        this.toastr.error(\"Unable to fetch data.\");\n        console.log(\"Error\", error);\n        reject(error);\n      });\n    });\n  }\n\n  patchFormData(data) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this.getindustry();\n        yield _this.getIdentityData();\n        yield _this.getAllUniversity();\n\n        _this.addNewAppUserForm.patchValue({\n          U_name: data.U_name,\n          U_email: data.U_email,\n          U_password: data.U_password,\n          U_profilePercentage: data.U_profilePercentage,\n          U_ethinicity_Toggle: data.U_ethinicity_Toggle,\n          U_gender_Toggle: data.U_gender_Toggle,\n          U_disability_Toggle: data.U_disability_Toggle,\n          U_religion_Toggle: data.U_religion_Toggle,\n          U_sexuality_Toggle: data.U_sexuality_Toggle,\n          U_institute: data.U_institute,\n          U_postcode: data === null || data === void 0 ? void 0 : data.U_postcode,\n          U_postcode_Toggle: data.U_postcode_Toggle ? data.U_postcode_Toggle : false,\n          // U_dp: data.U_dp,\n          U_education: data.U_education,\n          U_genderId: data.U_genderId,\n          U_ethinicityId: data.U_ethinicityId,\n          U_religionId: data.U_religionId,\n          U_sexualOrientationId: data.U_sexualOrientationId,\n          U_isDisability: data.U_isDisability,\n          U_first_generation: data.U_first_generation,\n          U_first_generation_Toggle: data.U_first_generation_Toggle,\n          U_freeMeal: data.U_freeMeal,\n          U_freeMeal_Toggle: data.U_freeMeal_Toggle ? data.U_freeMeal_Toggle : false,\n          U_profileAnonymous: data === null || data === void 0 ? void 0 : data.U_profileAnonymous,\n          U_aliasName: data === null || data === void 0 ? void 0 : data.U_aliasName\n        });\n\n        if (data.U_dp) {\n          _this.imageSrc = data.U_dp;\n        }\n\n        if (data.U_aliasDp) {\n          _this.aliasImageSrc = data.U_aliasDp;\n        }\n\n        if (data.U_institute) {\n          _this.addMenu(data.U_institute, 'U_institute', 'toPatchExisting');\n        }\n\n        if (data.U_education) {\n          _this.addMenu(data.U_education, 'U_education', 'toPatchExisting');\n        }\n\n        if (data.U_postcode) {\n          _this.selectPostcode(data.U_postcode);\n        }\n\n        _this.selectedSectors = _this.LikedSectors.filter(sector => data === null || data === void 0 ? void 0 : data.LI_contentId.includes(sector.IN_id)); // Update the form with the selected sectors\n\n        _this.updateFormArray('LI_contentId', _this.selectedSectors, false); // Patch the QUA_quiz_options for Activities and Work Types\n\n\n        _this.selectedActivities = data === null || data === void 0 ? void 0 : data.QUA_quiz_options.filter(option => (option === null || option === void 0 ? void 0 : option.quiz_id) === '5');\n        _this.selectedWorkTypes = data === null || data === void 0 ? void 0 : data.QUA_quiz_options.filter(option => (option === null || option === void 0 ? void 0 : option.quiz_id) === '2'); // Call method to update the FormArray in the form\n\n        _this.updateFormArray('QUA_quiz_options', _this.selectedActivities.concat(_this.selectedWorkTypes), false); // Update Identity Data\n\n\n        const selectedGender = _this.Genders.find(gender => (gender === null || gender === void 0 ? void 0 : gender.GE_id) === data.U_genderId);\n\n        _this.selectedGender.push(selectedGender);\n\n        const selectedEthnicity = _this.Ethnicities.find(ethnicity => (ethnicity === null || ethnicity === void 0 ? void 0 : ethnicity.ET_id) === (data === null || data === void 0 ? void 0 : data.U_ethinicityId));\n\n        _this.selectedEthnicity.push(selectedEthnicity);\n\n        const selectedReligion = _this.Religions.find(religion => (religion === null || religion === void 0 ? void 0 : religion.RE_id) === (data === null || data === void 0 ? void 0 : data.U_religionId));\n\n        _this.selectedReligion.push(selectedReligion);\n\n        const selectedSexualOrientation = _this.SexualOrientation.find(orientation => (orientation === null || orientation === void 0 ? void 0 : orientation.SO_id) === (data === null || data === void 0 ? void 0 : data.U_sexualOrientationId));\n\n        _this.selectedSexualOrientation.push(selectedSexualOrientation);\n\n        const selectedDisability = _this.hasADisability.find(disability => (disability === null || disability === void 0 ? void 0 : disability.QUO_id) === (data === null || data === void 0 ? void 0 : data.U_isDisability));\n\n        _this.selectedDisability.push(selectedDisability);\n\n        const selectedGeneration = _this.isThisGeneration.find(generation => (generation === null || generation === void 0 ? void 0 : generation.QUO_id) === (data === null || data === void 0 ? void 0 : data.U_first_generation));\n\n        _this.selectedGeneration.push(selectedGeneration);\n\n        const selectedFreeMeal = _this.FreeMeal.find(meal => (meal === null || meal === void 0 ? void 0 : meal.QUO_id) === (data === null || data === void 0 ? void 0 : data.U_freeMeal));\n\n        _this.selectedFreeMeal.push(selectedFreeMeal);\n      } catch (error) {\n        console.error('Error loading data:', error);\n      }\n\n      _this.calculateProfilePercentage();\n\n      console.log(\"Patched data\", _this.addNewAppUserForm.value);\n    })();\n  }\n\n  markAllFieldsAsTouched() {\n    Object.keys(this.addNewAppUserForm.controls).forEach(field => {\n      const control = this.addNewAppUserForm.get(field);\n      control === null || control === void 0 ? void 0 : control.markAsTouched({\n        onlySelf: true\n      });\n    });\n  }\n\n  calculateProfilePercentage() {\n    var _a, _b, _c, _d;\n\n    let percentage = 0;\n    const fields = [this.selectedGender, this.selectedEthnicity, this.selectedReligion, this.selectedSexualOrientation, this.selectedDisability, this.selectedGeneration, this.selectedFreeMeal, this.selectedPostcode ? [this.selectedPostcode] : [], ((_a = this.addNewAppUserForm.get('U_institute')) === null || _a === void 0 ? void 0 : _a.value) ? [(_b = this.addNewAppUserForm.get('U_institute')) === null || _b === void 0 ? void 0 : _b.value] : [], ((_c = this.addNewAppUserForm.get('U_education')) === null || _c === void 0 ? void 0 : _c.value) ? [(_d = this.addNewAppUserForm.get('U_education')) === null || _d === void 0 ? void 0 : _d.value] : []];\n    fields.forEach(field => {\n      if (field.length > 0) {\n        percentage += 10;\n      }\n    }); // Convert percentage to string and patch value\n\n    this.addNewAppUserForm.patchValue({\n      U_profilePercentage: percentage.toString()\n    });\n    console.log('this.addNewAppUserForm.value.U_profilePercentage:', this.addNewAppUserForm.value.U_profilePercentage);\n  }\n\n  togglePasswordVisibility(icon, controlName) {\n    const input = document.getElementById('U_password'); // const input = this.el.nativeElement.querySelector(`#${controlName}`) as HTMLInputElement;\n\n    const isPasswordType = input.type === 'password';\n    input.type = isPasswordType ? 'text' : 'password';\n    icon.classList.toggle('fa-eye-slash', isPasswordType);\n    icon.classList.toggle('fa-eye', !isPasswordType);\n  }\n\n  onFileSelected(event, field) {\n    var _a, _b;\n\n    const selectedFile = event.target.files[0];\n    console.log(\"selectedFile\", selectedFile); // Function to add a timestamp to the file name\n    // const addTimestamp = (fileName: string) => {\n    //   const currentTimestamp = new Date().getTime();\n    //   const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');\n    //   const extension = fileName.split('.').pop();\n    //   return `${nameWithoutExtension}_${currentTimestamp}.${extension}`;\n    // };\n\n    if (!selectedFile) {\n      if (field === 'U_dp') {\n        this.imageName = null;\n        this.imageSrc = null;\n        const fileControl = this.addNewAppUserForm.get('U_dp');\n        fileControl === null || fileControl === void 0 ? void 0 : fileControl.clearValidators();\n        fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\n      } else if (field === 'U_aliasDp') {\n        this.aliasImageName = null;\n        this.aliasImageSrc = null;\n        const aliasFileControl = this.addNewAppUserForm.get('U_aliasDp');\n        aliasFileControl === null || aliasFileControl === void 0 ? void 0 : aliasFileControl.clearValidators();\n        aliasFileControl === null || aliasFileControl === void 0 ? void 0 : aliasFileControl.updateValueAndValidity();\n      }\n\n      return;\n    }\n\n    if (field === 'U_dp' && selectedFile) {\n      // Append timestamp to image name\n      const newFileName = FileValidator.addTimestamp(selectedFile.name);\n      this.imageName = new File([selectedFile], newFileName, {\n        type: selectedFile.type\n      });\n      console.log(\"imageName\", this.imageName);\n      const fileControl = this.addNewAppUserForm.get('U_dp');\n      fileControl === null || fileControl === void 0 ? void 0 : fileControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\n      fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\n      const fileType = this.imageName.type.split('/')[0];\n      const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n\n      if (fileType !== 'image' || fileExtension === 'svg') {\n        event.target.value = '';\n        this.toastr.info('Please select an image file (excluding SVG).');\n        this.imageName = null;\n        this.imageSrc = null;\n        return;\n      }\n\n      if (this.imageName && fileType === 'image') {\n        const reader = new FileReader();\n        const img = new Image();\n\n        reader.onload = e => {\n          var _a, _b, _c;\n\n          this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\n\n          if (!((_c = (_b = this.addNewAppUserForm.get('U_dp')) === null || _b === void 0 ? void 0 : _b.errors) === null || _c === void 0 ? void 0 : _c.fileSizeValidator)) {\n            this.checkAspectRatio(img, 'U_dp');\n            this.imgChangeEvt = {\n              target: {\n                files: [this.imageName]\n              }\n            };\n          }\n        };\n\n        reader.readAsDataURL(this.imageName);\n      }\n    }\n\n    if (field === 'U_aliasDp' && selectedFile) {\n      // Append timestamp to alias image name\n      const newFileName = FileValidator.addTimestamp(selectedFile.name);\n      this.aliasImageName = new File([selectedFile], newFileName, {\n        type: selectedFile.type\n      });\n      const aliasFileControl = this.addNewAppUserForm.get('U_aliasDp');\n      aliasFileControl === null || aliasFileControl === void 0 ? void 0 : aliasFileControl.setValidators(FileValidator.fileSizeValidator(2048, this.aliasImageName));\n      aliasFileControl === null || aliasFileControl === void 0 ? void 0 : aliasFileControl.updateValueAndValidity();\n      const aliasFileType = this.aliasImageName.type.split('/')[0];\n      const aliasFileExtension = (_b = this.aliasImageName.name.split('.').pop()) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n\n      if (aliasFileType !== 'image' || aliasFileExtension === 'svg') {\n        event.target.value = '';\n        this.toastr.info('Please select an image file (excluding SVG).');\n        this.aliasImageName = null;\n        this.aliasImageSrc = null;\n        return;\n      }\n\n      if (this.aliasImageName && aliasFileType === 'image') {\n        const reader = new FileReader();\n        const img = new Image();\n\n        reader.onload = e => {\n          var _a, _b, _c;\n\n          this.aliasImageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\n\n          if (!((_c = (_b = this.addNewAppUserForm.get('U_aliasDp')) === null || _b === void 0 ? void 0 : _b.errors) === null || _c === void 0 ? void 0 : _c.fileSizeValidator)) {\n            this.checkAspectRatio(img, 'U_aliasDp');\n            this.aliasImgChangeEvt = {\n              target: {\n                files: [this.aliasImageName]\n              }\n            };\n          }\n        };\n\n        reader.readAsDataURL(this.aliasImageName);\n      }\n    }\n  }\n\n  checkAspectRatio(image, controlName) {\n    const aspectRatio = image.width / image.height;\n    const control = this.addNewAppUserForm.get(controlName);\n\n    if (aspectRatio !== 1) {\n      // this.toastr.warning('The image must have a 1:1 aspect ratio. Please select a square image.', 'Invalid Aspect Ratio');\n      control === null || control === void 0 ? void 0 : control.setErrors({\n        fileAspectRatioValidator: true\n      });\n    } else {\n      control === null || control === void 0 ? void 0 : control.setErrors(null);\n    }\n  }\n\n  dataURItoBlob(dataURI) {\n    const byteString = atob(dataURI.split(',')[1]);\n    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];\n    const arrayBuffer = new ArrayBuffer(byteString.length);\n    const uint8Array = new Uint8Array(arrayBuffer);\n\n    for (let i = 0; i < byteString.length; i++) {\n      uint8Array[i] = byteString.charCodeAt(i);\n    }\n\n    return new Blob([uint8Array], {\n      type: mimeString\n    });\n  }\n\n  cropImg(e, controlName) {\n    if (controlName == 'U_dp') {\n      this.imageSrc = e.base64;\n    } else if (controlName == 'U_aliasDp') {\n      this.aliasImageSrc = e.base64;\n    }\n  }\n\n  showCropper(controlName) {\n    if (controlName == 'U_dp') {\n      this.isCropperVisible = true;\n    } else if (controlName == 'U_aliasDp') {\n      this.isAliasCropperVisible = true;\n    }\n  }\n\n  hideCropper(controlName) {\n    if (controlName == 'U_dp') {\n      this.isCropperVisible = false;\n    } else if (controlName == 'U_aliasDp') {\n      this.isAliasCropperVisible = false;\n    }\n  }\n\n  saveCroppedImage(controlName) {\n    const addTimestamp = fileName => {\n      const currentTimestamp = new Date().getTime(); // Split the filename into name and extension\n\n      const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');\n      const extension = fileName.split('.').pop(); // If the name already has a timestamp, remove it (assuming it's the last segment separated by '_')\n\n      const cleanedName = nameWithoutExtension.replace(/_\\d{13}$/, ''); // Append the new timestamp to the name\n\n      return `${cleanedName}_${currentTimestamp}.${extension}`;\n    };\n\n    if (controlName == 'U_dp') {\n      if (this.imageSrc && this.imageName) {\n        // Convert cropped image to Blob\n        const blob = this.dataURItoBlob(this.imageSrc); // Add or replace timestamp in image name\n\n        const newFileName = addTimestamp(this.imageName.name);\n        this.imageName = new File([blob], newFileName, {\n          type: this.imageName.type\n        });\n        console.log(\"Cropped Image\", this.imageName); // Update imgChangeEvt with the new cropped image\n\n        this.imgChangeEvt = {\n          target: {\n            files: [this.imageName]\n          }\n        };\n        const fileControl = this.addNewAppUserForm.get('U_dp');\n        fileControl === null || fileControl === void 0 ? void 0 : fileControl.clearValidators();\n        fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\n        this.hideCropper('U_dp');\n      }\n    } else if (controlName == 'U_aliasDp') {\n      if (this.aliasImageSrc && this.aliasImageName) {\n        // Convert cropped image to Blob\n        const blob = this.dataURItoBlob(this.aliasImageSrc); // Add or replace timestamp in alias image name\n\n        const newFileName = addTimestamp(this.aliasImageName.name);\n        this.aliasImageName = new File([blob], newFileName, {\n          type: this.aliasImageName.type\n        });\n        console.log(\"Cropped Alias Image\", this.aliasImageName); // Update aliasImgChangeEvt with the new cropped image\n\n        this.aliasImgChangeEvt = {\n          target: {\n            files: [this.aliasImageName]\n          }\n        };\n        const fileControl = this.addNewAppUserForm.get('U_aliasDp');\n        fileControl === null || fileControl === void 0 ? void 0 : fileControl.clearValidators();\n        fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\n        this.hideCropper('U_aliasDp');\n      }\n    }\n  }\n\n  initCropper() {}\n\n  imgLoad() {}\n\n  imgFailed() {\n    this.toastr.error(\"Image Failed to show\");\n  }\n\n  anonymousCheck() {\n    var _a, _b, _c, _d, _e;\n\n    if (!((_a = this.addNewAppUserForm.get('U_profileAnonymous')) === null || _a === void 0 ? void 0 : _a.value)) {\n      this.aliasImageName = null;\n      this.aliasImageSrc = null;\n      (_b = this.addNewAppUserForm.get('U_aliasName')) === null || _b === void 0 ? void 0 : _b.clearValidators();\n      (_c = this.addNewAppUserForm.get('U_aliasName')) === null || _c === void 0 ? void 0 : _c.updateValueAndValidity();\n      (_d = this.addNewAppUserForm.get('U_aliasDp')) === null || _d === void 0 ? void 0 : _d.clearValidators();\n      (_e = this.addNewAppUserForm.get('U_aliasDp')) === null || _e === void 0 ? void 0 : _e.updateValueAndValidity();\n    }\n  } // uploadLogoUrl() {\n  //   //To upload logo from add new company form\n  //   if (!this.imageName) {\n  //     // this.toastr.error('Please select an image.');\n  //     return;\n  //   }\n  //   console.log('image', this.imageName);\n  //   this.dataTransferService\n  //     .uploadurl(this.imageName)\n  //     .subscribe((res: any) => {});\n  // }\n  // removeMenu(index: number, formControlName: string) {\n  //   const formArray = this.addNewAppUserForm.get(formControlName) as FormArray;\n  //   formArray.removeAt(index);\n  // }\n  // onBackspaceKey(event: KeyboardEvent, formControlName: string) {\n  //   if (event.key === 'Backspace') {\n  //     const inputElement = event.target as HTMLInputElement;\n  //     if (inputElement.value === '' && !event.shiftKey) {\n  //       event.preventDefault();\n  //       this.removeLastMenu(formControlName);\n  //     }\n  //   }\n  // }\n  // removeLastMenu(formArrayName: string) {\n  //   const formArray = this.addNewAppUserForm.get(formArrayName) as FormArray;\n  //   if (formArray.length > 0) {\n  //     formArray.removeAt(formArray.length - 1);\n  //   }\n  // }\n  // addMenu(event: any, formArrayName: string) {\n  //   const menuId = event.target.value;\n  //   console.log(\"Selected menuId:\", menuId); // Debugging line\n  //   if (menuId.trim() !== '') {\n  //     const selectedMenu = this.University.find((menu: any) => menu.INS_id === menuId);\n  //     console.log(\"Selected Menu:\", selectedMenu); // Debugging line\n  //     if (selectedMenu) {\n  //       const formArray = this.addNewAppUserForm.get(formArrayName) as FormArray;\n  //       const alreadyAdded = formArray.value.some((item: any) => item.INS_id === selectedMenu.INS_id);\n  //       if (!alreadyAdded) {\n  //         formArray.push(new FormControl({ INS_id: selectedMenu.INS_id, INS_title: selectedMenu.INS_title }));\n  //       }\n  //       event.target.value = '';\n  //     }\n  //   }\n  // }\n  // getFormArrayControls(formArrayName: string): FormControl[] {\n  //   return (this.addNewAppUserForm.get(formArrayName) as FormArray).controls as FormControl[];\n  // }\n  // getMenuTitle(id: any): string {\n  //   const menu = this.University.find((menu: any) => menu.INS_id === id);\n  //   return menu ? menu.INS_title : 'Unknown';\n  // }\n\n\n  isItemSingleSelected(item, selectedArray, singleSelect = false) {\n    if (singleSelect) {\n      return selectedArray.length > 0 && selectedArray[0].AM_id === item.AM_id;\n    }\n\n    return selectedArray.some(selected => selected.AM_id === item.AM_id);\n  }\n\n  isItemSelected(item, selectedArray, formControlName) {\n    if (!selectedArray) {\n      return false;\n    }\n\n    if (formControlName === 'LI_contentId') {\n      return selectedArray.some(selected => selected.IN_id === item.IN_id);\n    }\n\n    return selectedArray.some(selected => selected.option_id === item.option_id);\n  }\n\n  isItemSelectedForIdentity(item, selectedArray, singleSelect = false, type) {\n    var _a, _b, _c, _d, _e, _f, _g;\n\n    if (!selectedArray) {\n      console.log('item', item);\n      return false;\n    }\n\n    if (singleSelect) {\n      switch (type) {\n        case 'Gender':\n          return selectedArray.length > 0 && ((_a = selectedArray[0]) === null || _a === void 0 ? void 0 : _a.GE_id) === (item === null || item === void 0 ? void 0 : item.GE_id);\n          break;\n\n        case 'Ethnicity':\n          return selectedArray.length > 0 && ((_b = selectedArray[0]) === null || _b === void 0 ? void 0 : _b.ET_id) === (item === null || item === void 0 ? void 0 : item.ET_id);\n          break;\n\n        case 'Religion':\n          return selectedArray.length > 0 && ((_c = selectedArray[0]) === null || _c === void 0 ? void 0 : _c.RE_id) === (item === null || item === void 0 ? void 0 : item.RE_id);\n          break;\n\n        case 'Sexual-Orientation':\n          return selectedArray.length > 0 && ((_d = selectedArray[0]) === null || _d === void 0 ? void 0 : _d.SO_id) === (item === null || item === void 0 ? void 0 : item.SO_id);\n          break;\n\n        case 'U_isDisability':\n          return selectedArray.length > 0 && ((_e = selectedArray[0]) === null || _e === void 0 ? void 0 : _e.QUO_id) === (item === null || item === void 0 ? void 0 : item.QUO_id);\n          break;\n\n        case 'U_first_generation':\n          return selectedArray.length > 0 && ((_f = selectedArray[0]) === null || _f === void 0 ? void 0 : _f.QUO_id) === (item === null || item === void 0 ? void 0 : item.QUO_id);\n          break;\n\n        case 'U_freeMeal':\n          return selectedArray.length > 0 && ((_g = selectedArray[0]) === null || _g === void 0 ? void 0 : _g.QUO_id) === (item === null || item === void 0 ? void 0 : item.QUO_id);\n          break;\n      }\n    }\n\n    return selectedArray.some(selected => (selected === null || selected === void 0 ? void 0 : selected.QUO_id) === (item === null || item === void 0 ? void 0 : item.QUO_id));\n  }\n\n  toggleSelection(item, selectedArray, formControlName, singleSelect = false, type) {\n    const identifier = formControlName === 'LI_contentId' ? 'IN_id' : 'option_id';\n\n    if (singleSelect) {\n      if (this.isItemSelectedForIdentity(item, selectedArray, true, type)) {\n        selectedArray.splice(0, 1);\n      } else {\n        selectedArray.splice(0, selectedArray.length);\n        selectedArray.push(item);\n      }\n\n      this.calculateProfilePercentage();\n    } else {\n      const index = selectedArray.findIndex(selected => (selected === null || selected === void 0 ? void 0 : selected[identifier]) === item[identifier]);\n\n      if (index !== -1) {\n        selectedArray.splice(index, 1);\n      } else {\n        selectedArray.push(item);\n      }\n    }\n\n    this.updateFormArray(formControlName, selectedArray, singleSelect);\n  }\n\n  updateFormArray(controlName, selectedArray, singleSelect) {\n    const selectedItem = selectedArray.length > 0 ? selectedArray[0] : null;\n\n    if (singleSelect) {\n      const singleSelectFields = {\n        U_genderId: 'GE_id',\n        U_ethinicityId: 'ET_id',\n        U_religionId: 'RE_id',\n        U_sexualOrientationId: 'SO_id',\n        U_isDisability: 'QUO_id',\n        U_first_generation: 'QUO_id',\n        U_freeMeal: 'QUO_id'\n      };\n\n      if (controlName in singleSelectFields) {\n        this.addNewAppUserForm.setControl(controlName, new FormControl(selectedItem ? selectedItem[singleSelectFields[controlName]] : null));\n      } else {\n        console.warn(`Unexpected control name: ${controlName}`);\n      }\n    } else {\n      if (controlName === 'LI_contentId') {\n        const formArray = this.addNewAppUserForm.get(controlName);\n        formArray.clear(); // Clear existing controls\n\n        selectedArray.forEach(item => {\n          formArray.push(new FormControl(item.IN_id)); // Add new controls\n        });\n      } else {\n        const mergedArray = [...this.selectedActivities, ...this.selectedWorkTypes];\n        const formArray = this.formBuilder.array(mergedArray.map(item => new FormControl({\n          option_id: item.option_id,\n          option_title: item.option_title,\n          quiz_id: item.quiz_id\n        })));\n        this.addNewAppUserForm.setControl(controlName, formArray);\n      }\n    }\n  }\n\n  getAllPreferencesData() {\n    this.dataTransferService.getAllPreferencesData().subscribe(res => {\n      if (res.statusCode == 200 && res.data) {\n        res.data.forEach(quiz => {\n          if (quiz.quiz_options && quiz.quiz_options.length > 0) {\n            this.populateReferencesData(quiz.quiz_options);\n          } else {\n            this.ngxSpinnerService.hide('globalSpinner');\n            console.error('No Preferences Data available:', quiz.QU_id);\n          }\n        });\n      } else {\n        console.error('Failed to retrieve Preference sData or data is empty. Status code:', res.statusCode);\n      }\n    }, error => {\n      console.error('Error while fetching quiz data:', error);\n    });\n  }\n\n  populateReferencesData(options) {\n    options.forEach(item => {\n      const formattedItem = {\n        option_id: item.QUO_id,\n        option_title: item.QUO_title,\n        quiz_id: item.QUO_quizid\n      };\n\n      switch (item.QUO_quizid) {\n        case '5':\n          this.ActivitiesYouLike.push(formattedItem);\n          break;\n        // case '4':\n        //   this.LikedSectors.push(formattedItem);\n        //   break;\n\n        case '2':\n          this.WorkType.push(formattedItem);\n          break;\n      }\n    });\n  }\n\n  getIdentityData() {\n    this.ngxSpinnerService.show('globalSpinner');\n    return new Promise((resolve, reject) => {\n      this.dataTransferService.getIdentityData().subscribe(res => {\n        if (res.statusCode === 200) {\n          // this.ngxSpinnerService.hide('globalSpinner');\n          this.Ethnicities = res.ethnicities;\n          this.Genders = res.genders;\n          this.Religions = res.religion;\n          this.SexualOrientation = res.sexual_orientation;\n          resolve();\n        } else {\n          this.ngxSpinnerService.hide('globalSpinner');\n          console.error('Failed to retrieve identity data. Status code:', res.statusCode);\n          reject('Failed to retrieve identity data');\n        }\n      }, error => {\n        this.ngxSpinnerService.hide('globalSpinner');\n        console.error('Error while fetching identity data:', error);\n        reject(error);\n      });\n    });\n  }\n\n  getAllUniversity() {\n    this.ngxSpinnerService.show('globalSpinner');\n    this.dataTransferService.getAllUniversity().subscribe(res => {\n      if (res.statusCode == 200) {\n        this.University = res.data.sort((a, b) => a.INS_title.localeCompare(b.INS_title));\n        console.log('this.University', this.University);\n        this.filteredUniversityOptions = this.University;\n        this.ngxSpinnerService.hide('globalSpinner');\n      } else {\n        this.ngxSpinnerService.hide('globalSpinner');\n        console.error('Failed to retrieve University Data or data is empty. Status code:', res.statusCode);\n      }\n    }, error => {\n      this.ngxSpinnerService.hide('globalSpinner');\n      console.error('Error while fetching University data:', error);\n    });\n  }\n\n  getindustry() {\n    return new Promise((resolve, reject) => {\n      this.dataTransferService.getIndustryData().subscribe(res => {\n        if (res.status === 200) {\n          this.LikedSectors = res.data.sort((a, b) => a.IN_name.localeCompare(b.IN_name));\n          console.log('this.LikedSectors', this.LikedSectors);\n          resolve(this.LikedSectors);\n        } else {\n          console.error('Failed to fetch industry data:', res);\n          reject('Failed to fetch industry data');\n        }\n      }, error => {\n        console.error('Error fetching industry data:', error);\n        reject(error);\n      });\n    });\n  }\n\n  getAllDegree() {\n    this.ngxSpinnerService.show('globalSpinner');\n    this.dataTransferService.getAllDegree().subscribe(res => {\n      if (res.statusCode == 200) {\n        this.ngxSpinnerService.hide('globalSpinner');\n        this.Degree = res.data.sort((a, b) => a.ED_name.localeCompare(b.ED_name));\n        console.log('this.Degree', this.Degree);\n        this.filteredDegreeOptions = this.Degree;\n      } else {\n        this.ngxSpinnerService.hide('globalSpinner');\n        console.error('Failed to retrieve Degree Data or data is empty. Status code:', res.statusCode);\n      }\n    }, error => {\n      this.ngxSpinnerService.hide('globalSpinner');\n      console.error('Error while fetching University data:', error);\n    });\n  }\n\n  filterOptions(formControlName) {\n    if (formControlName === 'U_institute') {\n      const searchTerm = this.universitySearchControl.value.toLowerCase().trim();\n      this.filteredUniversityOptions = this.University.filter(option => option.INS_title.toLowerCase().includes(searchTerm));\n    } else if (formControlName === 'U_education') {\n      const searchTerm = this.degreeSearchControl.value.toLowerCase().trim();\n      this.filteredDegreeOptions = this.Degree.filter(option => option.ED_name.toLowerCase().includes(searchTerm));\n    }\n  }\n\n  removeMenu(formControlName, id) {\n    const formControl = this.addNewAppUserForm.get(formControlName);\n\n    if (formControl) {\n      formControl.setValue(null); // Clear the form control's value\n\n      if (formControlName === 'U_institute') {\n        this.selectedUniversity = null;\n        this.showTag = false;\n      } else if (formControlName === 'U_education') {\n        this.selectedDegree = null;\n        this.showTagOfEducation = false;\n      }\n    } else {\n      console.error('Form control not found:', formControlName);\n    }\n  }\n\n  addMenu(menu, formArrayName, title) {\n    const formControl = this.addNewAppUserForm.get(formArrayName);\n\n    if (formControl) {\n      if (formArrayName === 'U_institute') {\n        if (title === 'toPatchExisting') {\n          formControl.setValue(menu);\n        } else {\n          formControl.setValue(menu.INS_id);\n        }\n\n        this.selectedUniversity = menu;\n        this.universitySearchControl.setValue('');\n        this.filteredUniversityOptions = this.University;\n        this.showTag = true;\n        this.showDropdown = false; // Hide dropdown after selection // Assuming menu.INS_id is the ID you want to store\n      } else if (formArrayName === 'U_education') {\n        if (title === 'toPatchExisting') {\n          formControl.setValue(menu);\n        } else {\n          formControl.setValue(menu.ED_id);\n        }\n\n        this.selectedDegree = menu;\n        this.degreeSearchControl.setValue('');\n        this.filteredDegreeOptions = this.Degree;\n        this.showTagOfEducation = true;\n        this.showDropdownOfEducation = false;\n        console.log('degree form ', this.addNewAppUserForm.value); // Assuming menu.ED_id is the ID you want to store\n      }\n    }\n\n    this.calculateProfilePercentage();\n  }\n\n  getFormArrayValues(formArrayName) {\n    const formArray = this.addNewAppUserForm.get(formArrayName);\n    return formArray ? formArray.value : [];\n  }\n\n  getMenuTitle(id, formArrayName) {\n    if (formArrayName === 'U_institute') {\n      const menu = this.University.find(menu => (menu === null || menu === void 0 ? void 0 : menu.INS_id) === id);\n      return (menu === null || menu === void 0 ? void 0 : menu.INS_title) ? menu === null || menu === void 0 ? void 0 : menu.INS_title : 'Unknown';\n    }\n\n    if (formArrayName === 'U_education') {\n      const menu = this.Degree.find(menu => (menu === null || menu === void 0 ? void 0 : menu.ED_id) === id);\n      return (menu === null || menu === void 0 ? void 0 : menu.ED_name) ? menu === null || menu === void 0 ? void 0 : menu.ED_name : 'Unknown';\n    }\n  }\n\n  onClickOutside(event) {\n    if (!event.target.closest('.autocomplete-container-education')) {\n      this.showDropdownOfEducation = false; // Hide education dropdown if clicked outside\n    }\n\n    if (!event.target.closest('.autocomplete-container-institute')) {\n      this.showDropdown = false;\n    } // Add similar checks for other dropdowns as needed\n\n  }\n\n  fetchPostcodes(query) {\n    if (query.length > 0) {\n      return this.httpClient.get(`https://api.postcodes.io/postcodes/${query}/autocomplete`).pipe(map(response => response.result || []), catchError(() => of([])) // Handle errors and return an empty array\n      );\n    } else {\n      return of([]); // Return an observable of an empty array\n    }\n  }\n\n  filterPostcodes() {\n    const query = this.postcodeSearchControl.value;\n\n    if (query && query.length > 2) {\n      this.fetchPostcodes(query).subscribe(postcodes => {\n        this.filteredPostcodes = postcodes;\n        this.showPostcodeDropdown = postcodes.length > 0;\n      });\n    } else {\n      this.filteredPostcodes = [];\n      this.showPostcodeDropdown = false;\n    }\n  }\n\n  selectPostcode(postcode) {\n    this.selectedPostcode = postcode;\n    this.postcodeSearchControl.setValue('');\n    this.filteredPostcodes = [];\n    this.showPostcodeDropdown = false; // Fetch detailed information about the selected postcode\n\n    this.httpClient.get(`https://api.postcodes.io/postcodes?q=${postcode}`).subscribe(response => {\n      var _a, _b;\n\n      const result = response.result && response.result[0];\n\n      if (result) {\n        this.selectedPostcode = `${result.admin_district}, ${result.country}`; // Customize as needed\n        // Update the form control with the detailed name\n\n        (_a = this.addNewAppUserForm.get('U_postcode')) === null || _a === void 0 ? void 0 : _a.setValue(postcode);\n      } else {\n        // Handle case where no result is returned\n        (_b = this.addNewAppUserForm.get('U_postcode')) === null || _b === void 0 ? void 0 : _b.setValue('');\n      }\n    });\n    this.calculateProfilePercentage();\n  }\n\n  removeSelectedPostcode() {\n    var _a;\n\n    this.selectedPostcode = null;\n    (_a = this.addNewAppUserForm.get('U_postcode')) === null || _a === void 0 ? void 0 : _a.setValue('');\n    this.calculateProfilePercentage();\n  }\n\n  updateUserPreferences() {\n    this.applyQuizOptionValidator();\n    const identityFields = ['U_ethinicityId', 'U_genderId', 'U_isDisability', 'U_religionId', 'U_sexualOrientationId', 'U_first_generation', 'U_freeMeal', 'U_ethinicityId', 'U_ethinicity_Toggle', 'U_genderId', 'U_gender_Toggle', 'U_isDisability', 'U_disability_Toggle', 'U_religionId', 'U_religion_Toggle', 'U_sexualOrientationId', 'U_sexuality_Toggle', 'U_institute', 'U_postcode', 'U_postcode_Toggle', 'U_first_generation', 'U_first_generation_Toggle', 'U_freeMeal', 'U_freeMeal_Toggle', 'U_education', 'U_name', 'U_email', 'U_password', 'U_profilePercentage', 'U_dp'];\n    identityFields.forEach(field => {\n      var _a, _b;\n\n      (_a = this.addNewAppUserForm.get(field)) === null || _a === void 0 ? void 0 : _a.clearValidators();\n      (_b = this.addNewAppUserForm.get(field)) === null || _b === void 0 ? void 0 : _b.updateValueAndValidity();\n    });\n    const nonIdentityFields = ['LI_contentId'];\n    nonIdentityFields.forEach(field => {\n      var _a, _b;\n\n      (_a = this.addNewAppUserForm.get(field)) === null || _a === void 0 ? void 0 : _a.setValidators(Validators.required);\n      (_b = this.addNewAppUserForm.get(field)) === null || _b === void 0 ? void 0 : _b.updateValueAndValidity();\n    }); // Check if the form is valid\n\n    if (this.addNewAppUserForm.invalid) {\n      this.toastr.info(\"Please fill all required identity fields.\");\n      return;\n    }\n\n    this.ngxSpinnerService.show('globalSpinner');\n    const data = {\n      quizAns: [{\n        data: 'Answer 1',\n        quizId: 'NA',\n        QUA_quiz_options: this.addNewAppUserForm.value.QUA_quiz_options\n      }],\n      LI_contentId: this.addNewAppUserForm.value.LI_contentId,\n      U_id: this.userData.U_id,\n      LI_contentType: \"industry\" // U_seeker_disliked_industries:[''],\n\n    };\n    console.log(\"Data to update preferences\", data);\n    this.dataTransferService.updateUserPreferences(data).subscribe(res => {\n      if (res.statusCode == 200) {\n        this.ngxSpinnerService.hide('globalSpinner');\n        this.toastr.success(\"User preferences updated successfully.\");\n      } else {\n        console.error(\"Couldn't get status code 200\");\n        this.toastr.error(\"Unable to update user preferences\");\n        this.ngxSpinnerService.hide('globalSpinner');\n      }\n    }, error => {\n      this.ngxSpinnerService.hide('globalSpinner');\n      console.error(\"error\", error);\n      this.toastr.error(\"Unable to update user preferences\");\n    });\n  }\n\n  updateUserIdentity() {\n    const nonIdentityFields = ['U_name', 'U_email', 'U_password', 'U_profilePercentage', 'U_dp', 'QUA_quiz_options', 'LI_contentId'];\n    nonIdentityFields.forEach(field => {\n      var _a, _b;\n\n      (_a = this.addNewAppUserForm.get(field)) === null || _a === void 0 ? void 0 : _a.clearValidators();\n      (_b = this.addNewAppUserForm.get(field)) === null || _b === void 0 ? void 0 : _b.updateValueAndValidity();\n    }); // Re-validate identity fields, especially U_genderId\n\n    const identityFields = ['U_ethinicityId', 'U_genderId', 'U_isDisability', 'U_religionId', 'U_sexualOrientationId', 'U_first_generation', 'U_freeMeal'];\n    identityFields.forEach(field => {\n      var _a, _b;\n\n      (_a = this.addNewAppUserForm.get(field)) === null || _a === void 0 ? void 0 : _a.setValidators(Validators.required);\n      (_b = this.addNewAppUserForm.get(field)) === null || _b === void 0 ? void 0 : _b.updateValueAndValidity();\n    }); // Check if the form is valid\n\n    if (this.addNewAppUserForm.invalid) {\n      this.toastr.info(\"Please fill all required identity fields.\");\n      return;\n    }\n\n    this.ngxSpinnerService.show('globalSpinner');\n    this.calculateProfilePercentage();\n    const data = {\n      U_id: this.userData.U_id,\n      U_profilePercentage: this.addNewAppUserForm.value.U_profilePercentage,\n      U_ethinicityId: this.addNewAppUserForm.value.U_ethinicityId,\n      U_ethinicity_Toggle: this.addNewAppUserForm.value.U_ethinicity_Toggle,\n      U_genderId: this.addNewAppUserForm.value.U_genderId,\n      U_gender_Toggle: this.addNewAppUserForm.value.U_gender_Toggle,\n      U_isDisability: this.addNewAppUserForm.value.U_isDisability,\n      U_disability_Toggle: this.addNewAppUserForm.value.U_disability_Toggle,\n      U_religionId: this.addNewAppUserForm.value.U_religionId,\n      U_religion_Toggle: this.addNewAppUserForm.value.U_religion_Toggle,\n      U_sexualOrientationId: this.addNewAppUserForm.value.U_sexualOrientationId,\n      U_sexuality_Toggle: this.addNewAppUserForm.value.U_sexuality_Toggle,\n      U_institute: this.addNewAppUserForm.value.U_institute ? this.addNewAppUserForm.value.U_institute : '',\n      U_postcode: this.addNewAppUserForm.value.U_postcode ? this.addNewAppUserForm.value.U_postcode : '',\n      U_postcode_Toggle: this.addNewAppUserForm.value.U_postcode_Toggle,\n      U_first_generation: this.addNewAppUserForm.value.U_first_generation,\n      U_first_generation_Toggle: this.addNewAppUserForm.value.U_first_generation_Toggle,\n      U_freeMeal: this.addNewAppUserForm.value.U_freeMeal,\n      U_freeMeal_Toggle: this.addNewAppUserForm.value.U_freeMeal_Toggle,\n      U_education: this.addNewAppUserForm.value.U_education ? this.addNewAppUserForm.value.U_education : ''\n    };\n    console.log(\"Data to update identity\", data);\n    this.dataTransferService.updateUserIdentity(data).subscribe(res => {\n      if (res.statusCode === 200) {\n        this.ngxSpinnerService.hide('globalSpinner');\n        this.toastr.success(\"User identity updated successfully.\");\n      } else {\n        console.error(\"Couldn't get status code 200\");\n        this.toastr.error(\"Unable to update user identity\");\n      }\n    }, error => {\n      this.ngxSpinnerService.hide('globalSpinner');\n      console.error(\"error\", error);\n      this.toastr.error(\"Unable to update user identity\");\n    });\n  }\n\n  updateUserDetails() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c, _d;\n\n      try {\n        if (!((_b = (_a = _this2.addNewAppUserForm.get('U_aliasDp')) === null || _a === void 0 ? void 0 : _a.errors) === null || _b === void 0 ? void 0 : _b.fileAspectRatioValidator) && !((_d = (_c = _this2.addNewAppUserForm.get('U_dp')) === null || _c === void 0 ? void 0 : _c.errors) === null || _d === void 0 ? void 0 : _d.fileAspectRatioValidator)) {\n          let userDp;\n          let aliasDp;\n\n          _this2.ngxSpinnerService.show('globalSpinner');\n\n          if (_this2.addNewAppUserForm.value.U_dp) {\n            yield _this2.uploadLogoUrl(_this2.imageName);\n            userDp = _this2.baseUrl + _this2.imageName.name;\n          } else {\n            userDp = _this2.userData.U_dp;\n          }\n\n          if (_this2.addNewAppUserForm.value.U_aliasDp) {\n            yield _this2.uploadLogoUrl(_this2.aliasImageName);\n            aliasDp = _this2.baseUrl + _this2.aliasImageName.name;\n          } else {\n            aliasDp = _this2.userData.U_aliasDp;\n          }\n\n          const data = {\n            U_id: _this2.userData.U_id,\n            U_dp: userDp,\n            U_aliasName: _this2.addNewAppUserForm.value.U_aliasName || _this2.userData.U_aliasName || '',\n            U_aliasDp: aliasDp || ''\n          };\n          console.log(\"User details data to update\", data);\n\n          _this2.dataTransferService.updateUserDetails(data).subscribe(res => {\n            _this2.ngxSpinnerService.hide('globalSpinner');\n\n            _this2.toastr.success(\"User details updated successfully.\");\n          }, error => {\n            _this2.ngxSpinnerService.hide('globalSpinner');\n\n            console.error(\"error\", error);\n\n            _this2.toastr.error(\"Unable to update user details\");\n          });\n        } else {\n          _this2.toastr.info(\"Please resize the image\");\n        }\n      } catch (_e) {\n        _this2.toastr.error(\"Unable to update user details\");\n      }\n    })();\n  }\n\n  applyRequiredValidators() {\n    const requiredFields = ['U_name', 'U_email', 'U_password', 'U_profilePercentage', 'U_ethinicityId', 'U_genderId', 'U_isDisability', 'U_religionId', 'U_sexualOrientationId', 'U_first_generation', 'U_freeMeal', 'LI_contentId'];\n    const excludedFields = ['U_dp', 'U_education', 'U_postcode', 'U_institute', 'U_isExpert'];\n    Object.keys(this.addNewAppUserForm.controls).forEach(key => {\n      if (requiredFields.includes(key) && !excludedFields.includes(key)) {\n        const control = this.addNewAppUserForm.get(key);\n\n        if (control) {\n          const currentValidators = control.validator ? [control.validator] : [];\n          control.setValidators([...currentValidators, Validators.required]);\n          control.updateValueAndValidity(); // Update the control to reflect the new validators\n        }\n      }\n    });\n  }\n\n  applyQuizOptionValidator() {\n    const quizOptionsControl = this.addNewAppUserForm.get('QUA_quiz_options');\n\n    if (quizOptionsControl) {\n      const hasQuizIdFive = quizOptionsControl.value.some(option => option.quiz_id === '5');\n\n      if (!hasQuizIdFive) {\n        quizOptionsControl.setErrors({\n          quizIdFiveMissing: true\n        });\n      } else {\n        quizOptionsControl.setErrors(null);\n      }\n    }\n  }\n\n  createNewAppUser() {\n    this.applyRequiredValidators();\n    this.applyQuizOptionValidator();\n\n    if (this.addNewAppUserForm.invalid) {\n      console.log('this.addNewAppUserForm.value', this.addNewAppUserForm.value);\n      Object.keys(this.addNewAppUserForm.controls).forEach(key => {\n        var _a;\n\n        const controlErrors = (_a = this.addNewAppUserForm.get(key)) === null || _a === void 0 ? void 0 : _a.errors;\n\n        if (controlErrors) {\n          console.log(`Field ${key} is invalid:`, controlErrors);\n        }\n      });\n      this.toastr.info('Please fill all required fields correctly');\n      return;\n    } else {\n      // Initialize the Sharer DP and Alias DP URL variables\n      let sharerDpUrl;\n      let aliasDpUrl; // Handle Sharer DP upload if image is available\n\n      const sharerDpPromise = this.imageName ? this.uploadLogoUrl(this.imageName) : Promise.resolve(null);\n      console.log(\"sharerDpPromise\", sharerDpPromise); // Handle Alias DP upload if image is available\n\n      const aliasDpPromise = this.aliasImageName ? this.uploadLogoUrl(this.aliasImageName) : Promise.resolve(null);\n      console.log(\"aliasDpPromise\", aliasDpPromise); // Wait for both images to upload (if present)\n\n      Promise.all([sharerDpPromise, aliasDpPromise]).then(([sharerDpResponse, aliasDpResponse]) => {\n        var _a, _b; // Set the URLs if the images were uploaded\n\n\n        sharerDpUrl = this.imageName ? this.baseUrl + ((_a = this.imageName) === null || _a === void 0 ? void 0 : _a.name) : '';\n        console.log(\"sharerDpUrl\", sharerDpUrl);\n        aliasDpUrl = this.aliasImageName ? this.baseUrl + ((_b = this.aliasImageName) === null || _b === void 0 ? void 0 : _b.name) : '';\n        console.log(\"aliasDpUrl\", aliasDpUrl); // Finalize the post data with both Sharer and Alias DP URLs\n\n        this.finalizePostData(sharerDpUrl, aliasDpUrl);\n      }).catch(error => {\n        console.error('Error uploading images:', error);\n        this.toastr.error(\"Couldn't upload images.\");\n      });\n    }\n  } // Function to upload the image and return a Promise\n\n\n  uploadLogoUrl(image) {\n    return new Promise((resolve, reject) => {\n      if (!image) {\n        resolve(null);\n      }\n\n      console.log('Uploading image:', image);\n      this.dataTransferService.uploadurl(image).subscribe(res => {\n        console.log('Image upload success:', image === null || image === void 0 ? void 0 : image.name);\n        resolve(res); // Return the response when the upload is successful\n      }, error => {\n        console.error('Image upload failed:', error);\n        reject(error); // Reject the promise if the upload fails\n      });\n    });\n  } // Function to finalize postData and submit form\n\n\n  finalizePostData(sharerDpUrl, aliasDpUrl) {\n    const postData = {\n      U_name: this.addNewAppUserForm.value.U_name,\n      U_email: this.addNewAppUserForm.value.U_email,\n      U_password: this.addNewAppUserForm.value.U_password,\n      U_isExpert: this.addNewAppUserForm.value.U_isExpert,\n      U_countryId: 'UK',\n      U_profilePercentage: this.addNewAppUserForm.value.U_profilePercentage,\n      U_dob: '',\n      U_ethinicityId: this.addNewAppUserForm.value.U_ethinicityId,\n      U_ethinicity_Toggle: this.addNewAppUserForm.value.U_ethinicity_Toggle,\n      U_genderId: this.addNewAppUserForm.value.U_genderId,\n      U_gender_Toggle: this.addNewAppUserForm.value.U_gender_Toggle,\n      U_isDisability: this.addNewAppUserForm.value.U_isDisability,\n      U_disability_Toggle: this.addNewAppUserForm.value.U_disability_Toggle,\n      U_religionId: this.addNewAppUserForm.value.U_religionId,\n      U_religion_Toggle: this.addNewAppUserForm.value.U_religion_Toggle,\n      U_sexualOrientationId: this.addNewAppUserForm.value.U_sexualOrientationId,\n      U_sexuality_Toggle: this.addNewAppUserForm.value.U_sexuality_Toggle,\n      U_institute: this.addNewAppUserForm.value.U_institute || '',\n      U_postcode: this.addNewAppUserForm.value.U_postcode || '',\n      U_postcode_Toggle: this.addNewAppUserForm.value.U_postcode_Toggle,\n      U_first_generation: this.addNewAppUserForm.value.U_first_generation,\n      U_first_generation_Toggle: this.addNewAppUserForm.value.U_first_generation_Toggle,\n      U_dp: sharerDpUrl || '',\n      U_aliasDp: aliasDpUrl || '',\n      U_aliasName: this.addNewAppUserForm.value.U_aliasName,\n      U_profileAnonymous: this.addNewAppUserForm.value.U_profileAnonymous,\n      U_isSharer: '1',\n      U_industryId: '1',\n      U_roleId: '1',\n      U_regionalAccentId: '1',\n      U_registertype: 'email',\n      U_registertypeId: '1',\n      U_activeStatus: '1',\n      U_freeMeal: this.addNewAppUserForm.value.U_freeMeal,\n      U_freeMeal_Toggle: this.addNewAppUserForm.value.U_freeMeal_Toggle,\n      U_education: this.addNewAppUserForm.value.U_education || '',\n      U_totalLikeCount: 0,\n      U_seeker_disliked_industries: [{\n        U_seekerDislikedIndId: ''\n      }],\n      quizAns: [{\n        data: 'Answer 1',\n        quizId: 'NA',\n        QUA_quiz_options: this.addNewAppUserForm.value.QUA_quiz_options\n      }],\n      LI_contentId: this.addNewAppUserForm.value.LI_contentId\n    };\n    console.log(postData);\n    this.ngxSpinnerService.show('globalSpinner');\n    this.dataTransferService.createNewAppUser(postData).subscribe(res => {\n      if (res.statusCode == 200) {\n        this.toastr.success('New sharer added successfully.');\n        this.ngxSpinnerService.hide('globalSpinner');\n        this.router.navigate(['/actions/app-users']);\n        this.dataTransferService.getAllAppUsers();\n      } else {\n        this.toastr.error('Something went wrong');\n        console.error('Error:', \"Status code error\");\n        this.ngxSpinnerService.hide('globalSpinner');\n      }\n    }, error => {\n      this.ngxSpinnerService.hide('globalSpinner');\n\n      if (error.status === 400) {\n        this.toastr.error('This sharer already exists.');\n      } else if (error.status === 500) {\n        this.toastr.success('New sharer added successfully.');\n        this.router.navigate(['/actions/app-users']);\n        this.dataTransferService.getAllAppUsers();\n      } else {\n        console.error('Error:', error);\n        this.toastr.error(\"Couldn't add new sharer.\");\n      }\n    });\n  }\n\n  showAddUniversityDegreeModal(title) {\n    if (title === 'University') {\n      const modal = document.getElementById('add-university-modal');\n\n      if (modal) {\n        modal.style.display = 'block';\n      }\n    } else {\n      const modal = document.getElementById('add-degree-modal');\n\n      if (modal) {\n        modal.style.display = 'block';\n      }\n    }\n  }\n\n  hideAddUniversityDegreeModal(title) {\n    if (title === 'University') {\n      const modal = document.getElementById('add-university-modal');\n\n      if (modal) {\n        modal.style.display = 'none';\n      }\n\n      const universityInput = document.getElementById('INS_title');\n\n      if (universityInput) {\n        universityInput.value = '';\n      }\n    } else {\n      const modal = document.getElementById('add-degree-modal');\n\n      if (modal) {\n        modal.style.display = 'none';\n      }\n\n      const degreeInput = document.getElementById('ED_name');\n\n      if (degreeInput) {\n        degreeInput.value = '';\n      }\n    }\n  }\n\n  addUniversity() {\n    const titleElement = document.getElementById('INS_title');\n    const title = titleElement ? titleElement.value : '';\n    console.log('title', title);\n    const data = {\n      INS_title: title\n    };\n    this.dataTransferService.addUniversity(data).subscribe(res => {\n      if (res.statusCode === 200) {\n        this.toastr.success('New University Added Successfully');\n        this.getAllUniversity();\n        this.hideAddUniversityDegreeModal('University');\n      }\n    }, error => {\n      if (error.status === 400) {\n        this.toastr.error('The university already exists, as given.');\n        return;\n      }\n\n      this.toastr.error('Unable to add university');\n      console.log('Error while adding university', error);\n    });\n  }\n\n  addDegree() {\n    const titleElement = document.getElementById('ED_name');\n    const title = titleElement ? titleElement.value : '';\n    console.log('title', title);\n    const data = {\n      ED_name: title\n    };\n    this.dataTransferService.addDegree(data).subscribe(res => {\n      if (res.statusCode === 200) {\n        this.toastr.success('New Degree Added Successfully');\n        this.getAllDegree();\n        this.hideAddUniversityDegreeModal('Degree');\n      }\n    }, error => {\n      if (error.status === 400) {\n        this.toastr.error('The Degree already exists, as given.');\n        return;\n      }\n\n      this.toastr.error('Unable to add degree');\n      console.log('Error while adding degree', error);\n    });\n  }\n\n}\n\nEditAppUserComponent.ɵfac = function EditAppUserComponent_Factory(t) {\n  return new (t || EditAppUserComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.DataTransferService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.NgxSpinnerService), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n};\n\nEditAppUserComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: EditAppUserComponent,\n  selectors: [[\"app-edit-app-user\"]],\n  hostBindings: function EditAppUserComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function EditAppUserComponent_click_HostBindingHandler($event) {\n        return ctx.onClickOutside($event);\n      }, false, i0.ɵɵresolveDocument);\n    }\n  },\n  decls: 175,\n  vars: 39,\n  consts: [[1, \"content-wrapper\"], [1, \"container-scroll\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\", 2, \"background-color\", \"hsl(0, 0%, 100%)\"], [3, \"formGroup\"], [1, \"section\", \"shadow-lg\", \"py-3\", \"px-2\"], [1, \"heading\", \"shadow-sm\"], [1, \"row\", \"mx-3\", \"pt-2\"], [\"class\", \"form-group col-lg-6 pl-4\", 4, \"ngIf\"], [1, \"form-group\", \"col-lg-6\", \"pl-4\"], [\"for\", \"U_dp\", 1, \"label\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"U_dp\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Sharer DP\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"class\", \"btn-custom-small\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"row mx-4 mb-5\", 4, \"ngIf\"], [\"class\", \"row mx-3\", 4, \"ngIf\"], [\"class\", \"cropper-container\", \"class\", \"row mx-4 mb-5\", 4, \"ngIf\"], [\"class\", \"text-center mt-5\", 4, \"ngIf\"], [1, \"section\", \"shadow-lg\", \"py-3\", \"px-2\", \"mt-4\"], [1, \"form-group\", \"pl-4\", \"mt-3\"], [\"for\", \"QUA_quiz_options\", 1, \"required-field\", \"label\", \"ml-4\"], [1, \"d-flex\", \"flex-wrap\"], [\"class\", \"tag\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"pl-4\", \"mt-4\"], [\"for\", \"LI_contentId\", 1, \"required-field\", \"label\", \"ml-4\"], [\"for\", \"QUA_quiz_options\", 1, \"label\", \"ml-4\"], [1, \"form-check\", \"form-switch\", \"pt-2\"], [\"for\", \"flexSwitchCheckDefault\", 1, \"form-check-label\"], [\"checked\", \"\", \"disabled\", \"\", \"type\", \"checkbox\", \"id\", \"flexSwitchCheckDefault\", 1, \"form-check-input\", \"form-check-input-head\", \"mr-4\"], [1, \"form-check\", \"form-switch\"], [\"for\", \"U_genderId\", 1, \"required-field\", \"form-check-label\", \"label\"], [\"type\", \"checkbox\", \"id\", \"U_genderId\", \"formControlName\", \"U_gender_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_ethinicityId\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_ethinicityId\", \"formControlName\", \"U_ethinicity_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_religionId\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_religionId\", \"formControlName\", \"U_religion_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_sexualOrientationId\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_sexualOrientationId\", \"formControlName\", \"U_sexuality_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_isDisability\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_isDisability\", \"formControlName\", \"U_disability_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_first_generation\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_first_generation\", \"formControlName\", \"U_first_generation_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_freeMeal\", 1, \"required-field\", \"label\", \"ml-4\"], [\"type\", \"checkbox\", \"id\", \"U_freeMeal_Toggle\", \"formControlName\", \"U_freeMeal_Toggle\", 1, \"form-check-input\"], [1, \"form-group\", \"col-lg-7\", \"pl-4\", \"col-md-6\", \"col-sm-12\", \"mb-3\"], [\"for\", \"U_institute\", 1, \"label\", \"universityLabel\", \"ml-4\"], [1, \"autocomplete-container\", \"autocomplete-container-institute\", \"ml-4\"], [1, \"tag-input-wrapper\"], [\"class\", \"selected-options\", 4, \"ngIf\"], [\"type\", \"text\", \"placeholder\", \"Type to search...\", \"required\", \"\", 1, \"form-control\", \"tag-input\", \"form-control-sm\", 3, \"formControl\", \"input\", \"click\"], [\"class\", \"dropdown-list\", 4, \"ngIf\"], [\"class\", \"no-options-message\", 4, \"ngIf\"], [\"for\", \"U_education\", 1, \"label\", \"ml-4\"], [1, \"addNewBtn\", 3, \"click\"], [1, \"autocomplete-container\", \"autocomplete-container-education\", \"ml-4\"], [\"id\", \"add-degree-modal\", 1, \"add-degree-modal\", \"modal\"], [1, \"modal-dialog\", \"modal-dialog-centered\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"degreeBackdropLabel\", 1, \"modal-title\", \"fs-5\"], [\"type\", \"button\", \"data-dismiss\", \"add-degree-modal\", 1, \"close\", 2, \"color\", \"white\", 3, \"click\"], [1, \"modal-body\"], [\"type\", \"text\", \"id\", \"ED_name\", \"placeholder\", \"Enter New Degree Name\", 1, \"form-control\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"add-degree-modal\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"type\", \"button\", \"data-bs-dismiss\", \"add-degree-modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"for\", \"U_postcode\", 1, \"label\", \"ml-4\"], [\"type\", \"checkbox\", \"id\", \"U_postcode_Toggle\", \"formControlName\", \"U_postcode_Toggle\", 1, \"form-check-input\"], [\"id\", \"add-university-modal\", 1, \"add-university-modal\", \"modal\"], [\"id\", \"universityBackdropLabel\", 1, \"modal-title\", \"fs-5\"], [\"type\", \"button\", \"data-dismiss\", \"add-university-modal\", 1, \"close\", 2, \"color\", \"white\", 3, \"click\"], [\"type\", \"text\", \"id\", \"INS_title\", \"placeholder\", \"Enter New University Name\", 1, \"form-control\"], [\"type\", \"button\", \"data-bs-dismiss\", \"add-university-modal\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"type\", \"button\", \"data-bs-dismiss\", \"add-university-modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"for\", \"U_name\", 1, \"required-field\", \"label\"], [\"type\", \"text\", \"id\", \"U_name\", \"formControlName\", \"U_name\", \"placeholder\", \"Enter User Name\", 1, \"form-control\"], [\"for\", \"U_email\", 1, \"required-field\", \"label\"], [\"type\", \"email\", \"id\", \"U_email\", \"formControlName\", \"U_email\", \"placeholder\", \"Enter User Email\", \"autocomplete\", \"new-password\", 1, \"form-control\"], [1, \"text-danger\"], [4, \"ngIf\"], [\"for\", \"U_password\", 1, \"required-field\", \"label\"], [\"type\", \"password\", \"id\", \"U_password\", \"formControlName\", \"U_password\", \"placeholder\", \"Enter password\", \"autocomplete\", \"new-password\", 1, \"form-control\"], [1, \"fa\", \"fa-eye\", \"position-absolute\", \"toggle-password\", 3, \"click\"], [\"togglePasswordIcon\", \"\"], [\"alt\", \"Sharer DP\", 1, \"img-preview\", 3, \"src\"], [1, \"btn-custom-small\", 3, \"click\"], [1, \"row\", \"mx-4\", \"mb-5\"], [1, \"image-cropper\", \"text-center\", \"col-lg-12\"], [1, \"py-2\"], [\"format\", \"png\", 1, \"custom-image-cropper\", \"my-2\", 3, \"imageChangedEvent\", \"aspectRatio\", \"maintainAspectRatio\", \"imageCropped\", \"imageLoaded\", \"cropperReady\", \"loadImageFailed\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"m-2\", 3, \"click\"], [1, \"row\", \"mx-3\"], [1, \"form-check\", \"ml-1\"], [\"type\", \"checkbox\", \"formControlName\", \"U_isExpert\", \"id\", \"U_isExpert\"], [\"for\", \"U_isExpert\", 1, \"label\", \"ml-2\", \"mt-2\"], [\"type\", \"checkbox\", \"formControlName\", \"U_profileAnonymous\", \"id\", \"U_profileAnonymous\", 3, \"change\"], [\"for\", \"U_profileAnonymous\", 1, \"label\", \"ml-2\", \"mt-2\"], [\"for\", \"U_aliasName\", 1, \"label\"], [\"type\", \"text\", \"id\", \"U_aliasName\", \"formControlName\", \"U_aliasName\", \"placeholder\", \"Enter User Name\", 1, \"form-control\"], [\"for\", \"U_aliasDp\", 1, \"label\"], [\"type\", \"file\", \"formControlName\", \"U_aliasDp\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Alias DP Image\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Alias DP Image\", 1, \"img-preview\", 3, \"src\"], [1, \"text-center\", \"mt-5\"], [1, \"btn\", \"btn-primary\", \"mr-2\", \"px-5\", \"my-2\", \"submit-btn\", 3, \"click\"], [\"routerLink\", \"/actions/app-users\", 1, \"btn\", \"btn-light\", \"px-5\"], [1, \"tag\", 3, \"click\"], [1, \"check-mark\"], [1, \"selected-options\"], [1, \"selected-option\"], [\"type\", \"button\", 1, \"remove-selected-option\", 3, \"click\"], [1, \"dropdown-list\"], [3, \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"click\"], [1, \"no-options-message\"], [1, \"btn\", \"btn-primary\", \"mr-2\", \"px-5\", \"submit-btn\", 3, \"click\"]],\n  template: function EditAppUserComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"app-sidebar\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵtext(4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"div\", 3);\n      i0.ɵɵelementStart(6, \"form\", 4);\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵelementStart(8, \"h4\", 6);\n      i0.ɵɵtext(9, \"Sharer Details\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵtemplate(11, EditAppUserComponent_div_11_Template, 4, 0, \"div\", 8);\n      i0.ɵɵtemplate(12, EditAppUserComponent_div_12_Template, 5, 1, \"div\", 8);\n      i0.ɵɵtemplate(13, EditAppUserComponent_div_13_Template, 7, 1, \"div\", 8);\n      i0.ɵɵelementStart(14, \"div\", 9);\n      i0.ɵɵelementStart(15, \"label\", 10);\n      i0.ɵɵtext(16, \"Sharer DP\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 11);\n      i0.ɵɵelementStart(18, \"input\", 12);\n      i0.ɵɵlistener(\"change\", function EditAppUserComponent_Template_input_change_18_listener($event) {\n        return ctx.onFileSelected($event, \"U_dp\");\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(19, EditAppUserComponent_img_19_Template, 1, 1, \"img\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(20, EditAppUserComponent_div_20_Template, 2, 0, \"div\", 14);\n      i0.ɵɵtemplate(21, EditAppUserComponent_div_21_Template, 2, 0, \"div\", 14);\n      i0.ɵɵtemplate(22, EditAppUserComponent_a_22_Template, 2, 0, \"a\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(23, EditAppUserComponent_div_23_Template, 9, 3, \"div\", 16);\n      i0.ɵɵtemplate(24, EditAppUserComponent_div_24_Template, 3, 2, \"div\", 17);\n      i0.ɵɵtemplate(25, EditAppUserComponent_div_25_Template, 14, 5, \"div\", 17);\n      i0.ɵɵtemplate(26, EditAppUserComponent_div_26_Template, 9, 3, \"div\", 18);\n      i0.ɵɵtemplate(27, EditAppUserComponent_div_27_Template, 5, 0, \"div\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"div\", 20);\n      i0.ɵɵelementStart(29, \"h4\", 6);\n      i0.ɵɵtext(30, \"Preferences\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"div\", 21);\n      i0.ɵɵelementStart(32, \"label\", 22);\n      i0.ɵɵtext(33, \"Activities They Do a Lot In Their Role\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"div\", 23);\n      i0.ɵɵtemplate(35, EditAppUserComponent_div_35_Template, 4, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(36, \"hr\");\n      i0.ɵɵelementStart(37, \"div\", 25);\n      i0.ɵɵelementStart(38, \"label\", 26);\n      i0.ɵɵtext(39, \"Sectors\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"div\", 23);\n      i0.ɵɵtemplate(41, EditAppUserComponent_div_41_Template, 4, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(42, \"hr\");\n      i0.ɵɵelementStart(43, \"div\", 25);\n      i0.ɵɵelementStart(44, \"label\", 27);\n      i0.ɵɵtext(45, \"Their Typical Work Type\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(46, \"div\", 23);\n      i0.ɵɵtemplate(47, EditAppUserComponent_div_47_Template, 5, 5, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(48, EditAppUserComponent_div_48_Template, 5, 0, \"div\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"div\", 20);\n      i0.ɵɵelementStart(50, \"h4\", 6);\n      i0.ɵɵtext(51, \"Identity\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(52, \"div\", 28);\n      i0.ɵɵelementStart(53, \"label\", 29);\n      i0.ɵɵtext(54, \"Activate the toggle below if you want this info displayed on your profile\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(55, \"input\", 30);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(56, \"hr\");\n      i0.ɵɵelementStart(57, \"div\", 25);\n      i0.ɵɵelementStart(58, \"div\", 31);\n      i0.ɵɵelementStart(59, \"label\", 32);\n      i0.ɵɵtext(60, \"Gender\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(61, \"input\", 33);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(62, \"div\", 23);\n      i0.ɵɵtemplate(63, EditAppUserComponent_div_63_Template, 4, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(64, \"hr\");\n      i0.ɵɵelementStart(65, \"div\", 25);\n      i0.ɵɵelementStart(66, \"div\", 31);\n      i0.ɵɵelementStart(67, \"label\", 34);\n      i0.ɵɵtext(68, \"Ethnicity\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(69, \"input\", 35);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(70, \"div\", 23);\n      i0.ɵɵtemplate(71, EditAppUserComponent_div_71_Template, 4, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(72, \"hr\");\n      i0.ɵɵelementStart(73, \"div\", 25);\n      i0.ɵɵelementStart(74, \"div\", 31);\n      i0.ɵɵelementStart(75, \"label\", 36);\n      i0.ɵɵtext(76, \"Religion\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(77, \"input\", 37);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(78, \"div\", 23);\n      i0.ɵɵtemplate(79, EditAppUserComponent_div_79_Template, 4, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(80, \"hr\");\n      i0.ɵɵelementStart(81, \"div\", 25);\n      i0.ɵɵelementStart(82, \"div\", 31);\n      i0.ɵɵelementStart(83, \"label\", 38);\n      i0.ɵɵtext(84, \"Sexual Orientation\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(85, \"input\", 39);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(86, \"div\", 23);\n      i0.ɵɵtemplate(87, EditAppUserComponent_div_87_Template, 4, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(88, \"hr\");\n      i0.ɵɵelementStart(89, \"div\", 25);\n      i0.ɵɵelementStart(90, \"div\", 31);\n      i0.ɵɵelementStart(91, \"label\", 40);\n      i0.ɵɵtext(92, \"Has A Disability ?\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(93, \"input\", 41);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(94, \"div\", 23);\n      i0.ɵɵtemplate(95, EditAppUserComponent_div_95_Template, 4, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(96, \"hr\");\n      i0.ɵɵelementStart(97, \"div\", 25);\n      i0.ɵɵelementStart(98, \"div\", 31);\n      i0.ɵɵelementStart(99, \"label\", 42);\n      i0.ɵɵtext(100, \"Is This First Generation ?\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(101, \"input\", 43);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(102, \"div\", 23);\n      i0.ɵɵtemplate(103, EditAppUserComponent_div_103_Template, 4, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(104, \"hr\");\n      i0.ɵɵelementStart(105, \"div\", 25);\n      i0.ɵɵelementStart(106, \"label\", 44);\n      i0.ɵɵtext(107, \"Was Entitled To Free School Meals ?\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(108, \"input\", 45);\n      i0.ɵɵelementStart(109, \"div\", 23);\n      i0.ɵɵtemplate(110, EditAppUserComponent_div_110_Template, 4, 3, \"div\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(111, \"hr\");\n      i0.ɵɵelementStart(112, \"div\", 46);\n      i0.ɵɵelementStart(113, \"label\", 47);\n      i0.ɵɵtext(114, \"University\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(115, \"div\", 48);\n      i0.ɵɵelementStart(116, \"div\", 49);\n      i0.ɵɵtemplate(117, EditAppUserComponent_div_117_Template, 5, 1, \"div\", 50);\n      i0.ɵɵelementStart(118, \"input\", 51);\n      i0.ɵɵlistener(\"input\", function EditAppUserComponent_Template_input_input_118_listener() {\n        return ctx.filterOptions(\"U_institute\");\n      })(\"click\", function EditAppUserComponent_Template_input_click_118_listener() {\n        return ctx.showDropdown = true;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(119, EditAppUserComponent_div_119_Template, 3, 1, \"div\", 52);\n      i0.ɵɵtemplate(120, EditAppUserComponent_div_120_Template, 2, 0, \"div\", 53);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(121, \"hr\");\n      i0.ɵɵelementStart(122, \"div\", 46);\n      i0.ɵɵelementStart(123, \"label\", 54);\n      i0.ɵɵtext(124, \"Degree\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(125, \"a\", 55);\n      i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_a_click_125_listener() {\n        return ctx.showAddUniversityDegreeModal(\"Degree\");\n      });\n      i0.ɵɵtext(126, \"Add New +\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(127, \"div\", 56);\n      i0.ɵɵelementStart(128, \"div\", 49);\n      i0.ɵɵtemplate(129, EditAppUserComponent_div_129_Template, 5, 1, \"div\", 50);\n      i0.ɵɵelementStart(130, \"input\", 51);\n      i0.ɵɵlistener(\"input\", function EditAppUserComponent_Template_input_input_130_listener() {\n        return ctx.filterOptions(\"U_education\");\n      })(\"click\", function EditAppUserComponent_Template_input_click_130_listener() {\n        return ctx.showDropdownOfEducation = true;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(131, EditAppUserComponent_div_131_Template, 3, 1, \"div\", 52);\n      i0.ɵɵtemplate(132, EditAppUserComponent_div_132_Template, 2, 0, \"div\", 53);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(133, \"div\", 57);\n      i0.ɵɵelementStart(134, \"div\", 58);\n      i0.ɵɵelementStart(135, \"div\", 59);\n      i0.ɵɵelementStart(136, \"div\", 60);\n      i0.ɵɵelementStart(137, \"h4\", 61);\n      i0.ɵɵtext(138, \"Add New Degree\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(139, \"button\", 62);\n      i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_139_listener() {\n        return ctx.hideAddUniversityDegreeModal(\"Degree\");\n      });\n      i0.ɵɵtext(140, \"\\u00D7\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(141, \"div\", 63);\n      i0.ɵɵelement(142, \"input\", 64);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(143, \"div\", 65);\n      i0.ɵɵelementStart(144, \"button\", 66);\n      i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_144_listener() {\n        return ctx.addDegree();\n      });\n      i0.ɵɵtext(145, \"Add\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(146, \"button\", 67);\n      i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_146_listener() {\n        return ctx.hideAddUniversityDegreeModal(\"Degree\");\n      });\n      i0.ɵɵtext(147, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(148, \"hr\");\n      i0.ɵɵelementStart(149, \"div\", 46);\n      i0.ɵɵelementStart(150, \"label\", 68);\n      i0.ɵɵtext(151, \"Postcode\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(152, \"input\", 69);\n      i0.ɵɵelementStart(153, \"div\", 56);\n      i0.ɵɵelementStart(154, \"div\", 49);\n      i0.ɵɵtemplate(155, EditAppUserComponent_div_155_Template, 5, 1, \"div\", 50);\n      i0.ɵɵelementStart(156, \"input\", 51);\n      i0.ɵɵlistener(\"input\", function EditAppUserComponent_Template_input_input_156_listener() {\n        return ctx.filterPostcodes();\n      })(\"click\", function EditAppUserComponent_Template_input_click_156_listener() {\n        return ctx.showPostcodeDropdown = true;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(157, EditAppUserComponent_div_157_Template, 3, 1, \"div\", 52);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(158, EditAppUserComponent_div_158_Template, 5, 0, \"div\", 19);\n      i0.ɵɵtemplate(159, EditAppUserComponent_div_159_Template, 5, 0, \"div\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(160, \"div\", 70);\n      i0.ɵɵelementStart(161, \"div\", 58);\n      i0.ɵɵelementStart(162, \"div\", 59);\n      i0.ɵɵelementStart(163, \"div\", 60);\n      i0.ɵɵelementStart(164, \"h4\", 71);\n      i0.ɵɵtext(165, \"Add New University\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(166, \"button\", 72);\n      i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_166_listener() {\n        return ctx.hideAddUniversityDegreeModal(\"University\");\n      });\n      i0.ɵɵtext(167, \"\\u00D7\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(168, \"div\", 63);\n      i0.ɵɵelement(169, \"input\", 73);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(170, \"div\", 65);\n      i0.ɵɵelementStart(171, \"button\", 74);\n      i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_171_listener() {\n        return ctx.addUniversity();\n      });\n      i0.ɵɵtext(172, \"Add\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(173, \"button\", 75);\n      i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_173_listener() {\n        return ctx.hideAddUniversityDegreeModal(\"University\");\n      });\n      i0.ɵɵtext(174, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      let tmp_7_0;\n      let tmp_8_0;\n      let tmp_9_0;\n      let tmp_10_0;\n      let tmp_12_0;\n      let tmp_13_0;\n      let tmp_26_0;\n      let tmp_30_0;\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Sharer\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.addNewAppUserForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.title === \"Add\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title === \"Add\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title === \"Add\");\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.imageSrc);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx.addNewAppUserForm.get(\"U_dp\")) == null ? null : tmp_7_0.errors == null ? null : tmp_7_0.errors.fileSizeValidator);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.addNewAppUserForm.get(\"U_dp\")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors.fileAspectRatioValidator);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isCropperVisible && ctx.imageName && !((tmp_9_0 = ctx.addNewAppUserForm.get(\"U_dp\")) == null ? null : tmp_9_0.errors == null ? null : tmp_9_0.errors.fileSizeValidator));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isCropperVisible && !((tmp_10_0 = ctx.addNewAppUserForm.get(\"U_dp\")) == null ? null : tmp_10_0.errors == null ? null : tmp_10_0.errors.fileSizeValidator));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title === \"Add\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (tmp_12_0 = ctx.addNewAppUserForm.get(\"U_profileAnonymous\")) == null ? null : tmp_12_0.value);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isAliasCropperVisible && !((tmp_13_0 = ctx.addNewAppUserForm.get(\"U_aliasDp\")) == null ? null : tmp_13_0.errors == null ? null : tmp_13_0.errors.fileSizeValidator));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title == \"Edit\");\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngForOf\", ctx.ActivitiesYouLike);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngForOf\", ctx.LikedSectors);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngForOf\", ctx.WorkType);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title == \"Edit\");\n      i0.ɵɵadvance(15);\n      i0.ɵɵproperty(\"ngForOf\", ctx.Genders);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngForOf\", ctx.Ethnicities);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngForOf\", ctx.Religions);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngForOf\", ctx.SexualOrientation);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngForOf\", ctx.hasADisability);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngForOf\", ctx.isThisGeneration);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngForOf\", ctx.FreeMeal);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngIf\", ctx.showTag && ((tmp_26_0 = ctx.addNewAppUserForm.get(\"U_institute\")) == null ? null : tmp_26_0.value));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"formControl\", ctx.universitySearchControl);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showDropdown && ctx.filteredUniversityOptions.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showDropdown && ctx.filteredUniversityOptions.length === 0);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngIf\", ctx.showTagOfEducation && ((tmp_30_0 = ctx.addNewAppUserForm.get(\"U_education\")) == null ? null : tmp_30_0.value));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"formControl\", ctx.degreeSearchControl);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showDropdownOfEducation && ctx.filteredDegreeOptions.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showDropdownOfEducation && ctx.filteredDegreeOptions.length === 0);\n      i0.ɵɵadvance(23);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectedPostcode);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"formControl\", ctx.postcodeSearchControl);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showPostcodeDropdown && ctx.filteredPostcodes.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title !== \"Edit\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title == \"Edit\");\n    }\n  },\n  directives: [i7.SidebarComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i8.NgIf, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlName, i8.NgForOf, i2.CheckboxControlValueAccessor, i2.RequiredValidator, i2.FormControlDirective, i9.ImageCropperComponent, i1.RouterLink],\n  pipes: [i10.CamelCasePipe],\n  styles: [\".container-scroll[_ngcontent-%COMP%] {\\n  height: 100%;\\n  \\n  overflow-y: auto;\\n  \\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n}\\n\\n.addNewBtn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 20px;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n}\\n\\n\\n\\n@media screen and (min-width: 768px) and (max-width: 1024px) {\\n  .card-title[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n\\n  .card-text[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n.preferences-row[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.1);\\n  border-radius: 10px;\\n  padding: 20px;\\n  background-color: #fff;\\n}\\n\\n.identity-row[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.1);\\n  border-radius: 10px;\\n  padding: 20px;\\n  background-color: #fff;\\n}\\n\\n.heading[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  text-align: center;\\n  padding-top: 15px;\\n  padding-bottom: 15px;\\n  background-color: #ffffff;\\n  border-radius: 10px;\\n  font-weight: bold;\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  background-color: #ffffff;\\n  border-radius: 10px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 7px 25px 7px 15px;\\n  border: 1px solid #ddd;\\n  border-radius: 18px;\\n  background-color: #e6e6e6;\\n  cursor: pointer;\\n  margin: 5px;\\n  transition: background-color 0.3s;\\n  font-size: 0.9rem;\\n  margin-left: 20px;\\n}\\n\\n.tag.selected[_ngcontent-%COMP%] {\\n  background-color: #FF6B0B;\\n  color: #ffffff;\\n}\\n\\n.check-mark[_ngcontent-%COMP%] {\\n  display: none;\\n  position: absolute;\\n  right: 8px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  font-size: 14px;\\n  margin-left: 5px;\\n}\\n\\n.tag.selected[_ngcontent-%COMP%]   .check-mark[_ngcontent-%COMP%] {\\n  display: inline;\\n}\\n\\n.label[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: bold;\\n}\\n\\n\\n\\n.autocomplete-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.tag-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 100%;\\n  padding-right: 30px;\\n}\\n\\n.selected-options[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  margin-bottom: 5px;\\n  padding: 9px 10px 9px 10px;\\n  border: 1px solid #ddd;\\n  border-radius: 20px;\\n  background-color: #e6e6e6;\\n  cursor: pointer;\\n  background-color: #FF6B0B;\\n  color: #ffffff;\\n}\\n\\n.selected-option[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n.remove-selected-option[_ngcontent-%COMP%] {\\n  margin-left: 0.2rem;\\n  font-size: 1rem;\\n  color: #fff;\\n  cursor: pointer;\\n  background: none;\\n  border: none;\\n}\\n\\n.remove-selected-option[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.tag-input[_ngcontent-%COMP%] {\\n  height: 48px;\\n  padding: 10px;\\n  font-size: 14px;\\n  border: 1px solid #ced4da;\\n  border-radius: 5px;\\n}\\n\\n.dropdown-list[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  z-index: 1000;\\n  width: 100%;\\n  background-color: #ffffff;\\n  border: 1px solid #ced4da;\\n  border-top: none;\\n  border-radius: 0 0 5px 5px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n.dropdown-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style-type: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.dropdown-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 12px 20px;\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n}\\n\\n.dropdown-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  background-color: #f0f0f0;\\n}\\n\\n.no-options-message[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  font-size: 14px;\\n  color: #777777;\\n}\\n\\n.tag-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n}\\n\\n.form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0%;\\n  margin: 0%;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%] {\\n  width: 39px;\\n  height: 20px;\\n  margin-left: 10px;\\n  margin-top: 0%;\\n  margin-bottom: 4px;\\n  padding-top: 0%;\\n  background-color: #e9ecef;\\n  border: 1px solid #adb5bd;\\n  border-radius: 22px;\\n  appearance: none;\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n  cursor: pointer;\\n  outline: none;\\n  position: relative;\\n  transition: background-color 0.3s ease-in-out;\\n  order: 2;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #0d6efd;\\n  border-color: #0d6efd;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: block;\\n  width: 17px;\\n  height: 16px;\\n  background-color: #fff;\\n  border-radius: 50%;\\n  position: absolute;\\n  top: 1px;\\n  left: 1px;\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked::before {\\n  transform: translateX(18px);\\n}\\n\\n.form-check-label[_ngcontent-%COMP%] {\\n  order: 1;\\n}\\n\\n.form-check-input-head[_ngcontent-%COMP%] {\\n  width: 37px;\\n  height: 18px;\\n  margin-left: 10px;\\n  background-color: #e9ecef;\\n  border: 1px solid #adb5bd;\\n  border-radius: 22px;\\n  appearance: none;\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n  cursor: pointer;\\n  outline: none;\\n  position: relative;\\n  transition: background-color 0.3s ease-in-out;\\n  order: 2;\\n}\\n\\n.form-check-input-head[_ngcontent-%COMP%]:checked {\\n  background-color: #0d6dfd7b;\\n  border-color: #0d6dfd6c;\\n}\\n\\n.form-check-input-head[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: block;\\n  width: 16px;\\n  height: 14px;\\n  background-color: #fff;\\n  border-radius: 50%;\\n  position: absolute;\\n  top: 1px;\\n  left: 1px;\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.toggle-password[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  right: 25px;\\n  top: 45px;\\n}\\n\\n.imageNameBox[_ngcontent-%COMP%] {\\n  position: absolute;\\n  transform: translateY(-50%);\\n  top: 50%;\\n  right: 80px;\\n  max-width: 400px;\\n  max-height: 36px;\\n  background-color: white;\\n  outline: none;\\n  border: none;\\n}\\n\\n@media screen and (max-width: 767px) {\\n  .logo-input-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    right: 5px;\\n    max-width: 25px;\\n    min-height: 33px;\\n  }\\n\\n  .submit-btn[_ngcontent-%COMP%] {\\n    margin-bottom: 10px;\\n  }\\n\\n  .form-group[_ngcontent-%COMP%] {\\n    padding-left: 0px !important;\\n  }\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: red;\\n  font-size: 0.875em;\\n  margin-top: 0.25em;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  color: white;\\n  background-color: #007bff;\\n}\\n\\n.add-university-modal[_ngcontent-%COMP%], .add-degree-modal[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.5);\\n}\\n\\n.add-university-modal[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%], .add-degree-modal[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%] {\\n  margin-top: 0px !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/src/app/shared/sidebar/actions/App-User-Management/edit-app-user/edit-app-user.component.ts"], "names": ["FormControl", "Validators", "debounceTime", "distinctUntilChanged", "switchMap", "map", "catchError", "of", "FileValidator", "i0", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "i8", "i9", "i10", "EditAppUserComponent_div_11_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "EditAppUserComponent_div_12_div_4_div_1_Template", "EditAppUserComponent_div_12_div_4_div_2_Template", "EditAppUserComponent_div_12_div_4_Template", "ɵɵtemplate", "ctx_r33", "ɵɵnextContext", "tmp_0_0", "ɵɵadvance", "ɵɵproperty", "addNewAppUserForm", "get", "errors", "required", "controls", "<PERSON><PERSON><PERSON><PERSON>", "EditAppUserComponent_div_12_Template", "ctx_r1", "invalid", "dirty", "touched", "EditAppUserComponent_div_13_div_6_div_1_Template", "EditAppUserComponent_div_13_div_6_div_2_Template", "EditAppUserComponent_div_13_div_6_div_3_Template", "EditAppUserComponent_div_13_div_6_Template", "ctx_r37", "tmp_1_0", "tmp_2_0", "minlength", "pattern", "EditAppUserComponent_div_13_Template", "_r42", "ɵɵgetCurrentView", "ɵɵlistener", "EditAppUserComponent_div_13_Template_i_click_4_listener", "ɵɵrestoreView", "_r36", "ɵɵreference", "ctx_r41", "togglePasswordVisibility", "ctx_r2", "EditAppUserComponent_img_19_Template", "ctx_r3", "imageSrc", "ɵɵsanitizeUrl", "EditAppUserComponent_div_20_Template", "EditAppUserComponent_div_21_Template", "EditAppUserComponent_a_22_Template", "_r44", "EditAppUserComponent_a_22_Template_a_click_0_listener", "ctx_r43", "showCropper", "EditAppUserComponent_div_23_Template", "_r46", "EditAppUserComponent_div_23_Template_image_cropper_imageCropped_4_listener", "$event", "ctx_r45", "cropImg", "EditAppUserComponent_div_23_Template_image_cropper_imageLoaded_4_listener", "ctx_r47", "imgLoad", "EditAppUserComponent_div_23_Template_image_cropper_cropperReady_4_listener", "ctx_r48", "initCropper", "EditAppUserComponent_div_23_Template_image_cropper_loadImageFailed_4_listener", "ctx_r49", "imgFailed", "EditAppUserComponent_div_23_Template_button_click_5_listener", "ctx_r50", "saveCroppedImage", "EditAppUserComponent_div_23_Template_button_click_7_listener", "ctx_r51", "hideCropper", "ctx_r7", "imgChangeEvt", "EditAppUserComponent_div_24_div_1_Template", "EditAppUserComponent_div_24_div_2_Template", "_r55", "EditAppUserComponent_div_24_div_2_Template_input_change_2_listener", "ctx_r54", "<PERSON><PERSON><PERSON><PERSON>", "EditAppUserComponent_div_24_Template", "ctx_r8", "value", "EditAppUserComponent_div_25_img_10_Template", "ctx_r56", "aliasImageSrc", "EditAppUserComponent_div_25_div_11_Template", "EditAppUserComponent_div_25_div_12_Template", "EditAppUserComponent_div_25_a_13_Template", "_r61", "EditAppUserComponent_div_25_a_13_Template_a_click_0_listener", "ctx_r60", "EditAppUserComponent_div_25_Template", "_r63", "EditAppUserComponent_div_25_Template_input_change_9_listener", "ctx_r62", "onFileSelected", "ctx_r9", "tmp_3_0", "tmp_4_0", "is<PERSON><PERSON><PERSON>ly", "fileSizeValidator", "fileAspectRatioValidator", "isAliasCropperVisible", "aliasImageName", "EditAppUserComponent_div_26_Template", "_r65", "EditAppUserComponent_div_26_Template_image_cropper_imageCropped_4_listener", "ctx_r64", "EditAppUserComponent_div_26_Template_image_cropper_imageLoaded_4_listener", "ctx_r66", "EditAppUserComponent_div_26_Template_image_cropper_cropperReady_4_listener", "ctx_r67", "EditAppUserComponent_div_26_Template_image_cropper_loadImageFailed_4_listener", "ctx_r68", "EditAppUserComponent_div_26_Template_button_click_5_listener", "ctx_r69", "EditAppUserComponent_div_26_Template_button_click_7_listener", "ctx_r70", "ctx_r10", "aliasImgChangeEvt", "EditAppUserComponent_div_27_Template", "_r72", "EditAppUserComponent_div_27_Template_button_click_1_listener", "ctx_r71", "updateUserDetails", "EditAppUserComponent_div_35_Template", "_r75", "EditAppUserComponent_div_35_Template_div_click_0_listener", "restoredCtx", "activity_r73", "$implicit", "ctx_r74", "toggleSelection", "selectedActivities", "ctx_r12", "ɵɵclassProp", "isItemSelected", "ɵɵtextInterpolate1", "option_title", "EditAppUserComponent_div_41_Template", "_r78", "EditAppUserComponent_div_41_Template_div_click_0_listener", "sector_r76", "ctx_r77", "selectedSectors", "ctx_r13", "IN_name", "EditAppUserComponent_div_47_Template", "_r81", "EditAppUserComponent_div_47_Template_div_click_0_listener", "type_r79", "ctx_r80", "selectedWorkTypes", "ɵɵpipe", "ctx_r14", "ɵɵpipeBind1", "EditAppUserComponent_div_48_Template", "_r83", "EditAppUserComponent_div_48_Template_button_click_1_listener", "ctx_r82", "updateUserPreferences", "EditAppUserComponent_div_63_Template", "_r86", "EditAppUserComponent_div_63_Template_div_click_0_listener", "data_r84", "ctx_r85", "selected<PERSON><PERSON>", "ctx_r16", "isItemSelectedForIdentity", "GE_title", "EditAppUserComponent_div_71_Template", "_r89", "EditAppUserComponent_div_71_Template_div_click_0_listener", "data_r87", "ctx_r88", "selectedEthnicity", "ctx_r17", "ET_title", "EditAppUserComponent_div_79_Template", "_r92", "EditAppUserComponent_div_79_Template_div_click_0_listener", "data_r90", "ctx_r91", "selectedReligion", "ctx_r18", "RE_title", "EditAppUserComponent_div_87_Template", "_r95", "EditAppUserComponent_div_87_Template_div_click_0_listener", "data_r93", "ctx_r94", "selectedSexualOrientation", "ctx_r19", "SO_title", "EditAppUserComponent_div_95_Template", "_r98", "EditAppUserComponent_div_95_Template_div_click_0_listener", "type_r96", "ctx_r97", "selectedDisability", "ctx_r20", "AM_title", "EditAppUserComponent_div_103_Template", "_r101", "EditAppUserComponent_div_103_Template_div_click_0_listener", "type_r99", "ctx_r100", "selectedGeneration", "ctx_r21", "EditAppUserComponent_div_110_Template", "_r104", "EditAppUserComponent_div_110_Template_div_click_0_listener", "type_r102", "ctx_r103", "selectedFreeMeal", "ctx_r22", "EditAppUserComponent_div_117_Template", "_r106", "EditAppUserComponent_div_117_Template_button_click_3_listener", "ctx_r105", "tmp_b_0", "removeMenu", "ctx_r23", "getMenuTitle", "EditAppUserComponent_div_119_li_2_Template", "_r110", "EditAppUserComponent_div_119_li_2_Template_li_click_0_listener", "menu_r108", "ctx_r109", "addMenu", "INS_title", "EditAppUserComponent_div_119_Template", "ctx_r24", "filteredUniversityOptions", "EditAppUserComponent_div_120_Template", "EditAppUserComponent_div_129_Template", "_r112", "EditAppUserComponent_div_129_Template_button_click_3_listener", "ctx_r111", "ctx_r26", "EditAppUserComponent_div_131_li_2_Template", "_r116", "EditAppUserComponent_div_131_li_2_Template_li_click_0_listener", "menu_r114", "ctx_r115", "ED_name", "EditAppUserComponent_div_131_Template", "ctx_r27", "filteredDegreeOptions", "EditAppUserComponent_div_132_Template", "EditAppUserComponent_div_155_Template", "_r118", "EditAppUserComponent_div_155_Template_button_click_3_listener", "ctx_r117", "removeSelectedPostcode", "ctx_r29", "selectedPostcode", "EditAppUserComponent_div_157_li_2_Template", "_r122", "EditAppUserComponent_div_157_li_2_Template_li_click_0_listener", "postcode_r120", "ctx_r121", "selectPostcode", "EditAppUserComponent_div_157_Template", "ctx_r30", "filteredPostcodes", "EditAppUserComponent_div_158_Template", "_r124", "EditAppUserComponent_div_158_Template_button_click_1_listener", "ctx_r123", "createNewAppUser", "EditAppUserComponent_div_159_Template", "_r126", "EditAppUserComponent_div_159_Template_button_click_1_listener", "ctx_r125", "updateUserIdentity", "EditAppUserComponent", "constructor", "router", "formBuilder", "dataTransferService", "toastr", "activeRoute", "httpClient", "ngxSpinnerService", "route", "p", "baseUrl", "ActivitiesYouLike", "WorkType", "LikedSectors", "Genders", "Ethnicities", "Religions", "SexualOrientation", "Degree", "University", "showDropdown", "showPostcodeDropdown", "showPassword", "hasADisability", "QUO_id", "FreeMeal", "isThisGeneration", "isCropperVisible", "group", "U_name", "U_email", "strictEmailVali<PERSON><PERSON>", "U_password", "<PERSON><PERSON><PERSON><PERSON>", "U_profilePercentage", "U_ethinicityId", "U_ethinicity_Toggle", "U_genderId", "U_gender_Toggle", "U_isDisability", "U_disability_Toggle", "U_religionId", "U_religion_Toggle", "U_sexualOrientationId", "U_sexuality_Toggle", "U_institute", "U_postcode", "U_postcode_Toggle", "U_first_generation", "U_first_generation_Toggle", "U_dp", "U_freeMeal", "U_freeMeal_Toggle", "U_education", "QUA_quiz_options", "array", "LI_contentId", "U_isExpert", "U_profileAnonymous", "U_aliasName", "U_aliasDp", "queryParams", "subscribe", "params", "U_id", "title", "console", "log", "navigate", "ngOnInit", "getUserDetailsById", "then", "res", "patchFormData", "catch", "error", "getAllPreferencesData", "getIdentityData", "getindustry", "getAllUniversity", "universitySearchControl", "degreeSearchControl", "postcodeSearchControl", "getAllDegree", "valueChanges", "pipe", "fetchPostcodes", "postcodes", "length", "addControl", "Promise", "resolve", "reject", "data", "userData", "hide", "patchValue", "filter", "sector", "includes", "IN_id", "updateFormArray", "option", "quiz_id", "concat", "find", "gender", "GE_id", "push", "ethnicity", "ET_id", "religion", "RE_id", "orientation", "SO_id", "disability", "generation", "meal", "calculateProfilePercentage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ouched", "Object", "keys", "for<PERSON>ach", "field", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "onlySelf", "_a", "_b", "_c", "_d", "percentage", "fields", "toString", "icon", "controlName", "input", "document", "getElementById", "isPasswordType", "type", "classList", "toggle", "event", "selectedFile", "target", "files", "imageName", "fileControl", "clearValidators", "updateValueAndValidity", "aliasFileControl", "newFileName", "addTimestamp", "name", "File", "setValidators", "fileType", "split", "fileExtension", "pop", "toLowerCase", "info", "reader", "FileReader", "img", "Image", "onload", "e", "result", "checkAspectRatio", "readAsDataURL", "aliasFileType", "aliasFileExtension", "image", "aspectRatio", "width", "height", "setErrors", "dataURItoBlob", "dataURI", "byteString", "atob", "mimeString", "arrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uint8Array", "Uint8Array", "i", "charCodeAt", "Blob", "base64", "fileName", "currentTimestamp", "Date", "getTime", "nameWithoutExtension", "slice", "join", "extension", "cleanedName", "replace", "blob", "_e", "isItemSingleSelected", "item", "<PERSON><PERSON><PERSON><PERSON>", "singleSelect", "AM_id", "some", "selected", "formControlName", "option_id", "_f", "_g", "identifier", "splice", "index", "findIndex", "selectedItem", "singleSelectFields", "setControl", "warn", "formArray", "clear", "mergedArray", "statusCode", "quiz", "quiz_options", "populateReferencesData", "QU_id", "options", "formattedItem", "QUO_title", "QUO_quizid", "show", "ethnicities", "genders", "sexual_orientation", "sort", "a", "b", "localeCompare", "getIndustryData", "status", "filterOptions", "searchTerm", "trim", "id", "formControl", "setValue", "selectedUniversity", "showTag", "<PERSON><PERSON><PERSON><PERSON>", "showTagOfEducation", "menu", "formArrayName", "INS_id", "ED_id", "showDropdownOfEducation", "getFormArrayValues", "onClickOutside", "closest", "query", "response", "filterPostcodes", "postcode", "admin_district", "country", "applyQuizOptionValidator", "identityFields", "nonIdentityFields", "quizAns", "quizId", "LI_contentType", "success", "userDp", "aliasDp", "uploadLogoUrl", "applyRequiredValidators", "requiredFields", "excludedFields", "key", "currentValidators", "validator", "quizOptionsControl", "hasQuizIdFive", "quizIdFiveMissing", "controlErrors", "sharerDpUrl", "aliasDpUrl", "sharerDpPromise", "aliasDpPromise", "all", "sharerDpResponse", "aliasDpResponse", "finalizePostData", "uploadurl", "postData", "U_countryId", "U_dob", "U_isSharer", "U_industryId", "U_roleId", "U_regionalAccentId", "U_registertype", "U_registertypeId", "U_activeStatus", "U_totalLikeCount", "U_seeker_disliked_industries", "U_seekerDislikedIndId", "getAllAppUsers", "showAddUniversityDegreeModal", "modal", "style", "display", "hideAddUniversityDegreeModal", "universityInput", "degreeInput", "addUniversity", "titleElement", "addDegree", "ɵfac", "EditAppUserComponent_Factory", "t", "ɵɵdirectiveInject", "Router", "FormBuilder", "DataTransferService", "ToastrService", "ActivatedRoute", "HttpClient", "NgxSpinnerService", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostBindings", "EditAppUserComponent_HostBindings", "EditAppUserComponent_click_HostBindingHandler", "ɵɵresolveDocument", "decls", "vars", "consts", "template", "EditAppUserComponent_Template", "EditAppUserComponent_Template_input_change_18_listener", "EditAppUserComponent_Template_input_input_118_listener", "EditAppUserComponent_Template_input_click_118_listener", "EditAppUserComponent_Template_a_click_125_listener", "EditAppUserComponent_Template_input_input_130_listener", "EditAppUserComponent_Template_input_click_130_listener", "EditAppUserComponent_Template_button_click_139_listener", "EditAppUserComponent_Template_button_click_144_listener", "EditAppUserComponent_Template_button_click_146_listener", "EditAppUserComponent_Template_input_input_156_listener", "EditAppUserComponent_Template_input_click_156_listener", "EditAppUserComponent_Template_button_click_166_listener", "EditAppUserComponent_Template_button_click_171_listener", "EditAppUserComponent_Template_button_click_173_listener", "tmp_7_0", "tmp_8_0", "tmp_9_0", "tmp_10_0", "tmp_12_0", "tmp_13_0", "tmp_26_0", "tmp_30_0", "directives", "SidebarComponent", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "NgIf", "DefaultValueAccessor", "NgControlStatus", "FormControlName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckboxControlValueAccessor", "RequiredValidator", "FormControlDirective", "ImageCropperComponent", "RouterLink", "pipes", "CamelCasePipe", "styles"], "mappings": ";AAAA,SAASA,WAAT,EAAsBC,UAAtB,QAAyC,gBAAzC;AACA,SAASC,YAAT,EAAuBC,oBAAvB,EAA6CC,SAA7C,EAAwDC,GAAxD,EAA6DC,UAA7D,QAAgF,gBAAhF;AACA,SAASC,EAAT,QAAmB,MAAnB;AACA,SAASC,aAAT,QAA8B,mDAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+CAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,YAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,sBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,4BAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,OAAO,KAAKC,GAAZ,MAAqB,gCAArB;;AACA,SAASC,oCAAT,CAA8CC,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,aAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAjB,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASE,gDAAT,CAA0DN,EAA1D,EAA8DC,GAA9D,EAAmE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC7EZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,oBAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASG,gDAAT,CAA0DP,EAA1D,EAA8DC,GAA9D,EAAmE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC7EZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,uBAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASI,0CAAT,CAAoDR,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiBH,gDAAjB,EAAmE,CAAnE,EAAsE,CAAtE,EAAyE,KAAzE,EAAgF,EAAhF;AACAlB,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiBF,gDAAjB,EAAmE,CAAnE,EAAsE,CAAtE,EAAyE,KAAzE,EAAgF,EAAhF;AACAnB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMU,OAAO,GAAGtB,EAAE,CAACuB,aAAH,CAAiB,CAAjB,CAAhB;AACA,QAAIC,OAAJ;AACAxB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACF,OAAO,GAAGF,OAAO,CAACK,iBAAR,CAA0BC,GAA1B,CAA8B,SAA9B,CAAX,KAAwD,IAAxD,GAA+D,IAA/D,GAAsEJ,OAAO,CAACK,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCL,OAAO,CAACK,MAAR,CAAeC,QAA3I;AACA9B,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBJ,OAAO,CAACK,iBAAR,CAA0BI,QAA1B,CAAmC,SAAnC,EAA8CC,QAA9C,CAAuD,cAAvD,CAAtB;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8CrB,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,cAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAjB,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiBD,0CAAjB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,KAAnE,EAA0E,EAA1E;AACApB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMsB,MAAM,GAAGlC,EAAE,CAACuB,aAAH,EAAf;AACA,QAAIC,OAAJ;AACAxB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAAC,CAACF,OAAO,GAAGU,MAAM,CAACP,iBAAP,CAAyBC,GAAzB,CAA6B,SAA7B,CAAX,KAAuD,IAAvD,GAA8D,IAA9D,GAAqEJ,OAAO,CAACW,OAA9E,MAA2F,CAAC,CAACX,OAAO,GAAGU,MAAM,CAACP,iBAAP,CAAyBC,GAAzB,CAA6B,SAA7B,CAAX,KAAuD,IAAvD,GAA8D,IAA9D,GAAqEJ,OAAO,CAACY,KAA9E,MAAyF,CAACZ,OAAO,GAAGU,MAAM,CAACP,iBAAP,CAAyBC,GAAzB,CAA6B,SAA7B,CAAX,KAAuD,IAAvD,GAA8D,IAA9D,GAAqEJ,OAAO,CAACa,OAAtK,CAA3F,CAAtB;AACH;AAAE;;AACH,SAASC,gDAAT,CAA0D1B,EAA1D,EAA8DC,GAA9D,EAAmE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC7EZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,uBAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASuB,gDAAT,CAA0D3B,EAA1D,EAA8DC,GAA9D,EAAmE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC7EZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,8CAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASwB,gDAAT,CAA0D5B,EAA1D,EAA8DC,GAA9D,EAAmE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC7EZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,kCAAb;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,sBAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,sBAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,YAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,uBAAd;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASyB,0CAAT,CAAoD7B,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiBiB,gDAAjB,EAAmE,CAAnE,EAAsE,CAAtE,EAAyE,KAAzE,EAAgF,EAAhF;AACAtC,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiBkB,gDAAjB,EAAmE,CAAnE,EAAsE,CAAtE,EAAyE,KAAzE,EAAgF,EAAhF;AACAvC,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiBmB,gDAAjB,EAAmE,EAAnE,EAAuE,CAAvE,EAA0E,KAA1E,EAAiF,EAAjF;AACAxC,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM8B,OAAO,GAAG1C,EAAE,CAACuB,aAAH,CAAiB,CAAjB,CAAhB;AACA,QAAIC,OAAJ;AACA,QAAImB,OAAJ;AACA,QAAIC,OAAJ;AACA5C,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACF,OAAO,GAAGkB,OAAO,CAACf,iBAAR,CAA0BC,GAA1B,CAA8B,YAA9B,CAAX,KAA2D,IAA3D,GAAkE,IAAlE,GAAyEJ,OAAO,CAACK,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCL,OAAO,CAACK,MAAR,CAAeC,QAA9I;AACA9B,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACiB,OAAO,GAAGD,OAAO,CAACf,iBAAR,CAA0BC,GAA1B,CAA8B,YAA9B,CAAX,KAA2D,IAA3D,GAAkE,IAAlE,GAAyEe,OAAO,CAACd,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCc,OAAO,CAACd,MAAR,CAAegB,SAA9I;AACA7C,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACkB,OAAO,GAAGF,OAAO,CAACf,iBAAR,CAA0BC,GAA1B,CAA8B,YAA9B,CAAX,KAA2D,IAA3D,GAAkE,IAAlE,GAAyEgB,OAAO,CAACf,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCe,OAAO,CAACf,MAAR,CAAeiB,OAA9I;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8CnC,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMoC,IAAI,GAAGhD,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,iBAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAjB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,GAArB,EAA0B,EAA1B,EAA8B,EAA9B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASC,uDAAT,GAAmE;AAAEnD,MAAAA,EAAE,CAACoD,aAAH,CAAiBJ,IAAjB;;AAAwB,YAAMK,IAAI,GAAGrD,EAAE,CAACsD,WAAH,CAAe,CAAf,CAAb;;AAAgC,YAAMC,OAAO,GAAGvD,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOgC,OAAO,CAACC,wBAAR,CAAiCH,IAAjC,EAAuC,YAAvC,CAAP;AAA8D,KAAtP;AACArD,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiBoB,0CAAjB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,KAAnE,EAA0E,EAA1E;AACAzC,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM6C,MAAM,GAAGzD,EAAE,CAACuB,aAAH,EAAf;AACA,QAAIC,OAAJ;AACAxB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAAC,CAACF,OAAO,GAAGiC,MAAM,CAAC9B,iBAAP,CAAyBC,GAAzB,CAA6B,YAA7B,CAAX,KAA0D,IAA1D,GAAiE,IAAjE,GAAwEJ,OAAO,CAACW,OAAjF,MAA8F,CAAC,CAACX,OAAO,GAAGiC,MAAM,CAAC9B,iBAAP,CAAyBC,GAAzB,CAA6B,YAA7B,CAAX,KAA0D,IAA1D,GAAiE,IAAjE,GAAwEJ,OAAO,CAACY,KAAjF,MAA4F,CAACZ,OAAO,GAAGiC,MAAM,CAAC9B,iBAAP,CAAyBC,GAAzB,CAA6B,YAA7B,CAAX,KAA0D,IAA1D,GAAiE,IAAjE,GAAwEJ,OAAO,CAACa,OAA5K,CAA9F,CAAtB;AACH;AAAE;;AACH,SAASqB,oCAAT,CAA8C9C,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEZ,IAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb,EAAgB,KAAhB,EAAuB,EAAvB;AACH;;AAAC,MAAIL,EAAE,GAAG,CAAT,EAAY;AACV,UAAM+C,MAAM,GAAG3D,EAAE,CAACuB,aAAH,EAAf;AACAvB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,KAAd,EAAqBiC,MAAM,CAACC,QAA5B,EAAsC5D,EAAE,CAAC6D,aAAzC;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8ClD,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,uEAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAAS+C,oCAAT,CAA8CnD,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,yEAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASgD,kCAAT,CAA4CpD,EAA5C,EAAgDC,GAAhD,EAAqD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC/D,UAAMqD,IAAI,GAAGjE,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,GAArB,EAA0B,EAA1B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASgB,qDAAT,GAAiE;AAAElE,MAAAA,EAAE,CAACoD,aAAH,CAAiBa,IAAjB;AAAwB,YAAME,OAAO,GAAGnE,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO4C,OAAO,CAACC,WAAR,CAAoB,MAApB,CAAP;AAAqC,KAA3L;AACApE,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,SAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASqD,oCAAT,CAA8CzD,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAM0D,IAAI,GAAGtE,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB,EAA2B,EAA3B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,cAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,eAArB,EAAsC,EAAtC;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,cAAd,EAA8B,SAASqB,0EAAT,CAAoFC,MAApF,EAA4F;AAAExE,MAAAA,EAAE,CAACoD,aAAH,CAAiBkB,IAAjB;AAAwB,YAAMG,OAAO,GAAGzE,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOkD,OAAO,CAACC,OAAR,CAAgBF,MAAhB,EAAwB,MAAxB,CAAP;AAAyC,KAAjO,EAAmO,aAAnO,EAAkP,SAASG,yEAAT,GAAqF;AAAE3E,MAAAA,EAAE,CAACoD,aAAH,CAAiBkB,IAAjB;AAAwB,YAAMM,OAAO,GAAG5E,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOqD,OAAO,CAACC,OAAR,EAAP;AAA2B,KAAha,EAAka,cAAla,EAAkb,SAASC,0EAAT,GAAsF;AAAE9E,MAAAA,EAAE,CAACoD,aAAH,CAAiBkB,IAAjB;AAAwB,YAAMS,OAAO,GAAG/E,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOwD,OAAO,CAACC,WAAR,EAAP;AAA+B,KAArmB,EAAumB,iBAAvmB,EAA0nB,SAASC,6EAAT,GAAyF;AAAEjF,MAAAA,EAAE,CAACoD,aAAH,CAAiBkB,IAAjB;AAAwB,YAAMY,OAAO,GAAGlF,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO2D,OAAO,CAACC,SAAR,EAAP;AAA6B,KAA9yB;AACAnF,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASkC,4DAAT,GAAwE;AAAEpF,MAAAA,EAAE,CAACoD,aAAH,CAAiBkB,IAAjB;AAAwB,YAAMe,OAAO,GAAGrF,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO8D,OAAO,CAACC,gBAAR,CAAyB,MAAzB,CAAP;AAA0C,KAAvM;AACAtF,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,MAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASqC,4DAAT,GAAwE;AAAEvF,MAAAA,EAAE,CAACoD,aAAH,CAAiBkB,IAAjB;AAAwB,YAAMkB,OAAO,GAAGxF,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOiE,OAAO,CAACC,WAAR,CAAoB,MAApB,CAAP;AAAqC,KAAlM;AACAzF,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM8E,MAAM,GAAG1F,EAAE,CAACuB,aAAH,EAAf;AACAvB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,mBAAd,EAAmCgE,MAAM,CAACC,YAA1C,EAAwD,aAAxD,EAAuE,IAAI,CAA3E,EAA8E,qBAA9E,EAAqG,IAArG;AACH;AAAE;;AACH,SAASC,0CAAT,CAAoDhF,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAjB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,8BAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAAS6E,0CAAT,CAAoDjF,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvE,UAAMkF,IAAI,GAAG9F,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,QAAd,EAAwB,SAAS6C,kEAAT,GAA8E;AAAE/F,MAAAA,EAAE,CAACoD,aAAH,CAAiB0C,IAAjB;AAAwB,YAAME,OAAO,GAAGhG,EAAE,CAACuB,aAAH,CAAiB,CAAjB,CAAhB;AAAqC,aAAOyE,OAAO,CAACC,cAAR,EAAP;AAAkC,KAAvM;AACAjG,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,iCAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASkF,oCAAT,CAA8CtF,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiBuE,0CAAjB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,KAAnE,EAA0E,CAA1E;AACA5F,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiBwE,0CAAjB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,KAAnE,EAA0E,CAA1E;AACA7F,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMuF,MAAM,GAAGnG,EAAE,CAACuB,aAAH,EAAf;AACA,QAAIC,OAAJ;AACA,QAAImB,OAAJ;AACA3C,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,EAAE,CAACF,OAAO,GAAG2E,MAAM,CAACxE,iBAAP,CAAyBC,GAAzB,CAA6B,oBAA7B,CAAX,KAAkE,IAAlE,GAAyE,IAAzE,GAAgFJ,OAAO,CAAC4E,KAA1F,CAAtB;AACApG,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,EAAE,CAACiB,OAAO,GAAGwD,MAAM,CAACxE,iBAAP,CAAyBC,GAAzB,CAA6B,YAA7B,CAAX,KAA0D,IAA1D,GAAiE,IAAjE,GAAwEe,OAAO,CAACyD,KAAlF,CAAtB;AACH;AAAE;;AACH,SAASC,2CAAT,CAAqDzF,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb,EAAgB,KAAhB,EAAuB,GAAvB;AACH;;AAAC,MAAIL,EAAE,GAAG,CAAT,EAAY;AACV,UAAM0F,OAAO,GAAGtG,EAAE,CAACuB,aAAH,CAAiB,CAAjB,CAAhB;AACAvB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,KAAd,EAAqB4E,OAAO,CAACC,aAA7B,EAA4CvG,EAAE,CAAC6D,aAA/C;AACH;AAAE;;AACH,SAAS2C,2CAAT,CAAqD5F,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,uEAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASyF,2CAAT,CAAqD7F,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,yEAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAAS0F,yCAAT,CAAmD9F,EAAnD,EAAuDC,GAAvD,EAA4D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACtE,UAAM+F,IAAI,GAAG3G,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,GAArB,EAA0B,EAA1B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS0D,4DAAT,GAAwE;AAAE5G,MAAAA,EAAE,CAACoD,aAAH,CAAiBuD,IAAjB;AAAwB,YAAME,OAAO,GAAG7G,EAAE,CAACuB,aAAH,CAAiB,CAAjB,CAAhB;AAAqC,aAAOsF,OAAO,CAACzC,WAAR,CAAoB,WAApB,CAAP;AAA0C,KAAxM;AACApE,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,SAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAAS8F,oCAAT,CAA8ClG,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMmG,IAAI,GAAG/G,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,GAA9B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,YAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACiB,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,GAAzB;AACAjB,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,GAA9B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,UAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,GAA9B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,QAAd,EAAwB,SAAS8D,4DAAT,CAAsExC,MAAtE,EAA8E;AAAExE,MAAAA,EAAE,CAACoD,aAAH,CAAiB2D,IAAjB;AAAwB,YAAME,OAAO,GAAGjH,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO0F,OAAO,CAACC,cAAR,CAAuB1C,MAAvB,EAA+B,WAA/B,CAAP;AAAqD,KAAzN;AACAxE,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBgF,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,GAA5E;AACArG,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBmF,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACAxG,IAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBoF,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACAzG,IAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBqF,yCAAlB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,GAAnE,EAAwE,EAAxE;AACA1G,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMuG,MAAM,GAAGnH,EAAE,CAACuB,aAAH,EAAf;AACA,QAAIqB,OAAJ;AACA,QAAIwE,OAAJ;AACA,QAAIC,OAAJ;AACArH,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,UAAd,EAA0ByF,MAAM,CAACG,UAAjC;AACAtH,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsByF,MAAM,CAACZ,aAA7B;AACAvG,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACkB,OAAO,GAAGuE,MAAM,CAACxF,iBAAP,CAAyBC,GAAzB,CAA6B,WAA7B,CAAX,KAAyD,IAAzD,GAAgE,IAAhE,GAAuEgB,OAAO,CAACf,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCe,OAAO,CAACf,MAAR,CAAe0F,iBAA5I;AACAvH,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAAC0F,OAAO,GAAGD,MAAM,CAACxF,iBAAP,CAAyBC,GAAzB,CAA6B,WAA7B,CAAX,KAAyD,IAAzD,GAAgE,IAAhE,GAAuEwF,OAAO,CAACvF,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCuF,OAAO,CAACvF,MAAR,CAAe2F,wBAA5I;AACAxH,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACyF,MAAM,CAACM,qBAAR,IAAiCN,MAAM,CAACO,cAAxC,IAA0D,EAAE,CAACL,OAAO,GAAGF,MAAM,CAACxF,iBAAP,CAAyBC,GAAzB,CAA6B,WAA7B,CAAX,KAAyD,IAAzD,GAAgE,IAAhE,GAAuEyF,OAAO,CAACxF,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCwF,OAAO,CAACxF,MAAR,CAAe0F,iBAAxH,CAAhF;AACH;AAAE;;AACH,SAASI,oCAAT,CAA8C/G,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMgH,IAAI,GAAG5H,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB,EAA2B,EAA3B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,cAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,eAArB,EAAsC,EAAtC;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,cAAd,EAA8B,SAAS2E,0EAAT,CAAoFrD,MAApF,EAA4F;AAAExE,MAAAA,EAAE,CAACoD,aAAH,CAAiBwE,IAAjB;AAAwB,YAAME,OAAO,GAAG9H,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOuG,OAAO,CAACpD,OAAR,CAAgBF,MAAhB,EAAwB,WAAxB,CAAP;AAA8C,KAAtO,EAAwO,aAAxO,EAAuP,SAASuD,yEAAT,GAAqF;AAAE/H,MAAAA,EAAE,CAACoD,aAAH,CAAiBwE,IAAjB;AAAwB,YAAMI,OAAO,GAAGhI,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOyG,OAAO,CAACnD,OAAR,EAAP;AAA2B,KAAra,EAAua,cAAva,EAAub,SAASoD,0EAAT,GAAsF;AAAEjI,MAAAA,EAAE,CAACoD,aAAH,CAAiBwE,IAAjB;AAAwB,YAAMM,OAAO,GAAGlI,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO2G,OAAO,CAAClD,WAAR,EAAP;AAA+B,KAA1mB,EAA4mB,iBAA5mB,EAA+nB,SAASmD,6EAAT,GAAyF;AAAEnI,MAAAA,EAAE,CAACoD,aAAH,CAAiBwE,IAAjB;AAAwB,YAAMQ,OAAO,GAAGpI,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO6G,OAAO,CAACjD,SAAR,EAAP;AAA6B,KAAnzB;AACAnF,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASmF,4DAAT,GAAwE;AAAErI,MAAAA,EAAE,CAACoD,aAAH,CAAiBwE,IAAjB;AAAwB,YAAMU,OAAO,GAAGtI,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO+G,OAAO,CAAChD,gBAAR,CAAyB,WAAzB,CAAP;AAA+C,KAA5M;AACAtF,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,MAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASqF,4DAAT,GAAwE;AAAEvI,MAAAA,EAAE,CAACoD,aAAH,CAAiBwE,IAAjB;AAAwB,YAAMY,OAAO,GAAGxI,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOiH,OAAO,CAAC/C,WAAR,CAAoB,WAApB,CAAP;AAA0C,KAAvM;AACAzF,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM6H,OAAO,GAAGzI,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,mBAAd,EAAmC+G,OAAO,CAACC,iBAA3C,EAA8D,aAA9D,EAA6E,IAAI,CAAjF,EAAoF,qBAApF,EAA2G,IAA3G;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8C/H,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMgI,IAAI,GAAG5I,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS2F,4DAAT,GAAwE;AAAE7I,MAAAA,EAAE,CAACoD,aAAH,CAAiBwF,IAAjB;AAAwB,YAAME,OAAO,GAAG9I,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOuH,OAAO,CAACC,iBAAR,EAAP;AAAqC,KAAlM;AACA/I,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,MAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,MAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASgI,oCAAT,CAA8CpI,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMqI,IAAI,GAAGjJ,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASgG,yDAAT,GAAqE;AAAE,YAAMC,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiB6F,IAAjB,CAApB;AAA4C,YAAMG,YAAY,GAAGD,WAAW,CAACE,SAAjC;AAA4C,YAAMC,OAAO,GAAGtJ,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO+H,OAAO,CAACC,eAAR,CAAwBH,YAAxB,EAAsCE,OAAO,CAACE,kBAA9C,EAAkE,kBAAlE,EAAsF,KAAtF,EAA6F,aAA7F,CAAP;AAAqH,KAA/U;AACAxJ,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMwI,YAAY,GAAGvI,GAAG,CAACwI,SAAzB;AACA,UAAMI,OAAO,GAAGzJ,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2BD,OAAO,CAACE,cAAR,CAAuBP,YAAvB,EAAqCK,OAAO,CAACD,kBAA7C,EAAiE,kBAAjE,CAA3B;AACAxJ,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2BR,YAAY,CAACS,YAAxC,EAAsD,GAAtD;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8ClJ,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMmJ,IAAI,GAAG/J,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS8G,yDAAT,GAAqE;AAAE,YAAMb,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiB2G,IAAjB,CAApB;AAA4C,YAAME,UAAU,GAAGd,WAAW,CAACE,SAA/B;AAA0C,YAAMa,OAAO,GAAGlK,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO2I,OAAO,CAACX,eAAR,CAAwBU,UAAxB,EAAoCC,OAAO,CAACC,eAA5C,EAA6D,cAA7D,EAA6E,KAA7E,EAAoF,SAApF,CAAP;AAAwG,KAAhU;AACAnK,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMqJ,UAAU,GAAGpJ,GAAG,CAACwI,SAAvB;AACA,UAAMe,OAAO,GAAGpK,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2BU,OAAO,CAACT,cAAR,CAAuBM,UAAvB,EAAmCG,OAAO,CAACD,eAA3C,EAA4D,cAA5D,CAA3B;AACAnK,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2BK,UAAU,CAACI,OAAtC,EAA+C,GAA/C;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8C1J,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAM2J,IAAI,GAAGvK,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASsH,yDAAT,GAAqE;AAAE,YAAMrB,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiBmH,IAAjB,CAApB;AAA4C,YAAME,QAAQ,GAAGtB,WAAW,CAACE,SAA7B;AAAwC,YAAMqB,OAAO,GAAG1K,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOmJ,OAAO,CAACnB,eAAR,CAAwBkB,QAAxB,EAAkCC,OAAO,CAACC,iBAA1C,EAA6D,kBAA7D,EAAiF,KAAjF,EAAwF,UAAxF,CAAP;AAA6G,KAAnU;AACA3K,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAAC4K,MAAH,CAAU,CAAV,EAAa,WAAb;AACA5K,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM6J,QAAQ,GAAG5J,GAAG,CAACwI,SAArB;AACA,UAAMwB,OAAO,GAAG7K,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2BmB,OAAO,CAAClB,cAAR,CAAuBc,QAAvB,EAAiCI,OAAO,CAACF,iBAAzC,EAA4D,kBAA5D,CAA3B;AACA3K,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2B5J,EAAE,CAAC8K,WAAH,CAAe,CAAf,EAAkB,CAAlB,EAAqBL,QAAQ,CAACZ,YAA9B,CAA3B,EAAwE,GAAxE;AACH;AAAE;;AACH,SAASkB,oCAAT,CAA8CnK,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMoK,IAAI,GAAGhL,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS+H,4DAAT,GAAwE;AAAEjL,MAAAA,EAAE,CAACoD,aAAH,CAAiB4H,IAAjB;AAAwB,YAAME,OAAO,GAAGlL,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO2J,OAAO,CAACC,qBAAR,EAAP;AAAyC,KAAtM;AACAnL,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,MAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,MAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASoK,oCAAT,CAA8CxK,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMyK,IAAI,GAAGrL,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASoI,yDAAT,GAAqE;AAAE,YAAMnC,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiBiI,IAAjB,CAApB;AAA4C,YAAME,QAAQ,GAAGpC,WAAW,CAACE,SAA7B;AAAwC,YAAMmC,OAAO,GAAGxL,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOiK,OAAO,CAACjC,eAAR,CAAwBgC,QAAxB,EAAkCC,OAAO,CAACC,cAA1C,EAA0D,YAA1D,EAAwE,IAAxE,EAA8E,QAA9E,CAAP;AAAiG,KAAvT;AACAzL,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM2K,QAAQ,GAAG1K,GAAG,CAACwI,SAArB;AACA,UAAMqC,OAAO,GAAG1L,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2BgC,OAAO,CAACC,yBAAR,CAAkCJ,QAAlC,EAA4CG,OAAO,CAACD,cAApD,EAAoE,IAApE,EAA0E,QAA1E,CAA3B;AACAzL,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2B2B,QAAQ,CAACK,QAApC,EAA8C,GAA9C;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8CjL,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMkL,IAAI,GAAG9L,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS6I,yDAAT,GAAqE;AAAE,YAAM5C,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiB0I,IAAjB,CAApB;AAA4C,YAAME,QAAQ,GAAG7C,WAAW,CAACE,SAA7B;AAAwC,YAAM4C,OAAO,GAAGjM,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO0K,OAAO,CAAC1C,eAAR,CAAwByC,QAAxB,EAAkCC,OAAO,CAACC,iBAA1C,EAA6D,gBAA7D,EAA+E,IAA/E,EAAqF,WAArF,CAAP;AAA2G,KAAjU;AACAlM,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMoL,QAAQ,GAAGnL,GAAG,CAACwI,SAArB;AACA,UAAM8C,OAAO,GAAGnM,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2ByC,OAAO,CAACR,yBAAR,CAAkCK,QAAlC,EAA4CG,OAAO,CAACD,iBAApD,EAAuE,IAAvE,EAA6E,WAA7E,CAA3B;AACAlM,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2BoC,QAAQ,CAACI,QAApC,EAA8C,GAA9C;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8CzL,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAM0L,IAAI,GAAGtM,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASqJ,yDAAT,GAAqE;AAAE,YAAMpD,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiBkJ,IAAjB,CAApB;AAA4C,YAAME,QAAQ,GAAGrD,WAAW,CAACE,SAA7B;AAAwC,YAAMoD,OAAO,GAAGzM,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOkL,OAAO,CAAClD,eAAR,CAAwBiD,QAAxB,EAAkCC,OAAO,CAACC,gBAA1C,EAA4D,cAA5D,EAA4E,IAA5E,EAAkF,UAAlF,CAAP;AAAuG,KAA7T;AACA1M,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM4L,QAAQ,GAAG3L,GAAG,CAACwI,SAArB;AACA,UAAMsD,OAAO,GAAG3M,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2BiD,OAAO,CAAChB,yBAAR,CAAkCa,QAAlC,EAA4CG,OAAO,CAACD,gBAApD,EAAsE,IAAtE,EAA4E,UAA5E,CAA3B;AACA1M,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2B4C,QAAQ,CAACI,QAApC,EAA8C,GAA9C;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8CjM,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAMkM,IAAI,GAAG9M,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS6J,yDAAT,GAAqE;AAAE,YAAM5D,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiB0J,IAAjB,CAApB;AAA4C,YAAME,QAAQ,GAAG7D,WAAW,CAACE,SAA7B;AAAwC,YAAM4D,OAAO,GAAGjN,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAO0L,OAAO,CAAC1D,eAAR,CAAwByD,QAAxB,EAAkCC,OAAO,CAACC,yBAA1C,EAAqE,uBAArE,EAA8F,IAA9F,EAAoG,oBAApG,CAAP;AAAmI,KAAzV;AACAlN,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMoM,QAAQ,GAAGnM,GAAG,CAACwI,SAArB;AACA,UAAM8D,OAAO,GAAGnN,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2ByD,OAAO,CAACxB,yBAAR,CAAkCqB,QAAlC,EAA4CG,OAAO,CAACD,yBAApD,EAA+E,IAA/E,EAAqF,oBAArF,CAA3B;AACAlN,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2BoD,QAAQ,CAACI,QAApC,EAA8C,GAA9C;AACH;AAAE;;AACH,SAASC,oCAAT,CAA8CzM,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjE,UAAM0M,IAAI,GAAGtN,EAAE,CAACiD,gBAAH,EAAb;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASqK,yDAAT,GAAqE;AAAE,YAAMpE,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiBkK,IAAjB,CAApB;AAA4C,YAAME,QAAQ,GAAGrE,WAAW,CAACE,SAA7B;AAAwC,YAAMoE,OAAO,GAAGzN,EAAE,CAACuB,aAAH,EAAhB;AAAoC,aAAOkM,OAAO,CAAClE,eAAR,CAAwBiE,QAAxB,EAAkCC,OAAO,CAACC,kBAA1C,EAA8D,gBAA9D,EAAgF,IAAhF,EAAsF,YAAtF,CAAP;AAA6G,KAAnU;AACA1N,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM4M,QAAQ,GAAG3M,GAAG,CAACwI,SAArB;AACA,UAAMsE,OAAO,GAAG3N,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2BiE,OAAO,CAAChC,yBAAR,CAAkC6B,QAAlC,EAA4CG,OAAO,CAACD,kBAApD,EAAwE,IAAxE,EAA8E,gBAA9E,CAA3B;AACA1N,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2B4D,QAAQ,CAACI,QAApC,EAA8C,GAA9C;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+CjN,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMkN,KAAK,GAAG9N,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS6K,0DAAT,GAAsE;AAAE,YAAM5E,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiB0K,KAAjB,CAApB;AAA6C,YAAME,QAAQ,GAAG7E,WAAW,CAACE,SAA7B;AAAwC,YAAM4E,QAAQ,GAAGjO,EAAE,CAACuB,aAAH,EAAjB;AAAqC,aAAO0M,QAAQ,CAAC1E,eAAT,CAAyByE,QAAzB,EAAmCC,QAAQ,CAACC,kBAA5C,EAAgE,oBAAhE,EAAsF,IAAtF,EAA4F,YAA5F,CAAP;AAAmH,KAA5U;AACAlO,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMoN,QAAQ,GAAGnN,GAAG,CAACwI,SAArB;AACA,UAAM8E,OAAO,GAAGnO,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2ByE,OAAO,CAACxC,yBAAR,CAAkCqC,QAAlC,EAA4CG,OAAO,CAACD,kBAApD,EAAwE,IAAxE,EAA8E,oBAA9E,CAA3B;AACAlO,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2BoE,QAAQ,CAACJ,QAApC,EAA8C,GAA9C;AACH;AAAE;;AACH,SAASQ,qCAAT,CAA+CxN,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMyN,KAAK,GAAGrO,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASoL,0DAAT,GAAsE;AAAE,YAAMnF,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiBiL,KAAjB,CAApB;AAA6C,YAAME,SAAS,GAAGpF,WAAW,CAACE,SAA9B;AAAyC,YAAMmF,QAAQ,GAAGxO,EAAE,CAACuB,aAAH,EAAjB;AAAqC,aAAOiN,QAAQ,CAACjF,eAAT,CAAyBgF,SAAzB,EAAoCC,QAAQ,CAACC,gBAA7C,EAA+D,YAA/D,EAA6E,IAA7E,EAAmF,iBAAnF,CAAP;AAA+G,KAAzU;AACAzO,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,GAA7B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM2N,SAAS,GAAG1N,GAAG,CAACwI,SAAtB;AACA,UAAMqF,OAAO,GAAG1O,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAAC0J,WAAH,CAAe,UAAf,EAA2BgF,OAAO,CAAC/C,yBAAR,CAAkC4C,SAAlC,EAA6CG,OAAO,CAACD,gBAArD,EAAuE,IAAvE,EAA6E,YAA7E,CAA3B;AACAzO,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2B2E,SAAS,CAACX,QAArC,EAA+C,GAA/C;AACH;AAAE;;AACH,SAASe,qCAAT,CAA+C/N,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMgO,KAAK,GAAG5O,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS2L,6DAAT,GAAyE;AAAE7O,MAAAA,EAAE,CAACoD,aAAH,CAAiBwL,KAAjB;AAAyB,YAAME,QAAQ,GAAG9O,EAAE,CAACuB,aAAH,EAAjB;AAAqC,UAAIwN,OAAJ;AAAa,aAAOD,QAAQ,CAACE,UAAT,CAAoB,aAApB,EAAmC,CAACD,OAAO,GAAGD,QAAQ,CAACnN,iBAAT,CAA2BC,GAA3B,CAA+B,aAA/B,CAAX,KAA6D,IAA7D,GAAoE,IAApE,GAA2EmN,OAAO,CAAC3I,KAAtH,CAAP;AAAsI,KAAnT;AACApG,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,UAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMqO,OAAO,GAAGjP,EAAE,CAACuB,aAAH,EAAhB;AACA,QAAIC,OAAJ;AACAxB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2BqF,OAAO,CAACC,YAAR,CAAqB,CAAC1N,OAAO,GAAGyN,OAAO,CAACtN,iBAAR,CAA0BC,GAA1B,CAA8B,aAA9B,CAAX,KAA4D,IAA5D,GAAmE,IAAnE,GAA0EJ,OAAO,CAAC4E,KAAvG,EAA8G,aAA9G,CAA3B,EAAyJ,GAAzJ;AACH;AAAE;;AACH,SAAS+I,0CAAT,CAAoDvO,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvE,UAAMwO,KAAK,GAAGpP,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB,EAA2B,GAA3B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASmM,8DAAT,GAA0E;AAAE,YAAMlG,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiBgM,KAAjB,CAApB;AAA6C,YAAME,SAAS,GAAGnG,WAAW,CAACE,SAA9B;AAAyC,YAAMkG,QAAQ,GAAGvP,EAAE,CAACuB,aAAH,CAAiB,CAAjB,CAAjB;AAAsC,aAAOgO,QAAQ,CAACC,OAAT,CAAiBF,SAAjB,EAA4B,aAA5B,CAAP;AAAoD,KAAnR;AACAtP,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM0O,SAAS,GAAGzO,GAAG,CAACwI,SAAtB;AACArJ,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2B0F,SAAS,CAACG,SAArC,EAAgD,GAAhD;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+C9O,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB;AACAd,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiB8N,0CAAjB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,IAAnE,EAAyE,GAAzE;AACAnP,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM+O,OAAO,GAAG3P,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBiO,OAAO,CAACC,yBAAjC;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+CjP,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,mCAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAAS8O,qCAAT,CAA+ClP,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMmP,KAAK,GAAG/P,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS8M,6DAAT,GAAyE;AAAEhQ,MAAAA,EAAE,CAACoD,aAAH,CAAiB2M,KAAjB;AAAyB,YAAME,QAAQ,GAAGjQ,EAAE,CAACuB,aAAH,EAAjB;AAAqC,UAAIwN,OAAJ;AAAa,aAAOkB,QAAQ,CAACjB,UAAT,CAAoB,aAApB,EAAmC,CAACD,OAAO,GAAGkB,QAAQ,CAACtO,iBAAT,CAA2BC,GAA3B,CAA+B,aAA/B,CAAX,KAA6D,IAA7D,GAAoE,IAApE,GAA2EmN,OAAO,CAAC3I,KAAtH,CAAP;AAAsI,KAAnT;AACApG,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,UAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMsP,OAAO,GAAGlQ,EAAE,CAACuB,aAAH,EAAhB;AACA,QAAIC,OAAJ;AACAxB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2BsG,OAAO,CAAChB,YAAR,CAAqB,CAAC1N,OAAO,GAAG0O,OAAO,CAACvO,iBAAR,CAA0BC,GAA1B,CAA8B,aAA9B,CAAX,KAA4D,IAA5D,GAAmE,IAAnE,GAA0EJ,OAAO,CAAC4E,KAAvG,EAA8G,aAA9G,CAA3B,EAAyJ,GAAzJ;AACH;AAAE;;AACH,SAAS+J,0CAAT,CAAoDvP,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvE,UAAMwP,KAAK,GAAGpQ,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB,EAA2B,GAA3B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASmN,8DAAT,GAA0E;AAAE,YAAMlH,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiBgN,KAAjB,CAApB;AAA6C,YAAME,SAAS,GAAGnH,WAAW,CAACE,SAA9B;AAAyC,YAAMkH,QAAQ,GAAGvQ,EAAE,CAACuB,aAAH,CAAiB,CAAjB,CAAjB;AAAsC,aAAOgP,QAAQ,CAACf,OAAT,CAAiBc,SAAjB,EAA4B,aAA5B,CAAP;AAAoD,KAAnR;AACAtQ,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM0P,SAAS,GAAGzP,GAAG,CAACwI,SAAtB;AACArJ,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2B0G,SAAS,CAACE,OAArC,EAA8C,GAA9C;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+C7P,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB;AACAd,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiB8O,0CAAjB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,IAAnE,EAAyE,GAAzE;AACAnQ,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM8P,OAAO,GAAG1Q,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBgP,OAAO,CAACC,qBAAjC;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+ChQ,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,mCAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAAS6P,qCAAT,CAA+CjQ,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMkQ,KAAK,GAAG9Q,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS6N,6DAAT,GAAyE;AAAE/Q,MAAAA,EAAE,CAACoD,aAAH,CAAiB0N,KAAjB;AAAyB,YAAME,QAAQ,GAAGhR,EAAE,CAACuB,aAAH,EAAjB;AAAqC,aAAOyP,QAAQ,CAACC,sBAAT,EAAP;AAA2C,KAA3M;AACAjR,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,UAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMsQ,OAAO,GAAGlR,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2BsH,OAAO,CAACC,gBAAnC,EAAqD,GAArD;AACH;AAAE;;AACH,SAASC,0CAAT,CAAoDxQ,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvE,UAAMyQ,KAAK,GAAGrR,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB,EAA2B,GAA3B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASoO,8DAAT,GAA0E;AAAE,YAAMnI,WAAW,GAAGnJ,EAAE,CAACoD,aAAH,CAAiBiO,KAAjB,CAApB;AAA6C,YAAME,aAAa,GAAGpI,WAAW,CAACE,SAAlC;AAA6C,YAAMmI,QAAQ,GAAGxR,EAAE,CAACuB,aAAH,CAAiB,CAAjB,CAAjB;AAAsC,aAAOiQ,QAAQ,CAACC,cAAT,CAAwBF,aAAxB,CAAP;AAAgD,KAAnR;AACAvR,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM2Q,aAAa,GAAG1Q,GAAG,CAACwI,SAA1B;AACArJ,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC4J,kBAAH,CAAsB,GAAtB,EAA2B2H,aAA3B,EAA0C,GAA1C;AACH;AAAE;;AACH,SAASG,qCAAT,CAA+C9Q,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB;AACAd,IAAAA,EAAE,CAACqB,UAAH,CAAc,CAAd,EAAiB+P,0CAAjB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,IAAnE,EAAyE,GAAzE;AACApR,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM+Q,OAAO,GAAG3R,EAAE,CAACuB,aAAH,EAAhB;AACAvB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBiQ,OAAO,CAACC,iBAAjC;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+CjR,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMkR,KAAK,GAAG9R,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS6O,6DAAT,GAAyE;AAAE/R,MAAAA,EAAE,CAACoD,aAAH,CAAiB0O,KAAjB;AAAyB,YAAME,QAAQ,GAAGhS,EAAE,CAACuB,aAAH,EAAjB;AAAqC,aAAOyQ,QAAQ,CAACC,gBAAT,EAAP;AAAqC,KAArM;AACAjS,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,QAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAASkR,qCAAT,CAA+CtR,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMuR,KAAK,GAAGnS,EAAE,CAACiD,gBAAH,EAAd;;AACAjD,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,GAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASkP,6DAAT,GAAyE;AAAEpS,MAAAA,EAAE,CAACoD,aAAH,CAAiB+O,KAAjB;AAAyB,YAAME,QAAQ,GAAGrS,EAAE,CAACuB,aAAH,EAAjB;AAAqC,aAAO8Q,QAAQ,CAACC,kBAAT,EAAP;AAAuC,KAAvM;AACAtS,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,MAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,GAA/B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,MAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,OAAO,MAAMuR,oBAAN,CAA2B;AAC9BC,EAAAA,WAAW,CAACC,MAAD,EAASC,WAAT,EAAsBC,mBAAtB,EAA2CC,MAA3C,EAAmDC,WAAnD,EAAgEC,UAAhE,EAA4EC,iBAA5E,EAA+FC,KAA/F,EAAsG;AAC7G,SAAKP,MAAL,GAAcA,MAAd;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,mBAAL,GAA2BA,mBAA3B;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,CAAL,GAAS,CAAT;AACA,SAAK3L,UAAL,GAAkB,KAAlB;AACA,SAAK4L,OAAL,GAAe,4CAAf;AACA,SAAK1J,kBAAL,GAA0B,EAA1B;AACA,SAAKW,eAAL,GAAuB,EAAvB;AACA,SAAKQ,iBAAL,GAAyB,EAAzB;AACA,SAAKc,cAAL,GAAsB,EAAtB;AACA,SAAKS,iBAAL,GAAyB,EAAzB;AACA,SAAKQ,gBAAL,GAAwB,EAAxB;AACA,SAAKgB,kBAAL,GAA0B,EAA1B;AACA,SAAKQ,kBAAL,GAA0B,EAA1B;AACA,SAAKO,gBAAL,GAAwB,EAAxB;AACA,SAAKvB,yBAAL,GAAiC,EAAjC;AACA,SAAKiG,iBAAL,GAAyB,EAAzB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,YAAL,GAAoB,EAApB;AACA,SAAKC,OAAL,GAAe,EAAf;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,SAAL,GAAiB,EAAjB;AACA,SAAKC,iBAAL,GAAyB,EAAzB;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,UAAL,GAAkB,EAAlB;AACA,SAAK/D,yBAAL,GAAiC,EAAjC;AACA,SAAKe,qBAAL,GAA6B,EAA7B;AACA,SAAKiB,iBAAL,GAAyB,EAAzB;AACA,SAAKgC,YAAL,GAAoB,KAApB,CAlC6G,CAkClF;;AAC3B,SAAKC,oBAAL,GAA4B,KAA5B;AACA,SAAKC,YAAL,GAAoB,KAApB;AACA,SAAKC,cAAL,GAAsB,CAClB;AACIC,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KADkB,EAKlB;AACIoG,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KALkB,EASlB;AACIoG,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KATkB,EAalB;AACIoG,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KAbkB,CAAtB;AAkBA,SAAKqG,QAAL,GAAgB,CACZ;AACID,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KADY,EAKZ;AACIoG,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KALY,EASZ;AACIoG,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KATY,CAaZ;AACA;AACA;AACA;AAhBY,KAAhB;AAkBA,SAAKsG,gBAAL,GAAwB,CACpB;AACIF,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KADoB,EAKpB;AACIoG,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KALoB,EASpB;AACIoG,MAAAA,MAAM,EAAE,GADZ;AAEIpG,MAAAA,QAAQ,EAAE;AAFd,KAToB,CAapB;AACA;AACA;AACA;AAhBoB,KAAxB;AAkBA,SAAKjI,YAAL,GAAoB,EAApB;AACA,SAAK+C,iBAAL,GAAyB,EAAzB;AACA,SAAKyL,gBAAL,GAAwB,KAAxB;AACA,SAAK1M,qBAAL,GAA6B,KAA7B;AACA,SAAK9F,iBAAL,GAAyB,KAAK+Q,WAAL,CAAiB0B,KAAjB,CAAuB;AAC5CC,MAAAA,MAAM,EAAE,CAAC,EAAD,EAAK7U,UAAU,CAACsC,QAAhB,CADoC;AAE5CwS,MAAAA,OAAO,EAAE,CAAC,EAAD,EAAK,CAAC9U,UAAU,CAACsC,QAAZ,EAAsB/B,aAAa,CAACwU,oBAApC,CAAL,CAFmC;AAG5CC,MAAAA,UAAU,EAAE,CACR,EADQ,EAER,CACIhV,UAAU,CAACsC,QADf,EAEItC,UAAU,CAACiV,SAAX,CAAqB,CAArB,CAFJ,EAGIjV,UAAU,CAACsD,OAAX,CAAmB,gGAAnB,CAHJ,CAFQ,CAHgC;AAW5C4R,MAAAA,mBAAmB,EAAE,CAAC,GAAD,CAXuB;AAY5CC,MAAAA,cAAc,EAAE,CAAC,EAAD,EAAKnV,UAAU,CAACsC,QAAhB,CAZ4B;AAa5C8S,MAAAA,mBAAmB,EAAE,CAAC,KAAD,CAbuB;AAc5CC,MAAAA,UAAU,EAAE,CAAC,EAAD,EAAKrV,UAAU,CAACsC,QAAhB,CAdgC;AAe5CgT,MAAAA,eAAe,EAAE,CAAC,KAAD,CAf2B;AAgB5CC,MAAAA,cAAc,EAAE,CAAC,EAAD,EAAKvV,UAAU,CAACsC,QAAhB,CAhB4B;AAiB5CkT,MAAAA,mBAAmB,EAAE,CAAC,KAAD,CAjBuB;AAkB5CC,MAAAA,YAAY,EAAE,CAAC,EAAD,EAAKzV,UAAU,CAACsC,QAAhB,CAlB8B;AAmB5CoT,MAAAA,iBAAiB,EAAE,CAAC,KAAD,CAnByB;AAoB5CC,MAAAA,qBAAqB,EAAE,CAAC,EAAD,EAAK3V,UAAU,CAACsC,QAAhB,CApBqB;AAqB5CsT,MAAAA,kBAAkB,EAAE,CAAC,KAAD,CArBwB;AAsB5CC,MAAAA,WAAW,EAAE,CAAC,EAAD,CAtB+B;AAuB5CC,MAAAA,UAAU,EAAE,CAAC,EAAD,CAvBgC;AAwB5CC,MAAAA,iBAAiB,EAAE,CAAC,KAAD,CAxByB;AAyB5CC,MAAAA,kBAAkB,EAAE,CAAC,EAAD,EAAKhW,UAAU,CAACsC,QAAhB,CAzBwB;AA0B5C2T,MAAAA,yBAAyB,EAAE,CAAC,KAAD,CA1BiB;AA2B5CC,MAAAA,IAAI,EAAE,CAAC,IAAD,CA3BsC;AA4B5CC,MAAAA,UAAU,EAAE,CAAC,EAAD,EAAKnW,UAAU,CAACsC,QAAhB,CA5BgC;AA6B5C8T,MAAAA,iBAAiB,EAAE,CAAC,KAAD,CA7ByB;AA8B5CC,MAAAA,WAAW,EAAE,CAAC,EAAD,CA9B+B;AA+B5CC,MAAAA,gBAAgB,EAAE,KAAKpD,WAAL,CAAiBqD,KAAjB,CAAuB,EAAvB,CA/B0B;AAgC5CC,MAAAA,YAAY,EAAE,KAAKtD,WAAL,CAAiBqD,KAAjB,CAAuB,EAAvB,EAA2BvW,UAAU,CAACsC,QAAtC,CAhC8B;AAiC5CmU,MAAAA,UAAU,EAAE,CAAC,KAAD,CAjCgC;AAkC5CC,MAAAA,kBAAkB,EAAE,CAAC,KAAD,CAlCwB;AAmC5CC,MAAAA,WAAW,EAAE,CAAC,EAAD,CAnC+B;AAoC5CC,MAAAA,SAAS,EAAE,CAAC,EAAD;AApCiC,KAAvB,CAAzB;AAsCA,SAAKpD,KAAL,CAAWqD,WAAX,CAAuBC,SAAvB,CAAiCC,MAAM,IAAI;AACvC,UAAIA,MAAJ,EAAY;AACR,aAAKC,IAAL,GAAYD,MAAM,CAAC,MAAD,CAAlB;AACA,aAAKE,KAAL,GAAaF,MAAM,CAAC,OAAD,CAAnB;AACAG,QAAAA,OAAO,CAACC,GAAR,CAAY,KAAKH,IAAjB;AACH,OAJD,MAKK;AACD,aAAK/D,MAAL,CAAYmE,QAAZ,CAAqB,CAAC,oBAAD,CAArB;AACH;AACJ,KATD;AAUH;;AACDC,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKJ,KAAL,KAAe,MAAnB,EAA2B;AACvB,WAAKK,kBAAL,CAAwB,KAAKN,IAA7B,EAAmCO,IAAnC,CAAwCC,GAAG,IAAI;AAC3C,aAAKC,aAAL,CAAmBD,GAAnB;AACH,OAFD,EAEGE,KAFH,CAESC,KAAK,IAAI;AACdT,QAAAA,OAAO,CAACS,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;AACH,OAJD;AAKH;;AACD,SAAKC,qBAAL;;AACA,QAAI,KAAKX,KAAL,KAAe,MAAnB,EAA2B;AACvB,WAAKY,eAAL;AACA,WAAKC,WAAL;AACA,WAAKC,gBAAL;AACH;;AACD,SAAKC,uBAAL,GAA+B,IAAIjY,WAAJ,CAAgB,EAAhB,CAA/B;AACA,SAAKkY,mBAAL,GAA2B,IAAIlY,WAAJ,CAAgB,EAAhB,CAA3B;AACA,SAAKmY,qBAAL,GAA6B,IAAInY,WAAJ,CAAgB,EAAhB,CAA7B;AACA,SAAKoY,YAAL;AACA,SAAKD,qBAAL,CAA2BE,YAA3B,CACKC,IADL,CACUpY,YAAY,CAAC,GAAD,CADtB,EAC6BC,oBAAoB,EADjD,EACqDC,SAAS,CAAEyG,KAAD,IAAW,KAAK0R,cAAL,CAAoB1R,KAApB,CAAZ,CAD9D,EAEKkQ,SAFL,CAEgByB,SAAD,IAAe;AAC1B,WAAKnG,iBAAL,GAAyBmG,SAAzB;AACA,WAAKlE,oBAAL,GAA4BkE,SAAS,CAACC,MAAV,GAAmB,CAA/C;AACH,KALD;;AAMA,QAAI,CAAC,KAAKrW,iBAAL,CAAuBC,GAAvB,CAA2B,YAA3B,CAAL,EAA+C;AAC3C,WAAKD,iBAAL,CAAuBsW,UAAvB,CAAkC,YAAlC,EAAgD,IAAI1Y,WAAJ,CAAgB,EAAhB,EAAoBC,UAAU,CAACsC,QAA/B,CAAhD;AACH;AACJ;;AACDgV,EAAAA,kBAAkB,CAACN,IAAD,EAAO;AACrB,WAAO,IAAI0B,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,WAAKzF,mBAAL,CAAyBmE,kBAAzB,CAA4CN,IAA5C,EAAkDF,SAAlD,CAA6DU,GAAD,IAAS;AACjE,YAAIA,GAAG,CAACqB,IAAR,EAAc;AACV,eAAKC,QAAL,GAAgBtB,GAAG,CAACqB,IAApB;AACA3B,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6B,KAAK2B,QAAlC;AACAH,UAAAA,OAAO,CAAC,KAAKG,QAAN,CAAP,CAHU,CAGc;AAC3B,SAJD,MAKK;AACDF,UAAAA,MAAM,CAAC,eAAD,CAAN,CADC,CACwB;AAC5B;AACJ,OATD,EASIjB,KAAD,IAAW;AACV,aAAKpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA,aAAK3F,MAAL,CAAYuE,KAAZ,CAAkB,uBAAlB;AACAT,QAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBQ,KAArB;AACAiB,QAAAA,MAAM,CAACjB,KAAD,CAAN;AACH,OAdD;AAeH,KAhBM,CAAP;AAiBH;;AACKF,EAAAA,aAAa,CAACoB,IAAD,EAAO;AAAA;;AAAA;AACtB,UAAI;AACA,cAAM,KAAI,CAACf,WAAL,EAAN;AACA,cAAM,KAAI,CAACD,eAAL,EAAN;AACA,cAAM,KAAI,CAACE,gBAAL,EAAN;;AACA,QAAA,KAAI,CAAC5V,iBAAL,CAAuB6W,UAAvB,CAAkC;AAC9BnE,UAAAA,MAAM,EAAEgE,IAAI,CAAChE,MADiB;AAE9BC,UAAAA,OAAO,EAAE+D,IAAI,CAAC/D,OAFgB;AAG9BE,UAAAA,UAAU,EAAE6D,IAAI,CAAC7D,UAHa;AAI9BE,UAAAA,mBAAmB,EAAE2D,IAAI,CAAC3D,mBAJI;AAK9BE,UAAAA,mBAAmB,EAAEyD,IAAI,CAACzD,mBALI;AAM9BE,UAAAA,eAAe,EAAEuD,IAAI,CAACvD,eANQ;AAO9BE,UAAAA,mBAAmB,EAAEqD,IAAI,CAACrD,mBAPI;AAQ9BE,UAAAA,iBAAiB,EAAEmD,IAAI,CAACnD,iBARM;AAS9BE,UAAAA,kBAAkB,EAAEiD,IAAI,CAACjD,kBATK;AAU9BC,UAAAA,WAAW,EAAEgD,IAAI,CAAChD,WAVY;AAW9BC,UAAAA,UAAU,EAAE+C,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC/C,UAX/B;AAY9BC,UAAAA,iBAAiB,EAAE8C,IAAI,CAAC9C,iBAAL,GAAyB8C,IAAI,CAAC9C,iBAA9B,GAAkD,KAZvC;AAa9B;AACAM,UAAAA,WAAW,EAAEwC,IAAI,CAACxC,WAdY;AAe9BhB,UAAAA,UAAU,EAAEwD,IAAI,CAACxD,UAfa;AAgB9BF,UAAAA,cAAc,EAAE0D,IAAI,CAAC1D,cAhBS;AAiB9BM,UAAAA,YAAY,EAAEoD,IAAI,CAACpD,YAjBW;AAkB9BE,UAAAA,qBAAqB,EAAEkD,IAAI,CAAClD,qBAlBE;AAmB9BJ,UAAAA,cAAc,EAAEsD,IAAI,CAACtD,cAnBS;AAoB9BS,UAAAA,kBAAkB,EAAE6C,IAAI,CAAC7C,kBApBK;AAqB9BC,UAAAA,yBAAyB,EAAE4C,IAAI,CAAC5C,yBArBF;AAsB9BE,UAAAA,UAAU,EAAE0C,IAAI,CAAC1C,UAtBa;AAuB9BC,UAAAA,iBAAiB,EAAEyC,IAAI,CAACzC,iBAAL,GAAyByC,IAAI,CAACzC,iBAA9B,GAAkD,KAvBvC;AAwB9BM,UAAAA,kBAAkB,EAAEmC,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACnC,kBAxBvC;AAyB9BC,UAAAA,WAAW,EAAEkC,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAClC;AAzBhC,SAAlC;;AA2BA,YAAIkC,IAAI,CAAC3C,IAAT,EAAe;AACX,UAAA,KAAI,CAAC9R,QAAL,GAAgByU,IAAI,CAAC3C,IAArB;AACH;;AACD,YAAI2C,IAAI,CAACjC,SAAT,EAAoB;AAChB,UAAA,KAAI,CAAC7P,aAAL,GAAqB8R,IAAI,CAACjC,SAA1B;AACH;;AACD,YAAIiC,IAAI,CAAChD,WAAT,EAAsB;AAClB,UAAA,KAAI,CAAC7F,OAAL,CAAa6I,IAAI,CAAChD,WAAlB,EAA+B,aAA/B,EAA8C,iBAA9C;AACH;;AACD,YAAIgD,IAAI,CAACxC,WAAT,EAAsB;AAClB,UAAA,KAAI,CAACrG,OAAL,CAAa6I,IAAI,CAACxC,WAAlB,EAA+B,aAA/B,EAA8C,iBAA9C;AACH;;AACD,YAAIwC,IAAI,CAAC/C,UAAT,EAAqB;AACjB,UAAA,KAAI,CAAC7D,cAAL,CAAoB4G,IAAI,CAAC/C,UAAzB;AACH;;AACD,QAAA,KAAI,CAACnL,eAAL,GAAuB,KAAI,CAACkJ,YAAL,CAAkBoF,MAAlB,CAA0BC,MAAD,IAAYL,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACrC,YAAL,CAAkB2C,QAAlB,CAA2BD,MAAM,CAACE,KAAlC,CAAjF,CAAvB,CA9CA,CA+CA;;AACA,QAAA,KAAI,CAACC,eAAL,CAAqB,cAArB,EAAqC,KAAI,CAAC1O,eAA1C,EAA2D,KAA3D,EAhDA,CAiDA;;;AACA,QAAA,KAAI,CAACX,kBAAL,GAA0B6O,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACvC,gBAAL,CAAsB2C,MAAtB,CAA8BK,MAAD,IAAY,CAACA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACC,OAAxD,MAAqE,GAA9G,CAAtE;AACA,QAAA,KAAI,CAACpO,iBAAL,GAAyB0N,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACvC,gBAAL,CAAsB2C,MAAtB,CAA8BK,MAAD,IAAY,CAACA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACC,OAAxD,MAAqE,GAA9G,CAArE,CAnDA,CAoDA;;AACA,QAAA,KAAI,CAACF,eAAL,CAAqB,kBAArB,EAAyC,KAAI,CAACrP,kBAAL,CAAwBwP,MAAxB,CAA+B,KAAI,CAACrO,iBAApC,CAAzC,EAAiG,KAAjG,EArDA,CAsDA;;;AACA,cAAMc,cAAc,GAAG,KAAI,CAAC6H,OAAL,CAAa2F,IAAb,CAAmBC,MAAD,IAAY,CAACA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACC,KAAxD,MAAmEd,IAAI,CAACxD,UAAtG,CAAvB;;AACA,QAAA,KAAI,CAACpJ,cAAL,CAAoB2N,IAApB,CAAyB3N,cAAzB;;AACA,cAAMS,iBAAiB,GAAG,KAAI,CAACqH,WAAL,CAAiB0F,IAAjB,CAAuBI,SAAD,IAAe,CAACA,SAAS,KAAK,IAAd,IAAsBA,SAAS,KAAK,KAAK,CAAzC,GAA6C,KAAK,CAAlD,GAAsDA,SAAS,CAACC,KAAjE,OAA6EjB,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC1D,cAA9H,CAArC,CAA1B;;AACA,QAAA,KAAI,CAACzI,iBAAL,CAAuBkN,IAAvB,CAA4BlN,iBAA5B;;AACA,cAAMQ,gBAAgB,GAAG,KAAI,CAAC8G,SAAL,CAAeyF,IAAf,CAAqBM,QAAD,IAAc,CAACA,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACC,KAA9D,OAA0EnB,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACpD,YAA3H,CAAlC,CAAzB;;AACA,QAAA,KAAI,CAACvI,gBAAL,CAAsB0M,IAAtB,CAA2B1M,gBAA3B;;AACA,cAAMQ,yBAAyB,GAAG,KAAI,CAACuG,iBAAL,CAAuBwF,IAAvB,CAA6BQ,WAAD,IAAiB,CAACA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACC,KAAvE,OAAmFrB,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAClD,qBAApI,CAA7C,CAAlC;;AACA,QAAA,KAAI,CAACjI,yBAAL,CAA+BkM,IAA/B,CAAoClM,yBAApC;;AACA,cAAMQ,kBAAkB,GAAG,KAAI,CAACqG,cAAL,CAAoBkF,IAApB,CAA0BU,UAAD,IAAgB,CAACA,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAAC3F,MAApE,OAAiFqE,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACtD,cAAlI,CAAzC,CAA3B;;AACA,QAAA,KAAI,CAACrH,kBAAL,CAAwB0L,IAAxB,CAA6B1L,kBAA7B;;AACA,cAAMQ,kBAAkB,GAAG,KAAI,CAACgG,gBAAL,CAAsB+E,IAAtB,CAA4BW,UAAD,IAAgB,CAACA,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,UAAU,CAAC5F,MAApE,OAAiFqE,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC7C,kBAAlI,CAA3C,CAA3B;;AACA,QAAA,KAAI,CAACtH,kBAAL,CAAwBkL,IAAxB,CAA6BlL,kBAA7B;;AACA,cAAMO,gBAAgB,GAAG,KAAI,CAACwF,QAAL,CAAcgF,IAAd,CAAoBY,IAAD,IAAU,CAACA,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC7F,MAAlD,OAA+DqE,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC1C,UAAhH,CAA7B,CAAzB;;AACA,QAAA,KAAI,CAAClH,gBAAL,CAAsB2K,IAAtB,CAA2B3K,gBAA3B;AACH,OArED,CAsEA,OAAO0I,KAAP,EAAc;AACVT,QAAAA,OAAO,CAACS,KAAR,CAAc,qBAAd,EAAqCA,KAArC;AACH;;AACD,MAAA,KAAI,CAAC2C,0BAAL;;AACApD,MAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B,KAAI,CAAChV,iBAAL,CAAuByE,KAAnD;AA3EsB;AA4EzB;;AACD2T,EAAAA,sBAAsB,GAAG;AACrBC,IAAAA,MAAM,CAACC,IAAP,CAAY,KAAKtY,iBAAL,CAAuBI,QAAnC,EAA6CmY,OAA7C,CAAsDC,KAAD,IAAW;AAC5D,YAAMC,OAAO,GAAG,KAAKzY,iBAAL,CAAuBC,GAAvB,CAA2BuY,KAA3B,CAAhB;AACAC,MAAAA,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACC,aAAR,CAAsB;AAAEC,QAAAA,QAAQ,EAAE;AAAZ,OAAtB,CAAlD;AACH,KAHD;AAIH;;AACDR,EAAAA,0BAA0B,GAAG;AACzB,QAAIS,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,QAAIC,UAAU,GAAG,CAAjB;AACA,UAAMC,MAAM,GAAG,CACX,KAAKnP,cADM,EAEX,KAAKS,iBAFM,EAGX,KAAKQ,gBAHM,EAIX,KAAKQ,yBAJM,EAKX,KAAKQ,kBALM,EAMX,KAAKQ,kBANM,EAOX,KAAKO,gBAPM,EAQX,KAAK0C,gBAAL,GAAwB,CAAC,KAAKA,gBAAN,CAAxB,GAAkD,EARvC,EASX,CAAC,CAACoJ,EAAE,GAAG,KAAK5Y,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6D2Y,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAACnU,KAA1F,IACM,CAAC,CAACoU,EAAE,GAAG,KAAK7Y,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6D4Y,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAACpU,KAA1F,CADN,GAEM,EAXK,EAYX,CAAC,CAACqU,EAAE,GAAG,KAAK9Y,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6D6Y,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAACrU,KAA1F,IACM,CAAC,CAACsU,EAAE,GAAG,KAAK/Y,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6D8Y,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAACtU,KAA1F,CADN,GAEM,EAdK,CAAf;AAgBAwU,IAAAA,MAAM,CAACV,OAAP,CAAgBC,KAAD,IAAW;AACtB,UAAIA,KAAK,CAACnC,MAAN,GAAe,CAAnB,EAAsB;AAClB2C,QAAAA,UAAU,IAAI,EAAd;AACH;AACJ,KAJD,EAnByB,CAwBzB;;AACA,SAAKhZ,iBAAL,CAAuB6W,UAAvB,CAAkC;AAC9B9D,MAAAA,mBAAmB,EAAEiG,UAAU,CAACE,QAAX;AADS,KAAlC;AAGAnE,IAAAA,OAAO,CAACC,GAAR,CAAY,mDAAZ,EAAiE,KAAKhV,iBAAL,CAAuByE,KAAvB,CAA6BsO,mBAA9F;AACH;;AACDlR,EAAAA,wBAAwB,CAACsX,IAAD,EAAOC,WAAP,EAAoB;AACxC,UAAMC,KAAK,GAAGC,QAAQ,CAACC,cAAT,CAAwB,YAAxB,CAAd,CADwC,CAExC;;AACA,UAAMC,cAAc,GAAGH,KAAK,CAACI,IAAN,KAAe,UAAtC;AACAJ,IAAAA,KAAK,CAACI,IAAN,GAAaD,cAAc,GAAG,MAAH,GAAY,UAAvC;AACAL,IAAAA,IAAI,CAACO,SAAL,CAAeC,MAAf,CAAsB,cAAtB,EAAsCH,cAAtC;AACAL,IAAAA,IAAI,CAACO,SAAL,CAAeC,MAAf,CAAsB,QAAtB,EAAgC,CAACH,cAAjC;AACH;;AACDjU,EAAAA,cAAc,CAACqU,KAAD,EAAQpB,KAAR,EAAe;AACzB,QAAII,EAAJ,EAAQC,EAAR;;AACA,UAAMgB,YAAY,GAAGD,KAAK,CAACE,MAAN,CAAaC,KAAb,CAAmB,CAAnB,CAArB;AACAhF,IAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B6E,YAA5B,EAHyB,CAIzB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,QAAI,CAACA,YAAL,EAAmB;AACf,UAAIrB,KAAK,KAAK,MAAd,EAAsB;AAClB,aAAKwB,SAAL,GAAiB,IAAjB;AACA,aAAK/X,QAAL,GAAgB,IAAhB;AACA,cAAMgY,WAAW,GAAG,KAAKja,iBAAL,CAAuBC,GAAvB,CAA2B,MAA3B,CAApB;AACAga,QAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACC,eAAZ,EAA1D;AACAD,QAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACE,sBAAZ,EAA1D;AACH,OAND,MAOK,IAAI3B,KAAK,KAAK,WAAd,EAA2B;AAC5B,aAAKzS,cAAL,GAAsB,IAAtB;AACA,aAAKnB,aAAL,GAAqB,IAArB;AACA,cAAMwV,gBAAgB,GAAG,KAAKpa,iBAAL,CAAuBC,GAAvB,CAA2B,WAA3B,CAAzB;AACAma,QAAAA,gBAAgB,KAAK,IAArB,IAA6BA,gBAAgB,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,gBAAgB,CAACF,eAAjB,EAApE;AACAE,QAAAA,gBAAgB,KAAK,IAArB,IAA6BA,gBAAgB,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,gBAAgB,CAACD,sBAAjB,EAApE;AACH;;AACD;AACH;;AACD,QAAI3B,KAAK,KAAK,MAAV,IAAoBqB,YAAxB,EAAsC;AAClC;AACA,YAAMQ,WAAW,GAAGjc,aAAa,CAACkc,YAAd,CAA2BT,YAAY,CAACU,IAAxC,CAApB;AACA,WAAKP,SAAL,GAAiB,IAAIQ,IAAJ,CAAS,CAACX,YAAD,CAAT,EAAyBQ,WAAzB,EAAsC;AAAEZ,QAAAA,IAAI,EAAEI,YAAY,CAACJ;AAArB,OAAtC,CAAjB;AACA1E,MAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAKgF,SAA9B;AACA,YAAMC,WAAW,GAAG,KAAKja,iBAAL,CAAuBC,GAAvB,CAA2B,MAA3B,CAApB;AACAga,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACQ,aAAZ,CAA0Brc,aAAa,CAACwH,iBAAd,CAAgC,IAAhC,EAAsC,KAAKoU,SAA3C,CAA1B,CAA1D;AACAC,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACE,sBAAZ,EAA1D;AACA,YAAMO,QAAQ,GAAG,KAAKV,SAAL,CAAeP,IAAf,CAAoBkB,KAApB,CAA0B,GAA1B,EAA+B,CAA/B,CAAjB;AACA,YAAMC,aAAa,GAAG,CAAChC,EAAE,GAAG,KAAKoB,SAAL,CAAeO,IAAf,CAAoBI,KAApB,CAA0B,GAA1B,EAA+BE,GAA/B,EAAN,MAAgD,IAAhD,IAAwDjC,EAAE,KAAK,KAAK,CAApE,GAAwE,KAAK,CAA7E,GAAiFA,EAAE,CAACkC,WAAH,EAAvG;;AACA,UAAIJ,QAAQ,KAAK,OAAb,IAAwBE,aAAa,KAAK,KAA9C,EAAqD;AACjDhB,QAAAA,KAAK,CAACE,MAAN,CAAarV,KAAb,GAAqB,EAArB;AACA,aAAKwM,MAAL,CAAY8J,IAAZ,CAAiB,8CAAjB;AACA,aAAKf,SAAL,GAAiB,IAAjB;AACA,aAAK/X,QAAL,GAAgB,IAAhB;AACA;AACH;;AACD,UAAI,KAAK+X,SAAL,IAAkBU,QAAQ,KAAK,OAAnC,EAA4C;AACxC,cAAMM,MAAM,GAAG,IAAIC,UAAJ,EAAf;AACA,cAAMC,GAAG,GAAG,IAAIC,KAAJ,EAAZ;;AACAH,QAAAA,MAAM,CAACI,MAAP,GAAiBC,CAAD,IAAO;AACnB,cAAIzC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,eAAK7W,QAAL,GAAgB,CAAC2W,EAAE,GAAGyC,CAAC,CAACvB,MAAR,MAAoB,IAApB,IAA4BlB,EAAE,KAAK,KAAK,CAAxC,GAA4C,KAAK,CAAjD,GAAqDA,EAAE,CAAC0C,MAAxE;;AACA,cAAI,EAAE,CAACxC,EAAE,GAAG,CAACD,EAAE,GAAG,KAAK7Y,iBAAL,CAAuBC,GAAvB,CAA2B,MAA3B,CAAN,MAA8C,IAA9C,IAAsD4Y,EAAE,KAAK,KAAK,CAAlE,GAAsE,KAAK,CAA3E,GAA+EA,EAAE,CAAC3Y,MAAxF,MAAoG,IAApG,IAA4G4Y,EAAE,KAAK,KAAK,CAAxH,GAA4H,KAAK,CAAjI,GAAqIA,EAAE,CAAClT,iBAA1I,CAAJ,EAAkK;AAC9J,iBAAK2V,gBAAL,CAAsBL,GAAtB,EAA2B,MAA3B;AACA,iBAAKlX,YAAL,GAAoB;AAAE8V,cAAAA,MAAM,EAAE;AAAEC,gBAAAA,KAAK,EAAE,CAAC,KAAKC,SAAN;AAAT;AAAV,aAApB;AACH;AACJ,SAPD;;AAQAgB,QAAAA,MAAM,CAACQ,aAAP,CAAqB,KAAKxB,SAA1B;AACH;AACJ;;AACD,QAAIxB,KAAK,KAAK,WAAV,IAAyBqB,YAA7B,EAA2C;AACvC;AACA,YAAMQ,WAAW,GAAGjc,aAAa,CAACkc,YAAd,CAA2BT,YAAY,CAACU,IAAxC,CAApB;AACA,WAAKxU,cAAL,GAAsB,IAAIyU,IAAJ,CAAS,CAACX,YAAD,CAAT,EAAyBQ,WAAzB,EAAsC;AAAEZ,QAAAA,IAAI,EAAEI,YAAY,CAACJ;AAArB,OAAtC,CAAtB;AACA,YAAMW,gBAAgB,GAAG,KAAKpa,iBAAL,CAAuBC,GAAvB,CAA2B,WAA3B,CAAzB;AACAma,MAAAA,gBAAgB,KAAK,IAArB,IAA6BA,gBAAgB,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,gBAAgB,CAACK,aAAjB,CAA+Brc,aAAa,CAACwH,iBAAd,CAAgC,IAAhC,EAAsC,KAAKG,cAA3C,CAA/B,CAApE;AACAqU,MAAAA,gBAAgB,KAAK,IAArB,IAA6BA,gBAAgB,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,gBAAgB,CAACD,sBAAjB,EAApE;AACA,YAAMsB,aAAa,GAAG,KAAK1V,cAAL,CAAoB0T,IAApB,CAAyBkB,KAAzB,CAA+B,GAA/B,EAAoC,CAApC,CAAtB;AACA,YAAMe,kBAAkB,GAAG,CAAC7C,EAAE,GAAG,KAAK9S,cAAL,CAAoBwU,IAApB,CAAyBI,KAAzB,CAA+B,GAA/B,EAAoCE,GAApC,EAAN,MAAqD,IAArD,IAA6DhC,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAACiC,WAAH,EAAjH;;AACA,UAAIW,aAAa,KAAK,OAAlB,IAA6BC,kBAAkB,KAAK,KAAxD,EAA+D;AAC3D9B,QAAAA,KAAK,CAACE,MAAN,CAAarV,KAAb,GAAqB,EAArB;AACA,aAAKwM,MAAL,CAAY8J,IAAZ,CAAiB,8CAAjB;AACA,aAAKhV,cAAL,GAAsB,IAAtB;AACA,aAAKnB,aAAL,GAAqB,IAArB;AACA;AACH;;AACD,UAAI,KAAKmB,cAAL,IAAuB0V,aAAa,KAAK,OAA7C,EAAsD;AAClD,cAAMT,MAAM,GAAG,IAAIC,UAAJ,EAAf;AACA,cAAMC,GAAG,GAAG,IAAIC,KAAJ,EAAZ;;AACAH,QAAAA,MAAM,CAACI,MAAP,GAAiBC,CAAD,IAAO;AACnB,cAAIzC,EAAJ,EAAQC,EAAR,EAAYC,EAAZ;;AACA,eAAKlU,aAAL,GAAqB,CAACgU,EAAE,GAAGyC,CAAC,CAACvB,MAAR,MAAoB,IAApB,IAA4BlB,EAAE,KAAK,KAAK,CAAxC,GAA4C,KAAK,CAAjD,GAAqDA,EAAE,CAAC0C,MAA7E;;AACA,cAAI,EAAE,CAACxC,EAAE,GAAG,CAACD,EAAE,GAAG,KAAK7Y,iBAAL,CAAuBC,GAAvB,CAA2B,WAA3B,CAAN,MAAmD,IAAnD,IAA2D4Y,EAAE,KAAK,KAAK,CAAvE,GAA2E,KAAK,CAAhF,GAAoFA,EAAE,CAAC3Y,MAA7F,MAAyG,IAAzG,IAAiH4Y,EAAE,KAAK,KAAK,CAA7H,GAAiI,KAAK,CAAtI,GAA0IA,EAAE,CAAClT,iBAA/I,CAAJ,EAAuK;AACnK,iBAAK2V,gBAAL,CAAsBL,GAAtB,EAA2B,WAA3B;AACA,iBAAKnU,iBAAL,GAAyB;AAAE+S,cAAAA,MAAM,EAAE;AAAEC,gBAAAA,KAAK,EAAE,CAAC,KAAKhU,cAAN;AAAT;AAAV,aAAzB;AACH;AACJ,SAPD;;AAQAiV,QAAAA,MAAM,CAACQ,aAAP,CAAqB,KAAKzV,cAA1B;AACH;AACJ;AACJ;;AACDwV,EAAAA,gBAAgB,CAACI,KAAD,EAAQvC,WAAR,EAAqB;AACjC,UAAMwC,WAAW,GAAGD,KAAK,CAACE,KAAN,GAAcF,KAAK,CAACG,MAAxC;AACA,UAAMrD,OAAO,GAAG,KAAKzY,iBAAL,CAAuBC,GAAvB,CAA2BmZ,WAA3B,CAAhB;;AACA,QAAIwC,WAAW,KAAK,CAApB,EAAuB;AACnB;AACAnD,MAAAA,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACsD,SAAR,CAAkB;AAAElW,QAAAA,wBAAwB,EAAE;AAA5B,OAAlB,CAAlD;AACH,KAHD,MAIK;AACD4S,MAAAA,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACsD,SAAR,CAAkB,IAAlB,CAAlD;AACH;AACJ;;AACDC,EAAAA,aAAa,CAACC,OAAD,EAAU;AACnB,UAAMC,UAAU,GAAGC,IAAI,CAACF,OAAO,CAACtB,KAAR,CAAc,GAAd,EAAmB,CAAnB,CAAD,CAAvB;AACA,UAAMyB,UAAU,GAAGH,OAAO,CAACtB,KAAR,CAAc,GAAd,EAAmB,CAAnB,EAAsBA,KAAtB,CAA4B,GAA5B,EAAiC,CAAjC,EAAoCA,KAApC,CAA0C,GAA1C,EAA+C,CAA/C,CAAnB;AACA,UAAM0B,WAAW,GAAG,IAAIC,WAAJ,CAAgBJ,UAAU,CAAC7F,MAA3B,CAApB;AACA,UAAMkG,UAAU,GAAG,IAAIC,UAAJ,CAAeH,WAAf,CAAnB;;AACA,SAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,UAAU,CAAC7F,MAA/B,EAAuCoG,CAAC,EAAxC,EAA4C;AACxCF,MAAAA,UAAU,CAACE,CAAD,CAAV,GAAgBP,UAAU,CAACQ,UAAX,CAAsBD,CAAtB,CAAhB;AACH;;AACD,WAAO,IAAIE,IAAJ,CAAS,CAACJ,UAAD,CAAT,EAAuB;AAAE9C,MAAAA,IAAI,EAAE2C;AAAR,KAAvB,CAAP;AACH;;AACDrZ,EAAAA,OAAO,CAACsY,CAAD,EAAIjC,WAAJ,EAAiB;AACpB,QAAIA,WAAW,IAAI,MAAnB,EAA2B;AACvB,WAAKnX,QAAL,GAAgBoZ,CAAC,CAACuB,MAAlB;AACH,KAFD,MAGK,IAAIxD,WAAW,IAAI,WAAnB,EAAgC;AACjC,WAAKxU,aAAL,GAAqByW,CAAC,CAACuB,MAAvB;AACH;AACJ;;AACDna,EAAAA,WAAW,CAAC2W,WAAD,EAAc;AACrB,QAAIA,WAAW,IAAI,MAAnB,EAA2B;AACvB,WAAK5G,gBAAL,GAAwB,IAAxB;AACH,KAFD,MAGK,IAAI4G,WAAW,IAAI,WAAnB,EAAgC;AACjC,WAAKtT,qBAAL,GAA6B,IAA7B;AACH;AACJ;;AACDhC,EAAAA,WAAW,CAACsV,WAAD,EAAc;AACrB,QAAIA,WAAW,IAAI,MAAnB,EAA2B;AACvB,WAAK5G,gBAAL,GAAwB,KAAxB;AACH,KAFD,MAGK,IAAI4G,WAAW,IAAI,WAAnB,EAAgC;AACjC,WAAKtT,qBAAL,GAA6B,KAA7B;AACH;AACJ;;AACDnC,EAAAA,gBAAgB,CAACyV,WAAD,EAAc;AAC1B,UAAMkB,YAAY,GAAIuC,QAAD,IAAc;AAC/B,YAAMC,gBAAgB,GAAG,IAAIC,IAAJ,GAAWC,OAAX,EAAzB,CAD+B,CAE/B;;AACA,YAAMC,oBAAoB,GAAGJ,QAAQ,CAAClC,KAAT,CAAe,GAAf,EAAoBuC,KAApB,CAA0B,CAA1B,EAA6B,CAAC,CAA9B,EAAiCC,IAAjC,CAAsC,GAAtC,CAA7B;AACA,YAAMC,SAAS,GAAGP,QAAQ,CAAClC,KAAT,CAAe,GAAf,EAAoBE,GAApB,EAAlB,CAJ+B,CAK/B;;AACA,YAAMwC,WAAW,GAAGJ,oBAAoB,CAACK,OAArB,CAA6B,UAA7B,EAAyC,EAAzC,CAApB,CAN+B,CAO/B;;AACA,aAAQ,GAAED,WAAY,IAAGP,gBAAiB,IAAGM,SAAU,EAAvD;AACH,KATD;;AAUA,QAAIhE,WAAW,IAAI,MAAnB,EAA2B;AACvB,UAAI,KAAKnX,QAAL,IAAiB,KAAK+X,SAA1B,EAAqC;AACjC;AACA,cAAMuD,IAAI,GAAG,KAAKvB,aAAL,CAAmB,KAAK/Z,QAAxB,CAAb,CAFiC,CAGjC;;AACA,cAAMoY,WAAW,GAAGC,YAAY,CAAC,KAAKN,SAAL,CAAeO,IAAhB,CAAhC;AACA,aAAKP,SAAL,GAAiB,IAAIQ,IAAJ,CAAS,CAAC+C,IAAD,CAAT,EAAiBlD,WAAjB,EAA8B;AAAEZ,UAAAA,IAAI,EAAE,KAAKO,SAAL,CAAeP;AAAvB,SAA9B,CAAjB;AACA1E,QAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6B,KAAKgF,SAAlC,EANiC,CAOjC;;AACA,aAAKhW,YAAL,GAAoB;AAAE8V,UAAAA,MAAM,EAAE;AAAEC,YAAAA,KAAK,EAAE,CAAC,KAAKC,SAAN;AAAT;AAAV,SAApB;AACA,cAAMC,WAAW,GAAG,KAAKja,iBAAL,CAAuBC,GAAvB,CAA2B,MAA3B,CAApB;AACAga,QAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACC,eAAZ,EAA1D;AACAD,QAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACE,sBAAZ,EAA1D;AACA,aAAKrW,WAAL,CAAiB,MAAjB;AACH;AACJ,KAfD,MAgBK,IAAIsV,WAAW,IAAI,WAAnB,EAAgC;AACjC,UAAI,KAAKxU,aAAL,IAAsB,KAAKmB,cAA/B,EAA+C;AAC3C;AACA,cAAMwX,IAAI,GAAG,KAAKvB,aAAL,CAAmB,KAAKpX,aAAxB,CAAb,CAF2C,CAG3C;;AACA,cAAMyV,WAAW,GAAGC,YAAY,CAAC,KAAKvU,cAAL,CAAoBwU,IAArB,CAAhC;AACA,aAAKxU,cAAL,GAAsB,IAAIyU,IAAJ,CAAS,CAAC+C,IAAD,CAAT,EAAiBlD,WAAjB,EAA8B;AAAEZ,UAAAA,IAAI,EAAE,KAAK1T,cAAL,CAAoB0T;AAA5B,SAA9B,CAAtB;AACA1E,QAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmC,KAAKjP,cAAxC,EAN2C,CAO3C;;AACA,aAAKgB,iBAAL,GAAyB;AAAE+S,UAAAA,MAAM,EAAE;AAAEC,YAAAA,KAAK,EAAE,CAAC,KAAKhU,cAAN;AAAT;AAAV,SAAzB;AACA,cAAMkU,WAAW,GAAG,KAAKja,iBAAL,CAAuBC,GAAvB,CAA2B,WAA3B,CAApB;AACAga,QAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACC,eAAZ,EAA1D;AACAD,QAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACE,sBAAZ,EAA1D;AACA,aAAKrW,WAAL,CAAiB,WAAjB;AACH;AACJ;AACJ;;AACDT,EAAAA,WAAW,GAAG,CACb;;AACDH,EAAAA,OAAO,GAAG,CACT;;AACDM,EAAAA,SAAS,GAAG;AACR,SAAKyN,MAAL,CAAYuE,KAAZ,CAAkB,sBAAlB;AACH;;AACDlR,EAAAA,cAAc,GAAG;AACb,QAAIsU,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoByE,EAApB;;AACA,QAAI,EAAE,CAAC5E,EAAE,GAAG,KAAK5Y,iBAAL,CAAuBC,GAAvB,CAA2B,oBAA3B,CAAN,MAA4D,IAA5D,IAAoE2Y,EAAE,KAAK,KAAK,CAAhF,GAAoF,KAAK,CAAzF,GAA6FA,EAAE,CAACnU,KAAlG,CAAJ,EAA8G;AAC1G,WAAKsB,cAAL,GAAsB,IAAtB;AACA,WAAKnB,aAAL,GAAqB,IAArB;AACA,OAACiU,EAAE,GAAG,KAAK7Y,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6D4Y,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAACqB,eAAH,EAAtF;AACA,OAACpB,EAAE,GAAG,KAAK9Y,iBAAL,CAAuBC,GAAvB,CAA2B,aAA3B,CAAN,MAAqD,IAArD,IAA6D6Y,EAAE,KAAK,KAAK,CAAzE,GAA6E,KAAK,CAAlF,GAAsFA,EAAE,CAACqB,sBAAH,EAAtF;AACA,OAACpB,EAAE,GAAG,KAAK/Y,iBAAL,CAAuBC,GAAvB,CAA2B,WAA3B,CAAN,MAAmD,IAAnD,IAA2D8Y,EAAE,KAAK,KAAK,CAAvE,GAA2E,KAAK,CAAhF,GAAoFA,EAAE,CAACmB,eAAH,EAApF;AACA,OAACsD,EAAE,GAAG,KAAKxd,iBAAL,CAAuBC,GAAvB,CAA2B,WAA3B,CAAN,MAAmD,IAAnD,IAA2Dud,EAAE,KAAK,KAAK,CAAvE,GAA2E,KAAK,CAAhF,GAAoFA,EAAE,CAACrD,sBAAH,EAApF;AACH;AACJ,GA7f6B,CA8f9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAsD,EAAAA,oBAAoB,CAACC,IAAD,EAAOC,aAAP,EAAsBC,YAAY,GAAG,KAArC,EAA4C;AAC5D,QAAIA,YAAJ,EAAkB;AACd,aAAOD,aAAa,CAACtH,MAAd,GAAuB,CAAvB,IAA4BsH,aAAa,CAAC,CAAD,CAAb,CAAiBE,KAAjB,KAA2BH,IAAI,CAACG,KAAnE;AACH;;AACD,WAAOF,aAAa,CAACG,IAAd,CAAoBC,QAAD,IAAcA,QAAQ,CAACF,KAAT,KAAmBH,IAAI,CAACG,KAAzD,CAAP;AACH;;AACD7V,EAAAA,cAAc,CAAC0V,IAAD,EAAOC,aAAP,EAAsBK,eAAtB,EAAuC;AACjD,QAAI,CAACL,aAAL,EAAoB;AAChB,aAAO,KAAP;AACH;;AACD,QAAIK,eAAe,KAAK,cAAxB,EAAwC;AACpC,aAAOL,aAAa,CAACG,IAAd,CAAoBC,QAAD,IAAcA,QAAQ,CAAC9G,KAAT,KAAmByG,IAAI,CAACzG,KAAzD,CAAP;AACH;;AACD,WAAO0G,aAAa,CAACG,IAAd,CAAoBC,QAAD,IAAcA,QAAQ,CAACE,SAAT,KAAuBP,IAAI,CAACO,SAA7D,CAAP;AACH;;AACDjU,EAAAA,yBAAyB,CAAC0T,IAAD,EAAOC,aAAP,EAAsBC,YAAY,GAAG,KAArC,EAA4CnE,IAA5C,EAAkD;AACvE,QAAIb,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoByE,EAApB,EAAwBU,EAAxB,EAA4BC,EAA5B;;AACA,QAAI,CAACR,aAAL,EAAoB;AAChB5I,MAAAA,OAAO,CAACC,GAAR,CAAY,MAAZ,EAAoB0I,IAApB;AACA,aAAO,KAAP;AACH;;AACD,QAAIE,YAAJ,EAAkB;AACd,cAAQnE,IAAR;AACI,aAAK,QAAL;AACI,iBAAQkE,aAAa,CAACtH,MAAd,GAAuB,CAAvB,IAA4B,CAAC,CAACuC,EAAE,GAAG+E,aAAa,CAAC,CAAD,CAAnB,MAA4B,IAA5B,IAAoC/E,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACpB,KAAjE,OAA6EkG,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAClG,KAA9H,CAApC;AACA;;AACJ,aAAK,WAAL;AACI,iBAAQmG,aAAa,CAACtH,MAAd,GAAuB,CAAvB,IAA4B,CAAC,CAACwC,EAAE,GAAG8E,aAAa,CAAC,CAAD,CAAnB,MAA4B,IAA5B,IAAoC9E,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAAClB,KAAjE,OAA6E+F,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC/F,KAA9H,CAApC;AACA;;AACJ,aAAK,UAAL;AACI,iBAAQgG,aAAa,CAACtH,MAAd,GAAuB,CAAvB,IAA4B,CAAC,CAACyC,EAAE,GAAG6E,aAAa,CAAC,CAAD,CAAnB,MAA4B,IAA5B,IAAoC7E,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACjB,KAAjE,OAA6E6F,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC7F,KAA9H,CAApC;AACA;;AACJ,aAAK,oBAAL;AACI,iBAAQ8F,aAAa,CAACtH,MAAd,GAAuB,CAAvB,IAA4B,CAAC,CAAC0C,EAAE,GAAG4E,aAAa,CAAC,CAAD,CAAnB,MAA4B,IAA5B,IAAoC5E,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAAChB,KAAjE,OAA6E2F,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC3F,KAA9H,CAApC;AACA;;AACJ,aAAK,gBAAL;AACI,iBAAQ4F,aAAa,CAACtH,MAAd,GAAuB,CAAvB,IACJ,CAAC,CAACmH,EAAE,GAAGG,aAAa,CAAC,CAAD,CAAnB,MAA4B,IAA5B,IAAoCH,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACnL,MAAjE,OAA8EqL,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACrL,MAA/H,CADJ;AAEA;;AACJ,aAAK,oBAAL;AACI,iBAAQsL,aAAa,CAACtH,MAAd,GAAuB,CAAvB,IACJ,CAAC,CAAC6H,EAAE,GAAGP,aAAa,CAAC,CAAD,CAAnB,MAA4B,IAA5B,IAAoCO,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAAC7L,MAAjE,OAA8EqL,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACrL,MAA/H,CADJ;AAEA;;AACJ,aAAK,YAAL;AACI,iBAAQsL,aAAa,CAACtH,MAAd,GAAuB,CAAvB,IACJ,CAAC,CAAC8H,EAAE,GAAGR,aAAa,CAAC,CAAD,CAAnB,MAA4B,IAA5B,IAAoCQ,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAAC9L,MAAjE,OAA8EqL,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACrL,MAA/H,CADJ;AAEA;AAxBR;AA0BH;;AACD,WAAOsL,aAAa,CAACG,IAAd,CAAoBC,QAAD,IAAc,CAACA,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAAC1L,MAA9D,OAA2EqL,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACrL,MAA5H,CAAjC,CAAP;AACH;;AACDzK,EAAAA,eAAe,CAAC8V,IAAD,EAAOC,aAAP,EAAsBK,eAAtB,EAAuCJ,YAAY,GAAG,KAAtD,EAA6DnE,IAA7D,EAAmE;AAC9E,UAAM2E,UAAU,GAAGJ,eAAe,KAAK,cAApB,GAAqC,OAArC,GAA+C,WAAlE;;AACA,QAAIJ,YAAJ,EAAkB;AACd,UAAI,KAAK5T,yBAAL,CAA+B0T,IAA/B,EAAqCC,aAArC,EAAoD,IAApD,EAA0DlE,IAA1D,CAAJ,EAAqE;AACjEkE,QAAAA,aAAa,CAACU,MAAd,CAAqB,CAArB,EAAwB,CAAxB;AACH,OAFD,MAGK;AACDV,QAAAA,aAAa,CAACU,MAAd,CAAqB,CAArB,EAAwBV,aAAa,CAACtH,MAAtC;AACAsH,QAAAA,aAAa,CAAClG,IAAd,CAAmBiG,IAAnB;AACH;;AACD,WAAKvF,0BAAL;AACH,KATD,MAUK;AACD,YAAMmG,KAAK,GAAGX,aAAa,CAACY,SAAd,CAAyBR,QAAD,IAAc,CAACA,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACK,UAAD,CAA7D,MAA+EV,IAAI,CAACU,UAAD,CAAzH,CAAd;;AACA,UAAIE,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdX,QAAAA,aAAa,CAACU,MAAd,CAAqBC,KAArB,EAA4B,CAA5B;AACH,OAFD,MAGK;AACDX,QAAAA,aAAa,CAAClG,IAAd,CAAmBiG,IAAnB;AACH;AACJ;;AACD,SAAKxG,eAAL,CAAqB8G,eAArB,EAAsCL,aAAtC,EAAqDC,YAArD;AACH;;AACD1G,EAAAA,eAAe,CAACkC,WAAD,EAAcuE,aAAd,EAA6BC,YAA7B,EAA2C;AACtD,UAAMY,YAAY,GAAGb,aAAa,CAACtH,MAAd,GAAuB,CAAvB,GAA2BsH,aAAa,CAAC,CAAD,CAAxC,GAA8C,IAAnE;;AACA,QAAIC,YAAJ,EAAkB;AACd,YAAMa,kBAAkB,GAAG;AACvBvL,QAAAA,UAAU,EAAE,OADW;AAEvBF,QAAAA,cAAc,EAAE,OAFO;AAGvBM,QAAAA,YAAY,EAAE,OAHS;AAIvBE,QAAAA,qBAAqB,EAAE,OAJA;AAKvBJ,QAAAA,cAAc,EAAE,QALO;AAMvBS,QAAAA,kBAAkB,EAAE,QANG;AAOvBG,QAAAA,UAAU,EAAE;AAPW,OAA3B;;AASA,UAAIoF,WAAW,IAAIqF,kBAAnB,EAAuC;AACnC,aAAKze,iBAAL,CAAuB0e,UAAvB,CAAkCtF,WAAlC,EAA+C,IAAIxb,WAAJ,CAAgB4gB,YAAY,GAAGA,YAAY,CAACC,kBAAkB,CAACrF,WAAD,CAAnB,CAAf,GAAmD,IAA/E,CAA/C;AACH,OAFD,MAGK;AACDrE,QAAAA,OAAO,CAAC4J,IAAR,CAAc,4BAA2BvF,WAAY,EAArD;AACH;AACJ,KAhBD,MAiBK;AACD,UAAIA,WAAW,KAAK,cAApB,EAAoC;AAChC,cAAMwF,SAAS,GAAG,KAAK5e,iBAAL,CAAuBC,GAAvB,CAA2BmZ,WAA3B,CAAlB;AACAwF,QAAAA,SAAS,CAACC,KAAV,GAFgC,CAEb;;AACnBlB,QAAAA,aAAa,CAACpF,OAAd,CAAuBmF,IAAD,IAAU;AAC5BkB,UAAAA,SAAS,CAACnH,IAAV,CAAe,IAAI7Z,WAAJ,CAAgB8f,IAAI,CAACzG,KAArB,CAAf,EAD4B,CACiB;AAChD,SAFD;AAGH,OAND,MAOK;AACD,cAAM6H,WAAW,GAAG,CAChB,GAAG,KAAKjX,kBADQ,EAEhB,GAAG,KAAKmB,iBAFQ,CAApB;AAIA,cAAM4V,SAAS,GAAG,KAAK7N,WAAL,CAAiBqD,KAAjB,CAAuB0K,WAAW,CAAC7gB,GAAZ,CAAiByf,IAAD,IAAU,IAAI9f,WAAJ,CAAgB;AAC/EqgB,UAAAA,SAAS,EAAEP,IAAI,CAACO,SAD+D;AAE/E/V,UAAAA,YAAY,EAAEwV,IAAI,CAACxV,YAF4D;AAG/EkP,UAAAA,OAAO,EAAEsG,IAAI,CAACtG;AAHiE,SAAhB,CAA1B,CAAvB,CAAlB;AAKA,aAAKpX,iBAAL,CAAuB0e,UAAvB,CAAkCtF,WAAlC,EAA+CwF,SAA/C;AACH;AACJ;AACJ;;AACDnJ,EAAAA,qBAAqB,GAAG;AACpB,SAAKzE,mBAAL,CAAyByE,qBAAzB,GAAiDd,SAAjD,CAA4DU,GAAD,IAAS;AAChE,UAAIA,GAAG,CAAC0J,UAAJ,IAAkB,GAAlB,IAAyB1J,GAAG,CAACqB,IAAjC,EAAuC;AACnCrB,QAAAA,GAAG,CAACqB,IAAJ,CAAS6B,OAAT,CAAkByG,IAAD,IAAU;AACvB,cAAIA,IAAI,CAACC,YAAL,IAAqBD,IAAI,CAACC,YAAL,CAAkB5I,MAAlB,GAA2B,CAApD,EAAuD;AACnD,iBAAK6I,sBAAL,CAA4BF,IAAI,CAACC,YAAjC;AACH,WAFD,MAGK;AACD,iBAAK7N,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA7B,YAAAA,OAAO,CAACS,KAAR,CAAc,gCAAd,EAAgDwJ,IAAI,CAACG,KAArD;AACH;AACJ,SARD;AASH,OAVD,MAWK;AACDpK,QAAAA,OAAO,CAACS,KAAR,CAAc,oEAAd,EAAoFH,GAAG,CAAC0J,UAAxF;AACH;AACJ,KAfD,EAeIvJ,KAAD,IAAW;AACVT,MAAAA,OAAO,CAACS,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;AACH,KAjBD;AAkBH;;AACD0J,EAAAA,sBAAsB,CAACE,OAAD,EAAU;AAC5BA,IAAAA,OAAO,CAAC7G,OAAR,CAAiBmF,IAAD,IAAU;AACtB,YAAM2B,aAAa,GAAG;AAClBpB,QAAAA,SAAS,EAAEP,IAAI,CAACrL,MADE;AAElBnK,QAAAA,YAAY,EAAEwV,IAAI,CAAC4B,SAFD;AAGlBlI,QAAAA,OAAO,EAAEsG,IAAI,CAAC6B;AAHI,OAAtB;;AAKA,cAAQ7B,IAAI,CAAC6B,UAAb;AACI,aAAK,GAAL;AACI,eAAK/N,iBAAL,CAAuBiG,IAAvB,CAA4B4H,aAA5B;AACA;AACJ;AACA;AACA;;AACA,aAAK,GAAL;AACI,eAAK5N,QAAL,CAAcgG,IAAd,CAAmB4H,aAAnB;AACA;AATR;AAWH,KAjBD;AAkBH;;AACD3J,EAAAA,eAAe,GAAG;AACd,SAAKtE,iBAAL,CAAuBoO,IAAvB,CAA4B,eAA5B;AACA,WAAO,IAAIjJ,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,WAAKzF,mBAAL,CAAyB0E,eAAzB,GAA2Cf,SAA3C,CAAsDU,GAAD,IAAS;AAC1D,YAAIA,GAAG,CAAC0J,UAAJ,KAAmB,GAAvB,EAA4B;AACxB;AACA,eAAKnN,WAAL,GAAmByD,GAAG,CAACoK,WAAvB;AACA,eAAK9N,OAAL,GAAe0D,GAAG,CAACqK,OAAnB;AACA,eAAK7N,SAAL,GAAiBwD,GAAG,CAACuC,QAArB;AACA,eAAK9F,iBAAL,GAAyBuD,GAAG,CAACsK,kBAA7B;AACAnJ,UAAAA,OAAO;AACV,SAPD,MAQK;AACD,eAAKpF,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA7B,UAAAA,OAAO,CAACS,KAAR,CAAc,gDAAd,EAAgEH,GAAG,CAAC0J,UAApE;AACAtI,UAAAA,MAAM,CAAC,kCAAD,CAAN;AACH;AACJ,OAdD,EAcIjB,KAAD,IAAW;AACV,aAAKpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA7B,QAAAA,OAAO,CAACS,KAAR,CAAc,qCAAd,EAAqDA,KAArD;AACAiB,QAAAA,MAAM,CAACjB,KAAD,CAAN;AACH,OAlBD;AAmBH,KApBM,CAAP;AAqBH;;AACDI,EAAAA,gBAAgB,GAAG;AACf,SAAKxE,iBAAL,CAAuBoO,IAAvB,CAA4B,eAA5B;AACA,SAAKxO,mBAAL,CAAyB4E,gBAAzB,GAA4CjB,SAA5C,CAAuDU,GAAD,IAAS;AAC3D,UAAIA,GAAG,CAAC0J,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,aAAK/M,UAAL,GAAkBqD,GAAG,CAACqB,IAAJ,CAASkJ,IAAT,CAAc,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAAC/R,SAAF,CAAYiS,aAAZ,CAA0BD,CAAC,CAAChS,SAA5B,CAAxB,CAAlB;AACAiH,QAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B,KAAKhD,UAApC;AACA,aAAK/D,yBAAL,GAAiC,KAAK+D,UAAtC;AACA,aAAKZ,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACH,OALD,MAMK;AACD,aAAKxF,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA7B,QAAAA,OAAO,CAACS,KAAR,CAAc,mEAAd,EAAmFH,GAAG,CAAC0J,UAAvF;AACH;AACJ,KAXD,EAWIvJ,KAAD,IAAW;AACV,WAAKpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA7B,MAAAA,OAAO,CAACS,KAAR,CAAc,uCAAd,EAAuDA,KAAvD;AACH,KAdD;AAeH;;AACDG,EAAAA,WAAW,GAAG;AACV,WAAO,IAAIY,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,WAAKzF,mBAAL,CAAyBgP,eAAzB,GAA2CrL,SAA3C,CAAsDU,GAAD,IAAS;AAC1D,YAAIA,GAAG,CAAC4K,MAAJ,KAAe,GAAnB,EAAwB;AACpB,eAAKvO,YAAL,GAAoB2D,GAAG,CAACqB,IAAJ,CAASkJ,IAAT,CAAc,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACnX,OAAF,CAAUqX,aAAV,CAAwBD,CAAC,CAACpX,OAA1B,CAAxB,CAApB;AACAqM,UAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiC,KAAKtD,YAAtC;AACA8E,UAAAA,OAAO,CAAC,KAAK9E,YAAN,CAAP;AACH,SAJD,MAKK;AACDqD,UAAAA,OAAO,CAACS,KAAR,CAAc,gCAAd,EAAgDH,GAAhD;AACAoB,UAAAA,MAAM,CAAC,+BAAD,CAAN;AACH;AACJ,OAVD,EAUIjB,KAAD,IAAW;AACVT,QAAAA,OAAO,CAACS,KAAR,CAAc,+BAAd,EAA+CA,KAA/C;AACAiB,QAAAA,MAAM,CAACjB,KAAD,CAAN;AACH,OAbD;AAcH,KAfM,CAAP;AAgBH;;AACDQ,EAAAA,YAAY,GAAG;AACX,SAAK5E,iBAAL,CAAuBoO,IAAvB,CAA4B,eAA5B;AACA,SAAKxO,mBAAL,CAAyBgF,YAAzB,GAAwCrB,SAAxC,CAAmDU,GAAD,IAAS;AACvD,UAAIA,GAAG,CAAC0J,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,aAAK3N,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA,aAAK7E,MAAL,GAAcsD,GAAG,CAACqB,IAAJ,CAASkJ,IAAT,CAAc,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAAChR,OAAF,CAAUkR,aAAV,CAAwBD,CAAC,CAACjR,OAA1B,CAAxB,CAAd;AACAkG,QAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2B,KAAKjD,MAAhC;AACA,aAAK/C,qBAAL,GAA6B,KAAK+C,MAAlC;AACH,OALD,MAMK;AACD,aAAKX,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA7B,QAAAA,OAAO,CAACS,KAAR,CAAc,+DAAd,EAA+EH,GAAG,CAAC0J,UAAnF;AACH;AACJ,KAXD,EAWIvJ,KAAD,IAAW;AACV,WAAKpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA7B,MAAAA,OAAO,CAACS,KAAR,CAAc,uCAAd,EAAuDA,KAAvD;AACH,KAdD;AAeH;;AACD0K,EAAAA,aAAa,CAAClC,eAAD,EAAkB;AAC3B,QAAIA,eAAe,KAAK,aAAxB,EAAuC;AACnC,YAAMmC,UAAU,GAAG,KAAKtK,uBAAL,CAA6BpR,KAA7B,CACdqW,WADc,GAEdsF,IAFc,EAAnB;AAGA,WAAKnS,yBAAL,GAAiC,KAAK+D,UAAL,CAAgB8E,MAAhB,CAAwBK,MAAD,IAAYA,MAAM,CAACrJ,SAAP,CAAiBgN,WAAjB,GAA+B9D,QAA/B,CAAwCmJ,UAAxC,CAAnC,CAAjC;AACH,KALD,MAMK,IAAInC,eAAe,KAAK,aAAxB,EAAuC;AACxC,YAAMmC,UAAU,GAAG,KAAKrK,mBAAL,CAAyBrR,KAAzB,CAA+BqW,WAA/B,GAA6CsF,IAA7C,EAAnB;AACA,WAAKpR,qBAAL,GAA6B,KAAK+C,MAAL,CAAY+E,MAAZ,CAAoBK,MAAD,IAAYA,MAAM,CAACtI,OAAP,CAAeiM,WAAf,GAA6B9D,QAA7B,CAAsCmJ,UAAtC,CAA/B,CAA7B;AACH;AACJ;;AACD9S,EAAAA,UAAU,CAAC2Q,eAAD,EAAkBqC,EAAlB,EAAsB;AAC5B,UAAMC,WAAW,GAAG,KAAKtgB,iBAAL,CAAuBC,GAAvB,CAA2B+d,eAA3B,CAApB;;AACA,QAAIsC,WAAJ,EAAiB;AACbA,MAAAA,WAAW,CAACC,QAAZ,CAAqB,IAArB,EADa,CACe;;AAC5B,UAAIvC,eAAe,KAAK,aAAxB,EAAuC;AACnC,aAAKwC,kBAAL,GAA0B,IAA1B;AACA,aAAKC,OAAL,GAAe,KAAf;AACH,OAHD,MAIK,IAAIzC,eAAe,KAAK,aAAxB,EAAuC;AACxC,aAAK0C,cAAL,GAAsB,IAAtB;AACA,aAAKC,kBAAL,GAA0B,KAA1B;AACH;AACJ,KAVD,MAWK;AACD5L,MAAAA,OAAO,CAACS,KAAR,CAAc,yBAAd,EAAyCwI,eAAzC;AACH;AACJ;;AACDnQ,EAAAA,OAAO,CAAC+S,IAAD,EAAOC,aAAP,EAAsB/L,KAAtB,EAA6B;AAChC,UAAMwL,WAAW,GAAG,KAAKtgB,iBAAL,CAAuBC,GAAvB,CAA2B4gB,aAA3B,CAApB;;AACA,QAAIP,WAAJ,EAAiB;AACb,UAAIO,aAAa,KAAK,aAAtB,EAAqC;AACjC,YAAI/L,KAAK,KAAK,iBAAd,EAAiC;AAC7BwL,UAAAA,WAAW,CAACC,QAAZ,CAAqBK,IAArB;AACH,SAFD,MAGK;AACDN,UAAAA,WAAW,CAACC,QAAZ,CAAqBK,IAAI,CAACE,MAA1B;AACH;;AACD,aAAKN,kBAAL,GAA0BI,IAA1B;AACA,aAAK/K,uBAAL,CAA6B0K,QAA7B,CAAsC,EAAtC;AACA,aAAKtS,yBAAL,GAAiC,KAAK+D,UAAtC;AACA,aAAKyO,OAAL,GAAe,IAAf;AACA,aAAKxO,YAAL,GAAoB,KAApB,CAXiC,CAWN;AAC9B,OAZD,MAaK,IAAI4O,aAAa,KAAK,aAAtB,EAAqC;AACtC,YAAI/L,KAAK,KAAK,iBAAd,EAAiC;AAC7BwL,UAAAA,WAAW,CAACC,QAAZ,CAAqBK,IAArB;AACH,SAFD,MAGK;AACDN,UAAAA,WAAW,CAACC,QAAZ,CAAqBK,IAAI,CAACG,KAA1B;AACH;;AACD,aAAKL,cAAL,GAAsBE,IAAtB;AACA,aAAK9K,mBAAL,CAAyByK,QAAzB,CAAkC,EAAlC;AACA,aAAKvR,qBAAL,GAA6B,KAAK+C,MAAlC;AACA,aAAK4O,kBAAL,GAA0B,IAA1B;AACA,aAAKK,uBAAL,GAA+B,KAA/B;AACAjM,QAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B,KAAKhV,iBAAL,CAAuByE,KAAnD,EAZsC,CAYqB;AAC9D;AACJ;;AACD,SAAK0T,0BAAL;AACH;;AACD8I,EAAAA,kBAAkB,CAACJ,aAAD,EAAgB;AAC9B,UAAMjC,SAAS,GAAG,KAAK5e,iBAAL,CAAuBC,GAAvB,CAA2B4gB,aAA3B,CAAlB;AACA,WAAOjC,SAAS,GAAGA,SAAS,CAACna,KAAb,GAAqB,EAArC;AACH;;AACD8I,EAAAA,YAAY,CAAC8S,EAAD,EAAKQ,aAAL,EAAoB;AAC5B,QAAIA,aAAa,KAAK,aAAtB,EAAqC;AACjC,YAAMD,IAAI,GAAG,KAAK5O,UAAL,CAAgBsF,IAAhB,CAAsBsJ,IAAD,IAAU,CAACA,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACE,MAAlD,MAA8DT,EAA7F,CAAb;AACA,aAAO,CAACO,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC9S,SAAlD,IAA+D8S,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC9S,SAAhH,GAA4H,SAAnI;AACH;;AACD,QAAI+S,aAAa,KAAK,aAAtB,EAAqC;AACjC,YAAMD,IAAI,GAAG,KAAK7O,MAAL,CAAYuF,IAAZ,CAAkBsJ,IAAD,IAAU,CAACA,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACG,KAAlD,MAA6DV,EAAxF,CAAb;AACA,aAAO,CAACO,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC/R,OAAlD,IAA6D+R,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAC/R,OAA9G,GAAwH,SAA/H;AACH;AACJ;;AACDqS,EAAAA,cAAc,CAACtH,KAAD,EAAQ;AAClB,QAAI,CAACA,KAAK,CAACE,MAAN,CAAaqH,OAAb,CAAqB,mCAArB,CAAL,EAAgE;AAC5D,WAAKH,uBAAL,GAA+B,KAA/B,CAD4D,CACtB;AACzC;;AACD,QAAI,CAACpH,KAAK,CAACE,MAAN,CAAaqH,OAAb,CAAqB,mCAArB,CAAL,EAAgE;AAC5D,WAAKlP,YAAL,GAAoB,KAApB;AACH,KANiB,CAOlB;;AACH;;AACDkE,EAAAA,cAAc,CAACiL,KAAD,EAAQ;AAClB,QAAIA,KAAK,CAAC/K,MAAN,GAAe,CAAnB,EAAsB;AAClB,aAAO,KAAKlF,UAAL,CACFlR,GADE,CACG,sCAAqCmhB,KAAM,eAD9C,EAEFlL,IAFE,CAEGjY,GAAG,CAAEojB,QAAD,IAAcA,QAAQ,CAAC/F,MAAT,IAAmB,EAAlC,CAFN,EAE6Cpd,UAAU,CAAC,MAAMC,EAAE,CAAC,EAAD,CAAT,CAFvD,CAEsE;AAFtE,OAAP;AAIH,KALD,MAMK;AACD,aAAOA,EAAE,CAAC,EAAD,CAAT,CADC,CACc;AAClB;AACJ;;AACDmjB,EAAAA,eAAe,GAAG;AACd,UAAMF,KAAK,GAAG,KAAKrL,qBAAL,CAA2BtR,KAAzC;;AACA,QAAI2c,KAAK,IAAIA,KAAK,CAAC/K,MAAN,GAAe,CAA5B,EAA+B;AAC3B,WAAKF,cAAL,CAAoBiL,KAApB,EAA2BzM,SAA3B,CAAsCyB,SAAD,IAAe;AAChD,aAAKnG,iBAAL,GAAyBmG,SAAzB;AACA,aAAKlE,oBAAL,GAA4BkE,SAAS,CAACC,MAAV,GAAmB,CAA/C;AACH,OAHD;AAIH,KALD,MAMK;AACD,WAAKpG,iBAAL,GAAyB,EAAzB;AACA,WAAKiC,oBAAL,GAA4B,KAA5B;AACH;AACJ;;AACDpC,EAAAA,cAAc,CAACyR,QAAD,EAAW;AACrB,SAAK/R,gBAAL,GAAwB+R,QAAxB;AACA,SAAKxL,qBAAL,CAA2BwK,QAA3B,CAAoC,EAApC;AACA,SAAKtQ,iBAAL,GAAyB,EAAzB;AACA,SAAKiC,oBAAL,GAA4B,KAA5B,CAJqB,CAKrB;;AACA,SAAKf,UAAL,CACKlR,GADL,CACU,wCAAuCshB,QAAS,EAD1D,EAEK5M,SAFL,CAEgB0M,QAAD,IAAc;AACzB,UAAIzI,EAAJ,EAAQC,EAAR;;AACA,YAAMyC,MAAM,GAAG+F,QAAQ,CAAC/F,MAAT,IAAmB+F,QAAQ,CAAC/F,MAAT,CAAgB,CAAhB,CAAlC;;AACA,UAAIA,MAAJ,EAAY;AACR,aAAK9L,gBAAL,GAAyB,GAAE8L,MAAM,CAACkG,cAAe,KAAIlG,MAAM,CAACmG,OAAQ,EAApE,CADQ,CAC+D;AACvE;;AACA,SAAC7I,EAAE,GAAG,KAAK5Y,iBAAL,CAAuBC,GAAvB,CAA2B,YAA3B,CAAN,MAAoD,IAApD,IAA4D2Y,EAAE,KAAK,KAAK,CAAxE,GAA4E,KAAK,CAAjF,GAAqFA,EAAE,CAAC2H,QAAH,CAAYgB,QAAZ,CAArF;AACH,OAJD,MAKK;AACD;AACA,SAAC1I,EAAE,GAAG,KAAK7Y,iBAAL,CAAuBC,GAAvB,CAA2B,YAA3B,CAAN,MAAoD,IAApD,IAA4D4Y,EAAE,KAAK,KAAK,CAAxE,GAA4E,KAAK,CAAjF,GAAqFA,EAAE,CAAC0H,QAAH,CAAY,EAAZ,CAArF;AACH;AACJ,KAdD;AAeA,SAAKpI,0BAAL;AACH;;AACD7I,EAAAA,sBAAsB,GAAG;AACrB,QAAIsJ,EAAJ;;AACA,SAAKpJ,gBAAL,GAAwB,IAAxB;AACA,KAACoJ,EAAE,GAAG,KAAK5Y,iBAAL,CAAuBC,GAAvB,CAA2B,YAA3B,CAAN,MAAoD,IAApD,IAA4D2Y,EAAE,KAAK,KAAK,CAAxE,GAA4E,KAAK,CAAjF,GAAqFA,EAAE,CAAC2H,QAAH,CAAY,EAAZ,CAArF;AACA,SAAKpI,0BAAL;AACH;;AACD3O,EAAAA,qBAAqB,GAAG;AACpB,SAAKkY,wBAAL;AACA,UAAMC,cAAc,GAAG,CACnB,gBADmB,EAEnB,YAFmB,EAGnB,gBAHmB,EAInB,cAJmB,EAKnB,uBALmB,EAMnB,oBANmB,EAOnB,YAPmB,EAQnB,gBARmB,EASnB,qBATmB,EAUnB,YAVmB,EAWnB,iBAXmB,EAYnB,gBAZmB,EAanB,qBAbmB,EAcnB,cAdmB,EAenB,mBAfmB,EAgBnB,uBAhBmB,EAiBnB,oBAjBmB,EAkBnB,aAlBmB,EAmBnB,YAnBmB,EAoBnB,mBApBmB,EAqBnB,oBArBmB,EAsBnB,2BAtBmB,EAuBnB,YAvBmB,EAwBnB,mBAxBmB,EAyBnB,aAzBmB,EA0BnB,QA1BmB,EA2BnB,SA3BmB,EA4BnB,YA5BmB,EA6BnB,qBA7BmB,EA8BnB,MA9BmB,CAAvB;AAgCAA,IAAAA,cAAc,CAACpJ,OAAf,CAAuBC,KAAK,IAAI;AAC5B,UAAII,EAAJ,EAAQC,EAAR;;AACA,OAACD,EAAE,GAAG,KAAK5Y,iBAAL,CAAuBC,GAAvB,CAA2BuY,KAA3B,CAAN,MAA6C,IAA7C,IAAqDI,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAACsB,eAAH,EAA9E;AACA,OAACrB,EAAE,GAAG,KAAK7Y,iBAAL,CAAuBC,GAAvB,CAA2BuY,KAA3B,CAAN,MAA6C,IAA7C,IAAqDK,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAACsB,sBAAH,EAA9E;AACH,KAJD;AAKA,UAAMyH,iBAAiB,GAAG,CACtB,cADsB,CAA1B;AAGAA,IAAAA,iBAAiB,CAACrJ,OAAlB,CAA0BC,KAAK,IAAI;AAC/B,UAAII,EAAJ,EAAQC,EAAR;;AACA,OAACD,EAAE,GAAG,KAAK5Y,iBAAL,CAAuBC,GAAvB,CAA2BuY,KAA3B,CAAN,MAA6C,IAA7C,IAAqDI,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAAC6B,aAAH,CAAiB5c,UAAU,CAACsC,QAA5B,CAA9E;AACA,OAAC0Y,EAAE,GAAG,KAAK7Y,iBAAL,CAAuBC,GAAvB,CAA2BuY,KAA3B,CAAN,MAA6C,IAA7C,IAAqDK,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAACsB,sBAAH,EAA9E;AACH,KAJD,EA1CoB,CA+CpB;;AACA,QAAI,KAAKna,iBAAL,CAAuBQ,OAA3B,EAAoC;AAChC,WAAKyQ,MAAL,CAAY8J,IAAZ,CAAiB,2CAAjB;AACA;AACH;;AACD,SAAK3J,iBAAL,CAAuBoO,IAAvB,CAA4B,eAA5B;AACA,UAAM9I,IAAI,GAAG;AACTmL,MAAAA,OAAO,EAAE,CACL;AACInL,QAAAA,IAAI,EAAE,UADV;AAEIoL,QAAAA,MAAM,EAAE,IAFZ;AAGI3N,QAAAA,gBAAgB,EAAE,KAAKnU,iBAAL,CAAuByE,KAAvB,CAA6B0P;AAHnD,OADK,CADA;AAQTE,MAAAA,YAAY,EAAE,KAAKrU,iBAAL,CAAuByE,KAAvB,CAA6B4P,YARlC;AASTQ,MAAAA,IAAI,EAAE,KAAK8B,QAAL,CAAc9B,IATX;AAUTkN,MAAAA,cAAc,EAAE,UAVP,CAWT;;AAXS,KAAb;AAaAhN,IAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0C0B,IAA1C;AACA,SAAK1F,mBAAL,CAAyBxH,qBAAzB,CAA+CkN,IAA/C,EAAqD/B,SAArD,CAAgEU,GAAD,IAAS;AACpE,UAAIA,GAAG,CAAC0J,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,aAAK3N,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA,aAAK3F,MAAL,CAAY+Q,OAAZ,CAAoB,wCAApB;AACH,OAHD,MAIK;AACDjN,QAAAA,OAAO,CAACS,KAAR,CAAc,8BAAd;AACA,aAAKvE,MAAL,CAAYuE,KAAZ,CAAkB,mCAAlB;AACA,aAAKpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACH;AACJ,KAVD,EAUIpB,KAAD,IAAW;AACV,WAAKpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA7B,MAAAA,OAAO,CAACS,KAAR,CAAc,OAAd,EAAuBA,KAAvB;AACA,WAAKvE,MAAL,CAAYuE,KAAZ,CAAkB,mCAAlB;AACH,KAdD;AAeH;;AACD7E,EAAAA,kBAAkB,GAAG;AACjB,UAAMiR,iBAAiB,GAAG,CACtB,QADsB,EAEtB,SAFsB,EAGtB,YAHsB,EAItB,qBAJsB,EAKtB,MALsB,EAMtB,kBANsB,EAOtB,cAPsB,CAA1B;AASAA,IAAAA,iBAAiB,CAACrJ,OAAlB,CAA0BC,KAAK,IAAI;AAC/B,UAAII,EAAJ,EAAQC,EAAR;;AACA,OAACD,EAAE,GAAG,KAAK5Y,iBAAL,CAAuBC,GAAvB,CAA2BuY,KAA3B,CAAN,MAA6C,IAA7C,IAAqDI,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAACsB,eAAH,EAA9E;AACA,OAACrB,EAAE,GAAG,KAAK7Y,iBAAL,CAAuBC,GAAvB,CAA2BuY,KAA3B,CAAN,MAA6C,IAA7C,IAAqDK,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAACsB,sBAAH,EAA9E;AACH,KAJD,EAViB,CAejB;;AACA,UAAMwH,cAAc,GAAG,CACnB,gBADmB,EAEnB,YAFmB,EAGnB,gBAHmB,EAInB,cAJmB,EAKnB,uBALmB,EAMnB,oBANmB,EAOnB,YAPmB,CAAvB;AASAA,IAAAA,cAAc,CAACpJ,OAAf,CAAuBC,KAAK,IAAI;AAC5B,UAAII,EAAJ,EAAQC,EAAR;;AACA,OAACD,EAAE,GAAG,KAAK5Y,iBAAL,CAAuBC,GAAvB,CAA2BuY,KAA3B,CAAN,MAA6C,IAA7C,IAAqDI,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAAC6B,aAAH,CAAiB5c,UAAU,CAACsC,QAA5B,CAA9E;AACA,OAAC0Y,EAAE,GAAG,KAAK7Y,iBAAL,CAAuBC,GAAvB,CAA2BuY,KAA3B,CAAN,MAA6C,IAA7C,IAAqDK,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAACsB,sBAAH,EAA9E;AACH,KAJD,EAzBiB,CA8BjB;;AACA,QAAI,KAAKna,iBAAL,CAAuBQ,OAA3B,EAAoC;AAChC,WAAKyQ,MAAL,CAAY8J,IAAZ,CAAiB,2CAAjB;AACA;AACH;;AACD,SAAK3J,iBAAL,CAAuBoO,IAAvB,CAA4B,eAA5B;AACA,SAAKrH,0BAAL;AACA,UAAMzB,IAAI,GAAG;AACT7B,MAAAA,IAAI,EAAE,KAAK8B,QAAL,CAAc9B,IADX;AAET9B,MAAAA,mBAAmB,EAAE,KAAK/S,iBAAL,CAAuByE,KAAvB,CAA6BsO,mBAFzC;AAGTC,MAAAA,cAAc,EAAE,KAAKhT,iBAAL,CAAuByE,KAAvB,CAA6BuO,cAHpC;AAITC,MAAAA,mBAAmB,EAAE,KAAKjT,iBAAL,CAAuByE,KAAvB,CAA6BwO,mBAJzC;AAKTC,MAAAA,UAAU,EAAE,KAAKlT,iBAAL,CAAuByE,KAAvB,CAA6ByO,UALhC;AAMTC,MAAAA,eAAe,EAAE,KAAKnT,iBAAL,CAAuByE,KAAvB,CAA6B0O,eANrC;AAOTC,MAAAA,cAAc,EAAE,KAAKpT,iBAAL,CAAuByE,KAAvB,CAA6B2O,cAPpC;AAQTC,MAAAA,mBAAmB,EAAE,KAAKrT,iBAAL,CAAuByE,KAAvB,CAA6B4O,mBARzC;AASTC,MAAAA,YAAY,EAAE,KAAKtT,iBAAL,CAAuByE,KAAvB,CAA6B6O,YATlC;AAUTC,MAAAA,iBAAiB,EAAE,KAAKvT,iBAAL,CAAuByE,KAAvB,CAA6B8O,iBAVvC;AAWTC,MAAAA,qBAAqB,EAAE,KAAKxT,iBAAL,CAAuByE,KAAvB,CAA6B+O,qBAX3C;AAYTC,MAAAA,kBAAkB,EAAE,KAAKzT,iBAAL,CAAuByE,KAAvB,CAA6BgP,kBAZxC;AAaTC,MAAAA,WAAW,EAAE,KAAK1T,iBAAL,CAAuByE,KAAvB,CAA6BiP,WAA7B,GAA2C,KAAK1T,iBAAL,CAAuByE,KAAvB,CAA6BiP,WAAxE,GAAsF,EAb1F;AAcTC,MAAAA,UAAU,EAAE,KAAK3T,iBAAL,CAAuByE,KAAvB,CAA6BkP,UAA7B,GAA0C,KAAK3T,iBAAL,CAAuByE,KAAvB,CAA6BkP,UAAvE,GAAoF,EAdvF;AAeTC,MAAAA,iBAAiB,EAAE,KAAK5T,iBAAL,CAAuByE,KAAvB,CAA6BmP,iBAfvC;AAgBTC,MAAAA,kBAAkB,EAAE,KAAK7T,iBAAL,CAAuByE,KAAvB,CAA6BoP,kBAhBxC;AAiBTC,MAAAA,yBAAyB,EAAE,KAAK9T,iBAAL,CAAuByE,KAAvB,CAA6BqP,yBAjB/C;AAkBTE,MAAAA,UAAU,EAAE,KAAKhU,iBAAL,CAAuByE,KAAvB,CAA6BuP,UAlBhC;AAmBTC,MAAAA,iBAAiB,EAAE,KAAKjU,iBAAL,CAAuByE,KAAvB,CAA6BwP,iBAnBvC;AAoBTC,MAAAA,WAAW,EAAE,KAAKlU,iBAAL,CAAuByE,KAAvB,CAA6ByP,WAA7B,GAA2C,KAAKlU,iBAAL,CAAuByE,KAAvB,CAA6ByP,WAAxE,GAAsF;AApB1F,KAAb;AAsBAa,IAAAA,OAAO,CAACC,GAAR,CAAY,yBAAZ,EAAuC0B,IAAvC;AACA,SAAK1F,mBAAL,CAAyBL,kBAAzB,CAA4C+F,IAA5C,EAAkD/B,SAAlD,CAA6DU,GAAD,IAAS;AACjE,UAAIA,GAAG,CAAC0J,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,aAAK3N,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA,aAAK3F,MAAL,CAAY+Q,OAAZ,CAAoB,qCAApB;AACH,OAHD,MAIK;AACDjN,QAAAA,OAAO,CAACS,KAAR,CAAc,8BAAd;AACA,aAAKvE,MAAL,CAAYuE,KAAZ,CAAkB,gCAAlB;AACH;AACJ,KATD,EASIA,KAAD,IAAW;AACV,WAAKpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA7B,MAAAA,OAAO,CAACS,KAAR,CAAc,OAAd,EAAuBA,KAAvB;AACA,WAAKvE,MAAL,CAAYuE,KAAZ,CAAkB,gCAAlB;AACH,KAbD;AAcH;;AACKpO,EAAAA,iBAAiB,GAAG;AAAA;;AAAA;AACtB,UAAIwR,EAAJ,EAAQC,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB;;AACA,UAAI;AACA,YAAI,EAAE,CAACF,EAAE,GAAG,CAACD,EAAE,GAAG,MAAI,CAAC5Y,iBAAL,CAAuBC,GAAvB,CAA2B,WAA3B,CAAN,MAAmD,IAAnD,IAA2D2Y,EAAE,KAAK,KAAK,CAAvE,GAA2E,KAAK,CAAhF,GAAoFA,EAAE,CAAC1Y,MAA7F,MAAyG,IAAzG,IAAiH2Y,EAAE,KAAK,KAAK,CAA7H,GAAiI,KAAK,CAAtI,GAA0IA,EAAE,CAAChT,wBAA/I,KAA4K,EAAE,CAACkT,EAAE,GAAG,CAACD,EAAE,GAAG,MAAI,CAAC9Y,iBAAL,CAAuBC,GAAvB,CAA2B,MAA3B,CAAN,MAA8C,IAA9C,IAAsD6Y,EAAE,KAAK,KAAK,CAAlE,GAAsE,KAAK,CAA3E,GAA+EA,EAAE,CAAC5Y,MAAxF,MAAoG,IAApG,IAA4G6Y,EAAE,KAAK,KAAK,CAAxH,GAA4H,KAAK,CAAjI,GAAqIA,EAAE,CAAClT,wBAA1I,CAAhL,EAAqV;AACjV,cAAIoc,MAAJ;AACA,cAAIC,OAAJ;;AACA,UAAA,MAAI,CAAC9Q,iBAAL,CAAuBoO,IAAvB,CAA4B,eAA5B;;AACA,cAAI,MAAI,CAACxf,iBAAL,CAAuByE,KAAvB,CAA6BsP,IAAjC,EAAuC;AACnC,kBAAM,MAAI,CAACoO,aAAL,CAAmB,MAAI,CAACnI,SAAxB,CAAN;AACAiI,YAAAA,MAAM,GAAG,MAAI,CAAC1Q,OAAL,GAAe,MAAI,CAACyI,SAAL,CAAeO,IAAvC;AACH,WAHD,MAIK;AACD0H,YAAAA,MAAM,GAAG,MAAI,CAACtL,QAAL,CAAc5C,IAAvB;AACH;;AACD,cAAI,MAAI,CAAC/T,iBAAL,CAAuByE,KAAvB,CAA6BgQ,SAAjC,EAA4C;AACxC,kBAAM,MAAI,CAAC0N,aAAL,CAAmB,MAAI,CAACpc,cAAxB,CAAN;AACAmc,YAAAA,OAAO,GAAG,MAAI,CAAC3Q,OAAL,GAAe,MAAI,CAACxL,cAAL,CAAoBwU,IAA7C;AACH,WAHD,MAIK;AACD2H,YAAAA,OAAO,GAAG,MAAI,CAACvL,QAAL,CAAclC,SAAxB;AACH;;AACD,gBAAMiC,IAAI,GAAG;AACT7B,YAAAA,IAAI,EAAE,MAAI,CAAC8B,QAAL,CAAc9B,IADX;AAETd,YAAAA,IAAI,EAAEkO,MAFG;AAGTzN,YAAAA,WAAW,EAAG,MAAI,CAACxU,iBAAL,CAAuByE,KAAvB,CAA6B+P,WAA7B,IAA4C,MAAI,CAACmC,QAAL,CAAcnC,WAA3D,IAA2E,EAH/E;AAITC,YAAAA,SAAS,EAAEyN,OAAO,IAAI;AAJb,WAAb;AAMAnN,UAAAA,OAAO,CAACC,GAAR,CAAY,6BAAZ,EAA2C0B,IAA3C;;AACA,UAAA,MAAI,CAAC1F,mBAAL,CAAyB5J,iBAAzB,CAA2CsP,IAA3C,EAAiD/B,SAAjD,CAA4DU,GAAD,IAAS;AAChE,YAAA,MAAI,CAACjE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;;AACA,YAAA,MAAI,CAAC3F,MAAL,CAAY+Q,OAAZ,CAAoB,oCAApB;AACH,WAHD,EAGIxM,KAAD,IAAW;AACV,YAAA,MAAI,CAACpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;;AACA7B,YAAAA,OAAO,CAACS,KAAR,CAAc,OAAd,EAAuBA,KAAvB;;AACA,YAAA,MAAI,CAACvE,MAAL,CAAYuE,KAAZ,CAAkB,+BAAlB;AACH,WAPD;AAQH,SAjCD,MAkCK;AACD,UAAA,MAAI,CAACvE,MAAL,CAAY8J,IAAZ,CAAiB,yBAAjB;AACH;AACJ,OAtCD,CAuCA,OAAOyC,EAAP,EAAW;AACP,QAAA,MAAI,CAACvM,MAAL,CAAYuE,KAAZ,CAAkB,+BAAlB;AACH;AA3CqB;AA4CzB;;AACD4M,EAAAA,uBAAuB,GAAG;AACtB,UAAMC,cAAc,GAAG,CACnB,QADmB,EAEnB,SAFmB,EAGnB,YAHmB,EAInB,qBAJmB,EAKnB,gBALmB,EAMnB,YANmB,EAOnB,gBAPmB,EAQnB,cARmB,EASnB,uBATmB,EAUnB,oBAVmB,EAWnB,YAXmB,EAYnB,cAZmB,CAAvB;AAcA,UAAMC,cAAc,GAAG,CACnB,MADmB,EAEnB,aAFmB,EAGnB,YAHmB,EAInB,aAJmB,EAKnB,YALmB,CAAvB;AAOAjK,IAAAA,MAAM,CAACC,IAAP,CAAY,KAAKtY,iBAAL,CAAuBI,QAAnC,EAA6CmY,OAA7C,CAAsDgK,GAAD,IAAS;AAC1D,UAAIF,cAAc,CAACrL,QAAf,CAAwBuL,GAAxB,KAAgC,CAACD,cAAc,CAACtL,QAAf,CAAwBuL,GAAxB,CAArC,EAAmE;AAC/D,cAAM9J,OAAO,GAAG,KAAKzY,iBAAL,CAAuBC,GAAvB,CAA2BsiB,GAA3B,CAAhB;;AACA,YAAI9J,OAAJ,EAAa;AACT,gBAAM+J,iBAAiB,GAAG/J,OAAO,CAACgK,SAAR,GAAoB,CAAChK,OAAO,CAACgK,SAAT,CAApB,GAA0C,EAApE;AACAhK,UAAAA,OAAO,CAACgC,aAAR,CAAsB,CAAC,GAAG+H,iBAAJ,EAAuB3kB,UAAU,CAACsC,QAAlC,CAAtB;AACAsY,UAAAA,OAAO,CAAC0B,sBAAR,GAHS,CAGyB;AACrC;AACJ;AACJ,KATD;AAUH;;AACDuH,EAAAA,wBAAwB,GAAG;AACvB,UAAMgB,kBAAkB,GAAG,KAAK1iB,iBAAL,CAAuBC,GAAvB,CAA2B,kBAA3B,CAA3B;;AACA,QAAIyiB,kBAAJ,EAAwB;AACpB,YAAMC,aAAa,GAAGD,kBAAkB,CAACje,KAAnB,CAAyBqZ,IAAzB,CAA+B3G,MAAD,IAAYA,MAAM,CAACC,OAAP,KAAmB,GAA7D,CAAtB;;AACA,UAAI,CAACuL,aAAL,EAAoB;AAChBD,QAAAA,kBAAkB,CAAC3G,SAAnB,CAA6B;AAAE6G,UAAAA,iBAAiB,EAAE;AAArB,SAA7B;AACH,OAFD,MAGK;AACDF,QAAAA,kBAAkB,CAAC3G,SAAnB,CAA6B,IAA7B;AACH;AACJ;AACJ;;AACDzL,EAAAA,gBAAgB,GAAG;AACf,SAAK8R,uBAAL;AACA,SAAKV,wBAAL;;AACA,QAAI,KAAK1hB,iBAAL,CAAuBQ,OAA3B,EAAoC;AAChCuU,MAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4C,KAAKhV,iBAAL,CAAuByE,KAAnE;AACA4T,MAAAA,MAAM,CAACC,IAAP,CAAY,KAAKtY,iBAAL,CAAuBI,QAAnC,EAA6CmY,OAA7C,CAAsDgK,GAAD,IAAS;AAC1D,YAAI3J,EAAJ;;AACA,cAAMiK,aAAa,GAAG,CAACjK,EAAE,GAAG,KAAK5Y,iBAAL,CAAuBC,GAAvB,CAA2BsiB,GAA3B,CAAN,MAA2C,IAA3C,IAAmD3J,EAAE,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,EAAE,CAAC1Y,MAArG;;AACA,YAAI2iB,aAAJ,EAAmB;AACf9N,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQuN,GAAI,cAAzB,EAAwCM,aAAxC;AACH;AACJ,OAND;AAOA,WAAK5R,MAAL,CAAY8J,IAAZ,CAAiB,2CAAjB;AACA;AACH,KAXD,MAYK;AACD;AACA,UAAI+H,WAAJ;AACA,UAAIC,UAAJ,CAHC,CAID;;AACA,YAAMC,eAAe,GAAG,KAAKhJ,SAAL,GAAiB,KAAKmI,aAAL,CAAmB,KAAKnI,SAAxB,CAAjB,GAAsDzD,OAAO,CAACC,OAAR,CAAgB,IAAhB,CAA9E;AACAzB,MAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+BgO,eAA/B,EANC,CAOD;;AACA,YAAMC,cAAc,GAAG,KAAKld,cAAL,GAAsB,KAAKoc,aAAL,CAAmB,KAAKpc,cAAxB,CAAtB,GAAgEwQ,OAAO,CAACC,OAAR,CAAgB,IAAhB,CAAvF;AACAzB,MAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BiO,cAA9B,EATC,CAUD;;AACA1M,MAAAA,OAAO,CAAC2M,GAAR,CAAY,CAACF,eAAD,EAAkBC,cAAlB,CAAZ,EAA+C7N,IAA/C,CAAoD,CAAC,CAAC+N,gBAAD,EAAmBC,eAAnB,CAAD,KAAyC;AACzF,YAAIxK,EAAJ,EAAQC,EAAR,CADyF,CAEzF;;;AACAiK,QAAAA,WAAW,GAAG,KAAK9I,SAAL,GAAiB,KAAKzI,OAAL,IAAgB,CAACqH,EAAE,GAAG,KAAKoB,SAAX,MAA0B,IAA1B,IAAkCpB,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAAC2B,IAA9E,CAAjB,GAAuG,EAArH;AACAxF,QAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2B8N,WAA3B;AACAC,QAAAA,UAAU,GAAG,KAAKhd,cAAL,GAAsB,KAAKwL,OAAL,IAAgB,CAACsH,EAAE,GAAG,KAAK9S,cAAX,MAA+B,IAA/B,IAAuC8S,EAAE,KAAK,KAAK,CAAnD,GAAuD,KAAK,CAA5D,GAAgEA,EAAE,CAAC0B,IAAnF,CAAtB,GAAiH,EAA9H;AACAxF,QAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B+N,UAA1B,EANyF,CAOzF;;AACA,aAAKM,gBAAL,CAAsBP,WAAtB,EAAmCC,UAAnC;AACH,OATD,EASGxN,KATH,CASUC,KAAD,IAAW;AAChBT,QAAAA,OAAO,CAACS,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;AACA,aAAKvE,MAAL,CAAYuE,KAAZ,CAAkB,yBAAlB;AACH,OAZD;AAaH;AACJ,GAtsC6B,CAusC9B;;;AACA2M,EAAAA,aAAa,CAACxG,KAAD,EAAQ;AACjB,WAAO,IAAIpF,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,UAAI,CAACkF,KAAL,EAAY;AACRnF,QAAAA,OAAO,CAAC,IAAD,CAAP;AACH;;AACDzB,MAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgC2G,KAAhC;AACA,WAAK3K,mBAAL,CAAyBsS,SAAzB,CAAmC3H,KAAnC,EAA0ChH,SAA1C,CAAqDU,GAAD,IAAS;AACzDN,QAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqC2G,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACpB,IAAzF;AACA/D,QAAAA,OAAO,CAACnB,GAAD,CAAP,CAFyD,CAE3C;AACjB,OAHD,EAGIG,KAAD,IAAW;AACVT,QAAAA,OAAO,CAACS,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;AACAiB,QAAAA,MAAM,CAACjB,KAAD,CAAN,CAFU,CAEK;AAClB,OAND;AAOH,KAZM,CAAP;AAaH,GAttC6B,CAutC9B;;;AACA6N,EAAAA,gBAAgB,CAACP,WAAD,EAAcC,UAAd,EAA0B;AACtC,UAAMQ,QAAQ,GAAG;AACb7Q,MAAAA,MAAM,EAAE,KAAK1S,iBAAL,CAAuByE,KAAvB,CAA6BiO,MADxB;AAEbC,MAAAA,OAAO,EAAE,KAAK3S,iBAAL,CAAuByE,KAAvB,CAA6BkO,OAFzB;AAGbE,MAAAA,UAAU,EAAE,KAAK7S,iBAAL,CAAuByE,KAAvB,CAA6BoO,UAH5B;AAIbyB,MAAAA,UAAU,EAAE,KAAKtU,iBAAL,CAAuByE,KAAvB,CAA6B6P,UAJ5B;AAKbkP,MAAAA,WAAW,EAAE,IALA;AAMbzQ,MAAAA,mBAAmB,EAAE,KAAK/S,iBAAL,CAAuByE,KAAvB,CAA6BsO,mBANrC;AAOb0Q,MAAAA,KAAK,EAAE,EAPM;AAQbzQ,MAAAA,cAAc,EAAE,KAAKhT,iBAAL,CAAuByE,KAAvB,CAA6BuO,cARhC;AASbC,MAAAA,mBAAmB,EAAE,KAAKjT,iBAAL,CAAuByE,KAAvB,CAA6BwO,mBATrC;AAUbC,MAAAA,UAAU,EAAE,KAAKlT,iBAAL,CAAuByE,KAAvB,CAA6ByO,UAV5B;AAWbC,MAAAA,eAAe,EAAE,KAAKnT,iBAAL,CAAuByE,KAAvB,CAA6B0O,eAXjC;AAYbC,MAAAA,cAAc,EAAE,KAAKpT,iBAAL,CAAuByE,KAAvB,CAA6B2O,cAZhC;AAabC,MAAAA,mBAAmB,EAAE,KAAKrT,iBAAL,CAAuByE,KAAvB,CAA6B4O,mBAbrC;AAcbC,MAAAA,YAAY,EAAE,KAAKtT,iBAAL,CAAuByE,KAAvB,CAA6B6O,YAd9B;AAebC,MAAAA,iBAAiB,EAAE,KAAKvT,iBAAL,CAAuByE,KAAvB,CAA6B8O,iBAfnC;AAgBbC,MAAAA,qBAAqB,EAAE,KAAKxT,iBAAL,CAAuByE,KAAvB,CAA6B+O,qBAhBvC;AAiBbC,MAAAA,kBAAkB,EAAE,KAAKzT,iBAAL,CAAuByE,KAAvB,CAA6BgP,kBAjBpC;AAkBbC,MAAAA,WAAW,EAAE,KAAK1T,iBAAL,CAAuByE,KAAvB,CAA6BiP,WAA7B,IAA4C,EAlB5C;AAmBbC,MAAAA,UAAU,EAAE,KAAK3T,iBAAL,CAAuByE,KAAvB,CAA6BkP,UAA7B,IAA2C,EAnB1C;AAoBbC,MAAAA,iBAAiB,EAAE,KAAK5T,iBAAL,CAAuByE,KAAvB,CAA6BmP,iBApBnC;AAqBbC,MAAAA,kBAAkB,EAAE,KAAK7T,iBAAL,CAAuByE,KAAvB,CAA6BoP,kBArBpC;AAsBbC,MAAAA,yBAAyB,EAAE,KAAK9T,iBAAL,CAAuByE,KAAvB,CAA6BqP,yBAtB3C;AAuBbC,MAAAA,IAAI,EAAE+O,WAAW,IAAI,EAvBR;AAwBbrO,MAAAA,SAAS,EAAEsO,UAAU,IAAI,EAxBZ;AAyBbvO,MAAAA,WAAW,EAAE,KAAKxU,iBAAL,CAAuByE,KAAvB,CAA6B+P,WAzB7B;AA0BbD,MAAAA,kBAAkB,EAAE,KAAKvU,iBAAL,CAAuByE,KAAvB,CAA6B8P,kBA1BpC;AA2BbmP,MAAAA,UAAU,EAAE,GA3BC;AA4BbC,MAAAA,YAAY,EAAE,GA5BD;AA6BbC,MAAAA,QAAQ,EAAE,GA7BG;AA8BbC,MAAAA,kBAAkB,EAAE,GA9BP;AA+BbC,MAAAA,cAAc,EAAE,OA/BH;AAgCbC,MAAAA,gBAAgB,EAAE,GAhCL;AAiCbC,MAAAA,cAAc,EAAE,GAjCH;AAkCbhQ,MAAAA,UAAU,EAAE,KAAKhU,iBAAL,CAAuByE,KAAvB,CAA6BuP,UAlC5B;AAmCbC,MAAAA,iBAAiB,EAAE,KAAKjU,iBAAL,CAAuByE,KAAvB,CAA6BwP,iBAnCnC;AAoCbC,MAAAA,WAAW,EAAE,KAAKlU,iBAAL,CAAuByE,KAAvB,CAA6ByP,WAA7B,IAA4C,EApC5C;AAqCb+P,MAAAA,gBAAgB,EAAE,CArCL;AAsCbC,MAAAA,4BAA4B,EAAE,CAC1B;AAAEC,QAAAA,qBAAqB,EAAE;AAAzB,OAD0B,CAtCjB;AAyCbtC,MAAAA,OAAO,EAAE,CACL;AACInL,QAAAA,IAAI,EAAE,UADV;AAEIoL,QAAAA,MAAM,EAAE,IAFZ;AAGI3N,QAAAA,gBAAgB,EAAE,KAAKnU,iBAAL,CAAuByE,KAAvB,CAA6B0P;AAHnD,OADK,CAzCI;AAgDbE,MAAAA,YAAY,EAAE,KAAKrU,iBAAL,CAAuByE,KAAvB,CAA6B4P;AAhD9B,KAAjB;AAkDAU,IAAAA,OAAO,CAACC,GAAR,CAAYuO,QAAZ;AACA,SAAKnS,iBAAL,CAAuBoO,IAAvB,CAA4B,eAA5B;AACA,SAAKxO,mBAAL,CAAyBV,gBAAzB,CAA0CiT,QAA1C,EAAoD5O,SAApD,CAA+DU,GAAD,IAAS;AACnE,UAAIA,GAAG,CAAC0J,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,aAAK9N,MAAL,CAAY+Q,OAAZ,CAAoB,gCAApB;AACA,aAAK5Q,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACA,aAAK9F,MAAL,CAAYmE,QAAZ,CAAqB,CAAC,oBAAD,CAArB;AACA,aAAKjE,mBAAL,CAAyBoT,cAAzB;AACH,OALD,MAMK;AACD,aAAKnT,MAAL,CAAYuE,KAAZ,CAAkB,sBAAlB;AACAT,QAAAA,OAAO,CAACS,KAAR,CAAc,QAAd,EAAwB,mBAAxB;AACA,aAAKpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;AACH;AACJ,KAZD,EAYIpB,KAAD,IAAW;AACV,WAAKpE,iBAAL,CAAuBwF,IAAvB,CAA4B,eAA5B;;AACA,UAAIpB,KAAK,CAACyK,MAAN,KAAiB,GAArB,EAA0B;AACtB,aAAKhP,MAAL,CAAYuE,KAAZ,CAAkB,6BAAlB;AACH,OAFD,MAGK,IAAIA,KAAK,CAACyK,MAAN,KAAiB,GAArB,EAA0B;AAC3B,aAAKhP,MAAL,CAAY+Q,OAAZ,CAAoB,gCAApB;AACA,aAAKlR,MAAL,CAAYmE,QAAZ,CAAqB,CAAC,oBAAD,CAArB;AACA,aAAKjE,mBAAL,CAAyBoT,cAAzB;AACH,OAJI,MAKA;AACDrP,QAAAA,OAAO,CAACS,KAAR,CAAc,QAAd,EAAwBA,KAAxB;AACA,aAAKvE,MAAL,CAAYuE,KAAZ,CAAkB,0BAAlB;AACH;AACJ,KA1BD;AA2BH;;AACD6O,EAAAA,4BAA4B,CAACvP,KAAD,EAAQ;AAChC,QAAIA,KAAK,KAAK,YAAd,EAA4B;AACxB,YAAMwP,KAAK,GAAGhL,QAAQ,CAACC,cAAT,CAAwB,sBAAxB,CAAd;;AACA,UAAI+K,KAAJ,EAAW;AACPA,QAAAA,KAAK,CAACC,KAAN,CAAYC,OAAZ,GAAsB,OAAtB;AACH;AACJ,KALD,MAMK;AACD,YAAMF,KAAK,GAAGhL,QAAQ,CAACC,cAAT,CAAwB,kBAAxB,CAAd;;AACA,UAAI+K,KAAJ,EAAW;AACPA,QAAAA,KAAK,CAACC,KAAN,CAAYC,OAAZ,GAAsB,OAAtB;AACH;AACJ;AACJ;;AACDC,EAAAA,4BAA4B,CAAC3P,KAAD,EAAQ;AAChC,QAAIA,KAAK,KAAK,YAAd,EAA4B;AACxB,YAAMwP,KAAK,GAAGhL,QAAQ,CAACC,cAAT,CAAwB,sBAAxB,CAAd;;AACA,UAAI+K,KAAJ,EAAW;AACPA,QAAAA,KAAK,CAACC,KAAN,CAAYC,OAAZ,GAAsB,MAAtB;AACH;;AACD,YAAME,eAAe,GAAGpL,QAAQ,CAACC,cAAT,CAAwB,WAAxB,CAAxB;;AACA,UAAImL,eAAJ,EAAqB;AACjBA,QAAAA,eAAe,CAACjgB,KAAhB,GAAwB,EAAxB;AACH;AACJ,KATD,MAUK;AACD,YAAM6f,KAAK,GAAGhL,QAAQ,CAACC,cAAT,CAAwB,kBAAxB,CAAd;;AACA,UAAI+K,KAAJ,EAAW;AACPA,QAAAA,KAAK,CAACC,KAAN,CAAYC,OAAZ,GAAsB,MAAtB;AACH;;AACD,YAAMG,WAAW,GAAGrL,QAAQ,CAACC,cAAT,CAAwB,SAAxB,CAApB;;AACA,UAAIoL,WAAJ,EAAiB;AACbA,QAAAA,WAAW,CAAClgB,KAAZ,GAAoB,EAApB;AACH;AACJ;AACJ;;AACDmgB,EAAAA,aAAa,GAAG;AACZ,UAAMC,YAAY,GAAGvL,QAAQ,CAACC,cAAT,CAAwB,WAAxB,CAArB;AACA,UAAMzE,KAAK,GAAG+P,YAAY,GAAGA,YAAY,CAACpgB,KAAhB,GAAwB,EAAlD;AACAsQ,IAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBF,KAArB;AACA,UAAM4B,IAAI,GAAG;AACT5I,MAAAA,SAAS,EAAEgH;AADF,KAAb;AAGA,SAAK9D,mBAAL,CAAyB4T,aAAzB,CAAuClO,IAAvC,EAA6C/B,SAA7C,CAAwDU,GAAD,IAAS;AAC5D,UAAIA,GAAG,CAAC0J,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,aAAK9N,MAAL,CAAY+Q,OAAZ,CAAoB,mCAApB;AACA,aAAKpM,gBAAL;AACA,aAAK6O,4BAAL,CAAkC,YAAlC;AACH;AACJ,KAND,EAMIjP,KAAD,IAAW;AACV,UAAIA,KAAK,CAACyK,MAAN,KAAiB,GAArB,EAA0B;AACtB,aAAKhP,MAAL,CAAYuE,KAAZ,CAAkB,0CAAlB;AACA;AACH;;AACD,WAAKvE,MAAL,CAAYuE,KAAZ,CAAkB,0BAAlB;AACAT,MAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ,EAA6CQ,KAA7C;AACH,KAbD;AAcH;;AACDsP,EAAAA,SAAS,GAAG;AACR,UAAMD,YAAY,GAAGvL,QAAQ,CAACC,cAAT,CAAwB,SAAxB,CAArB;AACA,UAAMzE,KAAK,GAAG+P,YAAY,GAAGA,YAAY,CAACpgB,KAAhB,GAAwB,EAAlD;AACAsQ,IAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBF,KAArB;AACA,UAAM4B,IAAI,GAAG;AACT7H,MAAAA,OAAO,EAAEiG;AADA,KAAb;AAGA,SAAK9D,mBAAL,CAAyB8T,SAAzB,CAAmCpO,IAAnC,EAAyC/B,SAAzC,CAAoDU,GAAD,IAAS;AACxD,UAAIA,GAAG,CAAC0J,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,aAAK9N,MAAL,CAAY+Q,OAAZ,CAAoB,+BAApB;AACA,aAAKhM,YAAL;AACA,aAAKyO,4BAAL,CAAkC,QAAlC;AACH;AACJ,KAND,EAMIjP,KAAD,IAAW;AACV,UAAIA,KAAK,CAACyK,MAAN,KAAiB,GAArB,EAA0B;AACtB,aAAKhP,MAAL,CAAYuE,KAAZ,CAAkB,sCAAlB;AACA;AACH;;AACD,WAAKvE,MAAL,CAAYuE,KAAZ,CAAkB,sBAAlB;AACAT,MAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyCQ,KAAzC;AACH,KAbD;AAcH;;AAx3C6B;;AA03ClC5E,oBAAoB,CAACmU,IAArB,GAA4B,SAASC,4BAAT,CAAsCC,CAAtC,EAAyC;AAAE,SAAO,KAAKA,CAAC,IAAIrU,oBAAV,EAAgCvS,EAAE,CAAC6mB,iBAAH,CAAqB5mB,EAAE,CAAC6mB,MAAxB,CAAhC,EAAiE9mB,EAAE,CAAC6mB,iBAAH,CAAqB3mB,EAAE,CAAC6mB,WAAxB,CAAjE,EAAuG/mB,EAAE,CAAC6mB,iBAAH,CAAqB1mB,EAAE,CAAC6mB,mBAAxB,CAAvG,EAAqJhnB,EAAE,CAAC6mB,iBAAH,CAAqBzmB,EAAE,CAAC6mB,aAAxB,CAArJ,EAA6LjnB,EAAE,CAAC6mB,iBAAH,CAAqB5mB,EAAE,CAACinB,cAAxB,CAA7L,EAAsOlnB,EAAE,CAAC6mB,iBAAH,CAAqBxmB,EAAE,CAAC8mB,UAAxB,CAAtO,EAA2QnnB,EAAE,CAAC6mB,iBAAH,CAAqBvmB,EAAE,CAAC8mB,iBAAxB,CAA3Q,EAAuTpnB,EAAE,CAAC6mB,iBAAH,CAAqB5mB,EAAE,CAACinB,cAAxB,CAAvT,CAAP;AAAyW,CAAhb;;AACA3U,oBAAoB,CAAC8U,IAArB,GAA4B,aAAcrnB,EAAE,CAACsnB,iBAAH,CAAqB;AAAElM,EAAAA,IAAI,EAAE7I,oBAAR;AAA8BgV,EAAAA,SAAS,EAAE,CAAC,CAAC,mBAAD,CAAD,CAAzC;AAAkEC,EAAAA,YAAY,EAAE,SAASC,iCAAT,CAA2C7mB,EAA3C,EAA+CC,GAA/C,EAAoD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AACzMZ,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASwkB,6CAAT,CAAuDljB,MAAvD,EAA+D;AAAE,eAAO3D,GAAG,CAACgiB,cAAJ,CAAmBre,MAAnB,CAAP;AAAoC,OAA5H,EAA8H,KAA9H,EAAqIxE,EAAE,CAAC2nB,iBAAxI;AACH;AAAE,GAFwD;AAEtDC,EAAAA,KAAK,EAAE,GAF+C;AAE1CC,EAAAA,IAAI,EAAE,EAFoC;AAEhCC,EAAAA,MAAM,EAAE,CAAC,CAAC,CAAD,EAAI,iBAAJ,CAAD,EAAyB,CAAC,CAAD,EAAI,kBAAJ,CAAzB,EAAkD,CAAC,CAAD,EAAI,aAAJ,EAAmB,YAAnB,EAAiC,YAAjC,EAA+C,aAA/C,EAA8D,YAA9D,EAA4E,aAA5E,EAA2F,MAA3F,CAAlD,EAAsJ,CAAC,CAAD,EAAI,WAAJ,EAAiB,CAAjB,EAAoB,kBAApB,EAAwC,kBAAxC,CAAtJ,EAAmN,CAAC,CAAD,EAAI,WAAJ,CAAnN,EAAqO,CAAC,CAAD,EAAI,SAAJ,EAAe,WAAf,EAA4B,MAA5B,EAAoC,MAApC,CAArO,EAAkR,CAAC,CAAD,EAAI,SAAJ,EAAe,WAAf,CAAlR,EAA+S,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,EAAmB,MAAnB,CAA/S,EAA2U,CAAC,OAAD,EAAU,0BAAV,EAAsC,CAAtC,EAAyC,MAAzC,CAA3U,EAA6X,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,EAA8B,MAA9B,CAA7X,EAAoa,CAAC,KAAD,EAAQ,MAAR,EAAgB,CAAhB,EAAmB,OAAnB,CAApa,EAAic,CAAC,CAAD,EAAI,sBAAJ,CAAjc,EAA8d,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,MAApC,EAA4C,QAA5C,EAAsD,SAAtD,EAAiE,CAAjE,EAAoE,cAApE,EAAoF,iBAApF,EAAuG,CAAvG,EAA0G,UAA1G,EAAsH,QAAtH,CAA9d,EAA+lB,CAAC,KAAD,EAAQ,WAAR,EAAqB,OAArB,EAA8B,aAA9B,EAA6C,CAA7C,EAAgD,KAAhD,EAAuD,CAAvD,EAA0D,MAA1D,CAA/lB,EAAkqB,CAAC,OAAD,EAAU,aAAV,EAAyB,CAAzB,EAA4B,MAA5B,CAAlqB,EAAusB,CAAC,OAAD,EAAU,kBAAV,EAA8B,CAA9B,EAAiC,OAAjC,EAA0C,CAA1C,EAA6C,MAA7C,CAAvsB,EAA6vB,CAAC,OAAD,EAAU,eAAV,EAA2B,CAA3B,EAA8B,MAA9B,CAA7vB,EAAoyB,CAAC,OAAD,EAAU,UAAV,EAAsB,CAAtB,EAAyB,MAAzB,CAApyB,EAAs0B,CAAC,OAAD,EAAU,mBAAV,EAA+B,OAA/B,EAAwC,eAAxC,EAAyD,CAAzD,EAA4D,MAA5D,CAAt0B,EAA24B,CAAC,OAAD,EAAU,kBAAV,EAA8B,CAA9B,EAAiC,MAAjC,CAA34B,EAAq7B,CAAC,CAAD,EAAI,SAAJ,EAAe,WAAf,EAA4B,MAA5B,EAAoC,MAApC,EAA4C,MAA5C,CAAr7B,EAA0+B,CAAC,CAAD,EAAI,YAAJ,EAAkB,MAAlB,EAA0B,MAA1B,CAA1+B,EAA6gC,CAAC,KAAD,EAAQ,kBAAR,EAA4B,CAA5B,EAA+B,gBAA/B,EAAiD,OAAjD,EAA0D,MAA1D,CAA7gC,EAAglC,CAAC,CAAD,EAAI,QAAJ,EAAc,WAAd,CAAhlC,EAA4mC,CAAC,OAAD,EAAU,KAAV,EAAiB,CAAjB,EAAoB,UAApB,EAAgC,OAAhC,EAAyC,CAAzC,EAA4C,OAA5C,EAAqD,SAArD,CAA5mC,EAA6qC,CAAC,CAAD,EAAI,YAAJ,EAAkB,MAAlB,EAA0B,MAA1B,CAA7qC,EAAgtC,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,gBAA3B,EAA6C,OAA7C,EAAsD,MAAtD,CAAhtC,EAA+wC,CAAC,KAAD,EAAQ,kBAAR,EAA4B,CAA5B,EAA+B,OAA/B,EAAwC,MAAxC,CAA/wC,EAAg0C,CAAC,CAAD,EAAI,YAAJ,EAAkB,aAAlB,EAAiC,MAAjC,CAAh0C,EAA02C,CAAC,KAAD,EAAQ,wBAAR,EAAkC,CAAlC,EAAqC,kBAArC,CAA12C,EAAo6C,CAAC,SAAD,EAAY,EAAZ,EAAgB,UAAhB,EAA4B,EAA5B,EAAgC,MAAhC,EAAwC,UAAxC,EAAoD,IAApD,EAA0D,wBAA1D,EAAoF,CAApF,EAAuF,kBAAvF,EAA2G,uBAA3G,EAAoI,MAApI,CAAp6C,EAAijD,CAAC,CAAD,EAAI,YAAJ,EAAkB,aAAlB,CAAjjD,EAAmlD,CAAC,KAAD,EAAQ,YAAR,EAAsB,CAAtB,EAAyB,gBAAzB,EAA2C,kBAA3C,EAA+D,OAA/D,CAAnlD,EAA4pD,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,YAA3B,EAAyC,iBAAzC,EAA4D,iBAA5D,EAA+E,CAA/E,EAAkF,kBAAlF,CAA5pD,EAAmwD,CAAC,KAAD,EAAQ,gBAAR,EAA0B,CAA1B,EAA6B,gBAA7B,EAA+C,OAA/C,EAAwD,kBAAxD,CAAnwD,EAAg1D,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,gBAA3B,EAA6C,iBAA7C,EAAgE,qBAAhE,EAAuF,CAAvF,EAA0F,kBAA1F,CAAh1D,EAA+7D,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,gBAA3B,EAA6C,OAA7C,EAAsD,kBAAtD,CAA/7D,EAA0gE,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,cAA3B,EAA2C,iBAA3C,EAA8D,mBAA9D,EAAmF,CAAnF,EAAsF,kBAAtF,CAA1gE,EAAqnE,CAAC,KAAD,EAAQ,uBAAR,EAAiC,CAAjC,EAAoC,gBAApC,EAAsD,OAAtD,EAA+D,kBAA/D,CAArnE,EAAysE,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,uBAA3B,EAAoD,iBAApD,EAAuE,oBAAvE,EAA6F,CAA7F,EAAgG,kBAAhG,CAAzsE,EAA8zE,CAAC,KAAD,EAAQ,gBAAR,EAA0B,CAA1B,EAA6B,gBAA7B,EAA+C,OAA/C,EAAwD,kBAAxD,CAA9zE,EAA24E,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,gBAA3B,EAA6C,iBAA7C,EAAgE,qBAAhE,EAAuF,CAAvF,EAA0F,kBAA1F,CAA34E,EAA0/E,CAAC,KAAD,EAAQ,oBAAR,EAA8B,CAA9B,EAAiC,gBAAjC,EAAmD,OAAnD,EAA4D,kBAA5D,CAA1/E,EAA2kF,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,oBAA3B,EAAiD,iBAAjD,EAAoE,2BAApE,EAAiG,CAAjG,EAAoG,kBAApG,CAA3kF,EAAosF,CAAC,KAAD,EAAQ,YAAR,EAAsB,CAAtB,EAAyB,gBAAzB,EAA2C,OAA3C,EAAoD,MAApD,CAApsF,EAAiwF,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,mBAA3B,EAAgD,iBAAhD,EAAmE,mBAAnE,EAAwF,CAAxF,EAA2F,kBAA3F,CAAjwF,EAAi3F,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,EAA8B,MAA9B,EAAsC,UAAtC,EAAkD,WAAlD,EAA+D,MAA/D,CAAj3F,EAAy7F,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,OAA1B,EAAmC,iBAAnC,EAAsD,MAAtD,CAAz7F,EAAw/F,CAAC,CAAD,EAAI,wBAAJ,EAA8B,kCAA9B,EAAkE,MAAlE,CAAx/F,EAAmkG,CAAC,CAAD,EAAI,mBAAJ,CAAnkG,EAA6lG,CAAC,OAAD,EAAU,kBAAV,EAA8B,CAA9B,EAAiC,MAAjC,CAA7lG,EAAuoG,CAAC,MAAD,EAAS,MAAT,EAAiB,aAAjB,EAAgC,mBAAhC,EAAqD,UAArD,EAAiE,EAAjE,EAAqE,CAArE,EAAwE,cAAxE,EAAwF,WAAxF,EAAqG,iBAArG,EAAwH,CAAxH,EAA2H,aAA3H,EAA0I,OAA1I,EAAmJ,OAAnJ,CAAvoG,EAAoyG,CAAC,OAAD,EAAU,eAAV,EAA2B,CAA3B,EAA8B,MAA9B,CAApyG,EAA20G,CAAC,OAAD,EAAU,oBAAV,EAAgC,CAAhC,EAAmC,MAAnC,CAA30G,EAAu3G,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,OAA1B,EAAmC,MAAnC,CAAv3G,EAAm6G,CAAC,CAAD,EAAI,WAAJ,EAAiB,CAAjB,EAAoB,OAApB,CAAn6G,EAAi8G,CAAC,CAAD,EAAI,wBAAJ,EAA8B,kCAA9B,EAAkE,MAAlE,CAAj8G,EAA4gH,CAAC,IAAD,EAAO,kBAAP,EAA2B,CAA3B,EAA8B,kBAA9B,EAAkD,OAAlD,CAA5gH,EAAwkH,CAAC,CAAD,EAAI,cAAJ,EAAoB,uBAApB,CAAxkH,EAAsnH,CAAC,CAAD,EAAI,eAAJ,CAAtnH,EAA4oH,CAAC,CAAD,EAAI,cAAJ,CAA5oH,EAAiqH,CAAC,IAAD,EAAO,qBAAP,EAA8B,CAA9B,EAAiC,aAAjC,EAAgD,MAAhD,CAAjqH,EAA0tH,CAAC,MAAD,EAAS,QAAT,EAAmB,cAAnB,EAAmC,kBAAnC,EAAuD,CAAvD,EAA0D,OAA1D,EAAmE,CAAnE,EAAsE,OAAtE,EAA+E,OAA/E,EAAwF,CAAxF,EAA2F,OAA3F,CAA1tH,EAA+zH,CAAC,CAAD,EAAI,YAAJ,CAA/zH,EAAk1H,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,SAAvB,EAAkC,aAAlC,EAAiD,uBAAjD,EAA0E,CAA1E,EAA6E,cAA7E,CAAl1H,EAAg7H,CAAC,CAAD,EAAI,cAAJ,CAAh7H,EAAq8H,CAAC,MAAD,EAAS,QAAT,EAAmB,iBAAnB,EAAsC,kBAAtC,EAA0D,CAA1D,EAA6D,KAA7D,EAAoE,aAApE,EAAmF,CAAnF,EAAsF,OAAtF,CAAr8H,EAAqiI,CAAC,MAAD,EAAS,QAAT,EAAmB,iBAAnB,EAAsC,kBAAtC,EAA0D,CAA1D,EAA6D,KAA7D,EAAoE,eAApE,EAAqF,CAArF,EAAwF,OAAxF,CAAriI,EAAuoI,CAAC,KAAD,EAAQ,YAAR,EAAsB,CAAtB,EAAyB,OAAzB,EAAkC,MAAlC,CAAvoI,EAAkrI,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,mBAA3B,EAAgD,iBAAhD,EAAmE,mBAAnE,EAAwF,CAAxF,EAA2F,kBAA3F,CAAlrI,EAAkyI,CAAC,IAAD,EAAO,sBAAP,EAA+B,CAA/B,EAAkC,sBAAlC,EAA0D,OAA1D,CAAlyI,EAAs2I,CAAC,IAAD,EAAO,yBAAP,EAAkC,CAAlC,EAAqC,aAArC,EAAoD,MAApD,CAAt2I,EAAm6I,CAAC,MAAD,EAAS,QAAT,EAAmB,cAAnB,EAAmC,sBAAnC,EAA2D,CAA3D,EAA8D,OAA9D,EAAuE,CAAvE,EAA0E,OAA1E,EAAmF,OAAnF,EAA4F,CAA5F,EAA+F,OAA/F,CAAn6I,EAA4gJ,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,WAAvB,EAAoC,aAApC,EAAmD,2BAAnD,EAAgF,CAAhF,EAAmF,cAAnF,CAA5gJ,EAAgnJ,CAAC,MAAD,EAAS,QAAT,EAAmB,iBAAnB,EAAsC,sBAAtC,EAA8D,CAA9D,EAAiE,KAAjE,EAAwE,aAAxE,EAAuF,CAAvF,EAA0F,OAA1F,CAAhnJ,EAAotJ,CAAC,MAAD,EAAS,QAAT,EAAmB,iBAAnB,EAAsC,sBAAtC,EAA8D,CAA9D,EAAiE,KAAjE,EAAwE,eAAxE,EAAyF,CAAzF,EAA4F,OAA5F,CAAptJ,EAA0zJ,CAAC,KAAD,EAAQ,QAAR,EAAkB,CAAlB,EAAqB,gBAArB,EAAuC,OAAvC,CAA1zJ,EAA22J,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,QAAvB,EAAiC,iBAAjC,EAAoD,QAApD,EAA8D,aAA9D,EAA6E,iBAA7E,EAAgG,CAAhG,EAAmG,cAAnG,CAA32J,EAA+9J,CAAC,KAAD,EAAQ,SAAR,EAAmB,CAAnB,EAAsB,gBAAtB,EAAwC,OAAxC,CAA/9J,EAAihK,CAAC,MAAD,EAAS,OAAT,EAAkB,IAAlB,EAAwB,SAAxB,EAAmC,iBAAnC,EAAsD,SAAtD,EAAiE,aAAjE,EAAgF,kBAAhF,EAAoG,cAApG,EAAoH,cAApH,EAAoI,CAApI,EAAuI,cAAvI,CAAjhK,EAAyqK,CAAC,CAAD,EAAI,aAAJ,CAAzqK,EAA6rK,CAAC,CAAD,EAAI,MAAJ,CAA7rK,EAA0sK,CAAC,KAAD,EAAQ,YAAR,EAAsB,CAAtB,EAAyB,gBAAzB,EAA2C,OAA3C,CAA1sK,EAA+vK,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,YAA3B,EAAyC,iBAAzC,EAA4D,YAA5D,EAA0E,aAA1E,EAAyF,gBAAzF,EAA2G,cAA3G,EAA2H,cAA3H,EAA2I,CAA3I,EAA8I,cAA9I,CAA/vK,EAA85K,CAAC,CAAD,EAAI,IAAJ,EAAU,QAAV,EAAoB,mBAApB,EAAyC,iBAAzC,EAA4D,CAA5D,EAA+D,OAA/D,CAA95K,EAAu+K,CAAC,oBAAD,EAAuB,EAAvB,CAAv+K,EAAmgL,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,aAAxB,EAAuC,CAAvC,EAA0C,KAA1C,CAAngL,EAAqjL,CAAC,CAAD,EAAI,kBAAJ,EAAwB,CAAxB,EAA2B,OAA3B,CAArjL,EAA0lL,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,EAAmB,MAAnB,CAA1lL,EAAsnL,CAAC,CAAD,EAAI,eAAJ,EAAqB,aAArB,EAAoC,WAApC,CAAtnL,EAAwqL,CAAC,CAAD,EAAI,MAAJ,CAAxqL,EAAqrL,CAAC,QAAD,EAAW,KAAX,EAAkB,CAAlB,EAAqB,sBAArB,EAA6C,MAA7C,EAAqD,CAArD,EAAwD,mBAAxD,EAA6E,aAA7E,EAA4F,qBAA5F,EAAmH,cAAnH,EAAmI,aAAnI,EAAkJ,cAAlJ,EAAkK,iBAAlK,CAArrL,EAA22L,CAAC,CAAD,EAAI,KAAJ,EAAW,aAAX,EAA0B,QAA1B,EAAoC,KAApC,EAA2C,CAA3C,EAA8C,OAA9C,CAA32L,EAAm6L,CAAC,CAAD,EAAI,KAAJ,EAAW,eAAX,EAA4B,QAA5B,EAAsC,KAAtC,EAA6C,CAA7C,EAAgD,OAAhD,CAAn6L,EAA69L,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,CAA79L,EAAi/L,CAAC,CAAD,EAAI,YAAJ,EAAkB,MAAlB,CAAj/L,EAA4gM,CAAC,MAAD,EAAS,UAAT,EAAqB,iBAArB,EAAwC,YAAxC,EAAsD,IAAtD,EAA4D,YAA5D,CAA5gM,EAAulM,CAAC,KAAD,EAAQ,YAAR,EAAsB,CAAtB,EAAyB,OAAzB,EAAkC,MAAlC,EAA0C,MAA1C,CAAvlM,EAA0oM,CAAC,MAAD,EAAS,UAAT,EAAqB,iBAArB,EAAwC,oBAAxC,EAA8D,IAA9D,EAAoE,oBAApE,EAA0F,CAA1F,EAA6F,QAA7F,CAA1oM,EAAkvM,CAAC,KAAD,EAAQ,oBAAR,EAA8B,CAA9B,EAAiC,OAAjC,EAA0C,MAA1C,EAAkD,MAAlD,CAAlvM,EAA6yM,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,OAA1B,CAA7yM,EAAi1M,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,aAAvB,EAAsC,iBAAtC,EAAyD,aAAzD,EAAwE,aAAxE,EAAuF,iBAAvF,EAA0G,CAA1G,EAA6G,cAA7G,CAAj1M,EAA+8M,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,OAAxB,CAA/8M,EAAi/M,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,WAApC,EAAiD,QAAjD,EAA2D,SAA3D,EAAsE,CAAtE,EAAyE,cAAzE,EAAyF,iBAAzF,EAA4G,CAA5G,EAA+G,UAA/G,EAA2H,QAA3H,CAAj/M,EAAunN,CAAC,KAAD,EAAQ,gBAAR,EAA0B,OAA1B,EAAmC,aAAnC,EAAkD,CAAlD,EAAqD,KAArD,EAA4D,CAA5D,EAA+D,MAA/D,CAAvnN,EAA+rN,CAAC,KAAD,EAAQ,gBAAR,EAA0B,CAA1B,EAA6B,aAA7B,EAA4C,CAA5C,EAA+C,KAA/C,CAA/rN,EAAsvN,CAAC,CAAD,EAAI,aAAJ,EAAmB,MAAnB,CAAtvN,EAAkxN,CAAC,CAAD,EAAI,KAAJ,EAAW,aAAX,EAA0B,MAA1B,EAAkC,MAAlC,EAA0C,MAA1C,EAAkD,YAAlD,EAAgE,CAAhE,EAAmE,OAAnE,CAAlxN,EAA+1N,CAAC,YAAD,EAAe,oBAAf,EAAqC,CAArC,EAAwC,KAAxC,EAA+C,WAA/C,EAA4D,MAA5D,CAA/1N,EAAo6N,CAAC,CAAD,EAAI,KAAJ,EAAW,CAAX,EAAc,OAAd,CAAp6N,EAA47N,CAAC,CAAD,EAAI,YAAJ,CAA57N,EAA+8N,CAAC,CAAD,EAAI,kBAAJ,CAA/8N,EAAw+N,CAAC,CAAD,EAAI,iBAAJ,CAAx+N,EAAggO,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,wBAAtB,EAAgD,CAAhD,EAAmD,OAAnD,CAAhgO,EAA6jO,CAAC,CAAD,EAAI,eAAJ,CAA7jO,EAAmlO,CAAC,CAAD,EAAI,OAAJ,EAAa,CAAb,EAAgB,OAAhB,EAAyB,SAAzB,CAAnlO,EAAwnO,CAAC,CAAD,EAAI,OAAJ,CAAxnO,EAAsoO,CAAC,CAAD,EAAI,oBAAJ,CAAtoO,EAAiqO,CAAC,CAAD,EAAI,KAAJ,EAAW,aAAX,EAA0B,MAA1B,EAAkC,MAAlC,EAA0C,YAA1C,EAAwD,CAAxD,EAA2D,OAA3D,CAAjqO,CAFwB;AAE+sOC,EAAAA,QAAQ,EAAE,SAASC,6BAAT,CAAuCpnB,EAAvC,EAA2CC,GAA3C,EAAgD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AAC90OZ,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,aAArB;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,IAArB,EAA2B,CAA3B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,gBAAb;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBV,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,CAArE;AACAX,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBY,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,CAArE;AACAjC,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkB0B,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,CAArE;AACA/C,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,WAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,QAAd,EAAwB,SAAS+kB,sDAAT,CAAgEzjB,MAAhE,EAAwE;AAAE,eAAO3D,GAAG,CAACqG,cAAJ,CAAmB1C,MAAnB,EAA2B,MAA3B,CAAP;AAA4C,OAA9I;AACAxE,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBqC,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA1D,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkByC,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA9D,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkB0C,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA/D,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkB2C,kCAAlB,EAAsD,CAAtD,EAAyD,CAAzD,EAA4D,GAA5D,EAAiE,EAAjE;AACAhE,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBgD,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACArE,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkB6E,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAlG,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkByF,oCAAlB,EAAwD,EAAxD,EAA4D,CAA5D,EAA+D,KAA/D,EAAsE,EAAtE;AACA9G,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBsG,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA3H,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBsH,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA3I,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,IAAtB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,aAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,wCAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkB2H,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAhJ,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,SAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkByI,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA9J,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,yBAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBiJ,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACAtK,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkB0J,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA/K,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,IAAtB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,UAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,2EAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAjB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,QAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAjB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkB+J,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACApL,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,WAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAjB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBwK,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA7L,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,UAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAjB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBgL,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACArM,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,oBAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAjB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBwL,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACA7M,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,oBAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAjB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,EAAd,EAAkBgM,oCAAlB,EAAwD,CAAxD,EAA2D,CAA3D,EAA8D,KAA9D,EAAqE,EAArE;AACArN,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,4BAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,GAAb,EAAkB,OAAlB,EAA2B,EAA3B;AACAjB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmBwM,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACA7N,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,GAAb,EAAkB,IAAlB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,qCAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,GAAb,EAAkB,OAAlB,EAA2B,EAA3B;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmB+M,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACApO,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,GAAb,EAAkB,IAAlB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,YAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmBsN,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACA3O,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASglB,sDAAT,GAAkE;AAAE,eAAOrnB,GAAG,CAACghB,aAAJ,CAAkB,aAAlB,CAAP;AAA0C,OAArI,EAAuI,OAAvI,EAAgJ,SAASsG,sDAAT,GAAkE;AAAE,eAAOtnB,GAAG,CAAC+S,YAAJ,GAAmB,IAA1B;AAAiC,OAArP;AACA5T,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmBqO,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACA1P,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmBwO,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACA7P,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,GAAb,EAAkB,IAAlB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,QAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,GAAvB,EAA4B,EAA5B;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASklB,kDAAT,GAA8D;AAAE,eAAOvnB,GAAG,CAACmlB,4BAAJ,CAAiC,QAAjC,CAAP;AAAoD,OAA3I;AACAhmB,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,WAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmByO,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACA9P,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASmlB,sDAAT,GAAkE;AAAE,eAAOxnB,GAAG,CAACghB,aAAJ,CAAkB,aAAlB,CAAP;AAA0C,OAArI,EAAuI,OAAvI,EAAgJ,SAASyG,sDAAT,GAAkE;AAAE,eAAOznB,GAAG,CAAC8hB,uBAAJ,GAA8B,IAArC;AAA4C,OAAhQ;AACA3iB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmBoP,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACAzQ,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmBuP,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACA5Q,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,IAAvB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,gBAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASqlB,uDAAT,GAAmE;AAAE,eAAO1nB,GAAG,CAACulB,4BAAJ,CAAiC,QAAjC,CAAP;AAAoD,OAAhJ;AACApmB,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,QAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACiB,SAAH,CAAa,GAAb,EAAkB,OAAlB,EAA2B,EAA3B;AACAjB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASslB,uDAAT,GAAmE;AAAE,eAAO3nB,GAAG,CAAC4lB,SAAJ,EAAP;AAAyB,OAArH;AACAzmB,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,KAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASulB,uDAAT,GAAmE;AAAE,eAAO5nB,GAAG,CAACulB,4BAAJ,CAAiC,QAAjC,CAAP;AAAoD,OAAhJ;AACApmB,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,QAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,GAAb,EAAkB,IAAlB;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,UAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACiB,SAAH,CAAa,GAAb,EAAkB,OAAlB,EAA2B,EAA3B;AACAjB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmBwP,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACA7Q,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAASwlB,sDAAT,GAAkE;AAAE,eAAO7nB,GAAG,CAACoiB,eAAJ,EAAP;AAA+B,OAA1H,EAA4H,OAA5H,EAAqI,SAAS0F,sDAAT,GAAkE;AAAE,eAAO9nB,GAAG,CAACgT,oBAAJ,GAA2B,IAAlC;AAAyC,OAAlP;AACA7T,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmBqQ,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACA1R,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmBwQ,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACA7R,MAAAA,EAAE,CAACqB,UAAH,CAAc,GAAd,EAAmB6Q,qCAAnB,EAA0D,CAA1D,EAA6D,CAA7D,EAAgE,KAAhE,EAAuE,EAAvE;AACAlS,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,IAAvB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,oBAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS0lB,uDAAT,GAAmE;AAAE,eAAO/nB,GAAG,CAACulB,4BAAJ,CAAiC,YAAjC,CAAP;AAAwD,OAApJ;AACApmB,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,QAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACiB,SAAH,CAAa,GAAb,EAAkB,OAAlB,EAA2B,EAA3B;AACAjB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS2lB,uDAAT,GAAmE;AAAE,eAAOhoB,GAAG,CAAC0lB,aAAJ,EAAP;AAA6B,OAAzH;AACAvmB,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,KAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAd,MAAAA,EAAE,CAACkD,UAAH,CAAc,OAAd,EAAuB,SAAS4lB,uDAAT,GAAmE;AAAE,eAAOjoB,GAAG,CAACulB,4BAAJ,CAAiC,YAAjC,CAAP;AAAwD,OAApJ;AACApmB,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,QAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,QAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAImoB,OAAJ;AACA,UAAIC,OAAJ;AACA,UAAIC,OAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACAtpB,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC4J,kBAAH,CAAsB,EAAtB,EAA0B/I,GAAG,CAAC4V,KAA9B,EAAqC,SAArC;AACAzW,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,WAAd,EAA2Bb,GAAG,CAACc,iBAA/B;AACA3B,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC4V,KAAJ,KAAc,KAApC;AACAzW,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC4V,KAAJ,KAAc,KAApC;AACAzW,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC4V,KAAJ,KAAc,KAApC;AACAzW,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,UAAd,EAA0Bb,GAAG,CAACyG,UAA9B;AACAtH,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC+C,QAA1B;AACA5D,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACqnB,OAAO,GAAGloB,GAAG,CAACc,iBAAJ,CAAsBC,GAAtB,CAA0B,MAA1B,CAAX,KAAiD,IAAjD,GAAwD,IAAxD,GAA+DmnB,OAAO,CAAClnB,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCknB,OAAO,CAAClnB,MAAR,CAAe0F,iBAApI;AACAvH,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACsnB,OAAO,GAAGnoB,GAAG,CAACc,iBAAJ,CAAsBC,GAAtB,CAA0B,MAA1B,CAAX,KAAiD,IAAjD,GAAwD,IAAxD,GAA+DonB,OAAO,CAACnnB,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCmnB,OAAO,CAACnnB,MAAR,CAAe2F,wBAApI;AACAxH,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACb,GAAG,CAACsT,gBAAL,IAAyBtT,GAAG,CAAC8a,SAA7B,IAA0C,EAAE,CAACsN,OAAO,GAAGpoB,GAAG,CAACc,iBAAJ,CAAsBC,GAAtB,CAA0B,MAA1B,CAAX,KAAiD,IAAjD,GAAwD,IAAxD,GAA+DqnB,OAAO,CAACpnB,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgConB,OAAO,CAACpnB,MAAR,CAAe0F,iBAAhH,CAAhE;AACAvH,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAACsT,gBAAJ,IAAwB,EAAE,CAAC+U,QAAQ,GAAGroB,GAAG,CAACc,iBAAJ,CAAsBC,GAAtB,CAA0B,MAA1B,CAAZ,KAAkD,IAAlD,GAAyD,IAAzD,GAAgEsnB,QAAQ,CAACrnB,MAAT,IAAmB,IAAnB,GAA0B,IAA1B,GAAiCqnB,QAAQ,CAACrnB,MAAT,CAAgB0F,iBAAnH,CAA9C;AACAvH,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC4V,KAAJ,KAAc,KAApC;AACAzW,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsB,CAACynB,QAAQ,GAAGtoB,GAAG,CAACc,iBAAJ,CAAsBC,GAAtB,CAA0B,oBAA1B,CAAZ,KAAgE,IAAhE,GAAuE,IAAvE,GAA8EunB,QAAQ,CAAC/iB,KAA7G;AACApG,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC4G,qBAAJ,IAA6B,EAAE,CAAC2hB,QAAQ,GAAGvoB,GAAG,CAACc,iBAAJ,CAAsBC,GAAtB,CAA0B,WAA1B,CAAZ,KAAuD,IAAvD,GAA8D,IAA9D,GAAqEwnB,QAAQ,CAACvnB,MAAT,IAAmB,IAAnB,GAA0B,IAA1B,GAAiCunB,QAAQ,CAACvnB,MAAT,CAAgB0F,iBAAxH,CAAnD;AACAvH,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC4V,KAAJ,IAAa,MAAnC;AACAzW,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAACsS,iBAA7B;AACAnT,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAACwS,YAA7B;AACArT,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAACuS,QAA7B;AACApT,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC4V,KAAJ,IAAa,MAAnC;AACAzW,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAACyS,OAA7B;AACAtT,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAAC0S,WAA7B;AACAvT,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAAC2S,SAA7B;AACAxT,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAAC4S,iBAA7B;AACAzT,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAACkT,cAA7B;AACA/T,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAACqT,gBAA7B;AACAlU,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,SAAd,EAAyBb,GAAG,CAACoT,QAA7B;AACAjU,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAACuhB,OAAJ,KAAgB,CAACiH,QAAQ,GAAGxoB,GAAG,CAACc,iBAAJ,CAAsBC,GAAtB,CAA0B,aAA1B,CAAZ,KAAyD,IAAzD,GAAgE,IAAhE,GAAuEynB,QAAQ,CAACjjB,KAAhG,CAAtB;AACApG,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,aAAd,EAA6Bb,GAAG,CAAC2W,uBAAjC;AACAxX,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC+S,YAAJ,IAAoB/S,GAAG,CAAC+O,yBAAJ,CAA8BoI,MAA9B,GAAuC,CAAjF;AACAhY,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC+S,YAAJ,IAAoB/S,GAAG,CAAC+O,yBAAJ,CAA8BoI,MAA9B,KAAyC,CAAnF;AACAhY,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAACyhB,kBAAJ,KAA2B,CAACgH,QAAQ,GAAGzoB,GAAG,CAACc,iBAAJ,CAAsBC,GAAtB,CAA0B,aAA1B,CAAZ,KAAyD,IAAzD,GAAgE,IAAhE,GAAuE0nB,QAAQ,CAACljB,KAA3G,CAAtB;AACApG,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,aAAd,EAA6Bb,GAAG,CAAC4W,mBAAjC;AACAzX,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC8hB,uBAAJ,IAA+B9hB,GAAG,CAAC8P,qBAAJ,CAA0BqH,MAA1B,GAAmC,CAAxF;AACAhY,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC8hB,uBAAJ,IAA+B9hB,GAAG,CAAC8P,qBAAJ,CAA0BqH,MAA1B,KAAqC,CAA1F;AACAhY,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAACsQ,gBAA1B;AACAnR,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,aAAd,EAA6Bb,GAAG,CAAC6W,qBAAjC;AACA1X,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAACgT,oBAAJ,IAA4BhT,GAAG,CAAC+Q,iBAAJ,CAAsBoG,MAAtB,GAA+B,CAAjF;AACAhY,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC4V,KAAJ,KAAc,MAApC;AACAzW,MAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb;AACAzB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,MAAd,EAAsBb,GAAG,CAAC4V,KAAJ,IAAa,MAAnC;AACH;AAAE,GA/WwD;AA+WtD8S,EAAAA,UAAU,EAAE,CAAChpB,EAAE,CAACipB,gBAAJ,EAAsBtpB,EAAE,CAACupB,aAAzB,EAAwCvpB,EAAE,CAACwpB,oBAA3C,EAAiExpB,EAAE,CAACypB,kBAApE,EAAwFnpB,EAAE,CAACopB,IAA3F,EAAiG1pB,EAAE,CAAC2pB,oBAApG,EAA0H3pB,EAAE,CAAC4pB,eAA7H,EAA8I5pB,EAAE,CAAC6pB,eAAjJ,EAAkKvpB,EAAE,CAACwpB,OAArK,EAA8K9pB,EAAE,CAAC+pB,4BAAjL,EAA+M/pB,EAAE,CAACgqB,iBAAlN,EAAqOhqB,EAAE,CAACiqB,oBAAxO,EAA8P1pB,EAAE,CAAC2pB,qBAAjQ,EAAwRnqB,EAAE,CAACoqB,UAA3R,CA/W0C;AA+W8PC,EAAAA,KAAK,EAAE,CAAC5pB,GAAG,CAAC6pB,aAAL,CA/WrQ;AA+W0RC,EAAAA,MAAM,EAAE,CAAC,+rnBAAD;AA/WlS,CAArB,CAA1C", "sourcesContent": ["import { FormControl, Validators, } from '@angular/forms';\r\nimport { debounceTime, distinctUntilChanged, switchMap, map, catchError, } from 'rxjs/operators';\r\nimport { of } from 'rxjs';\r\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"@angular/router\";\r\nimport * as i2 from \"@angular/forms\";\r\nimport * as i3 from \"src/app/shared/services/data-transfer.service\";\r\nimport * as i4 from \"ngx-toastr\";\r\nimport * as i5 from \"@angular/common/http\";\r\nimport * as i6 from \"ngx-spinner\";\r\nimport * as i7 from \"../../../sidebar.component\";\r\nimport * as i8 from \"@angular/common\";\r\nimport * as i9 from \"ngx-image-cropper\";\r\nimport * as i10 from \"../../../../../camel-case.pipe\";\r\nfunction EditAppUserComponent_div_11_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 9);\r\n    i0.ɵɵelementStart(1, \"label\", 76);\r\n    i0.ɵɵtext(2, \"Sharer Name\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(3, \"input\", 77);\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_12_div_4_div_1_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \"Email is required.\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_12_div_4_div_2_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \"Invalid email format.\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_12_div_4_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 80);\r\n    i0.ɵɵtemplate(1, EditAppUserComponent_div_12_div_4_div_1_Template, 2, 0, \"div\", 81);\r\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_12_div_4_div_2_Template, 2, 0, \"div\", 81);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r33 = i0.ɵɵnextContext(2);\r\n    let tmp_0_0;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r33.addNewAppUserForm.get(\"U_email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.addNewAppUserForm.controls[\"U_email\"].hasError(\"invalidEmail\"));\r\n} }\r\nfunction EditAppUserComponent_div_12_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 9);\r\n    i0.ɵɵelementStart(1, \"label\", 78);\r\n    i0.ɵɵtext(2, \"Sharer Email\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(3, \"input\", 79);\r\n    i0.ɵɵtemplate(4, EditAppUserComponent_div_12_div_4_Template, 3, 2, \"div\", 14);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r1 = i0.ɵɵnextContext();\r\n    let tmp_0_0;\r\n    i0.ɵɵadvance(4);\r\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r1.addNewAppUserForm.get(\"U_email\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = ctx_r1.addNewAppUserForm.get(\"U_email\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = ctx_r1.addNewAppUserForm.get(\"U_email\")) == null ? null : tmp_0_0.touched)));\r\n} }\r\nfunction EditAppUserComponent_div_13_div_6_div_1_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \"Password is required.\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_13_div_6_div_2_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \"Password must be at least 8 characters long.\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_13_div_6_div_3_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \"Password must contain at least: \");\r\n    i0.ɵɵelementStart(2, \"ul\");\r\n    i0.ɵɵelementStart(3, \"li\");\r\n    i0.ɵɵtext(4, \"One uppercase letter\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(5, \"li\");\r\n    i0.ɵɵtext(6, \"One lowercase letter\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(7, \"li\");\r\n    i0.ɵɵtext(8, \"One number\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(9, \"li\");\r\n    i0.ɵɵtext(10, \"One special character\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_13_div_6_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 80);\r\n    i0.ɵɵtemplate(1, EditAppUserComponent_div_13_div_6_div_1_Template, 2, 0, \"div\", 81);\r\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_13_div_6_div_2_Template, 2, 0, \"div\", 81);\r\n    i0.ɵɵtemplate(3, EditAppUserComponent_div_13_div_6_div_3_Template, 11, 0, \"div\", 81);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r37 = i0.ɵɵnextContext(2);\r\n    let tmp_0_0;\r\n    let tmp_1_0;\r\n    let tmp_2_0;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r37.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r37.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.minlength);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r37.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors.pattern);\r\n} }\r\nfunction EditAppUserComponent_div_13_Template(rf, ctx) { if (rf & 1) {\r\n    const _r42 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 9);\r\n    i0.ɵɵelementStart(1, \"label\", 82);\r\n    i0.ɵɵtext(2, \"Sharer Password\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(3, \"input\", 83);\r\n    i0.ɵɵelementStart(4, \"i\", 84, 85);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_13_Template_i_click_4_listener() { i0.ɵɵrestoreView(_r42); const _r36 = i0.ɵɵreference(5); const ctx_r41 = i0.ɵɵnextContext(); return ctx_r41.togglePasswordVisibility(_r36, \"U_password\"); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(6, EditAppUserComponent_div_13_div_6_Template, 4, 3, \"div\", 14);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r2 = i0.ɵɵnextContext();\r\n    let tmp_0_0;\r\n    i0.ɵɵadvance(6);\r\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r2.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = ctx_r2.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = ctx_r2.addNewAppUserForm.get(\"U_password\")) == null ? null : tmp_0_0.touched)));\r\n} }\r\nfunction EditAppUserComponent_img_19_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"img\", 86);\r\n} if (rf & 2) {\r\n    const ctx_r3 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"src\", ctx_r3.imageSrc, i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction EditAppUserComponent_div_20_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 80);\r\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_21_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 80);\r\n    i0.ɵɵtext(1, \" The image must have a 1:1 aspect ratio. Please select a square image. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_a_22_Template(rf, ctx) { if (rf & 1) {\r\n    const _r44 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"a\", 87);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_a_22_Template_a_click_0_listener() { i0.ɵɵrestoreView(_r44); const ctx_r43 = i0.ɵɵnextContext(); return ctx_r43.showCropper(\"U_dp\"); });\r\n    i0.ɵɵtext(1, \"Edit DP\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_23_Template(rf, ctx) { if (rf & 1) {\r\n    const _r46 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 88);\r\n    i0.ɵɵelementStart(1, \"div\", 89);\r\n    i0.ɵɵelementStart(2, \"h5\", 90);\r\n    i0.ɵɵtext(3, \"Resize Image\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(4, \"image-cropper\", 91);\r\n    i0.ɵɵlistener(\"imageCropped\", function EditAppUserComponent_div_23_Template_image_cropper_imageCropped_4_listener($event) { i0.ɵɵrestoreView(_r46); const ctx_r45 = i0.ɵɵnextContext(); return ctx_r45.cropImg($event, \"U_dp\"); })(\"imageLoaded\", function EditAppUserComponent_div_23_Template_image_cropper_imageLoaded_4_listener() { i0.ɵɵrestoreView(_r46); const ctx_r47 = i0.ɵɵnextContext(); return ctx_r47.imgLoad(); })(\"cropperReady\", function EditAppUserComponent_div_23_Template_image_cropper_cropperReady_4_listener() { i0.ɵɵrestoreView(_r46); const ctx_r48 = i0.ɵɵnextContext(); return ctx_r48.initCropper(); })(\"loadImageFailed\", function EditAppUserComponent_div_23_Template_image_cropper_loadImageFailed_4_listener() { i0.ɵɵrestoreView(_r46); const ctx_r49 = i0.ɵɵnextContext(); return ctx_r49.imgFailed(); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(5, \"button\", 92);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_23_Template_button_click_5_listener() { i0.ɵɵrestoreView(_r46); const ctx_r50 = i0.ɵɵnextContext(); return ctx_r50.saveCroppedImage(\"U_dp\"); });\r\n    i0.ɵɵtext(6, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(7, \"button\", 93);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_23_Template_button_click_7_listener() { i0.ɵɵrestoreView(_r46); const ctx_r51 = i0.ɵɵnextContext(); return ctx_r51.hideCropper(\"U_dp\"); });\r\n    i0.ɵɵtext(8, \"Cancel\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r7 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(4);\r\n    i0.ɵɵproperty(\"imageChangedEvent\", ctx_r7.imgChangeEvt)(\"aspectRatio\", 1 / 1)(\"maintainAspectRatio\", true);\r\n} }\r\nfunction EditAppUserComponent_div_24_div_1_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 9);\r\n    i0.ɵɵelementStart(1, \"div\", 95);\r\n    i0.ɵɵelement(2, \"input\", 96);\r\n    i0.ɵɵelementStart(3, \"label\", 97);\r\n    i0.ɵɵtext(4, \" Make this an expert sharer \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_24_div_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r55 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 9);\r\n    i0.ɵɵelementStart(1, \"div\", 95);\r\n    i0.ɵɵelementStart(2, \"input\", 98);\r\n    i0.ɵɵlistener(\"change\", function EditAppUserComponent_div_24_div_2_Template_input_change_2_listener() { i0.ɵɵrestoreView(_r55); const ctx_r54 = i0.ɵɵnextContext(2); return ctx_r54.anonymousCheck(); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(3, \"label\", 99);\r\n    i0.ɵɵtext(4, \" Make this an anonymous sharer \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_24_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 94);\r\n    i0.ɵɵtemplate(1, EditAppUserComponent_div_24_div_1_Template, 5, 0, \"div\", 8);\r\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_24_div_2_Template, 5, 0, \"div\", 8);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r8 = i0.ɵɵnextContext();\r\n    let tmp_0_0;\r\n    let tmp_1_0;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !((tmp_0_0 = ctx_r8.addNewAppUserForm.get(\"U_profileAnonymous\")) == null ? null : tmp_0_0.value));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !((tmp_1_0 = ctx_r8.addNewAppUserForm.get(\"U_isExpert\")) == null ? null : tmp_1_0.value));\r\n} }\r\nfunction EditAppUserComponent_div_25_img_10_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"img\", 105);\r\n} if (rf & 2) {\r\n    const ctx_r56 = i0.ɵɵnextContext(2);\r\n    i0.ɵɵproperty(\"src\", ctx_r56.aliasImageSrc, i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction EditAppUserComponent_div_25_div_11_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 80);\r\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_25_div_12_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 80);\r\n    i0.ɵɵtext(1, \" The image must have a 1:1 aspect ratio. Please select a square image. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_25_a_13_Template(rf, ctx) { if (rf & 1) {\r\n    const _r61 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"a\", 87);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_25_a_13_Template_a_click_0_listener() { i0.ɵɵrestoreView(_r61); const ctx_r60 = i0.ɵɵnextContext(2); return ctx_r60.showCropper(\"U_aliasDp\"); });\r\n    i0.ɵɵtext(1, \"Edit DP\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_25_Template(rf, ctx) { if (rf & 1) {\r\n    const _r63 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 94);\r\n    i0.ɵɵelementStart(1, \"div\", 9);\r\n    i0.ɵɵelementStart(2, \"label\", 100);\r\n    i0.ɵɵtext(3, \"Alias Name\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(4, \"input\", 101);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(5, \"div\", 9);\r\n    i0.ɵɵelementStart(6, \"label\", 102);\r\n    i0.ɵɵtext(7, \"Alias DP\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(8, \"div\", 11);\r\n    i0.ɵɵelementStart(9, \"input\", 103);\r\n    i0.ɵɵlistener(\"change\", function EditAppUserComponent_div_25_Template_input_change_9_listener($event) { i0.ɵɵrestoreView(_r63); const ctx_r62 = i0.ɵɵnextContext(); return ctx_r62.onFileSelected($event, \"U_aliasDp\"); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(10, EditAppUserComponent_div_25_img_10_Template, 1, 1, \"img\", 104);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(11, EditAppUserComponent_div_25_div_11_Template, 2, 0, \"div\", 14);\r\n    i0.ɵɵtemplate(12, EditAppUserComponent_div_25_div_12_Template, 2, 0, \"div\", 14);\r\n    i0.ɵɵtemplate(13, EditAppUserComponent_div_25_a_13_Template, 2, 0, \"a\", 15);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r9 = i0.ɵɵnextContext();\r\n    let tmp_2_0;\r\n    let tmp_3_0;\r\n    let tmp_4_0;\r\n    i0.ɵɵadvance(9);\r\n    i0.ɵɵproperty(\"readonly\", ctx_r9.isReadonly);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.aliasImageSrc);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r9.addNewAppUserForm.get(\"U_aliasDp\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors.fileSizeValidator);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r9.addNewAppUserForm.get(\"U_aliasDp\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors.fileAspectRatioValidator);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isAliasCropperVisible && ctx_r9.aliasImageName && !((tmp_4_0 = ctx_r9.addNewAppUserForm.get(\"U_aliasDp\")) == null ? null : tmp_4_0.errors == null ? null : tmp_4_0.errors.fileSizeValidator));\r\n} }\r\nfunction EditAppUserComponent_div_26_Template(rf, ctx) { if (rf & 1) {\r\n    const _r65 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 88);\r\n    i0.ɵɵelementStart(1, \"div\", 89);\r\n    i0.ɵɵelementStart(2, \"h5\", 90);\r\n    i0.ɵɵtext(3, \"Resize Image\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(4, \"image-cropper\", 91);\r\n    i0.ɵɵlistener(\"imageCropped\", function EditAppUserComponent_div_26_Template_image_cropper_imageCropped_4_listener($event) { i0.ɵɵrestoreView(_r65); const ctx_r64 = i0.ɵɵnextContext(); return ctx_r64.cropImg($event, \"U_aliasDp\"); })(\"imageLoaded\", function EditAppUserComponent_div_26_Template_image_cropper_imageLoaded_4_listener() { i0.ɵɵrestoreView(_r65); const ctx_r66 = i0.ɵɵnextContext(); return ctx_r66.imgLoad(); })(\"cropperReady\", function EditAppUserComponent_div_26_Template_image_cropper_cropperReady_4_listener() { i0.ɵɵrestoreView(_r65); const ctx_r67 = i0.ɵɵnextContext(); return ctx_r67.initCropper(); })(\"loadImageFailed\", function EditAppUserComponent_div_26_Template_image_cropper_loadImageFailed_4_listener() { i0.ɵɵrestoreView(_r65); const ctx_r68 = i0.ɵɵnextContext(); return ctx_r68.imgFailed(); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(5, \"button\", 92);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_26_Template_button_click_5_listener() { i0.ɵɵrestoreView(_r65); const ctx_r69 = i0.ɵɵnextContext(); return ctx_r69.saveCroppedImage(\"U_aliasDp\"); });\r\n    i0.ɵɵtext(6, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(7, \"button\", 93);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_26_Template_button_click_7_listener() { i0.ɵɵrestoreView(_r65); const ctx_r70 = i0.ɵɵnextContext(); return ctx_r70.hideCropper(\"U_aliasDp\"); });\r\n    i0.ɵɵtext(8, \"Cancel\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r10 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(4);\r\n    i0.ɵɵproperty(\"imageChangedEvent\", ctx_r10.aliasImgChangeEvt)(\"aspectRatio\", 1 / 1)(\"maintainAspectRatio\", true);\r\n} }\r\nfunction EditAppUserComponent_div_27_Template(rf, ctx) { if (rf & 1) {\r\n    const _r72 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 106);\r\n    i0.ɵɵelementStart(1, \"button\", 107);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_27_Template_button_click_1_listener() { i0.ɵɵrestoreView(_r72); const ctx_r71 = i0.ɵɵnextContext(); return ctx_r71.updateUserDetails(); });\r\n    i0.ɵɵtext(2, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(3, \"button\", 108);\r\n    i0.ɵɵtext(4, \"Back\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_35_Template(rf, ctx) { if (rf & 1) {\r\n    const _r75 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_35_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r75); const activity_r73 = restoredCtx.$implicit; const ctx_r74 = i0.ɵɵnextContext(); return ctx_r74.toggleSelection(activity_r73, ctx_r74.selectedActivities, \"QUA_quiz_options\", false, \"Preferences\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementStart(2, \"span\", 110);\r\n    i0.ɵɵtext(3, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const activity_r73 = ctx.$implicit;\r\n    const ctx_r12 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r12.isItemSelected(activity_r73, ctx_r12.selectedActivities, \"QUA_quiz_options\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", activity_r73.option_title, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_41_Template(rf, ctx) { if (rf & 1) {\r\n    const _r78 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_41_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r78); const sector_r76 = restoredCtx.$implicit; const ctx_r77 = i0.ɵɵnextContext(); return ctx_r77.toggleSelection(sector_r76, ctx_r77.selectedSectors, \"LI_contentId\", false, \"Sectors\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementStart(2, \"span\", 110);\r\n    i0.ɵɵtext(3, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const sector_r76 = ctx.$implicit;\r\n    const ctx_r13 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r13.isItemSelected(sector_r76, ctx_r13.selectedSectors, \"LI_contentId\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", sector_r76.IN_name, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_47_Template(rf, ctx) { if (rf & 1) {\r\n    const _r81 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_47_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r81); const type_r79 = restoredCtx.$implicit; const ctx_r80 = i0.ɵɵnextContext(); return ctx_r80.toggleSelection(type_r79, ctx_r80.selectedWorkTypes, \"QUA_quiz_options\", false, \"WorkType\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵpipe(2, \"camelCase\");\r\n    i0.ɵɵelementStart(3, \"span\", 110);\r\n    i0.ɵɵtext(4, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const type_r79 = ctx.$implicit;\r\n    const ctx_r14 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r14.isItemSelected(type_r79, ctx_r14.selectedWorkTypes, \"QUA_quiz_options\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 3, type_r79.option_title), \" \");\r\n} }\r\nfunction EditAppUserComponent_div_48_Template(rf, ctx) { if (rf & 1) {\r\n    const _r83 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 106);\r\n    i0.ɵɵelementStart(1, \"button\", 107);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_48_Template_button_click_1_listener() { i0.ɵɵrestoreView(_r83); const ctx_r82 = i0.ɵɵnextContext(); return ctx_r82.updateUserPreferences(); });\r\n    i0.ɵɵtext(2, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(3, \"button\", 108);\r\n    i0.ɵɵtext(4, \"Back\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_63_Template(rf, ctx) { if (rf & 1) {\r\n    const _r86 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_63_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r86); const data_r84 = restoredCtx.$implicit; const ctx_r85 = i0.ɵɵnextContext(); return ctx_r85.toggleSelection(data_r84, ctx_r85.selectedGender, \"U_genderId\", true, \"Gender\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementStart(2, \"span\", 110);\r\n    i0.ɵɵtext(3, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const data_r84 = ctx.$implicit;\r\n    const ctx_r16 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r16.isItemSelectedForIdentity(data_r84, ctx_r16.selectedGender, true, \"Gender\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", data_r84.GE_title, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_71_Template(rf, ctx) { if (rf & 1) {\r\n    const _r89 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_71_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r89); const data_r87 = restoredCtx.$implicit; const ctx_r88 = i0.ɵɵnextContext(); return ctx_r88.toggleSelection(data_r87, ctx_r88.selectedEthnicity, \"U_ethinicityId\", true, \"Ethnicity\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementStart(2, \"span\", 110);\r\n    i0.ɵɵtext(3, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const data_r87 = ctx.$implicit;\r\n    const ctx_r17 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r17.isItemSelectedForIdentity(data_r87, ctx_r17.selectedEthnicity, true, \"Ethnicity\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", data_r87.ET_title, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_79_Template(rf, ctx) { if (rf & 1) {\r\n    const _r92 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_79_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r92); const data_r90 = restoredCtx.$implicit; const ctx_r91 = i0.ɵɵnextContext(); return ctx_r91.toggleSelection(data_r90, ctx_r91.selectedReligion, \"U_religionId\", true, \"Religion\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementStart(2, \"span\", 110);\r\n    i0.ɵɵtext(3, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const data_r90 = ctx.$implicit;\r\n    const ctx_r18 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r18.isItemSelectedForIdentity(data_r90, ctx_r18.selectedReligion, true, \"Religion\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", data_r90.RE_title, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_87_Template(rf, ctx) { if (rf & 1) {\r\n    const _r95 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_87_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r95); const data_r93 = restoredCtx.$implicit; const ctx_r94 = i0.ɵɵnextContext(); return ctx_r94.toggleSelection(data_r93, ctx_r94.selectedSexualOrientation, \"U_sexualOrientationId\", true, \"Sexual-Orientation\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementStart(2, \"span\", 110);\r\n    i0.ɵɵtext(3, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const data_r93 = ctx.$implicit;\r\n    const ctx_r19 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r19.isItemSelectedForIdentity(data_r93, ctx_r19.selectedSexualOrientation, true, \"Sexual-Orientation\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", data_r93.SO_title, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_95_Template(rf, ctx) { if (rf & 1) {\r\n    const _r98 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_95_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r98); const type_r96 = restoredCtx.$implicit; const ctx_r97 = i0.ɵɵnextContext(); return ctx_r97.toggleSelection(type_r96, ctx_r97.selectedDisability, \"U_isDisability\", true, \"Disability\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementStart(2, \"span\", 110);\r\n    i0.ɵɵtext(3, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const type_r96 = ctx.$implicit;\r\n    const ctx_r20 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r20.isItemSelectedForIdentity(type_r96, ctx_r20.selectedDisability, true, \"U_isDisability\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", type_r96.AM_title, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_103_Template(rf, ctx) { if (rf & 1) {\r\n    const _r101 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_103_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r101); const type_r99 = restoredCtx.$implicit; const ctx_r100 = i0.ɵɵnextContext(); return ctx_r100.toggleSelection(type_r99, ctx_r100.selectedGeneration, \"U_first_generation\", true, \"Generation\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementStart(2, \"span\", 110);\r\n    i0.ɵɵtext(3, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const type_r99 = ctx.$implicit;\r\n    const ctx_r21 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r21.isItemSelectedForIdentity(type_r99, ctx_r21.selectedGeneration, true, \"U_first_generation\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", type_r99.AM_title, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_110_Template(rf, ctx) { if (rf & 1) {\r\n    const _r104 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 109);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_110_Template_div_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r104); const type_r102 = restoredCtx.$implicit; const ctx_r103 = i0.ɵɵnextContext(); return ctx_r103.toggleSelection(type_r102, ctx_r103.selectedFreeMeal, \"U_freeMeal\", true, \"FreeSchoolMeals\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementStart(2, \"span\", 110);\r\n    i0.ɵɵtext(3, \"\\u2714\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const type_r102 = ctx.$implicit;\r\n    const ctx_r22 = i0.ɵɵnextContext();\r\n    i0.ɵɵclassProp(\"selected\", ctx_r22.isItemSelectedForIdentity(type_r102, ctx_r22.selectedFreeMeal, true, \"U_freeMeal\"));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", type_r102.AM_title, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_117_Template(rf, ctx) { if (rf & 1) {\r\n    const _r106 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 111);\r\n    i0.ɵɵelementStart(1, \"div\", 112);\r\n    i0.ɵɵtext(2);\r\n    i0.ɵɵelementStart(3, \"button\", 113);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_117_Template_button_click_3_listener() { i0.ɵɵrestoreView(_r106); const ctx_r105 = i0.ɵɵnextContext(); let tmp_b_0; return ctx_r105.removeMenu(\"U_institute\", (tmp_b_0 = ctx_r105.addNewAppUserForm.get(\"U_institute\")) == null ? null : tmp_b_0.value); });\r\n    i0.ɵɵtext(4, \" \\u00D7 \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r23 = i0.ɵɵnextContext();\r\n    let tmp_0_0;\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getMenuTitle((tmp_0_0 = ctx_r23.addNewAppUserForm.get(\"U_institute\")) == null ? null : tmp_0_0.value, \"U_institute\"), \" \");\r\n} }\r\nfunction EditAppUserComponent_div_119_li_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r110 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"li\", 116);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_119_li_2_Template_li_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r110); const menu_r108 = restoredCtx.$implicit; const ctx_r109 = i0.ɵɵnextContext(2); return ctx_r109.addMenu(menu_r108, \"U_institute\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const menu_r108 = ctx.$implicit;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", menu_r108.INS_title, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_119_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 114);\r\n    i0.ɵɵelementStart(1, \"ul\");\r\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_119_li_2_Template, 2, 1, \"li\", 115);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r24 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"ngForOf\", ctx_r24.filteredUniversityOptions);\r\n} }\r\nfunction EditAppUserComponent_div_120_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 117);\r\n    i0.ɵɵtext(1, \" No matching universities found. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_129_Template(rf, ctx) { if (rf & 1) {\r\n    const _r112 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 111);\r\n    i0.ɵɵelementStart(1, \"div\", 112);\r\n    i0.ɵɵtext(2);\r\n    i0.ɵɵelementStart(3, \"button\", 113);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_129_Template_button_click_3_listener() { i0.ɵɵrestoreView(_r112); const ctx_r111 = i0.ɵɵnextContext(); let tmp_b_0; return ctx_r111.removeMenu(\"U_education\", (tmp_b_0 = ctx_r111.addNewAppUserForm.get(\"U_education\")) == null ? null : tmp_b_0.value); });\r\n    i0.ɵɵtext(4, \" \\u00D7 \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r26 = i0.ɵɵnextContext();\r\n    let tmp_0_0;\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.getMenuTitle((tmp_0_0 = ctx_r26.addNewAppUserForm.get(\"U_education\")) == null ? null : tmp_0_0.value, \"U_education\"), \" \");\r\n} }\r\nfunction EditAppUserComponent_div_131_li_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r116 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"li\", 116);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_131_li_2_Template_li_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r116); const menu_r114 = restoredCtx.$implicit; const ctx_r115 = i0.ɵɵnextContext(2); return ctx_r115.addMenu(menu_r114, \"U_education\"); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const menu_r114 = ctx.$implicit;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", menu_r114.ED_name, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_131_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 114);\r\n    i0.ɵɵelementStart(1, \"ul\");\r\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_131_li_2_Template, 2, 1, \"li\", 115);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r27 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"ngForOf\", ctx_r27.filteredDegreeOptions);\r\n} }\r\nfunction EditAppUserComponent_div_132_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 117);\r\n    i0.ɵɵtext(1, \" No matching universities found. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_155_Template(rf, ctx) { if (rf & 1) {\r\n    const _r118 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 111);\r\n    i0.ɵɵelementStart(1, \"div\", 112);\r\n    i0.ɵɵtext(2);\r\n    i0.ɵɵelementStart(3, \"button\", 113);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_155_Template_button_click_3_listener() { i0.ɵɵrestoreView(_r118); const ctx_r117 = i0.ɵɵnextContext(); return ctx_r117.removeSelectedPostcode(); });\r\n    i0.ɵɵtext(4, \" \\u00D7 \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r29 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵtextInterpolate1(\" \", ctx_r29.selectedPostcode, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_157_li_2_Template(rf, ctx) { if (rf & 1) {\r\n    const _r122 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"li\", 116);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_157_li_2_Template_li_click_0_listener() { const restoredCtx = i0.ɵɵrestoreView(_r122); const postcode_r120 = restoredCtx.$implicit; const ctx_r121 = i0.ɵɵnextContext(2); return ctx_r121.selectPostcode(postcode_r120); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const postcode_r120 = ctx.$implicit;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", postcode_r120, \" \");\r\n} }\r\nfunction EditAppUserComponent_div_157_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 114);\r\n    i0.ɵɵelementStart(1, \"ul\");\r\n    i0.ɵɵtemplate(2, EditAppUserComponent_div_157_li_2_Template, 2, 1, \"li\", 115);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r30 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"ngForOf\", ctx_r30.filteredPostcodes);\r\n} }\r\nfunction EditAppUserComponent_div_158_Template(rf, ctx) { if (rf & 1) {\r\n    const _r124 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 106);\r\n    i0.ɵɵelementStart(1, \"button\", 118);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_158_Template_button_click_1_listener() { i0.ɵɵrestoreView(_r124); const ctx_r123 = i0.ɵɵnextContext(); return ctx_r123.createNewAppUser(); });\r\n    i0.ɵɵtext(2, \"Submit\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(3, \"button\", 108);\r\n    i0.ɵɵtext(4, \"Cancel\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction EditAppUserComponent_div_159_Template(rf, ctx) { if (rf & 1) {\r\n    const _r126 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 106);\r\n    i0.ɵɵelementStart(1, \"button\", 118);\r\n    i0.ɵɵlistener(\"click\", function EditAppUserComponent_div_159_Template_button_click_1_listener() { i0.ɵɵrestoreView(_r126); const ctx_r125 = i0.ɵɵnextContext(); return ctx_r125.updateUserIdentity(); });\r\n    i0.ɵɵtext(2, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(3, \"button\", 108);\r\n    i0.ɵɵtext(4, \"Back\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} }\r\nexport class EditAppUserComponent {\r\n    constructor(router, formBuilder, dataTransferService, toastr, activeRoute, httpClient, ngxSpinnerService, route) {\r\n        this.router = router;\r\n        this.formBuilder = formBuilder;\r\n        this.dataTransferService = dataTransferService;\r\n        this.toastr = toastr;\r\n        this.activeRoute = activeRoute;\r\n        this.httpClient = httpClient;\r\n        this.ngxSpinnerService = ngxSpinnerService;\r\n        this.route = route;\r\n        this.p = 1;\r\n        this.isReadonly = false;\r\n        this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\r\n        this.selectedActivities = [];\r\n        this.selectedSectors = [];\r\n        this.selectedWorkTypes = [];\r\n        this.selectedGender = [];\r\n        this.selectedEthnicity = [];\r\n        this.selectedReligion = [];\r\n        this.selectedDisability = [];\r\n        this.selectedGeneration = [];\r\n        this.selectedFreeMeal = [];\r\n        this.selectedSexualOrientation = [];\r\n        this.ActivitiesYouLike = [];\r\n        this.WorkType = [];\r\n        this.LikedSectors = [];\r\n        this.Genders = [];\r\n        this.Ethnicities = [];\r\n        this.Religions = [];\r\n        this.SexualOrientation = [];\r\n        this.Degree = [];\r\n        this.University = [];\r\n        this.filteredUniversityOptions = [];\r\n        this.filteredDegreeOptions = [];\r\n        this.filteredPostcodes = [];\r\n        this.showDropdown = false; // Controls visibility of the dropdown list\r\n        this.showPostcodeDropdown = false;\r\n        this.showPassword = false;\r\n        this.hasADisability = [\r\n            {\r\n                QUO_id: '1',\r\n                AM_title: 'Yes',\r\n            },\r\n            {\r\n                QUO_id: '2',\r\n                AM_title: 'No',\r\n            },\r\n            {\r\n                QUO_id: '3',\r\n                AM_title: 'Prefer Not To say',\r\n            },\r\n            {\r\n                QUO_id: '4',\r\n                AM_title: 'I Am Not Sure',\r\n            },\r\n        ];\r\n        this.FreeMeal = [\r\n            {\r\n                QUO_id: '1',\r\n                AM_title: 'Yes',\r\n            },\r\n            {\r\n                QUO_id: '2',\r\n                AM_title: 'No',\r\n            },\r\n            {\r\n                QUO_id: '3',\r\n                AM_title: 'Prefer Not To say',\r\n            },\r\n            // {\r\n            //   QUO_id: '4',\r\n            //   AM_title: 'I Am Not Sure',\r\n            // },\r\n        ];\r\n        this.isThisGeneration = [\r\n            {\r\n                QUO_id: '1',\r\n                AM_title: 'Yes',\r\n            },\r\n            {\r\n                QUO_id: '2',\r\n                AM_title: 'No',\r\n            },\r\n            {\r\n                QUO_id: '3',\r\n                AM_title: 'Prefer Not To say',\r\n            },\r\n            // {\r\n            //   QUO_id: '4',\r\n            //   AM_title: 'I Am Not Sure',\r\n            // },\r\n        ];\r\n        this.imgChangeEvt = \"\";\r\n        this.aliasImgChangeEvt = \"\";\r\n        this.isCropperVisible = false;\r\n        this.isAliasCropperVisible = false;\r\n        this.addNewAppUserForm = this.formBuilder.group({\r\n            U_name: ['', Validators.required],\r\n            U_email: ['', [Validators.required, FileValidator.strictEmailValidator]],\r\n            U_password: [\r\n                '',\r\n                [\r\n                    Validators.required,\r\n                    Validators.minLength(8),\r\n                    Validators.pattern(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])[A-Za-z\\d!@#$%^&*(),.?\":{}|<>]{8,}$/)\r\n                ],\r\n            ],\r\n            U_profilePercentage: ['0'],\r\n            U_ethinicityId: ['', Validators.required],\r\n            U_ethinicity_Toggle: [false],\r\n            U_genderId: ['', Validators.required],\r\n            U_gender_Toggle: [false],\r\n            U_isDisability: ['', Validators.required],\r\n            U_disability_Toggle: [false],\r\n            U_religionId: ['', Validators.required],\r\n            U_religion_Toggle: [false],\r\n            U_sexualOrientationId: ['', Validators.required],\r\n            U_sexuality_Toggle: [false],\r\n            U_institute: [''],\r\n            U_postcode: [''],\r\n            U_postcode_Toggle: [false],\r\n            U_first_generation: ['', Validators.required],\r\n            U_first_generation_Toggle: [false],\r\n            U_dp: [null],\r\n            U_freeMeal: ['', Validators.required],\r\n            U_freeMeal_Toggle: [false],\r\n            U_education: [''],\r\n            QUA_quiz_options: this.formBuilder.array([]),\r\n            LI_contentId: this.formBuilder.array([], Validators.required),\r\n            U_isExpert: [false],\r\n            U_profileAnonymous: [false],\r\n            U_aliasName: [''],\r\n            U_aliasDp: ['']\r\n        });\r\n        this.route.queryParams.subscribe(params => {\r\n            if (params) {\r\n                this.U_id = params['U_id'];\r\n                this.title = params['title'];\r\n                console.log(this.U_id);\r\n            }\r\n            else {\r\n                this.router.navigate(['/actions/app-users']);\r\n            }\r\n        });\r\n    }\r\n    ngOnInit() {\r\n        if (this.title === 'Edit') {\r\n            this.getUserDetailsById(this.U_id).then(res => {\r\n                this.patchFormData(res);\r\n            }).catch(error => {\r\n                console.error(\"Error in fetching user details:\", error);\r\n            });\r\n        }\r\n        this.getAllPreferencesData();\r\n        if (this.title !== 'Edit') {\r\n            this.getIdentityData();\r\n            this.getindustry();\r\n            this.getAllUniversity();\r\n        }\r\n        this.universitySearchControl = new FormControl('');\r\n        this.degreeSearchControl = new FormControl('');\r\n        this.postcodeSearchControl = new FormControl('');\r\n        this.getAllDegree();\r\n        this.postcodeSearchControl.valueChanges\r\n            .pipe(debounceTime(300), distinctUntilChanged(), switchMap((value) => this.fetchPostcodes(value)))\r\n            .subscribe((postcodes) => {\r\n            this.filteredPostcodes = postcodes;\r\n            this.showPostcodeDropdown = postcodes.length > 0;\r\n        });\r\n        if (!this.addNewAppUserForm.get('U_postcode')) {\r\n            this.addNewAppUserForm.addControl('U_postcode', new FormControl('', Validators.required));\r\n        }\r\n    }\r\n    getUserDetailsById(U_id) {\r\n        return new Promise((resolve, reject) => {\r\n            this.dataTransferService.getUserDetailsById(U_id).subscribe((res) => {\r\n                if (res.data) {\r\n                    this.userData = res.data;\r\n                    console.log(\"this.userData\", this.userData);\r\n                    resolve(this.userData); // Resolve with the userData\r\n                }\r\n                else {\r\n                    reject('No data found'); // Reject if no data found\r\n                }\r\n            }, (error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                this.toastr.error(\"Unable to fetch data.\");\r\n                console.log(\"Error\", error);\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n    async patchFormData(data) {\r\n        try {\r\n            await this.getindustry();\r\n            await this.getIdentityData();\r\n            await this.getAllUniversity();\r\n            this.addNewAppUserForm.patchValue({\r\n                U_name: data.U_name,\r\n                U_email: data.U_email,\r\n                U_password: data.U_password,\r\n                U_profilePercentage: data.U_profilePercentage,\r\n                U_ethinicity_Toggle: data.U_ethinicity_Toggle,\r\n                U_gender_Toggle: data.U_gender_Toggle,\r\n                U_disability_Toggle: data.U_disability_Toggle,\r\n                U_religion_Toggle: data.U_religion_Toggle,\r\n                U_sexuality_Toggle: data.U_sexuality_Toggle,\r\n                U_institute: data.U_institute,\r\n                U_postcode: data === null || data === void 0 ? void 0 : data.U_postcode,\r\n                U_postcode_Toggle: data.U_postcode_Toggle ? data.U_postcode_Toggle : false,\r\n                // U_dp: data.U_dp,\r\n                U_education: data.U_education,\r\n                U_genderId: data.U_genderId,\r\n                U_ethinicityId: data.U_ethinicityId,\r\n                U_religionId: data.U_religionId,\r\n                U_sexualOrientationId: data.U_sexualOrientationId,\r\n                U_isDisability: data.U_isDisability,\r\n                U_first_generation: data.U_first_generation,\r\n                U_first_generation_Toggle: data.U_first_generation_Toggle,\r\n                U_freeMeal: data.U_freeMeal,\r\n                U_freeMeal_Toggle: data.U_freeMeal_Toggle ? data.U_freeMeal_Toggle : false,\r\n                U_profileAnonymous: data === null || data === void 0 ? void 0 : data.U_profileAnonymous,\r\n                U_aliasName: data === null || data === void 0 ? void 0 : data.U_aliasName\r\n            });\r\n            if (data.U_dp) {\r\n                this.imageSrc = data.U_dp;\r\n            }\r\n            if (data.U_aliasDp) {\r\n                this.aliasImageSrc = data.U_aliasDp;\r\n            }\r\n            if (data.U_institute) {\r\n                this.addMenu(data.U_institute, 'U_institute', 'toPatchExisting');\r\n            }\r\n            if (data.U_education) {\r\n                this.addMenu(data.U_education, 'U_education', 'toPatchExisting');\r\n            }\r\n            if (data.U_postcode) {\r\n                this.selectPostcode(data.U_postcode);\r\n            }\r\n            this.selectedSectors = this.LikedSectors.filter((sector) => data === null || data === void 0 ? void 0 : data.LI_contentId.includes(sector.IN_id));\r\n            // Update the form with the selected sectors\r\n            this.updateFormArray('LI_contentId', this.selectedSectors, false);\r\n            // Patch the QUA_quiz_options for Activities and Work Types\r\n            this.selectedActivities = data === null || data === void 0 ? void 0 : data.QUA_quiz_options.filter((option) => (option === null || option === void 0 ? void 0 : option.quiz_id) === '5');\r\n            this.selectedWorkTypes = data === null || data === void 0 ? void 0 : data.QUA_quiz_options.filter((option) => (option === null || option === void 0 ? void 0 : option.quiz_id) === '2');\r\n            // Call method to update the FormArray in the form\r\n            this.updateFormArray('QUA_quiz_options', this.selectedActivities.concat(this.selectedWorkTypes), false);\r\n            // Update Identity Data\r\n            const selectedGender = this.Genders.find((gender) => (gender === null || gender === void 0 ? void 0 : gender.GE_id) === data.U_genderId);\r\n            this.selectedGender.push(selectedGender);\r\n            const selectedEthnicity = this.Ethnicities.find((ethnicity) => (ethnicity === null || ethnicity === void 0 ? void 0 : ethnicity.ET_id) === (data === null || data === void 0 ? void 0 : data.U_ethinicityId));\r\n            this.selectedEthnicity.push(selectedEthnicity);\r\n            const selectedReligion = this.Religions.find((religion) => (religion === null || religion === void 0 ? void 0 : religion.RE_id) === (data === null || data === void 0 ? void 0 : data.U_religionId));\r\n            this.selectedReligion.push(selectedReligion);\r\n            const selectedSexualOrientation = this.SexualOrientation.find((orientation) => (orientation === null || orientation === void 0 ? void 0 : orientation.SO_id) === (data === null || data === void 0 ? void 0 : data.U_sexualOrientationId));\r\n            this.selectedSexualOrientation.push(selectedSexualOrientation);\r\n            const selectedDisability = this.hasADisability.find((disability) => (disability === null || disability === void 0 ? void 0 : disability.QUO_id) === (data === null || data === void 0 ? void 0 : data.U_isDisability));\r\n            this.selectedDisability.push(selectedDisability);\r\n            const selectedGeneration = this.isThisGeneration.find((generation) => (generation === null || generation === void 0 ? void 0 : generation.QUO_id) === (data === null || data === void 0 ? void 0 : data.U_first_generation));\r\n            this.selectedGeneration.push(selectedGeneration);\r\n            const selectedFreeMeal = this.FreeMeal.find((meal) => (meal === null || meal === void 0 ? void 0 : meal.QUO_id) === (data === null || data === void 0 ? void 0 : data.U_freeMeal));\r\n            this.selectedFreeMeal.push(selectedFreeMeal);\r\n        }\r\n        catch (error) {\r\n            console.error('Error loading data:', error);\r\n        }\r\n        this.calculateProfilePercentage();\r\n        console.log(\"Patched data\", this.addNewAppUserForm.value);\r\n    }\r\n    markAllFieldsAsTouched() {\r\n        Object.keys(this.addNewAppUserForm.controls).forEach((field) => {\r\n            const control = this.addNewAppUserForm.get(field);\r\n            control === null || control === void 0 ? void 0 : control.markAsTouched({ onlySelf: true });\r\n        });\r\n    }\r\n    calculateProfilePercentage() {\r\n        var _a, _b, _c, _d;\r\n        let percentage = 0;\r\n        const fields = [\r\n            this.selectedGender,\r\n            this.selectedEthnicity,\r\n            this.selectedReligion,\r\n            this.selectedSexualOrientation,\r\n            this.selectedDisability,\r\n            this.selectedGeneration,\r\n            this.selectedFreeMeal,\r\n            this.selectedPostcode ? [this.selectedPostcode] : [],\r\n            ((_a = this.addNewAppUserForm.get('U_institute')) === null || _a === void 0 ? void 0 : _a.value)\r\n                ? [(_b = this.addNewAppUserForm.get('U_institute')) === null || _b === void 0 ? void 0 : _b.value]\r\n                : [],\r\n            ((_c = this.addNewAppUserForm.get('U_education')) === null || _c === void 0 ? void 0 : _c.value)\r\n                ? [(_d = this.addNewAppUserForm.get('U_education')) === null || _d === void 0 ? void 0 : _d.value]\r\n                : [],\r\n        ];\r\n        fields.forEach((field) => {\r\n            if (field.length > 0) {\r\n                percentage += 10;\r\n            }\r\n        });\r\n        // Convert percentage to string and patch value\r\n        this.addNewAppUserForm.patchValue({\r\n            U_profilePercentage: percentage.toString(),\r\n        });\r\n        console.log('this.addNewAppUserForm.value.U_profilePercentage:', this.addNewAppUserForm.value.U_profilePercentage);\r\n    }\r\n    togglePasswordVisibility(icon, controlName) {\r\n        const input = document.getElementById('U_password');\r\n        // const input = this.el.nativeElement.querySelector(`#${controlName}`) as HTMLInputElement;\r\n        const isPasswordType = input.type === 'password';\r\n        input.type = isPasswordType ? 'text' : 'password';\r\n        icon.classList.toggle('fa-eye-slash', isPasswordType);\r\n        icon.classList.toggle('fa-eye', !isPasswordType);\r\n    }\r\n    onFileSelected(event, field) {\r\n        var _a, _b;\r\n        const selectedFile = event.target.files[0];\r\n        console.log(\"selectedFile\", selectedFile);\r\n        // Function to add a timestamp to the file name\r\n        // const addTimestamp = (fileName: string) => {\r\n        //   const currentTimestamp = new Date().getTime();\r\n        //   const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');\r\n        //   const extension = fileName.split('.').pop();\r\n        //   return `${nameWithoutExtension}_${currentTimestamp}.${extension}`;\r\n        // };\r\n        if (!selectedFile) {\r\n            if (field === 'U_dp') {\r\n                this.imageName = null;\r\n                this.imageSrc = null;\r\n                const fileControl = this.addNewAppUserForm.get('U_dp');\r\n                fileControl === null || fileControl === void 0 ? void 0 : fileControl.clearValidators();\r\n                fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\r\n            }\r\n            else if (field === 'U_aliasDp') {\r\n                this.aliasImageName = null;\r\n                this.aliasImageSrc = null;\r\n                const aliasFileControl = this.addNewAppUserForm.get('U_aliasDp');\r\n                aliasFileControl === null || aliasFileControl === void 0 ? void 0 : aliasFileControl.clearValidators();\r\n                aliasFileControl === null || aliasFileControl === void 0 ? void 0 : aliasFileControl.updateValueAndValidity();\r\n            }\r\n            return;\r\n        }\r\n        if (field === 'U_dp' && selectedFile) {\r\n            // Append timestamp to image name\r\n            const newFileName = FileValidator.addTimestamp(selectedFile.name);\r\n            this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });\r\n            console.log(\"imageName\", this.imageName);\r\n            const fileControl = this.addNewAppUserForm.get('U_dp');\r\n            fileControl === null || fileControl === void 0 ? void 0 : fileControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\r\n            fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\r\n            const fileType = this.imageName.type.split('/')[0];\r\n            const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\r\n            if (fileType !== 'image' || fileExtension === 'svg') {\r\n                event.target.value = '';\r\n                this.toastr.info('Please select an image file (excluding SVG).');\r\n                this.imageName = null;\r\n                this.imageSrc = null;\r\n                return;\r\n            }\r\n            if (this.imageName && fileType === 'image') {\r\n                const reader = new FileReader();\r\n                const img = new Image();\r\n                reader.onload = (e) => {\r\n                    var _a, _b, _c;\r\n                    this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\r\n                    if (!((_c = (_b = this.addNewAppUserForm.get('U_dp')) === null || _b === void 0 ? void 0 : _b.errors) === null || _c === void 0 ? void 0 : _c.fileSizeValidator)) {\r\n                        this.checkAspectRatio(img, 'U_dp');\r\n                        this.imgChangeEvt = { target: { files: [this.imageName] } };\r\n                    }\r\n                };\r\n                reader.readAsDataURL(this.imageName);\r\n            }\r\n        }\r\n        if (field === 'U_aliasDp' && selectedFile) {\r\n            // Append timestamp to alias image name\r\n            const newFileName = FileValidator.addTimestamp(selectedFile.name);\r\n            this.aliasImageName = new File([selectedFile], newFileName, { type: selectedFile.type });\r\n            const aliasFileControl = this.addNewAppUserForm.get('U_aliasDp');\r\n            aliasFileControl === null || aliasFileControl === void 0 ? void 0 : aliasFileControl.setValidators(FileValidator.fileSizeValidator(2048, this.aliasImageName));\r\n            aliasFileControl === null || aliasFileControl === void 0 ? void 0 : aliasFileControl.updateValueAndValidity();\r\n            const aliasFileType = this.aliasImageName.type.split('/')[0];\r\n            const aliasFileExtension = (_b = this.aliasImageName.name.split('.').pop()) === null || _b === void 0 ? void 0 : _b.toLowerCase();\r\n            if (aliasFileType !== 'image' || aliasFileExtension === 'svg') {\r\n                event.target.value = '';\r\n                this.toastr.info('Please select an image file (excluding SVG).');\r\n                this.aliasImageName = null;\r\n                this.aliasImageSrc = null;\r\n                return;\r\n            }\r\n            if (this.aliasImageName && aliasFileType === 'image') {\r\n                const reader = new FileReader();\r\n                const img = new Image();\r\n                reader.onload = (e) => {\r\n                    var _a, _b, _c;\r\n                    this.aliasImageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\r\n                    if (!((_c = (_b = this.addNewAppUserForm.get('U_aliasDp')) === null || _b === void 0 ? void 0 : _b.errors) === null || _c === void 0 ? void 0 : _c.fileSizeValidator)) {\r\n                        this.checkAspectRatio(img, 'U_aliasDp');\r\n                        this.aliasImgChangeEvt = { target: { files: [this.aliasImageName] } };\r\n                    }\r\n                };\r\n                reader.readAsDataURL(this.aliasImageName);\r\n            }\r\n        }\r\n    }\r\n    checkAspectRatio(image, controlName) {\r\n        const aspectRatio = image.width / image.height;\r\n        const control = this.addNewAppUserForm.get(controlName);\r\n        if (aspectRatio !== 1) {\r\n            // this.toastr.warning('The image must have a 1:1 aspect ratio. Please select a square image.', 'Invalid Aspect Ratio');\r\n            control === null || control === void 0 ? void 0 : control.setErrors({ fileAspectRatioValidator: true });\r\n        }\r\n        else {\r\n            control === null || control === void 0 ? void 0 : control.setErrors(null);\r\n        }\r\n    }\r\n    dataURItoBlob(dataURI) {\r\n        const byteString = atob(dataURI.split(',')[1]);\r\n        const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];\r\n        const arrayBuffer = new ArrayBuffer(byteString.length);\r\n        const uint8Array = new Uint8Array(arrayBuffer);\r\n        for (let i = 0; i < byteString.length; i++) {\r\n            uint8Array[i] = byteString.charCodeAt(i);\r\n        }\r\n        return new Blob([uint8Array], { type: mimeString });\r\n    }\r\n    cropImg(e, controlName) {\r\n        if (controlName == 'U_dp') {\r\n            this.imageSrc = e.base64;\r\n        }\r\n        else if (controlName == 'U_aliasDp') {\r\n            this.aliasImageSrc = e.base64;\r\n        }\r\n    }\r\n    showCropper(controlName) {\r\n        if (controlName == 'U_dp') {\r\n            this.isCropperVisible = true;\r\n        }\r\n        else if (controlName == 'U_aliasDp') {\r\n            this.isAliasCropperVisible = true;\r\n        }\r\n    }\r\n    hideCropper(controlName) {\r\n        if (controlName == 'U_dp') {\r\n            this.isCropperVisible = false;\r\n        }\r\n        else if (controlName == 'U_aliasDp') {\r\n            this.isAliasCropperVisible = false;\r\n        }\r\n    }\r\n    saveCroppedImage(controlName) {\r\n        const addTimestamp = (fileName) => {\r\n            const currentTimestamp = new Date().getTime();\r\n            // Split the filename into name and extension\r\n            const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');\r\n            const extension = fileName.split('.').pop();\r\n            // If the name already has a timestamp, remove it (assuming it's the last segment separated by '_')\r\n            const cleanedName = nameWithoutExtension.replace(/_\\d{13}$/, '');\r\n            // Append the new timestamp to the name\r\n            return `${cleanedName}_${currentTimestamp}.${extension}`;\r\n        };\r\n        if (controlName == 'U_dp') {\r\n            if (this.imageSrc && this.imageName) {\r\n                // Convert cropped image to Blob\r\n                const blob = this.dataURItoBlob(this.imageSrc);\r\n                // Add or replace timestamp in image name\r\n                const newFileName = addTimestamp(this.imageName.name);\r\n                this.imageName = new File([blob], newFileName, { type: this.imageName.type });\r\n                console.log(\"Cropped Image\", this.imageName);\r\n                // Update imgChangeEvt with the new cropped image\r\n                this.imgChangeEvt = { target: { files: [this.imageName] } };\r\n                const fileControl = this.addNewAppUserForm.get('U_dp');\r\n                fileControl === null || fileControl === void 0 ? void 0 : fileControl.clearValidators();\r\n                fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\r\n                this.hideCropper('U_dp');\r\n            }\r\n        }\r\n        else if (controlName == 'U_aliasDp') {\r\n            if (this.aliasImageSrc && this.aliasImageName) {\r\n                // Convert cropped image to Blob\r\n                const blob = this.dataURItoBlob(this.aliasImageSrc);\r\n                // Add or replace timestamp in alias image name\r\n                const newFileName = addTimestamp(this.aliasImageName.name);\r\n                this.aliasImageName = new File([blob], newFileName, { type: this.aliasImageName.type });\r\n                console.log(\"Cropped Alias Image\", this.aliasImageName);\r\n                // Update aliasImgChangeEvt with the new cropped image\r\n                this.aliasImgChangeEvt = { target: { files: [this.aliasImageName] } };\r\n                const fileControl = this.addNewAppUserForm.get('U_aliasDp');\r\n                fileControl === null || fileControl === void 0 ? void 0 : fileControl.clearValidators();\r\n                fileControl === null || fileControl === void 0 ? void 0 : fileControl.updateValueAndValidity();\r\n                this.hideCropper('U_aliasDp');\r\n            }\r\n        }\r\n    }\r\n    initCropper() {\r\n    }\r\n    imgLoad() {\r\n    }\r\n    imgFailed() {\r\n        this.toastr.error(\"Image Failed to show\");\r\n    }\r\n    anonymousCheck() {\r\n        var _a, _b, _c, _d, _e;\r\n        if (!((_a = this.addNewAppUserForm.get('U_profileAnonymous')) === null || _a === void 0 ? void 0 : _a.value)) {\r\n            this.aliasImageName = null;\r\n            this.aliasImageSrc = null;\r\n            (_b = this.addNewAppUserForm.get('U_aliasName')) === null || _b === void 0 ? void 0 : _b.clearValidators();\r\n            (_c = this.addNewAppUserForm.get('U_aliasName')) === null || _c === void 0 ? void 0 : _c.updateValueAndValidity();\r\n            (_d = this.addNewAppUserForm.get('U_aliasDp')) === null || _d === void 0 ? void 0 : _d.clearValidators();\r\n            (_e = this.addNewAppUserForm.get('U_aliasDp')) === null || _e === void 0 ? void 0 : _e.updateValueAndValidity();\r\n        }\r\n    }\r\n    // uploadLogoUrl() {\r\n    //   //To upload logo from add new company form\r\n    //   if (!this.imageName) {\r\n    //     // this.toastr.error('Please select an image.');\r\n    //     return;\r\n    //   }\r\n    //   console.log('image', this.imageName);\r\n    //   this.dataTransferService\r\n    //     .uploadurl(this.imageName)\r\n    //     .subscribe((res: any) => {});\r\n    // }\r\n    // removeMenu(index: number, formControlName: string) {\r\n    //   const formArray = this.addNewAppUserForm.get(formControlName) as FormArray;\r\n    //   formArray.removeAt(index);\r\n    // }\r\n    // onBackspaceKey(event: KeyboardEvent, formControlName: string) {\r\n    //   if (event.key === 'Backspace') {\r\n    //     const inputElement = event.target as HTMLInputElement;\r\n    //     if (inputElement.value === '' && !event.shiftKey) {\r\n    //       event.preventDefault();\r\n    //       this.removeLastMenu(formControlName);\r\n    //     }\r\n    //   }\r\n    // }\r\n    // removeLastMenu(formArrayName: string) {\r\n    //   const formArray = this.addNewAppUserForm.get(formArrayName) as FormArray;\r\n    //   if (formArray.length > 0) {\r\n    //     formArray.removeAt(formArray.length - 1);\r\n    //   }\r\n    // }\r\n    // addMenu(event: any, formArrayName: string) {\r\n    //   const menuId = event.target.value;\r\n    //   console.log(\"Selected menuId:\", menuId); // Debugging line\r\n    //   if (menuId.trim() !== '') {\r\n    //     const selectedMenu = this.University.find((menu: any) => menu.INS_id === menuId);\r\n    //     console.log(\"Selected Menu:\", selectedMenu); // Debugging line\r\n    //     if (selectedMenu) {\r\n    //       const formArray = this.addNewAppUserForm.get(formArrayName) as FormArray;\r\n    //       const alreadyAdded = formArray.value.some((item: any) => item.INS_id === selectedMenu.INS_id);\r\n    //       if (!alreadyAdded) {\r\n    //         formArray.push(new FormControl({ INS_id: selectedMenu.INS_id, INS_title: selectedMenu.INS_title }));\r\n    //       }\r\n    //       event.target.value = '';\r\n    //     }\r\n    //   }\r\n    // }\r\n    // getFormArrayControls(formArrayName: string): FormControl[] {\r\n    //   return (this.addNewAppUserForm.get(formArrayName) as FormArray).controls as FormControl[];\r\n    // }\r\n    // getMenuTitle(id: any): string {\r\n    //   const menu = this.University.find((menu: any) => menu.INS_id === id);\r\n    //   return menu ? menu.INS_title : 'Unknown';\r\n    // }\r\n    isItemSingleSelected(item, selectedArray, singleSelect = false) {\r\n        if (singleSelect) {\r\n            return selectedArray.length > 0 && selectedArray[0].AM_id === item.AM_id;\r\n        }\r\n        return selectedArray.some((selected) => selected.AM_id === item.AM_id);\r\n    }\r\n    isItemSelected(item, selectedArray, formControlName) {\r\n        if (!selectedArray) {\r\n            return false;\r\n        }\r\n        if (formControlName === 'LI_contentId') {\r\n            return selectedArray.some((selected) => selected.IN_id === item.IN_id);\r\n        }\r\n        return selectedArray.some((selected) => selected.option_id === item.option_id);\r\n    }\r\n    isItemSelectedForIdentity(item, selectedArray, singleSelect = false, type) {\r\n        var _a, _b, _c, _d, _e, _f, _g;\r\n        if (!selectedArray) {\r\n            console.log('item', item);\r\n            return false;\r\n        }\r\n        if (singleSelect) {\r\n            switch (type) {\r\n                case 'Gender':\r\n                    return (selectedArray.length > 0 && ((_a = selectedArray[0]) === null || _a === void 0 ? void 0 : _a.GE_id) === (item === null || item === void 0 ? void 0 : item.GE_id));\r\n                    break;\r\n                case 'Ethnicity':\r\n                    return (selectedArray.length > 0 && ((_b = selectedArray[0]) === null || _b === void 0 ? void 0 : _b.ET_id) === (item === null || item === void 0 ? void 0 : item.ET_id));\r\n                    break;\r\n                case 'Religion':\r\n                    return (selectedArray.length > 0 && ((_c = selectedArray[0]) === null || _c === void 0 ? void 0 : _c.RE_id) === (item === null || item === void 0 ? void 0 : item.RE_id));\r\n                    break;\r\n                case 'Sexual-Orientation':\r\n                    return (selectedArray.length > 0 && ((_d = selectedArray[0]) === null || _d === void 0 ? void 0 : _d.SO_id) === (item === null || item === void 0 ? void 0 : item.SO_id));\r\n                    break;\r\n                case 'U_isDisability':\r\n                    return (selectedArray.length > 0 &&\r\n                        ((_e = selectedArray[0]) === null || _e === void 0 ? void 0 : _e.QUO_id) === (item === null || item === void 0 ? void 0 : item.QUO_id));\r\n                    break;\r\n                case 'U_first_generation':\r\n                    return (selectedArray.length > 0 &&\r\n                        ((_f = selectedArray[0]) === null || _f === void 0 ? void 0 : _f.QUO_id) === (item === null || item === void 0 ? void 0 : item.QUO_id));\r\n                    break;\r\n                case 'U_freeMeal':\r\n                    return (selectedArray.length > 0 &&\r\n                        ((_g = selectedArray[0]) === null || _g === void 0 ? void 0 : _g.QUO_id) === (item === null || item === void 0 ? void 0 : item.QUO_id));\r\n                    break;\r\n            }\r\n        }\r\n        return selectedArray.some((selected) => (selected === null || selected === void 0 ? void 0 : selected.QUO_id) === (item === null || item === void 0 ? void 0 : item.QUO_id));\r\n    }\r\n    toggleSelection(item, selectedArray, formControlName, singleSelect = false, type) {\r\n        const identifier = formControlName === 'LI_contentId' ? 'IN_id' : 'option_id';\r\n        if (singleSelect) {\r\n            if (this.isItemSelectedForIdentity(item, selectedArray, true, type)) {\r\n                selectedArray.splice(0, 1);\r\n            }\r\n            else {\r\n                selectedArray.splice(0, selectedArray.length);\r\n                selectedArray.push(item);\r\n            }\r\n            this.calculateProfilePercentage();\r\n        }\r\n        else {\r\n            const index = selectedArray.findIndex((selected) => (selected === null || selected === void 0 ? void 0 : selected[identifier]) === item[identifier]);\r\n            if (index !== -1) {\r\n                selectedArray.splice(index, 1);\r\n            }\r\n            else {\r\n                selectedArray.push(item);\r\n            }\r\n        }\r\n        this.updateFormArray(formControlName, selectedArray, singleSelect);\r\n    }\r\n    updateFormArray(controlName, selectedArray, singleSelect) {\r\n        const selectedItem = selectedArray.length > 0 ? selectedArray[0] : null;\r\n        if (singleSelect) {\r\n            const singleSelectFields = {\r\n                U_genderId: 'GE_id',\r\n                U_ethinicityId: 'ET_id',\r\n                U_religionId: 'RE_id',\r\n                U_sexualOrientationId: 'SO_id',\r\n                U_isDisability: 'QUO_id',\r\n                U_first_generation: 'QUO_id',\r\n                U_freeMeal: 'QUO_id',\r\n            };\r\n            if (controlName in singleSelectFields) {\r\n                this.addNewAppUserForm.setControl(controlName, new FormControl(selectedItem ? selectedItem[singleSelectFields[controlName]] : null));\r\n            }\r\n            else {\r\n                console.warn(`Unexpected control name: ${controlName}`);\r\n            }\r\n        }\r\n        else {\r\n            if (controlName === 'LI_contentId') {\r\n                const formArray = this.addNewAppUserForm.get(controlName);\r\n                formArray.clear(); // Clear existing controls\r\n                selectedArray.forEach((item) => {\r\n                    formArray.push(new FormControl(item.IN_id)); // Add new controls\r\n                });\r\n            }\r\n            else {\r\n                const mergedArray = [\r\n                    ...this.selectedActivities,\r\n                    ...this.selectedWorkTypes,\r\n                ];\r\n                const formArray = this.formBuilder.array(mergedArray.map((item) => new FormControl({\r\n                    option_id: item.option_id,\r\n                    option_title: item.option_title,\r\n                    quiz_id: item.quiz_id,\r\n                })));\r\n                this.addNewAppUserForm.setControl(controlName, formArray);\r\n            }\r\n        }\r\n    }\r\n    getAllPreferencesData() {\r\n        this.dataTransferService.getAllPreferencesData().subscribe((res) => {\r\n            if (res.statusCode == 200 && res.data) {\r\n                res.data.forEach((quiz) => {\r\n                    if (quiz.quiz_options && quiz.quiz_options.length > 0) {\r\n                        this.populateReferencesData(quiz.quiz_options);\r\n                    }\r\n                    else {\r\n                        this.ngxSpinnerService.hide('globalSpinner');\r\n                        console.error('No Preferences Data available:', quiz.QU_id);\r\n                    }\r\n                });\r\n            }\r\n            else {\r\n                console.error('Failed to retrieve Preference sData or data is empty. Status code:', res.statusCode);\r\n            }\r\n        }, (error) => {\r\n            console.error('Error while fetching quiz data:', error);\r\n        });\r\n    }\r\n    populateReferencesData(options) {\r\n        options.forEach((item) => {\r\n            const formattedItem = {\r\n                option_id: item.QUO_id,\r\n                option_title: item.QUO_title,\r\n                quiz_id: item.QUO_quizid,\r\n            };\r\n            switch (item.QUO_quizid) {\r\n                case '5':\r\n                    this.ActivitiesYouLike.push(formattedItem);\r\n                    break;\r\n                // case '4':\r\n                //   this.LikedSectors.push(formattedItem);\r\n                //   break;\r\n                case '2':\r\n                    this.WorkType.push(formattedItem);\r\n                    break;\r\n            }\r\n        });\r\n    }\r\n    getIdentityData() {\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        return new Promise((resolve, reject) => {\r\n            this.dataTransferService.getIdentityData().subscribe((res) => {\r\n                if (res.statusCode === 200) {\r\n                    // this.ngxSpinnerService.hide('globalSpinner');\r\n                    this.Ethnicities = res.ethnicities;\r\n                    this.Genders = res.genders;\r\n                    this.Religions = res.religion;\r\n                    this.SexualOrientation = res.sexual_orientation;\r\n                    resolve();\r\n                }\r\n                else {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    console.error('Failed to retrieve identity data. Status code:', res.statusCode);\r\n                    reject('Failed to retrieve identity data');\r\n                }\r\n            }, (error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                console.error('Error while fetching identity data:', error);\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n    getAllUniversity() {\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        this.dataTransferService.getAllUniversity().subscribe((res) => {\r\n            if (res.statusCode == 200) {\r\n                this.University = res.data.sort((a, b) => a.INS_title.localeCompare(b.INS_title));\r\n                console.log('this.University', this.University);\r\n                this.filteredUniversityOptions = this.University;\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n            }\r\n            else {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                console.error('Failed to retrieve University Data or data is empty. Status code:', res.statusCode);\r\n            }\r\n        }, (error) => {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            console.error('Error while fetching University data:', error);\r\n        });\r\n    }\r\n    getindustry() {\r\n        return new Promise((resolve, reject) => {\r\n            this.dataTransferService.getIndustryData().subscribe((res) => {\r\n                if (res.status === 200) {\r\n                    this.LikedSectors = res.data.sort((a, b) => a.IN_name.localeCompare(b.IN_name));\r\n                    console.log('this.LikedSectors', this.LikedSectors);\r\n                    resolve(this.LikedSectors);\r\n                }\r\n                else {\r\n                    console.error('Failed to fetch industry data:', res);\r\n                    reject('Failed to fetch industry data');\r\n                }\r\n            }, (error) => {\r\n                console.error('Error fetching industry data:', error);\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n    getAllDegree() {\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        this.dataTransferService.getAllDegree().subscribe((res) => {\r\n            if (res.statusCode == 200) {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                this.Degree = res.data.sort((a, b) => a.ED_name.localeCompare(b.ED_name));\r\n                console.log('this.Degree', this.Degree);\r\n                this.filteredDegreeOptions = this.Degree;\r\n            }\r\n            else {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                console.error('Failed to retrieve Degree Data or data is empty. Status code:', res.statusCode);\r\n            }\r\n        }, (error) => {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            console.error('Error while fetching University data:', error);\r\n        });\r\n    }\r\n    filterOptions(formControlName) {\r\n        if (formControlName === 'U_institute') {\r\n            const searchTerm = this.universitySearchControl.value\r\n                .toLowerCase()\r\n                .trim();\r\n            this.filteredUniversityOptions = this.University.filter((option) => option.INS_title.toLowerCase().includes(searchTerm));\r\n        }\r\n        else if (formControlName === 'U_education') {\r\n            const searchTerm = this.degreeSearchControl.value.toLowerCase().trim();\r\n            this.filteredDegreeOptions = this.Degree.filter((option) => option.ED_name.toLowerCase().includes(searchTerm));\r\n        }\r\n    }\r\n    removeMenu(formControlName, id) {\r\n        const formControl = this.addNewAppUserForm.get(formControlName);\r\n        if (formControl) {\r\n            formControl.setValue(null); // Clear the form control's value\r\n            if (formControlName === 'U_institute') {\r\n                this.selectedUniversity = null;\r\n                this.showTag = false;\r\n            }\r\n            else if (formControlName === 'U_education') {\r\n                this.selectedDegree = null;\r\n                this.showTagOfEducation = false;\r\n            }\r\n        }\r\n        else {\r\n            console.error('Form control not found:', formControlName);\r\n        }\r\n    }\r\n    addMenu(menu, formArrayName, title) {\r\n        const formControl = this.addNewAppUserForm.get(formArrayName);\r\n        if (formControl) {\r\n            if (formArrayName === 'U_institute') {\r\n                if (title === 'toPatchExisting') {\r\n                    formControl.setValue(menu);\r\n                }\r\n                else {\r\n                    formControl.setValue(menu.INS_id);\r\n                }\r\n                this.selectedUniversity = menu;\r\n                this.universitySearchControl.setValue('');\r\n                this.filteredUniversityOptions = this.University;\r\n                this.showTag = true;\r\n                this.showDropdown = false; // Hide dropdown after selection // Assuming menu.INS_id is the ID you want to store\r\n            }\r\n            else if (formArrayName === 'U_education') {\r\n                if (title === 'toPatchExisting') {\r\n                    formControl.setValue(menu);\r\n                }\r\n                else {\r\n                    formControl.setValue(menu.ED_id);\r\n                }\r\n                this.selectedDegree = menu;\r\n                this.degreeSearchControl.setValue('');\r\n                this.filteredDegreeOptions = this.Degree;\r\n                this.showTagOfEducation = true;\r\n                this.showDropdownOfEducation = false;\r\n                console.log('degree form ', this.addNewAppUserForm.value); // Assuming menu.ED_id is the ID you want to store\r\n            }\r\n        }\r\n        this.calculateProfilePercentage();\r\n    }\r\n    getFormArrayValues(formArrayName) {\r\n        const formArray = this.addNewAppUserForm.get(formArrayName);\r\n        return formArray ? formArray.value : [];\r\n    }\r\n    getMenuTitle(id, formArrayName) {\r\n        if (formArrayName === 'U_institute') {\r\n            const menu = this.University.find((menu) => (menu === null || menu === void 0 ? void 0 : menu.INS_id) === id);\r\n            return (menu === null || menu === void 0 ? void 0 : menu.INS_title) ? menu === null || menu === void 0 ? void 0 : menu.INS_title : 'Unknown';\r\n        }\r\n        if (formArrayName === 'U_education') {\r\n            const menu = this.Degree.find((menu) => (menu === null || menu === void 0 ? void 0 : menu.ED_id) === id);\r\n            return (menu === null || menu === void 0 ? void 0 : menu.ED_name) ? menu === null || menu === void 0 ? void 0 : menu.ED_name : 'Unknown';\r\n        }\r\n    }\r\n    onClickOutside(event) {\r\n        if (!event.target.closest('.autocomplete-container-education')) {\r\n            this.showDropdownOfEducation = false; // Hide education dropdown if clicked outside\r\n        }\r\n        if (!event.target.closest('.autocomplete-container-institute')) {\r\n            this.showDropdown = false;\r\n        }\r\n        // Add similar checks for other dropdowns as needed\r\n    }\r\n    fetchPostcodes(query) {\r\n        if (query.length > 0) {\r\n            return this.httpClient\r\n                .get(`https://api.postcodes.io/postcodes/${query}/autocomplete`)\r\n                .pipe(map((response) => response.result || []), catchError(() => of([])) // Handle errors and return an empty array\r\n            );\r\n        }\r\n        else {\r\n            return of([]); // Return an observable of an empty array\r\n        }\r\n    }\r\n    filterPostcodes() {\r\n        const query = this.postcodeSearchControl.value;\r\n        if (query && query.length > 2) {\r\n            this.fetchPostcodes(query).subscribe((postcodes) => {\r\n                this.filteredPostcodes = postcodes;\r\n                this.showPostcodeDropdown = postcodes.length > 0;\r\n            });\r\n        }\r\n        else {\r\n            this.filteredPostcodes = [];\r\n            this.showPostcodeDropdown = false;\r\n        }\r\n    }\r\n    selectPostcode(postcode) {\r\n        this.selectedPostcode = postcode;\r\n        this.postcodeSearchControl.setValue('');\r\n        this.filteredPostcodes = [];\r\n        this.showPostcodeDropdown = false;\r\n        // Fetch detailed information about the selected postcode\r\n        this.httpClient\r\n            .get(`https://api.postcodes.io/postcodes?q=${postcode}`)\r\n            .subscribe((response) => {\r\n            var _a, _b;\r\n            const result = response.result && response.result[0];\r\n            if (result) {\r\n                this.selectedPostcode = `${result.admin_district}, ${result.country}`; // Customize as needed\r\n                // Update the form control with the detailed name\r\n                (_a = this.addNewAppUserForm.get('U_postcode')) === null || _a === void 0 ? void 0 : _a.setValue(postcode);\r\n            }\r\n            else {\r\n                // Handle case where no result is returned\r\n                (_b = this.addNewAppUserForm.get('U_postcode')) === null || _b === void 0 ? void 0 : _b.setValue('');\r\n            }\r\n        });\r\n        this.calculateProfilePercentage();\r\n    }\r\n    removeSelectedPostcode() {\r\n        var _a;\r\n        this.selectedPostcode = null;\r\n        (_a = this.addNewAppUserForm.get('U_postcode')) === null || _a === void 0 ? void 0 : _a.setValue('');\r\n        this.calculateProfilePercentage();\r\n    }\r\n    updateUserPreferences() {\r\n        this.applyQuizOptionValidator();\r\n        const identityFields = [\r\n            'U_ethinicityId',\r\n            'U_genderId',\r\n            'U_isDisability',\r\n            'U_religionId',\r\n            'U_sexualOrientationId',\r\n            'U_first_generation',\r\n            'U_freeMeal',\r\n            'U_ethinicityId',\r\n            'U_ethinicity_Toggle',\r\n            'U_genderId',\r\n            'U_gender_Toggle',\r\n            'U_isDisability',\r\n            'U_disability_Toggle',\r\n            'U_religionId',\r\n            'U_religion_Toggle',\r\n            'U_sexualOrientationId',\r\n            'U_sexuality_Toggle',\r\n            'U_institute',\r\n            'U_postcode',\r\n            'U_postcode_Toggle',\r\n            'U_first_generation',\r\n            'U_first_generation_Toggle',\r\n            'U_freeMeal',\r\n            'U_freeMeal_Toggle',\r\n            'U_education',\r\n            'U_name',\r\n            'U_email',\r\n            'U_password',\r\n            'U_profilePercentage',\r\n            'U_dp',\r\n        ];\r\n        identityFields.forEach(field => {\r\n            var _a, _b;\r\n            (_a = this.addNewAppUserForm.get(field)) === null || _a === void 0 ? void 0 : _a.clearValidators();\r\n            (_b = this.addNewAppUserForm.get(field)) === null || _b === void 0 ? void 0 : _b.updateValueAndValidity();\r\n        });\r\n        const nonIdentityFields = [\r\n            'LI_contentId'\r\n        ];\r\n        nonIdentityFields.forEach(field => {\r\n            var _a, _b;\r\n            (_a = this.addNewAppUserForm.get(field)) === null || _a === void 0 ? void 0 : _a.setValidators(Validators.required);\r\n            (_b = this.addNewAppUserForm.get(field)) === null || _b === void 0 ? void 0 : _b.updateValueAndValidity();\r\n        });\r\n        // Check if the form is valid\r\n        if (this.addNewAppUserForm.invalid) {\r\n            this.toastr.info(\"Please fill all required identity fields.\");\r\n            return;\r\n        }\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        const data = {\r\n            quizAns: [\r\n                {\r\n                    data: 'Answer 1',\r\n                    quizId: 'NA',\r\n                    QUA_quiz_options: this.addNewAppUserForm.value.QUA_quiz_options,\r\n                },\r\n            ],\r\n            LI_contentId: this.addNewAppUserForm.value.LI_contentId,\r\n            U_id: this.userData.U_id,\r\n            LI_contentType: \"industry\",\r\n            // U_seeker_disliked_industries:[''],\r\n        };\r\n        console.log(\"Data to update preferences\", data);\r\n        this.dataTransferService.updateUserPreferences(data).subscribe((res) => {\r\n            if (res.statusCode == 200) {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                this.toastr.success(\"User preferences updated successfully.\");\r\n            }\r\n            else {\r\n                console.error(\"Couldn't get status code 200\");\r\n                this.toastr.error(\"Unable to update user preferences\");\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n            }\r\n        }, (error) => {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            console.error(\"error\", error);\r\n            this.toastr.error(\"Unable to update user preferences\");\r\n        });\r\n    }\r\n    updateUserIdentity() {\r\n        const nonIdentityFields = [\r\n            'U_name',\r\n            'U_email',\r\n            'U_password',\r\n            'U_profilePercentage',\r\n            'U_dp',\r\n            'QUA_quiz_options',\r\n            'LI_contentId'\r\n        ];\r\n        nonIdentityFields.forEach(field => {\r\n            var _a, _b;\r\n            (_a = this.addNewAppUserForm.get(field)) === null || _a === void 0 ? void 0 : _a.clearValidators();\r\n            (_b = this.addNewAppUserForm.get(field)) === null || _b === void 0 ? void 0 : _b.updateValueAndValidity();\r\n        });\r\n        // Re-validate identity fields, especially U_genderId\r\n        const identityFields = [\r\n            'U_ethinicityId',\r\n            'U_genderId',\r\n            'U_isDisability',\r\n            'U_religionId',\r\n            'U_sexualOrientationId',\r\n            'U_first_generation',\r\n            'U_freeMeal',\r\n        ];\r\n        identityFields.forEach(field => {\r\n            var _a, _b;\r\n            (_a = this.addNewAppUserForm.get(field)) === null || _a === void 0 ? void 0 : _a.setValidators(Validators.required);\r\n            (_b = this.addNewAppUserForm.get(field)) === null || _b === void 0 ? void 0 : _b.updateValueAndValidity();\r\n        });\r\n        // Check if the form is valid\r\n        if (this.addNewAppUserForm.invalid) {\r\n            this.toastr.info(\"Please fill all required identity fields.\");\r\n            return;\r\n        }\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        this.calculateProfilePercentage();\r\n        const data = {\r\n            U_id: this.userData.U_id,\r\n            U_profilePercentage: this.addNewAppUserForm.value.U_profilePercentage,\r\n            U_ethinicityId: this.addNewAppUserForm.value.U_ethinicityId,\r\n            U_ethinicity_Toggle: this.addNewAppUserForm.value.U_ethinicity_Toggle,\r\n            U_genderId: this.addNewAppUserForm.value.U_genderId,\r\n            U_gender_Toggle: this.addNewAppUserForm.value.U_gender_Toggle,\r\n            U_isDisability: this.addNewAppUserForm.value.U_isDisability,\r\n            U_disability_Toggle: this.addNewAppUserForm.value.U_disability_Toggle,\r\n            U_religionId: this.addNewAppUserForm.value.U_religionId,\r\n            U_religion_Toggle: this.addNewAppUserForm.value.U_religion_Toggle,\r\n            U_sexualOrientationId: this.addNewAppUserForm.value.U_sexualOrientationId,\r\n            U_sexuality_Toggle: this.addNewAppUserForm.value.U_sexuality_Toggle,\r\n            U_institute: this.addNewAppUserForm.value.U_institute ? this.addNewAppUserForm.value.U_institute : '',\r\n            U_postcode: this.addNewAppUserForm.value.U_postcode ? this.addNewAppUserForm.value.U_postcode : '',\r\n            U_postcode_Toggle: this.addNewAppUserForm.value.U_postcode_Toggle,\r\n            U_first_generation: this.addNewAppUserForm.value.U_first_generation,\r\n            U_first_generation_Toggle: this.addNewAppUserForm.value.U_first_generation_Toggle,\r\n            U_freeMeal: this.addNewAppUserForm.value.U_freeMeal,\r\n            U_freeMeal_Toggle: this.addNewAppUserForm.value.U_freeMeal_Toggle,\r\n            U_education: this.addNewAppUserForm.value.U_education ? this.addNewAppUserForm.value.U_education : '',\r\n        };\r\n        console.log(\"Data to update identity\", data);\r\n        this.dataTransferService.updateUserIdentity(data).subscribe((res) => {\r\n            if (res.statusCode === 200) {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                this.toastr.success(\"User identity updated successfully.\");\r\n            }\r\n            else {\r\n                console.error(\"Couldn't get status code 200\");\r\n                this.toastr.error(\"Unable to update user identity\");\r\n            }\r\n        }, (error) => {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            console.error(\"error\", error);\r\n            this.toastr.error(\"Unable to update user identity\");\r\n        });\r\n    }\r\n    async updateUserDetails() {\r\n        var _a, _b, _c, _d;\r\n        try {\r\n            if (!((_b = (_a = this.addNewAppUserForm.get('U_aliasDp')) === null || _a === void 0 ? void 0 : _a.errors) === null || _b === void 0 ? void 0 : _b.fileAspectRatioValidator) && !((_d = (_c = this.addNewAppUserForm.get('U_dp')) === null || _c === void 0 ? void 0 : _c.errors) === null || _d === void 0 ? void 0 : _d.fileAspectRatioValidator)) {\r\n                let userDp;\r\n                let aliasDp;\r\n                this.ngxSpinnerService.show('globalSpinner');\r\n                if (this.addNewAppUserForm.value.U_dp) {\r\n                    await this.uploadLogoUrl(this.imageName);\r\n                    userDp = this.baseUrl + this.imageName.name;\r\n                }\r\n                else {\r\n                    userDp = this.userData.U_dp;\r\n                }\r\n                if (this.addNewAppUserForm.value.U_aliasDp) {\r\n                    await this.uploadLogoUrl(this.aliasImageName);\r\n                    aliasDp = this.baseUrl + this.aliasImageName.name;\r\n                }\r\n                else {\r\n                    aliasDp = this.userData.U_aliasDp;\r\n                }\r\n                const data = {\r\n                    U_id: this.userData.U_id,\r\n                    U_dp: userDp,\r\n                    U_aliasName: (this.addNewAppUserForm.value.U_aliasName || this.userData.U_aliasName) || '',\r\n                    U_aliasDp: aliasDp || '',\r\n                };\r\n                console.log(\"User details data to update\", data);\r\n                this.dataTransferService.updateUserDetails(data).subscribe((res) => {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    this.toastr.success(\"User details updated successfully.\");\r\n                }, (error) => {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    console.error(\"error\", error);\r\n                    this.toastr.error(\"Unable to update user details\");\r\n                });\r\n            }\r\n            else {\r\n                this.toastr.info(\"Please resize the image\");\r\n            }\r\n        }\r\n        catch (_e) {\r\n            this.toastr.error(\"Unable to update user details\");\r\n        }\r\n    }\r\n    applyRequiredValidators() {\r\n        const requiredFields = [\r\n            'U_name',\r\n            'U_email',\r\n            'U_password',\r\n            'U_profilePercentage',\r\n            'U_ethinicityId',\r\n            'U_genderId',\r\n            'U_isDisability',\r\n            'U_religionId',\r\n            'U_sexualOrientationId',\r\n            'U_first_generation',\r\n            'U_freeMeal',\r\n            'LI_contentId',\r\n        ];\r\n        const excludedFields = [\r\n            'U_dp',\r\n            'U_education',\r\n            'U_postcode',\r\n            'U_institute',\r\n            'U_isExpert'\r\n        ];\r\n        Object.keys(this.addNewAppUserForm.controls).forEach((key) => {\r\n            if (requiredFields.includes(key) && !excludedFields.includes(key)) {\r\n                const control = this.addNewAppUserForm.get(key);\r\n                if (control) {\r\n                    const currentValidators = control.validator ? [control.validator] : [];\r\n                    control.setValidators([...currentValidators, Validators.required]);\r\n                    control.updateValueAndValidity(); // Update the control to reflect the new validators\r\n                }\r\n            }\r\n        });\r\n    }\r\n    applyQuizOptionValidator() {\r\n        const quizOptionsControl = this.addNewAppUserForm.get('QUA_quiz_options');\r\n        if (quizOptionsControl) {\r\n            const hasQuizIdFive = quizOptionsControl.value.some((option) => option.quiz_id === '5');\r\n            if (!hasQuizIdFive) {\r\n                quizOptionsControl.setErrors({ quizIdFiveMissing: true });\r\n            }\r\n            else {\r\n                quizOptionsControl.setErrors(null);\r\n            }\r\n        }\r\n    }\r\n    createNewAppUser() {\r\n        this.applyRequiredValidators();\r\n        this.applyQuizOptionValidator();\r\n        if (this.addNewAppUserForm.invalid) {\r\n            console.log('this.addNewAppUserForm.value', this.addNewAppUserForm.value);\r\n            Object.keys(this.addNewAppUserForm.controls).forEach((key) => {\r\n                var _a;\r\n                const controlErrors = (_a = this.addNewAppUserForm.get(key)) === null || _a === void 0 ? void 0 : _a.errors;\r\n                if (controlErrors) {\r\n                    console.log(`Field ${key} is invalid:`, controlErrors);\r\n                }\r\n            });\r\n            this.toastr.info('Please fill all required fields correctly');\r\n            return;\r\n        }\r\n        else {\r\n            // Initialize the Sharer DP and Alias DP URL variables\r\n            let sharerDpUrl;\r\n            let aliasDpUrl;\r\n            // Handle Sharer DP upload if image is available\r\n            const sharerDpPromise = this.imageName ? this.uploadLogoUrl(this.imageName) : Promise.resolve(null);\r\n            console.log(\"sharerDpPromise\", sharerDpPromise);\r\n            // Handle Alias DP upload if image is available\r\n            const aliasDpPromise = this.aliasImageName ? this.uploadLogoUrl(this.aliasImageName) : Promise.resolve(null);\r\n            console.log(\"aliasDpPromise\", aliasDpPromise);\r\n            // Wait for both images to upload (if present)\r\n            Promise.all([sharerDpPromise, aliasDpPromise]).then(([sharerDpResponse, aliasDpResponse]) => {\r\n                var _a, _b;\r\n                // Set the URLs if the images were uploaded\r\n                sharerDpUrl = this.imageName ? this.baseUrl + ((_a = this.imageName) === null || _a === void 0 ? void 0 : _a.name) : '';\r\n                console.log(\"sharerDpUrl\", sharerDpUrl);\r\n                aliasDpUrl = this.aliasImageName ? this.baseUrl + ((_b = this.aliasImageName) === null || _b === void 0 ? void 0 : _b.name) : '';\r\n                console.log(\"aliasDpUrl\", aliasDpUrl);\r\n                // Finalize the post data with both Sharer and Alias DP URLs\r\n                this.finalizePostData(sharerDpUrl, aliasDpUrl);\r\n            }).catch((error) => {\r\n                console.error('Error uploading images:', error);\r\n                this.toastr.error(\"Couldn't upload images.\");\r\n            });\r\n        }\r\n    }\r\n    // Function to upload the image and return a Promise\r\n    uploadLogoUrl(image) {\r\n        return new Promise((resolve, reject) => {\r\n            if (!image) {\r\n                resolve(null);\r\n            }\r\n            console.log('Uploading image:', image);\r\n            this.dataTransferService.uploadurl(image).subscribe((res) => {\r\n                console.log('Image upload success:', image === null || image === void 0 ? void 0 : image.name);\r\n                resolve(res); // Return the response when the upload is successful\r\n            }, (error) => {\r\n                console.error('Image upload failed:', error);\r\n                reject(error); // Reject the promise if the upload fails\r\n            });\r\n        });\r\n    }\r\n    // Function to finalize postData and submit form\r\n    finalizePostData(sharerDpUrl, aliasDpUrl) {\r\n        const postData = {\r\n            U_name: this.addNewAppUserForm.value.U_name,\r\n            U_email: this.addNewAppUserForm.value.U_email,\r\n            U_password: this.addNewAppUserForm.value.U_password,\r\n            U_isExpert: this.addNewAppUserForm.value.U_isExpert,\r\n            U_countryId: 'UK',\r\n            U_profilePercentage: this.addNewAppUserForm.value.U_profilePercentage,\r\n            U_dob: '',\r\n            U_ethinicityId: this.addNewAppUserForm.value.U_ethinicityId,\r\n            U_ethinicity_Toggle: this.addNewAppUserForm.value.U_ethinicity_Toggle,\r\n            U_genderId: this.addNewAppUserForm.value.U_genderId,\r\n            U_gender_Toggle: this.addNewAppUserForm.value.U_gender_Toggle,\r\n            U_isDisability: this.addNewAppUserForm.value.U_isDisability,\r\n            U_disability_Toggle: this.addNewAppUserForm.value.U_disability_Toggle,\r\n            U_religionId: this.addNewAppUserForm.value.U_religionId,\r\n            U_religion_Toggle: this.addNewAppUserForm.value.U_religion_Toggle,\r\n            U_sexualOrientationId: this.addNewAppUserForm.value.U_sexualOrientationId,\r\n            U_sexuality_Toggle: this.addNewAppUserForm.value.U_sexuality_Toggle,\r\n            U_institute: this.addNewAppUserForm.value.U_institute || '',\r\n            U_postcode: this.addNewAppUserForm.value.U_postcode || '',\r\n            U_postcode_Toggle: this.addNewAppUserForm.value.U_postcode_Toggle,\r\n            U_first_generation: this.addNewAppUserForm.value.U_first_generation,\r\n            U_first_generation_Toggle: this.addNewAppUserForm.value.U_first_generation_Toggle,\r\n            U_dp: sharerDpUrl || '',\r\n            U_aliasDp: aliasDpUrl || '',\r\n            U_aliasName: this.addNewAppUserForm.value.U_aliasName,\r\n            U_profileAnonymous: this.addNewAppUserForm.value.U_profileAnonymous,\r\n            U_isSharer: '1',\r\n            U_industryId: '1',\r\n            U_roleId: '1',\r\n            U_regionalAccentId: '1',\r\n            U_registertype: 'email',\r\n            U_registertypeId: '1',\r\n            U_activeStatus: '1',\r\n            U_freeMeal: this.addNewAppUserForm.value.U_freeMeal,\r\n            U_freeMeal_Toggle: this.addNewAppUserForm.value.U_freeMeal_Toggle,\r\n            U_education: this.addNewAppUserForm.value.U_education || '',\r\n            U_totalLikeCount: 0,\r\n            U_seeker_disliked_industries: [\r\n                { U_seekerDislikedIndId: '' },\r\n            ],\r\n            quizAns: [\r\n                {\r\n                    data: 'Answer 1',\r\n                    quizId: 'NA',\r\n                    QUA_quiz_options: this.addNewAppUserForm.value.QUA_quiz_options,\r\n                },\r\n            ],\r\n            LI_contentId: this.addNewAppUserForm.value.LI_contentId,\r\n        };\r\n        console.log(postData);\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        this.dataTransferService.createNewAppUser(postData).subscribe((res) => {\r\n            if (res.statusCode == 200) {\r\n                this.toastr.success('New sharer added successfully.');\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                this.router.navigate(['/actions/app-users']);\r\n                this.dataTransferService.getAllAppUsers();\r\n            }\r\n            else {\r\n                this.toastr.error('Something went wrong');\r\n                console.error('Error:', \"Status code error\");\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n            }\r\n        }, (error) => {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            if (error.status === 400) {\r\n                this.toastr.error('This sharer already exists.');\r\n            }\r\n            else if (error.status === 500) {\r\n                this.toastr.success('New sharer added successfully.');\r\n                this.router.navigate(['/actions/app-users']);\r\n                this.dataTransferService.getAllAppUsers();\r\n            }\r\n            else {\r\n                console.error('Error:', error);\r\n                this.toastr.error(\"Couldn't add new sharer.\");\r\n            }\r\n        });\r\n    }\r\n    showAddUniversityDegreeModal(title) {\r\n        if (title === 'University') {\r\n            const modal = document.getElementById('add-university-modal');\r\n            if (modal) {\r\n                modal.style.display = 'block';\r\n            }\r\n        }\r\n        else {\r\n            const modal = document.getElementById('add-degree-modal');\r\n            if (modal) {\r\n                modal.style.display = 'block';\r\n            }\r\n        }\r\n    }\r\n    hideAddUniversityDegreeModal(title) {\r\n        if (title === 'University') {\r\n            const modal = document.getElementById('add-university-modal');\r\n            if (modal) {\r\n                modal.style.display = 'none';\r\n            }\r\n            const universityInput = document.getElementById('INS_title');\r\n            if (universityInput) {\r\n                universityInput.value = '';\r\n            }\r\n        }\r\n        else {\r\n            const modal = document.getElementById('add-degree-modal');\r\n            if (modal) {\r\n                modal.style.display = 'none';\r\n            }\r\n            const degreeInput = document.getElementById('ED_name');\r\n            if (degreeInput) {\r\n                degreeInput.value = '';\r\n            }\r\n        }\r\n    }\r\n    addUniversity() {\r\n        const titleElement = document.getElementById('INS_title');\r\n        const title = titleElement ? titleElement.value : '';\r\n        console.log('title', title);\r\n        const data = {\r\n            INS_title: title,\r\n        };\r\n        this.dataTransferService.addUniversity(data).subscribe((res) => {\r\n            if (res.statusCode === 200) {\r\n                this.toastr.success('New University Added Successfully');\r\n                this.getAllUniversity();\r\n                this.hideAddUniversityDegreeModal('University');\r\n            }\r\n        }, (error) => {\r\n            if (error.status === 400) {\r\n                this.toastr.error('The university already exists, as given.');\r\n                return;\r\n            }\r\n            this.toastr.error('Unable to add university');\r\n            console.log('Error while adding university', error);\r\n        });\r\n    }\r\n    addDegree() {\r\n        const titleElement = document.getElementById('ED_name');\r\n        const title = titleElement ? titleElement.value : '';\r\n        console.log('title', title);\r\n        const data = {\r\n            ED_name: title,\r\n        };\r\n        this.dataTransferService.addDegree(data).subscribe((res) => {\r\n            if (res.statusCode === 200) {\r\n                this.toastr.success('New Degree Added Successfully');\r\n                this.getAllDegree();\r\n                this.hideAddUniversityDegreeModal('Degree');\r\n            }\r\n        }, (error) => {\r\n            if (error.status === 400) {\r\n                this.toastr.error('The Degree already exists, as given.');\r\n                return;\r\n            }\r\n            this.toastr.error('Unable to add degree');\r\n            console.log('Error while adding degree', error);\r\n        });\r\n    }\r\n}\r\nEditAppUserComponent.ɵfac = function EditAppUserComponent_Factory(t) { return new (t || EditAppUserComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.DataTransferService), i0.ɵɵdirectiveInject(i4.ToastrService), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i5.HttpClient), i0.ɵɵdirectiveInject(i6.NgxSpinnerService), i0.ɵɵdirectiveInject(i1.ActivatedRoute)); };\r\nEditAppUserComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: EditAppUserComponent, selectors: [[\"app-edit-app-user\"]], hostBindings: function EditAppUserComponent_HostBindings(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵlistener(\"click\", function EditAppUserComponent_click_HostBindingHandler($event) { return ctx.onClickOutside($event); }, false, i0.ɵɵresolveDocument);\r\n    } }, decls: 175, vars: 39, consts: [[1, \"content-wrapper\"], [1, \"container-scroll\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\", 2, \"background-color\", \"hsl(0, 0%, 100%)\"], [3, \"formGroup\"], [1, \"section\", \"shadow-lg\", \"py-3\", \"px-2\"], [1, \"heading\", \"shadow-sm\"], [1, \"row\", \"mx-3\", \"pt-2\"], [\"class\", \"form-group col-lg-6 pl-4\", 4, \"ngIf\"], [1, \"form-group\", \"col-lg-6\", \"pl-4\"], [\"for\", \"U_dp\", 1, \"label\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"U_dp\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Sharer DP\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"class\", \"btn-custom-small\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"row mx-4 mb-5\", 4, \"ngIf\"], [\"class\", \"row mx-3\", 4, \"ngIf\"], [\"class\", \"cropper-container\", \"class\", \"row mx-4 mb-5\", 4, \"ngIf\"], [\"class\", \"text-center mt-5\", 4, \"ngIf\"], [1, \"section\", \"shadow-lg\", \"py-3\", \"px-2\", \"mt-4\"], [1, \"form-group\", \"pl-4\", \"mt-3\"], [\"for\", \"QUA_quiz_options\", 1, \"required-field\", \"label\", \"ml-4\"], [1, \"d-flex\", \"flex-wrap\"], [\"class\", \"tag\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-group\", \"pl-4\", \"mt-4\"], [\"for\", \"LI_contentId\", 1, \"required-field\", \"label\", \"ml-4\"], [\"for\", \"QUA_quiz_options\", 1, \"label\", \"ml-4\"], [1, \"form-check\", \"form-switch\", \"pt-2\"], [\"for\", \"flexSwitchCheckDefault\", 1, \"form-check-label\"], [\"checked\", \"\", \"disabled\", \"\", \"type\", \"checkbox\", \"id\", \"flexSwitchCheckDefault\", 1, \"form-check-input\", \"form-check-input-head\", \"mr-4\"], [1, \"form-check\", \"form-switch\"], [\"for\", \"U_genderId\", 1, \"required-field\", \"form-check-label\", \"label\"], [\"type\", \"checkbox\", \"id\", \"U_genderId\", \"formControlName\", \"U_gender_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_ethinicityId\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_ethinicityId\", \"formControlName\", \"U_ethinicity_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_religionId\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_religionId\", \"formControlName\", \"U_religion_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_sexualOrientationId\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_sexualOrientationId\", \"formControlName\", \"U_sexuality_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_isDisability\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_isDisability\", \"formControlName\", \"U_disability_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_first_generation\", 1, \"required-field\", \"label\", \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"U_first_generation\", \"formControlName\", \"U_first_generation_Toggle\", 1, \"form-check-input\"], [\"for\", \"U_freeMeal\", 1, \"required-field\", \"label\", \"ml-4\"], [\"type\", \"checkbox\", \"id\", \"U_freeMeal_Toggle\", \"formControlName\", \"U_freeMeal_Toggle\", 1, \"form-check-input\"], [1, \"form-group\", \"col-lg-7\", \"pl-4\", \"col-md-6\", \"col-sm-12\", \"mb-3\"], [\"for\", \"U_institute\", 1, \"label\", \"universityLabel\", \"ml-4\"], [1, \"autocomplete-container\", \"autocomplete-container-institute\", \"ml-4\"], [1, \"tag-input-wrapper\"], [\"class\", \"selected-options\", 4, \"ngIf\"], [\"type\", \"text\", \"placeholder\", \"Type to search...\", \"required\", \"\", 1, \"form-control\", \"tag-input\", \"form-control-sm\", 3, \"formControl\", \"input\", \"click\"], [\"class\", \"dropdown-list\", 4, \"ngIf\"], [\"class\", \"no-options-message\", 4, \"ngIf\"], [\"for\", \"U_education\", 1, \"label\", \"ml-4\"], [1, \"addNewBtn\", 3, \"click\"], [1, \"autocomplete-container\", \"autocomplete-container-education\", \"ml-4\"], [\"id\", \"add-degree-modal\", 1, \"add-degree-modal\", \"modal\"], [1, \"modal-dialog\", \"modal-dialog-centered\"], [1, \"modal-content\"], [1, \"modal-header\"], [\"id\", \"degreeBackdropLabel\", 1, \"modal-title\", \"fs-5\"], [\"type\", \"button\", \"data-dismiss\", \"add-degree-modal\", 1, \"close\", 2, \"color\", \"white\", 3, \"click\"], [1, \"modal-body\"], [\"type\", \"text\", \"id\", \"ED_name\", \"placeholder\", \"Enter New Degree Name\", 1, \"form-control\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"add-degree-modal\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"type\", \"button\", \"data-bs-dismiss\", \"add-degree-modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"for\", \"U_postcode\", 1, \"label\", \"ml-4\"], [\"type\", \"checkbox\", \"id\", \"U_postcode_Toggle\", \"formControlName\", \"U_postcode_Toggle\", 1, \"form-check-input\"], [\"id\", \"add-university-modal\", 1, \"add-university-modal\", \"modal\"], [\"id\", \"universityBackdropLabel\", 1, \"modal-title\", \"fs-5\"], [\"type\", \"button\", \"data-dismiss\", \"add-university-modal\", 1, \"close\", 2, \"color\", \"white\", 3, \"click\"], [\"type\", \"text\", \"id\", \"INS_title\", \"placeholder\", \"Enter New University Name\", 1, \"form-control\"], [\"type\", \"button\", \"data-bs-dismiss\", \"add-university-modal\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"type\", \"button\", \"data-bs-dismiss\", \"add-university-modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"for\", \"U_name\", 1, \"required-field\", \"label\"], [\"type\", \"text\", \"id\", \"U_name\", \"formControlName\", \"U_name\", \"placeholder\", \"Enter User Name\", 1, \"form-control\"], [\"for\", \"U_email\", 1, \"required-field\", \"label\"], [\"type\", \"email\", \"id\", \"U_email\", \"formControlName\", \"U_email\", \"placeholder\", \"Enter User Email\", \"autocomplete\", \"new-password\", 1, \"form-control\"], [1, \"text-danger\"], [4, \"ngIf\"], [\"for\", \"U_password\", 1, \"required-field\", \"label\"], [\"type\", \"password\", \"id\", \"U_password\", \"formControlName\", \"U_password\", \"placeholder\", \"Enter password\", \"autocomplete\", \"new-password\", 1, \"form-control\"], [1, \"fa\", \"fa-eye\", \"position-absolute\", \"toggle-password\", 3, \"click\"], [\"togglePasswordIcon\", \"\"], [\"alt\", \"Sharer DP\", 1, \"img-preview\", 3, \"src\"], [1, \"btn-custom-small\", 3, \"click\"], [1, \"row\", \"mx-4\", \"mb-5\"], [1, \"image-cropper\", \"text-center\", \"col-lg-12\"], [1, \"py-2\"], [\"format\", \"png\", 1, \"custom-image-cropper\", \"my-2\", 3, \"imageChangedEvent\", \"aspectRatio\", \"maintainAspectRatio\", \"imageCropped\", \"imageLoaded\", \"cropperReady\", \"loadImageFailed\"], [1, \"btn\", \"btn-success\", \"btn-sm\", \"m-2\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"m-2\", 3, \"click\"], [1, \"row\", \"mx-3\"], [1, \"form-check\", \"ml-1\"], [\"type\", \"checkbox\", \"formControlName\", \"U_isExpert\", \"id\", \"U_isExpert\"], [\"for\", \"U_isExpert\", 1, \"label\", \"ml-2\", \"mt-2\"], [\"type\", \"checkbox\", \"formControlName\", \"U_profileAnonymous\", \"id\", \"U_profileAnonymous\", 3, \"change\"], [\"for\", \"U_profileAnonymous\", 1, \"label\", \"ml-2\", \"mt-2\"], [\"for\", \"U_aliasName\", 1, \"label\"], [\"type\", \"text\", \"id\", \"U_aliasName\", \"formControlName\", \"U_aliasName\", \"placeholder\", \"Enter User Name\", 1, \"form-control\"], [\"for\", \"U_aliasDp\", 1, \"label\"], [\"type\", \"file\", \"formControlName\", \"U_aliasDp\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Alias DP Image\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"alt\", \"Alias DP Image\", 1, \"img-preview\", 3, \"src\"], [1, \"text-center\", \"mt-5\"], [1, \"btn\", \"btn-primary\", \"mr-2\", \"px-5\", \"my-2\", \"submit-btn\", 3, \"click\"], [\"routerLink\", \"/actions/app-users\", 1, \"btn\", \"btn-light\", \"px-5\"], [1, \"tag\", 3, \"click\"], [1, \"check-mark\"], [1, \"selected-options\"], [1, \"selected-option\"], [\"type\", \"button\", 1, \"remove-selected-option\", 3, \"click\"], [1, \"dropdown-list\"], [3, \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"click\"], [1, \"no-options-message\"], [1, \"btn\", \"btn-primary\", \"mr-2\", \"px-5\", \"submit-btn\", 3, \"click\"]], template: function EditAppUserComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵelementStart(0, \"app-sidebar\");\r\n        i0.ɵɵelementStart(1, \"div\", 0);\r\n        i0.ɵɵelementStart(2, \"div\", 1);\r\n        i0.ɵɵelementStart(3, \"div\", 2);\r\n        i0.ɵɵtext(4);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(5, \"div\", 3);\r\n        i0.ɵɵelementStart(6, \"form\", 4);\r\n        i0.ɵɵelementStart(7, \"div\", 5);\r\n        i0.ɵɵelementStart(8, \"h4\", 6);\r\n        i0.ɵɵtext(9, \"Sharer Details\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(10, \"div\", 7);\r\n        i0.ɵɵtemplate(11, EditAppUserComponent_div_11_Template, 4, 0, \"div\", 8);\r\n        i0.ɵɵtemplate(12, EditAppUserComponent_div_12_Template, 5, 1, \"div\", 8);\r\n        i0.ɵɵtemplate(13, EditAppUserComponent_div_13_Template, 7, 1, \"div\", 8);\r\n        i0.ɵɵelementStart(14, \"div\", 9);\r\n        i0.ɵɵelementStart(15, \"label\", 10);\r\n        i0.ɵɵtext(16, \"Sharer DP\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(17, \"div\", 11);\r\n        i0.ɵɵelementStart(18, \"input\", 12);\r\n        i0.ɵɵlistener(\"change\", function EditAppUserComponent_Template_input_change_18_listener($event) { return ctx.onFileSelected($event, \"U_dp\"); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(19, EditAppUserComponent_img_19_Template, 1, 1, \"img\", 13);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(20, EditAppUserComponent_div_20_Template, 2, 0, \"div\", 14);\r\n        i0.ɵɵtemplate(21, EditAppUserComponent_div_21_Template, 2, 0, \"div\", 14);\r\n        i0.ɵɵtemplate(22, EditAppUserComponent_a_22_Template, 2, 0, \"a\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(23, EditAppUserComponent_div_23_Template, 9, 3, \"div\", 16);\r\n        i0.ɵɵtemplate(24, EditAppUserComponent_div_24_Template, 3, 2, \"div\", 17);\r\n        i0.ɵɵtemplate(25, EditAppUserComponent_div_25_Template, 14, 5, \"div\", 17);\r\n        i0.ɵɵtemplate(26, EditAppUserComponent_div_26_Template, 9, 3, \"div\", 18);\r\n        i0.ɵɵtemplate(27, EditAppUserComponent_div_27_Template, 5, 0, \"div\", 19);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(28, \"div\", 20);\r\n        i0.ɵɵelementStart(29, \"h4\", 6);\r\n        i0.ɵɵtext(30, \"Preferences\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(31, \"div\", 21);\r\n        i0.ɵɵelementStart(32, \"label\", 22);\r\n        i0.ɵɵtext(33, \"Activities They Do a Lot In Their Role\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(34, \"div\", 23);\r\n        i0.ɵɵtemplate(35, EditAppUserComponent_div_35_Template, 4, 3, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(36, \"hr\");\r\n        i0.ɵɵelementStart(37, \"div\", 25);\r\n        i0.ɵɵelementStart(38, \"label\", 26);\r\n        i0.ɵɵtext(39, \"Sectors\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(40, \"div\", 23);\r\n        i0.ɵɵtemplate(41, EditAppUserComponent_div_41_Template, 4, 3, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(42, \"hr\");\r\n        i0.ɵɵelementStart(43, \"div\", 25);\r\n        i0.ɵɵelementStart(44, \"label\", 27);\r\n        i0.ɵɵtext(45, \"Their Typical Work Type\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(46, \"div\", 23);\r\n        i0.ɵɵtemplate(47, EditAppUserComponent_div_47_Template, 5, 5, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(48, EditAppUserComponent_div_48_Template, 5, 0, \"div\", 19);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(49, \"div\", 20);\r\n        i0.ɵɵelementStart(50, \"h4\", 6);\r\n        i0.ɵɵtext(51, \"Identity\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(52, \"div\", 28);\r\n        i0.ɵɵelementStart(53, \"label\", 29);\r\n        i0.ɵɵtext(54, \"Activate the toggle below if you want this info displayed on your profile\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(55, \"input\", 30);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(56, \"hr\");\r\n        i0.ɵɵelementStart(57, \"div\", 25);\r\n        i0.ɵɵelementStart(58, \"div\", 31);\r\n        i0.ɵɵelementStart(59, \"label\", 32);\r\n        i0.ɵɵtext(60, \"Gender\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(61, \"input\", 33);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(62, \"div\", 23);\r\n        i0.ɵɵtemplate(63, EditAppUserComponent_div_63_Template, 4, 3, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(64, \"hr\");\r\n        i0.ɵɵelementStart(65, \"div\", 25);\r\n        i0.ɵɵelementStart(66, \"div\", 31);\r\n        i0.ɵɵelementStart(67, \"label\", 34);\r\n        i0.ɵɵtext(68, \"Ethnicity\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(69, \"input\", 35);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(70, \"div\", 23);\r\n        i0.ɵɵtemplate(71, EditAppUserComponent_div_71_Template, 4, 3, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(72, \"hr\");\r\n        i0.ɵɵelementStart(73, \"div\", 25);\r\n        i0.ɵɵelementStart(74, \"div\", 31);\r\n        i0.ɵɵelementStart(75, \"label\", 36);\r\n        i0.ɵɵtext(76, \"Religion\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(77, \"input\", 37);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(78, \"div\", 23);\r\n        i0.ɵɵtemplate(79, EditAppUserComponent_div_79_Template, 4, 3, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(80, \"hr\");\r\n        i0.ɵɵelementStart(81, \"div\", 25);\r\n        i0.ɵɵelementStart(82, \"div\", 31);\r\n        i0.ɵɵelementStart(83, \"label\", 38);\r\n        i0.ɵɵtext(84, \"Sexual Orientation\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(85, \"input\", 39);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(86, \"div\", 23);\r\n        i0.ɵɵtemplate(87, EditAppUserComponent_div_87_Template, 4, 3, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(88, \"hr\");\r\n        i0.ɵɵelementStart(89, \"div\", 25);\r\n        i0.ɵɵelementStart(90, \"div\", 31);\r\n        i0.ɵɵelementStart(91, \"label\", 40);\r\n        i0.ɵɵtext(92, \"Has A Disability ?\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(93, \"input\", 41);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(94, \"div\", 23);\r\n        i0.ɵɵtemplate(95, EditAppUserComponent_div_95_Template, 4, 3, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(96, \"hr\");\r\n        i0.ɵɵelementStart(97, \"div\", 25);\r\n        i0.ɵɵelementStart(98, \"div\", 31);\r\n        i0.ɵɵelementStart(99, \"label\", 42);\r\n        i0.ɵɵtext(100, \"Is This First Generation ?\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(101, \"input\", 43);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(102, \"div\", 23);\r\n        i0.ɵɵtemplate(103, EditAppUserComponent_div_103_Template, 4, 3, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(104, \"hr\");\r\n        i0.ɵɵelementStart(105, \"div\", 25);\r\n        i0.ɵɵelementStart(106, \"label\", 44);\r\n        i0.ɵɵtext(107, \"Was Entitled To Free School Meals ?\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(108, \"input\", 45);\r\n        i0.ɵɵelementStart(109, \"div\", 23);\r\n        i0.ɵɵtemplate(110, EditAppUserComponent_div_110_Template, 4, 3, \"div\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(111, \"hr\");\r\n        i0.ɵɵelementStart(112, \"div\", 46);\r\n        i0.ɵɵelementStart(113, \"label\", 47);\r\n        i0.ɵɵtext(114, \"University\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(115, \"div\", 48);\r\n        i0.ɵɵelementStart(116, \"div\", 49);\r\n        i0.ɵɵtemplate(117, EditAppUserComponent_div_117_Template, 5, 1, \"div\", 50);\r\n        i0.ɵɵelementStart(118, \"input\", 51);\r\n        i0.ɵɵlistener(\"input\", function EditAppUserComponent_Template_input_input_118_listener() { return ctx.filterOptions(\"U_institute\"); })(\"click\", function EditAppUserComponent_Template_input_click_118_listener() { return ctx.showDropdown = true; });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(119, EditAppUserComponent_div_119_Template, 3, 1, \"div\", 52);\r\n        i0.ɵɵtemplate(120, EditAppUserComponent_div_120_Template, 2, 0, \"div\", 53);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(121, \"hr\");\r\n        i0.ɵɵelementStart(122, \"div\", 46);\r\n        i0.ɵɵelementStart(123, \"label\", 54);\r\n        i0.ɵɵtext(124, \"Degree\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(125, \"a\", 55);\r\n        i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_a_click_125_listener() { return ctx.showAddUniversityDegreeModal(\"Degree\"); });\r\n        i0.ɵɵtext(126, \"Add New +\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(127, \"div\", 56);\r\n        i0.ɵɵelementStart(128, \"div\", 49);\r\n        i0.ɵɵtemplate(129, EditAppUserComponent_div_129_Template, 5, 1, \"div\", 50);\r\n        i0.ɵɵelementStart(130, \"input\", 51);\r\n        i0.ɵɵlistener(\"input\", function EditAppUserComponent_Template_input_input_130_listener() { return ctx.filterOptions(\"U_education\"); })(\"click\", function EditAppUserComponent_Template_input_click_130_listener() { return ctx.showDropdownOfEducation = true; });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(131, EditAppUserComponent_div_131_Template, 3, 1, \"div\", 52);\r\n        i0.ɵɵtemplate(132, EditAppUserComponent_div_132_Template, 2, 0, \"div\", 53);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(133, \"div\", 57);\r\n        i0.ɵɵelementStart(134, \"div\", 58);\r\n        i0.ɵɵelementStart(135, \"div\", 59);\r\n        i0.ɵɵelementStart(136, \"div\", 60);\r\n        i0.ɵɵelementStart(137, \"h4\", 61);\r\n        i0.ɵɵtext(138, \"Add New Degree\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(139, \"button\", 62);\r\n        i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_139_listener() { return ctx.hideAddUniversityDegreeModal(\"Degree\"); });\r\n        i0.ɵɵtext(140, \"\\u00D7\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(141, \"div\", 63);\r\n        i0.ɵɵelement(142, \"input\", 64);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(143, \"div\", 65);\r\n        i0.ɵɵelementStart(144, \"button\", 66);\r\n        i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_144_listener() { return ctx.addDegree(); });\r\n        i0.ɵɵtext(145, \"Add\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(146, \"button\", 67);\r\n        i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_146_listener() { return ctx.hideAddUniversityDegreeModal(\"Degree\"); });\r\n        i0.ɵɵtext(147, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(148, \"hr\");\r\n        i0.ɵɵelementStart(149, \"div\", 46);\r\n        i0.ɵɵelementStart(150, \"label\", 68);\r\n        i0.ɵɵtext(151, \"Postcode\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(152, \"input\", 69);\r\n        i0.ɵɵelementStart(153, \"div\", 56);\r\n        i0.ɵɵelementStart(154, \"div\", 49);\r\n        i0.ɵɵtemplate(155, EditAppUserComponent_div_155_Template, 5, 1, \"div\", 50);\r\n        i0.ɵɵelementStart(156, \"input\", 51);\r\n        i0.ɵɵlistener(\"input\", function EditAppUserComponent_Template_input_input_156_listener() { return ctx.filterPostcodes(); })(\"click\", function EditAppUserComponent_Template_input_click_156_listener() { return ctx.showPostcodeDropdown = true; });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(157, EditAppUserComponent_div_157_Template, 3, 1, \"div\", 52);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(158, EditAppUserComponent_div_158_Template, 5, 0, \"div\", 19);\r\n        i0.ɵɵtemplate(159, EditAppUserComponent_div_159_Template, 5, 0, \"div\", 19);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(160, \"div\", 70);\r\n        i0.ɵɵelementStart(161, \"div\", 58);\r\n        i0.ɵɵelementStart(162, \"div\", 59);\r\n        i0.ɵɵelementStart(163, \"div\", 60);\r\n        i0.ɵɵelementStart(164, \"h4\", 71);\r\n        i0.ɵɵtext(165, \"Add New University\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(166, \"button\", 72);\r\n        i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_166_listener() { return ctx.hideAddUniversityDegreeModal(\"University\"); });\r\n        i0.ɵɵtext(167, \"\\u00D7\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(168, \"div\", 63);\r\n        i0.ɵɵelement(169, \"input\", 73);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(170, \"div\", 65);\r\n        i0.ɵɵelementStart(171, \"button\", 74);\r\n        i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_171_listener() { return ctx.addUniversity(); });\r\n        i0.ɵɵtext(172, \"Add\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(173, \"button\", 75);\r\n        i0.ɵɵlistener(\"click\", function EditAppUserComponent_Template_button_click_173_listener() { return ctx.hideAddUniversityDegreeModal(\"University\"); });\r\n        i0.ɵɵtext(174, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n    } if (rf & 2) {\r\n        let tmp_7_0;\r\n        let tmp_8_0;\r\n        let tmp_9_0;\r\n        let tmp_10_0;\r\n        let tmp_12_0;\r\n        let tmp_13_0;\r\n        let tmp_26_0;\r\n        let tmp_30_0;\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Sharer\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"formGroup\", ctx.addNewAppUserForm);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title === \"Add\");\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title === \"Add\");\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title === \"Add\");\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.imageSrc);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx.addNewAppUserForm.get(\"U_dp\")) == null ? null : tmp_7_0.errors == null ? null : tmp_7_0.errors.fileSizeValidator);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.addNewAppUserForm.get(\"U_dp\")) == null ? null : tmp_8_0.errors == null ? null : tmp_8_0.errors.fileAspectRatioValidator);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isCropperVisible && ctx.imageName && !((tmp_9_0 = ctx.addNewAppUserForm.get(\"U_dp\")) == null ? null : tmp_9_0.errors == null ? null : tmp_9_0.errors.fileSizeValidator));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.isCropperVisible && !((tmp_10_0 = ctx.addNewAppUserForm.get(\"U_dp\")) == null ? null : tmp_10_0.errors == null ? null : tmp_10_0.errors.fileSizeValidator));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title === \"Add\");\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", (tmp_12_0 = ctx.addNewAppUserForm.get(\"U_profileAnonymous\")) == null ? null : tmp_12_0.value);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.isAliasCropperVisible && !((tmp_13_0 = ctx.addNewAppUserForm.get(\"U_aliasDp\")) == null ? null : tmp_13_0.errors == null ? null : tmp_13_0.errors.fileSizeValidator));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title == \"Edit\");\r\n        i0.ɵɵadvance(8);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.ActivitiesYouLike);\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.LikedSectors);\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.WorkType);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title == \"Edit\");\r\n        i0.ɵɵadvance(15);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.Genders);\r\n        i0.ɵɵadvance(8);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.Ethnicities);\r\n        i0.ɵɵadvance(8);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.Religions);\r\n        i0.ɵɵadvance(8);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.SexualOrientation);\r\n        i0.ɵɵadvance(8);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.hasADisability);\r\n        i0.ɵɵadvance(8);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.isThisGeneration);\r\n        i0.ɵɵadvance(7);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.FreeMeal);\r\n        i0.ɵɵadvance(7);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showTag && ((tmp_26_0 = ctx.addNewAppUserForm.get(\"U_institute\")) == null ? null : tmp_26_0.value));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"formControl\", ctx.universitySearchControl);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showDropdown && ctx.filteredUniversityOptions.length > 0);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showDropdown && ctx.filteredUniversityOptions.length === 0);\r\n        i0.ɵɵadvance(9);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showTagOfEducation && ((tmp_30_0 = ctx.addNewAppUserForm.get(\"U_education\")) == null ? null : tmp_30_0.value));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"formControl\", ctx.degreeSearchControl);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showDropdownOfEducation && ctx.filteredDegreeOptions.length > 0);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showDropdownOfEducation && ctx.filteredDegreeOptions.length === 0);\r\n        i0.ɵɵadvance(23);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedPostcode);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"formControl\", ctx.postcodeSearchControl);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showPostcodeDropdown && ctx.filteredPostcodes.length > 0);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title !== \"Edit\");\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title == \"Edit\");\r\n    } }, directives: [i7.SidebarComponent, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.FormGroupDirective, i8.NgIf, i2.DefaultValueAccessor, i2.NgControlStatus, i2.FormControlName, i8.NgForOf, i2.CheckboxControlValueAccessor, i2.RequiredValidator, i2.FormControlDirective, i9.ImageCropperComponent, i1.RouterLink], pipes: [i10.CamelCasePipe], styles: [\".container-scroll[_ngcontent-%COMP%] {\\n  height: 100%;\\n  \\n  overflow-y: auto;\\n  \\n}\\n\\n.card-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n}\\n\\n.addNewBtn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 20px;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n}\\n\\n\\n\\n@media screen and (min-width: 768px) and (max-width: 1024px) {\\n  .card-title[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n\\n  .card-text[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n}\\n\\n.preferences-row[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.1);\\n  border-radius: 10px;\\n  padding: 20px;\\n  background-color: #fff;\\n}\\n\\n.identity-row[_ngcontent-%COMP%] {\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.1);\\n  border-radius: 10px;\\n  padding: 20px;\\n  background-color: #fff;\\n}\\n\\n.heading[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  text-align: center;\\n  padding-top: 15px;\\n  padding-bottom: 15px;\\n  background-color: #ffffff;\\n  border-radius: 10px;\\n  font-weight: bold;\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  background-color: #ffffff;\\n  border-radius: 10px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 7px 25px 7px 15px;\\n  border: 1px solid #ddd;\\n  border-radius: 18px;\\n  background-color: #e6e6e6;\\n  cursor: pointer;\\n  margin: 5px;\\n  transition: background-color 0.3s;\\n  font-size: 0.9rem;\\n  margin-left: 20px;\\n}\\n\\n.tag.selected[_ngcontent-%COMP%] {\\n  background-color: #FF6B0B;\\n  color: #ffffff;\\n}\\n\\n.check-mark[_ngcontent-%COMP%] {\\n  display: none;\\n  position: absolute;\\n  right: 8px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  font-size: 14px;\\n  margin-left: 5px;\\n}\\n\\n.tag.selected[_ngcontent-%COMP%]   .check-mark[_ngcontent-%COMP%] {\\n  display: inline;\\n}\\n\\n.label[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: bold;\\n}\\n\\n\\n\\n.autocomplete-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.tag-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  width: 100%;\\n  padding-right: 30px;\\n}\\n\\n.selected-options[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  margin-bottom: 5px;\\n  padding: 9px 10px 9px 10px;\\n  border: 1px solid #ddd;\\n  border-radius: 20px;\\n  background-color: #e6e6e6;\\n  cursor: pointer;\\n  background-color: #FF6B0B;\\n  color: #ffffff;\\n}\\n\\n.selected-option[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n.remove-selected-option[_ngcontent-%COMP%] {\\n  margin-left: 0.2rem;\\n  font-size: 1rem;\\n  color: #fff;\\n  cursor: pointer;\\n  background: none;\\n  border: none;\\n}\\n\\n.remove-selected-option[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.tag-input[_ngcontent-%COMP%] {\\n  height: 48px;\\n  padding: 10px;\\n  font-size: 14px;\\n  border: 1px solid #ced4da;\\n  border-radius: 5px;\\n}\\n\\n.dropdown-list[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  z-index: 1000;\\n  width: 100%;\\n  background-color: #ffffff;\\n  border: 1px solid #ced4da;\\n  border-top: none;\\n  border-radius: 0 0 5px 5px;\\n  max-height: 200px;\\n  overflow-y: auto;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n.dropdown-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style-type: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.dropdown-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  padding: 12px 20px;\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n}\\n\\n.dropdown-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  background-color: #f0f0f0;\\n}\\n\\n.no-options-message[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  font-size: 14px;\\n  color: #777777;\\n}\\n\\n.tag-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #80bdff;\\n}\\n\\n.form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  padding: 0%;\\n  margin: 0%;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%] {\\n  width: 39px;\\n  height: 20px;\\n  margin-left: 10px;\\n  margin-top: 0%;\\n  margin-bottom: 4px;\\n  padding-top: 0%;\\n  background-color: #e9ecef;\\n  border: 1px solid #adb5bd;\\n  border-radius: 22px;\\n  appearance: none;\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n  cursor: pointer;\\n  outline: none;\\n  position: relative;\\n  transition: background-color 0.3s ease-in-out;\\n  order: 2;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #0d6efd;\\n  border-color: #0d6efd;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: block;\\n  width: 17px;\\n  height: 16px;\\n  background-color: #fff;\\n  border-radius: 50%;\\n  position: absolute;\\n  top: 1px;\\n  left: 1px;\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked::before {\\n  transform: translateX(18px);\\n}\\n\\n.form-check-label[_ngcontent-%COMP%] {\\n  order: 1;\\n}\\n\\n.form-check-input-head[_ngcontent-%COMP%] {\\n  width: 37px;\\n  height: 18px;\\n  margin-left: 10px;\\n  background-color: #e9ecef;\\n  border: 1px solid #adb5bd;\\n  border-radius: 22px;\\n  appearance: none;\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n  cursor: pointer;\\n  outline: none;\\n  position: relative;\\n  transition: background-color 0.3s ease-in-out;\\n  order: 2;\\n}\\n\\n.form-check-input-head[_ngcontent-%COMP%]:checked {\\n  background-color: #0d6dfd7b;\\n  border-color: #0d6dfd6c;\\n}\\n\\n.form-check-input-head[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: block;\\n  width: 16px;\\n  height: 14px;\\n  background-color: #fff;\\n  border-radius: 50%;\\n  position: absolute;\\n  top: 1px;\\n  left: 1px;\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.toggle-password[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  right: 25px;\\n  top: 45px;\\n}\\n\\n.imageNameBox[_ngcontent-%COMP%] {\\n  position: absolute;\\n  transform: translateY(-50%);\\n  top: 50%;\\n  right: 80px;\\n  max-width: 400px;\\n  max-height: 36px;\\n  background-color: white;\\n  outline: none;\\n  border: none;\\n}\\n\\n@media screen and (max-width: 767px) {\\n  .logo-input-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    right: 5px;\\n    max-width: 25px;\\n    min-height: 33px;\\n  }\\n\\n  .submit-btn[_ngcontent-%COMP%] {\\n    margin-bottom: 10px;\\n  }\\n\\n  .form-group[_ngcontent-%COMP%] {\\n    padding-left: 0px !important;\\n  }\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: red;\\n  font-size: 0.875em;\\n  margin-top: 0.25em;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  color: white;\\n  background-color: #007bff;\\n}\\n\\n.add-university-modal[_ngcontent-%COMP%], .add-degree-modal[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.5);\\n}\\n\\n.add-university-modal[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%], .add-degree-modal[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%] {\\n  margin-top: 0px !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"] });\r\n"]}, "metadata": {}, "sourceType": "module"}
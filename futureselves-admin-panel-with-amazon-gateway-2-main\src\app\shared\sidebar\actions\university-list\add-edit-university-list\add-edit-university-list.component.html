<app-sidebar>
    <div class="content-wrapper">
        <div class="row"> <!--Add new university form -->
            <div class="col-12 grid-margin stretch-card">
                <div class="card">
                    <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">{{title}}
                        University</div>
                    <div class="card-body">
                        <form [formGroup]="addNewUniversityForm" (ngSubmit)="onSubmit()" class="forms-sample">
                            <div class="row">
                              <div class="form-group col-lg-6">
                                <label class="required-field" for="INS_title">University Name</label>
                                <input type="text" class="form-control form-control-sm" id="INS_title"
                                       formControlName="INS_title" required [readonly]="isReadonly" placeholder="Enter Name" >
                              </div>
                          
                              <div class="form-group col-lg-6">
                                <label for="INS_dp">University Logo</label>
                                <div class="logo-input-container">
                                  <input type="file" class="form-control form-control-sm" formControlName="INS_dp"
                                         (change)="onFileSelected($event);" [readonly]="isReadonly" accept="image/*">
                                  <img *ngIf="imageSrc" [src]="imageSrc" alt="Employer Logo" class="img-preview">
                                </div>
                                <div *ngIf="addNewUniversityForm.get('INS_dp')?.errors?.fileSizeValidator" class="warning">
                                  The file size exceeds the 2 MB limit. Please select a smaller file.
                                </div>
                              </div>

                              <div class="form-group col-lg-6">
                                <label for="INS_service" class="required-field">University Service Description</label>
                                <textarea class="form-control form-control-sm" id="INS_service" formControlName="INS_service" required
                                    placeholder="Enter Description" [readOnly]="isReadonly" rows="3" cols="50"></textarea>
                              </div>
                          
                              <div class="form-group col-lg-6">
                                <label for="INS_website" class="required-field">Website</label>
                                <input type="text" class="form-control form-control-sm" id="INS_website"
                                       formControlName="INS_website" placeholder="Enter URL" [readonly]="isReadonly" required>
                                <div *ngIf="addNewUniversityForm.get('INS_website')?.invalid && addNewUniversityForm.get('INS_website')?.touched"
                                     class="warning">
                                  <!-- <div *ngIf="addNewUniversityForm.get('INS_website')?.errors?.required">Website is required.</div> -->
                                  <div *ngIf="addNewUniversityForm.get('INS_website')?.errors?.pattern">
                                    Please enter a valid URL starting with http:// or https://.
                                  </div>
                                </div>
                              </div>
                          
                              
                            </div>
                            <hr>
                            <h6 class="mb-3 py-2">Service Links</h6>                          
                            <div formArrayName="INS_serviceLinks">
                              <div *ngFor="let serviceLink of serviceLinks.controls; let i=index" [formGroupName]="i">
                                <span style="font-size: 0.9rem;">{{ serviceLinkHeadings[i] }}</span>
                                <!-- Row for Link and Description -->
                                <div class="row mt-2">
                                  <div class="form-group col-lg-6">
                                    <label [ngClass]="{'required-field': serviceLink.get(serviceLinkKeys[i])?.value}" style="font-size: 0.8rem;" [for]="serviceLinkKeys[i] + '_link'">Link</label>
                                    <input type="text" [id]="serviceLinkKeys[i] + '_link'" [formControlName]="serviceLinkKeys[i]"
                                           class="form-control form-control-sm" placeholder="Enter URL" [readonly]="isReadonly">
                                    <div *ngIf="serviceLink.get(serviceLinkKeys[i])?.invalid && serviceLink.get(serviceLinkKeys[i])?.touched" class="warning">
                                      <div *ngIf="serviceLink.get(serviceLinkKeys[i])?.errors?.pattern">
                                        Please enter a valid URL starting with http:// or https://.
                                      </div>
                                    </div>
                                  </div>
                                  <div class="form-group col-lg-6">
                                    <label [ngClass]="{'required-field': serviceLink.get(serviceLinkKeys[i])?.value}" style="font-size: 0.8rem;" for="SL_description">Description</label>
                                    <textarea id="SL_description" formControlName="SL_description" placeholder="Enter Description" class="form-control form-control-sm" rows="1"
                                              [readonly]="isReadonly"></textarea>
                                    <div *ngIf="serviceLink.get('SL_description')?.invalid" class="warning">
                                      <div *ngIf="serviceLink.get('SL_description')?.errors?.required">
                                        Description is required when a link is provided.
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            
                            
  <hr>
                            <div formArrayName="INS_HrInsights">
                                <h6 class="mb-3 py-2">Insights</h6>
                                <div *ngFor="let insight of insightFormArray.controls; let i=index" [formGroupName]="i" class="row mb-3">
                                   
                                    <div class="col-lg-11">
        
                                    <div class="row mr-0">
                                  <div class="form-group col-lg-3">
                                    <label class="subtitle" for="HRI_title">Insight Title</label>
                                    <input type="text" class="form-control form-control-sm" formControlName="HRI_title"  placeholder="Enter Title">
                                  </div>
                                  <div class="form-group col-lg-3">
                                    <label for="HRI_name" class="subtitle">Name</label>
                                    <input type="text" class="form-control form-control-sm" formControlName="HRI_name"  placeholder="Enter Name">
                                  </div>
                                  <div class="form-group col-lg-3">
                                    <label for="HRI_position" class="subtitle">Position</label>
                                    <input type="text" class="form-control form-control-sm" formControlName="HRI_position"  placeholder="Enter Position">
                                  </div>
                                  <div class="form-group col-lg-3">
                                    <label for="link" class="subtitle">Upload Insight</label>
                                    <input type="file" id="link" class="form-control form-control-sm"
                                           (change)="onAudioSelected($event, i)"
                                           accept="audio/*">
                                           <!-- <small><strong>Existing Audio Name: </strong> {{ existingAudioNames[i] }}</small><br> -->
                                          </div>
                                </div>
        
                            </div>
        
                                  <div class="col-lg-1 px-0 d-flex align-items-center btns">
                                    <button *ngIf="audioUrls[i]" type="button" class="btn insight-btn btn-sm mr-2" [ngClass]="{'btn-outline-primary': !isPlaying[i], 'btn-outline-secondary': isPlaying[i]}" (click)="toggleAudio(i)">
                                        <i class="fas" [ngClass]="isPlaying[i] ? 'fa-pause' : 'fa-play'"></i>
                                      </button>
                                    <button *ngIf="insightFormArray.length > 1" type="button" class="btn insight-btn btn-sm btn-outline-danger" (click)="removeInsight(i)">
                                      <i class="fas fa-minus icon"></i>
                                    </button>
        
                                    
                                      <audio #audioPlayer controls style="display: none;">
                                        <source [src]="audioUrls[i]" type="audio/mpeg">
                                        Your browser does not support the audio element.
                                      </audio>
                                  </div>
                                  
                                </div>
                                
                                <button type="button" *ngIf="insightFormArray.length < 3" class="btn btn-sm add-insight-btn btn-outline-primary" (click)="addInsight()">Add Insight
                                  <i class="fas fa-plus icon"></i>
                                </button>
                              </div>
                          
                            <div class="text-center mt-2">
                              <button *ngIf="!isReadonly" type="submit" class="btn btn-primary mr-2">Save</button>
                              <button class="btn btn-light" type="button" routerLink="/actions/universities">Cancel</button>
                            </div>
                          </form>
                          
                    </div>
                </div>
            </div>
        </div>
    </div>
</app-sidebar>
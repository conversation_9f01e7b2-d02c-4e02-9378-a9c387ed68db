import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';

@Injectable()
export class HttpConfigInterceptor implements HttpInterceptor {

  constructor(private router: Router, private toastr: ToastrService) {}

  intercept(request: HttpRequest<unknown>, next: HttpHand<PERSON>): Observable<HttpEvent<unknown>> {
    const baseUrlLondon = "https://wzbyh0qyz8.execute-api.eu-west-2.amazonaws.com/api";
    const baseUrlMumbai = "https://uy189rrz23.execute-api.ap-south-1.amazonaws.com/api";

    let xApiKey: string | null = null;

    if (request.url.startsWith(baseUrlLondon)) {
      xApiKey = "1OPJAOeHeF71kqwueQXmFvirKpCxG4f2f8XogFvb"; // London
    } else if (request.url.startsWith(baseUrlMumbai)) {
      xApiKey = "xPpKlU8Aoa120HtFhaHFg8ML6QtIFx0l6iSAR69b"; // Mumbai
    }else{
      xApiKey = "xPpKlU8Aoa120HtFhaHFg8ML6QtIFx0l6iSAR69b"; // Mumbai
    }

    const xAccessTtoken = sessionStorage.getItem('token');
    
    // Bypass for login requests
    if (request.url.includes('/login')) {
      return next.handle(request);
    }

    // Handle headers if token and API key are available
    let newHeaders = request.headers;
    if (xAccessTtoken && xApiKey) {
      newHeaders = newHeaders.append('Authorization', xAccessTtoken);
      newHeaders = newHeaders.append('x-api-key', xApiKey);
    } else {
      this.router.navigate(['/login']);
    }

    const authReq = request.clone({ headers: newHeaders });

    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          sessionStorage.clear();
          this.router.navigate(['/login']);
          this.toastr.error('Your session has expired. Please log in again.');
        }
        return throwError(() => error);
      })
    );
  }
}

{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function debounceTime(dueTime, scheduler = asyncScheduler) {\n  return operate((source, subscriber) => {\n    let activeTask = null;\n    let lastValue = null;\n    let lastTime = null;\n\n    const emit = () => {\n      if (activeTask) {\n        activeTask.unsubscribe();\n        activeTask = null;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n\n    function emitWhenIdle() {\n      const targetTime = lastTime + dueTime;\n      const now = scheduler.now();\n\n      if (now < targetTime) {\n        activeTask = this.schedule(undefined, targetTime - now);\n        subscriber.add(activeTask);\n        return;\n      }\n\n      emit();\n    }\n\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      lastValue = value;\n      lastTime = scheduler.now();\n\n      if (!activeTask) {\n        activeTask = scheduler.schedule(emitWhenIdle, dueTime);\n        subscriber.add(activeTask);\n      }\n    }, () => {\n      emit();\n      subscriber.complete();\n    }, undefined, () => {\n      lastValue = activeTask = null;\n    }));\n  });\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/operators/debounceTime.js"], "names": ["asyncScheduler", "operate", "OperatorSubscriber", "debounceTime", "dueTime", "scheduler", "source", "subscriber", "activeTask", "lastValue", "lastTime", "emit", "unsubscribe", "value", "next", "emitWhenIdle", "targetTime", "now", "schedule", "undefined", "add", "subscribe", "complete"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,kBAAT,QAAmC,sBAAnC;AACA,OAAO,SAASC,YAAT,CAAsBC,OAAtB,EAA+BC,SAAS,GAAGL,cAA3C,EAA2D;AAC9D,SAAOC,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;AACnC,QAAIC,UAAU,GAAG,IAAjB;AACA,QAAIC,SAAS,GAAG,IAAhB;AACA,QAAIC,QAAQ,GAAG,IAAf;;AACA,UAAMC,IAAI,GAAG,MAAM;AACf,UAAIH,UAAJ,EAAgB;AACZA,QAAAA,UAAU,CAACI,WAAX;AACAJ,QAAAA,UAAU,GAAG,IAAb;AACA,cAAMK,KAAK,GAAGJ,SAAd;AACAA,QAAAA,SAAS,GAAG,IAAZ;AACAF,QAAAA,UAAU,CAACO,IAAX,CAAgBD,KAAhB;AACH;AACJ,KARD;;AASA,aAASE,YAAT,GAAwB;AACpB,YAAMC,UAAU,GAAGN,QAAQ,GAAGN,OAA9B;AACA,YAAMa,GAAG,GAAGZ,SAAS,CAACY,GAAV,EAAZ;;AACA,UAAIA,GAAG,GAAGD,UAAV,EAAsB;AAClBR,QAAAA,UAAU,GAAG,KAAKU,QAAL,CAAcC,SAAd,EAAyBH,UAAU,GAAGC,GAAtC,CAAb;AACAV,QAAAA,UAAU,CAACa,GAAX,CAAeZ,UAAf;AACA;AACH;;AACDG,MAAAA,IAAI;AACP;;AACDL,IAAAA,MAAM,CAACe,SAAP,CAAiB,IAAInB,kBAAJ,CAAuBK,UAAvB,EAAoCM,KAAD,IAAW;AAC3DJ,MAAAA,SAAS,GAAGI,KAAZ;AACAH,MAAAA,QAAQ,GAAGL,SAAS,CAACY,GAAV,EAAX;;AACA,UAAI,CAACT,UAAL,EAAiB;AACbA,QAAAA,UAAU,GAAGH,SAAS,CAACa,QAAV,CAAmBH,YAAnB,EAAiCX,OAAjC,CAAb;AACAG,QAAAA,UAAU,CAACa,GAAX,CAAeZ,UAAf;AACH;AACJ,KAPgB,EAOd,MAAM;AACLG,MAAAA,IAAI;AACJJ,MAAAA,UAAU,CAACe,QAAX;AACH,KAVgB,EAUdH,SAVc,EAUH,MAAM;AAChBV,MAAAA,SAAS,GAAGD,UAAU,GAAG,IAAzB;AACH,KAZgB,CAAjB;AAaH,GApCa,CAAd;AAqCH", "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nexport function debounceTime(dueTime, scheduler = asyncScheduler) {\n    return operate((source, subscriber) => {\n        let activeTask = null;\n        let lastValue = null;\n        let lastTime = null;\n        const emit = () => {\n            if (activeTask) {\n                activeTask.unsubscribe();\n                activeTask = null;\n                const value = lastValue;\n                lastValue = null;\n                subscriber.next(value);\n            }\n        };\n        function emitWhenIdle() {\n            const targetTime = lastTime + dueTime;\n            const now = scheduler.now();\n            if (now < targetTime) {\n                activeTask = this.schedule(undefined, targetTime - now);\n                subscriber.add(activeTask);\n                return;\n            }\n            emit();\n        }\n        source.subscribe(new OperatorSubscriber(subscriber, (value) => {\n            lastValue = value;\n            lastTime = scheduler.now();\n            if (!activeTask) {\n                activeTask = scheduler.schedule(emitWhenIdle, dueTime);\n                subscriber.add(activeTask);\n            }\n        }, () => {\n            emit();\n            subscriber.complete();\n        }, undefined, () => {\n            lastValue = activeTask = null;\n        }));\n    });\n}\n"]}, "metadata": {}, "sourceType": "module"}
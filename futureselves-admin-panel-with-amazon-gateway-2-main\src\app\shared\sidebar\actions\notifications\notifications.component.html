<app-sidebar>
<div class="content-wrapper">
    <div class="row"> <!--Add new Grad scheme form -->
        <div class="col-12 grid-margin stretch-card">
            <div class="card">
                <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">Notifications
                </div>
                <div class="card-body">
                    <form class="forms-sample" (ngSubmit)="generateNotification()" [formGroup]="addNotificationForm">

                        <div class="row">

                            <div class="form-group col-lg-6">
                                <label for="UN_notificationType" >Users</label>
                                <select type="text" class="form-control form-control-sm"
                                    formControlName="UN_notificationType">
                                    <option  disabled [selected]="true">All Users</option>
                                    
                                </select>
                            </div>

                            <div class="form-group col-lg-6">
                                <label for="UN_title" class="required-field">Notification Title</label>
                                <input type="text" class="form-control form-control-sm" formControlName="UN_title"
                                    required [readonly]="isReadonly" />
                                <div class="invalid-feedback">
                                    Please enter a sector name.
                                </div>
                            </div>


                        </div>

                        <div class="row">
                            <div class="form-group col-lg-6">
                                <label for="UN_text" class="required-field">Notification Body</label>
                                <textarea class="form-control form-control-sm" formControlName="UN_text" required
                                    [readonly]="isReadonly" rows="5" cols="50"></textarea>
                                <div class="invalid-feedback">
                                    Please enter a sector description.
                                </div>
                            </div>



                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary mr-2">Generate Notifications</button>
                            <button type="button" class="btn btn-danger px-5" (click)="clearForm()">Clear</button>
                            <!-- <button class="btn btn-light" routerLink="/actions/sectors">Cancel</button> -->
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
</app-sidebar>
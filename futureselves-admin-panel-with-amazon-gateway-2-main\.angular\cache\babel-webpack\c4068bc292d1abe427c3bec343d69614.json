{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { Validators } from '@angular/forms';\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\nimport * as i3 from \"ngx-spinner\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../../../sidebar.component\";\nconst _c0 = [\"audioPlayer\"];\n\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\n\nfunction AddEditOpportunityComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelementStart(1, \"input\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function AddEditOpportunityComponent_div_13_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return ctx_r19.onTypeChange($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 57);\n    i0.ɵɵtext(3, \"Job\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.selectedType)(\"ngModelOptions\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\n\nfunction AddEditOpportunityComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelementStart(1, \"input\", 58);\n    i0.ɵɵlistener(\"ngModelChange\", function AddEditOpportunityComponent_div_14_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return ctx_r21.onTypeChange($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 59);\n    i0.ɵɵtext(3, \"Grad Scheme\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.selectedType)(\"ngModelOptions\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\n\nfunction AddEditOpportunityComponent_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const sector_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", sector_r23.IN_id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(sector_r23.IN_name);\n  }\n}\n\nfunction AddEditOpportunityComponent_option_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const roles_r24 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵproperty(\"value\", roles_r24.RO_id)(\"selected\", roles_r24.RO_id === ((tmp_1_0 = ctx_r3.jobForm.get(\"JB_roleId\")) == null ? null : tmp_1_0.value));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", roles_r24.RO_title, \"\");\n  }\n}\n\nfunction AddEditOpportunityComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1, \" Please select a sector first. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" Error: Word limit exceeded!(10 Words) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" Error: Word limit exceeded!(10 Words) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_input_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 64);\n    i0.ɵɵlistener(\"change\", function AddEditOpportunityComponent_input_45_Template_input_change_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return ctx_r25.updateStartDateMin();\n    })(\"input\", function AddEditOpportunityComponent_input_45_Template_input_input_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return ctx_r27.checkInvalidDate(\"JB_applicationDeadline\");\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"min\", ctx_r7.minDate)(\"readonly\", ctx_r7.isReadonly);\n  }\n}\n\nfunction AddEditOpportunityComponent_input_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 65);\n  }\n}\n\nfunction AddEditOpportunityComponent_input_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 66);\n    i0.ɵɵlistener(\"input\", function AddEditOpportunityComponent_input_50_Template_input_input_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return ctx_r28.checkInvalidDate(\"JB_startDate\");\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"min\", ctx_r9.minStartDate)(\"readonly\", ctx_r9.isReadonly);\n  }\n}\n\nfunction AddEditOpportunityComponent_input_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 67);\n  }\n}\n\nfunction AddEditOpportunityComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" Error: Word limit exceeded!(10 Words) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_div_77_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" Error: Character limit exceeded! (500 Characters) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AddEditOpportunityComponent_div_77_div_3_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.descriptionCharCount, \"/500\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r12.jobForm.get(\"JB_description\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.characterLimitExceeded);\n  }\n}\n\nfunction AddEditOpportunityComponent_div_82_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" Error: Character limit exceeded! (500 Characters) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_div_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AddEditOpportunityComponent_div_82_div_3_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.requirementsCharCount, \"/500\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r13.jobForm.get(\"JB_requirements\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.characterLimitExceeded);\n  }\n}\n\nfunction AddEditOpportunityComponent_div_87_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" Error: Character limit exceeded! (500 Characters) \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementStart(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AddEditOpportunityComponent_div_87_div_3_Template, 2, 0, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r14.hrtTipsCharCount, \"/500\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r14.jobForm.get(\"JB_hrtTips\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.characterLimitExceeded);\n  }\n}\n\nfunction AddEditOpportunityComponent_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1, \" Hours must be between 0 and 24. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_div_101_input_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 88);\n    i0.ɵɵlistener(\"change\", function AddEditOpportunityComponent_div_101_input_18_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const i_r34 = i0.ɵɵnextContext().index;\n      const ctx_r40 = i0.ɵɵnextContext();\n      return ctx_r40.onAudioSelected($event, i_r34);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_div_101_input_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"input\", 89);\n  }\n\n  if (rf & 2) {\n    const i_r34 = i0.ɵɵnextContext().index;\n    const ctx_r36 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r36.audioNames[i_r34]);\n  }\n}\n\nconst _c2 = function (a0, a1, a2) {\n  return {\n    \"btn-outline-primary\": a0,\n    \"btn-outline-secondary\": a1,\n    \"mt-2\": a2\n  };\n};\n\nfunction AddEditOpportunityComponent_div_101_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function AddEditOpportunityComponent_div_101_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const i_r34 = i0.ɵɵnextContext().index;\n      const ctx_r44 = i0.ɵɵnextContext();\n      return ctx_r44.toggleAudio(i_r34);\n    });\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r34 = i0.ɵɵnextContext().index;\n    const ctx_r37 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(2, _c2, !ctx_r37.isPlaying[i_r34], ctx_r37.isPlaying[i_r34], ctx_r37.isReadonly));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r37.isPlaying[i_r34] ? \"fa-pause\" : \"fa-play\");\n  }\n}\n\nfunction AddEditOpportunityComponent_div_101_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function AddEditOpportunityComponent_div_101_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const i_r34 = i0.ɵɵnextContext().index;\n      const ctx_r48 = i0.ɵɵnextContext();\n      return ctx_r48.removeInsight(i_r34);\n    });\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelementStart(1, \"div\", 70);\n    i0.ɵɵelementStart(2, \"div\", 71);\n    i0.ɵɵelementStart(3, \"div\", 72);\n    i0.ɵɵelementStart(4, \"label\", 73);\n    i0.ɵɵtext(5, \"Insight Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 72);\n    i0.ɵɵelementStart(8, \"label\", 75);\n    i0.ɵɵtext(9, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 72);\n    i0.ɵɵelementStart(12, \"label\", 77);\n    i0.ɵɵtext(13, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 72);\n    i0.ɵɵelementStart(16, \"label\", 79);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, AddEditOpportunityComponent_div_101_input_18_Template, 1, 0, \"input\", 80);\n    i0.ɵɵtemplate(19, AddEditOpportunityComponent_div_101_input_19_Template, 1, 1, \"input\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 82);\n    i0.ɵɵtemplate(21, AddEditOpportunityComponent_div_101_button_21_Template, 2, 6, \"button\", 83);\n    i0.ɵɵtemplate(22, AddEditOpportunityComponent_div_101_button_22_Template, 2, 0, \"button\", 84);\n    i0.ɵɵelementStart(23, \"audio\", 85, 86);\n    i0.ɵɵelement(25, \"source\", 87);\n    i0.ɵɵtext(26, \" Your browser does not support the audio element. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r34 = ctx.index;\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroupName\", i_r34);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"readOnly\", ctx_r16.isReadonly);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"readOnly\", ctx_r16.isReadonly);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"readOnly\", ctx_r16.isReadonly);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(!ctx_r16.isReadonly ? \"Upload Insight\" : \"Uploaded Insight\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.title !== \"View\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.title === \"View\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.audioUrls[i_r34]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.insightFormArray.length > 1 && !ctx_r16.isReadonly);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r16.audioUrls[i_r34], i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction AddEditOpportunityComponent_button_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function AddEditOpportunityComponent_button_102_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return ctx_r51.addInsight();\n    });\n    i0.ɵɵtext(1, \"Add Insight \");\n    i0.ɵɵelement(2, \"i\", 95);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditOpportunityComponent_button_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 96);\n    i0.ɵɵtext(1, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c3 = function (a0) {\n  return {\n    \"required-field\": a0\n  };\n};\n\nconst _c4 = function (a0) {\n  return {\n    \"readOnlyColor\": a0\n  };\n};\n\nconst _c5 = function (a0) {\n  return {\n    \"text-dark\": a0\n  };\n};\n\nfunction wordLimitValidator(maxWords) {\n  return control => {\n    if (control.value) {\n      const words = control.value.trim().split(/\\s+/);\n\n      if (words.length > maxWords) {\n        console.log('maxwords', maxWords);\n        return {\n          wordLimitExceeded: true,\n          wordCount: words.length\n        };\n      }\n    }\n\n    return null;\n  };\n}\n\nfunction nonNegativeValidator(control) {\n  // console.log('Control : ', control);\n  const value = control.value;\n\n  if (value < 0 || value > 24) {\n    return {\n      negativeValue: true\n    };\n  }\n\n  return null;\n}\n\nexport class AddEditOpportunityComponent {\n  constructor(formBuilder, dataTransferService, ngxSpinnerService, router, toastr, datePipe, route) {\n    var _a, _b;\n\n    this.formBuilder = formBuilder;\n    this.dataTransferService = dataTransferService;\n    this.ngxSpinnerService = ngxSpinnerService;\n    this.router = router;\n    this.toastr = toastr;\n    this.datePipe = datePipe;\n    this.route = route;\n    this.p = 1;\n    this.viewInsight = true;\n    this.title = 'Add New';\n    this.isReadonly = false;\n    this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\n    this.invalidDates = new Set();\n    this.selectedType = 'job'; // Default value is 'job'\n\n    this.descriptionCharCount = 0;\n    this.requirementsCharCount = 0;\n    this.hrtTipsCharCount = 0;\n    this.audioFileUrls = []; // Array to store the generated URLs\n\n    this.audioFiles = [];\n    this.characterCount = 0;\n    this.isPlaying = []; // Array to track playback state\n\n    this.audioUrls = []; // Array to store audio URLs\n\n    this.hideOppType = false;\n    this.audioNames = [];\n    this.route.queryParams.subscribe(params => {\n      if (params) {\n        this.CO_id = params['CO_id'];\n        console.log(this.CO_id);\n      } else {\n        this.router.navigate(['/actions/employer-opportunities']);\n      }\n    });\n    const state = (_b = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras) === null || _b === void 0 ? void 0 : _b.state; //State values\n\n    console.log('State Values', state);\n\n    if (state) {\n      this.viewInsight = state === null || state === void 0 ? void 0 : state.viewInsight;\n      this.title = state === null || state === void 0 ? void 0 : state.title;\n      this.opportunityData = state === null || state === void 0 ? void 0 : state.opportunityData; // this.JB_type=state?.opportunityData.JB_type;\n\n      console.log('this.opportunityData: ', this.opportunityData);\n    } else {\n      this.router.navigate([`actions/employer-opportunities`]);\n    }\n\n    this.minDate = new Date().toISOString().split('T')[0];\n    this.checkMinDate = this.minDate;\n    this.jobForm = this.formBuilder.group( //Form to add new scheme\n    {\n      JB_companyId: [''],\n      JB_companyLogo: [''],\n      JB_applicationDeadline: ['', [Validators.required]],\n      JB_startDate: ['', [Validators.required]],\n      JB_jobTitle: ['', [Validators.required, wordLimitValidator(10)]],\n      JB_description: ['', [Validators.required, this.characterLimitValidator(200)]],\n      JB_department: ['', [Validators.required, wordLimitValidator(10)]],\n      JB_hours: ['', [Validators.required, nonNegativeValidator]],\n      JB_hrtTips: ['', [Validators.required, this.characterLimitValidator(200)]],\n      JB_roleId: ['', [Validators.required]],\n      JB_location: ['', [Validators.required, wordLimitValidator(10)]],\n      JB_salary: ['', [Validators.required]],\n      JB_modeOfWork: ['', [Validators.required, wordLimitValidator(10)]],\n      JB_requirements: ['', [Validators.required, this.characterLimitValidator(200)]],\n      JB_applyLink: ['', [Validators.required]],\n      JB_type: ['', Validators.required],\n      JB_sectorId: [''],\n      JB_insights: this.formBuilder.array([this.createInsight()])\n    });\n    this.onTypeChange(this.selectedType);\n  }\n\n  ngOnInit() {\n    var _a, _b;\n\n    this.getCompanyById(this.CO_id);\n    this.getSectorTitles(); // this.getAllRoleBySectorId(this.sectorID);\n\n    this.minStartDate = this.minDate;\n\n    if (this.selectedType === 'job' && !((_a = this.jobForm.get('JB_sectorId')) === null || _a === void 0 ? void 0 : _a.value)) {\n      (_b = this.jobForm.get('JB_roleId')) === null || _b === void 0 ? void 0 : _b.disable();\n    }\n\n    if (this.title === 'Edit') {\n      this.populateForm(this.opportunityData);\n    }\n\n    if (this.title === 'View') {\n      this.makeDataReadOnly(this.opportunityData);\n    }\n  }\n\n  makeDataReadOnly(data) {\n    var _a, _b, _c;\n\n    (_a = this.jobForm.get('JB_sectorId')) === null || _a === void 0 ? void 0 : _a.disable();\n    (_b = this.jobForm.get('JB_roleId')) === null || _b === void 0 ? void 0 : _b.disable();\n    (_c = this.jobForm.get('JB_modeOfWork')) === null || _c === void 0 ? void 0 : _c.disable();\n    this.hideOppType = true;\n    this.getAllRole(data.JB_sectorId);\n    this.isReadonly = true;\n    this.jobForm.patchValue({\n      JB_jobTitle: data.JB_jobTitle,\n      JB_description: data.JB_description,\n      JB_department: data.JB_department,\n      JB_hours: data.JB_hours,\n      JB_hrtTips: data.JB_hrtTips,\n      JB_roleId: data.JB_roleId,\n      JB_location: data.JB_location,\n      JB_salary: data.JB_salary,\n      JB_modeOfWork: data.JB_modeOfWork,\n      JB_requirements: data.JB_requirements,\n      JB_applyLink: data.JB_applyLink,\n      JB_type: data.JB_type,\n      JB_sectorId: data.JB_sectorId,\n      JB_insights: data.JB_insights,\n      JB_applicationDeadline: this.datePipe.transform(data.JB_applicationDeadline, 'MMM d,y'),\n      JB_startDate: this.datePipe.transform(data.JB_startDate, 'MMM d,y'),\n      JB_companyId: this.CO_id,\n      CO_sectorId: this.sectorID,\n      JB_companyLogo: this.companyLogo\n    });\n    const hrInsightsArray = this.jobForm.get('JB_insights');\n    hrInsightsArray.clear();\n    this.audioNames = data.JB_insights.map(insight => insight.HRI_link.split('/').pop());\n    data.JB_insights.forEach(insight => {\n      const insightFormGroup = this.formBuilder.group({\n        HRI_title: [insight.HRI_title],\n        HRI_name: [insight.HRI_name],\n        HRI_position: [insight.HRI_position],\n        HRI_link: [insight.HRI_link]\n      });\n      hrInsightsArray.push(insightFormGroup);\n    });\n    this.audioUrls = data.JB_insights.map(insight => insight.HRI_link);\n    this.isPlaying = new Array(data.JB_insights.length).fill(false);\n\n    if (data.JB_type === 0) {\n      this.selectedType = 'job';\n    } else {\n      this.selectedType = 'jobScheme';\n    }\n  }\n\n  populateForm(data) {\n    var _a, _b, _c, _d;\n\n    this.hideOppType = true;\n    console.log('Data to patch to edit', data);\n    this.getAllRole(data.JB_sectorId);\n    this.jobForm.patchValue({\n      JB_jobTitle: data.JB_jobTitle,\n      JB_description: data.JB_description,\n      JB_department: data.JB_department,\n      JB_hours: data.JB_hours,\n      JB_hrtTips: data.JB_hrtTips,\n      JB_roleId: data.JB_roleId,\n      JB_location: data.JB_location,\n      JB_salary: data.JB_salary,\n      JB_modeOfWork: data.JB_modeOfWork,\n      JB_requirements: data.JB_requirements,\n      JB_applyLink: data.JB_applyLink,\n      JB_type: data.JB_type,\n      JB_sectorId: data.JB_sectorId,\n      JB_insights: data.JB_insights,\n      JB_applicationDeadline: data.JB_applicationDeadline,\n      JB_startDate: data.JB_startDate,\n      JB_companyId: this.CO_id,\n      CO_sectorId: this.sectorID,\n      JB_companyLogo: this.companyLogo\n    });\n    const hrInsightsArray = this.jobForm.get('JB_insights');\n    hrInsightsArray.clear();\n    data.JB_insights.forEach(insight => {\n      const insightFormGroup = this.formBuilder.group({\n        HRI_title: [insight.HRI_title],\n        HRI_name: [insight.HRI_name],\n        HRI_position: [insight.HRI_position],\n        HRI_link: [insight.HRI_link]\n      });\n      hrInsightsArray.push(insightFormGroup);\n    }); // Initialize audio URLs and playback states\n    // this.audioUrls = data.JB_insights.map((insight: any) => insight.HRI_link);\n    // this.isPlaying = new Array(data.JB_insights.length).fill(false);\n\n    this.audioUrls = data.JB_insights.map(insight => insight.HRI_link);\n    this.isPlaying = new Array(data.JB_insights.length).fill(false);\n    const JB_type = (_a = this.opportunityData) === null || _a === void 0 ? void 0 : _a.JB_type;\n\n    if (JB_type === 0) {\n      if ((_b = this.jobForm.get('JB_sectorId')) === null || _b === void 0 ? void 0 : _b.value) {\n        (_c = this.jobForm.get('JB_roleId')) === null || _c === void 0 ? void 0 : _c.enable();\n      } else {\n        (_d = this.jobForm.get('JB_roleId')) === null || _d === void 0 ? void 0 : _d.reset({\n          value: null,\n          disabled: true\n        });\n      }\n\n      this.selectedType = 'job';\n    } else {\n      this.selectedType = 'jobScheme';\n    }\n  }\n\n  get insightFormArray() {\n    return this.jobForm.get('JB_insights');\n  }\n\n  characterLimitValidator(limit) {\n    return control => {\n      if (control.value && control.value.length > limit) {\n        return {\n          characterLimitExceeded: true\n        };\n      }\n\n      return null;\n    };\n  }\n\n  onInput(field) {\n    const control = this.jobForm.get(field);\n\n    if (control) {\n      const value = control.value || '';\n\n      switch (field) {\n        case 'JB_description':\n          this.descriptionCharCount = value.length;\n          break;\n\n        case 'JB_requirements':\n          this.requirementsCharCount = value.length;\n          break;\n\n        case 'JB_hrtTips':\n          this.hrtTipsCharCount = value.length;\n          break;\n      }\n\n      control.updateValueAndValidity(); // Trigger validation\n    }\n  }\n\n  onTypeChange(value) {\n    var _a;\n\n    this.selectedType = value;\n    this.jobForm.reset();\n    const insightDiv = document.getElementById('link');\n\n    if (insightDiv) {\n      insightDiv.value = '';\n    }\n\n    this.audioUrls = [];\n    this.audioFiles = [];\n    this.isPlaying = [];\n\n    if (this.audioPlayers) {\n      this.audioPlayers.forEach(audioPlayer => {\n        audioPlayer.nativeElement.pause();\n        audioPlayer.nativeElement.currentTime = 0;\n      });\n    }\n\n    (_a = this.jobForm.get('JB_roleId')) === null || _a === void 0 ? void 0 : _a.reset({\n      value: null,\n      disabled: true\n    });\n    const sectorControl = this.jobForm.get('JB_sectorId');\n    const roleControl = this.jobForm.get('JB_roleId');\n\n    if (value === 'job') {\n      sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.setValidators([Validators.required]);\n      roleControl === null || roleControl === void 0 ? void 0 : roleControl.setValidators([Validators.required]);\n    } else {\n      sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.clearValidators();\n      roleControl === null || roleControl === void 0 ? void 0 : roleControl.clearValidators();\n    }\n\n    sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.updateValueAndValidity();\n    roleControl === null || roleControl === void 0 ? void 0 : roleControl.updateValueAndValidity();\n  }\n\n  onChangeIndustry(event) {\n    var _a;\n\n    const CO_sectorId = event.target.value;\n    (_a = this.jobForm.get('JB_roleId')) === null || _a === void 0 ? void 0 : _a.enable();\n    this.getAllRole(CO_sectorId);\n  }\n\n  updateStartDateMin() {\n    var _a;\n\n    const deadline = (_a = this.jobForm.get('JB_applicationDeadline')) === null || _a === void 0 ? void 0 : _a.value;\n\n    if (deadline) {\n      const minStartDate = new Date(deadline);\n      minStartDate.setDate(minStartDate.getDate() + 1); // Add one day to the deadline\n\n      this.minStartDate = minStartDate.toISOString().split('T')[0]; // After updating minimum start date, check the validity of the start date\n\n      this.checkInvalidDate('JB_startDate');\n    }\n  }\n\n  checkInvalidDate(controlName) {\n    const control = this.jobForm.get(controlName);\n\n    if (control && control.value && isNaN(Date.parse(control.value))) {\n      this.invalidDates.add(controlName);\n    } else {\n      this.invalidDates.delete(controlName);\n    }\n  }\n\n  getSectorTitles() {\n    // In Add company form - To sector dropdown\n    this.dataTransferService.getSectorTitles().subscribe(res => {\n      if (res.statusCode = 200) {\n        this.SectorList = res.data;\n        console.log('Sectors', this.SectorList);\n      } else {\n        console.error('Failed to fetch sectors. Status:', res.status);\n      }\n    });\n  }\n\n  formatDate(date) {\n    // Check if the date is valid\n    if (!isNaN(date.getTime())) {\n      return this.datePipe.transform(date, 'MMM dd yyyy');\n    } else {\n      return '';\n    }\n  }\n\n  getCompanyById(companyId) {\n    this.dataTransferService.getCompanyById(companyId).subscribe(res => {\n      this.companyLogo = res.CO_logo;\n      this.sectorID = res.CO_sectorId;\n      console.log('getCompanyById', res);\n    }, error => {\n      console.log(\"Error\", error);\n      this.toastr.error(\"Unable to fetch data\");\n    });\n  }\n\n  getAllRole(CO_sectorId) {\n    console.log('sectorId : ', CO_sectorId);\n    this.dataTransferService.getAllRoleBySectorId(CO_sectorId).subscribe({\n      next: res => {\n        if (res.statusCode === 200) {\n          this.RoleList = res.data;\n          console.log('RoleList : ', this.RoleList);\n        } else {\n          console.error('Failed to fetch role. Status:', res.status);\n        }\n      },\n      error: error => {\n        this.ngxSpinnerService.hide('globalSpinner');\n        console.error('Error occurred while fetching roles:', error);\n      }\n    });\n  } // getAllRoleBySectorId(sectorID: any) {\n  //   this.dataTransferService.getAllRoleBySectorId(sectorID).subscribe({\n  //     next: (res: any) => {\n  //       if (res.statusCode === 200) {\n  //         this.RoleList = res.data;\n  //         this.roleId = this.RoleList.RO_id;\n  //         console.log('RoleList : ', this.RoleList);\n  //         this.ngxSpinnerService.hide('globalSpinner');\n  //       } else {\n  //         this.ngxSpinnerService.hide('globalSpinner');\n  //         console.error('Failed to fetch role. Status:', res.status);\n  //       }\n  //     },\n  //     error: (error: any) => {\n  //       this.ngxSpinnerService.hide('globalSpinner');\n  //       console.error('Error occurred while fetching roles:', error);\n  //     },\n  //   });\n  // }\n\n\n  showDeleteModal(scheme) {\n    console.log('Delete Scheme', scheme);\n    this.deleteId = scheme.JB_id;\n  } // onFileSelected(event: any) {\n  //   //to preview image and take file in imageName to pass in upload api\n  //   if (event.target.files.length === 0) {\n  //     // Reset both imageName and imageSrc when no file is selected\n  //     this.imageName = null;\n  //     this.imageSrc = null;\n  //     return;\n  //   }\n  //   this.imageName = event.target.files[0];\n  //   const fileType = this.imageName.type.split('/')[0];\n  //   if (fileType !== 'image') {\n  //     event.target.value = '';\n  //     this.toastr.info('Please select an image file.');\n  //     this.imageName = null;\n  //     this.imageSrc = null;\n  //     return;\n  //   }\n  //   if (this.imageName && fileType == 'image') {\n  //     const reader = new FileReader();\n  //     reader.onload = (e) => {\n  //       this.imageSrc = e.target?.result as string | ArrayBuffer;\n  //     };\n  //     reader.readAsDataURL(this.imageName);\n  //   } else {\n  //     this.imageSrc = null; // Reset imageSrc if no file selected\n  //   }\n  //   console.log('imageName', this.imageName);\n  // }\n  // uploadLogoUrl() {\n  //   //To upload logo from add new company form\n  //   if (!this.imageName) {\n  //     // this.toastr.error('Please select an image.');\n  //     return;\n  //   }\n  //   console.log('image', this.imageName);\n  //   this.dataTransferService\n  //     .uploadurl(this.imageName)\n  //     .subscribe((res: any) => {});\n  // }\n  // getAllJobByJobId(scheme: any, title: string) {\n  //   this.title = title;\n  //   if (this.title == 'View') {\n  //     this.isReadonly = true;\n  //     this.viewInsight = false;\n  //     this.ngxSpinnerService.show('globalSpinner');\n  //     this.dataTransferService.getAllJobByJobId(scheme.JB_id).subscribe({\n  //       next: (res: any) => {\n  //         if (res.statusCode === 200 && res.data && res.data.length > 0) {\n  //           this.ngxSpinnerService.hide('globalSpinner');\n  //           const formatDate = (timestamp: number): string => {\n  //             const date = new Date(timestamp);\n  //             const year = date.getFullYear();\n  //             const month = String(date.getMonth() + 1).padStart(2, '0');\n  //             const day = String(date.getDate()).padStart(2, '0');\n  //             return `${month}-${day}-${year}`;\n  //           };\n  //           // Format dates in the response\n  //           const formattedPayload = res.data.map((item: any) => ({\n  //             ...item,\n  //             JB_startDate: formatDate(item.JB_startDate),\n  //             JB_applicationDeadline: formatDate(item.JB_applicationDeadline),\n  //           }));\n  //           this.jobForm.patchValue(formattedPayload[0]);\n  //           this.jobForm.get('JB_roleId')?.disable();\n  //           this.jobForm.get('JB_modeOfWork')?.disable();\n  //           // Log the updated JobList\n  //           console.log('Full JobList', formattedPayload[0]);\n  //         } else {\n  //           this.ngxSpinnerService.hide('globalSpinner');\n  //           console.error('Error: Invalid response or no data found.');\n  //         }\n  //       },\n  //       error: (error: any) => {\n  //         this.ngxSpinnerService.hide('globalSpinner');\n  //         console.error('Error occurred while fetching roles:', error);\n  //       },\n  //     });\n  //     // this.jobForm.patchValue(scheme);\n  //   }\n  // }\n  // editId: any;\n  // editScheme(scheme: any, title: string) {\n  //   this.isReadonly = false;\n  //   this.title = title;\n  //   this.jobForm.get('JB_roleId')?.enable();\n  //   this.jobForm.get('JB_modeOfWork')?.enable();\n  //   this.viewInsight = true;\n  //   this.editId = scheme.JB_id;\n  //   this.toEditInsight = scheme.JB_insights;\n  //   const formatDate = (timestamp: number): string => {\n  //     const date = new Date(timestamp);\n  //     const year = date.getFullYear();\n  //     const month = String(date.getMonth() + 1).padStart(2, '0');\n  //     const day = String(date.getDate()).padStart(2, '0');\n  //     return `${month}-${day}-${year}`;\n  //   };\n  //   const formattedPayload = {\n  //     ...scheme,\n  //     JB_startDate: formatDate(scheme.JB_startDate),\n  //     JB_applicationDeadline: formatDate(scheme.JB_applicationDeadline),\n  //   };\n  //   // this.selectedAudioFilethis.jobForm.get('JB_insights')?.setValue(this.selectedAudioFile);\n  //   this.jobForm.patchValue(formattedPayload);\n  // }\n\n\n  createInsight() {\n    return this.formBuilder.group({\n      HRI_title: [''],\n      HRI_name: [''],\n      HRI_position: [''],\n      HRI_link: [null]\n    });\n    this.isPlaying.push(false); // Initialize playback state for new row\n\n    this.audioUrls.push(''); // Initialize empty URL for new row\n  }\n\n  addInsight() {\n    this.insightFormArray.push(this.createInsight());\n  }\n\n  removeInsight(index) {\n    this.insightFormArray.removeAt(index);\n    this.audioUrls.splice(index, 1); // Remove URL for deleted row\n\n    this.isPlaying.splice(index, 1); // Remove playback state for deleted row\n\n    this.audioFiles.splice(index, 1); // Remove file object for deleted row\n  }\n\n  toggleAudio(index) {\n    const audioElements = this.audioPlayers.toArray();\n    const audioElement = audioElements[index].nativeElement; // Stop all other audio\n\n    this.stopAllAudio(index); // Toggle play/pause for the current audio element\n\n    if (this.isPlaying[index]) {\n      audioElement.pause();\n    } else {\n      audioElement.src = this.audioUrls[index];\n      audioElement.play();\n    } // Update the playback state\n\n\n    this.isPlaying[index] = !this.isPlaying[index];\n  }\n\n  stopAllAudio(currentIndex) {\n    this.audioPlayers.forEach((audioPlayer, index) => {\n      if (index !== currentIndex) {\n        audioPlayer.nativeElement.pause();\n        this.isPlaying[index] = false;\n      }\n    });\n  }\n\n  stopAudio(index) {\n    const audioElements = this.audioPlayers.toArray();\n    const audioElement = audioElements[index].nativeElement;\n    audioElement.pause();\n    audioElement.currentTime = 0; // Reset to the beginning\n\n    this.isPlaying[index] = false;\n  }\n\n  onAudioSelected(event, index) {\n    let audiofile;\n    const selectedFile = event.target.files[0];\n\n    if (selectedFile) {\n      const newFileName = FileValidator.addTimestamp(selectedFile.name);\n      audiofile = new File([selectedFile], newFileName, {\n        type: selectedFile.type\n      });\n    } else {\n      audiofile = null;\n    }\n\n    if (!audiofile) {\n      this.audioUrls[index] = '';\n      this.stopAudio(index);\n      return;\n    }\n\n    const audiofileType = audiofile.type.split('/')[0]; // Get the file type (e.g., 'audio', 'video', etc.)\n\n    if (audiofileType !== 'audio') {\n      // Reset the file input to clear the selected file\n      event.target.value = '';\n      this.toastr.info('Please select an audio file.');\n      return;\n    } // Store the file object in the array for later upload\n\n\n    this.audioFiles[index] = audiofile;\n    console.log('Audio file :', audiofile);\n    const reader = new FileReader();\n\n    reader.onload = () => {\n      // Store audio URL\n      this.audioUrls[index] = reader.result;\n    };\n\n    reader.readAsDataURL(audiofile);\n  }\n\n  uploadAudioFilesSequentially(files) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      const uploadPromises = files.map((file, index) => _this.uploadSingleAudioFile(file, index));\n      return Promise.all(uploadPromises);\n    })();\n  }\n\n  uploadSingleAudioFile(file, index) {\n    return new Promise((resolve, reject) => {\n      this.dataTransferService.uploadurl(file).subscribe(res => {\n        console.log('Upload successful', file.name);\n        const fileUrl = this.baseUrl + file.name;\n        this.audioUrls[index] = fileUrl; // Directly add the file URL to this.audioUrls array\n\n        resolve(fileUrl);\n      }, error => {\n        console.error('Upload error', error);\n        this.toastr.error('Failed to upload audio file');\n        reject(error);\n      });\n    });\n  }\n\n  onSubmit() {\n    if (this.title === 'Edit') {\n      const sectorControl = this.jobForm.get('JB_sectorId'); //Remove Validation as per selected type...\n\n      const roleControl = this.jobForm.get('JB_roleId');\n\n      if (this.selectedType === 'job') {\n        sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.setValidators([Validators.required]);\n        roleControl === null || roleControl === void 0 ? void 0 : roleControl.setValidators([Validators.required]);\n      } else {\n        sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.clearValidators();\n        roleControl === null || roleControl === void 0 ? void 0 : roleControl.clearValidators();\n      }\n\n      sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.updateValueAndValidity();\n      roleControl === null || roleControl === void 0 ? void 0 : roleControl.updateValueAndValidity();\n      this.ngxSpinnerService.show('globalSpinner');\n      this.uploadAudioFilesSequentially(this.audioFiles) //upload file one by one\n      .then(audioUrls => {\n        this.insightFormArray.controls.forEach((control, index) => {\n          var _a;\n\n          const group = control;\n          (_a = group.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(this.audioUrls[index]); // Patch the URL from the array\n        });\n\n        if (this.jobForm.invalid) {\n          console.log('this.job.value', this.jobForm.value);\n          this.ngxSpinnerService.hide('globalSpinner');\n          this.toastr.info('Please fill all required fields and ensure they are filled correctly.');\n          return;\n        } else {\n          console.log('this.job.value', this.jobForm.value);\n          this.updateOpportunity();\n        }\n      }).catch(error => {\n        this.ngxSpinnerService.hide('globalSpinner');\n        console.error('Error uploading audio files:', error);\n        this.toastr.error('Audio file upload failed');\n      });\n    } else {\n      this.jobForm.patchValue({\n        JB_companyId: this.CO_id,\n        CO_sectorId: this.sectorID,\n        JB_companyLogo: this.companyLogo,\n        JB_type: this.selectedType === 'job' ? 0 : 1\n      });\n      this.ngxSpinnerService.show('globalSpinner');\n      this.uploadAudioFilesSequentially(this.audioFiles).then(audioUrls => {\n        this.insightFormArray.controls.forEach((control, index) => {\n          var _a;\n\n          const group = control;\n          (_a = group.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(audioUrls[index]); // Patch the URL from the array\n        });\n\n        if (this.jobForm.invalid) {\n          console.log('this.job.value', this.jobForm.value);\n          this.ngxSpinnerService.hide('globalSpinner');\n          this.toastr.info('Please fill all required fields and ensure they are filled correctly.');\n          return;\n        } else {\n          console.log('this.job.value', this.jobForm.value);\n          this.addOpportunity();\n        }\n      }).catch(error => {\n        this.ngxSpinnerService.hide('globalSpinner');\n        console.error('Error uploading audio files:', error);\n        this.toastr.error('Audio file upload failed');\n      });\n    }\n  }\n\n  updateOpportunity() {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u;\n\n    console.log(\"selected Type\", this.selectedType);\n    const data = {\n      JB_jobTitle: (_a = this.jobForm.get('JB_jobTitle')) === null || _a === void 0 ? void 0 : _a.value,\n      JB_description: (_b = this.jobForm.get('JB_description')) === null || _b === void 0 ? void 0 : _b.value,\n      JB_department: (_c = this.jobForm.get('JB_department')) === null || _c === void 0 ? void 0 : _c.value,\n      JB_hours: (_d = this.jobForm.get('JB_hours')) === null || _d === void 0 ? void 0 : _d.value,\n      JB_hrtTips: (_e = this.jobForm.get('JB_hrtTips')) === null || _e === void 0 ? void 0 : _e.value,\n      JB_roleId: ((_f = this.jobForm.get('JB_roleId')) === null || _f === void 0 ? void 0 : _f.value) ? (_g = this.jobForm.get('JB_roleId')) === null || _g === void 0 ? void 0 : _g.value : '',\n      JB_location: (_h = this.jobForm.get('JB_location')) === null || _h === void 0 ? void 0 : _h.value,\n      JB_salary: (_j = this.jobForm.get('JB_salary')) === null || _j === void 0 ? void 0 : _j.value,\n      JB_modeOfWork: (_k = this.jobForm.get('JB_modeOfWork')) === null || _k === void 0 ? void 0 : _k.value,\n      JB_requirements: (_l = this.jobForm.get('JB_requirements')) === null || _l === void 0 ? void 0 : _l.value,\n      JB_applyLink: (_m = this.jobForm.get('JB_applyLink')) === null || _m === void 0 ? void 0 : _m.value,\n      JB_type: (_o = this.jobForm.get('JB_type')) === null || _o === void 0 ? void 0 : _o.value,\n      JB_sectorId: ((_p = this.jobForm.get('JB_sectorId')) === null || _p === void 0 ? void 0 : _p.value) ? (_q = this.jobForm.get('JB_sectorId')) === null || _q === void 0 ? void 0 : _q.value : '',\n      JB_insights: (_r = this.jobForm.get('JB_insights')) === null || _r === void 0 ? void 0 : _r.value,\n      JB_applicationDeadline: (_s = this.jobForm.get('JB_applicationDeadline')) === null || _s === void 0 ? void 0 : _s.value,\n      JB_startDate: (_t = this.jobForm.get('JB_startDate')) === null || _t === void 0 ? void 0 : _t.value,\n      JB_companyId: this.CO_id,\n      CO_sectorId: this.sectorID,\n      JB_companyLogo: this.companyLogo,\n      JB_id: this.opportunityData.JB_id\n    };\n    const startDateControl = this.jobForm.get('JB_startDate');\n\n    if (startDateControl) {\n      const startDateValue = startDateControl.value;\n      const deadlineValue = (_u = this.jobForm.get('JB_applicationDeadline')) === null || _u === void 0 ? void 0 : _u.value;\n\n      if (startDateValue && deadlineValue) {\n        const startDate = new Date(startDateValue);\n        const deadline = new Date(deadlineValue);\n\n        if (startDate >= deadline) {\n          if (deadlineValue < this.checkMinDate) {\n            // Application deadline is in the past\n            this.toastr.error('Application deadline must be today or a future date.');\n            return;\n          }\n\n          console.log('Edit SCHEME DATA : ', data);\n          this.ngxSpinnerService.show('globalSpinner');\n          this.dataTransferService.updateJobs(data).subscribe(res => {\n            console.log('Edit User', data);\n\n            if (res.statusCode == 200) {\n              this.ngxSpinnerService.hide('globalSpinner');\n              this.toastr.success('Opportunity Updated Successfully.');\n              const state = {\n                CO_id: this.CO_id,\n                CO_sectorId: this.sectorID,\n                CO_logo: this.companyLogo\n              };\n              this.router.navigate(['/actions/employer-opportunities/existing-opportunities'], {\n                queryParams: {\n                  CO_id: this.CO_id\n                }\n              });\n              this.dataTransferService.getAllSchemeByCompanyId(this.CO_id);\n            } else {\n              this.ngxSpinnerService.hide('globalSpinner');\n              console.log('', res.message);\n              this.toastr.error('', 'Something went wrong');\n            }\n          }, error => {\n            this.ngxSpinnerService.hide('globalSpinner');\n            console.error('Unable to edit :', error);\n            this.toastr.error('', 'Something went wrong');\n          });\n        } else {\n          this.ngxSpinnerService.hide('globalSpinner');\n          this.toastr.error('Start date must be after the application deadline.');\n          console.log('Invalid form submission!');\n        }\n      }\n    }\n  }\n\n  addOpportunity() {\n    var _a;\n\n    const startDateControl = this.jobForm.get('JB_startDate');\n\n    if (startDateControl) {\n      const startDateValue = startDateControl.value;\n      const deadlineValue = (_a = this.jobForm.get('JB_applicationDeadline')) === null || _a === void 0 ? void 0 : _a.value;\n\n      if (startDateValue && deadlineValue) {\n        const startDate = new Date(startDateValue);\n        const deadline = new Date(deadlineValue);\n\n        if (startDate >= deadline) {\n          if (deadlineValue < this.checkMinDate) {\n            // Application deadline is in the past\n            this.toastr.error('Application deadline must be today or a future date.');\n            return;\n          }\n\n          console.log('ADD SCHEME DATA : ', this.jobForm.value);\n          this.dataTransferService.addOpportunity(this.jobForm.value).subscribe(res => {\n            console.log('addOpportunity', res);\n            this.ngxSpinnerService.hide('globalSpinner');\n\n            if (res.statusCode === 200) {\n              this.toastr.success('', 'New opportunity added successfully.');\n              this.router.navigate(['/actions/employer-opportunities/existing-opportunities'], {\n                queryParams: {\n                  CO_id: this.CO_id\n                }\n              });\n              this.dataTransferService.getAllSchemeByCompanyId(this.CO_id);\n            } else {\n              this.toastr.error('', 'Something went wrong');\n            }\n          }, error => {\n            this.ngxSpinnerService.hide('globalSpinner');\n            console.error('Unable to add opportunity:', error);\n            this.toastr.error('', 'Something went wrong');\n          });\n        } else {\n          this.ngxSpinnerService.hide('globalSpinner');\n          this.toastr.error('Start date must be after the application deadline.');\n          console.log('Invalid form submission!');\n        }\n      }\n    }\n  }\n\n  goToExistingScheme() {\n    this.router.navigate([`/actions/employer-opportunities/existing-opportunities`], {\n      queryParams: {\n        CO_id: this.CO_id\n      }\n    });\n  }\n\n}\n\nAddEditOpportunityComponent.ɵfac = function AddEditOpportunityComponent_Factory(t) {\n  return new (t || AddEditOpportunityComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.NgxSpinnerService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.DatePipe), i0.ɵɵdirectiveInject(i4.ActivatedRoute));\n};\n\nAddEditOpportunityComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AddEditOpportunityComponent,\n  selectors: [[\"app-add-edit-opportunity\"]],\n  viewQuery: function AddEditOpportunityComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioPlayers = _t);\n    }\n  },\n  decls: 107,\n  vars: 85,\n  consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"mt-3\"], [1, \"radio-button-group\"], [\"class\", \"radio-button-label\", 4, \"ngIf\"], [1, \"form-group\", \"col-lg-4\"], [\"for\", \"JB_sectorId\", 3, \"ngClass\"], [\"id\", \"JB_sectorId\", \"formControlName\", \"JB_sectorId\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [\"selected\", \"\", \"disabled\", \"\", 3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"JB_roleId\", 3, \"ngClass\"], [\"id\", \"JB_roleId\", \"formControlName\", \"JB_roleId\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"disabled\", \"\", \"selected\", \"\", 3, \"value\"], [3, \"value\", \"selected\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"info\", 4, \"ngIf\"], [\"for\", \"JB_jobTitle\", 3, \"ngClass\"], [\"type\", \"textarea\", \"id\", \"JB_jobTitle\", \"formControlName\", \"JB_jobTitle\", \"required\", \"\", \"placeholder\", \"Enter Title\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"for\", \"JB_location\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"JB_location\", \"required\", \"\", \"placeholder\", \"e.g. London, UK\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"JB_applicationDeadline\", 3, \"ngClass\"], [\"type\", \"date\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"JB_applicationDeadline\", \"required\", \"\", 3, \"min\", \"readonly\", \"change\", \"input\", 4, \"ngIf\"], [\"type\", \"text\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"JB_applicationDeadline\", \"readonly\", \"\", 4, \"ngIf\"], [\"for\", \"JB_startDate\", 3, \"ngClass\"], [\"type\", \"date\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"JB_startDate\", \"required\", \"\", 3, \"min\", \"readonly\", \"input\", 4, \"ngIf\"], [\"type\", \"text\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"JB_startDate\", \"readonly\", \"\", 4, \"ngIf\"], [\"for\", \"JB_modeOfWork\", 3, \"ngClass\"], [\"id\", \"JB_modeOfWork\", \"type\", \"text\", \"formControlName\", \"JB_modeOfWork\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"disabled\"], [\"for\", \"JB_salary\", 3, \"ngClass\"], [\"type\", \"number\", \"formControlName\", \"JB_salary\", \"required\", \"\", \"placeholder\", \"Enter Salary\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"JB_department\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"JB_department\", \"required\", \"\", \"placeholder\", \"Enter Department\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"JB_description\", 3, \"ngClass\"], [\"formControlName\", \"JB_description\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Enter Description\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"input\"], [4, \"ngIf\"], [\"for\", \"JB_requirements\", 3, \"ngClass\"], [\"formControlName\", \"JB_requirements\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Enter Requirements\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"input\"], [\"for\", \"JB_hrtTips\", 3, \"ngClass\"], [\"formControlName\", \"JB_hrtTips\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Enter HR Tips\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"input\"], [\"for\", \"JB_hours\", 3, \"ngClass\"], [\"type\", \"number\", \"formControlName\", \"JB_hours\", \"required\", \"\", \"placeholder\", \"Enter Hours\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"JB_applyLink\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"JB_applyLink\", \"required\", \"\", \"placeholder\", \"Enter Apply Link\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"formArrayName\", \"JB_insights\"], [1, \"mb-3\", \"py-2\"], [\"class\", \"row mb-3\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"class\", \"btn btn-sm add-insight-btn btn-outline-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"text-center\", \"mt-3\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [1, \"btn\", \"btn-light\", 3, \"click\"], [1, \"radio-button-label\"], [\"type\", \"radio\", \"id\", \"job\", \"name\", \"opportunityType\", \"value\", \"job\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [\"for\", \"job\", 1, \"mt-2\"], [\"type\", \"radio\", \"id\", \"jobScheme\", \"name\", \"opportunityType\", \"value\", \"jobScheme\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [\"for\", \"jobScheme\", 1, \"mt-2\"], [3, \"value\"], [3, \"value\", \"selected\"], [1, \"info\"], [1, \"warning\"], [\"type\", \"date\", \"formControlName\", \"JB_applicationDeadline\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"min\", \"readonly\", \"change\", \"input\"], [\"type\", \"text\", \"formControlName\", \"JB_applicationDeadline\", \"readonly\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"JB_startDate\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"min\", \"readonly\", \"input\"], [\"type\", \"text\", \"formControlName\", \"JB_startDate\", \"readonly\", \"\", 1, \"form-control\", \"form-control-sm\"], [1, \"character-count\"], [1, \"row\", \"mb-3\", 3, \"formGroupName\"], [1, \"col-lg-11\"], [1, \"row\", \"mr-0\"], [1, \"form-group\", \"col-lg-3\"], [\"for\", \"HRI_title\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_title\", \"placeholder\", \"Enter Title\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"HRI_name\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_name\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"HRI_position\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_position\", \"placeholder\", \"Enter Position\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"link\", 1, \"subtitle\"], [\"type\", \"file\", \"id\", \"link\", \"class\", \"form-control form-control-sm\", \"accept\", \"audio/*\", 3, \"change\", 4, \"ngIf\"], [\"type\", \"text\", \"class\", \"form-control form-control-sm\", \"readOnly\", \"\", 3, \"value\", 4, \"ngIf\"], [1, \"col-lg-1\", \"px-0\", \"d-flex\", \"align-items-center\", \"btns\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm mr-2\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [\"controls\", \"\", 2, \"display\", \"none\"], [\"audioPlayer\", \"\"], [\"type\", \"audio/mpeg\", 3, \"src\"], [\"type\", \"file\", \"id\", \"link\", \"accept\", \"audio/*\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"type\", \"text\", \"readOnly\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"value\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"mr-2\", 3, \"ngClass\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-minus\", \"icon\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"add-insight-btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"icon\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]],\n  template: function AddEditOpportunityComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"app-sidebar\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵtext(6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵelementStart(8, \"form\", 6);\n      i0.ɵɵlistener(\"ngSubmit\", function AddEditOpportunityComponent_Template_form_ngSubmit_8_listener() {\n        return ctx.onSubmit();\n      });\n      i0.ɵɵelementStart(9, \"div\", 7);\n      i0.ɵɵelementStart(10, \"h5\");\n      i0.ɵɵtext(11, \"Opportunity Type\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"div\", 8);\n      i0.ɵɵtemplate(13, AddEditOpportunityComponent_div_13_Template, 4, 3, \"div\", 9);\n      i0.ɵɵtemplate(14, AddEditOpportunityComponent_div_14_Template, 4, 3, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(15, \"hr\");\n      i0.ɵɵelementStart(16, \"div\", 1);\n      i0.ɵɵelementStart(17, \"div\", 10);\n      i0.ɵɵelementStart(18, \"label\", 11);\n      i0.ɵɵtext(19, \"Sector\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(20, \"select\", 12);\n      i0.ɵɵlistener(\"change\", function AddEditOpportunityComponent_Template_select_change_20_listener($event) {\n        return ctx.onChangeIndustry($event);\n      });\n      i0.ɵɵelementStart(21, \"option\", 13);\n      i0.ɵɵtext(22, \"Please Select A Sector\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(23, AddEditOpportunityComponent_option_23_Template, 2, 2, \"option\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"div\", 10);\n      i0.ɵɵelementStart(25, \"label\", 15);\n      i0.ɵɵtext(26, \"Role\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"select\", 16);\n      i0.ɵɵelementStart(28, \"option\", 17);\n      i0.ɵɵtext(29, \"Please Select Role\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(30, AddEditOpportunityComponent_option_30_Template, 2, 3, \"option\", 18);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(31, AddEditOpportunityComponent_div_31_Template, 2, 0, \"div\", 19);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"div\", 10);\n      i0.ɵɵelementStart(33, \"label\", 20);\n      i0.ɵɵtext(34, \"Title\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(35, \"input\", 21);\n      i0.ɵɵtemplate(36, AddEditOpportunityComponent_div_36_Template, 2, 0, \"div\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"div\", 10);\n      i0.ɵɵelementStart(38, \"label\", 23);\n      i0.ɵɵtext(39, \"Location\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(40, \"input\", 24);\n      i0.ɵɵtemplate(41, AddEditOpportunityComponent_div_41_Template, 2, 0, \"div\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"div\", 10);\n      i0.ɵɵelementStart(43, \"label\", 25);\n      i0.ɵɵtext(44, \"Application Deadline\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(45, AddEditOpportunityComponent_input_45_Template, 1, 2, \"input\", 26);\n      i0.ɵɵtemplate(46, AddEditOpportunityComponent_input_46_Template, 1, 0, \"input\", 27);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"div\", 10);\n      i0.ɵɵelementStart(48, \"label\", 28);\n      i0.ɵɵtext(49, \"Start Date\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(50, AddEditOpportunityComponent_input_50_Template, 1, 2, \"input\", 29);\n      i0.ɵɵtemplate(51, AddEditOpportunityComponent_input_51_Template, 1, 0, \"input\", 30);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(52, \"div\", 10);\n      i0.ɵɵelementStart(53, \"label\", 31);\n      i0.ɵɵtext(54, \"Mode of work\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(55, \"select\", 32);\n      i0.ɵɵelementStart(56, \"option\", 17);\n      i0.ɵɵtext(57, \"Please Select Mode Of Work\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(58, \"option\");\n      i0.ɵɵtext(59, \"Remote\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(60, \"option\");\n      i0.ɵɵtext(61, \"On Site\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(62, \"option\");\n      i0.ɵɵtext(63, \"Hybrid\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(64, \"div\", 10);\n      i0.ɵɵelementStart(65, \"label\", 33);\n      i0.ɵɵtext(66, \"Salary\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(67, \"input\", 34);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(68, \"div\", 10);\n      i0.ɵɵelementStart(69, \"label\", 35);\n      i0.ɵɵtext(70, \"Department\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(71, \"input\", 36);\n      i0.ɵɵtemplate(72, AddEditOpportunityComponent_div_72_Template, 2, 0, \"div\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(73, \"div\", 10);\n      i0.ɵɵelementStart(74, \"label\", 37);\n      i0.ɵɵtext(75, \"Description\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(76, \"textarea\", 38);\n      i0.ɵɵlistener(\"input\", function AddEditOpportunityComponent_Template_textarea_input_76_listener() {\n        return ctx.onInput(\"JB_description\");\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(77, AddEditOpportunityComponent_div_77_Template, 4, 2, \"div\", 39);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(78, \"div\", 10);\n      i0.ɵɵelementStart(79, \"label\", 40);\n      i0.ɵɵtext(80, \"Requirements\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(81, \"textarea\", 41);\n      i0.ɵɵlistener(\"input\", function AddEditOpportunityComponent_Template_textarea_input_81_listener() {\n        return ctx.onInput(\"JB_requirements\");\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(82, AddEditOpportunityComponent_div_82_Template, 4, 2, \"div\", 39);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(83, \"div\", 10);\n      i0.ɵɵelementStart(84, \"label\", 42);\n      i0.ɵɵtext(85, \"HR Tips\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(86, \"textarea\", 43);\n      i0.ɵɵlistener(\"input\", function AddEditOpportunityComponent_Template_textarea_input_86_listener() {\n        return ctx.onInput(\"JB_hrtTips\");\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(87, AddEditOpportunityComponent_div_87_Template, 4, 2, \"div\", 39);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(88, \"div\", 10);\n      i0.ɵɵelementStart(89, \"label\", 44);\n      i0.ɵɵtext(90, \"Work Hours\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(91, \"input\", 45);\n      i0.ɵɵtemplate(92, AddEditOpportunityComponent_div_92_Template, 2, 0, \"div\", 22);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(93, \"div\", 10);\n      i0.ɵɵelementStart(94, \"label\", 46);\n      i0.ɵɵtext(95, \"Apply Link\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(96, \"input\", 47);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(97, \"hr\");\n      i0.ɵɵelementStart(98, \"div\", 48);\n      i0.ɵɵelementStart(99, \"h6\", 49);\n      i0.ɵɵtext(100, \"HR / Hiring Manager\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(101, AddEditOpportunityComponent_div_101_Template, 27, 10, \"div\", 50);\n      i0.ɵɵtemplate(102, AddEditOpportunityComponent_button_102_Template, 3, 0, \"button\", 51);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(103, \"div\", 52);\n      i0.ɵɵtemplate(104, AddEditOpportunityComponent_button_104_Template, 2, 0, \"button\", 53);\n      i0.ɵɵelementStart(105, \"button\", 54);\n      i0.ɵɵlistener(\"click\", function AddEditOpportunityComponent_Template_button_click_105_listener() {\n        return ctx.goToExistingScheme();\n      });\n      i0.ɵɵtext(106, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      let tmp_12_0;\n      let tmp_15_0;\n      let tmp_18_0;\n      let tmp_33_0;\n      let tmp_45_0;\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Opportunity\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.jobForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", !(ctx.hideOppType && ctx.selectedType === \"jobScheme\"));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !(ctx.hideOppType && ctx.selectedType === \"job\"));\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c3, ctx.selectedType == \"job\" && !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c4, ctx.isReadonly));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"value\", null);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.SectorList);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(55, _c3, ctx.selectedType == \"job\" && !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(57, _c4, ctx.isReadonly));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"value\", null);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.RoleList);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !((tmp_12_0 = ctx.jobForm.get(\"JB_sectorId\")) == null ? null : tmp_12_0.value) && !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(59, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx.jobForm.get(\"JB_jobTitle\")) == null ? null : tmp_15_0.errors) && ((tmp_15_0 = ctx.jobForm.get(\"JB_jobTitle\")) == null ? null : tmp_15_0.errors == null ? null : tmp_15_0.errors.wordLimitExceeded));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(61, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.jobForm.get(\"JB_location\")) == null ? null : tmp_18_0.errors) && ((tmp_18_0 = ctx.jobForm.get(\"JB_location\")) == null ? null : tmp_18_0.errors == null ? null : tmp_18_0.errors.wordLimitExceeded));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(63, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.title !== \"View\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title === \"View\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(65, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.title !== \"View\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.title === \"View\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(67, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(69, _c5, ctx.isReadonly))(\"disabled\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"value\", null);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(71, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(73, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_33_0 = ctx.jobForm.get(\"JB_department\")) == null ? null : tmp_33_0.errors) && ((tmp_33_0 = ctx.jobForm.get(\"JB_department\")) == null ? null : tmp_33_0.errors == null ? null : tmp_33_0.errors.wordLimitExceeded));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(75, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(77, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(79, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(81, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_45_0 = ctx.jobForm.get(\"JB_hours\")) == null ? null : tmp_45_0.errors) && ((tmp_45_0 = ctx.jobForm.get(\"JB_hours\")) == null ? null : tmp_45_0.errors == null ? null : tmp_45_0.errors.negativeValue));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(83, _c3, !ctx.isReadonly));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngForOf\", ctx.insightFormArray.controls);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.insightFormArray.length < 3 && !ctx.isReadonly);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n    }\n  },\n  directives: [i7.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i6.NgIf, i6.NgClass, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.RequiredValidator, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i6.NgForOf, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.FormArrayName, i1.RadioControlValueAccessor, i1.NgModel, i1.FormGroupName],\n  styles: [\".footer[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n\\n.head-exst[_ngcontent-%COMP%] {\\n  width: 100vh;\\n}\\n\\n.readOnlyColor[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n\\n.warning[_ngcontent-%COMP%] {\\n  color: #ee0c0c;\\n  font-size: smaller;\\n  margin-top: 4px;\\n}\\n\\n.info[_ngcontent-%COMP%] {\\n  color: rgba(88, 87, 87, 0.881);\\n  font-size: smaller;\\n  margin-top: 4px;\\n}\\n\\n.fa-plus[_ngcontent-%COMP%] {\\n  font-size: small;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  color: rgba(81, 80, 80, 0.856) !important;\\n}\\n\\n.radio-button-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 20px;\\n}\\n\\n.radio-button-label[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  margin-right: 10px;\\n}\\n\\n.radio-button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.character-count[_ngcontent-%COMP%] {\\n  font-size: smaller;\\n  margin-top: 2px;\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n\\n.fa-plus[_ngcontent-%COMP%] {\\n  font-size: small;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  color: rgba(81, 80, 80, 0.856) !important;\\n}\\n\\n.suggestion[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1224px) {\\n  .insight-btn[_ngcontent-%COMP%] {\\n    padding: 4px 5px;\\n  }\\n\\n  .fas[_ngcontent-%COMP%] {\\n    font-size: small;\\n  }\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1224px) {\\n  .insight-btn[_ngcontent-%COMP%] {\\n    padding: 4px 5px;\\n  }\\n\\n  .fas[_ngcontent-%COMP%] {\\n    font-size: small;\\n  }\\n}\\n\\n@media screen and (min-width: 320px) and (max-width: 768px) {\\n  .btns[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: center;\\n    margin-bottom: 20px;\\n  }\\n\\n  .add-insight-btn[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/src/app/shared/sidebar/actions/Employer-Opportunities/Opportunity/add-edit-opportunity/add-edit-opportunity.component.ts"], "names": ["Validators", "FileValidator", "i0", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "_c0", "_c1", "standalone", "AddEditOpportunityComponent_div_13_Template", "rf", "ctx", "_r20", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "AddEditOpportunityComponent_div_13_Template_input_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "ctx_r19", "ɵɵnextContext", "onTypeChange", "ɵɵelementEnd", "ɵɵtext", "ctx_r0", "ɵɵadvance", "ɵɵproperty", "selectedType", "ɵɵpureFunction0", "AddEditOpportunityComponent_div_14_Template", "_r22", "AddEditOpportunityComponent_div_14_Template_input_ngModelChange_1_listener", "ctx_r21", "ctx_r1", "AddEditOpportunityComponent_option_23_Template", "sector_r23", "$implicit", "IN_id", "ɵɵtextInterpolate", "IN_name", "AddEditOpportunityComponent_option_30_Template", "roles_r24", "ctx_r3", "tmp_1_0", "RO_id", "jobForm", "get", "value", "ɵɵtextInterpolate1", "RO_title", "AddEditOpportunityComponent_div_31_Template", "AddEditOpportunityComponent_div_36_Template", "AddEditOpportunityComponent_div_41_Template", "AddEditOpportunityComponent_input_45_Template", "_r26", "AddEditOpportunityComponent_input_45_Template_input_change_0_listener", "ctx_r25", "updateStartDateMin", "AddEditOpportunityComponent_input_45_Template_input_input_0_listener", "ctx_r27", "checkInvalidDate", "ctx_r7", "minDate", "is<PERSON><PERSON><PERSON>ly", "AddEditOpportunityComponent_input_46_Template", "ɵɵelement", "AddEditOpportunityComponent_input_50_Template", "_r29", "AddEditOpportunityComponent_input_50_Template_input_input_0_listener", "ctx_r28", "ctx_r9", "minStartDate", "AddEditOpportunityComponent_input_51_Template", "AddEditOpportunityComponent_div_72_Template", "AddEditOpportunityComponent_div_77_div_3_Template", "AddEditOpportunityComponent_div_77_Template", "ɵɵtemplate", "ctx_r12", "descriptionCharCount", "errors", "characterLimitExceeded", "AddEditOpportunityComponent_div_82_div_3_Template", "AddEditOpportunityComponent_div_82_Template", "ctx_r13", "requirementsCharCount", "AddEditOpportunityComponent_div_87_div_3_Template", "AddEditOpportunityComponent_div_87_Template", "ctx_r14", "hrtTipsCharCount", "AddEditOpportunityComponent_div_92_Template", "AddEditOpportunityComponent_div_101_input_18_Template", "_r42", "AddEditOpportunityComponent_div_101_input_18_Template_input_change_0_listener", "i_r34", "index", "ctx_r40", "onAudioSelected", "AddEditOpportunityComponent_div_101_input_19_Template", "ctx_r36", "audioNames", "_c2", "a0", "a1", "a2", "AddEditOpportunityComponent_div_101_button_21_Template", "_r46", "AddEditOpportunityComponent_div_101_button_21_Template_button_click_0_listener", "ctx_r44", "toggleAudio", "ctx_r37", "ɵɵpureFunction3", "isPlaying", "AddEditOpportunityComponent_div_101_button_22_Template", "_r50", "AddEditOpportunityComponent_div_101_button_22_Template_button_click_0_listener", "ctx_r48", "removeInsight", "AddEditOpportunityComponent_div_101_Template", "ctx_r16", "title", "audioUrls", "insightFormArray", "length", "ɵɵsanitizeUrl", "AddEditOpportunityComponent_button_102_Template", "_r52", "AddEditOpportunityComponent_button_102_Template_button_click_0_listener", "ctx_r51", "addInsight", "AddEditOpportunityComponent_button_104_Template", "_c3", "_c4", "_c5", "wordLimitValidator", "max<PERSON><PERSON>s", "control", "words", "trim", "split", "console", "log", "wordLimitExceeded", "wordCount", "nonNegativeValidator", "negativeValue", "AddEditOpportunityComponent", "constructor", "formBuilder", "dataTransferService", "ngxSpinnerService", "router", "toastr", "datePipe", "route", "_a", "_b", "p", "viewInsight", "baseUrl", "invalidDates", "Set", "audioFileUrls", "audioFiles", "characterCount", "hideOppType", "queryParams", "subscribe", "params", "CO_id", "navigate", "state", "getCurrentNavigation", "extras", "opportunityData", "Date", "toISOString", "checkMinDate", "group", "JB_companyId", "JB_companyLogo", "JB_applicationDeadline", "required", "JB_startDate", "JB_jobTitle", "JB_description", "characterLimitValidator", "JB_department", "JB_hours", "JB_hrtTips", "JB_roleId", "JB_location", "JB_salary", "JB_modeOfWork", "JB_requirements", "JB_applyLink", "JB_type", "JB_sectorId", "JB_insights", "array", "createInsight", "ngOnInit", "getCompanyById", "getSectorTitles", "disable", "populateForm", "makeDataReadOnly", "data", "_c", "getAllRole", "patchValue", "transform", "CO_sectorId", "sectorID", "companyLogo", "hrInsightsArray", "clear", "map", "insight", "HRI_link", "pop", "for<PERSON>ach", "insightFormGroup", "HRI_title", "HRI_name", "HRI_position", "push", "Array", "fill", "_d", "enable", "reset", "disabled", "limit", "onInput", "field", "updateValueAndValidity", "insightDiv", "document", "getElementById", "audioPlayers", "audioPlayer", "nativeElement", "pause", "currentTime", "sectorControl", "roleControl", "setValidators", "clearValidators", "onChangeIndustry", "event", "target", "deadline", "setDate", "getDate", "controlName", "isNaN", "parse", "add", "delete", "res", "statusCode", "SectorList", "error", "status", "formatDate", "date", "getTime", "companyId", "CO_logo", "getAllRoleBySectorId", "next", "RoleList", "hide", "showDeleteModal", "scheme", "deleteId", "JB_id", "removeAt", "splice", "audioElements", "toArray", "audioElement", "stopAllAudio", "src", "play", "currentIndex", "stopAudio", "audiofile", "selectedFile", "files", "newFileName", "addTimestamp", "name", "File", "type", "audiofileType", "info", "reader", "FileReader", "onload", "result", "readAsDataURL", "uploadAudioFilesSequentially", "uploadPromises", "file", "uploadSingleAudioFile", "Promise", "all", "resolve", "reject", "uploadurl", "fileUrl", "onSubmit", "show", "then", "controls", "setValue", "invalid", "updateOpportunity", "catch", "addOpportunity", "_e", "_f", "_g", "_h", "_j", "_k", "_l", "_m", "_o", "_p", "_q", "_r", "_s", "_t", "_u", "startDateControl", "startDateValue", "deadlineValue", "startDate", "updateJobs", "success", "getAllSchemeByCompanyId", "message", "goToExistingScheme", "ɵfac", "AddEditOpportunityComponent_Factory", "t", "ɵɵdirectiveInject", "FormBuilder", "DataTransferService", "NgxSpinnerService", "Router", "ToastrService", "DatePipe", "ActivatedRoute", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "AddEditOpportunityComponent_Query", "ɵɵviewQuery", "ɵɵqueryRefresh", "ɵɵloadQuery", "decls", "vars", "consts", "template", "AddEditOpportunityComponent_Template", "AddEditOpportunityComponent_Template_form_ngSubmit_8_listener", "AddEditOpportunityComponent_Template_select_change_20_listener", "AddEditOpportunityComponent_Template_textarea_input_76_listener", "AddEditOpportunityComponent_Template_textarea_input_81_listener", "AddEditOpportunityComponent_Template_textarea_input_86_listener", "AddEditOpportunityComponent_Template_button_click_105_listener", "tmp_12_0", "tmp_15_0", "tmp_18_0", "tmp_33_0", "tmp_45_0", "ɵɵpureFunction1", "directives", "SidebarComponent", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "NgIf", "Ng<PERSON><PERSON>", "SelectControlValueAccessor", "NgControlStatus", "FormControlName", "RequiredValidator", "NgSelectOption", "ɵNgSelectMultipleOption", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DefaultValueAccessor", "NumberValueAccessor", "FormArrayName", "RadioControlValueAccessor", "NgModel", "FormGroupName", "styles"], "mappings": ";AAAA,SAASA,UAAT,QAA4B,gBAA5B;AACA,SAASC,aAAT,QAA8B,mDAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+CAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,YAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+BAApB;AACA,MAAMC,GAAG,GAAG,CAAC,aAAD,CAAZ;;AACA,MAAMC,GAAG,GAAG,YAAY;AAAE,SAAO;AAAEC,IAAAA,UAAU,EAAE;AAAd,GAAP;AAA8B,CAAxD;;AACA,SAASC,2CAAT,CAAqDC,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxE,UAAME,IAAI,GAAGd,EAAE,CAACe,gBAAH,EAAb;;AACAf,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,eAAd,EAA+B,SAASC,0EAAT,CAAoFC,MAApF,EAA4F;AAAEnB,MAAAA,EAAE,CAACoB,aAAH,CAAiBN,IAAjB;AAAwB,YAAMO,OAAO,GAAGrB,EAAE,CAACsB,aAAH,EAAhB;AAAoC,aAAOD,OAAO,CAACE,YAAR,CAAqBJ,MAArB,CAAP;AAAsC,KAA/N;AACAnB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,KAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMc,MAAM,GAAG1B,EAAE,CAACsB,aAAH,EAAf;AACAtB,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyBF,MAAM,CAACG,YAAhC,EAA8C,gBAA9C,EAAgE7B,EAAE,CAAC8B,eAAH,CAAmB,CAAnB,EAAsBrB,GAAtB,CAAhE;AACH;AAAE;;AACH,SAASsB,2CAAT,CAAqDnB,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxE,UAAMoB,IAAI,GAAGhC,EAAE,CAACe,gBAAH,EAAb;;AACAf,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,eAAd,EAA+B,SAASgB,0EAAT,CAAoFd,MAApF,EAA4F;AAAEnB,MAAAA,EAAE,CAACoB,aAAH,CAAiBY,IAAjB;AAAwB,YAAME,OAAO,GAAGlC,EAAE,CAACsB,aAAH,EAAhB;AAAoC,aAAOY,OAAO,CAACX,YAAR,CAAqBJ,MAArB,CAAP;AAAsC,KAA/N;AACAnB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,aAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMuB,MAAM,GAAGnC,EAAE,CAACsB,aAAH,EAAf;AACAtB,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyBO,MAAM,CAACN,YAAhC,EAA8C,gBAA9C,EAAgE7B,EAAE,CAAC8B,eAAH,CAAmB,CAAnB,EAAsBrB,GAAtB,CAAhE;AACH;AAAE;;AACH,SAAS2B,8CAAT,CAAwDxB,EAAxD,EAA4DC,GAA5D,EAAiE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3EZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMyB,UAAU,GAAGxB,GAAG,CAACyB,SAAvB;AACAtC,IAAAA,EAAE,CAAC4B,UAAH,CAAc,OAAd,EAAuBS,UAAU,CAACE,KAAlC;AACAvC,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAACwC,iBAAH,CAAqBH,UAAU,CAACI,OAAhC;AACH;AAAE;;AACH,SAASC,8CAAT,CAAwD9B,EAAxD,EAA4DC,GAA5D,EAAiE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3EZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM+B,SAAS,GAAG9B,GAAG,CAACyB,SAAtB;AACA,UAAMM,MAAM,GAAG5C,EAAE,CAACsB,aAAH,EAAf;AACA,QAAIuB,OAAJ;AACA7C,IAAAA,EAAE,CAAC4B,UAAH,CAAc,OAAd,EAAuBe,SAAS,CAACG,KAAjC,EAAwC,UAAxC,EAAoDH,SAAS,CAACG,KAAV,MAAqB,CAACD,OAAO,GAAGD,MAAM,CAACG,OAAP,CAAeC,GAAf,CAAmB,WAAnB,CAAX,KAA+C,IAA/C,GAAsD,IAAtD,GAA6DH,OAAO,CAACI,KAA1F,CAApD;AACAjD,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAACkD,kBAAH,CAAsB,GAAtB,EAA2BP,SAAS,CAACQ,QAArC,EAA+C,EAA/C;AACH;AAAE;;AACH,SAASC,2CAAT,CAAqDxC,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,iCAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAAS6B,2CAAT,CAAqDzC,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,yCAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAAS8B,2CAAT,CAAqD1C,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,yCAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAAS+B,6CAAT,CAAuD3C,EAAvD,EAA2DC,GAA3D,EAAgE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC1E,UAAM4C,IAAI,GAAGxD,EAAE,CAACe,gBAAH,EAAb;;AACAf,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,QAAd,EAAwB,SAASwC,qEAAT,GAAiF;AAAEzD,MAAAA,EAAE,CAACoB,aAAH,CAAiBoC,IAAjB;AAAwB,YAAME,OAAO,GAAG1D,EAAE,CAACsB,aAAH,EAAhB;AAAoC,aAAOoC,OAAO,CAACC,kBAAR,EAAP;AAAsC,KAA7M,EAA+M,OAA/M,EAAwN,SAASC,oEAAT,GAAgF;AAAE5D,MAAAA,EAAE,CAACoB,aAAH,CAAiBoC,IAAjB;AAAwB,YAAMK,OAAO,GAAG7D,EAAE,CAACsB,aAAH,EAAhB;AAAoC,aAAOuC,OAAO,CAACC,gBAAR,CAAyB,wBAAzB,CAAP;AAA4D,KAAla;AACA9D,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmD,MAAM,GAAG/D,EAAE,CAACsB,aAAH,EAAf;AACAtB,IAAAA,EAAE,CAAC4B,UAAH,CAAc,KAAd,EAAqBmC,MAAM,CAACC,OAA5B,EAAqC,UAArC,EAAiDD,MAAM,CAACE,UAAxD;AACH;AAAE;;AACH,SAASC,6CAAT,CAAuDtD,EAAvD,EAA2DC,GAA3D,EAAgE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC1EZ,IAAAA,EAAE,CAACmE,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACH;AAAE;;AACH,SAASC,6CAAT,CAAuDxD,EAAvD,EAA2DC,GAA3D,EAAgE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC1E,UAAMyD,IAAI,GAAGrE,EAAE,CAACe,gBAAH,EAAb;;AACAf,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,OAAd,EAAuB,SAASqD,oEAAT,GAAgF;AAAEtE,MAAAA,EAAE,CAACoB,aAAH,CAAiBiD,IAAjB;AAAwB,YAAME,OAAO,GAAGvE,EAAE,CAACsB,aAAH,EAAhB;AAAoC,aAAOiD,OAAO,CAACT,gBAAR,CAAyB,cAAzB,CAAP;AAAkD,KAAvN;AACA9D,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM4D,MAAM,GAAGxE,EAAE,CAACsB,aAAH,EAAf;AACAtB,IAAAA,EAAE,CAAC4B,UAAH,CAAc,KAAd,EAAqB4C,MAAM,CAACC,YAA5B,EAA0C,UAA1C,EAAsDD,MAAM,CAACP,UAA7D;AACH;AAAE;;AACH,SAASS,6CAAT,CAAuD9D,EAAvD,EAA2DC,GAA3D,EAAgE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC1EZ,IAAAA,EAAE,CAACmE,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACH;AAAE;;AACH,SAASQ,2CAAT,CAAqD/D,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,yCAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASoD,iDAAT,CAA2DhE,EAA3D,EAA+DC,GAA/D,EAAoE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC9EZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,qDAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASqD,2CAAT,CAAqDjE,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAAC8E,UAAH,CAAc,CAAd,EAAiBF,iDAAjB,EAAoE,CAApE,EAAuE,CAAvE,EAA0E,KAA1E,EAAiF,EAAjF;AACA5E,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmE,OAAO,GAAG/E,EAAE,CAACsB,aAAH,EAAhB;AACA,QAAIuB,OAAJ;AACA7C,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAACkD,kBAAH,CAAsB,EAAtB,EAA0B6B,OAAO,CAACC,oBAAlC,EAAwD,MAAxD;AACAhF,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAACiB,OAAO,GAAGkC,OAAO,CAAChC,OAAR,CAAgBC,GAAhB,CAAoB,gBAApB,CAAX,KAAqD,IAArD,GAA4D,IAA5D,GAAmEH,OAAO,CAACoC,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCpC,OAAO,CAACoC,MAAR,CAAeC,sBAAxI;AACH;AAAE;;AACH,SAASC,iDAAT,CAA2DvE,EAA3D,EAA+DC,GAA/D,EAAoE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC9EZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,qDAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAAS4D,2CAAT,CAAqDxE,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAAC8E,UAAH,CAAc,CAAd,EAAiBK,iDAAjB,EAAoE,CAApE,EAAuE,CAAvE,EAA0E,KAA1E,EAAiF,EAAjF;AACAnF,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMyE,OAAO,GAAGrF,EAAE,CAACsB,aAAH,EAAhB;AACA,QAAIuB,OAAJ;AACA7C,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAACkD,kBAAH,CAAsB,EAAtB,EAA0BmC,OAAO,CAACC,qBAAlC,EAAyD,MAAzD;AACAtF,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAACiB,OAAO,GAAGwC,OAAO,CAACtC,OAAR,CAAgBC,GAAhB,CAAoB,iBAApB,CAAX,KAAsD,IAAtD,GAA6D,IAA7D,GAAoEH,OAAO,CAACoC,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCpC,OAAO,CAACoC,MAAR,CAAeC,sBAAzI;AACH;AAAE;;AACH,SAASK,iDAAT,CAA2D3E,EAA3D,EAA+DC,GAA/D,EAAoE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC9EZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,qDAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASgE,2CAAT,CAAqD5E,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAAC8E,UAAH,CAAc,CAAd,EAAiBS,iDAAjB,EAAoE,CAApE,EAAuE,CAAvE,EAA0E,KAA1E,EAAiF,EAAjF;AACAvF,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM6E,OAAO,GAAGzF,EAAE,CAACsB,aAAH,EAAhB;AACA,QAAIuB,OAAJ;AACA7C,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAACkD,kBAAH,CAAsB,EAAtB,EAA0BuC,OAAO,CAACC,gBAAlC,EAAoD,MAApD;AACA1F,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAACiB,OAAO,GAAG4C,OAAO,CAAC1C,OAAR,CAAgBC,GAAhB,CAAoB,YAApB,CAAX,KAAiD,IAAjD,GAAwD,IAAxD,GAA+DH,OAAO,CAACoC,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCpC,OAAO,CAACoC,MAAR,CAAeC,sBAApI;AACH;AAAE;;AACH,SAASS,2CAAT,CAAqD/E,EAArD,EAAyDC,GAAzD,EAA8D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxEZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,mCAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAASoE,qDAAT,CAA+DhF,EAA/D,EAAmEC,GAAnE,EAAwE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClF,UAAMiF,IAAI,GAAG7F,EAAE,CAACe,gBAAH,EAAb;;AACAf,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,QAAd,EAAwB,SAAS6E,6EAAT,CAAuF3E,MAAvF,EAA+F;AAAEnB,MAAAA,EAAE,CAACoB,aAAH,CAAiByE,IAAjB;AAAwB,YAAME,KAAK,GAAG/F,EAAE,CAACsB,aAAH,GAAmB0E,KAAjC;AAAwC,YAAMC,OAAO,GAAGjG,EAAE,CAACsB,aAAH,EAAhB;AAAoC,aAAO2E,OAAO,CAACC,eAAR,CAAwB/E,MAAxB,EAAgC4E,KAAhC,CAAP;AAAgD,KAA7Q;AACA/F,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAAS2E,qDAAT,CAA+DvF,EAA/D,EAAmEC,GAAnE,EAAwE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClFZ,IAAAA,EAAE,CAACmE,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACH;;AAAC,MAAIvD,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmF,KAAK,GAAG/F,EAAE,CAACsB,aAAH,GAAmB0E,KAAjC;AACA,UAAMI,OAAO,GAAGpG,EAAE,CAACsB,aAAH,EAAhB;AACAtB,IAAAA,EAAE,CAAC4B,UAAH,CAAc,OAAd,EAAuBwE,OAAO,CAACC,UAAR,CAAmBN,KAAnB,CAAvB;AACH;AAAE;;AACH,MAAMO,GAAG,GAAG,UAAUC,EAAV,EAAcC,EAAd,EAAkBC,EAAlB,EAAsB;AAAE,SAAO;AAAE,2BAAuBF,EAAzB;AAA6B,6BAAyBC,EAAtD;AAA0D,YAAQC;AAAlE,GAAP;AAAgF,CAApH;;AACA,SAASC,sDAAT,CAAgE9F,EAAhE,EAAoEC,GAApE,EAAyE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnF,UAAM+F,IAAI,GAAG3G,EAAE,CAACe,gBAAH,EAAb;;AACAf,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,OAAd,EAAuB,SAAS2F,8EAAT,GAA0F;AAAE5G,MAAAA,EAAE,CAACoB,aAAH,CAAiBuF,IAAjB;AAAwB,YAAMZ,KAAK,GAAG/F,EAAE,CAACsB,aAAH,GAAmB0E,KAAjC;AAAwC,YAAMa,OAAO,GAAG7G,EAAE,CAACsB,aAAH,EAAhB;AAAoC,aAAOuF,OAAO,CAACC,WAAR,CAAoBf,KAApB,CAAP;AAAoC,KAA3P;AACA/F,IAAAA,EAAE,CAACmE,SAAH,CAAa,CAAb,EAAgB,GAAhB,EAAqB,EAArB;AACAnE,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmF,KAAK,GAAG/F,EAAE,CAACsB,aAAH,GAAmB0E,KAAjC;AACA,UAAMe,OAAO,GAAG/G,EAAE,CAACsB,aAAH,EAAhB;AACAtB,IAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAACgH,eAAH,CAAmB,CAAnB,EAAsBV,GAAtB,EAA2B,CAACS,OAAO,CAACE,SAAR,CAAkBlB,KAAlB,CAA5B,EAAsDgB,OAAO,CAACE,SAAR,CAAkBlB,KAAlB,CAAtD,EAAgFgB,OAAO,CAAC9C,UAAxF,CAAzB;AACAjE,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyBmF,OAAO,CAACE,SAAR,CAAkBlB,KAAlB,IAA2B,UAA3B,GAAwC,SAAjE;AACH;AAAE;;AACH,SAASmB,sDAAT,CAAgEtG,EAAhE,EAAoEC,GAApE,EAAyE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACnF,UAAMuG,IAAI,GAAGnH,EAAE,CAACe,gBAAH,EAAb;;AACAf,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,OAAd,EAAuB,SAASmG,8EAAT,GAA0F;AAAEpH,MAAAA,EAAE,CAACoB,aAAH,CAAiB+F,IAAjB;AAAwB,YAAMpB,KAAK,GAAG/F,EAAE,CAACsB,aAAH,GAAmB0E,KAAjC;AAAwC,YAAMqB,OAAO,GAAGrH,EAAE,CAACsB,aAAH,EAAhB;AAAoC,aAAO+F,OAAO,CAACC,aAAR,CAAsBvB,KAAtB,CAAP;AAAsC,KAA7P;AACA/F,IAAAA,EAAE,CAACmE,SAAH,CAAa,CAAb,EAAgB,GAAhB,EAAqB,EAArB;AACAnE,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAAS+F,4CAAT,CAAsD3G,EAAtD,EAA0DC,GAA1D,EAA+D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACzEZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,eAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACmE,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAnE,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,MAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAnE,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,UAAd;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAnE,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBc,qDAAlB,EAAyE,CAAzE,EAA4E,CAA5E,EAA+E,OAA/E,EAAwF,EAAxF;AACA5F,IAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBqB,qDAAlB,EAAyE,CAAzE,EAA4E,CAA5E,EAA+E,OAA/E,EAAwF,EAAxF;AACAnG,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,IAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkB4B,sDAAlB,EAA0E,CAA1E,EAA6E,CAA7E,EAAgF,QAAhF,EAA0F,EAA1F;AACA1G,IAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBoC,sDAAlB,EAA0E,CAA1E,EAA6E,CAA7E,EAAgF,QAAhF,EAA0F,EAA1F;AACAlH,IAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B,EAAmC,EAAnC;AACAhB,IAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,QAAjB,EAA2B,EAA3B;AACAnE,IAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,oDAAd;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACAxB,IAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,MAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMmF,KAAK,GAAGlF,GAAG,CAACmF,KAAlB;AACA,UAAMwB,OAAO,GAAGxH,EAAE,CAACsB,aAAH,EAAhB;AACAtB,IAAAA,EAAE,CAAC4B,UAAH,CAAc,eAAd,EAA+BmE,KAA/B;AACA/F,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0B4F,OAAO,CAACvD,UAAlC;AACAjE,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0B4F,OAAO,CAACvD,UAAlC;AACAjE,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0B4F,OAAO,CAACvD,UAAlC;AACAjE,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAACwC,iBAAH,CAAqB,CAACgF,OAAO,CAACvD,UAAT,GAAsB,gBAAtB,GAAyC,kBAA9D;AACAjE,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB4F,OAAO,CAACC,KAAR,KAAkB,MAAxC;AACAzH,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB4F,OAAO,CAACC,KAAR,KAAkB,MAAxC;AACAzH,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB4F,OAAO,CAACE,SAAR,CAAkB3B,KAAlB,CAAtB;AACA/F,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB4F,OAAO,CAACG,gBAAR,CAAyBC,MAAzB,GAAkC,CAAlC,IAAuC,CAACJ,OAAO,CAACvD,UAAtE;AACAjE,IAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,IAAAA,EAAE,CAAC4B,UAAH,CAAc,KAAd,EAAqB4F,OAAO,CAACE,SAAR,CAAkB3B,KAAlB,CAArB,EAA+C/F,EAAE,CAAC6H,aAAlD;AACH;AAAE;;AACH,SAASC,+CAAT,CAAyDlH,EAAzD,EAA6DC,GAA7D,EAAkE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC5E,UAAMmH,IAAI,GAAG/H,EAAE,CAACe,gBAAH,EAAb;;AACAf,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,OAAd,EAAuB,SAAS+G,uEAAT,GAAmF;AAAEhI,MAAAA,EAAE,CAACoB,aAAH,CAAiB2G,IAAjB;AAAwB,YAAME,OAAO,GAAGjI,EAAE,CAACsB,aAAH,EAAhB;AAAoC,aAAO2G,OAAO,CAACC,UAAR,EAAP;AAA8B,KAAtM;AACAlI,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,cAAb;AACAzB,IAAAA,EAAE,CAACmE,SAAH,CAAa,CAAb,EAAgB,GAAhB,EAAqB,EAArB;AACAnE,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,SAAS2G,+CAAT,CAAyDvH,EAAzD,EAA6DC,GAA7D,EAAkE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC5EZ,IAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAhB,IAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV,EAAa,MAAb;AACAzB,IAAAA,EAAE,CAACwB,YAAH;AACH;AAAE;;AACH,MAAM4G,GAAG,GAAG,UAAU7B,EAAV,EAAc;AAAE,SAAO;AAAE,sBAAkBA;AAApB,GAAP;AAAkC,CAA9D;;AACA,MAAM8B,GAAG,GAAG,UAAU9B,EAAV,EAAc;AAAE,SAAO;AAAE,qBAAiBA;AAAnB,GAAP;AAAiC,CAA7D;;AACA,MAAM+B,GAAG,GAAG,UAAU/B,EAAV,EAAc;AAAE,SAAO;AAAE,iBAAaA;AAAf,GAAP;AAA6B,CAAzD;;AACA,SAASgC,kBAAT,CAA4BC,QAA5B,EAAsC;AAClC,SAAQC,OAAD,IAAa;AAChB,QAAIA,OAAO,CAACxF,KAAZ,EAAmB;AACf,YAAMyF,KAAK,GAAGD,OAAO,CAACxF,KAAR,CAAc0F,IAAd,GAAqBC,KAArB,CAA2B,KAA3B,CAAd;;AACA,UAAIF,KAAK,CAACd,MAAN,GAAeY,QAAnB,EAA6B;AACzBK,QAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwBN,QAAxB;AACA,eAAO;AAAEO,UAAAA,iBAAiB,EAAE,IAArB;AAA2BC,UAAAA,SAAS,EAAEN,KAAK,CAACd;AAA5C,SAAP;AACH;AACJ;;AACD,WAAO,IAAP;AACH,GATD;AAUH;;AACD,SAASqB,oBAAT,CAA8BR,OAA9B,EAAuC;AACnC;AACA,QAAMxF,KAAK,GAAGwF,OAAO,CAACxF,KAAtB;;AACA,MAAIA,KAAK,GAAG,CAAR,IAAaA,KAAK,GAAG,EAAzB,EAA6B;AACzB,WAAO;AAAEiG,MAAAA,aAAa,EAAE;AAAjB,KAAP;AACH;;AACD,SAAO,IAAP;AACH;;AACD,OAAO,MAAMC,2BAAN,CAAkC;AACrCC,EAAAA,WAAW,CAACC,WAAD,EAAcC,mBAAd,EAAmCC,iBAAnC,EAAsDC,MAAtD,EAA8DC,MAA9D,EAAsEC,QAAtE,EAAgFC,KAAhF,EAAuF;AAC9F,QAAIC,EAAJ,EAAQC,EAAR;;AACA,SAAKR,WAAL,GAAmBA,WAAnB;AACA,SAAKC,mBAAL,GAA2BA,mBAA3B;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKG,CAAL,GAAS,CAAT;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKtC,KAAL,GAAa,SAAb;AACA,SAAKxD,UAAL,GAAkB,KAAlB;AACA,SAAK+F,OAAL,GAAe,4CAAf;AACA,SAAKC,YAAL,GAAoB,IAAIC,GAAJ,EAApB;AACA,SAAKrI,YAAL,GAAoB,KAApB,CAf8F,CAenE;;AAC3B,SAAKmD,oBAAL,GAA4B,CAA5B;AACA,SAAKM,qBAAL,GAA6B,CAA7B;AACA,SAAKI,gBAAL,GAAwB,CAAxB;AACA,SAAKyE,aAAL,GAAqB,EAArB,CAnB8F,CAmBrE;;AACzB,SAAKC,UAAL,GAAkB,EAAlB;AACA,SAAKC,cAAL,GAAsB,CAAtB;AACA,SAAKpD,SAAL,GAAiB,EAAjB,CAtB8F,CAsBzE;;AACrB,SAAKS,SAAL,GAAiB,EAAjB,CAvB8F,CAuBzE;;AACrB,SAAK4C,WAAL,GAAmB,KAAnB;AACA,SAAKjE,UAAL,GAAkB,EAAlB;AACA,SAAKsD,KAAL,CAAWY,WAAX,CAAuBC,SAAvB,CAAiCC,MAAM,IAAI;AACvC,UAAIA,MAAJ,EAAY;AACR,aAAKC,KAAL,GAAaD,MAAM,CAAC,OAAD,CAAnB;AACA5B,QAAAA,OAAO,CAACC,GAAR,CAAY,KAAK4B,KAAjB;AACH,OAHD,MAIK;AACD,aAAKlB,MAAL,CAAYmB,QAAZ,CAAqB,CAAC,iCAAD,CAArB;AACH;AACJ,KARD;AASA,UAAMC,KAAK,GAAG,CAACf,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKJ,MAAL,CAAYqB,oBAAZ,EAAN,MAA8C,IAA9C,IAAsDjB,EAAE,KAAK,KAAK,CAAlE,GAAsE,KAAK,CAA3E,GAA+EA,EAAE,CAACkB,MAAxF,MAAoG,IAApG,IAA4GjB,EAAE,KAAK,KAAK,CAAxH,GAA4H,KAAK,CAAjI,GAAqIA,EAAE,CAACe,KAAtJ,CAnC8F,CAmC+D;;AAC7J/B,IAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B8B,KAA5B;;AACA,QAAIA,KAAJ,EAAW;AACP,WAAKb,WAAL,GAAmBa,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACb,WAAvE;AACA,WAAKtC,KAAL,GAAamD,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACnD,KAAjE;AACA,WAAKsD,eAAL,GAAuBH,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACG,eAA3E,CAHO,CAIP;;AACAlC,MAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsC,KAAKiC,eAA3C;AACH,KAND,MAOK;AACD,WAAKvB,MAAL,CAAYmB,QAAZ,CAAqB,CAAE,gCAAF,CAArB;AACH;;AACD,SAAK3G,OAAL,GAAe,IAAIgH,IAAJ,GAAWC,WAAX,GAAyBrC,KAAzB,CAA+B,GAA/B,EAAoC,CAApC,CAAf;AACA,SAAKsC,YAAL,GAAoB,KAAKlH,OAAzB;AACA,SAAKjB,OAAL,GAAe,KAAKsG,WAAL,CAAiB8B,KAAjB,EACf;AACA;AACIC,MAAAA,YAAY,EAAE,CAAC,EAAD,CADlB;AAEIC,MAAAA,cAAc,EAAE,CAAC,EAAD,CAFpB;AAGIC,MAAAA,sBAAsB,EAAE,CAAC,EAAD,EAAK,CAACxL,UAAU,CAACyL,QAAZ,CAAL,CAH5B;AAIIC,MAAAA,YAAY,EAAE,CAAC,EAAD,EAAK,CAAC1L,UAAU,CAACyL,QAAZ,CAAL,CAJlB;AAKIE,MAAAA,WAAW,EAAE,CAAC,EAAD,EAAK,CAAC3L,UAAU,CAACyL,QAAZ,EAAsBhD,kBAAkB,CAAC,EAAD,CAAxC,CAAL,CALjB;AAMImD,MAAAA,cAAc,EAAE,CACZ,EADY,EAEZ,CAAC5L,UAAU,CAACyL,QAAZ,EAAsB,KAAKI,uBAAL,CAA6B,GAA7B,CAAtB,CAFY,CANpB;AAUIC,MAAAA,aAAa,EAAE,CAAC,EAAD,EAAK,CAAC9L,UAAU,CAACyL,QAAZ,EAAsBhD,kBAAkB,CAAC,EAAD,CAAxC,CAAL,CAVnB;AAWIsD,MAAAA,QAAQ,EAAE,CAAC,EAAD,EAAK,CAAC/L,UAAU,CAACyL,QAAZ,EAAsBtC,oBAAtB,CAAL,CAXd;AAYI6C,MAAAA,UAAU,EAAE,CAAC,EAAD,EAAK,CAAChM,UAAU,CAACyL,QAAZ,EAAsB,KAAKI,uBAAL,CAA6B,GAA7B,CAAtB,CAAL,CAZhB;AAaII,MAAAA,SAAS,EAAE,CAAC,EAAD,EAAK,CAACjM,UAAU,CAACyL,QAAZ,CAAL,CAbf;AAcIS,MAAAA,WAAW,EAAE,CAAC,EAAD,EAAK,CAAClM,UAAU,CAACyL,QAAZ,EAAsBhD,kBAAkB,CAAC,EAAD,CAAxC,CAAL,CAdjB;AAeI0D,MAAAA,SAAS,EAAE,CAAC,EAAD,EAAK,CAACnM,UAAU,CAACyL,QAAZ,CAAL,CAff;AAgBIW,MAAAA,aAAa,EAAE,CAAC,EAAD,EAAK,CAACpM,UAAU,CAACyL,QAAZ,EAAsBhD,kBAAkB,CAAC,EAAD,CAAxC,CAAL,CAhBnB;AAiBI4D,MAAAA,eAAe,EAAE,CACb,EADa,EAEb,CAACrM,UAAU,CAACyL,QAAZ,EAAsB,KAAKI,uBAAL,CAA6B,GAA7B,CAAtB,CAFa,CAjBrB;AAqBIS,MAAAA,YAAY,EAAE,CAAC,EAAD,EAAK,CAACtM,UAAU,CAACyL,QAAZ,CAAL,CArBlB;AAsBIc,MAAAA,OAAO,EAAE,CAAC,EAAD,EAAKvM,UAAU,CAACyL,QAAhB,CAtBb;AAuBIe,MAAAA,WAAW,EAAE,CAAC,EAAD,CAvBjB;AAwBIC,MAAAA,WAAW,EAAE,KAAKlD,WAAL,CAAiBmD,KAAjB,CAAuB,CAAC,KAAKC,aAAL,EAAD,CAAvB;AAxBjB,KAFe,CAAf;AA4BA,SAAKlL,YAAL,CAAkB,KAAKM,YAAvB;AACH;;AACD6K,EAAAA,QAAQ,GAAG;AACP,QAAI9C,EAAJ,EAAQC,EAAR;;AACA,SAAK8C,cAAL,CAAoB,KAAKjC,KAAzB;AACA,SAAKkC,eAAL,GAHO,CAIP;;AACA,SAAKnI,YAAL,GAAoB,KAAKT,OAAzB;;AACA,QAAI,KAAKnC,YAAL,KAAsB,KAAtB,IAA+B,EAAE,CAAC+H,EAAE,GAAG,KAAK7G,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAN,MAA2C,IAA3C,IAAmD4G,EAAE,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,EAAE,CAAC3G,KAAjF,CAAnC,EAA4H;AACxH,OAAC4G,EAAE,GAAG,KAAK9G,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAN,MAAyC,IAAzC,IAAiD6G,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAACgD,OAAH,EAA1E;AACH;;AACD,QAAI,KAAKpF,KAAL,KAAe,MAAnB,EAA2B;AACvB,WAAKqF,YAAL,CAAkB,KAAK/B,eAAvB;AACH;;AACD,QAAI,KAAKtD,KAAL,KAAe,MAAnB,EAA2B;AACvB,WAAKsF,gBAAL,CAAsB,KAAKhC,eAA3B;AACH;AACJ;;AACDgC,EAAAA,gBAAgB,CAACC,IAAD,EAAO;AACnB,QAAIpD,EAAJ,EAAQC,EAAR,EAAYoD,EAAZ;;AACA,KAACrD,EAAE,GAAG,KAAK7G,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAN,MAA2C,IAA3C,IAAmD4G,EAAE,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,EAAE,CAACiD,OAAH,EAA5E;AACA,KAAChD,EAAE,GAAG,KAAK9G,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAN,MAAyC,IAAzC,IAAiD6G,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAACgD,OAAH,EAA1E;AACA,KAACI,EAAE,GAAG,KAAKlK,OAAL,CAAaC,GAAb,CAAiB,eAAjB,CAAN,MAA6C,IAA7C,IAAqDiK,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAACJ,OAAH,EAA9E;AACA,SAAKvC,WAAL,GAAmB,IAAnB;AACA,SAAK4C,UAAL,CAAgBF,IAAI,CAACV,WAArB;AACA,SAAKrI,UAAL,GAAkB,IAAlB;AACA,SAAKlB,OAAL,CAAaoK,UAAb,CAAwB;AACpB1B,MAAAA,WAAW,EAAEuB,IAAI,CAACvB,WADE;AAEpBC,MAAAA,cAAc,EAAEsB,IAAI,CAACtB,cAFD;AAGpBE,MAAAA,aAAa,EAAEoB,IAAI,CAACpB,aAHA;AAIpBC,MAAAA,QAAQ,EAAEmB,IAAI,CAACnB,QAJK;AAKpBC,MAAAA,UAAU,EAAEkB,IAAI,CAAClB,UALG;AAMpBC,MAAAA,SAAS,EAAEiB,IAAI,CAACjB,SANI;AAOpBC,MAAAA,WAAW,EAAEgB,IAAI,CAAChB,WAPE;AAQpBC,MAAAA,SAAS,EAAEe,IAAI,CAACf,SARI;AASpBC,MAAAA,aAAa,EAAEc,IAAI,CAACd,aATA;AAUpBC,MAAAA,eAAe,EAAEa,IAAI,CAACb,eAVF;AAWpBC,MAAAA,YAAY,EAAEY,IAAI,CAACZ,YAXC;AAYpBC,MAAAA,OAAO,EAAEW,IAAI,CAACX,OAZM;AAapBC,MAAAA,WAAW,EAAEU,IAAI,CAACV,WAbE;AAcpBC,MAAAA,WAAW,EAAES,IAAI,CAACT,WAdE;AAepBjB,MAAAA,sBAAsB,EAAE,KAAK5B,QAAL,CAAc0D,SAAd,CAAwBJ,IAAI,CAAC1B,sBAA7B,EAAqD,SAArD,CAfJ;AAgBpBE,MAAAA,YAAY,EAAE,KAAK9B,QAAL,CAAc0D,SAAd,CAAwBJ,IAAI,CAACxB,YAA7B,EAA2C,SAA3C,CAhBM;AAiBpBJ,MAAAA,YAAY,EAAE,KAAKV,KAjBC;AAkBpB2C,MAAAA,WAAW,EAAE,KAAKC,QAlBE;AAmBpBjC,MAAAA,cAAc,EAAE,KAAKkC;AAnBD,KAAxB;AAqBA,UAAMC,eAAe,GAAG,KAAKzK,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAxB;AACAwK,IAAAA,eAAe,CAACC,KAAhB;AACA,SAAKpH,UAAL,GAAkB2G,IAAI,CAACT,WAAL,CAAiBmB,GAAjB,CAAsBC,OAAD,IAAaA,OAAO,CAACC,QAAR,CAAiBhF,KAAjB,CAAuB,GAAvB,EAA4BiF,GAA5B,EAAlC,CAAlB;AACAb,IAAAA,IAAI,CAACT,WAAL,CAAiBuB,OAAjB,CAA0BH,OAAD,IAAa;AAClC,YAAMI,gBAAgB,GAAG,KAAK1E,WAAL,CAAiB8B,KAAjB,CAAuB;AAC5C6C,QAAAA,SAAS,EAAE,CAACL,OAAO,CAACK,SAAT,CADiC;AAE5CC,QAAAA,QAAQ,EAAE,CAACN,OAAO,CAACM,QAAT,CAFkC;AAG5CC,QAAAA,YAAY,EAAE,CAACP,OAAO,CAACO,YAAT,CAH8B;AAI5CN,QAAAA,QAAQ,EAAE,CAACD,OAAO,CAACC,QAAT;AAJkC,OAAvB,CAAzB;AAMAJ,MAAAA,eAAe,CAACW,IAAhB,CAAqBJ,gBAArB;AACH,KARD;AASA,SAAKrG,SAAL,GAAiBsF,IAAI,CAACT,WAAL,CAAiBmB,GAAjB,CAAsBC,OAAD,IAAaA,OAAO,CAACC,QAA1C,CAAjB;AACA,SAAK3G,SAAL,GAAiB,IAAImH,KAAJ,CAAUpB,IAAI,CAACT,WAAL,CAAiB3E,MAA3B,EAAmCyG,IAAnC,CAAwC,KAAxC,CAAjB;;AACA,QAAIrB,IAAI,CAACX,OAAL,KAAiB,CAArB,EAAwB;AACpB,WAAKxK,YAAL,GAAoB,KAApB;AACH,KAFD,MAGK;AACD,WAAKA,YAAL,GAAoB,WAApB;AACH;AACJ;;AACDiL,EAAAA,YAAY,CAACE,IAAD,EAAO;AACf,QAAIpD,EAAJ,EAAQC,EAAR,EAAYoD,EAAZ,EAAgBqB,EAAhB;;AACA,SAAKhE,WAAL,GAAmB,IAAnB;AACAzB,IAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqCkE,IAArC;AACA,SAAKE,UAAL,CAAgBF,IAAI,CAACV,WAArB;AACA,SAAKvJ,OAAL,CAAaoK,UAAb,CAAwB;AACpB1B,MAAAA,WAAW,EAAEuB,IAAI,CAACvB,WADE;AAEpBC,MAAAA,cAAc,EAAEsB,IAAI,CAACtB,cAFD;AAGpBE,MAAAA,aAAa,EAAEoB,IAAI,CAACpB,aAHA;AAIpBC,MAAAA,QAAQ,EAAEmB,IAAI,CAACnB,QAJK;AAKpBC,MAAAA,UAAU,EAAEkB,IAAI,CAAClB,UALG;AAMpBC,MAAAA,SAAS,EAAEiB,IAAI,CAACjB,SANI;AAOpBC,MAAAA,WAAW,EAAEgB,IAAI,CAAChB,WAPE;AAQpBC,MAAAA,SAAS,EAAEe,IAAI,CAACf,SARI;AASpBC,MAAAA,aAAa,EAAEc,IAAI,CAACd,aATA;AAUpBC,MAAAA,eAAe,EAAEa,IAAI,CAACb,eAVF;AAWpBC,MAAAA,YAAY,EAAEY,IAAI,CAACZ,YAXC;AAYpBC,MAAAA,OAAO,EAAEW,IAAI,CAACX,OAZM;AAapBC,MAAAA,WAAW,EAAEU,IAAI,CAACV,WAbE;AAcpBC,MAAAA,WAAW,EAAES,IAAI,CAACT,WAdE;AAepBjB,MAAAA,sBAAsB,EAAE0B,IAAI,CAAC1B,sBAfT;AAgBpBE,MAAAA,YAAY,EAAEwB,IAAI,CAACxB,YAhBC;AAiBpBJ,MAAAA,YAAY,EAAE,KAAKV,KAjBC;AAkBpB2C,MAAAA,WAAW,EAAE,KAAKC,QAlBE;AAmBpBjC,MAAAA,cAAc,EAAE,KAAKkC;AAnBD,KAAxB;AAqBA,UAAMC,eAAe,GAAG,KAAKzK,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAxB;AACAwK,IAAAA,eAAe,CAACC,KAAhB;AACAT,IAAAA,IAAI,CAACT,WAAL,CAAiBuB,OAAjB,CAA0BH,OAAD,IAAa;AAClC,YAAMI,gBAAgB,GAAG,KAAK1E,WAAL,CAAiB8B,KAAjB,CAAuB;AAC5C6C,QAAAA,SAAS,EAAE,CAACL,OAAO,CAACK,SAAT,CADiC;AAE5CC,QAAAA,QAAQ,EAAE,CAACN,OAAO,CAACM,QAAT,CAFkC;AAG5CC,QAAAA,YAAY,EAAE,CAACP,OAAO,CAACO,YAAT,CAH8B;AAI5CN,QAAAA,QAAQ,EAAE,CAACD,OAAO,CAACC,QAAT;AAJkC,OAAvB,CAAzB;AAMAJ,MAAAA,eAAe,CAACW,IAAhB,CAAqBJ,gBAArB;AACH,KARD,EA5Be,CAqCf;AACA;AACA;;AACA,SAAKrG,SAAL,GAAiBsF,IAAI,CAACT,WAAL,CAAiBmB,GAAjB,CAAsBC,OAAD,IAAaA,OAAO,CAACC,QAA1C,CAAjB;AACA,SAAK3G,SAAL,GAAiB,IAAImH,KAAJ,CAAUpB,IAAI,CAACT,WAAL,CAAiB3E,MAA3B,EAAmCyG,IAAnC,CAAwC,KAAxC,CAAjB;AACA,UAAMhC,OAAO,GAAG,CAACzC,EAAE,GAAG,KAAKmB,eAAX,MAAgC,IAAhC,IAAwCnB,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACyC,OAApF;;AACA,QAAIA,OAAO,KAAK,CAAhB,EAAmB;AACf,UAAI,CAACxC,EAAE,GAAG,KAAK9G,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAN,MAA2C,IAA3C,IAAmD6G,EAAE,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,EAAE,CAAC5G,KAAnF,EAA0F;AACtF,SAACgK,EAAE,GAAG,KAAKlK,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAN,MAAyC,IAAzC,IAAiDiK,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAACsB,MAAH,EAA1E;AACH,OAFD,MAGK;AACD,SAACD,EAAE,GAAG,KAAKvL,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAN,MAAyC,IAAzC,IAAiDsL,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAACE,KAAH,CAAS;AAAEvL,UAAAA,KAAK,EAAE,IAAT;AAAewL,UAAAA,QAAQ,EAAE;AAAzB,SAAT,CAA1E;AACH;;AACD,WAAK5M,YAAL,GAAoB,KAApB;AACH,KARD,MASK;AACD,WAAKA,YAAL,GAAoB,WAApB;AACH;AACJ;;AACmB,MAAhB8F,gBAAgB,GAAG;AACnB,WAAO,KAAK5E,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAP;AACH;;AACD2I,EAAAA,uBAAuB,CAAC+C,KAAD,EAAQ;AAC3B,WAAQjG,OAAD,IAAa;AAChB,UAAIA,OAAO,CAACxF,KAAR,IAAiBwF,OAAO,CAACxF,KAAR,CAAc2E,MAAd,GAAuB8G,KAA5C,EAAmD;AAC/C,eAAO;AAAExJ,UAAAA,sBAAsB,EAAE;AAA1B,SAAP;AACH;;AACD,aAAO,IAAP;AACH,KALD;AAMH;;AACDyJ,EAAAA,OAAO,CAACC,KAAD,EAAQ;AACX,UAAMnG,OAAO,GAAG,KAAK1F,OAAL,CAAaC,GAAb,CAAiB4L,KAAjB,CAAhB;;AACA,QAAInG,OAAJ,EAAa;AACT,YAAMxF,KAAK,GAAGwF,OAAO,CAACxF,KAAR,IAAiB,EAA/B;;AACA,cAAQ2L,KAAR;AACI,aAAK,gBAAL;AACI,eAAK5J,oBAAL,GAA4B/B,KAAK,CAAC2E,MAAlC;AACA;;AACJ,aAAK,iBAAL;AACI,eAAKtC,qBAAL,GAA6BrC,KAAK,CAAC2E,MAAnC;AACA;;AACJ,aAAK,YAAL;AACI,eAAKlC,gBAAL,GAAwBzC,KAAK,CAAC2E,MAA9B;AACA;AATR;;AAWAa,MAAAA,OAAO,CAACoG,sBAAR,GAbS,CAayB;AACrC;AACJ;;AACDtN,EAAAA,YAAY,CAAC0B,KAAD,EAAQ;AAChB,QAAI2G,EAAJ;;AACA,SAAK/H,YAAL,GAAoBoB,KAApB;AACA,SAAKF,OAAL,CAAayL,KAAb;AACA,UAAMM,UAAU,GAAGC,QAAQ,CAACC,cAAT,CAAwB,MAAxB,CAAnB;;AACA,QAAIF,UAAJ,EAAgB;AACZA,MAAAA,UAAU,CAAC7L,KAAX,GAAmB,EAAnB;AACH;;AACD,SAAKyE,SAAL,GAAiB,EAAjB;AACA,SAAK0C,UAAL,GAAkB,EAAlB;AACA,SAAKnD,SAAL,GAAiB,EAAjB;;AACA,QAAI,KAAKgI,YAAT,EAAuB;AACnB,WAAKA,YAAL,CAAkBnB,OAAlB,CAA2BoB,WAAD,IAAiB;AACvCA,QAAAA,WAAW,CAACC,aAAZ,CAA0BC,KAA1B;AACAF,QAAAA,WAAW,CAACC,aAAZ,CAA0BE,WAA1B,GAAwC,CAAxC;AACH,OAHD;AAIH;;AACD,KAACzF,EAAE,GAAG,KAAK7G,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAN,MAAyC,IAAzC,IAAiD4G,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAAC4E,KAAH,CAAS;AAAEvL,MAAAA,KAAK,EAAE,IAAT;AAAewL,MAAAA,QAAQ,EAAE;AAAzB,KAAT,CAA1E;AACA,UAAMa,aAAa,GAAG,KAAKvM,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAtB;AACA,UAAMuM,WAAW,GAAG,KAAKxM,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAApB;;AACA,QAAIC,KAAK,KAAK,KAAd,EAAqB;AACjBqM,MAAAA,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACE,aAAd,CAA4B,CAAC1P,UAAU,CAACyL,QAAZ,CAA5B,CAA9D;AACAgE,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACC,aAAZ,CAA0B,CAAC1P,UAAU,CAACyL,QAAZ,CAA1B,CAA1D;AACH,KAHD,MAIK;AACD+D,MAAAA,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACG,eAAd,EAA9D;AACAF,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACE,eAAZ,EAA1D;AACH;;AACDH,IAAAA,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACT,sBAAd,EAA9D;AACAU,IAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACV,sBAAZ,EAA1D;AACH;;AACDa,EAAAA,gBAAgB,CAACC,KAAD,EAAQ;AACpB,QAAI/F,EAAJ;;AACA,UAAMyD,WAAW,GAAGsC,KAAK,CAACC,MAAN,CAAa3M,KAAjC;AACA,KAAC2G,EAAE,GAAG,KAAK7G,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAN,MAAyC,IAAzC,IAAiD4G,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAAC2E,MAAH,EAA1E;AACA,SAAKrB,UAAL,CAAgBG,WAAhB;AACH;;AACD1J,EAAAA,kBAAkB,GAAG;AACjB,QAAIiG,EAAJ;;AACA,UAAMiG,QAAQ,GAAG,CAACjG,EAAE,GAAG,KAAK7G,OAAL,CAAaC,GAAb,CAAiB,wBAAjB,CAAN,MAAsD,IAAtD,IAA8D4G,EAAE,KAAK,KAAK,CAA1E,GAA8E,KAAK,CAAnF,GAAuFA,EAAE,CAAC3G,KAA3G;;AACA,QAAI4M,QAAJ,EAAc;AACV,YAAMpL,YAAY,GAAG,IAAIuG,IAAJ,CAAS6E,QAAT,CAArB;AACApL,MAAAA,YAAY,CAACqL,OAAb,CAAqBrL,YAAY,CAACsL,OAAb,KAAyB,CAA9C,EAFU,CAEwC;;AAClD,WAAKtL,YAAL,GAAoBA,YAAY,CAACwG,WAAb,GAA2BrC,KAA3B,CAAiC,GAAjC,EAAsC,CAAtC,CAApB,CAHU,CAIV;;AACA,WAAK9E,gBAAL,CAAsB,cAAtB;AACH;AACJ;;AACDA,EAAAA,gBAAgB,CAACkM,WAAD,EAAc;AAC1B,UAAMvH,OAAO,GAAG,KAAK1F,OAAL,CAAaC,GAAb,CAAiBgN,WAAjB,CAAhB;;AACA,QAAIvH,OAAO,IAAIA,OAAO,CAACxF,KAAnB,IAA4BgN,KAAK,CAACjF,IAAI,CAACkF,KAAL,CAAWzH,OAAO,CAACxF,KAAnB,CAAD,CAArC,EAAkE;AAC9D,WAAKgH,YAAL,CAAkBkG,GAAlB,CAAsBH,WAAtB;AACH,KAFD,MAGK;AACD,WAAK/F,YAAL,CAAkBmG,MAAlB,CAAyBJ,WAAzB;AACH;AACJ;;AACDpD,EAAAA,eAAe,GAAG;AACd;AACA,SAAKtD,mBAAL,CAAyBsD,eAAzB,GAA2CpC,SAA3C,CAAsD6F,GAAD,IAAS;AAC1D,UAAKA,GAAG,CAACC,UAAJ,GAAiB,GAAtB,EAA4B;AACxB,aAAKC,UAAL,GAAkBF,GAAG,CAACrD,IAAtB;AACAnE,QAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB,KAAKyH,UAA5B;AACH,OAHD,MAIK;AACD1H,QAAAA,OAAO,CAAC2H,KAAR,CAAc,kCAAd,EAAkDH,GAAG,CAACI,MAAtD;AACH;AACJ,KARD;AASH;;AACDC,EAAAA,UAAU,CAACC,IAAD,EAAO;AACb;AACA,QAAI,CAACV,KAAK,CAACU,IAAI,CAACC,OAAL,EAAD,CAAV,EAA4B;AACxB,aAAO,KAAKlH,QAAL,CAAc0D,SAAd,CAAwBuD,IAAxB,EAA8B,aAA9B,CAAP;AACH,KAFD,MAGK;AACD,aAAO,EAAP;AACH;AACJ;;AACDhE,EAAAA,cAAc,CAACkE,SAAD,EAAY;AACtB,SAAKvH,mBAAL,CAAyBqD,cAAzB,CAAwCkE,SAAxC,EAAmDrG,SAAnD,CAA8D6F,GAAD,IAAS;AAClE,WAAK9C,WAAL,GAAmB8C,GAAG,CAACS,OAAvB;AACA,WAAKxD,QAAL,GAAgB+C,GAAG,CAAChD,WAApB;AACAxE,MAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BuH,GAA9B;AACH,KAJD,EAIIG,KAAD,IAAW;AACV3H,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqB0H,KAArB;AACA,WAAK/G,MAAL,CAAY+G,KAAZ,CAAkB,sBAAlB;AACH,KAPD;AAQH;;AACDtD,EAAAA,UAAU,CAACG,WAAD,EAAc;AACpBxE,IAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2BuE,WAA3B;AACA,SAAK/D,mBAAL,CAAyByH,oBAAzB,CAA8C1D,WAA9C,EAA2D7C,SAA3D,CAAqE;AACjEwG,MAAAA,IAAI,EAAGX,GAAD,IAAS;AACX,YAAIA,GAAG,CAACC,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,eAAKW,QAAL,GAAgBZ,GAAG,CAACrD,IAApB;AACAnE,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2B,KAAKmI,QAAhC;AACH,SAHD,MAIK;AACDpI,UAAAA,OAAO,CAAC2H,KAAR,CAAc,+BAAd,EAA+CH,GAAG,CAACI,MAAnD;AACH;AACJ,OATgE;AAUjED,MAAAA,KAAK,EAAGA,KAAD,IAAW;AACd,aAAKjH,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACArI,QAAAA,OAAO,CAAC2H,KAAR,CAAc,sCAAd,EAAsDA,KAAtD;AACH;AAbgE,KAArE;AAeH,GAhVoC,CAiVrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAW,EAAAA,eAAe,CAACC,MAAD,EAAS;AACpBvI,IAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BsI,MAA7B;AACA,SAAKC,QAAL,GAAgBD,MAAM,CAACE,KAAvB;AACH,GAvWoC,CAwWrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA7E,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAKpD,WAAL,CAAiB8B,KAAjB,CAAuB;AAC1B6C,MAAAA,SAAS,EAAE,CAAC,EAAD,CADe;AAE1BC,MAAAA,QAAQ,EAAE,CAAC,EAAD,CAFgB;AAG1BC,MAAAA,YAAY,EAAE,CAAC,EAAD,CAHY;AAI1BN,MAAAA,QAAQ,EAAE,CAAC,IAAD;AAJgB,KAAvB,CAAP;AAMA,SAAK3G,SAAL,CAAekH,IAAf,CAAoB,KAApB,EAPY,CAOgB;;AAC5B,SAAKzG,SAAL,CAAeyG,IAAf,CAAoB,EAApB,EARY,CAQa;AAC5B;;AACDjG,EAAAA,UAAU,GAAG;AACT,SAAKP,gBAAL,CAAsBwG,IAAtB,CAA2B,KAAK1B,aAAL,EAA3B;AACH;;AACDnF,EAAAA,aAAa,CAACtB,KAAD,EAAQ;AACjB,SAAK2B,gBAAL,CAAsB4J,QAAtB,CAA+BvL,KAA/B;AACA,SAAK0B,SAAL,CAAe8J,MAAf,CAAsBxL,KAAtB,EAA6B,CAA7B,EAFiB,CAEgB;;AACjC,SAAKiB,SAAL,CAAeuK,MAAf,CAAsBxL,KAAtB,EAA6B,CAA7B,EAHiB,CAGgB;;AACjC,SAAKoE,UAAL,CAAgBoH,MAAhB,CAAuBxL,KAAvB,EAA8B,CAA9B,EAJiB,CAIiB;AACrC;;AACDc,EAAAA,WAAW,CAACd,KAAD,EAAQ;AACf,UAAMyL,aAAa,GAAG,KAAKxC,YAAL,CAAkByC,OAAlB,EAAtB;AACA,UAAMC,YAAY,GAAGF,aAAa,CAACzL,KAAD,CAAb,CAAqBmJ,aAA1C,CAFe,CAGf;;AACA,SAAKyC,YAAL,CAAkB5L,KAAlB,EAJe,CAKf;;AACA,QAAI,KAAKiB,SAAL,CAAejB,KAAf,CAAJ,EAA2B;AACvB2L,MAAAA,YAAY,CAACvC,KAAb;AACH,KAFD,MAGK;AACDuC,MAAAA,YAAY,CAACE,GAAb,GAAmB,KAAKnK,SAAL,CAAe1B,KAAf,CAAnB;AACA2L,MAAAA,YAAY,CAACG,IAAb;AACH,KAZc,CAaf;;;AACA,SAAK7K,SAAL,CAAejB,KAAf,IAAwB,CAAC,KAAKiB,SAAL,CAAejB,KAAf,CAAzB;AACH;;AACD4L,EAAAA,YAAY,CAACG,YAAD,EAAe;AACvB,SAAK9C,YAAL,CAAkBnB,OAAlB,CAA0B,CAACoB,WAAD,EAAclJ,KAAd,KAAwB;AAC9C,UAAIA,KAAK,KAAK+L,YAAd,EAA4B;AACxB7C,QAAAA,WAAW,CAACC,aAAZ,CAA0BC,KAA1B;AACA,aAAKnI,SAAL,CAAejB,KAAf,IAAwB,KAAxB;AACH;AACJ,KALD;AAMH;;AACDgM,EAAAA,SAAS,CAAChM,KAAD,EAAQ;AACb,UAAMyL,aAAa,GAAG,KAAKxC,YAAL,CAAkByC,OAAlB,EAAtB;AACA,UAAMC,YAAY,GAAGF,aAAa,CAACzL,KAAD,CAAb,CAAqBmJ,aAA1C;AACAwC,IAAAA,YAAY,CAACvC,KAAb;AACAuC,IAAAA,YAAY,CAACtC,WAAb,GAA2B,CAA3B,CAJa,CAIiB;;AAC9B,SAAKpI,SAAL,CAAejB,KAAf,IAAwB,KAAxB;AACH;;AACDE,EAAAA,eAAe,CAACyJ,KAAD,EAAQ3J,KAAR,EAAe;AAC1B,QAAIiM,SAAJ;AACA,UAAMC,YAAY,GAAGvC,KAAK,CAACC,MAAN,CAAauC,KAAb,CAAmB,CAAnB,CAArB;;AACA,QAAID,YAAJ,EAAkB;AACd,YAAME,WAAW,GAAGrS,aAAa,CAACsS,YAAd,CAA2BH,YAAY,CAACI,IAAxC,CAApB;AACAL,MAAAA,SAAS,GAAG,IAAIM,IAAJ,CAAS,CAACL,YAAD,CAAT,EAAyBE,WAAzB,EAAsC;AAAEI,QAAAA,IAAI,EAAEN,YAAY,CAACM;AAArB,OAAtC,CAAZ;AACH,KAHD,MAIK;AACDP,MAAAA,SAAS,GAAG,IAAZ;AACH;;AACD,QAAI,CAACA,SAAL,EAAgB;AACZ,WAAKvK,SAAL,CAAe1B,KAAf,IAAwB,EAAxB;AACA,WAAKgM,SAAL,CAAehM,KAAf;AACA;AACH;;AACD,UAAMyM,aAAa,GAAGR,SAAS,CAACO,IAAV,CAAe5J,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAtB,CAf0B,CAe0B;;AACpD,QAAI6J,aAAa,KAAK,OAAtB,EAA+B;AAC3B;AACA9C,MAAAA,KAAK,CAACC,MAAN,CAAa3M,KAAb,GAAqB,EAArB;AACA,WAAKwG,MAAL,CAAYiJ,IAAZ,CAAiB,8BAAjB;AACA;AACH,KArByB,CAsB1B;;;AACA,SAAKtI,UAAL,CAAgBpE,KAAhB,IAAyBiM,SAAzB;AACApJ,IAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BmJ,SAA5B;AACA,UAAMU,MAAM,GAAG,IAAIC,UAAJ,EAAf;;AACAD,IAAAA,MAAM,CAACE,MAAP,GAAgB,MAAM;AAClB;AACA,WAAKnL,SAAL,CAAe1B,KAAf,IAAwB2M,MAAM,CAACG,MAA/B;AACH,KAHD;;AAIAH,IAAAA,MAAM,CAACI,aAAP,CAAqBd,SAArB;AACH;;AACKe,EAAAA,4BAA4B,CAACb,KAAD,EAAQ;AAAA;;AAAA;AACtC,YAAMc,cAAc,GAAGd,KAAK,CAACzE,GAAN,CAAU,CAACwF,IAAD,EAAOlN,KAAP,KAAiB,KAAI,CAACmN,qBAAL,CAA2BD,IAA3B,EAAiClN,KAAjC,CAA3B,CAAvB;AACA,aAAOoN,OAAO,CAACC,GAAR,CAAYJ,cAAZ,CAAP;AAFsC;AAGzC;;AACDE,EAAAA,qBAAqB,CAACD,IAAD,EAAOlN,KAAP,EAAc;AAC/B,WAAO,IAAIoN,OAAJ,CAAY,CAACE,OAAD,EAAUC,MAAV,KAAqB;AACpC,WAAKjK,mBAAL,CAAyBkK,SAAzB,CAAmCN,IAAnC,EAAyC1I,SAAzC,CAAoD6F,GAAD,IAAS;AACxDxH,QAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCoK,IAAI,CAACZ,IAAtC;AACA,cAAMmB,OAAO,GAAG,KAAKzJ,OAAL,GAAekJ,IAAI,CAACZ,IAApC;AACA,aAAK5K,SAAL,CAAe1B,KAAf,IAAwByN,OAAxB,CAHwD,CAGvB;;AACjCH,QAAAA,OAAO,CAACG,OAAD,CAAP;AACH,OALD,EAKIjD,KAAD,IAAW;AACV3H,QAAAA,OAAO,CAAC2H,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,aAAK/G,MAAL,CAAY+G,KAAZ,CAAkB,6BAAlB;AACA+C,QAAAA,MAAM,CAAC/C,KAAD,CAAN;AACH,OATD;AAUH,KAXM,CAAP;AAYH;;AACDkD,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKjM,KAAL,KAAe,MAAnB,EAA2B;AACvB,YAAM6H,aAAa,GAAG,KAAKvM,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAtB,CADuB,CACgC;;AACvD,YAAMuM,WAAW,GAAG,KAAKxM,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAApB;;AACA,UAAI,KAAKnB,YAAL,KAAsB,KAA1B,EAAiC;AAC7ByN,QAAAA,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACE,aAAd,CAA4B,CAAC1P,UAAU,CAACyL,QAAZ,CAA5B,CAA9D;AACAgE,QAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACC,aAAZ,CAA0B,CAAC1P,UAAU,CAACyL,QAAZ,CAA1B,CAA1D;AACH,OAHD,MAIK;AACD+D,QAAAA,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACG,eAAd,EAA9D;AACAF,QAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACE,eAAZ,EAA1D;AACH;;AACDH,MAAAA,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACT,sBAAd,EAA9D;AACAU,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACV,sBAAZ,EAA1D;AACA,WAAKtF,iBAAL,CAAuBoK,IAAvB,CAA4B,eAA5B;AACA,WAAKX,4BAAL,CAAkC,KAAK5I,UAAvC,EAAmD;AAAnD,OACKwJ,IADL,CACWlM,SAAD,IAAe;AACrB,aAAKC,gBAAL,CAAsBkM,QAAtB,CAA+B/F,OAA/B,CAAuC,CAACrF,OAAD,EAAUzC,KAAV,KAAoB;AACvD,cAAI4D,EAAJ;;AACA,gBAAMuB,KAAK,GAAG1C,OAAd;AACA,WAACmB,EAAE,GAAGuB,KAAK,CAACnI,GAAN,CAAU,UAAV,CAAN,MAAiC,IAAjC,IAAyC4G,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACkK,QAAH,CAAY,KAAKpM,SAAL,CAAe1B,KAAf,CAAZ,CAAlE,CAHuD,CAG+C;AACzG,SAJD;;AAKA,YAAI,KAAKjD,OAAL,CAAagR,OAAjB,EAA0B;AACtBlL,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,KAAK/F,OAAL,CAAaE,KAA3C;AACA,eAAKsG,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACA,eAAKzH,MAAL,CAAYiJ,IAAZ,CAAiB,uEAAjB;AACA;AACH,SALD,MAMK;AACD7J,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,KAAK/F,OAAL,CAAaE,KAA3C;AACA,eAAK+Q,iBAAL;AACH;AACJ,OAjBD,EAkBKC,KAlBL,CAkBYzD,KAAD,IAAW;AAClB,aAAKjH,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACArI,QAAAA,OAAO,CAAC2H,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;AACA,aAAK/G,MAAL,CAAY+G,KAAZ,CAAkB,0BAAlB;AACH,OAtBD;AAuBH,KArCD,MAsCK;AACD,WAAKzN,OAAL,CAAaoK,UAAb,CAAwB;AACpB/B,QAAAA,YAAY,EAAE,KAAKV,KADC;AAEpB2C,QAAAA,WAAW,EAAE,KAAKC,QAFE;AAGpBjC,QAAAA,cAAc,EAAE,KAAKkC,WAHD;AAIpBlB,QAAAA,OAAO,EAAE,KAAKxK,YAAL,KAAsB,KAAtB,GAA8B,CAA9B,GAAkC;AAJvB,OAAxB;AAMA,WAAK0H,iBAAL,CAAuBoK,IAAvB,CAA4B,eAA5B;AACA,WAAKX,4BAAL,CAAkC,KAAK5I,UAAvC,EACKwJ,IADL,CACWlM,SAAD,IAAe;AACrB,aAAKC,gBAAL,CAAsBkM,QAAtB,CAA+B/F,OAA/B,CAAuC,CAACrF,OAAD,EAAUzC,KAAV,KAAoB;AACvD,cAAI4D,EAAJ;;AACA,gBAAMuB,KAAK,GAAG1C,OAAd;AACA,WAACmB,EAAE,GAAGuB,KAAK,CAACnI,GAAN,CAAU,UAAV,CAAN,MAAiC,IAAjC,IAAyC4G,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACkK,QAAH,CAAYpM,SAAS,CAAC1B,KAAD,CAArB,CAAlE,CAHuD,CAG0C;AACpG,SAJD;;AAKA,YAAI,KAAKjD,OAAL,CAAagR,OAAjB,EAA0B;AACtBlL,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,KAAK/F,OAAL,CAAaE,KAA3C;AACA,eAAKsG,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACA,eAAKzH,MAAL,CAAYiJ,IAAZ,CAAiB,uEAAjB;AACA;AACH,SALD,MAMK;AACD7J,UAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,KAAK/F,OAAL,CAAaE,KAA3C;AACA,eAAKiR,cAAL;AACH;AACJ,OAjBD,EAkBKD,KAlBL,CAkBYzD,KAAD,IAAW;AAClB,aAAKjH,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACArI,QAAAA,OAAO,CAAC2H,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;AACA,aAAK/G,MAAL,CAAY+G,KAAZ,CAAkB,0BAAlB;AACH,OAtBD;AAuBH;AACJ;;AACDwD,EAAAA,iBAAiB,GAAG;AAChB,QAAIpK,EAAJ,EAAQC,EAAR,EAAYoD,EAAZ,EAAgBqB,EAAhB,EAAoB6F,EAApB,EAAwBC,EAAxB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CC,EAA5C,EAAgDC,EAAhD,EAAoDC,EAApD,EAAwDC,EAAxD,EAA4DC,EAA5D,EAAgEC,EAAhE,EAAoEC,EAApE,EAAwEC,EAAxE,EAA4EC,EAA5E;;AACApM,IAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6B,KAAKjH,YAAlC;AACA,UAAMmL,IAAI,GAAG;AACTvB,MAAAA,WAAW,EAAE,CAAC7B,EAAE,GAAG,KAAK7G,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAN,MAA2C,IAA3C,IAAmD4G,EAAE,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,EAAE,CAAC3G,KADnF;AAETyI,MAAAA,cAAc,EAAE,CAAC7B,EAAE,GAAG,KAAK9G,OAAL,CAAaC,GAAb,CAAiB,gBAAjB,CAAN,MAA8C,IAA9C,IAAsD6G,EAAE,KAAK,KAAK,CAAlE,GAAsE,KAAK,CAA3E,GAA+EA,EAAE,CAAC5G,KAFzF;AAGT2I,MAAAA,aAAa,EAAE,CAACqB,EAAE,GAAG,KAAKlK,OAAL,CAAaC,GAAb,CAAiB,eAAjB,CAAN,MAA6C,IAA7C,IAAqDiK,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAAChK,KAHvF;AAIT4I,MAAAA,QAAQ,EAAE,CAACyC,EAAE,GAAG,KAAKvL,OAAL,CAAaC,GAAb,CAAiB,UAAjB,CAAN,MAAwC,IAAxC,IAAgDsL,EAAE,KAAK,KAAK,CAA5D,GAAgE,KAAK,CAArE,GAAyEA,EAAE,CAACrL,KAJ7E;AAKT6I,MAAAA,UAAU,EAAE,CAACqI,EAAE,GAAG,KAAKpR,OAAL,CAAaC,GAAb,CAAiB,YAAjB,CAAN,MAA0C,IAA1C,IAAkDmR,EAAE,KAAK,KAAK,CAA9D,GAAkE,KAAK,CAAvE,GAA2EA,EAAE,CAAClR,KALjF;AAMT8I,MAAAA,SAAS,EAAE,CAAC,CAACqI,EAAE,GAAG,KAAKrR,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAN,MAAyC,IAAzC,IAAiDoR,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAACnR,KAA9E,IAAuF,CAACoR,EAAE,GAAG,KAAKtR,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAN,MAAyC,IAAzC,IAAiDqR,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAACpR,KAApK,GAA4K,EAN9K;AAOT+I,MAAAA,WAAW,EAAE,CAACsI,EAAE,GAAG,KAAKvR,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAN,MAA2C,IAA3C,IAAmDsR,EAAE,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,EAAE,CAACrR,KAPnF;AAQTgJ,MAAAA,SAAS,EAAE,CAACsI,EAAE,GAAG,KAAKxR,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAN,MAAyC,IAAzC,IAAiDuR,EAAE,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,EAAE,CAACtR,KAR/E;AASTiJ,MAAAA,aAAa,EAAE,CAACsI,EAAE,GAAG,KAAKzR,OAAL,CAAaC,GAAb,CAAiB,eAAjB,CAAN,MAA6C,IAA7C,IAAqDwR,EAAE,KAAK,KAAK,CAAjE,GAAqE,KAAK,CAA1E,GAA8EA,EAAE,CAACvR,KATvF;AAUTkJ,MAAAA,eAAe,EAAE,CAACsI,EAAE,GAAG,KAAK1R,OAAL,CAAaC,GAAb,CAAiB,iBAAjB,CAAN,MAA+C,IAA/C,IAAuDyR,EAAE,KAAK,KAAK,CAAnE,GAAuE,KAAK,CAA5E,GAAgFA,EAAE,CAACxR,KAV3F;AAWTmJ,MAAAA,YAAY,EAAE,CAACsI,EAAE,GAAG,KAAK3R,OAAL,CAAaC,GAAb,CAAiB,cAAjB,CAAN,MAA4C,IAA5C,IAAoD0R,EAAE,KAAK,KAAK,CAAhE,GAAoE,KAAK,CAAzE,GAA6EA,EAAE,CAACzR,KAXrF;AAYToJ,MAAAA,OAAO,EAAE,CAACsI,EAAE,GAAG,KAAK5R,OAAL,CAAaC,GAAb,CAAiB,SAAjB,CAAN,MAAuC,IAAvC,IAA+C2R,EAAE,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,EAAE,CAAC1R,KAZ3E;AAaTqJ,MAAAA,WAAW,EAAE,CAAC,CAACsI,EAAE,GAAG,KAAK7R,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAN,MAA2C,IAA3C,IAAmD4R,EAAE,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,EAAE,CAAC3R,KAAhF,IAAyF,CAAC4R,EAAE,GAAG,KAAK9R,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAN,MAA2C,IAA3C,IAAmD6R,EAAE,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,EAAE,CAAC5R,KAAxK,GAAgL,EAbpL;AAcTsJ,MAAAA,WAAW,EAAE,CAACuI,EAAE,GAAG,KAAK/R,OAAL,CAAaC,GAAb,CAAiB,aAAjB,CAAN,MAA2C,IAA3C,IAAmD8R,EAAE,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,EAAE,CAAC7R,KAdnF;AAeTqI,MAAAA,sBAAsB,EAAE,CAACyJ,EAAE,GAAG,KAAKhS,OAAL,CAAaC,GAAb,CAAiB,wBAAjB,CAAN,MAAsD,IAAtD,IAA8D+R,EAAE,KAAK,KAAK,CAA1E,GAA8E,KAAK,CAAnF,GAAuFA,EAAE,CAAC9R,KAfzG;AAgBTuI,MAAAA,YAAY,EAAE,CAACwJ,EAAE,GAAG,KAAKjS,OAAL,CAAaC,GAAb,CAAiB,cAAjB,CAAN,MAA4C,IAA5C,IAAoDgS,EAAE,KAAK,KAAK,CAAhE,GAAoE,KAAK,CAAzE,GAA6EA,EAAE,CAAC/R,KAhBrF;AAiBTmI,MAAAA,YAAY,EAAE,KAAKV,KAjBV;AAkBT2C,MAAAA,WAAW,EAAE,KAAKC,QAlBT;AAmBTjC,MAAAA,cAAc,EAAE,KAAKkC,WAnBZ;AAoBT+D,MAAAA,KAAK,EAAE,KAAKvG,eAAL,CAAqBuG;AApBnB,KAAb;AAsBA,UAAM4D,gBAAgB,GAAG,KAAKnS,OAAL,CAAaC,GAAb,CAAiB,cAAjB,CAAzB;;AACA,QAAIkS,gBAAJ,EAAsB;AAClB,YAAMC,cAAc,GAAGD,gBAAgB,CAACjS,KAAxC;AACA,YAAMmS,aAAa,GAAG,CAACH,EAAE,GAAG,KAAKlS,OAAL,CAAaC,GAAb,CAAiB,wBAAjB,CAAN,MAAsD,IAAtD,IAA8DiS,EAAE,KAAK,KAAK,CAA1E,GAA8E,KAAK,CAAnF,GAAuFA,EAAE,CAAChS,KAAhH;;AACA,UAAIkS,cAAc,IAAIC,aAAtB,EAAqC;AACjC,cAAMC,SAAS,GAAG,IAAIrK,IAAJ,CAASmK,cAAT,CAAlB;AACA,cAAMtF,QAAQ,GAAG,IAAI7E,IAAJ,CAASoK,aAAT,CAAjB;;AACA,YAAIC,SAAS,IAAIxF,QAAjB,EAA2B;AACvB,cAAIuF,aAAa,GAAG,KAAKlK,YAAzB,EAAuC;AACnC;AACA,iBAAKzB,MAAL,CAAY+G,KAAZ,CAAkB,sDAAlB;AACA;AACH;;AACD3H,UAAAA,OAAO,CAACC,GAAR,CAAY,qBAAZ,EAAmCkE,IAAnC;AACA,eAAKzD,iBAAL,CAAuBoK,IAAvB,CAA4B,eAA5B;AACA,eAAKrK,mBAAL,CAAyBgM,UAAzB,CAAoCtI,IAApC,EAA0CxC,SAA1C,CAAqD6F,GAAD,IAAS;AACzDxH,YAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyBkE,IAAzB;;AACA,gBAAIqD,GAAG,CAACC,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,mBAAK/G,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACA,mBAAKzH,MAAL,CAAY8L,OAAZ,CAAoB,mCAApB;AACA,oBAAM3K,KAAK,GAAG;AACVF,gBAAAA,KAAK,EAAE,KAAKA,KADF;AAEV2C,gBAAAA,WAAW,EAAE,KAAKC,QAFR;AAGVwD,gBAAAA,OAAO,EAAE,KAAKvD;AAHJ,eAAd;AAKA,mBAAK/D,MAAL,CAAYmB,QAAZ,CAAqB,CAAC,wDAAD,CAArB,EAAiF;AAAEJ,gBAAAA,WAAW,EAAE;AAAEG,kBAAAA,KAAK,EAAE,KAAKA;AAAd;AAAf,eAAjF;AACA,mBAAKpB,mBAAL,CAAyBkM,uBAAzB,CAAiD,KAAK9K,KAAtD;AACH,aAVD,MAWK;AACD,mBAAKnB,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACArI,cAAAA,OAAO,CAACC,GAAR,CAAY,EAAZ,EAAgBuH,GAAG,CAACoF,OAApB;AACA,mBAAKhM,MAAL,CAAY+G,KAAZ,CAAkB,EAAlB,EAAsB,sBAAtB;AACH;AACJ,WAlBD,EAkBIA,KAAD,IAAW;AACV,iBAAKjH,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACArI,YAAAA,OAAO,CAAC2H,KAAR,CAAc,kBAAd,EAAkCA,KAAlC;AACA,iBAAK/G,MAAL,CAAY+G,KAAZ,CAAkB,EAAlB,EAAsB,sBAAtB;AACH,WAtBD;AAuBH,SA/BD,MAgCK;AACD,eAAKjH,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACA,eAAKzH,MAAL,CAAY+G,KAAZ,CAAkB,oDAAlB;AACA3H,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACH;AACJ;AACJ;AACJ;;AACDoL,EAAAA,cAAc,GAAG;AACb,QAAItK,EAAJ;;AACA,UAAMsL,gBAAgB,GAAG,KAAKnS,OAAL,CAAaC,GAAb,CAAiB,cAAjB,CAAzB;;AACA,QAAIkS,gBAAJ,EAAsB;AAClB,YAAMC,cAAc,GAAGD,gBAAgB,CAACjS,KAAxC;AACA,YAAMmS,aAAa,GAAG,CAACxL,EAAE,GAAG,KAAK7G,OAAL,CAAaC,GAAb,CAAiB,wBAAjB,CAAN,MAAsD,IAAtD,IAA8D4G,EAAE,KAAK,KAAK,CAA1E,GAA8E,KAAK,CAAnF,GAAuFA,EAAE,CAAC3G,KAAhH;;AACA,UAAIkS,cAAc,IAAIC,aAAtB,EAAqC;AACjC,cAAMC,SAAS,GAAG,IAAIrK,IAAJ,CAASmK,cAAT,CAAlB;AACA,cAAMtF,QAAQ,GAAG,IAAI7E,IAAJ,CAASoK,aAAT,CAAjB;;AACA,YAAIC,SAAS,IAAIxF,QAAjB,EAA2B;AACvB,cAAIuF,aAAa,GAAG,KAAKlK,YAAzB,EAAuC;AACnC;AACA,iBAAKzB,MAAL,CAAY+G,KAAZ,CAAkB,sDAAlB;AACA;AACH;;AACD3H,UAAAA,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkC,KAAK/F,OAAL,CAAaE,KAA/C;AACA,eAAKqG,mBAAL,CAAyB4K,cAAzB,CAAwC,KAAKnR,OAAL,CAAaE,KAArD,EAA4DuH,SAA5D,CAAuE6F,GAAD,IAAS;AAC3ExH,YAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BuH,GAA9B;AACA,iBAAK9G,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;;AACA,gBAAIb,GAAG,CAACC,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,mBAAK7G,MAAL,CAAY8L,OAAZ,CAAoB,EAApB,EAAwB,qCAAxB;AACA,mBAAK/L,MAAL,CAAYmB,QAAZ,CAAqB,CAAC,wDAAD,CAArB,EAAiF;AAAEJ,gBAAAA,WAAW,EAAE;AAAEG,kBAAAA,KAAK,EAAE,KAAKA;AAAd;AAAf,eAAjF;AACA,mBAAKpB,mBAAL,CAAyBkM,uBAAzB,CAAiD,KAAK9K,KAAtD;AACH,aAJD,MAKK;AACD,mBAAKjB,MAAL,CAAY+G,KAAZ,CAAkB,EAAlB,EAAsB,sBAAtB;AACH;AACJ,WAXD,EAWIA,KAAD,IAAW;AACV,iBAAKjH,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACArI,YAAAA,OAAO,CAAC2H,KAAR,CAAc,4BAAd,EAA4CA,KAA5C;AACA,iBAAK/G,MAAL,CAAY+G,KAAZ,CAAkB,EAAlB,EAAsB,sBAAtB;AACH,WAfD;AAgBH,SAvBD,MAwBK;AACD,eAAKjH,iBAAL,CAAuB2H,IAAvB,CAA4B,eAA5B;AACA,eAAKzH,MAAL,CAAY+G,KAAZ,CAAkB,oDAAlB;AACA3H,UAAAA,OAAO,CAACC,GAAR,CAAY,0BAAZ;AACH;AACJ;AACJ;AACJ;;AACD4M,EAAAA,kBAAkB,GAAG;AACjB,SAAKlM,MAAL,CAAYmB,QAAZ,CAAqB,CAAE,wDAAF,CAArB,EAAiF;AAAEJ,MAAAA,WAAW,EAAE;AAAEG,QAAAA,KAAK,EAAE,KAAKA;AAAd;AAAf,KAAjF;AACH;;AA/uBoC;;AAivBzCvB,2BAA2B,CAACwM,IAA5B,GAAmC,SAASC,mCAAT,CAA6CC,CAA7C,EAAgD;AAAE,SAAO,KAAKA,CAAC,IAAI1M,2BAAV,EAAuCnJ,EAAE,CAAC8V,iBAAH,CAAqB7V,EAAE,CAAC8V,WAAxB,CAAvC,EAA6E/V,EAAE,CAAC8V,iBAAH,CAAqB5V,EAAE,CAAC8V,mBAAxB,CAA7E,EAA2HhW,EAAE,CAAC8V,iBAAH,CAAqB3V,EAAE,CAAC8V,iBAAxB,CAA3H,EAAuKjW,EAAE,CAAC8V,iBAAH,CAAqB1V,EAAE,CAAC8V,MAAxB,CAAvK,EAAwMlW,EAAE,CAAC8V,iBAAH,CAAqBzV,EAAE,CAAC8V,aAAxB,CAAxM,EAAgPnW,EAAE,CAAC8V,iBAAH,CAAqBxV,EAAE,CAAC8V,QAAxB,CAAhP,EAAmRpW,EAAE,CAAC8V,iBAAH,CAAqB1V,EAAE,CAACiW,cAAxB,CAAnR,CAAP;AAAqU,CAA1Z;;AACAlN,2BAA2B,CAACmN,IAA5B,GAAmC,aAActW,EAAE,CAACuW,iBAAH,CAAqB;AAAE/D,EAAAA,IAAI,EAAErJ,2BAAR;AAAqCqN,EAAAA,SAAS,EAAE,CAAC,CAAC,0BAAD,CAAD,CAAhD;AAAgFC,EAAAA,SAAS,EAAE,SAASC,iCAAT,CAA2C9V,EAA3C,EAA+CC,GAA/C,EAAoD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AAC3NZ,MAAAA,EAAE,CAAC2W,WAAH,CAAenW,GAAf,EAAoB,CAApB;AACH;;AAAC,QAAII,EAAE,GAAG,CAAT,EAAY;AACV,UAAIoU,EAAJ;;AACAhV,MAAAA,EAAE,CAAC4W,cAAH,CAAkB5B,EAAE,GAAGhV,EAAE,CAAC6W,WAAH,EAAvB,MAA6ChW,GAAG,CAACoO,YAAJ,GAAmB+F,EAAhE;AACH;AAAE,GAL+D;AAK7D8B,EAAAA,KAAK,EAAE,GALsD;AAKjDC,EAAAA,IAAI,EAAE,EAL2C;AAKvCC,EAAAA,MAAM,EAAE,CAAC,CAAC,CAAD,EAAI,iBAAJ,CAAD,EAAyB,CAAC,CAAD,EAAI,KAAJ,CAAzB,EAAqC,CAAC,CAAD,EAAI,QAAJ,EAAc,aAAd,EAA6B,cAA7B,CAArC,EAAmF,CAAC,CAAD,EAAI,MAAJ,CAAnF,EAAgG,CAAC,CAAD,EAAI,aAAJ,EAAmB,YAAnB,EAAiC,YAAjC,EAA+C,aAA/C,EAA8D,YAA9D,EAA4E,aAA5E,EAA2F,MAA3F,CAAhG,EAAoM,CAAC,CAAD,EAAI,WAAJ,CAApM,EAAsN,CAAC,CAAD,EAAI,cAAJ,EAAoB,CAApB,EAAuB,WAAvB,EAAoC,UAApC,CAAtN,EAAuQ,CAAC,CAAD,EAAI,MAAJ,CAAvQ,EAAoR,CAAC,CAAD,EAAI,oBAAJ,CAApR,EAA+S,CAAC,OAAD,EAAU,oBAAV,EAAgC,CAAhC,EAAmC,MAAnC,CAA/S,EAA2V,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAA3V,EAA0X,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,SAA1B,CAA1X,EAAga,CAAC,IAAD,EAAO,aAAP,EAAsB,iBAAtB,EAAyC,aAAzC,EAAwD,UAAxD,EAAoE,EAApE,EAAwE,CAAxE,EAA2E,cAA3E,EAA2F,iBAA3F,EAA8G,CAA9G,EAAiH,SAAjH,EAA4H,QAA5H,CAAha,EAAuiB,CAAC,UAAD,EAAa,EAAb,EAAiB,UAAjB,EAA6B,EAA7B,EAAiC,CAAjC,EAAoC,OAApC,CAAviB,EAAqlB,CAAC,CAAD,EAAI,OAAJ,EAAa,CAAb,EAAgB,OAAhB,EAAyB,SAAzB,CAArlB,EAA0nB,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,SAAxB,CAA1nB,EAA8pB,CAAC,IAAD,EAAO,WAAP,EAAoB,iBAApB,EAAuC,WAAvC,EAAoD,UAApD,EAAgE,EAAhE,EAAoE,CAApE,EAAuE,cAAvE,EAAuF,iBAAvF,EAA0G,CAA1G,EAA6G,SAA7G,CAA9pB,EAAuxB,CAAC,UAAD,EAAa,EAAb,EAAiB,UAAjB,EAA6B,EAA7B,EAAiC,CAAjC,EAAoC,OAApC,CAAvxB,EAAq0B,CAAC,CAAD,EAAI,OAAJ,EAAa,UAAb,EAAyB,CAAzB,EAA4B,OAA5B,EAAqC,SAArC,CAAr0B,EAAs3B,CAAC,OAAD,EAAU,MAAV,EAAkB,CAAlB,EAAqB,MAArB,CAAt3B,EAAo5B,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,SAA1B,CAAp5B,EAA07B,CAAC,MAAD,EAAS,UAAT,EAAqB,IAArB,EAA2B,aAA3B,EAA0C,iBAA1C,EAA6D,aAA7D,EAA4E,UAA5E,EAAwF,EAAxF,EAA4F,aAA5F,EAA2G,aAA3G,EAA0H,CAA1H,EAA6H,cAA7H,EAA6I,iBAA7I,EAAgK,CAAhK,EAAmK,UAAnK,CAA17B,EAA0mC,CAAC,OAAD,EAAU,SAAV,EAAqB,CAArB,EAAwB,MAAxB,CAA1mC,EAA2oC,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,SAA1B,CAA3oC,EAAirC,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,aAApC,EAAmD,UAAnD,EAA+D,EAA/D,EAAmE,aAAnE,EAAkF,iBAAlF,EAAqG,CAArG,EAAwG,cAAxG,EAAwH,iBAAxH,EAA2I,CAA3I,EAA8I,UAA9I,CAAjrC,EAA40C,CAAC,KAAD,EAAQ,wBAAR,EAAkC,CAAlC,EAAqC,SAArC,CAA50C,EAA63C,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAjB,EAA0B,8BAA1B,EAA0D,iBAA1D,EAA6E,wBAA7E,EAAuG,UAAvG,EAAmH,EAAnH,EAAuH,CAAvH,EAA0H,KAA1H,EAAiI,UAAjI,EAA6I,QAA7I,EAAuJ,OAAvJ,EAAgK,CAAhK,EAAmK,MAAnK,CAA73C,EAAyiD,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAjB,EAA0B,8BAA1B,EAA0D,iBAA1D,EAA6E,wBAA7E,EAAuG,UAAvG,EAAmH,EAAnH,EAAuH,CAAvH,EAA0H,MAA1H,CAAziD,EAA4qD,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,SAA3B,CAA5qD,EAAmtD,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAjB,EAA0B,8BAA1B,EAA0D,iBAA1D,EAA6E,cAA7E,EAA6F,UAA7F,EAAyG,EAAzG,EAA6G,CAA7G,EAAgH,KAAhH,EAAuH,UAAvH,EAAmI,OAAnI,EAA4I,CAA5I,EAA+I,MAA/I,CAAntD,EAA22D,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAjB,EAA0B,8BAA1B,EAA0D,iBAA1D,EAA6E,cAA7E,EAA6F,UAA7F,EAAyG,EAAzG,EAA6G,CAA7G,EAAgH,MAAhH,CAA32D,EAAo+D,CAAC,KAAD,EAAQ,eAAR,EAAyB,CAAzB,EAA4B,SAA5B,CAAp+D,EAA4gE,CAAC,IAAD,EAAO,eAAP,EAAwB,MAAxB,EAAgC,MAAhC,EAAwC,iBAAxC,EAA2D,eAA3D,EAA4E,UAA5E,EAAwF,EAAxF,EAA4F,CAA5F,EAA+F,cAA/F,EAA+G,iBAA/G,EAAkI,CAAlI,EAAqI,SAArI,EAAgJ,UAAhJ,CAA5gE,EAAyqE,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,SAAxB,CAAzqE,EAA6sE,CAAC,MAAD,EAAS,QAAT,EAAmB,iBAAnB,EAAsC,WAAtC,EAAmD,UAAnD,EAA+D,EAA/D,EAAmE,aAAnE,EAAkF,cAAlF,EAAkG,CAAlG,EAAqG,cAArG,EAAqH,iBAArH,EAAwI,CAAxI,EAA2I,UAA3I,CAA7sE,EAAq2E,CAAC,KAAD,EAAQ,eAAR,EAAyB,CAAzB,EAA4B,SAA5B,CAAr2E,EAA64E,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,eAApC,EAAqD,UAArD,EAAiE,EAAjE,EAAqE,aAArE,EAAoF,kBAApF,EAAwG,CAAxG,EAA2G,cAA3G,EAA2H,iBAA3H,EAA8I,CAA9I,EAAiJ,UAAjJ,CAA74E,EAA2iF,CAAC,KAAD,EAAQ,gBAAR,EAA0B,CAA1B,EAA6B,SAA7B,CAA3iF,EAAolF,CAAC,iBAAD,EAAoB,gBAApB,EAAsC,UAAtC,EAAkD,EAAlD,EAAsD,MAAtD,EAA8D,GAA9D,EAAmE,MAAnE,EAA2E,IAA3E,EAAiF,aAAjF,EAAgG,mBAAhG,EAAqH,CAArH,EAAwH,cAAxH,EAAwI,iBAAxI,EAA2J,CAA3J,EAA8J,UAA9J,EAA0K,OAA1K,CAAplF,EAAwwF,CAAC,CAAD,EAAI,MAAJ,CAAxwF,EAAqxF,CAAC,KAAD,EAAQ,iBAAR,EAA2B,CAA3B,EAA8B,SAA9B,CAArxF,EAA+zF,CAAC,iBAAD,EAAoB,iBAApB,EAAuC,UAAvC,EAAmD,EAAnD,EAAuD,MAAvD,EAA+D,GAA/D,EAAoE,MAApE,EAA4E,IAA5E,EAAkF,aAAlF,EAAiG,oBAAjG,EAAuH,CAAvH,EAA0H,cAA1H,EAA0I,iBAA1I,EAA6J,CAA7J,EAAgK,UAAhK,EAA4K,OAA5K,CAA/zF,EAAq/F,CAAC,KAAD,EAAQ,YAAR,EAAsB,CAAtB,EAAyB,SAAzB,CAAr/F,EAA0hG,CAAC,iBAAD,EAAoB,YAApB,EAAkC,UAAlC,EAA8C,EAA9C,EAAkD,MAAlD,EAA0D,GAA1D,EAA+D,MAA/D,EAAuE,IAAvE,EAA6E,aAA7E,EAA4F,eAA5F,EAA6G,CAA7G,EAAgH,cAAhH,EAAgI,iBAAhI,EAAmJ,CAAnJ,EAAsJ,UAAtJ,EAAkK,OAAlK,CAA1hG,EAAssG,CAAC,KAAD,EAAQ,UAAR,EAAoB,CAApB,EAAuB,SAAvB,CAAtsG,EAAyuG,CAAC,MAAD,EAAS,QAAT,EAAmB,iBAAnB,EAAsC,UAAtC,EAAkD,UAAlD,EAA8D,EAA9D,EAAkE,aAAlE,EAAiF,aAAjF,EAAgG,CAAhG,EAAmG,cAAnG,EAAmH,iBAAnH,EAAsI,CAAtI,EAAyI,UAAzI,CAAzuG,EAA+3G,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,SAA3B,CAA/3G,EAAs6G,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,cAApC,EAAoD,UAApD,EAAgE,EAAhE,EAAoE,aAApE,EAAmF,kBAAnF,EAAuG,CAAvG,EAA0G,cAA1G,EAA0H,iBAA1H,EAA6I,CAA7I,EAAgJ,UAAhJ,CAAt6G,EAAmkH,CAAC,eAAD,EAAkB,aAAlB,CAAnkH,EAAqmH,CAAC,CAAD,EAAI,MAAJ,EAAY,MAAZ,CAArmH,EAA0nH,CAAC,OAAD,EAAU,UAAV,EAAsB,CAAtB,EAAyB,eAAzB,EAA0C,CAA1C,EAA6C,OAA7C,EAAsD,SAAtD,CAA1nH,EAA4rH,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,gDAA5B,EAA8E,CAA9E,EAAiF,OAAjF,EAA0F,CAA1F,EAA6F,MAA7F,CAA5rH,EAAkyH,CAAC,CAAD,EAAI,aAAJ,EAAmB,MAAnB,CAAlyH,EAA8zH,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,sBAA5B,EAAoD,CAApD,EAAuD,MAAvD,CAA9zH,EAA83H,CAAC,CAAD,EAAI,KAAJ,EAAW,WAAX,EAAwB,CAAxB,EAA2B,OAA3B,CAA93H,EAAm6H,CAAC,CAAD,EAAI,oBAAJ,CAAn6H,EAA87H,CAAC,MAAD,EAAS,OAAT,EAAkB,IAAlB,EAAwB,KAAxB,EAA+B,MAA/B,EAAuC,iBAAvC,EAA0D,OAA1D,EAAmE,KAAnE,EAA0E,CAA1E,EAA6E,SAA7E,EAAwF,gBAAxF,EAA0G,eAA1G,CAA97H,EAA0jI,CAAC,KAAD,EAAQ,KAAR,EAAe,CAAf,EAAkB,MAAlB,CAA1jI,EAAqlI,CAAC,MAAD,EAAS,OAAT,EAAkB,IAAlB,EAAwB,WAAxB,EAAqC,MAArC,EAA6C,iBAA7C,EAAgE,OAAhE,EAAyE,WAAzE,EAAsF,CAAtF,EAAyF,SAAzF,EAAoG,gBAApG,EAAsH,eAAtH,CAArlI,EAA6tI,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,MAAxB,CAA7tI,EAA8vI,CAAC,CAAD,EAAI,OAAJ,CAA9vI,EAA4wI,CAAC,CAAD,EAAI,OAAJ,EAAa,UAAb,CAA5wI,EAAsyI,CAAC,CAAD,EAAI,MAAJ,CAAtyI,EAAmzI,CAAC,CAAD,EAAI,SAAJ,CAAnzI,EAAm0I,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,wBAApC,EAA8D,UAA9D,EAA0E,EAA1E,EAA8E,CAA9E,EAAiF,cAAjF,EAAiG,iBAAjG,EAAoH,CAApH,EAAuH,KAAvH,EAA8H,UAA9H,EAA0I,QAA1I,EAAoJ,OAApJ,CAAn0I,EAAi+I,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,wBAApC,EAA8D,UAA9D,EAA0E,EAA1E,EAA8E,CAA9E,EAAiF,cAAjF,EAAiG,iBAAjG,CAAj+I,EAAslJ,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,cAApC,EAAoD,UAApD,EAAgE,EAAhE,EAAoE,CAApE,EAAuE,cAAvE,EAAuF,iBAAvF,EAA0G,CAA1G,EAA6G,KAA7G,EAAoH,UAApH,EAAgI,OAAhI,CAAtlJ,EAAguJ,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,cAApC,EAAoD,UAApD,EAAgE,EAAhE,EAAoE,CAApE,EAAuE,cAAvE,EAAuF,iBAAvF,CAAhuJ,EAA20J,CAAC,CAAD,EAAI,iBAAJ,CAA30J,EAAm2J,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,EAAmB,CAAnB,EAAsB,eAAtB,CAAn2J,EAA24J,CAAC,CAAD,EAAI,WAAJ,CAA34J,EAA65J,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,CAA75J,EAAi7J,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAAj7J,EAAg9J,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,UAAxB,CAAh9J,EAAq/J,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,WAApC,EAAiD,aAAjD,EAAgE,aAAhE,EAA+E,CAA/E,EAAkF,cAAlF,EAAkG,iBAAlG,EAAqH,CAArH,EAAwH,UAAxH,CAAr/J,EAA0nK,CAAC,KAAD,EAAQ,UAAR,EAAoB,CAApB,EAAuB,UAAvB,CAA1nK,EAA8pK,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,UAApC,EAAgD,aAAhD,EAA+D,YAA/D,EAA6E,CAA7E,EAAgF,cAAhF,EAAgG,iBAAhG,EAAmH,CAAnH,EAAsH,UAAtH,CAA9pK,EAAiyK,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,UAA3B,CAAjyK,EAAy0K,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,cAApC,EAAoD,aAApD,EAAmE,gBAAnE,EAAqF,CAArF,EAAwF,cAAxF,EAAwG,iBAAxG,EAA2H,CAA3H,EAA8H,UAA9H,CAAz0K,EAAo9K,CAAC,KAAD,EAAQ,MAAR,EAAgB,CAAhB,EAAmB,UAAnB,CAAp9K,EAAo/K,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,OAA/B,EAAwC,8BAAxC,EAAwE,QAAxE,EAAkF,SAAlF,EAA6F,CAA7F,EAAgG,QAAhG,EAA0G,CAA1G,EAA6G,MAA7G,CAAp/K,EAA0mL,CAAC,MAAD,EAAS,MAAT,EAAiB,OAAjB,EAA0B,8BAA1B,EAA0D,UAA1D,EAAsE,EAAtE,EAA0E,CAA1E,EAA6E,OAA7E,EAAsF,CAAtF,EAAyF,MAAzF,CAA1mL,EAA4sL,CAAC,CAAD,EAAI,UAAJ,EAAgB,MAAhB,EAAwB,QAAxB,EAAkC,oBAAlC,EAAwD,MAAxD,CAA5sL,EAA6wL,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,6BAA5B,EAA2D,CAA3D,EAA8D,SAA9D,EAAyE,OAAzE,EAAkF,CAAlF,EAAqF,MAArF,CAA7wL,EAA22L,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,2CAA5B,EAAyE,CAAzE,EAA4E,OAA5E,EAAqF,CAArF,EAAwF,MAAxF,CAA32L,EAA48L,CAAC,UAAD,EAAa,EAAb,EAAiB,CAAjB,EAAoB,SAApB,EAA+B,MAA/B,CAA58L,EAAo/L,CAAC,aAAD,EAAgB,EAAhB,CAAp/L,EAAygM,CAAC,MAAD,EAAS,YAAT,EAAuB,CAAvB,EAA0B,KAA1B,CAAzgM,EAA2iM,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,QAA/B,EAAyC,SAAzC,EAAoD,CAApD,EAAuD,cAAvD,EAAuE,iBAAvE,EAA0F,CAA1F,EAA6F,QAA7F,CAA3iM,EAAmpM,CAAC,MAAD,EAAS,MAAT,EAAiB,UAAjB,EAA6B,EAA7B,EAAiC,CAAjC,EAAoC,cAApC,EAAoD,iBAApD,EAAuE,CAAvE,EAA0E,OAA1E,CAAnpM,EAAuuM,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,QAA5C,EAAsD,MAAtD,EAA8D,CAA9D,EAAiE,SAAjE,EAA4E,OAA5E,CAAvuM,EAA6zM,CAAC,CAAD,EAAI,KAAJ,EAAW,CAAX,EAAc,SAAd,CAA7zM,EAAu1M,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,QAA5C,EAAsD,oBAAtD,EAA4E,CAA5E,EAA+E,OAA/E,CAAv1M,EAAg7M,CAAC,CAAD,EAAI,KAAJ,EAAW,UAAX,EAAuB,MAAvB,CAAh7M,EAAg9M,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,QAA7B,EAAuC,iBAAvC,EAA0D,qBAA1D,EAAiF,CAAjF,EAAoF,OAApF,CAAh9M,EAA8iN,CAAC,CAAD,EAAI,KAAJ,EAAW,SAAX,EAAsB,MAAtB,CAA9iN,EAA6kN,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,MAA5C,CAA7kN,CAL+B;AAKomNC,EAAAA,QAAQ,EAAE,SAASC,oCAAT,CAA8CtW,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AACjvNZ,MAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,aAArB;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,CAAV;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,CAA7B;AACAhB,MAAAA,EAAE,CAACiB,UAAH,CAAc,UAAd,EAA0B,SAASkW,6DAAT,GAAyE;AAAE,eAAOtW,GAAG,CAAC6S,QAAJ,EAAP;AAAwB,OAA7H;AACA1T,MAAAA,EAAE,CAACgB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,IAAtB;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,kBAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAhB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBnE,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,CAA5E;AACAX,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkB/C,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,CAA5E;AACA/B,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAnE,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,QAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAhB,MAAAA,EAAE,CAACiB,UAAH,CAAc,QAAd,EAAwB,SAASmW,8DAAT,CAAwEjW,MAAxE,EAAgF;AAAE,eAAON,GAAG,CAAC6O,gBAAJ,CAAqBvO,MAArB,CAAP;AAAsC,OAAhJ;AACAnB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,wBAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkB1C,8CAAlB,EAAkE,CAAlE,EAAqE,CAArE,EAAwE,QAAxE,EAAkF,EAAlF;AACApC,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,MAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,oBAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBpC,8CAAlB,EAAkE,CAAlE,EAAqE,CAArE,EAAwE,QAAxE,EAAkF,EAAlF;AACA1C,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkB1B,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACApD,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,OAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAnE,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBzB,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACArD,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,UAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAnE,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBxB,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACAtD,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,sBAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBvB,6CAAlB,EAAiE,CAAjE,EAAoE,CAApE,EAAuE,OAAvE,EAAgF,EAAhF;AACAvD,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBZ,6CAAlB,EAAiE,CAAjE,EAAoE,CAApE,EAAuE,OAAvE,EAAgF,EAAhF;AACAlE,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,YAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBV,6CAAlB,EAAiE,CAAjE,EAAoE,CAApE,EAAuE,OAAvE,EAAgF,EAAhF;AACApE,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBJ,6CAAlB,EAAiE,CAAjE,EAAoE,CAApE,EAAuE,OAAvE,EAAgF,EAAhF;AACA1E,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,cAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,4BAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,QAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,SAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,QAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,QAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAnE,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,YAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAnE,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBH,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACA3E,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,aAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,UAAtB,EAAkC,EAAlC;AACAhB,MAAAA,EAAE,CAACiB,UAAH,CAAc,OAAd,EAAuB,SAASoW,+DAAT,GAA2E;AAAE,eAAOxW,GAAG,CAAC8N,OAAJ,CAAY,gBAAZ,CAAP;AAAuC,OAA3I;AACA3O,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBD,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACA7E,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,cAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,UAAtB,EAAkC,EAAlC;AACAhB,MAAAA,EAAE,CAACiB,UAAH,CAAc,OAAd,EAAuB,SAASqW,+DAAT,GAA2E;AAAE,eAAOzW,GAAG,CAAC8N,OAAJ,CAAY,iBAAZ,CAAP;AAAwC,OAA5I;AACA3O,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBM,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACApF,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,SAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,UAAtB,EAAkC,EAAlC;AACAhB,MAAAA,EAAE,CAACiB,UAAH,CAAc,OAAd,EAAuB,SAASsW,+DAAT,GAA2E;AAAE,eAAO1W,GAAG,CAAC8N,OAAJ,CAAY,YAAZ,CAAP;AAAmC,OAAvI;AACA3O,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBU,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACAxF,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,YAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAnE,MAAAA,EAAE,CAAC8E,UAAH,CAAc,EAAd,EAAkBa,2CAAlB,EAA+D,CAA/D,EAAkE,CAAlE,EAAqE,KAArE,EAA4E,EAA5E;AACA3F,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,EAAV,EAAc,YAAd;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAnE,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACmE,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAnE,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAhB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,EAAlB,EAAsB,IAAtB,EAA4B,EAA5B;AACAhB,MAAAA,EAAE,CAACyB,MAAH,CAAU,GAAV,EAAe,qBAAf;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,GAAd,EAAmByC,4CAAnB,EAAiE,EAAjE,EAAqE,EAArE,EAAyE,KAAzE,EAAgF,EAAhF;AACAvH,MAAAA,EAAE,CAAC8E,UAAH,CAAc,GAAd,EAAmBgD,+CAAnB,EAAoE,CAApE,EAAuE,CAAvE,EAA0E,QAA1E,EAAoF,EAApF;AACA9H,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACgB,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAhB,MAAAA,EAAE,CAAC8E,UAAH,CAAc,GAAd,EAAmBqD,+CAAnB,EAAoE,CAApE,EAAuE,CAAvE,EAA0E,QAA1E,EAAoF,EAApF;AACAnI,MAAAA,EAAE,CAACgB,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAhB,MAAAA,EAAE,CAACiB,UAAH,CAAc,OAAd,EAAuB,SAASuW,8DAAT,GAA0E;AAAE,eAAO3W,GAAG,CAAC6U,kBAAJ,EAAP;AAAkC,OAArI;AACA1V,MAAAA,EAAE,CAACyB,MAAH,CAAU,GAAV,EAAe,QAAf;AACAzB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACAxB,MAAAA,EAAE,CAACwB,YAAH;AACH;;AAAC,QAAIZ,EAAE,GAAG,CAAT,EAAY;AACV,UAAI6W,QAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACA,UAAIC,QAAJ;AACA7X,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAACkD,kBAAH,CAAsB,EAAtB,EAA0BrC,GAAG,CAAC4G,KAA9B,EAAqC,cAArC;AACAzH,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,WAAd,EAA2Bf,GAAG,CAACkC,OAA/B;AACA/C,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,EAAEf,GAAG,CAACyJ,WAAJ,IAAmBzJ,GAAG,CAACgB,YAAJ,KAAqB,WAA1C,CAAtB;AACA7B,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,EAAEf,GAAG,CAACyJ,WAAJ,IAAmBzJ,GAAG,CAACgB,YAAJ,KAAqB,KAA1C,CAAtB;AACA7B,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4BvH,GAAG,CAACgB,YAAJ,IAAoB,KAApB,IAA6B,CAAChB,GAAG,CAACoD,UAA9D,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuBzP,GAAvB,EAA4BxH,GAAG,CAACoD,UAAhC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,OAAd,EAAuB,IAAvB;AACA5B,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyBf,GAAG,CAAC0P,UAA7B;AACAvQ,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4BvH,GAAG,CAACgB,YAAJ,IAAoB,KAApB,IAA6B,CAAChB,GAAG,CAACoD,UAA9D,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuBzP,GAAvB,EAA4BxH,GAAG,CAACoD,UAAhC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,OAAd,EAAuB,IAAvB;AACA5B,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyBf,GAAG,CAACoQ,QAA7B;AACAjR,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,EAAE,CAAC6V,QAAQ,GAAG5W,GAAG,CAACkC,OAAJ,CAAYC,GAAZ,CAAgB,aAAhB,CAAZ,KAA+C,IAA/C,GAAsD,IAAtD,GAA6DyU,QAAQ,CAACxU,KAAxE,KAAkF,CAACpC,GAAG,CAACoD,UAA7G;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0Bf,GAAG,CAACoD,UAA9B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAAC,CAAC8V,QAAQ,GAAG7W,GAAG,CAACkC,OAAJ,CAAYC,GAAZ,CAAgB,aAAhB,CAAZ,KAA+C,IAA/C,GAAsD,IAAtD,GAA6D0U,QAAQ,CAACzS,MAAvE,MAAmF,CAACyS,QAAQ,GAAG7W,GAAG,CAACkC,OAAJ,CAAYC,GAAZ,CAAgB,aAAhB,CAAZ,KAA+C,IAA/C,GAAsD,IAAtD,GAA6D0U,QAAQ,CAACzS,MAAT,IAAmB,IAAnB,GAA0B,IAA1B,GAAiCyS,QAAQ,CAACzS,MAAT,CAAgB8D,iBAAjM,CAAtB;AACA/I,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0Bf,GAAG,CAACoD,UAA9B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAAC,CAAC+V,QAAQ,GAAG9W,GAAG,CAACkC,OAAJ,CAAYC,GAAZ,CAAgB,aAAhB,CAAZ,KAA+C,IAA/C,GAAsD,IAAtD,GAA6D2U,QAAQ,CAAC1S,MAAvE,MAAmF,CAAC0S,QAAQ,GAAG9W,GAAG,CAACkC,OAAJ,CAAYC,GAAZ,CAAgB,aAAhB,CAAZ,KAA+C,IAA/C,GAAsD,IAAtD,GAA6D2U,QAAQ,CAAC1S,MAAT,IAAmB,IAAnB,GAA0B,IAA1B,GAAiC0S,QAAQ,CAAC1S,MAAT,CAAgB8D,iBAAjM,CAAtB;AACA/I,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsBf,GAAG,CAAC4G,KAAJ,KAAc,MAApC;AACAzH,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsBf,GAAG,CAAC4G,KAAJ,KAAc,MAApC;AACAzH,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsBf,GAAG,CAAC4G,KAAJ,KAAc,MAApC;AACAzH,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsBf,GAAG,CAAC4G,KAAJ,KAAc,MAApC;AACAzH,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuBxP,GAAvB,EAA4BzH,GAAG,CAACoD,UAAhC,CAAzB,EAAsE,UAAtE,EAAkFpD,GAAG,CAACoD,UAAtF;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,OAAd,EAAuB,IAAvB;AACA5B,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0Bf,GAAG,CAACoD,UAA9B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0Bf,GAAG,CAACoD,UAA9B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAAC,CAACgW,QAAQ,GAAG/W,GAAG,CAACkC,OAAJ,CAAYC,GAAZ,CAAgB,eAAhB,CAAZ,KAAiD,IAAjD,GAAwD,IAAxD,GAA+D4U,QAAQ,CAAC3S,MAAzE,MAAqF,CAAC2S,QAAQ,GAAG/W,GAAG,CAACkC,OAAJ,CAAYC,GAAZ,CAAgB,eAAhB,CAAZ,KAAiD,IAAjD,GAAwD,IAAxD,GAA+D4U,QAAQ,CAAC3S,MAAT,IAAmB,IAAnB,GAA0B,IAA1B,GAAiC2S,QAAQ,CAAC3S,MAAT,CAAgB8D,iBAArM,CAAtB;AACA/I,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0Bf,GAAG,CAACoD,UAA9B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAACf,GAAG,CAACoD,UAA3B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0Bf,GAAG,CAACoD,UAA9B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAACf,GAAG,CAACoD,UAA3B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0Bf,GAAG,CAACoD,UAA9B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAACf,GAAG,CAACoD,UAA3B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0Bf,GAAG,CAACoD,UAA9B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAAC,CAACiW,QAAQ,GAAGhX,GAAG,CAACkC,OAAJ,CAAYC,GAAZ,CAAgB,UAAhB,CAAZ,KAA4C,IAA5C,GAAmD,IAAnD,GAA0D6U,QAAQ,CAAC5S,MAApE,MAAgF,CAAC4S,QAAQ,GAAGhX,GAAG,CAACkC,OAAJ,CAAYC,GAAZ,CAAgB,UAAhB,CAAZ,KAA4C,IAA5C,GAAmD,IAAnD,GAA0D6U,QAAQ,CAAC5S,MAAT,IAAmB,IAAnB,GAA0B,IAA1B,GAAiC4S,QAAQ,CAAC5S,MAAT,CAAgBiE,aAA3L,CAAtB;AACAlJ,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyB5B,EAAE,CAAC8X,eAAH,CAAmB,EAAnB,EAAuB1P,GAAvB,EAA4B,CAACvH,GAAG,CAACoD,UAAjC,CAAzB;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,UAAd,EAA0Bf,GAAG,CAACoD,UAA9B;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,SAAd,EAAyBf,GAAG,CAAC8G,gBAAJ,CAAqBkM,QAA9C;AACA7T,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsBf,GAAG,CAAC8G,gBAAJ,CAAqBC,MAArB,GAA8B,CAA9B,IAAmC,CAAC/G,GAAG,CAACoD,UAA9D;AACAjE,MAAAA,EAAE,CAAC2B,SAAH,CAAa,CAAb;AACA3B,MAAAA,EAAE,CAAC4B,UAAH,CAAc,MAAd,EAAsB,CAACf,GAAG,CAACoD,UAA3B;AACH;AAAE,GAzR+D;AAyR7D8T,EAAAA,UAAU,EAAE,CAACxX,EAAE,CAACyX,gBAAJ,EAAsB/X,EAAE,CAACgY,aAAzB,EAAwChY,EAAE,CAACiY,oBAA3C,EAAiEjY,EAAE,CAACkY,kBAApE,EAAwF7X,EAAE,CAAC8X,IAA3F,EAAiG9X,EAAE,CAAC+X,OAApG,EAA6GpY,EAAE,CAACqY,0BAAhH,EAA4IrY,EAAE,CAACsY,eAA/I,EAAgKtY,EAAE,CAACuY,eAAnK,EAAoLvY,EAAE,CAACwY,iBAAvL,EAA0MxY,EAAE,CAACyY,cAA7M,EAA6NzY,EAAE,CAAC0Y,uBAAhO,EAAyPrY,EAAE,CAACsY,OAA5P,EAAqQ3Y,EAAE,CAAC4Y,oBAAxQ,EAA8R5Y,EAAE,CAAC6Y,mBAAjS,EAAsT7Y,EAAE,CAAC8Y,aAAzT,EAAwU9Y,EAAE,CAAC+Y,yBAA3U,EAAsW/Y,EAAE,CAACgZ,OAAzW,EAAkXhZ,EAAE,CAACiZ,aAArX,CAzRiD;AAyRoVC,EAAAA,MAAM,EAAE,CAAC,6hKAAD;AAzR5V,CAArB,CAAjD", "sourcesContent": ["import { Validators, } from '@angular/forms';\r\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"@angular/forms\";\r\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\r\nimport * as i3 from \"ngx-spinner\";\r\nimport * as i4 from \"@angular/router\";\r\nimport * as i5 from \"ngx-toastr\";\r\nimport * as i6 from \"@angular/common\";\r\nimport * as i7 from \"../../../../sidebar.component\";\r\nconst _c0 = [\"audioPlayer\"];\r\nconst _c1 = function () { return { standalone: true }; };\r\nfunction AddEditOpportunityComponent_div_13_Template(rf, ctx) { if (rf & 1) {\r\n    const _r20 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 55);\r\n    i0.ɵɵelementStart(1, \"input\", 56);\r\n    i0.ɵɵlistener(\"ngModelChange\", function AddEditOpportunityComponent_div_13_Template_input_ngModelChange_1_listener($event) { i0.ɵɵrestoreView(_r20); const ctx_r19 = i0.ɵɵnextContext(); return ctx_r19.onTypeChange($event); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(2, \"label\", 57);\r\n    i0.ɵɵtext(3, \"Job\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r0 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.selectedType)(\"ngModelOptions\", i0.ɵɵpureFunction0(2, _c1));\r\n} }\r\nfunction AddEditOpportunityComponent_div_14_Template(rf, ctx) { if (rf & 1) {\r\n    const _r22 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 55);\r\n    i0.ɵɵelementStart(1, \"input\", 58);\r\n    i0.ɵɵlistener(\"ngModelChange\", function AddEditOpportunityComponent_div_14_Template_input_ngModelChange_1_listener($event) { i0.ɵɵrestoreView(_r22); const ctx_r21 = i0.ɵɵnextContext(); return ctx_r21.onTypeChange($event); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(2, \"label\", 59);\r\n    i0.ɵɵtext(3, \"Grad Scheme\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r1 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.selectedType)(\"ngModelOptions\", i0.ɵɵpureFunction0(2, _c1));\r\n} }\r\nfunction AddEditOpportunityComponent_option_23_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"option\", 60);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const sector_r23 = ctx.$implicit;\r\n    i0.ɵɵproperty(\"value\", sector_r23.IN_id);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate(sector_r23.IN_name);\r\n} }\r\nfunction AddEditOpportunityComponent_option_30_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"option\", 61);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const roles_r24 = ctx.$implicit;\r\n    const ctx_r3 = i0.ɵɵnextContext();\r\n    let tmp_1_0;\r\n    i0.ɵɵproperty(\"value\", roles_r24.RO_id)(\"selected\", roles_r24.RO_id === ((tmp_1_0 = ctx_r3.jobForm.get(\"JB_roleId\")) == null ? null : tmp_1_0.value));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", roles_r24.RO_title, \"\");\r\n} }\r\nfunction AddEditOpportunityComponent_div_31_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 62);\r\n    i0.ɵɵtext(1, \" Please select a sector first. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_div_36_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 63);\r\n    i0.ɵɵtext(1, \" Error: Word limit exceeded!(10 Words) \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_div_41_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 63);\r\n    i0.ɵɵtext(1, \" Error: Word limit exceeded!(10 Words) \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_input_45_Template(rf, ctx) { if (rf & 1) {\r\n    const _r26 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"input\", 64);\r\n    i0.ɵɵlistener(\"change\", function AddEditOpportunityComponent_input_45_Template_input_change_0_listener() { i0.ɵɵrestoreView(_r26); const ctx_r25 = i0.ɵɵnextContext(); return ctx_r25.updateStartDateMin(); })(\"input\", function AddEditOpportunityComponent_input_45_Template_input_input_0_listener() { i0.ɵɵrestoreView(_r26); const ctx_r27 = i0.ɵɵnextContext(); return ctx_r27.checkInvalidDate(\"JB_applicationDeadline\"); });\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r7 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"min\", ctx_r7.minDate)(\"readonly\", ctx_r7.isReadonly);\r\n} }\r\nfunction AddEditOpportunityComponent_input_46_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"input\", 65);\r\n} }\r\nfunction AddEditOpportunityComponent_input_50_Template(rf, ctx) { if (rf & 1) {\r\n    const _r29 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"input\", 66);\r\n    i0.ɵɵlistener(\"input\", function AddEditOpportunityComponent_input_50_Template_input_input_0_listener() { i0.ɵɵrestoreView(_r29); const ctx_r28 = i0.ɵɵnextContext(); return ctx_r28.checkInvalidDate(\"JB_startDate\"); });\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r9 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"min\", ctx_r9.minStartDate)(\"readonly\", ctx_r9.isReadonly);\r\n} }\r\nfunction AddEditOpportunityComponent_input_51_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"input\", 67);\r\n} }\r\nfunction AddEditOpportunityComponent_div_72_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 63);\r\n    i0.ɵɵtext(1, \" Error: Word limit exceeded!(10 Words) \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_div_77_div_3_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 63);\r\n    i0.ɵɵtext(1, \" Error: Character limit exceeded! (500 Characters) \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_div_77_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵelementStart(1, \"span\", 68);\r\n    i0.ɵɵtext(2);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(3, AddEditOpportunityComponent_div_77_div_3_Template, 2, 0, \"div\", 22);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r12 = i0.ɵɵnextContext();\r\n    let tmp_1_0;\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.descriptionCharCount, \"/500\");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r12.jobForm.get(\"JB_description\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.characterLimitExceeded);\r\n} }\r\nfunction AddEditOpportunityComponent_div_82_div_3_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 63);\r\n    i0.ɵɵtext(1, \" Error: Character limit exceeded! (500 Characters) \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_div_82_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵelementStart(1, \"span\", 68);\r\n    i0.ɵɵtext(2);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(3, AddEditOpportunityComponent_div_82_div_3_Template, 2, 0, \"div\", 22);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r13 = i0.ɵɵnextContext();\r\n    let tmp_1_0;\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.requirementsCharCount, \"/500\");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r13.jobForm.get(\"JB_requirements\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.characterLimitExceeded);\r\n} }\r\nfunction AddEditOpportunityComponent_div_87_div_3_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 63);\r\n    i0.ɵɵtext(1, \" Error: Character limit exceeded! (500 Characters) \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_div_87_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵelementStart(1, \"span\", 68);\r\n    i0.ɵɵtext(2);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(3, AddEditOpportunityComponent_div_87_div_3_Template, 2, 0, \"div\", 22);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r14 = i0.ɵɵnextContext();\r\n    let tmp_1_0;\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵtextInterpolate1(\"\", ctx_r14.hrtTipsCharCount, \"/500\");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r14.jobForm.get(\"JB_hrtTips\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors.characterLimitExceeded);\r\n} }\r\nfunction AddEditOpportunityComponent_div_92_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 63);\r\n    i0.ɵɵtext(1, \" Hours must be between 0 and 24. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_div_101_input_18_Template(rf, ctx) { if (rf & 1) {\r\n    const _r42 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"input\", 88);\r\n    i0.ɵɵlistener(\"change\", function AddEditOpportunityComponent_div_101_input_18_Template_input_change_0_listener($event) { i0.ɵɵrestoreView(_r42); const i_r34 = i0.ɵɵnextContext().index; const ctx_r40 = i0.ɵɵnextContext(); return ctx_r40.onAudioSelected($event, i_r34); });\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_div_101_input_19_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"input\", 89);\r\n} if (rf & 2) {\r\n    const i_r34 = i0.ɵɵnextContext().index;\r\n    const ctx_r36 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"value\", ctx_r36.audioNames[i_r34]);\r\n} }\r\nconst _c2 = function (a0, a1, a2) { return { \"btn-outline-primary\": a0, \"btn-outline-secondary\": a1, \"mt-2\": a2 }; };\r\nfunction AddEditOpportunityComponent_div_101_button_21_Template(rf, ctx) { if (rf & 1) {\r\n    const _r46 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"button\", 90);\r\n    i0.ɵɵlistener(\"click\", function AddEditOpportunityComponent_div_101_button_21_Template_button_click_0_listener() { i0.ɵɵrestoreView(_r46); const i_r34 = i0.ɵɵnextContext().index; const ctx_r44 = i0.ɵɵnextContext(); return ctx_r44.toggleAudio(i_r34); });\r\n    i0.ɵɵelement(1, \"i\", 91);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const i_r34 = i0.ɵɵnextContext().index;\r\n    const ctx_r37 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(2, _c2, !ctx_r37.isPlaying[i_r34], ctx_r37.isPlaying[i_r34], ctx_r37.isReadonly));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngClass\", ctx_r37.isPlaying[i_r34] ? \"fa-pause\" : \"fa-play\");\r\n} }\r\nfunction AddEditOpportunityComponent_div_101_button_22_Template(rf, ctx) { if (rf & 1) {\r\n    const _r50 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"button\", 92);\r\n    i0.ɵɵlistener(\"click\", function AddEditOpportunityComponent_div_101_button_22_Template_button_click_0_listener() { i0.ɵɵrestoreView(_r50); const i_r34 = i0.ɵɵnextContext().index; const ctx_r48 = i0.ɵɵnextContext(); return ctx_r48.removeInsight(i_r34); });\r\n    i0.ɵɵelement(1, \"i\", 93);\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_div_101_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 69);\r\n    i0.ɵɵelementStart(1, \"div\", 70);\r\n    i0.ɵɵelementStart(2, \"div\", 71);\r\n    i0.ɵɵelementStart(3, \"div\", 72);\r\n    i0.ɵɵelementStart(4, \"label\", 73);\r\n    i0.ɵɵtext(5, \"Insight Title\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(6, \"input\", 74);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(7, \"div\", 72);\r\n    i0.ɵɵelementStart(8, \"label\", 75);\r\n    i0.ɵɵtext(9, \"Name\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(10, \"input\", 76);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(11, \"div\", 72);\r\n    i0.ɵɵelementStart(12, \"label\", 77);\r\n    i0.ɵɵtext(13, \"Position\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(14, \"input\", 78);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(15, \"div\", 72);\r\n    i0.ɵɵelementStart(16, \"label\", 79);\r\n    i0.ɵɵtext(17);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(18, AddEditOpportunityComponent_div_101_input_18_Template, 1, 0, \"input\", 80);\r\n    i0.ɵɵtemplate(19, AddEditOpportunityComponent_div_101_input_19_Template, 1, 1, \"input\", 81);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(20, \"div\", 82);\r\n    i0.ɵɵtemplate(21, AddEditOpportunityComponent_div_101_button_21_Template, 2, 6, \"button\", 83);\r\n    i0.ɵɵtemplate(22, AddEditOpportunityComponent_div_101_button_22_Template, 2, 0, \"button\", 84);\r\n    i0.ɵɵelementStart(23, \"audio\", 85, 86);\r\n    i0.ɵɵelement(25, \"source\", 87);\r\n    i0.ɵɵtext(26, \" Your browser does not support the audio element. \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const i_r34 = ctx.index;\r\n    const ctx_r16 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"formGroupName\", i_r34);\r\n    i0.ɵɵadvance(6);\r\n    i0.ɵɵproperty(\"readOnly\", ctx_r16.isReadonly);\r\n    i0.ɵɵadvance(4);\r\n    i0.ɵɵproperty(\"readOnly\", ctx_r16.isReadonly);\r\n    i0.ɵɵadvance(4);\r\n    i0.ɵɵproperty(\"readOnly\", ctx_r16.isReadonly);\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵtextInterpolate(!ctx_r16.isReadonly ? \"Upload Insight\" : \"Uploaded Insight\");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.title !== \"View\");\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.title === \"View\");\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.audioUrls[i_r34]);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.insightFormArray.length > 1 && !ctx_r16.isReadonly);\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"src\", ctx_r16.audioUrls[i_r34], i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction AddEditOpportunityComponent_button_102_Template(rf, ctx) { if (rf & 1) {\r\n    const _r52 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"button\", 94);\r\n    i0.ɵɵlistener(\"click\", function AddEditOpportunityComponent_button_102_Template_button_click_0_listener() { i0.ɵɵrestoreView(_r52); const ctx_r51 = i0.ɵɵnextContext(); return ctx_r51.addInsight(); });\r\n    i0.ɵɵtext(1, \"Add Insight \");\r\n    i0.ɵɵelement(2, \"i\", 95);\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditOpportunityComponent_button_104_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"button\", 96);\r\n    i0.ɵɵtext(1, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nconst _c3 = function (a0) { return { \"required-field\": a0 }; };\r\nconst _c4 = function (a0) { return { \"readOnlyColor\": a0 }; };\r\nconst _c5 = function (a0) { return { \"text-dark\": a0 }; };\r\nfunction wordLimitValidator(maxWords) {\r\n    return (control) => {\r\n        if (control.value) {\r\n            const words = control.value.trim().split(/\\s+/);\r\n            if (words.length > maxWords) {\r\n                console.log('maxwords', maxWords);\r\n                return { wordLimitExceeded: true, wordCount: words.length };\r\n            }\r\n        }\r\n        return null;\r\n    };\r\n}\r\nfunction nonNegativeValidator(control) {\r\n    // console.log('Control : ', control);\r\n    const value = control.value;\r\n    if (value < 0 || value > 24) {\r\n        return { negativeValue: true };\r\n    }\r\n    return null;\r\n}\r\nexport class AddEditOpportunityComponent {\r\n    constructor(formBuilder, dataTransferService, ngxSpinnerService, router, toastr, datePipe, route) {\r\n        var _a, _b;\r\n        this.formBuilder = formBuilder;\r\n        this.dataTransferService = dataTransferService;\r\n        this.ngxSpinnerService = ngxSpinnerService;\r\n        this.router = router;\r\n        this.toastr = toastr;\r\n        this.datePipe = datePipe;\r\n        this.route = route;\r\n        this.p = 1;\r\n        this.viewInsight = true;\r\n        this.title = 'Add New';\r\n        this.isReadonly = false;\r\n        this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\r\n        this.invalidDates = new Set();\r\n        this.selectedType = 'job'; // Default value is 'job'\r\n        this.descriptionCharCount = 0;\r\n        this.requirementsCharCount = 0;\r\n        this.hrtTipsCharCount = 0;\r\n        this.audioFileUrls = []; // Array to store the generated URLs\r\n        this.audioFiles = [];\r\n        this.characterCount = 0;\r\n        this.isPlaying = []; // Array to track playback state\r\n        this.audioUrls = []; // Array to store audio URLs\r\n        this.hideOppType = false;\r\n        this.audioNames = [];\r\n        this.route.queryParams.subscribe(params => {\r\n            if (params) {\r\n                this.CO_id = params['CO_id'];\r\n                console.log(this.CO_id);\r\n            }\r\n            else {\r\n                this.router.navigate(['/actions/employer-opportunities']);\r\n            }\r\n        });\r\n        const state = (_b = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras) === null || _b === void 0 ? void 0 : _b.state; //State values\r\n        console.log('State Values', state);\r\n        if (state) {\r\n            this.viewInsight = state === null || state === void 0 ? void 0 : state.viewInsight;\r\n            this.title = state === null || state === void 0 ? void 0 : state.title;\r\n            this.opportunityData = state === null || state === void 0 ? void 0 : state.opportunityData;\r\n            // this.JB_type=state?.opportunityData.JB_type;\r\n            console.log('this.opportunityData: ', this.opportunityData);\r\n        }\r\n        else {\r\n            this.router.navigate([`actions/employer-opportunities`]);\r\n        }\r\n        this.minDate = new Date().toISOString().split('T')[0];\r\n        this.checkMinDate = this.minDate;\r\n        this.jobForm = this.formBuilder.group(\r\n        //Form to add new scheme\r\n        {\r\n            JB_companyId: [''],\r\n            JB_companyLogo: [''],\r\n            JB_applicationDeadline: ['', [Validators.required]],\r\n            JB_startDate: ['', [Validators.required]],\r\n            JB_jobTitle: ['', [Validators.required, wordLimitValidator(10)]],\r\n            JB_description: [\r\n                '',\r\n                [Validators.required, this.characterLimitValidator(200)],\r\n            ],\r\n            JB_department: ['', [Validators.required, wordLimitValidator(10)]],\r\n            JB_hours: ['', [Validators.required, nonNegativeValidator]],\r\n            JB_hrtTips: ['', [Validators.required, this.characterLimitValidator(200)]],\r\n            JB_roleId: ['', [Validators.required]],\r\n            JB_location: ['', [Validators.required, wordLimitValidator(10)]],\r\n            JB_salary: ['', [Validators.required]],\r\n            JB_modeOfWork: ['', [Validators.required, wordLimitValidator(10)]],\r\n            JB_requirements: [\r\n                '',\r\n                [Validators.required, this.characterLimitValidator(200)],\r\n            ],\r\n            JB_applyLink: ['', [Validators.required]],\r\n            JB_type: ['', Validators.required],\r\n            JB_sectorId: [''],\r\n            JB_insights: this.formBuilder.array([this.createInsight()]),\r\n        });\r\n        this.onTypeChange(this.selectedType);\r\n    }\r\n    ngOnInit() {\r\n        var _a, _b;\r\n        this.getCompanyById(this.CO_id);\r\n        this.getSectorTitles();\r\n        // this.getAllRoleBySectorId(this.sectorID);\r\n        this.minStartDate = this.minDate;\r\n        if (this.selectedType === 'job' && !((_a = this.jobForm.get('JB_sectorId')) === null || _a === void 0 ? void 0 : _a.value)) {\r\n            (_b = this.jobForm.get('JB_roleId')) === null || _b === void 0 ? void 0 : _b.disable();\r\n        }\r\n        if (this.title === 'Edit') {\r\n            this.populateForm(this.opportunityData);\r\n        }\r\n        if (this.title === 'View') {\r\n            this.makeDataReadOnly(this.opportunityData);\r\n        }\r\n    }\r\n    makeDataReadOnly(data) {\r\n        var _a, _b, _c;\r\n        (_a = this.jobForm.get('JB_sectorId')) === null || _a === void 0 ? void 0 : _a.disable();\r\n        (_b = this.jobForm.get('JB_roleId')) === null || _b === void 0 ? void 0 : _b.disable();\r\n        (_c = this.jobForm.get('JB_modeOfWork')) === null || _c === void 0 ? void 0 : _c.disable();\r\n        this.hideOppType = true;\r\n        this.getAllRole(data.JB_sectorId);\r\n        this.isReadonly = true;\r\n        this.jobForm.patchValue({\r\n            JB_jobTitle: data.JB_jobTitle,\r\n            JB_description: data.JB_description,\r\n            JB_department: data.JB_department,\r\n            JB_hours: data.JB_hours,\r\n            JB_hrtTips: data.JB_hrtTips,\r\n            JB_roleId: data.JB_roleId,\r\n            JB_location: data.JB_location,\r\n            JB_salary: data.JB_salary,\r\n            JB_modeOfWork: data.JB_modeOfWork,\r\n            JB_requirements: data.JB_requirements,\r\n            JB_applyLink: data.JB_applyLink,\r\n            JB_type: data.JB_type,\r\n            JB_sectorId: data.JB_sectorId,\r\n            JB_insights: data.JB_insights,\r\n            JB_applicationDeadline: this.datePipe.transform(data.JB_applicationDeadline, 'MMM d,y'),\r\n            JB_startDate: this.datePipe.transform(data.JB_startDate, 'MMM d,y'),\r\n            JB_companyId: this.CO_id,\r\n            CO_sectorId: this.sectorID,\r\n            JB_companyLogo: this.companyLogo,\r\n        });\r\n        const hrInsightsArray = this.jobForm.get('JB_insights');\r\n        hrInsightsArray.clear();\r\n        this.audioNames = data.JB_insights.map((insight) => insight.HRI_link.split('/').pop());\r\n        data.JB_insights.forEach((insight) => {\r\n            const insightFormGroup = this.formBuilder.group({\r\n                HRI_title: [insight.HRI_title],\r\n                HRI_name: [insight.HRI_name],\r\n                HRI_position: [insight.HRI_position],\r\n                HRI_link: [insight.HRI_link],\r\n            });\r\n            hrInsightsArray.push(insightFormGroup);\r\n        });\r\n        this.audioUrls = data.JB_insights.map((insight) => insight.HRI_link);\r\n        this.isPlaying = new Array(data.JB_insights.length).fill(false);\r\n        if (data.JB_type === 0) {\r\n            this.selectedType = 'job';\r\n        }\r\n        else {\r\n            this.selectedType = 'jobScheme';\r\n        }\r\n    }\r\n    populateForm(data) {\r\n        var _a, _b, _c, _d;\r\n        this.hideOppType = true;\r\n        console.log('Data to patch to edit', data);\r\n        this.getAllRole(data.JB_sectorId);\r\n        this.jobForm.patchValue({\r\n            JB_jobTitle: data.JB_jobTitle,\r\n            JB_description: data.JB_description,\r\n            JB_department: data.JB_department,\r\n            JB_hours: data.JB_hours,\r\n            JB_hrtTips: data.JB_hrtTips,\r\n            JB_roleId: data.JB_roleId,\r\n            JB_location: data.JB_location,\r\n            JB_salary: data.JB_salary,\r\n            JB_modeOfWork: data.JB_modeOfWork,\r\n            JB_requirements: data.JB_requirements,\r\n            JB_applyLink: data.JB_applyLink,\r\n            JB_type: data.JB_type,\r\n            JB_sectorId: data.JB_sectorId,\r\n            JB_insights: data.JB_insights,\r\n            JB_applicationDeadline: data.JB_applicationDeadline,\r\n            JB_startDate: data.JB_startDate,\r\n            JB_companyId: this.CO_id,\r\n            CO_sectorId: this.sectorID,\r\n            JB_companyLogo: this.companyLogo,\r\n        });\r\n        const hrInsightsArray = this.jobForm.get('JB_insights');\r\n        hrInsightsArray.clear();\r\n        data.JB_insights.forEach((insight) => {\r\n            const insightFormGroup = this.formBuilder.group({\r\n                HRI_title: [insight.HRI_title],\r\n                HRI_name: [insight.HRI_name],\r\n                HRI_position: [insight.HRI_position],\r\n                HRI_link: [insight.HRI_link],\r\n            });\r\n            hrInsightsArray.push(insightFormGroup);\r\n        });\r\n        // Initialize audio URLs and playback states\r\n        // this.audioUrls = data.JB_insights.map((insight: any) => insight.HRI_link);\r\n        // this.isPlaying = new Array(data.JB_insights.length).fill(false);\r\n        this.audioUrls = data.JB_insights.map((insight) => insight.HRI_link);\r\n        this.isPlaying = new Array(data.JB_insights.length).fill(false);\r\n        const JB_type = (_a = this.opportunityData) === null || _a === void 0 ? void 0 : _a.JB_type;\r\n        if (JB_type === 0) {\r\n            if ((_b = this.jobForm.get('JB_sectorId')) === null || _b === void 0 ? void 0 : _b.value) {\r\n                (_c = this.jobForm.get('JB_roleId')) === null || _c === void 0 ? void 0 : _c.enable();\r\n            }\r\n            else {\r\n                (_d = this.jobForm.get('JB_roleId')) === null || _d === void 0 ? void 0 : _d.reset({ value: null, disabled: true });\r\n            }\r\n            this.selectedType = 'job';\r\n        }\r\n        else {\r\n            this.selectedType = 'jobScheme';\r\n        }\r\n    }\r\n    get insightFormArray() {\r\n        return this.jobForm.get('JB_insights');\r\n    }\r\n    characterLimitValidator(limit) {\r\n        return (control) => {\r\n            if (control.value && control.value.length > limit) {\r\n                return { characterLimitExceeded: true };\r\n            }\r\n            return null;\r\n        };\r\n    }\r\n    onInput(field) {\r\n        const control = this.jobForm.get(field);\r\n        if (control) {\r\n            const value = control.value || '';\r\n            switch (field) {\r\n                case 'JB_description':\r\n                    this.descriptionCharCount = value.length;\r\n                    break;\r\n                case 'JB_requirements':\r\n                    this.requirementsCharCount = value.length;\r\n                    break;\r\n                case 'JB_hrtTips':\r\n                    this.hrtTipsCharCount = value.length;\r\n                    break;\r\n            }\r\n            control.updateValueAndValidity(); // Trigger validation\r\n        }\r\n    }\r\n    onTypeChange(value) {\r\n        var _a;\r\n        this.selectedType = value;\r\n        this.jobForm.reset();\r\n        const insightDiv = document.getElementById('link');\r\n        if (insightDiv) {\r\n            insightDiv.value = '';\r\n        }\r\n        this.audioUrls = [];\r\n        this.audioFiles = [];\r\n        this.isPlaying = [];\r\n        if (this.audioPlayers) {\r\n            this.audioPlayers.forEach((audioPlayer) => {\r\n                audioPlayer.nativeElement.pause();\r\n                audioPlayer.nativeElement.currentTime = 0;\r\n            });\r\n        }\r\n        (_a = this.jobForm.get('JB_roleId')) === null || _a === void 0 ? void 0 : _a.reset({ value: null, disabled: true });\r\n        const sectorControl = this.jobForm.get('JB_sectorId');\r\n        const roleControl = this.jobForm.get('JB_roleId');\r\n        if (value === 'job') {\r\n            sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.setValidators([Validators.required]);\r\n            roleControl === null || roleControl === void 0 ? void 0 : roleControl.setValidators([Validators.required]);\r\n        }\r\n        else {\r\n            sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.clearValidators();\r\n            roleControl === null || roleControl === void 0 ? void 0 : roleControl.clearValidators();\r\n        }\r\n        sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.updateValueAndValidity();\r\n        roleControl === null || roleControl === void 0 ? void 0 : roleControl.updateValueAndValidity();\r\n    }\r\n    onChangeIndustry(event) {\r\n        var _a;\r\n        const CO_sectorId = event.target.value;\r\n        (_a = this.jobForm.get('JB_roleId')) === null || _a === void 0 ? void 0 : _a.enable();\r\n        this.getAllRole(CO_sectorId);\r\n    }\r\n    updateStartDateMin() {\r\n        var _a;\r\n        const deadline = (_a = this.jobForm.get('JB_applicationDeadline')) === null || _a === void 0 ? void 0 : _a.value;\r\n        if (deadline) {\r\n            const minStartDate = new Date(deadline);\r\n            minStartDate.setDate(minStartDate.getDate() + 1); // Add one day to the deadline\r\n            this.minStartDate = minStartDate.toISOString().split('T')[0];\r\n            // After updating minimum start date, check the validity of the start date\r\n            this.checkInvalidDate('JB_startDate');\r\n        }\r\n    }\r\n    checkInvalidDate(controlName) {\r\n        const control = this.jobForm.get(controlName);\r\n        if (control && control.value && isNaN(Date.parse(control.value))) {\r\n            this.invalidDates.add(controlName);\r\n        }\r\n        else {\r\n            this.invalidDates.delete(controlName);\r\n        }\r\n    }\r\n    getSectorTitles() {\r\n        // In Add company form - To sector dropdown\r\n        this.dataTransferService.getSectorTitles().subscribe((res) => {\r\n            if ((res.statusCode = 200)) {\r\n                this.SectorList = res.data;\r\n                console.log('Sectors', this.SectorList);\r\n            }\r\n            else {\r\n                console.error('Failed to fetch sectors. Status:', res.status);\r\n            }\r\n        });\r\n    }\r\n    formatDate(date) {\r\n        // Check if the date is valid\r\n        if (!isNaN(date.getTime())) {\r\n            return this.datePipe.transform(date, 'MMM dd yyyy');\r\n        }\r\n        else {\r\n            return '';\r\n        }\r\n    }\r\n    getCompanyById(companyId) {\r\n        this.dataTransferService.getCompanyById(companyId).subscribe((res) => {\r\n            this.companyLogo = res.CO_logo;\r\n            this.sectorID = res.CO_sectorId;\r\n            console.log('getCompanyById', res);\r\n        }, (error) => {\r\n            console.log(\"Error\", error);\r\n            this.toastr.error(\"Unable to fetch data\");\r\n        });\r\n    }\r\n    getAllRole(CO_sectorId) {\r\n        console.log('sectorId : ', CO_sectorId);\r\n        this.dataTransferService.getAllRoleBySectorId(CO_sectorId).subscribe({\r\n            next: (res) => {\r\n                if (res.statusCode === 200) {\r\n                    this.RoleList = res.data;\r\n                    console.log('RoleList : ', this.RoleList);\r\n                }\r\n                else {\r\n                    console.error('Failed to fetch role. Status:', res.status);\r\n                }\r\n            },\r\n            error: (error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                console.error('Error occurred while fetching roles:', error);\r\n            },\r\n        });\r\n    }\r\n    // getAllRoleBySectorId(sectorID: any) {\r\n    //   this.dataTransferService.getAllRoleBySectorId(sectorID).subscribe({\r\n    //     next: (res: any) => {\r\n    //       if (res.statusCode === 200) {\r\n    //         this.RoleList = res.data;\r\n    //         this.roleId = this.RoleList.RO_id;\r\n    //         console.log('RoleList : ', this.RoleList);\r\n    //         this.ngxSpinnerService.hide('globalSpinner');\r\n    //       } else {\r\n    //         this.ngxSpinnerService.hide('globalSpinner');\r\n    //         console.error('Failed to fetch role. Status:', res.status);\r\n    //       }\r\n    //     },\r\n    //     error: (error: any) => {\r\n    //       this.ngxSpinnerService.hide('globalSpinner');\r\n    //       console.error('Error occurred while fetching roles:', error);\r\n    //     },\r\n    //   });\r\n    // }\r\n    showDeleteModal(scheme) {\r\n        console.log('Delete Scheme', scheme);\r\n        this.deleteId = scheme.JB_id;\r\n    }\r\n    // onFileSelected(event: any) {\r\n    //   //to preview image and take file in imageName to pass in upload api\r\n    //   if (event.target.files.length === 0) {\r\n    //     // Reset both imageName and imageSrc when no file is selected\r\n    //     this.imageName = null;\r\n    //     this.imageSrc = null;\r\n    //     return;\r\n    //   }\r\n    //   this.imageName = event.target.files[0];\r\n    //   const fileType = this.imageName.type.split('/')[0];\r\n    //   if (fileType !== 'image') {\r\n    //     event.target.value = '';\r\n    //     this.toastr.info('Please select an image file.');\r\n    //     this.imageName = null;\r\n    //     this.imageSrc = null;\r\n    //     return;\r\n    //   }\r\n    //   if (this.imageName && fileType == 'image') {\r\n    //     const reader = new FileReader();\r\n    //     reader.onload = (e) => {\r\n    //       this.imageSrc = e.target?.result as string | ArrayBuffer;\r\n    //     };\r\n    //     reader.readAsDataURL(this.imageName);\r\n    //   } else {\r\n    //     this.imageSrc = null; // Reset imageSrc if no file selected\r\n    //   }\r\n    //   console.log('imageName', this.imageName);\r\n    // }\r\n    // uploadLogoUrl() {\r\n    //   //To upload logo from add new company form\r\n    //   if (!this.imageName) {\r\n    //     // this.toastr.error('Please select an image.');\r\n    //     return;\r\n    //   }\r\n    //   console.log('image', this.imageName);\r\n    //   this.dataTransferService\r\n    //     .uploadurl(this.imageName)\r\n    //     .subscribe((res: any) => {});\r\n    // }\r\n    // getAllJobByJobId(scheme: any, title: string) {\r\n    //   this.title = title;\r\n    //   if (this.title == 'View') {\r\n    //     this.isReadonly = true;\r\n    //     this.viewInsight = false;\r\n    //     this.ngxSpinnerService.show('globalSpinner');\r\n    //     this.dataTransferService.getAllJobByJobId(scheme.JB_id).subscribe({\r\n    //       next: (res: any) => {\r\n    //         if (res.statusCode === 200 && res.data && res.data.length > 0) {\r\n    //           this.ngxSpinnerService.hide('globalSpinner');\r\n    //           const formatDate = (timestamp: number): string => {\r\n    //             const date = new Date(timestamp);\r\n    //             const year = date.getFullYear();\r\n    //             const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    //             const day = String(date.getDate()).padStart(2, '0');\r\n    //             return `${month}-${day}-${year}`;\r\n    //           };\r\n    //           // Format dates in the response\r\n    //           const formattedPayload = res.data.map((item: any) => ({\r\n    //             ...item,\r\n    //             JB_startDate: formatDate(item.JB_startDate),\r\n    //             JB_applicationDeadline: formatDate(item.JB_applicationDeadline),\r\n    //           }));\r\n    //           this.jobForm.patchValue(formattedPayload[0]);\r\n    //           this.jobForm.get('JB_roleId')?.disable();\r\n    //           this.jobForm.get('JB_modeOfWork')?.disable();\r\n    //           // Log the updated JobList\r\n    //           console.log('Full JobList', formattedPayload[0]);\r\n    //         } else {\r\n    //           this.ngxSpinnerService.hide('globalSpinner');\r\n    //           console.error('Error: Invalid response or no data found.');\r\n    //         }\r\n    //       },\r\n    //       error: (error: any) => {\r\n    //         this.ngxSpinnerService.hide('globalSpinner');\r\n    //         console.error('Error occurred while fetching roles:', error);\r\n    //       },\r\n    //     });\r\n    //     // this.jobForm.patchValue(scheme);\r\n    //   }\r\n    // }\r\n    // editId: any;\r\n    // editScheme(scheme: any, title: string) {\r\n    //   this.isReadonly = false;\r\n    //   this.title = title;\r\n    //   this.jobForm.get('JB_roleId')?.enable();\r\n    //   this.jobForm.get('JB_modeOfWork')?.enable();\r\n    //   this.viewInsight = true;\r\n    //   this.editId = scheme.JB_id;\r\n    //   this.toEditInsight = scheme.JB_insights;\r\n    //   const formatDate = (timestamp: number): string => {\r\n    //     const date = new Date(timestamp);\r\n    //     const year = date.getFullYear();\r\n    //     const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    //     const day = String(date.getDate()).padStart(2, '0');\r\n    //     return `${month}-${day}-${year}`;\r\n    //   };\r\n    //   const formattedPayload = {\r\n    //     ...scheme,\r\n    //     JB_startDate: formatDate(scheme.JB_startDate),\r\n    //     JB_applicationDeadline: formatDate(scheme.JB_applicationDeadline),\r\n    //   };\r\n    //   // this.selectedAudioFilethis.jobForm.get('JB_insights')?.setValue(this.selectedAudioFile);\r\n    //   this.jobForm.patchValue(formattedPayload);\r\n    // }\r\n    createInsight() {\r\n        return this.formBuilder.group({\r\n            HRI_title: [''],\r\n            HRI_name: ['',],\r\n            HRI_position: [''],\r\n            HRI_link: [null],\r\n        });\r\n        this.isPlaying.push(false); // Initialize playback state for new row\r\n        this.audioUrls.push(''); // Initialize empty URL for new row\r\n    }\r\n    addInsight() {\r\n        this.insightFormArray.push(this.createInsight());\r\n    }\r\n    removeInsight(index) {\r\n        this.insightFormArray.removeAt(index);\r\n        this.audioUrls.splice(index, 1); // Remove URL for deleted row\r\n        this.isPlaying.splice(index, 1); // Remove playback state for deleted row\r\n        this.audioFiles.splice(index, 1); // Remove file object for deleted row\r\n    }\r\n    toggleAudio(index) {\r\n        const audioElements = this.audioPlayers.toArray();\r\n        const audioElement = audioElements[index].nativeElement;\r\n        // Stop all other audio\r\n        this.stopAllAudio(index);\r\n        // Toggle play/pause for the current audio element\r\n        if (this.isPlaying[index]) {\r\n            audioElement.pause();\r\n        }\r\n        else {\r\n            audioElement.src = this.audioUrls[index];\r\n            audioElement.play();\r\n        }\r\n        // Update the playback state\r\n        this.isPlaying[index] = !this.isPlaying[index];\r\n    }\r\n    stopAllAudio(currentIndex) {\r\n        this.audioPlayers.forEach((audioPlayer, index) => {\r\n            if (index !== currentIndex) {\r\n                audioPlayer.nativeElement.pause();\r\n                this.isPlaying[index] = false;\r\n            }\r\n        });\r\n    }\r\n    stopAudio(index) {\r\n        const audioElements = this.audioPlayers.toArray();\r\n        const audioElement = audioElements[index].nativeElement;\r\n        audioElement.pause();\r\n        audioElement.currentTime = 0; // Reset to the beginning\r\n        this.isPlaying[index] = false;\r\n    }\r\n    onAudioSelected(event, index) {\r\n        let audiofile;\r\n        const selectedFile = event.target.files[0];\r\n        if (selectedFile) {\r\n            const newFileName = FileValidator.addTimestamp(selectedFile.name);\r\n            audiofile = new File([selectedFile], newFileName, { type: selectedFile.type });\r\n        }\r\n        else {\r\n            audiofile = null;\r\n        }\r\n        if (!audiofile) {\r\n            this.audioUrls[index] = '';\r\n            this.stopAudio(index);\r\n            return;\r\n        }\r\n        const audiofileType = audiofile.type.split('/')[0]; // Get the file type (e.g., 'audio', 'video', etc.)\r\n        if (audiofileType !== 'audio') {\r\n            // Reset the file input to clear the selected file\r\n            event.target.value = '';\r\n            this.toastr.info('Please select an audio file.');\r\n            return;\r\n        }\r\n        // Store the file object in the array for later upload\r\n        this.audioFiles[index] = audiofile;\r\n        console.log('Audio file :', audiofile);\r\n        const reader = new FileReader();\r\n        reader.onload = () => {\r\n            // Store audio URL\r\n            this.audioUrls[index] = reader.result;\r\n        };\r\n        reader.readAsDataURL(audiofile);\r\n    }\r\n    async uploadAudioFilesSequentially(files) {\r\n        const uploadPromises = files.map((file, index) => this.uploadSingleAudioFile(file, index));\r\n        return Promise.all(uploadPromises);\r\n    }\r\n    uploadSingleAudioFile(file, index) {\r\n        return new Promise((resolve, reject) => {\r\n            this.dataTransferService.uploadurl(file).subscribe((res) => {\r\n                console.log('Upload successful', file.name);\r\n                const fileUrl = this.baseUrl + file.name;\r\n                this.audioUrls[index] = fileUrl; // Directly add the file URL to this.audioUrls array\r\n                resolve(fileUrl);\r\n            }, (error) => {\r\n                console.error('Upload error', error);\r\n                this.toastr.error('Failed to upload audio file');\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n    onSubmit() {\r\n        if (this.title === 'Edit') {\r\n            const sectorControl = this.jobForm.get('JB_sectorId'); //Remove Validation as per selected type...\r\n            const roleControl = this.jobForm.get('JB_roleId');\r\n            if (this.selectedType === 'job') {\r\n                sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.setValidators([Validators.required]);\r\n                roleControl === null || roleControl === void 0 ? void 0 : roleControl.setValidators([Validators.required]);\r\n            }\r\n            else {\r\n                sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.clearValidators();\r\n                roleControl === null || roleControl === void 0 ? void 0 : roleControl.clearValidators();\r\n            }\r\n            sectorControl === null || sectorControl === void 0 ? void 0 : sectorControl.updateValueAndValidity();\r\n            roleControl === null || roleControl === void 0 ? void 0 : roleControl.updateValueAndValidity();\r\n            this.ngxSpinnerService.show('globalSpinner');\r\n            this.uploadAudioFilesSequentially(this.audioFiles) //upload file one by one\r\n                .then((audioUrls) => {\r\n                this.insightFormArray.controls.forEach((control, index) => {\r\n                    var _a;\r\n                    const group = control;\r\n                    (_a = group.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(this.audioUrls[index]); // Patch the URL from the array\r\n                });\r\n                if (this.jobForm.invalid) {\r\n                    console.log('this.job.value', this.jobForm.value);\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    this.toastr.info('Please fill all required fields and ensure they are filled correctly.');\r\n                    return;\r\n                }\r\n                else {\r\n                    console.log('this.job.value', this.jobForm.value);\r\n                    this.updateOpportunity();\r\n                }\r\n            })\r\n                .catch((error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                console.error('Error uploading audio files:', error);\r\n                this.toastr.error('Audio file upload failed');\r\n            });\r\n        }\r\n        else {\r\n            this.jobForm.patchValue({\r\n                JB_companyId: this.CO_id,\r\n                CO_sectorId: this.sectorID,\r\n                JB_companyLogo: this.companyLogo,\r\n                JB_type: this.selectedType === 'job' ? 0 : 1,\r\n            });\r\n            this.ngxSpinnerService.show('globalSpinner');\r\n            this.uploadAudioFilesSequentially(this.audioFiles)\r\n                .then((audioUrls) => {\r\n                this.insightFormArray.controls.forEach((control, index) => {\r\n                    var _a;\r\n                    const group = control;\r\n                    (_a = group.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(audioUrls[index]); // Patch the URL from the array\r\n                });\r\n                if (this.jobForm.invalid) {\r\n                    console.log('this.job.value', this.jobForm.value);\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    this.toastr.info('Please fill all required fields and ensure they are filled correctly.');\r\n                    return;\r\n                }\r\n                else {\r\n                    console.log('this.job.value', this.jobForm.value);\r\n                    this.addOpportunity();\r\n                }\r\n            })\r\n                .catch((error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                console.error('Error uploading audio files:', error);\r\n                this.toastr.error('Audio file upload failed');\r\n            });\r\n        }\r\n    }\r\n    updateOpportunity() {\r\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u;\r\n        console.log(\"selected Type\", this.selectedType);\r\n        const data = {\r\n            JB_jobTitle: (_a = this.jobForm.get('JB_jobTitle')) === null || _a === void 0 ? void 0 : _a.value,\r\n            JB_description: (_b = this.jobForm.get('JB_description')) === null || _b === void 0 ? void 0 : _b.value,\r\n            JB_department: (_c = this.jobForm.get('JB_department')) === null || _c === void 0 ? void 0 : _c.value,\r\n            JB_hours: (_d = this.jobForm.get('JB_hours')) === null || _d === void 0 ? void 0 : _d.value,\r\n            JB_hrtTips: (_e = this.jobForm.get('JB_hrtTips')) === null || _e === void 0 ? void 0 : _e.value,\r\n            JB_roleId: ((_f = this.jobForm.get('JB_roleId')) === null || _f === void 0 ? void 0 : _f.value) ? (_g = this.jobForm.get('JB_roleId')) === null || _g === void 0 ? void 0 : _g.value : '',\r\n            JB_location: (_h = this.jobForm.get('JB_location')) === null || _h === void 0 ? void 0 : _h.value,\r\n            JB_salary: (_j = this.jobForm.get('JB_salary')) === null || _j === void 0 ? void 0 : _j.value,\r\n            JB_modeOfWork: (_k = this.jobForm.get('JB_modeOfWork')) === null || _k === void 0 ? void 0 : _k.value,\r\n            JB_requirements: (_l = this.jobForm.get('JB_requirements')) === null || _l === void 0 ? void 0 : _l.value,\r\n            JB_applyLink: (_m = this.jobForm.get('JB_applyLink')) === null || _m === void 0 ? void 0 : _m.value,\r\n            JB_type: (_o = this.jobForm.get('JB_type')) === null || _o === void 0 ? void 0 : _o.value,\r\n            JB_sectorId: ((_p = this.jobForm.get('JB_sectorId')) === null || _p === void 0 ? void 0 : _p.value) ? (_q = this.jobForm.get('JB_sectorId')) === null || _q === void 0 ? void 0 : _q.value : '',\r\n            JB_insights: (_r = this.jobForm.get('JB_insights')) === null || _r === void 0 ? void 0 : _r.value,\r\n            JB_applicationDeadline: (_s = this.jobForm.get('JB_applicationDeadline')) === null || _s === void 0 ? void 0 : _s.value,\r\n            JB_startDate: (_t = this.jobForm.get('JB_startDate')) === null || _t === void 0 ? void 0 : _t.value,\r\n            JB_companyId: this.CO_id,\r\n            CO_sectorId: this.sectorID,\r\n            JB_companyLogo: this.companyLogo,\r\n            JB_id: this.opportunityData.JB_id\r\n        };\r\n        const startDateControl = this.jobForm.get('JB_startDate');\r\n        if (startDateControl) {\r\n            const startDateValue = startDateControl.value;\r\n            const deadlineValue = (_u = this.jobForm.get('JB_applicationDeadline')) === null || _u === void 0 ? void 0 : _u.value;\r\n            if (startDateValue && deadlineValue) {\r\n                const startDate = new Date(startDateValue);\r\n                const deadline = new Date(deadlineValue);\r\n                if (startDate >= deadline) {\r\n                    if (deadlineValue < this.checkMinDate) {\r\n                        // Application deadline is in the past\r\n                        this.toastr.error('Application deadline must be today or a future date.');\r\n                        return;\r\n                    }\r\n                    console.log('Edit SCHEME DATA : ', data);\r\n                    this.ngxSpinnerService.show('globalSpinner');\r\n                    this.dataTransferService.updateJobs(data).subscribe((res) => {\r\n                        console.log('Edit User', data);\r\n                        if (res.statusCode == 200) {\r\n                            this.ngxSpinnerService.hide('globalSpinner');\r\n                            this.toastr.success('Opportunity Updated Successfully.');\r\n                            const state = {\r\n                                CO_id: this.CO_id,\r\n                                CO_sectorId: this.sectorID,\r\n                                CO_logo: this.companyLogo,\r\n                            };\r\n                            this.router.navigate(['/actions/employer-opportunities/existing-opportunities'], { queryParams: { CO_id: this.CO_id } });\r\n                            this.dataTransferService.getAllSchemeByCompanyId(this.CO_id);\r\n                        }\r\n                        else {\r\n                            this.ngxSpinnerService.hide('globalSpinner');\r\n                            console.log('', res.message);\r\n                            this.toastr.error('', 'Something went wrong');\r\n                        }\r\n                    }, (error) => {\r\n                        this.ngxSpinnerService.hide('globalSpinner');\r\n                        console.error('Unable to edit :', error);\r\n                        this.toastr.error('', 'Something went wrong');\r\n                    });\r\n                }\r\n                else {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    this.toastr.error('Start date must be after the application deadline.');\r\n                    console.log('Invalid form submission!');\r\n                }\r\n            }\r\n        }\r\n    }\r\n    addOpportunity() {\r\n        var _a;\r\n        const startDateControl = this.jobForm.get('JB_startDate');\r\n        if (startDateControl) {\r\n            const startDateValue = startDateControl.value;\r\n            const deadlineValue = (_a = this.jobForm.get('JB_applicationDeadline')) === null || _a === void 0 ? void 0 : _a.value;\r\n            if (startDateValue && deadlineValue) {\r\n                const startDate = new Date(startDateValue);\r\n                const deadline = new Date(deadlineValue);\r\n                if (startDate >= deadline) {\r\n                    if (deadlineValue < this.checkMinDate) {\r\n                        // Application deadline is in the past\r\n                        this.toastr.error('Application deadline must be today or a future date.');\r\n                        return;\r\n                    }\r\n                    console.log('ADD SCHEME DATA : ', this.jobForm.value);\r\n                    this.dataTransferService.addOpportunity(this.jobForm.value).subscribe((res) => {\r\n                        console.log('addOpportunity', res);\r\n                        this.ngxSpinnerService.hide('globalSpinner');\r\n                        if (res.statusCode === 200) {\r\n                            this.toastr.success('', 'New opportunity added successfully.');\r\n                            this.router.navigate(['/actions/employer-opportunities/existing-opportunities'], { queryParams: { CO_id: this.CO_id } });\r\n                            this.dataTransferService.getAllSchemeByCompanyId(this.CO_id);\r\n                        }\r\n                        else {\r\n                            this.toastr.error('', 'Something went wrong');\r\n                        }\r\n                    }, (error) => {\r\n                        this.ngxSpinnerService.hide('globalSpinner');\r\n                        console.error('Unable to add opportunity:', error);\r\n                        this.toastr.error('', 'Something went wrong');\r\n                    });\r\n                }\r\n                else {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    this.toastr.error('Start date must be after the application deadline.');\r\n                    console.log('Invalid form submission!');\r\n                }\r\n            }\r\n        }\r\n    }\r\n    goToExistingScheme() {\r\n        this.router.navigate([`/actions/employer-opportunities/existing-opportunities`], { queryParams: { CO_id: this.CO_id } });\r\n    }\r\n}\r\nAddEditOpportunityComponent.ɵfac = function AddEditOpportunityComponent_Factory(t) { return new (t || AddEditOpportunityComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.NgxSpinnerService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ToastrService), i0.ɵɵdirectiveInject(i6.DatePipe), i0.ɵɵdirectiveInject(i4.ActivatedRoute)); };\r\nAddEditOpportunityComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: AddEditOpportunityComponent, selectors: [[\"app-add-edit-opportunity\"]], viewQuery: function AddEditOpportunityComponent_Query(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵviewQuery(_c0, 5);\r\n    } if (rf & 2) {\r\n        let _t;\r\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioPlayers = _t);\r\n    } }, decls: 107, vars: 85, consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"mt-3\"], [1, \"radio-button-group\"], [\"class\", \"radio-button-label\", 4, \"ngIf\"], [1, \"form-group\", \"col-lg-4\"], [\"for\", \"JB_sectorId\", 3, \"ngClass\"], [\"id\", \"JB_sectorId\", \"formControlName\", \"JB_sectorId\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"change\"], [\"selected\", \"\", \"disabled\", \"\", 3, \"value\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"JB_roleId\", 3, \"ngClass\"], [\"id\", \"JB_roleId\", \"formControlName\", \"JB_roleId\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\"], [\"disabled\", \"\", \"selected\", \"\", 3, \"value\"], [3, \"value\", \"selected\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"info\", 4, \"ngIf\"], [\"for\", \"JB_jobTitle\", 3, \"ngClass\"], [\"type\", \"textarea\", \"id\", \"JB_jobTitle\", \"formControlName\", \"JB_jobTitle\", \"required\", \"\", \"placeholder\", \"Enter Title\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"for\", \"JB_location\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"JB_location\", \"required\", \"\", \"placeholder\", \"e.g. London, UK\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"JB_applicationDeadline\", 3, \"ngClass\"], [\"type\", \"date\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"JB_applicationDeadline\", \"required\", \"\", 3, \"min\", \"readonly\", \"change\", \"input\", 4, \"ngIf\"], [\"type\", \"text\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"JB_applicationDeadline\", \"readonly\", \"\", 4, \"ngIf\"], [\"for\", \"JB_startDate\", 3, \"ngClass\"], [\"type\", \"date\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"JB_startDate\", \"required\", \"\", 3, \"min\", \"readonly\", \"input\", 4, \"ngIf\"], [\"type\", \"text\", \"class\", \"form-control form-control-sm\", \"formControlName\", \"JB_startDate\", \"readonly\", \"\", 4, \"ngIf\"], [\"for\", \"JB_modeOfWork\", 3, \"ngClass\"], [\"id\", \"JB_modeOfWork\", \"type\", \"text\", \"formControlName\", \"JB_modeOfWork\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"ngClass\", \"disabled\"], [\"for\", \"JB_salary\", 3, \"ngClass\"], [\"type\", \"number\", \"formControlName\", \"JB_salary\", \"required\", \"\", \"placeholder\", \"Enter Salary\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"JB_department\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"JB_department\", \"required\", \"\", \"placeholder\", \"Enter Department\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"JB_description\", 3, \"ngClass\"], [\"formControlName\", \"JB_description\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Enter Description\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"input\"], [4, \"ngIf\"], [\"for\", \"JB_requirements\", 3, \"ngClass\"], [\"formControlName\", \"JB_requirements\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Enter Requirements\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"input\"], [\"for\", \"JB_hrtTips\", 3, \"ngClass\"], [\"formControlName\", \"JB_hrtTips\", \"required\", \"\", \"rows\", \"5\", \"cols\", \"50\", \"placeholder\", \"Enter HR Tips\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"input\"], [\"for\", \"JB_hours\", 3, \"ngClass\"], [\"type\", \"number\", \"formControlName\", \"JB_hours\", \"required\", \"\", \"placeholder\", \"Enter Hours\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"JB_applyLink\", 3, \"ngClass\"], [\"type\", \"text\", \"formControlName\", \"JB_applyLink\", \"required\", \"\", \"placeholder\", \"Enter Apply Link\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"formArrayName\", \"JB_insights\"], [1, \"mb-3\", \"py-2\"], [\"class\", \"row mb-3\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"class\", \"btn btn-sm add-insight-btn btn-outline-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"text-center\", \"mt-3\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [1, \"btn\", \"btn-light\", 3, \"click\"], [1, \"radio-button-label\"], [\"type\", \"radio\", \"id\", \"job\", \"name\", \"opportunityType\", \"value\", \"job\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [\"for\", \"job\", 1, \"mt-2\"], [\"type\", \"radio\", \"id\", \"jobScheme\", \"name\", \"opportunityType\", \"value\", \"jobScheme\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [\"for\", \"jobScheme\", 1, \"mt-2\"], [3, \"value\"], [3, \"value\", \"selected\"], [1, \"info\"], [1, \"warning\"], [\"type\", \"date\", \"formControlName\", \"JB_applicationDeadline\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"min\", \"readonly\", \"change\", \"input\"], [\"type\", \"text\", \"formControlName\", \"JB_applicationDeadline\", \"readonly\", \"\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"JB_startDate\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"min\", \"readonly\", \"input\"], [\"type\", \"text\", \"formControlName\", \"JB_startDate\", \"readonly\", \"\", 1, \"form-control\", \"form-control-sm\"], [1, \"character-count\"], [1, \"row\", \"mb-3\", 3, \"formGroupName\"], [1, \"col-lg-11\"], [1, \"row\", \"mr-0\"], [1, \"form-group\", \"col-lg-3\"], [\"for\", \"HRI_title\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_title\", \"placeholder\", \"Enter Title\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"HRI_name\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_name\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"HRI_position\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_position\", \"placeholder\", \"Enter Position\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"link\", 1, \"subtitle\"], [\"type\", \"file\", \"id\", \"link\", \"class\", \"form-control form-control-sm\", \"accept\", \"audio/*\", 3, \"change\", 4, \"ngIf\"], [\"type\", \"text\", \"class\", \"form-control form-control-sm\", \"readOnly\", \"\", 3, \"value\", 4, \"ngIf\"], [1, \"col-lg-1\", \"px-0\", \"d-flex\", \"align-items-center\", \"btns\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm mr-2\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [\"controls\", \"\", 2, \"display\", \"none\"], [\"audioPlayer\", \"\"], [\"type\", \"audio/mpeg\", 3, \"src\"], [\"type\", \"file\", \"id\", \"link\", \"accept\", \"audio/*\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [\"type\", \"text\", \"readOnly\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"value\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"mr-2\", 3, \"ngClass\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-minus\", \"icon\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"add-insight-btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"icon\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]], template: function AddEditOpportunityComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵelementStart(0, \"app-sidebar\");\r\n        i0.ɵɵelementStart(1, \"div\", 0);\r\n        i0.ɵɵelementStart(2, \"div\", 1);\r\n        i0.ɵɵelementStart(3, \"div\", 2);\r\n        i0.ɵɵelementStart(4, \"div\", 3);\r\n        i0.ɵɵelementStart(5, \"div\", 4);\r\n        i0.ɵɵtext(6);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(7, \"div\", 5);\r\n        i0.ɵɵelementStart(8, \"form\", 6);\r\n        i0.ɵɵlistener(\"ngSubmit\", function AddEditOpportunityComponent_Template_form_ngSubmit_8_listener() { return ctx.onSubmit(); });\r\n        i0.ɵɵelementStart(9, \"div\", 7);\r\n        i0.ɵɵelementStart(10, \"h5\");\r\n        i0.ɵɵtext(11, \"Opportunity Type\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(12, \"div\", 8);\r\n        i0.ɵɵtemplate(13, AddEditOpportunityComponent_div_13_Template, 4, 3, \"div\", 9);\r\n        i0.ɵɵtemplate(14, AddEditOpportunityComponent_div_14_Template, 4, 3, \"div\", 9);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(15, \"hr\");\r\n        i0.ɵɵelementStart(16, \"div\", 1);\r\n        i0.ɵɵelementStart(17, \"div\", 10);\r\n        i0.ɵɵelementStart(18, \"label\", 11);\r\n        i0.ɵɵtext(19, \"Sector\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(20, \"select\", 12);\r\n        i0.ɵɵlistener(\"change\", function AddEditOpportunityComponent_Template_select_change_20_listener($event) { return ctx.onChangeIndustry($event); });\r\n        i0.ɵɵelementStart(21, \"option\", 13);\r\n        i0.ɵɵtext(22, \"Please Select A Sector\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(23, AddEditOpportunityComponent_option_23_Template, 2, 2, \"option\", 14);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(24, \"div\", 10);\r\n        i0.ɵɵelementStart(25, \"label\", 15);\r\n        i0.ɵɵtext(26, \"Role\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(27, \"select\", 16);\r\n        i0.ɵɵelementStart(28, \"option\", 17);\r\n        i0.ɵɵtext(29, \"Please Select Role\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(30, AddEditOpportunityComponent_option_30_Template, 2, 3, \"option\", 18);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(31, AddEditOpportunityComponent_div_31_Template, 2, 0, \"div\", 19);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(32, \"div\", 10);\r\n        i0.ɵɵelementStart(33, \"label\", 20);\r\n        i0.ɵɵtext(34, \"Title\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(35, \"input\", 21);\r\n        i0.ɵɵtemplate(36, AddEditOpportunityComponent_div_36_Template, 2, 0, \"div\", 22);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(37, \"div\", 10);\r\n        i0.ɵɵelementStart(38, \"label\", 23);\r\n        i0.ɵɵtext(39, \"Location\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(40, \"input\", 24);\r\n        i0.ɵɵtemplate(41, AddEditOpportunityComponent_div_41_Template, 2, 0, \"div\", 22);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(42, \"div\", 10);\r\n        i0.ɵɵelementStart(43, \"label\", 25);\r\n        i0.ɵɵtext(44, \"Application Deadline\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(45, AddEditOpportunityComponent_input_45_Template, 1, 2, \"input\", 26);\r\n        i0.ɵɵtemplate(46, AddEditOpportunityComponent_input_46_Template, 1, 0, \"input\", 27);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(47, \"div\", 10);\r\n        i0.ɵɵelementStart(48, \"label\", 28);\r\n        i0.ɵɵtext(49, \"Start Date\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(50, AddEditOpportunityComponent_input_50_Template, 1, 2, \"input\", 29);\r\n        i0.ɵɵtemplate(51, AddEditOpportunityComponent_input_51_Template, 1, 0, \"input\", 30);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(52, \"div\", 10);\r\n        i0.ɵɵelementStart(53, \"label\", 31);\r\n        i0.ɵɵtext(54, \"Mode of work\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(55, \"select\", 32);\r\n        i0.ɵɵelementStart(56, \"option\", 17);\r\n        i0.ɵɵtext(57, \"Please Select Mode Of Work\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(58, \"option\");\r\n        i0.ɵɵtext(59, \"Remote\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(60, \"option\");\r\n        i0.ɵɵtext(61, \"On Site\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(62, \"option\");\r\n        i0.ɵɵtext(63, \"Hybrid\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(64, \"div\", 10);\r\n        i0.ɵɵelementStart(65, \"label\", 33);\r\n        i0.ɵɵtext(66, \"Salary\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(67, \"input\", 34);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(68, \"div\", 10);\r\n        i0.ɵɵelementStart(69, \"label\", 35);\r\n        i0.ɵɵtext(70, \"Department\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(71, \"input\", 36);\r\n        i0.ɵɵtemplate(72, AddEditOpportunityComponent_div_72_Template, 2, 0, \"div\", 22);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(73, \"div\", 10);\r\n        i0.ɵɵelementStart(74, \"label\", 37);\r\n        i0.ɵɵtext(75, \"Description\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(76, \"textarea\", 38);\r\n        i0.ɵɵlistener(\"input\", function AddEditOpportunityComponent_Template_textarea_input_76_listener() { return ctx.onInput(\"JB_description\"); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(77, AddEditOpportunityComponent_div_77_Template, 4, 2, \"div\", 39);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(78, \"div\", 10);\r\n        i0.ɵɵelementStart(79, \"label\", 40);\r\n        i0.ɵɵtext(80, \"Requirements\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(81, \"textarea\", 41);\r\n        i0.ɵɵlistener(\"input\", function AddEditOpportunityComponent_Template_textarea_input_81_listener() { return ctx.onInput(\"JB_requirements\"); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(82, AddEditOpportunityComponent_div_82_Template, 4, 2, \"div\", 39);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(83, \"div\", 10);\r\n        i0.ɵɵelementStart(84, \"label\", 42);\r\n        i0.ɵɵtext(85, \"HR Tips\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(86, \"textarea\", 43);\r\n        i0.ɵɵlistener(\"input\", function AddEditOpportunityComponent_Template_textarea_input_86_listener() { return ctx.onInput(\"JB_hrtTips\"); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(87, AddEditOpportunityComponent_div_87_Template, 4, 2, \"div\", 39);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(88, \"div\", 10);\r\n        i0.ɵɵelementStart(89, \"label\", 44);\r\n        i0.ɵɵtext(90, \"Work Hours\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(91, \"input\", 45);\r\n        i0.ɵɵtemplate(92, AddEditOpportunityComponent_div_92_Template, 2, 0, \"div\", 22);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(93, \"div\", 10);\r\n        i0.ɵɵelementStart(94, \"label\", 46);\r\n        i0.ɵɵtext(95, \"Apply Link\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(96, \"input\", 47);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(97, \"hr\");\r\n        i0.ɵɵelementStart(98, \"div\", 48);\r\n        i0.ɵɵelementStart(99, \"h6\", 49);\r\n        i0.ɵɵtext(100, \"HR / Hiring Manager\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(101, AddEditOpportunityComponent_div_101_Template, 27, 10, \"div\", 50);\r\n        i0.ɵɵtemplate(102, AddEditOpportunityComponent_button_102_Template, 3, 0, \"button\", 51);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(103, \"div\", 52);\r\n        i0.ɵɵtemplate(104, AddEditOpportunityComponent_button_104_Template, 2, 0, \"button\", 53);\r\n        i0.ɵɵelementStart(105, \"button\", 54);\r\n        i0.ɵɵlistener(\"click\", function AddEditOpportunityComponent_Template_button_click_105_listener() { return ctx.goToExistingScheme(); });\r\n        i0.ɵɵtext(106, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n    } if (rf & 2) {\r\n        let tmp_12_0;\r\n        let tmp_15_0;\r\n        let tmp_18_0;\r\n        let tmp_33_0;\r\n        let tmp_45_0;\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵtextInterpolate1(\"\", ctx.title, \" Opportunity\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"formGroup\", ctx.jobForm);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngIf\", !(ctx.hideOppType && ctx.selectedType === \"jobScheme\"));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", !(ctx.hideOppType && ctx.selectedType === \"job\"));\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c3, ctx.selectedType == \"job\" && !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(53, _c4, ctx.isReadonly));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"value\", null);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.SectorList);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(55, _c3, ctx.selectedType == \"job\" && !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(57, _c4, ctx.isReadonly));\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"value\", null);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.RoleList);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", !((tmp_12_0 = ctx.jobForm.get(\"JB_sectorId\")) == null ? null : tmp_12_0.value) && !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(59, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx.jobForm.get(\"JB_jobTitle\")) == null ? null : tmp_15_0.errors) && ((tmp_15_0 = ctx.jobForm.get(\"JB_jobTitle\")) == null ? null : tmp_15_0.errors == null ? null : tmp_15_0.errors.wordLimitExceeded));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(61, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.jobForm.get(\"JB_location\")) == null ? null : tmp_18_0.errors) && ((tmp_18_0 = ctx.jobForm.get(\"JB_location\")) == null ? null : tmp_18_0.errors == null ? null : tmp_18_0.errors.wordLimitExceeded));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(63, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title !== \"View\");\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title === \"View\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(65, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title !== \"View\");\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.title === \"View\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(67, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(69, _c5, ctx.isReadonly))(\"disabled\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"value\", null);\r\n        i0.ɵɵadvance(9);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(71, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(73, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ((tmp_33_0 = ctx.jobForm.get(\"JB_department\")) == null ? null : tmp_33_0.errors) && ((tmp_33_0 = ctx.jobForm.get(\"JB_department\")) == null ? null : tmp_33_0.errors == null ? null : tmp_33_0.errors.wordLimitExceeded));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(75, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(77, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(79, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(81, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ((tmp_45_0 = ctx.jobForm.get(\"JB_hours\")) == null ? null : tmp_45_0.errors) && ((tmp_45_0 = ctx.jobForm.get(\"JB_hours\")) == null ? null : tmp_45_0.errors == null ? null : tmp_45_0.errors.negativeValue));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(83, _c3, !ctx.isReadonly));\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.insightFormArray.controls);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.insightFormArray.length < 3 && !ctx.isReadonly);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n    } }, directives: [i7.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i6.NgIf, i6.NgClass, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.RequiredValidator, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i6.NgForOf, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.FormArrayName, i1.RadioControlValueAccessor, i1.NgModel, i1.FormGroupName], styles: [\".footer[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n\\n.head-exst[_ngcontent-%COMP%] {\\n  width: 100vh;\\n}\\n\\n.readOnlyColor[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n\\n.warning[_ngcontent-%COMP%] {\\n  color: #ee0c0c;\\n  font-size: smaller;\\n  margin-top: 4px;\\n}\\n\\n.info[_ngcontent-%COMP%] {\\n  color: rgba(88, 87, 87, 0.881);\\n  font-size: smaller;\\n  margin-top: 4px;\\n}\\n\\n.fa-plus[_ngcontent-%COMP%] {\\n  font-size: small;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  color: rgba(81, 80, 80, 0.856) !important;\\n}\\n\\n.radio-button-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 20px;\\n}\\n\\n.radio-button-label[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  margin-right: 10px;\\n}\\n\\n.radio-button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.character-count[_ngcontent-%COMP%] {\\n  font-size: smaller;\\n  margin-top: 2px;\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n\\n.fa-plus[_ngcontent-%COMP%] {\\n  font-size: small;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  color: rgba(81, 80, 80, 0.856) !important;\\n}\\n\\n.suggestion[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1224px) {\\n  .insight-btn[_ngcontent-%COMP%] {\\n    padding: 4px 5px;\\n  }\\n\\n  .fas[_ngcontent-%COMP%] {\\n    font-size: small;\\n  }\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1224px) {\\n  .insight-btn[_ngcontent-%COMP%] {\\n    padding: 4px 5px;\\n  }\\n\\n  .fas[_ngcontent-%COMP%] {\\n    font-size: small;\\n  }\\n}\\n\\n@media screen and (min-width: 320px) and (max-width: 768px) {\\n  .btns[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: center;\\n    margin-bottom: 20px;\\n  }\\n\\n  .add-insight-btn[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"] });\r\n"]}, "metadata": {}, "sourceType": "module"}
<app-sidebar>
    <div class="content-wrapper">
        <div class="row"> <!--Add new Grad scheme form -->
            <div class="col-12 grid-margin stretch-card">
                <div class="card">
                    <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">{{title}}
                        Opportunity</div>
                    <div class="card-body">
                        <form [formGroup]="jobForm" (ngSubmit)="onSubmit()" class="forms-sample">
                            <!-- <div class="mt-3">
                                <h5>Opportunity Type</h5>
                                <div class="radio-button-group">
                                    <div *ngIf="!(hideOppType && selectedType === 'jobScheme')" class="radio-button-label">
                                        <input  type="radio" id="job" name="opportunityType" [checked]="selectedType === 'jobScheme'" value="job" [ngModel]="selectedType" (ngModelChange)="onTypeChange($event)" [ngModelOptions]="{standalone: true}">
                                        <label class="mt-2" for="job">Job</label>
                                    </div>
                                    <div *ngIf="!(hideOppType && selectedType === 'job')" class="radio-button-label">
                                        <input type="radio" id="jobScheme" name="opportunityType" value="jobScheme" [checked]="selectedType === 'job'" [ngModel]="selectedType" (ngModelChange)="onTypeChange($event)" [ngModelOptions]="{standalone: true}">
                                        <label class="mt-2" for="jobScheme">Grad Scheme</label>
                                    </div>
                                </div>
                            </div> -->
                            <div class="mt-3">
                                <h5>Opportunity Type</h5>
                                <div class="radio-button-group">
                                    <div *ngIf="!(hideOppType && selectedType === 'jobScheme')" class="radio-button-label">
                                        <input type="radio" id="job" name="opportunityType" value="job" [ngModel]="selectedType" (ngModelChange)="onTypeChange($event)" [ngModelOptions]="{standalone: true}">
                                        <label class="mt-2" for="job">Job</label>
                                    </div>
                                    <div *ngIf="!(hideOppType && selectedType === 'job')" class="radio-button-label">
                                        <input type="radio" id="jobScheme" name="opportunityType" value="jobScheme" [ngModel]="selectedType" (ngModelChange)="onTypeChange($event)" [ngModelOptions]="{standalone: true}">
                                        <label class="mt-2" for="jobScheme">Grad Scheme</label>
                                    </div>
                                </div>
                            </div>
                            
                            <hr>
                            <div class="row">

                                <div class="form-group col-lg-4">
                                    <label for="JB_sectorId" [ngClass]="{'required-field':selectedType=='job'&&!isReadonly}">Sector</label>
                                    <select (change)="onChangeIndustry($event)" id="JB_sectorId" [ngClass]="{'readOnlyColor':isReadonly}" class="form-control form-control-sm"
                                        formControlName="JB_sectorId" required>
                                        <option selected [value]="null" disabled>Please Select A Sector</option>
                                        <option *ngFor="let sector of SectorList" [value]="sector.IN_id">{{ sector.IN_name }}</option>
                                    </select>
                                </div>

                                <div  class="form-group col-lg-4">
                                    <label for="JB_roleId" [ngClass]="{'required-field':selectedType=='job'&&!isReadonly}">Role</label>
                                    <select id="JB_roleId" [ngClass]="{'readOnlyColor':isReadonly}" class="form-control form-control-sm"
                                        formControlName="JB_roleId" required>
                                        <option [value]="null" disabled selected>Please Select Role</option>
                                        <option *ngFor="let roles of RoleList" [value]="roles.RO_id"
                                            [selected]="roles.RO_id === jobForm.get('JB_roleId')?.value">
                                            {{roles.RO_title }}</option>
                                    </select>
                                    <div class="info" *ngIf="!jobForm.get('JB_sectorId')?.value&&!isReadonly">
                                        Please select a sector first.
                                      </div>
                                </div>

                                <!-- <div *ngIf="selectedType != 'job'" class="form-group col-lg-4">
                                    <label for="JB_roleId" class="required-field">Role</label>
                                    <select id="JB_roleId" class="form-control form-control-sm"
                                        formControlName="JB_roleId" required>
                                        <option [value]="null" disabled selected>Please Select Role</option>
                                        <option *ngFor="let roles of RoleList" [value]="roles.RO_id"
                                            [selected]="roles.RO_id === jobForm.get('JB_roleId')?.value">
                                            {{roles.RO_title }}</option>
                                    </select>
                                </div> -->


                                <!-- <div class="form-group col-lg-4">
                                    <label for="JB_type" class="required-field">Opportunity Type</label>
                                    <select id="companySector" class="form-control form-control-sm"
                                        formControlName="JB_type" required>
                                        <option [value]="null" disabled selected>Please Select Opportunity Type</option>
                                        <option value="0">Job</option>
                                        <option value="1">Job Scheme</option>
                                    </select>
                                </div> -->

                                <div class="form-group col-lg-4">
                                    <label [ngClass]="{'required-field':!isReadonly}" for="JB_jobTitle">Title</label>
                                    <input type="textarea" class="form-control form-control-sm" id="JB_jobTitle"
                                        formControlName="JB_jobTitle" required [readonly]="isReadonly" placeholder="Enter Title">
                                    <div class="warning"
                                        *ngIf="jobForm.get('JB_jobTitle')?.errors && jobForm.get('JB_jobTitle')?.errors?.wordLimitExceeded">
                                        Error: Word limit exceeded!(10 Words)
                                    </div>
                                </div>

                                <div class="form-group col-lg-4">
                                    <label for="JB_location" [ngClass]="{'required-field':!isReadonly}">Location</label>
                                    <input type="text" class="form-control form-control-sm"
                                        formControlName="JB_location" required [readonly]="isReadonly" placeholder="e.g. London, UK">
                                    <div class="warning"
                                        *ngIf="jobForm.get('JB_location')?.errors && jobForm.get('JB_location')?.errors?.wordLimitExceeded">
                                        Error: Word limit exceeded!(10 Words)
                                    </div>
                                </div>

                                <div class="form-group col-lg-4">
                                    <label for="JB_applicationDeadline" [ngClass]="{'required-field':!isReadonly}">Application
                                        Deadline</label>
                                    <input *ngIf="title!=='View'" type="date" [min]="minDate" class="form-control form-control-sm"
                                        formControlName="JB_applicationDeadline" required [readonly]="isReadonly"
                                        (change)="updateStartDateMin()"
                                        (input)="checkInvalidDate('JB_applicationDeadline')">

                                     <input *ngIf="title==='View'" type="text" class="form-control form-control-sm" formControlName="JB_applicationDeadline" readonly>   
                                </div>

                                <div class="form-group col-lg-4">
                                    <label for="JB_startDate" [ngClass]="{'required-field':!isReadonly}">Start Date</label>
                                    <input *ngIf="title!=='View'" type="date" [min]="minStartDate" class="form-control form-control-sm"
                                        formControlName="JB_startDate" required [readonly]="isReadonly"
                                        (input)="checkInvalidDate('JB_startDate')">

                                        <input *ngIf="title==='View'" type="text" class="form-control form-control-sm" formControlName="JB_startDate" readonly>   
                                </div>

                                <div class="form-group col-lg-4">
                                    <label for="JB_modeOfWork" [ngClass]="{'required-field':!isReadonly}">Mode of work</label>

                                    <select id="JB_modeOfWork" type="text" [ngClass]="{'text-dark':isReadonly}" class="form-control form-control-sm"
                                        formControlName="JB_modeOfWork" required [disabled]="isReadonly">
                                        <option [value]="null" disabled selected>Please Select Mode Of Work</option>
                                        <option>Remote</option>
                                        <option>On Site</option>
                                        <option>Hybrid</option>
                                    </select>
                                </div>

                                <div class="form-group col-lg-4">
                                    <label for="JB_salary" [ngClass]="{'required-field':!isReadonly}">Salary</label>
                                    <input type="number" class="form-control form-control-sm"
                                        formControlName="JB_salary" required [readonly]="isReadonly" placeholder="Enter Salary">
                                    <!-- <div class="warning"
                                        *ngIf="jobForm.get('JB_salary')?.errors && jobForm.get('JB_salary')?.errors?.negativeValue">
                                        Salary cannot be negative.
                                    </div> -->
                                </div>
                                <div class="form-group col-lg-4">
                                    <label for="JB_department" [ngClass]="{'required-field':!isReadonly}">Department</label>
                                    <input type="text" class="form-control form-control-sm"
                                        formControlName="JB_department" required [readonly]="isReadonly" placeholder="Enter Department">
                                   
                                    <div class="warning"
                                        *ngIf="jobForm.get('JB_department')?.errors && jobForm.get('JB_department')?.errors?.wordLimitExceeded">
                                        Error: Word limit exceeded!(10 Words)
                                    </div>
                                </div>
                                
                                <!-- <div *ngIf="selectedType != 'job'" class="form-group col-lg-4">
                                    <label for="JB_hours" class="required-field">Work Hours</label>
                                    <input type="number" class="form-control form-control-sm" formControlName="JB_hours"
                                        required [readonly]="isReadonly" placeholder="Enter Job Hours" >
                                    
                                    <div class="warning"
                                        *ngIf="jobForm.get('JB_hours')?.errors && jobForm.get('JB_hours')?.errors?.negativeValue">
                                        Job hours must be between 0 and 24.
                                    </div>
                                </div>    -->

                                <div class="form-group col-lg-4">
                                    <label for="JB_description" [ngClass]="{'required-field':!isReadonly}">Description</label>
                                    <textarea class="form-control form-control-sm" formControlName="JB_description"
                                        required [readonly]="isReadonly" rows="5" cols="50" placeholder="Enter Description"
                                        (input)="onInput('JB_description')"></textarea>
                                    
                                    <div *ngIf="!isReadonly">
                                        <span class="character-count">{{ descriptionCharCount }}/500</span>
                                        <div class="warning"
                                            *ngIf="jobForm.get('JB_description')?.errors?.characterLimitExceeded">
                                            Error: Character limit exceeded! (500 Characters)
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group col-lg-4">
                                    <label for="JB_requirements" [ngClass]="{'required-field':!isReadonly}">Requirements</label>
                                    <textarea class="form-control form-control-sm" formControlName="JB_requirements"
                                        required [readonly]="isReadonly" rows="5" cols="50" placeholder="Enter Requirements"
                                        (input)="onInput('JB_requirements')"></textarea>
                                    
                                    <div *ngIf="!isReadonly">
                                        <span class="character-count">{{ requirementsCharCount }}/500</span>
                                        <div class="warning"
                                            *ngIf="jobForm.get('JB_requirements')?.errors?.characterLimitExceeded">
                                            Error: Character limit exceeded! (500 Characters)
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group col-lg-4">
                                    <label for="JB_hrtTips" [ngClass]="{'required-field':!isReadonly}">HR Tips</label>
                                    <textarea class="form-control form-control-sm" formControlName="JB_hrtTips"
                                        required [readonly]="isReadonly" rows="5" cols="50" placeholder="Enter HR Tips"
                                        (input)="onInput('JB_hrtTips')"></textarea>
                                    
                                    <div *ngIf="!isReadonly">
                                        <span class="character-count">{{ hrtTipsCharCount }}/500</span>
                                        <div class="warning"
                                            *ngIf="jobForm.get('JB_hrtTips')?.errors?.characterLimitExceeded">
                                            Error: Character limit exceeded! (500 Characters)
                                        </div>
                                    </div>
                                </div>
                                
                           
                                <div class="form-group col-lg-4">
                                    <label for="JB_hours" [ngClass]="{'required-field':!isReadonly}">Work Hours</label>
                                    <input type="number" class="form-control form-control-sm" formControlName="JB_hours"
                                        required [readonly]="isReadonly" placeholder="Enter Hours" >
                                    
                                    <div class="warning"
                                        *ngIf="jobForm.get('JB_hours')?.errors && jobForm.get('JB_hours')?.errors?.negativeValue">
                                        Hours must be between 0 and 24.
                                    </div>
                                </div>                                

                                <div class="form-group col-lg-4">
                                    <label for="JB_applyLink" [ngClass]="{'required-field':!isReadonly}">Apply Link</label>
                                    <input type="text" class="form-control form-control-sm"
                                        formControlName="JB_applyLink" required [readonly]="isReadonly" placeholder="Enter Apply Link">
                                   
                                </div>
                            </div>

                                <hr>


                                
                                <div formArrayName="JB_insights">
                                    <h6  class="mb-3 py-2">HR / Hiring Manager</h6>
                                    <div *ngFor="let insight of insightFormArray.controls; let i=index" [formGroupName]="i" class="row mb-3">
                                       
                                        <div class="col-lg-11">
            
                                        <div class="row mr-0">
                                      <div class="form-group col-lg-3">
                                        <label class="subtitle" for="HRI_title">Insight Title</label>
                                        <input type="text" class="form-control form-control-sm" formControlName="HRI_title"  placeholder="Enter Title" [readOnly]="isReadonly">
                                      </div>
                                      <div class="form-group col-lg-3">
                                        <label  for="HRI_name" class="subtitle">Name</label>
                                        <input type="text" class="form-control form-control-sm" formControlName="HRI_name"  placeholder="Enter Name" [readOnly]="isReadonly">
                                      </div>
                                      <div class="form-group col-lg-3">
                                        <label for="HRI_position" class="subtitle">Position</label>
                                        <input type="text" class="form-control form-control-sm" formControlName="HRI_position"  placeholder="Enter Position" [readOnly]="isReadonly">
                                      </div>
                                      <div class="form-group col-lg-3">
                                        <label  for="link" class="subtitle">{{!isReadonly?'Upload Insight':'Uploaded Insight'}}</label>
                                        <input *ngIf="title!=='View'" type="file" id="link" class="form-control form-control-sm"
                                               (change)="onAudioSelected($event, i)"
                                               accept="audio/*">

                                               <input *ngIf="title==='View'" type="text" class="form-control form-control-sm" [value]="audioNames[i]" readOnly>

                                      </div>
                                    </div>
            
                                </div>
            
                                      <div class="col-lg-1 px-0 d-flex align-items-center btns">
                                        <button *ngIf="audioUrls[i]" type="button" class="btn insight-btn btn-sm mr-2" [ngClass]="{'btn-outline-primary': !isPlaying[i], 'btn-outline-secondary': isPlaying[i],'mt-2':isReadonly}" (click)="toggleAudio(i)">
                                            <i class="fas" [ngClass]="isPlaying[i] ? 'fa-pause' : 'fa-play'"></i>
                                          </button>
                                        <button *ngIf="insightFormArray.length > 1&&!isReadonly" type="button" class="btn insight-btn btn-sm btn-outline-danger" (click)="removeInsight(i)">
                                          <i class="fas fa-minus icon"></i>
                                        </button>
            
                                        
                                          <audio #audioPlayer controls style="display: none;">
                                            <source [src]="audioUrls[i]" type="audio/mpeg">
                                            Your browser does not support the audio element.
                                          </audio>
                                      </div>
                                      
                                    </div>
                                    
                                    <button type="button" *ngIf="insightFormArray.length < 3&&!isReadonly" class="btn btn-sm add-insight-btn btn-outline-primary" (click)="addInsight()">Add Insight
                                      <i class="fas fa-plus icon"></i>
                                    </button>
                                  </div>

                            <div class="text-center mt-3">
                                <button *ngIf="!isReadonly" type="submit" class="btn btn-primary mr-2">Save</button>
                                <button class="btn btn-light" (click)="goToExistingScheme()">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>



    </div>
</app-sidebar>

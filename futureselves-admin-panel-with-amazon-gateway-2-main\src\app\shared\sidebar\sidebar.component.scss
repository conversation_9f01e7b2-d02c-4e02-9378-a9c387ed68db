.brand-logo img {
  max-width: 100%;
  height: 76px;
  /* Default height for larger screens */
  margin: auto;
  vertical-align: middle;
}

.navbar {
  background-color: #FFFFFF;
}


@media (max-width: 991px) {
  .navbar .navbar-menu-wrapper {
    width: calc(70% - 55px);
    padding-left: 15px;
    padding-right: 11px;
  }
}

@media (max-width: 480px) {
  .navbar .navbar-brand-wrapper {
    width: 100px;
  }
}

@media (max-width: 991px) {
  .navbar .navbar-brand-wrapper {
    width: 100px;
  }
}

.active-menu {
  background-color: #4B49AC;
  color: #ffffff !important;
  border-radius: 6px
}

.active-menu .menu-icon,
.active-menu .menu-title,
.active-menu .menu-arrow {
  color: #ffffff !important;
}

.sidebar .nav .nav-item .nav-link {
  padding: 0.8125rem 1.937rem 0.8125rem 0.5rem !important;
}

.sidebar .nav .nav-item .nav-link i.menu-icon {
  font-size: 1rem;
  line-height: 1;
  margin-right: 0.8rem;
  color: #6C7383;
}

.logOutModal{
  background-color: rgba(0, 0, 0, 0.5);
  color: black !important;
  font-size: 1rem;
}
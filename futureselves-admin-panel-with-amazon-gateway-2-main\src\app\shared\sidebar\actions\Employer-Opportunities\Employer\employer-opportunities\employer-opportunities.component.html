<app-sidebar>
  <div class="content-wrapper fade-in">
    <div *ngIf="!hideHeader" class="row  head-Home">
      <!-- Add and search bar -->
      <div class="col-lg-3 mb-3 mb-lg-0">
        <button type="submit" (click)="ShowNewCompanyForm()" class="btn btn-primary w-100">Add New Employer</button>
      </div>
      <div class="col-lg-6">
        <div class="input-group">
          <input type="text" [(ngModel)]="term" (input)="filterCompanies()" class="form-control shadow-sm rounded-start" placeholder="Search here by employer name"
            style="width: 100%;" aria-label="Search now">
          <!-- <span class="input-group-text bg-primary shadow-sm rounded-end"><i class="icon-search text-white" (click)="filterCompanies()"></i></span> -->
        </div>
      </div>
    </div>  

  
      <div class="row partner" *ngIf="!showForm"> 
        <div class="col-lg-12 grid-margin stretch-card">
          <div class="card">
            <div class="card-header card-title bg-primary rounded-top text-white text-center mb-0">Employer Partners</div>
            <div class="card" style="height: 500px; overflow-y: auto;">
              <div class="card-body">
                <div class="row">
                  <div class="col-lg-3 col-md-6" *ngFor="let company of filteredCompanyList">
                    <div class="partner-card" style="cursor: pointer;" (click)="openExtSchemeform(company)">
                      <img [src]="company.CO_logo" class="card-img-top" [alt]="company.CO_companyName">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

     
      </div>
    </app-sidebar>

  

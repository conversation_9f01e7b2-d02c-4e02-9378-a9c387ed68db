{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { Validators } from '@angular/forms';\nimport html2canvas from 'html2canvas';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common/http\";\nimport * as i7 from \"ngx-spinner\";\nimport * as i8 from \"../../../sidebar.component\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"ng-circle-progress\";\n\nfunction AddEditInsightComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ind_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", ind_r5.IN_id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ind_r5.IN_name, \" \");\n  }\n}\n\nfunction AddEditInsightComponent_audio_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"audio\", 69);\n    i0.ɵɵelement(1, \"source\", 70);\n    i0.ɵɵelement(2, \"source\", 71);\n    i0.ɵɵtext(3, \" Your browser does not support the audio element. \");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"src\", ctx_r1.selectsoundurl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"src\", ctx_r1.selectsoundurl, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction AddEditInsightComponent_a_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 72);\n    i0.ɵɵlistener(\"click\", function AddEditInsightComponent_a_48_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return ctx_r6.changeAction();\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.isReadonly ? \"Edit\" : \"Save\");\n  }\n}\n\nfunction AddEditInsightComponent_button_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 73);\n    i0.ɵɵtext(1, \"Approve\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditInsightComponent_div_138_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" Word limit exceeded! (\", ctx_r9.wordCount, \"/\", ctx_r9.wordLimit, \" words) \");\n  }\n}\n\nfunction AddEditInsightComponent_div_138_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵelementStart(1, \"textarea\", 75, 76);\n    i0.ɵɵlistener(\"input\", function AddEditInsightComponent_div_138_Template_textarea_input_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n\n      const _r8 = i0.ɵɵreference(2);\n\n      const ctx_r10 = i0.ɵɵnextContext();\n      return ctx_r10.checkWordLimit(_r8.value);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AddEditInsightComponent_div_138_div_3_Template, 2, 2, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.wordCount > ctx_r4.wordLimit);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"cncl-btn\": a0\n  };\n};\n\nexport class AddEditInsightComponent {\n  constructor(formBuilder, dataTransferService, toastr, domSanitizer, router, formModule, http, ngxSpinnerService, cdr, renderer) {\n    var _a, _b;\n\n    this.formBuilder = formBuilder;\n    this.dataTransferService = dataTransferService;\n    this.toastr = toastr;\n    this.domSanitizer = domSanitizer;\n    this.router = router;\n    this.formModule = formModule;\n    this.http = http;\n    this.ngxSpinnerService = ngxSpinnerService;\n    this.cdr = cdr;\n    this.renderer = renderer;\n    this.showhide = false;\n    this.soundList = [];\n    this.tempSoundList = [];\n    this.p = 1;\n    this.isReadonly = true;\n    this.Tagtitle = '';\n    this.baseurl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\n    this.state = [];\n    this.transcriptionGenerated = false;\n    this.showButton = true;\n    this.showRegenerateButton = false;\n    this.wordCount = 0;\n    this.wordLimit = 50;\n    this.dataLoaded = false;\n    this.badges = [];\n    this.currentBadgeIndex = 0;\n    this.showApproveBtn = true;\n    this.fileArray = [];\n    const state = (_b = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras) === null || _b === void 0 ? void 0 : _b.state;\n\n    if (state) {\n      this.state = state;\n      this.userId = state.AN_userId;\n      this.AN_id = state.selectedRecord.AN_id;\n    } else {\n      this.router.navigate([`/actions/insights`]);\n    }\n  }\n\n  ngOnInit() {\n    var _a; // this.getAllSoundBite();\n    // this.getalltags()\n\n\n    this.initForm();\n    this.getindustry();\n    (_a = this.soundForm.get('AN_industryId')) === null || _a === void 0 ? void 0 : _a.disable(); // this.getalldegree();\n\n    this.patchAllData(); // this.getBadgeData();\n  }\n\n  initForm() {\n    this.soundForm = this.formBuilder.group({\n      AN_title: ['', [Validators.required]],\n      AN_date: [''],\n      AN_status: [''],\n      AN_recordLink: [''],\n      U_name: [''],\n      U_updatedAt: [''],\n      AN_updatedAt: [''],\n      AN_createdBy: [''],\n      AN_degreeId: [''],\n      AN_description: [''],\n      AN_dp: [''],\n      AN_id: [''],\n      RO_title: [''],\n      AN_industryId: [''],\n      AN_isPublished: [''],\n      AN_keyIdeas: [''],\n      AN_questionId: [''],\n      AN_scheduleTime: [''],\n      AN_updatedBy: [''],\n      DE_title: [''],\n      AN_url2: [''],\n      AN_Reject_Reason: [''],\n      AN_transcription: [''],\n      AN_userId: [''],\n      //  ANT_tagtitle: [''],\n      sound_bites_links: [{\n        AL_answersId: [''],\n        AL_title: [''],\n        AL_url: ['']\n      }],\n      sound_bites_tags: [{\n        ANT_id: [''],\n        ANT_tagId: [''],\n        ANT_tagtitle: ['']\n      }]\n    });\n  }\n\n  get f() {\n    return this.soundForm.controls;\n  }\n\n  patchAllData() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this.getSoundbiteByid(_this.AN_id);\n        _this.soundForm.value.U_name = _this.selectedRecord.U_name;\n        _this.soundForm.value.AN_title = _this.selectedRecord.AN_title;\n        _this.soundForm.value.AN_industryId = _this.selectedRecord.AN_industryId;\n        _this.isanonimise = _this.selectedRecord.AN_isanonimise == true ? 'Yes' : 'No';\n        _this.ispublic = _this.selectedRecord.AN_ispublic == true ? 'Yes' : 'No';\n        _this.selectsoundurl = _this.selectedRecord.AN_recordLink; // this.cdr.detectChanges();\n\n        if (_this.selectedRecord.AN_originalRecordLink) {\n          _this.anonymousGenerated = true;\n        } //if status is pending and transcription is generated\n\n\n        if (_this.selectedRecord.AN_originalRecordLink && _this.selectedRecord.AN_isPublished === '1' && _this.selectedRecord.AN_transcription) {\n          _this.originalRecordLink = _this.selectedRecord.AN_originalRecordLink;\n          _this.anonymousLink = _this.selectedRecord.AN_recordLink;\n          _this.soundForm.value.AN_transcription = _this.selectedRecord.AN_transcription;\n          _this.showRegenerateButton = true;\n          _this.transcriptionGenerated = true;\n        }\n\n        if (!_this.selectedRecord.AN_originalRecordLink && _this.selectedRecord.AN_isPublished === '1' && _this.selectedRecord.AN_transcription) {\n          _this.showModal();\n\n          _this.soundForm.value.AN_transcription = _this.selectedRecord.AN_transcription;\n          _this.transcriptionGenerated = true;\n          _this.showRegenerateButton = true;\n        } //if status is approved\n\n\n        if (_this.selectedRecord.AN_status == '2') {\n          _this.soundForm.value.AN_transcription = _this.selectedRecord.AN_transcription;\n          _this.showButton = false;\n          _this.showApproveBtn = false;\n          _this.transcriptionGenerated = true;\n          _this.originalRecordLink = _this.selectedRecord.AN_originalRecordLink;\n          _this.anonymousLink = _this.selectedRecord.AN_recordLink;\n        } //if status is reject\n\n\n        if (_this.selectedRecord.AN_status == '3' && _this.selectedRecord.AN_transcription) {\n          _this.originalRecordLink = _this.selectedRecord.AN_originalRecordLink;\n          _this.anonymousLink = _this.selectedRecord.AN_recordLink;\n          _this.soundForm.value.AN_transcription = _this.selectedRecord.AN_transcription; // this.showModal();\n\n          _this.showRegenerateButton = true;\n          _this.transcriptionGenerated = true;\n        }\n\n        _this.soundForm.value.RO_title = _this.selectedRecord.RO_title;\n\n        if (_this.selectedRecord && _this.selectedRecord.tags) {\n          _this.Tagtitle = _this.selectedRecord.tags.map(tag => tag.A_title).join(', ');\n        } // const regex = /\\[\\d{2}:\\d{2}:\\d{2}\\.\\d{3}\\]\\s?/g;\n        // this.soundForm.value.AN_transcription=this.selectedRecord.AN_transcription.replace(regex, '').replace(/\\\\n/g, '');\n\n\n        _this.soundForm.patchValue(_this.selectedRecord);\n      } catch (_a) {\n        _this.toastr.error('Error in patching data');\n      }\n    })();\n  }\n\n  showModal() {\n    const modal = document.getElementById('Transcription-modal');\n\n    if (modal != null) {\n      modal.style.display = 'block';\n    }\n  }\n\n  hideModal() {\n    const modal = document.getElementById('Transcription-modal');\n\n    if (modal != null) {\n      modal.style.display = 'none';\n    }\n  } // abc:any;\n\n\n  checkWordLimit(value) {\n    const words = value.trim().split(/\\s+/);\n    this.wordCount = words.length;\n\n    if (this.wordCount > this.wordLimit) {\n      this.toastr.warning(`You have exceeded the word limit of ${this.wordLimit} words.`);\n      return;\n    }\n\n    this.getval(value);\n  }\n\n  getval(item) {\n    // console.log(item,\"textbox value\");\n    this.selectedvalue = item; // console.log(this.selectedvalue, 'textbox value');\n  }\n\n  radiovalue(event, reasonType) {\n    this.selectedvalue = event.target.value;\n    console.log(this.selectedvalue, 'option value');\n\n    if (reasonType == 'existingReason') {\n      this.showhide = false;\n      this.wordCount = 0;\n    } // console.log(this.abc,\"textbox value\");\n\n  }\n\n  showbox() {\n    // this.showhide=true;\n    this.showhide = true;\n  }\n\n  selected(event) {\n    this.filtervalue = event.target.value;\n    console.log('filtervalue', this.filtervalue);\n    this.soundList = [...this.tempSoundList];\n    if (this.filtervalue != 'All') this.soundList = this.soundList.filter(obj => obj.AN_status == this.filtervalue);\n  } // getvalue(value: any) {\n  //   if(value){\n  //   this.Tagtitle = value;\n  // }else{\n  //   this.Tagtitle = '';\n  // }\n  //   console.log(value, 'text tags');\n  // }\n\n\n  onFileSelected(event) {\n    this.fileName = event.target.files[0];\n    console.log('file data', this.fileName);\n    this.selectsoundurl = this.fileName.value;\n  }\n\n  onimageSelected(event) {\n    this.imageName = event.target.files[0];\n    console.log('image data', this.imageName);\n    this.imagedp = this.imageName.value;\n  }\n\n  getUserName(data) {\n    this.dataTransferService.getUserData(data).subscribe(res => {\n      if (res.statusCode == 200) {\n        this.userData = res.data.userDetails[0].U_name;\n        console.log('user data', this.userData);\n      }\n    }, error => {\n      console.log(\"error\", error);\n    });\n  }\n\n  getindustry() {\n    this.dataTransferService.getIndustryData().subscribe(res => {\n      console.log(res, 'industry list');\n\n      if (res.status == 200) {\n        this.industryList = res.data;\n        console.log(this.industryList, 'industrylist');\n      }\n    }, error => {\n      console.log(\"error\", error);\n    });\n  }\n\n  getalltags() {\n    this.dataTransferService.gettags().subscribe(res => {\n      console.log(res);\n\n      if (res.status == 200) {\n        this.TagList = res.data;\n        console.log('Tags : ', this.TagList);\n      }\n    }, error => {\n      console.log(\"error\", error);\n    });\n  } // getalldegree() {\n  //   this.dataTransferService.getdegree().subscribe((res: any) => {\n  //     if (res.statusCode == 200) {\n  //       this.DegreeList = res.data;\n  //     }\n  //   },(error:any)=>{\n  //     console.log(\"error\",error);\n  //   });\n  // }\n\n\n  changeAction() {\n    var _a;\n\n    if (this.isReadonly == false) {\n      const data = {\n        AN_id: this.selectedRecord.AN_id,\n        AN_transcription: (_a = this.soundForm.get('AN_transcription')) === null || _a === void 0 ? void 0 : _a.value\n      };\n      console.log('Data', data);\n      this.ngxSpinnerService.show('globalSpinner');\n      this.dataTransferService.updateTranscription(data).subscribe(res => {\n        if (res.statusCode === 200) {\n          this.ngxSpinnerService.hide('globalSpinner');\n          this.toastr.success('Transcription updated successfully');\n          const updatedTranscription = res.data.AN_transcription;\n          console.log('updatedTranscription', updatedTranscription);\n          this.soundForm.patchValue({\n            AN_transcription: updatedTranscription\n          });\n          this.isReadonly = !this.isReadonly;\n        } else {\n          this.ngxSpinnerService.hide('globalSpinner');\n          console.log(\"Coundn't get 200 status code\");\n          this.toastr.error('Unable to update transcription');\n        }\n      }, error => {\n        this.ngxSpinnerService.hide('globalSpinner');\n        this.toastr.error('Unable to update transcription');\n        console.error('error updating transcription', error);\n      });\n    } else {\n      this.isReadonly = !this.isReadonly;\n    }\n  } // hideAnonymousModal() {\n  //   const modal = document.getElementById('anonymous-warning-modal');\n  //   if (modal != null) {\n  //     modal.style.display = 'none';\n  //   }\n  // }\n  // showAnonymousModal() {\n  //   const modal = document.getElementById('anonymous-warning-modal');\n  //   if (modal != null) {\n  //     modal.style.display = 'block';\n  //   }\n  // }\n  // generateAnnonymousRecording() {\n  //   this.hideAnonymousModal();\n  //   const AN_id = this.selectedRecord.AN_id;\n  //   this.ngxSpinnerService.show('annonymousSpinner');\n  //   this.dataTransferService.generateAnnonymousInsight(AN_id).subscribe(\n  //     (res: any) => {\n  //       console.log('annanymous res', res);\n  //         this.anonymousLink = res.AN_recordLink;\n  //         this.cdr.detectChanges();\n  //         console.log('this.originalRecordLink',this.originalRecordLink);\n  //         this.originalRecordLink = res.AN_originalRecordLink;\n  //         this.ngxSpinnerService.hide('annonymousSpinner');\n  //     },\n  //     (error: any) => {\n  //       this.ngxSpinnerService.hide('annonymousSpinner');\n  //       this.toastr.error('Error generating Anonymous Insight.');\n  //     }\n  //   );\n  // }\n  // GenerateTranscriptById(fromModal?: any) {\n  //   this.AN_id = this.selectedRecord.AN_id;\n  //   console.log('Generate an id: ', this.AN_id);\n  //   if (fromModal) {\n  //     this.hideModal();\n  //     this.showRegenerateButton = true;\n  //   }\n  //   if (\n  //     this.selectedRecord.AN_transcription == '' ||\n  //     this.showRegenerateButton\n  //   ) {\n  //     this.ngxSpinnerService.show('generateTranscription');\n  //     this.dataTransferService.GenerateTranscriptById(this.AN_id).subscribe(\n  //       (res: any) => {\n  //         if (res.externalApiResponse.status === 'success') {\n  //           console.log('Generate button data', res);\n  //           const newTranscription = res.record.AN_transcription;\n  //           if (newTranscription !== '') {\n  //             // this.soundForm.patchValue({\n  //             //   AN_transcription: this.newTranscription,\n  //             // });\n  //             this.retryGetSoundBite(this.AN_id);\n  //             this.transcriptionGenerated = true;\n  //             this.showRegenerateButton = true;\n  //             //   if (this.wantToAnonymous && this.showRegenerateButton !== true) {\n  //             //     this.generateAnnonymousRecording(this.AN_id)\n  //             // }\n  //           } else {\n  //             this.GenerateTranscriptById();\n  //           }\n  //         } else {\n  //           this.handleApiError(res.message, this.AN_id);\n  //         }\n  //       },\n  //       (error: any) => {\n  //         // this.ngxSpinnerService.hide('generateTranscription');\n  //         this.handleError(error, this.AN_id);\n  //       }\n  //     );\n  //   } else {\n  //     this.soundForm.patchValue({\n  //       AN_transcription: this.selectedRecord.AN_transcription,\n  //     });\n  //     this.transcriptionGenerated = true;\n  //     this.showRegenerateButton = true; // if(this.wantToAnonymous){\n  //     //   this.generateAnnonymousRecording(this.AN_id)\n  //     // }\n  //   }\n  // }\n  // handleApiError(message: string, AN_id: any) {\n  //   if (message === 'Endpoint request timed out') {\n  //     this.retryGetSoundBite(AN_id);\n  //   } else {\n  //     this.ngxSpinnerService.hide('generateTranscription');\n  //     this.toastr.error('Unable to generate transcription please try again');\n  //     console.log('Error message: ', message);\n  //   }\n  // }\n  // handleError(error: any, AN_id: any) {\n  //   if (error.status === 504) {\n  //     console.log('504 Gateway Timeout error');\n  //     this.retryGetSoundBite(AN_id);\n  //   } else {\n  //     this.toastr.error('Something went wrong');\n  //     console.log('Error', error);\n  //     this.ngxSpinnerService.hide('generateTranscription');\n  //   }\n  // }\n  // retryGetSoundBite(AN_id: any) {\n  //   this.dataTransferService.getSoundBite().subscribe({\n  //     next: (res: any) => {\n  //       console.log(res);\n  //       if (res.statusCode === 200) {\n  //         let soundbite = res.data.find(\n  //           (soundbite: any) => soundbite.AN_id === AN_id\n  //         );\n  //         if (soundbite) {\n  //           console.log('Found soundbite: ', soundbite);\n  //           if (soundbite.AN_transcription == '') {\n  //             this.GenerateTranscriptById();\n  //           } else {\n  //             this.ngxSpinnerService.hide('generateTranscription');\n  //             // this.soundForm.value.AN_transcription = soundbite.AN_transcription;\n  //             this.soundForm.patchValue({\n  //               AN_transcription: soundbite.AN_transcription,\n  //             });\n  //             this.transcriptionGenerated = true;\n  //             this.showRegenerateButton = true;\n  //             // if (this.wantToAnonymous && this.showRegenerateButton !== true) {\n  //             //   this.generateAnnonymousRecording(this.AN_id)\n  //             // }\n  //           }\n  //         } else {\n  //           this.ngxSpinnerService.hide('generateTranscription');\n  //           this.toastr.error('Failed to generate insight, Try again');\n  //         }\n  //       } else {\n  //         this.ngxSpinnerService.hide('generateTranscription');\n  //         this.toastr.error('Failed to generate insight, Try again');\n  //       }\n  //     },\n  //     error: (error: any) => {\n  //       console.log('Error Message', error);\n  //       this.ngxSpinnerService.hide('generateTranscription');\n  //       this.toastr.error(\n  //         'Error retrieving insight',\n  //         error.message || 'Unknown error occurred'\n  //       );\n  //     },\n  //   });\n  // }\n  //     updateStatus(record: any, val: any) {\n  //       if (this.wordCount > this.wordLimit) {\n  //         console.error('Cannot update reason. Word limit exceeded.');\n  //     }\n  //     else{\n  //     //Approve status\n  //     console.log('approve reject data', record, val);\n  //     // this.soundForm.patchValue(record);\n  //     this.AN_status1 = val.toString();\n  //     this.soundForm.value.AN_status = this.AN_status1;\n  //     this.soundForm.value.AN_isPublished = this.AN_status1;\n  //     this.ID = record.AN_id;\n  //     this.soundForm.value.AN_id = this.ID;\n  //     // console.log(this.soundForm.value.AN_status, this.soundForm.value.AN_isPublished, this.soundForm.value, \"updated status\");\n  //     console.log('DATA-UPDATE_STATUS', this.soundForm.value);\n  //     // if(this.transcriptionGenerated){\n  //     this.dataTransferService.updateStatus(this.soundForm.value).subscribe(\n  //       (res: any) => {\n  //         if (res.statusCode == 200) {\n  //           this.toastr.success('Status saved successfully', res.message);\n  //           this.getAllSoundBite();\n  //           this.router.navigate(['/actions/insights']);\n  //         } else {\n  //           this.toastr.error(\n  //             'Error',\n  //             res ? res.message : 'Unknown error occurred'\n  //           );\n  //         }\n  //       },\n  //       (error: any) => {\n  //         this.toastr.error('Error', error.message || 'Unknown error occurred');\n  //       }\n  //     );\n  //     // }else{\n  //     //   this.toastr.error(\"Please generate transcription before approving.\");\n  //     // }\n  //   }\n  // }\n\n\n  getAllSoundBite() {\n    this.ngxSpinnerService.show('globalSpinner');\n    this.dataTransferService.getSoundBite().subscribe({\n      next: res => {\n        console.log(res);\n\n        if (res.statusCode == 200) {\n          this.soundList = res.data;\n          this.tempSoundList = [...this.soundList];\n          console.log('Sound', this.soundList);\n          this.ngxSpinnerService.hide('globalSpinner');\n        }\n      },\n      error: error => {\n        console.log('Error Message', error);\n        this.ngxSpinnerService.hide('globalSpinner');\n      }\n    });\n  }\n\n  updateAllData(record) {\n    this.soundForm.value.AN_id = record.AN_id;\n    this.soundForm.value.sound_bites_tags[0].ANT_tagtitle = this.Tagtitle;\n    console.log(this.soundForm.value, 'updated Status');\n    let postData = {\n      AN_id: record.AN_id,\n      ANT_tagtitle: this.Tagtitle\n    };\n    this.dataTransferService.updateAllData(postData).subscribe(res => {\n      console.log('Url Record', res);\n\n      if (!res.error) {\n        this.toastr.success('Save data succesfully', res.message);\n        this.getAllSoundBite();\n        this.router.navigate([`/actions/insights`]);\n      } else {\n        this.toastr.error('', res.message);\n      }\n    }, error => {\n      console.log(\"error\", error);\n    });\n  }\n\n  getuploadlink(record) {\n    console.log('Record :', record); // console.log(this.soundForm.value.AN_recordLink, \"response\");\n    // console.log(this.fileName, 'file');\n\n    this.dataTransferService.uploadurl(this.fileName).subscribe(res => {\n      console.log('Url Record', res);\n\n      if (!res.error) {\n        this.toastr.success('', res.message);\n        this.soundForm.patchValue(record);\n        this.getAllSoundBite();\n      } else {\n        this.toastr.error('', res.message);\n      }\n    });\n    this.soundForm.value.AN_id = record.AN_id;\n    this.soundForm.value.AN_url2 = record.AN_recordLink;\n    this.soundForm.value.AN_recordLink = this.baseurl + this.fileName.name;\n    let postData = {\n      AN_id: record.AN_id,\n      AN_url2: record.AN_recordLink,\n      AN_recordLink: this.baseurl + this.fileName.name\n    };\n    this.dataTransferService.updateAll(postData).subscribe(res => {\n      console.log('update Record', res);\n\n      if (!res.error) {\n        this.toastr.success('Save data succesfully', res.message); // this.getAllSoundBite();\n      } else {\n        this.toastr.error('', res.message);\n      }\n    }, error => {\n      console.log(\"error\", error);\n    });\n  }\n\n  getuploadDp(record) {\n    console.log('Record :', record); // this.imagedp = this.imageName.name;\n    // this.soundForm.value.AN_dp = this.baseurl + this.imagedp;\n\n    console.log(this.imageName, 'file');\n    this.dataTransferService.uploadurl(this.imageName).subscribe(res => {\n      console.log('Url Record', res);\n\n      if (!res.error) {\n        this.toastr.success('upload file successfully', res.message);\n        this.soundForm.patchValue(record); // this.getAllSoundBite();\n      } else {\n        this.toastr.error('', res.message);\n      }\n    });\n    this.soundForm.value.AN_id = record.AN_id;\n    this.imagedp = this.imageName.name;\n    this.soundForm.value.AN_dp = this.baseurl + this.imagedp;\n    let postData = {\n      AN_id: record.AN_id,\n      AN_dp: this.baseurl + this.imagedp\n    };\n    this.dataTransferService.updateAll(postData).subscribe(res => {\n      console.log('Url Record', res);\n\n      if (!res.error) {\n        this.toastr.success('Save data succesfully', res.message);\n        this.getAllSoundBite();\n      } else {\n        this.toastr.error('', res.message);\n      }\n    }, error => {\n      console.log(\"error\", error);\n    });\n  }\n\n  onReset() {\n    this.soundForm.reset();\n  }\n\n  listPage() {\n    this.router.navigate([`/actions/insights`]);\n  }\n\n  insightAcceptedNotification() {\n    const data = {\n      UN_contentId: \"1\",\n      UN_userId: this.selectedRecord.AN_userId\n    };\n    console.log(\"insightAcceptedNotification data\", data);\n    this.dataTransferService.insightAcceptedNotification(data).subscribe(res => {\n      console.log(\"insightAcceptedNotification called\");\n    }, error => {\n      console.log(\"Error in insightAcceptedNotification\", error);\n    });\n  }\n\n  insightRejectedNotification() {\n    const data = {\n      UN_contentId: \"1\",\n      UN_userId: this.selectedRecord.AN_userId,\n      UN_rejected_title: this.selectedvalue\n    };\n    console.log(\"insightRejectedNotification data\", data);\n    this.dataTransferService.insightRejectedNotification(data).subscribe(res => {\n      console.log(\"insightRejectedNotification called\");\n    }, error => {\n      console.log(\"Error in insightRejectedNotification\", error);\n    });\n  }\n\n  updatedReason() {\n    return new Promise((resolve, reject) => {\n      this.soundForm.value.AN_Reject_Reason = this.selectedvalue;\n      this.soundForm.value.AN_id = this.ID;\n      this.dataTransferService.updateRejectReason(this.soundForm.value).subscribe(res => {\n        console.log('updated reason', res); // this.toastr.success('Reason updated successfully');\n\n        this.router.navigate([`/actions/insights`]); // Resolve the promise with the response\n\n        resolve(res);\n      }, error => {\n        this.toastr.error(\"Error updating reason\");\n        console.log(\"error\", error); // Reject the promise with the error\n\n        reject(error);\n      });\n    });\n  }\n\n  checkUpdateStatus(record, val) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      // if (this.transcriptionGenerated) {\n      if (_this2.wordCount > _this2.wordLimit) {\n        _this2.toastr.error('Cannot update reason. Word limit exceeded.');\n\n        console.error('Cannot update reason. Word limit exceeded.');\n        return;\n      } // Show spinner before starting the update\n\n\n      _this2.ngxSpinnerService.show('globalSpinner'); // Prepare the data\n\n\n      _this2.AN_status1 = val.toString();\n      _this2.soundForm.value.AN_status = _this2.AN_status1;\n      _this2.soundForm.value.AN_isPublished = _this2.AN_status1;\n      _this2.ID = record.AN_id;\n      _this2.soundForm.value.AN_id = _this2.ID;\n\n      try {\n        if (val === 2) {\n          yield _this2.updateStatusAPI(_this2.soundForm.value);\n          console.log('Status updated successfully');\n\n          _this2.insightAcceptedNotification();\n\n          const badgeData = yield _this2.getBadgeData(); //getting badge data here\n\n          console.log('Badge Data:', badgeData);\n\n          if (badgeData && badgeData.entries && badgeData.entries.length > 0) {\n            //generating certificate from that badge data\n            _this2.generateCertificates(badgeData);\n          } else {\n            console.warn('No badge data available');\n          } // this.ngxSpinnerService.hide('globalSpinner');\n\n        } else {\n          yield _this2.updatedReason();\n          yield _this2.updateStatusAPI(_this2.soundForm.value);\n\n          _this2.insightRejectedNotification();\n\n          _this2.getAllSoundBite();\n\n          _this2.router.navigate(['/actions/insights']);\n\n          _this2.ngxSpinnerService.hide('globalSpinner'); // Hide spinner after navigation\n\n        }\n      } catch (error) {\n        console.error('Error in updateStatus:', error);\n\n        _this2.ngxSpinnerService.hide('globalSpinner'); // Hide spinner on error\n\n      } // } else {\n      //   this.toastr.info('Please generate the transcription first');\n      // }\n\n    })();\n  }\n\n  getBadgeData() {\n    return new Promise((resolve, reject) => {\n      const userId = this.selectedRecord.AN_userId;\n      const industryId = this.selectedRecord.AN_industryId;\n      const roleId = this.selectedRecord.AN_degreeId;\n      console.log('Fetching badge data for user:', userId, industryId, roleId);\n      this.dataTransferService.getBadgeData(userId, industryId, roleId).subscribe(badgeData => {\n        console.log('Received badge data:', badgeData);\n        resolve(badgeData);\n      }, error => {\n        this.getAllSoundBite();\n        this.router.navigate(['/actions/insights']);\n        console.info('No Badges Found !');\n        reject(new Error(error.message || 'Unknown error occurred'));\n      });\n    });\n  }\n\n  updateStatusAPI(data) {\n    return new Promise((resolve, reject) => {\n      this.dataTransferService.updateStatus(data).subscribe(res => {\n        if (res.statusCode === 200) {\n          this.toastr.success('Status updated successfully', res.message);\n          resolve();\n        } else {\n          this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner on error\n\n          reject(new Error(res ? res.message : 'Unknown error occurred'));\n        }\n      }, error => {\n        this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner on error\n\n        reject(new Error(error.message || 'Unknown error occurred'));\n        this.toastr.error('Error', error.message || 'Unknown error occurred');\n      });\n    });\n  }\n\n  generateCertificates(badgeData) {\n    this.badgeData = badgeData;\n    const badges = badgeData.entries[0].badges;\n\n    if (!badges || badges.length === 0) {\n      console.warn('No badges to generate certificates for.');\n      return;\n    }\n\n    this.badges = badges;\n    this.currentBadgeIndex = 0; // Initialize the current badge index\n\n    this.processBadge();\n  }\n\n  processBadge() {\n    if (this.currentBadgeIndex >= this.badges.length) {\n      // All badges processed, navigate to the new route\n      if (this.badges.length > 0) {\n        this.saveBadge().then(() => {\n          this.router.navigate(['/actions/insights']);\n          this.ngxSpinnerService.hide('globalSpinner');\n          return;\n        });\n      } else {\n        this.getAllSoundBite();\n        this.router.navigate(['/actions/insights']);\n        this.ngxSpinnerService.hide('globalSpinner');\n        return;\n      }\n    }\n\n    const badge = this.badges[this.currentBadgeIndex];\n    this.userName = this.badgeData.user.userName; // Use stored badgeData\n\n    this.industryName = this.badgeData.entries[0].industryName;\n    this.roleName = this.badgeData.entries[0].roleTitle;\n    this.certificateTitle = badge === null || badge === void 0 ? void 0 : badge.BA_title;\n    this.BA_id = badge === null || badge === void 0 ? void 0 : badge.BA_id;\n    this.certificateDescription = badge === null || badge === void 0 ? void 0 : badge.BA_description;\n    this.badgeImage = badge === null || badge === void 0 ? void 0 : badge.BA_image;\n    this.dateEarned = this.formatDate(new Date()); // Set today's date\n\n    this.BA_percentage = badge === null || badge === void 0 ? void 0 : badge.BA_percentage;\n    this.cdr.detectChanges();\n    setTimeout(() => {\n      this.captureImage(this.currentBadgeIndex, this.userName, this.certificateTitle);\n    }, 2000); // Delay to ensure data is rendered\n  }\n\n  formatDate(date) {\n    const options = {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    };\n    return date.toLocaleDateString('en-US', options);\n  }\n\n  captureImage(index, userName, certificateTitle) {\n    this.ngxSpinnerService.show('globalSpinner'); // Show spinner while capturing and uploading the image\n\n    const code = document.getElementById('certificate');\n\n    if (code) {\n      html2canvas(code).then(canvas => {\n        const imgData = canvas.toDataURL('image/png'); // const timestamp = new Date().toISOString().replace(/[:.-]/g, '');\n\n        const AN_userId = this.selectedRecord.AN_userId;\n        const RoleId = this.selectedRecord.AN_degreeId;\n        const fileName = `${RoleId}_${AN_userId}_Badge_${certificateTitle}`;\n        const file = this.dataURLtoFile(imgData, fileName); // Upload the file\n\n        this.uploadImage(file) // .then(() => {\n        //   this.saveBadge(file);\n        // })\n        .then(() => {\n          this.currentBadgeIndex++;\n          this.processBadge(); // Process the next badge\n        }).catch(error => {\n          console.error('Error uploading badge:', error);\n          this.toastr.error('Error', 'Failed to upload badge.');\n        });\n      }).catch(error => {\n        console.error('Error capturing badge:', error);\n        this.toastr.error('Error', 'Failed to capture badge.');\n        this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner\n      });\n    } else {\n      console.error(\"Element with ID 'certificate' not found.\");\n      this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner\n    }\n  }\n\n  dataURLtoBlob(dataURL) {\n    // Split the data URL into parts\n    const arr = dataURL.split(','); // Extract the MIME type\n\n    const mimeMatch = arr[0].match(/:(.*?);/);\n\n    if (!mimeMatch) {\n      throw new Error('Invalid data URL');\n    }\n\n    const mime = mimeMatch[1]; // Decode the base64 string\n\n    const bstr = atob(arr[1]);\n    const u8arr = new Uint8Array(bstr.length); // Populate the Uint8Array with the base64 string bytes\n\n    for (let i = 0; i < bstr.length; i++) {\n      u8arr[i] = bstr.charCodeAt(i);\n    } // Return the Blob with the correct MIME type\n\n\n    return new Blob([u8arr], {\n      type: mime\n    });\n  }\n\n  dataURLtoFile(dataURL, filename) {\n    const blob = this.dataURLtoBlob(dataURL);\n    return new File([blob], filename, {\n      type: blob.type\n    });\n  }\n\n  uploadImage(file) {\n    return new Promise((resolve, reject) => {\n      this.dataTransferService.uploadurl(file).subscribe(res => {\n        console.log('Badge uploaded successfully in S3 Bucket:');\n        const Newfile = this.baseurl + file.name;\n        const data = {\n          UB_badgeId: this.BA_id,\n          UB_roleId: this.selectedRecord.AN_degreeId,\n          UB_sectorId: this.selectedRecord.AN_industryId,\n          UB_userId: this.selectedRecord.AN_userId,\n          UN_title: this.certificateTitle,\n          UB_badge: Newfile\n        };\n        this.fileArray.push(data);\n        resolve();\n      }, error => {\n        console.error('Error uploading Badge  in S3 Bucket:', error);\n        this.toastr.error('Error uploading Badge');\n        reject(new Error(error.message || 'Unknown error occurred'));\n      });\n    });\n  }\n\n  saveBadge() {\n    return new Promise((resolve, reject) => {\n      console.log(\"this.fileArray\", this.fileArray);\n      this.dataTransferService.saveBadge(this.fileArray).subscribe(res => {\n        console.log('Badge saved in database');\n        resolve();\n      }, error => {\n        // this.toastr.error('Error uploading the badge.');\n        console.error('Error uploading Badge:', error);\n        reject(new Error(error.message || 'Unknown error occurred'));\n      });\n    });\n  }\n\n  downloadImage(file, fileName) {\n    const a = document.createElement('a');\n    a.href = file;\n    a.download = fileName;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n  }\n\n  getBadgeImage(title) {\n    switch (title) {\n      case 'Insight Initiator':\n        return './assets/images/badge_1.png';\n\n      case 'Career Contributor':\n        return './assets/images/badge_2.png';\n\n      case 'Social Mobility Champion':\n        return './assets/images/badge_3.png';\n\n      default:\n        return null;\n    }\n  } // downloadImage(dataUrl: string, filename: string) {\n  //   const link = document.createElement('a');\n  //   link.href = dataUrl;\n  //   link.download = filename;\n  //   document.body.appendChild(link);\n  //   link.click();\n  //   document.body.removeChild(link);\n  // }\n\n\n  getSoundbiteByid(AN_id) {\n    return new Promise((resolve, reject) => {\n      this.dataTransferService.getSoundbiteByid(AN_id).subscribe(res => {\n        if (res.statusCode === 200) {\n          this.selectedRecord = res.data;\n          resolve(res.data);\n        } else {\n          reject(`Unexpected status code: ${res.statusCode}`);\n        }\n      }, error => {\n        console.log(\"error\", error);\n        reject(error);\n      });\n    });\n  }\n\n}\n\nAddEditInsightComponent.ɵfac = function AddEditInsightComponent_Factory(t) {\n  return new (t || AddEditInsightComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.DomSanitizer), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i1.FormsModule), i0.ɵɵdirectiveInject(i6.HttpClient), i0.ɵɵdirectiveInject(i7.NgxSpinnerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n};\n\nAddEditInsightComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AddEditInsightComponent,\n  selectors: [[\"app-add-edit-insight\"]],\n  decls: 144,\n  vars: 45,\n  consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\"], [1, \"form-group\", \"col-lg-4\"], [\"for\", \"\"], [\"type\", \"text\", \"formControlName\", \"U_name\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"required\", \"\", \"formControlName\", \"AN_industryId\", 1, \"form-control\", \"form-control-sm\", 2, \"color\", \"#646363\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"formControlName\", \"RO_title\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"type\", \"text\", \"formControlName\", \"AN_title\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"ANT_tagtitle\"], [\"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\", \"value\"], [\"for\", \"is_annonymous\"], [1, \"original\"], [\"for\", \"AN_recordLink\"], [\"type\", \"hidden\", \"formControlName\", \"AN_recordLink\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"controls\", \"\", 4, \"ngIf\"], [1, \"form-group\", \"col-lg-8\"], [\"id\", \"exampleFormControlTextarea1\", \"rows\", \"8\", \"formControlName\", \"AN_transcription\", 1, \"form-control\", \"transcription\", 2, \"font-size\", \"22px\", 3, \"readOnly\"], [\"class\", \"saveBtn\", 3, \"click\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"type\", \"submit\", \"class\", \"btn btn-success\", \"data-toggle\", \"modal\", \"data-target\", \"#confirmtoapprove\", 4, \"ngIf\"], [\"type\", \"submit\", \"data-toggle\", \"modal\", \"data-target\", \"#myModal\", 1, \"btn\", \"btn-danger\"], [1, \"btn\", \"btn-secondary\", 3, \"ngClass\", \"click\"], [\"id\", \"confirmtoapprove\", \"role\", \"dialog\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"mar-top\"], [1, \"modal-content\"], [1, \"modal-header\"], [2, \"margin-left\", \"150px\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"close\", 2, \"color\", \"white\"], [1, \"modal-body\", \"text-center\"], [1, \"modal-footer\"], [\"id\", \"left\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"id\", \"right\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\"], [\"id\", \"certificate\", 1, \"py-3\", \"px-3\", 2, \"position\", \"absolute\", \"top\", \"0\", \"left\", \"-999999px\", \"visibility\", \"visible\"], [1, \"image\", \"text-center\", \"mb-2\"], [\"alt\", \"Badge\", \"width\", \"80\", \"height\", \"80\", 3, \"src\"], [1, \"title\", \"text-center\"], [1, \"font-weight-bold\"], [1, \"badge\", \"badge-pill\", \"badge-certificate\", \"mt-2\", \"font-weight-bold\"], [1, \"name\", \"text-center\", \"mt-3\"], [1, \"p-block\", \"mb-1\", \"font-weight-bold\"], [1, \"p-block\"], [1, \"career\", \"text-center\", \"mt-3\"], [1, \"date\", \"text-center\", \"mt-3\"], [1, \"msg\", \"d-flex\", \"justify-content-center\", \"mt-3\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-center\"], [1, \"progress-container\", \"text-center\", \"mt-3\"], [1, \"p-block\", \"font-weight-bold\", \"mb-2\"], [1, \"progress-meter\", \"d-flex\", \"justify-content-center\", 3, \"percent\", \"maxPercent\", \"toFixed\", \"radius\", \"space\", \"outerStrokeGradient\", \"outerStrokeWidth\", \"outerStrokeColor\", \"outerStrokeGradientStopColor\", \"innerStrokeColor\", \"innerStrokeWidth\", \"showTitle\", \"showUnits\", \"showSubtitle\", \"showBackground\", \"showInnerStroke\", \"clockwise\", \"responsive\", \"startFromZero\", \"showZeroOuterStroke\"], [1, \"image\", \"text-start\", \"mt-2\"], [\"src\", \"./assets/images/Orange icon_transperant.png\", \"alt\", \"\", \"width\", \"24\", \"height\", \"27\"], [1, \"brandlogo\"], [\"id\", \"myModal\", \"role\", \"dialog\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"form-check\", \"mb-3\"], [\"name\", \"feedback\", \"type\", \"radio\", \"value\", \"It's irrelevant\", 3, \"change\"], [1, \"ml-3\"], [\"name\", \"feedback\", \"type\", \"radio\", \"value\", \"It's not right\", 3, \"change\"], [\"name\", \"feedback\", \"type\", \"radio\", \"value\", \"its inappropriate\", 3, \"change\"], [1, \"form-check\", \"mb-4\"], [\"name\", \"feedback\", \"type\", \"radio\", \"value\", \"Other\", 3, \"click\", \"change\"], [\"class\", \"mx-4\", 4, \"ngIf\"], [3, \"value\"], [\"controls\", \"\"], [\"type\", \"audio/ogg\"], [\"type\", \"audio/mpeg\"], [1, \"saveBtn\", 3, \"click\"], [\"type\", \"submit\", \"data-toggle\", \"modal\", \"data-target\", \"#confirmtoapprove\", 1, \"btn\", \"btn-success\"], [1, \"mx-4\"], [\"type\", \"textarea\", \"placeholder\", \"Other reason\", \"rows\", \"4\", 1, \"form-control\", 3, \"input\"], [\"box\", \"\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"]],\n  template: function AddEditInsightComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"app-sidebar\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵtext(6, \"Insight Details\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵelementStart(8, \"form\", 6);\n      i0.ɵɵelementStart(9, \"div\", 1);\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵelementStart(11, \"label\", 8);\n      i0.ɵɵtext(12, \"User\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(13, \"input\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"div\", 7);\n      i0.ɵɵelementStart(15, \"label\", 8);\n      i0.ɵɵtext(16, \"Sector Name \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"select\", 10);\n      i0.ɵɵtemplate(18, AddEditInsightComponent_option_18_Template, 2, 2, \"option\", 11);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"div\", 7);\n      i0.ɵɵelementStart(20, \"label\", 8);\n      i0.ɵɵtext(21, \"Role\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(22, \"input\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(23, \"div\", 7);\n      i0.ɵɵelementStart(24, \"label\", 8);\n      i0.ɵɵtext(25, \"Question\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(26, \"input\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"div\", 7);\n      i0.ɵɵelementStart(28, \"label\", 14);\n      i0.ɵɵtext(29, \"Tags\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(30, \"input\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"div\", 7);\n      i0.ɵɵelementStart(32, \"label\", 16);\n      i0.ɵɵtext(33, \"Is Public?\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(34, \"input\", 15);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(35, \"div\", 7);\n      i0.ɵɵelementStart(36, \"div\", 17);\n      i0.ɵɵelementStart(37, \"label\", 18);\n      i0.ɵɵtext(38, \"Insight Link\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(39, \"br\");\n      i0.ɵɵelement(40, \"input\", 19);\n      i0.ɵɵtemplate(41, AddEditInsightComponent_audio_41_Template, 4, 2, \"audio\", 20);\n      i0.ɵɵelement(42, \"br\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"div\", 21);\n      i0.ɵɵelementStart(44, \"label\", 8);\n      i0.ɵɵtext(45, \"Transcription \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(46, \"br\");\n      i0.ɵɵelement(47, \"textarea\", 22);\n      i0.ɵɵtemplate(48, AddEditInsightComponent_a_48_Template, 2, 1, \"a\", 23);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"div\", 24);\n      i0.ɵɵtemplate(50, AddEditInsightComponent_button_50_Template, 2, 0, \"button\", 25);\n      i0.ɵɵtext(51, \"\\u00A0 \");\n      i0.ɵɵelementStart(52, \"button\", 26);\n      i0.ɵɵtext(53, \"Reject\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(54, \"\\u00A0 \");\n      i0.ɵɵelementStart(55, \"button\", 27);\n      i0.ɵɵlistener(\"click\", function AddEditInsightComponent_Template_button_click_55_listener() {\n        return ctx.listPage();\n      });\n      i0.ɵɵtext(56, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(57, \"div\", 28);\n      i0.ɵɵelementStart(58, \"div\", 29);\n      i0.ɵɵelementStart(59, \"div\", 30);\n      i0.ɵɵelementStart(60, \"div\", 31);\n      i0.ɵɵelementStart(61, \"h3\", 32);\n      i0.ɵɵtext(62, \"Confirmation !\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(63, \"button\", 33);\n      i0.ɵɵtext(64, \"\\u00D7\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(65, \"div\", 34);\n      i0.ɵɵelementStart(66, \"strong\");\n      i0.ɵɵtext(67, \"Are you sure you want to approve this Insight ?\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(68, \"div\", 35);\n      i0.ɵɵelementStart(69, \"button\", 36);\n      i0.ɵɵlistener(\"click\", function AddEditInsightComponent_Template_button_click_69_listener() {\n        return ctx.checkUpdateStatus(ctx.selectedRecord, 2);\n      });\n      i0.ɵɵtext(70, \"Confirm \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(71, \"button\", 37);\n      i0.ɵɵtext(72, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(73, \"div\", 38);\n      i0.ɵɵelementStart(74, \"div\", 39);\n      i0.ɵɵelement(75, \"img\", 40);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(76, \"div\", 41);\n      i0.ɵɵelementStart(77, \"h3\", 42);\n      i0.ɵɵtext(78);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(79, \"span\", 43);\n      i0.ɵɵtext(80, \"gradvisor certified\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(81, \"div\", 44);\n      i0.ɵɵelementStart(82, \"span\", 45);\n      i0.ɵɵtext(83, \"Sharer\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(84, \"span\", 46);\n      i0.ɵɵtext(85);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(86, \"div\", 47);\n      i0.ɵɵelementStart(87, \"span\", 45);\n      i0.ɵɵtext(88, \"Career\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(89, \"span\", 46);\n      i0.ɵɵtext(90);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(91, \"span\", 46);\n      i0.ɵɵtext(92);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(93, \"div\", 48);\n      i0.ɵɵelementStart(94, \"span\", 45);\n      i0.ɵɵtext(95, \"Date Earned\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(96, \"span\", 46);\n      i0.ɵɵtext(97);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(98, \"div\", 49);\n      i0.ɵɵelementStart(99, \"div\", 50);\n      i0.ɵɵelementStart(100, \"p\", 51);\n      i0.ɵɵtext(101);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(102, \"div\", 52);\n      i0.ɵɵelementStart(103, \"span\", 53);\n      i0.ɵɵtext(104, \"Your Progress\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(105, \"circle-progress\", 54);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(106, \"div\", 55);\n      i0.ɵɵelement(107, \"img\", 56);\n      i0.ɵɵelementStart(108, \"span\", 57);\n      i0.ɵɵtext(109, \"gradvisor\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(110, \"div\", 58);\n      i0.ɵɵelementStart(111, \"div\", 59);\n      i0.ɵɵelementStart(112, \"div\", 30);\n      i0.ɵɵelementStart(113, \"div\", 31);\n      i0.ɵɵelementStart(114, \"h3\");\n      i0.ɵɵtext(115, \"Reason For Rejection\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(116, \"button\", 33);\n      i0.ɵɵtext(117, \"\\u00D7\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(118, \"div\", 34);\n      i0.ɵɵelementStart(119, \"strong\");\n      i0.ɵɵtext(120, \"Please Kindly Give Your Reason.\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(121, \"hr\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(122, \"div\", 60);\n      i0.ɵɵelementStart(123, \"input\", 61);\n      i0.ɵɵlistener(\"change\", function AddEditInsightComponent_Template_input_change_123_listener($event) {\n        return ctx.radiovalue($event, \"existingReason\");\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(124, \"label\", 62);\n      i0.ɵɵtext(125, \"It's irrelevant\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(126, \"div\", 60);\n      i0.ɵɵelementStart(127, \"input\", 63);\n      i0.ɵɵlistener(\"change\", function AddEditInsightComponent_Template_input_change_127_listener($event) {\n        return ctx.radiovalue($event, \"existingReason\");\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(128, \"label\", 62);\n      i0.ɵɵtext(129, \"It's not right\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(130, \"div\", 60);\n      i0.ɵɵelementStart(131, \"input\", 64);\n      i0.ɵɵlistener(\"change\", function AddEditInsightComponent_Template_input_change_131_listener($event) {\n        return ctx.radiovalue($event, \"existingReason\");\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(132, \"label\", 62);\n      i0.ɵɵtext(133, \"its inappropriate\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(134, \"div\", 65);\n      i0.ɵɵelementStart(135, \"input\", 66);\n      i0.ɵɵlistener(\"click\", function AddEditInsightComponent_Template_input_click_135_listener() {\n        return ctx.showbox();\n      })(\"change\", function AddEditInsightComponent_Template_input_change_135_listener($event) {\n        return ctx.radiovalue($event, \"Other\");\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(136, \"label\", 62);\n      i0.ɵɵtext(137, \"Other\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(138, AddEditInsightComponent_div_138_Template, 4, 1, \"div\", 67);\n      i0.ɵɵelementStart(139, \"div\", 35);\n      i0.ɵɵelementStart(140, \"button\", 36);\n      i0.ɵɵlistener(\"click\", function AddEditInsightComponent_Template_button_click_140_listener() {\n        return ctx.checkUpdateStatus(ctx.selectedRecord, 3);\n      });\n      i0.ɵɵtext(141, \"Confirm \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(142, \"button\", 37);\n      i0.ɵɵtext(143, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"formGroup\", ctx.soundForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readOnly\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngForOf\", ctx.industryList);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"readOnly\", true);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"readOnly\", true);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"readOnly\", true)(\"value\", ctx.Tagtitle);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"readOnly\", true)(\"value\", ctx.ispublic);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.selectsoundurl);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"readOnly\", ctx.isReadonly || ctx.anonymousGenerated);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showApproveBtn && ctx.transcriptionGenerated && !ctx.anonymousGenerated);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showApproveBtn);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(43, _c0, ctx.showApproveBtn));\n      i0.ɵɵadvance(20);\n      i0.ɵɵproperty(\"src\", ctx.getBadgeImage(ctx.certificateTitle), i0.ɵɵsanitizeUrl);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate(ctx.certificateTitle);\n      i0.ɵɵadvance(7);\n      i0.ɵɵtextInterpolate(ctx.userName);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate1(\"\", ctx.roleName, \" in\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate(ctx.industryName);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate(ctx.dateEarned);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate(ctx.certificateDescription);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"percent\", ctx.BA_percentage)(\"maxPercent\", 100)(\"toFixed\", 0)(\"radius\", 30)(\"space\", -8)(\"outerStrokeGradient\", true)(\"outerStrokeWidth\", 8)(\"outerStrokeColor\", \"#4882c2\")(\"outerStrokeGradientStopColor\", \"#53a9ff\")(\"innerStrokeColor\", \"#e7e8ea\")(\"innerStrokeWidth\", 8)(\"showTitle\", true)(\"showUnits\", true)(\"showSubtitle\", false)(\"showBackground\", false)(\"showInnerStroke\", true)(\"clockwise\", false)(\"responsive\", false)(\"startFromZero\", false)(\"showZeroOuterStroke\", true);\n      i0.ɵɵadvance(33);\n      i0.ɵɵproperty(\"ngIf\", ctx.showhide);\n    }\n  },\n  directives: [i8.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.SelectControlValueAccessor, i1.RequiredValidator, i9.NgForOf, i9.NgIf, i9.NgClass, i10.CircleProgressComponent, i1.NgSelectOption, i1.ɵNgSelectMultipleOption],\n  styles: [\".modal-dialog[_ngcontent-%COMP%] {\\n  height: 50%;\\n  width: 50%;\\n  margin: auto;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  color: white;\\n  background-color: #007bff;\\n}\\n\\ntextarea[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: none !important;\\n  -webkit-appearance: none;\\n  outline: 0px !important;\\n  border: 1px solid black;\\n}\\n\\n.openmodal[_ngcontent-%COMP%] {\\n  margin-left: 40%;\\n  width: 25%;\\n  margin-top: 5%;\\n}\\n\\n.icon1[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\na[_ngcontent-%COMP%] {\\n  margin: auto;\\n}\\n\\n.form-check[_ngcontent-%COMP%] {\\n  padding-left: 5%;\\n}\\n\\n.mar-top[_ngcontent-%COMP%] {\\n  margin-top: 150px;\\n}\\n\\n.Gnrt-Transcription-btn[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  margin-left: 7px;\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.5);\\n}\\n\\n.invalid-feedback[_ngcontent-%COMP%] {\\n  display: none;\\n  width: 100%;\\n  margin-top: 0.25rem;\\n  font-size: 0.875em;\\n  color: #dc3545;\\n}\\n\\n.is-invalid[_ngcontent-%COMP%]    ~ .invalid-feedback[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.d-block[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.badge-certificate[_ngcontent-%COMP%] {\\n  background-color: #FFF3EB !important;\\n  color: #FF6700;\\n}\\n\\n.p-block[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: column;\\n}\\n\\n.brandlogo[_ngcontent-%COMP%] {\\n  margin-left: 5px;\\n  color: #FF6700;\\n  font-size: 18px;\\n}\\n\\n#certificate[_ngcontent-%COMP%] {\\n  width: 327px;\\n  font-family: -apple-system, BlinkMacSystemFont, sans-serif;\\n  background-color: #ffffff;\\n}\\n\\n.transcription[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.saveBtn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 18px;\\n  cursor: pointer;\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1024px) {\\n  audio[_ngcontent-%COMP%] {\\n    width: 215px !important;\\n  }\\n}\\n\\n@media screen and (min-width: 319px) and (max-width: 386px) {\\n  .cncl-btn[_ngcontent-%COMP%] {\\n    margin-top: 15px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/src/app/shared/sidebar/actions/Insights/add-edit-insight/add-edit-insight.component.ts"], "names": ["Validators", "html2canvas", "i0", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "i8", "i9", "i10", "AddEditInsightComponent_option_18_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ind_r5", "$implicit", "ɵɵproperty", "IN_id", "ɵɵadvance", "ɵɵtextInterpolate1", "IN_name", "AddEditInsightComponent_audio_41_Template", "ɵɵelement", "ctx_r1", "ɵɵnextContext", "ɵɵattribute", "selectsoundurl", "ɵɵsanitizeUrl", "AddEditInsightComponent_a_48_Template", "_r7", "ɵɵgetCurrentView", "ɵɵlistener", "AddEditInsightComponent_a_48_Template_a_click_0_listener", "ɵɵrestoreView", "ctx_r6", "changeAction", "ctx_r2", "ɵɵtextInterpolate", "is<PERSON><PERSON><PERSON>ly", "AddEditInsightComponent_button_50_Template", "AddEditInsightComponent_div_138_div_3_Template", "ctx_r9", "ɵɵtextInterpolate2", "wordCount", "wordLimit", "AddEditInsightComponent_div_138_Template", "_r11", "AddEditInsightComponent_div_138_Template_textarea_input_1_listener", "_r8", "ɵɵreference", "ctx_r10", "checkWordLimit", "value", "ɵɵtemplate", "ctx_r4", "_c0", "a0", "AddEditInsightComponent", "constructor", "formBuilder", "dataTransferService", "toastr", "domSani<PERSON>zer", "router", "formModule", "http", "ngxSpinnerService", "cdr", "renderer", "_a", "_b", "showhide", "soundList", "tempSoundList", "p", "Tagtitle", "baseurl", "state", "transcriptionGenerated", "showButton", "showRegenerateButton", "dataLoaded", "badges", "currentBadgeIndex", "showApproveBtn", "fileArray", "getCurrentNavigation", "extras", "userId", "AN_userId", "AN_id", "<PERSON><PERSON><PERSON><PERSON>", "navigate", "ngOnInit", "initForm", "getindustry", "soundForm", "get", "disable", "patchAllData", "group", "AN_title", "required", "AN_date", "AN_status", "AN_recordLink", "U_name", "U_updatedAt", "AN_updatedAt", "AN_createdBy", "AN_degreeId", "AN_description", "AN_dp", "RO_title", "AN_industryId", "AN_isPublished", "AN_keyIdeas", "AN_questionId", "AN_scheduleTime", "AN_updatedBy", "DE_title", "AN_url2", "AN_Reject_Reason", "AN_transcription", "sound_bites_links", "AL_answersId", "AL_title", "AL_url", "sound_bites_tags", "ANT_id", "ANT_tagId", "ANT_tagtitle", "f", "controls", "getSoundbiteByid", "isanonimise", "AN_isanonimise", "ispublic", "AN_ispublic", "AN_originalRecordLink", "anonymousGenerated", "originalRecordLink", "anonymousLink", "showModal", "tags", "map", "tag", "A_title", "join", "patchValue", "error", "modal", "document", "getElementById", "style", "display", "hideModal", "words", "trim", "split", "length", "warning", "getval", "item", "selectedvalue", "radiovalue", "event", "reasonType", "target", "console", "log", "showbox", "selected", "filtervalue", "filter", "obj", "onFileSelected", "fileName", "files", "onimageSelected", "imageName", "imagedp", "getUserName", "data", "getUserData", "subscribe", "res", "statusCode", "userData", "userDetails", "getIndustryData", "status", "industryList", "getalltags", "gettags", "TagList", "show", "updateTranscription", "hide", "success", "updatedTranscription", "getAllSoundBite", "getSoundBite", "next", "updateAllData", "record", "postData", "message", "getuploadlink", "uploadurl", "name", "updateAll", "getuploadDp", "onReset", "reset", "listPage", "insightAcceptedNotification", "UN_contentId", "UN_userId", "insightRejectedNotification", "UN_rejected_title", "updatedReason", "Promise", "resolve", "reject", "ID", "updateRejectReason", "checkUpdateStatus", "val", "AN_status1", "toString", "updateStatusAPI", "badgeData", "getBadgeData", "entries", "generateCertificates", "warn", "industryId", "roleId", "info", "Error", "updateStatus", "processBadge", "saveBadge", "then", "badge", "userName", "user", "industryName", "<PERSON><PERSON><PERSON>", "role<PERSON>itle", "certificateTitle", "BA_title", "BA_id", "certificateDescription", "BA_description", "badgeImage", "BA_image", "dateEarned", "formatDate", "Date", "BA_percentage", "detectChanges", "setTimeout", "captureImage", "date", "options", "year", "month", "day", "toLocaleDateString", "index", "code", "canvas", "imgData", "toDataURL", "RoleId", "file", "dataURLtoFile", "uploadImage", "catch", "dataURLtoBlob", "dataURL", "arr", "mimeMatch", "match", "mime", "bstr", "atob", "u8arr", "Uint8Array", "i", "charCodeAt", "Blob", "type", "filename", "blob", "File", "Newfile", "UB_badgeId", "UB_roleId", "UB_sectorId", "UB_userId", "UN_title", "UB_badge", "push", "downloadImage", "a", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "getBadgeImage", "title", "ɵfac", "AddEditInsightComponent_Factory", "t", "ɵɵdirectiveInject", "FormBuilder", "DataTransferService", "ToastrService", "Dom<PERSON><PERSON><PERSON>zer", "Router", "FormsModule", "HttpClient", "NgxSpinnerService", "ChangeDetectorRef", "Renderer2", "ɵcmp", "ɵɵdefineComponent", "selectors", "decls", "vars", "consts", "template", "AddEditInsightComponent_Template", "AddEditInsightComponent_Template_button_click_55_listener", "AddEditInsightComponent_Template_button_click_69_listener", "AddEditInsightComponent_Template_input_change_123_listener", "$event", "AddEditInsightComponent_Template_input_change_127_listener", "AddEditInsightComponent_Template_input_change_131_listener", "AddEditInsightComponent_Template_input_click_135_listener", "AddEditInsightComponent_Template_input_change_135_listener", "AddEditInsightComponent_Template_button_click_140_listener", "ɵɵpureFunction1", "directives", "SidebarComponent", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "DefaultValueAccessor", "NgControlStatus", "FormControlName", "SelectControlValueAccessor", "RequiredValidator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "Ng<PERSON><PERSON>", "CircleProgressComponent", "NgSelectOption", "ɵNgSelectMultipleOption", "styles"], "mappings": ";AAAA,SAASA,UAAT,QAA4B,gBAA5B;AACA,OAAOC,WAAP,MAAwB,aAAxB;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+CAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,YAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,2BAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,sBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,4BAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,GAAZ,MAAqB,oBAArB;;AACA,SAASC,0CAAT,CAAoDC,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMK,MAAM,GAAGJ,GAAG,CAACK,SAAnB;AACAlB,IAAAA,EAAE,CAACmB,UAAH,CAAc,OAAd,EAAuBF,MAAM,CAACG,KAA9B;AACApB,IAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,IAAAA,EAAE,CAACsB,kBAAH,CAAsB,GAAtB,EAA2BL,MAAM,CAACM,OAAlC,EAA2C,GAA3C;AACH;AAAE;;AACH,SAASC,yCAAT,CAAmDZ,EAAnD,EAAuDC,GAAvD,EAA4D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACtEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACAd,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb,EAAgB,QAAhB,EAA0B,EAA1B;AACAzB,IAAAA,EAAE,CAACyB,SAAH,CAAa,CAAb,EAAgB,QAAhB,EAA0B,EAA1B;AACAzB,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,oDAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMc,MAAM,GAAG1B,EAAE,CAAC2B,aAAH,EAAf;AACA3B,IAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,IAAAA,EAAE,CAAC4B,WAAH,CAAe,KAAf,EAAsBF,MAAM,CAACG,cAA7B,EAA6C7B,EAAE,CAAC8B,aAAhD;AACA9B,IAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,IAAAA,EAAE,CAAC4B,WAAH,CAAe,KAAf,EAAsBF,MAAM,CAACG,cAA7B,EAA6C7B,EAAE,CAAC8B,aAAhD;AACH;AAAE;;AACH,SAASC,qCAAT,CAA+CnB,EAA/C,EAAmDC,GAAnD,EAAwD;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClE,UAAMoB,GAAG,GAAGhC,EAAE,CAACiC,gBAAH,EAAZ;;AACAjC,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,GAArB,EAA0B,EAA1B;AACAd,IAAAA,EAAE,CAACkC,UAAH,CAAc,OAAd,EAAuB,SAASC,wDAAT,GAAoE;AAAEnC,MAAAA,EAAE,CAACoC,aAAH,CAAiBJ,GAAjB;AAAuB,YAAMK,MAAM,GAAGrC,EAAE,CAAC2B,aAAH,EAAf;AAAmC,aAAOU,MAAM,CAACC,YAAP,EAAP;AAA+B,KAAtL;AACAtC,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM2B,MAAM,GAAGvC,EAAE,CAAC2B,aAAH,EAAf;AACA3B,IAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,IAAAA,EAAE,CAACwC,iBAAH,CAAqBD,MAAM,CAACE,UAAP,GAAoB,MAApB,GAA6B,MAAlD;AACH;AAAE;;AACH,SAASC,0CAAT,CAAoD9B,EAApD,EAAwDC,GAAxD,EAA6D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvEZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,SAAb;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;AAAE;;AACH,SAAS2B,8CAAT,CAAwD/B,EAAxD,EAA4DC,GAA5D,EAAiE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3EZ,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACe,MAAH,CAAU,CAAV;AACAf,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAMgC,MAAM,GAAG5C,EAAE,CAAC2B,aAAH,CAAiB,CAAjB,CAAf;AACA3B,IAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,IAAAA,EAAE,CAAC6C,kBAAH,CAAsB,yBAAtB,EAAiDD,MAAM,CAACE,SAAxD,EAAmE,GAAnE,EAAwEF,MAAM,CAACG,SAA/E,EAA0F,UAA1F;AACH;AAAE;;AACH,SAASC,wCAAT,CAAkDpC,EAAlD,EAAsDC,GAAtD,EAA2D;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACrE,UAAMqC,IAAI,GAAGjD,EAAE,CAACiC,gBAAH,EAAb;;AACAjC,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACAd,IAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,UAArB,EAAiC,EAAjC,EAAqC,EAArC;AACAd,IAAAA,EAAE,CAACkC,UAAH,CAAc,OAAd,EAAuB,SAASgB,kEAAT,GAA8E;AAAElD,MAAAA,EAAE,CAACoC,aAAH,CAAiBa,IAAjB;;AAAwB,YAAME,GAAG,GAAGnD,EAAE,CAACoD,WAAH,CAAe,CAAf,CAAZ;;AAA+B,YAAMC,OAAO,GAAGrD,EAAE,CAAC2B,aAAH,EAAhB;AAAoC,aAAO0B,OAAO,CAACC,cAAR,CAAuBH,GAAG,CAACI,KAA3B,CAAP;AAA2C,KAA7O;AACAvD,IAAAA,EAAE,CAACgB,YAAH;AACAhB,IAAAA,EAAE,CAACwD,UAAH,CAAc,CAAd,EAAiBb,8CAAjB,EAAiE,CAAjE,EAAoE,CAApE,EAAuE,KAAvE,EAA8E,EAA9E;AACA3C,IAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,MAAIJ,EAAE,GAAG,CAAT,EAAY;AACV,UAAM6C,MAAM,GAAGzD,EAAE,CAAC2B,aAAH,EAAf;AACA3B,IAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,IAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsBsC,MAAM,CAACX,SAAP,GAAmBW,MAAM,CAACV,SAAhD;AACH;AAAE;;AACH,MAAMW,GAAG,GAAG,UAAUC,EAAV,EAAc;AAAE,SAAO;AAAE,gBAAYA;AAAd,GAAP;AAA4B,CAAxD;;AACA,OAAO,MAAMC,uBAAN,CAA8B;AACjCC,EAAAA,WAAW,CAACC,WAAD,EAAcC,mBAAd,EAAmCC,MAAnC,EAA2CC,YAA3C,EAAyDC,MAAzD,EAAiEC,UAAjE,EAA6EC,IAA7E,EAAmFC,iBAAnF,EAAsGC,GAAtG,EAA2GC,QAA3G,EAAqH;AAC5H,QAAIC,EAAJ,EAAQC,EAAR;;AACA,SAAKX,WAAL,GAAmBA,WAAnB;AACA,SAAKC,mBAAL,GAA2BA,mBAA3B;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,YAAL,GAAoBA,YAApB;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,GAAL,GAAWA,GAAX;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKG,QAAL,GAAgB,KAAhB;AACA,SAAKC,SAAL,GAAiB,EAAjB;AACA,SAAKC,aAAL,GAAqB,EAArB;AACA,SAAKC,CAAL,GAAS,CAAT;AACA,SAAKpC,UAAL,GAAkB,IAAlB;AACA,SAAKqC,QAAL,GAAgB,EAAhB;AACA,SAAKC,OAAL,GAAe,4CAAf;AACA,SAAKC,KAAL,GAAa,EAAb;AACA,SAAKC,sBAAL,GAA8B,KAA9B;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKC,oBAAL,GAA4B,KAA5B;AACA,SAAKrC,SAAL,GAAiB,CAAjB;AACA,SAAKC,SAAL,GAAiB,EAAjB;AACA,SAAKqC,UAAL,GAAkB,KAAlB;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,iBAAL,GAAyB,CAAzB;AACA,SAAKC,cAAL,GAAsB,IAAtB;AACA,SAAKC,SAAL,GAAiB,EAAjB;AACA,UAAMR,KAAK,GAAG,CAACP,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKN,MAAL,CAAYuB,oBAAZ,EAAN,MAA8C,IAA9C,IAAsDjB,EAAE,KAAK,KAAK,CAAlE,GAAsE,KAAK,CAA3E,GAA+EA,EAAE,CAACkB,MAAxF,MAAoG,IAApG,IAA4GjB,EAAE,KAAK,KAAK,CAAxH,GAA4H,KAAK,CAAjI,GAAqIA,EAAE,CAACO,KAAtJ;;AACA,QAAIA,KAAJ,EAAW;AACP,WAAKA,KAAL,GAAaA,KAAb;AACA,WAAKW,MAAL,GAAcX,KAAK,CAACY,SAApB;AACA,WAAKC,KAAL,GAAab,KAAK,CAACc,cAAN,CAAqBD,KAAlC;AACH,KAJD,MAKK;AACD,WAAK3B,MAAL,CAAY6B,QAAZ,CAAqB,CAAE,mBAAF,CAArB;AACH;AACJ;;AACDC,EAAAA,QAAQ,GAAG;AACP,QAAIxB,EAAJ,CADO,CAEP;AACA;;;AACA,SAAKyB,QAAL;AACA,SAAKC,WAAL;AACA,KAAC1B,EAAE,GAAG,KAAK2B,SAAL,CAAeC,GAAf,CAAmB,eAAnB,CAAN,MAA+C,IAA/C,IAAuD5B,EAAE,KAAK,KAAK,CAAnE,GAAuE,KAAK,CAA5E,GAAgFA,EAAE,CAAC6B,OAAH,EAAhF,CANO,CAOP;;AACA,SAAKC,YAAL,GARO,CASP;AACH;;AACDL,EAAAA,QAAQ,GAAG;AACP,SAAKE,SAAL,GAAiB,KAAKrC,WAAL,CAAiByC,KAAjB,CAAuB;AACpCC,MAAAA,QAAQ,EAAE,CAAC,EAAD,EAAK,CAAC1G,UAAU,CAAC2G,QAAZ,CAAL,CAD0B;AAEpCC,MAAAA,OAAO,EAAE,CAAC,EAAD,CAF2B;AAGpCC,MAAAA,SAAS,EAAE,CAAC,EAAD,CAHyB;AAIpCC,MAAAA,aAAa,EAAE,CAAC,EAAD,CAJqB;AAKpCC,MAAAA,MAAM,EAAE,CAAC,EAAD,CAL4B;AAMpCC,MAAAA,WAAW,EAAE,CAAC,EAAD,CANuB;AAOpCC,MAAAA,YAAY,EAAE,CAAC,EAAD,CAPsB;AAQpCC,MAAAA,YAAY,EAAE,CAAC,EAAD,CARsB;AASpCC,MAAAA,WAAW,EAAE,CAAC,EAAD,CATuB;AAUpCC,MAAAA,cAAc,EAAE,CAAC,EAAD,CAVoB;AAWpCC,MAAAA,KAAK,EAAE,CAAC,EAAD,CAX6B;AAYpCtB,MAAAA,KAAK,EAAE,CAAC,EAAD,CAZ6B;AAapCuB,MAAAA,QAAQ,EAAE,CAAC,EAAD,CAb0B;AAcpCC,MAAAA,aAAa,EAAE,CAAC,EAAD,CAdqB;AAepCC,MAAAA,cAAc,EAAE,CAAC,EAAD,CAfoB;AAgBpCC,MAAAA,WAAW,EAAE,CAAC,EAAD,CAhBuB;AAiBpCC,MAAAA,aAAa,EAAE,CAAC,EAAD,CAjBqB;AAkBpCC,MAAAA,eAAe,EAAE,CAAC,EAAD,CAlBmB;AAmBpCC,MAAAA,YAAY,EAAE,CAAC,EAAD,CAnBsB;AAoBpCC,MAAAA,QAAQ,EAAE,CAAC,EAAD,CApB0B;AAqBpCC,MAAAA,OAAO,EAAE,CAAC,EAAD,CArB2B;AAsBpCC,MAAAA,gBAAgB,EAAE,CAAC,EAAD,CAtBkB;AAuBpCC,MAAAA,gBAAgB,EAAE,CAAC,EAAD,CAvBkB;AAwBpClC,MAAAA,SAAS,EAAE,CAAC,EAAD,CAxByB;AAyBpC;AACAmC,MAAAA,iBAAiB,EAAE,CACf;AACIC,QAAAA,YAAY,EAAE,CAAC,EAAD,CADlB;AAEIC,QAAAA,QAAQ,EAAE,CAAC,EAAD,CAFd;AAGIC,QAAAA,MAAM,EAAE,CAAC,EAAD;AAHZ,OADe,CA1BiB;AAiCpCC,MAAAA,gBAAgB,EAAE,CACd;AACIC,QAAAA,MAAM,EAAE,CAAC,EAAD,CADZ;AAEIC,QAAAA,SAAS,EAAE,CAAC,EAAD,CAFf;AAGIC,QAAAA,YAAY,EAAE,CAAC,EAAD;AAHlB,OADc;AAjCkB,KAAvB,CAAjB;AAyCH;;AACI,MAADC,CAAC,GAAG;AACJ,WAAO,KAAKpC,SAAL,CAAeqC,QAAtB;AACH;;AACKlC,EAAAA,YAAY,GAAG;AAAA;;AAAA;AACjB,UAAI;AACA,cAAM,KAAI,CAACmC,gBAAL,CAAsB,KAAI,CAAC5C,KAA3B,CAAN;AACA,QAAA,KAAI,CAACM,SAAL,CAAe5C,KAAf,CAAqBsD,MAArB,GAA8B,KAAI,CAACf,cAAL,CAAoBe,MAAlD;AACA,QAAA,KAAI,CAACV,SAAL,CAAe5C,KAAf,CAAqBiD,QAArB,GAAgC,KAAI,CAACV,cAAL,CAAoBU,QAApD;AACA,QAAA,KAAI,CAACL,SAAL,CAAe5C,KAAf,CAAqB8D,aAArB,GAAqC,KAAI,CAACvB,cAAL,CAAoBuB,aAAzD;AACA,QAAA,KAAI,CAACqB,WAAL,GAAmB,KAAI,CAAC5C,cAAL,CAAoB6C,cAApB,IAAsC,IAAtC,GAA6C,KAA7C,GAAqD,IAAxE;AACA,QAAA,KAAI,CAACC,QAAL,GAAgB,KAAI,CAAC9C,cAAL,CAAoB+C,WAApB,IAAmC,IAAnC,GAA0C,KAA1C,GAAkD,IAAlE;AACA,QAAA,KAAI,CAAChH,cAAL,GAAsB,KAAI,CAACiE,cAAL,CAAoBc,aAA1C,CAPA,CAQA;;AACA,YAAI,KAAI,CAACd,cAAL,CAAoBgD,qBAAxB,EAA+C;AAC3C,UAAA,KAAI,CAACC,kBAAL,GAA0B,IAA1B;AACH,SAXD,CAYA;;;AACA,YAAI,KAAI,CAACjD,cAAL,CAAoBgD,qBAApB,IAA6C,KAAI,CAAChD,cAAL,CAAoBwB,cAApB,KAAuC,GAApF,IAA2F,KAAI,CAACxB,cAAL,CAAoBgC,gBAAnH,EAAqI;AACjI,UAAA,KAAI,CAACkB,kBAAL,GAA0B,KAAI,CAAClD,cAAL,CAAoBgD,qBAA9C;AACA,UAAA,KAAI,CAACG,aAAL,GAAqB,KAAI,CAACnD,cAAL,CAAoBc,aAAzC;AACA,UAAA,KAAI,CAACT,SAAL,CAAe5C,KAAf,CAAqBuE,gBAArB,GACI,KAAI,CAAChC,cAAL,CAAoBgC,gBADxB;AAEA,UAAA,KAAI,CAAC3C,oBAAL,GAA4B,IAA5B;AACA,UAAA,KAAI,CAACF,sBAAL,GAA8B,IAA9B;AACH;;AACD,YAAI,CAAC,KAAI,CAACa,cAAL,CAAoBgD,qBAArB,IACA,KAAI,CAAChD,cAAL,CAAoBwB,cAApB,KAAuC,GADvC,IAEA,KAAI,CAACxB,cAAL,CAAoBgC,gBAFxB,EAE0C;AACtC,UAAA,KAAI,CAACoB,SAAL;;AACA,UAAA,KAAI,CAAC/C,SAAL,CAAe5C,KAAf,CAAqBuE,gBAArB,GACI,KAAI,CAAChC,cAAL,CAAoBgC,gBADxB;AAEA,UAAA,KAAI,CAAC7C,sBAAL,GAA8B,IAA9B;AACA,UAAA,KAAI,CAACE,oBAAL,GAA4B,IAA5B;AACH,SA7BD,CA8BA;;;AACA,YAAI,KAAI,CAACW,cAAL,CAAoBa,SAApB,IAAiC,GAArC,EAA0C;AACtC,UAAA,KAAI,CAACR,SAAL,CAAe5C,KAAf,CAAqBuE,gBAArB,GAAwC,KAAI,CAAChC,cAAL,CAAoBgC,gBAA5D;AACA,UAAA,KAAI,CAAC5C,UAAL,GAAkB,KAAlB;AACA,UAAA,KAAI,CAACK,cAAL,GAAsB,KAAtB;AACA,UAAA,KAAI,CAACN,sBAAL,GAA8B,IAA9B;AACA,UAAA,KAAI,CAAC+D,kBAAL,GAA0B,KAAI,CAAClD,cAAL,CAAoBgD,qBAA9C;AACA,UAAA,KAAI,CAACG,aAAL,GAAqB,KAAI,CAACnD,cAAL,CAAoBc,aAAzC;AACH,SAtCD,CAuCA;;;AACA,YAAI,KAAI,CAACd,cAAL,CAAoBa,SAApB,IAAiC,GAAjC,IACA,KAAI,CAACb,cAAL,CAAoBgC,gBADxB,EAC0C;AACtC,UAAA,KAAI,CAACkB,kBAAL,GAA0B,KAAI,CAAClD,cAAL,CAAoBgD,qBAA9C;AACA,UAAA,KAAI,CAACG,aAAL,GAAqB,KAAI,CAACnD,cAAL,CAAoBc,aAAzC;AACA,UAAA,KAAI,CAACT,SAAL,CAAe5C,KAAf,CAAqBuE,gBAArB,GACI,KAAI,CAAChC,cAAL,CAAoBgC,gBADxB,CAHsC,CAKtC;;AACA,UAAA,KAAI,CAAC3C,oBAAL,GAA4B,IAA5B;AACA,UAAA,KAAI,CAACF,sBAAL,GAA8B,IAA9B;AACH;;AACD,QAAA,KAAI,CAACkB,SAAL,CAAe5C,KAAf,CAAqB6D,QAArB,GAAgC,KAAI,CAACtB,cAAL,CAAoBsB,QAApD;;AACA,YAAI,KAAI,CAACtB,cAAL,IAAuB,KAAI,CAACA,cAAL,CAAoBqD,IAA/C,EAAqD;AACjD,UAAA,KAAI,CAACrE,QAAL,GAAgB,KAAI,CAACgB,cAAL,CAAoBqD,IAApB,CACXC,GADW,CACNC,GAAD,IAASA,GAAG,CAACC,OADN,EAEXC,IAFW,CAEN,IAFM,CAAhB;AAGH,SAvDD,CAwDA;AACA;;;AACA,QAAA,KAAI,CAACpD,SAAL,CAAeqD,UAAf,CAA0B,KAAI,CAAC1D,cAA/B;AACH,OA3DD,CA4DA,OAAOtB,EAAP,EAAW;AACP,QAAA,KAAI,CAACR,MAAL,CAAYyF,KAAZ,CAAkB,wBAAlB;AACH;AA/DgB;AAgEpB;;AACDP,EAAAA,SAAS,GAAG;AACR,UAAMQ,KAAK,GAAGC,QAAQ,CAACC,cAAT,CAAwB,qBAAxB,CAAd;;AACA,QAAIF,KAAK,IAAI,IAAb,EAAmB;AACfA,MAAAA,KAAK,CAACG,KAAN,CAAYC,OAAZ,GAAsB,OAAtB;AACH;AACJ;;AACDC,EAAAA,SAAS,GAAG;AACR,UAAML,KAAK,GAAGC,QAAQ,CAACC,cAAT,CAAwB,qBAAxB,CAAd;;AACA,QAAIF,KAAK,IAAI,IAAb,EAAmB;AACfA,MAAAA,KAAK,CAACG,KAAN,CAAYC,OAAZ,GAAsB,MAAtB;AACH;AACJ,GA9KgC,CA+KjC;;;AACAxG,EAAAA,cAAc,CAACC,KAAD,EAAQ;AAClB,UAAMyG,KAAK,GAAGzG,KAAK,CAAC0G,IAAN,GAAaC,KAAb,CAAmB,KAAnB,CAAd;AACA,SAAKpH,SAAL,GAAiBkH,KAAK,CAACG,MAAvB;;AACA,QAAI,KAAKrH,SAAL,GAAiB,KAAKC,SAA1B,EAAqC;AACjC,WAAKiB,MAAL,CAAYoG,OAAZ,CAAqB,uCAAsC,KAAKrH,SAAU,SAA1E;AACA;AACH;;AACD,SAAKsH,MAAL,CAAY9G,KAAZ;AACH;;AACD8G,EAAAA,MAAM,CAACC,IAAD,EAAO;AACT;AACA,SAAKC,aAAL,GAAqBD,IAArB,CAFS,CAGT;AACH;;AACDE,EAAAA,UAAU,CAACC,KAAD,EAAQC,UAAR,EAAoB;AAC1B,SAAKH,aAAL,GAAqBE,KAAK,CAACE,MAAN,CAAapH,KAAlC;AACAqH,IAAAA,OAAO,CAACC,GAAR,CAAY,KAAKN,aAAjB,EAAgC,cAAhC;;AACA,QAAIG,UAAU,IAAI,gBAAlB,EAAoC;AAChC,WAAKhG,QAAL,GAAgB,KAAhB;AACA,WAAK5B,SAAL,GAAiB,CAAjB;AACH,KANyB,CAO1B;;AACH;;AACDgI,EAAAA,OAAO,GAAG;AACN;AACA,SAAKpG,QAAL,GAAgB,IAAhB;AACH;;AACDqG,EAAAA,QAAQ,CAACN,KAAD,EAAQ;AACZ,SAAKO,WAAL,GAAmBP,KAAK,CAACE,MAAN,CAAapH,KAAhC;AACAqH,IAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2B,KAAKG,WAAhC;AACA,SAAKrG,SAAL,GAAiB,CAAC,GAAG,KAAKC,aAAT,CAAjB;AACA,QAAI,KAAKoG,WAAL,IAAoB,KAAxB,EACI,KAAKrG,SAAL,GAAiB,KAAKA,SAAL,CAAesG,MAAf,CAAuBC,GAAD,IAASA,GAAG,CAACvE,SAAJ,IAAiB,KAAKqE,WAArD,CAAjB;AACP,GAjNgC,CAkNjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAG,EAAAA,cAAc,CAACV,KAAD,EAAQ;AAClB,SAAKW,QAAL,GAAgBX,KAAK,CAACE,MAAN,CAAaU,KAAb,CAAmB,CAAnB,CAAhB;AACAT,IAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAKO,QAA9B;AACA,SAAKvJ,cAAL,GAAsB,KAAKuJ,QAAL,CAAc7H,KAApC;AACH;;AACD+H,EAAAA,eAAe,CAACb,KAAD,EAAQ;AACnB,SAAKc,SAAL,GAAiBd,KAAK,CAACE,MAAN,CAAaU,KAAb,CAAmB,CAAnB,CAAjB;AACAT,IAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0B,KAAKU,SAA/B;AACA,SAAKC,OAAL,GAAe,KAAKD,SAAL,CAAehI,KAA9B;AACH;;AACDkI,EAAAA,WAAW,CAACC,IAAD,EAAO;AACd,SAAK3H,mBAAL,CAAyB4H,WAAzB,CAAqCD,IAArC,EAA2CE,SAA3C,CAAsDC,GAAD,IAAS;AAC1D,UAAIA,GAAG,CAACC,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,aAAKC,QAAL,GAAgBF,GAAG,CAACH,IAAJ,CAASM,WAAT,CAAqB,CAArB,EAAwBnF,MAAxC;AACA+D,QAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAKkB,QAA9B;AACH;AACJ,KALD,EAKItC,KAAD,IAAW;AACVmB,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBpB,KAArB;AACH,KAPD;AAQH;;AACDvD,EAAAA,WAAW,GAAG;AACV,SAAKnC,mBAAL,CAAyBkI,eAAzB,GAA2CL,SAA3C,CAAsDC,GAAD,IAAS;AAC1DjB,MAAAA,OAAO,CAACC,GAAR,CAAYgB,GAAZ,EAAiB,eAAjB;;AACA,UAAIA,GAAG,CAACK,MAAJ,IAAc,GAAlB,EAAuB;AACnB,aAAKC,YAAL,GAAoBN,GAAG,CAACH,IAAxB;AACAd,QAAAA,OAAO,CAACC,GAAR,CAAY,KAAKsB,YAAjB,EAA+B,cAA/B;AACH;AACJ,KAND,EAMI1C,KAAD,IAAW;AACVmB,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBpB,KAArB;AACH,KARD;AASH;;AACD2C,EAAAA,UAAU,GAAG;AACT,SAAKrI,mBAAL,CAAyBsI,OAAzB,GAAmCT,SAAnC,CAA8CC,GAAD,IAAS;AAClDjB,MAAAA,OAAO,CAACC,GAAR,CAAYgB,GAAZ;;AACA,UAAIA,GAAG,CAACK,MAAJ,IAAc,GAAlB,EAAuB;AACnB,aAAKI,OAAL,GAAeT,GAAG,CAACH,IAAnB;AACAd,QAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB,KAAKyB,OAA5B;AACH;AACJ,KAND,EAMI7C,KAAD,IAAW;AACVmB,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBpB,KAArB;AACH,KARD;AASH,GAnQgC,CAoQjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAnH,EAAAA,YAAY,GAAG;AACX,QAAIkC,EAAJ;;AACA,QAAI,KAAK/B,UAAL,IAAmB,KAAvB,EAA8B;AAC1B,YAAMiJ,IAAI,GAAG;AACT7F,QAAAA,KAAK,EAAE,KAAKC,cAAL,CAAoBD,KADlB;AAETiC,QAAAA,gBAAgB,EAAE,CAACtD,EAAE,GAAG,KAAK2B,SAAL,CAAeC,GAAf,CAAmB,kBAAnB,CAAN,MAAkD,IAAlD,IAA0D5B,EAAE,KAAK,KAAK,CAAtE,GAA0E,KAAK,CAA/E,GAAmFA,EAAE,CAACjB;AAF/F,OAAb;AAIAqH,MAAAA,OAAO,CAACC,GAAR,CAAY,MAAZ,EAAoBa,IAApB;AACA,WAAKrH,iBAAL,CAAuBkI,IAAvB,CAA4B,eAA5B;AACA,WAAKxI,mBAAL,CAAyByI,mBAAzB,CAA6Cd,IAA7C,EAAmDE,SAAnD,CAA8DC,GAAD,IAAS;AAClE,YAAIA,GAAG,CAACC,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,eAAKzH,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B;AACA,eAAKzI,MAAL,CAAY0I,OAAZ,CAAoB,oCAApB;AACA,gBAAMC,oBAAoB,GAAGd,GAAG,CAACH,IAAJ,CAAS5D,gBAAtC;AACA8C,UAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoC8B,oBAApC;AACA,eAAKxG,SAAL,CAAeqD,UAAf,CAA0B;AACtB1B,YAAAA,gBAAgB,EAAE6E;AADI,WAA1B;AAGA,eAAKlK,UAAL,GAAkB,CAAC,KAAKA,UAAxB;AACH,SATD,MAUK;AACD,eAAK4B,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B;AACA7B,UAAAA,OAAO,CAACC,GAAR,CAAY,8BAAZ;AACA,eAAK7G,MAAL,CAAYyF,KAAZ,CAAkB,gCAAlB;AACH;AACJ,OAhBD,EAgBIA,KAAD,IAAW;AACV,aAAKpF,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B;AACA,aAAKzI,MAAL,CAAYyF,KAAZ,CAAkB,gCAAlB;AACAmB,QAAAA,OAAO,CAACnB,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;AACH,OApBD;AAqBH,KA5BD,MA6BK;AACD,WAAKhH,UAAL,GAAkB,CAAC,KAAKA,UAAxB;AACH;AACJ,GA/SgC,CAgTjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAmK,EAAAA,eAAe,GAAG;AACd,SAAKvI,iBAAL,CAAuBkI,IAAvB,CAA4B,eAA5B;AACA,SAAKxI,mBAAL,CAAyB8I,YAAzB,GAAwCjB,SAAxC,CAAkD;AAC9CkB,MAAAA,IAAI,EAAGjB,GAAD,IAAS;AACXjB,QAAAA,OAAO,CAACC,GAAR,CAAYgB,GAAZ;;AACA,YAAIA,GAAG,CAACC,UAAJ,IAAkB,GAAtB,EAA2B;AACvB,eAAKnH,SAAL,GAAiBkH,GAAG,CAACH,IAArB;AACA,eAAK9G,aAAL,GAAqB,CAAC,GAAG,KAAKD,SAAT,CAArB;AACAiG,UAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqB,KAAKlG,SAA1B;AACA,eAAKN,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B;AACH;AACJ,OAT6C;AAU9ChD,MAAAA,KAAK,EAAGA,KAAD,IAAW;AACdmB,QAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BpB,KAA7B;AACA,aAAKpF,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B;AACH;AAb6C,KAAlD;AAeH;;AACDM,EAAAA,aAAa,CAACC,MAAD,EAAS;AAClB,SAAK7G,SAAL,CAAe5C,KAAf,CAAqBsC,KAArB,GAA6BmH,MAAM,CAACnH,KAApC;AACA,SAAKM,SAAL,CAAe5C,KAAf,CAAqB4E,gBAArB,CAAsC,CAAtC,EAAyCG,YAAzC,GAAwD,KAAKxD,QAA7D;AACA8F,IAAAA,OAAO,CAACC,GAAR,CAAY,KAAK1E,SAAL,CAAe5C,KAA3B,EAAkC,gBAAlC;AACA,QAAI0J,QAAQ,GAAG;AAAEpH,MAAAA,KAAK,EAAEmH,MAAM,CAACnH,KAAhB;AAAuByC,MAAAA,YAAY,EAAE,KAAKxD;AAA1C,KAAf;AACA,SAAKf,mBAAL,CAAyBgJ,aAAzB,CAAuCE,QAAvC,EAAiDrB,SAAjD,CAA4DC,GAAD,IAAS;AAChEjB,MAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BgB,GAA1B;;AACA,UAAI,CAACA,GAAG,CAACpC,KAAT,EAAgB;AACZ,aAAKzF,MAAL,CAAY0I,OAAZ,CAAoB,uBAApB,EAA6Cb,GAAG,CAACqB,OAAjD;AACA,aAAKN,eAAL;AACA,aAAK1I,MAAL,CAAY6B,QAAZ,CAAqB,CAAE,mBAAF,CAArB;AACH,OAJD,MAKK;AACD,aAAK/B,MAAL,CAAYyF,KAAZ,CAAkB,EAAlB,EAAsBoC,GAAG,CAACqB,OAA1B;AACH;AACJ,KAVD,EAUIzD,KAAD,IAAW;AACVmB,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBpB,KAArB;AACH,KAZD;AAaH;;AACD0D,EAAAA,aAAa,CAACH,MAAD,EAAS;AAClBpC,IAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwBmC,MAAxB,EADkB,CAElB;AACA;;AACA,SAAKjJ,mBAAL,CAAyBqJ,SAAzB,CAAmC,KAAKhC,QAAxC,EAAkDQ,SAAlD,CAA6DC,GAAD,IAAS;AACjEjB,MAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BgB,GAA1B;;AACA,UAAI,CAACA,GAAG,CAACpC,KAAT,EAAgB;AACZ,aAAKzF,MAAL,CAAY0I,OAAZ,CAAoB,EAApB,EAAwBb,GAAG,CAACqB,OAA5B;AACA,aAAK/G,SAAL,CAAeqD,UAAf,CAA0BwD,MAA1B;AACA,aAAKJ,eAAL;AACH,OAJD,MAKK;AACD,aAAK5I,MAAL,CAAYyF,KAAZ,CAAkB,EAAlB,EAAsBoC,GAAG,CAACqB,OAA1B;AACH;AACJ,KAVD;AAWA,SAAK/G,SAAL,CAAe5C,KAAf,CAAqBsC,KAArB,GAA6BmH,MAAM,CAACnH,KAApC;AACA,SAAKM,SAAL,CAAe5C,KAAf,CAAqBqE,OAArB,GAA+BoF,MAAM,CAACpG,aAAtC;AACA,SAAKT,SAAL,CAAe5C,KAAf,CAAqBqD,aAArB,GAAqC,KAAK7B,OAAL,GAAe,KAAKqG,QAAL,CAAciC,IAAlE;AACA,QAAIJ,QAAQ,GAAG;AACXpH,MAAAA,KAAK,EAAEmH,MAAM,CAACnH,KADH;AAEX+B,MAAAA,OAAO,EAAEoF,MAAM,CAACpG,aAFL;AAGXA,MAAAA,aAAa,EAAE,KAAK7B,OAAL,GAAe,KAAKqG,QAAL,CAAciC;AAHjC,KAAf;AAKA,SAAKtJ,mBAAL,CAAyBuJ,SAAzB,CAAmCL,QAAnC,EAA6CrB,SAA7C,CAAwDC,GAAD,IAAS;AAC5DjB,MAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BgB,GAA7B;;AACA,UAAI,CAACA,GAAG,CAACpC,KAAT,EAAgB;AACZ,aAAKzF,MAAL,CAAY0I,OAAZ,CAAoB,uBAApB,EAA6Cb,GAAG,CAACqB,OAAjD,EADY,CAEZ;AACH,OAHD,MAIK;AACD,aAAKlJ,MAAL,CAAYyF,KAAZ,CAAkB,EAAlB,EAAsBoC,GAAG,CAACqB,OAA1B;AACH;AACJ,KATD,EASIzD,KAAD,IAAW;AACVmB,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBpB,KAArB;AACH,KAXD;AAYH;;AACD8D,EAAAA,WAAW,CAACP,MAAD,EAAS;AAChBpC,IAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ,EAAwBmC,MAAxB,EADgB,CAEhB;AACA;;AACApC,IAAAA,OAAO,CAACC,GAAR,CAAY,KAAKU,SAAjB,EAA4B,MAA5B;AACA,SAAKxH,mBAAL,CAAyBqJ,SAAzB,CAAmC,KAAK7B,SAAxC,EAAmDK,SAAnD,CAA8DC,GAAD,IAAS;AAClEjB,MAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BgB,GAA1B;;AACA,UAAI,CAACA,GAAG,CAACpC,KAAT,EAAgB;AACZ,aAAKzF,MAAL,CAAY0I,OAAZ,CAAoB,0BAApB,EAAgDb,GAAG,CAACqB,OAApD;AACA,aAAK/G,SAAL,CAAeqD,UAAf,CAA0BwD,MAA1B,EAFY,CAGZ;AACH,OAJD,MAKK;AACD,aAAKhJ,MAAL,CAAYyF,KAAZ,CAAkB,EAAlB,EAAsBoC,GAAG,CAACqB,OAA1B;AACH;AACJ,KAVD;AAWA,SAAK/G,SAAL,CAAe5C,KAAf,CAAqBsC,KAArB,GAA6BmH,MAAM,CAACnH,KAApC;AACA,SAAK2F,OAAL,GAAe,KAAKD,SAAL,CAAe8B,IAA9B;AACA,SAAKlH,SAAL,CAAe5C,KAAf,CAAqB4D,KAArB,GAA6B,KAAKpC,OAAL,GAAe,KAAKyG,OAAjD;AACA,QAAIyB,QAAQ,GAAG;AAAEpH,MAAAA,KAAK,EAAEmH,MAAM,CAACnH,KAAhB;AAAuBsB,MAAAA,KAAK,EAAE,KAAKpC,OAAL,GAAe,KAAKyG;AAAlD,KAAf;AACA,SAAKzH,mBAAL,CAAyBuJ,SAAzB,CAAmCL,QAAnC,EAA6CrB,SAA7C,CAAwDC,GAAD,IAAS;AAC5DjB,MAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BgB,GAA1B;;AACA,UAAI,CAACA,GAAG,CAACpC,KAAT,EAAgB;AACZ,aAAKzF,MAAL,CAAY0I,OAAZ,CAAoB,uBAApB,EAA6Cb,GAAG,CAACqB,OAAjD;AACA,aAAKN,eAAL;AACH,OAHD,MAIK;AACD,aAAK5I,MAAL,CAAYyF,KAAZ,CAAkB,EAAlB,EAAsBoC,GAAG,CAACqB,OAA1B;AACH;AACJ,KATD,EASIzD,KAAD,IAAW;AACVmB,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBpB,KAArB;AACH,KAXD;AAYH;;AACD+D,EAAAA,OAAO,GAAG;AACN,SAAKrH,SAAL,CAAesH,KAAf;AACH;;AACDC,EAAAA,QAAQ,GAAG;AACP,SAAKxJ,MAAL,CAAY6B,QAAZ,CAAqB,CAAE,mBAAF,CAArB;AACH;;AACD4H,EAAAA,2BAA2B,GAAG;AAC1B,UAAMjC,IAAI,GAAG;AACTkC,MAAAA,YAAY,EAAE,GADL;AAETC,MAAAA,SAAS,EAAE,KAAK/H,cAAL,CAAoBF;AAFtB,KAAb;AAIAgF,IAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ,EAAgDa,IAAhD;AACA,SAAK3H,mBAAL,CAAyB4J,2BAAzB,CAAqDjC,IAArD,EAA2DE,SAA3D,CAAsEC,GAAD,IAAS;AAC1EjB,MAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACH,KAFD,EAEIpB,KAAD,IAAW;AACVmB,MAAAA,OAAO,CAACC,GAAR,CAAY,sCAAZ,EAAoDpB,KAApD;AACH,KAJD;AAKH;;AACDqE,EAAAA,2BAA2B,GAAG;AAC1B,UAAMpC,IAAI,GAAG;AACTkC,MAAAA,YAAY,EAAE,GADL;AAETC,MAAAA,SAAS,EAAE,KAAK/H,cAAL,CAAoBF,SAFtB;AAGTmI,MAAAA,iBAAiB,EAAE,KAAKxD;AAHf,KAAb;AAKAK,IAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ,EAAgDa,IAAhD;AACA,SAAK3H,mBAAL,CAAyB+J,2BAAzB,CAAqDpC,IAArD,EAA2DE,SAA3D,CAAsEC,GAAD,IAAS;AAC1EjB,MAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;AACH,KAFD,EAEIpB,KAAD,IAAW;AACVmB,MAAAA,OAAO,CAACC,GAAR,CAAY,sCAAZ,EAAoDpB,KAApD;AACH,KAJD;AAKH;;AACDuE,EAAAA,aAAa,GAAG;AACZ,WAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,WAAKhI,SAAL,CAAe5C,KAAf,CAAqBsE,gBAArB,GAAwC,KAAK0C,aAA7C;AACA,WAAKpE,SAAL,CAAe5C,KAAf,CAAqBsC,KAArB,GAA6B,KAAKuI,EAAlC;AACA,WAAKrK,mBAAL,CAAyBsK,kBAAzB,CAA4C,KAAKlI,SAAL,CAAe5C,KAA3D,EACKqI,SADL,CACgBC,GAAD,IAAS;AACpBjB,QAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8BgB,GAA9B,EADoB,CAEpB;;AACA,aAAK3H,MAAL,CAAY6B,QAAZ,CAAqB,CAAE,mBAAF,CAArB,EAHoB,CAIpB;;AACAmI,QAAAA,OAAO,CAACrC,GAAD,CAAP;AACH,OAPD,EAOIpC,KAAD,IAAW;AACV,aAAKzF,MAAL,CAAYyF,KAAZ,CAAkB,uBAAlB;AACAmB,QAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBpB,KAArB,EAFU,CAGV;;AACA0E,QAAAA,MAAM,CAAC1E,KAAD,CAAN;AACH,OAZD;AAaH,KAhBM,CAAP;AAiBH;;AACK6E,EAAAA,iBAAiB,CAACtB,MAAD,EAASuB,GAAT,EAAc;AAAA;;AAAA;AACjC;AACA,UAAI,MAAI,CAACzL,SAAL,GAAiB,MAAI,CAACC,SAA1B,EAAqC;AACjC,QAAA,MAAI,CAACiB,MAAL,CAAYyF,KAAZ,CAAkB,4CAAlB;;AACAmB,QAAAA,OAAO,CAACnB,KAAR,CAAc,4CAAd;AACA;AACH,OANgC,CAOjC;;;AACA,MAAA,MAAI,CAACpF,iBAAL,CAAuBkI,IAAvB,CAA4B,eAA5B,EARiC,CASjC;;;AACA,MAAA,MAAI,CAACiC,UAAL,GAAkBD,GAAG,CAACE,QAAJ,EAAlB;AACA,MAAA,MAAI,CAACtI,SAAL,CAAe5C,KAAf,CAAqBoD,SAArB,GAAiC,MAAI,CAAC6H,UAAtC;AACA,MAAA,MAAI,CAACrI,SAAL,CAAe5C,KAAf,CAAqB+D,cAArB,GAAsC,MAAI,CAACkH,UAA3C;AACA,MAAA,MAAI,CAACJ,EAAL,GAAUpB,MAAM,CAACnH,KAAjB;AACA,MAAA,MAAI,CAACM,SAAL,CAAe5C,KAAf,CAAqBsC,KAArB,GAA6B,MAAI,CAACuI,EAAlC;;AACA,UAAI;AACA,YAAIG,GAAG,KAAK,CAAZ,EAAe;AACX,gBAAM,MAAI,CAACG,eAAL,CAAqB,MAAI,CAACvI,SAAL,CAAe5C,KAApC,CAAN;AACAqH,UAAAA,OAAO,CAACC,GAAR,CAAY,6BAAZ;;AACA,UAAA,MAAI,CAAC8C,2BAAL;;AACA,gBAAMgB,SAAS,SAAS,MAAI,CAACC,YAAL,EAAxB,CAJW,CAIkC;;AAC7ChE,UAAAA,OAAO,CAACC,GAAR,CAAY,aAAZ,EAA2B8D,SAA3B;;AACA,cAAIA,SAAS,IAAIA,SAAS,CAACE,OAAvB,IAAkCF,SAAS,CAACE,OAAV,CAAkB1E,MAAlB,GAA2B,CAAjE,EAAoE;AAAE;AAClE,YAAA,MAAI,CAAC2E,oBAAL,CAA0BH,SAA1B;AACH,WAFD,MAGK;AACD/D,YAAAA,OAAO,CAACmE,IAAR,CAAa,yBAAb;AACH,WAXU,CAYX;;AACH,SAbD,MAcK;AACD,gBAAM,MAAI,CAACf,aAAL,EAAN;AACA,gBAAM,MAAI,CAACU,eAAL,CAAqB,MAAI,CAACvI,SAAL,CAAe5C,KAApC,CAAN;;AACA,UAAA,MAAI,CAACuK,2BAAL;;AACA,UAAA,MAAI,CAAClB,eAAL;;AACA,UAAA,MAAI,CAAC1I,MAAL,CAAY6B,QAAZ,CAAqB,CAAC,mBAAD,CAArB;;AACA,UAAA,MAAI,CAAC1B,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B,EANC,CAM6C;;AACjD;AACJ,OAvBD,CAwBA,OAAOhD,KAAP,EAAc;AACVmB,QAAAA,OAAO,CAACnB,KAAR,CAAc,wBAAd,EAAwCA,KAAxC;;AACA,QAAA,MAAI,CAACpF,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B,EAFU,CAEoC;;AACjD,OA1CgC,CA2CjC;AACA;AACA;;AA7CiC;AA8CpC;;AACDmC,EAAAA,YAAY,GAAG;AACX,WAAO,IAAIX,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,YAAMxI,MAAM,GAAG,KAAKG,cAAL,CAAoBF,SAAnC;AACA,YAAMoJ,UAAU,GAAG,KAAKlJ,cAAL,CAAoBuB,aAAvC;AACA,YAAM4H,MAAM,GAAG,KAAKnJ,cAAL,CAAoBmB,WAAnC;AACA2D,MAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ,EAA6ClF,MAA7C,EAAqDqJ,UAArD,EAAiEC,MAAjE;AACA,WAAKlL,mBAAL,CAAyB6K,YAAzB,CAAsCjJ,MAAtC,EAA8CqJ,UAA9C,EAA0DC,MAA1D,EAAkErD,SAAlE,CAA6E+C,SAAD,IAAe;AACvF/D,QAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoC8D,SAApC;AACAT,QAAAA,OAAO,CAACS,SAAD,CAAP;AACH,OAHD,EAGIlF,KAAD,IAAW;AACV,aAAKmD,eAAL;AACA,aAAK1I,MAAL,CAAY6B,QAAZ,CAAqB,CAAC,mBAAD,CAArB;AACA6E,QAAAA,OAAO,CAACsE,IAAR,CAAa,mBAAb;AACAf,QAAAA,MAAM,CAAC,IAAIgB,KAAJ,CAAU1F,KAAK,CAACyD,OAAN,IAAiB,wBAA3B,CAAD,CAAN;AACH,OARD;AASH,KAdM,CAAP;AAeH;;AACDwB,EAAAA,eAAe,CAAChD,IAAD,EAAO;AAClB,WAAO,IAAIuC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,WAAKpK,mBAAL,CAAyBqL,YAAzB,CAAsC1D,IAAtC,EAA4CE,SAA5C,CAAuDC,GAAD,IAAS;AAC3D,YAAIA,GAAG,CAACC,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,eAAK9H,MAAL,CAAY0I,OAAZ,CAAoB,6BAApB,EAAmDb,GAAG,CAACqB,OAAvD;AACAgB,UAAAA,OAAO;AACV,SAHD,MAIK;AACD,eAAK7J,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B,EADC,CAC6C;;AAC9C0B,UAAAA,MAAM,CAAC,IAAIgB,KAAJ,CAAUtD,GAAG,GAAGA,GAAG,CAACqB,OAAP,GAAiB,wBAA9B,CAAD,CAAN;AACH;AACJ,OATD,EASIzD,KAAD,IAAW;AACV,aAAKpF,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B,EADU,CACoC;;AAC9C0B,QAAAA,MAAM,CAAC,IAAIgB,KAAJ,CAAU1F,KAAK,CAACyD,OAAN,IAAiB,wBAA3B,CAAD,CAAN;AACA,aAAKlJ,MAAL,CAAYyF,KAAZ,CAAkB,OAAlB,EAA2BA,KAAK,CAACyD,OAAN,IAAiB,wBAA5C;AACH,OAbD;AAcH,KAfM,CAAP;AAgBH;;AACD4B,EAAAA,oBAAoB,CAACH,SAAD,EAAY;AAC5B,SAAKA,SAAL,GAAiBA,SAAjB;AACA,UAAMtJ,MAAM,GAAGsJ,SAAS,CAACE,OAAV,CAAkB,CAAlB,EAAqBxJ,MAApC;;AACA,QAAI,CAACA,MAAD,IAAWA,MAAM,CAAC8E,MAAP,KAAkB,CAAjC,EAAoC;AAChCS,MAAAA,OAAO,CAACmE,IAAR,CAAa,yCAAb;AACA;AACH;;AACD,SAAK1J,MAAL,GAAcA,MAAd;AACA,SAAKC,iBAAL,GAAyB,CAAzB,CAR4B,CAQA;;AAC5B,SAAK+J,YAAL;AACH;;AACDA,EAAAA,YAAY,GAAG;AACX,QAAI,KAAK/J,iBAAL,IAA0B,KAAKD,MAAL,CAAY8E,MAA1C,EAAkD;AAC9C;AACA,UAAI,KAAK9E,MAAL,CAAY8E,MAAZ,GAAqB,CAAzB,EAA4B;AACxB,aAAKmF,SAAL,GAAiBC,IAAjB,CAAsB,MAAM;AACxB,eAAKrL,MAAL,CAAY6B,QAAZ,CAAqB,CAAC,mBAAD,CAArB;AACA,eAAK1B,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B;AACA;AACH,SAJD;AAKH,OAND,MAOK;AACD,aAAKG,eAAL;AACA,aAAK1I,MAAL,CAAY6B,QAAZ,CAAqB,CAAC,mBAAD,CAArB;AACA,aAAK1B,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B;AACA;AACH;AACJ;;AACD,UAAM+C,KAAK,GAAG,KAAKnK,MAAL,CAAY,KAAKC,iBAAjB,CAAd;AACA,SAAKmK,QAAL,GAAgB,KAAKd,SAAL,CAAee,IAAf,CAAoBD,QAApC,CAlBW,CAkBmC;;AAC9C,SAAKE,YAAL,GAAoB,KAAKhB,SAAL,CAAeE,OAAf,CAAuB,CAAvB,EAA0Bc,YAA9C;AACA,SAAKC,QAAL,GAAgB,KAAKjB,SAAL,CAAeE,OAAf,CAAuB,CAAvB,EAA0BgB,SAA1C;AACA,SAAKC,gBAAL,GAAwBN,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACO,QAA5E;AACA,SAAKC,KAAL,GAAaR,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACQ,KAAjE;AACA,SAAKC,sBAAL,GAA8BT,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACU,cAAlF;AACA,SAAKC,UAAL,GAAkBX,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACY,QAAtE;AACA,SAAKC,UAAL,GAAkB,KAAKC,UAAL,CAAgB,IAAIC,IAAJ,EAAhB,CAAlB,CAzBW,CAyBoC;;AAC/C,SAAKC,aAAL,GAAqBhB,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACgB,aAAzE;AACA,SAAKlM,GAAL,CAASmM,aAAT;AACAC,IAAAA,UAAU,CAAC,MAAM;AACb,WAAKC,YAAL,CAAkB,KAAKrL,iBAAvB,EAA0C,KAAKmK,QAA/C,EAAyD,KAAKK,gBAA9D;AACH,KAFS,EAEP,IAFO,CAAV,CA5BW,CA8BD;AACb;;AACDQ,EAAAA,UAAU,CAACM,IAAD,EAAO;AACb,UAAMC,OAAO,GAAG;AACZC,MAAAA,IAAI,EAAE,SADM;AAEZC,MAAAA,KAAK,EAAE,MAFK;AAGZC,MAAAA,GAAG,EAAE;AAHO,KAAhB;AAKA,WAAOJ,IAAI,CAACK,kBAAL,CAAwB,OAAxB,EAAiCJ,OAAjC,CAAP;AACH;;AACDF,EAAAA,YAAY,CAACO,KAAD,EAAQzB,QAAR,EAAkBK,gBAAlB,EAAoC;AAC5C,SAAKzL,iBAAL,CAAuBkI,IAAvB,CAA4B,eAA5B,EAD4C,CACE;;AAC9C,UAAM4E,IAAI,GAAGxH,QAAQ,CAACC,cAAT,CAAwB,aAAxB,CAAb;;AACA,QAAIuH,IAAJ,EAAU;AACNpR,MAAAA,WAAW,CAACoR,IAAD,CAAX,CACK5B,IADL,CACW6B,MAAD,IAAY;AAClB,cAAMC,OAAO,GAAGD,MAAM,CAACE,SAAP,CAAiB,WAAjB,CAAhB,CADkB,CAElB;;AACA,cAAM1L,SAAS,GAAG,KAAKE,cAAL,CAAoBF,SAAtC;AACA,cAAM2L,MAAM,GAAG,KAAKzL,cAAL,CAAoBmB,WAAnC;AACA,cAAMmE,QAAQ,GAAI,GAAEmG,MAAO,IAAG3L,SAAU,UAASkK,gBAAiB,EAAlE;AACA,cAAM0B,IAAI,GAAG,KAAKC,aAAL,CAAmBJ,OAAnB,EAA4BjG,QAA5B,CAAb,CANkB,CAOlB;;AACA,aAAKsG,WAAL,CAAiBF,IAAjB,EACI;AACA;AACA;AAHJ,SAIKjC,IAJL,CAIU,MAAM;AACZ,eAAKjK,iBAAL;AACA,eAAK+J,YAAL,GAFY,CAES;AACxB,SAPD,EAQKsC,KARL,CAQYlI,KAAD,IAAW;AAClBmB,UAAAA,OAAO,CAACnB,KAAR,CAAc,wBAAd,EAAwCA,KAAxC;AACA,eAAKzF,MAAL,CAAYyF,KAAZ,CAAkB,OAAlB,EAA2B,yBAA3B;AACH,SAXD;AAYH,OArBD,EAqBGkI,KArBH,CAqBUlI,KAAD,IAAW;AAChBmB,QAAAA,OAAO,CAACnB,KAAR,CAAc,wBAAd,EAAwCA,KAAxC;AACA,aAAKzF,MAAL,CAAYyF,KAAZ,CAAkB,OAAlB,EAA2B,0BAA3B;AACA,aAAKpF,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B,EAHgB,CAG8B;AACjD,OAzBD;AA0BH,KA3BD,MA4BK;AACD7B,MAAAA,OAAO,CAACnB,KAAR,CAAc,0CAAd;AACA,WAAKpF,iBAAL,CAAuBoI,IAAvB,CAA4B,eAA5B,EAFC,CAE6C;AACjD;AACJ;;AACDmF,EAAAA,aAAa,CAACC,OAAD,EAAU;AACnB;AACA,UAAMC,GAAG,GAAGD,OAAO,CAAC3H,KAAR,CAAc,GAAd,CAAZ,CAFmB,CAGnB;;AACA,UAAM6H,SAAS,GAAGD,GAAG,CAAC,CAAD,CAAH,CAAOE,KAAP,CAAa,SAAb,CAAlB;;AACA,QAAI,CAACD,SAAL,EAAgB;AACZ,YAAM,IAAI5C,KAAJ,CAAU,kBAAV,CAAN;AACH;;AACD,UAAM8C,IAAI,GAAGF,SAAS,CAAC,CAAD,CAAtB,CARmB,CASnB;;AACA,UAAMG,IAAI,GAAGC,IAAI,CAACL,GAAG,CAAC,CAAD,CAAJ,CAAjB;AACA,UAAMM,KAAK,GAAG,IAAIC,UAAJ,CAAeH,IAAI,CAAC/H,MAApB,CAAd,CAXmB,CAYnB;;AACA,SAAK,IAAImI,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,IAAI,CAAC/H,MAAzB,EAAiCmI,CAAC,EAAlC,EAAsC;AAClCF,MAAAA,KAAK,CAACE,CAAD,CAAL,GAAWJ,IAAI,CAACK,UAAL,CAAgBD,CAAhB,CAAX;AACH,KAfkB,CAgBnB;;;AACA,WAAO,IAAIE,IAAJ,CAAS,CAACJ,KAAD,CAAT,EAAkB;AAAEK,MAAAA,IAAI,EAAER;AAAR,KAAlB,CAAP;AACH;;AACDR,EAAAA,aAAa,CAACI,OAAD,EAAUa,QAAV,EAAoB;AAC7B,UAAMC,IAAI,GAAG,KAAKf,aAAL,CAAmBC,OAAnB,CAAb;AACA,WAAO,IAAIe,IAAJ,CAAS,CAACD,IAAD,CAAT,EAAiBD,QAAjB,EAA2B;AAAED,MAAAA,IAAI,EAAEE,IAAI,CAACF;AAAb,KAA3B,CAAP;AACH;;AACDf,EAAAA,WAAW,CAACF,IAAD,EAAO;AACd,WAAO,IAAIvD,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,WAAKpK,mBAAL,CAAyBqJ,SAAzB,CAAmCoE,IAAnC,EAAyC5F,SAAzC,CAAoDC,GAAD,IAAS;AACxDjB,QAAAA,OAAO,CAACC,GAAR,CAAY,2CAAZ;AACA,cAAMgI,OAAO,GAAG,KAAK9N,OAAL,GAAeyM,IAAI,CAACnE,IAApC;AACA,cAAM3B,IAAI,GAAG;AACToH,UAAAA,UAAU,EAAE,KAAK9C,KADR;AAET+C,UAAAA,SAAS,EAAE,KAAKjN,cAAL,CAAoBmB,WAFtB;AAGT+L,UAAAA,WAAW,EAAE,KAAKlN,cAAL,CAAoBuB,aAHxB;AAIT4L,UAAAA,SAAS,EAAE,KAAKnN,cAAL,CAAoBF,SAJtB;AAKTsN,UAAAA,QAAQ,EAAE,KAAKpD,gBALN;AAMTqD,UAAAA,QAAQ,EAAEN;AAND,SAAb;AAQA,aAAKrN,SAAL,CAAe4N,IAAf,CAAoB1H,IAApB;AACAwC,QAAAA,OAAO;AACV,OAbD,EAaIzE,KAAD,IAAW;AACVmB,QAAAA,OAAO,CAACnB,KAAR,CAAc,sCAAd,EAAsDA,KAAtD;AACA,aAAKzF,MAAL,CAAYyF,KAAZ,CAAkB,uBAAlB;AACA0E,QAAAA,MAAM,CAAC,IAAIgB,KAAJ,CAAU1F,KAAK,CAACyD,OAAN,IAAiB,wBAA3B,CAAD,CAAN;AACH,OAjBD;AAkBH,KAnBM,CAAP;AAoBH;;AACDoC,EAAAA,SAAS,GAAG;AACR,WAAO,IAAIrB,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpCvD,MAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,KAAKrF,SAAnC;AACA,WAAKzB,mBAAL,CAAyBuL,SAAzB,CAAmC,KAAK9J,SAAxC,EAAmDoG,SAAnD,CAA8DC,GAAD,IAAS;AAClEjB,QAAAA,OAAO,CAACC,GAAR,CAAY,yBAAZ;AACAqD,QAAAA,OAAO;AACV,OAHD,EAGIzE,KAAD,IAAW;AACV;AACAmB,QAAAA,OAAO,CAACnB,KAAR,CAAc,wBAAd,EAAwCA,KAAxC;AACA0E,QAAAA,MAAM,CAAC,IAAIgB,KAAJ,CAAU1F,KAAK,CAACyD,OAAN,IAAiB,wBAA3B,CAAD,CAAN;AACH,OAPD;AAQH,KAVM,CAAP;AAWH;;AACDmG,EAAAA,aAAa,CAAC7B,IAAD,EAAOpG,QAAP,EAAiB;AAC1B,UAAMkI,CAAC,GAAG3J,QAAQ,CAAC4J,aAAT,CAAuB,GAAvB,CAAV;AACAD,IAAAA,CAAC,CAACE,IAAF,GAAShC,IAAT;AACA8B,IAAAA,CAAC,CAACG,QAAF,GAAarI,QAAb;AACAzB,IAAAA,QAAQ,CAAC+J,IAAT,CAAcC,WAAd,CAA0BL,CAA1B;AACAA,IAAAA,CAAC,CAACM,KAAF;AACAjK,IAAAA,QAAQ,CAAC+J,IAAT,CAAcG,WAAd,CAA0BP,CAA1B;AACH;;AACDQ,EAAAA,aAAa,CAACC,KAAD,EAAQ;AACjB,YAAQA,KAAR;AACI,WAAK,mBAAL;AACI,eAAO,6BAAP;;AACJ,WAAK,oBAAL;AACI,eAAO,6BAAP;;AACJ,WAAK,0BAAL;AACI,eAAO,6BAAP;;AACJ;AACI,eAAO,IAAP;AARR;AAUH,GAt3BgC,CAu3BjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAtL,EAAAA,gBAAgB,CAAC5C,KAAD,EAAQ;AACpB,WAAO,IAAIoI,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,WAAKpK,mBAAL,CAAyB0E,gBAAzB,CAA0C5C,KAA1C,EAAiD+F,SAAjD,CAA4DC,GAAD,IAAS;AAChE,YAAIA,GAAG,CAACC,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,eAAKhG,cAAL,GAAsB+F,GAAG,CAACH,IAA1B;AACAwC,UAAAA,OAAO,CAACrC,GAAG,CAACH,IAAL,CAAP;AACH,SAHD,MAIK;AACDyC,UAAAA,MAAM,CAAE,2BAA0BtC,GAAG,CAACC,UAAW,EAA3C,CAAN;AACH;AACJ,OARD,EAQIrC,KAAD,IAAW;AACVmB,QAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBpB,KAArB;AACA0E,QAAAA,MAAM,CAAC1E,KAAD,CAAN;AACH,OAXD;AAYH,KAbM,CAAP;AAcH;;AA94BgC;;AAg5BrC7F,uBAAuB,CAACoQ,IAAxB,GAA+B,SAASC,+BAAT,CAAyCC,CAAzC,EAA4C;AAAE,SAAO,KAAKA,CAAC,IAAItQ,uBAAV,EAAmC5D,EAAE,CAACmU,iBAAH,CAAqBlU,EAAE,CAACmU,WAAxB,CAAnC,EAAyEpU,EAAE,CAACmU,iBAAH,CAAqBjU,EAAE,CAACmU,mBAAxB,CAAzE,EAAuHrU,EAAE,CAACmU,iBAAH,CAAqBhU,EAAE,CAACmU,aAAxB,CAAvH,EAA+JtU,EAAE,CAACmU,iBAAH,CAAqB/T,EAAE,CAACmU,YAAxB,CAA/J,EAAsMvU,EAAE,CAACmU,iBAAH,CAAqB9T,EAAE,CAACmU,MAAxB,CAAtM,EAAuOxU,EAAE,CAACmU,iBAAH,CAAqBlU,EAAE,CAACwU,WAAxB,CAAvO,EAA6QzU,EAAE,CAACmU,iBAAH,CAAqB7T,EAAE,CAACoU,UAAxB,CAA7Q,EAAkT1U,EAAE,CAACmU,iBAAH,CAAqB5T,EAAE,CAACoU,iBAAxB,CAAlT,EAA8V3U,EAAE,CAACmU,iBAAH,CAAqBnU,EAAE,CAAC4U,iBAAxB,CAA9V,EAA0Y5U,EAAE,CAACmU,iBAAH,CAAqBnU,EAAE,CAAC6U,SAAxB,CAA1Y,CAAP;AAAub,CAApgB;;AACAjR,uBAAuB,CAACkR,IAAxB,GAA+B,aAAc9U,EAAE,CAAC+U,iBAAH,CAAqB;AAAEtC,EAAAA,IAAI,EAAE7O,uBAAR;AAAiCoR,EAAAA,SAAS,EAAE,CAAC,CAAC,sBAAD,CAAD,CAA5C;AAAwEC,EAAAA,KAAK,EAAE,GAA/E;AAAoFC,EAAAA,IAAI,EAAE,EAA1F;AAA8FC,EAAAA,MAAM,EAAE,CAAC,CAAC,CAAD,EAAI,iBAAJ,CAAD,EAAyB,CAAC,CAAD,EAAI,KAAJ,CAAzB,EAAqC,CAAC,CAAD,EAAI,QAAJ,EAAc,aAAd,EAA6B,cAA7B,CAArC,EAAmF,CAAC,CAAD,EAAI,MAAJ,CAAnF,EAAgG,CAAC,CAAD,EAAI,aAAJ,EAAmB,YAAnB,EAAiC,YAAjC,EAA+C,aAA/C,EAA8D,YAA9D,EAA4E,aAA5E,EAA2F,MAA3F,CAAhG,EAAoM,CAAC,CAAD,EAAI,WAAJ,CAApM,EAAsN,CAAC,CAAD,EAAI,cAAJ,EAAoB,CAApB,EAAuB,WAAvB,CAAtN,EAA2P,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAA3P,EAA0R,CAAC,KAAD,EAAQ,EAAR,CAA1R,EAAuS,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,QAApC,EAA8C,CAA9C,EAAiD,cAAjD,EAAiE,iBAAjE,EAAoF,CAApF,EAAuF,UAAvF,CAAvS,EAA2Y,CAAC,UAAD,EAAa,EAAb,EAAiB,iBAAjB,EAAoC,eAApC,EAAqD,CAArD,EAAwD,cAAxD,EAAwE,iBAAxE,EAA2F,CAA3F,EAA8F,OAA9F,EAAuG,SAAvG,CAA3Y,EAA8f,CAAC,CAAD,EAAI,OAAJ,EAAa,CAAb,EAAgB,OAAhB,EAAyB,SAAzB,CAA9f,EAAmiB,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,UAApC,EAAgD,UAAhD,EAA4D,EAA5D,EAAgE,CAAhE,EAAmE,cAAnE,EAAmF,iBAAnF,EAAsG,CAAtG,EAAyG,UAAzG,CAAniB,EAAypB,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,UAApC,EAAgD,UAAhD,EAA4D,EAA5D,EAAgE,CAAhE,EAAmE,cAAnE,EAAmF,iBAAnF,EAAsG,CAAtG,EAAyG,UAAzG,CAAzpB,EAA+wB,CAAC,KAAD,EAAQ,cAAR,CAA/wB,EAAwyB,CAAC,MAAD,EAAS,MAAT,EAAiB,CAAjB,EAAoB,cAApB,EAAoC,iBAApC,EAAuD,CAAvD,EAA0D,UAA1D,EAAsE,OAAtE,CAAxyB,EAAw3B,CAAC,KAAD,EAAQ,eAAR,CAAx3B,EAAk5B,CAAC,CAAD,EAAI,UAAJ,CAAl5B,EAAm6B,CAAC,KAAD,EAAQ,eAAR,CAAn6B,EAA67B,CAAC,MAAD,EAAS,QAAT,EAAmB,iBAAnB,EAAsC,eAAtC,EAAuD,UAAvD,EAAmE,EAAnE,EAAuE,CAAvE,EAA0E,cAA1E,EAA0F,iBAA1F,EAA6G,CAA7G,EAAgH,UAAhH,CAA77B,EAA0jC,CAAC,UAAD,EAAa,EAAb,EAAiB,CAAjB,EAAoB,MAApB,CAA1jC,EAAulC,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAAvlC,EAAsnC,CAAC,IAAD,EAAO,6BAAP,EAAsC,MAAtC,EAA8C,GAA9C,EAAmD,iBAAnD,EAAsE,kBAAtE,EAA0F,CAA1F,EAA6F,cAA7F,EAA6G,eAA7G,EAA8H,CAA9H,EAAiI,WAAjI,EAA8I,MAA9I,EAAsJ,CAAtJ,EAAyJ,UAAzJ,CAAtnC,EAA4xC,CAAC,OAAD,EAAU,SAAV,EAAqB,CAArB,EAAwB,OAAxB,EAAiC,CAAjC,EAAoC,MAApC,CAA5xC,EAAy0C,CAAC,CAAD,EAAI,aAAJ,EAAmB,MAAnB,CAAz0C,EAAq2C,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,iBAA5B,EAA+C,aAA/C,EAA8D,OAA9D,EAAuE,aAAvE,EAAsF,mBAAtF,EAA2G,CAA3G,EAA8G,MAA9G,CAAr2C,EAA49C,CAAC,MAAD,EAAS,QAAT,EAAmB,aAAnB,EAAkC,OAAlC,EAA2C,aAA3C,EAA0D,UAA1D,EAAsE,CAAtE,EAAyE,KAAzE,EAAgF,YAAhF,CAA59C,EAA2jD,CAAC,CAAD,EAAI,KAAJ,EAAW,eAAX,EAA4B,CAA5B,EAA+B,SAA/B,EAA0C,OAA1C,CAA3jD,EAA+mD,CAAC,IAAD,EAAO,kBAAP,EAA2B,MAA3B,EAAmC,QAAnC,EAA6C,CAA7C,EAAgD,OAAhD,EAAyD,MAAzD,CAA/mD,EAAirD,CAAC,CAAD,EAAI,cAAJ,EAAoB,SAApB,CAAjrD,EAAitD,CAAC,CAAD,EAAI,eAAJ,CAAjtD,EAAuuD,CAAC,CAAD,EAAI,cAAJ,CAAvuD,EAA4vD,CAAC,CAAD,EAAI,aAAJ,EAAmB,OAAnB,CAA5vD,EAAyxD,CAAC,MAAD,EAAS,QAAT,EAAmB,cAAnB,EAAmC,OAAnC,EAA4C,CAA5C,EAA+C,OAA/C,EAAwD,CAAxD,EAA2D,OAA3D,EAAoE,OAApE,CAAzxD,EAAu2D,CAAC,CAAD,EAAI,YAAJ,EAAkB,aAAlB,CAAv2D,EAAy4D,CAAC,CAAD,EAAI,cAAJ,CAAz4D,EAA85D,CAAC,IAAD,EAAO,MAAP,EAAe,cAAf,EAA+B,OAA/B,EAAwC,CAAxC,EAA2C,KAA3C,EAAkD,aAAlD,EAAiE,CAAjE,EAAoE,OAApE,CAA95D,EAA4+D,CAAC,IAAD,EAAO,OAAP,EAAgB,cAAhB,EAAgC,OAAhC,EAAyC,CAAzC,EAA4C,KAA5C,EAAmD,aAAnD,CAA5+D,EAA+iE,CAAC,IAAD,EAAO,aAAP,EAAsB,CAAtB,EAAyB,MAAzB,EAAiC,MAAjC,EAAyC,CAAzC,EAA4C,UAA5C,EAAwD,UAAxD,EAAoE,KAApE,EAA2E,GAA3E,EAAgF,MAAhF,EAAwF,WAAxF,EAAqG,YAArG,EAAmH,SAAnH,CAA/iE,EAA8qE,CAAC,CAAD,EAAI,OAAJ,EAAa,aAAb,EAA4B,MAA5B,CAA9qE,EAAmtE,CAAC,KAAD,EAAQ,OAAR,EAAiB,OAAjB,EAA0B,IAA1B,EAAgC,QAAhC,EAA0C,IAA1C,EAAgD,CAAhD,EAAmD,KAAnD,CAAntE,EAA8wE,CAAC,CAAD,EAAI,OAAJ,EAAa,aAAb,CAA9wE,EAA2yE,CAAC,CAAD,EAAI,kBAAJ,CAA3yE,EAAo0E,CAAC,CAAD,EAAI,OAAJ,EAAa,YAAb,EAA2B,mBAA3B,EAAgD,MAAhD,EAAwD,kBAAxD,CAAp0E,EAAi5E,CAAC,CAAD,EAAI,MAAJ,EAAY,aAAZ,EAA2B,MAA3B,CAAj5E,EAAq7E,CAAC,CAAD,EAAI,SAAJ,EAAe,MAAf,EAAuB,kBAAvB,CAAr7E,EAAi+E,CAAC,CAAD,EAAI,SAAJ,CAAj+E,EAAi/E,CAAC,CAAD,EAAI,QAAJ,EAAc,aAAd,EAA6B,MAA7B,CAAj/E,EAAuhF,CAAC,CAAD,EAAI,MAAJ,EAAY,aAAZ,EAA2B,MAA3B,CAAvhF,EAA2jF,CAAC,CAAD,EAAI,KAAJ,EAAW,QAAX,EAAqB,wBAArB,EAA+C,MAA/C,CAA3jF,EAAmnF,CAAC,CAAD,EAAI,QAAJ,EAAc,wBAAd,CAAnnF,EAA4pF,CAAC,CAAD,EAAI,aAAJ,CAA5pF,EAAgrF,CAAC,CAAD,EAAI,oBAAJ,EAA0B,aAA1B,EAAyC,MAAzC,CAAhrF,EAAkuF,CAAC,CAAD,EAAI,SAAJ,EAAe,kBAAf,EAAmC,MAAnC,CAAluF,EAA8wF,CAAC,CAAD,EAAI,gBAAJ,EAAsB,QAAtB,EAAgC,wBAAhC,EAA0D,CAA1D,EAA6D,SAA7D,EAAwE,YAAxE,EAAsF,SAAtF,EAAiG,QAAjG,EAA2G,OAA3G,EAAoH,qBAApH,EAA2I,kBAA3I,EAA+J,kBAA/J,EAAmL,8BAAnL,EAAmN,kBAAnN,EAAuO,kBAAvO,EAA2P,WAA3P,EAAwQ,WAAxQ,EAAqR,cAArR,EAAqS,gBAArS,EAAuT,iBAAvT,EAA0U,WAA1U,EAAuV,YAAvV,EAAqW,eAArW,EAAsX,qBAAtX,CAA9wF,EAA4pG,CAAC,CAAD,EAAI,OAAJ,EAAa,YAAb,EAA2B,MAA3B,CAA5pG,EAAgsG,CAAC,KAAD,EAAQ,6CAAR,EAAuD,KAAvD,EAA8D,EAA9D,EAAkE,OAAlE,EAA2E,IAA3E,EAAiF,QAAjF,EAA2F,IAA3F,CAAhsG,EAAkyG,CAAC,CAAD,EAAI,WAAJ,CAAlyG,EAAozG,CAAC,IAAD,EAAO,SAAP,EAAkB,MAAlB,EAA0B,QAA1B,EAAoC,CAApC,EAAuC,OAAvC,EAAgD,MAAhD,CAApzG,EAA62G,CAAC,CAAD,EAAI,cAAJ,CAA72G,EAAk4G,CAAC,CAAD,EAAI,YAAJ,EAAkB,MAAlB,CAAl4G,EAA65G,CAAC,MAAD,EAAS,UAAT,EAAqB,MAArB,EAA6B,OAA7B,EAAsC,OAAtC,EAA+C,iBAA/C,EAAkE,CAAlE,EAAqE,QAArE,CAA75G,EAA6+G,CAAC,CAAD,EAAI,MAAJ,CAA7+G,EAA0/G,CAAC,MAAD,EAAS,UAAT,EAAqB,MAArB,EAA6B,OAA7B,EAAsC,OAAtC,EAA+C,gBAA/C,EAAiE,CAAjE,EAAoE,QAApE,CAA1/G,EAAykH,CAAC,MAAD,EAAS,UAAT,EAAqB,MAArB,EAA6B,OAA7B,EAAsC,OAAtC,EAA+C,mBAA/C,EAAoE,CAApE,EAAuE,QAAvE,CAAzkH,EAA2pH,CAAC,CAAD,EAAI,YAAJ,EAAkB,MAAlB,CAA3pH,EAAsrH,CAAC,MAAD,EAAS,UAAT,EAAqB,MAArB,EAA6B,OAA7B,EAAsC,OAAtC,EAA+C,OAA/C,EAAwD,CAAxD,EAA2D,OAA3D,EAAoE,QAApE,CAAtrH,EAAqwH,CAAC,OAAD,EAAU,MAAV,EAAkB,CAAlB,EAAqB,MAArB,CAArwH,EAAmyH,CAAC,CAAD,EAAI,OAAJ,CAAnyH,EAAizH,CAAC,UAAD,EAAa,EAAb,CAAjzH,EAAm0H,CAAC,MAAD,EAAS,WAAT,CAAn0H,EAA01H,CAAC,MAAD,EAAS,YAAT,CAA11H,EAAk3H,CAAC,CAAD,EAAI,SAAJ,EAAe,CAAf,EAAkB,OAAlB,CAAl3H,EAA84H,CAAC,MAAD,EAAS,QAAT,EAAmB,aAAnB,EAAkC,OAAlC,EAA2C,aAA3C,EAA0D,mBAA1D,EAA+E,CAA/E,EAAkF,KAAlF,EAAyF,aAAzF,CAA94H,EAAu/H,CAAC,CAAD,EAAI,MAAJ,CAAv/H,EAAogI,CAAC,MAAD,EAAS,UAAT,EAAqB,aAArB,EAAoC,cAApC,EAAoD,MAApD,EAA4D,GAA5D,EAAiE,CAAjE,EAAoE,cAApE,EAAoF,CAApF,EAAuF,OAAvF,CAApgI,EAAqmI,CAAC,KAAD,EAAQ,EAAR,CAArmI,EAAknI,CAAC,OAAD,EAAU,aAAV,EAAyB,CAAzB,EAA4B,MAA5B,CAAlnI,EAAupI,CAAC,CAAD,EAAI,aAAJ,CAAvpI,CAAtG;AAAkxIC,EAAAA,QAAQ,EAAE,SAASC,gCAAT,CAA0CzU,EAA1C,EAA8CC,GAA9C,EAAmD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AACv5IZ,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,aAArB;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,CAAV,EAAa,iBAAb;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,MAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,CAA1B;AACAzB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,cAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACwD,UAAH,CAAc,EAAd,EAAkB7C,0CAAlB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,QAApE,EAA8E,EAA9E;AACAX,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,MAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAzB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,UAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAzB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,MAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAzB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,YAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAzB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,cAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAzB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAzB,MAAAA,EAAE,CAACwD,UAAH,CAAc,EAAd,EAAkBhC,yCAAlB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,OAAnE,EAA4E,EAA5E;AACAxB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAzB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,gBAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAzB,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,UAAjB,EAA6B,EAA7B;AACAzB,MAAAA,EAAE,CAACwD,UAAH,CAAc,EAAd,EAAkBzB,qCAAlB,EAAyD,CAAzD,EAA4D,CAA5D,EAA+D,GAA/D,EAAoE,EAApE;AACA/B,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACwD,UAAH,CAAc,EAAd,EAAkBd,0CAAlB,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE,QAApE,EAA8E,EAA9E;AACA1C,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,SAAd;AACAf,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,QAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,SAAd;AACAf,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACkC,UAAH,CAAc,OAAd,EAAuB,SAASoT,yDAAT,GAAqE;AAAE,eAAOzU,GAAG,CAAC6M,QAAJ,EAAP;AAAwB,OAAtH;AACA1N,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,QAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,IAAtB,EAA4B,EAA5B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,gBAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,QAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,QAAtB;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,iDAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACkC,UAAH,CAAc,OAAd,EAAuB,SAASqT,yDAAT,GAAqE;AAAE,eAAO1U,GAAG,CAACyN,iBAAJ,CAAsBzN,GAAG,CAACiF,cAA1B,EAA0C,CAA1C,CAAP;AAAsD,OAApJ;AACA9F,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,UAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,QAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACyB,SAAH,CAAa,EAAb,EAAiB,KAAjB,EAAwB,EAAxB;AACAzB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,IAAtB,EAA4B,EAA5B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,qBAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,QAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,QAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV,EAAc,aAAd;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,MAAtB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,EAAV;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,GAAvB,EAA4B,EAA5B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,MAAvB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,eAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACyB,SAAH,CAAa,GAAb,EAAkB,iBAAlB,EAAqC,EAArC;AACAzB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACyB,SAAH,CAAa,GAAb,EAAkB,KAAlB,EAAyB,EAAzB;AACAzB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,MAAvB,EAA+B,EAA/B;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,WAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,IAAvB;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,sBAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,QAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,iCAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACyB,SAAH,CAAa,GAAb,EAAkB,IAAlB;AACAzB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACkC,UAAH,CAAc,QAAd,EAAwB,SAASsT,0DAAT,CAAoEC,MAApE,EAA4E;AAAE,eAAO5U,GAAG,CAAC2J,UAAJ,CAAeiL,MAAf,EAAuB,gBAAvB,CAAP;AAAkD,OAAxJ;AACAzV,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,iBAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACkC,UAAH,CAAc,QAAd,EAAwB,SAASwT,0DAAT,CAAoED,MAApE,EAA4E;AAAE,eAAO5U,GAAG,CAAC2J,UAAJ,CAAeiL,MAAf,EAAuB,gBAAvB,CAAP;AAAkD,OAAxJ;AACAzV,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,gBAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACkC,UAAH,CAAc,QAAd,EAAwB,SAASyT,0DAAT,CAAoEF,MAApE,EAA4E;AAAE,eAAO5U,GAAG,CAAC2J,UAAJ,CAAeiL,MAAf,EAAuB,gBAAvB,CAAP;AAAkD,OAAxJ;AACAzV,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,mBAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACkC,UAAH,CAAc,OAAd,EAAuB,SAAS0T,yDAAT,GAAqE;AAAE,eAAO/U,GAAG,CAACiK,OAAJ,EAAP;AAAuB,OAArH,EAAuH,QAAvH,EAAiI,SAAS+K,0DAAT,CAAoEJ,MAApE,EAA4E;AAAE,eAAO5U,GAAG,CAAC2J,UAAJ,CAAeiL,MAAf,EAAuB,OAAvB,CAAP;AAAyC,OAAxP;AACAzV,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,OAAvB,EAAgC,EAAhC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,OAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACwD,UAAH,CAAc,GAAd,EAAmBR,wCAAnB,EAA6D,CAA7D,EAAgE,CAAhE,EAAmE,KAAnE,EAA0E,EAA1E;AACAhD,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,KAAvB,EAA8B,EAA9B;AACAd,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAd,MAAAA,EAAE,CAACkC,UAAH,CAAc,OAAd,EAAuB,SAAS4T,0DAAT,GAAsE;AAAE,eAAOjV,GAAG,CAACyN,iBAAJ,CAAsBzN,GAAG,CAACiF,cAA1B,EAA0C,CAA1C,CAAP;AAAsD,OAArJ;AACA9F,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,UAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACc,cAAH,CAAkB,GAAlB,EAAuB,QAAvB,EAAiC,EAAjC;AACAd,MAAAA,EAAE,CAACe,MAAH,CAAU,GAAV,EAAe,QAAf;AACAf,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACAhB,MAAAA,EAAE,CAACgB,YAAH;AACH;;AAAC,QAAIJ,EAAE,GAAG,CAAT,EAAY;AACVZ,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,WAAd,EAA2BN,GAAG,CAACsF,SAA/B;AACAnG,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0B,IAA1B;AACAnB,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,SAAd,EAAyBN,GAAG,CAACsL,YAA7B;AACAnM,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0B,IAA1B;AACAnB,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0B,IAA1B;AACAnB,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0B,IAA1B,EAAgC,OAAhC,EAAyCN,GAAG,CAACiE,QAA7C;AACA9E,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0B,IAA1B,EAAgC,OAAhC,EAAyCN,GAAG,CAAC+H,QAA7C;AACA5I,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0BN,GAAG,CAAC4B,UAA9B;AACAzC,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsBN,GAAG,CAACgB,cAA1B;AACA7B,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,UAAd,EAA0BN,GAAG,CAAC4B,UAAJ,IAAkB5B,GAAG,CAACkI,kBAAhD;AACA/I,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsBN,GAAG,CAAC0E,cAAJ,IAAsB1E,GAAG,CAACoE,sBAA1B,IAAoD,CAACpE,GAAG,CAACkI,kBAA/E;AACA/I,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsBN,GAAG,CAAC0E,cAA1B;AACAvF,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,SAAd,EAAyBnB,EAAE,CAAC+V,eAAH,CAAmB,EAAnB,EAAuBrS,GAAvB,EAA4B7C,GAAG,CAAC0E,cAAhC,CAAzB;AACAvF,MAAAA,EAAE,CAACqB,SAAH,CAAa,EAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,KAAd,EAAqBN,GAAG,CAACiT,aAAJ,CAAkBjT,GAAG,CAACiP,gBAAtB,CAArB,EAA8D9P,EAAE,CAAC8B,aAAjE;AACA9B,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACwC,iBAAH,CAAqB3B,GAAG,CAACiP,gBAAzB;AACA9P,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACwC,iBAAH,CAAqB3B,GAAG,CAAC4O,QAAzB;AACAzP,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACsB,kBAAH,CAAsB,EAAtB,EAA0BT,GAAG,CAAC+O,QAA9B,EAAwC,KAAxC;AACA5P,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACwC,iBAAH,CAAqB3B,GAAG,CAAC8O,YAAzB;AACA3P,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACwC,iBAAH,CAAqB3B,GAAG,CAACwP,UAAzB;AACArQ,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACwC,iBAAH,CAAqB3B,GAAG,CAACoP,sBAAzB;AACAjQ,MAAAA,EAAE,CAACqB,SAAH,CAAa,CAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,SAAd,EAAyBN,GAAG,CAAC2P,aAA7B,EAA4C,YAA5C,EAA0D,GAA1D,EAA+D,SAA/D,EAA0E,CAA1E,EAA6E,QAA7E,EAAuF,EAAvF,EAA2F,OAA3F,EAAoG,CAAC,CAArG,EAAwG,qBAAxG,EAA+H,IAA/H,EAAqI,kBAArI,EAAyJ,CAAzJ,EAA4J,kBAA5J,EAAgL,SAAhL,EAA2L,8BAA3L,EAA2N,SAA3N,EAAsO,kBAAtO,EAA0P,SAA1P,EAAqQ,kBAArQ,EAAyR,CAAzR,EAA4R,WAA5R,EAAyS,IAAzS,EAA+S,WAA/S,EAA4T,IAA5T,EAAkU,cAAlU,EAAkV,KAAlV,EAAyV,gBAAzV,EAA2W,KAA3W,EAAkX,iBAAlX,EAAqY,IAArY,EAA2Y,WAA3Y,EAAwZ,KAAxZ,EAA+Z,YAA/Z,EAA6a,KAA7a,EAAob,eAApb,EAAqc,KAArc,EAA4c,qBAA5c,EAAme,IAAne;AACAxQ,MAAAA,EAAE,CAACqB,SAAH,CAAa,EAAb;AACArB,MAAAA,EAAE,CAACmB,UAAH,CAAc,MAAd,EAAsBN,GAAG,CAAC6D,QAA1B;AACH;AAAE,GA3R2D;AA2RzDsR,EAAAA,UAAU,EAAE,CAACxV,EAAE,CAACyV,gBAAJ,EAAsBhW,EAAE,CAACiW,aAAzB,EAAwCjW,EAAE,CAACkW,oBAA3C,EAAiElW,EAAE,CAACmW,kBAApE,EAAwFnW,EAAE,CAACoW,oBAA3F,EAAiHpW,EAAE,CAACqW,eAApH,EAAqIrW,EAAE,CAACsW,eAAxI,EAAyJtW,EAAE,CAACuW,0BAA5J,EAAwLvW,EAAE,CAACwW,iBAA3L,EAA8MhW,EAAE,CAACiW,OAAjN,EAA0NjW,EAAE,CAACkW,IAA7N,EAAmOlW,EAAE,CAACmW,OAAtO,EAA+OlW,GAAG,CAACmW,uBAAnP,EAA4Q5W,EAAE,CAAC6W,cAA/Q,EAA+R7W,EAAE,CAAC8W,uBAAlS,CA3R6C;AA2R+QC,EAAAA,MAAM,EAAE,CAAC,0nMAAD;AA3RvR,CAArB,CAA7C", "sourcesContent": ["import { Validators, } from '@angular/forms';\r\nimport html2canvas from 'html2canvas';\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"@angular/forms\";\r\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\r\nimport * as i3 from \"ngx-toastr\";\r\nimport * as i4 from \"@angular/platform-browser\";\r\nimport * as i5 from \"@angular/router\";\r\nimport * as i6 from \"@angular/common/http\";\r\nimport * as i7 from \"ngx-spinner\";\r\nimport * as i8 from \"../../../sidebar.component\";\r\nimport * as i9 from \"@angular/common\";\r\nimport * as i10 from \"ng-circle-progress\";\r\nfunction AddEditInsightComponent_option_18_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"option\", 68);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ind_r5 = ctx.$implicit;\r\n    i0.ɵɵproperty(\"value\", ind_r5.IN_id);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate1(\" \", ind_r5.IN_name, \" \");\r\n} }\r\nfunction AddEditInsightComponent_audio_41_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"audio\", 69);\r\n    i0.ɵɵelement(1, \"source\", 70);\r\n    i0.ɵɵelement(2, \"source\", 71);\r\n    i0.ɵɵtext(3, \" Your browser does not support the audio element. \");\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r1 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵattribute(\"src\", ctx_r1.selectsoundurl, i0.ɵɵsanitizeUrl);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵattribute(\"src\", ctx_r1.selectsoundurl, i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction AddEditInsightComponent_a_48_Template(rf, ctx) { if (rf & 1) {\r\n    const _r7 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"a\", 72);\r\n    i0.ɵɵlistener(\"click\", function AddEditInsightComponent_a_48_Template_a_click_0_listener() { i0.ɵɵrestoreView(_r7); const ctx_r6 = i0.ɵɵnextContext(); return ctx_r6.changeAction(); });\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r2 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate(ctx_r2.isReadonly ? \"Edit\" : \"Save\");\r\n} }\r\nfunction AddEditInsightComponent_button_50_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"button\", 73);\r\n    i0.ɵɵtext(1, \"Approve\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditInsightComponent_div_138_div_3_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 78);\r\n    i0.ɵɵtext(1);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r9 = i0.ɵɵnextContext(2);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵtextInterpolate2(\" Word limit exceeded! (\", ctx_r9.wordCount, \"/\", ctx_r9.wordLimit, \" words) \");\r\n} }\r\nfunction AddEditInsightComponent_div_138_Template(rf, ctx) { if (rf & 1) {\r\n    const _r11 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 74);\r\n    i0.ɵɵelementStart(1, \"textarea\", 75, 76);\r\n    i0.ɵɵlistener(\"input\", function AddEditInsightComponent_div_138_Template_textarea_input_1_listener() { i0.ɵɵrestoreView(_r11); const _r8 = i0.ɵɵreference(2); const ctx_r10 = i0.ɵɵnextContext(); return ctx_r10.checkWordLimit(_r8.value); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵtemplate(3, AddEditInsightComponent_div_138_div_3_Template, 2, 2, \"div\", 77);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r4 = i0.ɵɵnextContext();\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.wordCount > ctx_r4.wordLimit);\r\n} }\r\nconst _c0 = function (a0) { return { \"cncl-btn\": a0 }; };\r\nexport class AddEditInsightComponent {\r\n    constructor(formBuilder, dataTransferService, toastr, domSanitizer, router, formModule, http, ngxSpinnerService, cdr, renderer) {\r\n        var _a, _b;\r\n        this.formBuilder = formBuilder;\r\n        this.dataTransferService = dataTransferService;\r\n        this.toastr = toastr;\r\n        this.domSanitizer = domSanitizer;\r\n        this.router = router;\r\n        this.formModule = formModule;\r\n        this.http = http;\r\n        this.ngxSpinnerService = ngxSpinnerService;\r\n        this.cdr = cdr;\r\n        this.renderer = renderer;\r\n        this.showhide = false;\r\n        this.soundList = [];\r\n        this.tempSoundList = [];\r\n        this.p = 1;\r\n        this.isReadonly = true;\r\n        this.Tagtitle = '';\r\n        this.baseurl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\r\n        this.state = [];\r\n        this.transcriptionGenerated = false;\r\n        this.showButton = true;\r\n        this.showRegenerateButton = false;\r\n        this.wordCount = 0;\r\n        this.wordLimit = 50;\r\n        this.dataLoaded = false;\r\n        this.badges = [];\r\n        this.currentBadgeIndex = 0;\r\n        this.showApproveBtn = true;\r\n        this.fileArray = [];\r\n        const state = (_b = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras) === null || _b === void 0 ? void 0 : _b.state;\r\n        if (state) {\r\n            this.state = state;\r\n            this.userId = state.AN_userId;\r\n            this.AN_id = state.selectedRecord.AN_id;\r\n        }\r\n        else {\r\n            this.router.navigate([`/actions/insights`]);\r\n        }\r\n    }\r\n    ngOnInit() {\r\n        var _a;\r\n        // this.getAllSoundBite();\r\n        // this.getalltags()\r\n        this.initForm();\r\n        this.getindustry();\r\n        (_a = this.soundForm.get('AN_industryId')) === null || _a === void 0 ? void 0 : _a.disable();\r\n        // this.getalldegree();\r\n        this.patchAllData();\r\n        // this.getBadgeData();\r\n    }\r\n    initForm() {\r\n        this.soundForm = this.formBuilder.group({\r\n            AN_title: ['', [Validators.required]],\r\n            AN_date: [''],\r\n            AN_status: [''],\r\n            AN_recordLink: [''],\r\n            U_name: [''],\r\n            U_updatedAt: [''],\r\n            AN_updatedAt: [''],\r\n            AN_createdBy: [''],\r\n            AN_degreeId: [''],\r\n            AN_description: [''],\r\n            AN_dp: [''],\r\n            AN_id: [''],\r\n            RO_title: [''],\r\n            AN_industryId: [''],\r\n            AN_isPublished: [''],\r\n            AN_keyIdeas: [''],\r\n            AN_questionId: [''],\r\n            AN_scheduleTime: [''],\r\n            AN_updatedBy: [''],\r\n            DE_title: [''],\r\n            AN_url2: [''],\r\n            AN_Reject_Reason: [''],\r\n            AN_transcription: [''],\r\n            AN_userId: [''],\r\n            //  ANT_tagtitle: [''],\r\n            sound_bites_links: [\r\n                {\r\n                    AL_answersId: [''],\r\n                    AL_title: [''],\r\n                    AL_url: [''],\r\n                },\r\n            ],\r\n            sound_bites_tags: [\r\n                {\r\n                    ANT_id: [''],\r\n                    ANT_tagId: [''],\r\n                    ANT_tagtitle: [''],\r\n                },\r\n            ],\r\n        });\r\n    }\r\n    get f() {\r\n        return this.soundForm.controls;\r\n    }\r\n    async patchAllData() {\r\n        try {\r\n            await this.getSoundbiteByid(this.AN_id);\r\n            this.soundForm.value.U_name = this.selectedRecord.U_name;\r\n            this.soundForm.value.AN_title = this.selectedRecord.AN_title;\r\n            this.soundForm.value.AN_industryId = this.selectedRecord.AN_industryId;\r\n            this.isanonimise = this.selectedRecord.AN_isanonimise == true ? 'Yes' : 'No';\r\n            this.ispublic = this.selectedRecord.AN_ispublic == true ? 'Yes' : 'No';\r\n            this.selectsoundurl = this.selectedRecord.AN_recordLink;\r\n            // this.cdr.detectChanges();\r\n            if (this.selectedRecord.AN_originalRecordLink) {\r\n                this.anonymousGenerated = true;\r\n            }\r\n            //if status is pending and transcription is generated\r\n            if (this.selectedRecord.AN_originalRecordLink && this.selectedRecord.AN_isPublished === '1' && this.selectedRecord.AN_transcription) {\r\n                this.originalRecordLink = this.selectedRecord.AN_originalRecordLink;\r\n                this.anonymousLink = this.selectedRecord.AN_recordLink;\r\n                this.soundForm.value.AN_transcription =\r\n                    this.selectedRecord.AN_transcription;\r\n                this.showRegenerateButton = true;\r\n                this.transcriptionGenerated = true;\r\n            }\r\n            if (!this.selectedRecord.AN_originalRecordLink &&\r\n                this.selectedRecord.AN_isPublished === '1' &&\r\n                this.selectedRecord.AN_transcription) {\r\n                this.showModal();\r\n                this.soundForm.value.AN_transcription =\r\n                    this.selectedRecord.AN_transcription;\r\n                this.transcriptionGenerated = true;\r\n                this.showRegenerateButton = true;\r\n            }\r\n            //if status is approved\r\n            if (this.selectedRecord.AN_status == '2') {\r\n                this.soundForm.value.AN_transcription = this.selectedRecord.AN_transcription;\r\n                this.showButton = false;\r\n                this.showApproveBtn = false;\r\n                this.transcriptionGenerated = true;\r\n                this.originalRecordLink = this.selectedRecord.AN_originalRecordLink;\r\n                this.anonymousLink = this.selectedRecord.AN_recordLink;\r\n            }\r\n            //if status is reject\r\n            if (this.selectedRecord.AN_status == '3' &&\r\n                this.selectedRecord.AN_transcription) {\r\n                this.originalRecordLink = this.selectedRecord.AN_originalRecordLink;\r\n                this.anonymousLink = this.selectedRecord.AN_recordLink;\r\n                this.soundForm.value.AN_transcription =\r\n                    this.selectedRecord.AN_transcription;\r\n                // this.showModal();\r\n                this.showRegenerateButton = true;\r\n                this.transcriptionGenerated = true;\r\n            }\r\n            this.soundForm.value.RO_title = this.selectedRecord.RO_title;\r\n            if (this.selectedRecord && this.selectedRecord.tags) {\r\n                this.Tagtitle = this.selectedRecord.tags\r\n                    .map((tag) => tag.A_title)\r\n                    .join(', ');\r\n            }\r\n            // const regex = /\\[\\d{2}:\\d{2}:\\d{2}\\.\\d{3}\\]\\s?/g;\r\n            // this.soundForm.value.AN_transcription=this.selectedRecord.AN_transcription.replace(regex, '').replace(/\\\\n/g, '');\r\n            this.soundForm.patchValue(this.selectedRecord);\r\n        }\r\n        catch (_a) {\r\n            this.toastr.error('Error in patching data');\r\n        }\r\n    }\r\n    showModal() {\r\n        const modal = document.getElementById('Transcription-modal');\r\n        if (modal != null) {\r\n            modal.style.display = 'block';\r\n        }\r\n    }\r\n    hideModal() {\r\n        const modal = document.getElementById('Transcription-modal');\r\n        if (modal != null) {\r\n            modal.style.display = 'none';\r\n        }\r\n    }\r\n    // abc:any;\r\n    checkWordLimit(value) {\r\n        const words = value.trim().split(/\\s+/);\r\n        this.wordCount = words.length;\r\n        if (this.wordCount > this.wordLimit) {\r\n            this.toastr.warning(`You have exceeded the word limit of ${this.wordLimit} words.`);\r\n            return;\r\n        }\r\n        this.getval(value);\r\n    }\r\n    getval(item) {\r\n        // console.log(item,\"textbox value\");\r\n        this.selectedvalue = item;\r\n        // console.log(this.selectedvalue, 'textbox value');\r\n    }\r\n    radiovalue(event, reasonType) {\r\n        this.selectedvalue = event.target.value;\r\n        console.log(this.selectedvalue, 'option value');\r\n        if (reasonType == 'existingReason') {\r\n            this.showhide = false;\r\n            this.wordCount = 0;\r\n        }\r\n        // console.log(this.abc,\"textbox value\");\r\n    }\r\n    showbox() {\r\n        // this.showhide=true;\r\n        this.showhide = true;\r\n    }\r\n    selected(event) {\r\n        this.filtervalue = event.target.value;\r\n        console.log('filtervalue', this.filtervalue);\r\n        this.soundList = [...this.tempSoundList];\r\n        if (this.filtervalue != 'All')\r\n            this.soundList = this.soundList.filter((obj) => obj.AN_status == this.filtervalue);\r\n    }\r\n    // getvalue(value: any) {\r\n    //   if(value){\r\n    //   this.Tagtitle = value;\r\n    // }else{\r\n    //   this.Tagtitle = '';\r\n    // }\r\n    //   console.log(value, 'text tags');\r\n    // }\r\n    onFileSelected(event) {\r\n        this.fileName = event.target.files[0];\r\n        console.log('file data', this.fileName);\r\n        this.selectsoundurl = this.fileName.value;\r\n    }\r\n    onimageSelected(event) {\r\n        this.imageName = event.target.files[0];\r\n        console.log('image data', this.imageName);\r\n        this.imagedp = this.imageName.value;\r\n    }\r\n    getUserName(data) {\r\n        this.dataTransferService.getUserData(data).subscribe((res) => {\r\n            if (res.statusCode == 200) {\r\n                this.userData = res.data.userDetails[0].U_name;\r\n                console.log('user data', this.userData);\r\n            }\r\n        }, (error) => {\r\n            console.log(\"error\", error);\r\n        });\r\n    }\r\n    getindustry() {\r\n        this.dataTransferService.getIndustryData().subscribe((res) => {\r\n            console.log(res, 'industry list');\r\n            if (res.status == 200) {\r\n                this.industryList = res.data;\r\n                console.log(this.industryList, 'industrylist');\r\n            }\r\n        }, (error) => {\r\n            console.log(\"error\", error);\r\n        });\r\n    }\r\n    getalltags() {\r\n        this.dataTransferService.gettags().subscribe((res) => {\r\n            console.log(res);\r\n            if (res.status == 200) {\r\n                this.TagList = res.data;\r\n                console.log('Tags : ', this.TagList);\r\n            }\r\n        }, (error) => {\r\n            console.log(\"error\", error);\r\n        });\r\n    }\r\n    // getalldegree() {\r\n    //   this.dataTransferService.getdegree().subscribe((res: any) => {\r\n    //     if (res.statusCode == 200) {\r\n    //       this.DegreeList = res.data;\r\n    //     }\r\n    //   },(error:any)=>{\r\n    //     console.log(\"error\",error);\r\n    //   });\r\n    // }\r\n    changeAction() {\r\n        var _a;\r\n        if (this.isReadonly == false) {\r\n            const data = {\r\n                AN_id: this.selectedRecord.AN_id,\r\n                AN_transcription: (_a = this.soundForm.get('AN_transcription')) === null || _a === void 0 ? void 0 : _a.value,\r\n            };\r\n            console.log('Data', data);\r\n            this.ngxSpinnerService.show('globalSpinner');\r\n            this.dataTransferService.updateTranscription(data).subscribe((res) => {\r\n                if (res.statusCode === 200) {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    this.toastr.success('Transcription updated successfully');\r\n                    const updatedTranscription = res.data.AN_transcription;\r\n                    console.log('updatedTranscription', updatedTranscription);\r\n                    this.soundForm.patchValue({\r\n                        AN_transcription: updatedTranscription,\r\n                    });\r\n                    this.isReadonly = !this.isReadonly;\r\n                }\r\n                else {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    console.log(\"Coundn't get 200 status code\");\r\n                    this.toastr.error('Unable to update transcription');\r\n                }\r\n            }, (error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                this.toastr.error('Unable to update transcription');\r\n                console.error('error updating transcription', error);\r\n            });\r\n        }\r\n        else {\r\n            this.isReadonly = !this.isReadonly;\r\n        }\r\n    }\r\n    // hideAnonymousModal() {\r\n    //   const modal = document.getElementById('anonymous-warning-modal');\r\n    //   if (modal != null) {\r\n    //     modal.style.display = 'none';\r\n    //   }\r\n    // }\r\n    // showAnonymousModal() {\r\n    //   const modal = document.getElementById('anonymous-warning-modal');\r\n    //   if (modal != null) {\r\n    //     modal.style.display = 'block';\r\n    //   }\r\n    // }\r\n    // generateAnnonymousRecording() {\r\n    //   this.hideAnonymousModal();\r\n    //   const AN_id = this.selectedRecord.AN_id;\r\n    //   this.ngxSpinnerService.show('annonymousSpinner');\r\n    //   this.dataTransferService.generateAnnonymousInsight(AN_id).subscribe(\r\n    //     (res: any) => {\r\n    //       console.log('annanymous res', res);\r\n    //         this.anonymousLink = res.AN_recordLink;\r\n    //         this.cdr.detectChanges();\r\n    //         console.log('this.originalRecordLink',this.originalRecordLink);\r\n    //         this.originalRecordLink = res.AN_originalRecordLink;\r\n    //         this.ngxSpinnerService.hide('annonymousSpinner');\r\n    //     },\r\n    //     (error: any) => {\r\n    //       this.ngxSpinnerService.hide('annonymousSpinner');\r\n    //       this.toastr.error('Error generating Anonymous Insight.');\r\n    //     }\r\n    //   );\r\n    // }\r\n    // GenerateTranscriptById(fromModal?: any) {\r\n    //   this.AN_id = this.selectedRecord.AN_id;\r\n    //   console.log('Generate an id: ', this.AN_id);\r\n    //   if (fromModal) {\r\n    //     this.hideModal();\r\n    //     this.showRegenerateButton = true;\r\n    //   }\r\n    //   if (\r\n    //     this.selectedRecord.AN_transcription == '' ||\r\n    //     this.showRegenerateButton\r\n    //   ) {\r\n    //     this.ngxSpinnerService.show('generateTranscription');\r\n    //     this.dataTransferService.GenerateTranscriptById(this.AN_id).subscribe(\r\n    //       (res: any) => {\r\n    //         if (res.externalApiResponse.status === 'success') {\r\n    //           console.log('Generate button data', res);\r\n    //           const newTranscription = res.record.AN_transcription;\r\n    //           if (newTranscription !== '') {\r\n    //             // this.soundForm.patchValue({\r\n    //             //   AN_transcription: this.newTranscription,\r\n    //             // });\r\n    //             this.retryGetSoundBite(this.AN_id);\r\n    //             this.transcriptionGenerated = true;\r\n    //             this.showRegenerateButton = true;\r\n    //             //   if (this.wantToAnonymous && this.showRegenerateButton !== true) {\r\n    //             //     this.generateAnnonymousRecording(this.AN_id)\r\n    //             // }\r\n    //           } else {\r\n    //             this.GenerateTranscriptById();\r\n    //           }\r\n    //         } else {\r\n    //           this.handleApiError(res.message, this.AN_id);\r\n    //         }\r\n    //       },\r\n    //       (error: any) => {\r\n    //         // this.ngxSpinnerService.hide('generateTranscription');\r\n    //         this.handleError(error, this.AN_id);\r\n    //       }\r\n    //     );\r\n    //   } else {\r\n    //     this.soundForm.patchValue({\r\n    //       AN_transcription: this.selectedRecord.AN_transcription,\r\n    //     });\r\n    //     this.transcriptionGenerated = true;\r\n    //     this.showRegenerateButton = true; // if(this.wantToAnonymous){\r\n    //     //   this.generateAnnonymousRecording(this.AN_id)\r\n    //     // }\r\n    //   }\r\n    // }\r\n    // handleApiError(message: string, AN_id: any) {\r\n    //   if (message === 'Endpoint request timed out') {\r\n    //     this.retryGetSoundBite(AN_id);\r\n    //   } else {\r\n    //     this.ngxSpinnerService.hide('generateTranscription');\r\n    //     this.toastr.error('Unable to generate transcription please try again');\r\n    //     console.log('Error message: ', message);\r\n    //   }\r\n    // }\r\n    // handleError(error: any, AN_id: any) {\r\n    //   if (error.status === 504) {\r\n    //     console.log('504 Gateway Timeout error');\r\n    //     this.retryGetSoundBite(AN_id);\r\n    //   } else {\r\n    //     this.toastr.error('Something went wrong');\r\n    //     console.log('Error', error);\r\n    //     this.ngxSpinnerService.hide('generateTranscription');\r\n    //   }\r\n    // }\r\n    // retryGetSoundBite(AN_id: any) {\r\n    //   this.dataTransferService.getSoundBite().subscribe({\r\n    //     next: (res: any) => {\r\n    //       console.log(res);\r\n    //       if (res.statusCode === 200) {\r\n    //         let soundbite = res.data.find(\r\n    //           (soundbite: any) => soundbite.AN_id === AN_id\r\n    //         );\r\n    //         if (soundbite) {\r\n    //           console.log('Found soundbite: ', soundbite);\r\n    //           if (soundbite.AN_transcription == '') {\r\n    //             this.GenerateTranscriptById();\r\n    //           } else {\r\n    //             this.ngxSpinnerService.hide('generateTranscription');\r\n    //             // this.soundForm.value.AN_transcription = soundbite.AN_transcription;\r\n    //             this.soundForm.patchValue({\r\n    //               AN_transcription: soundbite.AN_transcription,\r\n    //             });\r\n    //             this.transcriptionGenerated = true;\r\n    //             this.showRegenerateButton = true;\r\n    //             // if (this.wantToAnonymous && this.showRegenerateButton !== true) {\r\n    //             //   this.generateAnnonymousRecording(this.AN_id)\r\n    //             // }\r\n    //           }\r\n    //         } else {\r\n    //           this.ngxSpinnerService.hide('generateTranscription');\r\n    //           this.toastr.error('Failed to generate insight, Try again');\r\n    //         }\r\n    //       } else {\r\n    //         this.ngxSpinnerService.hide('generateTranscription');\r\n    //         this.toastr.error('Failed to generate insight, Try again');\r\n    //       }\r\n    //     },\r\n    //     error: (error: any) => {\r\n    //       console.log('Error Message', error);\r\n    //       this.ngxSpinnerService.hide('generateTranscription');\r\n    //       this.toastr.error(\r\n    //         'Error retrieving insight',\r\n    //         error.message || 'Unknown error occurred'\r\n    //       );\r\n    //     },\r\n    //   });\r\n    // }\r\n    //     updateStatus(record: any, val: any) {\r\n    //       if (this.wordCount > this.wordLimit) {\r\n    //         console.error('Cannot update reason. Word limit exceeded.');\r\n    //     }\r\n    //     else{\r\n    //     //Approve status\r\n    //     console.log('approve reject data', record, val);\r\n    //     // this.soundForm.patchValue(record);\r\n    //     this.AN_status1 = val.toString();\r\n    //     this.soundForm.value.AN_status = this.AN_status1;\r\n    //     this.soundForm.value.AN_isPublished = this.AN_status1;\r\n    //     this.ID = record.AN_id;\r\n    //     this.soundForm.value.AN_id = this.ID;\r\n    //     // console.log(this.soundForm.value.AN_status, this.soundForm.value.AN_isPublished, this.soundForm.value, \"updated status\");\r\n    //     console.log('DATA-UPDATE_STATUS', this.soundForm.value);\r\n    //     // if(this.transcriptionGenerated){\r\n    //     this.dataTransferService.updateStatus(this.soundForm.value).subscribe(\r\n    //       (res: any) => {\r\n    //         if (res.statusCode == 200) {\r\n    //           this.toastr.success('Status saved successfully', res.message);\r\n    //           this.getAllSoundBite();\r\n    //           this.router.navigate(['/actions/insights']);\r\n    //         } else {\r\n    //           this.toastr.error(\r\n    //             'Error',\r\n    //             res ? res.message : 'Unknown error occurred'\r\n    //           );\r\n    //         }\r\n    //       },\r\n    //       (error: any) => {\r\n    //         this.toastr.error('Error', error.message || 'Unknown error occurred');\r\n    //       }\r\n    //     );\r\n    //     // }else{\r\n    //     //   this.toastr.error(\"Please generate transcription before approving.\");\r\n    //     // }\r\n    //   }\r\n    // }\r\n    getAllSoundBite() {\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        this.dataTransferService.getSoundBite().subscribe({\r\n            next: (res) => {\r\n                console.log(res);\r\n                if (res.statusCode == 200) {\r\n                    this.soundList = res.data;\r\n                    this.tempSoundList = [...this.soundList];\r\n                    console.log('Sound', this.soundList);\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                }\r\n            },\r\n            error: (error) => {\r\n                console.log('Error Message', error);\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n            },\r\n        });\r\n    }\r\n    updateAllData(record) {\r\n        this.soundForm.value.AN_id = record.AN_id;\r\n        this.soundForm.value.sound_bites_tags[0].ANT_tagtitle = this.Tagtitle;\r\n        console.log(this.soundForm.value, 'updated Status');\r\n        let postData = { AN_id: record.AN_id, ANT_tagtitle: this.Tagtitle };\r\n        this.dataTransferService.updateAllData(postData).subscribe((res) => {\r\n            console.log('Url Record', res);\r\n            if (!res.error) {\r\n                this.toastr.success('Save data succesfully', res.message);\r\n                this.getAllSoundBite();\r\n                this.router.navigate([`/actions/insights`]);\r\n            }\r\n            else {\r\n                this.toastr.error('', res.message);\r\n            }\r\n        }, (error) => {\r\n            console.log(\"error\", error);\r\n        });\r\n    }\r\n    getuploadlink(record) {\r\n        console.log('Record :', record);\r\n        // console.log(this.soundForm.value.AN_recordLink, \"response\");\r\n        // console.log(this.fileName, 'file');\r\n        this.dataTransferService.uploadurl(this.fileName).subscribe((res) => {\r\n            console.log('Url Record', res);\r\n            if (!res.error) {\r\n                this.toastr.success('', res.message);\r\n                this.soundForm.patchValue(record);\r\n                this.getAllSoundBite();\r\n            }\r\n            else {\r\n                this.toastr.error('', res.message);\r\n            }\r\n        });\r\n        this.soundForm.value.AN_id = record.AN_id;\r\n        this.soundForm.value.AN_url2 = record.AN_recordLink;\r\n        this.soundForm.value.AN_recordLink = this.baseurl + this.fileName.name;\r\n        let postData = {\r\n            AN_id: record.AN_id,\r\n            AN_url2: record.AN_recordLink,\r\n            AN_recordLink: this.baseurl + this.fileName.name,\r\n        };\r\n        this.dataTransferService.updateAll(postData).subscribe((res) => {\r\n            console.log('update Record', res);\r\n            if (!res.error) {\r\n                this.toastr.success('Save data succesfully', res.message);\r\n                // this.getAllSoundBite();\r\n            }\r\n            else {\r\n                this.toastr.error('', res.message);\r\n            }\r\n        }, (error) => {\r\n            console.log(\"error\", error);\r\n        });\r\n    }\r\n    getuploadDp(record) {\r\n        console.log('Record :', record);\r\n        // this.imagedp = this.imageName.name;\r\n        // this.soundForm.value.AN_dp = this.baseurl + this.imagedp;\r\n        console.log(this.imageName, 'file');\r\n        this.dataTransferService.uploadurl(this.imageName).subscribe((res) => {\r\n            console.log('Url Record', res);\r\n            if (!res.error) {\r\n                this.toastr.success('upload file successfully', res.message);\r\n                this.soundForm.patchValue(record);\r\n                // this.getAllSoundBite();\r\n            }\r\n            else {\r\n                this.toastr.error('', res.message);\r\n            }\r\n        });\r\n        this.soundForm.value.AN_id = record.AN_id;\r\n        this.imagedp = this.imageName.name;\r\n        this.soundForm.value.AN_dp = this.baseurl + this.imagedp;\r\n        let postData = { AN_id: record.AN_id, AN_dp: this.baseurl + this.imagedp };\r\n        this.dataTransferService.updateAll(postData).subscribe((res) => {\r\n            console.log('Url Record', res);\r\n            if (!res.error) {\r\n                this.toastr.success('Save data succesfully', res.message);\r\n                this.getAllSoundBite();\r\n            }\r\n            else {\r\n                this.toastr.error('', res.message);\r\n            }\r\n        }, (error) => {\r\n            console.log(\"error\", error);\r\n        });\r\n    }\r\n    onReset() {\r\n        this.soundForm.reset();\r\n    }\r\n    listPage() {\r\n        this.router.navigate([`/actions/insights`]);\r\n    }\r\n    insightAcceptedNotification() {\r\n        const data = {\r\n            UN_contentId: \"1\",\r\n            UN_userId: this.selectedRecord.AN_userId\r\n        };\r\n        console.log(\"insightAcceptedNotification data\", data);\r\n        this.dataTransferService.insightAcceptedNotification(data).subscribe((res) => {\r\n            console.log(\"insightAcceptedNotification called\");\r\n        }, (error) => {\r\n            console.log(\"Error in insightAcceptedNotification\", error);\r\n        });\r\n    }\r\n    insightRejectedNotification() {\r\n        const data = {\r\n            UN_contentId: \"1\",\r\n            UN_userId: this.selectedRecord.AN_userId,\r\n            UN_rejected_title: this.selectedvalue\r\n        };\r\n        console.log(\"insightRejectedNotification data\", data);\r\n        this.dataTransferService.insightRejectedNotification(data).subscribe((res) => {\r\n            console.log(\"insightRejectedNotification called\");\r\n        }, (error) => {\r\n            console.log(\"Error in insightRejectedNotification\", error);\r\n        });\r\n    }\r\n    updatedReason() {\r\n        return new Promise((resolve, reject) => {\r\n            this.soundForm.value.AN_Reject_Reason = this.selectedvalue;\r\n            this.soundForm.value.AN_id = this.ID;\r\n            this.dataTransferService.updateRejectReason(this.soundForm.value)\r\n                .subscribe((res) => {\r\n                console.log('updated reason', res);\r\n                // this.toastr.success('Reason updated successfully');\r\n                this.router.navigate([`/actions/insights`]);\r\n                // Resolve the promise with the response\r\n                resolve(res);\r\n            }, (error) => {\r\n                this.toastr.error(\"Error updating reason\");\r\n                console.log(\"error\", error);\r\n                // Reject the promise with the error\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n    async checkUpdateStatus(record, val) {\r\n        // if (this.transcriptionGenerated) {\r\n        if (this.wordCount > this.wordLimit) {\r\n            this.toastr.error('Cannot update reason. Word limit exceeded.');\r\n            console.error('Cannot update reason. Word limit exceeded.');\r\n            return;\r\n        }\r\n        // Show spinner before starting the update\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        // Prepare the data\r\n        this.AN_status1 = val.toString();\r\n        this.soundForm.value.AN_status = this.AN_status1;\r\n        this.soundForm.value.AN_isPublished = this.AN_status1;\r\n        this.ID = record.AN_id;\r\n        this.soundForm.value.AN_id = this.ID;\r\n        try {\r\n            if (val === 2) {\r\n                await this.updateStatusAPI(this.soundForm.value);\r\n                console.log('Status updated successfully');\r\n                this.insightAcceptedNotification();\r\n                const badgeData = await this.getBadgeData(); //getting badge data here\r\n                console.log('Badge Data:', badgeData);\r\n                if (badgeData && badgeData.entries && badgeData.entries.length > 0) { //generating certificate from that badge data\r\n                    this.generateCertificates(badgeData);\r\n                }\r\n                else {\r\n                    console.warn('No badge data available');\r\n                }\r\n                // this.ngxSpinnerService.hide('globalSpinner');\r\n            }\r\n            else {\r\n                await this.updatedReason();\r\n                await this.updateStatusAPI(this.soundForm.value);\r\n                this.insightRejectedNotification();\r\n                this.getAllSoundBite();\r\n                this.router.navigate(['/actions/insights']);\r\n                this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner after navigation\r\n            }\r\n        }\r\n        catch (error) {\r\n            console.error('Error in updateStatus:', error);\r\n            this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner on error\r\n        }\r\n        // } else {\r\n        //   this.toastr.info('Please generate the transcription first');\r\n        // }\r\n    }\r\n    getBadgeData() {\r\n        return new Promise((resolve, reject) => {\r\n            const userId = this.selectedRecord.AN_userId;\r\n            const industryId = this.selectedRecord.AN_industryId;\r\n            const roleId = this.selectedRecord.AN_degreeId;\r\n            console.log('Fetching badge data for user:', userId, industryId, roleId);\r\n            this.dataTransferService.getBadgeData(userId, industryId, roleId).subscribe((badgeData) => {\r\n                console.log('Received badge data:', badgeData);\r\n                resolve(badgeData);\r\n            }, (error) => {\r\n                this.getAllSoundBite();\r\n                this.router.navigate(['/actions/insights']);\r\n                console.info('No Badges Found !');\r\n                reject(new Error(error.message || 'Unknown error occurred'));\r\n            });\r\n        });\r\n    }\r\n    updateStatusAPI(data) {\r\n        return new Promise((resolve, reject) => {\r\n            this.dataTransferService.updateStatus(data).subscribe((res) => {\r\n                if (res.statusCode === 200) {\r\n                    this.toastr.success('Status updated successfully', res.message);\r\n                    resolve();\r\n                }\r\n                else {\r\n                    this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner on error\r\n                    reject(new Error(res ? res.message : 'Unknown error occurred'));\r\n                }\r\n            }, (error) => {\r\n                this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner on error\r\n                reject(new Error(error.message || 'Unknown error occurred'));\r\n                this.toastr.error('Error', error.message || 'Unknown error occurred');\r\n            });\r\n        });\r\n    }\r\n    generateCertificates(badgeData) {\r\n        this.badgeData = badgeData;\r\n        const badges = badgeData.entries[0].badges;\r\n        if (!badges || badges.length === 0) {\r\n            console.warn('No badges to generate certificates for.');\r\n            return;\r\n        }\r\n        this.badges = badges;\r\n        this.currentBadgeIndex = 0; // Initialize the current badge index\r\n        this.processBadge();\r\n    }\r\n    processBadge() {\r\n        if (this.currentBadgeIndex >= this.badges.length) {\r\n            // All badges processed, navigate to the new route\r\n            if (this.badges.length > 0) {\r\n                this.saveBadge().then(() => {\r\n                    this.router.navigate(['/actions/insights']);\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    return;\r\n                });\r\n            }\r\n            else {\r\n                this.getAllSoundBite();\r\n                this.router.navigate(['/actions/insights']);\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                return;\r\n            }\r\n        }\r\n        const badge = this.badges[this.currentBadgeIndex];\r\n        this.userName = this.badgeData.user.userName; // Use stored badgeData\r\n        this.industryName = this.badgeData.entries[0].industryName;\r\n        this.roleName = this.badgeData.entries[0].roleTitle;\r\n        this.certificateTitle = badge === null || badge === void 0 ? void 0 : badge.BA_title;\r\n        this.BA_id = badge === null || badge === void 0 ? void 0 : badge.BA_id;\r\n        this.certificateDescription = badge === null || badge === void 0 ? void 0 : badge.BA_description;\r\n        this.badgeImage = badge === null || badge === void 0 ? void 0 : badge.BA_image;\r\n        this.dateEarned = this.formatDate(new Date()); // Set today's date\r\n        this.BA_percentage = badge === null || badge === void 0 ? void 0 : badge.BA_percentage;\r\n        this.cdr.detectChanges();\r\n        setTimeout(() => {\r\n            this.captureImage(this.currentBadgeIndex, this.userName, this.certificateTitle);\r\n        }, 2000); // Delay to ensure data is rendered\r\n    }\r\n    formatDate(date) {\r\n        const options = {\r\n            year: 'numeric',\r\n            month: 'long',\r\n            day: 'numeric',\r\n        };\r\n        return date.toLocaleDateString('en-US', options);\r\n    }\r\n    captureImage(index, userName, certificateTitle) {\r\n        this.ngxSpinnerService.show('globalSpinner'); // Show spinner while capturing and uploading the image\r\n        const code = document.getElementById('certificate');\r\n        if (code) {\r\n            html2canvas(code)\r\n                .then((canvas) => {\r\n                const imgData = canvas.toDataURL('image/png');\r\n                // const timestamp = new Date().toISOString().replace(/[:.-]/g, '');\r\n                const AN_userId = this.selectedRecord.AN_userId;\r\n                const RoleId = this.selectedRecord.AN_degreeId;\r\n                const fileName = `${RoleId}_${AN_userId}_Badge_${certificateTitle}`;\r\n                const file = this.dataURLtoFile(imgData, fileName);\r\n                // Upload the file\r\n                this.uploadImage(file)\r\n                    // .then(() => {\r\n                    //   this.saveBadge(file);\r\n                    // })\r\n                    .then(() => {\r\n                    this.currentBadgeIndex++;\r\n                    this.processBadge(); // Process the next badge\r\n                })\r\n                    .catch((error) => {\r\n                    console.error('Error uploading badge:', error);\r\n                    this.toastr.error('Error', 'Failed to upload badge.');\r\n                });\r\n            }).catch((error) => {\r\n                console.error('Error capturing badge:', error);\r\n                this.toastr.error('Error', 'Failed to capture badge.');\r\n                this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner\r\n            });\r\n        }\r\n        else {\r\n            console.error(\"Element with ID 'certificate' not found.\");\r\n            this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner\r\n        }\r\n    }\r\n    dataURLtoBlob(dataURL) {\r\n        // Split the data URL into parts\r\n        const arr = dataURL.split(',');\r\n        // Extract the MIME type\r\n        const mimeMatch = arr[0].match(/:(.*?);/);\r\n        if (!mimeMatch) {\r\n            throw new Error('Invalid data URL');\r\n        }\r\n        const mime = mimeMatch[1];\r\n        // Decode the base64 string\r\n        const bstr = atob(arr[1]);\r\n        const u8arr = new Uint8Array(bstr.length);\r\n        // Populate the Uint8Array with the base64 string bytes\r\n        for (let i = 0; i < bstr.length; i++) {\r\n            u8arr[i] = bstr.charCodeAt(i);\r\n        }\r\n        // Return the Blob with the correct MIME type\r\n        return new Blob([u8arr], { type: mime });\r\n    }\r\n    dataURLtoFile(dataURL, filename) {\r\n        const blob = this.dataURLtoBlob(dataURL);\r\n        return new File([blob], filename, { type: blob.type });\r\n    }\r\n    uploadImage(file) {\r\n        return new Promise((resolve, reject) => {\r\n            this.dataTransferService.uploadurl(file).subscribe((res) => {\r\n                console.log('Badge uploaded successfully in S3 Bucket:');\r\n                const Newfile = this.baseurl + file.name;\r\n                const data = {\r\n                    UB_badgeId: this.BA_id,\r\n                    UB_roleId: this.selectedRecord.AN_degreeId,\r\n                    UB_sectorId: this.selectedRecord.AN_industryId,\r\n                    UB_userId: this.selectedRecord.AN_userId,\r\n                    UN_title: this.certificateTitle,\r\n                    UB_badge: Newfile\r\n                };\r\n                this.fileArray.push(data);\r\n                resolve();\r\n            }, (error) => {\r\n                console.error('Error uploading Badge  in S3 Bucket:', error);\r\n                this.toastr.error('Error uploading Badge');\r\n                reject(new Error(error.message || 'Unknown error occurred'));\r\n            });\r\n        });\r\n    }\r\n    saveBadge() {\r\n        return new Promise((resolve, reject) => {\r\n            console.log(\"this.fileArray\", this.fileArray);\r\n            this.dataTransferService.saveBadge(this.fileArray).subscribe((res) => {\r\n                console.log('Badge saved in database');\r\n                resolve();\r\n            }, (error) => {\r\n                // this.toastr.error('Error uploading the badge.');\r\n                console.error('Error uploading Badge:', error);\r\n                reject(new Error(error.message || 'Unknown error occurred'));\r\n            });\r\n        });\r\n    }\r\n    downloadImage(file, fileName) {\r\n        const a = document.createElement('a');\r\n        a.href = file;\r\n        a.download = fileName;\r\n        document.body.appendChild(a);\r\n        a.click();\r\n        document.body.removeChild(a);\r\n    }\r\n    getBadgeImage(title) {\r\n        switch (title) {\r\n            case 'Insight Initiator':\r\n                return './assets/images/badge_1.png';\r\n            case 'Career Contributor':\r\n                return './assets/images/badge_2.png';\r\n            case 'Social Mobility Champion':\r\n                return './assets/images/badge_3.png';\r\n            default:\r\n                return null;\r\n        }\r\n    }\r\n    // downloadImage(dataUrl: string, filename: string) {\r\n    //   const link = document.createElement('a');\r\n    //   link.href = dataUrl;\r\n    //   link.download = filename;\r\n    //   document.body.appendChild(link);\r\n    //   link.click();\r\n    //   document.body.removeChild(link);\r\n    // }\r\n    getSoundbiteByid(AN_id) {\r\n        return new Promise((resolve, reject) => {\r\n            this.dataTransferService.getSoundbiteByid(AN_id).subscribe((res) => {\r\n                if (res.statusCode === 200) {\r\n                    this.selectedRecord = res.data;\r\n                    resolve(res.data);\r\n                }\r\n                else {\r\n                    reject(`Unexpected status code: ${res.statusCode}`);\r\n                }\r\n            }, (error) => {\r\n                console.log(\"error\", error);\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n}\r\nAddEditInsightComponent.ɵfac = function AddEditInsightComponent_Factory(t) { return new (t || AddEditInsightComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.ToastrService), i0.ɵɵdirectiveInject(i4.DomSanitizer), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i1.FormsModule), i0.ɵɵdirectiveInject(i6.HttpClient), i0.ɵɵdirectiveInject(i7.NgxSpinnerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2)); };\r\nAddEditInsightComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: AddEditInsightComponent, selectors: [[\"app-add-edit-insight\"]], decls: 144, vars: 45, consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\"], [1, \"form-group\", \"col-lg-4\"], [\"for\", \"\"], [\"type\", \"text\", \"formControlName\", \"U_name\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"required\", \"\", \"formControlName\", \"AN_industryId\", 1, \"form-control\", \"form-control-sm\", 2, \"color\", \"#646363\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"formControlName\", \"RO_title\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"type\", \"text\", \"formControlName\", \"AN_title\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"ANT_tagtitle\"], [\"type\", \"text\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\", \"value\"], [\"for\", \"is_annonymous\"], [1, \"original\"], [\"for\", \"AN_recordLink\"], [\"type\", \"hidden\", \"formControlName\", \"AN_recordLink\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"controls\", \"\", 4, \"ngIf\"], [1, \"form-group\", \"col-lg-8\"], [\"id\", \"exampleFormControlTextarea1\", \"rows\", \"8\", \"formControlName\", \"AN_transcription\", 1, \"form-control\", \"transcription\", 2, \"font-size\", \"22px\", 3, \"readOnly\"], [\"class\", \"saveBtn\", 3, \"click\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"type\", \"submit\", \"class\", \"btn btn-success\", \"data-toggle\", \"modal\", \"data-target\", \"#confirmtoapprove\", 4, \"ngIf\"], [\"type\", \"submit\", \"data-toggle\", \"modal\", \"data-target\", \"#myModal\", 1, \"btn\", \"btn-danger\"], [1, \"btn\", \"btn-secondary\", 3, \"ngClass\", \"click\"], [\"id\", \"confirmtoapprove\", \"role\", \"dialog\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"mar-top\"], [1, \"modal-content\"], [1, \"modal-header\"], [2, \"margin-left\", \"150px\"], [\"type\", \"button\", \"data-dismiss\", \"modal\", 1, \"close\", 2, \"color\", \"white\"], [1, \"modal-body\", \"text-center\"], [1, \"modal-footer\"], [\"id\", \"left\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [\"id\", \"right\", \"data-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\"], [\"id\", \"certificate\", 1, \"py-3\", \"px-3\", 2, \"position\", \"absolute\", \"top\", \"0\", \"left\", \"-999999px\", \"visibility\", \"visible\"], [1, \"image\", \"text-center\", \"mb-2\"], [\"alt\", \"Badge\", \"width\", \"80\", \"height\", \"80\", 3, \"src\"], [1, \"title\", \"text-center\"], [1, \"font-weight-bold\"], [1, \"badge\", \"badge-pill\", \"badge-certificate\", \"mt-2\", \"font-weight-bold\"], [1, \"name\", \"text-center\", \"mt-3\"], [1, \"p-block\", \"mb-1\", \"font-weight-bold\"], [1, \"p-block\"], [1, \"career\", \"text-center\", \"mt-3\"], [1, \"date\", \"text-center\", \"mt-3\"], [1, \"msg\", \"d-flex\", \"justify-content-center\", \"mt-3\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-center\"], [1, \"progress-container\", \"text-center\", \"mt-3\"], [1, \"p-block\", \"font-weight-bold\", \"mb-2\"], [1, \"progress-meter\", \"d-flex\", \"justify-content-center\", 3, \"percent\", \"maxPercent\", \"toFixed\", \"radius\", \"space\", \"outerStrokeGradient\", \"outerStrokeWidth\", \"outerStrokeColor\", \"outerStrokeGradientStopColor\", \"innerStrokeColor\", \"innerStrokeWidth\", \"showTitle\", \"showUnits\", \"showSubtitle\", \"showBackground\", \"showInnerStroke\", \"clockwise\", \"responsive\", \"startFromZero\", \"showZeroOuterStroke\"], [1, \"image\", \"text-start\", \"mt-2\"], [\"src\", \"./assets/images/Orange icon_transperant.png\", \"alt\", \"\", \"width\", \"24\", \"height\", \"27\"], [1, \"brandlogo\"], [\"id\", \"myModal\", \"role\", \"dialog\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"form-check\", \"mb-3\"], [\"name\", \"feedback\", \"type\", \"radio\", \"value\", \"It's irrelevant\", 3, \"change\"], [1, \"ml-3\"], [\"name\", \"feedback\", \"type\", \"radio\", \"value\", \"It's not right\", 3, \"change\"], [\"name\", \"feedback\", \"type\", \"radio\", \"value\", \"its inappropriate\", 3, \"change\"], [1, \"form-check\", \"mb-4\"], [\"name\", \"feedback\", \"type\", \"radio\", \"value\", \"Other\", 3, \"click\", \"change\"], [\"class\", \"mx-4\", 4, \"ngIf\"], [3, \"value\"], [\"controls\", \"\"], [\"type\", \"audio/ogg\"], [\"type\", \"audio/mpeg\"], [1, \"saveBtn\", 3, \"click\"], [\"type\", \"submit\", \"data-toggle\", \"modal\", \"data-target\", \"#confirmtoapprove\", 1, \"btn\", \"btn-success\"], [1, \"mx-4\"], [\"type\", \"textarea\", \"placeholder\", \"Other reason\", \"rows\", \"4\", 1, \"form-control\", 3, \"input\"], [\"box\", \"\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"]], template: function AddEditInsightComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵelementStart(0, \"app-sidebar\");\r\n        i0.ɵɵelementStart(1, \"div\", 0);\r\n        i0.ɵɵelementStart(2, \"div\", 1);\r\n        i0.ɵɵelementStart(3, \"div\", 2);\r\n        i0.ɵɵelementStart(4, \"div\", 3);\r\n        i0.ɵɵelementStart(5, \"div\", 4);\r\n        i0.ɵɵtext(6, \"Insight Details\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(7, \"div\", 5);\r\n        i0.ɵɵelementStart(8, \"form\", 6);\r\n        i0.ɵɵelementStart(9, \"div\", 1);\r\n        i0.ɵɵelementStart(10, \"div\", 7);\r\n        i0.ɵɵelementStart(11, \"label\", 8);\r\n        i0.ɵɵtext(12, \"User\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(13, \"input\", 9);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(14, \"div\", 7);\r\n        i0.ɵɵelementStart(15, \"label\", 8);\r\n        i0.ɵɵtext(16, \"Sector Name \");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(17, \"select\", 10);\r\n        i0.ɵɵtemplate(18, AddEditInsightComponent_option_18_Template, 2, 2, \"option\", 11);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(19, \"div\", 7);\r\n        i0.ɵɵelementStart(20, \"label\", 8);\r\n        i0.ɵɵtext(21, \"Role\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(22, \"input\", 12);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(23, \"div\", 7);\r\n        i0.ɵɵelementStart(24, \"label\", 8);\r\n        i0.ɵɵtext(25, \"Question\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(26, \"input\", 13);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(27, \"div\", 7);\r\n        i0.ɵɵelementStart(28, \"label\", 14);\r\n        i0.ɵɵtext(29, \"Tags\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(30, \"input\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(31, \"div\", 7);\r\n        i0.ɵɵelementStart(32, \"label\", 16);\r\n        i0.ɵɵtext(33, \"Is Public?\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(34, \"input\", 15);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(35, \"div\", 7);\r\n        i0.ɵɵelementStart(36, \"div\", 17);\r\n        i0.ɵɵelementStart(37, \"label\", 18);\r\n        i0.ɵɵtext(38, \"Insight Link\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(39, \"br\");\r\n        i0.ɵɵelement(40, \"input\", 19);\r\n        i0.ɵɵtemplate(41, AddEditInsightComponent_audio_41_Template, 4, 2, \"audio\", 20);\r\n        i0.ɵɵelement(42, \"br\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(43, \"div\", 21);\r\n        i0.ɵɵelementStart(44, \"label\", 8);\r\n        i0.ɵɵtext(45, \"Transcription \");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(46, \"br\");\r\n        i0.ɵɵelement(47, \"textarea\", 22);\r\n        i0.ɵɵtemplate(48, AddEditInsightComponent_a_48_Template, 2, 1, \"a\", 23);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(49, \"div\", 24);\r\n        i0.ɵɵtemplate(50, AddEditInsightComponent_button_50_Template, 2, 0, \"button\", 25);\r\n        i0.ɵɵtext(51, \"\\u00A0 \");\r\n        i0.ɵɵelementStart(52, \"button\", 26);\r\n        i0.ɵɵtext(53, \"Reject\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtext(54, \"\\u00A0 \");\r\n        i0.ɵɵelementStart(55, \"button\", 27);\r\n        i0.ɵɵlistener(\"click\", function AddEditInsightComponent_Template_button_click_55_listener() { return ctx.listPage(); });\r\n        i0.ɵɵtext(56, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(57, \"div\", 28);\r\n        i0.ɵɵelementStart(58, \"div\", 29);\r\n        i0.ɵɵelementStart(59, \"div\", 30);\r\n        i0.ɵɵelementStart(60, \"div\", 31);\r\n        i0.ɵɵelementStart(61, \"h3\", 32);\r\n        i0.ɵɵtext(62, \"Confirmation !\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(63, \"button\", 33);\r\n        i0.ɵɵtext(64, \"\\u00D7\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(65, \"div\", 34);\r\n        i0.ɵɵelementStart(66, \"strong\");\r\n        i0.ɵɵtext(67, \"Are you sure you want to approve this Insight ?\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(68, \"div\", 35);\r\n        i0.ɵɵelementStart(69, \"button\", 36);\r\n        i0.ɵɵlistener(\"click\", function AddEditInsightComponent_Template_button_click_69_listener() { return ctx.checkUpdateStatus(ctx.selectedRecord, 2); });\r\n        i0.ɵɵtext(70, \"Confirm \");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(71, \"button\", 37);\r\n        i0.ɵɵtext(72, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(73, \"div\", 38);\r\n        i0.ɵɵelementStart(74, \"div\", 39);\r\n        i0.ɵɵelement(75, \"img\", 40);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(76, \"div\", 41);\r\n        i0.ɵɵelementStart(77, \"h3\", 42);\r\n        i0.ɵɵtext(78);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(79, \"span\", 43);\r\n        i0.ɵɵtext(80, \"gradvisor certified\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(81, \"div\", 44);\r\n        i0.ɵɵelementStart(82, \"span\", 45);\r\n        i0.ɵɵtext(83, \"Sharer\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(84, \"span\", 46);\r\n        i0.ɵɵtext(85);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(86, \"div\", 47);\r\n        i0.ɵɵelementStart(87, \"span\", 45);\r\n        i0.ɵɵtext(88, \"Career\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(89, \"span\", 46);\r\n        i0.ɵɵtext(90);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(91, \"span\", 46);\r\n        i0.ɵɵtext(92);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(93, \"div\", 48);\r\n        i0.ɵɵelementStart(94, \"span\", 45);\r\n        i0.ɵɵtext(95, \"Date Earned\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(96, \"span\", 46);\r\n        i0.ɵɵtext(97);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(98, \"div\", 49);\r\n        i0.ɵɵelementStart(99, \"div\", 50);\r\n        i0.ɵɵelementStart(100, \"p\", 51);\r\n        i0.ɵɵtext(101);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(102, \"div\", 52);\r\n        i0.ɵɵelementStart(103, \"span\", 53);\r\n        i0.ɵɵtext(104, \"Your Progress\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(105, \"circle-progress\", 54);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(106, \"div\", 55);\r\n        i0.ɵɵelement(107, \"img\", 56);\r\n        i0.ɵɵelementStart(108, \"span\", 57);\r\n        i0.ɵɵtext(109, \"gradvisor\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(110, \"div\", 58);\r\n        i0.ɵɵelementStart(111, \"div\", 59);\r\n        i0.ɵɵelementStart(112, \"div\", 30);\r\n        i0.ɵɵelementStart(113, \"div\", 31);\r\n        i0.ɵɵelementStart(114, \"h3\");\r\n        i0.ɵɵtext(115, \"Reason For Rejection\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(116, \"button\", 33);\r\n        i0.ɵɵtext(117, \"\\u00D7\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(118, \"div\", 34);\r\n        i0.ɵɵelementStart(119, \"strong\");\r\n        i0.ɵɵtext(120, \"Please Kindly Give Your Reason.\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(121, \"hr\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(122, \"div\", 60);\r\n        i0.ɵɵelementStart(123, \"input\", 61);\r\n        i0.ɵɵlistener(\"change\", function AddEditInsightComponent_Template_input_change_123_listener($event) { return ctx.radiovalue($event, \"existingReason\"); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(124, \"label\", 62);\r\n        i0.ɵɵtext(125, \"It's irrelevant\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(126, \"div\", 60);\r\n        i0.ɵɵelementStart(127, \"input\", 63);\r\n        i0.ɵɵlistener(\"change\", function AddEditInsightComponent_Template_input_change_127_listener($event) { return ctx.radiovalue($event, \"existingReason\"); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(128, \"label\", 62);\r\n        i0.ɵɵtext(129, \"It's not right\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(130, \"div\", 60);\r\n        i0.ɵɵelementStart(131, \"input\", 64);\r\n        i0.ɵɵlistener(\"change\", function AddEditInsightComponent_Template_input_change_131_listener($event) { return ctx.radiovalue($event, \"existingReason\"); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(132, \"label\", 62);\r\n        i0.ɵɵtext(133, \"its inappropriate\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(134, \"div\", 65);\r\n        i0.ɵɵelementStart(135, \"input\", 66);\r\n        i0.ɵɵlistener(\"click\", function AddEditInsightComponent_Template_input_click_135_listener() { return ctx.showbox(); })(\"change\", function AddEditInsightComponent_Template_input_change_135_listener($event) { return ctx.radiovalue($event, \"Other\"); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(136, \"label\", 62);\r\n        i0.ɵɵtext(137, \"Other\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(138, AddEditInsightComponent_div_138_Template, 4, 1, \"div\", 67);\r\n        i0.ɵɵelementStart(139, \"div\", 35);\r\n        i0.ɵɵelementStart(140, \"button\", 36);\r\n        i0.ɵɵlistener(\"click\", function AddEditInsightComponent_Template_button_click_140_listener() { return ctx.checkUpdateStatus(ctx.selectedRecord, 3); });\r\n        i0.ɵɵtext(141, \"Confirm \");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(142, \"button\", 37);\r\n        i0.ɵɵtext(143, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n    } if (rf & 2) {\r\n        i0.ɵɵadvance(8);\r\n        i0.ɵɵproperty(\"formGroup\", ctx.soundForm);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readOnly\", true);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.industryList);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"readOnly\", true);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"readOnly\", true);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"readOnly\", true)(\"value\", ctx.Tagtitle);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"readOnly\", true)(\"value\", ctx.ispublic);\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.selectsoundurl);\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵproperty(\"readOnly\", ctx.isReadonly || ctx.anonymousGenerated);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showApproveBtn && ctx.transcriptionGenerated && !ctx.anonymousGenerated);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showApproveBtn);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(43, _c0, ctx.showApproveBtn));\r\n        i0.ɵɵadvance(20);\r\n        i0.ɵɵproperty(\"src\", ctx.getBadgeImage(ctx.certificateTitle), i0.ɵɵsanitizeUrl);\r\n        i0.ɵɵadvance(3);\r\n        i0.ɵɵtextInterpolate(ctx.certificateTitle);\r\n        i0.ɵɵadvance(7);\r\n        i0.ɵɵtextInterpolate(ctx.userName);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵtextInterpolate1(\"\", ctx.roleName, \" in\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵtextInterpolate(ctx.industryName);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵtextInterpolate(ctx.dateEarned);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵtextInterpolate(ctx.certificateDescription);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"percent\", ctx.BA_percentage)(\"maxPercent\", 100)(\"toFixed\", 0)(\"radius\", 30)(\"space\", -8)(\"outerStrokeGradient\", true)(\"outerStrokeWidth\", 8)(\"outerStrokeColor\", \"#4882c2\")(\"outerStrokeGradientStopColor\", \"#53a9ff\")(\"innerStrokeColor\", \"#e7e8ea\")(\"innerStrokeWidth\", 8)(\"showTitle\", true)(\"showUnits\", true)(\"showSubtitle\", false)(\"showBackground\", false)(\"showInnerStroke\", true)(\"clockwise\", false)(\"responsive\", false)(\"startFromZero\", false)(\"showZeroOuterStroke\", true);\r\n        i0.ɵɵadvance(33);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.showhide);\r\n    } }, directives: [i8.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.SelectControlValueAccessor, i1.RequiredValidator, i9.NgForOf, i9.NgIf, i9.NgClass, i10.CircleProgressComponent, i1.NgSelectOption, i1.ɵNgSelectMultipleOption], styles: [\".modal-dialog[_ngcontent-%COMP%] {\\n  height: 50%;\\n  width: 50%;\\n  margin: auto;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  color: white;\\n  background-color: #007bff;\\n}\\n\\ntextarea[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: none !important;\\n  -webkit-appearance: none;\\n  outline: 0px !important;\\n  border: 1px solid black;\\n}\\n\\n.openmodal[_ngcontent-%COMP%] {\\n  margin-left: 40%;\\n  width: 25%;\\n  margin-top: 5%;\\n}\\n\\n.icon1[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\na[_ngcontent-%COMP%] {\\n  margin: auto;\\n}\\n\\n.form-check[_ngcontent-%COMP%] {\\n  padding-left: 5%;\\n}\\n\\n.mar-top[_ngcontent-%COMP%] {\\n  margin-top: 150px;\\n}\\n\\n.Gnrt-Transcription-btn[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  margin-left: 7px;\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.5);\\n}\\n\\n.invalid-feedback[_ngcontent-%COMP%] {\\n  display: none;\\n  width: 100%;\\n  margin-top: 0.25rem;\\n  font-size: 0.875em;\\n  color: #dc3545;\\n}\\n\\n.is-invalid[_ngcontent-%COMP%]    ~ .invalid-feedback[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.d-block[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.badge-certificate[_ngcontent-%COMP%] {\\n  background-color: #FFF3EB !important;\\n  color: #FF6700;\\n}\\n\\n.p-block[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: column;\\n}\\n\\n.brandlogo[_ngcontent-%COMP%] {\\n  margin-left: 5px;\\n  color: #FF6700;\\n  font-size: 18px;\\n}\\n\\n#certificate[_ngcontent-%COMP%] {\\n  width: 327px;\\n  font-family: -apple-system, BlinkMacSystemFont, sans-serif;\\n  background-color: #ffffff;\\n}\\n\\n.transcription[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.saveBtn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 18px;\\n  cursor: pointer;\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1024px) {\\n  audio[_ngcontent-%COMP%] {\\n    width: 215px !important;\\n  }\\n}\\n\\n@media screen and (min-width: 319px) and (max-width: 386px) {\\n  .cncl-btn[_ngcontent-%COMP%] {\\n    margin-top: 15px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFkZC1lZGl0LWluc2lnaHQuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxXQUFBO0VBQ0EsVUFBQTtFQUNBLFlBQUE7QUFDSjs7QUFHQTtFQUNJLFlBQUE7RUFDQSx5QkFBQTtBQUFKOztBQUtBO0VBQVUsWUFBQTtFQUNOLDJCQUFBO0VBQ0Esd0JBQUE7RUFDQSx1QkFBQTtFQUNBLHVCQUFBO0FBREo7O0FBSUE7RUFDSSxnQkFBQTtFQUNBLFVBQUE7RUFDQSxjQUFBO0FBREo7O0FBSUE7RUFDRyxjQUFBO0FBREg7O0FBS0E7RUFDSSxZQUFBO0FBRko7O0FBSUE7RUFDSSxnQkFBQTtBQURKOztBQVFBO0VBQ0ksaUJBQUE7QUFMSjs7QUFPQTtFQUNJLGVBQUE7RUFDQSxnQkFBQTtBQUpKOztBQU9BO0VBQ0ksb0NBQUE7QUFKSjs7QUFNQztFQUNHLGFBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7QUFISjs7QUFNRTtFQUNFLGNBQUE7QUFISjs7QUFNRTtFQUNFLGNBQUE7QUFISjs7QUFpQkE7RUFDSSxvQ0FBQTtFQUNBLGNBQUE7QUFkSjs7QUFpQkU7RUFDRSxjQUFBO0FBZEo7O0FBaUJFO0VBQ0csYUFBQTtFQUNBLHVCQUFBO0VBQ0Esc0JBQUE7QUFkTDs7QUFpQkU7RUFDRSxnQkFBQTtFQUNBLGNBQUE7RUFFQSxlQUFBO0FBZko7O0FBa0JFO0VBQ0UsWUFBQTtFQUNBLDBEQUFBO0VBQ0EseUJBQUE7QUFmSjs7QUFrQkE7RUFDRSxrQkFBQTtBQWZGOztBQWlCQTtFQUNFLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7QUFkRjs7QUFpQkE7RUFDRTtJQUNFLHVCQUFBO0VBZEY7QUFDRjs7QUFpQkE7RUFDRTtJQUNFLGdCQUFBO0VBZkY7QUFDRiIsImZpbGUiOiJhZGQtZWRpdC1pbnNpZ2h0LmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLm1vZGFsLWRpYWxvZ3tcclxuICAgIGhlaWdodDogNTAlO1xyXG4gICAgd2lkdGg6IDUwJTtcclxuICAgIG1hcmdpbjphdXRvO1xyXG59XHJcblxyXG5cclxuLm1vZGFsLWhlYWRlcntcclxuICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDdiZmY7XHJcbn1cclxuXHJcblxyXG5cclxudGV4dGFyZWF7IGJvcmRlcjogbm9uZTsgXHJcbiAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbiAgICAtd2Via2l0LWFwcGVhcmFuY2U6bm9uZTtcclxuICAgIG91dGxpbmU6MHB4ICFpbXBvcnRhbnQ7XHJcbiAgICBib3JkZXI6IDFweCBzb2xpZCBibGFjaztcclxufVxyXG5cclxuLm9wZW5tb2RhbHtcclxuICAgIG1hcmdpbi1sZWZ0OiA0MCU7XHJcbiAgICB3aWR0aDogMjUlO1xyXG4gICAgbWFyZ2luLXRvcDogNSU7XHJcbn1cclxuXHJcbi5pY29uMXtcclxuICAgY29sb3I6ICMwMDdiZmY7XHJcblxyXG59XHJcblxyXG5he1xyXG4gICAgbWFyZ2luOiBhdXRvO1xyXG59XHJcbi5mb3JtLWNoZWNre1xyXG4gICAgcGFkZGluZy1sZWZ0OiA1JTtcclxufVxyXG5cclxuLy8gI2xlZnR7XHJcbi8vIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuLy8gbGVmdDogNTBweDtcclxuLy8gfVxyXG4ubWFyLXRvcHtcclxuICAgIG1hcmdpbi10b3A6MTUwcHg7XHJcbn1cclxuLkducnQtVHJhbnNjcmlwdGlvbi1idG57XHJcbiAgICBtYXJnaW4tdG9wOiA4cHg7XHJcbiAgICBtYXJnaW4tbGVmdDogN3B4O1xyXG59XHJcblxyXG4ubW9kYWwge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjUpXHJcbiB9XHJcbiAuaW52YWxpZC1mZWVkYmFjayB7XHJcbiAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBtYXJnaW4tdG9wOiAuMjVyZW07XHJcbiAgICBmb250LXNpemU6IC44NzVlbTtcclxuICAgIGNvbG9yOiAjZGMzNTQ1O1xyXG4gIH1cclxuICBcclxuICAuaXMtaW52YWxpZCB+IC5pbnZhbGlkLWZlZWRiYWNrIHtcclxuICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gIH1cclxuICBcclxuICAuZC1ibG9jayB7XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxuICB9XHJcbiAgXHJcbi8vICNyaWdodHtcclxuLy8gICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuLy8gICAgIHJpZ2h0OiAxMDBweDtcclxuLy8gfVxyXG4vLyBpbnB1dHtcclxuLy8gICAgIGNvbG9yOiAjMDA3YmZmO1xyXG5cclxuLy8gfVxyXG5cclxuLy9Dc3Mgb2YgQ2VydGlmaWNhdGVcclxuXHJcbi5iYWRnZS1jZXJ0aWZpY2F0ZXtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNGRkYzRUIgIWltcG9ydGFudDtcclxuICAgIGNvbG9yOiAjRkY2NzAwO1xyXG4gIH1cclxuICBcclxuICAucC1ibG9ja3tcclxuICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gIH1cclxuICBcclxuICAucHJvZ3Jlc3MtY29udGFpbmVye1xyXG4gICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICB9XHJcbiAgXHJcbiAgLmJyYW5kbG9nb3tcclxuICAgIG1hcmdpbi1sZWZ0OiA1cHg7XHJcbiAgICBjb2xvcjogI0ZGNjcwMDtcclxuICAgIC8vIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgZm9udC1zaXplOiAxOHB4O1xyXG4gIH1cclxuXHJcbiAgI2NlcnRpZmljYXRlIHtcclxuICAgIHdpZHRoOiAzMjdweDtcclxuICAgIGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsIHNhbnMtc2VyaWY7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmO1xyXG4gIH1cclxuICBcclxuLnRyYW5zY3JpcHRpb257XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG59XHJcbi5zYXZlQnRue1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICByaWdodDogMThweDtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbn1cclxuICBcclxuQG1lZGlhIHNjcmVlbiBhbmQgKG1pbi13aWR0aDogOTkycHgpIGFuZCAobWF4LXdpZHRoOiAxMDI0cHgpIHtcclxuICBhdWRpb3tcclxuICAgIHdpZHRoOiAyMTVweCAhaW1wb3J0YW50O1xyXG4gIH1cclxufVxyXG5cclxuQG1lZGlhIHNjcmVlbiBhbmQgKG1pbi13aWR0aDogMzE5cHgpIGFuZCAobWF4LXdpZHRoOiAzODZweCkge1xyXG4gIC5jbmNsLWJ0bntcclxuICAgIG1hcmdpbi10b3A6IDE1cHg7XHJcbiAgfVxyXG59XHJcbiJdfQ== */\"] });\r\n"]}, "metadata": {}, "sourceType": "module"}
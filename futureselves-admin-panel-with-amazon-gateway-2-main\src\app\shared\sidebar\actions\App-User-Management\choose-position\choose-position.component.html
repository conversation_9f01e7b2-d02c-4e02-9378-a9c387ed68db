<app-sidebar>
    <div class="content-wrapper">
      <div class="row">
        <div class="col-12 grid-margin stretch-card">
          <div class="card">
            <div class="card-header card-title bg-primary rounded-top text-white text-center mb-0">
              {{ showNewPositionForm ?'Add New Position' : 'Choose The Position You Would Like To Share About'}}
            </div>
            <div class="card-body">
              <div *ngIf="!showNewPositionForm">
                 <div *ngIf="!isExpert" class="row card-row">
                  <div class="col-lg-4 mb-4" *ngFor="let position of PositionData">
                    <div class="position-card"  (click)="viewCard(position)">
                      <div class="card-body p-2">
                        <h5 class="card-sector mb-2">{{ position.IN_name }}</h5>
                        <hr class="m-0 p-0">
                        <p class="mb-2 mt-1 card-role"><strong>{{ position.RO_title }}</strong></p>
                        <p class="mb-1 card-date">
                          Start Date: {{ formatDate(position.UI_startDate) }}
                        </p>
                        <p class="card-date">
                          End Date: {{ position.UI_endDate ? formatDate(position.UI_endDate) : 'Ongoing' }}
                        </p>
                      </div>
                    </div>
                  </div>

          
                  <div class="col-lg-4 mb-4">
                    <div class="add-new-card partner-card" (click)="addNewPosition()" style="cursor: pointer;">
                      <div class="card-body d-flex flex-column align-items-center justify-content-center"
                          data-bs-toggle="modal" data-bs-target="#staticBackdrop">
                        <span class="add-icon">+</span>
                        <span class="add-text">Add New Position</span>
                      </div>
                    </div>
                  </div>
          
                </div> 

                <div *ngIf="isExpert&&expertSector"  class="row card-row">
                  <div (click)="viewCard()" class="col-lg-4 mb-4">
                    <div class="position-card">
                      <div class="card-body p-2">
                        <h4 class="card-sector mb-3">Expert Insights</h4>
                        <hr class="m-0 p-0">
                        <h5 class="card-sector mt-3">{{expertSector}}</h5>    
                        <p class="mt-3 card-role"><strong>{{expertRole}}</strong></p>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
            
              <form *ngIf="showNewPositionForm" class="forms-sample mt-4" [formGroup]="addNewPositionForm" (ngSubmit)="savePosition()">
                <div class="row">
                  <div class="form-group col-lg-6">
                    <label for="UI_industryId" class="required-field">Sector</label>
                    <select (change)="onChangeIndustry($event)" class="form-control form-control-sm" formControlName="UI_industryId" required>
                      <option selected disabled value="">Select an sector name</option>
                      <option *ngFor="let industry of sectorsArray" [value]="industry.IN_id">{{ industry.IN_name }}</option>
                    </select>
                  </div>
              
                  <div class="form-group col-lg-6">
                    <label for="UI_roleId" class="required-field">Role</label>
                    <select (click)="onSelectOtherDropdown($event)" class="form-control form-control-sm" formControlName="UI_roleId" [style.pointer-events]="industrySelected ? 'auto' : 'none'" required>
                      <option selected disabled value="">Select a role name</option>
                      <option *ngFor="let role of rolesArray" [value]="role.RO_id">{{ role.RO_title }}</option>
                    </select>
                    <div *ngIf="!industrySelected" class="mt-1">
                      <small>Please select a sector first.</small>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="form-group col-lg-6">
                    <label for="UI_startDate"  class="required-field">Start Date</label>
                    <input type="date"   (change)="updateStartDateMin()" (input)="checkInvalidDate('UI_startDate')"  class="form-control form-control-sm" formControlName="UI_startDate" required />
                  </div>

                  <div class="form-group col-lg-6">
                    <label for="UI_endDate" class="required-field">End Date</label>
                    <input type="date" (input)="checkInvalidDate('UI_endDate')" [min]="minStartDate" class="form-control form-control-sm" formControlName="UI_endDate" [disabled]="isCurrentlyWorking || !addNewPositionForm.get('UI_startDate')?.value" />
                    
                      <div class="form-check ml-4">
                        <input class="form-check-input" (change)="onCurrentlyWorkingChange($event)" type="checkbox" value="" id="currentlyWorkingCheckbox">
                        <label class="form-check-label ml-1" for="currentlyWorkingCheckbox">
                          I am currently working in this role.
                        </label>
                    </div>
                  </div>

                </div>
                <div class="text-center my-3">
                  <button type="submit" class="btn btn-primary mr-2">Save</button>
                  <button type="button" class="btn btn-light" (click)="hideForm()">Cancel</button>
                </div>
              </form>
              

              <div *ngIf="!showNewPositionForm" class="text-center mt-4">
                <button class="btn btn-light mr-2 my-3" routerLink="/actions/app-users">Back</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </app-sidebar>
  

  <div class="modal" id="staticBackdrop">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title fs-5" id="staticBackdropLabel">Expert User</h4>
            </div>
            <div class="modal-body">
              <form  class="forms-sample mt-4" [formGroup]="addNewPositionForm">
                <div class="form-group col-lg-12">
                  <label for="UI_industryId" class="required-field">Sector</label>
                  <select class="form-control form-control-sm" formControlName="UI_industryId" required>
                    <option selected disabled value="">Select an sector name</option>
                    <option *ngFor="let industry of sectorsArray" [value]="industry.IN_id">{{ industry.IN_name }}</option>
                  </select>
                </div>
            
                <div class="form-group col-lg-12">
                  <label for="UI_roleId" class="required-field">Title / Role</label>
                  <input type="text" class="form-control form-control-sm" formControlName="UI_roleId" placeholder="Enter Role" required>
                  <div *ngIf="addNewPositionForm.get('UI_roleId')?.errors?.maxlength &&addNewPositionForm.get('UI_roleId')?.touched">
                    <div class="warning">Role name cannot exceed 50 characters.</div>
                  </div>
                </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" (click)="savePosition()">Save</button>
                <button type="button" class="btn btn-light" routerLink="/actions/app-users">Cancel</button>
            </div>
        </div>
    </div>
</div>

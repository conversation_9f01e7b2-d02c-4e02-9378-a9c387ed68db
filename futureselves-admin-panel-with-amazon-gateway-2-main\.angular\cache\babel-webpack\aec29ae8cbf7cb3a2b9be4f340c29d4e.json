{"ast": null, "code": "import * as i4 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Attribute, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject, merge, fromEvent } from 'rxjs';\nimport { startWith, takeUntil, take } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i2 from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst _c0 = [\"connectionContainer\"];\nconst _c1 = [\"inputContainer\"];\nconst _c2 = [\"label\"];\n\nfunction MatFormField_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 14);\n    i0.ɵɵelement(2, \"div\", 15);\n    i0.ɵɵelement(3, \"div\", 16);\n    i0.ɵɵelement(4, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 18);\n    i0.ɵɵelement(6, \"div\", 15);\n    i0.ɵɵelement(7, \"div\", 16);\n    i0.ɵɵelement(8, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nfunction MatFormField_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵlistener(\"cdkObserveContent\", function MatFormField_div_4_Template_div_cdkObserveContent_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return ctx_r9.updateOutlineGap();\n    });\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"cdkObserveContentDisabled\", ctx_r2.appearance != \"outline\");\n  }\n}\n\nfunction MatFormField_label_9_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r12._control.placeholder);\n  }\n}\n\nfunction MatFormField_label_9_ng_content_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 3, [\"*ngSwitchCase\", \"true\"]);\n  }\n}\n\nfunction MatFormField_label_9_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1, \" *\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction MatFormField_label_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"label\", 20, 21);\n    i0.ɵɵlistener(\"cdkObserveContent\", function MatFormField_label_9_Template_label_cdkObserveContent_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return ctx_r15.updateOutlineGap();\n    });\n    i0.ɵɵtemplate(2, MatFormField_label_9_ng_container_2_Template, 4, 1, \"ng-container\", 12);\n    i0.ɵɵtemplate(3, MatFormField_label_9_ng_content_3_Template, 1, 0, \"ng-content\", 12);\n    i0.ɵɵtemplate(4, MatFormField_label_9_span_4_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"mat-empty\", ctx_r4._control.empty && !ctx_r4._shouldAlwaysFloat())(\"mat-form-field-empty\", ctx_r4._control.empty && !ctx_r4._shouldAlwaysFloat())(\"mat-accent\", ctx_r4.color == \"accent\")(\"mat-warn\", ctx_r4.color == \"warn\");\n    i0.ɵɵproperty(\"cdkObserveContentDisabled\", ctx_r4.appearance != \"outline\")(\"id\", ctx_r4._labelId)(\"ngSwitch\", ctx_r4._hasLabel());\n    i0.ɵɵattribute(\"for\", ctx_r4._control.id)(\"aria-owns\", ctx_r4._control.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitchCase\", false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.hideRequiredMarker && ctx_r4._control.required && !ctx_r4._control.disabled);\n  }\n}\n\nfunction MatFormField_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵprojection(1, 4);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction MatFormField_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"span\", 26);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"mat-accent\", ctx_r6.color == \"accent\")(\"mat-warn\", ctx_r6.color == \"warn\");\n  }\n}\n\nfunction MatFormField_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵprojection(1, 5);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@transitionMessages\", ctx_r7._subscriptAnimationState);\n  }\n}\n\nfunction MatFormField_div_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"id\", ctx_r17._hintLabelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r17.hintLabel);\n  }\n}\n\nfunction MatFormField_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, MatFormField_div_14_div_1_Template, 2, 2, \"div\", 28);\n    i0.ɵɵprojection(2, 6);\n    i0.ɵɵelement(3, \"div\", 29);\n    i0.ɵɵprojection(4, 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@transitionMessages\", ctx_r8._subscriptAnimationState);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.hintLabel);\n  }\n}\n\nconst _c3 = [\"*\", [[\"\", \"matPrefix\", \"\"]], [[\"mat-placeholder\"]], [[\"mat-label\"]], [[\"\", \"matSuffix\", \"\"]], [[\"mat-error\"]], [[\"mat-hint\", 3, \"align\", \"end\"]], [[\"mat-hint\", \"align\", \"end\"]]];\nconst _c4 = [\"*\", \"[matPrefix]\", \"mat-placeholder\", \"mat-label\", \"[matSuffix]\", \"mat-error\", \"mat-hint:not([align='end'])\", \"mat-hint[align='end']\"];\nlet nextUniqueId$2 = 0;\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst MAT_ERROR = new InjectionToken('MatError');\n/** Single error message to be shown underneath the form field. */\n\nclass MatError {\n  constructor(ariaLive, elementRef) {\n    this.id = `mat-error-${nextUniqueId$2++}`; // If no aria-live value is set add 'polite' as a default. This is preferred over setting\n    // role='alert' so that screen readers do not interrupt the current task to read this aloud.\n\n    if (!ariaLive) {\n      elementRef.nativeElement.setAttribute('aria-live', 'polite');\n    }\n  }\n\n}\n\nMatError.ɵfac = function MatError_Factory(t) {\n  return new (t || MatError)(i0.ɵɵinjectAttribute('aria-live'), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nMatError.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatError,\n  selectors: [[\"mat-error\"]],\n  hostAttrs: [\"aria-atomic\", \"true\", 1, \"mat-error\"],\n  hostVars: 1,\n  hostBindings: function MatError_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.id);\n    }\n  },\n  inputs: {\n    id: \"id\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_ERROR,\n    useExisting: MatError\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatError, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-error',\n      host: {\n        'class': 'mat-error',\n        '[attr.id]': 'id',\n        'aria-atomic': 'true'\n      },\n      providers: [{\n        provide: MAT_ERROR,\n        useExisting: MatError\n      }]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['aria-live']\n      }]\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Animations used by the MatFormField.\n * @docs-private\n */\n\n\nconst matFormFieldAnimations = {\n  /** Animation that transitions the form field's error and hint messages. */\n  transitionMessages: trigger('transitionMessages', [// TODO(mmalerba): Use angular animations for label animation as well.\n  state('enter', style({\n    opacity: 1,\n    transform: 'translateY(0%)'\n  })), transition('void => enter', [style({\n    opacity: 0,\n    transform: 'translateY(-5px)'\n  }), animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)')])])\n};\n/** An interface which allows a control to work inside of a `MatFormField`. */\n\nclass MatFormFieldControl {}\n\nMatFormFieldControl.ɵfac = function MatFormFieldControl_Factory(t) {\n  return new (t || MatFormFieldControl)();\n};\n\nMatFormFieldControl.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatFormFieldControl\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldControl, [{\n    type: Directive\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @docs-private */\n\n\nfunction getMatFormFieldPlaceholderConflictError() {\n  return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\n\n\nfunction getMatFormFieldDuplicatedHintError(align) {\n  return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\n\n\nfunction getMatFormFieldMissingControlError() {\n  return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet nextUniqueId$1 = 0;\n/**\n * Injection token that can be used to reference instances of `MatHint`. It serves as\n * alternative token to the actual `MatHint` class which could cause unnecessary\n * retention of the class and its directive metadata.\n *\n * *Note*: This is not part of the public API as the MDC-based form-field will not\n * need a lightweight token for `MatHint` and we want to reduce breaking changes.\n */\n\nconst _MAT_HINT = new InjectionToken('MatHint');\n/** Hint text to be shown underneath the form field control. */\n\n\nclass MatHint {\n  constructor() {\n    /** Whether to align the hint label at the start or end of the line. */\n    this.align = 'start';\n    /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n\n    this.id = `mat-hint-${nextUniqueId$1++}`;\n  }\n\n}\n\nMatHint.ɵfac = function MatHint_Factory(t) {\n  return new (t || MatHint)();\n};\n\nMatHint.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatHint,\n  selectors: [[\"mat-hint\"]],\n  hostAttrs: [1, \"mat-hint\"],\n  hostVars: 4,\n  hostBindings: function MatHint_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.id)(\"align\", null);\n      i0.ɵɵclassProp(\"mat-form-field-hint-end\", ctx.align === \"end\");\n    }\n  },\n  inputs: {\n    align: \"align\",\n    id: \"id\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: _MAT_HINT,\n    useExisting: MatHint\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatHint, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-hint',\n      host: {\n        'class': 'mat-hint',\n        '[class.mat-form-field-hint-end]': 'align === \"end\"',\n        '[attr.id]': 'id',\n        // Remove align attribute to prevent it from interfering with layout.\n        '[attr.align]': 'null'\n      },\n      providers: [{\n        provide: _MAT_HINT,\n        useExisting: MatHint\n      }]\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** The floating label for a `mat-form-field`. */\n\n\nclass MatLabel {}\n\nMatLabel.ɵfac = function MatLabel_Factory(t) {\n  return new (t || MatLabel)();\n};\n\nMatLabel.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatLabel,\n  selectors: [[\"mat-label\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatLabel, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-label'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The placeholder text for an `MatFormField`.\n * @deprecated Use `<mat-label>` to specify the label and the `placeholder` attribute to specify the\n *     placeholder.\n * @breaking-change 8.0.0\n */\n\n\nclass MatPlaceholder {}\n\nMatPlaceholder.ɵfac = function MatPlaceholder_Factory(t) {\n  return new (t || MatPlaceholder)();\n};\n\nMatPlaceholder.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatPlaceholder,\n  selectors: [[\"mat-placeholder\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPlaceholder, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-placeholder'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_PREFIX = new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\n\nclass MatPrefix {}\n\nMatPrefix.ɵfac = function MatPrefix_Factory(t) {\n  return new (t || MatPrefix)();\n};\n\nMatPrefix.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatPrefix,\n  selectors: [[\"\", \"matPrefix\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_PREFIX,\n    useExisting: MatPrefix\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatPrefix, [{\n    type: Directive,\n    args: [{\n      selector: '[matPrefix]',\n      providers: [{\n        provide: MAT_PREFIX,\n        useExisting: MatPrefix\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_SUFFIX = new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\n\nclass MatSuffix {}\n\nMatSuffix.ɵfac = function MatSuffix_Factory(t) {\n  return new (t || MatSuffix)();\n};\n\nMatSuffix.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatSuffix,\n  selectors: [[\"\", \"matSuffix\", \"\"]],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_SUFFIX,\n    useExisting: MatSuffix\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSuffix, [{\n    type: Directive,\n    args: [{\n      selector: '[matSuffix]',\n      providers: [{\n        provide: MAT_SUFFIX,\n        useExisting: MatSuffix\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet nextUniqueId = 0;\nconst floatingLabelScale = 0.75;\nconst outlineGapPadding = 5;\n/**\n * Boilerplate for applying mixins to MatFormField.\n * @docs-private\n */\n\nconst _MatFormFieldBase = mixinColor(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n}, 'primary');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\n\n\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\n\nconst MAT_FORM_FIELD = new InjectionToken('MatFormField');\n/** Container for form controls that applies Material Design styling and behavior. */\n\nclass MatFormField extends _MatFormFieldBase {\n  constructor(elementRef, _changeDetectorRef, _dir, _defaults, _platform, _ngZone, _animationMode) {\n    super(elementRef);\n    this._changeDetectorRef = _changeDetectorRef;\n    this._dir = _dir;\n    this._defaults = _defaults;\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    /**\n     * Whether the outline gap needs to be calculated\n     * immediately on the next change detection run.\n     */\n\n    this._outlineGapCalculationNeededImmediately = false;\n    /** Whether the outline gap needs to be calculated next time the zone has stabilized. */\n\n    this._outlineGapCalculationNeededOnStable = false;\n    this._destroyed = new Subject();\n    /** Override for the logic that disables the label animation in certain cases. */\n\n    this._showAlwaysAnimate = false;\n    /** State of the mat-hint and mat-error animations. */\n\n    this._subscriptAnimationState = '';\n    this._hintLabel = ''; // Unique id for the hint label.\n\n    this._hintLabelId = `mat-hint-${nextUniqueId++}`; // Unique id for the label element.\n\n    this._labelId = `mat-form-field-label-${nextUniqueId++}`;\n    this.floatLabel = this._getDefaultFloatLabelState();\n    this._animationsEnabled = _animationMode !== 'NoopAnimations'; // Set the default through here so we invoke the setter on the first run.\n\n    this.appearance = _defaults && _defaults.appearance ? _defaults.appearance : 'legacy';\n    this._hideRequiredMarker = _defaults && _defaults.hideRequiredMarker != null ? _defaults.hideRequiredMarker : false;\n  }\n  /** The form-field appearance style. */\n\n\n  get appearance() {\n    return this._appearance;\n  }\n\n  set appearance(value) {\n    const oldValue = this._appearance;\n    this._appearance = value || this._defaults && this._defaults.appearance || 'legacy';\n\n    if (this._appearance === 'outline' && oldValue !== value) {\n      this._outlineGapCalculationNeededOnStable = true;\n    }\n  }\n  /** Whether the required marker should be hidden. */\n\n\n  get hideRequiredMarker() {\n    return this._hideRequiredMarker;\n  }\n\n  set hideRequiredMarker(value) {\n    this._hideRequiredMarker = coerceBooleanProperty(value);\n  }\n  /** Whether the floating label should always float or not. */\n\n\n  _shouldAlwaysFloat() {\n    return this.floatLabel === 'always' && !this._showAlwaysAnimate;\n  }\n  /** Whether the label can float or not. */\n\n\n  _canLabelFloat() {\n    return this.floatLabel !== 'never';\n  }\n  /** Text for the form field hint. */\n\n\n  get hintLabel() {\n    return this._hintLabel;\n  }\n\n  set hintLabel(value) {\n    this._hintLabel = value;\n\n    this._processHints();\n  }\n  /**\n   * Whether the label should always float, never float or float as the user types.\n   *\n   * Note: only the legacy appearance supports the `never` option. `never` was originally added as a\n   * way to make the floating label emulate the behavior of a standard input placeholder. However\n   * the form field now supports both floating labels and placeholders. Therefore in the non-legacy\n   * appearances the `never` option has been disabled in favor of just using the placeholder.\n   */\n\n\n  get floatLabel() {\n    return this.appearance !== 'legacy' && this._floatLabel === 'never' ? 'auto' : this._floatLabel;\n  }\n\n  set floatLabel(value) {\n    if (value !== this._floatLabel) {\n      this._floatLabel = value || this._getDefaultFloatLabelState();\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n\n  get _control() {\n    // TODO(crisbeto): we need this workaround in order to support both Ivy and ViewEngine.\n    //  We should clean this up once Ivy is the default renderer.\n    return this._explicitFormFieldControl || this._controlNonStatic || this._controlStatic;\n  }\n\n  set _control(value) {\n    this._explicitFormFieldControl = value;\n  }\n  /**\n   * Gets the id of the label element. If no label is present, returns `null`.\n   */\n\n\n  getLabelId() {\n    return this._hasFloatingLabel() ? this._labelId : null;\n  }\n  /**\n   * Gets an ElementRef for the element that a overlay attached to the form-field should be\n   * positioned relative to.\n   */\n\n\n  getConnectedOverlayOrigin() {\n    return this._connectionContainerRef || this._elementRef;\n  }\n\n  ngAfterContentInit() {\n    this._validateControlChild();\n\n    const control = this._control;\n\n    if (control.controlType) {\n      this._elementRef.nativeElement.classList.add(`mat-form-field-type-${control.controlType}`);\n    } // Subscribe to changes in the child control state in order to update the form field UI.\n\n\n    control.stateChanges.pipe(startWith(null)).subscribe(() => {\n      this._validatePlaceholders();\n\n      this._syncDescribedByIds();\n\n      this._changeDetectorRef.markForCheck();\n    }); // Run change detection if the value changes.\n\n    if (control.ngControl && control.ngControl.valueChanges) {\n      control.ngControl.valueChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._changeDetectorRef.markForCheck());\n    } // Note that we have to run outside of the `NgZone` explicitly,\n    // in order to avoid throwing users into an infinite loop\n    // if `zone-patch-rxjs` is included.\n\n\n    this._ngZone.runOutsideAngular(() => {\n      this._ngZone.onStable.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        if (this._outlineGapCalculationNeededOnStable) {\n          this.updateOutlineGap();\n        }\n      });\n    }); // Run change detection and update the outline if the suffix or prefix changes.\n\n\n    merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n      this._outlineGapCalculationNeededOnStable = true;\n\n      this._changeDetectorRef.markForCheck();\n    }); // Re-validate when the number of hints changes.\n\n    this._hintChildren.changes.pipe(startWith(null)).subscribe(() => {\n      this._processHints();\n\n      this._changeDetectorRef.markForCheck();\n    }); // Update the aria-described by when the number of errors changes.\n\n\n    this._errorChildren.changes.pipe(startWith(null)).subscribe(() => {\n      this._syncDescribedByIds();\n\n      this._changeDetectorRef.markForCheck();\n    });\n\n    if (this._dir) {\n      this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        if (typeof requestAnimationFrame === 'function') {\n          this._ngZone.runOutsideAngular(() => {\n            requestAnimationFrame(() => this.updateOutlineGap());\n          });\n        } else {\n          this.updateOutlineGap();\n        }\n      });\n    }\n  }\n\n  ngAfterContentChecked() {\n    this._validateControlChild();\n\n    if (this._outlineGapCalculationNeededImmediately) {\n      this.updateOutlineGap();\n    }\n  }\n\n  ngAfterViewInit() {\n    // Avoid animations on load.\n    this._subscriptAnimationState = 'enter';\n\n    this._changeDetectorRef.detectChanges();\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n\n    this._destroyed.complete();\n  }\n  /** Determines whether a class from the NgControl should be forwarded to the host element. */\n\n\n  _shouldForward(prop) {\n    const ngControl = this._control ? this._control.ngControl : null;\n    return ngControl && ngControl[prop];\n  }\n\n  _hasPlaceholder() {\n    return !!(this._control && this._control.placeholder || this._placeholderChild);\n  }\n\n  _hasLabel() {\n    return !!(this._labelChildNonStatic || this._labelChildStatic);\n  }\n\n  _shouldLabelFloat() {\n    return this._canLabelFloat() && (this._control && this._control.shouldLabelFloat || this._shouldAlwaysFloat());\n  }\n\n  _hideControlPlaceholder() {\n    // In the legacy appearance the placeholder is promoted to a label if no label is given.\n    return this.appearance === 'legacy' && !this._hasLabel() || this._hasLabel() && !this._shouldLabelFloat();\n  }\n\n  _hasFloatingLabel() {\n    // In the legacy appearance the placeholder is promoted to a label if no label is given.\n    return this._hasLabel() || this.appearance === 'legacy' && this._hasPlaceholder();\n  }\n  /** Determines whether to display hints or errors. */\n\n\n  _getDisplayedMessages() {\n    return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState ? 'error' : 'hint';\n  }\n  /** Animates the placeholder up and locks it in position. */\n\n\n  _animateAndLockLabel() {\n    if (this._hasFloatingLabel() && this._canLabelFloat()) {\n      // If animations are disabled, we shouldn't go in here,\n      // because the `transitionend` will never fire.\n      if (this._animationsEnabled && this._label) {\n        this._showAlwaysAnimate = true;\n        fromEvent(this._label.nativeElement, 'transitionend').pipe(take(1)).subscribe(() => {\n          this._showAlwaysAnimate = false;\n        });\n      }\n\n      this.floatLabel = 'always';\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Ensure that there is only one placeholder (either `placeholder` attribute on the child control\n   * or child element with the `mat-placeholder` directive).\n   */\n\n\n  _validatePlaceholders() {\n    if (this._control.placeholder && this._placeholderChild && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatFormFieldPlaceholderConflictError();\n    }\n  }\n  /** Does any extra processing that is required when handling the hints. */\n\n\n  _processHints() {\n    this._validateHints();\n\n    this._syncDescribedByIds();\n  }\n  /**\n   * Ensure that there is a maximum of one of each `<mat-hint>` alignment specified, with the\n   * attribute being considered as `align=\"start\"`.\n   */\n\n\n  _validateHints() {\n    if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      let startHint;\n      let endHint;\n\n      this._hintChildren.forEach(hint => {\n        if (hint.align === 'start') {\n          if (startHint || this.hintLabel) {\n            throw getMatFormFieldDuplicatedHintError('start');\n          }\n\n          startHint = hint;\n        } else if (hint.align === 'end') {\n          if (endHint) {\n            throw getMatFormFieldDuplicatedHintError('end');\n          }\n\n          endHint = hint;\n        }\n      });\n    }\n  }\n  /** Gets the default float label state. */\n\n\n  _getDefaultFloatLabelState() {\n    return this._defaults && this._defaults.floatLabel || 'auto';\n  }\n  /**\n   * Sets the list of element IDs that describe the child control. This allows the control to update\n   * its `aria-describedby` attribute accordingly.\n   */\n\n\n  _syncDescribedByIds() {\n    if (this._control) {\n      let ids = []; // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n\n      if (this._control.userAriaDescribedBy && typeof this._control.userAriaDescribedBy === 'string') {\n        ids.push(...this._control.userAriaDescribedBy.split(' '));\n      }\n\n      if (this._getDisplayedMessages() === 'hint') {\n        const startHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'start') : null;\n        const endHint = this._hintChildren ? this._hintChildren.find(hint => hint.align === 'end') : null;\n\n        if (startHint) {\n          ids.push(startHint.id);\n        } else if (this._hintLabel) {\n          ids.push(this._hintLabelId);\n        }\n\n        if (endHint) {\n          ids.push(endHint.id);\n        }\n      } else if (this._errorChildren) {\n        ids.push(...this._errorChildren.map(error => error.id));\n      }\n\n      this._control.setDescribedByIds(ids);\n    }\n  }\n  /** Throws an error if the form field's control is missing. */\n\n\n  _validateControlChild() {\n    if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatFormFieldMissingControlError();\n    }\n  }\n  /**\n   * Updates the width and position of the gap in the outline. Only relevant for the outline\n   * appearance.\n   */\n\n\n  updateOutlineGap() {\n    const labelEl = this._label ? this._label.nativeElement : null;\n    const container = this._connectionContainerRef.nativeElement;\n    const outlineStartSelector = '.mat-form-field-outline-start';\n    const outlineGapSelector = '.mat-form-field-outline-gap'; // getBoundingClientRect isn't available on the server.\n\n    if (this.appearance !== 'outline' || !this._platform.isBrowser) {\n      return;\n    } // If there is no content, set the gap elements to zero.\n\n\n    if (!labelEl || !labelEl.children.length || !labelEl.textContent.trim()) {\n      const gapElements = container.querySelectorAll(`${outlineStartSelector}, ${outlineGapSelector}`);\n\n      for (let i = 0; i < gapElements.length; i++) {\n        gapElements[i].style.width = '0';\n      }\n\n      return;\n    } // If the element is not present in the DOM, the outline gap will need to be calculated\n    // the next time it is checked and in the DOM.\n\n\n    if (!this._isAttachedToDOM()) {\n      this._outlineGapCalculationNeededImmediately = true;\n      return;\n    }\n\n    let startWidth = 0;\n    let gapWidth = 0;\n    const startEls = container.querySelectorAll(outlineStartSelector);\n    const gapEls = container.querySelectorAll(outlineGapSelector);\n\n    if (this._label && this._label.nativeElement.children.length) {\n      const containerRect = container.getBoundingClientRect(); // If the container's width and height are zero, it means that the element is\n      // invisible and we can't calculate the outline gap. Mark the element as needing\n      // to be checked the next time the zone stabilizes. We can't do this immediately\n      // on the next change detection, because even if the element becomes visible,\n      // the `ClientRect` won't be reclaculated immediately. We reset the\n      // `_outlineGapCalculationNeededImmediately` flag some we don't run the checks twice.\n\n      if (containerRect.width === 0 && containerRect.height === 0) {\n        this._outlineGapCalculationNeededOnStable = true;\n        this._outlineGapCalculationNeededImmediately = false;\n        return;\n      }\n\n      const containerStart = this._getStartEnd(containerRect);\n\n      const labelChildren = labelEl.children;\n\n      const labelStart = this._getStartEnd(labelChildren[0].getBoundingClientRect());\n\n      let labelWidth = 0;\n\n      for (let i = 0; i < labelChildren.length; i++) {\n        labelWidth += labelChildren[i].offsetWidth;\n      }\n\n      startWidth = Math.abs(labelStart - containerStart) - outlineGapPadding;\n      gapWidth = labelWidth > 0 ? labelWidth * floatingLabelScale + outlineGapPadding * 2 : 0;\n    }\n\n    for (let i = 0; i < startEls.length; i++) {\n      startEls[i].style.width = `${startWidth}px`;\n    }\n\n    for (let i = 0; i < gapEls.length; i++) {\n      gapEls[i].style.width = `${gapWidth}px`;\n    }\n\n    this._outlineGapCalculationNeededOnStable = this._outlineGapCalculationNeededImmediately = false;\n  }\n  /** Gets the start end of the rect considering the current directionality. */\n\n\n  _getStartEnd(rect) {\n    return this._dir && this._dir.value === 'rtl' ? rect.right : rect.left;\n  }\n  /** Checks whether the form field is attached to the DOM. */\n\n\n  _isAttachedToDOM() {\n    const element = this._elementRef.nativeElement;\n\n    if (element.getRootNode) {\n      const rootNode = element.getRootNode(); // If the element is inside the DOM the root node will be either the document\n      // or the closest shadow root, otherwise it'll be the element itself.\n\n      return rootNode && rootNode !== element;\n    } // Otherwise fall back to checking if it's in the document. This doesn't account for\n    // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n\n\n    return document.documentElement.contains(element);\n  }\n\n}\n\nMatFormField.ɵfac = function MatFormField_Factory(t) {\n  return new (t || MatFormField)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n};\n\nMatFormField.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatFormField,\n  selectors: [[\"mat-form-field\"]],\n  contentQueries: function MatFormField_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatFormFieldControl, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatFormFieldControl, 7);\n      i0.ɵɵcontentQuery(dirIndex, MatLabel, 5);\n      i0.ɵɵcontentQuery(dirIndex, MatLabel, 7);\n      i0.ɵɵcontentQuery(dirIndex, MatPlaceholder, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_ERROR, 5);\n      i0.ɵɵcontentQuery(dirIndex, _MAT_HINT, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_PREFIX, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_SUFFIX, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._controlNonStatic = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._controlStatic = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelChildNonStatic = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelChildStatic = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._placeholderChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._errorChildren = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._hintChildren = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._prefixChildren = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._suffixChildren = _t);\n    }\n  },\n  viewQuery: function MatFormField_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n      i0.ɵɵviewQuery(_c1, 5);\n      i0.ɵɵviewQuery(_c2, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._connectionContainerRef = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputContainerRef = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n    }\n  },\n  hostAttrs: [1, \"mat-form-field\"],\n  hostVars: 40,\n  hostBindings: function MatFormField_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"mat-form-field-appearance-standard\", ctx.appearance == \"standard\")(\"mat-form-field-appearance-fill\", ctx.appearance == \"fill\")(\"mat-form-field-appearance-outline\", ctx.appearance == \"outline\")(\"mat-form-field-appearance-legacy\", ctx.appearance == \"legacy\")(\"mat-form-field-invalid\", ctx._control.errorState)(\"mat-form-field-can-float\", ctx._canLabelFloat())(\"mat-form-field-should-float\", ctx._shouldLabelFloat())(\"mat-form-field-has-label\", ctx._hasFloatingLabel())(\"mat-form-field-hide-placeholder\", ctx._hideControlPlaceholder())(\"mat-form-field-disabled\", ctx._control.disabled)(\"mat-form-field-autofilled\", ctx._control.autofilled)(\"mat-focused\", ctx._control.focused)(\"ng-untouched\", ctx._shouldForward(\"untouched\"))(\"ng-touched\", ctx._shouldForward(\"touched\"))(\"ng-pristine\", ctx._shouldForward(\"pristine\"))(\"ng-dirty\", ctx._shouldForward(\"dirty\"))(\"ng-valid\", ctx._shouldForward(\"valid\"))(\"ng-invalid\", ctx._shouldForward(\"invalid\"))(\"ng-pending\", ctx._shouldForward(\"pending\"))(\"_mat-animation-noopable\", !ctx._animationsEnabled);\n    }\n  },\n  inputs: {\n    color: \"color\",\n    appearance: \"appearance\",\n    hideRequiredMarker: \"hideRequiredMarker\",\n    hintLabel: \"hintLabel\",\n    floatLabel: \"floatLabel\"\n  },\n  exportAs: [\"matFormField\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_FORM_FIELD,\n    useExisting: MatFormField\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c4,\n  decls: 15,\n  vars: 8,\n  consts: [[1, \"mat-form-field-wrapper\"], [1, \"mat-form-field-flex\", 3, \"click\"], [\"connectionContainer\", \"\"], [4, \"ngIf\"], [\"class\", \"mat-form-field-prefix\", 3, \"cdkObserveContentDisabled\", \"cdkObserveContent\", 4, \"ngIf\"], [1, \"mat-form-field-infix\"], [\"inputContainer\", \"\"], [1, \"mat-form-field-label-wrapper\"], [\"class\", \"mat-form-field-label\", 3, \"cdkObserveContentDisabled\", \"id\", \"mat-empty\", \"mat-form-field-empty\", \"mat-accent\", \"mat-warn\", \"ngSwitch\", \"cdkObserveContent\", 4, \"ngIf\"], [\"class\", \"mat-form-field-suffix\", 4, \"ngIf\"], [\"class\", \"mat-form-field-underline\", 4, \"ngIf\"], [1, \"mat-form-field-subscript-wrapper\", 3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [\"class\", \"mat-form-field-hint-wrapper\", 4, \"ngSwitchCase\"], [1, \"mat-form-field-outline\"], [1, \"mat-form-field-outline-start\"], [1, \"mat-form-field-outline-gap\"], [1, \"mat-form-field-outline-end\"], [1, \"mat-form-field-outline\", \"mat-form-field-outline-thick\"], [1, \"mat-form-field-prefix\", 3, \"cdkObserveContentDisabled\", \"cdkObserveContent\"], [1, \"mat-form-field-label\", 3, \"cdkObserveContentDisabled\", \"id\", \"ngSwitch\", \"cdkObserveContent\"], [\"label\", \"\"], [\"class\", \"mat-placeholder-required mat-form-field-required-marker\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"aria-hidden\", \"true\", 1, \"mat-placeholder-required\", \"mat-form-field-required-marker\"], [1, \"mat-form-field-suffix\"], [1, \"mat-form-field-underline\"], [1, \"mat-form-field-ripple\"], [1, \"mat-form-field-hint-wrapper\"], [\"class\", \"mat-hint\", 3, \"id\", 4, \"ngIf\"], [1, \"mat-form-field-hint-spacer\"], [1, \"mat-hint\", 3, \"id\"]],\n  template: function MatFormField_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c3);\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelementStart(1, \"div\", 1, 2);\n      i0.ɵɵlistener(\"click\", function MatFormField_Template_div_click_1_listener($event) {\n        return ctx._control.onContainerClick && ctx._control.onContainerClick($event);\n      });\n      i0.ɵɵtemplate(3, MatFormField_ng_container_3_Template, 9, 0, \"ng-container\", 3);\n      i0.ɵɵtemplate(4, MatFormField_div_4_Template, 2, 1, \"div\", 4);\n      i0.ɵɵelementStart(5, \"div\", 5, 6);\n      i0.ɵɵprojection(7);\n      i0.ɵɵelementStart(8, \"span\", 7);\n      i0.ɵɵtemplate(9, MatFormField_label_9_Template, 5, 16, \"label\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(10, MatFormField_div_10_Template, 2, 0, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(11, MatFormField_div_11_Template, 2, 4, \"div\", 10);\n      i0.ɵɵelementStart(12, \"div\", 11);\n      i0.ɵɵtemplate(13, MatFormField_div_13_Template, 2, 1, \"div\", 12);\n      i0.ɵɵtemplate(14, MatFormField_div_14_Template, 5, 2, \"div\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.appearance == \"outline\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx._prefixChildren.length);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx._hasFloatingLabel());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx._suffixChildren.length);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.appearance != \"outline\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitch\", ctx._getDisplayedMessages());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", \"error\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", \"hint\");\n    }\n  },\n  directives: [i3.NgIf, i3.NgSwitch, i3.NgSwitchCase, i4.CdkObserveContent],\n  styles: [\".mat-form-field{display:inline-block;position:relative;text-align:left}[dir=rtl] .mat-form-field{text-align:right}.mat-form-field-wrapper{position:relative}.mat-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-form-field-prefix,.mat-form-field-suffix{white-space:nowrap;flex:none;position:relative}.mat-form-field-infix{display:block;position:relative;flex:auto;min-width:0;width:180px}.cdk-high-contrast-active .mat-form-field-infix{border-image:linear-gradient(transparent, transparent)}.mat-form-field-label-wrapper{position:absolute;left:0;box-sizing:content-box;width:100%;height:100%;overflow:hidden;pointer-events:none}[dir=rtl] .mat-form-field-label-wrapper{left:auto;right:0}.mat-form-field-label{position:absolute;left:0;font:inherit;pointer-events:none;width:100%;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;transform-origin:0 0;transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1),color 400ms cubic-bezier(0.25, 0.8, 0.25, 1),width 400ms cubic-bezier(0.25, 0.8, 0.25, 1);display:none}[dir=rtl] .mat-form-field-label{transform-origin:100% 0;left:auto;right:0}.cdk-high-contrast-active .mat-form-field-disabled .mat-form-field-label{color:GrayText}.mat-form-field-empty.mat-form-field-label,.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label{display:block}.mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{display:none}.mat-form-field-can-float .mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{display:block;transition:none}.mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label,.mat-input-server[placeholder]:not(:placeholder-shown)+.mat-form-field-label-wrapper .mat-form-field-label{display:none}.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label,.mat-form-field-can-float .mat-input-server[placeholder]:not(:placeholder-shown)+.mat-form-field-label-wrapper .mat-form-field-label{display:block}.mat-form-field-label:not(.mat-form-field-empty){transition:none}.mat-form-field-underline{position:absolute;width:100%;pointer-events:none;transform:scale3d(1, 1.0001, 1)}.mat-form-field-ripple{position:absolute;left:0;width:100%;transform-origin:50%;transform:scaleX(0.5);opacity:0;transition:background-color 300ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-form-field.mat-focused .mat-form-field-ripple,.mat-form-field.mat-form-field-invalid .mat-form-field-ripple{opacity:1;transform:none;transition:transform 300ms cubic-bezier(0.25, 0.8, 0.25, 1),opacity 100ms cubic-bezier(0.25, 0.8, 0.25, 1),background-color 300ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-subscript-wrapper{position:absolute;box-sizing:border-box;width:100%;overflow:hidden}.mat-form-field-subscript-wrapper .mat-icon,.mat-form-field-label-wrapper .mat-icon{width:1em;height:1em;font-size:inherit;vertical-align:baseline}.mat-form-field-hint-wrapper{display:flex}.mat-form-field-hint-spacer{flex:1 0 1em}.mat-error{display:block}.mat-form-field-control-wrapper{position:relative}.mat-form-field-hint-end{order:1}.mat-form-field._mat-animation-noopable .mat-form-field-label,.mat-form-field._mat-animation-noopable .mat-form-field-ripple{transition:none}\\n\", \".mat-form-field-appearance-fill .mat-form-field-flex{border-radius:4px 4px 0 0;padding:.75em .75em 0 .75em}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-form-field-flex{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-flex{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-form-field-flex{outline:dashed 3px}.mat-form-field-appearance-fill .mat-form-field-underline::before{content:\\\"\\\";display:block;position:absolute;bottom:0;height:1px;width:100%}.mat-form-field-appearance-fill .mat-form-field-ripple{bottom:0;height:2px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-form-field-ripple{height:0}.mat-form-field-appearance-fill:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{opacity:1;transform:none;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-fill._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{transition:none}.mat-form-field-appearance-fill .mat-form-field-subscript-wrapper{padding:0 1em}\\n\", \".mat-input-element{font:inherit;background:transparent;color:currentColor;border:none;outline:none;padding:0;margin:0;width:100%;max-width:100%;vertical-align:bottom;text-align:inherit;box-sizing:content-box}.mat-input-element:-moz-ui-invalid{box-shadow:none}.mat-input-element,.mat-input-element::-webkit-search-cancel-button,.mat-input-element::-webkit-search-decoration,.mat-input-element::-webkit-search-results-button,.mat-input-element::-webkit-search-results-decoration{-webkit-appearance:none}.mat-input-element::-webkit-contacts-auto-fill-button,.mat-input-element::-webkit-caps-lock-indicator,.mat-input-element:not([type=password])::-webkit-credentials-auto-fill-button{visibility:hidden}.mat-input-element[type=date],.mat-input-element[type=datetime],.mat-input-element[type=datetime-local],.mat-input-element[type=month],.mat-input-element[type=week],.mat-input-element[type=time]{line-height:1}.mat-input-element[type=date]::after,.mat-input-element[type=datetime]::after,.mat-input-element[type=datetime-local]::after,.mat-input-element[type=month]::after,.mat-input-element[type=week]::after,.mat-input-element[type=time]::after{content:\\\" \\\";white-space:pre;width:1px}.mat-input-element::-webkit-inner-spin-button,.mat-input-element::-webkit-calendar-picker-indicator,.mat-input-element::-webkit-clear-button{font-size:.75em}.mat-input-element::placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element::-moz-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element::-webkit-input-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element:-ms-input-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-hide-placeholder .mat-input-element::placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element::-moz-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::-moz-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element::-webkit-input-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::-webkit-input-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element:-ms-input-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element:-ms-input-placeholder{opacity:0}textarea.mat-input-element{resize:vertical;overflow:auto}textarea.mat-input-element.cdk-textarea-autosize{resize:none}textarea.mat-input-element{padding:2px 0;margin:-2px 0}select.mat-input-element{-moz-appearance:none;-webkit-appearance:none;position:relative;background-color:transparent;display:inline-flex;box-sizing:border-box;padding-top:1em;top:-1em;margin-bottom:-1em}select.mat-input-element::-moz-focus-inner{border:0}select.mat-input-element:not(:disabled){cursor:pointer}.mat-form-field-type-mat-native-select .mat-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid transparent;border-right:5px solid transparent;border-top:5px solid;position:absolute;top:50%;right:0;margin-top:-2.5px;pointer-events:none}[dir=rtl] .mat-form-field-type-mat-native-select .mat-form-field-infix::after{right:auto;left:0}.mat-form-field-type-mat-native-select .mat-input-element{padding-right:15px}[dir=rtl] .mat-form-field-type-mat-native-select .mat-input-element{padding-right:0;padding-left:15px}.mat-form-field-type-mat-native-select .mat-form-field-label-wrapper{max-width:calc(100% - 10px)}.mat-form-field-type-mat-native-select.mat-form-field-appearance-outline .mat-form-field-infix::after{margin-top:-5px}.mat-form-field-type-mat-native-select.mat-form-field-appearance-fill .mat-form-field-infix::after{margin-top:-10px}\\n\", \".mat-form-field-appearance-legacy .mat-form-field-label{transform:perspective(100px)}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon{width:1em}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon-button,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon-button{font:inherit;vertical-align:baseline}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon-button .mat-icon{font-size:inherit}.mat-form-field-appearance-legacy .mat-form-field-underline{height:1px}.cdk-high-contrast-active .mat-form-field-appearance-legacy .mat-form-field-underline{height:0;border-top:solid 1px}.mat-form-field-appearance-legacy .mat-form-field-ripple{top:0;height:2px;overflow:hidden}.cdk-high-contrast-active .mat-form-field-appearance-legacy .mat-form-field-ripple{height:0;border-top:solid 2px}.mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{background-position:0;background-color:transparent}.cdk-high-contrast-active .mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{border-top-style:dotted;border-top-width:2px;border-top-color:GrayText}.mat-form-field-appearance-legacy.mat-form-field-invalid:not(.mat-focused) .mat-form-field-ripple{height:1px}\\n\", \".mat-form-field-appearance-outline .mat-form-field-wrapper{margin:.25em 0}.mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em 0 .75em;margin-top:-0.25em;position:relative}.mat-form-field-appearance-outline .mat-form-field-prefix,.mat-form-field-appearance-outline .mat-form-field-suffix{top:.25em}.mat-form-field-appearance-outline .mat-form-field-outline{display:flex;position:absolute;top:.25em;left:0;right:0;bottom:0;pointer-events:none}.mat-form-field-appearance-outline .mat-form-field-outline-start,.mat-form-field-appearance-outline .mat-form-field-outline-end{border:1px solid currentColor;min-width:5px}.mat-form-field-appearance-outline .mat-form-field-outline-start{border-radius:5px 0 0 5px;border-right-style:none}[dir=rtl] .mat-form-field-appearance-outline .mat-form-field-outline-start{border-right-style:solid;border-left-style:none;border-radius:0 5px 5px 0}.mat-form-field-appearance-outline .mat-form-field-outline-end{border-radius:0 5px 5px 0;border-left-style:none;flex-grow:1}[dir=rtl] .mat-form-field-appearance-outline .mat-form-field-outline-end{border-left-style:solid;border-right-style:none;border-radius:5px 0 0 5px}.mat-form-field-appearance-outline .mat-form-field-outline-gap{border-radius:.000001px;border:1px solid currentColor;border-left-style:none;border-right-style:none}.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:transparent}.mat-form-field-appearance-outline .mat-form-field-outline-thick{opacity:0}.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-start,.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-end,.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-gap{border-width:2px}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline,.mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline{opacity:0;transition:opacity 100ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick,.mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline-thick{opacity:1}.cdk-high-contrast-active .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick{border:3px dashed}.mat-form-field-appearance-outline:not(.mat-form-field-disabled) .mat-form-field-flex:hover .mat-form-field-outline{opacity:0;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-outline:not(.mat-form-field-disabled) .mat-form-field-flex:hover .mat-form-field-outline-thick{opacity:1}.mat-form-field-appearance-outline .mat-form-field-subscript-wrapper{padding:0 1em}.cdk-high-contrast-active .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline{color:GrayText}.mat-form-field-appearance-outline._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-outline,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-start,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-end,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-gap{transition:none}\\n\", \".mat-form-field-appearance-standard .mat-form-field-flex{padding-top:.75em}.mat-form-field-appearance-standard .mat-form-field-underline{height:1px}.cdk-high-contrast-active .mat-form-field-appearance-standard .mat-form-field-underline{height:0;border-top:solid 1px}.mat-form-field-appearance-standard .mat-form-field-ripple{bottom:0;height:2px}.cdk-high-contrast-active .mat-form-field-appearance-standard .mat-form-field-ripple{height:0;border-top:solid 2px}.mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{background-position:0;background-color:transparent}.cdk-high-contrast-active .mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{border-top-style:dotted;border-top-width:2px}.mat-form-field-appearance-standard:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{opacity:1;transform:none;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-standard._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{transition:none}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [matFormFieldAnimations.transitionMessages]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormField, [{\n    type: Component,\n    args: [{\n      selector: 'mat-form-field',\n      exportAs: 'matFormField',\n      animations: [matFormFieldAnimations.transitionMessages],\n      host: {\n        'class': 'mat-form-field',\n        '[class.mat-form-field-appearance-standard]': 'appearance == \"standard\"',\n        '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n        '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n        '[class.mat-form-field-appearance-legacy]': 'appearance == \"legacy\"',\n        '[class.mat-form-field-invalid]': '_control.errorState',\n        '[class.mat-form-field-can-float]': '_canLabelFloat()',\n        '[class.mat-form-field-should-float]': '_shouldLabelFloat()',\n        '[class.mat-form-field-has-label]': '_hasFloatingLabel()',\n        '[class.mat-form-field-hide-placeholder]': '_hideControlPlaceholder()',\n        '[class.mat-form-field-disabled]': '_control.disabled',\n        '[class.mat-form-field-autofilled]': '_control.autofilled',\n        '[class.mat-focused]': '_control.focused',\n        '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n        '[class.ng-touched]': '_shouldForward(\"touched\")',\n        '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n        '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n        '[class.ng-valid]': '_shouldForward(\"valid\")',\n        '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n        '[class.ng-pending]': '_shouldForward(\"pending\")',\n        '[class._mat-animation-noopable]': '!_animationsEnabled'\n      },\n      inputs: ['color'],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MAT_FORM_FIELD,\n        useExisting: MatFormField\n      }],\n      template: \"<div class=\\\"mat-form-field-wrapper\\\">\\n  <div class=\\\"mat-form-field-flex\\\" #connectionContainer\\n       (click)=\\\"_control.onContainerClick && _control.onContainerClick($event)\\\">\\n\\n    <!-- Outline used for outline appearance. -->\\n    <ng-container *ngIf=\\\"appearance == 'outline'\\\">\\n      <div class=\\\"mat-form-field-outline\\\">\\n        <div class=\\\"mat-form-field-outline-start\\\"></div>\\n        <div class=\\\"mat-form-field-outline-gap\\\"></div>\\n        <div class=\\\"mat-form-field-outline-end\\\"></div>\\n      </div>\\n      <div class=\\\"mat-form-field-outline mat-form-field-outline-thick\\\">\\n        <div class=\\\"mat-form-field-outline-start\\\"></div>\\n        <div class=\\\"mat-form-field-outline-gap\\\"></div>\\n        <div class=\\\"mat-form-field-outline-end\\\"></div>\\n      </div>\\n    </ng-container>\\n\\n    <div\\n      class=\\\"mat-form-field-prefix\\\"\\n      *ngIf=\\\"_prefixChildren.length\\\"\\n      (cdkObserveContent)=\\\"updateOutlineGap()\\\"\\n      [cdkObserveContentDisabled]=\\\"appearance != 'outline'\\\">\\n      <ng-content select=\\\"[matPrefix]\\\"></ng-content>\\n    </div>\\n\\n    <div class=\\\"mat-form-field-infix\\\" #inputContainer>\\n      <ng-content></ng-content>\\n\\n      <span class=\\\"mat-form-field-label-wrapper\\\">\\n        <!-- We add aria-owns as a workaround for an issue in JAWS & NVDA where the label isn't\\n             read if it comes before the control in the DOM. -->\\n        <label class=\\\"mat-form-field-label\\\"\\n               (cdkObserveContent)=\\\"updateOutlineGap()\\\"\\n               [cdkObserveContentDisabled]=\\\"appearance != 'outline'\\\"\\n               [id]=\\\"_labelId\\\"\\n               [attr.for]=\\\"_control.id\\\"\\n               [attr.aria-owns]=\\\"_control.id\\\"\\n               [class.mat-empty]=\\\"_control.empty && !_shouldAlwaysFloat()\\\"\\n               [class.mat-form-field-empty]=\\\"_control.empty && !_shouldAlwaysFloat()\\\"\\n               [class.mat-accent]=\\\"color == 'accent'\\\"\\n               [class.mat-warn]=\\\"color == 'warn'\\\"\\n               #label\\n               *ngIf=\\\"_hasFloatingLabel()\\\"\\n               [ngSwitch]=\\\"_hasLabel()\\\">\\n\\n          <!-- @breaking-change 8.0.0 remove in favor of mat-label element an placeholder attr. -->\\n          <ng-container *ngSwitchCase=\\\"false\\\">\\n            <ng-content select=\\\"mat-placeholder\\\"></ng-content>\\n            <span>{{_control.placeholder}}</span>\\n          </ng-container>\\n\\n          <ng-content select=\\\"mat-label\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n\\n          <!-- @breaking-change 8.0.0 remove `mat-placeholder-required` class -->\\n          <span\\n            class=\\\"mat-placeholder-required mat-form-field-required-marker\\\"\\n            aria-hidden=\\\"true\\\"\\n            *ngIf=\\\"!hideRequiredMarker && _control.required && !_control.disabled\\\">&#32;*</span>\\n        </label>\\n      </span>\\n    </div>\\n\\n    <div class=\\\"mat-form-field-suffix\\\" *ngIf=\\\"_suffixChildren.length\\\">\\n      <ng-content select=\\\"[matSuffix]\\\"></ng-content>\\n    </div>\\n  </div>\\n\\n  <!-- Underline used for legacy, standard, and box appearances. -->\\n  <div class=\\\"mat-form-field-underline\\\"\\n       *ngIf=\\\"appearance != 'outline'\\\">\\n    <span class=\\\"mat-form-field-ripple\\\"\\n          [class.mat-accent]=\\\"color == 'accent'\\\"\\n          [class.mat-warn]=\\\"color == 'warn'\\\"></span>\\n  </div>\\n\\n  <div class=\\\"mat-form-field-subscript-wrapper\\\"\\n       [ngSwitch]=\\\"_getDisplayedMessages()\\\">\\n    <div *ngSwitchCase=\\\"'error'\\\" [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n      <ng-content select=\\\"mat-error\\\"></ng-content>\\n    </div>\\n\\n    <div class=\\\"mat-form-field-hint-wrapper\\\" *ngSwitchCase=\\\"'hint'\\\"\\n      [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n      <!-- TODO(mmalerba): use an actual <mat-hint> once all selectors are switched to mat-* -->\\n      <div *ngIf=\\\"hintLabel\\\" [id]=\\\"_hintLabelId\\\" class=\\\"mat-hint\\\">{{hintLabel}}</div>\\n      <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n      <div class=\\\"mat-form-field-hint-spacer\\\"></div>\\n      <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mat-form-field{display:inline-block;position:relative;text-align:left}[dir=rtl] .mat-form-field{text-align:right}.mat-form-field-wrapper{position:relative}.mat-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-form-field-prefix,.mat-form-field-suffix{white-space:nowrap;flex:none;position:relative}.mat-form-field-infix{display:block;position:relative;flex:auto;min-width:0;width:180px}.cdk-high-contrast-active .mat-form-field-infix{border-image:linear-gradient(transparent, transparent)}.mat-form-field-label-wrapper{position:absolute;left:0;box-sizing:content-box;width:100%;height:100%;overflow:hidden;pointer-events:none}[dir=rtl] .mat-form-field-label-wrapper{left:auto;right:0}.mat-form-field-label{position:absolute;left:0;font:inherit;pointer-events:none;width:100%;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;transform-origin:0 0;transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1),color 400ms cubic-bezier(0.25, 0.8, 0.25, 1),width 400ms cubic-bezier(0.25, 0.8, 0.25, 1);display:none}[dir=rtl] .mat-form-field-label{transform-origin:100% 0;left:auto;right:0}.cdk-high-contrast-active .mat-form-field-disabled .mat-form-field-label{color:GrayText}.mat-form-field-empty.mat-form-field-label,.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label{display:block}.mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{display:none}.mat-form-field-can-float .mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{display:block;transition:none}.mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label,.mat-input-server[placeholder]:not(:placeholder-shown)+.mat-form-field-label-wrapper .mat-form-field-label{display:none}.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label,.mat-form-field-can-float .mat-input-server[placeholder]:not(:placeholder-shown)+.mat-form-field-label-wrapper .mat-form-field-label{display:block}.mat-form-field-label:not(.mat-form-field-empty){transition:none}.mat-form-field-underline{position:absolute;width:100%;pointer-events:none;transform:scale3d(1, 1.0001, 1)}.mat-form-field-ripple{position:absolute;left:0;width:100%;transform-origin:50%;transform:scaleX(0.5);opacity:0;transition:background-color 300ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-form-field.mat-focused .mat-form-field-ripple,.mat-form-field.mat-form-field-invalid .mat-form-field-ripple{opacity:1;transform:none;transition:transform 300ms cubic-bezier(0.25, 0.8, 0.25, 1),opacity 100ms cubic-bezier(0.25, 0.8, 0.25, 1),background-color 300ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-subscript-wrapper{position:absolute;box-sizing:border-box;width:100%;overflow:hidden}.mat-form-field-subscript-wrapper .mat-icon,.mat-form-field-label-wrapper .mat-icon{width:1em;height:1em;font-size:inherit;vertical-align:baseline}.mat-form-field-hint-wrapper{display:flex}.mat-form-field-hint-spacer{flex:1 0 1em}.mat-error{display:block}.mat-form-field-control-wrapper{position:relative}.mat-form-field-hint-end{order:1}.mat-form-field._mat-animation-noopable .mat-form-field-label,.mat-form-field._mat-animation-noopable .mat-form-field-ripple{transition:none}\\n\", \".mat-form-field-appearance-fill .mat-form-field-flex{border-radius:4px 4px 0 0;padding:.75em .75em 0 .75em}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-form-field-flex{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-flex{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-form-field-flex{outline:dashed 3px}.mat-form-field-appearance-fill .mat-form-field-underline::before{content:\\\"\\\";display:block;position:absolute;bottom:0;height:1px;width:100%}.mat-form-field-appearance-fill .mat-form-field-ripple{bottom:0;height:2px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-form-field-ripple{height:0}.mat-form-field-appearance-fill:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{opacity:1;transform:none;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-fill._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{transition:none}.mat-form-field-appearance-fill .mat-form-field-subscript-wrapper{padding:0 1em}\\n\", \".mat-input-element{font:inherit;background:transparent;color:currentColor;border:none;outline:none;padding:0;margin:0;width:100%;max-width:100%;vertical-align:bottom;text-align:inherit;box-sizing:content-box}.mat-input-element:-moz-ui-invalid{box-shadow:none}.mat-input-element,.mat-input-element::-webkit-search-cancel-button,.mat-input-element::-webkit-search-decoration,.mat-input-element::-webkit-search-results-button,.mat-input-element::-webkit-search-results-decoration{-webkit-appearance:none}.mat-input-element::-webkit-contacts-auto-fill-button,.mat-input-element::-webkit-caps-lock-indicator,.mat-input-element:not([type=password])::-webkit-credentials-auto-fill-button{visibility:hidden}.mat-input-element[type=date],.mat-input-element[type=datetime],.mat-input-element[type=datetime-local],.mat-input-element[type=month],.mat-input-element[type=week],.mat-input-element[type=time]{line-height:1}.mat-input-element[type=date]::after,.mat-input-element[type=datetime]::after,.mat-input-element[type=datetime-local]::after,.mat-input-element[type=month]::after,.mat-input-element[type=week]::after,.mat-input-element[type=time]::after{content:\\\" \\\";white-space:pre;width:1px}.mat-input-element::-webkit-inner-spin-button,.mat-input-element::-webkit-calendar-picker-indicator,.mat-input-element::-webkit-clear-button{font-size:.75em}.mat-input-element::placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element::-moz-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element::-webkit-input-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element:-ms-input-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-hide-placeholder .mat-input-element::placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element::-moz-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::-moz-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element::-webkit-input-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::-webkit-input-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element:-ms-input-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element:-ms-input-placeholder{opacity:0}textarea.mat-input-element{resize:vertical;overflow:auto}textarea.mat-input-element.cdk-textarea-autosize{resize:none}textarea.mat-input-element{padding:2px 0;margin:-2px 0}select.mat-input-element{-moz-appearance:none;-webkit-appearance:none;position:relative;background-color:transparent;display:inline-flex;box-sizing:border-box;padding-top:1em;top:-1em;margin-bottom:-1em}select.mat-input-element::-moz-focus-inner{border:0}select.mat-input-element:not(:disabled){cursor:pointer}.mat-form-field-type-mat-native-select .mat-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid transparent;border-right:5px solid transparent;border-top:5px solid;position:absolute;top:50%;right:0;margin-top:-2.5px;pointer-events:none}[dir=rtl] .mat-form-field-type-mat-native-select .mat-form-field-infix::after{right:auto;left:0}.mat-form-field-type-mat-native-select .mat-input-element{padding-right:15px}[dir=rtl] .mat-form-field-type-mat-native-select .mat-input-element{padding-right:0;padding-left:15px}.mat-form-field-type-mat-native-select .mat-form-field-label-wrapper{max-width:calc(100% - 10px)}.mat-form-field-type-mat-native-select.mat-form-field-appearance-outline .mat-form-field-infix::after{margin-top:-5px}.mat-form-field-type-mat-native-select.mat-form-field-appearance-fill .mat-form-field-infix::after{margin-top:-10px}\\n\", \".mat-form-field-appearance-legacy .mat-form-field-label{transform:perspective(100px)}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon{width:1em}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon-button,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon-button{font:inherit;vertical-align:baseline}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon-button .mat-icon{font-size:inherit}.mat-form-field-appearance-legacy .mat-form-field-underline{height:1px}.cdk-high-contrast-active .mat-form-field-appearance-legacy .mat-form-field-underline{height:0;border-top:solid 1px}.mat-form-field-appearance-legacy .mat-form-field-ripple{top:0;height:2px;overflow:hidden}.cdk-high-contrast-active .mat-form-field-appearance-legacy .mat-form-field-ripple{height:0;border-top:solid 2px}.mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{background-position:0;background-color:transparent}.cdk-high-contrast-active .mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{border-top-style:dotted;border-top-width:2px;border-top-color:GrayText}.mat-form-field-appearance-legacy.mat-form-field-invalid:not(.mat-focused) .mat-form-field-ripple{height:1px}\\n\", \".mat-form-field-appearance-outline .mat-form-field-wrapper{margin:.25em 0}.mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em 0 .75em;margin-top:-0.25em;position:relative}.mat-form-field-appearance-outline .mat-form-field-prefix,.mat-form-field-appearance-outline .mat-form-field-suffix{top:.25em}.mat-form-field-appearance-outline .mat-form-field-outline{display:flex;position:absolute;top:.25em;left:0;right:0;bottom:0;pointer-events:none}.mat-form-field-appearance-outline .mat-form-field-outline-start,.mat-form-field-appearance-outline .mat-form-field-outline-end{border:1px solid currentColor;min-width:5px}.mat-form-field-appearance-outline .mat-form-field-outline-start{border-radius:5px 0 0 5px;border-right-style:none}[dir=rtl] .mat-form-field-appearance-outline .mat-form-field-outline-start{border-right-style:solid;border-left-style:none;border-radius:0 5px 5px 0}.mat-form-field-appearance-outline .mat-form-field-outline-end{border-radius:0 5px 5px 0;border-left-style:none;flex-grow:1}[dir=rtl] .mat-form-field-appearance-outline .mat-form-field-outline-end{border-left-style:solid;border-right-style:none;border-radius:5px 0 0 5px}.mat-form-field-appearance-outline .mat-form-field-outline-gap{border-radius:.000001px;border:1px solid currentColor;border-left-style:none;border-right-style:none}.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:transparent}.mat-form-field-appearance-outline .mat-form-field-outline-thick{opacity:0}.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-start,.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-end,.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-gap{border-width:2px}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline,.mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline{opacity:0;transition:opacity 100ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick,.mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline-thick{opacity:1}.cdk-high-contrast-active .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick{border:3px dashed}.mat-form-field-appearance-outline:not(.mat-form-field-disabled) .mat-form-field-flex:hover .mat-form-field-outline{opacity:0;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-outline:not(.mat-form-field-disabled) .mat-form-field-flex:hover .mat-form-field-outline-thick{opacity:1}.mat-form-field-appearance-outline .mat-form-field-subscript-wrapper{padding:0 1em}.cdk-high-contrast-active .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline{color:GrayText}.mat-form-field-appearance-outline._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-outline,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-start,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-end,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-gap{transition:none}\\n\", \".mat-form-field-appearance-standard .mat-form-field-flex{padding-top:.75em}.mat-form-field-appearance-standard .mat-form-field-underline{height:1px}.cdk-high-contrast-active .mat-form-field-appearance-standard .mat-form-field-underline{height:0;border-top:solid 1px}.mat-form-field-appearance-standard .mat-form-field-ripple{bottom:0;height:2px}.cdk-high-contrast-active .mat-form-field-appearance-standard .mat-form-field-ripple{height:0;border-top:solid 2px}.mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{background-position:0;background-color:transparent}.cdk-high-contrast-active .mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{border-top-style:dotted;border-top-width:2px}.mat-form-field-appearance-standard:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{opacity:1;transform:none;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-standard._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{transition:none}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_FORM_FIELD_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i2.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    appearance: [{\n      type: Input\n    }],\n    hideRequiredMarker: [{\n      type: Input\n    }],\n    hintLabel: [{\n      type: Input\n    }],\n    floatLabel: [{\n      type: Input\n    }],\n    _connectionContainerRef: [{\n      type: ViewChild,\n      args: ['connectionContainer', {\n        static: true\n      }]\n    }],\n    _inputContainerRef: [{\n      type: ViewChild,\n      args: ['inputContainer']\n    }],\n    _label: [{\n      type: ViewChild,\n      args: ['label']\n    }],\n    _controlNonStatic: [{\n      type: ContentChild,\n      args: [MatFormFieldControl]\n    }],\n    _controlStatic: [{\n      type: ContentChild,\n      args: [MatFormFieldControl, {\n        static: true\n      }]\n    }],\n    _labelChildNonStatic: [{\n      type: ContentChild,\n      args: [MatLabel]\n    }],\n    _labelChildStatic: [{\n      type: ContentChild,\n      args: [MatLabel, {\n        static: true\n      }]\n    }],\n    _placeholderChild: [{\n      type: ContentChild,\n      args: [MatPlaceholder]\n    }],\n    _errorChildren: [{\n      type: ContentChildren,\n      args: [MAT_ERROR, {\n        descendants: true\n      }]\n    }],\n    _hintChildren: [{\n      type: ContentChildren,\n      args: [_MAT_HINT, {\n        descendants: true\n      }]\n    }],\n    _prefixChildren: [{\n      type: ContentChildren,\n      args: [MAT_PREFIX, {\n        descendants: true\n      }]\n    }],\n    _suffixChildren: [{\n      type: ContentChildren,\n      args: [MAT_SUFFIX, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatFormFieldModule {}\n\nMatFormFieldModule.ɵfac = function MatFormFieldModule_Factory(t) {\n  return new (t || MatFormFieldModule)();\n};\n\nMatFormFieldModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatFormFieldModule\n});\nMatFormFieldModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule, MatCommonModule, ObserversModule], MatCommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatFormFieldModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [MatError, MatFormField, MatHint, MatLabel, MatPlaceholder, MatPrefix, MatSuffix],\n      imports: [CommonModule, MatCommonModule, ObserversModule],\n      exports: [MatCommonModule, MatError, MatFormField, MatHint, MatLabel, MatPlaceholder, MatPrefix, MatSuffix]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_ERROR, MAT_FORM_FIELD, MAT_FORM_FIELD_DEFAULT_OPTIONS, MAT_PREFIX, MAT_SUFFIX, MatError, MatFormField, MatFormFieldControl, MatFormFieldModule, MatHint, MatLabel, MatPlaceholder, MatPrefix, MatSuffix, _MAT_HINT, getMatFormFieldDuplicatedHintError, getMatFormFieldMissingControlError, getMatFormFieldPlaceholderConflictError, matFormFieldAnimations };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/material/fesm2020/form-field.mjs"], "names": ["i4", "ObserversModule", "i3", "CommonModule", "i0", "InjectionToken", "Directive", "Attribute", "Input", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "ViewChild", "ContentChild", "ContentChildren", "NgModule", "mixinColor", "MatCommonModule", "i1", "coerceBooleanProperty", "Subject", "merge", "fromEvent", "startWith", "takeUntil", "take", "trigger", "state", "style", "transition", "animate", "i2", "ANIMATION_MODULE_TYPE", "nextUniqueId$2", "MAT_ERROR", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "ariaLive", "elementRef", "id", "nativeElement", "setAttribute", "ɵfac", "ElementRef", "ɵdir", "provide", "useExisting", "type", "args", "selector", "host", "providers", "undefined", "decorators", "matFormFieldAnimations", "transitionMessages", "opacity", "transform", "MatFormFieldControl", "getMatFormFieldPlaceholderConflictError", "Error", "getMatFormFieldDuplicatedHintError", "align", "getMatFormFieldMissingControlError", "nextUniqueId$1", "_MAT_HINT", "MatHint", "<PERSON><PERSON><PERSON><PERSON>", "MatPlaceholder", "MAT_PREFIX", "MatPrefix", "MAT_SUFFIX", "MatSuffix", "nextUniqueId", "floatingLabelScale", "outlineGapPadding", "_MatFormFieldBase", "_elementRef", "MAT_FORM_FIELD_DEFAULT_OPTIONS", "MAT_FORM_FIELD", "MatFormField", "_changeDetectorRef", "_dir", "_defaults", "_platform", "_ngZone", "_animationMode", "_outlineGapCalculationNeededImmediately", "_outlineGapCalculationNeededOnStable", "_destroyed", "_showAlwaysAnimate", "_subscriptAnimationState", "_<PERSON><PERSON><PERSON>l", "_hintLabelId", "_labelId", "floatLabel", "_getDefaultFloatLabelState", "_animationsEnabled", "appearance", "_hideRequiredMarker", "hideRequiredMarker", "_appearance", "value", "oldValue", "_shouldAlwaysFloat", "_canLabelFloat", "hintLabel", "_processHints", "_floatLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_control", "_explicitFormFieldControl", "_controlNonStatic", "_controlStatic", "getLabelId", "_hasFloatingLabel", "getConnectedOverlayOrigin", "_connectionContainerRef", "ngAfterContentInit", "_validateControlChild", "control", "controlType", "classList", "add", "stateChanges", "pipe", "subscribe", "_validatePlaceholders", "_syncDescribedByIds", "ngControl", "valueChanges", "runOutsideAngular", "onStable", "updateOutlineGap", "_prefixChildren", "changes", "_suffixC<PERSON><PERSON>n", "_hint<PERSON><PERSON><PERSON>n", "_errorC<PERSON><PERSON>n", "change", "requestAnimationFrame", "ngAfterContentChecked", "ngAfterViewInit", "detectChanges", "ngOnDestroy", "next", "complete", "_shouldForward", "prop", "_hasPlaceholder", "placeholder", "_placeholder<PERSON><PERSON>d", "_has<PERSON><PERSON>l", "_labelChildNonStatic", "_labelChildStatic", "_shouldLabelFloat", "shouldLabelFloat", "_hideControlPlaceholder", "_getDisplayedMessages", "length", "errorState", "_animateAndLockLabel", "_label", "ngDevMode", "_validateHints", "startHint", "endHint", "for<PERSON>ach", "hint", "ids", "userAriaDescribedBy", "push", "split", "find", "map", "error", "setDescribedByIds", "labelEl", "container", "outlineStartSelector", "outlineGapSelector", "<PERSON><PERSON><PERSON><PERSON>", "children", "textContent", "trim", "gapElements", "querySelectorAll", "i", "width", "_isAttachedToDOM", "startWidth", "gapWidth", "startEls", "gapEls", "containerRect", "getBoundingClientRect", "height", "containerStart", "_getStartEnd", "labelChildren", "labelStart", "labelWidth", "offsetWidth", "Math", "abs", "rect", "right", "left", "element", "getRootNode", "rootNode", "document", "documentElement", "contains", "ChangeDetectorRef", "Directionality", "Platform", "NgZone", "ɵcmp", "NgIf", "NgSwitch", "NgSwitchCase", "CdkObserveContent", "exportAs", "animations", "inputs", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "static", "_inputContainerRef", "descendants", "MatFormFieldModule", "ɵmod", "ɵinj", "declarations", "imports", "exports"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,wBAApB;AACA,SAASC,eAAT,QAAgC,wBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,SAApC,EAA+CC,KAA/C,EAAsDC,SAAtD,EAAiEC,iBAAjE,EAAoFC,uBAApF,EAA6GC,QAA7G,EAAuHC,MAAvH,EAA+HC,SAA/H,EAA0IC,YAA1I,EAAwJC,eAAxJ,EAAyKC,QAAzK,QAAyL,eAAzL;AACA,SAASC,UAAT,EAAqBC,eAArB,QAA4C,wBAA5C;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,SAAzB,QAA0C,MAA1C;AACA,SAASC,SAAT,EAAoBC,SAApB,EAA+BC,IAA/B,QAA2C,gBAA3C;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAmB2F9B,IAAAA,EA0nB83F,2B;AA1nB93FA,IAAAA,EA0nBs7F,6B;AA1nBt7FA,IAAAA,EA0nBs+F,wB;AA1nBt+FA,IAAAA,EA0nBkiG,wB;AA1nBliGA,IAAAA,EA0nB4lG,wB;AA1nB5lGA,IAAAA,EA0nBopG,e;AA1nBppGA,IAAAA,EA0nBkqG,6B;AA1nBlqGA,IAAAA,EA0nB+uG,wB;AA1nB/uGA,IAAAA,EA0nB2yG,wB;AA1nB3yGA,IAAAA,EA0nBq2G,wB;AA1nBr2GA,IAAAA,EA0nB65G,e;AA1nB75GA,IAAAA,EA0nBy6G,wB;;;;;;iBA1nBz6GA,E;;AAAAA,IAAAA,EA0nBg8G,6B;AA1nBh8GA,IAAAA,EA0nB2hH;AA1nB3hHA,MAAAA,EA0nB2hH;AAAA,qBA1nB3hHA,EA0nB2hH;AAAA,aAAsB,yBAAtB;AAAA,M;AA1nB3hHA,IAAAA,EA0nB6oH,mB;AA1nB7oHA,IAAAA,EA0nBmsH,e;;;;mBA1nBnsHA,E;AAAAA,IAAAA,EA0nB6kH,wE;;;;;;AA1nB7kHA,IAAAA,EA0nByyJ,2B;AA1nBzyJA,IAAAA,EA0nB61J,mB;AA1nB71JA,IAAAA,EA0nB+5J,0B;AA1nB/5JA,IAAAA,EA0nBq6J,U;AA1nBr6JA,IAAAA,EA0nB67J,e;AA1nB77JA,IAAAA,EA0nBg9J,wB;;;;oBA1nBh9JA,E;AAAAA,IAAAA,EA0nBq6J,a;AA1nBr6JA,IAAAA,EA0nBq6J,gD;;;;;;AA1nBr6JA,IAAAA,EA0nB6+J,8C;;;;;;AA1nB7+JA,IAAAA,EA0nBmpK,8B;AA1nBnpKA,IAAAA,EA0nBg2K,gB;AA1nBh2KA,IAAAA,EA0nBs2K,e;;;;;;iBA1nBt2KA,E;;AAAAA,IAAAA,EA0nB0gI,mC;AA1nB1gIA,IAAAA,EA0nBgkI;AA1nBhkIA,MAAAA,EA0nBgkI;AAAA,sBA1nBhkIA,EA0nBgkI;AAAA,aAAsB,0BAAtB;AAAA,M;AA1nBhkIA,IAAAA,EA0nByyJ,sF;AA1nBzyJA,IAAAA,EA0nB6+J,kF;AA1nB7+JA,IAAAA,EA0nBmpK,sE;AA1nBnpKA,IAAAA,EA0nBu3K,e;;;;mBA1nBv3KA,E;AAAAA,IAAAA,EA0nBi0I,2O;AA1nBj0IA,IAAAA,EA0nB2nI,+H;AA1nB3nIA,IAAAA,EA0nBquI,wE;AA1nBruIA,IAAAA,EA0nBwzJ,a;AA1nBxzJA,IAAAA,EA0nBwzJ,kC;AA1nBxzJA,IAAAA,EA0nB+gK,a;AA1nB/gKA,IAAAA,EA0nB+gK,iC;AA1nB/gKA,IAAAA,EA0nBwxK,a;AA1nBxxKA,IAAAA,EA0nBwxK,wG;;;;;;AA1nBxxKA,IAAAA,EA0nBk6K,6B;AA1nBl6KA,IAAAA,EA0nBg/K,mB;AA1nBh/KA,IAAAA,EA0nBsiL,e;;;;;;AA1nBtiLA,IAAAA,EA0nBkoL,6B;AA1nBloLA,IAAAA,EA0nB0tL,yB;AA1nB1tLA,IAAAA,EA0nB+2L,e;;;;mBA1nB/2LA,E;AAAAA,IAAAA,EA0nB2wL,a;AA1nB3wLA,IAAAA,EA0nB2wL,wF;;;;;;AA1nB3wLA,IAAAA,EA0nBg+L,yB;AA1nBh+LA,IAAAA,EA0nB0jM,mB;AA1nB1jMA,IAAAA,EA0nB8mM,e;;;;mBA1nB9mMA,E;AAAAA,IAAAA,EA0nB+/L,mE;;;;;;AA1nB//LA,IAAAA,EA0nBo2M,6B;AA1nBp2MA,IAAAA,EA0nBs6M,U;AA1nBt6MA,IAAAA,EA0nBm7M,e;;;;oBA1nBn7MA,E;AAAAA,IAAAA,EA0nB63M,uC;AA1nB73MA,IAAAA,EA0nBs6M,a;AA1nBt6MA,IAAAA,EA0nBs6M,qC;;;;;;AA1nBt6MA,IAAAA,EA0nB4nM,6B;AA1nB5nMA,IAAAA,EA0nBo2M,mE;AA1nBp2MA,IAAAA,EA0nBi8M,mB;AA1nBj8MA,IAAAA,EA0nBygN,wB;AA1nBzgNA,IAAAA,EA0nBikN,mB;AA1nBjkNA,IAAAA,EA0nBioN,e;;;;mBA1nBjoNA,E;AAAAA,IAAAA,EA0nBusM,mE;AA1nBvsMA,IAAAA,EA0nB02M,a;AA1nB12MA,IAAAA,EA0nB02M,qC;;;;;;AA5oBr8M,IAAI+B,cAAc,GAAG,CAArB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,SAAS,GAAG,IAAI/B,cAAJ,CAAmB,UAAnB,CAAlB;AACA;;AACA,MAAMgC,QAAN,CAAe;AACXC,EAAAA,WAAW,CAACC,QAAD,EAAWC,UAAX,EAAuB;AAC9B,SAAKC,EAAL,GAAW,aAAYN,cAAc,EAAG,EAAxC,CAD8B,CAE9B;AACA;;AACA,QAAI,CAACI,QAAL,EAAe;AACXC,MAAAA,UAAU,CAACE,aAAX,CAAyBC,YAAzB,CAAsC,WAAtC,EAAmD,QAAnD;AACH;AACJ;;AARU;;AAUfN,QAAQ,CAACO,IAAT;AAAA,mBAAqGP,QAArG,EAA2FjC,EAA3F,mBAA+H,WAA/H,GAA2FA,EAA3F,mBAAwKA,EAAE,CAACyC,UAA3K;AAAA;;AACAR,QAAQ,CAACS,IAAT,kBAD2F1C,EAC3F;AAAA,QAAyFiC,QAAzF;AAAA;AAAA,6BAAqL,MAArL;AAAA;AAAA;AAAA;AAD2FjC,MAAAA,EAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAD2FA,EAC3F,oBAA0Q,CAAC;AAAE2C,IAAAA,OAAO,EAAEX,SAAX;AAAsBY,IAAAA,WAAW,EAAEX;AAAnC,GAAD,CAA1Q;AAAA;;AACA;AAAA,qDAF2FjC,EAE3F,mBAA2FiC,QAA3F,EAAiH,CAAC;AACtGY,IAAAA,IAAI,EAAE3C,SADgG;AAEtG4C,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,WADX;AAECC,MAAAA,IAAI,EAAE;AACF,iBAAS,WADP;AAEF,qBAAa,IAFX;AAGF,uBAAe;AAHb,OAFP;AAOCC,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAEX,SAAX;AAAsBY,QAAAA,WAAW,EAAEX;AAAnC,OAAD;AAPZ,KAAD;AAFgG,GAAD,CAAjH,EAW4B,YAAY;AAAE,WAAO,CAAC;AAAEY,MAAAA,IAAI,EAAEK,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DN,QAAAA,IAAI,EAAE1C,SADwD;AAE9D2C,QAAAA,IAAI,EAAE,CAAC,WAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAED,MAAAA,IAAI,EAAE7C,EAAE,CAACyC;AAAX,KAH2B,CAAP;AAGO,GAdjD,EAcmE;AAAEJ,IAAAA,EAAE,EAAE,CAAC;AAC1DQ,MAAAA,IAAI,EAAEzC;AADoD,KAAD;AAAN,GAdnE;AAAA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMgD,sBAAsB,GAAG;AAC3B;AACAC,EAAAA,kBAAkB,EAAE7B,OAAO,CAAC,oBAAD,EAAuB,CAC9C;AACAC,EAAAA,KAAK,CAAC,OAAD,EAAUC,KAAK,CAAC;AAAE4B,IAAAA,OAAO,EAAE,CAAX;AAAcC,IAAAA,SAAS,EAAE;AAAzB,GAAD,CAAf,CAFyC,EAG9C5B,UAAU,CAAC,eAAD,EAAkB,CACxBD,KAAK,CAAC;AAAE4B,IAAAA,OAAO,EAAE,CAAX;AAAcC,IAAAA,SAAS,EAAE;AAAzB,GAAD,CADmB,EAExB3B,OAAO,CAAC,wCAAD,CAFiB,CAAlB,CAHoC,CAAvB;AAFA,CAA/B;AAYA;;AACA,MAAM4B,mBAAN,CAA0B;;AAE1BA,mBAAmB,CAAChB,IAApB;AAAA,mBAAgHgB,mBAAhH;AAAA;;AACAA,mBAAmB,CAACd,IAApB,kBA/C2F1C,EA+C3F;AAAA,QAAoGwD;AAApG;;AACA;AAAA,qDAhD2FxD,EAgD3F,mBAA2FwD,mBAA3F,EAA4H,CAAC;AACjHX,IAAAA,IAAI,EAAE3C;AAD2G,GAAD,CAA5H;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASuD,uCAAT,GAAmD;AAC/C,SAAOC,KAAK,CAAC,8DAAD,CAAZ;AACH;AACD;;;AACA,SAASC,kCAAT,CAA4CC,KAA5C,EAAmD;AAC/C,SAAOF,KAAK,CAAE,2CAA0CE,KAAM,KAAlD,CAAZ;AACH;AACD;;;AACA,SAASC,kCAAT,GAA8C;AAC1C,SAAOH,KAAK,CAAC,oDAAD,CAAZ;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAII,cAAc,GAAG,CAArB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,SAAS,GAAG,IAAI9D,cAAJ,CAAmB,SAAnB,CAAlB;AACA;;;AACA,MAAM+D,OAAN,CAAc;AACV9B,EAAAA,WAAW,GAAG;AACV;AACA,SAAK0B,KAAL,GAAa,OAAb;AACA;;AACA,SAAKvB,EAAL,GAAW,YAAWyB,cAAc,EAAG,EAAvC;AACH;;AANS;;AAQdE,OAAO,CAACxB,IAAR;AAAA,mBAAoGwB,OAApG;AAAA;;AACAA,OAAO,CAACtB,IAAR,kBAnG2F1C,EAmG3F;AAAA,QAAwFgE,OAAxF;AAAA;AAAA;AAAA;AAAA;AAAA;AAnG2FhE,MAAAA,EAmG3F;AAnG2FA,MAAAA,EAmG3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAnG2FA,EAmG3F,oBAA2T,CAAC;AAAE2C,IAAAA,OAAO,EAAEoB,SAAX;AAAsBnB,IAAAA,WAAW,EAAEoB;AAAnC,GAAD,CAA3T;AAAA;;AACA;AAAA,qDApG2FhE,EAoG3F,mBAA2FgE,OAA3F,EAAgH,CAAC;AACrGnB,IAAAA,IAAI,EAAE3C,SAD+F;AAErG4C,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,UADX;AAECC,MAAAA,IAAI,EAAE;AACF,iBAAS,UADP;AAEF,2CAAmC,iBAFjC;AAGF,qBAAa,IAHX;AAIF;AACA,wBAAgB;AALd,OAFP;AASCC,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAEoB,SAAX;AAAsBnB,QAAAA,WAAW,EAAEoB;AAAnC,OAAD;AATZ,KAAD;AAF+F,GAAD,CAAhH,QAa4B;AAAEJ,IAAAA,KAAK,EAAE,CAAC;AACtBf,MAAAA,IAAI,EAAEzC;AADgB,KAAD,CAAT;AAEZiC,IAAAA,EAAE,EAAE,CAAC;AACLQ,MAAAA,IAAI,EAAEzC;AADD,KAAD;AAFQ,GAb5B;AAAA;AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM6D,QAAN,CAAe;;AAEfA,QAAQ,CAACzB,IAAT;AAAA,mBAAqGyB,QAArG;AAAA;;AACAA,QAAQ,CAACvB,IAAT,kBAlI2F1C,EAkI3F;AAAA,QAAyFiE,QAAzF;AAAA;AAAA;;AACA;AAAA,qDAnI2FjE,EAmI3F,mBAA2FiE,QAA3F,EAAiH,CAAC;AACtGpB,IAAAA,IAAI,EAAE3C,SADgG;AAEtG4C,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE;AADX,KAAD;AAFgG,GAAD,CAAjH;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmB,cAAN,CAAqB;;AAErBA,cAAc,CAAC1B,IAAf;AAAA,mBAA2G0B,cAA3G;AAAA;;AACAA,cAAc,CAACxB,IAAf,kBA1J2F1C,EA0J3F;AAAA,QAA+FkE,cAA/F;AAAA;AAAA;;AACA;AAAA,qDA3J2FlE,EA2J3F,mBAA2FkE,cAA3F,EAAuH,CAAC;AAC5GrB,IAAAA,IAAI,EAAE3C,SADsG;AAE5G4C,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE;AADX,KAAD;AAFsG,GAAD,CAAvH;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoB,UAAU,GAAG,IAAIlE,cAAJ,CAAmB,WAAnB,CAAnB;AACA;;AACA,MAAMmE,SAAN,CAAgB;;AAEhBA,SAAS,CAAC5B,IAAV;AAAA,mBAAsG4B,SAAtG;AAAA;;AACAA,SAAS,CAAC1B,IAAV,kBAnL2F1C,EAmL3F;AAAA,QAA0FoE,SAA1F;AAAA;AAAA,aAnL2FpE,EAmL3F,oBAAyI,CAAC;AAAE2C,IAAAA,OAAO,EAAEwB,UAAX;AAAuBvB,IAAAA,WAAW,EAAEwB;AAApC,GAAD,CAAzI;AAAA;;AACA;AAAA,qDApL2FpE,EAoL3F,mBAA2FoE,SAA3F,EAAkH,CAAC;AACvGvB,IAAAA,IAAI,EAAE3C,SADiG;AAEvG4C,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,aADX;AAECE,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAEwB,UAAX;AAAuBvB,QAAAA,WAAW,EAAEwB;AAApC,OAAD;AAFZ,KAAD;AAFiG,GAAD,CAAlH;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,UAAU,GAAG,IAAIpE,cAAJ,CAAmB,WAAnB,CAAnB;AACA;;AACA,MAAMqE,SAAN,CAAgB;;AAEhBA,SAAS,CAAC9B,IAAV;AAAA,mBAAsG8B,SAAtG;AAAA;;AACAA,SAAS,CAAC5B,IAAV,kBA7M2F1C,EA6M3F;AAAA,QAA0FsE,SAA1F;AAAA;AAAA,aA7M2FtE,EA6M3F,oBAAyI,CAAC;AAAE2C,IAAAA,OAAO,EAAE0B,UAAX;AAAuBzB,IAAAA,WAAW,EAAE0B;AAApC,GAAD,CAAzI;AAAA;;AACA;AAAA,qDA9M2FtE,EA8M3F,mBAA2FsE,SAA3F,EAAkH,CAAC;AACvGzB,IAAAA,IAAI,EAAE3C,SADiG;AAEvG4C,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,aADX;AAECE,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAE0B,UAAX;AAAuBzB,QAAAA,WAAW,EAAE0B;AAApC,OAAD;AAFZ,KAAD;AAFiG,GAAD,CAAlH;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIC,YAAY,GAAG,CAAnB;AACA,MAAMC,kBAAkB,GAAG,IAA3B;AACA,MAAMC,iBAAiB,GAAG,CAA1B;AACA;AACA;AACA;AACA;;AACA,MAAMC,iBAAiB,GAAG5D,UAAU,CAAC,MAAM;AACvCoB,EAAAA,WAAW,CAACyC,WAAD,EAAc;AACrB,SAAKA,WAAL,GAAmBA,WAAnB;AACH;;AAHsC,CAAP,EAIjC,SAJiC,CAApC;AAKA;AACA;AACA;AACA;;;AACA,MAAMC,8BAA8B,GAAG,IAAI3E,cAAJ,CAAmB,gCAAnB,CAAvC;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM4E,cAAc,GAAG,IAAI5E,cAAJ,CAAmB,cAAnB,CAAvB;AACA;;AACA,MAAM6E,YAAN,SAA2BJ,iBAA3B,CAA6C;AACzCxC,EAAAA,WAAW,CAACE,UAAD,EAAa2C,kBAAb,EAAiCC,IAAjC,EAAuCC,SAAvC,EAAkDC,SAAlD,EAA6DC,OAA7D,EAAsEC,cAAtE,EAAsF;AAC7F,UAAMhD,UAAN;AACA,SAAK2C,kBAAL,GAA0BA,kBAA1B;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA;AACR;AACA;AACA;;AACQ,SAAKE,uCAAL,GAA+C,KAA/C;AACA;;AACA,SAAKC,oCAAL,GAA4C,KAA5C;AACA,SAAKC,UAAL,GAAkB,IAAIrE,OAAJ,EAAlB;AACA;;AACA,SAAKsE,kBAAL,GAA0B,KAA1B;AACA;;AACA,SAAKC,wBAAL,GAAgC,EAAhC;AACA,SAAKC,UAAL,GAAkB,EAAlB,CAnB6F,CAoB7F;;AACA,SAAKC,YAAL,GAAqB,YAAWpB,YAAY,EAAG,EAA/C,CArB6F,CAsB7F;;AACA,SAAKqB,QAAL,GAAiB,wBAAuBrB,YAAY,EAAG,EAAvD;AACA,SAAKsB,UAAL,GAAkB,KAAKC,0BAAL,EAAlB;AACA,SAAKC,kBAAL,GAA0BX,cAAc,KAAK,gBAA7C,CAzB6F,CA0B7F;;AACA,SAAKY,UAAL,GAAkBf,SAAS,IAAIA,SAAS,CAACe,UAAvB,GAAoCf,SAAS,CAACe,UAA9C,GAA2D,QAA7E;AACA,SAAKC,mBAAL,GACIhB,SAAS,IAAIA,SAAS,CAACiB,kBAAV,IAAgC,IAA7C,GAAoDjB,SAAS,CAACiB,kBAA9D,GAAmF,KADvF;AAEH;AACD;;;AACc,MAAVF,UAAU,GAAG;AACb,WAAO,KAAKG,WAAZ;AACH;;AACa,MAAVH,UAAU,CAACI,KAAD,EAAQ;AAClB,UAAMC,QAAQ,GAAG,KAAKF,WAAtB;AACA,SAAKA,WAAL,GAAmBC,KAAK,IAAK,KAAKnB,SAAL,IAAkB,KAAKA,SAAL,CAAee,UAA3C,IAA0D,QAA7E;;AACA,QAAI,KAAKG,WAAL,KAAqB,SAArB,IAAkCE,QAAQ,KAAKD,KAAnD,EAA0D;AACtD,WAAKd,oCAAL,GAA4C,IAA5C;AACH;AACJ;AACD;;;AACsB,MAAlBY,kBAAkB,GAAG;AACrB,WAAO,KAAKD,mBAAZ;AACH;;AACqB,MAAlBC,kBAAkB,CAACE,KAAD,EAAQ;AAC1B,SAAKH,mBAAL,GAA2BhF,qBAAqB,CAACmF,KAAD,CAAhD;AACH;AACD;;;AACAE,EAAAA,kBAAkB,GAAG;AACjB,WAAO,KAAKT,UAAL,KAAoB,QAApB,IAAgC,CAAC,KAAKL,kBAA7C;AACH;AACD;;;AACAe,EAAAA,cAAc,GAAG;AACb,WAAO,KAAKV,UAAL,KAAoB,OAA3B;AACH;AACD;;;AACa,MAATW,SAAS,GAAG;AACZ,WAAO,KAAKd,UAAZ;AACH;;AACY,MAATc,SAAS,CAACJ,KAAD,EAAQ;AACjB,SAAKV,UAAL,GAAkBU,KAAlB;;AACA,SAAKK,aAAL;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACkB,MAAVZ,UAAU,GAAG;AACb,WAAO,KAAKG,UAAL,KAAoB,QAApB,IAAgC,KAAKU,WAAL,KAAqB,OAArD,GAA+D,MAA/D,GAAwE,KAAKA,WAApF;AACH;;AACa,MAAVb,UAAU,CAACO,KAAD,EAAQ;AAClB,QAAIA,KAAK,KAAK,KAAKM,WAAnB,EAAgC;AAC5B,WAAKA,WAAL,GAAmBN,KAAK,IAAI,KAAKN,0BAAL,EAA5B;;AACA,WAAKf,kBAAL,CAAwB4B,YAAxB;AACH;AACJ;;AACW,MAARC,QAAQ,GAAG;AACX;AACA;AACA,WAAO,KAAKC,yBAAL,IAAkC,KAAKC,iBAAvC,IAA4D,KAAKC,cAAxE;AACH;;AACW,MAARH,QAAQ,CAACR,KAAD,EAAQ;AAChB,SAAKS,yBAAL,GAAiCT,KAAjC;AACH;AACD;AACJ;AACA;;;AACIY,EAAAA,UAAU,GAAG;AACT,WAAO,KAAKC,iBAAL,KAA2B,KAAKrB,QAAhC,GAA2C,IAAlD;AACH;AACD;AACJ;AACA;AACA;;;AACIsB,EAAAA,yBAAyB,GAAG;AACxB,WAAO,KAAKC,uBAAL,IAAgC,KAAKxC,WAA5C;AACH;;AACDyC,EAAAA,kBAAkB,GAAG;AACjB,SAAKC,qBAAL;;AACA,UAAMC,OAAO,GAAG,KAAKV,QAArB;;AACA,QAAIU,OAAO,CAACC,WAAZ,EAAyB;AACrB,WAAK5C,WAAL,CAAiBrC,aAAjB,CAA+BkF,SAA/B,CAAyCC,GAAzC,CAA8C,uBAAsBH,OAAO,CAACC,WAAY,EAAxF;AACH,KALgB,CAMjB;;;AACAD,IAAAA,OAAO,CAACI,YAAR,CAAqBC,IAArB,CAA0BtG,SAAS,CAAC,IAAD,CAAnC,EAA2CuG,SAA3C,CAAqD,MAAM;AACvD,WAAKC,qBAAL;;AACA,WAAKC,mBAAL;;AACA,WAAK/C,kBAAL,CAAwB4B,YAAxB;AACH,KAJD,EAPiB,CAYjB;;AACA,QAAIW,OAAO,CAACS,SAAR,IAAqBT,OAAO,CAACS,SAAR,CAAkBC,YAA3C,EAAyD;AACrDV,MAAAA,OAAO,CAACS,SAAR,CAAkBC,YAAlB,CACKL,IADL,CACUrG,SAAS,CAAC,KAAKiE,UAAN,CADnB,EAEKqC,SAFL,CAEe,MAAM,KAAK7C,kBAAL,CAAwB4B,YAAxB,EAFrB;AAGH,KAjBgB,CAkBjB;AACA;AACA;;;AACA,SAAKxB,OAAL,CAAa8C,iBAAb,CAA+B,MAAM;AACjC,WAAK9C,OAAL,CAAa+C,QAAb,CAAsBP,IAAtB,CAA2BrG,SAAS,CAAC,KAAKiE,UAAN,CAApC,EAAuDqC,SAAvD,CAAiE,MAAM;AACnE,YAAI,KAAKtC,oCAAT,EAA+C;AAC3C,eAAK6C,gBAAL;AACH;AACJ,OAJD;AAKH,KAND,EArBiB,CA4BjB;;;AACAhH,IAAAA,KAAK,CAAC,KAAKiH,eAAL,CAAqBC,OAAtB,EAA+B,KAAKC,eAAL,CAAqBD,OAApD,CAAL,CAAkET,SAAlE,CAA4E,MAAM;AAC9E,WAAKtC,oCAAL,GAA4C,IAA5C;;AACA,WAAKP,kBAAL,CAAwB4B,YAAxB;AACH,KAHD,EA7BiB,CAiCjB;;AACA,SAAK4B,aAAL,CAAmBF,OAAnB,CAA2BV,IAA3B,CAAgCtG,SAAS,CAAC,IAAD,CAAzC,EAAiDuG,SAAjD,CAA2D,MAAM;AAC7D,WAAKnB,aAAL;;AACA,WAAK1B,kBAAL,CAAwB4B,YAAxB;AACH,KAHD,EAlCiB,CAsCjB;;;AACA,SAAK6B,cAAL,CAAoBH,OAApB,CAA4BV,IAA5B,CAAiCtG,SAAS,CAAC,IAAD,CAA1C,EAAkDuG,SAAlD,CAA4D,MAAM;AAC9D,WAAKE,mBAAL;;AACA,WAAK/C,kBAAL,CAAwB4B,YAAxB;AACH,KAHD;;AAIA,QAAI,KAAK3B,IAAT,EAAe;AACX,WAAKA,IAAL,CAAUyD,MAAV,CAAiBd,IAAjB,CAAsBrG,SAAS,CAAC,KAAKiE,UAAN,CAA/B,EAAkDqC,SAAlD,CAA4D,MAAM;AAC9D,YAAI,OAAOc,qBAAP,KAAiC,UAArC,EAAiD;AAC7C,eAAKvD,OAAL,CAAa8C,iBAAb,CAA+B,MAAM;AACjCS,YAAAA,qBAAqB,CAAC,MAAM,KAAKP,gBAAL,EAAP,CAArB;AACH,WAFD;AAGH,SAJD,MAKK;AACD,eAAKA,gBAAL;AACH;AACJ,OATD;AAUH;AACJ;;AACDQ,EAAAA,qBAAqB,GAAG;AACpB,SAAKtB,qBAAL;;AACA,QAAI,KAAKhC,uCAAT,EAAkD;AAC9C,WAAK8C,gBAAL;AACH;AACJ;;AACDS,EAAAA,eAAe,GAAG;AACd;AACA,SAAKnD,wBAAL,GAAgC,OAAhC;;AACA,SAAKV,kBAAL,CAAwB8D,aAAxB;AACH;;AACDC,EAAAA,WAAW,GAAG;AACV,SAAKvD,UAAL,CAAgBwD,IAAhB;;AACA,SAAKxD,UAAL,CAAgByD,QAAhB;AACH;AACD;;;AACAC,EAAAA,cAAc,CAACC,IAAD,EAAO;AACjB,UAAMnB,SAAS,GAAG,KAAKnB,QAAL,GAAgB,KAAKA,QAAL,CAAcmB,SAA9B,GAA0C,IAA5D;AACA,WAAOA,SAAS,IAAIA,SAAS,CAACmB,IAAD,CAA7B;AACH;;AACDC,EAAAA,eAAe,GAAG;AACd,WAAO,CAAC,EAAG,KAAKvC,QAAL,IAAiB,KAAKA,QAAL,CAAcwC,WAAhC,IAAgD,KAAKC,iBAAvD,CAAR;AACH;;AACDC,EAAAA,SAAS,GAAG;AACR,WAAO,CAAC,EAAE,KAAKC,oBAAL,IAA6B,KAAKC,iBAApC,CAAR;AACH;;AACDC,EAAAA,iBAAiB,GAAG;AAChB,WAAQ,KAAKlD,cAAL,OACF,KAAKK,QAAL,IAAiB,KAAKA,QAAL,CAAc8C,gBAAhC,IAAqD,KAAKpD,kBAAL,EADlD,CAAR;AAEH;;AACDqD,EAAAA,uBAAuB,GAAG;AACtB;AACA,WAAS,KAAK3D,UAAL,KAAoB,QAApB,IAAgC,CAAC,KAAKsD,SAAL,EAAlC,IACH,KAAKA,SAAL,MAAoB,CAAC,KAAKG,iBAAL,EAD1B;AAEH;;AACDxC,EAAAA,iBAAiB,GAAG;AAChB;AACA,WAAO,KAAKqC,SAAL,MAAqB,KAAKtD,UAAL,KAAoB,QAApB,IAAgC,KAAKmD,eAAL,EAA5D;AACH;AACD;;;AACAS,EAAAA,qBAAqB,GAAG;AACpB,WAAO,KAAKpB,cAAL,IAAuB,KAAKA,cAAL,CAAoBqB,MAApB,GAA6B,CAApD,IAAyD,KAAKjD,QAAL,CAAckD,UAAvE,GACD,OADC,GAED,MAFN;AAGH;AACD;;;AACAC,EAAAA,oBAAoB,GAAG;AACnB,QAAI,KAAK9C,iBAAL,MAA4B,KAAKV,cAAL,EAAhC,EAAuD;AACnD;AACA;AACA,UAAI,KAAKR,kBAAL,IAA2B,KAAKiE,MAApC,EAA4C;AACxC,aAAKxE,kBAAL,GAA0B,IAA1B;AACApE,QAAAA,SAAS,CAAC,KAAK4I,MAAL,CAAY1H,aAAb,EAA4B,eAA5B,CAAT,CACKqF,IADL,CACUpG,IAAI,CAAC,CAAD,CADd,EAEKqG,SAFL,CAEe,MAAM;AACjB,eAAKpC,kBAAL,GAA0B,KAA1B;AACH,SAJD;AAKH;;AACD,WAAKK,UAAL,GAAkB,QAAlB;;AACA,WAAKd,kBAAL,CAAwB4B,YAAxB;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIkB,EAAAA,qBAAqB,GAAG;AACpB,QAAI,KAAKjB,QAAL,CAAcwC,WAAd,IACA,KAAKC,iBADL,KAEC,OAAOY,SAAP,KAAqB,WAArB,IAAoCA,SAFrC,CAAJ,EAEqD;AACjD,YAAMxG,uCAAuC,EAA7C;AACH;AACJ;AACD;;;AACAgD,EAAAA,aAAa,GAAG;AACZ,SAAKyD,cAAL;;AACA,SAAKpC,mBAAL;AACH;AACD;AACJ;AACA;AACA;;;AACIoC,EAAAA,cAAc,GAAG;AACb,QAAI,KAAK3B,aAAL,KAAuB,OAAO0B,SAAP,KAAqB,WAArB,IAAoCA,SAA3D,CAAJ,EAA2E;AACvE,UAAIE,SAAJ;AACA,UAAIC,OAAJ;;AACA,WAAK7B,aAAL,CAAmB8B,OAAnB,CAA4BC,IAAD,IAAU;AACjC,YAAIA,IAAI,CAAC1G,KAAL,KAAe,OAAnB,EAA4B;AACxB,cAAIuG,SAAS,IAAI,KAAK3D,SAAtB,EAAiC;AAC7B,kBAAM7C,kCAAkC,CAAC,OAAD,CAAxC;AACH;;AACDwG,UAAAA,SAAS,GAAGG,IAAZ;AACH,SALD,MAMK,IAAIA,IAAI,CAAC1G,KAAL,KAAe,KAAnB,EAA0B;AAC3B,cAAIwG,OAAJ,EAAa;AACT,kBAAMzG,kCAAkC,CAAC,KAAD,CAAxC;AACH;;AACDyG,UAAAA,OAAO,GAAGE,IAAV;AACH;AACJ,OAbD;AAcH;AACJ;AACD;;;AACAxE,EAAAA,0BAA0B,GAAG;AACzB,WAAQ,KAAKb,SAAL,IAAkB,KAAKA,SAAL,CAAeY,UAAlC,IAAiD,MAAxD;AACH;AACD;AACJ;AACA;AACA;;;AACIiC,EAAAA,mBAAmB,GAAG;AAClB,QAAI,KAAKlB,QAAT,EAAmB;AACf,UAAI2D,GAAG,GAAG,EAAV,CADe,CAEf;;AACA,UAAI,KAAK3D,QAAL,CAAc4D,mBAAd,IACA,OAAO,KAAK5D,QAAL,CAAc4D,mBAArB,KAA6C,QADjD,EAC2D;AACvDD,QAAAA,GAAG,CAACE,IAAJ,CAAS,GAAG,KAAK7D,QAAL,CAAc4D,mBAAd,CAAkCE,KAAlC,CAAwC,GAAxC,CAAZ;AACH;;AACD,UAAI,KAAKd,qBAAL,OAAiC,MAArC,EAA6C;AACzC,cAAMO,SAAS,GAAG,KAAK5B,aAAL,GACZ,KAAKA,aAAL,CAAmBoC,IAAnB,CAAwBL,IAAI,IAAIA,IAAI,CAAC1G,KAAL,KAAe,OAA/C,CADY,GAEZ,IAFN;AAGA,cAAMwG,OAAO,GAAG,KAAK7B,aAAL,GACV,KAAKA,aAAL,CAAmBoC,IAAnB,CAAwBL,IAAI,IAAIA,IAAI,CAAC1G,KAAL,KAAe,KAA/C,CADU,GAEV,IAFN;;AAGA,YAAIuG,SAAJ,EAAe;AACXI,UAAAA,GAAG,CAACE,IAAJ,CAASN,SAAS,CAAC9H,EAAnB;AACH,SAFD,MAGK,IAAI,KAAKqD,UAAT,EAAqB;AACtB6E,UAAAA,GAAG,CAACE,IAAJ,CAAS,KAAK9E,YAAd;AACH;;AACD,YAAIyE,OAAJ,EAAa;AACTG,UAAAA,GAAG,CAACE,IAAJ,CAASL,OAAO,CAAC/H,EAAjB;AACH;AACJ,OAhBD,MAiBK,IAAI,KAAKmG,cAAT,EAAyB;AAC1B+B,QAAAA,GAAG,CAACE,IAAJ,CAAS,GAAG,KAAKjC,cAAL,CAAoBoC,GAApB,CAAwBC,KAAK,IAAIA,KAAK,CAACxI,EAAvC,CAAZ;AACH;;AACD,WAAKuE,QAAL,CAAckE,iBAAd,CAAgCP,GAAhC;AACH;AACJ;AACD;;;AACAlD,EAAAA,qBAAqB,GAAG;AACpB,QAAI,CAAC,KAAKT,QAAN,KAAmB,OAAOqD,SAAP,KAAqB,WAArB,IAAoCA,SAAvD,CAAJ,EAAuE;AACnE,YAAMpG,kCAAkC,EAAxC;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIsE,EAAAA,gBAAgB,GAAG;AACf,UAAM4C,OAAO,GAAG,KAAKf,MAAL,GAAc,KAAKA,MAAL,CAAY1H,aAA1B,GAA0C,IAA1D;AACA,UAAM0I,SAAS,GAAG,KAAK7D,uBAAL,CAA6B7E,aAA/C;AACA,UAAM2I,oBAAoB,GAAG,+BAA7B;AACA,UAAMC,kBAAkB,GAAG,6BAA3B,CAJe,CAKf;;AACA,QAAI,KAAKlF,UAAL,KAAoB,SAApB,IAAiC,CAAC,KAAKd,SAAL,CAAeiG,SAArD,EAAgE;AAC5D;AACH,KARc,CASf;;;AACA,QAAI,CAACJ,OAAD,IAAY,CAACA,OAAO,CAACK,QAAR,CAAiBvB,MAA9B,IAAwC,CAACkB,OAAO,CAACM,WAAR,CAAoBC,IAApB,EAA7C,EAAyE;AACrE,YAAMC,WAAW,GAAGP,SAAS,CAACQ,gBAAV,CAA4B,GAAEP,oBAAqB,KAAIC,kBAAmB,EAA1E,CAApB;;AACA,WAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,WAAW,CAAC1B,MAAhC,EAAwC4B,CAAC,EAAzC,EAA6C;AACzCF,QAAAA,WAAW,CAACE,CAAD,CAAX,CAAe/J,KAAf,CAAqBgK,KAArB,GAA6B,GAA7B;AACH;;AACD;AACH,KAhBc,CAiBf;AACA;;;AACA,QAAI,CAAC,KAAKC,gBAAL,EAAL,EAA8B;AAC1B,WAAKtG,uCAAL,GAA+C,IAA/C;AACA;AACH;;AACD,QAAIuG,UAAU,GAAG,CAAjB;AACA,QAAIC,QAAQ,GAAG,CAAf;AACA,UAAMC,QAAQ,GAAGd,SAAS,CAACQ,gBAAV,CAA2BP,oBAA3B,CAAjB;AACA,UAAMc,MAAM,GAAGf,SAAS,CAACQ,gBAAV,CAA2BN,kBAA3B,CAAf;;AACA,QAAI,KAAKlB,MAAL,IAAe,KAAKA,MAAL,CAAY1H,aAAZ,CAA0B8I,QAA1B,CAAmCvB,MAAtD,EAA8D;AAC1D,YAAMmC,aAAa,GAAGhB,SAAS,CAACiB,qBAAV,EAAtB,CAD0D,CAE1D;AACA;AACA;AACA;AACA;AACA;;AACA,UAAID,aAAa,CAACN,KAAd,KAAwB,CAAxB,IAA6BM,aAAa,CAACE,MAAd,KAAyB,CAA1D,EAA6D;AACzD,aAAK5G,oCAAL,GAA4C,IAA5C;AACA,aAAKD,uCAAL,GAA+C,KAA/C;AACA;AACH;;AACD,YAAM8G,cAAc,GAAG,KAAKC,YAAL,CAAkBJ,aAAlB,CAAvB;;AACA,YAAMK,aAAa,GAAGtB,OAAO,CAACK,QAA9B;;AACA,YAAMkB,UAAU,GAAG,KAAKF,YAAL,CAAkBC,aAAa,CAAC,CAAD,CAAb,CAAiBJ,qBAAjB,EAAlB,CAAnB;;AACA,UAAIM,UAAU,GAAG,CAAjB;;AACA,WAAK,IAAId,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGY,aAAa,CAACxC,MAAlC,EAA0C4B,CAAC,EAA3C,EAA+C;AAC3Cc,QAAAA,UAAU,IAAIF,aAAa,CAACZ,CAAD,CAAb,CAAiBe,WAA/B;AACH;;AACDZ,MAAAA,UAAU,GAAGa,IAAI,CAACC,GAAL,CAASJ,UAAU,GAAGH,cAAtB,IAAwC1H,iBAArD;AACAoH,MAAAA,QAAQ,GAAGU,UAAU,GAAG,CAAb,GAAiBA,UAAU,GAAG/H,kBAAb,GAAkCC,iBAAiB,GAAG,CAAvE,GAA2E,CAAtF;AACH;;AACD,SAAK,IAAIgH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGK,QAAQ,CAACjC,MAA7B,EAAqC4B,CAAC,EAAtC,EAA0C;AACtCK,MAAAA,QAAQ,CAACL,CAAD,CAAR,CAAY/J,KAAZ,CAAkBgK,KAAlB,GAA2B,GAAEE,UAAW,IAAxC;AACH;;AACD,SAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGM,MAAM,CAAClC,MAA3B,EAAmC4B,CAAC,EAApC,EAAwC;AACpCM,MAAAA,MAAM,CAACN,CAAD,CAAN,CAAU/J,KAAV,CAAgBgK,KAAhB,GAAyB,GAAEG,QAAS,IAApC;AACH;;AACD,SAAKvG,oCAAL,GAA4C,KAAKD,uCAAL,GACxC,KADJ;AAEH;AACD;;;AACA+G,EAAAA,YAAY,CAACO,IAAD,EAAO;AACf,WAAO,KAAK3H,IAAL,IAAa,KAAKA,IAAL,CAAUoB,KAAV,KAAoB,KAAjC,GAAyCuG,IAAI,CAACC,KAA9C,GAAsDD,IAAI,CAACE,IAAlE;AACH;AACD;;;AACAlB,EAAAA,gBAAgB,GAAG;AACf,UAAMmB,OAAO,GAAG,KAAKnI,WAAL,CAAiBrC,aAAjC;;AACA,QAAIwK,OAAO,CAACC,WAAZ,EAAyB;AACrB,YAAMC,QAAQ,GAAGF,OAAO,CAACC,WAAR,EAAjB,CADqB,CAErB;AACA;;AACA,aAAOC,QAAQ,IAAIA,QAAQ,KAAKF,OAAhC;AACH,KAPc,CAQf;AACA;;;AACA,WAAOG,QAAQ,CAACC,eAAT,CAAyBC,QAAzB,CAAkCL,OAAlC,CAAP;AACH;;AAlYwC;;AAoY7ChI,YAAY,CAACtC,IAAb;AAAA,mBAAyGsC,YAAzG,EAznB2F9E,EAynB3F,mBAAuIA,EAAE,CAACyC,UAA1I,GAznB2FzC,EAynB3F,mBAAiKA,EAAE,CAACoN,iBAApK,GAznB2FpN,EAynB3F,mBAAkMgB,EAAE,CAACqM,cAArM,MAznB2FrN,EAynB3F,mBAAgP4E,8BAAhP,MAznB2F5E,EAynB3F,mBAA2S6B,EAAE,CAACyL,QAA9S,GAznB2FtN,EAynB3F,mBAAmUA,EAAE,CAACuN,MAAtU,GAznB2FvN,EAynB3F,mBAAyV8B,qBAAzV;AAAA;;AACAgD,YAAY,CAAC0I,IAAb,kBA1nB2FxN,EA0nB3F;AAAA,QAA6F8E,YAA7F;AAAA;AAAA;AAAA;AA1nB2F9E,MAAAA,EA0nB3F,0BAAulDwD,mBAAvlD;AA1nB2FxD,MAAAA,EA0nB3F,0BAA2rDwD,mBAA3rD;AA1nB2FxD,MAAAA,EA0nB3F,0BAAmzDiE,QAAnzD;AA1nB2FjE,MAAAA,EA0nB3F,0BAA+4DiE,QAA/4D;AA1nB2FjE,MAAAA,EA0nB3F,0BAAy/DkE,cAAz/D;AA1nB2FlE,MAAAA,EA0nB3F,0BAA2kEgC,SAA3kE;AA1nB2FhC,MAAAA,EA0nB3F,0BAAupE+D,SAAvpE;AA1nB2F/D,MAAAA,EA0nB3F,0BAAquEmE,UAAruE;AA1nB2FnE,MAAAA,EA0nB3F,0BAAozEqE,UAApzE;AAAA;;AAAA;AAAA;;AA1nB2FrE,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AAAA;AAAA;AAAA;AAAA;AA1nB2FA,MAAAA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F;AAAA;;AAAA;AAAA;;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB3F,qBA1nB2FA,EA0nB3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA1nB2FA,MAAAA,EA0nB3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA1nB2FA,EA0nB3F,oBAAs9C,CAAC;AAAE2C,IAAAA,OAAO,EAAEkC,cAAX;AAA2BjC,IAAAA,WAAW,EAAEkC;AAAxC,GAAD,CAAt9C,GA1nB2F9E,EA0nB3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA1nB2FA,MAAAA,EA0nB3F;AA1nB2FA,MAAAA,EA0nB8oF,4BAAzuF;AA1nB2FA,MAAAA,EA0nBwrF,+BAAnxF;AA1nB2FA,MAAAA,EA0nBwvF;AAAA,gDAAuC,qCAAvC;AAAA,QAAn1F;AA1nB2FA,MAAAA,EA0nB83F,6EAAz9F;AA1nB2FA,MAAAA,EA0nBg8G,2DAA3hH;AA1nB2FA,MAAAA,EA0nBitH,+BAA5yH;AA1nB2FA,MAAAA,EA0nB6wH,gBAAx2H;AA1nB2FA,MAAAA,EA0nBgzH,6BAA34H;AA1nB2FA,MAAAA,EA0nB0gI,gEAArmI;AA1nB2FA,MAAAA,EA0nBu4K,eAAl+K;AA1nB2FA,MAAAA,EA0nBo5K,eAA/+K;AA1nB2FA,MAAAA,EA0nBk6K,6DAA7/K;AA1nB2FA,MAAAA,EA0nBgjL,eAA3oL;AA1nB2FA,MAAAA,EA0nBkoL,8DAA7tL;AA1nB2FA,MAAAA,EA0nB23L,8BAAt9L;AA1nB2FA,MAAAA,EA0nBg+L,8DAA3jM;AA1nB2FA,MAAAA,EA0nB4nM,8DAAvtM;AA1nB2FA,MAAAA,EA0nB2oN,eAAtuN;AA1nB2FA,MAAAA,EA0nBmpN,eAA9uN;AAAA;;AAAA;AA1nB2FA,MAAAA,EA0nB64F,aAAx+F;AA1nB2FA,MAAAA,EA0nB64F,gDAAx+F;AA1nB2FA,MAAAA,EA0nBo/G,aAA/kH;AA1nB2FA,MAAAA,EA0nBo/G,+CAA/kH;AA1nB2FA,MAAAA,EA0nB8mJ,aAAzsJ;AA1nB2FA,MAAAA,EA0nB8mJ,4CAAzsJ;AA1nB2FA,MAAAA,EA0nBw8K,aAAniL;AA1nB2FA,MAAAA,EA0nBw8K,+CAAniL;AA1nB2FA,MAAAA,EA0nBmrL,aAA9wL;AA1nB2FA,MAAAA,EA0nBmrL,gDAA9wL;AA1nB2FA,MAAAA,EA0nBm7L,aAA9gM;AA1nB2FA,MAAAA,EA0nBm7L,oDAA9gM;AA1nB2FA,MAAAA,EA0nBs+L,aAAjkM;AA1nB2FA,MAAAA,EA0nBs+L,oCAAjkM;AA1nB2FA,MAAAA,EA0nBwqM,aAAnwM;AA1nB2FA,MAAAA,EA0nBwqM,mCAAnwM;AAAA;AAAA;AAAA,eAAi0qBF,EAAE,CAAC2N,IAAp0qB,EAAukrB3N,EAAE,CAAC4N,QAA1krB,EAA4orB5N,EAAE,CAAC6N,YAA/orB,EAAk5qB/N,EAAE,CAACgO,iBAAr5qB;AAAA;AAAA;AAAA;AAAA,eAAkurB,CAACxK,sBAAsB,CAACC,kBAAxB;AAAlurB;AAAA;AAAA;;AACA;AAAA,qDA3nB2FrD,EA2nB3F,mBAA2F8E,YAA3F,EAAqH,CAAC;AAC1GjC,IAAAA,IAAI,EAAExC,SADoG;AAE1GyC,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE,gBAAZ;AAA8B8K,MAAAA,QAAQ,EAAE,cAAxC;AAAwDC,MAAAA,UAAU,EAAE,CAAC1K,sBAAsB,CAACC,kBAAxB,CAApE;AAAiHL,MAAAA,IAAI,EAAE;AAClH,iBAAS,gBADyG;AAElH,sDAA8C,0BAFoE;AAGlH,kDAA0C,sBAHwE;AAIlH,qDAA6C,yBAJqE;AAKlH,oDAA4C,wBALsE;AAMlH,0CAAkC,qBANgF;AAOlH,4CAAoC,kBAP8E;AAQlH,+CAAuC,qBAR2E;AASlH,4CAAoC,qBAT8E;AAUlH,mDAA2C,2BAVuE;AAWlH,2CAAmC,mBAX+E;AAYlH,6CAAqC,qBAZ6E;AAalH,+BAAuB,kBAb2F;AAclH,gCAAwB,6BAd0F;AAelH,8BAAsB,2BAf4F;AAgBlH,+BAAuB,4BAhB2F;AAiBlH,4BAAoB,yBAjB8F;AAkBlH,4BAAoB,yBAlB8F;AAmBlH,8BAAsB,2BAnB4F;AAoBlH,8BAAsB,2BApB4F;AAqBlH,2CAAmC;AArB+E,OAAvH;AAsBI+K,MAAAA,MAAM,EAAE,CAAC,OAAD,CAtBZ;AAsBuBC,MAAAA,aAAa,EAAE1N,iBAAiB,CAAC2N,IAtBxD;AAsB8DC,MAAAA,eAAe,EAAE3N,uBAAuB,CAAC4N,MAtBvG;AAsB+GlL,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAEkC,cAAX;AAA2BjC,QAAAA,WAAW,EAAEkC;AAAxC,OAAD,CAtB1H;AAsBoLsJ,MAAAA,QAAQ,EAAE,+gIAtB9L;AAsB+sIC,MAAAA,MAAM,EAAE,CAAC,gvGAAD,EAAmvG,8sCAAnvG,EAAm8I,mwIAAn8I,EAAwsR,24CAAxsR,EAAqlU,o0GAArlU,EAA25a,6oCAA35a;AAtBvtI,KAAD;AAFoG,GAAD,CAArH,EAyB4B,YAAY;AAAE,WAAO,CAAC;AAAExL,MAAAA,IAAI,EAAE7C,EAAE,CAACyC;AAAX,KAAD,EAA0B;AAAEI,MAAAA,IAAI,EAAE7C,EAAE,CAACoN;AAAX,KAA1B,EAA0D;AAAEvK,MAAAA,IAAI,EAAE7B,EAAE,CAACqM,cAAX;AAA2BlK,MAAAA,UAAU,EAAE,CAAC;AAC/HN,QAAAA,IAAI,EAAErC;AADyH,OAAD;AAAvC,KAA1D,EAE3B;AAAEqC,MAAAA,IAAI,EAAEK,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAClCN,QAAAA,IAAI,EAAErC;AAD4B,OAAD,EAElC;AACCqC,QAAAA,IAAI,EAAEpC,MADP;AAECqC,QAAAA,IAAI,EAAE,CAAC8B,8BAAD;AAFP,OAFkC;AAA/B,KAF2B,EAO3B;AAAE/B,MAAAA,IAAI,EAAEhB,EAAE,CAACyL;AAAX,KAP2B,EAOJ;AAAEzK,MAAAA,IAAI,EAAE7C,EAAE,CAACuN;AAAX,KAPI,EAOiB;AAAE1K,MAAAA,IAAI,EAAEK,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9EN,QAAAA,IAAI,EAAErC;AADwE,OAAD,EAE9E;AACCqC,QAAAA,IAAI,EAAEpC,MADP;AAECqC,QAAAA,IAAI,EAAE,CAAChB,qBAAD;AAFP,OAF8E;AAA/B,KAPjB,CAAP;AAYlB,GArCxB,EAqC0C;AAAEkE,IAAAA,UAAU,EAAE,CAAC;AACzCnD,MAAAA,IAAI,EAAEzC;AADmC,KAAD,CAAd;AAE1B8F,IAAAA,kBAAkB,EAAE,CAAC;AACrBrD,MAAAA,IAAI,EAAEzC;AADe,KAAD,CAFM;AAI1BoG,IAAAA,SAAS,EAAE,CAAC;AACZ3D,MAAAA,IAAI,EAAEzC;AADM,KAAD,CAJe;AAM1ByF,IAAAA,UAAU,EAAE,CAAC;AACbhD,MAAAA,IAAI,EAAEzC;AADO,KAAD,CANc;AAQ1B+G,IAAAA,uBAAuB,EAAE,CAAC;AAC1BtE,MAAAA,IAAI,EAAEnC,SADoB;AAE1BoC,MAAAA,IAAI,EAAE,CAAC,qBAAD,EAAwB;AAAEwL,QAAAA,MAAM,EAAE;AAAV,OAAxB;AAFoB,KAAD,CARC;AAW1BC,IAAAA,kBAAkB,EAAE,CAAC;AACrB1L,MAAAA,IAAI,EAAEnC,SADe;AAErBoC,MAAAA,IAAI,EAAE,CAAC,gBAAD;AAFe,KAAD,CAXM;AAc1BkH,IAAAA,MAAM,EAAE,CAAC;AACTnH,MAAAA,IAAI,EAAEnC,SADG;AAEToC,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFG,KAAD,CAdkB;AAiB1BgE,IAAAA,iBAAiB,EAAE,CAAC;AACpBjE,MAAAA,IAAI,EAAElC,YADc;AAEpBmC,MAAAA,IAAI,EAAE,CAACU,mBAAD;AAFc,KAAD,CAjBO;AAoB1BuD,IAAAA,cAAc,EAAE,CAAC;AACjBlE,MAAAA,IAAI,EAAElC,YADW;AAEjBmC,MAAAA,IAAI,EAAE,CAACU,mBAAD,EAAsB;AAAE8K,QAAAA,MAAM,EAAE;AAAV,OAAtB;AAFW,KAAD,CApBU;AAuB1B/E,IAAAA,oBAAoB,EAAE,CAAC;AACvB1G,MAAAA,IAAI,EAAElC,YADiB;AAEvBmC,MAAAA,IAAI,EAAE,CAACmB,QAAD;AAFiB,KAAD,CAvBI;AA0B1BuF,IAAAA,iBAAiB,EAAE,CAAC;AACpB3G,MAAAA,IAAI,EAAElC,YADc;AAEpBmC,MAAAA,IAAI,EAAE,CAACmB,QAAD,EAAW;AAAEqK,QAAAA,MAAM,EAAE;AAAV,OAAX;AAFc,KAAD,CA1BO;AA6B1BjF,IAAAA,iBAAiB,EAAE,CAAC;AACpBxG,MAAAA,IAAI,EAAElC,YADc;AAEpBmC,MAAAA,IAAI,EAAE,CAACoB,cAAD;AAFc,KAAD,CA7BO;AAgC1BsE,IAAAA,cAAc,EAAE,CAAC;AACjB3F,MAAAA,IAAI,EAAEjC,eADW;AAEjBkC,MAAAA,IAAI,EAAE,CAACd,SAAD,EAAY;AAAEwM,QAAAA,WAAW,EAAE;AAAf,OAAZ;AAFW,KAAD,CAhCU;AAmC1BjG,IAAAA,aAAa,EAAE,CAAC;AAChB1F,MAAAA,IAAI,EAAEjC,eADU;AAEhBkC,MAAAA,IAAI,EAAE,CAACiB,SAAD,EAAY;AAAEyK,QAAAA,WAAW,EAAE;AAAf,OAAZ;AAFU,KAAD,CAnCW;AAsC1BpG,IAAAA,eAAe,EAAE,CAAC;AAClBvF,MAAAA,IAAI,EAAEjC,eADY;AAElBkC,MAAAA,IAAI,EAAE,CAACqB,UAAD,EAAa;AAAEqK,QAAAA,WAAW,EAAE;AAAf,OAAb;AAFY,KAAD,CAtCS;AAyC1BlG,IAAAA,eAAe,EAAE,CAAC;AAClBzF,MAAAA,IAAI,EAAEjC,eADY;AAElBkC,MAAAA,IAAI,EAAE,CAACuB,UAAD,EAAa;AAAEmK,QAAAA,WAAW,EAAE;AAAf,OAAb;AAFY,KAAD;AAzCS,GArC1C;AAAA;AAmFA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,kBAAN,CAAyB;;AAEzBA,kBAAkB,CAACjM,IAAnB;AAAA,mBAA+GiM,kBAA/G;AAAA;;AACAA,kBAAkB,CAACC,IAAnB,kBAxtB2F1O,EAwtB3F;AAAA,QAAgHyO;AAAhH;AAQAA,kBAAkB,CAACE,IAAnB,kBAhuB2F3O,EAguB3F;AAAA,YAA8I,CAACD,YAAD,EAAegB,eAAf,EAAgClB,eAAhC,CAA9I,EAAgMkB,eAAhM;AAAA;;AACA;AAAA,qDAjuB2Ff,EAiuB3F,mBAA2FyO,kBAA3F,EAA2H,CAAC;AAChH5L,IAAAA,IAAI,EAAEhC,QAD0G;AAEhHiC,IAAAA,IAAI,EAAE,CAAC;AACC8L,MAAAA,YAAY,EAAE,CAAC3M,QAAD,EAAW6C,YAAX,EAAyBd,OAAzB,EAAkCC,QAAlC,EAA4CC,cAA5C,EAA4DE,SAA5D,EAAuEE,SAAvE,CADf;AAECuK,MAAAA,OAAO,EAAE,CAAC9O,YAAD,EAAegB,eAAf,EAAgClB,eAAhC,CAFV;AAGCiP,MAAAA,OAAO,EAAE,CACL/N,eADK,EAELkB,QAFK,EAGL6C,YAHK,EAILd,OAJK,EAKLC,QALK,EAMLC,cANK,EAOLE,SAPK,EAQLE,SARK;AAHV,KAAD;AAF0G,GAAD,CAA3H;AAAA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAStC,SAAT,EAAoB6C,cAApB,EAAoCD,8BAApC,EAAoET,UAApE,EAAgFE,UAAhF,EAA4FpC,QAA5F,EAAsG6C,YAAtG,EAAoHtB,mBAApH,EAAyIiL,kBAAzI,EAA6JzK,OAA7J,EAAsKC,QAAtK,EAAgLC,cAAhL,EAAgME,SAAhM,EAA2ME,SAA3M,EAAsNP,SAAtN,EAAiOJ,kCAAjO,EAAqQE,kCAArQ,EAAySJ,uCAAzS,EAAkVL,sBAAlV", "sourcesContent": ["import * as i4 from '@angular/cdk/observers';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport * as i3 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Attribute, Input, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { mixinColor, MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Subject, merge, fromEvent } from 'rxjs';\nimport { startWith, takeUntil, take } from 'rxjs/operators';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i2 from '@angular/cdk/platform';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet nextUniqueId$2 = 0;\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_ERROR = new InjectionToken('MatError');\n/** Single error message to be shown underneath the form field. */\nclass MatError {\n    constructor(ariaLive, elementRef) {\n        this.id = `mat-error-${nextUniqueId$2++}`;\n        // If no aria-live value is set add 'polite' as a default. This is preferred over setting\n        // role='alert' so that screen readers do not interrupt the current task to read this aloud.\n        if (!ariaLive) {\n            elementRef.nativeElement.setAttribute('aria-live', 'polite');\n        }\n    }\n}\nMatError.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatError, deps: [{ token: 'aria-live', attribute: true }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nMatError.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatError, selector: \"mat-error\", inputs: { id: \"id\" }, host: { attributes: { \"aria-atomic\": \"true\" }, properties: { \"attr.id\": \"id\" }, classAttribute: \"mat-error\" }, providers: [{ provide: MAT_ERROR, useExisting: MatError }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatError, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-error',\n                    host: {\n                        'class': 'mat-error',\n                        '[attr.id]': 'id',\n                        'aria-atomic': 'true',\n                    },\n                    providers: [{ provide: MAT_ERROR, useExisting: MatError }],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['aria-live']\n                }] }, { type: i0.ElementRef }]; }, propDecorators: { id: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Animations used by the MatFormField.\n * @docs-private\n */\nconst matFormFieldAnimations = {\n    /** Animation that transitions the form field's error and hint messages. */\n    transitionMessages: trigger('transitionMessages', [\n        // TODO(mmalerba): Use angular animations for label animation as well.\n        state('enter', style({ opacity: 1, transform: 'translateY(0%)' })),\n        transition('void => enter', [\n            style({ opacity: 0, transform: 'translateY(-5px)' }),\n            animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n        ]),\n    ]),\n};\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\nclass MatFormFieldControl {\n}\nMatFormFieldControl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatFormFieldControl, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatFormFieldControl.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatFormFieldControl, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatFormFieldControl, decorators: [{\n            type: Directive\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nfunction getMatFormFieldPlaceholderConflictError() {\n    return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\nfunction getMatFormFieldDuplicatedHintError(align) {\n    return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\nfunction getMatFormFieldMissingControlError() {\n    return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet nextUniqueId$1 = 0;\n/**\n * Injection token that can be used to reference instances of `MatHint`. It serves as\n * alternative token to the actual `MatHint` class which could cause unnecessary\n * retention of the class and its directive metadata.\n *\n * *Note*: This is not part of the public API as the MDC-based form-field will not\n * need a lightweight token for `MatHint` and we want to reduce breaking changes.\n */\nconst _MAT_HINT = new InjectionToken('MatHint');\n/** Hint text to be shown underneath the form field control. */\nclass MatHint {\n    constructor() {\n        /** Whether to align the hint label at the start or end of the line. */\n        this.align = 'start';\n        /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n        this.id = `mat-hint-${nextUniqueId$1++}`;\n    }\n}\nMatHint.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatHint, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatHint.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatHint, selector: \"mat-hint\", inputs: { align: \"align\", id: \"id\" }, host: { properties: { \"class.mat-form-field-hint-end\": \"align === \\\"end\\\"\", \"attr.id\": \"id\", \"attr.align\": \"null\" }, classAttribute: \"mat-hint\" }, providers: [{ provide: _MAT_HINT, useExisting: MatHint }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatHint, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-hint',\n                    host: {\n                        'class': 'mat-hint',\n                        '[class.mat-form-field-hint-end]': 'align === \"end\"',\n                        '[attr.id]': 'id',\n                        // Remove align attribute to prevent it from interfering with layout.\n                        '[attr.align]': 'null',\n                    },\n                    providers: [{ provide: _MAT_HINT, useExisting: MatHint }],\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The floating label for a `mat-form-field`. */\nclass MatLabel {\n}\nMatLabel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatLabel.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatLabel, selector: \"mat-label\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-label',\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The placeholder text for an `MatFormField`.\n * @deprecated Use `<mat-label>` to specify the label and the `placeholder` attribute to specify the\n *     placeholder.\n * @breaking-change 8.0.0\n */\nclass MatPlaceholder {\n}\nMatPlaceholder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPlaceholder, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatPlaceholder.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatPlaceholder, selector: \"mat-placeholder\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPlaceholder, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-placeholder',\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_PREFIX = new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\nclass MatPrefix {\n}\nMatPrefix.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPrefix, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatPrefix.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatPrefix, selector: \"[matPrefix]\", providers: [{ provide: MAT_PREFIX, useExisting: MatPrefix }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatPrefix, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matPrefix]',\n                    providers: [{ provide: MAT_PREFIX, useExisting: MatPrefix }],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SUFFIX = new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\nclass MatSuffix {\n}\nMatSuffix.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSuffix, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatSuffix.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatSuffix, selector: \"[matSuffix]\", providers: [{ provide: MAT_SUFFIX, useExisting: MatSuffix }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatSuffix, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matSuffix]',\n                    providers: [{ provide: MAT_SUFFIX, useExisting: MatSuffix }],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet nextUniqueId = 0;\nconst floatingLabelScale = 0.75;\nconst outlineGapPadding = 5;\n/**\n * Boilerplate for applying mixins to MatFormField.\n * @docs-private\n */\nconst _MatFormFieldBase = mixinColor(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}, 'primary');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nconst MAT_FORM_FIELD = new InjectionToken('MatFormField');\n/** Container for form controls that applies Material Design styling and behavior. */\nclass MatFormField extends _MatFormFieldBase {\n    constructor(elementRef, _changeDetectorRef, _dir, _defaults, _platform, _ngZone, _animationMode) {\n        super(elementRef);\n        this._changeDetectorRef = _changeDetectorRef;\n        this._dir = _dir;\n        this._defaults = _defaults;\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        /**\n         * Whether the outline gap needs to be calculated\n         * immediately on the next change detection run.\n         */\n        this._outlineGapCalculationNeededImmediately = false;\n        /** Whether the outline gap needs to be calculated next time the zone has stabilized. */\n        this._outlineGapCalculationNeededOnStable = false;\n        this._destroyed = new Subject();\n        /** Override for the logic that disables the label animation in certain cases. */\n        this._showAlwaysAnimate = false;\n        /** State of the mat-hint and mat-error animations. */\n        this._subscriptAnimationState = '';\n        this._hintLabel = '';\n        // Unique id for the hint label.\n        this._hintLabelId = `mat-hint-${nextUniqueId++}`;\n        // Unique id for the label element.\n        this._labelId = `mat-form-field-label-${nextUniqueId++}`;\n        this.floatLabel = this._getDefaultFloatLabelState();\n        this._animationsEnabled = _animationMode !== 'NoopAnimations';\n        // Set the default through here so we invoke the setter on the first run.\n        this.appearance = _defaults && _defaults.appearance ? _defaults.appearance : 'legacy';\n        this._hideRequiredMarker =\n            _defaults && _defaults.hideRequiredMarker != null ? _defaults.hideRequiredMarker : false;\n    }\n    /** The form-field appearance style. */\n    get appearance() {\n        return this._appearance;\n    }\n    set appearance(value) {\n        const oldValue = this._appearance;\n        this._appearance = value || (this._defaults && this._defaults.appearance) || 'legacy';\n        if (this._appearance === 'outline' && oldValue !== value) {\n            this._outlineGapCalculationNeededOnStable = true;\n        }\n    }\n    /** Whether the required marker should be hidden. */\n    get hideRequiredMarker() {\n        return this._hideRequiredMarker;\n    }\n    set hideRequiredMarker(value) {\n        this._hideRequiredMarker = coerceBooleanProperty(value);\n    }\n    /** Whether the floating label should always float or not. */\n    _shouldAlwaysFloat() {\n        return this.floatLabel === 'always' && !this._showAlwaysAnimate;\n    }\n    /** Whether the label can float or not. */\n    _canLabelFloat() {\n        return this.floatLabel !== 'never';\n    }\n    /** Text for the form field hint. */\n    get hintLabel() {\n        return this._hintLabel;\n    }\n    set hintLabel(value) {\n        this._hintLabel = value;\n        this._processHints();\n    }\n    /**\n     * Whether the label should always float, never float or float as the user types.\n     *\n     * Note: only the legacy appearance supports the `never` option. `never` was originally added as a\n     * way to make the floating label emulate the behavior of a standard input placeholder. However\n     * the form field now supports both floating labels and placeholders. Therefore in the non-legacy\n     * appearances the `never` option has been disabled in favor of just using the placeholder.\n     */\n    get floatLabel() {\n        return this.appearance !== 'legacy' && this._floatLabel === 'never' ? 'auto' : this._floatLabel;\n    }\n    set floatLabel(value) {\n        if (value !== this._floatLabel) {\n            this._floatLabel = value || this._getDefaultFloatLabelState();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    get _control() {\n        // TODO(crisbeto): we need this workaround in order to support both Ivy and ViewEngine.\n        //  We should clean this up once Ivy is the default renderer.\n        return this._explicitFormFieldControl || this._controlNonStatic || this._controlStatic;\n    }\n    set _control(value) {\n        this._explicitFormFieldControl = value;\n    }\n    /**\n     * Gets the id of the label element. If no label is present, returns `null`.\n     */\n    getLabelId() {\n        return this._hasFloatingLabel() ? this._labelId : null;\n    }\n    /**\n     * Gets an ElementRef for the element that a overlay attached to the form-field should be\n     * positioned relative to.\n     */\n    getConnectedOverlayOrigin() {\n        return this._connectionContainerRef || this._elementRef;\n    }\n    ngAfterContentInit() {\n        this._validateControlChild();\n        const control = this._control;\n        if (control.controlType) {\n            this._elementRef.nativeElement.classList.add(`mat-form-field-type-${control.controlType}`);\n        }\n        // Subscribe to changes in the child control state in order to update the form field UI.\n        control.stateChanges.pipe(startWith(null)).subscribe(() => {\n            this._validatePlaceholders();\n            this._syncDescribedByIds();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Run change detection if the value changes.\n        if (control.ngControl && control.ngControl.valueChanges) {\n            control.ngControl.valueChanges\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(() => this._changeDetectorRef.markForCheck());\n        }\n        // Note that we have to run outside of the `NgZone` explicitly,\n        // in order to avoid throwing users into an infinite loop\n        // if `zone-patch-rxjs` is included.\n        this._ngZone.runOutsideAngular(() => {\n            this._ngZone.onStable.pipe(takeUntil(this._destroyed)).subscribe(() => {\n                if (this._outlineGapCalculationNeededOnStable) {\n                    this.updateOutlineGap();\n                }\n            });\n        });\n        // Run change detection and update the outline if the suffix or prefix changes.\n        merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n            this._outlineGapCalculationNeededOnStable = true;\n            this._changeDetectorRef.markForCheck();\n        });\n        // Re-validate when the number of hints changes.\n        this._hintChildren.changes.pipe(startWith(null)).subscribe(() => {\n            this._processHints();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Update the aria-described by when the number of errors changes.\n        this._errorChildren.changes.pipe(startWith(null)).subscribe(() => {\n            this._syncDescribedByIds();\n            this._changeDetectorRef.markForCheck();\n        });\n        if (this._dir) {\n            this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n                if (typeof requestAnimationFrame === 'function') {\n                    this._ngZone.runOutsideAngular(() => {\n                        requestAnimationFrame(() => this.updateOutlineGap());\n                    });\n                }\n                else {\n                    this.updateOutlineGap();\n                }\n            });\n        }\n    }\n    ngAfterContentChecked() {\n        this._validateControlChild();\n        if (this._outlineGapCalculationNeededImmediately) {\n            this.updateOutlineGap();\n        }\n    }\n    ngAfterViewInit() {\n        // Avoid animations on load.\n        this._subscriptAnimationState = 'enter';\n        this._changeDetectorRef.detectChanges();\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Determines whether a class from the NgControl should be forwarded to the host element. */\n    _shouldForward(prop) {\n        const ngControl = this._control ? this._control.ngControl : null;\n        return ngControl && ngControl[prop];\n    }\n    _hasPlaceholder() {\n        return !!((this._control && this._control.placeholder) || this._placeholderChild);\n    }\n    _hasLabel() {\n        return !!(this._labelChildNonStatic || this._labelChildStatic);\n    }\n    _shouldLabelFloat() {\n        return (this._canLabelFloat() &&\n            ((this._control && this._control.shouldLabelFloat) || this._shouldAlwaysFloat()));\n    }\n    _hideControlPlaceholder() {\n        // In the legacy appearance the placeholder is promoted to a label if no label is given.\n        return ((this.appearance === 'legacy' && !this._hasLabel()) ||\n            (this._hasLabel() && !this._shouldLabelFloat()));\n    }\n    _hasFloatingLabel() {\n        // In the legacy appearance the placeholder is promoted to a label if no label is given.\n        return this._hasLabel() || (this.appearance === 'legacy' && this._hasPlaceholder());\n    }\n    /** Determines whether to display hints or errors. */\n    _getDisplayedMessages() {\n        return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState\n            ? 'error'\n            : 'hint';\n    }\n    /** Animates the placeholder up and locks it in position. */\n    _animateAndLockLabel() {\n        if (this._hasFloatingLabel() && this._canLabelFloat()) {\n            // If animations are disabled, we shouldn't go in here,\n            // because the `transitionend` will never fire.\n            if (this._animationsEnabled && this._label) {\n                this._showAlwaysAnimate = true;\n                fromEvent(this._label.nativeElement, 'transitionend')\n                    .pipe(take(1))\n                    .subscribe(() => {\n                    this._showAlwaysAnimate = false;\n                });\n            }\n            this.floatLabel = 'always';\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Ensure that there is only one placeholder (either `placeholder` attribute on the child control\n     * or child element with the `mat-placeholder` directive).\n     */\n    _validatePlaceholders() {\n        if (this._control.placeholder &&\n            this._placeholderChild &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatFormFieldPlaceholderConflictError();\n        }\n    }\n    /** Does any extra processing that is required when handling the hints. */\n    _processHints() {\n        this._validateHints();\n        this._syncDescribedByIds();\n    }\n    /**\n     * Ensure that there is a maximum of one of each `<mat-hint>` alignment specified, with the\n     * attribute being considered as `align=\"start\"`.\n     */\n    _validateHints() {\n        if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            let startHint;\n            let endHint;\n            this._hintChildren.forEach((hint) => {\n                if (hint.align === 'start') {\n                    if (startHint || this.hintLabel) {\n                        throw getMatFormFieldDuplicatedHintError('start');\n                    }\n                    startHint = hint;\n                }\n                else if (hint.align === 'end') {\n                    if (endHint) {\n                        throw getMatFormFieldDuplicatedHintError('end');\n                    }\n                    endHint = hint;\n                }\n            });\n        }\n    }\n    /** Gets the default float label state. */\n    _getDefaultFloatLabelState() {\n        return (this._defaults && this._defaults.floatLabel) || 'auto';\n    }\n    /**\n     * Sets the list of element IDs that describe the child control. This allows the control to update\n     * its `aria-describedby` attribute accordingly.\n     */\n    _syncDescribedByIds() {\n        if (this._control) {\n            let ids = [];\n            // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n            if (this._control.userAriaDescribedBy &&\n                typeof this._control.userAriaDescribedBy === 'string') {\n                ids.push(...this._control.userAriaDescribedBy.split(' '));\n            }\n            if (this._getDisplayedMessages() === 'hint') {\n                const startHint = this._hintChildren\n                    ? this._hintChildren.find(hint => hint.align === 'start')\n                    : null;\n                const endHint = this._hintChildren\n                    ? this._hintChildren.find(hint => hint.align === 'end')\n                    : null;\n                if (startHint) {\n                    ids.push(startHint.id);\n                }\n                else if (this._hintLabel) {\n                    ids.push(this._hintLabelId);\n                }\n                if (endHint) {\n                    ids.push(endHint.id);\n                }\n            }\n            else if (this._errorChildren) {\n                ids.push(...this._errorChildren.map(error => error.id));\n            }\n            this._control.setDescribedByIds(ids);\n        }\n    }\n    /** Throws an error if the form field's control is missing. */\n    _validateControlChild() {\n        if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatFormFieldMissingControlError();\n        }\n    }\n    /**\n     * Updates the width and position of the gap in the outline. Only relevant for the outline\n     * appearance.\n     */\n    updateOutlineGap() {\n        const labelEl = this._label ? this._label.nativeElement : null;\n        const container = this._connectionContainerRef.nativeElement;\n        const outlineStartSelector = '.mat-form-field-outline-start';\n        const outlineGapSelector = '.mat-form-field-outline-gap';\n        // getBoundingClientRect isn't available on the server.\n        if (this.appearance !== 'outline' || !this._platform.isBrowser) {\n            return;\n        }\n        // If there is no content, set the gap elements to zero.\n        if (!labelEl || !labelEl.children.length || !labelEl.textContent.trim()) {\n            const gapElements = container.querySelectorAll(`${outlineStartSelector}, ${outlineGapSelector}`);\n            for (let i = 0; i < gapElements.length; i++) {\n                gapElements[i].style.width = '0';\n            }\n            return;\n        }\n        // If the element is not present in the DOM, the outline gap will need to be calculated\n        // the next time it is checked and in the DOM.\n        if (!this._isAttachedToDOM()) {\n            this._outlineGapCalculationNeededImmediately = true;\n            return;\n        }\n        let startWidth = 0;\n        let gapWidth = 0;\n        const startEls = container.querySelectorAll(outlineStartSelector);\n        const gapEls = container.querySelectorAll(outlineGapSelector);\n        if (this._label && this._label.nativeElement.children.length) {\n            const containerRect = container.getBoundingClientRect();\n            // If the container's width and height are zero, it means that the element is\n            // invisible and we can't calculate the outline gap. Mark the element as needing\n            // to be checked the next time the zone stabilizes. We can't do this immediately\n            // on the next change detection, because even if the element becomes visible,\n            // the `ClientRect` won't be reclaculated immediately. We reset the\n            // `_outlineGapCalculationNeededImmediately` flag some we don't run the checks twice.\n            if (containerRect.width === 0 && containerRect.height === 0) {\n                this._outlineGapCalculationNeededOnStable = true;\n                this._outlineGapCalculationNeededImmediately = false;\n                return;\n            }\n            const containerStart = this._getStartEnd(containerRect);\n            const labelChildren = labelEl.children;\n            const labelStart = this._getStartEnd(labelChildren[0].getBoundingClientRect());\n            let labelWidth = 0;\n            for (let i = 0; i < labelChildren.length; i++) {\n                labelWidth += labelChildren[i].offsetWidth;\n            }\n            startWidth = Math.abs(labelStart - containerStart) - outlineGapPadding;\n            gapWidth = labelWidth > 0 ? labelWidth * floatingLabelScale + outlineGapPadding * 2 : 0;\n        }\n        for (let i = 0; i < startEls.length; i++) {\n            startEls[i].style.width = `${startWidth}px`;\n        }\n        for (let i = 0; i < gapEls.length; i++) {\n            gapEls[i].style.width = `${gapWidth}px`;\n        }\n        this._outlineGapCalculationNeededOnStable = this._outlineGapCalculationNeededImmediately =\n            false;\n    }\n    /** Gets the start end of the rect considering the current directionality. */\n    _getStartEnd(rect) {\n        return this._dir && this._dir.value === 'rtl' ? rect.right : rect.left;\n    }\n    /** Checks whether the form field is attached to the DOM. */\n    _isAttachedToDOM() {\n        const element = this._elementRef.nativeElement;\n        if (element.getRootNode) {\n            const rootNode = element.getRootNode();\n            // If the element is inside the DOM the root node will be either the document\n            // or the closest shadow root, otherwise it'll be the element itself.\n            return rootNode && rootNode !== element;\n        }\n        // Otherwise fall back to checking if it's in the document. This doesn't account for\n        // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n        return document.documentElement.contains(element);\n    }\n}\nMatFormField.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatFormField, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.Directionality, optional: true }, { token: MAT_FORM_FIELD_DEFAULT_OPTIONS, optional: true }, { token: i2.Platform }, { token: i0.NgZone }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nMatFormField.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatFormField, selector: \"mat-form-field\", inputs: { color: \"color\", appearance: \"appearance\", hideRequiredMarker: \"hideRequiredMarker\", hintLabel: \"hintLabel\", floatLabel: \"floatLabel\" }, host: { properties: { \"class.mat-form-field-appearance-standard\": \"appearance == \\\"standard\\\"\", \"class.mat-form-field-appearance-fill\": \"appearance == \\\"fill\\\"\", \"class.mat-form-field-appearance-outline\": \"appearance == \\\"outline\\\"\", \"class.mat-form-field-appearance-legacy\": \"appearance == \\\"legacy\\\"\", \"class.mat-form-field-invalid\": \"_control.errorState\", \"class.mat-form-field-can-float\": \"_canLabelFloat()\", \"class.mat-form-field-should-float\": \"_shouldLabelFloat()\", \"class.mat-form-field-has-label\": \"_hasFloatingLabel()\", \"class.mat-form-field-hide-placeholder\": \"_hideControlPlaceholder()\", \"class.mat-form-field-disabled\": \"_control.disabled\", \"class.mat-form-field-autofilled\": \"_control.autofilled\", \"class.mat-focused\": \"_control.focused\", \"class.ng-untouched\": \"_shouldForward(\\\"untouched\\\")\", \"class.ng-touched\": \"_shouldForward(\\\"touched\\\")\", \"class.ng-pristine\": \"_shouldForward(\\\"pristine\\\")\", \"class.ng-dirty\": \"_shouldForward(\\\"dirty\\\")\", \"class.ng-valid\": \"_shouldForward(\\\"valid\\\")\", \"class.ng-invalid\": \"_shouldForward(\\\"invalid\\\")\", \"class.ng-pending\": \"_shouldForward(\\\"pending\\\")\", \"class._mat-animation-noopable\": \"!_animationsEnabled\" }, classAttribute: \"mat-form-field\" }, providers: [{ provide: MAT_FORM_FIELD, useExisting: MatFormField }], queries: [{ propertyName: \"_controlNonStatic\", first: true, predicate: MatFormFieldControl, descendants: true }, { propertyName: \"_controlStatic\", first: true, predicate: MatFormFieldControl, descendants: true, static: true }, { propertyName: \"_labelChildNonStatic\", first: true, predicate: MatLabel, descendants: true }, { propertyName: \"_labelChildStatic\", first: true, predicate: MatLabel, descendants: true, static: true }, { propertyName: \"_placeholderChild\", first: true, predicate: MatPlaceholder, descendants: true }, { propertyName: \"_errorChildren\", predicate: MAT_ERROR, descendants: true }, { propertyName: \"_hintChildren\", predicate: _MAT_HINT, descendants: true }, { propertyName: \"_prefixChildren\", predicate: MAT_PREFIX, descendants: true }, { propertyName: \"_suffixChildren\", predicate: MAT_SUFFIX, descendants: true }], viewQueries: [{ propertyName: \"_connectionContainerRef\", first: true, predicate: [\"connectionContainer\"], descendants: true, static: true }, { propertyName: \"_inputContainerRef\", first: true, predicate: [\"inputContainer\"], descendants: true }, { propertyName: \"_label\", first: true, predicate: [\"label\"], descendants: true }], exportAs: [\"matFormField\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mat-form-field-wrapper\\\">\\n  <div class=\\\"mat-form-field-flex\\\" #connectionContainer\\n       (click)=\\\"_control.onContainerClick && _control.onContainerClick($event)\\\">\\n\\n    <!-- Outline used for outline appearance. -->\\n    <ng-container *ngIf=\\\"appearance == 'outline'\\\">\\n      <div class=\\\"mat-form-field-outline\\\">\\n        <div class=\\\"mat-form-field-outline-start\\\"></div>\\n        <div class=\\\"mat-form-field-outline-gap\\\"></div>\\n        <div class=\\\"mat-form-field-outline-end\\\"></div>\\n      </div>\\n      <div class=\\\"mat-form-field-outline mat-form-field-outline-thick\\\">\\n        <div class=\\\"mat-form-field-outline-start\\\"></div>\\n        <div class=\\\"mat-form-field-outline-gap\\\"></div>\\n        <div class=\\\"mat-form-field-outline-end\\\"></div>\\n      </div>\\n    </ng-container>\\n\\n    <div\\n      class=\\\"mat-form-field-prefix\\\"\\n      *ngIf=\\\"_prefixChildren.length\\\"\\n      (cdkObserveContent)=\\\"updateOutlineGap()\\\"\\n      [cdkObserveContentDisabled]=\\\"appearance != 'outline'\\\">\\n      <ng-content select=\\\"[matPrefix]\\\"></ng-content>\\n    </div>\\n\\n    <div class=\\\"mat-form-field-infix\\\" #inputContainer>\\n      <ng-content></ng-content>\\n\\n      <span class=\\\"mat-form-field-label-wrapper\\\">\\n        <!-- We add aria-owns as a workaround for an issue in JAWS & NVDA where the label isn't\\n             read if it comes before the control in the DOM. -->\\n        <label class=\\\"mat-form-field-label\\\"\\n               (cdkObserveContent)=\\\"updateOutlineGap()\\\"\\n               [cdkObserveContentDisabled]=\\\"appearance != 'outline'\\\"\\n               [id]=\\\"_labelId\\\"\\n               [attr.for]=\\\"_control.id\\\"\\n               [attr.aria-owns]=\\\"_control.id\\\"\\n               [class.mat-empty]=\\\"_control.empty && !_shouldAlwaysFloat()\\\"\\n               [class.mat-form-field-empty]=\\\"_control.empty && !_shouldAlwaysFloat()\\\"\\n               [class.mat-accent]=\\\"color == 'accent'\\\"\\n               [class.mat-warn]=\\\"color == 'warn'\\\"\\n               #label\\n               *ngIf=\\\"_hasFloatingLabel()\\\"\\n               [ngSwitch]=\\\"_hasLabel()\\\">\\n\\n          <!-- @breaking-change 8.0.0 remove in favor of mat-label element an placeholder attr. -->\\n          <ng-container *ngSwitchCase=\\\"false\\\">\\n            <ng-content select=\\\"mat-placeholder\\\"></ng-content>\\n            <span>{{_control.placeholder}}</span>\\n          </ng-container>\\n\\n          <ng-content select=\\\"mat-label\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n\\n          <!-- @breaking-change 8.0.0 remove `mat-placeholder-required` class -->\\n          <span\\n            class=\\\"mat-placeholder-required mat-form-field-required-marker\\\"\\n            aria-hidden=\\\"true\\\"\\n            *ngIf=\\\"!hideRequiredMarker && _control.required && !_control.disabled\\\">&#32;*</span>\\n        </label>\\n      </span>\\n    </div>\\n\\n    <div class=\\\"mat-form-field-suffix\\\" *ngIf=\\\"_suffixChildren.length\\\">\\n      <ng-content select=\\\"[matSuffix]\\\"></ng-content>\\n    </div>\\n  </div>\\n\\n  <!-- Underline used for legacy, standard, and box appearances. -->\\n  <div class=\\\"mat-form-field-underline\\\"\\n       *ngIf=\\\"appearance != 'outline'\\\">\\n    <span class=\\\"mat-form-field-ripple\\\"\\n          [class.mat-accent]=\\\"color == 'accent'\\\"\\n          [class.mat-warn]=\\\"color == 'warn'\\\"></span>\\n  </div>\\n\\n  <div class=\\\"mat-form-field-subscript-wrapper\\\"\\n       [ngSwitch]=\\\"_getDisplayedMessages()\\\">\\n    <div *ngSwitchCase=\\\"'error'\\\" [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n      <ng-content select=\\\"mat-error\\\"></ng-content>\\n    </div>\\n\\n    <div class=\\\"mat-form-field-hint-wrapper\\\" *ngSwitchCase=\\\"'hint'\\\"\\n      [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n      <!-- TODO(mmalerba): use an actual <mat-hint> once all selectors are switched to mat-* -->\\n      <div *ngIf=\\\"hintLabel\\\" [id]=\\\"_hintLabelId\\\" class=\\\"mat-hint\\\">{{hintLabel}}</div>\\n      <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n      <div class=\\\"mat-form-field-hint-spacer\\\"></div>\\n      <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-form-field{display:inline-block;position:relative;text-align:left}[dir=rtl] .mat-form-field{text-align:right}.mat-form-field-wrapper{position:relative}.mat-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-form-field-prefix,.mat-form-field-suffix{white-space:nowrap;flex:none;position:relative}.mat-form-field-infix{display:block;position:relative;flex:auto;min-width:0;width:180px}.cdk-high-contrast-active .mat-form-field-infix{border-image:linear-gradient(transparent, transparent)}.mat-form-field-label-wrapper{position:absolute;left:0;box-sizing:content-box;width:100%;height:100%;overflow:hidden;pointer-events:none}[dir=rtl] .mat-form-field-label-wrapper{left:auto;right:0}.mat-form-field-label{position:absolute;left:0;font:inherit;pointer-events:none;width:100%;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;transform-origin:0 0;transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1),color 400ms cubic-bezier(0.25, 0.8, 0.25, 1),width 400ms cubic-bezier(0.25, 0.8, 0.25, 1);display:none}[dir=rtl] .mat-form-field-label{transform-origin:100% 0;left:auto;right:0}.cdk-high-contrast-active .mat-form-field-disabled .mat-form-field-label{color:GrayText}.mat-form-field-empty.mat-form-field-label,.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label{display:block}.mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{display:none}.mat-form-field-can-float .mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{display:block;transition:none}.mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label,.mat-input-server[placeholder]:not(:placeholder-shown)+.mat-form-field-label-wrapper .mat-form-field-label{display:none}.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label,.mat-form-field-can-float .mat-input-server[placeholder]:not(:placeholder-shown)+.mat-form-field-label-wrapper .mat-form-field-label{display:block}.mat-form-field-label:not(.mat-form-field-empty){transition:none}.mat-form-field-underline{position:absolute;width:100%;pointer-events:none;transform:scale3d(1, 1.0001, 1)}.mat-form-field-ripple{position:absolute;left:0;width:100%;transform-origin:50%;transform:scaleX(0.5);opacity:0;transition:background-color 300ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-form-field.mat-focused .mat-form-field-ripple,.mat-form-field.mat-form-field-invalid .mat-form-field-ripple{opacity:1;transform:none;transition:transform 300ms cubic-bezier(0.25, 0.8, 0.25, 1),opacity 100ms cubic-bezier(0.25, 0.8, 0.25, 1),background-color 300ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-subscript-wrapper{position:absolute;box-sizing:border-box;width:100%;overflow:hidden}.mat-form-field-subscript-wrapper .mat-icon,.mat-form-field-label-wrapper .mat-icon{width:1em;height:1em;font-size:inherit;vertical-align:baseline}.mat-form-field-hint-wrapper{display:flex}.mat-form-field-hint-spacer{flex:1 0 1em}.mat-error{display:block}.mat-form-field-control-wrapper{position:relative}.mat-form-field-hint-end{order:1}.mat-form-field._mat-animation-noopable .mat-form-field-label,.mat-form-field._mat-animation-noopable .mat-form-field-ripple{transition:none}\\n\", \".mat-form-field-appearance-fill .mat-form-field-flex{border-radius:4px 4px 0 0;padding:.75em .75em 0 .75em}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-form-field-flex{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-flex{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-form-field-flex{outline:dashed 3px}.mat-form-field-appearance-fill .mat-form-field-underline::before{content:\\\"\\\";display:block;position:absolute;bottom:0;height:1px;width:100%}.mat-form-field-appearance-fill .mat-form-field-ripple{bottom:0;height:2px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-form-field-ripple{height:0}.mat-form-field-appearance-fill:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{opacity:1;transform:none;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-fill._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{transition:none}.mat-form-field-appearance-fill .mat-form-field-subscript-wrapper{padding:0 1em}\\n\", \".mat-input-element{font:inherit;background:transparent;color:currentColor;border:none;outline:none;padding:0;margin:0;width:100%;max-width:100%;vertical-align:bottom;text-align:inherit;box-sizing:content-box}.mat-input-element:-moz-ui-invalid{box-shadow:none}.mat-input-element,.mat-input-element::-webkit-search-cancel-button,.mat-input-element::-webkit-search-decoration,.mat-input-element::-webkit-search-results-button,.mat-input-element::-webkit-search-results-decoration{-webkit-appearance:none}.mat-input-element::-webkit-contacts-auto-fill-button,.mat-input-element::-webkit-caps-lock-indicator,.mat-input-element:not([type=password])::-webkit-credentials-auto-fill-button{visibility:hidden}.mat-input-element[type=date],.mat-input-element[type=datetime],.mat-input-element[type=datetime-local],.mat-input-element[type=month],.mat-input-element[type=week],.mat-input-element[type=time]{line-height:1}.mat-input-element[type=date]::after,.mat-input-element[type=datetime]::after,.mat-input-element[type=datetime-local]::after,.mat-input-element[type=month]::after,.mat-input-element[type=week]::after,.mat-input-element[type=time]::after{content:\\\" \\\";white-space:pre;width:1px}.mat-input-element::-webkit-inner-spin-button,.mat-input-element::-webkit-calendar-picker-indicator,.mat-input-element::-webkit-clear-button{font-size:.75em}.mat-input-element::placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element::-moz-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element::-webkit-input-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element:-ms-input-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-hide-placeholder .mat-input-element::placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element::-moz-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::-moz-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element::-webkit-input-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::-webkit-input-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element:-ms-input-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element:-ms-input-placeholder{opacity:0}textarea.mat-input-element{resize:vertical;overflow:auto}textarea.mat-input-element.cdk-textarea-autosize{resize:none}textarea.mat-input-element{padding:2px 0;margin:-2px 0}select.mat-input-element{-moz-appearance:none;-webkit-appearance:none;position:relative;background-color:transparent;display:inline-flex;box-sizing:border-box;padding-top:1em;top:-1em;margin-bottom:-1em}select.mat-input-element::-moz-focus-inner{border:0}select.mat-input-element:not(:disabled){cursor:pointer}.mat-form-field-type-mat-native-select .mat-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid transparent;border-right:5px solid transparent;border-top:5px solid;position:absolute;top:50%;right:0;margin-top:-2.5px;pointer-events:none}[dir=rtl] .mat-form-field-type-mat-native-select .mat-form-field-infix::after{right:auto;left:0}.mat-form-field-type-mat-native-select .mat-input-element{padding-right:15px}[dir=rtl] .mat-form-field-type-mat-native-select .mat-input-element{padding-right:0;padding-left:15px}.mat-form-field-type-mat-native-select .mat-form-field-label-wrapper{max-width:calc(100% - 10px)}.mat-form-field-type-mat-native-select.mat-form-field-appearance-outline .mat-form-field-infix::after{margin-top:-5px}.mat-form-field-type-mat-native-select.mat-form-field-appearance-fill .mat-form-field-infix::after{margin-top:-10px}\\n\", \".mat-form-field-appearance-legacy .mat-form-field-label{transform:perspective(100px)}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon{width:1em}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon-button,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon-button{font:inherit;vertical-align:baseline}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon-button .mat-icon{font-size:inherit}.mat-form-field-appearance-legacy .mat-form-field-underline{height:1px}.cdk-high-contrast-active .mat-form-field-appearance-legacy .mat-form-field-underline{height:0;border-top:solid 1px}.mat-form-field-appearance-legacy .mat-form-field-ripple{top:0;height:2px;overflow:hidden}.cdk-high-contrast-active .mat-form-field-appearance-legacy .mat-form-field-ripple{height:0;border-top:solid 2px}.mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{background-position:0;background-color:transparent}.cdk-high-contrast-active .mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{border-top-style:dotted;border-top-width:2px;border-top-color:GrayText}.mat-form-field-appearance-legacy.mat-form-field-invalid:not(.mat-focused) .mat-form-field-ripple{height:1px}\\n\", \".mat-form-field-appearance-outline .mat-form-field-wrapper{margin:.25em 0}.mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em 0 .75em;margin-top:-0.25em;position:relative}.mat-form-field-appearance-outline .mat-form-field-prefix,.mat-form-field-appearance-outline .mat-form-field-suffix{top:.25em}.mat-form-field-appearance-outline .mat-form-field-outline{display:flex;position:absolute;top:.25em;left:0;right:0;bottom:0;pointer-events:none}.mat-form-field-appearance-outline .mat-form-field-outline-start,.mat-form-field-appearance-outline .mat-form-field-outline-end{border:1px solid currentColor;min-width:5px}.mat-form-field-appearance-outline .mat-form-field-outline-start{border-radius:5px 0 0 5px;border-right-style:none}[dir=rtl] .mat-form-field-appearance-outline .mat-form-field-outline-start{border-right-style:solid;border-left-style:none;border-radius:0 5px 5px 0}.mat-form-field-appearance-outline .mat-form-field-outline-end{border-radius:0 5px 5px 0;border-left-style:none;flex-grow:1}[dir=rtl] .mat-form-field-appearance-outline .mat-form-field-outline-end{border-left-style:solid;border-right-style:none;border-radius:5px 0 0 5px}.mat-form-field-appearance-outline .mat-form-field-outline-gap{border-radius:.000001px;border:1px solid currentColor;border-left-style:none;border-right-style:none}.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:transparent}.mat-form-field-appearance-outline .mat-form-field-outline-thick{opacity:0}.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-start,.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-end,.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-gap{border-width:2px}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline,.mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline{opacity:0;transition:opacity 100ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick,.mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline-thick{opacity:1}.cdk-high-contrast-active .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick{border:3px dashed}.mat-form-field-appearance-outline:not(.mat-form-field-disabled) .mat-form-field-flex:hover .mat-form-field-outline{opacity:0;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-outline:not(.mat-form-field-disabled) .mat-form-field-flex:hover .mat-form-field-outline-thick{opacity:1}.mat-form-field-appearance-outline .mat-form-field-subscript-wrapper{padding:0 1em}.cdk-high-contrast-active .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline{color:GrayText}.mat-form-field-appearance-outline._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-outline,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-start,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-end,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-gap{transition:none}\\n\", \".mat-form-field-appearance-standard .mat-form-field-flex{padding-top:.75em}.mat-form-field-appearance-standard .mat-form-field-underline{height:1px}.cdk-high-contrast-active .mat-form-field-appearance-standard .mat-form-field-underline{height:0;border-top:solid 1px}.mat-form-field-appearance-standard .mat-form-field-ripple{bottom:0;height:2px}.cdk-high-contrast-active .mat-form-field-appearance-standard .mat-form-field-ripple{height:0;border-top:solid 2px}.mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{background-position:0;background-color:transparent}.cdk-high-contrast-active .mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{border-top-style:dotted;border-top-width:2px}.mat-form-field-appearance-standard:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{opacity:1;transform:none;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-standard._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{transition:none}\\n\"], directives: [{ type: i3.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i4.CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: [\"cdkObserveContentDisabled\", \"debounce\"], outputs: [\"cdkObserveContent\"], exportAs: [\"cdkObserveContent\"] }, { type: i3.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { type: i3.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }], animations: [matFormFieldAnimations.transitionMessages], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatFormField, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-form-field', exportAs: 'matFormField', animations: [matFormFieldAnimations.transitionMessages], host: {\n                        'class': 'mat-form-field',\n                        '[class.mat-form-field-appearance-standard]': 'appearance == \"standard\"',\n                        '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n                        '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n                        '[class.mat-form-field-appearance-legacy]': 'appearance == \"legacy\"',\n                        '[class.mat-form-field-invalid]': '_control.errorState',\n                        '[class.mat-form-field-can-float]': '_canLabelFloat()',\n                        '[class.mat-form-field-should-float]': '_shouldLabelFloat()',\n                        '[class.mat-form-field-has-label]': '_hasFloatingLabel()',\n                        '[class.mat-form-field-hide-placeholder]': '_hideControlPlaceholder()',\n                        '[class.mat-form-field-disabled]': '_control.disabled',\n                        '[class.mat-form-field-autofilled]': '_control.autofilled',\n                        '[class.mat-focused]': '_control.focused',\n                        '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n                        '[class.ng-touched]': '_shouldForward(\"touched\")',\n                        '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n                        '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n                        '[class.ng-valid]': '_shouldForward(\"valid\")',\n                        '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n                        '[class.ng-pending]': '_shouldForward(\"pending\")',\n                        '[class._mat-animation-noopable]': '!_animationsEnabled',\n                    }, inputs: ['color'], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [{ provide: MAT_FORM_FIELD, useExisting: MatFormField }], template: \"<div class=\\\"mat-form-field-wrapper\\\">\\n  <div class=\\\"mat-form-field-flex\\\" #connectionContainer\\n       (click)=\\\"_control.onContainerClick && _control.onContainerClick($event)\\\">\\n\\n    <!-- Outline used for outline appearance. -->\\n    <ng-container *ngIf=\\\"appearance == 'outline'\\\">\\n      <div class=\\\"mat-form-field-outline\\\">\\n        <div class=\\\"mat-form-field-outline-start\\\"></div>\\n        <div class=\\\"mat-form-field-outline-gap\\\"></div>\\n        <div class=\\\"mat-form-field-outline-end\\\"></div>\\n      </div>\\n      <div class=\\\"mat-form-field-outline mat-form-field-outline-thick\\\">\\n        <div class=\\\"mat-form-field-outline-start\\\"></div>\\n        <div class=\\\"mat-form-field-outline-gap\\\"></div>\\n        <div class=\\\"mat-form-field-outline-end\\\"></div>\\n      </div>\\n    </ng-container>\\n\\n    <div\\n      class=\\\"mat-form-field-prefix\\\"\\n      *ngIf=\\\"_prefixChildren.length\\\"\\n      (cdkObserveContent)=\\\"updateOutlineGap()\\\"\\n      [cdkObserveContentDisabled]=\\\"appearance != 'outline'\\\">\\n      <ng-content select=\\\"[matPrefix]\\\"></ng-content>\\n    </div>\\n\\n    <div class=\\\"mat-form-field-infix\\\" #inputContainer>\\n      <ng-content></ng-content>\\n\\n      <span class=\\\"mat-form-field-label-wrapper\\\">\\n        <!-- We add aria-owns as a workaround for an issue in JAWS & NVDA where the label isn't\\n             read if it comes before the control in the DOM. -->\\n        <label class=\\\"mat-form-field-label\\\"\\n               (cdkObserveContent)=\\\"updateOutlineGap()\\\"\\n               [cdkObserveContentDisabled]=\\\"appearance != 'outline'\\\"\\n               [id]=\\\"_labelId\\\"\\n               [attr.for]=\\\"_control.id\\\"\\n               [attr.aria-owns]=\\\"_control.id\\\"\\n               [class.mat-empty]=\\\"_control.empty && !_shouldAlwaysFloat()\\\"\\n               [class.mat-form-field-empty]=\\\"_control.empty && !_shouldAlwaysFloat()\\\"\\n               [class.mat-accent]=\\\"color == 'accent'\\\"\\n               [class.mat-warn]=\\\"color == 'warn'\\\"\\n               #label\\n               *ngIf=\\\"_hasFloatingLabel()\\\"\\n               [ngSwitch]=\\\"_hasLabel()\\\">\\n\\n          <!-- @breaking-change 8.0.0 remove in favor of mat-label element an placeholder attr. -->\\n          <ng-container *ngSwitchCase=\\\"false\\\">\\n            <ng-content select=\\\"mat-placeholder\\\"></ng-content>\\n            <span>{{_control.placeholder}}</span>\\n          </ng-container>\\n\\n          <ng-content select=\\\"mat-label\\\" *ngSwitchCase=\\\"true\\\"></ng-content>\\n\\n          <!-- @breaking-change 8.0.0 remove `mat-placeholder-required` class -->\\n          <span\\n            class=\\\"mat-placeholder-required mat-form-field-required-marker\\\"\\n            aria-hidden=\\\"true\\\"\\n            *ngIf=\\\"!hideRequiredMarker && _control.required && !_control.disabled\\\">&#32;*</span>\\n        </label>\\n      </span>\\n    </div>\\n\\n    <div class=\\\"mat-form-field-suffix\\\" *ngIf=\\\"_suffixChildren.length\\\">\\n      <ng-content select=\\\"[matSuffix]\\\"></ng-content>\\n    </div>\\n  </div>\\n\\n  <!-- Underline used for legacy, standard, and box appearances. -->\\n  <div class=\\\"mat-form-field-underline\\\"\\n       *ngIf=\\\"appearance != 'outline'\\\">\\n    <span class=\\\"mat-form-field-ripple\\\"\\n          [class.mat-accent]=\\\"color == 'accent'\\\"\\n          [class.mat-warn]=\\\"color == 'warn'\\\"></span>\\n  </div>\\n\\n  <div class=\\\"mat-form-field-subscript-wrapper\\\"\\n       [ngSwitch]=\\\"_getDisplayedMessages()\\\">\\n    <div *ngSwitchCase=\\\"'error'\\\" [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n      <ng-content select=\\\"mat-error\\\"></ng-content>\\n    </div>\\n\\n    <div class=\\\"mat-form-field-hint-wrapper\\\" *ngSwitchCase=\\\"'hint'\\\"\\n      [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n      <!-- TODO(mmalerba): use an actual <mat-hint> once all selectors are switched to mat-* -->\\n      <div *ngIf=\\\"hintLabel\\\" [id]=\\\"_hintLabelId\\\" class=\\\"mat-hint\\\">{{hintLabel}}</div>\\n      <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n      <div class=\\\"mat-form-field-hint-spacer\\\"></div>\\n      <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n    </div>\\n  </div>\\n</div>\\n\", styles: [\".mat-form-field{display:inline-block;position:relative;text-align:left}[dir=rtl] .mat-form-field{text-align:right}.mat-form-field-wrapper{position:relative}.mat-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-form-field-prefix,.mat-form-field-suffix{white-space:nowrap;flex:none;position:relative}.mat-form-field-infix{display:block;position:relative;flex:auto;min-width:0;width:180px}.cdk-high-contrast-active .mat-form-field-infix{border-image:linear-gradient(transparent, transparent)}.mat-form-field-label-wrapper{position:absolute;left:0;box-sizing:content-box;width:100%;height:100%;overflow:hidden;pointer-events:none}[dir=rtl] .mat-form-field-label-wrapper{left:auto;right:0}.mat-form-field-label{position:absolute;left:0;font:inherit;pointer-events:none;width:100%;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;transform-origin:0 0;transition:transform 400ms cubic-bezier(0.25, 0.8, 0.25, 1),color 400ms cubic-bezier(0.25, 0.8, 0.25, 1),width 400ms cubic-bezier(0.25, 0.8, 0.25, 1);display:none}[dir=rtl] .mat-form-field-label{transform-origin:100% 0;left:auto;right:0}.cdk-high-contrast-active .mat-form-field-disabled .mat-form-field-label{color:GrayText}.mat-form-field-empty.mat-form-field-label,.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label{display:block}.mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{display:none}.mat-form-field-can-float .mat-form-field-autofill-control:-webkit-autofill+.mat-form-field-label-wrapper .mat-form-field-label{display:block;transition:none}.mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label,.mat-input-server[placeholder]:not(:placeholder-shown)+.mat-form-field-label-wrapper .mat-form-field-label{display:none}.mat-form-field-can-float .mat-input-server:focus+.mat-form-field-label-wrapper .mat-form-field-label,.mat-form-field-can-float .mat-input-server[placeholder]:not(:placeholder-shown)+.mat-form-field-label-wrapper .mat-form-field-label{display:block}.mat-form-field-label:not(.mat-form-field-empty){transition:none}.mat-form-field-underline{position:absolute;width:100%;pointer-events:none;transform:scale3d(1, 1.0001, 1)}.mat-form-field-ripple{position:absolute;left:0;width:100%;transform-origin:50%;transform:scaleX(0.5);opacity:0;transition:background-color 300ms cubic-bezier(0.55, 0, 0.55, 0.2)}.mat-form-field.mat-focused .mat-form-field-ripple,.mat-form-field.mat-form-field-invalid .mat-form-field-ripple{opacity:1;transform:none;transition:transform 300ms cubic-bezier(0.25, 0.8, 0.25, 1),opacity 100ms cubic-bezier(0.25, 0.8, 0.25, 1),background-color 300ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-subscript-wrapper{position:absolute;box-sizing:border-box;width:100%;overflow:hidden}.mat-form-field-subscript-wrapper .mat-icon,.mat-form-field-label-wrapper .mat-icon{width:1em;height:1em;font-size:inherit;vertical-align:baseline}.mat-form-field-hint-wrapper{display:flex}.mat-form-field-hint-spacer{flex:1 0 1em}.mat-error{display:block}.mat-form-field-control-wrapper{position:relative}.mat-form-field-hint-end{order:1}.mat-form-field._mat-animation-noopable .mat-form-field-label,.mat-form-field._mat-animation-noopable .mat-form-field-ripple{transition:none}\\n\", \".mat-form-field-appearance-fill .mat-form-field-flex{border-radius:4px 4px 0 0;padding:.75em .75em 0 .75em}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-form-field-flex{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-form-field-flex{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-form-field-flex{outline:dashed 3px}.mat-form-field-appearance-fill .mat-form-field-underline::before{content:\\\"\\\";display:block;position:absolute;bottom:0;height:1px;width:100%}.mat-form-field-appearance-fill .mat-form-field-ripple{bottom:0;height:2px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-form-field-ripple{height:0}.mat-form-field-appearance-fill:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{opacity:1;transform:none;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-fill._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{transition:none}.mat-form-field-appearance-fill .mat-form-field-subscript-wrapper{padding:0 1em}\\n\", \".mat-input-element{font:inherit;background:transparent;color:currentColor;border:none;outline:none;padding:0;margin:0;width:100%;max-width:100%;vertical-align:bottom;text-align:inherit;box-sizing:content-box}.mat-input-element:-moz-ui-invalid{box-shadow:none}.mat-input-element,.mat-input-element::-webkit-search-cancel-button,.mat-input-element::-webkit-search-decoration,.mat-input-element::-webkit-search-results-button,.mat-input-element::-webkit-search-results-decoration{-webkit-appearance:none}.mat-input-element::-webkit-contacts-auto-fill-button,.mat-input-element::-webkit-caps-lock-indicator,.mat-input-element:not([type=password])::-webkit-credentials-auto-fill-button{visibility:hidden}.mat-input-element[type=date],.mat-input-element[type=datetime],.mat-input-element[type=datetime-local],.mat-input-element[type=month],.mat-input-element[type=week],.mat-input-element[type=time]{line-height:1}.mat-input-element[type=date]::after,.mat-input-element[type=datetime]::after,.mat-input-element[type=datetime-local]::after,.mat-input-element[type=month]::after,.mat-input-element[type=week]::after,.mat-input-element[type=time]::after{content:\\\" \\\";white-space:pre;width:1px}.mat-input-element::-webkit-inner-spin-button,.mat-input-element::-webkit-calendar-picker-indicator,.mat-input-element::-webkit-clear-button{font-size:.75em}.mat-input-element::placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element::-moz-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element::-webkit-input-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-input-element:-ms-input-placeholder{-webkit-user-select:none;user-select:none;transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-hide-placeholder .mat-input-element::placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element::-moz-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::-moz-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element::-webkit-input-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element::-webkit-input-placeholder{opacity:0}.mat-form-field-hide-placeholder .mat-input-element:-ms-input-placeholder{color:transparent !important;-webkit-text-fill-color:transparent;transition:none}.cdk-high-contrast-active .mat-form-field-hide-placeholder .mat-input-element:-ms-input-placeholder{opacity:0}textarea.mat-input-element{resize:vertical;overflow:auto}textarea.mat-input-element.cdk-textarea-autosize{resize:none}textarea.mat-input-element{padding:2px 0;margin:-2px 0}select.mat-input-element{-moz-appearance:none;-webkit-appearance:none;position:relative;background-color:transparent;display:inline-flex;box-sizing:border-box;padding-top:1em;top:-1em;margin-bottom:-1em}select.mat-input-element::-moz-focus-inner{border:0}select.mat-input-element:not(:disabled){cursor:pointer}.mat-form-field-type-mat-native-select .mat-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid transparent;border-right:5px solid transparent;border-top:5px solid;position:absolute;top:50%;right:0;margin-top:-2.5px;pointer-events:none}[dir=rtl] .mat-form-field-type-mat-native-select .mat-form-field-infix::after{right:auto;left:0}.mat-form-field-type-mat-native-select .mat-input-element{padding-right:15px}[dir=rtl] .mat-form-field-type-mat-native-select .mat-input-element{padding-right:0;padding-left:15px}.mat-form-field-type-mat-native-select .mat-form-field-label-wrapper{max-width:calc(100% - 10px)}.mat-form-field-type-mat-native-select.mat-form-field-appearance-outline .mat-form-field-infix::after{margin-top:-5px}.mat-form-field-type-mat-native-select.mat-form-field-appearance-fill .mat-form-field-infix::after{margin-top:-10px}\\n\", \".mat-form-field-appearance-legacy .mat-form-field-label{transform:perspective(100px)}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon{width:1em}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon-button,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon-button{font:inherit;vertical-align:baseline}.mat-form-field-appearance-legacy .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field-appearance-legacy .mat-form-field-suffix .mat-icon-button .mat-icon{font-size:inherit}.mat-form-field-appearance-legacy .mat-form-field-underline{height:1px}.cdk-high-contrast-active .mat-form-field-appearance-legacy .mat-form-field-underline{height:0;border-top:solid 1px}.mat-form-field-appearance-legacy .mat-form-field-ripple{top:0;height:2px;overflow:hidden}.cdk-high-contrast-active .mat-form-field-appearance-legacy .mat-form-field-ripple{height:0;border-top:solid 2px}.mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{background-position:0;background-color:transparent}.cdk-high-contrast-active .mat-form-field-appearance-legacy.mat-form-field-disabled .mat-form-field-underline{border-top-style:dotted;border-top-width:2px;border-top-color:GrayText}.mat-form-field-appearance-legacy.mat-form-field-invalid:not(.mat-focused) .mat-form-field-ripple{height:1px}\\n\", \".mat-form-field-appearance-outline .mat-form-field-wrapper{margin:.25em 0}.mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em 0 .75em;margin-top:-0.25em;position:relative}.mat-form-field-appearance-outline .mat-form-field-prefix,.mat-form-field-appearance-outline .mat-form-field-suffix{top:.25em}.mat-form-field-appearance-outline .mat-form-field-outline{display:flex;position:absolute;top:.25em;left:0;right:0;bottom:0;pointer-events:none}.mat-form-field-appearance-outline .mat-form-field-outline-start,.mat-form-field-appearance-outline .mat-form-field-outline-end{border:1px solid currentColor;min-width:5px}.mat-form-field-appearance-outline .mat-form-field-outline-start{border-radius:5px 0 0 5px;border-right-style:none}[dir=rtl] .mat-form-field-appearance-outline .mat-form-field-outline-start{border-right-style:solid;border-left-style:none;border-radius:0 5px 5px 0}.mat-form-field-appearance-outline .mat-form-field-outline-end{border-radius:0 5px 5px 0;border-left-style:none;flex-grow:1}[dir=rtl] .mat-form-field-appearance-outline .mat-form-field-outline-end{border-left-style:solid;border-right-style:none;border-radius:5px 0 0 5px}.mat-form-field-appearance-outline .mat-form-field-outline-gap{border-radius:.000001px;border:1px solid currentColor;border-left-style:none;border-right-style:none}.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:transparent}.mat-form-field-appearance-outline .mat-form-field-outline-thick{opacity:0}.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-start,.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-end,.mat-form-field-appearance-outline .mat-form-field-outline-thick .mat-form-field-outline-gap{border-width:2px}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline,.mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline{opacity:0;transition:opacity 100ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick,.mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline-thick{opacity:1}.cdk-high-contrast-active .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick{border:3px dashed}.mat-form-field-appearance-outline:not(.mat-form-field-disabled) .mat-form-field-flex:hover .mat-form-field-outline{opacity:0;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-outline:not(.mat-form-field-disabled) .mat-form-field-flex:hover .mat-form-field-outline-thick{opacity:1}.mat-form-field-appearance-outline .mat-form-field-subscript-wrapper{padding:0 1em}.cdk-high-contrast-active .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline{color:GrayText}.mat-form-field-appearance-outline._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-outline,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-start,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-end,.mat-form-field-appearance-outline._mat-animation-noopable .mat-form-field-outline-gap{transition:none}\\n\", \".mat-form-field-appearance-standard .mat-form-field-flex{padding-top:.75em}.mat-form-field-appearance-standard .mat-form-field-underline{height:1px}.cdk-high-contrast-active .mat-form-field-appearance-standard .mat-form-field-underline{height:0;border-top:solid 1px}.mat-form-field-appearance-standard .mat-form-field-ripple{bottom:0;height:2px}.cdk-high-contrast-active .mat-form-field-appearance-standard .mat-form-field-ripple{height:0;border-top:solid 2px}.mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{background-position:0;background-color:transparent}.cdk-high-contrast-active .mat-form-field-appearance-standard.mat-form-field-disabled .mat-form-field-underline{border-top-style:dotted;border-top-width:2px}.mat-form-field-appearance-standard:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{opacity:1;transform:none;transition:opacity 600ms cubic-bezier(0.25, 0.8, 0.25, 1)}.mat-form-field-appearance-standard._mat-animation-noopable:not(.mat-form-field-disabled) .mat-form-field-flex:hover~.mat-form-field-underline .mat-form-field-ripple{transition:none}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD_DEFAULT_OPTIONS]\n                }] }, { type: i2.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { appearance: [{\n                type: Input\n            }], hideRequiredMarker: [{\n                type: Input\n            }], hintLabel: [{\n                type: Input\n            }], floatLabel: [{\n                type: Input\n            }], _connectionContainerRef: [{\n                type: ViewChild,\n                args: ['connectionContainer', { static: true }]\n            }], _inputContainerRef: [{\n                type: ViewChild,\n                args: ['inputContainer']\n            }], _label: [{\n                type: ViewChild,\n                args: ['label']\n            }], _controlNonStatic: [{\n                type: ContentChild,\n                args: [MatFormFieldControl]\n            }], _controlStatic: [{\n                type: ContentChild,\n                args: [MatFormFieldControl, { static: true }]\n            }], _labelChildNonStatic: [{\n                type: ContentChild,\n                args: [MatLabel]\n            }], _labelChildStatic: [{\n                type: ContentChild,\n                args: [MatLabel, { static: true }]\n            }], _placeholderChild: [{\n                type: ContentChild,\n                args: [MatPlaceholder]\n            }], _errorChildren: [{\n                type: ContentChildren,\n                args: [MAT_ERROR, { descendants: true }]\n            }], _hintChildren: [{\n                type: ContentChildren,\n                args: [_MAT_HINT, { descendants: true }]\n            }], _prefixChildren: [{\n                type: ContentChildren,\n                args: [MAT_PREFIX, { descendants: true }]\n            }], _suffixChildren: [{\n                type: ContentChildren,\n                args: [MAT_SUFFIX, { descendants: true }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatFormFieldModule {\n}\nMatFormFieldModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatFormFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatFormFieldModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatFormFieldModule, declarations: [MatError, MatFormField, MatHint, MatLabel, MatPlaceholder, MatPrefix, MatSuffix], imports: [CommonModule, MatCommonModule, ObserversModule], exports: [MatCommonModule,\n        MatError,\n        MatFormField,\n        MatHint,\n        MatLabel,\n        MatPlaceholder,\n        MatPrefix,\n        MatSuffix] });\nMatFormFieldModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatFormFieldModule, imports: [[CommonModule, MatCommonModule, ObserversModule], MatCommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatFormFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [MatError, MatFormField, MatHint, MatLabel, MatPlaceholder, MatPrefix, MatSuffix],\n                    imports: [CommonModule, MatCommonModule, ObserversModule],\n                    exports: [\n                        MatCommonModule,\n                        MatError,\n                        MatFormField,\n                        MatHint,\n                        MatLabel,\n                        MatPlaceholder,\n                        MatPrefix,\n                        MatSuffix,\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_ERROR, MAT_FORM_FIELD, MAT_FORM_FIELD_DEFAULT_OPTIONS, MAT_PREFIX, MAT_SUFFIX, MatError, MatFormField, MatFormFieldControl, MatFormFieldModule, MatHint, MatLabel, MatPlaceholder, MatPrefix, MatSuffix, _MAT_HINT, getMatFormFieldDuplicatedHintError, getMatFormFieldMissingControlError, getMatFormFieldPlaceholderConflictError, matFormFieldAnimations };\n"]}, "metadata": {}, "sourceType": "module"}
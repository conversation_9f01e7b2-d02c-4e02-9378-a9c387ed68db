<app-sidebar>
  <div class="content-wrapper fade-in">
    <div class="row mb-4">
      <!--<div class="col-12 col-md-auto col-sm-12 mb-3 mb-md-0">
     <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <li class="breadcrumb-item active" aria-current="page">All Collections</li>
      </ol>
    </nav> 
  </div>-->
  <div class="col-lg-3 mb-2 mb-lg-0">
    <button type="submit" (click)="addRecord()" class="btn btn-primary w-100">Add New Collection</button>
<!-- 
    <a>
      <input type="button" class="btn btn-primary shadow-sm custom-radius-btn" value="Add Collection" (click)="addRecord()"
        data-toggle="modal" data-target="#my-modal">
        
    </a> -->
  </div>
  <div class="col-lg-6">
    <div class="input-group">
      <input type="text" [(ngModel)]="term" class="form-control shadow-sm rounded-start" placeholder="Search now"
        aria-label="Search here" aria-describedby="basic-addon2">
      <!-- <span class="input-group-text bg-primary shadow-sm rounded-end" id="basic-addon2"><i class="icon-search text-white"></i></span> -->
    </div>
  </div>
    </div>
    <div class="row" *ngIf="!showForm">
      <div class="col-lg-12 grid-margin stretch-card">
        <div class="card">
          <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">All collections</div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm table-hover">
                <thead>
                  <tr>
                    <th>Collection Title </th>
                    <th>Collection Description</th>
                    <!-- <th>Collection Image</th> -->

                    <th class="text-center"> Date </th>
                    <!-- <th> Status </th> -->
                    <th class="text-center"> Action </th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let Collection of collectionList |filter: term |paginate : { itemsPerPage:10, currentPage:p1}">
                    <td>{{Collection?.PL_title}}</td>
                    <!--                                         <td> <img src="{{logoSrc+brand.BR_logo}}" alt="image" style="width: 100px;height: auto;" /></td>
 -->
                     <td>
                      <div class="description-content">
                        {{Collection?.PL_description}}
                      </div>
                      </td>
                      
                     <!-- <td>
                      <img [src]="Collection?.PL_dp" class="card-img-top square-img" [alt]="Collection?.PL_dp">
                    </td> -->


                    <!-- <td>{{sound?.AN_updatedBy}}</td> -->

                    <td class="text-center">{{Collection?.PL_createdAt | date: 'MMM d, y'}}</td>
                    <td class="text-center">
                      <div class="btn-group" role="group" aria-label="Basic example">
                        <button type="button" class="btn btn-primary btn-sm" (click)="editRecord(Collection,'Edit')"
                          placement="top" ngbTooltip="Update">
                          <i class="ti-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" (click)="showDeleteModal(Collection)" data-toggle="modal" data-target="#myModal"
                          placement="top" ngbTooltip="Delete">
                          <i class="ti-trash"></i>
                        </button>
                      </div>
                    </td>
                    <!--Division for Modal-->
                    
                  </tr>
                </tbody>
              </table>
              <pagination-controls (pageChange)="p1 = $event" class="ml-1 text-center"></pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-sidebar>


<div id="myModal" class="modal fade" role="dialog">
  <!--Modal-->
  <div class="modal-dialog">
    <!--Modal Content-->
    <div class="modal-content">
      <!-- Modal Header-->
      <div class="modal-header">
        <h3>Delete warning !</h3>
        <!--Close/Cross Button-->
        <button type="button" class="close" data-dismiss="modal"
          style="color: white;">&times;</button>
      </div>
      <!-- Modal Body-->
      <div class="modal-body text-center">
        <i class="ti-trash" style="color: red;"></i>
        <h3> <strong>Are you sure?</strong></h3>
        <p class="mt-3">
          Do you really want to delete this collection? 
        </p>
      </div>

      <!-- Modal Footer-->

      <div class="modal-footer text-center">
        <button (click)="DeleteCollection()" class="btn btn-danger" data-dismiss="modal">Delete</button>
        <a class="btn btn-primary" data-dismiss="modal">Cancel</a>
      </div>

    </div>

  </div>

</div>
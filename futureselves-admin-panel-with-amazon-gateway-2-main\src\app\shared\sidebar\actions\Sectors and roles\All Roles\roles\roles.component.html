<app-sidebar>
    <div class="content-wrapper">

      <div class="row mb-4 mx-2 head-Home">
          <!-- Add and search bar -->
          <div class="col-lg-3 mb-2 mb-lg-0">
            <button type="submit" (click)="showAddNewRoles()" class="btn btn-primary w-100">Add New Role</button>
          </div>
          <div class="col-lg-5">
            <div class="input-group">
              <input type="text" [(ngModel)]="term" class="form-control shadow-sm rounded-start" placeholder="Search here"
                style="width: 100%;" aria-label="Search now">
            </div>
          </div>
          <div class="col-lg-4 mb-2">
            <div class="input-group ">
              <label for="selectStatus" class="input-group-text" style="padding-top: 10px;">Added By :</label>
              <select class="custom-select" id="selectStatus" (change)="selected($event)" style="height: 46px !important;">
              <option value="All"  class="badge" selected>All</option>
              <option value="user" class="badge ">By User</option>
              <option value="" class="badge">By Admin</option>
            </select>
          </div>
          </div>
        </div>  

        <div class="row" *ngIf="showForm">
            <div class="col-lg-12 grid-margin stretch-card">
              <div class="card">
                <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">All Roles</div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-sm table-hover">
                      <thead class="text-center">
                        <tr>
                          <th class="text-left">Role Name</th>
                          <th>Role Description</th>
                          <th>Role Image</th>
                          <th>Is Added By User?</th>
                          <th class="text-center">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let roles of RoleList | filter: term|paginate: {itemsPerPage:7, currentPage:p} ">
                          <td>
                            <div class="description-content">
                              {{roles.RO_title}}
                            </div>
                          </td>

                          <td>
                            <div class="description-content">
                            {{roles.RO_description}}
                          </div>
                          </td>

                          <td class="text-center">
                            <img [src]="roles.RO_dp" class="card-img-top square-img" alt="Role Image">
                          </td>

                          <td class="text-center">{{roles.RO_createdByType=="user"?'Yes':'No'}}</td>

                          <td class="text-center">
                            <div class="btn-group" role="group" aria-label="Basic example">
                              <button type="button" (click)="showEditRole(roles)" class="btn btn-primary btn-sm" 
                                placement="top" ngbTooltip="Update">
                                <i class="ti-pencil"></i>
                              </button>

                              <button type="button" class="btn btn-primary btn-sm"
                                                        (click)="viewRole(roles)" placement="top"
                                                        ngbTooltip="View full details">
                                                        <i class="ti-eye"></i>
                                                    </button>

                              <button type="button" (click)="showDeleteModal(roles.RO_id)" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#myModal"
                                placement="top" ngbTooltip="Delete">
                                <i class="ti-trash"></i>
                              </button>
                            </div>
                          </td>
                          <!--Division for Modal-->
                          <div id="myModal" class="modal fade" role="dialog">
                            <!--Modal-->
                            <div class="modal-dialog">
                              <!--Modal Content-->
                              <div class="modal-content">
                                <!-- Modal Header-->
                                <div class="modal-header">
                                  <h3>Delete warning !</h3>
                                  <!--Close/Cross Button-->
                                  <button type="button" class="close" data-dismiss="modal"
                                    style="color: white;">&times;</button>
                                </div>
                                <!-- Modal Body-->
                                <div class="modal-body text-center">
                                  <i class="ti-trash" style="color: red;"></i>
                                  <h3> <strong>Are you sure?</strong></h3>
                                  <p class="mt-3">Do you really want to delete this role ?</p>
                                  
                                </div>
      
                                <!-- Modal Footer-->
      
                                <div class="modal-footer text-center">
                                  <button (click)="deleteRole()" class="btn btn-danger" data-dismiss="modal">Delete</button>
                                  <a class="btn btn-primary" data-dismiss="modal">Cancel</a>
                                </div>
      
                              </div>
      
                            </div>
      
                          </div>
                        </tr>
                      </tbody>
                    </table>
                    <pagination-controls (pageChange)="p = $event"
                    class="ml-1 text-center"></pagination-controls>
                    <div class="text-center footer">
                      <button class="btn btn-light" routerLink="/actions/sectors">Back</button>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>
        </app-sidebar>
    
        
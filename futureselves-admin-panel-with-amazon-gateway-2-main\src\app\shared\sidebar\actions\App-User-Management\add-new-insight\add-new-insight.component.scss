.title-card{
    width: 100%;
    font-weight: bold;
    // background-color: #FF6F0B;
    // color: #FF6F0B;
}

.position-card {
  cursor: pointer;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out; 
}

.position-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  background-color: #f8f8f8; 
}

.add-new-card {
    background-color: #e9ecef;
    color: #333; 
    text-align: center; 
    cursor: pointer; 
    border: 1px dashed black; 
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out; 
}

.add-new-card:hover {
    transform: translateY(-3px); 
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    background-color: #e9ecef;
}

@media (max-width:980px) {
    .position-card{
        margin-bottom: 10px;
    }
}

.position-card {
    border: 1px solid #ccc; 
  }
  
  .position-card .card-body {
    padding: 1.25rem; 
  }
  
  .position-card .card-body .row {
    margin-bottom: 1rem;
  }
  
  .position-card .card-body .d-flex {
    justify-content: space-between;
    align-items: center;
  }
  
  .position-card .card-body .ml-auto {
    margin-left: auto; 
  }
  
  .position-card .card-body .btn {
    padding: 0.25rem 0.5rem; 
  }
  
  
.form-check {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0%;
  margin: 0%;
}

.form-check-input {
  width: 37px;
  height: 18px;
  margin-left: 8px;
  margin-top: 0%;
  margin-bottom: 4px;
  padding-top: 0%;
  background-color: #e9ecef;
  border: 1px solid #adb5bd;
  border-radius: 22px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
  outline: none;
  position: relative;
  transition: background-color 0.3s ease-in-out;
  order: 2;
}

.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.form-check-input::before {
  content: "";
  display: block;
  width: 16px;
  height: 15px;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 1px;
  left: 1px;
  transition: transform 0.3s ease-in-out;
}

.form-check-input:checked::before {
  transform: translateX(17px);
}

.form-check-label {
  order: 1;
  margin-left: 0%;
  padding-left: 0%;
  font-weight: bold;
}
.modal {
  background-color: rgba(0, 0, 0, 0.6)
}
.inactive-card {
  background-color: #e0e0e0; 
  box-shadow: none; 
}

.inactive-card:hover {
  background-color: #e0e0e0; 
  box-shadow: none;
}

.info-card{
  padding-top: 10px;
  padding-bottom: 10px;
}

.divider{
  margin: 4px !important;
}

.main{
  padding-left: 30px;
  padding-right: 30px;
}

.questions-container{
  height: 335px;
  overflow-y: scroll;
}

.tag-input-container {
  display: flex;
  align-items: flex-start; 
  border: 1px solid #ced4da; 
  border-radius: 5px; 
  padding: 5px; 
  overflow-x: auto; 
  overflow-y: hidden; 
  height: 50px;
}


.tag.view-mode {
  background-color: #e2e4e7;
  color: #495057;
}

.tag-input-container:focus-within{
  outline: 1px solid rgba(0, 123, 255, 0.65); 
  transition: outline 0.3s ease; 

}

.tag-box {
  display: inline-flex; 
  flex-wrap: nowrap;
  gap: 5px;
}

.tag {
  background-color: #4B49AC;
  color: #fff;
  padding: 2px 8px;
  margin-top: 2px;
  margin-bottom: 2px;
  border-radius: 10px;
  display: flex; 
  align-items: center;
  max-height: auto;
  white-space: nowrap; 
  overflow: hidden; 
}

.tag .close {
  margin-left: 5px;
  margin-top: 2px;
  margin-bottom: 2px;
  cursor: pointer;
  border: none;
  outline: none;
  color: #fff;
}

.tag-input {
  border: none;
  outline: none;
  margin:2px 5px 2px 3px; 
  max-height: 30px;
  min-width: 150px;
}

.tag-input:focus-within{
  border: none;
  outline: none;
}

.tag-input-container::-webkit-scrollbar {
  width: 4px; 
}

.tag-input-container::-webkit-scrollbar-thumb {
  background-color: #888; 
  border-radius: 4px;
}

/* For Firefox */
.tag-input-container {
  scrollbar-width: thin; 
}

.tag-input-container::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
}
.logo-input-container {
  position: relative;
}

.logo-input-container img {
  position: absolute;
  top: 50%;
  right: 10px; 
  transform: translateY(-50%);
  max-width: 50px; 
  max-height: 38px; 

}
input[type="text"],
input[type="file"],
input[type="number"],
select {
  height: 50px; 
}

@media (max-width: 768px) {
  .question{
    font-size: smaller;
    min-width: 185px;
  } 

  .position-card{
    padding: 10px !important;
  }

.icon{
  font-size: smaller;
  padding: 0px !important;
}

.btn {
  margin-left: 8px;
  margin-bottom: 5px;
  margin-right: 5px !important;
}


.questions-container{
  height: auto;
  overflow-y: none;
}

.info-card{
  margin-bottom: 6px;
}
}

@media (max-width: 320px) {
  .question{
    font-size: smaller;
    min-width: 135px;
  } 
}

@media (min-width: 321px) and (max-width: 375px) {
  .question{
    font-size: smaller;
    min-width: 180px;
  } 
}

@media (min-width: 376px) and (max-width: 480px) {
  .question{
    font-size: smaller;
    min-width: 240px;
  } 
}

@media (min-width: 320px) and (max-width: 1025px) {
  .main{
    padding-left: 0px;
    padding-right: 0px;
  }
}
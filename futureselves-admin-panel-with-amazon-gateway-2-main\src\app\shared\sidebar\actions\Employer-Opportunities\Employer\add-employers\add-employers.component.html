<app-sidebar>
    <div class="content-wrapper">
<div class="row"><!-- Add new Company form -->

    <div class="col-12 grid-margin stretch-card">
        <div class="card">
            <div class="card-header card-title bg-primary rounded-top text-white text-center mb-0">{{title}}
                Employer</div>
            <div class="card-body">
                <form (ngSubmit)="onSubmit()" [formGroup]="addNewCompanyForm" class="forms-sample"
                    enctype="multipart/form-data">
                    <div class="row">
                        <div class="form-group col-lg-4">
                            <label for="CO_companyName" class="required-field">Employer Name</label>
                            <input type="text" class="form-control form-control-sm"
                                formControlName="CO_companyName" required [readonly]="isReadonly" placeholder="Enter Name">
                        </div>
                        <div class="form-group col-lg-4">
                            <label for="CO_logo" class="required-field">Employer Logo</label>
                            <div class="logo-input-container">
                                <input type="file" class="form-control form-control-sm"
                                    formControlName="CO_logo" (change)="onFileSelected($event);" required
                                    [readonly]="isReadonly" accept="image/*">
                                <img *ngIf="imageSrc" [src]="imageSrc" alt="Employer Logo" class="img-preview">
                            </div>
                            <!-- <small class="existing-logo" *ngIf="this.title === 'Edit' && ExistingLogo">
                              <strong>Existing Logo Name: </strong> 
                              <span class="logo-name">{{ ExistingLogo }}</span>
                          </small> -->
                         <div *ngIf="this.addNewCompanyForm.get('CO_logo')?.errors?.fileSizeValidator" class="warning">
                              The file size exceeds the 2 MB limit. Please select a smaller file.
                          </div> 
                          <div *ngIf="addNewCompanyForm.get('CO_logo')?.errors?.fileAspectRatioValidator" class="warning">
                            The image must have a 1:1 aspect ratio. Please select a square image.
                          </div> 

                          <a *ngIf="!isCropperVisible && imageName && !addNewCompanyForm.get('CO_logo')?.errors?.fileSizeValidator" class="btn-custom-small" (click)="showCropper('CO_logo')">Edit Logo</a>
                        </div>

                        <div *ngIf="isCropperVisible&&!addNewCompanyForm.get('CO_logo')?.errors?.fileSizeValidator" class="cropper-container  col-lg-12">
                          <div class="image-cropper mb-2 text-center">
                            <h5 class="py-2">Resize Image</h5>
                            <image-cropper class="custom-image-cropper my-2"
                            [imageChangedEvent]="imgChangeEvt"
                            [aspectRatio]="1/1"
                            [maintainAspectRatio]="true"
                            format="png"
                            (imageCropped)="cropImg($event)"
                            (imageLoaded)="imgLoad()"
                            (cropperReady)="initCropper()"
                            (loadImageFailed)="imgFailed()">
                            </image-cropper> 
                            <button class="btn btn-success btn-sm m-2" (click)="saveCroppedImage('CO_logo')">Save</button>
                            <button class="btn btn-secondary btn-sm m-2" (click)="hideCropper('CO_logo')">Cancel</button>
                          </div>
                          </div>


                        <div class="form-group col-lg-4">
                            <label for="CO_location" class="required-field">Employer Location</label>
                            <input type="text" class="form-control form-control-sm"
                                formControlName="CO_location" required [readonly]="isReadonly" placeholder="e.g. London, UK">
                        </div>

                      <div class="form-group col-lg-4">
                        <label for="CO_type" class="required-field">Employer Type</label>
                        <select id="CO_type" class="form-control form-control-sm" formControlName="CO_type" required (change)="onTypeChange($event)">
                            <option value="" disabled selected>Please select type</option>
                            <option value="Private Limited Company(LTD)">Private Limited Company(LTD)</option>
                            <option value="Public Limited Company(PLC)">Public Limited Company(PLC)</option>
                            <option value="Partnership">Partnership</option>
                            <option value="Non-Profit Organization">Non-Profit Organization</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
            
                    <div *ngIf="showOtherTypeInput" class="form-group col-lg-4">
                        <label for="CO_otherType" class="required-field">Specify Other Type</label>
                        <input type="text" class="form-control form-control-sm" formControlName="CO_otherType" placeholder="Enter Type">
                    </div>

                        <div class="form-group col-lg-4">
                            <label for="CO_founded" class="required-field">Founded</label>
                            <input type="text" mask="0000" class="form-control form-control-sm"
                                formControlName="CO_founded" required [readonly]="isReadonly" placeholder="Enter Year Only">
                        </div>
                        
                        <div class="form-group col-lg-4">
                            <label for="CO_sectorId" class="required-field">Employer Sector</label>
                            <select id="companySector" class="form-control form-control-sm"
                                formControlName="CO_sectorId" required>
                                <option value="" disabled selected>Please select sector</option>
                                <option *ngFor="let sector of SectorList" [value]="sector.IN_id">{{
                                    sector.IN_name }}</option>
                            </select>
                            <!-- <div class="invalid-feedback">
                                Please select Employer sector.
                            </div> -->
                        </div>

                      
                          
                        <div class="form-group col-lg-4">
                            <label for="CO_website" class="required-field">Website</label>
                            <input type="text" class="form-control form-control-sm"
                                formControlName="CO_website" required [readonly]="isReadonly" placeholder="Enter URL">
                                <div *ngIf="addNewCompanyForm.get('CO_website')?.invalid && addNewCompanyForm.get('CO_website')?.touched"
                                     class="warning">
                                  <div *ngIf="addNewCompanyForm.get('CO_website')?.errors?.required">Website is required.</div>
                                  <div *ngIf="addNewCompanyForm.get('CO_website')?.errors?.pattern">
                                    Please enter a valid URL starting with http:// or https://.
                                  </div>
                                </div>
                        </div>

                        <div class="form-group col-lg-4">
                            <label for="CO_size" class="required-field">Employer Size</label>
                            <!-- <input type="text" class="form-control form-control-sm"
                                formControlName="CO_size" required [readonly]="isReadonly" placeholder="Enter Website"> -->

                                <select id="CO_size" class="form-control form-control-sm"
                                formControlName="CO_size" required>
                                <option value="" disabled selected>Please select size</option>
                                <option value="1-9"> 1-9 Employee </option>
                                <option value="10-49">10-49 Employee </option>
                                <option value="50-499">50-499 Employee</option>
                                <option value="500-999">500-999 Employee </option>
                                <option value="1,000-4,999"> 1,000-4,999 Employee</option>
                                <option value="5,000+"> 5,000+ Employees</option>
                            </select>
                        </div>

                        <div class="form-group col-lg-4">
                          <label for="CO_about" class="required-field">About</label>
                          <textarea class="form-control form-control-sm" id="CO_about"
                            formControlName="CO_about" required [readonly]="isReadonly" rows="3"
                            cols="50" placeholder="Enter About"
                            maxlength="200" (input)="onTextChange($event)">
                          </textarea>
                          <div class="character-count">
                            {{ characterCount }}/500
                          </div>
                          <div class="warning"
                            *ngIf="addNewCompanyForm.get('CO_about')?.errors && addNewCompanyForm.get('CO_about')?.errors?.maxlength">
                            Error: Character limit exceeded! (500 Characters)
                          </div>
                          
                        </div>

                    </div>
                    <hr>

                    <div formArrayName="CO_HrInsights">
                        <h6 class="required-field mb-3 py-2">HR / Hiring Manager</h6>
                        <div *ngFor="let insight of insightFormArray.controls; let i=index" [formGroupName]="i" class="row mb-3">
                           
                            <div class="col-lg-11">

                            <div class="row mr-0">
                          <div class="form-group col-lg-3">
                            <label class="required-field subtitle" for="HRI_title">Insight Title</label>
                            <input type="text" class="form-control form-control-sm" formControlName="HRI_title" required placeholder="Enter Title">
                          </div>
                          <div class="form-group col-lg-3">
                            <label for="HRI_name" class="required-field subtitle">Name</label>
                            <input type="text" class="form-control form-control-sm" formControlName="HRI_name" required placeholder="Enter Name">
                          </div>
                          <div class="form-group col-lg-3">
                            <label for="HRI_position" class="required-field subtitle">Position</label>
                            <input type="text" class="form-control form-control-sm" formControlName="HRI_position" required placeholder="Enter Position">
                          </div>
                          <div class="form-group col-lg-3">
                            <label for="link" class="required-field subtitle">Upload Insight</label>
                            <input type="file" id="link" class="form-control form-control-sm"
                                   (change)="onAudioSelected($event, i)"
                                   accept="audio/*">
                                   <!-- <small><strong>Existing Audio Name: </strong> {{ existingAudioNames[i] }}</small><br> -->
                                  </div>
                        </div>

                    </div>

                          <div class="col-lg-1 px-0 d-flex align-items-center btns">
                            <button *ngIf="audioUrls[i]" type="button" class="btn insight-btn btn-sm mr-2" [ngClass]="{'btn-outline-primary': !isPlaying[i], 'btn-outline-secondary': isPlaying[i]}" (click)="toggleAudio(i)">
                                <i class="fas" [ngClass]="isPlaying[i] ? 'fa-pause' : 'fa-play'"></i>
                              </button>
                            <button *ngIf="insightFormArray.length > 1" type="button" class="btn insight-btn btn-sm btn-outline-danger" (click)="removeInsight(i)">
                              <i class="fas fa-minus icon"></i>
                            </button>

                            
                              <audio #audioPlayer controls style="display: none;">
                                <source [src]="audioUrls[i]" type="audio/mpeg">
                                Your browser does not support the audio element.
                              </audio>
                          </div>
                          
                        </div>
                        
                        <button type="button" *ngIf="insightFormArray.length < 3" class="btn btn-sm add-insight-btn btn-outline-primary" (click)="addInsight()">Add Insight
                          <i class="fas fa-plus icon"></i>
                        </button>
                      </div>
                      
                                   

                    <div class="text-center mt-4">
                        <button *ngIf="!isReadonly" type="submit" class="btn btn-primary mr-2">Save</button>
                        <button class="btn btn-light" type="button" routerLink="/actions/employer-opportunities">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</div>
</app-sidebar>
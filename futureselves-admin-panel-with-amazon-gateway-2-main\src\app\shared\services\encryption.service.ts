import { Injectable } from '@angular/core';
import * as CryptoJS from 'crypto-js';
import { Constants } from 'src/app/config/constants';
@Injectable({
  providedIn: 'root'
})
export class EncryptionService {

  private key = CryptoJS.enc.Utf8.parse(Constants.secretKey);

  constructor() {}

  decrypt(encryptedData: string): string {
    try {

      if (!encryptedData || typeof encryptedData !== 'string') {
        console.warn('Invalid encrypted data: data is null or not a string');
        return 'Decryption Error'; 
      }

      const textParts = encryptedData.split(':');
      
      if (textParts.length < 2) {
        console.warn('Invalid encrypted format: expected at least 2 parts (IV and encrypted data)');
        return 'Decryption Error';
      }

      const iv = CryptoJS.enc.Hex.parse(textParts.shift()!); 
      const encrypted = CryptoJS.enc.Hex.parse(textParts.join(':')); 

      const decrypted = CryptoJS.AES.decrypt(
        CryptoJS.lib.CipherParams.create({
          ciphertext: encrypted
        }),
        this.key,
        {
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7
        }
      );

      const utf8String = decrypted.toString(CryptoJS.enc.Utf8);

      if (!utf8String) {
        console.warn('Decryption resulted in empty UTF-8 string');
        return 'Decryption Error';
      }

      return utf8String;

    } catch (error) {
      console.error('Decryption Error:', error);
      return 'Decryption Error'; 
    }
  }
}

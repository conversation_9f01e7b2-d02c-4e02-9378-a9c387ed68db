
  .footer{
  background-color: white;
  }
  .head-exst{
    width: 100vh;
  }

  .warning{
    color: rgb(238, 12, 12);
    font-size: smaller ;
    margin-top: 4px;
  }

  
 .fa-plus{
  font-size: small;
 }

 .subtitle{
  color: rgba(81, 80, 80, 0.856) !important;
 }

 .radio-button-label {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
.radio-button-label input[type="radio"] {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}
.radio-button-group {
  display: flex;
  align-items: center;
}
.character-count{
  font-size: smaller;
  margin-top: 2px;
  display: flex;
  justify-content: flex-end;
}

@media screen and (min-width: 320px) and (max-width: 768px) {
.edit-btn{
  margin-top: 10px;
}
}
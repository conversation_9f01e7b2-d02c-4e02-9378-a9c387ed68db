// .custom-shadow {
//   box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
// }

// .img-size {
//   height: 35px;
//   width: 40px;
// }

// .card {
//   height: 130px;
// }

.container-scroll {
  height: 100%;
  /* Set a fixed height */
  overflow-y: auto;
  /* Enable vertical scroll */
}

// .card-text {
//   font-size: 14px;
// }

.card-title {
  font-size: 15px;
}



.addNewBtn{
  position: absolute;
  right: 20px;
  font-size: 0.9rem;
  cursor: pointer;
}

/* Media query for tablet screens */
@media screen and (min-width: 768px) and (max-width: 1024px) {
  .card-title {
    font-size: 13px;
  }

  .card-text {
    font-size: 10px;
  }
}

.preferences-row {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 20px;
  background-color: #fff;
}

.identity-row {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 20px;
  background-color: #fff;
}

.heading {
  display: block;
  width: 100%;
  text-align: center;
  padding-top: 15px;
  padding-bottom: 15px;
  background-color: #ffffff;
  border-radius: 10px;
  font-weight: bold;
}

.section {
  display: block;
  width: 100%;
  background-color: #ffffff;
  border-radius: 10px;
}


.tag {
  position: relative;
  padding: 7px 25px 7px 15px;
  border: 1px solid #ddd;
  border-radius: 18px;
  background-color: #e6e6e6;
  cursor: pointer;
  margin: 5px;
  transition: background-color 0.3s;
  font-size: 0.9rem;
  margin-left: 20px;
}

.tag.selected {
  // background-color:#4B49AC;
  background-color: #FF6B0B;
  color: #ffffff;
}

.check-mark {
  display: none;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  margin-left: 5px;
}

.tag.selected .check-mark {
  display: inline;
}


.label {
  font-size: 1rem;
  font-weight: bold;
}

/* Container for autocomplete input and dropdown */
// .
.autocomplete-container {
  position: relative;
  width: 100%;
}

.tag-input-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
  padding-right:30px;
}

.selected-options {
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 5px;
  padding: 9px 10px 9px 10px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background-color: #e6e6e6;
  cursor: pointer;
  background-color: #FF6B0B;
  color: #ffffff;
}

.selected-option{
  font-size: 1rem;
}




.remove-selected-option {
  margin-left: 0.2rem;
  font-size: 1rem;
  color: #fff;
  cursor: pointer;
  background: none;
  border: none;
}

.remove-selected-option:hover{
  transform: scale(1.1);
}

.tag-input {
  height: 48px;
  padding: 10px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 5px;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #ced4da;
  border-top: none;
  border-radius: 0 0 5px 5px;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dropdown-list ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.dropdown-list ul li {
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dropdown-list ul li:hover {
  background-color: #f0f0f0;
}

.no-options-message {
  padding: 10px;
  font-size: 14px;
  color: #777777;
}

.tag-input:focus {
  outline: none;
  border-color: #80bdff;
}

.form-check {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0%;
  margin: 0%;
}

.form-check-input {
  width: 39px;
  height: 20px;
  margin-left: 10px;
  margin-top: 0%;
  margin-bottom: 4px;
  padding-top: 0%;
  background-color: #e9ecef;
  border: 1px solid #adb5bd;
  border-radius: 22px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
  outline: none;
  position: relative;
  transition: background-color 0.3s ease-in-out;
  order: 2;
}

.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.form-check-input::before {
  content: "";
  display: block;
  width: 17px;
  height: 16px;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 1px;
  left: 1px;
  transition: transform 0.3s ease-in-out;
}

.form-check-input:checked::before {
  transform: translateX(18px);
}

.form-check-label {
  order: 1;
}


.form-check-input-head {
  width: 37px;
  height: 18px;
  margin-left: 10px;
  background-color: #e9ecef;
  border: 1px solid #adb5bd;
  border-radius: 22px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
  outline: none;
  position: relative;
  transition: background-color 0.3s ease-in-out;
  order: 2;
}

.form-check-input-head:checked {
  background-color: #0d6dfd7b;
  border-color: #0d6dfd6c;
}

.form-check-input-head::before {
  content: "";
  display: block;
  width: 16px;
  height: 14px;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 1px;
  left: 1px;
  transition: transform 0.3s ease-in-out;
}

.toggle-password {
  cursor: pointer;
  right: 25px;
  top: 45px;
}

.imageNameBox{
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  right: 80px; 
  max-width: 400px; 
  max-height: 36px; 
  background-color: white;
  outline: none;
  border: none;
}

@media screen and (max-width: 767px) {

  .logo-input-container img {
    right: 5px; 
    max-width: 25px; 
    min-height: 33px; 
  }

  .submit-btn{
    margin-bottom: 10px;
  }

  .form-group{
    padding-left: 0px !important;
  }

}
.text-danger {
  color: red;
  font-size: 0.875em;
  margin-top: 0.25em;
}

.modal-header{
  color: white;
  background-color: #007bff;
}

.add-university-modal,
.add-degree-modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.add-university-modal .modal-dialog,
.add-degree-modal .modal-dialog {
  margin-top: 0px !important;
}
    




// @media (min-width: 768px) {
//   .expert-msg-label {
//     font-size: 0.8vw; 
//   }
// }



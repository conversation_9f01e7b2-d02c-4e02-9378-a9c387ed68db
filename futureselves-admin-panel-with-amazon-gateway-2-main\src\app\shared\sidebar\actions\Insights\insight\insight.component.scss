// @import url('https://fonts.googleapis.com/css2?family=San+Francisco:wght@400;500;600;700&display=swap');
.modal-dialog{
    height: 50%;
    width: 50%;
    margin:auto;
}


.modal-header{
    color: white;
    background-color: #007bff;
}

textarea{ border: none; 
    box-shadow: none !important;
    -webkit-appearance:none;
    outline:0px !important;
    border: 1px solid black;
}

.openmodal{
    margin-left: 40%;
    width: 25%;
    margin-top: 5%;
}

.icon1{
   color: #007bff;

}

a{
    margin: auto;
}
.form-check{
    padding-left: 5%;
}

#left{
position: absolute;
left: 50px;
}
.mar-top{
    margin-top:150px;
}
.Gnrt-Transcription-btn{
    margin-top: 8px;
    margin-left: 7px;
}
// #right{
//     position: absolute;
//     right: 100px;
// }
// input{
//     color: #007bff;
    

// }


.name-lable{
    cursor: pointer;
}

// @keyframes fadeIn {
//     0% {
//       opacity: 0;
//       transform: translateY(-20px); 
//     }
//     100% {
//       opacity: 1;
//       transform: translateY(0);
//     }
//   }
  
//   .fade-in {
//     opacity: 0;
//     animation: fadeIn 0.5s ease-in forwards;
//   }
  



  
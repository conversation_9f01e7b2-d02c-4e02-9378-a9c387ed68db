{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\nexport function delay(due, scheduler = asyncScheduler) {\n  const duration = timer(due, scheduler);\n  return delayWhen(() => duration);\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/operators/delay.js"], "names": ["asyncScheduler", "<PERSON><PERSON>hen", "timer", "delay", "due", "scheduler", "duration"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,OAAO,SAASC,KAAT,CAAeC,GAAf,EAAoBC,SAAS,GAAGL,cAAhC,EAAgD;AACnD,QAAMM,QAAQ,GAAGJ,KAAK,CAACE,GAAD,EAAMC,SAAN,CAAtB;AACA,SAAOJ,SAAS,CAAC,MAAMK,QAAP,CAAhB;AACH", "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\nexport function delay(due, scheduler = asyncScheduler) {\n    const duration = timer(due, scheduler);\n    return delayWhen(() => duration);\n}\n"]}, "metadata": {}, "sourceType": "module"}
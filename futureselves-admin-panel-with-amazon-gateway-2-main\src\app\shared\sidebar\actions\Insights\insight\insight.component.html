<app-sidebar>
  <div class="content-wrapper fade-in">

    <div class="row clearfix">
      <div class="col-md-3">
        <nav class="name-lable" (click)="clearAllfilter()" aria-label="breadcrumb">
          <ol class="breadcrumb d-flex justify-content-center ">
            <li  class="breadcrumb-item active" aria-current="page">All Insights</li>
          </ol>
        </nav>
      </div>

      <div class="col-md-5 mb-2">
      <div class="input-group">
           <input type="text" [(ngModel)]="term" (input)="p=1" class="form-control shadow-sm rounded-start" placeholder="Search now"
          aria-label="Search here" aria-describedby="basic-addon2">
       <!-- <span class="input-group-text bg-primary shadow-sm rounded-end" style="height: 46px !important;"
          id="basic-addon2">
           <i class="icon-search text-white"></i> 
        </span>-->
      </div>
      </div>

      <div class="col-md-4 mb-2">
        <div class="input-group ">
          <label for="selectStatus" class="input-group-text" style="padding-top: 10px;">Status:</label>
          <select class="custom-select" id="selectStatus" (change)="selected($event)" style="height: 46px !important;">
          <option value="All" class="badge" selected>All Status</option>
          <option value="0" class="badge ">Draft</option>
          <option value="1" class="badge ">Pending</option>
          <option value="2" class="badge ">Approve</option>
          <option value="3" class="badge ">Reject</option>
        </select>
      </div>
      </div>

    </div>
  
    
    <div class="row">
      <div class="col-lg-12 grid-margin stretch-card">
        <div class="card" id="insightTable">
          <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">All Insights</div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm table-hover">
                <thead>
                  <tr>
                    <th> Sharer Name </th>
                    <th> Sector </th>
                    <th> Role </th>
                    <th> Questions </th>
                    <th> Is Public </th>
                    <th> Created Date </th>
                    <th> Status </th>
                    <th> Action </th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let sound of soundList|filter: term |paginate : { itemsPerPage:7, currentPage:p}">
                    <td>{{sound?.U_name}}</td>
                    <td>
                      <div class="text-wrap">
                        {{sound?.IN_name}}
                      </div>
                    </td>
                    <td>
                      <div class="text-wrap" style="width: 190px;">
                        {{sound?.RO_title}}
                      </div>
                    </td>
                    <td>
                      <div class="text-wrap" style="width: 260px;">
                        {{sound?.AN_title}}
                      </div>
                    </td>
                    <td class="text-center">
                      {{sound.AN_ispublic==false ? 'No' : 'Yes'}}
                    </td>
                    <td>{{sound?.AN_createdAt | date}}</td>
                    <!-- <td>{{sound?.AN_status}}</td> -->
                    <!-- <td><some-element></some-element></td> -->
                    <td><span *ngIf="sound.AN_status==0 && sound.AN_isPublished==0" class="badge badge-pill badge-secondary"> Draft</span>
                      <span *ngIf="sound.AN_status==1 && sound.AN_isPublished==1" class="badge badge-pill bg-warning text-dark"> Pending</span>
                      <span *ngIf="sound.AN_status==2 && sound.AN_isPublished==2" class="badge badge-pill badge-success"> Approved</span>
                      <span *ngIf="sound.AN_status==3 && sound.AN_isPublished==3" class="badge badge-pill badge-danger"> Reject</span>
                      <span *ngIf="sound.AN_status==4 && sound.AN_isPublished==4" class="badge badge-pill bg-info text-white"> Deleted By Sharer</span>

                    </td>
                    <!-- <div *ngIf="this.filtervalue==0"> 
                   <td> <span *ngIf="sound.AN_status==0" class="badge badge-pill badge-secondary">{{sound?.AN_status}} Draft</span></td>
                    </div>  -->
                    <!-- <div *ngIf="this.filtervalue==1">
                   <td> <span *ngIf="sound.AN_status==1" class="badge badge-pill bg-warning text-dark">{{sound?.AN_status}} Pending</span></td>
                  </div> -->
                    <!-- <div *ngIf="this.filtervalue==2">
                    <td> <span *ngIf="sound.AN_status==2" class="badge badge-pill badge-success">{{sound?.AN_status}} Approve</span></td>
                    </div> -->
                    <!-- <div *ngIf="this.filtervalue==3">
                     <td> <span *ngIf="sound.AN_status==3" class="badge badge-pill badge-danger">{{sound?.AN_status}} Reject</span></td>
                      </div> -->
                    <!-- <td> {{brand.BR_createdAt| date }} </td>
                                    <td> {{brand.BR_updatedAt| date }}</td> -->
                                    
                    <td>
                      <div role="group" aria-label="Basic example">
                        <button *ngIf="(sound.AN_isPublished==1||sound.AN_status==1)||(sound.AN_isPublished==2||sound.AN_status==2)||(sound.AN_isPublished==3||sound.AN_status==3)" type="button" class="btn btn-primary btn-sm" (click)="editRecord(sound,'edit')"
                          placement="top" ngbTooltip="Update">
                          <i class="ti-pencil"></i>
                        </button>
                        <!-- <button type="button" class="btn btn-primary btn-sm" [routerLink]=""
                          placement="top" ngbTooltip="View" (click)="editRecord(sound,'View')">
                          <i class="ti-eye"></i>
                        </button> -->
                        <!-- <button type="button" class="btn btn-primary btn-sm"
                                                     data-record-id="54" data-record-title="Something cool" data-toggle="modal" data-target="#confirm-delete">
                                                    <i class="ti-trash"></i>
                                                </button> -->
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <pagination-controls (pageChange)="p = $event" class="ml-1 text-center"></pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </app-sidebar>
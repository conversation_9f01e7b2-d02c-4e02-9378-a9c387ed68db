{"ast": null, "code": "/**\n * @license Angular v13.0.3\n * (c) 2010-2021 Google LLC. https://angular.io/\n * License: MIT\n */\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, ɵparseCookieValue, XhrFactory as XhrFactory$1 } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, PLATFORM_ID, NgModule } from '@angular/core';\nimport { of, Observable } from 'rxjs';\nimport { concatMap, filter, map } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\n\nclass HttpHandler {}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\n\n\nclass HttpBackend {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\n\n\nclass HttpHeaders {\n  /**  Constructs a new HTTP header object with the given values.*/\n  constructor(headers) {\n    /**\n     * Internal map of lowercased header names to the normalized\n     * form of the name (the form seen first).\n     */\n    this.normalizedNames = new Map();\n    /**\n     * Queued updates to be materialized the next initialization.\n     */\n\n    this.lazyUpdate = null;\n\n    if (!headers) {\n      this.headers = new Map();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        headers.split('\\n').forEach(line => {\n          const index = line.indexOf(':');\n\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const key = name.toLowerCase();\n            const value = line.slice(index + 1).trim();\n            this.maybeSetNormalizedName(name, key);\n\n            if (this.headers.has(key)) {\n              this.headers.get(key).push(value);\n            } else {\n              this.headers.set(key, [value]);\n            }\n          }\n        });\n      };\n    } else {\n      this.lazyInit = () => {\n        this.headers = new Map();\n        Object.keys(headers).forEach(name => {\n          let values = headers[name];\n          const key = name.toLowerCase();\n\n          if (typeof values === 'string') {\n            values = [values];\n          }\n\n          if (values.length > 0) {\n            this.headers.set(key, values);\n            this.maybeSetNormalizedName(name, key);\n          }\n        });\n      };\n    }\n  }\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n\n\n  has(name) {\n    this.init();\n    return this.headers.has(name.toLowerCase());\n  }\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n\n\n  get(name) {\n    this.init();\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n\n\n  keys() {\n    this.init();\n    return Array.from(this.normalizedNames.values());\n  }\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n\n\n  getAll(name) {\n    this.init();\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n\n\n  append(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or overide for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n\n\n  set(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n\n\n  delete(name, value) {\n    return this.clone({\n      name,\n      value,\n      op: 'd'\n    });\n  }\n\n  maybeSetNormalizedName(name, lcName) {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n\n  init() {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n\n      this.lazyInit = null;\n\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach(update => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n\n  copyFrom(other) {\n    other.init();\n    Array.from(other.headers.keys()).forEach(key => {\n      this.headers.set(key, other.headers.get(key));\n      this.normalizedNames.set(key, other.normalizedNames.get(key));\n    });\n  }\n\n  clone(update) {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n\n  applyUpdate(update) {\n    const key = update.name.toLowerCase();\n\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value;\n\n        if (typeof value === 'string') {\n          value = [value];\n        }\n\n        if (value.length === 0) {\n          return;\n        }\n\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n\n      case 'd':\n        const toDelete = update.value;\n\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n\n          if (!existing) {\n            return;\n          }\n\n          existing = existing.filter(value => toDelete.indexOf(value) === -1);\n\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n\n        break;\n    }\n  }\n  /**\n   * @internal\n   */\n\n\n  forEach(fn) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\n\n\nclass HttpUrlEncodingCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key) {\n    return standardEncoding(key);\n  }\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n\n\n  encodeValue(value) {\n    return standardEncoding(value);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n\n\n  decodeKey(key) {\n    return decodeURIComponent(key);\n  }\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n\n\n  decodeValue(value) {\n    return decodeURIComponent(value);\n  }\n\n}\n\nfunction paramParser(rawParams, codec) {\n  const map = new Map();\n\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach(param => {\n      const eqIdx = param.indexOf('=');\n      const [key, val] = eqIdx == -1 ? [codec.decodeKey(param), ''] : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n\n  return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\n\n\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '2B': '+',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/'\n};\n\nfunction standardEncoding(v) {\n  return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\n\nfunction valueToString(value) {\n  return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\n\n\nclass HttpParams {\n  constructor(options = {}) {\n    this.updates = null;\n    this.cloneFrom = null;\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n\n    if (!!options.fromString) {\n      if (!!options.fromObject) {\n        throw new Error(`Cannot specify both fromString and fromObject.`);\n      }\n\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map();\n      Object.keys(options.fromObject).forEach(key => {\n        const value = options.fromObject[key];\n        this.map.set(key, Array.isArray(value) ? value : [value]);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n\n\n  has(param) {\n    this.init();\n    return this.map.has(param);\n  }\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n\n\n  get(param) {\n    this.init();\n    const res = this.map.get(param);\n    return !!res ? res[0] : null;\n  }\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n\n\n  getAll(param) {\n    this.init();\n    return this.map.get(param) || null;\n  }\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n\n\n  keys() {\n    this.init();\n    return Array.from(this.map.keys());\n  }\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n\n\n  append(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'a'\n    });\n  }\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n\n\n  appendAll(params) {\n    const updates = [];\n    Object.keys(params).forEach(param => {\n      const value = params[param];\n\n      if (Array.isArray(value)) {\n        value.forEach(_value => {\n          updates.push({\n            param,\n            value: _value,\n            op: 'a'\n          });\n        });\n      } else {\n        updates.push({\n          param,\n          value: value,\n          op: 'a'\n        });\n      }\n    });\n    return this.clone(updates);\n  }\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n\n\n  set(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 's'\n    });\n  }\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n\n\n  delete(param, value) {\n    return this.clone({\n      param,\n      value,\n      op: 'd'\n    });\n  }\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n\n\n  toString() {\n    this.init();\n    return this.keys().map(key => {\n      const eKey = this.encoder.encodeKey(key); // `a: ['1']` produces `'a=1'`\n      // `b: []` produces `''`\n      // `c: ['1', '2']` produces `'c=1&c=2'`\n\n      return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value)).join('&');\n    }) // filter out empty values because `b: []` produces `''`\n    // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n    .filter(param => param !== '').join('&');\n  }\n\n  clone(update) {\n    const clone = new HttpParams({\n      encoder: this.encoder\n    });\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n\n  init() {\n    if (this.map === null) {\n      this.map = new Map();\n    }\n\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n      this.updates.forEach(update => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value));\n            this.map.set(update.param, base);\n            break;\n\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n\n              if (base.length > 0) {\n                this.map.set(update.param, base);\n              } else {\n                this.map.delete(update.param);\n              }\n            } else {\n              this.map.delete(update.param);\n              break;\n            }\n\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\n\n\nclass HttpContextToken {\n  constructor(defaultValue) {\n    this.defaultValue = defaultValue;\n  }\n\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\n\n\nclass HttpContext {\n  constructor() {\n    this.map = new Map();\n  }\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n\n\n  set(token, value) {\n    this.map.set(token, value);\n    return this;\n  }\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n\n\n  get(token) {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n\n    return this.map.get(token);\n  }\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n\n\n  delete(token) {\n    this.map.delete(token);\n    return this;\n  }\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n\n\n  keys() {\n    return this.map.keys();\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\n\n\nfunction mightHaveBody(method) {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n\n    default:\n      return true;\n  }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\n\n\nfunction isArrayBuffer(value) {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\n\n\nfunction isBlob(value) {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\n\n\nfunction isFormData(value) {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\n\n\nfunction isUrlSearchParams(value) {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\n\n\nclass HttpRequest {\n  constructor(method, url, third, fourth) {\n    this.url = url;\n    /**\n     * The request body, or `null` if one isn't set.\n     *\n     * Bodies are not enforced to be immutable, as they can include a reference to any\n     * user-defined data type. However, interceptors should take care to preserve\n     * idempotence by treating them as such.\n     */\n\n    this.body = null;\n    /**\n     * Whether this request should be made in a way that exposes progress events.\n     *\n     * Progress events are expensive (change detection runs on each event) and so\n     * they should only be requested if the consumer intends to monitor them.\n     */\n\n    this.reportProgress = false;\n    /**\n     * Whether this request should be sent with outgoing credentials (cookies).\n     */\n\n    this.withCredentials = false;\n    /**\n     * The expected response type of the server.\n     *\n     * This is used to parse the response appropriately before returning it to\n     * the requestee.\n     */\n\n    this.responseType = 'json';\n    this.method = method.toUpperCase(); // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n\n    let options; // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? third : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third;\n    } // If options have been passed, interpret them.\n\n\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials; // Override default response type of 'json' if one is provided.\n\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      } // Override headers if they're provided.\n\n\n      if (!!options.headers) {\n        this.headers = options.headers;\n      }\n\n      if (!!options.context) {\n        this.context = options.context;\n      }\n\n      if (!!options.params) {\n        this.params = options.params;\n      }\n    } // If no headers have been passed in, construct a new HttpHeaders instance.\n\n\n    if (!this.headers) {\n      this.headers = new HttpHeaders();\n    } // If no context have been passed in, construct a new HttpContext instance.\n\n\n    if (!this.context) {\n      this.context = new HttpContext();\n    } // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n\n\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?'); // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n\n        const sep = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n\n\n  serializeBody() {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    } // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n\n\n    if (isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) || isUrlSearchParams(this.body) || typeof this.body === 'string') {\n      return this.body;\n    } // Check whether the body is an instance of HttpUrlEncodedParams.\n\n\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    } // Check whether the body is an object or array, and serialize with JSON if so.\n\n\n    if (typeof this.body === 'object' || typeof this.body === 'boolean' || Array.isArray(this.body)) {\n      return JSON.stringify(this.body);\n    } // Fall back on toString() for everything else.\n\n\n    return this.body.toString();\n  }\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n\n\n  detectContentTypeHeader() {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    } // FormData bodies rely on the browser's content type assignment.\n\n\n    if (isFormData(this.body)) {\n      return null;\n    } // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n\n\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    } // Array buffers have unknown contents and thus no type can be inferred.\n\n\n    if (isArrayBuffer(this.body)) {\n      return null;\n    } // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n\n\n    if (typeof this.body === 'string') {\n      return 'text/plain';\n    } // `HttpUrlEncodedParams` has its own content-type.\n\n\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    } // Arrays, objects, boolean and numbers will be encoded as JSON.\n\n\n    if (typeof this.body === 'object' || typeof this.body === 'number' || typeof this.body === 'boolean') {\n      return 'application/json';\n    } // No type could be inferred.\n\n\n    return null;\n  }\n\n  clone(update = {}) {\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType; // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n\n    const body = update.body !== undefined ? update.body : this.body; // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n\n    const withCredentials = update.withCredentials !== undefined ? update.withCredentials : this.withCredentials;\n    const reportProgress = update.reportProgress !== undefined ? update.reportProgress : this.reportProgress; // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params; // Pass on context if needed\n\n    const context = update.context ?? this.context; // Check whether the caller has asked to add headers.\n\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n    } // Check whether the caller has asked to set params.\n\n\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce((params, param) => params.set(param, update.setParams[param]), params);\n    } // Finally, construct the new HttpRequest using the pieces from above.\n\n\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials\n    });\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\n\n\nvar HttpEventType;\n\n(function (HttpEventType) {\n  /**\n   * The request was sent out over the wire.\n   */\n  HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n  /**\n   * An upload progress event was received.\n   */\n\n  HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n  /**\n   * The response status code and headers were received.\n   */\n\n  HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n  /**\n   * A download progress event was received.\n   */\n\n  HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n  /**\n   * The full response including the body was received.\n   */\n\n  HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n\n  HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\n\n\nclass HttpResponseBase {\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(init, defaultStatus = 200\n  /* Ok */\n  , defaultStatusText = 'OK') {\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null; // Cache the ok value to avoid defining a getter.\n\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\n\n\nclass HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.ResponseHeader;\n  }\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n\n\n  clone(update = {}) {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\n\n\nclass HttpResponse extends HttpResponseBase {\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(init = {}) {\n    super(init);\n    this.type = HttpEventType.Response;\n    this.body = init.body !== undefined ? init.body : null;\n  }\n\n  clone(update = {}) {\n    return new HttpResponse({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined\n    });\n  }\n\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\n\n\nclass HttpErrorResponse extends HttpResponseBase {\n  constructor(init) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n    this.name = 'HttpErrorResponse';\n    /**\n     * Errors are never okay, even when the status code is in the 2xx success range.\n     */\n\n    this.ok = false; // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n    }\n\n    this.error = init.error || null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\n\n\nfunction addBody(options, body) {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials\n  };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n *\n * @usageNotes\n * Sample HTTP requests for the [Tour of Heroes](/tutorial/toh-pt0) application.\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\n\n\nclass HttpClient {\n  constructor(handler) {\n    this.handler = handler;\n  }\n  /**\n   * Constructs an observable for a generic HTTP request that, when subscribed,\n   * fires the request through the chain of registered interceptors and on to the\n   * server.\n   *\n   * You can pass an `HttpRequest` directly as the only parameter. In this case,\n   * the call returns an observable of the raw `HttpEvent` stream.\n   *\n   * Alternatively you can pass an HTTP method as the first parameter,\n   * a URL string as the second, and an options hash containing the request body as the third.\n   * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n   * type of returned observable.\n   *   * The `responseType` value determines how a successful response body is parsed.\n   *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n   * object as a type parameter to the call.\n   *\n   * The `observe` value determines the return type, according to what you are interested in\n   * observing.\n   *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n   * progress events by default.\n   *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n   * where the `T` parameter depends on the `responseType` and any optionally provided type\n   * parameter.\n   *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n   *\n   */\n\n\n  request(first, url, options = {}) {\n    let req; // First, check whether the primary argument is an instance of `HttpRequest`.\n\n    if (first instanceof HttpRequest) {\n      // It is. The other arguments must be undefined (per the signatures) and can be\n      // ignored.\n      req = first;\n    } else {\n      // It's a string, so it represents a URL. Construct a request based on it,\n      // and incorporate the remaining arguments (assuming `GET` unless a method is\n      // provided.\n      // Figure out the headers.\n      let headers = undefined;\n\n      if (options.headers instanceof HttpHeaders) {\n        headers = options.headers;\n      } else {\n        headers = new HttpHeaders(options.headers);\n      } // Sort out parameters.\n\n\n      let params = undefined;\n\n      if (!!options.params) {\n        if (options.params instanceof HttpParams) {\n          params = options.params;\n        } else {\n          params = new HttpParams({\n            fromObject: options.params\n          });\n        }\n      } // Construct the request.\n\n\n      req = new HttpRequest(first, url, options.body !== undefined ? options.body : null, {\n        headers,\n        context: options.context,\n        params,\n        reportProgress: options.reportProgress,\n        // By default, JSON is assumed to be returned for all calls.\n        responseType: options.responseType || 'json',\n        withCredentials: options.withCredentials\n      });\n    } // Start with an Observable.of() the initial request, and run the handler (which\n    // includes all interceptors) inside a concatMap(). This way, the handler runs\n    // inside an Observable chain, which causes interceptors to be re-run on every\n    // subscription (this also makes retries re-run the handler, including interceptors).\n\n\n    const events$ = of(req).pipe(concatMap(req => this.handler.handle(req))); // If coming via the API signature which accepts a previously constructed HttpRequest,\n    // the only option is to get the event stream. Otherwise, return the event stream if\n    // that is what was requested.\n\n    if (first instanceof HttpRequest || options.observe === 'events') {\n      return events$;\n    } // The requested stream contains either the full response or the body. In either\n    // case, the first step is to filter the event stream to extract a stream of\n    // responses(s).\n\n\n    const res$ = events$.pipe(filter(event => event instanceof HttpResponse)); // Decide which stream to return.\n\n    switch (options.observe || 'body') {\n      case 'body':\n        // The requested stream is the body. Map the response stream to the response\n        // body. This could be done more simply, but a misbehaving interceptor might\n        // transform the response body into a different format and ignore the requested\n        // responseType. Guard against this by validating that the response is of the\n        // requested type.\n        switch (req.responseType) {\n          case 'arraybuffer':\n            return res$.pipe(map(res => {\n              // Validate that the body is an ArrayBuffer.\n              if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                throw new Error('Response is not an ArrayBuffer.');\n              }\n\n              return res.body;\n            }));\n\n          case 'blob':\n            return res$.pipe(map(res => {\n              // Validate that the body is a Blob.\n              if (res.body !== null && !(res.body instanceof Blob)) {\n                throw new Error('Response is not a Blob.');\n              }\n\n              return res.body;\n            }));\n\n          case 'text':\n            return res$.pipe(map(res => {\n              // Validate that the body is a string.\n              if (res.body !== null && typeof res.body !== 'string') {\n                throw new Error('Response is not a string.');\n              }\n\n              return res.body;\n            }));\n\n          case 'json':\n          default:\n            // No validation needed for JSON responses, as they can be of any type.\n            return res$.pipe(map(res => res.body));\n        }\n\n      case 'response':\n        // The response stream was requested directly, so return it.\n        return res$;\n\n      default:\n        // Guard against new future observe types being added.\n        throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n    }\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `DELETE` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   */\n\n\n  delete(url, options = {}) {\n    return this.request('DELETE', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `GET` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  get(url, options = {}) {\n    return this.request('GET', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `HEAD` request to execute on the server. The `HEAD` method returns\n   * meta information about the resource without transferring the\n   * resource itself. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  head(url, options = {}) {\n    return this.request('HEAD', url, options);\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes a request with the special method\n   * `JSONP` to be dispatched via the interceptor pipeline.\n   * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n   * API endpoints that don't support newer,\n   * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n   * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n   * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n   * application making the request.\n   * The endpoint API must support JSONP callback for JSONP requests to work.\n   * The resource API returns the JSON response wrapped in a callback function.\n   * You can pass the callback function name as one of the query parameters.\n   * Note that JSONP requests can only be used with `GET` requests.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   */\n\n\n  jsonp(url, callbackParam) {\n    return this.request('JSONP', url, {\n      params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n      observe: 'body',\n      responseType: 'json'\n    });\n  }\n  /**\n   * Constructs an `Observable` that, when subscribed, causes the configured\n   * `OPTIONS` request to execute on the server. This method allows the client\n   * to determine the supported HTTP methods and other capabilities of an endpoint,\n   * without implying a resource action. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  options(url, options = {}) {\n    return this.request('OPTIONS', url, options);\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PATCH` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  patch(url, body, options = {}) {\n    return this.request('PATCH', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `POST` request to execute on the server. The server responds with the location of\n   * the replaced resource. See the individual overloads for\n   * details on the return type.\n   */\n\n\n  post(url, body, options = {}) {\n    return this.request('POST', url, addBody(options, body));\n  }\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n   * with a new set of values.\n   * See the individual overloads for details on the return type.\n   */\n\n\n  put(url, body, options = {}) {\n    return this.request('PUT', url, addBody(options, body));\n  }\n\n}\n\nHttpClient.ɵfac = function HttpClient_Factory(t) {\n  return new (t || HttpClient)(i0.ɵɵinject(HttpHandler));\n};\n\nHttpClient.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpClient,\n  factory: HttpClient.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClient, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: HttpHandler\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * `HttpHandler` which applies an `HttpInterceptor` to an `HttpRequest`.\n *\n *\n */\n\n\nclass HttpInterceptorHandler {\n  constructor(next, interceptor) {\n    this.next = next;\n    this.interceptor = interceptor;\n  }\n\n  handle(req) {\n    return this.interceptor.intercept(req, this.next);\n  }\n\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\n\n\nconst HTTP_INTERCEPTORS = new InjectionToken('HTTP_INTERCEPTORS');\n\nclass NoopInterceptor {\n  intercept(req, next) {\n    return next.handle(req);\n  }\n\n}\n\nNoopInterceptor.ɵfac = function NoopInterceptor_Factory(t) {\n  return new (t || NoopInterceptor)();\n};\n\nNoopInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NoopInterceptor,\n  factory: NoopInterceptor.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopInterceptor, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\n\n\nlet nextRequestId = 0; // Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\n\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.'; // Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\n\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\n\nclass JsonpCallbackContext {}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see `HttpHandler`\n * @see `HttpXhrBackend`\n *\n * @publicApi\n */\n\n\nclass JsonpClientBackend {\n  constructor(callbackMap, document) {\n    this.callbackMap = callbackMap;\n    this.document = document;\n    /**\n     * A resolved promise that can be used to schedule microtasks in the event handlers.\n     */\n\n    this.resolvedPromise = Promise.resolve();\n  }\n  /**\n   * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n   */\n\n\n  nextCallback() {\n    return `ng_jsonp_callback_${nextRequestId++}`;\n  }\n  /**\n   * Processes a JSONP request and returns an event stream of the results.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   *\n   */\n\n\n  handle(req) {\n    // Firstly, check both the method and response type. If either doesn't match\n    // then the request was improperly routed here and cannot be handled.\n    if (req.method !== 'JSONP') {\n      throw new Error(JSONP_ERR_WRONG_METHOD);\n    } else if (req.responseType !== 'json') {\n      throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n    } // Everything else happens inside the Observable boundary.\n\n\n    return new Observable(observer => {\n      // The first step to make a request is to generate the callback name, and replace the\n      // callback placeholder in the URL with the name. Care has to be taken here to ensure\n      // a trailing &, if matched, gets inserted back into the URL in the correct place.\n      const callback = this.nextCallback();\n      const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`); // Construct the <script> tag and point it at the URL.\n\n      const node = this.document.createElement('script');\n      node.src = url; // A JSONP request requires waiting for multiple callbacks. These variables\n      // are closed over and track state across those callbacks.\n      // The response object, if one has been received, or null otherwise.\n\n      let body = null; // Whether the response callback has been called.\n\n      let finished = false; // Whether the request has been cancelled (and thus any other callbacks)\n      // should be ignored.\n\n      let cancelled = false; // Set the response callback in this.callbackMap (which will be the window\n      // object in the browser. The script being loaded via the <script> tag will\n      // eventually call this callback.\n\n      this.callbackMap[callback] = data => {\n        // Data has been received from the JSONP script. Firstly, delete this callback.\n        delete this.callbackMap[callback]; // Next, make sure the request wasn't cancelled in the meantime.\n\n        if (cancelled) {\n          return;\n        } // Set state to indicate data was received.\n\n\n        body = data;\n        finished = true;\n      }; // cleanup() is a utility closure that removes the <script> from the page and\n      // the response callback from the window. This logic is used in both the\n      // success, error, and cancellation paths, so it's extracted out for convenience.\n\n\n      const cleanup = () => {\n        // Remove the <script> tag if it's still on the page.\n        if (node.parentNode) {\n          node.parentNode.removeChild(node);\n        } // Remove the response callback from the callbackMap (window object in the\n        // browser).\n\n\n        delete this.callbackMap[callback];\n      }; // onLoad() is the success callback which runs after the response callback\n      // if the JSONP script loads successfully. The event itself is unimportant.\n      // If something went wrong, onLoad() may run without the response callback\n      // having been invoked.\n\n\n      const onLoad = event => {\n        // Do nothing if the request has been cancelled.\n        if (cancelled) {\n          return;\n        } // We wrap it in an extra Promise, to ensure the microtask\n        // is scheduled after the loaded endpoint has executed any potential microtask itself,\n        // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n\n\n        this.resolvedPromise.then(() => {\n          // Cleanup the page.\n          cleanup(); // Check whether the response callback has run.\n\n          if (!finished) {\n            // It hasn't, something went wrong with the request. Return an error via\n            // the Observable error path. All JSONP errors have status 0.\n            observer.error(new HttpErrorResponse({\n              url,\n              status: 0,\n              statusText: 'JSONP Error',\n              error: new Error(JSONP_ERR_NO_CALLBACK)\n            }));\n            return;\n          } // Success. body either contains the response body or null if none was\n          // returned.\n\n\n          observer.next(new HttpResponse({\n            body,\n            status: 200\n            /* Ok */\n            ,\n            statusText: 'OK',\n            url\n          })); // Complete the stream, the response is over.\n\n          observer.complete();\n        });\n      }; // onError() is the error callback, which runs if the script returned generates\n      // a Javascript error. It emits the error via the Observable error channel as\n      // a HttpErrorResponse.\n\n\n      const onError = error => {\n        // If the request was already cancelled, no need to emit anything.\n        if (cancelled) {\n          return;\n        }\n\n        cleanup(); // Wrap the error in a HttpErrorResponse.\n\n        observer.error(new HttpErrorResponse({\n          error,\n          status: 0,\n          statusText: 'JSONP Error',\n          url\n        }));\n      }; // Subscribe to both the success (load) and error events on the <script> tag,\n      // and add it to the page.\n\n\n      node.addEventListener('load', onLoad);\n      node.addEventListener('error', onError);\n      this.document.body.appendChild(node); // The request has now been successfully sent.\n\n      observer.next({\n        type: HttpEventType.Sent\n      }); // Cancellation handler.\n\n      return () => {\n        // Track the cancellation so event listeners won't do anything even if already scheduled.\n        cancelled = true; // Remove the event listeners so they won't run if the events later fire.\n\n        node.removeEventListener('load', onLoad);\n        node.removeEventListener('error', onError); // And finally, clean up the page.\n\n        cleanup();\n      };\n    });\n  }\n\n}\n\nJsonpClientBackend.ɵfac = function JsonpClientBackend_Factory(t) {\n  return new (t || JsonpClientBackend)(i0.ɵɵinject(JsonpCallbackContext), i0.ɵɵinject(DOCUMENT));\n};\n\nJsonpClientBackend.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JsonpClientBackend,\n  factory: JsonpClientBackend.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpClientBackend, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: JsonpCallbackContext\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see `HttpInterceptor`\n *\n * @publicApi\n */\n\n\nclass JsonpInterceptor {\n  constructor(jsonp) {\n    this.jsonp = jsonp;\n  }\n  /**\n   * Identifies and handles a given JSONP request.\n   * @param req The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n\n\n  intercept(req, next) {\n    if (req.method === 'JSONP') {\n      return this.jsonp.handle(req);\n    } // Fall through for normal HTTP requests.\n\n\n    return next.handle(req);\n  }\n\n}\n\nJsonpInterceptor.ɵfac = function JsonpInterceptor_Factory(t) {\n  return new (t || JsonpInterceptor)(i0.ɵɵinject(JsonpClientBackend));\n};\n\nJsonpInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: JsonpInterceptor,\n  factory: JsonpInterceptor.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(JsonpInterceptor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: JsonpClientBackend\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\n\nfunction getResponseUrl(xhr) {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n\n  if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader('X-Request-URL');\n  }\n\n  return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see `HttpHandler`\n * @see `JsonpClientBackend`\n *\n * @publicApi\n */\n\n\nclass HttpXhrBackend {\n  constructor(xhrFactory) {\n    this.xhrFactory = xhrFactory;\n  }\n  /**\n   * Processes a request and returns a stream of response events.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   */\n\n\n  handle(req) {\n    // Quick check to give a better error message when a user attempts to use\n    // HttpClient.jsonp() without installing the HttpClientJsonpModule\n    if (req.method === 'JSONP') {\n      throw new Error(`Attempted to construct Jsonp request without HttpClientJsonpModule installed.`);\n    } // Everything happens on Observable subscription.\n\n\n    return new Observable(observer => {\n      // Start by setting up the XHR object with request method, URL, and withCredentials flag.\n      const xhr = this.xhrFactory.build();\n      xhr.open(req.method, req.urlWithParams);\n\n      if (!!req.withCredentials) {\n        xhr.withCredentials = true;\n      } // Add all the requested headers.\n\n\n      req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(','))); // Add an Accept header if one isn't present already.\n\n      if (!req.headers.has('Accept')) {\n        xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n      } // Auto-detect the Content-Type header if one isn't present already.\n\n\n      if (!req.headers.has('Content-Type')) {\n        const detectedType = req.detectContentTypeHeader(); // Sometimes Content-Type detection fails.\n\n        if (detectedType !== null) {\n          xhr.setRequestHeader('Content-Type', detectedType);\n        }\n      } // Set the responseType if one was requested.\n\n\n      if (req.responseType) {\n        const responseType = req.responseType.toLowerCase(); // JSON responses need to be processed as text. This is because if the server\n        // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n        // xhr.response will be null, and xhr.responseText cannot be accessed to\n        // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n        // is parsed by first requesting text and then applying JSON.parse.\n\n        xhr.responseType = responseType !== 'json' ? responseType : 'text';\n      } // Serialize the request body if one is present. If not, this will be set to null.\n\n\n      const reqBody = req.serializeBody(); // If progress events are enabled, response headers will be delivered\n      // in two events - the HttpHeaderResponse event and the full HttpResponse\n      // event. However, since response headers don't change in between these\n      // two events, it doesn't make sense to parse them twice. So headerResponse\n      // caches the data extracted from the response whenever it's first parsed,\n      // to ensure parsing isn't duplicated.\n\n      let headerResponse = null; // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n      // state, and memoizes it into headerResponse.\n\n      const partialFromXhr = () => {\n        if (headerResponse !== null) {\n          return headerResponse;\n        } // Read status and normalize an IE9 bug (https://bugs.jquery.com/ticket/1450).\n\n\n        const status = xhr.status === 1223 ? 204\n        /* NoContent */\n        : xhr.status;\n        const statusText = xhr.statusText || 'OK'; // Parse headers from XMLHttpRequest - this step is lazy.\n\n        const headers = new HttpHeaders(xhr.getAllResponseHeaders()); // Read the response URL from the XMLHttpResponse instance and fall back on the\n        // request URL.\n\n        const url = getResponseUrl(xhr) || req.url; // Construct the HttpHeaderResponse and memoize it.\n\n        headerResponse = new HttpHeaderResponse({\n          headers,\n          status,\n          statusText,\n          url\n        });\n        return headerResponse;\n      }; // Next, a few closures are defined for the various events which XMLHttpRequest can\n      // emit. This allows them to be unregistered as event listeners later.\n      // First up is the load event, which represents a response being fully available.\n\n\n      const onLoad = () => {\n        // Read response state from the memoized partial data.\n        let {\n          headers,\n          status,\n          statusText,\n          url\n        } = partialFromXhr(); // The body will be read out if present.\n\n        let body = null;\n\n        if (status !== 204\n        /* NoContent */\n        ) {\n          // Use XMLHttpRequest.response if set, responseText otherwise.\n          body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n        } // Normalize another potential bug (this one comes from CORS).\n\n\n        if (status === 0) {\n          status = !!body ? 200\n          /* Ok */\n          : 0;\n        } // ok determines whether the response will be transmitted on the event or\n        // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n        // but a successful status code can still result in an error if the user\n        // asked for JSON data and the body cannot be parsed as such.\n\n\n        let ok = status >= 200 && status < 300; // Check whether the body needs to be parsed as JSON (in many cases the browser\n        // will have done that already).\n\n        if (req.responseType === 'json' && typeof body === 'string') {\n          // Save the original body, before attempting XSSI prefix stripping.\n          const originalBody = body;\n          body = body.replace(XSSI_PREFIX, '');\n\n          try {\n            // Attempt the parse. If it fails, a parse error should be delivered to the user.\n            body = body !== '' ? JSON.parse(body) : null;\n          } catch (error) {\n            // Since the JSON.parse failed, it's reasonable to assume this might not have been a\n            // JSON response. Restore the original body (including any XSSI prefix) to deliver\n            // a better error response.\n            body = originalBody; // If this was an error request to begin with, leave it as a string, it probably\n            // just isn't JSON. Otherwise, deliver the parsing error to the user.\n\n            if (ok) {\n              // Even though the response status was 2xx, this is still an error.\n              ok = false; // The parse error contains the text of the body that failed to parse.\n\n              body = {\n                error,\n                text: body\n              };\n            }\n          }\n        }\n\n        if (ok) {\n          // A successful response is delivered on the event stream.\n          observer.next(new HttpResponse({\n            body,\n            headers,\n            status,\n            statusText,\n            url: url || undefined\n          })); // The full body has been received and delivered, no further events\n          // are possible. This request is complete.\n\n          observer.complete();\n        } else {\n          // An unsuccessful request is delivered on the error channel.\n          observer.error(new HttpErrorResponse({\n            // The error in this case is the response body (error from the server).\n            error: body,\n            headers,\n            status,\n            statusText,\n            url: url || undefined\n          }));\n        }\n      }; // The onError callback is called when something goes wrong at the network level.\n      // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n      // transmitted on the error channel.\n\n\n      const onError = error => {\n        const {\n          url\n        } = partialFromXhr();\n        const res = new HttpErrorResponse({\n          error,\n          status: xhr.status || 0,\n          statusText: xhr.statusText || 'Unknown Error',\n          url: url || undefined\n        });\n        observer.error(res);\n      }; // The sentHeaders flag tracks whether the HttpResponseHeaders event\n      // has been sent on the stream. This is necessary to track if progress\n      // is enabled since the event will be sent on only the first download\n      // progerss event.\n\n\n      let sentHeaders = false; // The download progress event handler, which is only registered if\n      // progress events are enabled.\n\n      const onDownProgress = event => {\n        // Send the HttpResponseHeaders event if it hasn't been sent already.\n        if (!sentHeaders) {\n          observer.next(partialFromXhr());\n          sentHeaders = true;\n        } // Start building the download progress event to deliver on the response\n        // event stream.\n\n\n        let progressEvent = {\n          type: HttpEventType.DownloadProgress,\n          loaded: event.loaded\n        }; // Set the total number of bytes in the event if it's available.\n\n        if (event.lengthComputable) {\n          progressEvent.total = event.total;\n        } // If the request was for text content and a partial response is\n        // available on XMLHttpRequest, include it in the progress event\n        // to allow for streaming reads.\n\n\n        if (req.responseType === 'text' && !!xhr.responseText) {\n          progressEvent.partialText = xhr.responseText;\n        } // Finally, fire the event.\n\n\n        observer.next(progressEvent);\n      }; // The upload progress event handler, which is only registered if\n      // progress events are enabled.\n\n\n      const onUpProgress = event => {\n        // Upload progress events are simpler. Begin building the progress\n        // event.\n        let progress = {\n          type: HttpEventType.UploadProgress,\n          loaded: event.loaded\n        }; // If the total number of bytes being uploaded is available, include\n        // it.\n\n        if (event.lengthComputable) {\n          progress.total = event.total;\n        } // Send the event.\n\n\n        observer.next(progress);\n      }; // By default, register for load and error events.\n\n\n      xhr.addEventListener('load', onLoad);\n      xhr.addEventListener('error', onError);\n      xhr.addEventListener('timeout', onError);\n      xhr.addEventListener('abort', onError); // Progress events are only enabled if requested.\n\n      if (req.reportProgress) {\n        // Download progress is always enabled if requested.\n        xhr.addEventListener('progress', onDownProgress); // Upload progress depends on whether there is a body to upload.\n\n        if (reqBody !== null && xhr.upload) {\n          xhr.upload.addEventListener('progress', onUpProgress);\n        }\n      } // Fire the request, and notify the event stream that it was fired.\n\n\n      xhr.send(reqBody);\n      observer.next({\n        type: HttpEventType.Sent\n      }); // This is the return from the Observable function, which is the\n      // request cancellation handler.\n\n      return () => {\n        // On a cancellation, remove all registered event listeners.\n        xhr.removeEventListener('error', onError);\n        xhr.removeEventListener('abort', onError);\n        xhr.removeEventListener('load', onLoad);\n        xhr.removeEventListener('timeout', onError);\n\n        if (req.reportProgress) {\n          xhr.removeEventListener('progress', onDownProgress);\n\n          if (reqBody !== null && xhr.upload) {\n            xhr.upload.removeEventListener('progress', onUpProgress);\n          }\n        } // Finally, abort the in-flight request.\n\n\n        if (xhr.readyState !== xhr.DONE) {\n          xhr.abort();\n        }\n      };\n    });\n  }\n\n}\n\nHttpXhrBackend.ɵfac = function HttpXhrBackend_Factory(t) {\n  return new (t || HttpXhrBackend)(i0.ɵɵinject(i1.XhrFactory));\n};\n\nHttpXhrBackend.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpXhrBackend,\n  factory: HttpXhrBackend.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXhrBackend, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i1.XhrFactory\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst XSRF_COOKIE_NAME = new InjectionToken('XSRF_COOKIE_NAME');\nconst XSRF_HEADER_NAME = new InjectionToken('XSRF_HEADER_NAME');\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\n\nclass HttpXsrfTokenExtractor {}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\n\n\nclass HttpXsrfCookieExtractor {\n  constructor(doc, platform, cookieName) {\n    this.doc = doc;\n    this.platform = platform;\n    this.cookieName = cookieName;\n    this.lastCookieString = '';\n    this.lastToken = null;\n    /**\n     * @internal for testing\n     */\n\n    this.parseCount = 0;\n  }\n\n  getToken() {\n    if (this.platform === 'server') {\n      return null;\n    }\n\n    const cookieString = this.doc.cookie || '';\n\n    if (cookieString !== this.lastCookieString) {\n      this.parseCount++;\n      this.lastToken = ɵparseCookieValue(cookieString, this.cookieName);\n      this.lastCookieString = cookieString;\n    }\n\n    return this.lastToken;\n  }\n\n}\n\nHttpXsrfCookieExtractor.ɵfac = function HttpXsrfCookieExtractor_Factory(t) {\n  return new (t || HttpXsrfCookieExtractor)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(XSRF_COOKIE_NAME));\n};\n\nHttpXsrfCookieExtractor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpXsrfCookieExtractor,\n  factory: HttpXsrfCookieExtractor.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfCookieExtractor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [XSRF_COOKIE_NAME]\n      }]\n    }];\n  }, null);\n})();\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\n\n\nclass HttpXsrfInterceptor {\n  constructor(tokenService, headerName) {\n    this.tokenService = tokenService;\n    this.headerName = headerName;\n  }\n\n  intercept(req, next) {\n    const lcUrl = req.url.toLowerCase(); // Skip both non-mutating requests and absolute URLs.\n    // Non-mutating requests don't require a token, and absolute URLs require special handling\n    // anyway as the cookie set\n    // on our origin is not the same as the token expected by another origin.\n\n    if (req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') || lcUrl.startsWith('https://')) {\n      return next.handle(req);\n    }\n\n    const token = this.tokenService.getToken(); // Be careful not to overwrite an existing header of the same name.\n\n    if (token !== null && !req.headers.has(this.headerName)) {\n      req = req.clone({\n        headers: req.headers.set(this.headerName, token)\n      });\n    }\n\n    return next.handle(req);\n  }\n\n}\n\nHttpXsrfInterceptor.ɵfac = function HttpXsrfInterceptor_Factory(t) {\n  return new (t || HttpXsrfInterceptor)(i0.ɵɵinject(HttpXsrfTokenExtractor), i0.ɵɵinject(XSRF_HEADER_NAME));\n};\n\nHttpXsrfInterceptor.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpXsrfInterceptor,\n  factory: HttpXsrfInterceptor.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpXsrfInterceptor, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: HttpXsrfTokenExtractor\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [XSRF_HEADER_NAME]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * An injectable `HttpHandler` that applies multiple interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * The interceptors are loaded lazily from the injector, to allow\n * interceptors to themselves inject classes depending indirectly\n * on `HttpInterceptingHandler` itself.\n * @see `HttpInterceptor`\n */\n\n\nclass HttpInterceptingHandler {\n  constructor(backend, injector) {\n    this.backend = backend;\n    this.injector = injector;\n    this.chain = null;\n  }\n\n  handle(req) {\n    if (this.chain === null) {\n      const interceptors = this.injector.get(HTTP_INTERCEPTORS, []);\n      this.chain = interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), this.backend);\n    }\n\n    return this.chain.handle(req);\n  }\n\n}\n\nHttpInterceptingHandler.ɵfac = function HttpInterceptingHandler_Factory(t) {\n  return new (t || HttpInterceptingHandler)(i0.ɵɵinject(HttpBackend), i0.ɵɵinject(i0.Injector));\n};\n\nHttpInterceptingHandler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HttpInterceptingHandler,\n  factory: HttpInterceptingHandler.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpInterceptingHandler, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: HttpBackend\n    }, {\n      type: i0.Injector\n    }];\n  }, null);\n})();\n/**\n * Constructs an `HttpHandler` that applies interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * Use as a factory function within `HttpClientModule`.\n *\n *\n */\n\n\nfunction interceptingHandler(backend, interceptors = []) {\n  if (!interceptors) {\n    return backend;\n  }\n\n  return interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), backend);\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\n\n\nfunction jsonpCallbackContext() {\n  if (typeof window === 'object') {\n    return window;\n  }\n\n  return {};\n}\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n */\n\n\nclass HttpClientXsrfModule {\n  /**\n   * Disable the default XSRF protection.\n   */\n  static disable() {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [{\n        provide: HttpXsrfInterceptor,\n        useClass: NoopInterceptor\n      }]\n    };\n  }\n  /**\n   * Configure XSRF protection.\n   * @param options An object that can specify either or both\n   * cookie name or header name.\n   * - Cookie name default is `XSRF-TOKEN`.\n   * - Header name default is `X-XSRF-TOKEN`.\n   *\n   */\n\n\n  static withOptions(options = {}) {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [options.cookieName ? {\n        provide: XSRF_COOKIE_NAME,\n        useValue: options.cookieName\n      } : [], options.headerName ? {\n        provide: XSRF_HEADER_NAME,\n        useValue: options.headerName\n      } : []]\n    };\n  }\n\n}\n\nHttpClientXsrfModule.ɵfac = function HttpClientXsrfModule_Factory(t) {\n  return new (t || HttpClientXsrfModule)();\n};\n\nHttpClientXsrfModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientXsrfModule\n});\nHttpClientXsrfModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [HttpXsrfInterceptor, {\n    provide: HTTP_INTERCEPTORS,\n    useExisting: HttpXsrfInterceptor,\n    multi: true\n  }, {\n    provide: HttpXsrfTokenExtractor,\n    useClass: HttpXsrfCookieExtractor\n  }, {\n    provide: XSRF_COOKIE_NAME,\n    useValue: 'XSRF-TOKEN'\n  }, {\n    provide: XSRF_HEADER_NAME,\n    useValue: 'X-XSRF-TOKEN'\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientXsrfModule, [{\n    type: NgModule,\n    args: [{\n      providers: [HttpXsrfInterceptor, {\n        provide: HTTP_INTERCEPTORS,\n        useExisting: HttpXsrfInterceptor,\n        multi: true\n      }, {\n        provide: HttpXsrfTokenExtractor,\n        useClass: HttpXsrfCookieExtractor\n      }, {\n        provide: XSRF_COOKIE_NAME,\n        useValue: 'XSRF-TOKEN'\n      }, {\n        provide: XSRF_HEADER_NAME,\n        useValue: 'X-XSRF-TOKEN'\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\n\n\nclass HttpClientModule {}\n\nHttpClientModule.ɵfac = function HttpClientModule_Factory(t) {\n  return new (t || HttpClientModule)();\n};\n\nHttpClientModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientModule\n});\nHttpClientModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [HttpClient, {\n    provide: HttpHandler,\n    useClass: HttpInterceptingHandler\n  }, HttpXhrBackend, {\n    provide: HttpBackend,\n    useExisting: HttpXhrBackend\n  }],\n  imports: [[HttpClientXsrfModule.withOptions({\n    cookieName: 'XSRF-TOKEN',\n    headerName: 'X-XSRF-TOKEN'\n  })]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientModule, [{\n    type: NgModule,\n    args: [{\n      /**\n       * Optional configuration for XSRF protection.\n       */\n      imports: [HttpClientXsrfModule.withOptions({\n        cookieName: 'XSRF-TOKEN',\n        headerName: 'X-XSRF-TOKEN'\n      })],\n\n      /**\n       * Configures the [dependency injector](guide/glossary#injector) where it is imported\n       * with supporting services for HTTP communications.\n       */\n      providers: [HttpClient, {\n        provide: HttpHandler,\n        useClass: HttpInterceptingHandler\n      }, HttpXhrBackend, {\n        provide: HttpBackend,\n        useExisting: HttpXhrBackend\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\n\n\nclass HttpClientJsonpModule {}\n\nHttpClientJsonpModule.ɵfac = function HttpClientJsonpModule_Factory(t) {\n  return new (t || HttpClientJsonpModule)();\n};\n\nHttpClientJsonpModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HttpClientJsonpModule\n});\nHttpClientJsonpModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [JsonpClientBackend, {\n    provide: JsonpCallbackContext,\n    useFactory: jsonpCallbackContext\n  }, {\n    provide: HTTP_INTERCEPTORS,\n    useClass: JsonpInterceptor,\n    multi: true\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HttpClientJsonpModule, [{\n    type: NgModule,\n    args: [{\n      providers: [JsonpClientBackend, {\n        provide: JsonpCallbackContext,\n        useFactory: jsonpCallbackContext\n      }, {\n        provide: HTTP_INTERCEPTORS,\n        useClass: JsonpInterceptor,\n        multi: true\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n * @see `XhrFactory`\n * @deprecated\n * `XhrFactory` has moved, please import `XhrFactory` from `@angular/common` instead.\n */\n\n\nconst XhrFactory = XhrFactory$1;\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HTTP_INTERCEPTORS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, XhrFactory, HttpInterceptingHandler as ɵHttpInterceptingHandler };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/common/fesm2020/http.mjs"], "names": ["i1", "DOCUMENT", "ɵparseCookieValue", "XhrFactory", "XhrFactory$1", "i0", "Injectable", "InjectionToken", "Inject", "PLATFORM_ID", "NgModule", "of", "Observable", "concatMap", "filter", "map", "HttpHandler", "HttpBackend", "HttpHeaders", "constructor", "headers", "normalizedNames", "Map", "lazyUpdate", "lazyInit", "split", "for<PERSON>ach", "line", "index", "indexOf", "name", "slice", "key", "toLowerCase", "value", "trim", "maybeSetNormalizedName", "has", "get", "push", "set", "Object", "keys", "values", "length", "init", "Array", "from", "getAll", "append", "clone", "op", "delete", "lcName", "copyFrom", "update", "applyUpdate", "other", "concat", "base", "undefined", "toDelete", "existing", "fn", "HttpUrlEncodingCodec", "encodeKey", "standardEncoding", "encodeValue", "decodeKey", "decodeURIComponent", "decodeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rawParams", "codec", "params", "replace", "param", "eqIdx", "val", "list", "STANDARD_ENCODING_REGEX", "STANDARD_ENCODING_REPLACEMENTS", "v", "encodeURIComponent", "s", "t", "valueToString", "HttpParams", "options", "updates", "cloneFrom", "encoder", "fromString", "fromObject", "Error", "isArray", "res", "appendAll", "_value", "toString", "<PERSON><PERSON><PERSON>", "join", "idx", "splice", "HttpContextToken", "defaultValue", "HttpContext", "token", "mightHaveBody", "method", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isBlob", "Blob", "isFormData", "FormData", "isUrlSearchParams", "URLSearchParams", "HttpRequest", "url", "third", "fourth", "body", "reportProgress", "withCredentials", "responseType", "toUpperCase", "context", "urlWithParams", "qIdx", "sep", "serializeBody", "JSON", "stringify", "detectContentTypeHeader", "type", "setHeaders", "reduce", "setParams", "HttpEventType", "HttpResponseBase", "defaultStatus", "defaultStatusText", "status", "statusText", "ok", "HttpHeaderResponse", "ResponseHeader", "HttpResponse", "Response", "HttpErrorResponse", "message", "error", "addBody", "observe", "HttpClient", "handler", "request", "first", "req", "events$", "pipe", "handle", "res$", "event", "head", "jsonp", "callback<PERSON><PERSON><PERSON>", "patch", "post", "put", "ɵfac", "ɵprov", "HttpInterceptorHandler", "next", "interceptor", "intercept", "HTTP_INTERCEPTORS", "NoopInterceptor", "nextRequestId", "JSONP_ERR_NO_CALLBACK", "JSONP_ERR_WRONG_METHOD", "JSONP_ERR_WRONG_RESPONSE_TYPE", "JsonpCallbackContext", "JsonpClientBackend", "callbackMap", "document", "resolvedPromise", "Promise", "resolve", "nextCallback", "observer", "callback", "node", "createElement", "src", "finished", "cancelled", "data", "cleanup", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "onLoad", "then", "complete", "onError", "addEventListener", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "removeEventListener", "decorators", "args", "JsonpInterceptor", "XSSI_PREFIX", "getResponseUrl", "xhr", "responseURL", "test", "getAllResponseHeaders", "getResponseHeader", "HttpXhrBackend", "xhrFactory", "build", "open", "setRequestHeader", "detectedType", "reqBody", "headerResponse", "partialFromXhr", "response", "responseText", "originalBody", "parse", "text", "sentHeaders", "onDownProgress", "progressEvent", "DownloadProgress", "loaded", "lengthComputable", "total", "partialText", "onUpProgress", "progress", "UploadProgress", "upload", "send", "readyState", "DONE", "abort", "XSRF_COOKIE_NAME", "XSRF_HEADER_NAME", "HttpXsrfTokenExtractor", "HttpXsrfCookieExtractor", "doc", "platform", "cookieName", "lastCookieString", "lastToken", "parseCount", "getToken", "cookieString", "cookie", "HttpXsrfInterceptor", "tokenService", "headerName", "lcUrl", "startsWith", "HttpInterceptingHandler", "backend", "injector", "chain", "interceptors", "reduceRight", "Injector", "intercepting<PERSON><PERSON><PERSON>", "jsonpCallbackContext", "window", "HttpClientXsrfModule", "disable", "ngModule", "providers", "provide", "useClass", "withOptions", "useValue", "ɵmod", "ɵinj", "useExisting", "multi", "HttpClientModule", "imports", "HttpClientJsonpModule", "useFactory", "ɵHttpInterceptingHandler"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,OAAO,KAAKA,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,iBAAnB,EAAsCC,UAAU,IAAIC,YAApD,QAAwE,iBAAxE;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,cAArB,EAAqCC,MAArC,EAA6CC,WAA7C,EAA0DC,QAA1D,QAA0E,eAA1E;AACA,SAASC,EAAT,EAAaC,UAAb,QAA+B,MAA/B;AACA,SAASC,SAAT,EAAoBC,MAApB,EAA4BC,GAA5B,QAAuC,gBAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,WAAN,CAAkB;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,WAAN,CAAkB;AAGlB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,WAAN,CAAkB;AACd;AACAC,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB;AACR;AACA;AACA;AACQ,SAAKC,eAAL,GAAuB,IAAIC,GAAJ,EAAvB;AACA;AACR;AACA;;AACQ,SAAKC,UAAL,GAAkB,IAAlB;;AACA,QAAI,CAACH,OAAL,EAAc;AACV,WAAKA,OAAL,GAAe,IAAIE,GAAJ,EAAf;AACH,KAFD,MAGK,IAAI,OAAOF,OAAP,KAAmB,QAAvB,EAAiC;AAClC,WAAKI,QAAL,GAAgB,MAAM;AAClB,aAAKJ,OAAL,GAAe,IAAIE,GAAJ,EAAf;AACAF,QAAAA,OAAO,CAACK,KAAR,CAAc,IAAd,EAAoBC,OAApB,CAA4BC,IAAI,IAAI;AAChC,gBAAMC,KAAK,GAAGD,IAAI,CAACE,OAAL,CAAa,GAAb,CAAd;;AACA,cAAID,KAAK,GAAG,CAAZ,EAAe;AACX,kBAAME,IAAI,GAAGH,IAAI,CAACI,KAAL,CAAW,CAAX,EAAcH,KAAd,CAAb;AACA,kBAAMI,GAAG,GAAGF,IAAI,CAACG,WAAL,EAAZ;AACA,kBAAMC,KAAK,GAAGP,IAAI,CAACI,KAAL,CAAWH,KAAK,GAAG,CAAnB,EAAsBO,IAAtB,EAAd;AACA,iBAAKC,sBAAL,CAA4BN,IAA5B,EAAkCE,GAAlC;;AACA,gBAAI,KAAKZ,OAAL,CAAaiB,GAAb,CAAiBL,GAAjB,CAAJ,EAA2B;AACvB,mBAAKZ,OAAL,CAAakB,GAAb,CAAiBN,GAAjB,EAAsBO,IAAtB,CAA2BL,KAA3B;AACH,aAFD,MAGK;AACD,mBAAKd,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsB,CAACE,KAAD,CAAtB;AACH;AACJ;AACJ,SAdD;AAeH,OAjBD;AAkBH,KAnBI,MAoBA;AACD,WAAKV,QAAL,GAAgB,MAAM;AAClB,aAAKJ,OAAL,GAAe,IAAIE,GAAJ,EAAf;AACAmB,QAAAA,MAAM,CAACC,IAAP,CAAYtB,OAAZ,EAAqBM,OAArB,CAA6BI,IAAI,IAAI;AACjC,cAAIa,MAAM,GAAGvB,OAAO,CAACU,IAAD,CAApB;AACA,gBAAME,GAAG,GAAGF,IAAI,CAACG,WAAL,EAAZ;;AACA,cAAI,OAAOU,MAAP,KAAkB,QAAtB,EAAgC;AAC5BA,YAAAA,MAAM,GAAG,CAACA,MAAD,CAAT;AACH;;AACD,cAAIA,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AACnB,iBAAKxB,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsBW,MAAtB;AACA,iBAAKP,sBAAL,CAA4BN,IAA5B,EAAkCE,GAAlC;AACH;AACJ,SAVD;AAWH,OAbD;AAcH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIK,EAAAA,GAAG,CAACP,IAAD,EAAO;AACN,SAAKe,IAAL;AACA,WAAO,KAAKzB,OAAL,CAAaiB,GAAb,CAAiBP,IAAI,CAACG,WAAL,EAAjB,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIK,EAAAA,GAAG,CAACR,IAAD,EAAO;AACN,SAAKe,IAAL;AACA,UAAMF,MAAM,GAAG,KAAKvB,OAAL,CAAakB,GAAb,CAAiBR,IAAI,CAACG,WAAL,EAAjB,CAAf;AACA,WAAOU,MAAM,IAAIA,MAAM,CAACC,MAAP,GAAgB,CAA1B,GAA8BD,MAAM,CAAC,CAAD,CAApC,GAA0C,IAAjD;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACID,EAAAA,IAAI,GAAG;AACH,SAAKG,IAAL;AACA,WAAOC,KAAK,CAACC,IAAN,CAAW,KAAK1B,eAAL,CAAqBsB,MAArB,EAAX,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIK,EAAAA,MAAM,CAAClB,IAAD,EAAO;AACT,SAAKe,IAAL;AACA,WAAO,KAAKzB,OAAL,CAAakB,GAAb,CAAiBR,IAAI,CAACG,WAAL,EAAjB,KAAwC,IAA/C;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIgB,EAAAA,MAAM,CAACnB,IAAD,EAAOI,KAAP,EAAc;AAChB,WAAO,KAAKgB,KAAL,CAAW;AAAEpB,MAAAA,IAAF;AAAQI,MAAAA,KAAR;AAAeiB,MAAAA,EAAE,EAAE;AAAnB,KAAX,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIX,EAAAA,GAAG,CAACV,IAAD,EAAOI,KAAP,EAAc;AACb,WAAO,KAAKgB,KAAL,CAAW;AAAEpB,MAAAA,IAAF;AAAQI,MAAAA,KAAR;AAAeiB,MAAAA,EAAE,EAAE;AAAnB,KAAX,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,MAAM,CAACtB,IAAD,EAAOI,KAAP,EAAc;AAChB,WAAO,KAAKgB,KAAL,CAAW;AAAEpB,MAAAA,IAAF;AAAQI,MAAAA,KAAR;AAAeiB,MAAAA,EAAE,EAAE;AAAnB,KAAX,CAAP;AACH;;AACDf,EAAAA,sBAAsB,CAACN,IAAD,EAAOuB,MAAP,EAAe;AACjC,QAAI,CAAC,KAAKhC,eAAL,CAAqBgB,GAArB,CAAyBgB,MAAzB,CAAL,EAAuC;AACnC,WAAKhC,eAAL,CAAqBmB,GAArB,CAAyBa,MAAzB,EAAiCvB,IAAjC;AACH;AACJ;;AACDe,EAAAA,IAAI,GAAG;AACH,QAAI,CAAC,CAAC,KAAKrB,QAAX,EAAqB;AACjB,UAAI,KAAKA,QAAL,YAAyBN,WAA7B,EAA0C;AACtC,aAAKoC,QAAL,CAAc,KAAK9B,QAAnB;AACH,OAFD,MAGK;AACD,aAAKA,QAAL;AACH;;AACD,WAAKA,QAAL,GAAgB,IAAhB;;AACA,UAAI,CAAC,CAAC,KAAKD,UAAX,EAAuB;AACnB,aAAKA,UAAL,CAAgBG,OAAhB,CAAwB6B,MAAM,IAAI,KAAKC,WAAL,CAAiBD,MAAjB,CAAlC;AACA,aAAKhC,UAAL,GAAkB,IAAlB;AACH;AACJ;AACJ;;AACD+B,EAAAA,QAAQ,CAACG,KAAD,EAAQ;AACZA,IAAAA,KAAK,CAACZ,IAAN;AACAC,IAAAA,KAAK,CAACC,IAAN,CAAWU,KAAK,CAACrC,OAAN,CAAcsB,IAAd,EAAX,EAAiChB,OAAjC,CAAyCM,GAAG,IAAI;AAC5C,WAAKZ,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsByB,KAAK,CAACrC,OAAN,CAAckB,GAAd,CAAkBN,GAAlB,CAAtB;AACA,WAAKX,eAAL,CAAqBmB,GAArB,CAAyBR,GAAzB,EAA8ByB,KAAK,CAACpC,eAAN,CAAsBiB,GAAtB,CAA0BN,GAA1B,CAA9B;AACH,KAHD;AAIH;;AACDkB,EAAAA,KAAK,CAACK,MAAD,EAAS;AACV,UAAML,KAAK,GAAG,IAAIhC,WAAJ,EAAd;AACAgC,IAAAA,KAAK,CAAC1B,QAAN,GACK,CAAC,CAAC,KAAKA,QAAP,IAAmB,KAAKA,QAAL,YAAyBN,WAA7C,GAA4D,KAAKM,QAAjE,GAA4E,IADhF;AAEA0B,IAAAA,KAAK,CAAC3B,UAAN,GAAmB,CAAC,KAAKA,UAAL,IAAmB,EAApB,EAAwBmC,MAAxB,CAA+B,CAACH,MAAD,CAA/B,CAAnB;AACA,WAAOL,KAAP;AACH;;AACDM,EAAAA,WAAW,CAACD,MAAD,EAAS;AAChB,UAAMvB,GAAG,GAAGuB,MAAM,CAACzB,IAAP,CAAYG,WAAZ,EAAZ;;AACA,YAAQsB,MAAM,CAACJ,EAAf;AACI,WAAK,GAAL;AACA,WAAK,GAAL;AACI,YAAIjB,KAAK,GAAGqB,MAAM,CAACrB,KAAnB;;AACA,YAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;AAC3BA,UAAAA,KAAK,GAAG,CAACA,KAAD,CAAR;AACH;;AACD,YAAIA,KAAK,CAACU,MAAN,KAAiB,CAArB,EAAwB;AACpB;AACH;;AACD,aAAKR,sBAAL,CAA4BmB,MAAM,CAACzB,IAAnC,EAAyCE,GAAzC;AACA,cAAM2B,IAAI,GAAG,CAACJ,MAAM,CAACJ,EAAP,KAAc,GAAd,GAAoB,KAAK/B,OAAL,CAAakB,GAAb,CAAiBN,GAAjB,CAApB,GAA4C4B,SAA7C,KAA2D,EAAxE;AACAD,QAAAA,IAAI,CAACpB,IAAL,CAAU,GAAGL,KAAb;AACA,aAAKd,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsB2B,IAAtB;AACA;;AACJ,WAAK,GAAL;AACI,cAAME,QAAQ,GAAGN,MAAM,CAACrB,KAAxB;;AACA,YAAI,CAAC2B,QAAL,EAAe;AACX,eAAKzC,OAAL,CAAagC,MAAb,CAAoBpB,GAApB;AACA,eAAKX,eAAL,CAAqB+B,MAArB,CAA4BpB,GAA5B;AACH,SAHD,MAIK;AACD,cAAI8B,QAAQ,GAAG,KAAK1C,OAAL,CAAakB,GAAb,CAAiBN,GAAjB,CAAf;;AACA,cAAI,CAAC8B,QAAL,EAAe;AACX;AACH;;AACDA,UAAAA,QAAQ,GAAGA,QAAQ,CAAChD,MAAT,CAAgBoB,KAAK,IAAI2B,QAAQ,CAAChC,OAAT,CAAiBK,KAAjB,MAA4B,CAAC,CAAtD,CAAX;;AACA,cAAI4B,QAAQ,CAAClB,MAAT,KAAoB,CAAxB,EAA2B;AACvB,iBAAKxB,OAAL,CAAagC,MAAb,CAAoBpB,GAApB;AACA,iBAAKX,eAAL,CAAqB+B,MAArB,CAA4BpB,GAA5B;AACH,WAHD,MAIK;AACD,iBAAKZ,OAAL,CAAaoB,GAAb,CAAiBR,GAAjB,EAAsB8B,QAAtB;AACH;AACJ;;AACD;AAnCR;AAqCH;AACD;AACJ;AACA;;;AACIpC,EAAAA,OAAO,CAACqC,EAAD,EAAK;AACR,SAAKlB,IAAL;AACAC,IAAAA,KAAK,CAACC,IAAN,CAAW,KAAK1B,eAAL,CAAqBqB,IAArB,EAAX,EACKhB,OADL,CACaM,GAAG,IAAI+B,EAAE,CAAC,KAAK1C,eAAL,CAAqBiB,GAArB,CAAyBN,GAAzB,CAAD,EAAgC,KAAKZ,OAAL,CAAakB,GAAb,CAAiBN,GAAjB,CAAhC,CADtB;AAEH;;AApNa;AAuNlB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgC,oBAAN,CAA2B;AACvB;AACJ;AACA;AACA;AACA;AACIC,EAAAA,SAAS,CAACjC,GAAD,EAAM;AACX,WAAOkC,gBAAgB,CAAClC,GAAD,CAAvB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACImC,EAAAA,WAAW,CAACjC,KAAD,EAAQ;AACf,WAAOgC,gBAAgB,CAAChC,KAAD,CAAvB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIkC,EAAAA,SAAS,CAACpC,GAAD,EAAM;AACX,WAAOqC,kBAAkB,CAACrC,GAAD,CAAzB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIsC,EAAAA,WAAW,CAACpC,KAAD,EAAQ;AACf,WAAOmC,kBAAkB,CAACnC,KAAD,CAAzB;AACH;;AAhCsB;;AAkC3B,SAASqC,WAAT,CAAqBC,SAArB,EAAgCC,KAAhC,EAAuC;AACnC,QAAM1D,GAAG,GAAG,IAAIO,GAAJ,EAAZ;;AACA,MAAIkD,SAAS,CAAC5B,MAAV,GAAmB,CAAvB,EAA0B;AACtB;AACA;AACA;AACA,UAAM8B,MAAM,GAAGF,SAAS,CAACG,OAAV,CAAkB,KAAlB,EAAyB,EAAzB,EAA6BlD,KAA7B,CAAmC,GAAnC,CAAf;AACAiD,IAAAA,MAAM,CAAChD,OAAP,CAAgBkD,KAAD,IAAW;AACtB,YAAMC,KAAK,GAAGD,KAAK,CAAC/C,OAAN,CAAc,GAAd,CAAd;AACA,YAAM,CAACG,GAAD,EAAM8C,GAAN,IAAaD,KAAK,IAAI,CAAC,CAAV,GACf,CAACJ,KAAK,CAACL,SAAN,CAAgBQ,KAAhB,CAAD,EAAyB,EAAzB,CADe,GAEf,CAACH,KAAK,CAACL,SAAN,CAAgBQ,KAAK,CAAC7C,KAAN,CAAY,CAAZ,EAAe8C,KAAf,CAAhB,CAAD,EAAyCJ,KAAK,CAACH,WAAN,CAAkBM,KAAK,CAAC7C,KAAN,CAAY8C,KAAK,GAAG,CAApB,CAAlB,CAAzC,CAFJ;AAGA,YAAME,IAAI,GAAGhE,GAAG,CAACuB,GAAJ,CAAQN,GAAR,KAAgB,EAA7B;AACA+C,MAAAA,IAAI,CAACxC,IAAL,CAAUuC,GAAV;AACA/D,MAAAA,GAAG,CAACyB,GAAJ,CAAQR,GAAR,EAAa+C,IAAb;AACH,KARD;AASH;;AACD,SAAOhE,GAAP;AACH;AACD;AACA;AACA;;;AACA,MAAMiE,uBAAuB,GAAG,iBAAhC;AACA,MAAMC,8BAA8B,GAAG;AACnC,QAAM,GAD6B;AAEnC,QAAM,GAF6B;AAGnC,QAAM,GAH6B;AAInC,QAAM,GAJ6B;AAKnC,QAAM,GAL6B;AAMnC,QAAM,GAN6B;AAOnC,QAAM,GAP6B;AAQnC,QAAM,GAR6B;AASnC,QAAM;AAT6B,CAAvC;;AAWA,SAASf,gBAAT,CAA0BgB,CAA1B,EAA6B;AACzB,SAAOC,kBAAkB,CAACD,CAAD,CAAlB,CAAsBP,OAAtB,CAA8BK,uBAA9B,EAAuD,CAACI,CAAD,EAAIC,CAAJ,KAAUJ,8BAA8B,CAACI,CAAD,CAA9B,IAAqCD,CAAtG,CAAP;AACH;;AACD,SAASE,aAAT,CAAuBpD,KAAvB,EAA8B;AAC1B,SAAQ,GAAEA,KAAM,EAAhB;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqD,UAAN,CAAiB;AACbpE,EAAAA,WAAW,CAACqE,OAAO,GAAG,EAAX,EAAe;AACtB,SAAKC,OAAL,GAAe,IAAf;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,OAAL,GAAeH,OAAO,CAACG,OAAR,IAAmB,IAAI3B,oBAAJ,EAAlC;;AACA,QAAI,CAAC,CAACwB,OAAO,CAACI,UAAd,EAA0B;AACtB,UAAI,CAAC,CAACJ,OAAO,CAACK,UAAd,EAA0B;AACtB,cAAM,IAAIC,KAAJ,CAAW,gDAAX,CAAN;AACH;;AACD,WAAK/E,GAAL,GAAWwD,WAAW,CAACiB,OAAO,CAACI,UAAT,EAAqB,KAAKD,OAA1B,CAAtB;AACH,KALD,MAMK,IAAI,CAAC,CAACH,OAAO,CAACK,UAAd,EAA0B;AAC3B,WAAK9E,GAAL,GAAW,IAAIO,GAAJ,EAAX;AACAmB,MAAAA,MAAM,CAACC,IAAP,CAAY8C,OAAO,CAACK,UAApB,EAAgCnE,OAAhC,CAAwCM,GAAG,IAAI;AAC3C,cAAME,KAAK,GAAGsD,OAAO,CAACK,UAAR,CAAmB7D,GAAnB,CAAd;AACA,aAAKjB,GAAL,CAASyB,GAAT,CAAaR,GAAb,EAAkBc,KAAK,CAACiD,OAAN,CAAc7D,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,CAAjD;AACH,OAHD;AAIH,KANI,MAOA;AACD,WAAKnB,GAAL,GAAW,IAAX;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIsB,EAAAA,GAAG,CAACuC,KAAD,EAAQ;AACP,SAAK/B,IAAL;AACA,WAAO,KAAK9B,GAAL,CAASsB,GAAT,CAAauC,KAAb,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACItC,EAAAA,GAAG,CAACsC,KAAD,EAAQ;AACP,SAAK/B,IAAL;AACA,UAAMmD,GAAG,GAAG,KAAKjF,GAAL,CAASuB,GAAT,CAAasC,KAAb,CAAZ;AACA,WAAO,CAAC,CAACoB,GAAF,GAAQA,GAAG,CAAC,CAAD,CAAX,GAAiB,IAAxB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIhD,EAAAA,MAAM,CAAC4B,KAAD,EAAQ;AACV,SAAK/B,IAAL;AACA,WAAO,KAAK9B,GAAL,CAASuB,GAAT,CAAasC,KAAb,KAAuB,IAA9B;AACH;AACD;AACJ;AACA;AACA;;;AACIlC,EAAAA,IAAI,GAAG;AACH,SAAKG,IAAL;AACA,WAAOC,KAAK,CAACC,IAAN,CAAW,KAAKhC,GAAL,CAAS2B,IAAT,EAAX,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIO,EAAAA,MAAM,CAAC2B,KAAD,EAAQ1C,KAAR,EAAe;AACjB,WAAO,KAAKgB,KAAL,CAAW;AAAE0B,MAAAA,KAAF;AAAS1C,MAAAA,KAAT;AAAgBiB,MAAAA,EAAE,EAAE;AAApB,KAAX,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACI8C,EAAAA,SAAS,CAACvB,MAAD,EAAS;AACd,UAAMe,OAAO,GAAG,EAAhB;AACAhD,IAAAA,MAAM,CAACC,IAAP,CAAYgC,MAAZ,EAAoBhD,OAApB,CAA4BkD,KAAK,IAAI;AACjC,YAAM1C,KAAK,GAAGwC,MAAM,CAACE,KAAD,CAApB;;AACA,UAAI9B,KAAK,CAACiD,OAAN,CAAc7D,KAAd,CAAJ,EAA0B;AACtBA,QAAAA,KAAK,CAACR,OAAN,CAAcwE,MAAM,IAAI;AACpBT,UAAAA,OAAO,CAAClD,IAAR,CAAa;AAAEqC,YAAAA,KAAF;AAAS1C,YAAAA,KAAK,EAAEgE,MAAhB;AAAwB/C,YAAAA,EAAE,EAAE;AAA5B,WAAb;AACH,SAFD;AAGH,OAJD,MAKK;AACDsC,QAAAA,OAAO,CAAClD,IAAR,CAAa;AAAEqC,UAAAA,KAAF;AAAS1C,UAAAA,KAAK,EAAEA,KAAhB;AAAuBiB,UAAAA,EAAE,EAAE;AAA3B,SAAb;AACH;AACJ,KAVD;AAWA,WAAO,KAAKD,KAAL,CAAWuC,OAAX,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIjD,EAAAA,GAAG,CAACoC,KAAD,EAAQ1C,KAAR,EAAe;AACd,WAAO,KAAKgB,KAAL,CAAW;AAAE0B,MAAAA,KAAF;AAAS1C,MAAAA,KAAT;AAAgBiB,MAAAA,EAAE,EAAE;AAApB,KAAX,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,MAAM,CAACwB,KAAD,EAAQ1C,KAAR,EAAe;AACjB,WAAO,KAAKgB,KAAL,CAAW;AAAE0B,MAAAA,KAAF;AAAS1C,MAAAA,KAAT;AAAgBiB,MAAAA,EAAE,EAAE;AAApB,KAAX,CAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIgD,EAAAA,QAAQ,GAAG;AACP,SAAKtD,IAAL;AACA,WAAO,KAAKH,IAAL,GACF3B,GADE,CACEiB,GAAG,IAAI;AACZ,YAAMoE,IAAI,GAAG,KAAKT,OAAL,CAAa1B,SAAb,CAAuBjC,GAAvB,CAAb,CADY,CAEZ;AACA;AACA;;AACA,aAAO,KAAKjB,GAAL,CAASuB,GAAT,CAAaN,GAAb,EAAkBjB,GAAlB,CAAsBmB,KAAK,IAAIkE,IAAI,GAAG,GAAP,GAAa,KAAKT,OAAL,CAAaxB,WAAb,CAAyBjC,KAAzB,CAA5C,EACFmE,IADE,CACG,GADH,CAAP;AAEH,KARM,EASH;AACA;AAVG,KAWFvF,MAXE,CAWK8D,KAAK,IAAIA,KAAK,KAAK,EAXxB,EAYFyB,IAZE,CAYG,GAZH,CAAP;AAaH;;AACDnD,EAAAA,KAAK,CAACK,MAAD,EAAS;AACV,UAAML,KAAK,GAAG,IAAIqC,UAAJ,CAAe;AAAEI,MAAAA,OAAO,EAAE,KAAKA;AAAhB,KAAf,CAAd;AACAzC,IAAAA,KAAK,CAACwC,SAAN,GAAkB,KAAKA,SAAL,IAAkB,IAApC;AACAxC,IAAAA,KAAK,CAACuC,OAAN,GAAgB,CAAC,KAAKA,OAAL,IAAgB,EAAjB,EAAqB/B,MAArB,CAA4BH,MAA5B,CAAhB;AACA,WAAOL,KAAP;AACH;;AACDL,EAAAA,IAAI,GAAG;AACH,QAAI,KAAK9B,GAAL,KAAa,IAAjB,EAAuB;AACnB,WAAKA,GAAL,GAAW,IAAIO,GAAJ,EAAX;AACH;;AACD,QAAI,KAAKoE,SAAL,KAAmB,IAAvB,EAA6B;AACzB,WAAKA,SAAL,CAAe7C,IAAf;AACA,WAAK6C,SAAL,CAAehD,IAAf,GAAsBhB,OAAtB,CAA8BM,GAAG,IAAI,KAAKjB,GAAL,CAASyB,GAAT,CAAaR,GAAb,EAAkB,KAAK0D,SAAL,CAAe3E,GAAf,CAAmBuB,GAAnB,CAAuBN,GAAvB,CAAlB,CAArC;AACA,WAAKyD,OAAL,CAAa/D,OAAb,CAAqB6B,MAAM,IAAI;AAC3B,gBAAQA,MAAM,CAACJ,EAAf;AACI,eAAK,GAAL;AACA,eAAK,GAAL;AACI,kBAAMQ,IAAI,GAAG,CAACJ,MAAM,CAACJ,EAAP,KAAc,GAAd,GAAoB,KAAKpC,GAAL,CAASuB,GAAT,CAAaiB,MAAM,CAACqB,KAApB,CAApB,GAAiDhB,SAAlD,KAAgE,EAA7E;AACAD,YAAAA,IAAI,CAACpB,IAAL,CAAU+C,aAAa,CAAC/B,MAAM,CAACrB,KAAR,CAAvB;AACA,iBAAKnB,GAAL,CAASyB,GAAT,CAAae,MAAM,CAACqB,KAApB,EAA2BjB,IAA3B;AACA;;AACJ,eAAK,GAAL;AACI,gBAAIJ,MAAM,CAACrB,KAAP,KAAiB0B,SAArB,EAAgC;AAC5B,kBAAID,IAAI,GAAG,KAAK5C,GAAL,CAASuB,GAAT,CAAaiB,MAAM,CAACqB,KAApB,KAA8B,EAAzC;AACA,oBAAM0B,GAAG,GAAG3C,IAAI,CAAC9B,OAAL,CAAayD,aAAa,CAAC/B,MAAM,CAACrB,KAAR,CAA1B,CAAZ;;AACA,kBAAIoE,GAAG,KAAK,CAAC,CAAb,EAAgB;AACZ3C,gBAAAA,IAAI,CAAC4C,MAAL,CAAYD,GAAZ,EAAiB,CAAjB;AACH;;AACD,kBAAI3C,IAAI,CAACf,MAAL,GAAc,CAAlB,EAAqB;AACjB,qBAAK7B,GAAL,CAASyB,GAAT,CAAae,MAAM,CAACqB,KAApB,EAA2BjB,IAA3B;AACH,eAFD,MAGK;AACD,qBAAK5C,GAAL,CAASqC,MAAT,CAAgBG,MAAM,CAACqB,KAAvB;AACH;AACJ,aAZD,MAaK;AACD,mBAAK7D,GAAL,CAASqC,MAAT,CAAgBG,MAAM,CAACqB,KAAvB;AACA;AACH;;AAxBT;AA0BH,OA3BD;AA4BA,WAAKc,SAAL,GAAiB,KAAKD,OAAL,GAAe,IAAhC;AACH;AACJ;;AA5KY;AA+KjB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMe,gBAAN,CAAuB;AACnBrF,EAAAA,WAAW,CAACsF,YAAD,EAAe;AACtB,SAAKA,YAAL,GAAoBA,YAApB;AACH;;AAHkB;AAKvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,WAAN,CAAkB;AACdvF,EAAAA,WAAW,GAAG;AACV,SAAKJ,GAAL,GAAW,IAAIO,GAAJ,EAAX;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIkB,EAAAA,GAAG,CAACmE,KAAD,EAAQzE,KAAR,EAAe;AACd,SAAKnB,GAAL,CAASyB,GAAT,CAAamE,KAAb,EAAoBzE,KAApB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACII,EAAAA,GAAG,CAACqE,KAAD,EAAQ;AACP,QAAI,CAAC,KAAK5F,GAAL,CAASsB,GAAT,CAAasE,KAAb,CAAL,EAA0B;AACtB,WAAK5F,GAAL,CAASyB,GAAT,CAAamE,KAAb,EAAoBA,KAAK,CAACF,YAAN,EAApB;AACH;;AACD,WAAO,KAAK1F,GAAL,CAASuB,GAAT,CAAaqE,KAAb,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIvD,EAAAA,MAAM,CAACuD,KAAD,EAAQ;AACV,SAAK5F,GAAL,CAASqC,MAAT,CAAgBuD,KAAhB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;;;AACIjE,EAAAA,IAAI,GAAG;AACH,WAAO,KAAK3B,GAAL,CAAS2B,IAAT,EAAP;AACH;;AA7Ca;AAgDlB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,SAASkE,aAAT,CAAuBC,MAAvB,EAA+B;AAC3B,UAAQA,MAAR;AACI,SAAK,QAAL;AACA,SAAK,KAAL;AACA,SAAK,MAAL;AACA,SAAK,SAAL;AACA,SAAK,OAAL;AACI,aAAO,KAAP;;AACJ;AACI,aAAO,IAAP;AARR;AAUH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,aAAT,CAAuB5E,KAAvB,EAA8B;AAC1B,SAAO,OAAO6E,WAAP,KAAuB,WAAvB,IAAsC7E,KAAK,YAAY6E,WAA9D;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,MAAT,CAAgB9E,KAAhB,EAAuB;AACnB,SAAO,OAAO+E,IAAP,KAAgB,WAAhB,IAA+B/E,KAAK,YAAY+E,IAAvD;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,UAAT,CAAoBhF,KAApB,EAA2B;AACvB,SAAO,OAAOiF,QAAP,KAAoB,WAApB,IAAmCjF,KAAK,YAAYiF,QAA3D;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASC,iBAAT,CAA2BlF,KAA3B,EAAkC;AAC9B,SAAO,OAAOmF,eAAP,KAA2B,WAA3B,IAA0CnF,KAAK,YAAYmF,eAAlE;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,WAAN,CAAkB;AACdnG,EAAAA,WAAW,CAAC0F,MAAD,EAASU,GAAT,EAAcC,KAAd,EAAqBC,MAArB,EAA6B;AACpC,SAAKF,GAAL,GAAWA,GAAX;AACA;AACR;AACA;AACA;AACA;AACA;AACA;;AACQ,SAAKG,IAAL,GAAY,IAAZ;AACA;AACR;AACA;AACA;AACA;AACA;;AACQ,SAAKC,cAAL,GAAsB,KAAtB;AACA;AACR;AACA;;AACQ,SAAKC,eAAL,GAAuB,KAAvB;AACA;AACR;AACA;AACA;AACA;AACA;;AACQ,SAAKC,YAAL,GAAoB,MAApB;AACA,SAAKhB,MAAL,GAAcA,MAAM,CAACiB,WAAP,EAAd,CA5BoC,CA6BpC;AACA;;AACA,QAAItC,OAAJ,CA/BoC,CAgCpC;AACA;;AACA,QAAIoB,aAAa,CAAC,KAAKC,MAAN,CAAb,IAA8B,CAAC,CAACY,MAApC,EAA4C;AACxC;AACA,WAAKC,IAAL,GAAaF,KAAK,KAAK5D,SAAX,GAAwB4D,KAAxB,GAAgC,IAA5C;AACAhC,MAAAA,OAAO,GAAGiC,MAAV;AACH,KAJD,MAKK;AACD;AACAjC,MAAAA,OAAO,GAAGgC,KAAV;AACH,KA1CmC,CA2CpC;;;AACA,QAAIhC,OAAJ,EAAa;AACT;AACA,WAAKmC,cAAL,GAAsB,CAAC,CAACnC,OAAO,CAACmC,cAAhC;AACA,WAAKC,eAAL,GAAuB,CAAC,CAACpC,OAAO,CAACoC,eAAjC,CAHS,CAIT;;AACA,UAAI,CAAC,CAACpC,OAAO,CAACqC,YAAd,EAA4B;AACxB,aAAKA,YAAL,GAAoBrC,OAAO,CAACqC,YAA5B;AACH,OAPQ,CAQT;;;AACA,UAAI,CAAC,CAACrC,OAAO,CAACpE,OAAd,EAAuB;AACnB,aAAKA,OAAL,GAAeoE,OAAO,CAACpE,OAAvB;AACH;;AACD,UAAI,CAAC,CAACoE,OAAO,CAACuC,OAAd,EAAuB;AACnB,aAAKA,OAAL,GAAevC,OAAO,CAACuC,OAAvB;AACH;;AACD,UAAI,CAAC,CAACvC,OAAO,CAACd,MAAd,EAAsB;AAClB,aAAKA,MAAL,GAAcc,OAAO,CAACd,MAAtB;AACH;AACJ,KA9DmC,CA+DpC;;;AACA,QAAI,CAAC,KAAKtD,OAAV,EAAmB;AACf,WAAKA,OAAL,GAAe,IAAIF,WAAJ,EAAf;AACH,KAlEmC,CAmEpC;;;AACA,QAAI,CAAC,KAAK6G,OAAV,EAAmB;AACf,WAAKA,OAAL,GAAe,IAAIrB,WAAJ,EAAf;AACH,KAtEmC,CAuEpC;;;AACA,QAAI,CAAC,KAAKhC,MAAV,EAAkB;AACd,WAAKA,MAAL,GAAc,IAAIa,UAAJ,EAAd;AACA,WAAKyC,aAAL,GAAqBT,GAArB;AACH,KAHD,MAIK;AACD;AACA,YAAM7C,MAAM,GAAG,KAAKA,MAAL,CAAYyB,QAAZ,EAAf;;AACA,UAAIzB,MAAM,CAAC9B,MAAP,KAAkB,CAAtB,EAAyB;AACrB;AACA,aAAKoF,aAAL,GAAqBT,GAArB;AACH,OAHD,MAIK;AACD;AACA,cAAMU,IAAI,GAAGV,GAAG,CAAC1F,OAAJ,CAAY,GAAZ,CAAb,CAFC,CAGD;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,cAAMqG,GAAG,GAAGD,IAAI,KAAK,CAAC,CAAV,GAAc,GAAd,GAAqBA,IAAI,GAAGV,GAAG,CAAC3E,MAAJ,GAAa,CAApB,GAAwB,GAAxB,GAA8B,EAA/D;AACA,aAAKoF,aAAL,GAAqBT,GAAG,GAAGW,GAAN,GAAYxD,MAAjC;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACIyD,EAAAA,aAAa,GAAG;AACZ;AACA,QAAI,KAAKT,IAAL,KAAc,IAAlB,EAAwB;AACpB,aAAO,IAAP;AACH,KAJW,CAKZ;AACA;;;AACA,QAAIZ,aAAa,CAAC,KAAKY,IAAN,CAAb,IAA4BV,MAAM,CAAC,KAAKU,IAAN,CAAlC,IAAiDR,UAAU,CAAC,KAAKQ,IAAN,CAA3D,IACAN,iBAAiB,CAAC,KAAKM,IAAN,CADjB,IACgC,OAAO,KAAKA,IAAZ,KAAqB,QADzD,EACmE;AAC/D,aAAO,KAAKA,IAAZ;AACH,KAVW,CAWZ;;;AACA,QAAI,KAAKA,IAAL,YAAqBnC,UAAzB,EAAqC;AACjC,aAAO,KAAKmC,IAAL,CAAUvB,QAAV,EAAP;AACH,KAdW,CAeZ;;;AACA,QAAI,OAAO,KAAKuB,IAAZ,KAAqB,QAArB,IAAiC,OAAO,KAAKA,IAAZ,KAAqB,SAAtD,IACA5E,KAAK,CAACiD,OAAN,CAAc,KAAK2B,IAAnB,CADJ,EAC8B;AAC1B,aAAOU,IAAI,CAACC,SAAL,CAAe,KAAKX,IAApB,CAAP;AACH,KAnBW,CAoBZ;;;AACA,WAAO,KAAKA,IAAL,CAAUvB,QAAV,EAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACImC,EAAAA,uBAAuB,GAAG;AACtB;AACA,QAAI,KAAKZ,IAAL,KAAc,IAAlB,EAAwB;AACpB,aAAO,IAAP;AACH,KAJqB,CAKtB;;;AACA,QAAIR,UAAU,CAAC,KAAKQ,IAAN,CAAd,EAA2B;AACvB,aAAO,IAAP;AACH,KARqB,CAStB;AACA;;;AACA,QAAIV,MAAM,CAAC,KAAKU,IAAN,CAAV,EAAuB;AACnB,aAAO,KAAKA,IAAL,CAAUa,IAAV,IAAkB,IAAzB;AACH,KAbqB,CActB;;;AACA,QAAIzB,aAAa,CAAC,KAAKY,IAAN,CAAjB,EAA8B;AAC1B,aAAO,IAAP;AACH,KAjBqB,CAkBtB;AACA;;;AACA,QAAI,OAAO,KAAKA,IAAZ,KAAqB,QAAzB,EAAmC;AAC/B,aAAO,YAAP;AACH,KAtBqB,CAuBtB;;;AACA,QAAI,KAAKA,IAAL,YAAqBnC,UAAzB,EAAqC;AACjC,aAAO,iDAAP;AACH,KA1BqB,CA2BtB;;;AACA,QAAI,OAAO,KAAKmC,IAAZ,KAAqB,QAArB,IAAiC,OAAO,KAAKA,IAAZ,KAAqB,QAAtD,IACA,OAAO,KAAKA,IAAZ,KAAqB,SADzB,EACoC;AAChC,aAAO,kBAAP;AACH,KA/BqB,CAgCtB;;;AACA,WAAO,IAAP;AACH;;AACDxE,EAAAA,KAAK,CAACK,MAAM,GAAG,EAAV,EAAc;AACf;AACA;AACA,UAAMsD,MAAM,GAAGtD,MAAM,CAACsD,MAAP,IAAiB,KAAKA,MAArC;AACA,UAAMU,GAAG,GAAGhE,MAAM,CAACgE,GAAP,IAAc,KAAKA,GAA/B;AACA,UAAMM,YAAY,GAAGtE,MAAM,CAACsE,YAAP,IAAuB,KAAKA,YAAjD,CALe,CAMf;AACA;AACA;AACA;;AACA,UAAMH,IAAI,GAAInE,MAAM,CAACmE,IAAP,KAAgB9D,SAAjB,GAA8BL,MAAM,CAACmE,IAArC,GAA4C,KAAKA,IAA9D,CAVe,CAWf;AACA;;AACA,UAAME,eAAe,GAAIrE,MAAM,CAACqE,eAAP,KAA2BhE,SAA5B,GAAyCL,MAAM,CAACqE,eAAhD,GAAkE,KAAKA,eAA/F;AACA,UAAMD,cAAc,GAAIpE,MAAM,CAACoE,cAAP,KAA0B/D,SAA3B,GAAwCL,MAAM,CAACoE,cAA/C,GAAgE,KAAKA,cAA5F,CAde,CAef;AACA;;AACA,QAAIvG,OAAO,GAAGmC,MAAM,CAACnC,OAAP,IAAkB,KAAKA,OAArC;AACA,QAAIsD,MAAM,GAAGnB,MAAM,CAACmB,MAAP,IAAiB,KAAKA,MAAnC,CAlBe,CAmBf;;AACA,UAAMqD,OAAO,GAAGxE,MAAM,CAACwE,OAAP,IAAkB,KAAKA,OAAvC,CApBe,CAqBf;;AACA,QAAIxE,MAAM,CAACiF,UAAP,KAAsB5E,SAA1B,EAAqC;AACjC;AACAxC,MAAAA,OAAO,GACHqB,MAAM,CAACC,IAAP,CAAYa,MAAM,CAACiF,UAAnB,EACKC,MADL,CACY,CAACrH,OAAD,EAAUU,IAAV,KAAmBV,OAAO,CAACoB,GAAR,CAAYV,IAAZ,EAAkByB,MAAM,CAACiF,UAAP,CAAkB1G,IAAlB,CAAlB,CAD/B,EAC2EV,OAD3E,CADJ;AAGH,KA3Bc,CA4Bf;;;AACA,QAAImC,MAAM,CAACmF,SAAX,EAAsB;AAClB;AACAhE,MAAAA,MAAM,GAAGjC,MAAM,CAACC,IAAP,CAAYa,MAAM,CAACmF,SAAnB,EACJD,MADI,CACG,CAAC/D,MAAD,EAASE,KAAT,KAAmBF,MAAM,CAAClC,GAAP,CAAWoC,KAAX,EAAkBrB,MAAM,CAACmF,SAAP,CAAiB9D,KAAjB,CAAlB,CADtB,EACkEF,MADlE,CAAT;AAEH,KAjCc,CAkCf;;;AACA,WAAO,IAAI4C,WAAJ,CAAgBT,MAAhB,EAAwBU,GAAxB,EAA6BG,IAA7B,EAAmC;AACtChD,MAAAA,MADsC;AAEtCtD,MAAAA,OAFsC;AAGtC2G,MAAAA,OAHsC;AAItCJ,MAAAA,cAJsC;AAKtCE,MAAAA,YALsC;AAMtCD,MAAAA;AANsC,KAAnC,CAAP;AAQH;;AAlNa;AAqNlB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIe,aAAJ;;AACA,CAAC,UAAUA,aAAV,EAAyB;AACtB;AACJ;AACA;AACIA,EAAAA,aAAa,CAACA,aAAa,CAAC,MAAD,CAAb,GAAwB,CAAzB,CAAb,GAA2C,MAA3C;AACA;AACJ;AACA;;AACIA,EAAAA,aAAa,CAACA,aAAa,CAAC,gBAAD,CAAb,GAAkC,CAAnC,CAAb,GAAqD,gBAArD;AACA;AACJ;AACA;;AACIA,EAAAA,aAAa,CAACA,aAAa,CAAC,gBAAD,CAAb,GAAkC,CAAnC,CAAb,GAAqD,gBAArD;AACA;AACJ;AACA;;AACIA,EAAAA,aAAa,CAACA,aAAa,CAAC,kBAAD,CAAb,GAAoC,CAArC,CAAb,GAAuD,kBAAvD;AACA;AACJ;AACA;;AACIA,EAAAA,aAAa,CAACA,aAAa,CAAC,UAAD,CAAb,GAA4B,CAA7B,CAAb,GAA+C,UAA/C;AACA;AACJ;AACA;;AACIA,EAAAA,aAAa,CAACA,aAAa,CAAC,MAAD,CAAb,GAAwB,CAAzB,CAAb,GAA2C,MAA3C;AACH,CAzBD,EAyBGA,aAAa,KAAKA,aAAa,GAAG,EAArB,CAzBhB;AA0BA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,gBAAN,CAAuB;AACnB;AACJ;AACA;AACA;AACA;AACA;AACIzH,EAAAA,WAAW,CAAC0B,IAAD,EAAOgG,aAAa,GAAG;AAAI;AAA3B,IAAqCC,iBAAiB,GAAG,IAAzD,EAA+D;AACtE;AACA;AACA,SAAK1H,OAAL,GAAeyB,IAAI,CAACzB,OAAL,IAAgB,IAAIF,WAAJ,EAA/B;AACA,SAAK6H,MAAL,GAAclG,IAAI,CAACkG,MAAL,KAAgBnF,SAAhB,GAA4Bf,IAAI,CAACkG,MAAjC,GAA0CF,aAAxD;AACA,SAAKG,UAAL,GAAkBnG,IAAI,CAACmG,UAAL,IAAmBF,iBAArC;AACA,SAAKvB,GAAL,GAAW1E,IAAI,CAAC0E,GAAL,IAAY,IAAvB,CANsE,CAOtE;;AACA,SAAK0B,EAAL,GAAU,KAAKF,MAAL,IAAe,GAAf,IAAsB,KAAKA,MAAL,GAAc,GAA9C;AACH;;AAhBkB;AAkBvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMG,kBAAN,SAAiCN,gBAAjC,CAAkD;AAC9C;AACJ;AACA;AACIzH,EAAAA,WAAW,CAAC0B,IAAI,GAAG,EAAR,EAAY;AACnB,UAAMA,IAAN;AACA,SAAK0F,IAAL,GAAYI,aAAa,CAACQ,cAA1B;AACH;AACD;AACJ;AACA;AACA;;;AACIjG,EAAAA,KAAK,CAACK,MAAM,GAAG,EAAV,EAAc;AACf;AACA;AACA,WAAO,IAAI2F,kBAAJ,CAAuB;AAC1B9H,MAAAA,OAAO,EAAEmC,MAAM,CAACnC,OAAP,IAAkB,KAAKA,OADN;AAE1B2H,MAAAA,MAAM,EAAExF,MAAM,CAACwF,MAAP,KAAkBnF,SAAlB,GAA8BL,MAAM,CAACwF,MAArC,GAA8C,KAAKA,MAFjC;AAG1BC,MAAAA,UAAU,EAAEzF,MAAM,CAACyF,UAAP,IAAqB,KAAKA,UAHZ;AAI1BzB,MAAAA,GAAG,EAAEhE,MAAM,CAACgE,GAAP,IAAc,KAAKA,GAAnB,IAA0B3D;AAJL,KAAvB,CAAP;AAMH;;AArB6C;AAuBlD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMwF,YAAN,SAA2BR,gBAA3B,CAA4C;AACxC;AACJ;AACA;AACIzH,EAAAA,WAAW,CAAC0B,IAAI,GAAG,EAAR,EAAY;AACnB,UAAMA,IAAN;AACA,SAAK0F,IAAL,GAAYI,aAAa,CAACU,QAA1B;AACA,SAAK3B,IAAL,GAAY7E,IAAI,CAAC6E,IAAL,KAAc9D,SAAd,GAA0Bf,IAAI,CAAC6E,IAA/B,GAAsC,IAAlD;AACH;;AACDxE,EAAAA,KAAK,CAACK,MAAM,GAAG,EAAV,EAAc;AACf,WAAO,IAAI6F,YAAJ,CAAiB;AACpB1B,MAAAA,IAAI,EAAGnE,MAAM,CAACmE,IAAP,KAAgB9D,SAAjB,GAA8BL,MAAM,CAACmE,IAArC,GAA4C,KAAKA,IADnC;AAEpBtG,MAAAA,OAAO,EAAEmC,MAAM,CAACnC,OAAP,IAAkB,KAAKA,OAFZ;AAGpB2H,MAAAA,MAAM,EAAGxF,MAAM,CAACwF,MAAP,KAAkBnF,SAAnB,GAAgCL,MAAM,CAACwF,MAAvC,GAAgD,KAAKA,MAHzC;AAIpBC,MAAAA,UAAU,EAAEzF,MAAM,CAACyF,UAAP,IAAqB,KAAKA,UAJlB;AAKpBzB,MAAAA,GAAG,EAAEhE,MAAM,CAACgE,GAAP,IAAc,KAAKA,GAAnB,IAA0B3D;AALX,KAAjB,CAAP;AAOH;;AAjBuC;AAmB5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0F,iBAAN,SAAgCV,gBAAhC,CAAiD;AAC7CzH,EAAAA,WAAW,CAAC0B,IAAD,EAAO;AACd;AACA,UAAMA,IAAN,EAAY,CAAZ,EAAe,eAAf;AACA,SAAKf,IAAL,GAAY,mBAAZ;AACA;AACR;AACA;;AACQ,SAAKmH,EAAL,GAAU,KAAV,CAPc,CAQd;AACA;AACA;;AACA,QAAI,KAAKF,MAAL,IAAe,GAAf,IAAsB,KAAKA,MAAL,GAAc,GAAxC,EAA6C;AACzC,WAAKQ,OAAL,GAAgB,mCAAkC1G,IAAI,CAAC0E,GAAL,IAAY,eAAgB,EAA9E;AACH,KAFD,MAGK;AACD,WAAKgC,OAAL,GAAgB,6BAA4B1G,IAAI,CAAC0E,GAAL,IAAY,eAAgB,KAAI1E,IAAI,CAACkG,MAAO,IAAGlG,IAAI,CAACmG,UAAW,EAA3G;AACH;;AACD,SAAKQ,KAAL,GAAa3G,IAAI,CAAC2G,KAAL,IAAc,IAA3B;AACH;;AAnB4C;AAsBjD;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,OAAT,CAAiBjE,OAAjB,EAA0BkC,IAA1B,EAAgC;AAC5B,SAAO;AACHA,IAAAA,IADG;AAEHtG,IAAAA,OAAO,EAAEoE,OAAO,CAACpE,OAFd;AAGH2G,IAAAA,OAAO,EAAEvC,OAAO,CAACuC,OAHd;AAIH2B,IAAAA,OAAO,EAAElE,OAAO,CAACkE,OAJd;AAKHhF,IAAAA,MAAM,EAAEc,OAAO,CAACd,MALb;AAMHiD,IAAAA,cAAc,EAAEnC,OAAO,CAACmC,cANrB;AAOHE,IAAAA,YAAY,EAAErC,OAAO,CAACqC,YAPnB;AAQHD,IAAAA,eAAe,EAAEpC,OAAO,CAACoC;AARtB,GAAP;AAUH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM+B,UAAN,CAAiB;AACbxI,EAAAA,WAAW,CAACyI,OAAD,EAAU;AACjB,SAAKA,OAAL,GAAeA,OAAf;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,OAAO,CAACC,KAAD,EAAQvC,GAAR,EAAa/B,OAAO,GAAG,EAAvB,EAA2B;AAC9B,QAAIuE,GAAJ,CAD8B,CAE9B;;AACA,QAAID,KAAK,YAAYxC,WAArB,EAAkC;AAC9B;AACA;AACAyC,MAAAA,GAAG,GAAGD,KAAN;AACH,KAJD,MAKK;AACD;AACA;AACA;AACA;AACA,UAAI1I,OAAO,GAAGwC,SAAd;;AACA,UAAI4B,OAAO,CAACpE,OAAR,YAA2BF,WAA/B,EAA4C;AACxCE,QAAAA,OAAO,GAAGoE,OAAO,CAACpE,OAAlB;AACH,OAFD,MAGK;AACDA,QAAAA,OAAO,GAAG,IAAIF,WAAJ,CAAgBsE,OAAO,CAACpE,OAAxB,CAAV;AACH,OAXA,CAYD;;;AACA,UAAIsD,MAAM,GAAGd,SAAb;;AACA,UAAI,CAAC,CAAC4B,OAAO,CAACd,MAAd,EAAsB;AAClB,YAAIc,OAAO,CAACd,MAAR,YAA0Ba,UAA9B,EAA0C;AACtCb,UAAAA,MAAM,GAAGc,OAAO,CAACd,MAAjB;AACH,SAFD,MAGK;AACDA,UAAAA,MAAM,GAAG,IAAIa,UAAJ,CAAe;AAAEM,YAAAA,UAAU,EAAEL,OAAO,CAACd;AAAtB,WAAf,CAAT;AACH;AACJ,OArBA,CAsBD;;;AACAqF,MAAAA,GAAG,GAAG,IAAIzC,WAAJ,CAAgBwC,KAAhB,EAAuBvC,GAAvB,EAA6B/B,OAAO,CAACkC,IAAR,KAAiB9D,SAAjB,GAA6B4B,OAAO,CAACkC,IAArC,GAA4C,IAAzE,EAAgF;AAClFtG,QAAAA,OADkF;AAElF2G,QAAAA,OAAO,EAAEvC,OAAO,CAACuC,OAFiE;AAGlFrD,QAAAA,MAHkF;AAIlFiD,QAAAA,cAAc,EAAEnC,OAAO,CAACmC,cAJ0D;AAKlF;AACAE,QAAAA,YAAY,EAAErC,OAAO,CAACqC,YAAR,IAAwB,MAN4C;AAOlFD,QAAAA,eAAe,EAAEpC,OAAO,CAACoC;AAPyD,OAAhF,CAAN;AASH,KAxC6B,CAyC9B;AACA;AACA;AACA;;;AACA,UAAMoC,OAAO,GAAGrJ,EAAE,CAACoJ,GAAD,CAAF,CAAQE,IAAR,CAAapJ,SAAS,CAAEkJ,GAAD,IAAS,KAAKH,OAAL,CAAaM,MAAb,CAAoBH,GAApB,CAAV,CAAtB,CAAhB,CA7C8B,CA8C9B;AACA;AACA;;AACA,QAAID,KAAK,YAAYxC,WAAjB,IAAgC9B,OAAO,CAACkE,OAAR,KAAoB,QAAxD,EAAkE;AAC9D,aAAOM,OAAP;AACH,KAnD6B,CAoD9B;AACA;AACA;;;AACA,UAAMG,IAAI,GAAGH,OAAO,CAACC,IAAR,CAAanJ,MAAM,CAAEsJ,KAAD,IAAWA,KAAK,YAAYhB,YAA7B,CAAnB,CAAb,CAvD8B,CAwD9B;;AACA,YAAQ5D,OAAO,CAACkE,OAAR,IAAmB,MAA3B;AACI,WAAK,MAAL;AACI;AACA;AACA;AACA;AACA;AACA,gBAAQK,GAAG,CAAClC,YAAZ;AACI,eAAK,aAAL;AACI,mBAAOsC,IAAI,CAACF,IAAL,CAAUlJ,GAAG,CAAEiF,GAAD,IAAS;AAC1B;AACA,kBAAIA,GAAG,CAAC0B,IAAJ,KAAa,IAAb,IAAqB,EAAE1B,GAAG,CAAC0B,IAAJ,YAAoBX,WAAtB,CAAzB,EAA6D;AACzD,sBAAM,IAAIjB,KAAJ,CAAU,iCAAV,CAAN;AACH;;AACD,qBAAOE,GAAG,CAAC0B,IAAX;AACH,aANmB,CAAb,CAAP;;AAOJ,eAAK,MAAL;AACI,mBAAOyC,IAAI,CAACF,IAAL,CAAUlJ,GAAG,CAAEiF,GAAD,IAAS;AAC1B;AACA,kBAAIA,GAAG,CAAC0B,IAAJ,KAAa,IAAb,IAAqB,EAAE1B,GAAG,CAAC0B,IAAJ,YAAoBT,IAAtB,CAAzB,EAAsD;AAClD,sBAAM,IAAInB,KAAJ,CAAU,yBAAV,CAAN;AACH;;AACD,qBAAOE,GAAG,CAAC0B,IAAX;AACH,aANmB,CAAb,CAAP;;AAOJ,eAAK,MAAL;AACI,mBAAOyC,IAAI,CAACF,IAAL,CAAUlJ,GAAG,CAAEiF,GAAD,IAAS;AAC1B;AACA,kBAAIA,GAAG,CAAC0B,IAAJ,KAAa,IAAb,IAAqB,OAAO1B,GAAG,CAAC0B,IAAX,KAAoB,QAA7C,EAAuD;AACnD,sBAAM,IAAI5B,KAAJ,CAAU,2BAAV,CAAN;AACH;;AACD,qBAAOE,GAAG,CAAC0B,IAAX;AACH,aANmB,CAAb,CAAP;;AAOJ,eAAK,MAAL;AACA;AACI;AACA,mBAAOyC,IAAI,CAACF,IAAL,CAAUlJ,GAAG,CAAEiF,GAAD,IAASA,GAAG,CAAC0B,IAAd,CAAb,CAAP;AA5BR;;AA8BJ,WAAK,UAAL;AACI;AACA,eAAOyC,IAAP;;AACJ;AACI;AACA,cAAM,IAAIrE,KAAJ,CAAW,uCAAsCN,OAAO,CAACkE,OAAQ,GAAjE,CAAN;AA1CR;AA4CH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACItG,EAAAA,MAAM,CAACmE,GAAD,EAAM/B,OAAO,GAAG,EAAhB,EAAoB;AACtB,WAAO,KAAKqE,OAAL,CAAa,QAAb,EAAuBtC,GAAvB,EAA4B/B,OAA5B,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIlD,EAAAA,GAAG,CAACiF,GAAD,EAAM/B,OAAO,GAAG,EAAhB,EAAoB;AACnB,WAAO,KAAKqE,OAAL,CAAa,KAAb,EAAoBtC,GAApB,EAAyB/B,OAAzB,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI6E,EAAAA,IAAI,CAAC9C,GAAD,EAAM/B,OAAO,GAAG,EAAhB,EAAoB;AACpB,WAAO,KAAKqE,OAAL,CAAa,MAAb,EAAqBtC,GAArB,EAA0B/B,OAA1B,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACI8E,EAAAA,KAAK,CAAC/C,GAAD,EAAMgD,aAAN,EAAqB;AACtB,WAAO,KAAKV,OAAL,CAAa,OAAb,EAAsBtC,GAAtB,EAA2B;AAC9B7C,MAAAA,MAAM,EAAE,IAAIa,UAAJ,GAAiBtC,MAAjB,CAAwBsH,aAAxB,EAAuC,gBAAvC,CADsB;AAE9Bb,MAAAA,OAAO,EAAE,MAFqB;AAG9B7B,MAAAA,YAAY,EAAE;AAHgB,KAA3B,CAAP;AAKH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIrC,EAAAA,OAAO,CAAC+B,GAAD,EAAM/B,OAAO,GAAG,EAAhB,EAAoB;AACvB,WAAO,KAAKqE,OAAL,CAAa,SAAb,EAAwBtC,GAAxB,EAA6B/B,OAA7B,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIgF,EAAAA,KAAK,CAACjD,GAAD,EAAMG,IAAN,EAAYlC,OAAO,GAAG,EAAtB,EAA0B;AAC3B,WAAO,KAAKqE,OAAL,CAAa,OAAb,EAAsBtC,GAAtB,EAA2BkC,OAAO,CAACjE,OAAD,EAAUkC,IAAV,CAAlC,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACI+C,EAAAA,IAAI,CAAClD,GAAD,EAAMG,IAAN,EAAYlC,OAAO,GAAG,EAAtB,EAA0B;AAC1B,WAAO,KAAKqE,OAAL,CAAa,MAAb,EAAqBtC,GAArB,EAA0BkC,OAAO,CAACjE,OAAD,EAAUkC,IAAV,CAAjC,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIgD,EAAAA,GAAG,CAACnD,GAAD,EAAMG,IAAN,EAAYlC,OAAO,GAAG,EAAtB,EAA0B;AACzB,WAAO,KAAKqE,OAAL,CAAa,KAAb,EAAoBtC,GAApB,EAAyBkC,OAAO,CAACjE,OAAD,EAAUkC,IAAV,CAAhC,CAAP;AACH;;AA9NY;;AAgOjBiC,UAAU,CAACgB,IAAX;AAAA,mBAAuGhB,UAAvG,EAA6FtJ,EAA7F,UAAmIW,WAAnI;AAAA;;AACA2I,UAAU,CAACiB,KAAX,kBAD6FvK,EAC7F;AAAA,SAA2GsJ,UAA3G;AAAA,WAA2GA,UAA3G;AAAA;;AACA;AAAA,qDAF6FtJ,EAE7F,mBAA2FsJ,UAA3F,EAAmH,CAAC;AACxGpB,IAAAA,IAAI,EAAEjI;AADkG,GAAD,CAAnH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEiI,MAAAA,IAAI,EAAEvH;AAAR,KAAD,CAAP;AAAiC,GAF3E;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM6J,sBAAN,CAA6B;AACzB1J,EAAAA,WAAW,CAAC2J,IAAD,EAAOC,WAAP,EAAoB;AAC3B,SAAKD,IAAL,GAAYA,IAAZ;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACH;;AACDb,EAAAA,MAAM,CAACH,GAAD,EAAM;AACR,WAAO,KAAKgB,WAAL,CAAiBC,SAAjB,CAA2BjB,GAA3B,EAAgC,KAAKe,IAArC,CAAP;AACH;;AAPwB;AAS7B;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMG,iBAAiB,GAAG,IAAI1K,cAAJ,CAAmB,mBAAnB,CAA1B;;AACA,MAAM2K,eAAN,CAAsB;AAClBF,EAAAA,SAAS,CAACjB,GAAD,EAAMe,IAAN,EAAY;AACjB,WAAOA,IAAI,CAACZ,MAAL,CAAYH,GAAZ,CAAP;AACH;;AAHiB;;AAKtBmB,eAAe,CAACP,IAAhB;AAAA,mBAA4GO,eAA5G;AAAA;;AACAA,eAAe,CAACN,KAAhB,kBAxC6FvK,EAwC7F;AAAA,SAAgH6K,eAAhH;AAAA,WAAgHA,eAAhH;AAAA;;AACA;AAAA,qDAzC6F7K,EAyC7F,mBAA2F6K,eAA3F,EAAwH,CAAC;AAC7G3C,IAAAA,IAAI,EAAEjI;AADuG,GAAD,CAAxH;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAI6K,aAAa,GAAG,CAApB,C,CACA;AACA;;AACA,MAAMC,qBAAqB,GAAG,gDAA9B,C,CACA;AACA;;AACA,MAAMC,sBAAsB,GAAG,+CAA/B;AACA,MAAMC,6BAA6B,GAAG,6CAAtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,oBAAN,CAA2B;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,kBAAN,CAAyB;AACrBrK,EAAAA,WAAW,CAACsK,WAAD,EAAcC,QAAd,EAAwB;AAC/B,SAAKD,WAAL,GAAmBA,WAAnB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA;AACR;AACA;;AACQ,SAAKC,eAAL,GAAuBC,OAAO,CAACC,OAAR,EAAvB;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,YAAY,GAAG;AACX,WAAQ,qBAAoBX,aAAa,EAAG,EAA5C;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIjB,EAAAA,MAAM,CAACH,GAAD,EAAM;AACR;AACA;AACA,QAAIA,GAAG,CAAClD,MAAJ,KAAe,OAAnB,EAA4B;AACxB,YAAM,IAAIf,KAAJ,CAAUuF,sBAAV,CAAN;AACH,KAFD,MAGK,IAAItB,GAAG,CAAClC,YAAJ,KAAqB,MAAzB,EAAiC;AAClC,YAAM,IAAI/B,KAAJ,CAAUwF,6BAAV,CAAN;AACH,KARO,CASR;;;AACA,WAAO,IAAI1K,UAAJ,CAAgBmL,QAAD,IAAc;AAChC;AACA;AACA;AACA,YAAMC,QAAQ,GAAG,KAAKF,YAAL,EAAjB;AACA,YAAMvE,GAAG,GAAGwC,GAAG,CAAC/B,aAAJ,CAAkBrD,OAAlB,CAA0B,sBAA1B,EAAmD,IAAGqH,QAAS,IAA/D,CAAZ,CALgC,CAMhC;;AACA,YAAMC,IAAI,GAAG,KAAKP,QAAL,CAAcQ,aAAd,CAA4B,QAA5B,CAAb;AACAD,MAAAA,IAAI,CAACE,GAAL,GAAW5E,GAAX,CARgC,CAShC;AACA;AACA;;AACA,UAAIG,IAAI,GAAG,IAAX,CAZgC,CAahC;;AACA,UAAI0E,QAAQ,GAAG,KAAf,CAdgC,CAehC;AACA;;AACA,UAAIC,SAAS,GAAG,KAAhB,CAjBgC,CAkBhC;AACA;AACA;;AACA,WAAKZ,WAAL,CAAiBO,QAAjB,IAA8BM,IAAD,IAAU;AACnC;AACA,eAAO,KAAKb,WAAL,CAAiBO,QAAjB,CAAP,CAFmC,CAGnC;;AACA,YAAIK,SAAJ,EAAe;AACX;AACH,SANkC,CAOnC;;;AACA3E,QAAAA,IAAI,GAAG4E,IAAP;AACAF,QAAAA,QAAQ,GAAG,IAAX;AACH,OAVD,CArBgC,CAgChC;AACA;AACA;;;AACA,YAAMG,OAAO,GAAG,MAAM;AAClB;AACA,YAAIN,IAAI,CAACO,UAAT,EAAqB;AACjBP,UAAAA,IAAI,CAACO,UAAL,CAAgBC,WAAhB,CAA4BR,IAA5B;AACH,SAJiB,CAKlB;AACA;;;AACA,eAAO,KAAKR,WAAL,CAAiBO,QAAjB,CAAP;AACH,OARD,CAnCgC,CA4ChC;AACA;AACA;AACA;;;AACA,YAAMU,MAAM,GAAItC,KAAD,IAAW;AACtB;AACA,YAAIiC,SAAJ,EAAe;AACX;AACH,SAJqB,CAKtB;AACA;AACA;;;AACA,aAAKV,eAAL,CAAqBgB,IAArB,CAA0B,MAAM;AAC5B;AACAJ,UAAAA,OAAO,GAFqB,CAG5B;;AACA,cAAI,CAACH,QAAL,EAAe;AACX;AACA;AACAL,YAAAA,QAAQ,CAACvC,KAAT,CAAe,IAAIF,iBAAJ,CAAsB;AACjC/B,cAAAA,GADiC;AAEjCwB,cAAAA,MAAM,EAAE,CAFyB;AAGjCC,cAAAA,UAAU,EAAE,aAHqB;AAIjCQ,cAAAA,KAAK,EAAE,IAAI1D,KAAJ,CAAUsF,qBAAV;AAJ0B,aAAtB,CAAf;AAMA;AACH,WAd2B,CAe5B;AACA;;;AACAW,UAAAA,QAAQ,CAACjB,IAAT,CAAc,IAAI1B,YAAJ,CAAiB;AAC3B1B,YAAAA,IAD2B;AAE3BqB,YAAAA,MAAM,EAAE;AAAI;AAFe;AAG3BC,YAAAA,UAAU,EAAE,IAHe;AAI3BzB,YAAAA;AAJ2B,WAAjB,CAAd,EAjB4B,CAuB5B;;AACAwE,UAAAA,QAAQ,CAACa,QAAT;AACH,SAzBD;AA0BH,OAlCD,CAhDgC,CAmFhC;AACA;AACA;;;AACA,YAAMC,OAAO,GAAIrD,KAAD,IAAW;AACvB;AACA,YAAI6C,SAAJ,EAAe;AACX;AACH;;AACDE,QAAAA,OAAO,GALgB,CAMvB;;AACAR,QAAAA,QAAQ,CAACvC,KAAT,CAAe,IAAIF,iBAAJ,CAAsB;AACjCE,UAAAA,KADiC;AAEjCT,UAAAA,MAAM,EAAE,CAFyB;AAGjCC,UAAAA,UAAU,EAAE,aAHqB;AAIjCzB,UAAAA;AAJiC,SAAtB,CAAf;AAMH,OAbD,CAtFgC,CAoGhC;AACA;;;AACA0E,MAAAA,IAAI,CAACa,gBAAL,CAAsB,MAAtB,EAA8BJ,MAA9B;AACAT,MAAAA,IAAI,CAACa,gBAAL,CAAsB,OAAtB,EAA+BD,OAA/B;AACA,WAAKnB,QAAL,CAAchE,IAAd,CAAmBqF,WAAnB,CAA+Bd,IAA/B,EAxGgC,CAyGhC;;AACAF,MAAAA,QAAQ,CAACjB,IAAT,CAAc;AAAEvC,QAAAA,IAAI,EAAEI,aAAa,CAACqE;AAAtB,OAAd,EA1GgC,CA2GhC;;AACA,aAAO,MAAM;AACT;AACAX,QAAAA,SAAS,GAAG,IAAZ,CAFS,CAGT;;AACAJ,QAAAA,IAAI,CAACgB,mBAAL,CAAyB,MAAzB,EAAiCP,MAAjC;AACAT,QAAAA,IAAI,CAACgB,mBAAL,CAAyB,OAAzB,EAAkCJ,OAAlC,EALS,CAMT;;AACAN,QAAAA,OAAO;AACV,OARD;AASH,KArHM,CAAP;AAsHH;;AArJoB;;AAuJzBf,kBAAkB,CAACb,IAAnB;AAAA,mBAA+Ga,kBAA/G,EAxO6FnL,EAwO7F,UAAmJkL,oBAAnJ,GAxO6FlL,EAwO7F,UAAoLJ,QAApL;AAAA;;AACAuL,kBAAkB,CAACZ,KAAnB,kBAzO6FvK,EAyO7F;AAAA,SAAmHmL,kBAAnH;AAAA,WAAmHA,kBAAnH;AAAA;;AACA;AAAA,qDA1O6FnL,EA0O7F,mBAA2FmL,kBAA3F,EAA2H,CAAC;AAChHjD,IAAAA,IAAI,EAAEjI;AAD0G,GAAD,CAA3H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEiI,MAAAA,IAAI,EAAEgD;AAAR,KAAD,EAAiC;AAAEhD,MAAAA,IAAI,EAAE3E,SAAR;AAAmBsJ,MAAAA,UAAU,EAAE,CAAC;AAC9F3E,QAAAA,IAAI,EAAE/H,MADwF;AAE9F2M,QAAAA,IAAI,EAAE,CAAClN,QAAD;AAFwF,OAAD;AAA/B,KAAjC,CAAP;AAGlB,GALxB;AAAA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmN,gBAAN,CAAuB;AACnBjM,EAAAA,WAAW,CAACmJ,KAAD,EAAQ;AACf,SAAKA,KAAL,GAAaA,KAAb;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIU,EAAAA,SAAS,CAACjB,GAAD,EAAMe,IAAN,EAAY;AACjB,QAAIf,GAAG,CAAClD,MAAJ,KAAe,OAAnB,EAA4B;AACxB,aAAO,KAAKyD,KAAL,CAAWJ,MAAX,CAAkBH,GAAlB,CAAP;AACH,KAHgB,CAIjB;;;AACA,WAAOe,IAAI,CAACZ,MAAL,CAAYH,GAAZ,CAAP;AACH;;AAjBkB;;AAmBvBqD,gBAAgB,CAACzC,IAAjB;AAAA,mBAA6GyC,gBAA7G,EA3Q6F/M,EA2Q7F,UAA+ImL,kBAA/I;AAAA;;AACA4B,gBAAgB,CAACxC,KAAjB,kBA5Q6FvK,EA4Q7F;AAAA,SAAiH+M,gBAAjH;AAAA,WAAiHA,gBAAjH;AAAA;;AACA;AAAA,qDA7Q6F/M,EA6Q7F,mBAA2F+M,gBAA3F,EAAyH,CAAC;AAC9G7E,IAAAA,IAAI,EAAEjI;AADwG,GAAD,CAAzH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEiI,MAAAA,IAAI,EAAEiD;AAAR,KAAD,CAAP;AAAwC,GAFlF;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM6B,WAAW,GAAG,cAApB;AACA;AACA;AACA;AACA;;AACA,SAASC,cAAT,CAAwBC,GAAxB,EAA6B;AACzB,MAAI,iBAAiBA,GAAjB,IAAwBA,GAAG,CAACC,WAAhC,EAA6C;AACzC,WAAOD,GAAG,CAACC,WAAX;AACH;;AACD,MAAI,mBAAmBC,IAAnB,CAAwBF,GAAG,CAACG,qBAAJ,EAAxB,CAAJ,EAA0D;AACtD,WAAOH,GAAG,CAACI,iBAAJ,CAAsB,eAAtB,CAAP;AACH;;AACD,SAAO,IAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,cAAN,CAAqB;AACjBzM,EAAAA,WAAW,CAAC0M,UAAD,EAAa;AACpB,SAAKA,UAAL,GAAkBA,UAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACI3D,EAAAA,MAAM,CAACH,GAAD,EAAM;AACR;AACA;AACA,QAAIA,GAAG,CAAClD,MAAJ,KAAe,OAAnB,EAA4B;AACxB,YAAM,IAAIf,KAAJ,CAAW,+EAAX,CAAN;AACH,KALO,CAMR;;;AACA,WAAO,IAAIlF,UAAJ,CAAgBmL,QAAD,IAAc;AAChC;AACA,YAAMwB,GAAG,GAAG,KAAKM,UAAL,CAAgBC,KAAhB,EAAZ;AACAP,MAAAA,GAAG,CAACQ,IAAJ,CAAShE,GAAG,CAAClD,MAAb,EAAqBkD,GAAG,CAAC/B,aAAzB;;AACA,UAAI,CAAC,CAAC+B,GAAG,CAACnC,eAAV,EAA2B;AACvB2F,QAAAA,GAAG,CAAC3F,eAAJ,GAAsB,IAAtB;AACH,OAN+B,CAOhC;;;AACAmC,MAAAA,GAAG,CAAC3I,OAAJ,CAAYM,OAAZ,CAAoB,CAACI,IAAD,EAAOa,MAAP,KAAkB4K,GAAG,CAACS,gBAAJ,CAAqBlM,IAArB,EAA2Ba,MAAM,CAAC0D,IAAP,CAAY,GAAZ,CAA3B,CAAtC,EARgC,CAShC;;AACA,UAAI,CAAC0D,GAAG,CAAC3I,OAAJ,CAAYiB,GAAZ,CAAgB,QAAhB,CAAL,EAAgC;AAC5BkL,QAAAA,GAAG,CAACS,gBAAJ,CAAqB,QAArB,EAA+B,mCAA/B;AACH,OAZ+B,CAahC;;;AACA,UAAI,CAACjE,GAAG,CAAC3I,OAAJ,CAAYiB,GAAZ,CAAgB,cAAhB,CAAL,EAAsC;AAClC,cAAM4L,YAAY,GAAGlE,GAAG,CAACzB,uBAAJ,EAArB,CADkC,CAElC;;AACA,YAAI2F,YAAY,KAAK,IAArB,EAA2B;AACvBV,UAAAA,GAAG,CAACS,gBAAJ,CAAqB,cAArB,EAAqCC,YAArC;AACH;AACJ,OApB+B,CAqBhC;;;AACA,UAAIlE,GAAG,CAAClC,YAAR,EAAsB;AAClB,cAAMA,YAAY,GAAGkC,GAAG,CAAClC,YAAJ,CAAiB5F,WAAjB,EAArB,CADkB,CAElB;AACA;AACA;AACA;AACA;;AACAsL,QAAAA,GAAG,CAAC1F,YAAJ,GAAqBA,YAAY,KAAK,MAAlB,GAA4BA,YAA5B,GAA2C,MAA/D;AACH,OA9B+B,CA+BhC;;;AACA,YAAMqG,OAAO,GAAGnE,GAAG,CAAC5B,aAAJ,EAAhB,CAhCgC,CAiChC;AACA;AACA;AACA;AACA;AACA;;AACA,UAAIgG,cAAc,GAAG,IAArB,CAvCgC,CAwChC;AACA;;AACA,YAAMC,cAAc,GAAG,MAAM;AACzB,YAAID,cAAc,KAAK,IAAvB,EAA6B;AACzB,iBAAOA,cAAP;AACH,SAHwB,CAIzB;;;AACA,cAAMpF,MAAM,GAAGwE,GAAG,CAACxE,MAAJ,KAAe,IAAf,GAAsB;AAAI;AAA1B,UAA4CwE,GAAG,CAACxE,MAA/D;AACA,cAAMC,UAAU,GAAGuE,GAAG,CAACvE,UAAJ,IAAkB,IAArC,CANyB,CAOzB;;AACA,cAAM5H,OAAO,GAAG,IAAIF,WAAJ,CAAgBqM,GAAG,CAACG,qBAAJ,EAAhB,CAAhB,CARyB,CASzB;AACA;;AACA,cAAMnG,GAAG,GAAG+F,cAAc,CAACC,GAAD,CAAd,IAAuBxD,GAAG,CAACxC,GAAvC,CAXyB,CAYzB;;AACA4G,QAAAA,cAAc,GAAG,IAAIjF,kBAAJ,CAAuB;AAAE9H,UAAAA,OAAF;AAAW2H,UAAAA,MAAX;AAAmBC,UAAAA,UAAnB;AAA+BzB,UAAAA;AAA/B,SAAvB,CAAjB;AACA,eAAO4G,cAAP;AACH,OAfD,CA1CgC,CA0DhC;AACA;AACA;;;AACA,YAAMzB,MAAM,GAAG,MAAM;AACjB;AACA,YAAI;AAAEtL,UAAAA,OAAF;AAAW2H,UAAAA,MAAX;AAAmBC,UAAAA,UAAnB;AAA+BzB,UAAAA;AAA/B,YAAuC6G,cAAc,EAAzD,CAFiB,CAGjB;;AACA,YAAI1G,IAAI,GAAG,IAAX;;AACA,YAAIqB,MAAM,KAAK;AAAI;AAAnB,UAAoC;AAChC;AACArB,UAAAA,IAAI,GAAI,OAAO6F,GAAG,CAACc,QAAX,KAAwB,WAAzB,GAAwCd,GAAG,CAACe,YAA5C,GAA2Df,GAAG,CAACc,QAAtE;AACH,SARgB,CASjB;;;AACA,YAAItF,MAAM,KAAK,CAAf,EAAkB;AACdA,UAAAA,MAAM,GAAG,CAAC,CAACrB,IAAF,GAAS;AAAI;AAAb,YAAwB,CAAjC;AACH,SAZgB,CAajB;AACA;AACA;AACA;;;AACA,YAAIuB,EAAE,GAAGF,MAAM,IAAI,GAAV,IAAiBA,MAAM,GAAG,GAAnC,CAjBiB,CAkBjB;AACA;;AACA,YAAIgB,GAAG,CAAClC,YAAJ,KAAqB,MAArB,IAA+B,OAAOH,IAAP,KAAgB,QAAnD,EAA6D;AACzD;AACA,gBAAM6G,YAAY,GAAG7G,IAArB;AACAA,UAAAA,IAAI,GAAGA,IAAI,CAAC/C,OAAL,CAAa0I,WAAb,EAA0B,EAA1B,CAAP;;AACA,cAAI;AACA;AACA3F,YAAAA,IAAI,GAAGA,IAAI,KAAK,EAAT,GAAcU,IAAI,CAACoG,KAAL,CAAW9G,IAAX,CAAd,GAAiC,IAAxC;AACH,WAHD,CAIA,OAAO8B,KAAP,EAAc;AACV;AACA;AACA;AACA9B,YAAAA,IAAI,GAAG6G,YAAP,CAJU,CAKV;AACA;;AACA,gBAAItF,EAAJ,EAAQ;AACJ;AACAA,cAAAA,EAAE,GAAG,KAAL,CAFI,CAGJ;;AACAvB,cAAAA,IAAI,GAAG;AAAE8B,gBAAAA,KAAF;AAASiF,gBAAAA,IAAI,EAAE/G;AAAf,eAAP;AACH;AACJ;AACJ;;AACD,YAAIuB,EAAJ,EAAQ;AACJ;AACA8C,UAAAA,QAAQ,CAACjB,IAAT,CAAc,IAAI1B,YAAJ,CAAiB;AAC3B1B,YAAAA,IAD2B;AAE3BtG,YAAAA,OAF2B;AAG3B2H,YAAAA,MAH2B;AAI3BC,YAAAA,UAJ2B;AAK3BzB,YAAAA,GAAG,EAAEA,GAAG,IAAI3D;AALe,WAAjB,CAAd,EAFI,CASJ;AACA;;AACAmI,UAAAA,QAAQ,CAACa,QAAT;AACH,SAZD,MAaK;AACD;AACAb,UAAAA,QAAQ,CAACvC,KAAT,CAAe,IAAIF,iBAAJ,CAAsB;AACjC;AACAE,YAAAA,KAAK,EAAE9B,IAF0B;AAGjCtG,YAAAA,OAHiC;AAIjC2H,YAAAA,MAJiC;AAKjCC,YAAAA,UALiC;AAMjCzB,YAAAA,GAAG,EAAEA,GAAG,IAAI3D;AANqB,WAAtB,CAAf;AAQH;AACJ,OAnED,CA7DgC,CAiIhC;AACA;AACA;;;AACA,YAAMiJ,OAAO,GAAIrD,KAAD,IAAW;AACvB,cAAM;AAAEjC,UAAAA;AAAF,YAAU6G,cAAc,EAA9B;AACA,cAAMpI,GAAG,GAAG,IAAIsD,iBAAJ,CAAsB;AAC9BE,UAAAA,KAD8B;AAE9BT,UAAAA,MAAM,EAAEwE,GAAG,CAACxE,MAAJ,IAAc,CAFQ;AAG9BC,UAAAA,UAAU,EAAEuE,GAAG,CAACvE,UAAJ,IAAkB,eAHA;AAI9BzB,UAAAA,GAAG,EAAEA,GAAG,IAAI3D;AAJkB,SAAtB,CAAZ;AAMAmI,QAAAA,QAAQ,CAACvC,KAAT,CAAexD,GAAf;AACH,OATD,CApIgC,CA8IhC;AACA;AACA;AACA;;;AACA,UAAI0I,WAAW,GAAG,KAAlB,CAlJgC,CAmJhC;AACA;;AACA,YAAMC,cAAc,GAAIvE,KAAD,IAAW;AAC9B;AACA,YAAI,CAACsE,WAAL,EAAkB;AACd3C,UAAAA,QAAQ,CAACjB,IAAT,CAAcsD,cAAc,EAA5B;AACAM,UAAAA,WAAW,GAAG,IAAd;AACH,SAL6B,CAM9B;AACA;;;AACA,YAAIE,aAAa,GAAG;AAChBrG,UAAAA,IAAI,EAAEI,aAAa,CAACkG,gBADJ;AAEhBC,UAAAA,MAAM,EAAE1E,KAAK,CAAC0E;AAFE,SAApB,CAR8B,CAY9B;;AACA,YAAI1E,KAAK,CAAC2E,gBAAV,EAA4B;AACxBH,UAAAA,aAAa,CAACI,KAAd,GAAsB5E,KAAK,CAAC4E,KAA5B;AACH,SAf6B,CAgB9B;AACA;AACA;;;AACA,YAAIjF,GAAG,CAAClC,YAAJ,KAAqB,MAArB,IAA+B,CAAC,CAAC0F,GAAG,CAACe,YAAzC,EAAuD;AACnDM,UAAAA,aAAa,CAACK,WAAd,GAA4B1B,GAAG,CAACe,YAAhC;AACH,SArB6B,CAsB9B;;;AACAvC,QAAAA,QAAQ,CAACjB,IAAT,CAAc8D,aAAd;AACH,OAxBD,CArJgC,CA8KhC;AACA;;;AACA,YAAMM,YAAY,GAAI9E,KAAD,IAAW;AAC5B;AACA;AACA,YAAI+E,QAAQ,GAAG;AACX5G,UAAAA,IAAI,EAAEI,aAAa,CAACyG,cADT;AAEXN,UAAAA,MAAM,EAAE1E,KAAK,CAAC0E;AAFH,SAAf,CAH4B,CAO5B;AACA;;AACA,YAAI1E,KAAK,CAAC2E,gBAAV,EAA4B;AACxBI,UAAAA,QAAQ,CAACH,KAAT,GAAiB5E,KAAK,CAAC4E,KAAvB;AACH,SAX2B,CAY5B;;;AACAjD,QAAAA,QAAQ,CAACjB,IAAT,CAAcqE,QAAd;AACH,OAdD,CAhLgC,CA+LhC;;;AACA5B,MAAAA,GAAG,CAACT,gBAAJ,CAAqB,MAArB,EAA6BJ,MAA7B;AACAa,MAAAA,GAAG,CAACT,gBAAJ,CAAqB,OAArB,EAA8BD,OAA9B;AACAU,MAAAA,GAAG,CAACT,gBAAJ,CAAqB,SAArB,EAAgCD,OAAhC;AACAU,MAAAA,GAAG,CAACT,gBAAJ,CAAqB,OAArB,EAA8BD,OAA9B,EAnMgC,CAoMhC;;AACA,UAAI9C,GAAG,CAACpC,cAAR,EAAwB;AACpB;AACA4F,QAAAA,GAAG,CAACT,gBAAJ,CAAqB,UAArB,EAAiC6B,cAAjC,EAFoB,CAGpB;;AACA,YAAIT,OAAO,KAAK,IAAZ,IAAoBX,GAAG,CAAC8B,MAA5B,EAAoC;AAChC9B,UAAAA,GAAG,CAAC8B,MAAJ,CAAWvC,gBAAX,CAA4B,UAA5B,EAAwCoC,YAAxC;AACH;AACJ,OA5M+B,CA6MhC;;;AACA3B,MAAAA,GAAG,CAAC+B,IAAJ,CAASpB,OAAT;AACAnC,MAAAA,QAAQ,CAACjB,IAAT,CAAc;AAAEvC,QAAAA,IAAI,EAAEI,aAAa,CAACqE;AAAtB,OAAd,EA/MgC,CAgNhC;AACA;;AACA,aAAO,MAAM;AACT;AACAO,QAAAA,GAAG,CAACN,mBAAJ,CAAwB,OAAxB,EAAiCJ,OAAjC;AACAU,QAAAA,GAAG,CAACN,mBAAJ,CAAwB,OAAxB,EAAiCJ,OAAjC;AACAU,QAAAA,GAAG,CAACN,mBAAJ,CAAwB,MAAxB,EAAgCP,MAAhC;AACAa,QAAAA,GAAG,CAACN,mBAAJ,CAAwB,SAAxB,EAAmCJ,OAAnC;;AACA,YAAI9C,GAAG,CAACpC,cAAR,EAAwB;AACpB4F,UAAAA,GAAG,CAACN,mBAAJ,CAAwB,UAAxB,EAAoC0B,cAApC;;AACA,cAAIT,OAAO,KAAK,IAAZ,IAAoBX,GAAG,CAAC8B,MAA5B,EAAoC;AAChC9B,YAAAA,GAAG,CAAC8B,MAAJ,CAAWpC,mBAAX,CAA+B,UAA/B,EAA2CiC,YAA3C;AACH;AACJ,SAXQ,CAYT;;;AACA,YAAI3B,GAAG,CAACgC,UAAJ,KAAmBhC,GAAG,CAACiC,IAA3B,EAAiC;AAC7BjC,UAAAA,GAAG,CAACkC,KAAJ;AACH;AACJ,OAhBD;AAiBH,KAnOM,CAAP;AAoOH;;AApPgB;;AAsPrB7B,cAAc,CAACjD,IAAf;AAAA,mBAA2GiD,cAA3G,EAniB6FvN,EAmiB7F,UAA2IL,EAAE,CAACG,UAA9I;AAAA;;AACAyN,cAAc,CAAChD,KAAf,kBApiB6FvK,EAoiB7F;AAAA,SAA+GuN,cAA/G;AAAA,WAA+GA,cAA/G;AAAA;;AACA;AAAA,qDAriB6FvN,EAqiB7F,mBAA2FuN,cAA3F,EAAuH,CAAC;AAC5GrF,IAAAA,IAAI,EAAEjI;AADsG,GAAD,CAAvH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEiI,MAAAA,IAAI,EAAEvI,EAAE,CAACG;AAAX,KAAD,CAAP;AAAmC,GAF7E;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMuP,gBAAgB,GAAG,IAAInP,cAAJ,CAAmB,kBAAnB,CAAzB;AACA,MAAMoP,gBAAgB,GAAG,IAAIpP,cAAJ,CAAmB,kBAAnB,CAAzB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMqP,sBAAN,CAA6B;AAE7B;AACA;AACA;;;AACA,MAAMC,uBAAN,CAA8B;AAC1B1O,EAAAA,WAAW,CAAC2O,GAAD,EAAMC,QAAN,EAAgBC,UAAhB,EAA4B;AACnC,SAAKF,GAAL,GAAWA,GAAX;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACA,SAAKC,gBAAL,GAAwB,EAAxB;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA;AACR;AACA;;AACQ,SAAKC,UAAL,GAAkB,CAAlB;AACH;;AACDC,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKL,QAAL,KAAkB,QAAtB,EAAgC;AAC5B,aAAO,IAAP;AACH;;AACD,UAAMM,YAAY,GAAG,KAAKP,GAAL,CAASQ,MAAT,IAAmB,EAAxC;;AACA,QAAID,YAAY,KAAK,KAAKJ,gBAA1B,EAA4C;AACxC,WAAKE,UAAL;AACA,WAAKD,SAAL,GAAiBhQ,iBAAiB,CAACmQ,YAAD,EAAe,KAAKL,UAApB,CAAlC;AACA,WAAKC,gBAAL,GAAwBI,YAAxB;AACH;;AACD,WAAO,KAAKH,SAAZ;AACH;;AAvByB;;AAyB9BL,uBAAuB,CAAClF,IAAxB;AAAA,mBAAoHkF,uBAApH,EArlB6FxP,EAqlB7F,UAA6JJ,QAA7J,GArlB6FI,EAqlB7F,UAAkLI,WAAlL,GArlB6FJ,EAqlB7F,UAA0MqP,gBAA1M;AAAA;;AACAG,uBAAuB,CAACjF,KAAxB,kBAtlB6FvK,EAslB7F;AAAA,SAAwHwP,uBAAxH;AAAA,WAAwHA,uBAAxH;AAAA;;AACA;AAAA,qDAvlB6FxP,EAulB7F,mBAA2FwP,uBAA3F,EAAgI,CAAC;AACrHtH,IAAAA,IAAI,EAAEjI;AAD+G,GAAD,CAAhI,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEiI,MAAAA,IAAI,EAAE3E,SAAR;AAAmBsJ,MAAAA,UAAU,EAAE,CAAC;AAC9D3E,QAAAA,IAAI,EAAE/H,MADwD;AAE9D2M,QAAAA,IAAI,EAAE,CAAClN,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEsI,MAAAA,IAAI,EAAE3E,SAAR;AAAmBsJ,MAAAA,UAAU,EAAE,CAAC;AAClC3E,QAAAA,IAAI,EAAE/H,MAD4B;AAElC2M,QAAAA,IAAI,EAAE,CAAC1M,WAAD;AAF4B,OAAD;AAA/B,KAH2B,EAM3B;AAAE8H,MAAAA,IAAI,EAAE3E,SAAR;AAAmBsJ,MAAAA,UAAU,EAAE,CAAC;AAClC3E,QAAAA,IAAI,EAAE/H,MAD4B;AAElC2M,QAAAA,IAAI,EAAE,CAACuC,gBAAD;AAF4B,OAAD;AAA/B,KAN2B,CAAP;AASlB,GAXxB;AAAA;AAYA;AACA;AACA;;;AACA,MAAMa,mBAAN,CAA0B;AACtBpP,EAAAA,WAAW,CAACqP,YAAD,EAAeC,UAAf,EAA2B;AAClC,SAAKD,YAAL,GAAoBA,YAApB;AACA,SAAKC,UAAL,GAAkBA,UAAlB;AACH;;AACDzF,EAAAA,SAAS,CAACjB,GAAD,EAAMe,IAAN,EAAY;AACjB,UAAM4F,KAAK,GAAG3G,GAAG,CAACxC,GAAJ,CAAQtF,WAAR,EAAd,CADiB,CAEjB;AACA;AACA;AACA;;AACA,QAAI8H,GAAG,CAAClD,MAAJ,KAAe,KAAf,IAAwBkD,GAAG,CAAClD,MAAJ,KAAe,MAAvC,IAAiD6J,KAAK,CAACC,UAAN,CAAiB,SAAjB,CAAjD,IACAD,KAAK,CAACC,UAAN,CAAiB,UAAjB,CADJ,EACkC;AAC9B,aAAO7F,IAAI,CAACZ,MAAL,CAAYH,GAAZ,CAAP;AACH;;AACD,UAAMpD,KAAK,GAAG,KAAK6J,YAAL,CAAkBJ,QAAlB,EAAd,CAViB,CAWjB;;AACA,QAAIzJ,KAAK,KAAK,IAAV,IAAkB,CAACoD,GAAG,CAAC3I,OAAJ,CAAYiB,GAAZ,CAAgB,KAAKoO,UAArB,CAAvB,EAAyD;AACrD1G,MAAAA,GAAG,GAAGA,GAAG,CAAC7G,KAAJ,CAAU;AAAE9B,QAAAA,OAAO,EAAE2I,GAAG,CAAC3I,OAAJ,CAAYoB,GAAZ,CAAgB,KAAKiO,UAArB,EAAiC9J,KAAjC;AAAX,OAAV,CAAN;AACH;;AACD,WAAOmE,IAAI,CAACZ,MAAL,CAAYH,GAAZ,CAAP;AACH;;AArBqB;;AAuB1BwG,mBAAmB,CAAC5F,IAApB;AAAA,mBAAgH4F,mBAAhH,EA7nB6FlQ,EA6nB7F,UAAqJuP,sBAArJ,GA7nB6FvP,EA6nB7F,UAAwLsP,gBAAxL;AAAA;;AACAY,mBAAmB,CAAC3F,KAApB,kBA9nB6FvK,EA8nB7F;AAAA,SAAoHkQ,mBAApH;AAAA,WAAoHA,mBAApH;AAAA;;AACA;AAAA,qDA/nB6FlQ,EA+nB7F,mBAA2FkQ,mBAA3F,EAA4H,CAAC;AACjHhI,IAAAA,IAAI,EAAEjI;AAD2G,GAAD,CAA5H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEiI,MAAAA,IAAI,EAAEqH;AAAR,KAAD,EAAmC;AAAErH,MAAAA,IAAI,EAAE3E,SAAR;AAAmBsJ,MAAAA,UAAU,EAAE,CAAC;AAChG3E,QAAAA,IAAI,EAAE/H,MAD0F;AAEhG2M,QAAAA,IAAI,EAAE,CAACwC,gBAAD;AAF0F,OAAD;AAA/B,KAAnC,CAAP;AAGlB,GALxB;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMiB,uBAAN,CAA8B;AAC1BzP,EAAAA,WAAW,CAAC0P,OAAD,EAAUC,QAAV,EAAoB;AAC3B,SAAKD,OAAL,GAAeA,OAAf;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,KAAL,GAAa,IAAb;AACH;;AACD7G,EAAAA,MAAM,CAACH,GAAD,EAAM;AACR,QAAI,KAAKgH,KAAL,KAAe,IAAnB,EAAyB;AACrB,YAAMC,YAAY,GAAG,KAAKF,QAAL,CAAcxO,GAAd,CAAkB2I,iBAAlB,EAAqC,EAArC,CAArB;AACA,WAAK8F,KAAL,GAAaC,YAAY,CAACC,WAAb,CAAyB,CAACnG,IAAD,EAAOC,WAAP,KAAuB,IAAIF,sBAAJ,CAA2BC,IAA3B,EAAiCC,WAAjC,CAAhD,EAA+F,KAAK8F,OAApG,CAAb;AACH;;AACD,WAAO,KAAKE,KAAL,CAAW7G,MAAX,CAAkBH,GAAlB,CAAP;AACH;;AAZyB;;AAc9B6G,uBAAuB,CAACjG,IAAxB;AAAA,mBAAoHiG,uBAApH,EApqB6FvQ,EAoqB7F,UAA6JY,WAA7J,GApqB6FZ,EAoqB7F,UAAqLA,EAAE,CAAC6Q,QAAxL;AAAA;;AACAN,uBAAuB,CAAChG,KAAxB,kBArqB6FvK,EAqqB7F;AAAA,SAAwHuQ,uBAAxH;AAAA,WAAwHA,uBAAxH;AAAA;;AACA;AAAA,qDAtqB6FvQ,EAsqB7F,mBAA2FuQ,uBAA3F,EAAgI,CAAC;AACrHrI,IAAAA,IAAI,EAAEjI;AAD+G,GAAD,CAAhI,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEiI,MAAAA,IAAI,EAAEtH;AAAR,KAAD,EAAwB;AAAEsH,MAAAA,IAAI,EAAElI,EAAE,CAAC6Q;AAAX,KAAxB,CAAP;AAAwD,GAFlG;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,mBAAT,CAA6BN,OAA7B,EAAsCG,YAAY,GAAG,EAArD,EAAyD;AACrD,MAAI,CAACA,YAAL,EAAmB;AACf,WAAOH,OAAP;AACH;;AACD,SAAOG,YAAY,CAACC,WAAb,CAAyB,CAACnG,IAAD,EAAOC,WAAP,KAAuB,IAAIF,sBAAJ,CAA2BC,IAA3B,EAAiCC,WAAjC,CAAhD,EAA+F8F,OAA/F,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASO,oBAAT,GAAgC;AAC5B,MAAI,OAAOC,MAAP,KAAkB,QAAtB,EAAgC;AAC5B,WAAOA,MAAP;AACH;;AACD,SAAO,EAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,oBAAN,CAA2B;AACvB;AACJ;AACA;AACkB,SAAPC,OAAO,GAAG;AACb,WAAO;AACHC,MAAAA,QAAQ,EAAEF,oBADP;AAEHG,MAAAA,SAAS,EAAE,CACP;AAAEC,QAAAA,OAAO,EAAEnB,mBAAX;AAAgCoB,QAAAA,QAAQ,EAAEzG;AAA1C,OADO;AAFR,KAAP;AAMH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACsB,SAAX0G,WAAW,CAACpM,OAAO,GAAG,EAAX,EAAe;AAC7B,WAAO;AACHgM,MAAAA,QAAQ,EAAEF,oBADP;AAEHG,MAAAA,SAAS,EAAE,CACPjM,OAAO,CAACwK,UAAR,GAAqB;AAAE0B,QAAAA,OAAO,EAAEhC,gBAAX;AAA6BmC,QAAAA,QAAQ,EAAErM,OAAO,CAACwK;AAA/C,OAArB,GAAmF,EAD5E,EAEPxK,OAAO,CAACiL,UAAR,GAAqB;AAAEiB,QAAAA,OAAO,EAAE/B,gBAAX;AAA6BkC,QAAAA,QAAQ,EAAErM,OAAO,CAACiL;AAA/C,OAArB,GAAmF,EAF5E;AAFR,KAAP;AAOH;;AA5BsB;;AA8B3Ba,oBAAoB,CAAC3G,IAArB;AAAA,mBAAiH2G,oBAAjH;AAAA;;AACAA,oBAAoB,CAACQ,IAArB,kBAhvB6FzR,EAgvB7F;AAAA,QAAkHiR;AAAlH;AACAA,oBAAoB,CAACS,IAArB,kBAjvB6F1R,EAivB7F;AAAA,aAAmJ,CAC3IkQ,mBAD2I,EAE3I;AAAEmB,IAAAA,OAAO,EAAEzG,iBAAX;AAA8B+G,IAAAA,WAAW,EAAEzB,mBAA3C;AAAgE0B,IAAAA,KAAK,EAAE;AAAvE,GAF2I,EAG3I;AAAEP,IAAAA,OAAO,EAAE9B,sBAAX;AAAmC+B,IAAAA,QAAQ,EAAE9B;AAA7C,GAH2I,EAI3I;AAAE6B,IAAAA,OAAO,EAAEhC,gBAAX;AAA6BmC,IAAAA,QAAQ,EAAE;AAAvC,GAJ2I,EAK3I;AAAEH,IAAAA,OAAO,EAAE/B,gBAAX;AAA6BkC,IAAAA,QAAQ,EAAE;AAAvC,GAL2I;AAAnJ;;AAOA;AAAA,qDAxvB6FxR,EAwvB7F,mBAA2FiR,oBAA3F,EAA6H,CAAC;AAClH/I,IAAAA,IAAI,EAAE7H,QAD4G;AAElHyM,IAAAA,IAAI,EAAE,CAAC;AACCsE,MAAAA,SAAS,EAAE,CACPlB,mBADO,EAEP;AAAEmB,QAAAA,OAAO,EAAEzG,iBAAX;AAA8B+G,QAAAA,WAAW,EAAEzB,mBAA3C;AAAgE0B,QAAAA,KAAK,EAAE;AAAvE,OAFO,EAGP;AAAEP,QAAAA,OAAO,EAAE9B,sBAAX;AAAmC+B,QAAAA,QAAQ,EAAE9B;AAA7C,OAHO,EAIP;AAAE6B,QAAAA,OAAO,EAAEhC,gBAAX;AAA6BmC,QAAAA,QAAQ,EAAE;AAAvC,OAJO,EAKP;AAAEH,QAAAA,OAAO,EAAE/B,gBAAX;AAA6BkC,QAAAA,QAAQ,EAAE;AAAvC,OALO;AADZ,KAAD;AAF4G,GAAD,CAA7H;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMK,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAACvH,IAAjB;AAAA,mBAA6GuH,gBAA7G;AAAA;;AACAA,gBAAgB,CAACJ,IAAjB,kBAhxB6FzR,EAgxB7F;AAAA,QAA8G6R;AAA9G;AACAA,gBAAgB,CAACH,IAAjB,kBAjxB6F1R,EAixB7F;AAAA,aAA2I,CACnIsJ,UADmI,EAEnI;AAAE+H,IAAAA,OAAO,EAAE1Q,WAAX;AAAwB2Q,IAAAA,QAAQ,EAAEf;AAAlC,GAFmI,EAGnIhD,cAHmI,EAInI;AAAE8D,IAAAA,OAAO,EAAEzQ,WAAX;AAAwB+Q,IAAAA,WAAW,EAAEpE;AAArC,GAJmI,CAA3I;AAAA,YAKiB,CACL0D,oBAAoB,CAACM,WAArB,CAAiC;AAC7B5B,IAAAA,UAAU,EAAE,YADiB;AAE7BS,IAAAA,UAAU,EAAE;AAFiB,GAAjC,CADK,CALjB;AAAA;;AAWA;AAAA,qDA5xB6FpQ,EA4xB7F,mBAA2F6R,gBAA3F,EAAyH,CAAC;AAC9G3J,IAAAA,IAAI,EAAE7H,QADwG;AAE9GyM,IAAAA,IAAI,EAAE,CAAC;AACC;AACpB;AACA;AACoBgF,MAAAA,OAAO,EAAE,CACLb,oBAAoB,CAACM,WAArB,CAAiC;AAC7B5B,QAAAA,UAAU,EAAE,YADiB;AAE7BS,QAAAA,UAAU,EAAE;AAFiB,OAAjC,CADK,CAJV;;AAUC;AACpB;AACA;AACA;AACoBgB,MAAAA,SAAS,EAAE,CACP9H,UADO,EAEP;AAAE+H,QAAAA,OAAO,EAAE1Q,WAAX;AAAwB2Q,QAAAA,QAAQ,EAAEf;AAAlC,OAFO,EAGPhD,cAHO,EAIP;AAAE8D,QAAAA,OAAO,EAAEzQ,WAAX;AAAwB+Q,QAAAA,WAAW,EAAEpE;AAArC,OAJO;AAdZ,KAAD;AAFwG,GAAD,CAAzH;AAAA;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMwE,qBAAN,CAA4B;;AAE5BA,qBAAqB,CAACzH,IAAtB;AAAA,mBAAkHyH,qBAAlH;AAAA;;AACAA,qBAAqB,CAACN,IAAtB,kBAl0B6FzR,EAk0B7F;AAAA,QAAmH+R;AAAnH;AACAA,qBAAqB,CAACL,IAAtB,kBAn0B6F1R,EAm0B7F;AAAA,aAAqJ,CAC7ImL,kBAD6I,EAE7I;AAAEkG,IAAAA,OAAO,EAAEnG,oBAAX;AAAiC8G,IAAAA,UAAU,EAAEjB;AAA7C,GAF6I,EAG7I;AAAEM,IAAAA,OAAO,EAAEzG,iBAAX;AAA8B0G,IAAAA,QAAQ,EAAEvE,gBAAxC;AAA0D6E,IAAAA,KAAK,EAAE;AAAjE,GAH6I;AAArJ;;AAKA;AAAA,qDAx0B6F5R,EAw0B7F,mBAA2F+R,qBAA3F,EAA8H,CAAC;AACnH7J,IAAAA,IAAI,EAAE7H,QAD6G;AAEnHyM,IAAAA,IAAI,EAAE,CAAC;AACCsE,MAAAA,SAAS,EAAE,CACPjG,kBADO,EAEP;AAAEkG,QAAAA,OAAO,EAAEnG,oBAAX;AAAiC8G,QAAAA,UAAU,EAAEjB;AAA7C,OAFO,EAGP;AAAEM,QAAAA,OAAO,EAAEzG,iBAAX;AAA8B0G,QAAAA,QAAQ,EAAEvE,gBAAxC;AAA0D6E,QAAAA,KAAK,EAAE;AAAjE,OAHO;AADZ,KAAD;AAF6G,GAAD,CAA9H;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM9R,UAAU,GAAGC,YAAnB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS6K,iBAAT,EAA4BhK,WAA5B,EAAyC0I,UAAzC,EAAqDyI,qBAArD,EAA4EF,gBAA5E,EAA8FZ,oBAA9F,EAAoH5K,WAApH,EAAiIF,gBAAjI,EAAmJ8C,iBAAnJ,EAAsKX,aAAtK,EAAqL3H,WAArL,EAAkMkI,kBAAlM,EAAsNhI,WAAtN,EAAmOqE,UAAnO,EAA+O+B,WAA/O,EAA4P8B,YAA5P,EAA0QR,gBAA1Q,EAA4R5E,oBAA5R,EAAkT4J,cAAlT,EAAkUgC,sBAAlU,EAA0VpE,kBAA1V,EAA8W4B,gBAA9W,EAAgYjN,UAAhY,EAA4YyQ,uBAAuB,IAAI0B,wBAAva", "sourcesContent": ["/**\n * @license Angular v13.0.3\n * (c) 2010-2021 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i1 from '@angular/common';\nimport { DOCUMENT, ɵparseCookieValue, XhrFactory as XhrFactory$1 } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, Inject, PLATFORM_ID, NgModule } from '@angular/core';\nimport { of, Observable } from 'rxjs';\nimport { concatMap, filter, map } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nclass HttpHandler {\n}\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nclass HttpBackend {\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nclass HttpHeaders {\n    /**  Constructs a new HTTP header object with the given values.*/\n    constructor(headers) {\n        /**\n         * Internal map of lowercased header names to the normalized\n         * form of the name (the form seen first).\n         */\n        this.normalizedNames = new Map();\n        /**\n         * Queued updates to be materialized the next initialization.\n         */\n        this.lazyUpdate = null;\n        if (!headers) {\n            this.headers = new Map();\n        }\n        else if (typeof headers === 'string') {\n            this.lazyInit = () => {\n                this.headers = new Map();\n                headers.split('\\n').forEach(line => {\n                    const index = line.indexOf(':');\n                    if (index > 0) {\n                        const name = line.slice(0, index);\n                        const key = name.toLowerCase();\n                        const value = line.slice(index + 1).trim();\n                        this.maybeSetNormalizedName(name, key);\n                        if (this.headers.has(key)) {\n                            this.headers.get(key).push(value);\n                        }\n                        else {\n                            this.headers.set(key, [value]);\n                        }\n                    }\n                });\n            };\n        }\n        else {\n            this.lazyInit = () => {\n                this.headers = new Map();\n                Object.keys(headers).forEach(name => {\n                    let values = headers[name];\n                    const key = name.toLowerCase();\n                    if (typeof values === 'string') {\n                        values = [values];\n                    }\n                    if (values.length > 0) {\n                        this.headers.set(key, values);\n                        this.maybeSetNormalizedName(name, key);\n                    }\n                });\n            };\n        }\n    }\n    /**\n     * Checks for existence of a given header.\n     *\n     * @param name The header name to check for existence.\n     *\n     * @returns True if the header exists, false otherwise.\n     */\n    has(name) {\n        this.init();\n        return this.headers.has(name.toLowerCase());\n    }\n    /**\n     * Retrieves the first value of a given header.\n     *\n     * @param name The header name.\n     *\n     * @returns The value string if the header exists, null otherwise\n     */\n    get(name) {\n        this.init();\n        const values = this.headers.get(name.toLowerCase());\n        return values && values.length > 0 ? values[0] : null;\n    }\n    /**\n     * Retrieves the names of the headers.\n     *\n     * @returns A list of header names.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.normalizedNames.values());\n    }\n    /**\n     * Retrieves a list of values for a given header.\n     *\n     * @param name The header name from which to retrieve values.\n     *\n     * @returns A string of values if the header exists, null otherwise.\n     */\n    getAll(name) {\n        this.init();\n        return this.headers.get(name.toLowerCase()) || null;\n    }\n    /**\n     * Appends a new value to the existing set of values for a header\n     * and returns them in a clone of the original instance.\n     *\n     * @param name The header name for which to append the values.\n     * @param value The value to append.\n     *\n     * @returns A clone of the HTTP headers object with the value appended to the given header.\n     */\n    append(name, value) {\n        return this.clone({ name, value, op: 'a' });\n    }\n    /**\n     * Sets or modifies a value for a given header in a clone of the original instance.\n     * If the header already exists, its value is replaced with the given value\n     * in the returned object.\n     *\n     * @param name The header name.\n     * @param value The value or values to set or overide for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the newly set header value.\n     */\n    set(name, value) {\n        return this.clone({ name, value, op: 's' });\n    }\n    /**\n     * Deletes values for a given header in a clone of the original instance.\n     *\n     * @param name The header name.\n     * @param value The value or values to delete for the given header.\n     *\n     * @returns A clone of the HTTP headers object with the given value deleted.\n     */\n    delete(name, value) {\n        return this.clone({ name, value, op: 'd' });\n    }\n    maybeSetNormalizedName(name, lcName) {\n        if (!this.normalizedNames.has(lcName)) {\n            this.normalizedNames.set(lcName, name);\n        }\n    }\n    init() {\n        if (!!this.lazyInit) {\n            if (this.lazyInit instanceof HttpHeaders) {\n                this.copyFrom(this.lazyInit);\n            }\n            else {\n                this.lazyInit();\n            }\n            this.lazyInit = null;\n            if (!!this.lazyUpdate) {\n                this.lazyUpdate.forEach(update => this.applyUpdate(update));\n                this.lazyUpdate = null;\n            }\n        }\n    }\n    copyFrom(other) {\n        other.init();\n        Array.from(other.headers.keys()).forEach(key => {\n            this.headers.set(key, other.headers.get(key));\n            this.normalizedNames.set(key, other.normalizedNames.get(key));\n        });\n    }\n    clone(update) {\n        const clone = new HttpHeaders();\n        clone.lazyInit =\n            (!!this.lazyInit && this.lazyInit instanceof HttpHeaders) ? this.lazyInit : this;\n        clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n        return clone;\n    }\n    applyUpdate(update) {\n        const key = update.name.toLowerCase();\n        switch (update.op) {\n            case 'a':\n            case 's':\n                let value = update.value;\n                if (typeof value === 'string') {\n                    value = [value];\n                }\n                if (value.length === 0) {\n                    return;\n                }\n                this.maybeSetNormalizedName(update.name, key);\n                const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n                base.push(...value);\n                this.headers.set(key, base);\n                break;\n            case 'd':\n                const toDelete = update.value;\n                if (!toDelete) {\n                    this.headers.delete(key);\n                    this.normalizedNames.delete(key);\n                }\n                else {\n                    let existing = this.headers.get(key);\n                    if (!existing) {\n                        return;\n                    }\n                    existing = existing.filter(value => toDelete.indexOf(value) === -1);\n                    if (existing.length === 0) {\n                        this.headers.delete(key);\n                        this.normalizedNames.delete(key);\n                    }\n                    else {\n                        this.headers.set(key, existing);\n                    }\n                }\n                break;\n        }\n    }\n    /**\n     * @internal\n     */\n    forEach(fn) {\n        this.init();\n        Array.from(this.normalizedNames.keys())\n            .forEach(key => fn(this.normalizedNames.get(key), this.headers.get(key)));\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nclass HttpUrlEncodingCodec {\n    /**\n     * Encodes a key name for a URL parameter or query-string.\n     * @param key The key name.\n     * @returns The encoded key name.\n     */\n    encodeKey(key) {\n        return standardEncoding(key);\n    }\n    /**\n     * Encodes the value of a URL parameter or query-string.\n     * @param value The value.\n     * @returns The encoded value.\n     */\n    encodeValue(value) {\n        return standardEncoding(value);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string key.\n     * @param key The encoded key name.\n     * @returns The decoded key name.\n     */\n    decodeKey(key) {\n        return decodeURIComponent(key);\n    }\n    /**\n     * Decodes an encoded URL parameter or query-string value.\n     * @param value The encoded value.\n     * @returns The decoded value.\n     */\n    decodeValue(value) {\n        return decodeURIComponent(value);\n    }\n}\nfunction paramParser(rawParams, codec) {\n    const map = new Map();\n    if (rawParams.length > 0) {\n        // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n        // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n        // may start with the `?` char, so we strip it if it's present.\n        const params = rawParams.replace(/^\\?/, '').split('&');\n        params.forEach((param) => {\n            const eqIdx = param.indexOf('=');\n            const [key, val] = eqIdx == -1 ?\n                [codec.decodeKey(param), ''] :\n                [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n            const list = map.get(key) || [];\n            list.push(val);\n            map.set(key, list);\n        });\n    }\n    return map;\n}\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS = {\n    '40': '@',\n    '3A': ':',\n    '24': '$',\n    '2C': ',',\n    '3B': ';',\n    '2B': '+',\n    '3D': '=',\n    '3F': '?',\n    '2F': '/',\n};\nfunction standardEncoding(v) {\n    return encodeURIComponent(v).replace(STANDARD_ENCODING_REGEX, (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s);\n}\nfunction valueToString(value) {\n    return `${value}`;\n}\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nclass HttpParams {\n    constructor(options = {}) {\n        this.updates = null;\n        this.cloneFrom = null;\n        this.encoder = options.encoder || new HttpUrlEncodingCodec();\n        if (!!options.fromString) {\n            if (!!options.fromObject) {\n                throw new Error(`Cannot specify both fromString and fromObject.`);\n            }\n            this.map = paramParser(options.fromString, this.encoder);\n        }\n        else if (!!options.fromObject) {\n            this.map = new Map();\n            Object.keys(options.fromObject).forEach(key => {\n                const value = options.fromObject[key];\n                this.map.set(key, Array.isArray(value) ? value : [value]);\n            });\n        }\n        else {\n            this.map = null;\n        }\n    }\n    /**\n     * Reports whether the body includes one or more values for a given parameter.\n     * @param param The parameter name.\n     * @returns True if the parameter has one or more values,\n     * false if it has no value or is not present.\n     */\n    has(param) {\n        this.init();\n        return this.map.has(param);\n    }\n    /**\n     * Retrieves the first value for a parameter.\n     * @param param The parameter name.\n     * @returns The first value of the given parameter,\n     * or `null` if the parameter is not present.\n     */\n    get(param) {\n        this.init();\n        const res = this.map.get(param);\n        return !!res ? res[0] : null;\n    }\n    /**\n     * Retrieves all values for a  parameter.\n     * @param param The parameter name.\n     * @returns All values in a string array,\n     * or `null` if the parameter not present.\n     */\n    getAll(param) {\n        this.init();\n        return this.map.get(param) || null;\n    }\n    /**\n     * Retrieves all the parameters for this body.\n     * @returns The parameter names in a string array.\n     */\n    keys() {\n        this.init();\n        return Array.from(this.map.keys());\n    }\n    /**\n     * Appends a new value to existing values for a parameter.\n     * @param param The parameter name.\n     * @param value The new value to add.\n     * @return A new body with the appended value.\n     */\n    append(param, value) {\n        return this.clone({ param, value, op: 'a' });\n    }\n    /**\n     * Constructs a new body with appended values for the given parameter name.\n     * @param params parameters and values\n     * @return A new body with the new value.\n     */\n    appendAll(params) {\n        const updates = [];\n        Object.keys(params).forEach(param => {\n            const value = params[param];\n            if (Array.isArray(value)) {\n                value.forEach(_value => {\n                    updates.push({ param, value: _value, op: 'a' });\n                });\n            }\n            else {\n                updates.push({ param, value: value, op: 'a' });\n            }\n        });\n        return this.clone(updates);\n    }\n    /**\n     * Replaces the value for a parameter.\n     * @param param The parameter name.\n     * @param value The new value.\n     * @return A new body with the new value.\n     */\n    set(param, value) {\n        return this.clone({ param, value, op: 's' });\n    }\n    /**\n     * Removes a given value or all values from a parameter.\n     * @param param The parameter name.\n     * @param value The value to remove, if provided.\n     * @return A new body with the given value removed, or with all values\n     * removed if no value is specified.\n     */\n    delete(param, value) {\n        return this.clone({ param, value, op: 'd' });\n    }\n    /**\n     * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n     * separated by `&`s.\n     */\n    toString() {\n        this.init();\n        return this.keys()\n            .map(key => {\n            const eKey = this.encoder.encodeKey(key);\n            // `a: ['1']` produces `'a=1'`\n            // `b: []` produces `''`\n            // `c: ['1', '2']` produces `'c=1&c=2'`\n            return this.map.get(key).map(value => eKey + '=' + this.encoder.encodeValue(value))\n                .join('&');\n        })\n            // filter out empty values because `b: []` produces `''`\n            // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n            .filter(param => param !== '')\n            .join('&');\n    }\n    clone(update) {\n        const clone = new HttpParams({ encoder: this.encoder });\n        clone.cloneFrom = this.cloneFrom || this;\n        clone.updates = (this.updates || []).concat(update);\n        return clone;\n    }\n    init() {\n        if (this.map === null) {\n            this.map = new Map();\n        }\n        if (this.cloneFrom !== null) {\n            this.cloneFrom.init();\n            this.cloneFrom.keys().forEach(key => this.map.set(key, this.cloneFrom.map.get(key)));\n            this.updates.forEach(update => {\n                switch (update.op) {\n                    case 'a':\n                    case 's':\n                        const base = (update.op === 'a' ? this.map.get(update.param) : undefined) || [];\n                        base.push(valueToString(update.value));\n                        this.map.set(update.param, base);\n                        break;\n                    case 'd':\n                        if (update.value !== undefined) {\n                            let base = this.map.get(update.param) || [];\n                            const idx = base.indexOf(valueToString(update.value));\n                            if (idx !== -1) {\n                                base.splice(idx, 1);\n                            }\n                            if (base.length > 0) {\n                                this.map.set(update.param, base);\n                            }\n                            else {\n                                this.map.delete(update.param);\n                            }\n                        }\n                        else {\n                            this.map.delete(update.param);\n                            break;\n                        }\n                }\n            });\n            this.cloneFrom = this.updates = null;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nclass HttpContextToken {\n    constructor(defaultValue) {\n        this.defaultValue = defaultValue;\n    }\n}\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nclass HttpContext {\n    constructor() {\n        this.map = new Map();\n    }\n    /**\n     * Store a value in the context. If a value is already present it will be overwritten.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     * @param value The value to store.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    set(token, value) {\n        this.map.set(token, value);\n        return this;\n    }\n    /**\n     * Retrieve the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns The stored value or default if one is defined.\n     */\n    get(token) {\n        if (!this.map.has(token)) {\n            this.map.set(token, token.defaultValue());\n        }\n        return this.map.get(token);\n    }\n    /**\n     * Delete the value associated with the given token.\n     *\n     * @param token The reference to an instance of `HttpContextToken`.\n     *\n     * @returns A reference to itself for easy chaining.\n     */\n    delete(token) {\n        this.map.delete(token);\n        return this;\n    }\n    /**\n     * @returns a list of tokens currently stored in the context.\n     */\n    keys() {\n        return this.map.keys();\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method) {\n    switch (method) {\n        case 'DELETE':\n        case 'GET':\n        case 'HEAD':\n        case 'OPTIONS':\n        case 'JSONP':\n            return false;\n        default:\n            return true;\n    }\n}\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value) {\n    return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value) {\n    return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value) {\n    return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value) {\n    return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nclass HttpRequest {\n    constructor(method, url, third, fourth) {\n        this.url = url;\n        /**\n         * The request body, or `null` if one isn't set.\n         *\n         * Bodies are not enforced to be immutable, as they can include a reference to any\n         * user-defined data type. However, interceptors should take care to preserve\n         * idempotence by treating them as such.\n         */\n        this.body = null;\n        /**\n         * Whether this request should be made in a way that exposes progress events.\n         *\n         * Progress events are expensive (change detection runs on each event) and so\n         * they should only be requested if the consumer intends to monitor them.\n         */\n        this.reportProgress = false;\n        /**\n         * Whether this request should be sent with outgoing credentials (cookies).\n         */\n        this.withCredentials = false;\n        /**\n         * The expected response type of the server.\n         *\n         * This is used to parse the response appropriately before returning it to\n         * the requestee.\n         */\n        this.responseType = 'json';\n        this.method = method.toUpperCase();\n        // Next, need to figure out which argument holds the HttpRequestInit\n        // options, if any.\n        let options;\n        // Check whether a body argument is expected. The only valid way to omit\n        // the body argument is to use a known no-body method like GET.\n        if (mightHaveBody(this.method) || !!fourth) {\n            // Body is the third argument, options are the fourth.\n            this.body = (third !== undefined) ? third : null;\n            options = fourth;\n        }\n        else {\n            // No body required, options are the third argument. The body stays null.\n            options = third;\n        }\n        // If options have been passed, interpret them.\n        if (options) {\n            // Normalize reportProgress and withCredentials.\n            this.reportProgress = !!options.reportProgress;\n            this.withCredentials = !!options.withCredentials;\n            // Override default response type of 'json' if one is provided.\n            if (!!options.responseType) {\n                this.responseType = options.responseType;\n            }\n            // Override headers if they're provided.\n            if (!!options.headers) {\n                this.headers = options.headers;\n            }\n            if (!!options.context) {\n                this.context = options.context;\n            }\n            if (!!options.params) {\n                this.params = options.params;\n            }\n        }\n        // If no headers have been passed in, construct a new HttpHeaders instance.\n        if (!this.headers) {\n            this.headers = new HttpHeaders();\n        }\n        // If no context have been passed in, construct a new HttpContext instance.\n        if (!this.context) {\n            this.context = new HttpContext();\n        }\n        // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n        if (!this.params) {\n            this.params = new HttpParams();\n            this.urlWithParams = url;\n        }\n        else {\n            // Encode the parameters to a string in preparation for inclusion in the URL.\n            const params = this.params.toString();\n            if (params.length === 0) {\n                // No parameters, the visible URL is just the URL given at creation time.\n                this.urlWithParams = url;\n            }\n            else {\n                // Does the URL already have query parameters? Look for '?'.\n                const qIdx = url.indexOf('?');\n                // There are 3 cases to handle:\n                // 1) No existing parameters -> append '?' followed by params.\n                // 2) '?' exists and is followed by existing query string ->\n                //    append '&' followed by params.\n                // 3) '?' exists at the end of the url -> append params directly.\n                // This basically amounts to determining the character, if any, with\n                // which to join the URL and parameters.\n                const sep = qIdx === -1 ? '?' : (qIdx < url.length - 1 ? '&' : '');\n                this.urlWithParams = url + sep + params;\n            }\n        }\n    }\n    /**\n     * Transform the free-form body into a serialized format suitable for\n     * transmission to the server.\n     */\n    serializeBody() {\n        // If no body is present, no need to serialize it.\n        if (this.body === null) {\n            return null;\n        }\n        // Check whether the body is already in a serialized form. If so,\n        // it can just be returned directly.\n        if (isArrayBuffer(this.body) || isBlob(this.body) || isFormData(this.body) ||\n            isUrlSearchParams(this.body) || typeof this.body === 'string') {\n            return this.body;\n        }\n        // Check whether the body is an instance of HttpUrlEncodedParams.\n        if (this.body instanceof HttpParams) {\n            return this.body.toString();\n        }\n        // Check whether the body is an object or array, and serialize with JSON if so.\n        if (typeof this.body === 'object' || typeof this.body === 'boolean' ||\n            Array.isArray(this.body)) {\n            return JSON.stringify(this.body);\n        }\n        // Fall back on toString() for everything else.\n        return this.body.toString();\n    }\n    /**\n     * Examine the body and attempt to infer an appropriate MIME type\n     * for it.\n     *\n     * If no such type can be inferred, this method will return `null`.\n     */\n    detectContentTypeHeader() {\n        // An empty body has no content type.\n        if (this.body === null) {\n            return null;\n        }\n        // FormData bodies rely on the browser's content type assignment.\n        if (isFormData(this.body)) {\n            return null;\n        }\n        // Blobs usually have their own content type. If it doesn't, then\n        // no type can be inferred.\n        if (isBlob(this.body)) {\n            return this.body.type || null;\n        }\n        // Array buffers have unknown contents and thus no type can be inferred.\n        if (isArrayBuffer(this.body)) {\n            return null;\n        }\n        // Technically, strings could be a form of JSON data, but it's safe enough\n        // to assume they're plain strings.\n        if (typeof this.body === 'string') {\n            return 'text/plain';\n        }\n        // `HttpUrlEncodedParams` has its own content-type.\n        if (this.body instanceof HttpParams) {\n            return 'application/x-www-form-urlencoded;charset=UTF-8';\n        }\n        // Arrays, objects, boolean and numbers will be encoded as JSON.\n        if (typeof this.body === 'object' || typeof this.body === 'number' ||\n            typeof this.body === 'boolean') {\n            return 'application/json';\n        }\n        // No type could be inferred.\n        return null;\n    }\n    clone(update = {}) {\n        // For method, url, and responseType, take the current value unless\n        // it is overridden in the update hash.\n        const method = update.method || this.method;\n        const url = update.url || this.url;\n        const responseType = update.responseType || this.responseType;\n        // The body is somewhat special - a `null` value in update.body means\n        // whatever current body is present is being overridden with an empty\n        // body, whereas an `undefined` value in update.body implies no\n        // override.\n        const body = (update.body !== undefined) ? update.body : this.body;\n        // Carefully handle the boolean options to differentiate between\n        // `false` and `undefined` in the update args.\n        const withCredentials = (update.withCredentials !== undefined) ? update.withCredentials : this.withCredentials;\n        const reportProgress = (update.reportProgress !== undefined) ? update.reportProgress : this.reportProgress;\n        // Headers and params may be appended to if `setHeaders` or\n        // `setParams` are used.\n        let headers = update.headers || this.headers;\n        let params = update.params || this.params;\n        // Pass on context if needed\n        const context = update.context ?? this.context;\n        // Check whether the caller has asked to add headers.\n        if (update.setHeaders !== undefined) {\n            // Set every requested header.\n            headers =\n                Object.keys(update.setHeaders)\n                    .reduce((headers, name) => headers.set(name, update.setHeaders[name]), headers);\n        }\n        // Check whether the caller has asked to set params.\n        if (update.setParams) {\n            // Set every requested param.\n            params = Object.keys(update.setParams)\n                .reduce((params, param) => params.set(param, update.setParams[param]), params);\n        }\n        // Finally, construct the new HttpRequest using the pieces from above.\n        return new HttpRequest(method, url, body, {\n            params,\n            headers,\n            context,\n            reportProgress,\n            responseType,\n            withCredentials,\n        });\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nvar HttpEventType;\n(function (HttpEventType) {\n    /**\n     * The request was sent out over the wire.\n     */\n    HttpEventType[HttpEventType[\"Sent\"] = 0] = \"Sent\";\n    /**\n     * An upload progress event was received.\n     */\n    HttpEventType[HttpEventType[\"UploadProgress\"] = 1] = \"UploadProgress\";\n    /**\n     * The response status code and headers were received.\n     */\n    HttpEventType[HttpEventType[\"ResponseHeader\"] = 2] = \"ResponseHeader\";\n    /**\n     * A download progress event was received.\n     */\n    HttpEventType[HttpEventType[\"DownloadProgress\"] = 3] = \"DownloadProgress\";\n    /**\n     * The full response including the body was received.\n     */\n    HttpEventType[HttpEventType[\"Response\"] = 4] = \"Response\";\n    /**\n     * A custom event from an interceptor or a backend.\n     */\n    HttpEventType[HttpEventType[\"User\"] = 5] = \"User\";\n})(HttpEventType || (HttpEventType = {}));\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nclass HttpResponseBase {\n    /**\n     * Super-constructor for all responses.\n     *\n     * The single parameter accepted is an initialization hash. Any properties\n     * of the response passed there will override the default values.\n     */\n    constructor(init, defaultStatus = 200 /* Ok */, defaultStatusText = 'OK') {\n        // If the hash has values passed, use them to initialize the response.\n        // Otherwise use the default values.\n        this.headers = init.headers || new HttpHeaders();\n        this.status = init.status !== undefined ? init.status : defaultStatus;\n        this.statusText = init.statusText || defaultStatusText;\n        this.url = init.url || null;\n        // Cache the ok value to avoid defining a getter.\n        this.ok = this.status >= 200 && this.status < 300;\n    }\n}\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nclass HttpHeaderResponse extends HttpResponseBase {\n    /**\n     * Create a new `HttpHeaderResponse` with the given parameters.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.type = HttpEventType.ResponseHeader;\n    }\n    /**\n     * Copy this `HttpHeaderResponse`, overriding its contents with the\n     * given parameter hash.\n     */\n    clone(update = {}) {\n        // Perform a straightforward initialization of the new HttpHeaderResponse,\n        // overriding the current parameters with new ones if given.\n        return new HttpHeaderResponse({\n            headers: update.headers || this.headers,\n            status: update.status !== undefined ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nclass HttpResponse extends HttpResponseBase {\n    /**\n     * Construct a new `HttpResponse`.\n     */\n    constructor(init = {}) {\n        super(init);\n        this.type = HttpEventType.Response;\n        this.body = init.body !== undefined ? init.body : null;\n    }\n    clone(update = {}) {\n        return new HttpResponse({\n            body: (update.body !== undefined) ? update.body : this.body,\n            headers: update.headers || this.headers,\n            status: (update.status !== undefined) ? update.status : this.status,\n            statusText: update.statusText || this.statusText,\n            url: update.url || this.url || undefined,\n        });\n    }\n}\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nclass HttpErrorResponse extends HttpResponseBase {\n    constructor(init) {\n        // Initialize with a default status of 0 / Unknown Error.\n        super(init, 0, 'Unknown Error');\n        this.name = 'HttpErrorResponse';\n        /**\n         * Errors are never okay, even when the status code is in the 2xx success range.\n         */\n        this.ok = false;\n        // If the response was successful, then this was a parse error. Otherwise, it was\n        // a protocol-level failure of some sort. Either the request failed in transit\n        // or the server returned an unsuccessful status code.\n        if (this.status >= 200 && this.status < 300) {\n            this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n        }\n        else {\n            this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${init.statusText}`;\n        }\n        this.error = init.error || null;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody(options, body) {\n    return {\n        body,\n        headers: options.headers,\n        context: options.context,\n        observe: options.observe,\n        params: options.params,\n        reportProgress: options.reportProgress,\n        responseType: options.responseType,\n        withCredentials: options.withCredentials,\n    };\n}\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n *\n * @usageNotes\n * Sample HTTP requests for the [Tour of Heroes](/tutorial/toh-pt0) application.\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\nclass HttpClient {\n    constructor(handler) {\n        this.handler = handler;\n    }\n    /**\n     * Constructs an observable for a generic HTTP request that, when subscribed,\n     * fires the request through the chain of registered interceptors and on to the\n     * server.\n     *\n     * You can pass an `HttpRequest` directly as the only parameter. In this case,\n     * the call returns an observable of the raw `HttpEvent` stream.\n     *\n     * Alternatively you can pass an HTTP method as the first parameter,\n     * a URL string as the second, and an options hash containing the request body as the third.\n     * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n     * type of returned observable.\n     *   * The `responseType` value determines how a successful response body is parsed.\n     *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n     * object as a type parameter to the call.\n     *\n     * The `observe` value determines the return type, according to what you are interested in\n     * observing.\n     *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n     * progress events by default.\n     *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n     * where the `T` parameter depends on the `responseType` and any optionally provided type\n     * parameter.\n     *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n     *\n     */\n    request(first, url, options = {}) {\n        let req;\n        // First, check whether the primary argument is an instance of `HttpRequest`.\n        if (first instanceof HttpRequest) {\n            // It is. The other arguments must be undefined (per the signatures) and can be\n            // ignored.\n            req = first;\n        }\n        else {\n            // It's a string, so it represents a URL. Construct a request based on it,\n            // and incorporate the remaining arguments (assuming `GET` unless a method is\n            // provided.\n            // Figure out the headers.\n            let headers = undefined;\n            if (options.headers instanceof HttpHeaders) {\n                headers = options.headers;\n            }\n            else {\n                headers = new HttpHeaders(options.headers);\n            }\n            // Sort out parameters.\n            let params = undefined;\n            if (!!options.params) {\n                if (options.params instanceof HttpParams) {\n                    params = options.params;\n                }\n                else {\n                    params = new HttpParams({ fromObject: options.params });\n                }\n            }\n            // Construct the request.\n            req = new HttpRequest(first, url, (options.body !== undefined ? options.body : null), {\n                headers,\n                context: options.context,\n                params,\n                reportProgress: options.reportProgress,\n                // By default, JSON is assumed to be returned for all calls.\n                responseType: options.responseType || 'json',\n                withCredentials: options.withCredentials,\n            });\n        }\n        // Start with an Observable.of() the initial request, and run the handler (which\n        // includes all interceptors) inside a concatMap(). This way, the handler runs\n        // inside an Observable chain, which causes interceptors to be re-run on every\n        // subscription (this also makes retries re-run the handler, including interceptors).\n        const events$ = of(req).pipe(concatMap((req) => this.handler.handle(req)));\n        // If coming via the API signature which accepts a previously constructed HttpRequest,\n        // the only option is to get the event stream. Otherwise, return the event stream if\n        // that is what was requested.\n        if (first instanceof HttpRequest || options.observe === 'events') {\n            return events$;\n        }\n        // The requested stream contains either the full response or the body. In either\n        // case, the first step is to filter the event stream to extract a stream of\n        // responses(s).\n        const res$ = events$.pipe(filter((event) => event instanceof HttpResponse));\n        // Decide which stream to return.\n        switch (options.observe || 'body') {\n            case 'body':\n                // The requested stream is the body. Map the response stream to the response\n                // body. This could be done more simply, but a misbehaving interceptor might\n                // transform the response body into a different format and ignore the requested\n                // responseType. Guard against this by validating that the response is of the\n                // requested type.\n                switch (req.responseType) {\n                    case 'arraybuffer':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is an ArrayBuffer.\n                            if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                                throw new Error('Response is not an ArrayBuffer.');\n                            }\n                            return res.body;\n                        }));\n                    case 'blob':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a Blob.\n                            if (res.body !== null && !(res.body instanceof Blob)) {\n                                throw new Error('Response is not a Blob.');\n                            }\n                            return res.body;\n                        }));\n                    case 'text':\n                        return res$.pipe(map((res) => {\n                            // Validate that the body is a string.\n                            if (res.body !== null && typeof res.body !== 'string') {\n                                throw new Error('Response is not a string.');\n                            }\n                            return res.body;\n                        }));\n                    case 'json':\n                    default:\n                        // No validation needed for JSON responses, as they can be of any type.\n                        return res$.pipe(map((res) => res.body));\n                }\n            case 'response':\n                // The response stream was requested directly, so return it.\n                return res$;\n            default:\n                // Guard against new future observe types being added.\n                throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n        }\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `DELETE` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     *\n     * @param url     The endpoint URL.\n     * @param options The HTTP options to send with the request.\n     *\n     */\n    delete(url, options = {}) {\n        return this.request('DELETE', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `GET` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    get(url, options = {}) {\n        return this.request('GET', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `HEAD` request to execute on the server. The `HEAD` method returns\n     * meta information about the resource without transferring the\n     * resource itself. See the individual overloads for\n     * details on the return type.\n     */\n    head(url, options = {}) {\n        return this.request('HEAD', url, options);\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes a request with the special method\n     * `JSONP` to be dispatched via the interceptor pipeline.\n     * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n     * API endpoints that don't support newer,\n     * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n     * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n     * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n     * application making the request.\n     * The endpoint API must support JSONP callback for JSONP requests to work.\n     * The resource API returns the JSON response wrapped in a callback function.\n     * You can pass the callback function name as one of the query parameters.\n     * Note that JSONP requests can only be used with `GET` requests.\n     *\n     * @param url The resource URL.\n     * @param callbackParam The callback function name.\n     *\n     */\n    jsonp(url, callbackParam) {\n        return this.request('JSONP', url, {\n            params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n            observe: 'body',\n            responseType: 'json',\n        });\n    }\n    /**\n     * Constructs an `Observable` that, when subscribed, causes the configured\n     * `OPTIONS` request to execute on the server. This method allows the client\n     * to determine the supported HTTP methods and other capabilities of an endpoint,\n     * without implying a resource action. See the individual overloads for\n     * details on the return type.\n     */\n    options(url, options = {}) {\n        return this.request('OPTIONS', url, options);\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PATCH` request to execute on the server. See the individual overloads for\n     * details on the return type.\n     */\n    patch(url, body, options = {}) {\n        return this.request('PATCH', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `POST` request to execute on the server. The server responds with the location of\n     * the replaced resource. See the individual overloads for\n     * details on the return type.\n     */\n    post(url, body, options = {}) {\n        return this.request('POST', url, addBody(options, body));\n    }\n    /**\n     * Constructs an observable that, when subscribed, causes the configured\n     * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n     * with a new set of values.\n     * See the individual overloads for details on the return type.\n     */\n    put(url, body, options = {}) {\n        return this.request('PUT', url, addBody(options, body));\n    }\n}\nHttpClient.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClient, deps: [{ token: HttpHandler }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpClient.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClient });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClient, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: HttpHandler }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * `HttpHandler` which applies an `HttpInterceptor` to an `HttpRequest`.\n *\n *\n */\nclass HttpInterceptorHandler {\n    constructor(next, interceptor) {\n        this.next = next;\n        this.interceptor = interceptor;\n    }\n    handle(req) {\n        return this.interceptor.intercept(req, this.next);\n    }\n}\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nconst HTTP_INTERCEPTORS = new InjectionToken('HTTP_INTERCEPTORS');\nclass NoopInterceptor {\n    intercept(req, next) {\n        return next.handle(req);\n    }\n}\nNoopInterceptor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NoopInterceptor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nNoopInterceptor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NoopInterceptor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NoopInterceptor, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId = 0;\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nconst JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nconst JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nconst JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nclass JsonpCallbackContext {\n}\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see `HttpHandler`\n * @see `HttpXhrBackend`\n *\n * @publicApi\n */\nclass JsonpClientBackend {\n    constructor(callbackMap, document) {\n        this.callbackMap = callbackMap;\n        this.document = document;\n        /**\n         * A resolved promise that can be used to schedule microtasks in the event handlers.\n         */\n        this.resolvedPromise = Promise.resolve();\n    }\n    /**\n     * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n     */\n    nextCallback() {\n        return `ng_jsonp_callback_${nextRequestId++}`;\n    }\n    /**\n     * Processes a JSONP request and returns an event stream of the results.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     *\n     */\n    handle(req) {\n        // Firstly, check both the method and response type. If either doesn't match\n        // then the request was improperly routed here and cannot be handled.\n        if (req.method !== 'JSONP') {\n            throw new Error(JSONP_ERR_WRONG_METHOD);\n        }\n        else if (req.responseType !== 'json') {\n            throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n        }\n        // Everything else happens inside the Observable boundary.\n        return new Observable((observer) => {\n            // The first step to make a request is to generate the callback name, and replace the\n            // callback placeholder in the URL with the name. Care has to be taken here to ensure\n            // a trailing &, if matched, gets inserted back into the URL in the correct place.\n            const callback = this.nextCallback();\n            const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n            // Construct the <script> tag and point it at the URL.\n            const node = this.document.createElement('script');\n            node.src = url;\n            // A JSONP request requires waiting for multiple callbacks. These variables\n            // are closed over and track state across those callbacks.\n            // The response object, if one has been received, or null otherwise.\n            let body = null;\n            // Whether the response callback has been called.\n            let finished = false;\n            // Whether the request has been cancelled (and thus any other callbacks)\n            // should be ignored.\n            let cancelled = false;\n            // Set the response callback in this.callbackMap (which will be the window\n            // object in the browser. The script being loaded via the <script> tag will\n            // eventually call this callback.\n            this.callbackMap[callback] = (data) => {\n                // Data has been received from the JSONP script. Firstly, delete this callback.\n                delete this.callbackMap[callback];\n                // Next, make sure the request wasn't cancelled in the meantime.\n                if (cancelled) {\n                    return;\n                }\n                // Set state to indicate data was received.\n                body = data;\n                finished = true;\n            };\n            // cleanup() is a utility closure that removes the <script> from the page and\n            // the response callback from the window. This logic is used in both the\n            // success, error, and cancellation paths, so it's extracted out for convenience.\n            const cleanup = () => {\n                // Remove the <script> tag if it's still on the page.\n                if (node.parentNode) {\n                    node.parentNode.removeChild(node);\n                }\n                // Remove the response callback from the callbackMap (window object in the\n                // browser).\n                delete this.callbackMap[callback];\n            };\n            // onLoad() is the success callback which runs after the response callback\n            // if the JSONP script loads successfully. The event itself is unimportant.\n            // If something went wrong, onLoad() may run without the response callback\n            // having been invoked.\n            const onLoad = (event) => {\n                // Do nothing if the request has been cancelled.\n                if (cancelled) {\n                    return;\n                }\n                // We wrap it in an extra Promise, to ensure the microtask\n                // is scheduled after the loaded endpoint has executed any potential microtask itself,\n                // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n                this.resolvedPromise.then(() => {\n                    // Cleanup the page.\n                    cleanup();\n                    // Check whether the response callback has run.\n                    if (!finished) {\n                        // It hasn't, something went wrong with the request. Return an error via\n                        // the Observable error path. All JSONP errors have status 0.\n                        observer.error(new HttpErrorResponse({\n                            url,\n                            status: 0,\n                            statusText: 'JSONP Error',\n                            error: new Error(JSONP_ERR_NO_CALLBACK),\n                        }));\n                        return;\n                    }\n                    // Success. body either contains the response body or null if none was\n                    // returned.\n                    observer.next(new HttpResponse({\n                        body,\n                        status: 200 /* Ok */,\n                        statusText: 'OK',\n                        url,\n                    }));\n                    // Complete the stream, the response is over.\n                    observer.complete();\n                });\n            };\n            // onError() is the error callback, which runs if the script returned generates\n            // a Javascript error. It emits the error via the Observable error channel as\n            // a HttpErrorResponse.\n            const onError = (error) => {\n                // If the request was already cancelled, no need to emit anything.\n                if (cancelled) {\n                    return;\n                }\n                cleanup();\n                // Wrap the error in a HttpErrorResponse.\n                observer.error(new HttpErrorResponse({\n                    error,\n                    status: 0,\n                    statusText: 'JSONP Error',\n                    url,\n                }));\n            };\n            // Subscribe to both the success (load) and error events on the <script> tag,\n            // and add it to the page.\n            node.addEventListener('load', onLoad);\n            node.addEventListener('error', onError);\n            this.document.body.appendChild(node);\n            // The request has now been successfully sent.\n            observer.next({ type: HttpEventType.Sent });\n            // Cancellation handler.\n            return () => {\n                // Track the cancellation so event listeners won't do anything even if already scheduled.\n                cancelled = true;\n                // Remove the event listeners so they won't run if the events later fire.\n                node.removeEventListener('load', onLoad);\n                node.removeEventListener('error', onError);\n                // And finally, clean up the page.\n                cleanup();\n            };\n        });\n    }\n}\nJsonpClientBackend.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: JsonpClientBackend, deps: [{ token: JsonpCallbackContext }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nJsonpClientBackend.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: JsonpClientBackend });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: JsonpClientBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: JsonpCallbackContext }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see `HttpInterceptor`\n *\n * @publicApi\n */\nclass JsonpInterceptor {\n    constructor(jsonp) {\n        this.jsonp = jsonp;\n    }\n    /**\n     * Identifies and handles a given JSONP request.\n     * @param req The outgoing request object to handle.\n     * @param next The next interceptor in the chain, or the backend\n     * if no interceptors remain in the chain.\n     * @returns An observable of the event stream.\n     */\n    intercept(req, next) {\n        if (req.method === 'JSONP') {\n            return this.jsonp.handle(req);\n        }\n        // Fall through for normal HTTP requests.\n        return next.handle(req);\n    }\n}\nJsonpInterceptor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: JsonpInterceptor, deps: [{ token: JsonpClientBackend }], target: i0.ɵɵFactoryTarget.Injectable });\nJsonpInterceptor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: JsonpInterceptor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: JsonpInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: JsonpClientBackend }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr) {\n    if ('responseURL' in xhr && xhr.responseURL) {\n        return xhr.responseURL;\n    }\n    if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n        return xhr.getResponseHeader('X-Request-URL');\n    }\n    return null;\n}\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see `HttpHandler`\n * @see `JsonpClientBackend`\n *\n * @publicApi\n */\nclass HttpXhrBackend {\n    constructor(xhrFactory) {\n        this.xhrFactory = xhrFactory;\n    }\n    /**\n     * Processes a request and returns a stream of response events.\n     * @param req The request object.\n     * @returns An observable of the response events.\n     */\n    handle(req) {\n        // Quick check to give a better error message when a user attempts to use\n        // HttpClient.jsonp() without installing the HttpClientJsonpModule\n        if (req.method === 'JSONP') {\n            throw new Error(`Attempted to construct Jsonp request without HttpClientJsonpModule installed.`);\n        }\n        // Everything happens on Observable subscription.\n        return new Observable((observer) => {\n            // Start by setting up the XHR object with request method, URL, and withCredentials flag.\n            const xhr = this.xhrFactory.build();\n            xhr.open(req.method, req.urlWithParams);\n            if (!!req.withCredentials) {\n                xhr.withCredentials = true;\n            }\n            // Add all the requested headers.\n            req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n            // Add an Accept header if one isn't present already.\n            if (!req.headers.has('Accept')) {\n                xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n            }\n            // Auto-detect the Content-Type header if one isn't present already.\n            if (!req.headers.has('Content-Type')) {\n                const detectedType = req.detectContentTypeHeader();\n                // Sometimes Content-Type detection fails.\n                if (detectedType !== null) {\n                    xhr.setRequestHeader('Content-Type', detectedType);\n                }\n            }\n            // Set the responseType if one was requested.\n            if (req.responseType) {\n                const responseType = req.responseType.toLowerCase();\n                // JSON responses need to be processed as text. This is because if the server\n                // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n                // xhr.response will be null, and xhr.responseText cannot be accessed to\n                // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n                // is parsed by first requesting text and then applying JSON.parse.\n                xhr.responseType = ((responseType !== 'json') ? responseType : 'text');\n            }\n            // Serialize the request body if one is present. If not, this will be set to null.\n            const reqBody = req.serializeBody();\n            // If progress events are enabled, response headers will be delivered\n            // in two events - the HttpHeaderResponse event and the full HttpResponse\n            // event. However, since response headers don't change in between these\n            // two events, it doesn't make sense to parse them twice. So headerResponse\n            // caches the data extracted from the response whenever it's first parsed,\n            // to ensure parsing isn't duplicated.\n            let headerResponse = null;\n            // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n            // state, and memoizes it into headerResponse.\n            const partialFromXhr = () => {\n                if (headerResponse !== null) {\n                    return headerResponse;\n                }\n                // Read status and normalize an IE9 bug (https://bugs.jquery.com/ticket/1450).\n                const status = xhr.status === 1223 ? 204 /* NoContent */ : xhr.status;\n                const statusText = xhr.statusText || 'OK';\n                // Parse headers from XMLHttpRequest - this step is lazy.\n                const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n                // Read the response URL from the XMLHttpResponse instance and fall back on the\n                // request URL.\n                const url = getResponseUrl(xhr) || req.url;\n                // Construct the HttpHeaderResponse and memoize it.\n                headerResponse = new HttpHeaderResponse({ headers, status, statusText, url });\n                return headerResponse;\n            };\n            // Next, a few closures are defined for the various events which XMLHttpRequest can\n            // emit. This allows them to be unregistered as event listeners later.\n            // First up is the load event, which represents a response being fully available.\n            const onLoad = () => {\n                // Read response state from the memoized partial data.\n                let { headers, status, statusText, url } = partialFromXhr();\n                // The body will be read out if present.\n                let body = null;\n                if (status !== 204 /* NoContent */) {\n                    // Use XMLHttpRequest.response if set, responseText otherwise.\n                    body = (typeof xhr.response === 'undefined') ? xhr.responseText : xhr.response;\n                }\n                // Normalize another potential bug (this one comes from CORS).\n                if (status === 0) {\n                    status = !!body ? 200 /* Ok */ : 0;\n                }\n                // ok determines whether the response will be transmitted on the event or\n                // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n                // but a successful status code can still result in an error if the user\n                // asked for JSON data and the body cannot be parsed as such.\n                let ok = status >= 200 && status < 300;\n                // Check whether the body needs to be parsed as JSON (in many cases the browser\n                // will have done that already).\n                if (req.responseType === 'json' && typeof body === 'string') {\n                    // Save the original body, before attempting XSSI prefix stripping.\n                    const originalBody = body;\n                    body = body.replace(XSSI_PREFIX, '');\n                    try {\n                        // Attempt the parse. If it fails, a parse error should be delivered to the user.\n                        body = body !== '' ? JSON.parse(body) : null;\n                    }\n                    catch (error) {\n                        // Since the JSON.parse failed, it's reasonable to assume this might not have been a\n                        // JSON response. Restore the original body (including any XSSI prefix) to deliver\n                        // a better error response.\n                        body = originalBody;\n                        // If this was an error request to begin with, leave it as a string, it probably\n                        // just isn't JSON. Otherwise, deliver the parsing error to the user.\n                        if (ok) {\n                            // Even though the response status was 2xx, this is still an error.\n                            ok = false;\n                            // The parse error contains the text of the body that failed to parse.\n                            body = { error, text: body };\n                        }\n                    }\n                }\n                if (ok) {\n                    // A successful response is delivered on the event stream.\n                    observer.next(new HttpResponse({\n                        body,\n                        headers,\n                        status,\n                        statusText,\n                        url: url || undefined,\n                    }));\n                    // The full body has been received and delivered, no further events\n                    // are possible. This request is complete.\n                    observer.complete();\n                }\n                else {\n                    // An unsuccessful request is delivered on the error channel.\n                    observer.error(new HttpErrorResponse({\n                        // The error in this case is the response body (error from the server).\n                        error: body,\n                        headers,\n                        status,\n                        statusText,\n                        url: url || undefined,\n                    }));\n                }\n            };\n            // The onError callback is called when something goes wrong at the network level.\n            // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n            // transmitted on the error channel.\n            const onError = (error) => {\n                const { url } = partialFromXhr();\n                const res = new HttpErrorResponse({\n                    error,\n                    status: xhr.status || 0,\n                    statusText: xhr.statusText || 'Unknown Error',\n                    url: url || undefined,\n                });\n                observer.error(res);\n            };\n            // The sentHeaders flag tracks whether the HttpResponseHeaders event\n            // has been sent on the stream. This is necessary to track if progress\n            // is enabled since the event will be sent on only the first download\n            // progerss event.\n            let sentHeaders = false;\n            // The download progress event handler, which is only registered if\n            // progress events are enabled.\n            const onDownProgress = (event) => {\n                // Send the HttpResponseHeaders event if it hasn't been sent already.\n                if (!sentHeaders) {\n                    observer.next(partialFromXhr());\n                    sentHeaders = true;\n                }\n                // Start building the download progress event to deliver on the response\n                // event stream.\n                let progressEvent = {\n                    type: HttpEventType.DownloadProgress,\n                    loaded: event.loaded,\n                };\n                // Set the total number of bytes in the event if it's available.\n                if (event.lengthComputable) {\n                    progressEvent.total = event.total;\n                }\n                // If the request was for text content and a partial response is\n                // available on XMLHttpRequest, include it in the progress event\n                // to allow for streaming reads.\n                if (req.responseType === 'text' && !!xhr.responseText) {\n                    progressEvent.partialText = xhr.responseText;\n                }\n                // Finally, fire the event.\n                observer.next(progressEvent);\n            };\n            // The upload progress event handler, which is only registered if\n            // progress events are enabled.\n            const onUpProgress = (event) => {\n                // Upload progress events are simpler. Begin building the progress\n                // event.\n                let progress = {\n                    type: HttpEventType.UploadProgress,\n                    loaded: event.loaded,\n                };\n                // If the total number of bytes being uploaded is available, include\n                // it.\n                if (event.lengthComputable) {\n                    progress.total = event.total;\n                }\n                // Send the event.\n                observer.next(progress);\n            };\n            // By default, register for load and error events.\n            xhr.addEventListener('load', onLoad);\n            xhr.addEventListener('error', onError);\n            xhr.addEventListener('timeout', onError);\n            xhr.addEventListener('abort', onError);\n            // Progress events are only enabled if requested.\n            if (req.reportProgress) {\n                // Download progress is always enabled if requested.\n                xhr.addEventListener('progress', onDownProgress);\n                // Upload progress depends on whether there is a body to upload.\n                if (reqBody !== null && xhr.upload) {\n                    xhr.upload.addEventListener('progress', onUpProgress);\n                }\n            }\n            // Fire the request, and notify the event stream that it was fired.\n            xhr.send(reqBody);\n            observer.next({ type: HttpEventType.Sent });\n            // This is the return from the Observable function, which is the\n            // request cancellation handler.\n            return () => {\n                // On a cancellation, remove all registered event listeners.\n                xhr.removeEventListener('error', onError);\n                xhr.removeEventListener('abort', onError);\n                xhr.removeEventListener('load', onLoad);\n                xhr.removeEventListener('timeout', onError);\n                if (req.reportProgress) {\n                    xhr.removeEventListener('progress', onDownProgress);\n                    if (reqBody !== null && xhr.upload) {\n                        xhr.upload.removeEventListener('progress', onUpProgress);\n                    }\n                }\n                // Finally, abort the in-flight request.\n                if (xhr.readyState !== xhr.DONE) {\n                    xhr.abort();\n                }\n            };\n        });\n    }\n}\nHttpXhrBackend.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpXhrBackend, deps: [{ token: i1.XhrFactory }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpXhrBackend.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpXhrBackend });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpXhrBackend, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i1.XhrFactory }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst XSRF_COOKIE_NAME = new InjectionToken('XSRF_COOKIE_NAME');\nconst XSRF_HEADER_NAME = new InjectionToken('XSRF_HEADER_NAME');\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nclass HttpXsrfTokenExtractor {\n}\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\nclass HttpXsrfCookieExtractor {\n    constructor(doc, platform, cookieName) {\n        this.doc = doc;\n        this.platform = platform;\n        this.cookieName = cookieName;\n        this.lastCookieString = '';\n        this.lastToken = null;\n        /**\n         * @internal for testing\n         */\n        this.parseCount = 0;\n    }\n    getToken() {\n        if (this.platform === 'server') {\n            return null;\n        }\n        const cookieString = this.doc.cookie || '';\n        if (cookieString !== this.lastCookieString) {\n            this.parseCount++;\n            this.lastToken = ɵparseCookieValue(cookieString, this.cookieName);\n            this.lastCookieString = cookieString;\n        }\n        return this.lastToken;\n    }\n}\nHttpXsrfCookieExtractor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpXsrfCookieExtractor, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: XSRF_COOKIE_NAME }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpXsrfCookieExtractor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpXsrfCookieExtractor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpXsrfCookieExtractor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [XSRF_COOKIE_NAME]\n                }] }]; } });\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\nclass HttpXsrfInterceptor {\n    constructor(tokenService, headerName) {\n        this.tokenService = tokenService;\n        this.headerName = headerName;\n    }\n    intercept(req, next) {\n        const lcUrl = req.url.toLowerCase();\n        // Skip both non-mutating requests and absolute URLs.\n        // Non-mutating requests don't require a token, and absolute URLs require special handling\n        // anyway as the cookie set\n        // on our origin is not the same as the token expected by another origin.\n        if (req.method === 'GET' || req.method === 'HEAD' || lcUrl.startsWith('http://') ||\n            lcUrl.startsWith('https://')) {\n            return next.handle(req);\n        }\n        const token = this.tokenService.getToken();\n        // Be careful not to overwrite an existing header of the same name.\n        if (token !== null && !req.headers.has(this.headerName)) {\n            req = req.clone({ headers: req.headers.set(this.headerName, token) });\n        }\n        return next.handle(req);\n    }\n}\nHttpXsrfInterceptor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpXsrfInterceptor, deps: [{ token: HttpXsrfTokenExtractor }, { token: XSRF_HEADER_NAME }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpXsrfInterceptor.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpXsrfInterceptor });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpXsrfInterceptor, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: HttpXsrfTokenExtractor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [XSRF_HEADER_NAME]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * An injectable `HttpHandler` that applies multiple interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * The interceptors are loaded lazily from the injector, to allow\n * interceptors to themselves inject classes depending indirectly\n * on `HttpInterceptingHandler` itself.\n * @see `HttpInterceptor`\n */\nclass HttpInterceptingHandler {\n    constructor(backend, injector) {\n        this.backend = backend;\n        this.injector = injector;\n        this.chain = null;\n    }\n    handle(req) {\n        if (this.chain === null) {\n            const interceptors = this.injector.get(HTTP_INTERCEPTORS, []);\n            this.chain = interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), this.backend);\n        }\n        return this.chain.handle(req);\n    }\n}\nHttpInterceptingHandler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpInterceptingHandler, deps: [{ token: HttpBackend }, { token: i0.Injector }], target: i0.ɵɵFactoryTarget.Injectable });\nHttpInterceptingHandler.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpInterceptingHandler });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpInterceptingHandler, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: HttpBackend }, { type: i0.Injector }]; } });\n/**\n * Constructs an `HttpHandler` that applies interceptors\n * to a request before passing it to the given `HttpBackend`.\n *\n * Use as a factory function within `HttpClientModule`.\n *\n *\n */\nfunction interceptingHandler(backend, interceptors = []) {\n    if (!interceptors) {\n        return backend;\n    }\n    return interceptors.reduceRight((next, interceptor) => new HttpInterceptorHandler(next, interceptor), backend);\n}\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nfunction jsonpCallbackContext() {\n    if (typeof window === 'object') {\n        return window;\n    }\n    return {};\n}\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n */\nclass HttpClientXsrfModule {\n    /**\n     * Disable the default XSRF protection.\n     */\n    static disable() {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [\n                { provide: HttpXsrfInterceptor, useClass: NoopInterceptor },\n            ],\n        };\n    }\n    /**\n     * Configure XSRF protection.\n     * @param options An object that can specify either or both\n     * cookie name or header name.\n     * - Cookie name default is `XSRF-TOKEN`.\n     * - Header name default is `X-XSRF-TOKEN`.\n     *\n     */\n    static withOptions(options = {}) {\n        return {\n            ngModule: HttpClientXsrfModule,\n            providers: [\n                options.cookieName ? { provide: XSRF_COOKIE_NAME, useValue: options.cookieName } : [],\n                options.headerName ? { provide: XSRF_HEADER_NAME, useValue: options.headerName } : [],\n            ],\n        };\n    }\n}\nHttpClientXsrfModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientXsrfModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientXsrfModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientXsrfModule });\nHttpClientXsrfModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientXsrfModule, providers: [\n        HttpXsrfInterceptor,\n        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n        { provide: XSRF_COOKIE_NAME, useValue: 'XSRF-TOKEN' },\n        { provide: XSRF_HEADER_NAME, useValue: 'X-XSRF-TOKEN' },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientXsrfModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        HttpXsrfInterceptor,\n                        { provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true },\n                        { provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor },\n                        { provide: XSRF_COOKIE_NAME, useValue: 'XSRF-TOKEN' },\n                        { provide: XSRF_HEADER_NAME, useValue: 'X-XSRF-TOKEN' },\n                    ],\n                }]\n        }] });\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\nclass HttpClientModule {\n}\nHttpClientModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientModule, imports: [HttpClientXsrfModule] });\nHttpClientModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientModule, providers: [\n        HttpClient,\n        { provide: HttpHandler, useClass: HttpInterceptingHandler },\n        HttpXhrBackend,\n        { provide: HttpBackend, useExisting: HttpXhrBackend },\n    ], imports: [[\n            HttpClientXsrfModule.withOptions({\n                cookieName: 'XSRF-TOKEN',\n                headerName: 'X-XSRF-TOKEN',\n            }),\n        ]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    /**\n                     * Optional configuration for XSRF protection.\n                     */\n                    imports: [\n                        HttpClientXsrfModule.withOptions({\n                            cookieName: 'XSRF-TOKEN',\n                            headerName: 'X-XSRF-TOKEN',\n                        }),\n                    ],\n                    /**\n                     * Configures the [dependency injector](guide/glossary#injector) where it is imported\n                     * with supporting services for HTTP communications.\n                     */\n                    providers: [\n                        HttpClient,\n                        { provide: HttpHandler, useClass: HttpInterceptingHandler },\n                        HttpXhrBackend,\n                        { provide: HttpBackend, useExisting: HttpXhrBackend },\n                    ],\n                }]\n        }] });\n/**\n * Configures the [dependency injector](guide/glossary#injector) for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in [DI token](guide/glossary#di-token) `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n */\nclass HttpClientJsonpModule {\n}\nHttpClientJsonpModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientJsonpModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHttpClientJsonpModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientJsonpModule });\nHttpClientJsonpModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientJsonpModule, providers: [\n        JsonpClientBackend,\n        { provide: JsonpCallbackContext, useFactory: jsonpCallbackContext },\n        { provide: HTTP_INTERCEPTORS, useClass: JsonpInterceptor, multi: true },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HttpClientJsonpModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        JsonpClientBackend,\n                        { provide: JsonpCallbackContext, useFactory: jsonpCallbackContext },\n                        { provide: HTTP_INTERCEPTORS, useClass: JsonpInterceptor, multi: true },\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n * @see `XhrFactory`\n * @deprecated\n * `XhrFactory` has moved, please import `XhrFactory` from `@angular/common` instead.\n */\nconst XhrFactory = XhrFactory$1;\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { HTTP_INTERCEPTORS, HttpBackend, HttpClient, HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule, HttpContext, HttpContextToken, HttpErrorResponse, HttpEventType, HttpHandler, HttpHeaderResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse, HttpResponseBase, HttpUrlEncodingCodec, HttpXhrBackend, HttpXsrfTokenExtractor, JsonpClientBackend, JsonpInterceptor, XhrFactory, HttpInterceptingHandler as ɵHttpInterceptingHandler };\n"]}, "metadata": {}, "sourceType": "module"}
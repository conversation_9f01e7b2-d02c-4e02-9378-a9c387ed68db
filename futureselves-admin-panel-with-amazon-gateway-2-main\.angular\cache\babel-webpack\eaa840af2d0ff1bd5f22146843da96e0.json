{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, isDevMode, Component, ChangeDetectionStrategy, ViewChild, Input, HostBinding, Output, HostListener, NgModule } from '@angular/core';\nimport * as i4 from '@angular/platform-browser';\nimport * as i5 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"wrapper\"];\nconst _c1 = [\"sourceImage\"];\n\nfunction ImageCropperComponent_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"img\", 4, 5);\n    i0.ɵɵlistener(\"load\", function ImageCropperComponent_img_2_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return ctx_r4.imageLoadedInView();\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"visibility\", ctx_r1.imageVisible ? \"visible\" : \"hidden\")(\"transform\", ctx_r1.safeTransformStyle);\n    i0.ɵɵproperty(\"src\", ctx_r1.safeImgDataUrl, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction ImageCropperComponent_div_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 9);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return ctx_r7.startMove($event, ctx_r7.moveTypes.Resize, \"topleft\");\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return ctx_r9.startMove($event, ctx_r9.moveTypes.Resize, \"topleft\");\n    });\n    i0.ɵɵelement(2, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 11);\n    i0.ɵɵelement(4, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 12);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_5_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return ctx_r10.startMove($event, ctx_r10.moveTypes.Resize, \"topright\");\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_5_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return ctx_r11.startMove($event, ctx_r11.moveTypes.Resize, \"topright\");\n    });\n    i0.ɵɵelement(6, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 13);\n    i0.ɵɵelement(8, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 14);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_9_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return ctx_r12.startMove($event, ctx_r12.moveTypes.Resize, \"bottomright\");\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_9_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return ctx_r13.startMove($event, ctx_r13.moveTypes.Resize, \"bottomright\");\n    });\n    i0.ɵɵelement(10, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 15);\n    i0.ɵɵelement(12, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 16);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_13_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return ctx_r14.startMove($event, ctx_r14.moveTypes.Resize, \"bottomleft\");\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_13_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return ctx_r15.startMove($event, ctx_r15.moveTypes.Resize, \"bottomleft\");\n    });\n    i0.ɵɵelement(14, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 17);\n    i0.ɵɵelement(16, \"span\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 18);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_17_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return ctx_r16.startMove($event, ctx_r16.moveTypes.Resize, \"top\");\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_17_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return ctx_r17.startMove($event, ctx_r17.moveTypes.Resize, \"top\");\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 19);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_18_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return ctx_r18.startMove($event, ctx_r18.moveTypes.Resize, \"right\");\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_18_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return ctx_r19.startMove($event, ctx_r19.moveTypes.Resize, \"right\");\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 20);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_19_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return ctx_r20.startMove($event, ctx_r20.moveTypes.Resize, \"bottom\");\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_19_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return ctx_r21.startMove($event, ctx_r21.moveTypes.Resize, \"bottom\");\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 21);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_ng_container_2_Template_span_mousedown_20_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return ctx_r22.startMove($event, ctx_r22.moveTypes.Resize, \"left\");\n    })(\"touchstart\", function ImageCropperComponent_div_4_ng_container_2_Template_span_touchstart_20_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return ctx_r23.startMove($event, ctx_r23.moveTypes.Resize, \"left\");\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nfunction ImageCropperComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"keydown\", function ImageCropperComponent_div_4_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return ctx_r24.keyboardAccess($event);\n    });\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵlistener(\"mousedown\", function ImageCropperComponent_div_4_Template_div_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return ctx_r26.startMove($event, ctx_r26.moveTypes.Move);\n    })(\"touchstart\", function ImageCropperComponent_div_4_Template_div_touchstart_1_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return ctx_r27.startMove($event, ctx_r27.moveTypes.Move);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ImageCropperComponent_div_4_ng_container_2_Template, 21, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"top\", ctx_r2.cropper.y1, \"px\")(\"left\", ctx_r2.cropper.x1, \"px\")(\"width\", ctx_r2.cropper.x2 - ctx_r2.cropper.x1, \"px\")(\"height\", ctx_r2.cropper.y2 - ctx_r2.cropper.y1, \"px\")(\"margin-left\", ctx_r2.alignImage === \"center\" ? ctx_r2.marginLeft : null)(\"visibility\", ctx_r2.imageVisible ? \"visible\" : \"hidden\");\n    i0.ɵɵclassProp(\"ngx-ic-round\", ctx_r2.roundCropper);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.hideResizeSquares);\n  }\n}\n\nclass CropperSettings {\n  constructor() {\n    // From options\n    this.format = 'png';\n    this.maintainAspectRatio = true;\n    this.transform = {};\n    this.aspectRatio = 1;\n    this.resizeToWidth = 0;\n    this.resizeToHeight = 0;\n    this.cropperMinWidth = 0;\n    this.cropperMinHeight = 0;\n    this.cropperMaxHeight = 0;\n    this.cropperMaxWidth = 0;\n    this.cropperStaticWidth = 0;\n    this.cropperStaticHeight = 0;\n    this.canvasRotation = 0;\n    this.initialStepSize = 3;\n    this.roundCropper = false;\n    this.onlyScaleDown = false;\n    this.imageQuality = 92;\n    this.autoCrop = true;\n    this.backgroundColor = null;\n    this.containWithinAspectRatio = false;\n    this.hideResizeSquares = false;\n    this.alignImage = 'center'; // Internal\n\n    this.cropperScaledMinWidth = 20;\n    this.cropperScaledMinHeight = 20;\n    this.cropperScaledMaxWidth = 20;\n    this.cropperScaledMaxHeight = 20;\n    this.stepSize = this.initialStepSize;\n  }\n\n  setOptions(options) {\n    Object.keys(options).filter(k => k in this).forEach(k => this[k] = options[k]);\n    this.validateOptions();\n  }\n\n  setOptionsFromChanges(changes) {\n    Object.keys(changes).filter(k => k in this).forEach(k => this[k] = changes[k].currentValue);\n    this.validateOptions();\n  }\n\n  validateOptions() {\n    if (this.maintainAspectRatio && !this.aspectRatio) {\n      throw new Error('`aspectRatio` should > 0 when `maintainAspectRatio` is enabled');\n    }\n  }\n\n}\n\nvar MoveTypes;\n\n(function (MoveTypes) {\n  MoveTypes[\"Move\"] = \"move\";\n  MoveTypes[\"Resize\"] = \"resize\";\n  MoveTypes[\"Pinch\"] = \"pinch\";\n})(MoveTypes || (MoveTypes = {}));\n\nfunction getPositionForKey(key) {\n  switch (key) {\n    case 'ArrowUp':\n      return 'top';\n\n    case 'ArrowRight':\n      return 'right';\n\n    case 'ArrowDown':\n      return 'bottom';\n\n    case 'ArrowLeft':\n    default:\n      return 'left';\n  }\n}\n\nfunction getInvertedPositionForKey(key) {\n  switch (key) {\n    case 'ArrowUp':\n      return 'bottom';\n\n    case 'ArrowRight':\n      return 'left';\n\n    case 'ArrowDown':\n      return 'top';\n\n    case 'ArrowLeft':\n    default:\n      return 'right';\n  }\n}\n\nfunction getEventForKey(key, stepSize) {\n  switch (key) {\n    case 'ArrowUp':\n      return {\n        clientX: 0,\n        clientY: stepSize * -1\n      };\n\n    case 'ArrowRight':\n      return {\n        clientX: stepSize,\n        clientY: 0\n      };\n\n    case 'ArrowDown':\n      return {\n        clientX: 0,\n        clientY: stepSize\n      };\n\n    case 'ArrowLeft':\n    default:\n      return {\n        clientX: stepSize * -1,\n        clientY: 0\n      };\n  }\n}\n/*\n * Hermite resize - fast image resize/resample using Hermite filter.\n * https://github.com/viliusle/Hermite-resize\n */\n\n\nfunction resizeCanvas(canvas, width, height) {\n  const width_source = canvas.width;\n  const height_source = canvas.height;\n  width = Math.round(width);\n  height = Math.round(height);\n  const ratio_w = width_source / width;\n  const ratio_h = height_source / height;\n  const ratio_w_half = Math.ceil(ratio_w / 2);\n  const ratio_h_half = Math.ceil(ratio_h / 2);\n  const ctx = canvas.getContext('2d');\n\n  if (ctx) {\n    const img = ctx.getImageData(0, 0, width_source, height_source);\n    const img2 = ctx.createImageData(width, height);\n    const data = img.data;\n    const data2 = img2.data;\n\n    for (let j = 0; j < height; j++) {\n      for (let i = 0; i < width; i++) {\n        const x2 = (i + j * width) * 4;\n        const center_y = j * ratio_h;\n        let weight = 0;\n        let weights = 0;\n        let weights_alpha = 0;\n        let gx_r = 0;\n        let gx_g = 0;\n        let gx_b = 0;\n        let gx_a = 0;\n        const xx_start = Math.floor(i * ratio_w);\n        const yy_start = Math.floor(j * ratio_h);\n        let xx_stop = Math.ceil((i + 1) * ratio_w);\n        let yy_stop = Math.ceil((j + 1) * ratio_h);\n        xx_stop = Math.min(xx_stop, width_source);\n        yy_stop = Math.min(yy_stop, height_source);\n\n        for (let yy = yy_start; yy < yy_stop; yy++) {\n          const dy = Math.abs(center_y - yy) / ratio_h_half;\n          const center_x = i * ratio_w;\n          const w0 = dy * dy; //pre-calc part of w\n\n          for (let xx = xx_start; xx < xx_stop; xx++) {\n            const dx = Math.abs(center_x - xx) / ratio_w_half;\n            const w = Math.sqrt(w0 + dx * dx);\n\n            if (w >= 1) {\n              //pixel too far\n              continue;\n            } //hermite filter\n\n\n            weight = 2 * w * w * w - 3 * w * w + 1;\n            const pos_x = 4 * (xx + yy * width_source); //alpha\n\n            gx_a += weight * data[pos_x + 3];\n            weights_alpha += weight; //colors\n\n            if (data[pos_x + 3] < 255) weight = weight * data[pos_x + 3] / 250;\n            gx_r += weight * data[pos_x];\n            gx_g += weight * data[pos_x + 1];\n            gx_b += weight * data[pos_x + 2];\n            weights += weight;\n          }\n        }\n\n        data2[x2] = gx_r / weights;\n        data2[x2 + 1] = gx_g / weights;\n        data2[x2 + 2] = gx_b / weights;\n        data2[x2 + 3] = gx_a / weights_alpha;\n      }\n    }\n\n    canvas.width = width;\n    canvas.height = height; //draw\n\n    ctx.putImageData(img2, 0, 0);\n  }\n}\n\nfunction percentage(percent, totalValue) {\n  return percent / 100 * totalValue;\n}\n\nclass CropService {\n  crop(sourceImage, loadedImage, cropper, settings) {\n    const imagePosition = this.getImagePosition(sourceImage, loadedImage, cropper, settings);\n    const width = imagePosition.x2 - imagePosition.x1;\n    const height = imagePosition.y2 - imagePosition.y1;\n    const cropCanvas = document.createElement('canvas');\n    cropCanvas.width = width;\n    cropCanvas.height = height;\n    const ctx = cropCanvas.getContext('2d');\n\n    if (!ctx) {\n      return null;\n    }\n\n    if (settings.backgroundColor != null) {\n      ctx.fillStyle = settings.backgroundColor;\n      ctx.fillRect(0, 0, width, height);\n    }\n\n    const scaleX = (settings.transform.scale || 1) * (settings.transform.flipH ? -1 : 1);\n    const scaleY = (settings.transform.scale || 1) * (settings.transform.flipV ? -1 : 1);\n    const transformedImage = loadedImage.transformed;\n    ctx.setTransform(scaleX, 0, 0, scaleY, transformedImage.size.width / 2, transformedImage.size.height / 2);\n    ctx.translate(-imagePosition.x1 / scaleX, -imagePosition.y1 / scaleY);\n    ctx.rotate((settings.transform.rotate || 0) * Math.PI / 180);\n    const translateH = settings.transform.translateH ? percentage(settings.transform.translateH, transformedImage.size.width) : 0;\n    const translateV = settings.transform.translateV ? percentage(settings.transform.translateV, transformedImage.size.height) : 0;\n    ctx.drawImage(transformedImage.image, translateH - transformedImage.size.width / 2, translateV - transformedImage.size.height / 2);\n    const output = {\n      width,\n      height,\n      imagePosition,\n      cropperPosition: { ...cropper\n      }\n    };\n\n    if (settings.containWithinAspectRatio) {\n      output.offsetImagePosition = this.getOffsetImagePosition(sourceImage, loadedImage, cropper, settings);\n    }\n\n    const resizeRatio = this.getResizeRatio(width, height, settings);\n\n    if (resizeRatio !== 1) {\n      output.width = Math.round(width * resizeRatio);\n      output.height = settings.maintainAspectRatio ? Math.round(output.width / settings.aspectRatio) : Math.round(height * resizeRatio);\n      resizeCanvas(cropCanvas, output.width, output.height);\n    }\n\n    output.base64 = cropCanvas.toDataURL('image/' + settings.format, this.getQuality(settings));\n    return output;\n  }\n\n  getImagePosition(sourceImage, loadedImage, cropper, settings) {\n    const sourceImageElement = sourceImage.nativeElement;\n    const ratio = loadedImage.transformed.size.width / sourceImageElement.offsetWidth;\n    const out = {\n      x1: Math.round(cropper.x1 * ratio),\n      y1: Math.round(cropper.y1 * ratio),\n      x2: Math.round(cropper.x2 * ratio),\n      y2: Math.round(cropper.y2 * ratio)\n    };\n\n    if (!settings.containWithinAspectRatio) {\n      out.x1 = Math.max(out.x1, 0);\n      out.y1 = Math.max(out.y1, 0);\n      out.x2 = Math.min(out.x2, loadedImage.transformed.size.width);\n      out.y2 = Math.min(out.y2, loadedImage.transformed.size.height);\n    }\n\n    return out;\n  }\n\n  getOffsetImagePosition(sourceImage, loadedImage, cropper, settings) {\n    const canvasRotation = settings.canvasRotation + loadedImage.exifTransform.rotate;\n    const sourceImageElement = sourceImage.nativeElement;\n    const ratio = loadedImage.transformed.size.width / sourceImageElement.offsetWidth;\n    let offsetX;\n    let offsetY;\n\n    if (canvasRotation % 2) {\n      offsetX = (loadedImage.transformed.size.width - loadedImage.original.size.height) / 2;\n      offsetY = (loadedImage.transformed.size.height - loadedImage.original.size.width) / 2;\n    } else {\n      offsetX = (loadedImage.transformed.size.width - loadedImage.original.size.width) / 2;\n      offsetY = (loadedImage.transformed.size.height - loadedImage.original.size.height) / 2;\n    }\n\n    const out = {\n      x1: Math.round(cropper.x1 * ratio) - offsetX,\n      y1: Math.round(cropper.y1 * ratio) - offsetY,\n      x2: Math.round(cropper.x2 * ratio) - offsetX,\n      y2: Math.round(cropper.y2 * ratio) - offsetY\n    };\n\n    if (!settings.containWithinAspectRatio) {\n      out.x1 = Math.max(out.x1, 0);\n      out.y1 = Math.max(out.y1, 0);\n      out.x2 = Math.min(out.x2, loadedImage.transformed.size.width);\n      out.y2 = Math.min(out.y2, loadedImage.transformed.size.height);\n    }\n\n    return out;\n  }\n\n  getResizeRatio(width, height, settings) {\n    const ratioWidth = settings.resizeToWidth / width;\n    const ratioHeight = settings.resizeToHeight / height;\n    const ratios = new Array();\n\n    if (settings.resizeToWidth > 0) {\n      ratios.push(ratioWidth);\n    }\n\n    if (settings.resizeToHeight > 0) {\n      ratios.push(ratioHeight);\n    }\n\n    const result = ratios.length === 0 ? 1 : Math.min(...ratios);\n\n    if (result > 1 && !settings.onlyScaleDown) {\n      return result;\n    }\n\n    return Math.min(result, 1);\n  }\n\n  getQuality(settings) {\n    return Math.min(1, Math.max(0, settings.imageQuality / 100));\n  }\n\n}\n\nCropService.ɵfac = function CropService_Factory(t) {\n  return new (t || CropService)();\n};\n\nCropService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CropService,\n  factory: CropService.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CropService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nclass CropperPositionService {\n  resetCropperPosition(sourceImage, cropperPosition, settings) {\n    if (!sourceImage?.nativeElement) {\n      return;\n    }\n\n    const sourceImageElement = sourceImage.nativeElement;\n\n    if (settings.cropperStaticHeight && settings.cropperStaticWidth) {\n      cropperPosition.x1 = 0;\n      cropperPosition.x2 = sourceImageElement.offsetWidth > settings.cropperStaticWidth ? settings.cropperStaticWidth : sourceImageElement.offsetWidth;\n      cropperPosition.y1 = 0;\n      cropperPosition.y2 = sourceImageElement.offsetHeight > settings.cropperStaticHeight ? settings.cropperStaticHeight : sourceImageElement.offsetHeight;\n    } else {\n      const cropperWidth = Math.min(settings.cropperScaledMaxWidth, sourceImageElement.offsetWidth);\n      const cropperHeight = Math.min(settings.cropperScaledMaxHeight, sourceImageElement.offsetHeight);\n\n      if (!settings.maintainAspectRatio) {\n        cropperPosition.x1 = 0;\n        cropperPosition.x2 = cropperWidth;\n        cropperPosition.y1 = 0;\n        cropperPosition.y2 = cropperHeight;\n      } else if (sourceImageElement.offsetWidth / settings.aspectRatio < sourceImageElement.offsetHeight) {\n        cropperPosition.x1 = 0;\n        cropperPosition.x2 = cropperWidth;\n        const cropperHeightWithAspectRatio = cropperWidth / settings.aspectRatio;\n        cropperPosition.y1 = (sourceImageElement.offsetHeight - cropperHeightWithAspectRatio) / 2;\n        cropperPosition.y2 = cropperPosition.y1 + cropperHeightWithAspectRatio;\n      } else {\n        cropperPosition.y1 = 0;\n        cropperPosition.y2 = cropperHeight;\n        const cropperWidthWithAspectRatio = cropperHeight * settings.aspectRatio;\n        cropperPosition.x1 = (sourceImageElement.offsetWidth - cropperWidthWithAspectRatio) / 2;\n        cropperPosition.x2 = cropperPosition.x1 + cropperWidthWithAspectRatio;\n      }\n    }\n  }\n\n  move(event, moveStart, cropperPosition) {\n    const diffX = this.getClientX(event) - moveStart.clientX;\n    const diffY = this.getClientY(event) - moveStart.clientY;\n    cropperPosition.x1 = moveStart.x1 + diffX;\n    cropperPosition.y1 = moveStart.y1 + diffY;\n    cropperPosition.x2 = moveStart.x2 + diffX;\n    cropperPosition.y2 = moveStart.y2 + diffY;\n  }\n\n  resize(event, moveStart, cropperPosition, maxSize, settings) {\n    const moveX = this.getClientX(event) - moveStart.clientX;\n    const moveY = this.getClientY(event) - moveStart.clientY;\n\n    switch (moveStart.position) {\n      case 'left':\n        cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n        break;\n\n      case 'topleft':\n        cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n        cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n        break;\n\n      case 'top':\n        cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n        break;\n\n      case 'topright':\n        cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n        cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n        break;\n\n      case 'right':\n        cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n        break;\n\n      case 'bottomright':\n        cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n        cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n        break;\n\n      case 'bottom':\n        cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n        break;\n\n      case 'bottomleft':\n        cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n        cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n        break;\n\n      case 'center':\n        const scale = event.scale;\n        const newWidth = Math.min(Math.max(settings.cropperScaledMinWidth, Math.abs(moveStart.x2 - moveStart.x1) * scale), settings.cropperScaledMaxWidth);\n        const newHeight = Math.min(Math.max(settings.cropperScaledMinHeight, Math.abs(moveStart.y2 - moveStart.y1) * scale), settings.cropperScaledMaxHeight);\n        cropperPosition.x1 = moveStart.clientX - newWidth / 2;\n        cropperPosition.x2 = moveStart.clientX + newWidth / 2;\n        cropperPosition.y1 = moveStart.clientY - newHeight / 2;\n        cropperPosition.y2 = moveStart.clientY + newHeight / 2;\n\n        if (cropperPosition.x1 < 0) {\n          cropperPosition.x2 -= cropperPosition.x1;\n          cropperPosition.x1 = 0;\n        } else if (cropperPosition.x2 > maxSize.width) {\n          cropperPosition.x1 -= cropperPosition.x2 - maxSize.width;\n          cropperPosition.x2 = maxSize.width;\n        }\n\n        if (cropperPosition.y1 < 0) {\n          cropperPosition.y2 -= cropperPosition.y1;\n          cropperPosition.y1 = 0;\n        } else if (cropperPosition.y2 > maxSize.height) {\n          cropperPosition.y1 -= cropperPosition.y2 - maxSize.height;\n          cropperPosition.y2 = maxSize.height;\n        }\n\n        break;\n    }\n\n    if (settings.maintainAspectRatio) {\n      this.checkAspectRatio(moveStart.position, cropperPosition, maxSize, settings);\n    }\n  }\n\n  checkAspectRatio(position, cropperPosition, maxSize, settings) {\n    let overflowX = 0;\n    let overflowY = 0;\n\n    switch (position) {\n      case 'top':\n        cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n        overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n        overflowY = Math.max(0 - cropperPosition.y1, 0);\n\n        if (overflowX > 0 || overflowY > 0) {\n          cropperPosition.x2 -= overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n          cropperPosition.y1 += overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n        }\n\n        break;\n\n      case 'bottom':\n        cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n        overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n        overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n\n        if (overflowX > 0 || overflowY > 0) {\n          cropperPosition.x2 -= overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n          cropperPosition.y2 -= overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n        }\n\n        break;\n\n      case 'topleft':\n        cropperPosition.y1 = cropperPosition.y2 - (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n        overflowX = Math.max(0 - cropperPosition.x1, 0);\n        overflowY = Math.max(0 - cropperPosition.y1, 0);\n\n        if (overflowX > 0 || overflowY > 0) {\n          cropperPosition.x1 += overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n          cropperPosition.y1 += overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n        }\n\n        break;\n\n      case 'topright':\n        cropperPosition.y1 = cropperPosition.y2 - (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n        overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n        overflowY = Math.max(0 - cropperPosition.y1, 0);\n\n        if (overflowX > 0 || overflowY > 0) {\n          cropperPosition.x2 -= overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n          cropperPosition.y1 += overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n        }\n\n        break;\n\n      case 'right':\n      case 'bottomright':\n        cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n        overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n        overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n\n        if (overflowX > 0 || overflowY > 0) {\n          cropperPosition.x2 -= overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n          cropperPosition.y2 -= overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n        }\n\n        break;\n\n      case 'left':\n      case 'bottomleft':\n        cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n        overflowX = Math.max(0 - cropperPosition.x1, 0);\n        overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n\n        if (overflowX > 0 || overflowY > 0) {\n          cropperPosition.x1 += overflowY * settings.aspectRatio > overflowX ? overflowY * settings.aspectRatio : overflowX;\n          cropperPosition.y2 -= overflowY * settings.aspectRatio > overflowX ? overflowY : overflowX / settings.aspectRatio;\n        }\n\n        break;\n\n      case 'center':\n        cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n        cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n        const overflowX1 = Math.max(0 - cropperPosition.x1, 0);\n        const overflowX2 = Math.max(cropperPosition.x2 - maxSize.width, 0);\n        const overflowY1 = Math.max(cropperPosition.y2 - maxSize.height, 0);\n        const overflowY2 = Math.max(0 - cropperPosition.y1, 0);\n\n        if (overflowX1 > 0 || overflowX2 > 0 || overflowY1 > 0 || overflowY2 > 0) {\n          cropperPosition.x1 += overflowY1 * settings.aspectRatio > overflowX1 ? overflowY1 * settings.aspectRatio : overflowX1;\n          cropperPosition.x2 -= overflowY2 * settings.aspectRatio > overflowX2 ? overflowY2 * settings.aspectRatio : overflowX2;\n          cropperPosition.y1 += overflowY2 * settings.aspectRatio > overflowX2 ? overflowY2 : overflowX2 / settings.aspectRatio;\n          cropperPosition.y2 -= overflowY1 * settings.aspectRatio > overflowX1 ? overflowY1 : overflowX1 / settings.aspectRatio;\n        }\n\n        break;\n    }\n  }\n\n  getClientX(event) {\n    return event.touches?.[0].clientX || event.clientX || 0;\n  }\n\n  getClientY(event) {\n    return event.touches?.[0].clientY || event.clientY || 0;\n  }\n\n}\n\nCropperPositionService.ɵfac = function CropperPositionService_Factory(t) {\n  return new (t || CropperPositionService)();\n};\n\nCropperPositionService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: CropperPositionService,\n  factory: CropperPositionService.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CropperPositionService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})(); // Black 2x1 JPEG, with the following meta information set:\n// - EXIF Orientation: 6 (Rotated 90° CCW)\n// Source: https://github.com/blueimp/JavaScript-Load-Image\n\n\nconst testAutoOrientationImageURL = 'data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAA' + 'AAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBA' + 'QEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQE' + 'BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/x' + 'ABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAA' + 'AAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==';\n\nfunction supportsAutomaticRotation() {\n  return new Promise(resolve => {\n    const img = new Image();\n\n    img.onload = () => {\n      // Check if browser supports automatic image orientation:\n      const supported = img.width === 1 && img.height === 2;\n      resolve(supported);\n    };\n\n    img.src = testAutoOrientationImageURL;\n  });\n}\n\nfunction getTransformationsFromExifData(exifRotationOrBase64Image) {\n  if (typeof exifRotationOrBase64Image === 'string') {\n    exifRotationOrBase64Image = getExifRotation(exifRotationOrBase64Image);\n  }\n\n  switch (exifRotationOrBase64Image) {\n    case 2:\n      return {\n        rotate: 0,\n        flip: true\n      };\n\n    case 3:\n      return {\n        rotate: 2,\n        flip: false\n      };\n\n    case 4:\n      return {\n        rotate: 2,\n        flip: true\n      };\n\n    case 5:\n      return {\n        rotate: 1,\n        flip: true\n      };\n\n    case 6:\n      return {\n        rotate: 1,\n        flip: false\n      };\n\n    case 7:\n      return {\n        rotate: 3,\n        flip: true\n      };\n\n    case 8:\n      return {\n        rotate: 3,\n        flip: false\n      };\n\n    default:\n      return {\n        rotate: 0,\n        flip: false\n      };\n  }\n}\n\nfunction getExifRotation(imageBase64) {\n  const view = new DataView(base64ToArrayBuffer(imageBase64));\n\n  if (view.getUint16(0, false) !== 0xFFD8) {\n    return -2;\n  }\n\n  const length = view.byteLength;\n  let offset = 2;\n\n  while (offset < length) {\n    if (view.getUint16(offset + 2, false) <= 8) return -1;\n    const marker = view.getUint16(offset, false);\n    offset += 2;\n\n    if (marker == 0xFFE1) {\n      if (view.getUint32(offset += 2, false) !== 0x45786966) {\n        return -1;\n      }\n\n      const little = view.getUint16(offset += 6, false) == 0x4949;\n      offset += view.getUint32(offset + 4, little);\n      const tags = view.getUint16(offset, little);\n      offset += 2;\n\n      for (let i = 0; i < tags; i++) {\n        if (view.getUint16(offset + i * 12, little) == 0x0112) {\n          return view.getUint16(offset + i * 12 + 8, little);\n        }\n      }\n    } else if ((marker & 0xFF00) !== 0xFF00) {\n      break;\n    } else {\n      offset += view.getUint16(offset, false);\n    }\n  }\n\n  return -1;\n}\n\nfunction base64ToArrayBuffer(imageBase64) {\n  imageBase64 = imageBase64.replace(/^data\\:([^\\;]+)\\;base64,/gmi, '');\n  const binaryString = atob(imageBase64);\n  const len = binaryString.length;\n  const bytes = new Uint8Array(len);\n\n  for (let i = 0; i < len; i++) {\n    bytes[i] = binaryString.charCodeAt(i);\n  }\n\n  return bytes.buffer;\n}\n\nclass LoadImageService {\n  constructor() {\n    this.autoRotateSupported = supportsAutomaticRotation();\n  }\n\n  loadImageFile(file, cropperSettings) {\n    return new Promise((resolve, reject) => {\n      const fileReader = new FileReader();\n\n      fileReader.onload = event => {\n        this.loadImage(event.target.result, file.type, cropperSettings).then(resolve).catch(reject);\n      };\n\n      fileReader.readAsDataURL(file);\n    });\n  }\n\n  loadImage(imageBase64, imageType, cropperSettings) {\n    if (!this.isValidImageType(imageType)) {\n      return Promise.reject(new Error('Invalid image type'));\n    }\n\n    return this.loadBase64Image(imageBase64, cropperSettings);\n  }\n\n  isValidImageType(type) {\n    return /image\\/(png|jpg|jpeg|bmp|gif|tiff|webp|x-icon|vnd.microsoft.icon)/.test(type);\n  }\n\n  loadImageFromURL(url, cropperSettings) {\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n\n      img.onerror = () => reject;\n\n      img.onload = () => {\n        const canvas = document.createElement('canvas');\n        const context = canvas.getContext('2d');\n        canvas.width = img.width;\n        canvas.height = img.height;\n        context?.drawImage(img, 0, 0);\n        this.loadBase64Image(canvas.toDataURL(), cropperSettings).then(resolve);\n      };\n\n      img.crossOrigin = 'anonymous';\n      img.src = url;\n    });\n  }\n\n  loadBase64Image(imageBase64, cropperSettings) {\n    return new Promise((resolve, reject) => {\n      const originalImage = new Image();\n\n      originalImage.onload = () => resolve({\n        originalImage,\n        originalBase64: imageBase64\n      });\n\n      originalImage.onerror = reject;\n      originalImage.src = imageBase64;\n    }).then(res => this.transformImageBase64(res, cropperSettings));\n  }\n\n  transformImageBase64(res, cropperSettings) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      const autoRotate = yield _this.autoRotateSupported;\n      const exifTransform = yield getTransformationsFromExifData(autoRotate ? -1 : res.originalBase64);\n\n      if (!res.originalImage || !res.originalImage.complete) {\n        return Promise.reject(new Error('No image loaded'));\n      }\n\n      const loadedImage = {\n        original: {\n          base64: res.originalBase64,\n          image: res.originalImage,\n          size: {\n            width: res.originalImage.naturalWidth,\n            height: res.originalImage.naturalHeight\n          }\n        },\n        exifTransform\n      };\n      return _this.transformLoadedImage(loadedImage, cropperSettings);\n    })();\n  }\n\n  transformLoadedImage(loadedImage, cropperSettings) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      const canvasRotation = cropperSettings.canvasRotation + loadedImage.exifTransform.rotate;\n      const originalSize = {\n        width: loadedImage.original.image.naturalWidth,\n        height: loadedImage.original.image.naturalHeight\n      };\n\n      if (canvasRotation === 0 && !loadedImage.exifTransform.flip && !cropperSettings.containWithinAspectRatio) {\n        return {\n          original: {\n            base64: loadedImage.original.base64,\n            image: loadedImage.original.image,\n            size: { ...originalSize\n            }\n          },\n          transformed: {\n            base64: loadedImage.original.base64,\n            image: loadedImage.original.image,\n            size: { ...originalSize\n            }\n          },\n          exifTransform: loadedImage.exifTransform\n        };\n      }\n\n      const transformedSize = _this2.getTransformedSize(originalSize, loadedImage.exifTransform, cropperSettings);\n\n      const canvas = document.createElement('canvas');\n      canvas.width = transformedSize.width;\n      canvas.height = transformedSize.height;\n      const ctx = canvas.getContext('2d');\n      ctx?.setTransform(loadedImage.exifTransform.flip ? -1 : 1, 0, 0, 1, canvas.width / 2, canvas.height / 2);\n      ctx?.rotate(Math.PI * (canvasRotation / 2));\n      ctx?.drawImage(loadedImage.original.image, -originalSize.width / 2, -originalSize.height / 2);\n      const transformedBase64 = canvas.toDataURL();\n      const transformedImage = yield _this2.loadImageFromBase64(transformedBase64);\n      return {\n        original: {\n          base64: loadedImage.original.base64,\n          image: loadedImage.original.image,\n          size: { ...originalSize\n          }\n        },\n        transformed: {\n          base64: transformedBase64,\n          image: transformedImage,\n          size: {\n            width: transformedImage.width,\n            height: transformedImage.height\n          }\n        },\n        exifTransform: loadedImage.exifTransform\n      };\n    })();\n  }\n\n  loadImageFromBase64(imageBase64) {\n    return new Promise((resolve, reject) => {\n      const image = new Image();\n\n      image.onload = () => resolve(image);\n\n      image.onerror = reject;\n      image.src = imageBase64;\n    });\n  }\n\n  getTransformedSize(originalSize, exifTransform, cropperSettings) {\n    const canvasRotation = cropperSettings.canvasRotation + exifTransform.rotate;\n\n    if (cropperSettings.containWithinAspectRatio) {\n      if (canvasRotation % 2) {\n        const minWidthToContain = originalSize.width * cropperSettings.aspectRatio;\n        const minHeightToContain = originalSize.height / cropperSettings.aspectRatio;\n        return {\n          width: Math.max(originalSize.height, minWidthToContain),\n          height: Math.max(originalSize.width, minHeightToContain)\n        };\n      } else {\n        const minWidthToContain = originalSize.height * cropperSettings.aspectRatio;\n        const minHeightToContain = originalSize.width / cropperSettings.aspectRatio;\n        return {\n          width: Math.max(originalSize.width, minWidthToContain),\n          height: Math.max(originalSize.height, minHeightToContain)\n        };\n      }\n    }\n\n    if (canvasRotation % 2) {\n      return {\n        height: originalSize.width,\n        width: originalSize.height\n      };\n    }\n\n    return {\n      width: originalSize.width,\n      height: originalSize.height\n    };\n  }\n\n}\n\nLoadImageService.ɵfac = function LoadImageService_Factory(t) {\n  return new (t || LoadImageService)();\n};\n\nLoadImageService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: LoadImageService,\n  factory: LoadImageService.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LoadImageService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nclass ImageCropperComponent {\n  constructor(cropService, cropperPositionService, loadImageService, sanitizer, cd) {\n    this.cropService = cropService;\n    this.cropperPositionService = cropperPositionService;\n    this.loadImageService = loadImageService;\n    this.sanitizer = sanitizer;\n    this.cd = cd;\n    this.Hammer = window?.['Hammer'] || null;\n    this.settings = new CropperSettings();\n    this.setImageMaxSizeRetries = 0;\n    this.marginLeft = '0px';\n    this.maxSize = {\n      width: 0,\n      height: 0\n    };\n    this.moveTypes = MoveTypes;\n    this.imageVisible = false;\n    this.format = this.settings.format;\n    this.transform = {};\n    this.maintainAspectRatio = this.settings.maintainAspectRatio;\n    this.aspectRatio = this.settings.aspectRatio;\n    this.resizeToWidth = this.settings.resizeToWidth;\n    this.resizeToHeight = this.settings.resizeToHeight;\n    this.cropperMinWidth = this.settings.cropperMinWidth;\n    this.cropperMinHeight = this.settings.cropperMinHeight;\n    this.cropperMaxHeight = this.settings.cropperMaxHeight;\n    this.cropperMaxWidth = this.settings.cropperMaxWidth;\n    this.cropperStaticWidth = this.settings.cropperStaticWidth;\n    this.cropperStaticHeight = this.settings.cropperStaticHeight;\n    this.canvasRotation = this.settings.canvasRotation;\n    this.initialStepSize = this.settings.initialStepSize;\n    this.roundCropper = this.settings.roundCropper;\n    this.onlyScaleDown = this.settings.onlyScaleDown;\n    this.imageQuality = this.settings.imageQuality;\n    this.autoCrop = this.settings.autoCrop;\n    this.backgroundColor = this.settings.backgroundColor;\n    this.containWithinAspectRatio = this.settings.containWithinAspectRatio;\n    this.hideResizeSquares = this.settings.hideResizeSquares;\n    this.cropper = {\n      x1: -100,\n      y1: -100,\n      x2: 10000,\n      y2: 10000\n    };\n    this.alignImage = this.settings.alignImage;\n    this.disabled = false;\n    this.imageCropped = new EventEmitter();\n    this.startCropImage = new EventEmitter();\n    this.imageLoaded = new EventEmitter();\n    this.cropperReady = new EventEmitter();\n    this.loadImageFailed = new EventEmitter();\n    this.reset();\n  }\n\n  ngOnChanges(changes) {\n    this.onChangesUpdateSettings(changes);\n    this.onChangesInputImage(changes);\n\n    if (this.loadedImage?.original.image.complete && (changes['containWithinAspectRatio'] || changes['canvasRotation'])) {\n      this.loadImageService.transformLoadedImage(this.loadedImage, this.settings).then(res => this.setLoadedImage(res)).catch(err => this.loadImageError(err));\n    }\n\n    if (changes['cropper'] || changes['maintainAspectRatio'] || changes['aspectRatio']) {\n      this.setMaxSize();\n      this.setCropperScaledMinSize();\n      this.setCropperScaledMaxSize();\n\n      if (this.maintainAspectRatio && (changes['maintainAspectRatio'] || changes['aspectRatio'])) {\n        this.resetCropperPosition();\n      } else if (changes['cropper']) {\n        this.checkCropperPosition(false);\n        this.doAutoCrop();\n      }\n\n      this.cd.markForCheck();\n    }\n\n    if (changes['transform']) {\n      this.transform = this.transform || {};\n      this.setCssTransform();\n      this.doAutoCrop();\n    }\n  }\n\n  onChangesUpdateSettings(changes) {\n    this.settings.setOptionsFromChanges(changes);\n\n    if (this.settings.cropperStaticHeight && this.settings.cropperStaticWidth) {\n      this.settings.setOptions({\n        hideResizeSquares: true,\n        cropperMinWidth: this.settings.cropperStaticWidth,\n        cropperMinHeight: this.settings.cropperStaticHeight,\n        cropperMaxHeight: this.settings.cropperStaticHeight,\n        cropperMaxWidth: this.settings.cropperStaticWidth,\n        maintainAspectRatio: false\n      });\n    }\n  }\n\n  onChangesInputImage(changes) {\n    if (changes['imageChangedEvent'] || changes['imageURL'] || changes['imageBase64'] || changes['imageFile']) {\n      this.reset();\n    }\n\n    if (changes['imageChangedEvent'] && this.isValidImageChangedEvent()) {\n      this.loadImageFile(this.imageChangedEvent.target.files[0]);\n    }\n\n    if (changes['imageURL'] && this.imageURL) {\n      this.loadImageFromURL(this.imageURL);\n    }\n\n    if (changes['imageBase64'] && this.imageBase64) {\n      this.loadBase64Image(this.imageBase64);\n    }\n\n    if (changes['imageFile'] && this.imageFile) {\n      this.loadImageFile(this.imageFile);\n    }\n  }\n\n  isValidImageChangedEvent() {\n    return this.imageChangedEvent?.target?.files?.length > 0;\n  }\n\n  setCssTransform() {\n    this.safeTransformStyle = this.sanitizer.bypassSecurityTrustStyle('scaleX(' + (this.transform.scale || 1) * (this.transform.flipH ? -1 : 1) + ')' + 'scaleY(' + (this.transform.scale || 1) * (this.transform.flipV ? -1 : 1) + ')' + 'rotate(' + (this.transform.rotate || 0) + 'deg)' + `translate(${this.transform.translateH || 0}%, ${this.transform.translateV || 0}%)`);\n  }\n\n  ngOnInit() {\n    this.settings.stepSize = this.initialStepSize;\n    this.activatePinchGesture();\n  }\n\n  reset() {\n    this.imageVisible = false;\n    this.loadedImage = undefined;\n    this.safeImgDataUrl = 'data:image/png;base64,iVBORw0KGg' + 'oAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQYV2NgAAIAAAU' + 'AAarVyFEAAAAASUVORK5CYII=';\n    this.moveStart = {\n      active: false,\n      type: null,\n      position: null,\n      x1: 0,\n      y1: 0,\n      x2: 0,\n      y2: 0,\n      clientX: 0,\n      clientY: 0\n    };\n    this.maxSize = {\n      width: 0,\n      height: 0\n    };\n    this.cropper.x1 = -100;\n    this.cropper.y1 = -100;\n    this.cropper.x2 = 10000;\n    this.cropper.y2 = 10000;\n  }\n\n  loadImageFile(file) {\n    this.loadImageService.loadImageFile(file, this.settings).then(res => this.setLoadedImage(res)).catch(err => this.loadImageError(err));\n  }\n\n  loadBase64Image(imageBase64) {\n    this.loadImageService.loadBase64Image(imageBase64, this.settings).then(res => this.setLoadedImage(res)).catch(err => this.loadImageError(err));\n  }\n\n  loadImageFromURL(url) {\n    this.loadImageService.loadImageFromURL(url, this.settings).then(res => this.setLoadedImage(res)).catch(err => this.loadImageError(err));\n  }\n\n  setLoadedImage(loadedImage) {\n    this.loadedImage = loadedImage;\n    this.safeImgDataUrl = this.sanitizer.bypassSecurityTrustResourceUrl(loadedImage.transformed.base64);\n    this.cd.markForCheck();\n  }\n\n  loadImageError(error) {\n    console.error(error);\n    this.loadImageFailed.emit();\n  }\n\n  imageLoadedInView() {\n    if (this.loadedImage != null) {\n      this.imageLoaded.emit(this.loadedImage);\n      this.setImageMaxSizeRetries = 0;\n      setTimeout(() => this.checkImageMaxSizeRecursively());\n    }\n  }\n\n  checkImageMaxSizeRecursively() {\n    if (this.setImageMaxSizeRetries > 40) {\n      this.loadImageFailed.emit();\n    } else if (this.sourceImageLoaded()) {\n      this.setMaxSize();\n      this.setCropperScaledMinSize();\n      this.setCropperScaledMaxSize();\n      this.resetCropperPosition();\n      this.cropperReady.emit({ ...this.maxSize\n      });\n      this.cd.markForCheck();\n    } else {\n      this.setImageMaxSizeRetries++;\n      setTimeout(() => this.checkImageMaxSizeRecursively(), 50);\n    }\n  }\n\n  sourceImageLoaded() {\n    return this.sourceImage?.nativeElement?.offsetWidth > 0;\n  }\n\n  onResize() {\n    if (!this.loadedImage) {\n      return;\n    }\n\n    this.resizeCropperPosition();\n    this.setMaxSize();\n    this.setCropperScaledMinSize();\n    this.setCropperScaledMaxSize();\n  }\n\n  activatePinchGesture() {\n    if (this.Hammer) {\n      const hammer = new this.Hammer(this.wrapper.nativeElement);\n      hammer.get('pinch').set({\n        enable: true\n      });\n      hammer.on('pinchmove', this.onPinch.bind(this));\n      hammer.on('pinchend', this.pinchStop.bind(this));\n      hammer.on('pinchstart', this.startPinch.bind(this));\n    } else if (isDevMode()) {\n      console.warn('[NgxImageCropper] Could not find HammerJS - Pinch Gesture won\\'t work');\n    }\n  }\n\n  resizeCropperPosition() {\n    const sourceImageElement = this.sourceImage.nativeElement;\n\n    if (this.maxSize.width !== sourceImageElement.offsetWidth || this.maxSize.height !== sourceImageElement.offsetHeight) {\n      this.cropper.x1 = this.cropper.x1 * sourceImageElement.offsetWidth / this.maxSize.width;\n      this.cropper.x2 = this.cropper.x2 * sourceImageElement.offsetWidth / this.maxSize.width;\n      this.cropper.y1 = this.cropper.y1 * sourceImageElement.offsetHeight / this.maxSize.height;\n      this.cropper.y2 = this.cropper.y2 * sourceImageElement.offsetHeight / this.maxSize.height;\n    }\n  }\n\n  resetCropperPosition() {\n    this.cropperPositionService.resetCropperPosition(this.sourceImage, this.cropper, this.settings);\n    this.doAutoCrop();\n    this.imageVisible = true;\n  }\n\n  keyboardAccess(event) {\n    this.changeKeyboardStepSize(event);\n    this.keyboardMoveCropper(event);\n  }\n\n  changeKeyboardStepSize(event) {\n    const key = +event.key;\n\n    if (key >= 1 && key <= 9) {\n      this.settings.stepSize = key;\n    }\n  }\n\n  keyboardMoveCropper(event) {\n    const keyboardWhiteList = ['ArrowUp', 'ArrowDown', 'ArrowRight', 'ArrowLeft'];\n\n    if (!keyboardWhiteList.includes(event.key)) {\n      return;\n    }\n\n    const moveType = event.shiftKey ? MoveTypes.Resize : MoveTypes.Move;\n    const position = event.altKey ? getInvertedPositionForKey(event.key) : getPositionForKey(event.key);\n    const moveEvent = getEventForKey(event.key, this.settings.stepSize);\n    event.preventDefault();\n    event.stopPropagation();\n    this.startMove({\n      clientX: 0,\n      clientY: 0\n    }, moveType, position);\n    this.moveImg(moveEvent);\n    this.moveStop();\n  }\n\n  startMove(event, moveType, position = null) {\n    if (this.moveStart?.active && this.moveStart?.type === MoveTypes.Pinch) {\n      return;\n    }\n\n    if (event.preventDefault) {\n      event.preventDefault();\n    }\n\n    this.moveStart = {\n      active: true,\n      type: moveType,\n      position,\n      clientX: this.cropperPositionService.getClientX(event),\n      clientY: this.cropperPositionService.getClientY(event),\n      ...this.cropper\n    };\n  }\n\n  startPinch(event) {\n    if (!this.safeImgDataUrl) {\n      return;\n    }\n\n    if (event.preventDefault) {\n      event.preventDefault();\n    }\n\n    this.moveStart = {\n      active: true,\n      type: MoveTypes.Pinch,\n      position: 'center',\n      clientX: this.cropper.x1 + (this.cropper.x2 - this.cropper.x1) / 2,\n      clientY: this.cropper.y1 + (this.cropper.y2 - this.cropper.y1) / 2,\n      ...this.cropper\n    };\n  }\n\n  moveImg(event) {\n    if (this.moveStart.active) {\n      if (event.stopPropagation) {\n        event.stopPropagation();\n      }\n\n      if (event.preventDefault) {\n        event.preventDefault();\n      }\n\n      if (this.moveStart.type === MoveTypes.Move) {\n        this.cropperPositionService.move(event, this.moveStart, this.cropper);\n        this.checkCropperPosition(true);\n      } else if (this.moveStart.type === MoveTypes.Resize) {\n        if (!this.cropperStaticWidth && !this.cropperStaticHeight) {\n          this.cropperPositionService.resize(event, this.moveStart, this.cropper, this.maxSize, this.settings);\n        }\n\n        this.checkCropperPosition(false);\n      }\n\n      this.cd.detectChanges();\n    }\n  }\n\n  onPinch(event) {\n    if (this.moveStart.active) {\n      if (event.stopPropagation) {\n        event.stopPropagation();\n      }\n\n      if (event.preventDefault) {\n        event.preventDefault();\n      }\n\n      if (this.moveStart.type === MoveTypes.Pinch) {\n        this.cropperPositionService.resize(event, this.moveStart, this.cropper, this.maxSize, this.settings);\n        this.checkCropperPosition(false);\n      }\n\n      this.cd.detectChanges();\n    }\n  }\n\n  setMaxSize() {\n    if (this.sourceImage) {\n      const sourceImageElement = this.sourceImage.nativeElement;\n      this.maxSize.width = sourceImageElement.offsetWidth;\n      this.maxSize.height = sourceImageElement.offsetHeight;\n      this.marginLeft = this.sanitizer.bypassSecurityTrustStyle('calc(50% - ' + this.maxSize.width / 2 + 'px)');\n    }\n  }\n\n  setCropperScaledMinSize() {\n    if (this.loadedImage?.transformed?.image) {\n      this.setCropperScaledMinWidth();\n      this.setCropperScaledMinHeight();\n    } else {\n      this.settings.cropperScaledMinWidth = 20;\n      this.settings.cropperScaledMinHeight = 20;\n    }\n  }\n\n  setCropperScaledMinWidth() {\n    this.settings.cropperScaledMinWidth = this.cropperMinWidth > 0 ? Math.max(20, this.cropperMinWidth / this.loadedImage.transformed.image.width * this.maxSize.width) : 20;\n  }\n\n  setCropperScaledMinHeight() {\n    if (this.maintainAspectRatio) {\n      this.settings.cropperScaledMinHeight = Math.max(20, this.settings.cropperScaledMinWidth / this.aspectRatio);\n    } else if (this.cropperMinHeight > 0) {\n      this.settings.cropperScaledMinHeight = Math.max(20, this.cropperMinHeight / this.loadedImage.transformed.image.height * this.maxSize.height);\n    } else {\n      this.settings.cropperScaledMinHeight = 20;\n    }\n  }\n\n  setCropperScaledMaxSize() {\n    if (this.loadedImage?.transformed?.image) {\n      const ratio = this.loadedImage.transformed.size.width / this.maxSize.width;\n      this.settings.cropperScaledMaxWidth = this.cropperMaxWidth > 20 ? this.cropperMaxWidth / ratio : this.maxSize.width;\n      this.settings.cropperScaledMaxHeight = this.cropperMaxHeight > 20 ? this.cropperMaxHeight / ratio : this.maxSize.height;\n\n      if (this.maintainAspectRatio) {\n        if (this.settings.cropperScaledMaxWidth > this.settings.cropperScaledMaxHeight * this.aspectRatio) {\n          this.settings.cropperScaledMaxWidth = this.settings.cropperScaledMaxHeight * this.aspectRatio;\n        } else if (this.settings.cropperScaledMaxWidth < this.settings.cropperScaledMaxHeight * this.aspectRatio) {\n          this.settings.cropperScaledMaxHeight = this.settings.cropperScaledMaxWidth / this.aspectRatio;\n        }\n      }\n    } else {\n      this.settings.cropperScaledMaxWidth = this.maxSize.width;\n      this.settings.cropperScaledMaxHeight = this.maxSize.height;\n    }\n  }\n\n  checkCropperPosition(maintainSize = false) {\n    if (this.cropper.x1 < 0) {\n      this.cropper.x2 -= maintainSize ? this.cropper.x1 : 0;\n      this.cropper.x1 = 0;\n    }\n\n    if (this.cropper.y1 < 0) {\n      this.cropper.y2 -= maintainSize ? this.cropper.y1 : 0;\n      this.cropper.y1 = 0;\n    }\n\n    if (this.cropper.x2 > this.maxSize.width) {\n      this.cropper.x1 -= maintainSize ? this.cropper.x2 - this.maxSize.width : 0;\n      this.cropper.x2 = this.maxSize.width;\n    }\n\n    if (this.cropper.y2 > this.maxSize.height) {\n      this.cropper.y1 -= maintainSize ? this.cropper.y2 - this.maxSize.height : 0;\n      this.cropper.y2 = this.maxSize.height;\n    }\n  }\n\n  moveStop() {\n    if (this.moveStart.active) {\n      this.moveStart.active = false;\n      this.doAutoCrop();\n    }\n  }\n\n  pinchStop() {\n    if (this.moveStart.active) {\n      this.moveStart.active = false;\n      this.doAutoCrop();\n    }\n  }\n\n  doAutoCrop() {\n    if (this.autoCrop) {\n      this.crop();\n    }\n  }\n\n  crop() {\n    if (this.loadedImage?.transformed?.image != null) {\n      this.startCropImage.emit();\n      const output = this.cropService.crop(this.sourceImage, this.loadedImage, this.cropper, this.settings);\n\n      if (output != null) {\n        this.imageCropped.emit(output);\n      }\n\n      return output;\n    }\n\n    return null;\n  }\n\n}\n\nImageCropperComponent.ɵfac = function ImageCropperComponent_Factory(t) {\n  return new (t || ImageCropperComponent)(i0.ɵɵdirectiveInject(CropService), i0.ɵɵdirectiveInject(CropperPositionService), i0.ɵɵdirectiveInject(LoadImageService), i0.ɵɵdirectiveInject(i4.DomSanitizer), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nImageCropperComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ImageCropperComponent,\n  selectors: [[\"image-cropper\"]],\n  viewQuery: function ImageCropperComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.wrapper = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sourceImage = _t.first);\n    }\n  },\n  hostVars: 4,\n  hostBindings: function ImageCropperComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"resize\", function ImageCropperComponent_resize_HostBindingHandler() {\n        return ctx.onResize();\n      }, false, i0.ɵɵresolveWindow)(\"mousemove\", function ImageCropperComponent_mousemove_HostBindingHandler($event) {\n        return ctx.moveImg($event);\n      }, false, i0.ɵɵresolveDocument)(\"touchmove\", function ImageCropperComponent_touchmove_HostBindingHandler($event) {\n        return ctx.moveImg($event);\n      }, false, i0.ɵɵresolveDocument)(\"mouseup\", function ImageCropperComponent_mouseup_HostBindingHandler() {\n        return ctx.moveStop();\n      }, false, i0.ɵɵresolveDocument)(\"touchend\", function ImageCropperComponent_touchend_HostBindingHandler() {\n        return ctx.moveStop();\n      }, false, i0.ɵɵresolveDocument);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"text-align\", ctx.alignImage);\n      i0.ɵɵclassProp(\"disabled\", ctx.disabled);\n    }\n  },\n  inputs: {\n    imageChangedEvent: \"imageChangedEvent\",\n    imageURL: \"imageURL\",\n    imageBase64: \"imageBase64\",\n    imageFile: \"imageFile\",\n    format: \"format\",\n    transform: \"transform\",\n    maintainAspectRatio: \"maintainAspectRatio\",\n    aspectRatio: \"aspectRatio\",\n    resizeToWidth: \"resizeToWidth\",\n    resizeToHeight: \"resizeToHeight\",\n    cropperMinWidth: \"cropperMinWidth\",\n    cropperMinHeight: \"cropperMinHeight\",\n    cropperMaxHeight: \"cropperMaxHeight\",\n    cropperMaxWidth: \"cropperMaxWidth\",\n    cropperStaticWidth: \"cropperStaticWidth\",\n    cropperStaticHeight: \"cropperStaticHeight\",\n    canvasRotation: \"canvasRotation\",\n    initialStepSize: \"initialStepSize\",\n    roundCropper: \"roundCropper\",\n    onlyScaleDown: \"onlyScaleDown\",\n    imageQuality: \"imageQuality\",\n    autoCrop: \"autoCrop\",\n    backgroundColor: \"backgroundColor\",\n    containWithinAspectRatio: \"containWithinAspectRatio\",\n    hideResizeSquares: \"hideResizeSquares\",\n    cropper: \"cropper\",\n    alignImage: \"alignImage\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    imageCropped: \"imageCropped\",\n    startCropImage: \"startCropImage\",\n    imageLoaded: \"imageLoaded\",\n    cropperReady: \"cropperReady\",\n    loadImageFailed: \"loadImageFailed\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 5,\n  vars: 10,\n  consts: [[\"wrapper\", \"\"], [\"class\", \"ngx-ic-source-image\", 3, \"src\", \"visibility\", \"transform\", \"load\", 4, \"ngIf\"], [1, \"ngx-ic-overlay\"], [\"class\", \"ngx-ic-cropper\", \"tabindex\", \"0\", 3, \"ngx-ic-round\", \"top\", \"left\", \"width\", \"height\", \"margin-left\", \"visibility\", \"keydown\", 4, \"ngIf\"], [1, \"ngx-ic-source-image\", 3, \"src\", \"load\"], [\"sourceImage\", \"\"], [\"tabindex\", \"0\", 1, \"ngx-ic-cropper\", 3, \"keydown\"], [1, \"ngx-ic-move\", 3, \"mousedown\", \"touchstart\"], [4, \"ngIf\"], [1, \"ngx-ic-resize\", \"ngx-ic-topleft\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-square\"], [1, \"ngx-ic-resize\", \"ngx-ic-top\"], [1, \"ngx-ic-resize\", \"ngx-ic-topright\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-resize\", \"ngx-ic-right\"], [1, \"ngx-ic-resize\", \"ngx-ic-bottomright\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-resize\", \"ngx-ic-bottom\"], [1, \"ngx-ic-resize\", \"ngx-ic-bottomleft\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-resize\", \"ngx-ic-left\"], [1, \"ngx-ic-resize-bar\", \"ngx-ic-top\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-resize-bar\", \"ngx-ic-right\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-resize-bar\", \"ngx-ic-bottom\", 3, \"mousedown\", \"touchstart\"], [1, \"ngx-ic-resize-bar\", \"ngx-ic-left\", 3, \"mousedown\", \"touchstart\"]],\n  template: function ImageCropperComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", null, 0);\n      i0.ɵɵtemplate(2, ImageCropperComponent_img_2_Template, 2, 5, \"img\", 1);\n      i0.ɵɵelement(3, \"div\", 2);\n      i0.ɵɵtemplate(4, ImageCropperComponent_div_4_Template, 3, 15, \"div\", 3);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"background\", ctx.imageVisible && ctx.backgroundColor);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.safeImgDataUrl);\n      i0.ɵɵadvance(1);\n      i0.ɵɵstyleProp(\"width\", ctx.maxSize.width, \"px\")(\"height\", ctx.maxSize.height, \"px\")(\"margin-left\", ctx.alignImage === \"center\" ? ctx.marginLeft : null);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.imageVisible);\n    }\n  },\n  directives: [i5.NgIf],\n  styles: [\"[_nghost-%COMP%]{display:flex;position:relative;width:100%;max-width:100%;max-height:100%;overflow:hidden;padding:5px;text-align:center}[_nghost-%COMP%] > div[_ngcontent-%COMP%]{width:100%;position:relative}[_nghost-%COMP%] > div[_ngcontent-%COMP%]   img.ngx-ic-source-image[_ngcontent-%COMP%]{max-width:100%;max-height:100%;transform-origin:center}[_nghost-%COMP%]   .ngx-ic-overlay[_ngcontent-%COMP%]{position:absolute;pointer-events:none;touch-action:none;outline:var(--cropper-overlay-color, white) solid 100vw;top:0;left:0}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]{position:absolute;display:flex;color:#53535c;background:transparent;outline:rgba(255,255,255,.3) solid 100vw;outline:var(--cropper-outline-color, rgba(255, 255, 255, .3)) solid 100vw;touch-action:none}@media (orientation: portrait){[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]{outline-width:100vh}}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]:after{position:absolute;content:\\\"\\\";top:0;bottom:0;left:0;right:0;pointer-events:none;border:dashed 1px;opacity:.75;color:inherit;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{width:100%;cursor:move;border:1px solid rgba(255,255,255,.5)}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]:focus   .ngx-ic-move[_ngcontent-%COMP%]{border-color:#1e90ff;border-width:2px}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%]{position:absolute;display:inline-block;line-height:6px;padding:8px;opacity:.85;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%]   .ngx-ic-square[_ngcontent-%COMP%]{display:inline-block;background:#53535C;width:6px;height:6px;border:1px solid rgba(255,255,255,.5);box-sizing:content-box}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-topleft[_ngcontent-%COMP%]{top:-12px;left:-12px;cursor:nwse-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-top[_ngcontent-%COMP%]{top:-12px;left:calc(50% - 12px);cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-topright[_ngcontent-%COMP%]{top:-12px;right:-12px;cursor:nesw-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-right[_ngcontent-%COMP%]{top:calc(50% - 12px);right:-12px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottomright[_ngcontent-%COMP%]{bottom:-12px;right:-12px;cursor:nwse-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottom[_ngcontent-%COMP%]{bottom:-12px;left:calc(50% - 12px);cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottomleft[_ngcontent-%COMP%]{bottom:-12px;left:-12px;cursor:nesw-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-left[_ngcontent-%COMP%]{top:calc(50% - 12px);left:-12px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar[_ngcontent-%COMP%]{position:absolute;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-top[_ngcontent-%COMP%]{top:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-right[_ngcontent-%COMP%]{top:11px;right:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-bottom[_ngcontent-%COMP%]{bottom:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-left[_ngcontent-%COMP%]{top:11px;left:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]{outline-color:transparent}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]:after{border-radius:100%;box-shadow:0 0 0 100vw #ffffff4d;box-shadow:0 0 0 100vw var(--cropper-outline-color, rgba(255, 255, 255, .3))}@media (orientation: portrait){[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]:after{box-shadow:0 0 0 100vh #ffffff4d;box-shadow:0 0 0 100vh var(--cropper-outline-color, rgba(255, 255, 255, .3))}}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{border-radius:100%}.disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%], .disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar[_ngcontent-%COMP%], .disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{display:none}\"],\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ImageCropperComponent, [{\n    type: Component,\n    args: [{\n      selector: 'image-cropper',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div [style.background]=\\\"imageVisible && backgroundColor\\\"\\n     #wrapper\\n>\\n    <img\\n      #sourceImage\\n      class=\\\"ngx-ic-source-image\\\"\\n      *ngIf=\\\"safeImgDataUrl\\\"\\n      [src]=\\\"safeImgDataUrl\\\"\\n      [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n      [style.transform]=\\\"safeTransformStyle\\\"\\n      (load)=\\\"imageLoadedInView()\\\"\\n    />\\n    <div\\n        class=\\\"ngx-ic-overlay\\\"\\n        [style.width.px]=\\\"maxSize.width\\\"\\n        [style.height.px]=\\\"maxSize.height\\\"\\n        [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n    ></div>\\n    <div class=\\\"ngx-ic-cropper\\\"\\n         *ngIf=\\\"imageVisible\\\"\\n         [class.ngx-ic-round]=\\\"roundCropper\\\"\\n         [style.top.px]=\\\"cropper.y1\\\"\\n         [style.left.px]=\\\"cropper.x1\\\"\\n         [style.width.px]=\\\"cropper.x2 - cropper.x1\\\"\\n         [style.height.px]=\\\"cropper.y2 - cropper.y1\\\"\\n         [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n         [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n         (keydown)=\\\"keyboardAccess($event)\\\"\\n         tabindex=\\\"0\\\"\\n    >\\n        <div\\n            (mousedown)=\\\"startMove($event, moveTypes.Move)\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Move)\\\"\\n            class=\\\"ngx-ic-move\\\">\\n        </div>\\n        <ng-container *ngIf=\\\"!hideResizeSquares\\\">\\n            <span class=\\\"ngx-ic-resize ngx-ic-topleft\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-top\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-topright\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-right\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-bottomright\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-bottom\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-bottomleft\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-left\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-top\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'top')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'top')\\\">\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-right\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'right')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'right')\\\">\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-bottom\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\">\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-left\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'left')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'left')\\\">\\n            </span>\\n        </ng-container>\\n    </div>\\n</div>\\n\",\n      styles: [\":host{display:flex;position:relative;width:100%;max-width:100%;max-height:100%;overflow:hidden;padding:5px;text-align:center}:host>div{width:100%;position:relative}:host>div img.ngx-ic-source-image{max-width:100%;max-height:100%;transform-origin:center}:host .ngx-ic-overlay{position:absolute;pointer-events:none;touch-action:none;outline:var(--cropper-overlay-color, white) solid 100vw;top:0;left:0}:host .ngx-ic-cropper{position:absolute;display:flex;color:#53535c;background:transparent;outline:rgba(255,255,255,.3) solid 100vw;outline:var(--cropper-outline-color, rgba(255, 255, 255, .3)) solid 100vw;touch-action:none}@media (orientation: portrait){:host .ngx-ic-cropper{outline-width:100vh}}:host .ngx-ic-cropper:after{position:absolute;content:\\\"\\\";top:0;bottom:0;left:0;right:0;pointer-events:none;border:dashed 1px;opacity:.75;color:inherit;z-index:1}:host .ngx-ic-cropper .ngx-ic-move{width:100%;cursor:move;border:1px solid rgba(255,255,255,.5)}:host .ngx-ic-cropper:focus .ngx-ic-move{border-color:#1e90ff;border-width:2px}:host .ngx-ic-cropper .ngx-ic-resize{position:absolute;display:inline-block;line-height:6px;padding:8px;opacity:.85;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize .ngx-ic-square{display:inline-block;background:#53535C;width:6px;height:6px;border:1px solid rgba(255,255,255,.5);box-sizing:content-box}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topleft{top:-12px;left:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-top{top:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topright{top:-12px;right:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-right{top:calc(50% - 12px);right:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomright{bottom:-12px;right:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottom{bottom:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomleft{bottom:-12px;left:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-left{top:calc(50% - 12px);left:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar{position:absolute;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-top{top:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-right{top:11px;right:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-bottom{bottom:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-left{top:11px;left:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper.ngx-ic-round{outline-color:transparent}:host .ngx-ic-cropper.ngx-ic-round:after{border-radius:100%;box-shadow:0 0 0 100vw #ffffff4d;box-shadow:0 0 0 100vw var(--cropper-outline-color, rgba(255, 255, 255, .3))}@media (orientation: portrait){:host .ngx-ic-cropper.ngx-ic-round:after{box-shadow:0 0 0 100vh #ffffff4d;box-shadow:0 0 0 100vh var(--cropper-outline-color, rgba(255, 255, 255, .3))}}:host .ngx-ic-cropper.ngx-ic-round .ngx-ic-move{border-radius:100%}:host.disabled .ngx-ic-cropper .ngx-ic-resize,:host.disabled .ngx-ic-cropper .ngx-ic-resize-bar,:host.disabled .ngx-ic-cropper .ngx-ic-move{display:none}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: CropService\n    }, {\n      type: CropperPositionService\n    }, {\n      type: LoadImageService\n    }, {\n      type: i4.DomSanitizer\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    wrapper: [{\n      type: ViewChild,\n      args: ['wrapper', {\n        static: true\n      }]\n    }],\n    sourceImage: [{\n      type: ViewChild,\n      args: ['sourceImage', {\n        static: false\n      }]\n    }],\n    imageChangedEvent: [{\n      type: Input\n    }],\n    imageURL: [{\n      type: Input\n    }],\n    imageBase64: [{\n      type: Input\n    }],\n    imageFile: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    transform: [{\n      type: Input\n    }],\n    maintainAspectRatio: [{\n      type: Input\n    }],\n    aspectRatio: [{\n      type: Input\n    }],\n    resizeToWidth: [{\n      type: Input\n    }],\n    resizeToHeight: [{\n      type: Input\n    }],\n    cropperMinWidth: [{\n      type: Input\n    }],\n    cropperMinHeight: [{\n      type: Input\n    }],\n    cropperMaxHeight: [{\n      type: Input\n    }],\n    cropperMaxWidth: [{\n      type: Input\n    }],\n    cropperStaticWidth: [{\n      type: Input\n    }],\n    cropperStaticHeight: [{\n      type: Input\n    }],\n    canvasRotation: [{\n      type: Input\n    }],\n    initialStepSize: [{\n      type: Input\n    }],\n    roundCropper: [{\n      type: Input\n    }],\n    onlyScaleDown: [{\n      type: Input\n    }],\n    imageQuality: [{\n      type: Input\n    }],\n    autoCrop: [{\n      type: Input\n    }],\n    backgroundColor: [{\n      type: Input\n    }],\n    containWithinAspectRatio: [{\n      type: Input\n    }],\n    hideResizeSquares: [{\n      type: Input\n    }],\n    cropper: [{\n      type: Input\n    }],\n    alignImage: [{\n      type: HostBinding,\n      args: ['style.text-align']\n    }, {\n      type: Input\n    }],\n    disabled: [{\n      type: HostBinding,\n      args: ['class.disabled']\n    }, {\n      type: Input\n    }],\n    imageCropped: [{\n      type: Output\n    }],\n    startCropImage: [{\n      type: Output\n    }],\n    imageLoaded: [{\n      type: Output\n    }],\n    cropperReady: [{\n      type: Output\n    }],\n    loadImageFailed: [{\n      type: Output\n    }],\n    onResize: [{\n      type: HostListener,\n      args: ['window:resize']\n    }],\n    moveImg: [{\n      type: HostListener,\n      args: ['document:mousemove', ['$event']]\n    }, {\n      type: HostListener,\n      args: ['document:touchmove', ['$event']]\n    }],\n    moveStop: [{\n      type: HostListener,\n      args: ['document:mouseup']\n    }, {\n      type: HostListener,\n      args: ['document:touchend']\n    }]\n  });\n})();\n\nclass ImageCropperModule {}\n\nImageCropperModule.ɵfac = function ImageCropperModule_Factory(t) {\n  return new (t || ImageCropperModule)();\n};\n\nImageCropperModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ImageCropperModule\n});\nImageCropperModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ImageCropperModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [ImageCropperComponent],\n      exports: [ImageCropperComponent]\n    }]\n  }], null, null);\n})();\n\nfunction base64ToFile(base64Image) {\n  const split = base64Image.split(',');\n  const type = split[0].replace('data:', '').replace(';base64', '');\n  const byteString = atob(split[1]);\n  const ab = new ArrayBuffer(byteString.length);\n  const ia = new Uint8Array(ab);\n\n  for (let i = 0; i < byteString.length; i += 1) {\n    ia[i] = byteString.charCodeAt(i);\n  }\n\n  return new Blob([ab], {\n    type\n  });\n}\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ImageCropperComponent, ImageCropperModule, base64ToFile, resizeCanvas };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/ngx-image-cropper/fesm2020/ngx-image-cropper.mjs"], "names": ["i0", "Injectable", "EventEmitter", "isDevMode", "Component", "ChangeDetectionStrategy", "ViewChild", "Input", "HostBinding", "Output", "HostListener", "NgModule", "i4", "i5", "CommonModule", "CropperSettings", "constructor", "format", "maintainAspectRatio", "transform", "aspectRatio", "resizeToWidth", "resizeToHeight", "cropper<PERSON><PERSON><PERSON><PERSON><PERSON>", "cropperMinHeight", "cropperMaxHeight", "cropperMaxWidth", "cropperStaticWidth", "cropperStaticHeight", "canvasRotation", "initialStepSize", "roundCropper", "onlyScaleDown", "imageQuality", "autoCrop", "backgroundColor", "containWithinAspectRatio", "hideResizeSquares", "alignImage", "cropperScaledMinWidth", "cropperScaledMinHeight", "cropperScaledMaxWidth", "cropperScaledMaxHeight", "stepSize", "setOptions", "options", "Object", "keys", "filter", "k", "for<PERSON>ach", "validateOptions", "setOptionsFromChanges", "changes", "currentValue", "Error", "MoveTypes", "getPositionForKey", "key", "getInvertedPositionForKey", "getEventForKey", "clientX", "clientY", "resizeCanvas", "canvas", "width", "height", "width_source", "height_source", "Math", "round", "ratio_w", "ratio_h", "ratio_w_half", "ceil", "ratio_h_half", "ctx", "getContext", "img", "getImageData", "img2", "createImageData", "data", "data2", "j", "i", "x2", "center_y", "weight", "weights", "weights_alpha", "gx_r", "gx_g", "gx_b", "gx_a", "xx_start", "floor", "yy_start", "xx_stop", "yy_stop", "min", "yy", "dy", "abs", "center_x", "w0", "xx", "dx", "w", "sqrt", "pos_x", "putImageData", "percentage", "percent", "totalValue", "CropService", "crop", "sourceImage", "loadedImage", "cropper", "settings", "imagePosition", "getImagePosition", "x1", "y2", "y1", "cropCanvas", "document", "createElement", "fillStyle", "fillRect", "scaleX", "scale", "flipH", "scaleY", "flipV", "transformedImage", "transformed", "setTransform", "size", "translate", "rotate", "PI", "translateH", "translateV", "drawImage", "image", "output", "cropperPosition", "offsetImagePosition", "getOffsetImagePosition", "resizeRatio", "getResizeRatio", "base64", "toDataURL", "getQuality", "sourceImageElement", "nativeElement", "ratio", "offsetWidth", "out", "max", "exifTransform", "offsetX", "offsetY", "original", "ratioWidth", "ratioHeight", "ratios", "Array", "push", "result", "length", "ɵfac", "ɵprov", "type", "args", "providedIn", "CropperPositionService", "resetCropperPosition", "offsetHeight", "cropper<PERSON>idth", "cropperHeight", "cropperHeightWithAspectRatio", "cropperWidthWithAspectRatio", "move", "event", "moveStart", "diffX", "getClientX", "diffY", "getClientY", "resize", "maxSize", "moveX", "moveY", "position", "newWidth", "newHeight", "checkAspectRatio", "overflowX", "overflowY", "overflowX1", "overflowX2", "overflowY1", "overflowY2", "touches", "testAutoOrientationImageURL", "supportsAutomaticRotation", "Promise", "resolve", "Image", "onload", "supported", "src", "getTransformationsFromExifData", "exifRotationOrBase64Image", "getExifRotation", "flip", "imageBase64", "view", "DataView", "base64ToArrayBuffer", "getUint16", "byteLength", "offset", "marker", "getUint32", "little", "tags", "replace", "binaryString", "atob", "len", "bytes", "Uint8Array", "charCodeAt", "buffer", "LoadImageService", "autoRotateSupported", "loadImageFile", "file", "cropperSettings", "reject", "fileReader", "FileReader", "loadImage", "target", "then", "catch", "readAsDataURL", "imageType", "isValidImageType", "loadBase64Image", "test", "loadImageFromURL", "url", "onerror", "context", "crossOrigin", "originalImage", "originalBase64", "res", "transformImageBase64", "autoRotate", "complete", "naturalWidth", "naturalHeight", "transformLoadedImage", "originalSize", "transformedSize", "getTransformedSize", "transformedBase64", "loadImageFromBase64", "minWidthToContain", "minHeightToContain", "ImageCropperComponent", "cropService", "cropperPositionService", "loadImageService", "sanitizer", "cd", "Hammer", "window", "setImageMaxSizeRetries", "marginLeft", "moveTypes", "imageVisible", "disabled", "imageCropped", "startCropImage", "imageLoaded", "cropperReady", "loadImageFailed", "reset", "ngOnChanges", "onChangesUpdateSettings", "onChangesInputImage", "setLoadedImage", "err", "loadImageError", "setMaxSize", "setCropperScaledMinSize", "setCropperScaledMaxSize", "checkCropperPosition", "doAutoCrop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setCssTransform", "isValidImageChangedEvent", "imageChangedEvent", "files", "imageURL", "imageFile", "safeTransformStyle", "bypassSecurityTrustStyle", "ngOnInit", "activatePinchGesture", "undefined", "safeImgDataUrl", "active", "bypassSecurityTrustResourceUrl", "error", "console", "emit", "imageLoadedInView", "setTimeout", "checkImageMaxSizeRecursively", "sourceImageLoaded", "onResize", "resizeCropperPosition", "hammer", "wrapper", "get", "set", "enable", "on", "onPinch", "bind", "pinchStop", "startPinch", "warn", "keyboardAccess", "changeKeyboardStepSize", "keyboardMoveCropper", "keyboardWhiteList", "includes", "moveType", "shift<PERSON>ey", "Resize", "Move", "altKey", "moveEvent", "preventDefault", "stopPropagation", "startMove", "moveImg", "moveStop", "Pinch", "detectChanges", "setCropperScaledMinWidth", "setCropperScaledMinHeight", "maintainSize", "Dom<PERSON><PERSON><PERSON>zer", "ChangeDetectorRef", "ɵcmp", "NgIf", "selector", "changeDetection", "OnPush", "template", "styles", "static", "ImageCropperModule", "ɵmod", "ɵinj", "imports", "declarations", "exports", "base64ToFile", "base64Image", "split", "byteString", "ab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ia", "Blob"], "mappings": ";AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,SAA9C,EAAyDC,uBAAzD,EAAkFC,SAAlF,EAA6FC,KAA7F,EAAoGC,WAApG,EAAiHC,MAAjH,EAAyHC,YAAzH,EAAuIC,QAAvI,QAAuJ,eAAvJ;AACA,OAAO,KAAKC,EAAZ,MAAoB,2BAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;;;;;gBAgS8Fd,E;;AAAAA,IAAAA,EA43BwvD,+B;AA53BxvDA,IAAAA,EA43B++D;AA53B/+DA,MAAAA,EA43B++D;AAAA,qBA53B/+DA,EA43B++D;AAAA,aAAS,0BAAT;AAAA,M;AA53B/+DA,IAAAA,EA43BwvD,e;;;;mBA53BxvDA,E;AAAAA,IAAAA,EA43B63D,8G;AA53B73DA,IAAAA,EA43B61D,0CA53B71DA,EA43B61D,e;;;;;;gBA53B71DA,E;;AAAAA,IAAAA,EA43Bs9F,2B;AA53Bt9FA,IAAAA,EA43B+gG,6B;AA53B/gGA,IAAAA,EA43B+kG;AA53B/kGA,MAAAA,EA43B+kG;AAAA,qBA53B/kGA,EA43B+kG;AAAA,aAAc,kDAAoC,SAApC,CAAd;AAAA;AA53B/kGA,MAAAA,EA43B+kG;AAAA,qBA53B/kGA,EA43B+kG;AAAA,aAAiG,kDAAoC,SAApC,CAAjG;AAAA,M;AA53B/kGA,IAAAA,EA43BmvG,yB;AA53BnvGA,IAAAA,EA43BsyG,e;AA53BtyGA,IAAAA,EA43B2zG,8B;AA53B3zGA,IAAAA,EA43Bs3G,yB;AA53Bt3GA,IAAAA,EA43By6G,e;AA53Bz6GA,IAAAA,EA43B87G,8B;AA53B97GA,IAAAA,EA43B+/G;AA53B//GA,MAAAA,EA43B+/G;AAAA,sBA53B//GA,EA43B+/G;AAAA,aAAc,oDAAoC,UAApC,CAAd;AAAA;AA53B//GA,MAAAA,EA43B+/G;AAAA,sBA53B//GA,EA43B+/G;AAAA,aAAkG,oDAAoC,UAApC,CAAlG;AAAA,M;AA53B//GA,IAAAA,EA43BqqH,yB;AA53BrqHA,IAAAA,EA43BwtH,e;AA53BxtHA,IAAAA,EA43B6uH,8B;AA53B7uHA,IAAAA,EA43B0yH,yB;AA53B1yHA,IAAAA,EA43B61H,e;AA53B71HA,IAAAA,EA43Bk3H,8B;AA53Bl3HA,IAAAA,EA43Bs7H;AA53Bt7HA,MAAAA,EA43Bs7H;AAAA,sBA53Bt7HA,EA43Bs7H;AAAA,aAAc,oDAAoC,aAApC,CAAd;AAAA;AA53Bt7HA,MAAAA,EA43Bs7H;AAAA,sBA53Bt7HA,EA43Bs7H;AAAA,aAAqG,oDAAoC,aAApC,CAArG;AAAA,M;AA53Bt7HA,IAAAA,EA43BkmI,0B;AA53BlmIA,IAAAA,EA43BqpI,e;AA53BrpIA,IAAAA,EA43B0qI,+B;AA53B1qIA,IAAAA,EA43BwuI,0B;AA53BxuIA,IAAAA,EA43B2xI,e;AA53B3xIA,IAAAA,EA43BgzI,+B;AA53BhzIA,IAAAA,EA43Bm3I;AA53Bn3IA,MAAAA,EA43Bm3I;AAAA,sBA53Bn3IA,EA43Bm3I;AAAA,aAAc,oDAAoC,YAApC,CAAd;AAAA;AA53Bn3IA,MAAAA,EA43Bm3I;AAAA,sBA53Bn3IA,EA43Bm3I;AAAA,aAAoG,oDAAoC,YAApC,CAApG;AAAA,M;AA53Bn3IA,IAAAA,EA43B6hJ,0B;AA53B7hJA,IAAAA,EA43BglJ,e;AA53BhlJA,IAAAA,EA43BqmJ,+B;AA53BrmJA,IAAAA,EA43BiqJ,0B;AA53BjqJA,IAAAA,EA43BotJ,e;AA53BptJA,IAAAA,EA43ByuJ,+B;AA53BzuJA,IAAAA,EA43ByyJ;AA53BzyJA,MAAAA,EA43ByyJ;AAAA,sBA53BzyJA,EA43ByyJ;AAAA,aAAc,oDAAoC,KAApC,CAAd;AAAA;AA53BzyJA,MAAAA,EA43ByyJ;AAAA,sBA53BzyJA,EA43ByyJ;AAAA,aAA6F,oDAAoC,KAApC,CAA7F;AAAA,M;AA53BzyJA,IAAAA,EA43Bi8J,e;AA53Bj8JA,IAAAA,EA43Bs9J,+B;AA53Bt9JA,IAAAA,EA43BwhK;AA53BxhKA,MAAAA,EA43BwhK;AAAA,sBA53BxhKA,EA43BwhK;AAAA,aAAc,oDAAoC,OAApC,CAAd;AAAA;AA53BxhKA,MAAAA,EA43BwhK;AAAA,sBA53BxhKA,EA43BwhK;AAAA,aAA+F,oDAAoC,OAApC,CAA/F;AAAA,M;AA53BxhKA,IAAAA,EA43BorK,e;AA53BprKA,IAAAA,EA43BysK,+B;AA53BzsKA,IAAAA,EA43B4wK;AA53B5wKA,MAAAA,EA43B4wK;AAAA,sBA53B5wKA,EA43B4wK;AAAA,aAAc,oDAAoC,QAApC,CAAd;AAAA;AA53B5wKA,MAAAA,EA43B4wK;AAAA,sBA53B5wKA,EA43B4wK;AAAA,aAAgG,oDAAoC,QAApC,CAAhG;AAAA,M;AA53B5wKA,IAAAA,EA43B06K,e;AA53B16KA,IAAAA,EA43B+7K,+B;AA53B/7KA,IAAAA,EA43BggL;AA53BhgLA,MAAAA,EA43BggL;AAAA,sBA53BhgLA,EA43BggL;AAAA,aAAc,oDAAoC,MAApC,CAAd;AAAA;AA53BhgLA,MAAAA,EA43BggL;AAAA,sBA53BhgLA,EA43BggL;AAAA,aAA8F,oDAAoC,MAApC,CAA9F;AAAA,M;AA53BhgLA,IAAAA,EA43B0pL,e;AA53B1pLA,IAAAA,EA43B2qL,wB;;;;;;iBA53B3qLA,E;;AAAAA,IAAAA,EA43B2vE,4B;AA53B3vEA,IAAAA,EA43BusF;AA53BvsFA,MAAAA,EA43BusF;AAAA,sBA53BvsFA,EA43BusF;AAAA,aAAY,8BAAZ;AAAA,M;AA53BvsFA,IAAAA,EA43BqxF,4B;AA53BrxFA,IAAAA,EA43BuyF;AA53BvyFA,MAAAA,EA43BuyF;AAAA,sBA53BvyFA,EA43BuyF;AAAA,aAAc,iDAAd;AAAA;AA53BvyFA,MAAAA,EA43BuyF;AAAA,sBA53BvyFA,EA43BuyF;AAAA,aAA8E,iDAA9E;AAAA,M;AA53BvyFA,IAAAA,EA43Bs8F,e;AA53Bt8FA,IAAAA,EA43Bs9F,6F;AA53Bt9FA,IAAAA,EA43BgsL,e;;;;mBA53BhsLA,E;AAAAA,IAAAA,EA43Bo3E,8T;AA53Bp3EA,IAAAA,EA43Bo0E,iD;AA53Bp0EA,IAAAA,EA43Bq+F,a;AA53Br+FA,IAAAA,EA43Bq+F,8C;;;;AA1pCnkG,MAAMe,eAAN,CAAsB;AAClBC,EAAAA,WAAW,GAAG;AACV;AACA,SAAKC,MAAL,GAAc,KAAd;AACA,SAAKC,mBAAL,GAA2B,IAA3B;AACA,SAAKC,SAAL,GAAiB,EAAjB;AACA,SAAKC,WAAL,GAAmB,CAAnB;AACA,SAAKC,aAAL,GAAqB,CAArB;AACA,SAAKC,cAAL,GAAsB,CAAtB;AACA,SAAKC,eAAL,GAAuB,CAAvB;AACA,SAAKC,gBAAL,GAAwB,CAAxB;AACA,SAAKC,gBAAL,GAAwB,CAAxB;AACA,SAAKC,eAAL,GAAuB,CAAvB;AACA,SAAKC,kBAAL,GAA0B,CAA1B;AACA,SAAKC,mBAAL,GAA2B,CAA3B;AACA,SAAKC,cAAL,GAAsB,CAAtB;AACA,SAAKC,eAAL,GAAuB,CAAvB;AACA,SAAKC,YAAL,GAAoB,KAApB;AACA,SAAKC,aAAL,GAAqB,KAArB;AACA,SAAKC,YAAL,GAAoB,EAApB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,eAAL,GAAuB,IAAvB;AACA,SAAKC,wBAAL,GAAgC,KAAhC;AACA,SAAKC,iBAAL,GAAyB,KAAzB;AACA,SAAKC,UAAL,GAAkB,QAAlB,CAvBU,CAwBV;;AACA,SAAKC,qBAAL,GAA6B,EAA7B;AACA,SAAKC,sBAAL,GAA8B,EAA9B;AACA,SAAKC,qBAAL,GAA6B,EAA7B;AACA,SAAKC,sBAAL,GAA8B,EAA9B;AACA,SAAKC,QAAL,GAAgB,KAAKb,eAArB;AACH;;AACDc,EAAAA,UAAU,CAACC,OAAD,EAAU;AAChBC,IAAAA,MAAM,CAACC,IAAP,CAAYF,OAAZ,EACKG,MADL,CACaC,CAAD,IAAOA,CAAC,IAAI,IADxB,EAEKC,OAFL,CAEcD,CAAD,IAAO,KAAKA,CAAL,IAAUJ,OAAO,CAACI,CAAD,CAFrC;AAGA,SAAKE,eAAL;AACH;;AACDC,EAAAA,qBAAqB,CAACC,OAAD,EAAU;AAC3BP,IAAAA,MAAM,CAACC,IAAP,CAAYM,OAAZ,EACKL,MADL,CACaC,CAAD,IAAOA,CAAC,IAAI,IADxB,EAEKC,OAFL,CAEcD,CAAD,IAAO,KAAKA,CAAL,IAAUI,OAAO,CAACJ,CAAD,CAAP,CAAWK,YAFzC;AAGA,SAAKH,eAAL;AACH;;AACDA,EAAAA,eAAe,GAAG;AACd,QAAI,KAAKjC,mBAAL,IAA4B,CAAC,KAAKE,WAAtC,EAAmD;AAC/C,YAAM,IAAImC,KAAJ,CAAU,gEAAV,CAAN;AACH;AACJ;;AAhDiB;;AAmDtB,IAAIC,SAAJ;;AACA,CAAC,UAAUA,SAAV,EAAqB;AAClBA,EAAAA,SAAS,CAAC,MAAD,CAAT,GAAoB,MAApB;AACAA,EAAAA,SAAS,CAAC,QAAD,CAAT,GAAsB,QAAtB;AACAA,EAAAA,SAAS,CAAC,OAAD,CAAT,GAAqB,OAArB;AACH,CAJD,EAIGA,SAAS,KAAKA,SAAS,GAAG,EAAjB,CAJZ;;AAMA,SAASC,iBAAT,CAA2BC,GAA3B,EAAgC;AAC5B,UAAQA,GAAR;AACI,SAAK,SAAL;AACI,aAAO,KAAP;;AACJ,SAAK,YAAL;AACI,aAAO,OAAP;;AACJ,SAAK,WAAL;AACI,aAAO,QAAP;;AACJ,SAAK,WAAL;AACA;AACI,aAAO,MAAP;AATR;AAWH;;AACD,SAASC,yBAAT,CAAmCD,GAAnC,EAAwC;AACpC,UAAQA,GAAR;AACI,SAAK,SAAL;AACI,aAAO,QAAP;;AACJ,SAAK,YAAL;AACI,aAAO,MAAP;;AACJ,SAAK,WAAL;AACI,aAAO,KAAP;;AACJ,SAAK,WAAL;AACA;AACI,aAAO,OAAP;AATR;AAWH;;AACD,SAASE,cAAT,CAAwBF,GAAxB,EAA6Bf,QAA7B,EAAuC;AACnC,UAAQe,GAAR;AACI,SAAK,SAAL;AACI,aAAO;AAAEG,QAAAA,OAAO,EAAE,CAAX;AAAcC,QAAAA,OAAO,EAAEnB,QAAQ,GAAG,CAAC;AAAnC,OAAP;;AACJ,SAAK,YAAL;AACI,aAAO;AAAEkB,QAAAA,OAAO,EAAElB,QAAX;AAAqBmB,QAAAA,OAAO,EAAE;AAA9B,OAAP;;AACJ,SAAK,WAAL;AACI,aAAO;AAAED,QAAAA,OAAO,EAAE,CAAX;AAAcC,QAAAA,OAAO,EAAEnB;AAAvB,OAAP;;AACJ,SAAK,WAAL;AACA;AACI,aAAO;AAAEkB,QAAAA,OAAO,EAAElB,QAAQ,GAAG,CAAC,CAAvB;AAA0BmB,QAAAA,OAAO,EAAE;AAAnC,OAAP;AATR;AAWH;AAED;AACA;AACA;AACA;;;AACA,SAASC,YAAT,CAAsBC,MAAtB,EAA8BC,KAA9B,EAAqCC,MAArC,EAA6C;AACzC,QAAMC,YAAY,GAAGH,MAAM,CAACC,KAA5B;AACA,QAAMG,aAAa,GAAGJ,MAAM,CAACE,MAA7B;AACAD,EAAAA,KAAK,GAAGI,IAAI,CAACC,KAAL,CAAWL,KAAX,CAAR;AACAC,EAAAA,MAAM,GAAGG,IAAI,CAACC,KAAL,CAAWJ,MAAX,CAAT;AACA,QAAMK,OAAO,GAAGJ,YAAY,GAAGF,KAA/B;AACA,QAAMO,OAAO,GAAGJ,aAAa,GAAGF,MAAhC;AACA,QAAMO,YAAY,GAAGJ,IAAI,CAACK,IAAL,CAAUH,OAAO,GAAG,CAApB,CAArB;AACA,QAAMI,YAAY,GAAGN,IAAI,CAACK,IAAL,CAAUF,OAAO,GAAG,CAApB,CAArB;AACA,QAAMI,GAAG,GAAGZ,MAAM,CAACa,UAAP,CAAkB,IAAlB,CAAZ;;AACA,MAAID,GAAJ,EAAS;AACL,UAAME,GAAG,GAAGF,GAAG,CAACG,YAAJ,CAAiB,CAAjB,EAAoB,CAApB,EAAuBZ,YAAvB,EAAqCC,aAArC,CAAZ;AACA,UAAMY,IAAI,GAAGJ,GAAG,CAACK,eAAJ,CAAoBhB,KAApB,EAA2BC,MAA3B,CAAb;AACA,UAAMgB,IAAI,GAAGJ,GAAG,CAACI,IAAjB;AACA,UAAMC,KAAK,GAAGH,IAAI,CAACE,IAAnB;;AACA,SAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,MAApB,EAA4BkB,CAAC,EAA7B,EAAiC;AAC7B,WAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpB,KAApB,EAA2BoB,CAAC,EAA5B,EAAgC;AAC5B,cAAMC,EAAE,GAAG,CAACD,CAAC,GAAGD,CAAC,GAAGnB,KAAT,IAAkB,CAA7B;AACA,cAAMsB,QAAQ,GAAGH,CAAC,GAAGZ,OAArB;AACA,YAAIgB,MAAM,GAAG,CAAb;AACA,YAAIC,OAAO,GAAG,CAAd;AACA,YAAIC,aAAa,GAAG,CAApB;AACA,YAAIC,IAAI,GAAG,CAAX;AACA,YAAIC,IAAI,GAAG,CAAX;AACA,YAAIC,IAAI,GAAG,CAAX;AACA,YAAIC,IAAI,GAAG,CAAX;AACA,cAAMC,QAAQ,GAAG1B,IAAI,CAAC2B,KAAL,CAAWX,CAAC,GAAGd,OAAf,CAAjB;AACA,cAAM0B,QAAQ,GAAG5B,IAAI,CAAC2B,KAAL,CAAWZ,CAAC,GAAGZ,OAAf,CAAjB;AACA,YAAI0B,OAAO,GAAG7B,IAAI,CAACK,IAAL,CAAU,CAACW,CAAC,GAAG,CAAL,IAAUd,OAApB,CAAd;AACA,YAAI4B,OAAO,GAAG9B,IAAI,CAACK,IAAL,CAAU,CAACU,CAAC,GAAG,CAAL,IAAUZ,OAApB,CAAd;AACA0B,QAAAA,OAAO,GAAG7B,IAAI,CAAC+B,GAAL,CAASF,OAAT,EAAkB/B,YAAlB,CAAV;AACAgC,QAAAA,OAAO,GAAG9B,IAAI,CAAC+B,GAAL,CAASD,OAAT,EAAkB/B,aAAlB,CAAV;;AACA,aAAK,IAAIiC,EAAE,GAAGJ,QAAd,EAAwBI,EAAE,GAAGF,OAA7B,EAAsCE,EAAE,EAAxC,EAA4C;AACxC,gBAAMC,EAAE,GAAGjC,IAAI,CAACkC,GAAL,CAAShB,QAAQ,GAAGc,EAApB,IAA0B1B,YAArC;AACA,gBAAM6B,QAAQ,GAAGnB,CAAC,GAAGd,OAArB;AACA,gBAAMkC,EAAE,GAAGH,EAAE,GAAGA,EAAhB,CAHwC,CAGpB;;AACpB,eAAK,IAAII,EAAE,GAAGX,QAAd,EAAwBW,EAAE,GAAGR,OAA7B,EAAsCQ,EAAE,EAAxC,EAA4C;AACxC,kBAAMC,EAAE,GAAGtC,IAAI,CAACkC,GAAL,CAASC,QAAQ,GAAGE,EAApB,IAA0BjC,YAArC;AACA,kBAAMmC,CAAC,GAAGvC,IAAI,CAACwC,IAAL,CAAUJ,EAAE,GAAGE,EAAE,GAAGA,EAApB,CAAV;;AACA,gBAAIC,CAAC,IAAI,CAAT,EAAY;AACR;AACA;AACH,aANuC,CAOxC;;;AACApB,YAAAA,MAAM,GAAG,IAAIoB,CAAJ,GAAQA,CAAR,GAAYA,CAAZ,GAAgB,IAAIA,CAAJ,GAAQA,CAAxB,GAA4B,CAArC;AACA,kBAAME,KAAK,GAAG,KAAKJ,EAAE,GAAGL,EAAE,GAAGlC,YAAf,CAAd,CATwC,CAUxC;;AACA2B,YAAAA,IAAI,IAAIN,MAAM,GAAGN,IAAI,CAAC4B,KAAK,GAAG,CAAT,CAArB;AACApB,YAAAA,aAAa,IAAIF,MAAjB,CAZwC,CAaxC;;AACA,gBAAIN,IAAI,CAAC4B,KAAK,GAAG,CAAT,CAAJ,GAAkB,GAAtB,EACItB,MAAM,GAAGA,MAAM,GAAGN,IAAI,CAAC4B,KAAK,GAAG,CAAT,CAAb,GAA2B,GAApC;AACJnB,YAAAA,IAAI,IAAIH,MAAM,GAAGN,IAAI,CAAC4B,KAAD,CAArB;AACAlB,YAAAA,IAAI,IAAIJ,MAAM,GAAGN,IAAI,CAAC4B,KAAK,GAAG,CAAT,CAArB;AACAjB,YAAAA,IAAI,IAAIL,MAAM,GAAGN,IAAI,CAAC4B,KAAK,GAAG,CAAT,CAArB;AACArB,YAAAA,OAAO,IAAID,MAAX;AACH;AACJ;;AACDL,QAAAA,KAAK,CAACG,EAAD,CAAL,GAAYK,IAAI,GAAGF,OAAnB;AACAN,QAAAA,KAAK,CAACG,EAAE,GAAG,CAAN,CAAL,GAAgBM,IAAI,GAAGH,OAAvB;AACAN,QAAAA,KAAK,CAACG,EAAE,GAAG,CAAN,CAAL,GAAgBO,IAAI,GAAGJ,OAAvB;AACAN,QAAAA,KAAK,CAACG,EAAE,GAAG,CAAN,CAAL,GAAgBQ,IAAI,GAAGJ,aAAvB;AACH;AACJ;;AACD1B,IAAAA,MAAM,CAACC,KAAP,GAAeA,KAAf;AACAD,IAAAA,MAAM,CAACE,MAAP,GAAgBA,MAAhB,CAvDK,CAwDL;;AACAU,IAAAA,GAAG,CAACmC,YAAJ,CAAiB/B,IAAjB,EAAuB,CAAvB,EAA0B,CAA1B;AACH;AACJ;;AAED,SAASgC,UAAT,CAAoBC,OAApB,EAA6BC,UAA7B,EAAyC;AACrC,SAAQD,OAAO,GAAG,GAAX,GAAkBC,UAAzB;AACH;;AAED,MAAMC,WAAN,CAAkB;AACdC,EAAAA,IAAI,CAACC,WAAD,EAAcC,WAAd,EAA2BC,OAA3B,EAAoCC,QAApC,EAA8C;AAC9C,UAAMC,aAAa,GAAG,KAAKC,gBAAL,CAAsBL,WAAtB,EAAmCC,WAAnC,EAAgDC,OAAhD,EAAyDC,QAAzD,CAAtB;AACA,UAAMvD,KAAK,GAAGwD,aAAa,CAACnC,EAAd,GAAmBmC,aAAa,CAACE,EAA/C;AACA,UAAMzD,MAAM,GAAGuD,aAAa,CAACG,EAAd,GAAmBH,aAAa,CAACI,EAAhD;AACA,UAAMC,UAAU,GAAGC,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAnB;AACAF,IAAAA,UAAU,CAAC7D,KAAX,GAAmBA,KAAnB;AACA6D,IAAAA,UAAU,CAAC5D,MAAX,GAAoBA,MAApB;AACA,UAAMU,GAAG,GAAGkD,UAAU,CAACjD,UAAX,CAAsB,IAAtB,CAAZ;;AACA,QAAI,CAACD,GAAL,EAAU;AACN,aAAO,IAAP;AACH;;AACD,QAAI4C,QAAQ,CAACrF,eAAT,IAA4B,IAAhC,EAAsC;AAClCyC,MAAAA,GAAG,CAACqD,SAAJ,GAAgBT,QAAQ,CAACrF,eAAzB;AACAyC,MAAAA,GAAG,CAACsD,QAAJ,CAAa,CAAb,EAAgB,CAAhB,EAAmBjE,KAAnB,EAA0BC,MAA1B;AACH;;AACD,UAAMiE,MAAM,GAAG,CAACX,QAAQ,CAACrG,SAAT,CAAmBiH,KAAnB,IAA4B,CAA7B,KAAmCZ,QAAQ,CAACrG,SAAT,CAAmBkH,KAAnB,GAA2B,CAAC,CAA5B,GAAgC,CAAnE,CAAf;AACA,UAAMC,MAAM,GAAG,CAACd,QAAQ,CAACrG,SAAT,CAAmBiH,KAAnB,IAA4B,CAA7B,KAAmCZ,QAAQ,CAACrG,SAAT,CAAmBoH,KAAnB,GAA2B,CAAC,CAA5B,GAAgC,CAAnE,CAAf;AACA,UAAMC,gBAAgB,GAAGlB,WAAW,CAACmB,WAArC;AACA7D,IAAAA,GAAG,CAAC8D,YAAJ,CAAiBP,MAAjB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+BG,MAA/B,EAAuCE,gBAAgB,CAACG,IAAjB,CAAsB1E,KAAtB,GAA8B,CAArE,EAAwEuE,gBAAgB,CAACG,IAAjB,CAAsBzE,MAAtB,GAA+B,CAAvG;AACAU,IAAAA,GAAG,CAACgE,SAAJ,CAAc,CAACnB,aAAa,CAACE,EAAf,GAAoBQ,MAAlC,EAA0C,CAACV,aAAa,CAACI,EAAf,GAAoBS,MAA9D;AACA1D,IAAAA,GAAG,CAACiE,MAAJ,CAAW,CAACrB,QAAQ,CAACrG,SAAT,CAAmB0H,MAAnB,IAA6B,CAA9B,IAAmCxE,IAAI,CAACyE,EAAxC,GAA6C,GAAxD;AACA,UAAMC,UAAU,GAAGvB,QAAQ,CAACrG,SAAT,CAAmB4H,UAAnB,GAAgC/B,UAAU,CAACQ,QAAQ,CAACrG,SAAT,CAAmB4H,UAApB,EAAgCP,gBAAgB,CAACG,IAAjB,CAAsB1E,KAAtD,CAA1C,GAAyG,CAA5H;AACA,UAAM+E,UAAU,GAAGxB,QAAQ,CAACrG,SAAT,CAAmB6H,UAAnB,GAAgChC,UAAU,CAACQ,QAAQ,CAACrG,SAAT,CAAmB6H,UAApB,EAAgCR,gBAAgB,CAACG,IAAjB,CAAsBzE,MAAtD,CAA1C,GAA0G,CAA7H;AACAU,IAAAA,GAAG,CAACqE,SAAJ,CAAcT,gBAAgB,CAACU,KAA/B,EAAsCH,UAAU,GAAGP,gBAAgB,CAACG,IAAjB,CAAsB1E,KAAtB,GAA8B,CAAjF,EAAoF+E,UAAU,GAAGR,gBAAgB,CAACG,IAAjB,CAAsBzE,MAAtB,GAA+B,CAAhI;AACA,UAAMiF,MAAM,GAAG;AACXlF,MAAAA,KADW;AACJC,MAAAA,MADI;AAEXuD,MAAAA,aAFW;AAGX2B,MAAAA,eAAe,EAAE,EAAE,GAAG7B;AAAL;AAHN,KAAf;;AAKA,QAAIC,QAAQ,CAACpF,wBAAb,EAAuC;AACnC+G,MAAAA,MAAM,CAACE,mBAAP,GAA6B,KAAKC,sBAAL,CAA4BjC,WAA5B,EAAyCC,WAAzC,EAAsDC,OAAtD,EAA+DC,QAA/D,CAA7B;AACH;;AACD,UAAM+B,WAAW,GAAG,KAAKC,cAAL,CAAoBvF,KAApB,EAA2BC,MAA3B,EAAmCsD,QAAnC,CAApB;;AACA,QAAI+B,WAAW,KAAK,CAApB,EAAuB;AACnBJ,MAAAA,MAAM,CAAClF,KAAP,GAAeI,IAAI,CAACC,KAAL,CAAWL,KAAK,GAAGsF,WAAnB,CAAf;AACAJ,MAAAA,MAAM,CAACjF,MAAP,GAAgBsD,QAAQ,CAACtG,mBAAT,GACVmD,IAAI,CAACC,KAAL,CAAW6E,MAAM,CAAClF,KAAP,GAAeuD,QAAQ,CAACpG,WAAnC,CADU,GAEViD,IAAI,CAACC,KAAL,CAAWJ,MAAM,GAAGqF,WAApB,CAFN;AAGAxF,MAAAA,YAAY,CAAC+D,UAAD,EAAaqB,MAAM,CAAClF,KAApB,EAA2BkF,MAAM,CAACjF,MAAlC,CAAZ;AACH;;AACDiF,IAAAA,MAAM,CAACM,MAAP,GAAgB3B,UAAU,CAAC4B,SAAX,CAAqB,WAAWlC,QAAQ,CAACvG,MAAzC,EAAiD,KAAK0I,UAAL,CAAgBnC,QAAhB,CAAjD,CAAhB;AACA,WAAO2B,MAAP;AACH;;AACDzB,EAAAA,gBAAgB,CAACL,WAAD,EAAcC,WAAd,EAA2BC,OAA3B,EAAoCC,QAApC,EAA8C;AAC1D,UAAMoC,kBAAkB,GAAGvC,WAAW,CAACwC,aAAvC;AACA,UAAMC,KAAK,GAAGxC,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6B1E,KAA7B,GAAqC2F,kBAAkB,CAACG,WAAtE;AACA,UAAMC,GAAG,GAAG;AACRrC,MAAAA,EAAE,EAAEtD,IAAI,CAACC,KAAL,CAAWiD,OAAO,CAACI,EAAR,GAAamC,KAAxB,CADI;AAERjC,MAAAA,EAAE,EAAExD,IAAI,CAACC,KAAL,CAAWiD,OAAO,CAACM,EAAR,GAAaiC,KAAxB,CAFI;AAGRxE,MAAAA,EAAE,EAAEjB,IAAI,CAACC,KAAL,CAAWiD,OAAO,CAACjC,EAAR,GAAawE,KAAxB,CAHI;AAIRlC,MAAAA,EAAE,EAAEvD,IAAI,CAACC,KAAL,CAAWiD,OAAO,CAACK,EAAR,GAAakC,KAAxB;AAJI,KAAZ;;AAMA,QAAI,CAACtC,QAAQ,CAACpF,wBAAd,EAAwC;AACpC4H,MAAAA,GAAG,CAACrC,EAAJ,GAAStD,IAAI,CAAC4F,GAAL,CAASD,GAAG,CAACrC,EAAb,EAAiB,CAAjB,CAAT;AACAqC,MAAAA,GAAG,CAACnC,EAAJ,GAASxD,IAAI,CAAC4F,GAAL,CAASD,GAAG,CAACnC,EAAb,EAAiB,CAAjB,CAAT;AACAmC,MAAAA,GAAG,CAAC1E,EAAJ,GAASjB,IAAI,CAAC+B,GAAL,CAAS4D,GAAG,CAAC1E,EAAb,EAAiBgC,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6B1E,KAA9C,CAAT;AACA+F,MAAAA,GAAG,CAACpC,EAAJ,GAASvD,IAAI,CAAC+B,GAAL,CAAS4D,GAAG,CAACpC,EAAb,EAAiBN,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6BzE,MAA9C,CAAT;AACH;;AACD,WAAO8F,GAAP;AACH;;AACDV,EAAAA,sBAAsB,CAACjC,WAAD,EAAcC,WAAd,EAA2BC,OAA3B,EAAoCC,QAApC,EAA8C;AAChE,UAAM3F,cAAc,GAAG2F,QAAQ,CAAC3F,cAAT,GAA0ByF,WAAW,CAAC4C,aAAZ,CAA0BrB,MAA3E;AACA,UAAMe,kBAAkB,GAAGvC,WAAW,CAACwC,aAAvC;AACA,UAAMC,KAAK,GAAGxC,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6B1E,KAA7B,GAAqC2F,kBAAkB,CAACG,WAAtE;AACA,QAAII,OAAJ;AACA,QAAIC,OAAJ;;AACA,QAAIvI,cAAc,GAAG,CAArB,EAAwB;AACpBsI,MAAAA,OAAO,GAAG,CAAC7C,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6B1E,KAA7B,GAAqCqD,WAAW,CAAC+C,QAAZ,CAAqB1B,IAArB,CAA0BzE,MAAhE,IAA0E,CAApF;AACAkG,MAAAA,OAAO,GAAG,CAAC9C,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6BzE,MAA7B,GAAsCoD,WAAW,CAAC+C,QAAZ,CAAqB1B,IAArB,CAA0B1E,KAAjE,IAA0E,CAApF;AACH,KAHD,MAIK;AACDkG,MAAAA,OAAO,GAAG,CAAC7C,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6B1E,KAA7B,GAAqCqD,WAAW,CAAC+C,QAAZ,CAAqB1B,IAArB,CAA0B1E,KAAhE,IAAyE,CAAnF;AACAmG,MAAAA,OAAO,GAAG,CAAC9C,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6BzE,MAA7B,GAAsCoD,WAAW,CAAC+C,QAAZ,CAAqB1B,IAArB,CAA0BzE,MAAjE,IAA2E,CAArF;AACH;;AACD,UAAM8F,GAAG,GAAG;AACRrC,MAAAA,EAAE,EAAEtD,IAAI,CAACC,KAAL,CAAWiD,OAAO,CAACI,EAAR,GAAamC,KAAxB,IAAiCK,OAD7B;AAERtC,MAAAA,EAAE,EAAExD,IAAI,CAACC,KAAL,CAAWiD,OAAO,CAACM,EAAR,GAAaiC,KAAxB,IAAiCM,OAF7B;AAGR9E,MAAAA,EAAE,EAAEjB,IAAI,CAACC,KAAL,CAAWiD,OAAO,CAACjC,EAAR,GAAawE,KAAxB,IAAiCK,OAH7B;AAIRvC,MAAAA,EAAE,EAAEvD,IAAI,CAACC,KAAL,CAAWiD,OAAO,CAACK,EAAR,GAAakC,KAAxB,IAAiCM;AAJ7B,KAAZ;;AAMA,QAAI,CAAC5C,QAAQ,CAACpF,wBAAd,EAAwC;AACpC4H,MAAAA,GAAG,CAACrC,EAAJ,GAAStD,IAAI,CAAC4F,GAAL,CAASD,GAAG,CAACrC,EAAb,EAAiB,CAAjB,CAAT;AACAqC,MAAAA,GAAG,CAACnC,EAAJ,GAASxD,IAAI,CAAC4F,GAAL,CAASD,GAAG,CAACnC,EAAb,EAAiB,CAAjB,CAAT;AACAmC,MAAAA,GAAG,CAAC1E,EAAJ,GAASjB,IAAI,CAAC+B,GAAL,CAAS4D,GAAG,CAAC1E,EAAb,EAAiBgC,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6B1E,KAA9C,CAAT;AACA+F,MAAAA,GAAG,CAACpC,EAAJ,GAASvD,IAAI,CAAC+B,GAAL,CAAS4D,GAAG,CAACpC,EAAb,EAAiBN,WAAW,CAACmB,WAAZ,CAAwBE,IAAxB,CAA6BzE,MAA9C,CAAT;AACH;;AACD,WAAO8F,GAAP;AACH;;AACDR,EAAAA,cAAc,CAACvF,KAAD,EAAQC,MAAR,EAAgBsD,QAAhB,EAA0B;AACpC,UAAM8C,UAAU,GAAG9C,QAAQ,CAACnG,aAAT,GAAyB4C,KAA5C;AACA,UAAMsG,WAAW,GAAG/C,QAAQ,CAAClG,cAAT,GAA0B4C,MAA9C;AACA,UAAMsG,MAAM,GAAG,IAAIC,KAAJ,EAAf;;AACA,QAAIjD,QAAQ,CAACnG,aAAT,GAAyB,CAA7B,EAAgC;AAC5BmJ,MAAAA,MAAM,CAACE,IAAP,CAAYJ,UAAZ;AACH;;AACD,QAAI9C,QAAQ,CAAClG,cAAT,GAA0B,CAA9B,EAAiC;AAC7BkJ,MAAAA,MAAM,CAACE,IAAP,CAAYH,WAAZ;AACH;;AACD,UAAMI,MAAM,GAAGH,MAAM,CAACI,MAAP,KAAkB,CAAlB,GAAsB,CAAtB,GAA0BvG,IAAI,CAAC+B,GAAL,CAAS,GAAGoE,MAAZ,CAAzC;;AACA,QAAIG,MAAM,GAAG,CAAT,IAAc,CAACnD,QAAQ,CAACxF,aAA5B,EAA2C;AACvC,aAAO2I,MAAP;AACH;;AACD,WAAOtG,IAAI,CAAC+B,GAAL,CAASuE,MAAT,EAAiB,CAAjB,CAAP;AACH;;AACDhB,EAAAA,UAAU,CAACnC,QAAD,EAAW;AACjB,WAAOnD,IAAI,CAAC+B,GAAL,CAAS,CAAT,EAAY/B,IAAI,CAAC4F,GAAL,CAAS,CAAT,EAAYzC,QAAQ,CAACvF,YAAT,GAAwB,GAApC,CAAZ,CAAP;AACH;;AA3Ga;;AA6GlBkF,WAAW,CAAC0D,IAAZ;AAAA,mBAAwG1D,WAAxG;AAAA;;AACAA,WAAW,CAAC2D,KAAZ,kBAD8F9K,EAC9F;AAAA,SAA4GmH,WAA5G;AAAA,WAA4GA,WAA5G;AAAA,cAAqI;AAArI;;AACA;AAAA,qDAF8FnH,EAE9F,mBAA2FmH,WAA3F,EAAoH,CAAC;AACzG4D,IAAAA,IAAI,EAAE9K,UADmG;AAEzG+K,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFmG,GAAD,CAApH;AAAA;;AAKA,MAAMC,sBAAN,CAA6B;AACzBC,EAAAA,oBAAoB,CAAC9D,WAAD,EAAc+B,eAAd,EAA+B5B,QAA/B,EAAyC;AACzD,QAAI,CAACH,WAAW,EAAEwC,aAAlB,EAAiC;AAC7B;AACH;;AACD,UAAMD,kBAAkB,GAAGvC,WAAW,CAACwC,aAAvC;;AACA,QAAIrC,QAAQ,CAAC5F,mBAAT,IAAgC4F,QAAQ,CAAC7F,kBAA7C,EAAiE;AAC7DyH,MAAAA,eAAe,CAACzB,EAAhB,GAAqB,CAArB;AACAyB,MAAAA,eAAe,CAAC9D,EAAhB,GAAqBsE,kBAAkB,CAACG,WAAnB,GAAiCvC,QAAQ,CAAC7F,kBAA1C,GACjB6F,QAAQ,CAAC7F,kBADQ,GACaiI,kBAAkB,CAACG,WADrD;AAEAX,MAAAA,eAAe,CAACvB,EAAhB,GAAqB,CAArB;AACAuB,MAAAA,eAAe,CAACxB,EAAhB,GAAqBgC,kBAAkB,CAACwB,YAAnB,GAAkC5D,QAAQ,CAAC5F,mBAA3C,GACjB4F,QAAQ,CAAC5F,mBADQ,GACcgI,kBAAkB,CAACwB,YADtD;AAEH,KAPD,MAQK;AACD,YAAMC,YAAY,GAAGhH,IAAI,CAAC+B,GAAL,CAASoB,QAAQ,CAAC/E,qBAAlB,EAAyCmH,kBAAkB,CAACG,WAA5D,CAArB;AACA,YAAMuB,aAAa,GAAGjH,IAAI,CAAC+B,GAAL,CAASoB,QAAQ,CAAC9E,sBAAlB,EAA0CkH,kBAAkB,CAACwB,YAA7D,CAAtB;;AACA,UAAI,CAAC5D,QAAQ,CAACtG,mBAAd,EAAmC;AAC/BkI,QAAAA,eAAe,CAACzB,EAAhB,GAAqB,CAArB;AACAyB,QAAAA,eAAe,CAAC9D,EAAhB,GAAqB+F,YAArB;AACAjC,QAAAA,eAAe,CAACvB,EAAhB,GAAqB,CAArB;AACAuB,QAAAA,eAAe,CAACxB,EAAhB,GAAqB0D,aAArB;AACH,OALD,MAMK,IAAI1B,kBAAkB,CAACG,WAAnB,GAAiCvC,QAAQ,CAACpG,WAA1C,GAAwDwI,kBAAkB,CAACwB,YAA/E,EAA6F;AAC9FhC,QAAAA,eAAe,CAACzB,EAAhB,GAAqB,CAArB;AACAyB,QAAAA,eAAe,CAAC9D,EAAhB,GAAqB+F,YAArB;AACA,cAAME,4BAA4B,GAAGF,YAAY,GAAG7D,QAAQ,CAACpG,WAA7D;AACAgI,QAAAA,eAAe,CAACvB,EAAhB,GAAqB,CAAC+B,kBAAkB,CAACwB,YAAnB,GAAkCG,4BAAnC,IAAmE,CAAxF;AACAnC,QAAAA,eAAe,CAACxB,EAAhB,GAAqBwB,eAAe,CAACvB,EAAhB,GAAqB0D,4BAA1C;AACH,OANI,MAOA;AACDnC,QAAAA,eAAe,CAACvB,EAAhB,GAAqB,CAArB;AACAuB,QAAAA,eAAe,CAACxB,EAAhB,GAAqB0D,aAArB;AACA,cAAME,2BAA2B,GAAGF,aAAa,GAAG9D,QAAQ,CAACpG,WAA7D;AACAgI,QAAAA,eAAe,CAACzB,EAAhB,GAAqB,CAACiC,kBAAkB,CAACG,WAAnB,GAAiCyB,2BAAlC,IAAiE,CAAtF;AACApC,QAAAA,eAAe,CAAC9D,EAAhB,GAAqB8D,eAAe,CAACzB,EAAhB,GAAqB6D,2BAA1C;AACH;AACJ;AACJ;;AACDC,EAAAA,IAAI,CAACC,KAAD,EAAQC,SAAR,EAAmBvC,eAAnB,EAAoC;AACpC,UAAMwC,KAAK,GAAG,KAAKC,UAAL,CAAgBH,KAAhB,IAAyBC,SAAS,CAAC9H,OAAjD;AACA,UAAMiI,KAAK,GAAG,KAAKC,UAAL,CAAgBL,KAAhB,IAAyBC,SAAS,CAAC7H,OAAjD;AACAsF,IAAAA,eAAe,CAACzB,EAAhB,GAAqBgE,SAAS,CAAChE,EAAV,GAAeiE,KAApC;AACAxC,IAAAA,eAAe,CAACvB,EAAhB,GAAqB8D,SAAS,CAAC9D,EAAV,GAAeiE,KAApC;AACA1C,IAAAA,eAAe,CAAC9D,EAAhB,GAAqBqG,SAAS,CAACrG,EAAV,GAAesG,KAApC;AACAxC,IAAAA,eAAe,CAACxB,EAAhB,GAAqB+D,SAAS,CAAC/D,EAAV,GAAekE,KAApC;AACH;;AACDE,EAAAA,MAAM,CAACN,KAAD,EAAQC,SAAR,EAAmBvC,eAAnB,EAAoC6C,OAApC,EAA6CzE,QAA7C,EAAuD;AACzD,UAAM0E,KAAK,GAAG,KAAKL,UAAL,CAAgBH,KAAhB,IAAyBC,SAAS,CAAC9H,OAAjD;AACA,UAAMsI,KAAK,GAAG,KAAKJ,UAAL,CAAgBL,KAAhB,IAAyBC,SAAS,CAAC7H,OAAjD;;AACA,YAAQ6H,SAAS,CAACS,QAAlB;AACI,WAAK,MAAL;AACIhD,QAAAA,eAAe,CAACzB,EAAhB,GAAqBtD,IAAI,CAAC+B,GAAL,CAAS/B,IAAI,CAAC4F,GAAL,CAAS0B,SAAS,CAAChE,EAAV,GAAeuE,KAAxB,EAA+B9C,eAAe,CAAC9D,EAAhB,GAAqBkC,QAAQ,CAAC/E,qBAA7D,CAAT,EAA8F2G,eAAe,CAAC9D,EAAhB,GAAqBkC,QAAQ,CAACjF,qBAA5H,CAArB;AACA;;AACJ,WAAK,SAAL;AACI6G,QAAAA,eAAe,CAACzB,EAAhB,GAAqBtD,IAAI,CAAC+B,GAAL,CAAS/B,IAAI,CAAC4F,GAAL,CAAS0B,SAAS,CAAChE,EAAV,GAAeuE,KAAxB,EAA+B9C,eAAe,CAAC9D,EAAhB,GAAqBkC,QAAQ,CAAC/E,qBAA7D,CAAT,EAA8F2G,eAAe,CAAC9D,EAAhB,GAAqBkC,QAAQ,CAACjF,qBAA5H,CAArB;AACA6G,QAAAA,eAAe,CAACvB,EAAhB,GAAqBxD,IAAI,CAAC+B,GAAL,CAAS/B,IAAI,CAAC4F,GAAL,CAAS0B,SAAS,CAAC9D,EAAV,GAAesE,KAAxB,EAA+B/C,eAAe,CAACxB,EAAhB,GAAqBJ,QAAQ,CAAC9E,sBAA7D,CAAT,EAA+F0G,eAAe,CAACxB,EAAhB,GAAqBJ,QAAQ,CAAChF,sBAA7H,CAArB;AACA;;AACJ,WAAK,KAAL;AACI4G,QAAAA,eAAe,CAACvB,EAAhB,GAAqBxD,IAAI,CAAC+B,GAAL,CAAS/B,IAAI,CAAC4F,GAAL,CAAS0B,SAAS,CAAC9D,EAAV,GAAesE,KAAxB,EAA+B/C,eAAe,CAACxB,EAAhB,GAAqBJ,QAAQ,CAAC9E,sBAA7D,CAAT,EAA+F0G,eAAe,CAACxB,EAAhB,GAAqBJ,QAAQ,CAAChF,sBAA7H,CAArB;AACA;;AACJ,WAAK,UAAL;AACI4G,QAAAA,eAAe,CAAC9D,EAAhB,GAAqBjB,IAAI,CAAC4F,GAAL,CAAS5F,IAAI,CAAC+B,GAAL,CAASuF,SAAS,CAACrG,EAAV,GAAe4G,KAAxB,EAA+B9C,eAAe,CAACzB,EAAhB,GAAqBH,QAAQ,CAAC/E,qBAA7D,CAAT,EAA8F2G,eAAe,CAACzB,EAAhB,GAAqBH,QAAQ,CAACjF,qBAA5H,CAArB;AACA6G,QAAAA,eAAe,CAACvB,EAAhB,GAAqBxD,IAAI,CAAC+B,GAAL,CAAS/B,IAAI,CAAC4F,GAAL,CAAS0B,SAAS,CAAC9D,EAAV,GAAesE,KAAxB,EAA+B/C,eAAe,CAACxB,EAAhB,GAAqBJ,QAAQ,CAAC9E,sBAA7D,CAAT,EAA+F0G,eAAe,CAACxB,EAAhB,GAAqBJ,QAAQ,CAAChF,sBAA7H,CAArB;AACA;;AACJ,WAAK,OAAL;AACI4G,QAAAA,eAAe,CAAC9D,EAAhB,GAAqBjB,IAAI,CAAC4F,GAAL,CAAS5F,IAAI,CAAC+B,GAAL,CAASuF,SAAS,CAACrG,EAAV,GAAe4G,KAAxB,EAA+B9C,eAAe,CAACzB,EAAhB,GAAqBH,QAAQ,CAAC/E,qBAA7D,CAAT,EAA8F2G,eAAe,CAACzB,EAAhB,GAAqBH,QAAQ,CAACjF,qBAA5H,CAArB;AACA;;AACJ,WAAK,aAAL;AACI6G,QAAAA,eAAe,CAAC9D,EAAhB,GAAqBjB,IAAI,CAAC4F,GAAL,CAAS5F,IAAI,CAAC+B,GAAL,CAASuF,SAAS,CAACrG,EAAV,GAAe4G,KAAxB,EAA+B9C,eAAe,CAACzB,EAAhB,GAAqBH,QAAQ,CAAC/E,qBAA7D,CAAT,EAA8F2G,eAAe,CAACzB,EAAhB,GAAqBH,QAAQ,CAACjF,qBAA5H,CAArB;AACA6G,QAAAA,eAAe,CAACxB,EAAhB,GAAqBvD,IAAI,CAAC4F,GAAL,CAAS5F,IAAI,CAAC+B,GAAL,CAASuF,SAAS,CAAC/D,EAAV,GAAeuE,KAAxB,EAA+B/C,eAAe,CAACvB,EAAhB,GAAqBL,QAAQ,CAAC9E,sBAA7D,CAAT,EAA+F0G,eAAe,CAACvB,EAAhB,GAAqBL,QAAQ,CAAChF,sBAA7H,CAArB;AACA;;AACJ,WAAK,QAAL;AACI4G,QAAAA,eAAe,CAACxB,EAAhB,GAAqBvD,IAAI,CAAC4F,GAAL,CAAS5F,IAAI,CAAC+B,GAAL,CAASuF,SAAS,CAAC/D,EAAV,GAAeuE,KAAxB,EAA+B/C,eAAe,CAACvB,EAAhB,GAAqBL,QAAQ,CAAC9E,sBAA7D,CAAT,EAA+F0G,eAAe,CAACvB,EAAhB,GAAqBL,QAAQ,CAAChF,sBAA7H,CAArB;AACA;;AACJ,WAAK,YAAL;AACI4G,QAAAA,eAAe,CAACzB,EAAhB,GAAqBtD,IAAI,CAAC+B,GAAL,CAAS/B,IAAI,CAAC4F,GAAL,CAAS0B,SAAS,CAAChE,EAAV,GAAeuE,KAAxB,EAA+B9C,eAAe,CAAC9D,EAAhB,GAAqBkC,QAAQ,CAAC/E,qBAA7D,CAAT,EAA8F2G,eAAe,CAAC9D,EAAhB,GAAqBkC,QAAQ,CAACjF,qBAA5H,CAArB;AACA6G,QAAAA,eAAe,CAACxB,EAAhB,GAAqBvD,IAAI,CAAC4F,GAAL,CAAS5F,IAAI,CAAC+B,GAAL,CAASuF,SAAS,CAAC/D,EAAV,GAAeuE,KAAxB,EAA+B/C,eAAe,CAACvB,EAAhB,GAAqBL,QAAQ,CAAC9E,sBAA7D,CAAT,EAA+F0G,eAAe,CAACvB,EAAhB,GAAqBL,QAAQ,CAAChF,sBAA7H,CAArB;AACA;;AACJ,WAAK,QAAL;AACI,cAAM4F,KAAK,GAAGsD,KAAK,CAACtD,KAApB;AACA,cAAMiE,QAAQ,GAAGhI,IAAI,CAAC+B,GAAL,CAAS/B,IAAI,CAAC4F,GAAL,CAASzC,QAAQ,CAACjF,qBAAlB,EAA0C8B,IAAI,CAACkC,GAAL,CAASoF,SAAS,CAACrG,EAAV,GAAeqG,SAAS,CAAChE,EAAlC,CAAD,GAA0CS,KAAnF,CAAT,EAAoGZ,QAAQ,CAAC/E,qBAA7G,CAAjB;AACA,cAAM6J,SAAS,GAAGjI,IAAI,CAAC+B,GAAL,CAAS/B,IAAI,CAAC4F,GAAL,CAASzC,QAAQ,CAAChF,sBAAlB,EAA2C6B,IAAI,CAACkC,GAAL,CAASoF,SAAS,CAAC/D,EAAV,GAAe+D,SAAS,CAAC9D,EAAlC,CAAD,GAA0CO,KAApF,CAAT,EAAqGZ,QAAQ,CAAC9E,sBAA9G,CAAlB;AACA0G,QAAAA,eAAe,CAACzB,EAAhB,GAAqBgE,SAAS,CAAC9H,OAAV,GAAoBwI,QAAQ,GAAG,CAApD;AACAjD,QAAAA,eAAe,CAAC9D,EAAhB,GAAqBqG,SAAS,CAAC9H,OAAV,GAAoBwI,QAAQ,GAAG,CAApD;AACAjD,QAAAA,eAAe,CAACvB,EAAhB,GAAqB8D,SAAS,CAAC7H,OAAV,GAAoBwI,SAAS,GAAG,CAArD;AACAlD,QAAAA,eAAe,CAACxB,EAAhB,GAAqB+D,SAAS,CAAC7H,OAAV,GAAoBwI,SAAS,GAAG,CAArD;;AACA,YAAIlD,eAAe,CAACzB,EAAhB,GAAqB,CAAzB,EAA4B;AACxByB,UAAAA,eAAe,CAAC9D,EAAhB,IAAsB8D,eAAe,CAACzB,EAAtC;AACAyB,UAAAA,eAAe,CAACzB,EAAhB,GAAqB,CAArB;AACH,SAHD,MAIK,IAAIyB,eAAe,CAAC9D,EAAhB,GAAqB2G,OAAO,CAAChI,KAAjC,EAAwC;AACzCmF,UAAAA,eAAe,CAACzB,EAAhB,IAAuByB,eAAe,CAAC9D,EAAhB,GAAqB2G,OAAO,CAAChI,KAApD;AACAmF,UAAAA,eAAe,CAAC9D,EAAhB,GAAqB2G,OAAO,CAAChI,KAA7B;AACH;;AACD,YAAImF,eAAe,CAACvB,EAAhB,GAAqB,CAAzB,EAA4B;AACxBuB,UAAAA,eAAe,CAACxB,EAAhB,IAAsBwB,eAAe,CAACvB,EAAtC;AACAuB,UAAAA,eAAe,CAACvB,EAAhB,GAAqB,CAArB;AACH,SAHD,MAIK,IAAIuB,eAAe,CAACxB,EAAhB,GAAqBqE,OAAO,CAAC/H,MAAjC,EAAyC;AAC1CkF,UAAAA,eAAe,CAACvB,EAAhB,IAAuBuB,eAAe,CAACxB,EAAhB,GAAqBqE,OAAO,CAAC/H,MAApD;AACAkF,UAAAA,eAAe,CAACxB,EAAhB,GAAqBqE,OAAO,CAAC/H,MAA7B;AACH;;AACD;AArDR;;AAuDA,QAAIsD,QAAQ,CAACtG,mBAAb,EAAkC;AAC9B,WAAKqL,gBAAL,CAAsBZ,SAAS,CAACS,QAAhC,EAA0ChD,eAA1C,EAA2D6C,OAA3D,EAAoEzE,QAApE;AACH;AACJ;;AACD+E,EAAAA,gBAAgB,CAACH,QAAD,EAAWhD,eAAX,EAA4B6C,OAA5B,EAAqCzE,QAArC,EAA+C;AAC3D,QAAIgF,SAAS,GAAG,CAAhB;AACA,QAAIC,SAAS,GAAG,CAAhB;;AACA,YAAQL,QAAR;AACI,WAAK,KAAL;AACIhD,QAAAA,eAAe,CAAC9D,EAAhB,GAAqB8D,eAAe,CAACzB,EAAhB,GAAqB,CAACyB,eAAe,CAACxB,EAAhB,GAAqBwB,eAAe,CAACvB,EAAtC,IAA4CL,QAAQ,CAACpG,WAA/F;AACAoL,QAAAA,SAAS,GAAGnI,IAAI,CAAC4F,GAAL,CAASb,eAAe,CAAC9D,EAAhB,GAAqB2G,OAAO,CAAChI,KAAtC,EAA6C,CAA7C,CAAZ;AACAwI,QAAAA,SAAS,GAAGpI,IAAI,CAAC4F,GAAL,CAAS,IAAIb,eAAe,CAACvB,EAA7B,EAAiC,CAAjC,CAAZ;;AACA,YAAI2E,SAAS,GAAG,CAAZ,IAAiBC,SAAS,GAAG,CAAjC,EAAoC;AAChCrD,UAAAA,eAAe,CAAC9D,EAAhB,IAAuBmH,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAkDC,SAAS,GAAGjF,QAAQ,CAACpG,WAAvE,GAAsFoL,SAA5G;AACApD,UAAAA,eAAe,CAACvB,EAAhB,IAAuB4E,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAiDC,SAAjD,GAA6DD,SAAS,GAAGhF,QAAQ,CAACpG,WAAxG;AACH;;AACD;;AACJ,WAAK,QAAL;AACIgI,QAAAA,eAAe,CAAC9D,EAAhB,GAAqB8D,eAAe,CAACzB,EAAhB,GAAqB,CAACyB,eAAe,CAACxB,EAAhB,GAAqBwB,eAAe,CAACvB,EAAtC,IAA4CL,QAAQ,CAACpG,WAA/F;AACAoL,QAAAA,SAAS,GAAGnI,IAAI,CAAC4F,GAAL,CAASb,eAAe,CAAC9D,EAAhB,GAAqB2G,OAAO,CAAChI,KAAtC,EAA6C,CAA7C,CAAZ;AACAwI,QAAAA,SAAS,GAAGpI,IAAI,CAAC4F,GAAL,CAASb,eAAe,CAACxB,EAAhB,GAAqBqE,OAAO,CAAC/H,MAAtC,EAA8C,CAA9C,CAAZ;;AACA,YAAIsI,SAAS,GAAG,CAAZ,IAAiBC,SAAS,GAAG,CAAjC,EAAoC;AAChCrD,UAAAA,eAAe,CAAC9D,EAAhB,IAAuBmH,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAkDC,SAAS,GAAGjF,QAAQ,CAACpG,WAAvE,GAAsFoL,SAA5G;AACApD,UAAAA,eAAe,CAACxB,EAAhB,IAAuB6E,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAiDC,SAAjD,GAA8DD,SAAS,GAAGhF,QAAQ,CAACpG,WAAzG;AACH;;AACD;;AACJ,WAAK,SAAL;AACIgI,QAAAA,eAAe,CAACvB,EAAhB,GAAqBuB,eAAe,CAACxB,EAAhB,GAAqB,CAACwB,eAAe,CAAC9D,EAAhB,GAAqB8D,eAAe,CAACzB,EAAtC,IAA4CH,QAAQ,CAACpG,WAA/F;AACAoL,QAAAA,SAAS,GAAGnI,IAAI,CAAC4F,GAAL,CAAS,IAAIb,eAAe,CAACzB,EAA7B,EAAiC,CAAjC,CAAZ;AACA8E,QAAAA,SAAS,GAAGpI,IAAI,CAAC4F,GAAL,CAAS,IAAIb,eAAe,CAACvB,EAA7B,EAAiC,CAAjC,CAAZ;;AACA,YAAI2E,SAAS,GAAG,CAAZ,IAAiBC,SAAS,GAAG,CAAjC,EAAoC;AAChCrD,UAAAA,eAAe,CAACzB,EAAhB,IAAuB8E,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAkDC,SAAS,GAAGjF,QAAQ,CAACpG,WAAvE,GAAsFoL,SAA5G;AACApD,UAAAA,eAAe,CAACvB,EAAhB,IAAuB4E,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAiDC,SAAjD,GAA6DD,SAAS,GAAGhF,QAAQ,CAACpG,WAAxG;AACH;;AACD;;AACJ,WAAK,UAAL;AACIgI,QAAAA,eAAe,CAACvB,EAAhB,GAAqBuB,eAAe,CAACxB,EAAhB,GAAqB,CAACwB,eAAe,CAAC9D,EAAhB,GAAqB8D,eAAe,CAACzB,EAAtC,IAA4CH,QAAQ,CAACpG,WAA/F;AACAoL,QAAAA,SAAS,GAAGnI,IAAI,CAAC4F,GAAL,CAASb,eAAe,CAAC9D,EAAhB,GAAqB2G,OAAO,CAAChI,KAAtC,EAA6C,CAA7C,CAAZ;AACAwI,QAAAA,SAAS,GAAGpI,IAAI,CAAC4F,GAAL,CAAS,IAAIb,eAAe,CAACvB,EAA7B,EAAiC,CAAjC,CAAZ;;AACA,YAAI2E,SAAS,GAAG,CAAZ,IAAiBC,SAAS,GAAG,CAAjC,EAAoC;AAChCrD,UAAAA,eAAe,CAAC9D,EAAhB,IAAuBmH,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAkDC,SAAS,GAAGjF,QAAQ,CAACpG,WAAvE,GAAsFoL,SAA5G;AACApD,UAAAA,eAAe,CAACvB,EAAhB,IAAuB4E,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAiDC,SAAjD,GAA6DD,SAAS,GAAGhF,QAAQ,CAACpG,WAAxG;AACH;;AACD;;AACJ,WAAK,OAAL;AACA,WAAK,aAAL;AACIgI,QAAAA,eAAe,CAACxB,EAAhB,GAAqBwB,eAAe,CAACvB,EAAhB,GAAqB,CAACuB,eAAe,CAAC9D,EAAhB,GAAqB8D,eAAe,CAACzB,EAAtC,IAA4CH,QAAQ,CAACpG,WAA/F;AACAoL,QAAAA,SAAS,GAAGnI,IAAI,CAAC4F,GAAL,CAASb,eAAe,CAAC9D,EAAhB,GAAqB2G,OAAO,CAAChI,KAAtC,EAA6C,CAA7C,CAAZ;AACAwI,QAAAA,SAAS,GAAGpI,IAAI,CAAC4F,GAAL,CAASb,eAAe,CAACxB,EAAhB,GAAqBqE,OAAO,CAAC/H,MAAtC,EAA8C,CAA9C,CAAZ;;AACA,YAAIsI,SAAS,GAAG,CAAZ,IAAiBC,SAAS,GAAG,CAAjC,EAAoC;AAChCrD,UAAAA,eAAe,CAAC9D,EAAhB,IAAuBmH,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAkDC,SAAS,GAAGjF,QAAQ,CAACpG,WAAvE,GAAsFoL,SAA5G;AACApD,UAAAA,eAAe,CAACxB,EAAhB,IAAuB6E,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAiDC,SAAjD,GAA6DD,SAAS,GAAGhF,QAAQ,CAACpG,WAAxG;AACH;;AACD;;AACJ,WAAK,MAAL;AACA,WAAK,YAAL;AACIgI,QAAAA,eAAe,CAACxB,EAAhB,GAAqBwB,eAAe,CAACvB,EAAhB,GAAqB,CAACuB,eAAe,CAAC9D,EAAhB,GAAqB8D,eAAe,CAACzB,EAAtC,IAA4CH,QAAQ,CAACpG,WAA/F;AACAoL,QAAAA,SAAS,GAAGnI,IAAI,CAAC4F,GAAL,CAAS,IAAIb,eAAe,CAACzB,EAA7B,EAAiC,CAAjC,CAAZ;AACA8E,QAAAA,SAAS,GAAGpI,IAAI,CAAC4F,GAAL,CAASb,eAAe,CAACxB,EAAhB,GAAqBqE,OAAO,CAAC/H,MAAtC,EAA8C,CAA9C,CAAZ;;AACA,YAAIsI,SAAS,GAAG,CAAZ,IAAiBC,SAAS,GAAG,CAAjC,EAAoC;AAChCrD,UAAAA,eAAe,CAACzB,EAAhB,IAAuB8E,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAkDC,SAAS,GAAGjF,QAAQ,CAACpG,WAAvE,GAAsFoL,SAA5G;AACApD,UAAAA,eAAe,CAACxB,EAAhB,IAAuB6E,SAAS,GAAGjF,QAAQ,CAACpG,WAAtB,GAAqCoL,SAArC,GAAiDC,SAAjD,GAA6DD,SAAS,GAAGhF,QAAQ,CAACpG,WAAxG;AACH;;AACD;;AACJ,WAAK,QAAL;AACIgI,QAAAA,eAAe,CAAC9D,EAAhB,GAAqB8D,eAAe,CAACzB,EAAhB,GAAqB,CAACyB,eAAe,CAACxB,EAAhB,GAAqBwB,eAAe,CAACvB,EAAtC,IAA4CL,QAAQ,CAACpG,WAA/F;AACAgI,QAAAA,eAAe,CAACxB,EAAhB,GAAqBwB,eAAe,CAACvB,EAAhB,GAAqB,CAACuB,eAAe,CAAC9D,EAAhB,GAAqB8D,eAAe,CAACzB,EAAtC,IAA4CH,QAAQ,CAACpG,WAA/F;AACA,cAAMsL,UAAU,GAAGrI,IAAI,CAAC4F,GAAL,CAAS,IAAIb,eAAe,CAACzB,EAA7B,EAAiC,CAAjC,CAAnB;AACA,cAAMgF,UAAU,GAAGtI,IAAI,CAAC4F,GAAL,CAASb,eAAe,CAAC9D,EAAhB,GAAqB2G,OAAO,CAAChI,KAAtC,EAA6C,CAA7C,CAAnB;AACA,cAAM2I,UAAU,GAAGvI,IAAI,CAAC4F,GAAL,CAASb,eAAe,CAACxB,EAAhB,GAAqBqE,OAAO,CAAC/H,MAAtC,EAA8C,CAA9C,CAAnB;AACA,cAAM2I,UAAU,GAAGxI,IAAI,CAAC4F,GAAL,CAAS,IAAIb,eAAe,CAACvB,EAA7B,EAAiC,CAAjC,CAAnB;;AACA,YAAI6E,UAAU,GAAG,CAAb,IAAkBC,UAAU,GAAG,CAA/B,IAAoCC,UAAU,GAAG,CAAjD,IAAsDC,UAAU,GAAG,CAAvE,EAA0E;AACtEzD,UAAAA,eAAe,CAACzB,EAAhB,IAAuBiF,UAAU,GAAGpF,QAAQ,CAACpG,WAAvB,GAAsCsL,UAAtC,GAAoDE,UAAU,GAAGpF,QAAQ,CAACpG,WAA1E,GAAyFsL,UAA/G;AACAtD,UAAAA,eAAe,CAAC9D,EAAhB,IAAuBuH,UAAU,GAAGrF,QAAQ,CAACpG,WAAvB,GAAsCuL,UAAtC,GAAoDE,UAAU,GAAGrF,QAAQ,CAACpG,WAA1E,GAAyFuL,UAA/G;AACAvD,UAAAA,eAAe,CAACvB,EAAhB,IAAuBgF,UAAU,GAAGrF,QAAQ,CAACpG,WAAvB,GAAsCuL,UAAtC,GAAmDE,UAAnD,GAAgEF,UAAU,GAAGnF,QAAQ,CAACpG,WAA5G;AACAgI,UAAAA,eAAe,CAACxB,EAAhB,IAAuBgF,UAAU,GAAGpF,QAAQ,CAACpG,WAAvB,GAAsCsL,UAAtC,GAAmDE,UAAnD,GAAgEF,UAAU,GAAGlF,QAAQ,CAACpG,WAA5G;AACH;;AACD;AAtER;AAwEH;;AACDyK,EAAAA,UAAU,CAACH,KAAD,EAAQ;AACd,WAAOA,KAAK,CAACoB,OAAN,GAAgB,CAAhB,EAAmBjJ,OAAnB,IAA8B6H,KAAK,CAAC7H,OAApC,IAA+C,CAAtD;AACH;;AACDkI,EAAAA,UAAU,CAACL,KAAD,EAAQ;AACd,WAAOA,KAAK,CAACoB,OAAN,GAAgB,CAAhB,EAAmBhJ,OAAnB,IAA8B4H,KAAK,CAAC5H,OAApC,IAA+C,CAAtD;AACH;;AA9LwB;;AAgM7BoH,sBAAsB,CAACL,IAAvB;AAAA,mBAAmHK,sBAAnH;AAAA;;AACAA,sBAAsB,CAACJ,KAAvB,kBAxM8F9K,EAwM9F;AAAA,SAAuHkL,sBAAvH;AAAA,WAAuHA,sBAAvH;AAAA,cAA2J;AAA3J;;AACA;AAAA,qDAzM8FlL,EAyM9F,mBAA2FkL,sBAA3F,EAA+H,CAAC;AACpHH,IAAAA,IAAI,EAAE9K,UAD8G;AAEpH+K,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF8G,GAAD,CAA/H;AAAA,K,CAKA;AACA;AACA;;;AACA,MAAM8B,2BAA2B,GAAG,2EAChC,wEADgC,GAEhC,wEAFgC,GAGhC,wEAHgC,GAIhC,wEAJgC,GAKhC,2DALJ;;AAMA,SAASC,yBAAT,GAAqC;AACjC,SAAO,IAAIC,OAAJ,CAAaC,OAAD,IAAa;AAC5B,UAAMpI,GAAG,GAAG,IAAIqI,KAAJ,EAAZ;;AACArI,IAAAA,GAAG,CAACsI,MAAJ,GAAa,MAAM;AACf;AACA,YAAMC,SAAS,GAAGvI,GAAG,CAACb,KAAJ,KAAc,CAAd,IAAmBa,GAAG,CAACZ,MAAJ,KAAe,CAApD;AACAgJ,MAAAA,OAAO,CAACG,SAAD,CAAP;AACH,KAJD;;AAKAvI,IAAAA,GAAG,CAACwI,GAAJ,GAAUP,2BAAV;AACH,GARM,CAAP;AASH;;AACD,SAASQ,8BAAT,CAAwCC,yBAAxC,EAAmE;AAC/D,MAAI,OAAOA,yBAAP,KAAqC,QAAzC,EAAmD;AAC/CA,IAAAA,yBAAyB,GAAGC,eAAe,CAACD,yBAAD,CAA3C;AACH;;AACD,UAAQA,yBAAR;AACI,SAAK,CAAL;AACI,aAAO;AAAE3E,QAAAA,MAAM,EAAE,CAAV;AAAa6E,QAAAA,IAAI,EAAE;AAAnB,OAAP;;AACJ,SAAK,CAAL;AACI,aAAO;AAAE7E,QAAAA,MAAM,EAAE,CAAV;AAAa6E,QAAAA,IAAI,EAAE;AAAnB,OAAP;;AACJ,SAAK,CAAL;AACI,aAAO;AAAE7E,QAAAA,MAAM,EAAE,CAAV;AAAa6E,QAAAA,IAAI,EAAE;AAAnB,OAAP;;AACJ,SAAK,CAAL;AACI,aAAO;AAAE7E,QAAAA,MAAM,EAAE,CAAV;AAAa6E,QAAAA,IAAI,EAAE;AAAnB,OAAP;;AACJ,SAAK,CAAL;AACI,aAAO;AAAE7E,QAAAA,MAAM,EAAE,CAAV;AAAa6E,QAAAA,IAAI,EAAE;AAAnB,OAAP;;AACJ,SAAK,CAAL;AACI,aAAO;AAAE7E,QAAAA,MAAM,EAAE,CAAV;AAAa6E,QAAAA,IAAI,EAAE;AAAnB,OAAP;;AACJ,SAAK,CAAL;AACI,aAAO;AAAE7E,QAAAA,MAAM,EAAE,CAAV;AAAa6E,QAAAA,IAAI,EAAE;AAAnB,OAAP;;AACJ;AACI,aAAO;AAAE7E,QAAAA,MAAM,EAAE,CAAV;AAAa6E,QAAAA,IAAI,EAAE;AAAnB,OAAP;AAhBR;AAkBH;;AACD,SAASD,eAAT,CAAyBE,WAAzB,EAAsC;AAClC,QAAMC,IAAI,GAAG,IAAIC,QAAJ,CAAaC,mBAAmB,CAACH,WAAD,CAAhC,CAAb;;AACA,MAAIC,IAAI,CAACG,SAAL,CAAe,CAAf,EAAkB,KAAlB,MAA6B,MAAjC,EAAyC;AACrC,WAAO,CAAC,CAAR;AACH;;AACD,QAAMnD,MAAM,GAAGgD,IAAI,CAACI,UAApB;AACA,MAAIC,MAAM,GAAG,CAAb;;AACA,SAAOA,MAAM,GAAGrD,MAAhB,EAAwB;AACpB,QAAIgD,IAAI,CAACG,SAAL,CAAeE,MAAM,GAAG,CAAxB,EAA2B,KAA3B,KAAqC,CAAzC,EACI,OAAO,CAAC,CAAR;AACJ,UAAMC,MAAM,GAAGN,IAAI,CAACG,SAAL,CAAeE,MAAf,EAAuB,KAAvB,CAAf;AACAA,IAAAA,MAAM,IAAI,CAAV;;AACA,QAAIC,MAAM,IAAI,MAAd,EAAsB;AAClB,UAAIN,IAAI,CAACO,SAAL,CAAeF,MAAM,IAAI,CAAzB,EAA4B,KAA5B,MAAuC,UAA3C,EAAuD;AACnD,eAAO,CAAC,CAAR;AACH;;AACD,YAAMG,MAAM,GAAGR,IAAI,CAACG,SAAL,CAAeE,MAAM,IAAI,CAAzB,EAA4B,KAA5B,KAAsC,MAArD;AACAA,MAAAA,MAAM,IAAIL,IAAI,CAACO,SAAL,CAAeF,MAAM,GAAG,CAAxB,EAA2BG,MAA3B,CAAV;AACA,YAAMC,IAAI,GAAGT,IAAI,CAACG,SAAL,CAAeE,MAAf,EAAuBG,MAAvB,CAAb;AACAH,MAAAA,MAAM,IAAI,CAAV;;AACA,WAAK,IAAI5I,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgJ,IAApB,EAA0BhJ,CAAC,EAA3B,EAA+B;AAC3B,YAAIuI,IAAI,CAACG,SAAL,CAAeE,MAAM,GAAI5I,CAAC,GAAG,EAA7B,EAAkC+I,MAAlC,KAA6C,MAAjD,EAAyD;AACrD,iBAAOR,IAAI,CAACG,SAAL,CAAeE,MAAM,GAAI5I,CAAC,GAAG,EAAd,GAAoB,CAAnC,EAAsC+I,MAAtC,CAAP;AACH;AACJ;AACJ,KAbD,MAcK,IAAI,CAACF,MAAM,GAAG,MAAV,MAAsB,MAA1B,EAAkC;AACnC;AACH,KAFI,MAGA;AACDD,MAAAA,MAAM,IAAIL,IAAI,CAACG,SAAL,CAAeE,MAAf,EAAuB,KAAvB,CAAV;AACH;AACJ;;AACD,SAAO,CAAC,CAAR;AACH;;AACD,SAASH,mBAAT,CAA6BH,WAA7B,EAA0C;AACtCA,EAAAA,WAAW,GAAGA,WAAW,CAACW,OAAZ,CAAoB,6BAApB,EAAmD,EAAnD,CAAd;AACA,QAAMC,YAAY,GAAGC,IAAI,CAACb,WAAD,CAAzB;AACA,QAAMc,GAAG,GAAGF,YAAY,CAAC3D,MAAzB;AACA,QAAM8D,KAAK,GAAG,IAAIC,UAAJ,CAAeF,GAAf,CAAd;;AACA,OAAK,IAAIpJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoJ,GAApB,EAAyBpJ,CAAC,EAA1B,EAA8B;AAC1BqJ,IAAAA,KAAK,CAACrJ,CAAD,CAAL,GAAWkJ,YAAY,CAACK,UAAb,CAAwBvJ,CAAxB,CAAX;AACH;;AACD,SAAOqJ,KAAK,CAACG,MAAb;AACH;;AAED,MAAMC,gBAAN,CAAuB;AACnB9N,EAAAA,WAAW,GAAG;AACV,SAAK+N,mBAAL,GAA2B/B,yBAAyB,EAApD;AACH;;AACDgC,EAAAA,aAAa,CAACC,IAAD,EAAOC,eAAP,EAAwB;AACjC,WAAO,IAAIjC,OAAJ,CAAY,CAACC,OAAD,EAAUiC,MAAV,KAAqB;AACpC,YAAMC,UAAU,GAAG,IAAIC,UAAJ,EAAnB;;AACAD,MAAAA,UAAU,CAAChC,MAAX,GAAqB1B,KAAD,IAAW;AAC3B,aAAK4D,SAAL,CAAe5D,KAAK,CAAC6D,MAAN,CAAa5E,MAA5B,EAAoCsE,IAAI,CAAClE,IAAzC,EAA+CmE,eAA/C,EACKM,IADL,CACUtC,OADV,EAEKuC,KAFL,CAEWN,MAFX;AAGH,OAJD;;AAKAC,MAAAA,UAAU,CAACM,aAAX,CAAyBT,IAAzB;AACH,KARM,CAAP;AASH;;AACDK,EAAAA,SAAS,CAAC3B,WAAD,EAAcgC,SAAd,EAAyBT,eAAzB,EAA0C;AAC/C,QAAI,CAAC,KAAKU,gBAAL,CAAsBD,SAAtB,CAAL,EAAuC;AACnC,aAAO1C,OAAO,CAACkC,MAAR,CAAe,IAAI5L,KAAJ,CAAU,oBAAV,CAAf,CAAP;AACH;;AACD,WAAO,KAAKsM,eAAL,CAAqBlC,WAArB,EAAkCuB,eAAlC,CAAP;AACH;;AACDU,EAAAA,gBAAgB,CAAC7E,IAAD,EAAO;AACnB,WAAO,oEAAoE+E,IAApE,CAAyE/E,IAAzE,CAAP;AACH;;AACDgF,EAAAA,gBAAgB,CAACC,GAAD,EAAMd,eAAN,EAAuB;AACnC,WAAO,IAAIjC,OAAJ,CAAY,CAACC,OAAD,EAAUiC,MAAV,KAAqB;AACpC,YAAMrK,GAAG,GAAG,IAAIqI,KAAJ,EAAZ;;AACArI,MAAAA,GAAG,CAACmL,OAAJ,GAAc,MAAMd,MAApB;;AACArK,MAAAA,GAAG,CAACsI,MAAJ,GAAa,MAAM;AACf,cAAMpJ,MAAM,GAAG+D,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAf;AACA,cAAMkI,OAAO,GAAGlM,MAAM,CAACa,UAAP,CAAkB,IAAlB,CAAhB;AACAb,QAAAA,MAAM,CAACC,KAAP,GAAea,GAAG,CAACb,KAAnB;AACAD,QAAAA,MAAM,CAACE,MAAP,GAAgBY,GAAG,CAACZ,MAApB;AACAgM,QAAAA,OAAO,EAAEjH,SAAT,CAAmBnE,GAAnB,EAAwB,CAAxB,EAA2B,CAA3B;AACA,aAAK+K,eAAL,CAAqB7L,MAAM,CAAC0F,SAAP,EAArB,EAAyCwF,eAAzC,EAA0DM,IAA1D,CAA+DtC,OAA/D;AACH,OAPD;;AAQApI,MAAAA,GAAG,CAACqL,WAAJ,GAAkB,WAAlB;AACArL,MAAAA,GAAG,CAACwI,GAAJ,GAAU0C,GAAV;AACH,KAbM,CAAP;AAcH;;AACDH,EAAAA,eAAe,CAAClC,WAAD,EAAcuB,eAAd,EAA+B;AAC1C,WAAO,IAAIjC,OAAJ,CAAY,CAACC,OAAD,EAAUiC,MAAV,KAAqB;AACpC,YAAMiB,aAAa,GAAG,IAAIjD,KAAJ,EAAtB;;AACAiD,MAAAA,aAAa,CAAChD,MAAd,GAAuB,MAAMF,OAAO,CAAC;AACjCkD,QAAAA,aADiC;AAEjCC,QAAAA,cAAc,EAAE1C;AAFiB,OAAD,CAApC;;AAIAyC,MAAAA,aAAa,CAACH,OAAd,GAAwBd,MAAxB;AACAiB,MAAAA,aAAa,CAAC9C,GAAd,GAAoBK,WAApB;AACH,KARM,EAQJ6B,IARI,CAQEc,GAAD,IAAS,KAAKC,oBAAL,CAA0BD,GAA1B,EAA+BpB,eAA/B,CARV,CAAP;AASH;;AACKqB,EAAAA,oBAAoB,CAACD,GAAD,EAAMpB,eAAN,EAAuB;AAAA;;AAAA;AAC7C,YAAMsB,UAAU,SAAS,KAAI,CAACzB,mBAA9B;AACA,YAAM7E,aAAa,SAASqD,8BAA8B,CAACiD,UAAU,GAAG,CAAC,CAAJ,GAAQF,GAAG,CAACD,cAAvB,CAA1D;;AACA,UAAI,CAACC,GAAG,CAACF,aAAL,IAAsB,CAACE,GAAG,CAACF,aAAJ,CAAkBK,QAA7C,EAAuD;AACnD,eAAOxD,OAAO,CAACkC,MAAR,CAAe,IAAI5L,KAAJ,CAAU,iBAAV,CAAf,CAAP;AACH;;AACD,YAAM+D,WAAW,GAAG;AAChB+C,QAAAA,QAAQ,EAAE;AACNZ,UAAAA,MAAM,EAAE6G,GAAG,CAACD,cADN;AAENnH,UAAAA,KAAK,EAAEoH,GAAG,CAACF,aAFL;AAGNzH,UAAAA,IAAI,EAAE;AACF1E,YAAAA,KAAK,EAAEqM,GAAG,CAACF,aAAJ,CAAkBM,YADvB;AAEFxM,YAAAA,MAAM,EAAEoM,GAAG,CAACF,aAAJ,CAAkBO;AAFxB;AAHA,SADM;AAShBzG,QAAAA;AATgB,OAApB;AAWA,aAAO,KAAI,CAAC0G,oBAAL,CAA0BtJ,WAA1B,EAAuC4H,eAAvC,CAAP;AAjB6C;AAkBhD;;AACK0B,EAAAA,oBAAoB,CAACtJ,WAAD,EAAc4H,eAAd,EAA+B;AAAA;;AAAA;AACrD,YAAMrN,cAAc,GAAGqN,eAAe,CAACrN,cAAhB,GAAiCyF,WAAW,CAAC4C,aAAZ,CAA0BrB,MAAlF;AACA,YAAMgI,YAAY,GAAG;AACjB5M,QAAAA,KAAK,EAAEqD,WAAW,CAAC+C,QAAZ,CAAqBnB,KAArB,CAA2BwH,YADjB;AAEjBxM,QAAAA,MAAM,EAAEoD,WAAW,CAAC+C,QAAZ,CAAqBnB,KAArB,CAA2ByH;AAFlB,OAArB;;AAIA,UAAI9O,cAAc,KAAK,CAAnB,IAAwB,CAACyF,WAAW,CAAC4C,aAAZ,CAA0BwD,IAAnD,IAA2D,CAACwB,eAAe,CAAC9M,wBAAhF,EAA0G;AACtG,eAAO;AACHiI,UAAAA,QAAQ,EAAE;AACNZ,YAAAA,MAAM,EAAEnC,WAAW,CAAC+C,QAAZ,CAAqBZ,MADvB;AAENP,YAAAA,KAAK,EAAE5B,WAAW,CAAC+C,QAAZ,CAAqBnB,KAFtB;AAGNP,YAAAA,IAAI,EAAE,EAAE,GAAGkI;AAAL;AAHA,WADP;AAMHpI,UAAAA,WAAW,EAAE;AACTgB,YAAAA,MAAM,EAAEnC,WAAW,CAAC+C,QAAZ,CAAqBZ,MADpB;AAETP,YAAAA,KAAK,EAAE5B,WAAW,CAAC+C,QAAZ,CAAqBnB,KAFnB;AAGTP,YAAAA,IAAI,EAAE,EAAE,GAAGkI;AAAL;AAHG,WANV;AAWH3G,UAAAA,aAAa,EAAE5C,WAAW,CAAC4C;AAXxB,SAAP;AAaH;;AACD,YAAM4G,eAAe,GAAG,MAAI,CAACC,kBAAL,CAAwBF,YAAxB,EAAsCvJ,WAAW,CAAC4C,aAAlD,EAAiEgF,eAAjE,CAAxB;;AACA,YAAMlL,MAAM,GAAG+D,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAf;AACAhE,MAAAA,MAAM,CAACC,KAAP,GAAe6M,eAAe,CAAC7M,KAA/B;AACAD,MAAAA,MAAM,CAACE,MAAP,GAAgB4M,eAAe,CAAC5M,MAAhC;AACA,YAAMU,GAAG,GAAGZ,MAAM,CAACa,UAAP,CAAkB,IAAlB,CAAZ;AACAD,MAAAA,GAAG,EAAE8D,YAAL,CAAkBpB,WAAW,CAAC4C,aAAZ,CAA0BwD,IAA1B,GAAiC,CAAC,CAAlC,GAAsC,CAAxD,EAA2D,CAA3D,EAA8D,CAA9D,EAAiE,CAAjE,EAAoE1J,MAAM,CAACC,KAAP,GAAe,CAAnF,EAAsFD,MAAM,CAACE,MAAP,GAAgB,CAAtG;AACAU,MAAAA,GAAG,EAAEiE,MAAL,CAAYxE,IAAI,CAACyE,EAAL,IAAWjH,cAAc,GAAG,CAA5B,CAAZ;AACA+C,MAAAA,GAAG,EAAEqE,SAAL,CAAe3B,WAAW,CAAC+C,QAAZ,CAAqBnB,KAApC,EAA2C,CAAC2H,YAAY,CAAC5M,KAAd,GAAsB,CAAjE,EAAoE,CAAC4M,YAAY,CAAC3M,MAAd,GAAuB,CAA3F;AACA,YAAM8M,iBAAiB,GAAGhN,MAAM,CAAC0F,SAAP,EAA1B;AACA,YAAMlB,gBAAgB,SAAS,MAAI,CAACyI,mBAAL,CAAyBD,iBAAzB,CAA/B;AACA,aAAO;AACH3G,QAAAA,QAAQ,EAAE;AACNZ,UAAAA,MAAM,EAAEnC,WAAW,CAAC+C,QAAZ,CAAqBZ,MADvB;AAENP,UAAAA,KAAK,EAAE5B,WAAW,CAAC+C,QAAZ,CAAqBnB,KAFtB;AAGNP,UAAAA,IAAI,EAAE,EAAE,GAAGkI;AAAL;AAHA,SADP;AAMHpI,QAAAA,WAAW,EAAE;AACTgB,UAAAA,MAAM,EAAEuH,iBADC;AAET9H,UAAAA,KAAK,EAAEV,gBAFE;AAGTG,UAAAA,IAAI,EAAE;AACF1E,YAAAA,KAAK,EAAEuE,gBAAgB,CAACvE,KADtB;AAEFC,YAAAA,MAAM,EAAEsE,gBAAgB,CAACtE;AAFvB;AAHG,SANV;AAcHgG,QAAAA,aAAa,EAAE5C,WAAW,CAAC4C;AAdxB,OAAP;AA/BqD;AA+CxD;;AACD+G,EAAAA,mBAAmB,CAACtD,WAAD,EAAc;AAC7B,WAAO,IAAIV,OAAJ,CAAa,CAACC,OAAD,EAAUiC,MAAV,KAAqB;AACrC,YAAMjG,KAAK,GAAG,IAAIiE,KAAJ,EAAd;;AACAjE,MAAAA,KAAK,CAACkE,MAAN,GAAe,MAAMF,OAAO,CAAChE,KAAD,CAA5B;;AACAA,MAAAA,KAAK,CAAC+G,OAAN,GAAgBd,MAAhB;AACAjG,MAAAA,KAAK,CAACoE,GAAN,GAAYK,WAAZ;AACH,KALM,CAAP;AAMH;;AACDoD,EAAAA,kBAAkB,CAACF,YAAD,EAAe3G,aAAf,EAA8BgF,eAA9B,EAA+C;AAC7D,UAAMrN,cAAc,GAAGqN,eAAe,CAACrN,cAAhB,GAAiCqI,aAAa,CAACrB,MAAtE;;AACA,QAAIqG,eAAe,CAAC9M,wBAApB,EAA8C;AAC1C,UAAIP,cAAc,GAAG,CAArB,EAAwB;AACpB,cAAMqP,iBAAiB,GAAGL,YAAY,CAAC5M,KAAb,GAAqBiL,eAAe,CAAC9N,WAA/D;AACA,cAAM+P,kBAAkB,GAAGN,YAAY,CAAC3M,MAAb,GAAsBgL,eAAe,CAAC9N,WAAjE;AACA,eAAO;AACH6C,UAAAA,KAAK,EAAEI,IAAI,CAAC4F,GAAL,CAAS4G,YAAY,CAAC3M,MAAtB,EAA8BgN,iBAA9B,CADJ;AAEHhN,UAAAA,MAAM,EAAEG,IAAI,CAAC4F,GAAL,CAAS4G,YAAY,CAAC5M,KAAtB,EAA6BkN,kBAA7B;AAFL,SAAP;AAIH,OAPD,MAQK;AACD,cAAMD,iBAAiB,GAAGL,YAAY,CAAC3M,MAAb,GAAsBgL,eAAe,CAAC9N,WAAhE;AACA,cAAM+P,kBAAkB,GAAGN,YAAY,CAAC5M,KAAb,GAAqBiL,eAAe,CAAC9N,WAAhE;AACA,eAAO;AACH6C,UAAAA,KAAK,EAAEI,IAAI,CAAC4F,GAAL,CAAS4G,YAAY,CAAC5M,KAAtB,EAA6BiN,iBAA7B,CADJ;AAEHhN,UAAAA,MAAM,EAAEG,IAAI,CAAC4F,GAAL,CAAS4G,YAAY,CAAC3M,MAAtB,EAA8BiN,kBAA9B;AAFL,SAAP;AAIH;AACJ;;AACD,QAAItP,cAAc,GAAG,CAArB,EAAwB;AACpB,aAAO;AACHqC,QAAAA,MAAM,EAAE2M,YAAY,CAAC5M,KADlB;AAEHA,QAAAA,KAAK,EAAE4M,YAAY,CAAC3M;AAFjB,OAAP;AAIH;;AACD,WAAO;AACHD,MAAAA,KAAK,EAAE4M,YAAY,CAAC5M,KADjB;AAEHC,MAAAA,MAAM,EAAE2M,YAAY,CAAC3M;AAFlB,KAAP;AAIH;;AA5JkB;;AA8JvB4K,gBAAgB,CAACjE,IAAjB;AAAA,mBAA6GiE,gBAA7G;AAAA;;AACAA,gBAAgB,CAAChE,KAAjB,kBAtc8F9K,EAsc9F;AAAA,SAAiH8O,gBAAjH;AAAA,WAAiHA,gBAAjH;AAAA,cAA+I;AAA/I;;AACA;AAAA,qDAvc8F9O,EAuc9F,mBAA2F8O,gBAA3F,EAAyH,CAAC;AAC9G/D,IAAAA,IAAI,EAAE9K,UADwG;AAE9G+K,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFwG,GAAD,CAAzH;AAAA;;AAKA,MAAMmG,qBAAN,CAA4B;AACxBpQ,EAAAA,WAAW,CAACqQ,WAAD,EAAcC,sBAAd,EAAsCC,gBAAtC,EAAwDC,SAAxD,EAAmEC,EAAnE,EAAuE;AAC9E,SAAKJ,WAAL,GAAmBA,WAAnB;AACA,SAAKC,sBAAL,GAA8BA,sBAA9B;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,EAAL,GAAUA,EAAV;AACA,SAAKC,MAAL,GAAcC,MAAM,GAAG,QAAH,CAAN,IAAsB,IAApC;AACA,SAAKnK,QAAL,GAAgB,IAAIzG,eAAJ,EAAhB;AACA,SAAK6Q,sBAAL,GAA8B,CAA9B;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAK5F,OAAL,GAAe;AACXhI,MAAAA,KAAK,EAAE,CADI;AAEXC,MAAAA,MAAM,EAAE;AAFG,KAAf;AAIA,SAAK4N,SAAL,GAAiBtO,SAAjB;AACA,SAAKuO,YAAL,GAAoB,KAApB;AACA,SAAK9Q,MAAL,GAAc,KAAKuG,QAAL,CAAcvG,MAA5B;AACA,SAAKE,SAAL,GAAiB,EAAjB;AACA,SAAKD,mBAAL,GAA2B,KAAKsG,QAAL,CAActG,mBAAzC;AACA,SAAKE,WAAL,GAAmB,KAAKoG,QAAL,CAAcpG,WAAjC;AACA,SAAKC,aAAL,GAAqB,KAAKmG,QAAL,CAAcnG,aAAnC;AACA,SAAKC,cAAL,GAAsB,KAAKkG,QAAL,CAAclG,cAApC;AACA,SAAKC,eAAL,GAAuB,KAAKiG,QAAL,CAAcjG,eAArC;AACA,SAAKC,gBAAL,GAAwB,KAAKgG,QAAL,CAAchG,gBAAtC;AACA,SAAKC,gBAAL,GAAwB,KAAK+F,QAAL,CAAc/F,gBAAtC;AACA,SAAKC,eAAL,GAAuB,KAAK8F,QAAL,CAAc9F,eAArC;AACA,SAAKC,kBAAL,GAA0B,KAAK6F,QAAL,CAAc7F,kBAAxC;AACA,SAAKC,mBAAL,GAA2B,KAAK4F,QAAL,CAAc5F,mBAAzC;AACA,SAAKC,cAAL,GAAsB,KAAK2F,QAAL,CAAc3F,cAApC;AACA,SAAKC,eAAL,GAAuB,KAAK0F,QAAL,CAAc1F,eAArC;AACA,SAAKC,YAAL,GAAoB,KAAKyF,QAAL,CAAczF,YAAlC;AACA,SAAKC,aAAL,GAAqB,KAAKwF,QAAL,CAAcxF,aAAnC;AACA,SAAKC,YAAL,GAAoB,KAAKuF,QAAL,CAAcvF,YAAlC;AACA,SAAKC,QAAL,GAAgB,KAAKsF,QAAL,CAActF,QAA9B;AACA,SAAKC,eAAL,GAAuB,KAAKqF,QAAL,CAAcrF,eAArC;AACA,SAAKC,wBAAL,GAAgC,KAAKoF,QAAL,CAAcpF,wBAA9C;AACA,SAAKC,iBAAL,GAAyB,KAAKmF,QAAL,CAAcnF,iBAAvC;AACA,SAAKkF,OAAL,GAAe;AACXI,MAAAA,EAAE,EAAE,CAAC,GADM;AAEXE,MAAAA,EAAE,EAAE,CAAC,GAFM;AAGXvC,MAAAA,EAAE,EAAE,KAHO;AAIXsC,MAAAA,EAAE,EAAE;AAJO,KAAf;AAMA,SAAKtF,UAAL,GAAkB,KAAKkF,QAAL,CAAclF,UAAhC;AACA,SAAK0P,QAAL,GAAgB,KAAhB;AACA,SAAKC,YAAL,GAAoB,IAAI/R,YAAJ,EAApB;AACA,SAAKgS,cAAL,GAAsB,IAAIhS,YAAJ,EAAtB;AACA,SAAKiS,WAAL,GAAmB,IAAIjS,YAAJ,EAAnB;AACA,SAAKkS,YAAL,GAAoB,IAAIlS,YAAJ,EAApB;AACA,SAAKmS,eAAL,GAAuB,IAAInS,YAAJ,EAAvB;AACA,SAAKoS,KAAL;AACH;;AACDC,EAAAA,WAAW,CAAClP,OAAD,EAAU;AACjB,SAAKmP,uBAAL,CAA6BnP,OAA7B;AACA,SAAKoP,mBAAL,CAAyBpP,OAAzB;;AACA,QAAI,KAAKiE,WAAL,EAAkB+C,QAAlB,CAA2BnB,KAA3B,CAAiCuH,QAAjC,KAA8CpN,OAAO,CAAC,0BAAD,CAAP,IAAuCA,OAAO,CAAC,gBAAD,CAA5F,CAAJ,EAAqH;AACjH,WAAKkO,gBAAL,CACKX,oBADL,CAC0B,KAAKtJ,WAD/B,EAC4C,KAAKE,QADjD,EAEKgI,IAFL,CAEWc,GAAD,IAAS,KAAKoC,cAAL,CAAoBpC,GAApB,CAFnB,EAGKb,KAHL,CAGYkD,GAAD,IAAS,KAAKC,cAAL,CAAoBD,GAApB,CAHpB;AAIH;;AACD,QAAItP,OAAO,CAAC,SAAD,CAAP,IAAsBA,OAAO,CAAC,qBAAD,CAA7B,IAAwDA,OAAO,CAAC,aAAD,CAAnE,EAAoF;AAChF,WAAKwP,UAAL;AACA,WAAKC,uBAAL;AACA,WAAKC,uBAAL;;AACA,UAAI,KAAK7R,mBAAL,KAA6BmC,OAAO,CAAC,qBAAD,CAAP,IAAkCA,OAAO,CAAC,aAAD,CAAtE,CAAJ,EAA4F;AACxF,aAAK8H,oBAAL;AACH,OAFD,MAGK,IAAI9H,OAAO,CAAC,SAAD,CAAX,EAAwB;AACzB,aAAK2P,oBAAL,CAA0B,KAA1B;AACA,aAAKC,UAAL;AACH;;AACD,WAAKxB,EAAL,CAAQyB,YAAR;AACH;;AACD,QAAI7P,OAAO,CAAC,WAAD,CAAX,EAA0B;AACtB,WAAKlC,SAAL,GAAiB,KAAKA,SAAL,IAAkB,EAAnC;AACA,WAAKgS,eAAL;AACA,WAAKF,UAAL;AACH;AACJ;;AACDT,EAAAA,uBAAuB,CAACnP,OAAD,EAAU;AAC7B,SAAKmE,QAAL,CAAcpE,qBAAd,CAAoCC,OAApC;;AACA,QAAI,KAAKmE,QAAL,CAAc5F,mBAAd,IAAqC,KAAK4F,QAAL,CAAc7F,kBAAvD,EAA2E;AACvE,WAAK6F,QAAL,CAAc5E,UAAd,CAAyB;AACrBP,QAAAA,iBAAiB,EAAE,IADE;AAErBd,QAAAA,eAAe,EAAE,KAAKiG,QAAL,CAAc7F,kBAFV;AAGrBH,QAAAA,gBAAgB,EAAE,KAAKgG,QAAL,CAAc5F,mBAHX;AAIrBH,QAAAA,gBAAgB,EAAE,KAAK+F,QAAL,CAAc5F,mBAJX;AAKrBF,QAAAA,eAAe,EAAE,KAAK8F,QAAL,CAAc7F,kBALV;AAMrBT,QAAAA,mBAAmB,EAAE;AANA,OAAzB;AAQH;AACJ;;AACDuR,EAAAA,mBAAmB,CAACpP,OAAD,EAAU;AACzB,QAAIA,OAAO,CAAC,mBAAD,CAAP,IAAgCA,OAAO,CAAC,UAAD,CAAvC,IAAuDA,OAAO,CAAC,aAAD,CAA9D,IAAiFA,OAAO,CAAC,WAAD,CAA5F,EAA2G;AACvG,WAAKiP,KAAL;AACH;;AACD,QAAIjP,OAAO,CAAC,mBAAD,CAAP,IAAgC,KAAK+P,wBAAL,EAApC,EAAqE;AACjE,WAAKpE,aAAL,CAAmB,KAAKqE,iBAAL,CAAuB9D,MAAvB,CAA8B+D,KAA9B,CAAoC,CAApC,CAAnB;AACH;;AACD,QAAIjQ,OAAO,CAAC,UAAD,CAAP,IAAuB,KAAKkQ,QAAhC,EAA0C;AACtC,WAAKxD,gBAAL,CAAsB,KAAKwD,QAA3B;AACH;;AACD,QAAIlQ,OAAO,CAAC,aAAD,CAAP,IAA0B,KAAKsK,WAAnC,EAAgD;AAC5C,WAAKkC,eAAL,CAAqB,KAAKlC,WAA1B;AACH;;AACD,QAAItK,OAAO,CAAC,WAAD,CAAP,IAAwB,KAAKmQ,SAAjC,EAA4C;AACxC,WAAKxE,aAAL,CAAmB,KAAKwE,SAAxB;AACH;AACJ;;AACDJ,EAAAA,wBAAwB,GAAG;AACvB,WAAO,KAAKC,iBAAL,EAAwB9D,MAAxB,EAAgC+D,KAAhC,EAAuC1I,MAAvC,GAAgD,CAAvD;AACH;;AACDuI,EAAAA,eAAe,GAAG;AACd,SAAKM,kBAAL,GAA0B,KAAKjC,SAAL,CAAekC,wBAAf,CAAwC,YAAY,CAAC,KAAKvS,SAAL,CAAeiH,KAAf,IAAwB,CAAzB,KAA+B,KAAKjH,SAAL,CAAekH,KAAf,GAAuB,CAAC,CAAxB,GAA4B,CAA3D,CAAZ,GAA4E,GAA5E,GAC9D,SAD8D,GAClD,CAAC,KAAKlH,SAAL,CAAeiH,KAAf,IAAwB,CAAzB,KAA+B,KAAKjH,SAAL,CAAeoH,KAAf,GAAuB,CAAC,CAAxB,GAA4B,CAA3D,CADkD,GACc,GADd,GAE9D,SAF8D,IAEjD,KAAKpH,SAAL,CAAe0H,MAAf,IAAyB,CAFwB,IAEnB,MAFmB,GAG7D,aAAY,KAAK1H,SAAL,CAAe4H,UAAf,IAA6B,CAAE,MAAK,KAAK5H,SAAL,CAAe6H,UAAf,IAA6B,CAAE,IAH1D,CAA1B;AAIH;;AACD2K,EAAAA,QAAQ,GAAG;AACP,SAAKnM,QAAL,CAAc7E,QAAd,GAAyB,KAAKb,eAA9B;AACA,SAAK8R,oBAAL;AACH;;AACDtB,EAAAA,KAAK,GAAG;AACJ,SAAKP,YAAL,GAAoB,KAApB;AACA,SAAKzK,WAAL,GAAmBuM,SAAnB;AACA,SAAKC,cAAL,GAAsB,qCAChB,2DADgB,GAEhB,2BAFN;AAGA,SAAKnI,SAAL,GAAiB;AACboI,MAAAA,MAAM,EAAE,KADK;AAEbhJ,MAAAA,IAAI,EAAE,IAFO;AAGbqB,MAAAA,QAAQ,EAAE,IAHG;AAIbzE,MAAAA,EAAE,EAAE,CAJS;AAKbE,MAAAA,EAAE,EAAE,CALS;AAMbvC,MAAAA,EAAE,EAAE,CANS;AAObsC,MAAAA,EAAE,EAAE,CAPS;AAQb/D,MAAAA,OAAO,EAAE,CARI;AASbC,MAAAA,OAAO,EAAE;AATI,KAAjB;AAWA,SAAKmI,OAAL,GAAe;AACXhI,MAAAA,KAAK,EAAE,CADI;AAEXC,MAAAA,MAAM,EAAE;AAFG,KAAf;AAIA,SAAKqD,OAAL,CAAaI,EAAb,GAAkB,CAAC,GAAnB;AACA,SAAKJ,OAAL,CAAaM,EAAb,GAAkB,CAAC,GAAnB;AACA,SAAKN,OAAL,CAAajC,EAAb,GAAkB,KAAlB;AACA,SAAKiC,OAAL,CAAaK,EAAb,GAAkB,KAAlB;AACH;;AACDoH,EAAAA,aAAa,CAACC,IAAD,EAAO;AAChB,SAAKsC,gBAAL,CACKvC,aADL,CACmBC,IADnB,EACyB,KAAKzH,QAD9B,EAEKgI,IAFL,CAEWc,GAAD,IAAS,KAAKoC,cAAL,CAAoBpC,GAApB,CAFnB,EAGKb,KAHL,CAGYkD,GAAD,IAAS,KAAKC,cAAL,CAAoBD,GAApB,CAHpB;AAIH;;AACD9C,EAAAA,eAAe,CAAClC,WAAD,EAAc;AACzB,SAAK4D,gBAAL,CACK1B,eADL,CACqBlC,WADrB,EACkC,KAAKnG,QADvC,EAEKgI,IAFL,CAEWc,GAAD,IAAS,KAAKoC,cAAL,CAAoBpC,GAApB,CAFnB,EAGKb,KAHL,CAGYkD,GAAD,IAAS,KAAKC,cAAL,CAAoBD,GAApB,CAHpB;AAIH;;AACD5C,EAAAA,gBAAgB,CAACC,GAAD,EAAM;AAClB,SAAKuB,gBAAL,CACKxB,gBADL,CACsBC,GADtB,EAC2B,KAAKxI,QADhC,EAEKgI,IAFL,CAEWc,GAAD,IAAS,KAAKoC,cAAL,CAAoBpC,GAApB,CAFnB,EAGKb,KAHL,CAGYkD,GAAD,IAAS,KAAKC,cAAL,CAAoBD,GAApB,CAHpB;AAIH;;AACDD,EAAAA,cAAc,CAACpL,WAAD,EAAc;AACxB,SAAKA,WAAL,GAAmBA,WAAnB;AACA,SAAKwM,cAAL,GAAsB,KAAKtC,SAAL,CAAewC,8BAAf,CAA8C1M,WAAW,CAACmB,WAAZ,CAAwBgB,MAAtE,CAAtB;AACA,SAAKgI,EAAL,CAAQyB,YAAR;AACH;;AACDN,EAAAA,cAAc,CAACqB,KAAD,EAAQ;AAClBC,IAAAA,OAAO,CAACD,KAAR,CAAcA,KAAd;AACA,SAAK5B,eAAL,CAAqB8B,IAArB;AACH;;AACDC,EAAAA,iBAAiB,GAAG;AAChB,QAAI,KAAK9M,WAAL,IAAoB,IAAxB,EAA8B;AAC1B,WAAK6K,WAAL,CAAiBgC,IAAjB,CAAsB,KAAK7M,WAA3B;AACA,WAAKsK,sBAAL,GAA8B,CAA9B;AACAyC,MAAAA,UAAU,CAAC,MAAM,KAAKC,4BAAL,EAAP,CAAV;AACH;AACJ;;AACDA,EAAAA,4BAA4B,GAAG;AAC3B,QAAI,KAAK1C,sBAAL,GAA8B,EAAlC,EAAsC;AAClC,WAAKS,eAAL,CAAqB8B,IAArB;AACH,KAFD,MAGK,IAAI,KAAKI,iBAAL,EAAJ,EAA8B;AAC/B,WAAK1B,UAAL;AACA,WAAKC,uBAAL;AACA,WAAKC,uBAAL;AACA,WAAK5H,oBAAL;AACA,WAAKiH,YAAL,CAAkB+B,IAAlB,CAAuB,EAAE,GAAG,KAAKlI;AAAV,OAAvB;AACA,WAAKwF,EAAL,CAAQyB,YAAR;AACH,KAPI,MAQA;AACD,WAAKtB,sBAAL;AACAyC,MAAAA,UAAU,CAAC,MAAM,KAAKC,4BAAL,EAAP,EAA4C,EAA5C,CAAV;AACH;AACJ;;AACDC,EAAAA,iBAAiB,GAAG;AAChB,WAAO,KAAKlN,WAAL,EAAkBwC,aAAlB,EAAiCE,WAAjC,GAA+C,CAAtD;AACH;;AACDyK,EAAAA,QAAQ,GAAG;AACP,QAAI,CAAC,KAAKlN,WAAV,EAAuB;AACnB;AACH;;AACD,SAAKmN,qBAAL;AACA,SAAK5B,UAAL;AACA,SAAKC,uBAAL;AACA,SAAKC,uBAAL;AACH;;AACDa,EAAAA,oBAAoB,GAAG;AACnB,QAAI,KAAKlC,MAAT,EAAiB;AACb,YAAMgD,MAAM,GAAG,IAAI,KAAKhD,MAAT,CAAgB,KAAKiD,OAAL,CAAa9K,aAA7B,CAAf;AACA6K,MAAAA,MAAM,CAACE,GAAP,CAAW,OAAX,EAAoBC,GAApB,CAAwB;AAAEC,QAAAA,MAAM,EAAE;AAAV,OAAxB;AACAJ,MAAAA,MAAM,CAACK,EAAP,CAAU,WAAV,EAAuB,KAAKC,OAAL,CAAaC,IAAb,CAAkB,IAAlB,CAAvB;AACAP,MAAAA,MAAM,CAACK,EAAP,CAAU,UAAV,EAAsB,KAAKG,SAAL,CAAeD,IAAf,CAAoB,IAApB,CAAtB;AACAP,MAAAA,MAAM,CAACK,EAAP,CAAU,YAAV,EAAwB,KAAKI,UAAL,CAAgBF,IAAhB,CAAqB,IAArB,CAAxB;AACH,KAND,MAOK,IAAI9U,SAAS,EAAb,EAAiB;AAClB+T,MAAAA,OAAO,CAACkB,IAAR,CAAa,uEAAb;AACH;AACJ;;AACDX,EAAAA,qBAAqB,GAAG;AACpB,UAAM7K,kBAAkB,GAAG,KAAKvC,WAAL,CAAiBwC,aAA5C;;AACA,QAAI,KAAKoC,OAAL,CAAahI,KAAb,KAAuB2F,kBAAkB,CAACG,WAA1C,IAAyD,KAAKkC,OAAL,CAAa/H,MAAb,KAAwB0F,kBAAkB,CAACwB,YAAxG,EAAsH;AAClH,WAAK7D,OAAL,CAAaI,EAAb,GAAkB,KAAKJ,OAAL,CAAaI,EAAb,GAAkBiC,kBAAkB,CAACG,WAArC,GAAmD,KAAKkC,OAAL,CAAahI,KAAlF;AACA,WAAKsD,OAAL,CAAajC,EAAb,GAAkB,KAAKiC,OAAL,CAAajC,EAAb,GAAkBsE,kBAAkB,CAACG,WAArC,GAAmD,KAAKkC,OAAL,CAAahI,KAAlF;AACA,WAAKsD,OAAL,CAAaM,EAAb,GAAkB,KAAKN,OAAL,CAAaM,EAAb,GAAkB+B,kBAAkB,CAACwB,YAArC,GAAoD,KAAKa,OAAL,CAAa/H,MAAnF;AACA,WAAKqD,OAAL,CAAaK,EAAb,GAAkB,KAAKL,OAAL,CAAaK,EAAb,GAAkBgC,kBAAkB,CAACwB,YAArC,GAAoD,KAAKa,OAAL,CAAa/H,MAAnF;AACH;AACJ;;AACDiH,EAAAA,oBAAoB,GAAG;AACnB,SAAKmG,sBAAL,CAA4BnG,oBAA5B,CAAiD,KAAK9D,WAAtD,EAAmE,KAAKE,OAAxE,EAAiF,KAAKC,QAAtF;AACA,SAAKyL,UAAL;AACA,SAAKlB,YAAL,GAAoB,IAApB;AACH;;AACDsD,EAAAA,cAAc,CAAC3J,KAAD,EAAQ;AAClB,SAAK4J,sBAAL,CAA4B5J,KAA5B;AACA,SAAK6J,mBAAL,CAAyB7J,KAAzB;AACH;;AACD4J,EAAAA,sBAAsB,CAAC5J,KAAD,EAAQ;AAC1B,UAAMhI,GAAG,GAAG,CAACgI,KAAK,CAAChI,GAAnB;;AACA,QAAIA,GAAG,IAAI,CAAP,IAAYA,GAAG,IAAI,CAAvB,EAA0B;AACtB,WAAK8D,QAAL,CAAc7E,QAAd,GAAyBe,GAAzB;AACH;AACJ;;AACD6R,EAAAA,mBAAmB,CAAC7J,KAAD,EAAQ;AACvB,UAAM8J,iBAAiB,GAAG,CAAC,SAAD,EAAY,WAAZ,EAAyB,YAAzB,EAAuC,WAAvC,CAA1B;;AACA,QAAI,CAAEA,iBAAiB,CAACC,QAAlB,CAA2B/J,KAAK,CAAChI,GAAjC,CAAN,EAA8C;AAC1C;AACH;;AACD,UAAMgS,QAAQ,GAAGhK,KAAK,CAACiK,QAAN,GAAiBnS,SAAS,CAACoS,MAA3B,GAAoCpS,SAAS,CAACqS,IAA/D;AACA,UAAMzJ,QAAQ,GAAGV,KAAK,CAACoK,MAAN,GAAenS,yBAAyB,CAAC+H,KAAK,CAAChI,GAAP,CAAxC,GAAsDD,iBAAiB,CAACiI,KAAK,CAAChI,GAAP,CAAxF;AACA,UAAMqS,SAAS,GAAGnS,cAAc,CAAC8H,KAAK,CAAChI,GAAP,EAAY,KAAK8D,QAAL,CAAc7E,QAA1B,CAAhC;AACA+I,IAAAA,KAAK,CAACsK,cAAN;AACAtK,IAAAA,KAAK,CAACuK,eAAN;AACA,SAAKC,SAAL,CAAe;AAAErS,MAAAA,OAAO,EAAE,CAAX;AAAcC,MAAAA,OAAO,EAAE;AAAvB,KAAf,EAA2C4R,QAA3C,EAAqDtJ,QAArD;AACA,SAAK+J,OAAL,CAAaJ,SAAb;AACA,SAAKK,QAAL;AACH;;AACDF,EAAAA,SAAS,CAACxK,KAAD,EAAQgK,QAAR,EAAkBtJ,QAAQ,GAAG,IAA7B,EAAmC;AACxC,QAAI,KAAKT,SAAL,EAAgBoI,MAAhB,IAA0B,KAAKpI,SAAL,EAAgBZ,IAAhB,KAAyBvH,SAAS,CAAC6S,KAAjE,EAAwE;AACpE;AACH;;AACD,QAAI3K,KAAK,CAACsK,cAAV,EAA0B;AACtBtK,MAAAA,KAAK,CAACsK,cAAN;AACH;;AACD,SAAKrK,SAAL,GAAiB;AACboI,MAAAA,MAAM,EAAE,IADK;AAEbhJ,MAAAA,IAAI,EAAE2K,QAFO;AAGbtJ,MAAAA,QAHa;AAIbvI,MAAAA,OAAO,EAAE,KAAKyN,sBAAL,CAA4BzF,UAA5B,CAAuCH,KAAvC,CAJI;AAKb5H,MAAAA,OAAO,EAAE,KAAKwN,sBAAL,CAA4BvF,UAA5B,CAAuCL,KAAvC,CALI;AAMb,SAAG,KAAKnE;AANK,KAAjB;AAQH;;AACD4N,EAAAA,UAAU,CAACzJ,KAAD,EAAQ;AACd,QAAI,CAAC,KAAKoI,cAAV,EAA0B;AACtB;AACH;;AACD,QAAIpI,KAAK,CAACsK,cAAV,EAA0B;AACtBtK,MAAAA,KAAK,CAACsK,cAAN;AACH;;AACD,SAAKrK,SAAL,GAAiB;AACboI,MAAAA,MAAM,EAAE,IADK;AAEbhJ,MAAAA,IAAI,EAAEvH,SAAS,CAAC6S,KAFH;AAGbjK,MAAAA,QAAQ,EAAE,QAHG;AAIbvI,MAAAA,OAAO,EAAE,KAAK0D,OAAL,CAAaI,EAAb,GAAkB,CAAC,KAAKJ,OAAL,CAAajC,EAAb,GAAkB,KAAKiC,OAAL,CAAaI,EAAhC,IAAsC,CAJpD;AAKb7D,MAAAA,OAAO,EAAE,KAAKyD,OAAL,CAAaM,EAAb,GAAkB,CAAC,KAAKN,OAAL,CAAaK,EAAb,GAAkB,KAAKL,OAAL,CAAaM,EAAhC,IAAsC,CALpD;AAMb,SAAG,KAAKN;AANK,KAAjB;AAQH;;AACD4O,EAAAA,OAAO,CAACzK,KAAD,EAAQ;AACX,QAAI,KAAKC,SAAL,CAAeoI,MAAnB,EAA2B;AACvB,UAAIrI,KAAK,CAACuK,eAAV,EAA2B;AACvBvK,QAAAA,KAAK,CAACuK,eAAN;AACH;;AACD,UAAIvK,KAAK,CAACsK,cAAV,EAA0B;AACtBtK,QAAAA,KAAK,CAACsK,cAAN;AACH;;AACD,UAAI,KAAKrK,SAAL,CAAeZ,IAAf,KAAwBvH,SAAS,CAACqS,IAAtC,EAA4C;AACxC,aAAKvE,sBAAL,CAA4B7F,IAA5B,CAAiCC,KAAjC,EAAwC,KAAKC,SAA7C,EAAwD,KAAKpE,OAA7D;AACA,aAAKyL,oBAAL,CAA0B,IAA1B;AACH,OAHD,MAIK,IAAI,KAAKrH,SAAL,CAAeZ,IAAf,KAAwBvH,SAAS,CAACoS,MAAtC,EAA8C;AAC/C,YAAI,CAAC,KAAKjU,kBAAN,IAA4B,CAAC,KAAKC,mBAAtC,EAA2D;AACvD,eAAK0P,sBAAL,CAA4BtF,MAA5B,CAAmCN,KAAnC,EAA0C,KAAKC,SAA/C,EAA0D,KAAKpE,OAA/D,EAAwE,KAAK0E,OAA7E,EAAsF,KAAKzE,QAA3F;AACH;;AACD,aAAKwL,oBAAL,CAA0B,KAA1B;AACH;;AACD,WAAKvB,EAAL,CAAQ6E,aAAR;AACH;AACJ;;AACDtB,EAAAA,OAAO,CAACtJ,KAAD,EAAQ;AACX,QAAI,KAAKC,SAAL,CAAeoI,MAAnB,EAA2B;AACvB,UAAIrI,KAAK,CAACuK,eAAV,EAA2B;AACvBvK,QAAAA,KAAK,CAACuK,eAAN;AACH;;AACD,UAAIvK,KAAK,CAACsK,cAAV,EAA0B;AACtBtK,QAAAA,KAAK,CAACsK,cAAN;AACH;;AACD,UAAI,KAAKrK,SAAL,CAAeZ,IAAf,KAAwBvH,SAAS,CAAC6S,KAAtC,EAA6C;AACzC,aAAK/E,sBAAL,CAA4BtF,MAA5B,CAAmCN,KAAnC,EAA0C,KAAKC,SAA/C,EAA0D,KAAKpE,OAA/D,EAAwE,KAAK0E,OAA7E,EAAsF,KAAKzE,QAA3F;AACA,aAAKwL,oBAAL,CAA0B,KAA1B;AACH;;AACD,WAAKvB,EAAL,CAAQ6E,aAAR;AACH;AACJ;;AACDzD,EAAAA,UAAU,GAAG;AACT,QAAI,KAAKxL,WAAT,EAAsB;AAClB,YAAMuC,kBAAkB,GAAG,KAAKvC,WAAL,CAAiBwC,aAA5C;AACA,WAAKoC,OAAL,CAAahI,KAAb,GAAqB2F,kBAAkB,CAACG,WAAxC;AACA,WAAKkC,OAAL,CAAa/H,MAAb,GAAsB0F,kBAAkB,CAACwB,YAAzC;AACA,WAAKyG,UAAL,GAAkB,KAAKL,SAAL,CAAekC,wBAAf,CAAwC,gBAAgB,KAAKzH,OAAL,CAAahI,KAAb,GAAqB,CAArC,GAAyC,KAAjF,CAAlB;AACH;AACJ;;AACD6O,EAAAA,uBAAuB,GAAG;AACtB,QAAI,KAAKxL,WAAL,EAAkBmB,WAAlB,EAA+BS,KAAnC,EAA0C;AACtC,WAAKqN,wBAAL;AACA,WAAKC,yBAAL;AACH,KAHD,MAIK;AACD,WAAKhP,QAAL,CAAcjF,qBAAd,GAAsC,EAAtC;AACA,WAAKiF,QAAL,CAAchF,sBAAd,GAAuC,EAAvC;AACH;AACJ;;AACD+T,EAAAA,wBAAwB,GAAG;AACvB,SAAK/O,QAAL,CAAcjF,qBAAd,GAAsC,KAAKhB,eAAL,GAAuB,CAAvB,GAChC8C,IAAI,CAAC4F,GAAL,CAAS,EAAT,EAAa,KAAK1I,eAAL,GAAuB,KAAK+F,WAAL,CAAiBmB,WAAjB,CAA6BS,KAA7B,CAAmCjF,KAA1D,GAAkE,KAAKgI,OAAL,CAAahI,KAA5F,CADgC,GAEhC,EAFN;AAGH;;AACDuS,EAAAA,yBAAyB,GAAG;AACxB,QAAI,KAAKtV,mBAAT,EAA8B;AAC1B,WAAKsG,QAAL,CAAchF,sBAAd,GAAuC6B,IAAI,CAAC4F,GAAL,CAAS,EAAT,EAAa,KAAKzC,QAAL,CAAcjF,qBAAd,GAAsC,KAAKnB,WAAxD,CAAvC;AACH,KAFD,MAGK,IAAI,KAAKI,gBAAL,GAAwB,CAA5B,EAA+B;AAChC,WAAKgG,QAAL,CAAchF,sBAAd,GAAuC6B,IAAI,CAAC4F,GAAL,CAAS,EAAT,EAAa,KAAKzI,gBAAL,GAAwB,KAAK8F,WAAL,CAAiBmB,WAAjB,CAA6BS,KAA7B,CAAmChF,MAA3D,GAAoE,KAAK+H,OAAL,CAAa/H,MAA9F,CAAvC;AACH,KAFI,MAGA;AACD,WAAKsD,QAAL,CAAchF,sBAAd,GAAuC,EAAvC;AACH;AACJ;;AACDuQ,EAAAA,uBAAuB,GAAG;AACtB,QAAI,KAAKzL,WAAL,EAAkBmB,WAAlB,EAA+BS,KAAnC,EAA0C;AACtC,YAAMY,KAAK,GAAG,KAAKxC,WAAL,CAAiBmB,WAAjB,CAA6BE,IAA7B,CAAkC1E,KAAlC,GAA0C,KAAKgI,OAAL,CAAahI,KAArE;AACA,WAAKuD,QAAL,CAAc/E,qBAAd,GAAsC,KAAKf,eAAL,GAAuB,EAAvB,GAA4B,KAAKA,eAAL,GAAuBoI,KAAnD,GAA2D,KAAKmC,OAAL,CAAahI,KAA9G;AACA,WAAKuD,QAAL,CAAc9E,sBAAd,GAAuC,KAAKjB,gBAAL,GAAwB,EAAxB,GAA6B,KAAKA,gBAAL,GAAwBqI,KAArD,GAA6D,KAAKmC,OAAL,CAAa/H,MAAjH;;AACA,UAAI,KAAKhD,mBAAT,EAA8B;AAC1B,YAAI,KAAKsG,QAAL,CAAc/E,qBAAd,GAAsC,KAAK+E,QAAL,CAAc9E,sBAAd,GAAuC,KAAKtB,WAAtF,EAAmG;AAC/F,eAAKoG,QAAL,CAAc/E,qBAAd,GAAsC,KAAK+E,QAAL,CAAc9E,sBAAd,GAAuC,KAAKtB,WAAlF;AACH,SAFD,MAGK,IAAI,KAAKoG,QAAL,CAAc/E,qBAAd,GAAsC,KAAK+E,QAAL,CAAc9E,sBAAd,GAAuC,KAAKtB,WAAtF,EAAmG;AACpG,eAAKoG,QAAL,CAAc9E,sBAAd,GAAuC,KAAK8E,QAAL,CAAc/E,qBAAd,GAAsC,KAAKrB,WAAlF;AACH;AACJ;AACJ,KAZD,MAaK;AACD,WAAKoG,QAAL,CAAc/E,qBAAd,GAAsC,KAAKwJ,OAAL,CAAahI,KAAnD;AACA,WAAKuD,QAAL,CAAc9E,sBAAd,GAAuC,KAAKuJ,OAAL,CAAa/H,MAApD;AACH;AACJ;;AACD8O,EAAAA,oBAAoB,CAACyD,YAAY,GAAG,KAAhB,EAAuB;AACvC,QAAI,KAAKlP,OAAL,CAAaI,EAAb,GAAkB,CAAtB,EAAyB;AACrB,WAAKJ,OAAL,CAAajC,EAAb,IAAmBmR,YAAY,GAAG,KAAKlP,OAAL,CAAaI,EAAhB,GAAqB,CAApD;AACA,WAAKJ,OAAL,CAAaI,EAAb,GAAkB,CAAlB;AACH;;AACD,QAAI,KAAKJ,OAAL,CAAaM,EAAb,GAAkB,CAAtB,EAAyB;AACrB,WAAKN,OAAL,CAAaK,EAAb,IAAmB6O,YAAY,GAAG,KAAKlP,OAAL,CAAaM,EAAhB,GAAqB,CAApD;AACA,WAAKN,OAAL,CAAaM,EAAb,GAAkB,CAAlB;AACH;;AACD,QAAI,KAAKN,OAAL,CAAajC,EAAb,GAAkB,KAAK2G,OAAL,CAAahI,KAAnC,EAA0C;AACtC,WAAKsD,OAAL,CAAaI,EAAb,IAAmB8O,YAAY,GAAI,KAAKlP,OAAL,CAAajC,EAAb,GAAkB,KAAK2G,OAAL,CAAahI,KAAnC,GAA4C,CAA3E;AACA,WAAKsD,OAAL,CAAajC,EAAb,GAAkB,KAAK2G,OAAL,CAAahI,KAA/B;AACH;;AACD,QAAI,KAAKsD,OAAL,CAAaK,EAAb,GAAkB,KAAKqE,OAAL,CAAa/H,MAAnC,EAA2C;AACvC,WAAKqD,OAAL,CAAaM,EAAb,IAAmB4O,YAAY,GAAI,KAAKlP,OAAL,CAAaK,EAAb,GAAkB,KAAKqE,OAAL,CAAa/H,MAAnC,GAA6C,CAA5E;AACA,WAAKqD,OAAL,CAAaK,EAAb,GAAkB,KAAKqE,OAAL,CAAa/H,MAA/B;AACH;AACJ;;AACDkS,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKzK,SAAL,CAAeoI,MAAnB,EAA2B;AACvB,WAAKpI,SAAL,CAAeoI,MAAf,GAAwB,KAAxB;AACA,WAAKd,UAAL;AACH;AACJ;;AACDiC,EAAAA,SAAS,GAAG;AACR,QAAI,KAAKvJ,SAAL,CAAeoI,MAAnB,EAA2B;AACvB,WAAKpI,SAAL,CAAeoI,MAAf,GAAwB,KAAxB;AACA,WAAKd,UAAL;AACH;AACJ;;AACDA,EAAAA,UAAU,GAAG;AACT,QAAI,KAAK/Q,QAAT,EAAmB;AACf,WAAKkF,IAAL;AACH;AACJ;;AACDA,EAAAA,IAAI,GAAG;AACH,QAAI,KAAKE,WAAL,EAAkBmB,WAAlB,EAA+BS,KAA/B,IAAwC,IAA5C,EAAkD;AAC9C,WAAKgJ,cAAL,CAAoBiC,IAApB;AACA,YAAMhL,MAAM,GAAG,KAAKkI,WAAL,CAAiBjK,IAAjB,CAAsB,KAAKC,WAA3B,EAAwC,KAAKC,WAA7C,EAA0D,KAAKC,OAA/D,EAAwE,KAAKC,QAA7E,CAAf;;AACA,UAAI2B,MAAM,IAAI,IAAd,EAAoB;AAChB,aAAK8I,YAAL,CAAkBkC,IAAlB,CAAuBhL,MAAvB;AACH;;AACD,aAAOA,MAAP;AACH;;AACD,WAAO,IAAP;AACH;;AA7auB;;AA+a5BiI,qBAAqB,CAACvG,IAAtB;AAAA,mBAAkHuG,qBAAlH,EA33B8FpR,EA23B9F,mBAAyJmH,WAAzJ,GA33B8FnH,EA23B9F,mBAAiLkL,sBAAjL,GA33B8FlL,EA23B9F,mBAAoN8O,gBAApN,GA33B8F9O,EA23B9F,mBAAiPY,EAAE,CAAC8V,YAApP,GA33B8F1W,EA23B9F,mBAA6QA,EAAE,CAAC2W,iBAAhR;AAAA;;AACAvF,qBAAqB,CAACwF,IAAtB,kBA53B8F5W,EA43B9F;AAAA,QAAsGoR,qBAAtG;AAAA;AAAA;AAAA;AA53B8FpR,MAAAA,EA43B9F;AA53B8FA,MAAAA,EA43B9F;AAAA;;AAAA;AAAA;;AA53B8FA,MAAAA,EA43B9F,qBA53B8FA,EA43B9F;AA53B8FA,MAAAA,EA43B9F,qBA53B8FA,EA43B9F;AAAA;AAAA;AAAA;AAAA;AAAA;AA53B8FA,MAAAA,EA43B9F;AAAA,eAAsG,cAAtG;AAAA,gBA53B8FA,EA43B9F;AAAA,eAAsG,mBAAtG;AAAA,gBA53B8FA,EA43B9F;AAAA,eAAsG,mBAAtG;AAAA,gBA53B8FA,EA43B9F;AAAA,eAAsG,cAAtG;AAAA,gBA53B8FA,EA43B9F;AAAA,eAAsG,cAAtG;AAAA,gBA53B8FA,EA43B9F;AAAA;;AAAA;AA53B8FA,MAAAA,EA43B9F;AA53B8FA,MAAAA,EA43B9F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA53B8FA,EA43B9F;AAAA;AAAA;AAAA;AAAA;AAAA;AA53B8FA,MAAAA,EA43BqqD,kCAAnwD;AA53B8FA,MAAAA,EA43BwvD,oEAAt1D;AA53B8FA,MAAAA,EA43B2hE,uBAAznE;AA53B8FA,MAAAA,EA43B2vE,qEAAz1E;AA53B8FA,MAAAA,EA43BwsL,eAAtyL;AAAA;;AAAA;AA53B8FA,MAAAA,EA43B0qD,mEAAxwD;AA53B8FA,MAAAA,EA43B8zD,aAA55D;AA53B8FA,MAAAA,EA43B8zD,uCAA55D;AA53B8FA,MAAAA,EA43B2kE,aAAzqE;AA53B8FA,MAAAA,EA43B2kE,sJAAzqE;AA53B8FA,MAAAA,EA43BoyE,aAAl4E;AA53B8FA,MAAAA,EA43BoyE,qCAAl4E;AAAA;AAAA;AAAA,eAAwlSa,EAAE,CAACgW,IAA3lS;AAAA;AAAA;AAAA;;AACA;AAAA,qDA73B8F7W,EA63B9F,mBAA2FoR,qBAA3F,EAA8H,CAAC;AACnHrG,IAAAA,IAAI,EAAE3K,SAD6G;AAEnH4K,IAAAA,IAAI,EAAE,CAAC;AAAE8L,MAAAA,QAAQ,EAAE,eAAZ;AAA6BC,MAAAA,eAAe,EAAE1W,uBAAuB,CAAC2W,MAAtE;AAA8EC,MAAAA,QAAQ,EAAE,6iIAAxF;AAAuoIC,MAAAA,MAAM,EAAE,CAAC,swGAAD;AAA/oI,KAAD;AAF6G,GAAD,CAA9H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEnM,MAAAA,IAAI,EAAE5D;AAAR,KAAD,EAAwB;AAAE4D,MAAAA,IAAI,EAAEG;AAAR,KAAxB,EAA0D;AAAEH,MAAAA,IAAI,EAAE+D;AAAR,KAA1D,EAAsF;AAAE/D,MAAAA,IAAI,EAAEnK,EAAE,CAAC8V;AAAX,KAAtF,EAAiH;AAAE3L,MAAAA,IAAI,EAAE/K,EAAE,CAAC2W;AAAX,KAAjH,CAAP;AAA0J,GAHpM,EAGsN;AAAEhC,IAAAA,OAAO,EAAE,CAAC;AAClN5J,MAAAA,IAAI,EAAEzK,SAD4M;AAElN0K,MAAAA,IAAI,EAAE,CAAC,SAAD,EAAY;AAAEmM,QAAAA,MAAM,EAAE;AAAV,OAAZ;AAF4M,KAAD,CAAX;AAGtM9P,IAAAA,WAAW,EAAE,CAAC;AACd0D,MAAAA,IAAI,EAAEzK,SADQ;AAEd0K,MAAAA,IAAI,EAAE,CAAC,aAAD,EAAgB;AAAEmM,QAAAA,MAAM,EAAE;AAAV,OAAhB;AAFQ,KAAD,CAHyL;AAMtM9D,IAAAA,iBAAiB,EAAE,CAAC;AACpBtI,MAAAA,IAAI,EAAExK;AADc,KAAD,CANmL;AAQtMgT,IAAAA,QAAQ,EAAE,CAAC;AACXxI,MAAAA,IAAI,EAAExK;AADK,KAAD,CAR4L;AAUtMoN,IAAAA,WAAW,EAAE,CAAC;AACd5C,MAAAA,IAAI,EAAExK;AADQ,KAAD,CAVyL;AAYtMiT,IAAAA,SAAS,EAAE,CAAC;AACZzI,MAAAA,IAAI,EAAExK;AADM,KAAD,CAZ2L;AActMU,IAAAA,MAAM,EAAE,CAAC;AACT8J,MAAAA,IAAI,EAAExK;AADG,KAAD,CAd8L;AAgBtMY,IAAAA,SAAS,EAAE,CAAC;AACZ4J,MAAAA,IAAI,EAAExK;AADM,KAAD,CAhB2L;AAkBtMW,IAAAA,mBAAmB,EAAE,CAAC;AACtB6J,MAAAA,IAAI,EAAExK;AADgB,KAAD,CAlBiL;AAoBtMa,IAAAA,WAAW,EAAE,CAAC;AACd2J,MAAAA,IAAI,EAAExK;AADQ,KAAD,CApByL;AAsBtMc,IAAAA,aAAa,EAAE,CAAC;AAChB0J,MAAAA,IAAI,EAAExK;AADU,KAAD,CAtBuL;AAwBtMe,IAAAA,cAAc,EAAE,CAAC;AACjByJ,MAAAA,IAAI,EAAExK;AADW,KAAD,CAxBsL;AA0BtMgB,IAAAA,eAAe,EAAE,CAAC;AAClBwJ,MAAAA,IAAI,EAAExK;AADY,KAAD,CA1BqL;AA4BtMiB,IAAAA,gBAAgB,EAAE,CAAC;AACnBuJ,MAAAA,IAAI,EAAExK;AADa,KAAD,CA5BoL;AA8BtMkB,IAAAA,gBAAgB,EAAE,CAAC;AACnBsJ,MAAAA,IAAI,EAAExK;AADa,KAAD,CA9BoL;AAgCtMmB,IAAAA,eAAe,EAAE,CAAC;AAClBqJ,MAAAA,IAAI,EAAExK;AADY,KAAD,CAhCqL;AAkCtMoB,IAAAA,kBAAkB,EAAE,CAAC;AACrBoJ,MAAAA,IAAI,EAAExK;AADe,KAAD,CAlCkL;AAoCtMqB,IAAAA,mBAAmB,EAAE,CAAC;AACtBmJ,MAAAA,IAAI,EAAExK;AADgB,KAAD,CApCiL;AAsCtMsB,IAAAA,cAAc,EAAE,CAAC;AACjBkJ,MAAAA,IAAI,EAAExK;AADW,KAAD,CAtCsL;AAwCtMuB,IAAAA,eAAe,EAAE,CAAC;AAClBiJ,MAAAA,IAAI,EAAExK;AADY,KAAD,CAxCqL;AA0CtMwB,IAAAA,YAAY,EAAE,CAAC;AACfgJ,MAAAA,IAAI,EAAExK;AADS,KAAD,CA1CwL;AA4CtMyB,IAAAA,aAAa,EAAE,CAAC;AAChB+I,MAAAA,IAAI,EAAExK;AADU,KAAD,CA5CuL;AA8CtM0B,IAAAA,YAAY,EAAE,CAAC;AACf8I,MAAAA,IAAI,EAAExK;AADS,KAAD,CA9CwL;AAgDtM2B,IAAAA,QAAQ,EAAE,CAAC;AACX6I,MAAAA,IAAI,EAAExK;AADK,KAAD,CAhD4L;AAkDtM4B,IAAAA,eAAe,EAAE,CAAC;AAClB4I,MAAAA,IAAI,EAAExK;AADY,KAAD,CAlDqL;AAoDtM6B,IAAAA,wBAAwB,EAAE,CAAC;AAC3B2I,MAAAA,IAAI,EAAExK;AADqB,KAAD,CApD4K;AAsDtM8B,IAAAA,iBAAiB,EAAE,CAAC;AACpB0I,MAAAA,IAAI,EAAExK;AADc,KAAD,CAtDmL;AAwDtMgH,IAAAA,OAAO,EAAE,CAAC;AACVwD,MAAAA,IAAI,EAAExK;AADI,KAAD,CAxD6L;AA0DtM+B,IAAAA,UAAU,EAAE,CAAC;AACbyI,MAAAA,IAAI,EAAEvK,WADO;AAEbwK,MAAAA,IAAI,EAAE,CAAC,kBAAD;AAFO,KAAD,EAGb;AACCD,MAAAA,IAAI,EAAExK;AADP,KAHa,CA1D0L;AA+DtMyR,IAAAA,QAAQ,EAAE,CAAC;AACXjH,MAAAA,IAAI,EAAEvK,WADK;AAEXwK,MAAAA,IAAI,EAAE,CAAC,gBAAD;AAFK,KAAD,EAGX;AACCD,MAAAA,IAAI,EAAExK;AADP,KAHW,CA/D4L;AAoEtM0R,IAAAA,YAAY,EAAE,CAAC;AACflH,MAAAA,IAAI,EAAEtK;AADS,KAAD,CApEwL;AAsEtMyR,IAAAA,cAAc,EAAE,CAAC;AACjBnH,MAAAA,IAAI,EAAEtK;AADW,KAAD,CAtEsL;AAwEtM0R,IAAAA,WAAW,EAAE,CAAC;AACdpH,MAAAA,IAAI,EAAEtK;AADQ,KAAD,CAxEyL;AA0EtM2R,IAAAA,YAAY,EAAE,CAAC;AACfrH,MAAAA,IAAI,EAAEtK;AADS,KAAD,CA1EwL;AA4EtM4R,IAAAA,eAAe,EAAE,CAAC;AAClBtH,MAAAA,IAAI,EAAEtK;AADY,KAAD,CA5EqL;AA8EtM+T,IAAAA,QAAQ,EAAE,CAAC;AACXzJ,MAAAA,IAAI,EAAErK,YADK;AAEXsK,MAAAA,IAAI,EAAE,CAAC,eAAD;AAFK,KAAD,CA9E4L;AAiFtMmL,IAAAA,OAAO,EAAE,CAAC;AACVpL,MAAAA,IAAI,EAAErK,YADI;AAEVsK,MAAAA,IAAI,EAAE,CAAC,oBAAD,EAAuB,CAAC,QAAD,CAAvB;AAFI,KAAD,EAGV;AACCD,MAAAA,IAAI,EAAErK,YADP;AAECsK,MAAAA,IAAI,EAAE,CAAC,oBAAD,EAAuB,CAAC,QAAD,CAAvB;AAFP,KAHU,CAjF6L;AAuFtMoL,IAAAA,QAAQ,EAAE,CAAC;AACXrL,MAAAA,IAAI,EAAErK,YADK;AAEXsK,MAAAA,IAAI,EAAE,CAAC,kBAAD;AAFK,KAAD,EAGX;AACCD,MAAAA,IAAI,EAAErK,YADP;AAECsK,MAAAA,IAAI,EAAE,CAAC,mBAAD;AAFP,KAHW;AAvF4L,GAHtN;AAAA;;AAkGA,MAAMoM,kBAAN,CAAyB;;AAEzBA,kBAAkB,CAACvM,IAAnB;AAAA,mBAA+GuM,kBAA/G;AAAA;;AACAA,kBAAkB,CAACC,IAAnB,kBAl+B8FrX,EAk+B9F;AAAA,QAAgHoX;AAAhH;AACAA,kBAAkB,CAACE,IAAnB,kBAn+B8FtX,EAm+B9F;AAAA,YAA8I,CAClIc,YADkI,CAA9I;AAAA;;AAGA;AAAA,qDAt+B8Fd,EAs+B9F,mBAA2FoX,kBAA3F,EAA2H,CAAC;AAChHrM,IAAAA,IAAI,EAAEpK,QAD0G;AAEhHqK,IAAAA,IAAI,EAAE,CAAC;AACCuM,MAAAA,OAAO,EAAE,CACLzW,YADK,CADV;AAIC0W,MAAAA,YAAY,EAAE,CACVpG,qBADU,CAJf;AAOCqG,MAAAA,OAAO,EAAE,CACLrG,qBADK;AAPV,KAAD;AAF0G,GAAD,CAA3H;AAAA;;AAeA,SAASsG,YAAT,CAAsBC,WAAtB,EAAmC;AAC/B,QAAMC,KAAK,GAAGD,WAAW,CAACC,KAAZ,CAAkB,GAAlB,CAAd;AACA,QAAM7M,IAAI,GAAG6M,KAAK,CAAC,CAAD,CAAL,CAAStJ,OAAT,CAAiB,OAAjB,EAA0B,EAA1B,EAA8BA,OAA9B,CAAsC,SAAtC,EAAiD,EAAjD,CAAb;AACA,QAAMuJ,UAAU,GAAGrJ,IAAI,CAACoJ,KAAK,CAAC,CAAD,CAAN,CAAvB;AACA,QAAME,EAAE,GAAG,IAAIC,WAAJ,CAAgBF,UAAU,CAACjN,MAA3B,CAAX;AACA,QAAMoN,EAAE,GAAG,IAAIrJ,UAAJ,CAAemJ,EAAf,CAAX;;AACA,OAAK,IAAIzS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwS,UAAU,CAACjN,MAA/B,EAAuCvF,CAAC,IAAI,CAA5C,EAA+C;AAC3C2S,IAAAA,EAAE,CAAC3S,CAAD,CAAF,GAAQwS,UAAU,CAACjJ,UAAX,CAAsBvJ,CAAtB,CAAR;AACH;;AACD,SAAO,IAAI4S,IAAJ,CAAS,CAACH,EAAD,CAAT,EAAe;AAAE/M,IAAAA;AAAF,GAAf,CAAP;AACH;AAED;AACA;AACA;;;AAEA,SAASqG,qBAAT,EAAgCgG,kBAAhC,EAAoDM,YAApD,EAAkE3T,YAAlE", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, isDevMode, Component, ChangeDetectionStrategy, ViewChild, Input, HostBinding, Output, HostListener, NgModule } from '@angular/core';\nimport * as i4 from '@angular/platform-browser';\nimport * as i5 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass CropperSettings {\n    constructor() {\n        // From options\n        this.format = 'png';\n        this.maintainAspectRatio = true;\n        this.transform = {};\n        this.aspectRatio = 1;\n        this.resizeToWidth = 0;\n        this.resizeToHeight = 0;\n        this.cropperMinWidth = 0;\n        this.cropperMinHeight = 0;\n        this.cropperMaxHeight = 0;\n        this.cropperMaxWidth = 0;\n        this.cropperStaticWidth = 0;\n        this.cropperStaticHeight = 0;\n        this.canvasRotation = 0;\n        this.initialStepSize = 3;\n        this.roundCropper = false;\n        this.onlyScaleDown = false;\n        this.imageQuality = 92;\n        this.autoCrop = true;\n        this.backgroundColor = null;\n        this.containWithinAspectRatio = false;\n        this.hideResizeSquares = false;\n        this.alignImage = 'center';\n        // Internal\n        this.cropperScaledMinWidth = 20;\n        this.cropperScaledMinHeight = 20;\n        this.cropperScaledMaxWidth = 20;\n        this.cropperScaledMaxHeight = 20;\n        this.stepSize = this.initialStepSize;\n    }\n    setOptions(options) {\n        Object.keys(options)\n            .filter((k) => k in this)\n            .forEach((k) => this[k] = options[k]);\n        this.validateOptions();\n    }\n    setOptionsFromChanges(changes) {\n        Object.keys(changes)\n            .filter((k) => k in this)\n            .forEach((k) => this[k] = changes[k].currentValue);\n        this.validateOptions();\n    }\n    validateOptions() {\n        if (this.maintainAspectRatio && !this.aspectRatio) {\n            throw new Error('`aspectRatio` should > 0 when `maintainAspectRatio` is enabled');\n        }\n    }\n}\n\nvar MoveTypes;\n(function (MoveTypes) {\n    MoveTypes[\"Move\"] = \"move\";\n    MoveTypes[\"Resize\"] = \"resize\";\n    MoveTypes[\"Pinch\"] = \"pinch\";\n})(MoveTypes || (MoveTypes = {}));\n\nfunction getPositionForKey(key) {\n    switch (key) {\n        case 'ArrowUp':\n            return 'top';\n        case 'ArrowRight':\n            return 'right';\n        case 'ArrowDown':\n            return 'bottom';\n        case 'ArrowLeft':\n        default:\n            return 'left';\n    }\n}\nfunction getInvertedPositionForKey(key) {\n    switch (key) {\n        case 'ArrowUp':\n            return 'bottom';\n        case 'ArrowRight':\n            return 'left';\n        case 'ArrowDown':\n            return 'top';\n        case 'ArrowLeft':\n        default:\n            return 'right';\n    }\n}\nfunction getEventForKey(key, stepSize) {\n    switch (key) {\n        case 'ArrowUp':\n            return { clientX: 0, clientY: stepSize * -1 };\n        case 'ArrowRight':\n            return { clientX: stepSize, clientY: 0 };\n        case 'ArrowDown':\n            return { clientX: 0, clientY: stepSize };\n        case 'ArrowLeft':\n        default:\n            return { clientX: stepSize * -1, clientY: 0 };\n    }\n}\n\n/*\n * Hermite resize - fast image resize/resample using Hermite filter.\n * https://github.com/viliusle/Hermite-resize\n */\nfunction resizeCanvas(canvas, width, height) {\n    const width_source = canvas.width;\n    const height_source = canvas.height;\n    width = Math.round(width);\n    height = Math.round(height);\n    const ratio_w = width_source / width;\n    const ratio_h = height_source / height;\n    const ratio_w_half = Math.ceil(ratio_w / 2);\n    const ratio_h_half = Math.ceil(ratio_h / 2);\n    const ctx = canvas.getContext('2d');\n    if (ctx) {\n        const img = ctx.getImageData(0, 0, width_source, height_source);\n        const img2 = ctx.createImageData(width, height);\n        const data = img.data;\n        const data2 = img2.data;\n        for (let j = 0; j < height; j++) {\n            for (let i = 0; i < width; i++) {\n                const x2 = (i + j * width) * 4;\n                const center_y = j * ratio_h;\n                let weight = 0;\n                let weights = 0;\n                let weights_alpha = 0;\n                let gx_r = 0;\n                let gx_g = 0;\n                let gx_b = 0;\n                let gx_a = 0;\n                const xx_start = Math.floor(i * ratio_w);\n                const yy_start = Math.floor(j * ratio_h);\n                let xx_stop = Math.ceil((i + 1) * ratio_w);\n                let yy_stop = Math.ceil((j + 1) * ratio_h);\n                xx_stop = Math.min(xx_stop, width_source);\n                yy_stop = Math.min(yy_stop, height_source);\n                for (let yy = yy_start; yy < yy_stop; yy++) {\n                    const dy = Math.abs(center_y - yy) / ratio_h_half;\n                    const center_x = i * ratio_w;\n                    const w0 = dy * dy; //pre-calc part of w\n                    for (let xx = xx_start; xx < xx_stop; xx++) {\n                        const dx = Math.abs(center_x - xx) / ratio_w_half;\n                        const w = Math.sqrt(w0 + dx * dx);\n                        if (w >= 1) {\n                            //pixel too far\n                            continue;\n                        }\n                        //hermite filter\n                        weight = 2 * w * w * w - 3 * w * w + 1;\n                        const pos_x = 4 * (xx + yy * width_source);\n                        //alpha\n                        gx_a += weight * data[pos_x + 3];\n                        weights_alpha += weight;\n                        //colors\n                        if (data[pos_x + 3] < 255)\n                            weight = weight * data[pos_x + 3] / 250;\n                        gx_r += weight * data[pos_x];\n                        gx_g += weight * data[pos_x + 1];\n                        gx_b += weight * data[pos_x + 2];\n                        weights += weight;\n                    }\n                }\n                data2[x2] = gx_r / weights;\n                data2[x2 + 1] = gx_g / weights;\n                data2[x2 + 2] = gx_b / weights;\n                data2[x2 + 3] = gx_a / weights_alpha;\n            }\n        }\n        canvas.width = width;\n        canvas.height = height;\n        //draw\n        ctx.putImageData(img2, 0, 0);\n    }\n}\n\nfunction percentage(percent, totalValue) {\n    return (percent / 100) * totalValue;\n}\n\nclass CropService {\n    crop(sourceImage, loadedImage, cropper, settings) {\n        const imagePosition = this.getImagePosition(sourceImage, loadedImage, cropper, settings);\n        const width = imagePosition.x2 - imagePosition.x1;\n        const height = imagePosition.y2 - imagePosition.y1;\n        const cropCanvas = document.createElement('canvas');\n        cropCanvas.width = width;\n        cropCanvas.height = height;\n        const ctx = cropCanvas.getContext('2d');\n        if (!ctx) {\n            return null;\n        }\n        if (settings.backgroundColor != null) {\n            ctx.fillStyle = settings.backgroundColor;\n            ctx.fillRect(0, 0, width, height);\n        }\n        const scaleX = (settings.transform.scale || 1) * (settings.transform.flipH ? -1 : 1);\n        const scaleY = (settings.transform.scale || 1) * (settings.transform.flipV ? -1 : 1);\n        const transformedImage = loadedImage.transformed;\n        ctx.setTransform(scaleX, 0, 0, scaleY, transformedImage.size.width / 2, transformedImage.size.height / 2);\n        ctx.translate(-imagePosition.x1 / scaleX, -imagePosition.y1 / scaleY);\n        ctx.rotate((settings.transform.rotate || 0) * Math.PI / 180);\n        const translateH = settings.transform.translateH ? percentage(settings.transform.translateH, transformedImage.size.width) : 0;\n        const translateV = settings.transform.translateV ? percentage(settings.transform.translateV, transformedImage.size.height) : 0;\n        ctx.drawImage(transformedImage.image, translateH - transformedImage.size.width / 2, translateV - transformedImage.size.height / 2);\n        const output = {\n            width, height,\n            imagePosition,\n            cropperPosition: { ...cropper }\n        };\n        if (settings.containWithinAspectRatio) {\n            output.offsetImagePosition = this.getOffsetImagePosition(sourceImage, loadedImage, cropper, settings);\n        }\n        const resizeRatio = this.getResizeRatio(width, height, settings);\n        if (resizeRatio !== 1) {\n            output.width = Math.round(width * resizeRatio);\n            output.height = settings.maintainAspectRatio\n                ? Math.round(output.width / settings.aspectRatio)\n                : Math.round(height * resizeRatio);\n            resizeCanvas(cropCanvas, output.width, output.height);\n        }\n        output.base64 = cropCanvas.toDataURL('image/' + settings.format, this.getQuality(settings));\n        return output;\n    }\n    getImagePosition(sourceImage, loadedImage, cropper, settings) {\n        const sourceImageElement = sourceImage.nativeElement;\n        const ratio = loadedImage.transformed.size.width / sourceImageElement.offsetWidth;\n        const out = {\n            x1: Math.round(cropper.x1 * ratio),\n            y1: Math.round(cropper.y1 * ratio),\n            x2: Math.round(cropper.x2 * ratio),\n            y2: Math.round(cropper.y2 * ratio)\n        };\n        if (!settings.containWithinAspectRatio) {\n            out.x1 = Math.max(out.x1, 0);\n            out.y1 = Math.max(out.y1, 0);\n            out.x2 = Math.min(out.x2, loadedImage.transformed.size.width);\n            out.y2 = Math.min(out.y2, loadedImage.transformed.size.height);\n        }\n        return out;\n    }\n    getOffsetImagePosition(sourceImage, loadedImage, cropper, settings) {\n        const canvasRotation = settings.canvasRotation + loadedImage.exifTransform.rotate;\n        const sourceImageElement = sourceImage.nativeElement;\n        const ratio = loadedImage.transformed.size.width / sourceImageElement.offsetWidth;\n        let offsetX;\n        let offsetY;\n        if (canvasRotation % 2) {\n            offsetX = (loadedImage.transformed.size.width - loadedImage.original.size.height) / 2;\n            offsetY = (loadedImage.transformed.size.height - loadedImage.original.size.width) / 2;\n        }\n        else {\n            offsetX = (loadedImage.transformed.size.width - loadedImage.original.size.width) / 2;\n            offsetY = (loadedImage.transformed.size.height - loadedImage.original.size.height) / 2;\n        }\n        const out = {\n            x1: Math.round(cropper.x1 * ratio) - offsetX,\n            y1: Math.round(cropper.y1 * ratio) - offsetY,\n            x2: Math.round(cropper.x2 * ratio) - offsetX,\n            y2: Math.round(cropper.y2 * ratio) - offsetY\n        };\n        if (!settings.containWithinAspectRatio) {\n            out.x1 = Math.max(out.x1, 0);\n            out.y1 = Math.max(out.y1, 0);\n            out.x2 = Math.min(out.x2, loadedImage.transformed.size.width);\n            out.y2 = Math.min(out.y2, loadedImage.transformed.size.height);\n        }\n        return out;\n    }\n    getResizeRatio(width, height, settings) {\n        const ratioWidth = settings.resizeToWidth / width;\n        const ratioHeight = settings.resizeToHeight / height;\n        const ratios = new Array();\n        if (settings.resizeToWidth > 0) {\n            ratios.push(ratioWidth);\n        }\n        if (settings.resizeToHeight > 0) {\n            ratios.push(ratioHeight);\n        }\n        const result = ratios.length === 0 ? 1 : Math.min(...ratios);\n        if (result > 1 && !settings.onlyScaleDown) {\n            return result;\n        }\n        return Math.min(result, 1);\n    }\n    getQuality(settings) {\n        return Math.min(1, Math.max(0, settings.imageQuality / 100));\n    }\n}\nCropService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: CropService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nCropService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: CropService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: CropService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nclass CropperPositionService {\n    resetCropperPosition(sourceImage, cropperPosition, settings) {\n        if (!sourceImage?.nativeElement) {\n            return;\n        }\n        const sourceImageElement = sourceImage.nativeElement;\n        if (settings.cropperStaticHeight && settings.cropperStaticWidth) {\n            cropperPosition.x1 = 0;\n            cropperPosition.x2 = sourceImageElement.offsetWidth > settings.cropperStaticWidth ?\n                settings.cropperStaticWidth : sourceImageElement.offsetWidth;\n            cropperPosition.y1 = 0;\n            cropperPosition.y2 = sourceImageElement.offsetHeight > settings.cropperStaticHeight ?\n                settings.cropperStaticHeight : sourceImageElement.offsetHeight;\n        }\n        else {\n            const cropperWidth = Math.min(settings.cropperScaledMaxWidth, sourceImageElement.offsetWidth);\n            const cropperHeight = Math.min(settings.cropperScaledMaxHeight, sourceImageElement.offsetHeight);\n            if (!settings.maintainAspectRatio) {\n                cropperPosition.x1 = 0;\n                cropperPosition.x2 = cropperWidth;\n                cropperPosition.y1 = 0;\n                cropperPosition.y2 = cropperHeight;\n            }\n            else if (sourceImageElement.offsetWidth / settings.aspectRatio < sourceImageElement.offsetHeight) {\n                cropperPosition.x1 = 0;\n                cropperPosition.x2 = cropperWidth;\n                const cropperHeightWithAspectRatio = cropperWidth / settings.aspectRatio;\n                cropperPosition.y1 = (sourceImageElement.offsetHeight - cropperHeightWithAspectRatio) / 2;\n                cropperPosition.y2 = cropperPosition.y1 + cropperHeightWithAspectRatio;\n            }\n            else {\n                cropperPosition.y1 = 0;\n                cropperPosition.y2 = cropperHeight;\n                const cropperWidthWithAspectRatio = cropperHeight * settings.aspectRatio;\n                cropperPosition.x1 = (sourceImageElement.offsetWidth - cropperWidthWithAspectRatio) / 2;\n                cropperPosition.x2 = cropperPosition.x1 + cropperWidthWithAspectRatio;\n            }\n        }\n    }\n    move(event, moveStart, cropperPosition) {\n        const diffX = this.getClientX(event) - moveStart.clientX;\n        const diffY = this.getClientY(event) - moveStart.clientY;\n        cropperPosition.x1 = moveStart.x1 + diffX;\n        cropperPosition.y1 = moveStart.y1 + diffY;\n        cropperPosition.x2 = moveStart.x2 + diffX;\n        cropperPosition.y2 = moveStart.y2 + diffY;\n    }\n    resize(event, moveStart, cropperPosition, maxSize, settings) {\n        const moveX = this.getClientX(event) - moveStart.clientX;\n        const moveY = this.getClientY(event) - moveStart.clientY;\n        switch (moveStart.position) {\n            case 'left':\n                cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n                break;\n            case 'topleft':\n                cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n                cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n                break;\n            case 'top':\n                cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n                break;\n            case 'topright':\n                cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n                cropperPosition.y1 = Math.min(Math.max(moveStart.y1 + moveY, cropperPosition.y2 - settings.cropperScaledMaxHeight), cropperPosition.y2 - settings.cropperScaledMinHeight);\n                break;\n            case 'right':\n                cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n                break;\n            case 'bottomright':\n                cropperPosition.x2 = Math.max(Math.min(moveStart.x2 + moveX, cropperPosition.x1 + settings.cropperScaledMaxWidth), cropperPosition.x1 + settings.cropperScaledMinWidth);\n                cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n                break;\n            case 'bottom':\n                cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n                break;\n            case 'bottomleft':\n                cropperPosition.x1 = Math.min(Math.max(moveStart.x1 + moveX, cropperPosition.x2 - settings.cropperScaledMaxWidth), cropperPosition.x2 - settings.cropperScaledMinWidth);\n                cropperPosition.y2 = Math.max(Math.min(moveStart.y2 + moveY, cropperPosition.y1 + settings.cropperScaledMaxHeight), cropperPosition.y1 + settings.cropperScaledMinHeight);\n                break;\n            case 'center':\n                const scale = event.scale;\n                const newWidth = Math.min(Math.max(settings.cropperScaledMinWidth, (Math.abs(moveStart.x2 - moveStart.x1)) * scale), settings.cropperScaledMaxWidth);\n                const newHeight = Math.min(Math.max(settings.cropperScaledMinHeight, (Math.abs(moveStart.y2 - moveStart.y1)) * scale), settings.cropperScaledMaxHeight);\n                cropperPosition.x1 = moveStart.clientX - newWidth / 2;\n                cropperPosition.x2 = moveStart.clientX + newWidth / 2;\n                cropperPosition.y1 = moveStart.clientY - newHeight / 2;\n                cropperPosition.y2 = moveStart.clientY + newHeight / 2;\n                if (cropperPosition.x1 < 0) {\n                    cropperPosition.x2 -= cropperPosition.x1;\n                    cropperPosition.x1 = 0;\n                }\n                else if (cropperPosition.x2 > maxSize.width) {\n                    cropperPosition.x1 -= (cropperPosition.x2 - maxSize.width);\n                    cropperPosition.x2 = maxSize.width;\n                }\n                if (cropperPosition.y1 < 0) {\n                    cropperPosition.y2 -= cropperPosition.y1;\n                    cropperPosition.y1 = 0;\n                }\n                else if (cropperPosition.y2 > maxSize.height) {\n                    cropperPosition.y1 -= (cropperPosition.y2 - maxSize.height);\n                    cropperPosition.y2 = maxSize.height;\n                }\n                break;\n        }\n        if (settings.maintainAspectRatio) {\n            this.checkAspectRatio(moveStart.position, cropperPosition, maxSize, settings);\n        }\n    }\n    checkAspectRatio(position, cropperPosition, maxSize, settings) {\n        let overflowX = 0;\n        let overflowY = 0;\n        switch (position) {\n            case 'top':\n                cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n                overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                overflowY = Math.max(0 - cropperPosition.y1, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x2 -= (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y1 += (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'bottom':\n                cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n                overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x2 -= (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y2 -= (overflowY * settings.aspectRatio) > overflowX ? overflowY : (overflowX / settings.aspectRatio);\n                }\n                break;\n            case 'topleft':\n                cropperPosition.y1 = cropperPosition.y2 - (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                overflowX = Math.max(0 - cropperPosition.x1, 0);\n                overflowY = Math.max(0 - cropperPosition.y1, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x1 += (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y1 += (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'topright':\n                cropperPosition.y1 = cropperPosition.y2 - (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                overflowY = Math.max(0 - cropperPosition.y1, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x2 -= (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y1 += (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'right':\n            case 'bottomright':\n                cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                overflowX = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x2 -= (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y2 -= (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'left':\n            case 'bottomleft':\n                cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                overflowX = Math.max(0 - cropperPosition.x1, 0);\n                overflowY = Math.max(cropperPosition.y2 - maxSize.height, 0);\n                if (overflowX > 0 || overflowY > 0) {\n                    cropperPosition.x1 += (overflowY * settings.aspectRatio) > overflowX ? (overflowY * settings.aspectRatio) : overflowX;\n                    cropperPosition.y2 -= (overflowY * settings.aspectRatio) > overflowX ? overflowY : overflowX / settings.aspectRatio;\n                }\n                break;\n            case 'center':\n                cropperPosition.x2 = cropperPosition.x1 + (cropperPosition.y2 - cropperPosition.y1) * settings.aspectRatio;\n                cropperPosition.y2 = cropperPosition.y1 + (cropperPosition.x2 - cropperPosition.x1) / settings.aspectRatio;\n                const overflowX1 = Math.max(0 - cropperPosition.x1, 0);\n                const overflowX2 = Math.max(cropperPosition.x2 - maxSize.width, 0);\n                const overflowY1 = Math.max(cropperPosition.y2 - maxSize.height, 0);\n                const overflowY2 = Math.max(0 - cropperPosition.y1, 0);\n                if (overflowX1 > 0 || overflowX2 > 0 || overflowY1 > 0 || overflowY2 > 0) {\n                    cropperPosition.x1 += (overflowY1 * settings.aspectRatio) > overflowX1 ? (overflowY1 * settings.aspectRatio) : overflowX1;\n                    cropperPosition.x2 -= (overflowY2 * settings.aspectRatio) > overflowX2 ? (overflowY2 * settings.aspectRatio) : overflowX2;\n                    cropperPosition.y1 += (overflowY2 * settings.aspectRatio) > overflowX2 ? overflowY2 : overflowX2 / settings.aspectRatio;\n                    cropperPosition.y2 -= (overflowY1 * settings.aspectRatio) > overflowX1 ? overflowY1 : overflowX1 / settings.aspectRatio;\n                }\n                break;\n        }\n    }\n    getClientX(event) {\n        return event.touches?.[0].clientX || event.clientX || 0;\n    }\n    getClientY(event) {\n        return event.touches?.[0].clientY || event.clientY || 0;\n    }\n}\nCropperPositionService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: CropperPositionService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nCropperPositionService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: CropperPositionService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: CropperPositionService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n// Black 2x1 JPEG, with the following meta information set:\n// - EXIF Orientation: 6 (Rotated 90° CCW)\n// Source: https://github.com/blueimp/JavaScript-Load-Image\nconst testAutoOrientationImageURL = 'data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAA' +\n    'AAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBA' +\n    'QEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQE' +\n    'BAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/x' +\n    'ABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAA' +\n    'AAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==';\nfunction supportsAutomaticRotation() {\n    return new Promise((resolve) => {\n        const img = new Image();\n        img.onload = () => {\n            // Check if browser supports automatic image orientation:\n            const supported = img.width === 1 && img.height === 2;\n            resolve(supported);\n        };\n        img.src = testAutoOrientationImageURL;\n    });\n}\nfunction getTransformationsFromExifData(exifRotationOrBase64Image) {\n    if (typeof exifRotationOrBase64Image === 'string') {\n        exifRotationOrBase64Image = getExifRotation(exifRotationOrBase64Image);\n    }\n    switch (exifRotationOrBase64Image) {\n        case 2:\n            return { rotate: 0, flip: true };\n        case 3:\n            return { rotate: 2, flip: false };\n        case 4:\n            return { rotate: 2, flip: true };\n        case 5:\n            return { rotate: 1, flip: true };\n        case 6:\n            return { rotate: 1, flip: false };\n        case 7:\n            return { rotate: 3, flip: true };\n        case 8:\n            return { rotate: 3, flip: false };\n        default:\n            return { rotate: 0, flip: false };\n    }\n}\nfunction getExifRotation(imageBase64) {\n    const view = new DataView(base64ToArrayBuffer(imageBase64));\n    if (view.getUint16(0, false) !== 0xFFD8) {\n        return -2;\n    }\n    const length = view.byteLength;\n    let offset = 2;\n    while (offset < length) {\n        if (view.getUint16(offset + 2, false) <= 8)\n            return -1;\n        const marker = view.getUint16(offset, false);\n        offset += 2;\n        if (marker == 0xFFE1) {\n            if (view.getUint32(offset += 2, false) !== 0x45786966) {\n                return -1;\n            }\n            const little = view.getUint16(offset += 6, false) == 0x4949;\n            offset += view.getUint32(offset + 4, little);\n            const tags = view.getUint16(offset, little);\n            offset += 2;\n            for (let i = 0; i < tags; i++) {\n                if (view.getUint16(offset + (i * 12), little) == 0x0112) {\n                    return view.getUint16(offset + (i * 12) + 8, little);\n                }\n            }\n        }\n        else if ((marker & 0xFF00) !== 0xFF00) {\n            break;\n        }\n        else {\n            offset += view.getUint16(offset, false);\n        }\n    }\n    return -1;\n}\nfunction base64ToArrayBuffer(imageBase64) {\n    imageBase64 = imageBase64.replace(/^data\\:([^\\;]+)\\;base64,/gmi, '');\n    const binaryString = atob(imageBase64);\n    const len = binaryString.length;\n    const bytes = new Uint8Array(len);\n    for (let i = 0; i < len; i++) {\n        bytes[i] = binaryString.charCodeAt(i);\n    }\n    return bytes.buffer;\n}\n\nclass LoadImageService {\n    constructor() {\n        this.autoRotateSupported = supportsAutomaticRotation();\n    }\n    loadImageFile(file, cropperSettings) {\n        return new Promise((resolve, reject) => {\n            const fileReader = new FileReader();\n            fileReader.onload = (event) => {\n                this.loadImage(event.target.result, file.type, cropperSettings)\n                    .then(resolve)\n                    .catch(reject);\n            };\n            fileReader.readAsDataURL(file);\n        });\n    }\n    loadImage(imageBase64, imageType, cropperSettings) {\n        if (!this.isValidImageType(imageType)) {\n            return Promise.reject(new Error('Invalid image type'));\n        }\n        return this.loadBase64Image(imageBase64, cropperSettings);\n    }\n    isValidImageType(type) {\n        return /image\\/(png|jpg|jpeg|bmp|gif|tiff|webp|x-icon|vnd.microsoft.icon)/.test(type);\n    }\n    loadImageFromURL(url, cropperSettings) {\n        return new Promise((resolve, reject) => {\n            const img = new Image();\n            img.onerror = () => reject;\n            img.onload = () => {\n                const canvas = document.createElement('canvas');\n                const context = canvas.getContext('2d');\n                canvas.width = img.width;\n                canvas.height = img.height;\n                context?.drawImage(img, 0, 0);\n                this.loadBase64Image(canvas.toDataURL(), cropperSettings).then(resolve);\n            };\n            img.crossOrigin = 'anonymous';\n            img.src = url;\n        });\n    }\n    loadBase64Image(imageBase64, cropperSettings) {\n        return new Promise((resolve, reject) => {\n            const originalImage = new Image();\n            originalImage.onload = () => resolve({\n                originalImage,\n                originalBase64: imageBase64\n            });\n            originalImage.onerror = reject;\n            originalImage.src = imageBase64;\n        }).then((res) => this.transformImageBase64(res, cropperSettings));\n    }\n    async transformImageBase64(res, cropperSettings) {\n        const autoRotate = await this.autoRotateSupported;\n        const exifTransform = await getTransformationsFromExifData(autoRotate ? -1 : res.originalBase64);\n        if (!res.originalImage || !res.originalImage.complete) {\n            return Promise.reject(new Error('No image loaded'));\n        }\n        const loadedImage = {\n            original: {\n                base64: res.originalBase64,\n                image: res.originalImage,\n                size: {\n                    width: res.originalImage.naturalWidth,\n                    height: res.originalImage.naturalHeight\n                }\n            },\n            exifTransform\n        };\n        return this.transformLoadedImage(loadedImage, cropperSettings);\n    }\n    async transformLoadedImage(loadedImage, cropperSettings) {\n        const canvasRotation = cropperSettings.canvasRotation + loadedImage.exifTransform.rotate;\n        const originalSize = {\n            width: loadedImage.original.image.naturalWidth,\n            height: loadedImage.original.image.naturalHeight\n        };\n        if (canvasRotation === 0 && !loadedImage.exifTransform.flip && !cropperSettings.containWithinAspectRatio) {\n            return {\n                original: {\n                    base64: loadedImage.original.base64,\n                    image: loadedImage.original.image,\n                    size: { ...originalSize }\n                },\n                transformed: {\n                    base64: loadedImage.original.base64,\n                    image: loadedImage.original.image,\n                    size: { ...originalSize }\n                },\n                exifTransform: loadedImage.exifTransform\n            };\n        }\n        const transformedSize = this.getTransformedSize(originalSize, loadedImage.exifTransform, cropperSettings);\n        const canvas = document.createElement('canvas');\n        canvas.width = transformedSize.width;\n        canvas.height = transformedSize.height;\n        const ctx = canvas.getContext('2d');\n        ctx?.setTransform(loadedImage.exifTransform.flip ? -1 : 1, 0, 0, 1, canvas.width / 2, canvas.height / 2);\n        ctx?.rotate(Math.PI * (canvasRotation / 2));\n        ctx?.drawImage(loadedImage.original.image, -originalSize.width / 2, -originalSize.height / 2);\n        const transformedBase64 = canvas.toDataURL();\n        const transformedImage = await this.loadImageFromBase64(transformedBase64);\n        return {\n            original: {\n                base64: loadedImage.original.base64,\n                image: loadedImage.original.image,\n                size: { ...originalSize }\n            },\n            transformed: {\n                base64: transformedBase64,\n                image: transformedImage,\n                size: {\n                    width: transformedImage.width,\n                    height: transformedImage.height\n                }\n            },\n            exifTransform: loadedImage.exifTransform\n        };\n    }\n    loadImageFromBase64(imageBase64) {\n        return new Promise(((resolve, reject) => {\n            const image = new Image();\n            image.onload = () => resolve(image);\n            image.onerror = reject;\n            image.src = imageBase64;\n        }));\n    }\n    getTransformedSize(originalSize, exifTransform, cropperSettings) {\n        const canvasRotation = cropperSettings.canvasRotation + exifTransform.rotate;\n        if (cropperSettings.containWithinAspectRatio) {\n            if (canvasRotation % 2) {\n                const minWidthToContain = originalSize.width * cropperSettings.aspectRatio;\n                const minHeightToContain = originalSize.height / cropperSettings.aspectRatio;\n                return {\n                    width: Math.max(originalSize.height, minWidthToContain),\n                    height: Math.max(originalSize.width, minHeightToContain)\n                };\n            }\n            else {\n                const minWidthToContain = originalSize.height * cropperSettings.aspectRatio;\n                const minHeightToContain = originalSize.width / cropperSettings.aspectRatio;\n                return {\n                    width: Math.max(originalSize.width, minWidthToContain),\n                    height: Math.max(originalSize.height, minHeightToContain)\n                };\n            }\n        }\n        if (canvasRotation % 2) {\n            return {\n                height: originalSize.width,\n                width: originalSize.height\n            };\n        }\n        return {\n            width: originalSize.width,\n            height: originalSize.height\n        };\n    }\n}\nLoadImageService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: LoadImageService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nLoadImageService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: LoadImageService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: LoadImageService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nclass ImageCropperComponent {\n    constructor(cropService, cropperPositionService, loadImageService, sanitizer, cd) {\n        this.cropService = cropService;\n        this.cropperPositionService = cropperPositionService;\n        this.loadImageService = loadImageService;\n        this.sanitizer = sanitizer;\n        this.cd = cd;\n        this.Hammer = window?.['Hammer'] || null;\n        this.settings = new CropperSettings();\n        this.setImageMaxSizeRetries = 0;\n        this.marginLeft = '0px';\n        this.maxSize = {\n            width: 0,\n            height: 0\n        };\n        this.moveTypes = MoveTypes;\n        this.imageVisible = false;\n        this.format = this.settings.format;\n        this.transform = {};\n        this.maintainAspectRatio = this.settings.maintainAspectRatio;\n        this.aspectRatio = this.settings.aspectRatio;\n        this.resizeToWidth = this.settings.resizeToWidth;\n        this.resizeToHeight = this.settings.resizeToHeight;\n        this.cropperMinWidth = this.settings.cropperMinWidth;\n        this.cropperMinHeight = this.settings.cropperMinHeight;\n        this.cropperMaxHeight = this.settings.cropperMaxHeight;\n        this.cropperMaxWidth = this.settings.cropperMaxWidth;\n        this.cropperStaticWidth = this.settings.cropperStaticWidth;\n        this.cropperStaticHeight = this.settings.cropperStaticHeight;\n        this.canvasRotation = this.settings.canvasRotation;\n        this.initialStepSize = this.settings.initialStepSize;\n        this.roundCropper = this.settings.roundCropper;\n        this.onlyScaleDown = this.settings.onlyScaleDown;\n        this.imageQuality = this.settings.imageQuality;\n        this.autoCrop = this.settings.autoCrop;\n        this.backgroundColor = this.settings.backgroundColor;\n        this.containWithinAspectRatio = this.settings.containWithinAspectRatio;\n        this.hideResizeSquares = this.settings.hideResizeSquares;\n        this.cropper = {\n            x1: -100,\n            y1: -100,\n            x2: 10000,\n            y2: 10000\n        };\n        this.alignImage = this.settings.alignImage;\n        this.disabled = false;\n        this.imageCropped = new EventEmitter();\n        this.startCropImage = new EventEmitter();\n        this.imageLoaded = new EventEmitter();\n        this.cropperReady = new EventEmitter();\n        this.loadImageFailed = new EventEmitter();\n        this.reset();\n    }\n    ngOnChanges(changes) {\n        this.onChangesUpdateSettings(changes);\n        this.onChangesInputImage(changes);\n        if (this.loadedImage?.original.image.complete && (changes['containWithinAspectRatio'] || changes['canvasRotation'])) {\n            this.loadImageService\n                .transformLoadedImage(this.loadedImage, this.settings)\n                .then((res) => this.setLoadedImage(res))\n                .catch((err) => this.loadImageError(err));\n        }\n        if (changes['cropper'] || changes['maintainAspectRatio'] || changes['aspectRatio']) {\n            this.setMaxSize();\n            this.setCropperScaledMinSize();\n            this.setCropperScaledMaxSize();\n            if (this.maintainAspectRatio && (changes['maintainAspectRatio'] || changes['aspectRatio'])) {\n                this.resetCropperPosition();\n            }\n            else if (changes['cropper']) {\n                this.checkCropperPosition(false);\n                this.doAutoCrop();\n            }\n            this.cd.markForCheck();\n        }\n        if (changes['transform']) {\n            this.transform = this.transform || {};\n            this.setCssTransform();\n            this.doAutoCrop();\n        }\n    }\n    onChangesUpdateSettings(changes) {\n        this.settings.setOptionsFromChanges(changes);\n        if (this.settings.cropperStaticHeight && this.settings.cropperStaticWidth) {\n            this.settings.setOptions({\n                hideResizeSquares: true,\n                cropperMinWidth: this.settings.cropperStaticWidth,\n                cropperMinHeight: this.settings.cropperStaticHeight,\n                cropperMaxHeight: this.settings.cropperStaticHeight,\n                cropperMaxWidth: this.settings.cropperStaticWidth,\n                maintainAspectRatio: false\n            });\n        }\n    }\n    onChangesInputImage(changes) {\n        if (changes['imageChangedEvent'] || changes['imageURL'] || changes['imageBase64'] || changes['imageFile']) {\n            this.reset();\n        }\n        if (changes['imageChangedEvent'] && this.isValidImageChangedEvent()) {\n            this.loadImageFile(this.imageChangedEvent.target.files[0]);\n        }\n        if (changes['imageURL'] && this.imageURL) {\n            this.loadImageFromURL(this.imageURL);\n        }\n        if (changes['imageBase64'] && this.imageBase64) {\n            this.loadBase64Image(this.imageBase64);\n        }\n        if (changes['imageFile'] && this.imageFile) {\n            this.loadImageFile(this.imageFile);\n        }\n    }\n    isValidImageChangedEvent() {\n        return this.imageChangedEvent?.target?.files?.length > 0;\n    }\n    setCssTransform() {\n        this.safeTransformStyle = this.sanitizer.bypassSecurityTrustStyle('scaleX(' + (this.transform.scale || 1) * (this.transform.flipH ? -1 : 1) + ')' +\n            'scaleY(' + (this.transform.scale || 1) * (this.transform.flipV ? -1 : 1) + ')' +\n            'rotate(' + (this.transform.rotate || 0) + 'deg)' +\n            `translate(${this.transform.translateH || 0}%, ${this.transform.translateV || 0}%)`);\n    }\n    ngOnInit() {\n        this.settings.stepSize = this.initialStepSize;\n        this.activatePinchGesture();\n    }\n    reset() {\n        this.imageVisible = false;\n        this.loadedImage = undefined;\n        this.safeImgDataUrl = 'data:image/png;base64,iVBORw0KGg'\n            + 'oAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQYV2NgAAIAAAU'\n            + 'AAarVyFEAAAAASUVORK5CYII=';\n        this.moveStart = {\n            active: false,\n            type: null,\n            position: null,\n            x1: 0,\n            y1: 0,\n            x2: 0,\n            y2: 0,\n            clientX: 0,\n            clientY: 0\n        };\n        this.maxSize = {\n            width: 0,\n            height: 0\n        };\n        this.cropper.x1 = -100;\n        this.cropper.y1 = -100;\n        this.cropper.x2 = 10000;\n        this.cropper.y2 = 10000;\n    }\n    loadImageFile(file) {\n        this.loadImageService\n            .loadImageFile(file, this.settings)\n            .then((res) => this.setLoadedImage(res))\n            .catch((err) => this.loadImageError(err));\n    }\n    loadBase64Image(imageBase64) {\n        this.loadImageService\n            .loadBase64Image(imageBase64, this.settings)\n            .then((res) => this.setLoadedImage(res))\n            .catch((err) => this.loadImageError(err));\n    }\n    loadImageFromURL(url) {\n        this.loadImageService\n            .loadImageFromURL(url, this.settings)\n            .then((res) => this.setLoadedImage(res))\n            .catch((err) => this.loadImageError(err));\n    }\n    setLoadedImage(loadedImage) {\n        this.loadedImage = loadedImage;\n        this.safeImgDataUrl = this.sanitizer.bypassSecurityTrustResourceUrl(loadedImage.transformed.base64);\n        this.cd.markForCheck();\n    }\n    loadImageError(error) {\n        console.error(error);\n        this.loadImageFailed.emit();\n    }\n    imageLoadedInView() {\n        if (this.loadedImage != null) {\n            this.imageLoaded.emit(this.loadedImage);\n            this.setImageMaxSizeRetries = 0;\n            setTimeout(() => this.checkImageMaxSizeRecursively());\n        }\n    }\n    checkImageMaxSizeRecursively() {\n        if (this.setImageMaxSizeRetries > 40) {\n            this.loadImageFailed.emit();\n        }\n        else if (this.sourceImageLoaded()) {\n            this.setMaxSize();\n            this.setCropperScaledMinSize();\n            this.setCropperScaledMaxSize();\n            this.resetCropperPosition();\n            this.cropperReady.emit({ ...this.maxSize });\n            this.cd.markForCheck();\n        }\n        else {\n            this.setImageMaxSizeRetries++;\n            setTimeout(() => this.checkImageMaxSizeRecursively(), 50);\n        }\n    }\n    sourceImageLoaded() {\n        return this.sourceImage?.nativeElement?.offsetWidth > 0;\n    }\n    onResize() {\n        if (!this.loadedImage) {\n            return;\n        }\n        this.resizeCropperPosition();\n        this.setMaxSize();\n        this.setCropperScaledMinSize();\n        this.setCropperScaledMaxSize();\n    }\n    activatePinchGesture() {\n        if (this.Hammer) {\n            const hammer = new this.Hammer(this.wrapper.nativeElement);\n            hammer.get('pinch').set({ enable: true });\n            hammer.on('pinchmove', this.onPinch.bind(this));\n            hammer.on('pinchend', this.pinchStop.bind(this));\n            hammer.on('pinchstart', this.startPinch.bind(this));\n        }\n        else if (isDevMode()) {\n            console.warn('[NgxImageCropper] Could not find HammerJS - Pinch Gesture won\\'t work');\n        }\n    }\n    resizeCropperPosition() {\n        const sourceImageElement = this.sourceImage.nativeElement;\n        if (this.maxSize.width !== sourceImageElement.offsetWidth || this.maxSize.height !== sourceImageElement.offsetHeight) {\n            this.cropper.x1 = this.cropper.x1 * sourceImageElement.offsetWidth / this.maxSize.width;\n            this.cropper.x2 = this.cropper.x2 * sourceImageElement.offsetWidth / this.maxSize.width;\n            this.cropper.y1 = this.cropper.y1 * sourceImageElement.offsetHeight / this.maxSize.height;\n            this.cropper.y2 = this.cropper.y2 * sourceImageElement.offsetHeight / this.maxSize.height;\n        }\n    }\n    resetCropperPosition() {\n        this.cropperPositionService.resetCropperPosition(this.sourceImage, this.cropper, this.settings);\n        this.doAutoCrop();\n        this.imageVisible = true;\n    }\n    keyboardAccess(event) {\n        this.changeKeyboardStepSize(event);\n        this.keyboardMoveCropper(event);\n    }\n    changeKeyboardStepSize(event) {\n        const key = +event.key;\n        if (key >= 1 && key <= 9) {\n            this.settings.stepSize = key;\n        }\n    }\n    keyboardMoveCropper(event) {\n        const keyboardWhiteList = ['ArrowUp', 'ArrowDown', 'ArrowRight', 'ArrowLeft'];\n        if (!(keyboardWhiteList.includes(event.key))) {\n            return;\n        }\n        const moveType = event.shiftKey ? MoveTypes.Resize : MoveTypes.Move;\n        const position = event.altKey ? getInvertedPositionForKey(event.key) : getPositionForKey(event.key);\n        const moveEvent = getEventForKey(event.key, this.settings.stepSize);\n        event.preventDefault();\n        event.stopPropagation();\n        this.startMove({ clientX: 0, clientY: 0 }, moveType, position);\n        this.moveImg(moveEvent);\n        this.moveStop();\n    }\n    startMove(event, moveType, position = null) {\n        if (this.moveStart?.active && this.moveStart?.type === MoveTypes.Pinch) {\n            return;\n        }\n        if (event.preventDefault) {\n            event.preventDefault();\n        }\n        this.moveStart = {\n            active: true,\n            type: moveType,\n            position,\n            clientX: this.cropperPositionService.getClientX(event),\n            clientY: this.cropperPositionService.getClientY(event),\n            ...this.cropper\n        };\n    }\n    startPinch(event) {\n        if (!this.safeImgDataUrl) {\n            return;\n        }\n        if (event.preventDefault) {\n            event.preventDefault();\n        }\n        this.moveStart = {\n            active: true,\n            type: MoveTypes.Pinch,\n            position: 'center',\n            clientX: this.cropper.x1 + (this.cropper.x2 - this.cropper.x1) / 2,\n            clientY: this.cropper.y1 + (this.cropper.y2 - this.cropper.y1) / 2,\n            ...this.cropper\n        };\n    }\n    moveImg(event) {\n        if (this.moveStart.active) {\n            if (event.stopPropagation) {\n                event.stopPropagation();\n            }\n            if (event.preventDefault) {\n                event.preventDefault();\n            }\n            if (this.moveStart.type === MoveTypes.Move) {\n                this.cropperPositionService.move(event, this.moveStart, this.cropper);\n                this.checkCropperPosition(true);\n            }\n            else if (this.moveStart.type === MoveTypes.Resize) {\n                if (!this.cropperStaticWidth && !this.cropperStaticHeight) {\n                    this.cropperPositionService.resize(event, this.moveStart, this.cropper, this.maxSize, this.settings);\n                }\n                this.checkCropperPosition(false);\n            }\n            this.cd.detectChanges();\n        }\n    }\n    onPinch(event) {\n        if (this.moveStart.active) {\n            if (event.stopPropagation) {\n                event.stopPropagation();\n            }\n            if (event.preventDefault) {\n                event.preventDefault();\n            }\n            if (this.moveStart.type === MoveTypes.Pinch) {\n                this.cropperPositionService.resize(event, this.moveStart, this.cropper, this.maxSize, this.settings);\n                this.checkCropperPosition(false);\n            }\n            this.cd.detectChanges();\n        }\n    }\n    setMaxSize() {\n        if (this.sourceImage) {\n            const sourceImageElement = this.sourceImage.nativeElement;\n            this.maxSize.width = sourceImageElement.offsetWidth;\n            this.maxSize.height = sourceImageElement.offsetHeight;\n            this.marginLeft = this.sanitizer.bypassSecurityTrustStyle('calc(50% - ' + this.maxSize.width / 2 + 'px)');\n        }\n    }\n    setCropperScaledMinSize() {\n        if (this.loadedImage?.transformed?.image) {\n            this.setCropperScaledMinWidth();\n            this.setCropperScaledMinHeight();\n        }\n        else {\n            this.settings.cropperScaledMinWidth = 20;\n            this.settings.cropperScaledMinHeight = 20;\n        }\n    }\n    setCropperScaledMinWidth() {\n        this.settings.cropperScaledMinWidth = this.cropperMinWidth > 0\n            ? Math.max(20, this.cropperMinWidth / this.loadedImage.transformed.image.width * this.maxSize.width)\n            : 20;\n    }\n    setCropperScaledMinHeight() {\n        if (this.maintainAspectRatio) {\n            this.settings.cropperScaledMinHeight = Math.max(20, this.settings.cropperScaledMinWidth / this.aspectRatio);\n        }\n        else if (this.cropperMinHeight > 0) {\n            this.settings.cropperScaledMinHeight = Math.max(20, this.cropperMinHeight / this.loadedImage.transformed.image.height * this.maxSize.height);\n        }\n        else {\n            this.settings.cropperScaledMinHeight = 20;\n        }\n    }\n    setCropperScaledMaxSize() {\n        if (this.loadedImage?.transformed?.image) {\n            const ratio = this.loadedImage.transformed.size.width / this.maxSize.width;\n            this.settings.cropperScaledMaxWidth = this.cropperMaxWidth > 20 ? this.cropperMaxWidth / ratio : this.maxSize.width;\n            this.settings.cropperScaledMaxHeight = this.cropperMaxHeight > 20 ? this.cropperMaxHeight / ratio : this.maxSize.height;\n            if (this.maintainAspectRatio) {\n                if (this.settings.cropperScaledMaxWidth > this.settings.cropperScaledMaxHeight * this.aspectRatio) {\n                    this.settings.cropperScaledMaxWidth = this.settings.cropperScaledMaxHeight * this.aspectRatio;\n                }\n                else if (this.settings.cropperScaledMaxWidth < this.settings.cropperScaledMaxHeight * this.aspectRatio) {\n                    this.settings.cropperScaledMaxHeight = this.settings.cropperScaledMaxWidth / this.aspectRatio;\n                }\n            }\n        }\n        else {\n            this.settings.cropperScaledMaxWidth = this.maxSize.width;\n            this.settings.cropperScaledMaxHeight = this.maxSize.height;\n        }\n    }\n    checkCropperPosition(maintainSize = false) {\n        if (this.cropper.x1 < 0) {\n            this.cropper.x2 -= maintainSize ? this.cropper.x1 : 0;\n            this.cropper.x1 = 0;\n        }\n        if (this.cropper.y1 < 0) {\n            this.cropper.y2 -= maintainSize ? this.cropper.y1 : 0;\n            this.cropper.y1 = 0;\n        }\n        if (this.cropper.x2 > this.maxSize.width) {\n            this.cropper.x1 -= maintainSize ? (this.cropper.x2 - this.maxSize.width) : 0;\n            this.cropper.x2 = this.maxSize.width;\n        }\n        if (this.cropper.y2 > this.maxSize.height) {\n            this.cropper.y1 -= maintainSize ? (this.cropper.y2 - this.maxSize.height) : 0;\n            this.cropper.y2 = this.maxSize.height;\n        }\n    }\n    moveStop() {\n        if (this.moveStart.active) {\n            this.moveStart.active = false;\n            this.doAutoCrop();\n        }\n    }\n    pinchStop() {\n        if (this.moveStart.active) {\n            this.moveStart.active = false;\n            this.doAutoCrop();\n        }\n    }\n    doAutoCrop() {\n        if (this.autoCrop) {\n            this.crop();\n        }\n    }\n    crop() {\n        if (this.loadedImage?.transformed?.image != null) {\n            this.startCropImage.emit();\n            const output = this.cropService.crop(this.sourceImage, this.loadedImage, this.cropper, this.settings);\n            if (output != null) {\n                this.imageCropped.emit(output);\n            }\n            return output;\n        }\n        return null;\n    }\n}\nImageCropperComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: ImageCropperComponent, deps: [{ token: CropService }, { token: CropperPositionService }, { token: LoadImageService }, { token: i4.DomSanitizer }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nImageCropperComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.1.1\", type: ImageCropperComponent, selector: \"image-cropper\", inputs: { imageChangedEvent: \"imageChangedEvent\", imageURL: \"imageURL\", imageBase64: \"imageBase64\", imageFile: \"imageFile\", format: \"format\", transform: \"transform\", maintainAspectRatio: \"maintainAspectRatio\", aspectRatio: \"aspectRatio\", resizeToWidth: \"resizeToWidth\", resizeToHeight: \"resizeToHeight\", cropperMinWidth: \"cropperMinWidth\", cropperMinHeight: \"cropperMinHeight\", cropperMaxHeight: \"cropperMaxHeight\", cropperMaxWidth: \"cropperMaxWidth\", cropperStaticWidth: \"cropperStaticWidth\", cropperStaticHeight: \"cropperStaticHeight\", canvasRotation: \"canvasRotation\", initialStepSize: \"initialStepSize\", roundCropper: \"roundCropper\", onlyScaleDown: \"onlyScaleDown\", imageQuality: \"imageQuality\", autoCrop: \"autoCrop\", backgroundColor: \"backgroundColor\", containWithinAspectRatio: \"containWithinAspectRatio\", hideResizeSquares: \"hideResizeSquares\", cropper: \"cropper\", alignImage: \"alignImage\", disabled: \"disabled\" }, outputs: { imageCropped: \"imageCropped\", startCropImage: \"startCropImage\", imageLoaded: \"imageLoaded\", cropperReady: \"cropperReady\", loadImageFailed: \"loadImageFailed\" }, host: { listeners: { \"window:resize\": \"onResize()\", \"document:mousemove\": \"moveImg($event)\", \"document:touchmove\": \"moveImg($event)\", \"document:mouseup\": \"moveStop()\", \"document:touchend\": \"moveStop()\" }, properties: { \"style.text-align\": \"this.alignImage\", \"class.disabled\": \"this.disabled\" } }, viewQueries: [{ propertyName: \"wrapper\", first: true, predicate: [\"wrapper\"], descendants: true, static: true }, { propertyName: \"sourceImage\", first: true, predicate: [\"sourceImage\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<div [style.background]=\\\"imageVisible && backgroundColor\\\"\\n     #wrapper\\n>\\n    <img\\n      #sourceImage\\n      class=\\\"ngx-ic-source-image\\\"\\n      *ngIf=\\\"safeImgDataUrl\\\"\\n      [src]=\\\"safeImgDataUrl\\\"\\n      [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n      [style.transform]=\\\"safeTransformStyle\\\"\\n      (load)=\\\"imageLoadedInView()\\\"\\n    />\\n    <div\\n        class=\\\"ngx-ic-overlay\\\"\\n        [style.width.px]=\\\"maxSize.width\\\"\\n        [style.height.px]=\\\"maxSize.height\\\"\\n        [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n    ></div>\\n    <div class=\\\"ngx-ic-cropper\\\"\\n         *ngIf=\\\"imageVisible\\\"\\n         [class.ngx-ic-round]=\\\"roundCropper\\\"\\n         [style.top.px]=\\\"cropper.y1\\\"\\n         [style.left.px]=\\\"cropper.x1\\\"\\n         [style.width.px]=\\\"cropper.x2 - cropper.x1\\\"\\n         [style.height.px]=\\\"cropper.y2 - cropper.y1\\\"\\n         [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n         [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n         (keydown)=\\\"keyboardAccess($event)\\\"\\n         tabindex=\\\"0\\\"\\n    >\\n        <div\\n            (mousedown)=\\\"startMove($event, moveTypes.Move)\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Move)\\\"\\n            class=\\\"ngx-ic-move\\\">\\n        </div>\\n        <ng-container *ngIf=\\\"!hideResizeSquares\\\">\\n            <span class=\\\"ngx-ic-resize ngx-ic-topleft\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-top\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-topright\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-right\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-bottomright\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-bottom\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-bottomleft\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-left\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-top\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'top')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'top')\\\">\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-right\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'right')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'right')\\\">\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-bottom\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\">\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-left\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'left')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'left')\\\">\\n            </span>\\n        </ng-container>\\n    </div>\\n</div>\\n\", styles: [\":host{display:flex;position:relative;width:100%;max-width:100%;max-height:100%;overflow:hidden;padding:5px;text-align:center}:host>div{width:100%;position:relative}:host>div img.ngx-ic-source-image{max-width:100%;max-height:100%;transform-origin:center}:host .ngx-ic-overlay{position:absolute;pointer-events:none;touch-action:none;outline:var(--cropper-overlay-color, white) solid 100vw;top:0;left:0}:host .ngx-ic-cropper{position:absolute;display:flex;color:#53535c;background:transparent;outline:rgba(255,255,255,.3) solid 100vw;outline:var(--cropper-outline-color, rgba(255, 255, 255, .3)) solid 100vw;touch-action:none}@media (orientation: portrait){:host .ngx-ic-cropper{outline-width:100vh}}:host .ngx-ic-cropper:after{position:absolute;content:\\\"\\\";top:0;bottom:0;left:0;right:0;pointer-events:none;border:dashed 1px;opacity:.75;color:inherit;z-index:1}:host .ngx-ic-cropper .ngx-ic-move{width:100%;cursor:move;border:1px solid rgba(255,255,255,.5)}:host .ngx-ic-cropper:focus .ngx-ic-move{border-color:#1e90ff;border-width:2px}:host .ngx-ic-cropper .ngx-ic-resize{position:absolute;display:inline-block;line-height:6px;padding:8px;opacity:.85;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize .ngx-ic-square{display:inline-block;background:#53535C;width:6px;height:6px;border:1px solid rgba(255,255,255,.5);box-sizing:content-box}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topleft{top:-12px;left:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-top{top:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topright{top:-12px;right:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-right{top:calc(50% - 12px);right:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomright{bottom:-12px;right:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottom{bottom:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomleft{bottom:-12px;left:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-left{top:calc(50% - 12px);left:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar{position:absolute;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-top{top:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-right{top:11px;right:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-bottom{bottom:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-left{top:11px;left:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper.ngx-ic-round{outline-color:transparent}:host .ngx-ic-cropper.ngx-ic-round:after{border-radius:100%;box-shadow:0 0 0 100vw #ffffff4d;box-shadow:0 0 0 100vw var(--cropper-outline-color, rgba(255, 255, 255, .3))}@media (orientation: portrait){:host .ngx-ic-cropper.ngx-ic-round:after{box-shadow:0 0 0 100vh #ffffff4d;box-shadow:0 0 0 100vh var(--cropper-outline-color, rgba(255, 255, 255, .3))}}:host .ngx-ic-cropper.ngx-ic-round .ngx-ic-move{border-radius:100%}:host.disabled .ngx-ic-cropper .ngx-ic-resize,:host.disabled .ngx-ic-cropper .ngx-ic-resize-bar,:host.disabled .ngx-ic-cropper .ngx-ic-move{display:none}\\n\"], directives: [{ type: i5.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: ImageCropperComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'image-cropper', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div [style.background]=\\\"imageVisible && backgroundColor\\\"\\n     #wrapper\\n>\\n    <img\\n      #sourceImage\\n      class=\\\"ngx-ic-source-image\\\"\\n      *ngIf=\\\"safeImgDataUrl\\\"\\n      [src]=\\\"safeImgDataUrl\\\"\\n      [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n      [style.transform]=\\\"safeTransformStyle\\\"\\n      (load)=\\\"imageLoadedInView()\\\"\\n    />\\n    <div\\n        class=\\\"ngx-ic-overlay\\\"\\n        [style.width.px]=\\\"maxSize.width\\\"\\n        [style.height.px]=\\\"maxSize.height\\\"\\n        [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n    ></div>\\n    <div class=\\\"ngx-ic-cropper\\\"\\n         *ngIf=\\\"imageVisible\\\"\\n         [class.ngx-ic-round]=\\\"roundCropper\\\"\\n         [style.top.px]=\\\"cropper.y1\\\"\\n         [style.left.px]=\\\"cropper.x1\\\"\\n         [style.width.px]=\\\"cropper.x2 - cropper.x1\\\"\\n         [style.height.px]=\\\"cropper.y2 - cropper.y1\\\"\\n         [style.margin-left]=\\\"alignImage === 'center' ? marginLeft : null\\\"\\n         [style.visibility]=\\\"imageVisible ? 'visible' : 'hidden'\\\"\\n         (keydown)=\\\"keyboardAccess($event)\\\"\\n         tabindex=\\\"0\\\"\\n    >\\n        <div\\n            (mousedown)=\\\"startMove($event, moveTypes.Move)\\\"\\n            (touchstart)=\\\"startMove($event, moveTypes.Move)\\\"\\n            class=\\\"ngx-ic-move\\\">\\n        </div>\\n        <ng-container *ngIf=\\\"!hideResizeSquares\\\">\\n            <span class=\\\"ngx-ic-resize ngx-ic-topleft\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-top\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-topright\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'topright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-right\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-bottomright\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomright')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-bottom\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-bottomleft\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottomleft')\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize ngx-ic-left\\\">\\n                <span class=\\\"ngx-ic-square\\\"></span>\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-top\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'top')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'top')\\\">\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-right\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'right')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'right')\\\">\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-bottom\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'bottom')\\\">\\n            </span>\\n            <span class=\\\"ngx-ic-resize-bar ngx-ic-left\\\"\\n                  (mousedown)=\\\"startMove($event, moveTypes.Resize, 'left')\\\"\\n                  (touchstart)=\\\"startMove($event, moveTypes.Resize, 'left')\\\">\\n            </span>\\n        </ng-container>\\n    </div>\\n</div>\\n\", styles: [\":host{display:flex;position:relative;width:100%;max-width:100%;max-height:100%;overflow:hidden;padding:5px;text-align:center}:host>div{width:100%;position:relative}:host>div img.ngx-ic-source-image{max-width:100%;max-height:100%;transform-origin:center}:host .ngx-ic-overlay{position:absolute;pointer-events:none;touch-action:none;outline:var(--cropper-overlay-color, white) solid 100vw;top:0;left:0}:host .ngx-ic-cropper{position:absolute;display:flex;color:#53535c;background:transparent;outline:rgba(255,255,255,.3) solid 100vw;outline:var(--cropper-outline-color, rgba(255, 255, 255, .3)) solid 100vw;touch-action:none}@media (orientation: portrait){:host .ngx-ic-cropper{outline-width:100vh}}:host .ngx-ic-cropper:after{position:absolute;content:\\\"\\\";top:0;bottom:0;left:0;right:0;pointer-events:none;border:dashed 1px;opacity:.75;color:inherit;z-index:1}:host .ngx-ic-cropper .ngx-ic-move{width:100%;cursor:move;border:1px solid rgba(255,255,255,.5)}:host .ngx-ic-cropper:focus .ngx-ic-move{border-color:#1e90ff;border-width:2px}:host .ngx-ic-cropper .ngx-ic-resize{position:absolute;display:inline-block;line-height:6px;padding:8px;opacity:.85;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize .ngx-ic-square{display:inline-block;background:#53535C;width:6px;height:6px;border:1px solid rgba(255,255,255,.5);box-sizing:content-box}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topleft{top:-12px;left:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-top{top:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-topright{top:-12px;right:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-right{top:calc(50% - 12px);right:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomright{bottom:-12px;right:-12px;cursor:nwse-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottom{bottom:-12px;left:calc(50% - 12px);cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-bottomleft{bottom:-12px;left:-12px;cursor:nesw-resize}:host .ngx-ic-cropper .ngx-ic-resize.ngx-ic-left{top:calc(50% - 12px);left:-12px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar{position:absolute;z-index:1}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-top{top:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-right{top:11px;right:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-bottom{bottom:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}:host .ngx-ic-cropper .ngx-ic-resize-bar.ngx-ic-left{top:11px;left:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}:host .ngx-ic-cropper.ngx-ic-round{outline-color:transparent}:host .ngx-ic-cropper.ngx-ic-round:after{border-radius:100%;box-shadow:0 0 0 100vw #ffffff4d;box-shadow:0 0 0 100vw var(--cropper-outline-color, rgba(255, 255, 255, .3))}@media (orientation: portrait){:host .ngx-ic-cropper.ngx-ic-round:after{box-shadow:0 0 0 100vh #ffffff4d;box-shadow:0 0 0 100vh var(--cropper-outline-color, rgba(255, 255, 255, .3))}}:host .ngx-ic-cropper.ngx-ic-round .ngx-ic-move{border-radius:100%}:host.disabled .ngx-ic-cropper .ngx-ic-resize,:host.disabled .ngx-ic-cropper .ngx-ic-resize-bar,:host.disabled .ngx-ic-cropper .ngx-ic-move{display:none}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: CropService }, { type: CropperPositionService }, { type: LoadImageService }, { type: i4.DomSanitizer }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { wrapper: [{\n                type: ViewChild,\n                args: ['wrapper', { static: true }]\n            }], sourceImage: [{\n                type: ViewChild,\n                args: ['sourceImage', { static: false }]\n            }], imageChangedEvent: [{\n                type: Input\n            }], imageURL: [{\n                type: Input\n            }], imageBase64: [{\n                type: Input\n            }], imageFile: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], transform: [{\n                type: Input\n            }], maintainAspectRatio: [{\n                type: Input\n            }], aspectRatio: [{\n                type: Input\n            }], resizeToWidth: [{\n                type: Input\n            }], resizeToHeight: [{\n                type: Input\n            }], cropperMinWidth: [{\n                type: Input\n            }], cropperMinHeight: [{\n                type: Input\n            }], cropperMaxHeight: [{\n                type: Input\n            }], cropperMaxWidth: [{\n                type: Input\n            }], cropperStaticWidth: [{\n                type: Input\n            }], cropperStaticHeight: [{\n                type: Input\n            }], canvasRotation: [{\n                type: Input\n            }], initialStepSize: [{\n                type: Input\n            }], roundCropper: [{\n                type: Input\n            }], onlyScaleDown: [{\n                type: Input\n            }], imageQuality: [{\n                type: Input\n            }], autoCrop: [{\n                type: Input\n            }], backgroundColor: [{\n                type: Input\n            }], containWithinAspectRatio: [{\n                type: Input\n            }], hideResizeSquares: [{\n                type: Input\n            }], cropper: [{\n                type: Input\n            }], alignImage: [{\n                type: HostBinding,\n                args: ['style.text-align']\n            }, {\n                type: Input\n            }], disabled: [{\n                type: HostBinding,\n                args: ['class.disabled']\n            }, {\n                type: Input\n            }], imageCropped: [{\n                type: Output\n            }], startCropImage: [{\n                type: Output\n            }], imageLoaded: [{\n                type: Output\n            }], cropperReady: [{\n                type: Output\n            }], loadImageFailed: [{\n                type: Output\n            }], onResize: [{\n                type: HostListener,\n                args: ['window:resize']\n            }], moveImg: [{\n                type: HostListener,\n                args: ['document:mousemove', ['$event']]\n            }, {\n                type: HostListener,\n                args: ['document:touchmove', ['$event']]\n            }], moveStop: [{\n                type: HostListener,\n                args: ['document:mouseup']\n            }, {\n                type: HostListener,\n                args: ['document:touchend']\n            }] } });\n\nclass ImageCropperModule {\n}\nImageCropperModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: ImageCropperModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nImageCropperModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: ImageCropperModule, declarations: [ImageCropperComponent], imports: [CommonModule], exports: [ImageCropperComponent] });\nImageCropperModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: ImageCropperModule, imports: [[\n            CommonModule\n        ]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.1.1\", ngImport: i0, type: ImageCropperModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule\n                    ],\n                    declarations: [\n                        ImageCropperComponent\n                    ],\n                    exports: [\n                        ImageCropperComponent\n                    ]\n                }]\n        }] });\n\nfunction base64ToFile(base64Image) {\n    const split = base64Image.split(',');\n    const type = split[0].replace('data:', '').replace(';base64', '');\n    const byteString = atob(split[1]);\n    const ab = new ArrayBuffer(byteString.length);\n    const ia = new Uint8Array(ab);\n    for (let i = 0; i < byteString.length; i += 1) {\n        ia[i] = byteString.charCodeAt(i);\n    }\n    return new Blob([ab], { type });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ImageCropperComponent, ImageCropperModule, base64ToFile, resizeCanvas };\n"]}, "metadata": {}, "sourceType": "module"}
import { Component, OnInit, HostListener, Input } from '@angular/core';
import { CamelCasePipe } from 'src/app/camel-case.pipe';
import {
  FormBuilder,
  FormGroup,
  FormArray,
  FormControl,
  Validators,
} from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
import {
  debounceTime,
  distinctUntilChanged,
  switchMap,
  map,
  catchError,
} from 'rxjs/operators';
import { of, Observable } from 'rxjs';
import { FileValidator } from 'src/app/shared/validators/fileValidator.validator';
import { ImageCroppedEvent } from 'ngx-image-cropper';

interface Preferences {
  [key: string]: string[];
}
@Component({
  selector: 'app-edit-app-user',
  templateUrl: './edit-app-user.component.html',
  styleUrls: ['./edit-app-user.component.scss'],
})
export class EditAppUserComponent implements OnInit {
  p: number = 1;
  imageName: any;
  aliasImageName:any;
  imageSrc: any;
  aliasImageSrc:any;
  term: string;
  isReadonly = false;
  userData: any;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  userId: any;
  title: any;
  addNewAppUserForm: FormGroup;
  selectedActivities: any[] = [];
  selectedSectors: any[] = [];
  selectedWorkTypes: any[] = [];
  selectedGender: any[] = [];
  selectedEthnicity: any[] = [];
  selectedReligion: any[] = [];
  selectedUniversity: any;
  selectedDegree: any;
  selectedDisability: any[] = [];
  selectedPostcode: any;
  selectedGeneration: any[] = [];
  selectedFreeMeal: any[] = [];
  selectedSexualOrientation: any[] = [];
  ActivitiesYouLike: any[] = [];
  WorkType: any[] = [];
  LikedSectors: any[] = [];
  Genders: any[] = [];
  Ethnicities: any[] = [];
  Religions: any[] = [];
  SexualOrientation: any[] = [];
  Degree: any[] = [];
  University: any[] = [];
  universitySearchControl: FormControl;
  degreeSearchControl: FormControl;
  postcodeSearchControl: FormControl;
  filteredUniversityOptions: any[] = [];
  filteredDegreeOptions: any[] = [];
  filteredPostcodes: string[] = [];
  showDropdown = false; // Controls visibility of the dropdown list
  showTag: boolean;
  showTagOfEducation: boolean;
  showDropdownOfEducation: boolean;
  showPostcodeDropdown: boolean = false;
  showPassword: boolean = false;

  hasADisability: any[] = [
    {
      QUO_id: '1',
      AM_title: 'Yes',
    },
    {
      QUO_id: '2',
      AM_title: 'No',
    },
    {
      QUO_id: '3',
      AM_title: 'Prefer Not To say',
    },
    {
      QUO_id: '4',
      AM_title: 'I Am Not Sure',
    },
  ];
  FreeMeal: any[] = [
    {
      QUO_id: '1',
      AM_title: 'Yes',
    },
    {
      QUO_id: '2',
      AM_title: 'No',
    },
    {
      QUO_id: '3',
      AM_title: 'Prefer Not To say',
    },
    // {
    //   QUO_id: '4',
    //   AM_title: 'I Am Not Sure',
    // },
  ];
  isThisGeneration: any[] = [
    {
      QUO_id: '1',
      AM_title: 'Yes',
    },
    {
      QUO_id: '2',
      AM_title: 'No',
    },
    {
      QUO_id: '3',
      AM_title: 'Prefer Not To say',
    },
    // {
    //   QUO_id: '4',
    //   AM_title: 'I Am Not Sure',
    // },
  ];
  U_id: any;
  imgChangeEvt: any="";
  aliasImgChangeEvt: any="";

  cropImagePreview: string | null | undefined;
  isCropperVisible = false;
  isAliasCropperVisible = false;

  constructor(
    private router: Router,
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private activeRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private ngxSpinnerService: NgxSpinnerService,
    private route: ActivatedRoute,
  ) {
    this.addNewAppUserForm = this.formBuilder.group({
      U_name: ['', Validators.required],
      U_email: ['', [Validators.required, FileValidator.strictEmailValidator]],
      U_password: [
        '',
        [
          Validators.required,
          Validators.minLength(8),
          Validators.pattern(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,}$/)
        ],
      ],
      U_profilePercentage: ['0'],
      U_ethinicityId: ['', Validators.required],
      U_ethinicity_Toggle: [false],
      U_genderId: ['', Validators.required],
      U_gender_Toggle: [false],
      U_isDisability: ['', Validators.required],
      U_disability_Toggle: [false],
      U_religionId: ['', Validators.required],
      U_religion_Toggle: [false],
      U_sexualOrientationId: ['', Validators.required],
      U_sexuality_Toggle: [false],
      U_institute: [''],
      U_postcode: [''],
      U_postcode_Toggle: [false],
      U_first_generation: ['', Validators.required],
      U_first_generation_Toggle: [false],
      U_dp: [null],
      U_freeMeal: ['', Validators.required],
      U_freeMeal_Toggle: [false],
      U_education: [''],
      QUA_quiz_options: this.formBuilder.array([]),
      LI_contentId: this.formBuilder.array([], Validators.required),
      U_isExpert:[false],
      U_profileAnonymous:[false],
      U_aliasName:[''],
      U_aliasDp:['']
    });

    this.route.queryParams.subscribe(params => {
      if(params){
      this.U_id = params['U_id'];
      this.title=params['title']
      console.log(this.U_id);
    }else{
      this.router.navigate(['/actions/app-users']);
    }
    });
  }

  ngOnInit(): void {
    if (this.title === 'Edit') {
      this.getUserDetailsById(this.U_id).then(res => {
        this.patchFormData(res); 
      }).catch(error => {
        console.error("Error in fetching user details:", error);
      });
    }

    this.getAllPreferencesData();
    if(this.title!=='Edit'){
    this.getIdentityData();
    this.getindustry();
    this.getAllUniversity();
  }
    this.universitySearchControl = new FormControl('');
    this.degreeSearchControl = new FormControl('');
    this.postcodeSearchControl = new FormControl('');
    this.getAllDegree();
    this.postcodeSearchControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        switchMap((value) => this.fetchPostcodes(value))
      )
      .subscribe((postcodes: string[]) => {
        this.filteredPostcodes = postcodes;
        this.showPostcodeDropdown = postcodes.length > 0;
      });
    if (!this.addNewAppUserForm.get('U_postcode')) {
      this.addNewAppUserForm.addControl(
        'U_postcode',
        new FormControl('', Validators.required)
      );
    }
  }

  getUserDetailsById(U_id: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.dataTransferService.getUserDetailsById(U_id).subscribe(
        (res: any) => {
          if (res.data) {
            this.userData = res.data;
            console.log("this.userData", this.userData);
            resolve(this.userData); // Resolve with the userData
          } else {
            reject('No data found'); // Reject if no data found
          }
        },
        (error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          this.toastr.error("Unable to fetch data.");
          console.log("Error", error);
          reject(error);
        }
      );
    });
  }
  

  async patchFormData(data: any): Promise<void> {
    try {

      await this.getindustry();
      await this.getIdentityData();
      await this.getAllUniversity();

      this.addNewAppUserForm.patchValue({
        U_name: data.U_name,
        U_email: data.U_email,
        U_password: data.U_password,
        U_profilePercentage: data.U_profilePercentage,
        U_ethinicity_Toggle: data.U_ethinicity_Toggle,
        U_gender_Toggle: data.U_gender_Toggle,
        U_disability_Toggle: data.U_disability_Toggle,
        U_religion_Toggle: data.U_religion_Toggle,
        U_sexuality_Toggle: data.U_sexuality_Toggle,
        U_institute: data.U_institute,
        U_postcode: data?.U_postcode,
        U_postcode_Toggle: data.U_postcode_Toggle? data.U_postcode_Toggle: false,
        // U_dp: data.U_dp,
        U_education: data.U_education,
        U_genderId: data.U_genderId,
        U_ethinicityId: data.U_ethinicityId,
        U_religionId: data.U_religionId,
        U_sexualOrientationId: data.U_sexualOrientationId,
        U_isDisability: data.U_isDisability,
        U_first_generation: data.U_first_generation,
        U_first_generation_Toggle: data.U_first_generation_Toggle,
        U_freeMeal: data.U_freeMeal,
        U_freeMeal_Toggle: data.U_freeMeal_Toggle? data.U_freeMeal_Toggle: false,
        U_profileAnonymous:data?.U_profileAnonymous,
        U_aliasName:data?.U_aliasName
      });

      if (data.U_dp) {
        this.imageSrc=data.U_dp;
      }

      if (data.U_aliasDp) {
        this.aliasImageSrc=data.U_aliasDp;
      }

      if (data.U_institute) {
        this.addMenu(data.U_institute, 'U_institute', 'toPatchExisting');
      }

      if (data.U_education) {
        this.addMenu(data.U_education, 'U_education', 'toPatchExisting');
      }

      if (data.U_postcode) {
        this.selectPostcode(data.U_postcode);
      }

      this.selectedSectors = this.LikedSectors.filter((sector: any) =>
        data?.LI_contentId.includes(sector.IN_id)
      );

      // Update the form with the selected sectors
      this.updateFormArray('LI_contentId', this.selectedSectors, false);

      // Patch the QUA_quiz_options for Activities and Work Types
      this.selectedActivities = data?.QUA_quiz_options.filter(
        (option: any) => option?.quiz_id === '5'
      );
      this.selectedWorkTypes = data?.QUA_quiz_options.filter(
        (option: any) => option?.quiz_id === '2'
      );

      // Call method to update the FormArray in the form
      this.updateFormArray(
        'QUA_quiz_options',
        this.selectedActivities.concat(this.selectedWorkTypes),
        false
      );

      // Update Identity Data
      const selectedGender = this.Genders.find(
        (gender: any) => gender?.GE_id === data.U_genderId
      );
      this.selectedGender.push(selectedGender);

      const selectedEthnicity = this.Ethnicities.find(
        (ethnicity: any) => ethnicity?.ET_id === data?.U_ethinicityId
      );
      this.selectedEthnicity.push(selectedEthnicity);

      const selectedReligion = this.Religions.find(
        (religion: any) => religion?.RE_id === data?.U_religionId
      );
      this.selectedReligion.push(selectedReligion);

      const selectedSexualOrientation = this.SexualOrientation.find(
        (orientation: any) => orientation?.SO_id === data?.U_sexualOrientationId
      );
      this.selectedSexualOrientation.push(selectedSexualOrientation);

      const selectedDisability = this.hasADisability.find(
        (disability: any) => disability?.QUO_id === data?.U_isDisability
      );
      this.selectedDisability.push(selectedDisability);

      const selectedGeneration = this.isThisGeneration.find(
        (generation: any) => generation?.QUO_id === data?.U_first_generation
      );
      this.selectedGeneration.push(selectedGeneration);

      const selectedFreeMeal = this.FreeMeal.find(
        (meal: any) => meal?.QUO_id === data?.U_freeMeal
      );
      this.selectedFreeMeal.push(selectedFreeMeal);
    } catch (error) {
      console.error('Error loading data:', error);
    }
    this.calculateProfilePercentage();
    console.log("Patched data",this.addNewAppUserForm.value);
    
  }

  markAllFieldsAsTouched() {
    Object.keys(this.addNewAppUserForm.controls).forEach((field) => {
      const control = this.addNewAppUserForm.get(field);
      control?.markAsTouched({ onlySelf: true });
    });
  }

  calculateProfilePercentage() {
    let percentage = 0;
    const fields = [
      this.selectedGender,
      this.selectedEthnicity,
      this.selectedReligion,
      this.selectedSexualOrientation,
      this.selectedDisability,
      this.selectedGeneration,
      this.selectedFreeMeal,
      this.selectedPostcode ? [this.selectedPostcode] : [],
      this.addNewAppUserForm.get('U_institute')?.value
        ? [this.addNewAppUserForm.get('U_institute')?.value]
        : [],
      this.addNewAppUserForm.get('U_education')?.value
        ? [this.addNewAppUserForm.get('U_education')?.value]
        : [],
    ];

    fields.forEach((field) => {
      if (field.length > 0) {
        percentage += 10;
      }
    });

    // Convert percentage to string and patch value
    this.addNewAppUserForm.patchValue({
      U_profilePercentage: percentage.toString(),
    });
    console.log(
      'this.addNewAppUserForm.value.U_profilePercentage:',
      this.addNewAppUserForm.value.U_profilePercentage
    );
  }

  togglePasswordVisibility(icon: HTMLElement, controlName: string): void {
    const input = document.getElementById('U_password') as HTMLInputElement;
    // const input = this.el.nativeElement.querySelector(`#${controlName}`) as HTMLInputElement;
    const isPasswordType = input.type === 'password';
    input.type = isPasswordType ? 'text' : 'password';
    icon.classList.toggle('fa-eye-slash', isPasswordType);
    icon.classList.toggle('fa-eye', !isPasswordType);
  }

  onFileSelected(event: any, field: string) {
    const selectedFile = event.target.files[0];
    console.log("selectedFile", selectedFile);
  
    // Function to add a timestamp to the file name
    // const addTimestamp = (fileName: string) => {
    //   const currentTimestamp = new Date().getTime();
    //   const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');
    //   const extension = fileName.split('.').pop();
    //   return `${nameWithoutExtension}_${currentTimestamp}.${extension}`;
    // };
  
    if (!selectedFile) {
      if (field === 'U_dp') {
        this.imageName = null;
        this.imageSrc = null;
        const fileControl = this.addNewAppUserForm.get('U_dp');
        fileControl?.clearValidators();
        fileControl?.updateValueAndValidity();
      } else if (field === 'U_aliasDp') {
        this.aliasImageName = null;
        this.aliasImageSrc = null;
        const aliasFileControl = this.addNewAppUserForm.get('U_aliasDp');
        aliasFileControl?.clearValidators();
        aliasFileControl?.updateValueAndValidity();
      }
      return;
    }
  
    if (field === 'U_dp' && selectedFile) {
      // Append timestamp to image name
      const newFileName = FileValidator.addTimestamp(selectedFile.name);
      this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });
       console.log("imageName",this.imageName);
  
      const fileControl = this.addNewAppUserForm.get('U_dp');
      fileControl?.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));
      fileControl?.updateValueAndValidity();
  
      const fileType = this.imageName.type.split('/')[0];
      const fileExtension = this.imageName.name.split('.').pop()?.toLowerCase();
      if (fileType !== 'image' || fileExtension === 'svg') {
        event.target.value = '';
        this.toastr.info('Please select an image file (excluding SVG).');
        this.imageName = null;
        this.imageSrc = null;
        return;
      }
  
      if (this.imageName && fileType === 'image') {
        const reader = new FileReader();
        const img = new Image();
        reader.onload = (e) => {
          this.imageSrc = e.target?.result as string | ArrayBuffer;
          if (!this.addNewAppUserForm.get('U_dp')?.errors?.fileSizeValidator) {
            this.checkAspectRatio(img, 'U_dp');
            this.imgChangeEvt = { target: { files: [this.imageName] } };
          }
        };
        reader.readAsDataURL(this.imageName);
      }
    }
  
    if (field === 'U_aliasDp' && selectedFile) {
      // Append timestamp to alias image name
      const newFileName = FileValidator.addTimestamp(selectedFile.name);
      this.aliasImageName = new File([selectedFile], newFileName, { type: selectedFile.type });
  
      const aliasFileControl = this.addNewAppUserForm.get('U_aliasDp');
      aliasFileControl?.setValidators(FileValidator.fileSizeValidator(2048, this.aliasImageName));
      aliasFileControl?.updateValueAndValidity();
  
      const aliasFileType = this.aliasImageName.type.split('/')[0];
      const aliasFileExtension = this.aliasImageName.name.split('.').pop()?.toLowerCase();
      if (aliasFileType !== 'image' || aliasFileExtension === 'svg') {
        event.target.value = '';
        this.toastr.info('Please select an image file (excluding SVG).');
        this.aliasImageName = null;
        this.aliasImageSrc = null;
        return;
      }
  
      if (this.aliasImageName && aliasFileType === 'image') {
        const reader = new FileReader();
        const img = new Image();
        reader.onload = (e) => {
          this.aliasImageSrc = e.target?.result as string | ArrayBuffer;
          if (!this.addNewAppUserForm.get('U_aliasDp')?.errors?.fileSizeValidator) {
            this.checkAspectRatio(img, 'U_aliasDp');
            this.aliasImgChangeEvt = { target: { files: [this.aliasImageName] } };
          }
        };
        reader.readAsDataURL(this.aliasImageName);
      }
    }
  }
  

  checkAspectRatio(image: HTMLImageElement,controlName:any) {
    const aspectRatio = image.width / image.height;
    const control=this.addNewAppUserForm.get(controlName);
    if (aspectRatio !== 1) {
      // this.toastr.warning('The image must have a 1:1 aspect ratio. Please select a square image.', 'Invalid Aspect Ratio');
      control?.setErrors({fileAspectRatioValidator:true})
    }else{
      control?.setErrors(null);
    }
  }

 
  
  dataURItoBlob(dataURI: string): Blob {
    const byteString = atob(dataURI.split(',')[1]);
    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const uint8Array = new Uint8Array(arrayBuffer);
  
    for (let i = 0; i < byteString.length; i++) {
      uint8Array[i] = byteString.charCodeAt(i);
    }
  
    return new Blob([uint8Array], { type: mimeString });
  }
  

  cropImg(e:ImageCroppedEvent,controlName:string){
    if(controlName=='U_dp'){
    this.imageSrc=e.base64
  }else if(controlName=='U_aliasDp'){
    this.aliasImageSrc=e.base64
  }
  }

  showCropper(controlName:string) {
    if(controlName=='U_dp'){
      this.isCropperVisible = true;
    }else if(controlName=='U_aliasDp'){
      this.isAliasCropperVisible = true;
    }
  }

  hideCropper(controlName:string) {
    if(controlName=='U_dp'){
      this.isCropperVisible = false;
    }else if(controlName=='U_aliasDp'){
      this.isAliasCropperVisible = false;
    }
  }
  
  saveCroppedImage(controlName: string) {
    const addTimestamp = (fileName: string) => {
      const currentTimestamp = new Date().getTime();
      // Split the filename into name and extension
      const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');
      const extension = fileName.split('.').pop();
  
      // If the name already has a timestamp, remove it (assuming it's the last segment separated by '_')
      const cleanedName = nameWithoutExtension.replace(/_\d{13}$/, '');
  
      // Append the new timestamp to the name
      return `${cleanedName}_${currentTimestamp}.${extension}`;
    };
  
    if (controlName == 'U_dp') {
      if (this.imageSrc && this.imageName) {
        // Convert cropped image to Blob
        const blob = this.dataURItoBlob(this.imageSrc);
        // Add or replace timestamp in image name
        const newFileName = addTimestamp(this.imageName.name);
        this.imageName = new File([blob], newFileName, { type: this.imageName.type });
        console.log("Cropped Image", this.imageName);
  
        // Update imgChangeEvt with the new cropped image
        this.imgChangeEvt = { target: { files: [this.imageName] } };
        const fileControl = this.addNewAppUserForm.get('U_dp');
        fileControl?.clearValidators();
        fileControl?.updateValueAndValidity();
        this.hideCropper('U_dp');
      }
    } else if (controlName == 'U_aliasDp') {
      if (this.aliasImageSrc && this.aliasImageName) {
        // Convert cropped image to Blob
        const blob = this.dataURItoBlob(this.aliasImageSrc);
        // Add or replace timestamp in alias image name
        const newFileName = addTimestamp(this.aliasImageName.name);
        this.aliasImageName = new File([blob], newFileName, { type: this.aliasImageName.type });
        console.log("Cropped Alias Image", this.aliasImageName);
  
        // Update aliasImgChangeEvt with the new cropped image
        this.aliasImgChangeEvt = { target: { files: [this.aliasImageName] } };
        const fileControl = this.addNewAppUserForm.get('U_aliasDp');
        fileControl?.clearValidators();
        fileControl?.updateValueAndValidity();
        this.hideCropper('U_aliasDp');
      }
    }
  }
  
  
  
  initCropper(){

  }

  imgLoad(){

  }

  imgFailed(){
    this.toastr.error("Image Failed to show");
  }

  anonymousCheck(){
    if(!this.addNewAppUserForm.get('U_profileAnonymous')?.value){
        this.aliasImageName = null;
        this.aliasImageSrc = null;
        this.addNewAppUserForm.get('U_aliasName')?.clearValidators();
        this.addNewAppUserForm.get('U_aliasName')?.updateValueAndValidity();
        this.addNewAppUserForm.get('U_aliasDp')?.clearValidators();
        this.addNewAppUserForm.get('U_aliasDp')?.updateValueAndValidity();
    }

  }

  // uploadLogoUrl() {
  //   //To upload logo from add new company form
  //   if (!this.imageName) {
  //     // this.toastr.error('Please select an image.');
  //     return;
  //   }
  //   console.log('image', this.imageName);
  //   this.dataTransferService
  //     .uploadurl(this.imageName)
  //     .subscribe((res: any) => {});
  // }



  // removeMenu(index: number, formControlName: string) {
  //   const formArray = this.addNewAppUserForm.get(formControlName) as FormArray;
  //   formArray.removeAt(index);
  // }

  // onBackspaceKey(event: KeyboardEvent, formControlName: string) {
  //   if (event.key === 'Backspace') {
  //     const inputElement = event.target as HTMLInputElement;
  //     if (inputElement.value === '' && !event.shiftKey) {
  //       event.preventDefault();
  //       this.removeLastMenu(formControlName);
  //     }
  //   }
  // }

  // removeLastMenu(formArrayName: string) {
  //   const formArray = this.addNewAppUserForm.get(formArrayName) as FormArray;
  //   if (formArray.length > 0) {
  //     formArray.removeAt(formArray.length - 1);
  //   }
  // }

  // addMenu(event: any, formArrayName: string) {
  //   const menuId = event.target.value;
  //   console.log("Selected menuId:", menuId); // Debugging line
  //   if (menuId.trim() !== '') {
  //     const selectedMenu = this.University.find((menu: any) => menu.INS_id === menuId);
  //     console.log("Selected Menu:", selectedMenu); // Debugging line
  //     if (selectedMenu) {
  //       const formArray = this.addNewAppUserForm.get(formArrayName) as FormArray;
  //       const alreadyAdded = formArray.value.some((item: any) => item.INS_id === selectedMenu.INS_id);
  //       if (!alreadyAdded) {
  //         formArray.push(new FormControl({ INS_id: selectedMenu.INS_id, INS_title: selectedMenu.INS_title }));
  //       }

  //       event.target.value = '';
  //     }
  //   }
  // }

  // getFormArrayControls(formArrayName: string): FormControl[] {
  //   return (this.addNewAppUserForm.get(formArrayName) as FormArray).controls as FormControl[];
  // }

  // getMenuTitle(id: any): string {
  //   const menu = this.University.find((menu: any) => menu.INS_id === id);
  //   return menu ? menu.INS_title : 'Unknown';
  // }

  isItemSingleSelected(
    item: any,
    selectedArray: any[],
    singleSelect: boolean = false
  ): boolean {
    if (singleSelect) {
      return selectedArray.length > 0 && selectedArray[0].AM_id === item.AM_id;
    }
    return selectedArray.some((selected) => selected.AM_id === item.AM_id);
  }

  isItemSelected(
    item: any,
    selectedArray: any[],
    formControlName: any
  ): boolean {
    if (!selectedArray) {
      return false;
    }
    if (formControlName === 'LI_contentId') {
      return selectedArray.some((selected) => selected.IN_id === item.IN_id);
    }
    return selectedArray.some(
      (selected) => selected.option_id === item.option_id
    );
  }

  isItemSelectedForIdentity(
    item: any,
    selectedArray: any[],
    singleSelect: boolean = false,
    type: any
  ): boolean {
    if (!selectedArray) {
      console.log('item', item);
      return false;
    }
    if (singleSelect) {
      switch (type) {
        case 'Gender':
          return (
            selectedArray.length > 0 && selectedArray[0]?.GE_id === item?.GE_id
          );
          break;

        case 'Ethnicity':
          return (
            selectedArray.length > 0 && selectedArray[0]?.ET_id === item?.ET_id
          );
          break;

        case 'Religion':
          return (
            selectedArray.length > 0 && selectedArray[0]?.RE_id === item?.RE_id
          );
          break;

        case 'Sexual-Orientation':
          return (
            selectedArray.length > 0 && selectedArray[0]?.SO_id === item?.SO_id
          );
          break;

        case 'U_isDisability':
          return (
            selectedArray.length > 0 &&
            selectedArray[0]?.QUO_id === item?.QUO_id
          );
          break;

        case 'U_first_generation':
          return (
            selectedArray.length > 0 &&
            selectedArray[0]?.QUO_id === item?.QUO_id
          );
          break;

        case 'U_freeMeal':
          return (
            selectedArray.length > 0 &&
            selectedArray[0]?.QUO_id === item?.QUO_id
          );
          break;
      }
    }
    return selectedArray.some((selected) => selected?.QUO_id === item?.QUO_id);
  }

  toggleSelection(
    item: any,
    selectedArray: any[],
    formControlName: string,
    singleSelect: boolean = false,
    type: any
  ): void {
    const identifier =
      formControlName === 'LI_contentId' ? 'IN_id' : 'option_id';

    if (singleSelect) {
      if (this.isItemSelectedForIdentity(item, selectedArray, true, type)) {
        selectedArray.splice(0, 1);
      } else {
        selectedArray.splice(0, selectedArray.length);
        selectedArray.push(item);
      }
      this.calculateProfilePercentage();
    } else {
      const index = selectedArray.findIndex(
        (selected) => selected?.[identifier] === item[identifier]
      );
      if (index !== -1) {
        selectedArray.splice(index, 1);
      } else {
        selectedArray.push(item);
      }
    }

    this.updateFormArray(formControlName, selectedArray, singleSelect);
  }

  updateFormArray(
    controlName: string,
    selectedArray: any[],
    singleSelect: boolean
  ): void {
    const selectedItem = selectedArray.length > 0 ? selectedArray[0] : null;

    if (singleSelect) {
      const singleSelectFields: { [key: string]: string } = {
        U_genderId: 'GE_id',
        U_ethinicityId: 'ET_id',
        U_religionId: 'RE_id',
        U_sexualOrientationId: 'SO_id',
        U_isDisability: 'QUO_id',
        U_first_generation: 'QUO_id',
        U_freeMeal: 'QUO_id',
      };

      if (controlName in singleSelectFields) {
        this.addNewAppUserForm.setControl(
          controlName,
          new FormControl(
            selectedItem ? selectedItem[singleSelectFields[controlName]] : null
          )
        );
      } else {
        console.warn(`Unexpected control name: ${controlName}`);
      }
    } else {
      if (controlName === 'LI_contentId') {
        const formArray = this.addNewAppUserForm.get(controlName) as FormArray;
        formArray.clear(); // Clear existing controls

        selectedArray.forEach((item) => {
          formArray.push(new FormControl(item.IN_id)); // Add new controls
        });
      } else {
        const mergedArray = [
          ...this.selectedActivities,
          ...this.selectedWorkTypes,
        ];

        const formArray = this.formBuilder.array(
          mergedArray.map(
            (item) =>
              new FormControl({
                option_id: item.option_id,
                option_title: item.option_title,
                quiz_id: item.quiz_id,
              })
          )
        );
        this.addNewAppUserForm.setControl(controlName, formArray);
      }
    }
  }

  getAllPreferencesData() {
    this.dataTransferService.getAllPreferencesData().subscribe(
      (res: any) => {
        if (res.statusCode == 200 && res.data) {
          res.data.forEach((quiz: any) => {
            if (quiz.quiz_options && quiz.quiz_options.length > 0) {
              this.populateReferencesData(quiz.quiz_options);
            } else {
              this.ngxSpinnerService.hide('globalSpinner');
              console.error('No Preferences Data available:', quiz.QU_id);
            }
          });
        } else {
          console.error(
            'Failed to retrieve Preference sData or data is empty. Status code:',
            res.statusCode
          );
        }
      },
      (error: any) => {
        console.error('Error while fetching quiz data:', error);
      }
    );
  }

  populateReferencesData(options: any[]) {
    options.forEach((item) => {
      const formattedItem = {
        option_id: item.QUO_id,
        option_title: item.QUO_title,
        quiz_id: item.QUO_quizid,
      };
      switch (item.QUO_quizid) {
        case '5':
          this.ActivitiesYouLike.push(formattedItem);
          break;

        // case '4':
        //   this.LikedSectors.push(formattedItem);
        //   break;

        case '2':
          this.WorkType.push(formattedItem);
          break;
      }
    });
  }

  getIdentityData(): Promise<void> {
    this.ngxSpinnerService.show('globalSpinner');
    return new Promise((resolve, reject) => {
      this.dataTransferService.getIdentityData().subscribe(
        (res: any) => {
          if (res.statusCode === 200) {
            // this.ngxSpinnerService.hide('globalSpinner');
            this.Ethnicities = res.ethnicities;
            this.Genders = res.genders;
            this.Religions = res.religion;
            this.SexualOrientation = res.sexual_orientation;
            resolve();
          } else {
            this.ngxSpinnerService.hide('globalSpinner');
            console.error(
              'Failed to retrieve identity data. Status code:',
              res.statusCode
            );
            reject('Failed to retrieve identity data');
          }
        },
        (error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Error while fetching identity data:', error);
          reject(error);
        }
      );
    });
  }

  getAllUniversity() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllUniversity().subscribe(
      (res: any) => {
        if (res.statusCode == 200) {
          this.University = res.data.sort((a: any, b: any) =>
            a.INS_title.localeCompare(b.INS_title)
          );
          console.log('this.University', this.University);
          this.filteredUniversityOptions = this.University;
          this.ngxSpinnerService.hide('globalSpinner');
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error(
            'Failed to retrieve University Data or data is empty. Status code:',
            res.statusCode
          );
        }
      },
      (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error while fetching University data:', error);
      }
    );
  }

  getindustry(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.dataTransferService.getIndustryData().subscribe(
        (res: any) => {
          if (res.status === 200) {
            this.LikedSectors = res.data.sort((a: any, b: any) =>
              a.IN_name.localeCompare(b.IN_name)
            );
            console.log('this.LikedSectors', this.LikedSectors);
            resolve(this.LikedSectors);
          } else {
            console.error('Failed to fetch industry data:', res);
            reject('Failed to fetch industry data');
          }
        },
        (error: any) => {
          console.error('Error fetching industry data:', error);
          reject(error);
        }
      );
    });
  }

  getAllDegree() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllDegree().subscribe(
      (res: any) => {
        if (res.statusCode == 200) {
          this.ngxSpinnerService.hide('globalSpinner');
          this.Degree = res.data.sort((a: any, b: any) =>
            a.ED_name.localeCompare(b.ED_name)
          );
          console.log('this.Degree', this.Degree);
          this.filteredDegreeOptions = this.Degree;
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error(
            'Failed to retrieve Degree Data or data is empty. Status code:',
            res.statusCode
          );
        }
      },
      (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error while fetching University data:', error);
      }
    );
  }

  filterOptions(formControlName: string) {
    if (formControlName === 'U_institute') {
      const searchTerm = this.universitySearchControl.value
        .toLowerCase()
        .trim();
      this.filteredUniversityOptions = this.University.filter((option) =>
        option.INS_title.toLowerCase().includes(searchTerm)
      );
    } else if (formControlName === 'U_education') {
      const searchTerm = this.degreeSearchControl.value.toLowerCase().trim();
      this.filteredDegreeOptions = this.Degree.filter((option) =>
        option.ED_name.toLowerCase().includes(searchTerm)
      );
    }
  }

  removeMenu(formControlName: string, id: any) {
    const formControl = this.addNewAppUserForm.get(formControlName);

    if (formControl) {
      formControl.setValue(null); // Clear the form control's value

      if (formControlName === 'U_institute') {
        this.selectedUniversity = null;
        this.showTag = false;
      } else if (formControlName === 'U_education') {
        this.selectedDegree = null;
        this.showTagOfEducation = false;
      }
    } else {
      console.error('Form control not found:', formControlName);
    }
  }

  addMenu(menu: any, formArrayName: string, title?: any) {
    const formControl = this.addNewAppUserForm.get(formArrayName);
    if (formControl) {
      if (formArrayName === 'U_institute') {
        if (title === 'toPatchExisting') {
          formControl.setValue(menu);
        } else {
          formControl.setValue(menu.INS_id);
        }
        this.selectedUniversity = menu;
        this.universitySearchControl.setValue('');
        this.filteredUniversityOptions = this.University;
        this.showTag = true;
        this.showDropdown = false; // Hide dropdown after selection // Assuming menu.INS_id is the ID you want to store
      } else if (formArrayName === 'U_education') {
        if (title === 'toPatchExisting') {
          formControl.setValue(menu);
        } else {
          formControl.setValue(menu.ED_id);
        }
        this.selectedDegree = menu;
        this.degreeSearchControl.setValue('');
        this.filteredDegreeOptions = this.Degree;
        this.showTagOfEducation = true;
        this.showDropdownOfEducation = false;
        console.log('degree form ', this.addNewAppUserForm.value); // Assuming menu.ED_id is the ID you want to store
      }
    }
    this.calculateProfilePercentage();
  }

  getFormArrayValues(formArrayName: string): any[] {
    const formArray = this.addNewAppUserForm.get(formArrayName) as FormArray;
    return formArray ? formArray.value : [];
  }

  getMenuTitle(id: any, formArrayName: string): any {
    if (formArrayName === 'U_institute') {
      const menu = this.University.find((menu: any) => menu?.INS_id === id);
      return menu?.INS_title ? menu?.INS_title : 'Unknown';
    }

    if (formArrayName === 'U_education') {
      const menu = this.Degree.find((menu: any) => menu?.ED_id === id);
      return menu?.ED_name ? menu?.ED_name : 'Unknown';
    }
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: any): void {
    if (!event.target.closest('.autocomplete-container-education')) {
      this.showDropdownOfEducation = false; // Hide education dropdown if clicked outside
    }
    if (!event.target.closest('.autocomplete-container-institute')) {
      this.showDropdown = false;
    }
    // Add similar checks for other dropdowns as needed
  }
  fetchPostcodes(query: string): Observable<string[]> {
    if (query.length > 0) {
      return this.httpClient
        .get<any>(`https://api.postcodes.io/postcodes/${query}/autocomplete`)
        .pipe(
          map((response) => response.result || []),
          catchError(() => of([])) // Handle errors and return an empty array
        );
    } else {
      return of([]); // Return an observable of an empty array
    }
  }

  filterPostcodes() {
    const query = this.postcodeSearchControl.value;
    if (query && query.length > 2) {
      this.fetchPostcodes(query).subscribe((postcodes: string[]) => {
        this.filteredPostcodes = postcodes;
        this.showPostcodeDropdown = postcodes.length > 0;
      });
    } else {
      this.filteredPostcodes = [];
      this.showPostcodeDropdown = false;
    }
  }

  selectPostcode(postcode: string) {
    this.selectedPostcode = postcode;
    this.postcodeSearchControl.setValue('');
    this.filteredPostcodes = [];
    this.showPostcodeDropdown = false;

    // Fetch detailed information about the selected postcode
    this.httpClient
      .get<any>(`https://api.postcodes.io/postcodes?q=${postcode}`)
      .subscribe((response) => {
        const result = response.result && response.result[0];
        if (result) {
          this.selectedPostcode = `${result.admin_district}, ${result.country}`; // Customize as needed

          // Update the form control with the detailed name
          this.addNewAppUserForm.get('U_postcode')?.setValue(postcode);
        } else {
          // Handle case where no result is returned
          this.addNewAppUserForm.get('U_postcode')?.setValue('');
        }
      });
    this.calculateProfilePercentage();
  }

  removeSelectedPostcode() {
    this.selectedPostcode = null;
    this.addNewAppUserForm.get('U_postcode')?.setValue('');
    this.calculateProfilePercentage();
  }
  
  updateUserPreferences(){
    this.applyQuizOptionValidator();
    const identityFields = [
      'U_ethinicityId',
      'U_genderId',
      'U_isDisability',
      'U_religionId',
      'U_sexualOrientationId',
      'U_first_generation',
      'U_freeMeal',
      'U_ethinicityId',
      'U_ethinicity_Toggle',
      'U_genderId',
      'U_gender_Toggle',
      'U_isDisability',
      'U_disability_Toggle',
      'U_religionId',
      'U_religion_Toggle',
      'U_sexualOrientationId',
      'U_sexuality_Toggle',
      'U_institute',
      'U_postcode',
      'U_postcode_Toggle',
      'U_first_generation',
      'U_first_generation_Toggle',
      'U_freeMeal',
      'U_freeMeal_Toggle',
      'U_education',
      'U_name',
      'U_email',
      'U_password',
      'U_profilePercentage',
      'U_dp',
      ];
    identityFields.forEach(field => {
      this.addNewAppUserForm.get(field)?.clearValidators();
      this.addNewAppUserForm.get(field)?.updateValueAndValidity();
    });
    
      const nonIdentityFields = [
        'LI_contentId'
      ];
      nonIdentityFields.forEach(field => {
        this.addNewAppUserForm.get(field)?.setValidators(Validators.required);
        this.addNewAppUserForm.get(field)?.updateValueAndValidity();
      });
  
      // Check if the form is valid
      if (this.addNewAppUserForm.invalid) {
        this.toastr.info("Please fill all required identity fields.");
        return;
      }
    this.ngxSpinnerService.show('globalSpinner');  
    const data = {
      quizAns: [
        {
          data: 'Answer 1',
          quizId: 'NA',
          QUA_quiz_options: this.addNewAppUserForm.value.QUA_quiz_options,
        },
      ],
      LI_contentId: this.addNewAppUserForm.value.LI_contentId,
      U_id:this.userData.U_id,
      LI_contentType: "industry",
      // U_seeker_disliked_industries:[''],
    };
    console.log("Data to update preferences",data);
    this.dataTransferService.updateUserPreferences(data).subscribe((res:any)=>{
      if(res.statusCode==200){
        this.ngxSpinnerService.hide('globalSpinner');
        this.toastr.success("User preferences updated successfully.")
      }else{
        console.error("Couldn't get status code 200");
        this.toastr.error("Unable to update user preferences");
              this.ngxSpinnerService.hide('globalSpinner');
      }
      },(error:any)=>{
      this.ngxSpinnerService.hide('globalSpinner');
      console.error("error",error);
      this.toastr.error("Unable to update user preferences");
      });
  }

  updateUserIdentity(){
    const nonIdentityFields = [
      'U_name',
      'U_email',
      'U_password',
      'U_profilePercentage',
      'U_dp',
      'QUA_quiz_options',
      'LI_contentId'
    ];
    nonIdentityFields.forEach(field => {
      this.addNewAppUserForm.get(field)?.clearValidators();
      this.addNewAppUserForm.get(field)?.updateValueAndValidity();
    });

      // Re-validate identity fields, especially U_genderId
      const identityFields = [
        'U_ethinicityId',
        'U_genderId',
        'U_isDisability',
        'U_religionId',
        'U_sexualOrientationId',
        'U_first_generation',
        'U_freeMeal',
        ];
      identityFields.forEach(field => {
        this.addNewAppUserForm.get(field)?.setValidators(Validators.required);
        this.addNewAppUserForm.get(field)?.updateValueAndValidity();
      });
  
      // Check if the form is valid
      if (this.addNewAppUserForm.invalid) {
        this.toastr.info("Please fill all required identity fields.");
        return;
      }
    this.ngxSpinnerService.show('globalSpinner');  
    this.calculateProfilePercentage();
    const data = {
      U_id:this.userData.U_id,
      U_profilePercentage: this.addNewAppUserForm.value.U_profilePercentage,
      U_ethinicityId: this.addNewAppUserForm.value.U_ethinicityId,
      U_ethinicity_Toggle: this.addNewAppUserForm.value.U_ethinicity_Toggle,
      U_genderId: this.addNewAppUserForm.value.U_genderId,
      U_gender_Toggle: this.addNewAppUserForm.value.U_gender_Toggle,
      U_isDisability: this.addNewAppUserForm.value.U_isDisability,
      U_disability_Toggle: this.addNewAppUserForm.value.U_disability_Toggle,
      U_religionId: this.addNewAppUserForm.value.U_religionId,
      U_religion_Toggle: this.addNewAppUserForm.value.U_religion_Toggle,
      U_sexualOrientationId:this.addNewAppUserForm.value.U_sexualOrientationId,
      U_sexuality_Toggle: this.addNewAppUserForm.value.U_sexuality_Toggle,
      U_institute: this.addNewAppUserForm.value.U_institute? this.addNewAppUserForm.value.U_institute: '',
      U_postcode: this.addNewAppUserForm.value.U_postcode? this.addNewAppUserForm.value.U_postcode: '',
      U_postcode_Toggle: this.addNewAppUserForm.value.U_postcode_Toggle,
      U_first_generation: this.addNewAppUserForm.value.U_first_generation,
      U_first_generation_Toggle:this.addNewAppUserForm.value.U_first_generation_Toggle,
      U_freeMeal: this.addNewAppUserForm.value.U_freeMeal,
      U_freeMeal_Toggle: this.addNewAppUserForm.value.U_freeMeal_Toggle,
      U_education: this.addNewAppUserForm.value.U_education? this.addNewAppUserForm.value.U_education: '',      
    };  
  console.log("Data to update identity",data); 
  this.dataTransferService.updateUserIdentity(data).subscribe((res:any)=>{
  if(res.statusCode===200){
    this.ngxSpinnerService.hide('globalSpinner');
    this.toastr.success("User identity updated successfully.")
  }else{
    console.error("Couldn't get status code 200");
    this.toastr.error("Unable to update user identity");
  }
  },(error:any)=>{
  this.ngxSpinnerService.hide('globalSpinner');
  console.error("error",error);
  this.toastr.error("Unable to update user identity");
  });
  
  }

 async updateUserDetails(){
  try{
    if(!this.addNewAppUserForm.get('U_aliasDp')?.errors?.fileAspectRatioValidator&&!this.addNewAppUserForm.get('U_dp')?.errors?.fileAspectRatioValidator){
    let userDp;
    let aliasDp;
    this.ngxSpinnerService.show('globalSpinner');

    if(this.addNewAppUserForm.value.U_dp){
     await this.uploadLogoUrl(this.imageName);
     userDp=this.baseUrl+this.imageName.name;
    }else{
      userDp=this.userData.U_dp
    }

    if(this.addNewAppUserForm.value.U_aliasDp){
      await this.uploadLogoUrl(this.aliasImageName);
      aliasDp=this.baseUrl+this.aliasImageName.name;
    } else{
      aliasDp=this.userData.U_aliasDp
    }

    const data={
      U_id:this.userData.U_id,
      U_dp: userDp,
      U_aliasName: (this.addNewAppUserForm.value.U_aliasName||this.userData.U_aliasName)||'',
      U_aliasDp: aliasDp||'',
    }
    console.log("User details data to update",data);
    
    this.dataTransferService.updateUserDetails(data).subscribe((res:any)=>{
        this.ngxSpinnerService.hide('globalSpinner');
        this.toastr.success("User details updated successfully.")
      },(error:any)=>{
      this.ngxSpinnerService.hide('globalSpinner');
      console.error("error",error);
      this.toastr.error("Unable to update user details");
      });
    }else{
      this.toastr.info("Please resize the image");
    }
    }catch{
      this.toastr.error("Unable to update user details");
    }
  }

  applyRequiredValidators() {
    const requiredFields = [
      'U_name',
      'U_email',
      'U_password',
      'U_profilePercentage',
      'U_ethinicityId',
      'U_genderId',
      'U_isDisability',
      'U_religionId',
      'U_sexualOrientationId',
      'U_first_generation',
      'U_freeMeal',
      'LI_contentId',
    ];
  
    const excludedFields = [
      'U_dp',
      'U_education',
      'U_postcode',
      'U_institute',
      'U_isExpert'
    ];
  
    Object.keys(this.addNewAppUserForm.controls).forEach((key) => {
      if (requiredFields.includes(key) && !excludedFields.includes(key)) {
        const control = this.addNewAppUserForm.get(key);
        if (control) {
          const currentValidators = control.validator ? [control.validator] : [];
          control.setValidators([...currentValidators, Validators.required]);
          control.updateValueAndValidity(); // Update the control to reflect the new validators
        }
      }
    });
  
    
  }
  
  applyQuizOptionValidator(){
    const quizOptionsControl = this.addNewAppUserForm.get('QUA_quiz_options');
    if (quizOptionsControl) {
      const hasQuizIdFive = quizOptionsControl.value.some((option: any) => option.quiz_id === '5');
      if (!hasQuizIdFive) {
        quizOptionsControl.setErrors({ quizIdFiveMissing: true });
      } else {
        quizOptionsControl.setErrors(null);
      }
    }
  }
  

  createNewAppUser() {
  this.applyRequiredValidators();
  this.applyQuizOptionValidator();

  if (this.addNewAppUserForm.invalid) {
    console.log('this.addNewAppUserForm.value', this.addNewAppUserForm.value);
    Object.keys(this.addNewAppUserForm.controls).forEach((key) => {
      const controlErrors = this.addNewAppUserForm.get(key)?.errors;
      if (controlErrors) {
        console.log(`Field ${key} is invalid:`, controlErrors);
      }
    });
    this.toastr.info('Please fill all required fields correctly');
    return;
  } else {
    // Initialize the Sharer DP and Alias DP URL variables
    let sharerDpUrl;
    let aliasDpUrl;

    // Handle Sharer DP upload if image is available
    const sharerDpPromise = this.imageName ? this.uploadLogoUrl(this.imageName) : Promise.resolve(null);
    console.log("sharerDpPromise",sharerDpPromise);
    
    // Handle Alias DP upload if image is available
    const aliasDpPromise = this.aliasImageName ? this.uploadLogoUrl(this.aliasImageName) : Promise.resolve(null);
    console.log("aliasDpPromise",aliasDpPromise);

    // Wait for both images to upload (if present)
    Promise.all([sharerDpPromise, aliasDpPromise]).then(([sharerDpResponse, aliasDpResponse]) => {
      // Set the URLs if the images were uploaded
      sharerDpUrl = this.imageName ? this.baseUrl + this.imageName?.name : '';
      console.log("sharerDpUrl",sharerDpUrl);
      aliasDpUrl = this.aliasImageName ? this.baseUrl + this.aliasImageName?.name : '';
      console.log("aliasDpUrl",aliasDpUrl);

      // Finalize the post data with both Sharer and Alias DP URLs
      this.finalizePostData(sharerDpUrl, aliasDpUrl);
    }).catch((error) => {
      console.error('Error uploading images:', error);
      this.toastr.error("Couldn't upload images.");
    });
  }
}

// Function to upload the image and return a Promise
uploadLogoUrl(image: File): Promise<any> {
  return new Promise((resolve, reject) => {
    if (!image) {
      resolve(null);
    }
    console.log('Uploading image:', image);
    this.dataTransferService.uploadurl(image).subscribe(
      (res: any) => {
        console.log('Image upload success:', image?.name);
        resolve(res); // Return the response when the upload is successful
      },
      (error: any) => {
        console.error('Image upload failed:', error);
        reject(error); // Reject the promise if the upload fails
      }
    );
  });
}

// Function to finalize postData and submit form
finalizePostData(sharerDpUrl: string, aliasDpUrl: string) {
  const postData = {
    U_name: this.addNewAppUserForm.value.U_name,
    U_email: this.addNewAppUserForm.value.U_email,
    U_password: this.addNewAppUserForm.value.U_password,
    U_isExpert: this.addNewAppUserForm.value.U_isExpert,
    U_countryId: 'UK',
    U_profilePercentage: this.addNewAppUserForm.value.U_profilePercentage,
    U_dob: '',
    U_ethinicityId: this.addNewAppUserForm.value.U_ethinicityId,
    U_ethinicity_Toggle: this.addNewAppUserForm.value.U_ethinicity_Toggle,
    U_genderId: this.addNewAppUserForm.value.U_genderId,
    U_gender_Toggle: this.addNewAppUserForm.value.U_gender_Toggle,
    U_isDisability: this.addNewAppUserForm.value.U_isDisability,
    U_disability_Toggle: this.addNewAppUserForm.value.U_disability_Toggle,
    U_religionId: this.addNewAppUserForm.value.U_religionId,
    U_religion_Toggle: this.addNewAppUserForm.value.U_religion_Toggle,
    U_sexualOrientationId: this.addNewAppUserForm.value.U_sexualOrientationId,
    U_sexuality_Toggle: this.addNewAppUserForm.value.U_sexuality_Toggle,
    U_institute: this.addNewAppUserForm.value.U_institute || '',
    U_postcode: this.addNewAppUserForm.value.U_postcode || '',
    U_postcode_Toggle: this.addNewAppUserForm.value.U_postcode_Toggle,
    U_first_generation: this.addNewAppUserForm.value.U_first_generation,
    U_first_generation_Toggle: this.addNewAppUserForm.value.U_first_generation_Toggle,
    U_dp: sharerDpUrl || '', // Sharer DP URL
    U_aliasDp: aliasDpUrl || '', // Alias DP URL
    U_aliasName:this.addNewAppUserForm.value.U_aliasName,
    U_profileAnonymous:this.addNewAppUserForm.value.U_profileAnonymous,
    U_isSharer: '1',
    U_industryId: '1',
    U_roleId: '1',
    U_regionalAccentId: '1',
    U_registertype: 'email',
    U_registertypeId: '1',
    U_activeStatus: '1',
    U_freeMeal: this.addNewAppUserForm.value.U_freeMeal,
    U_freeMeal_Toggle: this.addNewAppUserForm.value.U_freeMeal_Toggle,
    U_education: this.addNewAppUserForm.value.U_education || '',
    U_totalLikeCount: 0,
    U_seeker_disliked_industries: [
      { U_seekerDislikedIndId: '' },
    ],
    quizAns: [
      {
        data: 'Answer 1',
        quizId: 'NA',
        QUA_quiz_options: this.addNewAppUserForm.value.QUA_quiz_options,
      },
    ],
    LI_contentId: this.addNewAppUserForm.value.LI_contentId,
  };

  console.log(postData);
  this.ngxSpinnerService.show('globalSpinner');

  this.dataTransferService.createNewAppUser(postData).subscribe(
    (res: any) => {
      if (res.statusCode == 200) {
        this.toastr.success('New sharer added successfully.');
        this.ngxSpinnerService.hide('globalSpinner');
        this.router.navigate(['/actions/app-users']);
        this.dataTransferService.getAllAppUsers();
      }else{
        this.toastr.error('Something went wrong');
        console.error('Error:', "Status code error");
        this.ngxSpinnerService.hide('globalSpinner');
      }
    },
    (error: any) => {
      this.ngxSpinnerService.hide('globalSpinner');
      if (error.status === 400) {
        this.toastr.error('This sharer already exists.');
      }else if(error.status === 500){
        this.toastr.success('New sharer added successfully.');
        this.router.navigate(['/actions/app-users']);
        this.dataTransferService.getAllAppUsers();

      } else {
        console.error('Error:', error);
        this.toastr.error("Couldn't add new sharer.");
      }
    }
  );
}



  showAddUniversityDegreeModal(title: any) {
    if (title === 'University') {
      const modal = document.getElementById('add-university-modal');
      if (modal) {
        modal.style.display = 'block';
      }
    } else {
      const modal = document.getElementById('add-degree-modal');
      if (modal) {
        modal.style.display = 'block';
      }
    }
  }

  hideAddUniversityDegreeModal(title: any) {
    if (title === 'University') {
      const modal = document.getElementById('add-university-modal');
      if (modal) {
        modal.style.display = 'none';
      }
      const universityInput = document.getElementById(
        'INS_title'
      ) as HTMLInputElement;
      if (universityInput) {
        universityInput.value = '';
      }
    } else {
      const modal = document.getElementById('add-degree-modal');
      if (modal) {
        modal.style.display = 'none';
      }

      const degreeInput = document.getElementById(
        'ED_name'
      ) as HTMLInputElement;
      if (degreeInput) {
        degreeInput.value = '';
      }
    }
  }

  addUniversity() {
    const titleElement = document.getElementById(
      'INS_title'
    ) as HTMLInputElement;
    const title = titleElement ? titleElement.value : '';
    console.log('title', title);
    const data = {
      INS_title: title,
    };
    this.dataTransferService.addUniversity(data).subscribe(
      (res: any) => {
        if (res.statusCode === 200) {
          this.toastr.success('New University Added Successfully');
          this.getAllUniversity();
          this.hideAddUniversityDegreeModal('University');
        }
      },
      (error: any) => {
        if (error.status === 400) {
          this.toastr.error('The university already exists, as given.');
          return;
        }
        this.toastr.error('Unable to add university');
        console.log('Error while adding university', error);
      }
    );
  }

  addDegree() {
    const titleElement = document.getElementById('ED_name') as HTMLInputElement;
    const title = titleElement ? titleElement.value : '';
    console.log('title', title);
    const data = {
      ED_name: title,
    };
    this.dataTransferService.addDegree(data).subscribe(
      (res: any) => {
        if (res.statusCode === 200) {
          this.toastr.success('New Degree Added Successfully');
          this.getAllDegree();
          this.hideAddUniversityDegreeModal('Degree');
        }
      },
      (error: any) => {
        if (error.status === 400) {
          this.toastr.error('The Degree already exists, as given.');
          return;
        }
        this.toastr.error('Unable to add degree');
        console.log('Error while adding degree', error);
      }
    );
  }

  
}

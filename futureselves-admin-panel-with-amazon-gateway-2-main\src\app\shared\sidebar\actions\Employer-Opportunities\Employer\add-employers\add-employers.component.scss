
  .footer{
  background-color: white;
  }
  .head-exst{
    width: 100vh;
  }

  
 .fa-plus{
  font-size: small;
 }

 .subtitle{
  color: rgba(81, 80, 80, 0.856) !important;
 }

 .character-count{
    font-size: smaller;
    margin-top: 2px;
    display: flex;
    justify-content: flex-end;
 }

 .suggestion{
  font-size: 12px;
 }



@media screen and (min-width: 992px) and (max-width: 1224px) {
  .insight-btn{
    padding: 4px 5px;
  }

  .fas{
    font-size: small;
  }
}

@media screen and (min-width: 320px) and (max-width: 768px) {
.btns{
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.add-insight-btn{
  margin-bottom: 20px;
}
}

.existing-logo {
  max-width: 200px !important; 
  white-space: nowrap; 
  overflow: hidden; 
  text-overflow: ellipsis; 
  vertical-align: middle; 
}

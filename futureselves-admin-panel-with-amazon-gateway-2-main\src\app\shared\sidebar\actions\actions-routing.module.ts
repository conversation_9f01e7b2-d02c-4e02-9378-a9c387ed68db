import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ActionsComponent } from './actions.component';
import { InsightComponent } from './Insights/insight/insight.component';
import { DegreeComponent } from './degree/degree.component';
import { CollectionsComponent } from './Collection/collections/collections.component';
import {EthnicityComponent} from './ethnicity/ethnicity.component';
import {RegionalAccentComponent} from './regional-accent/regional-accent.component'
import {SexualOrientationComponent} from './sexual-orientation/sexual-orientation.component'
import { IndustryComponent} from './industry/industry.component'
import { EmployerOpportunitiesComponent } from './Employer-Opportunities/Employer/employer-opportunities/employer-opportunities.component';
import { OpportunitiesComponent } from './Employer-Opportunities/Opportunity/opportunities/opportunities.component';
import { UniversityListComponent } from './university-list/university-list/university-list.component';
import { AddEditUniversityListComponent } from './university-list/add-edit-university-list/add-edit-university-list.component';
import { AddEditInsightComponent } from './Insights/add-edit-insight/add-edit-insight.component';
import { RoleComponent } from 'src/app/admins/role/role.component';
import { RolesComponent } from './Sectors and roles/All Roles/roles/roles.component';
import { AddEditRoleComponent } from './Sectors and roles/All Roles/add-edit-role/add-edit-role.component';
import { SectorsComponent } from './Sectors and roles/All Sectors/sectors/sectors.component';
import { AddEditSectorComponent } from './Sectors and roles/All Sectors/add-edit-sector/add-edit-sector.component';
import { AddEditCollectionComponent } from './Collection/add-edit-collection/add-edit-collection.component';
import { AccessControlGuard } from 'src/app/Guards/access-control.guard';
import { UnauthorizePageComponent } from 'src/app/Error-Pages/unauthorize-page/unauthorize-page.component';
import { AppUsersComponent } from './App-User-Management/app-users/app-users.component';
import { UserStatusComponent } from './App-User-Management/user-status/user-status.component';
import { EditAppUserComponent } from './App-User-Management/edit-app-user/edit-app-user.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { AddNewInsightComponent } from './App-User-Management/add-new-insight/add-new-insight.component';
import { ChoosePositionComponent } from './App-User-Management/choose-position/choose-position.component';
import { AddEmployersComponent } from './Employer-Opportunities/Employer/add-employers/add-employers.component';
import { AddEditOpportunityComponent } from './Employer-Opportunities/Opportunity/add-edit-opportunity/add-edit-opportunity.component';

// const routes: Routes = [
//   { path: '', component: ActionsComponent },
  
//   //insight module
//   { path:'insights',component:SoundbiteComponent, canActivate: [AccessControlGuard]},
//   { path:'insights/edit-insights',component:AddEditGradVisorComponent,canActivate: [AccessControlGuard]},

//   { path:'degree',component:DegreeComponent},


//   //collection module
//   { path:'collections',component:CollectionsComponent,canActivate: [AccessControlGuard]},
//   {path:'collections/add-edit-collection',component:AddEditCollectionComponent,canActivate: [AccessControlGuard]},

//   { path:'ethnicity',component:EthnicityComponent},
//   { path:'religion',component:ReligionComponent},
//   { path:'regional-accent',component:RegionalAccentComponent},
//   { path:'sexual-orientation',component:SexualOrientationComponent},
//   { path:'industry',component:IndustryComponent},


//   //employer-opportunities module
//   { path:'employer-opportunities', component:EmployerOpportunitiesComponent,canActivate: [AccessControlGuard]},
//   { path:'employer-opportunities/add-edit-employer', component:AddEmployersComponent,canActivate: [AccessControlGuard]},
//   { path:'employer-opportunities/existing-opportunities', component:OpportunitiesComponent,canActivate: [AccessControlGuard]},
//   { path:'employer-opportunities/existing-opportunities/add-edit-opportunity', component:AddEditOpportunityComponent,canActivate: [AccessControlGuard]},


//   //universities module
//   { path:'universities',component:UniversityListComponent},
//   { path:'universities/add-edit-university',component:AddEditUniversityListComponent},


//   //sectors module
//   { path:'sectors',component:SectorsComponent,canActivate: [AccessControlGuard]},
//   { path:'sectors/add-edit-sector',component:AddEditSectorComponent,canActivate: [AccessControlGuard]},
//   { path:'sectors/roles',component:RolesComponent,canActivate: [AccessControlGuard]},
//   { path:'sectors/roles/add-edit-role',component:AddEditRoleComponent,canActivate: [AccessControlGuard]},


//   //app-users module
//   { path:'app-users', component: AppUsersComponent,canActivate: [AccessControlGuard]  },
//   { path:'app-users/user-status', component: UserStatusComponent,canActivate: [AccessControlGuard] },
//   { path:'app-users/add-edit-app-user', component: EditAppUserComponent,canActivate: [AccessControlGuard] },
//   { path:'app-users/choose-position',component:ChoosePositionComponent,canActivate: [AccessControlGuard]},
//   { path:'app-users/choose-position/add-new-insight', component: AddNewInsightComponent,canActivate: [AccessControlGuard] },

//   {path: 'notifications', component:NotificationsComponent}
// ];

const routes: Routes = [
  { path: '', component: ActionsComponent },

  // Insights
  {
    path: 'insights',
    canActivate: [AccessControlGuard],
    children: [
      { path: '', component: InsightComponent },
      { path: 'edit-insights', component: AddEditInsightComponent }
    ]
  },

  { path: 'degree', component: DegreeComponent },

  // Collections
  {
    path: 'collections',
    canActivate: [AccessControlGuard],
    children: [
      { path: '', component: CollectionsComponent },
      { path: 'add-edit-collection', component: AddEditCollectionComponent }
    ]
  },

  { path: 'ethnicity', component: EthnicityComponent },
  { path: 'regional-accent', component: RegionalAccentComponent },
  { path: 'sexual-orientation', component: SexualOrientationComponent },
  { path: 'industry', component: IndustryComponent },

  // Employer Opportunities
  {
    path: 'employer-opportunities',
    canActivate: [AccessControlGuard],
    children: [
      { path: '', component: EmployerOpportunitiesComponent },
      { path: 'add-edit-employer', component: AddEmployersComponent },
      { path: 'existing-opportunities', component: OpportunitiesComponent },
      { path: 'existing-opportunities/add-edit-opportunity', component: AddEditOpportunityComponent }
    ]
  },

  // Universities
  {
    path: 'universities',
    canActivate: [AccessControlGuard],
    children: [
      { path: '', component: UniversityListComponent },
      { path: 'add-edit-university', component: AddEditUniversityListComponent }
    ]
  },

  // Sectors
  {
    path: 'sectors',
    canActivate: [AccessControlGuard],
    children: [
      { path: '', component: SectorsComponent },
      { path: 'add-edit-sector', component: AddEditSectorComponent },
      { path: 'roles', component: RolesComponent },
      { path: 'roles/add-edit-role', component: AddEditRoleComponent }
    ]
  },

  // App Users
  {
    path: 'app-users',
    canActivate: [AccessControlGuard],
    children: [
      { path: '', component: AppUsersComponent },
      { path: 'user-status', component: UserStatusComponent },
      { path: 'add-edit-app-user', component: EditAppUserComponent },
      { path: 'choose-position', component: ChoosePositionComponent },
      { path: 'choose-position/add-new-insight', component: AddNewInsightComponent }
    ]
  },

  // Notifications
  { path: 'notifications', component: NotificationsComponent }
];


@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ActionsRoutingModule { }

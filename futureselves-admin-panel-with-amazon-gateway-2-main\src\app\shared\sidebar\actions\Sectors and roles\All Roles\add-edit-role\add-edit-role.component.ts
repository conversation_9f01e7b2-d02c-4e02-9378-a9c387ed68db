import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators,FormArray } from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
import { FileValidator } from 'src/app/shared/validators/fileValidator.validator';

@Component({
  selector: 'app-add-edit-role',
  templateUrl: './add-edit-role.component.html',
  styleUrls: ['./add-edit-role.component.scss']
})
export class AddEditRoleComponent implements OnInit {

  imageSrc: string | ArrayBuffer | null;
  addRoleForm: FormGroup;
  p: number = 1;
  user: any;
  imageName: any;
  showForm = false;
  term: string;
  title = 'Add New';
  isReadonly = false;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  sectorId: any;
  role: any;
  roleId: any;
  expectedActivitiesList:any;
  RoleList: any;
  salaryRange:any;
  
  constructor(private formBuilder: FormBuilder,
    private dataTransferService : DataTransferService,
    private toastr:ToastrService,
    private router:Router,
    private activeRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private ngxSpinnerService: NgxSpinnerService) { 

      const state=this.router.getCurrentNavigation()?.extras.state;
      if(state){
        console.log('Role from state : ',state.role);
        (this.sectorId=state.sectorId);
        (this.title=state.title);
        (this.isReadonly=state.isReadonly);
        (this.role=state.role);
        (this.roleId=state.roleId)
      }

      this.addRoleForm = this.formBuilder.group({
        RO_title: ['', Validators.required], // Title
        RO_dp: [null, Validators.required], // Role Image
        RO_References: this.formBuilder.array([], Validators.required), // Role References
        RO_Skills_and_Knowledge: this.formBuilder.array([], Validators.required), // Skills and knowledge
        RO_Place_of_Work: this.formBuilder.array([], Validators.required), // Place Of Work
        RO_Key_Insights: this.formBuilder.array([], Validators.required), // Key Insight
        RO_Salary: this.formBuilder.array([], Validators.required), // Role Salary
        RO_Salary_from: ['', Validators.required], // Salary From
        RO_Salary_to: ['', Validators.required], // Salary To
        RO_Daily_Tasks: this.formBuilder.array([], Validators.required),// Daily Tasks
        RO_Expected_Activities: this.formBuilder.array([],Validators.required), // Role Expected Activities
        RO_Expected_to_on_the_weekends: ['', Validators.required], // Expected to work on the weekends
        RO_Expected_to_work_beyond_nine_to_five: ['', Validators.required], // Expected to work beyond nine to five
        RO_Expected_to_work_internationally: ['', Validators.required], // Expected to work internationally
        RO_Expected_to_work_in_the_office: ['', Validators.required], // Expected to work in the office
        RO_description: ['', Validators.required], // Role Description
        RO_IndustryId:[''],
        RO_isAccept:[false]
      });
    }
  
    ngOnInit(): void {
      this.getAllExpctedActivities();
      this.getAllRoleBySectorId(this.sectorId);
      this.sectorId=this.sectorId;

      if(this.title=='View'){
        this.addRoleForm.get('RO_Expected_to_on_the_weekends')?.disable();
        this.addRoleForm.get('RO_Expected_to_work_beyond_nine_to_five')?.disable();
        this.addRoleForm.get('RO_Expected_to_work_internationally')?.disable();
        this.addRoleForm.get('RO_Expected_to_work_in_the_office')?.disable();
        this.addRoleForm.get('RO_isAccept')?.disable();
      }
      
      if(!this.sectorId){
        this.router.navigate([`actions/sectors/roles`]);
      };

      if(this.role){
      console.log("Role Data :",this.role);
      this.setRange();
      this.patchArrayValues(this.role.RO_References, 'RO_References');
      this.patchArrayValues(this.role.RO_Expected_Activities, 'RO_Expected_Activities');
      this.patchArrayValues(this.role.RO_Salary, 'RO_Salary');
      this.patchArrayValues(this.role.RO_Daily_Tasks, 'RO_Daily_Tasks');
      this.patchArrayValues(this.role.RO_Skills_and_Knowledge, 'RO_Skills_and_Knowledge');
      this.patchArrayValues(this.role.RO_Place_of_Work, 'RO_Place_of_Work');
      this.patchArrayValues(this.role.RO_Key_Insights, 'RO_Key_Insights');
      this.addRoleForm.patchValue({
        RO_isAccept: this.role.RO_isAccept == 'true' ? true : false,
      });
      this.addRoleForm.patchValue(this.role)
    }

    }

    setRange(){
      if(this.role.RO_Salary_from=="0" && this.role.RO_Salary_to=="25"){
        this.salaryRange="<25k";
      }else if(this.role.RO_Salary_from=="25" && this.role.RO_Salary_to=="30"){
        this.salaryRange="25-30k";
      }else if(this.role.RO_Salary_from=="30" && this.role.RO_Salary_to=="35"){
        this.salaryRange="30-35k";
      }else if(this.role.RO_Salary_from=="35" && this.role.RO_Salary_to=="100"){
        this.salaryRange="35k+";
      }
      }

    patchArrayValues(values: string[], controlName: string) {
      if (values && values.length > 0) {
        const formArray = this.addRoleForm.get(controlName) as FormArray;
        values.forEach(value => {
          formArray.push(this.formBuilder.control(value));
        });
      }
    }

    get f() {
      return this.addRoleForm.controls;
    }
  
    onFileSelected(event: any) {        
      let selectedFile = event.target.files[0];

      if (event.target.files.length === 0) {
        // Reset both imageName and imageSrc when no file is selected
        this.imageName = null;
        this.imageSrc = null;
        return;
      }
  
      const newFileName = FileValidator.addTimestamp(selectedFile.name);
      this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });

      if(this.imageName){
        const formControl=this.addRoleForm.get('RO_dp');
        formControl?.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));
        formControl?.updateValueAndValidity();
      }

      const fileType = this.imageName.type.split('/')[0];
    const fileExtension = this.imageName.name.split('.').pop()?.toLowerCase();
    if (fileType !== 'image' || fileExtension === 'svg') {
      event.target.value = '';
      this.toastr.info('Please select an image file (excluding SVG).');
      this.imageName = null;
      this.imageSrc = null;
      return;
    }
    
    if (this.imageName && fileType== 'image') {
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imageSrc = e.target?.result as string | ArrayBuffer;
      };
      reader.readAsDataURL(this.imageName);
    } else {
      this.imageSrc = null; // Reset imageSrc if no file selected
    }
      console.log('imageName', this.imageName);
    }
    
    uploadLogoUrl(): Promise<any> {
      return new Promise((resolve, reject) => {
        if (!this.imageName) {
          return resolve(null);  
        }
        console.log('image', this.imageName);
        this.dataTransferService.uploadurl(this.imageName).subscribe(
          (res: any) => {
            resolve(res);  
          },
          (error: any) => {
            reject(error);  
          }
        );
      });
    }
    


    
      async addRole() {
        try {
          if (this.title == 'Edit') {
            this.addRoleForm.value.RO_IndustryId = this.sectorId;
            this.addRoleForm.value.RO_id = this.roleId;
      
            if (this.imageName) {
              await this.uploadLogoUrl();
              const fileUrl = this.baseUrl + this.imageName.name;
              this.addRoleForm.value.RO_dp = fileUrl;
            } else {
              this.addRoleForm.value.RO_dp = this.role.RO_dp;
            }
      
            console.log("Role edit data : ", this.addRoleForm.value);
            this.ngxSpinnerService.show('globalSpinner');
      
            const res: any = await this.dataTransferService.updateRoleData(this.addRoleForm.value).toPromise();
            this.ngxSpinnerService.hide('globalSpinner');
      
            if (res.statusCode == 200) {
              this.toastr.success("Role updated successfully.");
              const state = { sectorId: this.sectorId };
              this.router.navigate([`actions/sectors/roles`], { state });
              this.dataTransferService.getAllRoleBySectorId(this.sectorId);
            } else {
              this.toastr.error("Something went wrong.");
              console.error('Unable to update role. Status:', res.status);
            }
      
          } else {
            if (this.addRoleForm.invalid) {
              console.log("this.addRoleForm.value", this.addRoleForm.value);
              this.toastr.info("Please fill all required fields");
              return;
            } else {
              this.ngxSpinnerService.show('globalSpinner');
              await this.uploadLogoUrl();
              const fileUrl = this.baseUrl + this.imageName.name;
              this.addRoleForm.value.RO_dp = fileUrl;
              this.addRoleForm.value.RO_IndustryId = this.sectorId;
      
              console.log("Add role post data : ", this.addRoleForm.value);
      
              const res: any = await this.dataTransferService.insertRoleData(this.addRoleForm.value).toPromise();
              this.ngxSpinnerService.hide('globalSpinner');
      
              if (res.statusCode == 200) {
                this.toastr.success("Role added successfully.");
                const state = { sectorId: this.sectorId };
                this.router.navigate([`actions/sectors/roles`], { state });
                this.dataTransferService.getAllRoleBySectorId(this.sectorId);
              } else {
                this.toastr.error("Something went wrong.");
                console.error('Unable to add role. Status:', res.status);
              }
            }
          }
        } catch (error) {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Error:', error);
          this.toastr.error("Something went wrong.");
        }
      }
      

      addRoleCnlBtn(){
        const state={
          sectorId:this.sectorId
        }
        this.router.navigate([`actions/sectors/roles`],{state});
      }

      addTag(tag: string, formArrayName: string) {
        if (tag.trim() !== '') {
          const formArray = this.addRoleForm.get(formArrayName) as FormArray;
          formArray.insert(0, new FormControl(tag.trim())); // Insert at the beginning
        }
      }
      


    removeTagForField(index: number, formArrayName: string) {
      const formArray = this.addRoleForm.get(formArrayName) as FormArray;
      formArray.removeAt(index);
    }


    onEnterKey(event: any, controlName: string) {
      
      if (event.key === 'Enter' ||event.key === 'Tab') {
        if(event.key === 'Enter'){
          event.preventDefault();
        }
        const inputElement = event.target as HTMLInputElement;
        const tag = inputElement.value.trim();
        if (tag !== '') {
          this.addTag(tag, controlName);
          inputElement.value = ''; // Clear the input field after adding the tag
        }
      }
      // if (event.key === 'Tab') {
      //   const inputElement = event.target as HTMLInputElement;
      //   const tag = inputElement.value.trim();
      //   if (tag !== '') {
      //     this.addTag(tag, controlName);
      //     inputElement.value = ''; // Clear the input field after adding the tag
      //   }
      // }
    }

   onInputBlur(event: any, controlName: string) {
  const inputElement = event.target as HTMLInputElement; // Type assertion
  const trimmedTag = inputElement.value.trim();
  if (trimmedTag !== '') {
    this.addTag(trimmedTag, controlName);
    inputElement.value = ''; 
  }
}

onBackspaceKey(event: KeyboardEvent, formArrayName: string) {
  if (event.key === 'Delete') {
    const inputElement = event.target as HTMLInputElement;
    if (inputElement.value === '' && !event.shiftKey) {
      event.preventDefault();
      this.removeLastTag(formArrayName);
    }
  }
}

// Method to remove the last tag from the specified form array
removeLastTag(formArrayName: string) {
  const formArray = this.addRoleForm.get(formArrayName) as FormArray;
  if (formArray.length > 0) {
    formArray.removeAt(0); // Remove the first tag
  }
}


addExpectedActivity(event: any) {
  const activity = event.target.value;
  if (activity.trim() !== '') {
    const formArray = this.addRoleForm.get('RO_Expected_Activities') as FormArray;
    const isDuplicate = formArray.controls.some(control => control.value === activity);

    if (!isDuplicate) {
      formArray.insert(0,new FormControl(activity.trim()));
    } else {
      console.warn('Activity already selected');
      this.toastr.info('Activity already selected');

    }
    // Reset the value of the select element
    event.target.value = '';
  }
}

getAllRoleBySectorId(sectorId:any){
  console.log("sectorId : ",sectorId);
  this.dataTransferService.getAllRoleBySectorId(sectorId).subscribe({
    next: (res: any) => {
      if (res.statusCode === 200) {
        this.RoleList = res.data;
        console.log("RoleList : ",this.RoleList);
      } else {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Failed to fetch role. Status:', res.status);
      }
    },
    error: (error: any) => {
      console.error('Error occurred while fetching roles:', error);
    },
  });
}

getAllExpctedActivities(){
  this.dataTransferService.getAllExpctedActivities().subscribe({
    next:(res:any)=>{
      if (res.statuscode === 200) {
        console.log("expectedActivitiesList res",res);
        
        this.expectedActivitiesList = res.filteredData[0].quiz_options;
        console.log("expectedActivitiesList : ",this.expectedActivitiesList);
        
      } else {
        console.error('Failed to fetch role. Status:', res.status);
      }
    }
  })
}

updateSalaryRange(range: any) {
  const selectedRange=range.target.value;
  if (selectedRange === '<25k') {
      this.addRoleForm.patchValue({
          RO_Salary_from: "0",
          RO_Salary_to: "25000"
      });
  } else if (selectedRange === '25-30k') {
      this.addRoleForm.patchValue({
          RO_Salary_from: "25000",
          RO_Salary_to: "30000"
      });
  } else if (selectedRange === '30-35k') {
      this.addRoleForm.patchValue({
          RO_Salary_from: "30000",
          RO_Salary_to: "35000"
      });
  } else if (selectedRange === '35k+') {
      this.addRoleForm.patchValue({
          RO_Salary_from: "35000",
          RO_Salary_to: "100000"
      });
  }
}

}

<app-sidebar>
    <div class="content-wrapper">

        <div class="row"> <!--Add new role form -->
            <div class="col-12 grid-margin stretch-card">
                <div class="card">
                    <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">{{title}}
                        Role</div>
                    <div class="card-body">
                        <form [formGroup]="addRoleForm" (ngSubmit)="addRole();" class="forms-sample">
                            <div class="row">
                                <div class="form-group col-lg-4">

                                    <label for="RO_title" class="required-field">Role Title</label>
                                    <input type="text" class="form-control form-control-sm"
                                        formControlName="RO_title" required [readonly]="isReadonly" placeholder="Enter title">
                                    
                                </div>


                                <div *ngIf="!isReadonly" class="form-group col-lg-4">
                                    <label for="RO_dp" class="required-field">Role Image</label>
                                    <div class="logo-input-container">
                                        <input type="file" class="form-control form-control-sm"
                                            formControlName="RO_dp" (change)="onFileSelected($event);" required
                                            [readonly]="isReadonly" accept="image/*">
                                        <img *ngIf="imageSrc" [src]="imageSrc" alt="Role Image" class="img-preview">
                                    </div>
                                    <div *ngIf="this.addRoleForm.get('RO_dp')?.errors?.fileSizeValidator" class="warning">
                                      The file size exceeds the 2 MB limit. Please select a smaller file.
                                  </div> 
                                </div>
                                <div *ngIf="isReadonly" class="form-group col-lg-4">
                                    <label for="RO_dp">Role Image<span *ngIf="!isReadonly" class="text-danger">*</span></label>
                                    <input type="text" class="form-control form-control-sm"
                                        formControlName="RO_dp" required [readonly]="isReadonly">
                                </div>

                                <div class="form-group col-lg-4">
                                    <label for="RO_References" class="required-field">Role Resources</label>
                                    <div class="tag-input-container">
                                        <input *ngIf="!isReadonly" type="text" (keydown)="onEnterKey($event, 'RO_References')" (keydown)="onBackspaceKey($event, 'RO_References')" (blur)="onInputBlur($event, 'RO_References')" class="tag-input form-control form-control-sm" placeholder="Add New +" required [readonly]="isReadonly">
                                      <div class="tag-box">
                                        <div *ngFor="let tag of addRoleForm.get('RO_References')?.value; let i = index"  [ngClass]="{'view-mode': title === 'View'}" class="tag">
                                          {{ tag }}
                                          <span *ngIf="!isReadonly" class="close"  (click)="removeTagForField(i, 'RO_References')">x</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                <div class="form-group col-lg-4">
                                    <label for="RO_Skills_and_Knowledge" class="required-field">Skills And Knowledge</label>
                                    <div class="tag-input-container">
                                        <input *ngIf="!isReadonly" type="text" (keydown)="onEnterKey($event, 'RO_Skills_and_Knowledge')" (keydown)="onBackspaceKey($event, 'RO_Skills_and_Knowledge')" (blur)="onInputBlur($event, 'RO_Skills_and_Knowledge')" class="tag-input form-control form-control-sm" placeholder="Add New +" required [readonly]="isReadonly">
                                      <div class="tag-box">
                                        <div *ngFor="let tag of addRoleForm.get('RO_Skills_and_Knowledge')?.value; let i = index"  [ngClass]="{'view-mode': title === 'View'}" class="tag">
                                          {{ tag }}
                                          <span *ngIf="!isReadonly" class="close"  (click)="removeTagForField(i, 'RO_Skills_and_Knowledge')">x</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div class="form-group col-lg-4">
                                    <label for="RO_Place_of_Work" class="required-field">Place Of Work</label>
                                    <div class="tag-input-container">
                                        <input *ngIf="!isReadonly" type="text" (keydown)="onEnterKey($event, 'RO_Place_of_Work')" (keydown)="onBackspaceKey($event, 'RO_Place_of_Work')" (blur)="onInputBlur($event, 'RO_Place_of_Work')" class="tag-input form-control form-control-sm" placeholder="Add New +" required [readonly]="isReadonly">
                                      <div class="tag-box">
                                        <div *ngFor="let tag of addRoleForm.get('RO_Place_of_Work')?.value; let i = index"  [ngClass]="{'view-mode': title === 'View'}" class="tag">
                                          {{ tag }}
                                          <span *ngIf="!isReadonly" class="close" (click)="removeTagForField(i, 'RO_Place_of_Work')">x</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>


                                  <div class="form-group col-lg-4">
                                    <label for="RO_Key_Insights" class="required-field">Role Key Highlights</label>
                                    <div class="tag-input-container">
                                        <input *ngIf="!isReadonly" type="text" (keydown)="onEnterKey($event, 'RO_Key_Insights')" (keydown)="onBackspaceKey($event, 'RO_Key_Insights')" (blur)="onInputBlur($event, 'RO_Key_Insights')" class="tag-input form-control form-control-sm" placeholder="Add New +" required [readonly]="isReadonly">
                                      <div class="tag-box">
                                        <div *ngFor="let tag of addRoleForm.get('RO_Key_Insights')?.value; let i = index"  [ngClass]="{'view-mode': title === 'View'}" class="tag">
                                          {{ tag }}
                                          <span *ngIf="!isReadonly" class="close" (click)="removeTagForField(i, 'RO_Key_Insights')">x</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                            </div>

                            <div class="row">
                                <div class="form-group col-lg-4">
                                    <label for="RO_Salary" class="required-field">Role Salary</label>
                                    <div class="tag-input-container">
                                        <input *ngIf="!isReadonly" type="text" (keydown)="onEnterKey($event, 'RO_Salary')" (keydown)="onBackspaceKey($event, 'RO_Salary')" (blur)="onInputBlur($event, 'RO_Salary')" class="tag-input form-control form-control-sm" placeholder="Add New +" required [readonly]="isReadonly">
                                      <div class="tag-box">
                                        <div *ngFor="let tag of addRoleForm.get('RO_Salary')?.value; let i = index"  [ngClass]="{'view-mode': title === 'View'}" class="tag">
                                          {{ tag }}
                                          <span *ngIf="!isReadonly" class="close" (click)="removeTagForField(i, 'RO_Salary')">x</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  <div *ngIf="!isReadonly" class="form-group col-lg-4">
                                    <label for="salaryRange" class="required-field">Expected Starting Salary</label>
                                    <select class="form-control form-control-sm" (change)="updateSalaryRange($event)" [disabled]="isReadonly" required>
                                        <option value="" selected disabled>Select a salary range</option>
                                        <option  value="<25k">Less than 25k</option>
                                        <option value="25-30k">25k - 30k</option>
                                        <option value="30-35k">30k - 35k</option>
                                        <option value="35k+">More than 35k</option>
                                    </select>
                                </div>

                                <div *ngIf="isReadonly" class="form-group col-lg-4">
                                    <label for="salaryRange" class="required-field">Salary Range</label>
                                      <input type="text" class="form-control form-control-sm" [value]="salaryRange" required [readonly]="isReadonly">
                                  </div>

                                <!-- <div class="form-group col-lg-4">
                                    <label for="RO_Salary_to" class="required-field">Salary To</label>
                                    <input type="number" class="form-control form-control-sm"
                                        formControlName="RO_Salary_to" required [readonly]="isReadonly" placeholder="Salary to">
                                    <div class="invalid-feedback">
                                        Please enter a Salary To.
                                    </div>
                                </div> -->
                                <div class="form-group col-lg-4">
                                    <label for="RO_Daily_Tasks" class="required-field">Role Daily Tasks</label>
                                    <div class="tag-input-container">
                                        <input *ngIf="!isReadonly" type="text" (keydown)="onEnterKey($event, 'RO_Daily_Tasks')" (keydown)="onBackspaceKey($event, 'RO_Daily_Tasks')" (blur)="onInputBlur($event, 'RO_Daily_Tasks')" class="tag-input form-control form-control-sm" placeholder="Add New +" required [readonly]="isReadonly">
                                      <div class="tag-box">
                                        <div *ngFor="let tag of addRoleForm.get('RO_Daily_Tasks')?.value; let i = index"  [ngClass]="{'view-mode': title === 'View'}" class="tag">
                                          {{ tag }}
                                          <span *ngIf="!isReadonly" class="close" (click)="removeTagForField(i, 'RO_Daily_Tasks')">x</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>


                            </div>

                            <div class="row">
                              
                                  <div class="form-group col-lg-4">
                                    <label for="RO_Expected_Activities" class="required-field">Role Expected Activities</label>
                                    <div class="tag-input-container">
                                        <select *ngIf="!isReadonly" (change)="addExpectedActivity($event)" (keydown)="onBackspaceKey($event, 'RO_Expected_Activities')" class="form-control tag-input form-control-sm" [disabled]="isReadonly" required>
                                            <option selected disabled value="">Add New +</option>
                                            <option *ngFor="let activity of expectedActivitiesList" [value]="activity.QUO_title">{{ activity.QUO_title }}</option>
                                        </select>
                                        <div class="tag-box">
                                            <div *ngFor="let tag of addRoleForm.get('RO_Expected_Activities')?.value; let i = index"  [ngClass]="{'view-mode': title === 'View'}" class="tag">
                                                {{ tag }}
                                                <span *ngIf="!isReadonly" class="close" (click)="removeTagForField(i, 'RO_Expected_Activities')">x</span>
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>
                                

                                <div class="form-group col-lg-4">
                                    <label for="RO_Expected_to_on_the_weekends" class="required-field">Expected To
                                        On The Weekends</label>
                                    <select class="form-control form-control-sm" formControlName="RO_Expected_to_on_the_weekends" [disabled]="isReadonly" required>
                                        <option value="" disabled selected>Expected to on the weekends</option>
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                    </select>
                                   
                                </div>

                                <div class="form-group col-lg-4">
                                    <label for="RO_Expected_to_work_beyond_nine_to_five" class="required-field">Expected To
                                        Work Beyond Nine To Five</label>
                                    <select [disabled]="isReadonly" class="form-control form-control-sm" formControlName="RO_Expected_to_work_beyond_nine_to_five"
                                        required>
                                        <option value="" disabled selected>Expected to work beyond nine to five</option>
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                    </select>
                                    
                                </div>
                            </div>

                            <div class="row">
                               

                                <div class="form-group col-lg-4">
                                    <label for="RO_Expected_to_work_internationally" class="required-field">Expected To
                                        Work Internationally</label>
                                    <select class="form-control form-control-sm" formControlName="RO_Expected_to_work_internationally"
                                        required [disabled]="isReadonly">
                                        <option value="" disabled selected>Expected to work internationally</option>
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                    </select>
                                   
                                </div>

                                <div class="form-group col-lg-4">
                                    <label for="RO_Expected_to_work_in_the_office" class="required-field">Expected to
                                        Work In The Office</label>
                                    <select class="form-control form-control-sm" formControlName="RO_Expected_to_work_in_the_office"
                                        required [disabled]="isReadonly">
                                        <option value="" disabled selected>Expected to work in the office</option>
                                        <option value="Yes">Yes</option>
                                        <option value="No">No</option>
                                    </select>
                                    
                                </div>

                                <div class="form-group col-lg-4">
                                  <label for="RO_isAccept" class="required-field">Role Status</label>
                                  <select class="form-control form-control-sm" formControlName="RO_isAccept" required [disabled]="isReadonly">
                                    <option value="" disabled>Please select a status</option>
                                    <option [value]="true">Approve</option>
                                    <option [value]="false">Not Approve</option>
                                  </select>
                                </div>
                                

                                    <div class="form-group col-lg-4">
                                        <label for="RO_description" class="required-field">Role Description</label>
                                        <textarea class="form-control form-control-sm" formControlName="RO_description"
                                            required [readonly]="isReadonly" rows="5" cols="50" placeholder="Role description "></textarea>
                                        
                                    </div>
                            </div>

                           

                            <div class="text-center">
                                <button *ngIf="!isReadonly" type="submit" class="btn btn-primary mr-2">Save</button>
                                <button class="btn btn-light" (click)="addRoleCnlBtn();">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</app-sidebar>
<app-sidebar>
  <div class="content-wrapper fade-in">

    <div class="row mb-4 head-Home">
      <!-- Add and search bar -->
      <div class="col-lg-3 mb-2 mb-lg-0">
        <button type="submit" (click)="showAddNewUniversity()" class="btn btn-primary w-100">Add New University</button>
      </div>
      <div class="col-lg-6">
        <div class="input-group">
          <input type="text" [(ngModel)]="term" class="form-control shadow-sm rounded-start" (input)="p=1"
            placeholder="Search here" style="width: 100%;" aria-label="Search now">
        </div>
      </div>
    </div>

    <div class="row" *ngIf="showForm">
      <div class="col-lg-12 grid-margin stretch-card">
        <div class="card">
          <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">All Universities</div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-sm table-hover">
                <thead class="text-center">
                  <tr>
                    <th class="text-left">Name</th>
                    <!-- <th>Description</th> -->
                    <!-- <th>Logo</th> -->
                    <!-- <th></th> -->
                    <th class="text-center">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    *ngFor="let university of UniversityData|filter: term |paginate : { itemsPerPage:7, currentPage:p}">
                    <td>{{university.INS_title}}</td>
                    <!-- <td class="description-content">{{university.INS_service}} </td> -->
                     <!-- <td class="text-center">
                      <img [src]="university.INS_dp" class="card-img-top square-img" alt="Sector Image">
                    </td> -->
                    <td class="text-center">
                      <div class="btn-group" role="group" aria-label="Basic example">
                        <button type="button" (click)="showEditUniversity(university)" class="btn btn-primary btn-sm"
                          placement="top" ngbTooltip="Update">
                          <i class="ti-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#myModal"
                                placement="top" ngbTooltip="Delete" (click)="getDeleteId(university.INS_id)">
                                <i class="ti-trash"></i>
                              </button>
                      </div>
                    </td>

                  </tr>
                </tbody>
              </table>
              <pagination-controls (pageChange)="p = $event" class="ml-1 text-center"></pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-sidebar>
 

<div id="myModal" class="modal fade" role="dialog">
  <!--Modal-->
  <div class="modal-dialog">
    <!--Modal Content-->
    <div class="modal-content">
      <!-- Modal Header-->
      <div class="modal-header">
        <h3>Delete warning !</h3>
        <!--Close/Cross Button-->
        <button type="button" class="close" data-dismiss="modal"
          style="color: white;">&times;</button>
      </div>
      <!-- Modal Body-->
      <div class="modal-body text-center">
        <i class="ti-trash" style="color: red;"></i>
        <h3> <strong>Are you sure?</strong></h3>
       <p class="mt-3"> Do you really want to delete this university?</p>
      </div>

      <!-- Modal Footer-->

      <div class="modal-footer text-center">
          <button type="button"
          class="btn btn-danger px-4"
          data-dismiss="modal" (click)="deleteInstituteById()">Delete
      </button>
       <a href="" class="btn btn-primary" data-dismiss="modal">Cancel</a>
      </div>

    </div>

  </div>

</div>
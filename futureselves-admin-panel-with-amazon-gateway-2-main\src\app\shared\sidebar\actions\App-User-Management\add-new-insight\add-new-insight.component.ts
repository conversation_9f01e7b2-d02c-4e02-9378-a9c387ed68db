import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
  FormArray,
} from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
import { formatDate } from '@angular/common';
import { FileValidator } from 'src/app/shared/validators/fileValidator.validator';
@Component({
  selector: 'app-add-new-insight',
  templateUrl: './add-new-insight.component.html',
  styleUrls: ['./add-new-insight.component.scss'],
})
export class AddNewInsightComponent implements OnInit {
  insightForm: FormGroup;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  data: {};
  questionData: any;
  selectedAudioFile: any;
  userId: any;
  insightExists: boolean;
  AN_industryId: any;
  AN_degreeId: any;
  question: any;
  playingQuestionId: number | null = null;
  previousAudio: any;
  previousAudioDuration: any;
  title: any;
  AN_id: any;
  trimedAudio: any;
  IN_name: any;
  RO_title: any;
  U_isSharer: any;
  U_name: any;
  isExpert: boolean;

  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private domSanitizer: DomSanitizer,
    private router: Router,
    private http: HttpClient,
    private ngxSpinnerService: NgxSpinnerService,
    private route: ActivatedRoute
  ) {
    this.insightForm = this.formBuilder.group({
      AN_recordLink: ['', [Validators.required]],
      AN_ispublic: [true],
      AN_isanonimise: [{ value: false, disabled:!this.insightForm?.get('AN_ispublic')?.value }],
      AN_voxpodDuration: [''],
      A_title: this.formBuilder.array([]),
    });

    this.route.queryParams.subscribe((params) => {
      if (params) {
        this.userId = params.U_id;
        const isExpert = params.U_isExpert;
        if(isExpert=='false'){
          this.isExpert=false;
          }
          if(isExpert=='true'){
            this.isExpert=true;
          }
          console.log(typeof this.isExpert,this.isExpert); // Output: string
      }
    });

    const state = this.router.getCurrentNavigation()?.extras.state;
    if (state) {
      console.log('State', state);
      (this.AN_industryId = state.positionData.UI_industryId),
        (this.AN_degreeId = state.positionData.UI_roleId);
      this.IN_name = state.positionData.IN_name;
      this.U_isSharer = state.U_isSharer;
      this.U_name = state.U_name;
      if (this.isExpert) {
        this.RO_title = state.positionData.UI_roleId;
      } else {
        this.RO_title = state.positionData.RO_title;
      }
    } else {
      this.router.navigate(['/actions/app-users/choose-position'], {
        queryParams: { U_id: this.userId, U_isExpert: this.isExpert },
      });
    }
  }

  ngOnInit(): void {
    this.getQuestionsAccordingToUserType();
    if (this.title !== 'edit') {
      this.insightForm.patchValue({
        AN_ispublic: true,
      });
    }
    
    this.insightForm.get('AN_ispublic')?.valueChanges.subscribe((value) => {
      const anonControl = this.insightForm.get('AN_isanonimise');
      if (value) {
        anonControl?.disable();
        anonControl?.setValue(false);  // Reset the anonymous option when public is turned on
      } else {
        anonControl?.enable();
      }
    });
  
  }

  getQuestionsAccordingToUserType() {
    if (this.isExpert) {
      this.getExpertQuestionsBySectorId(
        this.AN_industryId,
        this.userId,
        this.AN_degreeId
      );
    } else {
      this.getAllQuestionsForPosition(
        this.AN_industryId,
        this.userId,
        this.AN_degreeId
      );
    }
  }

  showModal(questions: any, title: any) {
    const modal = document.getElementById('staticBackdrop');
    if (modal != null) {
      modal.style.display = 'block';
      this.question = questions;

      if (title === 'edit') {
        this.title = title;
        this.AN_id = questions.AN_id;
        this.insightForm.patchValue({
          AN_ispublic: questions.AN_ispublic,
          AN_isanonimise: questions.AN_isanonimise,
        });
        this.trimedAudio = questions.AN_recordLink.substring(
          questions.AN_recordLink.lastIndexOf('/') + 1
        );
        this.previousAudio = questions.AN_recordLink;
        this.previousAudioDuration = questions.AN_voxpodDuration;
        console.log('Duration:', questions.AN_voxpodDuration);
      }
      
      else {
        this.insightForm.reset();
        this.insightForm.reset({
          AN_ispublic: 'true',
          AN_recordLink:''
        });
        this.title = 'add';
        this.insightForm.get('AN_recordLink')?.setValidators([Validators.required]);
        this.insightForm.get('AN_recordLink')?.updateValueAndValidity();
        }
    }
  }

  hideModal() {
    const modal = document.getElementById('staticBackdrop');
    if (modal != null) {
      modal.style.display = 'none';
      this.insightForm.reset();
      this.removeAllTags('A_title');
      this.trimedAudio = '';
      this.previousAudio = '';
      this.previousAudioDuration = '';
    }
  }

  goToChoosePosition() {
    this.router.navigate(['/actions/app-users/choose-position'], {
      queryParams: { U_id: this.userId, U_isExpert: this.isExpert },
    });
  }

  playAudio(question: any) {
    const audio = document.getElementById(
      `audioPlayer-${question.QU_id}`
    ) as HTMLAudioElement;
    if (audio) {
      if (this.playingQuestionId && this.playingQuestionId !== question.QU_id) {
        this.pauseAudioById(this.playingQuestionId);
      }
      audio.play();
      this.playingQuestionId = question.QU_id;

      // Set event listener to update playingQuestionId when audio ends
      audio.onended = () => {
        this.playingQuestionId = null;
      };
    }
  }

  pauseAudio(question: any) {
    this.pauseAudioById(question.QU_id);
  }

  pauseAudioById(questionId: number) {
    const audio = document.getElementById(
      `audioPlayer-${questionId}`
    ) as HTMLAudioElement;
    if (audio) {
      audio.pause();
      if (this.playingQuestionId === questionId) {
        this.playingQuestionId = null;
      }
    }
  }

  isPlaying(question: any): boolean {
    return this.playingQuestionId === question.QU_id;
  }

  onSubmit(): void {
    this.ngxSpinnerService.show('globalSpinner');

    if (this.title === 'edit') {
      if (this.insightForm.value.AN_voxpodDuration) {
        this.uploadAudioFile().then((fileUrl) => {
            this.insightForm.value.AN_recordLink = fileUrl;
            const data = {
              AN_id: this.AN_id,
              AN_recordLink: fileUrl,
              AN_voxpodDuration: this.insightForm.value.AN_voxpodDuration,
              AN_ispublic: this.insightForm.get('AN_ispublic')?.value || false,
              AN_isanonimise:this.insightForm.get('AN_isanonimise')?.value || false,
              AN_isExpert:this.isExpert?true:false
            };
            console.log('Edit Data:', data);
            this.updateInsight(data);
          })
          .catch((error) => {
            this.ngxSpinnerService.hide('globalSpinner');
            console.error('Audio upload failed', error);
          });
      } else {
        const data = {
          AN_id: this.AN_id,
          AN_recordLink: this.previousAudio,
          AN_voxpodDuration: this.previousAudioDuration,
          AN_ispublic: this.insightForm.get('AN_ispublic')?.value || false,
          AN_isanonimise:this.insightForm.get('AN_isanonimise')?.value || false,
          AN_isExpert:this.isExpert?true:false
        };
        console.log('Edit Data:', data);
        this.updateInsight(data);
      }
    } 

    
    else {
      console.log('Values : ', this.insightForm.value);
    
      if (this.insightForm.valid) {
        this.uploadAudioFile()
          .then((fileUrl) => {
            this.insightForm.value.AN_recordLink = fileUrl;
            const data = {
              AN_userId: this.userId,
              AN_degreeId: this.AN_degreeId,
              AN_industryId: this.AN_industryId,
              AN_experience: '',
              AN_description: '',
              AN_isPublished: '1',
              AN_totalLikeCount: 0,
              AN_keyIdeas: '',
              AN_dp: '',
              AN_length: '',
              AN_questionId: this.question.QU_id,
              AN_recordLink: fileUrl,
              AN_scheduleTime: '',
              AN_status: '1',
              AN_title: this.question.QU_title,
              AN_voxpodDuration: this.insightForm.value.AN_voxpodDuration,
              AN_transcription: '',
              AN_updatedAt: '',
              AN_updatedBy: '',
              AN_ispublic: this.insightForm.get('AN_ispublic')?.value?true:false,
              AN_isanonimise: this.insightForm.value.AN_isanonimise || false,
              AN_isExpert:this.isExpert?true:false,
              sound_bites_links: [''],
              sound_bites_tags: [''],
              reference: [''],
              tags: [
                {
                  A_id: '',
                  A_tagId: '',
                  A_title: this.insightForm.value.A_title,
                },
              ],
              AN_isTranscribed:false
            };
            console.log('Data:', data);
            this.uploadNewInsight(data);
          })
          .catch((error) => {
            this.ngxSpinnerService.hide('globalSpinner');
            console.error('Audio upload failed', error);
          });
      } else {
        this.ngxSpinnerService.hide('globalSpinner');
        this.toastr.info('Please Fill All Required Fields');
      }
    }
  }

  onAudioSelected(event: any): void {
    const audiofile = event.target.files[0];
    // this.selectedAudioFile = audiofile;

    const audiofileType = audiofile.type.split('/')[0];

    if (audiofileType !== 'audio') {
      event.target.value = '';
      this.toastr.info('Please select an audio file.');
      return;
    }

    const originalFileName = audiofile.name;

  // Get the current timestamp
  const timestamp = new Date().toISOString().replace(/[-:.]/g, ''); // Format: YYYYMMDDTHHMMSS

  // Get the file extension
  const fileExtension = originalFileName.split('.').pop();

  // Construct the new file name
  const newFileName = `${originalFileName.replace(`.${fileExtension}`, '')}_${timestamp}_${this.userId}.${fileExtension}`;

  // Create a new File object with the new name
  const newAudioFile = new File([audiofile], newFileName, { type: audiofile.type });

  // Set the selected audio file to the newly created file
  this.selectedAudioFile = newAudioFile;

  console.log('Original Audio file:', audiofile);
  console.log('New Audio file:', newAudioFile);

    if (audiofile) {
      const fileControl = this.insightForm.get('AN_recordLink');
      fileControl?.setValidators(
        FileValidator.fileSizeValidator(10240, audiofile)
      );
      fileControl?.updateValueAndValidity();
    }

    const audio = new Audio(URL.createObjectURL(audiofile));
    audio.addEventListener('loadedmetadata', () => {
      const durationInSeconds = Math.floor(audio.duration); // Round down to the nearest second
      const minutes = Math.floor(durationInSeconds / 60);
      const seconds = durationInSeconds % 60;

      // Format duration as "minutes:seconds" with leading zero if seconds < 10
      const duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      console.log('Audio duration:', duration);
      this.insightForm.patchValue({ AN_voxpodDuration: duration });
    });
  }

  uploadAudioFile(): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.selectedAudioFile) {
        this.toastr.error('Please select an audio file.');
        reject('No audio file selected');
        return;
      }
      console.log('audio', this.selectedAudioFile);
      this.dataTransferService.uploadurl(this.selectedAudioFile).subscribe(
        (res: any) => {
          console.log('Upload successful', this.selectedAudioFile.name);
          resolve(this.baseUrl + this.selectedAudioFile.name);
        },
        (error: any) => {
          console.error('Upload error', error);
          this.toastr.error('Failed to upload audio file');
          reject(error);
        }
      );
    });
  }

  uploadNewInsight(data: any) {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.uploadNewInsight(data).subscribe(
      (res: any) => {
        if (res.StatusCode == 200) {
          this.ngxSpinnerService.hide('globalSpinner');
          this.getQuestionsAccordingToUserType();
          this.toastr.success('Your Answer Uploaded Successfully.');
          this.ngxSpinnerService.hide('globalSpinner');
          this.hideModal();
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          this.toastr.error('Failed to upload insight');
          console.error('Failed to upload insight:', res);
        }
      },
      (error: any) => {
        this.toastr.error('Failed to upload insight');
        console.error('Failed to upload insight', error);
        this.ngxSpinnerService.hide('globalSpinner');
      }
    );
  }

  updateInsight(data: any) {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.updateInsight(data).subscribe(
      (res: any) => {
        if (res.statusCode == 200) {
          this.ngxSpinnerService.hide('globalSpinner');
          this.getQuestionsAccordingToUserType();
          this.hideModal();
          this.toastr.success('Answer Updated Successfully.');
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Failed to update insight');
          this.toastr.error('Failed to update insight');
        }
      },
      (error: any) => {
        this.toastr.error('Failed to update insight');
        console.error('Failed to update insight', error);
        this.ngxSpinnerService.hide('globalSpinner');
      }
    );
  }

  getAllQuestionsForPosition(
    AN_industryId: any,
    userId: any,
    AN_degreeId: any
  ): void {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService
      .getAllQuestionsForPosition(AN_industryId, userId, AN_degreeId)
      .subscribe({
        next: (res: any) => {
          this.ngxSpinnerService.hide('globalSpinner');

          if (res.statusCode == 200) {
            this.questionData = res.data;
            console.log('Question Data:', this.questionData);
          } else {
            this.toastr.error('Unable to fetch data');
          }
        },
        error: (error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Error fetching data:', error);
          this.toastr.error('Unable to fetch data');
        },
      });
  }

  getExpertQuestionsBySectorId(
    AN_industryId: any,
    userId: any,
    AN_degreeId: any
  ): void {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService
      .getExpertQuestionsBySectorId(AN_industryId, userId, AN_degreeId)
      .subscribe({
        next: (res: any) => {
          this.ngxSpinnerService.hide('globalSpinner');

          if (res.statusCode == 200) {
            this.questionData = res.data;
            console.log('Question Data:', this.questionData);
          } else {
            this.toastr.error('Unable to fetch data');
          }
        },
        error: (error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Error fetching data:', error);
          this.toastr.error('Unable to fetch data');
        },
      });
  }

  addTag(tag: string, formArrayName: string) {
    //Tag Box
    if (tag.trim() !== '') {
      const formArray = this.insightForm.get(formArrayName) as FormArray;
      formArray.push(new FormControl(tag.trim()));
    }
  }
  // Method to remove a tag from the specified form array
  removeTagForField(index: number, formArrayName: string) {
    const formArray = this.insightForm.get(formArrayName) as FormArray;
    formArray.removeAt(index);
  }
  // Method to handle Enter key event for adding a tag
  onEnterKey(event: any, controlName: string) {
    if (event.key === 'Enter') {
      event.preventDefault();
      const inputElement = event.target as HTMLInputElement;
      const tag = inputElement.value.trim();
      if (tag !== '') {
        this.addTag(tag, controlName);
        inputElement.value = ''; // Clear the input field after adding the tag
      }
    }
    if (event.key === 'Tab') {
      const inputElement = event.target as HTMLInputElement;
      const tag = inputElement.value.trim();
      if (tag !== '') {
        this.addTag(tag, controlName);
        inputElement.value = ''; // Clear the input field after adding the tag
      }
    }
  }

  onInputBlur(event: any, controlName: string) {
    const inputElement = event.target as HTMLInputElement; // Type assertion
    const trimmedTag = inputElement.value.trim();
    if (trimmedTag !== '') {
      this.addTag(trimmedTag, controlName);
      inputElement.value = ''; // Clear the input field afngter adding the tag
    }
  }

  onBackspaceKey(event: KeyboardEvent, formArrayName: string) {
    if (event.key === 'Backspace') {
      const inputElement = event.target as HTMLInputElement;
      if (inputElement.value === '' && !event.shiftKey) {
        event.preventDefault();
        this.removeLastTag(formArrayName);
      }
    }
  }

  // Method to remove the last tag from the specified form array
  removeLastTag(formArrayName: string) {
    const formArray = this.insightForm.get(formArrayName) as FormArray;
    if (formArray.length > 0) {
      formArray.removeAt(formArray.length - 1);
    }
  }

  removeAllTags(formArrayName: string) {
    const formArray = this.insightForm.get(formArrayName) as FormArray;
    while (formArray.length !== 0) {
      formArray.removeAt(0);
    }
  }
}

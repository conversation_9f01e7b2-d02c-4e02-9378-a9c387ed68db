{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { asyncScheduler } from '../scheduler/async';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function bufferTime(bufferTimeSpan, ...otherArgs) {\n  var _a, _b;\n\n  const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  const bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  const maxBufferSize = otherArgs[1] || Infinity;\n  return operate((source, subscriber) => {\n    let bufferRecords = [];\n    let restartOnEmit = false;\n\n    const emit = record => {\n      const {\n        buffer,\n        subs\n      } = record;\n      subs.unsubscribe();\n      arrRemove(bufferRecords, record);\n      subscriber.next(buffer);\n      restartOnEmit && startBuffer();\n    };\n\n    const startBuffer = () => {\n      if (bufferRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const buffer = [];\n        const record = {\n          buffer,\n          subs\n        };\n        bufferRecords.push(record);\n        executeSchedule(subs, scheduler, () => emit(record), bufferTimeSpan);\n      }\n    };\n\n    if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n    } else {\n      restartOnEmit = true;\n    }\n\n    startBuffer();\n    const bufferTimeSubscriber = new OperatorSubscriber(subscriber, value => {\n      const recordsCopy = bufferRecords.slice();\n\n      for (const record of recordsCopy) {\n        const {\n          buffer\n        } = record;\n        buffer.push(value);\n        maxBufferSize <= buffer.length && emit(record);\n      }\n    }, () => {\n      while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n        subscriber.next(bufferRecords.shift().buffer);\n      }\n\n      bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n      subscriber.complete();\n      subscriber.unsubscribe();\n    }, undefined, () => bufferRecords = null);\n    source.subscribe(bufferTimeSubscriber);\n  });\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/operators/bufferTime.js"], "names": ["Subscription", "operate", "OperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "asyncScheduler", "popScheduler", "executeSchedule", "bufferTime", "bufferTimeSpan", "otherArgs", "_a", "_b", "scheduler", "bufferCreationInterval", "maxBufferSize", "Infinity", "source", "subscriber", "bufferRecords", "restartOnEmit", "emit", "record", "buffer", "subs", "unsubscribe", "next", "startBuffer", "add", "push", "bufferTimeSubscriber", "value", "recordsCopy", "slice", "length", "shift", "complete", "undefined", "subscribe"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,kBAAT,QAAmC,sBAAnC;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,SAASC,cAAT,QAA+B,oBAA/B;AACA,SAASC,YAAT,QAA6B,cAA7B;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,SAASC,UAAT,CAAoBC,cAApB,EAAoC,GAAGC,SAAvC,EAAkD;AACrD,MAAIC,EAAJ,EAAQC,EAAR;;AACA,QAAMC,SAAS,GAAG,CAACF,EAAE,GAAGL,YAAY,CAACI,SAAD,CAAlB,MAAmC,IAAnC,IAA2CC,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgEN,cAAlF;AACA,QAAMS,sBAAsB,GAAG,CAACF,EAAE,GAAGF,SAAS,CAAC,CAAD,CAAf,MAAwB,IAAxB,IAAgCE,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqD,IAApF;AACA,QAAMG,aAAa,GAAGL,SAAS,CAAC,CAAD,CAAT,IAAgBM,QAAtC;AACA,SAAOd,OAAO,CAAC,CAACe,MAAD,EAASC,UAAT,KAAwB;AACnC,QAAIC,aAAa,GAAG,EAApB;AACA,QAAIC,aAAa,GAAG,KAApB;;AACA,UAAMC,IAAI,GAAIC,MAAD,IAAY;AACrB,YAAM;AAAEC,QAAAA,MAAF;AAAUC,QAAAA;AAAV,UAAmBF,MAAzB;AACAE,MAAAA,IAAI,CAACC,WAAL;AACArB,MAAAA,SAAS,CAACe,aAAD,EAAgBG,MAAhB,CAAT;AACAJ,MAAAA,UAAU,CAACQ,IAAX,CAAgBH,MAAhB;AACAH,MAAAA,aAAa,IAAIO,WAAW,EAA5B;AACH,KAND;;AAOA,UAAMA,WAAW,GAAG,MAAM;AACtB,UAAIR,aAAJ,EAAmB;AACf,cAAMK,IAAI,GAAG,IAAIvB,YAAJ,EAAb;AACAiB,QAAAA,UAAU,CAACU,GAAX,CAAeJ,IAAf;AACA,cAAMD,MAAM,GAAG,EAAf;AACA,cAAMD,MAAM,GAAG;AACXC,UAAAA,MADW;AAEXC,UAAAA;AAFW,SAAf;AAIAL,QAAAA,aAAa,CAACU,IAAd,CAAmBP,MAAnB;AACAf,QAAAA,eAAe,CAACiB,IAAD,EAAOX,SAAP,EAAkB,MAAMQ,IAAI,CAACC,MAAD,CAA5B,EAAsCb,cAAtC,CAAf;AACH;AACJ,KAZD;;AAaA,QAAIK,sBAAsB,KAAK,IAA3B,IAAmCA,sBAAsB,IAAI,CAAjE,EAAoE;AAChEP,MAAAA,eAAe,CAACW,UAAD,EAAaL,SAAb,EAAwBc,WAAxB,EAAqCb,sBAArC,EAA6D,IAA7D,CAAf;AACH,KAFD,MAGK;AACDM,MAAAA,aAAa,GAAG,IAAhB;AACH;;AACDO,IAAAA,WAAW;AACX,UAAMG,oBAAoB,GAAG,IAAI3B,kBAAJ,CAAuBe,UAAvB,EAAoCa,KAAD,IAAW;AACvE,YAAMC,WAAW,GAAGb,aAAa,CAACc,KAAd,EAApB;;AACA,WAAK,MAAMX,MAAX,IAAqBU,WAArB,EAAkC;AAC9B,cAAM;AAAET,UAAAA;AAAF,YAAaD,MAAnB;AACAC,QAAAA,MAAM,CAACM,IAAP,CAAYE,KAAZ;AACAhB,QAAAA,aAAa,IAAIQ,MAAM,CAACW,MAAxB,IAAkCb,IAAI,CAACC,MAAD,CAAtC;AACH;AACJ,KAP4B,EAO1B,MAAM;AACL,aAAOH,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACe,MAAnF,EAA2F;AACvFhB,QAAAA,UAAU,CAACQ,IAAX,CAAgBP,aAAa,CAACgB,KAAd,GAAsBZ,MAAtC;AACH;;AACDO,MAAAA,oBAAoB,KAAK,IAAzB,IAAiCA,oBAAoB,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,oBAAoB,CAACL,WAArB,EAA5E;AACAP,MAAAA,UAAU,CAACkB,QAAX;AACAlB,MAAAA,UAAU,CAACO,WAAX;AACH,KAd4B,EAc1BY,SAd0B,EAcf,MAAOlB,aAAa,GAAG,IAdR,CAA7B;AAeAF,IAAAA,MAAM,CAACqB,SAAP,CAAiBR,oBAAjB;AACH,GA9Ca,CAAd;AA+CH", "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { asyncScheduler } from '../scheduler/async';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function bufferTime(bufferTimeSpan, ...otherArgs) {\n    var _a, _b;\n    const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n    const bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    const maxBufferSize = otherArgs[1] || Infinity;\n    return operate((source, subscriber) => {\n        let bufferRecords = [];\n        let restartOnEmit = false;\n        const emit = (record) => {\n            const { buffer, subs } = record;\n            subs.unsubscribe();\n            arrRemove(bufferRecords, record);\n            subscriber.next(buffer);\n            restartOnEmit && startBuffer();\n        };\n        const startBuffer = () => {\n            if (bufferRecords) {\n                const subs = new Subscription();\n                subscriber.add(subs);\n                const buffer = [];\n                const record = {\n                    buffer,\n                    subs,\n                };\n                bufferRecords.push(record);\n                executeSchedule(subs, scheduler, () => emit(record), bufferTimeSpan);\n            }\n        };\n        if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n            executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n        }\n        else {\n            restartOnEmit = true;\n        }\n        startBuffer();\n        const bufferTimeSubscriber = new OperatorSubscriber(subscriber, (value) => {\n            const recordsCopy = bufferRecords.slice();\n            for (const record of recordsCopy) {\n                const { buffer } = record;\n                buffer.push(value);\n                maxBufferSize <= buffer.length && emit(record);\n            }\n        }, () => {\n            while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n                subscriber.next(bufferRecords.shift().buffer);\n            }\n            bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n            subscriber.complete();\n            subscriber.unsubscribe();\n        }, undefined, () => (bufferRecords = null));\n        source.subscribe(bufferTimeSubscriber);\n    });\n}\n"]}, "metadata": {}, "sourceType": "module"}
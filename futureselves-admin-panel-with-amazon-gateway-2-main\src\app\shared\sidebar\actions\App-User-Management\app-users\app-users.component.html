<app-sidebar>
    <div class="content-wrapper fade-in">
        <div class="row mb-2">
            <div *ngIf="userTypeId!=='4'" class="col-lg-2">
                <button (click)="showAddUserForm()" type="submit" class="btn btn-primary w-100">Add New Sharer</button>
            </div>
            <div class="col-lg-6">
                <div class="input-group mb-3">
                    <input type="text" [(ngModel)]="term" (input)="search(term)"
                        class="form-control shadow-sm rounded-start" placeholder="Search here" aria-label="Search now">
                </div>
            </div>
            <div class="col-lg-4">
                <div class="input-group mb-3">
                    <label for="selectStatus" class="input-group-text" style="padding-top: 10px;">Status:</label>
                    <select class="custom-select" id="selectStatus" (change)="selected($event)"
                        style="height: 46px !important;">
                        <option value="All" class="badge" selected>All Status</option>
                        <option value="1" class="badge">Active</option>
                        <option value="0" class="badge">Deactivated</option>
                    </select>
                </div>
            </div>
        </div>


        <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card">
                    <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">App Users
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            Name
                                        </th>

                                        <th class="text-center">
                                            Email
                                        </th>

                                        <th class="text-center">
                                            Created On
                                        </th>

                                        <th class="text-center">
                                            User Type
                                        </th>

                                        <th class="text-center">
                                            Registration Source
                                        </th>

                                        <th class="text-center">
                                            Status
                                        </th>

                                        <th class="text-center">
                                            Action Buttons
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        *ngFor="let user of userList| filter: term|paginate: {itemsPerPage:7, currentPage:p}">
                                        <td>{{user?.U_name | camelCase}}</td>
                                        <td class="text-center">{{user.U_email}}</td>
                                        <td class="text-center">{{ user?.U_createdAt | date: 'MMM d, y' }}</td>
                                        <td class="text-center">{{user.U_isExpert ? 'Expert Sharer' : (user.U_isSharer=="1" ? "Sharer":"Seeker")}}</td>
                                        <td class="text-center">
                                            <img class="img-size" ngbTooltip="Email" *ngIf="user.U_registertypeId == 1"
                                                src="./assets/logos/Email-logo.jpeg" alt="Email">
                                            <img class="img-size" ngbTooltip="Google" *ngIf="user.U_registertypeId == 2"
                                                src="./assets/logos/Google-logo.jpeg" alt="Google">
                                            <img class="img-size" ngbTooltip="Facebook"
                                                *ngIf="user.U_registertypeId == 3"
                                                src="./assets/logos/Facebook-logo.png" alt="Facebook">
                                            <img class="img-size" ngbTooltip="Apple" *ngIf="user.U_registertypeId == 4"
                                                src="./assets/logos/Apple-logo.jpg" alt="Apple">
                                            <img class="img-size" ngbTooltip="LinkedIn"
                                                *ngIf="user.U_registertypeId == 5"
                                                src="./assets/logos/Linkedin-logo.png" alt="LinkedIn">
                                        </td>
                                        <td class="text-center">
                                            <span *ngIf="user?.U_activeStatus == 1"
                                                class="badge badge-success">Active</span>
                                            <span *ngIf="user?.U_activeStatus == 0"
                                                class="badge badge-danger">Deactivated</span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group" aria-label="Basic example">

                                                <button  *ngIf="userTypeId!=='4'" type="button" (click)="showStatusForm(user)"
                                                    class="btn btn-primary btn-sm" placement="top" ngbTooltip="Status">
                                                    <i class="fa-solid fa-id-card-clip"></i>
                                                </button>

                                                <!-- <button type="button" (click)="showPreferncesForm(user.U_id)" class="btn btn-primary btn-sm"
                                                 placement="top" ngbTooltip="Preferences">
                                                 <i class="fa-solid fa-street-view"></i>
                                                 </button> -->

                                                <button  *ngIf="userTypeId!=='4'" type="button" (click)="showEditUserForm(user)"
                                                    class="btn btn-primary btn-sm" placement="top" ngbTooltip="Update">
                                                    <i class="ti-pencil text-white"></i>
                                                </button>

                                                <button type="button" (click)="showAddNewInsightForm(user)"
                                                    class="btn btn-primary btn-sm" placement="top"
                                                    ngbTooltip="Insights">
                                                    <i class="fa-solid fa-music"></i>
                                                </button>

                                                <!-- <button type="button" class="btn btn-primary btn-sm" placement="top" ngbTooltip="Delete" data-toggle="modal" data-target="#my-modal">
                                                    <i class="ti-trash"></i>
                                                </button> -->

                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <pagination-controls (pageChange)="p = $event"
                                class="ml-1 text-center"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div id="my-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content border-0">
                    <div class="modal-body p-0">
                        <div class="card border-0 p-sm-3 p-2 justify-content-center">
                            <div class="card-header float-end pb-0 bg-white border-0 ">
                                <div class="row">
                                    <div class="col ml-auto"><button type="button" class="close" data-dismiss="modal"
                                            aria-label="Close"> <span aria-hidden="true">&times;</span> </button></div>
                                </div>
                                <h3 class="text-center">Confirm Delete</h3>
                                <br>
                                <p class="font-weight-bold mb-2 text-center"><b> Are you sure to delete this ?</b></p>
                            </div>
                            <br>
                            <div class="card-body px-sm-4 mb-2 pt-1 pb-0">
                                <div class="row justify-content-center no-gutters">
                                    <div class="col-auto"><button type="button" class="btn btn-primary mx-3 text-white"
                                            data-dismiss="modal">Cancel</button></div>
                                    <div class="col-auto"><button type="button" class="btn btn-danger px-4"
                                            data-dismiss="modal">Delete</button></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
</app-sidebar>
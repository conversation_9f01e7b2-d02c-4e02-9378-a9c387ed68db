import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  p: number = 1;
  term: string;
  showForm = true;
  isReadonly = false;
  title = 'View';
  sectorId: any;
  RoleList: any;
  roleId: any;
  tempRoleList: any;
  filtervalue: any;
  
  constructor(private formBuilder: FormBuilder,
    private dataTransferService : DataTransferService,
    private toastr:ToastrService,
    private router:Router,
    private activeRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private ngxSpinnerService: NgxSpinnerService) {     
     
      const state=this.router.getCurrentNavigation()?.extras.state;
      console.log("State : ",state);

      if(state){
      (this.sectorId=state.sectorId)      
      }
     }

  ngOnInit(): void {
    this.sectorId=this.sectorId;
    this.sectorId ? this.getAllRoleBySectorId(this.sectorId) : this.router.navigate([`actions/sectors`]);
  }

  showAddNewRoles(){
    const state={
      title:'Add New',
      sectorId:this.sectorId,
      isReadonly:false,
      role:''
    }
    this.router.navigate([`actions/sectors/roles/add-edit-role`],{state});
  }

  showEditRole(formRole:any){
    const state={
      title:'Edit',
      sectorId:this.sectorId,
      isReadonly:false,
      role:formRole,
      roleId: formRole.RO_id

    }
    this.router.navigate([`actions/sectors/roles/add-edit-role`],{state});
  }

  viewRole(formRole:any){
    const state={
      title:'View',
      sectorId:this.sectorId,
      isReadonly:true,
      role:formRole
    }
    this.router.navigate([`actions/sectors/roles/add-edit-role`],{state});
  }

  showDeleteModal(RO_id:any){
    this.roleId=RO_id;
  }

  deleteRole(){
      this.ngxSpinnerService.show('globalSpinner');
      this.dataTransferService.deleteRoleData(this.roleId).subscribe((res:any)=>{
      if(res.statusCode==200){
        this.ngxSpinnerService.hide('globalSpinner');
        console.log('Role deleted successfully');
        this.toastr.success('Role deleted successfully');
        this.getAllRoleBySectorId(this.sectorId);
      }else{
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error deleting role Status:', res.status);
        this.toastr.error('Error deleting role'); 
      }
      }); 
  }

 

  getAllRoleBySectorId(sectorId:any){
    console.log("sectorId : ",sectorId);
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllRoleBySectorId(sectorId).subscribe({
      next: (res: any) => {
        if (res.statusCode === 200) {
          this.RoleList = res.data;
          this.tempRoleList = [...this.RoleList];
          console.log("RoleList : ",this.RoleList);
          this.ngxSpinnerService.hide('globalSpinner');
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Failed to fetch role. Status:', res.status);
        }
      },
      error: (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error occurred while fetching roles:', error);
      },
    });
  }

  selected(event: any) {
    this.p=1;
    this.filtervalue = event.target.value;
    console.log('filtervalue', this.filtervalue);
    
    this.RoleList = [...this.tempRoleList];
  
    if (this.filtervalue === 'user') {
      this.RoleList = this.RoleList.filter((obj: any) => obj.RO_createdByType === 'user');
    } 
    else if (this.filtervalue === '') {
      this.RoleList = this.RoleList.filter((obj: any) => obj.RO_createdByType !== 'user');
    }
    else if (this.filtervalue === 'All') {
      this.RoleList = [...this.tempRoleList];
    }
  
    console.log("Filtered RoleList: ", this.RoleList);
  }
  
    
}

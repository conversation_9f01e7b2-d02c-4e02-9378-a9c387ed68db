.modal-dialog{
    height: 50%;
    width: 50%;
    margin:auto;
}


.modal-header{
    color: white;
    background-color: #007bff;
}



textarea{ border: none; 
    box-shadow: none !important;
    -webkit-appearance:none;
    outline:0px !important;
    border: 1px solid black;
}

.openmodal{
    margin-left: 40%;
    width: 25%;
    margin-top: 5%;
}

.icon1{
   color: #007bff;

}

a{
    margin: auto;
}
.form-check{
    padding-left: 5%;
}

// #left{
// position: absolute;
// left: 50px;
// }
.mar-top{
    margin-top:150px;
}
.Gnrt-Transcription-btn{
    margin-top: 8px;
    margin-left: 7px;
}

.modal {
    background-color: rgba(0, 0, 0, 0.5)
 }
 .invalid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: .875em;
    color: #dc3545;
  }
  
  .is-invalid ~ .invalid-feedback {
    display: block;
  }
  
  .d-block {
    display: block;
  }
  
// #right{
//     position: absolute;
//     right: 100px;
// }
// input{
//     color: #007bff;

// }

//Css of Certificate

.badge-certificate{
    background-color: #FFF3EB !important;
    color: #FF6700;
  }
  
  .p-block{
    display: block;
  }
  
  .progress-container{
     display: flex;
     justify-content: center;
     flex-direction: column;
  }
  
  .brandlogo{
    margin-left: 5px;
    color: #FF6700;
    // font-weight: bold;
    font-size: 18px;
  }

  #certificate {
    width: 327px;
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: #ffffff;
  }
  
.transcription{
  position: relative;
}
.saveBtn{
  position: absolute;
  right: 18px;
  cursor: pointer;
}
  
@media screen and (min-width: 992px) and (max-width: 1024px) {
  audio{
    width: 215px !important;
  }
}

@media screen and (min-width: 319px) and (max-width: 386px) {
  .cncl-btn{
    margin-top: 15px;
  }
}

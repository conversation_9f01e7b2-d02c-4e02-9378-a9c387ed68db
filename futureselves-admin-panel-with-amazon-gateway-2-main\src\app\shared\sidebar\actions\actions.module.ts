import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common'; // Import DatePipe from CommonModule
import { ActionsRoutingModule } from './actions-routing.module';
import { ActionsComponent } from './actions.component';
import { SidebarModule } from '../sidebar.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { Ng2SearchPipeModule } from 'ng2-search-filter';
import { InsightComponent } from './Insights/insight/insight.component';
import { DegreeComponent } from './degree/degree.component';
import { CollectionsComponent } from './Collection/collections/collections.component';
import { EthnicityComponent } from './ethnicity/ethnicity.component';
import { SexualOrientationComponent } from './sexual-orientation/sexual-orientation.component';
import { RegionalAccentComponent } from './regional-accent/regional-accent.component';
import { IndustryComponent } from './industry/industry.component';
import { EmployerOpportunitiesComponent } from './Employer-Opportunities/Employer/employer-opportunities/employer-opportunities.component';
import { RouterModule } from '@angular/router';
import { OpportunitiesComponent } from './Employer-Opportunities/Opportunity/opportunities/opportunities.component';
import { UniversityListComponent } from './university-list/university-list/university-list.component';
import { AddEditUniversityListComponent } from './university-list/add-edit-university-list/add-edit-university-list.component';
import { AddEditInsightComponent } from './Insights/add-edit-insight/add-edit-insight.component';
import { RolesComponent } from './Sectors and roles/All Roles/roles/roles.component';
import { AddEditRoleComponent } from './Sectors and roles/All Roles/add-edit-role/add-edit-role.component';
import { SectorsComponent } from './Sectors and roles/All Sectors/sectors/sectors.component';
import { AddEditSectorComponent } from './Sectors and roles/All Sectors/add-edit-sector/add-edit-sector.component'; // Import RouterModule separately
import { TagInputModule } from 'ngx-chips';
import { HttpClientModule } from '@angular/common/http';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSelectModule } from '@angular/material/select';
import { AddEditCollectionComponent } from './Collection/add-edit-collection/add-edit-collection.component';
import { AppUsersComponent } from './App-User-Management/app-users/app-users.component';
import { UserStatusComponent } from './App-User-Management/user-status/user-status.component';
import { CamelCasePipe } from '../../../camel-case.pipe';
import { EditAppUserComponent } from './App-User-Management/edit-app-user/edit-app-user.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { AddNewInsightComponent } from './App-User-Management/add-new-insight/add-new-insight.component';
import { ChoosePositionComponent } from './App-User-Management/choose-position/choose-position.component';
import { NgCircleProgressModule } from 'ng-circle-progress';
import { NgxMaskModule, IConfig } from 'ngx-mask';
import { AddEmployersComponent } from './Employer-Opportunities/Employer/add-employers/add-employers.component';
import { AddEditOpportunityComponent } from './Employer-Opportunities/Opportunity/add-edit-opportunity/add-edit-opportunity.component';
import { NgSelectModule, NgLabelTemplateDirective, NgOptionTemplateDirective } from '@ng-select/ng-select';
import { ImageCropperModule } from 'ngx-image-cropper';
@NgModule({
  declarations: [
    ActionsComponent,
    InsightComponent,
    DegreeComponent,
    CollectionsComponent,
    EthnicityComponent,
    SexualOrientationComponent,
    RegionalAccentComponent,
    IndustryComponent,
    EmployerOpportunitiesComponent,
    OpportunitiesComponent,
    UniversityListComponent,
    AddEditInsightComponent,
    AddEditUniversityListComponent,
    RolesComponent,
    AddEditRoleComponent,
    SectorsComponent,
    AddEditSectorComponent,
    AddEditCollectionComponent,
    AppUsersComponent,
    UserStatusComponent,
    CamelCasePipe,
    EditAppUserComponent,
    NotificationsComponent,
    AddNewInsightComponent,
    ChoosePositionComponent,
    AddEmployersComponent,
    AddEditOpportunityComponent
  ],
  imports: [
    CommonModule,
    ActionsRoutingModule,
    SidebarModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgbTooltipModule,
    Ng2SearchPipeModule,
    RouterModule, TagInputModule, HttpClientModule,
    MatChipsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    MatSelectModule,
    NgCircleProgressModule.forRoot({}),
    NgxMaskModule.forRoot(),
    NgSelectModule,
    ImageCropperModule
    
  ],
  providers: [
    DatePipe 
  ]
})
export class ActionsModule { }

{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, Input, Output, ChangeDetectionStrategy, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { ZIndexUtils } from 'primeng-lts/utils';\nimport * as i5 from 'primeng-lts/api';\nimport { PrimeTemplate } from 'primeng-lts/api';\nimport * as i4 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng-lts/ripple';\nimport { RippleModule } from 'primeng-lts/ripple';\nimport * as i2 from 'primeng-lts/tooltip';\nimport { TooltipModule } from 'primeng-lts/tooltip';\n\nconst _c0 = function (a0) {\n  return {\n    \"p-hidden\": a0\n  };\n};\n\nfunction MenubarSub_ng_template_1_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 4);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, child_r1.visible === false));\n  }\n}\n\nfunction MenubarSub_ng_template_1_li_1_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 15);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r1.icon);\n  }\n}\n\nfunction MenubarSub_ng_template_1_li_1_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r1.label);\n  }\n}\n\nfunction MenubarSub_ng_template_1_li_1_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", child_r1.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nconst _c1 = function (a0, a1) {\n  return {\n    \"pi-angle-down\": a0,\n    \"pi-angle-right\": a1\n  };\n};\n\nfunction MenubarSub_ng_template_1_li_1_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c1, ctx_r13.root, !ctx_r13.root));\n  }\n}\n\nconst _c2 = function (a1) {\n  return {\n    \"p-menuitem-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nfunction MenubarSub_ng_template_1_li_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function MenubarSub_ng_template_1_li_1_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const child_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r17 = i0.ɵɵnextContext();\n      return ctx_r17.onItemClick($event, child_r1);\n    })(\"mouseenter\", function MenubarSub_ng_template_1_li_1_a_2_Template_a_mouseenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const child_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return ctx_r20.onItemMouseEnter($event, child_r1);\n    });\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_1_li_1_a_2_span_1_Template, 1, 1, \"span\", 11);\n    i0.ɵɵtemplate(2, MenubarSub_ng_template_1_li_1_a_2_span_2_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtemplate(3, MenubarSub_ng_template_1_li_1_a_2_ng_template_3_Template, 1, 1, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, MenubarSub_ng_template_1_li_1_a_2_span_5_Template, 1, 4, \"span\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r11 = i0.ɵɵreference(4);\n\n    const child_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c2, child_r1.disabled));\n    i0.ɵɵattribute(\"href\", child_r1.url, i0.ɵɵsanitizeUrl)(\"data-automationid\", child_r1.automationId)(\"target\", child_r1.target)(\"title\", child_r1.title)(\"id\", child_r1.id)(\"tabindex\", child_r1.disabled ? null : \"0\")(\"aria-haspopup\", ctx_r6.item.items != null)(\"aria-expanded\", ctx_r6.item === ctx_r6.activeItem);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.escape !== false)(\"ngIfElse\", _r11);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", child_r1.items);\n  }\n}\n\nfunction MenubarSub_ng_template_1_li_1_a_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 15);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r1.icon);\n  }\n}\n\nfunction MenubarSub_ng_template_1_li_1_a_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r1.label);\n  }\n}\n\nfunction MenubarSub_ng_template_1_li_1_a_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", child_r1.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction MenubarSub_ng_template_1_li_1_a_3_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c1, ctx_r27.root, !ctx_r27.root));\n  }\n}\n\nconst _c3 = function () {\n  return {\n    exact: false\n  };\n};\n\nfunction MenubarSub_ng_template_1_li_1_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 19);\n    i0.ɵɵlistener(\"click\", function MenubarSub_ng_template_1_li_1_a_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const child_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r31 = i0.ɵɵnextContext();\n      return ctx_r31.onItemClick($event, child_r1);\n    })(\"mouseenter\", function MenubarSub_ng_template_1_li_1_a_3_Template_a_mouseenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const child_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r34 = i0.ɵɵnextContext();\n      return ctx_r34.onItemMouseEnter($event, child_r1);\n    });\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_1_li_1_a_3_span_1_Template, 1, 1, \"span\", 11);\n    i0.ɵɵtemplate(2, MenubarSub_ng_template_1_li_1_a_3_span_2_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtemplate(3, MenubarSub_ng_template_1_li_1_a_3_ng_template_3_Template, 1, 1, \"ng-template\", null, 20, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, MenubarSub_ng_template_1_li_1_a_3_span_5_Template, 1, 4, \"span\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r25 = i0.ɵɵreference(4);\n\n    const child_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"routerLink\", child_r1.routerLink)(\"queryParams\", child_r1.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", child_r1.routerLinkActiveOptions || i0.ɵɵpureFunction0(20, _c3))(\"ngClass\", i0.ɵɵpureFunction1(21, _c2, child_r1.disabled))(\"fragment\", child_r1.fragment)(\"queryParamsHandling\", child_r1.queryParamsHandling)(\"preserveFragment\", child_r1.preserveFragment)(\"skipLocationChange\", child_r1.skipLocationChange)(\"replaceUrl\", child_r1.replaceUrl)(\"state\", child_r1.state);\n    i0.ɵɵattribute(\"data-automationid\", child_r1.automationId)(\"target\", child_r1.target)(\"title\", child_r1.title)(\"id\", child_r1.id)(\"tabindex\", child_r1.disabled ? null : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.escape !== false)(\"ngIfElse\", _r25);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", child_r1.items);\n  }\n}\n\nfunction MenubarSub_ng_template_1_li_1_p_menubarSub_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-menubarSub\", 21);\n    i0.ɵɵlistener(\"leafClick\", function MenubarSub_ng_template_1_li_1_p_menubarSub_4_Template_p_menubarSub_leafClick_0_listener() {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext(3);\n      return ctx_r37.onLeafClick();\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"parentActive\", child_r1 === ctx_r8.activeItem)(\"item\", child_r1)(\"mobileActive\", ctx_r8.mobileActive)(\"autoDisplay\", ctx_r8.autoDisplay);\n  }\n}\n\nconst _c4 = function (a1, a2) {\n  return {\n    \"p-menuitem\": true,\n    \"p-menuitem-active\": a1,\n    \"p-hidden\": a2\n  };\n};\n\nfunction MenubarSub_ng_template_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 5, 6);\n    i0.ɵɵtemplate(2, MenubarSub_ng_template_1_li_1_a_2_Template, 6, 15, \"a\", 7);\n    i0.ɵɵtemplate(3, MenubarSub_ng_template_1_li_1_a_3_Template, 6, 23, \"a\", 8);\n    i0.ɵɵtemplate(4, MenubarSub_ng_template_1_li_1_p_menubarSub_4_Template, 1, 4, \"p-menubarSub\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(child_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c4, child_r1 === ctx_r3.activeItem, child_r1.visible === false))(\"ngStyle\", child_r1.style)(\"tooltipOptions\", child_r1.tooltipOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !child_r1.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.items);\n  }\n}\n\nfunction MenubarSub_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_1_li_0_Template, 1, 3, \"li\", 2);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_1_li_1_Template, 5, 11, \"li\", 3);\n  }\n\n  if (rf & 2) {\n    const child_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", child_r1.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !child_r1.separator);\n  }\n}\n\nconst _c5 = function (a0, a1) {\n  return {\n    \"p-submenu-list\": a0,\n    \"p-menubar-root-list\": a1\n  };\n};\n\nconst _c6 = [\"menubutton\"];\nconst _c7 = [\"rootmenu\"];\n\nfunction Menubar_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Menubar_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtemplate(1, Menubar_div_1_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.startTemplate);\n  }\n}\n\nfunction Menubar_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Menubar_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtemplate(1, Menubar_div_7_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.endTemplate);\n  }\n}\n\nfunction Menubar_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c8 = function (a1) {\n  return {\n    \"p-menubar p-component\": true,\n    \"p-menubar-mobile-active\": a1\n  };\n};\n\nconst _c9 = [\"*\"];\n\nclass MenubarSub {\n  constructor(el, renderer, cd) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.leafClick = new EventEmitter();\n    this.menuHoverActive = false;\n  }\n\n  get parentActive() {\n    return this._parentActive;\n  }\n\n  set parentActive(value) {\n    if (!this.root) {\n      this._parentActive = value;\n      if (!value) this.activeItem = null;\n    }\n  }\n\n  onItemClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n\n    if (item.items) {\n      if (this.activeItem && item === this.activeItem) {\n        this.activeItem = null;\n        this.unbindDocumentClickListener();\n      } else {\n        this.activeItem = item;\n\n        if (this.root) {\n          this.bindDocumentClickListener();\n        }\n      }\n    }\n\n    if (!item.items) {\n      this.onLeafClick();\n    }\n  }\n\n  onItemMouseEnter(event, item) {\n    if (item.disabled || this.mobileActive) {\n      event.preventDefault();\n      return;\n    }\n\n    if (this.root) {\n      if (this.activeItem || this.autoDisplay) {\n        this.activeItem = item;\n        this.bindDocumentClickListener();\n      }\n    } else {\n      this.activeItem = item;\n      this.bindDocumentClickListener();\n    }\n  }\n\n  onLeafClick() {\n    this.activeItem = null;\n\n    if (this.root) {\n      this.unbindDocumentClickListener();\n    }\n\n    this.leafClick.emit();\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      this.documentClickListener = event => {\n        if (this.el && !this.el.nativeElement.contains(event.target)) {\n          this.activeItem = null;\n          this.cd.markForCheck();\n          this.unbindDocumentClickListener();\n        }\n      };\n\n      document.addEventListener('click', this.documentClickListener);\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      document.removeEventListener('click', this.documentClickListener);\n      this.documentClickListener = null;\n    }\n  }\n\n  ngOnDestroy() {\n    this.unbindDocumentClickListener();\n  }\n\n}\n\nMenubarSub.ɵfac = function MenubarSub_Factory(t) {\n  return new (t || MenubarSub)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nMenubarSub.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MenubarSub,\n  selectors: [[\"p-menubarSub\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    item: \"item\",\n    root: \"root\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    mobileActive: \"mobileActive\",\n    autoDisplay: \"autoDisplay\",\n    parentActive: \"parentActive\"\n  },\n  outputs: {\n    leafClick: \"leafClick\"\n  },\n  decls: 2,\n  vars: 6,\n  consts: [[3, \"ngClass\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-menu-separator\", \"role\", \"separator\", 3, \"ngClass\", 4, \"ngIf\"], [\"role\", \"none\", \"pTooltip\", \"\", 3, \"ngClass\", \"ngStyle\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 1, \"p-menu-separator\", 3, \"ngClass\"], [\"role\", \"none\", \"pTooltip\", \"\", 3, \"ngClass\", \"ngStyle\", \"tooltipOptions\"], [\"listItem\", \"\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"mouseenter\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"mouseenter\", 4, \"ngIf\"], [3, \"parentActive\", \"item\", \"mobileActive\", \"autoDisplay\", \"leafClick\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"mouseenter\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-submenu-icon pi\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-submenu-icon\", \"pi\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"mouseenter\"], [\"htmlRouteLabel\", \"\"], [3, \"parentActive\", \"item\", \"mobileActive\", \"autoDisplay\", \"leafClick\"]],\n  template: function MenubarSub_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ul\", 0);\n      i0.ɵɵtemplate(1, MenubarSub_ng_template_1_Template, 2, 2, \"ng-template\", 1);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c5, !ctx.root, ctx.root));\n      i0.ɵɵattribute(\"role\", ctx.root ? \"menubar\" : \"menu\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.root ? ctx.item : ctx.item.items);\n    }\n  },\n  directives: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.Tooltip, i1.NgStyle, i3.Ripple, i4.RouterLinkWithHref, i4.RouterLinkActive, MenubarSub],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-menubarSub',\n      template: `\n        <ul [ngClass]=\"{'p-submenu-list': !root, 'p-menubar-root-list': root}\" [attr.role]=\"root ? 'menubar' : 'menu'\">\n            <ng-template ngFor let-child [ngForOf]=\"(root ? item : item.items)\">\n                <li *ngIf=\"child.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': child.visible === false}\" role=\"separator\">\n                <li *ngIf=\"!child.separator\" #listItem [ngClass]=\"{'p-menuitem':true, 'p-menuitem-active': child === activeItem, 'p-hidden': child.visible === false}\" [ngStyle]=\"child.style\" [class]=\"child.styleClass\" role=\"none\" pTooltip [tooltipOptions]=\"child.tooltipOptions\">\n                    <a *ngIf=\"!child.routerLink\" [attr.href]=\"child.url\" [attr.data-automationid]=\"child.automationId\" [attr.target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" role=\"menuitem\"\n                        (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"\n                         [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\" [attr.tabindex]=\"child.disabled ? null : '0'\" [attr.aria-haspopup]=\"item.items != null\" [attr.aria-expanded]=\"item === activeItem\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-submenu-icon pi\" *ngIf=\"child.items\" [ngClass]=\"{'pi-angle-down':root,'pi-angle-right':!root}\"></span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" [routerLink]=\"child.routerLink\" [attr.data-automationid]=\"child.automationId\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\"\n                        [attr.target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" [attr.tabindex]=\"child.disabled ? null : '0'\" role=\"menuitem\"\n                        (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"\n                        [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                        [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-submenu-icon pi\" *ngIf=\"child.items\" [ngClass]=\"{'pi-angle-down':root,'pi-angle-right':!root}\"></span>\n                    </a>\n                    <p-menubarSub [parentActive]=\"child === activeItem\" [item]=\"child\" *ngIf=\"child.items\" [mobileActive]=\"mobileActive\" [autoDisplay]=\"autoDisplay\" (leafClick)=\"onLeafClick()\"></p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    item: [{\n      type: Input\n    }],\n    root: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    mobileActive: [{\n      type: Input\n    }],\n    autoDisplay: [{\n      type: Input\n    }],\n    parentActive: [{\n      type: Input\n    }],\n    leafClick: [{\n      type: Output\n    }]\n  });\n})();\n\nclass Menubar {\n  constructor(el, renderer, cd, config) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'start':\n          this.startTemplate = item.template;\n          break;\n\n        case 'end':\n          this.endTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  toggle(event) {\n    if (this.mobileActive) {\n      this.hide();\n      ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n    } else {\n      this.mobileActive = true;\n      ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n    }\n\n    this.bindOutsideClickListener();\n    event.preventDefault();\n  }\n\n  bindOutsideClickListener() {\n    if (!this.outsideClickListener) {\n      this.outsideClickListener = event => {\n        if (this.mobileActive && this.rootmenu.el.nativeElement !== event.target && !this.rootmenu.el.nativeElement.contains(event.target) && this.menubutton.nativeElement !== event.target && !this.menubutton.nativeElement.contains(event.target)) {\n          this.hide();\n        }\n      };\n\n      document.addEventListener('click', this.outsideClickListener);\n    }\n  }\n\n  hide() {\n    this.mobileActive = false;\n    this.cd.markForCheck();\n    ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n    this.unbindOutsideClickListener();\n  }\n\n  onLeafClick() {\n    this.hide();\n  }\n\n  unbindOutsideClickListener() {\n    if (this.outsideClickListener) {\n      document.removeEventListener('click', this.outsideClickListener);\n      this.outsideClickListener = null;\n    }\n  }\n\n  ngOnDestroy() {\n    this.unbindOutsideClickListener();\n  }\n\n}\n\nMenubar.ɵfac = function Menubar_Factory(t) {\n  return new (t || Menubar)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PrimeNGConfig));\n};\n\nMenubar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Menubar,\n  selectors: [[\"p-menubar\"]],\n  contentQueries: function Menubar_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function Menubar_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c6, 5);\n      i0.ɵɵviewQuery(_c7, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubutton = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    model: \"model\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    autoDisplay: \"autoDisplay\"\n  },\n  ngContentSelectors: _c9,\n  decls: 10,\n  vars: 14,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-menubar-start\", 4, \"ngIf\"], [\"tabindex\", \"0\", 1, \"p-menubar-button\", 3, \"click\"], [\"menubutton\", \"\"], [1, \"pi\", \"pi-bars\"], [\"root\", \"root\", 3, \"item\", \"baseZIndex\", \"autoZIndex\", \"mobileActive\", \"autoDisplay\", \"leafClick\"], [\"rootmenu\", \"\"], [\"class\", \"p-menubar-end\", 4, \"ngIf\", \"ngIfElse\"], [\"legacy\", \"\"], [1, \"p-menubar-start\"], [4, \"ngTemplateOutlet\"], [1, \"p-menubar-end\"]],\n  template: function Menubar_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtemplate(1, Menubar_div_1_Template, 2, 1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"a\", 2, 3);\n      i0.ɵɵlistener(\"click\", function Menubar_Template_a_click_2_listener($event) {\n        return ctx.toggle($event);\n      });\n      i0.ɵɵelement(4, \"i\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"p-menubarSub\", 5, 6);\n      i0.ɵɵlistener(\"leafClick\", function Menubar_Template_p_menubarSub_leafClick_5_listener() {\n        return ctx.onLeafClick();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(7, Menubar_div_7_Template, 2, 1, \"div\", 7);\n      i0.ɵɵtemplate(8, Menubar_ng_template_8_Template, 2, 0, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      const _r4 = i0.ɵɵreference(9);\n\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c8, ctx.mobileActive))(\"ngStyle\", ctx.style);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.startTemplate);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"item\", ctx.model)(\"baseZIndex\", ctx.baseZIndex)(\"autoZIndex\", ctx.autoZIndex)(\"mobileActive\", ctx.mobileActive)(\"autoDisplay\", ctx.autoDisplay);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.endTemplate)(\"ngIfElse\", _r4);\n    }\n  },\n  directives: [i1.NgClass, i1.NgStyle, i1.NgIf, MenubarSub, i1.NgTemplateOutlet],\n  styles: [\".p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:1}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Menubar, [{\n    type: Component,\n    args: [{\n      selector: 'p-menubar',\n      template: `\n        <div [ngClass]=\"{'p-menubar p-component':true, 'p-menubar-mobile-active': mobileActive}\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a #menubutton tabindex=\"0\" class=\"p-menubar-button\" (click)=\"toggle($event)\">\n                <i class=\"pi pi-bars\"></i>\n            </a>\n            <p-menubarSub #rootmenu [item]=\"model\" root=\"root\" [baseZIndex]=\"baseZIndex\" (leafClick)=\"onLeafClick()\" [autoZIndex]=\"autoZIndex\" [mobileActive]=\"mobileActive\" [autoDisplay]=\"autoDisplay\"></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      styleUrls: ['./menubar.css'],\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i5.PrimeNGConfig\n    }];\n  }, {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    autoDisplay: [{\n      type: Input\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    menubutton: [{\n      type: ViewChild,\n      args: ['menubutton']\n    }],\n    rootmenu: [{\n      type: ViewChild,\n      args: ['rootmenu']\n    }]\n  });\n})();\n\nclass MenubarModule {}\n\nMenubarModule.ɵfac = function MenubarModule_Factory(t) {\n  return new (t || MenubarModule)();\n};\n\nMenubarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MenubarModule\n});\nMenubarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule, RouterModule, RippleModule, TooltipModule], RouterModule, TooltipModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n      exports: [Menubar, RouterModule, TooltipModule],\n      declarations: [Menubar, MenubarSub]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Menubar, MenubarModule, MenubarSub };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/primeng-lts/fesm2015/primeng-lts-menubar.js"], "names": ["i0", "EventEmitter", "Component", "ViewEncapsulation", "Input", "Output", "ChangeDetectionStrategy", "ContentChildren", "ViewChild", "NgModule", "i1", "CommonModule", "ZIndexUtils", "i5", "PrimeTemplate", "i4", "RouterModule", "i3", "RippleModule", "i2", "TooltipModule", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "el", "renderer", "cd", "autoZIndex", "baseZIndex", "leafClick", "menuHoverActive", "parentActive", "_parentActive", "value", "root", "activeItem", "onItemClick", "event", "item", "disabled", "preventDefault", "url", "routerLink", "command", "originalEvent", "items", "unbindDocumentClickListener", "bindDocumentClickListener", "onLeafClick", "onItemMouseEnter", "mobileActive", "autoDisplay", "emit", "documentClickListener", "nativeElement", "contains", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "document", "addEventListener", "removeEventListener", "ngOnDestroy", "ɵfac", "ElementRef", "Renderer2", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "<PERSON><PERSON><PERSON>", "NgStyle", "<PERSON><PERSON><PERSON>", "RouterLinkWithHref", "RouterLinkActive", "type", "args", "selector", "template", "encapsulation", "None", "host", "Men<PERSON><PERSON>", "config", "ngAfterContentInit", "templates", "for<PERSON>ach", "getType", "startTemplate", "endTemplate", "toggle", "hide", "clear", "rootmenu", "set", "zIndex", "menu", "bindOutsideClickListener", "outsideClickListener", "menubutton", "unbindOutsideClickListener", "PrimeNGConfig", "NgTemplateOutlet", "changeDetection", "OnPush", "styleUrls", "model", "style", "styleClass", "MenubarModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,iBAAlC,EAAqDC,KAArD,EAA4DC,MAA5D,EAAoEC,uBAApE,EAA6FC,eAA7F,EAA8GC,SAA9G,EAAyHC,QAAzH,QAAyI,eAAzI;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,WAAT,QAA4B,mBAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,oBAApB;AACA,SAASC,YAAT,QAA6B,oBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,qBAApB;AACA,SAASC,aAAT,QAA8B,qBAA9B;;;;;;;;;;AAiG6FpB,IAAAA,EAI7E,sB;;;;qBAJ6EA,E;AAAAA,IAAAA,EAIxB,uBAJwBA,EAIxB,qD;;;;;;AAJwBA,IAAAA,EASrE,yB;;;;qBATqEA,E;AAAAA,IAAAA,EASpB,qC;;;;;;AAToBA,IAAAA,EAUrE,8B;AAVqEA,IAAAA,EAUQ,U;AAVRA,IAAAA,EAUuB,e;;;;qBAVvBA,E;AAAAA,IAAAA,EAUQ,a;AAVRA,IAAAA,EAUQ,kC;;;;;;AAVRA,IAAAA,EAW7C,yB;;;;qBAX6CA,E;AAAAA,IAAAA,EAWf,yCAXeA,EAWf,gB;;;;;;;;;;;;;AAXeA,IAAAA,EAYrE,yB;;;;oBAZqEA,E;AAAAA,IAAAA,EAYjB,uBAZiBA,EAYjB,sD;;;;;;;;;;;;;iBAZiBA,E;;AAAAA,IAAAA,EAMzE,2B;AANyEA,IAAAA,EAOrE;AAPqEA,MAAAA,EAOrE;AAAA,uBAPqEA,EAOrE;AAAA,sBAPqEA,EAOrE;AAAA,aAAS,qCAAT;AAAA;AAPqEA,MAAAA,EAOrE;AAAA,uBAPqEA,EAOrE;AAAA,sBAPqEA,EAOrE;AAAA,aAAmD,0CAAnD;AAAA,M;AAPqEA,IAAAA,EASrE,mF;AATqEA,IAAAA,EAUrE,mF;AAVqEA,IAAAA,EAWrE,wGAXqEA,EAWrE,wB;AAXqEA,IAAAA,EAYrE,mF;AAZqEA,IAAAA,EAazE,e;;;;iBAbyEA,E;;qBAAAA,E;mBAAAA,E;AAAAA,IAAAA,EAQpE,uBARoEA,EAQpE,6C;AARoEA,IAAAA,EAM5C,mCAN4CA,EAM5C,8Q;AAN4CA,IAAAA,EAStC,a;AATsCA,IAAAA,EAStC,kC;AATsCA,IAAAA,EAUtC,a;AAVsCA,IAAAA,EAUtC,gE;AAVsCA,IAAAA,EAYpC,a;AAZoCA,IAAAA,EAYpC,mC;;;;;;AAZoCA,IAAAA,EAmBrE,yB;;;;qBAnBqEA,E;AAAAA,IAAAA,EAmBpB,qC;;;;;;AAnBoBA,IAAAA,EAoBrE,8B;AApBqEA,IAAAA,EAoBa,U;AApBbA,IAAAA,EAoB4B,e;;;;qBApB5BA,E;AAAAA,IAAAA,EAoBa,a;AApBbA,IAAAA,EAoBa,kC;;;;;;AApBbA,IAAAA,EAqBxC,yB;;;;qBArBwCA,E;AAAAA,IAAAA,EAqBV,yCArBUA,EAqBV,gB;;;;;;AArBUA,IAAAA,EAsBrE,yB;;;;oBAtBqEA,E;AAAAA,IAAAA,EAsBjB,uBAtBiBA,EAsBjB,sD;;;;;;;;;;;;iBAtBiBA,E;;AAAAA,IAAAA,EAczE,2B;AAdyEA,IAAAA,EAgBrE;AAhBqEA,MAAAA,EAgBrE;AAAA,uBAhBqEA,EAgBrE;AAAA,sBAhBqEA,EAgBrE;AAAA,aAAS,qCAAT;AAAA;AAhBqEA,MAAAA,EAgBrE;AAAA,uBAhBqEA,EAgBrE;AAAA,sBAhBqEA,EAgBrE;AAAA,aAAmD,0CAAnD;AAAA,M;AAhBqEA,IAAAA,EAmBrE,mF;AAnBqEA,IAAAA,EAoBrE,mF;AApBqEA,IAAAA,EAqBrE,wGArBqEA,EAqBrE,wB;AArBqEA,IAAAA,EAsBrE,mF;AAtBqEA,IAAAA,EAuBzE,e;;;;iBAvByEA,E;;qBAAAA,E;AAAAA,IAAAA,EAc7C,iMAd6CA,EAc7C,sCAd6CA,EAc7C,+R;AAd6CA,IAAAA,EAcb,2K;AAdaA,IAAAA,EAmBtC,a;AAnBsCA,IAAAA,EAmBtC,kC;AAnBsCA,IAAAA,EAoBtC,a;AApBsCA,IAAAA,EAoBtC,gE;AApBsCA,IAAAA,EAsBpC,a;AAtBoCA,IAAAA,EAsBpC,mC;;;;;;iBAtBoCA,E;;AAAAA,IAAAA,EAwBzE,sC;AAxByEA,IAAAA,EAwBwE;AAxBxEA,MAAAA,EAwBwE;AAAA,sBAxBxEA,EAwBwE;AAAA,aAAa,qBAAb;AAAA,M;AAxBxEA,IAAAA,EAwBoG,e;;;;qBAxBpGA,E;mBAAAA,E;AAAAA,IAAAA,EAwB3D,qJ;;;;;;;;;;;;;;AAxB2DA,IAAAA,EAK7E,8B;AAL6EA,IAAAA,EAMzE,yE;AANyEA,IAAAA,EAczE,yE;AAdyEA,IAAAA,EAwBzE,8F;AAxByEA,IAAAA,EAyB7E,e;;;;qBAzB6EA,E;mBAAAA,E;AAAAA,IAAAA,EAKkG,gC;AALlGA,IAAAA,EAKtC,uBALsCA,EAKtC,2J;AALsCA,IAAAA,EAMrE,a;AANqEA,IAAAA,EAMrE,yC;AANqEA,IAAAA,EAcrE,a;AAdqEA,IAAAA,EAcrE,wC;AAdqEA,IAAAA,EAwBL,a;AAxBKA,IAAAA,EAwBL,mC;;;;;;AAxBKA,IAAAA,EAI7E,qE;AAJ6EA,IAAAA,EAK7E,sE;;;;;AAL6EA,IAAAA,EAIxE,uC;AAJwEA,IAAAA,EAKxE,a;AALwEA,IAAAA,EAKxE,wC;;;;;;;;;;;;;;;;AALwEA,IAAAA,EAsJ7E,sB;;;;;;AAtJ6EA,IAAAA,EAqJjF,4B;AArJiFA,IAAAA,EAsJ7E,+E;AAtJ6EA,IAAAA,EAuJjF,e;;;;mBAvJiFA,E;AAAAA,IAAAA,EAsJ9D,a;AAtJ8DA,IAAAA,EAsJ9D,qD;;;;;;AAtJ8DA,IAAAA,EA6J7E,sB;;;;;;AA7J6EA,IAAAA,EA4JjF,6B;AA5JiFA,IAAAA,EA6J7E,+E;AA7J6EA,IAAAA,EA8JjF,e;;;;mBA9JiFA,E;AAAAA,IAAAA,EA6J9D,a;AA7J8DA,IAAAA,EA6J9D,mD;;;;;;AA7J8DA,IAAAA,EAgK7E,6B;AAhK6EA,IAAAA,EAiKzE,gB;AAjKyEA,IAAAA,EAkK7E,e;;;;;;;;;;;;;AAjQhB,MAAMqB,UAAN,CAAiB;AACbC,EAAAA,WAAW,CAACC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmB;AAC1B,SAAKF,EAAL,GAAUA,EAAV;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,EAAL,GAAUA,EAAV;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKC,UAAL,GAAkB,CAAlB;AACA,SAAKC,SAAL,GAAiB,IAAI3B,YAAJ,EAAjB;AACA,SAAK4B,eAAL,GAAuB,KAAvB;AACH;;AACe,MAAZC,YAAY,GAAG;AACf,WAAO,KAAKC,aAAZ;AACH;;AACe,MAAZD,YAAY,CAACE,KAAD,EAAQ;AACpB,QAAI,CAAC,KAAKC,IAAV,EAAgB;AACZ,WAAKF,aAAL,GAAqBC,KAArB;AACA,UAAI,CAACA,KAAL,EACI,KAAKE,UAAL,GAAkB,IAAlB;AACP;AACJ;;AACDC,EAAAA,WAAW,CAACC,KAAD,EAAQC,IAAR,EAAc;AACrB,QAAIA,IAAI,CAACC,QAAT,EAAmB;AACfF,MAAAA,KAAK,CAACG,cAAN;AACA;AACH;;AACD,QAAI,CAACF,IAAI,CAACG,GAAN,IAAa,CAACH,IAAI,CAACI,UAAvB,EAAmC;AAC/BL,MAAAA,KAAK,CAACG,cAAN;AACH;;AACD,QAAIF,IAAI,CAACK,OAAT,EAAkB;AACdL,MAAAA,IAAI,CAACK,OAAL,CAAa;AACTC,QAAAA,aAAa,EAAEP,KADN;AAETC,QAAAA,IAAI,EAAEA;AAFG,OAAb;AAIH;;AACD,QAAIA,IAAI,CAACO,KAAT,EAAgB;AACZ,UAAI,KAAKV,UAAL,IAAmBG,IAAI,KAAK,KAAKH,UAArC,EAAiD;AAC7C,aAAKA,UAAL,GAAkB,IAAlB;AACA,aAAKW,2BAAL;AACH,OAHD,MAIK;AACD,aAAKX,UAAL,GAAkBG,IAAlB;;AACA,YAAI,KAAKJ,IAAT,EAAe;AACX,eAAKa,yBAAL;AACH;AACJ;AACJ;;AACD,QAAI,CAACT,IAAI,CAACO,KAAV,EAAiB;AACb,WAAKG,WAAL;AACH;AACJ;;AACDC,EAAAA,gBAAgB,CAACZ,KAAD,EAAQC,IAAR,EAAc;AAC1B,QAAIA,IAAI,CAACC,QAAL,IAAiB,KAAKW,YAA1B,EAAwC;AACpCb,MAAAA,KAAK,CAACG,cAAN;AACA;AACH;;AACD,QAAI,KAAKN,IAAT,EAAe;AACX,UAAI,KAAKC,UAAL,IAAmB,KAAKgB,WAA5B,EAAyC;AACrC,aAAKhB,UAAL,GAAkBG,IAAlB;AACA,aAAKS,yBAAL;AACH;AACJ,KALD,MAMK;AACD,WAAKZ,UAAL,GAAkBG,IAAlB;AACA,WAAKS,yBAAL;AACH;AACJ;;AACDC,EAAAA,WAAW,GAAG;AACV,SAAKb,UAAL,GAAkB,IAAlB;;AACA,QAAI,KAAKD,IAAT,EAAe;AACX,WAAKY,2BAAL;AACH;;AACD,SAAKjB,SAAL,CAAeuB,IAAf;AACH;;AACDL,EAAAA,yBAAyB,GAAG;AACxB,QAAI,CAAC,KAAKM,qBAAV,EAAiC;AAC7B,WAAKA,qBAAL,GAA8BhB,KAAD,IAAW;AACpC,YAAI,KAAKb,EAAL,IAAW,CAAC,KAAKA,EAAL,CAAQ8B,aAAR,CAAsBC,QAAtB,CAA+BlB,KAAK,CAACmB,MAArC,CAAhB,EAA8D;AAC1D,eAAKrB,UAAL,GAAkB,IAAlB;AACA,eAAKT,EAAL,CAAQ+B,YAAR;AACA,eAAKX,2BAAL;AACH;AACJ,OAND;;AAOAY,MAAAA,QAAQ,CAACC,gBAAT,CAA0B,OAA1B,EAAmC,KAAKN,qBAAxC;AACH;AACJ;;AACDP,EAAAA,2BAA2B,GAAG;AAC1B,QAAI,KAAKO,qBAAT,EAAgC;AAC5BK,MAAAA,QAAQ,CAACE,mBAAT,CAA6B,OAA7B,EAAsC,KAAKP,qBAA3C;AACA,WAAKA,qBAAL,GAA6B,IAA7B;AACH;AACJ;;AACDQ,EAAAA,WAAW,GAAG;AACV,SAAKf,2BAAL;AACH;;AA7FY;;AA+FjBxB,UAAU,CAACwC,IAAX;AAAA,mBAAuGxC,UAAvG,EAA6FrB,EAA7F,mBAAmIA,EAAE,CAAC8D,UAAtI,GAA6F9D,EAA7F,mBAA6JA,EAAE,CAAC+D,SAAhK,GAA6F/D,EAA7F,mBAAsLA,EAAE,CAACgE,iBAAzL;AAAA;;AACA3C,UAAU,CAAC4C,IAAX,kBAD6FjE,EAC7F;AAAA,QAA2FqB,UAA3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAD6FrB,MAAAA,EAErF,2BADR;AAD6FA,MAAAA,EAGjF,yEAFZ;AAD6FA,MAAAA,EA2BrF,eA1BR;AAAA;;AAAA;AAD6FA,MAAAA,EAEjF,uBAFiFA,EAEjF,8CADZ;AAD6FA,MAAAA,EAEd,mDAD/E;AAD6FA,MAAAA,EAGpD,aAFzC;AAD6FA,MAAAA,EAGpD,4DAFzC;AAAA;AAAA;AAAA,eA2BuOU,EAAE,CAACwD,OA3B1O,EA2BkTxD,EAAE,CAACyD,OA3BrT,EA2B4ZzD,EAAE,CAAC0D,IA3B/Z,EA2B6ejD,EAAE,CAACkD,OA3Bhf,EA2BuwB3D,EAAE,CAAC4D,OA3B1wB,EA2By0BrD,EAAE,CAACsD,MA3B50B,EA2Bq3BxD,EAAE,CAACyD,kBA3Bx3B,EA2BqmCzD,EAAE,CAAC0D,gBA3BxmC,EA2B4CpD,UA3B5C;AAAA;AAAA;;AA4BA;AAAA,qDA7B6FrB,EA6B7F,mBAA2FqB,UAA3F,EAAmH,CAAC;AACxGqD,IAAAA,IAAI,EAAExE,SADkG;AAExGyE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,cADX;AAECC,MAAAA,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA7BmB;AA8BCC,MAAAA,aAAa,EAAE3E,iBAAiB,CAAC4E,IA9BlC;AA+BCC,MAAAA,IAAI,EAAE;AACF,iBAAS;AADP;AA/BP,KAAD;AAFkG,GAAD,CAAnH,EAqC4B,YAAY;AAAE,WAAO,CAAC;AAAEN,MAAAA,IAAI,EAAE1E,EAAE,CAAC8D;AAAX,KAAD,EAA0B;AAAEY,MAAAA,IAAI,EAAE1E,EAAE,CAAC+D;AAAX,KAA1B,EAAkD;AAAEW,MAAAA,IAAI,EAAE1E,EAAE,CAACgE;AAAX,KAAlD,CAAP;AAA2F,GArCrI,EAqCuJ;AAAE3B,IAAAA,IAAI,EAAE,CAAC;AAChJqC,MAAAA,IAAI,EAAEtE;AAD0I,KAAD,CAAR;AAEvI6B,IAAAA,IAAI,EAAE,CAAC;AACPyC,MAAAA,IAAI,EAAEtE;AADC,KAAD,CAFiI;AAIvIsB,IAAAA,UAAU,EAAE,CAAC;AACbgD,MAAAA,IAAI,EAAEtE;AADO,KAAD,CAJ2H;AAMvIuB,IAAAA,UAAU,EAAE,CAAC;AACb+C,MAAAA,IAAI,EAAEtE;AADO,KAAD,CAN2H;AAQvI6C,IAAAA,YAAY,EAAE,CAAC;AACfyB,MAAAA,IAAI,EAAEtE;AADS,KAAD,CARyH;AAUvI8C,IAAAA,WAAW,EAAE,CAAC;AACdwB,MAAAA,IAAI,EAAEtE;AADQ,KAAD,CAV0H;AAYvI0B,IAAAA,YAAY,EAAE,CAAC;AACf4C,MAAAA,IAAI,EAAEtE;AADS,KAAD,CAZyH;AAcvIwB,IAAAA,SAAS,EAAE,CAAC;AACZ8C,MAAAA,IAAI,EAAErE;AADM,KAAD;AAd4H,GArCvJ;AAAA;;AAsDA,MAAM4E,OAAN,CAAc;AACV3D,EAAAA,WAAW,CAACC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmByD,MAAnB,EAA2B;AAClC,SAAK3D,EAAL,GAAUA,EAAV;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,EAAL,GAAUA,EAAV;AACA,SAAKyD,MAAL,GAAcA,MAAd;AACA,SAAKxD,UAAL,GAAkB,IAAlB;AACA,SAAKC,UAAL,GAAkB,CAAlB;AACH;;AACDwD,EAAAA,kBAAkB,GAAG;AACjB,SAAKC,SAAL,CAAeC,OAAf,CAAwBhD,IAAD,IAAU;AAC7B,cAAQA,IAAI,CAACiD,OAAL,EAAR;AACI,aAAK,OAAL;AACI,eAAKC,aAAL,GAAqBlD,IAAI,CAACwC,QAA1B;AACA;;AACJ,aAAK,KAAL;AACI,eAAKW,WAAL,GAAmBnD,IAAI,CAACwC,QAAxB;AACA;AANR;AAQH,KATD;AAUH;;AACDY,EAAAA,MAAM,CAACrD,KAAD,EAAQ;AACV,QAAI,KAAKa,YAAT,EAAuB;AACnB,WAAKyC,IAAL;AACA9E,MAAAA,WAAW,CAAC+E,KAAZ,CAAkB,KAAKC,QAAL,CAAcrE,EAAd,CAAiB8B,aAAnC;AACH,KAHD,MAIK;AACD,WAAKJ,YAAL,GAAoB,IAApB;AACArC,MAAAA,WAAW,CAACiF,GAAZ,CAAgB,MAAhB,EAAwB,KAAKD,QAAL,CAAcrE,EAAd,CAAiB8B,aAAzC,EAAwD,KAAK6B,MAAL,CAAYY,MAAZ,CAAmBC,IAA3E;AACH;;AACD,SAAKC,wBAAL;AACA5D,IAAAA,KAAK,CAACG,cAAN;AACH;;AACDyD,EAAAA,wBAAwB,GAAG;AACvB,QAAI,CAAC,KAAKC,oBAAV,EAAgC;AAC5B,WAAKA,oBAAL,GAA6B7D,KAAD,IAAW;AACnC,YAAI,KAAKa,YAAL,IAAqB,KAAK2C,QAAL,CAAcrE,EAAd,CAAiB8B,aAAjB,KAAmCjB,KAAK,CAACmB,MAA9D,IAAwE,CAAC,KAAKqC,QAAL,CAAcrE,EAAd,CAAiB8B,aAAjB,CAA+BC,QAA/B,CAAwClB,KAAK,CAACmB,MAA9C,CAAzE,IACG,KAAK2C,UAAL,CAAgB7C,aAAhB,KAAkCjB,KAAK,CAACmB,MAD3C,IACqD,CAAC,KAAK2C,UAAL,CAAgB7C,aAAhB,CAA8BC,QAA9B,CAAuClB,KAAK,CAACmB,MAA7C,CAD1D,EACgH;AAC5G,eAAKmC,IAAL;AACH;AACJ,OALD;;AAMAjC,MAAAA,QAAQ,CAACC,gBAAT,CAA0B,OAA1B,EAAmC,KAAKuC,oBAAxC;AACH;AACJ;;AACDP,EAAAA,IAAI,GAAG;AACH,SAAKzC,YAAL,GAAoB,KAApB;AACA,SAAKxB,EAAL,CAAQ+B,YAAR;AACA5C,IAAAA,WAAW,CAAC+E,KAAZ,CAAkB,KAAKC,QAAL,CAAcrE,EAAd,CAAiB8B,aAAnC;AACA,SAAK8C,0BAAL;AACH;;AACDpD,EAAAA,WAAW,GAAG;AACV,SAAK2C,IAAL;AACH;;AACDS,EAAAA,0BAA0B,GAAG;AACzB,QAAI,KAAKF,oBAAT,EAA+B;AAC3BxC,MAAAA,QAAQ,CAACE,mBAAT,CAA6B,OAA7B,EAAsC,KAAKsC,oBAA3C;AACA,WAAKA,oBAAL,GAA4B,IAA5B;AACH;AACJ;;AACDrC,EAAAA,WAAW,GAAG;AACV,SAAKuC,0BAAL;AACH;;AA7DS;;AA+DdlB,OAAO,CAACpB,IAAR;AAAA,mBAAoGoB,OAApG,EAlJ6FjF,EAkJ7F,mBAA6HA,EAAE,CAAC8D,UAAhI,GAlJ6F9D,EAkJ7F,mBAAuJA,EAAE,CAAC+D,SAA1J,GAlJ6F/D,EAkJ7F,mBAAgLA,EAAE,CAACgE,iBAAnL,GAlJ6FhE,EAkJ7F,mBAAiNa,EAAE,CAACuF,aAApN;AAAA;;AACAnB,OAAO,CAAChB,IAAR,kBAnJ6FjE,EAmJ7F;AAAA,QAAwFiF,OAAxF;AAAA;AAAA;AAAA;AAnJ6FjF,MAAAA,EAmJ7F,0BAAuWc,aAAvW;AAAA;;AAAA;AAAA;;AAnJ6Fd,MAAAA,EAmJ7F,qBAnJ6FA,EAmJ7F;AAAA;AAAA;AAAA;AAAA;AAnJ6FA,MAAAA,EAmJ7F;AAnJ6FA,MAAAA,EAmJ7F;AAAA;;AAAA;AAAA;;AAnJ6FA,MAAAA,EAmJ7F,qBAnJ6FA,EAmJ7F;AAnJ6FA,MAAAA,EAmJ7F,qBAnJ6FA,EAmJ7F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAnJ6FA,MAAAA,EAmJ7F;AAnJ6FA,MAAAA,EAoJrF,4BADR;AAnJ6FA,MAAAA,EAqJjF,sDAFZ;AAnJ6FA,MAAAA,EAwJjF,6BALZ;AAnJ6FA,MAAAA,EAwJ5B;AAAA,eAAS,kBAAT;AAAA,QALjE;AAnJ6FA,MAAAA,EAyJ7E,qBANhB;AAnJ6FA,MAAAA,EA0JjF,eAPZ;AAnJ6FA,MAAAA,EA2JjF,wCARZ;AAnJ6FA,MAAAA,EA2JJ;AAAA,eAAa,iBAAb;AAAA,QARzF;AAnJ6FA,MAAAA,EA2J4G,eARzM;AAnJ6FA,MAAAA,EA4JjF,sDATZ;AAnJ6FA,MAAAA,EA+JjF,6EA/JiFA,EA+JjF,wBAZZ;AAnJ6FA,MAAAA,EAoKrF,eAjBR;AAAA;;AAAA;AAAA,kBAnJ6FA,EAmJ7F;;AAnJ6FA,MAAAA,EAoJI,2BADjG;AAnJ6FA,MAAAA,EAoJhF,uBApJgFA,EAoJhF,kEADb;AAnJ6FA,MAAAA,EAqJnD,aAF1C;AAnJ6FA,MAAAA,EAqJnD,sCAF1C;AAnJ6FA,MAAAA,EA2JzD,aARpC;AAnJ6FA,MAAAA,EA2JzD,4JARpC;AAnJ6FA,MAAAA,EA4JrD,aATxC;AAnJ6FA,MAAAA,EA4JrD,qDATxC;AAAA;AAAA;AAAA,eAkB4oCU,EAAE,CAACwD,OAlB/oC,EAkButCxD,EAAE,CAAC4D,OAlB1tC,EAkByxC5D,EAAE,CAAC0D,IAlB5xC,EAkBi9B/C,UAlBj9B,EAkB02CX,EAAE,CAAC2F,gBAlB72C;AAAA;AAAA;AAAA;AAAA;;AAmBA;AAAA,qDAtK6FrG,EAsK7F,mBAA2FiF,OAA3F,EAAgH,CAAC;AACrGP,IAAAA,IAAI,EAAExE,SAD+F;AAErGyE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,WADX;AAECC,MAAAA,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KApBmB;AAqBCyB,MAAAA,eAAe,EAAEhG,uBAAuB,CAACiG,MArB1C;AAsBCzB,MAAAA,aAAa,EAAE3E,iBAAiB,CAAC4E,IAtBlC;AAuBCyB,MAAAA,SAAS,EAAE,CAAC,eAAD,CAvBZ;AAwBCxB,MAAAA,IAAI,EAAE;AACF,iBAAS;AADP;AAxBP,KAAD;AAF+F,GAAD,CAAhH,EA8B4B,YAAY;AAAE,WAAO,CAAC;AAAEN,MAAAA,IAAI,EAAE1E,EAAE,CAAC8D;AAAX,KAAD,EAA0B;AAAEY,MAAAA,IAAI,EAAE1E,EAAE,CAAC+D;AAAX,KAA1B,EAAkD;AAAEW,MAAAA,IAAI,EAAE1E,EAAE,CAACgE;AAAX,KAAlD,EAAkF;AAAEU,MAAAA,IAAI,EAAE7D,EAAE,CAACuF;AAAX,KAAlF,CAAP;AAAuH,GA9BjK,EA8BmL;AAAEK,IAAAA,KAAK,EAAE,CAAC;AAC7K/B,MAAAA,IAAI,EAAEtE;AADuK,KAAD,CAAT;AAEnKsG,IAAAA,KAAK,EAAE,CAAC;AACRhC,MAAAA,IAAI,EAAEtE;AADE,KAAD,CAF4J;AAInKuG,IAAAA,UAAU,EAAE,CAAC;AACbjC,MAAAA,IAAI,EAAEtE;AADO,KAAD,CAJuJ;AAMnKsB,IAAAA,UAAU,EAAE,CAAC;AACbgD,MAAAA,IAAI,EAAEtE;AADO,KAAD,CANuJ;AAQnKuB,IAAAA,UAAU,EAAE,CAAC;AACb+C,MAAAA,IAAI,EAAEtE;AADO,KAAD,CARuJ;AAUnK8C,IAAAA,WAAW,EAAE,CAAC;AACdwB,MAAAA,IAAI,EAAEtE;AADQ,KAAD,CAVsJ;AAYnKgF,IAAAA,SAAS,EAAE,CAAC;AACZV,MAAAA,IAAI,EAAEnE,eADM;AAEZoE,MAAAA,IAAI,EAAE,CAAC7D,aAAD;AAFM,KAAD,CAZwJ;AAenKoF,IAAAA,UAAU,EAAE,CAAC;AACbxB,MAAAA,IAAI,EAAElE,SADO;AAEbmE,MAAAA,IAAI,EAAE,CAAC,YAAD;AAFO,KAAD,CAfuJ;AAkBnKiB,IAAAA,QAAQ,EAAE,CAAC;AACXlB,MAAAA,IAAI,EAAElE,SADK;AAEXmE,MAAAA,IAAI,EAAE,CAAC,UAAD;AAFK,KAAD;AAlByJ,GA9BnL;AAAA;;AAoDA,MAAMiC,aAAN,CAAoB;;AAEpBA,aAAa,CAAC/C,IAAd;AAAA,mBAA0G+C,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBA7N6F7G,EA6N7F;AAAA,QAA2G4G;AAA3G;AACAA,aAAa,CAACE,IAAd,kBA9N6F9G,EA8N7F;AAAA,YAAoI,CAACW,YAAD,EAAeK,YAAf,EAA6BE,YAA7B,EAA2CE,aAA3C,CAApI,EAA+LJ,YAA/L,EAA6MI,aAA7M;AAAA;;AACA;AAAA,qDA/N6FpB,EA+N7F,mBAA2F4G,aAA3F,EAAsH,CAAC;AAC3GlC,IAAAA,IAAI,EAAEjE,QADqG;AAE3GkE,IAAAA,IAAI,EAAE,CAAC;AACCoC,MAAAA,OAAO,EAAE,CAACpG,YAAD,EAAeK,YAAf,EAA6BE,YAA7B,EAA2CE,aAA3C,CADV;AAEC4F,MAAAA,OAAO,EAAE,CAAC/B,OAAD,EAAUjE,YAAV,EAAwBI,aAAxB,CAFV;AAGC6F,MAAAA,YAAY,EAAE,CAAChC,OAAD,EAAU5D,UAAV;AAHf,KAAD;AAFqG,GAAD,CAAtH;AAAA;AASA;AACA;AACA;;;AAEA,SAAS4D,OAAT,EAAkB2B,aAAlB,EAAiCvF,UAAjC", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, Input, Output, ChangeDetectionStrategy, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { ZIndexUtils } from 'primeng-lts/utils';\nimport * as i5 from 'primeng-lts/api';\nimport { PrimeTemplate } from 'primeng-lts/api';\nimport * as i4 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng-lts/ripple';\nimport { RippleModule } from 'primeng-lts/ripple';\nimport * as i2 from 'primeng-lts/tooltip';\nimport { TooltipModule } from 'primeng-lts/tooltip';\n\nclass MenubarSub {\n    constructor(el, renderer, cd) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.leafClick = new EventEmitter();\n        this.menuHoverActive = false;\n    }\n    get parentActive() {\n        return this._parentActive;\n    }\n    set parentActive(value) {\n        if (!this.root) {\n            this._parentActive = value;\n            if (!value)\n                this.activeItem = null;\n        }\n    }\n    onItemClick(event, item) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        if (item.items) {\n            if (this.activeItem && item === this.activeItem) {\n                this.activeItem = null;\n                this.unbindDocumentClickListener();\n            }\n            else {\n                this.activeItem = item;\n                if (this.root) {\n                    this.bindDocumentClickListener();\n                }\n            }\n        }\n        if (!item.items) {\n            this.onLeafClick();\n        }\n    }\n    onItemMouseEnter(event, item) {\n        if (item.disabled || this.mobileActive) {\n            event.preventDefault();\n            return;\n        }\n        if (this.root) {\n            if (this.activeItem || this.autoDisplay) {\n                this.activeItem = item;\n                this.bindDocumentClickListener();\n            }\n        }\n        else {\n            this.activeItem = item;\n            this.bindDocumentClickListener();\n        }\n    }\n    onLeafClick() {\n        this.activeItem = null;\n        if (this.root) {\n            this.unbindDocumentClickListener();\n        }\n        this.leafClick.emit();\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            this.documentClickListener = (event) => {\n                if (this.el && !this.el.nativeElement.contains(event.target)) {\n                    this.activeItem = null;\n                    this.cd.markForCheck();\n                    this.unbindDocumentClickListener();\n                }\n            };\n            document.addEventListener('click', this.documentClickListener);\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            document.removeEventListener('click', this.documentClickListener);\n            this.documentClickListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.unbindDocumentClickListener();\n    }\n}\nMenubarSub.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: MenubarSub, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nMenubarSub.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.0.5\", type: MenubarSub, selector: \"p-menubarSub\", inputs: { item: \"item\", root: \"root\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", mobileActive: \"mobileActive\", autoDisplay: \"autoDisplay\", parentActive: \"parentActive\" }, outputs: { leafClick: \"leafClick\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <ul [ngClass]=\"{'p-submenu-list': !root, 'p-menubar-root-list': root}\" [attr.role]=\"root ? 'menubar' : 'menu'\">\n            <ng-template ngFor let-child [ngForOf]=\"(root ? item : item.items)\">\n                <li *ngIf=\"child.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': child.visible === false}\" role=\"separator\">\n                <li *ngIf=\"!child.separator\" #listItem [ngClass]=\"{'p-menuitem':true, 'p-menuitem-active': child === activeItem, 'p-hidden': child.visible === false}\" [ngStyle]=\"child.style\" [class]=\"child.styleClass\" role=\"none\" pTooltip [tooltipOptions]=\"child.tooltipOptions\">\n                    <a *ngIf=\"!child.routerLink\" [attr.href]=\"child.url\" [attr.data-automationid]=\"child.automationId\" [attr.target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" role=\"menuitem\"\n                        (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"\n                         [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\" [attr.tabindex]=\"child.disabled ? null : '0'\" [attr.aria-haspopup]=\"item.items != null\" [attr.aria-expanded]=\"item === activeItem\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-submenu-icon pi\" *ngIf=\"child.items\" [ngClass]=\"{'pi-angle-down':root,'pi-angle-right':!root}\"></span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" [routerLink]=\"child.routerLink\" [attr.data-automationid]=\"child.automationId\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\"\n                        [attr.target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" [attr.tabindex]=\"child.disabled ? null : '0'\" role=\"menuitem\"\n                        (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"\n                        [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                        [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-submenu-icon pi\" *ngIf=\"child.items\" [ngClass]=\"{'pi-angle-down':root,'pi-angle-right':!root}\"></span>\n                    </a>\n                    <p-menubarSub [parentActive]=\"child === activeItem\" [item]=\"child\" *ngIf=\"child.items\" [mobileActive]=\"mobileActive\" [autoDisplay]=\"autoDisplay\" (leafClick)=\"onLeafClick()\"></p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `, isInline: true, components: [{ type: MenubarSub, selector: \"p-menubarSub\", inputs: [\"item\", \"root\", \"autoZIndex\", \"baseZIndex\", \"mobileActive\", \"autoDisplay\", \"parentActive\"], outputs: [\"leafClick\"] }], directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i2.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { type: i3.Ripple, selector: \"[pRipple]\" }, { type: i4.RouterLinkWithHref, selector: \"a[routerLink],area[routerLink]\", inputs: [\"routerLink\", \"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"relativeTo\"] }, { type: i4.RouterLinkActive, selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"routerLinkActive\"], exportAs: [\"routerLinkActive\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: MenubarSub, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-menubarSub',\n                    template: `\n        <ul [ngClass]=\"{'p-submenu-list': !root, 'p-menubar-root-list': root}\" [attr.role]=\"root ? 'menubar' : 'menu'\">\n            <ng-template ngFor let-child [ngForOf]=\"(root ? item : item.items)\">\n                <li *ngIf=\"child.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': child.visible === false}\" role=\"separator\">\n                <li *ngIf=\"!child.separator\" #listItem [ngClass]=\"{'p-menuitem':true, 'p-menuitem-active': child === activeItem, 'p-hidden': child.visible === false}\" [ngStyle]=\"child.style\" [class]=\"child.styleClass\" role=\"none\" pTooltip [tooltipOptions]=\"child.tooltipOptions\">\n                    <a *ngIf=\"!child.routerLink\" [attr.href]=\"child.url\" [attr.data-automationid]=\"child.automationId\" [attr.target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" role=\"menuitem\"\n                        (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"\n                         [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\" [attr.tabindex]=\"child.disabled ? null : '0'\" [attr.aria-haspopup]=\"item.items != null\" [attr.aria-expanded]=\"item === activeItem\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-submenu-icon pi\" *ngIf=\"child.items\" [ngClass]=\"{'pi-angle-down':root,'pi-angle-right':!root}\"></span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" [routerLink]=\"child.routerLink\" [attr.data-automationid]=\"child.automationId\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\"\n                        [attr.target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" [attr.tabindex]=\"child.disabled ? null : '0'\" role=\"menuitem\"\n                        (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"\n                        [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                        [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-submenu-icon pi\" *ngIf=\"child.items\" [ngClass]=\"{'pi-angle-down':root,'pi-angle-right':!root}\"></span>\n                    </a>\n                    <p-menubarSub [parentActive]=\"child === activeItem\" [item]=\"child\" *ngIf=\"child.items\" [mobileActive]=\"mobileActive\" [autoDisplay]=\"autoDisplay\" (leafClick)=\"onLeafClick()\"></p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { item: [{\n                type: Input\n            }], root: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], mobileActive: [{\n                type: Input\n            }], autoDisplay: [{\n                type: Input\n            }], parentActive: [{\n                type: Input\n            }], leafClick: [{\n                type: Output\n            }] } });\nclass Menubar {\n    constructor(el, renderer, cd, config) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'start':\n                    this.startTemplate = item.template;\n                    break;\n                case 'end':\n                    this.endTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    toggle(event) {\n        if (this.mobileActive) {\n            this.hide();\n            ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n        }\n        else {\n            this.mobileActive = true;\n            ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n        }\n        this.bindOutsideClickListener();\n        event.preventDefault();\n    }\n    bindOutsideClickListener() {\n        if (!this.outsideClickListener) {\n            this.outsideClickListener = (event) => {\n                if (this.mobileActive && this.rootmenu.el.nativeElement !== event.target && !this.rootmenu.el.nativeElement.contains(event.target)\n                    && this.menubutton.nativeElement !== event.target && !this.menubutton.nativeElement.contains(event.target)) {\n                    this.hide();\n                }\n            };\n            document.addEventListener('click', this.outsideClickListener);\n        }\n    }\n    hide() {\n        this.mobileActive = false;\n        this.cd.markForCheck();\n        ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n        this.unbindOutsideClickListener();\n    }\n    onLeafClick() {\n        this.hide();\n    }\n    unbindOutsideClickListener() {\n        if (this.outsideClickListener) {\n            document.removeEventListener('click', this.outsideClickListener);\n            this.outsideClickListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.unbindOutsideClickListener();\n    }\n}\nMenubar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: Menubar, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i5.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\nMenubar.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.0.5\", type: Menubar, selector: \"p-menubar\", inputs: { model: \"model\", style: \"style\", styleClass: \"styleClass\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", autoDisplay: \"autoDisplay\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"menubutton\", first: true, predicate: [\"menubutton\"], descendants: true }, { propertyName: \"rootmenu\", first: true, predicate: [\"rootmenu\"], descendants: true }], ngImport: i0, template: `\n        <div [ngClass]=\"{'p-menubar p-component':true, 'p-menubar-mobile-active': mobileActive}\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a #menubutton tabindex=\"0\" class=\"p-menubar-button\" (click)=\"toggle($event)\">\n                <i class=\"pi pi-bars\"></i>\n            </a>\n            <p-menubarSub #rootmenu [item]=\"model\" root=\"root\" [baseZIndex]=\"baseZIndex\" (leafClick)=\"onLeafClick()\" [autoZIndex]=\"autoZIndex\" [mobileActive]=\"mobileActive\" [autoDisplay]=\"autoDisplay\"></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `, isInline: true, styles: [\".p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:1}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}\"], components: [{ type: MenubarSub, selector: \"p-menubarSub\", inputs: [\"item\", \"root\", \"autoZIndex\", \"baseZIndex\", \"mobileActive\", \"autoDisplay\", \"parentActive\"], outputs: [\"leafClick\"] }], directives: [{ type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: Menubar, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-menubar',\n                    template: `\n        <div [ngClass]=\"{'p-menubar p-component':true, 'p-menubar-mobile-active': mobileActive}\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a #menubutton tabindex=\"0\" class=\"p-menubar-button\" (click)=\"toggle($event)\">\n                <i class=\"pi pi-bars\"></i>\n            </a>\n            <p-menubarSub #rootmenu [item]=\"model\" root=\"root\" [baseZIndex]=\"baseZIndex\" (leafClick)=\"onLeafClick()\" [autoZIndex]=\"autoZIndex\" [mobileActive]=\"mobileActive\" [autoDisplay]=\"autoDisplay\"></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    styleUrls: ['./menubar.css'],\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i5.PrimeNGConfig }]; }, propDecorators: { model: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], autoDisplay: [{\n                type: Input\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], menubutton: [{\n                type: ViewChild,\n                args: ['menubutton']\n            }], rootmenu: [{\n                type: ViewChild,\n                args: ['rootmenu']\n            }] } });\nclass MenubarModule {\n}\nMenubarModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: MenubarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMenubarModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: MenubarModule, declarations: [Menubar, MenubarSub], imports: [CommonModule, RouterModule, RippleModule, TooltipModule], exports: [Menubar, RouterModule, TooltipModule] });\nMenubarModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: MenubarModule, imports: [[CommonModule, RouterModule, RippleModule, TooltipModule], RouterModule, TooltipModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: MenubarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n                    exports: [Menubar, RouterModule, TooltipModule],\n                    declarations: [Menubar, MenubarSub]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menubar, MenubarModule, MenubarSub };\n"]}, "metadata": {}, "sourceType": "module"}
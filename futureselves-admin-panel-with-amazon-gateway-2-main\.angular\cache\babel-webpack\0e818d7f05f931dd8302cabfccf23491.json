{"ast": null, "code": "import { coerceElement, coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\n\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n\n}\n\nMutationObserverFactory.ɵfac = function MutationObserverFactory_Factory(t) {\n  return new (t || MutationObserverFactory)();\n};\n\nMutationObserverFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MutationObserverFactory,\n  factory: MutationObserverFactory.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\n\n\nclass ContentObserver {\n  constructor(_mutationObserverFactory) {\n    this._mutationObserverFactory = _mutationObserverFactory;\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n\n    this._observedElements = new Map();\n  }\n\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n\n      const subscription = stream.subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n\n\n  _observeElement(element) {\n    if (!this._observedElements.has(element)) {\n      const stream = new Subject();\n\n      const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n\n      if (observer) {\n        observer.observe(element, {\n          characterData: true,\n          childList: true,\n          subtree: true\n        });\n      }\n\n      this._observedElements.set(element, {\n        observer,\n        stream,\n        count: 1\n      });\n    } else {\n      this._observedElements.get(element).count++;\n    }\n\n    return this._observedElements.get(element).stream;\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n\n\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n\n\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n\n      if (observer) {\n        observer.disconnect();\n      }\n\n      stream.complete();\n\n      this._observedElements.delete(element);\n    }\n  }\n\n}\n\nContentObserver.ɵfac = function ContentObserver_Factory(t) {\n  return new (t || ContentObserver)(i0.ɵɵinject(MutationObserverFactory));\n};\n\nContentObserver.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ContentObserver,\n  factory: ContentObserver.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: MutationObserverFactory\n    }];\n  }, null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\n\n\nclass CdkObserveContent {\n  constructor(_contentObserver, _elementRef, _ngZone) {\n    this._contentObserver = _contentObserver;\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    /** Event emitted for each change in the element's content. */\n\n    this.event = new EventEmitter();\n    this._disabled = false;\n    this._currentSubscription = null;\n  }\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  /** Debounce interval for emitting the changes. */\n\n\n  get debounce() {\n    return this._debounce;\n  }\n\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n\n    this._subscribe();\n  }\n\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n\n  _subscribe() {\n    this._unsubscribe();\n\n    const stream = this._contentObserver.observe(this._elementRef); // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n    // Consider brining it back inside the zone next time we're making breaking changes.\n    // Bringing it back inside can cause things like infinite change detection loops and changed\n    // after checked errors if people's code isn't handling it properly.\n\n\n    this._ngZone.runOutsideAngular(() => {\n      this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    });\n  }\n\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n\n}\n\nCdkObserveContent.ɵfac = function CdkObserveContent_Factory(t) {\n  return new (t || CdkObserveContent)(i0.ɵɵdirectiveInject(ContentObserver), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nCdkObserveContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkObserveContent,\n  selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n  inputs: {\n    disabled: [\"cdkObserveContentDisabled\", \"disabled\"],\n    debounce: \"debounce\"\n  },\n  outputs: {\n    event: \"cdkObserveContent\"\n  },\n  exportAs: [\"cdkObserveContent\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent'\n    }]\n  }], function () {\n    return [{\n      type: ContentObserver\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['cdkObserveContentDisabled']\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\n\nclass ObserversModule {}\n\nObserversModule.ɵfac = function ObserversModule_Factory(t) {\n  return new (t || ObserversModule)();\n};\n\nObserversModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ObserversModule\n});\nObserversModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MutationObserverFactory]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkObserveContent],\n      declarations: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/cdk/fesm2020/observers.mjs"], "names": ["coerceElement", "coerceBooleanProperty", "coerceNumberProperty", "i0", "Injectable", "EventEmitter", "Directive", "Output", "Input", "NgModule", "Observable", "Subject", "debounceTime", "MutationObserverFactory", "create", "callback", "MutationObserver", "ɵfac", "ɵprov", "type", "args", "providedIn", "ContentObserver", "constructor", "_mutationObserverFactory", "_observedElements", "Map", "ngOnDestroy", "for<PERSON>ach", "_", "element", "_cleanupObserver", "observe", "elementOrRef", "observer", "stream", "_observeElement", "subscription", "subscribe", "unsubscribe", "_unobserveElement", "has", "mutations", "next", "characterData", "childList", "subtree", "set", "count", "get", "disconnect", "complete", "delete", "CdkObserveContent", "_contentObserver", "_elementRef", "_ngZone", "event", "_disabled", "_currentSubscription", "disabled", "value", "_unsubscribe", "_subscribe", "debounce", "_debounce", "ngAfterContentInit", "runOutsideAngular", "pipe", "ElementRef", "NgZone", "ɵdir", "selector", "exportAs", "ObserversModule", "ɵmod", "ɵinj", "exports", "declarations", "providers"], "mappings": "AAAA,SAASA,aAAT,EAAwBC,qBAAxB,EAA+CC,oBAA/C,QAA2E,uBAA3E;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,MAA9C,EAAsDC,KAAtD,EAA6DC,QAA7D,QAA6E,eAA7E;AACA,SAASC,UAAT,EAAqBC,OAArB,QAAoC,MAApC;AACA,SAASC,YAAT,QAA6B,gBAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,MAAMC,uBAAN,CAA8B;AAC1BC,EAAAA,MAAM,CAACC,QAAD,EAAW;AACb,WAAO,OAAOC,gBAAP,KAA4B,WAA5B,GAA0C,IAA1C,GAAiD,IAAIA,gBAAJ,CAAqBD,QAArB,CAAxD;AACH;;AAHyB;;AAK9BF,uBAAuB,CAACI,IAAxB;AAAA,mBAAoHJ,uBAApH;AAAA;;AACAA,uBAAuB,CAACK,KAAxB,kBAD0Gf,EAC1G;AAAA,SAAwHU,uBAAxH;AAAA,WAAwHA,uBAAxH;AAAA,cAA6J;AAA7J;;AACA;AAAA,qDAF0GV,EAE1G,mBAA2FU,uBAA3F,EAAgI,CAAC;AACrHM,IAAAA,IAAI,EAAEf,UAD+G;AAErHgB,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF+G,GAAD,CAAhI;AAAA;AAIA;;;AACA,MAAMC,eAAN,CAAsB;AAClBC,EAAAA,WAAW,CAACC,wBAAD,EAA2B;AAClC,SAAKA,wBAAL,GAAgCA,wBAAhC;AACA;;AACA,SAAKC,iBAAL,GAAyB,IAAIC,GAAJ,EAAzB;AACH;;AACDC,EAAAA,WAAW,GAAG;AACV,SAAKF,iBAAL,CAAuBG,OAAvB,CAA+B,CAACC,CAAD,EAAIC,OAAJ,KAAgB,KAAKC,gBAAL,CAAsBD,OAAtB,CAA/C;AACH;;AACDE,EAAAA,OAAO,CAACC,YAAD,EAAe;AAClB,UAAMH,OAAO,GAAG9B,aAAa,CAACiC,YAAD,CAA7B;AACA,WAAO,IAAIvB,UAAJ,CAAgBwB,QAAD,IAAc;AAChC,YAAMC,MAAM,GAAG,KAAKC,eAAL,CAAqBN,OAArB,CAAf;;AACA,YAAMO,YAAY,GAAGF,MAAM,CAACG,SAAP,CAAiBJ,QAAjB,CAArB;AACA,aAAO,MAAM;AACTG,QAAAA,YAAY,CAACE,WAAb;;AACA,aAAKC,iBAAL,CAAuBV,OAAvB;AACH,OAHD;AAIH,KAPM,CAAP;AAQH;AACD;AACJ;AACA;AACA;;;AACIM,EAAAA,eAAe,CAACN,OAAD,EAAU;AACrB,QAAI,CAAC,KAAKL,iBAAL,CAAuBgB,GAAvB,CAA2BX,OAA3B,CAAL,EAA0C;AACtC,YAAMK,MAAM,GAAG,IAAIxB,OAAJ,EAAf;;AACA,YAAMuB,QAAQ,GAAG,KAAKV,wBAAL,CAA8BV,MAA9B,CAAqC4B,SAAS,IAAIP,MAAM,CAACQ,IAAP,CAAYD,SAAZ,CAAlD,CAAjB;;AACA,UAAIR,QAAJ,EAAc;AACVA,QAAAA,QAAQ,CAACF,OAAT,CAAiBF,OAAjB,EAA0B;AACtBc,UAAAA,aAAa,EAAE,IADO;AAEtBC,UAAAA,SAAS,EAAE,IAFW;AAGtBC,UAAAA,OAAO,EAAE;AAHa,SAA1B;AAKH;;AACD,WAAKrB,iBAAL,CAAuBsB,GAAvB,CAA2BjB,OAA3B,EAAoC;AAAEI,QAAAA,QAAF;AAAYC,QAAAA,MAAZ;AAAoBa,QAAAA,KAAK,EAAE;AAA3B,OAApC;AACH,KAXD,MAYK;AACD,WAAKvB,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,EAAoCkB,KAApC;AACH;;AACD,WAAO,KAAKvB,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,EAAoCK,MAA3C;AACH;AACD;AACJ;AACA;AACA;;;AACIK,EAAAA,iBAAiB,CAACV,OAAD,EAAU;AACvB,QAAI,KAAKL,iBAAL,CAAuBgB,GAAvB,CAA2BX,OAA3B,CAAJ,EAAyC;AACrC,WAAKL,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,EAAoCkB,KAApC;;AACA,UAAI,CAAC,KAAKvB,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,EAAoCkB,KAAzC,EAAgD;AAC5C,aAAKjB,gBAAL,CAAsBD,OAAtB;AACH;AACJ;AACJ;AACD;;;AACAC,EAAAA,gBAAgB,CAACD,OAAD,EAAU;AACtB,QAAI,KAAKL,iBAAL,CAAuBgB,GAAvB,CAA2BX,OAA3B,CAAJ,EAAyC;AACrC,YAAM;AAAEI,QAAAA,QAAF;AAAYC,QAAAA;AAAZ,UAAuB,KAAKV,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,CAA7B;;AACA,UAAII,QAAJ,EAAc;AACVA,QAAAA,QAAQ,CAACgB,UAAT;AACH;;AACDf,MAAAA,MAAM,CAACgB,QAAP;;AACA,WAAK1B,iBAAL,CAAuB2B,MAAvB,CAA8BtB,OAA9B;AACH;AACJ;;AAhEiB;;AAkEtBR,eAAe,CAACL,IAAhB;AAAA,mBAA4GK,eAA5G,EAzE0GnB,EAyE1G,UAA6IU,uBAA7I;AAAA;;AACAS,eAAe,CAACJ,KAAhB,kBA1E0Gf,EA0E1G;AAAA,SAAgHmB,eAAhH;AAAA,WAAgHA,eAAhH;AAAA,cAA6I;AAA7I;;AACA;AAAA,qDA3E0GnB,EA2E1G,mBAA2FmB,eAA3F,EAAwH,CAAC;AAC7GH,IAAAA,IAAI,EAAEf,UADuG;AAE7GgB,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFuG,GAAD,CAAxH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAEN;AAAR,KAAD,CAAP;AAA6C,GAHvF;AAAA;AAIA;AACA;AACA;AACA;;;AACA,MAAMwC,iBAAN,CAAwB;AACpB9B,EAAAA,WAAW,CAAC+B,gBAAD,EAAmBC,WAAnB,EAAgCC,OAAhC,EAAyC;AAChD,SAAKF,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA;;AACA,SAAKC,KAAL,GAAa,IAAIpD,YAAJ,EAAb;AACA,SAAKqD,SAAL,GAAiB,KAAjB;AACA,SAAKC,oBAAL,GAA4B,IAA5B;AACH;AACD;AACJ;AACA;AACA;;;AACgB,MAARC,QAAQ,GAAG;AACX,WAAO,KAAKF,SAAZ;AACH;;AACW,MAARE,QAAQ,CAACC,KAAD,EAAQ;AAChB,SAAKH,SAAL,GAAiBzD,qBAAqB,CAAC4D,KAAD,CAAtC;AACA,SAAKH,SAAL,GAAiB,KAAKI,YAAL,EAAjB,GAAuC,KAAKC,UAAL,EAAvC;AACH;AACD;;;AACY,MAARC,QAAQ,GAAG;AACX,WAAO,KAAKC,SAAZ;AACH;;AACW,MAARD,QAAQ,CAACH,KAAD,EAAQ;AAChB,SAAKI,SAAL,GAAiB/D,oBAAoB,CAAC2D,KAAD,CAArC;;AACA,SAAKE,UAAL;AACH;;AACDG,EAAAA,kBAAkB,GAAG;AACjB,QAAI,CAAC,KAAKP,oBAAN,IAA8B,CAAC,KAAKC,QAAxC,EAAkD;AAC9C,WAAKG,UAAL;AACH;AACJ;;AACDpC,EAAAA,WAAW,GAAG;AACV,SAAKmC,YAAL;AACH;;AACDC,EAAAA,UAAU,GAAG;AACT,SAAKD,YAAL;;AACA,UAAM3B,MAAM,GAAG,KAAKmB,gBAAL,CAAsBtB,OAAtB,CAA8B,KAAKuB,WAAnC,CAAf,CAFS,CAGT;AACA;AACA;AACA;;;AACA,SAAKC,OAAL,CAAaW,iBAAb,CAA+B,MAAM;AACjC,WAAKR,oBAAL,GAA4B,CAAC,KAAKK,QAAL,GAAgB7B,MAAM,CAACiC,IAAP,CAAYxD,YAAY,CAAC,KAAKoD,QAAN,CAAxB,CAAhB,GAA2D7B,MAA5D,EAAoEG,SAApE,CAA8E,KAAKmB,KAAnF,CAA5B;AACH,KAFD;AAGH;;AACDK,EAAAA,YAAY,GAAG;AACX,SAAKH,oBAAL,EAA2BpB,WAA3B;AACH;;AAlDmB;;AAoDxBc,iBAAiB,CAACpC,IAAlB;AAAA,mBAA8GoC,iBAA9G,EAvI0GlD,EAuI1G,mBAAiJmB,eAAjJ,GAvI0GnB,EAuI1G,mBAA6KA,EAAE,CAACkE,UAAhL,GAvI0GlE,EAuI1G,mBAAuMA,EAAE,CAACmE,MAA1M;AAAA;;AACAjB,iBAAiB,CAACkB,IAAlB,kBAxI0GpE,EAwI1G;AAAA,QAAkGkD,iBAAlG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAzI0GlD,EAyI1G,mBAA2FkD,iBAA3F,EAA0H,CAAC;AAC/GlC,IAAAA,IAAI,EAAEb,SADyG;AAE/Gc,IAAAA,IAAI,EAAE,CAAC;AACCoD,MAAAA,QAAQ,EAAE,qBADX;AAECC,MAAAA,QAAQ,EAAE;AAFX,KAAD;AAFyG,GAAD,CAA1H,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAEtD,MAAAA,IAAI,EAAEG;AAAR,KAAD,EAA4B;AAAEH,MAAAA,IAAI,EAAEhB,EAAE,CAACkE;AAAX,KAA5B,EAAqD;AAAElD,MAAAA,IAAI,EAAEhB,EAAE,CAACmE;AAAX,KAArD,CAAP;AAAmF,GAN7H,EAM+I;AAAEb,IAAAA,KAAK,EAAE,CAAC;AACzItC,MAAAA,IAAI,EAAEZ,MADmI;AAEzIa,MAAAA,IAAI,EAAE,CAAC,mBAAD;AAFmI,KAAD,CAAT;AAG/HwC,IAAAA,QAAQ,EAAE,CAAC;AACXzC,MAAAA,IAAI,EAAEX,KADK;AAEXY,MAAAA,IAAI,EAAE,CAAC,2BAAD;AAFK,KAAD,CAHqH;AAM/H4C,IAAAA,QAAQ,EAAE,CAAC;AACX7C,MAAAA,IAAI,EAAEX;AADK,KAAD;AANqH,GAN/I;AAAA;;AAeA,MAAMkE,eAAN,CAAsB;;AAEtBA,eAAe,CAACzD,IAAhB;AAAA,mBAA4GyD,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBA3J0GxE,EA2J1G;AAAA,QAA6GuE;AAA7G;AACAA,eAAe,CAACE,IAAhB,kBA5J0GzE,EA4J1G;AAAA,aAAyI,CAACU,uBAAD;AAAzI;;AACA;AAAA,qDA7J0GV,EA6J1G,mBAA2FuE,eAA3F,EAAwH,CAAC;AAC7GvD,IAAAA,IAAI,EAAEV,QADuG;AAE7GW,IAAAA,IAAI,EAAE,CAAC;AACCyD,MAAAA,OAAO,EAAE,CAACxB,iBAAD,CADV;AAECyB,MAAAA,YAAY,EAAE,CAACzB,iBAAD,CAFf;AAGC0B,MAAAA,SAAS,EAAE,CAAClE,uBAAD;AAHZ,KAAD;AAFuG,GAAD,CAAxH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASwC,iBAAT,EAA4B/B,eAA5B,EAA6CT,uBAA7C,EAAsE6D,eAAtE", "sourcesContent": ["import { coerceElement, coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n    create(callback) {\n        return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n}\nMutationObserverFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MutationObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nMutationObserverFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MutationObserverFactory, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MutationObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n    constructor(_mutationObserverFactory) {\n        this._mutationObserverFactory = _mutationObserverFactory;\n        /** Keeps track of the existing MutationObservers so they can be reused. */\n        this._observedElements = new Map();\n    }\n    ngOnDestroy() {\n        this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this._observeElement(element);\n            const subscription = stream.subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n                this._unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n        if (!this._observedElements.has(element)) {\n            const stream = new Subject();\n            const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n            if (observer) {\n                observer.observe(element, {\n                    characterData: true,\n                    childList: true,\n                    subtree: true,\n                });\n            }\n            this._observedElements.set(element, { observer, stream, count: 1 });\n        }\n        else {\n            this._observedElements.get(element).count++;\n        }\n        return this._observedElements.get(element).stream;\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n        if (this._observedElements.has(element)) {\n            this._observedElements.get(element).count--;\n            if (!this._observedElements.get(element).count) {\n                this._cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n        if (this._observedElements.has(element)) {\n            const { observer, stream } = this._observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this._observedElements.delete(element);\n        }\n    }\n}\nContentObserver.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ContentObserver, deps: [{ token: MutationObserverFactory }], target: i0.ɵɵFactoryTarget.Injectable });\nContentObserver.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ContentObserver, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ContentObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: MutationObserverFactory }]; } });\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n    constructor(_contentObserver, _elementRef, _ngZone) {\n        this._contentObserver = _contentObserver;\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        /** Event emitted for each change in the element's content. */\n        this.event = new EventEmitter();\n        this._disabled = false;\n        this._currentSubscription = null;\n    }\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n        return this._debounce;\n    }\n    set debounce(value) {\n        this._debounce = coerceNumberProperty(value);\n        this._subscribe();\n    }\n    ngAfterContentInit() {\n        if (!this._currentSubscription && !this.disabled) {\n            this._subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this._unsubscribe();\n    }\n    _subscribe() {\n        this._unsubscribe();\n        const stream = this._contentObserver.observe(this._elementRef);\n        // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n        // Consider brining it back inside the zone next time we're making breaking changes.\n        // Bringing it back inside can cause things like infinite change detection loops and changed\n        // after checked errors if people's code isn't handling it properly.\n        this._ngZone.runOutsideAngular(() => {\n            this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n        });\n    }\n    _unsubscribe() {\n        this._currentSubscription?.unsubscribe();\n    }\n}\nCdkObserveContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkObserveContent, deps: [{ token: ContentObserver }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nCdkObserveContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: { disabled: [\"cdkObserveContentDisabled\", \"disabled\"], debounce: \"debounce\" }, outputs: { event: \"cdkObserveContent\" }, exportAs: [\"cdkObserveContent\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkObserveContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkObserveContent]',\n                    exportAs: 'cdkObserveContent',\n                }]\n        }], ctorParameters: function () { return [{ type: ContentObserver }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { event: [{\n                type: Output,\n                args: ['cdkObserveContent']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkObserveContentDisabled']\n            }], debounce: [{\n                type: Input\n            }] } });\nclass ObserversModule {\n}\nObserversModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ObserversModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nObserversModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ObserversModule, declarations: [CdkObserveContent], exports: [CdkObserveContent] });\nObserversModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ObserversModule, providers: [MutationObserverFactory] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ObserversModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkObserveContent],\n                    declarations: [CdkObserveContent],\n                    providers: [MutationObserverFactory],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "/**\n * @license Angular v13.0.3\n * (c) 2010-2021 Google LLC. https://angular.io/\n * License: MIT\n */\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, InjectionToken, RendererFactory2, NgZone, NgModule } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵsupportsWebAnimations, ɵWebAnimationsDriver, ɵCssKeyframesDriver, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass BrowserAnimationBuilder extends AnimationBuilder {\n  constructor(rootRenderer, doc) {\n    super();\n    this._nextAnimationId = 0;\n    const typeData = {\n      id: '0',\n      encapsulation: ViewEncapsulation.None,\n      styles: [],\n      data: {\n        animation: []\n      }\n    };\n    this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n  }\n\n  build(animation) {\n    const id = this._nextAnimationId.toString();\n\n    this._nextAnimationId++;\n    const entry = Array.isArray(animation) ? sequence(animation) : animation;\n    issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n    return new BrowserAnimationFactory(id, this._renderer);\n  }\n\n}\n\nBrowserAnimationBuilder.ɵfac = function BrowserAnimationBuilder_Factory(t) {\n  return new (t || BrowserAnimationBuilder)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(DOCUMENT));\n};\n\nBrowserAnimationBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BrowserAnimationBuilder,\n  factory: BrowserAnimationBuilder.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationBuilder, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.RendererFactory2\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nclass BrowserAnimationFactory extends AnimationFactory {\n  constructor(_id, _renderer) {\n    super();\n    this._id = _id;\n    this._renderer = _renderer;\n  }\n\n  create(element, options) {\n    return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n  }\n\n}\n\nclass RendererAnimationPlayer {\n  constructor(id, element, options, _renderer) {\n    this.id = id;\n    this.element = element;\n    this._renderer = _renderer;\n    this.parentPlayer = null;\n    this._started = false;\n    this.totalTime = 0;\n\n    this._command('create', options);\n  }\n\n  _listen(eventName, callback) {\n    return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n  }\n\n  _command(command, ...args) {\n    return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n  }\n\n  onDone(fn) {\n    this._listen('done', fn);\n  }\n\n  onStart(fn) {\n    this._listen('start', fn);\n  }\n\n  onDestroy(fn) {\n    this._listen('destroy', fn);\n  }\n\n  init() {\n    this._command('init');\n  }\n\n  hasStarted() {\n    return this._started;\n  }\n\n  play() {\n    this._command('play');\n\n    this._started = true;\n  }\n\n  pause() {\n    this._command('pause');\n  }\n\n  restart() {\n    this._command('restart');\n  }\n\n  finish() {\n    this._command('finish');\n  }\n\n  destroy() {\n    this._command('destroy');\n  }\n\n  reset() {\n    this._command('reset');\n\n    this._started = false;\n  }\n\n  setPosition(p) {\n    this._command('setPosition', p);\n  }\n\n  getPosition() {\n    return this._renderer.engine.players[+this.id]?.getPosition() ?? 0;\n  }\n\n}\n\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n  return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\n\nclass AnimationRendererFactory {\n  constructor(delegate, engine, _zone) {\n    this.delegate = delegate;\n    this.engine = engine;\n    this._zone = _zone;\n    this._currentId = 0;\n    this._microtaskId = 1;\n    this._animationCallbacksBuffer = [];\n    this._rendererCache = new Map();\n    this._cdRecurDepth = 0;\n    this.promise = Promise.resolve(0);\n\n    engine.onRemovalComplete = (element, delegate) => {\n      // Note: if a component element has a leave animation, and a host leave animation,\n      // the view engine will call `removeChild` for the parent\n      // component renderer as well as for the child component renderer.\n      // Therefore, we need to check if we already removed the element.\n      const parentNode = delegate?.parentNode(element);\n\n      if (parentNode) {\n        delegate.removeChild(parentNode, element);\n      }\n    };\n  }\n\n  createRenderer(hostElement, type) {\n    const EMPTY_NAMESPACE_ID = ''; // cache the delegates to find out which cached delegate can\n    // be used by which cached renderer\n\n    const delegate = this.delegate.createRenderer(hostElement, type);\n\n    if (!hostElement || !type || !type.data || !type.data['animation']) {\n      let renderer = this._rendererCache.get(delegate);\n\n      if (!renderer) {\n        renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine); // only cache this result when the base renderer is used\n\n        this._rendererCache.set(delegate, renderer);\n      }\n\n      return renderer;\n    }\n\n    const componentId = type.id;\n    const namespaceId = type.id + '-' + this._currentId;\n    this._currentId++;\n    this.engine.register(namespaceId, hostElement);\n\n    const registerTrigger = trigger => {\n      if (Array.isArray(trigger)) {\n        trigger.forEach(registerTrigger);\n      } else {\n        this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n      }\n    };\n\n    const animationTriggers = type.data['animation'];\n    animationTriggers.forEach(registerTrigger);\n    return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n  }\n\n  begin() {\n    this._cdRecurDepth++;\n\n    if (this.delegate.begin) {\n      this.delegate.begin();\n    }\n  }\n\n  _scheduleCountTask() {\n    // always use promise to schedule microtask instead of use Zone\n    this.promise.then(() => {\n      this._microtaskId++;\n    });\n  }\n  /** @internal */\n\n\n  scheduleListenerCallback(count, fn, data) {\n    if (count >= 0 && count < this._microtaskId) {\n      this._zone.run(() => fn(data));\n\n      return;\n    }\n\n    if (this._animationCallbacksBuffer.length == 0) {\n      Promise.resolve(null).then(() => {\n        this._zone.run(() => {\n          this._animationCallbacksBuffer.forEach(tuple => {\n            const [fn, data] = tuple;\n            fn(data);\n          });\n\n          this._animationCallbacksBuffer = [];\n        });\n      });\n    }\n\n    this._animationCallbacksBuffer.push([fn, data]);\n  }\n\n  end() {\n    this._cdRecurDepth--; // this is to prevent animations from running twice when an inner\n    // component does CD when a parent component instead has inserted it\n\n    if (this._cdRecurDepth == 0) {\n      this._zone.runOutsideAngular(() => {\n        this._scheduleCountTask();\n\n        this.engine.flush(this._microtaskId);\n      });\n    }\n\n    if (this.delegate.end) {\n      this.delegate.end();\n    }\n  }\n\n  whenRenderingDone() {\n    return this.engine.whenRenderingDone();\n  }\n\n}\n\nAnimationRendererFactory.ɵfac = function AnimationRendererFactory_Factory(t) {\n  return new (t || AnimationRendererFactory)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(i1.ɵAnimationEngine), i0.ɵɵinject(i0.NgZone));\n};\n\nAnimationRendererFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AnimationRendererFactory,\n  factory: AnimationRendererFactory.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AnimationRendererFactory, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: i0.RendererFactory2\n    }, {\n      type: i1.ɵAnimationEngine\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n\nclass BaseAnimationRenderer {\n  constructor(namespaceId, delegate, engine) {\n    this.namespaceId = namespaceId;\n    this.delegate = delegate;\n    this.engine = engine;\n    this.destroyNode = this.delegate.destroyNode ? n => delegate.destroyNode(n) : null;\n  }\n\n  get data() {\n    return this.delegate.data;\n  }\n\n  destroy() {\n    this.engine.destroy(this.namespaceId, this.delegate);\n    this.delegate.destroy();\n  }\n\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n    this.engine.onInsert(this.namespaceId, newChild, parent, false);\n  }\n\n  insertBefore(parent, newChild, refChild, isMove = true) {\n    this.delegate.insertBefore(parent, newChild, refChild); // If `isMove` true than we should animate this insert.\n\n    this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n  }\n\n  removeChild(parent, oldChild, isHostElement) {\n    this.engine.onRemove(this.namespaceId, oldChild, this.delegate, isHostElement);\n  }\n\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n      this.disableAnimations(el, !!value);\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n\n  listen(target, eventName, callback) {\n    return this.delegate.listen(target, eventName, callback);\n  }\n\n  disableAnimations(element, value) {\n    this.engine.disableAnimations(element, value);\n  }\n\n}\n\nclass AnimationRenderer extends BaseAnimationRenderer {\n  constructor(factory, namespaceId, delegate, engine) {\n    super(namespaceId, delegate, engine);\n    this.factory = factory;\n    this.namespaceId = namespaceId;\n  }\n\n  setProperty(el, name, value) {\n    if (name.charAt(0) == ANIMATION_PREFIX) {\n      if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n        value = value === undefined ? true : !!value;\n        this.disableAnimations(el, value);\n      } else {\n        this.engine.process(this.namespaceId, el, name.substr(1), value);\n      }\n    } else {\n      this.delegate.setProperty(el, name, value);\n    }\n  }\n\n  listen(target, eventName, callback) {\n    if (eventName.charAt(0) == ANIMATION_PREFIX) {\n      const element = resolveElementFromTarget(target);\n      let name = eventName.substr(1);\n      let phase = ''; // @listener.phase is for trigger animation callbacks\n      // @@listener is for animation builder callbacks\n\n      if (name.charAt(0) != ANIMATION_PREFIX) {\n        [name, phase] = parseTriggerCallbackName(name);\n      }\n\n      return this.engine.listen(this.namespaceId, element, name, phase, event => {\n        const countId = event['_data'] || -1;\n        this.factory.scheduleListenerCallback(countId, callback, event);\n      });\n    }\n\n    return this.delegate.listen(target, eventName, callback);\n  }\n\n}\n\nfunction resolveElementFromTarget(target) {\n  switch (target) {\n    case 'body':\n      return document.body;\n\n    case 'document':\n      return document;\n\n    case 'window':\n      return window;\n\n    default:\n      return target;\n  }\n}\n\nfunction parseTriggerCallbackName(triggerName) {\n  const dotIndex = triggerName.indexOf('.');\n  const trigger = triggerName.substring(0, dotIndex);\n  const phase = triggerName.substr(dotIndex + 1);\n  return [trigger, phase];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass InjectableAnimationEngine extends ɵAnimationEngine {\n  constructor(doc, driver, normalizer) {\n    super(doc.body, driver, normalizer);\n  }\n\n  ngOnDestroy() {\n    this.flush();\n  }\n\n}\n\nInjectableAnimationEngine.ɵfac = function InjectableAnimationEngine_Factory(t) {\n  return new (t || InjectableAnimationEngine)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1.AnimationDriver), i0.ɵɵinject(i1.ɵAnimationStyleNormalizer));\n};\n\nInjectableAnimationEngine.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: InjectableAnimationEngine,\n  factory: InjectableAnimationEngine.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InjectableAnimationEngine, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1.AnimationDriver\n    }, {\n      type: i1.ɵAnimationStyleNormalizer\n    }];\n  }, null);\n})();\n\nfunction instantiateSupportedAnimationDriver() {\n  return ɵsupportsWebAnimations() ? new ɵWebAnimationsDriver() : new ɵCssKeyframesDriver();\n}\n\nfunction instantiateDefaultStyleNormalizer() {\n  return new ɵWebAnimationsStyleNormalizer();\n}\n\nfunction instantiateRendererFactory(renderer, engine, zone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\n/**\n * @publicApi\n */\n\n\nconst ANIMATION_MODULE_TYPE = new InjectionToken('AnimationModuleType');\nconst SHARED_ANIMATION_PROVIDERS = [{\n  provide: AnimationBuilder,\n  useClass: BrowserAnimationBuilder\n}, {\n  provide: ɵAnimationStyleNormalizer,\n  useFactory: instantiateDefaultStyleNormalizer\n}, {\n  provide: ɵAnimationEngine,\n  useClass: InjectableAnimationEngine\n}, {\n  provide: RendererFactory2,\n  useFactory: instantiateRendererFactory,\n  deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n}];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\n\nconst BROWSER_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useFactory: instantiateSupportedAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'BrowserAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\n\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [{\n  provide: AnimationDriver,\n  useClass: ɵNoopAnimationDriver\n}, {\n  provide: ANIMATION_MODULE_TYPE,\n  useValue: 'NoopAnimations'\n}, ...SHARED_ANIMATION_PROVIDERS];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\n\nclass BrowserAnimationsModule {\n  /**\n   * Configures the module based on the specified object.\n   *\n   * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n   * @see `BrowserAnimationsModuleConfig`\n   *\n   * @usageNotes\n   * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n   * function as follows:\n   * ```\n   * @NgModule({\n   *   imports: [BrowserAnimationsModule.withConfig(config)]\n   * })\n   * class MyNgModule {}\n   * ```\n   */\n  static withConfig(config) {\n    return {\n      ngModule: BrowserAnimationsModule,\n      providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS : BROWSER_ANIMATIONS_PROVIDERS\n    };\n  }\n\n}\n\nBrowserAnimationsModule.ɵfac = function BrowserAnimationsModule_Factory(t) {\n  return new (t || BrowserAnimationsModule)();\n};\n\nBrowserAnimationsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserAnimationsModule\n});\nBrowserAnimationsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: BROWSER_ANIMATIONS_PROVIDERS,\n  imports: [BrowserModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\n\n\nclass NoopAnimationsModule {}\n\nNoopAnimationsModule.ɵfac = function NoopAnimationsModule_Factory(t) {\n  return new (t || NoopAnimationsModule)();\n};\n\nNoopAnimationsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NoopAnimationsModule\n});\nNoopAnimationsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n  imports: [BrowserModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NoopAnimationsModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserModule],\n      providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ANIMATION_MODULE_TYPE, BrowserAnimationsModule, NoopAnimationsModule, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/platform-browser/fesm2020/animations.mjs"], "names": ["i0", "ViewEncapsulation", "Injectable", "Inject", "InjectionToken", "RendererFactory2", "NgZone", "NgModule", "ɵDomRendererFactory2", "BrowserModule", "AnimationBuilder", "sequence", "AnimationFactory", "i1", "ɵAnimationEngine", "ɵsupportsWebAnimations", "ɵWebAnimationsDriver", "ɵCssKeyframesDriver", "ɵWebAnimationsStyleNormalizer", "ɵAnimationStyleNormalizer", "AnimationDriver", "ɵNoopAnimationDriver", "DOCUMENT", "BrowserAnimationBuilder", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "doc", "_nextAnimationId", "typeData", "id", "encapsulation", "None", "styles", "data", "animation", "_renderer", "<PERSON><PERSON><PERSON><PERSON>", "body", "build", "toString", "entry", "Array", "isArray", "issueAnimationCommand", "BrowserAnimationFactory", "ɵfac", "ɵprov", "type", "undefined", "decorators", "args", "_id", "create", "element", "options", "RendererAnimationPlayer", "parentPlayer", "_started", "totalTime", "_command", "_listen", "eventName", "callback", "listen", "command", "onDone", "fn", "onStart", "onDestroy", "init", "hasStarted", "play", "pause", "restart", "finish", "destroy", "reset", "setPosition", "p", "getPosition", "engine", "players", "renderer", "setProperty", "ANIMATION_PREFIX", "DISABLE_ANIMATIONS_FLAG", "AnimationRendererFactory", "delegate", "_zone", "_currentId", "_microtaskId", "_animationCallbacksBuffer", "_rendererCache", "Map", "_cdRecurDepth", "promise", "Promise", "resolve", "onRemovalComplete", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "hostElement", "EMPTY_NAMESPACE_ID", "get", "BaseAnimationRenderer", "set", "componentId", "namespaceId", "register", "registerTrigger", "trigger", "for<PERSON>ach", "name", "animationTriggers", "<PERSON><PERSON><PERSON><PERSON>", "begin", "_scheduleCountTask", "then", "scheduleListenerCallback", "count", "run", "length", "tuple", "push", "end", "runOutsideAngular", "flush", "whenRenderingDone", "destroyNode", "n", "createElement", "namespace", "createComment", "value", "createText", "append<PERSON><PERSON><PERSON>", "parent", "<PERSON><PERSON><PERSON><PERSON>", "onInsert", "insertBefore", "refChild", "isMove", "<PERSON><PERSON><PERSON><PERSON>", "isHostElement", "onRemove", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "node", "nextS<PERSON>ling", "setAttribute", "el", "removeAttribute", "addClass", "removeClass", "setStyle", "style", "flags", "removeStyle", "char<PERSON>t", "disableAnimations", "setValue", "target", "factory", "process", "substr", "resolveElementFromTarget", "phase", "parseTriggerCallbackName", "event", "countId", "document", "window", "triggerName", "dotIndex", "indexOf", "substring", "InjectableAnimationEngine", "driver", "normalizer", "ngOnDestroy", "instantiateSupportedAnimationDriver", "instantiateDefaultStyleNormalizer", "instantiateRendererFactory", "zone", "ANIMATION_MODULE_TYPE", "SHARED_ANIMATION_PROVIDERS", "provide", "useClass", "useFactory", "deps", "BROWSER_ANIMATIONS_PROVIDERS", "useValue", "BROWSER_NOOP_ANIMATIONS_PROVIDERS", "BrowserAnimationsModule", "withConfig", "config", "ngModule", "providers", "ɵmod", "ɵinj", "exports", "NoopAnimationsModule", "ɵAnimationRenderer", "ɵAnimationRendererFactory", "ɵBrowserAnimationBuilder", "ɵBrowserAnimationFactory", "ɵInjectableAnimationEngine"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,iBAAT,EAA4BC,UAA5B,EAAwCC,MAAxC,EAAgDC,cAAhD,EAAgEC,gBAAhE,EAAkFC,MAAlF,EAA0FC,QAA1F,QAA0G,eAA1G;AACA,SAASC,oBAAT,EAA+BC,aAA/B,QAAoD,2BAApD;AACA,SAASC,gBAAT,EAA2BC,QAA3B,EAAqCC,gBAArC,QAA6D,qBAA7D;AACA,OAAO,KAAKC,EAAZ,MAAoB,6BAApB;AACA,SAASC,gBAAT,EAA2BC,sBAA3B,EAAmDC,oBAAnD,EAAyEC,mBAAzE,EAA8FC,6BAA9F,EAA6HC,yBAA7H,EAAwJC,eAAxJ,EAAyKC,oBAAzK,QAAqM,6BAArM;AACA,SAASC,QAAT,QAAyB,iBAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,uBAAN,SAAsCb,gBAAtC,CAAuD;AACnDc,EAAAA,WAAW,CAACC,YAAD,EAAeC,GAAf,EAAoB;AAC3B;AACA,SAAKC,gBAAL,GAAwB,CAAxB;AACA,UAAMC,QAAQ,GAAG;AAAEC,MAAAA,EAAE,EAAE,GAAN;AAAWC,MAAAA,aAAa,EAAE7B,iBAAiB,CAAC8B,IAA5C;AAAkDC,MAAAA,MAAM,EAAE,EAA1D;AAA8DC,MAAAA,IAAI,EAAE;AAAEC,QAAAA,SAAS,EAAE;AAAb;AAApE,KAAjB;AACA,SAAKC,SAAL,GAAiBV,YAAY,CAACW,cAAb,CAA4BV,GAAG,CAACW,IAAhC,EAAsCT,QAAtC,CAAjB;AACH;;AACDU,EAAAA,KAAK,CAACJ,SAAD,EAAY;AACb,UAAML,EAAE,GAAG,KAAKF,gBAAL,CAAsBY,QAAtB,EAAX;;AACA,SAAKZ,gBAAL;AACA,UAAMa,KAAK,GAAGC,KAAK,CAACC,OAAN,CAAcR,SAAd,IAA2BvB,QAAQ,CAACuB,SAAD,CAAnC,GAAiDA,SAA/D;AACAS,IAAAA,qBAAqB,CAAC,KAAKR,SAAN,EAAiB,IAAjB,EAAuBN,EAAvB,EAA2B,UAA3B,EAAuC,CAACW,KAAD,CAAvC,CAArB;AACA,WAAO,IAAII,uBAAJ,CAA4Bf,EAA5B,EAAgC,KAAKM,SAArC,CAAP;AACH;;AAbkD;;AAevDZ,uBAAuB,CAACsB,IAAxB;AAAA,mBAAoHtB,uBAApH,EAA0GvB,EAA1G,UAA6JA,EAAE,CAACK,gBAAhK,GAA0GL,EAA1G,UAA6LsB,QAA7L;AAAA;;AACAC,uBAAuB,CAACuB,KAAxB,kBAD0G9C,EAC1G;AAAA,SAAwHuB,uBAAxH;AAAA,WAAwHA,uBAAxH;AAAA;;AACA;AAAA,qDAF0GvB,EAE1G,mBAA2FuB,uBAA3F,EAAgI,CAAC;AACrHwB,IAAAA,IAAI,EAAE7C;AAD+G,GAAD,CAAhI,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE6C,MAAAA,IAAI,EAAE/C,EAAE,CAACK;AAAX,KAAD,EAAgC;AAAE0C,MAAAA,IAAI,EAAEC,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC7FF,QAAAA,IAAI,EAAE5C,MADuF;AAE7F+C,QAAAA,IAAI,EAAE,CAAC5B,QAAD;AAFuF,OAAD;AAA/B,KAAhC,CAAP;AAGlB,GALxB;AAAA;;AAMA,MAAMsB,uBAAN,SAAsChC,gBAAtC,CAAuD;AACnDY,EAAAA,WAAW,CAAC2B,GAAD,EAAMhB,SAAN,EAAiB;AACxB;AACA,SAAKgB,GAAL,GAAWA,GAAX;AACA,SAAKhB,SAAL,GAAiBA,SAAjB;AACH;;AACDiB,EAAAA,MAAM,CAACC,OAAD,EAAUC,OAAV,EAAmB;AACrB,WAAO,IAAIC,uBAAJ,CAA4B,KAAKJ,GAAjC,EAAsCE,OAAtC,EAA+CC,OAAO,IAAI,EAA1D,EAA8D,KAAKnB,SAAnE,CAAP;AACH;;AARkD;;AAUvD,MAAMoB,uBAAN,CAA8B;AAC1B/B,EAAAA,WAAW,CAACK,EAAD,EAAKwB,OAAL,EAAcC,OAAd,EAAuBnB,SAAvB,EAAkC;AACzC,SAAKN,EAAL,GAAUA,EAAV;AACA,SAAKwB,OAAL,GAAeA,OAAf;AACA,SAAKlB,SAAL,GAAiBA,SAAjB;AACA,SAAKqB,YAAL,GAAoB,IAApB;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKC,SAAL,GAAiB,CAAjB;;AACA,SAAKC,QAAL,CAAc,QAAd,EAAwBL,OAAxB;AACH;;AACDM,EAAAA,OAAO,CAACC,SAAD,EAAYC,QAAZ,EAAsB;AACzB,WAAO,KAAK3B,SAAL,CAAe4B,MAAf,CAAsB,KAAKV,OAA3B,EAAqC,KAAI,KAAKxB,EAAG,IAAGgC,SAAU,EAA9D,EAAiEC,QAAjE,CAAP;AACH;;AACDH,EAAAA,QAAQ,CAACK,OAAD,EAAU,GAAGd,IAAb,EAAmB;AACvB,WAAOP,qBAAqB,CAAC,KAAKR,SAAN,EAAiB,KAAKkB,OAAtB,EAA+B,KAAKxB,EAApC,EAAwCmC,OAAxC,EAAiDd,IAAjD,CAA5B;AACH;;AACDe,EAAAA,MAAM,CAACC,EAAD,EAAK;AACP,SAAKN,OAAL,CAAa,MAAb,EAAqBM,EAArB;AACH;;AACDC,EAAAA,OAAO,CAACD,EAAD,EAAK;AACR,SAAKN,OAAL,CAAa,OAAb,EAAsBM,EAAtB;AACH;;AACDE,EAAAA,SAAS,CAACF,EAAD,EAAK;AACV,SAAKN,OAAL,CAAa,SAAb,EAAwBM,EAAxB;AACH;;AACDG,EAAAA,IAAI,GAAG;AACH,SAAKV,QAAL,CAAc,MAAd;AACH;;AACDW,EAAAA,UAAU,GAAG;AACT,WAAO,KAAKb,QAAZ;AACH;;AACDc,EAAAA,IAAI,GAAG;AACH,SAAKZ,QAAL,CAAc,MAAd;;AACA,SAAKF,QAAL,GAAgB,IAAhB;AACH;;AACDe,EAAAA,KAAK,GAAG;AACJ,SAAKb,QAAL,CAAc,OAAd;AACH;;AACDc,EAAAA,OAAO,GAAG;AACN,SAAKd,QAAL,CAAc,SAAd;AACH;;AACDe,EAAAA,MAAM,GAAG;AACL,SAAKf,QAAL,CAAc,QAAd;AACH;;AACDgB,EAAAA,OAAO,GAAG;AACN,SAAKhB,QAAL,CAAc,SAAd;AACH;;AACDiB,EAAAA,KAAK,GAAG;AACJ,SAAKjB,QAAL,CAAc,OAAd;;AACA,SAAKF,QAAL,GAAgB,KAAhB;AACH;;AACDoB,EAAAA,WAAW,CAACC,CAAD,EAAI;AACX,SAAKnB,QAAL,CAAc,aAAd,EAA6BmB,CAA7B;AACH;;AACDC,EAAAA,WAAW,GAAG;AACV,WAAO,KAAK5C,SAAL,CAAe6C,MAAf,CAAsBC,OAAtB,CAA8B,CAAC,KAAKpD,EAApC,GAAyCkD,WAAzC,MAA0D,CAAjE;AACH;;AAxDyB;;AA0D9B,SAASpC,qBAAT,CAA+BuC,QAA/B,EAAyC7B,OAAzC,EAAkDxB,EAAlD,EAAsDmC,OAAtD,EAA+Dd,IAA/D,EAAqE;AACjE,SAAOgC,QAAQ,CAACC,WAAT,CAAqB9B,OAArB,EAA+B,KAAIxB,EAAG,IAAGmC,OAAQ,EAAjD,EAAoDd,IAApD,CAAP;AACH;;AAED,MAAMkC,gBAAgB,GAAG,GAAzB;AACA,MAAMC,uBAAuB,GAAG,YAAhC;;AACA,MAAMC,wBAAN,CAA+B;AAC3B9D,EAAAA,WAAW,CAAC+D,QAAD,EAAWP,MAAX,EAAmBQ,KAAnB,EAA0B;AACjC,SAAKD,QAAL,GAAgBA,QAAhB;AACA,SAAKP,MAAL,GAAcA,MAAd;AACA,SAAKQ,KAAL,GAAaA,KAAb;AACA,SAAKC,UAAL,GAAkB,CAAlB;AACA,SAAKC,YAAL,GAAoB,CAApB;AACA,SAAKC,yBAAL,GAAiC,EAAjC;AACA,SAAKC,cAAL,GAAsB,IAAIC,GAAJ,EAAtB;AACA,SAAKC,aAAL,GAAqB,CAArB;AACA,SAAKC,OAAL,GAAeC,OAAO,CAACC,OAAR,CAAgB,CAAhB,CAAf;;AACAjB,IAAAA,MAAM,CAACkB,iBAAP,GAA2B,CAAC7C,OAAD,EAAUkC,QAAV,KAAuB;AAC9C;AACA;AACA;AACA;AACA,YAAMY,UAAU,GAAGZ,QAAQ,EAAEY,UAAV,CAAqB9C,OAArB,CAAnB;;AACA,UAAI8C,UAAJ,EAAgB;AACZZ,QAAAA,QAAQ,CAACa,WAAT,CAAqBD,UAArB,EAAiC9C,OAAjC;AACH;AACJ,KATD;AAUH;;AACDjB,EAAAA,cAAc,CAACiE,WAAD,EAActD,IAAd,EAAoB;AAC9B,UAAMuD,kBAAkB,GAAG,EAA3B,CAD8B,CAE9B;AACA;;AACA,UAAMf,QAAQ,GAAG,KAAKA,QAAL,CAAcnD,cAAd,CAA6BiE,WAA7B,EAA0CtD,IAA1C,CAAjB;;AACA,QAAI,CAACsD,WAAD,IAAgB,CAACtD,IAAjB,IAAyB,CAACA,IAAI,CAACd,IAA/B,IAAuC,CAACc,IAAI,CAACd,IAAL,CAAU,WAAV,CAA5C,EAAoE;AAChE,UAAIiD,QAAQ,GAAG,KAAKU,cAAL,CAAoBW,GAApB,CAAwBhB,QAAxB,CAAf;;AACA,UAAI,CAACL,QAAL,EAAe;AACXA,QAAAA,QAAQ,GAAG,IAAIsB,qBAAJ,CAA0BF,kBAA1B,EAA8Cf,QAA9C,EAAwD,KAAKP,MAA7D,CAAX,CADW,CAEX;;AACA,aAAKY,cAAL,CAAoBa,GAApB,CAAwBlB,QAAxB,EAAkCL,QAAlC;AACH;;AACD,aAAOA,QAAP;AACH;;AACD,UAAMwB,WAAW,GAAG3D,IAAI,CAAClB,EAAzB;AACA,UAAM8E,WAAW,GAAG5D,IAAI,CAAClB,EAAL,GAAU,GAAV,GAAgB,KAAK4D,UAAzC;AACA,SAAKA,UAAL;AACA,SAAKT,MAAL,CAAY4B,QAAZ,CAAqBD,WAArB,EAAkCN,WAAlC;;AACA,UAAMQ,eAAe,GAAIC,OAAD,IAAa;AACjC,UAAIrE,KAAK,CAACC,OAAN,CAAcoE,OAAd,CAAJ,EAA4B;AACxBA,QAAAA,OAAO,CAACC,OAAR,CAAgBF,eAAhB;AACH,OAFD,MAGK;AACD,aAAK7B,MAAL,CAAY6B,eAAZ,CAA4BH,WAA5B,EAAyCC,WAAzC,EAAsDN,WAAtD,EAAmES,OAAO,CAACE,IAA3E,EAAiFF,OAAjF;AACH;AACJ,KAPD;;AAQA,UAAMG,iBAAiB,GAAGlE,IAAI,CAACd,IAAL,CAAU,WAAV,CAA1B;AACAgF,IAAAA,iBAAiB,CAACF,OAAlB,CAA0BF,eAA1B;AACA,WAAO,IAAIK,iBAAJ,CAAsB,IAAtB,EAA4BP,WAA5B,EAAyCpB,QAAzC,EAAmD,KAAKP,MAAxD,CAAP;AACH;;AACDmC,EAAAA,KAAK,GAAG;AACJ,SAAKrB,aAAL;;AACA,QAAI,KAAKP,QAAL,CAAc4B,KAAlB,EAAyB;AACrB,WAAK5B,QAAL,CAAc4B,KAAd;AACH;AACJ;;AACDC,EAAAA,kBAAkB,GAAG;AACjB;AACA,SAAKrB,OAAL,CAAasB,IAAb,CAAkB,MAAM;AACpB,WAAK3B,YAAL;AACH,KAFD;AAGH;AACD;;;AACA4B,EAAAA,wBAAwB,CAACC,KAAD,EAAQrD,EAAR,EAAYjC,IAAZ,EAAkB;AACtC,QAAIsF,KAAK,IAAI,CAAT,IAAcA,KAAK,GAAG,KAAK7B,YAA/B,EAA6C;AACzC,WAAKF,KAAL,CAAWgC,GAAX,CAAe,MAAMtD,EAAE,CAACjC,IAAD,CAAvB;;AACA;AACH;;AACD,QAAI,KAAK0D,yBAAL,CAA+B8B,MAA/B,IAAyC,CAA7C,EAAgD;AAC5CzB,MAAAA,OAAO,CAACC,OAAR,CAAgB,IAAhB,EAAsBoB,IAAtB,CAA2B,MAAM;AAC7B,aAAK7B,KAAL,CAAWgC,GAAX,CAAe,MAAM;AACjB,eAAK7B,yBAAL,CAA+BoB,OAA/B,CAAuCW,KAAK,IAAI;AAC5C,kBAAM,CAACxD,EAAD,EAAKjC,IAAL,IAAayF,KAAnB;AACAxD,YAAAA,EAAE,CAACjC,IAAD,CAAF;AACH,WAHD;;AAIA,eAAK0D,yBAAL,GAAiC,EAAjC;AACH,SAND;AAOH,OARD;AASH;;AACD,SAAKA,yBAAL,CAA+BgC,IAA/B,CAAoC,CAACzD,EAAD,EAAKjC,IAAL,CAApC;AACH;;AACD2F,EAAAA,GAAG,GAAG;AACF,SAAK9B,aAAL,GADE,CAEF;AACA;;AACA,QAAI,KAAKA,aAAL,IAAsB,CAA1B,EAA6B;AACzB,WAAKN,KAAL,CAAWqC,iBAAX,CAA6B,MAAM;AAC/B,aAAKT,kBAAL;;AACA,aAAKpC,MAAL,CAAY8C,KAAZ,CAAkB,KAAKpC,YAAvB;AACH,OAHD;AAIH;;AACD,QAAI,KAAKH,QAAL,CAAcqC,GAAlB,EAAuB;AACnB,WAAKrC,QAAL,CAAcqC,GAAd;AACH;AACJ;;AACDG,EAAAA,iBAAiB,GAAG;AAChB,WAAO,KAAK/C,MAAL,CAAY+C,iBAAZ,EAAP;AACH;;AAnG0B;;AAqG/BzC,wBAAwB,CAACzC,IAAzB;AAAA,mBAAqHyC,wBAArH,EAvL0GtF,EAuL1G,UAA+JA,EAAE,CAACK,gBAAlK,GAvL0GL,EAuL1G,UAA+La,EAAE,CAACC,gBAAlM,GAvL0Gd,EAuL1G,UAA+NA,EAAE,CAACM,MAAlO;AAAA;;AACAgF,wBAAwB,CAACxC,KAAzB,kBAxL0G9C,EAwL1G;AAAA,SAAyHsF,wBAAzH;AAAA,WAAyHA,wBAAzH;AAAA;;AACA;AAAA,qDAzL0GtF,EAyL1G,mBAA2FsF,wBAA3F,EAAiI,CAAC;AACtHvC,IAAAA,IAAI,EAAE7C;AADgH,GAAD,CAAjI,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE6C,MAAAA,IAAI,EAAE/C,EAAE,CAACK;AAAX,KAAD,EAAgC;AAAE0C,MAAAA,IAAI,EAAElC,EAAE,CAACC;AAAX,KAAhC,EAA+D;AAAEiC,MAAAA,IAAI,EAAE/C,EAAE,CAACM;AAAX,KAA/D,CAAP;AAA6F,GAFvI;AAAA;;AAGA,MAAMkG,qBAAN,CAA4B;AACxBhF,EAAAA,WAAW,CAACmF,WAAD,EAAcpB,QAAd,EAAwBP,MAAxB,EAAgC;AACvC,SAAK2B,WAAL,GAAmBA,WAAnB;AACA,SAAKpB,QAAL,GAAgBA,QAAhB;AACA,SAAKP,MAAL,GAAcA,MAAd;AACA,SAAKgD,WAAL,GAAmB,KAAKzC,QAAL,CAAcyC,WAAd,GAA6BC,CAAD,IAAO1C,QAAQ,CAACyC,WAAT,CAAqBC,CAArB,CAAnC,GAA6D,IAAhF;AACH;;AACO,MAAJhG,IAAI,GAAG;AACP,WAAO,KAAKsD,QAAL,CAActD,IAArB;AACH;;AACD0C,EAAAA,OAAO,GAAG;AACN,SAAKK,MAAL,CAAYL,OAAZ,CAAoB,KAAKgC,WAAzB,EAAsC,KAAKpB,QAA3C;AACA,SAAKA,QAAL,CAAcZ,OAAd;AACH;;AACDuD,EAAAA,aAAa,CAAClB,IAAD,EAAOmB,SAAP,EAAkB;AAC3B,WAAO,KAAK5C,QAAL,CAAc2C,aAAd,CAA4BlB,IAA5B,EAAkCmB,SAAlC,CAAP;AACH;;AACDC,EAAAA,aAAa,CAACC,KAAD,EAAQ;AACjB,WAAO,KAAK9C,QAAL,CAAc6C,aAAd,CAA4BC,KAA5B,CAAP;AACH;;AACDC,EAAAA,UAAU,CAACD,KAAD,EAAQ;AACd,WAAO,KAAK9C,QAAL,CAAc+C,UAAd,CAAyBD,KAAzB,CAAP;AACH;;AACDE,EAAAA,WAAW,CAACC,MAAD,EAASC,QAAT,EAAmB;AAC1B,SAAKlD,QAAL,CAAcgD,WAAd,CAA0BC,MAA1B,EAAkCC,QAAlC;AACA,SAAKzD,MAAL,CAAY0D,QAAZ,CAAqB,KAAK/B,WAA1B,EAAuC8B,QAAvC,EAAiDD,MAAjD,EAAyD,KAAzD;AACH;;AACDG,EAAAA,YAAY,CAACH,MAAD,EAASC,QAAT,EAAmBG,QAAnB,EAA6BC,MAAM,GAAG,IAAtC,EAA4C;AACpD,SAAKtD,QAAL,CAAcoD,YAAd,CAA2BH,MAA3B,EAAmCC,QAAnC,EAA6CG,QAA7C,EADoD,CAEpD;;AACA,SAAK5D,MAAL,CAAY0D,QAAZ,CAAqB,KAAK/B,WAA1B,EAAuC8B,QAAvC,EAAiDD,MAAjD,EAAyDK,MAAzD;AACH;;AACDzC,EAAAA,WAAW,CAACoC,MAAD,EAASM,QAAT,EAAmBC,aAAnB,EAAkC;AACzC,SAAK/D,MAAL,CAAYgE,QAAZ,CAAqB,KAAKrC,WAA1B,EAAuCmC,QAAvC,EAAiD,KAAKvD,QAAtD,EAAgEwD,aAAhE;AACH;;AACDE,EAAAA,iBAAiB,CAACC,cAAD,EAAiBC,eAAjB,EAAkC;AAC/C,WAAO,KAAK5D,QAAL,CAAc0D,iBAAd,CAAgCC,cAAhC,EAAgDC,eAAhD,CAAP;AACH;;AACDhD,EAAAA,UAAU,CAACiD,IAAD,EAAO;AACb,WAAO,KAAK7D,QAAL,CAAcY,UAAd,CAAyBiD,IAAzB,CAAP;AACH;;AACDC,EAAAA,WAAW,CAACD,IAAD,EAAO;AACd,WAAO,KAAK7D,QAAL,CAAc8D,WAAd,CAA0BD,IAA1B,CAAP;AACH;;AACDE,EAAAA,YAAY,CAACC,EAAD,EAAKvC,IAAL,EAAWqB,KAAX,EAAkBF,SAAlB,EAA6B;AACrC,SAAK5C,QAAL,CAAc+D,YAAd,CAA2BC,EAA3B,EAA+BvC,IAA/B,EAAqCqB,KAArC,EAA4CF,SAA5C;AACH;;AACDqB,EAAAA,eAAe,CAACD,EAAD,EAAKvC,IAAL,EAAWmB,SAAX,EAAsB;AACjC,SAAK5C,QAAL,CAAciE,eAAd,CAA8BD,EAA9B,EAAkCvC,IAAlC,EAAwCmB,SAAxC;AACH;;AACDsB,EAAAA,QAAQ,CAACF,EAAD,EAAKvC,IAAL,EAAW;AACf,SAAKzB,QAAL,CAAckE,QAAd,CAAuBF,EAAvB,EAA2BvC,IAA3B;AACH;;AACD0C,EAAAA,WAAW,CAACH,EAAD,EAAKvC,IAAL,EAAW;AAClB,SAAKzB,QAAL,CAAcmE,WAAd,CAA0BH,EAA1B,EAA8BvC,IAA9B;AACH;;AACD2C,EAAAA,QAAQ,CAACJ,EAAD,EAAKK,KAAL,EAAYvB,KAAZ,EAAmBwB,KAAnB,EAA0B;AAC9B,SAAKtE,QAAL,CAAcoE,QAAd,CAAuBJ,EAAvB,EAA2BK,KAA3B,EAAkCvB,KAAlC,EAAyCwB,KAAzC;AACH;;AACDC,EAAAA,WAAW,CAACP,EAAD,EAAKK,KAAL,EAAYC,KAAZ,EAAmB;AAC1B,SAAKtE,QAAL,CAAcuE,WAAd,CAA0BP,EAA1B,EAA8BK,KAA9B,EAAqCC,KAArC;AACH;;AACD1E,EAAAA,WAAW,CAACoE,EAAD,EAAKvC,IAAL,EAAWqB,KAAX,EAAkB;AACzB,QAAIrB,IAAI,CAAC+C,MAAL,CAAY,CAAZ,KAAkB3E,gBAAlB,IAAsC4B,IAAI,IAAI3B,uBAAlD,EAA2E;AACvE,WAAK2E,iBAAL,CAAuBT,EAAvB,EAA2B,CAAC,CAAClB,KAA7B;AACH,KAFD,MAGK;AACD,WAAK9C,QAAL,CAAcJ,WAAd,CAA0BoE,EAA1B,EAA8BvC,IAA9B,EAAoCqB,KAApC;AACH;AACJ;;AACD4B,EAAAA,QAAQ,CAACb,IAAD,EAAOf,KAAP,EAAc;AAClB,SAAK9C,QAAL,CAAc0E,QAAd,CAAuBb,IAAvB,EAA6Bf,KAA7B;AACH;;AACDtE,EAAAA,MAAM,CAACmG,MAAD,EAASrG,SAAT,EAAoBC,QAApB,EAA8B;AAChC,WAAO,KAAKyB,QAAL,CAAcxB,MAAd,CAAqBmG,MAArB,EAA6BrG,SAA7B,EAAwCC,QAAxC,CAAP;AACH;;AACDkG,EAAAA,iBAAiB,CAAC3G,OAAD,EAAUgF,KAAV,EAAiB;AAC9B,SAAKrD,MAAL,CAAYgF,iBAAZ,CAA8B3G,OAA9B,EAAuCgF,KAAvC;AACH;;AA9EuB;;AAgF5B,MAAMnB,iBAAN,SAAgCV,qBAAhC,CAAsD;AAClDhF,EAAAA,WAAW,CAAC2I,OAAD,EAAUxD,WAAV,EAAuBpB,QAAvB,EAAiCP,MAAjC,EAAyC;AAChD,UAAM2B,WAAN,EAAmBpB,QAAnB,EAA6BP,MAA7B;AACA,SAAKmF,OAAL,GAAeA,OAAf;AACA,SAAKxD,WAAL,GAAmBA,WAAnB;AACH;;AACDxB,EAAAA,WAAW,CAACoE,EAAD,EAAKvC,IAAL,EAAWqB,KAAX,EAAkB;AACzB,QAAIrB,IAAI,CAAC+C,MAAL,CAAY,CAAZ,KAAkB3E,gBAAtB,EAAwC;AACpC,UAAI4B,IAAI,CAAC+C,MAAL,CAAY,CAAZ,KAAkB,GAAlB,IAAyB/C,IAAI,IAAI3B,uBAArC,EAA8D;AAC1DgD,QAAAA,KAAK,GAAGA,KAAK,KAAKrF,SAAV,GAAsB,IAAtB,GAA6B,CAAC,CAACqF,KAAvC;AACA,aAAK2B,iBAAL,CAAuBT,EAAvB,EAA2BlB,KAA3B;AACH,OAHD,MAIK;AACD,aAAKrD,MAAL,CAAYoF,OAAZ,CAAoB,KAAKzD,WAAzB,EAAsC4C,EAAtC,EAA0CvC,IAAI,CAACqD,MAAL,CAAY,CAAZ,CAA1C,EAA0DhC,KAA1D;AACH;AACJ,KARD,MASK;AACD,WAAK9C,QAAL,CAAcJ,WAAd,CAA0BoE,EAA1B,EAA8BvC,IAA9B,EAAoCqB,KAApC;AACH;AACJ;;AACDtE,EAAAA,MAAM,CAACmG,MAAD,EAASrG,SAAT,EAAoBC,QAApB,EAA8B;AAChC,QAAID,SAAS,CAACkG,MAAV,CAAiB,CAAjB,KAAuB3E,gBAA3B,EAA6C;AACzC,YAAM/B,OAAO,GAAGiH,wBAAwB,CAACJ,MAAD,CAAxC;AACA,UAAIlD,IAAI,GAAGnD,SAAS,CAACwG,MAAV,CAAiB,CAAjB,CAAX;AACA,UAAIE,KAAK,GAAG,EAAZ,CAHyC,CAIzC;AACA;;AACA,UAAIvD,IAAI,CAAC+C,MAAL,CAAY,CAAZ,KAAkB3E,gBAAtB,EAAwC;AACpC,SAAC4B,IAAD,EAAOuD,KAAP,IAAgBC,wBAAwB,CAACxD,IAAD,CAAxC;AACH;;AACD,aAAO,KAAKhC,MAAL,CAAYjB,MAAZ,CAAmB,KAAK4C,WAAxB,EAAqCtD,OAArC,EAA8C2D,IAA9C,EAAoDuD,KAApD,EAA2DE,KAAK,IAAI;AACvE,cAAMC,OAAO,GAAGD,KAAK,CAAC,OAAD,CAAL,IAAkB,CAAC,CAAnC;AACA,aAAKN,OAAL,CAAa7C,wBAAb,CAAsCoD,OAAtC,EAA+C5G,QAA/C,EAAyD2G,KAAzD;AACH,OAHM,CAAP;AAIH;;AACD,WAAO,KAAKlF,QAAL,CAAcxB,MAAd,CAAqBmG,MAArB,EAA6BrG,SAA7B,EAAwCC,QAAxC,CAAP;AACH;;AApCiD;;AAsCtD,SAASwG,wBAAT,CAAkCJ,MAAlC,EAA0C;AACtC,UAAQA,MAAR;AACI,SAAK,MAAL;AACI,aAAOS,QAAQ,CAACtI,IAAhB;;AACJ,SAAK,UAAL;AACI,aAAOsI,QAAP;;AACJ,SAAK,QAAL;AACI,aAAOC,MAAP;;AACJ;AACI,aAAOV,MAAP;AARR;AAUH;;AACD,SAASM,wBAAT,CAAkCK,WAAlC,EAA+C;AAC3C,QAAMC,QAAQ,GAAGD,WAAW,CAACE,OAAZ,CAAoB,GAApB,CAAjB;AACA,QAAMjE,OAAO,GAAG+D,WAAW,CAACG,SAAZ,CAAsB,CAAtB,EAAyBF,QAAzB,CAAhB;AACA,QAAMP,KAAK,GAAGM,WAAW,CAACR,MAAZ,CAAmBS,QAAQ,GAAG,CAA9B,CAAd;AACA,SAAO,CAAChE,OAAD,EAAUyD,KAAV,CAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMU,yBAAN,SAAwCnK,gBAAxC,CAAyD;AACrDU,EAAAA,WAAW,CAACE,GAAD,EAAMwJ,MAAN,EAAcC,UAAd,EAA0B;AACjC,UAAMzJ,GAAG,CAACW,IAAV,EAAgB6I,MAAhB,EAAwBC,UAAxB;AACH;;AACDC,EAAAA,WAAW,GAAG;AACV,SAAKtD,KAAL;AACH;;AANoD;;AAQzDmD,yBAAyB,CAACpI,IAA1B;AAAA,mBAAsHoI,yBAAtH,EApV0GjL,EAoV1G,UAAiKsB,QAAjK,GApV0GtB,EAoV1G,UAAsLa,EAAE,CAACO,eAAzL,GApV0GpB,EAoV1G,UAAqNa,EAAE,CAACM,yBAAxN;AAAA;;AACA8J,yBAAyB,CAACnI,KAA1B,kBArV0G9C,EAqV1G;AAAA,SAA0HiL,yBAA1H;AAAA,WAA0HA,yBAA1H;AAAA;;AACA;AAAA,qDAtV0GjL,EAsV1G,mBAA2FiL,yBAA3F,EAAkI,CAAC;AACvHlI,IAAAA,IAAI,EAAE7C;AADiH,GAAD,CAAlI,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE6C,MAAAA,IAAI,EAAEC,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DF,QAAAA,IAAI,EAAE5C,MADwD;AAE9D+C,QAAAA,IAAI,EAAE,CAAC5B,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEyB,MAAAA,IAAI,EAAElC,EAAE,CAACO;AAAX,KAH2B,EAGG;AAAE2B,MAAAA,IAAI,EAAElC,EAAE,CAACM;AAAX,KAHH,CAAP;AAGoD,GAL9F;AAAA;;AAMA,SAASkK,mCAAT,GAA+C;AAC3C,SAAOtK,sBAAsB,KAAK,IAAIC,oBAAJ,EAAL,GAAkC,IAAIC,mBAAJ,EAA/D;AACH;;AACD,SAASqK,iCAAT,GAA6C;AACzC,SAAO,IAAIpK,6BAAJ,EAAP;AACH;;AACD,SAASqK,0BAAT,CAAoCrG,QAApC,EAA8CF,MAA9C,EAAsDwG,IAAtD,EAA4D;AACxD,SAAO,IAAIlG,wBAAJ,CAA6BJ,QAA7B,EAAuCF,MAAvC,EAA+CwG,IAA/C,CAAP;AACH;AACD;AACA;AACA;;;AACA,MAAMC,qBAAqB,GAAG,IAAIrL,cAAJ,CAAmB,qBAAnB,CAA9B;AACA,MAAMsL,0BAA0B,GAAG,CAC/B;AAAEC,EAAAA,OAAO,EAAEjL,gBAAX;AAA6BkL,EAAAA,QAAQ,EAAErK;AAAvC,CAD+B,EAE/B;AAAEoK,EAAAA,OAAO,EAAExK,yBAAX;AAAsC0K,EAAAA,UAAU,EAAEP;AAAlD,CAF+B,EAG/B;AAAEK,EAAAA,OAAO,EAAE7K,gBAAX;AAA6B8K,EAAAA,QAAQ,EAAEX;AAAvC,CAH+B,EAGqC;AAChEU,EAAAA,OAAO,EAAEtL,gBADuD;AAEhEwL,EAAAA,UAAU,EAAEN,0BAFoD;AAGhEO,EAAAA,IAAI,EAAE,CAACtL,oBAAD,EAAuBM,gBAAvB,EAAyCR,MAAzC;AAH0D,CAHrC,CAAnC;AASA;AACA;AACA;AACA;;AACA,MAAMyL,4BAA4B,GAAG,CACjC;AAAEJ,EAAAA,OAAO,EAAEvK,eAAX;AAA4ByK,EAAAA,UAAU,EAAER;AAAxC,CADiC,EAEjC;AAAEM,EAAAA,OAAO,EAAEF,qBAAX;AAAkCO,EAAAA,QAAQ,EAAE;AAA5C,CAFiC,EAEkC,GAAGN,0BAFrC,CAArC;AAIA;AACA;AACA;AACA;;AACA,MAAMO,iCAAiC,GAAG,CACtC;AAAEN,EAAAA,OAAO,EAAEvK,eAAX;AAA4BwK,EAAAA,QAAQ,EAAEvK;AAAtC,CADsC,EAEtC;AAAEsK,EAAAA,OAAO,EAAEF,qBAAX;AAAkCO,EAAAA,QAAQ,EAAE;AAA5C,CAFsC,EAE0B,GAAGN,0BAF7B,CAA1C;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMQ,uBAAN,CAA8B;AAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACqB,SAAVC,UAAU,CAACC,MAAD,EAAS;AACtB,WAAO;AACHC,MAAAA,QAAQ,EAAEH,uBADP;AAEHI,MAAAA,SAAS,EAAEF,MAAM,CAACpC,iBAAP,GAA2BiC,iCAA3B,GACPF;AAHD,KAAP;AAKH;;AAvByB;;AAyB9BG,uBAAuB,CAACrJ,IAAxB;AAAA,mBAAoHqJ,uBAApH;AAAA;;AACAA,uBAAuB,CAACK,IAAxB,kBAza0GvM,EAya1G;AAAA,QAAqHkM;AAArH;AACAA,uBAAuB,CAACM,IAAxB,kBA1a0GxM,EA0a1G;AAAA,aAAyJ+L,4BAAzJ;AAAA,YAAiMtL,aAAjM;AAAA;;AACA;AAAA,qDA3a0GT,EA2a1G,mBAA2FkM,uBAA3F,EAAgI,CAAC;AACrHnJ,IAAAA,IAAI,EAAExC,QAD+G;AAErH2C,IAAAA,IAAI,EAAE,CAAC;AACCuJ,MAAAA,OAAO,EAAE,CAAChM,aAAD,CADV;AAEC6L,MAAAA,SAAS,EAAEP;AAFZ,KAAD;AAF+G,GAAD,CAAhI;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMW,oBAAN,CAA2B;;AAE3BA,oBAAoB,CAAC7J,IAArB;AAAA,mBAAiH6J,oBAAjH;AAAA;;AACAA,oBAAoB,CAACH,IAArB,kBAzb0GvM,EAyb1G;AAAA,QAAkH0M;AAAlH;AACAA,oBAAoB,CAACF,IAArB,kBA1b0GxM,EA0b1G;AAAA,aAAmJiM,iCAAnJ;AAAA,YAAgMxL,aAAhM;AAAA;;AACA;AAAA,qDA3b0GT,EA2b1G,mBAA2F0M,oBAA3F,EAA6H,CAAC;AAClH3J,IAAAA,IAAI,EAAExC,QAD4G;AAElH2C,IAAAA,IAAI,EAAE,CAAC;AACCuJ,MAAAA,OAAO,EAAE,CAAChM,aAAD,CADV;AAEC6L,MAAAA,SAAS,EAAEL;AAFZ,KAAD;AAF4G,GAAD,CAA7H;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASR,qBAAT,EAAgCS,uBAAhC,EAAyDQ,oBAAzD,EAA+ExF,iBAAiB,IAAIyF,kBAApG,EAAwHrH,wBAAwB,IAAIsH,yBAApJ,EAA+KrL,uBAAuB,IAAIsL,wBAA1M,EAAoOjK,uBAAuB,IAAIkK,wBAA/P,EAAyR7B,yBAAyB,IAAI8B,0BAAtT", "sourcesContent": ["/**\n * @license Angular v13.0.3\n * (c) 2010-2021 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { ViewEncapsulation, Injectable, Inject, InjectionToken, RendererFactory2, NgZone, NgModule } from '@angular/core';\nimport { ɵDomRendererFactory2, BrowserModule } from '@angular/platform-browser';\nimport { AnimationBuilder, sequence, AnimationFactory } from '@angular/animations';\nimport * as i1 from '@angular/animations/browser';\nimport { ɵAnimationEngine, ɵsupportsWebAnimations, ɵWebAnimationsDriver, ɵCssKeyframesDriver, ɵWebAnimationsStyleNormalizer, ɵAnimationStyleNormalizer, AnimationDriver, ɵNoopAnimationDriver } from '@angular/animations/browser';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass BrowserAnimationBuilder extends AnimationBuilder {\n    constructor(rootRenderer, doc) {\n        super();\n        this._nextAnimationId = 0;\n        const typeData = { id: '0', encapsulation: ViewEncapsulation.None, styles: [], data: { animation: [] } };\n        this._renderer = rootRenderer.createRenderer(doc.body, typeData);\n    }\n    build(animation) {\n        const id = this._nextAnimationId.toString();\n        this._nextAnimationId++;\n        const entry = Array.isArray(animation) ? sequence(animation) : animation;\n        issueAnimationCommand(this._renderer, null, id, 'register', [entry]);\n        return new BrowserAnimationFactory(id, this._renderer);\n    }\n}\nBrowserAnimationBuilder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserAnimationBuilder, deps: [{ token: i0.RendererFactory2 }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nBrowserAnimationBuilder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserAnimationBuilder });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserAnimationBuilder, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.RendererFactory2 }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\nclass BrowserAnimationFactory extends AnimationFactory {\n    constructor(_id, _renderer) {\n        super();\n        this._id = _id;\n        this._renderer = _renderer;\n    }\n    create(element, options) {\n        return new RendererAnimationPlayer(this._id, element, options || {}, this._renderer);\n    }\n}\nclass RendererAnimationPlayer {\n    constructor(id, element, options, _renderer) {\n        this.id = id;\n        this.element = element;\n        this._renderer = _renderer;\n        this.parentPlayer = null;\n        this._started = false;\n        this.totalTime = 0;\n        this._command('create', options);\n    }\n    _listen(eventName, callback) {\n        return this._renderer.listen(this.element, `@@${this.id}:${eventName}`, callback);\n    }\n    _command(command, ...args) {\n        return issueAnimationCommand(this._renderer, this.element, this.id, command, args);\n    }\n    onDone(fn) {\n        this._listen('done', fn);\n    }\n    onStart(fn) {\n        this._listen('start', fn);\n    }\n    onDestroy(fn) {\n        this._listen('destroy', fn);\n    }\n    init() {\n        this._command('init');\n    }\n    hasStarted() {\n        return this._started;\n    }\n    play() {\n        this._command('play');\n        this._started = true;\n    }\n    pause() {\n        this._command('pause');\n    }\n    restart() {\n        this._command('restart');\n    }\n    finish() {\n        this._command('finish');\n    }\n    destroy() {\n        this._command('destroy');\n    }\n    reset() {\n        this._command('reset');\n        this._started = false;\n    }\n    setPosition(p) {\n        this._command('setPosition', p);\n    }\n    getPosition() {\n        return this._renderer.engine.players[+this.id]?.getPosition() ?? 0;\n    }\n}\nfunction issueAnimationCommand(renderer, element, id, command, args) {\n    return renderer.setProperty(element, `@@${id}:${command}`, args);\n}\n\nconst ANIMATION_PREFIX = '@';\nconst DISABLE_ANIMATIONS_FLAG = '@.disabled';\nclass AnimationRendererFactory {\n    constructor(delegate, engine, _zone) {\n        this.delegate = delegate;\n        this.engine = engine;\n        this._zone = _zone;\n        this._currentId = 0;\n        this._microtaskId = 1;\n        this._animationCallbacksBuffer = [];\n        this._rendererCache = new Map();\n        this._cdRecurDepth = 0;\n        this.promise = Promise.resolve(0);\n        engine.onRemovalComplete = (element, delegate) => {\n            // Note: if a component element has a leave animation, and a host leave animation,\n            // the view engine will call `removeChild` for the parent\n            // component renderer as well as for the child component renderer.\n            // Therefore, we need to check if we already removed the element.\n            const parentNode = delegate?.parentNode(element);\n            if (parentNode) {\n                delegate.removeChild(parentNode, element);\n            }\n        };\n    }\n    createRenderer(hostElement, type) {\n        const EMPTY_NAMESPACE_ID = '';\n        // cache the delegates to find out which cached delegate can\n        // be used by which cached renderer\n        const delegate = this.delegate.createRenderer(hostElement, type);\n        if (!hostElement || !type || !type.data || !type.data['animation']) {\n            let renderer = this._rendererCache.get(delegate);\n            if (!renderer) {\n                renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine);\n                // only cache this result when the base renderer is used\n                this._rendererCache.set(delegate, renderer);\n            }\n            return renderer;\n        }\n        const componentId = type.id;\n        const namespaceId = type.id + '-' + this._currentId;\n        this._currentId++;\n        this.engine.register(namespaceId, hostElement);\n        const registerTrigger = (trigger) => {\n            if (Array.isArray(trigger)) {\n                trigger.forEach(registerTrigger);\n            }\n            else {\n                this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);\n            }\n        };\n        const animationTriggers = type.data['animation'];\n        animationTriggers.forEach(registerTrigger);\n        return new AnimationRenderer(this, namespaceId, delegate, this.engine);\n    }\n    begin() {\n        this._cdRecurDepth++;\n        if (this.delegate.begin) {\n            this.delegate.begin();\n        }\n    }\n    _scheduleCountTask() {\n        // always use promise to schedule microtask instead of use Zone\n        this.promise.then(() => {\n            this._microtaskId++;\n        });\n    }\n    /** @internal */\n    scheduleListenerCallback(count, fn, data) {\n        if (count >= 0 && count < this._microtaskId) {\n            this._zone.run(() => fn(data));\n            return;\n        }\n        if (this._animationCallbacksBuffer.length == 0) {\n            Promise.resolve(null).then(() => {\n                this._zone.run(() => {\n                    this._animationCallbacksBuffer.forEach(tuple => {\n                        const [fn, data] = tuple;\n                        fn(data);\n                    });\n                    this._animationCallbacksBuffer = [];\n                });\n            });\n        }\n        this._animationCallbacksBuffer.push([fn, data]);\n    }\n    end() {\n        this._cdRecurDepth--;\n        // this is to prevent animations from running twice when an inner\n        // component does CD when a parent component instead has inserted it\n        if (this._cdRecurDepth == 0) {\n            this._zone.runOutsideAngular(() => {\n                this._scheduleCountTask();\n                this.engine.flush(this._microtaskId);\n            });\n        }\n        if (this.delegate.end) {\n            this.delegate.end();\n        }\n    }\n    whenRenderingDone() {\n        return this.engine.whenRenderingDone();\n    }\n}\nAnimationRendererFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: AnimationRendererFactory, deps: [{ token: i0.RendererFactory2 }, { token: i1.ɵAnimationEngine }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nAnimationRendererFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: AnimationRendererFactory });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: AnimationRendererFactory, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: i0.RendererFactory2 }, { type: i1.ɵAnimationEngine }, { type: i0.NgZone }]; } });\nclass BaseAnimationRenderer {\n    constructor(namespaceId, delegate, engine) {\n        this.namespaceId = namespaceId;\n        this.delegate = delegate;\n        this.engine = engine;\n        this.destroyNode = this.delegate.destroyNode ? (n) => delegate.destroyNode(n) : null;\n    }\n    get data() {\n        return this.delegate.data;\n    }\n    destroy() {\n        this.engine.destroy(this.namespaceId, this.delegate);\n        this.delegate.destroy();\n    }\n    createElement(name, namespace) {\n        return this.delegate.createElement(name, namespace);\n    }\n    createComment(value) {\n        return this.delegate.createComment(value);\n    }\n    createText(value) {\n        return this.delegate.createText(value);\n    }\n    appendChild(parent, newChild) {\n        this.delegate.appendChild(parent, newChild);\n        this.engine.onInsert(this.namespaceId, newChild, parent, false);\n    }\n    insertBefore(parent, newChild, refChild, isMove = true) {\n        this.delegate.insertBefore(parent, newChild, refChild);\n        // If `isMove` true than we should animate this insert.\n        this.engine.onInsert(this.namespaceId, newChild, parent, isMove);\n    }\n    removeChild(parent, oldChild, isHostElement) {\n        this.engine.onRemove(this.namespaceId, oldChild, this.delegate, isHostElement);\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n    }\n    parentNode(node) {\n        return this.delegate.parentNode(node);\n    }\n    nextSibling(node) {\n        return this.delegate.nextSibling(node);\n    }\n    setAttribute(el, name, value, namespace) {\n        this.delegate.setAttribute(el, name, value, namespace);\n    }\n    removeAttribute(el, name, namespace) {\n        this.delegate.removeAttribute(el, name, namespace);\n    }\n    addClass(el, name) {\n        this.delegate.addClass(el, name);\n    }\n    removeClass(el, name) {\n        this.delegate.removeClass(el, name);\n    }\n    setStyle(el, style, value, flags) {\n        this.delegate.setStyle(el, style, value, flags);\n    }\n    removeStyle(el, style, flags) {\n        this.delegate.removeStyle(el, style, flags);\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX && name == DISABLE_ANIMATIONS_FLAG) {\n            this.disableAnimations(el, !!value);\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    setValue(node, value) {\n        this.delegate.setValue(node, value);\n    }\n    listen(target, eventName, callback) {\n        return this.delegate.listen(target, eventName, callback);\n    }\n    disableAnimations(element, value) {\n        this.engine.disableAnimations(element, value);\n    }\n}\nclass AnimationRenderer extends BaseAnimationRenderer {\n    constructor(factory, namespaceId, delegate, engine) {\n        super(namespaceId, delegate, engine);\n        this.factory = factory;\n        this.namespaceId = namespaceId;\n    }\n    setProperty(el, name, value) {\n        if (name.charAt(0) == ANIMATION_PREFIX) {\n            if (name.charAt(1) == '.' && name == DISABLE_ANIMATIONS_FLAG) {\n                value = value === undefined ? true : !!value;\n                this.disableAnimations(el, value);\n            }\n            else {\n                this.engine.process(this.namespaceId, el, name.substr(1), value);\n            }\n        }\n        else {\n            this.delegate.setProperty(el, name, value);\n        }\n    }\n    listen(target, eventName, callback) {\n        if (eventName.charAt(0) == ANIMATION_PREFIX) {\n            const element = resolveElementFromTarget(target);\n            let name = eventName.substr(1);\n            let phase = '';\n            // @listener.phase is for trigger animation callbacks\n            // @@listener is for animation builder callbacks\n            if (name.charAt(0) != ANIMATION_PREFIX) {\n                [name, phase] = parseTriggerCallbackName(name);\n            }\n            return this.engine.listen(this.namespaceId, element, name, phase, event => {\n                const countId = event['_data'] || -1;\n                this.factory.scheduleListenerCallback(countId, callback, event);\n            });\n        }\n        return this.delegate.listen(target, eventName, callback);\n    }\n}\nfunction resolveElementFromTarget(target) {\n    switch (target) {\n        case 'body':\n            return document.body;\n        case 'document':\n            return document;\n        case 'window':\n            return window;\n        default:\n            return target;\n    }\n}\nfunction parseTriggerCallbackName(triggerName) {\n    const dotIndex = triggerName.indexOf('.');\n    const trigger = triggerName.substring(0, dotIndex);\n    const phase = triggerName.substr(dotIndex + 1);\n    return [trigger, phase];\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass InjectableAnimationEngine extends ɵAnimationEngine {\n    constructor(doc, driver, normalizer) {\n        super(doc.body, driver, normalizer);\n    }\n    ngOnDestroy() {\n        this.flush();\n    }\n}\nInjectableAnimationEngine.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: InjectableAnimationEngine, deps: [{ token: DOCUMENT }, { token: i1.AnimationDriver }, { token: i1.ɵAnimationStyleNormalizer }], target: i0.ɵɵFactoryTarget.Injectable });\nInjectableAnimationEngine.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: InjectableAnimationEngine });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: InjectableAnimationEngine, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1.AnimationDriver }, { type: i1.ɵAnimationStyleNormalizer }]; } });\nfunction instantiateSupportedAnimationDriver() {\n    return ɵsupportsWebAnimations() ? new ɵWebAnimationsDriver() : new ɵCssKeyframesDriver();\n}\nfunction instantiateDefaultStyleNormalizer() {\n    return new ɵWebAnimationsStyleNormalizer();\n}\nfunction instantiateRendererFactory(renderer, engine, zone) {\n    return new AnimationRendererFactory(renderer, engine, zone);\n}\n/**\n * @publicApi\n */\nconst ANIMATION_MODULE_TYPE = new InjectionToken('AnimationModuleType');\nconst SHARED_ANIMATION_PROVIDERS = [\n    { provide: AnimationBuilder, useClass: BrowserAnimationBuilder },\n    { provide: ɵAnimationStyleNormalizer, useFactory: instantiateDefaultStyleNormalizer },\n    { provide: ɵAnimationEngine, useClass: InjectableAnimationEngine }, {\n        provide: RendererFactory2,\n        useFactory: instantiateRendererFactory,\n        deps: [ɵDomRendererFactory2, ɵAnimationEngine, NgZone]\n    }\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nconst BROWSER_ANIMATIONS_PROVIDERS = [\n    { provide: AnimationDriver, useFactory: instantiateSupportedAnimationDriver },\n    { provide: ANIMATION_MODULE_TYPE, useValue: 'BrowserAnimations' }, ...SHARED_ANIMATION_PROVIDERS\n];\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nconst BROWSER_NOOP_ANIMATIONS_PROVIDERS = [\n    { provide: AnimationDriver, useClass: ɵNoopAnimationDriver },\n    { provide: ANIMATION_MODULE_TYPE, useValue: 'NoopAnimations' }, ...SHARED_ANIMATION_PROVIDERS\n];\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\nclass BrowserAnimationsModule {\n    /**\n     * Configures the module based on the specified object.\n     *\n     * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n     * @see `BrowserAnimationsModuleConfig`\n     *\n     * @usageNotes\n     * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n     * function as follows:\n     * ```\n     * @NgModule({\n     *   imports: [BrowserAnimationsModule.withConfig(config)]\n     * })\n     * class MyNgModule {}\n     * ```\n     */\n    static withConfig(config) {\n        return {\n            ngModule: BrowserAnimationsModule,\n            providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS :\n                BROWSER_ANIMATIONS_PROVIDERS\n        };\n    }\n}\nBrowserAnimationsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserAnimationsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserAnimationsModule, exports: [BrowserModule] });\nBrowserAnimationsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserAnimationsModule, providers: BROWSER_ANIMATIONS_PROVIDERS, imports: [BrowserModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: BrowserAnimationsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: BROWSER_ANIMATIONS_PROVIDERS,\n                }]\n        }] });\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\nclass NoopAnimationsModule {\n}\nNoopAnimationsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NoopAnimationsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNoopAnimationsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NoopAnimationsModule, exports: [BrowserModule] });\nNoopAnimationsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NoopAnimationsModule, providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS, imports: [BrowserModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: NoopAnimationsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserModule],\n                    providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ANIMATION_MODULE_TYPE, BrowserAnimationsModule, NoopAnimationsModule, AnimationRenderer as ɵAnimationRenderer, AnimationRendererFactory as ɵAnimationRendererFactory, BrowserAnimationBuilder as ɵBrowserAnimationBuilder, BrowserAnimationFactory as ɵBrowserAnimationFactory, InjectableAnimationEngine as ɵInjectableAnimationEngine };\n"]}, "metadata": {}, "sourceType": "module"}
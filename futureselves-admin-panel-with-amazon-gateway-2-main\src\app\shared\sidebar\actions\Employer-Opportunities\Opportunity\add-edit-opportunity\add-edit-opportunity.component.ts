import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChildren } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import {
  ActivatedRoute,
  Router,
  Params,
  NavigationEnd,
  RouterEvent,
} from '@angular/router';
import { filter } from 'rxjs/operators';
import { DatePipe } from '@angular/common';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormArray,
  FormControl,
  AbstractControl,
  ValidatorFn,
  ValidationErrors,
  ReactiveFormsModule,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { HttpClient } from '@angular/common/http';
import state from 'sweetalert/typings/modules/state';
import { ViewChild, ElementRef } from '@angular/core';
import { FileValidator } from 'src/app/shared/validators/fileValidator.validator';

function wordLimitValidator(maxWords: number) {
  return (control: AbstractControl): { [key: string]: any } | null => {
    if (control.value) {
      const words = control.value.trim().split(/\s+/);
      if (words.length > maxWords) {
        console.log('maxwords', maxWords);
        return { wordLimitExceeded: true, wordCount: words.length };
      }
    }
    return null;
  };
}

function nonNegativeValidator(
  control: FormControl
): { [key: string]: boolean } | null {
  // console.log('Control : ', control);
  const value = control.value;
  if (value < 0 || value > 24) {
    return { negativeValue: true };
  }
  return null;
}
@Component({
  selector: 'app-add-edit-opportunity',
  templateUrl: './add-edit-opportunity.component.html',
  styleUrls: ['./add-edit-opportunity.component.scss'],
})
export class AddEditOpportunityComponent implements OnInit {
  jobForm: FormGroup;
  p: number = 1;
  SectorList: any;
  CO_id: any;
  viewInsight = true;
  term: string;
  title = 'Add New';
  isReadonly = false;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  minDate: string;
  sectorID: any;
  companyLogo: any;
  RoleList: any;
  minStartDate: string;
  invalidDates: Set<string> = new Set<string>();
  checkMinDate: string;
  // selectedAudioFile: any;
  deleteId: any;
  toEditInsight: any;
  selectedType: string = 'job'; // Default value is 'job'
  descriptionCharCount: number = 0;
  requirementsCharCount: number = 0;
  hrtTipsCharCount: number = 0;
  audioFileUrls: string[] = []; // Array to store the generated URLs
  audioFiles: File[] = [];
  characterCount: number = 0;
  isPlaying: boolean[] = []; // Array to track playback state
  audioUrls: string[] = []; // Array to store audio URLs
  @ViewChildren('audioPlayer') audioPlayers: QueryList<
    ElementRef<HTMLAudioElement>
  >;
  opportunityData: any;
  hideOppType: boolean=false;
  audioNames: string[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private toastr: ToastrService,
    private datePipe: DatePipe,
    private route:ActivatedRoute
  ) {


    this.route.queryParams.subscribe(params => {
      if(params){
      this.CO_id = params['CO_id'];
      console.log(this.CO_id);
    }else{
      this.router.navigate(['/actions/employer-opportunities']);
    }
    });

    const state = this.router.getCurrentNavigation()?.extras?.state; //State values
    console.log('State Values', state);
    if (state) {
      this.viewInsight = state?.viewInsight;
      this.title = state?.title;
      this.opportunityData = state?.opportunityData;
      // this.JB_type=state?.opportunityData.JB_type;
      console.log('this.opportunityData: ', this.opportunityData);
    } else {
      this.router.navigate([`actions/employer-opportunities`]);
    }

    this.minDate = new Date().toISOString().split('T')[0];
    this.checkMinDate = this.minDate;

    this.jobForm = this.formBuilder.group(
      //Form to add new scheme
      {
        JB_companyId: [''],
        JB_companyLogo: [''],
        JB_applicationDeadline: ['', [Validators.required]],
        JB_startDate: ['', [Validators.required]],
        JB_jobTitle: ['', [Validators.required, wordLimitValidator(10)]],
        JB_description: [
          '',
          [Validators.required, this.characterLimitValidator(200)],
        ],
        JB_department: ['', [Validators.required, wordLimitValidator(10)]],
        JB_hours: ['', [Validators.required, nonNegativeValidator]],
        JB_hrtTips: ['',[Validators.required, this.characterLimitValidator(200)]],
        JB_roleId: ['', [Validators.required]],
        JB_location: ['', [Validators.required, wordLimitValidator(10)]],
        JB_salary: ['', [Validators.required]],
        JB_modeOfWork: ['', [Validators.required, wordLimitValidator(10)]],
        JB_requirements: [
          '',
          [Validators.required, this.characterLimitValidator(200)],
        ],
        JB_applyLink: ['', [Validators.required]],
        JB_type: ['', Validators.required],
        JB_sectorId: [''],
        JB_insights: this.formBuilder.array([this.createInsight()]),
      }
    );

    this.onTypeChange(this.selectedType);
  }

  ngOnInit(): void {
    this.getCompanyById(this.CO_id);
    this.getSectorTitles();
    // this.getAllRoleBySectorId(this.sectorID);
    this.minStartDate = this.minDate;

    if ( this.selectedType === 'job' && !this.jobForm.get('JB_sectorId')?.value) {
      this.jobForm.get('JB_roleId')?.disable();
    }
    if (this.title === 'Edit') {
      this.populateForm(this.opportunityData);
    }
    if (this.title === 'View') {
      this.makeDataReadOnly(this.opportunityData);
    }
  }

  makeDataReadOnly(data: any) {
    this.jobForm.get('JB_sectorId')?.disable()
    this.jobForm.get('JB_roleId')?.disable();
    this.jobForm.get('JB_modeOfWork')?.disable();
    this.hideOppType=true;
    this.getAllRole(data.JB_sectorId);
    this.isReadonly=true;
    this.jobForm.patchValue({
      JB_jobTitle: data.JB_jobTitle,
      JB_description: data.JB_description,
      JB_department: data.JB_department,
      JB_hours: data.JB_hours,
      JB_hrtTips: data.JB_hrtTips,
      JB_roleId: data.JB_roleId,
      JB_location: data.JB_location,
      JB_salary: data.JB_salary,
      JB_modeOfWork: data.JB_modeOfWork,
      JB_requirements: data.JB_requirements,
      JB_applyLink: data.JB_applyLink,
      JB_type: data.JB_type,
      JB_sectorId: data.JB_sectorId,
      JB_insights: data.JB_insights,
      JB_applicationDeadline:this.datePipe.transform(data.JB_applicationDeadline,'MMM d,y') ,
      JB_startDate:this.datePipe.transform(data.JB_startDate,'MMM d,y'),
      JB_companyId: this.CO_id,
      CO_sectorId: this.sectorID,
      JB_companyLogo: this.companyLogo,  
    });

    const hrInsightsArray = this.jobForm.get('JB_insights') as FormArray;
    hrInsightsArray.clear();

    this.audioNames = data.JB_insights.map((insight: any) => insight.HRI_link.split('/').pop());

    data.JB_insights.forEach((insight: any) => {
      const insightFormGroup = this.formBuilder.group({
        HRI_title: [insight.HRI_title],
        HRI_name: [insight.HRI_name],
        HRI_position: [insight.HRI_position],
        HRI_link: [insight.HRI_link],
      });
      hrInsightsArray.push(insightFormGroup);
    });

    this.audioUrls = data.JB_insights.map((insight: any) => insight.HRI_link);
    this.isPlaying = new Array(data.JB_insights.length).fill(false);
    

    if (data.JB_type === 0) {
      this.selectedType = 'job';
    } else{
      this.selectedType = 'jobScheme';
    }

}

  populateForm(data: any) {
    this.hideOppType=true;
    console.log('Data to patch to edit', data);
    this.getAllRole(data.JB_sectorId);
    this.jobForm.patchValue({
      JB_jobTitle: data.JB_jobTitle,
      JB_description: data.JB_description,
      JB_department: data.JB_department,
      JB_hours: data.JB_hours,
      JB_hrtTips: data.JB_hrtTips,
      JB_roleId: data.JB_roleId,
      JB_location: data.JB_location,
      JB_salary: data.JB_salary,
      JB_modeOfWork: data.JB_modeOfWork,
      JB_requirements: data.JB_requirements,
      JB_applyLink: data.JB_applyLink,
      JB_type: data.JB_type,
      JB_sectorId: data.JB_sectorId,
      JB_insights: data.JB_insights,
      JB_applicationDeadline:data.JB_applicationDeadline,
      JB_startDate:data.JB_startDate,
      JB_companyId: this.CO_id,
      CO_sectorId: this.sectorID,
      JB_companyLogo: this.companyLogo,  
    });

    const hrInsightsArray = this.jobForm.get('JB_insights') as FormArray;
    hrInsightsArray.clear();

    data.JB_insights.forEach((insight: any) => {
      const insightFormGroup = this.formBuilder.group({
        HRI_title: [insight.HRI_title],
        HRI_name: [insight.HRI_name],
        HRI_position: [insight.HRI_position],
        HRI_link: [insight.HRI_link],
      });
      hrInsightsArray.push(insightFormGroup);
    });

    // Initialize audio URLs and playback states
    // this.audioUrls = data.JB_insights.map((insight: any) => insight.HRI_link);
    // this.isPlaying = new Array(data.JB_insights.length).fill(false);
    this.audioUrls = data.JB_insights.map((insight: any) => insight.HRI_link);
    this.isPlaying = new Array(data.JB_insights.length).fill(false);

      const JB_type = this.opportunityData?.JB_type;
      if (JB_type === 0) {
        if(this.jobForm.get('JB_sectorId')?.value){
          this.jobForm.get('JB_roleId')?.enable();
        }else{
          this.jobForm.get('JB_roleId')?.reset({ value: null, disabled: true });
         
          }
        this.selectedType = 'job';
      } else{
        this.selectedType = 'jobScheme';
      }
  }


  get insightFormArray(): FormArray {
    return this.jobForm.get('JB_insights') as FormArray;
  }

  characterLimitValidator(limit: number) {
    return (control: any) => {
      if (control.value && control.value.length > limit) {
        return { characterLimitExceeded: true };
      }
      return null;
    };
  }

  onInput(field: string) {
    const control = this.jobForm.get(field);
    if (control) {
      const value = control.value || '';
      switch (field) {
        case 'JB_description':
          this.descriptionCharCount = value.length;
          break;
        case 'JB_requirements':
          this.requirementsCharCount = value.length;
          break;
        case 'JB_hrtTips':
          this.hrtTipsCharCount = value.length;
          break;
      }
      control.updateValueAndValidity(); // Trigger validation
    }
  }

  onTypeChange(value: string): void {
    this.selectedType = value;
    this.jobForm.reset();

    const insightDiv = document.getElementById('link') as HTMLInputElement;
    if (insightDiv) {
      insightDiv.value = '';
    }

    this.audioUrls = [];
    this.audioFiles = [];
    this.isPlaying = [];

    if (this.audioPlayers) {
      this.audioPlayers.forEach((audioPlayer) => {
        audioPlayer.nativeElement.pause();
        audioPlayer.nativeElement.currentTime = 0;
      });
    }
    this.jobForm.get('JB_roleId')?.reset({ value: null, disabled: true });
    const sectorControl = this.jobForm.get('JB_sectorId');
    const roleControl = this.jobForm.get('JB_roleId');
    if (value === 'job') {
      sectorControl?.setValidators([Validators.required]);
      roleControl?.setValidators([Validators.required]);
    } else {
      sectorControl?.clearValidators();
      roleControl?.clearValidators();
    }
    sectorControl?.updateValueAndValidity();
    roleControl?.updateValueAndValidity();
  }

  onChangeIndustry(event: any) {
    const CO_sectorId = event.target.value;
    this.jobForm.get('JB_roleId')?.enable();
    this.getAllRole(CO_sectorId);
  }

  updateStartDateMin(): void {
    const deadline = this.jobForm.get('JB_applicationDeadline')?.value;
    if (deadline) {
      const minStartDate = new Date(deadline);
      minStartDate.setDate(minStartDate.getDate() + 1); // Add one day to the deadline
      this.minStartDate = minStartDate.toISOString().split('T')[0];

      // After updating minimum start date, check the validity of the start date
      this.checkInvalidDate('JB_startDate');
    }
  }

  checkInvalidDate(controlName: string): void {
    const control = this.jobForm.get(controlName);
    if (control && control.value && isNaN(Date.parse(control.value))) {
      this.invalidDates.add(controlName);
    } else {
      this.invalidDates.delete(controlName);
    }
  }

  getSectorTitles() {
    // In Add company form - To sector dropdown
    this.dataTransferService.getSectorTitles().subscribe((res: any) => {
      if ((res.statusCode = 200)) {
        this.SectorList = res.data;
        console.log('Sectors', this.SectorList);
      } else {
        console.error('Failed to fetch sectors. Status:', res.status);
      }
    });
  }

  formatDate(date: Date): string | null {
    // Check if the date is valid
    if (!isNaN(date.getTime())) {
      return this.datePipe.transform(date, 'MMM dd yyyy');
    } else {
      return '';
    }
  }

  getCompanyById(companyId:any){
    this.dataTransferService.getCompanyById(companyId).subscribe((res:any)=>{
      this.companyLogo=res.CO_logo;
      this.sectorID=res.CO_sectorId;
      console.log('getCompanyById',res);
    },(error:any)=>{
     console.log("Error",error);
     this.toastr.error("Unable to fetch data");
    });
  }

  getAllRole(CO_sectorId: any) {
    console.log('sectorId : ', CO_sectorId);
    this.dataTransferService.getAllRoleBySectorId(CO_sectorId).subscribe({
      next: (res: any) => {
        if (res.statusCode === 200) {
          this.RoleList = res.data;
          console.log('RoleList : ', this.RoleList);
        } else {
          console.error('Failed to fetch role. Status:', res.status);
        }
      },
      error: (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error occurred while fetching roles:', error);
      },
    });
  }

  // getAllRoleBySectorId(sectorID: any) {
  //   this.dataTransferService.getAllRoleBySectorId(sectorID).subscribe({
  //     next: (res: any) => {
  //       if (res.statusCode === 200) {
  //         this.RoleList = res.data;
  //         this.roleId = this.RoleList.RO_id;
  //         console.log('RoleList : ', this.RoleList);
  //         this.ngxSpinnerService.hide('globalSpinner');
  //       } else {
  //         this.ngxSpinnerService.hide('globalSpinner');
  //         console.error('Failed to fetch role. Status:', res.status);
  //       }
  //     },
  //     error: (error: any) => {
  //       this.ngxSpinnerService.hide('globalSpinner');
  //       console.error('Error occurred while fetching roles:', error);
  //     },
  //   });
  // }

  showDeleteModal(scheme: any) {
    console.log('Delete Scheme', scheme);
    this.deleteId = scheme.JB_id;
  }

  // onFileSelected(event: any) {
  //   //to preview image and take file in imageName to pass in upload api
  //   if (event.target.files.length === 0) {
  //     // Reset both imageName and imageSrc when no file is selected
  //     this.imageName = null;
  //     this.imageSrc = null;
  //     return;
  //   }

  //   this.imageName = event.target.files[0];
  //   const fileType = this.imageName.type.split('/')[0];
  //   if (fileType !== 'image') {
  //     event.target.value = '';
  //     this.toastr.info('Please select an image file.');
  //     this.imageName = null;
  //     this.imageSrc = null;
  //     return;
  //   }

  //   if (this.imageName && fileType == 'image') {
  //     const reader = new FileReader();
  //     reader.onload = (e) => {
  //       this.imageSrc = e.target?.result as string | ArrayBuffer;
  //     };
  //     reader.readAsDataURL(this.imageName);
  //   } else {
  //     this.imageSrc = null; // Reset imageSrc if no file selected
  //   }
  //   console.log('imageName', this.imageName);
  // }

  // uploadLogoUrl() {
  //   //To upload logo from add new company form
  //   if (!this.imageName) {
  //     // this.toastr.error('Please select an image.');
  //     return;
  //   }
  //   console.log('image', this.imageName);
  //   this.dataTransferService
  //     .uploadurl(this.imageName)
  //     .subscribe((res: any) => {});
  // }

  // getAllJobByJobId(scheme: any, title: string) {
  //   this.title = title;
  //   if (this.title == 'View') {
  //     this.isReadonly = true;
  //     this.viewInsight = false;
  //     this.ngxSpinnerService.show('globalSpinner');
  //     this.dataTransferService.getAllJobByJobId(scheme.JB_id).subscribe({
  //       next: (res: any) => {
  //         if (res.statusCode === 200 && res.data && res.data.length > 0) {
  //           this.ngxSpinnerService.hide('globalSpinner');
  //           const formatDate = (timestamp: number): string => {
  //             const date = new Date(timestamp);
  //             const year = date.getFullYear();
  //             const month = String(date.getMonth() + 1).padStart(2, '0');
  //             const day = String(date.getDate()).padStart(2, '0');
  //             return `${month}-${day}-${year}`;
  //           };

  //           // Format dates in the response
  //           const formattedPayload = res.data.map((item: any) => ({
  //             ...item,
  //             JB_startDate: formatDate(item.JB_startDate),
  //             JB_applicationDeadline: formatDate(item.JB_applicationDeadline),
  //           }));

  //           this.jobForm.patchValue(formattedPayload[0]);
  //           this.jobForm.get('JB_roleId')?.disable();
  //           this.jobForm.get('JB_modeOfWork')?.disable();

  //           // Log the updated JobList
  //           console.log('Full JobList', formattedPayload[0]);
  //         } else {
  //           this.ngxSpinnerService.hide('globalSpinner');
  //           console.error('Error: Invalid response or no data found.');
  //         }
  //       },
  //       error: (error: any) => {
  //         this.ngxSpinnerService.hide('globalSpinner');
  //         console.error('Error occurred while fetching roles:', error);
  //       },
  //     });
  //     // this.jobForm.patchValue(scheme);
  //   }
  // }

  // editId: any;
  // editScheme(scheme: any, title: string) {
  //   this.isReadonly = false;
  //   this.title = title;
  //   this.jobForm.get('JB_roleId')?.enable();
  //   this.jobForm.get('JB_modeOfWork')?.enable();
  //   this.viewInsight = true;
  //   this.editId = scheme.JB_id;
  //   this.toEditInsight = scheme.JB_insights;
  //   const formatDate = (timestamp: number): string => {
  //     const date = new Date(timestamp);
  //     const year = date.getFullYear();
  //     const month = String(date.getMonth() + 1).padStart(2, '0');
  //     const day = String(date.getDate()).padStart(2, '0');
  //     return `${month}-${day}-${year}`;
  //   };

  //   const formattedPayload = {
  //     ...scheme,
  //     JB_startDate: formatDate(scheme.JB_startDate),
  //     JB_applicationDeadline: formatDate(scheme.JB_applicationDeadline),
  //   };
  //   // this.selectedAudioFilethis.jobForm.get('JB_insights')?.setValue(this.selectedAudioFile);
  //   this.jobForm.patchValue(formattedPayload);
  // }

  createInsight(): FormGroup {
    return this.formBuilder.group({
      HRI_title: [''],
      HRI_name: ['',],
      HRI_position: [''],
      HRI_link: [null],
    });
    this.isPlaying.push(false); // Initialize playback state for new row
    this.audioUrls.push(''); // Initialize empty URL for new row
  }

  addInsight() {
    this.insightFormArray.push(this.createInsight());
  }

  removeInsight(index: number) {
    this.insightFormArray.removeAt(index);
    this.audioUrls.splice(index, 1); // Remove URL for deleted row
    this.isPlaying.splice(index, 1); // Remove playback state for deleted row
    this.audioFiles.splice(index, 1); // Remove file object for deleted row
  }

  toggleAudio(index: number) {
    const audioElements = this.audioPlayers.toArray();
    const audioElement = audioElements[index].nativeElement;

    // Stop all other audio
    this.stopAllAudio(index);

    // Toggle play/pause for the current audio element
    if (this.isPlaying[index]) {
      audioElement.pause();
    } else {
      audioElement.src = this.audioUrls[index];
      audioElement.play();
    }

    // Update the playback state
    this.isPlaying[index] = !this.isPlaying[index];
  }

  stopAllAudio(currentIndex: number) {
    this.audioPlayers.forEach((audioPlayer, index) => {
      if (index !== currentIndex) {
        audioPlayer.nativeElement.pause();
        this.isPlaying[index] = false;
      }
    });
  }

  stopAudio(index: number) {
    const audioElements = this.audioPlayers.toArray();
    const audioElement = audioElements[index].nativeElement;

    audioElement.pause();
    audioElement.currentTime = 0; // Reset to the beginning
    this.isPlaying[index] = false;
  }

  onAudioSelected(event: any, index: number): void {
    let audiofile: File | null;
    const selectedFile = event.target.files[0];
    
    if (selectedFile) {
      const newFileName = FileValidator.addTimestamp(selectedFile.name);
      audiofile = new File([selectedFile], newFileName, { type: selectedFile.type });
    } else {
      audiofile = null;
    }
        
    if (!audiofile) {
      this.audioUrls[index] = '';
      this.stopAudio(index);
      return;
    }
    const audiofileType = audiofile.type.split('/')[0]; // Get the file type (e.g., 'audio', 'video', etc.)
    if (audiofileType !== 'audio') {
      // Reset the file input to clear the selected file
      event.target.value = '';
      this.toastr.info('Please select an audio file.');
      return;
    }

    // Store the file object in the array for later upload
    this.audioFiles[index] = audiofile;
    console.log('Audio file :', audiofile);

    const reader = new FileReader();
    reader.onload = () => {
      // Store audio URL
      this.audioUrls[index] = reader.result as string;
    };
    reader.readAsDataURL(audiofile);
  }

  async uploadAudioFilesSequentially(files: File[]): Promise<string[]> {
    const uploadPromises = files.map((file, index) =>
      this.uploadSingleAudioFile(file, index)
    );
    return Promise.all(uploadPromises);
  }

  uploadSingleAudioFile(file: File, index: number): Promise<string> {
    return new Promise((resolve, reject) => {
      this.dataTransferService.uploadurl(file).subscribe(
        (res: any) => {
          console.log('Upload successful', file.name);
          const fileUrl = this.baseUrl + file.name;
          this.audioUrls[index] = fileUrl; // Directly add the file URL to this.audioUrls array
          resolve(fileUrl);
        },
        (error: any) => {
          console.error('Upload error', error);
          this.toastr.error('Failed to upload audio file');
          reject(error);
        }
      );
    });
  }

  onSubmit() {
    if (this.title ==='Edit') {

      const sectorControl = this.jobForm.get('JB_sectorId');//Remove Validation as per selected type...
      const roleControl = this.jobForm.get('JB_roleId');
      if (this.selectedType === 'job') {
        sectorControl?.setValidators([Validators.required]);
        roleControl?.setValidators([Validators.required]);
      } else {
        sectorControl?.clearValidators();
        roleControl?.clearValidators();
      }
      sectorControl?.updateValueAndValidity();
      roleControl?.updateValueAndValidity();

    
      this.ngxSpinnerService.show('globalSpinner');
      this.uploadAudioFilesSequentially(this.audioFiles) //upload file one by one
        .then((audioUrls: string[]) => {
          this.insightFormArray.controls.forEach(
            (control: AbstractControl, index: number) => {
              const group = control as FormGroup;
              group.get('HRI_link')?.setValue(this.audioUrls[index]); // Patch the URL from the array
            }
          );
          if (this.jobForm.invalid) {
            console.log('this.job.value', this.jobForm.value);
            this.ngxSpinnerService.hide('globalSpinner');
            this.toastr.info(
              'Please fill all required fields and ensure they are filled correctly.'
            );
            return;
          } else {
            console.log('this.job.value', this.jobForm.value);
            this.updateOpportunity();
          }
        })
        .catch((error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Error uploading audio files:', error);
          this.toastr.error('Audio file upload failed');
        });
    } else {
      this.jobForm.patchValue({
        JB_companyId: this.CO_id,
        CO_sectorId: this.sectorID,
        JB_companyLogo: this.companyLogo,
        JB_type: this.selectedType === 'job' ? 0 : 1,
      });
      this.ngxSpinnerService.show('globalSpinner');
      this.uploadAudioFilesSequentially(this.audioFiles)
        .then((audioUrls: string[]) => {
          this.insightFormArray.controls.forEach(
            (control: AbstractControl, index: number) => {
              const group = control as FormGroup;
              group.get('HRI_link')?.setValue(audioUrls[index]); // Patch the URL from the array
            }
          );
          if (this.jobForm.invalid) {
            console.log('this.job.value', this.jobForm.value);
            this.ngxSpinnerService.hide('globalSpinner');
            this.toastr.info(
              'Please fill all required fields and ensure they are filled correctly.'
            );
            return;
          } else {
            console.log('this.job.value', this.jobForm.value);
            this.addOpportunity();
          }
        })
        .catch((error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Error uploading audio files:', error);
          this.toastr.error('Audio file upload failed');
        });
    }
  }

  updateOpportunity() {
    console.log("selected Type",this.selectedType);
    const data = {
      JB_jobTitle: this.jobForm.get('JB_jobTitle')?.value,
      JB_description: this.jobForm.get('JB_description')?.value,
      JB_department: this.jobForm.get('JB_department')?.value,
      JB_hours: this.jobForm.get('JB_hours')?.value,
      JB_hrtTips: this.jobForm.get('JB_hrtTips')?.value,
      JB_roleId: this.jobForm.get('JB_roleId')?.value ? this.jobForm.get('JB_roleId')?.value :'',
      JB_location: this.jobForm.get('JB_location')?.value,
      JB_salary: this.jobForm.get('JB_salary')?.value,
      JB_modeOfWork: this.jobForm.get('JB_modeOfWork')?.value,
      JB_requirements: this.jobForm.get('JB_requirements')?.value,
      JB_applyLink: this.jobForm.get('JB_applyLink')?.value,
      JB_type: this.jobForm.get('JB_type')?.value,
      JB_sectorId: this.jobForm.get('JB_sectorId')?.value ? this.jobForm.get('JB_sectorId')?.value : '',
      JB_insights: this.jobForm.get('JB_insights')?.value,
      JB_applicationDeadline: this.jobForm.get('JB_applicationDeadline')?.value,
      JB_startDate: this.jobForm.get('JB_startDate')?.value,
      JB_companyId: this.CO_id,
      CO_sectorId: this.sectorID,
      JB_companyLogo: this.companyLogo,
      JB_id:this.opportunityData.JB_id
    };
    
    const startDateControl = this.jobForm.get('JB_startDate');
    if (startDateControl) {
      const startDateValue = startDateControl.value;
      const deadlineValue = this.jobForm.get('JB_applicationDeadline')?.value;
      if (startDateValue && deadlineValue) {
        const startDate = new Date(startDateValue);
        const deadline = new Date(deadlineValue);
        if (startDate >= deadline) {
          if (deadlineValue < this.checkMinDate) {
            // Application deadline is in the past
            this.toastr.error(
              'Application deadline must be today or a future date.'
            );
            return;
          }
          console.log('Edit SCHEME DATA : ', data);

          this.ngxSpinnerService.show('globalSpinner');
          this.dataTransferService.updateJobs(data).subscribe(
            (res: any) => {
              console.log('Edit User', data);
              if (res.statusCode == 200) {
                this.ngxSpinnerService.hide('globalSpinner');
                this.toastr.success('Opportunity Updated Successfully.');
                const state = {
                  CO_id: this.CO_id,
                  CO_sectorId: this.sectorID,
                  CO_logo: this.companyLogo,
                };
                this.router.navigate(
                  ['/actions/employer-opportunities/existing-opportunities'],
                  {queryParams: { CO_id: this.CO_id }}
                );
                this.dataTransferService.getAllSchemeByCompanyId(this.CO_id);
              } else {
                this.ngxSpinnerService.hide('globalSpinner');
                console.log('', res.message);
                this.toastr.error('', 'Something went wrong');
              }
            },
            (error: any) => {
              this.ngxSpinnerService.hide('globalSpinner');
              console.error('Unable to edit :', error);
              this.toastr.error('', 'Something went wrong');
            }
          );
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          this.toastr.error(
            'Start date must be after the application deadline.'
          );
          console.log('Invalid form submission!');
        }
      }
    }
  }

  addOpportunity() {
    const startDateControl = this.jobForm.get('JB_startDate');
    if (startDateControl) {
      const startDateValue = startDateControl.value;
      const deadlineValue = this.jobForm.get('JB_applicationDeadline')?.value;
      if (startDateValue && deadlineValue) {
        const startDate = new Date(startDateValue);
        const deadline = new Date(deadlineValue);
        if (startDate >= deadline) {
          if (deadlineValue < this.checkMinDate) {
            // Application deadline is in the past
            this.toastr.error(
              'Application deadline must be today or a future date.'
            );
            return;
          }
          console.log('ADD SCHEME DATA : ', this.jobForm.value);

          this.dataTransferService.addOpportunity(this.jobForm.value).subscribe(
            (res: any) => {
              console.log('addOpportunity', res);
              this.ngxSpinnerService.hide('globalSpinner');

              if (res.statusCode === 200) {
                this.toastr.success('', 'New opportunity added successfully.');
                this.router.navigate(
                  ['/actions/employer-opportunities/existing-opportunities'],
                  {queryParams: { CO_id: this.CO_id }}
                );
                this.dataTransferService.getAllSchemeByCompanyId(this.CO_id);
              } else {
                this.toastr.error('', 'Something went wrong');
              }
            },
            (error: any) => {
              this.ngxSpinnerService.hide('globalSpinner');
              console.error('Unable to add opportunity:', error);
              this.toastr.error('', 'Something went wrong');
            }
          );
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          this.toastr.error(
            'Start date must be after the application deadline.'
          );
          console.log('Invalid form submission!');
        }
      }
    }
  }

  goToExistingScheme() {
    this.router.navigate(
      [`/actions/employer-opportunities/existing-opportunities`],
      {queryParams: { CO_id: this.CO_id }}
    );
  }
}

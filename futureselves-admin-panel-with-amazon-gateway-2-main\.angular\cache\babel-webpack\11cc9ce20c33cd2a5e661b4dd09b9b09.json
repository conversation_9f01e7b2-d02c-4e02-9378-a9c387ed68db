{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Optional, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler } from 'primeng-lts/dom';\nimport * as i1 from 'primeng-lts/api';\n\nclass Ripple {\n  constructor(el, zone, config) {\n    this.el = el;\n    this.zone = zone;\n    this.config = config;\n  }\n\n  ngAfterViewInit() {\n    if (this.config && this.config.ripple) {\n      this.zone.runOutsideAngular(() => {\n        this.create();\n        this.mouseDownListener = this.onMouseDown.bind(this);\n        this.el.nativeElement.addEventListener('mousedown', this.mouseDownListener);\n      });\n    }\n  }\n\n  onMouseDown(event) {\n    let ink = this.getInk();\n\n    if (!ink || getComputedStyle(ink, null).display === 'none') {\n      return;\n    }\n\n    DomHandler.removeClass(ink, 'p-ink-active');\n\n    if (!DomHandler.getHeight(ink) && !DomHandler.getWidth(ink)) {\n      let d = Math.max(DomHandler.getOuterWidth(this.el.nativeElement), DomHandler.getOuterHeight(this.el.nativeElement));\n      ink.style.height = d + 'px';\n      ink.style.width = d + 'px';\n    }\n\n    let offset = DomHandler.getOffset(this.el.nativeElement);\n    let x = event.pageX - offset.left + document.body.scrollTop - DomHandler.getWidth(ink) / 2;\n    let y = event.pageY - offset.top + document.body.scrollLeft - DomHandler.getHeight(ink) / 2;\n    ink.style.top = y + 'px';\n    ink.style.left = x + 'px';\n    DomHandler.addClass(ink, 'p-ink-active');\n  }\n\n  getInk() {\n    for (let i = 0; i < this.el.nativeElement.children.length; i++) {\n      if (this.el.nativeElement.children[i].className.indexOf('p-ink') !== -1) {\n        return this.el.nativeElement.children[i];\n      }\n    }\n\n    return null;\n  }\n\n  resetInk() {\n    let ink = this.getInk();\n\n    if (ink) {\n      DomHandler.removeClass(ink, 'p-ink-active');\n    }\n  }\n\n  onAnimationEnd(event) {\n    DomHandler.removeClass(event.currentTarget, 'p-ink-active');\n  }\n\n  create() {\n    let ink = document.createElement('span');\n    ink.className = 'p-ink';\n    this.el.nativeElement.appendChild(ink);\n    this.animationListener = this.onAnimationEnd.bind(this);\n    ink.addEventListener('animationend', this.animationListener);\n  }\n\n  remove() {\n    let ink = this.getInk();\n\n    if (ink) {\n      this.el.nativeElement.removeEventListener('mousedown', this.mouseDownListener);\n      ink.removeEventListener('animationend', this.animationListener);\n      DomHandler.removeElement(ink);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.config && this.config.ripple) {\n      this.remove();\n    }\n  }\n\n}\n\nRipple.ɵfac = function Ripple_Factory(t) {\n  return new (t || Ripple)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig, 8));\n};\n\nRipple.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: Ripple,\n  selectors: [[\"\", \"pRipple\", \"\"]],\n  hostAttrs: [1, \"p-ripple\", \"p-element\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Ripple, [{\n    type: Directive,\n    args: [{\n      selector: '[pRipple]',\n      host: {\n        'class': 'p-ripple p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.PrimeNGConfig,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\nclass RippleModule {}\n\nRippleModule.ɵfac = function RippleModule_Factory(t) {\n  return new (t || RippleModule)();\n};\n\nRippleModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: RippleModule\n});\nRippleModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Ripple],\n      declarations: [Ripple]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Ripple, RippleModule };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/primeng-lts/fesm2015/primeng-lts-ripple.js"], "names": ["i0", "Directive", "Optional", "NgModule", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "i1", "<PERSON><PERSON><PERSON>", "constructor", "el", "zone", "config", "ngAfterViewInit", "ripple", "runOutsideAngular", "create", "mouseDownListener", "onMouseDown", "bind", "nativeElement", "addEventListener", "event", "ink", "getInk", "getComputedStyle", "display", "removeClass", "getHeight", "getWidth", "d", "Math", "max", "getOuterWidth", "getOuterHeight", "style", "height", "width", "offset", "getOffset", "x", "pageX", "left", "document", "body", "scrollTop", "y", "pageY", "top", "scrollLeft", "addClass", "i", "children", "length", "className", "indexOf", "resetInk", "onAnimationEnd", "currentTarget", "createElement", "append<PERSON><PERSON><PERSON>", "animationListener", "remove", "removeEventListener", "removeElement", "ngOnDestroy", "ɵfac", "ElementRef", "NgZone", "PrimeNGConfig", "ɵdir", "type", "args", "selector", "host", "decorators", "RippleModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,QAApB,EAA8BC,QAA9B,QAA8C,eAA9C;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,QAA2B,iBAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;;AAEA,MAAMC,MAAN,CAAa;AACTC,EAAAA,WAAW,CAACC,EAAD,EAAKC,IAAL,EAAWC,MAAX,EAAmB;AAC1B,SAAKF,EAAL,GAAUA,EAAV;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKC,MAAL,GAAcA,MAAd;AACH;;AACDC,EAAAA,eAAe,GAAG;AACd,QAAI,KAAKD,MAAL,IAAe,KAAKA,MAAL,CAAYE,MAA/B,EAAuC;AACnC,WAAKH,IAAL,CAAUI,iBAAV,CAA4B,MAAM;AAC9B,aAAKC,MAAL;AACA,aAAKC,iBAAL,GAAyB,KAAKC,WAAL,CAAiBC,IAAjB,CAAsB,IAAtB,CAAzB;AACA,aAAKT,EAAL,CAAQU,aAAR,CAAsBC,gBAAtB,CAAuC,WAAvC,EAAoD,KAAKJ,iBAAzD;AACH,OAJD;AAKH;AACJ;;AACDC,EAAAA,WAAW,CAACI,KAAD,EAAQ;AACf,QAAIC,GAAG,GAAG,KAAKC,MAAL,EAAV;;AACA,QAAI,CAACD,GAAD,IAAQE,gBAAgB,CAACF,GAAD,EAAM,IAAN,CAAhB,CAA4BG,OAA5B,KAAwC,MAApD,EAA4D;AACxD;AACH;;AACDpB,IAAAA,UAAU,CAACqB,WAAX,CAAuBJ,GAAvB,EAA4B,cAA5B;;AACA,QAAI,CAACjB,UAAU,CAACsB,SAAX,CAAqBL,GAArB,CAAD,IAA8B,CAACjB,UAAU,CAACuB,QAAX,CAAoBN,GAApB,CAAnC,EAA6D;AACzD,UAAIO,CAAC,GAAGC,IAAI,CAACC,GAAL,CAAS1B,UAAU,CAAC2B,aAAX,CAAyB,KAAKvB,EAAL,CAAQU,aAAjC,CAAT,EAA0Dd,UAAU,CAAC4B,cAAX,CAA0B,KAAKxB,EAAL,CAAQU,aAAlC,CAA1D,CAAR;AACAG,MAAAA,GAAG,CAACY,KAAJ,CAAUC,MAAV,GAAmBN,CAAC,GAAG,IAAvB;AACAP,MAAAA,GAAG,CAACY,KAAJ,CAAUE,KAAV,GAAkBP,CAAC,GAAG,IAAtB;AACH;;AACD,QAAIQ,MAAM,GAAGhC,UAAU,CAACiC,SAAX,CAAqB,KAAK7B,EAAL,CAAQU,aAA7B,CAAb;AACA,QAAIoB,CAAC,GAAGlB,KAAK,CAACmB,KAAN,GAAcH,MAAM,CAACI,IAArB,GAA4BC,QAAQ,CAACC,IAAT,CAAcC,SAA1C,GAAsDvC,UAAU,CAACuB,QAAX,CAAoBN,GAApB,IAA2B,CAAzF;AACA,QAAIuB,CAAC,GAAGxB,KAAK,CAACyB,KAAN,GAAcT,MAAM,CAACU,GAArB,GAA2BL,QAAQ,CAACC,IAAT,CAAcK,UAAzC,GAAsD3C,UAAU,CAACsB,SAAX,CAAqBL,GAArB,IAA4B,CAA1F;AACAA,IAAAA,GAAG,CAACY,KAAJ,CAAUa,GAAV,GAAgBF,CAAC,GAAG,IAApB;AACAvB,IAAAA,GAAG,CAACY,KAAJ,CAAUO,IAAV,GAAiBF,CAAC,GAAG,IAArB;AACAlC,IAAAA,UAAU,CAAC4C,QAAX,CAAoB3B,GAApB,EAAyB,cAAzB;AACH;;AACDC,EAAAA,MAAM,GAAG;AACL,SAAK,IAAI2B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzC,EAAL,CAAQU,aAAR,CAAsBgC,QAAtB,CAA+BC,MAAnD,EAA2DF,CAAC,EAA5D,EAAgE;AAC5D,UAAI,KAAKzC,EAAL,CAAQU,aAAR,CAAsBgC,QAAtB,CAA+BD,CAA/B,EAAkCG,SAAlC,CAA4CC,OAA5C,CAAoD,OAApD,MAAiE,CAAC,CAAtE,EAAyE;AACrE,eAAO,KAAK7C,EAAL,CAAQU,aAAR,CAAsBgC,QAAtB,CAA+BD,CAA/B,CAAP;AACH;AACJ;;AACD,WAAO,IAAP;AACH;;AACDK,EAAAA,QAAQ,GAAG;AACP,QAAIjC,GAAG,GAAG,KAAKC,MAAL,EAAV;;AACA,QAAID,GAAJ,EAAS;AACLjB,MAAAA,UAAU,CAACqB,WAAX,CAAuBJ,GAAvB,EAA4B,cAA5B;AACH;AACJ;;AACDkC,EAAAA,cAAc,CAACnC,KAAD,EAAQ;AAClBhB,IAAAA,UAAU,CAACqB,WAAX,CAAuBL,KAAK,CAACoC,aAA7B,EAA4C,cAA5C;AACH;;AACD1C,EAAAA,MAAM,GAAG;AACL,QAAIO,GAAG,GAAGoB,QAAQ,CAACgB,aAAT,CAAuB,MAAvB,CAAV;AACApC,IAAAA,GAAG,CAAC+B,SAAJ,GAAgB,OAAhB;AACA,SAAK5C,EAAL,CAAQU,aAAR,CAAsBwC,WAAtB,CAAkCrC,GAAlC;AACA,SAAKsC,iBAAL,GAAyB,KAAKJ,cAAL,CAAoBtC,IAApB,CAAyB,IAAzB,CAAzB;AACAI,IAAAA,GAAG,CAACF,gBAAJ,CAAqB,cAArB,EAAqC,KAAKwC,iBAA1C;AACH;;AACDC,EAAAA,MAAM,GAAG;AACL,QAAIvC,GAAG,GAAG,KAAKC,MAAL,EAAV;;AACA,QAAID,GAAJ,EAAS;AACL,WAAKb,EAAL,CAAQU,aAAR,CAAsB2C,mBAAtB,CAA0C,WAA1C,EAAuD,KAAK9C,iBAA5D;AACAM,MAAAA,GAAG,CAACwC,mBAAJ,CAAwB,cAAxB,EAAwC,KAAKF,iBAA7C;AACAvD,MAAAA,UAAU,CAAC0D,aAAX,CAAyBzC,GAAzB;AACH;AACJ;;AACD0C,EAAAA,WAAW,GAAG;AACV,QAAI,KAAKrD,MAAL,IAAe,KAAKA,MAAL,CAAYE,MAA/B,EAAuC;AACnC,WAAKgD,MAAL;AACH;AACJ;;AArEQ;;AAuEbtD,MAAM,CAAC0D,IAAP;AAAA,mBAAmG1D,MAAnG,EAAyFP,EAAzF,mBAA2HA,EAAE,CAACkE,UAA9H,GAAyFlE,EAAzF,mBAAqJA,EAAE,CAACmE,MAAxJ,GAAyFnE,EAAzF,mBAA2KM,EAAE,CAAC8D,aAA9K;AAAA;;AACA7D,MAAM,CAAC8D,IAAP,kBADyFrE,EACzF;AAAA,QAAuFO,MAAvF;AAAA;AAAA;AAAA;;AACA;AAAA,qDAFyFP,EAEzF,mBAA2FO,MAA3F,EAA+G,CAAC;AACpG+D,IAAAA,IAAI,EAAErE,SAD8F;AAEpGsE,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,WADX;AAECC,MAAAA,IAAI,EAAE;AACF,iBAAS;AADP;AAFP,KAAD;AAF8F,GAAD,CAA/G,EAQ4B,YAAY;AAAE,WAAO,CAAC;AAAEH,MAAAA,IAAI,EAAEtE,EAAE,CAACkE;AAAX,KAAD,EAA0B;AAAEI,MAAAA,IAAI,EAAEtE,EAAE,CAACmE;AAAX,KAA1B,EAA+C;AAAEG,MAAAA,IAAI,EAAEhE,EAAE,CAAC8D,aAAX;AAA0BM,MAAAA,UAAU,EAAE,CAAC;AACnHJ,QAAAA,IAAI,EAAEpE;AAD6G,OAAD;AAAtC,KAA/C,CAAP;AAElB,GAVxB;AAAA;;AAWA,MAAMyE,YAAN,CAAmB;;AAEnBA,YAAY,CAACV,IAAb;AAAA,mBAAyGU,YAAzG;AAAA;;AACAA,YAAY,CAACC,IAAb,kBAhByF5E,EAgBzF;AAAA,QAA0G2E;AAA1G;AACAA,YAAY,CAACE,IAAb,kBAjByF7E,EAiBzF;AAAA,YAAkI,CAACI,YAAD,CAAlI;AAAA;;AACA;AAAA,qDAlByFJ,EAkBzF,mBAA2F2E,YAA3F,EAAqH,CAAC;AAC1GL,IAAAA,IAAI,EAAEnE,QADoG;AAE1GoE,IAAAA,IAAI,EAAE,CAAC;AACCO,MAAAA,OAAO,EAAE,CAAC1E,YAAD,CADV;AAEC2E,MAAAA,OAAO,EAAE,CAACxE,MAAD,CAFV;AAGCyE,MAAAA,YAAY,EAAE,CAACzE,MAAD;AAHf,KAAD;AAFoG,GAAD,CAArH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,MAAT,EAAiBoE,YAAjB", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Optional, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler } from 'primeng-lts/dom';\nimport * as i1 from 'primeng-lts/api';\n\nclass Ripple {\n    constructor(el, zone, config) {\n        this.el = el;\n        this.zone = zone;\n        this.config = config;\n    }\n    ngAfterViewInit() {\n        if (this.config && this.config.ripple) {\n            this.zone.runOutsideAngular(() => {\n                this.create();\n                this.mouseDownListener = this.onMouseDown.bind(this);\n                this.el.nativeElement.addEventListener('mousedown', this.mouseDownListener);\n            });\n        }\n    }\n    onMouseDown(event) {\n        let ink = this.getInk();\n        if (!ink || getComputedStyle(ink, null).display === 'none') {\n            return;\n        }\n        DomHandler.removeClass(ink, 'p-ink-active');\n        if (!DomHandler.getHeight(ink) && !DomHandler.getWidth(ink)) {\n            let d = Math.max(DomHandler.getOuterWidth(this.el.nativeElement), DomHandler.getOuterHeight(this.el.nativeElement));\n            ink.style.height = d + 'px';\n            ink.style.width = d + 'px';\n        }\n        let offset = DomHandler.getOffset(this.el.nativeElement);\n        let x = event.pageX - offset.left + document.body.scrollTop - DomHandler.getWidth(ink) / 2;\n        let y = event.pageY - offset.top + document.body.scrollLeft - DomHandler.getHeight(ink) / 2;\n        ink.style.top = y + 'px';\n        ink.style.left = x + 'px';\n        DomHandler.addClass(ink, 'p-ink-active');\n    }\n    getInk() {\n        for (let i = 0; i < this.el.nativeElement.children.length; i++) {\n            if (this.el.nativeElement.children[i].className.indexOf('p-ink') !== -1) {\n                return this.el.nativeElement.children[i];\n            }\n        }\n        return null;\n    }\n    resetInk() {\n        let ink = this.getInk();\n        if (ink) {\n            DomHandler.removeClass(ink, 'p-ink-active');\n        }\n    }\n    onAnimationEnd(event) {\n        DomHandler.removeClass(event.currentTarget, 'p-ink-active');\n    }\n    create() {\n        let ink = document.createElement('span');\n        ink.className = 'p-ink';\n        this.el.nativeElement.appendChild(ink);\n        this.animationListener = this.onAnimationEnd.bind(this);\n        ink.addEventListener('animationend', this.animationListener);\n    }\n    remove() {\n        let ink = this.getInk();\n        if (ink) {\n            this.el.nativeElement.removeEventListener('mousedown', this.mouseDownListener);\n            ink.removeEventListener('animationend', this.animationListener);\n            DomHandler.removeElement(ink);\n        }\n    }\n    ngOnDestroy() {\n        if (this.config && this.config.ripple) {\n            this.remove();\n        }\n    }\n}\nRipple.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: Ripple, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1.PrimeNGConfig, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nRipple.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"12.0.5\", type: Ripple, selector: \"[pRipple]\", host: { classAttribute: \"p-ripple p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: Ripple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pRipple]',\n                    host: {\n                        'class': 'p-ripple p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1.PrimeNGConfig, decorators: [{\n                    type: Optional\n                }] }]; } });\nclass RippleModule {\n}\nRippleModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: RippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nRippleModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: RippleModule, declarations: [Ripple], imports: [CommonModule], exports: [Ripple] });\nRippleModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: RippleModule, imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: RippleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Ripple],\n                    declarations: [Ripple]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Ripple, RippleModule };\n"]}, "metadata": {}, "sourceType": "module"}
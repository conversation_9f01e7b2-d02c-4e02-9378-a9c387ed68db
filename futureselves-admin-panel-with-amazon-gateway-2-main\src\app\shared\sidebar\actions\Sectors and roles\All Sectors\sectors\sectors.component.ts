import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
@Component({
  selector: 'app-sectors',
  templateUrl: './sectors.component.html',
  styleUrls: ['./sectors.component.scss']
})
export class SectorsComponent implements OnInit {

  p: number = 1;
  p1: number = 1;
  term: string;
  showForm = true;
  submitted = false;
  isReadonly = false;
  title = 'View';
  SectorData: any;
  sectorId: any;
  contentVisible = false;

  constructor(private formBuilder: FormBuilder,
    private dataTransferService : DataTransferService,
    private toastr:ToastrService,
    private router:Router,
    private activeRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private ngxSpinnerService: NgxSpinnerService) {     
     
     }

  ngOnInit(): void {
    setTimeout(()=>{
    this.contentVisible=true;
    },100)

    this.getAllSectorsData();
  }

  showAddNewSector(){
    const state={
      title:'Add New',
    }
    this.router.navigate([`actions/sectors/add-edit-sector`],{state});
  }

  showEditSector(sector_id:any,formSector:any){
    const state={
      title:'Edit',
      sectorId:sector_id,
      sector:formSector
    }
    this.router.navigate([`actions/sectors/add-edit-sector`],{state});
  }

  showRolesForm(sector_id:any){
    console.log("sector_id : ",sector_id);
    const state={
      sectorId:sector_id
    }
    this.router.navigate([`actions/sectors/roles`],{state});
  }

  getAllSectorsData(){
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getIndustryData().subscribe({
      next:(res:any)=>{
        if (res.status === 200) {
          this.SectorData = res.data.sort((a:any, b:any) => {
            const dateA = a.IN_createdAt ? new Date(a.IN_createdAt).getTime() : 0;
            const dateB = b.IN_createdAt ? new Date(b.IN_createdAt).getTime() : 0;
            
            // If either date is missing, push it to the end
            if (!dateA) return 1;
            if (!dateB) return -1;
            
            // Sort by date in descending order
            return dateB - dateA;
          });
          console.log("SectorData : ",this.SectorData);
          this.ngxSpinnerService.hide('globalSpinner');
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Failed to fetch sectors. Status:', res.status);
        }
      },
      error: (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error occurred while fetching sectors:', error);
      },
    })
  }
  showDeleteSectorModal(sector_id:any){
    this.sectorId=sector_id;
  }

  deleteSector(){
    console.log("sectorId",this.sectorId);
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.deleteIndustryData(this.sectorId).subscribe({
      next:(res:any)=>{
        if(res.statusCode=200){
          this.ngxSpinnerService.hide('globalSpinner');
          this.getAllSectorsData();
          this.toastr.success("Sector deleted successfully")
        }
      else {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error("Couldn't delete.  Status:", res.status);
        this.toastr.error("Couldn't delete.")

      }
      }
    })
  }

    }
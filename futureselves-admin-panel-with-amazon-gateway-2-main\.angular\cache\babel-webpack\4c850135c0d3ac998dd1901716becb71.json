{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Injectable, Inject, Optional, forwardRef, Directive, Input, Output, HostListener, Pipe, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\nconst NGX_MASK_CONFIG = new InjectionToken('ngx-mask config');\nconst NEW_CONFIG = new InjectionToken('new ngx-mask config');\nconst INITIAL_CONFIG = new InjectionToken('initial ngx-mask config');\nconst initialConfig = {\n  suffix: '',\n  prefix: '',\n  thousandSeparator: ' ',\n  decimalMarker: ['.', ','],\n  clearIfNotMatch: false,\n  showTemplate: false,\n  showMaskTyped: false,\n  placeHolderCharacter: '_',\n  dropSpecialCharacters: true,\n  hiddenInput: undefined,\n  shownMaskExpression: '',\n  separatorLimit: '',\n  allowNegativeNumbers: false,\n  validation: true,\n  // eslint-disable-next-line @typescript-eslint/quotes\n  specialCharacters: ['-', '/', '(', ')', '.', ':', ' ', '+', ',', '@', '[', ']', '\"', \"'\"],\n  leadZeroDateTime: false,\n  apm: false,\n  leadZero: false,\n  keepCharacterPositions: false,\n  triggerOnMaskChange: false,\n  inputTransformFn: value => value,\n  outputTransformFn: value => value,\n  maskFilled: new EventEmitter(),\n  patterns: {\n    '0': {\n      pattern: new RegExp('\\\\d')\n    },\n    '9': {\n      pattern: new RegExp('\\\\d'),\n      optional: true\n    },\n    X: {\n      pattern: new RegExp('\\\\d'),\n      symbol: '*'\n    },\n    A: {\n      pattern: new RegExp('[a-zA-Z0-9]')\n    },\n    S: {\n      pattern: new RegExp('[a-zA-Z]')\n    },\n    U: {\n      pattern: new RegExp('[A-Z]')\n    },\n    L: {\n      pattern: new RegExp('[a-z]')\n    },\n    d: {\n      pattern: new RegExp('\\\\d')\n    },\n    m: {\n      pattern: new RegExp('\\\\d')\n    },\n    M: {\n      pattern: new RegExp('\\\\d')\n    },\n    H: {\n      pattern: new RegExp('\\\\d')\n    },\n    h: {\n      pattern: new RegExp('\\\\d')\n    },\n    s: {\n      pattern: new RegExp('\\\\d')\n    }\n  }\n};\nconst timeMasks = [\"Hh:m0:s0\"\n/* HOURS_MINUTES_SECONDS */\n, \"Hh:m0\"\n/* HOURS_MINUTES */\n, \"m0:s0\"\n/* MINUTES_SECONDS */\n];\nconst withoutValidation = [\"percent\"\n/* PERCENT */\n, \"Hh\"\n/* HOURS_HOUR */\n, \"s0\"\n/* SECONDS */\n, \"m0\"\n/* MINUTES */\n, \"separator\"\n/* SEPARATOR */\n, \"d0/M0/0000\"\n/* DAYS_MONTHS_YEARS */\n, \"d0/M0\"\n/* DAYS_MONTHS */\n, \"d0\"\n/* DAYS */\n, \"M0\"\n/* MONTHS */\n];\n\nclass NgxMaskApplierService {\n  constructor(_config) {\n    this._config = _config;\n    this._shift = new Set();\n    this.plusOnePosition = false;\n    this.maskExpression = '';\n    this.actualValue = '';\n    this.showKeepCharacterExp = '';\n    this.shownMaskExpression = '';\n    this.deletedSpecialCharacter = false;\n\n    this._formatWithSeparators = (str, thousandSeparatorChar, decimalChars, precision) => {\n      let x = [];\n      let decimalChar = '';\n\n      if (Array.isArray(decimalChars)) {\n        const regExp = new RegExp(decimalChars.map(v => '[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v).join('|'));\n        x = str.split(regExp);\n        decimalChar = str.match(regExp)?.[0] ?? \"\"\n        /* EMPTY_STRING */\n        ;\n      } else {\n        x = str.split(decimalChars);\n        decimalChar = decimalChars;\n      }\n\n      const decimals = x.length > 1 ? `${decimalChar}${x[1]}` : \"\"\n      /* EMPTY_STRING */\n      ;\n      let res = x[0] ?? \"\"\n      /* EMPTY_STRING */\n      ;\n      const separatorLimit = this.separatorLimit.replace(/\\s/g, \"\"\n      /* EMPTY_STRING */\n      );\n\n      if (separatorLimit && +separatorLimit) {\n        if (res[0] === \"-\"\n        /* MINUS */\n        ) {\n          res = `-${res.slice(1, res.length).slice(0, separatorLimit.length)}`;\n        } else {\n          res = res.slice(0, separatorLimit.length);\n        }\n      }\n\n      const rgx = /(\\d+)(\\d{3})/;\n\n      while (thousandSeparatorChar && rgx.test(res)) {\n        res = res.replace(rgx, '$1' + thousandSeparatorChar + '$2');\n      }\n\n      if (precision === undefined) {\n        return res + decimals;\n      } else if (precision === 0) {\n        return res;\n      }\n\n      return res + decimals.substring(0, precision + 1);\n    };\n\n    this.percentage = str => {\n      const sanitizedStr = str.replace(',', '.');\n      const value = Number(sanitizedStr);\n      return !isNaN(value) && value >= 0 && value <= 100;\n    };\n\n    this.getPrecision = maskExpression => {\n      const x = maskExpression.split(\".\"\n      /* DOT */\n      );\n\n      if (x.length > 1) {\n        return Number(x[x.length - 1]);\n      }\n\n      return Infinity;\n    };\n\n    this.checkAndRemoveSuffix = inputValue => {\n      for (let i = this.suffix?.length - 1; i >= 0; i--) {\n        const substr = this.suffix.substring(i, this.suffix?.length);\n\n        if (inputValue.includes(substr) && i !== this.suffix?.length - 1 && (i - 1 < 0 || !inputValue.includes(this.suffix.substring(i - 1, this.suffix?.length)))) {\n          return inputValue.replace(substr, \"\"\n          /* EMPTY_STRING */\n          );\n        }\n      }\n\n      return inputValue;\n    };\n\n    this.checkInputPrecision = (inputValue, precision, decimalMarker) => {\n      if (precision < Infinity) {\n        // TODO need think about decimalMarker\n        if (Array.isArray(decimalMarker)) {\n          const marker = decimalMarker.find(dm => dm !== this.thousandSeparator); // eslint-disable-next-line no-param-reassign\n\n          decimalMarker = marker ? marker : decimalMarker[0];\n        }\n\n        const precisionRegEx = new RegExp(this._charToRegExpExpression(decimalMarker) + `\\\\d{${precision}}.*$`);\n        const precisionMatch = inputValue.match(precisionRegEx);\n        const precisionMatchLength = (precisionMatch && precisionMatch[0]?.length) ?? 0;\n\n        if (precisionMatchLength - 1 > precision) {\n          const diff = precisionMatchLength - 1 - precision; // eslint-disable-next-line no-param-reassign\n\n          inputValue = inputValue.substring(0, inputValue.length - diff);\n        }\n\n        if (precision === 0 && this._compareOrIncludes(inputValue[inputValue.length - 1], decimalMarker, this.thousandSeparator)) {\n          // eslint-disable-next-line no-param-reassign\n          inputValue = inputValue.substring(0, inputValue.length - 1);\n        }\n      }\n\n      return inputValue;\n    };\n\n    this.dropSpecialCharacters = this._config.dropSpecialCharacters;\n    this.hiddenInput = this._config.hiddenInput;\n    this.clearIfNotMatch = this._config.clearIfNotMatch;\n    this.specialCharacters = this._config.specialCharacters;\n    this.patterns = this._config.patterns;\n    this.prefix = this._config.prefix;\n    this.suffix = this._config.suffix;\n    this.thousandSeparator = this._config.thousandSeparator;\n    this.decimalMarker = this._config.decimalMarker;\n    this.showMaskTyped = this._config.showMaskTyped;\n    this.placeHolderCharacter = this._config.placeHolderCharacter;\n    this.validation = this._config.validation;\n    this.separatorLimit = this._config.separatorLimit;\n    this.allowNegativeNumbers = this._config.allowNegativeNumbers;\n    this.leadZeroDateTime = this._config.leadZeroDateTime;\n    this.leadZero = this._config.leadZero;\n    this.apm = this._config.apm;\n    this.inputTransformFn = this._config.inputTransformFn;\n    this.outputTransformFn = this._config.outputTransformFn;\n    this.keepCharacterPositions = this._config.keepCharacterPositions;\n  }\n\n  applyMaskWithPattern(inputValue, maskAndPattern) {\n    const [mask, customPattern] = maskAndPattern;\n    this.customPattern = customPattern;\n    return this.applyMask(inputValue, mask);\n  }\n\n  applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false, // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n  cb = () => {}) {\n    if (!maskExpression || typeof inputValue !== 'string') {\n      return \"\"\n      /* EMPTY_STRING */\n      ;\n    }\n\n    let cursor = 0;\n    let result = '';\n    let multi = false;\n    let backspaceShift = false;\n    let shift = 1;\n    let stepBack = false;\n\n    if (inputValue.slice(0, this.prefix.length) === this.prefix) {\n      // eslint-disable-next-line no-param-reassign\n      inputValue = inputValue.slice(this.prefix.length, inputValue.length);\n    }\n\n    if (!!this.suffix && inputValue?.length > 0) {\n      // eslint-disable-next-line no-param-reassign\n      inputValue = this.checkAndRemoveSuffix(inputValue);\n    }\n\n    if (inputValue === '(' && this.prefix) {\n      // eslint-disable-next-line no-param-reassign\n      inputValue = '';\n    }\n\n    const inputArray = inputValue.toString().split(\"\"\n    /* EMPTY_STRING */\n    );\n\n    if (this.allowNegativeNumbers && inputValue.slice(cursor, cursor + 1) === \"-\"\n    /* MINUS */\n    ) {\n      // eslint-disable-next-line no-param-reassign\n      result += inputValue.slice(cursor, cursor + 1);\n    }\n\n    if (maskExpression === \"IP\"\n    /* IP */\n    ) {\n      const valuesIP = inputValue.split(\".\"\n      /* DOT */\n      );\n      this.ipError = this._validIP(valuesIP); // eslint-disable-next-line no-param-reassign\n\n      maskExpression = '***************';\n    }\n\n    const arr = [];\n\n    for (let i = 0; i < inputValue.length; i++) {\n      if (inputValue[i]?.match('\\\\d')) {\n        arr.push(inputValue[i] ?? \"\"\n        /* EMPTY_STRING */\n        );\n      }\n    }\n\n    if (maskExpression === \"CPF_CNPJ\"\n    /* CPF_CNPJ */\n    ) {\n      this.cpfCnpjError = arr.length !== 11 && arr.length !== 14;\n\n      if (arr.length > 11) {\n        // eslint-disable-next-line no-param-reassign\n        maskExpression = '00.000.000/0000-00';\n      } else {\n        // eslint-disable-next-line no-param-reassign\n        maskExpression = '000.000.000-00';\n      }\n    }\n\n    if (maskExpression.startsWith(\"percent\"\n    /* PERCENT */\n    )) {\n      if (inputValue.match('[a-z]|[A-Z]') || // eslint-disable-next-line no-useless-escape\n      inputValue.match(/[-!$%^&*()_+|~=`{}\\[\\]:\";'<>?,\\/.]/) && !backspaced) {\n        // eslint-disable-next-line no-param-reassign\n        inputValue = this._stripToDecimal(inputValue);\n        const precision = this.getPrecision(maskExpression); // eslint-disable-next-line no-param-reassign\n\n        inputValue = this.checkInputPrecision(inputValue, precision, this.decimalMarker);\n      }\n\n      const decimalMarker = typeof this.decimalMarker === 'string' ? this.decimalMarker : \".\"\n      /* DOT */\n      ;\n\n      if (inputValue.indexOf(decimalMarker) > 0 && !this.percentage(inputValue.substring(0, inputValue.indexOf(decimalMarker)))) {\n        let base = inputValue.substring(0, inputValue.indexOf(decimalMarker) - 1);\n\n        if (this.allowNegativeNumbers && inputValue.slice(cursor, cursor + 1) === \"-\"\n        /* MINUS */\n        && !backspaced) {\n          base = inputValue.substring(0, inputValue.indexOf(decimalMarker));\n        } // eslint-disable-next-line no-param-reassign\n\n\n        inputValue = `${base}${inputValue.substring(inputValue.indexOf(decimalMarker), inputValue.length)}`;\n      }\n\n      let value = '';\n      this.allowNegativeNumbers && inputValue.slice(cursor, cursor + 1) === \"-\"\n      /* MINUS */\n      ? value = inputValue.slice(cursor + 1, cursor + inputValue.length) : value = inputValue;\n\n      if (this.percentage(value)) {\n        result = this._splitPercentZero(inputValue);\n      } else {\n        result = this._splitPercentZero(inputValue.substring(0, inputValue.length - 1));\n      }\n    } else if (maskExpression.startsWith(\"separator\"\n    /* SEPARATOR */\n    )) {\n      if (inputValue.match('[wа-яА-Я]') || inputValue.match('[ЁёА-я]') || inputValue.match('[a-z]|[A-Z]') || inputValue.match(/[-@#!$%\\\\^&*()_£¬'+|~=`{}\\]:\";<>.?/]/) || inputValue.match('[^A-Za-z0-9,]')) {\n        // eslint-disable-next-line no-param-reassign\n        inputValue = this._stripToDecimal(inputValue);\n      }\n\n      const precision = this.getPrecision(maskExpression);\n      const decimalMarker = Array.isArray(this.decimalMarker) ? \".\"\n      /* DOT */\n      : this.decimalMarker;\n\n      if (precision === 0) {\n        // eslint-disable-next-line no-param-reassign\n        inputValue = this.allowNegativeNumbers ? inputValue.length > 2 && inputValue[0] === \"-\"\n        /* MINUS */\n        && inputValue[1] === \"0\"\n        /* NUMBER_ZERO */\n        && inputValue[2] !== this.thousandSeparator && inputValue[2] !== \",\"\n        /* COMMA */\n        && inputValue[2] !== \".\"\n        /* DOT */\n        ? '-' + inputValue.slice(2, inputValue.length) : inputValue[0] === \"0\"\n        /* NUMBER_ZERO */\n        && inputValue.length > 1 && inputValue[1] !== this.thousandSeparator && inputValue[1] !== \",\"\n        /* COMMA */\n        && inputValue[1] !== \".\"\n        /* DOT */\n        ? inputValue.slice(1, inputValue.length) : inputValue : inputValue.length > 1 && inputValue[0] === \"0\"\n        /* NUMBER_ZERO */\n        && inputValue[1] !== this.thousandSeparator && inputValue[1] !== \",\"\n        /* COMMA */\n        && inputValue[1] !== \".\"\n        /* DOT */\n        ? inputValue.slice(1, inputValue.length) : inputValue;\n      } else {\n        // eslint-disable-next-line no-param-reassign\n        if (inputValue[0] === decimalMarker && inputValue.length > 1) {\n          // eslint-disable-next-line no-param-reassign\n          inputValue = \"0\"\n          /* NUMBER_ZERO */\n          + inputValue.slice(0, inputValue.length + 1);\n          this.plusOnePosition = true;\n        }\n\n        if (inputValue[0] === \"0\"\n        /* NUMBER_ZERO */\n        && inputValue[1] !== decimalMarker && inputValue[1] !== this.thousandSeparator) {\n          // eslint-disable-next-line no-param-reassign\n          inputValue = inputValue.length > 1 ? inputValue.slice(0, 1) + decimalMarker + inputValue.slice(1, inputValue.length + 1) : inputValue;\n          this.plusOnePosition = true;\n        }\n\n        if (this.allowNegativeNumbers && inputValue[0] === \"-\"\n        /* MINUS */\n        && (inputValue[1] === decimalMarker || inputValue[1] === \"0\"\n        /* NUMBER_ZERO */\n        )) {\n          // eslint-disable-next-line no-param-reassign\n          inputValue = inputValue[1] === decimalMarker && inputValue.length > 2 ? inputValue.slice(0, 1) + \"0\"\n          /* NUMBER_ZERO */\n          + inputValue.slice(1, inputValue.length) : inputValue[1] === \"0\"\n          /* NUMBER_ZERO */\n          && inputValue.length > 2 && inputValue[2] !== decimalMarker ? inputValue.slice(0, 2) + decimalMarker + inputValue.slice(2, inputValue.length) : inputValue;\n          this.plusOnePosition = true;\n        }\n      }\n\n      if (backspaced) {\n        if (inputValue[0] === \"0\"\n        /* NUMBER_ZERO */\n        && inputValue[1] === this.decimalMarker && (inputValue[position] === \"0\"\n        /* NUMBER_ZERO */\n        || inputValue[position] === this.decimalMarker)) {\n          // eslint-disable-next-line no-param-reassign\n          inputValue = inputValue.slice(2, inputValue.length);\n        }\n\n        if (inputValue[0] === \"-\"\n        /* MINUS */\n        && inputValue[1] === \"0\"\n        /* NUMBER_ZERO */\n        && inputValue[2] === this.decimalMarker && (inputValue[position] === \"0\"\n        /* NUMBER_ZERO */\n        || inputValue[position] === this.decimalMarker)) {\n          // eslint-disable-next-line no-param-reassign\n          inputValue = \"-\"\n          /* MINUS */\n          + inputValue.slice(3, inputValue.length);\n        } // eslint-disable-next-line no-param-reassign\n\n\n        inputValue = this._compareOrIncludes(inputValue[inputValue.length - 1], this.decimalMarker, this.thousandSeparator) ? inputValue.slice(0, inputValue.length - 1) : inputValue;\n      } // TODO: we had different rexexps here for the different cases... but tests dont seam to bother - check this\n      //  separator: no COMMA, dot-sep: no SPACE, COMMA OK, comma-sep: no SPACE, COMMA OK\n\n\n      const thousandSeparatorCharEscaped = this._charToRegExpExpression(this.thousandSeparator);\n\n      let invalidChars = '@#!$%^&*()_+|~=`{}\\\\[\\\\]:\\\\s,\\\\.\";<>?\\\\/'.replace(thousandSeparatorCharEscaped, ''); //.replace(decimalMarkerEscaped, '');\n\n      if (Array.isArray(this.decimalMarker)) {\n        for (const marker of this.decimalMarker) {\n          invalidChars = invalidChars.replace(this._charToRegExpExpression(marker), \"\"\n          /* EMPTY_STRING */\n          );\n        }\n      } else {\n        invalidChars = invalidChars.replace(this._charToRegExpExpression(this.decimalMarker), '');\n      }\n\n      const invalidCharRegexp = new RegExp('[' + invalidChars + ']');\n\n      if (inputValue.match(invalidCharRegexp)) {\n        // eslint-disable-next-line no-param-reassign\n        inputValue = inputValue.substring(0, inputValue.length - 1);\n      } // eslint-disable-next-line no-param-reassign\n\n\n      inputValue = this.checkInputPrecision(inputValue, precision, this.decimalMarker);\n      const strForSep = inputValue.replace(new RegExp(thousandSeparatorCharEscaped, 'g'), '');\n      result = this._formatWithSeparators(strForSep, this.thousandSeparator, this.decimalMarker, precision);\n      const commaShift = result.indexOf(\",\"\n      /* COMMA */\n      ) - inputValue.indexOf(\",\"\n      /* COMMA */\n      );\n      const shiftStep = result.length - inputValue.length;\n\n      if (shiftStep > 0 && result[position] !== this.thousandSeparator) {\n        backspaceShift = true;\n        let _shift = 0;\n\n        do {\n          this._shift.add(position + _shift);\n\n          _shift++;\n        } while (_shift < shiftStep);\n      } else if (result[position - 1] === this.decimalMarker || shiftStep === -4 || shiftStep === -3 || result[position] === \",\"\n      /* COMMA */\n      ) {\n        this._shift.clear();\n\n        this._shift.add(position - 1);\n      } else if (commaShift !== 0 && position > 0 && !(result.indexOf(\",\"\n      /* COMMA */\n      ) >= position && position > 3) || !(result.indexOf(\".\"\n      /* DOT */\n      ) >= position && position > 3) && shiftStep <= 0) {\n        this._shift.clear();\n\n        backspaceShift = true;\n        shift = shiftStep; // eslint-disable-next-line no-param-reassign\n\n        position += shiftStep;\n\n        this._shift.add(position);\n      } else {\n        this._shift.clear();\n      }\n    } else {\n      for ( // eslint-disable-next-line\n      let i = 0, inputSymbol = inputArray[0]; i < inputArray.length; i++, inputSymbol = inputArray[i] ?? \"\"\n      /* EMPTY_STRING */\n      ) {\n        if (cursor === maskExpression.length) {\n          break;\n        }\n\n        const symbolStarInPattern = (\"*\"\n        /* SYMBOL_STAR */\n        in this.patterns);\n\n        if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? \"\"\n        /* EMPTY_STRING */\n        ) && maskExpression[cursor + 1] === \"?\"\n        /* SYMBOL_QUESTION */\n        ) {\n          result += inputSymbol;\n          cursor += 2;\n        } else if (maskExpression[cursor + 1] === \"*\"\n        /* SYMBOL_STAR */\n        && multi && this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? \"\"\n        /* EMPTY_STRING */\n        )) {\n          result += inputSymbol;\n          cursor += 3;\n          multi = false;\n        } else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? \"\"\n        /* EMPTY_STRING */\n        ) && maskExpression[cursor + 1] === \"*\"\n        /* SYMBOL_STAR */\n        && !symbolStarInPattern) {\n          result += inputSymbol;\n          multi = true;\n        } else if (maskExpression[cursor + 1] === \"?\"\n        /* SYMBOL_QUESTION */\n        && this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? \"\"\n        /* EMPTY_STRING */\n        )) {\n          result += inputSymbol;\n          cursor += 3;\n        } else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? \"\"\n        /* EMPTY_STRING */\n        )) {\n          if (maskExpression[cursor] === \"H\"\n          /* HOURS */\n          ) {\n            if (this.apm ? Number(inputSymbol) > 9 : Number(inputSymbol) > 2) {\n              // eslint-disable-next-line no-param-reassign\n              position = !this.leadZeroDateTime ? position + 1 : position;\n              cursor += 1;\n\n              this._shiftStep(maskExpression, cursor, inputArray.length);\n\n              i--;\n\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n\n              continue;\n            }\n          }\n\n          if (maskExpression[cursor] === \"h\"\n          /* HOUR */\n          ) {\n            if (this.apm ? result.length === 1 && Number(result) > 1 || result === '1' && Number(inputSymbol) > 2 || inputValue.slice(cursor - 1, cursor).length === 1 && Number(inputValue.slice(cursor - 1, cursor)) > 2 || inputValue.slice(cursor - 1, cursor) === '1' && Number(inputSymbol) > 2 : result === '2' && Number(inputSymbol) > 3 || (result.slice(cursor - 2, cursor) === '2' || result.slice(cursor - 3, cursor) === '2' || result.slice(cursor - 4, cursor) === '2' || result.slice(cursor - 1, cursor) === '2') && Number(inputSymbol) > 3 && cursor > 10) {\n              // eslint-disable-next-line no-param-reassign\n              position = position + 1;\n              cursor += 1;\n              i--;\n              continue;\n            }\n          }\n\n          if (maskExpression[cursor] === \"m\"\n          /* MINUTE */\n          || maskExpression[cursor] === \"s\"\n          /* SECOND */\n          ) {\n            if (Number(inputSymbol) > 5) {\n              // eslint-disable-next-line no-param-reassign\n              position = !this.leadZeroDateTime ? position + 1 : position;\n              cursor += 1;\n\n              this._shiftStep(maskExpression, cursor, inputArray.length);\n\n              i--;\n\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n\n              continue;\n            }\n          }\n\n          const daysCount = 31;\n          const inputValueCursor = inputValue[cursor];\n          const inputValueCursorPlusOne = inputValue[cursor + 1];\n          const inputValueCursorPlusTwo = inputValue[cursor + 2];\n          const inputValueCursorMinusOne = inputValue[cursor - 1];\n          const inputValueCursorMinusTwo = inputValue[cursor - 2];\n          const inputValueCursorMinusThree = inputValue[cursor - 3];\n          const inputValueSliceMinusThreeMinusOne = inputValue.slice(cursor - 3, cursor - 1);\n          const inputValueSliceMinusOnePlusOne = inputValue.slice(cursor - 1, cursor + 1);\n          const inputValueSliceCursorPlusTwo = inputValue.slice(cursor, cursor + 2);\n          const inputValueSliceMinusTwoCursor = inputValue.slice(cursor - 2, cursor);\n\n          if (maskExpression[cursor] === \"d\"\n          /* DAY */\n          ) {\n            const maskStartWithMonth = maskExpression.slice(0, 2) === \"M0\"\n            /* MONTHS */\n            ;\n            const startWithMonthInput = maskExpression.slice(0, 2) === \"M0\"\n            /* MONTHS */\n            && this.specialCharacters.includes(inputValueCursorMinusTwo);\n\n            if (Number(inputSymbol) > 3 && this.leadZeroDateTime || !maskStartWithMonth && (Number(inputValueSliceCursorPlusTwo) > daysCount || Number(inputValueSliceMinusOnePlusOne) > daysCount || this.specialCharacters.includes(inputValueCursorPlusOne)) || (startWithMonthInput ? Number(inputValueSliceMinusOnePlusOne) > daysCount || !this.specialCharacters.includes(inputValueCursor) && this.specialCharacters.includes(inputValueCursorPlusTwo) || this.specialCharacters.includes(inputValueCursor) : Number(inputValueSliceCursorPlusTwo) > daysCount || this.specialCharacters.includes(inputValueCursorPlusOne))) {\n              // eslint-disable-next-line no-param-reassign\n              position = !this.leadZeroDateTime ? position + 1 : position;\n              cursor += 1;\n\n              this._shiftStep(maskExpression, cursor, inputArray.length);\n\n              i--;\n\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n\n              continue;\n            }\n          }\n\n          if (maskExpression[cursor] === \"M\"\n          /* MONTH */\n          ) {\n            const monthsCount = 12; // mask without day\n\n            const withoutDays = cursor === 0 && (Number(inputSymbol) > 2 || Number(inputValueSliceCursorPlusTwo) > monthsCount || this.specialCharacters.includes(inputValueCursorPlusOne)); // day<10 && month<12 for input\n\n            const specialChart = maskExpression.slice(cursor + 2, cursor + 3);\n            const day1monthInput = inputValueSliceMinusThreeMinusOne.includes(specialChart) && (this.specialCharacters.includes(inputValueCursorMinusTwo) && Number(inputValueSliceMinusOnePlusOne) > monthsCount && !this.specialCharacters.includes(inputValueCursor) || this.specialCharacters.includes(inputValueCursor) || this.specialCharacters.includes(inputValueCursorMinusThree) && Number(inputValueSliceMinusTwoCursor) > monthsCount && !this.specialCharacters.includes(inputValueCursorMinusOne) || this.specialCharacters.includes(inputValueCursorMinusOne)); //  month<12 && day<10 for input\n\n            const day2monthInput = Number(inputValueSliceMinusThreeMinusOne) <= daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && this.specialCharacters.includes(inputValueCursorMinusOne) && (Number(inputValueSliceCursorPlusTwo) > monthsCount || this.specialCharacters.includes(inputValueCursorPlusOne)); // cursor === 5 && without days\n\n            const day2monthInputDot = Number(inputValueSliceCursorPlusTwo) > monthsCount && cursor === 5 || this.specialCharacters.includes(inputValueCursorPlusOne) && cursor === 5; // // day<10 && month<12 for paste whole data\n\n            const day1monthPaste = Number(inputValueSliceMinusThreeMinusOne) > daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && !this.specialCharacters.includes(inputValueSliceMinusTwoCursor) && Number(inputValueSliceMinusTwoCursor) > monthsCount; // 10<day<31 && month<12 for paste whole data\n\n            const day2monthPaste = Number(inputValueSliceMinusThreeMinusOne) <= daysCount && !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) && !this.specialCharacters.includes(inputValueCursorMinusOne) && Number(inputValueSliceMinusOnePlusOne) > monthsCount;\n\n            if (Number(inputSymbol) > 1 && this.leadZeroDateTime || withoutDays || day1monthInput || day2monthPaste || day1monthPaste || day2monthInput || day2monthInputDot && !this.leadZeroDateTime) {\n              // eslint-disable-next-line no-param-reassign\n              position = !this.leadZeroDateTime ? position + 1 : position;\n              cursor += 1;\n\n              this._shiftStep(maskExpression, cursor, inputArray.length);\n\n              i--;\n\n              if (this.leadZeroDateTime) {\n                result += '0';\n              }\n\n              continue;\n            }\n          }\n\n          result += inputSymbol;\n          cursor++;\n        } else if (inputSymbol === \" \"\n        /* WHITE_SPACE */\n        && maskExpression[cursor] === \" \"\n        /* WHITE_SPACE */\n        || inputSymbol === \"/\"\n        /* SLASH */\n        && maskExpression[cursor] === \"/\"\n        /* SLASH */\n        ) {\n          result += inputSymbol;\n          cursor++;\n        } else if (this.specialCharacters.indexOf(maskExpression[cursor] ?? \"\"\n        /* EMPTY_STRING */\n        ) !== -1) {\n          result += maskExpression[cursor];\n          cursor++;\n\n          this._shiftStep(maskExpression, cursor, inputArray.length);\n\n          i--;\n        } else if (maskExpression[cursor] === \"9\"\n        /* NUMBER_NINE */\n        && this.showMaskTyped) {\n          this._shiftStep(maskExpression, cursor, inputArray.length);\n        } else if (this.patterns[maskExpression[cursor] ?? \"\"\n        /* EMPTY_STRING */\n        ] && this.patterns[maskExpression[cursor] ?? \"\"\n        /* EMPTY_STRING */\n        ]?.optional) {\n          if (!!inputArray[cursor] && maskExpression !== '***************' && maskExpression !== '000.000.000-00' && maskExpression !== '00.000.000/0000-00' && !maskExpression.match(/^9+\\.0+$/) && !this.patterns[maskExpression[cursor] ?? \"\"\n          /* EMPTY_STRING */\n          ]?.optional) {\n            result += inputArray[cursor];\n          }\n\n          if (maskExpression.includes(\"9\"\n          /* NUMBER_NINE */\n          + \"*\"\n          /* SYMBOL_STAR */\n          ) && maskExpression.includes(\"0\"\n          /* NUMBER_ZERO */\n          + \"*\"\n          /* SYMBOL_STAR */\n          )) {\n            cursor++;\n          }\n\n          cursor++;\n          i--;\n        } else if (this.maskExpression[cursor + 1] === \"*\"\n        /* SYMBOL_STAR */\n        && this._findSpecialChar(this.maskExpression[cursor + 2] ?? \"\"\n        /* EMPTY_STRING */\n        ) && this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] && multi) {\n          cursor += 3;\n          result += inputSymbol;\n        } else if (this.maskExpression[cursor + 1] === \"?\"\n        /* SYMBOL_QUESTION */\n        && this._findSpecialChar(this.maskExpression[cursor + 2] ?? \"\"\n        /* EMPTY_STRING */\n        ) && this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] && multi) {\n          cursor += 3;\n          result += inputSymbol;\n        } else if (this.showMaskTyped && this.specialCharacters.indexOf(inputSymbol) < 0 && inputSymbol !== this.placeHolderCharacter && this.placeHolderCharacter.length === 1) {\n          stepBack = true;\n        }\n      }\n    }\n\n    if (result.length + 1 === maskExpression.length && this.specialCharacters.indexOf(maskExpression[maskExpression.length - 1] ?? \"\"\n    /* EMPTY_STRING */\n    ) !== -1) {\n      result += maskExpression[maskExpression.length - 1];\n    }\n\n    let newPosition = position + 1;\n\n    while (this._shift.has(newPosition)) {\n      shift++;\n      newPosition++;\n    }\n\n    let actualShift = justPasted && !maskExpression.startsWith(\"separator\"\n    /* SEPARATOR */\n    ) ? cursor : this._shift.has(position) ? shift : 0;\n\n    if (stepBack) {\n      actualShift--;\n    }\n\n    cb(actualShift, backspaceShift);\n\n    if (shift < 0) {\n      this._shift.clear();\n    }\n\n    let onlySpecial = false;\n\n    if (backspaced) {\n      onlySpecial = inputArray.every(char => this.specialCharacters.includes(char));\n    }\n\n    let res = `${this.prefix}${onlySpecial ? \"\"\n    /* EMPTY_STRING */\n    : result}${this.showMaskTyped ? '' : this.suffix}`;\n\n    if (result.length === 0) {\n      res = !this.dropSpecialCharacters ? `${this.prefix}${result}` : `${result}`;\n    }\n\n    if (result.includes(\"-\"\n    /* MINUS */\n    ) && this.prefix && this.allowNegativeNumbers) {\n      if (backspaced && result === \"-\"\n      /* MINUS */\n      ) {\n        return '';\n      }\n\n      res = `${\"-\"\n      /* MINUS */\n      }${this.prefix}${result.split(\"-\"\n      /* MINUS */\n      ).join(\"\"\n      /* EMPTY_STRING */\n      )}${this.suffix}`;\n    }\n\n    return res;\n  }\n\n  _findDropSpecialChar(inputSymbol) {\n    if (Array.isArray(this.dropSpecialCharacters)) {\n      return this.dropSpecialCharacters.find(val => val === inputSymbol);\n    }\n\n    return this._findSpecialChar(inputSymbol);\n  }\n\n  _findSpecialChar(inputSymbol) {\n    return this.specialCharacters.find(val => val === inputSymbol);\n  }\n\n  _checkSymbolMask(inputSymbol, maskSymbol) {\n    this.patterns = this.customPattern ? this.customPattern : this.patterns;\n    return (this.patterns[maskSymbol]?.pattern && this.patterns[maskSymbol]?.pattern.test(inputSymbol)) ?? false;\n  }\n\n  _stripToDecimal(str) {\n    return str.split(\"\"\n    /* EMPTY_STRING */\n    ).filter((i, idx) => {\n      const isDecimalMarker = typeof this.decimalMarker === 'string' ? i === this.decimalMarker : // TODO (inepipenko) use utility type\n      this.decimalMarker.includes(i);\n      return i.match('^-?\\\\d') || i === this.thousandSeparator || isDecimalMarker || i === \"-\"\n      /* MINUS */\n      && idx === 0 && this.allowNegativeNumbers;\n    }).join(\"\"\n    /* EMPTY_STRING */\n    );\n  }\n\n  _charToRegExpExpression(char) {\n    // if (Array.isArray(char)) {\n    // \treturn char.map((v) => ('[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v)).join('|');\n    // }\n    if (char) {\n      const charsToEscape = '[\\\\^$.|?*+()';\n      return char === ' ' ? '\\\\s' : charsToEscape.indexOf(char) >= 0 ? `\\\\${char}` : char;\n    }\n\n    return char;\n  }\n\n  _shiftStep(maskExpression, cursor, inputLength) {\n    const shiftStep = /[*?]/g.test(maskExpression.slice(0, cursor)) ? inputLength : cursor;\n\n    this._shift.add(shiftStep + this.prefix.length || 0);\n  }\n\n  _compareOrIncludes(value, comparedValue, excludedValue) {\n    return Array.isArray(comparedValue) ? comparedValue.filter(v => v !== excludedValue).includes(value) : value === comparedValue;\n  }\n\n  _validIP(valuesIP) {\n    return !(valuesIP.length === 4 && !valuesIP.some((value, index) => {\n      if (valuesIP.length !== index + 1) {\n        return value === \"\"\n        /* EMPTY_STRING */\n        || Number(value) > 255;\n      }\n\n      return value === \"\"\n      /* EMPTY_STRING */\n      || Number(value.substring(0, 3)) > 255;\n    }));\n  }\n\n  _splitPercentZero(value) {\n    const decimalIndex = typeof this.decimalMarker === 'string' ? value.indexOf(this.decimalMarker) : value.indexOf(\".\"\n    /* DOT */\n    );\n\n    if (decimalIndex === -1) {\n      const parsedValue = parseInt(value, 10);\n      return isNaN(parsedValue) ? \"\"\n      /* EMPTY_STRING */\n      : parsedValue.toString();\n    } else {\n      const integerPart = parseInt(value.substring(0, decimalIndex), 10);\n      const decimalPart = value.substring(decimalIndex + 1);\n      const integerString = isNaN(integerPart) ? '' : integerPart.toString();\n      const decimal = typeof this.decimalMarker === 'string' ? this.decimalMarker : \".\"\n      /* DOT */\n      ;\n      return integerString === \"\"\n      /* EMPTY_STRING */\n      ? \"\"\n      /* EMPTY_STRING */\n      : integerString + decimal + decimalPart;\n    }\n  }\n\n}\n\nNgxMaskApplierService.ɵfac = function NgxMaskApplierService_Factory(t) {\n  return new (t || NgxMaskApplierService)(i0.ɵɵinject(NGX_MASK_CONFIG));\n};\n\nNgxMaskApplierService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxMaskApplierService,\n  factory: NgxMaskApplierService.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskApplierService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NGX_MASK_CONFIG]\n      }]\n    }];\n  }, null);\n})();\n\nclass NgxMaskService extends NgxMaskApplierService {\n  constructor(document, _config, _elementRef, _renderer) {\n    super(_config);\n    this.document = document;\n    this._config = _config;\n    this._elementRef = _elementRef;\n    this._renderer = _renderer;\n    this.isNumberValue = false;\n    this.maskIsShown = '';\n    this.selStart = null;\n    this.selEnd = null;\n    /**\n     * Whether we are currently in writeValue function, in this case when applying the mask we don't want to trigger onChange function,\n     * since writeValue should be a one way only process of writing the DOM value based on the Angular model value.\n     */\n\n    this.writingValue = false;\n    this.maskChanged = false;\n    this._maskExpressionArray = [];\n    this.triggerOnMaskChange = false;\n    this._emitValue = false;\n    this._previousValue = '';\n    this._currentValue = ''; // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n\n    this.onChange = _ => {};\n  } // eslint-disable-next-line complexity\n\n\n  applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false, // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n  cb = () => {}) {\n    if (!maskExpression) {\n      return inputValue !== this.actualValue ? this.actualValue : inputValue;\n    }\n\n    this.maskIsShown = this.showMaskTyped ? this.showMaskInInput() : \"\"\n    /* EMPTY_STRING */\n    ;\n\n    if (this.maskExpression === \"IP\"\n    /* IP */\n    && this.showMaskTyped) {\n      this.maskIsShown = this.showMaskInInput(inputValue || \"#\"\n      /* HASH */\n      );\n    }\n\n    if (this.maskExpression === \"CPF_CNPJ\"\n    /* CPF_CNPJ */\n    && this.showMaskTyped) {\n      this.maskIsShown = this.showMaskInInput(inputValue || \"#\"\n      /* HASH */\n      );\n    }\n\n    if (!inputValue && this.showMaskTyped) {\n      this.formControlResult(this.prefix);\n      return this.prefix + this.maskIsShown + this.suffix;\n    }\n\n    const getSymbol = !!inputValue && typeof this.selStart === 'number' ? inputValue[this.selStart] ?? \"\"\n    /* EMPTY_STRING */\n    : \"\"\n    /* EMPTY_STRING */\n    ;\n    let newInputValue = '';\n\n    if (this.hiddenInput !== undefined && !this.writingValue) {\n      let actualResult = inputValue && inputValue.length === 1 ? inputValue.split(\"\"\n      /* EMPTY_STRING */\n      ) : this.actualValue.split(\"\"\n      /* EMPTY_STRING */\n      ); // eslint-disable  @typescript-eslint/no-unused-expressions\n      // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n\n      if (typeof this.selStart === 'object' && typeof this.selEnd === 'object') {\n        this.selStart = Number(this.selStart);\n        this.selEnd = Number(this.selEnd);\n      } else {\n        inputValue !== \"\"\n        /* EMPTY_STRING */\n        && actualResult.length ? typeof this.selStart === 'number' && typeof this.selEnd === 'number' ? inputValue.length > actualResult.length ? actualResult.splice(this.selStart, 0, getSymbol) : inputValue.length < actualResult.length ? actualResult.length - inputValue.length === 1 ? backspaced ? actualResult.splice(this.selStart - 1, 1) : actualResult.splice(inputValue.length - 1, 1) : actualResult.splice(this.selStart, this.selEnd - this.selStart) : null : null : actualResult = [];\n      }\n\n      if (this.showMaskTyped) {\n        if (!this.hiddenInput) {\n          // eslint-disable-next-line no-param-reassign\n          inputValue = this.removeMask(inputValue);\n        }\n      } // eslint-enable  @typescript-eslint/no-unused-expressions\n\n\n      newInputValue = this.actualValue.length && actualResult.length <= inputValue.length ? this.shiftTypedSymbols(actualResult.join(\"\"\n      /* EMPTY_STRING */\n      )) : inputValue;\n    }\n\n    if (justPasted && (this.hiddenInput || !this.hiddenInput)) {\n      newInputValue = inputValue;\n    }\n\n    if (backspaced && this.specialCharacters.indexOf(this.maskExpression[position] ?? \"\"\n    /* EMPTY_STRING */\n    ) !== -1 && this.showMaskTyped) {\n      newInputValue = this._currentValue;\n    }\n\n    if (this.deletedSpecialCharacter && position) {\n      if (this.specialCharacters.includes(this.actualValue.slice(position, position + 1))) {\n        // eslint-disable-next-line no-param-reassign\n        position = position + 1;\n      } else if (maskExpression.slice(position - 1, position + 1) !== \"M0\"\n      /* MONTHS */\n      ) {\n        // eslint-disable-next-line no-param-reassign\n        position = position - 2;\n      } // eslint-disable-next-line no-param-reassign\n\n\n      this.deletedSpecialCharacter = false;\n    }\n\n    if (this.showMaskTyped && this.placeHolderCharacter.length === 1 && !this.leadZeroDateTime) {\n      // eslint-disable-next-line no-param-reassign\n      inputValue = this.removeMask(inputValue);\n    }\n\n    if (this.maskChanged) {\n      newInputValue = inputValue;\n    } else {\n      newInputValue = Boolean(newInputValue) && newInputValue.length ? newInputValue : inputValue;\n    }\n\n    if (this.showMaskTyped && this.keepCharacterPositions && this.actualValue && !justPasted) {\n      const value = this.dropSpecialCharacters ? this.removeMask(this.actualValue) : this.actualValue;\n      this.formControlResult(value);\n      return this.actualValue ? this.actualValue : this.prefix + this.maskIsShown + this.suffix;\n    }\n\n    const result = super.applyMask(newInputValue, maskExpression, position, justPasted, backspaced, cb);\n    this.actualValue = this.getActualValue(result); // handle some separator implications:\n    // a.) adjust decimalMarker default (. -> ,) if thousandSeparator is a dot\n\n    if (this.thousandSeparator === \".\"\n    /* DOT */\n    && this.decimalMarker === \".\"\n    /* DOT */\n    ) {\n      this.decimalMarker = \",\"\n      /* COMMA */\n      ;\n    } // b) remove decimal marker from list of special characters to mask\n\n\n    if (this.maskExpression.startsWith(\"separator\"\n    /* SEPARATOR */\n    ) && this.dropSpecialCharacters === true) {\n      this.specialCharacters = this.specialCharacters.filter(item => !this._compareOrIncludes(item, this.decimalMarker, this.thousandSeparator) //item !== this.decimalMarker, // !\n      );\n    }\n\n    if (result || result === '') {\n      this._previousValue = this._currentValue;\n      this._currentValue = result;\n      this._emitValue = this._previousValue !== this._currentValue || this.maskChanged || this._previousValue === this._currentValue && justPasted;\n    }\n\n    this._emitValue ? this.formControlResult(result) : '';\n\n    if (!this.showMaskTyped || this.showMaskTyped && this.hiddenInput) {\n      if (this.hiddenInput) {\n        if (backspaced) {\n          return this.hideInput(result, this.maskExpression);\n        }\n\n        return this.hideInput(result, this.maskExpression) + this.maskIsShown.slice(result.length);\n      }\n\n      return result;\n    }\n\n    const resLen = result.length;\n    const prefNmask = this.prefix + this.maskIsShown + this.suffix;\n\n    if (this.maskExpression.includes(\"H\"\n    /* HOURS */\n    )) {\n      const countSkipedSymbol = this._numberSkipedSymbols(result);\n\n      return result + prefNmask.slice(resLen + countSkipedSymbol);\n    } else if (this.maskExpression === \"IP\"\n    /* IP */\n    || this.maskExpression === \"CPF_CNPJ\"\n    /* CPF_CNPJ */\n    ) {\n      return result + prefNmask;\n    }\n\n    return result + prefNmask.slice(resLen);\n  } // get the number of characters that were shifted\n\n\n  _numberSkipedSymbols(value) {\n    const regex = /(^|\\D)(\\d\\D)/g;\n    let match = regex.exec(value);\n    let countSkipedSymbol = 0;\n\n    while (match != null) {\n      countSkipedSymbol += 1;\n      match = regex.exec(value);\n    }\n\n    return countSkipedSymbol;\n  }\n\n  applyValueChanges(position, justPasted, backspaced, // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n  cb = () => {}) {\n    const formElement = this._elementRef?.nativeElement;\n\n    if (!formElement) {\n      return;\n    }\n\n    formElement.value = this.applyMask(formElement.value, this.maskExpression, position, justPasted, backspaced, cb);\n\n    if (formElement === this._getActiveElement()) {\n      return;\n    }\n\n    this.clearIfNotMatchFn();\n  }\n\n  hideInput(inputValue, maskExpression) {\n    return inputValue.split(\"\"\n    /* EMPTY_STRING */\n    ).map((curr, index) => {\n      if (this.patterns && this.patterns[maskExpression[index] ?? \"\"\n      /* EMPTY_STRING */\n      ] && this.patterns[maskExpression[index] ?? \"\"\n      /* EMPTY_STRING */\n      ]?.symbol) {\n        return this.patterns[maskExpression[index] ?? \"\"\n        /* EMPTY_STRING */\n        ]?.symbol;\n      }\n\n      return curr;\n    }).join(\"\"\n    /* EMPTY_STRING */\n    );\n  } // this function is not necessary, it checks result against maskExpression\n\n\n  getActualValue(res) {\n    const compare = res.split(\"\"\n    /* EMPTY_STRING */\n    ).filter((symbol, i) => {\n      const maskChar = this.maskExpression[i] ?? \"\"\n      /* EMPTY_STRING */\n      ;\n      return this._checkSymbolMask(symbol, maskChar) || this.specialCharacters.includes(maskChar) && symbol === maskChar;\n    });\n\n    if (compare.join(\"\"\n    /* EMPTY_STRING */\n    ) === res) {\n      return compare.join(\"\"\n      /* EMPTY_STRING */\n      );\n    }\n\n    return res;\n  }\n\n  shiftTypedSymbols(inputValue) {\n    let symbolToReplace = '';\n    const newInputValue = inputValue && inputValue.split(\"\"\n    /* EMPTY_STRING */\n    ).map((currSymbol, index) => {\n      if (this.specialCharacters.includes(inputValue[index + 1] ?? \"\"\n      /* EMPTY_STRING */\n      ) && inputValue[index + 1] !== this.maskExpression[index + 1]) {\n        symbolToReplace = currSymbol;\n        return inputValue[index + 1];\n      }\n\n      if (symbolToReplace.length) {\n        const replaceSymbol = symbolToReplace;\n        symbolToReplace = \"\"\n        /* EMPTY_STRING */\n        ;\n        return replaceSymbol;\n      }\n\n      return currSymbol;\n    }) || [];\n    return newInputValue.join(\"\"\n    /* EMPTY_STRING */\n    );\n  }\n  /**\n   * Convert number value to string\n   * 3.1415 -> '3.1415'\n   * 1e-7 -> '0.0000001'\n   */\n\n\n  numberToString(value) {\n    if (!value && value !== 0 || this.maskExpression.startsWith(\"separator\"\n    /* SEPARATOR */\n    ) && (this.leadZero || !this.dropSpecialCharacters) || this.maskExpression.startsWith(\"separator\"\n    /* SEPARATOR */\n    ) && this.separatorLimit.length > 14 && String(value).length > 14) {\n      return String(value);\n    }\n\n    return Number(value).toLocaleString('fullwide', {\n      useGrouping: false,\n      maximumFractionDigits: 20\n    }).replace(`/${\"-\"\n    /* MINUS */\n    }/`, \"-\"\n    /* MINUS */\n    );\n  }\n\n  showMaskInInput(inputVal) {\n    if (this.showMaskTyped && !!this.shownMaskExpression) {\n      if (this.maskExpression.length !== this.shownMaskExpression.length) {\n        throw new Error('Mask expression must match mask placeholder length');\n      } else {\n        return this.shownMaskExpression;\n      }\n    } else if (this.showMaskTyped) {\n      if (inputVal) {\n        if (this.maskExpression === \"IP\"\n        /* IP */\n        ) {\n          return this._checkForIp(inputVal);\n        }\n\n        if (this.maskExpression === \"CPF_CNPJ\"\n        /* CPF_CNPJ */\n        ) {\n          return this._checkForCpfCnpj(inputVal);\n        }\n      }\n\n      if (this.placeHolderCharacter.length === this.maskExpression.length) {\n        return this.placeHolderCharacter;\n      }\n\n      return this.maskExpression.replace(/\\w/g, this.placeHolderCharacter);\n    }\n\n    return '';\n  }\n\n  clearIfNotMatchFn() {\n    const formElement = this._elementRef?.nativeElement;\n\n    if (!formElement) {\n      return;\n    }\n\n    if (this.clearIfNotMatch && this.prefix.length + this.maskExpression.length + this.suffix.length !== formElement.value.replace(this.placeHolderCharacter, \"\"\n    /* EMPTY_STRING */\n    ).length) {\n      this.formElementProperty = ['value', \"\"\n      /* EMPTY_STRING */\n      ];\n      this.applyMask('', this.maskExpression);\n    }\n  }\n\n  set formElementProperty([name, value]) {\n    if (!this._renderer || !this._elementRef) {\n      return;\n    }\n\n    Promise.resolve().then(() => this._renderer?.setProperty(this._elementRef?.nativeElement, name, value));\n  }\n\n  checkDropSpecialCharAmount(mask) {\n    const chars = mask.split(\"\"\n    /* EMPTY_STRING */\n    ).filter(item => this._findDropSpecialChar(item));\n    return chars.length;\n  }\n\n  removeMask(inputValue) {\n    return this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.specialCharacters.concat('_').concat(this.placeHolderCharacter));\n  }\n\n  _checkForIp(inputVal) {\n    if (inputVal === \"#\"\n    /* HASH */\n    ) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n\n    const arr = [];\n\n    for (let i = 0; i < inputVal.length; i++) {\n      const value = inputVal[i] ?? \"\"\n      /* EMPTY_STRING */\n      ;\n\n      if (!value) {\n        continue;\n      }\n\n      if (value.match('\\\\d')) {\n        arr.push(value);\n      }\n    }\n\n    if (arr.length <= 3) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n\n    if (arr.length > 3 && arr.length <= 6) {\n      return `${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n    }\n\n    if (arr.length > 6 && arr.length <= 9) {\n      return this.placeHolderCharacter;\n    }\n\n    if (arr.length > 9 && arr.length <= 12) {\n      return '';\n    }\n\n    return '';\n  }\n\n  _checkForCpfCnpj(inputVal) {\n    const cpf = `${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n    const cnpj = `${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `/${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` + `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n\n    if (inputVal === \"#\"\n    /* HASH */\n    ) {\n      return cpf;\n    }\n\n    const arr = [];\n\n    for (let i = 0; i < inputVal.length; i++) {\n      const value = inputVal[i] ?? \"\"\n      /* EMPTY_STRING */\n      ;\n\n      if (!value) {\n        continue;\n      }\n\n      if (value.match('\\\\d')) {\n        arr.push(value);\n      }\n    }\n\n    if (arr.length <= 3) {\n      return cpf.slice(arr.length, cpf.length);\n    }\n\n    if (arr.length > 3 && arr.length <= 6) {\n      return cpf.slice(arr.length + 1, cpf.length);\n    }\n\n    if (arr.length > 6 && arr.length <= 9) {\n      return cpf.slice(arr.length + 2, cpf.length);\n    }\n\n    if (arr.length > 9 && arr.length < 11) {\n      return cpf.slice(arr.length + 3, cpf.length);\n    }\n\n    if (arr.length === 11) {\n      return '';\n    }\n\n    if (arr.length === 12) {\n      if (inputVal.length === 17) {\n        return cnpj.slice(16, cnpj.length);\n      }\n\n      return cnpj.slice(15, cnpj.length);\n    }\n\n    if (arr.length > 12 && arr.length <= 14) {\n      return cnpj.slice(arr.length + 4, cnpj.length);\n    }\n\n    return '';\n  }\n  /**\n   * Recursively determine the current active element by navigating the Shadow DOM until the Active Element is found.\n   */\n\n\n  _getActiveElement(document = this.document) {\n    const shadowRootEl = document?.activeElement?.shadowRoot;\n\n    if (!shadowRootEl?.activeElement) {\n      return document.activeElement;\n    } else {\n      return this._getActiveElement(shadowRootEl);\n    }\n  }\n  /**\n   * Propogates the input value back to the Angular model by triggering the onChange function. It won't do this if writingValue\n   * is true. If that is true it means we are currently in the writeValue function, which is supposed to only update the actual\n   * DOM element based on the Angular model value. It should be a one way process, i.e. writeValue should not be modifying the Angular\n   * model value too. Therefore, we don't trigger onChange in this scenario.\n   * @param inputValue the current form input value\n   */\n\n\n  formControlResult(inputValue) {\n    if (this.writingValue || !this.triggerOnMaskChange && this.maskChanged) {\n      this.maskChanged ? this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue)))))) : '';\n      this.maskChanged = false;\n      return;\n    }\n\n    if (Array.isArray(this.dropSpecialCharacters)) {\n      this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.dropSpecialCharacters)))));\n    } else if (this.dropSpecialCharacters || !this.dropSpecialCharacters && this.prefix === inputValue) {\n      this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue))))));\n    } else {\n      this.onChange(this.outputTransformFn(this._toNumber(inputValue)));\n    }\n  }\n\n  _toNumber(value) {\n    if (!this.isNumberValue || value === \"\"\n    /* EMPTY_STRING */\n    ) {\n      return value;\n    }\n\n    if (this.maskExpression.startsWith(\"separator\"\n    /* SEPARATOR */\n    ) && (this.leadZero || !this.dropSpecialCharacters)) {\n      return value;\n    }\n\n    if (String(value).length > 16 && this.separatorLimit.length > 14) {\n      return String(value);\n    }\n\n    const num = Number(value);\n\n    if (this.maskExpression.startsWith(\"separator\"\n    /* SEPARATOR */\n    ) && Number.isNaN(num)) {\n      const val = String(value).replace(',', '.');\n      return Number(val);\n    }\n\n    return Number.isNaN(num) ? value : num;\n  }\n\n  _removeMask(value, specialCharactersForRemove) {\n    if (this.maskExpression.startsWith(\"percent\"\n    /* PERCENT */\n    ) && value.includes(\".\"\n    /* DOT */\n    )) {\n      return value;\n    }\n\n    return value ? value.replace(this._regExpForRemove(specialCharactersForRemove), \"\"\n    /* EMPTY_STRING */\n    ) : value;\n  }\n\n  _removePrefix(value) {\n    if (!this.prefix) {\n      return value;\n    }\n\n    return value ? value.replace(this.prefix, \"\"\n    /* EMPTY_STRING */\n    ) : value;\n  }\n\n  _removeSuffix(value) {\n    if (!this.suffix) {\n      return value;\n    }\n\n    return value ? value.replace(this.suffix, \"\"\n    /* EMPTY_STRING */\n    ) : value;\n  }\n\n  _retrieveSeparatorValue(result) {\n    let specialCharacters = Array.isArray(this.dropSpecialCharacters) ? this.specialCharacters.filter(v => {\n      return this.dropSpecialCharacters.includes(v);\n    }) : this.specialCharacters;\n\n    if (!this.deletedSpecialCharacter && this._checkPatternForSpace() && result.includes(\" \"\n    /* WHITE_SPACE */\n    ) && this.maskExpression.includes(\"*\"\n    /* SYMBOL_STAR */\n    )) {\n      specialCharacters = specialCharacters.filter(char => char !== \" \"\n      /* WHITE_SPACE */\n      );\n    }\n\n    return this._removeMask(result, specialCharacters);\n  }\n\n  _regExpForRemove(specialCharactersForRemove) {\n    return new RegExp(specialCharactersForRemove.map(item => `\\\\${item}`).join('|'), 'gi');\n  }\n\n  _replaceDecimalMarkerToDot(value) {\n    const markers = Array.isArray(this.decimalMarker) ? this.decimalMarker : [this.decimalMarker];\n    return value.replace(this._regExpForRemove(markers), \".\"\n    /* DOT */\n    );\n  }\n\n  _checkSymbols(result) {\n    if (result === \"\"\n    /* EMPTY_STRING */\n    ) {\n      return result;\n    }\n\n    if (this.maskExpression.startsWith(\"percent\"\n    /* PERCENT */\n    ) && this.decimalMarker === \",\"\n    /* COMMA */\n    ) {\n      // eslint-disable-next-line no-param-reassign\n      result = result.replace(\",\"\n      /* COMMA */\n      , \".\"\n      /* DOT */\n      );\n    }\n\n    const separatorPrecision = this._retrieveSeparatorPrecision(this.maskExpression);\n\n    const separatorValue = this._replaceDecimalMarkerToDot(this._retrieveSeparatorValue(result));\n\n    if (!this.isNumberValue) {\n      return separatorValue;\n    }\n\n    if (separatorPrecision) {\n      if (result === this.decimalMarker) {\n        return null;\n      }\n\n      if (this.separatorLimit.length > 14) {\n        return String(separatorValue);\n      }\n\n      return this._checkPrecision(this.maskExpression, separatorValue);\n    } else {\n      return separatorValue;\n    }\n  }\n\n  _checkPatternForSpace() {\n    for (const key in this.patterns) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (this.patterns[key] && this.patterns[key]?.hasOwnProperty('pattern')) {\n        const patternString = this.patterns[key]?.pattern.toString();\n        const pattern = this.patterns[key]?.pattern;\n\n        if (patternString?.includes(\" \"\n        /* WHITE_SPACE */\n        ) && pattern?.test(this.maskExpression)) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  } // TODO should think about helpers or separting decimal precision to own property\n\n\n  _retrieveSeparatorPrecision(maskExpretion) {\n    const matcher = maskExpretion.match(new RegExp(`^separator\\\\.([^d]*)`));\n    return matcher ? Number(matcher[1]) : null;\n  }\n\n  _checkPrecision(separatorExpression, separatorValue) {\n    const separatorPrecision = separatorExpression.slice(10, 11);\n\n    if (separatorExpression.indexOf('2') > 0 || this.leadZero && Number(separatorPrecision) > 1) {\n      if (this.decimalMarker === \",\"\n      /* COMMA */\n      && this.leadZero) {\n        // eslint-disable-next-line no-param-reassign\n        separatorValue = separatorValue.replace(',', '.');\n      }\n\n      return this.leadZero ? Number(separatorValue).toFixed(Number(separatorPrecision)) : Number(separatorValue).toFixed(2);\n    }\n\n    return this.numberToString(separatorValue);\n  }\n\n  _repeatPatternSymbols(maskExp) {\n    return maskExp.match(/{[0-9]+}/) && maskExp.split(\"\"\n    /* EMPTY_STRING */\n    ).reduce((accum, currVal, index) => {\n      this._start = currVal === \"{\"\n      /* CURLY_BRACKETS_LEFT */\n      ? index : this._start;\n\n      if (currVal !== \"}\"\n      /* CURLY_BRACKETS_RIGHT */\n      ) {\n        return this._findSpecialChar(currVal) ? accum + currVal : accum;\n      }\n\n      this._end = index;\n      const repeatNumber = Number(maskExp.slice(this._start + 1, this._end));\n      const replaceWith = new Array(repeatNumber + 1).join(maskExp[this._start - 1]);\n\n      if (maskExp.slice(0, this._start).length > 1 && maskExp.includes(\"S\"\n      /* LETTER_S */\n      )) {\n        const symbols = maskExp.slice(0, this._start - 1);\n        return symbols.includes(\"{\"\n        /* CURLY_BRACKETS_LEFT */\n        ) ? accum + replaceWith : symbols + accum + replaceWith;\n      } else {\n        return accum + replaceWith;\n      }\n    }, '') || maskExp;\n  }\n\n  currentLocaleDecimalMarker() {\n    return 1.1.toLocaleString().substring(1, 2);\n  }\n\n}\n\nNgxMaskService.ɵfac = function NgxMaskService_Factory(t) {\n  return new (t || NgxMaskService)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(NGX_MASK_CONFIG), i0.ɵɵinject(i0.ElementRef, 8), i0.ɵɵinject(i0.Renderer2, 8));\n};\n\nNgxMaskService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgxMaskService,\n  factory: NgxMaskService.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskService, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NGX_MASK_CONFIG]\n      }]\n    }, {\n      type: i0.ElementRef,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.Renderer2,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})(); // tslint:disable deprecation\n// tslint:disable no-input-rename\n\n\nclass NgxMaskDirective {\n  constructor( // tslint:disable-next-line\n  document, _config, _maskService) {\n    this.document = document;\n    this._config = _config;\n    this._maskService = _maskService; // eslint-disable-next-line @angular-eslint/no-input-rename\n\n    this.maskExpression = '';\n    this.specialCharacters = [];\n    this.patterns = {};\n    this.prefix = '';\n    this.suffix = '';\n    this.thousandSeparator = ' ';\n    this.decimalMarker = '.';\n    this.dropSpecialCharacters = null;\n    this.hiddenInput = null;\n    this.showMaskTyped = null;\n    this.placeHolderCharacter = null;\n    this.shownMaskExpression = null;\n    this.showTemplate = null;\n    this.clearIfNotMatch = null;\n    this.validation = null;\n    this.separatorLimit = null;\n    this.allowNegativeNumbers = null;\n    this.leadZeroDateTime = null;\n    this.leadZero = null;\n    this.triggerOnMaskChange = null;\n    this.apm = null;\n    this.inputTransformFn = null;\n    this.outputTransformFn = null;\n    this.keepCharacterPositions = null;\n    this.maskFilled = new EventEmitter();\n    this._maskValue = '';\n    this._isFocused = false;\n    this._position = null;\n    this._maskExpressionArray = [];\n    this._justPasted = false;\n    /**For IME composition event */\n\n    this._isComposing = false; // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n\n    this.onChange = _ => {}; // eslint-disable-next-line @typescript-eslint/no-empty-function\n\n\n    this.onTouch = () => {};\n  }\n\n  ngOnChanges(changes) {\n    const {\n      maskExpression,\n      specialCharacters,\n      patterns,\n      prefix,\n      suffix,\n      thousandSeparator,\n      decimalMarker,\n      dropSpecialCharacters,\n      hiddenInput,\n      showMaskTyped,\n      placeHolderCharacter,\n      shownMaskExpression,\n      showTemplate,\n      clearIfNotMatch,\n      validation,\n      separatorLimit,\n      allowNegativeNumbers,\n      leadZeroDateTime,\n      leadZero,\n      triggerOnMaskChange,\n      apm,\n      inputTransformFn,\n      outputTransformFn,\n      keepCharacterPositions\n    } = changes;\n\n    if (maskExpression) {\n      if (maskExpression.currentValue !== maskExpression.previousValue && !maskExpression.firstChange) {\n        this._maskService.maskChanged = true;\n      }\n\n      if (maskExpression.currentValue && maskExpression.currentValue.split(\"||\"\n      /* OR */\n      ).length > 1) {\n        this._maskExpressionArray = maskExpression.currentValue.split(\"||\"\n        /* OR */\n        ).sort((a, b) => {\n          return a.length - b.length;\n        });\n\n        this._setMask();\n      } else {\n        this._maskExpressionArray = [];\n        this._maskValue = maskExpression.currentValue || \"\"\n        /* EMPTY_STRING */\n        ;\n        this._maskService.maskExpression = this._maskValue;\n      }\n    }\n\n    if (specialCharacters) {\n      if (!specialCharacters.currentValue || !Array.isArray(specialCharacters.currentValue)) {\n        return;\n      } else {\n        this._maskService.specialCharacters = specialCharacters.currentValue || [];\n      }\n    }\n\n    if (allowNegativeNumbers) {\n      this._maskService.allowNegativeNumbers = allowNegativeNumbers.currentValue;\n\n      if (this._maskService.allowNegativeNumbers) {\n        this._maskService.specialCharacters = this._maskService.specialCharacters.filter(c => c !== \"-\"\n        /* MINUS */\n        );\n      }\n    } // Only overwrite the mask available patterns if a pattern has actually been passed in\n\n\n    if (patterns && patterns.currentValue) {\n      this._maskService.patterns = patterns.currentValue;\n    }\n\n    if (apm && apm.currentValue) {\n      this._maskService.apm = apm.currentValue;\n    }\n\n    if (prefix) {\n      this._maskService.prefix = prefix.currentValue;\n    }\n\n    if (suffix) {\n      this._maskService.suffix = suffix.currentValue;\n    }\n\n    if (thousandSeparator) {\n      this._maskService.thousandSeparator = thousandSeparator.currentValue;\n    }\n\n    if (decimalMarker) {\n      this._maskService.decimalMarker = decimalMarker.currentValue;\n    }\n\n    if (dropSpecialCharacters) {\n      this._maskService.dropSpecialCharacters = dropSpecialCharacters.currentValue;\n    }\n\n    if (hiddenInput) {\n      this._maskService.hiddenInput = hiddenInput.currentValue;\n    }\n\n    if (showMaskTyped) {\n      this._maskService.showMaskTyped = showMaskTyped.currentValue;\n\n      if (showMaskTyped.previousValue === false && showMaskTyped.currentValue === true && this._isFocused) {\n        requestAnimationFrame(() => {\n          this._maskService._elementRef?.nativeElement.click();\n        });\n      }\n    }\n\n    if (placeHolderCharacter) {\n      this._maskService.placeHolderCharacter = placeHolderCharacter.currentValue;\n    }\n\n    if (shownMaskExpression) {\n      this._maskService.shownMaskExpression = shownMaskExpression.currentValue;\n    }\n\n    if (showTemplate) {\n      this._maskService.showTemplate = showTemplate.currentValue;\n    }\n\n    if (clearIfNotMatch) {\n      this._maskService.clearIfNotMatch = clearIfNotMatch.currentValue;\n    }\n\n    if (validation) {\n      this._maskService.validation = validation.currentValue;\n    }\n\n    if (separatorLimit) {\n      this._maskService.separatorLimit = separatorLimit.currentValue;\n    }\n\n    if (leadZeroDateTime) {\n      this._maskService.leadZeroDateTime = leadZeroDateTime.currentValue;\n    }\n\n    if (leadZero) {\n      this._maskService.leadZero = leadZero.currentValue;\n    }\n\n    if (triggerOnMaskChange) {\n      this._maskService.triggerOnMaskChange = triggerOnMaskChange.currentValue;\n    }\n\n    if (inputTransformFn) {\n      this._maskService.inputTransformFn = inputTransformFn.currentValue;\n    }\n\n    if (outputTransformFn) {\n      this._maskService.outputTransformFn = outputTransformFn.currentValue;\n    }\n\n    if (keepCharacterPositions) {\n      this._maskService.keepCharacterPositions = keepCharacterPositions.currentValue;\n    }\n\n    this._applyMask();\n  } // eslint-disable-next-line complexity\n\n\n  validate({\n    value\n  }) {\n    if (!this._maskService.validation || !this._maskValue) {\n      return null;\n    }\n\n    if (this._maskService.ipError) {\n      return this._createValidationError(value);\n    }\n\n    if (this._maskService.cpfCnpjError) {\n      return this._createValidationError(value);\n    }\n\n    if (this._maskValue.startsWith(\"separator\"\n    /* SEPARATOR */\n    )) {\n      return null;\n    }\n\n    if (withoutValidation.includes(this._maskValue)) {\n      return null;\n    }\n\n    if (this._maskService.clearIfNotMatch) {\n      return null;\n    }\n\n    if (timeMasks.includes(this._maskValue)) {\n      return this._validateTime(value);\n    }\n\n    if (value && value.toString().length >= 1) {\n      let counterOfOpt = 0;\n\n      if (this._maskValue.startsWith(\"percent\"\n      /* PERCENT */\n      )) {\n        return null;\n      }\n\n      for (const key in this._maskService.patterns) {\n        if (this._maskService.patterns[key]?.optional) {\n          if (this._maskValue.indexOf(key) !== this._maskValue.lastIndexOf(key)) {\n            const opt = this._maskValue.split(\"\"\n            /* EMPTY_STRING */\n            ).filter(i => i === key).join(\"\"\n            /* EMPTY_STRING */\n            );\n\n            counterOfOpt += opt.length;\n          } else if (this._maskValue.indexOf(key) !== -1) {\n            counterOfOpt++;\n          }\n\n          if (this._maskValue.indexOf(key) !== -1 && value.toString().length >= this._maskValue.indexOf(key)) {\n            return null;\n          }\n\n          if (counterOfOpt === this._maskValue.length) {\n            return null;\n          }\n        }\n      }\n\n      if (this._maskValue.indexOf(\"{\"\n      /* CURLY_BRACKETS_LEFT */\n      ) === 1 && value.toString().length === this._maskValue.length + Number((this._maskValue.split(\"{\"\n      /* CURLY_BRACKETS_LEFT */\n      )[1] ?? \"\"\n      /* EMPTY_STRING */\n      ).split(\"}\"\n      /* CURLY_BRACKETS_RIGHT */\n      )[0]) - 4) {\n        return null;\n      } else if (this._maskValue.indexOf(\"*\"\n      /* SYMBOL_STAR */\n      ) > 1 && value.toString().length < this._maskValue.indexOf(\"*\"\n      /* SYMBOL_STAR */\n      ) || this._maskValue.indexOf(\"?\"\n      /* SYMBOL_QUESTION */\n      ) > 1 && value.toString().length < this._maskValue.indexOf(\"?\"\n      /* SYMBOL_QUESTION */\n      ) || this._maskValue.indexOf(\"{\"\n      /* CURLY_BRACKETS_LEFT */\n      ) === 1) {\n        return this._createValidationError(value);\n      }\n\n      if (this._maskValue.indexOf(\"*\"\n      /* SYMBOL_STAR */\n      ) === -1 || this._maskValue.indexOf(\"?\"\n      /* SYMBOL_QUESTION */\n      ) === -1) {\n        // eslint-disable-next-line no-param-reassign\n        value = typeof value === 'number' ? String(value) : value;\n\n        const array = this._maskValue.split('*');\n\n        const length = this._maskService.dropSpecialCharacters ? this._maskValue.length - this._maskService.checkDropSpecialCharAmount(this._maskValue) - counterOfOpt : this.prefix ? this._maskValue.length + this.prefix.length - counterOfOpt : this._maskValue.length - counterOfOpt;\n\n        if (array.length === 1) {\n          if (value.toString().length < length) {\n            return this._createValidationError(value);\n          }\n        }\n\n        if (array.length > 1) {\n          const lastIndexArray = array[array.length - 1];\n\n          if (lastIndexArray && this._maskService.specialCharacters.includes(lastIndexArray[0]) && String(value).includes(lastIndexArray[0] ?? '') && !this.dropSpecialCharacters) {\n            const special = value.split(lastIndexArray[0]);\n            return special[special.length - 1].length === lastIndexArray.length - 1 ? null : this._createValidationError(value);\n          } else if ((lastIndexArray && !this._maskService.specialCharacters.includes(lastIndexArray[0]) || !lastIndexArray || this._maskService.dropSpecialCharacters) && value.length >= length - 1) {\n            return null;\n          } else {\n            return this._createValidationError(value);\n          }\n        }\n      }\n\n      if (this._maskValue.indexOf(\"*\"\n      /* SYMBOL_STAR */\n      ) === 1 || this._maskValue.indexOf(\"?\"\n      /* SYMBOL_QUESTION */\n      ) === 1) {\n        return null;\n      }\n    }\n\n    if (value) {\n      this.maskFilled.emit();\n      return null;\n    }\n\n    return null;\n  }\n\n  onPaste() {\n    this._justPasted = true;\n  }\n\n  onFocus() {\n    this._isFocused = true;\n  }\n\n  onModelChange(value) {\n    // on form reset we need to update the actualValue\n    if ((value === \"\"\n    /* EMPTY_STRING */\n    || value === null || value === undefined) && this._maskService.actualValue) {\n      this._maskService.actualValue = this._maskService.getActualValue(\"\"\n      /* EMPTY_STRING */\n      );\n    }\n  }\n\n  onInput(e) {\n    // If IME is composing text, we wait for the composed text.\n    if (this._isComposing) return;\n    const el = e.target;\n\n    const transformedValue = this._maskService.inputTransformFn(el.value);\n\n    if (el.type !== 'number') {\n      if (typeof transformedValue === 'string' || typeof transformedValue === 'number') {\n        el.value = transformedValue.toString();\n        this._inputValue = el.value;\n\n        this._setMask();\n\n        if (!this._maskValue) {\n          this.onChange(el.value);\n          return;\n        }\n\n        let position = el.selectionStart === 1 ? el.selectionStart + this._maskService.prefix.length : el.selectionStart;\n\n        if (this.showMaskTyped && this.keepCharacterPositions && this._maskService.placeHolderCharacter.length === 1) {\n          const inputSymbol = el.value.slice(position - 1, position);\n          const prefixLength = this.prefix.length;\n\n          const checkSymbols = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position - 1 - prefixLength] ?? \"\"\n          /* EMPTY_STRING */\n          );\n\n          const checkSpecialCharacter = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position + 1 - prefixLength] ?? \"\"\n          /* EMPTY_STRING */\n          );\n\n          const selectRangeBackspace = this._maskService.selStart === this._maskService.selEnd;\n          const selStart = Number(this._maskService.selStart) - prefixLength ?? '';\n          const selEnd = Number(this._maskService.selEnd) - prefixLength ?? '';\n\n          if (this._code === \"Backspace\"\n          /* BACKSPACE */\n          ) {\n            if (!selectRangeBackspace) {\n              if (this._maskService.selStart === prefixLength) {\n                this._maskService.actualValue = this.prefix + this._maskService.maskIsShown.slice(0, selEnd) + this._inputValue.split(this.prefix).join('');\n              } else if (this._maskService.selStart === this._maskService.maskIsShown.length + prefixLength) {\n                this._maskService.actualValue = this._inputValue + this._maskService.maskIsShown.slice(selStart, selEnd);\n              } else {\n                this._maskService.actualValue = this.prefix + this._inputValue.split(this.prefix).join('').slice(0, selStart) + this._maskService.maskIsShown.slice(selStart, selEnd) + this._maskService.actualValue.slice(selEnd + prefixLength, this._maskService.maskIsShown.length + prefixLength) + this.suffix;\n              }\n            } else if (!this._maskService.specialCharacters.includes(this._maskService.maskExpression.slice(position - this.prefix.length, position + 1 - this.prefix.length)) && selectRangeBackspace) {\n              if (selStart === 1 && this.prefix) {\n                this._maskService.actualValue = this.prefix + this._maskService.placeHolderCharacter + el.value.split(this.prefix).join('').split(this.suffix).join('') + this.suffix;\n                position = position - 1;\n              } else {\n                const part1 = el.value.substring(0, position);\n                const part2 = el.value.substring(position);\n                this._maskService.actualValue = part1 + this._maskService.placeHolderCharacter + part2;\n              }\n            }\n          }\n\n          if (this._code !== \"Backspace\"\n          /* BACKSPACE */\n          ) {\n            if (!checkSymbols && !checkSpecialCharacter && selectRangeBackspace) {\n              position = Number(el.selectionStart) - 1;\n            } else if (this._maskService.specialCharacters.includes(el.value.slice(position, position + 1)) && checkSpecialCharacter && !this._maskService.specialCharacters.includes(el.value.slice(position + 1, position + 2))) {\n              this._maskService.actualValue = el.value.slice(0, position - 1) + el.value.slice(position, position + 1) + inputSymbol + el.value.slice(position + 2);\n              position = position + 1;\n            } else if (checkSymbols) {\n              if (el.value.length === 1 && position === 1) {\n                this._maskService.actualValue = this.prefix + inputSymbol + this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length) + this.suffix;\n              } else {\n                this._maskService.actualValue = el.value.slice(0, position - 1) + inputSymbol + el.value.slice(position + 1).split(this.suffix).join('') + this.suffix;\n              }\n            } else if (this.prefix && el.value.length === 1 && position - prefixLength === 1 && this._maskService._checkSymbolMask(el.value, this._maskService.maskExpression[position - 1 - prefixLength] ?? \"\"\n            /* EMPTY_STRING */\n            )) {\n              this._maskService.actualValue = this.prefix + el.value + this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length) + this.suffix;\n            }\n          }\n        }\n\n        let caretShift = 0;\n        let backspaceShift = false;\n\n        if (this._code === \"Delete\"\n        /* DELETE */\n        && \"separator\"\n        /* SEPARATOR */\n        ) {\n          this._maskService.deletedSpecialCharacter = true;\n        }\n\n        if (this._inputValue.length >= this._maskService.maskExpression.length - 1 && this._code !== \"Backspace\"\n        /* BACKSPACE */\n        && this._maskService.maskExpression === \"d0/M0/0000\"\n        /* DAYS_MONTHS_YEARS */\n        && position < 10) {\n          const inputSymbol = this._inputValue.slice(position - 1, position);\n\n          el.value = this._inputValue.slice(0, position - 1) + inputSymbol + this._inputValue.slice(position + 1);\n        }\n\n        if (this._maskService.maskExpression === \"d0/M0/0000\"\n        /* DAYS_MONTHS_YEARS */\n        && this.leadZeroDateTime) {\n          if (position < 3 && Number(el.value) > 31 && Number(el.value) < 40 || position === 5 && Number(el.value.slice(3, 5)) > 12) {\n            position = position + 2;\n          }\n        }\n\n        if (this._maskService.maskExpression === \"Hh:m0:s0\"\n        /* HOURS_MINUTES_SECONDS */\n        && this.apm) {\n          if (this._justPasted && el.value.slice(0, 2) === \"00\"\n          /* DOUBLE_ZERO */\n          ) {\n            el.value = el.value.slice(1, 2) + el.value.slice(2, el.value.length);\n          }\n\n          el.value = el.value === \"00\"\n          /* DOUBLE_ZERO */\n          ? \"0\"\n          /* NUMBER_ZERO */\n          : el.value;\n        }\n\n        this._maskService.applyValueChanges(position, this._justPasted, this._code === \"Backspace\"\n        /* BACKSPACE */\n        || this._code === \"Delete\"\n        /* DELETE */\n        , (shift, _backspaceShift) => {\n          this._justPasted = false;\n          caretShift = shift;\n          backspaceShift = _backspaceShift;\n        }); // only set the selection if the element is active\n\n\n        if (this._getActiveElement() !== el) {\n          return;\n        }\n\n        if (this._maskService.plusOnePosition) {\n          position = position + 1;\n          this._maskService.plusOnePosition = false;\n        } // update position after applyValueChanges to prevent cursor on wrong position when it has an array of maskExpression\n\n\n        if (this._maskExpressionArray.length) {\n          if (this._code === \"Backspace\"\n          /* BACKSPACE */\n          ) {\n            position = this.specialCharacters.includes(this._inputValue.slice(position - 1, position)) ? position - 1 : position;\n          } else {\n            position = el.selectionStart === 1 ? el.selectionStart + this._maskService.prefix.length : el.selectionStart;\n          }\n        }\n\n        this._position = this._position === 1 && this._inputValue.length === 1 ? null : this._position;\n        let positionToApply = this._position ? this._inputValue.length + position + caretShift : position + (this._code === \"Backspace\"\n        /* BACKSPACE */\n        && !backspaceShift ? 0 : caretShift);\n\n        if (positionToApply > this._getActualInputLength()) {\n          positionToApply = el.value === this._maskService.decimalMarker && el.value.length === 1 ? this._getActualInputLength() + 1 : this._getActualInputLength();\n        }\n\n        if (positionToApply < 0) {\n          positionToApply = 0;\n        }\n\n        el.setSelectionRange(positionToApply, positionToApply);\n        this._position = null;\n      } else {\n        console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof transformedValue);\n      }\n    } else {\n      if (!this._maskValue) {\n        this.onChange(el.value);\n        return;\n      }\n\n      this._maskService.applyValueChanges(el.value.length, this._justPasted, this._code === \"Backspace\"\n      /* BACKSPACE */\n      || this._code === \"Delete\"\n      /* DELETE */\n      );\n    }\n  } // IME starts\n\n\n  onCompositionStart() {\n    this._isComposing = true;\n  } // IME completes\n\n\n  onCompositionEnd(e) {\n    this._isComposing = false;\n    this._justPasted = true;\n    this.onInput(e);\n  }\n\n  onBlur(e) {\n    if (this._maskValue) {\n      const el = e.target;\n\n      if (this.leadZero && el.value.length > 0 && typeof this.decimalMarker === 'string') {\n        const maskExpression = this._maskService.maskExpression;\n        const precision = Number(this._maskService.maskExpression.slice(maskExpression.length - 1, maskExpression.length));\n\n        if (precision > 1) {\n          el.value = this.suffix ? el.value.split(this.suffix).join('') : el.value;\n          const decimalPart = el.value.split(this.decimalMarker)[1];\n          el.value = el.value.includes(this.decimalMarker) ? el.value + \"0\"\n          /* NUMBER_ZERO */\n          .repeat(precision - decimalPart.length) + this.suffix : el.value + this.decimalMarker + \"0\"\n          /* NUMBER_ZERO */\n          .repeat(precision) + this.suffix;\n          this._maskService.actualValue = el.value;\n        }\n      }\n\n      this._maskService.clearIfNotMatchFn();\n    }\n\n    this._isFocused = false;\n    this.onTouch();\n  }\n\n  onClick(e) {\n    if (!this._maskValue) {\n      return;\n    }\n\n    const el = e.target;\n    const posStart = 0;\n    const posEnd = 0;\n\n    if (el !== null && el.selectionStart !== null && el.selectionStart === el.selectionEnd && el.selectionStart > this._maskService.prefix.length && // eslint-disable-next-line\n    e.keyCode !== 38) {\n      if (this._maskService.showMaskTyped && !this.keepCharacterPositions) {\n        // We are showing the mask in the input\n        this._maskService.maskIsShown = this._maskService.showMaskInInput();\n\n        if (el.setSelectionRange && this._maskService.prefix + this._maskService.maskIsShown === el.value) {\n          // the input ONLY contains the mask, so position the cursor at the start\n          el.focus();\n          el.setSelectionRange(posStart, posEnd);\n        } else {\n          // the input contains some characters already\n          if (el.selectionStart > this._maskService.actualValue.length) {\n            // if the user clicked beyond our value's length, position the cursor at the end of our value\n            el.setSelectionRange(this._maskService.actualValue.length, this._maskService.actualValue.length);\n          }\n        }\n      }\n    }\n\n    const nextValue = el && (el.value === this._maskService.prefix ? this._maskService.prefix + this._maskService.maskIsShown : el.value);\n    /** Fix of cursor position jumping to end in most browsers no matter where cursor is inserted onFocus */\n\n    if (el && el.value !== nextValue) {\n      el.value = nextValue;\n    }\n    /** fix of cursor position with prefix when mouse click occur */\n\n\n    if (el && el.type !== 'number' && (el.selectionStart || el.selectionEnd) <= this._maskService.prefix.length) {\n      el.selectionStart = this._maskService.prefix.length;\n      return;\n    }\n    /** select only inserted text */\n\n\n    if (el && el.selectionEnd > this._getActualInputLength()) {\n      el.selectionEnd = this._getActualInputLength();\n    }\n  } // eslint-disable-next-line complexity\n\n\n  onKeyDown(e) {\n    if (!this._maskValue) {\n      return;\n    }\n\n    if (this._isComposing) {\n      // User finalize their choice from IME composition, so trigger onInput() for the composed text.\n      if (e.key === 'Enter') this.onCompositionEnd(e);\n      return;\n    }\n\n    this._code = e.code ? e.code : e.key;\n    const el = e.target;\n    this._inputValue = el.value;\n\n    this._setMask();\n\n    if (el.type !== 'number') {\n      if (e.key === \"ArrowUp\"\n      /* ARROW_UP */\n      ) {\n        e.preventDefault();\n      }\n\n      if (e.key === \"ArrowLeft\"\n      /* ARROW_LEFT */\n      || e.key === \"Backspace\"\n      /* BACKSPACE */\n      || e.key === \"Delete\"\n      /* DELETE */\n      ) {\n        if (e.key === \"Backspace\"\n        /* BACKSPACE */\n        && el.value.length === 0) {\n          el.selectionStart = el.selectionEnd;\n        }\n\n        if (e.key === \"Backspace\"\n        /* BACKSPACE */\n        && el.selectionStart !== 0) {\n          // If specialChars is false, (shouldn't ever happen) then set to the defaults\n          this.specialCharacters = this.specialCharacters?.length ? this.specialCharacters : this._config.specialCharacters;\n\n          if (this.prefix.length > 1 && el.selectionStart <= this.prefix.length) {\n            el.setSelectionRange(this.prefix.length, el.selectionEnd);\n          } else {\n            if (this._inputValue.length !== el.selectionStart && el.selectionStart !== 1) {\n              while (this.specialCharacters.includes((this._inputValue[el.selectionStart - 1] ?? \"\"\n              /* EMPTY_STRING */\n              ).toString()) && (this.prefix.length >= 1 && el.selectionStart > this.prefix.length || this.prefix.length === 0)) {\n                el.setSelectionRange(el.selectionStart - 1, el.selectionEnd);\n              }\n            }\n          }\n        }\n\n        this.checkSelectionOnDeletion(el);\n\n        if (this._maskService.prefix.length && el.selectionStart <= this._maskService.prefix.length && el.selectionEnd <= this._maskService.prefix.length) {\n          e.preventDefault();\n        }\n\n        const cursorStart = el.selectionStart;\n\n        if (e.key === \"Backspace\"\n        /* BACKSPACE */\n        && !el.readOnly && cursorStart === 0 && el.selectionEnd === el.value.length && el.value.length !== 0) {\n          this._position = this._maskService.prefix ? this._maskService.prefix.length : 0;\n\n          this._maskService.applyMask(this._maskService.prefix, this._maskService.maskExpression, this._position);\n        }\n      }\n\n      if (!!this.suffix && this.suffix.length > 1 && this._inputValue.length - this.suffix.length < el.selectionStart) {\n        el.setSelectionRange(this._inputValue.length - this.suffix.length, this._inputValue.length);\n      } else if (e.code === 'KeyA' && e.ctrlKey || e.code === 'KeyA' && e.metaKey // Cmd + A (Mac)\n      ) {\n        el.setSelectionRange(0, this._getActualInputLength());\n        e.preventDefault();\n      }\n\n      this._maskService.selStart = el.selectionStart;\n      this._maskService.selEnd = el.selectionEnd;\n    }\n  }\n  /** It writes the value in the input */\n\n\n  writeValue(controlValue) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      if (typeof controlValue === 'object' && controlValue !== null && 'value' in controlValue) {\n        if ('disable' in controlValue) {\n          _this.setDisabledState(Boolean(controlValue.disable));\n        } // eslint-disable-next-line no-param-reassign\n\n\n        controlValue = controlValue.value;\n      }\n\n      if (controlValue !== null) {\n        // eslint-disable-next-line no-param-reassign\n        controlValue = _this.inputTransformFn ? _this.inputTransformFn(controlValue) : controlValue;\n      }\n\n      if (typeof controlValue === 'string' || typeof controlValue === 'number' || controlValue === null || controlValue === undefined) {\n        if (controlValue === null || controlValue === undefined || controlValue === '') {\n          _this._maskService._currentValue = '';\n          _this._maskService._previousValue = '';\n        } // eslint-disable-next-line no-param-reassign\n\n\n        let inputValue = controlValue;\n\n        if (typeof inputValue === 'number' || _this._maskValue.startsWith(\"separator\"\n        /* SEPARATOR */\n        )) {\n          // eslint-disable-next-line no-param-reassign\n          inputValue = String(inputValue);\n\n          const localeDecimalMarker = _this._maskService.currentLocaleDecimalMarker();\n\n          if (!Array.isArray(_this._maskService.decimalMarker)) {\n            // eslint-disable-next-line no-param-reassign\n            inputValue = _this._maskService.decimalMarker !== localeDecimalMarker ? inputValue.replace(localeDecimalMarker, _this._maskService.decimalMarker) : inputValue;\n          }\n\n          if (_this._maskService.leadZero && inputValue && _this.maskExpression && _this.dropSpecialCharacters !== false) {\n            // eslint-disable-next-line no-param-reassign\n            inputValue = _this._maskService._checkPrecision(_this._maskService.maskExpression, inputValue);\n          }\n\n          if (_this._maskService.decimalMarker === \",\"\n          /* COMMA */\n          ) {\n            // eslint-disable-next-line no-param-reassign\n            inputValue = inputValue.toString().replace(\".\"\n            /* DOT */\n            , \",\"\n            /* COMMA */\n            );\n          }\n\n          if (_this.maskExpression?.startsWith(\"separator\"\n          /* SEPARATOR */\n          ) && _this.leadZero) {\n            requestAnimationFrame(() => {\n              _this._maskService.applyMask(inputValue?.toString() ?? '', _this._maskService.maskExpression);\n            });\n          }\n\n          _this._maskService.isNumberValue = true;\n        }\n\n        if (typeof inputValue !== 'string') {\n          // eslint-disable-next-line no-param-reassign\n          inputValue = '';\n        }\n\n        _this._inputValue = inputValue;\n\n        _this._setMask();\n\n        if (inputValue && _this._maskService.maskExpression || _this._maskService.maskExpression && (_this._maskService.prefix || _this._maskService.showMaskTyped)) {\n          // Let the service we know we are writing value so that triggering onChange function won't happen during applyMask\n          typeof _this.inputTransformFn !== 'function' ? _this._maskService.writingValue = true : '';\n          _this._maskService.formElementProperty = ['value', _this._maskService.applyMask(inputValue, _this._maskService.maskExpression)]; // Let the service know we've finished writing value\n\n          typeof _this.inputTransformFn !== 'function' ? _this._maskService.writingValue = false : '';\n        } else {\n          _this._maskService.formElementProperty = ['value', inputValue];\n        }\n\n        _this._inputValue = inputValue;\n      } else {\n        console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof controlValue);\n      }\n    })();\n  }\n\n  registerOnChange(fn) {\n    this._maskService.onChange = this.onChange = fn;\n  }\n\n  registerOnTouched(fn) {\n    this.onTouch = fn;\n  }\n\n  _getActiveElement(document = this.document) {\n    const shadowRootEl = document?.activeElement?.shadowRoot;\n\n    if (!shadowRootEl?.activeElement) {\n      return document.activeElement;\n    } else {\n      return this._getActiveElement(shadowRootEl);\n    }\n  }\n\n  checkSelectionOnDeletion(el) {\n    el.selectionStart = Math.min(Math.max(this.prefix.length, el.selectionStart), this._inputValue.length - this.suffix.length);\n    el.selectionEnd = Math.min(Math.max(this.prefix.length, el.selectionEnd), this._inputValue.length - this.suffix.length);\n  }\n  /** It disables the input element */\n\n\n  setDisabledState(isDisabled) {\n    this._maskService.formElementProperty = ['disabled', isDisabled];\n  } // eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n\n  _applyMask() {\n    this._maskService.maskExpression = this._maskService._repeatPatternSymbols(this._maskValue || '');\n    this._maskService.formElementProperty = ['value', this._maskService.applyMask(this._inputValue, this._maskService.maskExpression)];\n  }\n\n  _validateTime(value) {\n    const rowMaskLen = this._maskValue.split(\"\"\n    /* EMPTY_STRING */\n    ).filter(s => s !== ':').length;\n\n    if (!value) {\n      return null; // Don't validate empty values to allow for optional form control\n    }\n\n    if (+(value[value.length - 1] ?? -1) === 0 && value.length < rowMaskLen || value.length <= rowMaskLen - 2) {\n      return this._createValidationError(value);\n    }\n\n    return null;\n  }\n\n  _getActualInputLength() {\n    return this._maskService.actualValue.length || this._maskService.actualValue.length + this._maskService.prefix.length;\n  }\n\n  _createValidationError(actualValue) {\n    return {\n      mask: {\n        requiredMask: this._maskValue,\n        actualValue\n      }\n    };\n  }\n\n  _setMask() {\n    this._maskExpressionArray.some(mask => {\n      const specialChart = mask.split(\"\"\n      /* EMPTY_STRING */\n      ).some(char => this._maskService.specialCharacters.includes(char));\n\n      if (specialChart && this._inputValue && !mask.includes(\"S\"\n      /* LETTER_S */\n      ) || mask.includes(\"{\"\n      /* CURLY_BRACKETS_LEFT */\n      )) {\n        const test = this._maskService.removeMask(this._inputValue)?.length <= this._maskService.removeMask(mask)?.length;\n\n        if (test) {\n          this._maskValue = this.maskExpression = this._maskService.maskExpression = mask.includes(\"{\"\n          /* CURLY_BRACKETS_LEFT */\n          ) ? this._maskService._repeatPatternSymbols(mask) : mask;\n          return test;\n        } else {\n          const expression = this._maskExpressionArray[this._maskExpressionArray.length - 1] ?? \"\"\n          /* EMPTY_STRING */\n          ;\n          this._maskValue = this.maskExpression = this._maskService.maskExpression = expression.includes(\"{\"\n          /* CURLY_BRACKETS_LEFT */\n          ) ? this._maskService._repeatPatternSymbols(expression) : expression;\n        }\n      } else {\n        const check = this._maskService.removeMask(this._inputValue)?.split(\"\"\n        /* EMPTY_STRING */\n        ).every((character, index) => {\n          const indexMask = mask.charAt(index);\n          return this._maskService._checkSymbolMask(character, indexMask);\n        });\n\n        if (check) {\n          this._maskValue = this.maskExpression = this._maskService.maskExpression = mask;\n          return check;\n        }\n      }\n    });\n  }\n\n}\n\nNgxMaskDirective.ɵfac = function NgxMaskDirective_Factory(t) {\n  return new (t || NgxMaskDirective)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(NGX_MASK_CONFIG), i0.ɵɵdirectiveInject(NgxMaskService));\n};\n\nNgxMaskDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgxMaskDirective,\n  selectors: [[\"input\", \"mask\", \"\"], [\"textarea\", \"mask\", \"\"]],\n  hostBindings: function NgxMaskDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"paste\", function NgxMaskDirective_paste_HostBindingHandler() {\n        return ctx.onPaste();\n      })(\"focus\", function NgxMaskDirective_focus_HostBindingHandler($event) {\n        return ctx.onFocus($event);\n      })(\"ngModelChange\", function NgxMaskDirective_ngModelChange_HostBindingHandler($event) {\n        return ctx.onModelChange($event);\n      })(\"input\", function NgxMaskDirective_input_HostBindingHandler($event) {\n        return ctx.onInput($event);\n      })(\"compositionstart\", function NgxMaskDirective_compositionstart_HostBindingHandler($event) {\n        return ctx.onCompositionStart($event);\n      })(\"compositionend\", function NgxMaskDirective_compositionend_HostBindingHandler($event) {\n        return ctx.onCompositionEnd($event);\n      })(\"blur\", function NgxMaskDirective_blur_HostBindingHandler($event) {\n        return ctx.onBlur($event);\n      })(\"click\", function NgxMaskDirective_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      })(\"keydown\", function NgxMaskDirective_keydown_HostBindingHandler($event) {\n        return ctx.onKeyDown($event);\n      });\n    }\n  },\n  inputs: {\n    maskExpression: [\"mask\", \"maskExpression\"],\n    specialCharacters: \"specialCharacters\",\n    patterns: \"patterns\",\n    prefix: \"prefix\",\n    suffix: \"suffix\",\n    thousandSeparator: \"thousandSeparator\",\n    decimalMarker: \"decimalMarker\",\n    dropSpecialCharacters: \"dropSpecialCharacters\",\n    hiddenInput: \"hiddenInput\",\n    showMaskTyped: \"showMaskTyped\",\n    placeHolderCharacter: \"placeHolderCharacter\",\n    shownMaskExpression: \"shownMaskExpression\",\n    showTemplate: \"showTemplate\",\n    clearIfNotMatch: \"clearIfNotMatch\",\n    validation: \"validation\",\n    separatorLimit: \"separatorLimit\",\n    allowNegativeNumbers: \"allowNegativeNumbers\",\n    leadZeroDateTime: \"leadZeroDateTime\",\n    leadZero: \"leadZero\",\n    triggerOnMaskChange: \"triggerOnMaskChange\",\n    apm: \"apm\",\n    inputTransformFn: \"inputTransformFn\",\n    outputTransformFn: \"outputTransformFn\",\n    keepCharacterPositions: \"keepCharacterPositions\"\n  },\n  outputs: {\n    maskFilled: \"maskFilled\"\n  },\n  exportAs: [\"mask\", \"ngxMask\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => NgxMaskDirective),\n    multi: true\n  }, {\n    provide: NG_VALIDATORS,\n    useExisting: forwardRef(() => NgxMaskDirective),\n    multi: true\n  }, NgxMaskService]), i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'input[mask], textarea[mask]',\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NgxMaskDirective),\n        multi: true\n      }, {\n        provide: NG_VALIDATORS,\n        useExisting: forwardRef(() => NgxMaskDirective),\n        multi: true\n      }, NgxMaskService],\n      exportAs: 'mask,ngxMask'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NGX_MASK_CONFIG]\n      }]\n    }, {\n      type: NgxMaskService\n    }];\n  }, {\n    maskExpression: [{\n      type: Input,\n      args: ['mask']\n    }],\n    specialCharacters: [{\n      type: Input\n    }],\n    patterns: [{\n      type: Input\n    }],\n    prefix: [{\n      type: Input\n    }],\n    suffix: [{\n      type: Input\n    }],\n    thousandSeparator: [{\n      type: Input\n    }],\n    decimalMarker: [{\n      type: Input\n    }],\n    dropSpecialCharacters: [{\n      type: Input\n    }],\n    hiddenInput: [{\n      type: Input\n    }],\n    showMaskTyped: [{\n      type: Input\n    }],\n    placeHolderCharacter: [{\n      type: Input\n    }],\n    shownMaskExpression: [{\n      type: Input\n    }],\n    showTemplate: [{\n      type: Input\n    }],\n    clearIfNotMatch: [{\n      type: Input\n    }],\n    validation: [{\n      type: Input\n    }],\n    separatorLimit: [{\n      type: Input\n    }],\n    allowNegativeNumbers: [{\n      type: Input\n    }],\n    leadZeroDateTime: [{\n      type: Input\n    }],\n    leadZero: [{\n      type: Input\n    }],\n    triggerOnMaskChange: [{\n      type: Input\n    }],\n    apm: [{\n      type: Input\n    }],\n    inputTransformFn: [{\n      type: Input\n    }],\n    outputTransformFn: [{\n      type: Input\n    }],\n    keepCharacterPositions: [{\n      type: Input\n    }],\n    maskFilled: [{\n      type: Output\n    }],\n    onPaste: [{\n      type: HostListener,\n      args: ['paste']\n    }],\n    onFocus: [{\n      type: HostListener,\n      args: ['focus', ['$event']]\n    }],\n    onModelChange: [{\n      type: HostListener,\n      args: ['ngModelChange', ['$event']]\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }],\n    onCompositionStart: [{\n      type: HostListener,\n      args: ['compositionstart', ['$event']]\n    }],\n    onCompositionEnd: [{\n      type: HostListener,\n      args: ['compositionend', ['$event']]\n    }],\n    onBlur: [{\n      type: HostListener,\n      args: ['blur', ['$event']]\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\n\nclass NgxMaskPipe {\n  constructor(_maskService) {\n    this._maskService = _maskService;\n    this.defaultOptions = {};\n    this._maskExpressionArray = [];\n    this.mask = '';\n  }\n\n  transform(value, mask, {\n    patterns,\n    ...config\n  } = {}) {\n    const currentConfig = {\n      maskExpression: mask,\n      ...this.defaultOptions,\n      ...config,\n      patterns: { ...this._maskService.patterns,\n        ...patterns\n      }\n    };\n    Object.entries(currentConfig).forEach(([key, value]) => {\n      //eslint-disable-next-line  @typescript-eslint/no-explicit-any\n      this._maskService[key] = value;\n    });\n\n    if (mask.includes('||')) {\n      if (mask.split('||').length > 1) {\n        this._maskExpressionArray = mask.split('||').sort((a, b) => {\n          return a.length - b.length;\n        });\n\n        this._setMask(value);\n\n        return this._maskService.applyMask(`${value}`, this.mask);\n      } else {\n        this._maskExpressionArray = [];\n        return this._maskService.applyMask(`${value}`, this.mask);\n      }\n    }\n\n    if (mask.includes(\"{\"\n    /* CURLY_BRACKETS_LEFT */\n    )) {\n      return this._maskService.applyMask(`${value}`, this._maskService._repeatPatternSymbols(mask));\n    }\n\n    if (mask.startsWith(\"separator\"\n    /* SEPARATOR */\n    )) {\n      if (config.decimalMarker) {\n        this._maskService.decimalMarker = config.decimalMarker;\n      }\n\n      if (config.thousandSeparator) {\n        this._maskService.thousandSeparator = config.thousandSeparator;\n      }\n\n      if (config.leadZero) {\n        // eslint-disable-next-line no-param-reassign\n        this._maskService.leadZero = config.leadZero;\n      } // eslint-disable-next-line no-param-reassign\n\n\n      value = String(value);\n\n      const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n\n      if (!Array.isArray(this._maskService.decimalMarker)) {\n        // eslint-disable-next-line no-param-reassign\n        value = this._maskService.decimalMarker !== localeDecimalMarker ? value.replace(localeDecimalMarker, this._maskService.decimalMarker) : value;\n      }\n\n      if (this._maskService.leadZero && value && this._maskService.dropSpecialCharacters !== false) {\n        // eslint-disable-next-line no-param-reassign\n        value = this._maskService._checkPrecision(mask, value);\n      }\n\n      if (this._maskService.decimalMarker === \",\"\n      /* COMMA */\n      ) {\n        // eslint-disable-next-line no-param-reassign\n        value = value.toString().replace(\".\"\n        /* DOT */\n        , \",\"\n        /* COMMA */\n        );\n      }\n\n      this._maskService.isNumberValue = true;\n    }\n\n    if (value === null || value === undefined) {\n      return this._maskService.applyMask('', mask);\n    }\n\n    return this._maskService.applyMask(`${value}`, mask);\n  }\n\n  _setMask(value) {\n    if (this._maskExpressionArray.length > 0) {\n      this._maskExpressionArray.some(mask => {\n        const test = this._maskService.removeMask(value)?.length <= this._maskService.removeMask(mask)?.length;\n\n        if (value && test) {\n          this.mask = mask;\n          return test;\n        } else {\n          const expression = this._maskExpressionArray[this._maskExpressionArray.length - 1] ?? \"\"\n          /* EMPTY_STRING */\n          ;\n          this.mask = expression;\n        }\n      });\n    }\n  }\n\n}\n\nNgxMaskPipe.ɵfac = function NgxMaskPipe_Factory(t) {\n  return new (t || NgxMaskPipe)(i0.ɵɵdirectiveInject(NgxMaskService, 16));\n};\n\nNgxMaskPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"mask\",\n  type: NgxMaskPipe,\n  pure: true\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'mask',\n      pure: true\n    }]\n  }], function () {\n    return [{\n      type: NgxMaskService\n    }];\n  }, null);\n})();\n/**\n * @internal\n */\n\n\nfunction _configFactory(initConfig, configValue) {\n  return configValue instanceof Function ? { ...initConfig,\n    ...configValue()\n  } : { ...initConfig,\n    ...configValue\n  };\n}\n\nclass NgxMaskModule {\n  static forRoot(configValue) {\n    return {\n      ngModule: NgxMaskModule,\n      providers: [{\n        provide: NEW_CONFIG,\n        useValue: configValue\n      }, {\n        provide: INITIAL_CONFIG,\n        useValue: initialConfig\n      }, {\n        provide: NGX_MASK_CONFIG,\n        useFactory: _configFactory,\n        deps: [INITIAL_CONFIG, NEW_CONFIG]\n      }, NgxMaskService]\n    };\n  }\n\n  static forChild() {\n    return {\n      ngModule: NgxMaskModule\n    };\n  }\n\n}\n\nNgxMaskModule.ɵfac = function NgxMaskModule_Factory(t) {\n  return new (t || NgxMaskModule)();\n};\n\nNgxMaskModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxMaskModule\n});\nNgxMaskModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxMaskModule, [{\n    type: NgModule,\n    args: [{\n      exports: [NgxMaskDirective, NgxMaskPipe],\n      declarations: [NgxMaskDirective, NgxMaskPipe]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { INITIAL_CONFIG, NEW_CONFIG, NGX_MASK_CONFIG, NgxMaskDirective, NgxMaskModule, NgxMaskPipe, NgxMaskService, _configFactory, initialConfig, timeMasks, withoutValidation };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/ngx-mask/fesm2020/ngx-mask.mjs"], "names": ["i0", "InjectionToken", "EventEmitter", "Injectable", "Inject", "Optional", "forwardRef", "Directive", "Input", "Output", "HostListener", "<PERSON><PERSON>", "NgModule", "DOCUMENT", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "NGX_MASK_CONFIG", "NEW_CONFIG", "INITIAL_CONFIG", "initialConfig", "suffix", "prefix", "thousandSeparator", "decimalMarker", "clearIfNotMatch", "showTemplate", "showMaskTyped", "placeHolderCharacter", "dropSpecialCharacters", "hiddenInput", "undefined", "shownMaskExpression", "separatorLimit", "allowNegativeNumbers", "validation", "specialCharacters", "leadZeroDateTime", "apm", "leadZero", "keepCharacterPositions", "triggerOnMaskChange", "inputTransformFn", "value", "outputTransformFn", "maskFilled", "patterns", "pattern", "RegExp", "optional", "X", "symbol", "A", "S", "U", "L", "d", "m", "M", "H", "h", "s", "timeMasks", "withoutValidation", "NgxMaskApplierService", "constructor", "_config", "_shift", "Set", "plusOnePosition", "maskExpression", "actualValue", "showKeepCharacterExp", "deletedSpecialCharacter", "_formatWithSeparators", "str", "thousandSeparatorChar", "decimalChars", "precision", "x", "decimalChar", "Array", "isArray", "regExp", "map", "v", "indexOf", "join", "split", "match", "decimals", "length", "res", "replace", "slice", "rgx", "test", "substring", "percentage", "sanitizedStr", "Number", "isNaN", "getPrecision", "Infinity", "checkAndRemoveSuffix", "inputValue", "i", "substr", "includes", "checkInputPrecision", "marker", "find", "dm", "precisionRegEx", "_charToRegExpExpression", "precisionMatch", "precisionMatchLength", "diff", "_compareOrIncludes", "applyMaskWithPattern", "maskAndPattern", "mask", "customPattern", "applyMask", "position", "justPasted", "backspaced", "cb", "cursor", "result", "multi", "backspaceShift", "shift", "stepBack", "inputArray", "toString", "valuesIP", "ipError", "_validIP", "arr", "push", "cpfCnpjError", "startsWith", "_stripToDecimal", "base", "_splitPercentZero", "thousandSeparatorCharEscaped", "invalid<PERSON>hars", "invalidCharRegexp", "strForSep", "commaShift", "shiftStep", "add", "clear", "inputSymbol", "symbolStarInPattern", "_checkSymbolMask", "_shiftStep", "daysCount", "inputValueCursor", "inputValueCursorPlusOne", "inputValueCursorPlusTwo", "inputValueCursorMinusOne", "inputValueCursorMinusTwo", "inputValueCursorMinusThree", "inputValueSliceMinusThreeMinusOne", "inputValueSliceMinusOnePlusOne", "inputValueSliceCursorPlusTwo", "inputValueSliceMinusTwoCursor", "maskStartWithMonth", "startWithMonthInput", "monthsCount", "withoutDays", "<PERSON><PERSON><PERSON>", "day1monthInput", "day2monthInput", "day2monthInputDot", "day1monthPaste", "day2monthPaste", "_findSpecialChar", "newPosition", "has", "actualShift", "onlySpecial", "every", "char", "_findDropSpecialChar", "val", "maskSymbol", "filter", "idx", "isDecimalMarker", "charsToEscape", "inputLength", "comparedValue", "excludedValue", "some", "index", "decimalIndex", "parsedValue", "parseInt", "integerPart", "decimalPart", "integerString", "decimal", "ɵfac", "ɵprov", "type", "decorators", "args", "NgxMaskService", "document", "_elementRef", "_renderer", "isNumberValue", "maskIsShown", "selStart", "selEnd", "writingValue", "maskChanged", "_maskExpressionArray", "_emitValue", "_previousValue", "_currentValue", "onChange", "_", "showMaskInInput", "formControlResult", "getSymbol", "newInputValue", "actualResult", "splice", "removeMask", "shiftTypedSymbols", "Boolean", "getActualValue", "item", "hideInput", "resLen", "prefNmask", "countSkipedSymbol", "_numberSkipedSymbols", "regex", "exec", "applyValueChanges", "formElement", "nativeElement", "_getActiveElement", "clearIfNotMatchFn", "curr", "compare", "maskChar", "symbolToReplace", "currSymbol", "replaceSymbol", "numberToString", "String", "toLocaleString", "useGrouping", "maximumFractionDigits", "inputVal", "Error", "_checkForIp", "_checkForCpfCnpj", "formElementProperty", "name", "Promise", "resolve", "then", "setProperty", "checkDropSpecialCharAmount", "chars", "_removeMask", "_removeSuffix", "_removePrefix", "concat", "cpf", "cnpj", "shadowRootEl", "activeElement", "shadowRoot", "_toNumber", "_checkSymbols", "num", "specialCharactersForRemove", "_regExpForRemove", "_retrieveSeparatorV<PERSON>ue", "_checkPatternForSpace", "_replaceDecimalMarkerToDot", "markers", "separatorPrecision", "_retrieveSeparatorPrecision", "separatorValue", "_checkPrecision", "key", "hasOwnProperty", "patternString", "maskExpretion", "matcher", "separatorExpression", "toFixed", "_repeatPatternSymbols", "maskExp", "reduce", "accum", "currVal", "_start", "_end", "repeatNumber", "replaceWith", "symbols", "currentLocaleDecimalMarker", "ElementRef", "Renderer2", "NgxMaskDirective", "_maskService", "_maskValue", "_isFocused", "_position", "_justPasted", "_isComposing", "onTouch", "ngOnChanges", "changes", "currentValue", "previousValue", "firstChange", "sort", "a", "b", "_setMask", "c", "requestAnimationFrame", "click", "_applyMask", "validate", "_createValidationError", "_validateTime", "counterOfOpt", "lastIndexOf", "opt", "array", "lastIndexArray", "special", "emit", "onPaste", "onFocus", "onModelChange", "onInput", "e", "el", "target", "transformedValue", "_inputValue", "selectionStart", "prefixLength", "checkSymbols", "checkSpecialCharacter", "selectRangeBackspace", "_code", "part1", "part2", "caretShift", "_backspaceShift", "positionToApply", "_getActualInputLength", "setSelectionRange", "console", "warn", "onCompositionStart", "onCompositionEnd", "onBlur", "repeat", "onClick", "posStart", "posEnd", "selectionEnd", "keyCode", "focus", "nextValue", "onKeyDown", "code", "preventDefault", "checkSelectionOnDeletion", "cursorStart", "readOnly", "ctrl<PERSON>ey", "metaKey", "writeValue", "controlValue", "setDisabledState", "disable", "localeDecimalMarker", "registerOnChange", "fn", "registerOnTouched", "Math", "min", "max", "isDisabled", "rowMaskLen", "requiredMask", "expression", "check", "character", "indexMask", "char<PERSON>t", "ɵdir", "provide", "useExisting", "selector", "providers", "exportAs", "NgxMaskPipe", "defaultOptions", "transform", "config", "currentConfig", "Object", "entries", "for<PERSON>ach", "ɵpipe", "pure", "_configFactory", "initConfig", "config<PERSON><PERSON><PERSON>", "Function", "NgxMaskModule", "forRoot", "ngModule", "useValue", "useFactory", "deps", "<PERSON><PERSON><PERSON><PERSON>", "ɵmod", "ɵinj", "exports", "declarations"], "mappings": ";AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,YAAzB,EAAuCC,UAAvC,EAAmDC,MAAnD,EAA2DC,QAA3D,EAAqEC,UAArE,EAAiFC,SAAjF,EAA4FC,KAA5F,EAAmGC,MAAnG,EAA2GC,YAA3G,EAAyHC,IAAzH,EAA+HC,QAA/H,QAA+I,eAA/I;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,SAASC,iBAAT,EAA4BC,aAA5B,QAAiD,gBAAjD;AAEA,MAAMC,eAAe,GAAG,IAAIf,cAAJ,CAAmB,iBAAnB,CAAxB;AACA,MAAMgB,UAAU,GAAG,IAAIhB,cAAJ,CAAmB,qBAAnB,CAAnB;AACA,MAAMiB,cAAc,GAAG,IAAIjB,cAAJ,CAAmB,yBAAnB,CAAvB;AACA,MAAMkB,aAAa,GAAG;AAClBC,EAAAA,MAAM,EAAE,EADU;AAElBC,EAAAA,MAAM,EAAE,EAFU;AAGlBC,EAAAA,iBAAiB,EAAE,GAHD;AAIlBC,EAAAA,aAAa,EAAE,CAAC,GAAD,EAAM,GAAN,CAJG;AAKlBC,EAAAA,eAAe,EAAE,KALC;AAMlBC,EAAAA,YAAY,EAAE,KANI;AAOlBC,EAAAA,aAAa,EAAE,KAPG;AAQlBC,EAAAA,oBAAoB,EAAE,GARJ;AASlBC,EAAAA,qBAAqB,EAAE,IATL;AAUlBC,EAAAA,WAAW,EAAEC,SAVK;AAWlBC,EAAAA,mBAAmB,EAAE,EAXH;AAYlBC,EAAAA,cAAc,EAAE,EAZE;AAalBC,EAAAA,oBAAoB,EAAE,KAbJ;AAclBC,EAAAA,UAAU,EAAE,IAdM;AAelB;AACAC,EAAAA,iBAAiB,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB,EAA0B,GAA1B,EAA+B,GAA/B,EAAoC,GAApC,EAAyC,GAAzC,EAA8C,GAA9C,EAAmD,GAAnD,EAAwD,GAAxD,EAA6D,GAA7D,EAAkE,GAAlE,CAhBD;AAiBlBC,EAAAA,gBAAgB,EAAE,KAjBA;AAkBlBC,EAAAA,GAAG,EAAE,KAlBa;AAmBlBC,EAAAA,QAAQ,EAAE,KAnBQ;AAoBlBC,EAAAA,sBAAsB,EAAE,KApBN;AAqBlBC,EAAAA,mBAAmB,EAAE,KArBH;AAsBlBC,EAAAA,gBAAgB,EAAGC,KAAD,IAAWA,KAtBX;AAuBlBC,EAAAA,iBAAiB,EAAGD,KAAD,IAAWA,KAvBZ;AAwBlBE,EAAAA,UAAU,EAAE,IAAI1C,YAAJ,EAxBM;AAyBlB2C,EAAAA,QAAQ,EAAE;AACN,SAAK;AACDC,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,KAAX;AADR,KADC;AAIN,SAAK;AACDD,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,KAAX,CADR;AAEDC,MAAAA,QAAQ,EAAE;AAFT,KAJC;AAQNC,IAAAA,CAAC,EAAE;AACCH,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,KAAX,CADV;AAECG,MAAAA,MAAM,EAAE;AAFT,KARG;AAYNC,IAAAA,CAAC,EAAE;AACCL,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,aAAX;AADV,KAZG;AAeNK,IAAAA,CAAC,EAAE;AACCN,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,UAAX;AADV,KAfG;AAkBNM,IAAAA,CAAC,EAAE;AACCP,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,OAAX;AADV,KAlBG;AAqBNO,IAAAA,CAAC,EAAE;AACCR,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,OAAX;AADV,KArBG;AAwBNQ,IAAAA,CAAC,EAAE;AACCT,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,KAAX;AADV,KAxBG;AA2BNS,IAAAA,CAAC,EAAE;AACCV,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,KAAX;AADV,KA3BG;AA8BNU,IAAAA,CAAC,EAAE;AACCX,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,KAAX;AADV,KA9BG;AAiCNW,IAAAA,CAAC,EAAE;AACCZ,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,KAAX;AADV,KAjCG;AAoCNY,IAAAA,CAAC,EAAE;AACCb,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,KAAX;AADV,KApCG;AAuCNa,IAAAA,CAAC,EAAE;AACCd,MAAAA,OAAO,EAAE,IAAIC,MAAJ,CAAW,KAAX;AADV;AAvCG;AAzBQ,CAAtB;AAqEA,MAAMc,SAAS,GAAG,CACd;AAAW;AADG,EAEd;AAAQ;AAFM,EAGd;AAAQ;AAHM,CAAlB;AAKA,MAAMC,iBAAiB,GAAG,CACtB;AAAU;AADY,EAEtB;AAAK;AAFiB,EAGtB;AAAK;AAHiB,EAItB;AAAK;AAJiB,EAKtB;AAAY;AALU,EAMtB;AAAa;AANS,EAOtB;AAAQ;AAPc,EAQtB;AAAK;AARiB,EAStB;AAAK;AATiB,CAA1B;;AAYA,MAAMC,qBAAN,CAA4B;AACxBC,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,SAAKA,OAAL,GAAeA,OAAf;AACA,SAAKC,MAAL,GAAc,IAAIC,GAAJ,EAAd;AACA,SAAKC,eAAL,GAAuB,KAAvB;AACA,SAAKC,cAAL,GAAsB,EAAtB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,oBAAL,GAA4B,EAA5B;AACA,SAAKxC,mBAAL,GAA2B,EAA3B;AACA,SAAKyC,uBAAL,GAA+B,KAA/B;;AACA,SAAKC,qBAAL,GAA6B,CAACC,GAAD,EAAMC,qBAAN,EAA6BC,YAA7B,EAA2CC,SAA3C,KAAyD;AAClF,UAAIC,CAAC,GAAG,EAAR;AACA,UAAIC,WAAW,GAAG,EAAlB;;AACA,UAAIC,KAAK,CAACC,OAAN,CAAcL,YAAd,CAAJ,EAAiC;AAC7B,cAAMM,MAAM,GAAG,IAAInC,MAAJ,CAAW6B,YAAY,CAACO,GAAb,CAAkBC,CAAD,IAAQ,eAAeC,OAAf,CAAuBD,CAAvB,KAA6B,CAA7B,GAAkC,KAAIA,CAAE,EAAxC,GAA4CA,CAArE,EAAyEE,IAAzE,CAA8E,GAA9E,CAAX,CAAf;AACAR,QAAAA,CAAC,GAAGJ,GAAG,CAACa,KAAJ,CAAUL,MAAV,CAAJ;AACAH,QAAAA,WAAW,GAAGL,GAAG,CAACc,KAAJ,CAAUN,MAAV,IAAoB,CAApB,KAA0B;AAAG;AAA3C;AACH,OAJD,MAKK;AACDJ,QAAAA,CAAC,GAAGJ,GAAG,CAACa,KAAJ,CAAUX,YAAV,CAAJ;AACAG,QAAAA,WAAW,GAAGH,YAAd;AACH;;AACD,YAAMa,QAAQ,GAAGX,CAAC,CAACY,MAAF,GAAW,CAAX,GAAgB,GAAEX,WAAY,GAAED,CAAC,CAAC,CAAD,CAAI,EAArC,GAAyC;AAAG;AAA7D;AACA,UAAIa,GAAG,GAAGb,CAAC,CAAC,CAAD,CAAD,IAAQ;AAAG;AAArB;AACA,YAAM9C,cAAc,GAAG,KAAKA,cAAL,CAAoB4D,OAApB,CAA4B,KAA5B,EAAmC;AAAG;AAAtC,OAAvB;;AACA,UAAI5D,cAAc,IAAI,CAACA,cAAvB,EAAuC;AACnC,YAAI2D,GAAG,CAAC,CAAD,CAAH,KAAW;AAAI;AAAnB,UAAgC;AAC5BA,UAAAA,GAAG,GAAI,IAAGA,GAAG,CAACE,KAAJ,CAAU,CAAV,EAAaF,GAAG,CAACD,MAAjB,EAAyBG,KAAzB,CAA+B,CAA/B,EAAkC7D,cAAc,CAAC0D,MAAjD,CAAyD,EAAnE;AACH,SAFD,MAGK;AACDC,UAAAA,GAAG,GAAGA,GAAG,CAACE,KAAJ,CAAU,CAAV,EAAa7D,cAAc,CAAC0D,MAA5B,CAAN;AACH;AACJ;;AACD,YAAMI,GAAG,GAAG,cAAZ;;AACA,aAAOnB,qBAAqB,IAAImB,GAAG,CAACC,IAAJ,CAASJ,GAAT,CAAhC,EAA+C;AAC3CA,QAAAA,GAAG,GAAGA,GAAG,CAACC,OAAJ,CAAYE,GAAZ,EAAiB,OAAOnB,qBAAP,GAA+B,IAAhD,CAAN;AACH;;AACD,UAAIE,SAAS,KAAK/C,SAAlB,EAA6B;AACzB,eAAO6D,GAAG,GAAGF,QAAb;AACH,OAFD,MAGK,IAAIZ,SAAS,KAAK,CAAlB,EAAqB;AACtB,eAAOc,GAAP;AACH;;AACD,aAAOA,GAAG,GAAGF,QAAQ,CAACO,SAAT,CAAmB,CAAnB,EAAsBnB,SAAS,GAAG,CAAlC,CAAb;AACH,KAlCD;;AAmCA,SAAKoB,UAAL,GAAmBvB,GAAD,IAAS;AACvB,YAAMwB,YAAY,GAAGxB,GAAG,CAACkB,OAAJ,CAAY,GAAZ,EAAiB,GAAjB,CAArB;AACA,YAAMlD,KAAK,GAAGyD,MAAM,CAACD,YAAD,CAApB;AACA,aAAO,CAACE,KAAK,CAAC1D,KAAD,CAAN,IAAiBA,KAAK,IAAI,CAA1B,IAA+BA,KAAK,IAAI,GAA/C;AACH,KAJD;;AAKA,SAAK2D,YAAL,GAAqBhC,cAAD,IAAoB;AACpC,YAAMS,CAAC,GAAGT,cAAc,CAACkB,KAAf,CAAqB;AAAI;AAAzB,OAAV;;AACA,UAAIT,CAAC,CAACY,MAAF,GAAW,CAAf,EAAkB;AACd,eAAOS,MAAM,CAACrB,CAAC,CAACA,CAAC,CAACY,MAAF,GAAW,CAAZ,CAAF,CAAb;AACH;;AACD,aAAOY,QAAP;AACH,KAND;;AAOA,SAAKC,oBAAL,GAA6BC,UAAD,IAAgB;AACxC,WAAK,IAAIC,CAAC,GAAG,KAAKrF,MAAL,EAAasE,MAAb,GAAsB,CAAnC,EAAsCe,CAAC,IAAI,CAA3C,EAA8CA,CAAC,EAA/C,EAAmD;AAC/C,cAAMC,MAAM,GAAG,KAAKtF,MAAL,CAAY4E,SAAZ,CAAsBS,CAAtB,EAAyB,KAAKrF,MAAL,EAAasE,MAAtC,CAAf;;AACA,YAAIc,UAAU,CAACG,QAAX,CAAoBD,MAApB,KACAD,CAAC,KAAK,KAAKrF,MAAL,EAAasE,MAAb,GAAsB,CAD5B,KAECe,CAAC,GAAG,CAAJ,GAAQ,CAAR,IACG,CAACD,UAAU,CAACG,QAAX,CAAoB,KAAKvF,MAAL,CAAY4E,SAAZ,CAAsBS,CAAC,GAAG,CAA1B,EAA6B,KAAKrF,MAAL,EAAasE,MAA1C,CAApB,CAHL,CAAJ,EAGkF;AAC9E,iBAAOc,UAAU,CAACZ,OAAX,CAAmBc,MAAnB,EAA2B;AAAG;AAA9B,WAAP;AACH;AACJ;;AACD,aAAOF,UAAP;AACH,KAXD;;AAYA,SAAKI,mBAAL,GAA2B,CAACJ,UAAD,EAAa3B,SAAb,EAAwBtD,aAAxB,KAA0C;AACjE,UAAIsD,SAAS,GAAGyB,QAAhB,EAA0B;AACtB;AACA,YAAItB,KAAK,CAACC,OAAN,CAAc1D,aAAd,CAAJ,EAAkC;AAC9B,gBAAMsF,MAAM,GAAGtF,aAAa,CAACuF,IAAd,CAAoBC,EAAD,IAAQA,EAAE,KAAK,KAAKzF,iBAAvC,CAAf,CAD8B,CAE9B;;AACAC,UAAAA,aAAa,GAAGsF,MAAM,GAAGA,MAAH,GAAYtF,aAAa,CAAC,CAAD,CAA/C;AACH;;AACD,cAAMyF,cAAc,GAAG,IAAIjE,MAAJ,CAAW,KAAKkE,uBAAL,CAA6B1F,aAA7B,IAA+C,OAAMsD,SAAU,MAA1E,CAAvB;AACA,cAAMqC,cAAc,GAAGV,UAAU,CAAChB,KAAX,CAAiBwB,cAAjB,CAAvB;AACA,cAAMG,oBAAoB,GAAG,CAACD,cAAc,IAAIA,cAAc,CAAC,CAAD,CAAd,EAAmBxB,MAAtC,KAAiD,CAA9E;;AACA,YAAIyB,oBAAoB,GAAG,CAAvB,GAA2BtC,SAA/B,EAA0C;AACtC,gBAAMuC,IAAI,GAAGD,oBAAoB,GAAG,CAAvB,GAA2BtC,SAAxC,CADsC,CAEtC;;AACA2B,UAAAA,UAAU,GAAGA,UAAU,CAACR,SAAX,CAAqB,CAArB,EAAwBQ,UAAU,CAACd,MAAX,GAAoB0B,IAA5C,CAAb;AACH;;AACD,YAAIvC,SAAS,KAAK,CAAd,IACA,KAAKwC,kBAAL,CAAwBb,UAAU,CAACA,UAAU,CAACd,MAAX,GAAoB,CAArB,CAAlC,EAA2DnE,aAA3D,EAA0E,KAAKD,iBAA/E,CADJ,EACuG;AACnG;AACAkF,UAAAA,UAAU,GAAGA,UAAU,CAACR,SAAX,CAAqB,CAArB,EAAwBQ,UAAU,CAACd,MAAX,GAAoB,CAA5C,CAAb;AACH;AACJ;;AACD,aAAOc,UAAP;AACH,KAvBD;;AAwBA,SAAK5E,qBAAL,GAA6B,KAAKqC,OAAL,CAAarC,qBAA1C;AACA,SAAKC,WAAL,GAAmB,KAAKoC,OAAL,CAAapC,WAAhC;AACA,SAAKL,eAAL,GAAuB,KAAKyC,OAAL,CAAazC,eAApC;AACA,SAAKW,iBAAL,GAAyB,KAAK8B,OAAL,CAAa9B,iBAAtC;AACA,SAAKU,QAAL,GAAgB,KAAKoB,OAAL,CAAapB,QAA7B;AACA,SAAKxB,MAAL,GAAc,KAAK4C,OAAL,CAAa5C,MAA3B;AACA,SAAKD,MAAL,GAAc,KAAK6C,OAAL,CAAa7C,MAA3B;AACA,SAAKE,iBAAL,GAAyB,KAAK2C,OAAL,CAAa3C,iBAAtC;AACA,SAAKC,aAAL,GAAqB,KAAK0C,OAAL,CAAa1C,aAAlC;AACA,SAAKG,aAAL,GAAqB,KAAKuC,OAAL,CAAavC,aAAlC;AACA,SAAKC,oBAAL,GAA4B,KAAKsC,OAAL,CAAatC,oBAAzC;AACA,SAAKO,UAAL,GAAkB,KAAK+B,OAAL,CAAa/B,UAA/B;AACA,SAAKF,cAAL,GAAsB,KAAKiC,OAAL,CAAajC,cAAnC;AACA,SAAKC,oBAAL,GAA4B,KAAKgC,OAAL,CAAahC,oBAAzC;AACA,SAAKG,gBAAL,GAAwB,KAAK6B,OAAL,CAAa7B,gBAArC;AACA,SAAKE,QAAL,GAAgB,KAAK2B,OAAL,CAAa3B,QAA7B;AACA,SAAKD,GAAL,GAAW,KAAK4B,OAAL,CAAa5B,GAAxB;AACA,SAAKI,gBAAL,GAAwB,KAAKwB,OAAL,CAAaxB,gBAArC;AACA,SAAKE,iBAAL,GAAyB,KAAKsB,OAAL,CAAatB,iBAAtC;AACA,SAAKJ,sBAAL,GAA8B,KAAK0B,OAAL,CAAa1B,sBAA3C;AACH;;AACD+E,EAAAA,oBAAoB,CAACd,UAAD,EAAae,cAAb,EAA6B;AAC7C,UAAM,CAACC,IAAD,EAAOC,aAAP,IAAwBF,cAA9B;AACA,SAAKE,aAAL,GAAqBA,aAArB;AACA,WAAO,KAAKC,SAAL,CAAelB,UAAf,EAA2BgB,IAA3B,CAAP;AACH;;AACDE,EAAAA,SAAS,CAAClB,UAAD,EAAanC,cAAb,EAA6BsD,QAAQ,GAAG,CAAxC,EAA2CC,UAAU,GAAG,KAAxD,EAA+DC,UAAU,GAAG,KAA5E,EACT;AACAC,EAAAA,EAAE,GAAG,MAAM,CAAG,CAFL,EAEO;AACZ,QAAI,CAACzD,cAAD,IAAmB,OAAOmC,UAAP,KAAsB,QAA7C,EAAuD;AACnD,aAAO;AAAG;AAAV;AACH;;AACD,QAAIuB,MAAM,GAAG,CAAb;AACA,QAAIC,MAAM,GAAG,EAAb;AACA,QAAIC,KAAK,GAAG,KAAZ;AACA,QAAIC,cAAc,GAAG,KAArB;AACA,QAAIC,KAAK,GAAG,CAAZ;AACA,QAAIC,QAAQ,GAAG,KAAf;;AACA,QAAI5B,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoB,KAAKxE,MAAL,CAAYqE,MAAhC,MAA4C,KAAKrE,MAArD,EAA6D;AACzD;AACAmF,MAAAA,UAAU,GAAGA,UAAU,CAACX,KAAX,CAAiB,KAAKxE,MAAL,CAAYqE,MAA7B,EAAqCc,UAAU,CAACd,MAAhD,CAAb;AACH;;AACD,QAAI,CAAC,CAAC,KAAKtE,MAAP,IAAiBoF,UAAU,EAAEd,MAAZ,GAAqB,CAA1C,EAA6C;AACzC;AACAc,MAAAA,UAAU,GAAG,KAAKD,oBAAL,CAA0BC,UAA1B,CAAb;AACH;;AACD,QAAIA,UAAU,KAAK,GAAf,IAAsB,KAAKnF,MAA/B,EAAuC;AACnC;AACAmF,MAAAA,UAAU,GAAG,EAAb;AACH;;AACD,UAAM6B,UAAU,GAAG7B,UAAU,CAAC8B,QAAX,GAAsB/C,KAAtB,CAA4B;AAAG;AAA/B,KAAnB;;AACA,QAAI,KAAKtD,oBAAL,IACAuE,UAAU,CAACX,KAAX,CAAiBkC,MAAjB,EAAyBA,MAAM,GAAG,CAAlC,MAAyC;AAAI;AADjD,MAC8D;AAC1D;AACAC,MAAAA,MAAM,IAAIxB,UAAU,CAACX,KAAX,CAAiBkC,MAAjB,EAAyBA,MAAM,GAAG,CAAlC,CAAV;AACH;;AACD,QAAI1D,cAAc,KAAK;AAAK;AAA5B,MAAsC;AAClC,YAAMkE,QAAQ,GAAG/B,UAAU,CAACjB,KAAX,CAAiB;AAAI;AAArB,OAAjB;AACA,WAAKiD,OAAL,GAAe,KAAKC,QAAL,CAAcF,QAAd,CAAf,CAFkC,CAGlC;;AACAlE,MAAAA,cAAc,GAAG,iBAAjB;AACH;;AACD,UAAMqE,GAAG,GAAG,EAAZ;;AACA,SAAK,IAAIjC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,UAAU,CAACd,MAA/B,EAAuCe,CAAC,EAAxC,EAA4C;AACxC,UAAID,UAAU,CAACC,CAAD,CAAV,EAAejB,KAAf,CAAqB,KAArB,CAAJ,EAAiC;AAC7BkD,QAAAA,GAAG,CAACC,IAAJ,CAASnC,UAAU,CAACC,CAAD,CAAV,IAAiB;AAAG;AAA7B;AACH;AACJ;;AACD,QAAIpC,cAAc,KAAK;AAAW;AAAlC,MAAkD;AAC9C,WAAKuE,YAAL,GAAoBF,GAAG,CAAChD,MAAJ,KAAe,EAAf,IAAqBgD,GAAG,CAAChD,MAAJ,KAAe,EAAxD;;AACA,UAAIgD,GAAG,CAAChD,MAAJ,GAAa,EAAjB,EAAqB;AACjB;AACArB,QAAAA,cAAc,GAAG,oBAAjB;AACH,OAHD,MAIK;AACD;AACAA,QAAAA,cAAc,GAAG,gBAAjB;AACH;AACJ;;AACD,QAAIA,cAAc,CAACwE,UAAf,CAA0B;AAAU;AAApC,KAAJ,EAAwD;AACpD,UAAIrC,UAAU,CAAChB,KAAX,CAAiB,aAAjB,KACA;AACCgB,MAAAA,UAAU,CAAChB,KAAX,CAAiB,oCAAjB,KAA0D,CAACqC,UAFhE,EAE6E;AACzE;AACArB,QAAAA,UAAU,GAAG,KAAKsC,eAAL,CAAqBtC,UAArB,CAAb;AACA,cAAM3B,SAAS,GAAG,KAAKwB,YAAL,CAAkBhC,cAAlB,CAAlB,CAHyE,CAIzE;;AACAmC,QAAAA,UAAU,GAAG,KAAKI,mBAAL,CAAyBJ,UAAzB,EAAqC3B,SAArC,EAAgD,KAAKtD,aAArD,CAAb;AACH;;AACD,YAAMA,aAAa,GAAG,OAAO,KAAKA,aAAZ,KAA8B,QAA9B,GAAyC,KAAKA,aAA9C,GAA8D;AAAI;AAAxF;;AACA,UAAIiF,UAAU,CAACnB,OAAX,CAAmB9D,aAAnB,IAAoC,CAApC,IACA,CAAC,KAAK0E,UAAL,CAAgBO,UAAU,CAACR,SAAX,CAAqB,CAArB,EAAwBQ,UAAU,CAACnB,OAAX,CAAmB9D,aAAnB,CAAxB,CAAhB,CADL,EACkF;AAC9E,YAAIwH,IAAI,GAAGvC,UAAU,CAACR,SAAX,CAAqB,CAArB,EAAwBQ,UAAU,CAACnB,OAAX,CAAmB9D,aAAnB,IAAoC,CAA5D,CAAX;;AACA,YAAI,KAAKU,oBAAL,IACAuE,UAAU,CAACX,KAAX,CAAiBkC,MAAjB,EAAyBA,MAAM,GAAG,CAAlC,MAAyC;AAAI;AAD7C,WAEA,CAACF,UAFL,EAEiB;AACbkB,UAAAA,IAAI,GAAGvC,UAAU,CAACR,SAAX,CAAqB,CAArB,EAAwBQ,UAAU,CAACnB,OAAX,CAAmB9D,aAAnB,CAAxB,CAAP;AACH,SAN6E,CAO9E;;;AACAiF,QAAAA,UAAU,GAAI,GAAEuC,IAAK,GAAEvC,UAAU,CAACR,SAAX,CAAqBQ,UAAU,CAACnB,OAAX,CAAmB9D,aAAnB,CAArB,EAAwDiF,UAAU,CAACd,MAAnE,CAA2E,EAAlG;AACH;;AACD,UAAIhD,KAAK,GAAG,EAAZ;AACA,WAAKT,oBAAL,IACIuE,UAAU,CAACX,KAAX,CAAiBkC,MAAjB,EAAyBA,MAAM,GAAG,CAAlC,MAAyC;AAAI;AADjD,QAEOrF,KAAK,GAAG8D,UAAU,CAACX,KAAX,CAAiBkC,MAAM,GAAG,CAA1B,EAA6BA,MAAM,GAAGvB,UAAU,CAACd,MAAjD,CAFf,GAGOhD,KAAK,GAAG8D,UAHf;;AAIA,UAAI,KAAKP,UAAL,CAAgBvD,KAAhB,CAAJ,EAA4B;AACxBsF,QAAAA,MAAM,GAAG,KAAKgB,iBAAL,CAAuBxC,UAAvB,CAAT;AACH,OAFD,MAGK;AACDwB,QAAAA,MAAM,GAAG,KAAKgB,iBAAL,CAAuBxC,UAAU,CAACR,SAAX,CAAqB,CAArB,EAAwBQ,UAAU,CAACd,MAAX,GAAoB,CAA5C,CAAvB,CAAT;AACH;AACJ,KAjCD,MAkCK,IAAIrB,cAAc,CAACwE,UAAf,CAA0B;AAAY;AAAtC,KAAJ,EAA4D;AAC7D,UAAIrC,UAAU,CAAChB,KAAX,CAAiB,WAAjB,KACAgB,UAAU,CAAChB,KAAX,CAAiB,SAAjB,CADA,IAEAgB,UAAU,CAAChB,KAAX,CAAiB,aAAjB,CAFA,IAGAgB,UAAU,CAAChB,KAAX,CAAiB,sCAAjB,CAHA,IAIAgB,UAAU,CAAChB,KAAX,CAAiB,eAAjB,CAJJ,EAIuC;AACnC;AACAgB,QAAAA,UAAU,GAAG,KAAKsC,eAAL,CAAqBtC,UAArB,CAAb;AACH;;AACD,YAAM3B,SAAS,GAAG,KAAKwB,YAAL,CAAkBhC,cAAlB,CAAlB;AACA,YAAM9C,aAAa,GAAGyD,KAAK,CAACC,OAAN,CAAc,KAAK1D,aAAnB,IAChB;AAAI;AADY,QAEhB,KAAKA,aAFX;;AAGA,UAAIsD,SAAS,KAAK,CAAlB,EAAqB;AACjB;AACA2B,QAAAA,UAAU,GAAG,KAAKvE,oBAAL,GACPuE,UAAU,CAACd,MAAX,GAAoB,CAApB,IACEc,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AADxB,WAEEA,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAFxB,WAGEA,UAAU,CAAC,CAAD,CAAV,KAAkB,KAAKlF,iBAHzB,IAIEkF,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAJxB,WAKEA,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AALxB,UAMI,MAAMA,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAA/B,CANV,GAOIc,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAAtB,WACEA,UAAU,CAACd,MAAX,GAAoB,CADtB,IAEEc,UAAU,CAAC,CAAD,CAAV,KAAkB,KAAKlF,iBAFzB,IAGEkF,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAHxB,WAIEA,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAJxB,UAKIA,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAA/B,CALJ,GAMIc,UAdD,GAePA,UAAU,CAACd,MAAX,GAAoB,CAApB,IACEc,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AADxB,WAEEA,UAAU,CAAC,CAAD,CAAV,KAAkB,KAAKlF,iBAFzB,IAGEkF,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAHxB,WAIEA,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAJxB,UAKIA,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAA/B,CALJ,GAMIc,UArBV;AAsBH,OAxBD,MAyBK;AACD;AACA,YAAIA,UAAU,CAAC,CAAD,CAAV,KAAkBjF,aAAlB,IAAmCiF,UAAU,CAACd,MAAX,GAAoB,CAA3D,EAA8D;AAC1D;AACAc,UAAAA,UAAU,GACN;AAAI;AAAJ,YAAwBA,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAAX,GAAoB,CAAxC,CAD5B;AAEA,eAAKtB,eAAL,GAAuB,IAAvB;AACH;;AACD,YAAIoC,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAAtB,WACAA,UAAU,CAAC,CAAD,CAAV,KAAkBjF,aADlB,IAEAiF,UAAU,CAAC,CAAD,CAAV,KAAkB,KAAKlF,iBAF3B,EAE8C;AAC1C;AACAkF,UAAAA,UAAU,GACNA,UAAU,CAACd,MAAX,GAAoB,CAApB,GACMc,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoB,CAApB,IACEtE,aADF,GAEEiF,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAAX,GAAoB,CAAxC,CAHR,GAIMc,UALV;AAMA,eAAKpC,eAAL,GAAuB,IAAvB;AACH;;AACD,YAAI,KAAKnC,oBAAL,IACAuE,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AADtB,YAECA,UAAU,CAAC,CAAD,CAAV,KAAkBjF,aAAlB,IACGiF,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAH1B,SAAJ,EAGkD;AAC9C;AACAA,UAAAA,UAAU,GACNA,UAAU,CAAC,CAAD,CAAV,KAAkBjF,aAAlB,IAAmCiF,UAAU,CAACd,MAAX,GAAoB,CAAvD,GACMc,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoB,CAApB,IACE;AAAI;AADN,YAEEW,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAA/B,CAHR,GAIMc,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAAtB,aACEA,UAAU,CAACd,MAAX,GAAoB,CADtB,IAEEc,UAAU,CAAC,CAAD,CAAV,KAAkBjF,aAFpB,GAGIiF,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoB,CAApB,IACEtE,aADF,GAEEiF,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAA/B,CALN,GAMIc,UAXd;AAYA,eAAKpC,eAAL,GAAuB,IAAvB;AACH;AACJ;;AACD,UAAIyD,UAAJ,EAAgB;AACZ,YAAIrB,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAAtB,WACAA,UAAU,CAAC,CAAD,CAAV,KAAkB,KAAKjF,aADvB,KAECiF,UAAU,CAACmB,QAAD,CAAV,KAAyB;AAAI;AAA7B,WACGnB,UAAU,CAACmB,QAAD,CAAV,KAAyB,KAAKpG,aAHlC,CAAJ,EAGsD;AAClD;AACAiF,UAAAA,UAAU,GAAGA,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAA/B,CAAb;AACH;;AACD,YAAIc,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AAAtB,WACAA,UAAU,CAAC,CAAD,CAAV,KAAkB;AAAI;AADtB,WAEAA,UAAU,CAAC,CAAD,CAAV,KAAkB,KAAKjF,aAFvB,KAGCiF,UAAU,CAACmB,QAAD,CAAV,KAAyB;AAAI;AAA7B,WACGnB,UAAU,CAACmB,QAAD,CAAV,KAAyB,KAAKpG,aAJlC,CAAJ,EAIsD;AAClD;AACAiF,UAAAA,UAAU,GAAG;AAAI;AAAJ,YAAkBA,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAA/B,CAA/B;AACH,SAfW,CAgBZ;;;AACAc,QAAAA,UAAU,GAAG,KAAKa,kBAAL,CAAwBb,UAAU,CAACA,UAAU,CAACd,MAAX,GAAoB,CAArB,CAAlC,EAA2D,KAAKnE,aAAhE,EAA+E,KAAKD,iBAApF,IACPkF,UAAU,CAACX,KAAX,CAAiB,CAAjB,EAAoBW,UAAU,CAACd,MAAX,GAAoB,CAAxC,CADO,GAEPc,UAFN;AAGH,OAlG4D,CAmG7D;AACA;;;AACA,YAAMyC,4BAA4B,GAAG,KAAKhC,uBAAL,CAA6B,KAAK3F,iBAAlC,CAArC;;AACA,UAAI4H,YAAY,GAAG,2CAA2CtD,OAA3C,CAAmDqD,4BAAnD,EAAiF,EAAjF,CAAnB,CAtG6D,CAuG7D;;AACA,UAAIjE,KAAK,CAACC,OAAN,CAAc,KAAK1D,aAAnB,CAAJ,EAAuC;AACnC,aAAK,MAAMsF,MAAX,IAAqB,KAAKtF,aAA1B,EAAyC;AACrC2H,UAAAA,YAAY,GAAGA,YAAY,CAACtD,OAAb,CAAqB,KAAKqB,uBAAL,CAA6BJ,MAA7B,CAArB,EAA2D;AAAG;AAA9D,WAAf;AACH;AACJ,OAJD,MAKK;AACDqC,QAAAA,YAAY,GAAGA,YAAY,CAACtD,OAAb,CAAqB,KAAKqB,uBAAL,CAA6B,KAAK1F,aAAlC,CAArB,EAAuE,EAAvE,CAAf;AACH;;AACD,YAAM4H,iBAAiB,GAAG,IAAIpG,MAAJ,CAAW,MAAMmG,YAAN,GAAqB,GAAhC,CAA1B;;AACA,UAAI1C,UAAU,CAAChB,KAAX,CAAiB2D,iBAAjB,CAAJ,EAAyC;AACrC;AACA3C,QAAAA,UAAU,GAAGA,UAAU,CAACR,SAAX,CAAqB,CAArB,EAAwBQ,UAAU,CAACd,MAAX,GAAoB,CAA5C,CAAb;AACH,OApH4D,CAqH7D;;;AACAc,MAAAA,UAAU,GAAG,KAAKI,mBAAL,CAAyBJ,UAAzB,EAAqC3B,SAArC,EAAgD,KAAKtD,aAArD,CAAb;AACA,YAAM6H,SAAS,GAAG5C,UAAU,CAACZ,OAAX,CAAmB,IAAI7C,MAAJ,CAAWkG,4BAAX,EAAyC,GAAzC,CAAnB,EAAkE,EAAlE,CAAlB;AACAjB,MAAAA,MAAM,GAAG,KAAKvD,qBAAL,CAA2B2E,SAA3B,EAAsC,KAAK9H,iBAA3C,EAA8D,KAAKC,aAAnE,EAAkFsD,SAAlF,CAAT;AACA,YAAMwE,UAAU,GAAGrB,MAAM,CAAC3C,OAAP,CAAe;AAAI;AAAnB,UAAkCmB,UAAU,CAACnB,OAAX,CAAmB;AAAI;AAAvB,OAArD;AACA,YAAMiE,SAAS,GAAGtB,MAAM,CAACtC,MAAP,GAAgBc,UAAU,CAACd,MAA7C;;AACA,UAAI4D,SAAS,GAAG,CAAZ,IAAiBtB,MAAM,CAACL,QAAD,CAAN,KAAqB,KAAKrG,iBAA/C,EAAkE;AAC9D4G,QAAAA,cAAc,GAAG,IAAjB;AACA,YAAIhE,MAAM,GAAG,CAAb;;AACA,WAAG;AACC,eAAKA,MAAL,CAAYqF,GAAZ,CAAgB5B,QAAQ,GAAGzD,MAA3B;;AACAA,UAAAA,MAAM;AACT,SAHD,QAGSA,MAAM,GAAGoF,SAHlB;AAIH,OAPD,MAQK,IAAItB,MAAM,CAACL,QAAQ,GAAG,CAAZ,CAAN,KAAyB,KAAKpG,aAA9B,IACL+H,SAAS,KAAK,CAAC,CADV,IAELA,SAAS,KAAK,CAAC,CAFV,IAGLtB,MAAM,CAACL,QAAD,CAAN,KAAqB;AAAI;AAHxB,QAGqC;AACtC,aAAKzD,MAAL,CAAYsF,KAAZ;;AACA,aAAKtF,MAAL,CAAYqF,GAAZ,CAAgB5B,QAAQ,GAAG,CAA3B;AACH,OANI,MAOA,IAAK0B,UAAU,KAAK,CAAf,IACN1B,QAAQ,GAAG,CADL,IAEN,EAAEK,MAAM,CAAC3C,OAAP,CAAe;AAAI;AAAnB,WAAmCsC,QAAnC,IAA+CA,QAAQ,GAAG,CAA5D,CAFK,IAGJ,EAAEK,MAAM,CAAC3C,OAAP,CAAe;AAAI;AAAnB,WAAiCsC,QAAjC,IAA6CA,QAAQ,GAAG,CAA1D,KACG2B,SAAS,IAAI,CAJhB,EAIoB;AACrB,aAAKpF,MAAL,CAAYsF,KAAZ;;AACAtB,QAAAA,cAAc,GAAG,IAAjB;AACAC,QAAAA,KAAK,GAAGmB,SAAR,CAHqB,CAIrB;;AACA3B,QAAAA,QAAQ,IAAI2B,SAAZ;;AACA,aAAKpF,MAAL,CAAYqF,GAAZ,CAAgB5B,QAAhB;AACH,OAXI,MAYA;AACD,aAAKzD,MAAL,CAAYsF,KAAZ;AACH;AACJ,KAzJI,MA0JA;AACD,YACA;AACA,UAAI/C,CAAC,GAAG,CAAR,EAAWgD,WAAW,GAAGpB,UAAU,CAAC,CAAD,CAFnC,EAEwC5B,CAAC,GAAG4B,UAAU,CAAC3C,MAFvD,EAE+De,CAAC,IAAIgD,WAAW,GAAGpB,UAAU,CAAC5B,CAAD,CAAV,IAAiB;AAAG;AAFtG,QAE0H;AACtH,YAAIsB,MAAM,KAAK1D,cAAc,CAACqB,MAA9B,EAAsC;AAClC;AACH;;AACD,cAAMgE,mBAAmB,IAAG;AAAI;AAAJ,WAAyB,KAAK7G,QAAjC,CAAzB;;AACA,YAAI,KAAK8G,gBAAL,CAAsBF,WAAtB,EAAmCpF,cAAc,CAAC0D,MAAD,CAAd,IAA0B;AAAG;AAAhE,aACA1D,cAAc,CAAC0D,MAAM,GAAG,CAAV,CAAd,KAA+B;AAAI;AADvC,UAC8D;AAC1DC,UAAAA,MAAM,IAAIyB,WAAV;AACA1B,UAAAA,MAAM,IAAI,CAAV;AACH,SAJD,MAKK,IAAI1D,cAAc,CAAC0D,MAAM,GAAG,CAAV,CAAd,KAA+B;AAAI;AAAnC,WACLE,KADK,IAEL,KAAK0B,gBAAL,CAAsBF,WAAtB,EAAmCpF,cAAc,CAAC0D,MAAM,GAAG,CAAV,CAAd,IAA8B;AAAG;AAApE,SAFC,EAEwF;AACzFC,UAAAA,MAAM,IAAIyB,WAAV;AACA1B,UAAAA,MAAM,IAAI,CAAV;AACAE,UAAAA,KAAK,GAAG,KAAR;AACH,SANI,MAOA,IAAI,KAAK0B,gBAAL,CAAsBF,WAAtB,EAAmCpF,cAAc,CAAC0D,MAAD,CAAd,IAA0B;AAAG;AAAhE,aACL1D,cAAc,CAAC0D,MAAM,GAAG,CAAV,CAAd,KAA+B;AAAI;AAD9B,WAEL,CAAC2B,mBAFA,EAEqB;AACtB1B,UAAAA,MAAM,IAAIyB,WAAV;AACAxB,UAAAA,KAAK,GAAG,IAAR;AACH,SALI,MAMA,IAAI5D,cAAc,CAAC0D,MAAM,GAAG,CAAV,CAAd,KAA+B;AAAI;AAAnC,WACL,KAAK4B,gBAAL,CAAsBF,WAAtB,EAAmCpF,cAAc,CAAC0D,MAAM,GAAG,CAAV,CAAd,IAA8B;AAAG;AAApE,SADC,EACwF;AACzFC,UAAAA,MAAM,IAAIyB,WAAV;AACA1B,UAAAA,MAAM,IAAI,CAAV;AACH,SAJI,MAKA,IAAI,KAAK4B,gBAAL,CAAsBF,WAAtB,EAAmCpF,cAAc,CAAC0D,MAAD,CAAd,IAA0B;AAAG;AAAhE,SAAJ,EAAyF;AAC1F,cAAI1D,cAAc,CAAC0D,MAAD,CAAd,KAA2B;AAAI;AAAnC,YAAgD;AAC5C,gBAAI,KAAK1F,GAAL,GAAW8D,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAAjC,GAAqCtD,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAA/D,EAAkE;AAC9D;AACA9B,cAAAA,QAAQ,GAAG,CAAC,KAAKvF,gBAAN,GAAyBuF,QAAQ,GAAG,CAApC,GAAwCA,QAAnD;AACAI,cAAAA,MAAM,IAAI,CAAV;;AACA,mBAAK6B,UAAL,CAAgBvF,cAAhB,EAAgC0D,MAAhC,EAAwCM,UAAU,CAAC3C,MAAnD;;AACAe,cAAAA,CAAC;;AACD,kBAAI,KAAKrE,gBAAT,EAA2B;AACvB4F,gBAAAA,MAAM,IAAI,GAAV;AACH;;AACD;AACH;AACJ;;AACD,cAAI3D,cAAc,CAAC0D,MAAD,CAAd,KAA2B;AAAI;AAAnC,YAA+C;AAC3C,gBAAI,KAAK1F,GAAL,GACG2F,MAAM,CAACtC,MAAP,KAAkB,CAAlB,IAAuBS,MAAM,CAAC6B,MAAD,CAAN,GAAiB,CAAzC,IACGA,MAAM,KAAK,GAAX,IAAkB7B,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAD3C,IAEGjD,UAAU,CAACX,KAAX,CAAiBkC,MAAM,GAAG,CAA1B,EAA6BA,MAA7B,EAAqCrC,MAArC,KAAgD,CAAhD,IACGS,MAAM,CAACK,UAAU,CAACX,KAAX,CAAiBkC,MAAM,GAAG,CAA1B,EAA6BA,MAA7B,CAAD,CAAN,GAA+C,CAHrD,IAIGvB,UAAU,CAACX,KAAX,CAAiBkC,MAAM,GAAG,CAA1B,EAA6BA,MAA7B,MAAyC,GAAzC,IACG5B,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAN9B,GAOGzB,MAAM,KAAK,GAAX,IAAkB7B,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAAzC,IACG,CAACzB,MAAM,CAACnC,KAAP,CAAakC,MAAM,GAAG,CAAtB,EAAyBA,MAAzB,MAAqC,GAArC,IACEC,MAAM,CAACnC,KAAP,CAAakC,MAAM,GAAG,CAAtB,EAAyBA,MAAzB,MAAqC,GADvC,IAEEC,MAAM,CAACnC,KAAP,CAAakC,MAAM,GAAG,CAAtB,EAAyBA,MAAzB,MAAqC,GAFvC,IAGEC,MAAM,CAACnC,KAAP,CAAakC,MAAM,GAAG,CAAtB,EAAyBA,MAAzB,MAAqC,GAHxC,KAIG5B,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAJzB,IAKG1B,MAAM,GAAG,EAbrB,EAa0B;AACtB;AACAJ,cAAAA,QAAQ,GAAGA,QAAQ,GAAG,CAAtB;AACAI,cAAAA,MAAM,IAAI,CAAV;AACAtB,cAAAA,CAAC;AACD;AACH;AACJ;;AACD,cAAIpC,cAAc,CAAC0D,MAAD,CAAd,KAA2B;AAAI;AAA/B,aACA1D,cAAc,CAAC0D,MAAD,CAAd,KAA2B;AAAI;AADnC,YACiD;AAC7C,gBAAI5B,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAA1B,EAA6B;AACzB;AACA9B,cAAAA,QAAQ,GAAG,CAAC,KAAKvF,gBAAN,GAAyBuF,QAAQ,GAAG,CAApC,GAAwCA,QAAnD;AACAI,cAAAA,MAAM,IAAI,CAAV;;AACA,mBAAK6B,UAAL,CAAgBvF,cAAhB,EAAgC0D,MAAhC,EAAwCM,UAAU,CAAC3C,MAAnD;;AACAe,cAAAA,CAAC;;AACD,kBAAI,KAAKrE,gBAAT,EAA2B;AACvB4F,gBAAAA,MAAM,IAAI,GAAV;AACH;;AACD;AACH;AACJ;;AACD,gBAAM6B,SAAS,GAAG,EAAlB;AACA,gBAAMC,gBAAgB,GAAGtD,UAAU,CAACuB,MAAD,CAAnC;AACA,gBAAMgC,uBAAuB,GAAGvD,UAAU,CAACuB,MAAM,GAAG,CAAV,CAA1C;AACA,gBAAMiC,uBAAuB,GAAGxD,UAAU,CAACuB,MAAM,GAAG,CAAV,CAA1C;AACA,gBAAMkC,wBAAwB,GAAGzD,UAAU,CAACuB,MAAM,GAAG,CAAV,CAA3C;AACA,gBAAMmC,wBAAwB,GAAG1D,UAAU,CAACuB,MAAM,GAAG,CAAV,CAA3C;AACA,gBAAMoC,0BAA0B,GAAG3D,UAAU,CAACuB,MAAM,GAAG,CAAV,CAA7C;AACA,gBAAMqC,iCAAiC,GAAG5D,UAAU,CAACX,KAAX,CAAiBkC,MAAM,GAAG,CAA1B,EAA6BA,MAAM,GAAG,CAAtC,CAA1C;AACA,gBAAMsC,8BAA8B,GAAG7D,UAAU,CAACX,KAAX,CAAiBkC,MAAM,GAAG,CAA1B,EAA6BA,MAAM,GAAG,CAAtC,CAAvC;AACA,gBAAMuC,4BAA4B,GAAG9D,UAAU,CAACX,KAAX,CAAiBkC,MAAjB,EAAyBA,MAAM,GAAG,CAAlC,CAArC;AACA,gBAAMwC,6BAA6B,GAAG/D,UAAU,CAACX,KAAX,CAAiBkC,MAAM,GAAG,CAA1B,EAA6BA,MAA7B,CAAtC;;AACA,cAAI1D,cAAc,CAAC0D,MAAD,CAAd,KAA2B;AAAI;AAAnC,YAA8C;AAC1C,kBAAMyC,kBAAkB,GAAGnG,cAAc,CAACwB,KAAf,CAAqB,CAArB,EAAwB,CAAxB,MAA+B;AAAK;AAA/D;AACA,kBAAM4E,mBAAmB,GAAGpG,cAAc,CAACwB,KAAf,CAAqB,CAArB,EAAwB,CAAxB,MAA+B;AAAK;AAApC,eACxB,KAAK1D,iBAAL,CAAuBwE,QAAvB,CAAgCuD,wBAAhC,CADJ;;AAEA,gBAAK/D,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAAtB,IAA2B,KAAKrH,gBAAjC,IACC,CAACoI,kBAAD,KACIrE,MAAM,CAACmE,4BAAD,CAAN,GAAuCT,SAAvC,IACG1D,MAAM,CAACkE,8BAAD,CAAN,GAAyCR,SAD5C,IAEG,KAAK1H,iBAAL,CAAuBwE,QAAvB,CAAgCoD,uBAAhC,CAHP,CADD,KAKCU,mBAAmB,GACdtE,MAAM,CAACkE,8BAAD,CAAN,GAAyCR,SAAzC,IACG,CAAC,KAAK1H,iBAAL,CAAuBwE,QAAvB,CAAgCmD,gBAAhC,CAAD,IACG,KAAK3H,iBAAL,CAAuBwE,QAAvB,CAAgCqD,uBAAhC,CAFN,IAGE,KAAK7H,iBAAL,CAAuBwE,QAAvB,CAAgCmD,gBAAhC,CAJY,GAKd3D,MAAM,CAACmE,4BAAD,CAAN,GAAuCT,SAAvC,IACE,KAAK1H,iBAAL,CAAuBwE,QAAvB,CAAgCoD,uBAAhC,CAXR,CAAJ,EAWuE;AACnE;AACApC,cAAAA,QAAQ,GAAG,CAAC,KAAKvF,gBAAN,GAAyBuF,QAAQ,GAAG,CAApC,GAAwCA,QAAnD;AACAI,cAAAA,MAAM,IAAI,CAAV;;AACA,mBAAK6B,UAAL,CAAgBvF,cAAhB,EAAgC0D,MAAhC,EAAwCM,UAAU,CAAC3C,MAAnD;;AACAe,cAAAA,CAAC;;AACD,kBAAI,KAAKrE,gBAAT,EAA2B;AACvB4F,gBAAAA,MAAM,IAAI,GAAV;AACH;;AACD;AACH;AACJ;;AACD,cAAI3D,cAAc,CAAC0D,MAAD,CAAd,KAA2B;AAAI;AAAnC,YAAgD;AAC5C,kBAAM2C,WAAW,GAAG,EAApB,CAD4C,CAE5C;;AACA,kBAAMC,WAAW,GAAG5C,MAAM,KAAK,CAAX,KACf5B,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAAtB,IACGtD,MAAM,CAACmE,4BAAD,CAAN,GAAuCI,WAD1C,IAEG,KAAKvI,iBAAL,CAAuBwE,QAAvB,CAAgCoD,uBAAhC,CAHY,CAApB,CAH4C,CAO5C;;AACA,kBAAMa,YAAY,GAAGvG,cAAc,CAACwB,KAAf,CAAqBkC,MAAM,GAAG,CAA9B,EAAiCA,MAAM,GAAG,CAA1C,CAArB;AACA,kBAAM8C,cAAc,GAAGT,iCAAiC,CAACzD,QAAlC,CAA2CiE,YAA3C,MACjB,KAAKzI,iBAAL,CAAuBwE,QAAvB,CAAgCuD,wBAAhC,KACE/D,MAAM,CAACkE,8BAAD,CAAN,GAAyCK,WAD3C,IAEE,CAAC,KAAKvI,iBAAL,CAAuBwE,QAAvB,CAAgCmD,gBAAhC,CAFJ,IAGG,KAAK3H,iBAAL,CAAuBwE,QAAvB,CAAgCmD,gBAAhC,CAHH,IAII,KAAK3H,iBAAL,CAAuBwE,QAAvB,CAAgCwD,0BAAhC,KACGhE,MAAM,CAACoE,6BAAD,CAAN,GAAwCG,WAD3C,IAEG,CAAC,KAAKvI,iBAAL,CAAuBwE,QAAvB,CAAgCsD,wBAAhC,CANR,IAOG,KAAK9H,iBAAL,CAAuBwE,QAAvB,CAAgCsD,wBAAhC,CARe,CAAvB,CAT4C,CAkB5C;;AACA,kBAAMa,cAAc,GAAG3E,MAAM,CAACiE,iCAAD,CAAN,IAA6CP,SAA7C,IACnB,CAAC,KAAK1H,iBAAL,CAAuBwE,QAAvB,CAAgCyD,iCAAhC,CADkB,IAEnB,KAAKjI,iBAAL,CAAuBwE,QAAvB,CAAgCsD,wBAAhC,CAFmB,KAGlB9D,MAAM,CAACmE,4BAAD,CAAN,GAAuCI,WAAvC,IACG,KAAKvI,iBAAL,CAAuBwE,QAAvB,CAAgCoD,uBAAhC,CAJe,CAAvB,CAnB4C,CAwB5C;;AACA,kBAAMgB,iBAAiB,GAAI5E,MAAM,CAACmE,4BAAD,CAAN,GAAuCI,WAAvC,IAAsD3C,MAAM,KAAK,CAAlE,IACrB,KAAK5F,iBAAL,CAAuBwE,QAAvB,CAAgCoD,uBAAhC,KACGhC,MAAM,KAAK,CAFnB,CAzB4C,CA4B5C;;AACA,kBAAMiD,cAAc,GAAG7E,MAAM,CAACiE,iCAAD,CAAN,GAA4CP,SAA5C,IACnB,CAAC,KAAK1H,iBAAL,CAAuBwE,QAAvB,CAAgCyD,iCAAhC,CADkB,IAEnB,CAAC,KAAKjI,iBAAL,CAAuBwE,QAAvB,CAAgC4D,6BAAhC,CAFkB,IAGnBpE,MAAM,CAACoE,6BAAD,CAAN,GAAwCG,WAH5C,CA7B4C,CAiC5C;;AACA,kBAAMO,cAAc,GAAG9E,MAAM,CAACiE,iCAAD,CAAN,IAA6CP,SAA7C,IACnB,CAAC,KAAK1H,iBAAL,CAAuBwE,QAAvB,CAAgCyD,iCAAhC,CADkB,IAEnB,CAAC,KAAKjI,iBAAL,CAAuBwE,QAAvB,CAAgCsD,wBAAhC,CAFkB,IAGnB9D,MAAM,CAACkE,8BAAD,CAAN,GAAyCK,WAH7C;;AAIA,gBAAKvE,MAAM,CAACsD,WAAD,CAAN,GAAsB,CAAtB,IAA2B,KAAKrH,gBAAjC,IACAuI,WADA,IAEAE,cAFA,IAGAI,cAHA,IAIAD,cAJA,IAKAF,cALA,IAMCC,iBAAiB,IAAI,CAAC,KAAK3I,gBANhC,EAMmD;AAC/C;AACAuF,cAAAA,QAAQ,GAAG,CAAC,KAAKvF,gBAAN,GAAyBuF,QAAQ,GAAG,CAApC,GAAwCA,QAAnD;AACAI,cAAAA,MAAM,IAAI,CAAV;;AACA,mBAAK6B,UAAL,CAAgBvF,cAAhB,EAAgC0D,MAAhC,EAAwCM,UAAU,CAAC3C,MAAnD;;AACAe,cAAAA,CAAC;;AACD,kBAAI,KAAKrE,gBAAT,EAA2B;AACvB4F,gBAAAA,MAAM,IAAI,GAAV;AACH;;AACD;AACH;AACJ;;AACDA,UAAAA,MAAM,IAAIyB,WAAV;AACA1B,UAAAA,MAAM;AACT,SAlJI,MAmJA,IAAK0B,WAAW,KAAK;AAAI;AAApB,WACNpF,cAAc,CAAC0D,MAAD,CAAd,KAA2B;AAAI;AAD1B,WAEJ0B,WAAW,KAAK;AAAI;AAApB,WACGpF,cAAc,CAAC0D,MAAD,CAAd,KAA2B;AAAI;AAHlC,UAGgD;AACjDC,UAAAA,MAAM,IAAIyB,WAAV;AACA1B,UAAAA,MAAM;AACT,SANI,MAOA,IAAI,KAAK5F,iBAAL,CAAuBkD,OAAvB,CAA+BhB,cAAc,CAAC0D,MAAD,CAAd,IAA0B;AAAG;AAA5D,cAAoF,CAAC,CAAzF,EAA4F;AAC7FC,UAAAA,MAAM,IAAI3D,cAAc,CAAC0D,MAAD,CAAxB;AACAA,UAAAA,MAAM;;AACN,eAAK6B,UAAL,CAAgBvF,cAAhB,EAAgC0D,MAAhC,EAAwCM,UAAU,CAAC3C,MAAnD;;AACAe,UAAAA,CAAC;AACJ,SALI,MAMA,IAAIpC,cAAc,CAAC0D,MAAD,CAAd,KAA2B;AAAI;AAA/B,WACL,KAAKrG,aADJ,EACmB;AACpB,eAAKkI,UAAL,CAAgBvF,cAAhB,EAAgC0D,MAAhC,EAAwCM,UAAU,CAAC3C,MAAnD;AACH,SAHI,MAIA,IAAI,KAAK7C,QAAL,CAAcwB,cAAc,CAAC0D,MAAD,CAAd,IAA0B;AAAG;AAA3C,aACL,KAAKlF,QAAL,CAAcwB,cAAc,CAAC0D,MAAD,CAAd,IAA0B;AAAG;AAA3C,WAAgE/E,QAD/D,EACyE;AAC1E,cAAI,CAAC,CAACqF,UAAU,CAACN,MAAD,CAAZ,IACA1D,cAAc,KAAK,iBADnB,IAEAA,cAAc,KAAK,gBAFnB,IAGAA,cAAc,KAAK,oBAHnB,IAIA,CAACA,cAAc,CAACmB,KAAf,CAAqB,UAArB,CAJD,IAKA,CAAC,KAAK3C,QAAL,CAAcwB,cAAc,CAAC0D,MAAD,CAAd,IAA0B;AAAG;AAA3C,aACK/E,QANV,EAMoB;AAChBgF,YAAAA,MAAM,IAAIK,UAAU,CAACN,MAAD,CAApB;AACH;;AACD,cAAI1D,cAAc,CAACsC,QAAf,CAAwB;AAAI;AAAJ,YAAwB;AAAI;AAApD,eACAtC,cAAc,CAACsC,QAAf,CAAwB;AAAI;AAAJ,YAAwB;AAAI;AAApD,WADJ,EAC4E;AACxEoB,YAAAA,MAAM;AACT;;AACDA,UAAAA,MAAM;AACNtB,UAAAA,CAAC;AACJ,SAjBI,MAkBA,IAAI,KAAKpC,cAAL,CAAoB0D,MAAM,GAAG,CAA7B,MAAoC;AAAI;AAAxC,WACL,KAAKmD,gBAAL,CAAsB,KAAK7G,cAAL,CAAoB0D,MAAM,GAAG,CAA7B,KAAmC;AAAG;AAA5D,SADK,IAEL,KAAKmD,gBAAL,CAAsBzB,WAAtB,MAAuC,KAAKpF,cAAL,CAAoB0D,MAAM,GAAG,CAA7B,CAFlC,IAGLE,KAHC,EAGM;AACPF,UAAAA,MAAM,IAAI,CAAV;AACAC,UAAAA,MAAM,IAAIyB,WAAV;AACH,SANI,MAOA,IAAI,KAAKpF,cAAL,CAAoB0D,MAAM,GAAG,CAA7B,MAAoC;AAAI;AAAxC,WACL,KAAKmD,gBAAL,CAAsB,KAAK7G,cAAL,CAAoB0D,MAAM,GAAG,CAA7B,KAAmC;AAAG;AAA5D,SADK,IAEL,KAAKmD,gBAAL,CAAsBzB,WAAtB,MAAuC,KAAKpF,cAAL,CAAoB0D,MAAM,GAAG,CAA7B,CAFlC,IAGLE,KAHC,EAGM;AACPF,UAAAA,MAAM,IAAI,CAAV;AACAC,UAAAA,MAAM,IAAIyB,WAAV;AACH,SANI,MAOA,IAAI,KAAK/H,aAAL,IACL,KAAKS,iBAAL,CAAuBkD,OAAvB,CAA+BoE,WAA/B,IAA8C,CADzC,IAELA,WAAW,KAAK,KAAK9H,oBAFhB,IAGL,KAAKA,oBAAL,CAA0B+D,MAA1B,KAAqC,CAHpC,EAGuC;AACxC0C,UAAAA,QAAQ,GAAG,IAAX;AACH;AACJ;AACJ;;AACD,QAAIJ,MAAM,CAACtC,MAAP,GAAgB,CAAhB,KAAsBrB,cAAc,CAACqB,MAArC,IACA,KAAKvD,iBAAL,CAAuBkD,OAAvB,CAA+BhB,cAAc,CAACA,cAAc,CAACqB,MAAf,GAAwB,CAAzB,CAAd,IAA6C;AAAG;AAA/E,UAAuG,CAAC,CAD5G,EAC+G;AAC3GsC,MAAAA,MAAM,IAAI3D,cAAc,CAACA,cAAc,CAACqB,MAAf,GAAwB,CAAzB,CAAxB;AACH;;AACD,QAAIyF,WAAW,GAAGxD,QAAQ,GAAG,CAA7B;;AACA,WAAO,KAAKzD,MAAL,CAAYkH,GAAZ,CAAgBD,WAAhB,CAAP,EAAqC;AACjChD,MAAAA,KAAK;AACLgD,MAAAA,WAAW;AACd;;AACD,QAAIE,WAAW,GAAGzD,UAAU,IAAI,CAACvD,cAAc,CAACwE,UAAf,CAA0B;AAAY;AAAtC,KAAf,GACZd,MADY,GAEZ,KAAK7D,MAAL,CAAYkH,GAAZ,CAAgBzD,QAAhB,IACIQ,KADJ,GAEI,CAJV;;AAKA,QAAIC,QAAJ,EAAc;AACViD,MAAAA,WAAW;AACd;;AACDvD,IAAAA,EAAE,CAACuD,WAAD,EAAcnD,cAAd,CAAF;;AACA,QAAIC,KAAK,GAAG,CAAZ,EAAe;AACX,WAAKjE,MAAL,CAAYsF,KAAZ;AACH;;AACD,QAAI8B,WAAW,GAAG,KAAlB;;AACA,QAAIzD,UAAJ,EAAgB;AACZyD,MAAAA,WAAW,GAAGjD,UAAU,CAACkD,KAAX,CAAkBC,IAAD,IAAU,KAAKrJ,iBAAL,CAAuBwE,QAAvB,CAAgC6E,IAAhC,CAA3B,CAAd;AACH;;AACD,QAAI7F,GAAG,GAAI,GAAE,KAAKtE,MAAO,GAAEiK,WAAW,GAAG;AAAG;AAAN,MAA2BtD,MAAO,GAAE,KAAKtG,aAAL,GAAqB,EAArB,GAA0B,KAAKN,MAAO,EAAhH;;AACA,QAAI4G,MAAM,CAACtC,MAAP,KAAkB,CAAtB,EAAyB;AACrBC,MAAAA,GAAG,GAAG,CAAC,KAAK/D,qBAAN,GAA+B,GAAE,KAAKP,MAAO,GAAE2G,MAAO,EAAtD,GAA2D,GAAEA,MAAO,EAA1E;AACH;;AACD,QAAIA,MAAM,CAACrB,QAAP,CAAgB;AAAI;AAApB,SAAoC,KAAKtF,MAAzC,IAAmD,KAAKY,oBAA5D,EAAkF;AAC9E,UAAI4F,UAAU,IAAIG,MAAM,KAAK;AAAI;AAAjC,QAA8C;AAC1C,eAAO,EAAP;AACH;;AACDrC,MAAAA,GAAG,GAAI,GAAE;AAAI;AAAY,SAAE,KAAKtE,MAAO,GAAE2G,MAAM,CAC1CzC,KADoC,CAC9B;AAAI;AAD0B,QAEpCD,IAFoC,CAE/B;AAAG;AAF4B,OAER,GAAE,KAAKlE,MAAO,EAF/C;AAGH;;AACD,WAAOuE,GAAP;AACH;;AACD8F,EAAAA,oBAAoB,CAAChC,WAAD,EAAc;AAC9B,QAAIzE,KAAK,CAACC,OAAN,CAAc,KAAKrD,qBAAnB,CAAJ,EAA+C;AAC3C,aAAO,KAAKA,qBAAL,CAA2BkF,IAA3B,CAAiC4E,GAAD,IAASA,GAAG,KAAKjC,WAAjD,CAAP;AACH;;AACD,WAAO,KAAKyB,gBAAL,CAAsBzB,WAAtB,CAAP;AACH;;AACDyB,EAAAA,gBAAgB,CAACzB,WAAD,EAAc;AAC1B,WAAO,KAAKtH,iBAAL,CAAuB2E,IAAvB,CAA6B4E,GAAD,IAASA,GAAG,KAAKjC,WAA7C,CAAP;AACH;;AACDE,EAAAA,gBAAgB,CAACF,WAAD,EAAckC,UAAd,EAA0B;AACtC,SAAK9I,QAAL,GAAgB,KAAK4E,aAAL,GAAqB,KAAKA,aAA1B,GAA0C,KAAK5E,QAA/D;AACA,WAAQ,CAAC,KAAKA,QAAL,CAAc8I,UAAd,GAA2B7I,OAA3B,IACL,KAAKD,QAAL,CAAc8I,UAAd,GAA2B7I,OAA3B,CAAmCiD,IAAnC,CAAwC0D,WAAxC,CADI,KAEJ,KAFJ;AAGH;;AACDX,EAAAA,eAAe,CAACpE,GAAD,EAAM;AACjB,WAAOA,GAAG,CACLa,KADE,CACI;AAAG;AADP,MAEFqG,MAFE,CAEK,CAACnF,CAAD,EAAIoF,GAAJ,KAAY;AACpB,YAAMC,eAAe,GAAG,OAAO,KAAKvK,aAAZ,KAA8B,QAA9B,GAClBkF,CAAC,KAAK,KAAKlF,aADO,GAElB;AACE,WAAKA,aAAL,CAAmBoF,QAAnB,CAA4BF,CAA5B,CAHR;AAIA,aAAQA,CAAC,CAACjB,KAAF,CAAQ,QAAR,KACJiB,CAAC,KAAK,KAAKnF,iBADP,IAEJwK,eAFI,IAGHrF,CAAC,KAAK;AAAI;AAAV,SAAyBoF,GAAG,KAAK,CAAjC,IAAsC,KAAK5J,oBAHhD;AAIH,KAXM,EAYFqD,IAZE,CAYG;AAAG;AAZN,KAAP;AAaH;;AACD2B,EAAAA,uBAAuB,CAACuE,IAAD,EAAO;AAC1B;AACA;AACA;AACA,QAAIA,IAAJ,EAAU;AACN,YAAMO,aAAa,GAAG,cAAtB;AACA,aAAOP,IAAI,KAAK,GAAT,GAAe,KAAf,GAAuBO,aAAa,CAAC1G,OAAd,CAAsBmG,IAAtB,KAA+B,CAA/B,GAAoC,KAAIA,IAAK,EAA7C,GAAiDA,IAA/E;AACH;;AACD,WAAOA,IAAP;AACH;;AACD5B,EAAAA,UAAU,CAACvF,cAAD,EAAiB0D,MAAjB,EAAyBiE,WAAzB,EAAsC;AAC5C,UAAM1C,SAAS,GAAG,QAAQvD,IAAR,CAAa1B,cAAc,CAACwB,KAAf,CAAqB,CAArB,EAAwBkC,MAAxB,CAAb,IACZiE,WADY,GAEZjE,MAFN;;AAGA,SAAK7D,MAAL,CAAYqF,GAAZ,CAAgBD,SAAS,GAAG,KAAKjI,MAAL,CAAYqE,MAAxB,IAAkC,CAAlD;AACH;;AACD2B,EAAAA,kBAAkB,CAAC3E,KAAD,EAAQuJ,aAAR,EAAuBC,aAAvB,EAAsC;AACpD,WAAOlH,KAAK,CAACC,OAAN,CAAcgH,aAAd,IACDA,aAAa,CAACL,MAAd,CAAsBxG,CAAD,IAAOA,CAAC,KAAK8G,aAAlC,EAAiDvF,QAAjD,CAA0DjE,KAA1D,CADC,GAEDA,KAAK,KAAKuJ,aAFhB;AAGH;;AACDxD,EAAAA,QAAQ,CAACF,QAAD,EAAW;AACf,WAAO,EAAEA,QAAQ,CAAC7C,MAAT,KAAoB,CAApB,IACL,CAAC6C,QAAQ,CAAC4D,IAAT,CAAc,CAACzJ,KAAD,EAAQ0J,KAAR,KAAkB;AAC7B,UAAI7D,QAAQ,CAAC7C,MAAT,KAAoB0G,KAAK,GAAG,CAAhC,EAAmC;AAC/B,eAAO1J,KAAK,KAAK;AAAG;AAAb,WAAmCyD,MAAM,CAACzD,KAAD,CAAN,GAAgB,GAA1D;AACH;;AACD,aAAOA,KAAK,KAAK;AAAG;AAAb,SAAmCyD,MAAM,CAACzD,KAAK,CAACsD,SAAN,CAAgB,CAAhB,EAAmB,CAAnB,CAAD,CAAN,GAAgC,GAA1E;AACH,KALA,CADE,CAAP;AAOH;;AACDgD,EAAAA,iBAAiB,CAACtG,KAAD,EAAQ;AACrB,UAAM2J,YAAY,GAAG,OAAO,KAAK9K,aAAZ,KAA8B,QAA9B,GACfmB,KAAK,CAAC2C,OAAN,CAAc,KAAK9D,aAAnB,CADe,GAEfmB,KAAK,CAAC2C,OAAN,CAAc;AAAI;AAAlB,KAFN;;AAGA,QAAIgH,YAAY,KAAK,CAAC,CAAtB,EAAyB;AACrB,YAAMC,WAAW,GAAGC,QAAQ,CAAC7J,KAAD,EAAQ,EAAR,CAA5B;AACA,aAAO0D,KAAK,CAACkG,WAAD,CAAL,GAAqB;AAAG;AAAxB,QAA6CA,WAAW,CAAChE,QAAZ,EAApD;AACH,KAHD,MAIK;AACD,YAAMkE,WAAW,GAAGD,QAAQ,CAAC7J,KAAK,CAACsD,SAAN,CAAgB,CAAhB,EAAmBqG,YAAnB,CAAD,EAAmC,EAAnC,CAA5B;AACA,YAAMI,WAAW,GAAG/J,KAAK,CAACsD,SAAN,CAAgBqG,YAAY,GAAG,CAA/B,CAApB;AACA,YAAMK,aAAa,GAAGtG,KAAK,CAACoG,WAAD,CAAL,GAAqB,EAArB,GAA0BA,WAAW,CAAClE,QAAZ,EAAhD;AACA,YAAMqE,OAAO,GAAG,OAAO,KAAKpL,aAAZ,KAA8B,QAA9B,GAAyC,KAAKA,aAA9C,GAA8D;AAAI;AAAlF;AACA,aAAOmL,aAAa,KAAK;AAAG;AAArB,QACD;AAAG;AADF,QAEDA,aAAa,GAAGC,OAAhB,GAA0BF,WAFhC;AAGH;AACJ;;AAvsBuB;;AAysB5B1I,qBAAqB,CAAC6I,IAAtB;AAAA,mBAAmH7I,qBAAnH,EAAyG/D,EAAzG,UAA0JgB,eAA1J;AAAA;;AACA+C,qBAAqB,CAAC8I,KAAtB,kBADyG7M,EACzG;AAAA,SAAuH+D,qBAAvH;AAAA,WAAuHA,qBAAvH;AAAA;;AACA;AAAA,qDAFyG/D,EAEzG,mBAA4F+D,qBAA5F,EAA+H,CAAC;AACpH+I,IAAAA,IAAI,EAAE3M;AAD8G,GAAD,CAA/H,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE2M,MAAAA,IAAI,EAAEhL,SAAR;AAAmBiL,MAAAA,UAAU,EAAE,CAAC;AAC9DD,QAAAA,IAAI,EAAE1M,MADwD;AAE9D4M,QAAAA,IAAI,EAAE,CAAChM,eAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GALxB;AAAA;;AAOA,MAAMiM,cAAN,SAA6BlJ,qBAA7B,CAAmD;AAC/CC,EAAAA,WAAW,CAACkJ,QAAD,EAAWjJ,OAAX,EAAoBkJ,WAApB,EAAiCC,SAAjC,EAA4C;AACnD,UAAMnJ,OAAN;AACA,SAAKiJ,QAAL,GAAgBA,QAAhB;AACA,SAAKjJ,OAAL,GAAeA,OAAf;AACA,SAAKkJ,WAAL,GAAmBA,WAAnB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,aAAL,GAAqB,KAArB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,MAAL,GAAc,IAAd;AACA;AACR;AACA;AACA;;AACQ,SAAKC,YAAL,GAAoB,KAApB;AACA,SAAKC,WAAL,GAAmB,KAAnB;AACA,SAAKC,oBAAL,GAA4B,EAA5B;AACA,SAAKnL,mBAAL,GAA2B,KAA3B;AACA,SAAKoL,UAAL,GAAkB,KAAlB;AACA,SAAKC,cAAL,GAAsB,EAAtB;AACA,SAAKC,aAAL,GAAqB,EAArB,CApBmD,CAqBnD;;AACA,SAAKC,QAAL,GAAiBC,CAAD,IAAO,CAAG,CAA1B;AACH,GAxB8C,CAyB/C;;;AACAtG,EAAAA,SAAS,CAAClB,UAAD,EAAanC,cAAb,EAA6BsD,QAAQ,GAAG,CAAxC,EAA2CC,UAAU,GAAG,KAAxD,EAA+DC,UAAU,GAAG,KAA5E,EACT;AACAC,EAAAA,EAAE,GAAG,MAAM,CAAG,CAFL,EAEO;AACZ,QAAI,CAACzD,cAAL,EAAqB;AACjB,aAAOmC,UAAU,KAAK,KAAKlC,WAApB,GAAkC,KAAKA,WAAvC,GAAqDkC,UAA5D;AACH;;AACD,SAAK8G,WAAL,GAAmB,KAAK5L,aAAL,GACb,KAAKuM,eAAL,EADa,GAEb;AAAG;AAFT;;AAGA,QAAI,KAAK5J,cAAL,KAAwB;AAAK;AAA7B,OAAyC,KAAK3C,aAAlD,EAAiE;AAC7D,WAAK4L,WAAL,GAAmB,KAAKW,eAAL,CAAqBzH,UAAU,IAAI;AAAI;AAAvC,OAAnB;AACH;;AACD,QAAI,KAAKnC,cAAL,KAAwB;AAAW;AAAnC,OAAqD,KAAK3C,aAA9D,EAA6E;AACzE,WAAK4L,WAAL,GAAmB,KAAKW,eAAL,CAAqBzH,UAAU,IAAI;AAAI;AAAvC,OAAnB;AACH;;AACD,QAAI,CAACA,UAAD,IAAe,KAAK9E,aAAxB,EAAuC;AACnC,WAAKwM,iBAAL,CAAuB,KAAK7M,MAA5B;AACA,aAAO,KAAKA,MAAL,GAAc,KAAKiM,WAAnB,GAAiC,KAAKlM,MAA7C;AACH;;AACD,UAAM+M,SAAS,GAAG,CAAC,CAAC3H,UAAF,IAAgB,OAAO,KAAK+G,QAAZ,KAAyB,QAAzC,GACZ/G,UAAU,CAAC,KAAK+G,QAAN,CAAV,IAA6B;AAAG;AADpB,MAEZ;AAAG;AAFT;AAGA,QAAIa,aAAa,GAAG,EAApB;;AACA,QAAI,KAAKvM,WAAL,KAAqBC,SAArB,IAAkC,CAAC,KAAK2L,YAA5C,EAA0D;AACtD,UAAIY,YAAY,GAAG7H,UAAU,IAAIA,UAAU,CAACd,MAAX,KAAsB,CAApC,GACbc,UAAU,CAACjB,KAAX,CAAiB;AAAG;AAApB,OADa,GAEb,KAAKjB,WAAL,CAAiBiB,KAAjB,CAAuB;AAAG;AAA1B,OAFN,CADsD,CAItD;AACA;;AACA,UAAI,OAAO,KAAKgI,QAAZ,KAAyB,QAAzB,IAAqC,OAAO,KAAKC,MAAZ,KAAuB,QAAhE,EAA0E;AACtE,aAAKD,QAAL,GAAgBpH,MAAM,CAAC,KAAKoH,QAAN,CAAtB;AACA,aAAKC,MAAL,GAAcrH,MAAM,CAAC,KAAKqH,MAAN,CAApB;AACH,OAHD,MAIK;AACDhH,QAAAA,UAAU,KAAK;AAAG;AAAlB,WAAwC6H,YAAY,CAAC3I,MAArD,GACM,OAAO,KAAK6H,QAAZ,KAAyB,QAAzB,IAAqC,OAAO,KAAKC,MAAZ,KAAuB,QAA5D,GACIhH,UAAU,CAACd,MAAX,GAAoB2I,YAAY,CAAC3I,MAAjC,GACI2I,YAAY,CAACC,MAAb,CAAoB,KAAKf,QAAzB,EAAmC,CAAnC,EAAsCY,SAAtC,CADJ,GAEI3H,UAAU,CAACd,MAAX,GAAoB2I,YAAY,CAAC3I,MAAjC,GACI2I,YAAY,CAAC3I,MAAb,GAAsBc,UAAU,CAACd,MAAjC,KAA4C,CAA5C,GACImC,UAAU,GACNwG,YAAY,CAACC,MAAb,CAAoB,KAAKf,QAAL,GAAgB,CAApC,EAAuC,CAAvC,CADM,GAENc,YAAY,CAACC,MAAb,CAAoB9H,UAAU,CAACd,MAAX,GAAoB,CAAxC,EAA2C,CAA3C,CAHR,GAII2I,YAAY,CAACC,MAAb,CAAoB,KAAKf,QAAzB,EAAmC,KAAKC,MAAL,GAAc,KAAKD,QAAtD,CALR,GAMI,IATZ,GAUI,IAXV,GAYOc,YAAY,GAAG,EAZtB;AAaH;;AACD,UAAI,KAAK3M,aAAT,EAAwB;AACpB,YAAI,CAAC,KAAKG,WAAV,EAAuB;AACnB;AACA2E,UAAAA,UAAU,GAAG,KAAK+H,UAAL,CAAgB/H,UAAhB,CAAb;AACH;AACJ,OA9BqD,CA+BtD;;;AACA4H,MAAAA,aAAa,GACT,KAAK9J,WAAL,CAAiBoB,MAAjB,IAA2B2I,YAAY,CAAC3I,MAAb,IAAuBc,UAAU,CAACd,MAA7D,GACM,KAAK8I,iBAAL,CAAuBH,YAAY,CAAC/I,IAAb,CAAkB;AAAG;AAArB,OAAvB,CADN,GAEMkB,UAHV;AAIH;;AACD,QAAIoB,UAAU,KAAK,KAAK/F,WAAL,IAAoB,CAAC,KAAKA,WAA/B,CAAd,EAA2D;AACvDuM,MAAAA,aAAa,GAAG5H,UAAhB;AACH;;AACD,QAAIqB,UAAU,IACV,KAAK1F,iBAAL,CAAuBkD,OAAvB,CAA+B,KAAKhB,cAAL,CAAoBsD,QAApB,KAAiC;AAAG;AAAnE,UAA2F,CAAC,CAD5F,IAEA,KAAKjG,aAFT,EAEwB;AACpB0M,MAAAA,aAAa,GAAG,KAAKN,aAArB;AACH;;AACD,QAAI,KAAKtJ,uBAAL,IAAgCmD,QAApC,EAA8C;AAC1C,UAAI,KAAKxF,iBAAL,CAAuBwE,QAAvB,CAAgC,KAAKrC,WAAL,CAAiBuB,KAAjB,CAAuB8B,QAAvB,EAAiCA,QAAQ,GAAG,CAA5C,CAAhC,CAAJ,EAAqF;AACjF;AACAA,QAAAA,QAAQ,GAAGA,QAAQ,GAAG,CAAtB;AACH,OAHD,MAIK,IAAItD,cAAc,CAACwB,KAAf,CAAqB8B,QAAQ,GAAG,CAAhC,EAAmCA,QAAQ,GAAG,CAA9C,MAAqD;AAAK;AAA9D,QAA4E;AAC7E;AACAA,QAAAA,QAAQ,GAAGA,QAAQ,GAAG,CAAtB;AACH,OARyC,CAS1C;;;AACA,WAAKnD,uBAAL,GAA+B,KAA/B;AACH;;AACD,QAAI,KAAK9C,aAAL,IACA,KAAKC,oBAAL,CAA0B+D,MAA1B,KAAqC,CADrC,IAEA,CAAC,KAAKtD,gBAFV,EAE4B;AACxB;AACAoE,MAAAA,UAAU,GAAG,KAAK+H,UAAL,CAAgB/H,UAAhB,CAAb;AACH;;AACD,QAAI,KAAKkH,WAAT,EAAsB;AAClBU,MAAAA,aAAa,GAAG5H,UAAhB;AACH,KAFD,MAGK;AACD4H,MAAAA,aAAa,GACTK,OAAO,CAACL,aAAD,CAAP,IAA0BA,aAAa,CAAC1I,MAAxC,GAAiD0I,aAAjD,GAAiE5H,UADrE;AAEH;;AACD,QAAI,KAAK9E,aAAL,IAAsB,KAAKa,sBAA3B,IAAqD,KAAK+B,WAA1D,IAAyE,CAACsD,UAA9E,EAA0F;AACtF,YAAMlF,KAAK,GAAG,KAAKd,qBAAL,GACR,KAAK2M,UAAL,CAAgB,KAAKjK,WAArB,CADQ,GAER,KAAKA,WAFX;AAGA,WAAK4J,iBAAL,CAAuBxL,KAAvB;AACA,aAAO,KAAK4B,WAAL,GACD,KAAKA,WADJ,GAED,KAAKjD,MAAL,GAAc,KAAKiM,WAAnB,GAAiC,KAAKlM,MAF5C;AAGH;;AACD,UAAM4G,MAAM,GAAG,MAAMN,SAAN,CAAgB0G,aAAhB,EAA+B/J,cAA/B,EAA+CsD,QAA/C,EAAyDC,UAAzD,EAAqEC,UAArE,EAAiFC,EAAjF,CAAf;AACA,SAAKxD,WAAL,GAAmB,KAAKoK,cAAL,CAAoB1G,MAApB,CAAnB,CArGY,CAsGZ;AACA;;AACA,QAAI,KAAK1G,iBAAL,KAA2B;AAAI;AAA/B,OACA,KAAKC,aAAL,KAAuB;AAAI;AAD/B,MAC0C;AACtC,WAAKA,aAAL,GAAqB;AAAI;AAAzB;AACH,KA3GW,CA4GZ;;;AACA,QAAI,KAAK8C,cAAL,CAAoBwE,UAApB,CAA+B;AAAY;AAA3C,SACA,KAAKjH,qBAAL,KAA+B,IADnC,EACyC;AACrC,WAAKO,iBAAL,GAAyB,KAAKA,iBAAL,CAAuByJ,MAAvB,CAA+B+C,IAAD,IAAU,CAAC,KAAKtH,kBAAL,CAAwBsH,IAAxB,EAA8B,KAAKpN,aAAnC,EAAkD,KAAKD,iBAAvD,CAAzC,CAAmH;AAAnH,OAAzB;AAEH;;AACD,QAAI0G,MAAM,IAAIA,MAAM,KAAK,EAAzB,EAA6B;AACzB,WAAK6F,cAAL,GAAsB,KAAKC,aAA3B;AACA,WAAKA,aAAL,GAAqB9F,MAArB;AACA,WAAK4F,UAAL,GACI,KAAKC,cAAL,KAAwB,KAAKC,aAA7B,IACI,KAAKJ,WADT,IAEK,KAAKG,cAAL,KAAwB,KAAKC,aAA7B,IAA8ClG,UAHvD;AAIH;;AACD,SAAKgG,UAAL,GAAkB,KAAKM,iBAAL,CAAuBlG,MAAvB,CAAlB,GAAmD,EAAnD;;AACA,QAAI,CAAC,KAAKtG,aAAN,IAAwB,KAAKA,aAAL,IAAsB,KAAKG,WAAvD,EAAqE;AACjE,UAAI,KAAKA,WAAT,EAAsB;AAClB,YAAIgG,UAAJ,EAAgB;AACZ,iBAAO,KAAK+G,SAAL,CAAe5G,MAAf,EAAuB,KAAK3D,cAA5B,CAAP;AACH;;AACD,eAAQ,KAAKuK,SAAL,CAAe5G,MAAf,EAAuB,KAAK3D,cAA5B,IACJ,KAAKiJ,WAAL,CAAiBzH,KAAjB,CAAuBmC,MAAM,CAACtC,MAA9B,CADJ;AAEH;;AACD,aAAOsC,MAAP;AACH;;AACD,UAAM6G,MAAM,GAAG7G,MAAM,CAACtC,MAAtB;AACA,UAAMoJ,SAAS,GAAG,KAAKzN,MAAL,GAAc,KAAKiM,WAAnB,GAAiC,KAAKlM,MAAxD;;AACA,QAAI,KAAKiD,cAAL,CAAoBsC,QAApB,CAA6B;AAAI;AAAjC,KAAJ,EAAmD;AAC/C,YAAMoI,iBAAiB,GAAG,KAAKC,oBAAL,CAA0BhH,MAA1B,CAA1B;;AACA,aAAOA,MAAM,GAAG8G,SAAS,CAACjJ,KAAV,CAAgBgJ,MAAM,GAAGE,iBAAzB,CAAhB;AACH,KAHD,MAIK,IAAI,KAAK1K,cAAL,KAAwB;AAAK;AAA7B,OACL,KAAKA,cAAL,KAAwB;AAAW;AADlC,MACkD;AACnD,aAAO2D,MAAM,GAAG8G,SAAhB;AACH;;AACD,WAAO9G,MAAM,GAAG8G,SAAS,CAACjJ,KAAV,CAAgBgJ,MAAhB,CAAhB;AACH,GA5K8C,CA6K/C;;;AACAG,EAAAA,oBAAoB,CAACtM,KAAD,EAAQ;AACxB,UAAMuM,KAAK,GAAG,eAAd;AACA,QAAIzJ,KAAK,GAAGyJ,KAAK,CAACC,IAAN,CAAWxM,KAAX,CAAZ;AACA,QAAIqM,iBAAiB,GAAG,CAAxB;;AACA,WAAOvJ,KAAK,IAAI,IAAhB,EAAsB;AAClBuJ,MAAAA,iBAAiB,IAAI,CAArB;AACAvJ,MAAAA,KAAK,GAAGyJ,KAAK,CAACC,IAAN,CAAWxM,KAAX,CAAR;AACH;;AACD,WAAOqM,iBAAP;AACH;;AACDI,EAAAA,iBAAiB,CAACxH,QAAD,EAAWC,UAAX,EAAuBC,UAAvB,EACjB;AACAC,EAAAA,EAAE,GAAG,MAAM,CAAG,CAFG,EAED;AACZ,UAAMsH,WAAW,GAAG,KAAKjC,WAAL,EAAkBkC,aAAtC;;AACA,QAAI,CAACD,WAAL,EAAkB;AACd;AACH;;AACDA,IAAAA,WAAW,CAAC1M,KAAZ,GAAoB,KAAKgF,SAAL,CAAe0H,WAAW,CAAC1M,KAA3B,EAAkC,KAAK2B,cAAvC,EAAuDsD,QAAvD,EAAiEC,UAAjE,EAA6EC,UAA7E,EAAyFC,EAAzF,CAApB;;AACA,QAAIsH,WAAW,KAAK,KAAKE,iBAAL,EAApB,EAA8C;AAC1C;AACH;;AACD,SAAKC,iBAAL;AACH;;AACDX,EAAAA,SAAS,CAACpI,UAAD,EAAanC,cAAb,EAA6B;AAClC,WAAOmC,UAAU,CACZjB,KADE,CACI;AAAG;AADP,MAEFJ,GAFE,CAEE,CAACqK,IAAD,EAAOpD,KAAP,KAAiB;AACtB,UAAI,KAAKvJ,QAAL,IACA,KAAKA,QAAL,CAAcwB,cAAc,CAAC+H,KAAD,CAAd,IAAyB;AAAG;AAA1C,OADA,IAEA,KAAKvJ,QAAL,CAAcwB,cAAc,CAAC+H,KAAD,CAAd,IAAyB;AAAG;AAA1C,SAA+DlJ,MAFnE,EAE2E;AACvE,eAAO,KAAKL,QAAL,CAAcwB,cAAc,CAAC+H,KAAD,CAAd,IAAyB;AAAG;AAA1C,WACDlJ,MADN;AAEH;;AACD,aAAOsM,IAAP;AACH,KAVM,EAWFlK,IAXE,CAWG;AAAG;AAXN,KAAP;AAYH,GAlN8C,CAmN/C;;;AACAoJ,EAAAA,cAAc,CAAC/I,GAAD,EAAM;AAChB,UAAM8J,OAAO,GAAG9J,GAAG,CACdJ,KADW,CACL;AAAG;AADE,MAEXqG,MAFW,CAEJ,CAAC1I,MAAD,EAASuD,CAAT,KAAe;AACvB,YAAMiJ,QAAQ,GAAG,KAAKrL,cAAL,CAAoBoC,CAApB,KAA0B;AAAG;AAA9C;AACA,aAAQ,KAAKkD,gBAAL,CAAsBzG,MAAtB,EAA8BwM,QAA9B,KACH,KAAKvN,iBAAL,CAAuBwE,QAAvB,CAAgC+I,QAAhC,KAA6CxM,MAAM,KAAKwM,QAD7D;AAEH,KANe,CAAhB;;AAOA,QAAID,OAAO,CAACnK,IAAR,CAAa;AAAG;AAAhB,UAAwCK,GAA5C,EAAiD;AAC7C,aAAO8J,OAAO,CAACnK,IAAR,CAAa;AAAG;AAAhB,OAAP;AACH;;AACD,WAAOK,GAAP;AACH;;AACD6I,EAAAA,iBAAiB,CAAChI,UAAD,EAAa;AAC1B,QAAImJ,eAAe,GAAG,EAAtB;AACA,UAAMvB,aAAa,GAAI5H,UAAU,IAC7BA,UAAU,CACLjB,KADL,CACW;AAAG;AADd,MAEKJ,GAFL,CAES,CAACyK,UAAD,EAAaxD,KAAb,KAAuB;AAC5B,UAAI,KAAKjK,iBAAL,CAAuBwE,QAAvB,CAAgCH,UAAU,CAAC4F,KAAK,GAAG,CAAT,CAAV,IAAyB;AAAG;AAA5D,WACA5F,UAAU,CAAC4F,KAAK,GAAG,CAAT,CAAV,KAA0B,KAAK/H,cAAL,CAAoB+H,KAAK,GAAG,CAA5B,CAD9B,EAC8D;AAC1DuD,QAAAA,eAAe,GAAGC,UAAlB;AACA,eAAOpJ,UAAU,CAAC4F,KAAK,GAAG,CAAT,CAAjB;AACH;;AACD,UAAIuD,eAAe,CAACjK,MAApB,EAA4B;AACxB,cAAMmK,aAAa,GAAGF,eAAtB;AACAA,QAAAA,eAAe,GAAG;AAAG;AAArB;AACA,eAAOE,aAAP;AACH;;AACD,aAAOD,UAAP;AACH,KAdD,CADkB,IAgBlB,EAhBJ;AAiBA,WAAOxB,aAAa,CAAC9I,IAAd,CAAmB;AAAG;AAAtB,KAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIwK,EAAAA,cAAc,CAACpN,KAAD,EAAQ;AAClB,QAAK,CAACA,KAAD,IAAUA,KAAK,KAAK,CAArB,IACC,KAAK2B,cAAL,CAAoBwE,UAApB,CAA+B;AAAY;AAA3C,UACI,KAAKvG,QAAL,IAAiB,CAAC,KAAKV,qBAD3B,CADD,IAGC,KAAKyC,cAAL,CAAoBwE,UAApB,CAA+B;AAAY;AAA3C,SACG,KAAK7G,cAAL,CAAoB0D,MAApB,GAA6B,EADhC,IAEGqK,MAAM,CAACrN,KAAD,CAAN,CAAcgD,MAAd,GAAuB,EAL/B,EAKoC;AAChC,aAAOqK,MAAM,CAACrN,KAAD,CAAb;AACH;;AACD,WAAOyD,MAAM,CAACzD,KAAD,CAAN,CACFsN,cADE,CACa,UADb,EACyB;AAC5BC,MAAAA,WAAW,EAAE,KADe;AAE5BC,MAAAA,qBAAqB,EAAE;AAFK,KADzB,EAKFtK,OALE,CAKO,IAAG;AAAI;AAAY,OAL1B,EAK8B;AAAI;AALlC,KAAP;AAMH;;AACDqI,EAAAA,eAAe,CAACkC,QAAD,EAAW;AACtB,QAAI,KAAKzO,aAAL,IAAsB,CAAC,CAAC,KAAKK,mBAAjC,EAAsD;AAClD,UAAI,KAAKsC,cAAL,CAAoBqB,MAApB,KAA+B,KAAK3D,mBAAL,CAAyB2D,MAA5D,EAAoE;AAChE,cAAM,IAAI0K,KAAJ,CAAU,oDAAV,CAAN;AACH,OAFD,MAGK;AACD,eAAO,KAAKrO,mBAAZ;AACH;AACJ,KAPD,MAQK,IAAI,KAAKL,aAAT,EAAwB;AACzB,UAAIyO,QAAJ,EAAc;AACV,YAAI,KAAK9L,cAAL,KAAwB;AAAK;AAAjC,UAA2C;AACvC,iBAAO,KAAKgM,WAAL,CAAiBF,QAAjB,CAAP;AACH;;AACD,YAAI,KAAK9L,cAAL,KAAwB;AAAW;AAAvC,UAAuD;AACnD,iBAAO,KAAKiM,gBAAL,CAAsBH,QAAtB,CAAP;AACH;AACJ;;AACD,UAAI,KAAKxO,oBAAL,CAA0B+D,MAA1B,KAAqC,KAAKrB,cAAL,CAAoBqB,MAA7D,EAAqE;AACjE,eAAO,KAAK/D,oBAAZ;AACH;;AACD,aAAO,KAAK0C,cAAL,CAAoBuB,OAApB,CAA4B,KAA5B,EAAmC,KAAKjE,oBAAxC,CAAP;AACH;;AACD,WAAO,EAAP;AACH;;AACD4N,EAAAA,iBAAiB,GAAG;AAChB,UAAMH,WAAW,GAAG,KAAKjC,WAAL,EAAkBkC,aAAtC;;AACA,QAAI,CAACD,WAAL,EAAkB;AACd;AACH;;AACD,QAAI,KAAK5N,eAAL,IACA,KAAKH,MAAL,CAAYqE,MAAZ,GAAqB,KAAKrB,cAAL,CAAoBqB,MAAzC,GAAkD,KAAKtE,MAAL,CAAYsE,MAA9D,KACI0J,WAAW,CAAC1M,KAAZ,CAAkBkD,OAAlB,CAA0B,KAAKjE,oBAA/B,EAAqD;AAAG;AAAxD,MACK+D,MAHb,EAGqB;AACjB,WAAK6K,mBAAL,GAA2B,CAAC,OAAD,EAAU;AAAG;AAAb,OAA3B;AACA,WAAK7I,SAAL,CAAe,EAAf,EAAmB,KAAKrD,cAAxB;AACH;AACJ;;AACsB,MAAnBkM,mBAAmB,CAAC,CAACC,IAAD,EAAO9N,KAAP,CAAD,EAAgB;AACnC,QAAI,CAAC,KAAK0K,SAAN,IAAmB,CAAC,KAAKD,WAA7B,EAA0C;AACtC;AACH;;AACDsD,IAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM,KAAKvD,SAAL,EAAgBwD,WAAhB,CAA4B,KAAKzD,WAAL,EAAkBkC,aAA9C,EAA6DmB,IAA7D,EAAmE9N,KAAnE,CAA7B;AACH;;AACDmO,EAAAA,0BAA0B,CAACrJ,IAAD,EAAO;AAC7B,UAAMsJ,KAAK,GAAGtJ,IAAI,CACbjC,KADS,CACH;AAAG;AADA,MAETqG,MAFS,CAED+C,IAAD,IAAU,KAAKlD,oBAAL,CAA0BkD,IAA1B,CAFR,CAAd;AAGA,WAAOmC,KAAK,CAACpL,MAAb;AACH;;AACD6I,EAAAA,UAAU,CAAC/H,UAAD,EAAa;AACnB,WAAO,KAAKuK,WAAL,CAAiB,KAAKC,aAAL,CAAmB,KAAKC,aAAL,CAAmBzK,UAAnB,CAAnB,CAAjB,EAAqE,KAAKrE,iBAAL,CAAuB+O,MAAvB,CAA8B,GAA9B,EAAmCA,MAAnC,CAA0C,KAAKvP,oBAA/C,CAArE,CAAP;AACH;;AACD0O,EAAAA,WAAW,CAACF,QAAD,EAAW;AAClB,QAAIA,QAAQ,KAAK;AAAI;AAArB,MAAiC;AAC7B,aAAQ,GAAE,KAAKxO,oBAAqB,IAAG,KAAKA,oBAAqB,IAAG,KAAKA,oBAAqB,IAAG,KAAKA,oBAAqB,EAA3H;AACH;;AACD,UAAM+G,GAAG,GAAG,EAAZ;;AACA,SAAK,IAAIjC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0J,QAAQ,CAACzK,MAA7B,EAAqCe,CAAC,EAAtC,EAA0C;AACtC,YAAM/D,KAAK,GAAGyN,QAAQ,CAAC1J,CAAD,CAAR,IAAe;AAAG;AAAhC;;AACA,UAAI,CAAC/D,KAAL,EAAY;AACR;AACH;;AACD,UAAIA,KAAK,CAAC8C,KAAN,CAAY,KAAZ,CAAJ,EAAwB;AACpBkD,QAAAA,GAAG,CAACC,IAAJ,CAASjG,KAAT;AACH;AACJ;;AACD,QAAIgG,GAAG,CAAChD,MAAJ,IAAc,CAAlB,EAAqB;AACjB,aAAQ,GAAE,KAAK/D,oBAAqB,IAAG,KAAKA,oBAAqB,IAAG,KAAKA,oBAAqB,EAA9F;AACH;;AACD,QAAI+G,GAAG,CAAChD,MAAJ,GAAa,CAAb,IAAkBgD,GAAG,CAAChD,MAAJ,IAAc,CAApC,EAAuC;AACnC,aAAQ,GAAE,KAAK/D,oBAAqB,IAAG,KAAKA,oBAAqB,EAAjE;AACH;;AACD,QAAI+G,GAAG,CAAChD,MAAJ,GAAa,CAAb,IAAkBgD,GAAG,CAAChD,MAAJ,IAAc,CAApC,EAAuC;AACnC,aAAO,KAAK/D,oBAAZ;AACH;;AACD,QAAI+G,GAAG,CAAChD,MAAJ,GAAa,CAAb,IAAkBgD,GAAG,CAAChD,MAAJ,IAAc,EAApC,EAAwC;AACpC,aAAO,EAAP;AACH;;AACD,WAAO,EAAP;AACH;;AACD4K,EAAAA,gBAAgB,CAACH,QAAD,EAAW;AACvB,UAAMgB,GAAG,GAAI,GAAE,KAAKxP,oBAAqB,GAAE,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,EAArF,GACP,IAAG,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,EAD9E,GAEP,IAAG,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,EAF9E,GAGP,IAAG,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,EAH9D;AAIA,UAAMyP,IAAI,GAAI,GAAE,KAAKzP,oBAAqB,GAAE,KAAKA,oBAAqB,EAAzD,GACR,IAAG,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,EAD7E,GAER,IAAG,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,EAF7E,GAGR,IAAG,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,EAHzG,GAIR,IAAG,KAAKA,oBAAqB,GAAE,KAAKA,oBAAqB,EAJ9D;;AAKA,QAAIwO,QAAQ,KAAK;AAAI;AAArB,MAAiC;AAC7B,aAAOgB,GAAP;AACH;;AACD,UAAMzI,GAAG,GAAG,EAAZ;;AACA,SAAK,IAAIjC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0J,QAAQ,CAACzK,MAA7B,EAAqCe,CAAC,EAAtC,EAA0C;AACtC,YAAM/D,KAAK,GAAGyN,QAAQ,CAAC1J,CAAD,CAAR,IAAe;AAAG;AAAhC;;AACA,UAAI,CAAC/D,KAAL,EAAY;AACR;AACH;;AACD,UAAIA,KAAK,CAAC8C,KAAN,CAAY,KAAZ,CAAJ,EAAwB;AACpBkD,QAAAA,GAAG,CAACC,IAAJ,CAASjG,KAAT;AACH;AACJ;;AACD,QAAIgG,GAAG,CAAChD,MAAJ,IAAc,CAAlB,EAAqB;AACjB,aAAOyL,GAAG,CAACtL,KAAJ,CAAU6C,GAAG,CAAChD,MAAd,EAAsByL,GAAG,CAACzL,MAA1B,CAAP;AACH;;AACD,QAAIgD,GAAG,CAAChD,MAAJ,GAAa,CAAb,IAAkBgD,GAAG,CAAChD,MAAJ,IAAc,CAApC,EAAuC;AACnC,aAAOyL,GAAG,CAACtL,KAAJ,CAAU6C,GAAG,CAAChD,MAAJ,GAAa,CAAvB,EAA0ByL,GAAG,CAACzL,MAA9B,CAAP;AACH;;AACD,QAAIgD,GAAG,CAAChD,MAAJ,GAAa,CAAb,IAAkBgD,GAAG,CAAChD,MAAJ,IAAc,CAApC,EAAuC;AACnC,aAAOyL,GAAG,CAACtL,KAAJ,CAAU6C,GAAG,CAAChD,MAAJ,GAAa,CAAvB,EAA0ByL,GAAG,CAACzL,MAA9B,CAAP;AACH;;AACD,QAAIgD,GAAG,CAAChD,MAAJ,GAAa,CAAb,IAAkBgD,GAAG,CAAChD,MAAJ,GAAa,EAAnC,EAAuC;AACnC,aAAOyL,GAAG,CAACtL,KAAJ,CAAU6C,GAAG,CAAChD,MAAJ,GAAa,CAAvB,EAA0ByL,GAAG,CAACzL,MAA9B,CAAP;AACH;;AACD,QAAIgD,GAAG,CAAChD,MAAJ,KAAe,EAAnB,EAAuB;AACnB,aAAO,EAAP;AACH;;AACD,QAAIgD,GAAG,CAAChD,MAAJ,KAAe,EAAnB,EAAuB;AACnB,UAAIyK,QAAQ,CAACzK,MAAT,KAAoB,EAAxB,EAA4B;AACxB,eAAO0L,IAAI,CAACvL,KAAL,CAAW,EAAX,EAAeuL,IAAI,CAAC1L,MAApB,CAAP;AACH;;AACD,aAAO0L,IAAI,CAACvL,KAAL,CAAW,EAAX,EAAeuL,IAAI,CAAC1L,MAApB,CAAP;AACH;;AACD,QAAIgD,GAAG,CAAChD,MAAJ,GAAa,EAAb,IAAmBgD,GAAG,CAAChD,MAAJ,IAAc,EAArC,EAAyC;AACrC,aAAO0L,IAAI,CAACvL,KAAL,CAAW6C,GAAG,CAAChD,MAAJ,GAAa,CAAxB,EAA2B0L,IAAI,CAAC1L,MAAhC,CAAP;AACH;;AACD,WAAO,EAAP;AACH;AACD;AACJ;AACA;;;AACI4J,EAAAA,iBAAiB,CAACpC,QAAQ,GAAG,KAAKA,QAAjB,EAA2B;AACxC,UAAMmE,YAAY,GAAGnE,QAAQ,EAAEoE,aAAV,EAAyBC,UAA9C;;AACA,QAAI,CAACF,YAAY,EAAEC,aAAnB,EAAkC;AAC9B,aAAOpE,QAAQ,CAACoE,aAAhB;AACH,KAFD,MAGK;AACD,aAAO,KAAKhC,iBAAL,CAAuB+B,YAAvB,CAAP;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACInD,EAAAA,iBAAiB,CAAC1H,UAAD,EAAa;AAC1B,QAAI,KAAKiH,YAAL,IAAsB,CAAC,KAAKjL,mBAAN,IAA6B,KAAKkL,WAA5D,EAA0E;AACtE,WAAKA,WAAL,GACM,KAAKK,QAAL,CAAc,KAAKpL,iBAAL,CAAuB,KAAK6O,SAAL,CAAe,KAAKC,aAAL,CAAmB,KAAKT,aAAL,CAAmB,KAAKC,aAAL,CAAmBzK,UAAnB,CAAnB,CAAnB,CAAf,CAAvB,CAAd,CADN,GAEM,EAFN;AAGA,WAAKkH,WAAL,GAAmB,KAAnB;AACA;AACH;;AACD,QAAI1I,KAAK,CAACC,OAAN,CAAc,KAAKrD,qBAAnB,CAAJ,EAA+C;AAC3C,WAAKmM,QAAL,CAAc,KAAKpL,iBAAL,CAAuB,KAAK6O,SAAL,CAAe,KAAKC,aAAL,CAAmB,KAAKV,WAAL,CAAiB,KAAKC,aAAL,CAAmB,KAAKC,aAAL,CAAmBzK,UAAnB,CAAnB,CAAjB,EAAqE,KAAK5E,qBAA1E,CAAnB,CAAf,CAAvB,CAAd;AACH,KAFD,MAGK,IAAI,KAAKA,qBAAL,IACJ,CAAC,KAAKA,qBAAN,IAA+B,KAAKP,MAAL,KAAgBmF,UAD/C,EAC4D;AAC7D,WAAKuH,QAAL,CAAc,KAAKpL,iBAAL,CAAuB,KAAK6O,SAAL,CAAe,KAAKC,aAAL,CAAmB,KAAKT,aAAL,CAAmB,KAAKC,aAAL,CAAmBzK,UAAnB,CAAnB,CAAnB,CAAf,CAAvB,CAAd;AACH,KAHI,MAIA;AACD,WAAKuH,QAAL,CAAc,KAAKpL,iBAAL,CAAuB,KAAK6O,SAAL,CAAehL,UAAf,CAAvB,CAAd;AACH;AACJ;;AACDgL,EAAAA,SAAS,CAAC9O,KAAD,EAAQ;AACb,QAAI,CAAC,KAAK2K,aAAN,IAAuB3K,KAAK,KAAK;AAAG;AAAxC,MAA4D;AACxD,aAAOA,KAAP;AACH;;AACD,QAAI,KAAK2B,cAAL,CAAoBwE,UAApB,CAA+B;AAAY;AAA3C,UACC,KAAKvG,QAAL,IAAiB,CAAC,KAAKV,qBADxB,CAAJ,EACoD;AAChD,aAAOc,KAAP;AACH;;AACD,QAAIqN,MAAM,CAACrN,KAAD,CAAN,CAAcgD,MAAd,GAAuB,EAAvB,IAA6B,KAAK1D,cAAL,CAAoB0D,MAApB,GAA6B,EAA9D,EAAkE;AAC9D,aAAOqK,MAAM,CAACrN,KAAD,CAAb;AACH;;AACD,UAAMgP,GAAG,GAAGvL,MAAM,CAACzD,KAAD,CAAlB;;AACA,QAAI,KAAK2B,cAAL,CAAoBwE,UAApB,CAA+B;AAAY;AAA3C,SAA+D1C,MAAM,CAACC,KAAP,CAAasL,GAAb,CAAnE,EAAsF;AAClF,YAAMhG,GAAG,GAAGqE,MAAM,CAACrN,KAAD,CAAN,CAAckD,OAAd,CAAsB,GAAtB,EAA2B,GAA3B,CAAZ;AACA,aAAOO,MAAM,CAACuF,GAAD,CAAb;AACH;;AACD,WAAOvF,MAAM,CAACC,KAAP,CAAasL,GAAb,IAAoBhP,KAApB,GAA4BgP,GAAnC;AACH;;AACDX,EAAAA,WAAW,CAACrO,KAAD,EAAQiP,0BAAR,EAAoC;AAC3C,QAAI,KAAKtN,cAAL,CAAoBwE,UAApB,CAA+B;AAAU;AAAzC,SACAnG,KAAK,CAACiE,QAAN,CAAe;AAAI;AAAnB,KADJ,EACmC;AAC/B,aAAOjE,KAAP;AACH;;AACD,WAAOA,KAAK,GACNA,KAAK,CAACkD,OAAN,CAAc,KAAKgM,gBAAL,CAAsBD,0BAAtB,CAAd,EAAiE;AAAG;AAApE,KADM,GAENjP,KAFN;AAGH;;AACDuO,EAAAA,aAAa,CAACvO,KAAD,EAAQ;AACjB,QAAI,CAAC,KAAKrB,MAAV,EAAkB;AACd,aAAOqB,KAAP;AACH;;AACD,WAAOA,KAAK,GAAGA,KAAK,CAACkD,OAAN,CAAc,KAAKvE,MAAnB,EAA2B;AAAG;AAA9B,KAAH,GAAuDqB,KAAnE;AACH;;AACDsO,EAAAA,aAAa,CAACtO,KAAD,EAAQ;AACjB,QAAI,CAAC,KAAKtB,MAAV,EAAkB;AACd,aAAOsB,KAAP;AACH;;AACD,WAAOA,KAAK,GAAGA,KAAK,CAACkD,OAAN,CAAc,KAAKxE,MAAnB,EAA2B;AAAG;AAA9B,KAAH,GAAuDsB,KAAnE;AACH;;AACDmP,EAAAA,uBAAuB,CAAC7J,MAAD,EAAS;AAC5B,QAAI7F,iBAAiB,GAAG6C,KAAK,CAACC,OAAN,CAAc,KAAKrD,qBAAnB,IAClB,KAAKO,iBAAL,CAAuByJ,MAAvB,CAA+BxG,CAAD,IAAO;AACnC,aAAO,KAAKxD,qBAAL,CAA2B+E,QAA3B,CAAoCvB,CAApC,CAAP;AACH,KAFC,CADkB,GAIlB,KAAKjD,iBAJX;;AAKA,QAAI,CAAC,KAAKqC,uBAAN,IACA,KAAKsN,qBAAL,EADA,IAEA9J,MAAM,CAACrB,QAAP,CAAgB;AAAI;AAApB,KAFA,IAGA,KAAKtC,cAAL,CAAoBsC,QAApB,CAA6B;AAAI;AAAjC,KAHJ,EAGyD;AACrDxE,MAAAA,iBAAiB,GAAGA,iBAAiB,CAACyJ,MAAlB,CAA0BJ,IAAD,IAAUA,IAAI,KAAK;AAAI;AAAhD,OAApB;AACH;;AACD,WAAO,KAAKuF,WAAL,CAAiB/I,MAAjB,EAAyB7F,iBAAzB,CAAP;AACH;;AACDyP,EAAAA,gBAAgB,CAACD,0BAAD,EAA6B;AACzC,WAAO,IAAI5O,MAAJ,CAAW4O,0BAA0B,CAACxM,GAA3B,CAAgCwJ,IAAD,IAAW,KAAIA,IAAK,EAAnD,EAAsDrJ,IAAtD,CAA2D,GAA3D,CAAX,EAA4E,IAA5E,CAAP;AACH;;AACDyM,EAAAA,0BAA0B,CAACrP,KAAD,EAAQ;AAC9B,UAAMsP,OAAO,GAAGhN,KAAK,CAACC,OAAN,CAAc,KAAK1D,aAAnB,IACV,KAAKA,aADK,GAEV,CAAC,KAAKA,aAAN,CAFN;AAGA,WAAOmB,KAAK,CAACkD,OAAN,CAAc,KAAKgM,gBAAL,CAAsBI,OAAtB,CAAd,EAA8C;AAAI;AAAlD,KAAP;AACH;;AACDP,EAAAA,aAAa,CAACzJ,MAAD,EAAS;AAClB,QAAIA,MAAM,KAAK;AAAG;AAAlB,MAAsC;AAClC,aAAOA,MAAP;AACH;;AACD,QAAI,KAAK3D,cAAL,CAAoBwE,UAApB,CAA+B;AAAU;AAAzC,SACA,KAAKtH,aAAL,KAAuB;AAAI;AAD/B,MAC4C;AACxC;AACAyG,MAAAA,MAAM,GAAGA,MAAM,CAACpC,OAAP,CAAe;AAAI;AAAnB,QAAgC;AAAI;AAApC,OAAT;AACH;;AACD,UAAMqM,kBAAkB,GAAG,KAAKC,2BAAL,CAAiC,KAAK7N,cAAtC,CAA3B;;AACA,UAAM8N,cAAc,GAAG,KAAKJ,0BAAL,CAAgC,KAAKF,uBAAL,CAA6B7J,MAA7B,CAAhC,CAAvB;;AACA,QAAI,CAAC,KAAKqF,aAAV,EAAyB;AACrB,aAAO8E,cAAP;AACH;;AACD,QAAIF,kBAAJ,EAAwB;AACpB,UAAIjK,MAAM,KAAK,KAAKzG,aAApB,EAAmC;AAC/B,eAAO,IAAP;AACH;;AACD,UAAI,KAAKS,cAAL,CAAoB0D,MAApB,GAA6B,EAAjC,EAAqC;AACjC,eAAOqK,MAAM,CAACoC,cAAD,CAAb;AACH;;AACD,aAAO,KAAKC,eAAL,CAAqB,KAAK/N,cAA1B,EAA0C8N,cAA1C,CAAP;AACH,KARD,MASK;AACD,aAAOA,cAAP;AACH;AACJ;;AACDL,EAAAA,qBAAqB,GAAG;AACpB,SAAK,MAAMO,GAAX,IAAkB,KAAKxP,QAAvB,EAAiC;AAC7B;AACA,UAAI,KAAKA,QAAL,CAAcwP,GAAd,KAAsB,KAAKxP,QAAL,CAAcwP,GAAd,GAAoBC,cAApB,CAAmC,SAAnC,CAA1B,EAAyE;AACrE,cAAMC,aAAa,GAAG,KAAK1P,QAAL,CAAcwP,GAAd,GAAoBvP,OAApB,CAA4BwF,QAA5B,EAAtB;AACA,cAAMxF,OAAO,GAAG,KAAKD,QAAL,CAAcwP,GAAd,GAAoBvP,OAApC;;AACA,YAAIyP,aAAa,EAAE5L,QAAf,CAAwB;AAAI;AAA5B,aACA7D,OAAO,EAAEiD,IAAT,CAAc,KAAK1B,cAAnB,CADJ,EACwC;AACpC,iBAAO,IAAP;AACH;AACJ;AACJ;;AACD,WAAO,KAAP;AACH,GAzhB8C,CA0hB/C;;;AACA6N,EAAAA,2BAA2B,CAACM,aAAD,EAAgB;AACvC,UAAMC,OAAO,GAAGD,aAAa,CAAChN,KAAd,CAAoB,IAAIzC,MAAJ,CAAY,sBAAZ,CAApB,CAAhB;AACA,WAAO0P,OAAO,GAAGtM,MAAM,CAACsM,OAAO,CAAC,CAAD,CAAR,CAAT,GAAwB,IAAtC;AACH;;AACDL,EAAAA,eAAe,CAACM,mBAAD,EAAsBP,cAAtB,EAAsC;AACjD,UAAMF,kBAAkB,GAAGS,mBAAmB,CAAC7M,KAApB,CAA0B,EAA1B,EAA8B,EAA9B,CAA3B;;AACA,QAAI6M,mBAAmB,CAACrN,OAApB,CAA4B,GAA5B,IAAmC,CAAnC,IACC,KAAK/C,QAAL,IAAiB6D,MAAM,CAAC8L,kBAAD,CAAN,GAA6B,CADnD,EACuD;AACnD,UAAI,KAAK1Q,aAAL,KAAuB;AAAI;AAA3B,SAA0C,KAAKe,QAAnD,EAA6D;AACzD;AACA6P,QAAAA,cAAc,GAAGA,cAAc,CAACvM,OAAf,CAAuB,GAAvB,EAA4B,GAA5B,CAAjB;AACH;;AACD,aAAO,KAAKtD,QAAL,GACD6D,MAAM,CAACgM,cAAD,CAAN,CAAuBQ,OAAvB,CAA+BxM,MAAM,CAAC8L,kBAAD,CAArC,CADC,GAED9L,MAAM,CAACgM,cAAD,CAAN,CAAuBQ,OAAvB,CAA+B,CAA/B,CAFN;AAGH;;AACD,WAAO,KAAK7C,cAAL,CAAoBqC,cAApB,CAAP;AACH;;AACDS,EAAAA,qBAAqB,CAACC,OAAD,EAAU;AAC3B,WAASA,OAAO,CAACrN,KAAR,CAAc,UAAd,KACLqN,OAAO,CACFtN,KADL,CACW;AAAG;AADd,MAEKuN,MAFL,CAEY,CAACC,KAAD,EAAQC,OAAR,EAAiB5G,KAAjB,KAA2B;AACnC,WAAK6G,MAAL,GACID,OAAO,KAAK;AAAI;AAAhB,QAA4C5G,KAA5C,GAAoD,KAAK6G,MAD7D;;AAEA,UAAID,OAAO,KAAK;AAAI;AAApB,QAAgD;AAC5C,eAAO,KAAK9H,gBAAL,CAAsB8H,OAAtB,IAAiCD,KAAK,GAAGC,OAAzC,GAAmDD,KAA1D;AACH;;AACD,WAAKG,IAAL,GAAY9G,KAAZ;AACA,YAAM+G,YAAY,GAAGhN,MAAM,CAAC0M,OAAO,CAAChN,KAAR,CAAc,KAAKoN,MAAL,GAAc,CAA5B,EAA+B,KAAKC,IAApC,CAAD,CAA3B;AACA,YAAME,WAAW,GAAG,IAAIpO,KAAJ,CAAUmO,YAAY,GAAG,CAAzB,EAA4B7N,IAA5B,CAAiCuN,OAAO,CAAC,KAAKI,MAAL,GAAc,CAAf,CAAxC,CAApB;;AACA,UAAIJ,OAAO,CAAChN,KAAR,CAAc,CAAd,EAAiB,KAAKoN,MAAtB,EAA8BvN,MAA9B,GAAuC,CAAvC,IACAmN,OAAO,CAAClM,QAAR,CAAiB;AAAI;AAArB,OADJ,EAC0C;AACtC,cAAM0M,OAAO,GAAGR,OAAO,CAAChN,KAAR,CAAc,CAAd,EAAiB,KAAKoN,MAAL,GAAc,CAA/B,CAAhB;AACA,eAAOI,OAAO,CAAC1M,QAAR,CAAiB;AAAI;AAArB,YACDoM,KAAK,GAAGK,WADP,GAEDC,OAAO,GAAGN,KAAV,GAAkBK,WAFxB;AAGH,OAND,MAOK;AACD,eAAOL,KAAK,GAAGK,WAAf;AACH;AACJ,KArBD,EAqBG,EArBH,CADI,IAuBJP,OAvBJ;AAwBH;;AACDS,EAAAA,0BAA0B,GAAG;AACzB,WAAQ,GAAD,CAAMtD,cAAN,GAAuBhK,SAAvB,CAAiC,CAAjC,EAAoC,CAApC,CAAP;AACH;;AAzkB8C;;AA2kBnDiH,cAAc,CAACL,IAAf;AAAA,mBAA4GK,cAA5G,EAplByGjN,EAolBzG,UAA4Ia,QAA5I,GAplByGb,EAolBzG,UAAiKgB,eAAjK,GAplByGhB,EAolBzG,UAA6LA,EAAE,CAACuT,UAAhM,MAplByGvT,EAolBzG,UAAuOA,EAAE,CAACwT,SAA1O;AAAA;;AACAvG,cAAc,CAACJ,KAAf,kBArlByG7M,EAqlBzG;AAAA,SAAgHiN,cAAhH;AAAA,WAAgHA,cAAhH;AAAA;;AACA;AAAA,qDAtlByGjN,EAslBzG,mBAA4FiN,cAA5F,EAAwH,CAAC;AAC7GH,IAAAA,IAAI,EAAE3M;AADuG,GAAD,CAAxH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAE2M,MAAAA,IAAI,EAAEhL,SAAR;AAAmBiL,MAAAA,UAAU,EAAE,CAAC;AAC9DD,QAAAA,IAAI,EAAE1M,MADwD;AAE9D4M,QAAAA,IAAI,EAAE,CAACnM,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEiM,MAAAA,IAAI,EAAEhL,SAAR;AAAmBiL,MAAAA,UAAU,EAAE,CAAC;AAClCD,QAAAA,IAAI,EAAE1M,MAD4B;AAElC4M,QAAAA,IAAI,EAAE,CAAChM,eAAD;AAF4B,OAAD;AAA/B,KAH2B,EAM3B;AAAE8L,MAAAA,IAAI,EAAE9M,EAAE,CAACuT,UAAX;AAAuBxG,MAAAA,UAAU,EAAE,CAAC;AACtCD,QAAAA,IAAI,EAAEzM;AADgC,OAAD;AAAnC,KAN2B,EAQ3B;AAAEyM,MAAAA,IAAI,EAAE9M,EAAE,CAACwT,SAAX;AAAsBzG,MAAAA,UAAU,EAAE,CAAC;AACrCD,QAAAA,IAAI,EAAEzM;AAD+B,OAAD;AAAlC,KAR2B,CAAP;AAUlB,GAZxB;AAAA,K,CAcA;AACA;;;AACA,MAAMoT,gBAAN,CAAuB;AACnBzP,EAAAA,WAAW,EACX;AACAkJ,EAAAA,QAFW,EAEDjJ,OAFC,EAEQyP,YAFR,EAEsB;AAC7B,SAAKxG,QAAL,GAAgBA,QAAhB;AACA,SAAKjJ,OAAL,GAAeA,OAAf;AACA,SAAKyP,YAAL,GAAoBA,YAApB,CAH6B,CAI7B;;AACA,SAAKrP,cAAL,GAAsB,EAAtB;AACA,SAAKlC,iBAAL,GAAyB,EAAzB;AACA,SAAKU,QAAL,GAAgB,EAAhB;AACA,SAAKxB,MAAL,GAAc,EAAd;AACA,SAAKD,MAAL,GAAc,EAAd;AACA,SAAKE,iBAAL,GAAyB,GAAzB;AACA,SAAKC,aAAL,GAAqB,GAArB;AACA,SAAKK,qBAAL,GAA6B,IAA7B;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKH,aAAL,GAAqB,IAArB;AACA,SAAKC,oBAAL,GAA4B,IAA5B;AACA,SAAKI,mBAAL,GAA2B,IAA3B;AACA,SAAKN,YAAL,GAAoB,IAApB;AACA,SAAKD,eAAL,GAAuB,IAAvB;AACA,SAAKU,UAAL,GAAkB,IAAlB;AACA,SAAKF,cAAL,GAAsB,IAAtB;AACA,SAAKC,oBAAL,GAA4B,IAA5B;AACA,SAAKG,gBAAL,GAAwB,IAAxB;AACA,SAAKE,QAAL,GAAgB,IAAhB;AACA,SAAKE,mBAAL,GAA2B,IAA3B;AACA,SAAKH,GAAL,GAAW,IAAX;AACA,SAAKI,gBAAL,GAAwB,IAAxB;AACA,SAAKE,iBAAL,GAAyB,IAAzB;AACA,SAAKJ,sBAAL,GAA8B,IAA9B;AACA,SAAKK,UAAL,GAAkB,IAAI1C,YAAJ,EAAlB;AACA,SAAKyT,UAAL,GAAkB,EAAlB;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKlG,oBAAL,GAA4B,EAA5B;AACA,SAAKmG,WAAL,GAAmB,KAAnB;AACA;;AACA,SAAKC,YAAL,GAAoB,KAApB,CApC6B,CAqC7B;;AACA,SAAKhG,QAAL,GAAiBC,CAAD,IAAO,CAAG,CAA1B,CAtC6B,CAuC7B;;;AACA,SAAKgG,OAAL,GAAe,MAAM,CAAG,CAAxB;AACH;;AACDC,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,UAAM;AAAE7P,MAAAA,cAAF;AAAkBlC,MAAAA,iBAAlB;AAAqCU,MAAAA,QAArC;AAA+CxB,MAAAA,MAA/C;AAAuDD,MAAAA,MAAvD;AAA+DE,MAAAA,iBAA/D;AAAkFC,MAAAA,aAAlF;AAAiGK,MAAAA,qBAAjG;AAAwHC,MAAAA,WAAxH;AAAqIH,MAAAA,aAArI;AAAoJC,MAAAA,oBAApJ;AAA0KI,MAAAA,mBAA1K;AAA+LN,MAAAA,YAA/L;AAA6MD,MAAAA,eAA7M;AAA8NU,MAAAA,UAA9N;AAA0OF,MAAAA,cAA1O;AAA0PC,MAAAA,oBAA1P;AAAgRG,MAAAA,gBAAhR;AAAkSE,MAAAA,QAAlS;AAA4SE,MAAAA,mBAA5S;AAAiUH,MAAAA,GAAjU;AAAsUI,MAAAA,gBAAtU;AAAwVE,MAAAA,iBAAxV;AAA2WJ,MAAAA;AAA3W,QAAuY2R,OAA7Y;;AACA,QAAI7P,cAAJ,EAAoB;AAChB,UAAIA,cAAc,CAAC8P,YAAf,KAAgC9P,cAAc,CAAC+P,aAA/C,IACA,CAAC/P,cAAc,CAACgQ,WADpB,EACiC;AAC7B,aAAKX,YAAL,CAAkBhG,WAAlB,GAAgC,IAAhC;AACH;;AACD,UAAIrJ,cAAc,CAAC8P,YAAf,IACA9P,cAAc,CAAC8P,YAAf,CAA4B5O,KAA5B,CAAkC;AAAK;AAAvC,QAAiDG,MAAjD,GAA0D,CAD9D,EACiE;AAC7D,aAAKiI,oBAAL,GAA4BtJ,cAAc,CAAC8P,YAAf,CACvB5O,KADuB,CACjB;AAAK;AADY,UAEvB+O,IAFuB,CAElB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAChB,iBAAOD,CAAC,CAAC7O,MAAF,GAAW8O,CAAC,CAAC9O,MAApB;AACH,SAJ2B,CAA5B;;AAKA,aAAK+O,QAAL;AACH,OARD,MASK;AACD,aAAK9G,oBAAL,GAA4B,EAA5B;AACA,aAAKgG,UAAL,GAAkBtP,cAAc,CAAC8P,YAAf,IAA+B;AAAG;AAApD;AACA,aAAKT,YAAL,CAAkBrP,cAAlB,GAAmC,KAAKsP,UAAxC;AACH;AACJ;;AACD,QAAIxR,iBAAJ,EAAuB;AACnB,UAAI,CAACA,iBAAiB,CAACgS,YAAnB,IAAmC,CAACnP,KAAK,CAACC,OAAN,CAAc9C,iBAAiB,CAACgS,YAAhC,CAAxC,EAAuF;AACnF;AACH,OAFD,MAGK;AACD,aAAKT,YAAL,CAAkBvR,iBAAlB,GAAsCA,iBAAiB,CAACgS,YAAlB,IAAkC,EAAxE;AACH;AACJ;;AACD,QAAIlS,oBAAJ,EAA0B;AACtB,WAAKyR,YAAL,CAAkBzR,oBAAlB,GAAyCA,oBAAoB,CAACkS,YAA9D;;AACA,UAAI,KAAKT,YAAL,CAAkBzR,oBAAtB,EAA4C;AACxC,aAAKyR,YAAL,CAAkBvR,iBAAlB,GAAsC,KAAKuR,YAAL,CAAkBvR,iBAAlB,CAAoCyJ,MAApC,CAA4C8I,CAAD,IAAOA,CAAC,KAAK;AAAI;AAA5D,SAAtC;AACH;AACJ,KAnCgB,CAoCjB;;;AACA,QAAI7R,QAAQ,IAAIA,QAAQ,CAACsR,YAAzB,EAAuC;AACnC,WAAKT,YAAL,CAAkB7Q,QAAlB,GAA6BA,QAAQ,CAACsR,YAAtC;AACH;;AACD,QAAI9R,GAAG,IAAIA,GAAG,CAAC8R,YAAf,EAA6B;AACzB,WAAKT,YAAL,CAAkBrR,GAAlB,GAAwBA,GAAG,CAAC8R,YAA5B;AACH;;AACD,QAAI9S,MAAJ,EAAY;AACR,WAAKqS,YAAL,CAAkBrS,MAAlB,GAA2BA,MAAM,CAAC8S,YAAlC;AACH;;AACD,QAAI/S,MAAJ,EAAY;AACR,WAAKsS,YAAL,CAAkBtS,MAAlB,GAA2BA,MAAM,CAAC+S,YAAlC;AACH;;AACD,QAAI7S,iBAAJ,EAAuB;AACnB,WAAKoS,YAAL,CAAkBpS,iBAAlB,GAAsCA,iBAAiB,CAAC6S,YAAxD;AACH;;AACD,QAAI5S,aAAJ,EAAmB;AACf,WAAKmS,YAAL,CAAkBnS,aAAlB,GAAkCA,aAAa,CAAC4S,YAAhD;AACH;;AACD,QAAIvS,qBAAJ,EAA2B;AACvB,WAAK8R,YAAL,CAAkB9R,qBAAlB,GAA0CA,qBAAqB,CAACuS,YAAhE;AACH;;AACD,QAAItS,WAAJ,EAAiB;AACb,WAAK6R,YAAL,CAAkB7R,WAAlB,GAAgCA,WAAW,CAACsS,YAA5C;AACH;;AACD,QAAIzS,aAAJ,EAAmB;AACf,WAAKgS,YAAL,CAAkBhS,aAAlB,GAAkCA,aAAa,CAACyS,YAAhD;;AACA,UAAIzS,aAAa,CAAC0S,aAAd,KAAgC,KAAhC,IACA1S,aAAa,CAACyS,YAAd,KAA+B,IAD/B,IAEA,KAAKP,UAFT,EAEqB;AACjBe,QAAAA,qBAAqB,CAAC,MAAM;AACxB,eAAKjB,YAAL,CAAkBvG,WAAlB,EAA+BkC,aAA/B,CAA6CuF,KAA7C;AACH,SAFoB,CAArB;AAGH;AACJ;;AACD,QAAIjT,oBAAJ,EAA0B;AACtB,WAAK+R,YAAL,CAAkB/R,oBAAlB,GAAyCA,oBAAoB,CAACwS,YAA9D;AACH;;AACD,QAAIpS,mBAAJ,EAAyB;AACrB,WAAK2R,YAAL,CAAkB3R,mBAAlB,GAAwCA,mBAAmB,CAACoS,YAA5D;AACH;;AACD,QAAI1S,YAAJ,EAAkB;AACd,WAAKiS,YAAL,CAAkBjS,YAAlB,GAAiCA,YAAY,CAAC0S,YAA9C;AACH;;AACD,QAAI3S,eAAJ,EAAqB;AACjB,WAAKkS,YAAL,CAAkBlS,eAAlB,GAAoCA,eAAe,CAAC2S,YAApD;AACH;;AACD,QAAIjS,UAAJ,EAAgB;AACZ,WAAKwR,YAAL,CAAkBxR,UAAlB,GAA+BA,UAAU,CAACiS,YAA1C;AACH;;AACD,QAAInS,cAAJ,EAAoB;AAChB,WAAK0R,YAAL,CAAkB1R,cAAlB,GAAmCA,cAAc,CAACmS,YAAlD;AACH;;AACD,QAAI/R,gBAAJ,EAAsB;AAClB,WAAKsR,YAAL,CAAkBtR,gBAAlB,GAAqCA,gBAAgB,CAAC+R,YAAtD;AACH;;AACD,QAAI7R,QAAJ,EAAc;AACV,WAAKoR,YAAL,CAAkBpR,QAAlB,GAA6BA,QAAQ,CAAC6R,YAAtC;AACH;;AACD,QAAI3R,mBAAJ,EAAyB;AACrB,WAAKkR,YAAL,CAAkBlR,mBAAlB,GAAwCA,mBAAmB,CAAC2R,YAA5D;AACH;;AACD,QAAI1R,gBAAJ,EAAsB;AAClB,WAAKiR,YAAL,CAAkBjR,gBAAlB,GAAqCA,gBAAgB,CAAC0R,YAAtD;AACH;;AACD,QAAIxR,iBAAJ,EAAuB;AACnB,WAAK+Q,YAAL,CAAkB/Q,iBAAlB,GAAsCA,iBAAiB,CAACwR,YAAxD;AACH;;AACD,QAAI5R,sBAAJ,EAA4B;AACxB,WAAKmR,YAAL,CAAkBnR,sBAAlB,GAA2CA,sBAAsB,CAAC4R,YAAlE;AACH;;AACD,SAAKU,UAAL;AACH,GAzJkB,CA0JnB;;;AACAC,EAAAA,QAAQ,CAAC;AAAEpS,IAAAA;AAAF,GAAD,EAAY;AAChB,QAAI,CAAC,KAAKgR,YAAL,CAAkBxR,UAAnB,IAAiC,CAAC,KAAKyR,UAA3C,EAAuD;AACnD,aAAO,IAAP;AACH;;AACD,QAAI,KAAKD,YAAL,CAAkBlL,OAAtB,EAA+B;AAC3B,aAAO,KAAKuM,sBAAL,CAA4BrS,KAA5B,CAAP;AACH;;AACD,QAAI,KAAKgR,YAAL,CAAkB9K,YAAtB,EAAoC;AAChC,aAAO,KAAKmM,sBAAL,CAA4BrS,KAA5B,CAAP;AACH;;AACD,QAAI,KAAKiR,UAAL,CAAgB9K,UAAhB,CAA2B;AAAY;AAAvC,KAAJ,EAA6D;AACzD,aAAO,IAAP;AACH;;AACD,QAAI/E,iBAAiB,CAAC6C,QAAlB,CAA2B,KAAKgN,UAAhC,CAAJ,EAAiD;AAC7C,aAAO,IAAP;AACH;;AACD,QAAI,KAAKD,YAAL,CAAkBlS,eAAtB,EAAuC;AACnC,aAAO,IAAP;AACH;;AACD,QAAIqC,SAAS,CAAC8C,QAAV,CAAmB,KAAKgN,UAAxB,CAAJ,EAAyC;AACrC,aAAO,KAAKqB,aAAL,CAAmBtS,KAAnB,CAAP;AACH;;AACD,QAAIA,KAAK,IAAIA,KAAK,CAAC4F,QAAN,GAAiB5C,MAAjB,IAA2B,CAAxC,EAA2C;AACvC,UAAIuP,YAAY,GAAG,CAAnB;;AACA,UAAI,KAAKtB,UAAL,CAAgB9K,UAAhB,CAA2B;AAAU;AAArC,OAAJ,EAAyD;AACrD,eAAO,IAAP;AACH;;AACD,WAAK,MAAMwJ,GAAX,IAAkB,KAAKqB,YAAL,CAAkB7Q,QAApC,EAA8C;AAC1C,YAAI,KAAK6Q,YAAL,CAAkB7Q,QAAlB,CAA2BwP,GAA3B,GAAiCrP,QAArC,EAA+C;AAC3C,cAAI,KAAK2Q,UAAL,CAAgBtO,OAAhB,CAAwBgN,GAAxB,MAAiC,KAAKsB,UAAL,CAAgBuB,WAAhB,CAA4B7C,GAA5B,CAArC,EAAuE;AACnE,kBAAM8C,GAAG,GAAG,KAAKxB,UAAL,CACPpO,KADO,CACD;AAAG;AADF,cAEPqG,MAFO,CAECnF,CAAD,IAAOA,CAAC,KAAK4L,GAFb,EAGP/M,IAHO,CAGF;AAAG;AAHD,aAAZ;;AAIA2P,YAAAA,YAAY,IAAIE,GAAG,CAACzP,MAApB;AACH,WAND,MAOK,IAAI,KAAKiO,UAAL,CAAgBtO,OAAhB,CAAwBgN,GAAxB,MAAiC,CAAC,CAAtC,EAAyC;AAC1C4C,YAAAA,YAAY;AACf;;AACD,cAAI,KAAKtB,UAAL,CAAgBtO,OAAhB,CAAwBgN,GAAxB,MAAiC,CAAC,CAAlC,IACA3P,KAAK,CAAC4F,QAAN,GAAiB5C,MAAjB,IAA2B,KAAKiO,UAAL,CAAgBtO,OAAhB,CAAwBgN,GAAxB,CAD/B,EAC6D;AACzD,mBAAO,IAAP;AACH;;AACD,cAAI4C,YAAY,KAAK,KAAKtB,UAAL,CAAgBjO,MAArC,EAA6C;AACzC,mBAAO,IAAP;AACH;AACJ;AACJ;;AACD,UAAI,KAAKiO,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,YAA2D,CAA3D,IACA3C,KAAK,CAAC4F,QAAN,GAAiB5C,MAAjB,KACI,KAAKiO,UAAL,CAAgBjO,MAAhB,GACIS,MAAM,CAAC,CAAC,KAAKwN,UAAL,CAAgBpO,KAAhB,CAAsB;AAAI;AAA1B,QAAqD,CAArD,KACJ;AAAG;AADA,QACoBA,KADpB,CAC0B;AAAI;AAD9B,QAC0D,CAD1D,CAAD,CADV,GAGI,CALZ,EAKe;AACX,eAAO,IAAP;AACH,OAPD,MAQK,IAAK,KAAKoO,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,UAAiD,CAAjD,IACN3C,KAAK,CAAC4F,QAAN,GAAiB5C,MAAjB,GACI,KAAKiO,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,OAFC,IAGJ,KAAKsO,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,UAAqD,CAArD,IACG3C,KAAK,CAAC4F,QAAN,GAAiB5C,MAAjB,GACI,KAAKiO,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,OALH,IAML,KAAKsO,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,YAA2D,CAN1D,EAM6D;AAC9D,eAAO,KAAK0P,sBAAL,CAA4BrS,KAA5B,CAAP;AACH;;AACD,UAAI,KAAKiR,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,YAAmD,CAAC,CAApD,IACA,KAAKsO,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,YAAuD,CAAC,CAD5D,EAC+D;AAC3D;AACA3C,QAAAA,KAAK,GAAG,OAAOA,KAAP,KAAiB,QAAjB,GAA4BqN,MAAM,CAACrN,KAAD,CAAlC,GAA4CA,KAApD;;AACA,cAAM0S,KAAK,GAAG,KAAKzB,UAAL,CAAgBpO,KAAhB,CAAsB,GAAtB,CAAd;;AACA,cAAMG,MAAM,GAAG,KAAKgO,YAAL,CAAkB9R,qBAAlB,GACT,KAAK+R,UAAL,CAAgBjO,MAAhB,GACE,KAAKgO,YAAL,CAAkB7C,0BAAlB,CAA6C,KAAK8C,UAAlD,CADF,GAEEsB,YAHO,GAIT,KAAK5T,MAAL,GACI,KAAKsS,UAAL,CAAgBjO,MAAhB,GAAyB,KAAKrE,MAAL,CAAYqE,MAArC,GAA8CuP,YADlD,GAEI,KAAKtB,UAAL,CAAgBjO,MAAhB,GAAyBuP,YANnC;;AAOA,YAAIG,KAAK,CAAC1P,MAAN,KAAiB,CAArB,EAAwB;AACpB,cAAIhD,KAAK,CAAC4F,QAAN,GAAiB5C,MAAjB,GAA0BA,MAA9B,EAAsC;AAClC,mBAAO,KAAKqP,sBAAL,CAA4BrS,KAA5B,CAAP;AACH;AACJ;;AACD,YAAI0S,KAAK,CAAC1P,MAAN,GAAe,CAAnB,EAAsB;AAClB,gBAAM2P,cAAc,GAAGD,KAAK,CAACA,KAAK,CAAC1P,MAAN,GAAe,CAAhB,CAA5B;;AACA,cAAI2P,cAAc,IACd,KAAK3B,YAAL,CAAkBvR,iBAAlB,CAAoCwE,QAApC,CAA6C0O,cAAc,CAAC,CAAD,CAA3D,CADA,IAEAtF,MAAM,CAACrN,KAAD,CAAN,CAAciE,QAAd,CAAuB0O,cAAc,CAAC,CAAD,CAAd,IAAqB,EAA5C,CAFA,IAGA,CAAC,KAAKzT,qBAHV,EAGiC;AAC7B,kBAAM0T,OAAO,GAAG5S,KAAK,CAAC6C,KAAN,CAAY8P,cAAc,CAAC,CAAD,CAA1B,CAAhB;AACA,mBAAOC,OAAO,CAACA,OAAO,CAAC5P,MAAR,GAAiB,CAAlB,CAAP,CAA4BA,MAA5B,KAAuC2P,cAAc,CAAC3P,MAAf,GAAwB,CAA/D,GACD,IADC,GAED,KAAKqP,sBAAL,CAA4BrS,KAA5B,CAFN;AAGH,WARD,MASK,IAAI,CAAE2S,cAAc,IACrB,CAAC,KAAK3B,YAAL,CAAkBvR,iBAAlB,CAAoCwE,QAApC,CAA6C0O,cAAc,CAAC,CAAD,CAA3D,CADK,IAEN,CAACA,cAFK,IAGN,KAAK3B,YAAL,CAAkB9R,qBAHb,KAILc,KAAK,CAACgD,MAAN,IAAgBA,MAAM,GAAG,CAJxB,EAI2B;AAC5B,mBAAO,IAAP;AACH,WANI,MAOA;AACD,mBAAO,KAAKqP,sBAAL,CAA4BrS,KAA5B,CAAP;AACH;AACJ;AACJ;;AACD,UAAI,KAAKiR,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,YAAmD,CAAnD,IACA,KAAKsO,UAAL,CAAgBtO,OAAhB,CAAwB;AAAI;AAA5B,YAAuD,CAD3D,EAC8D;AAC1D,eAAO,IAAP;AACH;AACJ;;AACD,QAAI3C,KAAJ,EAAW;AACP,WAAKE,UAAL,CAAgB2S,IAAhB;AACA,aAAO,IAAP;AACH;;AACD,WAAO,IAAP;AACH;;AACDC,EAAAA,OAAO,GAAG;AACN,SAAK1B,WAAL,GAAmB,IAAnB;AACH;;AACD2B,EAAAA,OAAO,GAAG;AACN,SAAK7B,UAAL,GAAkB,IAAlB;AACH;;AACD8B,EAAAA,aAAa,CAAChT,KAAD,EAAQ;AACjB;AACA,QAAI,CAACA,KAAK,KAAK;AAAG;AAAb,OAAmCA,KAAK,KAAK,IAA7C,IAAqDA,KAAK,KAAKZ,SAAhE,KACA,KAAK4R,YAAL,CAAkBpP,WADtB,EACmC;AAC/B,WAAKoP,YAAL,CAAkBpP,WAAlB,GAAgC,KAAKoP,YAAL,CAAkBhF,cAAlB,CAAiC;AAAG;AAApC,OAAhC;AACH;AACJ;;AACDiH,EAAAA,OAAO,CAACC,CAAD,EAAI;AACP;AACA,QAAI,KAAK7B,YAAT,EACI;AACJ,UAAM8B,EAAE,GAAGD,CAAC,CAACE,MAAb;;AACA,UAAMC,gBAAgB,GAAG,KAAKrC,YAAL,CAAkBjR,gBAAlB,CAAmCoT,EAAE,CAACnT,KAAtC,CAAzB;;AACA,QAAImT,EAAE,CAAC/I,IAAH,KAAY,QAAhB,EAA0B;AACtB,UAAI,OAAOiJ,gBAAP,KAA4B,QAA5B,IAAwC,OAAOA,gBAAP,KAA4B,QAAxE,EAAkF;AAC9EF,QAAAA,EAAE,CAACnT,KAAH,GAAWqT,gBAAgB,CAACzN,QAAjB,EAAX;AACA,aAAK0N,WAAL,GAAmBH,EAAE,CAACnT,KAAtB;;AACA,aAAK+R,QAAL;;AACA,YAAI,CAAC,KAAKd,UAAV,EAAsB;AAClB,eAAK5F,QAAL,CAAc8H,EAAE,CAACnT,KAAjB;AACA;AACH;;AACD,YAAIiF,QAAQ,GAAGkO,EAAE,CAACI,cAAH,KAAsB,CAAtB,GACTJ,EAAE,CAACI,cAAH,GAAoB,KAAKvC,YAAL,CAAkBrS,MAAlB,CAAyBqE,MADpC,GAETmQ,EAAE,CAACI,cAFT;;AAGA,YAAI,KAAKvU,aAAL,IACA,KAAKa,sBADL,IAEA,KAAKmR,YAAL,CAAkB/R,oBAAlB,CAAuC+D,MAAvC,KAAkD,CAFtD,EAEyD;AACrD,gBAAM+D,WAAW,GAAGoM,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe8B,QAAQ,GAAG,CAA1B,EAA6BA,QAA7B,CAApB;AACA,gBAAMuO,YAAY,GAAG,KAAK7U,MAAL,CAAYqE,MAAjC;;AACA,gBAAMyQ,YAAY,GAAG,KAAKzC,YAAL,CAAkB/J,gBAAlB,CAAmCF,WAAnC,EAAgD,KAAKiK,YAAL,CAAkBrP,cAAlB,CAAiCsD,QAAQ,GAAG,CAAX,GAAeuO,YAAhD,KACjE;AAAG;AADc,WAArB;;AAEA,gBAAME,qBAAqB,GAAG,KAAK1C,YAAL,CAAkB/J,gBAAlB,CAAmCF,WAAnC,EAAgD,KAAKiK,YAAL,CAAkBrP,cAAlB,CAAiCsD,QAAQ,GAAG,CAAX,GAAeuO,YAAhD,KAC1E;AAAG;AADuB,WAA9B;;AAEA,gBAAMG,oBAAoB,GAAG,KAAK3C,YAAL,CAAkBnG,QAAlB,KAA+B,KAAKmG,YAAL,CAAkBlG,MAA9E;AACA,gBAAMD,QAAQ,GAAGpH,MAAM,CAAC,KAAKuN,YAAL,CAAkBnG,QAAnB,CAAN,GAAqC2I,YAArC,IAAqD,EAAtE;AACA,gBAAM1I,MAAM,GAAGrH,MAAM,CAAC,KAAKuN,YAAL,CAAkBlG,MAAnB,CAAN,GAAmC0I,YAAnC,IAAmD,EAAlE;;AACA,cAAI,KAAKI,KAAL,KAAe;AAAY;AAA/B,YAAgD;AAC5C,gBAAI,CAACD,oBAAL,EAA2B;AACvB,kBAAI,KAAK3C,YAAL,CAAkBnG,QAAlB,KAA+B2I,YAAnC,EAAiD;AAC7C,qBAAKxC,YAAL,CAAkBpP,WAAlB,GACI,KAAKjD,MAAL,GACI,KAAKqS,YAAL,CAAkBpG,WAAlB,CAA8BzH,KAA9B,CAAoC,CAApC,EAAuC2H,MAAvC,CADJ,GAEI,KAAKwI,WAAL,CAAiBzQ,KAAjB,CAAuB,KAAKlE,MAA5B,EAAoCiE,IAApC,CAAyC,EAAzC,CAHR;AAIH,eALD,MAMK,IAAI,KAAKoO,YAAL,CAAkBnG,QAAlB,KACL,KAAKmG,YAAL,CAAkBpG,WAAlB,CAA8B5H,MAA9B,GAAuCwQ,YADtC,EACoD;AACrD,qBAAKxC,YAAL,CAAkBpP,WAAlB,GACI,KAAK0R,WAAL,GACI,KAAKtC,YAAL,CAAkBpG,WAAlB,CAA8BzH,KAA9B,CAAoC0H,QAApC,EAA8CC,MAA9C,CAFR;AAGH,eALI,MAMA;AACD,qBAAKkG,YAAL,CAAkBpP,WAAlB,GACI,KAAKjD,MAAL,GACI,KAAK2U,WAAL,CACKzQ,KADL,CACW,KAAKlE,MADhB,EAEKiE,IAFL,CAEU,EAFV,EAGKO,KAHL,CAGW,CAHX,EAGc0H,QAHd,CADJ,GAKI,KAAKmG,YAAL,CAAkBpG,WAAlB,CAA8BzH,KAA9B,CAAoC0H,QAApC,EAA8CC,MAA9C,CALJ,GAMI,KAAKkG,YAAL,CAAkBpP,WAAlB,CAA8BuB,KAA9B,CAAoC2H,MAAM,GAAG0I,YAA7C,EAA2D,KAAKxC,YAAL,CAAkBpG,WAAlB,CAA8B5H,MAA9B,GAAuCwQ,YAAlG,CANJ,GAOI,KAAK9U,MARb;AASH;AACJ,aAxBD,MAyBK,IAAI,CAAC,KAAKsS,YAAL,CAAkBvR,iBAAlB,CAAoCwE,QAApC,CAA6C,KAAK+M,YAAL,CAAkBrP,cAAlB,CAAiCwB,KAAjC,CAAuC8B,QAAQ,GAAG,KAAKtG,MAAL,CAAYqE,MAA9D,EAAsEiC,QAAQ,GAAG,CAAX,GAAe,KAAKtG,MAAL,CAAYqE,MAAjG,CAA7C,CAAD,IACL2Q,oBADC,EACqB;AACtB,kBAAI9I,QAAQ,KAAK,CAAb,IAAkB,KAAKlM,MAA3B,EAAmC;AAC/B,qBAAKqS,YAAL,CAAkBpP,WAAlB,GACI,KAAKjD,MAAL,GACI,KAAKqS,YAAL,CAAkB/R,oBADtB,GAEIkU,EAAE,CAACnT,KAAH,CACK6C,KADL,CACW,KAAKlE,MADhB,EAEKiE,IAFL,CAEU,EAFV,EAGKC,KAHL,CAGW,KAAKnE,MAHhB,EAIKkE,IAJL,CAIU,EAJV,CAFJ,GAOI,KAAKlE,MARb;AASAuG,gBAAAA,QAAQ,GAAGA,QAAQ,GAAG,CAAtB;AACH,eAXD,MAYK;AACD,sBAAM4O,KAAK,GAAGV,EAAE,CAACnT,KAAH,CAASsD,SAAT,CAAmB,CAAnB,EAAsB2B,QAAtB,CAAd;AACA,sBAAM6O,KAAK,GAAGX,EAAE,CAACnT,KAAH,CAASsD,SAAT,CAAmB2B,QAAnB,CAAd;AACA,qBAAK+L,YAAL,CAAkBpP,WAAlB,GACIiS,KAAK,GAAG,KAAK7C,YAAL,CAAkB/R,oBAA1B,GAAiD6U,KADrD;AAEH;AACJ;AACJ;;AACD,cAAI,KAAKF,KAAL,KAAe;AAAY;AAA/B,YAAgD;AAC5C,gBAAI,CAACH,YAAD,IAAiB,CAACC,qBAAlB,IAA2CC,oBAA/C,EAAqE;AACjE1O,cAAAA,QAAQ,GAAGxB,MAAM,CAAC0P,EAAE,CAACI,cAAJ,CAAN,GAA4B,CAAvC;AACH,aAFD,MAGK,IAAI,KAAKvC,YAAL,CAAkBvR,iBAAlB,CAAoCwE,QAApC,CAA6CkP,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe8B,QAAf,EAAyBA,QAAQ,GAAG,CAApC,CAA7C,KACLyO,qBADK,IAEL,CAAC,KAAK1C,YAAL,CAAkBvR,iBAAlB,CAAoCwE,QAApC,CAA6CkP,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe8B,QAAQ,GAAG,CAA1B,EAA6BA,QAAQ,GAAG,CAAxC,CAA7C,CAFA,EAE0F;AAC3F,mBAAK+L,YAAL,CAAkBpP,WAAlB,GACIuR,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe,CAAf,EAAkB8B,QAAQ,GAAG,CAA7B,IACIkO,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe8B,QAAf,EAAyBA,QAAQ,GAAG,CAApC,CADJ,GAEI8B,WAFJ,GAGIoM,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe8B,QAAQ,GAAG,CAA1B,CAJR;AAKAA,cAAAA,QAAQ,GAAGA,QAAQ,GAAG,CAAtB;AACH,aATI,MAUA,IAAIwO,YAAJ,EAAkB;AACnB,kBAAIN,EAAE,CAACnT,KAAH,CAASgD,MAAT,KAAoB,CAApB,IAAyBiC,QAAQ,KAAK,CAA1C,EAA6C;AACzC,qBAAK+L,YAAL,CAAkBpP,WAAlB,GACI,KAAKjD,MAAL,GACIoI,WADJ,GAEI,KAAKiK,YAAL,CAAkBpG,WAAlB,CAA8BzH,KAA9B,CAAoC,CAApC,EAAuC,KAAK6N,YAAL,CAAkBpG,WAAlB,CAA8B5H,MAArE,CAFJ,GAGI,KAAKtE,MAJb;AAKH,eAND,MAOK;AACD,qBAAKsS,YAAL,CAAkBpP,WAAlB,GACIuR,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe,CAAf,EAAkB8B,QAAQ,GAAG,CAA7B,IACI8B,WADJ,GAEIoM,EAAE,CAACnT,KAAH,CACKmD,KADL,CACW8B,QAAQ,GAAG,CADtB,EAEKpC,KAFL,CAEW,KAAKnE,MAFhB,EAGKkE,IAHL,CAGU,EAHV,CAFJ,GAMI,KAAKlE,MAPb;AAQH;AACJ,aAlBI,MAmBA,IAAI,KAAKC,MAAL,IACLwU,EAAE,CAACnT,KAAH,CAASgD,MAAT,KAAoB,CADf,IAELiC,QAAQ,GAAGuO,YAAX,KAA4B,CAFvB,IAGL,KAAKxC,YAAL,CAAkB/J,gBAAlB,CAAmCkM,EAAE,CAACnT,KAAtC,EAA6C,KAAKgR,YAAL,CAAkBrP,cAAlB,CAAiCsD,QAAQ,GAAG,CAAX,GAAeuO,YAAhD,KACzC;AAAG;AADP,aAHC,EAI2B;AAC5B,mBAAKxC,YAAL,CAAkBpP,WAAlB,GACI,KAAKjD,MAAL,GACIwU,EAAE,CAACnT,KADP,GAEI,KAAKgR,YAAL,CAAkBpG,WAAlB,CAA8BzH,KAA9B,CAAoC,CAApC,EAAuC,KAAK6N,YAAL,CAAkBpG,WAAlB,CAA8B5H,MAArE,CAFJ,GAGI,KAAKtE,MAJb;AAKH;AACJ;AACJ;;AACD,YAAIqV,UAAU,GAAG,CAAjB;AACA,YAAIvO,cAAc,GAAG,KAArB;;AACA,YAAI,KAAKoO,KAAL,KAAe;AAAS;AAAxB,WAAwC;AAAY;AAAxD,UAAyE;AACrE,eAAK5C,YAAL,CAAkBlP,uBAAlB,GAA4C,IAA5C;AACH;;AACD,YAAI,KAAKwR,WAAL,CAAiBtQ,MAAjB,IAA2B,KAAKgO,YAAL,CAAkBrP,cAAlB,CAAiCqB,MAAjC,GAA0C,CAArE,IACA,KAAK4Q,KAAL,KAAe;AAAY;AAD3B,WAEA,KAAK5C,YAAL,CAAkBrP,cAAlB,KAAqC;AAAa;AAFlD,WAGAsD,QAAQ,GAAG,EAHf,EAGmB;AACf,gBAAM8B,WAAW,GAAG,KAAKuM,WAAL,CAAiBnQ,KAAjB,CAAuB8B,QAAQ,GAAG,CAAlC,EAAqCA,QAArC,CAApB;;AACAkO,UAAAA,EAAE,CAACnT,KAAH,GACI,KAAKsT,WAAL,CAAiBnQ,KAAjB,CAAuB,CAAvB,EAA0B8B,QAAQ,GAAG,CAArC,IACI8B,WADJ,GAEI,KAAKuM,WAAL,CAAiBnQ,KAAjB,CAAuB8B,QAAQ,GAAG,CAAlC,CAHR;AAIH;;AACD,YAAI,KAAK+L,YAAL,CAAkBrP,cAAlB,KAAqC;AAAa;AAAlD,WACA,KAAKjC,gBADT,EAC2B;AACvB,cAAKuF,QAAQ,GAAG,CAAX,IAAgBxB,MAAM,CAAC0P,EAAE,CAACnT,KAAJ,CAAN,GAAmB,EAAnC,IAAyCyD,MAAM,CAAC0P,EAAE,CAACnT,KAAJ,CAAN,GAAmB,EAA7D,IACCiF,QAAQ,KAAK,CAAb,IAAkBxB,MAAM,CAAC0P,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe,CAAf,EAAkB,CAAlB,CAAD,CAAN,GAA+B,EADtD,EAC2D;AACvD8B,YAAAA,QAAQ,GAAGA,QAAQ,GAAG,CAAtB;AACH;AACJ;;AACD,YAAI,KAAK+L,YAAL,CAAkBrP,cAAlB,KAAqC;AAAW;AAAhD,WACA,KAAKhC,GADT,EACc;AACV,cAAI,KAAKyR,WAAL,IAAoB+B,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe,CAAf,EAAkB,CAAlB,MAAyB;AAAK;AAAtD,YAAyE;AACrEgQ,YAAAA,EAAE,CAACnT,KAAH,GAAWmT,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe,CAAf,EAAkB,CAAlB,IAAuBgQ,EAAE,CAACnT,KAAH,CAASmD,KAAT,CAAe,CAAf,EAAkBgQ,EAAE,CAACnT,KAAH,CAASgD,MAA3B,CAAlC;AACH;;AACDmQ,UAAAA,EAAE,CAACnT,KAAH,GACImT,EAAE,CAACnT,KAAH,KAAa;AAAK;AAAlB,YACM;AAAI;AADV,YAEMmT,EAAE,CAACnT,KAHb;AAIH;;AACD,aAAKgR,YAAL,CAAkBvE,iBAAlB,CAAoCxH,QAApC,EAA8C,KAAKmM,WAAnD,EAAgE,KAAKwC,KAAL,KAAe;AAAY;AAA3B,WAA8C,KAAKA,KAAL,KAAe;AAAS;AAAtI,UAAoJ,CAACnO,KAAD,EAAQuO,eAAR,KAA4B;AAC5K,eAAK5C,WAAL,GAAmB,KAAnB;AACA2C,UAAAA,UAAU,GAAGtO,KAAb;AACAD,UAAAA,cAAc,GAAGwO,eAAjB;AACH,SAJD,EArJ8E,CA0J9E;;;AACA,YAAI,KAAKpH,iBAAL,OAA6BuG,EAAjC,EAAqC;AACjC;AACH;;AACD,YAAI,KAAKnC,YAAL,CAAkBtP,eAAtB,EAAuC;AACnCuD,UAAAA,QAAQ,GAAGA,QAAQ,GAAG,CAAtB;AACA,eAAK+L,YAAL,CAAkBtP,eAAlB,GAAoC,KAApC;AACH,SAjK6E,CAkK9E;;;AACA,YAAI,KAAKuJ,oBAAL,CAA0BjI,MAA9B,EAAsC;AAClC,cAAI,KAAK4Q,KAAL,KAAe;AAAY;AAA/B,YAAgD;AAC5C3O,YAAAA,QAAQ,GAAG,KAAKxF,iBAAL,CAAuBwE,QAAvB,CAAgC,KAAKqP,WAAL,CAAiBnQ,KAAjB,CAAuB8B,QAAQ,GAAG,CAAlC,EAAqCA,QAArC,CAAhC,IACLA,QAAQ,GAAG,CADN,GAELA,QAFN;AAGH,WAJD,MAKK;AACDA,YAAAA,QAAQ,GACJkO,EAAE,CAACI,cAAH,KAAsB,CAAtB,GACMJ,EAAE,CAACI,cAAH,GAAoB,KAAKvC,YAAL,CAAkBrS,MAAlB,CAAyBqE,MADnD,GAEMmQ,EAAE,CAACI,cAHb;AAIH;AACJ;;AACD,aAAKpC,SAAL,GACI,KAAKA,SAAL,KAAmB,CAAnB,IAAwB,KAAKmC,WAAL,CAAiBtQ,MAAjB,KAA4B,CAApD,GAAwD,IAAxD,GAA+D,KAAKmO,SADxE;AAEA,YAAI8C,eAAe,GAAG,KAAK9C,SAAL,GAChB,KAAKmC,WAAL,CAAiBtQ,MAAjB,GAA0BiC,QAA1B,GAAqC8O,UADrB,GAEhB9O,QAAQ,IACL,KAAK2O,KAAL,KAAe;AAAY;AAA3B,WAA8C,CAACpO,cAA/C,GAAgE,CAAhE,GAAoEuO,UAD/D,CAFd;;AAIA,YAAIE,eAAe,GAAG,KAAKC,qBAAL,EAAtB,EAAoD;AAChDD,UAAAA,eAAe,GACXd,EAAE,CAACnT,KAAH,KAAa,KAAKgR,YAAL,CAAkBnS,aAA/B,IAAgDsU,EAAE,CAACnT,KAAH,CAASgD,MAAT,KAAoB,CAApE,GACM,KAAKkR,qBAAL,KAA+B,CADrC,GAEM,KAAKA,qBAAL,EAHV;AAIH;;AACD,YAAID,eAAe,GAAG,CAAtB,EAAyB;AACrBA,UAAAA,eAAe,GAAG,CAAlB;AACH;;AACDd,QAAAA,EAAE,CAACgB,iBAAH,CAAqBF,eAArB,EAAsCA,eAAtC;AACA,aAAK9C,SAAL,GAAiB,IAAjB;AACH,OAjMD,MAkMK;AACDiD,QAAAA,OAAO,CAACC,IAAR,CAAa,oEAAb,EAAmF,OAAOhB,gBAA1F;AACH;AACJ,KAtMD,MAuMK;AACD,UAAI,CAAC,KAAKpC,UAAV,EAAsB;AAClB,aAAK5F,QAAL,CAAc8H,EAAE,CAACnT,KAAjB;AACA;AACH;;AACD,WAAKgR,YAAL,CAAkBvE,iBAAlB,CAAoC0G,EAAE,CAACnT,KAAH,CAASgD,MAA7C,EAAqD,KAAKoO,WAA1D,EAAuE,KAAKwC,KAAL,KAAe;AAAY;AAA3B,SAA8C,KAAKA,KAAL,KAAe;AAAS;AAA7I;AACH;AACJ,GAhfkB,CAifnB;;;AACAU,EAAAA,kBAAkB,GAAG;AACjB,SAAKjD,YAAL,GAAoB,IAApB;AACH,GApfkB,CAqfnB;;;AACAkD,EAAAA,gBAAgB,CAACrB,CAAD,EAAI;AAChB,SAAK7B,YAAL,GAAoB,KAApB;AACA,SAAKD,WAAL,GAAmB,IAAnB;AACA,SAAK6B,OAAL,CAAaC,CAAb;AACH;;AACDsB,EAAAA,MAAM,CAACtB,CAAD,EAAI;AACN,QAAI,KAAKjC,UAAT,EAAqB;AACjB,YAAMkC,EAAE,GAAGD,CAAC,CAACE,MAAb;;AACA,UAAI,KAAKxT,QAAL,IAAiBuT,EAAE,CAACnT,KAAH,CAASgD,MAAT,GAAkB,CAAnC,IAAwC,OAAO,KAAKnE,aAAZ,KAA8B,QAA1E,EAAoF;AAChF,cAAM8C,cAAc,GAAG,KAAKqP,YAAL,CAAkBrP,cAAzC;AACA,cAAMQ,SAAS,GAAGsB,MAAM,CAAC,KAAKuN,YAAL,CAAkBrP,cAAlB,CAAiCwB,KAAjC,CAAuCxB,cAAc,CAACqB,MAAf,GAAwB,CAA/D,EAAkErB,cAAc,CAACqB,MAAjF,CAAD,CAAxB;;AACA,YAAIb,SAAS,GAAG,CAAhB,EAAmB;AACfgR,UAAAA,EAAE,CAACnT,KAAH,GAAW,KAAKtB,MAAL,GAAcyU,EAAE,CAACnT,KAAH,CAAS6C,KAAT,CAAe,KAAKnE,MAApB,EAA4BkE,IAA5B,CAAiC,EAAjC,CAAd,GAAqDuQ,EAAE,CAACnT,KAAnE;AACA,gBAAM+J,WAAW,GAAGoJ,EAAE,CAACnT,KAAH,CAAS6C,KAAT,CAAe,KAAKhE,aAApB,EAAmC,CAAnC,CAApB;AACAsU,UAAAA,EAAE,CAACnT,KAAH,GAAWmT,EAAE,CAACnT,KAAH,CAASiE,QAAT,CAAkB,KAAKpF,aAAvB,IACLsU,EAAE,CAACnT,KAAH,GACE;AAAI;AAAJ,WAAsByU,MAAtB,CAA6BtS,SAAS,GAAG4H,WAAW,CAAC/G,MAArD,CADF,GAEE,KAAKtE,MAHF,GAILyU,EAAE,CAACnT,KAAH,GACE,KAAKnB,aADP,GAEE;AAAI;AAAJ,WAAsB4V,MAAtB,CAA6BtS,SAA7B,CAFF,GAGE,KAAKzD,MAPb;AAQA,eAAKsS,YAAL,CAAkBpP,WAAlB,GAAgCuR,EAAE,CAACnT,KAAnC;AACH;AACJ;;AACD,WAAKgR,YAAL,CAAkBnE,iBAAlB;AACH;;AACD,SAAKqE,UAAL,GAAkB,KAAlB;AACA,SAAKI,OAAL;AACH;;AACDoD,EAAAA,OAAO,CAACxB,CAAD,EAAI;AACP,QAAI,CAAC,KAAKjC,UAAV,EAAsB;AAClB;AACH;;AACD,UAAMkC,EAAE,GAAGD,CAAC,CAACE,MAAb;AACA,UAAMuB,QAAQ,GAAG,CAAjB;AACA,UAAMC,MAAM,GAAG,CAAf;;AACA,QAAIzB,EAAE,KAAK,IAAP,IACAA,EAAE,CAACI,cAAH,KAAsB,IADtB,IAEAJ,EAAE,CAACI,cAAH,KAAsBJ,EAAE,CAAC0B,YAFzB,IAGA1B,EAAE,CAACI,cAAH,GAAoB,KAAKvC,YAAL,CAAkBrS,MAAlB,CAAyBqE,MAH7C,IAIA;AACAkQ,IAAAA,CAAC,CAAC4B,OAAF,KAAc,EALlB,EAKsB;AAClB,UAAI,KAAK9D,YAAL,CAAkBhS,aAAlB,IAAmC,CAAC,KAAKa,sBAA7C,EAAqE;AACjE;AACA,aAAKmR,YAAL,CAAkBpG,WAAlB,GAAgC,KAAKoG,YAAL,CAAkBzF,eAAlB,EAAhC;;AACA,YAAI4H,EAAE,CAACgB,iBAAH,IACA,KAAKnD,YAAL,CAAkBrS,MAAlB,GAA2B,KAAKqS,YAAL,CAAkBpG,WAA7C,KAA6DuI,EAAE,CAACnT,KADpE,EAC2E;AACvE;AACAmT,UAAAA,EAAE,CAAC4B,KAAH;AACA5B,UAAAA,EAAE,CAACgB,iBAAH,CAAqBQ,QAArB,EAA+BC,MAA/B;AACH,SALD,MAMK;AACD;AACA,cAAIzB,EAAE,CAACI,cAAH,GAAoB,KAAKvC,YAAL,CAAkBpP,WAAlB,CAA8BoB,MAAtD,EAA8D;AAC1D;AACAmQ,YAAAA,EAAE,CAACgB,iBAAH,CAAqB,KAAKnD,YAAL,CAAkBpP,WAAlB,CAA8BoB,MAAnD,EAA2D,KAAKgO,YAAL,CAAkBpP,WAAlB,CAA8BoB,MAAzF;AACH;AACJ;AACJ;AACJ;;AACD,UAAMgS,SAAS,GAAG7B,EAAE,KACfA,EAAE,CAACnT,KAAH,KAAa,KAAKgR,YAAL,CAAkBrS,MAA/B,GACK,KAAKqS,YAAL,CAAkBrS,MAAlB,GAA2B,KAAKqS,YAAL,CAAkBpG,WADlD,GAEKuI,EAAE,CAACnT,KAHO,CAApB;AAIA;;AACA,QAAImT,EAAE,IAAIA,EAAE,CAACnT,KAAH,KAAagV,SAAvB,EAAkC;AAC9B7B,MAAAA,EAAE,CAACnT,KAAH,GAAWgV,SAAX;AACH;AACD;;;AACA,QAAI7B,EAAE,IACFA,EAAE,CAAC/I,IAAH,KAAY,QADZ,IAEA,CAAC+I,EAAE,CAACI,cAAH,IAAqBJ,EAAE,CAAC0B,YAAzB,KACI,KAAK7D,YAAL,CAAkBrS,MAAlB,CAAyBqE,MAHjC,EAGyC;AACrCmQ,MAAAA,EAAE,CAACI,cAAH,GAAoB,KAAKvC,YAAL,CAAkBrS,MAAlB,CAAyBqE,MAA7C;AACA;AACH;AACD;;;AACA,QAAImQ,EAAE,IAAIA,EAAE,CAAC0B,YAAH,GAAkB,KAAKX,qBAAL,EAA5B,EAA0D;AACtDf,MAAAA,EAAE,CAAC0B,YAAH,GAAkB,KAAKX,qBAAL,EAAlB;AACH;AACJ,GAvkBkB,CAwkBnB;;;AACAe,EAAAA,SAAS,CAAC/B,CAAD,EAAI;AACT,QAAI,CAAC,KAAKjC,UAAV,EAAsB;AAClB;AACH;;AACD,QAAI,KAAKI,YAAT,EAAuB;AACnB;AACA,UAAI6B,CAAC,CAACvD,GAAF,KAAU,OAAd,EACI,KAAK4E,gBAAL,CAAsBrB,CAAtB;AACJ;AACH;;AACD,SAAKU,KAAL,GAAaV,CAAC,CAACgC,IAAF,GAAShC,CAAC,CAACgC,IAAX,GAAkBhC,CAAC,CAACvD,GAAjC;AACA,UAAMwD,EAAE,GAAGD,CAAC,CAACE,MAAb;AACA,SAAKE,WAAL,GAAmBH,EAAE,CAACnT,KAAtB;;AACA,SAAK+R,QAAL;;AACA,QAAIoB,EAAE,CAAC/I,IAAH,KAAY,QAAhB,EAA0B;AACtB,UAAI8I,CAAC,CAACvD,GAAF,KAAU;AAAU;AAAxB,QAAwC;AACpCuD,QAAAA,CAAC,CAACiC,cAAF;AACH;;AACD,UAAIjC,CAAC,CAACvD,GAAF,KAAU;AAAY;AAAtB,SACAuD,CAAC,CAACvD,GAAF,KAAU;AAAY;AADtB,SAEAuD,CAAC,CAACvD,GAAF,KAAU;AAAS;AAFvB,QAEqC;AACjC,YAAIuD,CAAC,CAACvD,GAAF,KAAU;AAAY;AAAtB,WAAyCwD,EAAE,CAACnT,KAAH,CAASgD,MAAT,KAAoB,CAAjE,EAAoE;AAChEmQ,UAAAA,EAAE,CAACI,cAAH,GAAoBJ,EAAE,CAAC0B,YAAvB;AACH;;AACD,YAAI3B,CAAC,CAACvD,GAAF,KAAU;AAAY;AAAtB,WAAyCwD,EAAE,CAACI,cAAH,KAAsB,CAAnE,EAAsE;AAClE;AACA,eAAK9T,iBAAL,GAAyB,KAAKA,iBAAL,EAAwBuD,MAAxB,GACnB,KAAKvD,iBADc,GAEnB,KAAK8B,OAAL,CAAa9B,iBAFnB;;AAGA,cAAI,KAAKd,MAAL,CAAYqE,MAAZ,GAAqB,CAArB,IACAmQ,EAAE,CAACI,cAAH,IAAqB,KAAK5U,MAAL,CAAYqE,MADrC,EAC6C;AACzCmQ,YAAAA,EAAE,CAACgB,iBAAH,CAAqB,KAAKxV,MAAL,CAAYqE,MAAjC,EAAyCmQ,EAAE,CAAC0B,YAA5C;AACH,WAHD,MAIK;AACD,gBAAI,KAAKvB,WAAL,CAAiBtQ,MAAjB,KAA4BmQ,EAAE,CAACI,cAA/B,IACAJ,EAAE,CAACI,cAAH,KAAsB,CAD1B,EAC6B;AACzB,qBAAO,KAAK9T,iBAAL,CAAuBwE,QAAvB,CAAgC,CAAC,KAAKqP,WAAL,CAAiBH,EAAE,CAACI,cAAH,GAAoB,CAArC,KACpC;AAAG;AADgC,gBACZ3N,QADY,EAAhC,MAED,KAAKjH,MAAL,CAAYqE,MAAZ,IAAsB,CAAtB,IACEmQ,EAAE,CAACI,cAAH,GAAoB,KAAK5U,MAAL,CAAYqE,MADnC,IAEG,KAAKrE,MAAL,CAAYqE,MAAZ,KAAuB,CAJxB,CAAP,EAImC;AAC/BmQ,gBAAAA,EAAE,CAACgB,iBAAH,CAAqBhB,EAAE,CAACI,cAAH,GAAoB,CAAzC,EAA4CJ,EAAE,CAAC0B,YAA/C;AACH;AACJ;AACJ;AACJ;;AACD,aAAKO,wBAAL,CAA8BjC,EAA9B;;AACA,YAAI,KAAKnC,YAAL,CAAkBrS,MAAlB,CAAyBqE,MAAzB,IACAmQ,EAAE,CAACI,cAAH,IAAqB,KAAKvC,YAAL,CAAkBrS,MAAlB,CAAyBqE,MAD9C,IAEAmQ,EAAE,CAAC0B,YAAH,IAAmB,KAAK7D,YAAL,CAAkBrS,MAAlB,CAAyBqE,MAFhD,EAEwD;AACpDkQ,UAAAA,CAAC,CAACiC,cAAF;AACH;;AACD,cAAME,WAAW,GAAGlC,EAAE,CAACI,cAAvB;;AACA,YAAIL,CAAC,CAACvD,GAAF,KAAU;AAAY;AAAtB,WACA,CAACwD,EAAE,CAACmC,QADJ,IAEAD,WAAW,KAAK,CAFhB,IAGAlC,EAAE,CAAC0B,YAAH,KAAoB1B,EAAE,CAACnT,KAAH,CAASgD,MAH7B,IAIAmQ,EAAE,CAACnT,KAAH,CAASgD,MAAT,KAAoB,CAJxB,EAI2B;AACvB,eAAKmO,SAAL,GAAiB,KAAKH,YAAL,CAAkBrS,MAAlB,GAA2B,KAAKqS,YAAL,CAAkBrS,MAAlB,CAAyBqE,MAApD,GAA6D,CAA9E;;AACA,eAAKgO,YAAL,CAAkBhM,SAAlB,CAA4B,KAAKgM,YAAL,CAAkBrS,MAA9C,EAAsD,KAAKqS,YAAL,CAAkBrP,cAAxE,EAAwF,KAAKwP,SAA7F;AACH;AACJ;;AACD,UAAI,CAAC,CAAC,KAAKzS,MAAP,IACA,KAAKA,MAAL,CAAYsE,MAAZ,GAAqB,CADrB,IAEA,KAAKsQ,WAAL,CAAiBtQ,MAAjB,GAA0B,KAAKtE,MAAL,CAAYsE,MAAtC,GAA+CmQ,EAAE,CAACI,cAFtD,EAEsE;AAClEJ,QAAAA,EAAE,CAACgB,iBAAH,CAAqB,KAAKb,WAAL,CAAiBtQ,MAAjB,GAA0B,KAAKtE,MAAL,CAAYsE,MAA3D,EAAmE,KAAKsQ,WAAL,CAAiBtQ,MAApF;AACH,OAJD,MAKK,IAAKkQ,CAAC,CAACgC,IAAF,KAAW,MAAX,IAAqBhC,CAAC,CAACqC,OAAxB,IACJrC,CAAC,CAACgC,IAAF,KAAW,MAAX,IAAqBhC,CAAC,CAACsC,OADvB,CACgC;AADhC,QAEH;AACErC,QAAAA,EAAE,CAACgB,iBAAH,CAAqB,CAArB,EAAwB,KAAKD,qBAAL,EAAxB;AACAhB,QAAAA,CAAC,CAACiC,cAAF;AACH;;AACD,WAAKnE,YAAL,CAAkBnG,QAAlB,GAA6BsI,EAAE,CAACI,cAAhC;AACA,WAAKvC,YAAL,CAAkBlG,MAAlB,GAA2BqI,EAAE,CAAC0B,YAA9B;AACH;AACJ;AACD;;;AACMY,EAAAA,UAAU,CAACC,YAAD,EAAe;AAAA;;AAAA;AAC3B,UAAI,OAAOA,YAAP,KAAwB,QAAxB,IAAoCA,YAAY,KAAK,IAArD,IAA6D,WAAWA,YAA5E,EAA0F;AACtF,YAAI,aAAaA,YAAjB,EAA+B;AAC3B,UAAA,KAAI,CAACC,gBAAL,CAAsB5J,OAAO,CAAC2J,YAAY,CAACE,OAAd,CAA7B;AACH,SAHqF,CAItF;;;AACAF,QAAAA,YAAY,GAAGA,YAAY,CAAC1V,KAA5B;AACH;;AACD,UAAI0V,YAAY,KAAK,IAArB,EAA2B;AACvB;AACAA,QAAAA,YAAY,GAAG,KAAI,CAAC3V,gBAAL,GACT,KAAI,CAACA,gBAAL,CAAsB2V,YAAtB,CADS,GAETA,YAFN;AAGH;;AACD,UAAI,OAAOA,YAAP,KAAwB,QAAxB,IACA,OAAOA,YAAP,KAAwB,QADxB,IAEAA,YAAY,KAAK,IAFjB,IAGAA,YAAY,KAAKtW,SAHrB,EAGgC;AAC5B,YAAIsW,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAKtW,SAA1C,IAAuDsW,YAAY,KAAK,EAA5E,EAAgF;AAC5E,UAAA,KAAI,CAAC1E,YAAL,CAAkB5F,aAAlB,GAAkC,EAAlC;AACA,UAAA,KAAI,CAAC4F,YAAL,CAAkB7F,cAAlB,GAAmC,EAAnC;AACH,SAJ2B,CAK5B;;;AACA,YAAIrH,UAAU,GAAG4R,YAAjB;;AACA,YAAI,OAAO5R,UAAP,KAAsB,QAAtB,IACA,KAAI,CAACmN,UAAL,CAAgB9K,UAAhB,CAA2B;AAAY;AAAvC,SADJ,EAC6D;AACzD;AACArC,UAAAA,UAAU,GAAGuJ,MAAM,CAACvJ,UAAD,CAAnB;;AACA,gBAAM+R,mBAAmB,GAAG,KAAI,CAAC7E,YAAL,CAAkBJ,0BAAlB,EAA5B;;AACA,cAAI,CAACtO,KAAK,CAACC,OAAN,CAAc,KAAI,CAACyO,YAAL,CAAkBnS,aAAhC,CAAL,EAAqD;AACjD;AACAiF,YAAAA,UAAU,GACN,KAAI,CAACkN,YAAL,CAAkBnS,aAAlB,KAAoCgX,mBAApC,GACM/R,UAAU,CAACZ,OAAX,CAAmB2S,mBAAnB,EAAwC,KAAI,CAAC7E,YAAL,CAAkBnS,aAA1D,CADN,GAEMiF,UAHV;AAIH;;AACD,cAAI,KAAI,CAACkN,YAAL,CAAkBpR,QAAlB,IACAkE,UADA,IAEA,KAAI,CAACnC,cAFL,IAGA,KAAI,CAACzC,qBAAL,KAA+B,KAHnC,EAG0C;AACtC;AACA4E,YAAAA,UAAU,GAAG,KAAI,CAACkN,YAAL,CAAkBtB,eAAlB,CAAkC,KAAI,CAACsB,YAAL,CAAkBrP,cAApD,EAAoEmC,UAApE,CAAb;AACH;;AACD,cAAI,KAAI,CAACkN,YAAL,CAAkBnS,aAAlB,KAAoC;AAAI;AAA5C,YAAyD;AACrD;AACAiF,YAAAA,UAAU,GAAGA,UAAU,CAClB8B,QADQ,GAER1C,OAFQ,CAEA;AAAI;AAFJ,cAEe;AAAI;AAFnB,aAAb;AAGH;;AACD,cAAI,KAAI,CAACvB,cAAL,EAAqBwE,UAArB,CAAgC;AAAY;AAA5C,eAAgE,KAAI,CAACvG,QAAzE,EAAmF;AAC/EqS,YAAAA,qBAAqB,CAAC,MAAM;AACxB,cAAA,KAAI,CAACjB,YAAL,CAAkBhM,SAAlB,CAA4BlB,UAAU,EAAE8B,QAAZ,MAA0B,EAAtD,EAA0D,KAAI,CAACoL,YAAL,CAAkBrP,cAA5E;AACH,aAFoB,CAArB;AAGH;;AACD,UAAA,KAAI,CAACqP,YAAL,CAAkBrG,aAAlB,GAAkC,IAAlC;AACH;;AACD,YAAI,OAAO7G,UAAP,KAAsB,QAA1B,EAAoC;AAChC;AACAA,UAAAA,UAAU,GAAG,EAAb;AACH;;AACD,QAAA,KAAI,CAACwP,WAAL,GAAmBxP,UAAnB;;AACA,QAAA,KAAI,CAACiO,QAAL;;AACA,YAAKjO,UAAU,IAAI,KAAI,CAACkN,YAAL,CAAkBrP,cAAjC,IACC,KAAI,CAACqP,YAAL,CAAkBrP,cAAlB,KACI,KAAI,CAACqP,YAAL,CAAkBrS,MAAlB,IAA4B,KAAI,CAACqS,YAAL,CAAkBhS,aADlD,CADL,EAEwE;AACpE;AACA,iBAAO,KAAI,CAACe,gBAAZ,KAAiC,UAAjC,GACO,KAAI,CAACiR,YAAL,CAAkBjG,YAAlB,GAAiC,IADxC,GAEM,EAFN;AAGA,UAAA,KAAI,CAACiG,YAAL,CAAkBnD,mBAAlB,GAAwC,CACpC,OADoC,EAEpC,KAAI,CAACmD,YAAL,CAAkBhM,SAAlB,CAA4BlB,UAA5B,EAAwC,KAAI,CAACkN,YAAL,CAAkBrP,cAA1D,CAFoC,CAAxC,CALoE,CASpE;;AACA,iBAAO,KAAI,CAAC5B,gBAAZ,KAAiC,UAAjC,GACO,KAAI,CAACiR,YAAL,CAAkBjG,YAAlB,GAAiC,KADxC,GAEM,EAFN;AAGH,SAfD,MAgBK;AACD,UAAA,KAAI,CAACiG,YAAL,CAAkBnD,mBAAlB,GAAwC,CAAC,OAAD,EAAU/J,UAAV,CAAxC;AACH;;AACD,QAAA,KAAI,CAACwP,WAAL,GAAmBxP,UAAnB;AACH,OApED,MAqEK;AACDsQ,QAAAA,OAAO,CAACC,IAAR,CAAa,oEAAb,EAAmF,OAAOqB,YAA1F;AACH;AArF0B;AAsF9B;;AACDI,EAAAA,gBAAgB,CAACC,EAAD,EAAK;AACjB,SAAK/E,YAAL,CAAkB3F,QAAlB,GAA6B,KAAKA,QAAL,GAAgB0K,EAA7C;AACH;;AACDC,EAAAA,iBAAiB,CAACD,EAAD,EAAK;AAClB,SAAKzE,OAAL,GAAeyE,EAAf;AACH;;AACDnJ,EAAAA,iBAAiB,CAACpC,QAAQ,GAAG,KAAKA,QAAjB,EAA2B;AACxC,UAAMmE,YAAY,GAAGnE,QAAQ,EAAEoE,aAAV,EAAyBC,UAA9C;;AACA,QAAI,CAACF,YAAY,EAAEC,aAAnB,EAAkC;AAC9B,aAAOpE,QAAQ,CAACoE,aAAhB;AACH,KAFD,MAGK;AACD,aAAO,KAAKhC,iBAAL,CAAuB+B,YAAvB,CAAP;AACH;AACJ;;AACDyG,EAAAA,wBAAwB,CAACjC,EAAD,EAAK;AACzBA,IAAAA,EAAE,CAACI,cAAH,GAAoB0C,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAAS,KAAKxX,MAAL,CAAYqE,MAArB,EAA6BmQ,EAAE,CAACI,cAAhC,CAAT,EAA0D,KAAKD,WAAL,CAAiBtQ,MAAjB,GAA0B,KAAKtE,MAAL,CAAYsE,MAAhG,CAApB;AACAmQ,IAAAA,EAAE,CAAC0B,YAAH,GAAkBoB,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAAS,KAAKxX,MAAL,CAAYqE,MAArB,EAA6BmQ,EAAE,CAAC0B,YAAhC,CAAT,EAAwD,KAAKvB,WAAL,CAAiBtQ,MAAjB,GAA0B,KAAKtE,MAAL,CAAYsE,MAA9F,CAAlB;AACH;AACD;;;AACA2S,EAAAA,gBAAgB,CAACS,UAAD,EAAa;AACzB,SAAKpF,YAAL,CAAkBnD,mBAAlB,GAAwC,CAAC,UAAD,EAAauI,UAAb,CAAxC;AACH,GApwBkB,CAqwBnB;;;AACAjE,EAAAA,UAAU,GAAG;AACT,SAAKnB,YAAL,CAAkBrP,cAAlB,GAAmC,KAAKqP,YAAL,CAAkBd,qBAAlB,CAAwC,KAAKe,UAAL,IAAmB,EAA3D,CAAnC;AACA,SAAKD,YAAL,CAAkBnD,mBAAlB,GAAwC,CACpC,OADoC,EAEpC,KAAKmD,YAAL,CAAkBhM,SAAlB,CAA4B,KAAKsO,WAAjC,EAA8C,KAAKtC,YAAL,CAAkBrP,cAAhE,CAFoC,CAAxC;AAIH;;AACD2Q,EAAAA,aAAa,CAACtS,KAAD,EAAQ;AACjB,UAAMqW,UAAU,GAAG,KAAKpF,UAAL,CACdpO,KADc,CACR;AAAG;AADK,MAEdqG,MAFc,CAENhI,CAAD,IAAOA,CAAC,KAAK,GAFN,EAEW8B,MAF9B;;AAGA,QAAI,CAAChD,KAAL,EAAY;AACR,aAAO,IAAP,CADQ,CACK;AAChB;;AACD,QAAK,EAAEA,KAAK,CAACA,KAAK,CAACgD,MAAN,GAAe,CAAhB,CAAL,IAA2B,CAAC,CAA9B,MAAqC,CAArC,IAA0ChD,KAAK,CAACgD,MAAN,GAAeqT,UAA1D,IACArW,KAAK,CAACgD,MAAN,IAAgBqT,UAAU,GAAG,CADjC,EACoC;AAChC,aAAO,KAAKhE,sBAAL,CAA4BrS,KAA5B,CAAP;AACH;;AACD,WAAO,IAAP;AACH;;AACDkU,EAAAA,qBAAqB,GAAG;AACpB,WAAQ,KAAKlD,YAAL,CAAkBpP,WAAlB,CAA8BoB,MAA9B,IACJ,KAAKgO,YAAL,CAAkBpP,WAAlB,CAA8BoB,MAA9B,GAAuC,KAAKgO,YAAL,CAAkBrS,MAAlB,CAAyBqE,MADpE;AAEH;;AACDqP,EAAAA,sBAAsB,CAACzQ,WAAD,EAAc;AAChC,WAAO;AACHkD,MAAAA,IAAI,EAAE;AACFwR,QAAAA,YAAY,EAAE,KAAKrF,UADjB;AAEFrP,QAAAA;AAFE;AADH,KAAP;AAMH;;AACDmQ,EAAAA,QAAQ,GAAG;AACP,SAAK9G,oBAAL,CAA0BxB,IAA1B,CAAgC3E,IAAD,IAAU;AACrC,YAAMoD,YAAY,GAAGpD,IAAI,CACpBjC,KADgB,CACV;AAAG;AADO,QAEhB4G,IAFgB,CAEVX,IAAD,IAAU,KAAKkI,YAAL,CAAkBvR,iBAAlB,CAAoCwE,QAApC,CAA6C6E,IAA7C,CAFC,CAArB;;AAGA,UAAKZ,YAAY,IAAI,KAAKoL,WAArB,IAAoC,CAACxO,IAAI,CAACb,QAAL,CAAc;AAAI;AAAlB,OAAtC,IACAa,IAAI,CAACb,QAAL,CAAc;AAAI;AAAlB,OADJ,EACkD;AAC9C,cAAMZ,IAAI,GAAG,KAAK2N,YAAL,CAAkBnF,UAAlB,CAA6B,KAAKyH,WAAlC,GAAgDtQ,MAAhD,IACT,KAAKgO,YAAL,CAAkBnF,UAAlB,CAA6B/G,IAA7B,GAAoC9B,MADxC;;AAEA,YAAIK,IAAJ,EAAU;AACN,eAAK4N,UAAL,GACI,KAAKtP,cAAL,GACI,KAAKqP,YAAL,CAAkBrP,cAAlB,GACImD,IAAI,CAACb,QAAL,CAAc;AAAI;AAAlB,cACM,KAAK+M,YAAL,CAAkBd,qBAAlB,CAAwCpL,IAAxC,CADN,GAEMA,IALlB;AAMA,iBAAOzB,IAAP;AACH,SARD,MASK;AACD,gBAAMkT,UAAU,GAAG,KAAKtL,oBAAL,CAA0B,KAAKA,oBAAL,CAA0BjI,MAA1B,GAAmC,CAA7D,KACf;AAAG;AADP;AAEA,eAAKiO,UAAL,GACI,KAAKtP,cAAL,GACI,KAAKqP,YAAL,CAAkBrP,cAAlB,GACI4U,UAAU,CAACtS,QAAX,CAAoB;AAAI;AAAxB,cACM,KAAK+M,YAAL,CAAkBd,qBAAlB,CAAwCqG,UAAxC,CADN,GAEMA,UALlB;AAMH;AACJ,OAvBD,MAwBK;AACD,cAAMC,KAAK,GAAG,KAAKxF,YAAL,CACTnF,UADS,CACE,KAAKyH,WADP,GAERzQ,KAFQ,CAEF;AAAG;AAFD,UAGTgG,KAHS,CAGH,CAAC4N,SAAD,EAAY/M,KAAZ,KAAsB;AAC7B,gBAAMgN,SAAS,GAAG5R,IAAI,CAAC6R,MAAL,CAAYjN,KAAZ,CAAlB;AACA,iBAAO,KAAKsH,YAAL,CAAkB/J,gBAAlB,CAAmCwP,SAAnC,EAA8CC,SAA9C,CAAP;AACH,SANa,CAAd;;AAOA,YAAIF,KAAJ,EAAW;AACP,eAAKvF,UAAL,GAAkB,KAAKtP,cAAL,GAAsB,KAAKqP,YAAL,CAAkBrP,cAAlB,GAAmCmD,IAA3E;AACA,iBAAO0R,KAAP;AACH;AACJ;AACJ,KAzCD;AA0CH;;AAj1BkB;;AAm1BvBzF,gBAAgB,CAAC7G,IAAjB;AAAA,mBAA8G6G,gBAA9G,EAz7CyGzT,EAy7CzG,mBAAgJa,QAAhJ,GAz7CyGb,EAy7CzG,mBAAqKgB,eAArK,GAz7CyGhB,EAy7CzG,mBAAiMiN,cAAjM;AAAA;;AACAwG,gBAAgB,CAAC6F,IAAjB,kBA17CyGtZ,EA07CzG;AAAA,QAAkGyT,gBAAlG;AAAA;AAAA;AAAA;AA17CyGzT,MAAAA,EA07CzG;AAAA,eAAkG,aAAlG;AAAA;AAAA,eAAkG,mBAAlG;AAAA;AAAA,eAAkG,yBAAlG;AAAA;AAAA,eAAkG,mBAAlG;AAAA;AAAA,eAAkG,8BAAlG;AAAA;AAAA,eAAkG,4BAAlG;AAAA;AAAA,eAAkG,kBAAlG;AAAA;AAAA,eAAkG,mBAAlG;AAAA;AAAA,eAAkG,qBAAlG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA17CyGA,EA07CzG,oBAA81C,CACt1C;AACIuZ,IAAAA,OAAO,EAAEzY,iBADb;AAEI0Y,IAAAA,WAAW,EAAElZ,UAAU,CAAC,MAAMmT,gBAAP,CAF3B;AAGIxL,IAAAA,KAAK,EAAE;AAHX,GADs1C,EAMt1C;AACIsR,IAAAA,OAAO,EAAExY,aADb;AAEIyY,IAAAA,WAAW,EAAElZ,UAAU,CAAC,MAAMmT,gBAAP,CAF3B;AAGIxL,IAAAA,KAAK,EAAE;AAHX,GANs1C,EAWt1CgF,cAXs1C,CAA91C,GA17CyGjN,EA07CzG;AAAA;;AAaA;AAAA,qDAv8CyGA,EAu8CzG,mBAA4FyT,gBAA5F,EAA0H,CAAC;AAC/G3G,IAAAA,IAAI,EAAEvM,SADyG;AAE/GyM,IAAAA,IAAI,EAAE,CAAC;AACCyM,MAAAA,QAAQ,EAAE,6BADX;AAECC,MAAAA,SAAS,EAAE,CACP;AACIH,QAAAA,OAAO,EAAEzY,iBADb;AAEI0Y,QAAAA,WAAW,EAAElZ,UAAU,CAAC,MAAMmT,gBAAP,CAF3B;AAGIxL,QAAAA,KAAK,EAAE;AAHX,OADO,EAMP;AACIsR,QAAAA,OAAO,EAAExY,aADb;AAEIyY,QAAAA,WAAW,EAAElZ,UAAU,CAAC,MAAMmT,gBAAP,CAF3B;AAGIxL,QAAAA,KAAK,EAAE;AAHX,OANO,EAWPgF,cAXO,CAFZ;AAeC0M,MAAAA,QAAQ,EAAE;AAfX,KAAD;AAFyG,GAAD,CAA1H,EAmB4B,YAAY;AAAE,WAAO,CAAC;AAAE7M,MAAAA,IAAI,EAAEhL,SAAR;AAAmBiL,MAAAA,UAAU,EAAE,CAAC;AAC9DD,QAAAA,IAAI,EAAE1M,MADwD;AAE9D4M,QAAAA,IAAI,EAAE,CAACnM,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEiM,MAAAA,IAAI,EAAEhL,SAAR;AAAmBiL,MAAAA,UAAU,EAAE,CAAC;AAClCD,QAAAA,IAAI,EAAE1M,MAD4B;AAElC4M,QAAAA,IAAI,EAAE,CAAChM,eAAD;AAF4B,OAAD;AAA/B,KAH2B,EAM3B;AAAE8L,MAAAA,IAAI,EAAEG;AAAR,KAN2B,CAAP;AAMQ,GAzBlD,EAyBoE;AAAE5I,IAAAA,cAAc,EAAE,CAAC;AACvEyI,MAAAA,IAAI,EAAEtM,KADiE;AAEvEwM,MAAAA,IAAI,EAAE,CAAC,MAAD;AAFiE,KAAD,CAAlB;AAGpD7K,IAAAA,iBAAiB,EAAE,CAAC;AACpB2K,MAAAA,IAAI,EAAEtM;AADc,KAAD,CAHiC;AAKpDqC,IAAAA,QAAQ,EAAE,CAAC;AACXiK,MAAAA,IAAI,EAAEtM;AADK,KAAD,CAL0C;AAOpDa,IAAAA,MAAM,EAAE,CAAC;AACTyL,MAAAA,IAAI,EAAEtM;AADG,KAAD,CAP4C;AASpDY,IAAAA,MAAM,EAAE,CAAC;AACT0L,MAAAA,IAAI,EAAEtM;AADG,KAAD,CAT4C;AAWpDc,IAAAA,iBAAiB,EAAE,CAAC;AACpBwL,MAAAA,IAAI,EAAEtM;AADc,KAAD,CAXiC;AAapDe,IAAAA,aAAa,EAAE,CAAC;AAChBuL,MAAAA,IAAI,EAAEtM;AADU,KAAD,CAbqC;AAepDoB,IAAAA,qBAAqB,EAAE,CAAC;AACxBkL,MAAAA,IAAI,EAAEtM;AADkB,KAAD,CAf6B;AAiBpDqB,IAAAA,WAAW,EAAE,CAAC;AACdiL,MAAAA,IAAI,EAAEtM;AADQ,KAAD,CAjBuC;AAmBpDkB,IAAAA,aAAa,EAAE,CAAC;AAChBoL,MAAAA,IAAI,EAAEtM;AADU,KAAD,CAnBqC;AAqBpDmB,IAAAA,oBAAoB,EAAE,CAAC;AACvBmL,MAAAA,IAAI,EAAEtM;AADiB,KAAD,CArB8B;AAuBpDuB,IAAAA,mBAAmB,EAAE,CAAC;AACtB+K,MAAAA,IAAI,EAAEtM;AADgB,KAAD,CAvB+B;AAyBpDiB,IAAAA,YAAY,EAAE,CAAC;AACfqL,MAAAA,IAAI,EAAEtM;AADS,KAAD,CAzBsC;AA2BpDgB,IAAAA,eAAe,EAAE,CAAC;AAClBsL,MAAAA,IAAI,EAAEtM;AADY,KAAD,CA3BmC;AA6BpD0B,IAAAA,UAAU,EAAE,CAAC;AACb4K,MAAAA,IAAI,EAAEtM;AADO,KAAD,CA7BwC;AA+BpDwB,IAAAA,cAAc,EAAE,CAAC;AACjB8K,MAAAA,IAAI,EAAEtM;AADW,KAAD,CA/BoC;AAiCpDyB,IAAAA,oBAAoB,EAAE,CAAC;AACvB6K,MAAAA,IAAI,EAAEtM;AADiB,KAAD,CAjC8B;AAmCpD4B,IAAAA,gBAAgB,EAAE,CAAC;AACnB0K,MAAAA,IAAI,EAAEtM;AADa,KAAD,CAnCkC;AAqCpD8B,IAAAA,QAAQ,EAAE,CAAC;AACXwK,MAAAA,IAAI,EAAEtM;AADK,KAAD,CArC0C;AAuCpDgC,IAAAA,mBAAmB,EAAE,CAAC;AACtBsK,MAAAA,IAAI,EAAEtM;AADgB,KAAD,CAvC+B;AAyCpD6B,IAAAA,GAAG,EAAE,CAAC;AACNyK,MAAAA,IAAI,EAAEtM;AADA,KAAD,CAzC+C;AA2CpDiC,IAAAA,gBAAgB,EAAE,CAAC;AACnBqK,MAAAA,IAAI,EAAEtM;AADa,KAAD,CA3CkC;AA6CpDmC,IAAAA,iBAAiB,EAAE,CAAC;AACpBmK,MAAAA,IAAI,EAAEtM;AADc,KAAD,CA7CiC;AA+CpD+B,IAAAA,sBAAsB,EAAE,CAAC;AACzBuK,MAAAA,IAAI,EAAEtM;AADmB,KAAD,CA/C4B;AAiDpDoC,IAAAA,UAAU,EAAE,CAAC;AACbkK,MAAAA,IAAI,EAAErM;AADO,KAAD,CAjDwC;AAmDpD+U,IAAAA,OAAO,EAAE,CAAC;AACV1I,MAAAA,IAAI,EAAEpM,YADI;AAEVsM,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFI,KAAD,CAnD2C;AAsDpDyI,IAAAA,OAAO,EAAE,CAAC;AACV3I,MAAAA,IAAI,EAAEpM,YADI;AAEVsM,MAAAA,IAAI,EAAE,CAAC,OAAD,EAAU,CAAC,QAAD,CAAV;AAFI,KAAD,CAtD2C;AAyDpD0I,IAAAA,aAAa,EAAE,CAAC;AAChB5I,MAAAA,IAAI,EAAEpM,YADU;AAEhBsM,MAAAA,IAAI,EAAE,CAAC,eAAD,EAAkB,CAAC,QAAD,CAAlB;AAFU,KAAD,CAzDqC;AA4DpD2I,IAAAA,OAAO,EAAE,CAAC;AACV7I,MAAAA,IAAI,EAAEpM,YADI;AAEVsM,MAAAA,IAAI,EAAE,CAAC,OAAD,EAAU,CAAC,QAAD,CAAV;AAFI,KAAD,CA5D2C;AA+DpDgK,IAAAA,kBAAkB,EAAE,CAAC;AACrBlK,MAAAA,IAAI,EAAEpM,YADe;AAErBsM,MAAAA,IAAI,EAAE,CAAC,kBAAD,EAAqB,CAAC,QAAD,CAArB;AAFe,KAAD,CA/DgC;AAkEpDiK,IAAAA,gBAAgB,EAAE,CAAC;AACnBnK,MAAAA,IAAI,EAAEpM,YADa;AAEnBsM,MAAAA,IAAI,EAAE,CAAC,gBAAD,EAAmB,CAAC,QAAD,CAAnB;AAFa,KAAD,CAlEkC;AAqEpDkK,IAAAA,MAAM,EAAE,CAAC;AACTpK,MAAAA,IAAI,EAAEpM,YADG;AAETsM,MAAAA,IAAI,EAAE,CAAC,MAAD,EAAS,CAAC,QAAD,CAAT;AAFG,KAAD,CArE4C;AAwEpDoK,IAAAA,OAAO,EAAE,CAAC;AACVtK,MAAAA,IAAI,EAAEpM,YADI;AAEVsM,MAAAA,IAAI,EAAE,CAAC,OAAD,EAAU,CAAC,QAAD,CAAV;AAFI,KAAD,CAxE2C;AA2EpD2K,IAAAA,SAAS,EAAE,CAAC;AACZ7K,MAAAA,IAAI,EAAEpM,YADM;AAEZsM,MAAAA,IAAI,EAAE,CAAC,SAAD,EAAY,CAAC,QAAD,CAAZ;AAFM,KAAD;AA3EyC,GAzBpE;AAAA;;AAyGA,MAAM4M,WAAN,CAAkB;AACd5V,EAAAA,WAAW,CAAC0P,YAAD,EAAe;AACtB,SAAKA,YAAL,GAAoBA,YAApB;AACA,SAAKmG,cAAL,GAAsB,EAAtB;AACA,SAAKlM,oBAAL,GAA4B,EAA5B;AACA,SAAKnG,IAAL,GAAY,EAAZ;AACH;;AACDsS,EAAAA,SAAS,CAACpX,KAAD,EAAQ8E,IAAR,EAAc;AAAE3E,IAAAA,QAAF;AAAY,OAAGkX;AAAf,MAA0B,EAAxC,EAA4C;AACjD,UAAMC,aAAa,GAAG;AAClB3V,MAAAA,cAAc,EAAEmD,IADE;AAElB,SAAG,KAAKqS,cAFU;AAGlB,SAAGE,MAHe;AAIlBlX,MAAAA,QAAQ,EAAE,EACN,GAAG,KAAK6Q,YAAL,CAAkB7Q,QADf;AAEN,WAAGA;AAFG;AAJQ,KAAtB;AASAoX,IAAAA,MAAM,CAACC,OAAP,CAAeF,aAAf,EAA8BG,OAA9B,CAAsC,CAAC,CAAC9H,GAAD,EAAM3P,KAAN,CAAD,KAAkB;AACpD;AACA,WAAKgR,YAAL,CAAkBrB,GAAlB,IAAyB3P,KAAzB;AACH,KAHD;;AAIA,QAAI8E,IAAI,CAACb,QAAL,CAAc,IAAd,CAAJ,EAAyB;AACrB,UAAIa,IAAI,CAACjC,KAAL,CAAW,IAAX,EAAiBG,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,aAAKiI,oBAAL,GAA4BnG,IAAI,CAACjC,KAAL,CAAW,IAAX,EAAiB+O,IAAjB,CAAsB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AACxD,iBAAOD,CAAC,CAAC7O,MAAF,GAAW8O,CAAC,CAAC9O,MAApB;AACH,SAF2B,CAA5B;;AAGA,aAAK+O,QAAL,CAAc/R,KAAd;;AACA,eAAO,KAAKgR,YAAL,CAAkBhM,SAAlB,CAA6B,GAAEhF,KAAM,EAArC,EAAwC,KAAK8E,IAA7C,CAAP;AACH,OAND,MAOK;AACD,aAAKmG,oBAAL,GAA4B,EAA5B;AACA,eAAO,KAAK+F,YAAL,CAAkBhM,SAAlB,CAA6B,GAAEhF,KAAM,EAArC,EAAwC,KAAK8E,IAA7C,CAAP;AACH;AACJ;;AACD,QAAIA,IAAI,CAACb,QAAL,CAAc;AAAI;AAAlB,KAAJ,EAAkD;AAC9C,aAAO,KAAK+M,YAAL,CAAkBhM,SAAlB,CAA6B,GAAEhF,KAAM,EAArC,EAAwC,KAAKgR,YAAL,CAAkBd,qBAAlB,CAAwCpL,IAAxC,CAAxC,CAAP;AACH;;AACD,QAAIA,IAAI,CAACqB,UAAL,CAAgB;AAAY;AAA5B,KAAJ,EAAkD;AAC9C,UAAIkR,MAAM,CAACxY,aAAX,EAA0B;AACtB,aAAKmS,YAAL,CAAkBnS,aAAlB,GAAkCwY,MAAM,CAACxY,aAAzC;AACH;;AACD,UAAIwY,MAAM,CAACzY,iBAAX,EAA8B;AAC1B,aAAKoS,YAAL,CAAkBpS,iBAAlB,GAAsCyY,MAAM,CAACzY,iBAA7C;AACH;;AACD,UAAIyY,MAAM,CAACzX,QAAX,EAAqB;AACjB;AACA,aAAKoR,YAAL,CAAkBpR,QAAlB,GAA6ByX,MAAM,CAACzX,QAApC;AACH,OAV6C,CAW9C;;;AACAI,MAAAA,KAAK,GAAGqN,MAAM,CAACrN,KAAD,CAAd;;AACA,YAAM6V,mBAAmB,GAAG,KAAK7E,YAAL,CAAkBJ,0BAAlB,EAA5B;;AACA,UAAI,CAACtO,KAAK,CAACC,OAAN,CAAc,KAAKyO,YAAL,CAAkBnS,aAAhC,CAAL,EAAqD;AACjD;AACAmB,QAAAA,KAAK,GACD,KAAKgR,YAAL,CAAkBnS,aAAlB,KAAoCgX,mBAApC,GACM7V,KAAK,CAACkD,OAAN,CAAc2S,mBAAd,EAAmC,KAAK7E,YAAL,CAAkBnS,aAArD,CADN,GAEMmB,KAHV;AAIH;;AACD,UAAI,KAAKgR,YAAL,CAAkBpR,QAAlB,IACAI,KADA,IAEA,KAAKgR,YAAL,CAAkB9R,qBAAlB,KAA4C,KAFhD,EAEuD;AACnD;AACAc,QAAAA,KAAK,GAAG,KAAKgR,YAAL,CAAkBtB,eAAlB,CAAkC5K,IAAlC,EAAwC9E,KAAxC,CAAR;AACH;;AACD,UAAI,KAAKgR,YAAL,CAAkBnS,aAAlB,KAAoC;AAAI;AAA5C,QAAyD;AACrD;AACAmB,QAAAA,KAAK,GAAGA,KAAK,CAAC4F,QAAN,GAAiB1C,OAAjB,CAAyB;AAAI;AAA7B,UAAwC;AAAI;AAA5C,SAAR;AACH;;AACD,WAAK8N,YAAL,CAAkBrG,aAAlB,GAAkC,IAAlC;AACH;;AACD,QAAI3K,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAKZ,SAAhC,EAA2C;AACvC,aAAO,KAAK4R,YAAL,CAAkBhM,SAAlB,CAA4B,EAA5B,EAAgCF,IAAhC,CAAP;AACH;;AACD,WAAO,KAAKkM,YAAL,CAAkBhM,SAAlB,CAA6B,GAAEhF,KAAM,EAArC,EAAwC8E,IAAxC,CAAP;AACH;;AACDiN,EAAAA,QAAQ,CAAC/R,KAAD,EAAQ;AACZ,QAAI,KAAKiL,oBAAL,CAA0BjI,MAA1B,GAAmC,CAAvC,EAA0C;AACtC,WAAKiI,oBAAL,CAA0BxB,IAA1B,CAAgC3E,IAAD,IAAU;AACrC,cAAMzB,IAAI,GAAG,KAAK2N,YAAL,CAAkBnF,UAAlB,CAA6B7L,KAA7B,GAAqCgD,MAArC,IACT,KAAKgO,YAAL,CAAkBnF,UAAlB,CAA6B/G,IAA7B,GAAoC9B,MADxC;;AAEA,YAAIhD,KAAK,IAAIqD,IAAb,EAAmB;AACf,eAAKyB,IAAL,GAAYA,IAAZ;AACA,iBAAOzB,IAAP;AACH,SAHD,MAIK;AACD,gBAAMkT,UAAU,GAAG,KAAKtL,oBAAL,CAA0B,KAAKA,oBAAL,CAA0BjI,MAA1B,GAAmC,CAA7D,KACf;AAAG;AADP;AAEA,eAAK8B,IAAL,GAAYyR,UAAZ;AACH;AACJ,OAZD;AAaH;AACJ;;AA3Fa;;AA6FlBW,WAAW,CAAChN,IAAZ;AAAA,mBAAyGgN,WAAzG,EA7oDyG5Z,EA6oDzG,mBAAsIiN,cAAtI;AAAA;;AACA2M,WAAW,CAACQ,KAAZ,kBA9oDyGpa,EA8oDzG;AAAA;AAAA,QAAuG4Z,WAAvG;AAAA;AAAA;;AACA;AAAA,qDA/oDyG5Z,EA+oDzG,mBAA4F4Z,WAA5F,EAAqH,CAAC;AAC1G9M,IAAAA,IAAI,EAAEnM,IADoG;AAE1GqM,IAAAA,IAAI,EAAE,CAAC;AACCwD,MAAAA,IAAI,EAAE,MADP;AAEC6J,MAAAA,IAAI,EAAE;AAFP,KAAD;AAFoG,GAAD,CAArH,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAEvN,MAAAA,IAAI,EAAEG;AAAR,KAAD,CAAP;AAAoC,GAN9E;AAAA;AAQA;AACA;AACA;;;AACA,SAASqN,cAAT,CAAwBC,UAAxB,EAAoCC,WAApC,EAAiD;AAC7C,SAAOA,WAAW,YAAYC,QAAvB,GACD,EAAE,GAAGF,UAAL;AAAiB,OAAGC,WAAW;AAA/B,GADC,GAED,EAAE,GAAGD,UAAL;AAAiB,OAAGC;AAApB,GAFN;AAGH;;AACD,MAAME,aAAN,CAAoB;AACF,SAAPC,OAAO,CAACH,WAAD,EAAc;AACxB,WAAO;AACHI,MAAAA,QAAQ,EAAEF,aADP;AAEHhB,MAAAA,SAAS,EAAE,CACP;AACIH,QAAAA,OAAO,EAAEtY,UADb;AAEI4Z,QAAAA,QAAQ,EAAEL;AAFd,OADO,EAKP;AACIjB,QAAAA,OAAO,EAAErY,cADb;AAEI2Z,QAAAA,QAAQ,EAAE1Z;AAFd,OALO,EASP;AACIoY,QAAAA,OAAO,EAAEvY,eADb;AAEI8Z,QAAAA,UAAU,EAAER,cAFhB;AAGIS,QAAAA,IAAI,EAAE,CAAC7Z,cAAD,EAAiBD,UAAjB;AAHV,OATO,EAcPgM,cAdO;AAFR,KAAP;AAmBH;;AACc,SAAR+N,QAAQ,GAAG;AACd,WAAO;AACHJ,MAAAA,QAAQ,EAAEF;AADP,KAAP;AAGH;;AA1Be;;AA4BpBA,aAAa,CAAC9N,IAAd;AAAA,mBAA2G8N,aAA3G;AAAA;;AACAA,aAAa,CAACO,IAAd,kBA5rDyGjb,EA4rDzG;AAAA,QAA4G0a;AAA5G;AACAA,aAAa,CAACQ,IAAd,kBA7rDyGlb,EA6rDzG;;AACA;AAAA,qDA9rDyGA,EA8rDzG,mBAA4F0a,aAA5F,EAAuH,CAAC;AAC5G5N,IAAAA,IAAI,EAAElM,QADsG;AAE5GoM,IAAAA,IAAI,EAAE,CAAC;AACCmO,MAAAA,OAAO,EAAE,CAAC1H,gBAAD,EAAmBmG,WAAnB,CADV;AAECwB,MAAAA,YAAY,EAAE,CAAC3H,gBAAD,EAAmBmG,WAAnB;AAFf,KAAD;AAFsG,GAAD,CAAvH;AAAA;AAQA;AACA;AACA;;;AAEA,SAAS1Y,cAAT,EAAyBD,UAAzB,EAAqCD,eAArC,EAAsDyS,gBAAtD,EAAwEiH,aAAxE,EAAuFd,WAAvF,EAAoG3M,cAApG,EAAoHqN,cAApH,EAAoInZ,aAApI,EAAmJ0C,SAAnJ,EAA8JC,iBAA9J", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, Injectable, Inject, Optional, forwardRef, Directive, Input, Output, HostListener, Pipe, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS } from '@angular/forms';\n\nconst NGX_MASK_CONFIG = new InjectionToken('ngx-mask config');\nconst NEW_CONFIG = new InjectionToken('new ngx-mask config');\nconst INITIAL_CONFIG = new InjectionToken('initial ngx-mask config');\nconst initialConfig = {\n    suffix: '',\n    prefix: '',\n    thousandSeparator: ' ',\n    decimalMarker: ['.', ','],\n    clearIfNotMatch: false,\n    showTemplate: false,\n    showMaskTyped: false,\n    placeHolderCharacter: '_',\n    dropSpecialCharacters: true,\n    hiddenInput: undefined,\n    shownMaskExpression: '',\n    separatorLimit: '',\n    allowNegativeNumbers: false,\n    validation: true,\n    // eslint-disable-next-line @typescript-eslint/quotes\n    specialCharacters: ['-', '/', '(', ')', '.', ':', ' ', '+', ',', '@', '[', ']', '\"', \"'\"],\n    leadZeroDateTime: false,\n    apm: false,\n    leadZero: false,\n    keepCharacterPositions: false,\n    triggerOnMaskChange: false,\n    inputTransformFn: (value) => value,\n    outputTransformFn: (value) => value,\n    maskFilled: new EventEmitter(),\n    patterns: {\n        '0': {\n            pattern: new RegExp('\\\\d'),\n        },\n        '9': {\n            pattern: new RegExp('\\\\d'),\n            optional: true,\n        },\n        X: {\n            pattern: new RegExp('\\\\d'),\n            symbol: '*',\n        },\n        A: {\n            pattern: new RegExp('[a-zA-Z0-9]'),\n        },\n        S: {\n            pattern: new RegExp('[a-zA-Z]'),\n        },\n        U: {\n            pattern: new RegExp('[A-Z]'),\n        },\n        L: {\n            pattern: new RegExp('[a-z]'),\n        },\n        d: {\n            pattern: new RegExp('\\\\d'),\n        },\n        m: {\n            pattern: new RegExp('\\\\d'),\n        },\n        M: {\n            pattern: new RegExp('\\\\d'),\n        },\n        H: {\n            pattern: new RegExp('\\\\d'),\n        },\n        h: {\n            pattern: new RegExp('\\\\d'),\n        },\n        s: {\n            pattern: new RegExp('\\\\d'),\n        },\n    },\n};\nconst timeMasks = [\n    \"Hh:m0:s0\" /* HOURS_MINUTES_SECONDS */,\n    \"Hh:m0\" /* HOURS_MINUTES */,\n    \"m0:s0\" /* MINUTES_SECONDS */,\n];\nconst withoutValidation = [\n    \"percent\" /* PERCENT */,\n    \"Hh\" /* HOURS_HOUR */,\n    \"s0\" /* SECONDS */,\n    \"m0\" /* MINUTES */,\n    \"separator\" /* SEPARATOR */,\n    \"d0/M0/0000\" /* DAYS_MONTHS_YEARS */,\n    \"d0/M0\" /* DAYS_MONTHS */,\n    \"d0\" /* DAYS */,\n    \"M0\" /* MONTHS */,\n];\n\nclass NgxMaskApplierService {\n    constructor(_config) {\n        this._config = _config;\n        this._shift = new Set();\n        this.plusOnePosition = false;\n        this.maskExpression = '';\n        this.actualValue = '';\n        this.showKeepCharacterExp = '';\n        this.shownMaskExpression = '';\n        this.deletedSpecialCharacter = false;\n        this._formatWithSeparators = (str, thousandSeparatorChar, decimalChars, precision) => {\n            let x = [];\n            let decimalChar = '';\n            if (Array.isArray(decimalChars)) {\n                const regExp = new RegExp(decimalChars.map((v) => ('[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v)).join('|'));\n                x = str.split(regExp);\n                decimalChar = str.match(regExp)?.[0] ?? \"\" /* EMPTY_STRING */;\n            }\n            else {\n                x = str.split(decimalChars);\n                decimalChar = decimalChars;\n            }\n            const decimals = x.length > 1 ? `${decimalChar}${x[1]}` : \"\" /* EMPTY_STRING */;\n            let res = x[0] ?? \"\" /* EMPTY_STRING */;\n            const separatorLimit = this.separatorLimit.replace(/\\s/g, \"\" /* EMPTY_STRING */);\n            if (separatorLimit && +separatorLimit) {\n                if (res[0] === \"-\" /* MINUS */) {\n                    res = `-${res.slice(1, res.length).slice(0, separatorLimit.length)}`;\n                }\n                else {\n                    res = res.slice(0, separatorLimit.length);\n                }\n            }\n            const rgx = /(\\d+)(\\d{3})/;\n            while (thousandSeparatorChar && rgx.test(res)) {\n                res = res.replace(rgx, '$1' + thousandSeparatorChar + '$2');\n            }\n            if (precision === undefined) {\n                return res + decimals;\n            }\n            else if (precision === 0) {\n                return res;\n            }\n            return res + decimals.substring(0, precision + 1);\n        };\n        this.percentage = (str) => {\n            const sanitizedStr = str.replace(',', '.');\n            const value = Number(sanitizedStr);\n            return !isNaN(value) && value >= 0 && value <= 100;\n        };\n        this.getPrecision = (maskExpression) => {\n            const x = maskExpression.split(\".\" /* DOT */);\n            if (x.length > 1) {\n                return Number(x[x.length - 1]);\n            }\n            return Infinity;\n        };\n        this.checkAndRemoveSuffix = (inputValue) => {\n            for (let i = this.suffix?.length - 1; i >= 0; i--) {\n                const substr = this.suffix.substring(i, this.suffix?.length);\n                if (inputValue.includes(substr) &&\n                    i !== this.suffix?.length - 1 &&\n                    (i - 1 < 0 ||\n                        !inputValue.includes(this.suffix.substring(i - 1, this.suffix?.length)))) {\n                    return inputValue.replace(substr, \"\" /* EMPTY_STRING */);\n                }\n            }\n            return inputValue;\n        };\n        this.checkInputPrecision = (inputValue, precision, decimalMarker) => {\n            if (precision < Infinity) {\n                // TODO need think about decimalMarker\n                if (Array.isArray(decimalMarker)) {\n                    const marker = decimalMarker.find((dm) => dm !== this.thousandSeparator);\n                    // eslint-disable-next-line no-param-reassign\n                    decimalMarker = marker ? marker : decimalMarker[0];\n                }\n                const precisionRegEx = new RegExp(this._charToRegExpExpression(decimalMarker) + `\\\\d{${precision}}.*$`);\n                const precisionMatch = inputValue.match(precisionRegEx);\n                const precisionMatchLength = (precisionMatch && precisionMatch[0]?.length) ?? 0;\n                if (precisionMatchLength - 1 > precision) {\n                    const diff = precisionMatchLength - 1 - precision;\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue = inputValue.substring(0, inputValue.length - diff);\n                }\n                if (precision === 0 &&\n                    this._compareOrIncludes(inputValue[inputValue.length - 1], decimalMarker, this.thousandSeparator)) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue = inputValue.substring(0, inputValue.length - 1);\n                }\n            }\n            return inputValue;\n        };\n        this.dropSpecialCharacters = this._config.dropSpecialCharacters;\n        this.hiddenInput = this._config.hiddenInput;\n        this.clearIfNotMatch = this._config.clearIfNotMatch;\n        this.specialCharacters = this._config.specialCharacters;\n        this.patterns = this._config.patterns;\n        this.prefix = this._config.prefix;\n        this.suffix = this._config.suffix;\n        this.thousandSeparator = this._config.thousandSeparator;\n        this.decimalMarker = this._config.decimalMarker;\n        this.showMaskTyped = this._config.showMaskTyped;\n        this.placeHolderCharacter = this._config.placeHolderCharacter;\n        this.validation = this._config.validation;\n        this.separatorLimit = this._config.separatorLimit;\n        this.allowNegativeNumbers = this._config.allowNegativeNumbers;\n        this.leadZeroDateTime = this._config.leadZeroDateTime;\n        this.leadZero = this._config.leadZero;\n        this.apm = this._config.apm;\n        this.inputTransformFn = this._config.inputTransformFn;\n        this.outputTransformFn = this._config.outputTransformFn;\n        this.keepCharacterPositions = this._config.keepCharacterPositions;\n    }\n    applyMaskWithPattern(inputValue, maskAndPattern) {\n        const [mask, customPattern] = maskAndPattern;\n        this.customPattern = customPattern;\n        return this.applyMask(inputValue, mask);\n    }\n    applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false, \n    // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n    cb = () => { }) {\n        if (!maskExpression || typeof inputValue !== 'string') {\n            return \"\" /* EMPTY_STRING */;\n        }\n        let cursor = 0;\n        let result = '';\n        let multi = false;\n        let backspaceShift = false;\n        let shift = 1;\n        let stepBack = false;\n        if (inputValue.slice(0, this.prefix.length) === this.prefix) {\n            // eslint-disable-next-line no-param-reassign\n            inputValue = inputValue.slice(this.prefix.length, inputValue.length);\n        }\n        if (!!this.suffix && inputValue?.length > 0) {\n            // eslint-disable-next-line no-param-reassign\n            inputValue = this.checkAndRemoveSuffix(inputValue);\n        }\n        if (inputValue === '(' && this.prefix) {\n            // eslint-disable-next-line no-param-reassign\n            inputValue = '';\n        }\n        const inputArray = inputValue.toString().split(\"\" /* EMPTY_STRING */);\n        if (this.allowNegativeNumbers &&\n            inputValue.slice(cursor, cursor + 1) === \"-\" /* MINUS */) {\n            // eslint-disable-next-line no-param-reassign\n            result += inputValue.slice(cursor, cursor + 1);\n        }\n        if (maskExpression === \"IP\" /* IP */) {\n            const valuesIP = inputValue.split(\".\" /* DOT */);\n            this.ipError = this._validIP(valuesIP);\n            // eslint-disable-next-line no-param-reassign\n            maskExpression = '***************';\n        }\n        const arr = [];\n        for (let i = 0; i < inputValue.length; i++) {\n            if (inputValue[i]?.match('\\\\d')) {\n                arr.push(inputValue[i] ?? \"\" /* EMPTY_STRING */);\n            }\n        }\n        if (maskExpression === \"CPF_CNPJ\" /* CPF_CNPJ */) {\n            this.cpfCnpjError = arr.length !== 11 && arr.length !== 14;\n            if (arr.length > 11) {\n                // eslint-disable-next-line no-param-reassign\n                maskExpression = '00.000.000/0000-00';\n            }\n            else {\n                // eslint-disable-next-line no-param-reassign\n                maskExpression = '000.000.000-00';\n            }\n        }\n        if (maskExpression.startsWith(\"percent\" /* PERCENT */)) {\n            if (inputValue.match('[a-z]|[A-Z]') ||\n                // eslint-disable-next-line no-useless-escape\n                (inputValue.match(/[-!$%^&*()_+|~=`{}\\[\\]:\";'<>?,\\/.]/) && !backspaced)) {\n                // eslint-disable-next-line no-param-reassign\n                inputValue = this._stripToDecimal(inputValue);\n                const precision = this.getPrecision(maskExpression);\n                // eslint-disable-next-line no-param-reassign\n                inputValue = this.checkInputPrecision(inputValue, precision, this.decimalMarker);\n            }\n            const decimalMarker = typeof this.decimalMarker === 'string' ? this.decimalMarker : \".\" /* DOT */;\n            if (inputValue.indexOf(decimalMarker) > 0 &&\n                !this.percentage(inputValue.substring(0, inputValue.indexOf(decimalMarker)))) {\n                let base = inputValue.substring(0, inputValue.indexOf(decimalMarker) - 1);\n                if (this.allowNegativeNumbers &&\n                    inputValue.slice(cursor, cursor + 1) === \"-\" /* MINUS */ &&\n                    !backspaced) {\n                    base = inputValue.substring(0, inputValue.indexOf(decimalMarker));\n                }\n                // eslint-disable-next-line no-param-reassign\n                inputValue = `${base}${inputValue.substring(inputValue.indexOf(decimalMarker), inputValue.length)}`;\n            }\n            let value = '';\n            this.allowNegativeNumbers &&\n                inputValue.slice(cursor, cursor + 1) === \"-\" /* MINUS */\n                ? (value = inputValue.slice(cursor + 1, cursor + inputValue.length))\n                : (value = inputValue);\n            if (this.percentage(value)) {\n                result = this._splitPercentZero(inputValue);\n            }\n            else {\n                result = this._splitPercentZero(inputValue.substring(0, inputValue.length - 1));\n            }\n        }\n        else if (maskExpression.startsWith(\"separator\" /* SEPARATOR */)) {\n            if (inputValue.match('[wа-яА-Я]') ||\n                inputValue.match('[ЁёА-я]') ||\n                inputValue.match('[a-z]|[A-Z]') ||\n                inputValue.match(/[-@#!$%\\\\^&*()_£¬'+|~=`{}\\]:\";<>.?/]/) ||\n                inputValue.match('[^A-Za-z0-9,]')) {\n                // eslint-disable-next-line no-param-reassign\n                inputValue = this._stripToDecimal(inputValue);\n            }\n            const precision = this.getPrecision(maskExpression);\n            const decimalMarker = Array.isArray(this.decimalMarker)\n                ? \".\" /* DOT */\n                : this.decimalMarker;\n            if (precision === 0) {\n                // eslint-disable-next-line no-param-reassign\n                inputValue = this.allowNegativeNumbers\n                    ? inputValue.length > 2 &&\n                        inputValue[0] === \"-\" /* MINUS */ &&\n                        inputValue[1] === \"0\" /* NUMBER_ZERO */ &&\n                        inputValue[2] !== this.thousandSeparator &&\n                        inputValue[2] !== \",\" /* COMMA */ &&\n                        inputValue[2] !== \".\" /* DOT */\n                        ? '-' + inputValue.slice(2, inputValue.length)\n                        : inputValue[0] === \"0\" /* NUMBER_ZERO */ &&\n                            inputValue.length > 1 &&\n                            inputValue[1] !== this.thousandSeparator &&\n                            inputValue[1] !== \",\" /* COMMA */ &&\n                            inputValue[1] !== \".\" /* DOT */\n                            ? inputValue.slice(1, inputValue.length)\n                            : inputValue\n                    : inputValue.length > 1 &&\n                        inputValue[0] === \"0\" /* NUMBER_ZERO */ &&\n                        inputValue[1] !== this.thousandSeparator &&\n                        inputValue[1] !== \",\" /* COMMA */ &&\n                        inputValue[1] !== \".\" /* DOT */\n                        ? inputValue.slice(1, inputValue.length)\n                        : inputValue;\n            }\n            else {\n                // eslint-disable-next-line no-param-reassign\n                if (inputValue[0] === decimalMarker && inputValue.length > 1) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue =\n                        \"0\" /* NUMBER_ZERO */ + inputValue.slice(0, inputValue.length + 1);\n                    this.plusOnePosition = true;\n                }\n                if (inputValue[0] === \"0\" /* NUMBER_ZERO */ &&\n                    inputValue[1] !== decimalMarker &&\n                    inputValue[1] !== this.thousandSeparator) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue =\n                        inputValue.length > 1\n                            ? inputValue.slice(0, 1) +\n                                decimalMarker +\n                                inputValue.slice(1, inputValue.length + 1)\n                            : inputValue;\n                    this.plusOnePosition = true;\n                }\n                if (this.allowNegativeNumbers &&\n                    inputValue[0] === \"-\" /* MINUS */ &&\n                    (inputValue[1] === decimalMarker ||\n                        inputValue[1] === \"0\" /* NUMBER_ZERO */)) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue =\n                        inputValue[1] === decimalMarker && inputValue.length > 2\n                            ? inputValue.slice(0, 1) +\n                                \"0\" /* NUMBER_ZERO */ +\n                                inputValue.slice(1, inputValue.length)\n                            : inputValue[1] === \"0\" /* NUMBER_ZERO */ &&\n                                inputValue.length > 2 &&\n                                inputValue[2] !== decimalMarker\n                                ? inputValue.slice(0, 2) +\n                                    decimalMarker +\n                                    inputValue.slice(2, inputValue.length)\n                                : inputValue;\n                    this.plusOnePosition = true;\n                }\n            }\n            if (backspaced) {\n                if (inputValue[0] === \"0\" /* NUMBER_ZERO */ &&\n                    inputValue[1] === this.decimalMarker &&\n                    (inputValue[position] === \"0\" /* NUMBER_ZERO */ ||\n                        inputValue[position] === this.decimalMarker)) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue = inputValue.slice(2, inputValue.length);\n                }\n                if (inputValue[0] === \"-\" /* MINUS */ &&\n                    inputValue[1] === \"0\" /* NUMBER_ZERO */ &&\n                    inputValue[2] === this.decimalMarker &&\n                    (inputValue[position] === \"0\" /* NUMBER_ZERO */ ||\n                        inputValue[position] === this.decimalMarker)) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue = \"-\" /* MINUS */ + inputValue.slice(3, inputValue.length);\n                }\n                // eslint-disable-next-line no-param-reassign\n                inputValue = this._compareOrIncludes(inputValue[inputValue.length - 1], this.decimalMarker, this.thousandSeparator)\n                    ? inputValue.slice(0, inputValue.length - 1)\n                    : inputValue;\n            }\n            // TODO: we had different rexexps here for the different cases... but tests dont seam to bother - check this\n            //  separator: no COMMA, dot-sep: no SPACE, COMMA OK, comma-sep: no SPACE, COMMA OK\n            const thousandSeparatorCharEscaped = this._charToRegExpExpression(this.thousandSeparator);\n            let invalidChars = '@#!$%^&*()_+|~=`{}\\\\[\\\\]:\\\\s,\\\\.\";<>?\\\\/'.replace(thousandSeparatorCharEscaped, '');\n            //.replace(decimalMarkerEscaped, '');\n            if (Array.isArray(this.decimalMarker)) {\n                for (const marker of this.decimalMarker) {\n                    invalidChars = invalidChars.replace(this._charToRegExpExpression(marker), \"\" /* EMPTY_STRING */);\n                }\n            }\n            else {\n                invalidChars = invalidChars.replace(this._charToRegExpExpression(this.decimalMarker), '');\n            }\n            const invalidCharRegexp = new RegExp('[' + invalidChars + ']');\n            if (inputValue.match(invalidCharRegexp)) {\n                // eslint-disable-next-line no-param-reassign\n                inputValue = inputValue.substring(0, inputValue.length - 1);\n            }\n            // eslint-disable-next-line no-param-reassign\n            inputValue = this.checkInputPrecision(inputValue, precision, this.decimalMarker);\n            const strForSep = inputValue.replace(new RegExp(thousandSeparatorCharEscaped, 'g'), '');\n            result = this._formatWithSeparators(strForSep, this.thousandSeparator, this.decimalMarker, precision);\n            const commaShift = result.indexOf(\",\" /* COMMA */) - inputValue.indexOf(\",\" /* COMMA */);\n            const shiftStep = result.length - inputValue.length;\n            if (shiftStep > 0 && result[position] !== this.thousandSeparator) {\n                backspaceShift = true;\n                let _shift = 0;\n                do {\n                    this._shift.add(position + _shift);\n                    _shift++;\n                } while (_shift < shiftStep);\n            }\n            else if (result[position - 1] === this.decimalMarker ||\n                shiftStep === -4 ||\n                shiftStep === -3 ||\n                result[position] === \",\" /* COMMA */) {\n                this._shift.clear();\n                this._shift.add(position - 1);\n            }\n            else if ((commaShift !== 0 &&\n                position > 0 &&\n                !(result.indexOf(\",\" /* COMMA */) >= position && position > 3)) ||\n                (!(result.indexOf(\".\" /* DOT */) >= position && position > 3) &&\n                    shiftStep <= 0)) {\n                this._shift.clear();\n                backspaceShift = true;\n                shift = shiftStep;\n                // eslint-disable-next-line no-param-reassign\n                position += shiftStep;\n                this._shift.add(position);\n            }\n            else {\n                this._shift.clear();\n            }\n        }\n        else {\n            for (\n            // eslint-disable-next-line\n            let i = 0, inputSymbol = inputArray[0]; i < inputArray.length; i++, inputSymbol = inputArray[i] ?? \"\" /* EMPTY_STRING */) {\n                if (cursor === maskExpression.length) {\n                    break;\n                }\n                const symbolStarInPattern = \"*\" /* SYMBOL_STAR */ in this.patterns;\n                if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? \"\" /* EMPTY_STRING */) &&\n                    maskExpression[cursor + 1] === \"?\" /* SYMBOL_QUESTION */) {\n                    result += inputSymbol;\n                    cursor += 2;\n                }\n                else if (maskExpression[cursor + 1] === \"*\" /* SYMBOL_STAR */ &&\n                    multi &&\n                    this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? \"\" /* EMPTY_STRING */)) {\n                    result += inputSymbol;\n                    cursor += 3;\n                    multi = false;\n                }\n                else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? \"\" /* EMPTY_STRING */) &&\n                    maskExpression[cursor + 1] === \"*\" /* SYMBOL_STAR */ &&\n                    !symbolStarInPattern) {\n                    result += inputSymbol;\n                    multi = true;\n                }\n                else if (maskExpression[cursor + 1] === \"?\" /* SYMBOL_QUESTION */ &&\n                    this._checkSymbolMask(inputSymbol, maskExpression[cursor + 2] ?? \"\" /* EMPTY_STRING */)) {\n                    result += inputSymbol;\n                    cursor += 3;\n                }\n                else if (this._checkSymbolMask(inputSymbol, maskExpression[cursor] ?? \"\" /* EMPTY_STRING */)) {\n                    if (maskExpression[cursor] === \"H\" /* HOURS */) {\n                        if (this.apm ? Number(inputSymbol) > 9 : Number(inputSymbol) > 2) {\n                            // eslint-disable-next-line no-param-reassign\n                            position = !this.leadZeroDateTime ? position + 1 : position;\n                            cursor += 1;\n                            this._shiftStep(maskExpression, cursor, inputArray.length);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    if (maskExpression[cursor] === \"h\" /* HOUR */) {\n                        if (this.apm\n                            ? (result.length === 1 && Number(result) > 1) ||\n                                (result === '1' && Number(inputSymbol) > 2) ||\n                                (inputValue.slice(cursor - 1, cursor).length === 1 &&\n                                    Number(inputValue.slice(cursor - 1, cursor)) > 2) ||\n                                (inputValue.slice(cursor - 1, cursor) === '1' &&\n                                    Number(inputSymbol) > 2)\n                            : (result === '2' && Number(inputSymbol) > 3) ||\n                                ((result.slice(cursor - 2, cursor) === '2' ||\n                                    result.slice(cursor - 3, cursor) === '2' ||\n                                    result.slice(cursor - 4, cursor) === '2' ||\n                                    result.slice(cursor - 1, cursor) === '2') &&\n                                    Number(inputSymbol) > 3 &&\n                                    cursor > 10)) {\n                            // eslint-disable-next-line no-param-reassign\n                            position = position + 1;\n                            cursor += 1;\n                            i--;\n                            continue;\n                        }\n                    }\n                    if (maskExpression[cursor] === \"m\" /* MINUTE */ ||\n                        maskExpression[cursor] === \"s\" /* SECOND */) {\n                        if (Number(inputSymbol) > 5) {\n                            // eslint-disable-next-line no-param-reassign\n                            position = !this.leadZeroDateTime ? position + 1 : position;\n                            cursor += 1;\n                            this._shiftStep(maskExpression, cursor, inputArray.length);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    const daysCount = 31;\n                    const inputValueCursor = inputValue[cursor];\n                    const inputValueCursorPlusOne = inputValue[cursor + 1];\n                    const inputValueCursorPlusTwo = inputValue[cursor + 2];\n                    const inputValueCursorMinusOne = inputValue[cursor - 1];\n                    const inputValueCursorMinusTwo = inputValue[cursor - 2];\n                    const inputValueCursorMinusThree = inputValue[cursor - 3];\n                    const inputValueSliceMinusThreeMinusOne = inputValue.slice(cursor - 3, cursor - 1);\n                    const inputValueSliceMinusOnePlusOne = inputValue.slice(cursor - 1, cursor + 1);\n                    const inputValueSliceCursorPlusTwo = inputValue.slice(cursor, cursor + 2);\n                    const inputValueSliceMinusTwoCursor = inputValue.slice(cursor - 2, cursor);\n                    if (maskExpression[cursor] === \"d\" /* DAY */) {\n                        const maskStartWithMonth = maskExpression.slice(0, 2) === \"M0\" /* MONTHS */;\n                        const startWithMonthInput = maskExpression.slice(0, 2) === \"M0\" /* MONTHS */ &&\n                            this.specialCharacters.includes(inputValueCursorMinusTwo);\n                        if ((Number(inputSymbol) > 3 && this.leadZeroDateTime) ||\n                            (!maskStartWithMonth &&\n                                (Number(inputValueSliceCursorPlusTwo) > daysCount ||\n                                    Number(inputValueSliceMinusOnePlusOne) > daysCount ||\n                                    this.specialCharacters.includes(inputValueCursorPlusOne))) ||\n                            (startWithMonthInput\n                                ? Number(inputValueSliceMinusOnePlusOne) > daysCount ||\n                                    (!this.specialCharacters.includes(inputValueCursor) &&\n                                        this.specialCharacters.includes(inputValueCursorPlusTwo)) ||\n                                    this.specialCharacters.includes(inputValueCursor)\n                                : Number(inputValueSliceCursorPlusTwo) > daysCount ||\n                                    this.specialCharacters.includes(inputValueCursorPlusOne))) {\n                            // eslint-disable-next-line no-param-reassign\n                            position = !this.leadZeroDateTime ? position + 1 : position;\n                            cursor += 1;\n                            this._shiftStep(maskExpression, cursor, inputArray.length);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    if (maskExpression[cursor] === \"M\" /* MONTH */) {\n                        const monthsCount = 12;\n                        // mask without day\n                        const withoutDays = cursor === 0 &&\n                            (Number(inputSymbol) > 2 ||\n                                Number(inputValueSliceCursorPlusTwo) > monthsCount ||\n                                this.specialCharacters.includes(inputValueCursorPlusOne));\n                        // day<10 && month<12 for input\n                        const specialChart = maskExpression.slice(cursor + 2, cursor + 3);\n                        const day1monthInput = inputValueSliceMinusThreeMinusOne.includes(specialChart) &&\n                            ((this.specialCharacters.includes(inputValueCursorMinusTwo) &&\n                                Number(inputValueSliceMinusOnePlusOne) > monthsCount &&\n                                !this.specialCharacters.includes(inputValueCursor)) ||\n                                this.specialCharacters.includes(inputValueCursor) ||\n                                (this.specialCharacters.includes(inputValueCursorMinusThree) &&\n                                    Number(inputValueSliceMinusTwoCursor) > monthsCount &&\n                                    !this.specialCharacters.includes(inputValueCursorMinusOne)) ||\n                                this.specialCharacters.includes(inputValueCursorMinusOne));\n                        //  month<12 && day<10 for input\n                        const day2monthInput = Number(inputValueSliceMinusThreeMinusOne) <= daysCount &&\n                            !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) &&\n                            this.specialCharacters.includes(inputValueCursorMinusOne) &&\n                            (Number(inputValueSliceCursorPlusTwo) > monthsCount ||\n                                this.specialCharacters.includes(inputValueCursorPlusOne));\n                        // cursor === 5 && without days\n                        const day2monthInputDot = (Number(inputValueSliceCursorPlusTwo) > monthsCount && cursor === 5) ||\n                            (this.specialCharacters.includes(inputValueCursorPlusOne) &&\n                                cursor === 5);\n                        // // day<10 && month<12 for paste whole data\n                        const day1monthPaste = Number(inputValueSliceMinusThreeMinusOne) > daysCount &&\n                            !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) &&\n                            !this.specialCharacters.includes(inputValueSliceMinusTwoCursor) &&\n                            Number(inputValueSliceMinusTwoCursor) > monthsCount;\n                        // 10<day<31 && month<12 for paste whole data\n                        const day2monthPaste = Number(inputValueSliceMinusThreeMinusOne) <= daysCount &&\n                            !this.specialCharacters.includes(inputValueSliceMinusThreeMinusOne) &&\n                            !this.specialCharacters.includes(inputValueCursorMinusOne) &&\n                            Number(inputValueSliceMinusOnePlusOne) > monthsCount;\n                        if ((Number(inputSymbol) > 1 && this.leadZeroDateTime) ||\n                            withoutDays ||\n                            day1monthInput ||\n                            day2monthPaste ||\n                            day1monthPaste ||\n                            day2monthInput ||\n                            (day2monthInputDot && !this.leadZeroDateTime)) {\n                            // eslint-disable-next-line no-param-reassign\n                            position = !this.leadZeroDateTime ? position + 1 : position;\n                            cursor += 1;\n                            this._shiftStep(maskExpression, cursor, inputArray.length);\n                            i--;\n                            if (this.leadZeroDateTime) {\n                                result += '0';\n                            }\n                            continue;\n                        }\n                    }\n                    result += inputSymbol;\n                    cursor++;\n                }\n                else if ((inputSymbol === \" \" /* WHITE_SPACE */ &&\n                    maskExpression[cursor] === \" \" /* WHITE_SPACE */) ||\n                    (inputSymbol === \"/\" /* SLASH */ &&\n                        maskExpression[cursor] === \"/\" /* SLASH */)) {\n                    result += inputSymbol;\n                    cursor++;\n                }\n                else if (this.specialCharacters.indexOf(maskExpression[cursor] ?? \"\" /* EMPTY_STRING */) !== -1) {\n                    result += maskExpression[cursor];\n                    cursor++;\n                    this._shiftStep(maskExpression, cursor, inputArray.length);\n                    i--;\n                }\n                else if (maskExpression[cursor] === \"9\" /* NUMBER_NINE */ &&\n                    this.showMaskTyped) {\n                    this._shiftStep(maskExpression, cursor, inputArray.length);\n                }\n                else if (this.patterns[maskExpression[cursor] ?? \"\" /* EMPTY_STRING */] &&\n                    this.patterns[maskExpression[cursor] ?? \"\" /* EMPTY_STRING */]?.optional) {\n                    if (!!inputArray[cursor] &&\n                        maskExpression !== '***************' &&\n                        maskExpression !== '000.000.000-00' &&\n                        maskExpression !== '00.000.000/0000-00' &&\n                        !maskExpression.match(/^9+\\.0+$/) &&\n                        !this.patterns[maskExpression[cursor] ?? \"\" /* EMPTY_STRING */]\n                            ?.optional) {\n                        result += inputArray[cursor];\n                    }\n                    if (maskExpression.includes(\"9\" /* NUMBER_NINE */ + \"*\" /* SYMBOL_STAR */) &&\n                        maskExpression.includes(\"0\" /* NUMBER_ZERO */ + \"*\" /* SYMBOL_STAR */)) {\n                        cursor++;\n                    }\n                    cursor++;\n                    i--;\n                }\n                else if (this.maskExpression[cursor + 1] === \"*\" /* SYMBOL_STAR */ &&\n                    this._findSpecialChar(this.maskExpression[cursor + 2] ?? \"\" /* EMPTY_STRING */) &&\n                    this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] &&\n                    multi) {\n                    cursor += 3;\n                    result += inputSymbol;\n                }\n                else if (this.maskExpression[cursor + 1] === \"?\" /* SYMBOL_QUESTION */ &&\n                    this._findSpecialChar(this.maskExpression[cursor + 2] ?? \"\" /* EMPTY_STRING */) &&\n                    this._findSpecialChar(inputSymbol) === this.maskExpression[cursor + 2] &&\n                    multi) {\n                    cursor += 3;\n                    result += inputSymbol;\n                }\n                else if (this.showMaskTyped &&\n                    this.specialCharacters.indexOf(inputSymbol) < 0 &&\n                    inputSymbol !== this.placeHolderCharacter &&\n                    this.placeHolderCharacter.length === 1) {\n                    stepBack = true;\n                }\n            }\n        }\n        if (result.length + 1 === maskExpression.length &&\n            this.specialCharacters.indexOf(maskExpression[maskExpression.length - 1] ?? \"\" /* EMPTY_STRING */) !== -1) {\n            result += maskExpression[maskExpression.length - 1];\n        }\n        let newPosition = position + 1;\n        while (this._shift.has(newPosition)) {\n            shift++;\n            newPosition++;\n        }\n        let actualShift = justPasted && !maskExpression.startsWith(\"separator\" /* SEPARATOR */)\n            ? cursor\n            : this._shift.has(position)\n                ? shift\n                : 0;\n        if (stepBack) {\n            actualShift--;\n        }\n        cb(actualShift, backspaceShift);\n        if (shift < 0) {\n            this._shift.clear();\n        }\n        let onlySpecial = false;\n        if (backspaced) {\n            onlySpecial = inputArray.every((char) => this.specialCharacters.includes(char));\n        }\n        let res = `${this.prefix}${onlySpecial ? \"\" /* EMPTY_STRING */ : result}${this.showMaskTyped ? '' : this.suffix}`;\n        if (result.length === 0) {\n            res = !this.dropSpecialCharacters ? `${this.prefix}${result}` : `${result}`;\n        }\n        if (result.includes(\"-\" /* MINUS */) && this.prefix && this.allowNegativeNumbers) {\n            if (backspaced && result === \"-\" /* MINUS */) {\n                return '';\n            }\n            res = `${\"-\" /* MINUS */}${this.prefix}${result\n                .split(\"-\" /* MINUS */)\n                .join(\"\" /* EMPTY_STRING */)}${this.suffix}`;\n        }\n        return res;\n    }\n    _findDropSpecialChar(inputSymbol) {\n        if (Array.isArray(this.dropSpecialCharacters)) {\n            return this.dropSpecialCharacters.find((val) => val === inputSymbol);\n        }\n        return this._findSpecialChar(inputSymbol);\n    }\n    _findSpecialChar(inputSymbol) {\n        return this.specialCharacters.find((val) => val === inputSymbol);\n    }\n    _checkSymbolMask(inputSymbol, maskSymbol) {\n        this.patterns = this.customPattern ? this.customPattern : this.patterns;\n        return ((this.patterns[maskSymbol]?.pattern &&\n            this.patterns[maskSymbol]?.pattern.test(inputSymbol)) ??\n            false);\n    }\n    _stripToDecimal(str) {\n        return str\n            .split(\"\" /* EMPTY_STRING */)\n            .filter((i, idx) => {\n            const isDecimalMarker = typeof this.decimalMarker === 'string'\n                ? i === this.decimalMarker\n                : // TODO (inepipenko) use utility type\n                    this.decimalMarker.includes(i);\n            return (i.match('^-?\\\\d') ||\n                i === this.thousandSeparator ||\n                isDecimalMarker ||\n                (i === \"-\" /* MINUS */ && idx === 0 && this.allowNegativeNumbers));\n        })\n            .join(\"\" /* EMPTY_STRING */);\n    }\n    _charToRegExpExpression(char) {\n        // if (Array.isArray(char)) {\n        // \treturn char.map((v) => ('[\\\\^$.|?*+()'.indexOf(v) >= 0 ? `\\\\${v}` : v)).join('|');\n        // }\n        if (char) {\n            const charsToEscape = '[\\\\^$.|?*+()';\n            return char === ' ' ? '\\\\s' : charsToEscape.indexOf(char) >= 0 ? `\\\\${char}` : char;\n        }\n        return char;\n    }\n    _shiftStep(maskExpression, cursor, inputLength) {\n        const shiftStep = /[*?]/g.test(maskExpression.slice(0, cursor))\n            ? inputLength\n            : cursor;\n        this._shift.add(shiftStep + this.prefix.length || 0);\n    }\n    _compareOrIncludes(value, comparedValue, excludedValue) {\n        return Array.isArray(comparedValue)\n            ? comparedValue.filter((v) => v !== excludedValue).includes(value)\n            : value === comparedValue;\n    }\n    _validIP(valuesIP) {\n        return !(valuesIP.length === 4 &&\n            !valuesIP.some((value, index) => {\n                if (valuesIP.length !== index + 1) {\n                    return value === \"\" /* EMPTY_STRING */ || Number(value) > 255;\n                }\n                return value === \"\" /* EMPTY_STRING */ || Number(value.substring(0, 3)) > 255;\n            }));\n    }\n    _splitPercentZero(value) {\n        const decimalIndex = typeof this.decimalMarker === 'string'\n            ? value.indexOf(this.decimalMarker)\n            : value.indexOf(\".\" /* DOT */);\n        if (decimalIndex === -1) {\n            const parsedValue = parseInt(value, 10);\n            return isNaN(parsedValue) ? \"\" /* EMPTY_STRING */ : parsedValue.toString();\n        }\n        else {\n            const integerPart = parseInt(value.substring(0, decimalIndex), 10);\n            const decimalPart = value.substring(decimalIndex + 1);\n            const integerString = isNaN(integerPart) ? '' : integerPart.toString();\n            const decimal = typeof this.decimalMarker === 'string' ? this.decimalMarker : \".\" /* DOT */;\n            return integerString === \"\" /* EMPTY_STRING */\n                ? \"\" /* EMPTY_STRING */\n                : integerString + decimal + decimalPart;\n        }\n    }\n}\nNgxMaskApplierService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskApplierService, deps: [{ token: NGX_MASK_CONFIG }], target: i0.ɵɵFactoryTarget.Injectable });\nNgxMaskApplierService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskApplierService });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskApplierService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGX_MASK_CONFIG]\n                }] }]; } });\n\nclass NgxMaskService extends NgxMaskApplierService {\n    constructor(document, _config, _elementRef, _renderer) {\n        super(_config);\n        this.document = document;\n        this._config = _config;\n        this._elementRef = _elementRef;\n        this._renderer = _renderer;\n        this.isNumberValue = false;\n        this.maskIsShown = '';\n        this.selStart = null;\n        this.selEnd = null;\n        /**\n         * Whether we are currently in writeValue function, in this case when applying the mask we don't want to trigger onChange function,\n         * since writeValue should be a one way only process of writing the DOM value based on the Angular model value.\n         */\n        this.writingValue = false;\n        this.maskChanged = false;\n        this._maskExpressionArray = [];\n        this.triggerOnMaskChange = false;\n        this._emitValue = false;\n        this._previousValue = '';\n        this._currentValue = '';\n        // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n        this.onChange = (_) => { };\n    }\n    // eslint-disable-next-line complexity\n    applyMask(inputValue, maskExpression, position = 0, justPasted = false, backspaced = false, \n    // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n    cb = () => { }) {\n        if (!maskExpression) {\n            return inputValue !== this.actualValue ? this.actualValue : inputValue;\n        }\n        this.maskIsShown = this.showMaskTyped\n            ? this.showMaskInInput()\n            : \"\" /* EMPTY_STRING */;\n        if (this.maskExpression === \"IP\" /* IP */ && this.showMaskTyped) {\n            this.maskIsShown = this.showMaskInInput(inputValue || \"#\" /* HASH */);\n        }\n        if (this.maskExpression === \"CPF_CNPJ\" /* CPF_CNPJ */ && this.showMaskTyped) {\n            this.maskIsShown = this.showMaskInInput(inputValue || \"#\" /* HASH */);\n        }\n        if (!inputValue && this.showMaskTyped) {\n            this.formControlResult(this.prefix);\n            return this.prefix + this.maskIsShown + this.suffix;\n        }\n        const getSymbol = !!inputValue && typeof this.selStart === 'number'\n            ? inputValue[this.selStart] ?? \"\" /* EMPTY_STRING */\n            : \"\" /* EMPTY_STRING */;\n        let newInputValue = '';\n        if (this.hiddenInput !== undefined && !this.writingValue) {\n            let actualResult = inputValue && inputValue.length === 1\n                ? inputValue.split(\"\" /* EMPTY_STRING */)\n                : this.actualValue.split(\"\" /* EMPTY_STRING */);\n            // eslint-disable  @typescript-eslint/no-unused-expressions\n            // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n            if (typeof this.selStart === 'object' && typeof this.selEnd === 'object') {\n                this.selStart = Number(this.selStart);\n                this.selEnd = Number(this.selEnd);\n            }\n            else {\n                inputValue !== \"\" /* EMPTY_STRING */ && actualResult.length\n                    ? typeof this.selStart === 'number' && typeof this.selEnd === 'number'\n                        ? inputValue.length > actualResult.length\n                            ? actualResult.splice(this.selStart, 0, getSymbol)\n                            : inputValue.length < actualResult.length\n                                ? actualResult.length - inputValue.length === 1\n                                    ? backspaced\n                                        ? actualResult.splice(this.selStart - 1, 1)\n                                        : actualResult.splice(inputValue.length - 1, 1)\n                                    : actualResult.splice(this.selStart, this.selEnd - this.selStart)\n                                : null\n                        : null\n                    : (actualResult = []);\n            }\n            if (this.showMaskTyped) {\n                if (!this.hiddenInput) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue = this.removeMask(inputValue);\n                }\n            }\n            // eslint-enable  @typescript-eslint/no-unused-expressions\n            newInputValue =\n                this.actualValue.length && actualResult.length <= inputValue.length\n                    ? this.shiftTypedSymbols(actualResult.join(\"\" /* EMPTY_STRING */))\n                    : inputValue;\n        }\n        if (justPasted && (this.hiddenInput || !this.hiddenInput)) {\n            newInputValue = inputValue;\n        }\n        if (backspaced &&\n            this.specialCharacters.indexOf(this.maskExpression[position] ?? \"\" /* EMPTY_STRING */) !== -1 &&\n            this.showMaskTyped) {\n            newInputValue = this._currentValue;\n        }\n        if (this.deletedSpecialCharacter && position) {\n            if (this.specialCharacters.includes(this.actualValue.slice(position, position + 1))) {\n                // eslint-disable-next-line no-param-reassign\n                position = position + 1;\n            }\n            else if (maskExpression.slice(position - 1, position + 1) !== \"M0\" /* MONTHS */) {\n                // eslint-disable-next-line no-param-reassign\n                position = position - 2;\n            }\n            // eslint-disable-next-line no-param-reassign\n            this.deletedSpecialCharacter = false;\n        }\n        if (this.showMaskTyped &&\n            this.placeHolderCharacter.length === 1 &&\n            !this.leadZeroDateTime) {\n            // eslint-disable-next-line no-param-reassign\n            inputValue = this.removeMask(inputValue);\n        }\n        if (this.maskChanged) {\n            newInputValue = inputValue;\n        }\n        else {\n            newInputValue =\n                Boolean(newInputValue) && newInputValue.length ? newInputValue : inputValue;\n        }\n        if (this.showMaskTyped && this.keepCharacterPositions && this.actualValue && !justPasted) {\n            const value = this.dropSpecialCharacters\n                ? this.removeMask(this.actualValue)\n                : this.actualValue;\n            this.formControlResult(value);\n            return this.actualValue\n                ? this.actualValue\n                : this.prefix + this.maskIsShown + this.suffix;\n        }\n        const result = super.applyMask(newInputValue, maskExpression, position, justPasted, backspaced, cb);\n        this.actualValue = this.getActualValue(result);\n        // handle some separator implications:\n        // a.) adjust decimalMarker default (. -> ,) if thousandSeparator is a dot\n        if (this.thousandSeparator === \".\" /* DOT */ &&\n            this.decimalMarker === \".\" /* DOT */) {\n            this.decimalMarker = \",\" /* COMMA */;\n        }\n        // b) remove decimal marker from list of special characters to mask\n        if (this.maskExpression.startsWith(\"separator\" /* SEPARATOR */) &&\n            this.dropSpecialCharacters === true) {\n            this.specialCharacters = this.specialCharacters.filter((item) => !this._compareOrIncludes(item, this.decimalMarker, this.thousandSeparator) //item !== this.decimalMarker, // !\n            );\n        }\n        if (result || result === '') {\n            this._previousValue = this._currentValue;\n            this._currentValue = result;\n            this._emitValue =\n                this._previousValue !== this._currentValue ||\n                    this.maskChanged ||\n                    (this._previousValue === this._currentValue && justPasted);\n        }\n        this._emitValue ? this.formControlResult(result) : '';\n        if (!this.showMaskTyped || (this.showMaskTyped && this.hiddenInput)) {\n            if (this.hiddenInput) {\n                if (backspaced) {\n                    return this.hideInput(result, this.maskExpression);\n                }\n                return (this.hideInput(result, this.maskExpression) +\n                    this.maskIsShown.slice(result.length));\n            }\n            return result;\n        }\n        const resLen = result.length;\n        const prefNmask = this.prefix + this.maskIsShown + this.suffix;\n        if (this.maskExpression.includes(\"H\" /* HOURS */)) {\n            const countSkipedSymbol = this._numberSkipedSymbols(result);\n            return result + prefNmask.slice(resLen + countSkipedSymbol);\n        }\n        else if (this.maskExpression === \"IP\" /* IP */ ||\n            this.maskExpression === \"CPF_CNPJ\" /* CPF_CNPJ */) {\n            return result + prefNmask;\n        }\n        return result + prefNmask.slice(resLen);\n    }\n    // get the number of characters that were shifted\n    _numberSkipedSymbols(value) {\n        const regex = /(^|\\D)(\\d\\D)/g;\n        let match = regex.exec(value);\n        let countSkipedSymbol = 0;\n        while (match != null) {\n            countSkipedSymbol += 1;\n            match = regex.exec(value);\n        }\n        return countSkipedSymbol;\n    }\n    applyValueChanges(position, justPasted, backspaced, \n    // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n    cb = () => { }) {\n        const formElement = this._elementRef?.nativeElement;\n        if (!formElement) {\n            return;\n        }\n        formElement.value = this.applyMask(formElement.value, this.maskExpression, position, justPasted, backspaced, cb);\n        if (formElement === this._getActiveElement()) {\n            return;\n        }\n        this.clearIfNotMatchFn();\n    }\n    hideInput(inputValue, maskExpression) {\n        return inputValue\n            .split(\"\" /* EMPTY_STRING */)\n            .map((curr, index) => {\n            if (this.patterns &&\n                this.patterns[maskExpression[index] ?? \"\" /* EMPTY_STRING */] &&\n                this.patterns[maskExpression[index] ?? \"\" /* EMPTY_STRING */]?.symbol) {\n                return this.patterns[maskExpression[index] ?? \"\" /* EMPTY_STRING */]\n                    ?.symbol;\n            }\n            return curr;\n        })\n            .join(\"\" /* EMPTY_STRING */);\n    }\n    // this function is not necessary, it checks result against maskExpression\n    getActualValue(res) {\n        const compare = res\n            .split(\"\" /* EMPTY_STRING */)\n            .filter((symbol, i) => {\n            const maskChar = this.maskExpression[i] ?? \"\" /* EMPTY_STRING */;\n            return (this._checkSymbolMask(symbol, maskChar) ||\n                (this.specialCharacters.includes(maskChar) && symbol === maskChar));\n        });\n        if (compare.join(\"\" /* EMPTY_STRING */) === res) {\n            return compare.join(\"\" /* EMPTY_STRING */);\n        }\n        return res;\n    }\n    shiftTypedSymbols(inputValue) {\n        let symbolToReplace = '';\n        const newInputValue = (inputValue &&\n            inputValue\n                .split(\"\" /* EMPTY_STRING */)\n                .map((currSymbol, index) => {\n                if (this.specialCharacters.includes(inputValue[index + 1] ?? \"\" /* EMPTY_STRING */) &&\n                    inputValue[index + 1] !== this.maskExpression[index + 1]) {\n                    symbolToReplace = currSymbol;\n                    return inputValue[index + 1];\n                }\n                if (symbolToReplace.length) {\n                    const replaceSymbol = symbolToReplace;\n                    symbolToReplace = \"\" /* EMPTY_STRING */;\n                    return replaceSymbol;\n                }\n                return currSymbol;\n            })) ||\n            [];\n        return newInputValue.join(\"\" /* EMPTY_STRING */);\n    }\n    /**\n     * Convert number value to string\n     * 3.1415 -> '3.1415'\n     * 1e-7 -> '0.0000001'\n     */\n    numberToString(value) {\n        if ((!value && value !== 0) ||\n            (this.maskExpression.startsWith(\"separator\" /* SEPARATOR */) &&\n                (this.leadZero || !this.dropSpecialCharacters)) ||\n            (this.maskExpression.startsWith(\"separator\" /* SEPARATOR */) &&\n                this.separatorLimit.length > 14 &&\n                String(value).length > 14)) {\n            return String(value);\n        }\n        return Number(value)\n            .toLocaleString('fullwide', {\n            useGrouping: false,\n            maximumFractionDigits: 20,\n        })\n            .replace(`/${\"-\" /* MINUS */}/`, \"-\" /* MINUS */);\n    }\n    showMaskInInput(inputVal) {\n        if (this.showMaskTyped && !!this.shownMaskExpression) {\n            if (this.maskExpression.length !== this.shownMaskExpression.length) {\n                throw new Error('Mask expression must match mask placeholder length');\n            }\n            else {\n                return this.shownMaskExpression;\n            }\n        }\n        else if (this.showMaskTyped) {\n            if (inputVal) {\n                if (this.maskExpression === \"IP\" /* IP */) {\n                    return this._checkForIp(inputVal);\n                }\n                if (this.maskExpression === \"CPF_CNPJ\" /* CPF_CNPJ */) {\n                    return this._checkForCpfCnpj(inputVal);\n                }\n            }\n            if (this.placeHolderCharacter.length === this.maskExpression.length) {\n                return this.placeHolderCharacter;\n            }\n            return this.maskExpression.replace(/\\w/g, this.placeHolderCharacter);\n        }\n        return '';\n    }\n    clearIfNotMatchFn() {\n        const formElement = this._elementRef?.nativeElement;\n        if (!formElement) {\n            return;\n        }\n        if (this.clearIfNotMatch &&\n            this.prefix.length + this.maskExpression.length + this.suffix.length !==\n                formElement.value.replace(this.placeHolderCharacter, \"\" /* EMPTY_STRING */)\n                    .length) {\n            this.formElementProperty = ['value', \"\" /* EMPTY_STRING */];\n            this.applyMask('', this.maskExpression);\n        }\n    }\n    set formElementProperty([name, value]) {\n        if (!this._renderer || !this._elementRef) {\n            return;\n        }\n        Promise.resolve().then(() => this._renderer?.setProperty(this._elementRef?.nativeElement, name, value));\n    }\n    checkDropSpecialCharAmount(mask) {\n        const chars = mask\n            .split(\"\" /* EMPTY_STRING */)\n            .filter((item) => this._findDropSpecialChar(item));\n        return chars.length;\n    }\n    removeMask(inputValue) {\n        return this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.specialCharacters.concat('_').concat(this.placeHolderCharacter));\n    }\n    _checkForIp(inputVal) {\n        if (inputVal === \"#\" /* HASH */) {\n            return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n        }\n        const arr = [];\n        for (let i = 0; i < inputVal.length; i++) {\n            const value = inputVal[i] ?? \"\" /* EMPTY_STRING */;\n            if (!value) {\n                continue;\n            }\n            if (value.match('\\\\d')) {\n                arr.push(value);\n            }\n        }\n        if (arr.length <= 3) {\n            return `${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n        }\n        if (arr.length > 3 && arr.length <= 6) {\n            return `${this.placeHolderCharacter}.${this.placeHolderCharacter}`;\n        }\n        if (arr.length > 6 && arr.length <= 9) {\n            return this.placeHolderCharacter;\n        }\n        if (arr.length > 9 && arr.length <= 12) {\n            return '';\n        }\n        return '';\n    }\n    _checkForCpfCnpj(inputVal) {\n        const cpf = `${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n        const cnpj = `${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `/${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}` +\n            `-${this.placeHolderCharacter}${this.placeHolderCharacter}`;\n        if (inputVal === \"#\" /* HASH */) {\n            return cpf;\n        }\n        const arr = [];\n        for (let i = 0; i < inputVal.length; i++) {\n            const value = inputVal[i] ?? \"\" /* EMPTY_STRING */;\n            if (!value) {\n                continue;\n            }\n            if (value.match('\\\\d')) {\n                arr.push(value);\n            }\n        }\n        if (arr.length <= 3) {\n            return cpf.slice(arr.length, cpf.length);\n        }\n        if (arr.length > 3 && arr.length <= 6) {\n            return cpf.slice(arr.length + 1, cpf.length);\n        }\n        if (arr.length > 6 && arr.length <= 9) {\n            return cpf.slice(arr.length + 2, cpf.length);\n        }\n        if (arr.length > 9 && arr.length < 11) {\n            return cpf.slice(arr.length + 3, cpf.length);\n        }\n        if (arr.length === 11) {\n            return '';\n        }\n        if (arr.length === 12) {\n            if (inputVal.length === 17) {\n                return cnpj.slice(16, cnpj.length);\n            }\n            return cnpj.slice(15, cnpj.length);\n        }\n        if (arr.length > 12 && arr.length <= 14) {\n            return cnpj.slice(arr.length + 4, cnpj.length);\n        }\n        return '';\n    }\n    /**\n     * Recursively determine the current active element by navigating the Shadow DOM until the Active Element is found.\n     */\n    _getActiveElement(document = this.document) {\n        const shadowRootEl = document?.activeElement?.shadowRoot;\n        if (!shadowRootEl?.activeElement) {\n            return document.activeElement;\n        }\n        else {\n            return this._getActiveElement(shadowRootEl);\n        }\n    }\n    /**\n     * Propogates the input value back to the Angular model by triggering the onChange function. It won't do this if writingValue\n     * is true. If that is true it means we are currently in the writeValue function, which is supposed to only update the actual\n     * DOM element based on the Angular model value. It should be a one way process, i.e. writeValue should not be modifying the Angular\n     * model value too. Therefore, we don't trigger onChange in this scenario.\n     * @param inputValue the current form input value\n     */\n    formControlResult(inputValue) {\n        if (this.writingValue || (!this.triggerOnMaskChange && this.maskChanged)) {\n            this.maskChanged\n                ? this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue))))))\n                : '';\n            this.maskChanged = false;\n            return;\n        }\n        if (Array.isArray(this.dropSpecialCharacters)) {\n            this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeMask(this._removeSuffix(this._removePrefix(inputValue)), this.dropSpecialCharacters)))));\n        }\n        else if (this.dropSpecialCharacters ||\n            (!this.dropSpecialCharacters && this.prefix === inputValue)) {\n            this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(inputValue))))));\n        }\n        else {\n            this.onChange(this.outputTransformFn(this._toNumber(inputValue)));\n        }\n    }\n    _toNumber(value) {\n        if (!this.isNumberValue || value === \"\" /* EMPTY_STRING */) {\n            return value;\n        }\n        if (this.maskExpression.startsWith(\"separator\" /* SEPARATOR */) &&\n            (this.leadZero || !this.dropSpecialCharacters)) {\n            return value;\n        }\n        if (String(value).length > 16 && this.separatorLimit.length > 14) {\n            return String(value);\n        }\n        const num = Number(value);\n        if (this.maskExpression.startsWith(\"separator\" /* SEPARATOR */) && Number.isNaN(num)) {\n            const val = String(value).replace(',', '.');\n            return Number(val);\n        }\n        return Number.isNaN(num) ? value : num;\n    }\n    _removeMask(value, specialCharactersForRemove) {\n        if (this.maskExpression.startsWith(\"percent\" /* PERCENT */) &&\n            value.includes(\".\" /* DOT */)) {\n            return value;\n        }\n        return value\n            ? value.replace(this._regExpForRemove(specialCharactersForRemove), \"\" /* EMPTY_STRING */)\n            : value;\n    }\n    _removePrefix(value) {\n        if (!this.prefix) {\n            return value;\n        }\n        return value ? value.replace(this.prefix, \"\" /* EMPTY_STRING */) : value;\n    }\n    _removeSuffix(value) {\n        if (!this.suffix) {\n            return value;\n        }\n        return value ? value.replace(this.suffix, \"\" /* EMPTY_STRING */) : value;\n    }\n    _retrieveSeparatorValue(result) {\n        let specialCharacters = Array.isArray(this.dropSpecialCharacters)\n            ? this.specialCharacters.filter((v) => {\n                return this.dropSpecialCharacters.includes(v);\n            })\n            : this.specialCharacters;\n        if (!this.deletedSpecialCharacter &&\n            this._checkPatternForSpace() &&\n            result.includes(\" \" /* WHITE_SPACE */) &&\n            this.maskExpression.includes(\"*\" /* SYMBOL_STAR */)) {\n            specialCharacters = specialCharacters.filter((char) => char !== \" \" /* WHITE_SPACE */);\n        }\n        return this._removeMask(result, specialCharacters);\n    }\n    _regExpForRemove(specialCharactersForRemove) {\n        return new RegExp(specialCharactersForRemove.map((item) => `\\\\${item}`).join('|'), 'gi');\n    }\n    _replaceDecimalMarkerToDot(value) {\n        const markers = Array.isArray(this.decimalMarker)\n            ? this.decimalMarker\n            : [this.decimalMarker];\n        return value.replace(this._regExpForRemove(markers), \".\" /* DOT */);\n    }\n    _checkSymbols(result) {\n        if (result === \"\" /* EMPTY_STRING */) {\n            return result;\n        }\n        if (this.maskExpression.startsWith(\"percent\" /* PERCENT */) &&\n            this.decimalMarker === \",\" /* COMMA */) {\n            // eslint-disable-next-line no-param-reassign\n            result = result.replace(\",\" /* COMMA */, \".\" /* DOT */);\n        }\n        const separatorPrecision = this._retrieveSeparatorPrecision(this.maskExpression);\n        const separatorValue = this._replaceDecimalMarkerToDot(this._retrieveSeparatorValue(result));\n        if (!this.isNumberValue) {\n            return separatorValue;\n        }\n        if (separatorPrecision) {\n            if (result === this.decimalMarker) {\n                return null;\n            }\n            if (this.separatorLimit.length > 14) {\n                return String(separatorValue);\n            }\n            return this._checkPrecision(this.maskExpression, separatorValue);\n        }\n        else {\n            return separatorValue;\n        }\n    }\n    _checkPatternForSpace() {\n        for (const key in this.patterns) {\n            // eslint-disable-next-line no-prototype-builtins\n            if (this.patterns[key] && this.patterns[key]?.hasOwnProperty('pattern')) {\n                const patternString = this.patterns[key]?.pattern.toString();\n                const pattern = this.patterns[key]?.pattern;\n                if (patternString?.includes(\" \" /* WHITE_SPACE */) &&\n                    pattern?.test(this.maskExpression)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n    // TODO should think about helpers or separting decimal precision to own property\n    _retrieveSeparatorPrecision(maskExpretion) {\n        const matcher = maskExpretion.match(new RegExp(`^separator\\\\.([^d]*)`));\n        return matcher ? Number(matcher[1]) : null;\n    }\n    _checkPrecision(separatorExpression, separatorValue) {\n        const separatorPrecision = separatorExpression.slice(10, 11);\n        if (separatorExpression.indexOf('2') > 0 ||\n            (this.leadZero && Number(separatorPrecision) > 1)) {\n            if (this.decimalMarker === \",\" /* COMMA */ && this.leadZero) {\n                // eslint-disable-next-line no-param-reassign\n                separatorValue = separatorValue.replace(',', '.');\n            }\n            return this.leadZero\n                ? Number(separatorValue).toFixed(Number(separatorPrecision))\n                : Number(separatorValue).toFixed(2);\n        }\n        return this.numberToString(separatorValue);\n    }\n    _repeatPatternSymbols(maskExp) {\n        return ((maskExp.match(/{[0-9]+}/) &&\n            maskExp\n                .split(\"\" /* EMPTY_STRING */)\n                .reduce((accum, currVal, index) => {\n                this._start =\n                    currVal === \"{\" /* CURLY_BRACKETS_LEFT */ ? index : this._start;\n                if (currVal !== \"}\" /* CURLY_BRACKETS_RIGHT */) {\n                    return this._findSpecialChar(currVal) ? accum + currVal : accum;\n                }\n                this._end = index;\n                const repeatNumber = Number(maskExp.slice(this._start + 1, this._end));\n                const replaceWith = new Array(repeatNumber + 1).join(maskExp[this._start - 1]);\n                if (maskExp.slice(0, this._start).length > 1 &&\n                    maskExp.includes(\"S\" /* LETTER_S */)) {\n                    const symbols = maskExp.slice(0, this._start - 1);\n                    return symbols.includes(\"{\" /* CURLY_BRACKETS_LEFT */)\n                        ? accum + replaceWith\n                        : symbols + accum + replaceWith;\n                }\n                else {\n                    return accum + replaceWith;\n                }\n            }, '')) ||\n            maskExp);\n    }\n    currentLocaleDecimalMarker() {\n        return (1.1).toLocaleString().substring(1, 2);\n    }\n}\nNgxMaskService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskService, deps: [{ token: DOCUMENT }, { token: NGX_MASK_CONFIG }, { token: i0.ElementRef, optional: true }, { token: i0.Renderer2, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nNgxMaskService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskService });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskService, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGX_MASK_CONFIG]\n                }] }, { type: i0.ElementRef, decorators: [{\n                    type: Optional\n                }] }, { type: i0.Renderer2, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n// tslint:disable deprecation\n// tslint:disable no-input-rename\nclass NgxMaskDirective {\n    constructor(\n    // tslint:disable-next-line\n    document, _config, _maskService) {\n        this.document = document;\n        this._config = _config;\n        this._maskService = _maskService;\n        // eslint-disable-next-line @angular-eslint/no-input-rename\n        this.maskExpression = '';\n        this.specialCharacters = [];\n        this.patterns = {};\n        this.prefix = '';\n        this.suffix = '';\n        this.thousandSeparator = ' ';\n        this.decimalMarker = '.';\n        this.dropSpecialCharacters = null;\n        this.hiddenInput = null;\n        this.showMaskTyped = null;\n        this.placeHolderCharacter = null;\n        this.shownMaskExpression = null;\n        this.showTemplate = null;\n        this.clearIfNotMatch = null;\n        this.validation = null;\n        this.separatorLimit = null;\n        this.allowNegativeNumbers = null;\n        this.leadZeroDateTime = null;\n        this.leadZero = null;\n        this.triggerOnMaskChange = null;\n        this.apm = null;\n        this.inputTransformFn = null;\n        this.outputTransformFn = null;\n        this.keepCharacterPositions = null;\n        this.maskFilled = new EventEmitter();\n        this._maskValue = '';\n        this._isFocused = false;\n        this._position = null;\n        this._maskExpressionArray = [];\n        this._justPasted = false;\n        /**For IME composition event */\n        this._isComposing = false;\n        // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any\n        this.onChange = (_) => { };\n        // eslint-disable-next-line @typescript-eslint/no-empty-function\n        this.onTouch = () => { };\n    }\n    ngOnChanges(changes) {\n        const { maskExpression, specialCharacters, patterns, prefix, suffix, thousandSeparator, decimalMarker, dropSpecialCharacters, hiddenInput, showMaskTyped, placeHolderCharacter, shownMaskExpression, showTemplate, clearIfNotMatch, validation, separatorLimit, allowNegativeNumbers, leadZeroDateTime, leadZero, triggerOnMaskChange, apm, inputTransformFn, outputTransformFn, keepCharacterPositions, } = changes;\n        if (maskExpression) {\n            if (maskExpression.currentValue !== maskExpression.previousValue &&\n                !maskExpression.firstChange) {\n                this._maskService.maskChanged = true;\n            }\n            if (maskExpression.currentValue &&\n                maskExpression.currentValue.split(\"||\" /* OR */).length > 1) {\n                this._maskExpressionArray = maskExpression.currentValue\n                    .split(\"||\" /* OR */)\n                    .sort((a, b) => {\n                    return a.length - b.length;\n                });\n                this._setMask();\n            }\n            else {\n                this._maskExpressionArray = [];\n                this._maskValue = maskExpression.currentValue || \"\" /* EMPTY_STRING */;\n                this._maskService.maskExpression = this._maskValue;\n            }\n        }\n        if (specialCharacters) {\n            if (!specialCharacters.currentValue || !Array.isArray(specialCharacters.currentValue)) {\n                return;\n            }\n            else {\n                this._maskService.specialCharacters = specialCharacters.currentValue || [];\n            }\n        }\n        if (allowNegativeNumbers) {\n            this._maskService.allowNegativeNumbers = allowNegativeNumbers.currentValue;\n            if (this._maskService.allowNegativeNumbers) {\n                this._maskService.specialCharacters = this._maskService.specialCharacters.filter((c) => c !== \"-\" /* MINUS */);\n            }\n        }\n        // Only overwrite the mask available patterns if a pattern has actually been passed in\n        if (patterns && patterns.currentValue) {\n            this._maskService.patterns = patterns.currentValue;\n        }\n        if (apm && apm.currentValue) {\n            this._maskService.apm = apm.currentValue;\n        }\n        if (prefix) {\n            this._maskService.prefix = prefix.currentValue;\n        }\n        if (suffix) {\n            this._maskService.suffix = suffix.currentValue;\n        }\n        if (thousandSeparator) {\n            this._maskService.thousandSeparator = thousandSeparator.currentValue;\n        }\n        if (decimalMarker) {\n            this._maskService.decimalMarker = decimalMarker.currentValue;\n        }\n        if (dropSpecialCharacters) {\n            this._maskService.dropSpecialCharacters = dropSpecialCharacters.currentValue;\n        }\n        if (hiddenInput) {\n            this._maskService.hiddenInput = hiddenInput.currentValue;\n        }\n        if (showMaskTyped) {\n            this._maskService.showMaskTyped = showMaskTyped.currentValue;\n            if (showMaskTyped.previousValue === false &&\n                showMaskTyped.currentValue === true &&\n                this._isFocused) {\n                requestAnimationFrame(() => {\n                    this._maskService._elementRef?.nativeElement.click();\n                });\n            }\n        }\n        if (placeHolderCharacter) {\n            this._maskService.placeHolderCharacter = placeHolderCharacter.currentValue;\n        }\n        if (shownMaskExpression) {\n            this._maskService.shownMaskExpression = shownMaskExpression.currentValue;\n        }\n        if (showTemplate) {\n            this._maskService.showTemplate = showTemplate.currentValue;\n        }\n        if (clearIfNotMatch) {\n            this._maskService.clearIfNotMatch = clearIfNotMatch.currentValue;\n        }\n        if (validation) {\n            this._maskService.validation = validation.currentValue;\n        }\n        if (separatorLimit) {\n            this._maskService.separatorLimit = separatorLimit.currentValue;\n        }\n        if (leadZeroDateTime) {\n            this._maskService.leadZeroDateTime = leadZeroDateTime.currentValue;\n        }\n        if (leadZero) {\n            this._maskService.leadZero = leadZero.currentValue;\n        }\n        if (triggerOnMaskChange) {\n            this._maskService.triggerOnMaskChange = triggerOnMaskChange.currentValue;\n        }\n        if (inputTransformFn) {\n            this._maskService.inputTransformFn = inputTransformFn.currentValue;\n        }\n        if (outputTransformFn) {\n            this._maskService.outputTransformFn = outputTransformFn.currentValue;\n        }\n        if (keepCharacterPositions) {\n            this._maskService.keepCharacterPositions = keepCharacterPositions.currentValue;\n        }\n        this._applyMask();\n    }\n    // eslint-disable-next-line complexity\n    validate({ value }) {\n        if (!this._maskService.validation || !this._maskValue) {\n            return null;\n        }\n        if (this._maskService.ipError) {\n            return this._createValidationError(value);\n        }\n        if (this._maskService.cpfCnpjError) {\n            return this._createValidationError(value);\n        }\n        if (this._maskValue.startsWith(\"separator\" /* SEPARATOR */)) {\n            return null;\n        }\n        if (withoutValidation.includes(this._maskValue)) {\n            return null;\n        }\n        if (this._maskService.clearIfNotMatch) {\n            return null;\n        }\n        if (timeMasks.includes(this._maskValue)) {\n            return this._validateTime(value);\n        }\n        if (value && value.toString().length >= 1) {\n            let counterOfOpt = 0;\n            if (this._maskValue.startsWith(\"percent\" /* PERCENT */)) {\n                return null;\n            }\n            for (const key in this._maskService.patterns) {\n                if (this._maskService.patterns[key]?.optional) {\n                    if (this._maskValue.indexOf(key) !== this._maskValue.lastIndexOf(key)) {\n                        const opt = this._maskValue\n                            .split(\"\" /* EMPTY_STRING */)\n                            .filter((i) => i === key)\n                            .join(\"\" /* EMPTY_STRING */);\n                        counterOfOpt += opt.length;\n                    }\n                    else if (this._maskValue.indexOf(key) !== -1) {\n                        counterOfOpt++;\n                    }\n                    if (this._maskValue.indexOf(key) !== -1 &&\n                        value.toString().length >= this._maskValue.indexOf(key)) {\n                        return null;\n                    }\n                    if (counterOfOpt === this._maskValue.length) {\n                        return null;\n                    }\n                }\n            }\n            if (this._maskValue.indexOf(\"{\" /* CURLY_BRACKETS_LEFT */) === 1 &&\n                value.toString().length ===\n                    this._maskValue.length +\n                        Number((this._maskValue.split(\"{\" /* CURLY_BRACKETS_LEFT */)[1] ??\n                            \"\" /* EMPTY_STRING */).split(\"}\" /* CURLY_BRACKETS_RIGHT */)[0]) -\n                        4) {\n                return null;\n            }\n            else if ((this._maskValue.indexOf(\"*\" /* SYMBOL_STAR */) > 1 &&\n                value.toString().length <\n                    this._maskValue.indexOf(\"*\" /* SYMBOL_STAR */)) ||\n                (this._maskValue.indexOf(\"?\" /* SYMBOL_QUESTION */) > 1 &&\n                    value.toString().length <\n                        this._maskValue.indexOf(\"?\" /* SYMBOL_QUESTION */)) ||\n                this._maskValue.indexOf(\"{\" /* CURLY_BRACKETS_LEFT */) === 1) {\n                return this._createValidationError(value);\n            }\n            if (this._maskValue.indexOf(\"*\" /* SYMBOL_STAR */) === -1 ||\n                this._maskValue.indexOf(\"?\" /* SYMBOL_QUESTION */) === -1) {\n                // eslint-disable-next-line no-param-reassign\n                value = typeof value === 'number' ? String(value) : value;\n                const array = this._maskValue.split('*');\n                const length = this._maskService.dropSpecialCharacters\n                    ? this._maskValue.length -\n                        this._maskService.checkDropSpecialCharAmount(this._maskValue) -\n                        counterOfOpt\n                    : this.prefix\n                        ? this._maskValue.length + this.prefix.length - counterOfOpt\n                        : this._maskValue.length - counterOfOpt;\n                if (array.length === 1) {\n                    if (value.toString().length < length) {\n                        return this._createValidationError(value);\n                    }\n                }\n                if (array.length > 1) {\n                    const lastIndexArray = array[array.length - 1];\n                    if (lastIndexArray &&\n                        this._maskService.specialCharacters.includes(lastIndexArray[0]) &&\n                        String(value).includes(lastIndexArray[0] ?? '') &&\n                        !this.dropSpecialCharacters) {\n                        const special = value.split(lastIndexArray[0]);\n                        return special[special.length - 1].length === lastIndexArray.length - 1\n                            ? null\n                            : this._createValidationError(value);\n                    }\n                    else if (((lastIndexArray &&\n                        !this._maskService.specialCharacters.includes(lastIndexArray[0])) ||\n                        !lastIndexArray ||\n                        this._maskService.dropSpecialCharacters) &&\n                        value.length >= length - 1) {\n                        return null;\n                    }\n                    else {\n                        return this._createValidationError(value);\n                    }\n                }\n            }\n            if (this._maskValue.indexOf(\"*\" /* SYMBOL_STAR */) === 1 ||\n                this._maskValue.indexOf(\"?\" /* SYMBOL_QUESTION */) === 1) {\n                return null;\n            }\n        }\n        if (value) {\n            this.maskFilled.emit();\n            return null;\n        }\n        return null;\n    }\n    onPaste() {\n        this._justPasted = true;\n    }\n    onFocus() {\n        this._isFocused = true;\n    }\n    onModelChange(value) {\n        // on form reset we need to update the actualValue\n        if ((value === \"\" /* EMPTY_STRING */ || value === null || value === undefined) &&\n            this._maskService.actualValue) {\n            this._maskService.actualValue = this._maskService.getActualValue(\"\" /* EMPTY_STRING */);\n        }\n    }\n    onInput(e) {\n        // If IME is composing text, we wait for the composed text.\n        if (this._isComposing)\n            return;\n        const el = e.target;\n        const transformedValue = this._maskService.inputTransformFn(el.value);\n        if (el.type !== 'number') {\n            if (typeof transformedValue === 'string' || typeof transformedValue === 'number') {\n                el.value = transformedValue.toString();\n                this._inputValue = el.value;\n                this._setMask();\n                if (!this._maskValue) {\n                    this.onChange(el.value);\n                    return;\n                }\n                let position = el.selectionStart === 1\n                    ? el.selectionStart + this._maskService.prefix.length\n                    : el.selectionStart;\n                if (this.showMaskTyped &&\n                    this.keepCharacterPositions &&\n                    this._maskService.placeHolderCharacter.length === 1) {\n                    const inputSymbol = el.value.slice(position - 1, position);\n                    const prefixLength = this.prefix.length;\n                    const checkSymbols = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position - 1 - prefixLength] ??\n                        \"\" /* EMPTY_STRING */);\n                    const checkSpecialCharacter = this._maskService._checkSymbolMask(inputSymbol, this._maskService.maskExpression[position + 1 - prefixLength] ??\n                        \"\" /* EMPTY_STRING */);\n                    const selectRangeBackspace = this._maskService.selStart === this._maskService.selEnd;\n                    const selStart = Number(this._maskService.selStart) - prefixLength ?? '';\n                    const selEnd = Number(this._maskService.selEnd) - prefixLength ?? '';\n                    if (this._code === \"Backspace\" /* BACKSPACE */) {\n                        if (!selectRangeBackspace) {\n                            if (this._maskService.selStart === prefixLength) {\n                                this._maskService.actualValue =\n                                    this.prefix +\n                                        this._maskService.maskIsShown.slice(0, selEnd) +\n                                        this._inputValue.split(this.prefix).join('');\n                            }\n                            else if (this._maskService.selStart ===\n                                this._maskService.maskIsShown.length + prefixLength) {\n                                this._maskService.actualValue =\n                                    this._inputValue +\n                                        this._maskService.maskIsShown.slice(selStart, selEnd);\n                            }\n                            else {\n                                this._maskService.actualValue =\n                                    this.prefix +\n                                        this._inputValue\n                                            .split(this.prefix)\n                                            .join('')\n                                            .slice(0, selStart) +\n                                        this._maskService.maskIsShown.slice(selStart, selEnd) +\n                                        this._maskService.actualValue.slice(selEnd + prefixLength, this._maskService.maskIsShown.length + prefixLength) +\n                                        this.suffix;\n                            }\n                        }\n                        else if (!this._maskService.specialCharacters.includes(this._maskService.maskExpression.slice(position - this.prefix.length, position + 1 - this.prefix.length)) &&\n                            selectRangeBackspace) {\n                            if (selStart === 1 && this.prefix) {\n                                this._maskService.actualValue =\n                                    this.prefix +\n                                        this._maskService.placeHolderCharacter +\n                                        el.value\n                                            .split(this.prefix)\n                                            .join('')\n                                            .split(this.suffix)\n                                            .join('') +\n                                        this.suffix;\n                                position = position - 1;\n                            }\n                            else {\n                                const part1 = el.value.substring(0, position);\n                                const part2 = el.value.substring(position);\n                                this._maskService.actualValue =\n                                    part1 + this._maskService.placeHolderCharacter + part2;\n                            }\n                        }\n                    }\n                    if (this._code !== \"Backspace\" /* BACKSPACE */) {\n                        if (!checkSymbols && !checkSpecialCharacter && selectRangeBackspace) {\n                            position = Number(el.selectionStart) - 1;\n                        }\n                        else if (this._maskService.specialCharacters.includes(el.value.slice(position, position + 1)) &&\n                            checkSpecialCharacter &&\n                            !this._maskService.specialCharacters.includes(el.value.slice(position + 1, position + 2))) {\n                            this._maskService.actualValue =\n                                el.value.slice(0, position - 1) +\n                                    el.value.slice(position, position + 1) +\n                                    inputSymbol +\n                                    el.value.slice(position + 2);\n                            position = position + 1;\n                        }\n                        else if (checkSymbols) {\n                            if (el.value.length === 1 && position === 1) {\n                                this._maskService.actualValue =\n                                    this.prefix +\n                                        inputSymbol +\n                                        this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length) +\n                                        this.suffix;\n                            }\n                            else {\n                                this._maskService.actualValue =\n                                    el.value.slice(0, position - 1) +\n                                        inputSymbol +\n                                        el.value\n                                            .slice(position + 1)\n                                            .split(this.suffix)\n                                            .join('') +\n                                        this.suffix;\n                            }\n                        }\n                        else if (this.prefix &&\n                            el.value.length === 1 &&\n                            position - prefixLength === 1 &&\n                            this._maskService._checkSymbolMask(el.value, this._maskService.maskExpression[position - 1 - prefixLength] ??\n                                \"\" /* EMPTY_STRING */)) {\n                            this._maskService.actualValue =\n                                this.prefix +\n                                    el.value +\n                                    this._maskService.maskIsShown.slice(1, this._maskService.maskIsShown.length) +\n                                    this.suffix;\n                        }\n                    }\n                }\n                let caretShift = 0;\n                let backspaceShift = false;\n                if (this._code === \"Delete\" /* DELETE */ && \"separator\" /* SEPARATOR */) {\n                    this._maskService.deletedSpecialCharacter = true;\n                }\n                if (this._inputValue.length >= this._maskService.maskExpression.length - 1 &&\n                    this._code !== \"Backspace\" /* BACKSPACE */ &&\n                    this._maskService.maskExpression === \"d0/M0/0000\" /* DAYS_MONTHS_YEARS */ &&\n                    position < 10) {\n                    const inputSymbol = this._inputValue.slice(position - 1, position);\n                    el.value =\n                        this._inputValue.slice(0, position - 1) +\n                            inputSymbol +\n                            this._inputValue.slice(position + 1);\n                }\n                if (this._maskService.maskExpression === \"d0/M0/0000\" /* DAYS_MONTHS_YEARS */ &&\n                    this.leadZeroDateTime) {\n                    if ((position < 3 && Number(el.value) > 31 && Number(el.value) < 40) ||\n                        (position === 5 && Number(el.value.slice(3, 5)) > 12)) {\n                        position = position + 2;\n                    }\n                }\n                if (this._maskService.maskExpression === \"Hh:m0:s0\" /* HOURS_MINUTES_SECONDS */ &&\n                    this.apm) {\n                    if (this._justPasted && el.value.slice(0, 2) === \"00\" /* DOUBLE_ZERO */) {\n                        el.value = el.value.slice(1, 2) + el.value.slice(2, el.value.length);\n                    }\n                    el.value =\n                        el.value === \"00\" /* DOUBLE_ZERO */\n                            ? \"0\" /* NUMBER_ZERO */\n                            : el.value;\n                }\n                this._maskService.applyValueChanges(position, this._justPasted, this._code === \"Backspace\" /* BACKSPACE */ || this._code === \"Delete\" /* DELETE */, (shift, _backspaceShift) => {\n                    this._justPasted = false;\n                    caretShift = shift;\n                    backspaceShift = _backspaceShift;\n                });\n                // only set the selection if the element is active\n                if (this._getActiveElement() !== el) {\n                    return;\n                }\n                if (this._maskService.plusOnePosition) {\n                    position = position + 1;\n                    this._maskService.plusOnePosition = false;\n                }\n                // update position after applyValueChanges to prevent cursor on wrong position when it has an array of maskExpression\n                if (this._maskExpressionArray.length) {\n                    if (this._code === \"Backspace\" /* BACKSPACE */) {\n                        position = this.specialCharacters.includes(this._inputValue.slice(position - 1, position))\n                            ? position - 1\n                            : position;\n                    }\n                    else {\n                        position =\n                            el.selectionStart === 1\n                                ? el.selectionStart + this._maskService.prefix.length\n                                : el.selectionStart;\n                    }\n                }\n                this._position =\n                    this._position === 1 && this._inputValue.length === 1 ? null : this._position;\n                let positionToApply = this._position\n                    ? this._inputValue.length + position + caretShift\n                    : position +\n                        (this._code === \"Backspace\" /* BACKSPACE */ && !backspaceShift ? 0 : caretShift);\n                if (positionToApply > this._getActualInputLength()) {\n                    positionToApply =\n                        el.value === this._maskService.decimalMarker && el.value.length === 1\n                            ? this._getActualInputLength() + 1\n                            : this._getActualInputLength();\n                }\n                if (positionToApply < 0) {\n                    positionToApply = 0;\n                }\n                el.setSelectionRange(positionToApply, positionToApply);\n                this._position = null;\n            }\n            else {\n                console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof transformedValue);\n            }\n        }\n        else {\n            if (!this._maskValue) {\n                this.onChange(el.value);\n                return;\n            }\n            this._maskService.applyValueChanges(el.value.length, this._justPasted, this._code === \"Backspace\" /* BACKSPACE */ || this._code === \"Delete\" /* DELETE */);\n        }\n    }\n    // IME starts\n    onCompositionStart() {\n        this._isComposing = true;\n    }\n    // IME completes\n    onCompositionEnd(e) {\n        this._isComposing = false;\n        this._justPasted = true;\n        this.onInput(e);\n    }\n    onBlur(e) {\n        if (this._maskValue) {\n            const el = e.target;\n            if (this.leadZero && el.value.length > 0 && typeof this.decimalMarker === 'string') {\n                const maskExpression = this._maskService.maskExpression;\n                const precision = Number(this._maskService.maskExpression.slice(maskExpression.length - 1, maskExpression.length));\n                if (precision > 1) {\n                    el.value = this.suffix ? el.value.split(this.suffix).join('') : el.value;\n                    const decimalPart = el.value.split(this.decimalMarker)[1];\n                    el.value = el.value.includes(this.decimalMarker)\n                        ? el.value +\n                            \"0\" /* NUMBER_ZERO */.repeat(precision - decimalPart.length) +\n                            this.suffix\n                        : el.value +\n                            this.decimalMarker +\n                            \"0\" /* NUMBER_ZERO */.repeat(precision) +\n                            this.suffix;\n                    this._maskService.actualValue = el.value;\n                }\n            }\n            this._maskService.clearIfNotMatchFn();\n        }\n        this._isFocused = false;\n        this.onTouch();\n    }\n    onClick(e) {\n        if (!this._maskValue) {\n            return;\n        }\n        const el = e.target;\n        const posStart = 0;\n        const posEnd = 0;\n        if (el !== null &&\n            el.selectionStart !== null &&\n            el.selectionStart === el.selectionEnd &&\n            el.selectionStart > this._maskService.prefix.length &&\n            // eslint-disable-next-line\n            e.keyCode !== 38) {\n            if (this._maskService.showMaskTyped && !this.keepCharacterPositions) {\n                // We are showing the mask in the input\n                this._maskService.maskIsShown = this._maskService.showMaskInInput();\n                if (el.setSelectionRange &&\n                    this._maskService.prefix + this._maskService.maskIsShown === el.value) {\n                    // the input ONLY contains the mask, so position the cursor at the start\n                    el.focus();\n                    el.setSelectionRange(posStart, posEnd);\n                }\n                else {\n                    // the input contains some characters already\n                    if (el.selectionStart > this._maskService.actualValue.length) {\n                        // if the user clicked beyond our value's length, position the cursor at the end of our value\n                        el.setSelectionRange(this._maskService.actualValue.length, this._maskService.actualValue.length);\n                    }\n                }\n            }\n        }\n        const nextValue = el &&\n            (el.value === this._maskService.prefix\n                ? this._maskService.prefix + this._maskService.maskIsShown\n                : el.value);\n        /** Fix of cursor position jumping to end in most browsers no matter where cursor is inserted onFocus */\n        if (el && el.value !== nextValue) {\n            el.value = nextValue;\n        }\n        /** fix of cursor position with prefix when mouse click occur */\n        if (el &&\n            el.type !== 'number' &&\n            (el.selectionStart || el.selectionEnd) <=\n                this._maskService.prefix.length) {\n            el.selectionStart = this._maskService.prefix.length;\n            return;\n        }\n        /** select only inserted text */\n        if (el && el.selectionEnd > this._getActualInputLength()) {\n            el.selectionEnd = this._getActualInputLength();\n        }\n    }\n    // eslint-disable-next-line complexity\n    onKeyDown(e) {\n        if (!this._maskValue) {\n            return;\n        }\n        if (this._isComposing) {\n            // User finalize their choice from IME composition, so trigger onInput() for the composed text.\n            if (e.key === 'Enter')\n                this.onCompositionEnd(e);\n            return;\n        }\n        this._code = e.code ? e.code : e.key;\n        const el = e.target;\n        this._inputValue = el.value;\n        this._setMask();\n        if (el.type !== 'number') {\n            if (e.key === \"ArrowUp\" /* ARROW_UP */) {\n                e.preventDefault();\n            }\n            if (e.key === \"ArrowLeft\" /* ARROW_LEFT */ ||\n                e.key === \"Backspace\" /* BACKSPACE */ ||\n                e.key === \"Delete\" /* DELETE */) {\n                if (e.key === \"Backspace\" /* BACKSPACE */ && el.value.length === 0) {\n                    el.selectionStart = el.selectionEnd;\n                }\n                if (e.key === \"Backspace\" /* BACKSPACE */ && el.selectionStart !== 0) {\n                    // If specialChars is false, (shouldn't ever happen) then set to the defaults\n                    this.specialCharacters = this.specialCharacters?.length\n                        ? this.specialCharacters\n                        : this._config.specialCharacters;\n                    if (this.prefix.length > 1 &&\n                        el.selectionStart <= this.prefix.length) {\n                        el.setSelectionRange(this.prefix.length, el.selectionEnd);\n                    }\n                    else {\n                        if (this._inputValue.length !== el.selectionStart &&\n                            el.selectionStart !== 1) {\n                            while (this.specialCharacters.includes((this._inputValue[el.selectionStart - 1] ??\n                                \"\" /* EMPTY_STRING */).toString()) &&\n                                ((this.prefix.length >= 1 &&\n                                    el.selectionStart > this.prefix.length) ||\n                                    this.prefix.length === 0)) {\n                                el.setSelectionRange(el.selectionStart - 1, el.selectionEnd);\n                            }\n                        }\n                    }\n                }\n                this.checkSelectionOnDeletion(el);\n                if (this._maskService.prefix.length &&\n                    el.selectionStart <= this._maskService.prefix.length &&\n                    el.selectionEnd <= this._maskService.prefix.length) {\n                    e.preventDefault();\n                }\n                const cursorStart = el.selectionStart;\n                if (e.key === \"Backspace\" /* BACKSPACE */ &&\n                    !el.readOnly &&\n                    cursorStart === 0 &&\n                    el.selectionEnd === el.value.length &&\n                    el.value.length !== 0) {\n                    this._position = this._maskService.prefix ? this._maskService.prefix.length : 0;\n                    this._maskService.applyMask(this._maskService.prefix, this._maskService.maskExpression, this._position);\n                }\n            }\n            if (!!this.suffix &&\n                this.suffix.length > 1 &&\n                this._inputValue.length - this.suffix.length < el.selectionStart) {\n                el.setSelectionRange(this._inputValue.length - this.suffix.length, this._inputValue.length);\n            }\n            else if ((e.code === 'KeyA' && e.ctrlKey) ||\n                (e.code === 'KeyA' && e.metaKey) // Cmd + A (Mac)\n            ) {\n                el.setSelectionRange(0, this._getActualInputLength());\n                e.preventDefault();\n            }\n            this._maskService.selStart = el.selectionStart;\n            this._maskService.selEnd = el.selectionEnd;\n        }\n    }\n    /** It writes the value in the input */\n    async writeValue(controlValue) {\n        if (typeof controlValue === 'object' && controlValue !== null && 'value' in controlValue) {\n            if ('disable' in controlValue) {\n                this.setDisabledState(Boolean(controlValue.disable));\n            }\n            // eslint-disable-next-line no-param-reassign\n            controlValue = controlValue.value;\n        }\n        if (controlValue !== null) {\n            // eslint-disable-next-line no-param-reassign\n            controlValue = this.inputTransformFn\n                ? this.inputTransformFn(controlValue)\n                : controlValue;\n        }\n        if (typeof controlValue === 'string' ||\n            typeof controlValue === 'number' ||\n            controlValue === null ||\n            controlValue === undefined) {\n            if (controlValue === null || controlValue === undefined || controlValue === '') {\n                this._maskService._currentValue = '';\n                this._maskService._previousValue = '';\n            }\n            // eslint-disable-next-line no-param-reassign\n            let inputValue = controlValue;\n            if (typeof inputValue === 'number' ||\n                this._maskValue.startsWith(\"separator\" /* SEPARATOR */)) {\n                // eslint-disable-next-line no-param-reassign\n                inputValue = String(inputValue);\n                const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n                if (!Array.isArray(this._maskService.decimalMarker)) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue =\n                        this._maskService.decimalMarker !== localeDecimalMarker\n                            ? inputValue.replace(localeDecimalMarker, this._maskService.decimalMarker)\n                            : inputValue;\n                }\n                if (this._maskService.leadZero &&\n                    inputValue &&\n                    this.maskExpression &&\n                    this.dropSpecialCharacters !== false) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue = this._maskService._checkPrecision(this._maskService.maskExpression, inputValue);\n                }\n                if (this._maskService.decimalMarker === \",\" /* COMMA */) {\n                    // eslint-disable-next-line no-param-reassign\n                    inputValue = inputValue\n                        .toString()\n                        .replace(\".\" /* DOT */, \",\" /* COMMA */);\n                }\n                if (this.maskExpression?.startsWith(\"separator\" /* SEPARATOR */) && this.leadZero) {\n                    requestAnimationFrame(() => {\n                        this._maskService.applyMask(inputValue?.toString() ?? '', this._maskService.maskExpression);\n                    });\n                }\n                this._maskService.isNumberValue = true;\n            }\n            if (typeof inputValue !== 'string') {\n                // eslint-disable-next-line no-param-reassign\n                inputValue = '';\n            }\n            this._inputValue = inputValue;\n            this._setMask();\n            if ((inputValue && this._maskService.maskExpression) ||\n                (this._maskService.maskExpression &&\n                    (this._maskService.prefix || this._maskService.showMaskTyped))) {\n                // Let the service we know we are writing value so that triggering onChange function won't happen during applyMask\n                typeof this.inputTransformFn !== 'function'\n                    ? (this._maskService.writingValue = true)\n                    : '';\n                this._maskService.formElementProperty = [\n                    'value',\n                    this._maskService.applyMask(inputValue, this._maskService.maskExpression),\n                ];\n                // Let the service know we've finished writing value\n                typeof this.inputTransformFn !== 'function'\n                    ? (this._maskService.writingValue = false)\n                    : '';\n            }\n            else {\n                this._maskService.formElementProperty = ['value', inputValue];\n            }\n            this._inputValue = inputValue;\n        }\n        else {\n            console.warn('Ngx-mask writeValue work with string | number, your current value:', typeof controlValue);\n        }\n    }\n    registerOnChange(fn) {\n        this._maskService.onChange = this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouch = fn;\n    }\n    _getActiveElement(document = this.document) {\n        const shadowRootEl = document?.activeElement?.shadowRoot;\n        if (!shadowRootEl?.activeElement) {\n            return document.activeElement;\n        }\n        else {\n            return this._getActiveElement(shadowRootEl);\n        }\n    }\n    checkSelectionOnDeletion(el) {\n        el.selectionStart = Math.min(Math.max(this.prefix.length, el.selectionStart), this._inputValue.length - this.suffix.length);\n        el.selectionEnd = Math.min(Math.max(this.prefix.length, el.selectionEnd), this._inputValue.length - this.suffix.length);\n    }\n    /** It disables the input element */\n    setDisabledState(isDisabled) {\n        this._maskService.formElementProperty = ['disabled', isDisabled];\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    _applyMask() {\n        this._maskService.maskExpression = this._maskService._repeatPatternSymbols(this._maskValue || '');\n        this._maskService.formElementProperty = [\n            'value',\n            this._maskService.applyMask(this._inputValue, this._maskService.maskExpression),\n        ];\n    }\n    _validateTime(value) {\n        const rowMaskLen = this._maskValue\n            .split(\"\" /* EMPTY_STRING */)\n            .filter((s) => s !== ':').length;\n        if (!value) {\n            return null; // Don't validate empty values to allow for optional form control\n        }\n        if ((+(value[value.length - 1] ?? -1) === 0 && value.length < rowMaskLen) ||\n            value.length <= rowMaskLen - 2) {\n            return this._createValidationError(value);\n        }\n        return null;\n    }\n    _getActualInputLength() {\n        return (this._maskService.actualValue.length ||\n            this._maskService.actualValue.length + this._maskService.prefix.length);\n    }\n    _createValidationError(actualValue) {\n        return {\n            mask: {\n                requiredMask: this._maskValue,\n                actualValue,\n            },\n        };\n    }\n    _setMask() {\n        this._maskExpressionArray.some((mask) => {\n            const specialChart = mask\n                .split(\"\" /* EMPTY_STRING */)\n                .some((char) => this._maskService.specialCharacters.includes(char));\n            if ((specialChart && this._inputValue && !mask.includes(\"S\" /* LETTER_S */)) ||\n                mask.includes(\"{\" /* CURLY_BRACKETS_LEFT */)) {\n                const test = this._maskService.removeMask(this._inputValue)?.length <=\n                    this._maskService.removeMask(mask)?.length;\n                if (test) {\n                    this._maskValue =\n                        this.maskExpression =\n                            this._maskService.maskExpression =\n                                mask.includes(\"{\" /* CURLY_BRACKETS_LEFT */)\n                                    ? this._maskService._repeatPatternSymbols(mask)\n                                    : mask;\n                    return test;\n                }\n                else {\n                    const expression = this._maskExpressionArray[this._maskExpressionArray.length - 1] ??\n                        \"\" /* EMPTY_STRING */;\n                    this._maskValue =\n                        this.maskExpression =\n                            this._maskService.maskExpression =\n                                expression.includes(\"{\" /* CURLY_BRACKETS_LEFT */)\n                                    ? this._maskService._repeatPatternSymbols(expression)\n                                    : expression;\n                }\n            }\n            else {\n                const check = this._maskService\n                    .removeMask(this._inputValue)\n                    ?.split(\"\" /* EMPTY_STRING */)\n                    .every((character, index) => {\n                    const indexMask = mask.charAt(index);\n                    return this._maskService._checkSymbolMask(character, indexMask);\n                });\n                if (check) {\n                    this._maskValue = this.maskExpression = this._maskService.maskExpression = mask;\n                    return check;\n                }\n            }\n        });\n    }\n}\nNgxMaskDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskDirective, deps: [{ token: DOCUMENT }, { token: NGX_MASK_CONFIG }, { token: NgxMaskService }], target: i0.ɵɵFactoryTarget.Directive });\nNgxMaskDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.12\", type: NgxMaskDirective, selector: \"input[mask], textarea[mask]\", inputs: { maskExpression: [\"mask\", \"maskExpression\"], specialCharacters: \"specialCharacters\", patterns: \"patterns\", prefix: \"prefix\", suffix: \"suffix\", thousandSeparator: \"thousandSeparator\", decimalMarker: \"decimalMarker\", dropSpecialCharacters: \"dropSpecialCharacters\", hiddenInput: \"hiddenInput\", showMaskTyped: \"showMaskTyped\", placeHolderCharacter: \"placeHolderCharacter\", shownMaskExpression: \"shownMaskExpression\", showTemplate: \"showTemplate\", clearIfNotMatch: \"clearIfNotMatch\", validation: \"validation\", separatorLimit: \"separatorLimit\", allowNegativeNumbers: \"allowNegativeNumbers\", leadZeroDateTime: \"leadZeroDateTime\", leadZero: \"leadZero\", triggerOnMaskChange: \"triggerOnMaskChange\", apm: \"apm\", inputTransformFn: \"inputTransformFn\", outputTransformFn: \"outputTransformFn\", keepCharacterPositions: \"keepCharacterPositions\" }, outputs: { maskFilled: \"maskFilled\" }, host: { listeners: { \"paste\": \"onPaste()\", \"focus\": \"onFocus($event)\", \"ngModelChange\": \"onModelChange($event)\", \"input\": \"onInput($event)\", \"compositionstart\": \"onCompositionStart($event)\", \"compositionend\": \"onCompositionEnd($event)\", \"blur\": \"onBlur($event)\", \"click\": \"onClick($event)\", \"keydown\": \"onKeyDown($event)\" } }, providers: [\n        {\n            provide: NG_VALUE_ACCESSOR,\n            useExisting: forwardRef(() => NgxMaskDirective),\n            multi: true,\n        },\n        {\n            provide: NG_VALIDATORS,\n            useExisting: forwardRef(() => NgxMaskDirective),\n            multi: true,\n        },\n        NgxMaskService,\n    ], exportAs: [\"mask\", \"ngxMask\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[mask], textarea[mask]',\n                    providers: [\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => NgxMaskDirective),\n                            multi: true,\n                        },\n                        {\n                            provide: NG_VALIDATORS,\n                            useExisting: forwardRef(() => NgxMaskDirective),\n                            multi: true,\n                        },\n                        NgxMaskService,\n                    ],\n                    exportAs: 'mask,ngxMask',\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NGX_MASK_CONFIG]\n                }] }, { type: NgxMaskService }]; }, propDecorators: { maskExpression: [{\n                type: Input,\n                args: ['mask']\n            }], specialCharacters: [{\n                type: Input\n            }], patterns: [{\n                type: Input\n            }], prefix: [{\n                type: Input\n            }], suffix: [{\n                type: Input\n            }], thousandSeparator: [{\n                type: Input\n            }], decimalMarker: [{\n                type: Input\n            }], dropSpecialCharacters: [{\n                type: Input\n            }], hiddenInput: [{\n                type: Input\n            }], showMaskTyped: [{\n                type: Input\n            }], placeHolderCharacter: [{\n                type: Input\n            }], shownMaskExpression: [{\n                type: Input\n            }], showTemplate: [{\n                type: Input\n            }], clearIfNotMatch: [{\n                type: Input\n            }], validation: [{\n                type: Input\n            }], separatorLimit: [{\n                type: Input\n            }], allowNegativeNumbers: [{\n                type: Input\n            }], leadZeroDateTime: [{\n                type: Input\n            }], leadZero: [{\n                type: Input\n            }], triggerOnMaskChange: [{\n                type: Input\n            }], apm: [{\n                type: Input\n            }], inputTransformFn: [{\n                type: Input\n            }], outputTransformFn: [{\n                type: Input\n            }], keepCharacterPositions: [{\n                type: Input\n            }], maskFilled: [{\n                type: Output\n            }], onPaste: [{\n                type: HostListener,\n                args: ['paste']\n            }], onFocus: [{\n                type: HostListener,\n                args: ['focus', ['$event']]\n            }], onModelChange: [{\n                type: HostListener,\n                args: ['ngModelChange', ['$event']]\n            }], onInput: [{\n                type: HostListener,\n                args: ['input', ['$event']]\n            }], onCompositionStart: [{\n                type: HostListener,\n                args: ['compositionstart', ['$event']]\n            }], onCompositionEnd: [{\n                type: HostListener,\n                args: ['compositionend', ['$event']]\n            }], onBlur: [{\n                type: HostListener,\n                args: ['blur', ['$event']]\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }], onKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nclass NgxMaskPipe {\n    constructor(_maskService) {\n        this._maskService = _maskService;\n        this.defaultOptions = {};\n        this._maskExpressionArray = [];\n        this.mask = '';\n    }\n    transform(value, mask, { patterns, ...config } = {}) {\n        const currentConfig = {\n            maskExpression: mask,\n            ...this.defaultOptions,\n            ...config,\n            patterns: {\n                ...this._maskService.patterns,\n                ...patterns,\n            },\n        };\n        Object.entries(currentConfig).forEach(([key, value]) => {\n            //eslint-disable-next-line  @typescript-eslint/no-explicit-any\n            this._maskService[key] = value;\n        });\n        if (mask.includes('||')) {\n            if (mask.split('||').length > 1) {\n                this._maskExpressionArray = mask.split('||').sort((a, b) => {\n                    return a.length - b.length;\n                });\n                this._setMask(value);\n                return this._maskService.applyMask(`${value}`, this.mask);\n            }\n            else {\n                this._maskExpressionArray = [];\n                return this._maskService.applyMask(`${value}`, this.mask);\n            }\n        }\n        if (mask.includes(\"{\" /* CURLY_BRACKETS_LEFT */)) {\n            return this._maskService.applyMask(`${value}`, this._maskService._repeatPatternSymbols(mask));\n        }\n        if (mask.startsWith(\"separator\" /* SEPARATOR */)) {\n            if (config.decimalMarker) {\n                this._maskService.decimalMarker = config.decimalMarker;\n            }\n            if (config.thousandSeparator) {\n                this._maskService.thousandSeparator = config.thousandSeparator;\n            }\n            if (config.leadZero) {\n                // eslint-disable-next-line no-param-reassign\n                this._maskService.leadZero = config.leadZero;\n            }\n            // eslint-disable-next-line no-param-reassign\n            value = String(value);\n            const localeDecimalMarker = this._maskService.currentLocaleDecimalMarker();\n            if (!Array.isArray(this._maskService.decimalMarker)) {\n                // eslint-disable-next-line no-param-reassign\n                value =\n                    this._maskService.decimalMarker !== localeDecimalMarker\n                        ? value.replace(localeDecimalMarker, this._maskService.decimalMarker)\n                        : value;\n            }\n            if (this._maskService.leadZero &&\n                value &&\n                this._maskService.dropSpecialCharacters !== false) {\n                // eslint-disable-next-line no-param-reassign\n                value = this._maskService._checkPrecision(mask, value);\n            }\n            if (this._maskService.decimalMarker === \",\" /* COMMA */) {\n                // eslint-disable-next-line no-param-reassign\n                value = value.toString().replace(\".\" /* DOT */, \",\" /* COMMA */);\n            }\n            this._maskService.isNumberValue = true;\n        }\n        if (value === null || value === undefined) {\n            return this._maskService.applyMask('', mask);\n        }\n        return this._maskService.applyMask(`${value}`, mask);\n    }\n    _setMask(value) {\n        if (this._maskExpressionArray.length > 0) {\n            this._maskExpressionArray.some((mask) => {\n                const test = this._maskService.removeMask(value)?.length <=\n                    this._maskService.removeMask(mask)?.length;\n                if (value && test) {\n                    this.mask = mask;\n                    return test;\n                }\n                else {\n                    const expression = this._maskExpressionArray[this._maskExpressionArray.length - 1] ??\n                        \"\" /* EMPTY_STRING */;\n                    this.mask = expression;\n                }\n            });\n        }\n    }\n}\nNgxMaskPipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskPipe, deps: [{ token: NgxMaskService }], target: i0.ɵɵFactoryTarget.Pipe });\nNgxMaskPipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskPipe, name: \"mask\" });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'mask',\n                    pure: true,\n                }]\n        }], ctorParameters: function () { return [{ type: NgxMaskService }]; } });\n\n/**\n * @internal\n */\nfunction _configFactory(initConfig, configValue) {\n    return configValue instanceof Function\n        ? { ...initConfig, ...configValue() }\n        : { ...initConfig, ...configValue };\n}\nclass NgxMaskModule {\n    static forRoot(configValue) {\n        return {\n            ngModule: NgxMaskModule,\n            providers: [\n                {\n                    provide: NEW_CONFIG,\n                    useValue: configValue,\n                },\n                {\n                    provide: INITIAL_CONFIG,\n                    useValue: initialConfig,\n                },\n                {\n                    provide: NGX_MASK_CONFIG,\n                    useFactory: _configFactory,\n                    deps: [INITIAL_CONFIG, NEW_CONFIG],\n                },\n                NgxMaskService,\n            ],\n        };\n    }\n    static forChild() {\n        return {\n            ngModule: NgxMaskModule,\n        };\n    }\n}\nNgxMaskModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNgxMaskModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskModule, declarations: [NgxMaskDirective, NgxMaskPipe], exports: [NgxMaskDirective, NgxMaskPipe] });\nNgxMaskModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.12\", ngImport: i0, type: NgxMaskModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [NgxMaskDirective, NgxMaskPipe],\n                    declarations: [NgxMaskDirective, NgxMaskPipe],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INITIAL_CONFIG, NEW_CONFIG, NGX_MASK_CONFIG, NgxMaskDirective, NgxMaskModule, NgxMaskPipe, NgxMaskService, _configFactory, initialConfig, timeMasks, withoutValidation };\n"]}, "metadata": {}, "sourceType": "module"}
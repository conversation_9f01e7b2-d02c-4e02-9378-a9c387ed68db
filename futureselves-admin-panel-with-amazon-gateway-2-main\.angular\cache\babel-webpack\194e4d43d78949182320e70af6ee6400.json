{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport * as i0 from '@angular/core';\nimport { Pipe, Injectable, Directive, Input, EventEmitter, Component, Output, ViewChild, HostBinding, HostListener, TemplateRef, ContentChildren, forwardRef, ContentChild, ViewChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/forms';\nimport { FormControl, FormGroup, NG_VALUE_ACCESSOR, ReactiveFormsModule, FormsModule, COMPOSITION_BUFFER_MODE } from '@angular/forms';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1$1 from 'ng2-material-dropdown';\nimport { Ng2Dropdown, Ng2DropdownModule } from 'ng2-material-dropdown';\nimport { filter as filter$1, map, first as first$1, debounceTime as debounceTime$1 } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes } from '@angular/animations';\nimport { first, distinctUntilChanged, debounceTime, filter } from 'rxjs/operators';\nconst _c0 = [\"input\"];\n\nfunction TagComponent_div_1_ng_template_1_Template(rf, ctx) {}\n\nconst _c1 = function (a0, a1) {\n  return {\n    item: a0,\n    index: a1\n  };\n};\n\nfunction TagComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TagComponent_div_1_ng_template_1_Template, 0, 0, \"ng-template\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"contenteditable\", ctx_r0.editing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c1, ctx_r0.model, ctx_r0.index))(\"ngTemplateOutlet\", ctx_r0.template);\n  }\n}\n\nfunction TagComponent_div_2_delete_icon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"delete-icon\", 8);\n    i0.ɵɵlistener(\"click\", function TagComponent_div_2_delete_icon_3_Template_delete_icon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return ctx_r5.remove($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TagComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelementStart(1, \"div\", 6);\n    i0.ɵɵlistener(\"keydown.enter\", function TagComponent_div_2_Template_div_keydown_enter_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return ctx_r7.disableEditMode($event);\n    })(\"keydown.escape\", function TagComponent_div_2_Template_div_keydown_escape_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return ctx_r9.disableEditMode($event);\n    })(\"click\", function TagComponent_div_2_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return ctx_r10.editing ? $event.stopPropagation() : undefined;\n    })(\"blur\", function TagComponent_div_2_Template_div_blur_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return ctx_r11.onBlurred($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, TagComponent_div_2_delete_icon_3_Template, 1, 0, \"delete-icon\", 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"contenteditable\", ctx_r1.editing)(\"title\", ctx_r1.getDisplayValue(ctx_r1.model));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDisplayValue(ctx_r1.model), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isDeleteIconVisible());\n  }\n}\n\nfunction TagComponent_tag_ripple_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tag-ripple\", 9);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"state\", ctx_r2.rippleState);\n    i0.ɵɵattribute(\"tabindex\", -1);\n  }\n}\n\nfunction TagInputDropdown_ng2_menu_item_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n    i0.ɵɵpipe(1, \"highlight\");\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind2(1, 1, item_r1[ctx_r4.displayBy], ctx_r4.tagInput.inputForm.value.value), i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction TagInputDropdown_ng2_menu_item_2_2_ng_template_0_Template(rf, ctx) {}\n\nconst _c2 = function (a0, a1, a2) {\n  return {\n    item: a0,\n    index: a1,\n    last: a2\n  };\n};\n\nfunction TagInputDropdown_ng2_menu_item_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TagInputDropdown_ng2_menu_item_2_2_ng_template_0_Template, 0, 0, \"ng-template\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    const item_r1 = ctx_r8.$implicit;\n    const index_r2 = ctx_r8.index;\n    const last_r3 = ctx_r8.last;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r5.templates.first)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(2, _c2, item_r1, index_r2, last_r3));\n  }\n}\n\nfunction TagInputDropdown_ng2_menu_item_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ng2-menu-item\", 3);\n    i0.ɵɵtemplate(1, TagInputDropdown_ng2_menu_item_2_span_1_Template, 2, 4, \"span\", 4);\n    i0.ɵɵtemplate(2, TagInputDropdown_ng2_menu_item_2_2_Template, 1, 6, undefined, 5);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", item_r1)(\"ngSwitch\", !!ctx_r0.templates.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", false);\n  }\n}\n\nfunction TagInputComponent_tag_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tag\", 6);\n    i0.ɵɵlistener(\"onSelect\", function TagInputComponent_tag_2_Template_tag_onSelect_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const item_r3 = restoredCtx.$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return ctx_r5.selectItem(item_r3);\n    })(\"onRemove\", function TagInputComponent_tag_2_Template_tag_onRemove_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const item_r3 = restoredCtx.$implicit;\n      const i_r4 = restoredCtx.index;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return ctx_r7.onRemoveRequested(item_r3, i_r4);\n    })(\"onKeyDown\", function TagInputComponent_tag_2_Template_tag_onKeyDown_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return ctx_r8.handleKeydown($event);\n    })(\"onTagEdited\", function TagInputComponent_tag_2_Template_tag_onTagEdited_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return ctx_r9.updateEditedTag($event);\n    })(\"onBlur\", function TagInputComponent_tag_2_Template_tag_onBlur_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const i_r4 = restoredCtx.index;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return ctx_r10.onTagBlurred($event, i_r4);\n    })(\"dragstart\", function TagInputComponent_tag_2_Template_tag_dragstart_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const item_r3 = restoredCtx.$implicit;\n      const i_r4 = restoredCtx.index;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return ctx_r11.dragZone ? ctx_r11.onDragStarted($event, item_r3, i_r4) : undefined;\n    })(\"drop\", function TagInputComponent_tag_2_Template_tag_drop_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const i_r4 = restoredCtx.index;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return ctx_r12.dragZone ? ctx_r12.onTagDropped($event, i_r4) : undefined;\n    })(\"dragenter\", function TagInputComponent_tag_2_Template_tag_dragenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return ctx_r13.dragZone ? ctx_r13.onDragOver($event) : undefined;\n    })(\"dragover\", function TagInputComponent_tag_2_Template_tag_dragover_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const i_r4 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return ctx_r14.dragZone ? ctx_r14.onDragOver($event, i_r4) : undefined;\n    })(\"dragleave\", function TagInputComponent_tag_2_Template_tag_dragleave_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return ctx_r15.dragZone ? ctx_r15.dragProvider.onDragEnd() : undefined;\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"draggable\", ctx_r0.editable);\n    i0.ɵɵproperty(\"canAddTag\", ctx_r0.isTagValid)(\"disabled\", ctx_r0.disable)(\"@animation\", ctx_r0.animationMetadata)(\"hasRipple\", ctx_r0.ripple)(\"index\", i_r4)(\"removable\", ctx_r0.removable)(\"editable\", ctx_r0.editable)(\"displayBy\", ctx_r0.displayBy)(\"identifyBy\", ctx_r0.identifyBy)(\"template\", !!ctx_r0.hasCustomTemplate() ? ctx_r0.templates.first : undefined)(\"draggable\", ctx_r0.dragZone)(\"model\", item_r3);\n    i0.ɵɵattribute(\"tabindex\", 0);\n  }\n}\n\nfunction TagInputComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n}\n\nfunction TagInputComponent_div_6_p_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 10);\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const error_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(error_r17);\n  }\n}\n\nfunction TagInputComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, TagInputComponent_div_6_p_1_Template, 3, 1, \"p\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.theme);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.errors);\n  }\n}\n\nconst _c3 = [\"*\"];\n\nconst escape = s => s.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\n\nclass HighlightPipe {\n  /**\n   * @name transform\n   * @param value {string}\n   * @param arg {string}\n   */\n  transform(value, arg) {\n    if (!arg.trim()) {\n      return value;\n    }\n\n    try {\n      const regex = new RegExp(`(${escape(arg)})`, 'i');\n      return value.replace(regex, '<b>$1</b>');\n    } catch (e) {\n      return value;\n    }\n  }\n\n}\n\nHighlightPipe.ɵfac = function HighlightPipe_Factory(t) {\n  return new (t || HighlightPipe)();\n};\n\nHighlightPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"highlight\",\n  type: HighlightPipe,\n  pure: true\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighlightPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'highlight'\n    }]\n  }], null, null);\n})();\n/*\n** constants and default values for <tag-input>\n */\n\n\nconst PLACEHOLDER = '+ Tag';\nconst SECONDARY_PLACEHOLDER = 'Enter a new tag';\nconst KEYDOWN = 'keydown';\nconst KEYUP = 'keyup';\nconst FOCUS = 'focus';\nconst MAX_ITEMS_WARNING = 'The number of items specified was greater than the property max-items.';\nconst ACTIONS_KEYS = {\n  DELETE: 'DELETE',\n  SWITCH_PREV: 'SWITCH_PREV',\n  SWITCH_NEXT: 'SWITCH_NEXT',\n  TAB: 'TAB'\n};\nconst KEY_PRESS_ACTIONS = {\n  8: ACTIONS_KEYS.DELETE,\n  46: ACTIONS_KEYS.DELETE,\n  37: ACTIONS_KEYS.SWITCH_PREV,\n  39: ACTIONS_KEYS.SWITCH_NEXT,\n  9: ACTIONS_KEYS.TAB\n};\nconst DRAG_AND_DROP_KEY = 'Text';\nconst NEXT = 'NEXT';\nconst PREV = 'PREV';\n\nclass DragProvider {\n  constructor() {\n    this.state = {\n      dragging: false,\n      dropping: false,\n      index: undefined\n    };\n  }\n  /**\n   * @name setDraggedItem\n   * @param event\n   * @param tag\n   */\n\n\n  setDraggedItem(event, tag) {\n    if (event && event.dataTransfer) {\n      event.dataTransfer.setData(DRAG_AND_DROP_KEY, JSON.stringify(tag));\n    }\n  }\n  /**\n   * @name getDraggedItem\n   * @param event\n   */\n\n\n  getDraggedItem(event) {\n    if (event && event.dataTransfer) {\n      const data = event.dataTransfer.getData(DRAG_AND_DROP_KEY);\n\n      try {\n        return JSON.parse(data);\n      } catch {\n        return;\n      }\n    }\n  }\n  /**\n   * @name setSender\n   * @param sender\n   */\n\n\n  setSender(sender) {\n    this.sender = sender;\n  }\n  /**\n   * @name setReceiver\n   * @param receiver\n   */\n\n\n  setReceiver(receiver) {\n    this.receiver = receiver;\n  }\n  /**\n   * @name onTagDropped\n   * @param tag\n   * @param indexDragged\n   * @param indexDropped\n   */\n\n\n  onTagDropped(tag, indexDragged, indexDropped) {\n    this.onDragEnd();\n    this.sender.onRemoveRequested(tag, indexDragged);\n    this.receiver.onAddingRequested(false, tag, indexDropped);\n  }\n  /**\n   * @name setState\n   * @param state\n   */\n\n\n  setState(state) {\n    this.state = { ...this.state,\n      ...state\n    };\n  }\n  /**\n   * @name getState\n   * @param key\n   */\n\n\n  getState(key) {\n    return key ? this.state[key] : this.state;\n  }\n  /**\n   * @name onDragEnd\n   */\n\n\n  onDragEnd() {\n    this.setState({\n      dragging: false,\n      dropping: false,\n      index: undefined\n    });\n  }\n\n}\n\nDragProvider.ɵfac = function DragProvider_Factory(t) {\n  return new (t || DragProvider)();\n};\n\nDragProvider.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DragProvider,\n  factory: DragProvider.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragProvider, [{\n    type: Injectable\n  }], null, null);\n})();\n\nconst defaults = {\n  tagInput: {\n    separatorKeys: [],\n    separatorKeyCodes: [],\n    maxItems: Infinity,\n    placeholder: PLACEHOLDER,\n    secondaryPlaceholder: SECONDARY_PLACEHOLDER,\n    validators: [],\n    asyncValidators: [],\n    onlyFromAutocomplete: false,\n    errorMessages: {},\n    theme: '',\n    onTextChangeDebounce: 250,\n    inputId: null,\n    inputClass: '',\n    clearOnBlur: false,\n    hideForm: false,\n    addOnBlur: false,\n    addOnPaste: false,\n    pasteSplitPattern: ',',\n    blinkIfDupe: true,\n    removable: true,\n    editable: false,\n    allowDupes: false,\n    modelAsStrings: false,\n    trimTags: true,\n    ripple: true,\n    tabIndex: '',\n    disable: false,\n    dragZone: '',\n    onRemoving: undefined,\n    onAdding: undefined,\n    displayBy: 'display',\n    identifyBy: 'value',\n    animationDuration: {\n      enter: '250ms',\n      leave: '150ms'\n    }\n  },\n  dropdown: {\n    displayBy: 'display',\n    identifyBy: 'value',\n    appendToBody: true,\n    offset: '50 0',\n    focusFirstElement: false,\n    showDropdownIfEmpty: false,\n    minimumTextLength: 1,\n    limitItemsTo: Infinity,\n    keepOpen: true,\n    dynamicUpdate: true,\n    zIndex: 1000,\n    matchingFn\n  }\n};\n/**\n * @name matchingFn\n * @param this\n * @param value\n * @param target\n */\n\nfunction matchingFn(value, target) {\n  const targetValue = target[this.displayBy].toString();\n  return targetValue && targetValue.toLowerCase().indexOf(value.toLowerCase()) >= 0;\n}\n\nclass OptionsProvider {\n  setOptions(options) {\n    OptionsProvider.defaults.tagInput = { ...defaults.tagInput,\n      ...options.tagInput\n    };\n    OptionsProvider.defaults.dropdown = { ...defaults.dropdown,\n      ...options.dropdown\n    };\n  }\n\n}\n\nOptionsProvider.defaults = defaults;\n\nfunction isObject(obj) {\n  return obj === Object(obj);\n}\n\nclass TagInputAccessor {\n  constructor() {\n    this._items = [];\n    /**\n     * @name displayBy\n     */\n\n    this.displayBy = OptionsProvider.defaults.tagInput.displayBy;\n    /**\n     * @name identifyBy\n     */\n\n    this.identifyBy = OptionsProvider.defaults.tagInput.identifyBy;\n  }\n\n  get items() {\n    return this._items;\n  }\n\n  set items(items) {\n    this._items = items;\n\n    this._onChangeCallback(this._items);\n  }\n\n  onTouched() {\n    this._onTouchedCallback();\n  }\n\n  writeValue(items) {\n    this._items = items || [];\n  }\n\n  registerOnChange(fn) {\n    this._onChangeCallback = fn;\n  }\n\n  registerOnTouched(fn) {\n    this._onTouchedCallback = fn;\n  }\n  /**\n   * @name getItemValue\n   * @param item\n   * @param fromDropdown\n   */\n\n\n  getItemValue(item, fromDropdown = false) {\n    const property = fromDropdown && this.dropdown ? this.dropdown.identifyBy : this.identifyBy;\n    return isObject(item) ? item[property] : item;\n  }\n  /**\n   * @name getItemDisplay\n   * @param item\n   * @param fromDropdown\n   */\n\n\n  getItemDisplay(item, fromDropdown = false) {\n    const property = fromDropdown && this.dropdown ? this.dropdown.displayBy : this.displayBy;\n    return isObject(item) ? item[property] : item;\n  }\n  /**\n   * @name getItemsWithout\n   * @param index\n   */\n\n\n  getItemsWithout(index) {\n    return this.items.filter((item, position) => position !== index);\n  }\n\n}\n\nTagInputAccessor.ɵfac = function TagInputAccessor_Factory(t) {\n  return new (t || TagInputAccessor)();\n};\n\nTagInputAccessor.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TagInputAccessor,\n  inputs: {\n    displayBy: \"displayBy\",\n    identifyBy: \"identifyBy\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TagInputAccessor, [{\n    type: Directive\n  }], null, {\n    displayBy: [{\n      type: Input\n    }],\n    identifyBy: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @name listen\n * @param listenerType\n * @param action\n * @param condition\n */\n\n\nfunction listen(listenerType, action, condition = true) {\n  // if the event provided does not exist, throw an error\n  if (!this.listeners.hasOwnProperty(listenerType)) {\n    throw new Error('The event entered may be wrong');\n  } // if a condition is present and is false, exit early\n\n\n  if (!condition) {\n    return;\n  } // fire listener\n\n\n  this.listeners[listenerType].push(action);\n}\n\nclass TagInputForm {\n  constructor() {\n    /**\n     * @name onSubmit\n     */\n    this.onSubmit = new EventEmitter();\n    /**\n     * @name onBlur\n     */\n\n    this.onBlur = new EventEmitter();\n    /**\n     * @name onFocus\n     */\n\n    this.onFocus = new EventEmitter();\n    /**\n     * @name onKeyup\n     */\n\n    this.onKeyup = new EventEmitter();\n    /**\n     * @name onKeydown\n     */\n\n    this.onKeydown = new EventEmitter();\n    /**\n     * @name inputTextChange\n     */\n\n    this.inputTextChange = new EventEmitter();\n    /**\n     * @name validators\n     */\n\n    this.validators = [];\n    /**\n     * @name asyncValidators\n     * @desc array of AsyncValidator that are used to validate the tag before it gets appended to the list\n     */\n\n    this.asyncValidators = [];\n    /**\n     * @name tabindex\n     * @desc pass through the specified tabindex to the input\n     */\n\n    this.tabindex = '';\n    /**\n     * @name disabled\n     */\n\n    this.disabled = false;\n    this.item = new FormControl({\n      value: '',\n      disabled: this.disabled\n    });\n  }\n  /**\n   * @name inputText\n   */\n\n\n  get inputText() {\n    return this.item.value;\n  }\n  /**\n   * @name inputText\n   * @param text {string}\n   */\n\n\n  set inputText(text) {\n    this.item.setValue(text);\n    this.inputTextChange.emit(text);\n  }\n\n  ngOnInit() {\n    this.item.setValidators(this.validators);\n    this.item.setAsyncValidators(this.asyncValidators); // creating form\n\n    this.form = new FormGroup({\n      item: this.item\n    });\n  }\n\n  ngOnChanges(changes) {\n    if (changes.disabled && !changes.disabled.firstChange) {\n      if (changes.disabled.currentValue) {\n        this.form.controls['item'].disable();\n      } else {\n        this.form.controls['item'].enable();\n      }\n    }\n  }\n  /**\n   * @name value\n   */\n\n\n  get value() {\n    return this.form.get('item');\n  }\n  /**\n   * @name isInputFocused\n   */\n\n\n  isInputFocused() {\n    const doc = typeof document !== 'undefined' ? document : undefined;\n    return doc ? doc.activeElement === this.input.nativeElement : false;\n  }\n  /**\n   * @name getErrorMessages\n   * @param messages\n   */\n\n\n  getErrorMessages(messages) {\n    return Object.keys(messages).filter(err => this.value.hasError(err)).map(err => messages[err]);\n  }\n  /**\n   * @name hasErrors\n   */\n\n\n  hasErrors() {\n    const {\n      dirty,\n      value,\n      valid\n    } = this.form;\n    return dirty && value.item && !valid;\n  }\n  /**\n   * @name focus\n   */\n\n\n  focus() {\n    this.input.nativeElement.focus();\n  }\n  /**\n   * @name blur\n   */\n\n\n  blur() {\n    this.input.nativeElement.blur();\n  }\n  /**\n   * @name getElementPosition\n   */\n\n\n  getElementPosition() {\n    return this.input.nativeElement.getBoundingClientRect();\n  }\n  /**\n   * - removes input from the component\n   * @name destroy\n   */\n\n\n  destroy() {\n    const input = this.input.nativeElement;\n    input.parentElement.removeChild(input);\n  }\n  /**\n   * @name onKeyDown\n   * @param $event\n   */\n\n\n  onKeyDown($event) {\n    this.inputText = this.value.value;\n\n    if ($event.key === 'Enter') {\n      this.submit($event);\n    } else {\n      return this.onKeydown.emit($event);\n    }\n  }\n  /**\n   * @name onKeyUp\n   * @param $event\n   */\n\n\n  onKeyUp($event) {\n    this.inputText = this.value.value;\n    return this.onKeyup.emit($event);\n  }\n  /**\n   * @name submit\n   */\n\n\n  submit($event) {\n    $event.preventDefault();\n    this.onSubmit.emit($event);\n  }\n\n}\n\nTagInputForm.ɵfac = function TagInputForm_Factory(t) {\n  return new (t || TagInputForm)();\n};\n\nTagInputForm.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TagInputForm,\n  selectors: [[\"tag-input-form\"]],\n  viewQuery: function TagInputForm_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n    }\n  },\n  inputs: {\n    placeholder: \"placeholder\",\n    validators: \"validators\",\n    asyncValidators: \"asyncValidators\",\n    inputId: \"inputId\",\n    inputClass: \"inputClass\",\n    tabindex: \"tabindex\",\n    disabled: \"disabled\",\n    inputText: \"inputText\"\n  },\n  outputs: {\n    onSubmit: \"onSubmit\",\n    onBlur: \"onBlur\",\n    onFocus: \"onFocus\",\n    onKeyup: \"onKeyup\",\n    onKeydown: \"onKeydown\",\n    inputTextChange: \"inputTextChange\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 3,\n  vars: 8,\n  consts: [[3, \"formGroup\", \"ngSubmit\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"minlength\", \"1\", \"formControlName\", \"item\", 1, \"ng2-tag-input__text-input\", 3, \"tabindex\", \"ngClass\", \"focus\", \"blur\", \"keydown\", \"keyup\"], [\"input\", \"\"]],\n  template: function TagInputForm_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"form\", 0);\n      i0.ɵɵlistener(\"ngSubmit\", function TagInputForm_Template_form_ngSubmit_0_listener($event) {\n        return ctx.submit($event);\n      });\n      i0.ɵɵelementStart(1, \"input\", 1, 2);\n      i0.ɵɵlistener(\"focus\", function TagInputForm_Template_input_focus_1_listener($event) {\n        return ctx.onFocus.emit($event);\n      })(\"blur\", function TagInputForm_Template_input_blur_1_listener($event) {\n        return ctx.onBlur.emit($event);\n      })(\"keydown\", function TagInputForm_Template_input_keydown_1_listener($event) {\n        return ctx.onKeyDown($event);\n      })(\"keyup\", function TagInputForm_Template_input_keyup_1_listener($event) {\n        return ctx.onKeyUp($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"formGroup\", ctx.form);\n      i0.ɵɵadvance(1);\n      i0.ɵɵpropertyInterpolate(\"tabindex\", ctx.disabled ? -1 : ctx.tabindex ? ctx.tabindex : 0);\n      i0.ɵɵproperty(\"ngClass\", ctx.inputClass);\n      i0.ɵɵattribute(\"id\", ctx.inputId)(\"placeholder\", ctx.placeholder)(\"aria-label\", ctx.placeholder)(\"tabindex\", ctx.tabindex)(\"disabled\", ctx.disabled ? ctx.disabled : null);\n    }\n  },\n  directives: [i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.MinLengthValidator, i1.NgControlStatus, i1.FormControlName, i2.NgClass],\n  styles: [\".dark[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused[_ngcontent-%COMP%]{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.ng2-tag-input.ng2-tag-input--focused[_ngcontent-%COMP%]{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.ng2-tag-input[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{margin:.1em 0}.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.dark.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused[_ngcontent-%COMP%]{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{margin:.1em 0}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.error-message[_ngcontent-%COMP%]{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#d9534f}.ng2-tag-input__text-input[_ngcontent-%COMP%]{display:inline;vertical-align:middle;border:none;padding:0 .5rem;height:38px;font-size:1em;font-family:Roboto,Helvetica Neue,sans-serif}.ng2-tag-input__text-input[_ngcontent-%COMP%]:focus{outline:0}.ng2-tag-input__text-input[disabled=true][_ngcontent-%COMP%]{opacity:.5;background:#fff}\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TagInputForm, [{\n    type: Component,\n    args: [{\n      selector: 'tag-input-form',\n      template: \"<!-- form -->\\n<form (ngSubmit)=\\\"submit($event)\\\" [formGroup]=\\\"form\\\">\\n    <input #input\\n\\n           type=\\\"text\\\"\\n           class=\\\"ng2-tag-input__text-input\\\"\\n           autocomplete=\\\"off\\\"\\n           tabindex=\\\"{{ disabled ? -1 : tabindex ? tabindex : 0 }}\\\"\\n           minlength=\\\"1\\\"\\n           formControlName=\\\"item\\\"\\n\\n           [ngClass]=\\\"inputClass\\\"\\n           [attr.id]=\\\"inputId\\\"\\n           [attr.placeholder]=\\\"placeholder\\\"\\n           [attr.aria-label]=\\\"placeholder\\\"\\n           [attr.tabindex]=\\\"tabindex\\\"\\n           [attr.disabled]=\\\"disabled ? disabled : null\\\"\\n\\n           (focus)=\\\"onFocus.emit($event)\\\"\\n           (blur)=\\\"onBlur.emit($event)\\\"\\n           (keydown)=\\\"onKeyDown($event)\\\"\\n           (keyup)=\\\"onKeyUp($event)\\\"\\n    />\\n</form>\\n\",\n      styles: [\".dark tag:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info input{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info .form-control input::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info .form-control input:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info .form-control input::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info input:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading{border:none}.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.ng2-tag-input form{margin:.1em 0}.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.dark.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input form{margin:.1em 0}.bootstrap3-info.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.error-message{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap .error-message{color:#d9534f}.ng2-tag-input__text-input{display:inline;vertical-align:middle;border:none;padding:0 .5rem;height:38px;font-size:1em;font-family:Roboto,Helvetica Neue,sans-serif}.ng2-tag-input__text-input:focus{outline:0}.ng2-tag-input__text-input[disabled=true]{opacity:.5;background:#fff}\\n\"]\n    }]\n  }], null, {\n    onSubmit: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onKeyup: [{\n      type: Output\n    }],\n    onKeydown: [{\n      type: Output\n    }],\n    inputTextChange: [{\n      type: Output\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    validators: [{\n      type: Input\n    }],\n    asyncValidators: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    inputClass: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    input: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    inputText: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TagRipple {\n  constructor() {\n    this.state = 'none';\n  }\n\n}\n\nTagRipple.ɵfac = function TagRipple_Factory(t) {\n  return new (t || TagRipple)();\n};\n\nTagRipple.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TagRipple,\n  selectors: [[\"tag-ripple\"]],\n  inputs: {\n    state: \"state\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[1, \"tag-ripple\"]],\n  template: function TagRipple_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"@ink\", ctx.state);\n    }\n  },\n  styles: [\"[_nghost-%COMP%]{width:100%;height:100%;left:0;overflow:hidden;position:absolute}.tag-ripple[_ngcontent-%COMP%]{background:rgba(0,0,0,.1);top:50%;left:50%;height:100%;transform:translate(-50%,-50%);position:absolute}\"],\n  data: {\n    animation: [trigger('ink', [state('none', style({\n      width: 0,\n      opacity: 0\n    })), transition('none => clicked', [animate(300, keyframes([style({\n      opacity: 1,\n      offset: 0,\n      width: '30%',\n      borderRadius: '100%'\n    }), style({\n      opacity: 1,\n      offset: 0.5,\n      width: '50%'\n    }), style({\n      opacity: 0.5,\n      offset: 1,\n      width: '100%',\n      borderRadius: '16px'\n    })]))])])]\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TagRipple, [{\n    type: Component,\n    args: [{\n      selector: 'tag-ripple',\n      styles: [`\n        :host {\n            width: 100%;\n            height: 100%;\n            left: 0;\n            overflow: hidden;\n            position: absolute;\n        }\n\n        .tag-ripple {\n            background: rgba(0, 0, 0, 0.1);\n            top: 50%;\n            left: 50%;\n            height: 100%;\n            transform: translate(-50%, -50%);\n            position: absolute;\n        }\n    `],\n      template: `\n        <div class=\"tag-ripple\" [@ink]=\"state\"></div>\n    `,\n      animations: [trigger('ink', [state('none', style({\n        width: 0,\n        opacity: 0\n      })), transition('none => clicked', [animate(300, keyframes([style({\n        opacity: 1,\n        offset: 0,\n        width: '30%',\n        borderRadius: '100%'\n      }), style({\n        opacity: 1,\n        offset: 0.5,\n        width: '50%'\n      }), style({\n        opacity: 0.5,\n        offset: 1,\n        width: '100%',\n        borderRadius: '16px'\n      })]))])])]\n    }]\n  }], null, {\n    state: [{\n      type: Input\n    }]\n  });\n})();\n\nclass DeleteIconComponent {}\n\nDeleteIconComponent.ɵfac = function DeleteIconComponent_Factory(t) {\n  return new (t || DeleteIconComponent)();\n};\n\nDeleteIconComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: DeleteIconComponent,\n  selectors: [[\"delete-icon\"]],\n  decls: 3,\n  vars: 0,\n  consts: [[\"height\", \"16px\", \"viewBox\", \"0 0 32 32\", \"width\", \"16px\"], [\"d\", \"M17.459,16.014l8.239-8.194c0.395-0.391,0.395-1.024,0-1.414c-0.394-0.391-1.034-0.391-1.428,0  l-8.232,8.187L7.73,6.284c-0.394-0.395-1.034-0.395-1.428,0c-0.394,0.396-0.394,1.037,0,1.432l8.302,8.303l-8.332,8.286  c-0.394,0.391-0.394,1.024,0,1.414c0.394,0.391,1.034,0.391,1.428,0l8.325-8.279l8.275,8.276c0.394,0.395,1.034,0.395,1.428,0  c0.394-0.396,0.394-1.037,0-1.432L17.459,16.014z\", \"fill\", \"#121313\"]],\n  template: function DeleteIconComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"span\");\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(1, \"svg\", 0);\n      i0.ɵɵelement(2, \"path\", 1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n  },\n  styles: [\".dark[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused[_ngcontent-%COMP%]{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.ng2-tag-input.ng2-tag-input--focused[_ngcontent-%COMP%]{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.ng2-tag-input[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{margin:.1em 0}.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.dark.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused[_ngcontent-%COMP%]{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{margin:.1em 0}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.error-message[_ngcontent-%COMP%]{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#d9534f}delete-icon[_nghost-%COMP%]{width:20px;height:16px;transition:all .15s;display:inline-block;text-align:right}delete-icon[_nghost-%COMP%]   path[_ngcontent-%COMP%]{fill:#444}delete-icon[_nghost-%COMP%]   svg[_ngcontent-%COMP%]{vertical-align:bottom;height:34px}delete-icon[_nghost-%COMP%]:hover{transform:scale(1.5) translateY(-3px)}.dark[_nghost-%COMP%], .dark   [_nghost-%COMP%]{text-align:right}.dark[_nghost-%COMP%]   path[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   path[_ngcontent-%COMP%]{fill:#fff}.dark[_nghost-%COMP%]   svg[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   svg[_ngcontent-%COMP%]{vertical-align:bottom;height:34px}.minimal[_nghost-%COMP%], .minimal   [_nghost-%COMP%]{text-align:right}.minimal[_nghost-%COMP%]   path[_ngcontent-%COMP%], .minimal   [_nghost-%COMP%]   path[_ngcontent-%COMP%]{fill:#444}.minimal[_nghost-%COMP%]   svg[_ngcontent-%COMP%], .minimal   [_nghost-%COMP%]   svg[_ngcontent-%COMP%]{vertical-align:bottom;height:34px}.bootstrap[_nghost-%COMP%], .bootstrap   [_nghost-%COMP%]{text-align:right}.bootstrap[_nghost-%COMP%]   path[_ngcontent-%COMP%], .bootstrap   [_nghost-%COMP%]   path[_ngcontent-%COMP%]{fill:#fff}.bootstrap[_nghost-%COMP%]   svg[_ngcontent-%COMP%], .bootstrap   [_nghost-%COMP%]   svg[_ngcontent-%COMP%]{vertical-align:bottom;height:34px}tag:focus[_nghost-%COMP%]   path[_ngcontent-%COMP%], tag:focus   [_nghost-%COMP%]   path[_ngcontent-%COMP%], tag:active[_nghost-%COMP%]   path[_ngcontent-%COMP%], tag:active   [_nghost-%COMP%]   path[_ngcontent-%COMP%]{fill:#fff}.dark   tag:focus[_nghost-%COMP%]   path[_ngcontent-%COMP%], .dark   tag:focus   [_nghost-%COMP%]   path[_ngcontent-%COMP%], .dark   tag:active[_nghost-%COMP%]   path[_ngcontent-%COMP%], .dark   tag:active   [_nghost-%COMP%]   path[_ngcontent-%COMP%]{fill:#000}.minimal   tag:focus[_nghost-%COMP%]   path[_ngcontent-%COMP%], .minimal   tag:focus   [_nghost-%COMP%]   path[_ngcontent-%COMP%], .minimal   tag:active[_nghost-%COMP%]   path[_ngcontent-%COMP%], .minimal   tag:active   [_nghost-%COMP%]   path[_ngcontent-%COMP%]{fill:#000}.bootstrap   tag:focus[_nghost-%COMP%]   path[_ngcontent-%COMP%], .bootstrap   tag:focus   [_nghost-%COMP%]   path[_ngcontent-%COMP%], .bootstrap   tag:active[_nghost-%COMP%]   path[_ngcontent-%COMP%], .bootstrap   tag:active   [_nghost-%COMP%]   path[_ngcontent-%COMP%]{fill:#fff}.bootstrap3-info[_nghost-%COMP%], .bootstrap3-info   [_nghost-%COMP%]{height:inherit}.bootstrap3-info[_nghost-%COMP%]   path[_ngcontent-%COMP%], .bootstrap3-info   [_nghost-%COMP%]   path[_ngcontent-%COMP%]{fill:#fff}\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DeleteIconComponent, [{\n    type: Component,\n    args: [{\n      selector: 'delete-icon',\n      template: \"<span>\\n    <svg\\n        height=\\\"16px\\\"\\n        viewBox=\\\"0 0 32 32\\\"\\n        width=\\\"16px\\\"\\n    >\\n        <path\\n            d=\\\"M17.459,16.014l8.239-8.194c0.395-0.391,0.395-1.024,0-1.414c-0.394-0.391-1.034-0.391-1.428,0  l-8.232,8.187L7.73,6.284c-0.394-0.395-1.034-0.395-1.428,0c-0.394,0.396-0.394,1.037,0,1.432l8.302,8.303l-8.332,8.286  c-0.394,0.391-0.394,1.024,0,1.414c0.394,0.391,1.034,0.391,1.428,0l8.325-8.279l8.275,8.276c0.394,0.395,1.034,0.395,1.428,0  c0.394-0.396,0.394-1.037,0-1.432L17.459,16.014z\\\"\\n            fill=\\\"#121313\\\"\\n        />\\n    </svg>\\n</span>\",\n      styles: [\".dark tag:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info input{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info .form-control input::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info .form-control input:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info .form-control input::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info input:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading{border:none}.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.ng2-tag-input form{margin:.1em 0}.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.dark.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input form{margin:.1em 0}.bootstrap3-info.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.error-message{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap .error-message{color:#d9534f}:host(delete-icon){width:20px;height:16px;transition:all .15s;display:inline-block;text-align:right}:host(delete-icon) path{fill:#444}:host(delete-icon) svg{vertical-align:bottom;height:34px}:host(delete-icon):hover{transform:scale(1.5) translateY(-3px)}:host-context(.dark){text-align:right}:host-context(.dark) path{fill:#fff}:host-context(.dark) svg{vertical-align:bottom;height:34px}:host-context(.minimal){text-align:right}:host-context(.minimal) path{fill:#444}:host-context(.minimal) svg{vertical-align:bottom;height:34px}:host-context(.bootstrap){text-align:right}:host-context(.bootstrap) path{fill:#fff}:host-context(.bootstrap) svg{vertical-align:bottom;height:34px}:host-context(tag:focus) path,:host-context(tag:active) path{fill:#fff}:host-context(.dark tag:focus) path,:host-context(.dark tag:active) path{fill:#000}:host-context(.minimal tag:focus) path,:host-context(.minimal tag:active) path{fill:#000}:host-context(.bootstrap tag:focus) path,:host-context(.bootstrap tag:active) path{fill:#fff}:host-context(.bootstrap3-info){height:inherit}:host-context(.bootstrap3-info) path{fill:#fff}\\n\"]\n    }]\n  }], null, null);\n})(); // mocking navigator\n\n\nconst navigator = typeof window !== 'undefined' ? window.navigator : {\n  userAgent: 'Chrome',\n  vendor: 'Google Inc'\n};\nconst isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);\n\nclass TagComponent {\n  constructor(element, renderer, cdRef) {\n    this.element = element;\n    this.renderer = renderer;\n    this.cdRef = cdRef;\n    /**\n     * @name disabled\n     */\n\n    this.disabled = false;\n    /**\n     * @name onSelect\n     */\n\n    this.onSelect = new EventEmitter();\n    /**\n     * @name onRemove\n     */\n\n    this.onRemove = new EventEmitter();\n    /**\n     * @name onBlur\n     */\n\n    this.onBlur = new EventEmitter();\n    /**\n     * @name onKeyDown\n     */\n\n    this.onKeyDown = new EventEmitter();\n    /**\n     * @name onTagEdited\n     */\n\n    this.onTagEdited = new EventEmitter();\n    /**\n     * @name editing\n     */\n\n    this.editing = false;\n    /**\n     * @name rippleState\n     */\n\n    this.rippleState = 'none';\n  }\n  /**\n   * @name readonly {boolean}\n   */\n\n\n  get readonly() {\n    return typeof this.model !== 'string' && this.model.readonly === true;\n  }\n  /**\n   * @name select\n   */\n\n\n  select($event) {\n    if (this.readonly || this.disabled) {\n      return;\n    }\n\n    if ($event) {\n      $event.stopPropagation();\n    }\n\n    this.focus();\n    this.onSelect.emit(this.model);\n  }\n  /**\n   * @name remove\n   */\n\n\n  remove($event) {\n    $event.stopPropagation();\n    this.onRemove.emit(this);\n  }\n  /**\n   * @name focus\n   */\n\n\n  focus() {\n    this.element.nativeElement.focus();\n  }\n\n  move() {\n    this.moving = true;\n  }\n  /**\n   * @name keydown\n   * @param event\n   */\n\n\n  keydown(event) {\n    if (this.editing) {\n      if (event.keyCode === 13) {\n        return this.disableEditMode(event);\n      }\n    } else {\n      this.onKeyDown.emit({\n        event,\n        model: this.model\n      });\n    }\n  }\n  /**\n   * @name blink\n   */\n\n\n  blink() {\n    const classList = this.element.nativeElement.classList;\n    classList.add('blink');\n    setTimeout(() => classList.remove('blink'), 50);\n  }\n  /**\n   * @name toggleEditMode\n   */\n\n\n  toggleEditMode() {\n    if (this.editable) {\n      return this.editing ? undefined : this.activateEditMode();\n    }\n  }\n  /**\n   * @name onBlurred\n   * @param event\n   */\n\n\n  onBlurred(event) {\n    // Checks if it is editable first before handeling the onBlurred event in order to prevent\n    // a bug in IE where tags are still editable with onlyFromAutocomplete set to true\n    if (!this.editable) {\n      return;\n    }\n\n    this.disableEditMode();\n    const value = event.target.innerText;\n    const result = typeof this.model === 'string' ? value : { ...this.model,\n      [this.displayBy]: value\n    };\n    this.onBlur.emit(result);\n  }\n  /**\n   * @name getDisplayValue\n   * @param item\n   */\n\n\n  getDisplayValue(item) {\n    return typeof item === 'string' ? item : item[this.displayBy];\n  }\n  /**\n   * @desc returns whether the ripple is visible or not\n   * only works in Chrome\n   * @name isRippleVisible\n   */\n\n\n  get isRippleVisible() {\n    return !this.readonly && !this.editing && isChrome && this.hasRipple;\n  }\n  /**\n   * @name disableEditMode\n   * @param $event\n   */\n\n\n  disableEditMode($event) {\n    const classList = this.element.nativeElement.classList;\n    const input = this.getContentEditableText();\n    this.editing = false;\n    classList.remove('tag--editing');\n\n    if (!input) {\n      this.setContentEditableText(this.model);\n      return;\n    }\n\n    this.storeNewValue(input);\n    this.cdRef.detectChanges();\n\n    if ($event) {\n      $event.preventDefault();\n    }\n  }\n  /**\n   * @name isDeleteIconVisible\n   */\n\n\n  isDeleteIconVisible() {\n    return !this.readonly && !this.disabled && this.removable && !this.editing;\n  }\n  /**\n   * @name getContentEditableText\n   */\n\n\n  getContentEditableText() {\n    const input = this.getContentEditable();\n    return input ? input.innerText.trim() : '';\n  }\n  /**\n   * @name setContentEditableText\n   * @param model\n   */\n\n\n  setContentEditableText(model) {\n    const input = this.getContentEditable();\n    const value = this.getDisplayValue(model);\n    input.innerText = value;\n  }\n  /**\n   * @name\n   */\n\n\n  activateEditMode() {\n    const classList = this.element.nativeElement.classList;\n    classList.add('tag--editing');\n    this.editing = true;\n  }\n  /**\n   * @name storeNewValue\n   * @param input\n   */\n\n\n  storeNewValue(input) {\n    const exists = tag => {\n      return typeof tag === 'string' ? tag === input : tag[this.displayBy] === input;\n    };\n\n    const hasId = () => {\n      return this.model[this.identifyBy] !== this.model[this.displayBy];\n    }; // if the value changed, replace the value in the model\n\n\n    if (exists(this.model)) {\n      return;\n    }\n\n    const model = typeof this.model === 'string' ? input : {\n      index: this.index,\n      [this.identifyBy]: hasId() ? this.model[this.identifyBy] : input,\n      [this.displayBy]: input\n    };\n\n    if (this.canAddTag(model)) {\n      this.onTagEdited.emit({\n        tag: model,\n        index: this.index\n      });\n    } else {\n      this.setContentEditableText(this.model);\n    }\n  }\n  /**\n   * @name getContentEditable\n   */\n\n\n  getContentEditable() {\n    return this.element.nativeElement.querySelector('[contenteditable]');\n  }\n\n}\n\nTagComponent.ɵfac = function TagComponent_Factory(t) {\n  return new (t || TagComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nTagComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TagComponent,\n  selectors: [[\"tag\"]],\n  viewQuery: function TagComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TagRipple, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ripple = _t.first);\n    }\n  },\n  hostVars: 2,\n  hostBindings: function TagComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function TagComponent_keydown_HostBindingHandler($event) {\n        return ctx.keydown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"moving\", ctx.moving);\n    }\n  },\n  inputs: {\n    model: \"model\",\n    removable: \"removable\",\n    editable: \"editable\",\n    template: \"template\",\n    displayBy: \"displayBy\",\n    identifyBy: \"identifyBy\",\n    index: \"index\",\n    hasRipple: \"hasRipple\",\n    disabled: \"disabled\",\n    canAddTag: \"canAddTag\"\n  },\n  outputs: {\n    onSelect: \"onSelect\",\n    onRemove: \"onRemove\",\n    onBlur: \"onBlur\",\n    onKeyDown: \"onKeyDown\",\n    onTagEdited: \"onTagEdited\"\n  },\n  decls: 4,\n  vars: 8,\n  consts: [[3, \"ngSwitch\", \"click\", \"dblclick\", \"mousedown\", \"mouseup\"], [4, \"ngSwitchCase\"], [\"class\", \"tag-wrapper\", 4, \"ngSwitchCase\"], [3, \"state\", 4, \"ngIf\"], [3, \"ngTemplateOutletContext\", \"ngTemplateOutlet\"], [1, \"tag-wrapper\"], [\"spellcheck\", \"false\", 1, \"tag__text\", \"inline\", 3, \"keydown.enter\", \"keydown.escape\", \"click\", \"blur\"], [\"aria-label\", \"Remove tag\", \"role\", \"button\", 3, \"click\", 4, \"ngIf\"], [\"aria-label\", \"Remove tag\", \"role\", \"button\", 3, \"click\"], [3, \"state\"]],\n  template: function TagComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"click\", function TagComponent_Template_div_click_0_listener($event) {\n        return ctx.select($event);\n      })(\"dblclick\", function TagComponent_Template_div_dblclick_0_listener() {\n        return ctx.toggleEditMode();\n      })(\"mousedown\", function TagComponent_Template_div_mousedown_0_listener() {\n        return ctx.rippleState = \"clicked\";\n      })(\"mouseup\", function TagComponent_Template_div_mouseup_0_listener() {\n        return ctx.rippleState = \"none\";\n      });\n      i0.ɵɵtemplate(1, TagComponent_div_1_Template, 2, 6, \"div\", 1);\n      i0.ɵɵtemplate(2, TagComponent_div_2_Template, 4, 4, \"div\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(3, TagComponent_tag_ripple_3_Template, 1, 2, \"tag-ripple\", 3);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"disabled\", ctx.disabled);\n      i0.ɵɵproperty(\"ngSwitch\", !!ctx.template);\n      i0.ɵɵattribute(\"tabindex\", -1)(\"aria-label\", ctx.getDisplayValue(ctx.model));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", true);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngSwitchCase\", false);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.isRippleVisible);\n    }\n  },\n  directives: [i2.NgSwitch, i2.NgSwitchCase, i2.NgIf, i2.NgTemplateOutlet, DeleteIconComponent, TagRipple],\n  styles: [\"[_nghost-%COMP%], [_nghost-%COMP%] > div[_ngcontent-%COMP%], [_nghost-%COMP%] > div[_ngcontent-%COMP%]:focus{outline:0;overflow:hidden;transition:opacity 1s;z-index:1}[_nghost-%COMP%]{max-width:400px}.blink[_nghost-%COMP%]{-webkit-animation:blink .3s normal forwards ease-in-out;animation:blink .3s normal forwards ease-in-out}@-webkit-keyframes blink{0%{opacity:.3}}@keyframes blink{0%{opacity:.3}}[_nghost-%COMP%]   .disabled[_ngcontent-%COMP%]{cursor:not-allowed}[_nghost-%COMP%]   [contenteditable=true][_ngcontent-%COMP%]{outline:0}.tag-wrapper[_ngcontent-%COMP%]{flex-direction:row;display:flex}.tag__text[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TagComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tag',\n      template: \"<div (click)=\\\"select($event)\\\"\\n     (dblclick)=\\\"toggleEditMode()\\\"\\n     (mousedown)=\\\"rippleState='clicked'\\\"\\n     (mouseup)=\\\"rippleState='none'\\\"\\n     [ngSwitch]=\\\"!!template\\\"\\n     [class.disabled]=\\\"disabled\\\"\\n     [attr.tabindex]=\\\"-1\\\"\\n     [attr.aria-label]=\\\"getDisplayValue(model)\\\">\\n\\n    <div *ngSwitchCase=\\\"true\\\" [attr.contenteditable]=\\\"editing\\\">\\n        <!-- CUSTOM TEMPLATE -->\\n        <ng-template\\n            [ngTemplateOutletContext]=\\\"{ item: model, index: index }\\\"\\n            [ngTemplateOutlet]=\\\"template\\\">\\n        </ng-template>\\n    </div>\\n\\n    <div *ngSwitchCase=\\\"false\\\" class=\\\"tag-wrapper\\\">\\n        <!-- TAG NAME -->\\n        <div [attr.contenteditable]=\\\"editing\\\"\\n             [attr.title]=\\\"getDisplayValue(model)\\\"\\n             class=\\\"tag__text inline\\\"\\n             spellcheck=\\\"false\\\"\\n             (keydown.enter)=\\\"disableEditMode($event)\\\"\\n             (keydown.escape)=\\\"disableEditMode($event)\\\"\\n             (click)=\\\"editing ? $event.stopPropagation() : undefined\\\"\\n             (blur)=\\\"onBlurred($event)\\\">\\n            {{ getDisplayValue(model) }}\\n        </div>\\n\\n        <!-- 'X' BUTTON -->\\n        <delete-icon\\n            aria-label=\\\"Remove tag\\\"\\n            role=\\\"button\\\"\\n            (click)=\\\"remove($event)\\\"\\n            *ngIf=\\\"isDeleteIconVisible()\\\">\\n        </delete-icon>\\n    </div>\\n</div>\\n\\n<tag-ripple [state]=\\\"rippleState\\\"\\n            [attr.tabindex]=\\\"-1\\\"\\n            *ngIf=\\\"isRippleVisible\\\">\\n</tag-ripple>\\n\",\n      styles: [\":host,:host>div,:host>div:focus{outline:0;overflow:hidden;transition:opacity 1s;z-index:1}:host{max-width:400px}:host.blink{-webkit-animation:blink .3s normal forwards ease-in-out;animation:blink .3s normal forwards ease-in-out}@-webkit-keyframes blink{0%{opacity:.3}}@keyframes blink{0%{opacity:.3}}:host .disabled{cursor:not-allowed}:host [contenteditable=true]{outline:0}.tag-wrapper{flex-direction:row;display:flex}.tag__text{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    model: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input\n    }],\n    editable: [{\n      type: Input\n    }],\n    template: [{\n      type: Input\n    }],\n    displayBy: [{\n      type: Input\n    }],\n    identifyBy: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    hasRipple: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    canAddTag: [{\n      type: Input\n    }],\n    onSelect: [{\n      type: Output\n    }],\n    onRemove: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onKeyDown: [{\n      type: Output\n    }],\n    onTagEdited: [{\n      type: Output\n    }],\n    moving: [{\n      type: HostBinding,\n      args: ['class.moving']\n    }],\n    ripple: [{\n      type: ViewChild,\n      args: [TagRipple]\n    }],\n    keydown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\n/**\n * @name animations\n */\n\n\nconst animations = [trigger('animation', [state('in', style({\n  opacity: 1\n})), state('out', style({\n  opacity: 0\n})), transition(':enter', [animate('{{ enter }}', keyframes([style({\n  opacity: 0,\n  offset: 0,\n  transform: 'translate(0px, 20px)'\n}), style({\n  opacity: 0.3,\n  offset: 0.3,\n  transform: 'translate(0px, -10px)'\n}), style({\n  opacity: 0.5,\n  offset: 0.5,\n  transform: 'translate(0px, 0px)'\n}), style({\n  opacity: 0.75,\n  offset: 0.75,\n  transform: 'translate(0px, 5px)'\n}), style({\n  opacity: 1,\n  offset: 1,\n  transform: 'translate(0px, 0px)'\n})]))]), transition(':leave', [animate('{{ leave }}', keyframes([style({\n  opacity: 1,\n  transform: 'translateX(0)',\n  offset: 0\n}), style({\n  opacity: 1,\n  transform: 'translateX(-15px)',\n  offset: 0.7\n}), style({\n  opacity: 0,\n  transform: 'translateX(100%)',\n  offset: 1.0\n})]))])])];\n\nclass TagInputDropdown {\n  constructor(injector) {\n    var _this = this;\n\n    this.injector = injector;\n    /**\n     * @name offset\n     */\n\n    this.offset = defaults.dropdown.offset;\n    /**\n     * @name focusFirstElement\n     */\n\n    this.focusFirstElement = defaults.dropdown.focusFirstElement;\n    /**\n     * - show autocomplete dropdown if the value of input is empty\n     * @name showDropdownIfEmpty\n     */\n\n    this.showDropdownIfEmpty = defaults.dropdown.showDropdownIfEmpty;\n    /**\n     * - desc minimum text length in order to display the autocomplete dropdown\n     * @name minimumTextLength\n     */\n\n    this.minimumTextLength = defaults.dropdown.minimumTextLength;\n    /**\n     * - number of items to display in the autocomplete dropdown\n     * @name limitItemsTo\n     */\n\n    this.limitItemsTo = defaults.dropdown.limitItemsTo;\n    /**\n     * @name displayBy\n     */\n\n    this.displayBy = defaults.dropdown.displayBy;\n    /**\n     * @name identifyBy\n     */\n\n    this.identifyBy = defaults.dropdown.identifyBy;\n    /**\n     * @description a function a developer can use to implement custom matching for the autocomplete\n     * @name matchingFn\n     */\n\n    this.matchingFn = defaults.dropdown.matchingFn;\n    /**\n     * @name appendToBody\n     */\n\n    this.appendToBody = defaults.dropdown.appendToBody;\n    /**\n     * @name keepOpen\n     * @description option to leave dropdown open when adding a new item\n     */\n\n    this.keepOpen = defaults.dropdown.keepOpen;\n    /**\n     * @name dynamicUpdate\n     */\n\n    this.dynamicUpdate = defaults.dropdown.dynamicUpdate;\n    /**\n     * @name zIndex\n     */\n\n    this.zIndex = defaults.dropdown.zIndex;\n    /**\n     * list of items that match the current value of the input (for autocomplete)\n     * @name items\n     */\n\n    this.items = [];\n    /**\n     * @name tagInput\n     */\n\n    this.tagInput = this.injector.get(TagInputComponent);\n    /**\n     * @name _autocompleteItems\n     */\n\n    this._autocompleteItems = [];\n    /**\n     *\n     * @name show\n     */\n\n    this.show = () => {\n      const maxItemsReached = this.tagInput.items.length === this.tagInput.maxItems;\n      const value = this.getFormValue();\n      const hasMinimumText = value.trim().length >= this.minimumTextLength;\n      const position = this.calculatePosition();\n      const items = this.getMatchingItems(value);\n      const hasItems = items.length > 0;\n      const isHidden = this.isVisible === false;\n      const showDropdownIfEmpty = this.showDropdownIfEmpty && hasItems && !value;\n      const isDisabled = this.tagInput.disable;\n      const shouldShow = isHidden && (hasItems && hasMinimumText || showDropdownIfEmpty);\n      const shouldHide = this.isVisible && !hasItems;\n\n      if (this.autocompleteObservable && hasMinimumText) {\n        return this.getItemsFromObservable(value);\n      }\n\n      if (!this.showDropdownIfEmpty && !value || maxItemsReached || isDisabled) {\n        return this.dropdown.hide();\n      }\n\n      this.setItems(items);\n\n      if (shouldShow) {\n        this.dropdown.show(position);\n      } else if (shouldHide) {\n        this.hide();\n      }\n    };\n    /**\n     * @name requestAdding\n     * @param item {Ng2MenuItem}\n     */\n\n\n    this.requestAdding = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (item) {\n        const tag = _this.createTagModel(item);\n\n        yield _this.tagInput.onAddingRequested(true, tag).catch(() => {});\n      });\n\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    /**\n     * @name resetItems\n     */\n\n\n    this.resetItems = () => {\n      this.items = [];\n    };\n    /**\n     * @name getItemsFromObservable\n     * @param text\n     */\n\n\n    this.getItemsFromObservable = text => {\n      this.setLoadingState(true);\n\n      const subscribeFn = data => {\n        // hide loading animation\n        this.setLoadingState(false) // add items\n        .populateItems(data);\n        this.setItems(this.getMatchingItems(text));\n\n        if (this.items.length) {\n          this.dropdown.show(this.calculatePosition());\n        } else {\n          this.dropdown.hide();\n        }\n      };\n\n      this.autocompleteObservable(text).pipe(first()).subscribe(subscribeFn, () => this.setLoadingState(false));\n    };\n  }\n  /**\n   * @name autocompleteItems\n   * @param items\n   */\n\n\n  set autocompleteItems(items) {\n    this._autocompleteItems = items;\n  }\n  /**\n   * @name autocompleteItems\n   * @desc array of items that will populate the autocomplete\n   */\n\n\n  get autocompleteItems() {\n    const items = this._autocompleteItems;\n\n    if (!items) {\n      return [];\n    }\n\n    return items.map(item => {\n      return typeof item === 'string' ? {\n        [this.displayBy]: item,\n        [this.identifyBy]: item\n      } : item;\n    });\n  }\n  /**\n   * @name ngAfterviewInit\n   */\n\n\n  ngAfterViewInit() {\n    this.onItemClicked().subscribe(item => {\n      this.requestAdding(item);\n    }); // reset itemsMatching array when the dropdown is hidden\n\n    this.onHide().subscribe(this.resetItems);\n    const DEBOUNCE_TIME = 200;\n    const KEEP_OPEN = this.keepOpen;\n    this.tagInput.onTextChange.asObservable().pipe(distinctUntilChanged(), debounceTime(DEBOUNCE_TIME), filter(value => {\n      if (KEEP_OPEN === false) {\n        return value.length > 0;\n      }\n\n      return true;\n    })).subscribe(this.show);\n  }\n  /**\n   * @name updatePosition\n   */\n\n\n  updatePosition() {\n    const position = this.tagInput.inputForm.getElementPosition();\n    this.dropdown.menu.updatePosition(position, this.dynamicUpdate);\n  }\n  /**\n   * @name isVisible\n   */\n\n\n  get isVisible() {\n    return this.dropdown.menu.dropdownState.menuState.isVisible;\n  }\n  /**\n   * @name onHide\n   */\n\n\n  onHide() {\n    return this.dropdown.onHide;\n  }\n  /**\n   * @name onItemClicked\n   */\n\n\n  onItemClicked() {\n    return this.dropdown.onItemClicked;\n  }\n  /**\n   * @name selectedItem\n   */\n\n\n  get selectedItem() {\n    return this.dropdown.menu.dropdownState.dropdownState.selectedItem;\n  }\n  /**\n   * @name state\n   */\n\n\n  get state() {\n    return this.dropdown.menu.dropdownState;\n  }\n  /**\n   * @name hide\n   */\n\n\n  hide() {\n    this.resetItems();\n    this.dropdown.hide();\n  }\n  /**\n   * @name scrollListener\n   */\n\n\n  scrollListener() {\n    if (!this.isVisible || !this.dynamicUpdate) {\n      return;\n    }\n\n    this.updatePosition();\n  }\n  /**\n   * @name onWindowBlur\n   */\n\n\n  onWindowBlur() {\n    this.dropdown.hide();\n  }\n  /**\n   * @name getFormValue\n   */\n\n\n  getFormValue() {\n    const formValue = this.tagInput.formValue;\n    return formValue ? formValue.toString().trim() : '';\n  }\n  /**\n   * @name calculatePosition\n   */\n\n\n  calculatePosition() {\n    return this.tagInput.inputForm.getElementPosition();\n  }\n  /**\n   * @name createTagModel\n   * @param item\n   */\n\n\n  createTagModel(item) {\n    const display = typeof item.value === 'string' ? item.value : item.value[this.displayBy];\n    const value = typeof item.value === 'string' ? item.value : item.value[this.identifyBy];\n    return { ...item.value,\n      [this.tagInput.displayBy]: display,\n      [this.tagInput.identifyBy]: value\n    };\n  }\n  /**\n   *\n   * @param value {string}\n   */\n\n\n  getMatchingItems(value) {\n    if (!value && !this.showDropdownIfEmpty) {\n      return [];\n    }\n\n    const dupesAllowed = this.tagInput.allowDupes;\n    return this.autocompleteItems.filter(item => {\n      const hasValue = dupesAllowed ? false : this.tagInput.tags.some(tag => {\n        const identifyBy = this.tagInput.identifyBy;\n        const model = typeof tag.model === 'string' ? tag.model : tag.model[identifyBy];\n        return model === item[this.identifyBy];\n      });\n      return this.matchingFn(value, item) && hasValue === false;\n    });\n  }\n  /**\n   * @name setItems\n   */\n\n\n  setItems(items) {\n    this.items = items.slice(0, this.limitItemsTo || items.length);\n  }\n  /**\n   * @name populateItems\n   * @param data\n   */\n\n\n  populateItems(data) {\n    this.autocompleteItems = data.map(item => {\n      return typeof item === 'string' ? {\n        [this.displayBy]: item,\n        [this.identifyBy]: item\n      } : item;\n    });\n    return this;\n  }\n  /**\n   * @name setLoadingState\n   * @param state\n   */\n\n\n  setLoadingState(state) {\n    this.tagInput.isLoading = state;\n    return this;\n  }\n\n}\n\nTagInputDropdown.ɵfac = function TagInputDropdown_Factory(t) {\n  return new (t || TagInputDropdown)(i0.ɵɵdirectiveInject(i0.Injector));\n};\n\nTagInputDropdown.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TagInputDropdown,\n  selectors: [[\"tag-input-dropdown\"]],\n  contentQueries: function TagInputDropdown_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, TemplateRef, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function TagInputDropdown_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(Ng2Dropdown, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdown = _t.first);\n    }\n  },\n  hostBindings: function TagInputDropdown_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"scroll\", function TagInputDropdown_scroll_HostBindingHandler() {\n        return ctx.scrollListener();\n      }, false, i0.ɵɵresolveWindow)(\"blur\", function TagInputDropdown_blur_HostBindingHandler() {\n        return ctx.onWindowBlur();\n      }, false, i0.ɵɵresolveWindow);\n    }\n  },\n  inputs: {\n    offset: \"offset\",\n    focusFirstElement: \"focusFirstElement\",\n    showDropdownIfEmpty: \"showDropdownIfEmpty\",\n    autocompleteObservable: \"autocompleteObservable\",\n    minimumTextLength: \"minimumTextLength\",\n    limitItemsTo: \"limitItemsTo\",\n    displayBy: \"displayBy\",\n    identifyBy: \"identifyBy\",\n    matchingFn: \"matchingFn\",\n    appendToBody: \"appendToBody\",\n    keepOpen: \"keepOpen\",\n    dynamicUpdate: \"dynamicUpdate\",\n    zIndex: \"zIndex\",\n    autocompleteItems: \"autocompleteItems\"\n  },\n  decls: 3,\n  vars: 6,\n  consts: [[3, \"dynamicUpdate\"], [3, \"focusFirstElement\", \"zIndex\", \"appendToBody\", \"offset\"], [3, \"value\", \"ngSwitch\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\", \"ngSwitch\"], [3, \"innerHTML\", 4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [3, \"innerHTML\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n  template: function TagInputDropdown_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ng2-dropdown\", 0);\n      i0.ɵɵelementStart(1, \"ng2-dropdown-menu\", 1);\n      i0.ɵɵtemplate(2, TagInputDropdown_ng2_menu_item_2_Template, 3, 3, \"ng2-menu-item\", 2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"dynamicUpdate\", ctx.dynamicUpdate);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"focusFirstElement\", ctx.focusFirstElement)(\"zIndex\", ctx.zIndex)(\"appendToBody\", ctx.appendToBody)(\"offset\", ctx.offset);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.items);\n    }\n  },\n  directives: [i1$1.Ng2Dropdown, i1$1.Ng2DropdownMenu, i2.NgForOf, i1$1.Ng2MenuItem, i2.NgSwitch, i2.NgSwitchCase, i2.NgSwitchDefault, i2.NgTemplateOutlet],\n  pipes: [HighlightPipe],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TagInputDropdown, [{\n    type: Component,\n    args: [{\n      selector: 'tag-input-dropdown',\n      template: \"<ng2-dropdown [dynamicUpdate]=\\\"dynamicUpdate\\\">\\n    <ng2-dropdown-menu [focusFirstElement]=\\\"focusFirstElement\\\"\\n                       [zIndex]=\\\"zIndex\\\"\\n                       [appendToBody]=\\\"appendToBody\\\"\\n                       [offset]=\\\"offset\\\">\\n        <ng2-menu-item *ngFor=\\\"let item of items; let index = index; let last = last\\\"\\n                       [value]=\\\"item\\\"\\n                       [ngSwitch]=\\\"!!templates.length\\\">\\n\\n            <span *ngSwitchCase=\\\"false\\\"\\n                  [innerHTML]=\\\"item[displayBy] | highlight : tagInput.inputForm.value.value\\\">\\n            </span>\\n\\n            <ng-template *ngSwitchDefault\\n                      [ngTemplateOutlet]=\\\"templates.first\\\"\\n                      [ngTemplateOutletContext]=\\\"{ item: item, index: index, last: last }\\\">\\n            </ng-template>\\n        </ng2-menu-item>\\n    </ng2-dropdown-menu>\\n</ng2-dropdown>\\n\"\n    }]\n  }], function () {\n    return [{\n      type: i0.Injector\n    }];\n  }, {\n    dropdown: [{\n      type: ViewChild,\n      args: [Ng2Dropdown]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [TemplateRef]\n    }],\n    offset: [{\n      type: Input\n    }],\n    focusFirstElement: [{\n      type: Input\n    }],\n    showDropdownIfEmpty: [{\n      type: Input\n    }],\n    autocompleteObservable: [{\n      type: Input\n    }],\n    minimumTextLength: [{\n      type: Input\n    }],\n    limitItemsTo: [{\n      type: Input\n    }],\n    displayBy: [{\n      type: Input\n    }],\n    identifyBy: [{\n      type: Input\n    }],\n    matchingFn: [{\n      type: Input\n    }],\n    appendToBody: [{\n      type: Input\n    }],\n    keepOpen: [{\n      type: Input\n    }],\n    dynamicUpdate: [{\n      type: Input\n    }],\n    zIndex: [{\n      type: Input\n    }],\n    autocompleteItems: [{\n      type: Input\n    }],\n    scrollListener: [{\n      type: HostListener,\n      args: ['window:scroll']\n    }],\n    onWindowBlur: [{\n      type: HostListener,\n      args: ['window:blur']\n    }]\n  });\n})(); // angular\n\n\nconst CUSTOM_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => TagInputComponent),\n  multi: true\n};\n\nclass TagInputComponent extends TagInputAccessor {\n  constructor(renderer, dragProvider) {\n    var _this2;\n\n    super();\n    _this2 = this;\n    this.renderer = renderer;\n    this.dragProvider = dragProvider;\n    /**\n     * @name separatorKeys\n     * @desc keyboard keys with which a user can separate items\n     */\n\n    this.separatorKeys = defaults.tagInput.separatorKeys;\n    /**\n     * @name separatorKeyCodes\n     * @desc keyboard key codes with which a user can separate items\n     */\n\n    this.separatorKeyCodes = defaults.tagInput.separatorKeyCodes;\n    /**\n     * @name placeholder\n     * @desc the placeholder of the input text\n     */\n\n    this.placeholder = defaults.tagInput.placeholder;\n    /**\n     * @name secondaryPlaceholder\n     * @desc placeholder to appear when the input is empty\n     */\n\n    this.secondaryPlaceholder = defaults.tagInput.secondaryPlaceholder;\n    /**\n     * @name maxItems\n     * @desc maximum number of items that can be added\n     */\n\n    this.maxItems = defaults.tagInput.maxItems;\n    /**\n     * @name validators\n     * @desc array of Validators that are used to validate the tag before it gets appended to the list\n     */\n\n    this.validators = defaults.tagInput.validators;\n    /**\n     * @name asyncValidators\n     * @desc array of AsyncValidator that are used to validate the tag before it gets appended to the list\n     */\n\n    this.asyncValidators = defaults.tagInput.asyncValidators;\n    /**\n    * - if set to true, it will only possible to add items from the autocomplete\n    * @name onlyFromAutocomplete\n    */\n\n    this.onlyFromAutocomplete = defaults.tagInput.onlyFromAutocomplete;\n    /**\n     * @name errorMessages\n     */\n\n    this.errorMessages = defaults.tagInput.errorMessages;\n    /**\n     * @name theme\n     */\n\n    this.theme = defaults.tagInput.theme;\n    /**\n     * @name onTextChangeDebounce\n     */\n\n    this.onTextChangeDebounce = defaults.tagInput.onTextChangeDebounce;\n    /**\n     * - custom id assigned to the input\n     * @name id\n     */\n\n    this.inputId = defaults.tagInput.inputId;\n    /**\n     * - custom class assigned to the input\n     */\n\n    this.inputClass = defaults.tagInput.inputClass;\n    /**\n     * - option to clear text input when the form is blurred\n     * @name clearOnBlur\n     */\n\n    this.clearOnBlur = defaults.tagInput.clearOnBlur;\n    /**\n     * - hideForm\n     * @name clearOnBlur\n     */\n\n    this.hideForm = defaults.tagInput.hideForm;\n    /**\n     * @name addOnBlur\n     */\n\n    this.addOnBlur = defaults.tagInput.addOnBlur;\n    /**\n     * @name addOnPaste\n     */\n\n    this.addOnPaste = defaults.tagInput.addOnPaste;\n    /**\n     * - pattern used with the native method split() to separate patterns in the string pasted\n     * @name pasteSplitPattern\n     */\n\n    this.pasteSplitPattern = defaults.tagInput.pasteSplitPattern;\n    /**\n     * @name blinkIfDupe\n     */\n\n    this.blinkIfDupe = defaults.tagInput.blinkIfDupe;\n    /**\n     * @name removable\n     */\n\n    this.removable = defaults.tagInput.removable;\n    /**\n     * @name editable\n     */\n\n    this.editable = defaults.tagInput.editable;\n    /**\n     * @name allowDupes\n     */\n\n    this.allowDupes = defaults.tagInput.allowDupes;\n    /**\n     * @description if set to true, the newly added tags will be added as strings, and not objects\n     * @name modelAsStrings\n     */\n\n    this.modelAsStrings = defaults.tagInput.modelAsStrings;\n    /**\n     * @name trimTags\n     */\n\n    this.trimTags = defaults.tagInput.trimTags;\n    /**\n     * @name ripple\n     */\n\n    this.ripple = defaults.tagInput.ripple;\n    /**\n     * @name tabindex\n     * @desc pass through the specified tabindex to the input\n     */\n\n    this.tabindex = defaults.tagInput.tabIndex;\n    /**\n     * @name disable\n     */\n\n    this.disable = defaults.tagInput.disable;\n    /**\n     * @name dragZone\n     */\n\n    this.dragZone = defaults.tagInput.dragZone;\n    /**\n     * @name onRemoving\n     */\n\n    this.onRemoving = defaults.tagInput.onRemoving;\n    /**\n     * @name onAdding\n     */\n\n    this.onAdding = defaults.tagInput.onAdding;\n    /**\n     * @name animationDuration\n     */\n\n    this.animationDuration = defaults.tagInput.animationDuration;\n    /**\n     * @name onAdd\n     * @desc event emitted when adding a new item\n     */\n\n    this.onAdd = new EventEmitter();\n    /**\n     * @name onRemove\n     * @desc event emitted when removing an existing item\n     */\n\n    this.onRemove = new EventEmitter();\n    /**\n     * @name onSelect\n     * @desc event emitted when selecting an item\n     */\n\n    this.onSelect = new EventEmitter();\n    /**\n     * @name onFocus\n     * @desc event emitted when the input is focused\n     */\n\n    this.onFocus = new EventEmitter();\n    /**\n     * @name onFocus\n     * @desc event emitted when the input is blurred\n     */\n\n    this.onBlur = new EventEmitter();\n    /**\n     * @name onTextChange\n     * @desc event emitted when the input value changes\n     */\n\n    this.onTextChange = new EventEmitter();\n    /**\n     * - output triggered when text is pasted in the form\n     * @name onPaste\n     */\n\n    this.onPaste = new EventEmitter();\n    /**\n     * - output triggered when tag entered is not valid\n     * @name onValidationError\n     */\n\n    this.onValidationError = new EventEmitter();\n    /**\n     * - output triggered when tag is edited\n     * @name onTagEdited\n     */\n\n    this.onTagEdited = new EventEmitter();\n    /**\n     * @name isLoading\n     */\n\n    this.isLoading = false;\n    /**\n     * @name listeners\n     * @desc array of events that get fired using @fireEvents\n     */\n\n    this.listeners = {\n      [KEYDOWN]: [],\n      [KEYUP]: []\n    };\n    /**\n     * @description emitter for the 2-way data binding inputText value\n     * @name inputTextChange\n     */\n\n    this.inputTextChange = new EventEmitter();\n    /**\n     * @description private variable to bind get/set\n     * @name inputTextValue\n     */\n\n    this.inputTextValue = '';\n    this.errors = [];\n    /**\n     * @name appendTag\n     * @param tag {TagModel}\n     */\n\n    this.appendTag = (tag, index = this.items.length) => {\n      const items = this.items;\n      const model = this.modelAsStrings ? tag[this.identifyBy] : tag;\n      this.items = [...items.slice(0, index), model, ...items.slice(index, items.length)];\n    };\n    /**\n     * @name createTag\n     * @param model\n     */\n\n\n    this.createTag = model => {\n      const trim = (val, key) => {\n        return typeof val === 'string' ? val.trim() : val[key];\n      };\n\n      return { ...(typeof model !== 'string' ? model : {}),\n        [this.displayBy]: this.trimTags ? trim(model, this.displayBy) : model,\n        [this.identifyBy]: this.trimTags ? trim(model, this.identifyBy) : model\n      };\n    };\n    /**\n     *\n     * @param tag\n     * @param isFromAutocomplete\n     */\n\n\n    this.isTagValid = (tag, fromAutocomplete = false) => {\n      const selectedItem = this.dropdown ? this.dropdown.selectedItem : undefined;\n      const value = this.getItemDisplay(tag).trim();\n\n      if (selectedItem && !fromAutocomplete || !value) {\n        return false;\n      }\n\n      const dupe = this.findDupe(tag, fromAutocomplete); // if so, give a visual cue and return false\n\n      if (!this.allowDupes && dupe && this.blinkIfDupe) {\n        const model = this.tags.find(item => {\n          return this.getItemValue(item.model) === this.getItemValue(dupe);\n        });\n\n        if (model) {\n          model.blink();\n        }\n      }\n\n      const isFromAutocomplete = fromAutocomplete && this.onlyFromAutocomplete;\n      const assertions = [// 1. there must be no dupe OR dupes are allowed\n      !dupe || this.allowDupes, // 2. check max items has not been reached\n      !this.maxItemsReached, // 3. check item comes from autocomplete or onlyFromAutocomplete is false\n      isFromAutocomplete || !this.onlyFromAutocomplete];\n      return assertions.filter(Boolean).length === assertions.length;\n    };\n    /**\n     * @name onPasteCallback\n     * @param data\n     */\n\n\n    this.onPasteCallback = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (data) {\n        const getText = () => {\n          const isIE = Boolean(window.clipboardData);\n          const clipboardData = isIE ? window.clipboardData : data.clipboardData;\n          const type = isIE ? 'Text' : 'text/plain';\n          return clipboardData === null ? '' : clipboardData.getData(type) || '';\n        };\n\n        const text = getText();\n        const requests = text.split(_this2.pasteSplitPattern).map(item => {\n          const tag = _this2.createTag(item);\n\n          _this2.setInputValue(tag[_this2.displayBy]);\n\n          return _this2.onAddingRequested(false, tag);\n        });\n\n        const resetInput = () => setTimeout(() => _this2.setInputValue(''), 50);\n\n        Promise.all(requests).then(() => {\n          _this2.onPaste.emit(text);\n\n          resetInput();\n        }).catch(resetInput);\n      });\n\n      return function (_x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n  }\n  /**\n   * @name inputText\n   */\n\n\n  get inputText() {\n    return this.inputTextValue;\n  }\n  /**\n   * @name inputText\n   * @param text\n   */\n\n\n  set inputText(text) {\n    this.inputTextValue = text;\n    this.inputTextChange.emit(text);\n  }\n  /**\n   * @desc removes the tab index if it is set - it will be passed through to the input\n   * @name tabindexAttr\n   */\n\n\n  get tabindexAttr() {\n    return this.tabindex !== '' ? '-1' : '';\n  }\n  /**\n   * @name ngAfterViewInit\n   */\n\n\n  ngAfterViewInit() {\n    // set up listeners\n    this.setUpKeypressListeners();\n    this.setupSeparatorKeysListener();\n    this.setUpInputKeydownListeners();\n\n    if (this.onTextChange.observers.length) {\n      this.setUpTextChangeSubscriber();\n    } // if clear on blur is set to true, subscribe to the event and clear the text's form\n\n\n    if (this.clearOnBlur || this.addOnBlur) {\n      this.setUpOnBlurSubscriber();\n    } // if addOnPaste is set to true, register the handler and add items\n\n\n    if (this.addOnPaste) {\n      this.setUpOnPasteListener();\n    }\n\n    const statusChanges$ = this.inputForm.form.statusChanges;\n    statusChanges$.pipe(filter$1(status => status !== 'PENDING')).subscribe(() => {\n      this.errors = this.inputForm.getErrorMessages(this.errorMessages);\n    });\n    this.isProgressBarVisible$ = statusChanges$.pipe(map(status => {\n      return status === 'PENDING' || this.isLoading;\n    })); // if hideForm is set to true, remove the input\n\n    if (this.hideForm) {\n      this.inputForm.destroy();\n    }\n  }\n  /**\n   * @name ngOnInit\n   */\n\n\n  ngOnInit() {\n    // if the number of items specified in the model is > of the value of maxItems\n    // degrade gracefully and let the max number of items to be the number of items in the model\n    // though, warn the user.\n    const hasReachedMaxItems = this.maxItems !== undefined && this.items && this.items.length > this.maxItems;\n\n    if (hasReachedMaxItems) {\n      this.maxItems = this.items.length;\n      console.warn(MAX_ITEMS_WARNING);\n    } // Setting editable to false to fix problem with tags in IE still being editable when\n    // onlyFromAutocomplete is true\n\n\n    this.editable = this.onlyFromAutocomplete ? false : this.editable;\n    this.setAnimationMetadata();\n  }\n  /**\n   * @name onRemoveRequested\n   * @param tag\n   * @param index\n   */\n\n\n  onRemoveRequested(tag, index) {\n    return new Promise(resolve => {\n      const subscribeFn = model => {\n        this.removeItem(model, index);\n        resolve(tag);\n      };\n\n      this.onRemoving ? this.onRemoving(tag).pipe(first$1()).subscribe(subscribeFn) : subscribeFn(tag);\n    });\n  }\n  /**\n   * @name onAddingRequested\n   * @param fromAutocomplete {boolean}\n   * @param tag {TagModel}\n   * @param index? {number}\n   * @param giveupFocus? {boolean}\n   */\n\n\n  onAddingRequested(fromAutocomplete, tag, index, giveupFocus) {\n    return new Promise((resolve, reject) => {\n      const subscribeFn = model => {\n        return this.addItem(fromAutocomplete, model, index, giveupFocus).then(resolve).catch(reject);\n      };\n\n      return this.onAdding ? this.onAdding(tag).pipe(first$1()).subscribe(subscribeFn, reject) : subscribeFn(tag);\n    });\n  }\n  /**\n   * @name selectItem\n   * @desc selects item passed as parameter as the selected tag\n   * @param item\n   * @param emit\n   */\n\n\n  selectItem(item, emit = true) {\n    const isReadonly = item && typeof item !== 'string' && item.readonly;\n\n    if (isReadonly || this.selectedTag === item) {\n      return;\n    }\n\n    this.selectedTag = item;\n\n    if (emit) {\n      this.onSelect.emit(item);\n    }\n  }\n  /**\n   * @name fireEvents\n   * @desc goes through the list of the events for a given eventName, and fires each of them\n   * @param eventName\n   * @param $event\n   */\n\n\n  fireEvents(eventName, $event) {\n    this.listeners[eventName].forEach(listener => listener.call(this, $event));\n  }\n  /**\n   * @name handleKeydown\n   * @desc handles action when the user hits a keyboard key\n   * @param data\n   */\n\n\n  handleKeydown(data) {\n    const event = data.event;\n    const key = event.keyCode || event.which;\n    const shiftKey = event.shiftKey || false;\n\n    switch (KEY_PRESS_ACTIONS[key]) {\n      case ACTIONS_KEYS.DELETE:\n        if (this.selectedTag && this.removable) {\n          const index = this.items.indexOf(this.selectedTag);\n          this.onRemoveRequested(this.selectedTag, index);\n        }\n\n        break;\n\n      case ACTIONS_KEYS.SWITCH_PREV:\n        this.moveToTag(data.model, PREV);\n        break;\n\n      case ACTIONS_KEYS.SWITCH_NEXT:\n        this.moveToTag(data.model, NEXT);\n        break;\n\n      case ACTIONS_KEYS.TAB:\n        if (shiftKey) {\n          if (this.isFirstTag(data.model)) {\n            return;\n          }\n\n          this.moveToTag(data.model, PREV);\n        } else {\n          if (this.isLastTag(data.model) && (this.disable || this.maxItemsReached)) {\n            return;\n          }\n\n          this.moveToTag(data.model, NEXT);\n        }\n\n        break;\n\n      default:\n        return;\n    } // prevent default behaviour\n\n\n    event.preventDefault();\n  }\n\n  onFormSubmit() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this3.onAddingRequested(false, _this3.formValue);\n      } catch {\n        return;\n      }\n    })();\n  }\n  /**\n   * @name setInputValue\n   * @param value\n   */\n\n\n  setInputValue(value, emitEvent = true) {\n    const control = this.getControl(); // update form value with the transformed item\n\n    control.setValue(value, {\n      emitEvent\n    });\n  }\n  /**\n   * @name getControl\n   */\n\n\n  getControl() {\n    return this.inputForm.value;\n  }\n  /**\n   * @name focus\n   * @param applyFocus\n   * @param displayAutocomplete\n   */\n\n\n  focus(applyFocus = false, displayAutocomplete = false) {\n    if (this.dragProvider.getState('dragging')) {\n      return;\n    }\n\n    this.selectItem(undefined, false);\n\n    if (applyFocus) {\n      this.inputForm.focus();\n      this.onFocus.emit(this.formValue);\n    }\n  }\n  /**\n   * @name blur\n   */\n\n\n  blur() {\n    this.onTouched();\n    this.onBlur.emit(this.formValue);\n  }\n  /**\n   * @name hasErrors\n   */\n\n\n  hasErrors() {\n    return !!this.inputForm && this.inputForm.hasErrors();\n  }\n  /**\n   * @name isInputFocused\n   */\n\n\n  isInputFocused() {\n    return !!this.inputForm && this.inputForm.isInputFocused();\n  }\n  /**\n   * - this is the one way I found to tell if the template has been passed and it is not\n   * the template for the menu item\n   * @name hasCustomTemplate\n   */\n\n\n  hasCustomTemplate() {\n    const template = this.templates ? this.templates.first : undefined;\n    const menuTemplate = this.dropdown && this.dropdown.templates ? this.dropdown.templates.first : undefined;\n    return Boolean(template && template !== menuTemplate);\n  }\n  /**\n   * @name maxItemsReached\n   */\n\n\n  get maxItemsReached() {\n    return this.maxItems !== undefined && this.items.length >= this.maxItems;\n  }\n  /**\n   * @name formValue\n   */\n\n\n  get formValue() {\n    const form = this.inputForm.value;\n    return form ? form.value : '';\n  }\n  /**3\n   * @name onDragStarted\n   * @param event\n   * @param index\n   */\n\n\n  onDragStarted(event, tag, index) {\n    event.stopPropagation();\n    const item = {\n      zone: this.dragZone,\n      tag,\n      index\n    };\n    this.dragProvider.setSender(this);\n    this.dragProvider.setDraggedItem(event, item);\n    this.dragProvider.setState({\n      dragging: true,\n      index\n    });\n  }\n  /**\n   * @name onDragOver\n   * @param event\n   */\n\n\n  onDragOver(event, index) {\n    this.dragProvider.setState({\n      dropping: true\n    });\n    this.dragProvider.setReceiver(this);\n    event.preventDefault();\n  }\n  /**\n   * @name onTagDropped\n   * @param event\n   * @param index\n   */\n\n\n  onTagDropped(event, index) {\n    const item = this.dragProvider.getDraggedItem(event);\n\n    if (!item || item.zone !== this.dragZone) {\n      return;\n    }\n\n    this.dragProvider.onTagDropped(item.tag, item.index, index);\n    event.preventDefault();\n    event.stopPropagation();\n  }\n  /**\n   * @name isDropping\n   */\n\n\n  isDropping() {\n    const isReceiver = this.dragProvider.receiver === this;\n    const isDropping = this.dragProvider.getState('dropping');\n    return Boolean(isReceiver && isDropping);\n  }\n  /**\n   * @name onTagBlurred\n   * @param changedElement {TagModel}\n   * @param index {number}\n   */\n\n\n  onTagBlurred(changedElement, index) {\n    this.items[index] = changedElement;\n    this.blur();\n  }\n  /**\n   * @name trackBy\n   * @param items\n   */\n\n\n  trackBy(index, item) {\n    return item[this.identifyBy];\n  }\n  /**\n   * @name updateEditedTag\n   * @param tag\n   */\n\n\n  updateEditedTag(tag) {\n    this.onTagEdited.emit(tag);\n  }\n  /**\n   * @name moveToTag\n   * @param item\n   * @param direction\n   */\n\n\n  moveToTag(item, direction) {\n    const isLast = this.isLastTag(item);\n    const isFirst = this.isFirstTag(item);\n    const stopSwitch = direction === NEXT && isLast || direction === PREV && isFirst;\n\n    if (stopSwitch) {\n      this.focus(true);\n      return;\n    }\n\n    const offset = direction === NEXT ? 1 : -1;\n    const index = this.getTagIndex(item) + offset;\n    const tag = this.getTagAtIndex(index);\n    return tag.select.call(tag);\n  }\n  /**\n   * @name isFirstTag\n   * @param item {TagModel}\n   */\n\n\n  isFirstTag(item) {\n    return this.tags.first.model === item;\n  }\n  /**\n   * @name isLastTag\n   * @param item {TagModel}\n   */\n\n\n  isLastTag(item) {\n    return this.tags.last.model === item;\n  }\n  /**\n   * @name getTagIndex\n   * @param item\n   */\n\n\n  getTagIndex(item) {\n    const tags = this.tags.toArray();\n    return tags.findIndex(tag => tag.model === item);\n  }\n  /**\n   * @name getTagAtIndex\n   * @param index\n   */\n\n\n  getTagAtIndex(index) {\n    const tags = this.tags.toArray();\n    return tags[index];\n  }\n  /**\n   * @name removeItem\n   * @desc removes an item from the array of the model\n   * @param tag {TagModel}\n   * @param index {number}\n   */\n\n\n  removeItem(tag, index) {\n    this.items = this.getItemsWithout(index); // if the removed tag was selected, set it as undefined\n\n    if (this.selectedTag === tag) {\n      this.selectItem(undefined, false);\n    } // focus input\n\n\n    this.focus(true, false); // emit remove event\n\n    this.onRemove.emit(tag);\n  }\n  /**\n   * @name addItem\n   * @desc adds the current text model to the items array\n   * @param fromAutocomplete {boolean}\n   * @param item {TagModel}\n   * @param index? {number}\n   * @param giveupFocus? {boolean}\n   */\n\n\n  addItem(fromAutocomplete = false, item, index, giveupFocus) {\n    const display = this.getItemDisplay(item);\n    const tag = this.createTag(item);\n\n    if (fromAutocomplete) {\n      this.setInputValue(this.getItemValue(item, true));\n    }\n\n    return new Promise((resolve, reject) => {\n      /**\n       * @name reset\n       */\n      const reset = () => {\n        // reset control and focus input\n        this.setInputValue('');\n\n        if (giveupFocus) {\n          this.focus(false, false);\n        } else {\n          // focus input\n          this.focus(true, false);\n        }\n\n        resolve(display);\n      };\n\n      const appendItem = () => {\n        this.appendTag(tag, index); // emit event\n\n        this.onAdd.emit(tag);\n\n        if (!this.dropdown) {\n          return;\n        }\n\n        this.dropdown.hide();\n\n        if (this.dropdown.showDropdownIfEmpty) {\n          this.dropdown.show();\n        }\n      };\n\n      const status = this.inputForm.form.status;\n      const isTagValid = this.isTagValid(tag, fromAutocomplete);\n\n      const onValidationError = () => {\n        this.onValidationError.emit(tag);\n        return reject();\n      };\n\n      if (status === 'VALID' && isTagValid) {\n        appendItem();\n        return reset();\n      }\n\n      if (status === 'INVALID' || !isTagValid) {\n        reset();\n        return onValidationError();\n      }\n\n      if (status === 'PENDING') {\n        const statusUpdate$ = this.inputForm.form.statusChanges;\n        return statusUpdate$.pipe(filter$1(statusUpdate => statusUpdate !== 'PENDING'), first$1()).subscribe(statusUpdate => {\n          if (statusUpdate === 'VALID' && isTagValid) {\n            appendItem();\n            return reset();\n          } else {\n            reset();\n            return onValidationError();\n          }\n        });\n      }\n    });\n  }\n  /**\n   * @name setupSeparatorKeysListener\n   */\n\n\n  setupSeparatorKeysListener() {\n    const useSeparatorKeys = this.separatorKeyCodes.length > 0 || this.separatorKeys.length > 0;\n\n    const listener = $event => {\n      const hasKeyCode = this.separatorKeyCodes.indexOf($event.keyCode) >= 0;\n      const hasKey = this.separatorKeys.indexOf($event.key) >= 0; // the keyCode of keydown event is 229 when IME is processing the key event.\n\n      const isIMEProcessing = $event.keyCode === 229;\n\n      if (hasKeyCode || hasKey && !isIMEProcessing) {\n        $event.preventDefault();\n        this.onAddingRequested(false, this.formValue).catch(() => {});\n      }\n    };\n\n    listen.call(this, KEYDOWN, listener, useSeparatorKeys);\n  }\n  /**\n   * @name setUpKeypressListeners\n   */\n\n\n  setUpKeypressListeners() {\n    const listener = $event => {\n      const isCorrectKey = $event.keyCode === 37 || $event.keyCode === 8;\n\n      if (isCorrectKey && !this.formValue && this.items.length) {\n        this.tags.last.select.call(this.tags.last);\n      }\n    }; // setting up the keypress listeners\n\n\n    listen.call(this, KEYDOWN, listener);\n  }\n  /**\n   * @name setUpKeydownListeners\n   */\n\n\n  setUpInputKeydownListeners() {\n    this.inputForm.onKeydown.subscribe(event => {\n      if (event.key === 'Backspace' && this.formValue.trim() === '') {\n        event.preventDefault();\n      }\n    });\n  }\n  /**\n   * @name setUpOnPasteListener\n   */\n\n\n  setUpOnPasteListener() {\n    const input = this.inputForm.input.nativeElement; // attach listener to input\n\n    this.renderer.listen(input, 'paste', event => {\n      this.onPasteCallback(event);\n      event.preventDefault();\n      return true;\n    });\n  }\n  /**\n   * @name setUpTextChangeSubscriber\n   */\n\n\n  setUpTextChangeSubscriber() {\n    this.inputForm.form.valueChanges.pipe(debounceTime$1(this.onTextChangeDebounce)).subscribe(value => {\n      this.onTextChange.emit(value.item);\n    });\n  }\n  /**\n   * @name setUpOnBlurSubscriber\n   */\n\n\n  setUpOnBlurSubscriber() {\n    const filterFn = () => {\n      const isVisible = this.dropdown && this.dropdown.isVisible;\n      return !isVisible && !!this.formValue;\n    };\n\n    this.inputForm.onBlur.pipe(debounceTime$1(100), filter$1(filterFn)).subscribe(() => {\n      const reset = () => this.setInputValue('');\n\n      if (this.addOnBlur) {\n        return this.onAddingRequested(false, this.formValue, undefined, true).then(reset).catch(reset);\n      }\n\n      reset();\n    });\n  }\n  /**\n   * @name findDupe\n   * @param tag\n   * @param isFromAutocomplete\n   */\n\n\n  findDupe(tag, isFromAutocomplete) {\n    const identifyBy = isFromAutocomplete ? this.dropdown.identifyBy : this.identifyBy;\n    const id = tag[identifyBy];\n    return this.items.find(item => this.getItemValue(item) === id);\n  }\n  /**\n   * @name setAnimationMetadata\n   */\n\n\n  setAnimationMetadata() {\n    this.animationMetadata = {\n      value: 'in',\n      params: { ...this.animationDuration\n      }\n    };\n  }\n\n}\n\nTagInputComponent.ɵfac = function TagInputComponent_Factory(t) {\n  return new (t || TagInputComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DragProvider));\n};\n\nTagInputComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TagInputComponent,\n  selectors: [[\"tag-input\"]],\n  contentQueries: function TagInputComponent_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, TagInputDropdown, 5);\n      i0.ɵɵcontentQuery(dirIndex, TemplateRef, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdown = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function TagInputComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(TagInputForm, 5);\n      i0.ɵɵviewQuery(TagComponent, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputForm = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tags = _t);\n    }\n  },\n  hostVars: 1,\n  hostBindings: function TagInputComponent_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"tabindex\", ctx.tabindexAttr);\n    }\n  },\n  inputs: {\n    separatorKeys: \"separatorKeys\",\n    separatorKeyCodes: \"separatorKeyCodes\",\n    placeholder: \"placeholder\",\n    secondaryPlaceholder: \"secondaryPlaceholder\",\n    maxItems: \"maxItems\",\n    validators: \"validators\",\n    asyncValidators: \"asyncValidators\",\n    onlyFromAutocomplete: \"onlyFromAutocomplete\",\n    errorMessages: \"errorMessages\",\n    theme: \"theme\",\n    onTextChangeDebounce: \"onTextChangeDebounce\",\n    inputId: \"inputId\",\n    inputClass: \"inputClass\",\n    clearOnBlur: \"clearOnBlur\",\n    hideForm: \"hideForm\",\n    addOnBlur: \"addOnBlur\",\n    addOnPaste: \"addOnPaste\",\n    pasteSplitPattern: \"pasteSplitPattern\",\n    blinkIfDupe: \"blinkIfDupe\",\n    removable: \"removable\",\n    editable: \"editable\",\n    allowDupes: \"allowDupes\",\n    modelAsStrings: \"modelAsStrings\",\n    trimTags: \"trimTags\",\n    inputText: \"inputText\",\n    ripple: \"ripple\",\n    tabindex: \"tabindex\",\n    disable: \"disable\",\n    dragZone: \"dragZone\",\n    onRemoving: \"onRemoving\",\n    onAdding: \"onAdding\",\n    animationDuration: \"animationDuration\"\n  },\n  outputs: {\n    onAdd: \"onAdd\",\n    onRemove: \"onRemove\",\n    onSelect: \"onSelect\",\n    onFocus: \"onFocus\",\n    onBlur: \"onBlur\",\n    onTextChange: \"onTextChange\",\n    onPaste: \"onPaste\",\n    onValidationError: \"onValidationError\",\n    onTagEdited: \"onTagEdited\",\n    inputTextChange: \"inputTextChange\"\n  },\n  features: [i0.ɵɵProvidersFeature([CUSTOM_ACCESSOR]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c3,\n  decls: 8,\n  vars: 27,\n  consts: [[1, \"ng2-tag-input\", 3, \"ngClass\", \"click\", \"drop\", \"dragenter\", \"dragover\", \"dragend\"], [1, \"ng2-tags-container\"], [3, \"draggable\", \"canAddTag\", \"disabled\", \"hasRipple\", \"index\", \"removable\", \"editable\", \"displayBy\", \"identifyBy\", \"template\", \"model\", \"onSelect\", \"onRemove\", \"onKeyDown\", \"onTagEdited\", \"onBlur\", \"dragstart\", \"drop\", \"dragenter\", \"dragover\", \"dragleave\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"inputText\", \"disabled\", \"validators\", \"asyncValidators\", \"hidden\", \"placeholder\", \"inputClass\", \"inputId\", \"tabindex\", \"onSubmit\", \"onBlur\", \"click\", \"onKeydown\", \"onKeyup\"], [\"class\", \"progress-bar\", 4, \"ngIf\"], [\"class\", \"error-messages\", 3, \"ngClass\", 4, \"ngIf\"], [3, \"draggable\", \"canAddTag\", \"disabled\", \"hasRipple\", \"index\", \"removable\", \"editable\", \"displayBy\", \"identifyBy\", \"template\", \"model\", \"onSelect\", \"onRemove\", \"onKeyDown\", \"onTagEdited\", \"onBlur\", \"dragstart\", \"drop\", \"dragenter\", \"dragover\", \"dragleave\"], [1, \"progress-bar\"], [1, \"error-messages\", 3, \"ngClass\"], [\"class\", \"error-message\", 4, \"ngFor\", \"ngForOf\"], [1, \"error-message\"]],\n  template: function TagInputComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"click\", function TagInputComponent_Template_div_click_0_listener() {\n        return ctx.focus(true, false);\n      })(\"drop\", function TagInputComponent_Template_div_drop_0_listener($event) {\n        return ctx.dragZone ? ctx.onTagDropped($event, undefined) : undefined;\n      })(\"dragenter\", function TagInputComponent_Template_div_dragenter_0_listener($event) {\n        return ctx.dragZone ? ctx.onDragOver($event) : undefined;\n      })(\"dragover\", function TagInputComponent_Template_div_dragover_0_listener($event) {\n        return ctx.dragZone ? ctx.onDragOver($event) : undefined;\n      })(\"dragend\", function TagInputComponent_Template_div_dragend_0_listener() {\n        return ctx.dragZone ? ctx.dragProvider.onDragEnd() : undefined;\n      });\n      i0.ɵɵelementStart(1, \"div\", 1);\n      i0.ɵɵtemplate(2, TagInputComponent_tag_2_Template, 1, 14, \"tag\", 2);\n      i0.ɵɵelementStart(3, \"tag-input-form\", 3);\n      i0.ɵɵlistener(\"onSubmit\", function TagInputComponent_Template_tag_input_form_onSubmit_3_listener() {\n        return ctx.onFormSubmit();\n      })(\"onBlur\", function TagInputComponent_Template_tag_input_form_onBlur_3_listener() {\n        return ctx.blur();\n      })(\"click\", function TagInputComponent_Template_tag_input_form_click_3_listener() {\n        return ctx.dropdown ? ctx.dropdown.show() : undefined;\n      })(\"onKeydown\", function TagInputComponent_Template_tag_input_form_onKeydown_3_listener($event) {\n        return ctx.fireEvents(\"keydown\", $event);\n      })(\"onKeyup\", function TagInputComponent_Template_tag_input_form_onKeyup_3_listener($event) {\n        return ctx.fireEvents(\"keyup\", $event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, TagInputComponent_div_4_Template, 1, 0, \"div\", 4);\n      i0.ɵɵpipe(5, \"async\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(6, TagInputComponent_div_6_Template, 2, 2, \"div\", 5);\n      i0.ɵɵprojection(7);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"ng2-tag-input--dropping\", ctx.isDropping())(\"ng2-tag-input--disabled\", ctx.disable)(\"ng2-tag-input--loading\", ctx.isLoading)(\"ng2-tag-input--invalid\", ctx.hasErrors())(\"ng2-tag-input--focused\", ctx.isInputFocused());\n      i0.ɵɵproperty(\"ngClass\", ctx.theme);\n      i0.ɵɵattribute(\"tabindex\", -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.items)(\"ngForTrackBy\", ctx.trackBy);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"inputText\", ctx.inputText)(\"disabled\", ctx.disable)(\"validators\", ctx.validators)(\"asyncValidators\", ctx.asyncValidators)(\"hidden\", ctx.maxItemsReached)(\"placeholder\", ctx.items.length ? ctx.placeholder : ctx.secondaryPlaceholder)(\"inputClass\", ctx.inputClass)(\"inputId\", ctx.inputId)(\"tabindex\", ctx.tabindex);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(5, 25, ctx.isProgressBarVisible$));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.hasErrors());\n    }\n  },\n  directives: [i2.NgClass, i2.NgForOf, TagInputForm, i2.NgIf, TagComponent],\n  pipes: [i2.AsyncPipe],\n  styles: [\".dark[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused[_ngcontent-%COMP%]{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.ng2-tag-input.ng2-tag-input--focused[_ngcontent-%COMP%]{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.ng2-tag-input[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{margin:.1em 0}.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.dark.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused[_ngcontent-%COMP%]{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping[_ngcontent-%COMP%]{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid[_ngcontent-%COMP%]{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading[_ngcontent-%COMP%]{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled[_ngcontent-%COMP%]{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{margin:.1em 0}.bootstrap3-info.ng2-tag-input[_ngcontent-%COMP%]   .ng2-tags-container[_ngcontent-%COMP%]{flex-wrap:wrap;display:flex}.error-message[_ngcontent-%COMP%]{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#d9534f}.progress-bar[_ngcontent-%COMP%], .progress-bar[_ngcontent-%COMP%]:before{height:2px;width:100%;margin:0}.progress-bar[_ngcontent-%COMP%]{background-color:#2196f3;display:flex;position:absolute;bottom:0}.progress-bar[_ngcontent-%COMP%]:before{background-color:#82c4f8;content:\\\"\\\";-webkit-animation:running-progress 2s cubic-bezier(.4,0,.2,1) infinite;animation:running-progress 2s cubic-bezier(.4,0,.2,1) infinite}@-webkit-keyframes running-progress{0%{margin-left:0;margin-right:100%}50%{margin-left:25%;margin-right:0}to{margin-left:100%;margin-right:0}}@keyframes running-progress{0%{margin-left:0;margin-right:100%}50%{margin-left:25%;margin-right:0}to{margin-left:100%;margin-right:0}}tag[_ngcontent-%COMP%]{display:flex;flex-direction:row;flex-wrap:wrap;font-family:Roboto,Helvetica Neue,sans-serif;font-weight:400;font-size:1em;letter-spacing:.05rem;color:#444;border-radius:16px;transition:all .3s;margin:.1rem .3rem .1rem 0;padding:.08rem .45rem;height:32px;line-height:34px;background:#efefef;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}tag[_ngcontent-%COMP%]:not(.readonly):not(.tag--editing):focus{background:#2196F3;color:#fff;box-shadow:0 2px 3px 1px #d4d1d1}tag[_ngcontent-%COMP%]:not(.readonly):not(.tag--editing):active{background:#0d8aee;color:#fff;box-shadow:0 2px 3px 1px #d4d1d1}tag[_ngcontent-%COMP%]:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#e2e2e2;color:#000;color:initial;box-shadow:0 2px 3px 1px #d4d1d1}tag.readonly[_ngcontent-%COMP%]{cursor:default}tag.readonly[_ngcontent-%COMP%]:focus, tag[_ngcontent-%COMP%]:focus{outline:0}tag.tag--editing[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #ccc;cursor:text}.minimal[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]{display:flex;flex-direction:row;flex-wrap:wrap;border-radius:0;background:#f9f9f9;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.minimal[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(.readonly):not(.tag--editing):focus{background:#d0d0d0;color:#000;color:initial}.minimal[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(.readonly):not(.tag--editing):active{background:#d0d0d0;color:#000;color:initial}.minimal[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#ececec}.minimal[_ngcontent-%COMP%]   tag.readonly[_ngcontent-%COMP%]{cursor:default}.minimal[_ngcontent-%COMP%]   tag.readonly[_ngcontent-%COMP%]:focus, .minimal[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:focus{outline:0}.minimal[_ngcontent-%COMP%]   tag.tag--editing[_ngcontent-%COMP%]{cursor:text}.dark[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]{display:flex;flex-direction:row;flex-wrap:wrap;color:#f9f9f9;border-radius:3px;background:#444;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.dark[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(.readonly):not(.tag--editing):focus{background:#efefef;color:#444}.dark[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#2b2b2b;color:#f9f9f9}.dark[_ngcontent-%COMP%]   tag.readonly[_ngcontent-%COMP%]{cursor:default}.dark[_ngcontent-%COMP%]   tag.readonly[_ngcontent-%COMP%]:focus, .dark[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:focus{outline:0}.dark[_ngcontent-%COMP%]   tag.tag--editing[_ngcontent-%COMP%]{cursor:text}.bootstrap[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]{display:flex;flex-direction:row;flex-wrap:wrap;color:#f9f9f9;border-radius:.25rem;background:#0275d8;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.bootstrap[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(.readonly):not(.tag--editing):focus{background:#025aa5}.bootstrap[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(.readonly):not(.tag--editing):active{background:#025aa5}.bootstrap[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#0267bf;color:#f9f9f9}.bootstrap[_ngcontent-%COMP%]   tag.readonly[_ngcontent-%COMP%]{cursor:default}.bootstrap[_ngcontent-%COMP%]   tag.readonly[_ngcontent-%COMP%]:focus, .bootstrap[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:focus{outline:0}.bootstrap[_ngcontent-%COMP%]   tag.tag--editing[_ngcontent-%COMP%]{cursor:text}.bootstrap3-info[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]{display:flex;flex-direction:row;flex-wrap:wrap;font-family:inherit;font-weight:400;font-size:95%;color:#fff;border-radius:.25em;background:#5bc0de;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative;padding:.25em .6em;text-align:center;white-space:nowrap}.bootstrap3-info[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(.readonly):not(.tag--editing):focus{background:#28a1c5}.bootstrap3-info[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(.readonly):not(.tag--editing):active{background:#28a1c5}.bootstrap3-info[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#46b8da;color:#fff}.bootstrap3-info[_ngcontent-%COMP%]   tag.readonly[_ngcontent-%COMP%]{cursor:default}.bootstrap3-info[_ngcontent-%COMP%]   tag.readonly[_ngcontent-%COMP%]:focus, .bootstrap3-info[_ngcontent-%COMP%]   tag[_ngcontent-%COMP%]:focus{outline:0}.bootstrap3-info[_ngcontent-%COMP%]   tag.tag--editing[_ngcontent-%COMP%]{cursor:text}[_nghost-%COMP%]{display:block}\"],\n  data: {\n    animation: animations\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TagInputComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tag-input',\n      providers: [CUSTOM_ACCESSOR],\n      animations: animations,\n      template: \"<div\\n    [ngClass]=\\\"theme\\\"\\n    class=\\\"ng2-tag-input\\\"\\n    (click)=\\\"focus(true, false)\\\"\\n    [attr.tabindex]=\\\"-1\\\"\\n    (drop)=\\\"dragZone ? onTagDropped($event, undefined) : undefined\\\"\\n    (dragenter)=\\\"dragZone ? onDragOver($event) : undefined\\\"\\n    (dragover)=\\\"dragZone ? onDragOver($event) : undefined\\\"\\n    (dragend)=\\\"dragZone ? dragProvider.onDragEnd() : undefined\\\"\\n    [class.ng2-tag-input--dropping]=\\\"isDropping()\\\"\\n    [class.ng2-tag-input--disabled]=\\\"disable\\\"\\n    [class.ng2-tag-input--loading]=\\\"isLoading\\\"\\n    [class.ng2-tag-input--invalid]=\\\"hasErrors()\\\"\\n    [class.ng2-tag-input--focused]=\\\"isInputFocused()\\\"\\n>\\n\\n    <!-- TAGS -->\\n    <div class=\\\"ng2-tags-container\\\">\\n        <tag\\n            *ngFor=\\\"let item of items; let i = index; trackBy: trackBy\\\"\\n            (onSelect)=\\\"selectItem(item)\\\"\\n            (onRemove)=\\\"onRemoveRequested(item, i)\\\"\\n            (onKeyDown)=\\\"handleKeydown($event)\\\"\\n            (onTagEdited)=\\\"updateEditedTag($event)\\\"\\n            (onBlur)=\\\"onTagBlurred($event, i)\\\"\\n            draggable=\\\"{{ editable }}\\\"\\n            (dragstart)=\\\"dragZone ? onDragStarted($event, item, i) : undefined\\\"\\n            (drop)=\\\"dragZone ? onTagDropped($event, i) : undefined\\\"\\n            (dragenter)=\\\"dragZone ? onDragOver($event) : undefined\\\"\\n            (dragover)=\\\"dragZone ? onDragOver($event, i) : undefined\\\"\\n            (dragleave)=\\\"dragZone ? dragProvider.onDragEnd() : undefined\\\"\\n            [canAddTag]=\\\"isTagValid\\\"\\n            [attr.tabindex]=\\\"0\\\"\\n            [disabled]=\\\"disable\\\"\\n            [@animation]=\\\"animationMetadata\\\"\\n            [hasRipple]=\\\"ripple\\\"\\n            [index]=\\\"i\\\"\\n            [removable]=\\\"removable\\\"\\n            [editable]=\\\"editable\\\"\\n            [displayBy]=\\\"displayBy\\\"\\n            [identifyBy]=\\\"identifyBy\\\"\\n            [template]=\\\"!!hasCustomTemplate() ? templates.first : undefined\\\"\\n            [draggable]=\\\"dragZone\\\"\\n            [model]=\\\"item\\\"\\n        >\\n        </tag>\\n\\n        <tag-input-form\\n            (onSubmit)=\\\"onFormSubmit()\\\"\\n            (onBlur)=\\\"blur()\\\"\\n            (click)=\\\"dropdown ? dropdown.show() : undefined\\\"\\n            (onKeydown)=\\\"fireEvents('keydown', $event)\\\"\\n            (onKeyup)=\\\"fireEvents('keyup', $event)\\\"\\n            [inputText]=\\\"inputText\\\"\\n            [disabled]=\\\"disable\\\"\\n            [validators]=\\\"validators\\\"\\n            [asyncValidators]=\\\"asyncValidators\\\"\\n            [hidden]=\\\"maxItemsReached\\\"\\n            [placeholder]=\\\"items.length ? placeholder : secondaryPlaceholder\\\"\\n            [inputClass]=\\\"inputClass\\\"\\n            [inputId]=\\\"inputId\\\"\\n            [tabindex]=\\\"tabindex\\\"\\n        >\\n        </tag-input-form>\\n    </div>\\n\\n    <div\\n        class=\\\"progress-bar\\\"\\n        *ngIf=\\\"isProgressBarVisible$ | async\\\"\\n    ></div>\\n</div>\\n\\n<!-- ERRORS -->\\n<div\\n    *ngIf=\\\"hasErrors()\\\"\\n    [ngClass]=\\\"theme\\\"\\n    class=\\\"error-messages\\\"\\n>\\n    <p\\n        *ngFor=\\\"let error of errors\\\"\\n        class=\\\"error-message\\\"\\n    >\\n        <span>{{ error }}</span>\\n    </p>\\n</div>\\n<ng-content></ng-content>\\n\",\n      styles: [\".dark tag:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info input{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info .form-control input::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info .form-control input:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info .form-control input::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info input:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading{border:none}.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.ng2-tag-input form{margin:.1em 0}.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.dark.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input form{margin:.1em 0}.bootstrap3-info.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.error-message{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap .error-message{color:#d9534f}.progress-bar,.progress-bar:before{height:2px;width:100%;margin:0}.progress-bar{background-color:#2196f3;display:flex;position:absolute;bottom:0}.progress-bar:before{background-color:#82c4f8;content:\\\"\\\";-webkit-animation:running-progress 2s cubic-bezier(.4,0,.2,1) infinite;animation:running-progress 2s cubic-bezier(.4,0,.2,1) infinite}@-webkit-keyframes running-progress{0%{margin-left:0;margin-right:100%}50%{margin-left:25%;margin-right:0}to{margin-left:100%;margin-right:0}}@keyframes running-progress{0%{margin-left:0;margin-right:100%}50%{margin-left:25%;margin-right:0}to{margin-left:100%;margin-right:0}}tag{display:flex;flex-direction:row;flex-wrap:wrap;font-family:Roboto,Helvetica Neue,sans-serif;font-weight:400;font-size:1em;letter-spacing:.05rem;color:#444;border-radius:16px;transition:all .3s;margin:.1rem .3rem .1rem 0;padding:.08rem .45rem;height:32px;line-height:34px;background:#efefef;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}tag:not(.readonly):not(.tag--editing):focus{background:#2196F3;color:#fff;box-shadow:0 2px 3px 1px #d4d1d1}tag:not(.readonly):not(.tag--editing):active{background:#0d8aee;color:#fff;box-shadow:0 2px 3px 1px #d4d1d1}tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#e2e2e2;color:#000;color:initial;box-shadow:0 2px 3px 1px #d4d1d1}tag.readonly{cursor:default}tag.readonly:focus,tag:focus{outline:0}tag.tag--editing{background-color:#fff;border:1px solid #ccc;cursor:text}.minimal tag{display:flex;flex-direction:row;flex-wrap:wrap;border-radius:0;background:#f9f9f9;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.minimal tag:not(.readonly):not(.tag--editing):focus{background:#d0d0d0;color:#000;color:initial}.minimal tag:not(.readonly):not(.tag--editing):active{background:#d0d0d0;color:#000;color:initial}.minimal tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#ececec}.minimal tag.readonly{cursor:default}.minimal tag.readonly:focus,.minimal tag:focus{outline:0}.minimal tag.tag--editing{cursor:text}.dark tag{display:flex;flex-direction:row;flex-wrap:wrap;color:#f9f9f9;border-radius:3px;background:#444;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.dark tag:not(.readonly):not(.tag--editing):focus{background:#efefef;color:#444}.dark tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#2b2b2b;color:#f9f9f9}.dark tag.readonly{cursor:default}.dark tag.readonly:focus,.dark tag:focus{outline:0}.dark tag.tag--editing{cursor:text}.bootstrap tag{display:flex;flex-direction:row;flex-wrap:wrap;color:#f9f9f9;border-radius:.25rem;background:#0275d8;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.bootstrap tag:not(.readonly):not(.tag--editing):focus{background:#025aa5}.bootstrap tag:not(.readonly):not(.tag--editing):active{background:#025aa5}.bootstrap tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#0267bf;color:#f9f9f9}.bootstrap tag.readonly{cursor:default}.bootstrap tag.readonly:focus,.bootstrap tag:focus{outline:0}.bootstrap tag.tag--editing{cursor:text}.bootstrap3-info tag{display:flex;flex-direction:row;flex-wrap:wrap;font-family:inherit;font-weight:400;font-size:95%;color:#fff;border-radius:.25em;background:#5bc0de;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative;padding:.25em .6em;text-align:center;white-space:nowrap}.bootstrap3-info tag:not(.readonly):not(.tag--editing):focus{background:#28a1c5}.bootstrap3-info tag:not(.readonly):not(.tag--editing):active{background:#28a1c5}.bootstrap3-info tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#46b8da;color:#fff}.bootstrap3-info tag.readonly{cursor:default}.bootstrap3-info tag.readonly:focus,.bootstrap3-info tag:focus{outline:0}.bootstrap3-info tag.tag--editing{cursor:text}:host{display:block}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: DragProvider\n    }];\n  }, {\n    separatorKeys: [{\n      type: Input\n    }],\n    separatorKeyCodes: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    secondaryPlaceholder: [{\n      type: Input\n    }],\n    maxItems: [{\n      type: Input\n    }],\n    validators: [{\n      type: Input\n    }],\n    asyncValidators: [{\n      type: Input\n    }],\n    onlyFromAutocomplete: [{\n      type: Input\n    }],\n    errorMessages: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    onTextChangeDebounce: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    inputClass: [{\n      type: Input\n    }],\n    clearOnBlur: [{\n      type: Input\n    }],\n    hideForm: [{\n      type: Input\n    }],\n    addOnBlur: [{\n      type: Input\n    }],\n    addOnPaste: [{\n      type: Input\n    }],\n    pasteSplitPattern: [{\n      type: Input\n    }],\n    blinkIfDupe: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input\n    }],\n    editable: [{\n      type: Input\n    }],\n    allowDupes: [{\n      type: Input\n    }],\n    modelAsStrings: [{\n      type: Input\n    }],\n    trimTags: [{\n      type: Input\n    }],\n    inputText: [{\n      type: Input\n    }],\n    ripple: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    disable: [{\n      type: Input\n    }],\n    dragZone: [{\n      type: Input\n    }],\n    onRemoving: [{\n      type: Input\n    }],\n    onAdding: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }],\n    onAdd: [{\n      type: Output\n    }],\n    onRemove: [{\n      type: Output\n    }],\n    onSelect: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onTextChange: [{\n      type: Output\n    }],\n    onPaste: [{\n      type: Output\n    }],\n    onValidationError: [{\n      type: Output\n    }],\n    onTagEdited: [{\n      type: Output\n    }],\n    dropdown: [{\n      type: ContentChild,\n      args: [TagInputDropdown]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [TemplateRef, {\n        descendants: false\n      }]\n    }],\n    inputForm: [{\n      type: ViewChild,\n      args: [TagInputForm]\n    }],\n    tags: [{\n      type: ViewChildren,\n      args: [TagComponent]\n    }],\n    inputTextChange: [{\n      type: Output\n    }],\n    tabindexAttr: [{\n      type: HostBinding,\n      args: ['attr.tabindex']\n    }]\n  });\n})();\n\nconst optionsProvider = new OptionsProvider();\n\nclass TagInputModule {\n  /**\n   * @name withDefaults\n   * @param options {Options}\n   */\n  static withDefaults(options) {\n    optionsProvider.setOptions(options);\n  }\n\n}\n\nTagInputModule.ɵfac = function TagInputModule_Factory(t) {\n  return new (t || TagInputModule)();\n};\n\nTagInputModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TagInputModule\n});\nTagInputModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [DragProvider, {\n    provide: COMPOSITION_BUFFER_MODE,\n    useValue: false\n  }],\n  imports: [[CommonModule, ReactiveFormsModule, FormsModule, Ng2DropdownModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TagInputModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, ReactiveFormsModule, FormsModule, Ng2DropdownModule],\n      declarations: [TagInputComponent, DeleteIconComponent, TagInputForm, TagComponent, HighlightPipe, TagInputDropdown, TagRipple],\n      exports: [TagInputComponent, DeleteIconComponent, TagInputForm, TagComponent, HighlightPipe, TagInputDropdown, TagRipple],\n      providers: [DragProvider, {\n        provide: COMPOSITION_BUFFER_MODE,\n        useValue: false\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { DeleteIconComponent, HighlightPipe, TagComponent, TagInputComponent, TagInputDropdown, TagInputForm, TagInputModule, TagRipple };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/ngx-chips/fesm2020/ngx-chips.mjs"], "names": ["i0", "<PERSON><PERSON>", "Injectable", "Directive", "Input", "EventEmitter", "Component", "Output", "ViewChild", "HostBinding", "HostListener", "TemplateRef", "ContentChildren", "forwardRef", "ContentChild", "ViewChildren", "NgModule", "i1", "FormControl", "FormGroup", "NG_VALUE_ACCESSOR", "ReactiveFormsModule", "FormsModule", "COMPOSITION_BUFFER_MODE", "i2", "CommonModule", "i1$1", "Ng2Dropdown", "Ng2DropdownModule", "filter", "filter$1", "map", "first", "first$1", "debounceTime", "debounceTime$1", "trigger", "state", "style", "transition", "animate", "keyframes", "distinctUntilChanged", "escape", "s", "replace", "HighlightPipe", "transform", "value", "arg", "trim", "regex", "RegExp", "e", "ɵfac", "ɵpipe", "type", "args", "name", "PLACEHOLDER", "SECONDARY_PLACEHOLDER", "KEYDOWN", "KEYUP", "FOCUS", "MAX_ITEMS_WARNING", "ACTIONS_KEYS", "DELETE", "SWITCH_PREV", "SWITCH_NEXT", "TAB", "KEY_PRESS_ACTIONS", "DRAG_AND_DROP_KEY", "NEXT", "PREV", "<PERSON>ag<PERSON><PERSON><PERSON>", "constructor", "dragging", "dropping", "index", "undefined", "setDraggedItem", "event", "tag", "dataTransfer", "setData", "JSON", "stringify", "getDraggedItem", "data", "getData", "parse", "setSender", "sender", "setReceiver", "receiver", "onTagDropped", "indexDragged", "indexDropped", "onDragEnd", "onRemoveRequested", "onAddingRequested", "setState", "getState", "key", "ɵprov", "defaults", "tagInput", "separator<PERSON>eys", "separatorKeyCodes", "maxItems", "Infinity", "placeholder", "secondaryPlaceholder", "validators", "asyncValidators", "onlyFromAutocomplete", "errorMessages", "theme", "onTextChangeDebounce", "inputId", "inputClass", "clearOnBlur", "hideForm", "addOnBlur", "addOnPaste", "pasteSplitPattern", "blinkIfDupe", "removable", "editable", "allowDupes", "modelAsStrings", "trimTags", "ripple", "tabIndex", "disable", "dragZone", "onRemoving", "onAdding", "displayBy", "identify<PERSON>y", "animationDuration", "enter", "leave", "dropdown", "appendToBody", "offset", "focusFirstElement", "showDropdownIfEmpty", "minimumTextLength", "limitItemsTo", "keep<PERSON>pen", "dynamicUpdate", "zIndex", "matchingFn", "target", "targetValue", "toString", "toLowerCase", "indexOf", "OptionsProvider", "setOptions", "options", "isObject", "obj", "Object", "TagInputAccessor", "_items", "items", "_onChangeCallback", "onTouched", "_onTouchedCallback", "writeValue", "registerOnChange", "fn", "registerOnTouched", "getItemValue", "item", "fromDropdown", "property", "getItemDisplay", "getItemsWithout", "position", "ɵdir", "listen", "listenerType", "action", "condition", "listeners", "hasOwnProperty", "Error", "push", "TagInputForm", "onSubmit", "onBlur", "onFocus", "onKeyup", "onKeydown", "inputTextChange", "tabindex", "disabled", "inputText", "text", "setValue", "emit", "ngOnInit", "setValidators", "setAsyncValidators", "form", "ngOnChanges", "changes", "firstChange", "currentValue", "controls", "enable", "get", "isInputFocused", "doc", "document", "activeElement", "input", "nativeElement", "getErrorMessages", "messages", "keys", "err", "<PERSON><PERSON><PERSON><PERSON>", "hasErrors", "dirty", "valid", "focus", "blur", "getElementPosition", "getBoundingClientRect", "destroy", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "onKeyDown", "$event", "submit", "onKeyUp", "preventDefault", "ɵcmp", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "DefaultValueAccessor", "MinLengthValidator", "NgControlStatus", "FormControlName", "Ng<PERSON><PERSON>", "selector", "template", "styles", "TagRipple", "width", "opacity", "borderRadius", "animations", "DeleteIconComponent", "navigator", "window", "userAgent", "vendor", "isChrome", "test", "TagComponent", "element", "renderer", "cdRef", "onSelect", "onRemove", "onTagEdited", "editing", "rippleState", "readonly", "model", "select", "stopPropagation", "remove", "move", "moving", "keydown", "keyCode", "disableEditMode", "blink", "classList", "add", "setTimeout", "toggleEditMode", "activateEditMode", "onBlurred", "innerText", "result", "getDisplayValue", "isRippleVisible", "hasRipple", "getContentEditableText", "setContentEditableText", "storeNewValue", "detectChanges", "isDeleteIconVisible", "getContentEditable", "exists", "hasId", "canAddTag", "querySelector", "ElementRef", "Renderer2", "ChangeDetectorRef", "NgSwitch", "NgSwitchCase", "NgIf", "NgTemplateOutlet", "TagInputDropdown", "injector", "TagInputComponent", "_autocompleteItems", "show", "max<PERSON><PERSON>sReached", "length", "getFormValue", "hasMinimumText", "calculatePosition", "getMatchingItems", "hasItems", "isHidden", "isVisible", "isDisabled", "shouldShow", "shouldHide", "autocompleteObservable", "getItemsFromObservable", "hide", "setItems", "requestAdding", "createTagModel", "catch", "resetItems", "setLoadingState", "subscribeFn", "populateItems", "pipe", "subscribe", "autocompleteItems", "ngAfterViewInit", "onItemClicked", "onHide", "DEBOUNCE_TIME", "KEEP_OPEN", "onTextChange", "asObservable", "updatePosition", "inputForm", "menu", "dropdownState", "menuState", "selectedItem", "scrollListener", "onWindowBlur", "formValue", "display", "dupesAllowed", "hasValue", "tags", "some", "slice", "isLoading", "Injector", "Ng2DropdownMenu", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ng2MenuItem", "NgSwitchDefault", "templates", "CUSTOM_ACCESSOR", "provide", "useExisting", "multi", "dragProvider", "onAdd", "onPaste", "onValidationError", "inputTextValue", "errors", "appendTag", "createTag", "val", "isTagValid", "fromAutocomplete", "dupe", "findDupe", "find", "isFromAutocomplete", "assertions", "Boolean", "onPasteCallback", "getText", "isIE", "clipboardData", "requests", "split", "setInputValue", "resetInput", "Promise", "all", "then", "tabindexAttr", "setUpKeypressListeners", "setupSeparatorKeysListener", "setUpInputKeydownListeners", "observers", "setUpTextChangeSubscriber", "setUpOnBlurSubscriber", "setUpOnPasteListener", "statusChanges$", "statusChanges", "status", "isProgressBarVisible$", "hasReachedMaxItems", "console", "warn", "setAnimationMetadata", "resolve", "removeItem", "giveupFocus", "reject", "addItem", "selectItem", "is<PERSON><PERSON><PERSON>ly", "selectedTag", "fireEvents", "eventName", "for<PERSON>ach", "listener", "call", "handleKeydown", "which", "shift<PERSON>ey", "moveToTag", "isFirstTag", "isLastTag", "onFormSubmit", "emitEvent", "control", "getControl", "applyFocus", "displayAutocomplete", "hasCustomTemplate", "menuTemplate", "onDragStarted", "zone", "onDragOver", "isDropping", "isReceiver", "onTagBlurred", "changedElement", "trackBy", "updateEditedTag", "direction", "isLast", "<PERSON><PERSON><PERSON><PERSON>", "stopSwitch", "getTagIndex", "getTagAtIndex", "last", "toArray", "findIndex", "reset", "appendItem", "statusUpdate$", "statusUpdate", "useSeparatorKeys", "hasKeyCode", "<PERSON><PERSON><PERSON>", "isIMEProcessing", "isCorrectKey", "valueChanges", "filterFn", "id", "animationMetadata", "params", "AsyncPipe", "providers", "descendants", "optionsProvider", "TagInputModule", "with<PERSON><PERSON><PERSON><PERSON>", "ɵmod", "ɵinj", "useValue", "imports", "declarations", "exports"], "mappings": ";AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,IAAT,EAAeC,UAAf,EAA2BC,SAA3B,EAAsCC,KAAtC,EAA6CC,YAA7C,EAA2DC,SAA3D,EAAsEC,MAAtE,EAA8EC,SAA9E,EAAyFC,WAAzF,EAAsGC,YAAtG,EAAoHC,WAApH,EAAiIC,eAAjI,EAAkJC,UAAlJ,EAA8JC,YAA9J,EAA4KC,YAA5K,EAA0LC,QAA1L,QAA0M,eAA1M;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,WAAT,EAAsBC,SAAtB,EAAiCC,iBAAjC,EAAoDC,mBAApD,EAAyEC,WAAzE,EAAsFC,uBAAtF,QAAqH,gBAArH;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,IAAZ,MAAsB,uBAAtB;AACA,SAASC,WAAT,EAAsBC,iBAAtB,QAA+C,uBAA/C;AACA,SAASC,MAAM,IAAIC,QAAnB,EAA6BC,GAA7B,EAAkCC,KAAK,IAAIC,OAA3C,EAAoDC,YAAY,IAAIC,cAApE,QAA0F,MAA1F;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,EAAqDC,SAArD,QAAsE,qBAAtE;AACA,SAAST,KAAT,EAAgBU,oBAAhB,EAAsCR,YAAtC,EAAoDL,MAApD,QAAkE,gBAAlE;;;;;;;;;;;;;;AAsBgG7B,IAAAA,EAsxBw5B,yB;AAtxBx5BA,IAAAA,EAsxBmgC,iF;AAtxBngCA,IAAAA,EAsxBoqC,e;;;;mBAtxBpqCA,E;AAAAA,IAAAA,EAsxBo7B,+C;AAtxBp7BA,IAAAA,EAsxB6hC,a;AAtxB7hCA,IAAAA,EAsxB6hC,uCAtxB7hCA,EAsxB6hC,0F;;;;;;gBAtxB7hCA,E;;AAAAA,IAAAA,EAsxBgwD,oC;AAtxBhwDA,IAAAA,EAsxB81D;AAtxB91DA,MAAAA,EAsxB81D;AAAA,qBAtxB91DA,EAsxB81D;AAAA,aAAU,qBAAV;AAAA,M;AAtxB91DA,IAAAA,EAsxBg7D,e;;;;;;gBAtxBh7DA,E;;AAAAA,IAAAA,EAsxBkrC,4B;AAtxBlrCA,IAAAA,EAsxB0wC,4B;AAtxB1wCA,IAAAA,EAsxBk8C;AAtxBl8CA,MAAAA,EAsxBk8C;AAAA,qBAtxBl8CA,EAsxBk8C;AAAA,aAAkB,8BAAlB;AAAA;AAtxBl8CA,MAAAA,EAsxBk8C;AAAA,qBAtxBl8CA,EAsxBk8C;AAAA,aAA6E,8BAA7E;AAAA;AAtxBl8CA,MAAAA,EAsxBk8C;AAAA,sBAtxBl8CA,EAsxBk8C;AAAA,+BAAyI,wBAAzI,GAAoK,SAApK;AAAA;AAtxBl8CA,MAAAA,EAsxBk8C;AAAA,sBAtxBl8CA,EAsxBk8C;AAAA,aAAuM,yBAAvM;AAAA,M;AAtxBl8CA,IAAAA,EAsxB2qD,U;AAtxB3qDA,IAAAA,EAsxBitD,e;AAtxBjtDA,IAAAA,EAsxBgwD,iF;AAtxBhwDA,IAAAA,EAsxBo8D,e;;;;mBAtxBp8DA,E;AAAAA,IAAAA,EAsxB+wC,a;AAtxB/wCA,IAAAA,EAsxB+wC,8F;AAtxB/wCA,IAAAA,EAsxB2qD,a;AAtxB3qDA,IAAAA,EAsxB2qD,mE;AAtxB3qDA,IAAAA,EAsxBu4D,a;AAtxBv4DA,IAAAA,EAsxBu4D,iD;;;;;;AAtxBv4DA,IAAAA,EAsxBs9D,8B;;;;mBAtxBt9DA,E;AAAAA,IAAAA,EAsxBk+D,wC;AAtxBl+DA,IAAAA,EAsxBugE,4B;;;;;;AAtxBvgEA,IAAAA,EAkqCsuC,wB;AAlqCtuCA,IAAAA,E;;;;oBAAAA,E;mBAAAA,E;AAAAA,IAAAA,EAkqCuxC,yBAlqCvxCA,EAkqCuxC,sFAlqCvxCA,EAkqCuxC,gB;;;;;;;;;;;;;;;;AAlqCvxCA,IAAAA,EAkqCy4C,iG;;;;mBAlqCz4CA,E;;;;mBAAAA,E;AAAAA,IAAAA,EAkqC87C,mFAlqC97CA,EAkqC87C,qD;;;;;;AAlqC97CA,IAAAA,EAkqCmiC,sC;AAlqCniCA,IAAAA,EAkqCsuC,iF;AAlqCtuCA,IAAAA,EAkqCy4C,+E;AAlqCz4CA,IAAAA,EAkqCymD,e;;;;;mBAlqCzmDA,E;AAAAA,IAAAA,EAkqC2oC,oE;AAlqC3oCA,IAAAA,EAkqC6uC,a;AAlqC7uCA,IAAAA,EAkqC6uC,kC;;;;;;gBAlqC7uCA,E;;AAAAA,IAAAA,EA2jEo3E,4B;AA3jEp3EA,IAAAA,EA2jEi9E;AAAA,0BA3jEj9EA,EA2jEi9E;AAAA;AAAA,qBA3jEj9EA,EA2jEi9E;AAAA,aAAa,0BAAb;AAAA;AAAA,0BA3jEj9EA,EA2jEi9E;AAAA;AAAA;AAAA,qBA3jEj9EA,EA2jEi9E;AAAA,aAA0D,uCAA1D;AAAA;AA3jEj9EA,MAAAA,EA2jEi9E;AAAA,qBA3jEj9EA,EA2jEi9E;AAAA,aAAkH,4BAAlH;AAAA;AA3jEj9EA,MAAAA,EA2jEi9E;AAAA,qBA3jEj9EA,EA2jEi9E;AAAA,aAAuK,8BAAvK;AAAA;AAAA,0BA3jEj9EA,EA2jEi9E;AAAA;AAAA,sBA3jEj9EA,EA2jEi9E;AAAA,aAAyN,kCAAzN;AAAA;AAAA,0BA3jEj9EA,EA2jEi9E;AAAA;AAAA;AAAA,sBA3jEj9EA,EA2jEi9E;AAAA,gCAAmU,4CAAnU,GAAoW,SAApW;AAAA;AAAA,0BA3jEj9EA,EA2jEi9E;AAAA;AAAA,sBA3jEj9EA,EA2jEi9E;AAAA,gCAAiZ,kCAAjZ,GAA2a,SAA3a;AAAA;AA3jEj9EA,MAAAA,EA2jEi9E;AAAA,sBA3jEj9EA,EA2jEi9E;AAAA,gCAA6d,0BAA7d,GAAkf,SAAlf;AAAA;AAAA,0BA3jEj9EA,EA2jEi9E;AAAA;AAAA,sBA3jEj9EA,EA2jEi9E;AAAA,gCAAmiB,gCAAniB,GAA2jB,SAA3jB;AAAA;AA3jEj9EA,MAAAA,EA2jEi9E;AAAA,sBA3jEj9EA,EA2jEi9E;AAAA,gCAA6mB,gCAA7mB,GAAwoB,SAAxoB;AAAA,M;AA3jEj9EA,IAAAA,EA2jEuoH,e;;;;;;mBA3jEvoHA,E;AAAAA,IAAAA,EA2jEitF,oD;AA3jEjtFA,IAAAA,EA2jEknG,qZ;AA3jElnGA,IAAAA,EA2jE0pG,2B;;;;;;AA3jE1pGA,IAAAA,EA2jEm3I,uB;;;;;;AA3jEn3IA,IAAAA,EA2jEilJ,2B;AA3jEjlJA,IAAAA,EA2jE6qJ,0B;AA3jE7qJA,IAAAA,EA2jEmrJ,U;AA3jEnrJA,IAAAA,EA2jE8rJ,e;AA3jE9rJA,IAAAA,EA2jE2sJ,e;;;;;AA3jE3sJA,IAAAA,EA2jEmrJ,a;AA3jEnrJA,IAAAA,EA2jEmrJ,6B;;;;;;AA3jEnrJA,IAAAA,EA2jEk/I,4B;AA3jEl/IA,IAAAA,EA2jEilJ,kE;AA3jEjlJA,IAAAA,EA2jEitJ,e;;;;mBA3jEjtJA,E;AAAAA,IAAAA,EA2jEuhJ,oC;AA3jEvhJA,IAAAA,EA2jEgnJ,a;AA3jEhnJA,IAAAA,EA2jEgnJ,qC;;;;;;AA/kEhtJ,MAAM2C,MAAM,GAAGC,CAAC,IAAIA,CAAC,CAACC,OAAF,CAAU,wBAAV,EAAoC,MAApC,CAApB;;AACA,MAAMC,aAAN,CAAoB;AAChB;AACJ;AACA;AACA;AACA;AACIC,EAAAA,SAAS,CAACC,KAAD,EAAQC,GAAR,EAAa;AAClB,QAAI,CAACA,GAAG,CAACC,IAAJ,EAAL,EAAiB;AACb,aAAOF,KAAP;AACH;;AACD,QAAI;AACA,YAAMG,KAAK,GAAG,IAAIC,MAAJ,CAAY,IAAGT,MAAM,CAACM,GAAD,CAAM,GAA3B,EAA+B,GAA/B,CAAd;AACA,aAAOD,KAAK,CAACH,OAAN,CAAcM,KAAd,EAAqB,WAArB,CAAP;AACH,KAHD,CAIA,OAAOE,CAAP,EAAU;AACN,aAAOL,KAAP;AACH;AACJ;;AAjBe;;AAmBpBF,aAAa,CAACQ,IAAd;AAAA,mBAA0GR,aAA1G;AAAA;;AACAA,aAAa,CAACS,KAAd,kBADgGvD,EAChG;AAAA;AAAA,QAAwG8C,aAAxG;AAAA;AAAA;;AACA;AAAA,qDAFgG9C,EAEhG,mBAA2F8C,aAA3F,EAAsH,CAAC;AAC3GU,IAAAA,IAAI,EAAEvD,IADqG;AAE3GwD,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,IAAI,EAAE;AADP,KAAD;AAFqG,GAAD,CAAtH;AAAA;AAOA;AACA;AACA;;;AACA,MAAMC,WAAW,GAAG,OAApB;AACA,MAAMC,qBAAqB,GAAG,iBAA9B;AACA,MAAMC,OAAO,GAAG,SAAhB;AACA,MAAMC,KAAK,GAAG,OAAd;AACA,MAAMC,KAAK,GAAG,OAAd;AACA,MAAMC,iBAAiB,GAAG,wEAA1B;AACA,MAAMC,YAAY,GAAG;AACjBC,EAAAA,MAAM,EAAE,QADS;AAEjBC,EAAAA,WAAW,EAAE,aAFI;AAGjBC,EAAAA,WAAW,EAAE,aAHI;AAIjBC,EAAAA,GAAG,EAAE;AAJY,CAArB;AAMA,MAAMC,iBAAiB,GAAG;AACtB,KAAGL,YAAY,CAACC,MADM;AAEtB,MAAID,YAAY,CAACC,MAFK;AAGtB,MAAID,YAAY,CAACE,WAHK;AAItB,MAAIF,YAAY,CAACG,WAJK;AAKtB,KAAGH,YAAY,CAACI;AALM,CAA1B;AAOA,MAAME,iBAAiB,GAAG,MAA1B;AACA,MAAMC,IAAI,GAAG,MAAb;AACA,MAAMC,IAAI,GAAG,MAAb;;AAEA,MAAMC,YAAN,CAAmB;AACfC,EAAAA,WAAW,GAAG;AACV,SAAKtC,KAAL,GAAa;AACTuC,MAAAA,QAAQ,EAAE,KADD;AAETC,MAAAA,QAAQ,EAAE,KAFD;AAGTC,MAAAA,KAAK,EAAEC;AAHE,KAAb;AAKH;AACD;AACJ;AACA;AACA;AACA;;;AACIC,EAAAA,cAAc,CAACC,KAAD,EAAQC,GAAR,EAAa;AACvB,QAAID,KAAK,IAAIA,KAAK,CAACE,YAAnB,EAAiC;AAC7BF,MAAAA,KAAK,CAACE,YAAN,CAAmBC,OAAnB,CAA2Bb,iBAA3B,EAA8Cc,IAAI,CAACC,SAAL,CAAeJ,GAAf,CAA9C;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIK,EAAAA,cAAc,CAACN,KAAD,EAAQ;AAClB,QAAIA,KAAK,IAAIA,KAAK,CAACE,YAAnB,EAAiC;AAC7B,YAAMK,IAAI,GAAGP,KAAK,CAACE,YAAN,CAAmBM,OAAnB,CAA2BlB,iBAA3B,CAAb;;AACA,UAAI;AACA,eAAOc,IAAI,CAACK,KAAL,CAAWF,IAAX,CAAP;AACH,OAFD,CAGA,MAAM;AACF;AACH;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACIG,EAAAA,SAAS,CAACC,MAAD,EAAS;AACd,SAAKA,MAAL,GAAcA,MAAd;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,WAAW,CAACC,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,YAAY,CAACb,GAAD,EAAMc,YAAN,EAAoBC,YAApB,EAAkC;AAC1C,SAAKC,SAAL;AACA,SAAKN,MAAL,CAAYO,iBAAZ,CAA8BjB,GAA9B,EAAmCc,YAAnC;AACA,SAAKF,QAAL,CAAcM,iBAAd,CAAgC,KAAhC,EAAuClB,GAAvC,EAA4Ce,YAA5C;AACH;AACD;AACJ;AACA;AACA;;;AACII,EAAAA,QAAQ,CAAChE,KAAD,EAAQ;AACZ,SAAKA,KAAL,GAAa,EAAE,GAAG,KAAKA,KAAV;AAAiB,SAAGA;AAApB,KAAb;AACH;AACD;AACJ;AACA;AACA;;;AACIiE,EAAAA,QAAQ,CAACC,GAAD,EAAM;AACV,WAAOA,GAAG,GAAG,KAAKlE,KAAL,CAAWkE,GAAX,CAAH,GAAqB,KAAKlE,KAApC;AACH;AACD;AACJ;AACA;;;AACI6D,EAAAA,SAAS,GAAG;AACR,SAAKG,QAAL,CAAc;AACVzB,MAAAA,QAAQ,EAAE,KADA;AAEVC,MAAAA,QAAQ,EAAE,KAFA;AAGVC,MAAAA,KAAK,EAAEC;AAHG,KAAd;AAKH;;AAjFc;;AAmFnBL,YAAY,CAACpB,IAAb;AAAA,mBAAyGoB,YAAzG;AAAA;;AACAA,YAAY,CAAC8B,KAAb,kBAvHgGxG,EAuHhG;AAAA,SAA6G0E,YAA7G;AAAA,WAA6GA,YAA7G;AAAA;;AACA;AAAA,qDAxHgG1E,EAwHhG,mBAA2F0E,YAA3F,EAAqH,CAAC;AAC1GlB,IAAAA,IAAI,EAAEtD;AADoG,GAAD,CAArH;AAAA;;AAIA,MAAMuG,QAAQ,GAAG;AACbC,EAAAA,QAAQ,EAAE;AACNC,IAAAA,aAAa,EAAE,EADT;AAENC,IAAAA,iBAAiB,EAAE,EAFb;AAGNC,IAAAA,QAAQ,EAAEC,QAHJ;AAINC,IAAAA,WAAW,EAAEpD,WAJP;AAKNqD,IAAAA,oBAAoB,EAAEpD,qBALhB;AAMNqD,IAAAA,UAAU,EAAE,EANN;AAONC,IAAAA,eAAe,EAAE,EAPX;AAQNC,IAAAA,oBAAoB,EAAE,KARhB;AASNC,IAAAA,aAAa,EAAE,EATT;AAUNC,IAAAA,KAAK,EAAE,EAVD;AAWNC,IAAAA,oBAAoB,EAAE,GAXhB;AAYNC,IAAAA,OAAO,EAAE,IAZH;AAaNC,IAAAA,UAAU,EAAE,EAbN;AAcNC,IAAAA,WAAW,EAAE,KAdP;AAeNC,IAAAA,QAAQ,EAAE,KAfJ;AAgBNC,IAAAA,SAAS,EAAE,KAhBL;AAiBNC,IAAAA,UAAU,EAAE,KAjBN;AAkBNC,IAAAA,iBAAiB,EAAE,GAlBb;AAmBNC,IAAAA,WAAW,EAAE,IAnBP;AAoBNC,IAAAA,SAAS,EAAE,IApBL;AAqBNC,IAAAA,QAAQ,EAAE,KArBJ;AAsBNC,IAAAA,UAAU,EAAE,KAtBN;AAuBNC,IAAAA,cAAc,EAAE,KAvBV;AAwBNC,IAAAA,QAAQ,EAAE,IAxBJ;AAyBNC,IAAAA,MAAM,EAAE,IAzBF;AA0BNC,IAAAA,QAAQ,EAAE,EA1BJ;AA2BNC,IAAAA,OAAO,EAAE,KA3BH;AA4BNC,IAAAA,QAAQ,EAAE,EA5BJ;AA6BNC,IAAAA,UAAU,EAAEzD,SA7BN;AA8BN0D,IAAAA,QAAQ,EAAE1D,SA9BJ;AA+BN2D,IAAAA,SAAS,EAAE,SA/BL;AAgCNC,IAAAA,UAAU,EAAE,OAhCN;AAiCNC,IAAAA,iBAAiB,EAAE;AACfC,MAAAA,KAAK,EAAE,OADQ;AAEfC,MAAAA,KAAK,EAAE;AAFQ;AAjCb,GADG;AAuCbC,EAAAA,QAAQ,EAAE;AACNL,IAAAA,SAAS,EAAE,SADL;AAENC,IAAAA,UAAU,EAAE,OAFN;AAGNK,IAAAA,YAAY,EAAE,IAHR;AAINC,IAAAA,MAAM,EAAE,MAJF;AAKNC,IAAAA,iBAAiB,EAAE,KALb;AAMNC,IAAAA,mBAAmB,EAAE,KANf;AAONC,IAAAA,iBAAiB,EAAE,CAPb;AAQNC,IAAAA,YAAY,EAAEvC,QARR;AASNwC,IAAAA,QAAQ,EAAE,IATJ;AAUNC,IAAAA,aAAa,EAAE,IAVT;AAWNC,IAAAA,MAAM,EAAE,IAXF;AAYNC,IAAAA;AAZM;AAvCG,CAAjB;AAsDA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASA,UAAT,CAAoBzG,KAApB,EAA2B0G,MAA3B,EAAmC;AAC/B,QAAMC,WAAW,GAAGD,MAAM,CAAC,KAAKhB,SAAN,CAAN,CAAuBkB,QAAvB,EAApB;AACA,SAAOD,WAAW,IAAIA,WAAW,CAC5BE,WADiB,GAEjBC,OAFiB,CAET9G,KAAK,CAAC6G,WAAN,EAFS,KAEe,CAFrC;AAGH;;AAED,MAAME,eAAN,CAAsB;AAClBC,EAAAA,UAAU,CAACC,OAAD,EAAU;AAChBF,IAAAA,eAAe,CAACtD,QAAhB,CAAyBC,QAAzB,GAAoC,EAAE,GAAGD,QAAQ,CAACC,QAAd;AAAwB,SAAGuD,OAAO,CAACvD;AAAnC,KAApC;AACAqD,IAAAA,eAAe,CAACtD,QAAhB,CAAyBsC,QAAzB,GAAoC,EAAE,GAAGtC,QAAQ,CAACsC,QAAd;AAAwB,SAAGkB,OAAO,CAAClB;AAAnC,KAApC;AACH;;AAJiB;;AAMtBgB,eAAe,CAACtD,QAAhB,GAA2BA,QAA3B;;AAEA,SAASyD,QAAT,CAAkBC,GAAlB,EAAuB;AACnB,SAAOA,GAAG,KAAKC,MAAM,CAACD,GAAD,CAArB;AACH;;AACD,MAAME,gBAAN,CAAuB;AACnB1F,EAAAA,WAAW,GAAG;AACV,SAAK2F,MAAL,GAAc,EAAd;AACA;AACR;AACA;;AACQ,SAAK5B,SAAL,GAAiBqB,eAAe,CAACtD,QAAhB,CAAyBC,QAAzB,CAAkCgC,SAAnD;AACA;AACR;AACA;;AACQ,SAAKC,UAAL,GAAkBoB,eAAe,CAACtD,QAAhB,CAAyBC,QAAzB,CAAkCiC,UAApD;AACH;;AACQ,MAAL4B,KAAK,GAAG;AACR,WAAO,KAAKD,MAAZ;AACH;;AACQ,MAALC,KAAK,CAACA,KAAD,EAAQ;AACb,SAAKD,MAAL,GAAcC,KAAd;;AACA,SAAKC,iBAAL,CAAuB,KAAKF,MAA5B;AACH;;AACDG,EAAAA,SAAS,GAAG;AACR,SAAKC,kBAAL;AACH;;AACDC,EAAAA,UAAU,CAACJ,KAAD,EAAQ;AACd,SAAKD,MAAL,GAAcC,KAAK,IAAI,EAAvB;AACH;;AACDK,EAAAA,gBAAgB,CAACC,EAAD,EAAK;AACjB,SAAKL,iBAAL,GAAyBK,EAAzB;AACH;;AACDC,EAAAA,iBAAiB,CAACD,EAAD,EAAK;AAClB,SAAKH,kBAAL,GAA0BG,EAA1B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIE,EAAAA,YAAY,CAACC,IAAD,EAAOC,YAAY,GAAG,KAAtB,EAA6B;AACrC,UAAMC,QAAQ,GAAGD,YAAY,IAAI,KAAKlC,QAArB,GAAgC,KAAKA,QAAL,CAAcJ,UAA9C,GAA2D,KAAKA,UAAjF;AACA,WAAOuB,QAAQ,CAACc,IAAD,CAAR,GAAiBA,IAAI,CAACE,QAAD,CAArB,GAAkCF,IAAzC;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIG,EAAAA,cAAc,CAACH,IAAD,EAAOC,YAAY,GAAG,KAAtB,EAA6B;AACvC,UAAMC,QAAQ,GAAGD,YAAY,IAAI,KAAKlC,QAArB,GAAgC,KAAKA,QAAL,CAAcL,SAA9C,GAA0D,KAAKA,SAAhF;AACA,WAAOwB,QAAQ,CAACc,IAAD,CAAR,GAAiBA,IAAI,CAACE,QAAD,CAArB,GAAkCF,IAAzC;AACH;AACD;AACJ;AACA;AACA;;;AACII,EAAAA,eAAe,CAACtG,KAAD,EAAQ;AACnB,WAAO,KAAKyF,KAAL,CAAW1I,MAAX,CAAkB,CAACmJ,IAAD,EAAOK,QAAP,KAAoBA,QAAQ,KAAKvG,KAAnD,CAAP;AACH;;AAvDkB;;AAyDvBuF,gBAAgB,CAAC/G,IAAjB;AAAA,mBAA6G+G,gBAA7G;AAAA;;AACAA,gBAAgB,CAACiB,IAAjB,kBApQgGtL,EAoQhG;AAAA,QAAiGqK,gBAAjG;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDArQgGrK,EAqQhG,mBAA2FqK,gBAA3F,EAAyH,CAAC;AAC9G7G,IAAAA,IAAI,EAAErD;AADwG,GAAD,CAAzH,QAE4B;AAAEuI,IAAAA,SAAS,EAAE,CAAC;AAC1BlF,MAAAA,IAAI,EAAEpD;AADoB,KAAD,CAAb;AAEZuI,IAAAA,UAAU,EAAE,CAAC;AACbnF,MAAAA,IAAI,EAAEpD;AADO,KAAD;AAFA,GAF5B;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASmL,MAAT,CAAgBC,YAAhB,EAA8BC,MAA9B,EAAsCC,SAAS,GAAG,IAAlD,EAAwD;AACpD;AACA,MAAI,CAAC,KAAKC,SAAL,CAAeC,cAAf,CAA8BJ,YAA9B,CAAL,EAAkD;AAC9C,UAAM,IAAIK,KAAJ,CAAU,gCAAV,CAAN;AACH,GAJmD,CAKpD;;;AACA,MAAI,CAACH,SAAL,EAAgB;AACZ;AACH,GARmD,CASpD;;;AACA,OAAKC,SAAL,CAAeH,YAAf,EAA6BM,IAA7B,CAAkCL,MAAlC;AACH;;AAED,MAAMM,YAAN,CAAmB;AACfpH,EAAAA,WAAW,GAAG;AACV;AACR;AACA;AACQ,SAAKqH,QAAL,GAAgB,IAAI3L,YAAJ,EAAhB;AACA;AACR;AACA;;AACQ,SAAK4L,MAAL,GAAc,IAAI5L,YAAJ,EAAd;AACA;AACR;AACA;;AACQ,SAAK6L,OAAL,GAAe,IAAI7L,YAAJ,EAAf;AACA;AACR;AACA;;AACQ,SAAK8L,OAAL,GAAe,IAAI9L,YAAJ,EAAf;AACA;AACR;AACA;;AACQ,SAAK+L,SAAL,GAAiB,IAAI/L,YAAJ,EAAjB;AACA;AACR;AACA;;AACQ,SAAKgM,eAAL,GAAuB,IAAIhM,YAAJ,EAAvB;AACA;AACR;AACA;;AACQ,SAAK4G,UAAL,GAAkB,EAAlB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,eAAL,GAAuB,EAAvB;AACA;AACR;AACA;AACA;;AACQ,SAAKoF,QAAL,GAAgB,EAAhB;AACA;AACR;AACA;;AACQ,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKvB,IAAL,GAAY,IAAI9J,WAAJ,CAAgB;AAAE8B,MAAAA,KAAK,EAAE,EAAT;AAAauJ,MAAAA,QAAQ,EAAE,KAAKA;AAA5B,KAAhB,CAAZ;AACH;AACD;AACJ;AACA;;;AACiB,MAATC,SAAS,GAAG;AACZ,WAAO,KAAKxB,IAAL,CAAUhI,KAAjB;AACH;AACD;AACJ;AACA;AACA;;;AACiB,MAATwJ,SAAS,CAACC,IAAD,EAAO;AAChB,SAAKzB,IAAL,CAAU0B,QAAV,CAAmBD,IAAnB;AACA,SAAKJ,eAAL,CAAqBM,IAArB,CAA0BF,IAA1B;AACH;;AACDG,EAAAA,QAAQ,GAAG;AACP,SAAK5B,IAAL,CAAU6B,aAAV,CAAwB,KAAK5F,UAA7B;AACA,SAAK+D,IAAL,CAAU8B,kBAAV,CAA6B,KAAK5F,eAAlC,EAFO,CAGP;;AACA,SAAK6F,IAAL,GAAY,IAAI5L,SAAJ,CAAc;AACtB6J,MAAAA,IAAI,EAAE,KAAKA;AADW,KAAd,CAAZ;AAGH;;AACDgC,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,QAAIA,OAAO,CAACV,QAAR,IAAoB,CAACU,OAAO,CAACV,QAAR,CAAiBW,WAA1C,EAAuD;AACnD,UAAID,OAAO,CAACV,QAAR,CAAiBY,YAArB,EAAmC;AAC/B,aAAKJ,IAAL,CAAUK,QAAV,CAAmB,MAAnB,EAA2B9E,OAA3B;AACH,OAFD,MAGK;AACD,aAAKyE,IAAL,CAAUK,QAAV,CAAmB,MAAnB,EAA2BC,MAA3B;AACH;AACJ;AACJ;AACD;AACJ;AACA;;;AACa,MAALrK,KAAK,GAAG;AACR,WAAO,KAAK+J,IAAL,CAAUO,GAAV,CAAc,MAAd,CAAP;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,cAAc,GAAG;AACb,UAAMC,GAAG,GAAG,OAAOC,QAAP,KAAoB,WAApB,GAAkCA,QAAlC,GAA6C1I,SAAzD;AACA,WAAOyI,GAAG,GAAGA,GAAG,CAACE,aAAJ,KAAsB,KAAKC,KAAL,CAAWC,aAApC,GAAoD,KAA9D;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,gBAAgB,CAACC,QAAD,EAAW;AACvB,WAAO1D,MAAM,CAAC2D,IAAP,CAAYD,QAAZ,EACFjM,MADE,CACKmM,GAAG,IAAI,KAAKhL,KAAL,CAAWiL,QAAX,CAAoBD,GAApB,CADZ,EAEFjM,GAFE,CAEEiM,GAAG,IAAIF,QAAQ,CAACE,GAAD,CAFjB,CAAP;AAGH;AACD;AACJ;AACA;;;AACIE,EAAAA,SAAS,GAAG;AACR,UAAM;AAAEC,MAAAA,KAAF;AAASnL,MAAAA,KAAT;AAAgBoL,MAAAA;AAAhB,QAA0B,KAAKrB,IAArC;AACA,WAAOoB,KAAK,IAAInL,KAAK,CAACgI,IAAf,IAAuB,CAACoD,KAA/B;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,KAAK,GAAG;AACJ,SAAKV,KAAL,CAAWC,aAAX,CAAyBS,KAAzB;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,IAAI,GAAG;AACH,SAAKX,KAAL,CAAWC,aAAX,CAAyBU,IAAzB;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,kBAAkB,GAAG;AACjB,WAAO,KAAKZ,KAAL,CAAWC,aAAX,CAAyBY,qBAAzB,EAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,OAAO,GAAG;AACN,UAAMd,KAAK,GAAG,KAAKA,KAAL,CAAWC,aAAzB;AACAD,IAAAA,KAAK,CAACe,aAAN,CAAoBC,WAApB,CAAgChB,KAAhC;AACH;AACD;AACJ;AACA;AACA;;;AACIiB,EAAAA,SAAS,CAACC,MAAD,EAAS;AACd,SAAKrC,SAAL,GAAiB,KAAKxJ,KAAL,CAAWA,KAA5B;;AACA,QAAI6L,MAAM,CAACtI,GAAP,KAAe,OAAnB,EAA4B;AACxB,WAAKuI,MAAL,CAAYD,MAAZ;AACH,KAFD,MAGK;AACD,aAAO,KAAKzC,SAAL,CAAeO,IAAf,CAAoBkC,MAApB,CAAP;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIE,EAAAA,OAAO,CAACF,MAAD,EAAS;AACZ,SAAKrC,SAAL,GAAiB,KAAKxJ,KAAL,CAAWA,KAA5B;AACA,WAAO,KAAKmJ,OAAL,CAAaQ,IAAb,CAAkBkC,MAAlB,CAAP;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,MAAM,CAACD,MAAD,EAAS;AACXA,IAAAA,MAAM,CAACG,cAAP;AACA,SAAKhD,QAAL,CAAcW,IAAd,CAAmBkC,MAAnB;AACH;;AAhKc;;AAkKnB9C,YAAY,CAACzI,IAAb;AAAA,mBAAyGyI,YAAzG;AAAA;;AACAA,YAAY,CAACkD,IAAb,kBAncgGjP,EAmchG;AAAA,QAA6F+L,YAA7F;AAAA;AAAA;AAAA;AAncgG/L,MAAAA,EAmchG;AAAA;;AAAA;AAAA;;AAncgGA,MAAAA,EAmchG,qBAncgGA,EAmchG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAncgGA,EAmchG;AAAA;AAAA;AAAA;AAAA;AAAA;AAncgGA,MAAAA,EAmcqjB,6BAArpB;AAncgGA,MAAAA,EAmc2jB;AAAA,eAAa,kBAAb;AAAA,QAA3pB;AAncgGA,MAAAA,EAmconB,iCAAptB;AAncgGA,MAAAA,EAmc+oC;AAAA,eAAU,wBAAV;AAAA;AAAA,eAAsD,uBAAtD;AAAA;AAAA,eAAoG,qBAApG;AAAA;AAAA,eAA8I,mBAA9I;AAAA,QAA/uC;AAncgGA,MAAAA,EAmconB,eAAptB;AAncgGA,MAAAA,EAmcwzC,eAAx5C;AAAA;;AAAA;AAncgGA,MAAAA,EAmcylB,kCAAzrB;AAncgGA,MAAAA,EAmc2vB,aAA31B;AAncgGA,MAAAA,EAmc2vB,uFAA31B;AAncgGA,MAAAA,EAmcq4B,sCAAr+B;AAncgGA,MAAAA,EAmc06B,wKAA1gC;AAAA;AAAA;AAAA,eAAo2KiB,EAAE,CAACiO,aAAv2K,EAA07KjO,EAAE,CAACkO,oBAA77K,EAAmkLlO,EAAE,CAACmO,kBAAtkL,EAAisLnO,EAAE,CAACoO,oBAApsL,EAA87LpO,EAAE,CAACqO,kBAAj8L,EAA8kMrO,EAAE,CAACsO,eAAjlM,EAAmqMtO,EAAE,CAACuO,eAAtqM,EAAgzMhO,EAAE,CAACiO,OAAnzM;AAAA;AAAA;;AACA;AAAA,qDApcgGzP,EAochG,mBAA2F+L,YAA3F,EAAqH,CAAC;AAC1GvI,IAAAA,IAAI,EAAElD,SADoG;AAE1GmD,IAAAA,IAAI,EAAE,CAAC;AAAEiM,MAAAA,QAAQ,EAAE,gBAAZ;AAA8BC,MAAAA,QAAQ,EAAE,6xBAAxC;AAAu0BC,MAAAA,MAAM,EAAE,CAAC,+5HAAD;AAA/0B,KAAD;AAFoG,GAAD,CAArH,QAG4B;AAAE5D,IAAAA,QAAQ,EAAE,CAAC;AACzBxI,MAAAA,IAAI,EAAEjD;AADmB,KAAD,CAAZ;AAEZ0L,IAAAA,MAAM,EAAE,CAAC;AACTzI,MAAAA,IAAI,EAAEjD;AADG,KAAD,CAFI;AAIZ2L,IAAAA,OAAO,EAAE,CAAC;AACV1I,MAAAA,IAAI,EAAEjD;AADI,KAAD,CAJG;AAMZ4L,IAAAA,OAAO,EAAE,CAAC;AACV3I,MAAAA,IAAI,EAAEjD;AADI,KAAD,CANG;AAQZ6L,IAAAA,SAAS,EAAE,CAAC;AACZ5I,MAAAA,IAAI,EAAEjD;AADM,KAAD,CARC;AAUZ8L,IAAAA,eAAe,EAAE,CAAC;AAClB7I,MAAAA,IAAI,EAAEjD;AADY,KAAD,CAVL;AAYZwG,IAAAA,WAAW,EAAE,CAAC;AACdvD,MAAAA,IAAI,EAAEpD;AADQ,KAAD,CAZD;AAcZ6G,IAAAA,UAAU,EAAE,CAAC;AACbzD,MAAAA,IAAI,EAAEpD;AADO,KAAD,CAdA;AAgBZ8G,IAAAA,eAAe,EAAE,CAAC;AAClB1D,MAAAA,IAAI,EAAEpD;AADY,KAAD,CAhBL;AAkBZmH,IAAAA,OAAO,EAAE,CAAC;AACV/D,MAAAA,IAAI,EAAEpD;AADI,KAAD,CAlBG;AAoBZoH,IAAAA,UAAU,EAAE,CAAC;AACbhE,MAAAA,IAAI,EAAEpD;AADO,KAAD,CApBA;AAsBZkM,IAAAA,QAAQ,EAAE,CAAC;AACX9I,MAAAA,IAAI,EAAEpD;AADK,KAAD,CAtBE;AAwBZmM,IAAAA,QAAQ,EAAE,CAAC;AACX/I,MAAAA,IAAI,EAAEpD;AADK,KAAD,CAxBE;AA0BZuN,IAAAA,KAAK,EAAE,CAAC;AACRnK,MAAAA,IAAI,EAAEhD,SADE;AAERiD,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFE,KAAD,CA1BK;AA6BZ+I,IAAAA,SAAS,EAAE,CAAC;AACZhJ,MAAAA,IAAI,EAAEpD;AADM,KAAD;AA7BC,GAH5B;AAAA;;AAoCA,MAAMyP,SAAN,CAAgB;AACZlL,EAAAA,WAAW,GAAG;AACV,SAAKtC,KAAL,GAAa,MAAb;AACH;;AAHW;;AAKhBwN,SAAS,CAACvM,IAAV;AAAA,mBAAsGuM,SAAtG;AAAA;;AACAA,SAAS,CAACZ,IAAV,kBA9egGjP,EA8ehG;AAAA,QAA0F6P,SAA1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA9egG7P,MAAAA,EA+exF,uBADR;AAAA;;AAAA;AA9egGA,MAAAA,EA+ehE,8BADhC;AAAA;AAAA;AAAA;AAAA;AAAA,eAE6O,CACrOoC,OAAO,CAAC,KAAD,EAAQ,CACXC,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;AAAEwN,MAAAA,KAAK,EAAE,CAAT;AAAYC,MAAAA,OAAO,EAAE;AAArB,KAAD,CAAd,CADM,EAEXxN,UAAU,CAAC,iBAAD,EAAoB,CAC1BC,OAAO,CAAC,GAAD,EAAMC,SAAS,CAAC,CACnBH,KAAK,CAAC;AAAEyN,MAAAA,OAAO,EAAE,CAAX;AAAc9G,MAAAA,MAAM,EAAE,CAAtB;AAAyB6G,MAAAA,KAAK,EAAE,KAAhC;AAAuCE,MAAAA,YAAY,EAAE;AAArD,KAAD,CADc,EAEnB1N,KAAK,CAAC;AAAEyN,MAAAA,OAAO,EAAE,CAAX;AAAc9G,MAAAA,MAAM,EAAE,GAAtB;AAA2B6G,MAAAA,KAAK,EAAE;AAAlC,KAAD,CAFc,EAGnBxN,KAAK,CAAC;AAAEyN,MAAAA,OAAO,EAAE,GAAX;AAAgB9G,MAAAA,MAAM,EAAE,CAAxB;AAA2B6G,MAAAA,KAAK,EAAE,MAAlC;AAA0CE,MAAAA,YAAY,EAAE;AAAxD,KAAD,CAHc,CAAD,CAAf,CADmB,CAApB,CAFC,CAAR,CAD8N;AAF7O;AAAA;;AAcA;AAAA,qDA5fgGhQ,EA4fhG,mBAA2F6P,SAA3F,EAAkH,CAAC;AACvGrM,IAAAA,IAAI,EAAElD,SADiG;AAEvGmD,IAAAA,IAAI,EAAE,CAAC;AACCiM,MAAAA,QAAQ,EAAE,YADX;AAECE,MAAAA,MAAM,EAAE,CAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAjB4B,CAFT;AAoBCD,MAAAA,QAAQ,EAAG;AAC/B;AACA,KAtBmB;AAuBCM,MAAAA,UAAU,EAAE,CACR7N,OAAO,CAAC,KAAD,EAAQ,CACXC,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;AAAEwN,QAAAA,KAAK,EAAE,CAAT;AAAYC,QAAAA,OAAO,EAAE;AAArB,OAAD,CAAd,CADM,EAEXxN,UAAU,CAAC,iBAAD,EAAoB,CAC1BC,OAAO,CAAC,GAAD,EAAMC,SAAS,CAAC,CACnBH,KAAK,CAAC;AAAEyN,QAAAA,OAAO,EAAE,CAAX;AAAc9G,QAAAA,MAAM,EAAE,CAAtB;AAAyB6G,QAAAA,KAAK,EAAE,KAAhC;AAAuCE,QAAAA,YAAY,EAAE;AAArD,OAAD,CADc,EAEnB1N,KAAK,CAAC;AAAEyN,QAAAA,OAAO,EAAE,CAAX;AAAc9G,QAAAA,MAAM,EAAE,GAAtB;AAA2B6G,QAAAA,KAAK,EAAE;AAAlC,OAAD,CAFc,EAGnBxN,KAAK,CAAC;AAAEyN,QAAAA,OAAO,EAAE,GAAX;AAAgB9G,QAAAA,MAAM,EAAE,CAAxB;AAA2B6G,QAAAA,KAAK,EAAE,MAAlC;AAA0CE,QAAAA,YAAY,EAAE;AAAxD,OAAD,CAHc,CAAD,CAAf,CADmB,CAApB,CAFC,CAAR,CADC;AAvBb,KAAD;AAFiG,GAAD,CAAlH,QAsC4B;AAAE3N,IAAAA,KAAK,EAAE,CAAC;AACtBmB,MAAAA,IAAI,EAAEpD;AADgB,KAAD;AAAT,GAtC5B;AAAA;;AA0CA,MAAM8P,mBAAN,CAA0B;;AAE1BA,mBAAmB,CAAC5M,IAApB;AAAA,mBAAgH4M,mBAAhH;AAAA;;AACAA,mBAAmB,CAACjB,IAApB,kBAziBgGjP,EAyiBhG;AAAA,QAAoGkQ,mBAApG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAziBgGlQ,MAAAA,EAyiB2E,0BAA3K;AAziBgGA,MAAAA,EAyiBuF,iBAAvL;AAziBgGA,MAAAA,EAyiBuF,4BAAvL;AAziBgGA,MAAAA,EAyiB4L,wBAA5R;AAziBgGA,MAAAA,EAyiBioB,eAAjuB;AAziBgGA,MAAAA,EAyiByoB,eAAzuB;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDA1iBgGA,EA0iBhG,mBAA2FkQ,mBAA3F,EAA4H,CAAC;AACjH1M,IAAAA,IAAI,EAAElD,SAD2G;AAEjHmD,IAAAA,IAAI,EAAE,CAAC;AAAEiM,MAAAA,QAAQ,EAAE,aAAZ;AAA2BC,MAAAA,QAAQ,EAAE,ukBAArC;AAA8mBC,MAAAA,MAAM,EAAE,CAAC,+tJAAD;AAAtnB,KAAD;AAF2G,GAAD,CAA5H;AAAA,K,CAKA;;;AACA,MAAMO,SAAS,GAAG,OAAOC,MAAP,KAAkB,WAAlB,GAAgCA,MAAM,CAACD,SAAvC,GAAmD;AACjEE,EAAAA,SAAS,EAAE,QADsD;AAEjEC,EAAAA,MAAM,EAAE;AAFyD,CAArE;AAIA,MAAMC,QAAQ,GAAG,SAASC,IAAT,CAAcL,SAAS,CAACE,SAAxB,KAAsC,aAAaG,IAAb,CAAkBL,SAAS,CAACG,MAA5B,CAAvD;;AACA,MAAMG,YAAN,CAAmB;AACf9L,EAAAA,WAAW,CAAC+L,OAAD,EAAUC,QAAV,EAAoBC,KAApB,EAA2B;AAClC,SAAKF,OAAL,GAAeA,OAAf;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA;AACR;AACA;;AACQ,SAAKrE,QAAL,GAAgB,KAAhB;AACA;AACR;AACA;;AACQ,SAAKsE,QAAL,GAAgB,IAAIxQ,YAAJ,EAAhB;AACA;AACR;AACA;;AACQ,SAAKyQ,QAAL,GAAgB,IAAIzQ,YAAJ,EAAhB;AACA;AACR;AACA;;AACQ,SAAK4L,MAAL,GAAc,IAAI5L,YAAJ,EAAd;AACA;AACR;AACA;;AACQ,SAAKuO,SAAL,GAAiB,IAAIvO,YAAJ,EAAjB;AACA;AACR;AACA;;AACQ,SAAK0Q,WAAL,GAAmB,IAAI1Q,YAAJ,EAAnB;AACA;AACR;AACA;;AACQ,SAAK2Q,OAAL,GAAe,KAAf;AACA;AACR;AACA;;AACQ,SAAKC,WAAL,GAAmB,MAAnB;AACH;AACD;AACJ;AACA;;;AACgB,MAARC,QAAQ,GAAG;AACX,WAAO,OAAO,KAAKC,KAAZ,KAAsB,QAAtB,IAAkC,KAAKA,KAAL,CAAWD,QAAX,KAAwB,IAAjE;AACH;AACD;AACJ;AACA;;;AACIE,EAAAA,MAAM,CAACvC,MAAD,EAAS;AACX,QAAI,KAAKqC,QAAL,IAAiB,KAAK3E,QAA1B,EAAoC;AAChC;AACH;;AACD,QAAIsC,MAAJ,EAAY;AACRA,MAAAA,MAAM,CAACwC,eAAP;AACH;;AACD,SAAKhD,KAAL;AACA,SAAKwC,QAAL,CAAclE,IAAd,CAAmB,KAAKwE,KAAxB;AACH;AACD;AACJ;AACA;;;AACIG,EAAAA,MAAM,CAACzC,MAAD,EAAS;AACXA,IAAAA,MAAM,CAACwC,eAAP;AACA,SAAKP,QAAL,CAAcnE,IAAd,CAAmB,IAAnB;AACH;AACD;AACJ;AACA;;;AACI0B,EAAAA,KAAK,GAAG;AACJ,SAAKqC,OAAL,CAAa9C,aAAb,CAA2BS,KAA3B;AACH;;AACDkD,EAAAA,IAAI,GAAG;AACH,SAAKC,MAAL,GAAc,IAAd;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,OAAO,CAACxM,KAAD,EAAQ;AACX,QAAI,KAAK+L,OAAT,EAAkB;AACd,UAAI/L,KAAK,CAACyM,OAAN,KAAkB,EAAtB,EAA0B;AACtB,eAAO,KAAKC,eAAL,CAAqB1M,KAArB,CAAP;AACH;AACJ,KAJD,MAKK;AACD,WAAK2J,SAAL,CAAejC,IAAf,CAAoB;AAAE1H,QAAAA,KAAF;AAASkM,QAAAA,KAAK,EAAE,KAAKA;AAArB,OAApB;AACH;AACJ;AACD;AACJ;AACA;;;AACIS,EAAAA,KAAK,GAAG;AACJ,UAAMC,SAAS,GAAG,KAAKnB,OAAL,CAAa9C,aAAb,CAA2BiE,SAA7C;AACAA,IAAAA,SAAS,CAACC,GAAV,CAAc,OAAd;AACAC,IAAAA,UAAU,CAAC,MAAMF,SAAS,CAACP,MAAV,CAAiB,OAAjB,CAAP,EAAkC,EAAlC,CAAV;AACH;AACD;AACJ;AACA;;;AACIU,EAAAA,cAAc,GAAG;AACb,QAAI,KAAKhK,QAAT,EAAmB;AACf,aAAO,KAAKgJ,OAAL,GAAejM,SAAf,GAA2B,KAAKkN,gBAAL,EAAlC;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,SAAS,CAACjN,KAAD,EAAQ;AACb;AACA;AACA,QAAI,CAAC,KAAK+C,QAAV,EAAoB;AAChB;AACH;;AACD,SAAK2J,eAAL;AACA,UAAM3O,KAAK,GAAGiC,KAAK,CAACyE,MAAN,CAAayI,SAA3B;AACA,UAAMC,MAAM,GAAG,OAAO,KAAKjB,KAAZ,KAAsB,QAAtB,GACTnO,KADS,GAET,EAAE,GAAG,KAAKmO,KAAV;AAAiB,OAAC,KAAKzI,SAAN,GAAkB1F;AAAnC,KAFN;AAGA,SAAKiJ,MAAL,CAAYU,IAAZ,CAAiByF,MAAjB;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,eAAe,CAACrH,IAAD,EAAO;AAClB,WAAO,OAAOA,IAAP,KAAgB,QAAhB,GAA2BA,IAA3B,GAAkCA,IAAI,CAAC,KAAKtC,SAAN,CAA7C;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACuB,MAAf4J,eAAe,GAAG;AAClB,WAAO,CAAC,KAAKpB,QAAN,IAAkB,CAAC,KAAKF,OAAxB,IAAmCT,QAAnC,IAA+C,KAAKgC,SAA3D;AACH;AACD;AACJ;AACA;AACA;;;AACIZ,EAAAA,eAAe,CAAC9C,MAAD,EAAS;AACpB,UAAMgD,SAAS,GAAG,KAAKnB,OAAL,CAAa9C,aAAb,CAA2BiE,SAA7C;AACA,UAAMlE,KAAK,GAAG,KAAK6E,sBAAL,EAAd;AACA,SAAKxB,OAAL,GAAe,KAAf;AACAa,IAAAA,SAAS,CAACP,MAAV,CAAiB,cAAjB;;AACA,QAAI,CAAC3D,KAAL,EAAY;AACR,WAAK8E,sBAAL,CAA4B,KAAKtB,KAAjC;AACA;AACH;;AACD,SAAKuB,aAAL,CAAmB/E,KAAnB;AACA,SAAKiD,KAAL,CAAW+B,aAAX;;AACA,QAAI9D,MAAJ,EAAY;AACRA,MAAAA,MAAM,CAACG,cAAP;AACH;AACJ;AACD;AACJ;AACA;;;AACI4D,EAAAA,mBAAmB,GAAG;AAClB,WAAQ,CAAC,KAAK1B,QAAN,IAAkB,CAAC,KAAK3E,QAAxB,IAAoC,KAAKxE,SAAzC,IAAsD,CAAC,KAAKiJ,OAApE;AACH;AACD;AACJ;AACA;;;AACIwB,EAAAA,sBAAsB,GAAG;AACrB,UAAM7E,KAAK,GAAG,KAAKkF,kBAAL,EAAd;AACA,WAAOlF,KAAK,GAAGA,KAAK,CAACwE,SAAN,CAAgBjP,IAAhB,EAAH,GAA4B,EAAxC;AACH;AACD;AACJ;AACA;AACA;;;AACIuP,EAAAA,sBAAsB,CAACtB,KAAD,EAAQ;AAC1B,UAAMxD,KAAK,GAAG,KAAKkF,kBAAL,EAAd;AACA,UAAM7P,KAAK,GAAG,KAAKqP,eAAL,CAAqBlB,KAArB,CAAd;AACAxD,IAAAA,KAAK,CAACwE,SAAN,GAAkBnP,KAAlB;AACH;AACD;AACJ;AACA;;;AACIiP,EAAAA,gBAAgB,GAAG;AACf,UAAMJ,SAAS,GAAG,KAAKnB,OAAL,CAAa9C,aAAb,CAA2BiE,SAA7C;AACAA,IAAAA,SAAS,CAACC,GAAV,CAAc,cAAd;AACA,SAAKd,OAAL,GAAe,IAAf;AACH;AACD;AACJ;AACA;AACA;;;AACI0B,EAAAA,aAAa,CAAC/E,KAAD,EAAQ;AACjB,UAAMmF,MAAM,GAAI5N,GAAD,IAAS;AACpB,aAAO,OAAOA,GAAP,KAAe,QAAf,GACDA,GAAG,KAAKyI,KADP,GAEDzI,GAAG,CAAC,KAAKwD,SAAN,CAAH,KAAwBiF,KAF9B;AAGH,KAJD;;AAKA,UAAMoF,KAAK,GAAG,MAAM;AAChB,aAAO,KAAK5B,KAAL,CAAW,KAAKxI,UAAhB,MAAgC,KAAKwI,KAAL,CAAW,KAAKzI,SAAhB,CAAvC;AACH,KAFD,CANiB,CASjB;;;AACA,QAAIoK,MAAM,CAAC,KAAK3B,KAAN,CAAV,EAAwB;AACpB;AACH;;AACD,UAAMA,KAAK,GAAG,OAAO,KAAKA,KAAZ,KAAsB,QAAtB,GACRxD,KADQ,GAER;AACE7I,MAAAA,KAAK,EAAE,KAAKA,KADd;AAEE,OAAC,KAAK6D,UAAN,GAAmBoK,KAAK,KAClB,KAAK5B,KAAL,CAAW,KAAKxI,UAAhB,CADkB,GAElBgF,KAJR;AAKE,OAAC,KAAKjF,SAAN,GAAkBiF;AALpB,KAFN;;AASA,QAAI,KAAKqF,SAAL,CAAe7B,KAAf,CAAJ,EAA2B;AACvB,WAAKJ,WAAL,CAAiBpE,IAAjB,CAAsB;AAAEzH,QAAAA,GAAG,EAAEiM,KAAP;AAAcrM,QAAAA,KAAK,EAAE,KAAKA;AAA1B,OAAtB;AACH,KAFD,MAGK;AACD,WAAK2N,sBAAL,CAA4B,KAAKtB,KAAjC;AACH;AACJ;AACD;AACJ;AACA;;;AACI0B,EAAAA,kBAAkB,GAAG;AACjB,WAAO,KAAKnC,OAAL,CAAa9C,aAAb,CAA2BqF,aAA3B,CAAyC,mBAAzC,CAAP;AACH;;AA9Nc;;AAgOnBxC,YAAY,CAACnN,IAAb;AAAA,mBAAyGmN,YAAzG,EArxBgGzQ,EAqxBhG,mBAAuIA,EAAE,CAACkT,UAA1I,GArxBgGlT,EAqxBhG,mBAAiKA,EAAE,CAACmT,SAApK,GArxBgGnT,EAqxBhG,mBAA0LA,EAAE,CAACoT,iBAA7L;AAAA;;AACA3C,YAAY,CAACxB,IAAb,kBAtxBgGjP,EAsxBhG;AAAA,QAA6FyQ,YAA7F;AAAA;AAAA;AAAA;AAtxBgGzQ,MAAAA,EAsxBhG,aAAyoB6P,SAAzoB;AAAA;;AAAA;AAAA;;AAtxBgG7P,MAAAA,EAsxBhG,qBAtxBgGA,EAsxBhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAtxBgGA,MAAAA,EAsxBhG;AAAA,eAA6F,mBAA7F;AAAA;AAAA;;AAAA;AAtxBgGA,MAAAA,EAsxBhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAtxBgGA,MAAAA,EAsxBmmB,4BAAnsB;AAtxBgGA,MAAAA,EAsxBwmB;AAAA,eAAU,kBAAV;AAAA;AAAA,eAA8C,oBAA9C;AAAA;AAAA,iCAAiG,SAAjG;AAAA;AAAA,iCAA2I,MAA3I;AAAA,QAAxsB;AAtxBgGA,MAAAA,EAsxBw5B,2DAAx/B;AAtxBgGA,MAAAA,EAsxBkrC,2DAAlxC;AAtxBgGA,MAAAA,EAsxB48D,eAA5iE;AAtxBgGA,MAAAA,EAsxBs9D,yEAAtjE;AAAA;;AAAA;AAtxBgGA,MAAAA,EAsxBkyB,sCAAl4B;AAtxBgGA,MAAAA,EAsxBkwB,uCAAl2B;AAtxBgGA,MAAAA,EAsxBs0B,0EAAt6B;AAtxBgGA,MAAAA,EAsxB85B,aAA9/B;AAtxBgGA,MAAAA,EAsxB85B,iCAA9/B;AAtxBgGA,MAAAA,EAsxBwrC,aAAxxC;AAtxBgGA,MAAAA,EAsxBwrC,kCAAxxC;AAtxBgGA,MAAAA,EAsxB4iE,aAA5oE;AAtxBgGA,MAAAA,EAsxB4iE,wCAA5oE;AAAA;AAAA;AAAA,eAA40FwB,EAAE,CAAC6R,QAA/0F,EAAi5F7R,EAAE,CAAC8R,YAAp5F,EAA0lG9R,EAAE,CAAC+R,IAA7lG,EAAk+F/R,EAAE,CAACgS,gBAAr+F,EAAssFtD,mBAAtsF,EAA8vFL,SAA9vF;AAAA;AAAA;;AACA;AAAA,qDAvxBgG7P,EAuxBhG,mBAA2FyQ,YAA3F,EAAqH,CAAC;AAC1GjN,IAAAA,IAAI,EAAElD,SADoG;AAE1GmD,IAAAA,IAAI,EAAE,CAAC;AAAEiM,MAAAA,QAAQ,EAAE,KAAZ;AAAmBC,MAAAA,QAAQ,EAAE,q/CAA7B;AAAohDC,MAAAA,MAAM,EAAE,CAAC,4eAAD;AAA5hD,KAAD;AAFoG,GAAD,CAArH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEpM,MAAAA,IAAI,EAAExD,EAAE,CAACkT;AAAX,KAAD,EAA0B;AAAE1P,MAAAA,IAAI,EAAExD,EAAE,CAACmT;AAAX,KAA1B,EAAkD;AAAE3P,MAAAA,IAAI,EAAExD,EAAE,CAACoT;AAAX,KAAlD,CAAP;AAA2F,GAHrI,EAGuJ;AAAEjC,IAAAA,KAAK,EAAE,CAAC;AACjJ3N,MAAAA,IAAI,EAAEpD;AAD2I,KAAD,CAAT;AAEvI2H,IAAAA,SAAS,EAAE,CAAC;AACZvE,MAAAA,IAAI,EAAEpD;AADM,KAAD,CAF4H;AAIvI4H,IAAAA,QAAQ,EAAE,CAAC;AACXxE,MAAAA,IAAI,EAAEpD;AADK,KAAD,CAJ6H;AAMvIuP,IAAAA,QAAQ,EAAE,CAAC;AACXnM,MAAAA,IAAI,EAAEpD;AADK,KAAD,CAN6H;AAQvIsI,IAAAA,SAAS,EAAE,CAAC;AACZlF,MAAAA,IAAI,EAAEpD;AADM,KAAD,CAR4H;AAUvIuI,IAAAA,UAAU,EAAE,CAAC;AACbnF,MAAAA,IAAI,EAAEpD;AADO,KAAD,CAV2H;AAYvI0E,IAAAA,KAAK,EAAE,CAAC;AACRtB,MAAAA,IAAI,EAAEpD;AADE,KAAD,CAZgI;AAcvImS,IAAAA,SAAS,EAAE,CAAC;AACZ/O,MAAAA,IAAI,EAAEpD;AADM,KAAD,CAd4H;AAgBvImM,IAAAA,QAAQ,EAAE,CAAC;AACX/I,MAAAA,IAAI,EAAEpD;AADK,KAAD,CAhB6H;AAkBvI4S,IAAAA,SAAS,EAAE,CAAC;AACZxP,MAAAA,IAAI,EAAEpD;AADM,KAAD,CAlB4H;AAoBvIyQ,IAAAA,QAAQ,EAAE,CAAC;AACXrN,MAAAA,IAAI,EAAEjD;AADK,KAAD,CApB6H;AAsBvIuQ,IAAAA,QAAQ,EAAE,CAAC;AACXtN,MAAAA,IAAI,EAAEjD;AADK,KAAD,CAtB6H;AAwBvI0L,IAAAA,MAAM,EAAE,CAAC;AACTzI,MAAAA,IAAI,EAAEjD;AADG,KAAD,CAxB+H;AA0BvIqO,IAAAA,SAAS,EAAE,CAAC;AACZpL,MAAAA,IAAI,EAAEjD;AADM,KAAD,CA1B4H;AA4BvIwQ,IAAAA,WAAW,EAAE,CAAC;AACdvN,MAAAA,IAAI,EAAEjD;AADQ,KAAD,CA5B0H;AA8BvIiR,IAAAA,MAAM,EAAE,CAAC;AACThO,MAAAA,IAAI,EAAE/C,WADG;AAETgD,MAAAA,IAAI,EAAE,CAAC,cAAD;AAFG,KAAD,CA9B+H;AAiCvI2E,IAAAA,MAAM,EAAE,CAAC;AACT5E,MAAAA,IAAI,EAAEhD,SADG;AAETiD,MAAAA,IAAI,EAAE,CAACoM,SAAD;AAFG,KAAD,CAjC+H;AAoCvI4B,IAAAA,OAAO,EAAE,CAAC;AACVjO,MAAAA,IAAI,EAAE9C,YADI;AAEV+C,MAAAA,IAAI,EAAE,CAAC,SAAD,EAAY,CAAC,QAAD,CAAZ;AAFI,KAAD;AApC8H,GAHvJ;AAAA;AA4CA;AACA;AACA;;;AACA,MAAMwM,UAAU,GAAG,CACf7N,OAAO,CAAC,WAAD,EAAc,CACjBC,KAAK,CAAC,IAAD,EAAOC,KAAK,CAAC;AACdyN,EAAAA,OAAO,EAAE;AADK,CAAD,CAAZ,CADY,EAIjB1N,KAAK,CAAC,KAAD,EAAQC,KAAK,CAAC;AACfyN,EAAAA,OAAO,EAAE;AADM,CAAD,CAAb,CAJY,EAOjBxN,UAAU,CAAC,QAAD,EAAW,CACjBC,OAAO,CAAC,aAAD,EAAgBC,SAAS,CAAC,CAC7BH,KAAK,CAAC;AAAEyN,EAAAA,OAAO,EAAE,CAAX;AAAc9G,EAAAA,MAAM,EAAE,CAAtB;AAAyBlG,EAAAA,SAAS,EAAE;AAApC,CAAD,CADwB,EAE7BT,KAAK,CAAC;AAAEyN,EAAAA,OAAO,EAAE,GAAX;AAAgB9G,EAAAA,MAAM,EAAE,GAAxB;AAA6BlG,EAAAA,SAAS,EAAE;AAAxC,CAAD,CAFwB,EAG7BT,KAAK,CAAC;AAAEyN,EAAAA,OAAO,EAAE,GAAX;AAAgB9G,EAAAA,MAAM,EAAE,GAAxB;AAA6BlG,EAAAA,SAAS,EAAE;AAAxC,CAAD,CAHwB,EAI7BT,KAAK,CAAC;AAAEyN,EAAAA,OAAO,EAAE,IAAX;AAAiB9G,EAAAA,MAAM,EAAE,IAAzB;AAA+BlG,EAAAA,SAAS,EAAE;AAA1C,CAAD,CAJwB,EAK7BT,KAAK,CAAC;AAAEyN,EAAAA,OAAO,EAAE,CAAX;AAAc9G,EAAAA,MAAM,EAAE,CAAtB;AAAyBlG,EAAAA,SAAS,EAAE;AAApC,CAAD,CALwB,CAAD,CAAzB,CADU,CAAX,CAPO,EAgBjBR,UAAU,CAAC,QAAD,EAAW,CACjBC,OAAO,CAAC,aAAD,EAAgBC,SAAS,CAAC,CAC7BH,KAAK,CAAC;AAAEyN,EAAAA,OAAO,EAAE,CAAX;AAAchN,EAAAA,SAAS,EAAE,eAAzB;AAA0CkG,EAAAA,MAAM,EAAE;AAAlD,CAAD,CADwB,EAE7B3G,KAAK,CAAC;AAAEyN,EAAAA,OAAO,EAAE,CAAX;AAAchN,EAAAA,SAAS,EAAE,mBAAzB;AAA8CkG,EAAAA,MAAM,EAAE;AAAtD,CAAD,CAFwB,EAG7B3G,KAAK,CAAC;AAAEyN,EAAAA,OAAO,EAAE,CAAX;AAAchN,EAAAA,SAAS,EAAE,kBAAzB;AAA6CkG,EAAAA,MAAM,EAAE;AAArD,CAAD,CAHwB,CAAD,CAAzB,CADU,CAAX,CAhBO,CAAd,CADQ,CAAnB;;AA2BA,MAAMwK,gBAAN,CAAuB;AACnB9O,EAAAA,WAAW,CAAC+O,QAAD,EAAW;AAAA;;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACA;AACR;AACA;;AACQ,SAAKzK,MAAL,GAAcxC,QAAQ,CAACsC,QAAT,CAAkBE,MAAhC;AACA;AACR;AACA;;AACQ,SAAKC,iBAAL,GAAyBzC,QAAQ,CAACsC,QAAT,CAAkBG,iBAA3C;AACA;AACR;AACA;AACA;;AACQ,SAAKC,mBAAL,GAA2B1C,QAAQ,CAACsC,QAAT,CAAkBI,mBAA7C;AACA;AACR;AACA;AACA;;AACQ,SAAKC,iBAAL,GAAyB3C,QAAQ,CAACsC,QAAT,CAAkBK,iBAA3C;AACA;AACR;AACA;AACA;;AACQ,SAAKC,YAAL,GAAoB5C,QAAQ,CAACsC,QAAT,CAAkBM,YAAtC;AACA;AACR;AACA;;AACQ,SAAKX,SAAL,GAAiBjC,QAAQ,CAACsC,QAAT,CAAkBL,SAAnC;AACA;AACR;AACA;;AACQ,SAAKC,UAAL,GAAkBlC,QAAQ,CAACsC,QAAT,CAAkBJ,UAApC;AACA;AACR;AACA;AACA;;AACQ,SAAKc,UAAL,GAAkBhD,QAAQ,CAACsC,QAAT,CAAkBU,UAApC;AACA;AACR;AACA;;AACQ,SAAKT,YAAL,GAAoBvC,QAAQ,CAACsC,QAAT,CAAkBC,YAAtC;AACA;AACR;AACA;AACA;;AACQ,SAAKM,QAAL,GAAgB7C,QAAQ,CAACsC,QAAT,CAAkBO,QAAlC;AACA;AACR;AACA;;AACQ,SAAKC,aAAL,GAAqB9C,QAAQ,CAACsC,QAAT,CAAkBQ,aAAvC;AACA;AACR;AACA;;AACQ,SAAKC,MAAL,GAAc/C,QAAQ,CAACsC,QAAT,CAAkBS,MAAhC;AACA;AACR;AACA;AACA;;AACQ,SAAKe,KAAL,GAAa,EAAb;AACA;AACR;AACA;;AACQ,SAAK7D,QAAL,GAAgB,KAAKgN,QAAL,CAAcpG,GAAd,CAAkBqG,iBAAlB,CAAhB;AACA;AACR;AACA;;AACQ,SAAKC,kBAAL,GAA0B,EAA1B;AACA;AACR;AACA;AACA;;AACQ,SAAKC,IAAL,GAAY,MAAM;AACd,YAAMC,eAAe,GAAG,KAAKpN,QAAL,CAAc6D,KAAd,CAAoBwJ,MAApB,KAA+B,KAAKrN,QAAL,CAAcG,QAArE;AACA,YAAM7D,KAAK,GAAG,KAAKgR,YAAL,EAAd;AACA,YAAMC,cAAc,GAAGjR,KAAK,CAACE,IAAN,GAAa6Q,MAAb,IAAuB,KAAK3K,iBAAnD;AACA,YAAMiC,QAAQ,GAAG,KAAK6I,iBAAL,EAAjB;AACA,YAAM3J,KAAK,GAAG,KAAK4J,gBAAL,CAAsBnR,KAAtB,CAAd;AACA,YAAMoR,QAAQ,GAAG7J,KAAK,CAACwJ,MAAN,GAAe,CAAhC;AACA,YAAMM,QAAQ,GAAG,KAAKC,SAAL,KAAmB,KAApC;AACA,YAAMnL,mBAAmB,GAAG,KAAKA,mBAAL,IAA4BiL,QAA5B,IAAwC,CAACpR,KAArE;AACA,YAAMuR,UAAU,GAAG,KAAK7N,QAAL,CAAc4B,OAAjC;AACA,YAAMkM,UAAU,GAAGH,QAAQ,KAAMD,QAAQ,IAAIH,cAAb,IAAgC9K,mBAArC,CAA3B;AACA,YAAMsL,UAAU,GAAG,KAAKH,SAAL,IAAkB,CAACF,QAAtC;;AACA,UAAI,KAAKM,sBAAL,IAA+BT,cAAnC,EAAmD;AAC/C,eAAO,KAAKU,sBAAL,CAA4B3R,KAA5B,CAAP;AACH;;AACD,UAAK,CAAC,KAAKmG,mBAAN,IAA6B,CAACnG,KAA/B,IACA8Q,eADA,IAEAS,UAFJ,EAEgB;AACZ,eAAO,KAAKxL,QAAL,CAAc6L,IAAd,EAAP;AACH;;AACD,WAAKC,QAAL,CAActK,KAAd;;AACA,UAAIiK,UAAJ,EAAgB;AACZ,aAAKzL,QAAL,CAAc8K,IAAd,CAAmBxI,QAAnB;AACH,OAFD,MAGK,IAAIoJ,UAAJ,EAAgB;AACjB,aAAKG,IAAL;AACH;AACJ,KA3BD;AA4BA;AACR;AACA;AACA;;;AACQ,SAAKE,aAAL;AAAA,mCAAqB,WAAO9J,IAAP,EAAgB;AACjC,cAAM9F,GAAG,GAAG,KAAI,CAAC6P,cAAL,CAAoB/J,IAApB,CAAZ;;AACA,cAAM,KAAI,CAACtE,QAAL,CAAcN,iBAAd,CAAgC,IAAhC,EAAsClB,GAAtC,EAA2C8P,KAA3C,CAAiD,MAAM,CAAG,CAA1D,CAAN;AACH,OAHD;;AAAA;AAAA;AAAA;AAAA;AAIA;AACR;AACA;;;AACQ,SAAKC,UAAL,GAAkB,MAAM;AACpB,WAAK1K,KAAL,GAAa,EAAb;AACH,KAFD;AAGA;AACR;AACA;AACA;;;AACQ,SAAKoK,sBAAL,GAA+BlI,IAAD,IAAU;AACpC,WAAKyI,eAAL,CAAqB,IAArB;;AACA,YAAMC,WAAW,GAAI3P,IAAD,IAAU;AAC1B;AACA,aAAK0P,eAAL,CAAqB,KAArB,EACI;AADJ,SAEKE,aAFL,CAEmB5P,IAFnB;AAGA,aAAKqP,QAAL,CAAc,KAAKV,gBAAL,CAAsB1H,IAAtB,CAAd;;AACA,YAAI,KAAKlC,KAAL,CAAWwJ,MAAf,EAAuB;AACnB,eAAKhL,QAAL,CAAc8K,IAAd,CAAmB,KAAKK,iBAAL,EAAnB;AACH,SAFD,MAGK;AACD,eAAKnL,QAAL,CAAc6L,IAAd;AACH;AACJ,OAZD;;AAaA,WAAKF,sBAAL,CAA4BjI,IAA5B,EACK4I,IADL,CACUrT,KAAK,EADf,EAEKsT,SAFL,CAEeH,WAFf,EAE4B,MAAM,KAAKD,eAAL,CAAqB,KAArB,CAFlC;AAGH,KAlBD;AAmBH;AACD;AACJ;AACA;AACA;;;AACyB,MAAjBK,iBAAiB,CAAChL,KAAD,EAAQ;AACzB,SAAKqJ,kBAAL,GAA0BrJ,KAA1B;AACH;AACD;AACJ;AACA;AACA;;;AACyB,MAAjBgL,iBAAiB,GAAG;AACpB,UAAMhL,KAAK,GAAG,KAAKqJ,kBAAnB;;AACA,QAAI,CAACrJ,KAAL,EAAY;AACR,aAAO,EAAP;AACH;;AACD,WAAOA,KAAK,CAACxI,GAAN,CAAWiJ,IAAD,IAAU;AACvB,aAAO,OAAOA,IAAP,KAAgB,QAAhB,GACD;AACE,SAAC,KAAKtC,SAAN,GAAkBsC,IADpB;AAEE,SAAC,KAAKrC,UAAN,GAAmBqC;AAFrB,OADC,GAKDA,IALN;AAMH,KAPM,CAAP;AAQH;AACD;AACJ;AACA;;;AACIwK,EAAAA,eAAe,GAAG;AACd,SAAKC,aAAL,GAAqBH,SAArB,CAAgCtK,IAAD,IAAU;AACrC,WAAK8J,aAAL,CAAmB9J,IAAnB;AACH,KAFD,EADc,CAId;;AACA,SAAK0K,MAAL,GAAcJ,SAAd,CAAwB,KAAKL,UAA7B;AACA,UAAMU,aAAa,GAAG,GAAtB;AACA,UAAMC,SAAS,GAAG,KAAKtM,QAAvB;AACA,SAAK5C,QAAL,CAAcmP,YAAd,CACKC,YADL,GAEKT,IAFL,CAEU3S,oBAAoB,EAF9B,EAEkCR,YAAY,CAACyT,aAAD,CAF9C,EAE+D9T,MAAM,CAAEmB,KAAD,IAAW;AAC7E,UAAI4S,SAAS,KAAK,KAAlB,EAAyB;AACrB,eAAO5S,KAAK,CAAC+Q,MAAN,GAAe,CAAtB;AACH;;AACD,aAAO,IAAP;AACH,KALoE,CAFrE,EAQKuB,SARL,CAQe,KAAKzB,IARpB;AASH;AACD;AACJ;AACA;;;AACIkC,EAAAA,cAAc,GAAG;AACb,UAAM1K,QAAQ,GAAG,KAAK3E,QAAL,CAAcsP,SAAd,CAAwBzH,kBAAxB,EAAjB;AACA,SAAKxF,QAAL,CAAckN,IAAd,CAAmBF,cAAnB,CAAkC1K,QAAlC,EAA4C,KAAK9B,aAAjD;AACH;AACD;AACJ;AACA;;;AACiB,MAAT+K,SAAS,GAAG;AACZ,WAAO,KAAKvL,QAAL,CAAckN,IAAd,CAAmBC,aAAnB,CAAiCC,SAAjC,CAA2C7B,SAAlD;AACH;AACD;AACJ;AACA;;;AACIoB,EAAAA,MAAM,GAAG;AACL,WAAO,KAAK3M,QAAL,CAAc2M,MAArB;AACH;AACD;AACJ;AACA;;;AACID,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAK1M,QAAL,CAAc0M,aAArB;AACH;AACD;AACJ;AACA;;;AACoB,MAAZW,YAAY,GAAG;AACf,WAAO,KAAKrN,QAAL,CAAckN,IAAd,CAAmBC,aAAnB,CAAiCA,aAAjC,CAA+CE,YAAtD;AACH;AACD;AACJ;AACA;;;AACa,MAAL/T,KAAK,GAAG;AACR,WAAO,KAAK0G,QAAL,CAAckN,IAAd,CAAmBC,aAA1B;AACH;AACD;AACJ;AACA;;;AACItB,EAAAA,IAAI,GAAG;AACH,SAAKK,UAAL;AACA,SAAKlM,QAAL,CAAc6L,IAAd;AACH;AACD;AACJ;AACA;;;AACIyB,EAAAA,cAAc,GAAG;AACb,QAAI,CAAC,KAAK/B,SAAN,IAAmB,CAAC,KAAK/K,aAA7B,EAA4C;AACxC;AACH;;AACD,SAAKwM,cAAL;AACH;AACD;AACJ;AACA;;;AACIO,EAAAA,YAAY,GAAG;AACX,SAAKvN,QAAL,CAAc6L,IAAd;AACH;AACD;AACJ;AACA;;;AACIZ,EAAAA,YAAY,GAAG;AACX,UAAMuC,SAAS,GAAG,KAAK7P,QAAL,CAAc6P,SAAhC;AACA,WAAOA,SAAS,GAAGA,SAAS,CAAC3M,QAAV,GAAqB1G,IAArB,EAAH,GAAiC,EAAjD;AACH;AACD;AACJ;AACA;;;AACIgR,EAAAA,iBAAiB,GAAG;AAChB,WAAO,KAAKxN,QAAL,CAAcsP,SAAd,CAAwBzH,kBAAxB,EAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIwG,EAAAA,cAAc,CAAC/J,IAAD,EAAO;AACjB,UAAMwL,OAAO,GAAG,OAAOxL,IAAI,CAAChI,KAAZ,KAAsB,QAAtB,GAAiCgI,IAAI,CAAChI,KAAtC,GAA8CgI,IAAI,CAAChI,KAAL,CAAW,KAAK0F,SAAhB,CAA9D;AACA,UAAM1F,KAAK,GAAG,OAAOgI,IAAI,CAAChI,KAAZ,KAAsB,QAAtB,GAAiCgI,IAAI,CAAChI,KAAtC,GAA8CgI,IAAI,CAAChI,KAAL,CAAW,KAAK2F,UAAhB,CAA5D;AACA,WAAO,EACH,GAAGqC,IAAI,CAAChI,KADL;AAEH,OAAC,KAAK0D,QAAL,CAAcgC,SAAf,GAA2B8N,OAFxB;AAGH,OAAC,KAAK9P,QAAL,CAAciC,UAAf,GAA4B3F;AAHzB,KAAP;AAKH;AACD;AACJ;AACA;AACA;;;AACImR,EAAAA,gBAAgB,CAACnR,KAAD,EAAQ;AACpB,QAAI,CAACA,KAAD,IAAU,CAAC,KAAKmG,mBAApB,EAAyC;AACrC,aAAO,EAAP;AACH;;AACD,UAAMsN,YAAY,GAAG,KAAK/P,QAAL,CAAcuB,UAAnC;AACA,WAAO,KAAKsN,iBAAL,CAAuB1T,MAAvB,CAA+BmJ,IAAD,IAAU;AAC3C,YAAM0L,QAAQ,GAAGD,YAAY,GACvB,KADuB,GAEvB,KAAK/P,QAAL,CAAciQ,IAAd,CAAmBC,IAAnB,CAAwB1R,GAAG,IAAI;AAC7B,cAAMyD,UAAU,GAAG,KAAKjC,QAAL,CAAciC,UAAjC;AACA,cAAMwI,KAAK,GAAG,OAAOjM,GAAG,CAACiM,KAAX,KAAqB,QAArB,GAAgCjM,GAAG,CAACiM,KAApC,GAA4CjM,GAAG,CAACiM,KAAJ,CAAUxI,UAAV,CAA1D;AACA,eAAOwI,KAAK,KAAKnG,IAAI,CAAC,KAAKrC,UAAN,CAArB;AACH,OAJC,CAFN;AAOA,aAAO,KAAKc,UAAL,CAAgBzG,KAAhB,EAAuBgI,IAAvB,KAAgC0L,QAAQ,KAAK,KAApD;AACH,KATM,CAAP;AAUH;AACD;AACJ;AACA;;;AACI7B,EAAAA,QAAQ,CAACtK,KAAD,EAAQ;AACZ,SAAKA,KAAL,GAAaA,KAAK,CAACsM,KAAN,CAAY,CAAZ,EAAe,KAAKxN,YAAL,IAAqBkB,KAAK,CAACwJ,MAA1C,CAAb;AACH;AACD;AACJ;AACA;AACA;;;AACIqB,EAAAA,aAAa,CAAC5P,IAAD,EAAO;AAChB,SAAK+P,iBAAL,GAAyB/P,IAAI,CAACzD,GAAL,CAASiJ,IAAI,IAAI;AACtC,aAAO,OAAOA,IAAP,KAAgB,QAAhB,GACD;AACE,SAAC,KAAKtC,SAAN,GAAkBsC,IADpB;AAEE,SAAC,KAAKrC,UAAN,GAAmBqC;AAFrB,OADC,GAKDA,IALN;AAMH,KAPwB,CAAzB;AAQA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIkK,EAAAA,eAAe,CAAC7S,KAAD,EAAQ;AACnB,SAAKqE,QAAL,CAAcoQ,SAAd,GAA0BzU,KAA1B;AACA,WAAO,IAAP;AACH;;AA9TkB;;AAgUvBoR,gBAAgB,CAACnQ,IAAjB;AAAA,mBAA6GmQ,gBAA7G,EAjqCgGzT,EAiqChG,mBAA+IA,EAAE,CAAC+W,QAAlJ;AAAA;;AACAtD,gBAAgB,CAACxE,IAAjB,kBAlqCgGjP,EAkqChG;AAAA,QAAiGyT,gBAAjG;AAAA;AAAA;AAAA;AAlqCgGzT,MAAAA,EAkqChG,0BAAwuBW,WAAxuB;AAAA;;AAAA;AAAA;;AAlqCgGX,MAAAA,EAkqChG,qBAlqCgGA,EAkqChG;AAAA;AAAA;AAAA;AAAA;AAlqCgGA,MAAAA,EAkqChG,aAA0zB2B,WAA1zB;AAAA;;AAAA;AAAA;;AAlqCgG3B,MAAAA,EAkqChG,qBAlqCgGA,EAkqChG;AAAA;AAAA;AAAA;AAAA;AAlqCgGA,MAAAA,EAkqChG;AAAA,eAAiG,oBAAjG;AAAA,gBAlqCgGA,EAkqChG;AAAA,eAAiG,kBAAjG;AAAA,gBAlqCgGA,EAkqChG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAlqCgGA,MAAAA,EAkqCsxB,qCAAt3B;AAlqCgGA,MAAAA,EAkqC40B,0CAA56B;AAlqCgGA,MAAAA,EAkqCmiC,mFAAnoC;AAlqCgGA,MAAAA,EAkqC+nD,eAA/tD;AAlqCgGA,MAAAA,EAkqCqpD,eAArvD;AAAA;;AAAA;AAlqCgGA,MAAAA,EAkqCoyB,+CAAp4B;AAlqCgGA,MAAAA,EAkqC+1B,aAA/7B;AAlqCgGA,MAAAA,EAkqC+1B,qIAA/7B;AAlqCgGA,MAAAA,EAkqCokC,aAApqC;AAlqCgGA,MAAAA,EAkqCokC,iCAApqC;AAAA;AAAA;AAAA,eAA8xD0B,IAAI,CAACC,WAAnyD,EAAi7DD,IAAI,CAACsV,eAAt7D,EAAoqExV,EAAE,CAACyV,OAAvqE,EAA4jEvV,IAAI,CAACwV,WAAjkE,EAA8wE1V,EAAE,CAAC6R,QAAjxE,EAAm1E7R,EAAE,CAAC8R,YAAt1E,EAAo6E9R,EAAE,CAAC2V,eAAv6E,EAAi+E3V,EAAE,CAACgS,gBAAp+E;AAAA,UAAwmF1Q,aAAxmF;AAAA;AAAA;;AACA;AAAA,qDAnqCgG9C,EAmqChG,mBAA2FyT,gBAA3F,EAAyH,CAAC;AAC9GjQ,IAAAA,IAAI,EAAElD,SADwG;AAE9GmD,IAAAA,IAAI,EAAE,CAAC;AAAEiM,MAAAA,QAAQ,EAAE,oBAAZ;AAAkCC,MAAAA,QAAQ,EAAE;AAA5C,KAAD;AAFwG,GAAD,CAAzH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEnM,MAAAA,IAAI,EAAExD,EAAE,CAAC+W;AAAX,KAAD,CAAP;AAAiC,GAH3E,EAG6F;AAAEhO,IAAAA,QAAQ,EAAE,CAAC;AAC1FvF,MAAAA,IAAI,EAAEhD,SADoF;AAE1FiD,MAAAA,IAAI,EAAE,CAAC9B,WAAD;AAFoF,KAAD,CAAZ;AAG7EyV,IAAAA,SAAS,EAAE,CAAC;AACZ5T,MAAAA,IAAI,EAAE5C,eADM;AAEZ6C,MAAAA,IAAI,EAAE,CAAC9C,WAAD;AAFM,KAAD,CAHkE;AAM7EsI,IAAAA,MAAM,EAAE,CAAC;AACTzF,MAAAA,IAAI,EAAEpD;AADG,KAAD,CANqE;AAQ7E8I,IAAAA,iBAAiB,EAAE,CAAC;AACpB1F,MAAAA,IAAI,EAAEpD;AADc,KAAD,CAR0D;AAU7E+I,IAAAA,mBAAmB,EAAE,CAAC;AACtB3F,MAAAA,IAAI,EAAEpD;AADgB,KAAD,CAVwD;AAY7EsU,IAAAA,sBAAsB,EAAE,CAAC;AACzBlR,MAAAA,IAAI,EAAEpD;AADmB,KAAD,CAZqD;AAc7EgJ,IAAAA,iBAAiB,EAAE,CAAC;AACpB5F,MAAAA,IAAI,EAAEpD;AADc,KAAD,CAd0D;AAgB7EiJ,IAAAA,YAAY,EAAE,CAAC;AACf7F,MAAAA,IAAI,EAAEpD;AADS,KAAD,CAhB+D;AAkB7EsI,IAAAA,SAAS,EAAE,CAAC;AACZlF,MAAAA,IAAI,EAAEpD;AADM,KAAD,CAlBkE;AAoB7EuI,IAAAA,UAAU,EAAE,CAAC;AACbnF,MAAAA,IAAI,EAAEpD;AADO,KAAD,CApBiE;AAsB7EqJ,IAAAA,UAAU,EAAE,CAAC;AACbjG,MAAAA,IAAI,EAAEpD;AADO,KAAD,CAtBiE;AAwB7E4I,IAAAA,YAAY,EAAE,CAAC;AACfxF,MAAAA,IAAI,EAAEpD;AADS,KAAD,CAxB+D;AA0B7EkJ,IAAAA,QAAQ,EAAE,CAAC;AACX9F,MAAAA,IAAI,EAAEpD;AADK,KAAD,CA1BmE;AA4B7EmJ,IAAAA,aAAa,EAAE,CAAC;AAChB/F,MAAAA,IAAI,EAAEpD;AADU,KAAD,CA5B8D;AA8B7EoJ,IAAAA,MAAM,EAAE,CAAC;AACThG,MAAAA,IAAI,EAAEpD;AADG,KAAD,CA9BqE;AAgC7EmV,IAAAA,iBAAiB,EAAE,CAAC;AACpB/R,MAAAA,IAAI,EAAEpD;AADc,KAAD,CAhC0D;AAkC7EiW,IAAAA,cAAc,EAAE,CAAC;AACjB7S,MAAAA,IAAI,EAAE9C,YADW;AAEjB+C,MAAAA,IAAI,EAAE,CAAC,eAAD;AAFW,KAAD,CAlC6D;AAqC7E6S,IAAAA,YAAY,EAAE,CAAC;AACf9S,MAAAA,IAAI,EAAE9C,YADS;AAEf+C,MAAAA,IAAI,EAAE,CAAC,aAAD;AAFS,KAAD;AArC+D,GAH7F;AAAA,K,CA6CA;;;AACA,MAAM4T,eAAe,GAAG;AACpBC,EAAAA,OAAO,EAAElW,iBADW;AAEpBmW,EAAAA,WAAW,EAAE1W,UAAU,CAAC,MAAM8S,iBAAP,CAFH;AAGpB6D,EAAAA,KAAK,EAAE;AAHa,CAAxB;;AAKA,MAAM7D,iBAAN,SAAgCtJ,gBAAhC,CAAiD;AAC7C1F,EAAAA,WAAW,CAACgM,QAAD,EAAW8G,YAAX,EAAyB;AAAA;;AAChC,WADgC;AAAA;AAEhC,SAAK9G,QAAL,GAAgBA,QAAhB;AACA,SAAK8G,YAAL,GAAoBA,YAApB;AACA;AACR;AACA;AACA;;AACQ,SAAK9Q,aAAL,GAAqBF,QAAQ,CAACC,QAAT,CAAkBC,aAAvC;AACA;AACR;AACA;AACA;;AACQ,SAAKC,iBAAL,GAAyBH,QAAQ,CAACC,QAAT,CAAkBE,iBAA3C;AACA;AACR;AACA;AACA;;AACQ,SAAKG,WAAL,GAAmBN,QAAQ,CAACC,QAAT,CAAkBK,WAArC;AACA;AACR;AACA;AACA;;AACQ,SAAKC,oBAAL,GAA4BP,QAAQ,CAACC,QAAT,CAAkBM,oBAA9C;AACA;AACR;AACA;AACA;;AACQ,SAAKH,QAAL,GAAgBJ,QAAQ,CAACC,QAAT,CAAkBG,QAAlC;AACA;AACR;AACA;AACA;;AACQ,SAAKI,UAAL,GAAkBR,QAAQ,CAACC,QAAT,CAAkBO,UAApC;AACA;AACR;AACA;AACA;;AACQ,SAAKC,eAAL,GAAuBT,QAAQ,CAACC,QAAT,CAAkBQ,eAAzC;AACA;AACR;AACA;AACA;;AACQ,SAAKC,oBAAL,GAA4BV,QAAQ,CAACC,QAAT,CAAkBS,oBAA9C;AACA;AACR;AACA;;AACQ,SAAKC,aAAL,GAAqBX,QAAQ,CAACC,QAAT,CAAkBU,aAAvC;AACA;AACR;AACA;;AACQ,SAAKC,KAAL,GAAaZ,QAAQ,CAACC,QAAT,CAAkBW,KAA/B;AACA;AACR;AACA;;AACQ,SAAKC,oBAAL,GAA4Bb,QAAQ,CAACC,QAAT,CAAkBY,oBAA9C;AACA;AACR;AACA;AACA;;AACQ,SAAKC,OAAL,GAAed,QAAQ,CAACC,QAAT,CAAkBa,OAAjC;AACA;AACR;AACA;;AACQ,SAAKC,UAAL,GAAkBf,QAAQ,CAACC,QAAT,CAAkBc,UAApC;AACA;AACR;AACA;AACA;;AACQ,SAAKC,WAAL,GAAmBhB,QAAQ,CAACC,QAAT,CAAkBe,WAArC;AACA;AACR;AACA;AACA;;AACQ,SAAKC,QAAL,GAAgBjB,QAAQ,CAACC,QAAT,CAAkBgB,QAAlC;AACA;AACR;AACA;;AACQ,SAAKC,SAAL,GAAiBlB,QAAQ,CAACC,QAAT,CAAkBiB,SAAnC;AACA;AACR;AACA;;AACQ,SAAKC,UAAL,GAAkBnB,QAAQ,CAACC,QAAT,CAAkBkB,UAApC;AACA;AACR;AACA;AACA;;AACQ,SAAKC,iBAAL,GAAyBpB,QAAQ,CAACC,QAAT,CAAkBmB,iBAA3C;AACA;AACR;AACA;;AACQ,SAAKC,WAAL,GAAmBrB,QAAQ,CAACC,QAAT,CAAkBoB,WAArC;AACA;AACR;AACA;;AACQ,SAAKC,SAAL,GAAiBtB,QAAQ,CAACC,QAAT,CAAkBqB,SAAnC;AACA;AACR;AACA;;AACQ,SAAKC,QAAL,GAAgBvB,QAAQ,CAACC,QAAT,CAAkBsB,QAAlC;AACA;AACR;AACA;;AACQ,SAAKC,UAAL,GAAkBxB,QAAQ,CAACC,QAAT,CAAkBuB,UAApC;AACA;AACR;AACA;AACA;;AACQ,SAAKC,cAAL,GAAsBzB,QAAQ,CAACC,QAAT,CAAkBwB,cAAxC;AACA;AACR;AACA;;AACQ,SAAKC,QAAL,GAAgB1B,QAAQ,CAACC,QAAT,CAAkByB,QAAlC;AACA;AACR;AACA;;AACQ,SAAKC,MAAL,GAAc3B,QAAQ,CAACC,QAAT,CAAkB0B,MAAhC;AACA;AACR;AACA;AACA;;AACQ,SAAKkE,QAAL,GAAgB7F,QAAQ,CAACC,QAAT,CAAkB2B,QAAlC;AACA;AACR;AACA;;AACQ,SAAKC,OAAL,GAAe7B,QAAQ,CAACC,QAAT,CAAkB4B,OAAjC;AACA;AACR;AACA;;AACQ,SAAKC,QAAL,GAAgB9B,QAAQ,CAACC,QAAT,CAAkB6B,QAAlC;AACA;AACR;AACA;;AACQ,SAAKC,UAAL,GAAkB/B,QAAQ,CAACC,QAAT,CAAkB8B,UAApC;AACA;AACR;AACA;;AACQ,SAAKC,QAAL,GAAgBhC,QAAQ,CAACC,QAAT,CAAkB+B,QAAlC;AACA;AACR;AACA;;AACQ,SAAKG,iBAAL,GAAyBnC,QAAQ,CAACC,QAAT,CAAkBkC,iBAA3C;AACA;AACR;AACA;AACA;;AACQ,SAAK8O,KAAL,GAAa,IAAIrX,YAAJ,EAAb;AACA;AACR;AACA;AACA;;AACQ,SAAKyQ,QAAL,GAAgB,IAAIzQ,YAAJ,EAAhB;AACA;AACR;AACA;AACA;;AACQ,SAAKwQ,QAAL,GAAgB,IAAIxQ,YAAJ,EAAhB;AACA;AACR;AACA;AACA;;AACQ,SAAK6L,OAAL,GAAe,IAAI7L,YAAJ,EAAf;AACA;AACR;AACA;AACA;;AACQ,SAAK4L,MAAL,GAAc,IAAI5L,YAAJ,EAAd;AACA;AACR;AACA;AACA;;AACQ,SAAKwV,YAAL,GAAoB,IAAIxV,YAAJ,EAApB;AACA;AACR;AACA;AACA;;AACQ,SAAKsX,OAAL,GAAe,IAAItX,YAAJ,EAAf;AACA;AACR;AACA;AACA;;AACQ,SAAKuX,iBAAL,GAAyB,IAAIvX,YAAJ,EAAzB;AACA;AACR;AACA;AACA;;AACQ,SAAK0Q,WAAL,GAAmB,IAAI1Q,YAAJ,EAAnB;AACA;AACR;AACA;;AACQ,SAAKyW,SAAL,GAAiB,KAAjB;AACA;AACR;AACA;AACA;;AACQ,SAAKnL,SAAL,GAAiB;AACb,OAAC9H,OAAD,GAAW,EADE;AAEb,OAACC,KAAD,GAAS;AAFI,KAAjB;AAIA;AACR;AACA;AACA;;AACQ,SAAKuI,eAAL,GAAuB,IAAIhM,YAAJ,EAAvB;AACA;AACR;AACA;AACA;;AACQ,SAAKwX,cAAL,GAAsB,EAAtB;AACA,SAAKC,MAAL,GAAc,EAAd;AACA;AACR;AACA;AACA;;AACQ,SAAKC,SAAL,GAAiB,CAAC7S,GAAD,EAAMJ,KAAK,GAAG,KAAKyF,KAAL,CAAWwJ,MAAzB,KAAoC;AACjD,YAAMxJ,KAAK,GAAG,KAAKA,KAAnB;AACA,YAAM4G,KAAK,GAAG,KAAKjJ,cAAL,GAAsBhD,GAAG,CAAC,KAAKyD,UAAN,CAAzB,GAA6CzD,GAA3D;AACA,WAAKqF,KAAL,GAAa,CACT,GAAGA,KAAK,CAACsM,KAAN,CAAY,CAAZ,EAAe/R,KAAf,CADM,EAETqM,KAFS,EAGT,GAAG5G,KAAK,CAACsM,KAAN,CAAY/R,KAAZ,EAAmByF,KAAK,CAACwJ,MAAzB,CAHM,CAAb;AAKH,KARD;AASA;AACR;AACA;AACA;;;AACQ,SAAKiE,SAAL,GAAkB7G,KAAD,IAAW;AACxB,YAAMjO,IAAI,GAAG,CAAC+U,GAAD,EAAM1R,GAAN,KAAc;AACvB,eAAO,OAAO0R,GAAP,KAAe,QAAf,GAA0BA,GAAG,CAAC/U,IAAJ,EAA1B,GAAuC+U,GAAG,CAAC1R,GAAD,CAAjD;AACH,OAFD;;AAGA,aAAO,EACH,IAAG,OAAO4K,KAAP,KAAiB,QAAjB,GAA4BA,KAA5B,GAAoC,EAAvC,CADG;AAEH,SAAC,KAAKzI,SAAN,GAAkB,KAAKP,QAAL,GAAgBjF,IAAI,CAACiO,KAAD,EAAQ,KAAKzI,SAAb,CAApB,GAA8CyI,KAF7D;AAGH,SAAC,KAAKxI,UAAN,GAAmB,KAAKR,QAAL,GAAgBjF,IAAI,CAACiO,KAAD,EAAQ,KAAKxI,UAAb,CAApB,GAA+CwI;AAH/D,OAAP;AAKH,KATD;AAUA;AACR;AACA;AACA;AACA;;;AACQ,SAAK+G,UAAL,GAAkB,CAAChT,GAAD,EAAMiT,gBAAgB,GAAG,KAAzB,KAAmC;AACjD,YAAM/B,YAAY,GAAG,KAAKrN,QAAL,GAAgB,KAAKA,QAAL,CAAcqN,YAA9B,GAA6CrR,SAAlE;AACA,YAAM/B,KAAK,GAAG,KAAKmI,cAAL,CAAoBjG,GAApB,EAAyBhC,IAAzB,EAAd;;AACA,UAAIkT,YAAY,IAAI,CAAC+B,gBAAjB,IAAqC,CAACnV,KAA1C,EAAiD;AAC7C,eAAO,KAAP;AACH;;AACD,YAAMoV,IAAI,GAAG,KAAKC,QAAL,CAAcnT,GAAd,EAAmBiT,gBAAnB,CAAb,CANiD,CAOjD;;AACA,UAAI,CAAC,KAAKlQ,UAAN,IAAoBmQ,IAApB,IAA4B,KAAKtQ,WAArC,EAAkD;AAC9C,cAAMqJ,KAAK,GAAG,KAAKwF,IAAL,CAAU2B,IAAV,CAAetN,IAAI,IAAI;AACjC,iBAAO,KAAKD,YAAL,CAAkBC,IAAI,CAACmG,KAAvB,MAAkC,KAAKpG,YAAL,CAAkBqN,IAAlB,CAAzC;AACH,SAFa,CAAd;;AAGA,YAAIjH,KAAJ,EAAW;AACPA,UAAAA,KAAK,CAACS,KAAN;AACH;AACJ;;AACD,YAAM2G,kBAAkB,GAAGJ,gBAAgB,IAAI,KAAKhR,oBAApD;AACA,YAAMqR,UAAU,GAAG,CACf;AACA,OAACJ,IAAD,IAAS,KAAKnQ,UAFC,EAGf;AACA,OAAC,KAAK6L,eAJS,EAKf;AACEyE,MAAAA,kBAAD,IAAwB,CAAC,KAAKpR,oBANhB,CAAnB;AAQA,aAAOqR,UAAU,CAAC3W,MAAX,CAAkB4W,OAAlB,EAA2B1E,MAA3B,KAAsCyE,UAAU,CAACzE,MAAxD;AACH,KA1BD;AA2BA;AACR;AACA;AACA;;;AACQ,SAAK2E,eAAL;AAAA,oCAAuB,WAAOlT,IAAP,EAAgB;AACnC,cAAMmT,OAAO,GAAG,MAAM;AAClB,gBAAMC,IAAI,GAAGH,OAAO,CAACrI,MAAM,CAACyI,aAAR,CAApB;AACA,gBAAMA,aAAa,GAAGD,IAAI,GAAIxI,MAAM,CAACyI,aAAX,GAA4BrT,IAAI,CAACqT,aAA3D;AACA,gBAAMrV,IAAI,GAAGoV,IAAI,GAAG,MAAH,GAAY,YAA7B;AACA,iBAAOC,aAAa,KAAK,IAAlB,GAAyB,EAAzB,GAA8BA,aAAa,CAACpT,OAAd,CAAsBjC,IAAtB,KAA+B,EAApE;AACH,SALD;;AAMA,cAAMiJ,IAAI,GAAGkM,OAAO,EAApB;AACA,cAAMG,QAAQ,GAAGrM,IAAI,CAChBsM,KADY,CACN,MAAI,CAAClR,iBADC,EAEZ9F,GAFY,CAERiJ,IAAI,IAAI;AACb,gBAAM9F,GAAG,GAAG,MAAI,CAAC8S,SAAL,CAAehN,IAAf,CAAZ;;AACA,UAAA,MAAI,CAACgO,aAAL,CAAmB9T,GAAG,CAAC,MAAI,CAACwD,SAAN,CAAtB;;AACA,iBAAO,MAAI,CAACtC,iBAAL,CAAuB,KAAvB,EAA8BlB,GAA9B,CAAP;AACH,SANgB,CAAjB;;AAOA,cAAM+T,UAAU,GAAG,MAAMlH,UAAU,CAAC,MAAM,MAAI,CAACiH,aAAL,CAAmB,EAAnB,CAAP,EAA+B,EAA/B,CAAnC;;AACAE,QAAAA,OAAO,CAACC,GAAR,CAAYL,QAAZ,EAAsBM,IAAtB,CAA2B,MAAM;AAC7B,UAAA,MAAI,CAACzB,OAAL,CAAahL,IAAb,CAAkBF,IAAlB;;AACAwM,UAAAA,UAAU;AACb,SAHD,EAIKjE,KAJL,CAIWiE,UAJX;AAKH,OArBD;;AAAA;AAAA;AAAA;AAAA;AAsBH;AACD;AACJ;AACA;;;AACiB,MAATzM,SAAS,GAAG;AACZ,WAAO,KAAKqL,cAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACiB,MAATrL,SAAS,CAACC,IAAD,EAAO;AAChB,SAAKoL,cAAL,GAAsBpL,IAAtB;AACA,SAAKJ,eAAL,CAAqBM,IAArB,CAA0BF,IAA1B;AACH;AACD;AACJ;AACA;AACA;;;AACoB,MAAZ4M,YAAY,GAAG;AACf,WAAO,KAAK/M,QAAL,KAAkB,EAAlB,GAAuB,IAAvB,GAA8B,EAArC;AACH;AACD;AACJ;AACA;;;AACIkJ,EAAAA,eAAe,GAAG;AACd;AACA,SAAK8D,sBAAL;AACA,SAAKC,0BAAL;AACA,SAAKC,0BAAL;;AACA,QAAI,KAAK3D,YAAL,CAAkB4D,SAAlB,CAA4B1F,MAAhC,EAAwC;AACpC,WAAK2F,yBAAL;AACH,KAPa,CAQd;;;AACA,QAAI,KAAKjS,WAAL,IAAoB,KAAKE,SAA7B,EAAwC;AACpC,WAAKgS,qBAAL;AACH,KAXa,CAYd;;;AACA,QAAI,KAAK/R,UAAT,EAAqB;AACjB,WAAKgS,oBAAL;AACH;;AACD,UAAMC,cAAc,GAAG,KAAK7D,SAAL,CAAejJ,IAAf,CAAoB+M,aAA3C;AACAD,IAAAA,cAAc,CAACxE,IAAf,CAAoBvT,QAAQ,CAAEiY,MAAD,IAAYA,MAAM,KAAK,SAAxB,CAA5B,EAAgEzE,SAAhE,CAA0E,MAAM;AAC5E,WAAKwC,MAAL,GAAc,KAAK9B,SAAL,CAAenI,gBAAf,CAAgC,KAAKzG,aAArC,CAAd;AACH,KAFD;AAGA,SAAK4S,qBAAL,GAA6BH,cAAc,CAACxE,IAAf,CAAoBtT,GAAG,CAAEgY,MAAD,IAAY;AAC7D,aAAOA,MAAM,KAAK,SAAX,IAAwB,KAAKjD,SAApC;AACH,KAFmD,CAAvB,CAA7B,CApBc,CAuBd;;AACA,QAAI,KAAKpP,QAAT,EAAmB;AACf,WAAKsO,SAAL,CAAevH,OAAf;AACH;AACJ;AACD;AACJ;AACA;;;AACI7B,EAAAA,QAAQ,GAAG;AACP;AACA;AACA;AACA,UAAMqN,kBAAkB,GAAG,KAAKpT,QAAL,KAAkB9B,SAAlB,IACvB,KAAKwF,KADkB,IAEvB,KAAKA,KAAL,CAAWwJ,MAAX,GAAoB,KAAKlN,QAF7B;;AAGA,QAAIoT,kBAAJ,EAAwB;AACpB,WAAKpT,QAAL,GAAgB,KAAK0D,KAAL,CAAWwJ,MAA3B;AACAmG,MAAAA,OAAO,CAACC,IAAR,CAAanW,iBAAb;AACH,KAVM,CAWP;AACA;;;AACA,SAAKgE,QAAL,GAAgB,KAAKb,oBAAL,GAA4B,KAA5B,GAAoC,KAAKa,QAAzD;AACA,SAAKoS,oBAAL;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIjU,EAAAA,iBAAiB,CAACjB,GAAD,EAAMJ,KAAN,EAAa;AAC1B,WAAO,IAAIoU,OAAJ,CAAYmB,OAAO,IAAI;AAC1B,YAAMlF,WAAW,GAAIhE,KAAD,IAAW;AAC3B,aAAKmJ,UAAL,CAAgBnJ,KAAhB,EAAuBrM,KAAvB;AACAuV,QAAAA,OAAO,CAACnV,GAAD,CAAP;AACH,OAHD;;AAIA,WAAKsD,UAAL,GACI,KAAKA,UAAL,CAAgBtD,GAAhB,EACKmQ,IADL,CACUpT,OAAO,EADjB,EAEKqT,SAFL,CAEeH,WAFf,CADJ,GAGkCA,WAAW,CAACjQ,GAAD,CAH7C;AAIH,KATM,CAAP;AAUH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIkB,EAAAA,iBAAiB,CAAC+R,gBAAD,EAAmBjT,GAAnB,EAAwBJ,KAAxB,EAA+ByV,WAA/B,EAA4C;AACzD,WAAO,IAAIrB,OAAJ,CAAY,CAACmB,OAAD,EAAUG,MAAV,KAAqB;AACpC,YAAMrF,WAAW,GAAIhE,KAAD,IAAW;AAC3B,eAAO,KACFsJ,OADE,CACMtC,gBADN,EACwBhH,KADxB,EAC+BrM,KAD/B,EACsCyV,WADtC,EAEFnB,IAFE,CAEGiB,OAFH,EAGFrF,KAHE,CAGIwF,MAHJ,CAAP;AAIH,OALD;;AAMA,aAAO,KAAK/R,QAAL,GACH,KAAKA,QAAL,CAAcvD,GAAd,EACKmQ,IADL,CACUpT,OAAO,EADjB,EAEKqT,SAFL,CAEeH,WAFf,EAE4BqF,MAF5B,CADG,GAGmCrF,WAAW,CAACjQ,GAAD,CAHrD;AAIH,KAXM,CAAP;AAYH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIwV,EAAAA,UAAU,CAAC1P,IAAD,EAAO2B,IAAI,GAAG,IAAd,EAAoB;AAC1B,UAAMgO,UAAU,GAAG3P,IAAI,IAAI,OAAOA,IAAP,KAAgB,QAAxB,IAAoCA,IAAI,CAACkG,QAA5D;;AACA,QAAIyJ,UAAU,IAAI,KAAKC,WAAL,KAAqB5P,IAAvC,EAA6C;AACzC;AACH;;AACD,SAAK4P,WAAL,GAAmB5P,IAAnB;;AACA,QAAI2B,IAAJ,EAAU;AACN,WAAKkE,QAAL,CAAclE,IAAd,CAAmB3B,IAAnB;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACI6P,EAAAA,UAAU,CAACC,SAAD,EAAYjM,MAAZ,EAAoB;AAC1B,SAAKlD,SAAL,CAAemP,SAAf,EAA0BC,OAA1B,CAAkCC,QAAQ,IAAIA,QAAQ,CAACC,IAAT,CAAc,IAAd,EAAoBpM,MAApB,CAA9C;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIqM,EAAAA,aAAa,CAAC1V,IAAD,EAAO;AAChB,UAAMP,KAAK,GAAGO,IAAI,CAACP,KAAnB;AACA,UAAMsB,GAAG,GAAGtB,KAAK,CAACyM,OAAN,IAAiBzM,KAAK,CAACkW,KAAnC;AACA,UAAMC,QAAQ,GAAGnW,KAAK,CAACmW,QAAN,IAAkB,KAAnC;;AACA,YAAQ9W,iBAAiB,CAACiC,GAAD,CAAzB;AACI,WAAKtC,YAAY,CAACC,MAAlB;AACI,YAAI,KAAK0W,WAAL,IAAoB,KAAK7S,SAA7B,EAAwC;AACpC,gBAAMjD,KAAK,GAAG,KAAKyF,KAAL,CAAWT,OAAX,CAAmB,KAAK8Q,WAAxB,CAAd;AACA,eAAKzU,iBAAL,CAAuB,KAAKyU,WAA5B,EAAyC9V,KAAzC;AACH;;AACD;;AACJ,WAAKb,YAAY,CAACE,WAAlB;AACI,aAAKkX,SAAL,CAAe7V,IAAI,CAAC2L,KAApB,EAA2B1M,IAA3B;AACA;;AACJ,WAAKR,YAAY,CAACG,WAAlB;AACI,aAAKiX,SAAL,CAAe7V,IAAI,CAAC2L,KAApB,EAA2B3M,IAA3B;AACA;;AACJ,WAAKP,YAAY,CAACI,GAAlB;AACI,YAAI+W,QAAJ,EAAc;AACV,cAAI,KAAKE,UAAL,CAAgB9V,IAAI,CAAC2L,KAArB,CAAJ,EAAiC;AAC7B;AACH;;AACD,eAAKkK,SAAL,CAAe7V,IAAI,CAAC2L,KAApB,EAA2B1M,IAA3B;AACH,SALD,MAMK;AACD,cAAI,KAAK8W,SAAL,CAAe/V,IAAI,CAAC2L,KAApB,MAA+B,KAAK7I,OAAL,IAAgB,KAAKwL,eAApD,CAAJ,EAA0E;AACtE;AACH;;AACD,eAAKuH,SAAL,CAAe7V,IAAI,CAAC2L,KAApB,EAA2B3M,IAA3B;AACH;;AACD;;AACJ;AACI;AA5BR,KAJgB,CAkChB;;;AACAS,IAAAA,KAAK,CAAC+J,cAAN;AACH;;AACKwM,EAAAA,YAAY,GAAG;AAAA;;AAAA;AACjB,UAAI;AACA,cAAM,MAAI,CAACpV,iBAAL,CAAuB,KAAvB,EAA8B,MAAI,CAACmQ,SAAnC,CAAN;AACH,OAFD,CAGA,MAAM;AACF;AACH;AANgB;AAOpB;AACD;AACJ;AACA;AACA;;;AACIyC,EAAAA,aAAa,CAAChW,KAAD,EAAQyY,SAAS,GAAG,IAApB,EAA0B;AACnC,UAAMC,OAAO,GAAG,KAAKC,UAAL,EAAhB,CADmC,CAEnC;;AACAD,IAAAA,OAAO,CAAChP,QAAR,CAAiB1J,KAAjB,EAAwB;AAAEyY,MAAAA;AAAF,KAAxB;AACH;AACD;AACJ;AACA;;;AACIE,EAAAA,UAAU,GAAG;AACT,WAAO,KAAK3F,SAAL,CAAehT,KAAtB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIqL,EAAAA,KAAK,CAACuN,UAAU,GAAG,KAAd,EAAqBC,mBAAmB,GAAG,KAA3C,EAAkD;AACnD,QAAI,KAAKpE,YAAL,CAAkBnR,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;AACxC;AACH;;AACD,SAAKoU,UAAL,CAAgB3V,SAAhB,EAA2B,KAA3B;;AACA,QAAI6W,UAAJ,EAAgB;AACZ,WAAK5F,SAAL,CAAe3H,KAAf;AACA,WAAKnC,OAAL,CAAaS,IAAb,CAAkB,KAAK4J,SAAvB;AACH;AACJ;AACD;AACJ;AACA;;;AACIjI,EAAAA,IAAI,GAAG;AACH,SAAK7D,SAAL;AACA,SAAKwB,MAAL,CAAYU,IAAZ,CAAiB,KAAK4J,SAAtB;AACH;AACD;AACJ;AACA;;;AACIrI,EAAAA,SAAS,GAAG;AACR,WAAO,CAAC,CAAC,KAAK8H,SAAP,IAAoB,KAAKA,SAAL,CAAe9H,SAAf,EAA3B;AACH;AACD;AACJ;AACA;;;AACIX,EAAAA,cAAc,GAAG;AACb,WAAO,CAAC,CAAC,KAAKyI,SAAP,IAAoB,KAAKA,SAAL,CAAezI,cAAf,EAA3B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIuO,EAAAA,iBAAiB,GAAG;AAChB,UAAMnM,QAAQ,GAAG,KAAKyH,SAAL,GAAiB,KAAKA,SAAL,CAAepV,KAAhC,GAAwC+C,SAAzD;AACA,UAAMgX,YAAY,GAAG,KAAKhT,QAAL,IAAiB,KAAKA,QAAL,CAAcqO,SAA/B,GACjB,KAAKrO,QAAL,CAAcqO,SAAd,CAAwBpV,KADP,GACe+C,SADpC;AAEA,WAAO0T,OAAO,CAAC9I,QAAQ,IAAIA,QAAQ,KAAKoM,YAA1B,CAAd;AACH;AACD;AACJ;AACA;;;AACuB,MAAfjI,eAAe,GAAG;AAClB,WAAO,KAAKjN,QAAL,KAAkB9B,SAAlB,IACH,KAAKwF,KAAL,CAAWwJ,MAAX,IAAqB,KAAKlN,QAD9B;AAEH;AACD;AACJ;AACA;;;AACiB,MAAT0P,SAAS,GAAG;AACZ,UAAMxJ,IAAI,GAAG,KAAKiJ,SAAL,CAAehT,KAA5B;AACA,WAAO+J,IAAI,GAAGA,IAAI,CAAC/J,KAAR,GAAgB,EAA3B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIgZ,EAAAA,aAAa,CAAC/W,KAAD,EAAQC,GAAR,EAAaJ,KAAb,EAAoB;AAC7BG,IAAAA,KAAK,CAACoM,eAAN;AACA,UAAMrG,IAAI,GAAG;AAAEiR,MAAAA,IAAI,EAAE,KAAK1T,QAAb;AAAuBrD,MAAAA,GAAvB;AAA4BJ,MAAAA;AAA5B,KAAb;AACA,SAAK2S,YAAL,CAAkB9R,SAAlB,CAA4B,IAA5B;AACA,SAAK8R,YAAL,CAAkBzS,cAAlB,CAAiCC,KAAjC,EAAwC+F,IAAxC;AACA,SAAKyM,YAAL,CAAkBpR,QAAlB,CAA2B;AAAEzB,MAAAA,QAAQ,EAAE,IAAZ;AAAkBE,MAAAA;AAAlB,KAA3B;AACH;AACD;AACJ;AACA;AACA;;;AACIoX,EAAAA,UAAU,CAACjX,KAAD,EAAQH,KAAR,EAAe;AACrB,SAAK2S,YAAL,CAAkBpR,QAAlB,CAA2B;AAAExB,MAAAA,QAAQ,EAAE;AAAZ,KAA3B;AACA,SAAK4S,YAAL,CAAkB5R,WAAlB,CAA8B,IAA9B;AACAZ,IAAAA,KAAK,CAAC+J,cAAN;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIjJ,EAAAA,YAAY,CAACd,KAAD,EAAQH,KAAR,EAAe;AACvB,UAAMkG,IAAI,GAAG,KAAKyM,YAAL,CAAkBlS,cAAlB,CAAiCN,KAAjC,CAAb;;AACA,QAAI,CAAC+F,IAAD,IAASA,IAAI,CAACiR,IAAL,KAAc,KAAK1T,QAAhC,EAA0C;AACtC;AACH;;AACD,SAAKkP,YAAL,CAAkB1R,YAAlB,CAA+BiF,IAAI,CAAC9F,GAApC,EAAyC8F,IAAI,CAAClG,KAA9C,EAAqDA,KAArD;AACAG,IAAAA,KAAK,CAAC+J,cAAN;AACA/J,IAAAA,KAAK,CAACoM,eAAN;AACH;AACD;AACJ;AACA;;;AACI8K,EAAAA,UAAU,GAAG;AACT,UAAMC,UAAU,GAAG,KAAK3E,YAAL,CAAkB3R,QAAlB,KAA+B,IAAlD;AACA,UAAMqW,UAAU,GAAG,KAAK1E,YAAL,CAAkBnR,QAAlB,CAA2B,UAA3B,CAAnB;AACA,WAAOmS,OAAO,CAAC2D,UAAU,IAAID,UAAf,CAAd;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIE,EAAAA,YAAY,CAACC,cAAD,EAAiBxX,KAAjB,EAAwB;AAChC,SAAKyF,KAAL,CAAWzF,KAAX,IAAoBwX,cAApB;AACA,SAAKhO,IAAL;AACH;AACD;AACJ;AACA;AACA;;;AACIiO,EAAAA,OAAO,CAACzX,KAAD,EAAQkG,IAAR,EAAc;AACjB,WAAOA,IAAI,CAAC,KAAKrC,UAAN,CAAX;AACH;AACD;AACJ;AACA;AACA;;;AACI6T,EAAAA,eAAe,CAACtX,GAAD,EAAM;AACjB,SAAK6L,WAAL,CAAiBpE,IAAjB,CAAsBzH,GAAtB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACImW,EAAAA,SAAS,CAACrQ,IAAD,EAAOyR,SAAP,EAAkB;AACvB,UAAMC,MAAM,GAAG,KAAKnB,SAAL,CAAevQ,IAAf,CAAf;AACA,UAAM2R,OAAO,GAAG,KAAKrB,UAAL,CAAgBtQ,IAAhB,CAAhB;AACA,UAAM4R,UAAU,GAAIH,SAAS,KAAKjY,IAAd,IAAsBkY,MAAvB,IACdD,SAAS,KAAKhY,IAAd,IAAsBkY,OAD3B;;AAEA,QAAIC,UAAJ,EAAgB;AACZ,WAAKvO,KAAL,CAAW,IAAX;AACA;AACH;;AACD,UAAMpF,MAAM,GAAGwT,SAAS,KAAKjY,IAAd,GAAqB,CAArB,GAAyB,CAAC,CAAzC;AACA,UAAMM,KAAK,GAAG,KAAK+X,WAAL,CAAiB7R,IAAjB,IAAyB/B,MAAvC;AACA,UAAM/D,GAAG,GAAG,KAAK4X,aAAL,CAAmBhY,KAAnB,CAAZ;AACA,WAAOI,GAAG,CAACkM,MAAJ,CAAW6J,IAAX,CAAgB/V,GAAhB,CAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIoW,EAAAA,UAAU,CAACtQ,IAAD,EAAO;AACb,WAAO,KAAK2L,IAAL,CAAU3U,KAAV,CAAgBmP,KAAhB,KAA0BnG,IAAjC;AACH;AACD;AACJ;AACA;AACA;;;AACIuQ,EAAAA,SAAS,CAACvQ,IAAD,EAAO;AACZ,WAAO,KAAK2L,IAAL,CAAUoG,IAAV,CAAe5L,KAAf,KAAyBnG,IAAhC;AACH;AACD;AACJ;AACA;AACA;;;AACI6R,EAAAA,WAAW,CAAC7R,IAAD,EAAO;AACd,UAAM2L,IAAI,GAAG,KAAKA,IAAL,CAAUqG,OAAV,EAAb;AACA,WAAOrG,IAAI,CAACsG,SAAL,CAAe/X,GAAG,IAAIA,GAAG,CAACiM,KAAJ,KAAcnG,IAApC,CAAP;AACH;AACD;AACJ;AACA;AACA;;;AACI8R,EAAAA,aAAa,CAAChY,KAAD,EAAQ;AACjB,UAAM6R,IAAI,GAAG,KAAKA,IAAL,CAAUqG,OAAV,EAAb;AACA,WAAOrG,IAAI,CAAC7R,KAAD,CAAX;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIwV,EAAAA,UAAU,CAACpV,GAAD,EAAMJ,KAAN,EAAa;AACnB,SAAKyF,KAAL,GAAa,KAAKa,eAAL,CAAqBtG,KAArB,CAAb,CADmB,CAEnB;;AACA,QAAI,KAAK8V,WAAL,KAAqB1V,GAAzB,EAA8B;AAC1B,WAAKwV,UAAL,CAAgB3V,SAAhB,EAA2B,KAA3B;AACH,KALkB,CAMnB;;;AACA,SAAKsJ,KAAL,CAAW,IAAX,EAAiB,KAAjB,EAPmB,CAQnB;;AACA,SAAKyC,QAAL,CAAcnE,IAAd,CAAmBzH,GAAnB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIuV,EAAAA,OAAO,CAACtC,gBAAgB,GAAG,KAApB,EAA2BnN,IAA3B,EAAiClG,KAAjC,EAAwCyV,WAAxC,EAAqD;AACxD,UAAM/D,OAAO,GAAG,KAAKrL,cAAL,CAAoBH,IAApB,CAAhB;AACA,UAAM9F,GAAG,GAAG,KAAK8S,SAAL,CAAehN,IAAf,CAAZ;;AACA,QAAImN,gBAAJ,EAAsB;AAClB,WAAKa,aAAL,CAAmB,KAAKjO,YAAL,CAAkBC,IAAlB,EAAwB,IAAxB,CAAnB;AACH;;AACD,WAAO,IAAIkO,OAAJ,CAAY,CAACmB,OAAD,EAAUG,MAAV,KAAqB;AACpC;AACZ;AACA;AACY,YAAM0C,KAAK,GAAG,MAAM;AAChB;AACA,aAAKlE,aAAL,CAAmB,EAAnB;;AACA,YAAIuB,WAAJ,EAAiB;AACb,eAAKlM,KAAL,CAAW,KAAX,EAAkB,KAAlB;AACH,SAFD,MAGK;AACD;AACA,eAAKA,KAAL,CAAW,IAAX,EAAiB,KAAjB;AACH;;AACDgM,QAAAA,OAAO,CAAC7D,OAAD,CAAP;AACH,OAXD;;AAYA,YAAM2G,UAAU,GAAG,MAAM;AACrB,aAAKpF,SAAL,CAAe7S,GAAf,EAAoBJ,KAApB,EADqB,CAErB;;AACA,aAAK4S,KAAL,CAAW/K,IAAX,CAAgBzH,GAAhB;;AACA,YAAI,CAAC,KAAK6D,QAAV,EAAoB;AAChB;AACH;;AACD,aAAKA,QAAL,CAAc6L,IAAd;;AACA,YAAI,KAAK7L,QAAL,CAAcI,mBAAlB,EAAuC;AACnC,eAAKJ,QAAL,CAAc8K,IAAd;AACH;AACJ,OAXD;;AAYA,YAAMkG,MAAM,GAAG,KAAK/D,SAAL,CAAejJ,IAAf,CAAoBgN,MAAnC;AACA,YAAM7B,UAAU,GAAG,KAAKA,UAAL,CAAgBhT,GAAhB,EAAqBiT,gBAArB,CAAnB;;AACA,YAAMP,iBAAiB,GAAG,MAAM;AAC5B,aAAKA,iBAAL,CAAuBjL,IAAvB,CAA4BzH,GAA5B;AACA,eAAOsV,MAAM,EAAb;AACH,OAHD;;AAIA,UAAIT,MAAM,KAAK,OAAX,IAAsB7B,UAA1B,EAAsC;AAClCiF,QAAAA,UAAU;AACV,eAAOD,KAAK,EAAZ;AACH;;AACD,UAAInD,MAAM,KAAK,SAAX,IAAwB,CAAC7B,UAA7B,EAAyC;AACrCgF,QAAAA,KAAK;AACL,eAAOtF,iBAAiB,EAAxB;AACH;;AACD,UAAImC,MAAM,KAAK,SAAf,EAA0B;AACtB,cAAMqD,aAAa,GAAG,KAAKpH,SAAL,CAAejJ,IAAf,CAAoB+M,aAA1C;AACA,eAAOsD,aAAa,CACf/H,IADE,CACGvT,QAAQ,CAACub,YAAY,IAAIA,YAAY,KAAK,SAAlC,CADX,EACyDpb,OAAO,EADhE,EAEFqT,SAFE,CAES+H,YAAD,IAAkB;AAC7B,cAAIA,YAAY,KAAK,OAAjB,IAA4BnF,UAAhC,EAA4C;AACxCiF,YAAAA,UAAU;AACV,mBAAOD,KAAK,EAAZ;AACH,WAHD,MAIK;AACDA,YAAAA,KAAK;AACL,mBAAOtF,iBAAiB,EAAxB;AACH;AACJ,SAXM,CAAP;AAYH;AACJ,KAzDM,CAAP;AA0DH;AACD;AACJ;AACA;;;AACI2B,EAAAA,0BAA0B,GAAG;AACzB,UAAM+D,gBAAgB,GAAG,KAAK1W,iBAAL,CAAuBmN,MAAvB,GAAgC,CAAhC,IAAqC,KAAKpN,aAAL,CAAmBoN,MAAnB,GAA4B,CAA1F;;AACA,UAAMiH,QAAQ,GAAInM,MAAD,IAAY;AACzB,YAAM0O,UAAU,GAAG,KAAK3W,iBAAL,CAAuBkD,OAAvB,CAA+B+E,MAAM,CAAC6C,OAAtC,KAAkD,CAArE;AACA,YAAM8L,MAAM,GAAG,KAAK7W,aAAL,CAAmBmD,OAAnB,CAA2B+E,MAAM,CAACtI,GAAlC,KAA0C,CAAzD,CAFyB,CAGzB;;AACA,YAAMkX,eAAe,GAAG5O,MAAM,CAAC6C,OAAP,KAAmB,GAA3C;;AACA,UAAI6L,UAAU,IAAKC,MAAM,IAAI,CAACC,eAA9B,EAAgD;AAC5C5O,QAAAA,MAAM,CAACG,cAAP;AACA,aAAK5I,iBAAL,CAAuB,KAAvB,EAA8B,KAAKmQ,SAAnC,EACKvB,KADL,CACW,MAAM,CAAG,CADpB;AAEH;AACJ,KAVD;;AAWAzJ,IAAAA,MAAM,CAAC0P,IAAP,CAAY,IAAZ,EAAkBpX,OAAlB,EAA2BmX,QAA3B,EAAqCsC,gBAArC;AACH;AACD;AACJ;AACA;;;AACIhE,EAAAA,sBAAsB,GAAG;AACrB,UAAM0B,QAAQ,GAAInM,MAAD,IAAY;AACzB,YAAM6O,YAAY,GAAG7O,MAAM,CAAC6C,OAAP,KAAmB,EAAnB,IAAyB7C,MAAM,CAAC6C,OAAP,KAAmB,CAAjE;;AACA,UAAIgM,YAAY,IACZ,CAAC,KAAKnH,SADN,IAEA,KAAKhM,KAAL,CAAWwJ,MAFf,EAEuB;AACnB,aAAK4C,IAAL,CAAUoG,IAAV,CAAe3L,MAAf,CAAsB6J,IAAtB,CAA2B,KAAKtE,IAAL,CAAUoG,IAArC;AACH;AACJ,KAPD,CADqB,CASrB;;;AACAxR,IAAAA,MAAM,CAAC0P,IAAP,CAAY,IAAZ,EAAkBpX,OAAlB,EAA2BmX,QAA3B;AACH;AACD;AACJ;AACA;;;AACIxB,EAAAA,0BAA0B,GAAG;AACzB,SAAKxD,SAAL,CAAe5J,SAAf,CAAyBkJ,SAAzB,CAAmCrQ,KAAK,IAAI;AACxC,UAAIA,KAAK,CAACsB,GAAN,KAAc,WAAd,IAA6B,KAAKgQ,SAAL,CAAerT,IAAf,OAA0B,EAA3D,EAA+D;AAC3D+B,QAAAA,KAAK,CAAC+J,cAAN;AACH;AACJ,KAJD;AAKH;AACD;AACJ;AACA;;;AACI4K,EAAAA,oBAAoB,GAAG;AACnB,UAAMjM,KAAK,GAAG,KAAKqI,SAAL,CAAerI,KAAf,CAAqBC,aAAnC,CADmB,CAEnB;;AACA,SAAK+C,QAAL,CAAcpF,MAAd,CAAqBoC,KAArB,EAA4B,OAA5B,EAAsC1I,KAAD,IAAW;AAC5C,WAAKyT,eAAL,CAAqBzT,KAArB;AACAA,MAAAA,KAAK,CAAC+J,cAAN;AACA,aAAO,IAAP;AACH,KAJD;AAKH;AACD;AACJ;AACA;;;AACI0K,EAAAA,yBAAyB,GAAG;AACxB,SAAK1D,SAAL,CAAejJ,IAAf,CACK4Q,YADL,CAEKtI,IAFL,CAEUlT,cAAc,CAAC,KAAKmF,oBAAN,CAFxB,EAGKgO,SAHL,CAGgBtS,KAAD,IAAW;AACtB,WAAK6S,YAAL,CAAkBlJ,IAAlB,CAAuB3J,KAAK,CAACgI,IAA7B;AACH,KALD;AAMH;AACD;AACJ;AACA;;;AACI2O,EAAAA,qBAAqB,GAAG;AACpB,UAAMiE,QAAQ,GAAG,MAAM;AACnB,YAAMtJ,SAAS,GAAG,KAAKvL,QAAL,IAAiB,KAAKA,QAAL,CAAcuL,SAAjD;AACA,aAAO,CAACA,SAAD,IAAc,CAAC,CAAC,KAAKiC,SAA5B;AACH,KAHD;;AAIA,SAAKP,SAAL,CACK/J,MADL,CAEKoJ,IAFL,CAEUlT,cAAc,CAAC,GAAD,CAFxB,EAE+BL,QAAQ,CAAC8b,QAAD,CAFvC,EAGKtI,SAHL,CAGe,MAAM;AACjB,YAAM4H,KAAK,GAAG,MAAM,KAAKlE,aAAL,CAAmB,EAAnB,CAApB;;AACA,UAAI,KAAKrR,SAAT,EAAoB;AAChB,eAAO,KACFvB,iBADE,CACgB,KADhB,EACuB,KAAKmQ,SAD5B,EACuCxR,SADvC,EACkD,IADlD,EAEFqU,IAFE,CAEG8D,KAFH,EAGFlI,KAHE,CAGIkI,KAHJ,CAAP;AAIH;;AACDA,MAAAA,KAAK;AACR,KAZD;AAaH;AACD;AACJ;AACA;AACA;AACA;;;AACI7E,EAAAA,QAAQ,CAACnT,GAAD,EAAMqT,kBAAN,EAA0B;AAC9B,UAAM5P,UAAU,GAAG4P,kBAAkB,GAAG,KAAKxP,QAAL,CAAcJ,UAAjB,GAA8B,KAAKA,UAAxE;AACA,UAAMkV,EAAE,GAAG3Y,GAAG,CAACyD,UAAD,CAAd;AACA,WAAO,KAAK4B,KAAL,CAAW+N,IAAX,CAAgBtN,IAAI,IAAI,KAAKD,YAAL,CAAkBC,IAAlB,MAA4B6S,EAApD,CAAP;AACH;AACD;AACJ;AACA;;;AACIzD,EAAAA,oBAAoB,GAAG;AACnB,SAAK0D,iBAAL,GAAyB;AACrB9a,MAAAA,KAAK,EAAE,IADc;AAErB+a,MAAAA,MAAM,EAAE,EAAE,GAAG,KAAKnV;AAAV;AAFa,KAAzB;AAIH;;AAl2B4C;;AAo2BjD+K,iBAAiB,CAACrQ,IAAlB;AAAA,mBAA8GqQ,iBAA9G,EA1jEgG3T,EA0jEhG,mBAAiJA,EAAE,CAACmT,SAApJ,GA1jEgGnT,EA0jEhG,mBAA0K0E,YAA1K;AAAA;;AACAiP,iBAAiB,CAAC1E,IAAlB,kBA3jEgGjP,EA2jEhG;AAAA,QAAkG2T,iBAAlG;AAAA;AAAA;AAAA;AA3jEgG3T,MAAAA,EA2jEhG,0BAAu8CyT,gBAAv8C;AA3jEgGzT,MAAAA,EA2jEhG,0BAAshDW,WAAthD;AAAA;;AAAA;AAAA;;AA3jEgGX,MAAAA,EA2jEhG,qBA3jEgGA,EA2jEhG;AA3jEgGA,MAAAA,EA2jEhG,qBA3jEgGA,EA2jEhG;AAAA;AAAA;AAAA;AAAA;AA3jEgGA,MAAAA,EA2jEhG,aAAymD+L,YAAzmD;AA3jEgG/L,MAAAA,EA2jEhG,aAA+qDyQ,YAA/qD;AAAA;;AAAA;AAAA;;AA3jEgGzQ,MAAAA,EA2jEhG,qBA3jEgGA,EA2jEhG;AA3jEgGA,MAAAA,EA2jEhG,qBA3jEgGA,EA2jEhG;AAAA;AAAA;AAAA;AAAA;AAAA;AA3jEgGA,MAAAA,EA2jEhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA3jEgGA,EA2jEhG,oBAAs3C,CAACqX,eAAD,CAAt3C,GA3jEgGrX,EA2jEhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA3jEgGA,MAAAA,EA2jEhG;AA3jEgGA,MAAAA,EA2jEmqD,4BAAnwD;AA3jEgGA,MAAAA,EA2jEmuD;AAAA,eAAU,UAAM,IAAN,EAAY,KAAZ,CAAV;AAAA;AAAA,8BAAoF,yBAAqB,SAArB,CAApF,GAAsH,SAAtH;AAAA;AAAA,8BAAgK,sBAAhK,GAAqL,SAArL;AAAA;AAAA,8BAA8N,sBAA9N,GAAmP,SAAnP;AAAA;AAAA,8BAA2R,4BAA3R,GAAsT,SAAtT;AAAA,QAAn0D;AA3jEgGA,MAAAA,EA2jEw0E,4BAAx6E;AA3jEgGA,MAAAA,EA2jEo3E,iEAAp9E;AA3jEgGA,MAAAA,EA2jEypH,uCAAzvH;AA3jEgGA,MAAAA,EA2jEsrH;AAAA,eAAa,kBAAb;AAAA;AAAA,eAAsD,UAAtD;AAAA;AAAA,8BAAiG,mBAAjG,GAAmH,SAAnH;AAAA;AAAA,eAA0J,eAAW,SAAX,SAA1J;AAAA;AAAA,eAAmN,eAAW,OAAX,SAAnN;AAAA,QAAtxH;AA3jEgGA,MAAAA,EA2jE80I,eAA96I;AA3jEgGA,MAAAA,EA2jEq2I,eAAr8I;AA3jEgGA,MAAAA,EA2jEm3I,gEAAn9I;AA3jEgGA,MAAAA,EA2jEhG;AA3jEgGA,MAAAA,EA2jEu9I,eAAvjJ;AA3jEgGA,MAAAA,EA2jEk/I,gEAAllJ;AA3jEgGA,MAAAA,EA2jEytJ,gBAAzzJ;AAAA;;AAAA;AA3jEgGA,MAAAA,EA2jE0iE,qOAA1oE;AA3jEgGA,MAAAA,EA2jE6qD,iCAA7wD;AA3jEgGA,MAAAA,EA2jEuwD,4BAAv2D;AA3jEgGA,MAAAA,EA2jEw5E,aAAx/E;AA3jEgGA,MAAAA,EA2jEw5E,8DAAx/E;AA3jEgGA,MAAAA,EA2jEo7H,aAAphI;AA3jEgGA,MAAAA,EA2jEo7H,mUAAphI;AA3jEgGA,MAAAA,EA2jEk6I,aAAlgJ;AA3jEgGA,MAAAA,EA2jEk6I,oBA3jEl6IA,EA2jEk6I,+CAAlgJ;AA3jEgGA,MAAAA,EA2jE6/I,aAA7lJ;AA3jEgGA,MAAAA,EA2jE6/I,oCAA7lJ;AAAA;AAAA;AAAA,eAAwkawB,EAAE,CAACiO,OAA3ka,EAAmpajO,EAAE,CAACyV,OAAtpa,EAAuzZlL,YAAvzZ,EAA6vavK,EAAE,CAAC+R,IAAhwa,EAAmkZ9C,YAAnkZ;AAAA,UAAy1ajP,EAAE,CAACwc,SAA51a;AAAA;AAAA;AAAA,eAAq3a/N;AAAr3a;AAAA;;AACA;AAAA,qDA5jEgGjQ,EA4jEhG,mBAA2F2T,iBAA3F,EAA0H,CAAC;AAC/GnQ,IAAAA,IAAI,EAAElD,SADyG;AAE/GmD,IAAAA,IAAI,EAAE,CAAC;AAAEiM,MAAAA,QAAQ,EAAE,WAAZ;AAAyBuO,MAAAA,SAAS,EAAE,CAAC5G,eAAD,CAApC;AAAuDpH,MAAAA,UAAU,EAAEA,UAAnE;AAA+EN,MAAAA,QAAQ,EAAE,mlGAAzF;AAA8qGC,MAAAA,MAAM,EAAE,CAAC,2sPAAD;AAAtrG,KAAD;AAFyG,GAAD,CAA1H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEpM,MAAAA,IAAI,EAAExD,EAAE,CAACmT;AAAX,KAAD,EAAyB;AAAE3P,MAAAA,IAAI,EAAEkB;AAAR,KAAzB,CAAP;AAA0D,GAHpG,EAGsH;AAAEiC,IAAAA,aAAa,EAAE,CAAC;AACxHnD,MAAAA,IAAI,EAAEpD;AADkH,KAAD,CAAjB;AAEtGwG,IAAAA,iBAAiB,EAAE,CAAC;AACpBpD,MAAAA,IAAI,EAAEpD;AADc,KAAD,CAFmF;AAItG2G,IAAAA,WAAW,EAAE,CAAC;AACdvD,MAAAA,IAAI,EAAEpD;AADQ,KAAD,CAJyF;AAMtG4G,IAAAA,oBAAoB,EAAE,CAAC;AACvBxD,MAAAA,IAAI,EAAEpD;AADiB,KAAD,CANgF;AAQtGyG,IAAAA,QAAQ,EAAE,CAAC;AACXrD,MAAAA,IAAI,EAAEpD;AADK,KAAD,CAR4F;AAUtG6G,IAAAA,UAAU,EAAE,CAAC;AACbzD,MAAAA,IAAI,EAAEpD;AADO,KAAD,CAV0F;AAYtG8G,IAAAA,eAAe,EAAE,CAAC;AAClB1D,MAAAA,IAAI,EAAEpD;AADY,KAAD,CAZqF;AActG+G,IAAAA,oBAAoB,EAAE,CAAC;AACvB3D,MAAAA,IAAI,EAAEpD;AADiB,KAAD,CAdgF;AAgBtGgH,IAAAA,aAAa,EAAE,CAAC;AAChB5D,MAAAA,IAAI,EAAEpD;AADU,KAAD,CAhBuF;AAkBtGiH,IAAAA,KAAK,EAAE,CAAC;AACR7D,MAAAA,IAAI,EAAEpD;AADE,KAAD,CAlB+F;AAoBtGkH,IAAAA,oBAAoB,EAAE,CAAC;AACvB9D,MAAAA,IAAI,EAAEpD;AADiB,KAAD,CApBgF;AAsBtGmH,IAAAA,OAAO,EAAE,CAAC;AACV/D,MAAAA,IAAI,EAAEpD;AADI,KAAD,CAtB6F;AAwBtGoH,IAAAA,UAAU,EAAE,CAAC;AACbhE,MAAAA,IAAI,EAAEpD;AADO,KAAD,CAxB0F;AA0BtGqH,IAAAA,WAAW,EAAE,CAAC;AACdjE,MAAAA,IAAI,EAAEpD;AADQ,KAAD,CA1ByF;AA4BtGsH,IAAAA,QAAQ,EAAE,CAAC;AACXlE,MAAAA,IAAI,EAAEpD;AADK,KAAD,CA5B4F;AA8BtGuH,IAAAA,SAAS,EAAE,CAAC;AACZnE,MAAAA,IAAI,EAAEpD;AADM,KAAD,CA9B2F;AAgCtGwH,IAAAA,UAAU,EAAE,CAAC;AACbpE,MAAAA,IAAI,EAAEpD;AADO,KAAD,CAhC0F;AAkCtGyH,IAAAA,iBAAiB,EAAE,CAAC;AACpBrE,MAAAA,IAAI,EAAEpD;AADc,KAAD,CAlCmF;AAoCtG0H,IAAAA,WAAW,EAAE,CAAC;AACdtE,MAAAA,IAAI,EAAEpD;AADQ,KAAD,CApCyF;AAsCtG2H,IAAAA,SAAS,EAAE,CAAC;AACZvE,MAAAA,IAAI,EAAEpD;AADM,KAAD,CAtC2F;AAwCtG4H,IAAAA,QAAQ,EAAE,CAAC;AACXxE,MAAAA,IAAI,EAAEpD;AADK,KAAD,CAxC4F;AA0CtG6H,IAAAA,UAAU,EAAE,CAAC;AACbzE,MAAAA,IAAI,EAAEpD;AADO,KAAD,CA1C0F;AA4CtG8H,IAAAA,cAAc,EAAE,CAAC;AACjB1E,MAAAA,IAAI,EAAEpD;AADW,KAAD,CA5CsF;AA8CtG+H,IAAAA,QAAQ,EAAE,CAAC;AACX3E,MAAAA,IAAI,EAAEpD;AADK,KAAD,CA9C4F;AAgDtGoM,IAAAA,SAAS,EAAE,CAAC;AACZhJ,MAAAA,IAAI,EAAEpD;AADM,KAAD,CAhD2F;AAkDtGgI,IAAAA,MAAM,EAAE,CAAC;AACT5E,MAAAA,IAAI,EAAEpD;AADG,KAAD,CAlD8F;AAoDtGkM,IAAAA,QAAQ,EAAE,CAAC;AACX9I,MAAAA,IAAI,EAAEpD;AADK,KAAD,CApD4F;AAsDtGkI,IAAAA,OAAO,EAAE,CAAC;AACV9E,MAAAA,IAAI,EAAEpD;AADI,KAAD,CAtD6F;AAwDtGmI,IAAAA,QAAQ,EAAE,CAAC;AACX/E,MAAAA,IAAI,EAAEpD;AADK,KAAD,CAxD4F;AA0DtGoI,IAAAA,UAAU,EAAE,CAAC;AACbhF,MAAAA,IAAI,EAAEpD;AADO,KAAD,CA1D0F;AA4DtGqI,IAAAA,QAAQ,EAAE,CAAC;AACXjF,MAAAA,IAAI,EAAEpD;AADK,KAAD,CA5D4F;AA8DtGwI,IAAAA,iBAAiB,EAAE,CAAC;AACpBpF,MAAAA,IAAI,EAAEpD;AADc,KAAD,CA9DmF;AAgEtGsX,IAAAA,KAAK,EAAE,CAAC;AACRlU,MAAAA,IAAI,EAAEjD;AADE,KAAD,CAhE+F;AAkEtGuQ,IAAAA,QAAQ,EAAE,CAAC;AACXtN,MAAAA,IAAI,EAAEjD;AADK,KAAD,CAlE4F;AAoEtGsQ,IAAAA,QAAQ,EAAE,CAAC;AACXrN,MAAAA,IAAI,EAAEjD;AADK,KAAD,CApE4F;AAsEtG2L,IAAAA,OAAO,EAAE,CAAC;AACV1I,MAAAA,IAAI,EAAEjD;AADI,KAAD,CAtE6F;AAwEtG0L,IAAAA,MAAM,EAAE,CAAC;AACTzI,MAAAA,IAAI,EAAEjD;AADG,KAAD,CAxE8F;AA0EtGsV,IAAAA,YAAY,EAAE,CAAC;AACfrS,MAAAA,IAAI,EAAEjD;AADS,KAAD,CA1EwF;AA4EtGoX,IAAAA,OAAO,EAAE,CAAC;AACVnU,MAAAA,IAAI,EAAEjD;AADI,KAAD,CA5E6F;AA8EtGqX,IAAAA,iBAAiB,EAAE,CAAC;AACpBpU,MAAAA,IAAI,EAAEjD;AADc,KAAD,CA9EmF;AAgFtGwQ,IAAAA,WAAW,EAAE,CAAC;AACdvN,MAAAA,IAAI,EAAEjD;AADQ,KAAD,CAhFyF;AAkFtGwI,IAAAA,QAAQ,EAAE,CAAC;AACXvF,MAAAA,IAAI,EAAE1C,YADK;AAEX2C,MAAAA,IAAI,EAAE,CAACgQ,gBAAD;AAFK,KAAD,CAlF4F;AAqFtG2D,IAAAA,SAAS,EAAE,CAAC;AACZ5T,MAAAA,IAAI,EAAE5C,eADM;AAEZ6C,MAAAA,IAAI,EAAE,CAAC9C,WAAD,EAAc;AAAEud,QAAAA,WAAW,EAAE;AAAf,OAAd;AAFM,KAAD,CArF2F;AAwFtGlI,IAAAA,SAAS,EAAE,CAAC;AACZxS,MAAAA,IAAI,EAAEhD,SADM;AAEZiD,MAAAA,IAAI,EAAE,CAACsI,YAAD;AAFM,KAAD,CAxF2F;AA2FtG4K,IAAAA,IAAI,EAAE,CAAC;AACPnT,MAAAA,IAAI,EAAEzC,YADC;AAEP0C,MAAAA,IAAI,EAAE,CAACgN,YAAD;AAFC,KAAD,CA3FgG;AA8FtGpE,IAAAA,eAAe,EAAE,CAAC;AAClB7I,MAAAA,IAAI,EAAEjD;AADY,KAAD,CA9FqF;AAgGtG8Y,IAAAA,YAAY,EAAE,CAAC;AACf7V,MAAAA,IAAI,EAAE/C,WADS;AAEfgD,MAAAA,IAAI,EAAE,CAAC,eAAD;AAFS,KAAD;AAhGwF,GAHtH;AAAA;;AAwGA,MAAM0a,eAAe,GAAG,IAAIpU,eAAJ,EAAxB;;AACA,MAAMqU,cAAN,CAAqB;AACjB;AACJ;AACA;AACA;AACuB,SAAZC,YAAY,CAACpU,OAAD,EAAU;AACzBkU,IAAAA,eAAe,CAACnU,UAAhB,CAA2BC,OAA3B;AACH;;AAPgB;;AASrBmU,cAAc,CAAC9a,IAAf;AAAA,mBAA2G8a,cAA3G;AAAA;;AACAA,cAAc,CAACE,IAAf,kBA/qEgGte,EA+qEhG;AAAA,QAA4Goe;AAA5G;AAgBAA,cAAc,CAACG,IAAf,kBA/rEgGve,EA+rEhG;AAAA,aAAuI,CAC/H0E,YAD+H,EAE/H;AAAE4S,IAAAA,OAAO,EAAE/V,uBAAX;AAAoCid,IAAAA,QAAQ,EAAE;AAA9C,GAF+H,CAAvI;AAAA,YAGiB,CACL/c,YADK,EAELJ,mBAFK,EAGLC,WAHK,EAILM,iBAJK,CAHjB;AAAA;;AASA;AAAA,qDAxsEgG5B,EAwsEhG,mBAA2Foe,cAA3F,EAAuH,CAAC;AAC5G5a,IAAAA,IAAI,EAAExC,QADsG;AAE5GyC,IAAAA,IAAI,EAAE,CAAC;AACCgb,MAAAA,OAAO,EAAE,CACLhd,YADK,EAELJ,mBAFK,EAGLC,WAHK,EAILM,iBAJK,CADV;AAOC8c,MAAAA,YAAY,EAAE,CACV/K,iBADU,EAEVzD,mBAFU,EAGVnE,YAHU,EAIV0E,YAJU,EAKV3N,aALU,EAMV2Q,gBANU,EAOV5D,SAPU,CAPf;AAgBC8O,MAAAA,OAAO,EAAE,CACLhL,iBADK,EAELzD,mBAFK,EAGLnE,YAHK,EAIL0E,YAJK,EAKL3N,aALK,EAML2Q,gBANK,EAOL5D,SAPK,CAhBV;AAyBCoO,MAAAA,SAAS,EAAE,CACPvZ,YADO,EAEP;AAAE4S,QAAAA,OAAO,EAAE/V,uBAAX;AAAoCid,QAAAA,QAAQ,EAAE;AAA9C,OAFO;AAzBZ,KAAD;AAFsG,GAAD,CAAvH;AAAA;AAkCA;AACA;AACA;;;AAEA,SAAStO,mBAAT,EAA8BpN,aAA9B,EAA6C2N,YAA7C,EAA2DkD,iBAA3D,EAA8EF,gBAA9E,EAAgG1H,YAAhG,EAA8GqS,cAA9G,EAA8HvO,SAA9H", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pipe, Injectable, Directive, Input, EventEmitter, Component, Output, ViewChild, HostBinding, HostListener, TemplateRef, ContentChildren, forwardRef, ContentChild, ViewChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/forms';\nimport { FormControl, FormGroup, NG_VALUE_ACCESSOR, ReactiveFormsModule, FormsModule, COMPOSITION_BUFFER_MODE } from '@angular/forms';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1$1 from 'ng2-material-dropdown';\nimport { Ng2Dropdown, Ng2DropdownModule } from 'ng2-material-dropdown';\nimport { filter as filter$1, map, first as first$1, debounceTime as debounceTime$1 } from 'rxjs';\nimport { trigger, state, style, transition, animate, keyframes } from '@angular/animations';\nimport { first, distinctUntilChanged, debounceTime, filter } from 'rxjs/operators';\n\nconst escape = s => s.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&');\nclass HighlightPipe {\n    /**\n     * @name transform\n     * @param value {string}\n     * @param arg {string}\n     */\n    transform(value, arg) {\n        if (!arg.trim()) {\n            return value;\n        }\n        try {\n            const regex = new RegExp(`(${escape(arg)})`, 'i');\n            return value.replace(regex, '<b>$1</b>');\n        }\n        catch (e) {\n            return value;\n        }\n    }\n}\nHighlightPipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HighlightPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe });\nHighlightPipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HighlightPipe, name: \"highlight\" });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: HighlightPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'highlight'\n                }]\n        }] });\n\n/*\n** constants and default values for <tag-input>\n */\nconst PLACEHOLDER = '+ Tag';\nconst SECONDARY_PLACEHOLDER = 'Enter a new tag';\nconst KEYDOWN = 'keydown';\nconst KEYUP = 'keyup';\nconst FOCUS = 'focus';\nconst MAX_ITEMS_WARNING = 'The number of items specified was greater than the property max-items.';\nconst ACTIONS_KEYS = {\n    DELETE: 'DELETE',\n    SWITCH_PREV: 'SWITCH_PREV',\n    SWITCH_NEXT: 'SWITCH_NEXT',\n    TAB: 'TAB'\n};\nconst KEY_PRESS_ACTIONS = {\n    8: ACTIONS_KEYS.DELETE,\n    46: ACTIONS_KEYS.DELETE,\n    37: ACTIONS_KEYS.SWITCH_PREV,\n    39: ACTIONS_KEYS.SWITCH_NEXT,\n    9: ACTIONS_KEYS.TAB\n};\nconst DRAG_AND_DROP_KEY = 'Text';\nconst NEXT = 'NEXT';\nconst PREV = 'PREV';\n\nclass DragProvider {\n    constructor() {\n        this.state = {\n            dragging: false,\n            dropping: false,\n            index: undefined\n        };\n    }\n    /**\n     * @name setDraggedItem\n     * @param event\n     * @param tag\n     */\n    setDraggedItem(event, tag) {\n        if (event && event.dataTransfer) {\n            event.dataTransfer.setData(DRAG_AND_DROP_KEY, JSON.stringify(tag));\n        }\n    }\n    /**\n     * @name getDraggedItem\n     * @param event\n     */\n    getDraggedItem(event) {\n        if (event && event.dataTransfer) {\n            const data = event.dataTransfer.getData(DRAG_AND_DROP_KEY);\n            try {\n                return JSON.parse(data);\n            }\n            catch {\n                return;\n            }\n        }\n    }\n    /**\n     * @name setSender\n     * @param sender\n     */\n    setSender(sender) {\n        this.sender = sender;\n    }\n    /**\n     * @name setReceiver\n     * @param receiver\n     */\n    setReceiver(receiver) {\n        this.receiver = receiver;\n    }\n    /**\n     * @name onTagDropped\n     * @param tag\n     * @param indexDragged\n     * @param indexDropped\n     */\n    onTagDropped(tag, indexDragged, indexDropped) {\n        this.onDragEnd();\n        this.sender.onRemoveRequested(tag, indexDragged);\n        this.receiver.onAddingRequested(false, tag, indexDropped);\n    }\n    /**\n     * @name setState\n     * @param state\n     */\n    setState(state) {\n        this.state = { ...this.state, ...state };\n    }\n    /**\n     * @name getState\n     * @param key\n     */\n    getState(key) {\n        return key ? this.state[key] : this.state;\n    }\n    /**\n     * @name onDragEnd\n     */\n    onDragEnd() {\n        this.setState({\n            dragging: false,\n            dropping: false,\n            index: undefined\n        });\n    }\n}\nDragProvider.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DragProvider, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nDragProvider.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DragProvider });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DragProvider, decorators: [{\n            type: Injectable\n        }] });\n\nconst defaults = {\n    tagInput: {\n        separatorKeys: [],\n        separatorKeyCodes: [],\n        maxItems: Infinity,\n        placeholder: PLACEHOLDER,\n        secondaryPlaceholder: SECONDARY_PLACEHOLDER,\n        validators: [],\n        asyncValidators: [],\n        onlyFromAutocomplete: false,\n        errorMessages: {},\n        theme: '',\n        onTextChangeDebounce: 250,\n        inputId: null,\n        inputClass: '',\n        clearOnBlur: false,\n        hideForm: false,\n        addOnBlur: false,\n        addOnPaste: false,\n        pasteSplitPattern: ',',\n        blinkIfDupe: true,\n        removable: true,\n        editable: false,\n        allowDupes: false,\n        modelAsStrings: false,\n        trimTags: true,\n        ripple: true,\n        tabIndex: '',\n        disable: false,\n        dragZone: '',\n        onRemoving: undefined,\n        onAdding: undefined,\n        displayBy: 'display',\n        identifyBy: 'value',\n        animationDuration: {\n            enter: '250ms',\n            leave: '150ms'\n        }\n    },\n    dropdown: {\n        displayBy: 'display',\n        identifyBy: 'value',\n        appendToBody: true,\n        offset: '50 0',\n        focusFirstElement: false,\n        showDropdownIfEmpty: false,\n        minimumTextLength: 1,\n        limitItemsTo: Infinity,\n        keepOpen: true,\n        dynamicUpdate: true,\n        zIndex: 1000,\n        matchingFn\n    }\n};\n/**\n * @name matchingFn\n * @param this\n * @param value\n * @param target\n */\nfunction matchingFn(value, target) {\n    const targetValue = target[this.displayBy].toString();\n    return targetValue && targetValue\n        .toLowerCase()\n        .indexOf(value.toLowerCase()) >= 0;\n}\n\nclass OptionsProvider {\n    setOptions(options) {\n        OptionsProvider.defaults.tagInput = { ...defaults.tagInput, ...options.tagInput };\n        OptionsProvider.defaults.dropdown = { ...defaults.dropdown, ...options.dropdown };\n    }\n}\nOptionsProvider.defaults = defaults;\n\nfunction isObject(obj) {\n    return obj === Object(obj);\n}\nclass TagInputAccessor {\n    constructor() {\n        this._items = [];\n        /**\n         * @name displayBy\n         */\n        this.displayBy = OptionsProvider.defaults.tagInput.displayBy;\n        /**\n         * @name identifyBy\n         */\n        this.identifyBy = OptionsProvider.defaults.tagInput.identifyBy;\n    }\n    get items() {\n        return this._items;\n    }\n    set items(items) {\n        this._items = items;\n        this._onChangeCallback(this._items);\n    }\n    onTouched() {\n        this._onTouchedCallback();\n    }\n    writeValue(items) {\n        this._items = items || [];\n    }\n    registerOnChange(fn) {\n        this._onChangeCallback = fn;\n    }\n    registerOnTouched(fn) {\n        this._onTouchedCallback = fn;\n    }\n    /**\n     * @name getItemValue\n     * @param item\n     * @param fromDropdown\n     */\n    getItemValue(item, fromDropdown = false) {\n        const property = fromDropdown && this.dropdown ? this.dropdown.identifyBy : this.identifyBy;\n        return isObject(item) ? item[property] : item;\n    }\n    /**\n     * @name getItemDisplay\n     * @param item\n     * @param fromDropdown\n     */\n    getItemDisplay(item, fromDropdown = false) {\n        const property = fromDropdown && this.dropdown ? this.dropdown.displayBy : this.displayBy;\n        return isObject(item) ? item[property] : item;\n    }\n    /**\n     * @name getItemsWithout\n     * @param index\n     */\n    getItemsWithout(index) {\n        return this.items.filter((item, position) => position !== index);\n    }\n}\nTagInputAccessor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputAccessor, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nTagInputAccessor.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.3\", type: TagInputAccessor, inputs: { displayBy: \"displayBy\", identifyBy: \"identifyBy\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputAccessor, decorators: [{\n            type: Directive\n        }], propDecorators: { displayBy: [{\n                type: Input\n            }], identifyBy: [{\n                type: Input\n            }] } });\n\n/**\n * @name listen\n * @param listenerType\n * @param action\n * @param condition\n */\nfunction listen(listenerType, action, condition = true) {\n    // if the event provided does not exist, throw an error\n    if (!this.listeners.hasOwnProperty(listenerType)) {\n        throw new Error('The event entered may be wrong');\n    }\n    // if a condition is present and is false, exit early\n    if (!condition) {\n        return;\n    }\n    // fire listener\n    this.listeners[listenerType].push(action);\n}\n\nclass TagInputForm {\n    constructor() {\n        /**\n         * @name onSubmit\n         */\n        this.onSubmit = new EventEmitter();\n        /**\n         * @name onBlur\n         */\n        this.onBlur = new EventEmitter();\n        /**\n         * @name onFocus\n         */\n        this.onFocus = new EventEmitter();\n        /**\n         * @name onKeyup\n         */\n        this.onKeyup = new EventEmitter();\n        /**\n         * @name onKeydown\n         */\n        this.onKeydown = new EventEmitter();\n        /**\n         * @name inputTextChange\n         */\n        this.inputTextChange = new EventEmitter();\n        /**\n         * @name validators\n         */\n        this.validators = [];\n        /**\n         * @name asyncValidators\n         * @desc array of AsyncValidator that are used to validate the tag before it gets appended to the list\n         */\n        this.asyncValidators = [];\n        /**\n         * @name tabindex\n         * @desc pass through the specified tabindex to the input\n         */\n        this.tabindex = '';\n        /**\n         * @name disabled\n         */\n        this.disabled = false;\n        this.item = new FormControl({ value: '', disabled: this.disabled });\n    }\n    /**\n     * @name inputText\n     */\n    get inputText() {\n        return this.item.value;\n    }\n    /**\n     * @name inputText\n     * @param text {string}\n     */\n    set inputText(text) {\n        this.item.setValue(text);\n        this.inputTextChange.emit(text);\n    }\n    ngOnInit() {\n        this.item.setValidators(this.validators);\n        this.item.setAsyncValidators(this.asyncValidators);\n        // creating form\n        this.form = new FormGroup({\n            item: this.item\n        });\n    }\n    ngOnChanges(changes) {\n        if (changes.disabled && !changes.disabled.firstChange) {\n            if (changes.disabled.currentValue) {\n                this.form.controls['item'].disable();\n            }\n            else {\n                this.form.controls['item'].enable();\n            }\n        }\n    }\n    /**\n     * @name value\n     */\n    get value() {\n        return this.form.get('item');\n    }\n    /**\n     * @name isInputFocused\n     */\n    isInputFocused() {\n        const doc = typeof document !== 'undefined' ? document : undefined;\n        return doc ? doc.activeElement === this.input.nativeElement : false;\n    }\n    /**\n     * @name getErrorMessages\n     * @param messages\n     */\n    getErrorMessages(messages) {\n        return Object.keys(messages)\n            .filter(err => this.value.hasError(err))\n            .map(err => messages[err]);\n    }\n    /**\n     * @name hasErrors\n     */\n    hasErrors() {\n        const { dirty, value, valid } = this.form;\n        return dirty && value.item && !valid;\n    }\n    /**\n     * @name focus\n     */\n    focus() {\n        this.input.nativeElement.focus();\n    }\n    /**\n     * @name blur\n     */\n    blur() {\n        this.input.nativeElement.blur();\n    }\n    /**\n     * @name getElementPosition\n     */\n    getElementPosition() {\n        return this.input.nativeElement.getBoundingClientRect();\n    }\n    /**\n     * - removes input from the component\n     * @name destroy\n     */\n    destroy() {\n        const input = this.input.nativeElement;\n        input.parentElement.removeChild(input);\n    }\n    /**\n     * @name onKeyDown\n     * @param $event\n     */\n    onKeyDown($event) {\n        this.inputText = this.value.value;\n        if ($event.key === 'Enter') {\n            this.submit($event);\n        }\n        else {\n            return this.onKeydown.emit($event);\n        }\n    }\n    /**\n     * @name onKeyUp\n     * @param $event\n     */\n    onKeyUp($event) {\n        this.inputText = this.value.value;\n        return this.onKeyup.emit($event);\n    }\n    /**\n     * @name submit\n     */\n    submit($event) {\n        $event.preventDefault();\n        this.onSubmit.emit($event);\n    }\n}\nTagInputForm.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputForm, deps: [], target: i0.ɵɵFactoryTarget.Component });\nTagInputForm.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.3\", type: TagInputForm, selector: \"tag-input-form\", inputs: { placeholder: \"placeholder\", validators: \"validators\", asyncValidators: \"asyncValidators\", inputId: \"inputId\", inputClass: \"inputClass\", tabindex: \"tabindex\", disabled: \"disabled\", inputText: \"inputText\" }, outputs: { onSubmit: \"onSubmit\", onBlur: \"onBlur\", onFocus: \"onFocus\", onKeyup: \"onKeyup\", onKeydown: \"onKeydown\", inputTextChange: \"inputTextChange\" }, viewQueries: [{ propertyName: \"input\", first: true, predicate: [\"input\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: \"<!-- form -->\\n<form (ngSubmit)=\\\"submit($event)\\\" [formGroup]=\\\"form\\\">\\n    <input #input\\n\\n           type=\\\"text\\\"\\n           class=\\\"ng2-tag-input__text-input\\\"\\n           autocomplete=\\\"off\\\"\\n           tabindex=\\\"{{ disabled ? -1 : tabindex ? tabindex : 0 }}\\\"\\n           minlength=\\\"1\\\"\\n           formControlName=\\\"item\\\"\\n\\n           [ngClass]=\\\"inputClass\\\"\\n           [attr.id]=\\\"inputId\\\"\\n           [attr.placeholder]=\\\"placeholder\\\"\\n           [attr.aria-label]=\\\"placeholder\\\"\\n           [attr.tabindex]=\\\"tabindex\\\"\\n           [attr.disabled]=\\\"disabled ? disabled : null\\\"\\n\\n           (focus)=\\\"onFocus.emit($event)\\\"\\n           (blur)=\\\"onBlur.emit($event)\\\"\\n           (keydown)=\\\"onKeyDown($event)\\\"\\n           (keyup)=\\\"onKeyUp($event)\\\"\\n    />\\n</form>\\n\", styles: [\".dark tag:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info input{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info .form-control input::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info .form-control input:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info .form-control input::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info input:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading{border:none}.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.ng2-tag-input form{margin:.1em 0}.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.dark.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input form{margin:.1em 0}.bootstrap3-info.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.error-message{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap .error-message{color:#d9534f}.ng2-tag-input__text-input{display:inline;vertical-align:middle;border:none;padding:0 .5rem;height:38px;font-size:1em;font-family:Roboto,Helvetica Neue,sans-serif}.ng2-tag-input__text-input:focus{outline:0}.ng2-tag-input__text-input[disabled=true]{opacity:.5;background:#fff}\\n\"], directives: [{ type: i1.ɵNgNoValidate, selector: \"form:not([ngNoForm]):not([ngNativeValidate])\" }, { type: i1.NgControlStatusGroup, selector: \"[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]\" }, { type: i1.FormGroupDirective, selector: \"[formGroup]\", inputs: [\"formGroup\"], outputs: [\"ngSubmit\"], exportAs: [\"ngForm\"] }, { type: i1.DefaultValueAccessor, selector: \"input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]\" }, { type: i1.MinLengthValidator, selector: \"[minlength][formControlName],[minlength][formControl],[minlength][ngModel]\", inputs: [\"minlength\"] }, { type: i1.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { type: i1.FormControlName, selector: \"[formControlName]\", inputs: [\"formControlName\", \"disabled\", \"ngModel\"], outputs: [\"ngModelChange\"] }, { type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputForm, decorators: [{\n            type: Component,\n            args: [{ selector: 'tag-input-form', template: \"<!-- form -->\\n<form (ngSubmit)=\\\"submit($event)\\\" [formGroup]=\\\"form\\\">\\n    <input #input\\n\\n           type=\\\"text\\\"\\n           class=\\\"ng2-tag-input__text-input\\\"\\n           autocomplete=\\\"off\\\"\\n           tabindex=\\\"{{ disabled ? -1 : tabindex ? tabindex : 0 }}\\\"\\n           minlength=\\\"1\\\"\\n           formControlName=\\\"item\\\"\\n\\n           [ngClass]=\\\"inputClass\\\"\\n           [attr.id]=\\\"inputId\\\"\\n           [attr.placeholder]=\\\"placeholder\\\"\\n           [attr.aria-label]=\\\"placeholder\\\"\\n           [attr.tabindex]=\\\"tabindex\\\"\\n           [attr.disabled]=\\\"disabled ? disabled : null\\\"\\n\\n           (focus)=\\\"onFocus.emit($event)\\\"\\n           (blur)=\\\"onBlur.emit($event)\\\"\\n           (keydown)=\\\"onKeyDown($event)\\\"\\n           (keyup)=\\\"onKeyUp($event)\\\"\\n    />\\n</form>\\n\", styles: [\".dark tag:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info input{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info .form-control input::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info .form-control input:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info .form-control input::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info input:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading{border:none}.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.ng2-tag-input form{margin:.1em 0}.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.dark.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input form{margin:.1em 0}.bootstrap3-info.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.error-message{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap .error-message{color:#d9534f}.ng2-tag-input__text-input{display:inline;vertical-align:middle;border:none;padding:0 .5rem;height:38px;font-size:1em;font-family:Roboto,Helvetica Neue,sans-serif}.ng2-tag-input__text-input:focus{outline:0}.ng2-tag-input__text-input[disabled=true]{opacity:.5;background:#fff}\\n\"] }]\n        }], propDecorators: { onSubmit: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], onFocus: [{\n                type: Output\n            }], onKeyup: [{\n                type: Output\n            }], onKeydown: [{\n                type: Output\n            }], inputTextChange: [{\n                type: Output\n            }], placeholder: [{\n                type: Input\n            }], validators: [{\n                type: Input\n            }], asyncValidators: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], inputClass: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], input: [{\n                type: ViewChild,\n                args: ['input']\n            }], inputText: [{\n                type: Input\n            }] } });\n\nclass TagRipple {\n    constructor() {\n        this.state = 'none';\n    }\n}\nTagRipple.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagRipple, deps: [], target: i0.ɵɵFactoryTarget.Component });\nTagRipple.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.3\", type: TagRipple, selector: \"tag-ripple\", inputs: { state: \"state\" }, ngImport: i0, template: `\n        <div class=\"tag-ripple\" [@ink]=\"state\"></div>\n    `, isInline: true, styles: [\":host{width:100%;height:100%;left:0;overflow:hidden;position:absolute}.tag-ripple{background:rgba(0,0,0,.1);top:50%;left:50%;height:100%;transform:translate(-50%,-50%);position:absolute}\\n\"], animations: [\n        trigger('ink', [\n            state('none', style({ width: 0, opacity: 0 })),\n            transition('none => clicked', [\n                animate(300, keyframes([\n                    style({ opacity: 1, offset: 0, width: '30%', borderRadius: '100%' }),\n                    style({ opacity: 1, offset: 0.5, width: '50%' }),\n                    style({ opacity: 0.5, offset: 1, width: '100%', borderRadius: '16px' })\n                ]))\n            ])\n        ])\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagRipple, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'tag-ripple',\n                    styles: [`\n        :host {\n            width: 100%;\n            height: 100%;\n            left: 0;\n            overflow: hidden;\n            position: absolute;\n        }\n\n        .tag-ripple {\n            background: rgba(0, 0, 0, 0.1);\n            top: 50%;\n            left: 50%;\n            height: 100%;\n            transform: translate(-50%, -50%);\n            position: absolute;\n        }\n    `],\n                    template: `\n        <div class=\"tag-ripple\" [@ink]=\"state\"></div>\n    `,\n                    animations: [\n                        trigger('ink', [\n                            state('none', style({ width: 0, opacity: 0 })),\n                            transition('none => clicked', [\n                                animate(300, keyframes([\n                                    style({ opacity: 1, offset: 0, width: '30%', borderRadius: '100%' }),\n                                    style({ opacity: 1, offset: 0.5, width: '50%' }),\n                                    style({ opacity: 0.5, offset: 1, width: '100%', borderRadius: '16px' })\n                                ]))\n                            ])\n                        ])\n                    ]\n                }]\n        }], propDecorators: { state: [{\n                type: Input\n            }] } });\n\nclass DeleteIconComponent {\n}\nDeleteIconComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DeleteIconComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\nDeleteIconComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.3\", type: DeleteIconComponent, selector: \"delete-icon\", ngImport: i0, template: \"<span>\\n    <svg\\n        height=\\\"16px\\\"\\n        viewBox=\\\"0 0 32 32\\\"\\n        width=\\\"16px\\\"\\n    >\\n        <path\\n            d=\\\"M17.459,16.014l8.239-8.194c0.395-0.391,0.395-1.024,0-1.414c-0.394-0.391-1.034-0.391-1.428,0  l-8.232,8.187L7.73,6.284c-0.394-0.395-1.034-0.395-1.428,0c-0.394,0.396-0.394,1.037,0,1.432l8.302,8.303l-8.332,8.286  c-0.394,0.391-0.394,1.024,0,1.414c0.394,0.391,1.034,0.391,1.428,0l8.325-8.279l8.275,8.276c0.394,0.395,1.034,0.395,1.428,0  c0.394-0.396,0.394-1.037,0-1.432L17.459,16.014z\\\"\\n            fill=\\\"#121313\\\"\\n        />\\n    </svg>\\n</span>\", styles: [\".dark tag:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info input{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info .form-control input::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info .form-control input:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info .form-control input::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info input:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading{border:none}.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.ng2-tag-input form{margin:.1em 0}.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.dark.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input form{margin:.1em 0}.bootstrap3-info.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.error-message{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap .error-message{color:#d9534f}:host(delete-icon){width:20px;height:16px;transition:all .15s;display:inline-block;text-align:right}:host(delete-icon) path{fill:#444}:host(delete-icon) svg{vertical-align:bottom;height:34px}:host(delete-icon):hover{transform:scale(1.5) translateY(-3px)}:host-context(.dark){text-align:right}:host-context(.dark) path{fill:#fff}:host-context(.dark) svg{vertical-align:bottom;height:34px}:host-context(.minimal){text-align:right}:host-context(.minimal) path{fill:#444}:host-context(.minimal) svg{vertical-align:bottom;height:34px}:host-context(.bootstrap){text-align:right}:host-context(.bootstrap) path{fill:#fff}:host-context(.bootstrap) svg{vertical-align:bottom;height:34px}:host-context(tag:focus) path,:host-context(tag:active) path{fill:#fff}:host-context(.dark tag:focus) path,:host-context(.dark tag:active) path{fill:#000}:host-context(.minimal tag:focus) path,:host-context(.minimal tag:active) path{fill:#000}:host-context(.bootstrap tag:focus) path,:host-context(.bootstrap tag:active) path{fill:#fff}:host-context(.bootstrap3-info){height:inherit}:host-context(.bootstrap3-info) path{fill:#fff}\\n\"] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: DeleteIconComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'delete-icon', template: \"<span>\\n    <svg\\n        height=\\\"16px\\\"\\n        viewBox=\\\"0 0 32 32\\\"\\n        width=\\\"16px\\\"\\n    >\\n        <path\\n            d=\\\"M17.459,16.014l8.239-8.194c0.395-0.391,0.395-1.024,0-1.414c-0.394-0.391-1.034-0.391-1.428,0  l-8.232,8.187L7.73,6.284c-0.394-0.395-1.034-0.395-1.428,0c-0.394,0.396-0.394,1.037,0,1.432l8.302,8.303l-8.332,8.286  c-0.394,0.391-0.394,1.024,0,1.414c0.394,0.391,1.034,0.391,1.428,0l8.325-8.279l8.275,8.276c0.394,0.395,1.034,0.395,1.428,0  c0.394-0.396,0.394-1.037,0-1.432L17.459,16.014z\\\"\\n            fill=\\\"#121313\\\"\\n        />\\n    </svg>\\n</span>\", styles: [\".dark tag:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info input{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info .form-control input::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info .form-control input:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info .form-control input::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info input:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading{border:none}.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.ng2-tag-input form{margin:.1em 0}.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.dark.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input form{margin:.1em 0}.bootstrap3-info.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.error-message{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap .error-message{color:#d9534f}:host(delete-icon){width:20px;height:16px;transition:all .15s;display:inline-block;text-align:right}:host(delete-icon) path{fill:#444}:host(delete-icon) svg{vertical-align:bottom;height:34px}:host(delete-icon):hover{transform:scale(1.5) translateY(-3px)}:host-context(.dark){text-align:right}:host-context(.dark) path{fill:#fff}:host-context(.dark) svg{vertical-align:bottom;height:34px}:host-context(.minimal){text-align:right}:host-context(.minimal) path{fill:#444}:host-context(.minimal) svg{vertical-align:bottom;height:34px}:host-context(.bootstrap){text-align:right}:host-context(.bootstrap) path{fill:#fff}:host-context(.bootstrap) svg{vertical-align:bottom;height:34px}:host-context(tag:focus) path,:host-context(tag:active) path{fill:#fff}:host-context(.dark tag:focus) path,:host-context(.dark tag:active) path{fill:#000}:host-context(.minimal tag:focus) path,:host-context(.minimal tag:active) path{fill:#000}:host-context(.bootstrap tag:focus) path,:host-context(.bootstrap tag:active) path{fill:#fff}:host-context(.bootstrap3-info){height:inherit}:host-context(.bootstrap3-info) path{fill:#fff}\\n\"] }]\n        }] });\n\n// mocking navigator\nconst navigator = typeof window !== 'undefined' ? window.navigator : {\n    userAgent: 'Chrome',\n    vendor: 'Google Inc'\n};\nconst isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);\nclass TagComponent {\n    constructor(element, renderer, cdRef) {\n        this.element = element;\n        this.renderer = renderer;\n        this.cdRef = cdRef;\n        /**\n         * @name disabled\n         */\n        this.disabled = false;\n        /**\n         * @name onSelect\n         */\n        this.onSelect = new EventEmitter();\n        /**\n         * @name onRemove\n         */\n        this.onRemove = new EventEmitter();\n        /**\n         * @name onBlur\n         */\n        this.onBlur = new EventEmitter();\n        /**\n         * @name onKeyDown\n         */\n        this.onKeyDown = new EventEmitter();\n        /**\n         * @name onTagEdited\n         */\n        this.onTagEdited = new EventEmitter();\n        /**\n         * @name editing\n         */\n        this.editing = false;\n        /**\n         * @name rippleState\n         */\n        this.rippleState = 'none';\n    }\n    /**\n     * @name readonly {boolean}\n     */\n    get readonly() {\n        return typeof this.model !== 'string' && this.model.readonly === true;\n    }\n    /**\n     * @name select\n     */\n    select($event) {\n        if (this.readonly || this.disabled) {\n            return;\n        }\n        if ($event) {\n            $event.stopPropagation();\n        }\n        this.focus();\n        this.onSelect.emit(this.model);\n    }\n    /**\n     * @name remove\n     */\n    remove($event) {\n        $event.stopPropagation();\n        this.onRemove.emit(this);\n    }\n    /**\n     * @name focus\n     */\n    focus() {\n        this.element.nativeElement.focus();\n    }\n    move() {\n        this.moving = true;\n    }\n    /**\n     * @name keydown\n     * @param event\n     */\n    keydown(event) {\n        if (this.editing) {\n            if (event.keyCode === 13) {\n                return this.disableEditMode(event);\n            }\n        }\n        else {\n            this.onKeyDown.emit({ event, model: this.model });\n        }\n    }\n    /**\n     * @name blink\n     */\n    blink() {\n        const classList = this.element.nativeElement.classList;\n        classList.add('blink');\n        setTimeout(() => classList.remove('blink'), 50);\n    }\n    /**\n     * @name toggleEditMode\n     */\n    toggleEditMode() {\n        if (this.editable) {\n            return this.editing ? undefined : this.activateEditMode();\n        }\n    }\n    /**\n     * @name onBlurred\n     * @param event\n     */\n    onBlurred(event) {\n        // Checks if it is editable first before handeling the onBlurred event in order to prevent\n        // a bug in IE where tags are still editable with onlyFromAutocomplete set to true\n        if (!this.editable) {\n            return;\n        }\n        this.disableEditMode();\n        const value = event.target.innerText;\n        const result = typeof this.model === 'string'\n            ? value\n            : { ...this.model, [this.displayBy]: value };\n        this.onBlur.emit(result);\n    }\n    /**\n     * @name getDisplayValue\n     * @param item\n     */\n    getDisplayValue(item) {\n        return typeof item === 'string' ? item : item[this.displayBy];\n    }\n    /**\n     * @desc returns whether the ripple is visible or not\n     * only works in Chrome\n     * @name isRippleVisible\n     */\n    get isRippleVisible() {\n        return !this.readonly && !this.editing && isChrome && this.hasRipple;\n    }\n    /**\n     * @name disableEditMode\n     * @param $event\n     */\n    disableEditMode($event) {\n        const classList = this.element.nativeElement.classList;\n        const input = this.getContentEditableText();\n        this.editing = false;\n        classList.remove('tag--editing');\n        if (!input) {\n            this.setContentEditableText(this.model);\n            return;\n        }\n        this.storeNewValue(input);\n        this.cdRef.detectChanges();\n        if ($event) {\n            $event.preventDefault();\n        }\n    }\n    /**\n     * @name isDeleteIconVisible\n     */\n    isDeleteIconVisible() {\n        return (!this.readonly && !this.disabled && this.removable && !this.editing);\n    }\n    /**\n     * @name getContentEditableText\n     */\n    getContentEditableText() {\n        const input = this.getContentEditable();\n        return input ? input.innerText.trim() : '';\n    }\n    /**\n     * @name setContentEditableText\n     * @param model\n     */\n    setContentEditableText(model) {\n        const input = this.getContentEditable();\n        const value = this.getDisplayValue(model);\n        input.innerText = value;\n    }\n    /**\n     * @name\n     */\n    activateEditMode() {\n        const classList = this.element.nativeElement.classList;\n        classList.add('tag--editing');\n        this.editing = true;\n    }\n    /**\n     * @name storeNewValue\n     * @param input\n     */\n    storeNewValue(input) {\n        const exists = (tag) => {\n            return typeof tag === 'string'\n                ? tag === input\n                : tag[this.displayBy] === input;\n        };\n        const hasId = () => {\n            return this.model[this.identifyBy] !== this.model[this.displayBy];\n        };\n        // if the value changed, replace the value in the model\n        if (exists(this.model)) {\n            return;\n        }\n        const model = typeof this.model === 'string'\n            ? input\n            : {\n                index: this.index,\n                [this.identifyBy]: hasId()\n                    ? this.model[this.identifyBy]\n                    : input,\n                [this.displayBy]: input\n            };\n        if (this.canAddTag(model)) {\n            this.onTagEdited.emit({ tag: model, index: this.index });\n        }\n        else {\n            this.setContentEditableText(this.model);\n        }\n    }\n    /**\n     * @name getContentEditable\n     */\n    getContentEditable() {\n        return this.element.nativeElement.querySelector('[contenteditable]');\n    }\n}\nTagComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagComponent, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nTagComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.3\", type: TagComponent, selector: \"tag\", inputs: { model: \"model\", removable: \"removable\", editable: \"editable\", template: \"template\", displayBy: \"displayBy\", identifyBy: \"identifyBy\", index: \"index\", hasRipple: \"hasRipple\", disabled: \"disabled\", canAddTag: \"canAddTag\" }, outputs: { onSelect: \"onSelect\", onRemove: \"onRemove\", onBlur: \"onBlur\", onKeyDown: \"onKeyDown\", onTagEdited: \"onTagEdited\" }, host: { listeners: { \"keydown\": \"keydown($event)\" }, properties: { \"class.moving\": \"this.moving\" } }, viewQueries: [{ propertyName: \"ripple\", first: true, predicate: TagRipple, descendants: true }], ngImport: i0, template: \"<div (click)=\\\"select($event)\\\"\\n     (dblclick)=\\\"toggleEditMode()\\\"\\n     (mousedown)=\\\"rippleState='clicked'\\\"\\n     (mouseup)=\\\"rippleState='none'\\\"\\n     [ngSwitch]=\\\"!!template\\\"\\n     [class.disabled]=\\\"disabled\\\"\\n     [attr.tabindex]=\\\"-1\\\"\\n     [attr.aria-label]=\\\"getDisplayValue(model)\\\">\\n\\n    <div *ngSwitchCase=\\\"true\\\" [attr.contenteditable]=\\\"editing\\\">\\n        <!-- CUSTOM TEMPLATE -->\\n        <ng-template\\n            [ngTemplateOutletContext]=\\\"{ item: model, index: index }\\\"\\n            [ngTemplateOutlet]=\\\"template\\\">\\n        </ng-template>\\n    </div>\\n\\n    <div *ngSwitchCase=\\\"false\\\" class=\\\"tag-wrapper\\\">\\n        <!-- TAG NAME -->\\n        <div [attr.contenteditable]=\\\"editing\\\"\\n             [attr.title]=\\\"getDisplayValue(model)\\\"\\n             class=\\\"tag__text inline\\\"\\n             spellcheck=\\\"false\\\"\\n             (keydown.enter)=\\\"disableEditMode($event)\\\"\\n             (keydown.escape)=\\\"disableEditMode($event)\\\"\\n             (click)=\\\"editing ? $event.stopPropagation() : undefined\\\"\\n             (blur)=\\\"onBlurred($event)\\\">\\n            {{ getDisplayValue(model) }}\\n        </div>\\n\\n        <!-- 'X' BUTTON -->\\n        <delete-icon\\n            aria-label=\\\"Remove tag\\\"\\n            role=\\\"button\\\"\\n            (click)=\\\"remove($event)\\\"\\n            *ngIf=\\\"isDeleteIconVisible()\\\">\\n        </delete-icon>\\n    </div>\\n</div>\\n\\n<tag-ripple [state]=\\\"rippleState\\\"\\n            [attr.tabindex]=\\\"-1\\\"\\n            *ngIf=\\\"isRippleVisible\\\">\\n</tag-ripple>\\n\", styles: [\":host,:host>div,:host>div:focus{outline:0;overflow:hidden;transition:opacity 1s;z-index:1}:host{max-width:400px}:host.blink{-webkit-animation:blink .3s normal forwards ease-in-out;animation:blink .3s normal forwards ease-in-out}@-webkit-keyframes blink{0%{opacity:.3}}@keyframes blink{0%{opacity:.3}}:host .disabled{cursor:not-allowed}:host [contenteditable=true]{outline:0}.tag-wrapper{flex-direction:row;display:flex}.tag__text{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\\n\"], components: [{ type: DeleteIconComponent, selector: \"delete-icon\" }, { type: TagRipple, selector: \"tag-ripple\", inputs: [\"state\"] }], directives: [{ type: i2.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { type: i2.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }, { type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'tag', template: \"<div (click)=\\\"select($event)\\\"\\n     (dblclick)=\\\"toggleEditMode()\\\"\\n     (mousedown)=\\\"rippleState='clicked'\\\"\\n     (mouseup)=\\\"rippleState='none'\\\"\\n     [ngSwitch]=\\\"!!template\\\"\\n     [class.disabled]=\\\"disabled\\\"\\n     [attr.tabindex]=\\\"-1\\\"\\n     [attr.aria-label]=\\\"getDisplayValue(model)\\\">\\n\\n    <div *ngSwitchCase=\\\"true\\\" [attr.contenteditable]=\\\"editing\\\">\\n        <!-- CUSTOM TEMPLATE -->\\n        <ng-template\\n            [ngTemplateOutletContext]=\\\"{ item: model, index: index }\\\"\\n            [ngTemplateOutlet]=\\\"template\\\">\\n        </ng-template>\\n    </div>\\n\\n    <div *ngSwitchCase=\\\"false\\\" class=\\\"tag-wrapper\\\">\\n        <!-- TAG NAME -->\\n        <div [attr.contenteditable]=\\\"editing\\\"\\n             [attr.title]=\\\"getDisplayValue(model)\\\"\\n             class=\\\"tag__text inline\\\"\\n             spellcheck=\\\"false\\\"\\n             (keydown.enter)=\\\"disableEditMode($event)\\\"\\n             (keydown.escape)=\\\"disableEditMode($event)\\\"\\n             (click)=\\\"editing ? $event.stopPropagation() : undefined\\\"\\n             (blur)=\\\"onBlurred($event)\\\">\\n            {{ getDisplayValue(model) }}\\n        </div>\\n\\n        <!-- 'X' BUTTON -->\\n        <delete-icon\\n            aria-label=\\\"Remove tag\\\"\\n            role=\\\"button\\\"\\n            (click)=\\\"remove($event)\\\"\\n            *ngIf=\\\"isDeleteIconVisible()\\\">\\n        </delete-icon>\\n    </div>\\n</div>\\n\\n<tag-ripple [state]=\\\"rippleState\\\"\\n            [attr.tabindex]=\\\"-1\\\"\\n            *ngIf=\\\"isRippleVisible\\\">\\n</tag-ripple>\\n\", styles: [\":host,:host>div,:host>div:focus{outline:0;overflow:hidden;transition:opacity 1s;z-index:1}:host{max-width:400px}:host.blink{-webkit-animation:blink .3s normal forwards ease-in-out;animation:blink .3s normal forwards ease-in-out}@-webkit-keyframes blink{0%{opacity:.3}}@keyframes blink{0%{opacity:.3}}:host .disabled{cursor:not-allowed}:host [contenteditable=true]{outline:0}.tag-wrapper{flex-direction:row;display:flex}.tag__text{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { model: [{\n                type: Input\n            }], removable: [{\n                type: Input\n            }], editable: [{\n                type: Input\n            }], template: [{\n                type: Input\n            }], displayBy: [{\n                type: Input\n            }], identifyBy: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }], hasRipple: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], canAddTag: [{\n                type: Input\n            }], onSelect: [{\n                type: Output\n            }], onRemove: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], onKeyDown: [{\n                type: Output\n            }], onTagEdited: [{\n                type: Output\n            }], moving: [{\n                type: HostBinding,\n                args: ['class.moving']\n            }], ripple: [{\n                type: ViewChild,\n                args: [TagRipple]\n            }], keydown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\n/**\n * @name animations\n */\nconst animations = [\n    trigger('animation', [\n        state('in', style({\n            opacity: 1\n        })),\n        state('out', style({\n            opacity: 0\n        })),\n        transition(':enter', [\n            animate('{{ enter }}', keyframes([\n                style({ opacity: 0, offset: 0, transform: 'translate(0px, 20px)' }),\n                style({ opacity: 0.3, offset: 0.3, transform: 'translate(0px, -10px)' }),\n                style({ opacity: 0.5, offset: 0.5, transform: 'translate(0px, 0px)' }),\n                style({ opacity: 0.75, offset: 0.75, transform: 'translate(0px, 5px)' }),\n                style({ opacity: 1, offset: 1, transform: 'translate(0px, 0px)' })\n            ]))\n        ]),\n        transition(':leave', [\n            animate('{{ leave }}', keyframes([\n                style({ opacity: 1, transform: 'translateX(0)', offset: 0 }),\n                style({ opacity: 1, transform: 'translateX(-15px)', offset: 0.7 }),\n                style({ opacity: 0, transform: 'translateX(100%)', offset: 1.0 })\n            ]))\n        ])\n    ])\n];\n\nclass TagInputDropdown {\n    constructor(injector) {\n        this.injector = injector;\n        /**\n         * @name offset\n         */\n        this.offset = defaults.dropdown.offset;\n        /**\n         * @name focusFirstElement\n         */\n        this.focusFirstElement = defaults.dropdown.focusFirstElement;\n        /**\n         * - show autocomplete dropdown if the value of input is empty\n         * @name showDropdownIfEmpty\n         */\n        this.showDropdownIfEmpty = defaults.dropdown.showDropdownIfEmpty;\n        /**\n         * - desc minimum text length in order to display the autocomplete dropdown\n         * @name minimumTextLength\n         */\n        this.minimumTextLength = defaults.dropdown.minimumTextLength;\n        /**\n         * - number of items to display in the autocomplete dropdown\n         * @name limitItemsTo\n         */\n        this.limitItemsTo = defaults.dropdown.limitItemsTo;\n        /**\n         * @name displayBy\n         */\n        this.displayBy = defaults.dropdown.displayBy;\n        /**\n         * @name identifyBy\n         */\n        this.identifyBy = defaults.dropdown.identifyBy;\n        /**\n         * @description a function a developer can use to implement custom matching for the autocomplete\n         * @name matchingFn\n         */\n        this.matchingFn = defaults.dropdown.matchingFn;\n        /**\n         * @name appendToBody\n         */\n        this.appendToBody = defaults.dropdown.appendToBody;\n        /**\n         * @name keepOpen\n         * @description option to leave dropdown open when adding a new item\n         */\n        this.keepOpen = defaults.dropdown.keepOpen;\n        /**\n         * @name dynamicUpdate\n         */\n        this.dynamicUpdate = defaults.dropdown.dynamicUpdate;\n        /**\n         * @name zIndex\n         */\n        this.zIndex = defaults.dropdown.zIndex;\n        /**\n         * list of items that match the current value of the input (for autocomplete)\n         * @name items\n         */\n        this.items = [];\n        /**\n         * @name tagInput\n         */\n        this.tagInput = this.injector.get(TagInputComponent);\n        /**\n         * @name _autocompleteItems\n         */\n        this._autocompleteItems = [];\n        /**\n         *\n         * @name show\n         */\n        this.show = () => {\n            const maxItemsReached = this.tagInput.items.length === this.tagInput.maxItems;\n            const value = this.getFormValue();\n            const hasMinimumText = value.trim().length >= this.minimumTextLength;\n            const position = this.calculatePosition();\n            const items = this.getMatchingItems(value);\n            const hasItems = items.length > 0;\n            const isHidden = this.isVisible === false;\n            const showDropdownIfEmpty = this.showDropdownIfEmpty && hasItems && !value;\n            const isDisabled = this.tagInput.disable;\n            const shouldShow = isHidden && ((hasItems && hasMinimumText) || showDropdownIfEmpty);\n            const shouldHide = this.isVisible && !hasItems;\n            if (this.autocompleteObservable && hasMinimumText) {\n                return this.getItemsFromObservable(value);\n            }\n            if ((!this.showDropdownIfEmpty && !value) ||\n                maxItemsReached ||\n                isDisabled) {\n                return this.dropdown.hide();\n            }\n            this.setItems(items);\n            if (shouldShow) {\n                this.dropdown.show(position);\n            }\n            else if (shouldHide) {\n                this.hide();\n            }\n        };\n        /**\n         * @name requestAdding\n         * @param item {Ng2MenuItem}\n         */\n        this.requestAdding = async (item) => {\n            const tag = this.createTagModel(item);\n            await this.tagInput.onAddingRequested(true, tag).catch(() => { });\n        };\n        /**\n         * @name resetItems\n         */\n        this.resetItems = () => {\n            this.items = [];\n        };\n        /**\n         * @name getItemsFromObservable\n         * @param text\n         */\n        this.getItemsFromObservable = (text) => {\n            this.setLoadingState(true);\n            const subscribeFn = (data) => {\n                // hide loading animation\n                this.setLoadingState(false)\n                    // add items\n                    .populateItems(data);\n                this.setItems(this.getMatchingItems(text));\n                if (this.items.length) {\n                    this.dropdown.show(this.calculatePosition());\n                }\n                else {\n                    this.dropdown.hide();\n                }\n            };\n            this.autocompleteObservable(text)\n                .pipe(first())\n                .subscribe(subscribeFn, () => this.setLoadingState(false));\n        };\n    }\n    /**\n     * @name autocompleteItems\n     * @param items\n     */\n    set autocompleteItems(items) {\n        this._autocompleteItems = items;\n    }\n    /**\n     * @name autocompleteItems\n     * @desc array of items that will populate the autocomplete\n     */\n    get autocompleteItems() {\n        const items = this._autocompleteItems;\n        if (!items) {\n            return [];\n        }\n        return items.map((item) => {\n            return typeof item === 'string'\n                ? {\n                    [this.displayBy]: item,\n                    [this.identifyBy]: item\n                }\n                : item;\n        });\n    }\n    /**\n     * @name ngAfterviewInit\n     */\n    ngAfterViewInit() {\n        this.onItemClicked().subscribe((item) => {\n            this.requestAdding(item);\n        });\n        // reset itemsMatching array when the dropdown is hidden\n        this.onHide().subscribe(this.resetItems);\n        const DEBOUNCE_TIME = 200;\n        const KEEP_OPEN = this.keepOpen;\n        this.tagInput.onTextChange\n            .asObservable()\n            .pipe(distinctUntilChanged(), debounceTime(DEBOUNCE_TIME), filter((value) => {\n            if (KEEP_OPEN === false) {\n                return value.length > 0;\n            }\n            return true;\n        }))\n            .subscribe(this.show);\n    }\n    /**\n     * @name updatePosition\n     */\n    updatePosition() {\n        const position = this.tagInput.inputForm.getElementPosition();\n        this.dropdown.menu.updatePosition(position, this.dynamicUpdate);\n    }\n    /**\n     * @name isVisible\n     */\n    get isVisible() {\n        return this.dropdown.menu.dropdownState.menuState.isVisible;\n    }\n    /**\n     * @name onHide\n     */\n    onHide() {\n        return this.dropdown.onHide;\n    }\n    /**\n     * @name onItemClicked\n     */\n    onItemClicked() {\n        return this.dropdown.onItemClicked;\n    }\n    /**\n     * @name selectedItem\n     */\n    get selectedItem() {\n        return this.dropdown.menu.dropdownState.dropdownState.selectedItem;\n    }\n    /**\n     * @name state\n     */\n    get state() {\n        return this.dropdown.menu.dropdownState;\n    }\n    /**\n     * @name hide\n     */\n    hide() {\n        this.resetItems();\n        this.dropdown.hide();\n    }\n    /**\n     * @name scrollListener\n     */\n    scrollListener() {\n        if (!this.isVisible || !this.dynamicUpdate) {\n            return;\n        }\n        this.updatePosition();\n    }\n    /**\n     * @name onWindowBlur\n     */\n    onWindowBlur() {\n        this.dropdown.hide();\n    }\n    /**\n     * @name getFormValue\n     */\n    getFormValue() {\n        const formValue = this.tagInput.formValue;\n        return formValue ? formValue.toString().trim() : '';\n    }\n    /**\n     * @name calculatePosition\n     */\n    calculatePosition() {\n        return this.tagInput.inputForm.getElementPosition();\n    }\n    /**\n     * @name createTagModel\n     * @param item\n     */\n    createTagModel(item) {\n        const display = typeof item.value === 'string' ? item.value : item.value[this.displayBy];\n        const value = typeof item.value === 'string' ? item.value : item.value[this.identifyBy];\n        return {\n            ...item.value,\n            [this.tagInput.displayBy]: display,\n            [this.tagInput.identifyBy]: value\n        };\n    }\n    /**\n     *\n     * @param value {string}\n     */\n    getMatchingItems(value) {\n        if (!value && !this.showDropdownIfEmpty) {\n            return [];\n        }\n        const dupesAllowed = this.tagInput.allowDupes;\n        return this.autocompleteItems.filter((item) => {\n            const hasValue = dupesAllowed\n                ? false\n                : this.tagInput.tags.some(tag => {\n                    const identifyBy = this.tagInput.identifyBy;\n                    const model = typeof tag.model === 'string' ? tag.model : tag.model[identifyBy];\n                    return model === item[this.identifyBy];\n                });\n            return this.matchingFn(value, item) && hasValue === false;\n        });\n    }\n    /**\n     * @name setItems\n     */\n    setItems(items) {\n        this.items = items.slice(0, this.limitItemsTo || items.length);\n    }\n    /**\n     * @name populateItems\n     * @param data\n     */\n    populateItems(data) {\n        this.autocompleteItems = data.map(item => {\n            return typeof item === 'string'\n                ? {\n                    [this.displayBy]: item,\n                    [this.identifyBy]: item\n                }\n                : item;\n        });\n        return this;\n    }\n    /**\n     * @name setLoadingState\n     * @param state\n     */\n    setLoadingState(state) {\n        this.tagInput.isLoading = state;\n        return this;\n    }\n}\nTagInputDropdown.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputDropdown, deps: [{ token: i0.Injector }], target: i0.ɵɵFactoryTarget.Component });\nTagInputDropdown.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.3\", type: TagInputDropdown, selector: \"tag-input-dropdown\", inputs: { offset: \"offset\", focusFirstElement: \"focusFirstElement\", showDropdownIfEmpty: \"showDropdownIfEmpty\", autocompleteObservable: \"autocompleteObservable\", minimumTextLength: \"minimumTextLength\", limitItemsTo: \"limitItemsTo\", displayBy: \"displayBy\", identifyBy: \"identifyBy\", matchingFn: \"matchingFn\", appendToBody: \"appendToBody\", keepOpen: \"keepOpen\", dynamicUpdate: \"dynamicUpdate\", zIndex: \"zIndex\", autocompleteItems: \"autocompleteItems\" }, host: { listeners: { \"window:scroll\": \"scrollListener()\", \"window:blur\": \"onWindowBlur()\" } }, queries: [{ propertyName: \"templates\", predicate: TemplateRef }], viewQueries: [{ propertyName: \"dropdown\", first: true, predicate: Ng2Dropdown, descendants: true }], ngImport: i0, template: \"<ng2-dropdown [dynamicUpdate]=\\\"dynamicUpdate\\\">\\n    <ng2-dropdown-menu [focusFirstElement]=\\\"focusFirstElement\\\"\\n                       [zIndex]=\\\"zIndex\\\"\\n                       [appendToBody]=\\\"appendToBody\\\"\\n                       [offset]=\\\"offset\\\">\\n        <ng2-menu-item *ngFor=\\\"let item of items; let index = index; let last = last\\\"\\n                       [value]=\\\"item\\\"\\n                       [ngSwitch]=\\\"!!templates.length\\\">\\n\\n            <span *ngSwitchCase=\\\"false\\\"\\n                  [innerHTML]=\\\"item[displayBy] | highlight : tagInput.inputForm.value.value\\\">\\n            </span>\\n\\n            <ng-template *ngSwitchDefault\\n                      [ngTemplateOutlet]=\\\"templates.first\\\"\\n                      [ngTemplateOutletContext]=\\\"{ item: item, index: index, last: last }\\\">\\n            </ng-template>\\n        </ng2-menu-item>\\n    </ng2-dropdown-menu>\\n</ng2-dropdown>\\n\", components: [{ type: i1$1.Ng2Dropdown, selector: \"ng2-dropdown\", inputs: [\"dynamicUpdate\"], outputs: [\"onItemClicked\", \"onItemSelected\", \"onShow\", \"onHide\"] }, { type: i1$1.Ng2DropdownMenu, selector: \"ng2-dropdown-menu\", inputs: [\"width\", \"focusFirstElement\", \"offset\", \"appendToBody\", \"zIndex\"] }, { type: i1$1.Ng2MenuItem, selector: \"ng2-menu-item\", inputs: [\"preventClose\", \"value\"] }], directives: [{ type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { type: i2.NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { type: i2.NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { type: i2.NgSwitchDefault, selector: \"[ngSwitchDefault]\" }, { type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }], pipes: { \"highlight\": HighlightPipe } });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputDropdown, decorators: [{\n            type: Component,\n            args: [{ selector: 'tag-input-dropdown', template: \"<ng2-dropdown [dynamicUpdate]=\\\"dynamicUpdate\\\">\\n    <ng2-dropdown-menu [focusFirstElement]=\\\"focusFirstElement\\\"\\n                       [zIndex]=\\\"zIndex\\\"\\n                       [appendToBody]=\\\"appendToBody\\\"\\n                       [offset]=\\\"offset\\\">\\n        <ng2-menu-item *ngFor=\\\"let item of items; let index = index; let last = last\\\"\\n                       [value]=\\\"item\\\"\\n                       [ngSwitch]=\\\"!!templates.length\\\">\\n\\n            <span *ngSwitchCase=\\\"false\\\"\\n                  [innerHTML]=\\\"item[displayBy] | highlight : tagInput.inputForm.value.value\\\">\\n            </span>\\n\\n            <ng-template *ngSwitchDefault\\n                      [ngTemplateOutlet]=\\\"templates.first\\\"\\n                      [ngTemplateOutletContext]=\\\"{ item: item, index: index, last: last }\\\">\\n            </ng-template>\\n        </ng2-menu-item>\\n    </ng2-dropdown-menu>\\n</ng2-dropdown>\\n\" }]\n        }], ctorParameters: function () { return [{ type: i0.Injector }]; }, propDecorators: { dropdown: [{\n                type: ViewChild,\n                args: [Ng2Dropdown]\n            }], templates: [{\n                type: ContentChildren,\n                args: [TemplateRef]\n            }], offset: [{\n                type: Input\n            }], focusFirstElement: [{\n                type: Input\n            }], showDropdownIfEmpty: [{\n                type: Input\n            }], autocompleteObservable: [{\n                type: Input\n            }], minimumTextLength: [{\n                type: Input\n            }], limitItemsTo: [{\n                type: Input\n            }], displayBy: [{\n                type: Input\n            }], identifyBy: [{\n                type: Input\n            }], matchingFn: [{\n                type: Input\n            }], appendToBody: [{\n                type: Input\n            }], keepOpen: [{\n                type: Input\n            }], dynamicUpdate: [{\n                type: Input\n            }], zIndex: [{\n                type: Input\n            }], autocompleteItems: [{\n                type: Input\n            }], scrollListener: [{\n                type: HostListener,\n                args: ['window:scroll']\n            }], onWindowBlur: [{\n                type: HostListener,\n                args: ['window:blur']\n            }] } });\n\n// angular\nconst CUSTOM_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => TagInputComponent),\n    multi: true\n};\nclass TagInputComponent extends TagInputAccessor {\n    constructor(renderer, dragProvider) {\n        super();\n        this.renderer = renderer;\n        this.dragProvider = dragProvider;\n        /**\n         * @name separatorKeys\n         * @desc keyboard keys with which a user can separate items\n         */\n        this.separatorKeys = defaults.tagInput.separatorKeys;\n        /**\n         * @name separatorKeyCodes\n         * @desc keyboard key codes with which a user can separate items\n         */\n        this.separatorKeyCodes = defaults.tagInput.separatorKeyCodes;\n        /**\n         * @name placeholder\n         * @desc the placeholder of the input text\n         */\n        this.placeholder = defaults.tagInput.placeholder;\n        /**\n         * @name secondaryPlaceholder\n         * @desc placeholder to appear when the input is empty\n         */\n        this.secondaryPlaceholder = defaults.tagInput.secondaryPlaceholder;\n        /**\n         * @name maxItems\n         * @desc maximum number of items that can be added\n         */\n        this.maxItems = defaults.tagInput.maxItems;\n        /**\n         * @name validators\n         * @desc array of Validators that are used to validate the tag before it gets appended to the list\n         */\n        this.validators = defaults.tagInput.validators;\n        /**\n         * @name asyncValidators\n         * @desc array of AsyncValidator that are used to validate the tag before it gets appended to the list\n         */\n        this.asyncValidators = defaults.tagInput.asyncValidators;\n        /**\n        * - if set to true, it will only possible to add items from the autocomplete\n        * @name onlyFromAutocomplete\n        */\n        this.onlyFromAutocomplete = defaults.tagInput.onlyFromAutocomplete;\n        /**\n         * @name errorMessages\n         */\n        this.errorMessages = defaults.tagInput.errorMessages;\n        /**\n         * @name theme\n         */\n        this.theme = defaults.tagInput.theme;\n        /**\n         * @name onTextChangeDebounce\n         */\n        this.onTextChangeDebounce = defaults.tagInput.onTextChangeDebounce;\n        /**\n         * - custom id assigned to the input\n         * @name id\n         */\n        this.inputId = defaults.tagInput.inputId;\n        /**\n         * - custom class assigned to the input\n         */\n        this.inputClass = defaults.tagInput.inputClass;\n        /**\n         * - option to clear text input when the form is blurred\n         * @name clearOnBlur\n         */\n        this.clearOnBlur = defaults.tagInput.clearOnBlur;\n        /**\n         * - hideForm\n         * @name clearOnBlur\n         */\n        this.hideForm = defaults.tagInput.hideForm;\n        /**\n         * @name addOnBlur\n         */\n        this.addOnBlur = defaults.tagInput.addOnBlur;\n        /**\n         * @name addOnPaste\n         */\n        this.addOnPaste = defaults.tagInput.addOnPaste;\n        /**\n         * - pattern used with the native method split() to separate patterns in the string pasted\n         * @name pasteSplitPattern\n         */\n        this.pasteSplitPattern = defaults.tagInput.pasteSplitPattern;\n        /**\n         * @name blinkIfDupe\n         */\n        this.blinkIfDupe = defaults.tagInput.blinkIfDupe;\n        /**\n         * @name removable\n         */\n        this.removable = defaults.tagInput.removable;\n        /**\n         * @name editable\n         */\n        this.editable = defaults.tagInput.editable;\n        /**\n         * @name allowDupes\n         */\n        this.allowDupes = defaults.tagInput.allowDupes;\n        /**\n         * @description if set to true, the newly added tags will be added as strings, and not objects\n         * @name modelAsStrings\n         */\n        this.modelAsStrings = defaults.tagInput.modelAsStrings;\n        /**\n         * @name trimTags\n         */\n        this.trimTags = defaults.tagInput.trimTags;\n        /**\n         * @name ripple\n         */\n        this.ripple = defaults.tagInput.ripple;\n        /**\n         * @name tabindex\n         * @desc pass through the specified tabindex to the input\n         */\n        this.tabindex = defaults.tagInput.tabIndex;\n        /**\n         * @name disable\n         */\n        this.disable = defaults.tagInput.disable;\n        /**\n         * @name dragZone\n         */\n        this.dragZone = defaults.tagInput.dragZone;\n        /**\n         * @name onRemoving\n         */\n        this.onRemoving = defaults.tagInput.onRemoving;\n        /**\n         * @name onAdding\n         */\n        this.onAdding = defaults.tagInput.onAdding;\n        /**\n         * @name animationDuration\n         */\n        this.animationDuration = defaults.tagInput.animationDuration;\n        /**\n         * @name onAdd\n         * @desc event emitted when adding a new item\n         */\n        this.onAdd = new EventEmitter();\n        /**\n         * @name onRemove\n         * @desc event emitted when removing an existing item\n         */\n        this.onRemove = new EventEmitter();\n        /**\n         * @name onSelect\n         * @desc event emitted when selecting an item\n         */\n        this.onSelect = new EventEmitter();\n        /**\n         * @name onFocus\n         * @desc event emitted when the input is focused\n         */\n        this.onFocus = new EventEmitter();\n        /**\n         * @name onFocus\n         * @desc event emitted when the input is blurred\n         */\n        this.onBlur = new EventEmitter();\n        /**\n         * @name onTextChange\n         * @desc event emitted when the input value changes\n         */\n        this.onTextChange = new EventEmitter();\n        /**\n         * - output triggered when text is pasted in the form\n         * @name onPaste\n         */\n        this.onPaste = new EventEmitter();\n        /**\n         * - output triggered when tag entered is not valid\n         * @name onValidationError\n         */\n        this.onValidationError = new EventEmitter();\n        /**\n         * - output triggered when tag is edited\n         * @name onTagEdited\n         */\n        this.onTagEdited = new EventEmitter();\n        /**\n         * @name isLoading\n         */\n        this.isLoading = false;\n        /**\n         * @name listeners\n         * @desc array of events that get fired using @fireEvents\n         */\n        this.listeners = {\n            [KEYDOWN]: [],\n            [KEYUP]: []\n        };\n        /**\n         * @description emitter for the 2-way data binding inputText value\n         * @name inputTextChange\n         */\n        this.inputTextChange = new EventEmitter();\n        /**\n         * @description private variable to bind get/set\n         * @name inputTextValue\n         */\n        this.inputTextValue = '';\n        this.errors = [];\n        /**\n         * @name appendTag\n         * @param tag {TagModel}\n         */\n        this.appendTag = (tag, index = this.items.length) => {\n            const items = this.items;\n            const model = this.modelAsStrings ? tag[this.identifyBy] : tag;\n            this.items = [\n                ...items.slice(0, index),\n                model,\n                ...items.slice(index, items.length)\n            ];\n        };\n        /**\n         * @name createTag\n         * @param model\n         */\n        this.createTag = (model) => {\n            const trim = (val, key) => {\n                return typeof val === 'string' ? val.trim() : val[key];\n            };\n            return {\n                ...typeof model !== 'string' ? model : {},\n                [this.displayBy]: this.trimTags ? trim(model, this.displayBy) : model,\n                [this.identifyBy]: this.trimTags ? trim(model, this.identifyBy) : model\n            };\n        };\n        /**\n         *\n         * @param tag\n         * @param isFromAutocomplete\n         */\n        this.isTagValid = (tag, fromAutocomplete = false) => {\n            const selectedItem = this.dropdown ? this.dropdown.selectedItem : undefined;\n            const value = this.getItemDisplay(tag).trim();\n            if (selectedItem && !fromAutocomplete || !value) {\n                return false;\n            }\n            const dupe = this.findDupe(tag, fromAutocomplete);\n            // if so, give a visual cue and return false\n            if (!this.allowDupes && dupe && this.blinkIfDupe) {\n                const model = this.tags.find(item => {\n                    return this.getItemValue(item.model) === this.getItemValue(dupe);\n                });\n                if (model) {\n                    model.blink();\n                }\n            }\n            const isFromAutocomplete = fromAutocomplete && this.onlyFromAutocomplete;\n            const assertions = [\n                // 1. there must be no dupe OR dupes are allowed\n                !dupe || this.allowDupes,\n                // 2. check max items has not been reached\n                !this.maxItemsReached,\n                // 3. check item comes from autocomplete or onlyFromAutocomplete is false\n                ((isFromAutocomplete) || !this.onlyFromAutocomplete)\n            ];\n            return assertions.filter(Boolean).length === assertions.length;\n        };\n        /**\n         * @name onPasteCallback\n         * @param data\n         */\n        this.onPasteCallback = async (data) => {\n            const getText = () => {\n                const isIE = Boolean(window.clipboardData);\n                const clipboardData = isIE ? (window.clipboardData) : data.clipboardData;\n                const type = isIE ? 'Text' : 'text/plain';\n                return clipboardData === null ? '' : clipboardData.getData(type) || '';\n            };\n            const text = getText();\n            const requests = text\n                .split(this.pasteSplitPattern)\n                .map(item => {\n                const tag = this.createTag(item);\n                this.setInputValue(tag[this.displayBy]);\n                return this.onAddingRequested(false, tag);\n            });\n            const resetInput = () => setTimeout(() => this.setInputValue(''), 50);\n            Promise.all(requests).then(() => {\n                this.onPaste.emit(text);\n                resetInput();\n            })\n                .catch(resetInput);\n        };\n    }\n    /**\n     * @name inputText\n     */\n    get inputText() {\n        return this.inputTextValue;\n    }\n    /**\n     * @name inputText\n     * @param text\n     */\n    set inputText(text) {\n        this.inputTextValue = text;\n        this.inputTextChange.emit(text);\n    }\n    /**\n     * @desc removes the tab index if it is set - it will be passed through to the input\n     * @name tabindexAttr\n     */\n    get tabindexAttr() {\n        return this.tabindex !== '' ? '-1' : '';\n    }\n    /**\n     * @name ngAfterViewInit\n     */\n    ngAfterViewInit() {\n        // set up listeners\n        this.setUpKeypressListeners();\n        this.setupSeparatorKeysListener();\n        this.setUpInputKeydownListeners();\n        if (this.onTextChange.observers.length) {\n            this.setUpTextChangeSubscriber();\n        }\n        // if clear on blur is set to true, subscribe to the event and clear the text's form\n        if (this.clearOnBlur || this.addOnBlur) {\n            this.setUpOnBlurSubscriber();\n        }\n        // if addOnPaste is set to true, register the handler and add items\n        if (this.addOnPaste) {\n            this.setUpOnPasteListener();\n        }\n        const statusChanges$ = this.inputForm.form.statusChanges;\n        statusChanges$.pipe(filter$1((status) => status !== 'PENDING')).subscribe(() => {\n            this.errors = this.inputForm.getErrorMessages(this.errorMessages);\n        });\n        this.isProgressBarVisible$ = statusChanges$.pipe(map((status) => {\n            return status === 'PENDING' || this.isLoading;\n        }));\n        // if hideForm is set to true, remove the input\n        if (this.hideForm) {\n            this.inputForm.destroy();\n        }\n    }\n    /**\n     * @name ngOnInit\n     */\n    ngOnInit() {\n        // if the number of items specified in the model is > of the value of maxItems\n        // degrade gracefully and let the max number of items to be the number of items in the model\n        // though, warn the user.\n        const hasReachedMaxItems = this.maxItems !== undefined &&\n            this.items &&\n            this.items.length > this.maxItems;\n        if (hasReachedMaxItems) {\n            this.maxItems = this.items.length;\n            console.warn(MAX_ITEMS_WARNING);\n        }\n        // Setting editable to false to fix problem with tags in IE still being editable when\n        // onlyFromAutocomplete is true\n        this.editable = this.onlyFromAutocomplete ? false : this.editable;\n        this.setAnimationMetadata();\n    }\n    /**\n     * @name onRemoveRequested\n     * @param tag\n     * @param index\n     */\n    onRemoveRequested(tag, index) {\n        return new Promise(resolve => {\n            const subscribeFn = (model) => {\n                this.removeItem(model, index);\n                resolve(tag);\n            };\n            this.onRemoving ?\n                this.onRemoving(tag)\n                    .pipe(first$1())\n                    .subscribe(subscribeFn) : subscribeFn(tag);\n        });\n    }\n    /**\n     * @name onAddingRequested\n     * @param fromAutocomplete {boolean}\n     * @param tag {TagModel}\n     * @param index? {number}\n     * @param giveupFocus? {boolean}\n     */\n    onAddingRequested(fromAutocomplete, tag, index, giveupFocus) {\n        return new Promise((resolve, reject) => {\n            const subscribeFn = (model) => {\n                return this\n                    .addItem(fromAutocomplete, model, index, giveupFocus)\n                    .then(resolve)\n                    .catch(reject);\n            };\n            return this.onAdding ?\n                this.onAdding(tag)\n                    .pipe(first$1())\n                    .subscribe(subscribeFn, reject) : subscribeFn(tag);\n        });\n    }\n    /**\n     * @name selectItem\n     * @desc selects item passed as parameter as the selected tag\n     * @param item\n     * @param emit\n     */\n    selectItem(item, emit = true) {\n        const isReadonly = item && typeof item !== 'string' && item.readonly;\n        if (isReadonly || this.selectedTag === item) {\n            return;\n        }\n        this.selectedTag = item;\n        if (emit) {\n            this.onSelect.emit(item);\n        }\n    }\n    /**\n     * @name fireEvents\n     * @desc goes through the list of the events for a given eventName, and fires each of them\n     * @param eventName\n     * @param $event\n     */\n    fireEvents(eventName, $event) {\n        this.listeners[eventName].forEach(listener => listener.call(this, $event));\n    }\n    /**\n     * @name handleKeydown\n     * @desc handles action when the user hits a keyboard key\n     * @param data\n     */\n    handleKeydown(data) {\n        const event = data.event;\n        const key = event.keyCode || event.which;\n        const shiftKey = event.shiftKey || false;\n        switch (KEY_PRESS_ACTIONS[key]) {\n            case ACTIONS_KEYS.DELETE:\n                if (this.selectedTag && this.removable) {\n                    const index = this.items.indexOf(this.selectedTag);\n                    this.onRemoveRequested(this.selectedTag, index);\n                }\n                break;\n            case ACTIONS_KEYS.SWITCH_PREV:\n                this.moveToTag(data.model, PREV);\n                break;\n            case ACTIONS_KEYS.SWITCH_NEXT:\n                this.moveToTag(data.model, NEXT);\n                break;\n            case ACTIONS_KEYS.TAB:\n                if (shiftKey) {\n                    if (this.isFirstTag(data.model)) {\n                        return;\n                    }\n                    this.moveToTag(data.model, PREV);\n                }\n                else {\n                    if (this.isLastTag(data.model) && (this.disable || this.maxItemsReached)) {\n                        return;\n                    }\n                    this.moveToTag(data.model, NEXT);\n                }\n                break;\n            default:\n                return;\n        }\n        // prevent default behaviour\n        event.preventDefault();\n    }\n    async onFormSubmit() {\n        try {\n            await this.onAddingRequested(false, this.formValue);\n        }\n        catch {\n            return;\n        }\n    }\n    /**\n     * @name setInputValue\n     * @param value\n     */\n    setInputValue(value, emitEvent = true) {\n        const control = this.getControl();\n        // update form value with the transformed item\n        control.setValue(value, { emitEvent });\n    }\n    /**\n     * @name getControl\n     */\n    getControl() {\n        return this.inputForm.value;\n    }\n    /**\n     * @name focus\n     * @param applyFocus\n     * @param displayAutocomplete\n     */\n    focus(applyFocus = false, displayAutocomplete = false) {\n        if (this.dragProvider.getState('dragging')) {\n            return;\n        }\n        this.selectItem(undefined, false);\n        if (applyFocus) {\n            this.inputForm.focus();\n            this.onFocus.emit(this.formValue);\n        }\n    }\n    /**\n     * @name blur\n     */\n    blur() {\n        this.onTouched();\n        this.onBlur.emit(this.formValue);\n    }\n    /**\n     * @name hasErrors\n     */\n    hasErrors() {\n        return !!this.inputForm && this.inputForm.hasErrors();\n    }\n    /**\n     * @name isInputFocused\n     */\n    isInputFocused() {\n        return !!this.inputForm && this.inputForm.isInputFocused();\n    }\n    /**\n     * - this is the one way I found to tell if the template has been passed and it is not\n     * the template for the menu item\n     * @name hasCustomTemplate\n     */\n    hasCustomTemplate() {\n        const template = this.templates ? this.templates.first : undefined;\n        const menuTemplate = this.dropdown && this.dropdown.templates ?\n            this.dropdown.templates.first : undefined;\n        return Boolean(template && template !== menuTemplate);\n    }\n    /**\n     * @name maxItemsReached\n     */\n    get maxItemsReached() {\n        return this.maxItems !== undefined &&\n            this.items.length >= this.maxItems;\n    }\n    /**\n     * @name formValue\n     */\n    get formValue() {\n        const form = this.inputForm.value;\n        return form ? form.value : '';\n    }\n    /**3\n     * @name onDragStarted\n     * @param event\n     * @param index\n     */\n    onDragStarted(event, tag, index) {\n        event.stopPropagation();\n        const item = { zone: this.dragZone, tag, index };\n        this.dragProvider.setSender(this);\n        this.dragProvider.setDraggedItem(event, item);\n        this.dragProvider.setState({ dragging: true, index });\n    }\n    /**\n     * @name onDragOver\n     * @param event\n     */\n    onDragOver(event, index) {\n        this.dragProvider.setState({ dropping: true });\n        this.dragProvider.setReceiver(this);\n        event.preventDefault();\n    }\n    /**\n     * @name onTagDropped\n     * @param event\n     * @param index\n     */\n    onTagDropped(event, index) {\n        const item = this.dragProvider.getDraggedItem(event);\n        if (!item || item.zone !== this.dragZone) {\n            return;\n        }\n        this.dragProvider.onTagDropped(item.tag, item.index, index);\n        event.preventDefault();\n        event.stopPropagation();\n    }\n    /**\n     * @name isDropping\n     */\n    isDropping() {\n        const isReceiver = this.dragProvider.receiver === this;\n        const isDropping = this.dragProvider.getState('dropping');\n        return Boolean(isReceiver && isDropping);\n    }\n    /**\n     * @name onTagBlurred\n     * @param changedElement {TagModel}\n     * @param index {number}\n     */\n    onTagBlurred(changedElement, index) {\n        this.items[index] = changedElement;\n        this.blur();\n    }\n    /**\n     * @name trackBy\n     * @param items\n     */\n    trackBy(index, item) {\n        return item[this.identifyBy];\n    }\n    /**\n     * @name updateEditedTag\n     * @param tag\n     */\n    updateEditedTag(tag) {\n        this.onTagEdited.emit(tag);\n    }\n    /**\n     * @name moveToTag\n     * @param item\n     * @param direction\n     */\n    moveToTag(item, direction) {\n        const isLast = this.isLastTag(item);\n        const isFirst = this.isFirstTag(item);\n        const stopSwitch = (direction === NEXT && isLast) ||\n            (direction === PREV && isFirst);\n        if (stopSwitch) {\n            this.focus(true);\n            return;\n        }\n        const offset = direction === NEXT ? 1 : -1;\n        const index = this.getTagIndex(item) + offset;\n        const tag = this.getTagAtIndex(index);\n        return tag.select.call(tag);\n    }\n    /**\n     * @name isFirstTag\n     * @param item {TagModel}\n     */\n    isFirstTag(item) {\n        return this.tags.first.model === item;\n    }\n    /**\n     * @name isLastTag\n     * @param item {TagModel}\n     */\n    isLastTag(item) {\n        return this.tags.last.model === item;\n    }\n    /**\n     * @name getTagIndex\n     * @param item\n     */\n    getTagIndex(item) {\n        const tags = this.tags.toArray();\n        return tags.findIndex(tag => tag.model === item);\n    }\n    /**\n     * @name getTagAtIndex\n     * @param index\n     */\n    getTagAtIndex(index) {\n        const tags = this.tags.toArray();\n        return tags[index];\n    }\n    /**\n     * @name removeItem\n     * @desc removes an item from the array of the model\n     * @param tag {TagModel}\n     * @param index {number}\n     */\n    removeItem(tag, index) {\n        this.items = this.getItemsWithout(index);\n        // if the removed tag was selected, set it as undefined\n        if (this.selectedTag === tag) {\n            this.selectItem(undefined, false);\n        }\n        // focus input\n        this.focus(true, false);\n        // emit remove event\n        this.onRemove.emit(tag);\n    }\n    /**\n     * @name addItem\n     * @desc adds the current text model to the items array\n     * @param fromAutocomplete {boolean}\n     * @param item {TagModel}\n     * @param index? {number}\n     * @param giveupFocus? {boolean}\n     */\n    addItem(fromAutocomplete = false, item, index, giveupFocus) {\n        const display = this.getItemDisplay(item);\n        const tag = this.createTag(item);\n        if (fromAutocomplete) {\n            this.setInputValue(this.getItemValue(item, true));\n        }\n        return new Promise((resolve, reject) => {\n            /**\n             * @name reset\n             */\n            const reset = () => {\n                // reset control and focus input\n                this.setInputValue('');\n                if (giveupFocus) {\n                    this.focus(false, false);\n                }\n                else {\n                    // focus input\n                    this.focus(true, false);\n                }\n                resolve(display);\n            };\n            const appendItem = () => {\n                this.appendTag(tag, index);\n                // emit event\n                this.onAdd.emit(tag);\n                if (!this.dropdown) {\n                    return;\n                }\n                this.dropdown.hide();\n                if (this.dropdown.showDropdownIfEmpty) {\n                    this.dropdown.show();\n                }\n            };\n            const status = this.inputForm.form.status;\n            const isTagValid = this.isTagValid(tag, fromAutocomplete);\n            const onValidationError = () => {\n                this.onValidationError.emit(tag);\n                return reject();\n            };\n            if (status === 'VALID' && isTagValid) {\n                appendItem();\n                return reset();\n            }\n            if (status === 'INVALID' || !isTagValid) {\n                reset();\n                return onValidationError();\n            }\n            if (status === 'PENDING') {\n                const statusUpdate$ = this.inputForm.form.statusChanges;\n                return statusUpdate$\n                    .pipe(filter$1(statusUpdate => statusUpdate !== 'PENDING'), first$1())\n                    .subscribe((statusUpdate) => {\n                    if (statusUpdate === 'VALID' && isTagValid) {\n                        appendItem();\n                        return reset();\n                    }\n                    else {\n                        reset();\n                        return onValidationError();\n                    }\n                });\n            }\n        });\n    }\n    /**\n     * @name setupSeparatorKeysListener\n     */\n    setupSeparatorKeysListener() {\n        const useSeparatorKeys = this.separatorKeyCodes.length > 0 || this.separatorKeys.length > 0;\n        const listener = ($event) => {\n            const hasKeyCode = this.separatorKeyCodes.indexOf($event.keyCode) >= 0;\n            const hasKey = this.separatorKeys.indexOf($event.key) >= 0;\n            // the keyCode of keydown event is 229 when IME is processing the key event.\n            const isIMEProcessing = $event.keyCode === 229;\n            if (hasKeyCode || (hasKey && !isIMEProcessing)) {\n                $event.preventDefault();\n                this.onAddingRequested(false, this.formValue)\n                    .catch(() => { });\n            }\n        };\n        listen.call(this, KEYDOWN, listener, useSeparatorKeys);\n    }\n    /**\n     * @name setUpKeypressListeners\n     */\n    setUpKeypressListeners() {\n        const listener = ($event) => {\n            const isCorrectKey = $event.keyCode === 37 || $event.keyCode === 8;\n            if (isCorrectKey &&\n                !this.formValue &&\n                this.items.length) {\n                this.tags.last.select.call(this.tags.last);\n            }\n        };\n        // setting up the keypress listeners\n        listen.call(this, KEYDOWN, listener);\n    }\n    /**\n     * @name setUpKeydownListeners\n     */\n    setUpInputKeydownListeners() {\n        this.inputForm.onKeydown.subscribe(event => {\n            if (event.key === 'Backspace' && this.formValue.trim() === '') {\n                event.preventDefault();\n            }\n        });\n    }\n    /**\n     * @name setUpOnPasteListener\n     */\n    setUpOnPasteListener() {\n        const input = this.inputForm.input.nativeElement;\n        // attach listener to input\n        this.renderer.listen(input, 'paste', (event) => {\n            this.onPasteCallback(event);\n            event.preventDefault();\n            return true;\n        });\n    }\n    /**\n     * @name setUpTextChangeSubscriber\n     */\n    setUpTextChangeSubscriber() {\n        this.inputForm.form\n            .valueChanges\n            .pipe(debounceTime$1(this.onTextChangeDebounce))\n            .subscribe((value) => {\n            this.onTextChange.emit(value.item);\n        });\n    }\n    /**\n     * @name setUpOnBlurSubscriber\n     */\n    setUpOnBlurSubscriber() {\n        const filterFn = () => {\n            const isVisible = this.dropdown && this.dropdown.isVisible;\n            return !isVisible && !!this.formValue;\n        };\n        this.inputForm\n            .onBlur\n            .pipe(debounceTime$1(100), filter$1(filterFn))\n            .subscribe(() => {\n            const reset = () => this.setInputValue('');\n            if (this.addOnBlur) {\n                return this\n                    .onAddingRequested(false, this.formValue, undefined, true)\n                    .then(reset)\n                    .catch(reset);\n            }\n            reset();\n        });\n    }\n    /**\n     * @name findDupe\n     * @param tag\n     * @param isFromAutocomplete\n     */\n    findDupe(tag, isFromAutocomplete) {\n        const identifyBy = isFromAutocomplete ? this.dropdown.identifyBy : this.identifyBy;\n        const id = tag[identifyBy];\n        return this.items.find(item => this.getItemValue(item) === id);\n    }\n    /**\n     * @name setAnimationMetadata\n     */\n    setAnimationMetadata() {\n        this.animationMetadata = {\n            value: 'in',\n            params: { ...this.animationDuration }\n        };\n    }\n}\nTagInputComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputComponent, deps: [{ token: i0.Renderer2 }, { token: DragProvider }], target: i0.ɵɵFactoryTarget.Component });\nTagInputComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.3\", type: TagInputComponent, selector: \"tag-input\", inputs: { separatorKeys: \"separatorKeys\", separatorKeyCodes: \"separatorKeyCodes\", placeholder: \"placeholder\", secondaryPlaceholder: \"secondaryPlaceholder\", maxItems: \"maxItems\", validators: \"validators\", asyncValidators: \"asyncValidators\", onlyFromAutocomplete: \"onlyFromAutocomplete\", errorMessages: \"errorMessages\", theme: \"theme\", onTextChangeDebounce: \"onTextChangeDebounce\", inputId: \"inputId\", inputClass: \"inputClass\", clearOnBlur: \"clearOnBlur\", hideForm: \"hideForm\", addOnBlur: \"addOnBlur\", addOnPaste: \"addOnPaste\", pasteSplitPattern: \"pasteSplitPattern\", blinkIfDupe: \"blinkIfDupe\", removable: \"removable\", editable: \"editable\", allowDupes: \"allowDupes\", modelAsStrings: \"modelAsStrings\", trimTags: \"trimTags\", inputText: \"inputText\", ripple: \"ripple\", tabindex: \"tabindex\", disable: \"disable\", dragZone: \"dragZone\", onRemoving: \"onRemoving\", onAdding: \"onAdding\", animationDuration: \"animationDuration\" }, outputs: { onAdd: \"onAdd\", onRemove: \"onRemove\", onSelect: \"onSelect\", onFocus: \"onFocus\", onBlur: \"onBlur\", onTextChange: \"onTextChange\", onPaste: \"onPaste\", onValidationError: \"onValidationError\", onTagEdited: \"onTagEdited\", inputTextChange: \"inputTextChange\" }, host: { properties: { \"attr.tabindex\": \"this.tabindexAttr\" } }, providers: [CUSTOM_ACCESSOR], queries: [{ propertyName: \"dropdown\", first: true, predicate: TagInputDropdown, descendants: true }, { propertyName: \"templates\", predicate: TemplateRef }], viewQueries: [{ propertyName: \"inputForm\", first: true, predicate: TagInputForm, descendants: true }, { propertyName: \"tags\", predicate: TagComponent, descendants: true }], usesInheritance: true, ngImport: i0, template: \"<div\\n    [ngClass]=\\\"theme\\\"\\n    class=\\\"ng2-tag-input\\\"\\n    (click)=\\\"focus(true, false)\\\"\\n    [attr.tabindex]=\\\"-1\\\"\\n    (drop)=\\\"dragZone ? onTagDropped($event, undefined) : undefined\\\"\\n    (dragenter)=\\\"dragZone ? onDragOver($event) : undefined\\\"\\n    (dragover)=\\\"dragZone ? onDragOver($event) : undefined\\\"\\n    (dragend)=\\\"dragZone ? dragProvider.onDragEnd() : undefined\\\"\\n    [class.ng2-tag-input--dropping]=\\\"isDropping()\\\"\\n    [class.ng2-tag-input--disabled]=\\\"disable\\\"\\n    [class.ng2-tag-input--loading]=\\\"isLoading\\\"\\n    [class.ng2-tag-input--invalid]=\\\"hasErrors()\\\"\\n    [class.ng2-tag-input--focused]=\\\"isInputFocused()\\\"\\n>\\n\\n    <!-- TAGS -->\\n    <div class=\\\"ng2-tags-container\\\">\\n        <tag\\n            *ngFor=\\\"let item of items; let i = index; trackBy: trackBy\\\"\\n            (onSelect)=\\\"selectItem(item)\\\"\\n            (onRemove)=\\\"onRemoveRequested(item, i)\\\"\\n            (onKeyDown)=\\\"handleKeydown($event)\\\"\\n            (onTagEdited)=\\\"updateEditedTag($event)\\\"\\n            (onBlur)=\\\"onTagBlurred($event, i)\\\"\\n            draggable=\\\"{{ editable }}\\\"\\n            (dragstart)=\\\"dragZone ? onDragStarted($event, item, i) : undefined\\\"\\n            (drop)=\\\"dragZone ? onTagDropped($event, i) : undefined\\\"\\n            (dragenter)=\\\"dragZone ? onDragOver($event) : undefined\\\"\\n            (dragover)=\\\"dragZone ? onDragOver($event, i) : undefined\\\"\\n            (dragleave)=\\\"dragZone ? dragProvider.onDragEnd() : undefined\\\"\\n            [canAddTag]=\\\"isTagValid\\\"\\n            [attr.tabindex]=\\\"0\\\"\\n            [disabled]=\\\"disable\\\"\\n            [@animation]=\\\"animationMetadata\\\"\\n            [hasRipple]=\\\"ripple\\\"\\n            [index]=\\\"i\\\"\\n            [removable]=\\\"removable\\\"\\n            [editable]=\\\"editable\\\"\\n            [displayBy]=\\\"displayBy\\\"\\n            [identifyBy]=\\\"identifyBy\\\"\\n            [template]=\\\"!!hasCustomTemplate() ? templates.first : undefined\\\"\\n            [draggable]=\\\"dragZone\\\"\\n            [model]=\\\"item\\\"\\n        >\\n        </tag>\\n\\n        <tag-input-form\\n            (onSubmit)=\\\"onFormSubmit()\\\"\\n            (onBlur)=\\\"blur()\\\"\\n            (click)=\\\"dropdown ? dropdown.show() : undefined\\\"\\n            (onKeydown)=\\\"fireEvents('keydown', $event)\\\"\\n            (onKeyup)=\\\"fireEvents('keyup', $event)\\\"\\n            [inputText]=\\\"inputText\\\"\\n            [disabled]=\\\"disable\\\"\\n            [validators]=\\\"validators\\\"\\n            [asyncValidators]=\\\"asyncValidators\\\"\\n            [hidden]=\\\"maxItemsReached\\\"\\n            [placeholder]=\\\"items.length ? placeholder : secondaryPlaceholder\\\"\\n            [inputClass]=\\\"inputClass\\\"\\n            [inputId]=\\\"inputId\\\"\\n            [tabindex]=\\\"tabindex\\\"\\n        >\\n        </tag-input-form>\\n    </div>\\n\\n    <div\\n        class=\\\"progress-bar\\\"\\n        *ngIf=\\\"isProgressBarVisible$ | async\\\"\\n    ></div>\\n</div>\\n\\n<!-- ERRORS -->\\n<div\\n    *ngIf=\\\"hasErrors()\\\"\\n    [ngClass]=\\\"theme\\\"\\n    class=\\\"error-messages\\\"\\n>\\n    <p\\n        *ngFor=\\\"let error of errors\\\"\\n        class=\\\"error-message\\\"\\n    >\\n        <span>{{ error }}</span>\\n    </p>\\n</div>\\n<ng-content></ng-content>\\n\", styles: [\".dark tag:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info input{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info .form-control input::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info .form-control input:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info .form-control input::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info input:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading{border:none}.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.ng2-tag-input form{margin:.1em 0}.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.dark.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input form{margin:.1em 0}.bootstrap3-info.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.error-message{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap .error-message{color:#d9534f}.progress-bar,.progress-bar:before{height:2px;width:100%;margin:0}.progress-bar{background-color:#2196f3;display:flex;position:absolute;bottom:0}.progress-bar:before{background-color:#82c4f8;content:\\\"\\\";-webkit-animation:running-progress 2s cubic-bezier(.4,0,.2,1) infinite;animation:running-progress 2s cubic-bezier(.4,0,.2,1) infinite}@-webkit-keyframes running-progress{0%{margin-left:0;margin-right:100%}50%{margin-left:25%;margin-right:0}to{margin-left:100%;margin-right:0}}@keyframes running-progress{0%{margin-left:0;margin-right:100%}50%{margin-left:25%;margin-right:0}to{margin-left:100%;margin-right:0}}tag{display:flex;flex-direction:row;flex-wrap:wrap;font-family:Roboto,Helvetica Neue,sans-serif;font-weight:400;font-size:1em;letter-spacing:.05rem;color:#444;border-radius:16px;transition:all .3s;margin:.1rem .3rem .1rem 0;padding:.08rem .45rem;height:32px;line-height:34px;background:#efefef;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}tag:not(.readonly):not(.tag--editing):focus{background:#2196F3;color:#fff;box-shadow:0 2px 3px 1px #d4d1d1}tag:not(.readonly):not(.tag--editing):active{background:#0d8aee;color:#fff;box-shadow:0 2px 3px 1px #d4d1d1}tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#e2e2e2;color:#000;color:initial;box-shadow:0 2px 3px 1px #d4d1d1}tag.readonly{cursor:default}tag.readonly:focus,tag:focus{outline:0}tag.tag--editing{background-color:#fff;border:1px solid #ccc;cursor:text}.minimal tag{display:flex;flex-direction:row;flex-wrap:wrap;border-radius:0;background:#f9f9f9;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.minimal tag:not(.readonly):not(.tag--editing):focus{background:#d0d0d0;color:#000;color:initial}.minimal tag:not(.readonly):not(.tag--editing):active{background:#d0d0d0;color:#000;color:initial}.minimal tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#ececec}.minimal tag.readonly{cursor:default}.minimal tag.readonly:focus,.minimal tag:focus{outline:0}.minimal tag.tag--editing{cursor:text}.dark tag{display:flex;flex-direction:row;flex-wrap:wrap;color:#f9f9f9;border-radius:3px;background:#444;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.dark tag:not(.readonly):not(.tag--editing):focus{background:#efefef;color:#444}.dark tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#2b2b2b;color:#f9f9f9}.dark tag.readonly{cursor:default}.dark tag.readonly:focus,.dark tag:focus{outline:0}.dark tag.tag--editing{cursor:text}.bootstrap tag{display:flex;flex-direction:row;flex-wrap:wrap;color:#f9f9f9;border-radius:.25rem;background:#0275d8;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.bootstrap tag:not(.readonly):not(.tag--editing):focus{background:#025aa5}.bootstrap tag:not(.readonly):not(.tag--editing):active{background:#025aa5}.bootstrap tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#0267bf;color:#f9f9f9}.bootstrap tag.readonly{cursor:default}.bootstrap tag.readonly:focus,.bootstrap tag:focus{outline:0}.bootstrap tag.tag--editing{cursor:text}.bootstrap3-info tag{display:flex;flex-direction:row;flex-wrap:wrap;font-family:inherit;font-weight:400;font-size:95%;color:#fff;border-radius:.25em;background:#5bc0de;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative;padding:.25em .6em;text-align:center;white-space:nowrap}.bootstrap3-info tag:not(.readonly):not(.tag--editing):focus{background:#28a1c5}.bootstrap3-info tag:not(.readonly):not(.tag--editing):active{background:#28a1c5}.bootstrap3-info tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#46b8da;color:#fff}.bootstrap3-info tag.readonly{cursor:default}.bootstrap3-info tag.readonly:focus,.bootstrap3-info tag:focus{outline:0}.bootstrap3-info tag.tag--editing{cursor:text}:host{display:block}\\n\"], components: [{ type: TagComponent, selector: \"tag\", inputs: [\"model\", \"removable\", \"editable\", \"template\", \"displayBy\", \"identifyBy\", \"index\", \"hasRipple\", \"disabled\", \"canAddTag\"], outputs: [\"onSelect\", \"onRemove\", \"onBlur\", \"onKeyDown\", \"onTagEdited\"] }, { type: TagInputForm, selector: \"tag-input-form\", inputs: [\"placeholder\", \"validators\", \"asyncValidators\", \"inputId\", \"inputClass\", \"tabindex\", \"disabled\", \"inputText\"], outputs: [\"onSubmit\", \"onBlur\", \"onFocus\", \"onKeyup\", \"onKeydown\", \"inputTextChange\"] }], directives: [{ type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], pipes: { \"async\": i2.AsyncPipe }, animations: animations });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'tag-input', providers: [CUSTOM_ACCESSOR], animations: animations, template: \"<div\\n    [ngClass]=\\\"theme\\\"\\n    class=\\\"ng2-tag-input\\\"\\n    (click)=\\\"focus(true, false)\\\"\\n    [attr.tabindex]=\\\"-1\\\"\\n    (drop)=\\\"dragZone ? onTagDropped($event, undefined) : undefined\\\"\\n    (dragenter)=\\\"dragZone ? onDragOver($event) : undefined\\\"\\n    (dragover)=\\\"dragZone ? onDragOver($event) : undefined\\\"\\n    (dragend)=\\\"dragZone ? dragProvider.onDragEnd() : undefined\\\"\\n    [class.ng2-tag-input--dropping]=\\\"isDropping()\\\"\\n    [class.ng2-tag-input--disabled]=\\\"disable\\\"\\n    [class.ng2-tag-input--loading]=\\\"isLoading\\\"\\n    [class.ng2-tag-input--invalid]=\\\"hasErrors()\\\"\\n    [class.ng2-tag-input--focused]=\\\"isInputFocused()\\\"\\n>\\n\\n    <!-- TAGS -->\\n    <div class=\\\"ng2-tags-container\\\">\\n        <tag\\n            *ngFor=\\\"let item of items; let i = index; trackBy: trackBy\\\"\\n            (onSelect)=\\\"selectItem(item)\\\"\\n            (onRemove)=\\\"onRemoveRequested(item, i)\\\"\\n            (onKeyDown)=\\\"handleKeydown($event)\\\"\\n            (onTagEdited)=\\\"updateEditedTag($event)\\\"\\n            (onBlur)=\\\"onTagBlurred($event, i)\\\"\\n            draggable=\\\"{{ editable }}\\\"\\n            (dragstart)=\\\"dragZone ? onDragStarted($event, item, i) : undefined\\\"\\n            (drop)=\\\"dragZone ? onTagDropped($event, i) : undefined\\\"\\n            (dragenter)=\\\"dragZone ? onDragOver($event) : undefined\\\"\\n            (dragover)=\\\"dragZone ? onDragOver($event, i) : undefined\\\"\\n            (dragleave)=\\\"dragZone ? dragProvider.onDragEnd() : undefined\\\"\\n            [canAddTag]=\\\"isTagValid\\\"\\n            [attr.tabindex]=\\\"0\\\"\\n            [disabled]=\\\"disable\\\"\\n            [@animation]=\\\"animationMetadata\\\"\\n            [hasRipple]=\\\"ripple\\\"\\n            [index]=\\\"i\\\"\\n            [removable]=\\\"removable\\\"\\n            [editable]=\\\"editable\\\"\\n            [displayBy]=\\\"displayBy\\\"\\n            [identifyBy]=\\\"identifyBy\\\"\\n            [template]=\\\"!!hasCustomTemplate() ? templates.first : undefined\\\"\\n            [draggable]=\\\"dragZone\\\"\\n            [model]=\\\"item\\\"\\n        >\\n        </tag>\\n\\n        <tag-input-form\\n            (onSubmit)=\\\"onFormSubmit()\\\"\\n            (onBlur)=\\\"blur()\\\"\\n            (click)=\\\"dropdown ? dropdown.show() : undefined\\\"\\n            (onKeydown)=\\\"fireEvents('keydown', $event)\\\"\\n            (onKeyup)=\\\"fireEvents('keyup', $event)\\\"\\n            [inputText]=\\\"inputText\\\"\\n            [disabled]=\\\"disable\\\"\\n            [validators]=\\\"validators\\\"\\n            [asyncValidators]=\\\"asyncValidators\\\"\\n            [hidden]=\\\"maxItemsReached\\\"\\n            [placeholder]=\\\"items.length ? placeholder : secondaryPlaceholder\\\"\\n            [inputClass]=\\\"inputClass\\\"\\n            [inputId]=\\\"inputId\\\"\\n            [tabindex]=\\\"tabindex\\\"\\n        >\\n        </tag-input-form>\\n    </div>\\n\\n    <div\\n        class=\\\"progress-bar\\\"\\n        *ngIf=\\\"isProgressBarVisible$ | async\\\"\\n    ></div>\\n</div>\\n\\n<!-- ERRORS -->\\n<div\\n    *ngIf=\\\"hasErrors()\\\"\\n    [ngClass]=\\\"theme\\\"\\n    class=\\\"error-messages\\\"\\n>\\n    <p\\n        *ngFor=\\\"let error of errors\\\"\\n        class=\\\"error-message\\\"\\n    >\\n        <span>{{ error }}</span>\\n    </p>\\n</div>\\n<ng-content></ng-content>\\n\", styles: [\".dark tag:focus{box-shadow:0 0 0 1px #323232}.ng2-tag-input.bootstrap3-info{background-color:#fff;display:inline-block;color:#555;vertical-align:middle;max-width:100%;height:42px;line-height:44px}.ng2-tag-input.bootstrap3-info input{border:none;box-shadow:none;outline:none;background-color:transparent;padding:0 6px;margin:0;width:auto;max-width:inherit}.ng2-tag-input.bootstrap3-info .form-control input::-moz-placeholder{color:#777;opacity:1}.ng2-tag-input.bootstrap3-info .form-control input:-ms-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info .form-control input::-webkit-input-placeholder{color:#777}.ng2-tag-input.bootstrap3-info input:focus{border:none;box-shadow:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--focused{box-shadow:inset 0 1px 1px #0006;border:1px solid #ccc}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{box-shadow:inset 0 1px 1px #d9534f}.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;transition:all .25s;padding:.25rem 0;min-height:32px;cursor:text;border-bottom:2px solid #efefef}.ng2-tag-input:focus{outline:0}.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #2196F3}.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #f44336}.ng2-tag-input.ng2-tag-input--loading{border:none}.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.ng2-tag-input form{margin:.1em 0}.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.minimal.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:1px solid transparent}.minimal.ng2-tag-input:focus{outline:0}.minimal.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.minimal.ng2-tag-input.ng2-tag-input--loading{border:none}.minimal.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.minimal.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.dark.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #444}.dark.ng2-tag-input:focus{outline:0}.dark.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.dark.ng2-tag-input.ng2-tag-input--loading{border:none}.dark.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.dark.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;cursor:text;border-bottom:2px solid #efefef}.bootstrap.ng2-tag-input:focus{outline:0}.bootstrap.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap.ng2-tag-input.ng2-tag-input--focused{border-bottom:2px solid #0275d8}.bootstrap.ng2-tag-input.ng2-tag-input--invalid{border-bottom:2px solid #d9534f}.bootstrap.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.bootstrap3-info.ng2-tag-input{display:block;flex-direction:row;flex-wrap:wrap;position:relative;padding:4px;cursor:text;box-shadow:inset 0 1px 1px #00000013;border-radius:4px}.bootstrap3-info.ng2-tag-input:focus{outline:0}.bootstrap3-info.ng2-tag-input.ng2-tag-input--dropping{opacity:.7}.bootstrap3-info.ng2-tag-input.ng2-tag-input--invalid{border-bottom:1px solid #d9534f}.bootstrap3-info.ng2-tag-input.ng2-tag-input--loading{border:none}.bootstrap3-info.ng2-tag-input.ng2-tag-input--disabled{opacity:.5;cursor:not-allowed}.bootstrap3-info.ng2-tag-input form{margin:.1em 0}.bootstrap3-info.ng2-tag-input .ng2-tags-container{flex-wrap:wrap;display:flex}.error-message{font-size:.8em;color:#f44336;margin:.5em 0 0}.bootstrap .error-message{color:#d9534f}.progress-bar,.progress-bar:before{height:2px;width:100%;margin:0}.progress-bar{background-color:#2196f3;display:flex;position:absolute;bottom:0}.progress-bar:before{background-color:#82c4f8;content:\\\"\\\";-webkit-animation:running-progress 2s cubic-bezier(.4,0,.2,1) infinite;animation:running-progress 2s cubic-bezier(.4,0,.2,1) infinite}@-webkit-keyframes running-progress{0%{margin-left:0;margin-right:100%}50%{margin-left:25%;margin-right:0}to{margin-left:100%;margin-right:0}}@keyframes running-progress{0%{margin-left:0;margin-right:100%}50%{margin-left:25%;margin-right:0}to{margin-left:100%;margin-right:0}}tag{display:flex;flex-direction:row;flex-wrap:wrap;font-family:Roboto,Helvetica Neue,sans-serif;font-weight:400;font-size:1em;letter-spacing:.05rem;color:#444;border-radius:16px;transition:all .3s;margin:.1rem .3rem .1rem 0;padding:.08rem .45rem;height:32px;line-height:34px;background:#efefef;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}tag:not(.readonly):not(.tag--editing):focus{background:#2196F3;color:#fff;box-shadow:0 2px 3px 1px #d4d1d1}tag:not(.readonly):not(.tag--editing):active{background:#0d8aee;color:#fff;box-shadow:0 2px 3px 1px #d4d1d1}tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#e2e2e2;color:#000;color:initial;box-shadow:0 2px 3px 1px #d4d1d1}tag.readonly{cursor:default}tag.readonly:focus,tag:focus{outline:0}tag.tag--editing{background-color:#fff;border:1px solid #ccc;cursor:text}.minimal tag{display:flex;flex-direction:row;flex-wrap:wrap;border-radius:0;background:#f9f9f9;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.minimal tag:not(.readonly):not(.tag--editing):focus{background:#d0d0d0;color:#000;color:initial}.minimal tag:not(.readonly):not(.tag--editing):active{background:#d0d0d0;color:#000;color:initial}.minimal tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#ececec}.minimal tag.readonly{cursor:default}.minimal tag.readonly:focus,.minimal tag:focus{outline:0}.minimal tag.tag--editing{cursor:text}.dark tag{display:flex;flex-direction:row;flex-wrap:wrap;color:#f9f9f9;border-radius:3px;background:#444;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.dark tag:not(.readonly):not(.tag--editing):focus{background:#efefef;color:#444}.dark tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#2b2b2b;color:#f9f9f9}.dark tag.readonly{cursor:default}.dark tag.readonly:focus,.dark tag:focus{outline:0}.dark tag.tag--editing{cursor:text}.bootstrap tag{display:flex;flex-direction:row;flex-wrap:wrap;color:#f9f9f9;border-radius:.25rem;background:#0275d8;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative}.bootstrap tag:not(.readonly):not(.tag--editing):focus{background:#025aa5}.bootstrap tag:not(.readonly):not(.tag--editing):active{background:#025aa5}.bootstrap tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#0267bf;color:#f9f9f9}.bootstrap tag.readonly{cursor:default}.bootstrap tag.readonly:focus,.bootstrap tag:focus{outline:0}.bootstrap tag.tag--editing{cursor:text}.bootstrap3-info tag{display:flex;flex-direction:row;flex-wrap:wrap;font-family:inherit;font-weight:400;font-size:95%;color:#fff;border-radius:.25em;background:#5bc0de;-webkit-user-select:none;-moz-user-select:none;user-select:none;overflow:hidden;outline:0;cursor:pointer;position:relative;padding:.25em .6em;text-align:center;white-space:nowrap}.bootstrap3-info tag:not(.readonly):not(.tag--editing):focus{background:#28a1c5}.bootstrap3-info tag:not(.readonly):not(.tag--editing):active{background:#28a1c5}.bootstrap3-info tag:not(:focus):not(.tag--editing):not(:active):not(.readonly):hover{background:#46b8da;color:#fff}.bootstrap3-info tag.readonly{cursor:default}.bootstrap3-info tag.readonly:focus,.bootstrap3-info tag:focus{outline:0}.bootstrap3-info tag.tag--editing{cursor:text}:host{display:block}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }, { type: DragProvider }]; }, propDecorators: { separatorKeys: [{\n                type: Input\n            }], separatorKeyCodes: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], secondaryPlaceholder: [{\n                type: Input\n            }], maxItems: [{\n                type: Input\n            }], validators: [{\n                type: Input\n            }], asyncValidators: [{\n                type: Input\n            }], onlyFromAutocomplete: [{\n                type: Input\n            }], errorMessages: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], onTextChangeDebounce: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], inputClass: [{\n                type: Input\n            }], clearOnBlur: [{\n                type: Input\n            }], hideForm: [{\n                type: Input\n            }], addOnBlur: [{\n                type: Input\n            }], addOnPaste: [{\n                type: Input\n            }], pasteSplitPattern: [{\n                type: Input\n            }], blinkIfDupe: [{\n                type: Input\n            }], removable: [{\n                type: Input\n            }], editable: [{\n                type: Input\n            }], allowDupes: [{\n                type: Input\n            }], modelAsStrings: [{\n                type: Input\n            }], trimTags: [{\n                type: Input\n            }], inputText: [{\n                type: Input\n            }], ripple: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], disable: [{\n                type: Input\n            }], dragZone: [{\n                type: Input\n            }], onRemoving: [{\n                type: Input\n            }], onAdding: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }], onAdd: [{\n                type: Output\n            }], onRemove: [{\n                type: Output\n            }], onSelect: [{\n                type: Output\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], onTextChange: [{\n                type: Output\n            }], onPaste: [{\n                type: Output\n            }], onValidationError: [{\n                type: Output\n            }], onTagEdited: [{\n                type: Output\n            }], dropdown: [{\n                type: ContentChild,\n                args: [TagInputDropdown]\n            }], templates: [{\n                type: ContentChildren,\n                args: [TemplateRef, { descendants: false }]\n            }], inputForm: [{\n                type: ViewChild,\n                args: [TagInputForm]\n            }], tags: [{\n                type: ViewChildren,\n                args: [TagComponent]\n            }], inputTextChange: [{\n                type: Output\n            }], tabindexAttr: [{\n                type: HostBinding,\n                args: ['attr.tabindex']\n            }] } });\n\nconst optionsProvider = new OptionsProvider();\nclass TagInputModule {\n    /**\n     * @name withDefaults\n     * @param options {Options}\n     */\n    static withDefaults(options) {\n        optionsProvider.setOptions(options);\n    }\n}\nTagInputModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTagInputModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputModule, declarations: [TagInputComponent,\n        DeleteIconComponent,\n        TagInputForm,\n        TagComponent,\n        HighlightPipe,\n        TagInputDropdown,\n        TagRipple], imports: [CommonModule,\n        ReactiveFormsModule,\n        FormsModule,\n        Ng2DropdownModule], exports: [TagInputComponent,\n        DeleteIconComponent,\n        TagInputForm,\n        TagComponent,\n        HighlightPipe,\n        TagInputDropdown,\n        TagRipple] });\nTagInputModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputModule, providers: [\n        DragProvider,\n        { provide: COMPOSITION_BUFFER_MODE, useValue: false },\n    ], imports: [[\n            CommonModule,\n            ReactiveFormsModule,\n            FormsModule,\n            Ng2DropdownModule\n        ]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.3\", ngImport: i0, type: TagInputModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        ReactiveFormsModule,\n                        FormsModule,\n                        Ng2DropdownModule\n                    ],\n                    declarations: [\n                        TagInputComponent,\n                        DeleteIconComponent,\n                        TagInputForm,\n                        TagComponent,\n                        HighlightPipe,\n                        TagInputDropdown,\n                        TagRipple\n                    ],\n                    exports: [\n                        TagInputComponent,\n                        DeleteIconComponent,\n                        TagInputForm,\n                        TagComponent,\n                        HighlightPipe,\n                        TagInputDropdown,\n                        TagRipple\n                    ],\n                    providers: [\n                        DragProvider,\n                        { provide: COMPOSITION_BUFFER_MODE, useValue: false },\n                    ]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DeleteIconComponent, HighlightPipe, TagComponent, TagInputComponent, TagInputDropdown, TagInputForm, TagInputModule, TagRipple };\n"]}, "metadata": {}, "sourceType": "module"}
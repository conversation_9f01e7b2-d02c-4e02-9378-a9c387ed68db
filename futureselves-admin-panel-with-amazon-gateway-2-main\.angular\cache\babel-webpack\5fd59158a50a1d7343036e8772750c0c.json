{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { sample } from './sample';\nimport { interval } from '../observable/interval';\nexport function sampleTime(period, scheduler = asyncScheduler) {\n  return sample(interval(period, scheduler));\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/operators/sampleTime.js"], "names": ["asyncScheduler", "sample", "interval", "sampleTime", "period", "scheduler"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,QAAT,QAAyB,wBAAzB;AACA,OAAO,SAASC,UAAT,CAAoBC,MAApB,EAA4BC,SAAS,GAAGL,cAAxC,EAAwD;AAC3D,SAAOC,MAAM,CAACC,QAAQ,CAACE,MAAD,EAASC,SAAT,CAAT,CAAb;AACH", "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { sample } from './sample';\nimport { interval } from '../observable/interval';\nexport function sampleTime(period, scheduler = asyncScheduler) {\n    return sample(interval(period, scheduler));\n}\n"]}, "metadata": {}, "sourceType": "module"}
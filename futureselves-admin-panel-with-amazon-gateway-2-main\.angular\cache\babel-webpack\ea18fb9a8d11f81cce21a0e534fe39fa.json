{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport Chart from 'chart.js/auto';\n\nclass UIChart {\n  constructor(el) {\n    this.el = el;\n    this.plugins = [];\n    this.responsive = true;\n    this.onDataSelect = new EventEmitter();\n    this._options = {};\n  }\n\n  get data() {\n    return this._data;\n  }\n\n  set data(val) {\n    this._data = val;\n    this.reinit();\n  }\n\n  get options() {\n    return this._options;\n  }\n\n  set options(val) {\n    this._options = val;\n    this.reinit();\n  }\n\n  ngAfterViewInit() {\n    this.initChart();\n    this.initialized = true;\n  }\n\n  onCanvasClick(event) {\n    if (this.chart) {\n      const element = this.chart.getElementsAtEventForMode(event, 'nearest', {\n        intersect: true\n      }, false);\n      const dataset = this.chart.getElementsAtEventForMode(event, 'dataset', {\n        intersect: true\n      }, false);\n\n      if (element && element[0] && dataset) {\n        this.onDataSelect.emit({\n          originalEvent: event,\n          element: element[0],\n          dataset: dataset\n        });\n      }\n    }\n  }\n\n  initChart() {\n    let opts = this.options || {};\n    opts.responsive = this.responsive; // allows chart to resize in responsive mode\n\n    if (opts.responsive && (this.height || this.width)) {\n      opts.maintainAspectRatio = false;\n    }\n\n    this.chart = new Chart(this.el.nativeElement.children[0].children[0], {\n      type: this.type,\n      data: this.data,\n      options: this.options,\n      plugins: this.plugins\n    });\n  }\n\n  getCanvas() {\n    return this.el.nativeElement.children[0].children[0];\n  }\n\n  getBase64Image() {\n    return this.chart.toBase64Image();\n  }\n\n  generateLegend() {\n    if (this.chart) {\n      return this.chart.generateLegend();\n    }\n  }\n\n  refresh() {\n    if (this.chart) {\n      this.chart.update();\n    }\n  }\n\n  reinit() {\n    if (this.chart) {\n      this.chart.destroy();\n      this.initChart();\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.chart) {\n      this.chart.destroy();\n      this.initialized = false;\n      this.chart = null;\n    }\n  }\n\n}\n\nUIChart.ɵfac = function UIChart_Factory(t) {\n  return new (t || UIChart)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nUIChart.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: UIChart,\n  selectors: [[\"p-chart\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    type: \"type\",\n    plugins: \"plugins\",\n    width: \"width\",\n    height: \"height\",\n    responsive: \"responsive\",\n    data: \"data\",\n    options: \"options\"\n  },\n  outputs: {\n    onDataSelect: \"onDataSelect\"\n  },\n  decls: 2,\n  vars: 6,\n  consts: [[2, \"position\", \"relative\"], [3, \"click\"]],\n  template: function UIChart_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵelementStart(1, \"canvas\", 1);\n      i0.ɵɵlistener(\"click\", function UIChart_Template_canvas_click_1_listener($event) {\n        return ctx.onCanvasClick($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"width\", ctx.responsive && !ctx.width ? null : ctx.width)(\"height\", ctx.responsive && !ctx.height ? null : ctx.height);\n      i0.ɵɵadvance(1);\n      i0.ɵɵattribute(\"width\", ctx.responsive && !ctx.width ? null : ctx.width)(\"height\", ctx.responsive && !ctx.height ? null : ctx.height);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UIChart, [{\n    type: Component,\n    args: [{\n      selector: 'p-chart',\n      template: `\n        <div style=\"position:relative\" [style.width]=\"responsive && !width ? null : width\" [style.height]=\"responsive && !height ? null : height\">\n            <canvas [attr.width]=\"responsive && !width ? null : width\" [attr.height]=\"responsive && !height ? null : height\" (click)=\"onCanvasClick($event)\"></canvas>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    type: [{\n      type: Input\n    }],\n    plugins: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }],\n    responsive: [{\n      type: Input\n    }],\n    onDataSelect: [{\n      type: Output\n    }],\n    data: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }]\n  });\n})();\n\nclass ChartModule {}\n\nChartModule.ɵfac = function ChartModule_Factory(t) {\n  return new (t || ChartModule)();\n};\n\nChartModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ChartModule\n});\nChartModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ChartModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [UIChart],\n      declarations: [UIChart]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ChartModule, UIChart };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/primeng-lts/fesm2015/primeng-lts-chart.js"], "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "CommonModule", "Chart", "UIChart", "constructor", "el", "plugins", "responsive", "onDataSelect", "_options", "data", "_data", "val", "reinit", "options", "ngAfterViewInit", "initChart", "initialized", "onCanvasClick", "event", "chart", "element", "getElementsAtEventForMode", "intersect", "dataset", "emit", "originalEvent", "opts", "height", "width", "maintainAspectRatio", "nativeElement", "children", "type", "get<PERSON>anvas", "getBase64Image", "toBase64Image", "generateLegend", "refresh", "update", "destroy", "ngOnDestroy", "ɵfac", "ElementRef", "ɵcmp", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "ChartModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,MAArF,EAA6FC,QAA7F,QAA6G,eAA7G;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAOC,KAAP,MAAkB,eAAlB;;AAEA,MAAMC,OAAN,CAAc;AACVC,EAAAA,WAAW,CAACC,EAAD,EAAK;AACZ,SAAKA,EAAL,GAAUA,EAAV;AACA,SAAKC,OAAL,GAAe,EAAf;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKC,YAAL,GAAoB,IAAId,YAAJ,EAApB;AACA,SAAKe,QAAL,GAAgB,EAAhB;AACH;;AACO,MAAJC,IAAI,GAAG;AACP,WAAO,KAAKC,KAAZ;AACH;;AACO,MAAJD,IAAI,CAACE,GAAD,EAAM;AACV,SAAKD,KAAL,GAAaC,GAAb;AACA,SAAKC,MAAL;AACH;;AACU,MAAPC,OAAO,GAAG;AACV,WAAO,KAAKL,QAAZ;AACH;;AACU,MAAPK,OAAO,CAACF,GAAD,EAAM;AACb,SAAKH,QAAL,GAAgBG,GAAhB;AACA,SAAKC,MAAL;AACH;;AACDE,EAAAA,eAAe,GAAG;AACd,SAAKC,SAAL;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACH;;AACDC,EAAAA,aAAa,CAACC,KAAD,EAAQ;AACjB,QAAI,KAAKC,KAAT,EAAgB;AACZ,YAAMC,OAAO,GAAG,KAAKD,KAAL,CAAWE,yBAAX,CAAqCH,KAArC,EAA4C,SAA5C,EAAuD;AAAEI,QAAAA,SAAS,EAAE;AAAb,OAAvD,EAA4E,KAA5E,CAAhB;AACA,YAAMC,OAAO,GAAG,KAAKJ,KAAL,CAAWE,yBAAX,CAAqCH,KAArC,EAA4C,SAA5C,EAAuD;AAAEI,QAAAA,SAAS,EAAE;AAAb,OAAvD,EAA4E,KAA5E,CAAhB;;AACA,UAAIF,OAAO,IAAIA,OAAO,CAAC,CAAD,CAAlB,IAAyBG,OAA7B,EAAsC;AAClC,aAAKhB,YAAL,CAAkBiB,IAAlB,CAAuB;AAAEC,UAAAA,aAAa,EAAEP,KAAjB;AAAwBE,UAAAA,OAAO,EAAEA,OAAO,CAAC,CAAD,CAAxC;AAA6CG,UAAAA,OAAO,EAAEA;AAAtD,SAAvB;AACH;AACJ;AACJ;;AACDR,EAAAA,SAAS,GAAG;AACR,QAAIW,IAAI,GAAG,KAAKb,OAAL,IAAgB,EAA3B;AACAa,IAAAA,IAAI,CAACpB,UAAL,GAAkB,KAAKA,UAAvB,CAFQ,CAGR;;AACA,QAAIoB,IAAI,CAACpB,UAAL,KAAoB,KAAKqB,MAAL,IAAe,KAAKC,KAAxC,CAAJ,EAAoD;AAChDF,MAAAA,IAAI,CAACG,mBAAL,GAA2B,KAA3B;AACH;;AACD,SAAKV,KAAL,GAAa,IAAIlB,KAAJ,CAAU,KAAKG,EAAL,CAAQ0B,aAAR,CAAsBC,QAAtB,CAA+B,CAA/B,EAAkCA,QAAlC,CAA2C,CAA3C,CAAV,EAAyD;AAClEC,MAAAA,IAAI,EAAE,KAAKA,IADuD;AAElEvB,MAAAA,IAAI,EAAE,KAAKA,IAFuD;AAGlEI,MAAAA,OAAO,EAAE,KAAKA,OAHoD;AAIlER,MAAAA,OAAO,EAAE,KAAKA;AAJoD,KAAzD,CAAb;AAMH;;AACD4B,EAAAA,SAAS,GAAG;AACR,WAAO,KAAK7B,EAAL,CAAQ0B,aAAR,CAAsBC,QAAtB,CAA+B,CAA/B,EAAkCA,QAAlC,CAA2C,CAA3C,CAAP;AACH;;AACDG,EAAAA,cAAc,GAAG;AACb,WAAO,KAAKf,KAAL,CAAWgB,aAAX,EAAP;AACH;;AACDC,EAAAA,cAAc,GAAG;AACb,QAAI,KAAKjB,KAAT,EAAgB;AACZ,aAAO,KAAKA,KAAL,CAAWiB,cAAX,EAAP;AACH;AACJ;;AACDC,EAAAA,OAAO,GAAG;AACN,QAAI,KAAKlB,KAAT,EAAgB;AACZ,WAAKA,KAAL,CAAWmB,MAAX;AACH;AACJ;;AACD1B,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKO,KAAT,EAAgB;AACZ,WAAKA,KAAL,CAAWoB,OAAX;AACA,WAAKxB,SAAL;AACH;AACJ;;AACDyB,EAAAA,WAAW,GAAG;AACV,QAAI,KAAKrB,KAAT,EAAgB;AACZ,WAAKA,KAAL,CAAWoB,OAAX;AACA,WAAKvB,WAAL,GAAmB,KAAnB;AACA,WAAKG,KAAL,GAAa,IAAb;AACH;AACJ;;AA7ES;;AA+EdjB,OAAO,CAACuC,IAAR;AAAA,mBAAoGvC,OAApG,EAA0FV,EAA1F,mBAA6HA,EAAE,CAACkD,UAAhI;AAAA;;AACAxC,OAAO,CAACyC,IAAR,kBAD0FnD,EAC1F;AAAA,QAAwFU,OAAxF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAD0FV,MAAAA,EAElF,4BADR;AAD0FA,MAAAA,EAG9E,+BAFZ;AAD0FA,MAAAA,EAGmC;AAAA,eAAS,yBAAT;AAAA,QAF7H;AAD0FA,MAAAA,EAGmE,eAF7J;AAD0FA,MAAAA,EAIlF,eAHR;AAAA;;AAAA;AAD0FA,MAAAA,EAEnD,mIADvC;AAD0FA,MAAAA,EAGtE,aAFpB;AAD0FA,MAAAA,EAGtE,mIAFpB;AAAA;AAAA;AAAA;AAAA;AAAA;;AAKA;AAAA,qDAN0FA,EAM1F,mBAA2FU,OAA3F,EAAgH,CAAC;AACrG8B,IAAAA,IAAI,EAAEtC,SAD+F;AAErGkD,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,SADX;AAECC,MAAAA,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,KANmB;AAOCC,MAAAA,eAAe,EAAEpD,uBAAuB,CAACqD,MAP1C;AAQCC,MAAAA,aAAa,EAAErD,iBAAiB,CAACsD,IARlC;AASCC,MAAAA,IAAI,EAAE;AACF,iBAAS;AADP;AATP,KAAD;AAF+F,GAAD,CAAhH,EAe4B,YAAY;AAAE,WAAO,CAAC;AAAEnB,MAAAA,IAAI,EAAExC,EAAE,CAACkD;AAAX,KAAD,CAAP;AAAmC,GAf7E,EAe+F;AAAEV,IAAAA,IAAI,EAAE,CAAC;AACxFA,MAAAA,IAAI,EAAEnC;AADkF,KAAD,CAAR;AAE/EQ,IAAAA,OAAO,EAAE,CAAC;AACV2B,MAAAA,IAAI,EAAEnC;AADI,KAAD,CAFsE;AAI/E+B,IAAAA,KAAK,EAAE,CAAC;AACRI,MAAAA,IAAI,EAAEnC;AADE,KAAD,CAJwE;AAM/E8B,IAAAA,MAAM,EAAE,CAAC;AACTK,MAAAA,IAAI,EAAEnC;AADG,KAAD,CANuE;AAQ/ES,IAAAA,UAAU,EAAE,CAAC;AACb0B,MAAAA,IAAI,EAAEnC;AADO,KAAD,CARmE;AAU/EU,IAAAA,YAAY,EAAE,CAAC;AACfyB,MAAAA,IAAI,EAAElC;AADS,KAAD,CAViE;AAY/EW,IAAAA,IAAI,EAAE,CAAC;AACPuB,MAAAA,IAAI,EAAEnC;AADC,KAAD,CAZyE;AAc/EgB,IAAAA,OAAO,EAAE,CAAC;AACVmB,MAAAA,IAAI,EAAEnC;AADI,KAAD;AAdsE,GAf/F;AAAA;;AAgCA,MAAMuD,WAAN,CAAkB;;AAElBA,WAAW,CAACX,IAAZ;AAAA,mBAAwGW,WAAxG;AAAA;;AACAA,WAAW,CAACC,IAAZ,kBAzC0F7D,EAyC1F;AAAA,QAAyG4D;AAAzG;AACAA,WAAW,CAACE,IAAZ,kBA1C0F9D,EA0C1F;AAAA,YAAgI,CAACQ,YAAD,CAAhI;AAAA;;AACA;AAAA,qDA3C0FR,EA2C1F,mBAA2F4D,WAA3F,EAAoH,CAAC;AACzGpB,IAAAA,IAAI,EAAEjC,QADmG;AAEzG6C,IAAAA,IAAI,EAAE,CAAC;AACCW,MAAAA,OAAO,EAAE,CAACvD,YAAD,CADV;AAECwD,MAAAA,OAAO,EAAE,CAACtD,OAAD,CAFV;AAGCuD,MAAAA,YAAY,EAAE,CAACvD,OAAD;AAHf,KAAD;AAFmG,GAAD,CAApH;AAAA;AASA;AACA;AACA;;;AAEA,SAASkD,WAAT,EAAsBlD,OAAtB", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport Chart from 'chart.js/auto';\n\nclass UIChart {\n    constructor(el) {\n        this.el = el;\n        this.plugins = [];\n        this.responsive = true;\n        this.onDataSelect = new EventEmitter();\n        this._options = {};\n    }\n    get data() {\n        return this._data;\n    }\n    set data(val) {\n        this._data = val;\n        this.reinit();\n    }\n    get options() {\n        return this._options;\n    }\n    set options(val) {\n        this._options = val;\n        this.reinit();\n    }\n    ngAfterViewInit() {\n        this.initChart();\n        this.initialized = true;\n    }\n    onCanvasClick(event) {\n        if (this.chart) {\n            const element = this.chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, false);\n            const dataset = this.chart.getElementsAtEventForMode(event, 'dataset', { intersect: true }, false);\n            if (element && element[0] && dataset) {\n                this.onDataSelect.emit({ originalEvent: event, element: element[0], dataset: dataset });\n            }\n        }\n    }\n    initChart() {\n        let opts = this.options || {};\n        opts.responsive = this.responsive;\n        // allows chart to resize in responsive mode\n        if (opts.responsive && (this.height || this.width)) {\n            opts.maintainAspectRatio = false;\n        }\n        this.chart = new Chart(this.el.nativeElement.children[0].children[0], {\n            type: this.type,\n            data: this.data,\n            options: this.options,\n            plugins: this.plugins\n        });\n    }\n    getCanvas() {\n        return this.el.nativeElement.children[0].children[0];\n    }\n    getBase64Image() {\n        return this.chart.toBase64Image();\n    }\n    generateLegend() {\n        if (this.chart) {\n            return this.chart.generateLegend();\n        }\n    }\n    refresh() {\n        if (this.chart) {\n            this.chart.update();\n        }\n    }\n    reinit() {\n        if (this.chart) {\n            this.chart.destroy();\n            this.initChart();\n        }\n    }\n    ngOnDestroy() {\n        if (this.chart) {\n            this.chart.destroy();\n            this.initialized = false;\n            this.chart = null;\n        }\n    }\n}\nUIChart.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: UIChart, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nUIChart.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"12.0.5\", type: UIChart, selector: \"p-chart\", inputs: { type: \"type\", plugins: \"plugins\", width: \"width\", height: \"height\", responsive: \"responsive\", data: \"data\", options: \"options\" }, outputs: { onDataSelect: \"onDataSelect\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div style=\"position:relative\" [style.width]=\"responsive && !width ? null : width\" [style.height]=\"responsive && !height ? null : height\">\n            <canvas [attr.width]=\"responsive && !width ? null : width\" [attr.height]=\"responsive && !height ? null : height\" (click)=\"onCanvasClick($event)\"></canvas>\n        </div>\n    `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: UIChart, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-chart',\n                    template: `\n        <div style=\"position:relative\" [style.width]=\"responsive && !width ? null : width\" [style.height]=\"responsive && !height ? null : height\">\n            <canvas [attr.width]=\"responsive && !width ? null : width\" [attr.height]=\"responsive && !height ? null : height\" (click)=\"onCanvasClick($event)\"></canvas>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { type: [{\n                type: Input\n            }], plugins: [{\n                type: Input\n            }], width: [{\n                type: Input\n            }], height: [{\n                type: Input\n            }], responsive: [{\n                type: Input\n            }], onDataSelect: [{\n                type: Output\n            }], data: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }] } });\nclass ChartModule {\n}\nChartModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: ChartModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nChartModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: ChartModule, declarations: [UIChart], imports: [CommonModule], exports: [UIChart] });\nChartModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: ChartModule, imports: [[CommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.0.5\", ngImport: i0, type: ChartModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [UIChart],\n                    declarations: [UIChart]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ChartModule, UIChart };\n"]}, "metadata": {}, "sourceType": "module"}
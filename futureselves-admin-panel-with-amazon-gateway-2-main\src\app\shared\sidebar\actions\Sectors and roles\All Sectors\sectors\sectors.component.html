<app-sidebar>
    <div class="content-wrapper fade-in">

      <div class="row mb-4 head-Home">
          <!-- Add and search bar -->
          <div class="col-lg-3 mb-2 mb-lg-0">
            <button type="submit" (click)="showAddNewSector()" class="btn btn-primary w-100">Add New Sector</button>
          </div>
          <div class="col-lg-6">
            <div class="input-group">
              <input type="text" [(ngModel)]="term" class="form-control shadow-sm rounded-start" placeholder="Search here"
                style="width: 100%;" aria-label="Search now">
            </div>
          </div>
        </div>  

        <div class="row" *ngIf="showForm">
            <div class="col-lg-12 grid-margin stretch-card">
              <div class="card">
                <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">All Sectors</div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-sm table-hover">
                      <thead class="text-center">
                        <tr>
                          <th class="text-left">Sector Name</th>
                          <th>Sector Description</th>
                          <th>Sector Image</th>
                          <th class="text-center">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <!-- <tr *ngFor="let Collection of collectionList|filter: term1 |paginate : { itemsPerPage:10, currentPage:p1}"> -->
                        <tr *ngFor="let data of SectorData | filter: term|paginate: {itemsPerPage:7, currentPage:p}" >
                          <td>
                            <div class="description-content">{{data.IN_name}}</div>
                          </td>
                    
                          <td>
                            <div class="description-content">{{data.IN_description}}</div>
                          </td>
                          
                          <td class="text-center">
                            <img [src]="data.IN_dp" class="card-img-top square-img" alt="Sector Image">
                          </td>
                          <td class="text-center">
                            <div class="btn-group" role="group" aria-label="Basic example">
                              <button type="button" (click)="showEditSector(data.IN_id,data)" class="btn btn-primary btn-sm" 
                                placement="top" ngbTooltip="Update">
                                <i class="ti-pencil"></i>
                              </button>
                              <button type="button" (click)="showDeleteSectorModal(data.IN_id)" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#myModal"
                                placement="top" ngbTooltip="Delete">
                                <i class="ti-trash"></i>
                              </button>
                              <button type="button" (click)="showRolesForm(data.IN_id)" class="btn btn-primary btn-sm" 
                              placement="top" ngbTooltip="Roles">
                              <i class="fa-solid fa-id-card"></i>
                            </button>
                            </div>
                          </td>
                        </tr>
                          <!--Division for Modal-->
                         

                      </tbody>
                    </table>
                    <pagination-controls (pageChange)="p = $event"
                    class="ml-1 text-center"></pagination-controls>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>

        </app-sidebar>
    
        
        <div id="myModal" class="modal fade" role="dialog">
          <!--Modal-->
          <div class="modal-dialog">
            <!--Modal Content-->
            <div class="modal-content">
              <!-- Modal Header-->
              <div class="modal-header">
                <h3>Delete warning !</h3>
                <!--Close/Cross Button-->
                <button type="button" class="close" data-dismiss="modal"
                  style="color: white;">&times;</button>
              </div>
              <!-- Modal Body-->
              <div class="modal-body text-center">
                <i class="ti-trash" style="color: red;"></i>
                <h3> <strong>Are you sure?</strong></h3>
                <p class="mt-3">Do you really want to delete this sector?</p>
              </div>

              <!-- Modal Footer-->

              <div class="modal-footer text-center">
                <button  class="btn btn-danger" (click)="deleteSector()" data-dismiss="modal">Delete</button>
                <a href="" class="btn btn-primary" data-dismiss="modal">Cancel</a>
              </div>

            </div>

          </div>

        </div>
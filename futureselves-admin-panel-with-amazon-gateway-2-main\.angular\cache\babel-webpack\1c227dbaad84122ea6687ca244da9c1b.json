{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, Injectable, EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Output, ViewChild, InjectionToken, forwardRef, TemplateRef, Attribute, HostBinding, ContentChild, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { takeUntil, auditTime, startWith, tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { animationFrameScheduler, asapScheduler, Subject, fromEvent, merge } from 'rxjs';\nimport * as i4 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nconst _c0 = [\"content\"];\nconst _c1 = [\"scroll\"];\nconst _c2 = [\"padding\"];\n\nconst _c3 = function (a0) {\n  return {\n    searchTerm: a0\n  };\n};\n\nfunction NgDropdownPanelComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelementContainer(1, 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, ctx_r0.filterValue));\n  }\n}\n\nfunction NgDropdownPanelComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelementContainer(1, 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, ctx_r4.filterValue));\n  }\n}\n\nconst _c4 = [\"*\"];\nconst _c5 = [\"searchInput\"];\n\nfunction NgSelectComponent_ng_container_4_div_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵlistener(\"click\", function NgSelectComponent_ng_container_4_div_1_ng_template_1_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const item_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return ctx_r11.unselect(item_r7);\n    });\n    i0.ɵɵtext(1, \"\\xD7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"span\", 16);\n  }\n\n  if (rf & 2) {\n    const item_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngItemLabel\", item_r7.label)(\"escape\", ctx_r9.escapeHTML);\n  }\n}\n\nfunction NgSelectComponent_ng_container_4_div_1_ng_template_3_Template(rf, ctx) {}\n\nconst _c6 = function (a0, a1, a2) {\n  return {\n    item: a0,\n    clear: a1,\n    label: a2\n  };\n};\n\nfunction NgSelectComponent_ng_container_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_container_4_div_1_ng_template_1_Template, 3, 2, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(3, NgSelectComponent_ng_container_4_div_1_ng_template_3_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r7 = ctx.$implicit;\n\n    const _r8 = i0.ɵɵreference(2);\n\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ng-value-disabled\", item_r7.disabled);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r6.labelTemplate || _r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(4, _c6, item_r7.value, ctx_r6.clearItem, item_r7.label));\n  }\n}\n\nfunction NgSelectComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_container_4_div_1_Template, 4, 8, \"div\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedItems)(\"ngForTrackBy\", ctx_r0.trackByOption);\n  }\n}\n\nfunction NgSelectComponent_5_ng_template_0_Template(rf, ctx) {}\n\nconst _c7 = function (a0, a1) {\n  return {\n    items: a0,\n    clear: a1\n  };\n};\n\nfunction NgSelectComponent_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NgSelectComponent_5_ng_template_0_Template, 0, 0, \"ng-template\", 14);\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.multiLabelTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c7, ctx_r1.selectedValues, ctx_r1.clearItem));\n  }\n}\n\nfunction NgSelectComponent_ng_container_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 19);\n  }\n}\n\nfunction NgSelectComponent_ng_container_9_ng_template_3_Template(rf, ctx) {}\n\nfunction NgSelectComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_container_9_ng_template_1_Template, 1, 0, \"ng-template\", null, 17, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(3, NgSelectComponent_ng_container_9_ng_template_3_Template, 0, 0, \"ng-template\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r16 = i0.ɵɵreference(2);\n\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.loadingSpinnerTemplate || _r16);\n  }\n}\n\nfunction NgSelectComponent_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵelementStart(1, \"span\", 21);\n    i0.ɵɵtext(2, \"\\xD7\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r4.clearAllText);\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 27);\n  }\n\n  if (rf & 2) {\n    const item_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r26 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngItemLabel\", item_r24.label)(\"escape\", ctx_r26.escapeHTML);\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_3_Template(rf, ctx) {}\n\nconst _c8 = function (a0, a1, a2, a3) {\n  return {\n    item: a0,\n    item$: a1,\n    index: a2,\n    searchTerm: a3\n  };\n};\n\nfunction NgSelectComponent_ng_dropdown_panel_13_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function NgSelectComponent_ng_dropdown_panel_13_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const item_r24 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return ctx_r29.toggleItem(item_r24);\n    })(\"mouseover\", function NgSelectComponent_ng_dropdown_panel_13_div_2_Template_div_mouseover_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const item_r24 = restoredCtx.$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return ctx_r31.onItemHover(item_r24);\n    });\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_1_Template, 1, 2, \"ng-template\", null, 26, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(3, NgSelectComponent_ng_dropdown_panel_13_div_2_ng_template_3_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r24 = ctx.$implicit;\n\n    const _r25 = i0.ɵɵreference(2);\n\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ng-option-disabled\", item_r24.disabled)(\"ng-option-selected\", item_r24.selected)(\"ng-optgroup\", item_r24.children)(\"ng-option\", !item_r24.children)(\"ng-option-child\", !!item_r24.parent)(\"ng-option-marked\", item_r24 === ctx_r19.itemsList.markedItem);\n    i0.ɵɵattribute(\"role\", item_r24.children ? \"group\" : \"option\")(\"aria-selected\", item_r24.selected)(\"id\", item_r24 == null ? null : item_r24.htmlId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", item_r24.children ? ctx_r19.optgroupTemplate || _r25 : ctx_r19.optionTemplate || _r25)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction4(17, _c8, item_r24.value, item_r24, item_r24.index, ctx_r19.searchTerm));\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelementStart(1, \"span\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r33.addTagText);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\\\"\", ctx_r33.searchTerm, \"\\\"\");\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_3_Template(rf, ctx) {}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵlistener(\"mouseover\", function NgSelectComponent_ng_dropdown_panel_13_div_3_Template_div_mouseover_0_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return ctx_r35.itemsList.unmarkItem();\n    })(\"click\", function NgSelectComponent_ng_dropdown_panel_13_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return ctx_r37.selectTag();\n    });\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_1_Template, 4, 2, \"ng-template\", null, 29, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(3, NgSelectComponent_ng_dropdown_panel_13_div_3_ng_template_3_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r32 = i0.ɵɵreference(2);\n\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ng-option-marked\", !ctx_r20.itemsList.markedItem);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r20.tagTemplate || _r32)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c3, ctx_r20.searchTerm));\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r39.notFoundText);\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_3_Template(rf, ctx) {}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_1_Template, 2, 1, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(3, NgSelectComponent_ng_dropdown_panel_13_ng_container_4_ng_template_3_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r38 = i0.ɵɵreference(2);\n\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r21.notFoundTemplate || _r38)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, ctx_r21.searchTerm));\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r42.typeToSearchText);\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_3_Template(rf, ctx) {}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_1_Template, 2, 1, \"ng-template\", null, 33, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(3, NgSelectComponent_ng_dropdown_panel_13_ng_container_5_ng_template_3_Template, 0, 0, \"ng-template\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r41 = i0.ɵɵreference(2);\n\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r22.typeToSearchTemplate || _r41);\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r45.loadingText);\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_3_Template(rf, ctx) {}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_1_Template, 2, 1, \"ng-template\", null, 34, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(3, NgSelectComponent_ng_dropdown_panel_13_ng_container_6_ng_template_3_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r44 = i0.ɵɵreference(2);\n\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r23.loadingTextTemplate || _r44)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, ctx_r23.searchTerm));\n  }\n}\n\nfunction NgSelectComponent_ng_dropdown_panel_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ng-dropdown-panel\", 22);\n    i0.ɵɵlistener(\"update\", function NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_update_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return ctx_r47.viewPortItems = $event;\n    })(\"scroll\", function NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return ctx_r49.scroll.emit($event);\n    })(\"scrollToEnd\", function NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_scrollToEnd_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return ctx_r50.scrollToEnd.emit($event);\n    })(\"outsideClick\", function NgSelectComponent_ng_dropdown_panel_13_Template_ng_dropdown_panel_outsideClick_0_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return ctx_r51.close();\n    });\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵtemplate(2, NgSelectComponent_ng_dropdown_panel_13_div_2_Template, 4, 22, \"div\", 23);\n    i0.ɵɵtemplate(3, NgSelectComponent_ng_dropdown_panel_13_div_3_Template, 4, 6, \"div\", 24);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(4, NgSelectComponent_ng_dropdown_panel_13_ng_container_4_Template, 4, 4, \"ng-container\", 3);\n    i0.ɵɵtemplate(5, NgSelectComponent_ng_dropdown_panel_13_ng_container_5_Template, 4, 1, \"ng-container\", 3);\n    i0.ɵɵtemplate(6, NgSelectComponent_ng_dropdown_panel_13_ng_container_6_Template, 4, 4, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ng-select-multiple\", ctx_r5.multiple);\n    i0.ɵɵproperty(\"virtualScroll\", ctx_r5.virtualScroll)(\"bufferAmount\", ctx_r5.bufferAmount)(\"appendTo\", ctx_r5.appendTo)(\"position\", ctx_r5.dropdownPosition)(\"headerTemplate\", ctx_r5.headerTemplate)(\"footerTemplate\", ctx_r5.footerTemplate)(\"filterValue\", ctx_r5.searchTerm)(\"items\", ctx_r5.itemsList.filteredItems)(\"markedItem\", ctx_r5.itemsList.markedItem)(\"ngClass\", ctx_r5.appendTo ? ctx_r5.classes : null)(\"id\", ctx_r5.dropdownId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.viewPortItems)(\"ngForTrackBy\", ctx_r5.trackByOption);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showAddTag);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showNoItemsFound());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.showTypeToSearch());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loading && ctx_r5.itemsList.filteredItems.length === 0);\n  }\n}\n\nconst unescapedHTMLExp = /[&<>\"']/g;\nconst hasUnescapedHTMLExp = RegExp(unescapedHTMLExp.source);\nconst htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  '\\'': '&#39;'\n};\n\nfunction escapeHTML(value) {\n  return value && hasUnescapedHTMLExp.test(value) ? value.replace(unescapedHTMLExp, chr => htmlEscapes[chr]) : value;\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n\nfunction isObject(value) {\n  return typeof value === 'object' && isDefined(value);\n}\n\nfunction isPromise(value) {\n  return value instanceof Promise;\n}\n\nfunction isFunction(value) {\n  return value instanceof Function;\n}\n\nclass NgItemLabelDirective {\n  constructor(element) {\n    this.element = element;\n    this.escape = true;\n  }\n\n  ngOnChanges(changes) {\n    this.element.nativeElement.innerHTML = this.escape ? escapeHTML(this.ngItemLabel) : this.ngItemLabel;\n  }\n\n}\n\nNgItemLabelDirective.ɵfac = function NgItemLabelDirective_Factory(t) {\n  return new (t || NgItemLabelDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nNgItemLabelDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgItemLabelDirective,\n  selectors: [[\"\", \"ngItemLabel\", \"\"]],\n  inputs: {\n    ngItemLabel: \"ngItemLabel\",\n    escape: \"escape\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgItemLabelDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ngItemLabel]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    ngItemLabel: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input\n    }]\n  });\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgOptionTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgOptionTemplateDirective.ɵfac = function NgOptionTemplateDirective_Factory(t) {\n  return new (t || NgOptionTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgOptionTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgOptionTemplateDirective,\n  selectors: [[\"\", \"ng-option-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptionTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-option-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgOptgroupTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgOptgroupTemplateDirective.ɵfac = function NgOptgroupTemplateDirective_Factory(t) {\n  return new (t || NgOptgroupTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgOptgroupTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgOptgroupTemplateDirective,\n  selectors: [[\"\", \"ng-optgroup-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptgroupTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-optgroup-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgLabelTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgLabelTemplateDirective.ɵfac = function NgLabelTemplateDirective_Factory(t) {\n  return new (t || NgLabelTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgLabelTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgLabelTemplateDirective,\n  selectors: [[\"\", \"ng-label-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLabelTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-label-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgMultiLabelTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgMultiLabelTemplateDirective.ɵfac = function NgMultiLabelTemplateDirective_Factory(t) {\n  return new (t || NgMultiLabelTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgMultiLabelTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgMultiLabelTemplateDirective,\n  selectors: [[\"\", \"ng-multi-label-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgMultiLabelTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-multi-label-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgHeaderTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgHeaderTemplateDirective.ɵfac = function NgHeaderTemplateDirective_Factory(t) {\n  return new (t || NgHeaderTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgHeaderTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgHeaderTemplateDirective,\n  selectors: [[\"\", \"ng-header-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgHeaderTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-header-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgFooterTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgFooterTemplateDirective.ɵfac = function NgFooterTemplateDirective_Factory(t) {\n  return new (t || NgFooterTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgFooterTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgFooterTemplateDirective,\n  selectors: [[\"\", \"ng-footer-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgFooterTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-footer-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgNotFoundTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgNotFoundTemplateDirective.ɵfac = function NgNotFoundTemplateDirective_Factory(t) {\n  return new (t || NgNotFoundTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgNotFoundTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgNotFoundTemplateDirective,\n  selectors: [[\"\", \"ng-notfound-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgNotFoundTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-notfound-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgTypeToSearchTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgTypeToSearchTemplateDirective.ɵfac = function NgTypeToSearchTemplateDirective_Factory(t) {\n  return new (t || NgTypeToSearchTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgTypeToSearchTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgTypeToSearchTemplateDirective,\n  selectors: [[\"\", \"ng-typetosearch-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgTypeToSearchTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-typetosearch-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgLoadingTextTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgLoadingTextTemplateDirective.ɵfac = function NgLoadingTextTemplateDirective_Factory(t) {\n  return new (t || NgLoadingTextTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgLoadingTextTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgLoadingTextTemplateDirective,\n  selectors: [[\"\", \"ng-loadingtext-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLoadingTextTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-loadingtext-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgTagTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgTagTemplateDirective.ɵfac = function NgTagTemplateDirective_Factory(t) {\n  return new (t || NgTagTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgTagTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgTagTemplateDirective,\n  selectors: [[\"\", \"ng-tag-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgTagTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-tag-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})(); // eslint-disable-next-line @angular-eslint/directive-selector\n\n\nclass NgLoadingSpinnerTemplateDirective {\n  constructor(template) {\n    this.template = template;\n  }\n\n}\n\nNgLoadingSpinnerTemplateDirective.ɵfac = function NgLoadingSpinnerTemplateDirective_Factory(t) {\n  return new (t || NgLoadingSpinnerTemplateDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nNgLoadingSpinnerTemplateDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: NgLoadingSpinnerTemplateDirective,\n  selectors: [[\"\", \"ng-loadingspinner-tmp\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgLoadingSpinnerTemplateDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ng-loadingspinner-tmp]'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, null);\n})();\n\nfunction newId() {\n  // First character is an 'a', it's good practice to tag id to begin with a letter\n  return 'axxxxxxxxxxx'.replace(/[x]/g, () => {\n    // eslint-disable-next-line no-bitwise\n    const val = Math.random() * 16 | 0;\n    return val.toString(16);\n  });\n}\n\nconst diacritics = {\n  '\\u24B6': 'A',\n  '\\uFF21': 'A',\n  '\\u00C0': 'A',\n  '\\u00C1': 'A',\n  '\\u00C2': 'A',\n  '\\u1EA6': 'A',\n  '\\u1EA4': 'A',\n  '\\u1EAA': 'A',\n  '\\u1EA8': 'A',\n  '\\u00C3': 'A',\n  '\\u0100': 'A',\n  '\\u0102': 'A',\n  '\\u1EB0': 'A',\n  '\\u1EAE': 'A',\n  '\\u1EB4': 'A',\n  '\\u1EB2': 'A',\n  '\\u0226': 'A',\n  '\\u01E0': 'A',\n  '\\u00C4': 'A',\n  '\\u01DE': 'A',\n  '\\u1EA2': 'A',\n  '\\u00C5': 'A',\n  '\\u01FA': 'A',\n  '\\u01CD': 'A',\n  '\\u0200': 'A',\n  '\\u0202': 'A',\n  '\\u1EA0': 'A',\n  '\\u1EAC': 'A',\n  '\\u1EB6': 'A',\n  '\\u1E00': 'A',\n  '\\u0104': 'A',\n  '\\u023A': 'A',\n  '\\u2C6F': 'A',\n  '\\uA732': 'AA',\n  '\\u00C6': 'AE',\n  '\\u01FC': 'AE',\n  '\\u01E2': 'AE',\n  '\\uA734': 'AO',\n  '\\uA736': 'AU',\n  '\\uA738': 'AV',\n  '\\uA73A': 'AV',\n  '\\uA73C': 'AY',\n  '\\u24B7': 'B',\n  '\\uFF22': 'B',\n  '\\u1E02': 'B',\n  '\\u1E04': 'B',\n  '\\u1E06': 'B',\n  '\\u0243': 'B',\n  '\\u0182': 'B',\n  '\\u0181': 'B',\n  '\\u24B8': 'C',\n  '\\uFF23': 'C',\n  '\\u0106': 'C',\n  '\\u0108': 'C',\n  '\\u010A': 'C',\n  '\\u010C': 'C',\n  '\\u00C7': 'C',\n  '\\u1E08': 'C',\n  '\\u0187': 'C',\n  '\\u023B': 'C',\n  '\\uA73E': 'C',\n  '\\u24B9': 'D',\n  '\\uFF24': 'D',\n  '\\u1E0A': 'D',\n  '\\u010E': 'D',\n  '\\u1E0C': 'D',\n  '\\u1E10': 'D',\n  '\\u1E12': 'D',\n  '\\u1E0E': 'D',\n  '\\u0110': 'D',\n  '\\u018B': 'D',\n  '\\u018A': 'D',\n  '\\u0189': 'D',\n  '\\uA779': 'D',\n  '\\u01F1': 'DZ',\n  '\\u01C4': 'DZ',\n  '\\u01F2': 'Dz',\n  '\\u01C5': 'Dz',\n  '\\u24BA': 'E',\n  '\\uFF25': 'E',\n  '\\u00C8': 'E',\n  '\\u00C9': 'E',\n  '\\u00CA': 'E',\n  '\\u1EC0': 'E',\n  '\\u1EBE': 'E',\n  '\\u1EC4': 'E',\n  '\\u1EC2': 'E',\n  '\\u1EBC': 'E',\n  '\\u0112': 'E',\n  '\\u1E14': 'E',\n  '\\u1E16': 'E',\n  '\\u0114': 'E',\n  '\\u0116': 'E',\n  '\\u00CB': 'E',\n  '\\u1EBA': 'E',\n  '\\u011A': 'E',\n  '\\u0204': 'E',\n  '\\u0206': 'E',\n  '\\u1EB8': 'E',\n  '\\u1EC6': 'E',\n  '\\u0228': 'E',\n  '\\u1E1C': 'E',\n  '\\u0118': 'E',\n  '\\u1E18': 'E',\n  '\\u1E1A': 'E',\n  '\\u0190': 'E',\n  '\\u018E': 'E',\n  '\\u24BB': 'F',\n  '\\uFF26': 'F',\n  '\\u1E1E': 'F',\n  '\\u0191': 'F',\n  '\\uA77B': 'F',\n  '\\u24BC': 'G',\n  '\\uFF27': 'G',\n  '\\u01F4': 'G',\n  '\\u011C': 'G',\n  '\\u1E20': 'G',\n  '\\u011E': 'G',\n  '\\u0120': 'G',\n  '\\u01E6': 'G',\n  '\\u0122': 'G',\n  '\\u01E4': 'G',\n  '\\u0193': 'G',\n  '\\uA7A0': 'G',\n  '\\uA77D': 'G',\n  '\\uA77E': 'G',\n  '\\u24BD': 'H',\n  '\\uFF28': 'H',\n  '\\u0124': 'H',\n  '\\u1E22': 'H',\n  '\\u1E26': 'H',\n  '\\u021E': 'H',\n  '\\u1E24': 'H',\n  '\\u1E28': 'H',\n  '\\u1E2A': 'H',\n  '\\u0126': 'H',\n  '\\u2C67': 'H',\n  '\\u2C75': 'H',\n  '\\uA78D': 'H',\n  '\\u24BE': 'I',\n  '\\uFF29': 'I',\n  '\\u00CC': 'I',\n  '\\u00CD': 'I',\n  '\\u00CE': 'I',\n  '\\u0128': 'I',\n  '\\u012A': 'I',\n  '\\u012C': 'I',\n  '\\u0130': 'I',\n  '\\u00CF': 'I',\n  '\\u1E2E': 'I',\n  '\\u1EC8': 'I',\n  '\\u01CF': 'I',\n  '\\u0208': 'I',\n  '\\u020A': 'I',\n  '\\u1ECA': 'I',\n  '\\u012E': 'I',\n  '\\u1E2C': 'I',\n  '\\u0197': 'I',\n  '\\u24BF': 'J',\n  '\\uFF2A': 'J',\n  '\\u0134': 'J',\n  '\\u0248': 'J',\n  '\\u24C0': 'K',\n  '\\uFF2B': 'K',\n  '\\u1E30': 'K',\n  '\\u01E8': 'K',\n  '\\u1E32': 'K',\n  '\\u0136': 'K',\n  '\\u1E34': 'K',\n  '\\u0198': 'K',\n  '\\u2C69': 'K',\n  '\\uA740': 'K',\n  '\\uA742': 'K',\n  '\\uA744': 'K',\n  '\\uA7A2': 'K',\n  '\\u24C1': 'L',\n  '\\uFF2C': 'L',\n  '\\u013F': 'L',\n  '\\u0139': 'L',\n  '\\u013D': 'L',\n  '\\u1E36': 'L',\n  '\\u1E38': 'L',\n  '\\u013B': 'L',\n  '\\u1E3C': 'L',\n  '\\u1E3A': 'L',\n  '\\u0141': 'L',\n  '\\u023D': 'L',\n  '\\u2C62': 'L',\n  '\\u2C60': 'L',\n  '\\uA748': 'L',\n  '\\uA746': 'L',\n  '\\uA780': 'L',\n  '\\u01C7': 'LJ',\n  '\\u01C8': 'Lj',\n  '\\u24C2': 'M',\n  '\\uFF2D': 'M',\n  '\\u1E3E': 'M',\n  '\\u1E40': 'M',\n  '\\u1E42': 'M',\n  '\\u2C6E': 'M',\n  '\\u019C': 'M',\n  '\\u24C3': 'N',\n  '\\uFF2E': 'N',\n  '\\u01F8': 'N',\n  '\\u0143': 'N',\n  '\\u00D1': 'N',\n  '\\u1E44': 'N',\n  '\\u0147': 'N',\n  '\\u1E46': 'N',\n  '\\u0145': 'N',\n  '\\u1E4A': 'N',\n  '\\u1E48': 'N',\n  '\\u0220': 'N',\n  '\\u019D': 'N',\n  '\\uA790': 'N',\n  '\\uA7A4': 'N',\n  '\\u01CA': 'NJ',\n  '\\u01CB': 'Nj',\n  '\\u24C4': 'O',\n  '\\uFF2F': 'O',\n  '\\u00D2': 'O',\n  '\\u00D3': 'O',\n  '\\u00D4': 'O',\n  '\\u1ED2': 'O',\n  '\\u1ED0': 'O',\n  '\\u1ED6': 'O',\n  '\\u1ED4': 'O',\n  '\\u00D5': 'O',\n  '\\u1E4C': 'O',\n  '\\u022C': 'O',\n  '\\u1E4E': 'O',\n  '\\u014C': 'O',\n  '\\u1E50': 'O',\n  '\\u1E52': 'O',\n  '\\u014E': 'O',\n  '\\u022E': 'O',\n  '\\u0230': 'O',\n  '\\u00D6': 'O',\n  '\\u022A': 'O',\n  '\\u1ECE': 'O',\n  '\\u0150': 'O',\n  '\\u01D1': 'O',\n  '\\u020C': 'O',\n  '\\u020E': 'O',\n  '\\u01A0': 'O',\n  '\\u1EDC': 'O',\n  '\\u1EDA': 'O',\n  '\\u1EE0': 'O',\n  '\\u1EDE': 'O',\n  '\\u1EE2': 'O',\n  '\\u1ECC': 'O',\n  '\\u1ED8': 'O',\n  '\\u01EA': 'O',\n  '\\u01EC': 'O',\n  '\\u00D8': 'O',\n  '\\u01FE': 'O',\n  '\\u0186': 'O',\n  '\\u019F': 'O',\n  '\\uA74A': 'O',\n  '\\uA74C': 'O',\n  '\\u01A2': 'OI',\n  '\\uA74E': 'OO',\n  '\\u0222': 'OU',\n  '\\u24C5': 'P',\n  '\\uFF30': 'P',\n  '\\u1E54': 'P',\n  '\\u1E56': 'P',\n  '\\u01A4': 'P',\n  '\\u2C63': 'P',\n  '\\uA750': 'P',\n  '\\uA752': 'P',\n  '\\uA754': 'P',\n  '\\u24C6': 'Q',\n  '\\uFF31': 'Q',\n  '\\uA756': 'Q',\n  '\\uA758': 'Q',\n  '\\u024A': 'Q',\n  '\\u24C7': 'R',\n  '\\uFF32': 'R',\n  '\\u0154': 'R',\n  '\\u1E58': 'R',\n  '\\u0158': 'R',\n  '\\u0210': 'R',\n  '\\u0212': 'R',\n  '\\u1E5A': 'R',\n  '\\u1E5C': 'R',\n  '\\u0156': 'R',\n  '\\u1E5E': 'R',\n  '\\u024C': 'R',\n  '\\u2C64': 'R',\n  '\\uA75A': 'R',\n  '\\uA7A6': 'R',\n  '\\uA782': 'R',\n  '\\u24C8': 'S',\n  '\\uFF33': 'S',\n  '\\u1E9E': 'S',\n  '\\u015A': 'S',\n  '\\u1E64': 'S',\n  '\\u015C': 'S',\n  '\\u1E60': 'S',\n  '\\u0160': 'S',\n  '\\u1E66': 'S',\n  '\\u1E62': 'S',\n  '\\u1E68': 'S',\n  '\\u0218': 'S',\n  '\\u015E': 'S',\n  '\\u2C7E': 'S',\n  '\\uA7A8': 'S',\n  '\\uA784': 'S',\n  '\\u24C9': 'T',\n  '\\uFF34': 'T',\n  '\\u1E6A': 'T',\n  '\\u0164': 'T',\n  '\\u1E6C': 'T',\n  '\\u021A': 'T',\n  '\\u0162': 'T',\n  '\\u1E70': 'T',\n  '\\u1E6E': 'T',\n  '\\u0166': 'T',\n  '\\u01AC': 'T',\n  '\\u01AE': 'T',\n  '\\u023E': 'T',\n  '\\uA786': 'T',\n  '\\uA728': 'TZ',\n  '\\u24CA': 'U',\n  '\\uFF35': 'U',\n  '\\u00D9': 'U',\n  '\\u00DA': 'U',\n  '\\u00DB': 'U',\n  '\\u0168': 'U',\n  '\\u1E78': 'U',\n  '\\u016A': 'U',\n  '\\u1E7A': 'U',\n  '\\u016C': 'U',\n  '\\u00DC': 'U',\n  '\\u01DB': 'U',\n  '\\u01D7': 'U',\n  '\\u01D5': 'U',\n  '\\u01D9': 'U',\n  '\\u1EE6': 'U',\n  '\\u016E': 'U',\n  '\\u0170': 'U',\n  '\\u01D3': 'U',\n  '\\u0214': 'U',\n  '\\u0216': 'U',\n  '\\u01AF': 'U',\n  '\\u1EEA': 'U',\n  '\\u1EE8': 'U',\n  '\\u1EEE': 'U',\n  '\\u1EEC': 'U',\n  '\\u1EF0': 'U',\n  '\\u1EE4': 'U',\n  '\\u1E72': 'U',\n  '\\u0172': 'U',\n  '\\u1E76': 'U',\n  '\\u1E74': 'U',\n  '\\u0244': 'U',\n  '\\u24CB': 'V',\n  '\\uFF36': 'V',\n  '\\u1E7C': 'V',\n  '\\u1E7E': 'V',\n  '\\u01B2': 'V',\n  '\\uA75E': 'V',\n  '\\u0245': 'V',\n  '\\uA760': 'VY',\n  '\\u24CC': 'W',\n  '\\uFF37': 'W',\n  '\\u1E80': 'W',\n  '\\u1E82': 'W',\n  '\\u0174': 'W',\n  '\\u1E86': 'W',\n  '\\u1E84': 'W',\n  '\\u1E88': 'W',\n  '\\u2C72': 'W',\n  '\\u24CD': 'X',\n  '\\uFF38': 'X',\n  '\\u1E8A': 'X',\n  '\\u1E8C': 'X',\n  '\\u24CE': 'Y',\n  '\\uFF39': 'Y',\n  '\\u1EF2': 'Y',\n  '\\u00DD': 'Y',\n  '\\u0176': 'Y',\n  '\\u1EF8': 'Y',\n  '\\u0232': 'Y',\n  '\\u1E8E': 'Y',\n  '\\u0178': 'Y',\n  '\\u1EF6': 'Y',\n  '\\u1EF4': 'Y',\n  '\\u01B3': 'Y',\n  '\\u024E': 'Y',\n  '\\u1EFE': 'Y',\n  '\\u24CF': 'Z',\n  '\\uFF3A': 'Z',\n  '\\u0179': 'Z',\n  '\\u1E90': 'Z',\n  '\\u017B': 'Z',\n  '\\u017D': 'Z',\n  '\\u1E92': 'Z',\n  '\\u1E94': 'Z',\n  '\\u01B5': 'Z',\n  '\\u0224': 'Z',\n  '\\u2C7F': 'Z',\n  '\\u2C6B': 'Z',\n  '\\uA762': 'Z',\n  '\\u24D0': 'a',\n  '\\uFF41': 'a',\n  '\\u1E9A': 'a',\n  '\\u00E0': 'a',\n  '\\u00E1': 'a',\n  '\\u00E2': 'a',\n  '\\u1EA7': 'a',\n  '\\u1EA5': 'a',\n  '\\u1EAB': 'a',\n  '\\u1EA9': 'a',\n  '\\u00E3': 'a',\n  '\\u0101': 'a',\n  '\\u0103': 'a',\n  '\\u1EB1': 'a',\n  '\\u1EAF': 'a',\n  '\\u1EB5': 'a',\n  '\\u1EB3': 'a',\n  '\\u0227': 'a',\n  '\\u01E1': 'a',\n  '\\u00E4': 'a',\n  '\\u01DF': 'a',\n  '\\u1EA3': 'a',\n  '\\u00E5': 'a',\n  '\\u01FB': 'a',\n  '\\u01CE': 'a',\n  '\\u0201': 'a',\n  '\\u0203': 'a',\n  '\\u1EA1': 'a',\n  '\\u1EAD': 'a',\n  '\\u1EB7': 'a',\n  '\\u1E01': 'a',\n  '\\u0105': 'a',\n  '\\u2C65': 'a',\n  '\\u0250': 'a',\n  '\\uA733': 'aa',\n  '\\u00E6': 'ae',\n  '\\u01FD': 'ae',\n  '\\u01E3': 'ae',\n  '\\uA735': 'ao',\n  '\\uA737': 'au',\n  '\\uA739': 'av',\n  '\\uA73B': 'av',\n  '\\uA73D': 'ay',\n  '\\u24D1': 'b',\n  '\\uFF42': 'b',\n  '\\u1E03': 'b',\n  '\\u1E05': 'b',\n  '\\u1E07': 'b',\n  '\\u0180': 'b',\n  '\\u0183': 'b',\n  '\\u0253': 'b',\n  '\\u24D2': 'c',\n  '\\uFF43': 'c',\n  '\\u0107': 'c',\n  '\\u0109': 'c',\n  '\\u010B': 'c',\n  '\\u010D': 'c',\n  '\\u00E7': 'c',\n  '\\u1E09': 'c',\n  '\\u0188': 'c',\n  '\\u023C': 'c',\n  '\\uA73F': 'c',\n  '\\u2184': 'c',\n  '\\u24D3': 'd',\n  '\\uFF44': 'd',\n  '\\u1E0B': 'd',\n  '\\u010F': 'd',\n  '\\u1E0D': 'd',\n  '\\u1E11': 'd',\n  '\\u1E13': 'd',\n  '\\u1E0F': 'd',\n  '\\u0111': 'd',\n  '\\u018C': 'd',\n  '\\u0256': 'd',\n  '\\u0257': 'd',\n  '\\uA77A': 'd',\n  '\\u01F3': 'dz',\n  '\\u01C6': 'dz',\n  '\\u24D4': 'e',\n  '\\uFF45': 'e',\n  '\\u00E8': 'e',\n  '\\u00E9': 'e',\n  '\\u00EA': 'e',\n  '\\u1EC1': 'e',\n  '\\u1EBF': 'e',\n  '\\u1EC5': 'e',\n  '\\u1EC3': 'e',\n  '\\u1EBD': 'e',\n  '\\u0113': 'e',\n  '\\u1E15': 'e',\n  '\\u1E17': 'e',\n  '\\u0115': 'e',\n  '\\u0117': 'e',\n  '\\u00EB': 'e',\n  '\\u1EBB': 'e',\n  '\\u011B': 'e',\n  '\\u0205': 'e',\n  '\\u0207': 'e',\n  '\\u1EB9': 'e',\n  '\\u1EC7': 'e',\n  '\\u0229': 'e',\n  '\\u1E1D': 'e',\n  '\\u0119': 'e',\n  '\\u1E19': 'e',\n  '\\u1E1B': 'e',\n  '\\u0247': 'e',\n  '\\u025B': 'e',\n  '\\u01DD': 'e',\n  '\\u24D5': 'f',\n  '\\uFF46': 'f',\n  '\\u1E1F': 'f',\n  '\\u0192': 'f',\n  '\\uA77C': 'f',\n  '\\u24D6': 'g',\n  '\\uFF47': 'g',\n  '\\u01F5': 'g',\n  '\\u011D': 'g',\n  '\\u1E21': 'g',\n  '\\u011F': 'g',\n  '\\u0121': 'g',\n  '\\u01E7': 'g',\n  '\\u0123': 'g',\n  '\\u01E5': 'g',\n  '\\u0260': 'g',\n  '\\uA7A1': 'g',\n  '\\u1D79': 'g',\n  '\\uA77F': 'g',\n  '\\u24D7': 'h',\n  '\\uFF48': 'h',\n  '\\u0125': 'h',\n  '\\u1E23': 'h',\n  '\\u1E27': 'h',\n  '\\u021F': 'h',\n  '\\u1E25': 'h',\n  '\\u1E29': 'h',\n  '\\u1E2B': 'h',\n  '\\u1E96': 'h',\n  '\\u0127': 'h',\n  '\\u2C68': 'h',\n  '\\u2C76': 'h',\n  '\\u0265': 'h',\n  '\\u0195': 'hv',\n  '\\u24D8': 'i',\n  '\\uFF49': 'i',\n  '\\u00EC': 'i',\n  '\\u00ED': 'i',\n  '\\u00EE': 'i',\n  '\\u0129': 'i',\n  '\\u012B': 'i',\n  '\\u012D': 'i',\n  '\\u00EF': 'i',\n  '\\u1E2F': 'i',\n  '\\u1EC9': 'i',\n  '\\u01D0': 'i',\n  '\\u0209': 'i',\n  '\\u020B': 'i',\n  '\\u1ECB': 'i',\n  '\\u012F': 'i',\n  '\\u1E2D': 'i',\n  '\\u0268': 'i',\n  '\\u0131': 'i',\n  '\\u24D9': 'j',\n  '\\uFF4A': 'j',\n  '\\u0135': 'j',\n  '\\u01F0': 'j',\n  '\\u0249': 'j',\n  '\\u24DA': 'k',\n  '\\uFF4B': 'k',\n  '\\u1E31': 'k',\n  '\\u01E9': 'k',\n  '\\u1E33': 'k',\n  '\\u0137': 'k',\n  '\\u1E35': 'k',\n  '\\u0199': 'k',\n  '\\u2C6A': 'k',\n  '\\uA741': 'k',\n  '\\uA743': 'k',\n  '\\uA745': 'k',\n  '\\uA7A3': 'k',\n  '\\u24DB': 'l',\n  '\\uFF4C': 'l',\n  '\\u0140': 'l',\n  '\\u013A': 'l',\n  '\\u013E': 'l',\n  '\\u1E37': 'l',\n  '\\u1E39': 'l',\n  '\\u013C': 'l',\n  '\\u1E3D': 'l',\n  '\\u1E3B': 'l',\n  '\\u017F': 'l',\n  '\\u0142': 'l',\n  '\\u019A': 'l',\n  '\\u026B': 'l',\n  '\\u2C61': 'l',\n  '\\uA749': 'l',\n  '\\uA781': 'l',\n  '\\uA747': 'l',\n  '\\u01C9': 'lj',\n  '\\u24DC': 'm',\n  '\\uFF4D': 'm',\n  '\\u1E3F': 'm',\n  '\\u1E41': 'm',\n  '\\u1E43': 'm',\n  '\\u0271': 'm',\n  '\\u026F': 'm',\n  '\\u24DD': 'n',\n  '\\uFF4E': 'n',\n  '\\u01F9': 'n',\n  '\\u0144': 'n',\n  '\\u00F1': 'n',\n  '\\u1E45': 'n',\n  '\\u0148': 'n',\n  '\\u1E47': 'n',\n  '\\u0146': 'n',\n  '\\u1E4B': 'n',\n  '\\u1E49': 'n',\n  '\\u019E': 'n',\n  '\\u0272': 'n',\n  '\\u0149': 'n',\n  '\\uA791': 'n',\n  '\\uA7A5': 'n',\n  '\\u01CC': 'nj',\n  '\\u24DE': 'o',\n  '\\uFF4F': 'o',\n  '\\u00F2': 'o',\n  '\\u00F3': 'o',\n  '\\u00F4': 'o',\n  '\\u1ED3': 'o',\n  '\\u1ED1': 'o',\n  '\\u1ED7': 'o',\n  '\\u1ED5': 'o',\n  '\\u00F5': 'o',\n  '\\u1E4D': 'o',\n  '\\u022D': 'o',\n  '\\u1E4F': 'o',\n  '\\u014D': 'o',\n  '\\u1E51': 'o',\n  '\\u1E53': 'o',\n  '\\u014F': 'o',\n  '\\u022F': 'o',\n  '\\u0231': 'o',\n  '\\u00F6': 'o',\n  '\\u022B': 'o',\n  '\\u1ECF': 'o',\n  '\\u0151': 'o',\n  '\\u01D2': 'o',\n  '\\u020D': 'o',\n  '\\u020F': 'o',\n  '\\u01A1': 'o',\n  '\\u1EDD': 'o',\n  '\\u1EDB': 'o',\n  '\\u1EE1': 'o',\n  '\\u1EDF': 'o',\n  '\\u1EE3': 'o',\n  '\\u1ECD': 'o',\n  '\\u1ED9': 'o',\n  '\\u01EB': 'o',\n  '\\u01ED': 'o',\n  '\\u00F8': 'o',\n  '\\u01FF': 'o',\n  '\\u0254': 'o',\n  '\\uA74B': 'o',\n  '\\uA74D': 'o',\n  '\\u0275': 'o',\n  '\\u01A3': 'oi',\n  '\\u0223': 'ou',\n  '\\uA74F': 'oo',\n  '\\u24DF': 'p',\n  '\\uFF50': 'p',\n  '\\u1E55': 'p',\n  '\\u1E57': 'p',\n  '\\u01A5': 'p',\n  '\\u1D7D': 'p',\n  '\\uA751': 'p',\n  '\\uA753': 'p',\n  '\\uA755': 'p',\n  '\\u24E0': 'q',\n  '\\uFF51': 'q',\n  '\\u024B': 'q',\n  '\\uA757': 'q',\n  '\\uA759': 'q',\n  '\\u24E1': 'r',\n  '\\uFF52': 'r',\n  '\\u0155': 'r',\n  '\\u1E59': 'r',\n  '\\u0159': 'r',\n  '\\u0211': 'r',\n  '\\u0213': 'r',\n  '\\u1E5B': 'r',\n  '\\u1E5D': 'r',\n  '\\u0157': 'r',\n  '\\u1E5F': 'r',\n  '\\u024D': 'r',\n  '\\u027D': 'r',\n  '\\uA75B': 'r',\n  '\\uA7A7': 'r',\n  '\\uA783': 'r',\n  '\\u24E2': 's',\n  '\\uFF53': 's',\n  '\\u00DF': 's',\n  '\\u015B': 's',\n  '\\u1E65': 's',\n  '\\u015D': 's',\n  '\\u1E61': 's',\n  '\\u0161': 's',\n  '\\u1E67': 's',\n  '\\u1E63': 's',\n  '\\u1E69': 's',\n  '\\u0219': 's',\n  '\\u015F': 's',\n  '\\u023F': 's',\n  '\\uA7A9': 's',\n  '\\uA785': 's',\n  '\\u1E9B': 's',\n  '\\u24E3': 't',\n  '\\uFF54': 't',\n  '\\u1E6B': 't',\n  '\\u1E97': 't',\n  '\\u0165': 't',\n  '\\u1E6D': 't',\n  '\\u021B': 't',\n  '\\u0163': 't',\n  '\\u1E71': 't',\n  '\\u1E6F': 't',\n  '\\u0167': 't',\n  '\\u01AD': 't',\n  '\\u0288': 't',\n  '\\u2C66': 't',\n  '\\uA787': 't',\n  '\\uA729': 'tz',\n  '\\u24E4': 'u',\n  '\\uFF55': 'u',\n  '\\u00F9': 'u',\n  '\\u00FA': 'u',\n  '\\u00FB': 'u',\n  '\\u0169': 'u',\n  '\\u1E79': 'u',\n  '\\u016B': 'u',\n  '\\u1E7B': 'u',\n  '\\u016D': 'u',\n  '\\u00FC': 'u',\n  '\\u01DC': 'u',\n  '\\u01D8': 'u',\n  '\\u01D6': 'u',\n  '\\u01DA': 'u',\n  '\\u1EE7': 'u',\n  '\\u016F': 'u',\n  '\\u0171': 'u',\n  '\\u01D4': 'u',\n  '\\u0215': 'u',\n  '\\u0217': 'u',\n  '\\u01B0': 'u',\n  '\\u1EEB': 'u',\n  '\\u1EE9': 'u',\n  '\\u1EEF': 'u',\n  '\\u1EED': 'u',\n  '\\u1EF1': 'u',\n  '\\u1EE5': 'u',\n  '\\u1E73': 'u',\n  '\\u0173': 'u',\n  '\\u1E77': 'u',\n  '\\u1E75': 'u',\n  '\\u0289': 'u',\n  '\\u24E5': 'v',\n  '\\uFF56': 'v',\n  '\\u1E7D': 'v',\n  '\\u1E7F': 'v',\n  '\\u028B': 'v',\n  '\\uA75F': 'v',\n  '\\u028C': 'v',\n  '\\uA761': 'vy',\n  '\\u24E6': 'w',\n  '\\uFF57': 'w',\n  '\\u1E81': 'w',\n  '\\u1E83': 'w',\n  '\\u0175': 'w',\n  '\\u1E87': 'w',\n  '\\u1E85': 'w',\n  '\\u1E98': 'w',\n  '\\u1E89': 'w',\n  '\\u2C73': 'w',\n  '\\u24E7': 'x',\n  '\\uFF58': 'x',\n  '\\u1E8B': 'x',\n  '\\u1E8D': 'x',\n  '\\u24E8': 'y',\n  '\\uFF59': 'y',\n  '\\u1EF3': 'y',\n  '\\u00FD': 'y',\n  '\\u0177': 'y',\n  '\\u1EF9': 'y',\n  '\\u0233': 'y',\n  '\\u1E8F': 'y',\n  '\\u00FF': 'y',\n  '\\u1EF7': 'y',\n  '\\u1E99': 'y',\n  '\\u1EF5': 'y',\n  '\\u01B4': 'y',\n  '\\u024F': 'y',\n  '\\u1EFF': 'y',\n  '\\u24E9': 'z',\n  '\\uFF5A': 'z',\n  '\\u017A': 'z',\n  '\\u1E91': 'z',\n  '\\u017C': 'z',\n  '\\u017E': 'z',\n  '\\u1E93': 'z',\n  '\\u1E95': 'z',\n  '\\u01B6': 'z',\n  '\\u0225': 'z',\n  '\\u0240': 'z',\n  '\\u2C6C': 'z',\n  '\\uA763': 'z',\n  '\\u0386': '\\u0391',\n  '\\u0388': '\\u0395',\n  '\\u0389': '\\u0397',\n  '\\u038A': '\\u0399',\n  '\\u03AA': '\\u0399',\n  '\\u038C': '\\u039F',\n  '\\u038E': '\\u03A5',\n  '\\u03AB': '\\u03A5',\n  '\\u038F': '\\u03A9',\n  '\\u03AC': '\\u03B1',\n  '\\u03AD': '\\u03B5',\n  '\\u03AE': '\\u03B7',\n  '\\u03AF': '\\u03B9',\n  '\\u03CA': '\\u03B9',\n  '\\u0390': '\\u03B9',\n  '\\u03CC': '\\u03BF',\n  '\\u03CD': '\\u03C5',\n  '\\u03CB': '\\u03C5',\n  '\\u03B0': '\\u03C5',\n  '\\u03C9': '\\u03C9',\n  '\\u03C2': '\\u03C3'\n};\n\nfunction stripSpecialChars(text) {\n  const match = a => diacritics[a] || a;\n\n  return text.replace(/[^\\u0000-\\u007E]/g, match);\n}\n\nclass ItemsList {\n  constructor(_ngSelect, _selectionModel) {\n    this._ngSelect = _ngSelect;\n    this._selectionModel = _selectionModel;\n    this._items = [];\n    this._filteredItems = [];\n    this._markedIndex = -1;\n  }\n\n  get items() {\n    return this._items;\n  }\n\n  get filteredItems() {\n    return this._filteredItems;\n  }\n\n  get markedIndex() {\n    return this._markedIndex;\n  }\n\n  get selectedItems() {\n    return this._selectionModel.value;\n  }\n\n  get markedItem() {\n    return this._filteredItems[this._markedIndex];\n  }\n\n  get noItemsToSelect() {\n    return this._ngSelect.hideSelected && this._items.length === this.selectedItems.length;\n  }\n\n  get maxItemsSelected() {\n    return this._ngSelect.multiple && this._ngSelect.maxSelectedItems <= this.selectedItems.length;\n  }\n\n  get lastSelectedItem() {\n    let i = this.selectedItems.length - 1;\n\n    for (; i >= 0; i--) {\n      const item = this.selectedItems[i];\n\n      if (!item.disabled) {\n        return item;\n      }\n    }\n\n    return null;\n  }\n\n  setItems(items) {\n    this._items = items.map((item, index) => this.mapItem(item, index));\n\n    if (this._ngSelect.groupBy) {\n      this._groups = this._groupBy(this._items, this._ngSelect.groupBy);\n      this._items = this._flatten(this._groups);\n    } else {\n      this._groups = new Map();\n\n      this._groups.set(undefined, this._items);\n    }\n\n    this._filteredItems = [...this._items];\n  }\n\n  select(item) {\n    if (item.selected || this.maxItemsSelected) {\n      return;\n    }\n\n    const multiple = this._ngSelect.multiple;\n\n    if (!multiple) {\n      this.clearSelected();\n    }\n\n    this._selectionModel.select(item, multiple, this._ngSelect.selectableGroupAsModel);\n\n    if (this._ngSelect.hideSelected) {\n      this._hideSelected(item);\n    }\n  }\n\n  unselect(item) {\n    if (!item.selected) {\n      return;\n    }\n\n    this._selectionModel.unselect(item, this._ngSelect.multiple);\n\n    if (this._ngSelect.hideSelected && isDefined(item.index) && this._ngSelect.multiple) {\n      this._showSelected(item);\n    }\n  }\n\n  findItem(value) {\n    let findBy;\n\n    if (this._ngSelect.compareWith) {\n      findBy = item => this._ngSelect.compareWith(item.value, value);\n    } else if (this._ngSelect.bindValue) {\n      findBy = item => !item.children && this.resolveNested(item.value, this._ngSelect.bindValue) === value;\n    } else {\n      findBy = item => item.value === value || !item.children && item.label && item.label === this.resolveNested(value, this._ngSelect.bindLabel);\n    }\n\n    return this._items.find(item => findBy(item));\n  }\n\n  addItem(item) {\n    const option = this.mapItem(item, this._items.length);\n\n    this._items.push(option);\n\n    this._filteredItems.push(option);\n\n    return option;\n  }\n\n  clearSelected(keepDisabled = false) {\n    this._selectionModel.clear(keepDisabled);\n\n    this._items.forEach(item => {\n      item.selected = keepDisabled && item.selected && item.disabled;\n      item.marked = false;\n    });\n\n    if (this._ngSelect.hideSelected) {\n      this.resetFilteredItems();\n    }\n  }\n\n  findByLabel(term) {\n    term = stripSpecialChars(term).toLocaleLowerCase();\n    return this.filteredItems.find(item => {\n      const label = stripSpecialChars(item.label).toLocaleLowerCase();\n      return label.substr(0, term.length) === term;\n    });\n  }\n\n  filter(term) {\n    if (!term) {\n      this.resetFilteredItems();\n      return;\n    }\n\n    this._filteredItems = [];\n    term = this._ngSelect.searchFn ? term : stripSpecialChars(term).toLocaleLowerCase();\n    const match = this._ngSelect.searchFn || this._defaultSearchFn;\n    const hideSelected = this._ngSelect.hideSelected;\n\n    for (const key of Array.from(this._groups.keys())) {\n      const matchedItems = [];\n\n      for (const item of this._groups.get(key)) {\n        if (hideSelected && (item.parent && item.parent.selected || item.selected)) {\n          continue;\n        }\n\n        const searchItem = this._ngSelect.searchFn ? item.value : item;\n\n        if (match(term, searchItem)) {\n          matchedItems.push(item);\n        }\n      }\n\n      if (matchedItems.length > 0) {\n        const [last] = matchedItems.slice(-1);\n\n        if (last.parent) {\n          const head = this._items.find(x => x === last.parent);\n\n          this._filteredItems.push(head);\n        }\n\n        this._filteredItems.push(...matchedItems);\n      }\n    }\n  }\n\n  resetFilteredItems() {\n    if (this._filteredItems.length === this._items.length) {\n      return;\n    }\n\n    if (this._ngSelect.hideSelected && this.selectedItems.length > 0) {\n      this._filteredItems = this._items.filter(x => !x.selected);\n    } else {\n      this._filteredItems = this._items;\n    }\n  }\n\n  unmarkItem() {\n    this._markedIndex = -1;\n  }\n\n  markNextItem() {\n    this._stepToItem(+1);\n  }\n\n  markPreviousItem() {\n    this._stepToItem(-1);\n  }\n\n  markItem(item) {\n    this._markedIndex = this._filteredItems.indexOf(item);\n  }\n\n  markSelectedOrDefault(markDefault) {\n    if (this._filteredItems.length === 0) {\n      return;\n    }\n\n    const lastMarkedIndex = this._getLastMarkedIndex();\n\n    if (lastMarkedIndex > -1) {\n      this._markedIndex = lastMarkedIndex;\n    } else {\n      this._markedIndex = markDefault ? this.filteredItems.findIndex(x => !x.disabled) : -1;\n    }\n  }\n\n  resolveNested(option, key) {\n    if (!isObject(option)) {\n      return option;\n    }\n\n    if (key.indexOf('.') === -1) {\n      return option[key];\n    } else {\n      const keys = key.split('.');\n      let value = option;\n\n      for (let i = 0, len = keys.length; i < len; ++i) {\n        if (value == null) {\n          return null;\n        }\n\n        value = value[keys[i]];\n      }\n\n      return value;\n    }\n  }\n\n  mapItem(item, index) {\n    const label = isDefined(item.$ngOptionLabel) ? item.$ngOptionLabel : this.resolveNested(item, this._ngSelect.bindLabel);\n    const value = isDefined(item.$ngOptionValue) ? item.$ngOptionValue : item;\n    return {\n      index,\n      label: isDefined(label) ? label.toString() : '',\n      value,\n      disabled: item.disabled,\n      htmlId: `${this._ngSelect.dropdownId}-${index}`\n    };\n  }\n\n  mapSelectedItems() {\n    const multiple = this._ngSelect.multiple;\n\n    for (const selected of this.selectedItems) {\n      const value = this._ngSelect.bindValue ? this.resolveNested(selected.value, this._ngSelect.bindValue) : selected.value;\n      const item = isDefined(value) ? this.findItem(value) : null;\n\n      this._selectionModel.unselect(selected, multiple);\n\n      this._selectionModel.select(item || selected, multiple, this._ngSelect.selectableGroupAsModel);\n    }\n\n    if (this._ngSelect.hideSelected) {\n      this._filteredItems = this.filteredItems.filter(x => this.selectedItems.indexOf(x) === -1);\n    }\n  }\n\n  _showSelected(item) {\n    this._filteredItems.push(item);\n\n    if (item.parent) {\n      const parent = item.parent;\n\n      const parentExists = this._filteredItems.find(x => x === parent);\n\n      if (!parentExists) {\n        this._filteredItems.push(parent);\n      }\n    } else if (item.children) {\n      for (const child of item.children) {\n        child.selected = false;\n\n        this._filteredItems.push(child);\n      }\n    }\n\n    this._filteredItems = [...this._filteredItems.sort((a, b) => a.index - b.index)];\n  }\n\n  _hideSelected(item) {\n    this._filteredItems = this._filteredItems.filter(x => x !== item);\n\n    if (item.parent) {\n      const children = item.parent.children;\n\n      if (children.every(x => x.selected)) {\n        this._filteredItems = this._filteredItems.filter(x => x !== item.parent);\n      }\n    } else if (item.children) {\n      this._filteredItems = this.filteredItems.filter(x => x.parent !== item);\n    }\n  }\n\n  _defaultSearchFn(search, opt) {\n    const label = stripSpecialChars(opt.label).toLocaleLowerCase();\n    return label.indexOf(search) > -1;\n  }\n\n  _getNextItemIndex(steps) {\n    if (steps > 0) {\n      return this._markedIndex >= this._filteredItems.length - 1 ? 0 : this._markedIndex + 1;\n    }\n\n    return this._markedIndex <= 0 ? this._filteredItems.length - 1 : this._markedIndex - 1;\n  }\n\n  _stepToItem(steps) {\n    if (this._filteredItems.length === 0 || this._filteredItems.every(x => x.disabled)) {\n      return;\n    }\n\n    this._markedIndex = this._getNextItemIndex(steps);\n\n    if (this.markedItem.disabled) {\n      this._stepToItem(steps);\n    }\n  }\n\n  _getLastMarkedIndex() {\n    if (this._ngSelect.hideSelected) {\n      return -1;\n    }\n\n    if (this._markedIndex > -1 && this.markedItem === undefined) {\n      return -1;\n    }\n\n    const selectedIndex = this._filteredItems.indexOf(this.lastSelectedItem);\n\n    if (this.lastSelectedItem && selectedIndex < 0) {\n      return -1;\n    }\n\n    return Math.max(this.markedIndex, selectedIndex);\n  }\n\n  _groupBy(items, prop) {\n    const groups = new Map();\n\n    if (items.length === 0) {\n      return groups;\n    } // Check if items are already grouped by given key.\n\n\n    if (Array.isArray(items[0].value[prop])) {\n      for (const item of items) {\n        const children = (item.value[prop] || []).map((x, index) => this.mapItem(x, index));\n        groups.set(item, children);\n      }\n\n      return groups;\n    }\n\n    const isFnKey = isFunction(this._ngSelect.groupBy);\n\n    const keyFn = item => {\n      const key = isFnKey ? prop(item.value) : item.value[prop];\n      return isDefined(key) ? key : undefined;\n    }; // Group items by key.\n\n\n    for (const item of items) {\n      const key = keyFn(item);\n      const group = groups.get(key);\n\n      if (group) {\n        group.push(item);\n      } else {\n        groups.set(key, [item]);\n      }\n    }\n\n    return groups;\n  }\n\n  _flatten(groups) {\n    const isGroupByFn = isFunction(this._ngSelect.groupBy);\n    const items = [];\n\n    for (const key of Array.from(groups.keys())) {\n      let i = items.length;\n\n      if (key === undefined) {\n        const withoutGroup = groups.get(undefined) || [];\n        items.push(...withoutGroup.map(x => {\n          x.index = i++;\n          return x;\n        }));\n        continue;\n      }\n\n      const isObjectKey = isObject(key);\n      const parent = {\n        label: isObjectKey ? '' : String(key),\n        children: undefined,\n        parent: null,\n        index: i++,\n        disabled: !this._ngSelect.selectableGroup,\n        htmlId: newId()\n      };\n      const groupKey = isGroupByFn ? this._ngSelect.bindLabel : this._ngSelect.groupBy;\n\n      const groupValue = this._ngSelect.groupValue || (() => {\n        if (isObjectKey) {\n          return key.value;\n        }\n\n        return {\n          [groupKey]: key\n        };\n      });\n\n      const children = groups.get(key).map(x => {\n        x.parent = parent;\n        x.children = undefined;\n        x.index = i++;\n        return x;\n      });\n      parent.children = children;\n      parent.value = groupValue(key, children.map(x => x.value));\n      items.push(parent);\n      items.push(...children);\n    }\n\n    return items;\n  }\n\n}\n\nvar KeyCode;\n\n(function (KeyCode) {\n  KeyCode[KeyCode[\"Tab\"] = 9] = \"Tab\";\n  KeyCode[KeyCode[\"Enter\"] = 13] = \"Enter\";\n  KeyCode[KeyCode[\"Esc\"] = 27] = \"Esc\";\n  KeyCode[KeyCode[\"Space\"] = 32] = \"Space\";\n  KeyCode[KeyCode[\"ArrowUp\"] = 38] = \"ArrowUp\";\n  KeyCode[KeyCode[\"ArrowDown\"] = 40] = \"ArrowDown\";\n  KeyCode[KeyCode[\"Backspace\"] = 8] = \"Backspace\";\n})(KeyCode || (KeyCode = {}));\n\nclass NgDropdownPanelService {\n  constructor() {\n    this._dimensions = {\n      itemHeight: 0,\n      panelHeight: 0,\n      itemsPerViewport: 0\n    };\n  }\n\n  get dimensions() {\n    return this._dimensions;\n  }\n\n  calculateItems(scrollPos, itemsLength, buffer) {\n    const d = this._dimensions;\n    const scrollHeight = d.itemHeight * itemsLength;\n    const scrollTop = Math.max(0, scrollPos);\n    const indexByScrollTop = scrollTop / scrollHeight * itemsLength;\n    let end = Math.min(itemsLength, Math.ceil(indexByScrollTop) + (d.itemsPerViewport + 1));\n    const maxStartEnd = end;\n    const maxStart = Math.max(0, maxStartEnd - d.itemsPerViewport);\n    let start = Math.min(maxStart, Math.floor(indexByScrollTop));\n    let topPadding = d.itemHeight * Math.ceil(start) - d.itemHeight * Math.min(start, buffer);\n    topPadding = !isNaN(topPadding) ? topPadding : 0;\n    start = !isNaN(start) ? start : -1;\n    end = !isNaN(end) ? end : -1;\n    start -= buffer;\n    start = Math.max(0, start);\n    end += buffer;\n    end = Math.min(itemsLength, end);\n    return {\n      topPadding,\n      scrollHeight,\n      start,\n      end\n    };\n  }\n\n  setDimensions(itemHeight, panelHeight) {\n    const itemsPerViewport = Math.max(1, Math.floor(panelHeight / itemHeight));\n    this._dimensions = {\n      itemHeight,\n      panelHeight,\n      itemsPerViewport\n    };\n  }\n\n  getScrollTo(itemTop, itemHeight, lastScroll) {\n    const {\n      panelHeight\n    } = this.dimensions;\n    const itemBottom = itemTop + itemHeight;\n    const top = lastScroll;\n    const bottom = top + panelHeight;\n\n    if (panelHeight >= itemBottom && lastScroll === itemTop) {\n      return null;\n    }\n\n    if (itemBottom > bottom) {\n      return top + itemBottom - bottom;\n    } else if (itemTop <= top) {\n      return itemTop;\n    }\n\n    return null;\n  }\n\n}\n\nNgDropdownPanelService.ɵfac = function NgDropdownPanelService_Factory(t) {\n  return new (t || NgDropdownPanelService)();\n};\n\nNgDropdownPanelService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgDropdownPanelService,\n  factory: NgDropdownPanelService.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgDropdownPanelService, [{\n    type: Injectable\n  }], null, null);\n})();\n\nconst CSS_POSITIONS = ['top', 'right', 'bottom', 'left'];\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n\nclass NgDropdownPanelComponent {\n  constructor(_renderer, _zone, _panelService, _elementRef, _document) {\n    this._renderer = _renderer;\n    this._zone = _zone;\n    this._panelService = _panelService;\n    this._document = _document;\n    this.items = [];\n    this.position = 'auto';\n    this.virtualScroll = false;\n    this.filterValue = null;\n    this.update = new EventEmitter();\n    this.scroll = new EventEmitter();\n    this.scrollToEnd = new EventEmitter();\n    this.outsideClick = new EventEmitter();\n    this._destroy$ = new Subject();\n    this._scrollToEndFired = false;\n    this._updateScrollHeight = false;\n    this._lastScrollPosition = 0;\n    this._dropdown = _elementRef.nativeElement;\n  }\n\n  get currentPosition() {\n    return this._currentPosition;\n  }\n\n  get itemsLength() {\n    return this._itemsLength;\n  }\n\n  set itemsLength(value) {\n    if (value !== this._itemsLength) {\n      this._itemsLength = value;\n\n      this._onItemsLengthChanged();\n    }\n  }\n\n  get _startOffset() {\n    if (this.markedItem) {\n      const {\n        itemHeight,\n        panelHeight\n      } = this._panelService.dimensions;\n      const offset = this.markedItem.index * itemHeight;\n      return panelHeight > offset ? 0 : offset;\n    }\n\n    return 0;\n  }\n\n  ngOnInit() {\n    this._select = this._dropdown.parentElement;\n    this._virtualPadding = this.paddingElementRef.nativeElement;\n    this._scrollablePanel = this.scrollElementRef.nativeElement;\n    this._contentPanel = this.contentElementRef.nativeElement;\n\n    this._handleScroll();\n\n    this._handleOutsideClick();\n\n    this._appendDropdown();\n\n    this._setupMousedownListener();\n  }\n\n  ngOnChanges(changes) {\n    if (changes.items) {\n      const change = changes.items;\n\n      this._onItemsChange(change.currentValue, change.firstChange);\n    }\n  }\n\n  ngOnDestroy() {\n    this._destroy$.next();\n\n    this._destroy$.complete();\n\n    this._destroy$.unsubscribe();\n\n    if (this.appendTo) {\n      this._renderer.removeChild(this._dropdown.parentNode, this._dropdown);\n    }\n  }\n\n  scrollTo(option, startFromOption = false) {\n    if (!option) {\n      return;\n    }\n\n    const index = this.items.indexOf(option);\n\n    if (index < 0 || index >= this.itemsLength) {\n      return;\n    }\n\n    let scrollTo;\n\n    if (this.virtualScroll) {\n      const itemHeight = this._panelService.dimensions.itemHeight;\n      scrollTo = this._panelService.getScrollTo(index * itemHeight, itemHeight, this._lastScrollPosition);\n    } else {\n      const item = this._dropdown.querySelector(`#${option.htmlId}`);\n\n      const lastScroll = startFromOption ? item.offsetTop : this._lastScrollPosition;\n      scrollTo = this._panelService.getScrollTo(item.offsetTop, item.clientHeight, lastScroll);\n    }\n\n    if (isDefined(scrollTo)) {\n      this._scrollablePanel.scrollTop = scrollTo;\n    }\n  }\n\n  scrollToTag() {\n    const panel = this._scrollablePanel;\n    panel.scrollTop = panel.scrollHeight - panel.clientHeight;\n  }\n\n  adjustPosition() {\n    this._updateYPosition();\n  }\n\n  _handleDropdownPosition() {\n    this._currentPosition = this._calculateCurrentPosition(this._dropdown);\n\n    if (CSS_POSITIONS.includes(this._currentPosition)) {\n      this._updateDropdownClass(this._currentPosition);\n    } else {\n      this._updateDropdownClass('bottom');\n    }\n\n    if (this.appendTo) {\n      this._updateYPosition();\n    }\n\n    this._dropdown.style.opacity = '1';\n  }\n\n  _updateDropdownClass(currentPosition) {\n    CSS_POSITIONS.forEach(position => {\n      const REMOVE_CSS_CLASS = `ng-select-${position}`;\n\n      this._renderer.removeClass(this._dropdown, REMOVE_CSS_CLASS);\n\n      this._renderer.removeClass(this._select, REMOVE_CSS_CLASS);\n    });\n    const ADD_CSS_CLASS = `ng-select-${currentPosition}`;\n\n    this._renderer.addClass(this._dropdown, ADD_CSS_CLASS);\n\n    this._renderer.addClass(this._select, ADD_CSS_CLASS);\n  }\n\n  _handleScroll() {\n    this._zone.runOutsideAngular(() => {\n      fromEvent(this.scrollElementRef.nativeElement, 'scroll').pipe(takeUntil(this._destroy$), auditTime(0, SCROLL_SCHEDULER)).subscribe(e => {\n        const path = e.path || e.composedPath && e.composedPath();\n        const scrollTop = !path || path.length === 0 ? e.target.scrollTop : path[0].scrollTop;\n\n        this._onContentScrolled(scrollTop);\n      });\n    });\n  }\n\n  _handleOutsideClick() {\n    if (!this._document) {\n      return;\n    }\n\n    this._zone.runOutsideAngular(() => {\n      merge(fromEvent(this._document, 'touchstart', {\n        capture: true\n      }), fromEvent(this._document, 'mousedown', {\n        capture: true\n      })).pipe(takeUntil(this._destroy$)).subscribe($event => this._checkToClose($event));\n    });\n  }\n\n  _checkToClose($event) {\n    if (this._select.contains($event.target) || this._dropdown.contains($event.target)) {\n      return;\n    }\n\n    const path = $event.path || $event.composedPath && $event.composedPath();\n\n    if ($event.target && $event.target.shadowRoot && path && path[0] && this._select.contains(path[0])) {\n      return;\n    }\n\n    this._zone.run(() => this.outsideClick.emit());\n  }\n\n  _onItemsChange(items, firstChange) {\n    this.items = items || [];\n    this._scrollToEndFired = false;\n    this.itemsLength = items.length;\n\n    if (this.virtualScroll) {\n      this._updateItemsRange(firstChange);\n    } else {\n      this._setVirtualHeight();\n\n      this._updateItems(firstChange);\n    }\n  }\n\n  _updateItems(firstChange) {\n    this.update.emit(this.items);\n\n    if (firstChange === false) {\n      return;\n    }\n\n    this._zone.runOutsideAngular(() => {\n      Promise.resolve().then(() => {\n        const panelHeight = this._scrollablePanel.clientHeight;\n\n        this._panelService.setDimensions(0, panelHeight);\n\n        this._handleDropdownPosition();\n\n        this.scrollTo(this.markedItem, firstChange);\n      });\n    });\n  }\n\n  _updateItemsRange(firstChange) {\n    this._zone.runOutsideAngular(() => {\n      this._measureDimensions().then(() => {\n        if (firstChange) {\n          this._renderItemsRange(this._startOffset);\n\n          this._handleDropdownPosition();\n        } else {\n          this._renderItemsRange();\n        }\n      });\n    });\n  }\n\n  _onContentScrolled(scrollTop) {\n    if (this.virtualScroll) {\n      this._renderItemsRange(scrollTop);\n    }\n\n    this._lastScrollPosition = scrollTop;\n\n    this._fireScrollToEnd(scrollTop);\n  }\n\n  _updateVirtualHeight(height) {\n    if (this._updateScrollHeight) {\n      this._virtualPadding.style.height = `${height}px`;\n      this._updateScrollHeight = false;\n    }\n  }\n\n  _setVirtualHeight() {\n    if (!this._virtualPadding) {\n      return;\n    }\n\n    this._virtualPadding.style.height = `0px`;\n  }\n\n  _onItemsLengthChanged() {\n    this._updateScrollHeight = true;\n  }\n\n  _renderItemsRange(scrollTop = null) {\n    if (scrollTop && this._lastScrollPosition === scrollTop) {\n      return;\n    }\n\n    scrollTop = scrollTop || this._scrollablePanel.scrollTop;\n\n    const range = this._panelService.calculateItems(scrollTop, this.itemsLength, this.bufferAmount);\n\n    this._updateVirtualHeight(range.scrollHeight);\n\n    this._contentPanel.style.transform = `translateY(${range.topPadding}px)`;\n\n    this._zone.run(() => {\n      this.update.emit(this.items.slice(range.start, range.end));\n      this.scroll.emit({\n        start: range.start,\n        end: range.end\n      });\n    });\n\n    if (isDefined(scrollTop) && this._lastScrollPosition === 0) {\n      this._scrollablePanel.scrollTop = scrollTop;\n      this._lastScrollPosition = scrollTop;\n    }\n  }\n\n  _measureDimensions() {\n    if (this._panelService.dimensions.itemHeight > 0 || this.itemsLength === 0) {\n      return Promise.resolve(this._panelService.dimensions);\n    }\n\n    const [first] = this.items;\n    this.update.emit([first]);\n    return Promise.resolve().then(() => {\n      const option = this._dropdown.querySelector(`#${first.htmlId}`);\n\n      const optionHeight = option.clientHeight;\n      this._virtualPadding.style.height = `${optionHeight * this.itemsLength}px`;\n      const panelHeight = this._scrollablePanel.clientHeight;\n\n      this._panelService.setDimensions(optionHeight, panelHeight);\n\n      return this._panelService.dimensions;\n    });\n  }\n\n  _fireScrollToEnd(scrollTop) {\n    if (this._scrollToEndFired || scrollTop === 0) {\n      return;\n    }\n\n    const padding = this.virtualScroll ? this._virtualPadding : this._contentPanel;\n\n    if (scrollTop + this._dropdown.clientHeight >= padding.clientHeight - 1) {\n      this._zone.run(() => this.scrollToEnd.emit());\n\n      this._scrollToEndFired = true;\n    }\n  }\n\n  _calculateCurrentPosition(dropdownEl) {\n    if (this.position !== 'auto') {\n      return this.position;\n    }\n\n    const selectRect = this._select.getBoundingClientRect();\n\n    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;\n    const offsetTop = selectRect.top + window.pageYOffset;\n    const height = selectRect.height;\n    const dropdownHeight = dropdownEl.getBoundingClientRect().height;\n\n    if (offsetTop + height + dropdownHeight > scrollTop + document.documentElement.clientHeight) {\n      return 'top';\n    } else {\n      return 'bottom';\n    }\n  }\n\n  _appendDropdown() {\n    if (!this.appendTo) {\n      return;\n    }\n\n    this._parent = document.querySelector(this.appendTo);\n\n    if (!this._parent) {\n      throw new Error(`appendTo selector ${this.appendTo} did not found any parent element`);\n    }\n\n    this._updateXPosition();\n\n    this._parent.appendChild(this._dropdown);\n  }\n\n  _updateXPosition() {\n    const select = this._select.getBoundingClientRect();\n\n    const parent = this._parent.getBoundingClientRect();\n\n    const offsetLeft = select.left - parent.left;\n    this._dropdown.style.left = offsetLeft + 'px';\n    this._dropdown.style.width = select.width + 'px';\n    this._dropdown.style.minWidth = select.width + 'px';\n  }\n\n  _updateYPosition() {\n    const select = this._select.getBoundingClientRect();\n\n    const parent = this._parent.getBoundingClientRect();\n\n    const delta = select.height;\n\n    if (this._currentPosition === 'top') {\n      const offsetBottom = parent.bottom - select.bottom;\n      this._dropdown.style.bottom = offsetBottom + delta + 'px';\n      this._dropdown.style.top = 'auto';\n    } else if (this._currentPosition === 'bottom') {\n      const offsetTop = select.top - parent.top;\n      this._dropdown.style.top = offsetTop + delta + 'px';\n      this._dropdown.style.bottom = 'auto';\n    }\n  }\n\n  _setupMousedownListener() {\n    this._zone.runOutsideAngular(() => {\n      fromEvent(this._dropdown, 'mousedown').pipe(takeUntil(this._destroy$)).subscribe(event => {\n        const target = event.target;\n\n        if (target.tagName === 'INPUT') {\n          return;\n        }\n\n        event.preventDefault();\n      });\n    });\n  }\n\n}\n\nNgDropdownPanelComponent.ɵfac = function NgDropdownPanelComponent_Factory(t) {\n  return new (t || NgDropdownPanelComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(NgDropdownPanelService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT, 8));\n};\n\nNgDropdownPanelComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgDropdownPanelComponent,\n  selectors: [[\"ng-dropdown-panel\"]],\n  viewQuery: function NgDropdownPanelComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7, ElementRef);\n      i0.ɵɵviewQuery(_c1, 7, ElementRef);\n      i0.ɵɵviewQuery(_c2, 7, ElementRef);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentElementRef = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollElementRef = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paddingElementRef = _t.first);\n    }\n  },\n  inputs: {\n    items: \"items\",\n    markedItem: \"markedItem\",\n    position: \"position\",\n    appendTo: \"appendTo\",\n    bufferAmount: \"bufferAmount\",\n    virtualScroll: \"virtualScroll\",\n    headerTemplate: \"headerTemplate\",\n    footerTemplate: \"footerTemplate\",\n    filterValue: \"filterValue\"\n  },\n  outputs: {\n    update: \"update\",\n    scroll: \"scroll\",\n    scrollToEnd: \"scrollToEnd\",\n    outsideClick: \"outsideClick\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c4,\n  decls: 9,\n  vars: 6,\n  consts: [[\"class\", \"ng-dropdown-header\", 4, \"ngIf\"], [1, \"ng-dropdown-panel-items\", \"scroll-host\"], [\"scroll\", \"\"], [\"padding\", \"\"], [\"content\", \"\"], [\"class\", \"ng-dropdown-footer\", 4, \"ngIf\"], [1, \"ng-dropdown-header\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"ng-dropdown-footer\"]],\n  template: function NgDropdownPanelComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, NgDropdownPanelComponent_div_0_Template, 2, 4, \"div\", 0);\n      i0.ɵɵelementStart(1, \"div\", 1, 2);\n      i0.ɵɵelement(3, \"div\", null, 3);\n      i0.ɵɵelementStart(5, \"div\", null, 4);\n      i0.ɵɵprojection(7);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(8, NgDropdownPanelComponent_div_8_Template, 2, 4, \"div\", 5);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.headerTemplate);\n      i0.ɵɵadvance(3);\n      i0.ɵɵclassProp(\"total-padding\", ctx.virtualScroll);\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"scrollable-content\", ctx.virtualScroll && ctx.items.length);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.footerTemplate);\n    }\n  },\n  directives: [i4.NgIf, i4.NgTemplateOutlet],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgDropdownPanelComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'ng-dropdown-panel',\n      template: `\n        <div *ngIf=\"headerTemplate\" class=\"ng-dropdown-header\">\n            <ng-container [ngTemplateOutlet]=\"headerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n        <div #scroll class=\"ng-dropdown-panel-items scroll-host\">\n            <div #padding [class.total-padding]=\"virtualScroll\"></div>\n            <div #content [class.scrollable-content]=\"virtualScroll && items.length\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n        <div *ngIf=\"footerTemplate\" class=\"ng-dropdown-footer\">\n            <ng-container [ngTemplateOutlet]=\"footerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n    `\n    }]\n  }], function () {\n    return [{\n      type: i0.Renderer2\n    }, {\n      type: i0.NgZone\n    }, {\n      type: NgDropdownPanelService\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    items: [{\n      type: Input\n    }],\n    markedItem: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    bufferAmount: [{\n      type: Input\n    }],\n    virtualScroll: [{\n      type: Input\n    }],\n    headerTemplate: [{\n      type: Input\n    }],\n    footerTemplate: [{\n      type: Input\n    }],\n    filterValue: [{\n      type: Input\n    }],\n    update: [{\n      type: Output\n    }],\n    scroll: [{\n      type: Output\n    }],\n    scrollToEnd: [{\n      type: Output\n    }],\n    outsideClick: [{\n      type: Output\n    }],\n    contentElementRef: [{\n      type: ViewChild,\n      args: ['content', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    scrollElementRef: [{\n      type: ViewChild,\n      args: ['scroll', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    paddingElementRef: [{\n      type: ViewChild,\n      args: ['padding', {\n        read: ElementRef,\n        static: true\n      }]\n    }]\n  });\n})();\n\nclass NgOptionComponent {\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    this.stateChange$ = new Subject();\n    this._disabled = false;\n  }\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = this._isDisabled(value);\n  }\n\n  get label() {\n    return (this.elementRef.nativeElement.textContent || '').trim();\n  }\n\n  ngOnChanges(changes) {\n    if (changes.disabled) {\n      this.stateChange$.next({\n        value: this.value,\n        disabled: this._disabled\n      });\n    }\n  }\n\n  ngAfterViewChecked() {\n    if (this.label !== this._previousLabel) {\n      this._previousLabel = this.label;\n      this.stateChange$.next({\n        value: this.value,\n        disabled: this._disabled,\n        label: this.elementRef.nativeElement.innerHTML\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    this.stateChange$.complete();\n  }\n\n  _isDisabled(value) {\n    return value != null && `${value}` !== 'false';\n  }\n\n}\n\nNgOptionComponent.ɵfac = function NgOptionComponent_Factory(t) {\n  return new (t || NgOptionComponent)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nNgOptionComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgOptionComponent,\n  selectors: [[\"ng-option\"]],\n  inputs: {\n    value: \"value\",\n    disabled: \"disabled\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c4,\n  decls: 1,\n  vars: 0,\n  template: function NgOptionComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵprojection(0);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ng-option',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `<ng-content></ng-content>`\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    value: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n\nclass NgSelectConfig {\n  constructor() {\n    this.notFoundText = 'No items found';\n    this.typeToSearchText = 'Type to search';\n    this.addTagText = 'Add item';\n    this.loadingText = 'Loading...';\n    this.clearAllText = 'Clear all';\n    this.disableVirtualScroll = true;\n    this.openOnEnter = true;\n    this.appearance = 'underline';\n  }\n\n}\n\nNgSelectConfig.ɵfac = function NgSelectConfig_Factory(t) {\n  return new (t || NgSelectConfig)();\n};\n\nNgSelectConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: NgSelectConfig,\n  factory: NgSelectConfig.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nclass ConsoleService {\n  warn(message) {\n    console.warn(message);\n  }\n\n}\n\nConsoleService.ɵfac = function ConsoleService_Factory(t) {\n  return new (t || ConsoleService)();\n};\n\nConsoleService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ConsoleService,\n  factory: ConsoleService.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConsoleService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\nconst SELECTION_MODEL_FACTORY = new InjectionToken('ng-select-selection-model');\n\nclass NgSelectComponent {\n  constructor(classes, autoFocus, config, newSelectionModel, _elementRef, _cd, _console) {\n    this.classes = classes;\n    this.autoFocus = autoFocus;\n    this.config = config;\n    this._cd = _cd;\n    this._console = _console;\n    this.markFirst = true;\n    this.dropdownPosition = 'auto';\n    this.loading = false;\n    this.closeOnSelect = true;\n    this.hideSelected = false;\n    this.selectOnTab = false;\n    this.bufferAmount = 4;\n    this.selectableGroup = false;\n    this.selectableGroupAsModel = true;\n    this.searchFn = null;\n    this.trackByFn = null;\n    this.clearOnBackspace = true;\n    this.labelForId = null;\n    this.inputAttrs = {};\n    this.readonly = false;\n    this.searchWhileComposing = true;\n    this.minTermLength = 0;\n    this.editableSearchTerm = false;\n\n    this.keyDownFn = _ => true;\n\n    this.multiple = false;\n    this.addTag = false;\n    this.searchable = true;\n    this.clearable = true;\n    this.isOpen = false; // output events\n\n    this.blurEvent = new EventEmitter();\n    this.focusEvent = new EventEmitter();\n    this.changeEvent = new EventEmitter();\n    this.openEvent = new EventEmitter();\n    this.closeEvent = new EventEmitter();\n    this.searchEvent = new EventEmitter();\n    this.clearEvent = new EventEmitter();\n    this.addEvent = new EventEmitter();\n    this.removeEvent = new EventEmitter();\n    this.scroll = new EventEmitter();\n    this.scrollToEnd = new EventEmitter();\n    this.useDefaultClass = true;\n    this.viewPortItems = [];\n    this.searchTerm = null;\n    this.dropdownId = newId();\n    this.escapeHTML = true;\n    this._items = [];\n    this._defaultLabel = 'label';\n    this._pressedKeys = [];\n    this._isComposing = false;\n    this._destroy$ = new Subject();\n    this._keyPress$ = new Subject();\n\n    this._onChange = _ => {};\n\n    this._onTouched = () => {};\n\n    this.clearItem = item => {\n      const option = this.selectedItems.find(x => x.value === item);\n      this.unselect(option);\n    };\n\n    this.trackByOption = (_, item) => {\n      if (this.trackByFn) {\n        return this.trackByFn(item.value);\n      }\n\n      return item;\n    };\n\n    this._mergeGlobalConfig(config);\n\n    this.itemsList = new ItemsList(this, newSelectionModel());\n    this.element = _elementRef.nativeElement;\n  }\n\n  get items() {\n    return this._items;\n  }\n\n  set items(value) {\n    if (value === null) {\n      value = [];\n    }\n\n    this._itemsAreUsed = true;\n    this._items = value;\n  }\n\n  get compareWith() {\n    return this._compareWith;\n  }\n\n  set compareWith(fn) {\n    if (fn !== undefined && fn !== null && !isFunction(fn)) {\n      throw Error('`compareWith` must be a function.');\n    }\n\n    this._compareWith = fn;\n  }\n\n  get clearSearchOnAdd() {\n    if (isDefined(this._clearSearchOnAdd)) {\n      return this._clearSearchOnAdd;\n    } else if (isDefined(this.config.clearSearchOnAdd)) {\n      return this.config.clearSearchOnAdd;\n    }\n\n    return this.closeOnSelect;\n  }\n\n  set clearSearchOnAdd(value) {\n    this._clearSearchOnAdd = value;\n  }\n\n  get disabled() {\n    return this.readonly || this._disabled;\n  }\n\n  get filtered() {\n    return !!this.searchTerm && this.searchable || this._isComposing;\n  }\n\n  get single() {\n    return !this.multiple;\n  }\n\n  get _editableSearchTerm() {\n    return this.editableSearchTerm && !this.multiple;\n  }\n\n  get selectedItems() {\n    return this.itemsList.selectedItems;\n  }\n\n  get selectedValues() {\n    return this.selectedItems.map(x => x.value);\n  }\n\n  get hasValue() {\n    return this.selectedItems.length > 0;\n  }\n\n  get currentPanelPosition() {\n    if (this.dropdownPanel) {\n      return this.dropdownPanel.currentPosition;\n    }\n\n    return undefined;\n  }\n\n  ngOnInit() {\n    this._handleKeyPresses();\n\n    this._setInputAttributes();\n  }\n\n  ngOnChanges(changes) {\n    if (changes.multiple) {\n      this.itemsList.clearSelected();\n    }\n\n    if (changes.items) {\n      this._setItems(changes.items.currentValue || []);\n    }\n\n    if (changes.isOpen) {\n      this._manualOpen = isDefined(changes.isOpen.currentValue);\n    }\n  }\n\n  ngAfterViewInit() {\n    if (!this._itemsAreUsed) {\n      this.escapeHTML = false;\n\n      this._setItemsFromNgOptions();\n    }\n\n    if (isDefined(this.autoFocus)) {\n      this.focus();\n    }\n  }\n\n  ngOnDestroy() {\n    this._destroy$.next();\n\n    this._destroy$.complete();\n  }\n\n  handleKeyDown($event) {\n    const keyCode = KeyCode[$event.which];\n\n    if (keyCode) {\n      if (this.keyDownFn($event) === false) {\n        return;\n      }\n\n      this.handleKeyCode($event);\n    } else if ($event.key && $event.key.length === 1) {\n      this._keyPress$.next($event.key.toLocaleLowerCase());\n    }\n  }\n\n  handleKeyCode($event) {\n    switch ($event.which) {\n      case KeyCode.ArrowDown:\n        this._handleArrowDown($event);\n\n        break;\n\n      case KeyCode.ArrowUp:\n        this._handleArrowUp($event);\n\n        break;\n\n      case KeyCode.Space:\n        this._handleSpace($event);\n\n        break;\n\n      case KeyCode.Enter:\n        this._handleEnter($event);\n\n        break;\n\n      case KeyCode.Tab:\n        this._handleTab($event);\n\n        break;\n\n      case KeyCode.Esc:\n        this.close();\n        $event.preventDefault();\n        break;\n\n      case KeyCode.Backspace:\n        this._handleBackspace();\n\n        break;\n    }\n  }\n\n  handleMousedown($event) {\n    const target = $event.target;\n\n    if (target.tagName !== 'INPUT') {\n      $event.preventDefault();\n    }\n\n    if (target.classList.contains('ng-clear-wrapper')) {\n      this.handleClearClick();\n      return;\n    }\n\n    if (target.classList.contains('ng-arrow-wrapper')) {\n      this.handleArrowClick();\n      return;\n    }\n\n    if (target.classList.contains('ng-value-icon')) {\n      return;\n    }\n\n    if (!this.focused) {\n      this.focus();\n    }\n\n    if (this.searchable) {\n      this.open();\n    } else {\n      this.toggle();\n    }\n  }\n\n  handleArrowClick() {\n    if (this.isOpen) {\n      this.close();\n    } else {\n      this.open();\n    }\n  }\n\n  handleClearClick() {\n    if (this.hasValue) {\n      this.itemsList.clearSelected(true);\n\n      this._updateNgModel();\n    }\n\n    this._clearSearch();\n\n    this.focus();\n    this.clearEvent.emit();\n\n    this._onSelectionChanged();\n  }\n\n  clearModel() {\n    if (!this.clearable) {\n      return;\n    }\n\n    this.itemsList.clearSelected();\n\n    this._updateNgModel();\n  }\n\n  writeValue(value) {\n    this.itemsList.clearSelected();\n\n    this._handleWriteValue(value);\n\n    this._cd.markForCheck();\n  }\n\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n\n  setDisabledState(state) {\n    this._disabled = state;\n\n    this._cd.markForCheck();\n  }\n\n  toggle() {\n    if (!this.isOpen) {\n      this.open();\n    } else {\n      this.close();\n    }\n  }\n\n  open() {\n    if (this.disabled || this.isOpen || this.itemsList.maxItemsSelected || this._manualOpen) {\n      return;\n    }\n\n    if (!this._isTypeahead && !this.addTag && this.itemsList.noItemsToSelect) {\n      return;\n    }\n\n    this.isOpen = true;\n    this.itemsList.markSelectedOrDefault(this.markFirst);\n    this.openEvent.emit();\n\n    if (!this.searchTerm) {\n      this.focus();\n    }\n\n    this.detectChanges();\n  }\n\n  close() {\n    if (!this.isOpen || this._manualOpen) {\n      return;\n    }\n\n    this.isOpen = false;\n    this._isComposing = false;\n\n    if (!this._editableSearchTerm) {\n      this._clearSearch();\n    } else {\n      this.itemsList.resetFilteredItems();\n    }\n\n    this.itemsList.unmarkItem();\n\n    this._onTouched();\n\n    this.closeEvent.emit();\n\n    this._cd.markForCheck();\n  }\n\n  toggleItem(item) {\n    if (!item || item.disabled || this.disabled) {\n      return;\n    }\n\n    if (this.multiple && item.selected) {\n      this.unselect(item);\n    } else {\n      this.select(item);\n    }\n\n    if (this._editableSearchTerm) {\n      this._setSearchTermFromItems();\n    }\n\n    this._onSelectionChanged();\n  }\n\n  select(item) {\n    if (!item.selected) {\n      this.itemsList.select(item);\n\n      if (this.clearSearchOnAdd && !this._editableSearchTerm) {\n        this._clearSearch();\n      }\n\n      this._updateNgModel();\n\n      if (this.multiple) {\n        this.addEvent.emit(item.value);\n      }\n    }\n\n    if (this.closeOnSelect || this.itemsList.noItemsToSelect) {\n      this.close();\n    }\n  }\n\n  focus() {\n    this.searchInput.nativeElement.focus();\n  }\n\n  blur() {\n    this.searchInput.nativeElement.blur();\n  }\n\n  unselect(item) {\n    if (!item) {\n      return;\n    }\n\n    this.itemsList.unselect(item);\n    this.focus();\n\n    this._updateNgModel();\n\n    this.removeEvent.emit(item);\n  }\n\n  selectTag() {\n    let tag;\n\n    if (isFunction(this.addTag)) {\n      tag = this.addTag(this.searchTerm);\n    } else {\n      tag = this._primitive ? this.searchTerm : {\n        [this.bindLabel]: this.searchTerm\n      };\n    }\n\n    const handleTag = item => this._isTypeahead || !this.isOpen ? this.itemsList.mapItem(item, null) : this.itemsList.addItem(item);\n\n    if (isPromise(tag)) {\n      tag.then(item => this.select(handleTag(item))).catch(() => {});\n    } else if (tag) {\n      this.select(handleTag(tag));\n    }\n  }\n\n  showClear() {\n    return this.clearable && (this.hasValue || this.searchTerm) && !this.disabled;\n  }\n\n  get showAddTag() {\n    if (!this._validTerm) {\n      return false;\n    }\n\n    const term = this.searchTerm.toLowerCase().trim();\n    return this.addTag && !this.itemsList.filteredItems.some(x => x.label.toLowerCase() === term) && (!this.hideSelected && this.isOpen || !this.selectedItems.some(x => x.label.toLowerCase() === term)) && !this.loading;\n  }\n\n  showNoItemsFound() {\n    const empty = this.itemsList.filteredItems.length === 0;\n    return (empty && !this._isTypeahead && !this.loading || empty && this._isTypeahead && this._validTerm && !this.loading) && !this.showAddTag;\n  }\n\n  showTypeToSearch() {\n    const empty = this.itemsList.filteredItems.length === 0;\n    return empty && this._isTypeahead && !this._validTerm && !this.loading;\n  }\n\n  onCompositionStart() {\n    this._isComposing = true;\n  }\n\n  onCompositionEnd(term) {\n    this._isComposing = false;\n\n    if (this.searchWhileComposing) {\n      return;\n    }\n\n    this.filter(term);\n  }\n\n  filter(term) {\n    if (this._isComposing && !this.searchWhileComposing) {\n      return;\n    }\n\n    this.searchTerm = term;\n\n    if (this._isTypeahead && (this._validTerm || this.minTermLength === 0)) {\n      this.typeahead.next(term);\n    }\n\n    if (!this._isTypeahead) {\n      this.itemsList.filter(this.searchTerm);\n\n      if (this.isOpen) {\n        this.itemsList.markSelectedOrDefault(this.markFirst);\n      }\n    }\n\n    this.searchEvent.emit({\n      term,\n      items: this.itemsList.filteredItems.map(x => x.value)\n    });\n    this.open();\n  }\n\n  onInputFocus($event) {\n    if (this.focused) {\n      return;\n    }\n\n    if (this._editableSearchTerm) {\n      this._setSearchTermFromItems();\n    }\n\n    this.element.classList.add('ng-select-focused');\n    this.focusEvent.emit($event);\n    this.focused = true;\n  }\n\n  onInputBlur($event) {\n    this.element.classList.remove('ng-select-focused');\n    this.blurEvent.emit($event);\n\n    if (!this.isOpen && !this.disabled) {\n      this._onTouched();\n    }\n\n    if (this._editableSearchTerm) {\n      this._setSearchTermFromItems();\n    }\n\n    this.focused = false;\n  }\n\n  onItemHover(item) {\n    if (item.disabled) {\n      return;\n    }\n\n    this.itemsList.markItem(item);\n  }\n\n  detectChanges() {\n    if (!this._cd.destroyed) {\n      this._cd.detectChanges();\n    }\n  }\n\n  _setSearchTermFromItems() {\n    const selected = this.selectedItems && this.selectedItems[0];\n    this.searchTerm = selected && selected.label || null;\n  }\n\n  _setItems(items) {\n    const firstItem = items[0];\n    this.bindLabel = this.bindLabel || this._defaultLabel;\n    this._primitive = isDefined(firstItem) ? !isObject(firstItem) : this._primitive || this.bindLabel === this._defaultLabel;\n    this.itemsList.setItems(items);\n\n    if (items.length > 0 && this.hasValue) {\n      this.itemsList.mapSelectedItems();\n    }\n\n    if (this.isOpen && isDefined(this.searchTerm) && !this._isTypeahead) {\n      this.itemsList.filter(this.searchTerm);\n    }\n\n    if (this._isTypeahead || this.isOpen) {\n      this.itemsList.markSelectedOrDefault(this.markFirst);\n    }\n  }\n\n  _setItemsFromNgOptions() {\n    const mapNgOptions = options => {\n      this.items = options.map(option => ({\n        $ngOptionValue: option.value,\n        $ngOptionLabel: option.elementRef.nativeElement.innerHTML,\n        disabled: option.disabled\n      }));\n      this.itemsList.setItems(this.items);\n\n      if (this.hasValue) {\n        this.itemsList.mapSelectedItems();\n      }\n\n      this.detectChanges();\n    };\n\n    const handleOptionChange = () => {\n      const changedOrDestroyed = merge(this.ngOptions.changes, this._destroy$);\n      merge(...this.ngOptions.map(option => option.stateChange$)).pipe(takeUntil(changedOrDestroyed)).subscribe(option => {\n        const item = this.itemsList.findItem(option.value);\n        item.disabled = option.disabled;\n        item.label = option.label || item.label;\n\n        this._cd.detectChanges();\n      });\n    };\n\n    this.ngOptions.changes.pipe(startWith(this.ngOptions), takeUntil(this._destroy$)).subscribe(options => {\n      this.bindLabel = this._defaultLabel;\n      mapNgOptions(options);\n      handleOptionChange();\n    });\n  }\n\n  _isValidWriteValue(value) {\n    if (!isDefined(value) || this.multiple && value === '' || Array.isArray(value) && value.length === 0) {\n      return false;\n    }\n\n    const validateBinding = item => {\n      if (!isDefined(this.compareWith) && isObject(item) && this.bindValue) {\n        this._console.warn(`Setting object(${JSON.stringify(item)}) as your model with bindValue is not allowed unless [compareWith] is used.`);\n\n        return false;\n      }\n\n      return true;\n    };\n\n    if (this.multiple) {\n      if (!Array.isArray(value)) {\n        this._console.warn('Multiple select ngModel should be array.');\n\n        return false;\n      }\n\n      return value.every(item => validateBinding(item));\n    } else {\n      return validateBinding(value);\n    }\n  }\n\n  _handleWriteValue(ngModel) {\n    if (!this._isValidWriteValue(ngModel)) {\n      return;\n    }\n\n    const select = val => {\n      let item = this.itemsList.findItem(val);\n\n      if (item) {\n        this.itemsList.select(item);\n      } else {\n        const isValObject = isObject(val);\n        const isPrimitive = !isValObject && !this.bindValue;\n\n        if (isValObject || isPrimitive) {\n          this.itemsList.select(this.itemsList.mapItem(val, null));\n        } else if (this.bindValue) {\n          item = {\n            [this.bindLabel]: null,\n            [this.bindValue]: val\n          };\n          this.itemsList.select(this.itemsList.mapItem(item, null));\n        }\n      }\n    };\n\n    if (this.multiple) {\n      ngModel.forEach(item => select(item));\n    } else {\n      select(ngModel);\n    }\n  }\n\n  _handleKeyPresses() {\n    if (this.searchable) {\n      return;\n    }\n\n    this._keyPress$.pipe(takeUntil(this._destroy$), tap(letter => this._pressedKeys.push(letter)), debounceTime(200), filter(() => this._pressedKeys.length > 0), map(() => this._pressedKeys.join(''))).subscribe(term => {\n      const item = this.itemsList.findByLabel(term);\n\n      if (item) {\n        if (this.isOpen) {\n          this.itemsList.markItem(item);\n\n          this._scrollToMarked();\n\n          this._cd.markForCheck();\n        } else {\n          this.select(item);\n        }\n      }\n\n      this._pressedKeys = [];\n    });\n  }\n\n  _setInputAttributes() {\n    const input = this.searchInput.nativeElement;\n    const attributes = {\n      type: 'text',\n      autocorrect: 'off',\n      autocapitalize: 'off',\n      autocomplete: this.labelForId ? 'off' : this.dropdownId,\n      ...this.inputAttrs\n    };\n\n    for (const key of Object.keys(attributes)) {\n      input.setAttribute(key, attributes[key]);\n    }\n  }\n\n  _updateNgModel() {\n    const model = [];\n\n    for (const item of this.selectedItems) {\n      if (this.bindValue) {\n        let value = null;\n\n        if (item.children) {\n          const groupKey = this.groupValue ? this.bindValue : this.groupBy;\n          value = item.value[groupKey || this.groupBy];\n        } else {\n          value = this.itemsList.resolveNested(item.value, this.bindValue);\n        }\n\n        model.push(value);\n      } else {\n        model.push(item.value);\n      }\n    }\n\n    const selected = this.selectedItems.map(x => x.value);\n\n    if (this.multiple) {\n      this._onChange(model);\n\n      this.changeEvent.emit(selected);\n    } else {\n      this._onChange(isDefined(model[0]) ? model[0] : null);\n\n      this.changeEvent.emit(selected[0]);\n    }\n\n    this._cd.markForCheck();\n  }\n\n  _clearSearch() {\n    if (!this.searchTerm) {\n      return;\n    }\n\n    this._changeSearch(null);\n\n    this.itemsList.resetFilteredItems();\n  }\n\n  _changeSearch(searchTerm) {\n    this.searchTerm = searchTerm;\n\n    if (this._isTypeahead) {\n      this.typeahead.next(searchTerm);\n    }\n  }\n\n  _scrollToMarked() {\n    if (!this.isOpen || !this.dropdownPanel) {\n      return;\n    }\n\n    this.dropdownPanel.scrollTo(this.itemsList.markedItem);\n  }\n\n  _scrollToTag() {\n    if (!this.isOpen || !this.dropdownPanel) {\n      return;\n    }\n\n    this.dropdownPanel.scrollToTag();\n  }\n\n  _onSelectionChanged() {\n    if (this.isOpen && this.multiple && this.appendTo) {\n      // Make sure items are rendered.\n      this._cd.detectChanges();\n\n      this.dropdownPanel.adjustPosition();\n    }\n  }\n\n  _handleTab($event) {\n    if (this.isOpen === false && !this.addTag) {\n      return;\n    }\n\n    if (this.selectOnTab) {\n      if (this.itemsList.markedItem) {\n        this.toggleItem(this.itemsList.markedItem);\n        $event.preventDefault();\n      } else if (this.showAddTag) {\n        this.selectTag();\n        $event.preventDefault();\n      } else {\n        this.close();\n      }\n    } else {\n      this.close();\n    }\n  }\n\n  _handleEnter($event) {\n    if (this.isOpen || this._manualOpen) {\n      if (this.itemsList.markedItem) {\n        this.toggleItem(this.itemsList.markedItem);\n      } else if (this.showAddTag) {\n        this.selectTag();\n      }\n    } else if (this.openOnEnter) {\n      this.open();\n    } else {\n      return;\n    }\n\n    $event.preventDefault();\n  }\n\n  _handleSpace($event) {\n    if (this.isOpen || this._manualOpen) {\n      return;\n    }\n\n    this.open();\n    $event.preventDefault();\n  }\n\n  _handleArrowDown($event) {\n    if (this._nextItemIsTag(+1)) {\n      this.itemsList.unmarkItem();\n\n      this._scrollToTag();\n    } else {\n      this.itemsList.markNextItem();\n\n      this._scrollToMarked();\n    }\n\n    this.open();\n    $event.preventDefault();\n  }\n\n  _handleArrowUp($event) {\n    if (!this.isOpen) {\n      return;\n    }\n\n    if (this._nextItemIsTag(-1)) {\n      this.itemsList.unmarkItem();\n\n      this._scrollToTag();\n    } else {\n      this.itemsList.markPreviousItem();\n\n      this._scrollToMarked();\n    }\n\n    $event.preventDefault();\n  }\n\n  _nextItemIsTag(nextStep) {\n    const nextIndex = this.itemsList.markedIndex + nextStep;\n    return this.addTag && this.searchTerm && this.itemsList.markedItem && (nextIndex < 0 || nextIndex === this.itemsList.filteredItems.length);\n  }\n\n  _handleBackspace() {\n    if (this.searchTerm || !this.clearable || !this.clearOnBackspace || !this.hasValue) {\n      return;\n    }\n\n    if (this.multiple) {\n      this.unselect(this.itemsList.lastSelectedItem);\n    } else {\n      this.clearModel();\n    }\n  }\n\n  get _isTypeahead() {\n    return this.typeahead && this.typeahead.observers.length > 0;\n  }\n\n  get _validTerm() {\n    const term = this.searchTerm && this.searchTerm.trim();\n    return term && term.length >= this.minTermLength;\n  }\n\n  _mergeGlobalConfig(config) {\n    this.placeholder = this.placeholder || config.placeholder;\n    this.notFoundText = this.notFoundText || config.notFoundText;\n    this.typeToSearchText = this.typeToSearchText || config.typeToSearchText;\n    this.addTagText = this.addTagText || config.addTagText;\n    this.loadingText = this.loadingText || config.loadingText;\n    this.clearAllText = this.clearAllText || config.clearAllText;\n    this.virtualScroll = isDefined(this.virtualScroll) ? this.virtualScroll : isDefined(config.disableVirtualScroll) ? !config.disableVirtualScroll : false;\n    this.openOnEnter = isDefined(this.openOnEnter) ? this.openOnEnter : config.openOnEnter;\n    this.appendTo = this.appendTo || config.appendTo;\n    this.bindValue = this.bindValue || config.bindValue;\n    this.bindLabel = this.bindLabel || config.bindLabel;\n    this.appearance = this.appearance || config.appearance;\n  }\n\n}\n\nNgSelectComponent.ɵfac = function NgSelectComponent_Factory(t) {\n  return new (t || NgSelectComponent)(i0.ɵɵinjectAttribute('class'), i0.ɵɵinjectAttribute('autofocus'), i0.ɵɵdirectiveInject(NgSelectConfig), i0.ɵɵdirectiveInject(SELECTION_MODEL_FACTORY), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(ConsoleService));\n};\n\nNgSelectComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: NgSelectComponent,\n  selectors: [[\"ng-select\"]],\n  contentQueries: function NgSelectComponent_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, NgOptionTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgOptgroupTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgLabelTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgMultiLabelTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgHeaderTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgFooterTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgNotFoundTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgTypeToSearchTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgLoadingTextTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgTagTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgLoadingSpinnerTemplateDirective, 5, TemplateRef);\n      i0.ɵɵcontentQuery(dirIndex, NgOptionComponent, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optgroupTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.labelTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.multiLabelTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notFoundTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.typeToSearchTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loadingTextTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tagTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loadingSpinnerTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ngOptions = _t);\n    }\n  },\n  viewQuery: function NgSelectComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(NgDropdownPanelComponent, 5);\n      i0.ɵɵviewQuery(_c5, 7);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdownPanel = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n    }\n  },\n  hostVars: 20,\n  hostBindings: function NgSelectComponent_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function NgSelectComponent_keydown_HostBindingHandler($event) {\n        return ctx.handleKeyDown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"ng-select-typeahead\", ctx.typeahead)(\"ng-select-multiple\", ctx.multiple)(\"ng-select-taggable\", ctx.addTag)(\"ng-select-searchable\", ctx.searchable)(\"ng-select-clearable\", ctx.clearable)(\"ng-select-opened\", ctx.isOpen)(\"ng-select\", ctx.useDefaultClass)(\"ng-select-disabled\", ctx.disabled)(\"ng-select-filtered\", ctx.filtered)(\"ng-select-single\", ctx.single);\n    }\n  },\n  inputs: {\n    bindLabel: \"bindLabel\",\n    bindValue: \"bindValue\",\n    markFirst: \"markFirst\",\n    placeholder: \"placeholder\",\n    notFoundText: \"notFoundText\",\n    typeToSearchText: \"typeToSearchText\",\n    addTagText: \"addTagText\",\n    loadingText: \"loadingText\",\n    clearAllText: \"clearAllText\",\n    appearance: \"appearance\",\n    dropdownPosition: \"dropdownPosition\",\n    appendTo: \"appendTo\",\n    loading: \"loading\",\n    closeOnSelect: \"closeOnSelect\",\n    hideSelected: \"hideSelected\",\n    selectOnTab: \"selectOnTab\",\n    openOnEnter: \"openOnEnter\",\n    maxSelectedItems: \"maxSelectedItems\",\n    groupBy: \"groupBy\",\n    groupValue: \"groupValue\",\n    bufferAmount: \"bufferAmount\",\n    virtualScroll: \"virtualScroll\",\n    selectableGroup: \"selectableGroup\",\n    selectableGroupAsModel: \"selectableGroupAsModel\",\n    searchFn: \"searchFn\",\n    trackByFn: \"trackByFn\",\n    clearOnBackspace: \"clearOnBackspace\",\n    labelForId: \"labelForId\",\n    inputAttrs: \"inputAttrs\",\n    tabIndex: \"tabIndex\",\n    readonly: \"readonly\",\n    searchWhileComposing: \"searchWhileComposing\",\n    minTermLength: \"minTermLength\",\n    editableSearchTerm: \"editableSearchTerm\",\n    keyDownFn: \"keyDownFn\",\n    typeahead: \"typeahead\",\n    multiple: \"multiple\",\n    addTag: \"addTag\",\n    searchable: \"searchable\",\n    clearable: \"clearable\",\n    isOpen: \"isOpen\",\n    items: \"items\",\n    compareWith: \"compareWith\",\n    clearSearchOnAdd: \"clearSearchOnAdd\"\n  },\n  outputs: {\n    blurEvent: \"blur\",\n    focusEvent: \"focus\",\n    changeEvent: \"change\",\n    openEvent: \"open\",\n    closeEvent: \"close\",\n    searchEvent: \"search\",\n    clearEvent: \"clear\",\n    addEvent: \"add\",\n    removeEvent: \"remove\",\n    scroll: \"scroll\",\n    scrollToEnd: \"scrollToEnd\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => NgSelectComponent),\n    multi: true\n  }, NgDropdownPanelService]), i0.ɵɵNgOnChangesFeature],\n  decls: 14,\n  vars: 19,\n  consts: [[1, \"ng-select-container\", 3, \"mousedown\"], [1, \"ng-value-container\"], [1, \"ng-placeholder\"], [4, \"ngIf\"], [\"role\", \"combobox\", \"aria-haspopup\", \"listbox\", 1, \"ng-input\"], [\"aria-autocomplete\", \"list\", 3, \"readOnly\", \"disabled\", \"value\", \"input\", \"compositionstart\", \"compositionend\", \"focus\", \"blur\", \"change\"], [\"searchInput\", \"\"], [\"class\", \"ng-clear-wrapper\", 3, \"title\", 4, \"ngIf\"], [1, \"ng-arrow-wrapper\"], [1, \"ng-arrow\"], [\"class\", \"ng-dropdown-panel\", \"role\", \"listbox\", \"aria-label\", \"Options list\", 3, \"virtualScroll\", \"bufferAmount\", \"appendTo\", \"position\", \"headerTemplate\", \"footerTemplate\", \"filterValue\", \"items\", \"markedItem\", \"ng-select-multiple\", \"ngClass\", \"id\", \"update\", \"scroll\", \"scrollToEnd\", \"outsideClick\", 4, \"ngIf\"], [\"class\", \"ng-value\", 3, \"ng-value-disabled\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"ng-value\"], [\"defaultLabelTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"aria-hidden\", \"true\", 1, \"ng-value-icon\", \"left\", 3, \"click\"], [1, \"ng-value-label\", 3, \"ngItemLabel\", \"escape\"], [\"defaultLoadingSpinnerTemplate\", \"\"], [3, \"ngTemplateOutlet\"], [1, \"ng-spinner-loader\"], [1, \"ng-clear-wrapper\", 3, \"title\"], [\"aria-hidden\", \"true\", 1, \"ng-clear\"], [\"role\", \"listbox\", \"aria-label\", \"Options list\", 1, \"ng-dropdown-panel\", 3, \"virtualScroll\", \"bufferAmount\", \"appendTo\", \"position\", \"headerTemplate\", \"footerTemplate\", \"filterValue\", \"items\", \"markedItem\", \"ngClass\", \"id\", \"update\", \"scroll\", \"scrollToEnd\", \"outsideClick\"], [\"class\", \"ng-option\", 3, \"ng-option-disabled\", \"ng-option-selected\", \"ng-optgroup\", \"ng-option\", \"ng-option-child\", \"ng-option-marked\", \"click\", \"mouseover\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"ng-option\", \"role\", \"option\", 3, \"ng-option-marked\", \"mouseover\", \"click\", 4, \"ngIf\"], [1, \"ng-option\", 3, \"click\", \"mouseover\"], [\"defaultOptionTemplate\", \"\"], [1, \"ng-option-label\", 3, \"ngItemLabel\", \"escape\"], [\"role\", \"option\", 1, \"ng-option\", 3, \"mouseover\", \"click\"], [\"defaultTagTemplate\", \"\"], [1, \"ng-tag-label\"], [\"defaultNotFoundTemplate\", \"\"], [1, \"ng-option\", \"ng-option-disabled\"], [\"defaultTypeToSearchTemplate\", \"\"], [\"defaultLoadingTextTemplate\", \"\"]],\n  template: function NgSelectComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r52 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"mousedown\", function NgSelectComponent_Template_div_mousedown_0_listener($event) {\n        return ctx.handleMousedown($event);\n      });\n      i0.ɵɵelementStart(1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"div\", 2);\n      i0.ɵɵtext(3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(4, NgSelectComponent_ng_container_4_Template, 2, 2, \"ng-container\", 3);\n      i0.ɵɵtemplate(5, NgSelectComponent_5_Template, 1, 5, undefined, 3);\n      i0.ɵɵelementStart(6, \"div\", 4);\n      i0.ɵɵelementStart(7, \"input\", 5, 6);\n      i0.ɵɵlistener(\"input\", function NgSelectComponent_Template_input_input_7_listener() {\n        i0.ɵɵrestoreView(_r52);\n\n        const _r2 = i0.ɵɵreference(8);\n\n        return ctx.filter(_r2.value);\n      })(\"compositionstart\", function NgSelectComponent_Template_input_compositionstart_7_listener() {\n        return ctx.onCompositionStart();\n      })(\"compositionend\", function NgSelectComponent_Template_input_compositionend_7_listener() {\n        i0.ɵɵrestoreView(_r52);\n\n        const _r2 = i0.ɵɵreference(8);\n\n        return ctx.onCompositionEnd(_r2.value);\n      })(\"focus\", function NgSelectComponent_Template_input_focus_7_listener($event) {\n        return ctx.onInputFocus($event);\n      })(\"blur\", function NgSelectComponent_Template_input_blur_7_listener($event) {\n        return ctx.onInputBlur($event);\n      })(\"change\", function NgSelectComponent_Template_input_change_7_listener($event) {\n        return $event.stopPropagation();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(9, NgSelectComponent_ng_container_9_Template, 4, 1, \"ng-container\", 3);\n      i0.ɵɵtemplate(10, NgSelectComponent_span_10_Template, 3, 1, \"span\", 7);\n      i0.ɵɵelementStart(11, \"span\", 8);\n      i0.ɵɵelement(12, \"span\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(13, NgSelectComponent_ng_dropdown_panel_13_Template, 7, 19, \"ng-dropdown-panel\", 10);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"ng-appearance-outline\", ctx.appearance === \"outline\")(\"ng-has-value\", ctx.hasValue);\n      i0.ɵɵadvance(3);\n      i0.ɵɵtextInterpolate(ctx.placeholder);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (!ctx.multiLabelTemplate || !ctx.multiple) && ctx.selectedItems.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.multiple && ctx.multiLabelTemplate && ctx.selectedValues.length > 0);\n      i0.ɵɵadvance(1);\n      i0.ɵɵattribute(\"aria-expanded\", ctx.isOpen)(\"aria-owns\", ctx.isOpen ? ctx.dropdownId : null);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"readOnly\", !ctx.searchable || ctx.itemsList.maxItemsSelected)(\"disabled\", ctx.disabled)(\"value\", ctx.searchTerm ? ctx.searchTerm : \"\");\n      i0.ɵɵattribute(\"id\", ctx.labelForId)(\"tabindex\", ctx.tabIndex)(\"aria-activedescendant\", ctx.isOpen ? ctx.itemsList == null ? null : ctx.itemsList.markedItem == null ? null : ctx.itemsList.markedItem.htmlId : null)(\"aria-controls\", ctx.isOpen ? ctx.dropdownId : null);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showClear());\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.isOpen);\n    }\n  },\n  directives: [i4.NgIf, i4.NgForOf, i4.NgTemplateOutlet, NgItemLabelDirective, NgDropdownPanelComponent, i4.NgClass],\n  styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:none;user-select:none;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@keyframes load8{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectComponent, [{\n    type: Component,\n    args: [{\n      selector: 'ng-select',\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NgSelectComponent),\n        multi: true\n      }, NgDropdownPanelService],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div\\n    (mousedown)=\\\"handleMousedown($event)\\\"\\n    [class.ng-appearance-outline]=\\\"appearance === 'outline'\\\"\\n    [class.ng-has-value]=\\\"hasValue\\\"\\n    class=\\\"ng-select-container\\\">\\n\\n    <div class=\\\"ng-value-container\\\">\\n        <div class=\\\"ng-placeholder\\\">{{placeholder}}</div>\\n\\n        <ng-container *ngIf=\\\"(!multiLabelTemplate  || !multiple ) && selectedItems.length > 0\\\">\\n            <div [class.ng-value-disabled]=\\\"item.disabled\\\" class=\\\"ng-value\\\" *ngFor=\\\"let item of selectedItems; trackBy: trackByOption\\\">\\n                <ng-template #defaultLabelTemplate>\\n                    <span class=\\\"ng-value-icon left\\\" (click)=\\\"unselect(item);\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n                    <span class=\\\"ng-value-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n                </ng-template>\\n\\n                <ng-template\\n                    [ngTemplateOutlet]=\\\"labelTemplate || defaultLabelTemplate\\\"\\n                    [ngTemplateOutletContext]=\\\"{ item: item.value, clear: clearItem, label: item.label }\\\">\\n                </ng-template>\\n            </div>\\n        </ng-container>\\n\\n        <ng-template *ngIf=\\\"multiple && multiLabelTemplate && selectedValues.length > 0\\\"\\n                [ngTemplateOutlet]=\\\"multiLabelTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ items: selectedValues, clear: clearItem }\\\">\\n        </ng-template>\\n\\n        <div class=\\\"ng-input\\\"\\n            role=\\\"combobox\\\" \\n            [attr.aria-expanded]=\\\"isOpen\\\" \\n            [attr.aria-owns]=\\\"isOpen ? dropdownId : null\\\" \\n            aria-haspopup=\\\"listbox\\\">\\n\\n            <input #searchInput\\n                   [attr.id]=\\\"labelForId\\\"\\n                   [attr.tabindex]=\\\"tabIndex\\\"\\n                   [readOnly]=\\\"!searchable || itemsList.maxItemsSelected\\\"\\n                   [disabled]=\\\"disabled\\\"\\n                   [value]=\\\"searchTerm ? searchTerm : ''\\\"\\n                   (input)=\\\"filter(searchInput.value)\\\"\\n                   (compositionstart)=\\\"onCompositionStart()\\\"\\n                   (compositionend)=\\\"onCompositionEnd(searchInput.value)\\\"\\n                   (focus)=\\\"onInputFocus($event)\\\"\\n                   (blur)=\\\"onInputBlur($event)\\\"\\n                   (change)=\\\"$event.stopPropagation()\\\"\\n                   [attr.aria-activedescendant]=\\\"isOpen ? itemsList?.markedItem?.htmlId : null\\\"\\n                   aria-autocomplete=\\\"list\\\"\\n                   [attr.aria-controls]=\\\"isOpen ? dropdownId : null\\\">\\n        </div>\\n    </div>\\n\\n    <ng-container *ngIf=\\\"loading\\\">\\n        <ng-template #defaultLoadingSpinnerTemplate>\\n            <div class=\\\"ng-spinner-loader\\\"></div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingSpinnerTemplate || defaultLoadingSpinnerTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <span *ngIf=\\\"showClear()\\\" class=\\\"ng-clear-wrapper\\\" title=\\\"{{clearAllText}}\\\">\\n        <span class=\\\"ng-clear\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n    </span>\\n\\n    <span class=\\\"ng-arrow-wrapper\\\">\\n        <span class=\\\"ng-arrow\\\"></span>\\n    </span>\\n</div>\\n\\n<ng-dropdown-panel *ngIf=\\\"isOpen\\\"\\n                   class=\\\"ng-dropdown-panel\\\"\\n                   [virtualScroll]=\\\"virtualScroll\\\"\\n                   [bufferAmount]=\\\"bufferAmount\\\"\\n                   [appendTo]=\\\"appendTo\\\"\\n                   [position]=\\\"dropdownPosition\\\"\\n                   [headerTemplate]=\\\"headerTemplate\\\"\\n                   [footerTemplate]=\\\"footerTemplate\\\"\\n                   [filterValue]=\\\"searchTerm\\\"\\n                   [items]=\\\"itemsList.filteredItems\\\"\\n                   [markedItem]=\\\"itemsList.markedItem\\\"\\n                   (update)=\\\"viewPortItems = $event\\\"\\n                   (scroll)=\\\"scroll.emit($event)\\\"\\n                   (scrollToEnd)=\\\"scrollToEnd.emit($event)\\\"\\n                   (outsideClick)=\\\"close()\\\"\\n                   [class.ng-select-multiple]=\\\"multiple\\\"\\n                   [ngClass]=\\\"appendTo ? classes : null\\\"\\n                   [id]=\\\"dropdownId\\\"\\n                   role=\\\"listbox\\\"\\n                   aria-label=\\\"Options list\\\">\\n\\n    <ng-container>\\n        <div class=\\\"ng-option\\\" [attr.role]=\\\"item.children ? 'group' : 'option'\\\" (click)=\\\"toggleItem(item)\\\" (mouseover)=\\\"onItemHover(item)\\\"\\n                *ngFor=\\\"let item of viewPortItems; trackBy: trackByOption\\\"\\n                [class.ng-option-disabled]=\\\"item.disabled\\\"\\n                [class.ng-option-selected]=\\\"item.selected\\\"\\n                [class.ng-optgroup]=\\\"item.children\\\"\\n                [class.ng-option]=\\\"!item.children\\\"\\n                [class.ng-option-child]=\\\"!!item.parent\\\"\\n                [class.ng-option-marked]=\\\"item === itemsList.markedItem\\\"\\n                [attr.aria-selected]=\\\"item.selected\\\"\\n                [attr.id]=\\\"item?.htmlId\\\">\\n\\n            <ng-template #defaultOptionTemplate>\\n                <span class=\\\"ng-option-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"item.children ? (optgroupTemplate || defaultOptionTemplate) : (optionTemplate || defaultOptionTemplate)\\\"\\n                [ngTemplateOutletContext]=\\\"{ item: item.value, item$:item, index: item.index, searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n\\n        <div class=\\\"ng-option\\\" [class.ng-option-marked]=\\\"!itemsList.markedItem\\\" (mouseover)=\\\"itemsList.unmarkItem()\\\" role=\\\"option\\\" (click)=\\\"selectTag()\\\" *ngIf=\\\"showAddTag\\\">\\n            <ng-template #defaultTagTemplate>\\n                <span><span class=\\\"ng-tag-label\\\">{{addTagText}}</span>\\\"{{searchTerm}}\\\"</span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"tagTemplate || defaultTagTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showNoItemsFound()\\\">\\n        <ng-template #defaultNotFoundTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{notFoundText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"notFoundTemplate || defaultNotFoundTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showTypeToSearch()\\\">\\n        <ng-template #defaultTypeToSearchTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{typeToSearchText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"typeToSearchTemplate || defaultTypeToSearchTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"loading && itemsList.filteredItems.length === 0\\\">\\n        <ng-template #defaultLoadingTextTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{loadingText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingTextTemplate || defaultLoadingTextTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n</ng-dropdown-panel>\\n\",\n      styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:none;user-select:none;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@keyframes load8{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['class']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['autofocus']\n      }]\n    }, {\n      type: NgSelectConfig\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [SELECTION_MODEL_FACTORY]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: ConsoleService\n    }];\n  }, {\n    bindLabel: [{\n      type: Input\n    }],\n    bindValue: [{\n      type: Input\n    }],\n    markFirst: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    notFoundText: [{\n      type: Input\n    }],\n    typeToSearchText: [{\n      type: Input\n    }],\n    addTagText: [{\n      type: Input\n    }],\n    loadingText: [{\n      type: Input\n    }],\n    clearAllText: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }],\n    dropdownPosition: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    closeOnSelect: [{\n      type: Input\n    }],\n    hideSelected: [{\n      type: Input\n    }],\n    selectOnTab: [{\n      type: Input\n    }],\n    openOnEnter: [{\n      type: Input\n    }],\n    maxSelectedItems: [{\n      type: Input\n    }],\n    groupBy: [{\n      type: Input\n    }],\n    groupValue: [{\n      type: Input\n    }],\n    bufferAmount: [{\n      type: Input\n    }],\n    virtualScroll: [{\n      type: Input\n    }],\n    selectableGroup: [{\n      type: Input\n    }],\n    selectableGroupAsModel: [{\n      type: Input\n    }],\n    searchFn: [{\n      type: Input\n    }],\n    trackByFn: [{\n      type: Input\n    }],\n    clearOnBackspace: [{\n      type: Input\n    }],\n    labelForId: [{\n      type: Input\n    }],\n    inputAttrs: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    searchWhileComposing: [{\n      type: Input\n    }],\n    minTermLength: [{\n      type: Input\n    }],\n    editableSearchTerm: [{\n      type: Input\n    }],\n    keyDownFn: [{\n      type: Input\n    }],\n    typeahead: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-typeahead']\n    }],\n    multiple: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-multiple']\n    }],\n    addTag: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-taggable']\n    }],\n    searchable: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-searchable']\n    }],\n    clearable: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-clearable']\n    }],\n    isOpen: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: ['class.ng-select-opened']\n    }],\n    items: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    clearSearchOnAdd: [{\n      type: Input\n    }],\n    blurEvent: [{\n      type: Output,\n      args: ['blur']\n    }],\n    focusEvent: [{\n      type: Output,\n      args: ['focus']\n    }],\n    changeEvent: [{\n      type: Output,\n      args: ['change']\n    }],\n    openEvent: [{\n      type: Output,\n      args: ['open']\n    }],\n    closeEvent: [{\n      type: Output,\n      args: ['close']\n    }],\n    searchEvent: [{\n      type: Output,\n      args: ['search']\n    }],\n    clearEvent: [{\n      type: Output,\n      args: ['clear']\n    }],\n    addEvent: [{\n      type: Output,\n      args: ['add']\n    }],\n    removeEvent: [{\n      type: Output,\n      args: ['remove']\n    }],\n    scroll: [{\n      type: Output,\n      args: ['scroll']\n    }],\n    scrollToEnd: [{\n      type: Output,\n      args: ['scrollToEnd']\n    }],\n    optionTemplate: [{\n      type: ContentChild,\n      args: [NgOptionTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    optgroupTemplate: [{\n      type: ContentChild,\n      args: [NgOptgroupTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    labelTemplate: [{\n      type: ContentChild,\n      args: [NgLabelTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    multiLabelTemplate: [{\n      type: ContentChild,\n      args: [NgMultiLabelTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    headerTemplate: [{\n      type: ContentChild,\n      args: [NgHeaderTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    footerTemplate: [{\n      type: ContentChild,\n      args: [NgFooterTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    notFoundTemplate: [{\n      type: ContentChild,\n      args: [NgNotFoundTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    typeToSearchTemplate: [{\n      type: ContentChild,\n      args: [NgTypeToSearchTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    loadingTextTemplate: [{\n      type: ContentChild,\n      args: [NgLoadingTextTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    tagTemplate: [{\n      type: ContentChild,\n      args: [NgTagTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    loadingSpinnerTemplate: [{\n      type: ContentChild,\n      args: [NgLoadingSpinnerTemplateDirective, {\n        read: TemplateRef\n      }]\n    }],\n    dropdownPanel: [{\n      type: ViewChild,\n      args: [forwardRef(() => NgDropdownPanelComponent)]\n    }],\n    searchInput: [{\n      type: ViewChild,\n      args: ['searchInput', {\n        static: true\n      }]\n    }],\n    ngOptions: [{\n      type: ContentChildren,\n      args: [NgOptionComponent, {\n        descendants: true\n      }]\n    }],\n    useDefaultClass: [{\n      type: HostBinding,\n      args: ['class.ng-select']\n    }],\n    disabled: [{\n      type: HostBinding,\n      args: ['class.ng-select-disabled']\n    }],\n    filtered: [{\n      type: HostBinding,\n      args: ['class.ng-select-filtered']\n    }],\n    single: [{\n      type: HostBinding,\n      args: ['class.ng-select-single']\n    }],\n    handleKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\n\nfunction DefaultSelectionModelFactory() {\n  return new DefaultSelectionModel();\n}\n\nclass DefaultSelectionModel {\n  constructor() {\n    this._selected = [];\n  }\n\n  get value() {\n    return this._selected;\n  }\n\n  select(item, multiple, groupAsModel) {\n    item.selected = true;\n\n    if (!item.children || !multiple && groupAsModel) {\n      this._selected.push(item);\n    }\n\n    if (multiple) {\n      if (item.parent) {\n        const childrenCount = item.parent.children.length;\n        const selectedCount = item.parent.children.filter(x => x.selected).length;\n        item.parent.selected = childrenCount === selectedCount;\n      } else if (item.children) {\n        this._setChildrenSelectedState(item.children, true);\n\n        this._removeChildren(item);\n\n        if (groupAsModel && this._activeChildren(item)) {\n          this._selected = [...this._selected.filter(x => x.parent !== item), item];\n        } else {\n          this._selected = [...this._selected, ...item.children.filter(x => !x.disabled)];\n        }\n      }\n    }\n  }\n\n  unselect(item, multiple) {\n    this._selected = this._selected.filter(x => x !== item);\n    item.selected = false;\n\n    if (multiple) {\n      if (item.parent && item.parent.selected) {\n        const children = item.parent.children;\n\n        this._removeParent(item.parent);\n\n        this._removeChildren(item.parent);\n\n        this._selected.push(...children.filter(x => x !== item && !x.disabled));\n\n        item.parent.selected = false;\n      } else if (item.children) {\n        this._setChildrenSelectedState(item.children, false);\n\n        this._removeChildren(item);\n      }\n    }\n  }\n\n  clear(keepDisabled) {\n    this._selected = keepDisabled ? this._selected.filter(x => x.disabled) : [];\n  }\n\n  _setChildrenSelectedState(children, selected) {\n    for (const child of children) {\n      if (child.disabled) {\n        continue;\n      }\n\n      child.selected = selected;\n    }\n  }\n\n  _removeChildren(parent) {\n    this._selected = [...this._selected.filter(x => x.parent !== parent), ...parent.children.filter(x => x.parent === parent && x.disabled && x.selected)];\n  }\n\n  _removeParent(parent) {\n    this._selected = this._selected.filter(x => x !== parent);\n  }\n\n  _activeChildren(item) {\n    return item.children.every(x => !x.disabled || x.selected);\n  }\n\n}\n\nclass NgSelectModule {}\n\nNgSelectModule.ɵfac = function NgSelectModule_Factory(t) {\n  return new (t || NgSelectModule)();\n};\n\nNgSelectModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgSelectModule\n});\nNgSelectModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: SELECTION_MODEL_FACTORY,\n    useValue: DefaultSelectionModelFactory\n  }],\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [NgDropdownPanelComponent, NgOptionComponent, NgSelectComponent, NgOptgroupTemplateDirective, NgOptionTemplateDirective, NgLabelTemplateDirective, NgMultiLabelTemplateDirective, NgHeaderTemplateDirective, NgFooterTemplateDirective, NgNotFoundTemplateDirective, NgTypeToSearchTemplateDirective, NgLoadingTextTemplateDirective, NgTagTemplateDirective, NgLoadingSpinnerTemplateDirective, NgItemLabelDirective],\n      imports: [CommonModule],\n      exports: [NgSelectComponent, NgOptionComponent, NgOptgroupTemplateDirective, NgOptionTemplateDirective, NgLabelTemplateDirective, NgMultiLabelTemplateDirective, NgHeaderTemplateDirective, NgFooterTemplateDirective, NgNotFoundTemplateDirective, NgTypeToSearchTemplateDirective, NgLoadingTextTemplateDirective, NgTagTemplateDirective, NgLoadingSpinnerTemplateDirective],\n      providers: [{\n        provide: SELECTION_MODEL_FACTORY,\n        useValue: DefaultSelectionModelFactory\n      }]\n    }]\n  }], null, null);\n})();\n/*\n * Public API Surface of ng-select\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { NgFooterTemplateDirective, NgHeaderTemplateDirective, NgItemLabelDirective, NgLabelTemplateDirective, NgLoadingSpinnerTemplateDirective, NgLoadingTextTemplateDirective, NgMultiLabelTemplateDirective, NgNotFoundTemplateDirective, NgOptgroupTemplateDirective, NgOptionComponent, NgOptionTemplateDirective, NgSelectComponent, NgSelectConfig, NgSelectModule, NgTagTemplateDirective, NgTypeToSearchTemplateDirective, SELECTION_MODEL_FACTORY };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@ng-select/ng-select/fesm2020/ng-select-ng-select.mjs"], "names": ["i0", "Directive", "Input", "Injectable", "EventEmitter", "ElementRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Optional", "Inject", "Output", "ViewChild", "InjectionToken", "forwardRef", "TemplateRef", "Attribute", "HostBinding", "ContentChild", "ContentChildren", "HostListener", "NgModule", "NG_VALUE_ACCESSOR", "takeUntil", "auditTime", "startWith", "tap", "debounceTime", "filter", "map", "animationFrameScheduler", "asapScheduler", "Subject", "fromEvent", "merge", "i4", "DOCUMENT", "CommonModule", "unescapedHTMLExp", "hasUnescapedHTMLExp", "RegExp", "source", "htmlEscapes", "escapeHTML", "value", "test", "replace", "chr", "isDefined", "undefined", "isObject", "isPromise", "Promise", "isFunction", "Function", "NgItemLabelDirective", "constructor", "element", "escape", "ngOnChanges", "changes", "nativeElement", "innerHTML", "ngItemLabel", "ɵfac", "ɵdir", "type", "args", "selector", "NgOptionTemplateDirective", "template", "NgOptgroupTemplateDirective", "NgLabelTemplateDirective", "NgMultiLabelTemplateDirective", "NgHeaderTemplateDirective", "NgFooterTemplateDirective", "NgNotFoundTemplateDirective", "NgTypeToSearchTemplateDirective", "NgLoadingTextTemplateDirective", "NgTagTemplateDirective", "NgLoadingSpinnerTemplateDirective", "newId", "val", "Math", "random", "toString", "diacritics", "stripSpecialChars", "text", "match", "a", "ItemsList", "_ngSelect", "_selectionModel", "_items", "_filteredItems", "_markedIndex", "items", "filteredItems", "markedIndex", "selectedItems", "markedItem", "noItemsToSelect", "hideSelected", "length", "maxItemsSelected", "multiple", "maxSelectedItems", "lastSelectedItem", "i", "item", "disabled", "setItems", "index", "mapItem", "groupBy", "_groups", "_groupBy", "_flatten", "Map", "set", "select", "selected", "clearSelected", "selectableGroupAsModel", "_hideSelected", "unselect", "_showSelected", "findItem", "find<PERSON><PERSON>", "compareWith", "bindValue", "children", "resolveNested", "label", "bind<PERSON>abel", "find", "addItem", "option", "push", "keepDisabled", "clear", "for<PERSON>ach", "marked", "resetFilteredItems", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "term", "toLocaleLowerCase", "substr", "searchFn", "_defaultSearchFn", "key", "Array", "from", "keys", "matchedItems", "get", "parent", "searchItem", "last", "slice", "head", "x", "unmarkItem", "markNextItem", "_stepToItem", "markPreviousItem", "markItem", "indexOf", "markSele<PERSON><PERSON>rDefault", "<PERSON><PERSON><PERSON><PERSON>", "lastMarkedIndex", "_getLastMarkedIndex", "findIndex", "split", "len", "$ngOptionLabel", "$ngOptionValue", "htmlId", "dropdownId", "mapSelectedItems", "parentExists", "child", "sort", "b", "every", "search", "opt", "_getNextItemIndex", "steps", "selectedIndex", "max", "prop", "groups", "isArray", "isFnKey", "keyFn", "group", "isGroupByFn", "withoutGroup", "isObjectKey", "String", "selectableGroup", "groupKey", "groupValue", "KeyCode", "NgDropdownPanelService", "_dimensions", "itemHeight", "panelHeight", "itemsPerViewport", "dimensions", "calculateItems", "scrollPos", "itemsLength", "buffer", "d", "scrollHeight", "scrollTop", "indexByScrollTop", "end", "min", "ceil", "maxStartEnd", "maxStart", "start", "floor", "topPadding", "isNaN", "setDimensions", "getScrollTo", "itemTop", "lastScroll", "itemBottom", "top", "bottom", "ɵprov", "CSS_POSITIONS", "SCROLL_SCHEDULER", "requestAnimationFrame", "NgDropdownPanelComponent", "_renderer", "_zone", "_panelService", "_elementRef", "_document", "position", "virtualScroll", "filterValue", "update", "scroll", "scrollToEnd", "outsideClick", "_destroy$", "_scrollToEndFired", "_updateScrollHeight", "_lastScrollPosition", "_dropdown", "currentPosition", "_currentPosition", "_itemsLength", "_onItemsLengthChanged", "_startOffset", "offset", "ngOnInit", "_select", "parentElement", "_virtualPadding", "paddingElementRef", "_scrollablePanel", "scrollElementRef", "_contentPanel", "contentElementRef", "_handleScroll", "_handleOutsideClick", "_appendDropdown", "_setupMousedownListener", "change", "_onItemsChange", "currentValue", "firstChange", "ngOnDestroy", "next", "complete", "unsubscribe", "appendTo", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "scrollTo", "startFromOption", "querySelector", "offsetTop", "clientHeight", "scrollToTag", "panel", "adjustPosition", "_updateYPosition", "_handleDropdownPosition", "_calculateCurrentPosition", "includes", "_updateDropdownClass", "style", "opacity", "REMOVE_CSS_CLASS", "removeClass", "ADD_CSS_CLASS", "addClass", "runOutsideAngular", "pipe", "subscribe", "e", "path", "<PERSON><PERSON><PERSON>", "target", "_onContentScrolled", "capture", "$event", "_checkToClose", "contains", "shadowRoot", "run", "emit", "_updateItemsRange", "_setVirtualHeight", "_updateItems", "resolve", "then", "_measureDimensions", "_renderItemsRange", "_fireScrollToEnd", "_updateVirtualHeight", "height", "range", "bufferAmount", "transform", "first", "optionHeight", "padding", "dropdownEl", "selectRect", "getBoundingClientRect", "document", "documentElement", "body", "window", "pageYOffset", "dropdownHeight", "_parent", "Error", "_updateXPosition", "append<PERSON><PERSON><PERSON>", "offsetLeft", "left", "width", "min<PERSON><PERSON><PERSON>", "delta", "offsetBottom", "event", "tagName", "preventDefault", "Renderer2", "NgZone", "ɵcmp", "NgIf", "NgTemplateOutlet", "changeDetection", "OnPush", "encapsulation", "None", "decorators", "headerTemplate", "footerTemplate", "read", "static", "NgOptionComponent", "elementRef", "stateChange$", "_disabled", "_isDisabled", "textContent", "trim", "ngAfterViewChecked", "_<PERSON><PERSON><PERSON><PERSON>", "NgSelectConfig", "notFoundText", "typeToSearchText", "addTagText", "loadingText", "clearAllText", "disableVirtualScroll", "openOnEnter", "appearance", "providedIn", "ConsoleService", "warn", "message", "console", "SELECTION_MODEL_FACTORY", "NgSelectComponent", "classes", "autoFocus", "config", "newSelectionModel", "_cd", "_console", "<PERSON><PERSON><PERSON><PERSON>", "dropdownPosition", "loading", "closeOnSelect", "selectOnTab", "trackByFn", "clearOnBackspace", "labelForId", "inputAttrs", "readonly", "searchWhileComposing", "minT<PERSON><PERSON><PERSON><PERSON><PERSON>", "editableSearchTerm", "keyDownFn", "_", "addTag", "searchable", "clearable", "isOpen", "blurEvent", "focusEvent", "changeEvent", "openEvent", "closeEvent", "searchEvent", "clearEvent", "addEvent", "removeEvent", "useDefaultClass", "viewPortItems", "searchTerm", "_defaultLabel", "_pressed<PERSON><PERSON>s", "_isComposing", "_keyPress$", "_onChange", "_onTouched", "clearItem", "trackByOption", "_mergeGlobalConfig", "itemsList", "_itemsAreUsed", "_compareWith", "fn", "clearSearchOnAdd", "_clearSearchOnAdd", "filtered", "single", "_editableSearchTerm", "<PERSON><PERSON><PERSON><PERSON>", "hasValue", "currentPanelPosition", "dropdownPanel", "_handleKeyPresses", "_setInputAttributes", "_setItems", "_manualOpen", "ngAfterViewInit", "_setItemsFromNgOptions", "focus", "handleKeyDown", "keyCode", "which", "handleKeyCode", "ArrowDown", "_handleArrowDown", "ArrowUp", "_handleArrowUp", "Space", "_handleSpace", "Enter", "_handleEnter", "Tab", "_handleTab", "Esc", "close", "Backspace", "_handleBackspace", "handleMousedown", "classList", "handleClearClick", "handleArrowClick", "focused", "open", "toggle", "_updateNgModel", "_clearSearch", "_onSelectionChanged", "clearModel", "writeValue", "_handleWriteValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "registerOnTouched", "setDisabledState", "state", "_isTypeahead", "detectChanges", "toggleItem", "_setSearchTermFromItems", "searchInput", "blur", "selectTag", "tag", "_primitive", "handleTag", "catch", "showClear", "showAddTag", "_validTerm", "toLowerCase", "some", "showNoItemsFound", "empty", "showTypeToSearch", "onCompositionStart", "onCompositionEnd", "typeahead", "onInputFocus", "add", "onInputBlur", "remove", "onItemHover", "destroyed", "firstItem", "mapNgOptions", "options", "handleOptionChange", "changedOrDestroyed", "ngOptions", "_isValidWriteValue", "validateBinding", "JSON", "stringify", "ngModel", "isValObject", "isPrimitive", "letter", "join", "_scrollToMarked", "input", "attributes", "autocorrect", "autocapitalize", "autocomplete", "Object", "setAttribute", "model", "_changeSearch", "_scrollToTag", "_nextItemIsTag", "nextStep", "nextIndex", "observers", "placeholder", "ChangeDetectorRef", "provide", "useExisting", "multi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ng<PERSON><PERSON>", "providers", "styles", "tabIndex", "optionTemplate", "optgroupTemplate", "labelTemplate", "multiLabelTemplate", "notFoundTemplate", "typeToSearchTemplate", "loadingTextTemplate", "tagTemplate", "loadingSpinnerTemplate", "descendants", "DefaultSelectionModelFactory", "DefaultSelectionModel", "_selected", "groupAsModel", "childrenCount", "selectedCount", "_setChildrenSelectedState", "_remove<PERSON><PERSON><PERSON>n", "_active<PERSON><PERSON><PERSON><PERSON>", "_removeParent", "NgSelectModule", "ɵmod", "ɵinj", "useValue", "declarations", "imports", "exports"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,KAApB,EAA2BC,UAA3B,EAAuCC,YAAvC,EAAqDC,UAArD,EAAiEC,SAAjE,EAA4EC,uBAA5E,EAAqGC,iBAArG,EAAwHC,QAAxH,EAAkIC,MAAlI,EAA0IC,MAA1I,EAAkJC,SAAlJ,EAA6JC,cAA7J,EAA6KC,UAA7K,EAAyLC,WAAzL,EAAsMC,SAAtM,EAAiNC,WAAjN,EAA8NC,YAA9N,EAA4OC,eAA5O,EAA6PC,YAA7P,EAA2QC,QAA3Q,QAA2R,eAA3R;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,SAASC,SAAT,EAAoBC,SAApB,EAA+BC,SAA/B,EAA0CC,GAA1C,EAA+CC,YAA/C,EAA6DC,MAA7D,EAAqEC,GAArE,QAAgF,gBAAhF;AACA,SAASC,uBAAT,EAAkCC,aAAlC,EAAiDC,OAAjD,EAA0DC,SAA1D,EAAqEC,KAArE,QAAkF,MAAlF;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,EAAmBC,YAAnB,QAAuC,iBAAvC;;;;;;;;;;;;;AAwCuGrC,IAAAA,EAmtD/F,4B;AAntD+FA,IAAAA,EAotD3F,yB;AAptD2FA,IAAAA,EAqtD/F,e;;;;mBArtD+FA,E;AAAAA,IAAAA,EAotD7E,a;AAptD6EA,IAAAA,EAotD7E,kFAptD6EA,EAotD7E,6C;;;;;;AAptD6EA,IAAAA,EA4tD/F,4B;AA5tD+FA,IAAAA,EA6tD3F,yB;AA7tD2FA,IAAAA,EA8tD/F,e;;;;mBA9tD+FA,E;AAAAA,IAAAA,EA6tD7E,a;AA7tD6EA,IAAAA,EA6tD7E,kFA7tD6EA,EA6tD7E,6C;;;;;;;;;iBA7tD6EA,E;;AAAAA,IAAAA,EAumF8zE,8B;AAvmF9zEA,IAAAA,EAumFi2E;AAvmFj2EA,MAAAA,EAumFi2E;AAAA,sBAvmFj2EA,EAumFi2E;AAAA,sBAvmFj2EA,EAumFi2E;AAAA,aAAU,yBAAV;AAAA,M;AAvmFj2EA,IAAAA,EAumFk5E,kB;AAvmFl5EA,IAAAA,EAumFw5E,e;AAvmFx5EA,IAAAA,EAumFq7E,yB;;;;oBAvmFr7EA,E;mBAAAA,E;AAAAA,IAAAA,EAumFo9E,a;AAvmFp9EA,IAAAA,EAumFo9E,sE;;;;;;;;;;;;;;;;AAvmFp9EA,IAAAA,EAumFknE,6B;AAvmFlnEA,IAAAA,EAumFqwE,6GAvmFrwEA,EAumFqwE,wB;AAvmFrwEA,IAAAA,EAumFokF,sG;AAvmFpkFA,IAAAA,EAumF8zF,e;;;;;;gBAvmF9zFA,E;;mBAAAA,E;AAAAA,IAAAA,EAumFunE,mD;AAvmFvnEA,IAAAA,EAumFsmF,a;AAvmFtmFA,IAAAA,EAumFsmF,wFAvmFtmFA,EAumFsmF,yE;;;;;;AAvmFtmFA,IAAAA,EAumF2gE,2B;AAvmF3gEA,IAAAA,EAumFknE,gF;AAvmFlnEA,IAAAA,EAumF80F,wB;;;;mBAvmF90FA,E;AAAAA,IAAAA,EAumFwsE,a;AAvmFxsEA,IAAAA,EAumFwsE,kF;;;;;;;;;;;;;;;AAvmFxsEA,IAAAA,EAumFy2F,mF;;;;mBAvmFz2FA,E;AAAAA,IAAAA,EAumF68F,sFAvmF78FA,EAumF68F,kE;;;;;;AAvmF78FA,IAAAA,EAumFo0I,wB;;;;;;;;AAvmFp0IA,IAAAA,EAumFguI,2B;AAvmFhuIA,IAAAA,EAumF0wI,uGAvmF1wIA,EAumF0wI,wB;AAvmF1wIA,IAAAA,EAumF+4I,gG;AAvmF/4IA,IAAAA,EAumFshJ,wB;;;;iBAvmFthJA,E;;mBAAAA,E;AAAAA,IAAAA,EAumFy6I,a;AAvmFz6IA,IAAAA,EAumFy6I,sE;;;;;;AAvmFz6IA,IAAAA,EAumF6iJ,8B;AAvmF7iJA,IAAAA,EAumFyoJ,8B;AAvmFzoJA,IAAAA,EAumFurJ,kB;AAvmFvrJA,IAAAA,EAumF6rJ,e;AAvmF7rJA,IAAAA,EAumF0sJ,e;;;;mBAvmF1sJA,E;AAAAA,IAAAA,EAumFomJ,oD;;;;;;AAvmFpmJA,IAAAA,EAumFilN,yB;;;;qBAvmFjlNA,E;oBAAAA,E;AAAAA,IAAAA,EAumFinN,wE;;;;;;;;;;;;;;;;;iBAvmFjnNA,E;;AAAAA,IAAAA,EAumF81L,6B;AAvmF91LA,IAAAA,EAumF06L;AAAA,0BAvmF16LA,EAumF06L;AAAA;AAAA,sBAvmF16LA,EAumF06L;AAAA,aAAU,4BAAV;AAAA;AAAA,0BAvmF16LA,EAumF06L;AAAA;AAAA,sBAvmF16LA,EAumF06L;AAAA,aAA2C,6BAA3C;AAAA,M;AAvmF16LA,IAAAA,EAumF2hN,mHAvmF3hNA,EAumF2hN,wB;AAvmF3hNA,IAAAA,EAumFytN,4G;AAvmFztNA,IAAAA,EAumFuhO,e;;;;;;iBAvmFvhOA,E;;oBAAAA,E;AAAAA,IAAAA,EAumFwkM,sQ;AAvmFxkMA,IAAAA,EAumFu3L,iJ;AAvmFv3LA,IAAAA,EAumFuvN,a;AAvmFvvNA,IAAAA,EAumFuvN,kJAvmFvvNA,EAumFuvN,wF;;;;;;AAvmFvvNA,IAAAA,EAumF0xO,0B;AAvmF1xOA,IAAAA,EAumFgyO,8B;AAvmFhyOA,IAAAA,EAumF6zO,U;AAvmF7zOA,IAAAA,EAumF20O,e;AAvmF30OA,IAAAA,EAumFk1O,U;AAvmFl1OA,IAAAA,EAumFo2O,e;;;;oBAvmFp2OA,E;AAAAA,IAAAA,EAumF6zO,a;AAvmF7zOA,IAAAA,EAumF6zO,sC;AAvmF7zOA,IAAAA,EAumFk1O,a;AAvmFl1OA,IAAAA,EAumFk1O,mD;;;;;;;;iBAvmFl1OA,E;;AAAAA,IAAAA,EAumFyiO,6B;AAvmFziOA,IAAAA,EAumFqnO;AAvmFrnOA,MAAAA,EAumFqnO;AAAA,sBAvmFrnOA,EAumFqnO;AAAA,aAAc,8BAAd;AAAA;AAvmFrnOA,MAAAA,EAumFqnO;AAAA,sBAvmFrnOA,EAumFqnO;AAAA,aAAiE,mBAAjE;AAAA,M;AAvmFrnOA,IAAAA,EAumFuuO,mHAvmFvuOA,EAumFuuO,wB;AAvmFvuOA,IAAAA,EAumFu5O,4G;AAvmFv5OA,IAAAA,EAumF8lP,e;;;;iBAvmF9lPA,E;;oBAAAA,E;AAAAA,IAAAA,EAumFkkO,+D;AAvmFlkOA,IAAAA,EAumFq7O,a;AAvmFr7OA,IAAAA,EAumFq7O,wFAvmFr7OA,EAumFq7O,6C;;;;;;AAvmFr7OA,IAAAA,EAumF0uP,6B;AAvmF1uPA,IAAAA,EAumFsxP,U;AAvmFtxPA,IAAAA,EAumFsyP,e;;;;oBAvmFtyPA,E;AAAAA,IAAAA,EAumFsxP,a;AAvmFtxPA,IAAAA,EAumFsxP,wC;;;;;;;;AAvmFtxPA,IAAAA,EAumFioP,2B;AAvmFjoPA,IAAAA,EAumFsrP,4HAvmFtrPA,EAumFsrP,wB;AAvmFtrPA,IAAAA,EAumFg1P,qH;AAvmFh1PA,IAAAA,EAumFihQ,wB;;;;iBAvmFjhQA,E;;oBAAAA,E;AAAAA,IAAAA,EAumF02P,a;AAvmF12PA,IAAAA,EAumF02P,6FAvmF12PA,EAumF02P,6C;;;;;;AAvmF12PA,IAAAA,EAumFqpQ,6B;AAvmFrpQA,IAAAA,EAumFisQ,U;AAvmFjsQA,IAAAA,EAumFqtQ,e;;;;oBAvmFrtQA,E;AAAAA,IAAAA,EAumFisQ,a;AAvmFjsQA,IAAAA,EAumFisQ,4C;;;;;;;;AAvmFjsQA,IAAAA,EAumFwiQ,2B;AAvmFxiQA,IAAAA,EAumF6lQ,4HAvmF7lQA,EAumF6lQ,wB;AAvmF7lQA,IAAAA,EAumF+vQ,qH;AAvmF/vQA,IAAAA,EAumFk4Q,wB;;;;iBAvmFl4QA,E;;oBAAAA,E;AAAAA,IAAAA,EAumFyxQ,a;AAvmFzxQA,IAAAA,EAumFyxQ,qE;;;;;;AAvmFzxQA,IAAAA,EAumFkiR,6B;AAvmFliRA,IAAAA,EAumF8kR,U;AAvmF9kRA,IAAAA,EAumF6lR,e;;;;oBAvmF7lRA,E;AAAAA,IAAAA,EAumF8kR,a;AAvmF9kRA,IAAAA,EAumF8kR,uC;;;;;;;;AAvmF9kRA,IAAAA,EAumFy5Q,2B;AAvmFz5QA,IAAAA,EAumF2+Q,4HAvmF3+QA,EAumF2+Q,wB;AAvmF3+QA,IAAAA,EAumFuoR,qH;AAvmFvoRA,IAAAA,EAumF80R,wB;;;;iBAvmF90RA,E;;oBAAAA,E;AAAAA,IAAAA,EAumFiqR,a;AAvmFjqRA,IAAAA,EAumFiqR,gGAvmFjqRA,EAumFiqR,6C;;;;;;iBAvmFjqRA,E;;AAAAA,IAAAA,EAumF6zJ,2C;AAvmF7zJA,IAAAA,EAumFk4K;AAvmFl4KA,MAAAA,EAumFk4K;AAAA,sBAvmFl4KA,EAumFk4K;AAAA;AAAA;AAvmFl4KA,MAAAA,EAumFk4K;AAAA,sBAvmFl4KA,EAumFk4K;AAAA,aAAmE,2BAAnE;AAAA;AAvmFl4KA,MAAAA,EAumFk4K;AAAA,sBAvmFl4KA,EAumFk4K;AAAA,aAA6H,gCAA7H;AAAA;AAvmFl4KA,MAAAA,EAumFk4K;AAAA,sBAvmFl4KA,EAumFk4K;AAAA,aAA6L,eAA7L;AAAA,M;AAvmFl4KA,IAAAA,EAumFs0L,2B;AAvmFt0LA,IAAAA,EAumF81L,uF;AAvmF91LA,IAAAA,EAumFyiO,sF;AAvmFziOA,IAAAA,EAumF0mP,wB;AAvmF1mPA,IAAAA,EAumFioP,uG;AAvmFjoPA,IAAAA,EAumFwiQ,uG;AAvmFxiQA,IAAAA,EAumFy5Q,uG;AAvmFz5QA,IAAAA,EAumFi2R,e;;;;mBAvmFj2RA,E;AAAAA,IAAAA,EAumF6lL,mD;AAvmF7lLA,IAAAA,EAumFq6J,8a;AAvmFr6JA,IAAAA,EAumF4gM,a;AAvmF5gMA,IAAAA,EAumF4gM,kF;AAvmF5gMA,IAAAA,EAumFqsO,a;AAvmFrsOA,IAAAA,EAumFqsO,sC;AAvmFrsOA,IAAAA,EAumFgpP,a;AAvmFhpPA,IAAAA,EAumFgpP,8C;AAvmFhpPA,IAAAA,EAumFujQ,a;AAvmFvjQA,IAAAA,EAumFujQ,8C;AAvmFvjQA,IAAAA,EAumFw6Q,a;AAvmFx6QA,IAAAA,EAumFw6Q,kF;;;;AA7oF/gR,MAAMsC,gBAAgB,GAAG,UAAzB;AACA,MAAMC,mBAAmB,GAAGC,MAAM,CAACF,gBAAgB,CAACG,MAAlB,CAAlC;AACA,MAAMC,WAAW,GAAG;AAChB,OAAK,OADW;AAEhB,OAAK,MAFW;AAGhB,OAAK,MAHW;AAIhB,OAAK,QAJW;AAKhB,QAAM;AALU,CAApB;;AAOA,SAASC,UAAT,CAAoBC,KAApB,EAA2B;AACvB,SAAQA,KAAK,IAAIL,mBAAmB,CAACM,IAApB,CAAyBD,KAAzB,CAAV,GACHA,KAAK,CAACE,OAAN,CAAcR,gBAAd,EAAgCS,GAAG,IAAIL,WAAW,CAACK,GAAD,CAAlD,CADG,GAEHH,KAFJ;AAGH;;AACD,SAASI,SAAT,CAAmBJ,KAAnB,EAA0B;AACtB,SAAOA,KAAK,KAAKK,SAAV,IAAuBL,KAAK,KAAK,IAAxC;AACH;;AACD,SAASM,QAAT,CAAkBN,KAAlB,EAAyB;AACrB,SAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6BI,SAAS,CAACJ,KAAD,CAA7C;AACH;;AACD,SAASO,SAAT,CAAmBP,KAAnB,EAA0B;AACtB,SAAOA,KAAK,YAAYQ,OAAxB;AACH;;AACD,SAASC,UAAT,CAAoBT,KAApB,EAA2B;AACvB,SAAOA,KAAK,YAAYU,QAAxB;AACH;;AAED,MAAMC,oBAAN,CAA2B;AACvBC,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,SAAKA,OAAL,GAAeA,OAAf;AACA,SAAKC,MAAL,GAAc,IAAd;AACH;;AACDC,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,SAAKH,OAAL,CAAaI,aAAb,CAA2BC,SAA3B,GAAuC,KAAKJ,MAAL,GACnCf,UAAU,CAAC,KAAKoB,WAAN,CADyB,GAEnC,KAAKA,WAFT;AAGH;;AATsB;;AAW3BR,oBAAoB,CAACS,IAArB;AAAA,mBAAiHT,oBAAjH,EAAuGvD,EAAvG,mBAAuJA,EAAE,CAACK,UAA1J;AAAA;;AACAkD,oBAAoB,CAACU,IAArB,kBADuGjE,EACvG;AAAA,QAAqGuD,oBAArG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aADuGvD,EACvG;AAAA;;AACA;AAAA,qDAFuGA,EAEvG,mBAA2FuD,oBAA3F,EAA6H,CAAC;AAClHW,IAAAA,IAAI,EAAEjE,SAD4G;AAElHkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAF4G,GAAD,CAA7H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACK;AAAX,KAAD,CAAP;AAAmC,GAH7E,EAG+F;AAAE0D,IAAAA,WAAW,EAAE,CAAC;AAC/FG,MAAAA,IAAI,EAAEhE;AADyF,KAAD,CAAf;AAE/EwD,IAAAA,MAAM,EAAE,CAAC;AACTQ,MAAAA,IAAI,EAAEhE;AADG,KAAD;AAFuE,GAH/F;AAAA,K,CAQA;;;AACA,MAAMmE,yBAAN,CAAgC;AAC5Bb,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAH2B;;AAKhCD,yBAAyB,CAACL,IAA1B;AAAA,mBAAsHK,yBAAtH,EAhBuGrE,EAgBvG,mBAAiKA,EAAE,CAACe,WAApK;AAAA;;AACAsD,yBAAyB,CAACJ,IAA1B,kBAjBuGjE,EAiBvG;AAAA,QAA0GqE,yBAA1G;AAAA;AAAA;;AACA;AAAA,qDAlBuGrE,EAkBvG,mBAA2FqE,yBAA3F,EAAkI,CAAC;AACvHH,IAAAA,IAAI,EAAEjE,SADiH;AAEvHkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFiH,GAAD,CAAlI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAMwD,2BAAN,CAAkC;AAC9Bf,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAH6B;;AAKlCC,2BAA2B,CAACP,IAA5B;AAAA,mBAAwHO,2BAAxH,EA5BuGvE,EA4BvG,mBAAqKA,EAAE,CAACe,WAAxK;AAAA;;AACAwD,2BAA2B,CAACN,IAA5B,kBA7BuGjE,EA6BvG;AAAA,QAA4GuE,2BAA5G;AAAA;AAAA;;AACA;AAAA,qDA9BuGvE,EA8BvG,mBAA2FuE,2BAA3F,EAAoI,CAAC;AACzHL,IAAAA,IAAI,EAAEjE,SADmH;AAEzHkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFmH,GAAD,CAApI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAMyD,wBAAN,CAA+B;AAC3BhB,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAH0B;;AAK/BE,wBAAwB,CAACR,IAAzB;AAAA,mBAAqHQ,wBAArH,EAxCuGxE,EAwCvG,mBAA+JA,EAAE,CAACe,WAAlK;AAAA;;AACAyD,wBAAwB,CAACP,IAAzB,kBAzCuGjE,EAyCvG;AAAA,QAAyGwE,wBAAzG;AAAA;AAAA;;AACA;AAAA,qDA1CuGxE,EA0CvG,mBAA2FwE,wBAA3F,EAAiI,CAAC;AACtHN,IAAAA,IAAI,EAAEjE,SADgH;AAEtHkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFgH,GAAD,CAAjI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAM0D,6BAAN,CAAoC;AAChCjB,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAH+B;;AAKpCG,6BAA6B,CAACT,IAA9B;AAAA,mBAA0HS,6BAA1H,EApDuGzE,EAoDvG,mBAAyKA,EAAE,CAACe,WAA5K;AAAA;;AACA0D,6BAA6B,CAACR,IAA9B,kBArDuGjE,EAqDvG;AAAA,QAA8GyE,6BAA9G;AAAA;AAAA;;AACA;AAAA,qDAtDuGzE,EAsDvG,mBAA2FyE,6BAA3F,EAAsI,CAAC;AAC3HP,IAAAA,IAAI,EAAEjE,SADqH;AAE3HkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFqH,GAAD,CAAtI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAM2D,yBAAN,CAAgC;AAC5BlB,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAH2B;;AAKhCI,yBAAyB,CAACV,IAA1B;AAAA,mBAAsHU,yBAAtH,EAhEuG1E,EAgEvG,mBAAiKA,EAAE,CAACe,WAApK;AAAA;;AACA2D,yBAAyB,CAACT,IAA1B,kBAjEuGjE,EAiEvG;AAAA,QAA0G0E,yBAA1G;AAAA;AAAA;;AACA;AAAA,qDAlEuG1E,EAkEvG,mBAA2F0E,yBAA3F,EAAkI,CAAC;AACvHR,IAAAA,IAAI,EAAEjE,SADiH;AAEvHkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFiH,GAAD,CAAlI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAM4D,yBAAN,CAAgC;AAC5BnB,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAH2B;;AAKhCK,yBAAyB,CAACX,IAA1B;AAAA,mBAAsHW,yBAAtH,EA5EuG3E,EA4EvG,mBAAiKA,EAAE,CAACe,WAApK;AAAA;;AACA4D,yBAAyB,CAACV,IAA1B,kBA7EuGjE,EA6EvG;AAAA,QAA0G2E,yBAA1G;AAAA;AAAA;;AACA;AAAA,qDA9EuG3E,EA8EvG,mBAA2F2E,yBAA3F,EAAkI,CAAC;AACvHT,IAAAA,IAAI,EAAEjE,SADiH;AAEvHkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFiH,GAAD,CAAlI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAM6D,2BAAN,CAAkC;AAC9BpB,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAH6B;;AAKlCM,2BAA2B,CAACZ,IAA5B;AAAA,mBAAwHY,2BAAxH,EAxFuG5E,EAwFvG,mBAAqKA,EAAE,CAACe,WAAxK;AAAA;;AACA6D,2BAA2B,CAACX,IAA5B,kBAzFuGjE,EAyFvG;AAAA,QAA4G4E,2BAA5G;AAAA;AAAA;;AACA;AAAA,qDA1FuG5E,EA0FvG,mBAA2F4E,2BAA3F,EAAoI,CAAC;AACzHV,IAAAA,IAAI,EAAEjE,SADmH;AAEzHkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFmH,GAAD,CAApI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAM8D,+BAAN,CAAsC;AAClCrB,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAHiC;;AAKtCO,+BAA+B,CAACb,IAAhC;AAAA,mBAA4Ha,+BAA5H,EApGuG7E,EAoGvG,mBAA6KA,EAAE,CAACe,WAAhL;AAAA;;AACA8D,+BAA+B,CAACZ,IAAhC,kBArGuGjE,EAqGvG;AAAA,QAAgH6E,+BAAhH;AAAA;AAAA;;AACA;AAAA,qDAtGuG7E,EAsGvG,mBAA2F6E,+BAA3F,EAAwI,CAAC;AAC7HX,IAAAA,IAAI,EAAEjE,SADuH;AAE7HkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFuH,GAAD,CAAxI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAM+D,8BAAN,CAAqC;AACjCtB,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAHgC;;AAKrCQ,8BAA8B,CAACd,IAA/B;AAAA,mBAA2Hc,8BAA3H,EAhHuG9E,EAgHvG,mBAA2KA,EAAE,CAACe,WAA9K;AAAA;;AACA+D,8BAA8B,CAACb,IAA/B,kBAjHuGjE,EAiHvG;AAAA,QAA+G8E,8BAA/G;AAAA;AAAA;;AACA;AAAA,qDAlHuG9E,EAkHvG,mBAA2F8E,8BAA3F,EAAuI,CAAC;AAC5HZ,IAAAA,IAAI,EAAEjE,SADsH;AAE5HkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFsH,GAAD,CAAvI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAMgE,sBAAN,CAA6B;AACzBvB,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAHwB;;AAK7BS,sBAAsB,CAACf,IAAvB;AAAA,mBAAmHe,sBAAnH,EA5HuG/E,EA4HvG,mBAA2JA,EAAE,CAACe,WAA9J;AAAA;;AACAgE,sBAAsB,CAACd,IAAvB,kBA7HuGjE,EA6HvG;AAAA,QAAuG+E,sBAAvG;AAAA;AAAA;;AACA;AAAA,qDA9HuG/E,EA8HvG,mBAA2F+E,sBAA3F,EAA+H,CAAC;AACpHb,IAAAA,IAAI,EAAEjE,SAD8G;AAEpHkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAF8G,GAAD,CAA/H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA,K,CAIA;;;AACA,MAAMiE,iCAAN,CAAwC;AACpCxB,EAAAA,WAAW,CAACc,QAAD,EAAW;AAClB,SAAKA,QAAL,GAAgBA,QAAhB;AACH;;AAHmC;;AAKxCU,iCAAiC,CAAChB,IAAlC;AAAA,mBAA8HgB,iCAA9H,EAxIuGhF,EAwIvG,mBAAiLA,EAAE,CAACe,WAApL;AAAA;;AACAiE,iCAAiC,CAACf,IAAlC,kBAzIuGjE,EAyIvG;AAAA,QAAkHgF,iCAAlH;AAAA;AAAA;;AACA;AAAA,qDA1IuGhF,EA0IvG,mBAA2FgF,iCAA3F,EAA0I,CAAC;AAC/Hd,IAAAA,IAAI,EAAEjE,SADyH;AAE/HkE,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE;AAAZ,KAAD;AAFyH,GAAD,CAA1I,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE,EAAE,CAACe;AAAX,KAAD,CAAP;AAAoC,GAH9E;AAAA;;AAKA,SAASkE,KAAT,GAAiB;AACb;AACA,SAAO,eAAenC,OAAf,CAAuB,MAAvB,EAA+B,MAAM;AACxC;AACA,UAAMoC,GAAG,GAAGC,IAAI,CAACC,MAAL,KAAgB,EAAhB,GAAqB,CAAjC;AACA,WAAOF,GAAG,CAACG,QAAJ,CAAa,EAAb,CAAP;AACH,GAJM,CAAP;AAKH;;AAED,MAAMC,UAAU,GAAG;AACf,YAAU,GADK;AAEf,YAAU,GAFK;AAGf,YAAU,GAHK;AAIf,YAAU,GAJK;AAKf,YAAU,GALK;AAMf,YAAU,GANK;AAOf,YAAU,GAPK;AAQf,YAAU,GARK;AASf,YAAU,GATK;AAUf,YAAU,GAVK;AAWf,YAAU,GAXK;AAYf,YAAU,GAZK;AAaf,YAAU,GAbK;AAcf,YAAU,GAdK;AAef,YAAU,GAfK;AAgBf,YAAU,GAhBK;AAiBf,YAAU,GAjBK;AAkBf,YAAU,GAlBK;AAmBf,YAAU,GAnBK;AAoBf,YAAU,GApBK;AAqBf,YAAU,GArBK;AAsBf,YAAU,GAtBK;AAuBf,YAAU,GAvBK;AAwBf,YAAU,GAxBK;AAyBf,YAAU,GAzBK;AA0Bf,YAAU,GA1BK;AA2Bf,YAAU,GA3BK;AA4Bf,YAAU,GA5BK;AA6Bf,YAAU,GA7BK;AA8Bf,YAAU,GA9BK;AA+Bf,YAAU,GA/BK;AAgCf,YAAU,GAhCK;AAiCf,YAAU,GAjCK;AAkCf,YAAU,IAlCK;AAmCf,YAAU,IAnCK;AAoCf,YAAU,IApCK;AAqCf,YAAU,IArCK;AAsCf,YAAU,IAtCK;AAuCf,YAAU,IAvCK;AAwCf,YAAU,IAxCK;AAyCf,YAAU,IAzCK;AA0Cf,YAAU,IA1CK;AA2Cf,YAAU,GA3CK;AA4Cf,YAAU,GA5CK;AA6Cf,YAAU,GA7CK;AA8Cf,YAAU,GA9CK;AA+Cf,YAAU,GA/CK;AAgDf,YAAU,GAhDK;AAiDf,YAAU,GAjDK;AAkDf,YAAU,GAlDK;AAmDf,YAAU,GAnDK;AAoDf,YAAU,GApDK;AAqDf,YAAU,GArDK;AAsDf,YAAU,GAtDK;AAuDf,YAAU,GAvDK;AAwDf,YAAU,GAxDK;AAyDf,YAAU,GAzDK;AA0Df,YAAU,GA1DK;AA2Df,YAAU,GA3DK;AA4Df,YAAU,GA5DK;AA6Df,YAAU,GA7DK;AA8Df,YAAU,GA9DK;AA+Df,YAAU,GA/DK;AAgEf,YAAU,GAhEK;AAiEf,YAAU,GAjEK;AAkEf,YAAU,GAlEK;AAmEf,YAAU,GAnEK;AAoEf,YAAU,GApEK;AAqEf,YAAU,GArEK;AAsEf,YAAU,GAtEK;AAuEf,YAAU,GAvEK;AAwEf,YAAU,GAxEK;AAyEf,YAAU,GAzEK;AA0Ef,YAAU,GA1EK;AA2Ef,YAAU,IA3EK;AA4Ef,YAAU,IA5EK;AA6Ef,YAAU,IA7EK;AA8Ef,YAAU,IA9EK;AA+Ef,YAAU,GA/EK;AAgFf,YAAU,GAhFK;AAiFf,YAAU,GAjFK;AAkFf,YAAU,GAlFK;AAmFf,YAAU,GAnFK;AAoFf,YAAU,GApFK;AAqFf,YAAU,GArFK;AAsFf,YAAU,GAtFK;AAuFf,YAAU,GAvFK;AAwFf,YAAU,GAxFK;AAyFf,YAAU,GAzFK;AA0Ff,YAAU,GA1FK;AA2Ff,YAAU,GA3FK;AA4Ff,YAAU,GA5FK;AA6Ff,YAAU,GA7FK;AA8Ff,YAAU,GA9FK;AA+Ff,YAAU,GA/FK;AAgGf,YAAU,GAhGK;AAiGf,YAAU,GAjGK;AAkGf,YAAU,GAlGK;AAmGf,YAAU,GAnGK;AAoGf,YAAU,GApGK;AAqGf,YAAU,GArGK;AAsGf,YAAU,GAtGK;AAuGf,YAAU,GAvGK;AAwGf,YAAU,GAxGK;AAyGf,YAAU,GAzGK;AA0Gf,YAAU,GA1GK;AA2Gf,YAAU,GA3GK;AA4Gf,YAAU,GA5GK;AA6Gf,YAAU,GA7GK;AA8Gf,YAAU,GA9GK;AA+Gf,YAAU,GA/GK;AAgHf,YAAU,GAhHK;AAiHf,YAAU,GAjHK;AAkHf,YAAU,GAlHK;AAmHf,YAAU,GAnHK;AAoHf,YAAU,GApHK;AAqHf,YAAU,GArHK;AAsHf,YAAU,GAtHK;AAuHf,YAAU,GAvHK;AAwHf,YAAU,GAxHK;AAyHf,YAAU,GAzHK;AA0Hf,YAAU,GA1HK;AA2Hf,YAAU,GA3HK;AA4Hf,YAAU,GA5HK;AA6Hf,YAAU,GA7HK;AA8Hf,YAAU,GA9HK;AA+Hf,YAAU,GA/HK;AAgIf,YAAU,GAhIK;AAiIf,YAAU,GAjIK;AAkIf,YAAU,GAlIK;AAmIf,YAAU,GAnIK;AAoIf,YAAU,GApIK;AAqIf,YAAU,GArIK;AAsIf,YAAU,GAtIK;AAuIf,YAAU,GAvIK;AAwIf,YAAU,GAxIK;AAyIf,YAAU,GAzIK;AA0If,YAAU,GA1IK;AA2If,YAAU,GA3IK;AA4If,YAAU,GA5IK;AA6If,YAAU,GA7IK;AA8If,YAAU,GA9IK;AA+If,YAAU,GA/IK;AAgJf,YAAU,GAhJK;AAiJf,YAAU,GAjJK;AAkJf,YAAU,GAlJK;AAmJf,YAAU,GAnJK;AAoJf,YAAU,GApJK;AAqJf,YAAU,GArJK;AAsJf,YAAU,GAtJK;AAuJf,YAAU,GAvJK;AAwJf,YAAU,GAxJK;AAyJf,YAAU,GAzJK;AA0Jf,YAAU,GA1JK;AA2Jf,YAAU,GA3JK;AA4Jf,YAAU,GA5JK;AA6Jf,YAAU,GA7JK;AA8Jf,YAAU,GA9JK;AA+Jf,YAAU,GA/JK;AAgKf,YAAU,GAhKK;AAiKf,YAAU,GAjKK;AAkKf,YAAU,GAlKK;AAmKf,YAAU,GAnKK;AAoKf,YAAU,GApKK;AAqKf,YAAU,GArKK;AAsKf,YAAU,GAtKK;AAuKf,YAAU,GAvKK;AAwKf,YAAU,GAxKK;AAyKf,YAAU,GAzKK;AA0Kf,YAAU,GA1KK;AA2Kf,YAAU,GA3KK;AA4Kf,YAAU,GA5KK;AA6Kf,YAAU,GA7KK;AA8Kf,YAAU,GA9KK;AA+Kf,YAAU,GA/KK;AAgLf,YAAU,GAhLK;AAiLf,YAAU,GAjLK;AAkLf,YAAU,GAlLK;AAmLf,YAAU,GAnLK;AAoLf,YAAU,GApLK;AAqLf,YAAU,GArLK;AAsLf,YAAU,GAtLK;AAuLf,YAAU,GAvLK;AAwLf,YAAU,GAxLK;AAyLf,YAAU,GAzLK;AA0Lf,YAAU,GA1LK;AA2Lf,YAAU,GA3LK;AA4Lf,YAAU,GA5LK;AA6Lf,YAAU,GA7LK;AA8Lf,YAAU,GA9LK;AA+Lf,YAAU,GA/LK;AAgMf,YAAU,GAhMK;AAiMf,YAAU,IAjMK;AAkMf,YAAU,IAlMK;AAmMf,YAAU,GAnMK;AAoMf,YAAU,GApMK;AAqMf,YAAU,GArMK;AAsMf,YAAU,GAtMK;AAuMf,YAAU,GAvMK;AAwMf,YAAU,GAxMK;AAyMf,YAAU,GAzMK;AA0Mf,YAAU,GA1MK;AA2Mf,YAAU,GA3MK;AA4Mf,YAAU,GA5MK;AA6Mf,YAAU,GA7MK;AA8Mf,YAAU,GA9MK;AA+Mf,YAAU,GA/MK;AAgNf,YAAU,GAhNK;AAiNf,YAAU,GAjNK;AAkNf,YAAU,GAlNK;AAmNf,YAAU,GAnNK;AAoNf,YAAU,GApNK;AAqNf,YAAU,GArNK;AAsNf,YAAU,GAtNK;AAuNf,YAAU,GAvNK;AAwNf,YAAU,GAxNK;AAyNf,YAAU,IAzNK;AA0Nf,YAAU,IA1NK;AA2Nf,YAAU,GA3NK;AA4Nf,YAAU,GA5NK;AA6Nf,YAAU,GA7NK;AA8Nf,YAAU,GA9NK;AA+Nf,YAAU,GA/NK;AAgOf,YAAU,GAhOK;AAiOf,YAAU,GAjOK;AAkOf,YAAU,GAlOK;AAmOf,YAAU,GAnOK;AAoOf,YAAU,GApOK;AAqOf,YAAU,GArOK;AAsOf,YAAU,GAtOK;AAuOf,YAAU,GAvOK;AAwOf,YAAU,GAxOK;AAyOf,YAAU,GAzOK;AA0Of,YAAU,GA1OK;AA2Of,YAAU,GA3OK;AA4Of,YAAU,GA5OK;AA6Of,YAAU,GA7OK;AA8Of,YAAU,GA9OK;AA+Of,YAAU,GA/OK;AAgPf,YAAU,GAhPK;AAiPf,YAAU,GAjPK;AAkPf,YAAU,GAlPK;AAmPf,YAAU,GAnPK;AAoPf,YAAU,GApPK;AAqPf,YAAU,GArPK;AAsPf,YAAU,GAtPK;AAuPf,YAAU,GAvPK;AAwPf,YAAU,GAxPK;AAyPf,YAAU,GAzPK;AA0Pf,YAAU,GA1PK;AA2Pf,YAAU,GA3PK;AA4Pf,YAAU,GA5PK;AA6Pf,YAAU,GA7PK;AA8Pf,YAAU,GA9PK;AA+Pf,YAAU,GA/PK;AAgQf,YAAU,GAhQK;AAiQf,YAAU,GAjQK;AAkQf,YAAU,GAlQK;AAmQf,YAAU,GAnQK;AAoQf,YAAU,GApQK;AAqQf,YAAU,IArQK;AAsQf,YAAU,IAtQK;AAuQf,YAAU,IAvQK;AAwQf,YAAU,GAxQK;AAyQf,YAAU,GAzQK;AA0Qf,YAAU,GA1QK;AA2Qf,YAAU,GA3QK;AA4Qf,YAAU,GA5QK;AA6Qf,YAAU,GA7QK;AA8Qf,YAAU,GA9QK;AA+Qf,YAAU,GA/QK;AAgRf,YAAU,GAhRK;AAiRf,YAAU,GAjRK;AAkRf,YAAU,GAlRK;AAmRf,YAAU,GAnRK;AAoRf,YAAU,GApRK;AAqRf,YAAU,GArRK;AAsRf,YAAU,GAtRK;AAuRf,YAAU,GAvRK;AAwRf,YAAU,GAxRK;AAyRf,YAAU,GAzRK;AA0Rf,YAAU,GA1RK;AA2Rf,YAAU,GA3RK;AA4Rf,YAAU,GA5RK;AA6Rf,YAAU,GA7RK;AA8Rf,YAAU,GA9RK;AA+Rf,YAAU,GA/RK;AAgSf,YAAU,GAhSK;AAiSf,YAAU,GAjSK;AAkSf,YAAU,GAlSK;AAmSf,YAAU,GAnSK;AAoSf,YAAU,GApSK;AAqSf,YAAU,GArSK;AAsSf,YAAU,GAtSK;AAuSf,YAAU,GAvSK;AAwSf,YAAU,GAxSK;AAySf,YAAU,GAzSK;AA0Sf,YAAU,GA1SK;AA2Sf,YAAU,GA3SK;AA4Sf,YAAU,GA5SK;AA6Sf,YAAU,GA7SK;AA8Sf,YAAU,GA9SK;AA+Sf,YAAU,GA/SK;AAgTf,YAAU,GAhTK;AAiTf,YAAU,GAjTK;AAkTf,YAAU,GAlTK;AAmTf,YAAU,GAnTK;AAoTf,YAAU,GApTK;AAqTf,YAAU,GArTK;AAsTf,YAAU,GAtTK;AAuTf,YAAU,GAvTK;AAwTf,YAAU,GAxTK;AAyTf,YAAU,GAzTK;AA0Tf,YAAU,GA1TK;AA2Tf,YAAU,GA3TK;AA4Tf,YAAU,GA5TK;AA6Tf,YAAU,GA7TK;AA8Tf,YAAU,GA9TK;AA+Tf,YAAU,GA/TK;AAgUf,YAAU,GAhUK;AAiUf,YAAU,GAjUK;AAkUf,YAAU,GAlUK;AAmUf,YAAU,GAnUK;AAoUf,YAAU,IApUK;AAqUf,YAAU,GArUK;AAsUf,YAAU,GAtUK;AAuUf,YAAU,GAvUK;AAwUf,YAAU,GAxUK;AAyUf,YAAU,GAzUK;AA0Uf,YAAU,GA1UK;AA2Uf,YAAU,GA3UK;AA4Uf,YAAU,GA5UK;AA6Uf,YAAU,GA7UK;AA8Uf,YAAU,GA9UK;AA+Uf,YAAU,GA/UK;AAgVf,YAAU,GAhVK;AAiVf,YAAU,GAjVK;AAkVf,YAAU,GAlVK;AAmVf,YAAU,GAnVK;AAoVf,YAAU,GApVK;AAqVf,YAAU,GArVK;AAsVf,YAAU,GAtVK;AAuVf,YAAU,GAvVK;AAwVf,YAAU,GAxVK;AAyVf,YAAU,GAzVK;AA0Vf,YAAU,GA1VK;AA2Vf,YAAU,GA3VK;AA4Vf,YAAU,GA5VK;AA6Vf,YAAU,GA7VK;AA8Vf,YAAU,GA9VK;AA+Vf,YAAU,GA/VK;AAgWf,YAAU,GAhWK;AAiWf,YAAU,GAjWK;AAkWf,YAAU,GAlWK;AAmWf,YAAU,GAnWK;AAoWf,YAAU,GApWK;AAqWf,YAAU,GArWK;AAsWf,YAAU,GAtWK;AAuWf,YAAU,GAvWK;AAwWf,YAAU,GAxWK;AAyWf,YAAU,GAzWK;AA0Wf,YAAU,GA1WK;AA2Wf,YAAU,GA3WK;AA4Wf,YAAU,GA5WK;AA6Wf,YAAU,IA7WK;AA8Wf,YAAU,GA9WK;AA+Wf,YAAU,GA/WK;AAgXf,YAAU,GAhXK;AAiXf,YAAU,GAjXK;AAkXf,YAAU,GAlXK;AAmXf,YAAU,GAnXK;AAoXf,YAAU,GApXK;AAqXf,YAAU,GArXK;AAsXf,YAAU,GAtXK;AAuXf,YAAU,GAvXK;AAwXf,YAAU,GAxXK;AAyXf,YAAU,GAzXK;AA0Xf,YAAU,GA1XK;AA2Xf,YAAU,GA3XK;AA4Xf,YAAU,GA5XK;AA6Xf,YAAU,GA7XK;AA8Xf,YAAU,GA9XK;AA+Xf,YAAU,GA/XK;AAgYf,YAAU,GAhYK;AAiYf,YAAU,GAjYK;AAkYf,YAAU,GAlYK;AAmYf,YAAU,GAnYK;AAoYf,YAAU,GApYK;AAqYf,YAAU,GArYK;AAsYf,YAAU,GAtYK;AAuYf,YAAU,GAvYK;AAwYf,YAAU,GAxYK;AAyYf,YAAU,GAzYK;AA0Yf,YAAU,GA1YK;AA2Yf,YAAU,GA3YK;AA4Yf,YAAU,GA5YK;AA6Yf,YAAU,GA7YK;AA8Yf,YAAU,GA9YK;AA+Yf,YAAU,GA/YK;AAgZf,YAAU,GAhZK;AAiZf,YAAU,GAjZK;AAkZf,YAAU,GAlZK;AAmZf,YAAU,GAnZK;AAoZf,YAAU,GApZK;AAqZf,YAAU,GArZK;AAsZf,YAAU,GAtZK;AAuZf,YAAU,GAvZK;AAwZf,YAAU,GAxZK;AAyZf,YAAU,GAzZK;AA0Zf,YAAU,GA1ZK;AA2Zf,YAAU,GA3ZK;AA4Zf,YAAU,GA5ZK;AA6Zf,YAAU,GA7ZK;AA8Zf,YAAU,GA9ZK;AA+Zf,YAAU,GA/ZK;AAgaf,YAAU,GAhaK;AAiaf,YAAU,GAjaK;AAkaf,YAAU,GAlaK;AAmaf,YAAU,GAnaK;AAoaf,YAAU,GApaK;AAqaf,YAAU,GAraK;AAsaf,YAAU,GAtaK;AAuaf,YAAU,GAvaK;AAwaf,YAAU,GAxaK;AAyaf,YAAU,GAzaK;AA0af,YAAU,GA1aK;AA2af,YAAU,GA3aK;AA4af,YAAU,GA5aK;AA6af,YAAU,GA7aK;AA8af,YAAU,GA9aK;AA+af,YAAU,GA/aK;AAgbf,YAAU,GAhbK;AAibf,YAAU,GAjbK;AAkbf,YAAU,GAlbK;AAmbf,YAAU,GAnbK;AAobf,YAAU,GApbK;AAqbf,YAAU,GArbK;AAsbf,YAAU,GAtbK;AAubf,YAAU,GAvbK;AAwbf,YAAU,IAxbK;AAybf,YAAU,IAzbK;AA0bf,YAAU,IA1bK;AA2bf,YAAU,IA3bK;AA4bf,YAAU,IA5bK;AA6bf,YAAU,IA7bK;AA8bf,YAAU,IA9bK;AA+bf,YAAU,IA/bK;AAgcf,YAAU,IAhcK;AAicf,YAAU,GAjcK;AAkcf,YAAU,GAlcK;AAmcf,YAAU,GAncK;AAocf,YAAU,GApcK;AAqcf,YAAU,GArcK;AAscf,YAAU,GAtcK;AAucf,YAAU,GAvcK;AAwcf,YAAU,GAxcK;AAycf,YAAU,GAzcK;AA0cf,YAAU,GA1cK;AA2cf,YAAU,GA3cK;AA4cf,YAAU,GA5cK;AA6cf,YAAU,GA7cK;AA8cf,YAAU,GA9cK;AA+cf,YAAU,GA/cK;AAgdf,YAAU,GAhdK;AAidf,YAAU,GAjdK;AAkdf,YAAU,GAldK;AAmdf,YAAU,GAndK;AAodf,YAAU,GApdK;AAqdf,YAAU,GArdK;AAsdf,YAAU,GAtdK;AAudf,YAAU,GAvdK;AAwdf,YAAU,GAxdK;AAydf,YAAU,GAzdK;AA0df,YAAU,GA1dK;AA2df,YAAU,GA3dK;AA4df,YAAU,GA5dK;AA6df,YAAU,GA7dK;AA8df,YAAU,GA9dK;AA+df,YAAU,GA/dK;AAgef,YAAU,GAheK;AAief,YAAU,GAjeK;AAkef,YAAU,IAleK;AAmef,YAAU,IAneK;AAoef,YAAU,GApeK;AAqef,YAAU,GAreK;AAsef,YAAU,GAteK;AAuef,YAAU,GAveK;AAwef,YAAU,GAxeK;AAyef,YAAU,GAzeK;AA0ef,YAAU,GA1eK;AA2ef,YAAU,GA3eK;AA4ef,YAAU,GA5eK;AA6ef,YAAU,GA7eK;AA8ef,YAAU,GA9eK;AA+ef,YAAU,GA/eK;AAgff,YAAU,GAhfK;AAiff,YAAU,GAjfK;AAkff,YAAU,GAlfK;AAmff,YAAU,GAnfK;AAoff,YAAU,GApfK;AAqff,YAAU,GArfK;AAsff,YAAU,GAtfK;AAuff,YAAU,GAvfK;AAwff,YAAU,GAxfK;AAyff,YAAU,GAzfK;AA0ff,YAAU,GA1fK;AA2ff,YAAU,GA3fK;AA4ff,YAAU,GA5fK;AA6ff,YAAU,GA7fK;AA8ff,YAAU,GA9fK;AA+ff,YAAU,GA/fK;AAggBf,YAAU,GAhgBK;AAigBf,YAAU,GAjgBK;AAkgBf,YAAU,GAlgBK;AAmgBf,YAAU,GAngBK;AAogBf,YAAU,GApgBK;AAqgBf,YAAU,GArgBK;AAsgBf,YAAU,GAtgBK;AAugBf,YAAU,GAvgBK;AAwgBf,YAAU,GAxgBK;AAygBf,YAAU,GAzgBK;AA0gBf,YAAU,GA1gBK;AA2gBf,YAAU,GA3gBK;AA4gBf,YAAU,GA5gBK;AA6gBf,YAAU,GA7gBK;AA8gBf,YAAU,GA9gBK;AA+gBf,YAAU,GA/gBK;AAghBf,YAAU,GAhhBK;AAihBf,YAAU,GAjhBK;AAkhBf,YAAU,GAlhBK;AAmhBf,YAAU,GAnhBK;AAohBf,YAAU,GAphBK;AAqhBf,YAAU,GArhBK;AAshBf,YAAU,GAthBK;AAuhBf,YAAU,GAvhBK;AAwhBf,YAAU,GAxhBK;AAyhBf,YAAU,GAzhBK;AA0hBf,YAAU,GA1hBK;AA2hBf,YAAU,GA3hBK;AA4hBf,YAAU,GA5hBK;AA6hBf,YAAU,GA7hBK;AA8hBf,YAAU,GA9hBK;AA+hBf,YAAU,GA/hBK;AAgiBf,YAAU,GAhiBK;AAiiBf,YAAU,GAjiBK;AAkiBf,YAAU,GAliBK;AAmiBf,YAAU,IAniBK;AAoiBf,YAAU,GApiBK;AAqiBf,YAAU,GAriBK;AAsiBf,YAAU,GAtiBK;AAuiBf,YAAU,GAviBK;AAwiBf,YAAU,GAxiBK;AAyiBf,YAAU,GAziBK;AA0iBf,YAAU,GA1iBK;AA2iBf,YAAU,GA3iBK;AA4iBf,YAAU,GA5iBK;AA6iBf,YAAU,GA7iBK;AA8iBf,YAAU,GA9iBK;AA+iBf,YAAU,GA/iBK;AAgjBf,YAAU,GAhjBK;AAijBf,YAAU,GAjjBK;AAkjBf,YAAU,GAljBK;AAmjBf,YAAU,GAnjBK;AAojBf,YAAU,GApjBK;AAqjBf,YAAU,GArjBK;AAsjBf,YAAU,GAtjBK;AAujBf,YAAU,GAvjBK;AAwjBf,YAAU,GAxjBK;AAyjBf,YAAU,GAzjBK;AA0jBf,YAAU,GA1jBK;AA2jBf,YAAU,GA3jBK;AA4jBf,YAAU,GA5jBK;AA6jBf,YAAU,GA7jBK;AA8jBf,YAAU,GA9jBK;AA+jBf,YAAU,GA/jBK;AAgkBf,YAAU,GAhkBK;AAikBf,YAAU,GAjkBK;AAkkBf,YAAU,GAlkBK;AAmkBf,YAAU,GAnkBK;AAokBf,YAAU,GApkBK;AAqkBf,YAAU,GArkBK;AAskBf,YAAU,GAtkBK;AAukBf,YAAU,GAvkBK;AAwkBf,YAAU,GAxkBK;AAykBf,YAAU,GAzkBK;AA0kBf,YAAU,GA1kBK;AA2kBf,YAAU,GA3kBK;AA4kBf,YAAU,GA5kBK;AA6kBf,YAAU,GA7kBK;AA8kBf,YAAU,GA9kBK;AA+kBf,YAAU,GA/kBK;AAglBf,YAAU,GAhlBK;AAilBf,YAAU,GAjlBK;AAklBf,YAAU,GAllBK;AAmlBf,YAAU,GAnlBK;AAolBf,YAAU,GAplBK;AAqlBf,YAAU,GArlBK;AAslBf,YAAU,GAtlBK;AAulBf,YAAU,GAvlBK;AAwlBf,YAAU,GAxlBK;AAylBf,YAAU,GAzlBK;AA0lBf,YAAU,GA1lBK;AA2lBf,YAAU,IA3lBK;AA4lBf,YAAU,GA5lBK;AA6lBf,YAAU,GA7lBK;AA8lBf,YAAU,GA9lBK;AA+lBf,YAAU,GA/lBK;AAgmBf,YAAU,GAhmBK;AAimBf,YAAU,GAjmBK;AAkmBf,YAAU,GAlmBK;AAmmBf,YAAU,GAnmBK;AAomBf,YAAU,GApmBK;AAqmBf,YAAU,GArmBK;AAsmBf,YAAU,GAtmBK;AAumBf,YAAU,GAvmBK;AAwmBf,YAAU,GAxmBK;AAymBf,YAAU,GAzmBK;AA0mBf,YAAU,GA1mBK;AA2mBf,YAAU,GA3mBK;AA4mBf,YAAU,GA5mBK;AA6mBf,YAAU,GA7mBK;AA8mBf,YAAU,GA9mBK;AA+mBf,YAAU,GA/mBK;AAgnBf,YAAU,GAhnBK;AAinBf,YAAU,GAjnBK;AAknBf,YAAU,GAlnBK;AAmnBf,YAAU,IAnnBK;AAonBf,YAAU,GApnBK;AAqnBf,YAAU,GArnBK;AAsnBf,YAAU,GAtnBK;AAunBf,YAAU,GAvnBK;AAwnBf,YAAU,GAxnBK;AAynBf,YAAU,GAznBK;AA0nBf,YAAU,GA1nBK;AA2nBf,YAAU,GA3nBK;AA4nBf,YAAU,GA5nBK;AA6nBf,YAAU,GA7nBK;AA8nBf,YAAU,GA9nBK;AA+nBf,YAAU,GA/nBK;AAgoBf,YAAU,GAhoBK;AAioBf,YAAU,GAjoBK;AAkoBf,YAAU,GAloBK;AAmoBf,YAAU,GAnoBK;AAooBf,YAAU,GApoBK;AAqoBf,YAAU,GAroBK;AAsoBf,YAAU,GAtoBK;AAuoBf,YAAU,GAvoBK;AAwoBf,YAAU,GAxoBK;AAyoBf,YAAU,GAzoBK;AA0oBf,YAAU,GA1oBK;AA2oBf,YAAU,GA3oBK;AA4oBf,YAAU,GA5oBK;AA6oBf,YAAU,GA7oBK;AA8oBf,YAAU,GA9oBK;AA+oBf,YAAU,GA/oBK;AAgpBf,YAAU,GAhpBK;AAipBf,YAAU,GAjpBK;AAkpBf,YAAU,GAlpBK;AAmpBf,YAAU,GAnpBK;AAopBf,YAAU,GAppBK;AAqpBf,YAAU,GArpBK;AAspBf,YAAU,GAtpBK;AAupBf,YAAU,GAvpBK;AAwpBf,YAAU,GAxpBK;AAypBf,YAAU,GAzpBK;AA0pBf,YAAU,GA1pBK;AA2pBf,YAAU,GA3pBK;AA4pBf,YAAU,GA5pBK;AA6pBf,YAAU,GA7pBK;AA8pBf,YAAU,IA9pBK;AA+pBf,YAAU,IA/pBK;AAgqBf,YAAU,IAhqBK;AAiqBf,YAAU,GAjqBK;AAkqBf,YAAU,GAlqBK;AAmqBf,YAAU,GAnqBK;AAoqBf,YAAU,GApqBK;AAqqBf,YAAU,GArqBK;AAsqBf,YAAU,GAtqBK;AAuqBf,YAAU,GAvqBK;AAwqBf,YAAU,GAxqBK;AAyqBf,YAAU,GAzqBK;AA0qBf,YAAU,GA1qBK;AA2qBf,YAAU,GA3qBK;AA4qBf,YAAU,GA5qBK;AA6qBf,YAAU,GA7qBK;AA8qBf,YAAU,GA9qBK;AA+qBf,YAAU,GA/qBK;AAgrBf,YAAU,GAhrBK;AAirBf,YAAU,GAjrBK;AAkrBf,YAAU,GAlrBK;AAmrBf,YAAU,GAnrBK;AAorBf,YAAU,GAprBK;AAqrBf,YAAU,GArrBK;AAsrBf,YAAU,GAtrBK;AAurBf,YAAU,GAvrBK;AAwrBf,YAAU,GAxrBK;AAyrBf,YAAU,GAzrBK;AA0rBf,YAAU,GA1rBK;AA2rBf,YAAU,GA3rBK;AA4rBf,YAAU,GA5rBK;AA6rBf,YAAU,GA7rBK;AA8rBf,YAAU,GA9rBK;AA+rBf,YAAU,GA/rBK;AAgsBf,YAAU,GAhsBK;AAisBf,YAAU,GAjsBK;AAksBf,YAAU,GAlsBK;AAmsBf,YAAU,GAnsBK;AAosBf,YAAU,GApsBK;AAqsBf,YAAU,GArsBK;AAssBf,YAAU,GAtsBK;AAusBf,YAAU,GAvsBK;AAwsBf,YAAU,GAxsBK;AAysBf,YAAU,GAzsBK;AA0sBf,YAAU,GA1sBK;AA2sBf,YAAU,GA3sBK;AA4sBf,YAAU,GA5sBK;AA6sBf,YAAU,GA7sBK;AA8sBf,YAAU,GA9sBK;AA+sBf,YAAU,GA/sBK;AAgtBf,YAAU,GAhtBK;AAitBf,YAAU,GAjtBK;AAktBf,YAAU,GAltBK;AAmtBf,YAAU,GAntBK;AAotBf,YAAU,GAptBK;AAqtBf,YAAU,GArtBK;AAstBf,YAAU,GAttBK;AAutBf,YAAU,GAvtBK;AAwtBf,YAAU,GAxtBK;AAytBf,YAAU,GAztBK;AA0tBf,YAAU,GA1tBK;AA2tBf,YAAU,GA3tBK;AA4tBf,YAAU,GA5tBK;AA6tBf,YAAU,GA7tBK;AA8tBf,YAAU,GA9tBK;AA+tBf,YAAU,IA/tBK;AAguBf,YAAU,GAhuBK;AAiuBf,YAAU,GAjuBK;AAkuBf,YAAU,GAluBK;AAmuBf,YAAU,GAnuBK;AAouBf,YAAU,GApuBK;AAquBf,YAAU,GAruBK;AAsuBf,YAAU,GAtuBK;AAuuBf,YAAU,GAvuBK;AAwuBf,YAAU,GAxuBK;AAyuBf,YAAU,GAzuBK;AA0uBf,YAAU,GA1uBK;AA2uBf,YAAU,GA3uBK;AA4uBf,YAAU,GA5uBK;AA6uBf,YAAU,GA7uBK;AA8uBf,YAAU,GA9uBK;AA+uBf,YAAU,GA/uBK;AAgvBf,YAAU,GAhvBK;AAivBf,YAAU,GAjvBK;AAkvBf,YAAU,GAlvBK;AAmvBf,YAAU,GAnvBK;AAovBf,YAAU,GApvBK;AAqvBf,YAAU,GArvBK;AAsvBf,YAAU,GAtvBK;AAuvBf,YAAU,GAvvBK;AAwvBf,YAAU,GAxvBK;AAyvBf,YAAU,GAzvBK;AA0vBf,YAAU,GA1vBK;AA2vBf,YAAU,GA3vBK;AA4vBf,YAAU,GA5vBK;AA6vBf,YAAU,GA7vBK;AA8vBf,YAAU,GA9vBK;AA+vBf,YAAU,GA/vBK;AAgwBf,YAAU,GAhwBK;AAiwBf,YAAU,GAjwBK;AAkwBf,YAAU,GAlwBK;AAmwBf,YAAU,GAnwBK;AAowBf,YAAU,GApwBK;AAqwBf,YAAU,GArwBK;AAswBf,YAAU,GAtwBK;AAuwBf,YAAU,GAvwBK;AAwwBf,YAAU,IAxwBK;AAywBf,YAAU,GAzwBK;AA0wBf,YAAU,GA1wBK;AA2wBf,YAAU,GA3wBK;AA4wBf,YAAU,GA5wBK;AA6wBf,YAAU,GA7wBK;AA8wBf,YAAU,GA9wBK;AA+wBf,YAAU,GA/wBK;AAgxBf,YAAU,GAhxBK;AAixBf,YAAU,GAjxBK;AAkxBf,YAAU,GAlxBK;AAmxBf,YAAU,GAnxBK;AAoxBf,YAAU,GApxBK;AAqxBf,YAAU,GArxBK;AAsxBf,YAAU,GAtxBK;AAuxBf,YAAU,GAvxBK;AAwxBf,YAAU,GAxxBK;AAyxBf,YAAU,GAzxBK;AA0xBf,YAAU,GA1xBK;AA2xBf,YAAU,GA3xBK;AA4xBf,YAAU,GA5xBK;AA6xBf,YAAU,GA7xBK;AA8xBf,YAAU,GA9xBK;AA+xBf,YAAU,GA/xBK;AAgyBf,YAAU,GAhyBK;AAiyBf,YAAU,GAjyBK;AAkyBf,YAAU,GAlyBK;AAmyBf,YAAU,GAnyBK;AAoyBf,YAAU,GApyBK;AAqyBf,YAAU,GAryBK;AAsyBf,YAAU,GAtyBK;AAuyBf,YAAU,GAvyBK;AAwyBf,YAAU,GAxyBK;AAyyBf,YAAU,GAzyBK;AA0yBf,YAAU,GA1yBK;AA2yBf,YAAU,GA3yBK;AA4yBf,YAAU,GA5yBK;AA6yBf,YAAU,GA7yBK;AA8yBf,YAAU,GA9yBK;AA+yBf,YAAU,GA/yBK;AAgzBf,YAAU,GAhzBK;AAizBf,YAAU,GAjzBK;AAkzBf,YAAU,GAlzBK;AAmzBf,YAAU,QAnzBK;AAozBf,YAAU,QApzBK;AAqzBf,YAAU,QArzBK;AAszBf,YAAU,QAtzBK;AAuzBf,YAAU,QAvzBK;AAwzBf,YAAU,QAxzBK;AAyzBf,YAAU,QAzzBK;AA0zBf,YAAU,QA1zBK;AA2zBf,YAAU,QA3zBK;AA4zBf,YAAU,QA5zBK;AA6zBf,YAAU,QA7zBK;AA8zBf,YAAU,QA9zBK;AA+zBf,YAAU,QA/zBK;AAg0Bf,YAAU,QAh0BK;AAi0Bf,YAAU,QAj0BK;AAk0Bf,YAAU,QAl0BK;AAm0Bf,YAAU,QAn0BK;AAo0Bf,YAAU,QAp0BK;AAq0Bf,YAAU,QAr0BK;AAs0Bf,YAAU,QAt0BK;AAu0Bf,YAAU;AAv0BK,CAAnB;;AAy0BA,SAASC,iBAAT,CAA2BC,IAA3B,EAAiC;AAC7B,QAAMC,KAAK,GAAIC,CAAD,IAAOJ,UAAU,CAACI,CAAD,CAAV,IAAiBA,CAAtC;;AACA,SAAOF,IAAI,CAAC1C,OAAL,CAAa,mBAAb,EAAkC2C,KAAlC,CAAP;AACH;;AAED,MAAME,SAAN,CAAgB;AACZnC,EAAAA,WAAW,CAACoC,SAAD,EAAYC,eAAZ,EAA6B;AACpC,SAAKD,SAAL,GAAiBA,SAAjB;AACA,SAAKC,eAAL,GAAuBA,eAAvB;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,cAAL,GAAsB,EAAtB;AACA,SAAKC,YAAL,GAAoB,CAAC,CAArB;AACH;;AACQ,MAALC,KAAK,GAAG;AACR,WAAO,KAAKH,MAAZ;AACH;;AACgB,MAAbI,aAAa,GAAG;AAChB,WAAO,KAAKH,cAAZ;AACH;;AACc,MAAXI,WAAW,GAAG;AACd,WAAO,KAAKH,YAAZ;AACH;;AACgB,MAAbI,aAAa,GAAG;AAChB,WAAO,KAAKP,eAAL,CAAqBjD,KAA5B;AACH;;AACa,MAAVyD,UAAU,GAAG;AACb,WAAO,KAAKN,cAAL,CAAoB,KAAKC,YAAzB,CAAP;AACH;;AACkB,MAAfM,eAAe,GAAG;AAClB,WAAO,KAAKV,SAAL,CAAeW,YAAf,IAA+B,KAAKT,MAAL,CAAYU,MAAZ,KAAuB,KAAKJ,aAAL,CAAmBI,MAAhF;AACH;;AACmB,MAAhBC,gBAAgB,GAAG;AACnB,WAAO,KAAKb,SAAL,CAAec,QAAf,IAA2B,KAAKd,SAAL,CAAee,gBAAf,IAAmC,KAAKP,aAAL,CAAmBI,MAAxF;AACH;;AACmB,MAAhBI,gBAAgB,GAAG;AACnB,QAAIC,CAAC,GAAG,KAAKT,aAAL,CAAmBI,MAAnB,GAA4B,CAApC;;AACA,WAAOK,CAAC,IAAI,CAAZ,EAAeA,CAAC,EAAhB,EAAoB;AAChB,YAAMC,IAAI,GAAG,KAAKV,aAAL,CAAmBS,CAAnB,CAAb;;AACA,UAAI,CAACC,IAAI,CAACC,QAAV,EAAoB;AAChB,eAAOD,IAAP;AACH;AACJ;;AACD,WAAO,IAAP;AACH;;AACDE,EAAAA,QAAQ,CAACf,KAAD,EAAQ;AACZ,SAAKH,MAAL,GAAcG,KAAK,CAACpE,GAAN,CAAU,CAACiF,IAAD,EAAOG,KAAP,KAAiB,KAAKC,OAAL,CAAaJ,IAAb,EAAmBG,KAAnB,CAA3B,CAAd;;AACA,QAAI,KAAKrB,SAAL,CAAeuB,OAAnB,EAA4B;AACxB,WAAKC,OAAL,GAAe,KAAKC,QAAL,CAAc,KAAKvB,MAAnB,EAA2B,KAAKF,SAAL,CAAeuB,OAA1C,CAAf;AACA,WAAKrB,MAAL,GAAc,KAAKwB,QAAL,CAAc,KAAKF,OAAnB,CAAd;AACH,KAHD,MAIK;AACD,WAAKA,OAAL,GAAe,IAAIG,GAAJ,EAAf;;AACA,WAAKH,OAAL,CAAaI,GAAb,CAAiBvE,SAAjB,EAA4B,KAAK6C,MAAjC;AACH;;AACD,SAAKC,cAAL,GAAsB,CAAC,GAAG,KAAKD,MAAT,CAAtB;AACH;;AACD2B,EAAAA,MAAM,CAACX,IAAD,EAAO;AACT,QAAIA,IAAI,CAACY,QAAL,IAAiB,KAAKjB,gBAA1B,EAA4C;AACxC;AACH;;AACD,UAAMC,QAAQ,GAAG,KAAKd,SAAL,CAAec,QAAhC;;AACA,QAAI,CAACA,QAAL,EAAe;AACX,WAAKiB,aAAL;AACH;;AACD,SAAK9B,eAAL,CAAqB4B,MAArB,CAA4BX,IAA5B,EAAkCJ,QAAlC,EAA4C,KAAKd,SAAL,CAAegC,sBAA3D;;AACA,QAAI,KAAKhC,SAAL,CAAeW,YAAnB,EAAiC;AAC7B,WAAKsB,aAAL,CAAmBf,IAAnB;AACH;AACJ;;AACDgB,EAAAA,QAAQ,CAAChB,IAAD,EAAO;AACX,QAAI,CAACA,IAAI,CAACY,QAAV,EAAoB;AAChB;AACH;;AACD,SAAK7B,eAAL,CAAqBiC,QAArB,CAA8BhB,IAA9B,EAAoC,KAAKlB,SAAL,CAAec,QAAnD;;AACA,QAAI,KAAKd,SAAL,CAAeW,YAAf,IAA+BvD,SAAS,CAAC8D,IAAI,CAACG,KAAN,CAAxC,IAAwD,KAAKrB,SAAL,CAAec,QAA3E,EAAqF;AACjF,WAAKqB,aAAL,CAAmBjB,IAAnB;AACH;AACJ;;AACDkB,EAAAA,QAAQ,CAACpF,KAAD,EAAQ;AACZ,QAAIqF,MAAJ;;AACA,QAAI,KAAKrC,SAAL,CAAesC,WAAnB,EAAgC;AAC5BD,MAAAA,MAAM,GAAGnB,IAAI,IAAI,KAAKlB,SAAL,CAAesC,WAAf,CAA2BpB,IAAI,CAAClE,KAAhC,EAAuCA,KAAvC,CAAjB;AACH,KAFD,MAGK,IAAI,KAAKgD,SAAL,CAAeuC,SAAnB,EAA8B;AAC/BF,MAAAA,MAAM,GAAGnB,IAAI,IAAI,CAACA,IAAI,CAACsB,QAAN,IAAkB,KAAKC,aAAL,CAAmBvB,IAAI,CAAClE,KAAxB,EAA+B,KAAKgD,SAAL,CAAeuC,SAA9C,MAA6DvF,KAAhG;AACH,KAFI,MAGA;AACDqF,MAAAA,MAAM,GAAGnB,IAAI,IAAIA,IAAI,CAAClE,KAAL,KAAeA,KAAf,IACb,CAACkE,IAAI,CAACsB,QAAN,IAAkBtB,IAAI,CAACwB,KAAvB,IAAgCxB,IAAI,CAACwB,KAAL,KAAe,KAAKD,aAAL,CAAmBzF,KAAnB,EAA0B,KAAKgD,SAAL,CAAe2C,SAAzC,CADnD;AAEH;;AACD,WAAO,KAAKzC,MAAL,CAAY0C,IAAZ,CAAiB1B,IAAI,IAAImB,MAAM,CAACnB,IAAD,CAA/B,CAAP;AACH;;AACD2B,EAAAA,OAAO,CAAC3B,IAAD,EAAO;AACV,UAAM4B,MAAM,GAAG,KAAKxB,OAAL,CAAaJ,IAAb,EAAmB,KAAKhB,MAAL,CAAYU,MAA/B,CAAf;;AACA,SAAKV,MAAL,CAAY6C,IAAZ,CAAiBD,MAAjB;;AACA,SAAK3C,cAAL,CAAoB4C,IAApB,CAAyBD,MAAzB;;AACA,WAAOA,MAAP;AACH;;AACDf,EAAAA,aAAa,CAACiB,YAAY,GAAG,KAAhB,EAAuB;AAChC,SAAK/C,eAAL,CAAqBgD,KAArB,CAA2BD,YAA3B;;AACA,SAAK9C,MAAL,CAAYgD,OAAZ,CAAoBhC,IAAI,IAAI;AACxBA,MAAAA,IAAI,CAACY,QAAL,GAAgBkB,YAAY,IAAI9B,IAAI,CAACY,QAArB,IAAiCZ,IAAI,CAACC,QAAtD;AACAD,MAAAA,IAAI,CAACiC,MAAL,GAAc,KAAd;AACH,KAHD;;AAIA,QAAI,KAAKnD,SAAL,CAAeW,YAAnB,EAAiC;AAC7B,WAAKyC,kBAAL;AACH;AACJ;;AACDC,EAAAA,WAAW,CAACC,IAAD,EAAO;AACdA,IAAAA,IAAI,GAAG3D,iBAAiB,CAAC2D,IAAD,CAAjB,CAAwBC,iBAAxB,EAAP;AACA,WAAO,KAAKjD,aAAL,CAAmBsC,IAAnB,CAAwB1B,IAAI,IAAI;AACnC,YAAMwB,KAAK,GAAG/C,iBAAiB,CAACuB,IAAI,CAACwB,KAAN,CAAjB,CAA8Ba,iBAA9B,EAAd;AACA,aAAOb,KAAK,CAACc,MAAN,CAAa,CAAb,EAAgBF,IAAI,CAAC1C,MAArB,MAAiC0C,IAAxC;AACH,KAHM,CAAP;AAIH;;AACDtH,EAAAA,MAAM,CAACsH,IAAD,EAAO;AACT,QAAI,CAACA,IAAL,EAAW;AACP,WAAKF,kBAAL;AACA;AACH;;AACD,SAAKjD,cAAL,GAAsB,EAAtB;AACAmD,IAAAA,IAAI,GAAG,KAAKtD,SAAL,CAAeyD,QAAf,GAA0BH,IAA1B,GAAiC3D,iBAAiB,CAAC2D,IAAD,CAAjB,CAAwBC,iBAAxB,EAAxC;AACA,UAAM1D,KAAK,GAAG,KAAKG,SAAL,CAAeyD,QAAf,IAA2B,KAAKC,gBAA9C;AACA,UAAM/C,YAAY,GAAG,KAAKX,SAAL,CAAeW,YAApC;;AACA,SAAK,MAAMgD,GAAX,IAAkBC,KAAK,CAACC,IAAN,CAAW,KAAKrC,OAAL,CAAasC,IAAb,EAAX,CAAlB,EAAmD;AAC/C,YAAMC,YAAY,GAAG,EAArB;;AACA,WAAK,MAAM7C,IAAX,IAAmB,KAAKM,OAAL,CAAawC,GAAb,CAAiBL,GAAjB,CAAnB,EAA0C;AACtC,YAAIhD,YAAY,KAAKO,IAAI,CAAC+C,MAAL,IAAe/C,IAAI,CAAC+C,MAAL,CAAYnC,QAA3B,IAAuCZ,IAAI,CAACY,QAAjD,CAAhB,EAA4E;AACxE;AACH;;AACD,cAAMoC,UAAU,GAAG,KAAKlE,SAAL,CAAeyD,QAAf,GAA0BvC,IAAI,CAAClE,KAA/B,GAAuCkE,IAA1D;;AACA,YAAIrB,KAAK,CAACyD,IAAD,EAAOY,UAAP,CAAT,EAA6B;AACzBH,UAAAA,YAAY,CAAChB,IAAb,CAAkB7B,IAAlB;AACH;AACJ;;AACD,UAAI6C,YAAY,CAACnD,MAAb,GAAsB,CAA1B,EAA6B;AACzB,cAAM,CAACuD,IAAD,IAASJ,YAAY,CAACK,KAAb,CAAmB,CAAC,CAApB,CAAf;;AACA,YAAID,IAAI,CAACF,MAAT,EAAiB;AACb,gBAAMI,IAAI,GAAG,KAAKnE,MAAL,CAAY0C,IAAZ,CAAiB0B,CAAC,IAAIA,CAAC,KAAKH,IAAI,CAACF,MAAjC,CAAb;;AACA,eAAK9D,cAAL,CAAoB4C,IAApB,CAAyBsB,IAAzB;AACH;;AACD,aAAKlE,cAAL,CAAoB4C,IAApB,CAAyB,GAAGgB,YAA5B;AACH;AACJ;AACJ;;AACDX,EAAAA,kBAAkB,GAAG;AACjB,QAAI,KAAKjD,cAAL,CAAoBS,MAApB,KAA+B,KAAKV,MAAL,CAAYU,MAA/C,EAAuD;AACnD;AACH;;AACD,QAAI,KAAKZ,SAAL,CAAeW,YAAf,IAA+B,KAAKH,aAAL,CAAmBI,MAAnB,GAA4B,CAA/D,EAAkE;AAC9D,WAAKT,cAAL,GAAsB,KAAKD,MAAL,CAAYlE,MAAZ,CAAmBsI,CAAC,IAAI,CAACA,CAAC,CAACxC,QAA3B,CAAtB;AACH,KAFD,MAGK;AACD,WAAK3B,cAAL,GAAsB,KAAKD,MAA3B;AACH;AACJ;;AACDqE,EAAAA,UAAU,GAAG;AACT,SAAKnE,YAAL,GAAoB,CAAC,CAArB;AACH;;AACDoE,EAAAA,YAAY,GAAG;AACX,SAAKC,WAAL,CAAiB,CAAC,CAAlB;AACH;;AACDC,EAAAA,gBAAgB,GAAG;AACf,SAAKD,WAAL,CAAiB,CAAC,CAAlB;AACH;;AACDE,EAAAA,QAAQ,CAACzD,IAAD,EAAO;AACX,SAAKd,YAAL,GAAoB,KAAKD,cAAL,CAAoByE,OAApB,CAA4B1D,IAA5B,CAApB;AACH;;AACD2D,EAAAA,qBAAqB,CAACC,WAAD,EAAc;AAC/B,QAAI,KAAK3E,cAAL,CAAoBS,MAApB,KAA+B,CAAnC,EAAsC;AAClC;AACH;;AACD,UAAMmE,eAAe,GAAG,KAAKC,mBAAL,EAAxB;;AACA,QAAID,eAAe,GAAG,CAAC,CAAvB,EAA0B;AACtB,WAAK3E,YAAL,GAAoB2E,eAApB;AACH,KAFD,MAGK;AACD,WAAK3E,YAAL,GAAoB0E,WAAW,GAAG,KAAKxE,aAAL,CAAmB2E,SAAnB,CAA6BX,CAAC,IAAI,CAACA,CAAC,CAACnD,QAArC,CAAH,GAAoD,CAAC,CAApF;AACH;AACJ;;AACDsB,EAAAA,aAAa,CAACK,MAAD,EAASa,GAAT,EAAc;AACvB,QAAI,CAACrG,QAAQ,CAACwF,MAAD,CAAb,EAAuB;AACnB,aAAOA,MAAP;AACH;;AACD,QAAIa,GAAG,CAACiB,OAAJ,CAAY,GAAZ,MAAqB,CAAC,CAA1B,EAA6B;AACzB,aAAO9B,MAAM,CAACa,GAAD,CAAb;AACH,KAFD,MAGK;AACD,YAAMG,IAAI,GAAGH,GAAG,CAACuB,KAAJ,CAAU,GAAV,CAAb;AACA,UAAIlI,KAAK,GAAG8F,MAAZ;;AACA,WAAK,IAAI7B,CAAC,GAAG,CAAR,EAAWkE,GAAG,GAAGrB,IAAI,CAAClD,MAA3B,EAAmCK,CAAC,GAAGkE,GAAvC,EAA4C,EAAElE,CAA9C,EAAiD;AAC7C,YAAIjE,KAAK,IAAI,IAAb,EAAmB;AACf,iBAAO,IAAP;AACH;;AACDA,QAAAA,KAAK,GAAGA,KAAK,CAAC8G,IAAI,CAAC7C,CAAD,CAAL,CAAb;AACH;;AACD,aAAOjE,KAAP;AACH;AACJ;;AACDsE,EAAAA,OAAO,CAACJ,IAAD,EAAOG,KAAP,EAAc;AACjB,UAAMqB,KAAK,GAAGtF,SAAS,CAAC8D,IAAI,CAACkE,cAAN,CAAT,GAAiClE,IAAI,CAACkE,cAAtC,GAAuD,KAAK3C,aAAL,CAAmBvB,IAAnB,EAAyB,KAAKlB,SAAL,CAAe2C,SAAxC,CAArE;AACA,UAAM3F,KAAK,GAAGI,SAAS,CAAC8D,IAAI,CAACmE,cAAN,CAAT,GAAiCnE,IAAI,CAACmE,cAAtC,GAAuDnE,IAArE;AACA,WAAO;AACHG,MAAAA,KADG;AAEHqB,MAAAA,KAAK,EAAEtF,SAAS,CAACsF,KAAD,CAAT,GAAmBA,KAAK,CAACjD,QAAN,EAAnB,GAAsC,EAF1C;AAGHzC,MAAAA,KAHG;AAIHmE,MAAAA,QAAQ,EAAED,IAAI,CAACC,QAJZ;AAKHmE,MAAAA,MAAM,EAAG,GAAE,KAAKtF,SAAL,CAAeuF,UAAW,IAAGlE,KAAM;AAL3C,KAAP;AAOH;;AACDmE,EAAAA,gBAAgB,GAAG;AACf,UAAM1E,QAAQ,GAAG,KAAKd,SAAL,CAAec,QAAhC;;AACA,SAAK,MAAMgB,QAAX,IAAuB,KAAKtB,aAA5B,EAA2C;AACvC,YAAMxD,KAAK,GAAG,KAAKgD,SAAL,CAAeuC,SAAf,GAA2B,KAAKE,aAAL,CAAmBX,QAAQ,CAAC9E,KAA5B,EAAmC,KAAKgD,SAAL,CAAeuC,SAAlD,CAA3B,GAA0FT,QAAQ,CAAC9E,KAAjH;AACA,YAAMkE,IAAI,GAAG9D,SAAS,CAACJ,KAAD,CAAT,GAAmB,KAAKoF,QAAL,CAAcpF,KAAd,CAAnB,GAA0C,IAAvD;;AACA,WAAKiD,eAAL,CAAqBiC,QAArB,CAA8BJ,QAA9B,EAAwChB,QAAxC;;AACA,WAAKb,eAAL,CAAqB4B,MAArB,CAA4BX,IAAI,IAAIY,QAApC,EAA8ChB,QAA9C,EAAwD,KAAKd,SAAL,CAAegC,sBAAvE;AACH;;AACD,QAAI,KAAKhC,SAAL,CAAeW,YAAnB,EAAiC;AAC7B,WAAKR,cAAL,GAAsB,KAAKG,aAAL,CAAmBtE,MAAnB,CAA0BsI,CAAC,IAAI,KAAK9D,aAAL,CAAmBoE,OAAnB,CAA2BN,CAA3B,MAAkC,CAAC,CAAlE,CAAtB;AACH;AACJ;;AACDnC,EAAAA,aAAa,CAACjB,IAAD,EAAO;AAChB,SAAKf,cAAL,CAAoB4C,IAApB,CAAyB7B,IAAzB;;AACA,QAAIA,IAAI,CAAC+C,MAAT,EAAiB;AACb,YAAMA,MAAM,GAAG/C,IAAI,CAAC+C,MAApB;;AACA,YAAMwB,YAAY,GAAG,KAAKtF,cAAL,CAAoByC,IAApB,CAAyB0B,CAAC,IAAIA,CAAC,KAAKL,MAApC,CAArB;;AACA,UAAI,CAACwB,YAAL,EAAmB;AACf,aAAKtF,cAAL,CAAoB4C,IAApB,CAAyBkB,MAAzB;AACH;AACJ,KAND,MAOK,IAAI/C,IAAI,CAACsB,QAAT,EAAmB;AACpB,WAAK,MAAMkD,KAAX,IAAoBxE,IAAI,CAACsB,QAAzB,EAAmC;AAC/BkD,QAAAA,KAAK,CAAC5D,QAAN,GAAiB,KAAjB;;AACA,aAAK3B,cAAL,CAAoB4C,IAApB,CAAyB2C,KAAzB;AACH;AACJ;;AACD,SAAKvF,cAAL,GAAsB,CAAC,GAAG,KAAKA,cAAL,CAAoBwF,IAApB,CAAyB,CAAC7F,CAAD,EAAI8F,CAAJ,KAAW9F,CAAC,CAACuB,KAAF,GAAUuE,CAAC,CAACvE,KAAhD,CAAJ,CAAtB;AACH;;AACDY,EAAAA,aAAa,CAACf,IAAD,EAAO;AAChB,SAAKf,cAAL,GAAsB,KAAKA,cAAL,CAAoBnE,MAApB,CAA2BsI,CAAC,IAAIA,CAAC,KAAKpD,IAAtC,CAAtB;;AACA,QAAIA,IAAI,CAAC+C,MAAT,EAAiB;AACb,YAAMzB,QAAQ,GAAGtB,IAAI,CAAC+C,MAAL,CAAYzB,QAA7B;;AACA,UAAIA,QAAQ,CAACqD,KAAT,CAAevB,CAAC,IAAIA,CAAC,CAACxC,QAAtB,CAAJ,EAAqC;AACjC,aAAK3B,cAAL,GAAsB,KAAKA,cAAL,CAAoBnE,MAApB,CAA2BsI,CAAC,IAAIA,CAAC,KAAKpD,IAAI,CAAC+C,MAA3C,CAAtB;AACH;AACJ,KALD,MAMK,IAAI/C,IAAI,CAACsB,QAAT,EAAmB;AACpB,WAAKrC,cAAL,GAAsB,KAAKG,aAAL,CAAmBtE,MAAnB,CAA0BsI,CAAC,IAAIA,CAAC,CAACL,MAAF,KAAa/C,IAA5C,CAAtB;AACH;AACJ;;AACDwC,EAAAA,gBAAgB,CAACoC,MAAD,EAASC,GAAT,EAAc;AAC1B,UAAMrD,KAAK,GAAG/C,iBAAiB,CAACoG,GAAG,CAACrD,KAAL,CAAjB,CAA6Ba,iBAA7B,EAAd;AACA,WAAOb,KAAK,CAACkC,OAAN,CAAckB,MAAd,IAAwB,CAAC,CAAhC;AACH;;AACDE,EAAAA,iBAAiB,CAACC,KAAD,EAAQ;AACrB,QAAIA,KAAK,GAAG,CAAZ,EAAe;AACX,aAAQ,KAAK7F,YAAL,IAAqB,KAAKD,cAAL,CAAoBS,MAApB,GAA6B,CAAnD,GAAwD,CAAxD,GAA6D,KAAKR,YAAL,GAAoB,CAAxF;AACH;;AACD,WAAQ,KAAKA,YAAL,IAAqB,CAAtB,GAA4B,KAAKD,cAAL,CAAoBS,MAApB,GAA6B,CAAzD,GAA+D,KAAKR,YAAL,GAAoB,CAA1F;AACH;;AACDqE,EAAAA,WAAW,CAACwB,KAAD,EAAQ;AACf,QAAI,KAAK9F,cAAL,CAAoBS,MAApB,KAA+B,CAA/B,IAAoC,KAAKT,cAAL,CAAoB0F,KAApB,CAA0BvB,CAAC,IAAIA,CAAC,CAACnD,QAAjC,CAAxC,EAAoF;AAChF;AACH;;AACD,SAAKf,YAAL,GAAoB,KAAK4F,iBAAL,CAAuBC,KAAvB,CAApB;;AACA,QAAI,KAAKxF,UAAL,CAAgBU,QAApB,EAA8B;AAC1B,WAAKsD,WAAL,CAAiBwB,KAAjB;AACH;AACJ;;AACDjB,EAAAA,mBAAmB,GAAG;AAClB,QAAI,KAAKhF,SAAL,CAAeW,YAAnB,EAAiC;AAC7B,aAAO,CAAC,CAAR;AACH;;AACD,QAAI,KAAKP,YAAL,GAAoB,CAAC,CAArB,IAA0B,KAAKK,UAAL,KAAoBpD,SAAlD,EAA6D;AACzD,aAAO,CAAC,CAAR;AACH;;AACD,UAAM6I,aAAa,GAAG,KAAK/F,cAAL,CAAoByE,OAApB,CAA4B,KAAK5D,gBAAjC,CAAtB;;AACA,QAAI,KAAKA,gBAAL,IAAyBkF,aAAa,GAAG,CAA7C,EAAgD;AAC5C,aAAO,CAAC,CAAR;AACH;;AACD,WAAO3G,IAAI,CAAC4G,GAAL,CAAS,KAAK5F,WAAd,EAA2B2F,aAA3B,CAAP;AACH;;AACDzE,EAAAA,QAAQ,CAACpB,KAAD,EAAQ+F,IAAR,EAAc;AAClB,UAAMC,MAAM,GAAG,IAAI1E,GAAJ,EAAf;;AACA,QAAItB,KAAK,CAACO,MAAN,KAAiB,CAArB,EAAwB;AACpB,aAAOyF,MAAP;AACH,KAJiB,CAKlB;;;AACA,QAAIzC,KAAK,CAAC0C,OAAN,CAAcjG,KAAK,CAAC,CAAD,CAAL,CAASrD,KAAT,CAAeoJ,IAAf,CAAd,CAAJ,EAAyC;AACrC,WAAK,MAAMlF,IAAX,IAAmBb,KAAnB,EAA0B;AACtB,cAAMmC,QAAQ,GAAG,CAACtB,IAAI,CAAClE,KAAL,CAAWoJ,IAAX,KAAoB,EAArB,EAAyBnK,GAAzB,CAA6B,CAACqI,CAAD,EAAIjD,KAAJ,KAAc,KAAKC,OAAL,CAAagD,CAAb,EAAgBjD,KAAhB,CAA3C,CAAjB;AACAgF,QAAAA,MAAM,CAACzE,GAAP,CAAWV,IAAX,EAAiBsB,QAAjB;AACH;;AACD,aAAO6D,MAAP;AACH;;AACD,UAAME,OAAO,GAAG9I,UAAU,CAAC,KAAKuC,SAAL,CAAeuB,OAAhB,CAA1B;;AACA,UAAMiF,KAAK,GAAItF,IAAD,IAAU;AACpB,YAAMyC,GAAG,GAAG4C,OAAO,GAAGH,IAAI,CAAClF,IAAI,CAAClE,KAAN,CAAP,GAAsBkE,IAAI,CAAClE,KAAL,CAAWoJ,IAAX,CAAzC;AACA,aAAOhJ,SAAS,CAACuG,GAAD,CAAT,GAAiBA,GAAjB,GAAuBtG,SAA9B;AACH,KAHD,CAdkB,CAkBlB;;;AACA,SAAK,MAAM6D,IAAX,IAAmBb,KAAnB,EAA0B;AACtB,YAAMsD,GAAG,GAAG6C,KAAK,CAACtF,IAAD,CAAjB;AACA,YAAMuF,KAAK,GAAGJ,MAAM,CAACrC,GAAP,CAAWL,GAAX,CAAd;;AACA,UAAI8C,KAAJ,EAAW;AACPA,QAAAA,KAAK,CAAC1D,IAAN,CAAW7B,IAAX;AACH,OAFD,MAGK;AACDmF,QAAAA,MAAM,CAACzE,GAAP,CAAW+B,GAAX,EAAgB,CAACzC,IAAD,CAAhB;AACH;AACJ;;AACD,WAAOmF,MAAP;AACH;;AACD3E,EAAAA,QAAQ,CAAC2E,MAAD,EAAS;AACb,UAAMK,WAAW,GAAGjJ,UAAU,CAAC,KAAKuC,SAAL,CAAeuB,OAAhB,CAA9B;AACA,UAAMlB,KAAK,GAAG,EAAd;;AACA,SAAK,MAAMsD,GAAX,IAAkBC,KAAK,CAACC,IAAN,CAAWwC,MAAM,CAACvC,IAAP,EAAX,CAAlB,EAA6C;AACzC,UAAI7C,CAAC,GAAGZ,KAAK,CAACO,MAAd;;AACA,UAAI+C,GAAG,KAAKtG,SAAZ,EAAuB;AACnB,cAAMsJ,YAAY,GAAGN,MAAM,CAACrC,GAAP,CAAW3G,SAAX,KAAyB,EAA9C;AACAgD,QAAAA,KAAK,CAAC0C,IAAN,CAAW,GAAG4D,YAAY,CAAC1K,GAAb,CAAiBqI,CAAC,IAAI;AAChCA,UAAAA,CAAC,CAACjD,KAAF,GAAUJ,CAAC,EAAX;AACA,iBAAOqD,CAAP;AACH,SAHa,CAAd;AAIA;AACH;;AACD,YAAMsC,WAAW,GAAGtJ,QAAQ,CAACqG,GAAD,CAA5B;AACA,YAAMM,MAAM,GAAG;AACXvB,QAAAA,KAAK,EAAEkE,WAAW,GAAG,EAAH,GAAQC,MAAM,CAAClD,GAAD,CADrB;AAEXnB,QAAAA,QAAQ,EAAEnF,SAFC;AAGX4G,QAAAA,MAAM,EAAE,IAHG;AAIX5C,QAAAA,KAAK,EAAEJ,CAAC,EAJG;AAKXE,QAAAA,QAAQ,EAAE,CAAC,KAAKnB,SAAL,CAAe8G,eALf;AAMXxB,QAAAA,MAAM,EAAEjG,KAAK;AANF,OAAf;AAQA,YAAM0H,QAAQ,GAAGL,WAAW,GAAG,KAAK1G,SAAL,CAAe2C,SAAlB,GAA8B,KAAK3C,SAAL,CAAeuB,OAAzE;;AACA,YAAMyF,UAAU,GAAG,KAAKhH,SAAL,CAAegH,UAAf,KAA8B,MAAM;AACnD,YAAIJ,WAAJ,EAAiB;AACb,iBAAOjD,GAAG,CAAC3G,KAAX;AACH;;AACD,eAAO;AAAE,WAAC+J,QAAD,GAAYpD;AAAd,SAAP;AACH,OALkB,CAAnB;;AAMA,YAAMnB,QAAQ,GAAG6D,MAAM,CAACrC,GAAP,CAAWL,GAAX,EAAgB1H,GAAhB,CAAoBqI,CAAC,IAAI;AACtCA,QAAAA,CAAC,CAACL,MAAF,GAAWA,MAAX;AACAK,QAAAA,CAAC,CAAC9B,QAAF,GAAanF,SAAb;AACAiH,QAAAA,CAAC,CAACjD,KAAF,GAAUJ,CAAC,EAAX;AACA,eAAOqD,CAAP;AACH,OALgB,CAAjB;AAMAL,MAAAA,MAAM,CAACzB,QAAP,GAAkBA,QAAlB;AACAyB,MAAAA,MAAM,CAACjH,KAAP,GAAegK,UAAU,CAACrD,GAAD,EAAMnB,QAAQ,CAACvG,GAAT,CAAaqI,CAAC,IAAIA,CAAC,CAACtH,KAApB,CAAN,CAAzB;AACAqD,MAAAA,KAAK,CAAC0C,IAAN,CAAWkB,MAAX;AACA5D,MAAAA,KAAK,CAAC0C,IAAN,CAAW,GAAGP,QAAd;AACH;;AACD,WAAOnC,KAAP;AACH;;AA9VW;;AAiWhB,IAAI4G,OAAJ;;AACA,CAAC,UAAUA,OAAV,EAAmB;AAChBA,EAAAA,OAAO,CAACA,OAAO,CAAC,KAAD,CAAP,GAAiB,CAAlB,CAAP,GAA8B,KAA9B;AACAA,EAAAA,OAAO,CAACA,OAAO,CAAC,OAAD,CAAP,GAAmB,EAApB,CAAP,GAAiC,OAAjC;AACAA,EAAAA,OAAO,CAACA,OAAO,CAAC,KAAD,CAAP,GAAiB,EAAlB,CAAP,GAA+B,KAA/B;AACAA,EAAAA,OAAO,CAACA,OAAO,CAAC,OAAD,CAAP,GAAmB,EAApB,CAAP,GAAiC,OAAjC;AACAA,EAAAA,OAAO,CAACA,OAAO,CAAC,SAAD,CAAP,GAAqB,EAAtB,CAAP,GAAmC,SAAnC;AACAA,EAAAA,OAAO,CAACA,OAAO,CAAC,WAAD,CAAP,GAAuB,EAAxB,CAAP,GAAqC,WAArC;AACAA,EAAAA,OAAO,CAACA,OAAO,CAAC,WAAD,CAAP,GAAuB,CAAxB,CAAP,GAAoC,WAApC;AACH,CARD,EAQGA,OAAO,KAAKA,OAAO,GAAG,EAAf,CARV;;AAUA,MAAMC,sBAAN,CAA6B;AACzBtJ,EAAAA,WAAW,GAAG;AACV,SAAKuJ,WAAL,GAAmB;AACfC,MAAAA,UAAU,EAAE,CADG;AAEfC,MAAAA,WAAW,EAAE,CAFE;AAGfC,MAAAA,gBAAgB,EAAE;AAHH,KAAnB;AAKH;;AACa,MAAVC,UAAU,GAAG;AACb,WAAO,KAAKJ,WAAZ;AACH;;AACDK,EAAAA,cAAc,CAACC,SAAD,EAAYC,WAAZ,EAAyBC,MAAzB,EAAiC;AAC3C,UAAMC,CAAC,GAAG,KAAKT,WAAf;AACA,UAAMU,YAAY,GAAGD,CAAC,CAACR,UAAF,GAAeM,WAApC;AACA,UAAMI,SAAS,GAAGvI,IAAI,CAAC4G,GAAL,CAAS,CAAT,EAAYsB,SAAZ,CAAlB;AACA,UAAMM,gBAAgB,GAAGD,SAAS,GAAGD,YAAZ,GAA2BH,WAApD;AACA,QAAIM,GAAG,GAAGzI,IAAI,CAAC0I,GAAL,CAASP,WAAT,EAAsBnI,IAAI,CAAC2I,IAAL,CAAUH,gBAAV,KAA+BH,CAAC,CAACN,gBAAF,GAAqB,CAApD,CAAtB,CAAV;AACA,UAAMa,WAAW,GAAGH,GAApB;AACA,UAAMI,QAAQ,GAAG7I,IAAI,CAAC4G,GAAL,CAAS,CAAT,EAAYgC,WAAW,GAAGP,CAAC,CAACN,gBAA5B,CAAjB;AACA,QAAIe,KAAK,GAAG9I,IAAI,CAAC0I,GAAL,CAASG,QAAT,EAAmB7I,IAAI,CAAC+I,KAAL,CAAWP,gBAAX,CAAnB,CAAZ;AACA,QAAIQ,UAAU,GAAGX,CAAC,CAACR,UAAF,GAAe7H,IAAI,CAAC2I,IAAL,CAAUG,KAAV,CAAf,GAAmCT,CAAC,CAACR,UAAF,GAAe7H,IAAI,CAAC0I,GAAL,CAASI,KAAT,EAAgBV,MAAhB,CAAnE;AACAY,IAAAA,UAAU,GAAG,CAACC,KAAK,CAACD,UAAD,CAAN,GAAqBA,UAArB,GAAkC,CAA/C;AACAF,IAAAA,KAAK,GAAG,CAACG,KAAK,CAACH,KAAD,CAAN,GAAgBA,KAAhB,GAAwB,CAAC,CAAjC;AACAL,IAAAA,GAAG,GAAG,CAACQ,KAAK,CAACR,GAAD,CAAN,GAAcA,GAAd,GAAoB,CAAC,CAA3B;AACAK,IAAAA,KAAK,IAAIV,MAAT;AACAU,IAAAA,KAAK,GAAG9I,IAAI,CAAC4G,GAAL,CAAS,CAAT,EAAYkC,KAAZ,CAAR;AACAL,IAAAA,GAAG,IAAIL,MAAP;AACAK,IAAAA,GAAG,GAAGzI,IAAI,CAAC0I,GAAL,CAASP,WAAT,EAAsBM,GAAtB,CAAN;AACA,WAAO;AACHO,MAAAA,UADG;AAEHV,MAAAA,YAFG;AAGHQ,MAAAA,KAHG;AAIHL,MAAAA;AAJG,KAAP;AAMH;;AACDS,EAAAA,aAAa,CAACrB,UAAD,EAAaC,WAAb,EAA0B;AACnC,UAAMC,gBAAgB,GAAG/H,IAAI,CAAC4G,GAAL,CAAS,CAAT,EAAY5G,IAAI,CAAC+I,KAAL,CAAWjB,WAAW,GAAGD,UAAzB,CAAZ,CAAzB;AACA,SAAKD,WAAL,GAAmB;AACfC,MAAAA,UADe;AAEfC,MAAAA,WAFe;AAGfC,MAAAA;AAHe,KAAnB;AAKH;;AACDoB,EAAAA,WAAW,CAACC,OAAD,EAAUvB,UAAV,EAAsBwB,UAAtB,EAAkC;AACzC,UAAM;AAAEvB,MAAAA;AAAF,QAAkB,KAAKE,UAA7B;AACA,UAAMsB,UAAU,GAAGF,OAAO,GAAGvB,UAA7B;AACA,UAAM0B,GAAG,GAAGF,UAAZ;AACA,UAAMG,MAAM,GAAGD,GAAG,GAAGzB,WAArB;;AACA,QAAIA,WAAW,IAAIwB,UAAf,IAA6BD,UAAU,KAAKD,OAAhD,EAAyD;AACrD,aAAO,IAAP;AACH;;AACD,QAAIE,UAAU,GAAGE,MAAjB,EAAyB;AACrB,aAAOD,GAAG,GAAGD,UAAN,GAAmBE,MAA1B;AACH,KAFD,MAGK,IAAIJ,OAAO,IAAIG,GAAf,EAAoB;AACrB,aAAOH,OAAP;AACH;;AACD,WAAO,IAAP;AACH;;AA1DwB;;AA4D7BzB,sBAAsB,CAAC9I,IAAvB;AAAA,mBAAmH8I,sBAAnH;AAAA;;AACAA,sBAAsB,CAAC8B,KAAvB,kBA/4CuG5O,EA+4CvG;AAAA,SAAuH8M,sBAAvH;AAAA,WAAuHA,sBAAvH;AAAA;;AACA;AAAA,qDAh5CuG9M,EAg5CvG,mBAA2F8M,sBAA3F,EAA+H,CAAC;AACpH5I,IAAAA,IAAI,EAAE/D;AAD8G,GAAD,CAA/H;AAAA;;AAIA,MAAM0O,aAAa,GAAG,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAAtB;AACA,MAAMC,gBAAgB,GAAG,OAAOC,qBAAP,KAAiC,WAAjC,GAA+CjN,uBAA/C,GAAyEC,aAAlG;;AACA,MAAMiN,wBAAN,CAA+B;AAC3BxL,EAAAA,WAAW,CAACyL,SAAD,EAAYC,KAAZ,EAAmBC,aAAnB,EAAkCC,WAAlC,EAA+CC,SAA/C,EAA0D;AACjE,SAAKJ,SAAL,GAAiBA,SAAjB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,aAAL,GAAqBA,aAArB;AACA,SAAKE,SAAL,GAAiBA,SAAjB;AACA,SAAKpJ,KAAL,GAAa,EAAb;AACA,SAAKqJ,QAAL,GAAgB,MAAhB;AACA,SAAKC,aAAL,GAAqB,KAArB;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,MAAL,GAAc,IAAIrP,YAAJ,EAAd;AACA,SAAKsP,MAAL,GAAc,IAAItP,YAAJ,EAAd;AACA,SAAKuP,WAAL,GAAmB,IAAIvP,YAAJ,EAAnB;AACA,SAAKwP,YAAL,GAAoB,IAAIxP,YAAJ,EAApB;AACA,SAAKyP,SAAL,GAAiB,IAAI7N,OAAJ,EAAjB;AACA,SAAK8N,iBAAL,GAAyB,KAAzB;AACA,SAAKC,mBAAL,GAA2B,KAA3B;AACA,SAAKC,mBAAL,GAA2B,CAA3B;AACA,SAAKC,SAAL,GAAiBb,WAAW,CAACvL,aAA7B;AACH;;AACkB,MAAfqM,eAAe,GAAG;AAClB,WAAO,KAAKC,gBAAZ;AACH;;AACc,MAAX7C,WAAW,GAAG;AACd,WAAO,KAAK8C,YAAZ;AACH;;AACc,MAAX9C,WAAW,CAAC1K,KAAD,EAAQ;AACnB,QAAIA,KAAK,KAAK,KAAKwN,YAAnB,EAAiC;AAC7B,WAAKA,YAAL,GAAoBxN,KAApB;;AACA,WAAKyN,qBAAL;AACH;AACJ;;AACe,MAAZC,YAAY,GAAG;AACf,QAAI,KAAKjK,UAAT,EAAqB;AACjB,YAAM;AAAE2G,QAAAA,UAAF;AAAcC,QAAAA;AAAd,UAA8B,KAAKkC,aAAL,CAAmBhC,UAAvD;AACA,YAAMoD,MAAM,GAAG,KAAKlK,UAAL,CAAgBY,KAAhB,GAAwB+F,UAAvC;AACA,aAAOC,WAAW,GAAGsD,MAAd,GAAuB,CAAvB,GAA2BA,MAAlC;AACH;;AACD,WAAO,CAAP;AACH;;AACDC,EAAAA,QAAQ,GAAG;AACP,SAAKC,OAAL,GAAe,KAAKR,SAAL,CAAeS,aAA9B;AACA,SAAKC,eAAL,GAAuB,KAAKC,iBAAL,CAAuB/M,aAA9C;AACA,SAAKgN,gBAAL,GAAwB,KAAKC,gBAAL,CAAsBjN,aAA9C;AACA,SAAKkN,aAAL,GAAqB,KAAKC,iBAAL,CAAuBnN,aAA5C;;AACA,SAAKoN,aAAL;;AACA,SAAKC,mBAAL;;AACA,SAAKC,eAAL;;AACA,SAAKC,uBAAL;AACH;;AACDzN,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,QAAIA,OAAO,CAACqC,KAAZ,EAAmB;AACf,YAAMoL,MAAM,GAAGzN,OAAO,CAACqC,KAAvB;;AACA,WAAKqL,cAAL,CAAoBD,MAAM,CAACE,YAA3B,EAAyCF,MAAM,CAACG,WAAhD;AACH;AACJ;;AACDC,EAAAA,WAAW,GAAG;AACV,SAAK5B,SAAL,CAAe6B,IAAf;;AACA,SAAK7B,SAAL,CAAe8B,QAAf;;AACA,SAAK9B,SAAL,CAAe+B,WAAf;;AACA,QAAI,KAAKC,QAAT,EAAmB;AACf,WAAK5C,SAAL,CAAe6C,WAAf,CAA2B,KAAK7B,SAAL,CAAe8B,UAA1C,EAAsD,KAAK9B,SAA3D;AACH;AACJ;;AACD+B,EAAAA,QAAQ,CAACtJ,MAAD,EAASuJ,eAAe,GAAG,KAA3B,EAAkC;AACtC,QAAI,CAACvJ,MAAL,EAAa;AACT;AACH;;AACD,UAAMzB,KAAK,GAAG,KAAKhB,KAAL,CAAWuE,OAAX,CAAmB9B,MAAnB,CAAd;;AACA,QAAIzB,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAI,KAAKqG,WAA/B,EAA4C;AACxC;AACH;;AACD,QAAI0E,QAAJ;;AACA,QAAI,KAAKzC,aAAT,EAAwB;AACpB,YAAMvC,UAAU,GAAG,KAAKmC,aAAL,CAAmBhC,UAAnB,CAA8BH,UAAjD;AACAgF,MAAAA,QAAQ,GAAG,KAAK7C,aAAL,CAAmBb,WAAnB,CAA+BrH,KAAK,GAAG+F,UAAvC,EAAmDA,UAAnD,EAA+D,KAAKgD,mBAApE,CAAX;AACH,KAHD,MAIK;AACD,YAAMlJ,IAAI,GAAG,KAAKmJ,SAAL,CAAeiC,aAAf,CAA8B,IAAGxJ,MAAM,CAACwC,MAAO,EAA/C,CAAb;;AACA,YAAMsD,UAAU,GAAGyD,eAAe,GAAGnL,IAAI,CAACqL,SAAR,GAAoB,KAAKnC,mBAA3D;AACAgC,MAAAA,QAAQ,GAAG,KAAK7C,aAAL,CAAmBb,WAAnB,CAA+BxH,IAAI,CAACqL,SAApC,EAA+CrL,IAAI,CAACsL,YAApD,EAAkE5D,UAAlE,CAAX;AACH;;AACD,QAAIxL,SAAS,CAACgP,QAAD,CAAb,EAAyB;AACrB,WAAKnB,gBAAL,CAAsBnD,SAAtB,GAAkCsE,QAAlC;AACH;AACJ;;AACDK,EAAAA,WAAW,GAAG;AACV,UAAMC,KAAK,GAAG,KAAKzB,gBAAnB;AACAyB,IAAAA,KAAK,CAAC5E,SAAN,GAAkB4E,KAAK,CAAC7E,YAAN,GAAqB6E,KAAK,CAACF,YAA7C;AACH;;AACDG,EAAAA,cAAc,GAAG;AACb,SAAKC,gBAAL;AACH;;AACDC,EAAAA,uBAAuB,GAAG;AACtB,SAAKtC,gBAAL,GAAwB,KAAKuC,yBAAL,CAA+B,KAAKzC,SAApC,CAAxB;;AACA,QAAIpB,aAAa,CAAC8D,QAAd,CAAuB,KAAKxC,gBAA5B,CAAJ,EAAmD;AAC/C,WAAKyC,oBAAL,CAA0B,KAAKzC,gBAA/B;AACH,KAFD,MAGK;AACD,WAAKyC,oBAAL,CAA0B,QAA1B;AACH;;AACD,QAAI,KAAKf,QAAT,EAAmB;AACf,WAAKW,gBAAL;AACH;;AACD,SAAKvC,SAAL,CAAe4C,KAAf,CAAqBC,OAArB,GAA+B,GAA/B;AACH;;AACDF,EAAAA,oBAAoB,CAAC1C,eAAD,EAAkB;AAClCrB,IAAAA,aAAa,CAAC/F,OAAd,CAAuBwG,QAAD,IAAc;AAChC,YAAMyD,gBAAgB,GAAI,aAAYzD,QAAS,EAA/C;;AACA,WAAKL,SAAL,CAAe+D,WAAf,CAA2B,KAAK/C,SAAhC,EAA2C8C,gBAA3C;;AACA,WAAK9D,SAAL,CAAe+D,WAAf,CAA2B,KAAKvC,OAAhC,EAAyCsC,gBAAzC;AACH,KAJD;AAKA,UAAME,aAAa,GAAI,aAAY/C,eAAgB,EAAnD;;AACA,SAAKjB,SAAL,CAAeiE,QAAf,CAAwB,KAAKjD,SAA7B,EAAwCgD,aAAxC;;AACA,SAAKhE,SAAL,CAAeiE,QAAf,CAAwB,KAAKzC,OAA7B,EAAsCwC,aAAtC;AACH;;AACDhC,EAAAA,aAAa,GAAG;AACZ,SAAK/B,KAAL,CAAWiE,iBAAX,CAA6B,MAAM;AAC/BlR,MAAAA,SAAS,CAAC,KAAK6O,gBAAL,CAAsBjN,aAAvB,EAAsC,QAAtC,CAAT,CACKuP,IADL,CACU7R,SAAS,CAAC,KAAKsO,SAAN,CADnB,EACqCrO,SAAS,CAAC,CAAD,EAAIsN,gBAAJ,CAD9C,EAEKuE,SAFL,CAEgBC,CAAD,IAAO;AAClB,cAAMC,IAAI,GAAGD,CAAC,CAACC,IAAF,IAAWD,CAAC,CAACE,YAAF,IAAkBF,CAAC,CAACE,YAAF,EAA1C;AACA,cAAM9F,SAAS,GAAG,CAAC6F,IAAD,IAASA,IAAI,CAAC/M,MAAL,KAAgB,CAAzB,GAA6B8M,CAAC,CAACG,MAAF,CAAS/F,SAAtC,GAAkD6F,IAAI,CAAC,CAAD,CAAJ,CAAQ7F,SAA5E;;AACA,aAAKgG,kBAAL,CAAwBhG,SAAxB;AACH,OAND;AAOH,KARD;AASH;;AACDwD,EAAAA,mBAAmB,GAAG;AAClB,QAAI,CAAC,KAAK7B,SAAV,EAAqB;AACjB;AACH;;AACD,SAAKH,KAAL,CAAWiE,iBAAX,CAA6B,MAAM;AAC/BjR,MAAAA,KAAK,CAACD,SAAS,CAAC,KAAKoN,SAAN,EAAiB,YAAjB,EAA+B;AAAEsE,QAAAA,OAAO,EAAE;AAAX,OAA/B,CAAV,EAA6D1R,SAAS,CAAC,KAAKoN,SAAN,EAAiB,WAAjB,EAA8B;AAAEsE,QAAAA,OAAO,EAAE;AAAX,OAA9B,CAAtE,CAAL,CAA6HP,IAA7H,CAAkI7R,SAAS,CAAC,KAAKsO,SAAN,CAA3I,EACKwD,SADL,CACeO,MAAM,IAAI,KAAKC,aAAL,CAAmBD,MAAnB,CADzB;AAEH,KAHD;AAIH;;AACDC,EAAAA,aAAa,CAACD,MAAD,EAAS;AAClB,QAAI,KAAKnD,OAAL,CAAaqD,QAAb,CAAsBF,MAAM,CAACH,MAA7B,KAAwC,KAAKxD,SAAL,CAAe6D,QAAf,CAAwBF,MAAM,CAACH,MAA/B,CAA5C,EAAoF;AAChF;AACH;;AACD,UAAMF,IAAI,GAAGK,MAAM,CAACL,IAAP,IAAgBK,MAAM,CAACJ,YAAP,IAAuBI,MAAM,CAACJ,YAAP,EAApD;;AACA,QAAII,MAAM,CAACH,MAAP,IAAiBG,MAAM,CAACH,MAAP,CAAcM,UAA/B,IAA6CR,IAA7C,IAAqDA,IAAI,CAAC,CAAD,CAAzD,IAAgE,KAAK9C,OAAL,CAAaqD,QAAb,CAAsBP,IAAI,CAAC,CAAD,CAA1B,CAApE,EAAoG;AAChG;AACH;;AACD,SAAKrE,KAAL,CAAW8E,GAAX,CAAe,MAAM,KAAKpE,YAAL,CAAkBqE,IAAlB,EAArB;AACH;;AACD3C,EAAAA,cAAc,CAACrL,KAAD,EAAQuL,WAAR,EAAqB;AAC/B,SAAKvL,KAAL,GAAaA,KAAK,IAAI,EAAtB;AACA,SAAK6J,iBAAL,GAAyB,KAAzB;AACA,SAAKxC,WAAL,GAAmBrH,KAAK,CAACO,MAAzB;;AACA,QAAI,KAAK+I,aAAT,EAAwB;AACpB,WAAK2E,iBAAL,CAAuB1C,WAAvB;AACH,KAFD,MAGK;AACD,WAAK2C,iBAAL;;AACA,WAAKC,YAAL,CAAkB5C,WAAlB;AACH;AACJ;;AACD4C,EAAAA,YAAY,CAAC5C,WAAD,EAAc;AACtB,SAAK/B,MAAL,CAAYwE,IAAZ,CAAiB,KAAKhO,KAAtB;;AACA,QAAIuL,WAAW,KAAK,KAApB,EAA2B;AACvB;AACH;;AACD,SAAKtC,KAAL,CAAWiE,iBAAX,CAA6B,MAAM;AAC/B/P,MAAAA,OAAO,CAACiR,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AACzB,cAAMrH,WAAW,GAAG,KAAK4D,gBAAL,CAAsBuB,YAA1C;;AACA,aAAKjD,aAAL,CAAmBd,aAAnB,CAAiC,CAAjC,EAAoCpB,WAApC;;AACA,aAAKwF,uBAAL;;AACA,aAAKT,QAAL,CAAc,KAAK3L,UAAnB,EAA+BmL,WAA/B;AACH,OALD;AAMH,KAPD;AAQH;;AACD0C,EAAAA,iBAAiB,CAAC1C,WAAD,EAAc;AAC3B,SAAKtC,KAAL,CAAWiE,iBAAX,CAA6B,MAAM;AAC/B,WAAKoB,kBAAL,GAA0BD,IAA1B,CAA+B,MAAM;AACjC,YAAI9C,WAAJ,EAAiB;AACb,eAAKgD,iBAAL,CAAuB,KAAKlE,YAA5B;;AACA,eAAKmC,uBAAL;AACH,SAHD,MAIK;AACD,eAAK+B,iBAAL;AACH;AACJ,OARD;AASH,KAVD;AAWH;;AACDd,EAAAA,kBAAkB,CAAChG,SAAD,EAAY;AAC1B,QAAI,KAAK6B,aAAT,EAAwB;AACpB,WAAKiF,iBAAL,CAAuB9G,SAAvB;AACH;;AACD,SAAKsC,mBAAL,GAA2BtC,SAA3B;;AACA,SAAK+G,gBAAL,CAAsB/G,SAAtB;AACH;;AACDgH,EAAAA,oBAAoB,CAACC,MAAD,EAAS;AACzB,QAAI,KAAK5E,mBAAT,EAA8B;AAC1B,WAAKY,eAAL,CAAqBkC,KAArB,CAA2B8B,MAA3B,GAAqC,GAAEA,MAAO,IAA9C;AACA,WAAK5E,mBAAL,GAA2B,KAA3B;AACH;AACJ;;AACDoE,EAAAA,iBAAiB,GAAG;AAChB,QAAI,CAAC,KAAKxD,eAAV,EAA2B;AACvB;AACH;;AACD,SAAKA,eAAL,CAAqBkC,KAArB,CAA2B8B,MAA3B,GAAqC,KAArC;AACH;;AACDtE,EAAAA,qBAAqB,GAAG;AACpB,SAAKN,mBAAL,GAA2B,IAA3B;AACH;;AACDyE,EAAAA,iBAAiB,CAAC9G,SAAS,GAAG,IAAb,EAAmB;AAChC,QAAIA,SAAS,IAAI,KAAKsC,mBAAL,KAA6BtC,SAA9C,EAAyD;AACrD;AACH;;AACDA,IAAAA,SAAS,GAAGA,SAAS,IAAI,KAAKmD,gBAAL,CAAsBnD,SAA/C;;AACA,UAAMkH,KAAK,GAAG,KAAKzF,aAAL,CAAmB/B,cAAnB,CAAkCM,SAAlC,EAA6C,KAAKJ,WAAlD,EAA+D,KAAKuH,YAApE,CAAd;;AACA,SAAKH,oBAAL,CAA0BE,KAAK,CAACnH,YAAhC;;AACA,SAAKsD,aAAL,CAAmB8B,KAAnB,CAAyBiC,SAAzB,GAAsC,cAAaF,KAAK,CAACzG,UAAW,KAApE;;AACA,SAAKe,KAAL,CAAW8E,GAAX,CAAe,MAAM;AACjB,WAAKvE,MAAL,CAAYwE,IAAZ,CAAiB,KAAKhO,KAAL,CAAW+D,KAAX,CAAiB4K,KAAK,CAAC3G,KAAvB,EAA8B2G,KAAK,CAAChH,GAApC,CAAjB;AACA,WAAK8B,MAAL,CAAYuE,IAAZ,CAAiB;AAAEhG,QAAAA,KAAK,EAAE2G,KAAK,CAAC3G,KAAf;AAAsBL,QAAAA,GAAG,EAAEgH,KAAK,CAAChH;AAAjC,OAAjB;AACH,KAHD;;AAIA,QAAI5K,SAAS,CAAC0K,SAAD,CAAT,IAAwB,KAAKsC,mBAAL,KAA6B,CAAzD,EAA4D;AACxD,WAAKa,gBAAL,CAAsBnD,SAAtB,GAAkCA,SAAlC;AACA,WAAKsC,mBAAL,GAA2BtC,SAA3B;AACH;AACJ;;AACD6G,EAAAA,kBAAkB,GAAG;AACjB,QAAI,KAAKpF,aAAL,CAAmBhC,UAAnB,CAA8BH,UAA9B,GAA2C,CAA3C,IAAgD,KAAKM,WAAL,KAAqB,CAAzE,EAA4E;AACxE,aAAOlK,OAAO,CAACiR,OAAR,CAAgB,KAAKlF,aAAL,CAAmBhC,UAAnC,CAAP;AACH;;AACD,UAAM,CAAC4H,KAAD,IAAU,KAAK9O,KAArB;AACA,SAAKwJ,MAAL,CAAYwE,IAAZ,CAAiB,CAACc,KAAD,CAAjB;AACA,WAAO3R,OAAO,CAACiR,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AAChC,YAAM5L,MAAM,GAAG,KAAKuH,SAAL,CAAeiC,aAAf,CAA8B,IAAG6C,KAAK,CAAC7J,MAAO,EAA9C,CAAf;;AACA,YAAM8J,YAAY,GAAGtM,MAAM,CAAC0J,YAA5B;AACA,WAAKzB,eAAL,CAAqBkC,KAArB,CAA2B8B,MAA3B,GAAqC,GAAEK,YAAY,GAAG,KAAK1H,WAAY,IAAvE;AACA,YAAML,WAAW,GAAG,KAAK4D,gBAAL,CAAsBuB,YAA1C;;AACA,WAAKjD,aAAL,CAAmBd,aAAnB,CAAiC2G,YAAjC,EAA+C/H,WAA/C;;AACA,aAAO,KAAKkC,aAAL,CAAmBhC,UAA1B;AACH,KAPM,CAAP;AAQH;;AACDsH,EAAAA,gBAAgB,CAAC/G,SAAD,EAAY;AACxB,QAAI,KAAKoC,iBAAL,IAA0BpC,SAAS,KAAK,CAA5C,EAA+C;AAC3C;AACH;;AACD,UAAMuH,OAAO,GAAG,KAAK1F,aAAL,GACZ,KAAKoB,eADO,GAEZ,KAAKI,aAFT;;AAGA,QAAIrD,SAAS,GAAG,KAAKuC,SAAL,CAAemC,YAA3B,IAA2C6C,OAAO,CAAC7C,YAAR,GAAuB,CAAtE,EAAyE;AACrE,WAAKlD,KAAL,CAAW8E,GAAX,CAAe,MAAM,KAAKrE,WAAL,CAAiBsE,IAAjB,EAArB;;AACA,WAAKnE,iBAAL,GAAyB,IAAzB;AACH;AACJ;;AACD4C,EAAAA,yBAAyB,CAACwC,UAAD,EAAa;AAClC,QAAI,KAAK5F,QAAL,KAAkB,MAAtB,EAA8B;AAC1B,aAAO,KAAKA,QAAZ;AACH;;AACD,UAAM6F,UAAU,GAAG,KAAK1E,OAAL,CAAa2E,qBAAb,EAAnB;;AACA,UAAM1H,SAAS,GAAG2H,QAAQ,CAACC,eAAT,CAAyB5H,SAAzB,IAAsC2H,QAAQ,CAACE,IAAT,CAAc7H,SAAtE;AACA,UAAMyE,SAAS,GAAGgD,UAAU,CAACzG,GAAX,GAAiB8G,MAAM,CAACC,WAA1C;AACA,UAAMd,MAAM,GAAGQ,UAAU,CAACR,MAA1B;AACA,UAAMe,cAAc,GAAGR,UAAU,CAACE,qBAAX,GAAmCT,MAA1D;;AACA,QAAIxC,SAAS,GAAGwC,MAAZ,GAAqBe,cAArB,GAAsChI,SAAS,GAAG2H,QAAQ,CAACC,eAAT,CAAyBlD,YAA/E,EAA6F;AACzF,aAAO,KAAP;AACH,KAFD,MAGK;AACD,aAAO,QAAP;AACH;AACJ;;AACDjB,EAAAA,eAAe,GAAG;AACd,QAAI,CAAC,KAAKU,QAAV,EAAoB;AAChB;AACH;;AACD,SAAK8D,OAAL,GAAeN,QAAQ,CAACnD,aAAT,CAAuB,KAAKL,QAA5B,CAAf;;AACA,QAAI,CAAC,KAAK8D,OAAV,EAAmB;AACf,YAAM,IAAIC,KAAJ,CAAW,qBAAoB,KAAK/D,QAAS,mCAA7C,CAAN;AACH;;AACD,SAAKgE,gBAAL;;AACA,SAAKF,OAAL,CAAaG,WAAb,CAAyB,KAAK7F,SAA9B;AACH;;AACD4F,EAAAA,gBAAgB,GAAG;AACf,UAAMpO,MAAM,GAAG,KAAKgJ,OAAL,CAAa2E,qBAAb,EAAf;;AACA,UAAMvL,MAAM,GAAG,KAAK8L,OAAL,CAAaP,qBAAb,EAAf;;AACA,UAAMW,UAAU,GAAGtO,MAAM,CAACuO,IAAP,GAAcnM,MAAM,CAACmM,IAAxC;AACA,SAAK/F,SAAL,CAAe4C,KAAf,CAAqBmD,IAArB,GAA4BD,UAAU,GAAG,IAAzC;AACA,SAAK9F,SAAL,CAAe4C,KAAf,CAAqBoD,KAArB,GAA6BxO,MAAM,CAACwO,KAAP,GAAe,IAA5C;AACA,SAAKhG,SAAL,CAAe4C,KAAf,CAAqBqD,QAArB,GAAgCzO,MAAM,CAACwO,KAAP,GAAe,IAA/C;AACH;;AACDzD,EAAAA,gBAAgB,GAAG;AACf,UAAM/K,MAAM,GAAG,KAAKgJ,OAAL,CAAa2E,qBAAb,EAAf;;AACA,UAAMvL,MAAM,GAAG,KAAK8L,OAAL,CAAaP,qBAAb,EAAf;;AACA,UAAMe,KAAK,GAAG1O,MAAM,CAACkN,MAArB;;AACA,QAAI,KAAKxE,gBAAL,KAA0B,KAA9B,EAAqC;AACjC,YAAMiG,YAAY,GAAGvM,MAAM,CAAC8E,MAAP,GAAgBlH,MAAM,CAACkH,MAA5C;AACA,WAAKsB,SAAL,CAAe4C,KAAf,CAAqBlE,MAArB,GAA8ByH,YAAY,GAAGD,KAAf,GAAuB,IAArD;AACA,WAAKlG,SAAL,CAAe4C,KAAf,CAAqBnE,GAArB,GAA2B,MAA3B;AACH,KAJD,MAKK,IAAI,KAAKyB,gBAAL,KAA0B,QAA9B,EAAwC;AACzC,YAAMgC,SAAS,GAAG1K,MAAM,CAACiH,GAAP,GAAa7E,MAAM,CAAC6E,GAAtC;AACA,WAAKuB,SAAL,CAAe4C,KAAf,CAAqBnE,GAArB,GAA2ByD,SAAS,GAAGgE,KAAZ,GAAoB,IAA/C;AACA,WAAKlG,SAAL,CAAe4C,KAAf,CAAqBlE,MAArB,GAA8B,MAA9B;AACH;AACJ;;AACDyC,EAAAA,uBAAuB,GAAG;AACtB,SAAKlC,KAAL,CAAWiE,iBAAX,CAA6B,MAAM;AAC/BlR,MAAAA,SAAS,CAAC,KAAKgO,SAAN,EAAiB,WAAjB,CAAT,CACKmD,IADL,CACU7R,SAAS,CAAC,KAAKsO,SAAN,CADnB,EAEKwD,SAFL,CAEgBgD,KAAD,IAAW;AACtB,cAAM5C,MAAM,GAAG4C,KAAK,CAAC5C,MAArB;;AACA,YAAIA,MAAM,CAAC6C,OAAP,KAAmB,OAAvB,EAAgC;AAC5B;AACH;;AACDD,QAAAA,KAAK,CAACE,cAAN;AACH,OARD;AASH,KAVD;AAWH;;AAzT0B;;AA2T/BvH,wBAAwB,CAAChL,IAAzB;AAAA,mBAAqHgL,wBAArH,EAjtDuGhP,EAitDvG,mBAA+JA,EAAE,CAACwW,SAAlK,GAjtDuGxW,EAitDvG,mBAAwLA,EAAE,CAACyW,MAA3L,GAjtDuGzW,EAitDvG,mBAA8M8M,sBAA9M,GAjtDuG9M,EAitDvG,mBAAiPA,EAAE,CAACK,UAApP,GAjtDuGL,EAitDvG,mBAA2QoC,QAA3Q;AAAA;;AACA4M,wBAAwB,CAAC0H,IAAzB,kBAltDuG1W,EAktDvG;AAAA,QAAyGgP,wBAAzG;AAAA;AAAA;AAAA;AAltDuGhP,MAAAA,EAktDvG,qBAA8nBK,UAA9nB;AAltDuGL,MAAAA,EAktDvG,qBAA2vBK,UAA3vB;AAltDuGL,MAAAA,EAktDvG,qBAA03BK,UAA13B;AAAA;;AAAA;AAAA;;AAltDuGL,MAAAA,EAktDvG,qBAltDuGA,EAktDvG;AAltDuGA,MAAAA,EAktDvG,qBAltDuGA,EAktDvG;AAltDuGA,MAAAA,EAktDvG,qBAltDuGA,EAktDvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAltDuGA,EAktDvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAltDuGA,MAAAA,EAktDvG;AAltDuGA,MAAAA,EAmtD/F,uEADR;AAltDuGA,MAAAA,EAstD/F,+BAJR;AAltDuGA,MAAAA,EAutD3F,6BALZ;AAltDuGA,MAAAA,EAwtD3F,kCANZ;AAltDuGA,MAAAA,EAytDvF,gBAPhB;AAltDuGA,MAAAA,EA0tD3F,eARZ;AAltDuGA,MAAAA,EA2tD/F,eATR;AAltDuGA,MAAAA,EA4tD/F,uEAVR;AAAA;;AAAA;AAltDuGA,MAAAA,EAmtDzF,uCADd;AAltDuGA,MAAAA,EAutD7E,aAL1B;AAltDuGA,MAAAA,EAutD7E,gDAL1B;AAltDuGA,MAAAA,EAwtD7E,aAN1B;AAltDuGA,MAAAA,EAwtD7E,yEAN1B;AAltDuGA,MAAAA,EA4tDzF,aAVd;AAltDuGA,MAAAA,EA4tDzF,uCAVd;AAAA;AAAA;AAAA,eAa4CmC,EAAE,CAACwU,IAb/C,EAa6HxU,EAAE,CAACyU,gBAbhI;AAAA;AAAA;AAAA;;AAcA;AAAA,qDAhuDuG5W,EAguDvG,mBAA2FgP,wBAA3F,EAAiI,CAAC;AACtH9K,IAAAA,IAAI,EAAE5D,SADgH;AAEtH6D,IAAAA,IAAI,EAAE,CAAC;AACC0S,MAAAA,eAAe,EAAEtW,uBAAuB,CAACuW,MAD1C;AAECC,MAAAA,aAAa,EAAEvW,iBAAiB,CAACwW,IAFlC;AAGC5S,MAAAA,QAAQ,EAAE,mBAHX;AAICE,MAAAA,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBmB,KAAD;AAFgH,GAAD,CAAjI,EAqB4B,YAAY;AAAE,WAAO,CAAC;AAAEJ,MAAAA,IAAI,EAAElE,EAAE,CAACwW;AAAX,KAAD,EAAyB;AAAEtS,MAAAA,IAAI,EAAElE,EAAE,CAACyW;AAAX,KAAzB,EAA8C;AAAEvS,MAAAA,IAAI,EAAE4I;AAAR,KAA9C,EAAgF;AAAE5I,MAAAA,IAAI,EAAElE,EAAE,CAACK;AAAX,KAAhF,EAAyG;AAAE6D,MAAAA,IAAI,EAAEjB,SAAR;AAAmBgU,MAAAA,UAAU,EAAE,CAAC;AACtK/S,QAAAA,IAAI,EAAEzD;AADgK,OAAD,EAEtK;AACCyD,QAAAA,IAAI,EAAExD,MADP;AAECyD,QAAAA,IAAI,EAAE,CAAC/B,QAAD;AAFP,OAFsK;AAA/B,KAAzG,CAAP;AAKlB,GA1BxB,EA0B0C;AAAE6D,IAAAA,KAAK,EAAE,CAAC;AACpC/B,MAAAA,IAAI,EAAEhE;AAD8B,KAAD,CAAT;AAE1BmG,IAAAA,UAAU,EAAE,CAAC;AACbnC,MAAAA,IAAI,EAAEhE;AADO,KAAD,CAFc;AAI1BoP,IAAAA,QAAQ,EAAE,CAAC;AACXpL,MAAAA,IAAI,EAAEhE;AADK,KAAD,CAJgB;AAM1B2R,IAAAA,QAAQ,EAAE,CAAC;AACX3N,MAAAA,IAAI,EAAEhE;AADK,KAAD,CANgB;AAQ1B2U,IAAAA,YAAY,EAAE,CAAC;AACf3Q,MAAAA,IAAI,EAAEhE;AADS,KAAD,CARY;AAU1BqP,IAAAA,aAAa,EAAE,CAAC;AAChBrL,MAAAA,IAAI,EAAEhE;AADU,KAAD,CAVW;AAY1BgX,IAAAA,cAAc,EAAE,CAAC;AACjBhT,MAAAA,IAAI,EAAEhE;AADW,KAAD,CAZU;AAc1BiX,IAAAA,cAAc,EAAE,CAAC;AACjBjT,MAAAA,IAAI,EAAEhE;AADW,KAAD,CAdU;AAgB1BsP,IAAAA,WAAW,EAAE,CAAC;AACdtL,MAAAA,IAAI,EAAEhE;AADQ,KAAD,CAhBa;AAkB1BuP,IAAAA,MAAM,EAAE,CAAC;AACTvL,MAAAA,IAAI,EAAEvD;AADG,KAAD,CAlBkB;AAoB1B+O,IAAAA,MAAM,EAAE,CAAC;AACTxL,MAAAA,IAAI,EAAEvD;AADG,KAAD,CApBkB;AAsB1BgP,IAAAA,WAAW,EAAE,CAAC;AACdzL,MAAAA,IAAI,EAAEvD;AADQ,KAAD,CAtBa;AAwB1BiP,IAAAA,YAAY,EAAE,CAAC;AACf1L,MAAAA,IAAI,EAAEvD;AADS,KAAD,CAxBY;AA0B1BqQ,IAAAA,iBAAiB,EAAE,CAAC;AACpB9M,MAAAA,IAAI,EAAEtD,SADc;AAEpBuD,MAAAA,IAAI,EAAE,CAAC,SAAD,EAAY;AAAEiT,QAAAA,IAAI,EAAE/W,UAAR;AAAoBgX,QAAAA,MAAM,EAAE;AAA5B,OAAZ;AAFc,KAAD,CA1BO;AA6B1BvG,IAAAA,gBAAgB,EAAE,CAAC;AACnB5M,MAAAA,IAAI,EAAEtD,SADa;AAEnBuD,MAAAA,IAAI,EAAE,CAAC,QAAD,EAAW;AAAEiT,QAAAA,IAAI,EAAE/W,UAAR;AAAoBgX,QAAAA,MAAM,EAAE;AAA5B,OAAX;AAFa,KAAD,CA7BQ;AAgC1BzG,IAAAA,iBAAiB,EAAE,CAAC;AACpB1M,MAAAA,IAAI,EAAEtD,SADc;AAEpBuD,MAAAA,IAAI,EAAE,CAAC,SAAD,EAAY;AAAEiT,QAAAA,IAAI,EAAE/W,UAAR;AAAoBgX,QAAAA,MAAM,EAAE;AAA5B,OAAZ;AAFc,KAAD;AAhCO,GA1B1C;AAAA;;AA+DA,MAAMC,iBAAN,CAAwB;AACpB9T,EAAAA,WAAW,CAAC+T,UAAD,EAAa;AACpB,SAAKA,UAAL,GAAkBA,UAAlB;AACA,SAAKC,YAAL,GAAoB,IAAIxV,OAAJ,EAApB;AACA,SAAKyV,SAAL,GAAiB,KAAjB;AACH;;AACW,MAAR1Q,QAAQ,GAAG;AAAE,WAAO,KAAK0Q,SAAZ;AAAwB;;AAC7B,MAAR1Q,QAAQ,CAACnE,KAAD,EAAQ;AAAE,SAAK6U,SAAL,GAAiB,KAAKC,WAAL,CAAiB9U,KAAjB,CAAjB;AAA2C;;AACxD,MAAL0F,KAAK,GAAG;AACR,WAAO,CAAC,KAAKiP,UAAL,CAAgB1T,aAAhB,CAA8B8T,WAA9B,IAA6C,EAA9C,EAAkDC,IAAlD,EAAP;AACH;;AACDjU,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,QAAIA,OAAO,CAACmD,QAAZ,EAAsB;AAClB,WAAKyQ,YAAL,CAAkB9F,IAAlB,CAAuB;AACnB9O,QAAAA,KAAK,EAAE,KAAKA,KADO;AAEnBmE,QAAAA,QAAQ,EAAE,KAAK0Q;AAFI,OAAvB;AAIH;AACJ;;AACDI,EAAAA,kBAAkB,GAAG;AACjB,QAAI,KAAKvP,KAAL,KAAe,KAAKwP,cAAxB,EAAwC;AACpC,WAAKA,cAAL,GAAsB,KAAKxP,KAA3B;AACA,WAAKkP,YAAL,CAAkB9F,IAAlB,CAAuB;AACnB9O,QAAAA,KAAK,EAAE,KAAKA,KADO;AAEnBmE,QAAAA,QAAQ,EAAE,KAAK0Q,SAFI;AAGnBnP,QAAAA,KAAK,EAAE,KAAKiP,UAAL,CAAgB1T,aAAhB,CAA8BC;AAHlB,OAAvB;AAKH;AACJ;;AACD2N,EAAAA,WAAW,GAAG;AACV,SAAK+F,YAAL,CAAkB7F,QAAlB;AACH;;AACD+F,EAAAA,WAAW,CAAC9U,KAAD,EAAQ;AACf,WAAOA,KAAK,IAAI,IAAT,IAAkB,GAAEA,KAAM,EAAT,KAAe,OAAvC;AACH;;AAlCmB;;AAoCxB0U,iBAAiB,CAACtT,IAAlB;AAAA,mBAA8GsT,iBAA9G,EAn0DuGtX,EAm0DvG,mBAAiJA,EAAE,CAACK,UAApJ;AAAA;;AACAiX,iBAAiB,CAACZ,IAAlB,kBAp0DuG1W,EAo0DvG;AAAA,QAAkGsX,iBAAlG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAp0DuGtX,EAo0DvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAp0DuGA,MAAAA,EAo0DvG;AAp0DuGA,MAAAA,EAo0DqI,gBAA5O;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAr0DuGA,EAq0DvG,mBAA2FsX,iBAA3F,EAA0H,CAAC;AAC/GpT,IAAAA,IAAI,EAAE5D,SADyG;AAE/G6D,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,WADX;AAECyS,MAAAA,eAAe,EAAEtW,uBAAuB,CAACuW,MAF1C;AAGCxS,MAAAA,QAAQ,EAAG;AAHZ,KAAD;AAFyG,GAAD,CAA1H,EAO4B,YAAY;AAAE,WAAO,CAAC;AAAEJ,MAAAA,IAAI,EAAElE,EAAE,CAACK;AAAX,KAAD,CAAP;AAAmC,GAP7E,EAO+F;AAAEuC,IAAAA,KAAK,EAAE,CAAC;AACzFsB,MAAAA,IAAI,EAAEhE;AADmF,KAAD,CAAT;AAE/E6G,IAAAA,QAAQ,EAAE,CAAC;AACX7C,MAAAA,IAAI,EAAEhE;AADK,KAAD;AAFqE,GAP/F;AAAA;;AAaA,MAAM6X,cAAN,CAAqB;AACjBvU,EAAAA,WAAW,GAAG;AACV,SAAKwU,YAAL,GAAoB,gBAApB;AACA,SAAKC,gBAAL,GAAwB,gBAAxB;AACA,SAAKC,UAAL,GAAkB,UAAlB;AACA,SAAKC,WAAL,GAAmB,YAAnB;AACA,SAAKC,YAAL,GAAoB,WAApB;AACA,SAAKC,oBAAL,GAA4B,IAA5B;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,UAAL,GAAkB,WAAlB;AACH;;AAVgB;;AAYrBR,cAAc,CAAC/T,IAAf;AAAA,mBAA2G+T,cAA3G;AAAA;;AACAA,cAAc,CAACnJ,KAAf,kBA/1DuG5O,EA+1DvG;AAAA,SAA+G+X,cAA/G;AAAA,WAA+GA,cAA/G;AAAA,cAA2I;AAA3I;;AACA;AAAA,qDAh2DuG/X,EAg2DvG,mBAA2F+X,cAA3F,EAAuH,CAAC;AAC5G7T,IAAAA,IAAI,EAAE/D,UADsG;AAE5GgE,IAAAA,IAAI,EAAE,CAAC;AAAEqU,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFsG,GAAD,CAAvH;AAAA;;AAKA,MAAMC,cAAN,CAAqB;AACjBC,EAAAA,IAAI,CAACC,OAAD,EAAU;AACVC,IAAAA,OAAO,CAACF,IAAR,CAAaC,OAAb;AACH;;AAHgB;;AAKrBF,cAAc,CAACzU,IAAf;AAAA,mBAA2GyU,cAA3G;AAAA;;AACAA,cAAc,CAAC7J,KAAf,kBA32DuG5O,EA22DvG;AAAA,SAA+GyY,cAA/G;AAAA,WAA+GA,cAA/G;AAAA,cAA2I;AAA3I;;AACA;AAAA,qDA52DuGzY,EA42DvG,mBAA2FyY,cAA3F,EAAuH,CAAC;AAC5GvU,IAAAA,IAAI,EAAE/D,UADsG;AAE5GgE,IAAAA,IAAI,EAAE,CAAC;AAAEqU,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFsG,GAAD,CAAvH;AAAA;;AAKA,MAAMK,uBAAuB,GAAG,IAAIhY,cAAJ,CAAmB,2BAAnB,CAAhC;;AACA,MAAMiY,iBAAN,CAAwB;AACpBtV,EAAAA,WAAW,CAACuV,OAAD,EAAUC,SAAV,EAAqBC,MAArB,EAA6BC,iBAA7B,EAAgD9J,WAAhD,EAA6D+J,GAA7D,EAAkEC,QAAlE,EAA4E;AACnF,SAAKL,OAAL,GAAeA,OAAf;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKE,GAAL,GAAWA,GAAX;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,gBAAL,GAAwB,MAAxB;AACA,SAAKC,OAAL,GAAe,KAAf;AACA,SAAKC,aAAL,GAAqB,IAArB;AACA,SAAKjT,YAAL,GAAoB,KAApB;AACA,SAAKkT,WAAL,GAAmB,KAAnB;AACA,SAAK5E,YAAL,GAAoB,CAApB;AACA,SAAKnI,eAAL,GAAuB,KAAvB;AACA,SAAK9E,sBAAL,GAA8B,IAA9B;AACA,SAAKyB,QAAL,GAAgB,IAAhB;AACA,SAAKqQ,SAAL,GAAiB,IAAjB;AACA,SAAKC,gBAAL,GAAwB,IAAxB;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKC,UAAL,GAAkB,EAAlB;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKC,oBAAL,GAA4B,IAA5B;AACA,SAAKC,aAAL,GAAqB,CAArB;AACA,SAAKC,kBAAL,GAA0B,KAA1B;;AACA,SAAKC,SAAL,GAAkBC,CAAD,IAAO,IAAxB;;AACA,SAAKzT,QAAL,GAAgB,KAAhB;AACA,SAAK0T,MAAL,GAAc,KAAd;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,MAAL,GAAc,KAAd,CA7BmF,CA8BnF;;AACA,SAAKC,SAAL,GAAiB,IAAIpa,YAAJ,EAAjB;AACA,SAAKqa,UAAL,GAAkB,IAAIra,YAAJ,EAAlB;AACA,SAAKsa,WAAL,GAAmB,IAAIta,YAAJ,EAAnB;AACA,SAAKua,SAAL,GAAiB,IAAIva,YAAJ,EAAjB;AACA,SAAKwa,UAAL,GAAkB,IAAIxa,YAAJ,EAAlB;AACA,SAAKya,WAAL,GAAmB,IAAIza,YAAJ,EAAnB;AACA,SAAK0a,UAAL,GAAkB,IAAI1a,YAAJ,EAAlB;AACA,SAAK2a,QAAL,GAAgB,IAAI3a,YAAJ,EAAhB;AACA,SAAK4a,WAAL,GAAmB,IAAI5a,YAAJ,EAAnB;AACA,SAAKsP,MAAL,GAAc,IAAItP,YAAJ,EAAd;AACA,SAAKuP,WAAL,GAAmB,IAAIvP,YAAJ,EAAnB;AACA,SAAK6a,eAAL,GAAuB,IAAvB;AACA,SAAKC,aAAL,GAAqB,EAArB;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKhQ,UAAL,GAAkBlG,KAAK,EAAvB;AACA,SAAKtC,UAAL,GAAkB,IAAlB;AACA,SAAKmD,MAAL,GAAc,EAAd;AACA,SAAKsV,aAAL,GAAqB,OAArB;AACA,SAAKC,YAAL,GAAoB,EAApB;AACA,SAAKC,YAAL,GAAoB,KAApB;AACA,SAAKzL,SAAL,GAAiB,IAAI7N,OAAJ,EAAjB;AACA,SAAKuZ,UAAL,GAAkB,IAAIvZ,OAAJ,EAAlB;;AACA,SAAKwZ,SAAL,GAAkBrB,CAAD,IAAO,CAAG,CAA3B;;AACA,SAAKsB,UAAL,GAAkB,MAAM,CAAG,CAA3B;;AACA,SAAKC,SAAL,GAAkB5U,IAAD,IAAU;AACvB,YAAM4B,MAAM,GAAG,KAAKtC,aAAL,CAAmBoC,IAAnB,CAAwB0B,CAAC,IAAIA,CAAC,CAACtH,KAAF,KAAYkE,IAAzC,CAAf;AACA,WAAKgB,QAAL,CAAcY,MAAd;AACH,KAHD;;AAIA,SAAKiT,aAAL,GAAqB,CAACxB,CAAD,EAAIrT,IAAJ,KAAa;AAC9B,UAAI,KAAK4S,SAAT,EAAoB;AAChB,eAAO,KAAKA,SAAL,CAAe5S,IAAI,CAAClE,KAApB,CAAP;AACH;;AACD,aAAOkE,IAAP;AACH,KALD;;AAMA,SAAK8U,kBAAL,CAAwB3C,MAAxB;;AACA,SAAK4C,SAAL,GAAiB,IAAIlW,SAAJ,CAAc,IAAd,EAAoBuT,iBAAiB,EAArC,CAAjB;AACA,SAAKzV,OAAL,GAAe2L,WAAW,CAACvL,aAA3B;AACH;;AACQ,MAALoC,KAAK,GAAG;AAAE,WAAO,KAAKH,MAAZ;AAAqB;;AAE1B,MAALG,KAAK,CAACrD,KAAD,EAAQ;AACb,QAAIA,KAAK,KAAK,IAAd,EAAoB;AAChBA,MAAAA,KAAK,GAAG,EAAR;AACH;;AACD,SAAKkZ,aAAL,GAAqB,IAArB;AACA,SAAKhW,MAAL,GAAclD,KAAd;AACH;;AAEc,MAAXsF,WAAW,GAAG;AAAE,WAAO,KAAK6T,YAAZ;AAA2B;;AAChC,MAAX7T,WAAW,CAAC8T,EAAD,EAAK;AAChB,QAAIA,EAAE,KAAK/Y,SAAP,IAAoB+Y,EAAE,KAAK,IAA3B,IAAmC,CAAC3Y,UAAU,CAAC2Y,EAAD,CAAlD,EAAwD;AACpD,YAAMpG,KAAK,CAAC,mCAAD,CAAX;AACH;;AACD,SAAKmG,YAAL,GAAoBC,EAApB;AACH;;AACmB,MAAhBC,gBAAgB,GAAG;AACnB,QAAIjZ,SAAS,CAAC,KAAKkZ,iBAAN,CAAb,EAAuC;AACnC,aAAO,KAAKA,iBAAZ;AACH,KAFD,MAGK,IAAIlZ,SAAS,CAAC,KAAKiW,MAAL,CAAYgD,gBAAb,CAAb,EAA6C;AAC9C,aAAO,KAAKhD,MAAL,CAAYgD,gBAAnB;AACH;;AACD,WAAO,KAAKzC,aAAZ;AACH;;AAEmB,MAAhByC,gBAAgB,CAACrZ,KAAD,EAAQ;AACxB,SAAKsZ,iBAAL,GAAyBtZ,KAAzB;AACH;;AAEW,MAARmE,QAAQ,GAAG;AAAE,WAAO,KAAK+S,QAAL,IAAiB,KAAKrC,SAA7B;AAAyC;;AAE9C,MAAR0E,QAAQ,GAAG;AAAE,WAAQ,CAAC,CAAC,KAAKhB,UAAP,IAAqB,KAAKd,UAA1B,IAAwC,KAAKiB,YAArD;AAAqE;;AAE5E,MAANc,MAAM,GAAG;AAAE,WAAO,CAAC,KAAK1V,QAAb;AAAwB;;AAEhB,MAAnB2V,mBAAmB,GAAG;AACtB,WAAO,KAAKpC,kBAAL,IAA2B,CAAC,KAAKvT,QAAxC;AACH;;AACgB,MAAbN,aAAa,GAAG;AAChB,WAAO,KAAKyV,SAAL,CAAezV,aAAtB;AACH;;AACiB,MAAdkW,cAAc,GAAG;AACjB,WAAO,KAAKlW,aAAL,CAAmBvE,GAAnB,CAAuBqI,CAAC,IAAIA,CAAC,CAACtH,KAA9B,CAAP;AACH;;AACW,MAAR2Z,QAAQ,GAAG;AACX,WAAO,KAAKnW,aAAL,CAAmBI,MAAnB,GAA4B,CAAnC;AACH;;AACuB,MAApBgW,oBAAoB,GAAG;AACvB,QAAI,KAAKC,aAAT,EAAwB;AACpB,aAAO,KAAKA,aAAL,CAAmBvM,eAA1B;AACH;;AACD,WAAOjN,SAAP;AACH;;AACDuN,EAAAA,QAAQ,GAAG;AACP,SAAKkM,iBAAL;;AACA,SAAKC,mBAAL;AACH;;AACDhZ,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,QAAIA,OAAO,CAAC8C,QAAZ,EAAsB;AAClB,WAAKmV,SAAL,CAAelU,aAAf;AACH;;AACD,QAAI/D,OAAO,CAACqC,KAAZ,EAAmB;AACf,WAAK2W,SAAL,CAAehZ,OAAO,CAACqC,KAAR,CAAcsL,YAAd,IAA8B,EAA7C;AACH;;AACD,QAAI3N,OAAO,CAAC2W,MAAZ,EAAoB;AAChB,WAAKsC,WAAL,GAAmB7Z,SAAS,CAACY,OAAO,CAAC2W,MAAR,CAAehJ,YAAhB,CAA5B;AACH;AACJ;;AACDuL,EAAAA,eAAe,GAAG;AACd,QAAI,CAAC,KAAKhB,aAAV,EAAyB;AACrB,WAAKnZ,UAAL,GAAkB,KAAlB;;AACA,WAAKoa,sBAAL;AACH;;AACD,QAAI/Z,SAAS,CAAC,KAAKgW,SAAN,CAAb,EAA+B;AAC3B,WAAKgE,KAAL;AACH;AACJ;;AACDvL,EAAAA,WAAW,GAAG;AACV,SAAK5B,SAAL,CAAe6B,IAAf;;AACA,SAAK7B,SAAL,CAAe8B,QAAf;AACH;;AACDsL,EAAAA,aAAa,CAACrJ,MAAD,EAAS;AAClB,UAAMsJ,OAAO,GAAGrQ,OAAO,CAAC+G,MAAM,CAACuJ,KAAR,CAAvB;;AACA,QAAID,OAAJ,EAAa;AACT,UAAI,KAAKhD,SAAL,CAAetG,MAAf,MAA2B,KAA/B,EAAsC;AAClC;AACH;;AACD,WAAKwJ,aAAL,CAAmBxJ,MAAnB;AACH,KALD,MAMK,IAAIA,MAAM,CAACrK,GAAP,IAAcqK,MAAM,CAACrK,GAAP,CAAW/C,MAAX,KAAsB,CAAxC,EAA2C;AAC5C,WAAK+U,UAAL,CAAgB7J,IAAhB,CAAqBkC,MAAM,CAACrK,GAAP,CAAWJ,iBAAX,EAArB;AACH;AACJ;;AACDiU,EAAAA,aAAa,CAACxJ,MAAD,EAAS;AAClB,YAAQA,MAAM,CAACuJ,KAAf;AACI,WAAKtQ,OAAO,CAACwQ,SAAb;AACI,aAAKC,gBAAL,CAAsB1J,MAAtB;;AACA;;AACJ,WAAK/G,OAAO,CAAC0Q,OAAb;AACI,aAAKC,cAAL,CAAoB5J,MAApB;;AACA;;AACJ,WAAK/G,OAAO,CAAC4Q,KAAb;AACI,aAAKC,YAAL,CAAkB9J,MAAlB;;AACA;;AACJ,WAAK/G,OAAO,CAAC8Q,KAAb;AACI,aAAKC,YAAL,CAAkBhK,MAAlB;;AACA;;AACJ,WAAK/G,OAAO,CAACgR,GAAb;AACI,aAAKC,UAAL,CAAgBlK,MAAhB;;AACA;;AACJ,WAAK/G,OAAO,CAACkR,GAAb;AACI,aAAKC,KAAL;AACApK,QAAAA,MAAM,CAAC2C,cAAP;AACA;;AACJ,WAAK1J,OAAO,CAACoR,SAAb;AACI,aAAKC,gBAAL;;AACA;AAtBR;AAwBH;;AACDC,EAAAA,eAAe,CAACvK,MAAD,EAAS;AACpB,UAAMH,MAAM,GAAGG,MAAM,CAACH,MAAtB;;AACA,QAAIA,MAAM,CAAC6C,OAAP,KAAmB,OAAvB,EAAgC;AAC5B1C,MAAAA,MAAM,CAAC2C,cAAP;AACH;;AACD,QAAI9C,MAAM,CAAC2K,SAAP,CAAiBtK,QAAjB,CAA0B,kBAA1B,CAAJ,EAAmD;AAC/C,WAAKuK,gBAAL;AACA;AACH;;AACD,QAAI5K,MAAM,CAAC2K,SAAP,CAAiBtK,QAAjB,CAA0B,kBAA1B,CAAJ,EAAmD;AAC/C,WAAKwK,gBAAL;AACA;AACH;;AACD,QAAI7K,MAAM,CAAC2K,SAAP,CAAiBtK,QAAjB,CAA0B,eAA1B,CAAJ,EAAgD;AAC5C;AACH;;AACD,QAAI,CAAC,KAAKyK,OAAV,EAAmB;AACf,WAAKvB,KAAL;AACH;;AACD,QAAI,KAAK3C,UAAT,EAAqB;AACjB,WAAKmE,IAAL;AACH,KAFD,MAGK;AACD,WAAKC,MAAL;AACH;AACJ;;AACDH,EAAAA,gBAAgB,GAAG;AACf,QAAI,KAAK/D,MAAT,EAAiB;AACb,WAAKyD,KAAL;AACH,KAFD,MAGK;AACD,WAAKQ,IAAL;AACH;AACJ;;AACDH,EAAAA,gBAAgB,GAAG;AACf,QAAI,KAAK9B,QAAT,EAAmB;AACf,WAAKV,SAAL,CAAelU,aAAf,CAA6B,IAA7B;;AACA,WAAK+W,cAAL;AACH;;AACD,SAAKC,YAAL;;AACA,SAAK3B,KAAL;AACA,SAAKlC,UAAL,CAAgB7G,IAAhB;;AACA,SAAK2K,mBAAL;AACH;;AACDC,EAAAA,UAAU,GAAG;AACT,QAAI,CAAC,KAAKvE,SAAV,EAAqB;AACjB;AACH;;AACD,SAAKuB,SAAL,CAAelU,aAAf;;AACA,SAAK+W,cAAL;AACH;;AACDI,EAAAA,UAAU,CAAClc,KAAD,EAAQ;AACd,SAAKiZ,SAAL,CAAelU,aAAf;;AACA,SAAKoX,iBAAL,CAAuBnc,KAAvB;;AACA,SAAKuW,GAAL,CAAS6F,YAAT;AACH;;AACDC,EAAAA,gBAAgB,CAACjD,EAAD,EAAK;AACjB,SAAKR,SAAL,GAAiBQ,EAAjB;AACH;;AACDkD,EAAAA,iBAAiB,CAAClD,EAAD,EAAK;AAClB,SAAKP,UAAL,GAAkBO,EAAlB;AACH;;AACDmD,EAAAA,gBAAgB,CAACC,KAAD,EAAQ;AACpB,SAAK3H,SAAL,GAAiB2H,KAAjB;;AACA,SAAKjG,GAAL,CAAS6F,YAAT;AACH;;AACDP,EAAAA,MAAM,GAAG;AACL,QAAI,CAAC,KAAKlE,MAAV,EAAkB;AACd,WAAKiE,IAAL;AACH,KAFD,MAGK;AACD,WAAKR,KAAL;AACH;AACJ;;AACDQ,EAAAA,IAAI,GAAG;AACH,QAAI,KAAKzX,QAAL,IAAiB,KAAKwT,MAAtB,IAAgC,KAAKsB,SAAL,CAAepV,gBAA/C,IAAmE,KAAKoW,WAA5E,EAAyF;AACrF;AACH;;AACD,QAAI,CAAC,KAAKwC,YAAN,IAAsB,CAAC,KAAKjF,MAA5B,IAAsC,KAAKyB,SAAL,CAAevV,eAAzD,EAA0E;AACtE;AACH;;AACD,SAAKiU,MAAL,GAAc,IAAd;AACA,SAAKsB,SAAL,CAAepR,qBAAf,CAAqC,KAAK4O,SAA1C;AACA,SAAKsB,SAAL,CAAe1G,IAAf;;AACA,QAAI,CAAC,KAAKkH,UAAV,EAAsB;AAClB,WAAK6B,KAAL;AACH;;AACD,SAAKsC,aAAL;AACH;;AACDtB,EAAAA,KAAK,GAAG;AACJ,QAAI,CAAC,KAAKzD,MAAN,IAAgB,KAAKsC,WAAzB,EAAsC;AAClC;AACH;;AACD,SAAKtC,MAAL,GAAc,KAAd;AACA,SAAKe,YAAL,GAAoB,KAApB;;AACA,QAAI,CAAC,KAAKe,mBAAV,EAA+B;AAC3B,WAAKsC,YAAL;AACH,KAFD,MAGK;AACD,WAAK9C,SAAL,CAAe7S,kBAAf;AACH;;AACD,SAAK6S,SAAL,CAAe1R,UAAf;;AACA,SAAKsR,UAAL;;AACA,SAAKb,UAAL,CAAgB3G,IAAhB;;AACA,SAAKkF,GAAL,CAAS6F,YAAT;AACH;;AACDO,EAAAA,UAAU,CAACzY,IAAD,EAAO;AACb,QAAI,CAACA,IAAD,IAASA,IAAI,CAACC,QAAd,IAA0B,KAAKA,QAAnC,EAA6C;AACzC;AACH;;AACD,QAAI,KAAKL,QAAL,IAAiBI,IAAI,CAACY,QAA1B,EAAoC;AAChC,WAAKI,QAAL,CAAchB,IAAd;AACH,KAFD,MAGK;AACD,WAAKW,MAAL,CAAYX,IAAZ;AACH;;AACD,QAAI,KAAKuV,mBAAT,EAA8B;AAC1B,WAAKmD,uBAAL;AACH;;AACD,SAAKZ,mBAAL;AACH;;AACDnX,EAAAA,MAAM,CAACX,IAAD,EAAO;AACT,QAAI,CAACA,IAAI,CAACY,QAAV,EAAoB;AAChB,WAAKmU,SAAL,CAAepU,MAAf,CAAsBX,IAAtB;;AACA,UAAI,KAAKmV,gBAAL,IAAyB,CAAC,KAAKI,mBAAnC,EAAwD;AACpD,aAAKsC,YAAL;AACH;;AACD,WAAKD,cAAL;;AACA,UAAI,KAAKhY,QAAT,EAAmB;AACf,aAAKqU,QAAL,CAAc9G,IAAd,CAAmBnN,IAAI,CAAClE,KAAxB;AACH;AACJ;;AACD,QAAI,KAAK4W,aAAL,IAAsB,KAAKqC,SAAL,CAAevV,eAAzC,EAA0D;AACtD,WAAK0X,KAAL;AACH;AACJ;;AACDhB,EAAAA,KAAK,GAAG;AACJ,SAAKyC,WAAL,CAAiB5b,aAAjB,CAA+BmZ,KAA/B;AACH;;AACD0C,EAAAA,IAAI,GAAG;AACH,SAAKD,WAAL,CAAiB5b,aAAjB,CAA+B6b,IAA/B;AACH;;AACD5X,EAAAA,QAAQ,CAAChB,IAAD,EAAO;AACX,QAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,SAAK+U,SAAL,CAAe/T,QAAf,CAAwBhB,IAAxB;AACA,SAAKkW,KAAL;;AACA,SAAK0B,cAAL;;AACA,SAAK1D,WAAL,CAAiB/G,IAAjB,CAAsBnN,IAAtB;AACH;;AACD6Y,EAAAA,SAAS,GAAG;AACR,QAAIC,GAAJ;;AACA,QAAIvc,UAAU,CAAC,KAAK+W,MAAN,CAAd,EAA6B;AACzBwF,MAAAA,GAAG,GAAG,KAAKxF,MAAL,CAAY,KAAKe,UAAjB,CAAN;AACH,KAFD,MAGK;AACDyE,MAAAA,GAAG,GAAG,KAAKC,UAAL,GAAkB,KAAK1E,UAAvB,GAAoC;AAAE,SAAC,KAAK5S,SAAN,GAAkB,KAAK4S;AAAzB,OAA1C;AACH;;AACD,UAAM2E,SAAS,GAAIhZ,IAAD,IAAU,KAAKuY,YAAL,IAAqB,CAAC,KAAK9E,MAA3B,GAAoC,KAAKsB,SAAL,CAAe3U,OAAf,CAAuBJ,IAAvB,EAA6B,IAA7B,CAApC,GAAyE,KAAK+U,SAAL,CAAepT,OAAf,CAAuB3B,IAAvB,CAArG;;AACA,QAAI3D,SAAS,CAACyc,GAAD,CAAb,EAAoB;AAChBA,MAAAA,GAAG,CAACtL,IAAJ,CAASxN,IAAI,IAAI,KAAKW,MAAL,CAAYqY,SAAS,CAAChZ,IAAD,CAArB,CAAjB,EAA+CiZ,KAA/C,CAAqD,MAAM,CAAG,CAA9D;AACH,KAFD,MAGK,IAAIH,GAAJ,EAAS;AACV,WAAKnY,MAAL,CAAYqY,SAAS,CAACF,GAAD,CAArB;AACH;AACJ;;AACDI,EAAAA,SAAS,GAAG;AACR,WAAO,KAAK1F,SAAL,KAAmB,KAAKiC,QAAL,IAAiB,KAAKpB,UAAzC,KAAwD,CAAC,KAAKpU,QAArE;AACH;;AACa,MAAVkZ,UAAU,GAAG;AACb,QAAI,CAAC,KAAKC,UAAV,EAAsB;AAClB,aAAO,KAAP;AACH;;AACD,UAAMhX,IAAI,GAAG,KAAKiS,UAAL,CAAgBgF,WAAhB,GAA8BvI,IAA9B,EAAb;AACA,WAAO,KAAKwC,MAAL,IACF,CAAC,KAAKyB,SAAL,CAAe3V,aAAf,CAA6Bka,IAA7B,CAAkClW,CAAC,IAAIA,CAAC,CAAC5B,KAAF,CAAQ6X,WAAR,OAA0BjX,IAAjE,CAAD,KACI,CAAC,KAAK3C,YAAN,IAAsB,KAAKgU,MAA3B,IAAqC,CAAC,KAAKnU,aAAL,CAAmBga,IAAnB,CAAwBlW,CAAC,IAAIA,CAAC,CAAC5B,KAAF,CAAQ6X,WAAR,OAA0BjX,IAAvD,CAD1C,CADE,IAGH,CAAC,KAAKqQ,OAHV;AAIH;;AACD8G,EAAAA,gBAAgB,GAAG;AACf,UAAMC,KAAK,GAAG,KAAKzE,SAAL,CAAe3V,aAAf,CAA6BM,MAA7B,KAAwC,CAAtD;AACA,WAAO,CAAE8Z,KAAK,IAAI,CAAC,KAAKjB,YAAf,IAA+B,CAAC,KAAK9F,OAAtC,IACH+G,KAAK,IAAI,KAAKjB,YAAd,IAA8B,KAAKa,UAAnC,IAAiD,CAAC,KAAK3G,OADrD,KAEH,CAAC,KAAK0G,UAFV;AAGH;;AACDM,EAAAA,gBAAgB,GAAG;AACf,UAAMD,KAAK,GAAG,KAAKzE,SAAL,CAAe3V,aAAf,CAA6BM,MAA7B,KAAwC,CAAtD;AACA,WAAO8Z,KAAK,IAAI,KAAKjB,YAAd,IAA8B,CAAC,KAAKa,UAApC,IAAkD,CAAC,KAAK3G,OAA/D;AACH;;AACDiH,EAAAA,kBAAkB,GAAG;AACjB,SAAKlF,YAAL,GAAoB,IAApB;AACH;;AACDmF,EAAAA,gBAAgB,CAACvX,IAAD,EAAO;AACnB,SAAKoS,YAAL,GAAoB,KAApB;;AACA,QAAI,KAAKvB,oBAAT,EAA+B;AAC3B;AACH;;AACD,SAAKnY,MAAL,CAAYsH,IAAZ;AACH;;AACDtH,EAAAA,MAAM,CAACsH,IAAD,EAAO;AACT,QAAI,KAAKoS,YAAL,IAAqB,CAAC,KAAKvB,oBAA/B,EAAqD;AACjD;AACH;;AACD,SAAKoB,UAAL,GAAkBjS,IAAlB;;AACA,QAAI,KAAKmW,YAAL,KAAsB,KAAKa,UAAL,IAAmB,KAAKlG,aAAL,KAAuB,CAAhE,CAAJ,EAAwE;AACpE,WAAK0G,SAAL,CAAehP,IAAf,CAAoBxI,IAApB;AACH;;AACD,QAAI,CAAC,KAAKmW,YAAV,EAAwB;AACpB,WAAKxD,SAAL,CAAeja,MAAf,CAAsB,KAAKuZ,UAA3B;;AACA,UAAI,KAAKZ,MAAT,EAAiB;AACb,aAAKsB,SAAL,CAAepR,qBAAf,CAAqC,KAAK4O,SAA1C;AACH;AACJ;;AACD,SAAKwB,WAAL,CAAiB5G,IAAjB,CAAsB;AAAE/K,MAAAA,IAAF;AAAQjD,MAAAA,KAAK,EAAE,KAAK4V,SAAL,CAAe3V,aAAf,CAA6BrE,GAA7B,CAAiCqI,CAAC,IAAIA,CAAC,CAACtH,KAAxC;AAAf,KAAtB;AACA,SAAK4b,IAAL;AACH;;AACDmC,EAAAA,YAAY,CAAC/M,MAAD,EAAS;AACjB,QAAI,KAAK2K,OAAT,EAAkB;AACd;AACH;;AACD,QAAI,KAAKlC,mBAAT,EAA8B;AAC1B,WAAKmD,uBAAL;AACH;;AACD,SAAK/b,OAAL,CAAa2a,SAAb,CAAuBwC,GAAvB,CAA2B,mBAA3B;AACA,SAAKnG,UAAL,CAAgBxG,IAAhB,CAAqBL,MAArB;AACA,SAAK2K,OAAL,GAAe,IAAf;AACH;;AACDsC,EAAAA,WAAW,CAACjN,MAAD,EAAS;AAChB,SAAKnQ,OAAL,CAAa2a,SAAb,CAAuB0C,MAAvB,CAA8B,mBAA9B;AACA,SAAKtG,SAAL,CAAevG,IAAf,CAAoBL,MAApB;;AACA,QAAI,CAAC,KAAK2G,MAAN,IAAgB,CAAC,KAAKxT,QAA1B,EAAoC;AAChC,WAAK0U,UAAL;AACH;;AACD,QAAI,KAAKY,mBAAT,EAA8B;AAC1B,WAAKmD,uBAAL;AACH;;AACD,SAAKjB,OAAL,GAAe,KAAf;AACH;;AACDwC,EAAAA,WAAW,CAACja,IAAD,EAAO;AACd,QAAIA,IAAI,CAACC,QAAT,EAAmB;AACf;AACH;;AACD,SAAK8U,SAAL,CAAetR,QAAf,CAAwBzD,IAAxB;AACH;;AACDwY,EAAAA,aAAa,GAAG;AACZ,QAAI,CAAC,KAAKnG,GAAL,CAAS6H,SAAd,EAAyB;AACrB,WAAK7H,GAAL,CAASmG,aAAT;AACH;AACJ;;AACDE,EAAAA,uBAAuB,GAAG;AACtB,UAAM9X,QAAQ,GAAG,KAAKtB,aAAL,IAAsB,KAAKA,aAAL,CAAmB,CAAnB,CAAvC;AACA,SAAK+U,UAAL,GAAmBzT,QAAQ,IAAIA,QAAQ,CAACY,KAAtB,IAAgC,IAAlD;AACH;;AACDsU,EAAAA,SAAS,CAAC3W,KAAD,EAAQ;AACb,UAAMgb,SAAS,GAAGhb,KAAK,CAAC,CAAD,CAAvB;AACA,SAAKsC,SAAL,GAAiB,KAAKA,SAAL,IAAkB,KAAK6S,aAAxC;AACA,SAAKyE,UAAL,GAAkB7c,SAAS,CAACie,SAAD,CAAT,GAAuB,CAAC/d,QAAQ,CAAC+d,SAAD,CAAhC,GAA8C,KAAKpB,UAAL,IAAmB,KAAKtX,SAAL,KAAmB,KAAK6S,aAA3G;AACA,SAAKS,SAAL,CAAe7U,QAAf,CAAwBf,KAAxB;;AACA,QAAIA,KAAK,CAACO,MAAN,GAAe,CAAf,IAAoB,KAAK+V,QAA7B,EAAuC;AACnC,WAAKV,SAAL,CAAezQ,gBAAf;AACH;;AACD,QAAI,KAAKmP,MAAL,IAAevX,SAAS,CAAC,KAAKmY,UAAN,CAAxB,IAA6C,CAAC,KAAKkE,YAAvD,EAAqE;AACjE,WAAKxD,SAAL,CAAeja,MAAf,CAAsB,KAAKuZ,UAA3B;AACH;;AACD,QAAI,KAAKkE,YAAL,IAAqB,KAAK9E,MAA9B,EAAsC;AAClC,WAAKsB,SAAL,CAAepR,qBAAf,CAAqC,KAAK4O,SAA1C;AACH;AACJ;;AACD0D,EAAAA,sBAAsB,GAAG;AACrB,UAAMmE,YAAY,GAAIC,OAAD,IAAa;AAC9B,WAAKlb,KAAL,GAAakb,OAAO,CAACtf,GAAR,CAAY6G,MAAM,KAAK;AAChCuC,QAAAA,cAAc,EAAEvC,MAAM,CAAC9F,KADS;AAEhCoI,QAAAA,cAAc,EAAEtC,MAAM,CAAC6O,UAAP,CAAkB1T,aAAlB,CAAgCC,SAFhB;AAGhCiD,QAAAA,QAAQ,EAAE2B,MAAM,CAAC3B;AAHe,OAAL,CAAlB,CAAb;AAKA,WAAK8U,SAAL,CAAe7U,QAAf,CAAwB,KAAKf,KAA7B;;AACA,UAAI,KAAKsW,QAAT,EAAmB;AACf,aAAKV,SAAL,CAAezQ,gBAAf;AACH;;AACD,WAAKkU,aAAL;AACH,KAXD;;AAYA,UAAM8B,kBAAkB,GAAG,MAAM;AAC7B,YAAMC,kBAAkB,GAAGnf,KAAK,CAAC,KAAKof,SAAL,CAAe1d,OAAhB,EAAyB,KAAKiM,SAA9B,CAAhC;AACA3N,MAAAA,KAAK,CAAC,GAAG,KAAKof,SAAL,CAAezf,GAAf,CAAmB6G,MAAM,IAAIA,MAAM,CAAC8O,YAApC,CAAJ,CAAL,CACKpE,IADL,CACU7R,SAAS,CAAC8f,kBAAD,CADnB,EAEKhO,SAFL,CAEe3K,MAAM,IAAI;AACrB,cAAM5B,IAAI,GAAG,KAAK+U,SAAL,CAAe7T,QAAf,CAAwBU,MAAM,CAAC9F,KAA/B,CAAb;AACAkE,QAAAA,IAAI,CAACC,QAAL,GAAgB2B,MAAM,CAAC3B,QAAvB;AACAD,QAAAA,IAAI,CAACwB,KAAL,GAAaI,MAAM,CAACJ,KAAP,IAAgBxB,IAAI,CAACwB,KAAlC;;AACA,aAAK6Q,GAAL,CAASmG,aAAT;AACH,OAPD;AAQH,KAVD;;AAWA,SAAKgC,SAAL,CAAe1d,OAAf,CACKwP,IADL,CACU3R,SAAS,CAAC,KAAK6f,SAAN,CADnB,EACqC/f,SAAS,CAAC,KAAKsO,SAAN,CAD9C,EAEKwD,SAFL,CAEe8N,OAAO,IAAI;AACtB,WAAK5Y,SAAL,GAAiB,KAAK6S,aAAtB;AACA8F,MAAAA,YAAY,CAACC,OAAD,CAAZ;AACAC,MAAAA,kBAAkB;AACrB,KAND;AAOH;;AACDG,EAAAA,kBAAkB,CAAC3e,KAAD,EAAQ;AACtB,QAAI,CAACI,SAAS,CAACJ,KAAD,CAAV,IAAsB,KAAK8D,QAAL,IAAiB9D,KAAK,KAAK,EAAjD,IAAwD4G,KAAK,CAAC0C,OAAN,CAActJ,KAAd,KAAwBA,KAAK,CAAC4D,MAAN,KAAiB,CAArG,EAAwG;AACpG,aAAO,KAAP;AACH;;AACD,UAAMgb,eAAe,GAAI1a,IAAD,IAAU;AAC9B,UAAI,CAAC9D,SAAS,CAAC,KAAKkF,WAAN,CAAV,IAAgChF,QAAQ,CAAC4D,IAAD,CAAxC,IAAkD,KAAKqB,SAA3D,EAAsE;AAClE,aAAKiR,QAAL,CAAcV,IAAd,CAAoB,kBAAiB+I,IAAI,CAACC,SAAL,CAAe5a,IAAf,CAAqB,6EAA1D;;AACA,eAAO,KAAP;AACH;;AACD,aAAO,IAAP;AACH,KAND;;AAOA,QAAI,KAAKJ,QAAT,EAAmB;AACf,UAAI,CAAC8C,KAAK,CAAC0C,OAAN,CAActJ,KAAd,CAAL,EAA2B;AACvB,aAAKwW,QAAL,CAAcV,IAAd,CAAmB,0CAAnB;;AACA,eAAO,KAAP;AACH;;AACD,aAAO9V,KAAK,CAAC6I,KAAN,CAAY3E,IAAI,IAAI0a,eAAe,CAAC1a,IAAD,CAAnC,CAAP;AACH,KAND,MAOK;AACD,aAAO0a,eAAe,CAAC5e,KAAD,CAAtB;AACH;AACJ;;AACDmc,EAAAA,iBAAiB,CAAC4C,OAAD,EAAU;AACvB,QAAI,CAAC,KAAKJ,kBAAL,CAAwBI,OAAxB,CAAL,EAAuC;AACnC;AACH;;AACD,UAAMla,MAAM,GAAIvC,GAAD,IAAS;AACpB,UAAI4B,IAAI,GAAG,KAAK+U,SAAL,CAAe7T,QAAf,CAAwB9C,GAAxB,CAAX;;AACA,UAAI4B,IAAJ,EAAU;AACN,aAAK+U,SAAL,CAAepU,MAAf,CAAsBX,IAAtB;AACH,OAFD,MAGK;AACD,cAAM8a,WAAW,GAAG1e,QAAQ,CAACgC,GAAD,CAA5B;AACA,cAAM2c,WAAW,GAAG,CAACD,WAAD,IAAgB,CAAC,KAAKzZ,SAA1C;;AACA,YAAKyZ,WAAW,IAAIC,WAApB,EAAkC;AAC9B,eAAKhG,SAAL,CAAepU,MAAf,CAAsB,KAAKoU,SAAL,CAAe3U,OAAf,CAAuBhC,GAAvB,EAA4B,IAA5B,CAAtB;AACH,SAFD,MAGK,IAAI,KAAKiD,SAAT,EAAoB;AACrBrB,UAAAA,IAAI,GAAG;AACH,aAAC,KAAKyB,SAAN,GAAkB,IADf;AAEH,aAAC,KAAKJ,SAAN,GAAkBjD;AAFf,WAAP;AAIA,eAAK2W,SAAL,CAAepU,MAAf,CAAsB,KAAKoU,SAAL,CAAe3U,OAAf,CAAuBJ,IAAvB,EAA6B,IAA7B,CAAtB;AACH;AACJ;AACJ,KAnBD;;AAoBA,QAAI,KAAKJ,QAAT,EAAmB;AACfib,MAAAA,OAAO,CAAC7Y,OAAR,CAAgBhC,IAAI,IAAIW,MAAM,CAACX,IAAD,CAA9B;AACH,KAFD,MAGK;AACDW,MAAAA,MAAM,CAACka,OAAD,CAAN;AACH;AACJ;;AACDjF,EAAAA,iBAAiB,GAAG;AAChB,QAAI,KAAKrC,UAAT,EAAqB;AACjB;AACH;;AACD,SAAKkB,UAAL,CACKnI,IADL,CACU7R,SAAS,CAAC,KAAKsO,SAAN,CADnB,EACqCnO,GAAG,CAACogB,MAAM,IAAI,KAAKzG,YAAL,CAAkB1S,IAAlB,CAAuBmZ,MAAvB,CAAX,CADxC,EACoFngB,YAAY,CAAC,GAAD,CADhG,EACuGC,MAAM,CAAC,MAAM,KAAKyZ,YAAL,CAAkB7U,MAAlB,GAA2B,CAAlC,CAD7G,EACmJ3E,GAAG,CAAC,MAAM,KAAKwZ,YAAL,CAAkB0G,IAAlB,CAAuB,EAAvB,CAAP,CADtJ,EAEK1O,SAFL,CAEenK,IAAI,IAAI;AACnB,YAAMpC,IAAI,GAAG,KAAK+U,SAAL,CAAe5S,WAAf,CAA2BC,IAA3B,CAAb;;AACA,UAAIpC,IAAJ,EAAU;AACN,YAAI,KAAKyT,MAAT,EAAiB;AACb,eAAKsB,SAAL,CAAetR,QAAf,CAAwBzD,IAAxB;;AACA,eAAKkb,eAAL;;AACA,eAAK7I,GAAL,CAAS6F,YAAT;AACH,SAJD,MAKK;AACD,eAAKvX,MAAL,CAAYX,IAAZ;AACH;AACJ;;AACD,WAAKuU,YAAL,GAAoB,EAApB;AACH,KAfD;AAgBH;;AACDsB,EAAAA,mBAAmB,GAAG;AAClB,UAAMsF,KAAK,GAAG,KAAKxC,WAAL,CAAiB5b,aAA/B;AACA,UAAMqe,UAAU,GAAG;AACfhe,MAAAA,IAAI,EAAE,MADS;AAEfie,MAAAA,WAAW,EAAE,KAFE;AAGfC,MAAAA,cAAc,EAAE,KAHD;AAIfC,MAAAA,YAAY,EAAE,KAAKzI,UAAL,GAAkB,KAAlB,GAA0B,KAAKzO,UAJ9B;AAKf,SAAG,KAAK0O;AALO,KAAnB;;AAOA,SAAK,MAAMtQ,GAAX,IAAkB+Y,MAAM,CAAC5Y,IAAP,CAAYwY,UAAZ,CAAlB,EAA2C;AACvCD,MAAAA,KAAK,CAACM,YAAN,CAAmBhZ,GAAnB,EAAwB2Y,UAAU,CAAC3Y,GAAD,CAAlC;AACH;AACJ;;AACDmV,EAAAA,cAAc,GAAG;AACb,UAAM8D,KAAK,GAAG,EAAd;;AACA,SAAK,MAAM1b,IAAX,IAAmB,KAAKV,aAAxB,EAAuC;AACnC,UAAI,KAAK+B,SAAT,EAAoB;AAChB,YAAIvF,KAAK,GAAG,IAAZ;;AACA,YAAIkE,IAAI,CAACsB,QAAT,EAAmB;AACf,gBAAMuE,QAAQ,GAAG,KAAKC,UAAL,GAAkB,KAAKzE,SAAvB,GAAmC,KAAKhB,OAAzD;AACAvE,UAAAA,KAAK,GAAGkE,IAAI,CAAClE,KAAL,CAAW+J,QAAQ,IAAI,KAAKxF,OAA5B,CAAR;AACH,SAHD,MAIK;AACDvE,UAAAA,KAAK,GAAG,KAAKiZ,SAAL,CAAexT,aAAf,CAA6BvB,IAAI,CAAClE,KAAlC,EAAyC,KAAKuF,SAA9C,CAAR;AACH;;AACDqa,QAAAA,KAAK,CAAC7Z,IAAN,CAAW/F,KAAX;AACH,OAVD,MAWK;AACD4f,QAAAA,KAAK,CAAC7Z,IAAN,CAAW7B,IAAI,CAAClE,KAAhB;AACH;AACJ;;AACD,UAAM8E,QAAQ,GAAG,KAAKtB,aAAL,CAAmBvE,GAAnB,CAAuBqI,CAAC,IAAIA,CAAC,CAACtH,KAA9B,CAAjB;;AACA,QAAI,KAAK8D,QAAT,EAAmB;AACf,WAAK8U,SAAL,CAAegH,KAAf;;AACA,WAAK9H,WAAL,CAAiBzG,IAAjB,CAAsBvM,QAAtB;AACH,KAHD,MAIK;AACD,WAAK8T,SAAL,CAAexY,SAAS,CAACwf,KAAK,CAAC,CAAD,CAAN,CAAT,GAAsBA,KAAK,CAAC,CAAD,CAA3B,GAAiC,IAAhD;;AACA,WAAK9H,WAAL,CAAiBzG,IAAjB,CAAsBvM,QAAQ,CAAC,CAAD,CAA9B;AACH;;AACD,SAAKyR,GAAL,CAAS6F,YAAT;AACH;;AACDL,EAAAA,YAAY,GAAG;AACX,QAAI,CAAC,KAAKxD,UAAV,EAAsB;AAClB;AACH;;AACD,SAAKsH,aAAL,CAAmB,IAAnB;;AACA,SAAK5G,SAAL,CAAe7S,kBAAf;AACH;;AACDyZ,EAAAA,aAAa,CAACtH,UAAD,EAAa;AACtB,SAAKA,UAAL,GAAkBA,UAAlB;;AACA,QAAI,KAAKkE,YAAT,EAAuB;AACnB,WAAKqB,SAAL,CAAehP,IAAf,CAAoByJ,UAApB;AACH;AACJ;;AACD6G,EAAAA,eAAe,GAAG;AACd,QAAI,CAAC,KAAKzH,MAAN,IAAgB,CAAC,KAAKkC,aAA1B,EAAyC;AACrC;AACH;;AACD,SAAKA,aAAL,CAAmBzK,QAAnB,CAA4B,KAAK6J,SAAL,CAAexV,UAA3C;AACH;;AACDqc,EAAAA,YAAY,GAAG;AACX,QAAI,CAAC,KAAKnI,MAAN,IAAgB,CAAC,KAAKkC,aAA1B,EAAyC;AACrC;AACH;;AACD,SAAKA,aAAL,CAAmBpK,WAAnB;AACH;;AACDuM,EAAAA,mBAAmB,GAAG;AAClB,QAAI,KAAKrE,MAAL,IAAe,KAAK7T,QAApB,IAAgC,KAAKmL,QAAzC,EAAmD;AAC/C;AACA,WAAKsH,GAAL,CAASmG,aAAT;;AACA,WAAK7C,aAAL,CAAmBlK,cAAnB;AACH;AACJ;;AACDuL,EAAAA,UAAU,CAAClK,MAAD,EAAS;AACf,QAAI,KAAK2G,MAAL,KAAgB,KAAhB,IAAyB,CAAC,KAAKH,MAAnC,EAA2C;AACvC;AACH;;AACD,QAAI,KAAKX,WAAT,EAAsB;AAClB,UAAI,KAAKoC,SAAL,CAAexV,UAAnB,EAA+B;AAC3B,aAAKkZ,UAAL,CAAgB,KAAK1D,SAAL,CAAexV,UAA/B;AACAuN,QAAAA,MAAM,CAAC2C,cAAP;AACH,OAHD,MAIK,IAAI,KAAK0J,UAAT,EAAqB;AACtB,aAAKN,SAAL;AACA/L,QAAAA,MAAM,CAAC2C,cAAP;AACH,OAHI,MAIA;AACD,aAAKyH,KAAL;AACH;AACJ,KAZD,MAaK;AACD,WAAKA,KAAL;AACH;AACJ;;AACDJ,EAAAA,YAAY,CAAChK,MAAD,EAAS;AACjB,QAAI,KAAK2G,MAAL,IAAe,KAAKsC,WAAxB,EAAqC;AACjC,UAAI,KAAKhB,SAAL,CAAexV,UAAnB,EAA+B;AAC3B,aAAKkZ,UAAL,CAAgB,KAAK1D,SAAL,CAAexV,UAA/B;AACH,OAFD,MAGK,IAAI,KAAK4Z,UAAT,EAAqB;AACtB,aAAKN,SAAL;AACH;AACJ,KAPD,MAQK,IAAI,KAAKrH,WAAT,EAAsB;AACvB,WAAKkG,IAAL;AACH,KAFI,MAGA;AACD;AACH;;AACD5K,IAAAA,MAAM,CAAC2C,cAAP;AACH;;AACDmH,EAAAA,YAAY,CAAC9J,MAAD,EAAS;AACjB,QAAI,KAAK2G,MAAL,IAAe,KAAKsC,WAAxB,EAAqC;AACjC;AACH;;AACD,SAAK2B,IAAL;AACA5K,IAAAA,MAAM,CAAC2C,cAAP;AACH;;AACD+G,EAAAA,gBAAgB,CAAC1J,MAAD,EAAS;AACrB,QAAI,KAAK+O,cAAL,CAAoB,CAAC,CAArB,CAAJ,EAA6B;AACzB,WAAK9G,SAAL,CAAe1R,UAAf;;AACA,WAAKuY,YAAL;AACH,KAHD,MAIK;AACD,WAAK7G,SAAL,CAAezR,YAAf;;AACA,WAAK4X,eAAL;AACH;;AACD,SAAKxD,IAAL;AACA5K,IAAAA,MAAM,CAAC2C,cAAP;AACH;;AACDiH,EAAAA,cAAc,CAAC5J,MAAD,EAAS;AACnB,QAAI,CAAC,KAAK2G,MAAV,EAAkB;AACd;AACH;;AACD,QAAI,KAAKoI,cAAL,CAAoB,CAAC,CAArB,CAAJ,EAA6B;AACzB,WAAK9G,SAAL,CAAe1R,UAAf;;AACA,WAAKuY,YAAL;AACH,KAHD,MAIK;AACD,WAAK7G,SAAL,CAAevR,gBAAf;;AACA,WAAK0X,eAAL;AACH;;AACDpO,IAAAA,MAAM,CAAC2C,cAAP;AACH;;AACDoM,EAAAA,cAAc,CAACC,QAAD,EAAW;AACrB,UAAMC,SAAS,GAAG,KAAKhH,SAAL,CAAe1V,WAAf,GAA6Byc,QAA/C;AACA,WAAO,KAAKxI,MAAL,IAAe,KAAKe,UAApB,IACA,KAAKU,SAAL,CAAexV,UADf,KAECwc,SAAS,GAAG,CAAZ,IAAiBA,SAAS,KAAK,KAAKhH,SAAL,CAAe3V,aAAf,CAA6BM,MAF7D,CAAP;AAGH;;AACD0X,EAAAA,gBAAgB,GAAG;AACf,QAAI,KAAK/C,UAAL,IAAmB,CAAC,KAAKb,SAAzB,IAAsC,CAAC,KAAKX,gBAA5C,IAAgE,CAAC,KAAK4C,QAA1E,EAAoF;AAChF;AACH;;AACD,QAAI,KAAK7V,QAAT,EAAmB;AACf,WAAKoB,QAAL,CAAc,KAAK+T,SAAL,CAAejV,gBAA7B;AACH,KAFD,MAGK;AACD,WAAKiY,UAAL;AACH;AACJ;;AACe,MAAZQ,YAAY,GAAG;AACf,WAAO,KAAKqB,SAAL,IAAkB,KAAKA,SAAL,CAAeoC,SAAf,CAAyBtc,MAAzB,GAAkC,CAA3D;AACH;;AACa,MAAV0Z,UAAU,GAAG;AACb,UAAMhX,IAAI,GAAG,KAAKiS,UAAL,IAAmB,KAAKA,UAAL,CAAgBvD,IAAhB,EAAhC;AACA,WAAO1O,IAAI,IAAIA,IAAI,CAAC1C,MAAL,IAAe,KAAKwT,aAAnC;AACH;;AACD4B,EAAAA,kBAAkB,CAAC3C,MAAD,EAAS;AACvB,SAAK8J,WAAL,GAAmB,KAAKA,WAAL,IAAoB9J,MAAM,CAAC8J,WAA9C;AACA,SAAK/K,YAAL,GAAoB,KAAKA,YAAL,IAAqBiB,MAAM,CAACjB,YAAhD;AACA,SAAKC,gBAAL,GAAwB,KAAKA,gBAAL,IAAyBgB,MAAM,CAAChB,gBAAxD;AACA,SAAKC,UAAL,GAAkB,KAAKA,UAAL,IAAmBe,MAAM,CAACf,UAA5C;AACA,SAAKC,WAAL,GAAmB,KAAKA,WAAL,IAAoBc,MAAM,CAACd,WAA9C;AACA,SAAKC,YAAL,GAAoB,KAAKA,YAAL,IAAqBa,MAAM,CAACb,YAAhD;AACA,SAAK7I,aAAL,GAAqBvM,SAAS,CAAC,KAAKuM,aAAN,CAAT,GACf,KAAKA,aADU,GAEfvM,SAAS,CAACiW,MAAM,CAACZ,oBAAR,CAAT,GAAyC,CAACY,MAAM,CAACZ,oBAAjD,GAAwE,KAF9E;AAGA,SAAKC,WAAL,GAAmBtV,SAAS,CAAC,KAAKsV,WAAN,CAAT,GAA8B,KAAKA,WAAnC,GAAiDW,MAAM,CAACX,WAA3E;AACA,SAAKzG,QAAL,GAAgB,KAAKA,QAAL,IAAiBoH,MAAM,CAACpH,QAAxC;AACA,SAAK1J,SAAL,GAAiB,KAAKA,SAAL,IAAkB8Q,MAAM,CAAC9Q,SAA1C;AACA,SAAKI,SAAL,GAAiB,KAAKA,SAAL,IAAkB0Q,MAAM,CAAC1Q,SAA1C;AACA,SAAKgQ,UAAL,GAAkB,KAAKA,UAAL,IAAmBU,MAAM,CAACV,UAA5C;AACH;;AA9uBmB;;AAgvBxBO,iBAAiB,CAAC9U,IAAlB;AAAA,mBAA8G8U,iBAA9G,EAlmFuG9Y,EAkmFvG,mBAAiJ,OAAjJ,GAlmFuGA,EAkmFvG,mBAAsL,WAAtL,GAlmFuGA,EAkmFvG,mBAA+N+X,cAA/N,GAlmFuG/X,EAkmFvG,mBAA0P6Y,uBAA1P,GAlmFuG7Y,EAkmFvG,mBAA8RA,EAAE,CAACK,UAAjS,GAlmFuGL,EAkmFvG,mBAAwTA,EAAE,CAACgjB,iBAA3T,GAlmFuGhjB,EAkmFvG,mBAAyVyY,cAAzV;AAAA;;AACAK,iBAAiB,CAACpC,IAAlB,kBAnmFuG1W,EAmmFvG;AAAA,QAAkG8Y,iBAAlG;AAAA;AAAA;AAAA;AAnmFuG9Y,MAAAA,EAmmFvG,0BAIwGqE,yBAJxG,KAI4JtD,WAJ5J;AAnmFuGf,MAAAA,EAmmFvG,0BAIuOuE,2BAJvO,KAI6RxD,WAJ7R;AAnmFuGf,MAAAA,EAmmFvG,0BAIqWwE,wBAJrW,KAIwZzD,WAJxZ;AAnmFuGf,MAAAA,EAmmFvG,0BAIqeyE,6BAJre,KAI6hB1D,WAJ7hB;AAnmFuGf,MAAAA,EAmmFvG,0BAIsmB0E,yBAJtmB,KAI0pB3D,WAJ1pB;AAnmFuGf,MAAAA,EAmmFvG,0BAImuB2E,yBAJnuB,KAIuxB5D,WAJvxB;AAnmFuGf,MAAAA,EAmmFvG,0BAIk2B4E,2BAJl2B,KAIw5B7D,WAJx5B;AAnmFuGf,MAAAA,EAmmFvG,0BAIu+B6E,+BAJv+B,KAIiiC9D,WAJjiC;AAnmFuGf,MAAAA,EAmmFvG,0BAI+mC8E,8BAJ/mC,KAIwqC/D,WAJxqC;AAnmFuGf,MAAAA,EAmmFvG,0BAI8uC+E,sBAJ9uC,KAI+xChE,WAJ/xC;AAnmFuGf,MAAAA,EAmmFvG,0BAIg3CgF,iCAJh3C,KAI46CjE,WAJ56C;AAnmFuGf,MAAAA,EAmmFvG,0BAIm+CsX,iBAJn+C;AAAA;;AAAA;AAAA;;AAnmFuGtX,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAAA;AAAA;AAAA;AAAA;AAnmFuGA,MAAAA,EAmmFvG,aAIsnDgP,wBAJtnD;AAnmFuGhP,MAAAA,EAmmFvG;AAAA;;AAAA;AAAA;;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAnmFuGA,MAAAA,EAmmFvG,qBAnmFuGA,EAmmFvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAnmFuGA,MAAAA,EAmmFvG;AAAA,eAAkG,yBAAlG;AAAA;AAAA;;AAAA;AAnmFuGA,MAAAA,EAmmFvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAnmFuGA,EAmmFvG,oBAAkoE,CAAC;AACvnEijB,IAAAA,OAAO,EAAE3hB,iBAD8mE;AAEvnE4hB,IAAAA,WAAW,EAAEpiB,UAAU,CAAC,MAAMgY,iBAAP,CAFgmE;AAGvnEqK,IAAAA,KAAK,EAAE;AAHgnE,GAAD,EAIvnErW,sBAJunE,CAAloE,GAnmFuG9M,EAmmFvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAnmFuGA,EAmmFvG;;AAnmFuGA,MAAAA,EAumF4tD,4BAJn0D;AAnmFuGA,MAAAA,EAumFsuD;AAAA,eAAc,2BAAd;AAAA,QAJ70D;AAnmFuGA,MAAAA,EAumFg6D,4BAJvgE;AAnmFuGA,MAAAA,EAumF48D,4BAJnjE;AAnmFuGA,MAAAA,EAumF0+D,UAJjlE;AAnmFuGA,MAAAA,EAumFy/D,eAJhmE;AAnmFuGA,MAAAA,EAumF2gE,kFAJlnE;AAnmFuGA,MAAAA,EAumFy2F,gEAJh9F;AAnmFuGA,MAAAA,EAumFsnG,4BAJ7tG;AAnmFuGA,MAAAA,EAumFi1G,iCAJx7G;AAnmFuGA,MAAAA,EAumF6oH;AAvmF7oHA,QAAAA,EAumF6oH;;AAAA,oBAvmF7oHA,EAumF6oH;;AAAA,eAAU,qBAAV;AAAA;AAAA,eAA+E,wBAA/E;AAAA;AAvmF7oHA,QAAAA,EAumF6oH;;AAAA,oBAvmF7oHA,EAumF6oH;;AAAA,eAA6I,+BAA7I;AAAA;AAAA,eAAiN,wBAAjN;AAAA;AAAA,eAAqQ,uBAArQ;AAAA;AAAA,eAA0T,wBAA1T;AAAA,QAJpvH;AAnmFuGA,MAAAA,EAumFi1G,eAJx7G;AAnmFuGA,MAAAA,EAumFssI,eAJ7yI;AAnmFuGA,MAAAA,EAumFktI,eAJzzI;AAnmFuGA,MAAAA,EAumFguI,kFAJv0I;AAnmFuGA,MAAAA,EAumF6iJ,oEAJppJ;AAnmFuGA,MAAAA,EAumFytJ,8BAJh0J;AAnmFuGA,MAAAA,EAumFowJ,yBAJ32J;AAnmFuGA,MAAAA,EAumF0yJ,eAJj5J;AAnmFuGA,MAAAA,EAumFmzJ,eAJ15J;AAnmFuGA,MAAAA,EAumF6zJ,gGAJp6J;AAAA;;AAAA;AAnmFuGA,MAAAA,EAumFmxD,iGAJ13D;AAnmFuGA,MAAAA,EAumF0+D,aAJjlE;AAnmFuGA,MAAAA,EAumF0+D,mCAJjlE;AAnmFuGA,MAAAA,EAumF0hE,aAJjoE;AAnmFuGA,MAAAA,EAumF0hE,+FAJjoE;AAnmFuGA,MAAAA,EAumFu3F,aAJ99F;AAnmFuGA,MAAAA,EAumFu3F,4FAJ99F;AAnmFuGA,MAAAA,EAumF2rG,aAJlyG;AAnmFuGA,MAAAA,EAumF2rG,0FAJlyG;AAnmFuGA,MAAAA,EAumFu9G,aAJ9jH;AAnmFuGA,MAAAA,EAumFu9G,mJAJ9jH;AAnmFuGA,MAAAA,EAumFy3G,wQAJh+G;AAnmFuGA,MAAAA,EAumF+uI,aAJt1I;AAnmFuGA,MAAAA,EAumF+uI,gCAJt1I;AAnmFuGA,MAAAA,EAumFojJ,aAJ3pJ;AAnmFuGA,MAAAA,EAumFojJ,oCAJ3pJ;AAnmFuGA,MAAAA,EAumFi1J,aAJx7J;AAnmFuGA,MAAAA,EAumFi1J,+BAJx7J;AAAA;AAAA;AAAA,eAIogcmC,EAAE,CAACwU,IAJvgc,EAIqlcxU,EAAE,CAACihB,OAJxlc,EAI6xcjhB,EAAE,CAACyU,gBAJhyc,EAI+rcrT,oBAJ/rc,EAIwubyL,wBAJxub,EAIq5c7M,EAAE,CAACkhB,OAJx5c;AAAA;AAAA;AAAA;AAAA;;AAKA;AAAA,qDAxmFuGrjB,EAwmFvG,mBAA2F8Y,iBAA3F,EAA0H,CAAC;AAC/G5U,IAAAA,IAAI,EAAE5D,SADyG;AAE/G6D,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE,WAAZ;AAAyBkf,MAAAA,SAAS,EAAE,CAAC;AAC5BL,QAAAA,OAAO,EAAE3hB,iBADmB;AAE5B4hB,QAAAA,WAAW,EAAEpiB,UAAU,CAAC,MAAMgY,iBAAP,CAFK;AAG5BqK,QAAAA,KAAK,EAAE;AAHqB,OAAD,EAI5BrW,sBAJ4B,CAApC;AAIiCiK,MAAAA,aAAa,EAAEvW,iBAAiB,CAACwW,IAJlE;AAIwEH,MAAAA,eAAe,EAAEtW,uBAAuB,CAACuW,MAJjH;AAIyHxS,MAAAA,QAAQ,EAAE,6pOAJnI;AAIkyOif,MAAAA,MAAM,EAAE,CAAC,suJAAD;AAJ1yO,KAAD;AAFyG,GAAD,CAA1H,EAO4B,YAAY;AAAE,WAAO,CAAC;AAAErf,MAAAA,IAAI,EAAEjB,SAAR;AAAmBgU,MAAAA,UAAU,EAAE,CAAC;AAC9D/S,QAAAA,IAAI,EAAElD,SADwD;AAE9DmD,QAAAA,IAAI,EAAE,CAAC,OAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAED,MAAAA,IAAI,EAAEjB,SAAR;AAAmBgU,MAAAA,UAAU,EAAE,CAAC;AAClC/S,QAAAA,IAAI,EAAElD,SAD4B;AAElCmD,QAAAA,IAAI,EAAE,CAAC,WAAD;AAF4B,OAAD;AAA/B,KAH2B,EAM3B;AAAED,MAAAA,IAAI,EAAE6T;AAAR,KAN2B,EAMD;AAAE7T,MAAAA,IAAI,EAAEjB,SAAR;AAAmBgU,MAAAA,UAAU,EAAE,CAAC;AAC5D/S,QAAAA,IAAI,EAAExD,MADsD;AAE5DyD,QAAAA,IAAI,EAAE,CAAC0U,uBAAD;AAFsD,OAAD;AAA/B,KANC,EAS3B;AAAE3U,MAAAA,IAAI,EAAElE,EAAE,CAACK;AAAX,KAT2B,EASF;AAAE6D,MAAAA,IAAI,EAAElE,EAAE,CAACgjB;AAAX,KATE,EAS8B;AAAE9e,MAAAA,IAAI,EAAEuU;AAAR,KAT9B,CAAP;AASiE,GAhB3G,EAgB6H;AAAElQ,IAAAA,SAAS,EAAE,CAAC;AAC3HrE,MAAAA,IAAI,EAAEhE;AADqH,KAAD,CAAb;AAE7GiI,IAAAA,SAAS,EAAE,CAAC;AACZjE,MAAAA,IAAI,EAAEhE;AADM,KAAD,CAFkG;AAI7GmZ,IAAAA,SAAS,EAAE,CAAC;AACZnV,MAAAA,IAAI,EAAEhE;AADM,KAAD,CAJkG;AAM7G6iB,IAAAA,WAAW,EAAE,CAAC;AACd7e,MAAAA,IAAI,EAAEhE;AADQ,KAAD,CANgG;AAQ7G8X,IAAAA,YAAY,EAAE,CAAC;AACf9T,MAAAA,IAAI,EAAEhE;AADS,KAAD,CAR+F;AAU7G+X,IAAAA,gBAAgB,EAAE,CAAC;AACnB/T,MAAAA,IAAI,EAAEhE;AADa,KAAD,CAV2F;AAY7GgY,IAAAA,UAAU,EAAE,CAAC;AACbhU,MAAAA,IAAI,EAAEhE;AADO,KAAD,CAZiG;AAc7GiY,IAAAA,WAAW,EAAE,CAAC;AACdjU,MAAAA,IAAI,EAAEhE;AADQ,KAAD,CAdgG;AAgB7GkY,IAAAA,YAAY,EAAE,CAAC;AACflU,MAAAA,IAAI,EAAEhE;AADS,KAAD,CAhB+F;AAkB7GqY,IAAAA,UAAU,EAAE,CAAC;AACbrU,MAAAA,IAAI,EAAEhE;AADO,KAAD,CAlBiG;AAoB7GoZ,IAAAA,gBAAgB,EAAE,CAAC;AACnBpV,MAAAA,IAAI,EAAEhE;AADa,KAAD,CApB2F;AAsB7G2R,IAAAA,QAAQ,EAAE,CAAC;AACX3N,MAAAA,IAAI,EAAEhE;AADK,KAAD,CAtBmG;AAwB7GqZ,IAAAA,OAAO,EAAE,CAAC;AACVrV,MAAAA,IAAI,EAAEhE;AADI,KAAD,CAxBoG;AA0B7GsZ,IAAAA,aAAa,EAAE,CAAC;AAChBtV,MAAAA,IAAI,EAAEhE;AADU,KAAD,CA1B8F;AA4B7GqG,IAAAA,YAAY,EAAE,CAAC;AACfrC,MAAAA,IAAI,EAAEhE;AADS,KAAD,CA5B+F;AA8B7GuZ,IAAAA,WAAW,EAAE,CAAC;AACdvV,MAAAA,IAAI,EAAEhE;AADQ,KAAD,CA9BgG;AAgC7GoY,IAAAA,WAAW,EAAE,CAAC;AACdpU,MAAAA,IAAI,EAAEhE;AADQ,KAAD,CAhCgG;AAkC7GyG,IAAAA,gBAAgB,EAAE,CAAC;AACnBzC,MAAAA,IAAI,EAAEhE;AADa,KAAD,CAlC2F;AAoC7GiH,IAAAA,OAAO,EAAE,CAAC;AACVjD,MAAAA,IAAI,EAAEhE;AADI,KAAD,CApCoG;AAsC7G0M,IAAAA,UAAU,EAAE,CAAC;AACb1I,MAAAA,IAAI,EAAEhE;AADO,KAAD,CAtCiG;AAwC7G2U,IAAAA,YAAY,EAAE,CAAC;AACf3Q,MAAAA,IAAI,EAAEhE;AADS,KAAD,CAxC+F;AA0C7GqP,IAAAA,aAAa,EAAE,CAAC;AAChBrL,MAAAA,IAAI,EAAEhE;AADU,KAAD,CA1C8F;AA4C7GwM,IAAAA,eAAe,EAAE,CAAC;AAClBxI,MAAAA,IAAI,EAAEhE;AADY,KAAD,CA5C4F;AA8C7G0H,IAAAA,sBAAsB,EAAE,CAAC;AACzB1D,MAAAA,IAAI,EAAEhE;AADmB,KAAD,CA9CqF;AAgD7GmJ,IAAAA,QAAQ,EAAE,CAAC;AACXnF,MAAAA,IAAI,EAAEhE;AADK,KAAD,CAhDmG;AAkD7GwZ,IAAAA,SAAS,EAAE,CAAC;AACZxV,MAAAA,IAAI,EAAEhE;AADM,KAAD,CAlDkG;AAoD7GyZ,IAAAA,gBAAgB,EAAE,CAAC;AACnBzV,MAAAA,IAAI,EAAEhE;AADa,KAAD,CApD2F;AAsD7G0Z,IAAAA,UAAU,EAAE,CAAC;AACb1V,MAAAA,IAAI,EAAEhE;AADO,KAAD,CAtDiG;AAwD7G2Z,IAAAA,UAAU,EAAE,CAAC;AACb3V,MAAAA,IAAI,EAAEhE;AADO,KAAD,CAxDiG;AA0D7GsjB,IAAAA,QAAQ,EAAE,CAAC;AACXtf,MAAAA,IAAI,EAAEhE;AADK,KAAD,CA1DmG;AA4D7G4Z,IAAAA,QAAQ,EAAE,CAAC;AACX5V,MAAAA,IAAI,EAAEhE;AADK,KAAD,CA5DmG;AA8D7G6Z,IAAAA,oBAAoB,EAAE,CAAC;AACvB7V,MAAAA,IAAI,EAAEhE;AADiB,KAAD,CA9DuF;AAgE7G8Z,IAAAA,aAAa,EAAE,CAAC;AAChB9V,MAAAA,IAAI,EAAEhE;AADU,KAAD,CAhE8F;AAkE7G+Z,IAAAA,kBAAkB,EAAE,CAAC;AACrB/V,MAAAA,IAAI,EAAEhE;AADe,KAAD,CAlEyF;AAoE7Gga,IAAAA,SAAS,EAAE,CAAC;AACZhW,MAAAA,IAAI,EAAEhE;AADM,KAAD,CApEkG;AAsE7GwgB,IAAAA,SAAS,EAAE,CAAC;AACZxc,MAAAA,IAAI,EAAEhE;AADM,KAAD,EAEZ;AACCgE,MAAAA,IAAI,EAAEjD,WADP;AAECkD,MAAAA,IAAI,EAAE,CAAC,2BAAD;AAFP,KAFY,CAtEkG;AA2E7GuC,IAAAA,QAAQ,EAAE,CAAC;AACXxC,MAAAA,IAAI,EAAEhE;AADK,KAAD,EAEX;AACCgE,MAAAA,IAAI,EAAEjD,WADP;AAECkD,MAAAA,IAAI,EAAE,CAAC,0BAAD;AAFP,KAFW,CA3EmG;AAgF7GiW,IAAAA,MAAM,EAAE,CAAC;AACTlW,MAAAA,IAAI,EAAEhE;AADG,KAAD,EAET;AACCgE,MAAAA,IAAI,EAAEjD,WADP;AAECkD,MAAAA,IAAI,EAAE,CAAC,0BAAD;AAFP,KAFS,CAhFqG;AAqF7GkW,IAAAA,UAAU,EAAE,CAAC;AACbnW,MAAAA,IAAI,EAAEhE;AADO,KAAD,EAEb;AACCgE,MAAAA,IAAI,EAAEjD,WADP;AAECkD,MAAAA,IAAI,EAAE,CAAC,4BAAD;AAFP,KAFa,CArFiG;AA0F7GmW,IAAAA,SAAS,EAAE,CAAC;AACZpW,MAAAA,IAAI,EAAEhE;AADM,KAAD,EAEZ;AACCgE,MAAAA,IAAI,EAAEjD,WADP;AAECkD,MAAAA,IAAI,EAAE,CAAC,2BAAD;AAFP,KAFY,CA1FkG;AA+F7GoW,IAAAA,MAAM,EAAE,CAAC;AACTrW,MAAAA,IAAI,EAAEhE;AADG,KAAD,EAET;AACCgE,MAAAA,IAAI,EAAEjD,WADP;AAECkD,MAAAA,IAAI,EAAE,CAAC,wBAAD;AAFP,KAFS,CA/FqG;AAoG7G8B,IAAAA,KAAK,EAAE,CAAC;AACR/B,MAAAA,IAAI,EAAEhE;AADE,KAAD,CApGsG;AAsG7GgI,IAAAA,WAAW,EAAE,CAAC;AACdhE,MAAAA,IAAI,EAAEhE;AADQ,KAAD,CAtGgG;AAwG7G+b,IAAAA,gBAAgB,EAAE,CAAC;AACnB/X,MAAAA,IAAI,EAAEhE;AADa,KAAD,CAxG2F;AA0G7Gsa,IAAAA,SAAS,EAAE,CAAC;AACZtW,MAAAA,IAAI,EAAEvD,MADM;AAEZwD,MAAAA,IAAI,EAAE,CAAC,MAAD;AAFM,KAAD,CA1GkG;AA6G7GsW,IAAAA,UAAU,EAAE,CAAC;AACbvW,MAAAA,IAAI,EAAEvD,MADO;AAEbwD,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFO,KAAD,CA7GiG;AAgH7GuW,IAAAA,WAAW,EAAE,CAAC;AACdxW,MAAAA,IAAI,EAAEvD,MADQ;AAEdwD,MAAAA,IAAI,EAAE,CAAC,QAAD;AAFQ,KAAD,CAhHgG;AAmH7GwW,IAAAA,SAAS,EAAE,CAAC;AACZzW,MAAAA,IAAI,EAAEvD,MADM;AAEZwD,MAAAA,IAAI,EAAE,CAAC,MAAD;AAFM,KAAD,CAnHkG;AAsH7GyW,IAAAA,UAAU,EAAE,CAAC;AACb1W,MAAAA,IAAI,EAAEvD,MADO;AAEbwD,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFO,KAAD,CAtHiG;AAyH7G0W,IAAAA,WAAW,EAAE,CAAC;AACd3W,MAAAA,IAAI,EAAEvD,MADQ;AAEdwD,MAAAA,IAAI,EAAE,CAAC,QAAD;AAFQ,KAAD,CAzHgG;AA4H7G2W,IAAAA,UAAU,EAAE,CAAC;AACb5W,MAAAA,IAAI,EAAEvD,MADO;AAEbwD,MAAAA,IAAI,EAAE,CAAC,OAAD;AAFO,KAAD,CA5HiG;AA+H7G4W,IAAAA,QAAQ,EAAE,CAAC;AACX7W,MAAAA,IAAI,EAAEvD,MADK;AAEXwD,MAAAA,IAAI,EAAE,CAAC,KAAD;AAFK,KAAD,CA/HmG;AAkI7G6W,IAAAA,WAAW,EAAE,CAAC;AACd9W,MAAAA,IAAI,EAAEvD,MADQ;AAEdwD,MAAAA,IAAI,EAAE,CAAC,QAAD;AAFQ,KAAD,CAlIgG;AAqI7GuL,IAAAA,MAAM,EAAE,CAAC;AACTxL,MAAAA,IAAI,EAAEvD,MADG;AAETwD,MAAAA,IAAI,EAAE,CAAC,QAAD;AAFG,KAAD,CArIqG;AAwI7GwL,IAAAA,WAAW,EAAE,CAAC;AACdzL,MAAAA,IAAI,EAAEvD,MADQ;AAEdwD,MAAAA,IAAI,EAAE,CAAC,aAAD;AAFQ,KAAD,CAxIgG;AA2I7Gsf,IAAAA,cAAc,EAAE,CAAC;AACjBvf,MAAAA,IAAI,EAAEhD,YADW;AAEjBiD,MAAAA,IAAI,EAAE,CAACE,yBAAD,EAA4B;AAAE+S,QAAAA,IAAI,EAAErW;AAAR,OAA5B;AAFW,KAAD,CA3I6F;AA8I7G2iB,IAAAA,gBAAgB,EAAE,CAAC;AACnBxf,MAAAA,IAAI,EAAEhD,YADa;AAEnBiD,MAAAA,IAAI,EAAE,CAACI,2BAAD,EAA8B;AAAE6S,QAAAA,IAAI,EAAErW;AAAR,OAA9B;AAFa,KAAD,CA9I2F;AAiJ7G4iB,IAAAA,aAAa,EAAE,CAAC;AAChBzf,MAAAA,IAAI,EAAEhD,YADU;AAEhBiD,MAAAA,IAAI,EAAE,CAACK,wBAAD,EAA2B;AAAE4S,QAAAA,IAAI,EAAErW;AAAR,OAA3B;AAFU,KAAD,CAjJ8F;AAoJ7G6iB,IAAAA,kBAAkB,EAAE,CAAC;AACrB1f,MAAAA,IAAI,EAAEhD,YADe;AAErBiD,MAAAA,IAAI,EAAE,CAACM,6BAAD,EAAgC;AAAE2S,QAAAA,IAAI,EAAErW;AAAR,OAAhC;AAFe,KAAD,CApJyF;AAuJ7GmW,IAAAA,cAAc,EAAE,CAAC;AACjBhT,MAAAA,IAAI,EAAEhD,YADW;AAEjBiD,MAAAA,IAAI,EAAE,CAACO,yBAAD,EAA4B;AAAE0S,QAAAA,IAAI,EAAErW;AAAR,OAA5B;AAFW,KAAD,CAvJ6F;AA0J7GoW,IAAAA,cAAc,EAAE,CAAC;AACjBjT,MAAAA,IAAI,EAAEhD,YADW;AAEjBiD,MAAAA,IAAI,EAAE,CAACQ,yBAAD,EAA4B;AAAEyS,QAAAA,IAAI,EAAErW;AAAR,OAA5B;AAFW,KAAD,CA1J6F;AA6J7G8iB,IAAAA,gBAAgB,EAAE,CAAC;AACnB3f,MAAAA,IAAI,EAAEhD,YADa;AAEnBiD,MAAAA,IAAI,EAAE,CAACS,2BAAD,EAA8B;AAAEwS,QAAAA,IAAI,EAAErW;AAAR,OAA9B;AAFa,KAAD,CA7J2F;AAgK7G+iB,IAAAA,oBAAoB,EAAE,CAAC;AACvB5f,MAAAA,IAAI,EAAEhD,YADiB;AAEvBiD,MAAAA,IAAI,EAAE,CAACU,+BAAD,EAAkC;AAAEuS,QAAAA,IAAI,EAAErW;AAAR,OAAlC;AAFiB,KAAD,CAhKuF;AAmK7GgjB,IAAAA,mBAAmB,EAAE,CAAC;AACtB7f,MAAAA,IAAI,EAAEhD,YADgB;AAEtBiD,MAAAA,IAAI,EAAE,CAACW,8BAAD,EAAiC;AAAEsS,QAAAA,IAAI,EAAErW;AAAR,OAAjC;AAFgB,KAAD,CAnKwF;AAsK7GijB,IAAAA,WAAW,EAAE,CAAC;AACd9f,MAAAA,IAAI,EAAEhD,YADQ;AAEdiD,MAAAA,IAAI,EAAE,CAACY,sBAAD,EAAyB;AAAEqS,QAAAA,IAAI,EAAErW;AAAR,OAAzB;AAFQ,KAAD,CAtKgG;AAyK7GkjB,IAAAA,sBAAsB,EAAE,CAAC;AACzB/f,MAAAA,IAAI,EAAEhD,YADmB;AAEzBiD,MAAAA,IAAI,EAAE,CAACa,iCAAD,EAAoC;AAAEoS,QAAAA,IAAI,EAAErW;AAAR,OAApC;AAFmB,KAAD,CAzKqF;AA4K7G0b,IAAAA,aAAa,EAAE,CAAC;AAChBvY,MAAAA,IAAI,EAAEtD,SADU;AAEhBuD,MAAAA,IAAI,EAAE,CAACrD,UAAU,CAAC,MAAMkO,wBAAP,CAAX;AAFU,KAAD,CA5K8F;AA+K7GyQ,IAAAA,WAAW,EAAE,CAAC;AACdvb,MAAAA,IAAI,EAAEtD,SADQ;AAEduD,MAAAA,IAAI,EAAE,CAAC,aAAD,EAAgB;AAAEkT,QAAAA,MAAM,EAAE;AAAV,OAAhB;AAFQ,KAAD,CA/KgG;AAkL7GiK,IAAAA,SAAS,EAAE,CAAC;AACZpd,MAAAA,IAAI,EAAE/C,eADM;AAEZgD,MAAAA,IAAI,EAAE,CAACmT,iBAAD,EAAoB;AAAE4M,QAAAA,WAAW,EAAE;AAAf,OAApB;AAFM,KAAD,CAlLkG;AAqL7GjJ,IAAAA,eAAe,EAAE,CAAC;AAClB/W,MAAAA,IAAI,EAAEjD,WADY;AAElBkD,MAAAA,IAAI,EAAE,CAAC,iBAAD;AAFY,KAAD,CArL4F;AAwL7G4C,IAAAA,QAAQ,EAAE,CAAC;AACX7C,MAAAA,IAAI,EAAEjD,WADK;AAEXkD,MAAAA,IAAI,EAAE,CAAC,0BAAD;AAFK,KAAD,CAxLmG;AA2L7GgY,IAAAA,QAAQ,EAAE,CAAC;AACXjY,MAAAA,IAAI,EAAEjD,WADK;AAEXkD,MAAAA,IAAI,EAAE,CAAC,0BAAD;AAFK,KAAD,CA3LmG;AA8L7GiY,IAAAA,MAAM,EAAE,CAAC;AACTlY,MAAAA,IAAI,EAAEjD,WADG;AAETkD,MAAAA,IAAI,EAAE,CAAC,wBAAD;AAFG,KAAD,CA9LqG;AAiM7G8Y,IAAAA,aAAa,EAAE,CAAC;AAChB/Y,MAAAA,IAAI,EAAE9C,YADU;AAEhB+C,MAAAA,IAAI,EAAE,CAAC,SAAD,EAAY,CAAC,QAAD,CAAZ;AAFU,KAAD;AAjM8F,GAhB7H;AAAA;;AAsNA,SAASggB,4BAAT,GAAwC;AACpC,SAAO,IAAIC,qBAAJ,EAAP;AACH;;AACD,MAAMA,qBAAN,CAA4B;AACxB5gB,EAAAA,WAAW,GAAG;AACV,SAAK6gB,SAAL,GAAiB,EAAjB;AACH;;AACQ,MAALzhB,KAAK,GAAG;AACR,WAAO,KAAKyhB,SAAZ;AACH;;AACD5c,EAAAA,MAAM,CAACX,IAAD,EAAOJ,QAAP,EAAiB4d,YAAjB,EAA+B;AACjCxd,IAAAA,IAAI,CAACY,QAAL,GAAgB,IAAhB;;AACA,QAAI,CAACZ,IAAI,CAACsB,QAAN,IAAmB,CAAC1B,QAAD,IAAa4d,YAApC,EAAmD;AAC/C,WAAKD,SAAL,CAAe1b,IAAf,CAAoB7B,IAApB;AACH;;AACD,QAAIJ,QAAJ,EAAc;AACV,UAAII,IAAI,CAAC+C,MAAT,EAAiB;AACb,cAAM0a,aAAa,GAAGzd,IAAI,CAAC+C,MAAL,CAAYzB,QAAZ,CAAqB5B,MAA3C;AACA,cAAMge,aAAa,GAAG1d,IAAI,CAAC+C,MAAL,CAAYzB,QAAZ,CAAqBxG,MAArB,CAA4BsI,CAAC,IAAIA,CAAC,CAACxC,QAAnC,EAA6ClB,MAAnE;AACAM,QAAAA,IAAI,CAAC+C,MAAL,CAAYnC,QAAZ,GAAuB6c,aAAa,KAAKC,aAAzC;AACH,OAJD,MAKK,IAAI1d,IAAI,CAACsB,QAAT,EAAmB;AACpB,aAAKqc,yBAAL,CAA+B3d,IAAI,CAACsB,QAApC,EAA8C,IAA9C;;AACA,aAAKsc,eAAL,CAAqB5d,IAArB;;AACA,YAAIwd,YAAY,IAAI,KAAKK,eAAL,CAAqB7d,IAArB,CAApB,EAAgD;AAC5C,eAAKud,SAAL,GAAiB,CAAC,GAAG,KAAKA,SAAL,CAAeziB,MAAf,CAAsBsI,CAAC,IAAIA,CAAC,CAACL,MAAF,KAAa/C,IAAxC,CAAJ,EAAmDA,IAAnD,CAAjB;AACH,SAFD,MAGK;AACD,eAAKud,SAAL,GAAiB,CAAC,GAAG,KAAKA,SAAT,EAAoB,GAAGvd,IAAI,CAACsB,QAAL,CAAcxG,MAAd,CAAqBsI,CAAC,IAAI,CAACA,CAAC,CAACnD,QAA7B,CAAvB,CAAjB;AACH;AACJ;AACJ;AACJ;;AACDe,EAAAA,QAAQ,CAAChB,IAAD,EAAOJ,QAAP,EAAiB;AACrB,SAAK2d,SAAL,GAAiB,KAAKA,SAAL,CAAeziB,MAAf,CAAsBsI,CAAC,IAAIA,CAAC,KAAKpD,IAAjC,CAAjB;AACAA,IAAAA,IAAI,CAACY,QAAL,GAAgB,KAAhB;;AACA,QAAIhB,QAAJ,EAAc;AACV,UAAII,IAAI,CAAC+C,MAAL,IAAe/C,IAAI,CAAC+C,MAAL,CAAYnC,QAA/B,EAAyC;AACrC,cAAMU,QAAQ,GAAGtB,IAAI,CAAC+C,MAAL,CAAYzB,QAA7B;;AACA,aAAKwc,aAAL,CAAmB9d,IAAI,CAAC+C,MAAxB;;AACA,aAAK6a,eAAL,CAAqB5d,IAAI,CAAC+C,MAA1B;;AACA,aAAKwa,SAAL,CAAe1b,IAAf,CAAoB,GAAGP,QAAQ,CAACxG,MAAT,CAAgBsI,CAAC,IAAIA,CAAC,KAAKpD,IAAN,IAAc,CAACoD,CAAC,CAACnD,QAAtC,CAAvB;;AACAD,QAAAA,IAAI,CAAC+C,MAAL,CAAYnC,QAAZ,GAAuB,KAAvB;AACH,OAND,MAOK,IAAIZ,IAAI,CAACsB,QAAT,EAAmB;AACpB,aAAKqc,yBAAL,CAA+B3d,IAAI,CAACsB,QAApC,EAA8C,KAA9C;;AACA,aAAKsc,eAAL,CAAqB5d,IAArB;AACH;AACJ;AACJ;;AACD+B,EAAAA,KAAK,CAACD,YAAD,EAAe;AAChB,SAAKyb,SAAL,GAAiBzb,YAAY,GAAG,KAAKyb,SAAL,CAAeziB,MAAf,CAAsBsI,CAAC,IAAIA,CAAC,CAACnD,QAA7B,CAAH,GAA4C,EAAzE;AACH;;AACD0d,EAAAA,yBAAyB,CAACrc,QAAD,EAAWV,QAAX,EAAqB;AAC1C,SAAK,MAAM4D,KAAX,IAAoBlD,QAApB,EAA8B;AAC1B,UAAIkD,KAAK,CAACvE,QAAV,EAAoB;AAChB;AACH;;AACDuE,MAAAA,KAAK,CAAC5D,QAAN,GAAiBA,QAAjB;AACH;AACJ;;AACDgd,EAAAA,eAAe,CAAC7a,MAAD,EAAS;AACpB,SAAKwa,SAAL,GAAiB,CACb,GAAG,KAAKA,SAAL,CAAeziB,MAAf,CAAsBsI,CAAC,IAAIA,CAAC,CAACL,MAAF,KAAaA,MAAxC,CADU,EAEb,GAAGA,MAAM,CAACzB,QAAP,CAAgBxG,MAAhB,CAAuBsI,CAAC,IAAIA,CAAC,CAACL,MAAF,KAAaA,MAAb,IAAuBK,CAAC,CAACnD,QAAzB,IAAqCmD,CAAC,CAACxC,QAAnE,CAFU,CAAjB;AAIH;;AACDkd,EAAAA,aAAa,CAAC/a,MAAD,EAAS;AAClB,SAAKwa,SAAL,GAAiB,KAAKA,SAAL,CAAeziB,MAAf,CAAsBsI,CAAC,IAAIA,CAAC,KAAKL,MAAjC,CAAjB;AACH;;AACD8a,EAAAA,eAAe,CAAC7d,IAAD,EAAO;AAClB,WAAOA,IAAI,CAACsB,QAAL,CAAcqD,KAAd,CAAoBvB,CAAC,IAAI,CAACA,CAAC,CAACnD,QAAH,IAAemD,CAAC,CAACxC,QAA1C,CAAP;AACH;;AArEuB;;AAwE5B,MAAMmd,cAAN,CAAqB;;AAErBA,cAAc,CAAC7gB,IAAf;AAAA,mBAA2G6gB,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBA54FuG9kB,EA44FvG;AAAA,QAA4G6kB;AAA5G;AA2BAA,cAAc,CAACE,IAAf,kBAv6FuG/kB,EAu6FvG;AAAA,aAAuI,CAC/H;AAAEijB,IAAAA,OAAO,EAAEpK,uBAAX;AAAoCmM,IAAAA,QAAQ,EAAEb;AAA9C,GAD+H,CAAvI;AAAA,YAEiB,CACL9hB,YADK,CAFjB;AAAA;;AAKA;AAAA,qDA56FuGrC,EA46FvG,mBAA2F6kB,cAA3F,EAAuH,CAAC;AAC5G3gB,IAAAA,IAAI,EAAE7C,QADsG;AAE5G8C,IAAAA,IAAI,EAAE,CAAC;AACC8gB,MAAAA,YAAY,EAAE,CACVjW,wBADU,EAEVsI,iBAFU,EAGVwB,iBAHU,EAIVvU,2BAJU,EAKVF,yBALU,EAMVG,wBANU,EAOVC,6BAPU,EAQVC,yBARU,EASVC,yBATU,EAUVC,2BAVU,EAWVC,+BAXU,EAYVC,8BAZU,EAaVC,sBAbU,EAcVC,iCAdU,EAeVzB,oBAfU,CADf;AAkBC2hB,MAAAA,OAAO,EAAE,CACL7iB,YADK,CAlBV;AAqBC8iB,MAAAA,OAAO,EAAE,CACLrM,iBADK,EAELxB,iBAFK,EAGL/S,2BAHK,EAILF,yBAJK,EAKLG,wBALK,EAMLC,6BANK,EAOLC,yBAPK,EAQLC,yBARK,EASLC,2BATK,EAULC,+BAVK,EAWLC,8BAXK,EAYLC,sBAZK,EAaLC,iCAbK,CArBV;AAoCCse,MAAAA,SAAS,EAAE,CACP;AAAEL,QAAAA,OAAO,EAAEpK,uBAAX;AAAoCmM,QAAAA,QAAQ,EAAEb;AAA9C,OADO;AApCZ,KAAD;AAFsG,GAAD,CAAvH;AAAA;AA4CA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASxf,yBAAT,EAAoCD,yBAApC,EAA+DnB,oBAA/D,EAAqFiB,wBAArF,EAA+GQ,iCAA/G,EAAkJF,8BAAlJ,EAAkLL,6BAAlL,EAAiNG,2BAAjN,EAA8OL,2BAA9O,EAA2Q+S,iBAA3Q,EAA8RjT,yBAA9R,EAAyTyU,iBAAzT,EAA4Uf,cAA5U,EAA4V8M,cAA5V,EAA4W9f,sBAA5W,EAAoYF,+BAApY,EAAqagU,uBAAra", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, Injectable, EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Output, ViewChild, InjectionToken, forwardRef, TemplateRef, Attribute, HostBinding, ContentChild, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { takeUntil, auditTime, startWith, tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { animationFrameScheduler, asapScheduler, Subject, fromEvent, merge } from 'rxjs';\nimport * as i4 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\n\nconst unescapedHTMLExp = /[&<>\"']/g;\nconst hasUnescapedHTMLExp = RegExp(unescapedHTMLExp.source);\nconst htmlEscapes = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    '\\'': '&#39;'\n};\nfunction escapeHTML(value) {\n    return (value && hasUnescapedHTMLExp.test(value)) ?\n        value.replace(unescapedHTMLExp, chr => htmlEscapes[chr]) :\n        value;\n}\nfunction isDefined(value) {\n    return value !== undefined && value !== null;\n}\nfunction isObject(value) {\n    return typeof value === 'object' && isDefined(value);\n}\nfunction isPromise(value) {\n    return value instanceof Promise;\n}\nfunction isFunction(value) {\n    return value instanceof Function;\n}\n\nclass NgItemLabelDirective {\n    constructor(element) {\n        this.element = element;\n        this.escape = true;\n    }\n    ngOnChanges(changes) {\n        this.element.nativeElement.innerHTML = this.escape ?\n            escapeHTML(this.ngItemLabel) :\n            this.ngItemLabel;\n    }\n}\nNgItemLabelDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgItemLabelDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgItemLabelDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgItemLabelDirective, selector: \"[ngItemLabel]\", inputs: { ngItemLabel: \"ngItemLabel\", escape: \"escape\" }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgItemLabelDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ngItemLabel]' }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { ngItemLabel: [{\n                type: Input\n            }], escape: [{\n                type: Input\n            }] } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgOptionTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgOptionTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgOptionTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgOptionTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgOptionTemplateDirective, selector: \"[ng-option-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgOptionTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-option-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgOptgroupTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgOptgroupTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgOptgroupTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgOptgroupTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgOptgroupTemplateDirective, selector: \"[ng-optgroup-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgOptgroupTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-optgroup-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgLabelTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgLabelTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgLabelTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgLabelTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgLabelTemplateDirective, selector: \"[ng-label-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgLabelTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-label-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgMultiLabelTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgMultiLabelTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgMultiLabelTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgMultiLabelTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgMultiLabelTemplateDirective, selector: \"[ng-multi-label-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgMultiLabelTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-multi-label-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgHeaderTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgHeaderTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgHeaderTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgHeaderTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgHeaderTemplateDirective, selector: \"[ng-header-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgHeaderTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-header-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgFooterTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgFooterTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgFooterTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgFooterTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgFooterTemplateDirective, selector: \"[ng-footer-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgFooterTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-footer-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgNotFoundTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgNotFoundTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgNotFoundTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgNotFoundTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgNotFoundTemplateDirective, selector: \"[ng-notfound-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgNotFoundTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-notfound-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgTypeToSearchTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgTypeToSearchTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgTypeToSearchTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgTypeToSearchTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgTypeToSearchTemplateDirective, selector: \"[ng-typetosearch-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgTypeToSearchTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-typetosearch-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgLoadingTextTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgLoadingTextTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgLoadingTextTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgLoadingTextTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgLoadingTextTemplateDirective, selector: \"[ng-loadingtext-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgLoadingTextTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-loadingtext-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgTagTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgTagTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgTagTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgTagTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgTagTemplateDirective, selector: \"[ng-tag-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgTagTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-tag-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n// eslint-disable-next-line @angular-eslint/directive-selector\nclass NgLoadingSpinnerTemplateDirective {\n    constructor(template) {\n        this.template = template;\n    }\n}\nNgLoadingSpinnerTemplateDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgLoadingSpinnerTemplateDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nNgLoadingSpinnerTemplateDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgLoadingSpinnerTemplateDirective, selector: \"[ng-loadingspinner-tmp]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgLoadingSpinnerTemplateDirective, decorators: [{\n            type: Directive,\n            args: [{ selector: '[ng-loadingspinner-tmp]' }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; } });\n\nfunction newId() {\n    // First character is an 'a', it's good practice to tag id to begin with a letter\n    return 'axxxxxxxxxxx'.replace(/[x]/g, () => {\n        // eslint-disable-next-line no-bitwise\n        const val = Math.random() * 16 | 0;\n        return val.toString(16);\n    });\n}\n\nconst diacritics = {\n    '\\u24B6': 'A',\n    '\\uFF21': 'A',\n    '\\u00C0': 'A',\n    '\\u00C1': 'A',\n    '\\u00C2': 'A',\n    '\\u1EA6': 'A',\n    '\\u1EA4': 'A',\n    '\\u1EAA': 'A',\n    '\\u1EA8': 'A',\n    '\\u00C3': 'A',\n    '\\u0100': 'A',\n    '\\u0102': 'A',\n    '\\u1EB0': 'A',\n    '\\u1EAE': 'A',\n    '\\u1EB4': 'A',\n    '\\u1EB2': 'A',\n    '\\u0226': 'A',\n    '\\u01E0': 'A',\n    '\\u00C4': 'A',\n    '\\u01DE': 'A',\n    '\\u1EA2': 'A',\n    '\\u00C5': 'A',\n    '\\u01FA': 'A',\n    '\\u01CD': 'A',\n    '\\u0200': 'A',\n    '\\u0202': 'A',\n    '\\u1EA0': 'A',\n    '\\u1EAC': 'A',\n    '\\u1EB6': 'A',\n    '\\u1E00': 'A',\n    '\\u0104': 'A',\n    '\\u023A': 'A',\n    '\\u2C6F': 'A',\n    '\\uA732': 'AA',\n    '\\u00C6': 'AE',\n    '\\u01FC': 'AE',\n    '\\u01E2': 'AE',\n    '\\uA734': 'AO',\n    '\\uA736': 'AU',\n    '\\uA738': 'AV',\n    '\\uA73A': 'AV',\n    '\\uA73C': 'AY',\n    '\\u24B7': 'B',\n    '\\uFF22': 'B',\n    '\\u1E02': 'B',\n    '\\u1E04': 'B',\n    '\\u1E06': 'B',\n    '\\u0243': 'B',\n    '\\u0182': 'B',\n    '\\u0181': 'B',\n    '\\u24B8': 'C',\n    '\\uFF23': 'C',\n    '\\u0106': 'C',\n    '\\u0108': 'C',\n    '\\u010A': 'C',\n    '\\u010C': 'C',\n    '\\u00C7': 'C',\n    '\\u1E08': 'C',\n    '\\u0187': 'C',\n    '\\u023B': 'C',\n    '\\uA73E': 'C',\n    '\\u24B9': 'D',\n    '\\uFF24': 'D',\n    '\\u1E0A': 'D',\n    '\\u010E': 'D',\n    '\\u1E0C': 'D',\n    '\\u1E10': 'D',\n    '\\u1E12': 'D',\n    '\\u1E0E': 'D',\n    '\\u0110': 'D',\n    '\\u018B': 'D',\n    '\\u018A': 'D',\n    '\\u0189': 'D',\n    '\\uA779': 'D',\n    '\\u01F1': 'DZ',\n    '\\u01C4': 'DZ',\n    '\\u01F2': 'Dz',\n    '\\u01C5': 'Dz',\n    '\\u24BA': 'E',\n    '\\uFF25': 'E',\n    '\\u00C8': 'E',\n    '\\u00C9': 'E',\n    '\\u00CA': 'E',\n    '\\u1EC0': 'E',\n    '\\u1EBE': 'E',\n    '\\u1EC4': 'E',\n    '\\u1EC2': 'E',\n    '\\u1EBC': 'E',\n    '\\u0112': 'E',\n    '\\u1E14': 'E',\n    '\\u1E16': 'E',\n    '\\u0114': 'E',\n    '\\u0116': 'E',\n    '\\u00CB': 'E',\n    '\\u1EBA': 'E',\n    '\\u011A': 'E',\n    '\\u0204': 'E',\n    '\\u0206': 'E',\n    '\\u1EB8': 'E',\n    '\\u1EC6': 'E',\n    '\\u0228': 'E',\n    '\\u1E1C': 'E',\n    '\\u0118': 'E',\n    '\\u1E18': 'E',\n    '\\u1E1A': 'E',\n    '\\u0190': 'E',\n    '\\u018E': 'E',\n    '\\u24BB': 'F',\n    '\\uFF26': 'F',\n    '\\u1E1E': 'F',\n    '\\u0191': 'F',\n    '\\uA77B': 'F',\n    '\\u24BC': 'G',\n    '\\uFF27': 'G',\n    '\\u01F4': 'G',\n    '\\u011C': 'G',\n    '\\u1E20': 'G',\n    '\\u011E': 'G',\n    '\\u0120': 'G',\n    '\\u01E6': 'G',\n    '\\u0122': 'G',\n    '\\u01E4': 'G',\n    '\\u0193': 'G',\n    '\\uA7A0': 'G',\n    '\\uA77D': 'G',\n    '\\uA77E': 'G',\n    '\\u24BD': 'H',\n    '\\uFF28': 'H',\n    '\\u0124': 'H',\n    '\\u1E22': 'H',\n    '\\u1E26': 'H',\n    '\\u021E': 'H',\n    '\\u1E24': 'H',\n    '\\u1E28': 'H',\n    '\\u1E2A': 'H',\n    '\\u0126': 'H',\n    '\\u2C67': 'H',\n    '\\u2C75': 'H',\n    '\\uA78D': 'H',\n    '\\u24BE': 'I',\n    '\\uFF29': 'I',\n    '\\u00CC': 'I',\n    '\\u00CD': 'I',\n    '\\u00CE': 'I',\n    '\\u0128': 'I',\n    '\\u012A': 'I',\n    '\\u012C': 'I',\n    '\\u0130': 'I',\n    '\\u00CF': 'I',\n    '\\u1E2E': 'I',\n    '\\u1EC8': 'I',\n    '\\u01CF': 'I',\n    '\\u0208': 'I',\n    '\\u020A': 'I',\n    '\\u1ECA': 'I',\n    '\\u012E': 'I',\n    '\\u1E2C': 'I',\n    '\\u0197': 'I',\n    '\\u24BF': 'J',\n    '\\uFF2A': 'J',\n    '\\u0134': 'J',\n    '\\u0248': 'J',\n    '\\u24C0': 'K',\n    '\\uFF2B': 'K',\n    '\\u1E30': 'K',\n    '\\u01E8': 'K',\n    '\\u1E32': 'K',\n    '\\u0136': 'K',\n    '\\u1E34': 'K',\n    '\\u0198': 'K',\n    '\\u2C69': 'K',\n    '\\uA740': 'K',\n    '\\uA742': 'K',\n    '\\uA744': 'K',\n    '\\uA7A2': 'K',\n    '\\u24C1': 'L',\n    '\\uFF2C': 'L',\n    '\\u013F': 'L',\n    '\\u0139': 'L',\n    '\\u013D': 'L',\n    '\\u1E36': 'L',\n    '\\u1E38': 'L',\n    '\\u013B': 'L',\n    '\\u1E3C': 'L',\n    '\\u1E3A': 'L',\n    '\\u0141': 'L',\n    '\\u023D': 'L',\n    '\\u2C62': 'L',\n    '\\u2C60': 'L',\n    '\\uA748': 'L',\n    '\\uA746': 'L',\n    '\\uA780': 'L',\n    '\\u01C7': 'LJ',\n    '\\u01C8': 'Lj',\n    '\\u24C2': 'M',\n    '\\uFF2D': 'M',\n    '\\u1E3E': 'M',\n    '\\u1E40': 'M',\n    '\\u1E42': 'M',\n    '\\u2C6E': 'M',\n    '\\u019C': 'M',\n    '\\u24C3': 'N',\n    '\\uFF2E': 'N',\n    '\\u01F8': 'N',\n    '\\u0143': 'N',\n    '\\u00D1': 'N',\n    '\\u1E44': 'N',\n    '\\u0147': 'N',\n    '\\u1E46': 'N',\n    '\\u0145': 'N',\n    '\\u1E4A': 'N',\n    '\\u1E48': 'N',\n    '\\u0220': 'N',\n    '\\u019D': 'N',\n    '\\uA790': 'N',\n    '\\uA7A4': 'N',\n    '\\u01CA': 'NJ',\n    '\\u01CB': 'Nj',\n    '\\u24C4': 'O',\n    '\\uFF2F': 'O',\n    '\\u00D2': 'O',\n    '\\u00D3': 'O',\n    '\\u00D4': 'O',\n    '\\u1ED2': 'O',\n    '\\u1ED0': 'O',\n    '\\u1ED6': 'O',\n    '\\u1ED4': 'O',\n    '\\u00D5': 'O',\n    '\\u1E4C': 'O',\n    '\\u022C': 'O',\n    '\\u1E4E': 'O',\n    '\\u014C': 'O',\n    '\\u1E50': 'O',\n    '\\u1E52': 'O',\n    '\\u014E': 'O',\n    '\\u022E': 'O',\n    '\\u0230': 'O',\n    '\\u00D6': 'O',\n    '\\u022A': 'O',\n    '\\u1ECE': 'O',\n    '\\u0150': 'O',\n    '\\u01D1': 'O',\n    '\\u020C': 'O',\n    '\\u020E': 'O',\n    '\\u01A0': 'O',\n    '\\u1EDC': 'O',\n    '\\u1EDA': 'O',\n    '\\u1EE0': 'O',\n    '\\u1EDE': 'O',\n    '\\u1EE2': 'O',\n    '\\u1ECC': 'O',\n    '\\u1ED8': 'O',\n    '\\u01EA': 'O',\n    '\\u01EC': 'O',\n    '\\u00D8': 'O',\n    '\\u01FE': 'O',\n    '\\u0186': 'O',\n    '\\u019F': 'O',\n    '\\uA74A': 'O',\n    '\\uA74C': 'O',\n    '\\u01A2': 'OI',\n    '\\uA74E': 'OO',\n    '\\u0222': 'OU',\n    '\\u24C5': 'P',\n    '\\uFF30': 'P',\n    '\\u1E54': 'P',\n    '\\u1E56': 'P',\n    '\\u01A4': 'P',\n    '\\u2C63': 'P',\n    '\\uA750': 'P',\n    '\\uA752': 'P',\n    '\\uA754': 'P',\n    '\\u24C6': 'Q',\n    '\\uFF31': 'Q',\n    '\\uA756': 'Q',\n    '\\uA758': 'Q',\n    '\\u024A': 'Q',\n    '\\u24C7': 'R',\n    '\\uFF32': 'R',\n    '\\u0154': 'R',\n    '\\u1E58': 'R',\n    '\\u0158': 'R',\n    '\\u0210': 'R',\n    '\\u0212': 'R',\n    '\\u1E5A': 'R',\n    '\\u1E5C': 'R',\n    '\\u0156': 'R',\n    '\\u1E5E': 'R',\n    '\\u024C': 'R',\n    '\\u2C64': 'R',\n    '\\uA75A': 'R',\n    '\\uA7A6': 'R',\n    '\\uA782': 'R',\n    '\\u24C8': 'S',\n    '\\uFF33': 'S',\n    '\\u1E9E': 'S',\n    '\\u015A': 'S',\n    '\\u1E64': 'S',\n    '\\u015C': 'S',\n    '\\u1E60': 'S',\n    '\\u0160': 'S',\n    '\\u1E66': 'S',\n    '\\u1E62': 'S',\n    '\\u1E68': 'S',\n    '\\u0218': 'S',\n    '\\u015E': 'S',\n    '\\u2C7E': 'S',\n    '\\uA7A8': 'S',\n    '\\uA784': 'S',\n    '\\u24C9': 'T',\n    '\\uFF34': 'T',\n    '\\u1E6A': 'T',\n    '\\u0164': 'T',\n    '\\u1E6C': 'T',\n    '\\u021A': 'T',\n    '\\u0162': 'T',\n    '\\u1E70': 'T',\n    '\\u1E6E': 'T',\n    '\\u0166': 'T',\n    '\\u01AC': 'T',\n    '\\u01AE': 'T',\n    '\\u023E': 'T',\n    '\\uA786': 'T',\n    '\\uA728': 'TZ',\n    '\\u24CA': 'U',\n    '\\uFF35': 'U',\n    '\\u00D9': 'U',\n    '\\u00DA': 'U',\n    '\\u00DB': 'U',\n    '\\u0168': 'U',\n    '\\u1E78': 'U',\n    '\\u016A': 'U',\n    '\\u1E7A': 'U',\n    '\\u016C': 'U',\n    '\\u00DC': 'U',\n    '\\u01DB': 'U',\n    '\\u01D7': 'U',\n    '\\u01D5': 'U',\n    '\\u01D9': 'U',\n    '\\u1EE6': 'U',\n    '\\u016E': 'U',\n    '\\u0170': 'U',\n    '\\u01D3': 'U',\n    '\\u0214': 'U',\n    '\\u0216': 'U',\n    '\\u01AF': 'U',\n    '\\u1EEA': 'U',\n    '\\u1EE8': 'U',\n    '\\u1EEE': 'U',\n    '\\u1EEC': 'U',\n    '\\u1EF0': 'U',\n    '\\u1EE4': 'U',\n    '\\u1E72': 'U',\n    '\\u0172': 'U',\n    '\\u1E76': 'U',\n    '\\u1E74': 'U',\n    '\\u0244': 'U',\n    '\\u24CB': 'V',\n    '\\uFF36': 'V',\n    '\\u1E7C': 'V',\n    '\\u1E7E': 'V',\n    '\\u01B2': 'V',\n    '\\uA75E': 'V',\n    '\\u0245': 'V',\n    '\\uA760': 'VY',\n    '\\u24CC': 'W',\n    '\\uFF37': 'W',\n    '\\u1E80': 'W',\n    '\\u1E82': 'W',\n    '\\u0174': 'W',\n    '\\u1E86': 'W',\n    '\\u1E84': 'W',\n    '\\u1E88': 'W',\n    '\\u2C72': 'W',\n    '\\u24CD': 'X',\n    '\\uFF38': 'X',\n    '\\u1E8A': 'X',\n    '\\u1E8C': 'X',\n    '\\u24CE': 'Y',\n    '\\uFF39': 'Y',\n    '\\u1EF2': 'Y',\n    '\\u00DD': 'Y',\n    '\\u0176': 'Y',\n    '\\u1EF8': 'Y',\n    '\\u0232': 'Y',\n    '\\u1E8E': 'Y',\n    '\\u0178': 'Y',\n    '\\u1EF6': 'Y',\n    '\\u1EF4': 'Y',\n    '\\u01B3': 'Y',\n    '\\u024E': 'Y',\n    '\\u1EFE': 'Y',\n    '\\u24CF': 'Z',\n    '\\uFF3A': 'Z',\n    '\\u0179': 'Z',\n    '\\u1E90': 'Z',\n    '\\u017B': 'Z',\n    '\\u017D': 'Z',\n    '\\u1E92': 'Z',\n    '\\u1E94': 'Z',\n    '\\u01B5': 'Z',\n    '\\u0224': 'Z',\n    '\\u2C7F': 'Z',\n    '\\u2C6B': 'Z',\n    '\\uA762': 'Z',\n    '\\u24D0': 'a',\n    '\\uFF41': 'a',\n    '\\u1E9A': 'a',\n    '\\u00E0': 'a',\n    '\\u00E1': 'a',\n    '\\u00E2': 'a',\n    '\\u1EA7': 'a',\n    '\\u1EA5': 'a',\n    '\\u1EAB': 'a',\n    '\\u1EA9': 'a',\n    '\\u00E3': 'a',\n    '\\u0101': 'a',\n    '\\u0103': 'a',\n    '\\u1EB1': 'a',\n    '\\u1EAF': 'a',\n    '\\u1EB5': 'a',\n    '\\u1EB3': 'a',\n    '\\u0227': 'a',\n    '\\u01E1': 'a',\n    '\\u00E4': 'a',\n    '\\u01DF': 'a',\n    '\\u1EA3': 'a',\n    '\\u00E5': 'a',\n    '\\u01FB': 'a',\n    '\\u01CE': 'a',\n    '\\u0201': 'a',\n    '\\u0203': 'a',\n    '\\u1EA1': 'a',\n    '\\u1EAD': 'a',\n    '\\u1EB7': 'a',\n    '\\u1E01': 'a',\n    '\\u0105': 'a',\n    '\\u2C65': 'a',\n    '\\u0250': 'a',\n    '\\uA733': 'aa',\n    '\\u00E6': 'ae',\n    '\\u01FD': 'ae',\n    '\\u01E3': 'ae',\n    '\\uA735': 'ao',\n    '\\uA737': 'au',\n    '\\uA739': 'av',\n    '\\uA73B': 'av',\n    '\\uA73D': 'ay',\n    '\\u24D1': 'b',\n    '\\uFF42': 'b',\n    '\\u1E03': 'b',\n    '\\u1E05': 'b',\n    '\\u1E07': 'b',\n    '\\u0180': 'b',\n    '\\u0183': 'b',\n    '\\u0253': 'b',\n    '\\u24D2': 'c',\n    '\\uFF43': 'c',\n    '\\u0107': 'c',\n    '\\u0109': 'c',\n    '\\u010B': 'c',\n    '\\u010D': 'c',\n    '\\u00E7': 'c',\n    '\\u1E09': 'c',\n    '\\u0188': 'c',\n    '\\u023C': 'c',\n    '\\uA73F': 'c',\n    '\\u2184': 'c',\n    '\\u24D3': 'd',\n    '\\uFF44': 'd',\n    '\\u1E0B': 'd',\n    '\\u010F': 'd',\n    '\\u1E0D': 'd',\n    '\\u1E11': 'd',\n    '\\u1E13': 'd',\n    '\\u1E0F': 'd',\n    '\\u0111': 'd',\n    '\\u018C': 'd',\n    '\\u0256': 'd',\n    '\\u0257': 'd',\n    '\\uA77A': 'd',\n    '\\u01F3': 'dz',\n    '\\u01C6': 'dz',\n    '\\u24D4': 'e',\n    '\\uFF45': 'e',\n    '\\u00E8': 'e',\n    '\\u00E9': 'e',\n    '\\u00EA': 'e',\n    '\\u1EC1': 'e',\n    '\\u1EBF': 'e',\n    '\\u1EC5': 'e',\n    '\\u1EC3': 'e',\n    '\\u1EBD': 'e',\n    '\\u0113': 'e',\n    '\\u1E15': 'e',\n    '\\u1E17': 'e',\n    '\\u0115': 'e',\n    '\\u0117': 'e',\n    '\\u00EB': 'e',\n    '\\u1EBB': 'e',\n    '\\u011B': 'e',\n    '\\u0205': 'e',\n    '\\u0207': 'e',\n    '\\u1EB9': 'e',\n    '\\u1EC7': 'e',\n    '\\u0229': 'e',\n    '\\u1E1D': 'e',\n    '\\u0119': 'e',\n    '\\u1E19': 'e',\n    '\\u1E1B': 'e',\n    '\\u0247': 'e',\n    '\\u025B': 'e',\n    '\\u01DD': 'e',\n    '\\u24D5': 'f',\n    '\\uFF46': 'f',\n    '\\u1E1F': 'f',\n    '\\u0192': 'f',\n    '\\uA77C': 'f',\n    '\\u24D6': 'g',\n    '\\uFF47': 'g',\n    '\\u01F5': 'g',\n    '\\u011D': 'g',\n    '\\u1E21': 'g',\n    '\\u011F': 'g',\n    '\\u0121': 'g',\n    '\\u01E7': 'g',\n    '\\u0123': 'g',\n    '\\u01E5': 'g',\n    '\\u0260': 'g',\n    '\\uA7A1': 'g',\n    '\\u1D79': 'g',\n    '\\uA77F': 'g',\n    '\\u24D7': 'h',\n    '\\uFF48': 'h',\n    '\\u0125': 'h',\n    '\\u1E23': 'h',\n    '\\u1E27': 'h',\n    '\\u021F': 'h',\n    '\\u1E25': 'h',\n    '\\u1E29': 'h',\n    '\\u1E2B': 'h',\n    '\\u1E96': 'h',\n    '\\u0127': 'h',\n    '\\u2C68': 'h',\n    '\\u2C76': 'h',\n    '\\u0265': 'h',\n    '\\u0195': 'hv',\n    '\\u24D8': 'i',\n    '\\uFF49': 'i',\n    '\\u00EC': 'i',\n    '\\u00ED': 'i',\n    '\\u00EE': 'i',\n    '\\u0129': 'i',\n    '\\u012B': 'i',\n    '\\u012D': 'i',\n    '\\u00EF': 'i',\n    '\\u1E2F': 'i',\n    '\\u1EC9': 'i',\n    '\\u01D0': 'i',\n    '\\u0209': 'i',\n    '\\u020B': 'i',\n    '\\u1ECB': 'i',\n    '\\u012F': 'i',\n    '\\u1E2D': 'i',\n    '\\u0268': 'i',\n    '\\u0131': 'i',\n    '\\u24D9': 'j',\n    '\\uFF4A': 'j',\n    '\\u0135': 'j',\n    '\\u01F0': 'j',\n    '\\u0249': 'j',\n    '\\u24DA': 'k',\n    '\\uFF4B': 'k',\n    '\\u1E31': 'k',\n    '\\u01E9': 'k',\n    '\\u1E33': 'k',\n    '\\u0137': 'k',\n    '\\u1E35': 'k',\n    '\\u0199': 'k',\n    '\\u2C6A': 'k',\n    '\\uA741': 'k',\n    '\\uA743': 'k',\n    '\\uA745': 'k',\n    '\\uA7A3': 'k',\n    '\\u24DB': 'l',\n    '\\uFF4C': 'l',\n    '\\u0140': 'l',\n    '\\u013A': 'l',\n    '\\u013E': 'l',\n    '\\u1E37': 'l',\n    '\\u1E39': 'l',\n    '\\u013C': 'l',\n    '\\u1E3D': 'l',\n    '\\u1E3B': 'l',\n    '\\u017F': 'l',\n    '\\u0142': 'l',\n    '\\u019A': 'l',\n    '\\u026B': 'l',\n    '\\u2C61': 'l',\n    '\\uA749': 'l',\n    '\\uA781': 'l',\n    '\\uA747': 'l',\n    '\\u01C9': 'lj',\n    '\\u24DC': 'm',\n    '\\uFF4D': 'm',\n    '\\u1E3F': 'm',\n    '\\u1E41': 'm',\n    '\\u1E43': 'm',\n    '\\u0271': 'm',\n    '\\u026F': 'm',\n    '\\u24DD': 'n',\n    '\\uFF4E': 'n',\n    '\\u01F9': 'n',\n    '\\u0144': 'n',\n    '\\u00F1': 'n',\n    '\\u1E45': 'n',\n    '\\u0148': 'n',\n    '\\u1E47': 'n',\n    '\\u0146': 'n',\n    '\\u1E4B': 'n',\n    '\\u1E49': 'n',\n    '\\u019E': 'n',\n    '\\u0272': 'n',\n    '\\u0149': 'n',\n    '\\uA791': 'n',\n    '\\uA7A5': 'n',\n    '\\u01CC': 'nj',\n    '\\u24DE': 'o',\n    '\\uFF4F': 'o',\n    '\\u00F2': 'o',\n    '\\u00F3': 'o',\n    '\\u00F4': 'o',\n    '\\u1ED3': 'o',\n    '\\u1ED1': 'o',\n    '\\u1ED7': 'o',\n    '\\u1ED5': 'o',\n    '\\u00F5': 'o',\n    '\\u1E4D': 'o',\n    '\\u022D': 'o',\n    '\\u1E4F': 'o',\n    '\\u014D': 'o',\n    '\\u1E51': 'o',\n    '\\u1E53': 'o',\n    '\\u014F': 'o',\n    '\\u022F': 'o',\n    '\\u0231': 'o',\n    '\\u00F6': 'o',\n    '\\u022B': 'o',\n    '\\u1ECF': 'o',\n    '\\u0151': 'o',\n    '\\u01D2': 'o',\n    '\\u020D': 'o',\n    '\\u020F': 'o',\n    '\\u01A1': 'o',\n    '\\u1EDD': 'o',\n    '\\u1EDB': 'o',\n    '\\u1EE1': 'o',\n    '\\u1EDF': 'o',\n    '\\u1EE3': 'o',\n    '\\u1ECD': 'o',\n    '\\u1ED9': 'o',\n    '\\u01EB': 'o',\n    '\\u01ED': 'o',\n    '\\u00F8': 'o',\n    '\\u01FF': 'o',\n    '\\u0254': 'o',\n    '\\uA74B': 'o',\n    '\\uA74D': 'o',\n    '\\u0275': 'o',\n    '\\u01A3': 'oi',\n    '\\u0223': 'ou',\n    '\\uA74F': 'oo',\n    '\\u24DF': 'p',\n    '\\uFF50': 'p',\n    '\\u1E55': 'p',\n    '\\u1E57': 'p',\n    '\\u01A5': 'p',\n    '\\u1D7D': 'p',\n    '\\uA751': 'p',\n    '\\uA753': 'p',\n    '\\uA755': 'p',\n    '\\u24E0': 'q',\n    '\\uFF51': 'q',\n    '\\u024B': 'q',\n    '\\uA757': 'q',\n    '\\uA759': 'q',\n    '\\u24E1': 'r',\n    '\\uFF52': 'r',\n    '\\u0155': 'r',\n    '\\u1E59': 'r',\n    '\\u0159': 'r',\n    '\\u0211': 'r',\n    '\\u0213': 'r',\n    '\\u1E5B': 'r',\n    '\\u1E5D': 'r',\n    '\\u0157': 'r',\n    '\\u1E5F': 'r',\n    '\\u024D': 'r',\n    '\\u027D': 'r',\n    '\\uA75B': 'r',\n    '\\uA7A7': 'r',\n    '\\uA783': 'r',\n    '\\u24E2': 's',\n    '\\uFF53': 's',\n    '\\u00DF': 's',\n    '\\u015B': 's',\n    '\\u1E65': 's',\n    '\\u015D': 's',\n    '\\u1E61': 's',\n    '\\u0161': 's',\n    '\\u1E67': 's',\n    '\\u1E63': 's',\n    '\\u1E69': 's',\n    '\\u0219': 's',\n    '\\u015F': 's',\n    '\\u023F': 's',\n    '\\uA7A9': 's',\n    '\\uA785': 's',\n    '\\u1E9B': 's',\n    '\\u24E3': 't',\n    '\\uFF54': 't',\n    '\\u1E6B': 't',\n    '\\u1E97': 't',\n    '\\u0165': 't',\n    '\\u1E6D': 't',\n    '\\u021B': 't',\n    '\\u0163': 't',\n    '\\u1E71': 't',\n    '\\u1E6F': 't',\n    '\\u0167': 't',\n    '\\u01AD': 't',\n    '\\u0288': 't',\n    '\\u2C66': 't',\n    '\\uA787': 't',\n    '\\uA729': 'tz',\n    '\\u24E4': 'u',\n    '\\uFF55': 'u',\n    '\\u00F9': 'u',\n    '\\u00FA': 'u',\n    '\\u00FB': 'u',\n    '\\u0169': 'u',\n    '\\u1E79': 'u',\n    '\\u016B': 'u',\n    '\\u1E7B': 'u',\n    '\\u016D': 'u',\n    '\\u00FC': 'u',\n    '\\u01DC': 'u',\n    '\\u01D8': 'u',\n    '\\u01D6': 'u',\n    '\\u01DA': 'u',\n    '\\u1EE7': 'u',\n    '\\u016F': 'u',\n    '\\u0171': 'u',\n    '\\u01D4': 'u',\n    '\\u0215': 'u',\n    '\\u0217': 'u',\n    '\\u01B0': 'u',\n    '\\u1EEB': 'u',\n    '\\u1EE9': 'u',\n    '\\u1EEF': 'u',\n    '\\u1EED': 'u',\n    '\\u1EF1': 'u',\n    '\\u1EE5': 'u',\n    '\\u1E73': 'u',\n    '\\u0173': 'u',\n    '\\u1E77': 'u',\n    '\\u1E75': 'u',\n    '\\u0289': 'u',\n    '\\u24E5': 'v',\n    '\\uFF56': 'v',\n    '\\u1E7D': 'v',\n    '\\u1E7F': 'v',\n    '\\u028B': 'v',\n    '\\uA75F': 'v',\n    '\\u028C': 'v',\n    '\\uA761': 'vy',\n    '\\u24E6': 'w',\n    '\\uFF57': 'w',\n    '\\u1E81': 'w',\n    '\\u1E83': 'w',\n    '\\u0175': 'w',\n    '\\u1E87': 'w',\n    '\\u1E85': 'w',\n    '\\u1E98': 'w',\n    '\\u1E89': 'w',\n    '\\u2C73': 'w',\n    '\\u24E7': 'x',\n    '\\uFF58': 'x',\n    '\\u1E8B': 'x',\n    '\\u1E8D': 'x',\n    '\\u24E8': 'y',\n    '\\uFF59': 'y',\n    '\\u1EF3': 'y',\n    '\\u00FD': 'y',\n    '\\u0177': 'y',\n    '\\u1EF9': 'y',\n    '\\u0233': 'y',\n    '\\u1E8F': 'y',\n    '\\u00FF': 'y',\n    '\\u1EF7': 'y',\n    '\\u1E99': 'y',\n    '\\u1EF5': 'y',\n    '\\u01B4': 'y',\n    '\\u024F': 'y',\n    '\\u1EFF': 'y',\n    '\\u24E9': 'z',\n    '\\uFF5A': 'z',\n    '\\u017A': 'z',\n    '\\u1E91': 'z',\n    '\\u017C': 'z',\n    '\\u017E': 'z',\n    '\\u1E93': 'z',\n    '\\u1E95': 'z',\n    '\\u01B6': 'z',\n    '\\u0225': 'z',\n    '\\u0240': 'z',\n    '\\u2C6C': 'z',\n    '\\uA763': 'z',\n    '\\u0386': '\\u0391',\n    '\\u0388': '\\u0395',\n    '\\u0389': '\\u0397',\n    '\\u038A': '\\u0399',\n    '\\u03AA': '\\u0399',\n    '\\u038C': '\\u039F',\n    '\\u038E': '\\u03A5',\n    '\\u03AB': '\\u03A5',\n    '\\u038F': '\\u03A9',\n    '\\u03AC': '\\u03B1',\n    '\\u03AD': '\\u03B5',\n    '\\u03AE': '\\u03B7',\n    '\\u03AF': '\\u03B9',\n    '\\u03CA': '\\u03B9',\n    '\\u0390': '\\u03B9',\n    '\\u03CC': '\\u03BF',\n    '\\u03CD': '\\u03C5',\n    '\\u03CB': '\\u03C5',\n    '\\u03B0': '\\u03C5',\n    '\\u03C9': '\\u03C9',\n    '\\u03C2': '\\u03C3'\n};\nfunction stripSpecialChars(text) {\n    const match = (a) => diacritics[a] || a;\n    return text.replace(/[^\\u0000-\\u007E]/g, match);\n}\n\nclass ItemsList {\n    constructor(_ngSelect, _selectionModel) {\n        this._ngSelect = _ngSelect;\n        this._selectionModel = _selectionModel;\n        this._items = [];\n        this._filteredItems = [];\n        this._markedIndex = -1;\n    }\n    get items() {\n        return this._items;\n    }\n    get filteredItems() {\n        return this._filteredItems;\n    }\n    get markedIndex() {\n        return this._markedIndex;\n    }\n    get selectedItems() {\n        return this._selectionModel.value;\n    }\n    get markedItem() {\n        return this._filteredItems[this._markedIndex];\n    }\n    get noItemsToSelect() {\n        return this._ngSelect.hideSelected && this._items.length === this.selectedItems.length;\n    }\n    get maxItemsSelected() {\n        return this._ngSelect.multiple && this._ngSelect.maxSelectedItems <= this.selectedItems.length;\n    }\n    get lastSelectedItem() {\n        let i = this.selectedItems.length - 1;\n        for (; i >= 0; i--) {\n            const item = this.selectedItems[i];\n            if (!item.disabled) {\n                return item;\n            }\n        }\n        return null;\n    }\n    setItems(items) {\n        this._items = items.map((item, index) => this.mapItem(item, index));\n        if (this._ngSelect.groupBy) {\n            this._groups = this._groupBy(this._items, this._ngSelect.groupBy);\n            this._items = this._flatten(this._groups);\n        }\n        else {\n            this._groups = new Map();\n            this._groups.set(undefined, this._items);\n        }\n        this._filteredItems = [...this._items];\n    }\n    select(item) {\n        if (item.selected || this.maxItemsSelected) {\n            return;\n        }\n        const multiple = this._ngSelect.multiple;\n        if (!multiple) {\n            this.clearSelected();\n        }\n        this._selectionModel.select(item, multiple, this._ngSelect.selectableGroupAsModel);\n        if (this._ngSelect.hideSelected) {\n            this._hideSelected(item);\n        }\n    }\n    unselect(item) {\n        if (!item.selected) {\n            return;\n        }\n        this._selectionModel.unselect(item, this._ngSelect.multiple);\n        if (this._ngSelect.hideSelected && isDefined(item.index) && this._ngSelect.multiple) {\n            this._showSelected(item);\n        }\n    }\n    findItem(value) {\n        let findBy;\n        if (this._ngSelect.compareWith) {\n            findBy = item => this._ngSelect.compareWith(item.value, value);\n        }\n        else if (this._ngSelect.bindValue) {\n            findBy = item => !item.children && this.resolveNested(item.value, this._ngSelect.bindValue) === value;\n        }\n        else {\n            findBy = item => item.value === value ||\n                !item.children && item.label && item.label === this.resolveNested(value, this._ngSelect.bindLabel);\n        }\n        return this._items.find(item => findBy(item));\n    }\n    addItem(item) {\n        const option = this.mapItem(item, this._items.length);\n        this._items.push(option);\n        this._filteredItems.push(option);\n        return option;\n    }\n    clearSelected(keepDisabled = false) {\n        this._selectionModel.clear(keepDisabled);\n        this._items.forEach(item => {\n            item.selected = keepDisabled && item.selected && item.disabled;\n            item.marked = false;\n        });\n        if (this._ngSelect.hideSelected) {\n            this.resetFilteredItems();\n        }\n    }\n    findByLabel(term) {\n        term = stripSpecialChars(term).toLocaleLowerCase();\n        return this.filteredItems.find(item => {\n            const label = stripSpecialChars(item.label).toLocaleLowerCase();\n            return label.substr(0, term.length) === term;\n        });\n    }\n    filter(term) {\n        if (!term) {\n            this.resetFilteredItems();\n            return;\n        }\n        this._filteredItems = [];\n        term = this._ngSelect.searchFn ? term : stripSpecialChars(term).toLocaleLowerCase();\n        const match = this._ngSelect.searchFn || this._defaultSearchFn;\n        const hideSelected = this._ngSelect.hideSelected;\n        for (const key of Array.from(this._groups.keys())) {\n            const matchedItems = [];\n            for (const item of this._groups.get(key)) {\n                if (hideSelected && (item.parent && item.parent.selected || item.selected)) {\n                    continue;\n                }\n                const searchItem = this._ngSelect.searchFn ? item.value : item;\n                if (match(term, searchItem)) {\n                    matchedItems.push(item);\n                }\n            }\n            if (matchedItems.length > 0) {\n                const [last] = matchedItems.slice(-1);\n                if (last.parent) {\n                    const head = this._items.find(x => x === last.parent);\n                    this._filteredItems.push(head);\n                }\n                this._filteredItems.push(...matchedItems);\n            }\n        }\n    }\n    resetFilteredItems() {\n        if (this._filteredItems.length === this._items.length) {\n            return;\n        }\n        if (this._ngSelect.hideSelected && this.selectedItems.length > 0) {\n            this._filteredItems = this._items.filter(x => !x.selected);\n        }\n        else {\n            this._filteredItems = this._items;\n        }\n    }\n    unmarkItem() {\n        this._markedIndex = -1;\n    }\n    markNextItem() {\n        this._stepToItem(+1);\n    }\n    markPreviousItem() {\n        this._stepToItem(-1);\n    }\n    markItem(item) {\n        this._markedIndex = this._filteredItems.indexOf(item);\n    }\n    markSelectedOrDefault(markDefault) {\n        if (this._filteredItems.length === 0) {\n            return;\n        }\n        const lastMarkedIndex = this._getLastMarkedIndex();\n        if (lastMarkedIndex > -1) {\n            this._markedIndex = lastMarkedIndex;\n        }\n        else {\n            this._markedIndex = markDefault ? this.filteredItems.findIndex(x => !x.disabled) : -1;\n        }\n    }\n    resolveNested(option, key) {\n        if (!isObject(option)) {\n            return option;\n        }\n        if (key.indexOf('.') === -1) {\n            return option[key];\n        }\n        else {\n            const keys = key.split('.');\n            let value = option;\n            for (let i = 0, len = keys.length; i < len; ++i) {\n                if (value == null) {\n                    return null;\n                }\n                value = value[keys[i]];\n            }\n            return value;\n        }\n    }\n    mapItem(item, index) {\n        const label = isDefined(item.$ngOptionLabel) ? item.$ngOptionLabel : this.resolveNested(item, this._ngSelect.bindLabel);\n        const value = isDefined(item.$ngOptionValue) ? item.$ngOptionValue : item;\n        return {\n            index,\n            label: isDefined(label) ? label.toString() : '',\n            value,\n            disabled: item.disabled,\n            htmlId: `${this._ngSelect.dropdownId}-${index}`,\n        };\n    }\n    mapSelectedItems() {\n        const multiple = this._ngSelect.multiple;\n        for (const selected of this.selectedItems) {\n            const value = this._ngSelect.bindValue ? this.resolveNested(selected.value, this._ngSelect.bindValue) : selected.value;\n            const item = isDefined(value) ? this.findItem(value) : null;\n            this._selectionModel.unselect(selected, multiple);\n            this._selectionModel.select(item || selected, multiple, this._ngSelect.selectableGroupAsModel);\n        }\n        if (this._ngSelect.hideSelected) {\n            this._filteredItems = this.filteredItems.filter(x => this.selectedItems.indexOf(x) === -1);\n        }\n    }\n    _showSelected(item) {\n        this._filteredItems.push(item);\n        if (item.parent) {\n            const parent = item.parent;\n            const parentExists = this._filteredItems.find(x => x === parent);\n            if (!parentExists) {\n                this._filteredItems.push(parent);\n            }\n        }\n        else if (item.children) {\n            for (const child of item.children) {\n                child.selected = false;\n                this._filteredItems.push(child);\n            }\n        }\n        this._filteredItems = [...this._filteredItems.sort((a, b) => (a.index - b.index))];\n    }\n    _hideSelected(item) {\n        this._filteredItems = this._filteredItems.filter(x => x !== item);\n        if (item.parent) {\n            const children = item.parent.children;\n            if (children.every(x => x.selected)) {\n                this._filteredItems = this._filteredItems.filter(x => x !== item.parent);\n            }\n        }\n        else if (item.children) {\n            this._filteredItems = this.filteredItems.filter(x => x.parent !== item);\n        }\n    }\n    _defaultSearchFn(search, opt) {\n        const label = stripSpecialChars(opt.label).toLocaleLowerCase();\n        return label.indexOf(search) > -1;\n    }\n    _getNextItemIndex(steps) {\n        if (steps > 0) {\n            return (this._markedIndex >= this._filteredItems.length - 1) ? 0 : (this._markedIndex + 1);\n        }\n        return (this._markedIndex <= 0) ? (this._filteredItems.length - 1) : (this._markedIndex - 1);\n    }\n    _stepToItem(steps) {\n        if (this._filteredItems.length === 0 || this._filteredItems.every(x => x.disabled)) {\n            return;\n        }\n        this._markedIndex = this._getNextItemIndex(steps);\n        if (this.markedItem.disabled) {\n            this._stepToItem(steps);\n        }\n    }\n    _getLastMarkedIndex() {\n        if (this._ngSelect.hideSelected) {\n            return -1;\n        }\n        if (this._markedIndex > -1 && this.markedItem === undefined) {\n            return -1;\n        }\n        const selectedIndex = this._filteredItems.indexOf(this.lastSelectedItem);\n        if (this.lastSelectedItem && selectedIndex < 0) {\n            return -1;\n        }\n        return Math.max(this.markedIndex, selectedIndex);\n    }\n    _groupBy(items, prop) {\n        const groups = new Map();\n        if (items.length === 0) {\n            return groups;\n        }\n        // Check if items are already grouped by given key.\n        if (Array.isArray(items[0].value[prop])) {\n            for (const item of items) {\n                const children = (item.value[prop] || []).map((x, index) => this.mapItem(x, index));\n                groups.set(item, children);\n            }\n            return groups;\n        }\n        const isFnKey = isFunction(this._ngSelect.groupBy);\n        const keyFn = (item) => {\n            const key = isFnKey ? prop(item.value) : item.value[prop];\n            return isDefined(key) ? key : undefined;\n        };\n        // Group items by key.\n        for (const item of items) {\n            const key = keyFn(item);\n            const group = groups.get(key);\n            if (group) {\n                group.push(item);\n            }\n            else {\n                groups.set(key, [item]);\n            }\n        }\n        return groups;\n    }\n    _flatten(groups) {\n        const isGroupByFn = isFunction(this._ngSelect.groupBy);\n        const items = [];\n        for (const key of Array.from(groups.keys())) {\n            let i = items.length;\n            if (key === undefined) {\n                const withoutGroup = groups.get(undefined) || [];\n                items.push(...withoutGroup.map(x => {\n                    x.index = i++;\n                    return x;\n                }));\n                continue;\n            }\n            const isObjectKey = isObject(key);\n            const parent = {\n                label: isObjectKey ? '' : String(key),\n                children: undefined,\n                parent: null,\n                index: i++,\n                disabled: !this._ngSelect.selectableGroup,\n                htmlId: newId(),\n            };\n            const groupKey = isGroupByFn ? this._ngSelect.bindLabel : this._ngSelect.groupBy;\n            const groupValue = this._ngSelect.groupValue || (() => {\n                if (isObjectKey) {\n                    return key.value;\n                }\n                return { [groupKey]: key };\n            });\n            const children = groups.get(key).map(x => {\n                x.parent = parent;\n                x.children = undefined;\n                x.index = i++;\n                return x;\n            });\n            parent.children = children;\n            parent.value = groupValue(key, children.map(x => x.value));\n            items.push(parent);\n            items.push(...children);\n        }\n        return items;\n    }\n}\n\nvar KeyCode;\n(function (KeyCode) {\n    KeyCode[KeyCode[\"Tab\"] = 9] = \"Tab\";\n    KeyCode[KeyCode[\"Enter\"] = 13] = \"Enter\";\n    KeyCode[KeyCode[\"Esc\"] = 27] = \"Esc\";\n    KeyCode[KeyCode[\"Space\"] = 32] = \"Space\";\n    KeyCode[KeyCode[\"ArrowUp\"] = 38] = \"ArrowUp\";\n    KeyCode[KeyCode[\"ArrowDown\"] = 40] = \"ArrowDown\";\n    KeyCode[KeyCode[\"Backspace\"] = 8] = \"Backspace\";\n})(KeyCode || (KeyCode = {}));\n\nclass NgDropdownPanelService {\n    constructor() {\n        this._dimensions = {\n            itemHeight: 0,\n            panelHeight: 0,\n            itemsPerViewport: 0\n        };\n    }\n    get dimensions() {\n        return this._dimensions;\n    }\n    calculateItems(scrollPos, itemsLength, buffer) {\n        const d = this._dimensions;\n        const scrollHeight = d.itemHeight * itemsLength;\n        const scrollTop = Math.max(0, scrollPos);\n        const indexByScrollTop = scrollTop / scrollHeight * itemsLength;\n        let end = Math.min(itemsLength, Math.ceil(indexByScrollTop) + (d.itemsPerViewport + 1));\n        const maxStartEnd = end;\n        const maxStart = Math.max(0, maxStartEnd - d.itemsPerViewport);\n        let start = Math.min(maxStart, Math.floor(indexByScrollTop));\n        let topPadding = d.itemHeight * Math.ceil(start) - (d.itemHeight * Math.min(start, buffer));\n        topPadding = !isNaN(topPadding) ? topPadding : 0;\n        start = !isNaN(start) ? start : -1;\n        end = !isNaN(end) ? end : -1;\n        start -= buffer;\n        start = Math.max(0, start);\n        end += buffer;\n        end = Math.min(itemsLength, end);\n        return {\n            topPadding,\n            scrollHeight,\n            start,\n            end\n        };\n    }\n    setDimensions(itemHeight, panelHeight) {\n        const itemsPerViewport = Math.max(1, Math.floor(panelHeight / itemHeight));\n        this._dimensions = {\n            itemHeight,\n            panelHeight,\n            itemsPerViewport\n        };\n    }\n    getScrollTo(itemTop, itemHeight, lastScroll) {\n        const { panelHeight } = this.dimensions;\n        const itemBottom = itemTop + itemHeight;\n        const top = lastScroll;\n        const bottom = top + panelHeight;\n        if (panelHeight >= itemBottom && lastScroll === itemTop) {\n            return null;\n        }\n        if (itemBottom > bottom) {\n            return top + itemBottom - bottom;\n        }\n        else if (itemTop <= top) {\n            return itemTop;\n        }\n        return null;\n    }\n}\nNgDropdownPanelService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgDropdownPanelService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nNgDropdownPanelService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgDropdownPanelService });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgDropdownPanelService, decorators: [{\n            type: Injectable\n        }] });\n\nconst CSS_POSITIONS = ['top', 'right', 'bottom', 'left'];\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\nclass NgDropdownPanelComponent {\n    constructor(_renderer, _zone, _panelService, _elementRef, _document) {\n        this._renderer = _renderer;\n        this._zone = _zone;\n        this._panelService = _panelService;\n        this._document = _document;\n        this.items = [];\n        this.position = 'auto';\n        this.virtualScroll = false;\n        this.filterValue = null;\n        this.update = new EventEmitter();\n        this.scroll = new EventEmitter();\n        this.scrollToEnd = new EventEmitter();\n        this.outsideClick = new EventEmitter();\n        this._destroy$ = new Subject();\n        this._scrollToEndFired = false;\n        this._updateScrollHeight = false;\n        this._lastScrollPosition = 0;\n        this._dropdown = _elementRef.nativeElement;\n    }\n    get currentPosition() {\n        return this._currentPosition;\n    }\n    get itemsLength() {\n        return this._itemsLength;\n    }\n    set itemsLength(value) {\n        if (value !== this._itemsLength) {\n            this._itemsLength = value;\n            this._onItemsLengthChanged();\n        }\n    }\n    get _startOffset() {\n        if (this.markedItem) {\n            const { itemHeight, panelHeight } = this._panelService.dimensions;\n            const offset = this.markedItem.index * itemHeight;\n            return panelHeight > offset ? 0 : offset;\n        }\n        return 0;\n    }\n    ngOnInit() {\n        this._select = this._dropdown.parentElement;\n        this._virtualPadding = this.paddingElementRef.nativeElement;\n        this._scrollablePanel = this.scrollElementRef.nativeElement;\n        this._contentPanel = this.contentElementRef.nativeElement;\n        this._handleScroll();\n        this._handleOutsideClick();\n        this._appendDropdown();\n        this._setupMousedownListener();\n    }\n    ngOnChanges(changes) {\n        if (changes.items) {\n            const change = changes.items;\n            this._onItemsChange(change.currentValue, change.firstChange);\n        }\n    }\n    ngOnDestroy() {\n        this._destroy$.next();\n        this._destroy$.complete();\n        this._destroy$.unsubscribe();\n        if (this.appendTo) {\n            this._renderer.removeChild(this._dropdown.parentNode, this._dropdown);\n        }\n    }\n    scrollTo(option, startFromOption = false) {\n        if (!option) {\n            return;\n        }\n        const index = this.items.indexOf(option);\n        if (index < 0 || index >= this.itemsLength) {\n            return;\n        }\n        let scrollTo;\n        if (this.virtualScroll) {\n            const itemHeight = this._panelService.dimensions.itemHeight;\n            scrollTo = this._panelService.getScrollTo(index * itemHeight, itemHeight, this._lastScrollPosition);\n        }\n        else {\n            const item = this._dropdown.querySelector(`#${option.htmlId}`);\n            const lastScroll = startFromOption ? item.offsetTop : this._lastScrollPosition;\n            scrollTo = this._panelService.getScrollTo(item.offsetTop, item.clientHeight, lastScroll);\n        }\n        if (isDefined(scrollTo)) {\n            this._scrollablePanel.scrollTop = scrollTo;\n        }\n    }\n    scrollToTag() {\n        const panel = this._scrollablePanel;\n        panel.scrollTop = panel.scrollHeight - panel.clientHeight;\n    }\n    adjustPosition() {\n        this._updateYPosition();\n    }\n    _handleDropdownPosition() {\n        this._currentPosition = this._calculateCurrentPosition(this._dropdown);\n        if (CSS_POSITIONS.includes(this._currentPosition)) {\n            this._updateDropdownClass(this._currentPosition);\n        }\n        else {\n            this._updateDropdownClass('bottom');\n        }\n        if (this.appendTo) {\n            this._updateYPosition();\n        }\n        this._dropdown.style.opacity = '1';\n    }\n    _updateDropdownClass(currentPosition) {\n        CSS_POSITIONS.forEach((position) => {\n            const REMOVE_CSS_CLASS = `ng-select-${position}`;\n            this._renderer.removeClass(this._dropdown, REMOVE_CSS_CLASS);\n            this._renderer.removeClass(this._select, REMOVE_CSS_CLASS);\n        });\n        const ADD_CSS_CLASS = `ng-select-${currentPosition}`;\n        this._renderer.addClass(this._dropdown, ADD_CSS_CLASS);\n        this._renderer.addClass(this._select, ADD_CSS_CLASS);\n    }\n    _handleScroll() {\n        this._zone.runOutsideAngular(() => {\n            fromEvent(this.scrollElementRef.nativeElement, 'scroll')\n                .pipe(takeUntil(this._destroy$), auditTime(0, SCROLL_SCHEDULER))\n                .subscribe((e) => {\n                const path = e.path || (e.composedPath && e.composedPath());\n                const scrollTop = !path || path.length === 0 ? e.target.scrollTop : path[0].scrollTop;\n                this._onContentScrolled(scrollTop);\n            });\n        });\n    }\n    _handleOutsideClick() {\n        if (!this._document) {\n            return;\n        }\n        this._zone.runOutsideAngular(() => {\n            merge(fromEvent(this._document, 'touchstart', { capture: true }), fromEvent(this._document, 'mousedown', { capture: true })).pipe(takeUntil(this._destroy$))\n                .subscribe($event => this._checkToClose($event));\n        });\n    }\n    _checkToClose($event) {\n        if (this._select.contains($event.target) || this._dropdown.contains($event.target)) {\n            return;\n        }\n        const path = $event.path || ($event.composedPath && $event.composedPath());\n        if ($event.target && $event.target.shadowRoot && path && path[0] && this._select.contains(path[0])) {\n            return;\n        }\n        this._zone.run(() => this.outsideClick.emit());\n    }\n    _onItemsChange(items, firstChange) {\n        this.items = items || [];\n        this._scrollToEndFired = false;\n        this.itemsLength = items.length;\n        if (this.virtualScroll) {\n            this._updateItemsRange(firstChange);\n        }\n        else {\n            this._setVirtualHeight();\n            this._updateItems(firstChange);\n        }\n    }\n    _updateItems(firstChange) {\n        this.update.emit(this.items);\n        if (firstChange === false) {\n            return;\n        }\n        this._zone.runOutsideAngular(() => {\n            Promise.resolve().then(() => {\n                const panelHeight = this._scrollablePanel.clientHeight;\n                this._panelService.setDimensions(0, panelHeight);\n                this._handleDropdownPosition();\n                this.scrollTo(this.markedItem, firstChange);\n            });\n        });\n    }\n    _updateItemsRange(firstChange) {\n        this._zone.runOutsideAngular(() => {\n            this._measureDimensions().then(() => {\n                if (firstChange) {\n                    this._renderItemsRange(this._startOffset);\n                    this._handleDropdownPosition();\n                }\n                else {\n                    this._renderItemsRange();\n                }\n            });\n        });\n    }\n    _onContentScrolled(scrollTop) {\n        if (this.virtualScroll) {\n            this._renderItemsRange(scrollTop);\n        }\n        this._lastScrollPosition = scrollTop;\n        this._fireScrollToEnd(scrollTop);\n    }\n    _updateVirtualHeight(height) {\n        if (this._updateScrollHeight) {\n            this._virtualPadding.style.height = `${height}px`;\n            this._updateScrollHeight = false;\n        }\n    }\n    _setVirtualHeight() {\n        if (!this._virtualPadding) {\n            return;\n        }\n        this._virtualPadding.style.height = `0px`;\n    }\n    _onItemsLengthChanged() {\n        this._updateScrollHeight = true;\n    }\n    _renderItemsRange(scrollTop = null) {\n        if (scrollTop && this._lastScrollPosition === scrollTop) {\n            return;\n        }\n        scrollTop = scrollTop || this._scrollablePanel.scrollTop;\n        const range = this._panelService.calculateItems(scrollTop, this.itemsLength, this.bufferAmount);\n        this._updateVirtualHeight(range.scrollHeight);\n        this._contentPanel.style.transform = `translateY(${range.topPadding}px)`;\n        this._zone.run(() => {\n            this.update.emit(this.items.slice(range.start, range.end));\n            this.scroll.emit({ start: range.start, end: range.end });\n        });\n        if (isDefined(scrollTop) && this._lastScrollPosition === 0) {\n            this._scrollablePanel.scrollTop = scrollTop;\n            this._lastScrollPosition = scrollTop;\n        }\n    }\n    _measureDimensions() {\n        if (this._panelService.dimensions.itemHeight > 0 || this.itemsLength === 0) {\n            return Promise.resolve(this._panelService.dimensions);\n        }\n        const [first] = this.items;\n        this.update.emit([first]);\n        return Promise.resolve().then(() => {\n            const option = this._dropdown.querySelector(`#${first.htmlId}`);\n            const optionHeight = option.clientHeight;\n            this._virtualPadding.style.height = `${optionHeight * this.itemsLength}px`;\n            const panelHeight = this._scrollablePanel.clientHeight;\n            this._panelService.setDimensions(optionHeight, panelHeight);\n            return this._panelService.dimensions;\n        });\n    }\n    _fireScrollToEnd(scrollTop) {\n        if (this._scrollToEndFired || scrollTop === 0) {\n            return;\n        }\n        const padding = this.virtualScroll ?\n            this._virtualPadding :\n            this._contentPanel;\n        if (scrollTop + this._dropdown.clientHeight >= padding.clientHeight - 1) {\n            this._zone.run(() => this.scrollToEnd.emit());\n            this._scrollToEndFired = true;\n        }\n    }\n    _calculateCurrentPosition(dropdownEl) {\n        if (this.position !== 'auto') {\n            return this.position;\n        }\n        const selectRect = this._select.getBoundingClientRect();\n        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;\n        const offsetTop = selectRect.top + window.pageYOffset;\n        const height = selectRect.height;\n        const dropdownHeight = dropdownEl.getBoundingClientRect().height;\n        if (offsetTop + height + dropdownHeight > scrollTop + document.documentElement.clientHeight) {\n            return 'top';\n        }\n        else {\n            return 'bottom';\n        }\n    }\n    _appendDropdown() {\n        if (!this.appendTo) {\n            return;\n        }\n        this._parent = document.querySelector(this.appendTo);\n        if (!this._parent) {\n            throw new Error(`appendTo selector ${this.appendTo} did not found any parent element`);\n        }\n        this._updateXPosition();\n        this._parent.appendChild(this._dropdown);\n    }\n    _updateXPosition() {\n        const select = this._select.getBoundingClientRect();\n        const parent = this._parent.getBoundingClientRect();\n        const offsetLeft = select.left - parent.left;\n        this._dropdown.style.left = offsetLeft + 'px';\n        this._dropdown.style.width = select.width + 'px';\n        this._dropdown.style.minWidth = select.width + 'px';\n    }\n    _updateYPosition() {\n        const select = this._select.getBoundingClientRect();\n        const parent = this._parent.getBoundingClientRect();\n        const delta = select.height;\n        if (this._currentPosition === 'top') {\n            const offsetBottom = parent.bottom - select.bottom;\n            this._dropdown.style.bottom = offsetBottom + delta + 'px';\n            this._dropdown.style.top = 'auto';\n        }\n        else if (this._currentPosition === 'bottom') {\n            const offsetTop = select.top - parent.top;\n            this._dropdown.style.top = offsetTop + delta + 'px';\n            this._dropdown.style.bottom = 'auto';\n        }\n    }\n    _setupMousedownListener() {\n        this._zone.runOutsideAngular(() => {\n            fromEvent(this._dropdown, 'mousedown')\n                .pipe(takeUntil(this._destroy$))\n                .subscribe((event) => {\n                const target = event.target;\n                if (target.tagName === 'INPUT') {\n                    return;\n                }\n                event.preventDefault();\n            });\n        });\n    }\n}\nNgDropdownPanelComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgDropdownPanelComponent, deps: [{ token: i0.Renderer2 }, { token: i0.NgZone }, { token: NgDropdownPanelService }, { token: i0.ElementRef }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Component });\nNgDropdownPanelComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgDropdownPanelComponent, selector: \"ng-dropdown-panel\", inputs: { items: \"items\", markedItem: \"markedItem\", position: \"position\", appendTo: \"appendTo\", bufferAmount: \"bufferAmount\", virtualScroll: \"virtualScroll\", headerTemplate: \"headerTemplate\", footerTemplate: \"footerTemplate\", filterValue: \"filterValue\" }, outputs: { update: \"update\", scroll: \"scroll\", scrollToEnd: \"scrollToEnd\", outsideClick: \"outsideClick\" }, viewQueries: [{ propertyName: \"contentElementRef\", first: true, predicate: [\"content\"], descendants: true, read: ElementRef, static: true }, { propertyName: \"scrollElementRef\", first: true, predicate: [\"scroll\"], descendants: true, read: ElementRef, static: true }, { propertyName: \"paddingElementRef\", first: true, predicate: [\"padding\"], descendants: true, read: ElementRef, static: true }], usesOnChanges: true, ngImport: i0, template: `\n        <div *ngIf=\"headerTemplate\" class=\"ng-dropdown-header\">\n            <ng-container [ngTemplateOutlet]=\"headerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n        <div #scroll class=\"ng-dropdown-panel-items scroll-host\">\n            <div #padding [class.total-padding]=\"virtualScroll\"></div>\n            <div #content [class.scrollable-content]=\"virtualScroll && items.length\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n        <div *ngIf=\"footerTemplate\" class=\"ng-dropdown-footer\">\n            <ng-container [ngTemplateOutlet]=\"footerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n    `, isInline: true, directives: [{ type: i4.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i4.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgDropdownPanelComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    selector: 'ng-dropdown-panel',\n                    template: `\n        <div *ngIf=\"headerTemplate\" class=\"ng-dropdown-header\">\n            <ng-container [ngTemplateOutlet]=\"headerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n        <div #scroll class=\"ng-dropdown-panel-items scroll-host\">\n            <div #padding [class.total-padding]=\"virtualScroll\"></div>\n            <div #content [class.scrollable-content]=\"virtualScroll && items.length\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n        <div *ngIf=\"footerTemplate\" class=\"ng-dropdown-footer\">\n            <ng-container [ngTemplateOutlet]=\"footerTemplate\" [ngTemplateOutletContext]=\"{ searchTerm: filterValue }\"></ng-container>\n        </div>\n    `\n                }]\n        }], ctorParameters: function () { return [{ type: i0.Renderer2 }, { type: i0.NgZone }, { type: NgDropdownPanelService }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { items: [{\n                type: Input\n            }], markedItem: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], bufferAmount: [{\n                type: Input\n            }], virtualScroll: [{\n                type: Input\n            }], headerTemplate: [{\n                type: Input\n            }], footerTemplate: [{\n                type: Input\n            }], filterValue: [{\n                type: Input\n            }], update: [{\n                type: Output\n            }], scroll: [{\n                type: Output\n            }], scrollToEnd: [{\n                type: Output\n            }], outsideClick: [{\n                type: Output\n            }], contentElementRef: [{\n                type: ViewChild,\n                args: ['content', { read: ElementRef, static: true }]\n            }], scrollElementRef: [{\n                type: ViewChild,\n                args: ['scroll', { read: ElementRef, static: true }]\n            }], paddingElementRef: [{\n                type: ViewChild,\n                args: ['padding', { read: ElementRef, static: true }]\n            }] } });\n\nclass NgOptionComponent {\n    constructor(elementRef) {\n        this.elementRef = elementRef;\n        this.stateChange$ = new Subject();\n        this._disabled = false;\n    }\n    get disabled() { return this._disabled; }\n    set disabled(value) { this._disabled = this._isDisabled(value); }\n    get label() {\n        return (this.elementRef.nativeElement.textContent || '').trim();\n    }\n    ngOnChanges(changes) {\n        if (changes.disabled) {\n            this.stateChange$.next({\n                value: this.value,\n                disabled: this._disabled\n            });\n        }\n    }\n    ngAfterViewChecked() {\n        if (this.label !== this._previousLabel) {\n            this._previousLabel = this.label;\n            this.stateChange$.next({\n                value: this.value,\n                disabled: this._disabled,\n                label: this.elementRef.nativeElement.innerHTML\n            });\n        }\n    }\n    ngOnDestroy() {\n        this.stateChange$.complete();\n    }\n    _isDisabled(value) {\n        return value != null && `${value}` !== 'false';\n    }\n}\nNgOptionComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgOptionComponent, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nNgOptionComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgOptionComponent, selector: \"ng-option\", inputs: { value: \"value\", disabled: \"disabled\" }, usesOnChanges: true, ngImport: i0, template: `<ng-content></ng-content>`, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgOptionComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ng-option',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `<ng-content></ng-content>`\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { value: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\nclass NgSelectConfig {\n    constructor() {\n        this.notFoundText = 'No items found';\n        this.typeToSearchText = 'Type to search';\n        this.addTagText = 'Add item';\n        this.loadingText = 'Loading...';\n        this.clearAllText = 'Clear all';\n        this.disableVirtualScroll = true;\n        this.openOnEnter = true;\n        this.appearance = 'underline';\n    }\n}\nNgSelectConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgSelectConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nNgSelectConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgSelectConfig, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgSelectConfig, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nclass ConsoleService {\n    warn(message) {\n        console.warn(message);\n    }\n}\nConsoleService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: ConsoleService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nConsoleService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: ConsoleService, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: ConsoleService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\nconst SELECTION_MODEL_FACTORY = new InjectionToken('ng-select-selection-model');\nclass NgSelectComponent {\n    constructor(classes, autoFocus, config, newSelectionModel, _elementRef, _cd, _console) {\n        this.classes = classes;\n        this.autoFocus = autoFocus;\n        this.config = config;\n        this._cd = _cd;\n        this._console = _console;\n        this.markFirst = true;\n        this.dropdownPosition = 'auto';\n        this.loading = false;\n        this.closeOnSelect = true;\n        this.hideSelected = false;\n        this.selectOnTab = false;\n        this.bufferAmount = 4;\n        this.selectableGroup = false;\n        this.selectableGroupAsModel = true;\n        this.searchFn = null;\n        this.trackByFn = null;\n        this.clearOnBackspace = true;\n        this.labelForId = null;\n        this.inputAttrs = {};\n        this.readonly = false;\n        this.searchWhileComposing = true;\n        this.minTermLength = 0;\n        this.editableSearchTerm = false;\n        this.keyDownFn = (_) => true;\n        this.multiple = false;\n        this.addTag = false;\n        this.searchable = true;\n        this.clearable = true;\n        this.isOpen = false;\n        // output events\n        this.blurEvent = new EventEmitter();\n        this.focusEvent = new EventEmitter();\n        this.changeEvent = new EventEmitter();\n        this.openEvent = new EventEmitter();\n        this.closeEvent = new EventEmitter();\n        this.searchEvent = new EventEmitter();\n        this.clearEvent = new EventEmitter();\n        this.addEvent = new EventEmitter();\n        this.removeEvent = new EventEmitter();\n        this.scroll = new EventEmitter();\n        this.scrollToEnd = new EventEmitter();\n        this.useDefaultClass = true;\n        this.viewPortItems = [];\n        this.searchTerm = null;\n        this.dropdownId = newId();\n        this.escapeHTML = true;\n        this._items = [];\n        this._defaultLabel = 'label';\n        this._pressedKeys = [];\n        this._isComposing = false;\n        this._destroy$ = new Subject();\n        this._keyPress$ = new Subject();\n        this._onChange = (_) => { };\n        this._onTouched = () => { };\n        this.clearItem = (item) => {\n            const option = this.selectedItems.find(x => x.value === item);\n            this.unselect(option);\n        };\n        this.trackByOption = (_, item) => {\n            if (this.trackByFn) {\n                return this.trackByFn(item.value);\n            }\n            return item;\n        };\n        this._mergeGlobalConfig(config);\n        this.itemsList = new ItemsList(this, newSelectionModel());\n        this.element = _elementRef.nativeElement;\n    }\n    get items() { return this._items; }\n    ;\n    set items(value) {\n        if (value === null) {\n            value = [];\n        }\n        this._itemsAreUsed = true;\n        this._items = value;\n    }\n    ;\n    get compareWith() { return this._compareWith; }\n    set compareWith(fn) {\n        if (fn !== undefined && fn !== null && !isFunction(fn)) {\n            throw Error('`compareWith` must be a function.');\n        }\n        this._compareWith = fn;\n    }\n    get clearSearchOnAdd() {\n        if (isDefined(this._clearSearchOnAdd)) {\n            return this._clearSearchOnAdd;\n        }\n        else if (isDefined(this.config.clearSearchOnAdd)) {\n            return this.config.clearSearchOnAdd;\n        }\n        return this.closeOnSelect;\n    }\n    ;\n    set clearSearchOnAdd(value) {\n        this._clearSearchOnAdd = value;\n    }\n    ;\n    get disabled() { return this.readonly || this._disabled; }\n    ;\n    get filtered() { return (!!this.searchTerm && this.searchable || this._isComposing); }\n    ;\n    get single() { return !this.multiple; }\n    ;\n    get _editableSearchTerm() {\n        return this.editableSearchTerm && !this.multiple;\n    }\n    get selectedItems() {\n        return this.itemsList.selectedItems;\n    }\n    get selectedValues() {\n        return this.selectedItems.map(x => x.value);\n    }\n    get hasValue() {\n        return this.selectedItems.length > 0;\n    }\n    get currentPanelPosition() {\n        if (this.dropdownPanel) {\n            return this.dropdownPanel.currentPosition;\n        }\n        return undefined;\n    }\n    ngOnInit() {\n        this._handleKeyPresses();\n        this._setInputAttributes();\n    }\n    ngOnChanges(changes) {\n        if (changes.multiple) {\n            this.itemsList.clearSelected();\n        }\n        if (changes.items) {\n            this._setItems(changes.items.currentValue || []);\n        }\n        if (changes.isOpen) {\n            this._manualOpen = isDefined(changes.isOpen.currentValue);\n        }\n    }\n    ngAfterViewInit() {\n        if (!this._itemsAreUsed) {\n            this.escapeHTML = false;\n            this._setItemsFromNgOptions();\n        }\n        if (isDefined(this.autoFocus)) {\n            this.focus();\n        }\n    }\n    ngOnDestroy() {\n        this._destroy$.next();\n        this._destroy$.complete();\n    }\n    handleKeyDown($event) {\n        const keyCode = KeyCode[$event.which];\n        if (keyCode) {\n            if (this.keyDownFn($event) === false) {\n                return;\n            }\n            this.handleKeyCode($event);\n        }\n        else if ($event.key && $event.key.length === 1) {\n            this._keyPress$.next($event.key.toLocaleLowerCase());\n        }\n    }\n    handleKeyCode($event) {\n        switch ($event.which) {\n            case KeyCode.ArrowDown:\n                this._handleArrowDown($event);\n                break;\n            case KeyCode.ArrowUp:\n                this._handleArrowUp($event);\n                break;\n            case KeyCode.Space:\n                this._handleSpace($event);\n                break;\n            case KeyCode.Enter:\n                this._handleEnter($event);\n                break;\n            case KeyCode.Tab:\n                this._handleTab($event);\n                break;\n            case KeyCode.Esc:\n                this.close();\n                $event.preventDefault();\n                break;\n            case KeyCode.Backspace:\n                this._handleBackspace();\n                break;\n        }\n    }\n    handleMousedown($event) {\n        const target = $event.target;\n        if (target.tagName !== 'INPUT') {\n            $event.preventDefault();\n        }\n        if (target.classList.contains('ng-clear-wrapper')) {\n            this.handleClearClick();\n            return;\n        }\n        if (target.classList.contains('ng-arrow-wrapper')) {\n            this.handleArrowClick();\n            return;\n        }\n        if (target.classList.contains('ng-value-icon')) {\n            return;\n        }\n        if (!this.focused) {\n            this.focus();\n        }\n        if (this.searchable) {\n            this.open();\n        }\n        else {\n            this.toggle();\n        }\n    }\n    handleArrowClick() {\n        if (this.isOpen) {\n            this.close();\n        }\n        else {\n            this.open();\n        }\n    }\n    handleClearClick() {\n        if (this.hasValue) {\n            this.itemsList.clearSelected(true);\n            this._updateNgModel();\n        }\n        this._clearSearch();\n        this.focus();\n        this.clearEvent.emit();\n        this._onSelectionChanged();\n    }\n    clearModel() {\n        if (!this.clearable) {\n            return;\n        }\n        this.itemsList.clearSelected();\n        this._updateNgModel();\n    }\n    writeValue(value) {\n        this.itemsList.clearSelected();\n        this._handleWriteValue(value);\n        this._cd.markForCheck();\n    }\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    setDisabledState(state) {\n        this._disabled = state;\n        this._cd.markForCheck();\n    }\n    toggle() {\n        if (!this.isOpen) {\n            this.open();\n        }\n        else {\n            this.close();\n        }\n    }\n    open() {\n        if (this.disabled || this.isOpen || this.itemsList.maxItemsSelected || this._manualOpen) {\n            return;\n        }\n        if (!this._isTypeahead && !this.addTag && this.itemsList.noItemsToSelect) {\n            return;\n        }\n        this.isOpen = true;\n        this.itemsList.markSelectedOrDefault(this.markFirst);\n        this.openEvent.emit();\n        if (!this.searchTerm) {\n            this.focus();\n        }\n        this.detectChanges();\n    }\n    close() {\n        if (!this.isOpen || this._manualOpen) {\n            return;\n        }\n        this.isOpen = false;\n        this._isComposing = false;\n        if (!this._editableSearchTerm) {\n            this._clearSearch();\n        }\n        else {\n            this.itemsList.resetFilteredItems();\n        }\n        this.itemsList.unmarkItem();\n        this._onTouched();\n        this.closeEvent.emit();\n        this._cd.markForCheck();\n    }\n    toggleItem(item) {\n        if (!item || item.disabled || this.disabled) {\n            return;\n        }\n        if (this.multiple && item.selected) {\n            this.unselect(item);\n        }\n        else {\n            this.select(item);\n        }\n        if (this._editableSearchTerm) {\n            this._setSearchTermFromItems();\n        }\n        this._onSelectionChanged();\n    }\n    select(item) {\n        if (!item.selected) {\n            this.itemsList.select(item);\n            if (this.clearSearchOnAdd && !this._editableSearchTerm) {\n                this._clearSearch();\n            }\n            this._updateNgModel();\n            if (this.multiple) {\n                this.addEvent.emit(item.value);\n            }\n        }\n        if (this.closeOnSelect || this.itemsList.noItemsToSelect) {\n            this.close();\n        }\n    }\n    focus() {\n        this.searchInput.nativeElement.focus();\n    }\n    blur() {\n        this.searchInput.nativeElement.blur();\n    }\n    unselect(item) {\n        if (!item) {\n            return;\n        }\n        this.itemsList.unselect(item);\n        this.focus();\n        this._updateNgModel();\n        this.removeEvent.emit(item);\n    }\n    selectTag() {\n        let tag;\n        if (isFunction(this.addTag)) {\n            tag = this.addTag(this.searchTerm);\n        }\n        else {\n            tag = this._primitive ? this.searchTerm : { [this.bindLabel]: this.searchTerm };\n        }\n        const handleTag = (item) => this._isTypeahead || !this.isOpen ? this.itemsList.mapItem(item, null) : this.itemsList.addItem(item);\n        if (isPromise(tag)) {\n            tag.then(item => this.select(handleTag(item))).catch(() => { });\n        }\n        else if (tag) {\n            this.select(handleTag(tag));\n        }\n    }\n    showClear() {\n        return this.clearable && (this.hasValue || this.searchTerm) && !this.disabled;\n    }\n    get showAddTag() {\n        if (!this._validTerm) {\n            return false;\n        }\n        const term = this.searchTerm.toLowerCase().trim();\n        return this.addTag &&\n            (!this.itemsList.filteredItems.some(x => x.label.toLowerCase() === term) &&\n                (!this.hideSelected && this.isOpen || !this.selectedItems.some(x => x.label.toLowerCase() === term))) &&\n            !this.loading;\n    }\n    showNoItemsFound() {\n        const empty = this.itemsList.filteredItems.length === 0;\n        return ((empty && !this._isTypeahead && !this.loading) ||\n            (empty && this._isTypeahead && this._validTerm && !this.loading)) &&\n            !this.showAddTag;\n    }\n    showTypeToSearch() {\n        const empty = this.itemsList.filteredItems.length === 0;\n        return empty && this._isTypeahead && !this._validTerm && !this.loading;\n    }\n    onCompositionStart() {\n        this._isComposing = true;\n    }\n    onCompositionEnd(term) {\n        this._isComposing = false;\n        if (this.searchWhileComposing) {\n            return;\n        }\n        this.filter(term);\n    }\n    filter(term) {\n        if (this._isComposing && !this.searchWhileComposing) {\n            return;\n        }\n        this.searchTerm = term;\n        if (this._isTypeahead && (this._validTerm || this.minTermLength === 0)) {\n            this.typeahead.next(term);\n        }\n        if (!this._isTypeahead) {\n            this.itemsList.filter(this.searchTerm);\n            if (this.isOpen) {\n                this.itemsList.markSelectedOrDefault(this.markFirst);\n            }\n        }\n        this.searchEvent.emit({ term, items: this.itemsList.filteredItems.map(x => x.value) });\n        this.open();\n    }\n    onInputFocus($event) {\n        if (this.focused) {\n            return;\n        }\n        if (this._editableSearchTerm) {\n            this._setSearchTermFromItems();\n        }\n        this.element.classList.add('ng-select-focused');\n        this.focusEvent.emit($event);\n        this.focused = true;\n    }\n    onInputBlur($event) {\n        this.element.classList.remove('ng-select-focused');\n        this.blurEvent.emit($event);\n        if (!this.isOpen && !this.disabled) {\n            this._onTouched();\n        }\n        if (this._editableSearchTerm) {\n            this._setSearchTermFromItems();\n        }\n        this.focused = false;\n    }\n    onItemHover(item) {\n        if (item.disabled) {\n            return;\n        }\n        this.itemsList.markItem(item);\n    }\n    detectChanges() {\n        if (!this._cd.destroyed) {\n            this._cd.detectChanges();\n        }\n    }\n    _setSearchTermFromItems() {\n        const selected = this.selectedItems && this.selectedItems[0];\n        this.searchTerm = (selected && selected.label) || null;\n    }\n    _setItems(items) {\n        const firstItem = items[0];\n        this.bindLabel = this.bindLabel || this._defaultLabel;\n        this._primitive = isDefined(firstItem) ? !isObject(firstItem) : this._primitive || this.bindLabel === this._defaultLabel;\n        this.itemsList.setItems(items);\n        if (items.length > 0 && this.hasValue) {\n            this.itemsList.mapSelectedItems();\n        }\n        if (this.isOpen && isDefined(this.searchTerm) && !this._isTypeahead) {\n            this.itemsList.filter(this.searchTerm);\n        }\n        if (this._isTypeahead || this.isOpen) {\n            this.itemsList.markSelectedOrDefault(this.markFirst);\n        }\n    }\n    _setItemsFromNgOptions() {\n        const mapNgOptions = (options) => {\n            this.items = options.map(option => ({\n                $ngOptionValue: option.value,\n                $ngOptionLabel: option.elementRef.nativeElement.innerHTML,\n                disabled: option.disabled\n            }));\n            this.itemsList.setItems(this.items);\n            if (this.hasValue) {\n                this.itemsList.mapSelectedItems();\n            }\n            this.detectChanges();\n        };\n        const handleOptionChange = () => {\n            const changedOrDestroyed = merge(this.ngOptions.changes, this._destroy$);\n            merge(...this.ngOptions.map(option => option.stateChange$))\n                .pipe(takeUntil(changedOrDestroyed))\n                .subscribe(option => {\n                const item = this.itemsList.findItem(option.value);\n                item.disabled = option.disabled;\n                item.label = option.label || item.label;\n                this._cd.detectChanges();\n            });\n        };\n        this.ngOptions.changes\n            .pipe(startWith(this.ngOptions), takeUntil(this._destroy$))\n            .subscribe(options => {\n            this.bindLabel = this._defaultLabel;\n            mapNgOptions(options);\n            handleOptionChange();\n        });\n    }\n    _isValidWriteValue(value) {\n        if (!isDefined(value) || (this.multiple && value === '') || Array.isArray(value) && value.length === 0) {\n            return false;\n        }\n        const validateBinding = (item) => {\n            if (!isDefined(this.compareWith) && isObject(item) && this.bindValue) {\n                this._console.warn(`Setting object(${JSON.stringify(item)}) as your model with bindValue is not allowed unless [compareWith] is used.`);\n                return false;\n            }\n            return true;\n        };\n        if (this.multiple) {\n            if (!Array.isArray(value)) {\n                this._console.warn('Multiple select ngModel should be array.');\n                return false;\n            }\n            return value.every(item => validateBinding(item));\n        }\n        else {\n            return validateBinding(value);\n        }\n    }\n    _handleWriteValue(ngModel) {\n        if (!this._isValidWriteValue(ngModel)) {\n            return;\n        }\n        const select = (val) => {\n            let item = this.itemsList.findItem(val);\n            if (item) {\n                this.itemsList.select(item);\n            }\n            else {\n                const isValObject = isObject(val);\n                const isPrimitive = !isValObject && !this.bindValue;\n                if ((isValObject || isPrimitive)) {\n                    this.itemsList.select(this.itemsList.mapItem(val, null));\n                }\n                else if (this.bindValue) {\n                    item = {\n                        [this.bindLabel]: null,\n                        [this.bindValue]: val\n                    };\n                    this.itemsList.select(this.itemsList.mapItem(item, null));\n                }\n            }\n        };\n        if (this.multiple) {\n            ngModel.forEach(item => select(item));\n        }\n        else {\n            select(ngModel);\n        }\n    }\n    _handleKeyPresses() {\n        if (this.searchable) {\n            return;\n        }\n        this._keyPress$\n            .pipe(takeUntil(this._destroy$), tap(letter => this._pressedKeys.push(letter)), debounceTime(200), filter(() => this._pressedKeys.length > 0), map(() => this._pressedKeys.join('')))\n            .subscribe(term => {\n            const item = this.itemsList.findByLabel(term);\n            if (item) {\n                if (this.isOpen) {\n                    this.itemsList.markItem(item);\n                    this._scrollToMarked();\n                    this._cd.markForCheck();\n                }\n                else {\n                    this.select(item);\n                }\n            }\n            this._pressedKeys = [];\n        });\n    }\n    _setInputAttributes() {\n        const input = this.searchInput.nativeElement;\n        const attributes = {\n            type: 'text',\n            autocorrect: 'off',\n            autocapitalize: 'off',\n            autocomplete: this.labelForId ? 'off' : this.dropdownId,\n            ...this.inputAttrs\n        };\n        for (const key of Object.keys(attributes)) {\n            input.setAttribute(key, attributes[key]);\n        }\n    }\n    _updateNgModel() {\n        const model = [];\n        for (const item of this.selectedItems) {\n            if (this.bindValue) {\n                let value = null;\n                if (item.children) {\n                    const groupKey = this.groupValue ? this.bindValue : this.groupBy;\n                    value = item.value[groupKey || this.groupBy];\n                }\n                else {\n                    value = this.itemsList.resolveNested(item.value, this.bindValue);\n                }\n                model.push(value);\n            }\n            else {\n                model.push(item.value);\n            }\n        }\n        const selected = this.selectedItems.map(x => x.value);\n        if (this.multiple) {\n            this._onChange(model);\n            this.changeEvent.emit(selected);\n        }\n        else {\n            this._onChange(isDefined(model[0]) ? model[0] : null);\n            this.changeEvent.emit(selected[0]);\n        }\n        this._cd.markForCheck();\n    }\n    _clearSearch() {\n        if (!this.searchTerm) {\n            return;\n        }\n        this._changeSearch(null);\n        this.itemsList.resetFilteredItems();\n    }\n    _changeSearch(searchTerm) {\n        this.searchTerm = searchTerm;\n        if (this._isTypeahead) {\n            this.typeahead.next(searchTerm);\n        }\n    }\n    _scrollToMarked() {\n        if (!this.isOpen || !this.dropdownPanel) {\n            return;\n        }\n        this.dropdownPanel.scrollTo(this.itemsList.markedItem);\n    }\n    _scrollToTag() {\n        if (!this.isOpen || !this.dropdownPanel) {\n            return;\n        }\n        this.dropdownPanel.scrollToTag();\n    }\n    _onSelectionChanged() {\n        if (this.isOpen && this.multiple && this.appendTo) {\n            // Make sure items are rendered.\n            this._cd.detectChanges();\n            this.dropdownPanel.adjustPosition();\n        }\n    }\n    _handleTab($event) {\n        if (this.isOpen === false && !this.addTag) {\n            return;\n        }\n        if (this.selectOnTab) {\n            if (this.itemsList.markedItem) {\n                this.toggleItem(this.itemsList.markedItem);\n                $event.preventDefault();\n            }\n            else if (this.showAddTag) {\n                this.selectTag();\n                $event.preventDefault();\n            }\n            else {\n                this.close();\n            }\n        }\n        else {\n            this.close();\n        }\n    }\n    _handleEnter($event) {\n        if (this.isOpen || this._manualOpen) {\n            if (this.itemsList.markedItem) {\n                this.toggleItem(this.itemsList.markedItem);\n            }\n            else if (this.showAddTag) {\n                this.selectTag();\n            }\n        }\n        else if (this.openOnEnter) {\n            this.open();\n        }\n        else {\n            return;\n        }\n        $event.preventDefault();\n    }\n    _handleSpace($event) {\n        if (this.isOpen || this._manualOpen) {\n            return;\n        }\n        this.open();\n        $event.preventDefault();\n    }\n    _handleArrowDown($event) {\n        if (this._nextItemIsTag(+1)) {\n            this.itemsList.unmarkItem();\n            this._scrollToTag();\n        }\n        else {\n            this.itemsList.markNextItem();\n            this._scrollToMarked();\n        }\n        this.open();\n        $event.preventDefault();\n    }\n    _handleArrowUp($event) {\n        if (!this.isOpen) {\n            return;\n        }\n        if (this._nextItemIsTag(-1)) {\n            this.itemsList.unmarkItem();\n            this._scrollToTag();\n        }\n        else {\n            this.itemsList.markPreviousItem();\n            this._scrollToMarked();\n        }\n        $event.preventDefault();\n    }\n    _nextItemIsTag(nextStep) {\n        const nextIndex = this.itemsList.markedIndex + nextStep;\n        return this.addTag && this.searchTerm\n            && this.itemsList.markedItem\n            && (nextIndex < 0 || nextIndex === this.itemsList.filteredItems.length);\n    }\n    _handleBackspace() {\n        if (this.searchTerm || !this.clearable || !this.clearOnBackspace || !this.hasValue) {\n            return;\n        }\n        if (this.multiple) {\n            this.unselect(this.itemsList.lastSelectedItem);\n        }\n        else {\n            this.clearModel();\n        }\n    }\n    get _isTypeahead() {\n        return this.typeahead && this.typeahead.observers.length > 0;\n    }\n    get _validTerm() {\n        const term = this.searchTerm && this.searchTerm.trim();\n        return term && term.length >= this.minTermLength;\n    }\n    _mergeGlobalConfig(config) {\n        this.placeholder = this.placeholder || config.placeholder;\n        this.notFoundText = this.notFoundText || config.notFoundText;\n        this.typeToSearchText = this.typeToSearchText || config.typeToSearchText;\n        this.addTagText = this.addTagText || config.addTagText;\n        this.loadingText = this.loadingText || config.loadingText;\n        this.clearAllText = this.clearAllText || config.clearAllText;\n        this.virtualScroll = isDefined(this.virtualScroll)\n            ? this.virtualScroll\n            : isDefined(config.disableVirtualScroll) ? !config.disableVirtualScroll : false;\n        this.openOnEnter = isDefined(this.openOnEnter) ? this.openOnEnter : config.openOnEnter;\n        this.appendTo = this.appendTo || config.appendTo;\n        this.bindValue = this.bindValue || config.bindValue;\n        this.bindLabel = this.bindLabel || config.bindLabel;\n        this.appearance = this.appearance || config.appearance;\n    }\n}\nNgSelectComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgSelectComponent, deps: [{ token: 'class', attribute: true }, { token: 'autofocus', attribute: true }, { token: NgSelectConfig }, { token: SELECTION_MODEL_FACTORY }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: ConsoleService }], target: i0.ɵɵFactoryTarget.Component });\nNgSelectComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.0.1\", type: NgSelectComponent, selector: \"ng-select\", inputs: { bindLabel: \"bindLabel\", bindValue: \"bindValue\", markFirst: \"markFirst\", placeholder: \"placeholder\", notFoundText: \"notFoundText\", typeToSearchText: \"typeToSearchText\", addTagText: \"addTagText\", loadingText: \"loadingText\", clearAllText: \"clearAllText\", appearance: \"appearance\", dropdownPosition: \"dropdownPosition\", appendTo: \"appendTo\", loading: \"loading\", closeOnSelect: \"closeOnSelect\", hideSelected: \"hideSelected\", selectOnTab: \"selectOnTab\", openOnEnter: \"openOnEnter\", maxSelectedItems: \"maxSelectedItems\", groupBy: \"groupBy\", groupValue: \"groupValue\", bufferAmount: \"bufferAmount\", virtualScroll: \"virtualScroll\", selectableGroup: \"selectableGroup\", selectableGroupAsModel: \"selectableGroupAsModel\", searchFn: \"searchFn\", trackByFn: \"trackByFn\", clearOnBackspace: \"clearOnBackspace\", labelForId: \"labelForId\", inputAttrs: \"inputAttrs\", tabIndex: \"tabIndex\", readonly: \"readonly\", searchWhileComposing: \"searchWhileComposing\", minTermLength: \"minTermLength\", editableSearchTerm: \"editableSearchTerm\", keyDownFn: \"keyDownFn\", typeahead: \"typeahead\", multiple: \"multiple\", addTag: \"addTag\", searchable: \"searchable\", clearable: \"clearable\", isOpen: \"isOpen\", items: \"items\", compareWith: \"compareWith\", clearSearchOnAdd: \"clearSearchOnAdd\" }, outputs: { blurEvent: \"blur\", focusEvent: \"focus\", changeEvent: \"change\", openEvent: \"open\", closeEvent: \"close\", searchEvent: \"search\", clearEvent: \"clear\", addEvent: \"add\", removeEvent: \"remove\", scroll: \"scroll\", scrollToEnd: \"scrollToEnd\" }, host: { listeners: { \"keydown\": \"handleKeyDown($event)\" }, properties: { \"class.ng-select-typeahead\": \"this.typeahead\", \"class.ng-select-multiple\": \"this.multiple\", \"class.ng-select-taggable\": \"this.addTag\", \"class.ng-select-searchable\": \"this.searchable\", \"class.ng-select-clearable\": \"this.clearable\", \"class.ng-select-opened\": \"this.isOpen\", \"class.ng-select\": \"this.useDefaultClass\", \"class.ng-select-disabled\": \"this.disabled\", \"class.ng-select-filtered\": \"this.filtered\", \"class.ng-select-single\": \"this.single\" } }, providers: [{\n            provide: NG_VALUE_ACCESSOR,\n            useExisting: forwardRef(() => NgSelectComponent),\n            multi: true\n        }, NgDropdownPanelService], queries: [{ propertyName: \"optionTemplate\", first: true, predicate: NgOptionTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"optgroupTemplate\", first: true, predicate: NgOptgroupTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"labelTemplate\", first: true, predicate: NgLabelTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"multiLabelTemplate\", first: true, predicate: NgMultiLabelTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"headerTemplate\", first: true, predicate: NgHeaderTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"footerTemplate\", first: true, predicate: NgFooterTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"notFoundTemplate\", first: true, predicate: NgNotFoundTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"typeToSearchTemplate\", first: true, predicate: NgTypeToSearchTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"loadingTextTemplate\", first: true, predicate: NgLoadingTextTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"tagTemplate\", first: true, predicate: NgTagTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"loadingSpinnerTemplate\", first: true, predicate: NgLoadingSpinnerTemplateDirective, descendants: true, read: TemplateRef }, { propertyName: \"ngOptions\", predicate: NgOptionComponent, descendants: true }], viewQueries: [{ propertyName: \"dropdownPanel\", first: true, predicate: i0.forwardRef(function () { return NgDropdownPanelComponent; }), descendants: true }, { propertyName: \"searchInput\", first: true, predicate: [\"searchInput\"], descendants: true, static: true }], usesOnChanges: true, ngImport: i0, template: \"<div\\n    (mousedown)=\\\"handleMousedown($event)\\\"\\n    [class.ng-appearance-outline]=\\\"appearance === 'outline'\\\"\\n    [class.ng-has-value]=\\\"hasValue\\\"\\n    class=\\\"ng-select-container\\\">\\n\\n    <div class=\\\"ng-value-container\\\">\\n        <div class=\\\"ng-placeholder\\\">{{placeholder}}</div>\\n\\n        <ng-container *ngIf=\\\"(!multiLabelTemplate  || !multiple ) && selectedItems.length > 0\\\">\\n            <div [class.ng-value-disabled]=\\\"item.disabled\\\" class=\\\"ng-value\\\" *ngFor=\\\"let item of selectedItems; trackBy: trackByOption\\\">\\n                <ng-template #defaultLabelTemplate>\\n                    <span class=\\\"ng-value-icon left\\\" (click)=\\\"unselect(item);\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n                    <span class=\\\"ng-value-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n                </ng-template>\\n\\n                <ng-template\\n                    [ngTemplateOutlet]=\\\"labelTemplate || defaultLabelTemplate\\\"\\n                    [ngTemplateOutletContext]=\\\"{ item: item.value, clear: clearItem, label: item.label }\\\">\\n                </ng-template>\\n            </div>\\n        </ng-container>\\n\\n        <ng-template *ngIf=\\\"multiple && multiLabelTemplate && selectedValues.length > 0\\\"\\n                [ngTemplateOutlet]=\\\"multiLabelTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ items: selectedValues, clear: clearItem }\\\">\\n        </ng-template>\\n\\n        <div class=\\\"ng-input\\\"\\n            role=\\\"combobox\\\" \\n            [attr.aria-expanded]=\\\"isOpen\\\" \\n            [attr.aria-owns]=\\\"isOpen ? dropdownId : null\\\" \\n            aria-haspopup=\\\"listbox\\\">\\n\\n            <input #searchInput\\n                   [attr.id]=\\\"labelForId\\\"\\n                   [attr.tabindex]=\\\"tabIndex\\\"\\n                   [readOnly]=\\\"!searchable || itemsList.maxItemsSelected\\\"\\n                   [disabled]=\\\"disabled\\\"\\n                   [value]=\\\"searchTerm ? searchTerm : ''\\\"\\n                   (input)=\\\"filter(searchInput.value)\\\"\\n                   (compositionstart)=\\\"onCompositionStart()\\\"\\n                   (compositionend)=\\\"onCompositionEnd(searchInput.value)\\\"\\n                   (focus)=\\\"onInputFocus($event)\\\"\\n                   (blur)=\\\"onInputBlur($event)\\\"\\n                   (change)=\\\"$event.stopPropagation()\\\"\\n                   [attr.aria-activedescendant]=\\\"isOpen ? itemsList?.markedItem?.htmlId : null\\\"\\n                   aria-autocomplete=\\\"list\\\"\\n                   [attr.aria-controls]=\\\"isOpen ? dropdownId : null\\\">\\n        </div>\\n    </div>\\n\\n    <ng-container *ngIf=\\\"loading\\\">\\n        <ng-template #defaultLoadingSpinnerTemplate>\\n            <div class=\\\"ng-spinner-loader\\\"></div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingSpinnerTemplate || defaultLoadingSpinnerTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <span *ngIf=\\\"showClear()\\\" class=\\\"ng-clear-wrapper\\\" title=\\\"{{clearAllText}}\\\">\\n        <span class=\\\"ng-clear\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n    </span>\\n\\n    <span class=\\\"ng-arrow-wrapper\\\">\\n        <span class=\\\"ng-arrow\\\"></span>\\n    </span>\\n</div>\\n\\n<ng-dropdown-panel *ngIf=\\\"isOpen\\\"\\n                   class=\\\"ng-dropdown-panel\\\"\\n                   [virtualScroll]=\\\"virtualScroll\\\"\\n                   [bufferAmount]=\\\"bufferAmount\\\"\\n                   [appendTo]=\\\"appendTo\\\"\\n                   [position]=\\\"dropdownPosition\\\"\\n                   [headerTemplate]=\\\"headerTemplate\\\"\\n                   [footerTemplate]=\\\"footerTemplate\\\"\\n                   [filterValue]=\\\"searchTerm\\\"\\n                   [items]=\\\"itemsList.filteredItems\\\"\\n                   [markedItem]=\\\"itemsList.markedItem\\\"\\n                   (update)=\\\"viewPortItems = $event\\\"\\n                   (scroll)=\\\"scroll.emit($event)\\\"\\n                   (scrollToEnd)=\\\"scrollToEnd.emit($event)\\\"\\n                   (outsideClick)=\\\"close()\\\"\\n                   [class.ng-select-multiple]=\\\"multiple\\\"\\n                   [ngClass]=\\\"appendTo ? classes : null\\\"\\n                   [id]=\\\"dropdownId\\\"\\n                   role=\\\"listbox\\\"\\n                   aria-label=\\\"Options list\\\">\\n\\n    <ng-container>\\n        <div class=\\\"ng-option\\\" [attr.role]=\\\"item.children ? 'group' : 'option'\\\" (click)=\\\"toggleItem(item)\\\" (mouseover)=\\\"onItemHover(item)\\\"\\n                *ngFor=\\\"let item of viewPortItems; trackBy: trackByOption\\\"\\n                [class.ng-option-disabled]=\\\"item.disabled\\\"\\n                [class.ng-option-selected]=\\\"item.selected\\\"\\n                [class.ng-optgroup]=\\\"item.children\\\"\\n                [class.ng-option]=\\\"!item.children\\\"\\n                [class.ng-option-child]=\\\"!!item.parent\\\"\\n                [class.ng-option-marked]=\\\"item === itemsList.markedItem\\\"\\n                [attr.aria-selected]=\\\"item.selected\\\"\\n                [attr.id]=\\\"item?.htmlId\\\">\\n\\n            <ng-template #defaultOptionTemplate>\\n                <span class=\\\"ng-option-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"item.children ? (optgroupTemplate || defaultOptionTemplate) : (optionTemplate || defaultOptionTemplate)\\\"\\n                [ngTemplateOutletContext]=\\\"{ item: item.value, item$:item, index: item.index, searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n\\n        <div class=\\\"ng-option\\\" [class.ng-option-marked]=\\\"!itemsList.markedItem\\\" (mouseover)=\\\"itemsList.unmarkItem()\\\" role=\\\"option\\\" (click)=\\\"selectTag()\\\" *ngIf=\\\"showAddTag\\\">\\n            <ng-template #defaultTagTemplate>\\n                <span><span class=\\\"ng-tag-label\\\">{{addTagText}}</span>\\\"{{searchTerm}}\\\"</span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"tagTemplate || defaultTagTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showNoItemsFound()\\\">\\n        <ng-template #defaultNotFoundTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{notFoundText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"notFoundTemplate || defaultNotFoundTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showTypeToSearch()\\\">\\n        <ng-template #defaultTypeToSearchTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{typeToSearchText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"typeToSearchTemplate || defaultTypeToSearchTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"loading && itemsList.filteredItems.length === 0\\\">\\n        <ng-template #defaultLoadingTextTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{loadingText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingTextTemplate || defaultLoadingTextTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n</ng-dropdown-panel>\\n\", styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:none;user-select:none;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@keyframes load8{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}\\n\"], components: [{ type: NgDropdownPanelComponent, selector: \"ng-dropdown-panel\", inputs: [\"items\", \"markedItem\", \"position\", \"appendTo\", \"bufferAmount\", \"virtualScroll\", \"headerTemplate\", \"footerTemplate\", \"filterValue\"], outputs: [\"update\", \"scroll\", \"scrollToEnd\", \"outsideClick\"] }], directives: [{ type: i4.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i4.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { type: NgItemLabelDirective, selector: \"[ngItemLabel]\", inputs: [\"ngItemLabel\", \"escape\"] }, { type: i4.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\"] }, { type: i4.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgSelectComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'ng-select', providers: [{\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => NgSelectComponent),\n                            multi: true\n                        }, NgDropdownPanelService], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div\\n    (mousedown)=\\\"handleMousedown($event)\\\"\\n    [class.ng-appearance-outline]=\\\"appearance === 'outline'\\\"\\n    [class.ng-has-value]=\\\"hasValue\\\"\\n    class=\\\"ng-select-container\\\">\\n\\n    <div class=\\\"ng-value-container\\\">\\n        <div class=\\\"ng-placeholder\\\">{{placeholder}}</div>\\n\\n        <ng-container *ngIf=\\\"(!multiLabelTemplate  || !multiple ) && selectedItems.length > 0\\\">\\n            <div [class.ng-value-disabled]=\\\"item.disabled\\\" class=\\\"ng-value\\\" *ngFor=\\\"let item of selectedItems; trackBy: trackByOption\\\">\\n                <ng-template #defaultLabelTemplate>\\n                    <span class=\\\"ng-value-icon left\\\" (click)=\\\"unselect(item);\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n                    <span class=\\\"ng-value-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n                </ng-template>\\n\\n                <ng-template\\n                    [ngTemplateOutlet]=\\\"labelTemplate || defaultLabelTemplate\\\"\\n                    [ngTemplateOutletContext]=\\\"{ item: item.value, clear: clearItem, label: item.label }\\\">\\n                </ng-template>\\n            </div>\\n        </ng-container>\\n\\n        <ng-template *ngIf=\\\"multiple && multiLabelTemplate && selectedValues.length > 0\\\"\\n                [ngTemplateOutlet]=\\\"multiLabelTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ items: selectedValues, clear: clearItem }\\\">\\n        </ng-template>\\n\\n        <div class=\\\"ng-input\\\"\\n            role=\\\"combobox\\\" \\n            [attr.aria-expanded]=\\\"isOpen\\\" \\n            [attr.aria-owns]=\\\"isOpen ? dropdownId : null\\\" \\n            aria-haspopup=\\\"listbox\\\">\\n\\n            <input #searchInput\\n                   [attr.id]=\\\"labelForId\\\"\\n                   [attr.tabindex]=\\\"tabIndex\\\"\\n                   [readOnly]=\\\"!searchable || itemsList.maxItemsSelected\\\"\\n                   [disabled]=\\\"disabled\\\"\\n                   [value]=\\\"searchTerm ? searchTerm : ''\\\"\\n                   (input)=\\\"filter(searchInput.value)\\\"\\n                   (compositionstart)=\\\"onCompositionStart()\\\"\\n                   (compositionend)=\\\"onCompositionEnd(searchInput.value)\\\"\\n                   (focus)=\\\"onInputFocus($event)\\\"\\n                   (blur)=\\\"onInputBlur($event)\\\"\\n                   (change)=\\\"$event.stopPropagation()\\\"\\n                   [attr.aria-activedescendant]=\\\"isOpen ? itemsList?.markedItem?.htmlId : null\\\"\\n                   aria-autocomplete=\\\"list\\\"\\n                   [attr.aria-controls]=\\\"isOpen ? dropdownId : null\\\">\\n        </div>\\n    </div>\\n\\n    <ng-container *ngIf=\\\"loading\\\">\\n        <ng-template #defaultLoadingSpinnerTemplate>\\n            <div class=\\\"ng-spinner-loader\\\"></div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingSpinnerTemplate || defaultLoadingSpinnerTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <span *ngIf=\\\"showClear()\\\" class=\\\"ng-clear-wrapper\\\" title=\\\"{{clearAllText}}\\\">\\n        <span class=\\\"ng-clear\\\" aria-hidden=\\\"true\\\">\\u00D7</span>\\n    </span>\\n\\n    <span class=\\\"ng-arrow-wrapper\\\">\\n        <span class=\\\"ng-arrow\\\"></span>\\n    </span>\\n</div>\\n\\n<ng-dropdown-panel *ngIf=\\\"isOpen\\\"\\n                   class=\\\"ng-dropdown-panel\\\"\\n                   [virtualScroll]=\\\"virtualScroll\\\"\\n                   [bufferAmount]=\\\"bufferAmount\\\"\\n                   [appendTo]=\\\"appendTo\\\"\\n                   [position]=\\\"dropdownPosition\\\"\\n                   [headerTemplate]=\\\"headerTemplate\\\"\\n                   [footerTemplate]=\\\"footerTemplate\\\"\\n                   [filterValue]=\\\"searchTerm\\\"\\n                   [items]=\\\"itemsList.filteredItems\\\"\\n                   [markedItem]=\\\"itemsList.markedItem\\\"\\n                   (update)=\\\"viewPortItems = $event\\\"\\n                   (scroll)=\\\"scroll.emit($event)\\\"\\n                   (scrollToEnd)=\\\"scrollToEnd.emit($event)\\\"\\n                   (outsideClick)=\\\"close()\\\"\\n                   [class.ng-select-multiple]=\\\"multiple\\\"\\n                   [ngClass]=\\\"appendTo ? classes : null\\\"\\n                   [id]=\\\"dropdownId\\\"\\n                   role=\\\"listbox\\\"\\n                   aria-label=\\\"Options list\\\">\\n\\n    <ng-container>\\n        <div class=\\\"ng-option\\\" [attr.role]=\\\"item.children ? 'group' : 'option'\\\" (click)=\\\"toggleItem(item)\\\" (mouseover)=\\\"onItemHover(item)\\\"\\n                *ngFor=\\\"let item of viewPortItems; trackBy: trackByOption\\\"\\n                [class.ng-option-disabled]=\\\"item.disabled\\\"\\n                [class.ng-option-selected]=\\\"item.selected\\\"\\n                [class.ng-optgroup]=\\\"item.children\\\"\\n                [class.ng-option]=\\\"!item.children\\\"\\n                [class.ng-option-child]=\\\"!!item.parent\\\"\\n                [class.ng-option-marked]=\\\"item === itemsList.markedItem\\\"\\n                [attr.aria-selected]=\\\"item.selected\\\"\\n                [attr.id]=\\\"item?.htmlId\\\">\\n\\n            <ng-template #defaultOptionTemplate>\\n                <span class=\\\"ng-option-label\\\" [ngItemLabel]=\\\"item.label\\\" [escape]=\\\"escapeHTML\\\"></span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"item.children ? (optgroupTemplate || defaultOptionTemplate) : (optionTemplate || defaultOptionTemplate)\\\"\\n                [ngTemplateOutletContext]=\\\"{ item: item.value, item$:item, index: item.index, searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n\\n        <div class=\\\"ng-option\\\" [class.ng-option-marked]=\\\"!itemsList.markedItem\\\" (mouseover)=\\\"itemsList.unmarkItem()\\\" role=\\\"option\\\" (click)=\\\"selectTag()\\\" *ngIf=\\\"showAddTag\\\">\\n            <ng-template #defaultTagTemplate>\\n                <span><span class=\\\"ng-tag-label\\\">{{addTagText}}</span>\\\"{{searchTerm}}\\\"</span>\\n            </ng-template>\\n\\n            <ng-template\\n                [ngTemplateOutlet]=\\\"tagTemplate || defaultTagTemplate\\\"\\n                [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n            </ng-template>\\n        </div>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showNoItemsFound()\\\">\\n        <ng-template #defaultNotFoundTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{notFoundText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"notFoundTemplate || defaultNotFoundTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"showTypeToSearch()\\\">\\n        <ng-template #defaultTypeToSearchTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{typeToSearchText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"typeToSearchTemplate || defaultTypeToSearchTemplate\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n    <ng-container *ngIf=\\\"loading && itemsList.filteredItems.length === 0\\\">\\n        <ng-template #defaultLoadingTextTemplate>\\n            <div class=\\\"ng-option ng-option-disabled\\\">{{loadingText}}</div>\\n        </ng-template>\\n\\n        <ng-template\\n            [ngTemplateOutlet]=\\\"loadingTextTemplate || defaultLoadingTextTemplate\\\"\\n            [ngTemplateOutletContext]=\\\"{ searchTerm: searchTerm }\\\">\\n        </ng-template>\\n    </ng-container>\\n\\n</ng-dropdown-panel>\\n\", styles: [\"@charset \\\"UTF-8\\\";.ng-select{position:relative;display:block;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:none;user-select:none;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@keyframes load8{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:\\\"\\\\200b\\\"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['class']\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['autofocus']\n                }] }, { type: NgSelectConfig }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [SELECTION_MODEL_FACTORY]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: ConsoleService }]; }, propDecorators: { bindLabel: [{\n                type: Input\n            }], bindValue: [{\n                type: Input\n            }], markFirst: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], notFoundText: [{\n                type: Input\n            }], typeToSearchText: [{\n                type: Input\n            }], addTagText: [{\n                type: Input\n            }], loadingText: [{\n                type: Input\n            }], clearAllText: [{\n                type: Input\n            }], appearance: [{\n                type: Input\n            }], dropdownPosition: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], closeOnSelect: [{\n                type: Input\n            }], hideSelected: [{\n                type: Input\n            }], selectOnTab: [{\n                type: Input\n            }], openOnEnter: [{\n                type: Input\n            }], maxSelectedItems: [{\n                type: Input\n            }], groupBy: [{\n                type: Input\n            }], groupValue: [{\n                type: Input\n            }], bufferAmount: [{\n                type: Input\n            }], virtualScroll: [{\n                type: Input\n            }], selectableGroup: [{\n                type: Input\n            }], selectableGroupAsModel: [{\n                type: Input\n            }], searchFn: [{\n                type: Input\n            }], trackByFn: [{\n                type: Input\n            }], clearOnBackspace: [{\n                type: Input\n            }], labelForId: [{\n                type: Input\n            }], inputAttrs: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], searchWhileComposing: [{\n                type: Input\n            }], minTermLength: [{\n                type: Input\n            }], editableSearchTerm: [{\n                type: Input\n            }], keyDownFn: [{\n                type: Input\n            }], typeahead: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-typeahead']\n            }], multiple: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-multiple']\n            }], addTag: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-taggable']\n            }], searchable: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-searchable']\n            }], clearable: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-clearable']\n            }], isOpen: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: ['class.ng-select-opened']\n            }], items: [{\n                type: Input\n            }], compareWith: [{\n                type: Input\n            }], clearSearchOnAdd: [{\n                type: Input\n            }], blurEvent: [{\n                type: Output,\n                args: ['blur']\n            }], focusEvent: [{\n                type: Output,\n                args: ['focus']\n            }], changeEvent: [{\n                type: Output,\n                args: ['change']\n            }], openEvent: [{\n                type: Output,\n                args: ['open']\n            }], closeEvent: [{\n                type: Output,\n                args: ['close']\n            }], searchEvent: [{\n                type: Output,\n                args: ['search']\n            }], clearEvent: [{\n                type: Output,\n                args: ['clear']\n            }], addEvent: [{\n                type: Output,\n                args: ['add']\n            }], removeEvent: [{\n                type: Output,\n                args: ['remove']\n            }], scroll: [{\n                type: Output,\n                args: ['scroll']\n            }], scrollToEnd: [{\n                type: Output,\n                args: ['scrollToEnd']\n            }], optionTemplate: [{\n                type: ContentChild,\n                args: [NgOptionTemplateDirective, { read: TemplateRef }]\n            }], optgroupTemplate: [{\n                type: ContentChild,\n                args: [NgOptgroupTemplateDirective, { read: TemplateRef }]\n            }], labelTemplate: [{\n                type: ContentChild,\n                args: [NgLabelTemplateDirective, { read: TemplateRef }]\n            }], multiLabelTemplate: [{\n                type: ContentChild,\n                args: [NgMultiLabelTemplateDirective, { read: TemplateRef }]\n            }], headerTemplate: [{\n                type: ContentChild,\n                args: [NgHeaderTemplateDirective, { read: TemplateRef }]\n            }], footerTemplate: [{\n                type: ContentChild,\n                args: [NgFooterTemplateDirective, { read: TemplateRef }]\n            }], notFoundTemplate: [{\n                type: ContentChild,\n                args: [NgNotFoundTemplateDirective, { read: TemplateRef }]\n            }], typeToSearchTemplate: [{\n                type: ContentChild,\n                args: [NgTypeToSearchTemplateDirective, { read: TemplateRef }]\n            }], loadingTextTemplate: [{\n                type: ContentChild,\n                args: [NgLoadingTextTemplateDirective, { read: TemplateRef }]\n            }], tagTemplate: [{\n                type: ContentChild,\n                args: [NgTagTemplateDirective, { read: TemplateRef }]\n            }], loadingSpinnerTemplate: [{\n                type: ContentChild,\n                args: [NgLoadingSpinnerTemplateDirective, { read: TemplateRef }]\n            }], dropdownPanel: [{\n                type: ViewChild,\n                args: [forwardRef(() => NgDropdownPanelComponent)]\n            }], searchInput: [{\n                type: ViewChild,\n                args: ['searchInput', { static: true }]\n            }], ngOptions: [{\n                type: ContentChildren,\n                args: [NgOptionComponent, { descendants: true }]\n            }], useDefaultClass: [{\n                type: HostBinding,\n                args: ['class.ng-select']\n            }], disabled: [{\n                type: HostBinding,\n                args: ['class.ng-select-disabled']\n            }], filtered: [{\n                type: HostBinding,\n                args: ['class.ng-select-filtered']\n            }], single: [{\n                type: HostBinding,\n                args: ['class.ng-select-single']\n            }], handleKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\n\nfunction DefaultSelectionModelFactory() {\n    return new DefaultSelectionModel();\n}\nclass DefaultSelectionModel {\n    constructor() {\n        this._selected = [];\n    }\n    get value() {\n        return this._selected;\n    }\n    select(item, multiple, groupAsModel) {\n        item.selected = true;\n        if (!item.children || (!multiple && groupAsModel)) {\n            this._selected.push(item);\n        }\n        if (multiple) {\n            if (item.parent) {\n                const childrenCount = item.parent.children.length;\n                const selectedCount = item.parent.children.filter(x => x.selected).length;\n                item.parent.selected = childrenCount === selectedCount;\n            }\n            else if (item.children) {\n                this._setChildrenSelectedState(item.children, true);\n                this._removeChildren(item);\n                if (groupAsModel && this._activeChildren(item)) {\n                    this._selected = [...this._selected.filter(x => x.parent !== item), item];\n                }\n                else {\n                    this._selected = [...this._selected, ...item.children.filter(x => !x.disabled)];\n                }\n            }\n        }\n    }\n    unselect(item, multiple) {\n        this._selected = this._selected.filter(x => x !== item);\n        item.selected = false;\n        if (multiple) {\n            if (item.parent && item.parent.selected) {\n                const children = item.parent.children;\n                this._removeParent(item.parent);\n                this._removeChildren(item.parent);\n                this._selected.push(...children.filter(x => x !== item && !x.disabled));\n                item.parent.selected = false;\n            }\n            else if (item.children) {\n                this._setChildrenSelectedState(item.children, false);\n                this._removeChildren(item);\n            }\n        }\n    }\n    clear(keepDisabled) {\n        this._selected = keepDisabled ? this._selected.filter(x => x.disabled) : [];\n    }\n    _setChildrenSelectedState(children, selected) {\n        for (const child of children) {\n            if (child.disabled) {\n                continue;\n            }\n            child.selected = selected;\n        }\n    }\n    _removeChildren(parent) {\n        this._selected = [\n            ...this._selected.filter(x => x.parent !== parent),\n            ...parent.children.filter(x => x.parent === parent && x.disabled && x.selected)\n        ];\n    }\n    _removeParent(parent) {\n        this._selected = this._selected.filter(x => x !== parent);\n    }\n    _activeChildren(item) {\n        return item.children.every(x => !x.disabled || x.selected);\n    }\n}\n\nclass NgSelectModule {\n}\nNgSelectModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgSelectModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nNgSelectModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgSelectModule, declarations: [NgDropdownPanelComponent,\n        NgOptionComponent,\n        NgSelectComponent,\n        NgOptgroupTemplateDirective,\n        NgOptionTemplateDirective,\n        NgLabelTemplateDirective,\n        NgMultiLabelTemplateDirective,\n        NgHeaderTemplateDirective,\n        NgFooterTemplateDirective,\n        NgNotFoundTemplateDirective,\n        NgTypeToSearchTemplateDirective,\n        NgLoadingTextTemplateDirective,\n        NgTagTemplateDirective,\n        NgLoadingSpinnerTemplateDirective,\n        NgItemLabelDirective], imports: [CommonModule], exports: [NgSelectComponent,\n        NgOptionComponent,\n        NgOptgroupTemplateDirective,\n        NgOptionTemplateDirective,\n        NgLabelTemplateDirective,\n        NgMultiLabelTemplateDirective,\n        NgHeaderTemplateDirective,\n        NgFooterTemplateDirective,\n        NgNotFoundTemplateDirective,\n        NgTypeToSearchTemplateDirective,\n        NgLoadingTextTemplateDirective,\n        NgTagTemplateDirective,\n        NgLoadingSpinnerTemplateDirective] });\nNgSelectModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgSelectModule, providers: [\n        { provide: SELECTION_MODEL_FACTORY, useValue: DefaultSelectionModelFactory }\n    ], imports: [[\n            CommonModule\n        ]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.0.1\", ngImport: i0, type: NgSelectModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [\n                        NgDropdownPanelComponent,\n                        NgOptionComponent,\n                        NgSelectComponent,\n                        NgOptgroupTemplateDirective,\n                        NgOptionTemplateDirective,\n                        NgLabelTemplateDirective,\n                        NgMultiLabelTemplateDirective,\n                        NgHeaderTemplateDirective,\n                        NgFooterTemplateDirective,\n                        NgNotFoundTemplateDirective,\n                        NgTypeToSearchTemplateDirective,\n                        NgLoadingTextTemplateDirective,\n                        NgTagTemplateDirective,\n                        NgLoadingSpinnerTemplateDirective,\n                        NgItemLabelDirective\n                    ],\n                    imports: [\n                        CommonModule\n                    ],\n                    exports: [\n                        NgSelectComponent,\n                        NgOptionComponent,\n                        NgOptgroupTemplateDirective,\n                        NgOptionTemplateDirective,\n                        NgLabelTemplateDirective,\n                        NgMultiLabelTemplateDirective,\n                        NgHeaderTemplateDirective,\n                        NgFooterTemplateDirective,\n                        NgNotFoundTemplateDirective,\n                        NgTypeToSearchTemplateDirective,\n                        NgLoadingTextTemplateDirective,\n                        NgTagTemplateDirective,\n                        NgLoadingSpinnerTemplateDirective\n                    ],\n                    providers: [\n                        { provide: SELECTION_MODEL_FACTORY, useValue: DefaultSelectionModelFactory }\n                    ]\n                }]\n        }] });\n\n/*\n * Public API Surface of ng-select\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NgFooterTemplateDirective, NgHeaderTemplateDirective, NgItemLabelDirective, NgLabelTemplateDirective, NgLoadingSpinnerTemplateDirective, NgLoadingTextTemplateDirective, NgMultiLabelTemplateDirective, NgNotFoundTemplateDirective, NgOptgroupTemplateDirective, NgOptionComponent, NgOptionTemplateDirective, NgSelectComponent, NgSelectConfig, NgSelectModule, NgTagTemplateDirective, NgTypeToSearchTemplateDirective, SELECTION_MODEL_FACTORY };\n"]}, "metadata": {}, "sourceType": "module"}
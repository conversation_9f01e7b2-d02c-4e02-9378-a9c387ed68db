import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-collections',
  templateUrl: './collections.component.html',
  styleUrls: ['./collections.component.scss']
})
export class CollectionsComponent implements OnInit {
  p: number = 1;
  p1: number = 1;
  term: string;
  showForm = false;
  isReadonly = false;
  title = 'View';
  collectionList: any;  
  deleteId: any;
  contentVisible = false;

  constructor(private formBuilder: FormBuilder,
    private dataTransferService : DataTransferService,
    private toastr:ToastrService,
    private router:Router,
    private activeRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private ngxSpinnerService: NgxSpinnerService) {

     }

  ngOnInit(): void {
    setTimeout(() => {
      this.contentVisible = true;
    }, 100); 
    this.getAllCollections();
    // this.getAllSoundBite();
}

 
  DeleteCollection() {
    const PL_id=this.deleteId;
    this.dataTransferService.deletecollection(PL_id).subscribe((res: any) => {
      console.log('Delete data', res);
      if (res.statusCode == 200) {
        this.toastr.success('Collection deleted successfully');
        this.getAllCollections();
      } else {
        this.toastr.error("", res.message);
      }
    })
  }
 

  showDeleteModal(collection:any){
    this.deleteId=collection.PL_id;
  }

  addRecord(){
    const state={
      title:'Add New',
      isReadonly:false,
    }
    this.router.navigate([`actions/collections/add-edit-collection`],{state});
  }

  editRecord(collection: any, collectionTitle: string) {
   const state={
    collectionData: collection,
    title:collectionTitle
   }
   this.router.navigate([`actions/collections/add-edit-collection`],{state});
  }

  getAllCollections() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getCollection().subscribe({
      next: (res: any) => {
      if (res.statusCode == 200) {
        this.collectionList = res.data;
        console.log('collectionList', this.collectionList);
        this.ngxSpinnerService.hide('globalSpinner'); 
      }
      },
      error: (error: any) => {
        console.log('Error Message',error);
        this.ngxSpinnerService.hide('globalSpinner'); 
      },
    })
  }
}

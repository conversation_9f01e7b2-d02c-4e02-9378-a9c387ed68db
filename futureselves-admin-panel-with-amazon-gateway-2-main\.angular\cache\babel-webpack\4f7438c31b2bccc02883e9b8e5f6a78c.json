{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler = async) {\n  return audit(() => timer(duration, scheduler));\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/operators/auditTime.js"], "names": ["async", "audit", "timer", "auditTime", "duration", "scheduler"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,OAAO,SAASC,SAAT,CAAmBC,QAAnB,EAA6BC,SAAS,GAAGL,KAAzC,EAAgD;AACnD,SAAOC,KAAK,CAAC,MAAMC,KAAK,CAACE,QAAD,EAAWC,SAAX,CAAZ,CAAZ;AACH", "sourcesContent": ["import { async } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler = async) {\n    return audit(() => timer(duration, scheduler));\n}\n"]}, "metadata": {}, "sourceType": "module"}
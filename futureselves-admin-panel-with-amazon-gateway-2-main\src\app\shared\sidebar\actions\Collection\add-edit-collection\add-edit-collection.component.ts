import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
import { FileValidator } from 'src/app/shared/validators/fileValidator.validator';

@Component({
  selector: 'app-add-edit-collection',
  templateUrl: './add-edit-collection.component.html',
  styleUrls: ['./add-edit-collection.component.scss']
})
export class AddEditCollectionComponent implements OnInit {

  soundList:any;
  p: number = 1;
  p1: number = 1;
  term: string;
  showForm = false;
  submitted = false;
  isReadonly = false;
  title = 'Add New';
  industryList: any;
  imageName: any;
  searchForm: FormGroup;
  filteredData : any[]=[];
  publishVoxlistForm: FormGroup | any;
  submittedVoxList:boolean = false;
  SharerList:any=[];
  industrySelected: boolean = false;
  Allroles:any=[]
  CO_sectorId: any;
  imageSrc: string | ArrayBuffer | null;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  imagedp: string | null = null; 
  collectionData: any;
  insightList: any;
  editId: any;
  newPl_dp: any;
  constructor(private formBuilder: FormBuilder,
    private dataTransferService : DataTransferService,
    private toastr:ToastrService,
    private router:Router,
    private activeRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private ngxSpinnerService: NgxSpinnerService) {

      this.publishVoxlistForm = this.formBuilder.group({
        // PL_createdAt: new Date(),
        PL_isActive: true,
        PL_userId: sessionStorage.getItem('userID'),
        PL_title: ['', [Validators.required]],
        PL_voxlist: [[], [Validators.required, Validators.minLength(1)]],
        PL_description: ['',[Validators.required]],
        PL_dp: [null,[Validators.required]],
      })

      const state=this.router.getCurrentNavigation()?.extras.state;
      if(state){
        (this.title=state.title);
        (this.isReadonly=state?.isReadonly);
        (this.collectionData=state.collectionData);
        (this.editId=state.collectionData?.PL_id);
      }else{
        this.router.navigate([`actions/collections`]);
      }
    
    }

  ngOnInit(): void {

    this.initForm();
    // this.getAllSoundBite();
    this.getAllInsights();
    this.getAllUsers();
    this.getAllRole(this.CO_sectorId);
    this.getAllindustry();

    if (this.title === 'Edit') {
      console.log("Collection data from state : ",this.collectionData);
      this.publishVoxlistForm.patchValue(this.collectionData);
      const sectorId1 = this.collectionData.PL_voxlist[0]?.VL_sectorId; // Get the sectorId from the nested structure
      this.patchIndustryId(sectorId1); // Call the patch method to update the industry dropdown
      this.industrySelected=true;
      this.searchForm.get('AN_degreeId')?.enable();
      this.searchForm.get('AN_userId')?.enable();
      this.searchForm.get('AN_questionId')?.enable();

      this.collectionData.PL_voxlist.forEach((obj:any)=>{
      this.selectedAudios.push(obj);
      
      console.log("edit selectedAudios data : ",this.selectedAudios);
      this.getAllRole(sectorId1);
      }
      );

    }else{
      if(!this.industrySelected){
        this.searchForm.get('AN_degreeId')?.disable();
        this.searchForm.get('AN_userId')?.disable();
        this.searchForm.get('AN_questionId')?.disable();
      }
    }
    // this.onSearch();
    // this.getFilteredData();
  }



  
  patchIndustryId(sectorId: string): void {
    this.searchForm.patchValue({
      AN_industryId: sectorId
    });
    this.getFilteredData();
  }

  initForm(){
    this.searchForm = this.formBuilder.group({
      AN_userId: [''],
      AN_questionId: [''],
      AN_industryId: [''],
      AN_degreeId: [''],
    });

  }
  get f() {
    return this.searchForm.controls;
  }

  onSubmitFilter(){
    this.p=1;
    this.getFilteredData();
  }

  getFilteredData() {
  
    console.log("this.searchForm.value",this.searchForm.value);
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.filterResultData(this.searchForm.value).subscribe({
        next: (value: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          // if (value.status == 200){
            this.filteredData = value.data;
            console.log('Filtered data', this.filteredData);
        },
        error: (error:any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          console.log(error);
          this.toastr.error("No data found matching your filters. Please adjust and try again.");
        }
      });
  }

  resetFilter() {
    this.searchForm.patchValue({
      AN_userId:'',
      AN_questionId:'',
      // AN_industryId:'',
      AN_degreeId: '',
  });
  this.getFilteredData();
}
  
  onFileSelected(event: any) {        //to preview image and take file in imageName to pass in upload api
    let selectedFile = event.target.files[0];
    //to preview image and take file in imageName to pass in upload api
    if (event.target.files.length === 0) {
      // Reset both imageName and imageSrc when no file is selected
      this.imageName = null;
      this.imageSrc = null;
      return;
    }

    const newFileName = FileValidator.addTimestamp(selectedFile.name);
    this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });
    this.imagedp = this.imageName.value;

    if(this.imageName){
      const formControl=this.publishVoxlistForm.get('PL_dp');
      formControl?.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));
      formControl?.updateValueAndValidity();
    }

    const fileType = this.imageName.type.split('/')[0];
    const fileExtension = this.imageName.name.split('.').pop()?.toLowerCase();
    if (fileType !== 'image' || fileExtension === 'svg') {
      event.target.value = '';
      this.toastr.info('Please select an image file (excluding SVG).');
      this.imageName = null;
      this.imageSrc = null;
      return;
    }
  
  if (this.imageName && fileType== 'image') {
    const reader = new FileReader();
    reader.onload = (e) => {
      this.imageSrc = e.target?.result as string | ArrayBuffer;
    };
    reader.readAsDataURL(this.imageName);
  } else {
    this.imageSrc = null; // Reset imageSrc if no file selected
  }
    console.log('imageName', this.imageName);
  }
  
    uploadLogoUrl() {          //To upload logo from add new company form
      if (!this.imageName) {
        // this.toastr.error('Please select an image.');
        return;
      }
      console.log('image', this.imageName);
      this.dataTransferService.uploadurl(this.imageName).subscribe((res: any) => {
      });
    }
  
  getAllInsights() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllInsights().subscribe({
      next: (res: any) => {
      console.log(res);
      if (res.statusCode == 200) {
        this.ngxSpinnerService.hide('globalSpinner');
        this.insightList = res.data;
        console.log('insightList', this.insightList);
      }
      },
      error: (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.log('Error Message',error);
      },
    })
  }
 
  onReset() {
    this.submitted = false;
  }

  originalAudios:any[]=[{sharer:'Omkar',question:'What is the basic path to enter in the IT industry?',industry:'IT',role:'Software Developer',audio:"https://voxpod.s3.eu-west-2.amazonaws.com/Zainab-rec/19.+Can+you+provide+a+brief+summary+of+your+career+journey+so+far.mp3"},
  {sharer:'Rushi',question:'What should we do to get 60LPA package?',industry:'Management',role:'Chef Manager',audio:"https://voxpod.s3.eu-west-2.amazonaws.com/Zainab-rec/19.+Can+you+provide+a+brief+summary+of+your+career+journey+so+far2.mp3"},
  {sharer:'Priyanka',question:'How to manage work life balance?',industry:'Health',role:'Doctor',audio:"https://voxpod.s3.eu-west-2.amazonaws.com/Zainab-rec/19.+Can+you+provide+a+brief+summary+of+your+career+journey+so+far3.mp3"}];
  
  selectedAudios:any[]=[];
  
  addToSelected(audioData: any) {
    const isDuplicate = this.selectedAudios.some((selectedAudio) => selectedAudio.AN_recordLink === audioData.AN_recordLink);
    
    if (!isDuplicate) {
      const clonedAudio = { ...audioData };
        this.selectedAudios.push(clonedAudio);
      console.log('selectedAudios', this.selectedAudios);
    } else {
      this.toastr.info('This insight is already selected.');
      // alert('This audio is already in the selected list.');
    }
  }

  removeFromSelectedAudios(audioData: any) {
    const index = this.selectedAudios.indexOf(audioData);
    if (index !== -1) {
      this.selectedAudios.splice(index, 1);
      // this.originalAudios.push(audioData);
    }
    console.log('selectedAudios',this.selectedAudios);
  }

  isSelected(audioData: any): boolean {
    return this.selectedAudios.some(selectedAudio => 
      selectedAudio.AN_recordLink === audioData.AN_recordLink ||
      selectedAudio.VL_soundbiteLink === audioData.AN_recordLink
    );
  }
  
  

  toggleSelection(audioData: any) {
    if (this.isSelected(audioData)) {
      this.removeFromSelectedAudios(audioData);
    } else {
      this.addToSelected(audioData);
    }
  }


  get fVoxList(): any {
    return this.publishVoxlistForm.controls;
  }

  clickPublishVoxlist(){
    this.submittedVoxList = true;  

    if (this.title == 'Edit') {   //Edit New Collection        
          if(this.imageName){
            this.uploadLogoUrl();
            this.publishVoxlistForm.value.PL_dp=this.baseUrl + this.imageName.name;
            this.newPl_dp=this.publishVoxlistForm.value.PL_dp
            console.log("newPl_dp : ",this.newPl_dp);

              }if(!this.imageName){
                this.publishVoxlistForm.value.PL_dp= this.collectionData.PL_dp;
                console.log("For empty this.publishVoxlistForm.value.PL_dp : ",this.publishVoxlistForm.value.PL_dp);

                  } 
         
          this.publishVoxlistForm.value.PL_id = this.collectionData.PL_id;  
          console.log("PL_id : ",this.publishVoxlistForm.value.PL_id);   
          const transformedData = this.selectedAudios.map(audio => ({
            VL_createdAt: audio.AN_createdAt ? audio.AN_createdAt : audio.VL_createdAt,
            VL_questionName:audio.AN_title ? audio.AN_title : audio.VL_questionName,
            VL_questionId : audio.AN_questionId ? audio.AN_questionId : audio.VL_questionId,
            VL_roleId: audio.AN_degreeId ? audio.AN_degreeId : audio.VL_roleId,
            VL_roleName:audio.RO_title ? audio.RO_title : audio.VL_roleName,
            VL_sectorId: audio.AN_industryId ? audio.AN_industryId : audio.VL_sectorId,
            VL_sectorName:audio.IN_name ? audio.IN_name : audio.VL_sectorName,
            VL_sharerDp:audio.AN_dp ? audio.AN_dp : audio.VL_sharerDp,
            VL_sharerName:audio.U_name ? audio.U_name : audio.VL_sharerName,
            VL_sharerUserId:audio.AN_userId ? audio.AN_userId : audio.VL_sharerUserId,
            VL_soundbiteId: audio.AN_id ? audio.AN_id : audio.VL_soundbiteId,
            VL_soundbiteLink: audio.AN_recordLink ? audio.AN_recordLink : audio.VL_soundbiteLink,
            VL_voxpodDuration:audio.AN_voxpodDuration ? audio.AN_voxpodDuration : audio.VL_voxpodDuration
          }));             
          this.publishVoxlistForm.get('PL_voxlist').patchValue(transformedData);
          console.log("For edit collection selectedAudios   : ",transformedData);              

          const postData={
            PL_id:this.editId,
            PL_title:this.publishVoxlistForm.value.PL_title,
            PL_dp: this.newPl_dp ? this.newPl_dp : this.collectionData.PL_dp,
            PL_isActive:this.publishVoxlistForm.value.PL_isActive,
            PL_userId:this.publishVoxlistForm.value.PL_userId,
            PL_description:this.publishVoxlistForm.value.PL_description,
            PL_voxlist:this.publishVoxlistForm.value.PL_voxlist
          }
          console.log("Edit collection post data  : ",postData); 
          
          this.dataTransferService.updateCollection(postData).subscribe((res: any) => {
            if (res.statusCode == 200) {
              this.toastr.success('Collection updated successfully',);
              this.router.navigate([`actions/collections`]);
              this.dataTransferService.getCollection();
            } else {
              this.toastr.error("", res.message);
            }
          })
        }


    else{         //Add new collection  
    const transformedData = this.selectedAudios.map(audio => ({
      VL_createdAt: audio.AN_createdAt,
      VL_questionName:audio.AN_title,
      VL_questionId : audio.AN_questionId,
      VL_roleId: audio.AN_degreeId,
      VL_roleName:audio.RO_title,
      VL_sectorId: audio.AN_industryId,
      VL_sectorName:audio.IN_name,
      VL_sharerDp:audio.AN_dp,
      VL_sharerName:audio.U_name,
      VL_sharerUserId:audio.AN_userId,
      VL_soundbiteId: audio.AN_id,
      VL_soundbiteLink:audio.AN_recordLink,
      VL_voxpodDuration:audio.AN_voxpodDuration
    }));
    this.publishVoxlistForm.get('PL_voxlist').patchValue(transformedData);
       
    this.uploadLogoUrl();
    this.publishVoxlistForm.value.PL_dp=this.baseUrl + this.imageName?.name;
    console.log("this.publishVoxlistForm.value.PL_dp : ",this.publishVoxlistForm.value.PL_dp);
    
    if (this.publishVoxlistForm.valid) {

      console.log('onSubmit', this.publishVoxlistForm.value);
      this.ngxSpinnerService.show('globalSpinner');
      this.dataTransferService.addNewVoxList(this.publishVoxlistForm.value).subscribe({
        next: (value: any) => {
          if (value.statusCode == 200) {
            this.ngxSpinnerService.hide('globalSpinner');
            this.toastr.success('Collection added successfully.');
            // this.publishVoxlistForm.reset();
            
            this.dataTransferService.getCollection();
            this.router.navigate([`actions/collections`]);
          }
        },
        error: (error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          console.log('Error Message',error);
        },
        complete: () => {
          this.ngxSpinnerService.hide('globalSpinner');
        }
      })
    }
  }
    if (this.publishVoxlistForm.invalid) {
      if (this.publishVoxlistForm.get('PL_voxlist').hasError('minlength') || this.isDataArrayEmpty()) {
        this.toastr.warning('New Collection should contain at least one insight.');
      }
      console.log('invalid form 123');
      this.toastr.info('Please filled all required fields correctly');
      return;
    }

  }

  isDataArrayEmpty(): boolean {
    const dataArrayControl = this.publishVoxlistForm.get('PL_voxlist');
    const dataArrayValue = dataArrayControl.value as any[];
    return !dataArrayValue || dataArrayValue.length === 0;
  }

  getAllUsers(){
    this.dataTransferService.getAllAppUsers().subscribe((res:any)=>{
      console.log(res);
      if(res.statusCode==200){
        this.SharerList = res.data.filter((sharer:any)=>sharer.U_isSharer=="1");
        console.log("all sharer names",this.SharerList);
      }
      else {
        this.toastr.error("", res.message);
      }
    })
  }
  
  getAllindustry(){
    this.dataTransferService.getIndustryData().subscribe({
      next:(res:any)=>{
        if (res.status === 200) {
          this.industryList = res.data;
          console.log("CO_sectorId",this.CO_sectorId);
          
          console.log("industryList : ",this.industryList);
        } else {
          console.error('Failed to fetch sectors. Status:', res.status);
        }
      },
      error: (error: any) => {
        console.error('Error occurred while fetching sectors:', error);
      },
    })
  }

  onChangeIndustry(event: any) {
    this.CO_sectorId = event.target.value;
    this.industrySelected = event.target.value !== '';
    if(this.industrySelected){
      this.searchForm.get('AN_degreeId')?.enable();
      this.searchForm.get('AN_userId')?.enable();
      this.searchForm.get('AN_questionId')?.enable();
    }else{
      this.searchForm.get('AN_degreeId')?.disable();
      this.searchForm.get('AN_userId')?.disable();
      this.searchForm.get('AN_questionId')?.disable();
    }
    this.getAllRole(this.CO_sectorId);
    this.getFilteredData();
}

onSelectOtherDropdown(event: any) {
  if (!this.industrySelected) {

    this.toastr.info('Please select sector first.');
    // Prevent selecting options from other dropdowns
    event.target.value = ''; // Reset the value of the dropdown
  }
}


  getAllRole(CO_sectorId:any){
    console.log("sectorId : ",CO_sectorId);
    this.dataTransferService.getAllRoleBySectorId(CO_sectorId).subscribe({
      next: (res: any) => {
        if (res.statusCode === 200) {
          this.Allroles = res.data;
          console.log("RoleList : ",this.Allroles);
        } else {
          console.error('Failed to fetch role. Status:', res.status);
        }
      },
      error: (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error occurred while fetching roles:', error);
      },
    });
  }
  
}

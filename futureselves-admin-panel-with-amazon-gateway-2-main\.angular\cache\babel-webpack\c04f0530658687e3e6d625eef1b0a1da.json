{"ast": null, "code": "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { getSupportedInputTypes } from '@angular/cdk/platform';\nimport * as i4 from '@angular/cdk/text-field';\nimport { TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Optional, Self, Inject, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport * as i3 from '@angular/material/core';\nimport { mixinErrorState, MatCommonModule, ErrorStateMatcher } from '@angular/material/core';\nimport * as i5 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nimport { Subject } from 'rxjs';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @docs-private */\n\nfunction getMatInputUnsupportedTypeError(type) {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\n\n\nconst MAT_INPUT_VALUE_ACCESSOR = new InjectionToken('MAT_INPUT_VALUE_ACCESSOR');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\n\nconst MAT_INPUT_INVALID_TYPES = ['button', 'checkbox', 'file', 'hidden', 'image', 'radio', 'range', 'reset', 'submit'];\nlet nextUniqueId = 0; // Boilerplate for applying mixins to MatInput.\n\n/** @docs-private */\n\nconst _MatInputBase = mixinErrorState(class {\n  constructor(_defaultErrorStateMatcher, _parentForm, _parentFormGroup,\n  /** @docs-private */\n  ngControl) {\n    this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n    this._parentForm = _parentForm;\n    this._parentFormGroup = _parentFormGroup;\n    this.ngControl = ngControl;\n  }\n\n});\n/** Directive that allows a native input to work inside a `MatFormField`. */\n\n\nclass MatInput extends _MatInputBase {\n  constructor(_elementRef, _platform, ngControl, _parentForm, _parentFormGroup, _defaultErrorStateMatcher, inputValueAccessor, _autofillMonitor, ngZone, // TODO: Remove this once the legacy appearance has been removed. We only need\n  // to inject the form-field for determining whether the placeholder has been promoted.\n  _formField) {\n    super(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n    this._elementRef = _elementRef;\n    this._platform = _platform;\n    this._autofillMonitor = _autofillMonitor;\n    this._formField = _formField;\n    this._uid = `mat-input-${nextUniqueId++}`;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n    this.focused = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n    this.stateChanges = new Subject();\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n    this.controlType = 'mat-input';\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n    this.autofilled = false;\n    this._disabled = false;\n    this._type = 'text';\n    this._readonly = false;\n    this._neverEmptyInputTypes = ['date', 'datetime', 'datetime-local', 'month', 'time', 'week'].filter(t => getSupportedInputTypes().has(t));\n\n    this._iOSKeyupListener = event => {\n      const el = event.target; // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n      // indicate different things. If the value is 0, it means that the caret is at the start\n      // of the input, whereas a value of `null` means that the input doesn't support\n      // manipulating the selection range. Inputs that don't support setting the selection range\n      // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n      // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n\n      if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n        // Note: Just setting `0, 0` doesn't fix the issue. Setting\n        // `1, 1` fixes it for the first time that you type text and\n        // then hold delete. Toggling to `1, 1` and then back to\n        // `0, 0` seems to completely fix it.\n        el.setSelectionRange(1, 1);\n        el.setSelectionRange(0, 0);\n      }\n    };\n\n    const element = this._elementRef.nativeElement;\n    const nodeName = element.nodeName.toLowerCase(); // If no input value accessor was explicitly specified, use the element as the input value\n    // accessor.\n\n    this._inputValueAccessor = inputValueAccessor || element;\n    this._previousNativeValue = this.value; // Force setter to be called in case id was not specified.\n\n    this.id = this.id; // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n    // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n    // exists on iOS, we only bother to install the listener on iOS.\n\n    if (_platform.IOS) {\n      ngZone.runOutsideAngular(() => {\n        _elementRef.nativeElement.addEventListener('keyup', this._iOSKeyupListener);\n      });\n    }\n\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeSelect = nodeName === 'select';\n    this._isTextarea = nodeName === 'textarea';\n    this._isInFormField = !!_formField;\n\n    if (this._isNativeSelect) {\n      this.controlType = element.multiple ? 'mat-native-select-multiple' : 'mat-native-select';\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get disabled() {\n    if (this.ngControl && this.ngControl.disabled !== null) {\n      return this.ngControl.disabled;\n    }\n\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value); // Browsers may not fire the blur event if the input is disabled too quickly.\n    // Reset from here to ensure that the element doesn't become stuck.\n\n    if (this.focused) {\n      this.focused = false;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get id() {\n    return this._id;\n  }\n\n  set id(value) {\n    this._id = value || this._uid;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  /** Input type of the element. */\n\n\n  get type() {\n    return this._type;\n  }\n\n  set type(value) {\n    this._type = value || 'text';\n\n    this._validateType(); // When using Angular inputs, developers are no longer able to set the properties on the native\n    // input element. To ensure that bindings for `type` work, we need to sync the setter\n    // with the native property. Textarea elements don't support the type property or attribute.\n\n\n    if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n      this._elementRef.nativeElement.type = this._type;\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get value() {\n    return this._inputValueAccessor.value;\n  }\n\n  set value(value) {\n    if (value !== this.value) {\n      this._inputValueAccessor.value = value;\n      this.stateChanges.next();\n    }\n  }\n  /** Whether the element is readonly. */\n\n\n  get readonly() {\n    return this._readonly;\n  }\n\n  set readonly(value) {\n    this._readonly = coerceBooleanProperty(value);\n  }\n\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n        this.autofilled = event.isAutofilled;\n        this.stateChanges.next();\n      });\n    }\n  }\n\n  ngOnChanges() {\n    this.stateChanges.next();\n  }\n\n  ngOnDestroy() {\n    this.stateChanges.complete();\n\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n    }\n\n    if (this._platform.IOS) {\n      this._elementRef.nativeElement.removeEventListener('keyup', this._iOSKeyupListener);\n    }\n  }\n\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n    } // We need to dirty-check the native element's value, because there are some cases where\n    // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n    // updating the value using `emitEvent: false`).\n\n\n    this._dirtyCheckNativeValue(); // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n    // present or not depends on a query which is prone to \"changed after checked\" errors.\n\n\n    this._dirtyCheckPlaceholder();\n  }\n  /** Focuses the input. */\n\n\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Callback for the cases where the focused state of the input changes. */\n\n\n  _focusChanged(isFocused) {\n    if (isFocused !== this.focused) {\n      this.focused = isFocused;\n      this.stateChanges.next();\n    }\n  }\n\n  _onInput() {// This is a noop function and is used to let Angular know whenever the value changes.\n    // Angular will run a new change detection each time the `input` event has been dispatched.\n    // It's necessary that Angular recognizes the value change, because when floatingLabel\n    // is set to false and Angular forms aren't used, the placeholder won't recognize the\n    // value changes and will not disappear.\n    // Listening to the input event wouldn't be necessary when the input is using the\n    // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n  }\n  /** Does some manual dirty checking on the native input `placeholder` attribute. */\n\n\n  _dirtyCheckPlaceholder() {\n    // If we're hiding the native placeholder, it should also be cleared from the DOM, otherwise\n    // screen readers will read it out twice: once from the label and once from the attribute.\n    // TODO: can be removed once we get rid of the `legacy` style for the form field, because it's\n    // the only one that supports promoting the placeholder to a label.\n    const placeholder = this._formField?._hideControlPlaceholder?.() ? null : this.placeholder;\n\n    if (placeholder !== this._previousPlaceholder) {\n      const element = this._elementRef.nativeElement;\n      this._previousPlaceholder = placeholder;\n      placeholder ? element.setAttribute('placeholder', placeholder) : element.removeAttribute('placeholder');\n    }\n  }\n  /** Does some manual dirty checking on the native input `value` property. */\n\n\n  _dirtyCheckNativeValue() {\n    const newValue = this._elementRef.nativeElement.value;\n\n    if (this._previousNativeValue !== newValue) {\n      this._previousNativeValue = newValue;\n      this.stateChanges.next();\n    }\n  }\n  /** Make sure the input is a supported type. */\n\n\n  _validateType() {\n    if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatInputUnsupportedTypeError(this._type);\n    }\n  }\n  /** Checks whether the input type is one of the types that are never empty. */\n\n\n  _isNeverEmpty() {\n    return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n  }\n  /** Checks whether the input is invalid based on the native validation. */\n\n\n  _isBadInput() {\n    // The `validity` property won't be present on platform-server.\n    let validity = this._elementRef.nativeElement.validity;\n    return validity && validity.badInput;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get empty() {\n    return !this._isNeverEmpty() && !this._elementRef.nativeElement.value && !this._isBadInput() && !this.autofilled;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get shouldLabelFloat() {\n    if (this._isNativeSelect) {\n      // For a single-selection `<select>`, the label should float when the selected option has\n      // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n      // overlapping the label with the options.\n      const selectElement = this._elementRef.nativeElement;\n      const firstOption = selectElement.options[0]; // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n      // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n\n      return this.focused || selectElement.multiple || !this.empty || !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label);\n    } else {\n      return this.focused || !this.empty;\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  onContainerClick() {\n    // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n    // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n    // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n    if (!this.focused) {\n      this.focus();\n    }\n  }\n  /** Whether the form control is a native select that is displayed inline. */\n\n\n  _isInlineSelect() {\n    const element = this._elementRef.nativeElement;\n    return this._isNativeSelect && (element.multiple || element.size > 1);\n  }\n\n}\n\nMatInput.ɵfac = function MatInput_Factory(t) {\n  return new (t || MatInput)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i2.NgControl, 10), i0.ɵɵdirectiveInject(i2.NgForm, 8), i0.ɵɵdirectiveInject(i2.FormGroupDirective, 8), i0.ɵɵdirectiveInject(i3.ErrorStateMatcher), i0.ɵɵdirectiveInject(MAT_INPUT_VALUE_ACCESSOR, 10), i0.ɵɵdirectiveInject(i4.AutofillMonitor), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8));\n};\n\nMatInput.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatInput,\n  selectors: [[\"input\", \"matInput\", \"\"], [\"textarea\", \"matInput\", \"\"], [\"select\", \"matNativeControl\", \"\"], [\"input\", \"matNativeControl\", \"\"], [\"textarea\", \"matNativeControl\", \"\"]],\n  hostAttrs: [1, \"mat-input-element\", \"mat-form-field-autofill-control\"],\n  hostVars: 12,\n  hostBindings: function MatInput_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focus\", function MatInput_focus_HostBindingHandler() {\n        return ctx._focusChanged(true);\n      })(\"blur\", function MatInput_blur_HostBindingHandler() {\n        return ctx._focusChanged(false);\n      })(\"input\", function MatInput_input_HostBindingHandler() {\n        return ctx._onInput();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"disabled\", ctx.disabled)(\"required\", ctx.required);\n      i0.ɵɵattribute(\"id\", ctx.id)(\"data-placeholder\", ctx.placeholder)(\"name\", ctx.name || null)(\"readonly\", ctx.readonly && !ctx._isNativeSelect || null)(\"aria-invalid\", ctx.empty && ctx.required ? null : ctx.errorState)(\"aria-required\", ctx.required);\n      i0.ɵɵclassProp(\"mat-input-server\", ctx._isServer)(\"mat-native-select-inline\", ctx._isInlineSelect());\n    }\n  },\n  inputs: {\n    disabled: \"disabled\",\n    id: \"id\",\n    placeholder: \"placeholder\",\n    name: \"name\",\n    required: \"required\",\n    type: \"type\",\n    errorStateMatcher: \"errorStateMatcher\",\n    userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"],\n    value: \"value\",\n    readonly: \"readonly\"\n  },\n  exportAs: [\"matInput\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MatFormFieldControl,\n    useExisting: MatInput\n  }]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInput, [{\n    type: Directive,\n    args: [{\n      selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n      exportAs: 'matInput',\n      host: {\n        /**\n         * @breaking-change 8.0.0 remove .mat-form-field-autofill-control in favor of AutofillMonitor.\n         */\n        'class': 'mat-input-element mat-form-field-autofill-control',\n        '[class.mat-input-server]': '_isServer',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[attr.id]': 'id',\n        // At the time of writing, we have a lot of customer tests that look up the input based on its\n        // placeholder. Since we sometimes omit the placeholder attribute from the DOM to prevent screen\n        // readers from reading it twice, we have to keep it somewhere in the DOM for the lookup.\n        '[attr.data-placeholder]': 'placeholder',\n        '[disabled]': 'disabled',\n        '[required]': 'required',\n        '[attr.name]': 'name || null',\n        '[attr.readonly]': 'readonly && !_isNativeSelect || null',\n        '[class.mat-native-select-inline]': '_isInlineSelect()',\n        // Only mark the input as invalid for assistive technology if it has a value since the\n        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n        '[attr.aria-required]': 'required',\n        '(focus)': '_focusChanged(true)',\n        '(blur)': '_focusChanged(false)',\n        '(input)': '_onInput()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatInput\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.Platform\n    }, {\n      type: i2.NgControl,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }]\n    }, {\n      type: i2.NgForm,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i2.FormGroupDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i3.ErrorStateMatcher\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }, {\n        type: Inject,\n        args: [MAT_INPUT_VALUE_ACCESSOR]\n      }]\n    }, {\n      type: i4.AutofillMonitor\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i5.MatFormField,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_FORM_FIELD]\n      }]\n    }];\n  }, {\n    disabled: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    value: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass MatInputModule {}\n\nMatInputModule.ɵfac = function MatInputModule_Factory(t) {\n  return new (t || MatInputModule)();\n};\n\nMatInputModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatInputModule\n});\nMatInputModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [ErrorStateMatcher],\n  imports: [[TextFieldModule, MatFormFieldModule, MatCommonModule], TextFieldModule, // We re-export the `MatFormFieldModule` since `MatInput` will almost always\n  // be used together with `MatFormField`.\n  MatFormFieldModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInputModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [MatInput],\n      imports: [TextFieldModule, MatFormFieldModule, MatCommonModule],\n      exports: [TextFieldModule, // We re-export the `MatFormFieldModule` since `MatInput` will almost always\n      // be used together with `MatFormField`.\n      MatFormFieldModule, MatInput],\n      providers: [ErrorStateMatcher]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/material/fesm2020/input.mjs"], "names": ["coerceBooleanProperty", "i1", "getSupportedInputTypes", "i4", "TextFieldModule", "i0", "InjectionToken", "Directive", "Optional", "Self", "Inject", "Input", "NgModule", "i2", "Validators", "i3", "mixinErrorState", "MatCommonModule", "ErrorStateMatcher", "i5", "MAT_FORM_FIELD", "MatFormFieldControl", "MatFormFieldModule", "Subject", "getMatInputUnsupportedTypeError", "type", "Error", "MAT_INPUT_VALUE_ACCESSOR", "MAT_INPUT_INVALID_TYPES", "nextUniqueId", "_MatInputBase", "constructor", "_defaultErrorStateMatcher", "_parentForm", "_parentFormGroup", "ngControl", "MatInput", "_elementRef", "_platform", "inputValueAccessor", "_autofillMonitor", "ngZone", "_formField", "_uid", "focused", "stateChanges", "controlType", "autofilled", "_disabled", "_type", "_readonly", "_neverEmptyInputTypes", "filter", "t", "has", "_iOSKeyupListener", "event", "el", "target", "value", "selectionStart", "selectionEnd", "setSelectionRange", "element", "nativeElement", "nodeName", "toLowerCase", "_inputValueAccessor", "_previousNativeValue", "id", "IOS", "runOutsideAngular", "addEventListener", "_isServer", "<PERSON><PERSON><PERSON><PERSON>", "_isNativeSelect", "_isTextarea", "_isInFormField", "multiple", "disabled", "next", "_id", "required", "_required", "control", "hasValidator", "_validateType", "readonly", "ngAfterViewInit", "monitor", "subscribe", "isAutofilled", "ngOnChanges", "ngOnDestroy", "complete", "stopMonitoring", "removeEventListener", "ngDoCheck", "updateErrorState", "_dirtyCheckNativeValue", "_dirtyCheckPlaceholder", "focus", "options", "_focusChanged", "isFocused", "_onInput", "placeholder", "_hideControlPlaceholder", "_previousPlaceholder", "setAttribute", "removeAttribute", "newValue", "indexOf", "ngDevMode", "_isNeverEmpty", "_isBadInput", "validity", "badInput", "empty", "shouldLabelFloat", "selectElement", "firstOption", "selectedIndex", "label", "setDescribedByIds", "ids", "length", "join", "onContainerClick", "_isInlineSelect", "size", "ɵfac", "ElementRef", "Platform", "NgControl", "NgForm", "FormGroupDirective", "AutofillMonitor", "NgZone", "ɵdir", "provide", "useExisting", "args", "selector", "exportAs", "host", "providers", "decorators", "undefined", "MatFormField", "name", "errorStateMatcher", "userAriaDescribedBy", "MatInputModule", "ɵmod", "ɵinj", "declarations", "imports", "exports"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,uBAAtC;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,sBAAT,QAAuC,uBAAvC;AACA,OAAO,KAAKC,EAAZ,MAAoB,yBAApB;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,QAApC,EAA8CC,IAA9C,EAAoDC,MAApD,EAA4DC,KAA5D,EAAmEC,QAAnE,QAAmF,eAAnF;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,UAAT,QAA2B,gBAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,eAAT,EAA0BC,eAA1B,EAA2CC,iBAA3C,QAAoE,wBAApE;AACA,OAAO,KAAKC,EAAZ,MAAoB,8BAApB;AACA,SAASC,cAAT,EAAyBC,mBAAzB,EAA8CC,kBAA9C,QAAwE,8BAAxE;AACA,SAASC,OAAT,QAAwB,MAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,SAASC,+BAAT,CAAyCC,IAAzC,EAA+C;AAC3C,SAAOC,KAAK,CAAE,eAAcD,IAAK,gCAArB,CAAZ;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAME,wBAAwB,GAAG,IAAIrB,cAAJ,CAAmB,0BAAnB,CAAjC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMsB,uBAAuB,GAAG,CAC5B,QAD4B,EAE5B,UAF4B,EAG5B,MAH4B,EAI5B,QAJ4B,EAK5B,OAL4B,EAM5B,OAN4B,EAO5B,OAP4B,EAQ5B,OAR4B,EAS5B,QAT4B,CAAhC;AAWA,IAAIC,YAAY,GAAG,CAAnB,C,CACA;;AACA;;AACA,MAAMC,aAAa,GAAGd,eAAe,CAAC,MAAM;AACxCe,EAAAA,WAAW,CAACC,yBAAD,EAA4BC,WAA5B,EAAyCC,gBAAzC;AACX;AACAC,EAAAA,SAFW,EAEA;AACP,SAAKH,yBAAL,GAAiCA,yBAAjC;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACH;;AARuC,CAAP,CAArC;AAUA;;;AACA,MAAMC,QAAN,SAAuBN,aAAvB,CAAqC;AACjCC,EAAAA,WAAW,CAACM,WAAD,EAAcC,SAAd,EAAyBH,SAAzB,EAAoCF,WAApC,EAAiDC,gBAAjD,EAAmEF,yBAAnE,EAA8FO,kBAA9F,EAAkHC,gBAAlH,EAAoIC,MAApI,EACX;AACA;AACAC,EAAAA,UAHW,EAGC;AACR,UAAMV,yBAAN,EAAiCC,WAAjC,EAA8CC,gBAA9C,EAAgEC,SAAhE;AACA,SAAKE,WAAL,GAAmBA,WAAnB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACA,SAAKE,gBAAL,GAAwBA,gBAAxB;AACA,SAAKE,UAAL,GAAkBA,UAAlB;AACA,SAAKC,IAAL,GAAa,aAAYd,YAAY,EAAG,EAAxC;AACA;AACR;AACA;AACA;;AACQ,SAAKe,OAAL,GAAe,KAAf;AACA;AACR;AACA;AACA;;AACQ,SAAKC,YAAL,GAAoB,IAAItB,OAAJ,EAApB;AACA;AACR;AACA;AACA;;AACQ,SAAKuB,WAAL,GAAmB,WAAnB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,KAAL,GAAa,MAAb;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,qBAAL,GAA6B,CACzB,MADyB,EAEzB,UAFyB,EAGzB,gBAHyB,EAIzB,OAJyB,EAKzB,MALyB,EAMzB,MANyB,EAO3BC,MAP2B,CAOpBC,CAAC,IAAInD,sBAAsB,GAAGoD,GAAzB,CAA6BD,CAA7B,CAPe,CAA7B;;AAQA,SAAKE,iBAAL,GAA0BC,KAAD,IAAW;AAChC,YAAMC,EAAE,GAAGD,KAAK,CAACE,MAAjB,CADgC,CAEhC;AACA;AACA;AACA;AACA;AACA;;AACA,UAAI,CAACD,EAAE,CAACE,KAAJ,IAAaF,EAAE,CAACG,cAAH,KAAsB,CAAnC,IAAwCH,EAAE,CAACI,YAAH,KAAoB,CAAhE,EAAmE;AAC/D;AACA;AACA;AACA;AACAJ,QAAAA,EAAE,CAACK,iBAAH,CAAqB,CAArB,EAAwB,CAAxB;AACAL,QAAAA,EAAE,CAACK,iBAAH,CAAqB,CAArB,EAAwB,CAAxB;AACH;AACJ,KAhBD;;AAiBA,UAAMC,OAAO,GAAG,KAAK1B,WAAL,CAAiB2B,aAAjC;AACA,UAAMC,QAAQ,GAAGF,OAAO,CAACE,QAAR,CAAiBC,WAAjB,EAAjB,CAxDQ,CAyDR;AACA;;AACA,SAAKC,mBAAL,GAA2B5B,kBAAkB,IAAIwB,OAAjD;AACA,SAAKK,oBAAL,GAA4B,KAAKT,KAAjC,CA5DQ,CA6DR;;AACA,SAAKU,EAAL,GAAU,KAAKA,EAAf,CA9DQ,CA+DR;AACA;AACA;;AACA,QAAI/B,SAAS,CAACgC,GAAd,EAAmB;AACf7B,MAAAA,MAAM,CAAC8B,iBAAP,CAAyB,MAAM;AAC3BlC,QAAAA,WAAW,CAAC2B,aAAZ,CAA0BQ,gBAA1B,CAA2C,OAA3C,EAAoD,KAAKjB,iBAAzD;AACH,OAFD;AAGH;;AACD,SAAKkB,SAAL,GAAiB,CAAC,KAAKnC,SAAL,CAAeoC,SAAjC;AACA,SAAKC,eAAL,GAAuBV,QAAQ,KAAK,QAApC;AACA,SAAKW,WAAL,GAAmBX,QAAQ,KAAK,UAAhC;AACA,SAAKY,cAAL,GAAsB,CAAC,CAACnC,UAAxB;;AACA,QAAI,KAAKiC,eAAT,EAA0B;AACtB,WAAK7B,WAAL,GAAmBiB,OAAO,CAACe,QAAR,GACb,4BADa,GAEb,mBAFN;AAGH;AACJ;AACD;AACJ;AACA;AACA;;;AACgB,MAARC,QAAQ,GAAG;AACX,QAAI,KAAK5C,SAAL,IAAkB,KAAKA,SAAL,CAAe4C,QAAf,KAA4B,IAAlD,EAAwD;AACpD,aAAO,KAAK5C,SAAL,CAAe4C,QAAtB;AACH;;AACD,WAAO,KAAK/B,SAAZ;AACH;;AACW,MAAR+B,QAAQ,CAACpB,KAAD,EAAQ;AAChB,SAAKX,SAAL,GAAiBhD,qBAAqB,CAAC2D,KAAD,CAAtC,CADgB,CAEhB;AACA;;AACA,QAAI,KAAKf,OAAT,EAAkB;AACd,WAAKA,OAAL,GAAe,KAAf;AACA,WAAKC,YAAL,CAAkBmC,IAAlB;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACU,MAAFX,EAAE,GAAG;AACL,WAAO,KAAKY,GAAZ;AACH;;AACK,MAAFZ,EAAE,CAACV,KAAD,EAAQ;AACV,SAAKsB,GAAL,GAAWtB,KAAK,IAAI,KAAKhB,IAAzB;AACH;AACD;AACJ;AACA;AACA;;;AACgB,MAARuC,QAAQ,GAAG;AACX,WAAO,KAAKC,SAAL,IAAkB,KAAKhD,SAAL,EAAgBiD,OAAhB,EAAyBC,YAAzB,CAAsCvE,UAAU,CAACoE,QAAjD,CAAlB,IAAgF,KAAvF;AACH;;AACW,MAARA,QAAQ,CAACvB,KAAD,EAAQ;AAChB,SAAKwB,SAAL,GAAiBnF,qBAAqB,CAAC2D,KAAD,CAAtC;AACH;AACD;;;AACQ,MAAJlC,IAAI,GAAG;AACP,WAAO,KAAKwB,KAAZ;AACH;;AACO,MAAJxB,IAAI,CAACkC,KAAD,EAAQ;AACZ,SAAKV,KAAL,GAAaU,KAAK,IAAI,MAAtB;;AACA,SAAK2B,aAAL,GAFY,CAGZ;AACA;AACA;;;AACA,QAAI,CAAC,KAAKV,WAAN,IAAqB1E,sBAAsB,GAAGoD,GAAzB,CAA6B,KAAKL,KAAlC,CAAzB,EAAmE;AAC/D,WAAKZ,WAAL,CAAiB2B,aAAjB,CAA+BvC,IAA/B,GAAsC,KAAKwB,KAA3C;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACa,MAALU,KAAK,GAAG;AACR,WAAO,KAAKQ,mBAAL,CAAyBR,KAAhC;AACH;;AACQ,MAALA,KAAK,CAACA,KAAD,EAAQ;AACb,QAAIA,KAAK,KAAK,KAAKA,KAAnB,EAA0B;AACtB,WAAKQ,mBAAL,CAAyBR,KAAzB,GAAiCA,KAAjC;AACA,WAAKd,YAAL,CAAkBmC,IAAlB;AACH;AACJ;AACD;;;AACY,MAARO,QAAQ,GAAG;AACX,WAAO,KAAKrC,SAAZ;AACH;;AACW,MAARqC,QAAQ,CAAC5B,KAAD,EAAQ;AAChB,SAAKT,SAAL,GAAiBlD,qBAAqB,CAAC2D,KAAD,CAAtC;AACH;;AACD6B,EAAAA,eAAe,GAAG;AACd,QAAI,KAAKlD,SAAL,CAAeoC,SAAnB,EAA8B;AAC1B,WAAKlC,gBAAL,CAAsBiD,OAAtB,CAA8B,KAAKpD,WAAL,CAAiB2B,aAA/C,EAA8D0B,SAA9D,CAAwElC,KAAK,IAAI;AAC7E,aAAKT,UAAL,GAAkBS,KAAK,CAACmC,YAAxB;AACA,aAAK9C,YAAL,CAAkBmC,IAAlB;AACH,OAHD;AAIH;AACJ;;AACDY,EAAAA,WAAW,GAAG;AACV,SAAK/C,YAAL,CAAkBmC,IAAlB;AACH;;AACDa,EAAAA,WAAW,GAAG;AACV,SAAKhD,YAAL,CAAkBiD,QAAlB;;AACA,QAAI,KAAKxD,SAAL,CAAeoC,SAAnB,EAA8B;AAC1B,WAAKlC,gBAAL,CAAsBuD,cAAtB,CAAqC,KAAK1D,WAAL,CAAiB2B,aAAtD;AACH;;AACD,QAAI,KAAK1B,SAAL,CAAegC,GAAnB,EAAwB;AACpB,WAAKjC,WAAL,CAAiB2B,aAAjB,CAA+BgC,mBAA/B,CAAmD,OAAnD,EAA4D,KAAKzC,iBAAjE;AACH;AACJ;;AACD0C,EAAAA,SAAS,GAAG;AACR,QAAI,KAAK9D,SAAT,EAAoB;AAChB;AACA;AACA;AACA,WAAK+D,gBAAL;AACH,KANO,CAOR;AACA;AACA;;;AACA,SAAKC,sBAAL,GAVQ,CAWR;AACA;;;AACA,SAAKC,sBAAL;AACH;AACD;;;AACAC,EAAAA,KAAK,CAACC,OAAD,EAAU;AACX,SAAKjE,WAAL,CAAiB2B,aAAjB,CAA+BqC,KAA/B,CAAqCC,OAArC;AACH;AACD;;;AACAC,EAAAA,aAAa,CAACC,SAAD,EAAY;AACrB,QAAIA,SAAS,KAAK,KAAK5D,OAAvB,EAAgC;AAC5B,WAAKA,OAAL,GAAe4D,SAAf;AACA,WAAK3D,YAAL,CAAkBmC,IAAlB;AACH;AACJ;;AACDyB,EAAAA,QAAQ,GAAG,CACP;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AACD;;;AACAL,EAAAA,sBAAsB,GAAG;AACrB;AACA;AACA;AACA;AACA,UAAMM,WAAW,GAAG,KAAKhE,UAAL,EAAiBiE,uBAAjB,OAA+C,IAA/C,GAAsD,KAAKD,WAA/E;;AACA,QAAIA,WAAW,KAAK,KAAKE,oBAAzB,EAA+C;AAC3C,YAAM7C,OAAO,GAAG,KAAK1B,WAAL,CAAiB2B,aAAjC;AACA,WAAK4C,oBAAL,GAA4BF,WAA5B;AACAA,MAAAA,WAAW,GACL3C,OAAO,CAAC8C,YAAR,CAAqB,aAArB,EAAoCH,WAApC,CADK,GAEL3C,OAAO,CAAC+C,eAAR,CAAwB,aAAxB,CAFN;AAGH;AACJ;AACD;;;AACAX,EAAAA,sBAAsB,GAAG;AACrB,UAAMY,QAAQ,GAAG,KAAK1E,WAAL,CAAiB2B,aAAjB,CAA+BL,KAAhD;;AACA,QAAI,KAAKS,oBAAL,KAA8B2C,QAAlC,EAA4C;AACxC,WAAK3C,oBAAL,GAA4B2C,QAA5B;AACA,WAAKlE,YAAL,CAAkBmC,IAAlB;AACH;AACJ;AACD;;;AACAM,EAAAA,aAAa,GAAG;AACZ,QAAI1D,uBAAuB,CAACoF,OAAxB,CAAgC,KAAK/D,KAArC,IAA8C,CAAC,CAA/C,KACC,OAAOgE,SAAP,KAAqB,WAArB,IAAoCA,SADrC,CAAJ,EACqD;AACjD,YAAMzF,+BAA+B,CAAC,KAAKyB,KAAN,CAArC;AACH;AACJ;AACD;;;AACAiE,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAK/D,qBAAL,CAA2B6D,OAA3B,CAAmC,KAAK/D,KAAxC,IAAiD,CAAC,CAAzD;AACH;AACD;;;AACAkE,EAAAA,WAAW,GAAG;AACV;AACA,QAAIC,QAAQ,GAAG,KAAK/E,WAAL,CAAiB2B,aAAjB,CAA+BoD,QAA9C;AACA,WAAOA,QAAQ,IAAIA,QAAQ,CAACC,QAA5B;AACH;AACD;AACJ;AACA;AACA;;;AACa,MAALC,KAAK,GAAG;AACR,WAAQ,CAAC,KAAKJ,aAAL,EAAD,IACJ,CAAC,KAAK7E,WAAL,CAAiB2B,aAAjB,CAA+BL,KAD5B,IAEJ,CAAC,KAAKwD,WAAL,EAFG,IAGJ,CAAC,KAAKpE,UAHV;AAIH;AACD;AACJ;AACA;AACA;;;AACwB,MAAhBwE,gBAAgB,GAAG;AACnB,QAAI,KAAK5C,eAAT,EAA0B;AACtB;AACA;AACA;AACA,YAAM6C,aAAa,GAAG,KAAKnF,WAAL,CAAiB2B,aAAvC;AACA,YAAMyD,WAAW,GAAGD,aAAa,CAAClB,OAAd,CAAsB,CAAtB,CAApB,CALsB,CAMtB;AACA;;AACA,aAAQ,KAAK1D,OAAL,IACJ4E,aAAa,CAAC1C,QADV,IAEJ,CAAC,KAAKwC,KAFF,IAGJ,CAAC,EAAEE,aAAa,CAACE,aAAd,GAA8B,CAAC,CAA/B,IAAoCD,WAApC,IAAmDA,WAAW,CAACE,KAAjE,CAHL;AAIH,KAZD,MAaK;AACD,aAAO,KAAK/E,OAAL,IAAgB,CAAC,KAAK0E,KAA7B;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIM,EAAAA,iBAAiB,CAACC,GAAD,EAAM;AACnB,QAAIA,GAAG,CAACC,MAAR,EAAgB;AACZ,WAAKzF,WAAL,CAAiB2B,aAAjB,CAA+B6C,YAA/B,CAA4C,kBAA5C,EAAgEgB,GAAG,CAACE,IAAJ,CAAS,GAAT,CAAhE;AACH,KAFD,MAGK;AACD,WAAK1F,WAAL,CAAiB2B,aAAjB,CAA+B8C,eAA/B,CAA+C,kBAA/C;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIkB,EAAAA,gBAAgB,GAAG;AACf;AACA;AACA;AACA,QAAI,CAAC,KAAKpF,OAAV,EAAmB;AACf,WAAKyD,KAAL;AACH;AACJ;AACD;;;AACA4B,EAAAA,eAAe,GAAG;AACd,UAAMlE,OAAO,GAAG,KAAK1B,WAAL,CAAiB2B,aAAjC;AACA,WAAO,KAAKW,eAAL,KAAyBZ,OAAO,CAACe,QAAR,IAAoBf,OAAO,CAACmE,IAAR,GAAe,CAA5D,CAAP;AACH;;AAzTgC;;AA2TrC9F,QAAQ,CAAC+F,IAAT;AAAA,mBAAqG/F,QAArG,EAA2F/B,EAA3F,mBAA+HA,EAAE,CAAC+H,UAAlI,GAA2F/H,EAA3F,mBAAyJJ,EAAE,CAACoI,QAA5J,GAA2FhI,EAA3F,mBAAiLQ,EAAE,CAACyH,SAApL,OAA2FjI,EAA3F,mBAAsOQ,EAAE,CAAC0H,MAAzO,MAA2FlI,EAA3F,mBAA4QQ,EAAE,CAAC2H,kBAA/Q,MAA2FnI,EAA3F,mBAA8TU,EAAE,CAACG,iBAAjU,GAA2Fb,EAA3F,mBAA+VsB,wBAA/V,OAA2FtB,EAA3F,mBAAgaF,EAAE,CAACsI,eAAna,GAA2FpI,EAA3F,mBAA+bA,EAAE,CAACqI,MAAlc,GAA2FrI,EAA3F,mBAAqde,cAArd;AAAA;;AACAgB,QAAQ,CAACuG,IAAT,kBAD2FtI,EAC3F;AAAA,QAAyF+B,QAAzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAD2F/B,MAAAA,EAC3F;AAAA,eAAyF,kBAAc,IAAd,CAAzF;AAAA;AAAA,eAAyF,kBAAc,KAAd,CAAzF;AAAA;AAAA,eAAyF,cAAzF;AAAA;AAAA;;AAAA;AAD2FA,MAAAA,EAC3F;AAD2FA,MAAAA,EAC3F;AAD2FA,MAAAA,EAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAD2FA,EAC3F,oBAAmkC,CAAC;AAAEuI,IAAAA,OAAO,EAAEvH,mBAAX;AAAgCwH,IAAAA,WAAW,EAAEzG;AAA7C,GAAD,CAAnkC,GAD2F/B,EAC3F,6BAD2FA,EAC3F;AAAA;;AACA;AAAA,qDAF2FA,EAE3F,mBAA2F+B,QAA3F,EAAiH,CAAC;AACtGX,IAAAA,IAAI,EAAElB,SADgG;AAEtGuI,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAG;AAC/B,0DAFmB;AAGCC,MAAAA,QAAQ,EAAE,UAHX;AAICC,MAAAA,IAAI,EAAE;AACF;AACxB;AACA;AACwB,iBAAS,mDAJP;AAKF,oCAA4B,WAL1B;AAMF;AACA;AACA,qBAAa,IARX;AASF;AACA;AACA;AACA,mCAA2B,aAZzB;AAaF,sBAAc,UAbZ;AAcF,sBAAc,UAdZ;AAeF,uBAAe,cAfb;AAgBF,2BAAmB,sCAhBjB;AAiBF,4CAAoC,mBAjBlC;AAkBF;AACA;AACA,+BAAuB,yCApBrB;AAqBF,gCAAwB,UArBtB;AAsBF,mBAAW,qBAtBT;AAuBF,kBAAU,sBAvBR;AAwBF,mBAAW;AAxBT,OAJP;AA8BCC,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAEvH,mBAAX;AAAgCwH,QAAAA,WAAW,EAAEzG;AAA7C,OAAD;AA9BZ,KAAD;AAFgG,GAAD,CAAjH,EAkC4B,YAAY;AAAE,WAAO,CAAC;AAAEX,MAAAA,IAAI,EAAEpB,EAAE,CAAC+H;AAAX,KAAD,EAA0B;AAAE3G,MAAAA,IAAI,EAAExB,EAAE,CAACoI;AAAX,KAA1B,EAAiD;AAAE5G,MAAAA,IAAI,EAAEZ,EAAE,CAACyH,SAAX;AAAsBa,MAAAA,UAAU,EAAE,CAAC;AACjH1H,QAAAA,IAAI,EAAEjB;AAD2G,OAAD,EAEjH;AACCiB,QAAAA,IAAI,EAAEhB;AADP,OAFiH;AAAlC,KAAjD,EAI3B;AAAEgB,MAAAA,IAAI,EAAEZ,EAAE,CAAC0H,MAAX;AAAmBY,MAAAA,UAAU,EAAE,CAAC;AAClC1H,QAAAA,IAAI,EAAEjB;AAD4B,OAAD;AAA/B,KAJ2B,EAM3B;AAAEiB,MAAAA,IAAI,EAAEZ,EAAE,CAAC2H,kBAAX;AAA+BW,MAAAA,UAAU,EAAE,CAAC;AAC9C1H,QAAAA,IAAI,EAAEjB;AADwC,OAAD;AAA3C,KAN2B,EAQ3B;AAAEiB,MAAAA,IAAI,EAAEV,EAAE,CAACG;AAAX,KAR2B,EAQK;AAAEO,MAAAA,IAAI,EAAE2H,SAAR;AAAmBD,MAAAA,UAAU,EAAE,CAAC;AAClE1H,QAAAA,IAAI,EAAEjB;AAD4D,OAAD,EAElE;AACCiB,QAAAA,IAAI,EAAEhB;AADP,OAFkE,EAIlE;AACCgB,QAAAA,IAAI,EAAEf,MADP;AAECoI,QAAAA,IAAI,EAAE,CAACnH,wBAAD;AAFP,OAJkE;AAA/B,KARL,EAe3B;AAAEF,MAAAA,IAAI,EAAEtB,EAAE,CAACsI;AAAX,KAf2B,EAeG;AAAEhH,MAAAA,IAAI,EAAEpB,EAAE,CAACqI;AAAX,KAfH,EAewB;AAAEjH,MAAAA,IAAI,EAAEN,EAAE,CAACkI,YAAX;AAAyBF,MAAAA,UAAU,EAAE,CAAC;AAC3F1H,QAAAA,IAAI,EAAEjB;AADqF,OAAD,EAE3F;AACCiB,QAAAA,IAAI,EAAEf,MADP;AAECoI,QAAAA,IAAI,EAAE,CAAC1H,cAAD;AAFP,OAF2F;AAArC,KAfxB,CAAP;AAoBlB,GAtDxB,EAsD0C;AAAE2D,IAAAA,QAAQ,EAAE,CAAC;AACvCtD,MAAAA,IAAI,EAAEd;AADiC,KAAD,CAAZ;AAE1B0D,IAAAA,EAAE,EAAE,CAAC;AACL5C,MAAAA,IAAI,EAAEd;AADD,KAAD,CAFsB;AAI1B+F,IAAAA,WAAW,EAAE,CAAC;AACdjF,MAAAA,IAAI,EAAEd;AADQ,KAAD,CAJa;AAM1B2I,IAAAA,IAAI,EAAE,CAAC;AACP7H,MAAAA,IAAI,EAAEd;AADC,KAAD,CANoB;AAQ1BuE,IAAAA,QAAQ,EAAE,CAAC;AACXzD,MAAAA,IAAI,EAAEd;AADK,KAAD,CARgB;AAU1Bc,IAAAA,IAAI,EAAE,CAAC;AACPA,MAAAA,IAAI,EAAEd;AADC,KAAD,CAVoB;AAY1B4I,IAAAA,iBAAiB,EAAE,CAAC;AACpB9H,MAAAA,IAAI,EAAEd;AADc,KAAD,CAZO;AAc1B6I,IAAAA,mBAAmB,EAAE,CAAC;AACtB/H,MAAAA,IAAI,EAAEd,KADgB;AAEtBmI,MAAAA,IAAI,EAAE,CAAC,kBAAD;AAFgB,KAAD,CAdK;AAiB1BnF,IAAAA,KAAK,EAAE,CAAC;AACRlC,MAAAA,IAAI,EAAEd;AADE,KAAD,CAjBmB;AAmB1B4E,IAAAA,QAAQ,EAAE,CAAC;AACX9D,MAAAA,IAAI,EAAEd;AADK,KAAD;AAnBgB,GAtD1C;AAAA;AA6EA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM8I,cAAN,CAAqB;;AAErBA,cAAc,CAACtB,IAAf;AAAA,mBAA2GsB,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAzF2FrJ,EAyF3F;AAAA,QAA4GoJ;AAA5G;AAKAA,cAAc,CAACE,IAAf,kBA9F2FtJ,EA8F3F;AAAA,aAAuI,CAACa,iBAAD,CAAvI;AAAA,YAAsK,CAACd,eAAD,EAAkBkB,kBAAlB,EAAsCL,eAAtC,CAAtK,EAA8Nb,eAA9N,EACQ;AACA;AACAkB,EAAAA,kBAHR;AAAA;;AAIA;AAAA,qDAlG2FjB,EAkG3F,mBAA2FoJ,cAA3F,EAAuH,CAAC;AAC5GhI,IAAAA,IAAI,EAAEb,QADsG;AAE5GkI,IAAAA,IAAI,EAAE,CAAC;AACCc,MAAAA,YAAY,EAAE,CAACxH,QAAD,CADf;AAECyH,MAAAA,OAAO,EAAE,CAACzJ,eAAD,EAAkBkB,kBAAlB,EAAsCL,eAAtC,CAFV;AAGC6I,MAAAA,OAAO,EAAE,CACL1J,eADK,EAEL;AACA;AACAkB,MAAAA,kBAJK,EAKLc,QALK,CAHV;AAUC8G,MAAAA,SAAS,EAAE,CAAChI,iBAAD;AAVZ,KAAD;AAFsG,GAAD,CAAvH;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASS,wBAAT,EAAmCS,QAAnC,EAA6CqH,cAA7C,EAA6DjI,+BAA7D", "sourcesContent": ["import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { getSupportedInputTypes } from '@angular/cdk/platform';\nimport * as i4 from '@angular/cdk/text-field';\nimport { TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Optional, Self, Inject, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport * as i3 from '@angular/material/core';\nimport { mixinErrorState, MatCommonModule, ErrorStateMatcher } from '@angular/material/core';\nimport * as i5 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nimport { Subject } from 'rxjs';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n    return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\nconst MAT_INPUT_VALUE_ACCESSOR = new InjectionToken('MAT_INPUT_VALUE_ACCESSOR');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = [\n    'button',\n    'checkbox',\n    'file',\n    'hidden',\n    'image',\n    'radio',\n    'range',\n    'reset',\n    'submit',\n];\nlet nextUniqueId = 0;\n// Boilerplate for applying mixins to MatInput.\n/** @docs-private */\nconst _MatInputBase = mixinErrorState(class {\n    constructor(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, \n    /** @docs-private */\n    ngControl) {\n        this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n        this._parentForm = _parentForm;\n        this._parentFormGroup = _parentFormGroup;\n        this.ngControl = ngControl;\n    }\n});\n/** Directive that allows a native input to work inside a `MatFormField`. */\nclass MatInput extends _MatInputBase {\n    constructor(_elementRef, _platform, ngControl, _parentForm, _parentFormGroup, _defaultErrorStateMatcher, inputValueAccessor, _autofillMonitor, ngZone, \n    // TODO: Remove this once the legacy appearance has been removed. We only need\n    // to inject the form-field for determining whether the placeholder has been promoted.\n    _formField) {\n        super(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n        this._elementRef = _elementRef;\n        this._platform = _platform;\n        this._autofillMonitor = _autofillMonitor;\n        this._formField = _formField;\n        this._uid = `mat-input-${nextUniqueId++}`;\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.focused = false;\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.stateChanges = new Subject();\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.controlType = 'mat-input';\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.autofilled = false;\n        this._disabled = false;\n        this._type = 'text';\n        this._readonly = false;\n        this._neverEmptyInputTypes = [\n            'date',\n            'datetime',\n            'datetime-local',\n            'month',\n            'time',\n            'week',\n        ].filter(t => getSupportedInputTypes().has(t));\n        this._iOSKeyupListener = (event) => {\n            const el = event.target;\n            // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n            // indicate different things. If the value is 0, it means that the caret is at the start\n            // of the input, whereas a value of `null` means that the input doesn't support\n            // manipulating the selection range. Inputs that don't support setting the selection range\n            // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n            // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n            if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n                // Note: Just setting `0, 0` doesn't fix the issue. Setting\n                // `1, 1` fixes it for the first time that you type text and\n                // then hold delete. Toggling to `1, 1` and then back to\n                // `0, 0` seems to completely fix it.\n                el.setSelectionRange(1, 1);\n                el.setSelectionRange(0, 0);\n            }\n        };\n        const element = this._elementRef.nativeElement;\n        const nodeName = element.nodeName.toLowerCase();\n        // If no input value accessor was explicitly specified, use the element as the input value\n        // accessor.\n        this._inputValueAccessor = inputValueAccessor || element;\n        this._previousNativeValue = this.value;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n        // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n        // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n        // exists on iOS, we only bother to install the listener on iOS.\n        if (_platform.IOS) {\n            ngZone.runOutsideAngular(() => {\n                _elementRef.nativeElement.addEventListener('keyup', this._iOSKeyupListener);\n            });\n        }\n        this._isServer = !this._platform.isBrowser;\n        this._isNativeSelect = nodeName === 'select';\n        this._isTextarea = nodeName === 'textarea';\n        this._isInFormField = !!_formField;\n        if (this._isNativeSelect) {\n            this.controlType = element.multiple\n                ? 'mat-native-select-multiple'\n                : 'mat-native-select';\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n        if (this.ngControl && this.ngControl.disabled !== null) {\n            return this.ngControl.disabled;\n        }\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // Browsers may not fire the blur event if the input is disabled too quickly.\n        // Reset from here to ensure that the element doesn't become stuck.\n        if (this.focused) {\n            this.focused = false;\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n    }\n    /** Input type of the element. */\n    get type() {\n        return this._type;\n    }\n    set type(value) {\n        this._type = value || 'text';\n        this._validateType();\n        // When using Angular inputs, developers are no longer able to set the properties on the native\n        // input element. To ensure that bindings for `type` work, we need to sync the setter\n        // with the native property. Textarea elements don't support the type property or attribute.\n        if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n            this._elementRef.nativeElement.type = this._type;\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n        return this._inputValueAccessor.value;\n    }\n    set value(value) {\n        if (value !== this.value) {\n            this._inputValueAccessor.value = value;\n            this.stateChanges.next();\n        }\n    }\n    /** Whether the element is readonly. */\n    get readonly() {\n        return this._readonly;\n    }\n    set readonly(value) {\n        this._readonly = coerceBooleanProperty(value);\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n                this.autofilled = event.isAutofilled;\n                this.stateChanges.next();\n            });\n        }\n    }\n    ngOnChanges() {\n        this.stateChanges.next();\n    }\n    ngOnDestroy() {\n        this.stateChanges.complete();\n        if (this._platform.isBrowser) {\n            this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n        }\n        if (this._platform.IOS) {\n            this._elementRef.nativeElement.removeEventListener('keyup', this._iOSKeyupListener);\n        }\n    }\n    ngDoCheck() {\n        if (this.ngControl) {\n            // We need to re-evaluate this on every change detection cycle, because there are some\n            // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n            // that whatever logic is in here has to be super lean or we risk destroying the performance.\n            this.updateErrorState();\n        }\n        // We need to dirty-check the native element's value, because there are some cases where\n        // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n        // updating the value using `emitEvent: false`).\n        this._dirtyCheckNativeValue();\n        // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n        // present or not depends on a query which is prone to \"changed after checked\" errors.\n        this._dirtyCheckPlaceholder();\n    }\n    /** Focuses the input. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Callback for the cases where the focused state of the input changes. */\n    _focusChanged(isFocused) {\n        if (isFocused !== this.focused) {\n            this.focused = isFocused;\n            this.stateChanges.next();\n        }\n    }\n    _onInput() {\n        // This is a noop function and is used to let Angular know whenever the value changes.\n        // Angular will run a new change detection each time the `input` event has been dispatched.\n        // It's necessary that Angular recognizes the value change, because when floatingLabel\n        // is set to false and Angular forms aren't used, the placeholder won't recognize the\n        // value changes and will not disappear.\n        // Listening to the input event wouldn't be necessary when the input is using the\n        // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n    }\n    /** Does some manual dirty checking on the native input `placeholder` attribute. */\n    _dirtyCheckPlaceholder() {\n        // If we're hiding the native placeholder, it should also be cleared from the DOM, otherwise\n        // screen readers will read it out twice: once from the label and once from the attribute.\n        // TODO: can be removed once we get rid of the `legacy` style for the form field, because it's\n        // the only one that supports promoting the placeholder to a label.\n        const placeholder = this._formField?._hideControlPlaceholder?.() ? null : this.placeholder;\n        if (placeholder !== this._previousPlaceholder) {\n            const element = this._elementRef.nativeElement;\n            this._previousPlaceholder = placeholder;\n            placeholder\n                ? element.setAttribute('placeholder', placeholder)\n                : element.removeAttribute('placeholder');\n        }\n    }\n    /** Does some manual dirty checking on the native input `value` property. */\n    _dirtyCheckNativeValue() {\n        const newValue = this._elementRef.nativeElement.value;\n        if (this._previousNativeValue !== newValue) {\n            this._previousNativeValue = newValue;\n            this.stateChanges.next();\n        }\n    }\n    /** Make sure the input is a supported type. */\n    _validateType() {\n        if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatInputUnsupportedTypeError(this._type);\n        }\n    }\n    /** Checks whether the input type is one of the types that are never empty. */\n    _isNeverEmpty() {\n        return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n    }\n    /** Checks whether the input is invalid based on the native validation. */\n    _isBadInput() {\n        // The `validity` property won't be present on platform-server.\n        let validity = this._elementRef.nativeElement.validity;\n        return validity && validity.badInput;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n        return (!this._isNeverEmpty() &&\n            !this._elementRef.nativeElement.value &&\n            !this._isBadInput() &&\n            !this.autofilled);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        if (this._isNativeSelect) {\n            // For a single-selection `<select>`, the label should float when the selected option has\n            // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n            // overlapping the label with the options.\n            const selectElement = this._elementRef.nativeElement;\n            const firstOption = selectElement.options[0];\n            // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n            // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n            return (this.focused ||\n                selectElement.multiple ||\n                !this.empty ||\n                !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label));\n        }\n        else {\n            return this.focused || !this.empty;\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        if (ids.length) {\n            this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            this._elementRef.nativeElement.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n        // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n        // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n        if (!this.focused) {\n            this.focus();\n        }\n    }\n    /** Whether the form control is a native select that is displayed inline. */\n    _isInlineSelect() {\n        const element = this._elementRef.nativeElement;\n        return this._isNativeSelect && (element.multiple || element.size > 1);\n    }\n}\nMatInput.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatInput, deps: [{ token: i0.ElementRef }, { token: i1.Platform }, { token: i2.NgControl, optional: true, self: true }, { token: i2.NgForm, optional: true }, { token: i2.FormGroupDirective, optional: true }, { token: i3.ErrorStateMatcher }, { token: MAT_INPUT_VALUE_ACCESSOR, optional: true, self: true }, { token: i4.AutofillMonitor }, { token: i0.NgZone }, { token: MAT_FORM_FIELD, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatInput.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatInput, selector: \"input[matInput], textarea[matInput], select[matNativeControl],\\n      input[matNativeControl], textarea[matNativeControl]\", inputs: { disabled: \"disabled\", id: \"id\", placeholder: \"placeholder\", name: \"name\", required: \"required\", type: \"type\", errorStateMatcher: \"errorStateMatcher\", userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], value: \"value\", readonly: \"readonly\" }, host: { listeners: { \"focus\": \"_focusChanged(true)\", \"blur\": \"_focusChanged(false)\", \"input\": \"_onInput()\" }, properties: { \"class.mat-input-server\": \"_isServer\", \"attr.id\": \"id\", \"attr.data-placeholder\": \"placeholder\", \"disabled\": \"disabled\", \"required\": \"required\", \"attr.name\": \"name || null\", \"attr.readonly\": \"readonly && !_isNativeSelect || null\", \"class.mat-native-select-inline\": \"_isInlineSelect()\", \"attr.aria-invalid\": \"(empty && required) ? null : errorState\", \"attr.aria-required\": \"required\" }, classAttribute: \"mat-input-element mat-form-field-autofill-control\" }, providers: [{ provide: MatFormFieldControl, useExisting: MatInput }], exportAs: [\"matInput\"], usesInheritance: true, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n                    exportAs: 'matInput',\n                    host: {\n                        /**\n                         * @breaking-change 8.0.0 remove .mat-form-field-autofill-control in favor of AutofillMonitor.\n                         */\n                        'class': 'mat-input-element mat-form-field-autofill-control',\n                        '[class.mat-input-server]': '_isServer',\n                        // Native input properties that are overwritten by Angular inputs need to be synced with\n                        // the native input element. Otherwise property bindings for those don't work.\n                        '[attr.id]': 'id',\n                        // At the time of writing, we have a lot of customer tests that look up the input based on its\n                        // placeholder. Since we sometimes omit the placeholder attribute from the DOM to prevent screen\n                        // readers from reading it twice, we have to keep it somewhere in the DOM for the lookup.\n                        '[attr.data-placeholder]': 'placeholder',\n                        '[disabled]': 'disabled',\n                        '[required]': 'required',\n                        '[attr.name]': 'name || null',\n                        '[attr.readonly]': 'readonly && !_isNativeSelect || null',\n                        '[class.mat-native-select-inline]': '_isInlineSelect()',\n                        // Only mark the input as invalid for assistive technology if it has a value since the\n                        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n                        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n                        '[attr.aria-required]': 'required',\n                        '(focus)': '_focusChanged(true)',\n                        '(blur)': '_focusChanged(false)',\n                        '(input)': '_onInput()',\n                    },\n                    providers: [{ provide: MatFormFieldControl, useExisting: MatInput }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.Platform }, { type: i2.NgControl, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }] }, { type: i2.NgForm, decorators: [{\n                    type: Optional\n                }] }, { type: i2.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i3.ErrorStateMatcher }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }, {\n                    type: Inject,\n                    args: [MAT_INPUT_VALUE_ACCESSOR]\n                }] }, { type: i4.AutofillMonitor }, { type: i0.NgZone }, { type: i5.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }] }]; }, propDecorators: { disabled: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], errorStateMatcher: [{\n                type: Input\n            }], userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], value: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass MatInputModule {\n}\nMatInputModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatInputModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatInputModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatInputModule, declarations: [MatInput], imports: [TextFieldModule, MatFormFieldModule, MatCommonModule], exports: [TextFieldModule,\n        // We re-export the `MatFormFieldModule` since `MatInput` will almost always\n        // be used together with `MatFormField`.\n        MatFormFieldModule,\n        MatInput] });\nMatInputModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatInputModule, providers: [ErrorStateMatcher], imports: [[TextFieldModule, MatFormFieldModule, MatCommonModule], TextFieldModule,\n        // We re-export the `MatFormFieldModule` since `MatInput` will almost always\n        // be used together with `MatFormField`.\n        MatFormFieldModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatInputModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [MatInput],\n                    imports: [TextFieldModule, MatFormFieldModule, MatCommonModule],\n                    exports: [\n                        TextFieldModule,\n                        // We re-export the `MatFormFieldModule` since `MatInput` will almost always\n                        // be used together with `MatFormField`.\n                        MatFormFieldModule,\n                        MatInput,\n                    ],\n                    providers: [ErrorStateMatcher],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n"]}, "metadata": {}, "sourceType": "module"}
{"ast": null, "code": "import { SPAC<PERSON>, BACKSPACE, DELE<PERSON>, TAB, hasMod<PERSON><PERSON><PERSON>, ENT<PERSON> } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, EventEmitter, Optional, Inject, Attribute, ContentChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Self, ContentChildren, NgModule } from '@angular/core';\nimport * as i3 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, RippleRenderer, MAT_RIPPLE_GLOBAL_OPTIONS, mixinErrorState, MatCommonModule, ErrorStateMatcher } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, merge } from 'rxjs';\nimport { take, takeUntil, startWith } from 'rxjs/operators';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\n/** Event object emitted by MatChip when selected or deselected. */\n\nconst _c0 = [\"*\"];\n\nclass MatChipSelectionChange {\n  constructor(\n  /** Reference to the chip that emitted the event. */\n  source,\n  /** Whether the chip that emitted the event is selected. */\n  selected,\n  /** Whether the selection change was a result of a user interaction. */\n  isUserInput = false) {\n    this.source = source;\n    this.selected = selected;\n    this.isUserInput = isUserInput;\n  }\n\n}\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst MAT_CHIP_REMOVE = new InjectionToken('MatChipRemove');\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst MAT_CHIP_AVATAR = new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst MAT_CHIP_TRAILING_ICON = new InjectionToken('MatChipTrailingIcon'); // Boilerplate for applying mixins to MatChip.\n\n/** @docs-private */\n\nclass MatChipBase {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n\n}\n\nconst _MatChipMixinBase = mixinTabIndex(mixinColor(mixinDisableRipple(MatChipBase), 'primary'), -1);\n/**\n * Dummy directive to add CSS class to chip avatar.\n * @docs-private\n */\n\n\nclass MatChipAvatar {}\n\nMatChipAvatar.ɵfac = function MatChipAvatar_Factory(t) {\n  return new (t || MatChipAvatar)();\n};\n\nMatChipAvatar.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChipAvatar,\n  selectors: [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]],\n  hostAttrs: [1, \"mat-chip-avatar\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_CHIP_AVATAR,\n    useExisting: MatChipAvatar\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipAvatar, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-chip-avatar, [matChipAvatar]',\n      host: {\n        'class': 'mat-chip-avatar'\n      },\n      providers: [{\n        provide: MAT_CHIP_AVATAR,\n        useExisting: MatChipAvatar\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Dummy directive to add CSS class to chip trailing icon.\n * @docs-private\n */\n\n\nclass MatChipTrailingIcon {}\n\nMatChipTrailingIcon.ɵfac = function MatChipTrailingIcon_Factory(t) {\n  return new (t || MatChipTrailingIcon)();\n};\n\nMatChipTrailingIcon.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChipTrailingIcon,\n  selectors: [[\"mat-chip-trailing-icon\"], [\"\", \"matChipTrailingIcon\", \"\"]],\n  hostAttrs: [1, \"mat-chip-trailing-icon\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_CHIP_TRAILING_ICON,\n    useExisting: MatChipTrailingIcon\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipTrailingIcon, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n      host: {\n        'class': 'mat-chip-trailing-icon'\n      },\n      providers: [{\n        provide: MAT_CHIP_TRAILING_ICON,\n        useExisting: MatChipTrailingIcon\n      }]\n    }]\n  }], null, null);\n})();\n/** Material Design styled chip directive. Used inside the MatChipList component. */\n\n\nclass MatChip extends _MatChipMixinBase {\n  constructor(elementRef, _ngZone, platform, globalRippleOptions, _changeDetectorRef, _document, animationMode, tabIndex) {\n    super(elementRef);\n    this._ngZone = _ngZone;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Whether the chip has focus. */\n\n    this._hasFocus = false;\n    /** Whether the chip list is selectable */\n\n    this.chipListSelectable = true;\n    /** Whether the chip list is in multi-selection mode. */\n\n    this._chipListMultiple = false;\n    /** Whether the chip list as a whole is disabled. */\n\n    this._chipListDisabled = false;\n    this._selected = false;\n    this._selectable = true;\n    this._disabled = false;\n    this._removable = true;\n    /** Emits when the chip is focused. */\n\n    this._onFocus = new Subject();\n    /** Emits when the chip is blured. */\n\n    this._onBlur = new Subject();\n    /** Emitted when the chip is selected or deselected. */\n\n    this.selectionChange = new EventEmitter();\n    /** Emitted when the chip is destroyed. */\n\n    this.destroyed = new EventEmitter();\n    /** Emitted when a chip is to be removed. */\n\n    this.removed = new EventEmitter();\n\n    this._addHostClassName(); // Dynamically create the ripple target, append it within the chip, and use it as the\n    // chip's ripple target. Adding the class '.mat-chip-ripple' ensures that it will have\n    // the proper styles.\n\n\n    this._chipRippleTarget = _document.createElement('div');\n\n    this._chipRippleTarget.classList.add('mat-chip-ripple');\n\n    this._elementRef.nativeElement.appendChild(this._chipRippleTarget);\n\n    this._chipRipple = new RippleRenderer(this, _ngZone, this._chipRippleTarget, platform);\n\n    this._chipRipple.setupTriggerEvents(elementRef);\n\n    this.rippleConfig = globalRippleOptions || {};\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n    this.tabIndex = tabIndex != null ? parseInt(tabIndex) || -1 : -1;\n  }\n  /**\n   * Whether ripples are disabled on interaction\n   * @docs-private\n   */\n\n\n  get rippleDisabled() {\n    return this.disabled || this.disableRipple || this._animationsDisabled || !!this.rippleConfig.disabled;\n  }\n  /** Whether the chip is selected. */\n\n\n  get selected() {\n    return this._selected;\n  }\n\n  set selected(value) {\n    const coercedValue = coerceBooleanProperty(value);\n\n    if (coercedValue !== this._selected) {\n      this._selected = coercedValue;\n\n      this._dispatchSelectionChange();\n    }\n  }\n  /** The value of the chip. Defaults to the content inside `<mat-chip>` tags. */\n\n\n  get value() {\n    return this._value !== undefined ? this._value : this._elementRef.nativeElement.textContent;\n  }\n\n  set value(value) {\n    this._value = value;\n  }\n  /**\n   * Whether or not the chip is selectable. When a chip is not selectable,\n   * changes to its selected state are always ignored. By default a chip is\n   * selectable, and it becomes non-selectable if its parent chip list is\n   * not selectable.\n   */\n\n\n  get selectable() {\n    return this._selectable && this.chipListSelectable;\n  }\n\n  set selectable(value) {\n    this._selectable = coerceBooleanProperty(value);\n  }\n  /** Whether the chip is disabled. */\n\n\n  get disabled() {\n    return this._chipListDisabled || this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  /**\n   * Determines whether or not the chip displays the remove styling and emits (removed) events.\n   */\n\n\n  get removable() {\n    return this._removable;\n  }\n\n  set removable(value) {\n    this._removable = coerceBooleanProperty(value);\n  }\n  /** The ARIA selected applied to the chip. */\n\n\n  get ariaSelected() {\n    // Remove the `aria-selected` when the chip is deselected in single-selection mode, because\n    // it adds noise to NVDA users where \"not selected\" will be read out for each chip.\n    return this.selectable && (this._chipListMultiple || this.selected) ? this.selected.toString() : null;\n  }\n\n  _addHostClassName() {\n    const basicChipAttrName = 'mat-basic-chip';\n    const element = this._elementRef.nativeElement;\n\n    if (element.hasAttribute(basicChipAttrName) || element.tagName.toLowerCase() === basicChipAttrName) {\n      element.classList.add(basicChipAttrName);\n      return;\n    } else {\n      element.classList.add('mat-standard-chip');\n    }\n  }\n\n  ngOnDestroy() {\n    this.destroyed.emit({\n      chip: this\n    });\n\n    this._chipRipple._removeTriggerEvents();\n  }\n  /** Selects the chip. */\n\n\n  select() {\n    if (!this._selected) {\n      this._selected = true;\n\n      this._dispatchSelectionChange();\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Deselects the chip. */\n\n\n  deselect() {\n    if (this._selected) {\n      this._selected = false;\n\n      this._dispatchSelectionChange();\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Select this chip and emit selected event */\n\n\n  selectViaInteraction() {\n    if (!this._selected) {\n      this._selected = true;\n\n      this._dispatchSelectionChange(true);\n\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Toggles the current selected state of this chip. */\n\n\n  toggleSelected(isUserInput = false) {\n    this._selected = !this.selected;\n\n    this._dispatchSelectionChange(isUserInput);\n\n    this._changeDetectorRef.markForCheck();\n\n    return this.selected;\n  }\n  /** Allows for programmatic focusing of the chip. */\n\n\n  focus() {\n    if (!this._hasFocus) {\n      this._elementRef.nativeElement.focus();\n\n      this._onFocus.next({\n        chip: this\n      });\n    }\n\n    this._hasFocus = true;\n  }\n  /**\n   * Allows for programmatic removal of the chip. Called by the MatChipList when the DELETE or\n   * BACKSPACE keys are pressed.\n   *\n   * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n   */\n\n\n  remove() {\n    if (this.removable) {\n      this.removed.emit({\n        chip: this\n      });\n    }\n  }\n  /** Handles click events on the chip. */\n\n\n  _handleClick(event) {\n    if (this.disabled) {\n      event.preventDefault();\n    }\n  }\n  /** Handle custom key presses. */\n\n\n  _handleKeydown(event) {\n    if (this.disabled) {\n      return;\n    }\n\n    switch (event.keyCode) {\n      case DELETE:\n      case BACKSPACE:\n        // If we are removable, remove the focused chip\n        this.remove(); // Always prevent so page navigation does not occur\n\n        event.preventDefault();\n        break;\n\n      case SPACE:\n        // If we are selectable, toggle the focused chip\n        if (this.selectable) {\n          this.toggleSelected(true);\n        } // Always prevent space from scrolling the page since the list has focus\n\n\n        event.preventDefault();\n        break;\n    }\n  }\n\n  _blur() {\n    // When animations are enabled, Angular may end up removing the chip from the DOM a little\n    // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n    // that moves focus not the next item. To work around the issue, we defer marking the chip\n    // as not focused until the next time the zone stabilizes.\n    this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      this._ngZone.run(() => {\n        this._hasFocus = false;\n\n        this._onBlur.next({\n          chip: this\n        });\n      });\n    });\n  }\n\n  _dispatchSelectionChange(isUserInput = false) {\n    this.selectionChange.emit({\n      source: this,\n      isUserInput,\n      selected: this._selected\n    });\n  }\n\n}\n\nMatChip.ɵfac = function MatChip_Factory(t) {\n  return new (t || MatChip)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵinjectAttribute('tabindex'));\n};\n\nMatChip.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChip,\n  selectors: [[\"mat-basic-chip\"], [\"\", \"mat-basic-chip\", \"\"], [\"mat-chip\"], [\"\", \"mat-chip\", \"\"]],\n  contentQueries: function MatChip_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n      i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.avatar = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trailingIcon = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.removeIcon = _t.first);\n    }\n  },\n  hostAttrs: [\"role\", \"option\", 1, \"mat-chip\", \"mat-focus-indicator\"],\n  hostVars: 14,\n  hostBindings: function MatChip_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatChip_click_HostBindingHandler($event) {\n        return ctx._handleClick($event);\n      })(\"keydown\", function MatChip_keydown_HostBindingHandler($event) {\n        return ctx._handleKeydown($event);\n      })(\"focus\", function MatChip_focus_HostBindingHandler() {\n        return ctx.focus();\n      })(\"blur\", function MatChip_blur_HostBindingHandler() {\n        return ctx._blur();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : ctx.tabIndex)(\"disabled\", ctx.disabled || null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-selected\", ctx.ariaSelected);\n      i0.ɵɵclassProp(\"mat-chip-selected\", ctx.selected)(\"mat-chip-with-avatar\", ctx.avatar)(\"mat-chip-with-trailing-icon\", ctx.trailingIcon || ctx.removeIcon)(\"mat-chip-disabled\", ctx.disabled)(\"_mat-animation-noopable\", ctx._animationsDisabled);\n    }\n  },\n  inputs: {\n    color: \"color\",\n    disableRipple: \"disableRipple\",\n    tabIndex: \"tabIndex\",\n    selected: \"selected\",\n    value: \"value\",\n    selectable: \"selectable\",\n    disabled: \"disabled\",\n    removable: \"removable\"\n  },\n  outputs: {\n    selectionChange: \"selectionChange\",\n    destroyed: \"destroyed\",\n    removed: \"removed\"\n  },\n  exportAs: [\"matChip\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChip, [{\n    type: Directive,\n    args: [{\n      selector: `mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]`,\n      inputs: ['color', 'disableRipple', 'tabIndex'],\n      exportAs: 'matChip',\n      host: {\n        'class': 'mat-chip mat-focus-indicator',\n        '[attr.tabindex]': 'disabled ? null : tabIndex',\n        'role': 'option',\n        '[class.mat-chip-selected]': 'selected',\n        '[class.mat-chip-with-avatar]': 'avatar',\n        '[class.mat-chip-with-trailing-icon]': 'trailingIcon || removeIcon',\n        '[class.mat-chip-disabled]': 'disabled',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[attr.disabled]': 'disabled || null',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-selected]': 'ariaSelected',\n        '(click)': '_handleClick($event)',\n        '(keydown)': '_handleKeydown($event)',\n        '(focus)': 'focus()',\n        '(blur)': '_blur()'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }];\n  }, {\n    avatar: [{\n      type: ContentChild,\n      args: [MAT_CHIP_AVATAR]\n    }],\n    trailingIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_TRAILING_ICON]\n    }],\n    removeIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_REMOVE]\n    }],\n    selected: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    selectable: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    destroyed: [{\n      type: Output\n    }],\n    removed: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * Applies proper (click) support and adds styling for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n *     `<mat-chip>\n *       <mat-icon matChipRemove>cancel</mat-icon>\n *     </mat-chip>`\n *\n * You *may* use a custom icon, but you may need to override the `mat-chip-remove` positioning\n * styles to properly center the icon within the chip.\n */\n\n\nclass MatChipRemove {\n  constructor(_parentChip, elementRef) {\n    this._parentChip = _parentChip;\n\n    if (elementRef.nativeElement.nodeName === 'BUTTON') {\n      elementRef.nativeElement.setAttribute('type', 'button');\n    }\n  }\n  /** Calls the parent chip's public `remove()` method if applicable. */\n\n\n  _handleClick(event) {\n    const parentChip = this._parentChip;\n\n    if (parentChip.removable && !parentChip.disabled) {\n      parentChip.remove();\n    } // We need to stop event propagation because otherwise the event will bubble up to the\n    // form field and cause the `onContainerClick` method to be invoked. This method would then\n    // reset the focused chip that has been focused after chip removal. Usually the parent\n    // the parent click listener of the `MatChip` would prevent propagation, but it can happen\n    // that the chip is being removed before the event bubbles up.\n\n\n    event.stopPropagation();\n    event.preventDefault();\n  }\n\n}\n\nMatChipRemove.ɵfac = function MatChipRemove_Factory(t) {\n  return new (t || MatChipRemove)(i0.ɵɵdirectiveInject(MatChip), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nMatChipRemove.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChipRemove,\n  selectors: [[\"\", \"matChipRemove\", \"\"]],\n  hostAttrs: [1, \"mat-chip-remove\", \"mat-chip-trailing-icon\"],\n  hostBindings: function MatChipRemove_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function MatChipRemove_click_HostBindingHandler($event) {\n        return ctx._handleClick($event);\n      });\n    }\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MAT_CHIP_REMOVE,\n    useExisting: MatChipRemove\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipRemove, [{\n    type: Directive,\n    args: [{\n      selector: '[matChipRemove]',\n      host: {\n        'class': 'mat-chip-remove mat-chip-trailing-icon',\n        '(click)': '_handleClick($event)'\n      },\n      providers: [{\n        provide: MAT_CHIP_REMOVE,\n        useExisting: MatChipRemove\n      }]\n    }]\n  }], function () {\n    return [{\n      type: MatChip\n    }, {\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Injection token to be used to override the default options for the chips module. */\n\n\nconst MAT_CHIPS_DEFAULT_OPTIONS = new InjectionToken('mat-chips-default-options');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatChipList.\n\n/** @docs-private */\n\nconst _MatChipListBase = mixinErrorState(class {\n  constructor(_defaultErrorStateMatcher, _parentForm, _parentFormGroup,\n  /** @docs-private */\n  ngControl) {\n    this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n    this._parentForm = _parentForm;\n    this._parentFormGroup = _parentFormGroup;\n    this.ngControl = ngControl;\n  }\n\n}); // Increasing integer for generating unique ids for chip-list components.\n\n\nlet nextUniqueId$1 = 0;\n/** Change event object that is emitted when the chip list value has changed. */\n\nclass MatChipListChange {\n  constructor(\n  /** Chip list that emitted the event. */\n  source,\n  /** Value of the chip list when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n\n}\n/**\n * A material design chips component (named ChipList for its similarity to the List component).\n */\n\n\nclass MatChipList extends _MatChipListBase {\n  constructor(_elementRef, _changeDetectorRef, _dir, _parentForm, _parentFormGroup, _defaultErrorStateMatcher, ngControl) {\n    super(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._dir = _dir;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n\n    this.controlType = 'mat-chip-list';\n    /**\n     * When a chip is destroyed, we store the index of the destroyed chip until the chips\n     * query list notifies about the update. This is necessary because we cannot determine an\n     * appropriate chip that should receive focus until the array of chips updated completely.\n     */\n\n    this._lastDestroyedChipIndex = null;\n    /** Subject that emits when the component has been destroyed. */\n\n    this._destroyed = new Subject();\n    /** Uid of the chip list */\n\n    this._uid = `mat-chip-list-${nextUniqueId$1++}`;\n    /** Tab index for the chip list. */\n\n    this._tabIndex = 0;\n    /**\n     * User defined tab index.\n     * When it is not null, use user defined tab index. Otherwise use _tabIndex\n     */\n\n    this._userTabIndex = null;\n    /** Function when touched */\n\n    this._onTouched = () => {};\n    /** Function when changed */\n\n\n    this._onChange = () => {};\n\n    this._multiple = false;\n\n    this._compareWith = (o1, o2) => o1 === o2;\n\n    this._disabled = false;\n    /** Orientation of the chip list. */\n\n    this.ariaOrientation = 'horizontal';\n    this._selectable = true;\n    /** Event emitted when the selected chip list value has been changed by the user. */\n\n    this.change = new EventEmitter();\n    /**\n     * Event that emits whenever the raw value of the chip-list changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n\n    this.valueChange = new EventEmitter();\n\n    if (this.ngControl) {\n      this.ngControl.valueAccessor = this;\n    }\n  }\n  /** The array of selected chips inside chip list. */\n\n\n  get selected() {\n    return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n  }\n  /** The ARIA role applied to the chip list. */\n\n\n  get role() {\n    return this.empty ? null : 'listbox';\n  }\n  /** Whether the user should be allowed to select multiple chips. */\n\n\n  get multiple() {\n    return this._multiple;\n  }\n\n  set multiple(value) {\n    this._multiple = coerceBooleanProperty(value);\n\n    this._syncChipsState();\n  }\n  /**\n   * A function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n\n\n  get compareWith() {\n    return this._compareWith;\n  }\n\n  set compareWith(fn) {\n    this._compareWith = fn;\n\n    if (this._selectionModel) {\n      // A different comparator means the selection could change.\n      this._initializeSelection();\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get value() {\n    return this._value;\n  }\n\n  set value(value) {\n    this.writeValue(value);\n    this._value = value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get id() {\n    return this._chipInput ? this._chipInput.id : this._uid;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n    this.stateChanges.next();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get placeholder() {\n    return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n  }\n\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  /** Whether any chips or the matChipInput inside of this chip-list has focus. */\n\n\n  get focused() {\n    return this._chipInput && this._chipInput.focused || this._hasFocusedChip();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get empty() {\n    return (!this._chipInput || this._chipInput.empty) && (!this.chips || this.chips.length === 0);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get shouldLabelFloat() {\n    return !this.empty || this.focused;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  get disabled() {\n    return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n\n    this._syncChipsState();\n  }\n  /**\n   * Whether or not this chip list is selectable. When a chip list is not selectable,\n   * the selected states for all the chips inside the chip list are always ignored.\n   */\n\n\n  get selectable() {\n    return this._selectable;\n  }\n\n  set selectable(value) {\n    this._selectable = coerceBooleanProperty(value);\n\n    if (this.chips) {\n      this.chips.forEach(chip => chip.chipListSelectable = this._selectable);\n    }\n  }\n\n  set tabIndex(value) {\n    this._userTabIndex = value;\n    this._tabIndex = value;\n  }\n  /** Combined stream of all of the child chips' selection change events. */\n\n\n  get chipSelectionChanges() {\n    return merge(...this.chips.map(chip => chip.selectionChange));\n  }\n  /** Combined stream of all of the child chips' focus change events. */\n\n\n  get chipFocusChanges() {\n    return merge(...this.chips.map(chip => chip._onFocus));\n  }\n  /** Combined stream of all of the child chips' blur change events. */\n\n\n  get chipBlurChanges() {\n    return merge(...this.chips.map(chip => chip._onBlur));\n  }\n  /** Combined stream of all of the child chips' remove change events. */\n\n\n  get chipRemoveChanges() {\n    return merge(...this.chips.map(chip => chip.destroyed));\n  }\n\n  ngAfterContentInit() {\n    this._keyManager = new FocusKeyManager(this.chips).withWrap().withVerticalOrientation().withHomeAndEnd().withHorizontalOrientation(this._dir ? this._dir.value : 'ltr');\n\n    if (this._dir) {\n      this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(dir => this._keyManager.withHorizontalOrientation(dir));\n    }\n\n    this._keyManager.tabOut.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._allowFocusEscape();\n    }); // When the list changes, re-subscribe\n\n\n    this.chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      if (this.disabled) {\n        // Since this happens after the content has been\n        // checked, we need to defer it to the next tick.\n        Promise.resolve().then(() => {\n          this._syncChipsState();\n        });\n      }\n\n      this._resetChips(); // Reset chips selected/deselected status\n\n\n      this._initializeSelection(); // Check to see if we need to update our tab index\n\n\n      this._updateTabIndex(); // Check to see if we have a destroyed chip and need to refocus\n\n\n      this._updateFocusForDestroyedChips();\n\n      this.stateChanges.next();\n    });\n  }\n\n  ngOnInit() {\n    this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n    this.stateChanges.next();\n  }\n\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n\n      if (this.ngControl.disabled !== this._disabled) {\n        this.disabled = !!this.ngControl.disabled;\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this._destroyed.next();\n\n    this._destroyed.complete();\n\n    this.stateChanges.complete();\n\n    this._dropSubscriptions();\n  }\n  /** Associates an HTML input element with this chip list. */\n\n\n  registerInput(inputElement) {\n    this._chipInput = inputElement; // We use this attribute to match the chip list to its input in test harnesses.\n    // Set the attribute directly here to avoid \"changed after checked\" errors.\n\n    this._elementRef.nativeElement.setAttribute('data-mat-chip-input', inputElement.id);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  setDescribedByIds(ids) {\n    this._ariaDescribedby = ids.join(' ');\n  } // Implemented as part of ControlValueAccessor.\n\n\n  writeValue(value) {\n    if (this.chips) {\n      this._setSelectionByValue(value, false);\n    }\n  } // Implemented as part of ControlValueAccessor.\n\n\n  registerOnChange(fn) {\n    this._onChange = fn;\n  } // Implemented as part of ControlValueAccessor.\n\n\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  } // Implemented as part of ControlValueAccessor.\n\n\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this.stateChanges.next();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n\n\n  onContainerClick(event) {\n    if (!this._originatesFromChip(event)) {\n      this.focus();\n    }\n  }\n  /**\n   * Focuses the first non-disabled chip in this chip list, or the associated input when there\n   * are no eligible chips.\n   */\n\n\n  focus(options) {\n    if (this.disabled) {\n      return;\n    } // TODO: ARIA says this should focus the first `selected` chip if any are selected.\n    // Focus on first element if there's no chipInput inside chip-list\n\n\n    if (this._chipInput && this._chipInput.focused) {// do nothing\n    } else if (this.chips.length > 0) {\n      this._keyManager.setFirstItemActive();\n\n      this.stateChanges.next();\n    } else {\n      this._focusInput(options);\n\n      this.stateChanges.next();\n    }\n  }\n  /** Attempt to focus an input if we have one. */\n\n\n  _focusInput(options) {\n    if (this._chipInput) {\n      this._chipInput.focus(options);\n    }\n  }\n  /**\n   * Pass events to the keyboard manager. Available here for tests.\n   */\n\n\n  _keydown(event) {\n    const target = event.target;\n\n    if (target && target.classList.contains('mat-chip')) {\n      this._keyManager.onKeydown(event);\n\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Check the tab index as you should not be allowed to focus an empty list.\n   */\n\n\n  _updateTabIndex() {\n    // If we have 0 chips, we should not allow keyboard focus\n    this._tabIndex = this._userTabIndex || (this.chips.length === 0 ? -1 : 0);\n  }\n  /**\n   * If the amount of chips changed, we need to update the\n   * key manager state and focus the next closest chip.\n   */\n\n\n  _updateFocusForDestroyedChips() {\n    // Move focus to the closest chip. If no other chips remain, focus the chip-list itself.\n    if (this._lastDestroyedChipIndex != null) {\n      if (this.chips.length) {\n        const newChipIndex = Math.min(this._lastDestroyedChipIndex, this.chips.length - 1);\n\n        this._keyManager.setActiveItem(newChipIndex);\n      } else {\n        this.focus();\n      }\n    }\n\n    this._lastDestroyedChipIndex = null;\n  }\n  /**\n   * Utility to ensure all indexes are valid.\n   *\n   * @param index The index to be checked.\n   * @returns True if the index is valid for our list of chips.\n   */\n\n\n  _isValidIndex(index) {\n    return index >= 0 && index < this.chips.length;\n  }\n\n  _setSelectionByValue(value, isUserInput = true) {\n    this._clearSelection();\n\n    this.chips.forEach(chip => chip.deselect());\n\n    if (Array.isArray(value)) {\n      value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n\n      this._sortValues();\n    } else {\n      const correspondingChip = this._selectValue(value, isUserInput); // Shift focus to the active item. Note that we shouldn't do this in multiple\n      // mode, because we don't know what chip the user interacted with last.\n\n\n      if (correspondingChip) {\n        if (isUserInput) {\n          this._keyManager.setActiveItem(correspondingChip);\n        }\n      }\n    }\n  }\n  /**\n   * Finds and selects the chip based on its value.\n   * @returns Chip that has the corresponding value.\n   */\n\n\n  _selectValue(value, isUserInput = true) {\n    const correspondingChip = this.chips.find(chip => {\n      return chip.value != null && this._compareWith(chip.value, value);\n    });\n\n    if (correspondingChip) {\n      isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n\n      this._selectionModel.select(correspondingChip);\n    }\n\n    return correspondingChip;\n  }\n\n  _initializeSelection() {\n    // Defer setting the value in order to avoid the \"Expression\n    // has changed after it was checked\" errors from Angular.\n    Promise.resolve().then(() => {\n      if (this.ngControl || this._value) {\n        this._setSelectionByValue(this.ngControl ? this.ngControl.value : this._value, false);\n\n        this.stateChanges.next();\n      }\n    });\n  }\n  /**\n   * Deselects every chip in the list.\n   * @param skip Chip that should not be deselected.\n   */\n\n\n  _clearSelection(skip) {\n    this._selectionModel.clear();\n\n    this.chips.forEach(chip => {\n      if (chip !== skip) {\n        chip.deselect();\n      }\n    });\n    this.stateChanges.next();\n  }\n  /**\n   * Sorts the model values, ensuring that they keep the same\n   * order that they have in the panel.\n   */\n\n\n  _sortValues() {\n    if (this._multiple) {\n      this._selectionModel.clear();\n\n      this.chips.forEach(chip => {\n        if (chip.selected) {\n          this._selectionModel.select(chip);\n        }\n      });\n      this.stateChanges.next();\n    }\n  }\n  /** Emits change event to set the model value. */\n\n\n  _propagateChanges(fallbackValue) {\n    let valueToEmit = null;\n\n    if (Array.isArray(this.selected)) {\n      valueToEmit = this.selected.map(chip => chip.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : fallbackValue;\n    }\n\n    this._value = valueToEmit;\n    this.change.emit(new MatChipListChange(this, valueToEmit));\n    this.valueChange.emit(valueToEmit);\n\n    this._onChange(valueToEmit);\n\n    this._changeDetectorRef.markForCheck();\n  }\n  /** When blurred, mark the field as touched when focus moved outside the chip list. */\n\n\n  _blur() {\n    if (!this._hasFocusedChip()) {\n      this._keyManager.setActiveItem(-1);\n    }\n\n    if (!this.disabled) {\n      if (this._chipInput) {\n        // If there's a chip input, we should check whether the focus moved to chip input.\n        // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n        // to chip input, do nothing.\n        // Timeout is needed to wait for the focus() event trigger on chip input.\n        setTimeout(() => {\n          if (!this.focused) {\n            this._markAsTouched();\n          }\n        });\n      } else {\n        // If there's no chip input, then mark the field as touched.\n        this._markAsTouched();\n      }\n    }\n  }\n  /** Mark the field as touched */\n\n\n  _markAsTouched() {\n    this._onTouched();\n\n    this._changeDetectorRef.markForCheck();\n\n    this.stateChanges.next();\n  }\n  /**\n   * Removes the `tabindex` from the chip list and resets it back afterwards, allowing the\n   * user to tab out of it. This prevents the list from capturing focus and redirecting\n   * it back to the first chip, creating a focus trap, if it user tries to tab away.\n   */\n\n\n  _allowFocusEscape() {\n    if (this._tabIndex !== -1) {\n      this._tabIndex = -1;\n      setTimeout(() => {\n        this._tabIndex = this._userTabIndex || 0;\n\n        this._changeDetectorRef.markForCheck();\n      });\n    }\n  }\n\n  _resetChips() {\n    this._dropSubscriptions();\n\n    this._listenToChipsFocus();\n\n    this._listenToChipsSelection();\n\n    this._listenToChipsRemoved();\n  }\n\n  _dropSubscriptions() {\n    if (this._chipFocusSubscription) {\n      this._chipFocusSubscription.unsubscribe();\n\n      this._chipFocusSubscription = null;\n    }\n\n    if (this._chipBlurSubscription) {\n      this._chipBlurSubscription.unsubscribe();\n\n      this._chipBlurSubscription = null;\n    }\n\n    if (this._chipSelectionSubscription) {\n      this._chipSelectionSubscription.unsubscribe();\n\n      this._chipSelectionSubscription = null;\n    }\n\n    if (this._chipRemoveSubscription) {\n      this._chipRemoveSubscription.unsubscribe();\n\n      this._chipRemoveSubscription = null;\n    }\n  }\n  /** Listens to user-generated selection events on each chip. */\n\n\n  _listenToChipsSelection() {\n    this._chipSelectionSubscription = this.chipSelectionChanges.subscribe(event => {\n      event.source.selected ? this._selectionModel.select(event.source) : this._selectionModel.deselect(event.source); // For single selection chip list, make sure the deselected value is unselected.\n\n      if (!this.multiple) {\n        this.chips.forEach(chip => {\n          if (!this._selectionModel.isSelected(chip) && chip.selected) {\n            chip.deselect();\n          }\n        });\n      }\n\n      if (event.isUserInput) {\n        this._propagateChanges();\n      }\n    });\n  }\n  /** Listens to user-generated selection events on each chip. */\n\n\n  _listenToChipsFocus() {\n    this._chipFocusSubscription = this.chipFocusChanges.subscribe(event => {\n      let chipIndex = this.chips.toArray().indexOf(event.chip);\n\n      if (this._isValidIndex(chipIndex)) {\n        this._keyManager.updateActiveItem(chipIndex);\n      }\n\n      this.stateChanges.next();\n    });\n    this._chipBlurSubscription = this.chipBlurChanges.subscribe(() => {\n      this._blur();\n\n      this.stateChanges.next();\n    });\n  }\n\n  _listenToChipsRemoved() {\n    this._chipRemoveSubscription = this.chipRemoveChanges.subscribe(event => {\n      const chip = event.chip;\n      const chipIndex = this.chips.toArray().indexOf(event.chip); // In case the chip that will be removed is currently focused, we temporarily store\n      // the index in order to be able to determine an appropriate sibling chip that will\n      // receive focus.\n\n      if (this._isValidIndex(chipIndex) && chip._hasFocus) {\n        this._lastDestroyedChipIndex = chipIndex;\n      }\n    });\n  }\n  /** Checks whether an event comes from inside a chip element. */\n\n\n  _originatesFromChip(event) {\n    let currentElement = event.target;\n\n    while (currentElement && currentElement !== this._elementRef.nativeElement) {\n      if (currentElement.classList.contains('mat-chip')) {\n        return true;\n      }\n\n      currentElement = currentElement.parentElement;\n    }\n\n    return false;\n  }\n  /** Checks whether any of the chips is focused. */\n\n\n  _hasFocusedChip() {\n    return this.chips && this.chips.some(chip => chip._hasFocus);\n  }\n  /** Syncs the list's state with the individual chips. */\n\n\n  _syncChipsState() {\n    if (this.chips) {\n      this.chips.forEach(chip => {\n        chip._chipListDisabled = this._disabled;\n        chip._chipListMultiple = this.multiple;\n      });\n    }\n  }\n\n}\n\nMatChipList.ɵfac = function MatChipList_Factory(t) {\n  return new (t || MatChipList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(i2.NgForm, 8), i0.ɵɵdirectiveInject(i2.FormGroupDirective, 8), i0.ɵɵdirectiveInject(i3.ErrorStateMatcher), i0.ɵɵdirectiveInject(i2.NgControl, 10));\n};\n\nMatChipList.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MatChipList,\n  selectors: [[\"mat-chip-list\"]],\n  contentQueries: function MatChipList_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, MatChip, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chips = _t);\n    }\n  },\n  hostAttrs: [1, \"mat-chip-list\"],\n  hostVars: 15,\n  hostBindings: function MatChipList_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"focus\", function MatChipList_focus_HostBindingHandler() {\n        return ctx.focus();\n      })(\"blur\", function MatChipList_blur_HostBindingHandler() {\n        return ctx._blur();\n      })(\"keydown\", function MatChipList_keydown_HostBindingHandler($event) {\n        return ctx._keydown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx._uid);\n      i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : ctx._tabIndex)(\"aria-describedby\", ctx._ariaDescribedby || null)(\"aria-required\", ctx.role ? ctx.required : null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState)(\"aria-multiselectable\", ctx.multiple)(\"role\", ctx.role)(\"aria-orientation\", ctx.ariaOrientation);\n      i0.ɵɵclassProp(\"mat-chip-list-disabled\", ctx.disabled)(\"mat-chip-list-invalid\", ctx.errorState)(\"mat-chip-list-required\", ctx.required);\n    }\n  },\n  inputs: {\n    errorStateMatcher: \"errorStateMatcher\",\n    multiple: \"multiple\",\n    compareWith: \"compareWith\",\n    value: \"value\",\n    required: \"required\",\n    placeholder: \"placeholder\",\n    disabled: \"disabled\",\n    ariaOrientation: [\"aria-orientation\", \"ariaOrientation\"],\n    selectable: \"selectable\",\n    tabIndex: \"tabIndex\"\n  },\n  outputs: {\n    change: \"change\",\n    valueChange: \"valueChange\"\n  },\n  exportAs: [\"matChipList\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: MatFormFieldControl,\n    useExisting: MatChipList\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c0,\n  decls: 2,\n  vars: 0,\n  consts: [[1, \"mat-chip-list-wrapper\"]],\n  template: function MatChipList_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n    }\n  },\n  styles: [\".mat-chip{position:relative;box-sizing:border-box;-webkit-tap-highlight-color:transparent;border:none;-webkit-appearance:none;-moz-appearance:none}.mat-standard-chip{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:inline-flex;padding:7px 12px;border-radius:16px;align-items:center;cursor:default;min-height:32px;height:1px}._mat-animation-noopable.mat-standard-chip{transition:none;animation:none}.mat-standard-chip .mat-chip-remove{border:none;-webkit-appearance:none;-moz-appearance:none;padding:0;background:none}.mat-standard-chip .mat-chip-remove.mat-icon,.mat-standard-chip .mat-chip-remove .mat-icon{width:18px;height:18px;font-size:18px}.mat-standard-chip::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;opacity:0;content:\\\"\\\";pointer-events:none;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-standard-chip:hover::after{opacity:.12}.mat-standard-chip:focus{outline:none}.mat-standard-chip:focus::after{opacity:.16}.cdk-high-contrast-active .mat-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-standard-chip:focus{outline:dotted 2px}.cdk-high-contrast-active .mat-standard-chip.mat-chip-selected{outline-width:3px}.mat-standard-chip.mat-chip-disabled::after{opacity:0}.mat-standard-chip.mat-chip-disabled .mat-chip-remove,.mat-standard-chip.mat-chip-disabled .mat-chip-trailing-icon{cursor:default}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar,.mat-standard-chip.mat-chip-with-avatar{padding-top:0;padding-bottom:0}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-right:8px;padding-left:0}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-left:8px;padding-right:0}.mat-standard-chip.mat-chip-with-trailing-icon{padding-top:7px;padding-bottom:7px;padding-right:8px;padding-left:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon{padding-left:8px;padding-right:12px}.mat-standard-chip.mat-chip-with-avatar{padding-left:0;padding-right:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-avatar{padding-right:0;padding-left:12px}.mat-standard-chip .mat-chip-avatar{width:24px;height:24px;margin-right:8px;margin-left:4px}[dir=rtl] .mat-standard-chip .mat-chip-avatar{margin-left:8px;margin-right:4px}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{width:18px;height:18px;cursor:pointer}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-standard-chip .mat-chip-remove,[dir=rtl] .mat-standard-chip .mat-chip-trailing-icon{margin-right:8px;margin-left:0}.mat-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit;overflow:hidden;transform:translateZ(0)}.mat-chip-list-wrapper{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;margin:-4px}.mat-chip-list-wrapper input.mat-input-element,.mat-chip-list-wrapper .mat-standard-chip{margin:4px}.mat-chip-list-stacked .mat-chip-list-wrapper{flex-direction:column;align-items:flex-start}.mat-chip-list-stacked .mat-chip-list-wrapper .mat-standard-chip{width:100%}.mat-chip-avatar{border-radius:50%;justify-content:center;align-items:center;display:flex;overflow:hidden;object-fit:cover}input.mat-chip-input{width:150px;margin:4px;flex:1 0 150px}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-list',\n      template: `<div class=\"mat-chip-list-wrapper\"><ng-content></ng-content></div>`,\n      exportAs: 'matChipList',\n      host: {\n        '[attr.tabindex]': 'disabled ? null : _tabIndex',\n        '[attr.aria-describedby]': '_ariaDescribedby || null',\n        '[attr.aria-required]': 'role ? required : null',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[attr.aria-multiselectable]': 'multiple',\n        '[attr.role]': 'role',\n        '[class.mat-chip-list-disabled]': 'disabled',\n        '[class.mat-chip-list-invalid]': 'errorState',\n        '[class.mat-chip-list-required]': 'required',\n        '[attr.aria-orientation]': 'ariaOrientation',\n        'class': 'mat-chip-list',\n        '(focus)': 'focus()',\n        '(blur)': '_blur()',\n        '(keydown)': '_keydown($event)',\n        '[id]': '_uid'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatChipList\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-chip{position:relative;box-sizing:border-box;-webkit-tap-highlight-color:transparent;border:none;-webkit-appearance:none;-moz-appearance:none}.mat-standard-chip{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:inline-flex;padding:7px 12px;border-radius:16px;align-items:center;cursor:default;min-height:32px;height:1px}._mat-animation-noopable.mat-standard-chip{transition:none;animation:none}.mat-standard-chip .mat-chip-remove{border:none;-webkit-appearance:none;-moz-appearance:none;padding:0;background:none}.mat-standard-chip .mat-chip-remove.mat-icon,.mat-standard-chip .mat-chip-remove .mat-icon{width:18px;height:18px;font-size:18px}.mat-standard-chip::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;opacity:0;content:\\\"\\\";pointer-events:none;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-standard-chip:hover::after{opacity:.12}.mat-standard-chip:focus{outline:none}.mat-standard-chip:focus::after{opacity:.16}.cdk-high-contrast-active .mat-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-standard-chip:focus{outline:dotted 2px}.cdk-high-contrast-active .mat-standard-chip.mat-chip-selected{outline-width:3px}.mat-standard-chip.mat-chip-disabled::after{opacity:0}.mat-standard-chip.mat-chip-disabled .mat-chip-remove,.mat-standard-chip.mat-chip-disabled .mat-chip-trailing-icon{cursor:default}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar,.mat-standard-chip.mat-chip-with-avatar{padding-top:0;padding-bottom:0}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-right:8px;padding-left:0}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-left:8px;padding-right:0}.mat-standard-chip.mat-chip-with-trailing-icon{padding-top:7px;padding-bottom:7px;padding-right:8px;padding-left:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon{padding-left:8px;padding-right:12px}.mat-standard-chip.mat-chip-with-avatar{padding-left:0;padding-right:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-avatar{padding-right:0;padding-left:12px}.mat-standard-chip .mat-chip-avatar{width:24px;height:24px;margin-right:8px;margin-left:4px}[dir=rtl] .mat-standard-chip .mat-chip-avatar{margin-left:8px;margin-right:4px}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{width:18px;height:18px;cursor:pointer}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-standard-chip .mat-chip-remove,[dir=rtl] .mat-standard-chip .mat-chip-trailing-icon{margin-right:8px;margin-left:0}.mat-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit;overflow:hidden;transform:translateZ(0)}.mat-chip-list-wrapper{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;margin:-4px}.mat-chip-list-wrapper input.mat-input-element,.mat-chip-list-wrapper .mat-standard-chip{margin:4px}.mat-chip-list-stacked .mat-chip-list-wrapper{flex-direction:column;align-items:flex-start}.mat-chip-list-stacked .mat-chip-list-wrapper .mat-standard-chip{width:100%}.mat-chip-avatar{border-radius:50%;justify-content:center;align-items:center;display:flex;overflow:hidden;object-fit:cover}input.mat-chip-input{width:150px;margin:4px;flex:1 0 150px}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1$1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i2.NgForm,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i2.FormGroupDirective,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i3.ErrorStateMatcher\n    }, {\n      type: i2.NgControl,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }]\n    }];\n  }, {\n    errorStateMatcher: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    ariaOrientation: [{\n      type: Input,\n      args: ['aria-orientation']\n    }],\n    selectable: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    chips: [{\n      type: ContentChildren,\n      args: [MatChip, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids.\n\n\nlet nextUniqueId = 0;\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of an `<mat-chip-list>`.\n */\n\nclass MatChipInput {\n  constructor(_elementRef, _defaultOptions) {\n    this._elementRef = _elementRef;\n    this._defaultOptions = _defaultOptions;\n    /** Whether the control is focused. */\n\n    this.focused = false;\n    this._addOnBlur = false;\n    /**\n     * The list of key codes that will trigger a chipEnd event.\n     *\n     * Defaults to `[ENTER]`.\n     */\n\n    this.separatorKeyCodes = this._defaultOptions.separatorKeyCodes;\n    /** Emitted when a chip is to be added. */\n\n    this.chipEnd = new EventEmitter();\n    /** The input's placeholder text. */\n\n    this.placeholder = '';\n    /** Unique id for the input. */\n\n    this.id = `mat-chip-list-input-${nextUniqueId++}`;\n    this._disabled = false;\n    this.inputElement = this._elementRef.nativeElement;\n  }\n  /** Register input for chip list */\n\n\n  set chipList(value) {\n    if (value) {\n      this._chipList = value;\n\n      this._chipList.registerInput(this);\n    }\n  }\n  /**\n   * Whether or not the chipEnd event will be emitted when the input is blurred.\n   */\n\n\n  get addOnBlur() {\n    return this._addOnBlur;\n  }\n\n  set addOnBlur(value) {\n    this._addOnBlur = coerceBooleanProperty(value);\n  }\n  /** Whether the input is disabled. */\n\n\n  get disabled() {\n    return this._disabled || this._chipList && this._chipList.disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n  /** Whether the input is empty. */\n\n\n  get empty() {\n    return !this.inputElement.value;\n  }\n\n  ngOnChanges() {\n    this._chipList.stateChanges.next();\n  }\n\n  ngOnDestroy() {\n    this.chipEnd.complete();\n  }\n\n  ngAfterContentInit() {\n    this._focusLastChipOnBackspace = this.empty;\n  }\n  /** Utility method to make host definition/tests more clear. */\n\n\n  _keydown(event) {\n    if (event) {\n      // Allow the user's focus to escape when they're tabbing forward. Note that we don't\n      // want to do this when going backwards, because focus should go back to the first chip.\n      if (event.keyCode === TAB && !hasModifierKey(event, 'shiftKey')) {\n        this._chipList._allowFocusEscape();\n      } // To prevent the user from accidentally deleting chips when pressing BACKSPACE continuously,\n      // We focus the last chip on backspace only after the user has released the backspace button,\n      // and the input is empty (see behaviour in _keyup)\n\n\n      if (event.keyCode === BACKSPACE && this._focusLastChipOnBackspace) {\n        this._chipList._keyManager.setLastItemActive();\n\n        event.preventDefault();\n        return;\n      } else {\n        this._focusLastChipOnBackspace = false;\n      }\n    }\n\n    this._emitChipEnd(event);\n  }\n  /**\n   * Pass events to the keyboard manager. Available here for tests.\n   */\n\n\n  _keyup(event) {\n    // Allow user to move focus to chips next time he presses backspace\n    if (!this._focusLastChipOnBackspace && event.keyCode === BACKSPACE && this.empty) {\n      this._focusLastChipOnBackspace = true;\n      event.preventDefault();\n    }\n  }\n  /** Checks to see if the blur should emit the (chipEnd) event. */\n\n\n  _blur() {\n    if (this.addOnBlur) {\n      this._emitChipEnd();\n    }\n\n    this.focused = false; // Blur the chip list if it is not focused\n\n    if (!this._chipList.focused) {\n      this._chipList._blur();\n    }\n\n    this._chipList.stateChanges.next();\n  }\n\n  _focus() {\n    this.focused = true;\n    this._focusLastChipOnBackspace = this.empty;\n\n    this._chipList.stateChanges.next();\n  }\n  /** Checks to see if the (chipEnd) event needs to be emitted. */\n\n\n  _emitChipEnd(event) {\n    if (!this.inputElement.value && !!event) {\n      this._chipList._keydown(event);\n    }\n\n    if (!event || this._isSeparatorKey(event)) {\n      this.chipEnd.emit({\n        input: this.inputElement,\n        value: this.inputElement.value,\n        chipInput: this\n      });\n      event?.preventDefault();\n    }\n  }\n\n  _onInput() {\n    // Let chip list know whenever the value changes.\n    this._chipList.stateChanges.next();\n  }\n  /** Focuses the input. */\n\n\n  focus(options) {\n    this.inputElement.focus(options);\n  }\n  /** Clears the input */\n\n\n  clear() {\n    this.inputElement.value = '';\n    this._focusLastChipOnBackspace = true;\n  }\n  /** Checks whether a keycode is one of the configured separators. */\n\n\n  _isSeparatorKey(event) {\n    return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n  }\n\n}\n\nMatChipInput.ɵfac = function MatChipInput_Factory(t) {\n  return new (t || MatChipInput)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_CHIPS_DEFAULT_OPTIONS));\n};\n\nMatChipInput.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: MatChipInput,\n  selectors: [[\"input\", \"matChipInputFor\", \"\"]],\n  hostAttrs: [1, \"mat-chip-input\", \"mat-input-element\"],\n  hostVars: 5,\n  hostBindings: function MatChipInput_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function MatChipInput_keydown_HostBindingHandler($event) {\n        return ctx._keydown($event);\n      })(\"keyup\", function MatChipInput_keyup_HostBindingHandler($event) {\n        return ctx._keyup($event);\n      })(\"blur\", function MatChipInput_blur_HostBindingHandler() {\n        return ctx._blur();\n      })(\"focus\", function MatChipInput_focus_HostBindingHandler() {\n        return ctx._focus();\n      })(\"input\", function MatChipInput_input_HostBindingHandler() {\n        return ctx._onInput();\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵhostProperty(\"id\", ctx.id);\n      i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"placeholder\", ctx.placeholder || null)(\"aria-invalid\", ctx._chipList && ctx._chipList.ngControl ? ctx._chipList.ngControl.invalid : null)(\"aria-required\", ctx._chipList && ctx._chipList.required || null);\n    }\n  },\n  inputs: {\n    chipList: [\"matChipInputFor\", \"chipList\"],\n    addOnBlur: [\"matChipInputAddOnBlur\", \"addOnBlur\"],\n    separatorKeyCodes: [\"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"],\n    placeholder: \"placeholder\",\n    id: \"id\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    chipEnd: \"matChipInputTokenEnd\"\n  },\n  exportAs: [\"matChipInput\", \"matChipInputFor\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipInput, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matChipInputFor]',\n      exportAs: 'matChipInput, matChipInputFor',\n      host: {\n        'class': 'mat-chip-input mat-input-element',\n        '(keydown)': '_keydown($event)',\n        '(keyup)': '_keyup($event)',\n        '(blur)': '_blur()',\n        '(focus)': '_focus()',\n        '(input)': '_onInput()',\n        '[id]': 'id',\n        '[attr.disabled]': 'disabled || null',\n        '[attr.placeholder]': 'placeholder || null',\n        '[attr.aria-invalid]': '_chipList && _chipList.ngControl ? _chipList.ngControl.invalid : null',\n        '[attr.aria-required]': '_chipList && _chipList.required || null'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_CHIPS_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    chipList: [{\n      type: Input,\n      args: ['matChipInputFor']\n    }],\n    addOnBlur: [{\n      type: Input,\n      args: ['matChipInputAddOnBlur']\n    }],\n    separatorKeyCodes: [{\n      type: Input,\n      args: ['matChipInputSeparatorKeyCodes']\n    }],\n    chipEnd: [{\n      type: Output,\n      args: ['matChipInputTokenEnd']\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst CHIP_DECLARATIONS = [MatChipList, MatChip, MatChipInput, MatChipRemove, MatChipAvatar, MatChipTrailingIcon];\n\nclass MatChipsModule {}\n\nMatChipsModule.ɵfac = function MatChipsModule_Factory(t) {\n  return new (t || MatChipsModule)();\n};\n\nMatChipsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MatChipsModule\n});\nMatChipsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [ErrorStateMatcher, {\n    provide: MAT_CHIPS_DEFAULT_OPTIONS,\n    useValue: {\n      separatorKeyCodes: [ENTER]\n    }\n  }],\n  imports: [[MatCommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule],\n      exports: CHIP_DECLARATIONS,\n      declarations: CHIP_DECLARATIONS,\n      providers: [ErrorStateMatcher, {\n        provide: MAT_CHIPS_DEFAULT_OPTIONS,\n        useValue: {\n          separatorKeyCodes: [ENTER]\n        }\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipInput, MatChipList, MatChipListChange, MatChipRemove, MatChipSelectionChange, MatChipTrailingIcon, MatChipsModule };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/material/fesm2020/chips.mjs"], "names": ["SPACE", "BACKSPACE", "DELETE", "TAB", "hasModifierKey", "ENTER", "i0", "InjectionToken", "Directive", "EventEmitter", "Optional", "Inject", "Attribute", "ContentChild", "Input", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Self", "ContentChildren", "NgModule", "i3", "mixinTabIndex", "mixinColor", "mixinDisableRipple", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MAT_RIPPLE_GLOBAL_OPTIONS", "mixinErrorState", "MatCommonModule", "ErrorStateMatcher", "coerceBooleanProperty", "i1", "DOCUMENT", "ANIMATION_MODULE_TYPE", "Subject", "merge", "take", "takeUntil", "startWith", "FocusKeyManager", "i1$1", "SelectionModel", "i2", "Validators", "MatFormFieldControl", "MatChipSelectionChange", "constructor", "source", "selected", "isUserInput", "MAT_CHIP_REMOVE", "MAT_CHIP_AVATAR", "MAT_CHIP_TRAILING_ICON", "MatChipBase", "_elementRef", "_MatChipMixinBase", "MatChipAvatar", "ɵfac", "ɵdir", "provide", "useExisting", "type", "args", "selector", "host", "providers", "MatChipTrailingIcon", "MatChip", "elementRef", "_ngZone", "platform", "globalRippleOptions", "_changeDetectorRef", "_document", "animationMode", "tabIndex", "_hasFocus", "chipListSelectable", "_chipListMultiple", "_chipListDisabled", "_selected", "_selectable", "_disabled", "_removable", "_onFocus", "_onBlur", "selectionChange", "destroyed", "removed", "_addHostClassName", "_chipRippleTarget", "createElement", "classList", "add", "nativeElement", "append<PERSON><PERSON><PERSON>", "_chipRipple", "setupTriggerEvents", "rippleConfig", "_animationsDisabled", "parseInt", "rippleDisabled", "disabled", "disable<PERSON><PERSON><PERSON>", "value", "coerced<PERSON><PERSON><PERSON>", "_dispatchSelectionChange", "_value", "undefined", "textContent", "selectable", "removable", "ariaSelected", "toString", "basicChipAttrName", "element", "hasAttribute", "tagName", "toLowerCase", "ngOnDestroy", "emit", "chip", "_removeTriggerEvents", "select", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deselect", "selectViaInteraction", "toggleSelected", "focus", "next", "remove", "_handleClick", "event", "preventDefault", "_handleKeydown", "keyCode", "_blur", "onStable", "pipe", "subscribe", "run", "ElementRef", "NgZone", "Platform", "ChangeDetectorRef", "inputs", "exportAs", "decorators", "avatar", "trailingIcon", "removeIcon", "MatChipRemove", "_parentChip", "nodeName", "setAttribute", "parentChip", "stopPropagation", "MAT_CHIPS_DEFAULT_OPTIONS", "_MatChipListBase", "_defaultErrorStateMatcher", "_parentForm", "_parentFormGroup", "ngControl", "nextUniqueId$1", "MatChipListChange", "MatChipList", "_dir", "controlType", "_lastDestroyedChipIndex", "_destroyed", "_uid", "_tabIndex", "_userTabIndex", "_onTouched", "_onChange", "_multiple", "_compareWith", "o1", "o2", "ariaOrientation", "change", "valueChange", "valueAccessor", "multiple", "_selectionModel", "role", "empty", "_syncChipsState", "compareWith", "fn", "_initializeSelection", "writeValue", "id", "_chipInput", "required", "_required", "control", "hasValidator", "stateChanges", "placeholder", "_placeholder", "focused", "_hasFocusedChip", "chips", "length", "shouldLabelFloat", "for<PERSON>ach", "chipSelectionChanges", "map", "chipFocusChanges", "chipBlurChanges", "chipRemoveChanges", "ngAfterContentInit", "_keyManager", "withWrap", "withVerticalOrientation", "withHomeAndEnd", "withHorizontalOrientation", "dir", "tabOut", "_allowFocusEscape", "changes", "Promise", "resolve", "then", "_resetChips", "_updateTabIndex", "_updateFocusForDestroyedChips", "ngOnInit", "ngDoCheck", "updateErrorState", "complete", "_dropSubscriptions", "registerInput", "inputElement", "setDescribedByIds", "ids", "_ariaDes<PERSON>by", "join", "_setSelectionByValue", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "onContainerClick", "_originatesFromChip", "options", "setFirstItemActive", "_focusInput", "_keydown", "target", "contains", "onKeydown", "newChipIndex", "Math", "min", "setActiveItem", "_isValidIndex", "index", "_clearSelection", "Array", "isArray", "currentValue", "_selectValue", "_sortValues", "correspondingChip", "find", "skip", "clear", "_propagateChanges", "fallback<PERSON><PERSON><PERSON>", "valueToEmit", "setTimeout", "_markAsTouched", "_listenToChipsFocus", "_listenToChipsSelection", "_listenToChipsRemoved", "_chipFocusSubscription", "unsubscribe", "_chipBlurSubscription", "_chipSelectionSubscription", "_chipRemoveSubscription", "isSelected", "chipIndex", "toArray", "indexOf", "updateActiveItem", "currentElement", "parentElement", "some", "Directionality", "NgForm", "FormGroupDirective", "NgControl", "ɵcmp", "template", "encapsulation", "None", "changeDetection", "OnPush", "styles", "errorStateMatcher", "descendants", "nextUniqueId", "MatChipInput", "_defaultOptions", "_addOnBlur", "separatorKeyCodes", "chipEnd", "chipList", "_chipList", "addOnBlur", "ngOnChanges", "_focusLastChipOnBackspace", "setLastItemActive", "_emitChipEnd", "_keyup", "_focus", "_isSeparator<PERSON>ey", "input", "chipInput", "_onInput", "Set", "has", "CHIP_DECLARATIONS", "MatChipsModule", "ɵmod", "ɵinj", "useValue", "imports", "exports", "declarations"], "mappings": "AAAA,SAASA,KAAT,EAAgBC,SAAhB,EAA2BC,MAA3B,EAAmCC,GAAnC,EAAwCC,cAAxC,EAAwDC,KAAxD,QAAqE,uBAArE;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,SAAzB,EAAoCC,YAApC,EAAkDC,QAAlD,EAA4DC,MAA5D,EAAoEC,SAApE,EAA+EC,YAA/E,EAA6FC,KAA7F,EAAoGC,MAApG,EAA4GC,SAA5G,EAAuHC,iBAAvH,EAA0IC,uBAA1I,EAAmKC,IAAnK,EAAyKC,eAAzK,EAA0LC,QAA1L,QAA0M,eAA1M;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,aAAT,EAAwBC,UAAxB,EAAoCC,kBAApC,EAAwDC,cAAxD,EAAwEC,yBAAxE,EAAmGC,eAAnG,EAAoHC,eAApH,EAAqIC,iBAArI,QAA8J,wBAA9J;AACA,SAASC,qBAAT,QAAsC,uBAAtC;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,SAASC,qBAAT,QAAsC,sCAAtC;AACA,SAASC,OAAT,EAAkBC,KAAlB,QAA+B,MAA/B;AACA,SAASC,IAAT,EAAeC,SAAf,EAA0BC,SAA1B,QAA2C,gBAA3C;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,OAAO,KAAKC,IAAZ,MAAsB,mBAAtB;AACA,SAASC,cAAT,QAA+B,0BAA/B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,UAAT,QAA2B,gBAA3B;AACA,SAASC,mBAAT,QAAoC,8BAApC;AAEA;;;;AACA,MAAMC,sBAAN,CAA6B;AACzBC,EAAAA,WAAW;AACX;AACAC,EAAAA,MAFW;AAGX;AACAC,EAAAA,QAJW;AAKX;AACAC,EAAAA,WAAW,GAAG,KANH,EAMU;AACjB,SAAKF,MAAL,GAAcA,MAAd;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACH;;AAXwB;AAa7B;AACA;AACA;AACA;AACA;;;AACA,MAAMC,eAAe,GAAG,IAAI5C,cAAJ,CAAmB,eAAnB,CAAxB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM6C,eAAe,GAAG,IAAI7C,cAAJ,CAAmB,eAAnB,CAAxB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM8C,sBAAsB,GAAG,IAAI9C,cAAJ,CAAmB,qBAAnB,CAA/B,C,CACA;;AACA;;AACA,MAAM+C,WAAN,CAAkB;AACdP,EAAAA,WAAW,CAACQ,WAAD,EAAc;AACrB,SAAKA,WAAL,GAAmBA,WAAnB;AACH;;AAHa;;AAKlB,MAAMC,iBAAiB,GAAGjC,aAAa,CAACC,UAAU,CAACC,kBAAkB,CAAC6B,WAAD,CAAnB,EAAkC,SAAlC,CAAX,EAAyD,CAAC,CAA1D,CAAvC;AACA;AACA;AACA;AACA;;;AACA,MAAMG,aAAN,CAAoB;;AAEpBA,aAAa,CAACC,IAAd;AAAA,mBAA0GD,aAA1G;AAAA;;AACAA,aAAa,CAACE,IAAd,kBADgGrD,EAChG;AAAA,QAA8FmD,aAA9F;AAAA;AAAA;AAAA,aADgGnD,EAChG,oBAAmN,CAAC;AAAEsD,IAAAA,OAAO,EAAER,eAAX;AAA4BS,IAAAA,WAAW,EAAEJ;AAAzC,GAAD,CAAnN;AAAA;;AACA;AAAA,qDAFgGnD,EAEhG,mBAA2FmD,aAA3F,EAAsH,CAAC;AAC3GK,IAAAA,IAAI,EAAEtD,SADqG;AAE3GuD,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,kCADX;AAECC,MAAAA,IAAI,EAAE;AAAE,iBAAS;AAAX,OAFP;AAGCC,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAER,eAAX;AAA4BS,QAAAA,WAAW,EAAEJ;AAAzC,OAAD;AAHZ,KAAD;AAFqG,GAAD,CAAtH;AAAA;AAQA;AACA;AACA;AACA;;;AACA,MAAMU,mBAAN,CAA0B;;AAE1BA,mBAAmB,CAACT,IAApB;AAAA,mBAAgHS,mBAAhH;AAAA;;AACAA,mBAAmB,CAACR,IAApB,kBAjBgGrD,EAiBhG;AAAA,QAAoG6D,mBAApG;AAAA;AAAA;AAAA,aAjBgG7D,EAiBhG,oBAAmP,CAAC;AAAEsD,IAAAA,OAAO,EAAEP,sBAAX;AAAmCQ,IAAAA,WAAW,EAAEM;AAAhD,GAAD,CAAnP;AAAA;;AACA;AAAA,qDAlBgG7D,EAkBhG,mBAA2F6D,mBAA3F,EAA4H,CAAC;AACjHL,IAAAA,IAAI,EAAEtD,SAD2G;AAEjHuD,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,+CADX;AAECC,MAAAA,IAAI,EAAE;AAAE,iBAAS;AAAX,OAFP;AAGCC,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAEP,sBAAX;AAAmCQ,QAAAA,WAAW,EAAEM;AAAhD,OAAD;AAHZ,KAAD;AAF2G,GAAD,CAA5H;AAAA;AAQA;;;AACA,MAAMC,OAAN,SAAsBZ,iBAAtB,CAAwC;AACpCT,EAAAA,WAAW,CAACsB,UAAD,EAAaC,OAAb,EAAsBC,QAAtB,EAAgCC,mBAAhC,EAAqDC,kBAArD,EAAyEC,SAAzE,EAAoFC,aAApF,EAAmGC,QAAnG,EAA6G;AACpH,UAAMP,UAAN;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKG,kBAAL,GAA0BA,kBAA1B;AACA;;AACA,SAAKI,SAAL,GAAiB,KAAjB;AACA;;AACA,SAAKC,kBAAL,GAA0B,IAA1B;AACA;;AACA,SAAKC,iBAAL,GAAyB,KAAzB;AACA;;AACA,SAAKC,iBAAL,GAAyB,KAAzB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA;;AACA,SAAKC,QAAL,GAAgB,IAAIlD,OAAJ,EAAhB;AACA;;AACA,SAAKmD,OAAL,GAAe,IAAInD,OAAJ,EAAf;AACA;;AACA,SAAKoD,eAAL,GAAuB,IAAI9E,YAAJ,EAAvB;AACA;;AACA,SAAK+E,SAAL,GAAiB,IAAI/E,YAAJ,EAAjB;AACA;;AACA,SAAKgF,OAAL,GAAe,IAAIhF,YAAJ,EAAf;;AACA,SAAKiF,iBAAL,GA1BoH,CA2BpH;AACA;AACA;;;AACA,SAAKC,iBAAL,GAAyBjB,SAAS,CAACkB,aAAV,CAAwB,KAAxB,CAAzB;;AACA,SAAKD,iBAAL,CAAuBE,SAAvB,CAAiCC,GAAjC,CAAqC,iBAArC;;AACA,SAAKvC,WAAL,CAAiBwC,aAAjB,CAA+BC,WAA/B,CAA2C,KAAKL,iBAAhD;;AACA,SAAKM,WAAL,GAAmB,IAAIvE,cAAJ,CAAmB,IAAnB,EAAyB4C,OAAzB,EAAkC,KAAKqB,iBAAvC,EAA0DpB,QAA1D,CAAnB;;AACA,SAAK0B,WAAL,CAAiBC,kBAAjB,CAAoC7B,UAApC;;AACA,SAAK8B,YAAL,GAAoB3B,mBAAmB,IAAI,EAA3C;AACA,SAAK4B,mBAAL,GAA2BzB,aAAa,KAAK,gBAA7C;AACA,SAAKC,QAAL,GAAgBA,QAAQ,IAAI,IAAZ,GAAmByB,QAAQ,CAACzB,QAAD,CAAR,IAAsB,CAAC,CAA1C,GAA8C,CAAC,CAA/D;AACH;AACD;AACJ;AACA;AACA;;;AACsB,MAAd0B,cAAc,GAAG;AACjB,WAAQ,KAAKC,QAAL,IACJ,KAAKC,aADD,IAEJ,KAAKJ,mBAFD,IAGJ,CAAC,CAAC,KAAKD,YAAL,CAAkBI,QAHxB;AAIH;AACD;;;AACY,MAARtD,QAAQ,GAAG;AACX,WAAO,KAAKgC,SAAZ;AACH;;AACW,MAARhC,QAAQ,CAACwD,KAAD,EAAQ;AAChB,UAAMC,YAAY,GAAG3E,qBAAqB,CAAC0E,KAAD,CAA1C;;AACA,QAAIC,YAAY,KAAK,KAAKzB,SAA1B,EAAqC;AACjC,WAAKA,SAAL,GAAiByB,YAAjB;;AACA,WAAKC,wBAAL;AACH;AACJ;AACD;;;AACS,MAALF,KAAK,GAAG;AACR,WAAO,KAAKG,MAAL,KAAgBC,SAAhB,GAA4B,KAAKD,MAAjC,GAA0C,KAAKrD,WAAL,CAAiBwC,aAAjB,CAA+Be,WAAhF;AACH;;AACQ,MAALL,KAAK,CAACA,KAAD,EAAQ;AACb,SAAKG,MAAL,GAAcH,KAAd;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACkB,MAAVM,UAAU,GAAG;AACb,WAAO,KAAK7B,WAAL,IAAoB,KAAKJ,kBAAhC;AACH;;AACa,MAAViC,UAAU,CAACN,KAAD,EAAQ;AAClB,SAAKvB,WAAL,GAAmBnD,qBAAqB,CAAC0E,KAAD,CAAxC;AACH;AACD;;;AACY,MAARF,QAAQ,GAAG;AACX,WAAO,KAAKvB,iBAAL,IAA0B,KAAKG,SAAtC;AACH;;AACW,MAARoB,QAAQ,CAACE,KAAD,EAAQ;AAChB,SAAKtB,SAAL,GAAiBpD,qBAAqB,CAAC0E,KAAD,CAAtC;AACH;AACD;AACJ;AACA;;;AACiB,MAATO,SAAS,GAAG;AACZ,WAAO,KAAK5B,UAAZ;AACH;;AACY,MAAT4B,SAAS,CAACP,KAAD,EAAQ;AACjB,SAAKrB,UAAL,GAAkBrD,qBAAqB,CAAC0E,KAAD,CAAvC;AACH;AACD;;;AACgB,MAAZQ,YAAY,GAAG;AACf;AACA;AACA,WAAO,KAAKF,UAAL,KAAoB,KAAKhC,iBAAL,IAA0B,KAAK9B,QAAnD,IACD,KAAKA,QAAL,CAAciE,QAAd,EADC,GAED,IAFN;AAGH;;AACDxB,EAAAA,iBAAiB,GAAG;AAChB,UAAMyB,iBAAiB,GAAG,gBAA1B;AACA,UAAMC,OAAO,GAAG,KAAK7D,WAAL,CAAiBwC,aAAjC;;AACA,QAAIqB,OAAO,CAACC,YAAR,CAAqBF,iBAArB,KACAC,OAAO,CAACE,OAAR,CAAgBC,WAAhB,OAAkCJ,iBADtC,EACyD;AACrDC,MAAAA,OAAO,CAACvB,SAAR,CAAkBC,GAAlB,CAAsBqB,iBAAtB;AACA;AACH,KAJD,MAKK;AACDC,MAAAA,OAAO,CAACvB,SAAR,CAAkBC,GAAlB,CAAsB,mBAAtB;AACH;AACJ;;AACD0B,EAAAA,WAAW,GAAG;AACV,SAAKhC,SAAL,CAAeiC,IAAf,CAAoB;AAAEC,MAAAA,IAAI,EAAE;AAAR,KAApB;;AACA,SAAKzB,WAAL,CAAiB0B,oBAAjB;AACH;AACD;;;AACAC,EAAAA,MAAM,GAAG;AACL,QAAI,CAAC,KAAK3C,SAAV,EAAqB;AACjB,WAAKA,SAAL,GAAiB,IAAjB;;AACA,WAAK0B,wBAAL;;AACA,WAAKlC,kBAAL,CAAwBoD,YAAxB;AACH;AACJ;AACD;;;AACAC,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAK7C,SAAT,EAAoB;AAChB,WAAKA,SAAL,GAAiB,KAAjB;;AACA,WAAK0B,wBAAL;;AACA,WAAKlC,kBAAL,CAAwBoD,YAAxB;AACH;AACJ;AACD;;;AACAE,EAAAA,oBAAoB,GAAG;AACnB,QAAI,CAAC,KAAK9C,SAAV,EAAqB;AACjB,WAAKA,SAAL,GAAiB,IAAjB;;AACA,WAAK0B,wBAAL,CAA8B,IAA9B;;AACA,WAAKlC,kBAAL,CAAwBoD,YAAxB;AACH;AACJ;AACD;;;AACAG,EAAAA,cAAc,CAAC9E,WAAW,GAAG,KAAf,EAAsB;AAChC,SAAK+B,SAAL,GAAiB,CAAC,KAAKhC,QAAvB;;AACA,SAAK0D,wBAAL,CAA8BzD,WAA9B;;AACA,SAAKuB,kBAAL,CAAwBoD,YAAxB;;AACA,WAAO,KAAK5E,QAAZ;AACH;AACD;;;AACAgF,EAAAA,KAAK,GAAG;AACJ,QAAI,CAAC,KAAKpD,SAAV,EAAqB;AACjB,WAAKtB,WAAL,CAAiBwC,aAAjB,CAA+BkC,KAA/B;;AACA,WAAK5C,QAAL,CAAc6C,IAAd,CAAmB;AAAER,QAAAA,IAAI,EAAE;AAAR,OAAnB;AACH;;AACD,SAAK7C,SAAL,GAAiB,IAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIsD,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKnB,SAAT,EAAoB;AAChB,WAAKvB,OAAL,CAAagC,IAAb,CAAkB;AAAEC,QAAAA,IAAI,EAAE;AAAR,OAAlB;AACH;AACJ;AACD;;;AACAU,EAAAA,YAAY,CAACC,KAAD,EAAQ;AAChB,QAAI,KAAK9B,QAAT,EAAmB;AACf8B,MAAAA,KAAK,CAACC,cAAN;AACH;AACJ;AACD;;;AACAC,EAAAA,cAAc,CAACF,KAAD,EAAQ;AAClB,QAAI,KAAK9B,QAAT,EAAmB;AACf;AACH;;AACD,YAAQ8B,KAAK,CAACG,OAAd;AACI,WAAKtI,MAAL;AACA,WAAKD,SAAL;AACI;AACA,aAAKkI,MAAL,GAFJ,CAGI;;AACAE,QAAAA,KAAK,CAACC,cAAN;AACA;;AACJ,WAAKtI,KAAL;AACI;AACA,YAAI,KAAK+G,UAAT,EAAqB;AACjB,eAAKiB,cAAL,CAAoB,IAApB;AACH,SAJL,CAKI;;;AACAK,QAAAA,KAAK,CAACC,cAAN;AACA;AAfR;AAiBH;;AACDG,EAAAA,KAAK,GAAG;AACJ;AACA;AACA;AACA;AACA,SAAKnE,OAAL,CAAaoE,QAAb,CAAsBC,IAAtB,CAA2BtG,IAAI,CAAC,CAAD,CAA/B,EAAoCuG,SAApC,CAA8C,MAAM;AAChD,WAAKtE,OAAL,CAAauE,GAAb,CAAiB,MAAM;AACnB,aAAKhE,SAAL,GAAiB,KAAjB;;AACA,aAAKS,OAAL,CAAa4C,IAAb,CAAkB;AAAER,UAAAA,IAAI,EAAE;AAAR,SAAlB;AACH,OAHD;AAIH,KALD;AAMH;;AACDf,EAAAA,wBAAwB,CAACzD,WAAW,GAAG,KAAf,EAAsB;AAC1C,SAAKqC,eAAL,CAAqBkC,IAArB,CAA0B;AACtBzE,MAAAA,MAAM,EAAE,IADc;AAEtBE,MAAAA,WAFsB;AAGtBD,MAAAA,QAAQ,EAAE,KAAKgC;AAHO,KAA1B;AAKH;;AAzNmC;;AA2NxCb,OAAO,CAACV,IAAR;AAAA,mBAAoGU,OAApG,EAtPgG9D,EAsPhG,mBAA6HA,EAAE,CAACwI,UAAhI,GAtPgGxI,EAsPhG,mBAAuJA,EAAE,CAACyI,MAA1J,GAtPgGzI,EAsPhG,mBAA6K0B,EAAE,CAACgH,QAAhL,GAtPgG1I,EAsPhG,mBAAqMqB,yBAArM,MAtPgGrB,EAsPhG,mBAA2PA,EAAE,CAAC2I,iBAA9P,GAtPgG3I,EAsPhG,mBAA4R2B,QAA5R,GAtPgG3B,EAsPhG,mBAAiT4B,qBAAjT,MAtPgG5B,EAsPhG,mBAAmW,UAAnW;AAAA;;AACA8D,OAAO,CAACT,IAAR,kBAvPgGrD,EAuPhG;AAAA,QAAwF8D,OAAxF;AAAA;AAAA;AAAA;AAvPgG9D,MAAAA,EAuPhG,0BAA8nC8C,eAA9nC;AAvPgG9C,MAAAA,EAuPhG,0BAA4tC+C,sBAA5tC;AAvPgG/C,MAAAA,EAuPhG,0BAA+zC6C,eAA/zC;AAAA;;AAAA;AAAA;;AAvPgG7C,MAAAA,EAuPhG,qBAvPgGA,EAuPhG;AAvPgGA,MAAAA,EAuPhG,qBAvPgGA,EAuPhG;AAvPgGA,MAAAA,EAuPhG,qBAvPgGA,EAuPhG;AAAA;AAAA;AAAA,sBAAge,QAAhe;AAAA;AAAA;AAAA;AAvPgGA,MAAAA,EAuPhG;AAAA,eAAwF,wBAAxF;AAAA;AAAA,eAAwF,0BAAxF;AAAA;AAAA,eAAwF,WAAxF;AAAA;AAAA,eAAwF,WAAxF;AAAA;AAAA;;AAAA;AAvPgGA,MAAAA,EAuPhG;AAvPgGA,MAAAA,EAuPhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAvPgGA,EAuPhG;AAAA;;AACA;AAAA,qDAxPgGA,EAwPhG,mBAA2F8D,OAA3F,EAAgH,CAAC;AACrGN,IAAAA,IAAI,EAAEtD,SAD+F;AAErGuD,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAG,wDADZ;AAECkF,MAAAA,MAAM,EAAE,CAAC,OAAD,EAAU,eAAV,EAA2B,UAA3B,CAFT;AAGCC,MAAAA,QAAQ,EAAE,SAHX;AAIClF,MAAAA,IAAI,EAAE;AACF,iBAAS,8BADP;AAEF,2BAAmB,4BAFjB;AAGF,gBAAQ,QAHN;AAIF,qCAA6B,UAJ3B;AAKF,wCAAgC,QAL9B;AAMF,+CAAuC,4BANrC;AAOF,qCAA6B,UAP3B;AAQF,2CAAmC,qBARjC;AASF,2BAAmB,kBATjB;AAUF,gCAAwB,qBAVtB;AAWF,gCAAwB,cAXtB;AAYF,mBAAW,sBAZT;AAaF,qBAAa,wBAbX;AAcF,mBAAW,SAdT;AAeF,kBAAU;AAfR;AAJP,KAAD;AAF+F,GAAD,CAAhH,EAwB4B,YAAY;AAAE,WAAO,CAAC;AAAEH,MAAAA,IAAI,EAAExD,EAAE,CAACwI;AAAX,KAAD,EAA0B;AAAEhF,MAAAA,IAAI,EAAExD,EAAE,CAACyI;AAAX,KAA1B,EAA+C;AAAEjF,MAAAA,IAAI,EAAE9B,EAAE,CAACgH;AAAX,KAA/C,EAAsE;AAAElF,MAAAA,IAAI,EAAE+C,SAAR;AAAmBuC,MAAAA,UAAU,EAAE,CAAC;AACnItF,QAAAA,IAAI,EAAEpD;AAD6H,OAAD,EAEnI;AACCoD,QAAAA,IAAI,EAAEnD,MADP;AAECoD,QAAAA,IAAI,EAAE,CAACpC,yBAAD;AAFP,OAFmI;AAA/B,KAAtE,EAK3B;AAAEmC,MAAAA,IAAI,EAAExD,EAAE,CAAC2I;AAAX,KAL2B,EAKK;AAAEnF,MAAAA,IAAI,EAAE+C,SAAR;AAAmBuC,MAAAA,UAAU,EAAE,CAAC;AAClEtF,QAAAA,IAAI,EAAEnD,MAD4D;AAElEoD,QAAAA,IAAI,EAAE,CAAC9B,QAAD;AAF4D,OAAD;AAA/B,KALL,EAQ3B;AAAE6B,MAAAA,IAAI,EAAE+C,SAAR;AAAmBuC,MAAAA,UAAU,EAAE,CAAC;AAClCtF,QAAAA,IAAI,EAAEpD;AAD4B,OAAD,EAElC;AACCoD,QAAAA,IAAI,EAAEnD,MADP;AAECoD,QAAAA,IAAI,EAAE,CAAC7B,qBAAD;AAFP,OAFkC;AAA/B,KAR2B,EAa3B;AAAE4B,MAAAA,IAAI,EAAE+C,SAAR;AAAmBuC,MAAAA,UAAU,EAAE,CAAC;AAClCtF,QAAAA,IAAI,EAAElD,SAD4B;AAElCmD,QAAAA,IAAI,EAAE,CAAC,UAAD;AAF4B,OAAD;AAA/B,KAb2B,CAAP;AAgBlB,GAxCxB,EAwC0C;AAAEsF,IAAAA,MAAM,EAAE,CAAC;AACrCvF,MAAAA,IAAI,EAAEjD,YAD+B;AAErCkD,MAAAA,IAAI,EAAE,CAACX,eAAD;AAF+B,KAAD,CAAV;AAG1BkG,IAAAA,YAAY,EAAE,CAAC;AACfxF,MAAAA,IAAI,EAAEjD,YADS;AAEfkD,MAAAA,IAAI,EAAE,CAACV,sBAAD;AAFS,KAAD,CAHY;AAM1BkG,IAAAA,UAAU,EAAE,CAAC;AACbzF,MAAAA,IAAI,EAAEjD,YADO;AAEbkD,MAAAA,IAAI,EAAE,CAACZ,eAAD;AAFO,KAAD,CANc;AAS1BF,IAAAA,QAAQ,EAAE,CAAC;AACXa,MAAAA,IAAI,EAAEhD;AADK,KAAD,CATgB;AAW1B2F,IAAAA,KAAK,EAAE,CAAC;AACR3C,MAAAA,IAAI,EAAEhD;AADE,KAAD,CAXmB;AAa1BiG,IAAAA,UAAU,EAAE,CAAC;AACbjD,MAAAA,IAAI,EAAEhD;AADO,KAAD,CAbc;AAe1ByF,IAAAA,QAAQ,EAAE,CAAC;AACXzC,MAAAA,IAAI,EAAEhD;AADK,KAAD,CAfgB;AAiB1BkG,IAAAA,SAAS,EAAE,CAAC;AACZlD,MAAAA,IAAI,EAAEhD;AADM,KAAD,CAjBe;AAmB1ByE,IAAAA,eAAe,EAAE,CAAC;AAClBzB,MAAAA,IAAI,EAAE/C;AADY,KAAD,CAnBS;AAqB1ByE,IAAAA,SAAS,EAAE,CAAC;AACZ1B,MAAAA,IAAI,EAAE/C;AADM,KAAD,CArBe;AAuB1B0E,IAAAA,OAAO,EAAE,CAAC;AACV3B,MAAAA,IAAI,EAAE/C;AADI,KAAD;AAvBiB,GAxC1C;AAAA;AAkEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMyI,aAAN,CAAoB;AAChBzG,EAAAA,WAAW,CAAC0G,WAAD,EAAcpF,UAAd,EAA0B;AACjC,SAAKoF,WAAL,GAAmBA,WAAnB;;AACA,QAAIpF,UAAU,CAAC0B,aAAX,CAAyB2D,QAAzB,KAAsC,QAA1C,EAAoD;AAChDrF,MAAAA,UAAU,CAAC0B,aAAX,CAAyB4D,YAAzB,CAAsC,MAAtC,EAA8C,QAA9C;AACH;AACJ;AACD;;;AACAvB,EAAAA,YAAY,CAACC,KAAD,EAAQ;AAChB,UAAMuB,UAAU,GAAG,KAAKH,WAAxB;;AACA,QAAIG,UAAU,CAAC5C,SAAX,IAAwB,CAAC4C,UAAU,CAACrD,QAAxC,EAAkD;AAC9CqD,MAAAA,UAAU,CAACzB,MAAX;AACH,KAJe,CAKhB;AACA;AACA;AACA;AACA;;;AACAE,IAAAA,KAAK,CAACwB,eAAN;AACAxB,IAAAA,KAAK,CAACC,cAAN;AACH;;AApBe;;AAsBpBkB,aAAa,CAAC9F,IAAd;AAAA,mBAA0G8F,aAA1G,EA7VgGlJ,EA6VhG,mBAAyI8D,OAAzI,GA7VgG9D,EA6VhG,mBAA6JA,EAAE,CAACwI,UAAhK;AAAA;;AACAU,aAAa,CAAC7F,IAAd,kBA9VgGrD,EA8VhG;AAAA,QAA8FkJ,aAA9F;AAAA;AAAA;AAAA;AAAA;AA9VgGlJ,MAAAA,EA8VhG;AAAA,eAA8F,wBAA9F;AAAA;AAAA;AAAA;AAAA,aA9VgGA,EA8VhG,oBAAyQ,CAAC;AAAEsD,IAAAA,OAAO,EAAET,eAAX;AAA4BU,IAAAA,WAAW,EAAE2F;AAAzC,GAAD,CAAzQ;AAAA;;AACA;AAAA,qDA/VgGlJ,EA+VhG,mBAA2FkJ,aAA3F,EAAsH,CAAC;AAC3G1F,IAAAA,IAAI,EAAEtD,SADqG;AAE3GuD,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,iBADX;AAECC,MAAAA,IAAI,EAAE;AACF,iBAAS,wCADP;AAEF,mBAAW;AAFT,OAFP;AAMCC,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAET,eAAX;AAA4BU,QAAAA,WAAW,EAAE2F;AAAzC,OAAD;AANZ,KAAD;AAFqG,GAAD,CAAtH,EAU4B,YAAY;AAAE,WAAO,CAAC;AAAE1F,MAAAA,IAAI,EAAEM;AAAR,KAAD,EAAoB;AAAEN,MAAAA,IAAI,EAAExD,EAAE,CAACwI;AAAX,KAApB,CAAP;AAAsD,GAVhG;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMgB,yBAAyB,GAAG,IAAIvJ,cAAJ,CAAmB,2BAAnB,CAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAMwJ,gBAAgB,GAAGnI,eAAe,CAAC,MAAM;AAC3CmB,EAAAA,WAAW,CAACiH,yBAAD,EAA4BC,WAA5B,EAAyCC,gBAAzC;AACX;AACAC,EAAAA,SAFW,EAEA;AACP,SAAKH,yBAAL,GAAiCA,yBAAjC;AACA,SAAKC,WAAL,GAAmBA,WAAnB;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,SAAL,GAAiBA,SAAjB;AACH;;AAR0C,CAAP,CAAxC,C,CAUA;;;AACA,IAAIC,cAAc,GAAG,CAArB;AACA;;AACA,MAAMC,iBAAN,CAAwB;AACpBtH,EAAAA,WAAW;AACX;AACAC,EAAAA,MAFW;AAGX;AACAyD,EAAAA,KAJW,EAIJ;AACH,SAAKzD,MAAL,GAAcA,MAAd;AACA,SAAKyD,KAAL,GAAaA,KAAb;AACH;;AARmB;AAUxB;AACA;AACA;;;AACA,MAAM6D,WAAN,SAA0BP,gBAA1B,CAA2C;AACvChH,EAAAA,WAAW,CAACQ,WAAD,EAAckB,kBAAd,EAAkC8F,IAAlC,EAAwCN,WAAxC,EAAqDC,gBAArD,EAAuEF,yBAAvE,EAAkGG,SAAlG,EAA6G;AACpH,UAAMH,yBAAN,EAAiCC,WAAjC,EAA8CC,gBAA9C,EAAgEC,SAAhE;AACA,SAAK5G,WAAL,GAAmBA,WAAnB;AACA,SAAKkB,kBAAL,GAA0BA,kBAA1B;AACA,SAAK8F,IAAL,GAAYA,IAAZ;AACA;AACR;AACA;AACA;;AACQ,SAAKC,WAAL,GAAmB,eAAnB;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAKC,uBAAL,GAA+B,IAA/B;AACA;;AACA,SAAKC,UAAL,GAAkB,IAAIvI,OAAJ,EAAlB;AACA;;AACA,SAAKwI,IAAL,GAAa,iBAAgBP,cAAc,EAAG,EAA9C;AACA;;AACA,SAAKQ,SAAL,GAAiB,CAAjB;AACA;AACR;AACA;AACA;;AACQ,SAAKC,aAAL,GAAqB,IAArB;AACA;;AACA,SAAKC,UAAL,GAAkB,MAAM,CAAG,CAA3B;AACA;;;AACA,SAAKC,SAAL,GAAiB,MAAM,CAAG,CAA1B;;AACA,SAAKC,SAAL,GAAiB,KAAjB;;AACA,SAAKC,YAAL,GAAoB,CAACC,EAAD,EAAKC,EAAL,KAAYD,EAAE,KAAKC,EAAvC;;AACA,SAAKhG,SAAL,GAAiB,KAAjB;AACA;;AACA,SAAKiG,eAAL,GAAuB,YAAvB;AACA,SAAKlG,WAAL,GAAmB,IAAnB;AACA;;AACA,SAAKmG,MAAL,GAAc,IAAI5K,YAAJ,EAAd;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAK6K,WAAL,GAAmB,IAAI7K,YAAJ,EAAnB;;AACA,QAAI,KAAK0J,SAAT,EAAoB;AAChB,WAAKA,SAAL,CAAeoB,aAAf,GAA+B,IAA/B;AACH;AACJ;AACD;;;AACY,MAARtI,QAAQ,GAAG;AACX,WAAO,KAAKuI,QAAL,GAAgB,KAAKC,eAAL,EAAsBxI,QAAtB,IAAkC,EAAlD,GAAuD,KAAKwI,eAAL,EAAsBxI,QAAtB,CAA+B,CAA/B,CAA9D;AACH;AACD;;;AACQ,MAAJyI,IAAI,GAAG;AACP,WAAO,KAAKC,KAAL,GAAa,IAAb,GAAoB,SAA3B;AACH;AACD;;;AACY,MAARH,QAAQ,GAAG;AACX,WAAO,KAAKR,SAAZ;AACH;;AACW,MAARQ,QAAQ,CAAC/E,KAAD,EAAQ;AAChB,SAAKuE,SAAL,GAAiBjJ,qBAAqB,CAAC0E,KAAD,CAAtC;;AACA,SAAKmF,eAAL;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACmB,MAAXC,WAAW,GAAG;AACd,WAAO,KAAKZ,YAAZ;AACH;;AACc,MAAXY,WAAW,CAACC,EAAD,EAAK;AAChB,SAAKb,YAAL,GAAoBa,EAApB;;AACA,QAAI,KAAKL,eAAT,EAA0B;AACtB;AACA,WAAKM,oBAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACa,MAALtF,KAAK,GAAG;AACR,WAAO,KAAKG,MAAZ;AACH;;AACQ,MAALH,KAAK,CAACA,KAAD,EAAQ;AACb,SAAKuF,UAAL,CAAgBvF,KAAhB;AACA,SAAKG,MAAL,GAAcH,KAAd;AACH;AACD;AACJ;AACA;AACA;;;AACU,MAAFwF,EAAE,GAAG;AACL,WAAO,KAAKC,UAAL,GAAkB,KAAKA,UAAL,CAAgBD,EAAlC,GAAuC,KAAKtB,IAAnD;AACH;AACD;AACJ;AACA;AACA;;;AACgB,MAARwB,QAAQ,GAAG;AACX,WAAO,KAAKC,SAAL,IAAkB,KAAKjC,SAAL,EAAgBkC,OAAhB,EAAyBC,YAAzB,CAAsC1J,UAAU,CAACuJ,QAAjD,CAAlB,IAAgF,KAAvF;AACH;;AACW,MAARA,QAAQ,CAAC1F,KAAD,EAAQ;AAChB,SAAK2F,SAAL,GAAiBrK,qBAAqB,CAAC0E,KAAD,CAAtC;AACA,SAAK8F,YAAL,CAAkBrE,IAAlB;AACH;AACD;AACJ;AACA;AACA;;;AACmB,MAAXsE,WAAW,GAAG;AACd,WAAO,KAAKN,UAAL,GAAkB,KAAKA,UAAL,CAAgBM,WAAlC,GAAgD,KAAKC,YAA5D;AACH;;AACc,MAAXD,WAAW,CAAC/F,KAAD,EAAQ;AACnB,SAAKgG,YAAL,GAAoBhG,KAApB;AACA,SAAK8F,YAAL,CAAkBrE,IAAlB;AACH;AACD;;;AACW,MAAPwE,OAAO,GAAG;AACV,WAAQ,KAAKR,UAAL,IAAmB,KAAKA,UAAL,CAAgBQ,OAApC,IAAgD,KAAKC,eAAL,EAAvD;AACH;AACD;AACJ;AACA;AACA;;;AACa,MAALhB,KAAK,GAAG;AACR,WAAO,CAAC,CAAC,KAAKO,UAAN,IAAoB,KAAKA,UAAL,CAAgBP,KAArC,MAAgD,CAAC,KAAKiB,KAAN,IAAe,KAAKA,KAAL,CAAWC,MAAX,KAAsB,CAArF,CAAP;AACH;AACD;AACJ;AACA;AACA;;;AACwB,MAAhBC,gBAAgB,GAAG;AACnB,WAAO,CAAC,KAAKnB,KAAN,IAAe,KAAKe,OAA3B;AACH;AACD;AACJ;AACA;AACA;;;AACgB,MAARnG,QAAQ,GAAG;AACX,WAAO,KAAK4D,SAAL,GAAiB,CAAC,CAAC,KAAKA,SAAL,CAAe5D,QAAlC,GAA6C,KAAKpB,SAAzD;AACH;;AACW,MAARoB,QAAQ,CAACE,KAAD,EAAQ;AAChB,SAAKtB,SAAL,GAAiBpD,qBAAqB,CAAC0E,KAAD,CAAtC;;AACA,SAAKmF,eAAL;AACH;AACD;AACJ;AACA;AACA;;;AACkB,MAAV7E,UAAU,GAAG;AACb,WAAO,KAAK7B,WAAZ;AACH;;AACa,MAAV6B,UAAU,CAACN,KAAD,EAAQ;AAClB,SAAKvB,WAAL,GAAmBnD,qBAAqB,CAAC0E,KAAD,CAAxC;;AACA,QAAI,KAAKmG,KAAT,EAAgB;AACZ,WAAKA,KAAL,CAAWG,OAAX,CAAmBrF,IAAI,IAAKA,IAAI,CAAC5C,kBAAL,GAA0B,KAAKI,WAA3D;AACH;AACJ;;AACW,MAARN,QAAQ,CAAC6B,KAAD,EAAQ;AAChB,SAAKoE,aAAL,GAAqBpE,KAArB;AACA,SAAKmE,SAAL,GAAiBnE,KAAjB;AACH;AACD;;;AACwB,MAApBuG,oBAAoB,GAAG;AACvB,WAAO5K,KAAK,CAAC,GAAG,KAAKwK,KAAL,CAAWK,GAAX,CAAevF,IAAI,IAAIA,IAAI,CAACnC,eAA5B,CAAJ,CAAZ;AACH;AACD;;;AACoB,MAAhB2H,gBAAgB,GAAG;AACnB,WAAO9K,KAAK,CAAC,GAAG,KAAKwK,KAAL,CAAWK,GAAX,CAAevF,IAAI,IAAIA,IAAI,CAACrC,QAA5B,CAAJ,CAAZ;AACH;AACD;;;AACmB,MAAf8H,eAAe,GAAG;AAClB,WAAO/K,KAAK,CAAC,GAAG,KAAKwK,KAAL,CAAWK,GAAX,CAAevF,IAAI,IAAIA,IAAI,CAACpC,OAA5B,CAAJ,CAAZ;AACH;AACD;;;AACqB,MAAjB8H,iBAAiB,GAAG;AACpB,WAAOhL,KAAK,CAAC,GAAG,KAAKwK,KAAL,CAAWK,GAAX,CAAevF,IAAI,IAAIA,IAAI,CAAClC,SAA5B,CAAJ,CAAZ;AACH;;AACD6H,EAAAA,kBAAkB,GAAG;AACjB,SAAKC,WAAL,GAAmB,IAAI9K,eAAJ,CAAoB,KAAKoK,KAAzB,EACdW,QADc,GAEdC,uBAFc,GAGdC,cAHc,GAIdC,yBAJc,CAIY,KAAKnD,IAAL,GAAY,KAAKA,IAAL,CAAU9D,KAAtB,GAA8B,KAJ1C,CAAnB;;AAKA,QAAI,KAAK8D,IAAT,EAAe;AACX,WAAKA,IAAL,CAAUc,MAAV,CACK1C,IADL,CACUrG,SAAS,CAAC,KAAKoI,UAAN,CADnB,EAEK9B,SAFL,CAEe+E,GAAG,IAAI,KAAKL,WAAL,CAAiBI,yBAAjB,CAA2CC,GAA3C,CAFtB;AAGH;;AACD,SAAKL,WAAL,CAAiBM,MAAjB,CAAwBjF,IAAxB,CAA6BrG,SAAS,CAAC,KAAKoI,UAAN,CAAtC,EAAyD9B,SAAzD,CAAmE,MAAM;AACrE,WAAKiF,iBAAL;AACH,KAFD,EAXiB,CAcjB;;;AACA,SAAKjB,KAAL,CAAWkB,OAAX,CAAmBnF,IAAnB,CAAwBpG,SAAS,CAAC,IAAD,CAAjC,EAAyCD,SAAS,CAAC,KAAKoI,UAAN,CAAlD,EAAqE9B,SAArE,CAA+E,MAAM;AACjF,UAAI,KAAKrC,QAAT,EAAmB;AACf;AACA;AACAwH,QAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AACzB,eAAKrC,eAAL;AACH,SAFD;AAGH;;AACD,WAAKsC,WAAL,GARiF,CASjF;;;AACA,WAAKnC,oBAAL,GAViF,CAWjF;;;AACA,WAAKoC,eAAL,GAZiF,CAajF;;;AACA,WAAKC,6BAAL;;AACA,WAAK7B,YAAL,CAAkBrE,IAAlB;AACH,KAhBD;AAiBH;;AACDmG,EAAAA,QAAQ,GAAG;AACP,SAAK5C,eAAL,GAAuB,IAAI/I,cAAJ,CAAmB,KAAK8I,QAAxB,EAAkC3E,SAAlC,EAA6C,KAA7C,CAAvB;AACA,SAAK0F,YAAL,CAAkBrE,IAAlB;AACH;;AACDoG,EAAAA,SAAS,GAAG;AACR,QAAI,KAAKnE,SAAT,EAAoB;AAChB;AACA;AACA;AACA,WAAKoE,gBAAL;;AACA,UAAI,KAAKpE,SAAL,CAAe5D,QAAf,KAA4B,KAAKpB,SAArC,EAAgD;AAC5C,aAAKoB,QAAL,GAAgB,CAAC,CAAC,KAAK4D,SAAL,CAAe5D,QAAjC;AACH;AACJ;AACJ;;AACDiB,EAAAA,WAAW,GAAG;AACV,SAAKkD,UAAL,CAAgBxC,IAAhB;;AACA,SAAKwC,UAAL,CAAgB8D,QAAhB;;AACA,SAAKjC,YAAL,CAAkBiC,QAAlB;;AACA,SAAKC,kBAAL;AACH;AACD;;;AACAC,EAAAA,aAAa,CAACC,YAAD,EAAe;AACxB,SAAKzC,UAAL,GAAkByC,YAAlB,CADwB,CAExB;AACA;;AACA,SAAKpL,WAAL,CAAiBwC,aAAjB,CAA+B4D,YAA/B,CAA4C,qBAA5C,EAAmEgF,YAAY,CAAC1C,EAAhF;AACH;AACD;AACJ;AACA;AACA;;;AACI2C,EAAAA,iBAAiB,CAACC,GAAD,EAAM;AACnB,SAAKC,gBAAL,GAAwBD,GAAG,CAACE,IAAJ,CAAS,GAAT,CAAxB;AACH,GA1PsC,CA2PvC;;;AACA/C,EAAAA,UAAU,CAACvF,KAAD,EAAQ;AACd,QAAI,KAAKmG,KAAT,EAAgB;AACZ,WAAKoC,oBAAL,CAA0BvI,KAA1B,EAAiC,KAAjC;AACH;AACJ,GAhQsC,CAiQvC;;;AACAwI,EAAAA,gBAAgB,CAACnD,EAAD,EAAK;AACjB,SAAKf,SAAL,GAAiBe,EAAjB;AACH,GApQsC,CAqQvC;;;AACAoD,EAAAA,iBAAiB,CAACpD,EAAD,EAAK;AAClB,SAAKhB,UAAL,GAAkBgB,EAAlB;AACH,GAxQsC,CAyQvC;;;AACAqD,EAAAA,gBAAgB,CAACC,UAAD,EAAa;AACzB,SAAK7I,QAAL,GAAgB6I,UAAhB;AACA,SAAK7C,YAAL,CAAkBrE,IAAlB;AACH;AACD;AACJ;AACA;AACA;;;AACImH,EAAAA,gBAAgB,CAAChH,KAAD,EAAQ;AACpB,QAAI,CAAC,KAAKiH,mBAAL,CAAyBjH,KAAzB,CAAL,EAAsC;AAClC,WAAKJ,KAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIA,EAAAA,KAAK,CAACsH,OAAD,EAAU;AACX,QAAI,KAAKhJ,QAAT,EAAmB;AACf;AACH,KAHU,CAIX;AACA;;;AACA,QAAI,KAAK2F,UAAL,IAAmB,KAAKA,UAAL,CAAgBQ,OAAvC,EAAgD,CAC5C;AACH,KAFD,MAGK,IAAI,KAAKE,KAAL,CAAWC,MAAX,GAAoB,CAAxB,EAA2B;AAC5B,WAAKS,WAAL,CAAiBkC,kBAAjB;;AACA,WAAKjD,YAAL,CAAkBrE,IAAlB;AACH,KAHI,MAIA;AACD,WAAKuH,WAAL,CAAiBF,OAAjB;;AACA,WAAKhD,YAAL,CAAkBrE,IAAlB;AACH;AACJ;AACD;;;AACAuH,EAAAA,WAAW,CAACF,OAAD,EAAU;AACjB,QAAI,KAAKrD,UAAT,EAAqB;AACjB,WAAKA,UAAL,CAAgBjE,KAAhB,CAAsBsH,OAAtB;AACH;AACJ;AACD;AACJ;AACA;;;AACIG,EAAAA,QAAQ,CAACrH,KAAD,EAAQ;AACZ,UAAMsH,MAAM,GAAGtH,KAAK,CAACsH,MAArB;;AACA,QAAIA,MAAM,IAAIA,MAAM,CAAC9J,SAAP,CAAiB+J,QAAjB,CAA0B,UAA1B,CAAd,EAAqD;AACjD,WAAKtC,WAAL,CAAiBuC,SAAjB,CAA2BxH,KAA3B;;AACA,WAAKkE,YAAL,CAAkBrE,IAAlB;AACH;AACJ;AACD;AACJ;AACA;;;AACIiG,EAAAA,eAAe,GAAG;AACd;AACA,SAAKvD,SAAL,GAAiB,KAAKC,aAAL,KAAuB,KAAK+B,KAAL,CAAWC,MAAX,KAAsB,CAAtB,GAA0B,CAAC,CAA3B,GAA+B,CAAtD,CAAjB;AACH;AACD;AACJ;AACA;AACA;;;AACIuB,EAAAA,6BAA6B,GAAG;AAC5B;AACA,QAAI,KAAK3D,uBAAL,IAAgC,IAApC,EAA0C;AACtC,UAAI,KAAKmC,KAAL,CAAWC,MAAf,EAAuB;AACnB,cAAMiD,YAAY,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKvF,uBAAd,EAAuC,KAAKmC,KAAL,CAAWC,MAAX,GAAoB,CAA3D,CAArB;;AACA,aAAKS,WAAL,CAAiB2C,aAAjB,CAA+BH,YAA/B;AACH,OAHD,MAIK;AACD,aAAK7H,KAAL;AACH;AACJ;;AACD,SAAKwC,uBAAL,GAA+B,IAA/B;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIyF,EAAAA,aAAa,CAACC,KAAD,EAAQ;AACjB,WAAOA,KAAK,IAAI,CAAT,IAAcA,KAAK,GAAG,KAAKvD,KAAL,CAAWC,MAAxC;AACH;;AACDmC,EAAAA,oBAAoB,CAACvI,KAAD,EAAQvD,WAAW,GAAG,IAAtB,EAA4B;AAC5C,SAAKkN,eAAL;;AACA,SAAKxD,KAAL,CAAWG,OAAX,CAAmBrF,IAAI,IAAIA,IAAI,CAACI,QAAL,EAA3B;;AACA,QAAIuI,KAAK,CAACC,OAAN,CAAc7J,KAAd,CAAJ,EAA0B;AACtBA,MAAAA,KAAK,CAACsG,OAAN,CAAcwD,YAAY,IAAI,KAAKC,YAAL,CAAkBD,YAAlB,EAAgCrN,WAAhC,CAA9B;;AACA,WAAKuN,WAAL;AACH,KAHD,MAIK;AACD,YAAMC,iBAAiB,GAAG,KAAKF,YAAL,CAAkB/J,KAAlB,EAAyBvD,WAAzB,CAA1B,CADC,CAED;AACA;;;AACA,UAAIwN,iBAAJ,EAAuB;AACnB,YAAIxN,WAAJ,EAAiB;AACb,eAAKoK,WAAL,CAAiB2C,aAAjB,CAA+BS,iBAA/B;AACH;AACJ;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACIF,EAAAA,YAAY,CAAC/J,KAAD,EAAQvD,WAAW,GAAG,IAAtB,EAA4B;AACpC,UAAMwN,iBAAiB,GAAG,KAAK9D,KAAL,CAAW+D,IAAX,CAAgBjJ,IAAI,IAAI;AAC9C,aAAOA,IAAI,CAACjB,KAAL,IAAc,IAAd,IAAsB,KAAKwE,YAAL,CAAkBvD,IAAI,CAACjB,KAAvB,EAA8BA,KAA9B,CAA7B;AACH,KAFyB,CAA1B;;AAGA,QAAIiK,iBAAJ,EAAuB;AACnBxN,MAAAA,WAAW,GAAGwN,iBAAiB,CAAC3I,oBAAlB,EAAH,GAA8C2I,iBAAiB,CAAC9I,MAAlB,EAAzD;;AACA,WAAK6D,eAAL,CAAqB7D,MAArB,CAA4B8I,iBAA5B;AACH;;AACD,WAAOA,iBAAP;AACH;;AACD3E,EAAAA,oBAAoB,GAAG;AACnB;AACA;AACAgC,IAAAA,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;AACzB,UAAI,KAAK9D,SAAL,IAAkB,KAAKvD,MAA3B,EAAmC;AAC/B,aAAKoI,oBAAL,CAA0B,KAAK7E,SAAL,GAAiB,KAAKA,SAAL,CAAe1D,KAAhC,GAAwC,KAAKG,MAAvE,EAA+E,KAA/E;;AACA,aAAK2F,YAAL,CAAkBrE,IAAlB;AACH;AACJ,KALD;AAMH;AACD;AACJ;AACA;AACA;;;AACIkI,EAAAA,eAAe,CAACQ,IAAD,EAAO;AAClB,SAAKnF,eAAL,CAAqBoF,KAArB;;AACA,SAAKjE,KAAL,CAAWG,OAAX,CAAmBrF,IAAI,IAAI;AACvB,UAAIA,IAAI,KAAKkJ,IAAb,EAAmB;AACflJ,QAAAA,IAAI,CAACI,QAAL;AACH;AACJ,KAJD;AAKA,SAAKyE,YAAL,CAAkBrE,IAAlB;AACH;AACD;AACJ;AACA;AACA;;;AACIuI,EAAAA,WAAW,GAAG;AACV,QAAI,KAAKzF,SAAT,EAAoB;AAChB,WAAKS,eAAL,CAAqBoF,KAArB;;AACA,WAAKjE,KAAL,CAAWG,OAAX,CAAmBrF,IAAI,IAAI;AACvB,YAAIA,IAAI,CAACzE,QAAT,EAAmB;AACf,eAAKwI,eAAL,CAAqB7D,MAArB,CAA4BF,IAA5B;AACH;AACJ,OAJD;AAKA,WAAK6E,YAAL,CAAkBrE,IAAlB;AACH;AACJ;AACD;;;AACA4I,EAAAA,iBAAiB,CAACC,aAAD,EAAgB;AAC7B,QAAIC,WAAW,GAAG,IAAlB;;AACA,QAAIX,KAAK,CAACC,OAAN,CAAc,KAAKrN,QAAnB,CAAJ,EAAkC;AAC9B+N,MAAAA,WAAW,GAAG,KAAK/N,QAAL,CAAcgK,GAAd,CAAkBvF,IAAI,IAAIA,IAAI,CAACjB,KAA/B,CAAd;AACH,KAFD,MAGK;AACDuK,MAAAA,WAAW,GAAG,KAAK/N,QAAL,GAAgB,KAAKA,QAAL,CAAcwD,KAA9B,GAAsCsK,aAApD;AACH;;AACD,SAAKnK,MAAL,GAAcoK,WAAd;AACA,SAAK3F,MAAL,CAAY5D,IAAZ,CAAiB,IAAI4C,iBAAJ,CAAsB,IAAtB,EAA4B2G,WAA5B,CAAjB;AACA,SAAK1F,WAAL,CAAiB7D,IAAjB,CAAsBuJ,WAAtB;;AACA,SAAKjG,SAAL,CAAeiG,WAAf;;AACA,SAAKvM,kBAAL,CAAwBoD,YAAxB;AACH;AACD;;;AACAY,EAAAA,KAAK,GAAG;AACJ,QAAI,CAAC,KAAKkE,eAAL,EAAL,EAA6B;AACzB,WAAKW,WAAL,CAAiB2C,aAAjB,CAA+B,CAAC,CAAhC;AACH;;AACD,QAAI,CAAC,KAAK1J,QAAV,EAAoB;AAChB,UAAI,KAAK2F,UAAT,EAAqB;AACjB;AACA;AACA;AACA;AACA+E,QAAAA,UAAU,CAAC,MAAM;AACb,cAAI,CAAC,KAAKvE,OAAV,EAAmB;AACf,iBAAKwE,cAAL;AACH;AACJ,SAJS,CAAV;AAKH,OAVD,MAWK;AACD;AACA,aAAKA,cAAL;AACH;AACJ;AACJ;AACD;;;AACAA,EAAAA,cAAc,GAAG;AACb,SAAKpG,UAAL;;AACA,SAAKrG,kBAAL,CAAwBoD,YAAxB;;AACA,SAAK0E,YAAL,CAAkBrE,IAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACI2F,EAAAA,iBAAiB,GAAG;AAChB,QAAI,KAAKjD,SAAL,KAAmB,CAAC,CAAxB,EAA2B;AACvB,WAAKA,SAAL,GAAiB,CAAC,CAAlB;AACAqG,MAAAA,UAAU,CAAC,MAAM;AACb,aAAKrG,SAAL,GAAiB,KAAKC,aAAL,IAAsB,CAAvC;;AACA,aAAKpG,kBAAL,CAAwBoD,YAAxB;AACH,OAHS,CAAV;AAIH;AACJ;;AACDqG,EAAAA,WAAW,GAAG;AACV,SAAKO,kBAAL;;AACA,SAAK0C,mBAAL;;AACA,SAAKC,uBAAL;;AACA,SAAKC,qBAAL;AACH;;AACD5C,EAAAA,kBAAkB,GAAG;AACjB,QAAI,KAAK6C,sBAAT,EAAiC;AAC7B,WAAKA,sBAAL,CAA4BC,WAA5B;;AACA,WAAKD,sBAAL,GAA8B,IAA9B;AACH;;AACD,QAAI,KAAKE,qBAAT,EAAgC;AAC5B,WAAKA,qBAAL,CAA2BD,WAA3B;;AACA,WAAKC,qBAAL,GAA6B,IAA7B;AACH;;AACD,QAAI,KAAKC,0BAAT,EAAqC;AACjC,WAAKA,0BAAL,CAAgCF,WAAhC;;AACA,WAAKE,0BAAL,GAAkC,IAAlC;AACH;;AACD,QAAI,KAAKC,uBAAT,EAAkC;AAC9B,WAAKA,uBAAL,CAA6BH,WAA7B;;AACA,WAAKG,uBAAL,GAA+B,IAA/B;AACH;AACJ;AACD;;;AACAN,EAAAA,uBAAuB,GAAG;AACtB,SAAKK,0BAAL,GAAkC,KAAKzE,oBAAL,CAA0BpE,SAA1B,CAAoCP,KAAK,IAAI;AAC3EA,MAAAA,KAAK,CAACrF,MAAN,CAAaC,QAAb,GACM,KAAKwI,eAAL,CAAqB7D,MAArB,CAA4BS,KAAK,CAACrF,MAAlC,CADN,GAEM,KAAKyI,eAAL,CAAqB3D,QAArB,CAA8BO,KAAK,CAACrF,MAApC,CAFN,CAD2E,CAI3E;;AACA,UAAI,CAAC,KAAKwI,QAAV,EAAoB;AAChB,aAAKoB,KAAL,CAAWG,OAAX,CAAmBrF,IAAI,IAAI;AACvB,cAAI,CAAC,KAAK+D,eAAL,CAAqBkG,UAArB,CAAgCjK,IAAhC,CAAD,IAA0CA,IAAI,CAACzE,QAAnD,EAA6D;AACzDyE,YAAAA,IAAI,CAACI,QAAL;AACH;AACJ,SAJD;AAKH;;AACD,UAAIO,KAAK,CAACnF,WAAV,EAAuB;AACnB,aAAK4N,iBAAL;AACH;AACJ,KAfiC,CAAlC;AAgBH;AACD;;;AACAK,EAAAA,mBAAmB,GAAG;AAClB,SAAKG,sBAAL,GAA8B,KAAKpE,gBAAL,CAAsBtE,SAAtB,CAAgCP,KAAK,IAAI;AACnE,UAAIuJ,SAAS,GAAG,KAAKhF,KAAL,CAAWiF,OAAX,GAAqBC,OAArB,CAA6BzJ,KAAK,CAACX,IAAnC,CAAhB;;AACA,UAAI,KAAKwI,aAAL,CAAmB0B,SAAnB,CAAJ,EAAmC;AAC/B,aAAKtE,WAAL,CAAiByE,gBAAjB,CAAkCH,SAAlC;AACH;;AACD,WAAKrF,YAAL,CAAkBrE,IAAlB;AACH,KAN6B,CAA9B;AAOA,SAAKsJ,qBAAL,GAA6B,KAAKrE,eAAL,CAAqBvE,SAArB,CAA+B,MAAM;AAC9D,WAAKH,KAAL;;AACA,WAAK8D,YAAL,CAAkBrE,IAAlB;AACH,KAH4B,CAA7B;AAIH;;AACDmJ,EAAAA,qBAAqB,GAAG;AACpB,SAAKK,uBAAL,GAA+B,KAAKtE,iBAAL,CAAuBxE,SAAvB,CAAiCP,KAAK,IAAI;AACrE,YAAMX,IAAI,GAAGW,KAAK,CAACX,IAAnB;AACA,YAAMkK,SAAS,GAAG,KAAKhF,KAAL,CAAWiF,OAAX,GAAqBC,OAArB,CAA6BzJ,KAAK,CAACX,IAAnC,CAAlB,CAFqE,CAGrE;AACA;AACA;;AACA,UAAI,KAAKwI,aAAL,CAAmB0B,SAAnB,KAAiClK,IAAI,CAAC7C,SAA1C,EAAqD;AACjD,aAAK4F,uBAAL,GAA+BmH,SAA/B;AACH;AACJ,KAT8B,CAA/B;AAUH;AACD;;;AACAtC,EAAAA,mBAAmB,CAACjH,KAAD,EAAQ;AACvB,QAAI2J,cAAc,GAAG3J,KAAK,CAACsH,MAA3B;;AACA,WAAOqC,cAAc,IAAIA,cAAc,KAAK,KAAKzO,WAAL,CAAiBwC,aAA7D,EAA4E;AACxE,UAAIiM,cAAc,CAACnM,SAAf,CAAyB+J,QAAzB,CAAkC,UAAlC,CAAJ,EAAmD;AAC/C,eAAO,IAAP;AACH;;AACDoC,MAAAA,cAAc,GAAGA,cAAc,CAACC,aAAhC;AACH;;AACD,WAAO,KAAP;AACH;AACD;;;AACAtF,EAAAA,eAAe,GAAG;AACd,WAAO,KAAKC,KAAL,IAAc,KAAKA,KAAL,CAAWsF,IAAX,CAAgBxK,IAAI,IAAIA,IAAI,CAAC7C,SAA7B,CAArB;AACH;AACD;;;AACA+G,EAAAA,eAAe,GAAG;AACd,QAAI,KAAKgB,KAAT,EAAgB;AACZ,WAAKA,KAAL,CAAWG,OAAX,CAAmBrF,IAAI,IAAI;AACvBA,QAAAA,IAAI,CAAC1C,iBAAL,GAAyB,KAAKG,SAA9B;AACAuC,QAAAA,IAAI,CAAC3C,iBAAL,GAAyB,KAAKyG,QAA9B;AACH,OAHD;AAIH;AACJ;;AA1jBsC;;AA4jB3ClB,WAAW,CAAC5G,IAAZ;AAAA,mBAAwG4G,WAAxG,EAp9BgGhK,EAo9BhG,mBAAqIA,EAAE,CAACwI,UAAxI,GAp9BgGxI,EAo9BhG,mBAA+JA,EAAE,CAAC2I,iBAAlK,GAp9BgG3I,EAo9BhG,mBAAgMmC,IAAI,CAAC0P,cAArM,MAp9BgG7R,EAo9BhG,mBAAgPqC,EAAE,CAACyP,MAAnP,MAp9BgG9R,EAo9BhG,mBAAsRqC,EAAE,CAAC0P,kBAAzR,MAp9BgG/R,EAo9BhG,mBAAwUgB,EAAE,CAACQ,iBAA3U,GAp9BgGxB,EAo9BhG,mBAAyWqC,EAAE,CAAC2P,SAA5W;AAAA;;AACAhI,WAAW,CAACiI,IAAZ,kBAr9BgGjS,EAq9BhG;AAAA,QAA4FgK,WAA5F;AAAA;AAAA;AAAA;AAr9BgGhK,MAAAA,EAq9BhG,0BAAgtC8D,OAAhtC;AAAA;;AAAA;AAAA;;AAr9BgG9D,MAAAA,EAq9BhG,qBAr9BgGA,EAq9BhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAr9BgGA,MAAAA,EAq9BhG;AAAA,eAA4F,WAA5F;AAAA;AAAA,eAA4F,WAA5F;AAAA;AAAA,eAA4F,oBAA5F;AAAA;AAAA;;AAAA;AAr9BgGA,MAAAA,EAq9BhG;AAr9BgGA,MAAAA,EAq9BhG;AAr9BgGA,MAAAA,EAq9BhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAr9BgGA,EAq9BhG,oBAAomC,CAAC;AAAEsD,IAAAA,OAAO,EAAEf,mBAAX;AAAgCgB,IAAAA,WAAW,EAAEyG;AAA7C,GAAD,CAApmC,GAr9BgGhK,EAq9BhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAr9BgGA,MAAAA,EAq9BhG;AAr9BgGA,MAAAA,EAq9B0tC,4BAA1zC;AAr9BgGA,MAAAA,EAq9B6vC,gBAA71C;AAr9BgGA,MAAAA,EAq9BsxC,eAAt3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDAt9BgGA,EAs9BhG,mBAA2FgK,WAA3F,EAAoH,CAAC;AACzGxG,IAAAA,IAAI,EAAE9C,SADmG;AAEzG+C,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,QAAQ,EAAE,eAAZ;AAA6BwO,MAAAA,QAAQ,EAAG,oEAAxC;AAA6GrJ,MAAAA,QAAQ,EAAE,aAAvH;AAAsIlF,MAAAA,IAAI,EAAE;AACvI,2BAAmB,6BADoH;AAEvI,mCAA2B,0BAF4G;AAGvI,gCAAwB,wBAH+G;AAIvI,gCAAwB,qBAJ+G;AAKvI,+BAAuB,YALgH;AAMvI,uCAA+B,UANwG;AAOvI,uBAAe,MAPwH;AAQvI,0CAAkC,UARqG;AASvI,yCAAiC,YATsG;AAUvI,0CAAkC,UAVqG;AAWvI,mCAA2B,iBAX4G;AAYvI,iBAAS,eAZ8H;AAavI,mBAAW,SAb4H;AAcvI,kBAAU,SAd6H;AAevI,qBAAa,kBAf0H;AAgBvI,gBAAQ;AAhB+H,OAA5I;AAiBIC,MAAAA,SAAS,EAAE,CAAC;AAAEN,QAAAA,OAAO,EAAEf,mBAAX;AAAgCgB,QAAAA,WAAW,EAAEyG;AAA7C,OAAD,CAjBf;AAiB6EmI,MAAAA,aAAa,EAAExR,iBAAiB,CAACyR,IAjB9G;AAiBoHC,MAAAA,eAAe,EAAEzR,uBAAuB,CAAC0R,MAjB7J;AAiBqKC,MAAAA,MAAM,EAAE,CAAC,kwGAAD;AAjB7K,KAAD;AAFmG,GAAD,CAApH,EAoB4B,YAAY;AAAE,WAAO,CAAC;AAAE/O,MAAAA,IAAI,EAAExD,EAAE,CAACwI;AAAX,KAAD,EAA0B;AAAEhF,MAAAA,IAAI,EAAExD,EAAE,CAAC2I;AAAX,KAA1B,EAA0D;AAAEnF,MAAAA,IAAI,EAAErB,IAAI,CAAC0P,cAAb;AAA6B/I,MAAAA,UAAU,EAAE,CAAC;AACjItF,QAAAA,IAAI,EAAEpD;AAD2H,OAAD;AAAzC,KAA1D,EAE3B;AAAEoD,MAAAA,IAAI,EAAEnB,EAAE,CAACyP,MAAX;AAAmBhJ,MAAAA,UAAU,EAAE,CAAC;AAClCtF,QAAAA,IAAI,EAAEpD;AAD4B,OAAD;AAA/B,KAF2B,EAI3B;AAAEoD,MAAAA,IAAI,EAAEnB,EAAE,CAAC0P,kBAAX;AAA+BjJ,MAAAA,UAAU,EAAE,CAAC;AAC9CtF,QAAAA,IAAI,EAAEpD;AADwC,OAAD;AAA3C,KAJ2B,EAM3B;AAAEoD,MAAAA,IAAI,EAAExC,EAAE,CAACQ;AAAX,KAN2B,EAMK;AAAEgC,MAAAA,IAAI,EAAEnB,EAAE,CAAC2P,SAAX;AAAsBlJ,MAAAA,UAAU,EAAE,CAAC;AACrEtF,QAAAA,IAAI,EAAEpD;AAD+D,OAAD,EAErE;AACCoD,QAAAA,IAAI,EAAE3C;AADP,OAFqE;AAAlC,KANL,CAAP;AAUlB,GA9BxB,EA8B0C;AAAE2R,IAAAA,iBAAiB,EAAE,CAAC;AAChDhP,MAAAA,IAAI,EAAEhD;AAD0C,KAAD,CAArB;AAE1B0K,IAAAA,QAAQ,EAAE,CAAC;AACX1H,MAAAA,IAAI,EAAEhD;AADK,KAAD,CAFgB;AAI1B+K,IAAAA,WAAW,EAAE,CAAC;AACd/H,MAAAA,IAAI,EAAEhD;AADQ,KAAD,CAJa;AAM1B2F,IAAAA,KAAK,EAAE,CAAC;AACR3C,MAAAA,IAAI,EAAEhD;AADE,KAAD,CANmB;AAQ1BqL,IAAAA,QAAQ,EAAE,CAAC;AACXrI,MAAAA,IAAI,EAAEhD;AADK,KAAD,CARgB;AAU1B0L,IAAAA,WAAW,EAAE,CAAC;AACd1I,MAAAA,IAAI,EAAEhD;AADQ,KAAD,CAVa;AAY1ByF,IAAAA,QAAQ,EAAE,CAAC;AACXzC,MAAAA,IAAI,EAAEhD;AADK,KAAD,CAZgB;AAc1BsK,IAAAA,eAAe,EAAE,CAAC;AAClBtH,MAAAA,IAAI,EAAEhD,KADY;AAElBiD,MAAAA,IAAI,EAAE,CAAC,kBAAD;AAFY,KAAD,CAdS;AAiB1BgD,IAAAA,UAAU,EAAE,CAAC;AACbjD,MAAAA,IAAI,EAAEhD;AADO,KAAD,CAjBc;AAmB1B8D,IAAAA,QAAQ,EAAE,CAAC;AACXd,MAAAA,IAAI,EAAEhD;AADK,KAAD,CAnBgB;AAqB1BuK,IAAAA,MAAM,EAAE,CAAC;AACTvH,MAAAA,IAAI,EAAE/C;AADG,KAAD,CArBkB;AAuB1BuK,IAAAA,WAAW,EAAE,CAAC;AACdxH,MAAAA,IAAI,EAAE/C;AADQ,KAAD,CAvBa;AAyB1B6L,IAAAA,KAAK,EAAE,CAAC;AACR9I,MAAAA,IAAI,EAAE1C,eADE;AAER2C,MAAAA,IAAI,EAAE,CAACK,OAAD,EAAU;AACR;AACA;AACA2O,QAAAA,WAAW,EAAE;AAHL,OAAV;AAFE,KAAD;AAzBmB,GA9B1C;AAAA;AAgEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIC,YAAY,GAAG,CAAnB;AACA;AACA;AACA;AACA;;AACA,MAAMC,YAAN,CAAmB;AACflQ,EAAAA,WAAW,CAACQ,WAAD,EAAc2P,eAAd,EAA+B;AACtC,SAAK3P,WAAL,GAAmBA,WAAnB;AACA,SAAK2P,eAAL,GAAuBA,eAAvB;AACA;;AACA,SAAKxG,OAAL,GAAe,KAAf;AACA,SAAKyG,UAAL,GAAkB,KAAlB;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAKC,iBAAL,GAAyB,KAAKF,eAAL,CAAqBE,iBAA9C;AACA;;AACA,SAAKC,OAAL,GAAe,IAAI5S,YAAJ,EAAf;AACA;;AACA,SAAK+L,WAAL,GAAmB,EAAnB;AACA;;AACA,SAAKP,EAAL,GAAW,uBAAsB+G,YAAY,EAAG,EAAhD;AACA,SAAK7N,SAAL,GAAiB,KAAjB;AACA,SAAKwJ,YAAL,GAAoB,KAAKpL,WAAL,CAAiBwC,aAArC;AACH;AACD;;;AACY,MAARuN,QAAQ,CAAC7M,KAAD,EAAQ;AAChB,QAAIA,KAAJ,EAAW;AACP,WAAK8M,SAAL,GAAiB9M,KAAjB;;AACA,WAAK8M,SAAL,CAAe7E,aAAf,CAA6B,IAA7B;AACH;AACJ;AACD;AACJ;AACA;;;AACiB,MAAT8E,SAAS,GAAG;AACZ,WAAO,KAAKL,UAAZ;AACH;;AACY,MAATK,SAAS,CAAC/M,KAAD,EAAQ;AACjB,SAAK0M,UAAL,GAAkBpR,qBAAqB,CAAC0E,KAAD,CAAvC;AACH;AACD;;;AACY,MAARF,QAAQ,GAAG;AACX,WAAO,KAAKpB,SAAL,IAAmB,KAAKoO,SAAL,IAAkB,KAAKA,SAAL,CAAehN,QAA3D;AACH;;AACW,MAARA,QAAQ,CAACE,KAAD,EAAQ;AAChB,SAAKtB,SAAL,GAAiBpD,qBAAqB,CAAC0E,KAAD,CAAtC;AACH;AACD;;;AACS,MAALkF,KAAK,GAAG;AACR,WAAO,CAAC,KAAKgD,YAAL,CAAkBlI,KAA1B;AACH;;AACDgN,EAAAA,WAAW,GAAG;AACV,SAAKF,SAAL,CAAehH,YAAf,CAA4BrE,IAA5B;AACH;;AACDV,EAAAA,WAAW,GAAG;AACV,SAAK6L,OAAL,CAAa7E,QAAb;AACH;;AACDnB,EAAAA,kBAAkB,GAAG;AACjB,SAAKqG,yBAAL,GAAiC,KAAK/H,KAAtC;AACH;AACD;;;AACA+D,EAAAA,QAAQ,CAACrH,KAAD,EAAQ;AACZ,QAAIA,KAAJ,EAAW;AACP;AACA;AACA,UAAIA,KAAK,CAACG,OAAN,KAAkBrI,GAAlB,IAAyB,CAACC,cAAc,CAACiI,KAAD,EAAQ,UAAR,CAA5C,EAAiE;AAC7D,aAAKkL,SAAL,CAAe1F,iBAAf;AACH,OALM,CAMP;AACA;AACA;;;AACA,UAAIxF,KAAK,CAACG,OAAN,KAAkBvI,SAAlB,IAA+B,KAAKyT,yBAAxC,EAAmE;AAC/D,aAAKH,SAAL,CAAejG,WAAf,CAA2BqG,iBAA3B;;AACAtL,QAAAA,KAAK,CAACC,cAAN;AACA;AACH,OAJD,MAKK;AACD,aAAKoL,yBAAL,GAAiC,KAAjC;AACH;AACJ;;AACD,SAAKE,YAAL,CAAkBvL,KAAlB;AACH;AACD;AACJ;AACA;;;AACIwL,EAAAA,MAAM,CAACxL,KAAD,EAAQ;AACV;AACA,QAAI,CAAC,KAAKqL,yBAAN,IAAmCrL,KAAK,CAACG,OAAN,KAAkBvI,SAArD,IAAkE,KAAK0L,KAA3E,EAAkF;AAC9E,WAAK+H,yBAAL,GAAiC,IAAjC;AACArL,MAAAA,KAAK,CAACC,cAAN;AACH;AACJ;AACD;;;AACAG,EAAAA,KAAK,GAAG;AACJ,QAAI,KAAK+K,SAAT,EAAoB;AAChB,WAAKI,YAAL;AACH;;AACD,SAAKlH,OAAL,GAAe,KAAf,CAJI,CAKJ;;AACA,QAAI,CAAC,KAAK6G,SAAL,CAAe7G,OAApB,EAA6B;AACzB,WAAK6G,SAAL,CAAe9K,KAAf;AACH;;AACD,SAAK8K,SAAL,CAAehH,YAAf,CAA4BrE,IAA5B;AACH;;AACD4L,EAAAA,MAAM,GAAG;AACL,SAAKpH,OAAL,GAAe,IAAf;AACA,SAAKgH,yBAAL,GAAiC,KAAK/H,KAAtC;;AACA,SAAK4H,SAAL,CAAehH,YAAf,CAA4BrE,IAA5B;AACH;AACD;;;AACA0L,EAAAA,YAAY,CAACvL,KAAD,EAAQ;AAChB,QAAI,CAAC,KAAKsG,YAAL,CAAkBlI,KAAnB,IAA4B,CAAC,CAAC4B,KAAlC,EAAyC;AACrC,WAAKkL,SAAL,CAAe7D,QAAf,CAAwBrH,KAAxB;AACH;;AACD,QAAI,CAACA,KAAD,IAAU,KAAK0L,eAAL,CAAqB1L,KAArB,CAAd,EAA2C;AACvC,WAAKgL,OAAL,CAAa5L,IAAb,CAAkB;AACduM,QAAAA,KAAK,EAAE,KAAKrF,YADE;AAEdlI,QAAAA,KAAK,EAAE,KAAKkI,YAAL,CAAkBlI,KAFX;AAGdwN,QAAAA,SAAS,EAAE;AAHG,OAAlB;AAKA5L,MAAAA,KAAK,EAAEC,cAAP;AACH;AACJ;;AACD4L,EAAAA,QAAQ,GAAG;AACP;AACA,SAAKX,SAAL,CAAehH,YAAf,CAA4BrE,IAA5B;AACH;AACD;;;AACAD,EAAAA,KAAK,CAACsH,OAAD,EAAU;AACX,SAAKZ,YAAL,CAAkB1G,KAAlB,CAAwBsH,OAAxB;AACH;AACD;;;AACAsB,EAAAA,KAAK,GAAG;AACJ,SAAKlC,YAAL,CAAkBlI,KAAlB,GAA0B,EAA1B;AACA,SAAKiN,yBAAL,GAAiC,IAAjC;AACH;AACD;;;AACAK,EAAAA,eAAe,CAAC1L,KAAD,EAAQ;AACnB,WAAO,CAACjI,cAAc,CAACiI,KAAD,CAAf,IAA0B,IAAI8L,GAAJ,CAAQ,KAAKf,iBAAb,EAAgCgB,GAAhC,CAAoC/L,KAAK,CAACG,OAA1C,CAAjC;AACH;;AAzIc;;AA2InByK,YAAY,CAACvP,IAAb;AAAA,mBAAyGuP,YAAzG,EA9qCgG3S,EA8qChG,mBAAuIA,EAAE,CAACwI,UAA1I,GA9qCgGxI,EA8qChG,mBAAiKwJ,yBAAjK;AAAA;;AACAmJ,YAAY,CAACtP,IAAb,kBA/qCgGrD,EA+qChG;AAAA,QAA6F2S,YAA7F;AAAA;AAAA;AAAA;AAAA;AAAA;AA/qCgG3S,MAAAA,EA+qChG;AAAA,eAA6F,oBAA7F;AAAA;AAAA,eAA6F,kBAA7F;AAAA;AAAA,eAA6F,WAA7F;AAAA;AAAA,eAA6F,YAA7F;AAAA;AAAA,eAA6F,cAA7F;AAAA;AAAA;;AAAA;AA/qCgGA,MAAAA,EA+qChG;AA/qCgGA,MAAAA,EA+qChG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA/qCgGA,EA+qChG;AAAA;;AACA;AAAA,qDAhrCgGA,EAgrChG,mBAA2F2S,YAA3F,EAAqH,CAAC;AAC1GnP,IAAAA,IAAI,EAAEtD,SADoG;AAE1GuD,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,wBADX;AAECmF,MAAAA,QAAQ,EAAE,+BAFX;AAGClF,MAAAA,IAAI,EAAE;AACF,iBAAS,kCADP;AAEF,qBAAa,kBAFX;AAGF,mBAAW,gBAHT;AAIF,kBAAU,SAJR;AAKF,mBAAW,UALT;AAMF,mBAAW,YANT;AAOF,gBAAQ,IAPN;AAQF,2BAAmB,kBARjB;AASF,8BAAsB,qBATpB;AAUF,+BAAuB,uEAVrB;AAWF,gCAAwB;AAXtB;AAHP,KAAD;AAFoG,GAAD,CAArH,EAmB4B,YAAY;AAAE,WAAO,CAAC;AAAEH,MAAAA,IAAI,EAAExD,EAAE,CAACwI;AAAX,KAAD,EAA0B;AAAEhF,MAAAA,IAAI,EAAE+C,SAAR;AAAmBuC,MAAAA,UAAU,EAAE,CAAC;AACvFtF,QAAAA,IAAI,EAAEnD,MADiF;AAEvFoD,QAAAA,IAAI,EAAE,CAAC+F,yBAAD;AAFiF,OAAD;AAA/B,KAA1B,CAAP;AAGlB,GAtBxB,EAsB0C;AAAEwJ,IAAAA,QAAQ,EAAE,CAAC;AACvCxP,MAAAA,IAAI,EAAEhD,KADiC;AAEvCiD,MAAAA,IAAI,EAAE,CAAC,iBAAD;AAFiC,KAAD,CAAZ;AAG1ByP,IAAAA,SAAS,EAAE,CAAC;AACZ1P,MAAAA,IAAI,EAAEhD,KADM;AAEZiD,MAAAA,IAAI,EAAE,CAAC,uBAAD;AAFM,KAAD,CAHe;AAM1BqP,IAAAA,iBAAiB,EAAE,CAAC;AACpBtP,MAAAA,IAAI,EAAEhD,KADc;AAEpBiD,MAAAA,IAAI,EAAE,CAAC,+BAAD;AAFc,KAAD,CANO;AAS1BsP,IAAAA,OAAO,EAAE,CAAC;AACVvP,MAAAA,IAAI,EAAE/C,MADI;AAEVgD,MAAAA,IAAI,EAAE,CAAC,sBAAD;AAFI,KAAD,CATiB;AAY1ByI,IAAAA,WAAW,EAAE,CAAC;AACd1I,MAAAA,IAAI,EAAEhD;AADQ,KAAD,CAZa;AAc1BmL,IAAAA,EAAE,EAAE,CAAC;AACLnI,MAAAA,IAAI,EAAEhD;AADD,KAAD,CAdsB;AAgB1ByF,IAAAA,QAAQ,EAAE,CAAC;AACXzC,MAAAA,IAAI,EAAEhD;AADK,KAAD;AAhBgB,GAtB1C;AAAA;AA0CA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMuT,iBAAiB,GAAG,CACtB/J,WADsB,EAEtBlG,OAFsB,EAGtB6O,YAHsB,EAItBzJ,aAJsB,EAKtB/F,aALsB,EAMtBU,mBANsB,CAA1B;;AAQA,MAAMmQ,cAAN,CAAqB;;AAErBA,cAAc,CAAC5Q,IAAf;AAAA,mBAA2G4Q,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBA5uCgGjU,EA4uChG;AAAA,QAA4GgU;AAA5G;AAWAA,cAAc,CAACE,IAAf,kBAvvCgGlU,EAuvChG;AAAA,aAAuI,CAC/HwB,iBAD+H,EAE/H;AACI8B,IAAAA,OAAO,EAAEkG,yBADb;AAEI2K,IAAAA,QAAQ,EAAE;AACNrB,MAAAA,iBAAiB,EAAE,CAAC/S,KAAD;AADb;AAFd,GAF+H,CAAvI;AAAA,YAQiB,CAACwB,eAAD,CARjB;AAAA;;AASA;AAAA,qDAhwCgGvB,EAgwChG,mBAA2FgU,cAA3F,EAAuH,CAAC;AAC5GxQ,IAAAA,IAAI,EAAEzC,QADsG;AAE5G0C,IAAAA,IAAI,EAAE,CAAC;AACC2Q,MAAAA,OAAO,EAAE,CAAC7S,eAAD,CADV;AAEC8S,MAAAA,OAAO,EAAEN,iBAFV;AAGCO,MAAAA,YAAY,EAAEP,iBAHf;AAICnQ,MAAAA,SAAS,EAAE,CACPpC,iBADO,EAEP;AACI8B,QAAAA,OAAO,EAAEkG,yBADb;AAEI2K,QAAAA,QAAQ,EAAE;AACNrB,UAAAA,iBAAiB,EAAE,CAAC/S,KAAD;AADb;AAFd,OAFO;AAJZ,KAAD;AAFsG,GAAD,CAAvH;AAAA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASyJ,yBAAT,EAAoC1G,eAApC,EAAqDD,eAArD,EAAsEE,sBAAtE,EAA8Fe,OAA9F,EAAuGX,aAAvG,EAAsHwP,YAAtH,EAAoI3I,WAApI,EAAiJD,iBAAjJ,EAAoKb,aAApK,EAAmL1G,sBAAnL,EAA2MqB,mBAA3M,EAAgOmQ,cAAhO", "sourcesContent": ["import { <PERSON>AC<PERSON>, BACKSPACE, DELE<PERSON>, TAB, hasMod<PERSON><PERSON><PERSON>, ENT<PERSON> } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, EventEmitter, Optional, Inject, Attribute, ContentChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, Self, ContentChildren, NgModule } from '@angular/core';\nimport * as i3 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, RippleRenderer, MAT_RIPPLE_GLOBAL_OPTIONS, mixinErrorState, MatCommonModule, ErrorStateMatcher } from '@angular/material/core';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { Subject, merge } from 'rxjs';\nimport { take, takeUntil, startWith } from 'rxjs/operators';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { MatFormFieldControl } from '@angular/material/form-field';\n\n/** Event object emitted by MatChip when selected or deselected. */\nclass MatChipSelectionChange {\n    constructor(\n    /** Reference to the chip that emitted the event. */\n    source, \n    /** Whether the chip that emitted the event is selected. */\n    selected, \n    /** Whether the selection change was a result of a user interaction. */\n    isUserInput = false) {\n        this.source = source;\n        this.selected = selected;\n        this.isUserInput = isUserInput;\n    }\n}\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_REMOVE = new InjectionToken('MatChipRemove');\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_AVATAR = new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_TRAILING_ICON = new InjectionToken('MatChipTrailingIcon');\n// Boilerplate for applying mixins to MatChip.\n/** @docs-private */\nclass MatChipBase {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}\nconst _MatChipMixinBase = mixinTabIndex(mixinColor(mixinDisableRipple(MatChipBase), 'primary'), -1);\n/**\n * Dummy directive to add CSS class to chip avatar.\n * @docs-private\n */\nclass MatChipAvatar {\n}\nMatChipAvatar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatChipAvatar.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatChipAvatar, selector: \"mat-chip-avatar, [matChipAvatar]\", host: { classAttribute: \"mat-chip-avatar\" }, providers: [{ provide: MAT_CHIP_AVATAR, useExisting: MatChipAvatar }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-chip-avatar, [matChipAvatar]',\n                    host: { 'class': 'mat-chip-avatar' },\n                    providers: [{ provide: MAT_CHIP_AVATAR, useExisting: MatChipAvatar }],\n                }]\n        }] });\n/**\n * Dummy directive to add CSS class to chip trailing icon.\n * @docs-private\n */\nclass MatChipTrailingIcon {\n}\nMatChipTrailingIcon.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipTrailingIcon, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nMatChipTrailingIcon.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatChipTrailingIcon, selector: \"mat-chip-trailing-icon, [matChipTrailingIcon]\", host: { classAttribute: \"mat-chip-trailing-icon\" }, providers: [{ provide: MAT_CHIP_TRAILING_ICON, useExisting: MatChipTrailingIcon }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipTrailingIcon, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n                    host: { 'class': 'mat-chip-trailing-icon' },\n                    providers: [{ provide: MAT_CHIP_TRAILING_ICON, useExisting: MatChipTrailingIcon }],\n                }]\n        }] });\n/** Material Design styled chip directive. Used inside the MatChipList component. */\nclass MatChip extends _MatChipMixinBase {\n    constructor(elementRef, _ngZone, platform, globalRippleOptions, _changeDetectorRef, _document, animationMode, tabIndex) {\n        super(elementRef);\n        this._ngZone = _ngZone;\n        this._changeDetectorRef = _changeDetectorRef;\n        /** Whether the chip has focus. */\n        this._hasFocus = false;\n        /** Whether the chip list is selectable */\n        this.chipListSelectable = true;\n        /** Whether the chip list is in multi-selection mode. */\n        this._chipListMultiple = false;\n        /** Whether the chip list as a whole is disabled. */\n        this._chipListDisabled = false;\n        this._selected = false;\n        this._selectable = true;\n        this._disabled = false;\n        this._removable = true;\n        /** Emits when the chip is focused. */\n        this._onFocus = new Subject();\n        /** Emits when the chip is blured. */\n        this._onBlur = new Subject();\n        /** Emitted when the chip is selected or deselected. */\n        this.selectionChange = new EventEmitter();\n        /** Emitted when the chip is destroyed. */\n        this.destroyed = new EventEmitter();\n        /** Emitted when a chip is to be removed. */\n        this.removed = new EventEmitter();\n        this._addHostClassName();\n        // Dynamically create the ripple target, append it within the chip, and use it as the\n        // chip's ripple target. Adding the class '.mat-chip-ripple' ensures that it will have\n        // the proper styles.\n        this._chipRippleTarget = _document.createElement('div');\n        this._chipRippleTarget.classList.add('mat-chip-ripple');\n        this._elementRef.nativeElement.appendChild(this._chipRippleTarget);\n        this._chipRipple = new RippleRenderer(this, _ngZone, this._chipRippleTarget, platform);\n        this._chipRipple.setupTriggerEvents(elementRef);\n        this.rippleConfig = globalRippleOptions || {};\n        this._animationsDisabled = animationMode === 'NoopAnimations';\n        this.tabIndex = tabIndex != null ? parseInt(tabIndex) || -1 : -1;\n    }\n    /**\n     * Whether ripples are disabled on interaction\n     * @docs-private\n     */\n    get rippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._animationsDisabled ||\n            !!this.rippleConfig.disabled);\n    }\n    /** Whether the chip is selected. */\n    get selected() {\n        return this._selected;\n    }\n    set selected(value) {\n        const coercedValue = coerceBooleanProperty(value);\n        if (coercedValue !== this._selected) {\n            this._selected = coercedValue;\n            this._dispatchSelectionChange();\n        }\n    }\n    /** The value of the chip. Defaults to the content inside `<mat-chip>` tags. */\n    get value() {\n        return this._value !== undefined ? this._value : this._elementRef.nativeElement.textContent;\n    }\n    set value(value) {\n        this._value = value;\n    }\n    /**\n     * Whether or not the chip is selectable. When a chip is not selectable,\n     * changes to its selected state are always ignored. By default a chip is\n     * selectable, and it becomes non-selectable if its parent chip list is\n     * not selectable.\n     */\n    get selectable() {\n        return this._selectable && this.chipListSelectable;\n    }\n    set selectable(value) {\n        this._selectable = coerceBooleanProperty(value);\n    }\n    /** Whether the chip is disabled. */\n    get disabled() {\n        return this._chipListDisabled || this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    /**\n     * Determines whether or not the chip displays the remove styling and emits (removed) events.\n     */\n    get removable() {\n        return this._removable;\n    }\n    set removable(value) {\n        this._removable = coerceBooleanProperty(value);\n    }\n    /** The ARIA selected applied to the chip. */\n    get ariaSelected() {\n        // Remove the `aria-selected` when the chip is deselected in single-selection mode, because\n        // it adds noise to NVDA users where \"not selected\" will be read out for each chip.\n        return this.selectable && (this._chipListMultiple || this.selected)\n            ? this.selected.toString()\n            : null;\n    }\n    _addHostClassName() {\n        const basicChipAttrName = 'mat-basic-chip';\n        const element = this._elementRef.nativeElement;\n        if (element.hasAttribute(basicChipAttrName) ||\n            element.tagName.toLowerCase() === basicChipAttrName) {\n            element.classList.add(basicChipAttrName);\n            return;\n        }\n        else {\n            element.classList.add('mat-standard-chip');\n        }\n    }\n    ngOnDestroy() {\n        this.destroyed.emit({ chip: this });\n        this._chipRipple._removeTriggerEvents();\n    }\n    /** Selects the chip. */\n    select() {\n        if (!this._selected) {\n            this._selected = true;\n            this._dispatchSelectionChange();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Deselects the chip. */\n    deselect() {\n        if (this._selected) {\n            this._selected = false;\n            this._dispatchSelectionChange();\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Select this chip and emit selected event */\n    selectViaInteraction() {\n        if (!this._selected) {\n            this._selected = true;\n            this._dispatchSelectionChange(true);\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Toggles the current selected state of this chip. */\n    toggleSelected(isUserInput = false) {\n        this._selected = !this.selected;\n        this._dispatchSelectionChange(isUserInput);\n        this._changeDetectorRef.markForCheck();\n        return this.selected;\n    }\n    /** Allows for programmatic focusing of the chip. */\n    focus() {\n        if (!this._hasFocus) {\n            this._elementRef.nativeElement.focus();\n            this._onFocus.next({ chip: this });\n        }\n        this._hasFocus = true;\n    }\n    /**\n     * Allows for programmatic removal of the chip. Called by the MatChipList when the DELETE or\n     * BACKSPACE keys are pressed.\n     *\n     * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n     */\n    remove() {\n        if (this.removable) {\n            this.removed.emit({ chip: this });\n        }\n    }\n    /** Handles click events on the chip. */\n    _handleClick(event) {\n        if (this.disabled) {\n            event.preventDefault();\n        }\n    }\n    /** Handle custom key presses. */\n    _handleKeydown(event) {\n        if (this.disabled) {\n            return;\n        }\n        switch (event.keyCode) {\n            case DELETE:\n            case BACKSPACE:\n                // If we are removable, remove the focused chip\n                this.remove();\n                // Always prevent so page navigation does not occur\n                event.preventDefault();\n                break;\n            case SPACE:\n                // If we are selectable, toggle the focused chip\n                if (this.selectable) {\n                    this.toggleSelected(true);\n                }\n                // Always prevent space from scrolling the page since the list has focus\n                event.preventDefault();\n                break;\n        }\n    }\n    _blur() {\n        // When animations are enabled, Angular may end up removing the chip from the DOM a little\n        // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n        // that moves focus not the next item. To work around the issue, we defer marking the chip\n        // as not focused until the next time the zone stabilizes.\n        this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            this._ngZone.run(() => {\n                this._hasFocus = false;\n                this._onBlur.next({ chip: this });\n            });\n        });\n    }\n    _dispatchSelectionChange(isUserInput = false) {\n        this.selectionChange.emit({\n            source: this,\n            isUserInput,\n            selected: this._selected,\n        });\n    }\n}\nMatChip.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChip, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1.Platform }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: i0.ChangeDetectorRef }, { token: DOCUMENT }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: 'tabindex', attribute: true }], target: i0.ɵɵFactoryTarget.Directive });\nMatChip.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatChip, selector: \"mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]\", inputs: { color: \"color\", disableRipple: \"disableRipple\", tabIndex: \"tabIndex\", selected: \"selected\", value: \"value\", selectable: \"selectable\", disabled: \"disabled\", removable: \"removable\" }, outputs: { selectionChange: \"selectionChange\", destroyed: \"destroyed\", removed: \"removed\" }, host: { attributes: { \"role\": \"option\" }, listeners: { \"click\": \"_handleClick($event)\", \"keydown\": \"_handleKeydown($event)\", \"focus\": \"focus()\", \"blur\": \"_blur()\" }, properties: { \"attr.tabindex\": \"disabled ? null : tabIndex\", \"class.mat-chip-selected\": \"selected\", \"class.mat-chip-with-avatar\": \"avatar\", \"class.mat-chip-with-trailing-icon\": \"trailingIcon || removeIcon\", \"class.mat-chip-disabled\": \"disabled\", \"class._mat-animation-noopable\": \"_animationsDisabled\", \"attr.disabled\": \"disabled || null\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-selected\": \"ariaSelected\" }, classAttribute: \"mat-chip mat-focus-indicator\" }, queries: [{ propertyName: \"avatar\", first: true, predicate: MAT_CHIP_AVATAR, descendants: true }, { propertyName: \"trailingIcon\", first: true, predicate: MAT_CHIP_TRAILING_ICON, descendants: true }, { propertyName: \"removeIcon\", first: true, predicate: MAT_CHIP_REMOVE, descendants: true }], exportAs: [\"matChip\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]`,\n                    inputs: ['color', 'disableRipple', 'tabIndex'],\n                    exportAs: 'matChip',\n                    host: {\n                        'class': 'mat-chip mat-focus-indicator',\n                        '[attr.tabindex]': 'disabled ? null : tabIndex',\n                        'role': 'option',\n                        '[class.mat-chip-selected]': 'selected',\n                        '[class.mat-chip-with-avatar]': 'avatar',\n                        '[class.mat-chip-with-trailing-icon]': 'trailingIcon || removeIcon',\n                        '[class.mat-chip-disabled]': 'disabled',\n                        '[class._mat-animation-noopable]': '_animationsDisabled',\n                        '[attr.disabled]': 'disabled || null',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-selected]': 'ariaSelected',\n                        '(click)': '_handleClick($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(focus)': 'focus()',\n                        '(blur)': '_blur()',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }]; }, propDecorators: { avatar: [{\n                type: ContentChild,\n                args: [MAT_CHIP_AVATAR]\n            }], trailingIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_TRAILING_ICON]\n            }], removeIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_REMOVE]\n            }], selected: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], selectable: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], removable: [{\n                type: Input\n            }], selectionChange: [{\n                type: Output\n            }], destroyed: [{\n                type: Output\n            }], removed: [{\n                type: Output\n            }] } });\n/**\n * Applies proper (click) support and adds styling for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n *     `<mat-chip>\n *       <mat-icon matChipRemove>cancel</mat-icon>\n *     </mat-chip>`\n *\n * You *may* use a custom icon, but you may need to override the `mat-chip-remove` positioning\n * styles to properly center the icon within the chip.\n */\nclass MatChipRemove {\n    constructor(_parentChip, elementRef) {\n        this._parentChip = _parentChip;\n        if (elementRef.nativeElement.nodeName === 'BUTTON') {\n            elementRef.nativeElement.setAttribute('type', 'button');\n        }\n    }\n    /** Calls the parent chip's public `remove()` method if applicable. */\n    _handleClick(event) {\n        const parentChip = this._parentChip;\n        if (parentChip.removable && !parentChip.disabled) {\n            parentChip.remove();\n        }\n        // We need to stop event propagation because otherwise the event will bubble up to the\n        // form field and cause the `onContainerClick` method to be invoked. This method would then\n        // reset the focused chip that has been focused after chip removal. Usually the parent\n        // the parent click listener of the `MatChip` would prevent propagation, but it can happen\n        // that the chip is being removed before the event bubbles up.\n        event.stopPropagation();\n        event.preventDefault();\n    }\n}\nMatChipRemove.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipRemove, deps: [{ token: MatChip }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nMatChipRemove.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatChipRemove, selector: \"[matChipRemove]\", host: { listeners: { \"click\": \"_handleClick($event)\" }, classAttribute: \"mat-chip-remove mat-chip-trailing-icon\" }, providers: [{ provide: MAT_CHIP_REMOVE, useExisting: MatChipRemove }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipRemove, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matChipRemove]',\n                    host: {\n                        'class': 'mat-chip-remove mat-chip-trailing-icon',\n                        '(click)': '_handleClick($event)',\n                    },\n                    providers: [{ provide: MAT_CHIP_REMOVE, useExisting: MatChipRemove }],\n                }]\n        }], ctorParameters: function () { return [{ type: MatChip }, { type: i0.ElementRef }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Injection token to be used to override the default options for the chips module. */\nconst MAT_CHIPS_DEFAULT_OPTIONS = new InjectionToken('mat-chips-default-options');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Boilerplate for applying mixins to MatChipList.\n/** @docs-private */\nconst _MatChipListBase = mixinErrorState(class {\n    constructor(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, \n    /** @docs-private */\n    ngControl) {\n        this._defaultErrorStateMatcher = _defaultErrorStateMatcher;\n        this._parentForm = _parentForm;\n        this._parentFormGroup = _parentFormGroup;\n        this.ngControl = ngControl;\n    }\n});\n// Increasing integer for generating unique ids for chip-list components.\nlet nextUniqueId$1 = 0;\n/** Change event object that is emitted when the chip list value has changed. */\nclass MatChipListChange {\n    constructor(\n    /** Chip list that emitted the event. */\n    source, \n    /** Value of the chip list when the event was emitted. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/**\n * A material design chips component (named ChipList for its similarity to the List component).\n */\nclass MatChipList extends _MatChipListBase {\n    constructor(_elementRef, _changeDetectorRef, _dir, _parentForm, _parentFormGroup, _defaultErrorStateMatcher, ngControl) {\n        super(_defaultErrorStateMatcher, _parentForm, _parentFormGroup, ngControl);\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._dir = _dir;\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.controlType = 'mat-chip-list';\n        /**\n         * When a chip is destroyed, we store the index of the destroyed chip until the chips\n         * query list notifies about the update. This is necessary because we cannot determine an\n         * appropriate chip that should receive focus until the array of chips updated completely.\n         */\n        this._lastDestroyedChipIndex = null;\n        /** Subject that emits when the component has been destroyed. */\n        this._destroyed = new Subject();\n        /** Uid of the chip list */\n        this._uid = `mat-chip-list-${nextUniqueId$1++}`;\n        /** Tab index for the chip list. */\n        this._tabIndex = 0;\n        /**\n         * User defined tab index.\n         * When it is not null, use user defined tab index. Otherwise use _tabIndex\n         */\n        this._userTabIndex = null;\n        /** Function when touched */\n        this._onTouched = () => { };\n        /** Function when changed */\n        this._onChange = () => { };\n        this._multiple = false;\n        this._compareWith = (o1, o2) => o1 === o2;\n        this._disabled = false;\n        /** Orientation of the chip list. */\n        this.ariaOrientation = 'horizontal';\n        this._selectable = true;\n        /** Event emitted when the selected chip list value has been changed by the user. */\n        this.change = new EventEmitter();\n        /**\n         * Event that emits whenever the raw value of the chip-list changes. This is here primarily\n         * to facilitate the two-way binding for the `value` input.\n         * @docs-private\n         */\n        this.valueChange = new EventEmitter();\n        if (this.ngControl) {\n            this.ngControl.valueAccessor = this;\n        }\n    }\n    /** The array of selected chips inside chip list. */\n    get selected() {\n        return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The ARIA role applied to the chip list. */\n    get role() {\n        return this.empty ? null : 'listbox';\n    }\n    /** Whether the user should be allowed to select multiple chips. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        this._multiple = coerceBooleanProperty(value);\n        this._syncChipsState();\n    }\n    /**\n     * A function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n        return this._compareWith;\n    }\n    set compareWith(fn) {\n        this._compareWith = fn;\n        if (this._selectionModel) {\n            // A different comparator means the selection could change.\n            this._initializeSelection();\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        this.writeValue(value);\n        this._value = value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n        return this._chipInput ? this._chipInput.id : this._uid;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n        this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get placeholder() {\n        return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    /** Whether any chips or the matChipInput inside of this chip-list has focus. */\n    get focused() {\n        return (this._chipInput && this._chipInput.focused) || this._hasFocusedChip();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n        return (!this._chipInput || this._chipInput.empty) && (!this.chips || this.chips.length === 0);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        return !this.empty || this.focused;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n        return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._syncChipsState();\n    }\n    /**\n     * Whether or not this chip list is selectable. When a chip list is not selectable,\n     * the selected states for all the chips inside the chip list are always ignored.\n     */\n    get selectable() {\n        return this._selectable;\n    }\n    set selectable(value) {\n        this._selectable = coerceBooleanProperty(value);\n        if (this.chips) {\n            this.chips.forEach(chip => (chip.chipListSelectable = this._selectable));\n        }\n    }\n    set tabIndex(value) {\n        this._userTabIndex = value;\n        this._tabIndex = value;\n    }\n    /** Combined stream of all of the child chips' selection change events. */\n    get chipSelectionChanges() {\n        return merge(...this.chips.map(chip => chip.selectionChange));\n    }\n    /** Combined stream of all of the child chips' focus change events. */\n    get chipFocusChanges() {\n        return merge(...this.chips.map(chip => chip._onFocus));\n    }\n    /** Combined stream of all of the child chips' blur change events. */\n    get chipBlurChanges() {\n        return merge(...this.chips.map(chip => chip._onBlur));\n    }\n    /** Combined stream of all of the child chips' remove change events. */\n    get chipRemoveChanges() {\n        return merge(...this.chips.map(chip => chip.destroyed));\n    }\n    ngAfterContentInit() {\n        this._keyManager = new FocusKeyManager(this.chips)\n            .withWrap()\n            .withVerticalOrientation()\n            .withHomeAndEnd()\n            .withHorizontalOrientation(this._dir ? this._dir.value : 'ltr');\n        if (this._dir) {\n            this._dir.change\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(dir => this._keyManager.withHorizontalOrientation(dir));\n        }\n        this._keyManager.tabOut.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            this._allowFocusEscape();\n        });\n        // When the list changes, re-subscribe\n        this.chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            if (this.disabled) {\n                // Since this happens after the content has been\n                // checked, we need to defer it to the next tick.\n                Promise.resolve().then(() => {\n                    this._syncChipsState();\n                });\n            }\n            this._resetChips();\n            // Reset chips selected/deselected status\n            this._initializeSelection();\n            // Check to see if we need to update our tab index\n            this._updateTabIndex();\n            // Check to see if we have a destroyed chip and need to refocus\n            this._updateFocusForDestroyedChips();\n            this.stateChanges.next();\n        });\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple, undefined, false);\n        this.stateChanges.next();\n    }\n    ngDoCheck() {\n        if (this.ngControl) {\n            // We need to re-evaluate this on every change detection cycle, because there are some\n            // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n            // that whatever logic is in here has to be super lean or we risk destroying the performance.\n            this.updateErrorState();\n            if (this.ngControl.disabled !== this._disabled) {\n                this.disabled = !!this.ngControl.disabled;\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        this.stateChanges.complete();\n        this._dropSubscriptions();\n    }\n    /** Associates an HTML input element with this chip list. */\n    registerInput(inputElement) {\n        this._chipInput = inputElement;\n        // We use this attribute to match the chip list to its input in test harnesses.\n        // Set the attribute directly here to avoid \"changed after checked\" errors.\n        this._elementRef.nativeElement.setAttribute('data-mat-chip-input', inputElement.id);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        this._ariaDescribedby = ids.join(' ');\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        if (this.chips) {\n            this._setSelectionByValue(value, false);\n        }\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick(event) {\n        if (!this._originatesFromChip(event)) {\n            this.focus();\n        }\n    }\n    /**\n     * Focuses the first non-disabled chip in this chip list, or the associated input when there\n     * are no eligible chips.\n     */\n    focus(options) {\n        if (this.disabled) {\n            return;\n        }\n        // TODO: ARIA says this should focus the first `selected` chip if any are selected.\n        // Focus on first element if there's no chipInput inside chip-list\n        if (this._chipInput && this._chipInput.focused) {\n            // do nothing\n        }\n        else if (this.chips.length > 0) {\n            this._keyManager.setFirstItemActive();\n            this.stateChanges.next();\n        }\n        else {\n            this._focusInput(options);\n            this.stateChanges.next();\n        }\n    }\n    /** Attempt to focus an input if we have one. */\n    _focusInput(options) {\n        if (this._chipInput) {\n            this._chipInput.focus(options);\n        }\n    }\n    /**\n     * Pass events to the keyboard manager. Available here for tests.\n     */\n    _keydown(event) {\n        const target = event.target;\n        if (target && target.classList.contains('mat-chip')) {\n            this._keyManager.onKeydown(event);\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Check the tab index as you should not be allowed to focus an empty list.\n     */\n    _updateTabIndex() {\n        // If we have 0 chips, we should not allow keyboard focus\n        this._tabIndex = this._userTabIndex || (this.chips.length === 0 ? -1 : 0);\n    }\n    /**\n     * If the amount of chips changed, we need to update the\n     * key manager state and focus the next closest chip.\n     */\n    _updateFocusForDestroyedChips() {\n        // Move focus to the closest chip. If no other chips remain, focus the chip-list itself.\n        if (this._lastDestroyedChipIndex != null) {\n            if (this.chips.length) {\n                const newChipIndex = Math.min(this._lastDestroyedChipIndex, this.chips.length - 1);\n                this._keyManager.setActiveItem(newChipIndex);\n            }\n            else {\n                this.focus();\n            }\n        }\n        this._lastDestroyedChipIndex = null;\n    }\n    /**\n     * Utility to ensure all indexes are valid.\n     *\n     * @param index The index to be checked.\n     * @returns True if the index is valid for our list of chips.\n     */\n    _isValidIndex(index) {\n        return index >= 0 && index < this.chips.length;\n    }\n    _setSelectionByValue(value, isUserInput = true) {\n        this._clearSelection();\n        this.chips.forEach(chip => chip.deselect());\n        if (Array.isArray(value)) {\n            value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n            this._sortValues();\n        }\n        else {\n            const correspondingChip = this._selectValue(value, isUserInput);\n            // Shift focus to the active item. Note that we shouldn't do this in multiple\n            // mode, because we don't know what chip the user interacted with last.\n            if (correspondingChip) {\n                if (isUserInput) {\n                    this._keyManager.setActiveItem(correspondingChip);\n                }\n            }\n        }\n    }\n    /**\n     * Finds and selects the chip based on its value.\n     * @returns Chip that has the corresponding value.\n     */\n    _selectValue(value, isUserInput = true) {\n        const correspondingChip = this.chips.find(chip => {\n            return chip.value != null && this._compareWith(chip.value, value);\n        });\n        if (correspondingChip) {\n            isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n            this._selectionModel.select(correspondingChip);\n        }\n        return correspondingChip;\n    }\n    _initializeSelection() {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n            if (this.ngControl || this._value) {\n                this._setSelectionByValue(this.ngControl ? this.ngControl.value : this._value, false);\n                this.stateChanges.next();\n            }\n        });\n    }\n    /**\n     * Deselects every chip in the list.\n     * @param skip Chip that should not be deselected.\n     */\n    _clearSelection(skip) {\n        this._selectionModel.clear();\n        this.chips.forEach(chip => {\n            if (chip !== skip) {\n                chip.deselect();\n            }\n        });\n        this.stateChanges.next();\n    }\n    /**\n     * Sorts the model values, ensuring that they keep the same\n     * order that they have in the panel.\n     */\n    _sortValues() {\n        if (this._multiple) {\n            this._selectionModel.clear();\n            this.chips.forEach(chip => {\n                if (chip.selected) {\n                    this._selectionModel.select(chip);\n                }\n            });\n            this.stateChanges.next();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n        let valueToEmit = null;\n        if (Array.isArray(this.selected)) {\n            valueToEmit = this.selected.map(chip => chip.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : fallbackValue;\n        }\n        this._value = valueToEmit;\n        this.change.emit(new MatChipListChange(this, valueToEmit));\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** When blurred, mark the field as touched when focus moved outside the chip list. */\n    _blur() {\n        if (!this._hasFocusedChip()) {\n            this._keyManager.setActiveItem(-1);\n        }\n        if (!this.disabled) {\n            if (this._chipInput) {\n                // If there's a chip input, we should check whether the focus moved to chip input.\n                // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n                // to chip input, do nothing.\n                // Timeout is needed to wait for the focus() event trigger on chip input.\n                setTimeout(() => {\n                    if (!this.focused) {\n                        this._markAsTouched();\n                    }\n                });\n            }\n            else {\n                // If there's no chip input, then mark the field as touched.\n                this._markAsTouched();\n            }\n        }\n    }\n    /** Mark the field as touched */\n    _markAsTouched() {\n        this._onTouched();\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    /**\n     * Removes the `tabindex` from the chip list and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the list from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n        if (this._tabIndex !== -1) {\n            this._tabIndex = -1;\n            setTimeout(() => {\n                this._tabIndex = this._userTabIndex || 0;\n                this._changeDetectorRef.markForCheck();\n            });\n        }\n    }\n    _resetChips() {\n        this._dropSubscriptions();\n        this._listenToChipsFocus();\n        this._listenToChipsSelection();\n        this._listenToChipsRemoved();\n    }\n    _dropSubscriptions() {\n        if (this._chipFocusSubscription) {\n            this._chipFocusSubscription.unsubscribe();\n            this._chipFocusSubscription = null;\n        }\n        if (this._chipBlurSubscription) {\n            this._chipBlurSubscription.unsubscribe();\n            this._chipBlurSubscription = null;\n        }\n        if (this._chipSelectionSubscription) {\n            this._chipSelectionSubscription.unsubscribe();\n            this._chipSelectionSubscription = null;\n        }\n        if (this._chipRemoveSubscription) {\n            this._chipRemoveSubscription.unsubscribe();\n            this._chipRemoveSubscription = null;\n        }\n    }\n    /** Listens to user-generated selection events on each chip. */\n    _listenToChipsSelection() {\n        this._chipSelectionSubscription = this.chipSelectionChanges.subscribe(event => {\n            event.source.selected\n                ? this._selectionModel.select(event.source)\n                : this._selectionModel.deselect(event.source);\n            // For single selection chip list, make sure the deselected value is unselected.\n            if (!this.multiple) {\n                this.chips.forEach(chip => {\n                    if (!this._selectionModel.isSelected(chip) && chip.selected) {\n                        chip.deselect();\n                    }\n                });\n            }\n            if (event.isUserInput) {\n                this._propagateChanges();\n            }\n        });\n    }\n    /** Listens to user-generated selection events on each chip. */\n    _listenToChipsFocus() {\n        this._chipFocusSubscription = this.chipFocusChanges.subscribe(event => {\n            let chipIndex = this.chips.toArray().indexOf(event.chip);\n            if (this._isValidIndex(chipIndex)) {\n                this._keyManager.updateActiveItem(chipIndex);\n            }\n            this.stateChanges.next();\n        });\n        this._chipBlurSubscription = this.chipBlurChanges.subscribe(() => {\n            this._blur();\n            this.stateChanges.next();\n        });\n    }\n    _listenToChipsRemoved() {\n        this._chipRemoveSubscription = this.chipRemoveChanges.subscribe(event => {\n            const chip = event.chip;\n            const chipIndex = this.chips.toArray().indexOf(event.chip);\n            // In case the chip that will be removed is currently focused, we temporarily store\n            // the index in order to be able to determine an appropriate sibling chip that will\n            // receive focus.\n            if (this._isValidIndex(chipIndex) && chip._hasFocus) {\n                this._lastDestroyedChipIndex = chipIndex;\n            }\n        });\n    }\n    /** Checks whether an event comes from inside a chip element. */\n    _originatesFromChip(event) {\n        let currentElement = event.target;\n        while (currentElement && currentElement !== this._elementRef.nativeElement) {\n            if (currentElement.classList.contains('mat-chip')) {\n                return true;\n            }\n            currentElement = currentElement.parentElement;\n        }\n        return false;\n    }\n    /** Checks whether any of the chips is focused. */\n    _hasFocusedChip() {\n        return this.chips && this.chips.some(chip => chip._hasFocus);\n    }\n    /** Syncs the list's state with the individual chips. */\n    _syncChipsState() {\n        if (this.chips) {\n            this.chips.forEach(chip => {\n                chip._chipListDisabled = this._disabled;\n                chip._chipListMultiple = this.multiple;\n            });\n        }\n    }\n}\nMatChipList.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipList, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1$1.Directionality, optional: true }, { token: i2.NgForm, optional: true }, { token: i2.FormGroupDirective, optional: true }, { token: i3.ErrorStateMatcher }, { token: i2.NgControl, optional: true, self: true }], target: i0.ɵɵFactoryTarget.Component });\nMatChipList.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatChipList, selector: \"mat-chip-list\", inputs: { errorStateMatcher: \"errorStateMatcher\", multiple: \"multiple\", compareWith: \"compareWith\", value: \"value\", required: \"required\", placeholder: \"placeholder\", disabled: \"disabled\", ariaOrientation: [\"aria-orientation\", \"ariaOrientation\"], selectable: \"selectable\", tabIndex: \"tabIndex\" }, outputs: { change: \"change\", valueChange: \"valueChange\" }, host: { listeners: { \"focus\": \"focus()\", \"blur\": \"_blur()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.tabindex\": \"disabled ? null : _tabIndex\", \"attr.aria-describedby\": \"_ariaDescribedby || null\", \"attr.aria-required\": \"role ? required : null\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"attr.aria-multiselectable\": \"multiple\", \"attr.role\": \"role\", \"class.mat-chip-list-disabled\": \"disabled\", \"class.mat-chip-list-invalid\": \"errorState\", \"class.mat-chip-list-required\": \"required\", \"attr.aria-orientation\": \"ariaOrientation\", \"id\": \"_uid\" }, classAttribute: \"mat-chip-list\" }, providers: [{ provide: MatFormFieldControl, useExisting: MatChipList }], queries: [{ propertyName: \"chips\", predicate: MatChip, descendants: true }], exportAs: [\"matChipList\"], usesInheritance: true, ngImport: i0, template: `<div class=\"mat-chip-list-wrapper\"><ng-content></ng-content></div>`, isInline: true, styles: [\".mat-chip{position:relative;box-sizing:border-box;-webkit-tap-highlight-color:transparent;border:none;-webkit-appearance:none;-moz-appearance:none}.mat-standard-chip{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:inline-flex;padding:7px 12px;border-radius:16px;align-items:center;cursor:default;min-height:32px;height:1px}._mat-animation-noopable.mat-standard-chip{transition:none;animation:none}.mat-standard-chip .mat-chip-remove{border:none;-webkit-appearance:none;-moz-appearance:none;padding:0;background:none}.mat-standard-chip .mat-chip-remove.mat-icon,.mat-standard-chip .mat-chip-remove .mat-icon{width:18px;height:18px;font-size:18px}.mat-standard-chip::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;opacity:0;content:\\\"\\\";pointer-events:none;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-standard-chip:hover::after{opacity:.12}.mat-standard-chip:focus{outline:none}.mat-standard-chip:focus::after{opacity:.16}.cdk-high-contrast-active .mat-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-standard-chip:focus{outline:dotted 2px}.cdk-high-contrast-active .mat-standard-chip.mat-chip-selected{outline-width:3px}.mat-standard-chip.mat-chip-disabled::after{opacity:0}.mat-standard-chip.mat-chip-disabled .mat-chip-remove,.mat-standard-chip.mat-chip-disabled .mat-chip-trailing-icon{cursor:default}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar,.mat-standard-chip.mat-chip-with-avatar{padding-top:0;padding-bottom:0}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-right:8px;padding-left:0}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-left:8px;padding-right:0}.mat-standard-chip.mat-chip-with-trailing-icon{padding-top:7px;padding-bottom:7px;padding-right:8px;padding-left:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon{padding-left:8px;padding-right:12px}.mat-standard-chip.mat-chip-with-avatar{padding-left:0;padding-right:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-avatar{padding-right:0;padding-left:12px}.mat-standard-chip .mat-chip-avatar{width:24px;height:24px;margin-right:8px;margin-left:4px}[dir=rtl] .mat-standard-chip .mat-chip-avatar{margin-left:8px;margin-right:4px}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{width:18px;height:18px;cursor:pointer}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-standard-chip .mat-chip-remove,[dir=rtl] .mat-standard-chip .mat-chip-trailing-icon{margin-right:8px;margin-left:0}.mat-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit;overflow:hidden;transform:translateZ(0)}.mat-chip-list-wrapper{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;margin:-4px}.mat-chip-list-wrapper input.mat-input-element,.mat-chip-list-wrapper .mat-standard-chip{margin:4px}.mat-chip-list-stacked .mat-chip-list-wrapper{flex-direction:column;align-items:flex-start}.mat-chip-list-stacked .mat-chip-list-wrapper .mat-standard-chip{width:100%}.mat-chip-avatar{border-radius:50%;justify-content:center;align-items:center;display:flex;overflow:hidden;object-fit:cover}input.mat-chip-input{width:150px;margin:4px;flex:1 0 150px}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-list', template: `<div class=\"mat-chip-list-wrapper\"><ng-content></ng-content></div>`, exportAs: 'matChipList', host: {\n                        '[attr.tabindex]': 'disabled ? null : _tabIndex',\n                        '[attr.aria-describedby]': '_ariaDescribedby || null',\n                        '[attr.aria-required]': 'role ? required : null',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[attr.aria-multiselectable]': 'multiple',\n                        '[attr.role]': 'role',\n                        '[class.mat-chip-list-disabled]': 'disabled',\n                        '[class.mat-chip-list-invalid]': 'errorState',\n                        '[class.mat-chip-list-required]': 'required',\n                        '[attr.aria-orientation]': 'ariaOrientation',\n                        'class': 'mat-chip-list',\n                        '(focus)': 'focus()',\n                        '(blur)': '_blur()',\n                        '(keydown)': '_keydown($event)',\n                        '[id]': '_uid',\n                    }, providers: [{ provide: MatFormFieldControl, useExisting: MatChipList }], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-chip{position:relative;box-sizing:border-box;-webkit-tap-highlight-color:transparent;border:none;-webkit-appearance:none;-moz-appearance:none}.mat-standard-chip{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);display:inline-flex;padding:7px 12px;border-radius:16px;align-items:center;cursor:default;min-height:32px;height:1px}._mat-animation-noopable.mat-standard-chip{transition:none;animation:none}.mat-standard-chip .mat-chip-remove{border:none;-webkit-appearance:none;-moz-appearance:none;padding:0;background:none}.mat-standard-chip .mat-chip-remove.mat-icon,.mat-standard-chip .mat-chip-remove .mat-icon{width:18px;height:18px;font-size:18px}.mat-standard-chip::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit;opacity:0;content:\\\"\\\";pointer-events:none;transition:opacity 200ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-standard-chip:hover::after{opacity:.12}.mat-standard-chip:focus{outline:none}.mat-standard-chip:focus::after{opacity:.16}.cdk-high-contrast-active .mat-standard-chip{outline:solid 1px}.cdk-high-contrast-active .mat-standard-chip:focus{outline:dotted 2px}.cdk-high-contrast-active .mat-standard-chip.mat-chip-selected{outline-width:3px}.mat-standard-chip.mat-chip-disabled::after{opacity:0}.mat-standard-chip.mat-chip-disabled .mat-chip-remove,.mat-standard-chip.mat-chip-disabled .mat-chip-trailing-icon{cursor:default}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar,.mat-standard-chip.mat-chip-with-avatar{padding-top:0;padding-bottom:0}.mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-right:8px;padding-left:0}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon.mat-chip-with-avatar{padding-left:8px;padding-right:0}.mat-standard-chip.mat-chip-with-trailing-icon{padding-top:7px;padding-bottom:7px;padding-right:8px;padding-left:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-trailing-icon{padding-left:8px;padding-right:12px}.mat-standard-chip.mat-chip-with-avatar{padding-left:0;padding-right:12px}[dir=rtl] .mat-standard-chip.mat-chip-with-avatar{padding-right:0;padding-left:12px}.mat-standard-chip .mat-chip-avatar{width:24px;height:24px;margin-right:8px;margin-left:4px}[dir=rtl] .mat-standard-chip .mat-chip-avatar{margin-left:8px;margin-right:4px}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{width:18px;height:18px;cursor:pointer}.mat-standard-chip .mat-chip-remove,.mat-standard-chip .mat-chip-trailing-icon{margin-left:8px;margin-right:0}[dir=rtl] .mat-standard-chip .mat-chip-remove,[dir=rtl] .mat-standard-chip .mat-chip-trailing-icon{margin-right:8px;margin-left:0}.mat-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit;overflow:hidden;transform:translateZ(0)}.mat-chip-list-wrapper{display:flex;flex-direction:row;flex-wrap:wrap;align-items:center;margin:-4px}.mat-chip-list-wrapper input.mat-input-element,.mat-chip-list-wrapper .mat-standard-chip{margin:4px}.mat-chip-list-stacked .mat-chip-list-wrapper{flex-direction:column;align-items:flex-start}.mat-chip-list-stacked .mat-chip-list-wrapper .mat-standard-chip{width:100%}.mat-chip-avatar{border-radius:50%;justify-content:center;align-items:center;display:flex;overflow:hidden;object-fit:cover}input.mat-chip-input{width:150px;margin:4px;flex:1 0 150px}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i2.NgForm, decorators: [{\n                    type: Optional\n                }] }, { type: i2.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i3.ErrorStateMatcher }, { type: i2.NgControl, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }] }]; }, propDecorators: { errorStateMatcher: [{\n                type: Input\n            }], multiple: [{\n                type: Input\n            }], compareWith: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], ariaOrientation: [{\n                type: Input,\n                args: ['aria-orientation']\n            }], selectable: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }], chips: [{\n                type: ContentChildren,\n                args: [MatChip, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Increasing integer for generating unique ids.\nlet nextUniqueId = 0;\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of an `<mat-chip-list>`.\n */\nclass MatChipInput {\n    constructor(_elementRef, _defaultOptions) {\n        this._elementRef = _elementRef;\n        this._defaultOptions = _defaultOptions;\n        /** Whether the control is focused. */\n        this.focused = false;\n        this._addOnBlur = false;\n        /**\n         * The list of key codes that will trigger a chipEnd event.\n         *\n         * Defaults to `[ENTER]`.\n         */\n        this.separatorKeyCodes = this._defaultOptions.separatorKeyCodes;\n        /** Emitted when a chip is to be added. */\n        this.chipEnd = new EventEmitter();\n        /** The input's placeholder text. */\n        this.placeholder = '';\n        /** Unique id for the input. */\n        this.id = `mat-chip-list-input-${nextUniqueId++}`;\n        this._disabled = false;\n        this.inputElement = this._elementRef.nativeElement;\n    }\n    /** Register input for chip list */\n    set chipList(value) {\n        if (value) {\n            this._chipList = value;\n            this._chipList.registerInput(this);\n        }\n    }\n    /**\n     * Whether or not the chipEnd event will be emitted when the input is blurred.\n     */\n    get addOnBlur() {\n        return this._addOnBlur;\n    }\n    set addOnBlur(value) {\n        this._addOnBlur = coerceBooleanProperty(value);\n    }\n    /** Whether the input is disabled. */\n    get disabled() {\n        return this._disabled || (this._chipList && this._chipList.disabled);\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    /** Whether the input is empty. */\n    get empty() {\n        return !this.inputElement.value;\n    }\n    ngOnChanges() {\n        this._chipList.stateChanges.next();\n    }\n    ngOnDestroy() {\n        this.chipEnd.complete();\n    }\n    ngAfterContentInit() {\n        this._focusLastChipOnBackspace = this.empty;\n    }\n    /** Utility method to make host definition/tests more clear. */\n    _keydown(event) {\n        if (event) {\n            // Allow the user's focus to escape when they're tabbing forward. Note that we don't\n            // want to do this when going backwards, because focus should go back to the first chip.\n            if (event.keyCode === TAB && !hasModifierKey(event, 'shiftKey')) {\n                this._chipList._allowFocusEscape();\n            }\n            // To prevent the user from accidentally deleting chips when pressing BACKSPACE continuously,\n            // We focus the last chip on backspace only after the user has released the backspace button,\n            // and the input is empty (see behaviour in _keyup)\n            if (event.keyCode === BACKSPACE && this._focusLastChipOnBackspace) {\n                this._chipList._keyManager.setLastItemActive();\n                event.preventDefault();\n                return;\n            }\n            else {\n                this._focusLastChipOnBackspace = false;\n            }\n        }\n        this._emitChipEnd(event);\n    }\n    /**\n     * Pass events to the keyboard manager. Available here for tests.\n     */\n    _keyup(event) {\n        // Allow user to move focus to chips next time he presses backspace\n        if (!this._focusLastChipOnBackspace && event.keyCode === BACKSPACE && this.empty) {\n            this._focusLastChipOnBackspace = true;\n            event.preventDefault();\n        }\n    }\n    /** Checks to see if the blur should emit the (chipEnd) event. */\n    _blur() {\n        if (this.addOnBlur) {\n            this._emitChipEnd();\n        }\n        this.focused = false;\n        // Blur the chip list if it is not focused\n        if (!this._chipList.focused) {\n            this._chipList._blur();\n        }\n        this._chipList.stateChanges.next();\n    }\n    _focus() {\n        this.focused = true;\n        this._focusLastChipOnBackspace = this.empty;\n        this._chipList.stateChanges.next();\n    }\n    /** Checks to see if the (chipEnd) event needs to be emitted. */\n    _emitChipEnd(event) {\n        if (!this.inputElement.value && !!event) {\n            this._chipList._keydown(event);\n        }\n        if (!event || this._isSeparatorKey(event)) {\n            this.chipEnd.emit({\n                input: this.inputElement,\n                value: this.inputElement.value,\n                chipInput: this,\n            });\n            event?.preventDefault();\n        }\n    }\n    _onInput() {\n        // Let chip list know whenever the value changes.\n        this._chipList.stateChanges.next();\n    }\n    /** Focuses the input. */\n    focus(options) {\n        this.inputElement.focus(options);\n    }\n    /** Clears the input */\n    clear() {\n        this.inputElement.value = '';\n        this._focusLastChipOnBackspace = true;\n    }\n    /** Checks whether a keycode is one of the configured separators. */\n    _isSeparatorKey(event) {\n        return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n    }\n}\nMatChipInput.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipInput, deps: [{ token: i0.ElementRef }, { token: MAT_CHIPS_DEFAULT_OPTIONS }], target: i0.ɵɵFactoryTarget.Directive });\nMatChipInput.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: MatChipInput, selector: \"input[matChipInputFor]\", inputs: { chipList: [\"matChipInputFor\", \"chipList\"], addOnBlur: [\"matChipInputAddOnBlur\", \"addOnBlur\"], separatorKeyCodes: [\"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"], placeholder: \"placeholder\", id: \"id\", disabled: \"disabled\" }, outputs: { chipEnd: \"matChipInputTokenEnd\" }, host: { listeners: { \"keydown\": \"_keydown($event)\", \"keyup\": \"_keyup($event)\", \"blur\": \"_blur()\", \"focus\": \"_focus()\", \"input\": \"_onInput()\" }, properties: { \"id\": \"id\", \"attr.disabled\": \"disabled || null\", \"attr.placeholder\": \"placeholder || null\", \"attr.aria-invalid\": \"_chipList && _chipList.ngControl ? _chipList.ngControl.invalid : null\", \"attr.aria-required\": \"_chipList && _chipList.required || null\" }, classAttribute: \"mat-chip-input mat-input-element\" }, exportAs: [\"matChipInput\", \"matChipInputFor\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[matChipInputFor]',\n                    exportAs: 'matChipInput, matChipInputFor',\n                    host: {\n                        'class': 'mat-chip-input mat-input-element',\n                        '(keydown)': '_keydown($event)',\n                        '(keyup)': '_keyup($event)',\n                        '(blur)': '_blur()',\n                        '(focus)': '_focus()',\n                        '(input)': '_onInput()',\n                        '[id]': 'id',\n                        '[attr.disabled]': 'disabled || null',\n                        '[attr.placeholder]': 'placeholder || null',\n                        '[attr.aria-invalid]': '_chipList && _chipList.ngControl ? _chipList.ngControl.invalid : null',\n                        '[attr.aria-required]': '_chipList && _chipList.required || null',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_CHIPS_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { chipList: [{\n                type: Input,\n                args: ['matChipInputFor']\n            }], addOnBlur: [{\n                type: Input,\n                args: ['matChipInputAddOnBlur']\n            }], separatorKeyCodes: [{\n                type: Input,\n                args: ['matChipInputSeparatorKeyCodes']\n            }], chipEnd: [{\n                type: Output,\n                args: ['matChipInputTokenEnd']\n            }], placeholder: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst CHIP_DECLARATIONS = [\n    MatChipList,\n    MatChip,\n    MatChipInput,\n    MatChipRemove,\n    MatChipAvatar,\n    MatChipTrailingIcon,\n];\nclass MatChipsModule {\n}\nMatChipsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMatChipsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipsModule, declarations: [MatChipList,\n        MatChip,\n        MatChipInput,\n        MatChipRemove,\n        MatChipAvatar,\n        MatChipTrailingIcon], imports: [MatCommonModule], exports: [MatChipList,\n        MatChip,\n        MatChipInput,\n        MatChipRemove,\n        MatChipAvatar,\n        MatChipTrailingIcon] });\nMatChipsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipsModule, providers: [\n        ErrorStateMatcher,\n        {\n            provide: MAT_CHIPS_DEFAULT_OPTIONS,\n            useValue: {\n                separatorKeyCodes: [ENTER],\n            },\n        },\n    ], imports: [[MatCommonModule]] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: MatChipsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule],\n                    exports: CHIP_DECLARATIONS,\n                    declarations: CHIP_DECLARATIONS,\n                    providers: [\n                        ErrorStateMatcher,\n                        {\n                            provide: MAT_CHIPS_DEFAULT_OPTIONS,\n                            useValue: {\n                                separatorKeyCodes: [ENTER],\n                            },\n                        },\n                    ],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipInput, MatChipList, MatChipListChange, MatChipRemove, MatChipSelectionChange, MatChipTrailingIcon, MatChipsModule };\n"]}, "metadata": {}, "sourceType": "module"}
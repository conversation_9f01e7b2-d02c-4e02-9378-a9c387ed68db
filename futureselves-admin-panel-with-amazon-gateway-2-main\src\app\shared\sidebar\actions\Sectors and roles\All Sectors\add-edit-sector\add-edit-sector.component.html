<app-sidebar>
    <div class="content-wrapper">

        <div class="row"> <!--Add new Grad scheme form -->
            <div class="col-12 grid-margin stretch-card">
                <div class="card">
                    <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-0">{{title}}
                        Sector</div>
                    <div class="card-body">
                        <form class="forms-sample" [formGroup]="addSectorForm" (ngSubmit)="insertSectorData()">

                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label for="IN_name" class="required-field">Sector Name</label>
                                    <input type="text" class="form-control form-control-sm"
                                        formControlName="IN_name" required [readonly]="isReadonly" placeholder="Enter Sector Name" />
                                    <div class="warning" *ngIf="addSectorForm.get('IN_name')?.errors&&addSectorForm.get('IN_name')?.errors?.wordLimitExceeded">
                                        Error: Word Limit Exceeded! (20 Words)
                                    </div>
                                </div>

                                <div class="form-group col-lg-6">
                                    <label for="IN_dp" class="required-field">Sector Image</label>
                                    <div class="logo-input-container">
                                        <input type="file" class="form-control form-control-sm"
                                            formControlName="IN_dp" (change)="onFileSelected($event);" required
                                            [readonly]="isReadonly" accept="image/*">
                                        <img *ngIf="imageSrc" [src]="imageSrc" alt="Sector Image" class="img-preview">
                                    </div>
                                    <div *ngIf="this.addSectorForm.get('IN_dp')?.errors?.fileSizeValidator" class="warning">
                                        The file size exceeds the 2 MB limit. Please select a smaller file.
                                    </div> 
                                </div>
                            </div>

                            <div class="row">
                                <div class="form-group col-lg-6">
                                    <label for="IN_description" class="required-field">Sector Description</label>
                                    <textarea class="form-control form-control-sm" formControlName="IN_description"
                                        required [readonly]="isReadonly" rows="5" cols="50" placeholder="Enter Sector Description"></textarea>
                                    <div class="warning" *ngIf="addSectorForm.get('IN_description')?.errors && addSectorForm.get('IN_description')?.errors?.wordLimitExceeded">
                                        Error: Word Limit exceeded!(200 Words)
                                    </div>
                                </div>

                                <div *ngIf="title=='Add New'" class="form-group col-lg-6">
                                    <label for="IN_createdBy" class="required-field">Creator Name</label>
                                    <input type="text" class="form-control form-control-sm"
                                        formControlName="IN_createdBy" required [readonly]="isReadonly" placeholder="Enter Creator Name">
                                    
                                </div>
                                
                            </div>
                                          <div class="text-center">
                                <button *ngIf="!isReadonly" type="submit" class="btn btn-primary mr-2">Save</button>
                                <button class="btn btn-light" routerLink="/actions/sectors">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</app-sidebar>
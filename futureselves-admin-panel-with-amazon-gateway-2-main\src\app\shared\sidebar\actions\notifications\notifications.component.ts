import { Component, OnInit } from '@angular/core';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss']
})
export class NotificationsComponent implements OnInit {
  imageSrc: string | ArrayBuffer | null;
  addNotificationForm: FormGroup;
  p: number = 1;
  imageName: any;
  showForm = false;
  term: string;
  title = 'Add New';
  isReadonly = false;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  selectedRecord: any;
  sectorId: any;
  sectorData: any;
  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private toastr: ToastrService,
  ) {

    this.addNotificationForm=this.formBuilder.group({
      UN_notificationType: [''],
      UN_title: ['',Validators.required],
      UN_text: ['',Validators.required],

    });

   }

  ngOnInit(): void {
   this.addNotificationForm.get('UN_notificationType')?.disable()
  }

  generateNotification(){
    if(this.addNotificationForm.invalid){
      this.toastr.info("Please fill all required fields.");
    }
    else{

    const postData = {
      UN_notificationType:"Broadcast",
      UN_title: this.addNotificationForm.get('UN_title')?.value,
      UN_text: this.addNotificationForm.get('UN_text')?.value
    };

    this.ngxSpinnerService.show('globalSpinner');
    console.log("Post data to generate notification : ",postData);
    this.dataTransferService.generateNotification(postData).subscribe((res: any) => {
      
      if(res.statusCode == 200){
        this.ngxSpinnerService.hide('globalSpinner');
        this.toastr.success("Notifications have been generated successfully.");
        this.clearForm();
      } else {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error:', res.error);
        this.toastr.error('Error: ', res.error);
      }
    }, (error) => {
      this.ngxSpinnerService.hide('globalSpinner');
      console.error('Request failed:', error);
      this.toastr.error('An unexpected error occurred. Please try again later.');
    });

  }

  }
  
  clearForm(){
      this.addNotificationForm.get('UN_title')?.reset();
      this.addNotificationForm.get('UN_text')?.reset();
  }

}

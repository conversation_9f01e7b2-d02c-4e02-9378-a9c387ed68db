{"ast": null, "code": "import { Observable } from '../Observable';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleAsyncIterable(input, scheduler) {\n  if (!input) {\n    throw new Error('Iterable cannot be null');\n  }\n\n  return new Observable(subscriber => {\n    executeSchedule(subscriber, scheduler, () => {\n      const iterator = input[Symbol.asyncIterator]();\n      executeSchedule(subscriber, scheduler, () => {\n        iterator.next().then(result => {\n          if (result.done) {\n            subscriber.complete();\n          } else {\n            subscriber.next(result.value);\n          }\n        });\n      }, 0, true);\n    });\n  });\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/scheduled/scheduleAsyncIterable.js"], "names": ["Observable", "executeSchedule", "scheduleAsyncIterable", "input", "scheduler", "Error", "subscriber", "iterator", "Symbol", "asyncIterator", "next", "then", "result", "done", "complete", "value"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,SAASC,qBAAT,CAA+BC,KAA/B,EAAsCC,SAAtC,EAAiD;AACpD,MAAI,CAACD,KAAL,EAAY;AACR,UAAM,IAAIE,KAAJ,CAAU,yBAAV,CAAN;AACH;;AACD,SAAO,IAAIL,UAAJ,CAAgBM,UAAD,IAAgB;AAClCL,IAAAA,eAAe,CAACK,UAAD,EAAaF,SAAb,EAAwB,MAAM;AACzC,YAAMG,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAACC,aAAR,CAAL,EAAjB;AACAR,MAAAA,eAAe,CAACK,UAAD,EAAaF,SAAb,EAAwB,MAAM;AACzCG,QAAAA,QAAQ,CAACG,IAAT,GAAgBC,IAAhB,CAAsBC,MAAD,IAAY;AAC7B,cAAIA,MAAM,CAACC,IAAX,EAAiB;AACbP,YAAAA,UAAU,CAACQ,QAAX;AACH,WAFD,MAGK;AACDR,YAAAA,UAAU,CAACI,IAAX,CAAgBE,MAAM,CAACG,KAAvB;AACH;AACJ,SAPD;AAQH,OATc,EASZ,CATY,EAST,IATS,CAAf;AAUH,KAZc,CAAf;AAaH,GAdM,CAAP;AAeH", "sourcesContent": ["import { Observable } from '../Observable';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleAsyncIterable(input, scheduler) {\n    if (!input) {\n        throw new Error('Iterable cannot be null');\n    }\n    return new Observable((subscriber) => {\n        executeSchedule(subscriber, scheduler, () => {\n            const iterator = input[Symbol.asyncIterator]();\n            executeSchedule(subscriber, scheduler, () => {\n                iterator.next().then((result) => {\n                    if (result.done) {\n                        subscriber.complete();\n                    }\n                    else {\n                        subscriber.next(result.value);\n                    }\n                });\n            }, 0, true);\n        });\n    });\n}\n"]}, "metadata": {}, "sourceType": "module"}
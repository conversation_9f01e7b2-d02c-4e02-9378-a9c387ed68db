import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  Validators,
} from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, ActivatedRoute } from '@angular/router';
import { Constants } from 'src/app/config/constants';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
@Component({
  selector: 'app-user-status',
  templateUrl: './user-status.component.html',
  styleUrls: ['./user-status.component.scss'],
})
export class UserStatusComponent implements OnInit {
  userForm: FormGroup;
  title: any;
  userId: any;
  isReadonly: boolean = true;
  likedActivities: string[] = [];
  dislikedActivities: string[] = [];
  doNotWantToWork: string[] = [];
  minimumSalaryExpected: string[] = [];
  LikedSector: string[] = [];
  userName: any;
  userEmail: any;
  registrationSource: any;
  createdDate: any;
  activeStatus: any;
  userData: any;

  constructor(
    private fb: FormBuilder,
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private domSanitizer: DomSanitizer,
    private router: Router,
    private formModule: FormsModule,
    private http: HttpClient,
    private ngxSpinnerService: NgxSpinnerService,
    private route: ActivatedRoute
  ) {
    this.userForm = this.fb.group({
      U_institute: [''],
      U_education: [''],
      U_postcode: [''],
      U_sexualOrientation: [''],
      U_freeMeal: [''],
      U_first_generation: [''],
      U_isDisability: [''],
      U_gender: [''],
      U_ethnicity: [''],
      U_religionTitle: [''],
      // Add more form controls if needed
    });

    // this.route.queryParams.subscribe((params) => {
    //   this.title = params['title'];
    //   this.userId = params['userId'];
    // });

       const state = this.router.getCurrentNavigation()?.extras.state;
      if (state?.userData) {
        console.log('App user data state : ', state);
        this.userData = state.userData;
        this.userName = this.userData.U_name;
        this.userEmail = this.userData.U_email;
        this.registrationSource = this.userData.U_registertypeId;
        this.userId = this.userData.U_id;
        this.activeStatus = this.userData.U_activeStatus;
        const createdDate = this.userData?.U_createdAt;
        if (createdDate) {
          const dateObject = new Date(createdDate);
          const options: Intl.DateTimeFormatOptions = {
            month: 'long',
            day: 'numeric',
            year: 'numeric',
          };
          this.createdDate = dateObject.toLocaleDateString('en-US', options);
        } else {
          this.createdDate = 'Not Available';
        }
      } else {
      this.router.navigate(['/actions/app-users']);
    }
  }

  ngOnInit(): void {
    // this.userId
    //   ? (this.userId = this.userId)
    //   : this.router.navigate([`/actions/app-users`]);
    // this.getAllUserIdentity(this.userId);
    // this.getAllUserReferences(this.userId);
    // this.getLikedSectors(this.userId);
    
  }

  // getAllUserIdentity(userId: any) {
  //   this.ngxSpinnerService.show('globalSpinner');
  //   this.dataTransferService.getAllUserPreferences(userId).subscribe(
  //     (res: any) => {
  //       if (res.statusCode == 200) {
  //         this.ngxSpinnerService.hide('globalSpinner');
  //         const data = res.data.user;
  //         console.log('Res :', res);

  //         Object.keys(data).forEach((key) => {
  //           if (data[key] === null) {
  //             data[key] = '';
  //           }
  //         });

  //         if (data.U_postcode) {
  //           this.getCountryNameFromPostcode(data.U_postcode);
  //           console.log('Postcode : ', data.U_postcode);
  //         }

  //         // Transform U_isDisability value
  //         if (data.U_isDisability == 1) {
  //           data.U_isDisability = 'Yes';
  //         } else if (data.U_isDisability == 2) {
  //           data.U_isDisability = 'No';
  //         } else if (data.U_isDisability == 3) {
  //           data.U_isDisability = 'Prefer Not To say';
  //         } else if (data.U_isDisability == 4) {
  //           data.U_isDisability = 'I Am Not Sure';
  //         }

  //         if (data.U_freeMeal == 1) {
  //           data.U_freeMeal = 'Yes';
  //         } else if (data.U_freeMeal == 2) {
  //           data.U_freeMeal = 'No';
  //         } else if (data.U_freeMeal == 3) {
  //           data.U_freeMeal = 'Prefer Not To Say';
  //         } else if (data.U_freeMeal == 4) {
  //           data.U_freeMeal = 'I Am Not Sure';
  //         }

  //         if (data.U_first_generation == 1) {
  //           data.U_first_generation = 'Yes';
  //         } else if (data.U_first_generation == 2) {
  //           data.U_first_generation = 'No';
  //         } else if (data.U_first_generation == 3) {
  //           data.U_first_generation = 'Prefer Not To Say';
  //         } else if (data.U_first_generation == 4) {
  //           data.U_first_generation = 'I Am Not Sure';
  //         }

  //         this.userForm.patchValue(data);
  //         console.log('User identity data : ', data);
  //       } else {
  //         this.ngxSpinnerService.hide('globalSpinner');
  //         this.toastr.error('Unable to get user identity data');
  //         this.router.navigate([`/actions/app-users`]);
  //       }
  //     },
  //     (error: any) => {
  //       this.toastr.error('Unable to get user identity data');
  //       this.ngxSpinnerService.hide('globalSpinner');
  //       console.error('Unable to get user identity data:', error);
  //     }
  //   );
  // }

  // Function to populate the tag arrays
  // populateTags(data: any[]) {
  //   data.forEach((item) => {
  //     switch (item.quiz_id) {
  //       case '5':
  //         this.likedActivities.push(item.option_title);
  //         break;
  //       case '1':
  //         this.dislikedActivities.push(item.option_title);
  //         break;
  //       case '2':
  //         this.doNotWantToWork.push(item.option_title);
  //         break;
  //       case '3':
  //         this.minimumSalaryExpected.push(item.option_title);
  //         break;
  //     }
  //   });
  // }

  // Call populateTags inside getAllUserReferences after fetching data
  // getAllUserReferences(userId: any) {
  //   this.ngxSpinnerService.show('globalSpinner');
  //   this.dataTransferService.getAllUserPreferences(userId).subscribe(
  //     (res: any) => {
  //       if (res.statusCode == 200) {
  //         this.ngxSpinnerService.hide('globalSpinner');
  //         const data = res.data.quizAnswers[0].QUA_quiz_options;
  //         console.log('References :', data);
  //         this.populateTags(data);
  //       } else {
  //         this.ngxSpinnerService.hide('globalSpinner');
  //         this.toastr.error('Unable to get user {{title}}');
  //         this.router.navigate([`/actions/app-users`]);
  //       }
  //     },
  //     (error: any) => {
  //       this.toastr.error('Unable to get user references data');
  //       this.ngxSpinnerService.hide('globalSpinner');
  //       console.error('Unable to get user references data:', error);
  //     }
  //   );
  // }

  // getLikedSectors(LI_userId: any) {
  //   this.dataTransferService.getLikedSectors(LI_userId).subscribe({
  //     next: (res: any) => {
  //       if (res.statusCode == 200) {
  //         const industries = res.industry;
  //         // Reset LikedSector array
  //         this.LikedSector = [];
  //         // Populate LikedSector array with industry names
  //         industries?.forEach((industry: any) => {
  //           this.LikedSector.push(industry.name);
  //         });
  //         console.log('Liked Industry :', this.LikedSector);
  //       } else {
  //         console.log('Unable to fetch liked sector data...');
  //       }
  //     },
  //   });
  // }

  updateActiveStatus(status: any) {
    const data = {
      userId: this.userId,
      activeStatus: status,
    };
    console.log('Data to update user status : ', data);

    this.dataTransferService.updateActiveStatus(data).subscribe({
      next: (res: any) => {
        if (res.status == 200) {
          this.toastr.success('User status updated.');
          this.ngxSpinnerService.hide('globalSpinner');
          this.dataTransferService.getAllAppUsers();
          this.router.navigate(['/actions/app-users']);
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          this.toastr.error('Unable to update user status');
        }
      },
      error: (error: any) => {
        console.log('Error Message', error);
        this.ngxSpinnerService.hide('globalSpinner');
        this.toastr.error('Unable to update user status');
      },
    });
  }

  getCountryNameFromPostcode(postcode: string) {
    this.http
      .get(`https://api.postcodes.io/postcodes/${postcode}`)
      .subscribe((postcodeRes: any) => {
        if (postcodeRes.status === 200) {
          const countryName = postcodeRes.result.country;
          const adminDistrict = postcodeRes.result.admin_district;

          // Concatenate country name and admin district
          const concatenatedValue = `${adminDistrict} - ${countryName}`;

          // Patch the concatenated value to the U_postcode field
          this.userForm.patchValue({ U_postcode: concatenatedValue });
        } else {
          // Handle error
          console.error('Error fetching country name from postcode API');
        }
      });
  }
}

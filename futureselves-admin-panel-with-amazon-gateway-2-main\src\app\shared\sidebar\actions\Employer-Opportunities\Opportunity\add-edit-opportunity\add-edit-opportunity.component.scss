
  .footer{
  background-color: white;
  }
  .head-exst{
    width: 100vh;
  }

  .readOnlyColor{
    color: #495057;
  }

  .warning{
    color: rgb(238, 12, 12);
    font-size: smaller ;
    margin-top: 4px;
  }
  
  .info{
    color: rgba(88, 87, 87, 0.881);
    font-size: smaller ;
    margin-top: 4px;
  }
  
 .fa-plus{
  font-size: small;
 }

 .subtitle{
  color: rgba(81, 80, 80, 0.856) !important;
 }

 .radio-button-label {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
.radio-button-label input[type="radio"] {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}
.radio-button-group {
  display: flex;
  align-items: center;
}
.character-count{
  font-size: smaller;
  margin-top: 2px;
  display: flex;
  justify-content: flex-end;
}

 
.fa-plus{
  font-size: small;
 }

 .subtitle{
  color: rgba(81, 80, 80, 0.856) !important;
 }


 .suggestion{
  font-size: 12px;
 }

 @media screen and (min-width: 992px) and (max-width: 1224px) {
  .insight-btn{
    padding: 4px 5px;
  }

  .fas{
    font-size: small;
  }
}

@media screen and (min-width: 992px) and (max-width: 1224px) {
  .insight-btn{
    padding: 4px 5px;
  }

  .fas{
    font-size: small;
  }
}

@media screen and (min-width: 320px) and (max-width: 768px) {
.btns{
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.add-insight-btn{
  margin-bottom: 20px;
}
}


import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {  ActivatedRoute, Router} from '@angular/router';
import { DatePipe } from '@angular/common';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import {  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
  AbstractControl,
} from '@angular/forms';

import { NgxSpinnerService } from 'ngx-spinner';
import { ViewChild, ElementRef } from '@angular/core';

// function wordLimitValidator(maxWords: number) {
//   return (control: AbstractControl): { [key: string]: any } | null => {
//     if (control.value) {
//       const words = control.value.trim().split(/\s+/);
//       if (words.length > maxWords) {
//         console.log('maxwords', maxWords);
//         return { wordLimitExceeded: true, wordCount: words.length };
//       }
//     }
//     return null;
//   };
// }

// function nonNegativeValidator(
//   control: FormControl
// ): { [key: string]: boolean } | null {
//   // console.log('Control : ', control);
//   const value = control.value;
//   if (value < 0 || value > 24) {
//     return { negativeValue: true };
//   }
//   return null;
// }

@Component({
  selector: 'app-opportunities',
  templateUrl: './opportunities.component.html',
  styleUrls: ['./opportunities.component.scss'],
})
export class OpportunitiesComponent implements OnInit {
  // @ViewChild('audioInput') audioInput!: ElementRef;
  // jobForm: FormGroup;
  // imageSrc: string | ArrayBuffer | null;
  // addNewCompanyForm: FormGroup;
  // ExistingOpportunitiessForm: FormGroup;
  p: number = 1;
  // user: any;
  // imageName: any;
  // CompanyList: any;
  // SectorList: any;
  CO_id: any;
  // hideHeader = false;
  viewInsight = true;
  // showForm = false;
  // queryParam: any;
  term: string;
  // submitted = false;
  title = 'Add New';
  isReadonly = false;
  // fileName: File | undefined;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  // imagedp: string | null = null; // Assuming this is used to display the uploaded image
  // baseimage:any = 'https://voxpod.s3.eu-west-2.amazonaws.com/2f1010ae38-7e24-4fe6-a771-c5363510f7c65_dp.png ';
  // selectedRecord: any;
  // title1: string;
  ExistingOpportunities: any;
  companyId: any;
  // minDate: string;
  sectorID: any;
  companyLogo: any;
  // RoleList: any;
  // roleId: Promise<unknown>;
  // minStartDate: string;
  // invalidDates: Set<string> = new Set<string>();
  // JobList: any;
  // checkMinDate: string;
  // scheme: any;
  // selectedAudioFile: any;
  deleteId: any;
  // toEditInsight: any;
  // selectedType: string = 'job';  // Default value is 'job'
  // rolesArray: any;
  descriptionCharCount: number = 0;
  requirementsCharCount: number = 0;
  hrtTipsCharCount: number = 0;
  Mainstate: any;
  employerData: any;
  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private toastr: ToastrService,
    private datePipe: DatePipe,
    private route: ActivatedRoute

  ) {
    
    this.route.queryParams.subscribe(params => {
      if(params){
      this.CO_id = params['CO_id'];
      console.log(this.CO_id);
    }else{
      this.router.navigate(['/actions/employer-opportunities']);
    }
    });

    // this.minDate = new Date().toISOString().split('T')[0];
    // this.checkMinDate = this.minDate;

    // this.jobForm = this.formBuilder.group(
    //   //Form to add new scheme
    //   {
    //     JB_companyId: [''],
    //     JB_companyLogo: [''],
    //     JB_applicationDeadline: ['', [Validators.required]],
    //     JB_startDate: ['', [Validators.required]],
    //     JB_jobTitle: ['', [Validators.required, wordLimitValidator(10)]],
    //     JB_description: ['', [Validators.required, this.characterLimitValidator(200)]],
    //     JB_department: ['', [Validators.required, wordLimitValidator(10)]],
    //     JB_hours: ['', [Validators.required, nonNegativeValidator]],
    //     JB_hrtTips: ['', [Validators.required, this.characterLimitValidator(200)]],
    //     JB_roleId: [{ value: '', disabled: this.selectedType !== 'job' },[Validators.required]],
    //     JB_location: ['', [Validators.required, wordLimitValidator(10)]],
    //     JB_salary: ['', [Validators.required]],
    //     JB_modeOfWork: ['', [Validators.required, wordLimitValidator(10)]],
    //     JB_requirements: ['', [Validators.required, this.characterLimitValidator(200)]],
    //     JB_applyLink: ['', [Validators.required]],
    //     JB_type: ['', Validators.required],
    //     JB_sectorId:['']
    //   }
    // );
    // this.jobForm.get('JB_sectorId')?.valueChanges.subscribe(value => {
    //   if (this.selectedType === 'job') {
    //     if (value) {
    //       this.jobForm.get('JB_roleId')?.enable();
    //     } else {
    //       this.jobForm.get('JB_roleId')?.disable();
    //     }
    //   }
    // });

    // this.addNewCompanyForm = this.formBuilder.group({
    //   CO_companyName: ['', [Validators.required]],
    //   CO_logo: ['', [Validators.required]],
    //   CO_location: ['', [Validators.required]],
    //   CO_type: ['', [Validators.required]],
    //   CO_founded: ['', [Validators.required]],
    //   CO_sectorId: ['', [Validators.required]],
    //   CO_about: ['', [Validators.required, wordLimitValidator(100)]],
    //   CO_website: ['', [Validators.required]],
    //   CO_size: ['', [Validators.required]],
    //   CO_HrInsights:  this.formBuilder.array([this.createInsight()])
    // });
  }

  ngOnInit(): void {
    this.getAllSchemeByCompanyId(this.CO_id);
    this.getCompanyById(this.CO_id)
    // this.getSectorTitles();
    // this.getAllRoleBySectorId(this.sectorID);
    // this.minStartDate = this.minDate;
  }

  // characterLimitValidator(limit: number) {
  //   return (control: any) => {
  //     if (control.value && control.value.length > limit) {
  //       return { characterLimitExceeded: true };
  //     }
  //     return null;
  //   };
  // }

  // onInput(field: string) {
  //   const control = this.jobForm.get(field);
  //   if (control) {
  //     const value = control.value || '';
  //     switch (field) {
  //       case 'JB_description':
  //         this.descriptionCharCount = value.length;
  //         break;
  //       case 'JB_requirements':
  //         this.requirementsCharCount = value.length;
  //         break;
  //       case 'JB_hrtTips':
  //         this.hrtTipsCharCount = value.length;
  //         break;
  //     }
  //     control.updateValueAndValidity(); // Trigger validation
  //   }
  // }

  // get insightFormArray(): FormArray {
  //   return this.addNewCompanyForm.get('CO_HrInsights') as FormArray;
  // }

  // createInsight(): FormGroup {
  //   return this.formBuilder.group({
  //     title: ['', [Validators.required]],
  //     Name: ['', [Validators.required]],
  //     Position: ['', [Validators.required]],
  //     Insight_Link: ['', [Validators.required]]
  //   });
  // }

  // addInsight() {
  //   this.insightFormArray.push(this.createInsight());
  // }

  // removeInsight(index: number): void {
  //   this.insightFormArray.removeAt(index);
  // }

  // onTypeChange(value: string): void {
  //   this.selectedType = value;
  //   this.jobForm.get('JB_sectorId')?.reset();
  //   this.jobForm.reset();

  //   if (this.selectedType === 'job') {
  //     this.jobForm.get('JB_roleId')?.reset({ value: '', disabled: true });
  //   } else {
  //     this.jobForm.get('JB_roleId')?.enable();
  //   }
  // }

  // onChangeIndustry(event: any) {
  //   const CO_sectorId = event.target.value;
  //   this.jobForm.get('UI_roleId')?.enable();
  //   this.getAllRole(CO_sectorId);
  // }

  // getAllRole(CO_sectorId:any){
  //   console.log("sectorId : ",CO_sectorId);
  //   this.dataTransferService.getAllRoleBySectorId(CO_sectorId).subscribe({
  //     next: (res: any) => {
  //       if (res.statusCode === 200) {
  //         this.rolesArray = res.data;
  //         console.log("RoleList : ",this.rolesArray);
  //       } else {
  //         console.error('Failed to fetch role. Status:', res.status);
  //       }
  //     },
  //     error: (error: any) => {
  //       this.ngxSpinnerService.hide('globalSpinner');
  //       console.error('Error occurred while fetching roles:', error);
  //     },
  //   });
  // }

  // updateStartDateMin(): void {
  //   const deadline = this.jobForm.get('JB_applicationDeadline')?.value;
  //   if (deadline) {
  //     const minStartDate = new Date(deadline);
  //     minStartDate.setDate(minStartDate.getDate() + 1); // Add one day to the deadline
  //     this.minStartDate = minStartDate.toISOString().split('T')[0];

  //     // After updating minimum start date, check the validity of the start date
  //     this.checkInvalidDate('JB_startDate');
  //   }
  // }

  // checkInvalidDate(controlName: string): void {
  //   const control = this.jobForm.get(controlName);
  //   if (control && control.value && isNaN(Date.parse(control.value))) {
  //     this.invalidDates.add(controlName);
  //   } else {
  //     this.invalidDates.delete(controlName);
  //   }
  // }



  // addSchema() {
  //   const startDateControl = this.jobForm.get('JB_startDate');
  //   if (startDateControl) {
  //     const startDateValue = startDateControl.value;
  //     const deadlineValue = this.jobForm.get('JB_applicationDeadline')?.value;
  //     if (startDateValue && deadlineValue) {
  //       const startDate = new Date(startDateValue);
  //       const deadline = new Date(deadlineValue);
  //       if (startDate >= deadline) {
  //         if (deadlineValue < this.checkMinDate) {
  //           // Application deadline is in the past
  //           this.toastr.error(
  //             'Application deadline must be today or a future date.'
  //           );
  //           return;
  //         }
  //         if (this.jobForm.valid) {
          
  //           console.log('ADD SCHEME DATA : ', this.jobForm.value);
  //           this.ngxSpinnerService.show('globalSpinner');
  //           this.dataTransferService
  //             .addSchema(this.jobForm.value)
  //             .subscribe((res: any) => {
  //               console.log('addSchema', res);
  //               if (res.statusCode == 200) {
  //                 this.ngxSpinnerService.hide('globalSpinner');
  //                 this.toastr.success('', 'Grad Scheme Saved. ');
  //                 this.onReset();
  //                 this.showForm = false;
  //                 this.getAllSchemeByCompanyId(this.companyId);
  //                 this.showExistingOpportunities();
  //               } else {
  //                 this.ngxSpinnerService.hide('globalSpinner');
  //                 this.toastr.error('', 'Something Went Wrong');
  //               }
  //             });
  //         } else {
  //           this.toastr.info('Please fill all required fields and ensure they are filled correctly.');
  //           console.log('Invalid form submission!');
  //           this.ngxSpinnerService.hide('globalSpinner');
  //         }
  //       } else {
  //         this.toastr.error(
  //           'Start date must be after the application deadline.'
  //         ); // Adjusted error message
  //         console.log('Invalid form submission!');
  //       }
  //     }
  //   }
  // }

  // onReset() {
  //   this.submitted = false;
  //   this.jobForm.reset();
  //   // this.addNewCompanyForm.reset();
  //   // this.setCheckBoxDefault();
  // }

  // filteredSchemeList: any = [];
  // filterScheme() {
  //   // Search Scheme
  //   if (!this.term.trim()) {
  //     this.filteredSchemeList = this.ExistingOpportunities;
  //   } else {
  //     this.filteredSchemeList = this.ExistingOpportunities.filter((scheme: any) =>
  //       scheme.JB_jobTitle?.toLowerCase().includes(this.term.toLowerCase())
  //     );
  //   }
  //   console.log('filteredSchemeList : ', this.filteredSchemeList);
  //   return this.filteredSchemeList;
  // }
 
  getCompanyById(companyId:any){
    this.dataTransferService.getCompanyById(companyId).subscribe((res:any)=>{
      this.companyLogo=res.CO_logo;
      this.sectorID=res.CO_sectorId;
      console.log('getCompanyById',res);
    },(error:any)=>{
     console.log("Error",error);
    //  this.toastr.error("Unable to fetch data");
    });
  }

  addRecord() {
    const state={
    viewInsight : true,
    title:'Add'
  }
    this.router.navigate([`/actions/employer-opportunities/existing-opportunities/add-edit-opportunity`],{queryParams:{CO_id:this.CO_id},state});
  }

  editEmployer(action:any) {
    const state={
    title:action
  }
    this.router.navigate([`/actions/employer-opportunities/add-edit-employer`],{queryParams:{CO_id:this.CO_id},state});

  }

  // ShowNewCompanyForm() {
  //   //ADD Company Button Click
  //   this.isReadonly = false;
  //   this.title = 'AddCompany';
  //   this.hideHeader = true;
  //   this.showForm = true;
  // }

  // addCmpCnlBtn() {
  //   this.showForm = false;
  //   this.hideHeader = false;
  // }

  // newSchemecnlbtn() {
  //   this.showForm = false;
  //   this.title = 'existing';
  // }

  // showExistingOpportunities() {
  //   return (this.title = 'existing');
  // }

  // getSectorTitles() {
  //   // In Add company form - To sector dropdown
  //   this.dataTransferService.getSectorTitles().subscribe((res: any) => {
  //     if ((res.statusCode = 200)) {
  //       this.SectorList = res.data;
  //       console.log('Sectors', this.SectorList);
  //     } else {
  //       console.error('Failed to fetch sectors. Status:', res.status);
  //     }
  //   });
  // }

  formatDate(date: Date): string | null {
    // Check if the date is valid
    if (!isNaN(date.getTime())) {
      return this.datePipe.transform(date, 'MMM dd yyyy');
    } else {
      return '';
    }
  }

  getAllSchemeByCompanyId(companyId: any) {
    // Get existing schemes by company id
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllSchemeByCompanyId(companyId).subscribe({
      next: (res: any) => {
        if (res.statusCode === 200) {
          this.ngxSpinnerService.hide('globalSpinner');
          console.log('Existing Scheme:', res.data);

          // Convert timestamps to date format for each scheme
          this.ExistingOpportunities = res.data;
          // this.filteredSchemeList = this.ExistingOpportunities;
          this.companyId = companyId;
          console.log('GetAllScheme', this.ExistingOpportunities);
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Failed to fetch companies. Status:', res.status);
        }
      },
      error: (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error occurred while fetching companies:', error);
      },
    });
  }

  // getAllRoleBySectorId(sectorID: any) {
  //   this.dataTransferService.getAllRoleBySectorId(sectorID).subscribe({
  //     next: (res: any) => {
  //       if (res.statusCode === 200) {
  //         this.RoleList = res.data;
  //         this.roleId = this.RoleList.RO_id;
  //         console.log('RoleList : ', this.RoleList);
  //         this.ngxSpinnerService.hide('globalSpinner');
  //       } else {
  //         this.ngxSpinnerService.hide('globalSpinner');
  //         console.error('Failed to fetch role. Status:', res.status);
  //       }
  //     },
  //     error: (error: any) => {
  //       this.ngxSpinnerService.hide('globalSpinner');
  //       console.error('Error occurred while fetching roles:', error);
  //     },
  //   });
  // }

  showDeleteModal(scheme: any) {
    console.log('Delete Scheme', scheme);
    this.deleteId = scheme.JB_id;
  }

  DeleteJobsById() {
    console.log('Id to delete : ', this.deleteId);

    this.dataTransferService
      .DeleteJobsById(this.deleteId)
      .subscribe((res: any) => {
        if ((res.statusCode = 200)) {
          this.getAllSchemeByCompanyId(this.companyId);
          this.toastr.success('Opportunity deleted successfully');
        } else {
          console.error("Couldn't delete.  Status:", res.status);
        }
      });
  }

  editOpportunity(opportunity:any,type:any){
const state={
  viewInsight : true,
  title:type,
  opportunityData:opportunity
}
this.router.navigate([`/actions/employer-opportunities/existing-opportunities/add-edit-opportunity`],{queryParams:{CO_id:this.CO_id},state});
  }

  viewOpportunity(opportunity:any,type:any){
    const state={
      viewInsight : false,
      title:type,
      opportunityData:opportunity
    }
    this.router.navigate([`/actions/employer-opportunities/existing-opportunities/add-edit-opportunity`],{queryParams:{CO_id:this.CO_id},state});
    }
 
}

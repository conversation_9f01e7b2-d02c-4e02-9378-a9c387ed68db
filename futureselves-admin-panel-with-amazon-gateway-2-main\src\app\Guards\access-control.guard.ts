import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AccessControlGuard implements CanActivate {

  constructor(private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    // Get accessMenuList from local storage
    const adminModulesData = sessionStorage.getItem('adminModules');

    if (adminModulesData !== null) {
      const accessMenuList = JSON.parse(adminModulesData);
      const requestedRoute = state.url;

      const hasAccess = accessMenuList.some((menu: { AM_url: string; }) => requestedRoute.startsWith(menu.AM_url));

      if (hasAccess) {
        return true; 
      } else {
        this.router.navigate(['/unauthorized-page']); 
        return false;
      }
    } else {
      console.error('adminModules not found in sessionStorage');
      this.router.navigate(['/unauthorized-page']); 
      return false; 
    }
  }
}

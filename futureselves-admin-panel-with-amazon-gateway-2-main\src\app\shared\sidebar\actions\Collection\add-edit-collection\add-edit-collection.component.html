<app-sidebar>
  <div class="content-wrapper">
    <div class="card-header card-title bg-primary rounded-top text-white text-center  mb-1">{{title}}
      Collection</div>

    <div class="row"> <!-- Publish Form -->
      <div class="col-lg-12"> 
        <div class="card shadow-sm">
          <div class="card-body mx-2">

            <form [formGroup]="publishVoxlistForm"> 
              <div class="row">
                <div class="form-group col-lg-4">
                  <label for="PL_title" class="required-field">Collection name</label>
                  <input type="text" formControlName="PL_title" class="form-control form-control-sm"
                    placeholder="Enter collection name" aria-label="voxList" aria-describedby="voxList" required>
                </div>

                <div class="form-group col-lg-4">
                  <label for="PL_dp" class="required-field">Collection Image</label>
                  <div class="logo-input-container">
                    <input type="file" class="form-control form-control-sm" formControlName="PL_dp"
                      (change)="onFileSelected($event);" required [readonly]="isReadonly" accept="image/*">
                    <img *ngIf="imageSrc" [src]="imageSrc" alt="Role Image" class="img-preview">
                  </div>
                  <div *ngIf="this.publishVoxlistForm.get('PL_dp')?.errors?.fileSizeValidator" class="warning">
                    The file size exceeds the 2 MB limit. Please select a smaller file.
                </div> 
                </div>

                <div class="form-group col-lg-4">
                  <label for="PL_description" class="required-field">Collection Description</label>
                  <textarea class="form-control form-control-sm" formControlName="PL_description" required
                    [readonly]="isReadonly" rows="3" cols="50" placeholder="Collection description "></textarea>
                </div>
              </div>
            </form>

          </div>
        </div>
      </div>
    </div>

    <div class="row mt-3 ">  <!-- Insight filter card -->
      <div class="col-lg-12"> 
        <div class="card shadow-sm text-center">
          <h6 class="mt-4">Select Insights From Below To Publish Collection</h6>
          <div class="card-body mx-2">

            <div class="row mt-2"> <!--  Search Form -->
              <form [formGroup]="searchForm" (ngSubmit)="onSubmitFilter()">
                <div class="row">

                  <div class="form-group col-lg-3">
                    <label for="AN_industryId" style="font-weight: bold; font-size: medium;" class="required-field"
                      [class.text-danger]="!industrySelected"> Sector Name </label>
                    <select class="form-control form-control-sm" formControlName="AN_industryId"
                      (change)="onChangeIndustry($event)">
                      <option selected disabled value="">Select an sector name</option>
                      <option *ngFor="let industry of industryList" [value]="industry.IN_id">
                        {{industry.IN_name}}
                      </option>
                    </select>
                  </div>
               
                  <!-- <div class="form-group col-lg-3">
                    <label for="AN_degreeId" style="font-size: small">Filter by Role Name
                    </label>
                    <div (click)="onSelectOtherDropdown($event)">
                      <select style="font-size: small" class="form-control form-control-sm"
                        formControlName="AN_degreeId" [style.pointer-events]="industrySelected ? 'auto' : 'none'">
                        <option selected disabled value="">Select a role name</option>
                        <option *ngFor="let data of Allroles" [value]="data.RO_id">
                          {{data.RO_title}}
                        </option>
                      </select>
                    </div>
                  </div> -->
                  

                  <!-- <div class="form-group col-lg-3">
                    <label for="AN_userId" style="font-size: small">Filter by Sharer Name</label>
                    <div (click)="onSelectOtherDropdown($event)">
                      <select style="font-size: small" class="form-control form-control-sm" formControlName="AN_userId"
                        [style.pointer-events]="industrySelected ? 'auto' : 'none'">
                        <option selected disabled value="">Select a sharer name</option>
                        <option *ngFor="let data of SharerList" [value]="data.U_id">
                          {{data.U_name}}
                        </option>
                      </select>
                    </div>
                  </div> -->
                 
                  <!-- <div class="form-group col-lg-3">
                    <label for="AN_questionId" style="font-size: small">Filter by Insight Name</label>
                    <div (click)="onSelectOtherDropdown($event)">
                      <select style="font-size: small" class="form-control form-control-sm"
                        formControlName="AN_questionId" [style.pointer-events]="industrySelected ? 'auto' : 'none'">
                        <option selected disabled value="">Select an insight name</option>
                        <option *ngFor="let data of insightList" [value]="data.QU_id">
                          {{data.QU_title}}
                        </option>
                      </select>
                    </div>
                  </div> -->

                  <div class="form-group col-lg-3">
                    <label for="AN_degreeId" style="font-size: small">Filter by Role Name</label>
                    <ng-select
                      (click)="onSelectOtherDropdown($event)" 
                      style="font-size: small; width: 100%;" 
                      [items]="Allroles" 
                      bindLabel="RO_title" 
                      bindValue="RO_id" 
                      formControlName="AN_degreeId" 
                      [searchable]="true"
                      [clearable]="true"
                      [disabled]="!industrySelected"
                      class="custom-ng-select">
                      <ng-option [value]="null" disabled>Select a role name</ng-option>
                    </ng-select>
                  </div>
                  
                  <div class="form-group col-lg-3">
                    <label for="AN_userId" style="font-size: small">Filter by Sharer Name</label>
                    <ng-select
                      (click)="onSelectOtherDropdown($event)" 
                      style="font-size: small; width: 100%;" 
                      [items]="SharerList"
                      bindLabel="U_name"
                      bindValue="U_id"
                      formControlName="AN_userId" 
                      [searchable]="true"
                      [clearable]="true"
                      [disabled]="!industrySelected"
                      class="custom-ng-select">
                      <ng-option [value]="null" disabled>Select a sharer name</ng-option>
                    </ng-select>
                  </div>
                  
                  <div class="form-group col-lg-3">
                    <label for="AN_questionId" style="font-size: small">Filter by Insight Name</label>
                    <ng-select
                      (click)="onSelectOtherDropdown($event)" 
                      style="font-size: small; width: 100%;"  
                      [items]="insightList"
                      bindLabel="QU_title"
                      bindValue="QU_id"
                      formControlName="AN_questionId" 
                      [searchable]="true"
                      [clearable]="true"
                      [disabled]="!industrySelected"
                      class="custom-ng-select">
                      <ng-option [value]="null" disabled>Select an insight name</ng-option>
                    </ng-select>
                  </div>
                  
                  
                  
                </div>

                <div class="form-group float-right">
                  <button mat-raised-button type="submit"
                    class="btn btn-sm btn-primary mx-2 px-4 mr-2">Filter</button>

                  <button mat-raised-button type="button" class="btn mx-2 px-4 btn-sm btn-secondary"
                    (click)="resetFilter()">Reset</button>
                </div>
              </form>
            </div>
            <hr class="mt-0">
            
            <div class="row"> <!-- Data Container of filtered insights -->
              <div class="table-responsive">
                <table class="table table-hover" *ngIf="industrySelected">
                  <thead class=" text-primary ">
                    <th class="text-center">
                      Action
                    </th>
                    <th class="text-center">
                      Role
                    </th>
                    <th>
                      Sharer
                    </th>
                    <th class="text-center">
                      Question
                    </th>
                    <th class="text-center">
                      Sector
                    </th>

                    <th class="text-center">
                      Audio
                    </th>

                    <!-- <th>
                              Sentence
                          </th> -->
                    <!-- <th>Action</th> -->
                  </thead>
                  <tbody>
                    <tr
                      *ngFor="let soundBite  of filteredData | filter: term | paginate: { itemsPerPage: 3, currentPage: p }">
                      <!-- <td>
                                <input type="checkbox" [(ngModel)]="data.selected" />
                              </td> -->
                              <td>
                                <div class="">
                                  <button 
                                    class="btn btn-primary btn-sm text-center" 
                                    (click)="toggleSelection(soundBite)"
                                  >
                                    {{ isSelected(soundBite) ? 'Added' : 'Add' }}
                                    <i class="icon-circle-plus mx-1 text-white"></i>
                                  </button>
                                </div>
                              </td>
                              

                      <td class="text-center">
                        <!-- <span  [ngClass]="{'badge-danger' : data.Content === 'Red','badge-success' : data.Content === 'Green'}"
                                class="badge" style="font-size:small;">
                                    {{data.AN_Role}}
                                </span> -->
                        {{soundBite?.RO_title}}
                      </td>

                      <td>
                        <div class="text-wrap" style="width: 8rem;">
                          <!-- <ng-container *ngFor="let soundBite of data.soundBites"> -->
                          {{ soundBite?.U_name }}
                          <!-- </ng-container> -->
                          <!-- {{data.sharer}} -->
                        </div>
                      </td>
                      <td>
                        <div class="text-wrap text-center" style="width: 8rem;">
                          {{soundBite?.AN_title}}
                        </div>
                      </td>
                      <td>
                        <div class="text-wrap text-center" style="width: 8rem;">
                          {{soundBite?.IN_name}}
                        </div>
                      </td>

                      <td>
                        <!-- {{soundBite?.AN_recordLink}} -->
                        <div>
                          <audio controls class="text-wrap audioControl" >
                            <source src="{{soundBite?.AN_recordLink}}" type="audio/ogg">
                            <source src="{{soundBite?.AN_recordLink}}" type="audio/mpeg">
                          </audio><br>
                        </div>
                      </td>

                      <!-- <td>
                                <ng-container *ngFor="let filterWord of data.filter_words">
                                    {{ filterWord.word }}
                                </ng-container>
                              </td> -->
                    </tr>
                  </tbody>
                </table>
                <div *ngIf="!industrySelected" class="text-center mt-3">
                  <p style="color: red;">Please select sector to view data.</p>
                </div>
                <div class="my-3" *ngIf="industrySelected">
                  <pagination-controls (pageChange)="p = $event" class="text-center"></pagination-controls>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>


    <div class="row mt-3"> <!-- Selected Insights To Publish Collection -->
      <div class="col-lg-12"> 
        <div class="card shadow-sm text-center">
          <h6 class="mt-4">Selected Insights To Publish Collection</h6>
          <div class="card-body mx-2">
            <div class="row scrollable-container-right"> <!-- Right Page soundbite form for publish -->
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="text-primary">
                    <th>
                      Action
                    </th>
                    <th class="text-center">
                      Role
                    </th>
                    <th>
                      Sharer
                    </th>
                    <th>
                      Question
                    </th>
                    <th>
                      Sector
                    </th>

                    <th class="text-center">
                      Audio
                    </th>

                  </thead>
                  <tbody>
                    <!-- <tr *ngFor="let data of selectedAudios | filter: term | paginate: { itemsPerPage: 10, currentPage: p }"> -->
                    <tr *ngFor="let data of selectedAudios">
                      <td>
                        <div class="">
                          <button class="btn btn-primary btn-sm" (click)="removeFromSelectedAudios(data)">
                            Remove<i class="icon-circle-cross mx-1 text-white"></i>
                          </button>
                        </div>
                      </td>

                      <td>
                        {{data.RO_title || data?.VL_roleName}}
                      </td>

                      <td>
                        <div class="text-wrap" style="width: 8rem;">
                          {{data?.U_name || data?.VL_sharerName}}
                        </div>
                      </td>
                      <td>
                        <div class="text-wrap" style="width: 8rem;">
                          {{data.AN_title || data?.VL_questionName}}
                        </div>
                      </td>
                      <td>
                        <div class="text-wrap" style="width: 8rem;">
                          {{data.IN_name || data?.VL_sectorName}}
                        </div>
                      </td>

                      <td>
                        <div>
                          <audio controls class="text-wrap audioControl">
                            <source src="{{data?.AN_recordLink || data?.VL_soundbiteLink}}" type="audio/ogg">
                            <source src="{{data?.AN_recordLink || data?.VL_soundbiteLink}}" type="audio/mpeg">
                          </audio><br>
                        </div>
                      </td>

                    </tr>
                  </tbody>
                </table>
                <!-- <div class="my-3">
                  <pagination-controls (pageChange)="p = $event" class="text-center"></pagination-controls>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="btns mt-3 text-center">
      <input class="btn btn-primary custom-radius-btn mr-3" type="button" value="Publish" (click)="clickPublishVoxlist()">
        <button class="btn btn-light" routerLink="/actions/collections">Cancel</button>
    </div>

  </div>
</app-sidebar>
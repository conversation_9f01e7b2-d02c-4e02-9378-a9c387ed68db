{"ast": null, "code": "import _asyncToGenerator from \"D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { Validators } from '@angular/forms';\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"@angular/common/http\";\nimport * as i5 from \"ngx-spinner\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"ngx-toastr\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../sidebar.component\";\nconst _c0 = [\"audioPlayer\"];\n\nfunction AddEditUniversityListComponent_img_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 28);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r0.imageSrc, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction AddEditUniversityListComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditUniversityListComponent_div_29_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid URL starting with http:// or https://. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditUniversityListComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, AddEditUniversityListComponent_div_29_div_1_Template, 2, 0, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.addNewUniversityForm.get(\"INS_website\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.pattern);\n  }\n}\n\nfunction AddEditUniversityListComponent_div_34_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Please enter a valid URL starting with http:// or https://. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditUniversityListComponent_div_34_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, AddEditUniversityListComponent_div_34_div_8_div_1_Template, 2, 0, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    const i_r9 = ctx_r13.index;\n    const serviceLink_r8 = ctx_r13.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceLink_r8.get(ctx_r10.serviceLinkKeys[i_r9])) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.pattern);\n  }\n}\n\nfunction AddEditUniversityListComponent_div_34_div_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Description is required when a link is provided. \");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditUniversityListComponent_div_34_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, AddEditUniversityListComponent_div_34_div_13_div_1_Template, 2, 0, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceLink_r8 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceLink_r8.get(\"SL_description\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required);\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"required-field\": a0\n  };\n};\n\nfunction AddEditUniversityListComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵelementStart(1, \"span\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 33);\n    i0.ɵɵelementStart(4, \"div\", 7);\n    i0.ɵɵelementStart(5, \"label\", 34);\n    i0.ɵɵtext(6, \"Link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 35);\n    i0.ɵɵtemplate(8, AddEditUniversityListComponent_div_34_div_8_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 7);\n    i0.ɵɵelementStart(10, \"label\", 36);\n    i0.ɵɵtext(11, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"textarea\", 37);\n    i0.ɵɵtemplate(13, AddEditUniversityListComponent_div_34_div_13_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const serviceLink_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_10_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.serviceLinkHeadings[i_r9]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c1, (tmp_2_0 = serviceLink_r8.get(ctx_r3.serviceLinkKeys[i_r9])) == null ? null : tmp_2_0.value))(\"for\", ctx_r3.serviceLinkKeys[i_r9] + \"_link\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", ctx_r3.serviceLinkKeys[i_r9] + \"_link\")(\"formControlName\", ctx_r3.serviceLinkKeys[i_r9])(\"readonly\", ctx_r3.isReadonly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = serviceLink_r8.get(ctx_r3.serviceLinkKeys[i_r9])) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = serviceLink_r8.get(ctx_r3.serviceLinkKeys[i_r9])) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, (tmp_8_0 = serviceLink_r8.get(ctx_r3.serviceLinkKeys[i_r9])) == null ? null : tmp_8_0.value));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"readonly\", ctx_r3.isReadonly);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_10_0 = serviceLink_r8.get(\"SL_description\")) == null ? null : tmp_10_0.invalid);\n  }\n}\n\nconst _c2 = function (a0, a1) {\n  return {\n    \"btn-outline-primary\": a0,\n    \"btn-outline-secondary\": a1\n  };\n};\n\nfunction AddEditUniversityListComponent_div_39_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function AddEditUniversityListComponent_div_39_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const i_r17 = i0.ɵɵnextContext().index;\n      const ctx_r21 = i0.ɵɵnextContext();\n      return ctx_r21.toggleAudio(i_r17);\n    });\n    i0.ɵɵelement(1, \"i\", 57);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r17 = i0.ɵɵnextContext().index;\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c2, !ctx_r18.isPlaying[i_r17], ctx_r18.isPlaying[i_r17]));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r18.isPlaying[i_r17] ? \"fa-pause\" : \"fa-play\");\n  }\n}\n\nfunction AddEditUniversityListComponent_div_39_button_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function AddEditUniversityListComponent_div_39_button_21_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const i_r17 = i0.ɵɵnextContext().index;\n      const ctx_r25 = i0.ɵɵnextContext();\n      return ctx_r25.removeInsight(i_r17);\n    });\n    i0.ɵɵelement(1, \"i\", 59);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditUniversityListComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelementStart(1, \"div\", 39);\n    i0.ɵɵelementStart(2, \"div\", 40);\n    i0.ɵɵelementStart(3, \"div\", 41);\n    i0.ɵɵelementStart(4, \"label\", 42);\n    i0.ɵɵtext(5, \"Insight Title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 41);\n    i0.ɵɵelementStart(8, \"label\", 44);\n    i0.ɵɵtext(9, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 41);\n    i0.ɵɵelementStart(12, \"label\", 46);\n    i0.ɵɵtext(13, \"Position\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 41);\n    i0.ɵɵelementStart(16, \"label\", 48);\n    i0.ɵɵtext(17, \"Upload Insight\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 49);\n    i0.ɵɵlistener(\"change\", function AddEditUniversityListComponent_div_39_Template_input_change_18_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r29);\n      const i_r17 = restoredCtx.index;\n      const ctx_r28 = i0.ɵɵnextContext();\n      return ctx_r28.onAudioSelected($event, i_r17);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 50);\n    i0.ɵɵtemplate(20, AddEditUniversityListComponent_div_39_button_20_Template, 2, 5, \"button\", 51);\n    i0.ɵɵtemplate(21, AddEditUniversityListComponent_div_39_button_21_Template, 2, 0, \"button\", 52);\n    i0.ɵɵelementStart(22, \"audio\", 53, 54);\n    i0.ɵɵelement(24, \"source\", 55);\n    i0.ɵɵtext(25, \" Your browser does not support the audio element. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const i_r17 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroupName\", i_r17);\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.audioUrls[i_r17]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.insightFormArray.length > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r4.audioUrls[i_r17], i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction AddEditUniversityListComponent_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function AddEditUniversityListComponent_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return ctx_r30.addInsight();\n    });\n    i0.ɵɵtext(1, \"Add Insight \");\n    i0.ɵɵelement(2, \"i\", 61);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AddEditUniversityListComponent_button_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵtext(1, \"Save\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nexport class AddEditUniversityListComponent {\n  constructor(formBuilder, dataTransferService, domSanitizer, http, ngxSpinnerService, router, toastr, route, datePipe) {\n    var _a;\n\n    this.formBuilder = formBuilder;\n    this.dataTransferService = dataTransferService;\n    this.domSanitizer = domSanitizer;\n    this.http = http;\n    this.ngxSpinnerService = ngxSpinnerService;\n    this.router = router;\n    this.toastr = toastr;\n    this.route = route;\n    this.datePipe = datePipe;\n    this.p = 1;\n    this.showForm = false;\n    this.submitted = false;\n    this.title = 'Add New';\n    this.isReadonly = false;\n    this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\n    this.serviceLinkHeadings = ['Appointment', 'Internship Discussions', 'Practice Interviews', 'Events', 'Online Resources', 'Jobs'];\n    this.serviceLinkKeys = ['SL_appointment', 'SL_internshipDiscussion', 'SL_practiceInterview', 'SL_events', 'SL_onlineResources', 'SL_jobs'];\n    this.audioFiles = [];\n    this.characterCount = 0;\n    this.isPlaying = []; // Array to track playback state\n\n    this.audioUrls = []; // Array to store audio URLs\n\n    this.showOtherTypeInput = false;\n    this.existingAudioNames = [];\n    this.audioFileUrls = [];\n    this.addNewUniversityForm = this.formBuilder.group({\n      INS_title: ['', [Validators.required]],\n      INS_dp: [''],\n      INS_website: ['', [Validators.required, Validators.pattern('https?://.+')]],\n      INS_service: ['', [Validators.required]],\n      INS_serviceLinks: this.formBuilder.array(this.serviceLinkKeys.map((key, index) => this.createServiceLinkGroup(key))),\n      INS_HrInsights: this.formBuilder.array([this.createInsight()])\n    });\n    this.route.queryParams.subscribe(param => {\n      this.INS_id = param['INS_id'];\n      console.log(\"INS_id\", this.INS_id);\n    });\n    const state = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras.state;\n\n    if (state) {\n      this.title = state.title;\n    } else {\n      this.router.navigate([`/actions/universities`]);\n    }\n  }\n\n  ngOnInit() {\n    if (this.title === 'Edit') {\n      this.getInstituteById(this.INS_id);\n    }\n  }\n\n  getInstituteById(INS_id) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const res = yield _this.dataTransferService.getInstituteById(INS_id).toPromise();\n        console.log(\"getInstituteById res\", res);\n\n        if (res && res.data) {\n          _this.universityData = res.data;\n\n          _this.patchForm(_this.universityData);\n        } else {\n          console.error(\"Response is undefined or missing 'data' property.\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching institute data:\", error);\n      }\n    })();\n  }\n\n  patchForm(data) {\n    var _a;\n\n    if (this.title === 'Edit') {\n      const serviceLinks = this.addNewUniversityForm.get('INS_serviceLinks');\n\n      if (data === null || data === void 0 ? void 0 : data.INS_serviceLinks) {\n        console.log(\"data.INS_serviceLinks\");\n        (_a = data === null || data === void 0 ? void 0 : data.INS_serviceLinks) === null || _a === void 0 ? void 0 : _a.forEach((serviceLink, index) => {\n          const linkKey = this.serviceLinkKeys[index];\n          const formGroup = serviceLinks.at(index);\n\n          if (formGroup) {\n            formGroup.patchValue({\n              [linkKey]: serviceLink[linkKey] || '',\n              SL_description: serviceLink.SL_description || ''\n            });\n          }\n        });\n      }\n\n      this.addNewUniversityForm.patchValue({\n        INS_title: data === null || data === void 0 ? void 0 : data.INS_title,\n        INS_website: data === null || data === void 0 ? void 0 : data.INS_website,\n        INS_service: data === null || data === void 0 ? void 0 : data.INS_service\n      });\n\n      if (data === null || data === void 0 ? void 0 : data.INS_HrInsights) {\n        const hrInsightsArray = this.addNewUniversityForm.get('INS_HrInsights');\n        hrInsightsArray.clear();\n        data.INS_HrInsights.forEach(insight => {\n          const insightFormGroup = this.formBuilder.group({\n            HRI_title: [insight.HRI_title],\n            HRI_name: [insight.HRI_name],\n            HRI_position: [insight.HRI_position],\n            HRI_link: [insight.HRI_link]\n          });\n          hrInsightsArray.push(insightFormGroup);\n          this.audioUrls = data.INS_HrInsights.map(insight => insight.HRI_link);\n          this.isPlaying = new Array(data.INS_HrInsights.length).fill(false);\n          this.existingAudioNames = data.INS_HrInsights.map(insight => insight.HRI_link.substring(insight.HRI_link.lastIndexOf('/') + 1));\n        });\n      }\n    }\n  }\n\n  updateUniversity() {\n    var _a;\n\n    if (this.addNewUniversityForm.invalid) {\n      Object.keys(this.addNewUniversityForm.controls).forEach(name => {\n        const control = this.addNewUniversityForm.get(name);\n\n        if (control === null || control === void 0 ? void 0 : control.invalid) {\n          console.log(`Invalid control: ${name}, Errors:`, control.errors);\n        }\n      });\n      this.toastr.info('Please fill all required fields correctly');\n      return;\n    }\n\n    const uploadDp = ((_a = this.addNewUniversityForm.get('INS_dp')) === null || _a === void 0 ? void 0 : _a.value) ? this.uploadLogoUrl() : Promise.resolve(this.universityData.INS_dp);\n    this.ngxSpinnerService.show('globalSpinner');\n    uploadDp.then(dpUrl => {\n      return this.uploadAudioFiles(this.audioFiles).then(audioUrls => {\n        var _a, _b, _c, _d, _e;\n\n        console.log(\"Uploaded audio files:\", this.audioFiles);\n        this.insightFormArray.controls.forEach((control, index) => {\n          var _a;\n\n          (_a = control.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(this.audioUrls[index]);\n        });\n        const data = {\n          INS_id: this.INS_id,\n          INS_title: (_a = this.addNewUniversityForm.get('INS_title')) === null || _a === void 0 ? void 0 : _a.value,\n          INS_dp: dpUrl ? dpUrl : \"\",\n          INS_website: (_b = this.addNewUniversityForm.get('INS_website')) === null || _b === void 0 ? void 0 : _b.value,\n          INS_service: (_c = this.addNewUniversityForm.get('INS_service')) === null || _c === void 0 ? void 0 : _c.value,\n          INS_serviceLinks: (_d = this.addNewUniversityForm.get('INS_serviceLinks')) === null || _d === void 0 ? void 0 : _d.value,\n          INS_HrInsights: (_e = this.addNewUniversityForm.get('INS_HrInsights')) === null || _e === void 0 ? void 0 : _e.value,\n          INS_city: '',\n          INS_country: '',\n          INS_state: ''\n        };\n        console.log(\"Data to update...\", data);\n        return this.dataTransferService.updateInstitute(data).toPromise();\n      });\n    }).then(res => {\n      this.ngxSpinnerService.hide('globalSpinner');\n\n      if (res.statusCode === 200) {\n        this.toastr.success('University updated successfully.');\n        this.router.navigate(['actions/universities']);\n      } else {\n        this.toastr.error('Something went wrong.');\n      }\n    }).catch(error => {\n      this.ngxSpinnerService.hide('globalSpinner');\n      const errorMessage = error === 'Audio file upload failed.' ? error : 'Error updating university.';\n      this.toastr.error(errorMessage);\n    });\n  }\n\n  get serviceLinks() {\n    return this.addNewUniversityForm.get('INS_serviceLinks');\n  }\n\n  get insightFormArray() {\n    return this.addNewUniversityForm.get('INS_HrInsights');\n  }\n\n  createServiceLinkGroup(linkKey) {\n    let group = {};\n    group[linkKey] = ['', [Validators.pattern('https?://.+')]];\n    group['SL_description'] = [''];\n    const formGroup = this.formBuilder.group(group); // Apply the custom validator\n\n    formGroup.setValidators(this.linkDescriptionValidator(linkKey, 'SL_description'));\n    return formGroup;\n  }\n\n  linkDescriptionValidator(linkKey, descriptionKey) {\n    return formGroup => {\n      const linkControl = formGroup.get(linkKey);\n      const descriptionControl = formGroup.get(descriptionKey);\n\n      if ((linkControl === null || linkControl === void 0 ? void 0 : linkControl.value) && !(descriptionControl === null || descriptionControl === void 0 ? void 0 : descriptionControl.value)) {\n        descriptionControl === null || descriptionControl === void 0 ? void 0 : descriptionControl.setErrors({\n          required: true\n        });\n        return {\n          descriptionRequired: true\n        };\n      } else {\n        descriptionControl === null || descriptionControl === void 0 ? void 0 : descriptionControl.setErrors(null);\n        return null;\n      }\n    };\n  }\n\n  addInsight() {\n    this.insightFormArray.push(this.createInsight());\n  }\n\n  removeInsight(index) {\n    this.insightFormArray.removeAt(index);\n    this.audioUrls.splice(index, 1); // Remove URL for deleted row\n\n    this.isPlaying.splice(index, 1); // Remove playback state for deleted row\n\n    this.audioFiles.splice(index, 1); // Remove file object for deleted row\n  }\n\n  toggleAudio(index) {\n    const audioElements = this.audioPlayers.toArray();\n    const audioElement = audioElements[index].nativeElement; // Stop all other audio\n\n    this.stopAllAudio(index); // Toggle play/pause for the current audio element\n\n    if (this.isPlaying[index]) {\n      audioElement.pause();\n    } else {\n      audioElement.src = this.audioUrls[index];\n      audioElement.play();\n    } // Update the playback state\n\n\n    this.isPlaying[index] = !this.isPlaying[index];\n  }\n\n  stopAllAudio(currentIndex) {\n    this.audioPlayers.forEach((audioPlayer, index) => {\n      if (index !== currentIndex) {\n        audioPlayer.nativeElement.pause();\n        this.isPlaying[index] = false;\n      }\n    });\n  }\n\n  stopAudio(index) {\n    const audioElements = this.audioPlayers.toArray();\n    const audioElement = audioElements[index].nativeElement;\n    audioElement.pause();\n    audioElement.currentTime = 0; // Reset to the beginning\n\n    this.isPlaying[index] = false;\n  }\n\n  createInsight() {\n    return this.formBuilder.group({\n      HRI_title: [''],\n      HRI_name: [''],\n      HRI_position: [''],\n      HRI_link: [null]\n    });\n    this.isPlaying.push(false); // Initialize playback state for new row\n\n    this.audioUrls.push(''); // Initialize empty URL for new row\n  }\n\n  onAudioSelected(event, index) {\n    let audiofile;\n    const selectedFile = event.target.files[0];\n\n    if (selectedFile) {\n      const newFileName = FileValidator.addTimestamp(selectedFile.name);\n      audiofile = new File([selectedFile], newFileName, {\n        type: selectedFile.type\n      });\n    } else {\n      audiofile = null;\n    }\n\n    if (!audiofile) {\n      this.audioUrls[index] = '';\n      this.stopAudio(index);\n      return;\n    }\n\n    const audiofileType = audiofile.type.split('/')[0]; // Get the file type (e.g., 'audio', 'video', etc.)\n\n    if (audiofileType !== 'audio') {\n      // Reset the file input to clear the selected file\n      event.target.value = '';\n      this.toastr.info('Please select an audio file.');\n      return;\n    } // Store the file object in the array for later upload\n\n\n    this.audioFiles[index] = audiofile;\n    console.log('Audio file :', audiofile);\n    const reader = new FileReader();\n\n    reader.onload = () => {\n      // Store audio URL\n      this.audioUrls[index] = reader.result;\n    };\n\n    reader.readAsDataURL(audiofile);\n  }\n\n  uploadAudioFiles(files) {\n    const uploadPromises = files.map((file, index) => this.uploadSingleAudioFile(file, index));\n    return Promise.all(uploadPromises);\n  } // Updated uploadSingleAudioFile function\n\n\n  uploadSingleAudioFile(file, index) {\n    return new Promise((resolve, reject) => {\n      this.dataTransferService.uploadurl(file).subscribe(res => {\n        console.log('Upload successful', file.name);\n        const fileUrl = this.baseUrl + file.name;\n        this.audioUrls[index] = fileUrl;\n        resolve(fileUrl);\n      }, error => {\n        console.error('Upload error', error);\n        this.toastr.error('Failed to upload audio file');\n        reject(error);\n      });\n    });\n  }\n\n  onFileSelected(event) {\n    var _a;\n\n    let selectedFile = event.target.files[0];\n\n    if (event.target.files.length === 0) {\n      // Reset both imageName and imageSrc when no file is selected\n      this.imageName = null;\n      this.imageSrc = null;\n      return;\n    }\n\n    const newFileName = FileValidator.addTimestamp(selectedFile.name);\n    this.imageName = new File([selectedFile], newFileName, {\n      type: selectedFile.type\n    });\n\n    if (this.imageName) {\n      const formControl = this.addNewUniversityForm.get('INS_dp');\n      formControl === null || formControl === void 0 ? void 0 : formControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\n      formControl === null || formControl === void 0 ? void 0 : formControl.updateValueAndValidity();\n    }\n\n    const fileType = this.imageName.type.split('/')[0];\n    const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n\n    if (fileType !== 'image' || fileExtension === 'svg') {\n      event.target.value = '';\n      this.toastr.info('Please select an image file (excluding SVG).');\n      this.imageName = null;\n      this.imageSrc = null;\n      return;\n    }\n\n    if (this.imageName && fileType == 'image') {\n      const reader = new FileReader();\n\n      reader.onload = e => {\n        var _a;\n\n        this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\n      };\n\n      reader.readAsDataURL(this.imageName);\n    } else {\n      this.imageSrc = null; // Reset imageSrc if no file selected\n    }\n\n    console.log('imageName', this.imageName);\n  }\n\n  uploadLogoUrl() {\n    return new Promise((resolve, reject) => {\n      if (!this.imageName) {\n        reject('Please select an image.');\n        return;\n      }\n\n      console.log('Uploading image:', this.imageName);\n      this.dataTransferService.uploadurl(this.imageName).subscribe(res => {\n        const fileUrl = this.baseUrl + this.imageName.name; // Ensure you're concatenating correctly\n\n        resolve(fileUrl);\n      }, error => {\n        reject('Error uploading image: ' + error.message || error);\n      });\n    });\n  }\n\n  saveUniversity() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c, _d, _e;\n\n      if (_this2.addNewUniversityForm.invalid) {\n        console.log('this.job.value', _this2.addNewUniversityForm.value);\n\n        _this2.ngxSpinnerService.hide('globalSpinner');\n\n        _this2.toastr.info('Please fill all required fields and ensure they are filled correctly.');\n\n        return;\n      } else {\n        try {\n          _this2.ngxSpinnerService.show('globalSpinner');\n\n          if (_this2.imageName) {\n            yield _this2.uploadLogoUrl();\n            _this2.fileUrl = _this2.baseUrl + _this2.imageName.name;\n          }\n\n          const audioUrls = yield _this2.uploadAudioFiles(_this2.audioFiles);\n          console.log(\"this.audioFiles\", _this2.audioFiles);\n\n          _this2.insightFormArray.controls.forEach((control, index) => {\n            var _a;\n\n            const group = control;\n            (_a = group.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(audioUrls[index]);\n          });\n\n          console.log(\"this.addNewUniversityForm.value\", _this2.addNewUniversityForm.value);\n          const data = {\n            INS_title: (_a = _this2.addNewUniversityForm.get('INS_title')) === null || _a === void 0 ? void 0 : _a.value,\n            INS_dp: _this2.fileUrl,\n            INS_website: (_b = _this2.addNewUniversityForm.get('INS_website')) === null || _b === void 0 ? void 0 : _b.value,\n            INS_service: (_c = _this2.addNewUniversityForm.get('INS_service')) === null || _c === void 0 ? void 0 : _c.value,\n            INS_serviceLinks: (_d = _this2.addNewUniversityForm.get('INS_serviceLinks')) === null || _d === void 0 ? void 0 : _d.value,\n            INS_HrInsights: (_e = _this2.addNewUniversityForm.get('INS_HrInsights')) === null || _e === void 0 ? void 0 : _e.value,\n            INS_city: '',\n            INS_country: '',\n            INS_state: ''\n          };\n          console.log(\"Add University Data : \", data);\n\n          _this2.dataTransferService.addUniversity(data).subscribe(res => {\n            _this2.ngxSpinnerService.hide('globalSpinner');\n\n            _this2.toastr.success(\"University added successfully\");\n\n            _this2.router.navigate([`/actions/universities`]);\n          }, error => {\n            _this2.ngxSpinnerService.hide('globalSpinner');\n\n            console.log(\"Error\", error);\n\n            _this2.toastr.error(\"Unable to add university\");\n          });\n        } catch (error) {\n          _this2.ngxSpinnerService.hide('globalSpinner');\n\n          console.error(\"An error occurred while saving the university\", error);\n\n          _this2.toastr.error(\"An error occurred while saving the university\");\n        }\n      }\n    })();\n  }\n\n  onSubmit() {\n    if (this.title == 'Add') {\n      this.saveUniversity();\n    } else {\n      this.updateUniversity();\n    }\n  }\n\n}\n\nAddEditUniversityListComponent.ɵfac = function AddEditUniversityListComponent_Factory(t) {\n  return new (t || AddEditUniversityListComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.DomSanitizer), i0.ɵɵdirectiveInject(i4.HttpClient), i0.ɵɵdirectiveInject(i5.NgxSpinnerService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.ToastrService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i8.DatePipe));\n};\n\nAddEditUniversityListComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: AddEditUniversityListComponent,\n  selectors: [[\"app-add-edit-university-list\"]],\n  viewQuery: function AddEditUniversityListComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioPlayers = _t);\n    }\n  },\n  decls: 45,\n  vars: 13,\n  consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\", \"col-lg-6\"], [\"for\", \"INS_title\", 1, \"required-field\"], [\"type\", \"text\", \"id\", \"INS_title\", \"formControlName\", \"INS_title\", \"required\", \"\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"INS_dp\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"INS_dp\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Employer Logo\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"for\", \"INS_service\", 1, \"required-field\"], [\"id\", \"INS_service\", \"formControlName\", \"INS_service\", \"required\", \"\", \"placeholder\", \"Enter Description\", \"rows\", \"3\", \"cols\", \"50\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"INS_website\", 1, \"required-field\"], [\"type\", \"text\", \"id\", \"INS_website\", \"formControlName\", \"INS_website\", \"placeholder\", \"Enter URL\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [1, \"mb-3\", \"py-2\"], [\"formArrayName\", \"INS_serviceLinks\"], [3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"formArrayName\", \"INS_HrInsights\"], [\"class\", \"row mb-3\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"class\", \"btn btn-sm add-insight-btn btn-outline-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"text-center\", \"mt-2\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [\"type\", \"button\", \"routerLink\", \"/actions/universities\", 1, \"btn\", \"btn-light\"], [\"alt\", \"Employer Logo\", 1, \"img-preview\", 3, \"src\"], [1, \"warning\"], [4, \"ngIf\"], [3, \"formGroupName\"], [2, \"font-size\", \"0.9rem\"], [1, \"row\", \"mt-2\"], [2, \"font-size\", \"0.8rem\", 3, \"ngClass\", \"for\"], [\"type\", \"text\", \"placeholder\", \"Enter URL\", 1, \"form-control\", \"form-control-sm\", 3, \"id\", \"formControlName\", \"readonly\"], [\"for\", \"SL_description\", 2, \"font-size\", \"0.8rem\", 3, \"ngClass\"], [\"id\", \"SL_description\", \"formControlName\", \"SL_description\", \"placeholder\", \"Enter Description\", \"rows\", \"1\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [1, \"row\", \"mb-3\", 3, \"formGroupName\"], [1, \"col-lg-11\"], [1, \"row\", \"mr-0\"], [1, \"form-group\", \"col-lg-3\"], [\"for\", \"HRI_title\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_title\", \"placeholder\", \"Enter Title\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"HRI_name\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_name\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"HRI_position\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_position\", \"placeholder\", \"Enter Position\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"link\", 1, \"subtitle\"], [\"type\", \"file\", \"id\", \"link\", \"accept\", \"audio/*\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [1, \"col-lg-1\", \"px-0\", \"d-flex\", \"align-items-center\", \"btns\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm mr-2\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [\"controls\", \"\", 2, \"display\", \"none\"], [\"audioPlayer\", \"\"], [\"type\", \"audio/mpeg\", 3, \"src\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"mr-2\", 3, \"ngClass\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-minus\", \"icon\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"add-insight-btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"icon\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]],\n  template: function AddEditUniversityListComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"app-sidebar\");\n      i0.ɵɵelementStart(1, \"div\", 0);\n      i0.ɵɵelementStart(2, \"div\", 1);\n      i0.ɵɵelementStart(3, \"div\", 2);\n      i0.ɵɵelementStart(4, \"div\", 3);\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵtext(6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵelementStart(8, \"form\", 6);\n      i0.ɵɵlistener(\"ngSubmit\", function AddEditUniversityListComponent_Template_form_ngSubmit_8_listener() {\n        return ctx.onSubmit();\n      });\n      i0.ɵɵelementStart(9, \"div\", 1);\n      i0.ɵɵelementStart(10, \"div\", 7);\n      i0.ɵɵelementStart(11, \"label\", 8);\n      i0.ɵɵtext(12, \"University Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(13, \"input\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"div\", 7);\n      i0.ɵɵelementStart(15, \"label\", 10);\n      i0.ɵɵtext(16, \"University Logo\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"div\", 11);\n      i0.ɵɵelementStart(18, \"input\", 12);\n      i0.ɵɵlistener(\"change\", function AddEditUniversityListComponent_Template_input_change_18_listener($event) {\n        return ctx.onFileSelected($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(19, AddEditUniversityListComponent_img_19_Template, 1, 1, \"img\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(20, AddEditUniversityListComponent_div_20_Template, 2, 0, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"div\", 7);\n      i0.ɵɵelementStart(22, \"label\", 15);\n      i0.ɵɵtext(23, \"University Service Description\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(24, \"textarea\", 16);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(25, \"div\", 7);\n      i0.ɵɵelementStart(26, \"label\", 17);\n      i0.ɵɵtext(27, \"Website\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(28, \"input\", 18);\n      i0.ɵɵtemplate(29, AddEditUniversityListComponent_div_29_Template, 2, 1, \"div\", 14);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(30, \"hr\");\n      i0.ɵɵelementStart(31, \"h6\", 19);\n      i0.ɵɵtext(32, \"Service Links\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"div\", 20);\n      i0.ɵɵtemplate(34, AddEditUniversityListComponent_div_34_Template, 14, 15, \"div\", 21);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(35, \"hr\");\n      i0.ɵɵelementStart(36, \"div\", 22);\n      i0.ɵɵelementStart(37, \"h6\", 19);\n      i0.ɵɵtext(38, \"Insights\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(39, AddEditUniversityListComponent_div_39_Template, 26, 4, \"div\", 23);\n      i0.ɵɵtemplate(40, AddEditUniversityListComponent_button_40_Template, 3, 0, \"button\", 24);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"div\", 25);\n      i0.ɵɵtemplate(42, AddEditUniversityListComponent_button_42_Template, 2, 0, \"button\", 26);\n      i0.ɵɵelementStart(43, \"button\", 27);\n      i0.ɵɵtext(44, \"Cancel\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      let tmp_5_0;\n      let tmp_8_0;\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate1(\"\", ctx.title, \" University\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.addNewUniversityForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.imageSrc);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.addNewUniversityForm.get(\"INS_dp\")) == null ? null : tmp_5_0.errors == null ? null : tmp_5_0.errors.fileSizeValidator);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"readOnly\", ctx.isReadonly);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.addNewUniversityForm.get(\"INS_website\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.addNewUniversityForm.get(\"INS_website\")) == null ? null : tmp_8_0.touched));\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngForOf\", ctx.serviceLinks.controls);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngForOf\", ctx.insightFormArray.controls);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.insightFormArray.length < 3);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\n    }\n  },\n  directives: [i9.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.RequiredValidator, i8.NgIf, i1.FormArrayName, i8.NgForOf, i6.RouterLink, i1.FormGroupName, i8.NgClass],\n  styles: [\".fa-plus[_ngcontent-%COMP%] {\\n  font-size: small;\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1224px) {\\n  .insight-btn[_ngcontent-%COMP%] {\\n    padding: 7px 8px;\\n  }\\n\\n  .fas[_ngcontent-%COMP%] {\\n    font-size: small;\\n  }\\n}\\n\\n@media screen and (min-width: 320px) and (max-width: 768px) {\\n  .btns[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: center;\\n    margin-bottom: 20px;\\n  }\\n\\n  .add-insight-btn[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFkZC1lZGl0LXVuaXZlcnNpdHktbGlzdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0FBQ0o7O0FBRUU7RUFDRTtJQUNFLGdCQUFBO0VBQ0o7O0VBRUU7SUFDRSxnQkFBQTtFQUNKO0FBQ0Y7O0FBR0U7RUFDQTtJQUNFLGFBQUE7SUFDQSx1QkFBQTtJQUNBLG1CQUFBO0VBREY7O0VBSUE7SUFDRSxtQkFBQTtFQURGO0FBQ0YiLCJmaWxlIjoiYWRkLWVkaXQtdW5pdmVyc2l0eS1saXN0LmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmZhLXBsdXN7XHJcbiAgICBmb250LXNpemU6IHNtYWxsO1xyXG4gICB9XHJcbiAgXHJcbiAgQG1lZGlhIHNjcmVlbiBhbmQgKG1pbi13aWR0aDogOTkycHgpIGFuZCAobWF4LXdpZHRoOiAxMjI0cHgpIHtcclxuICAgIC5pbnNpZ2h0LWJ0bntcclxuICAgICAgcGFkZGluZzogN3B4IDhweDtcclxuICAgIH1cclxuICBcclxuICAgIC5mYXN7XHJcbiAgICAgIGZvbnQtc2l6ZTogc21hbGw7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBcclxuICBAbWVkaWEgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAzMjBweCkgYW5kIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLmJ0bnN7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG4gIH1cclxuICBcclxuICAuYWRkLWluc2lnaHQtYnRue1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICB9XHJcbiAgfVxyXG4gICJdfQ== */\"]\n});", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/src/app/shared/sidebar/actions/university-list/add-edit-university-list/add-edit-university-list.component.ts"], "names": ["Validators", "FileValidator", "i0", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "i8", "i9", "_c0", "AddEditUniversityListComponent_img_19_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "imageSrc", "ɵɵsanitizeUrl", "AddEditUniversityListComponent_div_20_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "AddEditUniversityListComponent_div_29_div_1_Template", "AddEditUniversityListComponent_div_29_Template", "ɵɵtemplate", "ctx_r2", "tmp_0_0", "ɵɵadvance", "addNewUniversityForm", "get", "errors", "pattern", "AddEditUniversityListComponent_div_34_div_8_div_1_Template", "AddEditUniversityListComponent_div_34_div_8_Template", "ctx_r13", "i_r9", "index", "serviceLink_r8", "$implicit", "ctx_r10", "serviceLinkKeys", "AddEditUniversityListComponent_div_34_div_13_div_1_Template", "AddEditUniversityListComponent_div_34_div_13_Template", "required", "_c1", "a0", "AddEditUniversityListComponent_div_34_Template", "ctx_r3", "tmp_2_0", "tmp_7_0", "tmp_8_0", "tmp_10_0", "ɵɵtextInterpolate", "serviceLinkHeadings", "ɵɵpureFunction1", "value", "is<PERSON><PERSON><PERSON>ly", "invalid", "touched", "_c2", "a1", "AddEditUniversityListComponent_div_39_button_20_Template", "_r23", "ɵɵgetCurrentView", "ɵɵlistener", "AddEditUniversityListComponent_div_39_button_20_Template_button_click_0_listener", "ɵɵrestoreView", "i_r17", "ctx_r21", "toggleAudio", "ctx_r18", "ɵɵpureFunction2", "isPlaying", "AddEditUniversityListComponent_div_39_button_21_Template", "_r27", "AddEditUniversityListComponent_div_39_button_21_Template_button_click_0_listener", "ctx_r25", "removeInsight", "AddEditUniversityListComponent_div_39_Template", "_r29", "AddEditUniversityListComponent_div_39_Template_input_change_18_listener", "$event", "restoredCtx", "ctx_r28", "onAudioSelected", "ctx_r4", "audioUrls", "insightFormArray", "length", "AddEditUniversityListComponent_button_40_Template", "_r31", "AddEditUniversityListComponent_button_40_Template_button_click_0_listener", "ctx_r30", "addInsight", "AddEditUniversityListComponent_button_42_Template", "AddEditUniversityListComponent", "constructor", "formBuilder", "dataTransferService", "domSani<PERSON>zer", "http", "ngxSpinnerService", "router", "toastr", "route", "datePipe", "_a", "p", "showForm", "submitted", "title", "baseUrl", "audioFiles", "characterCount", "showOtherTypeInput", "existingAudioNames", "audioFileUrls", "group", "INS_title", "INS_dp", "INS_website", "INS_service", "INS_serviceLinks", "array", "map", "key", "createServiceLinkGroup", "INS_HrInsights", "createInsight", "queryParams", "subscribe", "param", "INS_id", "console", "log", "state", "getCurrentNavigation", "extras", "navigate", "ngOnInit", "getInstituteById", "res", "to<PERSON>romise", "data", "universityData", "patchForm", "error", "serviceLinks", "for<PERSON>ach", "serviceLink", "linkKey", "formGroup", "at", "patchValue", "SL_description", "hrInsightsArray", "clear", "insight", "insightFormGroup", "HRI_title", "HRI_name", "HRI_position", "HRI_link", "push", "Array", "fill", "substring", "lastIndexOf", "updateUniversity", "Object", "keys", "controls", "name", "control", "info", "uploadDp", "uploadLogoUrl", "Promise", "resolve", "show", "then", "dpUrl", "uploadAudioFiles", "_b", "_c", "_d", "_e", "setValue", "INS_city", "INS_country", "INS_state", "updateInstitute", "hide", "statusCode", "success", "catch", "errorMessage", "setValidators", "linkDescriptionValidator", "<PERSON><PERSON><PERSON>", "linkControl", "descriptionControl", "setErrors", "descriptionRequired", "removeAt", "splice", "audioElements", "audioPlayers", "toArray", "audioElement", "nativeElement", "stopAllAudio", "pause", "src", "play", "currentIndex", "audioPlayer", "stopAudio", "currentTime", "event", "audiofile", "selectedFile", "target", "files", "newFileName", "addTimestamp", "File", "type", "audiofileType", "split", "reader", "FileReader", "onload", "result", "readAsDataURL", "uploadPromises", "file", "uploadSingleAudioFile", "all", "reject", "uploadurl", "fileUrl", "onFileSelected", "imageName", "formControl", "fileSizeValidator", "updateValueAndValidity", "fileType", "fileExtension", "pop", "toLowerCase", "e", "message", "saveUniversity", "addUniversity", "onSubmit", "ɵfac", "AddEditUniversityListComponent_Factory", "t", "ɵɵdirectiveInject", "FormBuilder", "DataTransferService", "Dom<PERSON><PERSON><PERSON>zer", "HttpClient", "NgxSpinnerService", "Router", "ToastrService", "ActivatedRoute", "DatePipe", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "AddEditUniversityListComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "decls", "vars", "consts", "template", "AddEditUniversityListComponent_Template", "AddEditUniversityListComponent_Template_form_ngSubmit_8_listener", "AddEditUniversityListComponent_Template_input_change_18_listener", "tmp_5_0", "ɵɵtextInterpolate1", "directives", "SidebarComponent", "ɵNgNoValidate", "NgControlStatusGroup", "FormGroupDirective", "DefaultValueAccessor", "NgControlStatus", "FormControlName", "RequiredValidator", "NgIf", "FormArrayName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RouterLink", "FormGroupName", "Ng<PERSON><PERSON>", "styles"], "mappings": ";AAAA,SAASA,UAAT,QAA4B,gBAA5B;AACA,SAASC,aAAT,QAA8B,mDAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,+CAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,2BAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,sBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,YAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,OAAO,KAAKC,EAAZ,MAAoB,4BAApB;AACA,MAAMC,GAAG,GAAG,CAAC,aAAD,CAAZ;;AACA,SAASC,8CAAT,CAAwDC,EAAxD,EAA4DC,GAA5D,EAAiE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3EZ,IAAAA,EAAE,CAACc,SAAH,CAAa,CAAb,EAAgB,KAAhB,EAAuB,EAAvB;AACH;;AAAC,MAAIF,EAAE,GAAG,CAAT,EAAY;AACV,UAAMG,MAAM,GAAGf,EAAE,CAACgB,aAAH,EAAf;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,KAAd,EAAqBF,MAAM,CAACG,QAA5B,EAAsClB,EAAE,CAACmB,aAAzC;AACH;AAAE;;AACH,SAASC,8CAAT,CAAwDR,EAAxD,EAA4DC,GAA5D,EAAiE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3EZ,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV,EAAa,uEAAb;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACH;AAAE;;AACH,SAASC,oDAAT,CAA8DZ,EAA9D,EAAkEC,GAAlE,EAAuE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjFZ,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV,EAAa,+DAAb;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACH;AAAE;;AACH,SAASE,8CAAT,CAAwDb,EAAxD,EAA4DC,GAA5D,EAAiE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3EZ,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,CAAd,EAAiBF,oDAAjB,EAAuE,CAAvE,EAA0E,CAA1E,EAA6E,KAA7E,EAAoF,EAApF;AACAxB,IAAAA,EAAE,CAACuB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMe,MAAM,GAAG3B,EAAE,CAACgB,aAAH,EAAf;AACA,QAAIY,OAAJ;AACA5B,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB,CAACW,OAAO,GAAGD,MAAM,CAACG,oBAAP,CAA4BC,GAA5B,CAAgC,aAAhC,CAAX,KAA8D,IAA9D,GAAqE,IAArE,GAA4EH,OAAO,CAACI,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCJ,OAAO,CAACI,MAAR,CAAeC,OAAjJ;AACH;AAAE;;AACH,SAASC,0DAAT,CAAoEtB,EAApE,EAAwEC,GAAxE,EAA6E;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACvFZ,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV,EAAa,+DAAb;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACH;AAAE;;AACH,SAASY,oDAAT,CAA8DvB,EAA9D,EAAkEC,GAAlE,EAAuE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACjFZ,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,CAAd,EAAiBQ,0DAAjB,EAA6E,CAA7E,EAAgF,CAAhF,EAAmF,KAAnF,EAA0F,EAA1F;AACAlC,IAAAA,EAAE,CAACuB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMwB,OAAO,GAAGpC,EAAE,CAACgB,aAAH,EAAhB;AACA,UAAMqB,IAAI,GAAGD,OAAO,CAACE,KAArB;AACA,UAAMC,cAAc,GAAGH,OAAO,CAACI,SAA/B;AACA,UAAMC,OAAO,GAAGzC,EAAE,CAACgB,aAAH,EAAhB;AACA,QAAIY,OAAJ;AACA5B,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB,CAACW,OAAO,GAAGW,cAAc,CAACR,GAAf,CAAmBU,OAAO,CAACC,eAAR,CAAwBL,IAAxB,CAAnB,CAAX,KAAiE,IAAjE,GAAwE,IAAxE,GAA+ET,OAAO,CAACI,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCJ,OAAO,CAACI,MAAR,CAAeC,OAApJ;AACH;AAAE;;AACH,SAASU,2DAAT,CAAqE/B,EAArE,EAAyEC,GAAzE,EAA8E;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACxFZ,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV,EAAa,oDAAb;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACH;AAAE;;AACH,SAASqB,qDAAT,CAA+DhC,EAA/D,EAAmEC,GAAnE,EAAwE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAClFZ,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,CAAd,EAAiBiB,2DAAjB,EAA8E,CAA9E,EAAiF,CAAjF,EAAoF,KAApF,EAA2F,EAA3F;AACA3C,IAAAA,EAAE,CAACuB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM2B,cAAc,GAAGvC,EAAE,CAACgB,aAAH,GAAmBwB,SAA1C;AACA,QAAIZ,OAAJ;AACA5B,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB,CAACW,OAAO,GAAGW,cAAc,CAACR,GAAf,CAAmB,gBAAnB,CAAX,KAAoD,IAApD,GAA2D,IAA3D,GAAkEH,OAAO,CAACI,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgCJ,OAAO,CAACI,MAAR,CAAea,QAAvI;AACH;AAAE;;AACH,MAAMC,GAAG,GAAG,UAAUC,EAAV,EAAc;AAAE,SAAO;AAAE,sBAAkBA;AAApB,GAAP;AAAkC,CAA9D;;AACA,SAASC,8CAAT,CAAwDpC,EAAxD,EAA4DC,GAA5D,EAAiE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3EZ,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,EAA7B;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV,EAAa,MAAb;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACc,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAd,IAAAA,EAAE,CAAC0B,UAAH,CAAc,CAAd,EAAiBS,oDAAjB,EAAuE,CAAvE,EAA0E,CAA1E,EAA6E,KAA7E,EAAoF,EAApF;AACAnC,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,aAAd;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACc,SAAH,CAAa,EAAb,EAAiB,UAAjB,EAA6B,EAA7B;AACAd,IAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBkB,qDAAlB,EAAyE,CAAzE,EAA4E,CAA5E,EAA+E,KAA/E,EAAsF,EAAtF;AACA5C,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACuB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAM2B,cAAc,GAAG1B,GAAG,CAAC2B,SAA3B;AACA,UAAMH,IAAI,GAAGxB,GAAG,CAACyB,KAAjB;AACA,UAAMW,MAAM,GAAGjD,EAAE,CAACgB,aAAH,EAAf;AACA,QAAIkC,OAAJ;AACA,QAAIC,OAAJ;AACA,QAAIC,OAAJ;AACA,QAAIC,QAAJ;AACArD,IAAAA,EAAE,CAACiB,UAAH,CAAc,eAAd,EAA+BoB,IAA/B;AACArC,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACsD,iBAAH,CAAqBL,MAAM,CAACM,mBAAP,CAA2BlB,IAA3B,CAArB;AACArC,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,SAAd,EAAyBjB,EAAE,CAACwD,eAAH,CAAmB,EAAnB,EAAuBV,GAAvB,EAA4B,CAACI,OAAO,GAAGX,cAAc,CAACR,GAAf,CAAmBkB,MAAM,CAACP,eAAP,CAAuBL,IAAvB,CAAnB,CAAX,KAAgE,IAAhE,GAAuE,IAAvE,GAA8Ea,OAAO,CAACO,KAAlH,CAAzB,EAAmJ,KAAnJ,EAA0JR,MAAM,CAACP,eAAP,CAAuBL,IAAvB,IAA+B,OAAzL;AACArC,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,IAAd,EAAoBgC,MAAM,CAACP,eAAP,CAAuBL,IAAvB,IAA+B,OAAnD,EAA4D,iBAA5D,EAA+EY,MAAM,CAACP,eAAP,CAAuBL,IAAvB,CAA/E,EAA6G,UAA7G,EAAyHY,MAAM,CAACS,UAAhI;AACA1D,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB,CAAC,CAACkC,OAAO,GAAGZ,cAAc,CAACR,GAAf,CAAmBkB,MAAM,CAACP,eAAP,CAAuBL,IAAvB,CAAnB,CAAX,KAAgE,IAAhE,GAAuE,IAAvE,GAA8Ec,OAAO,CAACQ,OAAvF,MAAoG,CAACR,OAAO,GAAGZ,cAAc,CAACR,GAAf,CAAmBkB,MAAM,CAACP,eAAP,CAAuBL,IAAvB,CAAnB,CAAX,KAAgE,IAAhE,GAAuE,IAAvE,GAA8Ec,OAAO,CAACS,OAA1L,CAAtB;AACA5D,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,SAAd,EAAyBjB,EAAE,CAACwD,eAAH,CAAmB,EAAnB,EAAuBV,GAAvB,EAA4B,CAACM,OAAO,GAAGb,cAAc,CAACR,GAAf,CAAmBkB,MAAM,CAACP,eAAP,CAAuBL,IAAvB,CAAnB,CAAX,KAAgE,IAAhE,GAAuE,IAAvE,GAA8Ee,OAAO,CAACK,KAAlH,CAAzB;AACAzD,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,UAAd,EAA0BgC,MAAM,CAACS,UAAjC;AACA1D,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB,CAACoC,QAAQ,GAAGd,cAAc,CAACR,GAAf,CAAmB,gBAAnB,CAAZ,KAAqD,IAArD,GAA4D,IAA5D,GAAmEsB,QAAQ,CAACM,OAAlG;AACH;AAAE;;AACH,MAAME,GAAG,GAAG,UAAUd,EAAV,EAAce,EAAd,EAAkB;AAAE,SAAO;AAAE,2BAAuBf,EAAzB;AAA6B,6BAAyBe;AAAtD,GAAP;AAAoE,CAApG;;AACA,SAASC,wDAAT,CAAkEnD,EAAlE,EAAsEC,GAAtE,EAA2E;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACrF,UAAMoD,IAAI,GAAGhE,EAAE,CAACiE,gBAAH,EAAb;;AACAjE,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACArB,IAAAA,EAAE,CAACkE,UAAH,CAAc,OAAd,EAAuB,SAASC,gFAAT,GAA4F;AAAEnE,MAAAA,EAAE,CAACoE,aAAH,CAAiBJ,IAAjB;AAAwB,YAAMK,KAAK,GAAGrE,EAAE,CAACgB,aAAH,GAAmBsB,KAAjC;AAAwC,YAAMgC,OAAO,GAAGtE,EAAE,CAACgB,aAAH,EAAhB;AAAoC,aAAOsD,OAAO,CAACC,WAAR,CAAoBF,KAApB,CAAP;AAAoC,KAA7P;AACArE,IAAAA,EAAE,CAACc,SAAH,CAAa,CAAb,EAAgB,GAAhB,EAAqB,EAArB;AACAd,IAAAA,EAAE,CAACuB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMyD,KAAK,GAAGrE,EAAE,CAACgB,aAAH,GAAmBsB,KAAjC;AACA,UAAMkC,OAAO,GAAGxE,EAAE,CAACgB,aAAH,EAAhB;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,SAAd,EAAyBjB,EAAE,CAACyE,eAAH,CAAmB,CAAnB,EAAsBZ,GAAtB,EAA2B,CAACW,OAAO,CAACE,SAAR,CAAkBL,KAAlB,CAA5B,EAAsDG,OAAO,CAACE,SAAR,CAAkBL,KAAlB,CAAtD,CAAzB;AACArE,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,SAAd,EAAyBuD,OAAO,CAACE,SAAR,CAAkBL,KAAlB,IAA2B,UAA3B,GAAwC,SAAjE;AACH;AAAE;;AACH,SAASM,wDAAT,CAAkE/D,EAAlE,EAAsEC,GAAtE,EAA2E;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AACrF,UAAMgE,IAAI,GAAG5E,EAAE,CAACiE,gBAAH,EAAb;;AACAjE,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACArB,IAAAA,EAAE,CAACkE,UAAH,CAAc,OAAd,EAAuB,SAASW,gFAAT,GAA4F;AAAE7E,MAAAA,EAAE,CAACoE,aAAH,CAAiBQ,IAAjB;AAAwB,YAAMP,KAAK,GAAGrE,EAAE,CAACgB,aAAH,GAAmBsB,KAAjC;AAAwC,YAAMwC,OAAO,GAAG9E,EAAE,CAACgB,aAAH,EAAhB;AAAoC,aAAO8D,OAAO,CAACC,aAAR,CAAsBV,KAAtB,CAAP;AAAsC,KAA/P;AACArE,IAAAA,EAAE,CAACc,SAAH,CAAa,CAAb,EAAgB,GAAhB,EAAqB,EAArB;AACAd,IAAAA,EAAE,CAACuB,YAAH;AACH;AAAE;;AACH,SAASyD,8CAAT,CAAwDpE,EAAxD,EAA4DC,GAA5D,EAAiE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC3E,UAAMqE,IAAI,GAAGjF,EAAE,CAACiE,gBAAH,EAAb;;AACAjE,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV,EAAa,eAAb;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACc,SAAH,CAAa,CAAb,EAAgB,OAAhB,EAAyB,EAAzB;AACAd,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,EAA5B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,OAArB,EAA8B,EAA9B;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV,EAAa,MAAb;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACc,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAd,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,UAAd;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACc,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAd,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACArB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,gBAAd;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACArB,IAAAA,EAAE,CAACkE,UAAH,CAAc,QAAd,EAAwB,SAASgB,uEAAT,CAAiFC,MAAjF,EAAyF;AAAE,YAAMC,WAAW,GAAGpF,EAAE,CAACoE,aAAH,CAAiBa,IAAjB,CAApB;AAA4C,YAAMZ,KAAK,GAAGe,WAAW,CAAC9C,KAA1B;AAAiC,YAAM+C,OAAO,GAAGrF,EAAE,CAACgB,aAAH,EAAhB;AAAoC,aAAOqE,OAAO,CAACC,eAAR,CAAwBH,MAAxB,EAAgCd,KAAhC,CAAP;AAAgD,KAApR;AACArE,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACArB,IAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBqC,wDAAlB,EAA4E,CAA5E,EAA+E,CAA/E,EAAkF,QAAlF,EAA4F,EAA5F;AACA/D,IAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBiD,wDAAlB,EAA4E,CAA5E,EAA+E,CAA/E,EAAkF,QAAlF,EAA4F,EAA5F;AACA3E,IAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B,EAAmC,EAAnC;AACArB,IAAAA,EAAE,CAACc,SAAH,CAAa,EAAb,EAAiB,QAAjB,EAA2B,EAA3B;AACAd,IAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,oDAAd;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACuB,YAAH;AACAvB,IAAAA,EAAE,CAACuB,YAAH;AACH;;AAAC,MAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAMyD,KAAK,GAAGxD,GAAG,CAACyB,KAAlB;AACA,UAAMiD,MAAM,GAAGvF,EAAE,CAACgB,aAAH,EAAf;AACAhB,IAAAA,EAAE,CAACiB,UAAH,CAAc,eAAd,EAA+BoD,KAA/B;AACArE,IAAAA,EAAE,CAAC6B,SAAH,CAAa,EAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsBsE,MAAM,CAACC,SAAP,CAAiBnB,KAAjB,CAAtB;AACArE,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsBsE,MAAM,CAACE,gBAAP,CAAwBC,MAAxB,GAAiC,CAAvD;AACA1F,IAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,IAAAA,EAAE,CAACiB,UAAH,CAAc,KAAd,EAAqBsE,MAAM,CAACC,SAAP,CAAiBnB,KAAjB,CAArB,EAA8CrE,EAAE,CAACmB,aAAjD;AACH;AAAE;;AACH,SAASwE,iDAAT,CAA2D/E,EAA3D,EAA+DC,GAA/D,EAAoE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC9E,UAAMgF,IAAI,GAAG5F,EAAE,CAACiE,gBAAH,EAAb;;AACAjE,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACArB,IAAAA,EAAE,CAACkE,UAAH,CAAc,OAAd,EAAuB,SAAS2B,yEAAT,GAAqF;AAAE7F,MAAAA,EAAE,CAACoE,aAAH,CAAiBwB,IAAjB;AAAwB,YAAME,OAAO,GAAG9F,EAAE,CAACgB,aAAH,EAAhB;AAAoC,aAAO8E,OAAO,CAACC,UAAR,EAAP;AAA8B,KAAxM;AACA/F,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV,EAAa,cAAb;AACAtB,IAAAA,EAAE,CAACc,SAAH,CAAa,CAAb,EAAgB,GAAhB,EAAqB,EAArB;AACAd,IAAAA,EAAE,CAACuB,YAAH;AACH;AAAE;;AACH,SAASyE,iDAAT,CAA2DpF,EAA3D,EAA+DC,GAA/D,EAAoE;AAAE,MAAID,EAAE,GAAG,CAAT,EAAY;AAC9EZ,IAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,QAArB,EAA+B,EAA/B;AACArB,IAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV,EAAa,MAAb;AACAtB,IAAAA,EAAE,CAACuB,YAAH;AACH;AAAE;;AACH,OAAO,MAAM0E,8BAAN,CAAqC;AACxCC,EAAAA,WAAW,CAACC,WAAD,EAAcC,mBAAd,EAAmCC,YAAnC,EAAiDC,IAAjD,EAAuDC,iBAAvD,EAA0EC,MAA1E,EAAkFC,MAAlF,EAA0FC,KAA1F,EAAiGC,QAAjG,EAA2G;AAClH,QAAIC,EAAJ;;AACA,SAAKT,WAAL,GAAmBA,WAAnB;AACA,SAAKC,mBAAL,GAA2BA,mBAA3B;AACA,SAAKC,YAAL,GAAoBA,YAApB;AACA,SAAKC,IAAL,GAAYA,IAAZ;AACA,SAAKC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,MAAL,GAAcA,MAAd;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKE,CAAL,GAAS,CAAT;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,KAAL,GAAa,SAAb;AACA,SAAKtD,UAAL,GAAkB,KAAlB;AACA,SAAKuD,OAAL,GAAe,4CAAf;AACA,SAAK1D,mBAAL,GAA2B,CACvB,aADuB,EAEvB,wBAFuB,EAGvB,qBAHuB,EAIvB,QAJuB,EAKvB,kBALuB,EAMvB,MANuB,CAA3B;AAQA,SAAKb,eAAL,GAAuB,CACnB,gBADmB,EAEnB,yBAFmB,EAGnB,sBAHmB,EAInB,WAJmB,EAKnB,oBALmB,EAMnB,SANmB,CAAvB;AAQA,SAAKwE,UAAL,GAAkB,EAAlB;AACA,SAAKC,cAAL,GAAsB,CAAtB;AACA,SAAKzC,SAAL,GAAiB,EAAjB,CAnCkH,CAmC7F;;AACrB,SAAKc,SAAL,GAAiB,EAAjB,CApCkH,CAoC7F;;AACrB,SAAK4B,kBAAL,GAA0B,KAA1B;AACA,SAAKC,kBAAL,GAA0B,EAA1B;AACA,SAAKC,aAAL,GAAqB,EAArB;AACA,SAAKxF,oBAAL,GAA4B,KAAKqE,WAAL,CAAiBoB,KAAjB,CAAuB;AAC/CC,MAAAA,SAAS,EAAE,CAAC,EAAD,EAAK,CAAC1H,UAAU,CAAC+C,QAAZ,CAAL,CADoC;AAE/C4E,MAAAA,MAAM,EAAE,CAAC,EAAD,CAFuC;AAG/CC,MAAAA,WAAW,EAAE,CAAC,EAAD,EAAK,CAAC5H,UAAU,CAAC+C,QAAZ,EAAsB/C,UAAU,CAACmC,OAAX,CAAmB,aAAnB,CAAtB,CAAL,CAHkC;AAI/C0F,MAAAA,WAAW,EAAE,CAAC,EAAD,EAAK,CAAC7H,UAAU,CAAC+C,QAAZ,CAAL,CAJkC;AAK/C+E,MAAAA,gBAAgB,EAAE,KAAKzB,WAAL,CAAiB0B,KAAjB,CAAuB,KAAKnF,eAAL,CAAqBoF,GAArB,CAAyB,CAACC,GAAD,EAAMzF,KAAN,KAAgB,KAAK0F,sBAAL,CAA4BD,GAA5B,CAAzC,CAAvB,CAL6B;AAM/CE,MAAAA,cAAc,EAAE,KAAK9B,WAAL,CAAiB0B,KAAjB,CAAuB,CAAC,KAAKK,aAAL,EAAD,CAAvB;AAN+B,KAAvB,CAA5B;AAQA,SAAKxB,KAAL,CAAWyB,WAAX,CAAuBC,SAAvB,CAAiCC,KAAK,IAAI;AACtC,WAAKC,MAAL,GAAcD,KAAK,CAAC,QAAD,CAAnB;AACAE,MAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ,EAAsB,KAAKF,MAA3B;AACH,KAHD;AAIA,UAAMG,KAAK,GAAG,CAAC7B,EAAE,GAAG,KAAKJ,MAAL,CAAYkC,oBAAZ,EAAN,MAA8C,IAA9C,IAAsD9B,EAAE,KAAK,KAAK,CAAlE,GAAsE,KAAK,CAA3E,GAA+EA,EAAE,CAAC+B,MAAH,CAAUF,KAAvG;;AACA,QAAIA,KAAJ,EAAW;AACN,WAAKzB,KAAL,GAAayB,KAAK,CAACzB,KAApB;AACH,KAFD,MAGK;AACD,WAAKR,MAAL,CAAYoC,QAAZ,CAAqB,CAAE,uBAAF,CAArB;AACH;AACJ;;AACDC,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAK7B,KAAL,KAAe,MAAnB,EAA2B;AACvB,WAAK8B,gBAAL,CAAsB,KAAKR,MAA3B;AACH;AACJ;;AACKQ,EAAAA,gBAAgB,CAACR,MAAD,EAAS;AAAA;;AAAA;AAC3B,UAAI;AACA,cAAMS,GAAG,SAAS,KAAI,CAAC3C,mBAAL,CAAyB0C,gBAAzB,CAA0CR,MAA1C,EAAkDU,SAAlD,EAAlB;AACAT,QAAAA,OAAO,CAACC,GAAR,CAAY,sBAAZ,EAAoCO,GAApC;;AACA,YAAIA,GAAG,IAAIA,GAAG,CAACE,IAAf,EAAqB;AACjB,UAAA,KAAI,CAACC,cAAL,GAAsBH,GAAG,CAACE,IAA1B;;AACA,UAAA,KAAI,CAACE,SAAL,CAAe,KAAI,CAACD,cAApB;AACH,SAHD,MAIK;AACDX,UAAAA,OAAO,CAACa,KAAR,CAAc,mDAAd;AACH;AACJ,OAVD,CAWA,OAAOA,KAAP,EAAc;AACVb,QAAAA,OAAO,CAACa,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;AACH;AAd0B;AAe9B;;AACDD,EAAAA,SAAS,CAACF,IAAD,EAAO;AACZ,QAAIrC,EAAJ;;AACA,QAAI,KAAKI,KAAL,KAAe,MAAnB,EAA2B;AACvB,YAAMqC,YAAY,GAAG,KAAKvH,oBAAL,CAA0BC,GAA1B,CAA8B,kBAA9B,CAArB;;AACA,UAAIkH,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACrB,gBAArD,EAAuE;AACnEW,QAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ;AACA,SAAC5B,EAAE,GAAGqC,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACrB,gBAAvD,MAA6E,IAA7E,IAAqFhB,EAAE,KAAK,KAAK,CAAjG,GAAqG,KAAK,CAA1G,GAA8GA,EAAE,CAAC0C,OAAH,CAAW,CAACC,WAAD,EAAcjH,KAAd,KAAwB;AAC7I,gBAAMkH,OAAO,GAAG,KAAK9G,eAAL,CAAqBJ,KAArB,CAAhB;AACA,gBAAMmH,SAAS,GAAGJ,YAAY,CAACK,EAAb,CAAgBpH,KAAhB,CAAlB;;AACA,cAAImH,SAAJ,EAAe;AACXA,YAAAA,SAAS,CAACE,UAAV,CAAqB;AACjB,eAACH,OAAD,GAAWD,WAAW,CAACC,OAAD,CAAX,IAAwB,EADlB;AAEjBI,cAAAA,cAAc,EAAEL,WAAW,CAACK,cAAZ,IAA8B;AAF7B,aAArB;AAIH;AACJ,SAT6G,CAA9G;AAUH;;AACD,WAAK9H,oBAAL,CAA0B6H,UAA1B,CAAqC;AACjCnC,QAAAA,SAAS,EAAEyB,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACzB,SAD3B;AAEjCE,QAAAA,WAAW,EAAEuB,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACvB,WAF7B;AAGjCC,QAAAA,WAAW,EAAEsB,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACtB;AAH7B,OAArC;;AAKA,UAAIsB,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAAChB,cAArD,EAAqE;AACjE,cAAM4B,eAAe,GAAG,KAAK/H,oBAAL,CAA0BC,GAA1B,CAA8B,gBAA9B,CAAxB;AACA8H,QAAAA,eAAe,CAACC,KAAhB;AACAb,QAAAA,IAAI,CAAChB,cAAL,CAAoBqB,OAApB,CAA6BS,OAAD,IAAa;AACrC,gBAAMC,gBAAgB,GAAG,KAAK7D,WAAL,CAAiBoB,KAAjB,CAAuB;AAC5C0C,YAAAA,SAAS,EAAE,CAACF,OAAO,CAACE,SAAT,CADiC;AAE5CC,YAAAA,QAAQ,EAAE,CAACH,OAAO,CAACG,QAAT,CAFkC;AAG5CC,YAAAA,YAAY,EAAE,CAACJ,OAAO,CAACI,YAAT,CAH8B;AAI5CC,YAAAA,QAAQ,EAAE,CAACL,OAAO,CAACK,QAAT;AAJkC,WAAvB,CAAzB;AAMAP,UAAAA,eAAe,CAACQ,IAAhB,CAAqBL,gBAArB;AACA,eAAKxE,SAAL,GAAiByD,IAAI,CAAChB,cAAL,CAAoBH,GAApB,CAAyBiC,OAAD,IAAaA,OAAO,CAACK,QAA7C,CAAjB;AACA,eAAK1F,SAAL,GAAiB,IAAI4F,KAAJ,CAAUrB,IAAI,CAAChB,cAAL,CAAoBvC,MAA9B,EAAsC6E,IAAtC,CAA2C,KAA3C,CAAjB;AACA,eAAKlD,kBAAL,GAA0B4B,IAAI,CAAChB,cAAL,CAAoBH,GAApB,CAAyBiC,OAAD,IAAaA,OAAO,CAACK,QAAR,CAAiBI,SAAjB,CAA2BT,OAAO,CAACK,QAAR,CAAiBK,WAAjB,CAA6B,GAA7B,IAAoC,CAA/D,CAArC,CAA1B;AACH,SAXD;AAYH;AACJ;AACJ;;AACDC,EAAAA,gBAAgB,GAAG;AACf,QAAI9D,EAAJ;;AACA,QAAI,KAAK9E,oBAAL,CAA0B6B,OAA9B,EAAuC;AACnCgH,MAAAA,MAAM,CAACC,IAAP,CAAY,KAAK9I,oBAAL,CAA0B+I,QAAtC,EAAgDvB,OAAhD,CAAwDwB,IAAI,IAAI;AAC5D,cAAMC,OAAO,GAAG,KAAKjJ,oBAAL,CAA0BC,GAA1B,CAA8B+I,IAA9B,CAAhB;;AACA,YAAIC,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACpH,OAA9D,EAAuE;AACnE4E,UAAAA,OAAO,CAACC,GAAR,CAAa,oBAAmBsC,IAAK,WAArC,EAAiDC,OAAO,CAAC/I,MAAzD;AACH;AACJ,OALD;AAMA,WAAKyE,MAAL,CAAYuE,IAAZ,CAAiB,2CAAjB;AACA;AACH;;AACD,UAAMC,QAAQ,GAAG,CAAC,CAACrE,EAAE,GAAG,KAAK9E,oBAAL,CAA0BC,GAA1B,CAA8B,QAA9B,CAAN,MAAmD,IAAnD,IAA2D6E,EAAE,KAAK,KAAK,CAAvE,GAA2E,KAAK,CAAhF,GAAoFA,EAAE,CAACnD,KAAxF,IAAiG,KAAKyH,aAAL,EAAjG,GAAwHC,OAAO,CAACC,OAAR,CAAgB,KAAKlC,cAAL,CAAoBzB,MAApC,CAAzI;AACA,SAAKlB,iBAAL,CAAuB8E,IAAvB,CAA4B,eAA5B;AACAJ,IAAAA,QAAQ,CAACK,IAAT,CAAeC,KAAD,IAAW;AACrB,aAAO,KAAKC,gBAAL,CAAsB,KAAKtE,UAA3B,EAAuCoE,IAAvC,CAA6C9F,SAAD,IAAe;AAC9D,YAAIoB,EAAJ,EAAQ6E,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB;;AACArD,QAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqC,KAAKtB,UAA1C;AACA,aAAKzB,gBAAL,CAAsBoF,QAAtB,CAA+BvB,OAA/B,CAAuC,CAACyB,OAAD,EAAUzI,KAAV,KAAoB;AACvD,cAAIsE,EAAJ;;AACA,WAACA,EAAE,GAAGmE,OAAO,CAAChJ,GAAR,CAAY,UAAZ,CAAN,MAAmC,IAAnC,IAA2C6E,EAAE,KAAK,KAAK,CAAvD,GAA2D,KAAK,CAAhE,GAAoEA,EAAE,CAACiF,QAAH,CAAY,KAAKrG,SAAL,CAAelD,KAAf,CAAZ,CAApE;AACH,SAHD;AAIA,cAAM2G,IAAI,GAAG;AACTX,UAAAA,MAAM,EAAE,KAAKA,MADJ;AAETd,UAAAA,SAAS,EAAE,CAACZ,EAAE,GAAG,KAAK9E,oBAAL,CAA0BC,GAA1B,CAA8B,WAA9B,CAAN,MAAsD,IAAtD,IAA8D6E,EAAE,KAAK,KAAK,CAA1E,GAA8E,KAAK,CAAnF,GAAuFA,EAAE,CAACnD,KAF5F;AAGTgE,UAAAA,MAAM,EAAE8D,KAAK,GAAGA,KAAH,GAAW,EAHf;AAIT7D,UAAAA,WAAW,EAAE,CAAC+D,EAAE,GAAG,KAAK3J,oBAAL,CAA0BC,GAA1B,CAA8B,aAA9B,CAAN,MAAwD,IAAxD,IAAgE0J,EAAE,KAAK,KAAK,CAA5E,GAAgF,KAAK,CAArF,GAAyFA,EAAE,CAAChI,KAJhG;AAKTkE,UAAAA,WAAW,EAAE,CAAC+D,EAAE,GAAG,KAAK5J,oBAAL,CAA0BC,GAA1B,CAA8B,aAA9B,CAAN,MAAwD,IAAxD,IAAgE2J,EAAE,KAAK,KAAK,CAA5E,GAAgF,KAAK,CAArF,GAAyFA,EAAE,CAACjI,KALhG;AAMTmE,UAAAA,gBAAgB,EAAE,CAAC+D,EAAE,GAAG,KAAK7J,oBAAL,CAA0BC,GAA1B,CAA8B,kBAA9B,CAAN,MAA6D,IAA7D,IAAqE4J,EAAE,KAAK,KAAK,CAAjF,GAAqF,KAAK,CAA1F,GAA8FA,EAAE,CAAClI,KAN1G;AAOTwE,UAAAA,cAAc,EAAE,CAAC2D,EAAE,GAAG,KAAK9J,oBAAL,CAA0BC,GAA1B,CAA8B,gBAA9B,CAAN,MAA2D,IAA3D,IAAmE6J,EAAE,KAAK,KAAK,CAA/E,GAAmF,KAAK,CAAxF,GAA4FA,EAAE,CAACnI,KAPtG;AAQTqI,UAAAA,QAAQ,EAAE,EARD;AASTC,UAAAA,WAAW,EAAE,EATJ;AAUTC,UAAAA,SAAS,EAAE;AAVF,SAAb;AAYAzD,QAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCS,IAAjC;AACA,eAAO,KAAK7C,mBAAL,CAAyB6F,eAAzB,CAAyChD,IAAzC,EAA+CD,SAA/C,EAAP;AACH,OArBM,CAAP;AAsBH,KAvBD,EAuBGsC,IAvBH,CAuBSvC,GAAD,IAAS;AACb,WAAKxC,iBAAL,CAAuB2F,IAAvB,CAA4B,eAA5B;;AACA,UAAInD,GAAG,CAACoD,UAAJ,KAAmB,GAAvB,EAA4B;AACxB,aAAK1F,MAAL,CAAY2F,OAAZ,CAAoB,kCAApB;AACA,aAAK5F,MAAL,CAAYoC,QAAZ,CAAqB,CAAC,sBAAD,CAArB;AACH,OAHD,MAIK;AACD,aAAKnC,MAAL,CAAY2C,KAAZ,CAAkB,uBAAlB;AACH;AACJ,KAhCD,EAgCGiD,KAhCH,CAgCUjD,KAAD,IAAW;AAChB,WAAK7C,iBAAL,CAAuB2F,IAAvB,CAA4B,eAA5B;AACA,YAAMI,YAAY,GAAGlD,KAAK,KAAK,2BAAV,GAAwCA,KAAxC,GAAgD,4BAArE;AACA,WAAK3C,MAAL,CAAY2C,KAAZ,CAAkBkD,YAAlB;AACH,KApCD;AAqCH;;AACe,MAAZjD,YAAY,GAAG;AACf,WAAO,KAAKvH,oBAAL,CAA0BC,GAA1B,CAA8B,kBAA9B,CAAP;AACH;;AACmB,MAAhB0D,gBAAgB,GAAG;AACnB,WAAO,KAAK3D,oBAAL,CAA0BC,GAA1B,CAA8B,gBAA9B,CAAP;AACH;;AACDiG,EAAAA,sBAAsB,CAACwB,OAAD,EAAU;AAC5B,QAAIjC,KAAK,GAAG,EAAZ;AACAA,IAAAA,KAAK,CAACiC,OAAD,CAAL,GAAiB,CAAC,EAAD,EAAK,CAAC1J,UAAU,CAACmC,OAAX,CAAmB,aAAnB,CAAD,CAAL,CAAjB;AACAsF,IAAAA,KAAK,CAAC,gBAAD,CAAL,GAA0B,CAAC,EAAD,CAA1B;AACA,UAAMkC,SAAS,GAAG,KAAKtD,WAAL,CAAiBoB,KAAjB,CAAuBA,KAAvB,CAAlB,CAJ4B,CAK5B;;AACAkC,IAAAA,SAAS,CAAC8C,aAAV,CAAwB,KAAKC,wBAAL,CAA8BhD,OAA9B,EAAuC,gBAAvC,CAAxB;AACA,WAAOC,SAAP;AACH;;AACD+C,EAAAA,wBAAwB,CAAChD,OAAD,EAAUiD,cAAV,EAA0B;AAC9C,WAAQhD,SAAD,IAAe;AAClB,YAAMiD,WAAW,GAAGjD,SAAS,CAAC1H,GAAV,CAAcyH,OAAd,CAApB;AACA,YAAMmD,kBAAkB,GAAGlD,SAAS,CAAC1H,GAAV,CAAc0K,cAAd,CAA3B;;AACA,UAAI,CAACC,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACjJ,KAAvE,KAAiF,EAAEkJ,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,kBAAkB,CAAClJ,KAA7F,CAArF,EAA0L;AACtLkJ,QAAAA,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,kBAAkB,CAACC,SAAnB,CAA6B;AAAE/J,UAAAA,QAAQ,EAAE;AAAZ,SAA7B,CAAxE;AACA,eAAO;AAAEgK,UAAAA,mBAAmB,EAAE;AAAvB,SAAP;AACH,OAHD,MAIK;AACDF,QAAAA,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,kBAAkB,CAACC,SAAnB,CAA6B,IAA7B,CAAxE;AACA,eAAO,IAAP;AACH;AACJ,KAXD;AAYH;;AACD7G,EAAAA,UAAU,GAAG;AACT,SAAKN,gBAAL,CAAsB4E,IAAtB,CAA2B,KAAKnC,aAAL,EAA3B;AACH;;AACDnD,EAAAA,aAAa,CAACzC,KAAD,EAAQ;AACjB,SAAKmD,gBAAL,CAAsBqH,QAAtB,CAA+BxK,KAA/B;AACA,SAAKkD,SAAL,CAAeuH,MAAf,CAAsBzK,KAAtB,EAA6B,CAA7B,EAFiB,CAEgB;;AACjC,SAAKoC,SAAL,CAAeqI,MAAf,CAAsBzK,KAAtB,EAA6B,CAA7B,EAHiB,CAGgB;;AACjC,SAAK4E,UAAL,CAAgB6F,MAAhB,CAAuBzK,KAAvB,EAA8B,CAA9B,EAJiB,CAIiB;AACrC;;AACDiC,EAAAA,WAAW,CAACjC,KAAD,EAAQ;AACf,UAAM0K,aAAa,GAAG,KAAKC,YAAL,CAAkBC,OAAlB,EAAtB;AACA,UAAMC,YAAY,GAAGH,aAAa,CAAC1K,KAAD,CAAb,CAAqB8K,aAA1C,CAFe,CAGf;;AACA,SAAKC,YAAL,CAAkB/K,KAAlB,EAJe,CAKf;;AACA,QAAI,KAAKoC,SAAL,CAAepC,KAAf,CAAJ,EAA2B;AACvB6K,MAAAA,YAAY,CAACG,KAAb;AACH,KAFD,MAGK;AACDH,MAAAA,YAAY,CAACI,GAAb,GAAmB,KAAK/H,SAAL,CAAelD,KAAf,CAAnB;AACA6K,MAAAA,YAAY,CAACK,IAAb;AACH,KAZc,CAaf;;;AACA,SAAK9I,SAAL,CAAepC,KAAf,IAAwB,CAAC,KAAKoC,SAAL,CAAepC,KAAf,CAAzB;AACH;;AACD+K,EAAAA,YAAY,CAACI,YAAD,EAAe;AACvB,SAAKR,YAAL,CAAkB3D,OAAlB,CAA0B,CAACoE,WAAD,EAAcpL,KAAd,KAAwB;AAC9C,UAAIA,KAAK,KAAKmL,YAAd,EAA4B;AACxBC,QAAAA,WAAW,CAACN,aAAZ,CAA0BE,KAA1B;AACA,aAAK5I,SAAL,CAAepC,KAAf,IAAwB,KAAxB;AACH;AACJ,KALD;AAMH;;AACDqL,EAAAA,SAAS,CAACrL,KAAD,EAAQ;AACb,UAAM0K,aAAa,GAAG,KAAKC,YAAL,CAAkBC,OAAlB,EAAtB;AACA,UAAMC,YAAY,GAAGH,aAAa,CAAC1K,KAAD,CAAb,CAAqB8K,aAA1C;AACAD,IAAAA,YAAY,CAACG,KAAb;AACAH,IAAAA,YAAY,CAACS,WAAb,GAA2B,CAA3B,CAJa,CAIiB;;AAC9B,SAAKlJ,SAAL,CAAepC,KAAf,IAAwB,KAAxB;AACH;;AACD4F,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAK/B,WAAL,CAAiBoB,KAAjB,CAAuB;AAC1B0C,MAAAA,SAAS,EAAE,CAAC,EAAD,CADe;AAE1BC,MAAAA,QAAQ,EAAE,CAAC,EAAD,CAFgB;AAG1BC,MAAAA,YAAY,EAAE,CAAC,EAAD,CAHY;AAI1BC,MAAAA,QAAQ,EAAE,CAAC,IAAD;AAJgB,KAAvB,CAAP;AAMA,SAAK1F,SAAL,CAAe2F,IAAf,CAAoB,KAApB,EAPY,CAOgB;;AAC5B,SAAK7E,SAAL,CAAe6E,IAAf,CAAoB,EAApB,EARY,CAQa;AAC5B;;AACD/E,EAAAA,eAAe,CAACuI,KAAD,EAAQvL,KAAR,EAAe;AAC1B,QAAIwL,SAAJ;AACA,UAAMC,YAAY,GAAGF,KAAK,CAACG,MAAN,CAAaC,KAAb,CAAmB,CAAnB,CAArB;;AACA,QAAIF,YAAJ,EAAkB;AACd,YAAMG,WAAW,GAAGnO,aAAa,CAACoO,YAAd,CAA2BJ,YAAY,CAACjD,IAAxC,CAApB;AACAgD,MAAAA,SAAS,GAAG,IAAIM,IAAJ,CAAS,CAACL,YAAD,CAAT,EAAyBG,WAAzB,EAAsC;AAAEG,QAAAA,IAAI,EAAEN,YAAY,CAACM;AAArB,OAAtC,CAAZ;AACH,KAHD,MAIK;AACDP,MAAAA,SAAS,GAAG,IAAZ;AACH;;AACD,QAAI,CAACA,SAAL,EAAgB;AACZ,WAAKtI,SAAL,CAAelD,KAAf,IAAwB,EAAxB;AACA,WAAKqL,SAAL,CAAerL,KAAf;AACA;AACH;;AACD,UAAMgM,aAAa,GAAGR,SAAS,CAACO,IAAV,CAAeE,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAtB,CAf0B,CAe0B;;AACpD,QAAID,aAAa,KAAK,OAAtB,EAA+B;AAC3B;AACAT,MAAAA,KAAK,CAACG,MAAN,CAAavK,KAAb,GAAqB,EAArB;AACA,WAAKgD,MAAL,CAAYuE,IAAZ,CAAiB,8BAAjB;AACA;AACH,KArByB,CAsB1B;;;AACA,SAAK9D,UAAL,CAAgB5E,KAAhB,IAAyBwL,SAAzB;AACAvF,IAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4BsF,SAA5B;AACA,UAAMU,MAAM,GAAG,IAAIC,UAAJ,EAAf;;AACAD,IAAAA,MAAM,CAACE,MAAP,GAAgB,MAAM;AAClB;AACA,WAAKlJ,SAAL,CAAelD,KAAf,IAAwBkM,MAAM,CAACG,MAA/B;AACH,KAHD;;AAIAH,IAAAA,MAAM,CAACI,aAAP,CAAqBd,SAArB;AACH;;AACDtC,EAAAA,gBAAgB,CAACyC,KAAD,EAAQ;AACpB,UAAMY,cAAc,GAAGZ,KAAK,CAACnG,GAAN,CAAU,CAACgH,IAAD,EAAOxM,KAAP,KAAiB,KAAKyM,qBAAL,CAA2BD,IAA3B,EAAiCxM,KAAjC,CAA3B,CAAvB;AACA,WAAO6I,OAAO,CAAC6D,GAAR,CAAYH,cAAZ,CAAP;AACH,GAhSuC,CAiSxC;;;AACAE,EAAAA,qBAAqB,CAACD,IAAD,EAAOxM,KAAP,EAAc;AAC/B,WAAO,IAAI6I,OAAJ,CAAY,CAACC,OAAD,EAAU6D,MAAV,KAAqB;AACpC,WAAK7I,mBAAL,CAAyB8I,SAAzB,CAAmCJ,IAAnC,EAAyC1G,SAAzC,CAAoDW,GAAD,IAAS;AACxDR,QAAAA,OAAO,CAACC,GAAR,CAAY,mBAAZ,EAAiCsG,IAAI,CAAChE,IAAtC;AACA,cAAMqE,OAAO,GAAG,KAAKlI,OAAL,GAAe6H,IAAI,CAAChE,IAApC;AACA,aAAKtF,SAAL,CAAelD,KAAf,IAAwB6M,OAAxB;AACA/D,QAAAA,OAAO,CAAC+D,OAAD,CAAP;AACH,OALD,EAKI/F,KAAD,IAAW;AACVb,QAAAA,OAAO,CAACa,KAAR,CAAc,cAAd,EAA8BA,KAA9B;AACA,aAAK3C,MAAL,CAAY2C,KAAZ,CAAkB,6BAAlB;AACA6F,QAAAA,MAAM,CAAC7F,KAAD,CAAN;AACH,OATD;AAUH,KAXM,CAAP;AAYH;;AACDgG,EAAAA,cAAc,CAACvB,KAAD,EAAQ;AAClB,QAAIjH,EAAJ;;AACA,QAAImH,YAAY,GAAGF,KAAK,CAACG,MAAN,CAAaC,KAAb,CAAmB,CAAnB,CAAnB;;AACA,QAAIJ,KAAK,CAACG,MAAN,CAAaC,KAAb,CAAmBvI,MAAnB,KAA8B,CAAlC,EAAqC;AACjC;AACA,WAAK2J,SAAL,GAAiB,IAAjB;AACA,WAAKnO,QAAL,GAAgB,IAAhB;AACA;AACH;;AACD,UAAMgN,WAAW,GAAGnO,aAAa,CAACoO,YAAd,CAA2BJ,YAAY,CAACjD,IAAxC,CAApB;AACA,SAAKuE,SAAL,GAAiB,IAAIjB,IAAJ,CAAS,CAACL,YAAD,CAAT,EAAyBG,WAAzB,EAAsC;AAAEG,MAAAA,IAAI,EAAEN,YAAY,CAACM;AAArB,KAAtC,CAAjB;;AACA,QAAI,KAAKgB,SAAT,EAAoB;AAChB,YAAMC,WAAW,GAAG,KAAKxN,oBAAL,CAA0BC,GAA1B,CAA8B,QAA9B,CAApB;AACAuN,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAAC/C,aAAZ,CAA0BxM,aAAa,CAACwP,iBAAd,CAAgC,IAAhC,EAAsC,KAAKF,SAA3C,CAA1B,CAA1D;AACAC,MAAAA,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACE,sBAAZ,EAA1D;AACH;;AACD,UAAMC,QAAQ,GAAG,KAAKJ,SAAL,CAAehB,IAAf,CAAoBE,KAApB,CAA0B,GAA1B,EAA+B,CAA/B,CAAjB;AACA,UAAMmB,aAAa,GAAG,CAAC9I,EAAE,GAAG,KAAKyI,SAAL,CAAevE,IAAf,CAAoByD,KAApB,CAA0B,GAA1B,EAA+BoB,GAA/B,EAAN,MAAgD,IAAhD,IAAwD/I,EAAE,KAAK,KAAK,CAApE,GAAwE,KAAK,CAA7E,GAAiFA,EAAE,CAACgJ,WAAH,EAAvG;;AACA,QAAIH,QAAQ,KAAK,OAAb,IAAwBC,aAAa,KAAK,KAA9C,EAAqD;AACjD7B,MAAAA,KAAK,CAACG,MAAN,CAAavK,KAAb,GAAqB,EAArB;AACA,WAAKgD,MAAL,CAAYuE,IAAZ,CAAiB,8CAAjB;AACA,WAAKqE,SAAL,GAAiB,IAAjB;AACA,WAAKnO,QAAL,GAAgB,IAAhB;AACA;AACH;;AACD,QAAI,KAAKmO,SAAL,IAAkBI,QAAQ,IAAI,OAAlC,EAA2C;AACvC,YAAMjB,MAAM,GAAG,IAAIC,UAAJ,EAAf;;AACAD,MAAAA,MAAM,CAACE,MAAP,GAAiBmB,CAAD,IAAO;AACnB,YAAIjJ,EAAJ;;AACA,aAAK1F,QAAL,GAAgB,CAAC0F,EAAE,GAAGiJ,CAAC,CAAC7B,MAAR,MAAoB,IAApB,IAA4BpH,EAAE,KAAK,KAAK,CAAxC,GAA4C,KAAK,CAAjD,GAAqDA,EAAE,CAAC+H,MAAxE;AACH,OAHD;;AAIAH,MAAAA,MAAM,CAACI,aAAP,CAAqB,KAAKS,SAA1B;AACH,KAPD,MAQK;AACD,WAAKnO,QAAL,GAAgB,IAAhB,CADC,CACqB;AACzB;;AACDqH,IAAAA,OAAO,CAACC,GAAR,CAAY,WAAZ,EAAyB,KAAK6G,SAA9B;AACH;;AACDnE,EAAAA,aAAa,GAAG;AACZ,WAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAU6D,MAAV,KAAqB;AACpC,UAAI,CAAC,KAAKI,SAAV,EAAqB;AACjBJ,QAAAA,MAAM,CAAC,yBAAD,CAAN;AACA;AACH;;AACD1G,MAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgC,KAAK6G,SAArC;AACA,WAAKjJ,mBAAL,CAAyB8I,SAAzB,CAAmC,KAAKG,SAAxC,EAAmDjH,SAAnD,CAA8DW,GAAD,IAAS;AAClE,cAAMoG,OAAO,GAAG,KAAKlI,OAAL,GAAe,KAAKoI,SAAL,CAAevE,IAA9C,CADkE,CACd;;AACpDM,QAAAA,OAAO,CAAC+D,OAAD,CAAP;AACH,OAHD,EAGI/F,KAAD,IAAW;AACV6F,QAAAA,MAAM,CAAC,4BAA4B7F,KAAK,CAAC0G,OAAlC,IAA6C1G,KAA9C,CAAN;AACH,OALD;AAMH,KAZM,CAAP;AAaH;;AACK2G,EAAAA,cAAc,GAAG;AAAA;;AAAA;AACnB,UAAInJ,EAAJ,EAAQ6E,EAAR,EAAYC,EAAZ,EAAgBC,EAAhB,EAAoBC,EAApB;;AACA,UAAI,MAAI,CAAC9J,oBAAL,CAA0B6B,OAA9B,EAAuC;AACnC4E,QAAAA,OAAO,CAACC,GAAR,CAAY,gBAAZ,EAA8B,MAAI,CAAC1G,oBAAL,CAA0B2B,KAAxD;;AACA,QAAA,MAAI,CAAC8C,iBAAL,CAAuB2F,IAAvB,CAA4B,eAA5B;;AACA,QAAA,MAAI,CAACzF,MAAL,CAAYuE,IAAZ,CAAiB,uEAAjB;;AACA;AACH,OALD,MAMK;AACD,YAAI;AACA,UAAA,MAAI,CAACzE,iBAAL,CAAuB8E,IAAvB,CAA4B,eAA5B;;AACA,cAAI,MAAI,CAACgE,SAAT,EAAoB;AAChB,kBAAM,MAAI,CAACnE,aAAL,EAAN;AACA,YAAA,MAAI,CAACiE,OAAL,GAAe,MAAI,CAAClI,OAAL,GAAe,MAAI,CAACoI,SAAL,CAAevE,IAA7C;AACH;;AACD,gBAAMtF,SAAS,SAAS,MAAI,CAACgG,gBAAL,CAAsB,MAAI,CAACtE,UAA3B,CAAxB;AACAqB,UAAAA,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B,MAAI,CAACtB,UAApC;;AACA,UAAA,MAAI,CAACzB,gBAAL,CAAsBoF,QAAtB,CAA+BvB,OAA/B,CAAuC,CAACyB,OAAD,EAAUzI,KAAV,KAAoB;AACvD,gBAAIsE,EAAJ;;AACA,kBAAMW,KAAK,GAAGwD,OAAd;AACA,aAACnE,EAAE,GAAGW,KAAK,CAACxF,GAAN,CAAU,UAAV,CAAN,MAAiC,IAAjC,IAAyC6E,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACiF,QAAH,CAAYrG,SAAS,CAAClD,KAAD,CAArB,CAAlE;AACH,WAJD;;AAKAiG,UAAAA,OAAO,CAACC,GAAR,CAAY,iCAAZ,EAA+C,MAAI,CAAC1G,oBAAL,CAA0B2B,KAAzE;AACA,gBAAMwF,IAAI,GAAG;AACTzB,YAAAA,SAAS,EAAE,CAACZ,EAAE,GAAG,MAAI,CAAC9E,oBAAL,CAA0BC,GAA1B,CAA8B,WAA9B,CAAN,MAAsD,IAAtD,IAA8D6E,EAAE,KAAK,KAAK,CAA1E,GAA8E,KAAK,CAAnF,GAAuFA,EAAE,CAACnD,KAD5F;AAETgE,YAAAA,MAAM,EAAE,MAAI,CAAC0H,OAFJ;AAGTzH,YAAAA,WAAW,EAAE,CAAC+D,EAAE,GAAG,MAAI,CAAC3J,oBAAL,CAA0BC,GAA1B,CAA8B,aAA9B,CAAN,MAAwD,IAAxD,IAAgE0J,EAAE,KAAK,KAAK,CAA5E,GAAgF,KAAK,CAArF,GAAyFA,EAAE,CAAChI,KAHhG;AAITkE,YAAAA,WAAW,EAAE,CAAC+D,EAAE,GAAG,MAAI,CAAC5J,oBAAL,CAA0BC,GAA1B,CAA8B,aAA9B,CAAN,MAAwD,IAAxD,IAAgE2J,EAAE,KAAK,KAAK,CAA5E,GAAgF,KAAK,CAArF,GAAyFA,EAAE,CAACjI,KAJhG;AAKTmE,YAAAA,gBAAgB,EAAE,CAAC+D,EAAE,GAAG,MAAI,CAAC7J,oBAAL,CAA0BC,GAA1B,CAA8B,kBAA9B,CAAN,MAA6D,IAA7D,IAAqE4J,EAAE,KAAK,KAAK,CAAjF,GAAqF,KAAK,CAA1F,GAA8FA,EAAE,CAAClI,KAL1G;AAMTwE,YAAAA,cAAc,EAAE,CAAC2D,EAAE,GAAG,MAAI,CAAC9J,oBAAL,CAA0BC,GAA1B,CAA8B,gBAA9B,CAAN,MAA2D,IAA3D,IAAmE6J,EAAE,KAAK,KAAK,CAA/E,GAAmF,KAAK,CAAxF,GAA4FA,EAAE,CAACnI,KANtG;AAOTqI,YAAAA,QAAQ,EAAE,EAPD;AAQTC,YAAAA,WAAW,EAAE,EARJ;AASTC,YAAAA,SAAS,EAAE;AATF,WAAb;AAWAzD,UAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsCS,IAAtC;;AACA,UAAA,MAAI,CAAC7C,mBAAL,CAAyB4J,aAAzB,CAAuC/G,IAAvC,EAA6Cb,SAA7C,CAAwDW,GAAD,IAAS;AAC5D,YAAA,MAAI,CAACxC,iBAAL,CAAuB2F,IAAvB,CAA4B,eAA5B;;AACA,YAAA,MAAI,CAACzF,MAAL,CAAY2F,OAAZ,CAAoB,+BAApB;;AACA,YAAA,MAAI,CAAC5F,MAAL,CAAYoC,QAAZ,CAAqB,CAAE,uBAAF,CAArB;AACH,WAJD,EAIIQ,KAAD,IAAW;AACV,YAAA,MAAI,CAAC7C,iBAAL,CAAuB2F,IAAvB,CAA4B,eAA5B;;AACA3D,YAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ,EAAqBY,KAArB;;AACA,YAAA,MAAI,CAAC3C,MAAL,CAAY2C,KAAZ,CAAkB,0BAAlB;AACH,WARD;AASH,SAnCD,CAoCA,OAAOA,KAAP,EAAc;AACV,UAAA,MAAI,CAAC7C,iBAAL,CAAuB2F,IAAvB,CAA4B,eAA5B;;AACA3D,UAAAA,OAAO,CAACa,KAAR,CAAc,+CAAd,EAA+DA,KAA/D;;AACA,UAAA,MAAI,CAAC3C,MAAL,CAAY2C,KAAZ,CAAkB,+CAAlB;AACH;AACJ;AAlDkB;AAmDtB;;AACD6G,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAKjJ,KAAL,IAAc,KAAlB,EAAyB;AACrB,WAAK+I,cAAL;AACH,KAFD,MAGK;AACD,WAAKrF,gBAAL;AACH;AACJ;;AAhauC;;AAka5CzE,8BAA8B,CAACiK,IAA/B,GAAsC,SAASC,sCAAT,CAAgDC,CAAhD,EAAmD;AAAE,SAAO,KAAKA,CAAC,IAAInK,8BAAV,EAA0CjG,EAAE,CAACqQ,iBAAH,CAAqBpQ,EAAE,CAACqQ,WAAxB,CAA1C,EAAgFtQ,EAAE,CAACqQ,iBAAH,CAAqBnQ,EAAE,CAACqQ,mBAAxB,CAAhF,EAA8HvQ,EAAE,CAACqQ,iBAAH,CAAqBlQ,EAAE,CAACqQ,YAAxB,CAA9H,EAAqKxQ,EAAE,CAACqQ,iBAAH,CAAqBjQ,EAAE,CAACqQ,UAAxB,CAArK,EAA0MzQ,EAAE,CAACqQ,iBAAH,CAAqBhQ,EAAE,CAACqQ,iBAAxB,CAA1M,EAAsP1Q,EAAE,CAACqQ,iBAAH,CAAqB/P,EAAE,CAACqQ,MAAxB,CAAtP,EAAuR3Q,EAAE,CAACqQ,iBAAH,CAAqB9P,EAAE,CAACqQ,aAAxB,CAAvR,EAA+T5Q,EAAE,CAACqQ,iBAAH,CAAqB/P,EAAE,CAACuQ,cAAxB,CAA/T,EAAwW7Q,EAAE,CAACqQ,iBAAH,CAAqB7P,EAAE,CAACsQ,QAAxB,CAAxW,CAAP;AAAoZ,CAA/e;;AACA7K,8BAA8B,CAAC8K,IAA/B,GAAsC,aAAc/Q,EAAE,CAACgR,iBAAH,CAAqB;AAAE3C,EAAAA,IAAI,EAAEpI,8BAAR;AAAwCgL,EAAAA,SAAS,EAAE,CAAC,CAAC,8BAAD,CAAD,CAAnD;AAAuFC,EAAAA,SAAS,EAAE,SAASC,oCAAT,CAA8CvQ,EAA9C,EAAkDC,GAAlD,EAAuD;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AACxOZ,MAAAA,EAAE,CAACoR,WAAH,CAAe1Q,GAAf,EAAoB,CAApB;AACH;;AAAC,QAAIE,EAAE,GAAG,CAAT,EAAY;AACV,UAAIyQ,EAAJ;;AACArR,MAAAA,EAAE,CAACsR,cAAH,CAAkBD,EAAE,GAAGrR,EAAE,CAACuR,WAAH,EAAvB,MAA6C1Q,GAAG,CAACoM,YAAJ,GAAmBoE,EAAhE;AACH;AAAE,GALkE;AAKhEG,EAAAA,KAAK,EAAE,EALyD;AAKrDC,EAAAA,IAAI,EAAE,EAL+C;AAK3CC,EAAAA,MAAM,EAAE,CAAC,CAAC,CAAD,EAAI,iBAAJ,CAAD,EAAyB,CAAC,CAAD,EAAI,KAAJ,CAAzB,EAAqC,CAAC,CAAD,EAAI,QAAJ,EAAc,aAAd,EAA6B,cAA7B,CAArC,EAAmF,CAAC,CAAD,EAAI,MAAJ,CAAnF,EAAgG,CAAC,CAAD,EAAI,aAAJ,EAAmB,YAAnB,EAAiC,YAAjC,EAA+C,aAA/C,EAA8D,YAA9D,EAA4E,aAA5E,EAA2F,MAA3F,CAAhG,EAAoM,CAAC,CAAD,EAAI,WAAJ,CAApM,EAAsN,CAAC,CAAD,EAAI,cAAJ,EAAoB,CAApB,EAAuB,WAAvB,EAAoC,UAApC,CAAtN,EAAuQ,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAAvQ,EAAsS,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,gBAAxB,CAAtS,EAAiV,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,WAAvB,EAAoC,iBAApC,EAAuD,WAAvD,EAAoE,UAApE,EAAgF,EAAhF,EAAoF,aAApF,EAAmG,YAAnG,EAAiH,CAAjH,EAAoH,cAApH,EAAoI,iBAApI,EAAuJ,CAAvJ,EAA0J,UAA1J,CAAjV,EAAwf,CAAC,KAAD,EAAQ,QAAR,CAAxf,EAA2gB,CAAC,CAAD,EAAI,sBAAJ,CAA3gB,EAAwiB,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,QAApC,EAA8C,QAA9C,EAAwD,SAAxD,EAAmE,CAAnE,EAAsE,cAAtE,EAAsF,iBAAtF,EAAyG,CAAzG,EAA4G,UAA5G,EAAwH,QAAxH,CAAxiB,EAA2qB,CAAC,KAAD,EAAQ,eAAR,EAAyB,OAAzB,EAAkC,aAAlC,EAAiD,CAAjD,EAAoD,KAApD,EAA2D,CAA3D,EAA8D,MAA9D,CAA3qB,EAAkvB,CAAC,OAAD,EAAU,SAAV,EAAqB,CAArB,EAAwB,MAAxB,CAAlvB,EAAmxB,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,gBAA1B,CAAnxB,EAAg0B,CAAC,IAAD,EAAO,aAAP,EAAsB,iBAAtB,EAAyC,aAAzC,EAAwD,UAAxD,EAAoE,EAApE,EAAwE,aAAxE,EAAuF,mBAAvF,EAA4G,MAA5G,EAAoH,GAApH,EAAyH,MAAzH,EAAiI,IAAjI,EAAuI,CAAvI,EAA0I,cAA1I,EAA0J,iBAA1J,EAA6K,CAA7K,EAAgL,UAAhL,CAAh0B,EAA6/B,CAAC,KAAD,EAAQ,aAAR,EAAuB,CAAvB,EAA0B,gBAA1B,CAA7/B,EAA0iC,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,aAAvB,EAAsC,iBAAtC,EAAyD,aAAzD,EAAwE,aAAxE,EAAuF,WAAvF,EAAoG,UAApG,EAAgH,EAAhH,EAAoH,CAApH,EAAuH,cAAvH,EAAuI,iBAAvI,EAA0J,CAA1J,EAA6J,UAA7J,CAA1iC,EAAotC,CAAC,CAAD,EAAI,MAAJ,EAAY,MAAZ,CAAptC,EAAyuC,CAAC,eAAD,EAAkB,kBAAlB,CAAzuC,EAAgxC,CAAC,CAAD,EAAI,eAAJ,EAAqB,CAArB,EAAwB,OAAxB,EAAiC,SAAjC,CAAhxC,EAA6zC,CAAC,eAAD,EAAkB,gBAAlB,CAA7zC,EAAk2C,CAAC,OAAD,EAAU,UAAV,EAAsB,CAAtB,EAAyB,eAAzB,EAA0C,CAA1C,EAA6C,OAA7C,EAAsD,SAAtD,CAAl2C,EAAo6C,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,gDAA5B,EAA8E,CAA9E,EAAiF,OAAjF,EAA0F,CAA1F,EAA6F,MAA7F,CAAp6C,EAA0gD,CAAC,CAAD,EAAI,aAAJ,EAAmB,MAAnB,CAA1gD,EAAsiD,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,sBAA5B,EAAoD,CAApD,EAAuD,MAAvD,CAAtiD,EAAsmD,CAAC,MAAD,EAAS,QAAT,EAAmB,YAAnB,EAAiC,uBAAjC,EAA0D,CAA1D,EAA6D,KAA7D,EAAoE,WAApE,CAAtmD,EAAwrD,CAAC,KAAD,EAAQ,eAAR,EAAyB,CAAzB,EAA4B,aAA5B,EAA2C,CAA3C,EAA8C,KAA9C,CAAxrD,EAA8uD,CAAC,CAAD,EAAI,SAAJ,CAA9uD,EAA8vD,CAAC,CAAD,EAAI,MAAJ,CAA9vD,EAA2wD,CAAC,CAAD,EAAI,eAAJ,CAA3wD,EAAiyD,CAAC,CAAD,EAAI,WAAJ,EAAiB,QAAjB,CAAjyD,EAA6zD,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,CAA7zD,EAAi1D,CAAC,CAAD,EAAI,WAAJ,EAAiB,QAAjB,EAA2B,CAA3B,EAA8B,SAA9B,EAAyC,KAAzC,CAAj1D,EAAk4D,CAAC,MAAD,EAAS,MAAT,EAAiB,aAAjB,EAAgC,WAAhC,EAA6C,CAA7C,EAAgD,cAAhD,EAAgE,iBAAhE,EAAmF,CAAnF,EAAsF,IAAtF,EAA4F,iBAA5F,EAA+G,UAA/G,CAAl4D,EAA8/D,CAAC,KAAD,EAAQ,gBAAR,EAA0B,CAA1B,EAA6B,WAA7B,EAA0C,QAA1C,EAAoD,CAApD,EAAuD,SAAvD,CAA9/D,EAAikE,CAAC,IAAD,EAAO,gBAAP,EAAyB,iBAAzB,EAA4C,gBAA5C,EAA8D,aAA9D,EAA6E,mBAA7E,EAAkG,MAAlG,EAA0G,GAA1G,EAA+G,CAA/G,EAAkH,cAAlH,EAAkI,iBAAlI,EAAqJ,CAArJ,EAAwJ,UAAxJ,CAAjkE,EAAsuE,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,EAAmB,CAAnB,EAAsB,eAAtB,CAAtuE,EAA8wE,CAAC,CAAD,EAAI,WAAJ,CAA9wE,EAAgyE,CAAC,CAAD,EAAI,KAAJ,EAAW,MAAX,CAAhyE,EAAozE,CAAC,CAAD,EAAI,YAAJ,EAAkB,UAAlB,CAApzE,EAAm1E,CAAC,KAAD,EAAQ,WAAR,EAAqB,CAArB,EAAwB,UAAxB,CAAn1E,EAAw3E,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,WAApC,EAAiD,aAAjD,EAAgE,aAAhE,EAA+E,CAA/E,EAAkF,cAAlF,EAAkG,iBAAlG,CAAx3E,EAA8+E,CAAC,KAAD,EAAQ,UAAR,EAAoB,CAApB,EAAuB,UAAvB,CAA9+E,EAAkhF,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,UAApC,EAAgD,aAAhD,EAA+D,YAA/D,EAA6E,CAA7E,EAAgF,cAAhF,EAAgG,iBAAhG,CAAlhF,EAAsoF,CAAC,KAAD,EAAQ,cAAR,EAAwB,CAAxB,EAA2B,UAA3B,CAAtoF,EAA8qF,CAAC,MAAD,EAAS,MAAT,EAAiB,iBAAjB,EAAoC,cAApC,EAAoD,aAApD,EAAmE,gBAAnE,EAAqF,CAArF,EAAwF,cAAxF,EAAwG,iBAAxG,CAA9qF,EAA0yF,CAAC,KAAD,EAAQ,MAAR,EAAgB,CAAhB,EAAmB,UAAnB,CAA1yF,EAA00F,CAAC,MAAD,EAAS,MAAT,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,QAA/B,EAAyC,SAAzC,EAAoD,CAApD,EAAuD,cAAvD,EAAuE,iBAAvE,EAA0F,CAA1F,EAA6F,QAA7F,CAA10F,EAAk7F,CAAC,CAAD,EAAI,UAAJ,EAAgB,MAAhB,EAAwB,QAAxB,EAAkC,oBAAlC,EAAwD,MAAxD,CAAl7F,EAAm/F,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,6BAA5B,EAA2D,CAA3D,EAA8D,SAA9D,EAAyE,OAAzE,EAAkF,CAAlF,EAAqF,MAArF,CAAn/F,EAAilG,CAAC,MAAD,EAAS,QAAT,EAAmB,OAAnB,EAA4B,2CAA5B,EAAyE,CAAzE,EAA4E,OAA5E,EAAqF,CAArF,EAAwF,MAAxF,CAAjlG,EAAkrG,CAAC,UAAD,EAAa,EAAb,EAAiB,CAAjB,EAAoB,SAApB,EAA+B,MAA/B,CAAlrG,EAA0tG,CAAC,aAAD,EAAgB,EAAhB,CAA1tG,EAA+uG,CAAC,MAAD,EAAS,YAAT,EAAuB,CAAvB,EAA0B,KAA1B,CAA/uG,EAAixG,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,QAA5C,EAAsD,MAAtD,EAA8D,CAA9D,EAAiE,SAAjE,EAA4E,OAA5E,CAAjxG,EAAu2G,CAAC,CAAD,EAAI,KAAJ,EAAW,CAAX,EAAc,SAAd,CAAv2G,EAAi4G,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,QAA5C,EAAsD,oBAAtD,EAA4E,CAA5E,EAA+E,OAA/E,CAAj4G,EAA09G,CAAC,CAAD,EAAI,KAAJ,EAAW,UAAX,EAAuB,MAAvB,CAA19G,EAA0/G,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,QAA7B,EAAuC,iBAAvC,EAA0D,qBAA1D,EAAiF,CAAjF,EAAoF,OAApF,CAA1/G,EAAwlH,CAAC,CAAD,EAAI,KAAJ,EAAW,SAAX,EAAsB,MAAtB,CAAxlH,EAAunH,CAAC,MAAD,EAAS,QAAT,EAAmB,CAAnB,EAAsB,KAAtB,EAA6B,aAA7B,EAA4C,MAA5C,CAAvnH,CALmC;AAK0oHC,EAAAA,QAAQ,EAAE,SAASC,uCAAT,CAAiDhR,EAAjD,EAAqDC,GAArD,EAA0D;AAAE,QAAID,EAAE,GAAG,CAAT,EAAY;AAC7xHZ,MAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,aAArB;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACArB,MAAAA,EAAE,CAACsB,MAAH,CAAU,CAAV;AACAtB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,MAArB,EAA6B,CAA7B;AACArB,MAAAA,EAAE,CAACkE,UAAH,CAAc,UAAd,EAA0B,SAAS2N,gEAAT,GAA4E;AAAE,eAAOhR,GAAG,CAACoP,QAAJ,EAAP;AAAwB,OAAhI;AACAjQ,MAAAA,EAAE,CAACqB,cAAH,CAAkB,CAAlB,EAAqB,KAArB,EAA4B,CAA5B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,CAA/B;AACArB,MAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,iBAAd;AACAtB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACc,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,CAA1B;AACAd,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACArB,MAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,iBAAd;AACAtB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACArB,MAAAA,EAAE,CAACkE,UAAH,CAAc,QAAd,EAAwB,SAAS4N,gEAAT,CAA0E3M,MAA1E,EAAkF;AAAE,eAAOtE,GAAG,CAACuO,cAAJ,CAAmBjK,MAAnB,CAAP;AAAoC,OAAhJ;AACAnF,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBf,8CAAlB,EAAkE,CAAlE,EAAqE,CAArE,EAAwE,KAAxE,EAA+E,EAA/E;AACAX,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBN,8CAAlB,EAAkE,CAAlE,EAAqE,CAArE,EAAwE,KAAxE,EAA+E,EAA/E;AACApB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACArB,MAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,gCAAd;AACAtB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACc,SAAH,CAAa,EAAb,EAAiB,UAAjB,EAA6B,EAA7B;AACAd,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,CAA7B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,OAAtB,EAA+B,EAA/B;AACArB,MAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,SAAd;AACAtB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACc,SAAH,CAAa,EAAb,EAAiB,OAAjB,EAA0B,EAA1B;AACAd,MAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBD,8CAAlB,EAAkE,CAAlE,EAAqE,CAArE,EAAwE,KAAxE,EAA+E,EAA/E;AACAzB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACc,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAd,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,IAAtB,EAA4B,EAA5B;AACArB,MAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,eAAd;AACAtB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACArB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBsB,8CAAlB,EAAkE,EAAlE,EAAsE,EAAtE,EAA0E,KAA1E,EAAiF,EAAjF;AACAhD,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACc,SAAH,CAAa,EAAb,EAAiB,IAAjB;AACAd,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACArB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,IAAtB,EAA4B,EAA5B;AACArB,MAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,UAAd;AACAtB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBsD,8CAAlB,EAAkE,EAAlE,EAAsE,CAAtE,EAAyE,KAAzE,EAAgF,EAAhF;AACAhF,MAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBiE,iDAAlB,EAAqE,CAArE,EAAwE,CAAxE,EAA2E,QAA3E,EAAqF,EAArF;AACA3F,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,KAAtB,EAA6B,EAA7B;AACArB,MAAAA,EAAE,CAAC0B,UAAH,CAAc,EAAd,EAAkBsE,iDAAlB,EAAqE,CAArE,EAAwE,CAAxE,EAA2E,QAA3E,EAAqF,EAArF;AACAhG,MAAAA,EAAE,CAACqB,cAAH,CAAkB,EAAlB,EAAsB,QAAtB,EAAgC,EAAhC;AACArB,MAAAA,EAAE,CAACsB,MAAH,CAAU,EAAV,EAAc,QAAd;AACAtB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACuB,YAAH;AACAvB,MAAAA,EAAE,CAACuB,YAAH;AACH;;AAAC,QAAIX,EAAE,GAAG,CAAT,EAAY;AACV,UAAImR,OAAJ;AACA,UAAI3O,OAAJ;AACApD,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACgS,kBAAH,CAAsB,EAAtB,EAA0BnR,GAAG,CAACmG,KAA9B,EAAqC,aAArC;AACAhH,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,WAAd,EAA2BJ,GAAG,CAACiB,oBAA/B;AACA9B,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAAC6C,UAA9B;AACA1D,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAAC6C,UAA9B;AACA1D,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsBJ,GAAG,CAACK,QAA1B;AACAlB,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB,CAAC8Q,OAAO,GAAGlR,GAAG,CAACiB,oBAAJ,CAAyBC,GAAzB,CAA6B,QAA7B,CAAX,KAAsD,IAAtD,GAA6D,IAA7D,GAAoEgQ,OAAO,CAAC/P,MAAR,IAAkB,IAAlB,GAAyB,IAAzB,GAAgC+P,OAAO,CAAC/P,MAAR,CAAeuN,iBAAzI;AACAvP,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAAC6C,UAA9B;AACA1D,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,UAAd,EAA0BJ,GAAG,CAAC6C,UAA9B;AACA1D,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB,CAAC,CAACmC,OAAO,GAAGvC,GAAG,CAACiB,oBAAJ,CAAyBC,GAAzB,CAA6B,aAA7B,CAAX,KAA2D,IAA3D,GAAkE,IAAlE,GAAyEqB,OAAO,CAACO,OAAlF,MAA+F,CAACP,OAAO,GAAGvC,GAAG,CAACiB,oBAAJ,CAAyBC,GAAzB,CAA6B,aAA7B,CAAX,KAA2D,IAA3D,GAAkE,IAAlE,GAAyEqB,OAAO,CAACQ,OAAhL,CAAtB;AACA5D,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,SAAd,EAAyBJ,GAAG,CAACwI,YAAJ,CAAiBwB,QAA1C;AACA7K,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,SAAd,EAAyBJ,GAAG,CAAC4E,gBAAJ,CAAqBoF,QAA9C;AACA7K,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsBJ,GAAG,CAAC4E,gBAAJ,CAAqBC,MAArB,GAA8B,CAApD;AACA1F,MAAAA,EAAE,CAAC6B,SAAH,CAAa,CAAb;AACA7B,MAAAA,EAAE,CAACiB,UAAH,CAAc,MAAd,EAAsB,CAACJ,GAAG,CAAC6C,UAA3B;AACH;AAAE,GA3GkE;AA2GhEuO,EAAAA,UAAU,EAAE,CAACxR,EAAE,CAACyR,gBAAJ,EAAsBjS,EAAE,CAACkS,aAAzB,EAAwClS,EAAE,CAACmS,oBAA3C,EAAiEnS,EAAE,CAACoS,kBAApE,EAAwFpS,EAAE,CAACqS,oBAA3F,EAAiHrS,EAAE,CAACsS,eAApH,EAAqItS,EAAE,CAACuS,eAAxI,EAAyJvS,EAAE,CAACwS,iBAA5J,EAA+KjS,EAAE,CAACkS,IAAlL,EAAwLzS,EAAE,CAAC0S,aAA3L,EAA0MnS,EAAE,CAACoS,OAA7M,EAAsNtS,EAAE,CAACuS,UAAzN,EAAqO5S,EAAE,CAAC6S,aAAxO,EAAuPtS,EAAE,CAACuS,OAA1P,CA3GoD;AA2GgNC,EAAAA,MAAM,EAAE,CAAC,4lDAAD;AA3GxN,CAArB,CAApD", "sourcesContent": ["import { Validators, } from '@angular/forms';\r\nimport { FileValidator } from 'src/app/shared/validators/fileValidator.validator';\r\nimport * as i0 from \"@angular/core\";\r\nimport * as i1 from \"@angular/forms\";\r\nimport * as i2 from \"src/app/shared/services/data-transfer.service\";\r\nimport * as i3 from \"@angular/platform-browser\";\r\nimport * as i4 from \"@angular/common/http\";\r\nimport * as i5 from \"ngx-spinner\";\r\nimport * as i6 from \"@angular/router\";\r\nimport * as i7 from \"ngx-toastr\";\r\nimport * as i8 from \"@angular/common\";\r\nimport * as i9 from \"../../../sidebar.component\";\r\nconst _c0 = [\"audioPlayer\"];\r\nfunction AddEditUniversityListComponent_img_19_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelement(0, \"img\", 28);\r\n} if (rf & 2) {\r\n    const ctx_r0 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"src\", ctx_r0.imageSrc, i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction AddEditUniversityListComponent_div_20_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 29);\r\n    i0.ɵɵtext(1, \" The file size exceeds the 2 MB limit. Please select a smaller file. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditUniversityListComponent_div_29_div_1_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \" Please enter a valid URL starting with http:// or https://. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditUniversityListComponent_div_29_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 29);\r\n    i0.ɵɵtemplate(1, AddEditUniversityListComponent_div_29_div_1_Template, 2, 0, \"div\", 30);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r2 = i0.ɵɵnextContext();\r\n    let tmp_0_0;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.addNewUniversityForm.get(\"INS_website\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.pattern);\r\n} }\r\nfunction AddEditUniversityListComponent_div_34_div_8_div_1_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \" Please enter a valid URL starting with http:// or https://. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditUniversityListComponent_div_34_div_8_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 29);\r\n    i0.ɵɵtemplate(1, AddEditUniversityListComponent_div_34_div_8_div_1_Template, 2, 0, \"div\", 30);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const ctx_r13 = i0.ɵɵnextContext();\r\n    const i_r9 = ctx_r13.index;\r\n    const serviceLink_r8 = ctx_r13.$implicit;\r\n    const ctx_r10 = i0.ɵɵnextContext();\r\n    let tmp_0_0;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceLink_r8.get(ctx_r10.serviceLinkKeys[i_r9])) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.pattern);\r\n} }\r\nfunction AddEditUniversityListComponent_div_34_div_13_div_1_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\");\r\n    i0.ɵɵtext(1, \" Description is required when a link is provided. \");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditUniversityListComponent_div_34_div_13_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 29);\r\n    i0.ɵɵtemplate(1, AddEditUniversityListComponent_div_34_div_13_div_1_Template, 2, 0, \"div\", 30);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const serviceLink_r8 = i0.ɵɵnextContext().$implicit;\r\n    let tmp_0_0;\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = serviceLink_r8.get(\"SL_description\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors.required);\r\n} }\r\nconst _c1 = function (a0) { return { \"required-field\": a0 }; };\r\nfunction AddEditUniversityListComponent_div_34_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"div\", 31);\r\n    i0.ɵɵelementStart(1, \"span\", 32);\r\n    i0.ɵɵtext(2);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(3, \"div\", 33);\r\n    i0.ɵɵelementStart(4, \"div\", 7);\r\n    i0.ɵɵelementStart(5, \"label\", 34);\r\n    i0.ɵɵtext(6, \"Link\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(7, \"input\", 35);\r\n    i0.ɵɵtemplate(8, AddEditUniversityListComponent_div_34_div_8_Template, 2, 1, \"div\", 14);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(9, \"div\", 7);\r\n    i0.ɵɵelementStart(10, \"label\", 36);\r\n    i0.ɵɵtext(11, \"Description\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(12, \"textarea\", 37);\r\n    i0.ɵɵtemplate(13, AddEditUniversityListComponent_div_34_div_13_Template, 2, 1, \"div\", 14);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const serviceLink_r8 = ctx.$implicit;\r\n    const i_r9 = ctx.index;\r\n    const ctx_r3 = i0.ɵɵnextContext();\r\n    let tmp_2_0;\r\n    let tmp_7_0;\r\n    let tmp_8_0;\r\n    let tmp_10_0;\r\n    i0.ɵɵproperty(\"formGroupName\", i_r9);\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵtextInterpolate(ctx_r3.serviceLinkHeadings[i_r9]);\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c1, (tmp_2_0 = serviceLink_r8.get(ctx_r3.serviceLinkKeys[i_r9])) == null ? null : tmp_2_0.value))(\"for\", ctx_r3.serviceLinkKeys[i_r9] + \"_link\");\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"id\", ctx_r3.serviceLinkKeys[i_r9] + \"_link\")(\"formControlName\", ctx_r3.serviceLinkKeys[i_r9])(\"readonly\", ctx_r3.isReadonly);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = serviceLink_r8.get(ctx_r3.serviceLinkKeys[i_r9])) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = serviceLink_r8.get(ctx_r3.serviceLinkKeys[i_r9])) == null ? null : tmp_7_0.touched));\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, (tmp_8_0 = serviceLink_r8.get(ctx_r3.serviceLinkKeys[i_r9])) == null ? null : tmp_8_0.value));\r\n    i0.ɵɵadvance(2);\r\n    i0.ɵɵproperty(\"readonly\", ctx_r3.isReadonly);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", (tmp_10_0 = serviceLink_r8.get(\"SL_description\")) == null ? null : tmp_10_0.invalid);\r\n} }\r\nconst _c2 = function (a0, a1) { return { \"btn-outline-primary\": a0, \"btn-outline-secondary\": a1 }; };\r\nfunction AddEditUniversityListComponent_div_39_button_20_Template(rf, ctx) { if (rf & 1) {\r\n    const _r23 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"button\", 56);\r\n    i0.ɵɵlistener(\"click\", function AddEditUniversityListComponent_div_39_button_20_Template_button_click_0_listener() { i0.ɵɵrestoreView(_r23); const i_r17 = i0.ɵɵnextContext().index; const ctx_r21 = i0.ɵɵnextContext(); return ctx_r21.toggleAudio(i_r17); });\r\n    i0.ɵɵelement(1, \"i\", 57);\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const i_r17 = i0.ɵɵnextContext().index;\r\n    const ctx_r18 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c2, !ctx_r18.isPlaying[i_r17], ctx_r18.isPlaying[i_r17]));\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngClass\", ctx_r18.isPlaying[i_r17] ? \"fa-pause\" : \"fa-play\");\r\n} }\r\nfunction AddEditUniversityListComponent_div_39_button_21_Template(rf, ctx) { if (rf & 1) {\r\n    const _r27 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"button\", 58);\r\n    i0.ɵɵlistener(\"click\", function AddEditUniversityListComponent_div_39_button_21_Template_button_click_0_listener() { i0.ɵɵrestoreView(_r27); const i_r17 = i0.ɵɵnextContext().index; const ctx_r25 = i0.ɵɵnextContext(); return ctx_r25.removeInsight(i_r17); });\r\n    i0.ɵɵelement(1, \"i\", 59);\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditUniversityListComponent_div_39_Template(rf, ctx) { if (rf & 1) {\r\n    const _r29 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"div\", 38);\r\n    i0.ɵɵelementStart(1, \"div\", 39);\r\n    i0.ɵɵelementStart(2, \"div\", 40);\r\n    i0.ɵɵelementStart(3, \"div\", 41);\r\n    i0.ɵɵelementStart(4, \"label\", 42);\r\n    i0.ɵɵtext(5, \"Insight Title\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(6, \"input\", 43);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(7, \"div\", 41);\r\n    i0.ɵɵelementStart(8, \"label\", 44);\r\n    i0.ɵɵtext(9, \"Name\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(10, \"input\", 45);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(11, \"div\", 41);\r\n    i0.ɵɵelementStart(12, \"label\", 46);\r\n    i0.ɵɵtext(13, \"Position\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelement(14, \"input\", 47);\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(15, \"div\", 41);\r\n    i0.ɵɵelementStart(16, \"label\", 48);\r\n    i0.ɵɵtext(17, \"Upload Insight\");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(18, \"input\", 49);\r\n    i0.ɵɵlistener(\"change\", function AddEditUniversityListComponent_div_39_Template_input_change_18_listener($event) { const restoredCtx = i0.ɵɵrestoreView(_r29); const i_r17 = restoredCtx.index; const ctx_r28 = i0.ɵɵnextContext(); return ctx_r28.onAudioSelected($event, i_r17); });\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementStart(19, \"div\", 50);\r\n    i0.ɵɵtemplate(20, AddEditUniversityListComponent_div_39_button_20_Template, 2, 5, \"button\", 51);\r\n    i0.ɵɵtemplate(21, AddEditUniversityListComponent_div_39_button_21_Template, 2, 0, \"button\", 52);\r\n    i0.ɵɵelementStart(22, \"audio\", 53, 54);\r\n    i0.ɵɵelement(24, \"source\", 55);\r\n    i0.ɵɵtext(25, \" Your browser does not support the audio element. \");\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n    i0.ɵɵelementEnd();\r\n} if (rf & 2) {\r\n    const i_r17 = ctx.index;\r\n    const ctx_r4 = i0.ɵɵnextContext();\r\n    i0.ɵɵproperty(\"formGroupName\", i_r17);\r\n    i0.ɵɵadvance(20);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.audioUrls[i_r17]);\r\n    i0.ɵɵadvance(1);\r\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.insightFormArray.length > 1);\r\n    i0.ɵɵadvance(3);\r\n    i0.ɵɵproperty(\"src\", ctx_r4.audioUrls[i_r17], i0.ɵɵsanitizeUrl);\r\n} }\r\nfunction AddEditUniversityListComponent_button_40_Template(rf, ctx) { if (rf & 1) {\r\n    const _r31 = i0.ɵɵgetCurrentView();\r\n    i0.ɵɵelementStart(0, \"button\", 60);\r\n    i0.ɵɵlistener(\"click\", function AddEditUniversityListComponent_button_40_Template_button_click_0_listener() { i0.ɵɵrestoreView(_r31); const ctx_r30 = i0.ɵɵnextContext(); return ctx_r30.addInsight(); });\r\n    i0.ɵɵtext(1, \"Add Insight \");\r\n    i0.ɵɵelement(2, \"i\", 61);\r\n    i0.ɵɵelementEnd();\r\n} }\r\nfunction AddEditUniversityListComponent_button_42_Template(rf, ctx) { if (rf & 1) {\r\n    i0.ɵɵelementStart(0, \"button\", 62);\r\n    i0.ɵɵtext(1, \"Save\");\r\n    i0.ɵɵelementEnd();\r\n} }\r\nexport class AddEditUniversityListComponent {\r\n    constructor(formBuilder, dataTransferService, domSanitizer, http, ngxSpinnerService, router, toastr, route, datePipe) {\r\n        var _a;\r\n        this.formBuilder = formBuilder;\r\n        this.dataTransferService = dataTransferService;\r\n        this.domSanitizer = domSanitizer;\r\n        this.http = http;\r\n        this.ngxSpinnerService = ngxSpinnerService;\r\n        this.router = router;\r\n        this.toastr = toastr;\r\n        this.route = route;\r\n        this.datePipe = datePipe;\r\n        this.p = 1;\r\n        this.showForm = false;\r\n        this.submitted = false;\r\n        this.title = 'Add New';\r\n        this.isReadonly = false;\r\n        this.baseUrl = 'https://voxpod.s3.eu-west-2.amazonaws.com/';\r\n        this.serviceLinkHeadings = [\r\n            'Appointment',\r\n            'Internship Discussions',\r\n            'Practice Interviews',\r\n            'Events',\r\n            'Online Resources',\r\n            'Jobs'\r\n        ];\r\n        this.serviceLinkKeys = [\r\n            'SL_appointment',\r\n            'SL_internshipDiscussion',\r\n            'SL_practiceInterview',\r\n            'SL_events',\r\n            'SL_onlineResources',\r\n            'SL_jobs'\r\n        ];\r\n        this.audioFiles = [];\r\n        this.characterCount = 0;\r\n        this.isPlaying = []; // Array to track playback state\r\n        this.audioUrls = []; // Array to store audio URLs\r\n        this.showOtherTypeInput = false;\r\n        this.existingAudioNames = [];\r\n        this.audioFileUrls = [];\r\n        this.addNewUniversityForm = this.formBuilder.group({\r\n            INS_title: ['', [Validators.required]],\r\n            INS_dp: [''],\r\n            INS_website: ['', [Validators.required, Validators.pattern('https?://.+')]],\r\n            INS_service: ['', [Validators.required]],\r\n            INS_serviceLinks: this.formBuilder.array(this.serviceLinkKeys.map((key, index) => this.createServiceLinkGroup(key))),\r\n            INS_HrInsights: this.formBuilder.array([this.createInsight()]),\r\n        });\r\n        this.route.queryParams.subscribe(param => {\r\n            this.INS_id = param['INS_id'];\r\n            console.log(\"INS_id\", this.INS_id);\r\n        });\r\n        const state = (_a = this.router.getCurrentNavigation()) === null || _a === void 0 ? void 0 : _a.extras.state;\r\n        if (state) {\r\n            (this.title = state.title);\r\n        }\r\n        else {\r\n            this.router.navigate([`/actions/universities`]);\r\n        }\r\n    }\r\n    ngOnInit() {\r\n        if (this.title === 'Edit') {\r\n            this.getInstituteById(this.INS_id);\r\n        }\r\n    }\r\n    async getInstituteById(INS_id) {\r\n        try {\r\n            const res = await this.dataTransferService.getInstituteById(INS_id).toPromise();\r\n            console.log(\"getInstituteById res\", res);\r\n            if (res && res.data) {\r\n                this.universityData = res.data;\r\n                this.patchForm(this.universityData);\r\n            }\r\n            else {\r\n                console.error(\"Response is undefined or missing 'data' property.\");\r\n            }\r\n        }\r\n        catch (error) {\r\n            console.error(\"Error fetching institute data:\", error);\r\n        }\r\n    }\r\n    patchForm(data) {\r\n        var _a;\r\n        if (this.title === 'Edit') {\r\n            const serviceLinks = this.addNewUniversityForm.get('INS_serviceLinks');\r\n            if (data === null || data === void 0 ? void 0 : data.INS_serviceLinks) {\r\n                console.log(\"data.INS_serviceLinks\");\r\n                (_a = data === null || data === void 0 ? void 0 : data.INS_serviceLinks) === null || _a === void 0 ? void 0 : _a.forEach((serviceLink, index) => {\r\n                    const linkKey = this.serviceLinkKeys[index];\r\n                    const formGroup = serviceLinks.at(index);\r\n                    if (formGroup) {\r\n                        formGroup.patchValue({\r\n                            [linkKey]: serviceLink[linkKey] || '',\r\n                            SL_description: serviceLink.SL_description || ''\r\n                        });\r\n                    }\r\n                });\r\n            }\r\n            this.addNewUniversityForm.patchValue({\r\n                INS_title: data === null || data === void 0 ? void 0 : data.INS_title,\r\n                INS_website: data === null || data === void 0 ? void 0 : data.INS_website,\r\n                INS_service: data === null || data === void 0 ? void 0 : data.INS_service\r\n            });\r\n            if (data === null || data === void 0 ? void 0 : data.INS_HrInsights) {\r\n                const hrInsightsArray = this.addNewUniversityForm.get('INS_HrInsights');\r\n                hrInsightsArray.clear();\r\n                data.INS_HrInsights.forEach((insight) => {\r\n                    const insightFormGroup = this.formBuilder.group({\r\n                        HRI_title: [insight.HRI_title],\r\n                        HRI_name: [insight.HRI_name],\r\n                        HRI_position: [insight.HRI_position],\r\n                        HRI_link: [insight.HRI_link]\r\n                    });\r\n                    hrInsightsArray.push(insightFormGroup);\r\n                    this.audioUrls = data.INS_HrInsights.map((insight) => insight.HRI_link);\r\n                    this.isPlaying = new Array(data.INS_HrInsights.length).fill(false);\r\n                    this.existingAudioNames = data.INS_HrInsights.map((insight) => insight.HRI_link.substring(insight.HRI_link.lastIndexOf('/') + 1));\r\n                });\r\n            }\r\n        }\r\n    }\r\n    updateUniversity() {\r\n        var _a;\r\n        if (this.addNewUniversityForm.invalid) {\r\n            Object.keys(this.addNewUniversityForm.controls).forEach(name => {\r\n                const control = this.addNewUniversityForm.get(name);\r\n                if (control === null || control === void 0 ? void 0 : control.invalid) {\r\n                    console.log(`Invalid control: ${name}, Errors:`, control.errors);\r\n                }\r\n            });\r\n            this.toastr.info('Please fill all required fields correctly');\r\n            return;\r\n        }\r\n        const uploadDp = ((_a = this.addNewUniversityForm.get('INS_dp')) === null || _a === void 0 ? void 0 : _a.value) ? this.uploadLogoUrl() : Promise.resolve(this.universityData.INS_dp);\r\n        this.ngxSpinnerService.show('globalSpinner');\r\n        uploadDp.then((dpUrl) => {\r\n            return this.uploadAudioFiles(this.audioFiles).then((audioUrls) => {\r\n                var _a, _b, _c, _d, _e;\r\n                console.log(\"Uploaded audio files:\", this.audioFiles);\r\n                this.insightFormArray.controls.forEach((control, index) => {\r\n                    var _a;\r\n                    (_a = control.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(this.audioUrls[index]);\r\n                });\r\n                const data = {\r\n                    INS_id: this.INS_id,\r\n                    INS_title: (_a = this.addNewUniversityForm.get('INS_title')) === null || _a === void 0 ? void 0 : _a.value,\r\n                    INS_dp: dpUrl ? dpUrl : \"\",\r\n                    INS_website: (_b = this.addNewUniversityForm.get('INS_website')) === null || _b === void 0 ? void 0 : _b.value,\r\n                    INS_service: (_c = this.addNewUniversityForm.get('INS_service')) === null || _c === void 0 ? void 0 : _c.value,\r\n                    INS_serviceLinks: (_d = this.addNewUniversityForm.get('INS_serviceLinks')) === null || _d === void 0 ? void 0 : _d.value,\r\n                    INS_HrInsights: (_e = this.addNewUniversityForm.get('INS_HrInsights')) === null || _e === void 0 ? void 0 : _e.value,\r\n                    INS_city: '',\r\n                    INS_country: '',\r\n                    INS_state: ''\r\n                };\r\n                console.log(\"Data to update...\", data);\r\n                return this.dataTransferService.updateInstitute(data).toPromise();\r\n            });\r\n        }).then((res) => {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            if (res.statusCode === 200) {\r\n                this.toastr.success('University updated successfully.');\r\n                this.router.navigate(['actions/universities']);\r\n            }\r\n            else {\r\n                this.toastr.error('Something went wrong.');\r\n            }\r\n        }).catch((error) => {\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            const errorMessage = error === 'Audio file upload failed.' ? error : 'Error updating university.';\r\n            this.toastr.error(errorMessage);\r\n        });\r\n    }\r\n    get serviceLinks() {\r\n        return this.addNewUniversityForm.get('INS_serviceLinks');\r\n    }\r\n    get insightFormArray() {\r\n        return this.addNewUniversityForm.get('INS_HrInsights');\r\n    }\r\n    createServiceLinkGroup(linkKey) {\r\n        let group = {};\r\n        group[linkKey] = ['', [Validators.pattern('https?://.+')]];\r\n        group['SL_description'] = [''];\r\n        const formGroup = this.formBuilder.group(group);\r\n        // Apply the custom validator\r\n        formGroup.setValidators(this.linkDescriptionValidator(linkKey, 'SL_description'));\r\n        return formGroup;\r\n    }\r\n    linkDescriptionValidator(linkKey, descriptionKey) {\r\n        return (formGroup) => {\r\n            const linkControl = formGroup.get(linkKey);\r\n            const descriptionControl = formGroup.get(descriptionKey);\r\n            if ((linkControl === null || linkControl === void 0 ? void 0 : linkControl.value) && !(descriptionControl === null || descriptionControl === void 0 ? void 0 : descriptionControl.value)) {\r\n                descriptionControl === null || descriptionControl === void 0 ? void 0 : descriptionControl.setErrors({ required: true });\r\n                return { descriptionRequired: true };\r\n            }\r\n            else {\r\n                descriptionControl === null || descriptionControl === void 0 ? void 0 : descriptionControl.setErrors(null);\r\n                return null;\r\n            }\r\n        };\r\n    }\r\n    addInsight() {\r\n        this.insightFormArray.push(this.createInsight());\r\n    }\r\n    removeInsight(index) {\r\n        this.insightFormArray.removeAt(index);\r\n        this.audioUrls.splice(index, 1); // Remove URL for deleted row\r\n        this.isPlaying.splice(index, 1); // Remove playback state for deleted row\r\n        this.audioFiles.splice(index, 1); // Remove file object for deleted row\r\n    }\r\n    toggleAudio(index) {\r\n        const audioElements = this.audioPlayers.toArray();\r\n        const audioElement = audioElements[index].nativeElement;\r\n        // Stop all other audio\r\n        this.stopAllAudio(index);\r\n        // Toggle play/pause for the current audio element\r\n        if (this.isPlaying[index]) {\r\n            audioElement.pause();\r\n        }\r\n        else {\r\n            audioElement.src = this.audioUrls[index];\r\n            audioElement.play();\r\n        }\r\n        // Update the playback state\r\n        this.isPlaying[index] = !this.isPlaying[index];\r\n    }\r\n    stopAllAudio(currentIndex) {\r\n        this.audioPlayers.forEach((audioPlayer, index) => {\r\n            if (index !== currentIndex) {\r\n                audioPlayer.nativeElement.pause();\r\n                this.isPlaying[index] = false;\r\n            }\r\n        });\r\n    }\r\n    stopAudio(index) {\r\n        const audioElements = this.audioPlayers.toArray();\r\n        const audioElement = audioElements[index].nativeElement;\r\n        audioElement.pause();\r\n        audioElement.currentTime = 0; // Reset to the beginning\r\n        this.isPlaying[index] = false;\r\n    }\r\n    createInsight() {\r\n        return this.formBuilder.group({\r\n            HRI_title: [''],\r\n            HRI_name: [''],\r\n            HRI_position: [''],\r\n            HRI_link: [null]\r\n        });\r\n        this.isPlaying.push(false); // Initialize playback state for new row\r\n        this.audioUrls.push(''); // Initialize empty URL for new row\r\n    }\r\n    onAudioSelected(event, index) {\r\n        let audiofile;\r\n        const selectedFile = event.target.files[0];\r\n        if (selectedFile) {\r\n            const newFileName = FileValidator.addTimestamp(selectedFile.name);\r\n            audiofile = new File([selectedFile], newFileName, { type: selectedFile.type });\r\n        }\r\n        else {\r\n            audiofile = null;\r\n        }\r\n        if (!audiofile) {\r\n            this.audioUrls[index] = '';\r\n            this.stopAudio(index);\r\n            return;\r\n        }\r\n        const audiofileType = audiofile.type.split('/')[0]; // Get the file type (e.g., 'audio', 'video', etc.)\r\n        if (audiofileType !== 'audio') {\r\n            // Reset the file input to clear the selected file\r\n            event.target.value = '';\r\n            this.toastr.info('Please select an audio file.');\r\n            return;\r\n        }\r\n        // Store the file object in the array for later upload\r\n        this.audioFiles[index] = audiofile;\r\n        console.log('Audio file :', audiofile);\r\n        const reader = new FileReader();\r\n        reader.onload = () => {\r\n            // Store audio URL\r\n            this.audioUrls[index] = reader.result;\r\n        };\r\n        reader.readAsDataURL(audiofile);\r\n    }\r\n    uploadAudioFiles(files) {\r\n        const uploadPromises = files.map((file, index) => this.uploadSingleAudioFile(file, index));\r\n        return Promise.all(uploadPromises);\r\n    }\r\n    // Updated uploadSingleAudioFile function\r\n    uploadSingleAudioFile(file, index) {\r\n        return new Promise((resolve, reject) => {\r\n            this.dataTransferService.uploadurl(file).subscribe((res) => {\r\n                console.log('Upload successful', file.name);\r\n                const fileUrl = this.baseUrl + file.name;\r\n                this.audioUrls[index] = fileUrl;\r\n                resolve(fileUrl);\r\n            }, (error) => {\r\n                console.error('Upload error', error);\r\n                this.toastr.error('Failed to upload audio file');\r\n                reject(error);\r\n            });\r\n        });\r\n    }\r\n    onFileSelected(event) {\r\n        var _a;\r\n        let selectedFile = event.target.files[0];\r\n        if (event.target.files.length === 0) {\r\n            // Reset both imageName and imageSrc when no file is selected\r\n            this.imageName = null;\r\n            this.imageSrc = null;\r\n            return;\r\n        }\r\n        const newFileName = FileValidator.addTimestamp(selectedFile.name);\r\n        this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });\r\n        if (this.imageName) {\r\n            const formControl = this.addNewUniversityForm.get('INS_dp');\r\n            formControl === null || formControl === void 0 ? void 0 : formControl.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));\r\n            formControl === null || formControl === void 0 ? void 0 : formControl.updateValueAndValidity();\r\n        }\r\n        const fileType = this.imageName.type.split('/')[0];\r\n        const fileExtension = (_a = this.imageName.name.split('.').pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();\r\n        if (fileType !== 'image' || fileExtension === 'svg') {\r\n            event.target.value = '';\r\n            this.toastr.info('Please select an image file (excluding SVG).');\r\n            this.imageName = null;\r\n            this.imageSrc = null;\r\n            return;\r\n        }\r\n        if (this.imageName && fileType == 'image') {\r\n            const reader = new FileReader();\r\n            reader.onload = (e) => {\r\n                var _a;\r\n                this.imageSrc = (_a = e.target) === null || _a === void 0 ? void 0 : _a.result;\r\n            };\r\n            reader.readAsDataURL(this.imageName);\r\n        }\r\n        else {\r\n            this.imageSrc = null; // Reset imageSrc if no file selected\r\n        }\r\n        console.log('imageName', this.imageName);\r\n    }\r\n    uploadLogoUrl() {\r\n        return new Promise((resolve, reject) => {\r\n            if (!this.imageName) {\r\n                reject('Please select an image.');\r\n                return;\r\n            }\r\n            console.log('Uploading image:', this.imageName);\r\n            this.dataTransferService.uploadurl(this.imageName).subscribe((res) => {\r\n                const fileUrl = this.baseUrl + this.imageName.name; // Ensure you're concatenating correctly\r\n                resolve(fileUrl);\r\n            }, (error) => {\r\n                reject('Error uploading image: ' + error.message || error);\r\n            });\r\n        });\r\n    }\r\n    async saveUniversity() {\r\n        var _a, _b, _c, _d, _e;\r\n        if (this.addNewUniversityForm.invalid) {\r\n            console.log('this.job.value', this.addNewUniversityForm.value);\r\n            this.ngxSpinnerService.hide('globalSpinner');\r\n            this.toastr.info('Please fill all required fields and ensure they are filled correctly.');\r\n            return;\r\n        }\r\n        else {\r\n            try {\r\n                this.ngxSpinnerService.show('globalSpinner');\r\n                if (this.imageName) {\r\n                    await this.uploadLogoUrl();\r\n                    this.fileUrl = this.baseUrl + this.imageName.name;\r\n                }\r\n                const audioUrls = await this.uploadAudioFiles(this.audioFiles);\r\n                console.log(\"this.audioFiles\", this.audioFiles);\r\n                this.insightFormArray.controls.forEach((control, index) => {\r\n                    var _a;\r\n                    const group = control;\r\n                    (_a = group.get('HRI_link')) === null || _a === void 0 ? void 0 : _a.setValue(audioUrls[index]);\r\n                });\r\n                console.log(\"this.addNewUniversityForm.value\", this.addNewUniversityForm.value);\r\n                const data = {\r\n                    INS_title: (_a = this.addNewUniversityForm.get('INS_title')) === null || _a === void 0 ? void 0 : _a.value,\r\n                    INS_dp: this.fileUrl,\r\n                    INS_website: (_b = this.addNewUniversityForm.get('INS_website')) === null || _b === void 0 ? void 0 : _b.value,\r\n                    INS_service: (_c = this.addNewUniversityForm.get('INS_service')) === null || _c === void 0 ? void 0 : _c.value,\r\n                    INS_serviceLinks: (_d = this.addNewUniversityForm.get('INS_serviceLinks')) === null || _d === void 0 ? void 0 : _d.value,\r\n                    INS_HrInsights: (_e = this.addNewUniversityForm.get('INS_HrInsights')) === null || _e === void 0 ? void 0 : _e.value,\r\n                    INS_city: '',\r\n                    INS_country: '',\r\n                    INS_state: ''\r\n                };\r\n                console.log(\"Add University Data : \", data);\r\n                this.dataTransferService.addUniversity(data).subscribe((res) => {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    this.toastr.success(\"University added successfully\");\r\n                    this.router.navigate([`/actions/universities`]);\r\n                }, (error) => {\r\n                    this.ngxSpinnerService.hide('globalSpinner');\r\n                    console.log(\"Error\", error);\r\n                    this.toastr.error(\"Unable to add university\");\r\n                });\r\n            }\r\n            catch (error) {\r\n                this.ngxSpinnerService.hide('globalSpinner');\r\n                console.error(\"An error occurred while saving the university\", error);\r\n                this.toastr.error(\"An error occurred while saving the university\");\r\n            }\r\n        }\r\n    }\r\n    onSubmit() {\r\n        if (this.title == 'Add') {\r\n            this.saveUniversity();\r\n        }\r\n        else {\r\n            this.updateUniversity();\r\n        }\r\n    }\r\n}\r\nAddEditUniversityListComponent.ɵfac = function AddEditUniversityListComponent_Factory(t) { return new (t || AddEditUniversityListComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataTransferService), i0.ɵɵdirectiveInject(i3.DomSanitizer), i0.ɵɵdirectiveInject(i4.HttpClient), i0.ɵɵdirectiveInject(i5.NgxSpinnerService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.ToastrService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i8.DatePipe)); };\r\nAddEditUniversityListComponent.ɵcmp = /*@__PURE__*/ i0.ɵɵdefineComponent({ type: AddEditUniversityListComponent, selectors: [[\"app-add-edit-university-list\"]], viewQuery: function AddEditUniversityListComponent_Query(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵviewQuery(_c0, 5);\r\n    } if (rf & 2) {\r\n        let _t;\r\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.audioPlayers = _t);\r\n    } }, decls: 45, vars: 13, consts: [[1, \"content-wrapper\"], [1, \"row\"], [1, \"col-12\", \"grid-margin\", \"stretch-card\"], [1, \"card\"], [1, \"card-header\", \"card-title\", \"bg-primary\", \"rounded-top\", \"text-white\", \"text-center\", \"mb-0\"], [1, \"card-body\"], [1, \"forms-sample\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\", \"col-lg-6\"], [\"for\", \"INS_title\", 1, \"required-field\"], [\"type\", \"text\", \"id\", \"INS_title\", \"formControlName\", \"INS_title\", \"required\", \"\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [\"for\", \"INS_dp\"], [1, \"logo-input-container\"], [\"type\", \"file\", \"formControlName\", \"INS_dp\", \"accept\", \"image/*\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\", \"change\"], [\"alt\", \"Employer Logo\", \"class\", \"img-preview\", 3, \"src\", 4, \"ngIf\"], [\"class\", \"warning\", 4, \"ngIf\"], [\"for\", \"INS_service\", 1, \"required-field\"], [\"id\", \"INS_service\", \"formControlName\", \"INS_service\", \"required\", \"\", \"placeholder\", \"Enter Description\", \"rows\", \"3\", \"cols\", \"50\", 1, \"form-control\", \"form-control-sm\", 3, \"readOnly\"], [\"for\", \"INS_website\", 1, \"required-field\"], [\"type\", \"text\", \"id\", \"INS_website\", \"formControlName\", \"INS_website\", \"placeholder\", \"Enter URL\", \"required\", \"\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [1, \"mb-3\", \"py-2\"], [\"formArrayName\", \"INS_serviceLinks\"], [3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"formArrayName\", \"INS_HrInsights\"], [\"class\", \"row mb-3\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"class\", \"btn btn-sm add-insight-btn btn-outline-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"text-center\", \"mt-2\"], [\"type\", \"submit\", \"class\", \"btn btn-primary mr-2\", 4, \"ngIf\"], [\"type\", \"button\", \"routerLink\", \"/actions/universities\", 1, \"btn\", \"btn-light\"], [\"alt\", \"Employer Logo\", 1, \"img-preview\", 3, \"src\"], [1, \"warning\"], [4, \"ngIf\"], [3, \"formGroupName\"], [2, \"font-size\", \"0.9rem\"], [1, \"row\", \"mt-2\"], [2, \"font-size\", \"0.8rem\", 3, \"ngClass\", \"for\"], [\"type\", \"text\", \"placeholder\", \"Enter URL\", 1, \"form-control\", \"form-control-sm\", 3, \"id\", \"formControlName\", \"readonly\"], [\"for\", \"SL_description\", 2, \"font-size\", \"0.8rem\", 3, \"ngClass\"], [\"id\", \"SL_description\", \"formControlName\", \"SL_description\", \"placeholder\", \"Enter Description\", \"rows\", \"1\", 1, \"form-control\", \"form-control-sm\", 3, \"readonly\"], [1, \"row\", \"mb-3\", 3, \"formGroupName\"], [1, \"col-lg-11\"], [1, \"row\", \"mr-0\"], [1, \"form-group\", \"col-lg-3\"], [\"for\", \"HRI_title\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_title\", \"placeholder\", \"Enter Title\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"HRI_name\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_name\", \"placeholder\", \"Enter Name\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"HRI_position\", 1, \"subtitle\"], [\"type\", \"text\", \"formControlName\", \"HRI_position\", \"placeholder\", \"Enter Position\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"link\", 1, \"subtitle\"], [\"type\", \"file\", \"id\", \"link\", \"accept\", \"audio/*\", 1, \"form-control\", \"form-control-sm\", 3, \"change\"], [1, \"col-lg-1\", \"px-0\", \"d-flex\", \"align-items-center\", \"btns\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm mr-2\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn insight-btn btn-sm btn-outline-danger\", 3, \"click\", 4, \"ngIf\"], [\"controls\", \"\", 2, \"display\", \"none\"], [\"audioPlayer\", \"\"], [\"type\", \"audio/mpeg\", 3, \"src\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"mr-2\", 3, \"ngClass\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"btn\", \"insight-btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"fas\", \"fa-minus\", \"icon\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"add-insight-btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"icon\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"mr-2\"]], template: function AddEditUniversityListComponent_Template(rf, ctx) { if (rf & 1) {\r\n        i0.ɵɵelementStart(0, \"app-sidebar\");\r\n        i0.ɵɵelementStart(1, \"div\", 0);\r\n        i0.ɵɵelementStart(2, \"div\", 1);\r\n        i0.ɵɵelementStart(3, \"div\", 2);\r\n        i0.ɵɵelementStart(4, \"div\", 3);\r\n        i0.ɵɵelementStart(5, \"div\", 4);\r\n        i0.ɵɵtext(6);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(7, \"div\", 5);\r\n        i0.ɵɵelementStart(8, \"form\", 6);\r\n        i0.ɵɵlistener(\"ngSubmit\", function AddEditUniversityListComponent_Template_form_ngSubmit_8_listener() { return ctx.onSubmit(); });\r\n        i0.ɵɵelementStart(9, \"div\", 1);\r\n        i0.ɵɵelementStart(10, \"div\", 7);\r\n        i0.ɵɵelementStart(11, \"label\", 8);\r\n        i0.ɵɵtext(12, \"University Name\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(13, \"input\", 9);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(14, \"div\", 7);\r\n        i0.ɵɵelementStart(15, \"label\", 10);\r\n        i0.ɵɵtext(16, \"University Logo\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(17, \"div\", 11);\r\n        i0.ɵɵelementStart(18, \"input\", 12);\r\n        i0.ɵɵlistener(\"change\", function AddEditUniversityListComponent_Template_input_change_18_listener($event) { return ctx.onFileSelected($event); });\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(19, AddEditUniversityListComponent_img_19_Template, 1, 1, \"img\", 13);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(20, AddEditUniversityListComponent_div_20_Template, 2, 0, \"div\", 14);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(21, \"div\", 7);\r\n        i0.ɵɵelementStart(22, \"label\", 15);\r\n        i0.ɵɵtext(23, \"University Service Description\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(24, \"textarea\", 16);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(25, \"div\", 7);\r\n        i0.ɵɵelementStart(26, \"label\", 17);\r\n        i0.ɵɵtext(27, \"Website\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(28, \"input\", 18);\r\n        i0.ɵɵtemplate(29, AddEditUniversityListComponent_div_29_Template, 2, 1, \"div\", 14);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(30, \"hr\");\r\n        i0.ɵɵelementStart(31, \"h6\", 19);\r\n        i0.ɵɵtext(32, \"Service Links\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(33, \"div\", 20);\r\n        i0.ɵɵtemplate(34, AddEditUniversityListComponent_div_34_Template, 14, 15, \"div\", 21);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelement(35, \"hr\");\r\n        i0.ɵɵelementStart(36, \"div\", 22);\r\n        i0.ɵɵelementStart(37, \"h6\", 19);\r\n        i0.ɵɵtext(38, \"Insights\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵtemplate(39, AddEditUniversityListComponent_div_39_Template, 26, 4, \"div\", 23);\r\n        i0.ɵɵtemplate(40, AddEditUniversityListComponent_button_40_Template, 3, 0, \"button\", 24);\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementStart(41, \"div\", 25);\r\n        i0.ɵɵtemplate(42, AddEditUniversityListComponent_button_42_Template, 2, 0, \"button\", 26);\r\n        i0.ɵɵelementStart(43, \"button\", 27);\r\n        i0.ɵɵtext(44, \"Cancel\");\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n        i0.ɵɵelementEnd();\r\n    } if (rf & 2) {\r\n        let tmp_5_0;\r\n        let tmp_8_0;\r\n        i0.ɵɵadvance(6);\r\n        i0.ɵɵtextInterpolate1(\"\", ctx.title, \" University\");\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"formGroup\", ctx.addNewUniversityForm);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.imageSrc);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.addNewUniversityForm.get(\"INS_dp\")) == null ? null : tmp_5_0.errors == null ? null : tmp_5_0.errors.fileSizeValidator);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"readOnly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(4);\r\n        i0.ɵɵproperty(\"readonly\", ctx.isReadonly);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.addNewUniversityForm.get(\"INS_website\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.addNewUniversityForm.get(\"INS_website\")) == null ? null : tmp_8_0.touched));\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.serviceLinks.controls);\r\n        i0.ɵɵadvance(5);\r\n        i0.ɵɵproperty(\"ngForOf\", ctx.insightFormArray.controls);\r\n        i0.ɵɵadvance(1);\r\n        i0.ɵɵproperty(\"ngIf\", ctx.insightFormArray.length < 3);\r\n        i0.ɵɵadvance(2);\r\n        i0.ɵɵproperty(\"ngIf\", !ctx.isReadonly);\r\n    } }, directives: [i9.SidebarComponent, i1.ɵNgNoValidate, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.DefaultValueAccessor, i1.NgControlStatus, i1.FormControlName, i1.RequiredValidator, i8.NgIf, i1.FormArrayName, i8.NgForOf, i6.RouterLink, i1.FormGroupName, i8.NgClass], styles: [\".fa-plus[_ngcontent-%COMP%] {\\n  font-size: small;\\n}\\n\\n@media screen and (min-width: 992px) and (max-width: 1224px) {\\n  .insight-btn[_ngcontent-%COMP%] {\\n    padding: 7px 8px;\\n  }\\n\\n  .fas[_ngcontent-%COMP%] {\\n    font-size: small;\\n  }\\n}\\n\\n@media screen and (min-width: 320px) and (max-width: 768px) {\\n  .btns[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: center;\\n    margin-bottom: 20px;\\n  }\\n\\n  .add-insight-btn[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFkZC1lZGl0LXVuaXZlcnNpdHktbGlzdC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGdCQUFBO0FBQ0o7O0FBRUU7RUFDRTtJQUNFLGdCQUFBO0VBQ0o7O0VBRUU7SUFDRSxnQkFBQTtFQUNKO0FBQ0Y7O0FBR0U7RUFDQTtJQUNFLGFBQUE7SUFDQSx1QkFBQTtJQUNBLG1CQUFBO0VBREY7O0VBSUE7SUFDRSxtQkFBQTtFQURGO0FBQ0YiLCJmaWxlIjoiYWRkLWVkaXQtdW5pdmVyc2l0eS1saXN0LmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLmZhLXBsdXN7XHJcbiAgICBmb250LXNpemU6IHNtYWxsO1xyXG4gICB9XHJcbiAgXHJcbiAgQG1lZGlhIHNjcmVlbiBhbmQgKG1pbi13aWR0aDogOTkycHgpIGFuZCAobWF4LXdpZHRoOiAxMjI0cHgpIHtcclxuICAgIC5pbnNpZ2h0LWJ0bntcclxuICAgICAgcGFkZGluZzogN3B4IDhweDtcclxuICAgIH1cclxuICBcclxuICAgIC5mYXN7XHJcbiAgICAgIGZvbnQtc2l6ZTogc21hbGw7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBcclxuICBAbWVkaWEgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAzMjBweCkgYW5kIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLmJ0bnN7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG4gIH1cclxuICBcclxuICAuYWRkLWluc2lnaHQtYnRue1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICB9XHJcbiAgfVxyXG4gICJdfQ== */\"] });\r\n"]}, "metadata": {}, "sourceType": "module"}
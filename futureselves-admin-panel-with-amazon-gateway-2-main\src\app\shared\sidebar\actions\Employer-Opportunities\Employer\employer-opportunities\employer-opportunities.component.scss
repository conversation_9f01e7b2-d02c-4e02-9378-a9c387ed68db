.partner{
  margin-top: 30px;
}

.partner-card {
  position: relative;
  background-color: #ffffffed;
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 10px;
  box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  height: 145px; 
  display: flex;
  justify-content: center;
  align-items: center; 
}

.partner-card:hover {
  transform: translateY(-4px);
  box-shadow: 1px 2px 8px 12px rgba(0, 0, 0, 0.15);
}

.partner-card .card-img-top {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  max-height: 95px;
  max-width: 90%;
  object-fit: contain; 
}

.partner-card .card-body {
  padding: 20px;
}

.partner-card .card-title {
  font-size: 18px;
  font-weight: bold;
  margin-top: 10px;
}

// .head-Home {
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
// }



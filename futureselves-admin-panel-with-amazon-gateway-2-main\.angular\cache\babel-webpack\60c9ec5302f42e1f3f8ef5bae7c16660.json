{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { ElementRef, Injector, Directive, EventEmitter, Inject, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\n\nfunction throwNullPortalError() {\n  throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\n\n\nfunction throwPortalAlreadyAttachedError() {\n  throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\n\n\nfunction throwPortalOutletAlreadyDisposedError() {\n  throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\n\n\nfunction throwUnknownPortalTypeError() {\n  throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' + 'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\n\n\nfunction throwNullPortalOutletError() {\n  throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\n\n\nfunction throwNoPortalAttachedError() {\n  throw Error('Attempting to detach a portal that is not attached to a host');\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\n\n\nclass Portal {\n  /** Attach this portal to a host. */\n  attach(host) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (host == null) {\n        throwNullPortalOutletError();\n      }\n\n      if (host.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n    }\n\n    this._attachedHost = host;\n    return host.attach(this);\n  }\n  /** Detach this portal from its host */\n\n\n  detach() {\n    let host = this._attachedHost;\n\n    if (host != null) {\n      this._attachedHost = null;\n      host.detach();\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwNoPortalAttachedError();\n    }\n  }\n  /** Whether this portal is attached to a host. */\n\n\n  get isAttached() {\n    return this._attachedHost != null;\n  }\n  /**\n   * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n   * the PortalOutlet when it is performing an `attach()` or `detach()`.\n   */\n\n\n  setAttachedHost(host) {\n    this._attachedHost = host;\n  }\n\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\n\n\nclass ComponentPortal extends Portal {\n  constructor(component, viewContainerRef, injector, componentFactoryResolver) {\n    super();\n    this.component = component;\n    this.viewContainerRef = viewContainerRef;\n    this.injector = injector;\n    this.componentFactoryResolver = componentFactoryResolver;\n  }\n\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\n\n\nclass TemplatePortal extends Portal {\n  constructor(template, viewContainerRef, context) {\n    super();\n    this.templateRef = template;\n    this.viewContainerRef = viewContainerRef;\n    this.context = context;\n  }\n\n  get origin() {\n    return this.templateRef.elementRef;\n  }\n  /**\n   * Attach the portal to the provided `PortalOutlet`.\n   * When a context is provided it will override the `context` property of the `TemplatePortal`\n   * instance.\n   */\n\n\n  attach(host, context = this.context) {\n    this.context = context;\n    return super.attach(host);\n  }\n\n  detach() {\n    this.context = undefined;\n    return super.detach();\n  }\n\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\n\n\nclass DomPortal extends Portal {\n  constructor(element) {\n    super();\n    this.element = element instanceof ElementRef ? element.nativeElement : element;\n  }\n\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\n\n\nclass BasePortalOutlet {\n  constructor() {\n    /** Whether this host has already been permanently disposed. */\n    this._isDisposed = false; // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n\n    this.attachDomPortal = null;\n  }\n  /** Whether this host has an attached portal. */\n\n\n  hasAttached() {\n    return !!this._attachedPortal;\n  }\n  /** Attaches a portal. */\n\n\n  attach(portal) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!portal) {\n        throwNullPortalError();\n      }\n\n      if (this.hasAttached()) {\n        throwPortalAlreadyAttachedError();\n      }\n\n      if (this._isDisposed) {\n        throwPortalOutletAlreadyDisposedError();\n      }\n    }\n\n    if (portal instanceof ComponentPortal) {\n      this._attachedPortal = portal;\n      return this.attachComponentPortal(portal);\n    } else if (portal instanceof TemplatePortal) {\n      this._attachedPortal = portal;\n      return this.attachTemplatePortal(portal); // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n    } else if (this.attachDomPortal && portal instanceof DomPortal) {\n      this._attachedPortal = portal;\n      return this.attachDomPortal(portal);\n    }\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throwUnknownPortalTypeError();\n    }\n  }\n  /** Detaches a previously attached portal. */\n\n\n  detach() {\n    if (this._attachedPortal) {\n      this._attachedPortal.setAttachedHost(null);\n\n      this._attachedPortal = null;\n    }\n\n    this._invokeDisposeFn();\n  }\n  /** Permanently dispose of this portal host. */\n\n\n  dispose() {\n    if (this.hasAttached()) {\n      this.detach();\n    }\n\n    this._invokeDisposeFn();\n\n    this._isDisposed = true;\n  }\n  /** @docs-private */\n\n\n  setDisposeFn(fn) {\n    this._disposeFn = fn;\n  }\n\n  _invokeDisposeFn() {\n    if (this._disposeFn) {\n      this._disposeFn();\n\n      this._disposeFn = null;\n    }\n  }\n\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass BasePortalHost extends BasePortalOutlet {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\n\n\nclass DomPortalOutlet extends BasePortalOutlet {\n  /**\n   * @param outletElement Element into which the content is projected.\n   * @param _componentFactoryResolver Used to resolve the component factory.\n   *   Only required when attaching component portals.\n   * @param _appRef Reference to the application. Only used in component portals when there\n   *   is no `ViewContainerRef` available.\n   * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n   *   have one. Only used for component portals.\n   * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n   *   become a required parameter.\n   */\n  constructor(\n  /** Element into which the content is projected. */\n  outletElement, _componentFactoryResolver, _appRef, _defaultInjector,\n  /**\n   * @deprecated `_document` Parameter to be made required.\n   * @breaking-change 10.0.0\n   */\n  _document) {\n    super();\n    this.outletElement = outletElement;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._defaultInjector = _defaultInjector;\n    /**\n     * Attaches a DOM portal by transferring its content into the outlet.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n\n    this.attachDomPortal = portal => {\n      // @breaking-change 10.0.0 Remove check and error once the\n      // `_document` constructor parameter is required.\n      if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Cannot attach DOM portal without _document constructor parameter');\n      }\n\n      const element = portal.element;\n\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      } // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n\n\n      const anchorNode = this._document.createComment('dom-portal');\n\n      element.parentNode.insertBefore(anchorNode, element);\n      this.outletElement.appendChild(element);\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        // We can't use `replaceWith` here because IE doesn't support it.\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n\n    this._document = _document;\n  }\n  /**\n   * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n   * @param portal Portal to be attached\n   * @returns Reference to the created component.\n   */\n\n\n  attachComponentPortal(portal) {\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !resolver) {\n      throw Error('Cannot attach component portal to outlet without a ComponentFactoryResolver.');\n    }\n\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    let componentRef; // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n    // for the component (in terms of Angular's component tree, not rendering).\n    // When the ViewContainerRef is missing, we use the factory to create the component directly\n    // and then manually attach the view to the application.\n\n    if (portal.viewContainerRef) {\n      componentRef = portal.viewContainerRef.createComponent(componentFactory, portal.viewContainerRef.length, portal.injector || portal.viewContainerRef.injector);\n      this.setDisposeFn(() => componentRef.destroy());\n    } else {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n        throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n      }\n\n      componentRef = componentFactory.create(portal.injector || this._defaultInjector || Injector.NULL);\n\n      this._appRef.attachView(componentRef.hostView);\n\n      this.setDisposeFn(() => {\n        // Verify that the ApplicationRef has registered views before trying to detach a host view.\n        // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n        if (this._appRef.viewCount > 0) {\n          this._appRef.detachView(componentRef.hostView);\n        }\n\n        componentRef.destroy();\n      });\n    } // At this point the component has been instantiated, so we move it to the location in the DOM\n    // where we want it to be rendered.\n\n\n    this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n    this._attachedPortal = portal;\n    return componentRef;\n  }\n  /**\n   * Attaches a template portal to the DOM as an embedded view.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n\n\n  attachTemplatePortal(portal) {\n    let viewContainer = portal.viewContainerRef;\n    let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context); // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n    // But for the DomPortalOutlet the view can be added everywhere in the DOM\n    // (e.g Overlay Container) To move the view to the specified host element. We just\n    // re-append the existing root nodes.\n\n    viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode)); // Note that we want to detect changes after the nodes have been moved so that\n    // any directives inside the portal that are looking at the DOM inside a lifecycle\n    // hook won't be invoked too early.\n\n    viewRef.detectChanges();\n    this.setDisposeFn(() => {\n      let index = viewContainer.indexOf(viewRef);\n\n      if (index !== -1) {\n        viewContainer.remove(index);\n      }\n    });\n    this._attachedPortal = portal; // TODO(jelbourn): Return locals from view.\n\n    return viewRef;\n  }\n  /**\n   * Clears out a portal from the DOM.\n   */\n\n\n  dispose() {\n    super.dispose();\n    this.outletElement.remove();\n  }\n  /** Gets the root HTMLElement for an instantiated component. */\n\n\n  _getComponentRootNode(componentRef) {\n    return componentRef.hostView.rootNodes[0];\n  }\n\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass DomPortalHost extends DomPortalOutlet {}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\n\n\nclass CdkPortal extends TemplatePortal {\n  constructor(templateRef, viewContainerRef) {\n    super(templateRef, viewContainerRef);\n  }\n\n}\n\nCdkPortal.ɵfac = function CdkPortal_Factory(t) {\n  return new (t || CdkPortal)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n};\n\nCdkPortal.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkPortal,\n  selectors: [[\"\", \"cdkPortal\", \"\"]],\n  exportAs: [\"cdkPortal\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortal, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortal]',\n      exportAs: 'cdkPortal'\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }, {\n      type: i0.ViewContainerRef\n    }];\n  }, null);\n})();\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass TemplatePortalDirective extends CdkPortal {}\n\nTemplatePortalDirective.ɵfac = /* @__PURE__ */function () {\n  let ɵTemplatePortalDirective_BaseFactory;\n  return function TemplatePortalDirective_Factory(t) {\n    return (ɵTemplatePortalDirective_BaseFactory || (ɵTemplatePortalDirective_BaseFactory = i0.ɵɵgetInheritedFactory(TemplatePortalDirective)))(t || TemplatePortalDirective);\n  };\n}();\n\nTemplatePortalDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TemplatePortalDirective,\n  selectors: [[\"\", \"cdk-portal\", \"\"], [\"\", \"portal\", \"\"]],\n  exportAs: [\"cdkPortal\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkPortal,\n    useExisting: TemplatePortalDirective\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TemplatePortalDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-portal], [portal]',\n      exportAs: 'cdkPortal',\n      providers: [{\n        provide: CdkPortal,\n        useExisting: TemplatePortalDirective\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\n\n\nclass CdkPortalOutlet extends BasePortalOutlet {\n  constructor(_componentFactoryResolver, _viewContainerRef,\n  /**\n   * @deprecated `_document` parameter to be made required.\n   * @breaking-change 9.0.0\n   */\n  _document) {\n    super();\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._viewContainerRef = _viewContainerRef;\n    /** Whether the portal component is initialized. */\n\n    this._isInitialized = false;\n    /** Emits when a portal is attached to the outlet. */\n\n    this.attached = new EventEmitter();\n    /**\n     * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n\n    this.attachDomPortal = portal => {\n      // @breaking-change 9.0.0 Remove check and error once the\n      // `_document` constructor parameter is required.\n      if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Cannot attach DOM portal without _document constructor parameter');\n      }\n\n      const element = portal.element;\n\n      if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('DOM portal content must be attached to a parent node.');\n      } // Anchor used to save the element's previous position so\n      // that we can restore it when the portal is detached.\n\n\n      const anchorNode = this._document.createComment('dom-portal');\n\n      portal.setAttachedHost(this);\n      element.parentNode.insertBefore(anchorNode, element);\n\n      this._getRootNode().appendChild(element);\n\n      this._attachedPortal = portal;\n      super.setDisposeFn(() => {\n        if (anchorNode.parentNode) {\n          anchorNode.parentNode.replaceChild(element, anchorNode);\n        }\n      });\n    };\n\n    this._document = _document;\n  }\n  /** Portal associated with the Portal outlet. */\n\n\n  get portal() {\n    return this._attachedPortal;\n  }\n\n  set portal(portal) {\n    // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n    // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n    // and attach a portal programmatically in the parent component. When Angular does the first CD\n    // round, it will fire the setter with empty string, causing the user's content to be cleared.\n    if (this.hasAttached() && !portal && !this._isInitialized) {\n      return;\n    }\n\n    if (this.hasAttached()) {\n      super.detach();\n    }\n\n    if (portal) {\n      super.attach(portal);\n    }\n\n    this._attachedPortal = portal || null;\n  }\n  /** Component or view reference that is attached to the portal. */\n\n\n  get attachedRef() {\n    return this._attachedRef;\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    super.dispose();\n    this._attachedPortal = null;\n    this._attachedRef = null;\n  }\n  /**\n   * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n   *\n   * @param portal Portal to be attached to the portal outlet.\n   * @returns Reference to the created component.\n   */\n\n\n  attachComponentPortal(portal) {\n    portal.setAttachedHost(this); // If the portal specifies an origin, use that as the logical location of the component\n    // in the application tree. Otherwise use the location of this PortalOutlet.\n\n    const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n    const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n    const componentFactory = resolver.resolveComponentFactory(portal.component);\n    const ref = viewContainerRef.createComponent(componentFactory, viewContainerRef.length, portal.injector || viewContainerRef.injector); // If we're using a view container that's different from the injected one (e.g. when the portal\n    // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n    // inside of the alternate view container.\n\n    if (viewContainerRef !== this._viewContainerRef) {\n      this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n    }\n\n    super.setDisposeFn(() => ref.destroy());\n    this._attachedPortal = portal;\n    this._attachedRef = ref;\n    this.attached.emit(ref);\n    return ref;\n  }\n  /**\n   * Attach the given TemplatePortal to this PortalHost as an embedded View.\n   * @param portal Portal to be attached.\n   * @returns Reference to the created embedded view.\n   */\n\n\n  attachTemplatePortal(portal) {\n    portal.setAttachedHost(this);\n\n    const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context);\n\n    super.setDisposeFn(() => this._viewContainerRef.clear());\n    this._attachedPortal = portal;\n    this._attachedRef = viewRef;\n    this.attached.emit(viewRef);\n    return viewRef;\n  }\n  /** Gets the root node of the portal outlet. */\n\n\n  _getRootNode() {\n    const nativeElement = this._viewContainerRef.element.nativeElement; // The directive could be set on a template which will result in a comment\n    // node being the root. Use the comment's parent node if that is the case.\n\n    return nativeElement.nodeType === nativeElement.ELEMENT_NODE ? nativeElement : nativeElement.parentNode;\n  }\n\n}\n\nCdkPortalOutlet.ɵfac = function CdkPortalOutlet_Factory(t) {\n  return new (t || CdkPortalOutlet)(i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT));\n};\n\nCdkPortalOutlet.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkPortalOutlet,\n  selectors: [[\"\", \"cdkPortalOutlet\", \"\"]],\n  inputs: {\n    portal: [\"cdkPortalOutlet\", \"portal\"]\n  },\n  outputs: {\n    attached: \"attached\"\n  },\n  exportAs: [\"cdkPortalOutlet\"],\n  features: [i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkPortalOutlet, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalOutlet]',\n      exportAs: 'cdkPortalOutlet',\n      inputs: ['portal: cdkPortalOutlet']\n    }]\n  }], function () {\n    return [{\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, {\n    attached: [{\n      type: Output\n    }]\n  });\n})();\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\n\n\nclass PortalHostDirective extends CdkPortalOutlet {}\n\nPortalHostDirective.ɵfac = /* @__PURE__ */function () {\n  let ɵPortalHostDirective_BaseFactory;\n  return function PortalHostDirective_Factory(t) {\n    return (ɵPortalHostDirective_BaseFactory || (ɵPortalHostDirective_BaseFactory = i0.ɵɵgetInheritedFactory(PortalHostDirective)))(t || PortalHostDirective);\n  };\n}();\n\nPortalHostDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: PortalHostDirective,\n  selectors: [[\"\", \"cdkPortalHost\", \"\"], [\"\", \"portalHost\", \"\"]],\n  inputs: {\n    portal: [\"cdkPortalHost\", \"portal\"]\n  },\n  exportAs: [\"cdkPortalHost\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkPortalOutlet,\n    useExisting: PortalHostDirective\n  }]), i0.ɵɵInheritDefinitionFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalHostDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkPortalHost], [portalHost]',\n      exportAs: 'cdkPortalHost',\n      inputs: ['portal: cdkPortalHost'],\n      providers: [{\n        provide: CdkPortalOutlet,\n        useExisting: PortalHostDirective\n      }]\n    }]\n  }], null, null);\n})();\n\nclass PortalModule {}\n\nPortalModule.ɵfac = function PortalModule_Factory(t) {\n  return new (t || PortalModule)();\n};\n\nPortalModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: PortalModule\n});\nPortalModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PortalModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n      declarations: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\n\n\nclass PortalInjector {\n  constructor(_parentInjector, _customTokens) {\n    this._parentInjector = _parentInjector;\n    this._customTokens = _customTokens;\n  }\n\n  get(token, notFoundValue) {\n    const value = this._customTokens.get(token);\n\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n\n    return this._parentInjector.get(token, notFoundValue);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BasePortalHost, BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalHost, DomPortalOutlet, Portal, PortalHostDirective, PortalInjector, PortalModule, TemplatePortal, TemplatePortalDirective };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/cdk/fesm2020/portal.mjs"], "names": ["i0", "ElementRef", "Injector", "Directive", "EventEmitter", "Inject", "Output", "NgModule", "DOCUMENT", "throwNullPortalError", "Error", "throwPortalAlreadyAttachedError", "throwPortalOutletAlreadyDisposedError", "throwUnknownPortalTypeError", "throwNullPortalOutletError", "throwNoPortalAttachedError", "Portal", "attach", "host", "ngDevMode", "has<PERSON>tta<PERSON>", "_attachedHost", "detach", "isAttached", "setAttachedHost", "ComponentPortal", "constructor", "component", "viewContainerRef", "injector", "componentFactoryResolver", "TemplatePortal", "template", "context", "templateRef", "origin", "elementRef", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "element", "nativeElement", "BasePortalOutlet", "_isDisposed", "attachDomPortal", "_attachedPortal", "portal", "attachComponentPortal", "attachTemplatePortal", "_invokeDisposeFn", "dispose", "setDisposeFn", "fn", "_disposeFn", "BasePortalHost", "DomPortalOutlet", "outletElement", "_componentFactoryResolver", "_appRef", "_defaultInjector", "_document", "parentNode", "anchorNode", "createComment", "insertBefore", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "resolver", "componentFactory", "resolveComponentFactory", "componentRef", "createComponent", "length", "destroy", "create", "NULL", "attachView", "<PERSON><PERSON><PERSON><PERSON>", "viewCount", "detach<PERSON>iew", "_getComponentRootNode", "viewContainer", "viewRef", "createEmbeddedView", "rootNodes", "for<PERSON>ach", "rootNode", "detectChanges", "index", "indexOf", "remove", "DomPortalHost", "CdkPortal", "ɵfac", "TemplateRef", "ViewContainerRef", "ɵdir", "type", "args", "selector", "exportAs", "TemplatePortalDirective", "provide", "useExisting", "providers", "CdkPortalOutlet", "_viewContainerRef", "_isInitialized", "attached", "_getRootNode", "attachedRef", "_attachedRef", "ngOnInit", "ngOnDestroy", "ref", "emit", "clear", "nodeType", "ELEMENT_NODE", "ComponentFactoryResolver", "inputs", "decorators", "PortalHostDirective", "PortalModule", "ɵmod", "ɵinj", "exports", "declarations", "PortalInjector", "_parentInjector", "_customTokens", "get", "token", "notFoundValue", "value"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,QAArB,EAA+BC,SAA/B,EAA0CC,YAA1C,EAAwDC,MAAxD,EAAgEC,MAAhE,EAAwEC,QAAxE,QAAwF,eAAxF;AACA,SAASC,QAAT,QAAyB,iBAAzB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,SAASC,oBAAT,GAAgC;AAC5B,QAAMC,KAAK,CAAC,iCAAD,CAAX;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASC,+BAAT,GAA2C;AACvC,QAAMD,KAAK,CAAC,oCAAD,CAAX;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASE,qCAAT,GAAiD;AAC7C,QAAMF,KAAK,CAAC,6CAAD,CAAX;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASG,2BAAT,GAAuC;AACnC,QAAMH,KAAK,CAAC,kFACR,wCADO,CAAX;AAEH;AACD;AACA;AACA;AACA;;;AACA,SAASI,0BAAT,GAAsC;AAClC,QAAMJ,KAAK,CAAC,sDAAD,CAAX;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASK,0BAAT,GAAsC;AAClC,QAAML,KAAK,CAAC,8DAAD,CAAX;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMM,MAAN,CAAa;AACT;AACAC,EAAAA,MAAM,CAACC,IAAD,EAAO;AACT,QAAI,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,UAAID,IAAI,IAAI,IAAZ,EAAkB;AACdJ,QAAAA,0BAA0B;AAC7B;;AACD,UAAII,IAAI,CAACE,WAAL,EAAJ,EAAwB;AACpBT,QAAAA,+BAA+B;AAClC;AACJ;;AACD,SAAKU,aAAL,GAAqBH,IAArB;AACA,WAAOA,IAAI,CAACD,MAAL,CAAY,IAAZ,CAAP;AACH;AACD;;;AACAK,EAAAA,MAAM,GAAG;AACL,QAAIJ,IAAI,GAAG,KAAKG,aAAhB;;AACA,QAAIH,IAAI,IAAI,IAAZ,EAAkB;AACd,WAAKG,aAAL,GAAqB,IAArB;AACAH,MAAAA,IAAI,CAACI,MAAL;AACH,KAHD,MAIK,IAAI,OAAOH,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AACpDJ,MAAAA,0BAA0B;AAC7B;AACJ;AACD;;;AACc,MAAVQ,UAAU,GAAG;AACb,WAAO,KAAKF,aAAL,IAAsB,IAA7B;AACH;AACD;AACJ;AACA;AACA;;;AACIG,EAAAA,eAAe,CAACN,IAAD,EAAO;AAClB,SAAKG,aAAL,GAAqBH,IAArB;AACH;;AAnCQ;AAqCb;AACA;AACA;;;AACA,MAAMO,eAAN,SAA8BT,MAA9B,CAAqC;AACjCU,EAAAA,WAAW,CAACC,SAAD,EAAYC,gBAAZ,EAA8BC,QAA9B,EAAwCC,wBAAxC,EAAkE;AACzE;AACA,SAAKH,SAAL,GAAiBA,SAAjB;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAKC,QAAL,GAAgBA,QAAhB;AACA,SAAKC,wBAAL,GAAgCA,wBAAhC;AACH;;AAPgC;AASrC;AACA;AACA;;;AACA,MAAMC,cAAN,SAA6Bf,MAA7B,CAAoC;AAChCU,EAAAA,WAAW,CAACM,QAAD,EAAWJ,gBAAX,EAA6BK,OAA7B,EAAsC;AAC7C;AACA,SAAKC,WAAL,GAAmBF,QAAnB;AACA,SAAKJ,gBAAL,GAAwBA,gBAAxB;AACA,SAAKK,OAAL,GAAeA,OAAf;AACH;;AACS,MAANE,MAAM,GAAG;AACT,WAAO,KAAKD,WAAL,CAAiBE,UAAxB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACInB,EAAAA,MAAM,CAACC,IAAD,EAAOe,OAAO,GAAG,KAAKA,OAAtB,EAA+B;AACjC,SAAKA,OAAL,GAAeA,OAAf;AACA,WAAO,MAAMhB,MAAN,CAAaC,IAAb,CAAP;AACH;;AACDI,EAAAA,MAAM,GAAG;AACL,SAAKW,OAAL,GAAeI,SAAf;AACA,WAAO,MAAMf,MAAN,EAAP;AACH;;AAtB+B;AAwBpC;AACA;AACA;AACA;AACA;;;AACA,MAAMgB,SAAN,SAAwBtB,MAAxB,CAA+B;AAC3BU,EAAAA,WAAW,CAACa,OAAD,EAAU;AACjB;AACA,SAAKA,OAAL,GAAeA,OAAO,YAAYtC,UAAnB,GAAgCsC,OAAO,CAACC,aAAxC,GAAwDD,OAAvE;AACH;;AAJ0B;AAM/B;AACA;AACA;AACA;;;AACA,MAAME,gBAAN,CAAuB;AACnBf,EAAAA,WAAW,GAAG;AACV;AACA,SAAKgB,WAAL,GAAmB,KAAnB,CAFU,CAGV;;AACA,SAAKC,eAAL,GAAuB,IAAvB;AACH;AACD;;;AACAvB,EAAAA,WAAW,GAAG;AACV,WAAO,CAAC,CAAC,KAAKwB,eAAd;AACH;AACD;;;AACA3B,EAAAA,MAAM,CAAC4B,MAAD,EAAS;AACX,QAAI,OAAO1B,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,UAAI,CAAC0B,MAAL,EAAa;AACTpC,QAAAA,oBAAoB;AACvB;;AACD,UAAI,KAAKW,WAAL,EAAJ,EAAwB;AACpBT,QAAAA,+BAA+B;AAClC;;AACD,UAAI,KAAK+B,WAAT,EAAsB;AAClB9B,QAAAA,qCAAqC;AACxC;AACJ;;AACD,QAAIiC,MAAM,YAAYpB,eAAtB,EAAuC;AACnC,WAAKmB,eAAL,GAAuBC,MAAvB;AACA,aAAO,KAAKC,qBAAL,CAA2BD,MAA3B,CAAP;AACH,KAHD,MAIK,IAAIA,MAAM,YAAYd,cAAtB,EAAsC;AACvC,WAAKa,eAAL,GAAuBC,MAAvB;AACA,aAAO,KAAKE,oBAAL,CAA0BF,MAA1B,CAAP,CAFuC,CAGvC;AACH,KAJI,MAKA,IAAI,KAAKF,eAAL,IAAwBE,MAAM,YAAYP,SAA9C,EAAyD;AAC1D,WAAKM,eAAL,GAAuBC,MAAvB;AACA,aAAO,KAAKF,eAAL,CAAqBE,MAArB,CAAP;AACH;;AACD,QAAI,OAAO1B,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/CN,MAAAA,2BAA2B;AAC9B;AACJ;AACD;;;AACAS,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKsB,eAAT,EAA0B;AACtB,WAAKA,eAAL,CAAqBpB,eAArB,CAAqC,IAArC;;AACA,WAAKoB,eAAL,GAAuB,IAAvB;AACH;;AACD,SAAKI,gBAAL;AACH;AACD;;;AACAC,EAAAA,OAAO,GAAG;AACN,QAAI,KAAK7B,WAAL,EAAJ,EAAwB;AACpB,WAAKE,MAAL;AACH;;AACD,SAAK0B,gBAAL;;AACA,SAAKN,WAAL,GAAmB,IAAnB;AACH;AACD;;;AACAQ,EAAAA,YAAY,CAACC,EAAD,EAAK;AACb,SAAKC,UAAL,GAAkBD,EAAlB;AACH;;AACDH,EAAAA,gBAAgB,GAAG;AACf,QAAI,KAAKI,UAAT,EAAqB;AACjB,WAAKA,UAAL;;AACA,WAAKA,UAAL,GAAkB,IAAlB;AACH;AACJ;;AAlEkB;AAoEvB;AACA;AACA;AACA;;;AACA,MAAMC,cAAN,SAA6BZ,gBAA7B,CAA8C;AAG9C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMa,eAAN,SAA8Bb,gBAA9B,CAA+C;AAC3C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACIf,EAAAA,WAAW;AACX;AACA6B,EAAAA,aAFW,EAEIC,yBAFJ,EAE+BC,OAF/B,EAEwCC,gBAFxC;AAGX;AACJ;AACA;AACA;AACIC,EAAAA,SAPW,EAOA;AACP;AACA,SAAKJ,aAAL,GAAqBA,aAArB;AACA,SAAKC,yBAAL,GAAiCA,yBAAjC;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA;AACR;AACA;AACA;AACA;AACA;;AACQ,SAAKf,eAAL,GAAwBE,MAAD,IAAY;AAC/B;AACA;AACA,UAAI,CAAC,KAAKc,SAAN,KAAoB,OAAOxC,SAAP,KAAqB,WAArB,IAAoCA,SAAxD,CAAJ,EAAwE;AACpE,cAAMT,KAAK,CAAC,kEAAD,CAAX;AACH;;AACD,YAAM6B,OAAO,GAAGM,MAAM,CAACN,OAAvB;;AACA,UAAI,CAACA,OAAO,CAACqB,UAAT,KAAwB,OAAOzC,SAAP,KAAqB,WAArB,IAAoCA,SAA5D,CAAJ,EAA4E;AACxE,cAAMT,KAAK,CAAC,uDAAD,CAAX;AACH,OAT8B,CAU/B;AACA;;;AACA,YAAMmD,UAAU,GAAG,KAAKF,SAAL,CAAeG,aAAf,CAA6B,YAA7B,CAAnB;;AACAvB,MAAAA,OAAO,CAACqB,UAAR,CAAmBG,YAAnB,CAAgCF,UAAhC,EAA4CtB,OAA5C;AACA,WAAKgB,aAAL,CAAmBS,WAAnB,CAA+BzB,OAA/B;AACA,WAAKK,eAAL,GAAuBC,MAAvB;AACA,YAAMK,YAAN,CAAmB,MAAM;AACrB;AACA,YAAIW,UAAU,CAACD,UAAf,EAA2B;AACvBC,UAAAA,UAAU,CAACD,UAAX,CAAsBK,YAAtB,CAAmC1B,OAAnC,EAA4CsB,UAA5C;AACH;AACJ,OALD;AAMH,KAtBD;;AAuBA,SAAKF,SAAL,GAAiBA,SAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIb,EAAAA,qBAAqB,CAACD,MAAD,EAAS;AAC1B,UAAMqB,QAAQ,GAAIrB,MAAM,CAACf,wBAAP,IAAmC,KAAK0B,yBAA1D;;AACA,QAAI,CAAC,OAAOrC,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmD,CAAC+C,QAAxD,EAAkE;AAC9D,YAAMxD,KAAK,CAAC,8EAAD,CAAX;AACH;;AACD,UAAMyD,gBAAgB,GAAGD,QAAQ,CAACE,uBAAT,CAAiCvB,MAAM,CAAClB,SAAxC,CAAzB;AACA,QAAI0C,YAAJ,CAN0B,CAO1B;AACA;AACA;AACA;;AACA,QAAIxB,MAAM,CAACjB,gBAAX,EAA6B;AACzByC,MAAAA,YAAY,GAAGxB,MAAM,CAACjB,gBAAP,CAAwB0C,eAAxB,CAAwCH,gBAAxC,EAA0DtB,MAAM,CAACjB,gBAAP,CAAwB2C,MAAlF,EAA0F1B,MAAM,CAAChB,QAAP,IAAmBgB,MAAM,CAACjB,gBAAP,CAAwBC,QAArI,CAAf;AACA,WAAKqB,YAAL,CAAkB,MAAMmB,YAAY,CAACG,OAAb,EAAxB;AACH,KAHD,MAIK;AACD,UAAI,CAAC,OAAOrD,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KAAmD,CAAC,KAAKsC,OAA7D,EAAsE;AAClE,cAAM/C,KAAK,CAAC,qEAAD,CAAX;AACH;;AACD2D,MAAAA,YAAY,GAAGF,gBAAgB,CAACM,MAAjB,CAAwB5B,MAAM,CAAChB,QAAP,IAAmB,KAAK6B,gBAAxB,IAA4CxD,QAAQ,CAACwE,IAA7E,CAAf;;AACA,WAAKjB,OAAL,CAAakB,UAAb,CAAwBN,YAAY,CAACO,QAArC;;AACA,WAAK1B,YAAL,CAAkB,MAAM;AACpB;AACA;AACA,YAAI,KAAKO,OAAL,CAAaoB,SAAb,GAAyB,CAA7B,EAAgC;AAC5B,eAAKpB,OAAL,CAAaqB,UAAb,CAAwBT,YAAY,CAACO,QAArC;AACH;;AACDP,QAAAA,YAAY,CAACG,OAAb;AACH,OAPD;AAQH,KA7ByB,CA8B1B;AACA;;;AACA,SAAKjB,aAAL,CAAmBS,WAAnB,CAA+B,KAAKe,qBAAL,CAA2BV,YAA3B,CAA/B;AACA,SAAKzB,eAAL,GAAuBC,MAAvB;AACA,WAAOwB,YAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACItB,EAAAA,oBAAoB,CAACF,MAAD,EAAS;AACzB,QAAImC,aAAa,GAAGnC,MAAM,CAACjB,gBAA3B;AACA,QAAIqD,OAAO,GAAGD,aAAa,CAACE,kBAAd,CAAiCrC,MAAM,CAACX,WAAxC,EAAqDW,MAAM,CAACZ,OAA5D,CAAd,CAFyB,CAGzB;AACA;AACA;AACA;;AACAgD,IAAAA,OAAO,CAACE,SAAR,CAAkBC,OAAlB,CAA0BC,QAAQ,IAAI,KAAK9B,aAAL,CAAmBS,WAAnB,CAA+BqB,QAA/B,CAAtC,EAPyB,CAQzB;AACA;AACA;;AACAJ,IAAAA,OAAO,CAACK,aAAR;AACA,SAAKpC,YAAL,CAAkB,MAAM;AACpB,UAAIqC,KAAK,GAAGP,aAAa,CAACQ,OAAd,CAAsBP,OAAtB,CAAZ;;AACA,UAAIM,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdP,QAAAA,aAAa,CAACS,MAAd,CAAqBF,KAArB;AACH;AACJ,KALD;AAMA,SAAK3C,eAAL,GAAuBC,MAAvB,CAlByB,CAmBzB;;AACA,WAAOoC,OAAP;AACH;AACD;AACJ;AACA;;;AACIhC,EAAAA,OAAO,GAAG;AACN,UAAMA,OAAN;AACA,SAAKM,aAAL,CAAmBkC,MAAnB;AACH;AACD;;;AACAV,EAAAA,qBAAqB,CAACV,YAAD,EAAe;AAChC,WAAOA,YAAY,CAACO,QAAb,CAAsBO,SAAtB,CAAgC,CAAhC,CAAP;AACH;;AAtI0C;AAwI/C;AACA;AACA;AACA;;;AACA,MAAMO,aAAN,SAA4BpC,eAA5B,CAA4C;AAG5C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMqC,SAAN,SAAwB5D,cAAxB,CAAuC;AACnCL,EAAAA,WAAW,CAACQ,WAAD,EAAcN,gBAAd,EAAgC;AACvC,UAAMM,WAAN,EAAmBN,gBAAnB;AACH;;AAHkC;;AAKvC+D,SAAS,CAACC,IAAV;AAAA,mBAAsGD,SAAtG,EAA4F3F,EAA5F,mBAAiIA,EAAE,CAAC6F,WAApI,GAA4F7F,EAA5F,mBAA4JA,EAAE,CAAC8F,gBAA/J;AAAA;;AACAH,SAAS,CAACI,IAAV,kBAD4F/F,EAC5F;AAAA,QAA0F2F,SAA1F;AAAA;AAAA;AAAA,aAD4F3F,EAC5F;AAAA;;AACA;AAAA,qDAF4FA,EAE5F,mBAA2F2F,SAA3F,EAAkH,CAAC;AACvGK,IAAAA,IAAI,EAAE7F,SADiG;AAEvG8F,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,aADX;AAECC,MAAAA,QAAQ,EAAE;AAFX,KAAD;AAFiG,GAAD,CAAlH,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAEH,MAAAA,IAAI,EAAEhG,EAAE,CAAC6F;AAAX,KAAD,EAA2B;AAAEG,MAAAA,IAAI,EAAEhG,EAAE,CAAC8F;AAAX,KAA3B,CAAP;AAAmE,GAN7G;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMM,uBAAN,SAAsCT,SAAtC,CAAgD;;AAEhDS,uBAAuB,CAACR,IAAxB;AAAA;AAAA;AAAA,4FAf4F5F,EAe5F,uBAAoHoG,uBAApH,SAAoHA,uBAApH;AAAA;AAAA;;AACAA,uBAAuB,CAACL,IAAxB,kBAhB4F/F,EAgB5F;AAAA,QAAwGoG,uBAAxG;AAAA;AAAA;AAAA,aAhB4FpG,EAgB5F,oBAAgL,CACxK;AACIqG,IAAAA,OAAO,EAAEV,SADb;AAEIW,IAAAA,WAAW,EAAEF;AAFjB,GADwK,CAAhL,GAhB4FpG,EAgB5F;AAAA;;AAMA;AAAA,qDAtB4FA,EAsB5F,mBAA2FoG,uBAA3F,EAAgI,CAAC;AACrHJ,IAAAA,IAAI,EAAE7F,SAD+G;AAErH8F,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,wBADX;AAECC,MAAAA,QAAQ,EAAE,WAFX;AAGCI,MAAAA,SAAS,EAAE,CACP;AACIF,QAAAA,OAAO,EAAEV,SADb;AAEIW,QAAAA,WAAW,EAAEF;AAFjB,OADO;AAHZ,KAAD;AAF+G,GAAD,CAAhI;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMI,eAAN,SAA8B/D,gBAA9B,CAA+C;AAC3Cf,EAAAA,WAAW,CAAC8B,yBAAD,EAA4BiD,iBAA5B;AACX;AACJ;AACA;AACA;AACI9C,EAAAA,SALW,EAKA;AACP;AACA,SAAKH,yBAAL,GAAiCA,yBAAjC;AACA,SAAKiD,iBAAL,GAAyBA,iBAAzB;AACA;;AACA,SAAKC,cAAL,GAAsB,KAAtB;AACA;;AACA,SAAKC,QAAL,GAAgB,IAAIvG,YAAJ,EAAhB;AACA;AACR;AACA;AACA;AACA;AACA;;AACQ,SAAKuC,eAAL,GAAwBE,MAAD,IAAY;AAC/B;AACA;AACA,UAAI,CAAC,KAAKc,SAAN,KAAoB,OAAOxC,SAAP,KAAqB,WAArB,IAAoCA,SAAxD,CAAJ,EAAwE;AACpE,cAAMT,KAAK,CAAC,kEAAD,CAAX;AACH;;AACD,YAAM6B,OAAO,GAAGM,MAAM,CAACN,OAAvB;;AACA,UAAI,CAACA,OAAO,CAACqB,UAAT,KAAwB,OAAOzC,SAAP,KAAqB,WAArB,IAAoCA,SAA5D,CAAJ,EAA4E;AACxE,cAAMT,KAAK,CAAC,uDAAD,CAAX;AACH,OAT8B,CAU/B;AACA;;;AACA,YAAMmD,UAAU,GAAG,KAAKF,SAAL,CAAeG,aAAf,CAA6B,YAA7B,CAAnB;;AACAjB,MAAAA,MAAM,CAACrB,eAAP,CAAuB,IAAvB;AACAe,MAAAA,OAAO,CAACqB,UAAR,CAAmBG,YAAnB,CAAgCF,UAAhC,EAA4CtB,OAA5C;;AACA,WAAKqE,YAAL,GAAoB5C,WAApB,CAAgCzB,OAAhC;;AACA,WAAKK,eAAL,GAAuBC,MAAvB;AACA,YAAMK,YAAN,CAAmB,MAAM;AACrB,YAAIW,UAAU,CAACD,UAAf,EAA2B;AACvBC,UAAAA,UAAU,CAACD,UAAX,CAAsBK,YAAtB,CAAmC1B,OAAnC,EAA4CsB,UAA5C;AACH;AACJ,OAJD;AAKH,KAtBD;;AAuBA,SAAKF,SAAL,GAAiBA,SAAjB;AACH;AACD;;;AACU,MAANd,MAAM,GAAG;AACT,WAAO,KAAKD,eAAZ;AACH;;AACS,MAANC,MAAM,CAACA,MAAD,EAAS;AACf;AACA;AACA;AACA;AACA,QAAI,KAAKzB,WAAL,MAAsB,CAACyB,MAAvB,IAAiC,CAAC,KAAK6D,cAA3C,EAA2D;AACvD;AACH;;AACD,QAAI,KAAKtF,WAAL,EAAJ,EAAwB;AACpB,YAAME,MAAN;AACH;;AACD,QAAIuB,MAAJ,EAAY;AACR,YAAM5B,MAAN,CAAa4B,MAAb;AACH;;AACD,SAAKD,eAAL,GAAuBC,MAAM,IAAI,IAAjC;AACH;AACD;;;AACe,MAAXgE,WAAW,GAAG;AACd,WAAO,KAAKC,YAAZ;AACH;;AACDC,EAAAA,QAAQ,GAAG;AACP,SAAKL,cAAL,GAAsB,IAAtB;AACH;;AACDM,EAAAA,WAAW,GAAG;AACV,UAAM/D,OAAN;AACA,SAAKL,eAAL,GAAuB,IAAvB;AACA,SAAKkE,YAAL,GAAoB,IAApB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIhE,EAAAA,qBAAqB,CAACD,MAAD,EAAS;AAC1BA,IAAAA,MAAM,CAACrB,eAAP,CAAuB,IAAvB,EAD0B,CAE1B;AACA;;AACA,UAAMI,gBAAgB,GAAGiB,MAAM,CAACjB,gBAAP,IAA2B,IAA3B,GAAkCiB,MAAM,CAACjB,gBAAzC,GAA4D,KAAK6E,iBAA1F;AACA,UAAMvC,QAAQ,GAAGrB,MAAM,CAACf,wBAAP,IAAmC,KAAK0B,yBAAzD;AACA,UAAMW,gBAAgB,GAAGD,QAAQ,CAACE,uBAAT,CAAiCvB,MAAM,CAAClB,SAAxC,CAAzB;AACA,UAAMsF,GAAG,GAAGrF,gBAAgB,CAAC0C,eAAjB,CAAiCH,gBAAjC,EAAmDvC,gBAAgB,CAAC2C,MAApE,EAA4E1B,MAAM,CAAChB,QAAP,IAAmBD,gBAAgB,CAACC,QAAhH,CAAZ,CAP0B,CAQ1B;AACA;AACA;;AACA,QAAID,gBAAgB,KAAK,KAAK6E,iBAA9B,EAAiD;AAC7C,WAAKG,YAAL,GAAoB5C,WAApB,CAAgCiD,GAAG,CAACrC,QAAJ,CAAaO,SAAb,CAAuB,CAAvB,CAAhC;AACH;;AACD,UAAMjC,YAAN,CAAmB,MAAM+D,GAAG,CAACzC,OAAJ,EAAzB;AACA,SAAK5B,eAAL,GAAuBC,MAAvB;AACA,SAAKiE,YAAL,GAAoBG,GAApB;AACA,SAAKN,QAAL,CAAcO,IAAd,CAAmBD,GAAnB;AACA,WAAOA,GAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIlE,EAAAA,oBAAoB,CAACF,MAAD,EAAS;AACzBA,IAAAA,MAAM,CAACrB,eAAP,CAAuB,IAAvB;;AACA,UAAMyD,OAAO,GAAG,KAAKwB,iBAAL,CAAuBvB,kBAAvB,CAA0CrC,MAAM,CAACX,WAAjD,EAA8DW,MAAM,CAACZ,OAArE,CAAhB;;AACA,UAAMiB,YAAN,CAAmB,MAAM,KAAKuD,iBAAL,CAAuBU,KAAvB,EAAzB;AACA,SAAKvE,eAAL,GAAuBC,MAAvB;AACA,SAAKiE,YAAL,GAAoB7B,OAApB;AACA,SAAK0B,QAAL,CAAcO,IAAd,CAAmBjC,OAAnB;AACA,WAAOA,OAAP;AACH;AACD;;;AACA2B,EAAAA,YAAY,GAAG;AACX,UAAMpE,aAAa,GAAG,KAAKiE,iBAAL,CAAuBlE,OAAvB,CAA+BC,aAArD,CADW,CAEX;AACA;;AACA,WAAQA,aAAa,CAAC4E,QAAd,KAA2B5E,aAAa,CAAC6E,YAAzC,GACF7E,aADE,GAEFA,aAAa,CAACoB,UAFpB;AAGH;;AA7H0C;;AA+H/C4C,eAAe,CAACZ,IAAhB;AAAA,mBAA4GY,eAA5G,EAzK4FxG,EAyK5F,mBAA6IA,EAAE,CAACsH,wBAAhJ,GAzK4FtH,EAyK5F,mBAAqLA,EAAE,CAAC8F,gBAAxL,GAzK4F9F,EAyK5F,mBAAqNQ,QAArN;AAAA;;AACAgG,eAAe,CAACT,IAAhB,kBA1K4F/F,EA0K5F;AAAA,QAAgGwG,eAAhG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA1K4FxG,EA0K5F;AAAA;;AACA;AAAA,qDA3K4FA,EA2K5F,mBAA2FwG,eAA3F,EAAwH,CAAC;AAC7GR,IAAAA,IAAI,EAAE7F,SADuG;AAE7G8F,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,mBADX;AAECC,MAAAA,QAAQ,EAAE,iBAFX;AAGCoB,MAAAA,MAAM,EAAE,CAAC,yBAAD;AAHT,KAAD;AAFuG,GAAD,CAAxH,EAO4B,YAAY;AAAE,WAAO,CAAC;AAAEvB,MAAAA,IAAI,EAAEhG,EAAE,CAACsH;AAAX,KAAD,EAAwC;AAAEtB,MAAAA,IAAI,EAAEhG,EAAE,CAAC8F;AAAX,KAAxC,EAAuE;AAAEE,MAAAA,IAAI,EAAE3D,SAAR;AAAmBmF,MAAAA,UAAU,EAAE,CAAC;AACpIxB,QAAAA,IAAI,EAAE3F,MAD8H;AAEpI4F,QAAAA,IAAI,EAAE,CAACzF,QAAD;AAF8H,OAAD;AAA/B,KAAvE,CAAP;AAGlB,GAVxB,EAU0C;AAAEmG,IAAAA,QAAQ,EAAE,CAAC;AACvCX,MAAAA,IAAI,EAAE1F;AADiC,KAAD;AAAZ,GAV1C;AAAA;AAaA;AACA;AACA;AACA;;;AACA,MAAMmH,mBAAN,SAAkCjB,eAAlC,CAAkD;;AAElDiB,mBAAmB,CAAC7B,IAApB;AAAA;AAAA;AAAA,oFA9L4F5F,EA8L5F,uBAAgHyH,mBAAhH,SAAgHA,mBAAhH;AAAA;AAAA;;AACAA,mBAAmB,CAAC1B,IAApB,kBA/L4F/F,EA+L5F;AAAA,QAAoGyH,mBAApG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA/L4FzH,EA+L5F,oBAAgO,CACxN;AACIqG,IAAAA,OAAO,EAAEG,eADb;AAEIF,IAAAA,WAAW,EAAEmB;AAFjB,GADwN,CAAhO,GA/L4FzH,EA+L5F;AAAA;;AAMA;AAAA,qDArM4FA,EAqM5F,mBAA2FyH,mBAA3F,EAA4H,CAAC;AACjHzB,IAAAA,IAAI,EAAE7F,SAD2G;AAEjH8F,IAAAA,IAAI,EAAE,CAAC;AACCC,MAAAA,QAAQ,EAAE,+BADX;AAECC,MAAAA,QAAQ,EAAE,eAFX;AAGCoB,MAAAA,MAAM,EAAE,CAAC,uBAAD,CAHT;AAIChB,MAAAA,SAAS,EAAE,CACP;AACIF,QAAAA,OAAO,EAAEG,eADb;AAEIF,QAAAA,WAAW,EAAEmB;AAFjB,OADO;AAJZ,KAAD;AAF2G,GAAD,CAA5H;AAAA;;AAcA,MAAMC,YAAN,CAAmB;;AAEnBA,YAAY,CAAC9B,IAAb;AAAA,mBAAyG8B,YAAzG;AAAA;;AACAA,YAAY,CAACC,IAAb,kBAtN4F3H,EAsN5F;AAAA,QAA0G0H;AAA1G;AACAA,YAAY,CAACE,IAAb,kBAvN4F5H,EAuN5F;;AACA;AAAA,qDAxN4FA,EAwN5F,mBAA2F0H,YAA3F,EAAqH,CAAC;AAC1G1B,IAAAA,IAAI,EAAEzF,QADoG;AAE1G0F,IAAAA,IAAI,EAAE,CAAC;AACC4B,MAAAA,OAAO,EAAE,CAAClC,SAAD,EAAYa,eAAZ,EAA6BJ,uBAA7B,EAAsDqB,mBAAtD,CADV;AAECK,MAAAA,YAAY,EAAE,CAACnC,SAAD,EAAYa,eAAZ,EAA6BJ,uBAA7B,EAAsDqB,mBAAtD;AAFf,KAAD;AAFoG,GAAD,CAArH;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMM,cAAN,CAAqB;AACjBrG,EAAAA,WAAW,CAACsG,eAAD,EAAkBC,aAAlB,EAAiC;AACxC,SAAKD,eAAL,GAAuBA,eAAvB;AACA,SAAKC,aAAL,GAAqBA,aAArB;AACH;;AACDC,EAAAA,GAAG,CAACC,KAAD,EAAQC,aAAR,EAAuB;AACtB,UAAMC,KAAK,GAAG,KAAKJ,aAAL,CAAmBC,GAAnB,CAAuBC,KAAvB,CAAd;;AACA,QAAI,OAAOE,KAAP,KAAiB,WAArB,EAAkC;AAC9B,aAAOA,KAAP;AACH;;AACD,WAAO,KAAKL,eAAL,CAAqBE,GAArB,CAAyBC,KAAzB,EAAgCC,aAAhC,CAAP;AACH;;AAXgB;AAcrB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS/E,cAAT,EAAyBZ,gBAAzB,EAA2CkD,SAA3C,EAAsDa,eAAtD,EAAuE/E,eAAvE,EAAwFa,SAAxF,EAAmGoD,aAAnG,EAAkHpC,eAAlH,EAAmItC,MAAnI,EAA2IyG,mBAA3I,EAAgKM,cAAhK,EAAgLL,YAAhL,EAA8L3F,cAA9L,EAA8MqE,uBAA9M", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { ElementRef, Injector, Directive, EventEmitter, Inject, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Throws an exception when attempting to attach a null portal to a host.\n * @docs-private\n */\nfunction throwNullPortalError() {\n    throw Error('Must provide a portal to attach');\n}\n/**\n * Throws an exception when attempting to attach a portal to a host that is already attached.\n * @docs-private\n */\nfunction throwPortalAlreadyAttachedError() {\n    throw Error('Host already has a portal attached');\n}\n/**\n * Throws an exception when attempting to attach a portal to an already-disposed host.\n * @docs-private\n */\nfunction throwPortalOutletAlreadyDisposedError() {\n    throw Error('This PortalOutlet has already been disposed');\n}\n/**\n * Throws an exception when attempting to attach an unknown portal type.\n * @docs-private\n */\nfunction throwUnknownPortalTypeError() {\n    throw Error('Attempting to attach an unknown Portal type. BasePortalOutlet accepts either ' +\n        'a ComponentPortal or a TemplatePortal.');\n}\n/**\n * Throws an exception when attempting to attach a portal to a null host.\n * @docs-private\n */\nfunction throwNullPortalOutletError() {\n    throw Error('Attempting to attach a portal to a null PortalOutlet');\n}\n/**\n * Throws an exception when attempting to detach a portal that is not attached.\n * @docs-private\n */\nfunction throwNoPortalAttachedError() {\n    throw Error('Attempting to detach a portal that is not attached to a host');\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A `Portal` is something that you want to render somewhere else.\n * It can be attach to / detached from a `PortalOutlet`.\n */\nclass Portal {\n    /** Attach this portal to a host. */\n    attach(host) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (host == null) {\n                throwNullPortalOutletError();\n            }\n            if (host.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n        }\n        this._attachedHost = host;\n        return host.attach(this);\n    }\n    /** Detach this portal from its host */\n    detach() {\n        let host = this._attachedHost;\n        if (host != null) {\n            this._attachedHost = null;\n            host.detach();\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwNoPortalAttachedError();\n        }\n    }\n    /** Whether this portal is attached to a host. */\n    get isAttached() {\n        return this._attachedHost != null;\n    }\n    /**\n     * Sets the PortalOutlet reference without performing `attach()`. This is used directly by\n     * the PortalOutlet when it is performing an `attach()` or `detach()`.\n     */\n    setAttachedHost(host) {\n        this._attachedHost = host;\n    }\n}\n/**\n * A `ComponentPortal` is a portal that instantiates some Component upon attachment.\n */\nclass ComponentPortal extends Portal {\n    constructor(component, viewContainerRef, injector, componentFactoryResolver) {\n        super();\n        this.component = component;\n        this.viewContainerRef = viewContainerRef;\n        this.injector = injector;\n        this.componentFactoryResolver = componentFactoryResolver;\n    }\n}\n/**\n * A `TemplatePortal` is a portal that represents some embedded template (TemplateRef).\n */\nclass TemplatePortal extends Portal {\n    constructor(template, viewContainerRef, context) {\n        super();\n        this.templateRef = template;\n        this.viewContainerRef = viewContainerRef;\n        this.context = context;\n    }\n    get origin() {\n        return this.templateRef.elementRef;\n    }\n    /**\n     * Attach the portal to the provided `PortalOutlet`.\n     * When a context is provided it will override the `context` property of the `TemplatePortal`\n     * instance.\n     */\n    attach(host, context = this.context) {\n        this.context = context;\n        return super.attach(host);\n    }\n    detach() {\n        this.context = undefined;\n        return super.detach();\n    }\n}\n/**\n * A `DomPortal` is a portal whose DOM element will be taken from its current position\n * in the DOM and moved into a portal outlet, when it is attached. On detach, the content\n * will be restored to its original position.\n */\nclass DomPortal extends Portal {\n    constructor(element) {\n        super();\n        this.element = element instanceof ElementRef ? element.nativeElement : element;\n    }\n}\n/**\n * Partial implementation of PortalOutlet that handles attaching\n * ComponentPortal and TemplatePortal.\n */\nclass BasePortalOutlet {\n    constructor() {\n        /** Whether this host has already been permanently disposed. */\n        this._isDisposed = false;\n        // @breaking-change 10.0.0 `attachDomPortal` to become a required abstract method.\n        this.attachDomPortal = null;\n    }\n    /** Whether this host has an attached portal. */\n    hasAttached() {\n        return !!this._attachedPortal;\n    }\n    /** Attaches a portal. */\n    attach(portal) {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!portal) {\n                throwNullPortalError();\n            }\n            if (this.hasAttached()) {\n                throwPortalAlreadyAttachedError();\n            }\n            if (this._isDisposed) {\n                throwPortalOutletAlreadyDisposedError();\n            }\n        }\n        if (portal instanceof ComponentPortal) {\n            this._attachedPortal = portal;\n            return this.attachComponentPortal(portal);\n        }\n        else if (portal instanceof TemplatePortal) {\n            this._attachedPortal = portal;\n            return this.attachTemplatePortal(portal);\n            // @breaking-change 10.0.0 remove null check for `this.attachDomPortal`.\n        }\n        else if (this.attachDomPortal && portal instanceof DomPortal) {\n            this._attachedPortal = portal;\n            return this.attachDomPortal(portal);\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throwUnknownPortalTypeError();\n        }\n    }\n    /** Detaches a previously attached portal. */\n    detach() {\n        if (this._attachedPortal) {\n            this._attachedPortal.setAttachedHost(null);\n            this._attachedPortal = null;\n        }\n        this._invokeDisposeFn();\n    }\n    /** Permanently dispose of this portal host. */\n    dispose() {\n        if (this.hasAttached()) {\n            this.detach();\n        }\n        this._invokeDisposeFn();\n        this._isDisposed = true;\n    }\n    /** @docs-private */\n    setDisposeFn(fn) {\n        this._disposeFn = fn;\n    }\n    _invokeDisposeFn() {\n        if (this._disposeFn) {\n            this._disposeFn();\n            this._disposeFn = null;\n        }\n    }\n}\n/**\n * @deprecated Use `BasePortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass BasePortalHost extends BasePortalOutlet {\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A PortalOutlet for attaching portals to an arbitrary DOM element outside of the Angular\n * application context.\n */\nclass DomPortalOutlet extends BasePortalOutlet {\n    /**\n     * @param outletElement Element into which the content is projected.\n     * @param _componentFactoryResolver Used to resolve the component factory.\n     *   Only required when attaching component portals.\n     * @param _appRef Reference to the application. Only used in component portals when there\n     *   is no `ViewContainerRef` available.\n     * @param _defaultInjector Injector to use as a fallback when the portal being attached doesn't\n     *   have one. Only used for component portals.\n     * @param _document Reference to the document. Used when attaching a DOM portal. Will eventually\n     *   become a required parameter.\n     */\n    constructor(\n    /** Element into which the content is projected. */\n    outletElement, _componentFactoryResolver, _appRef, _defaultInjector, \n    /**\n     * @deprecated `_document` Parameter to be made required.\n     * @breaking-change 10.0.0\n     */\n    _document) {\n        super();\n        this.outletElement = outletElement;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._appRef = _appRef;\n        this._defaultInjector = _defaultInjector;\n        /**\n         * Attaches a DOM portal by transferring its content into the outlet.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            // @breaking-change 10.0.0 Remove check and error once the\n            // `_document` constructor parameter is required.\n            if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Cannot attach DOM portal without _document constructor parameter');\n            }\n            const element = portal.element;\n            if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('DOM portal content must be attached to a parent node.');\n            }\n            // Anchor used to save the element's previous position so\n            // that we can restore it when the portal is detached.\n            const anchorNode = this._document.createComment('dom-portal');\n            element.parentNode.insertBefore(anchorNode, element);\n            this.outletElement.appendChild(element);\n            this._attachedPortal = portal;\n            super.setDisposeFn(() => {\n                // We can't use `replaceWith` here because IE doesn't support it.\n                if (anchorNode.parentNode) {\n                    anchorNode.parentNode.replaceChild(element, anchorNode);\n                }\n            });\n        };\n        this._document = _document;\n    }\n    /**\n     * Attach the given ComponentPortal to DOM element using the ComponentFactoryResolver.\n     * @param portal Portal to be attached\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        const resolver = (portal.componentFactoryResolver || this._componentFactoryResolver);\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && !resolver) {\n            throw Error('Cannot attach component portal to outlet without a ComponentFactoryResolver.');\n        }\n        const componentFactory = resolver.resolveComponentFactory(portal.component);\n        let componentRef;\n        // If the portal specifies a ViewContainerRef, we will use that as the attachment point\n        // for the component (in terms of Angular's component tree, not rendering).\n        // When the ViewContainerRef is missing, we use the factory to create the component directly\n        // and then manually attach the view to the application.\n        if (portal.viewContainerRef) {\n            componentRef = portal.viewContainerRef.createComponent(componentFactory, portal.viewContainerRef.length, portal.injector || portal.viewContainerRef.injector);\n            this.setDisposeFn(() => componentRef.destroy());\n        }\n        else {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._appRef) {\n                throw Error('Cannot attach component portal to outlet without an ApplicationRef.');\n            }\n            componentRef = componentFactory.create(portal.injector || this._defaultInjector || Injector.NULL);\n            this._appRef.attachView(componentRef.hostView);\n            this.setDisposeFn(() => {\n                // Verify that the ApplicationRef has registered views before trying to detach a host view.\n                // This check also protects the `detachView` from being called on a destroyed ApplicationRef.\n                if (this._appRef.viewCount > 0) {\n                    this._appRef.detachView(componentRef.hostView);\n                }\n                componentRef.destroy();\n            });\n        }\n        // At this point the component has been instantiated, so we move it to the location in the DOM\n        // where we want it to be rendered.\n        this.outletElement.appendChild(this._getComponentRootNode(componentRef));\n        this._attachedPortal = portal;\n        return componentRef;\n    }\n    /**\n     * Attaches a template portal to the DOM as an embedded view.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        let viewContainer = portal.viewContainerRef;\n        let viewRef = viewContainer.createEmbeddedView(portal.templateRef, portal.context);\n        // The method `createEmbeddedView` will add the view as a child of the viewContainer.\n        // But for the DomPortalOutlet the view can be added everywhere in the DOM\n        // (e.g Overlay Container) To move the view to the specified host element. We just\n        // re-append the existing root nodes.\n        viewRef.rootNodes.forEach(rootNode => this.outletElement.appendChild(rootNode));\n        // Note that we want to detect changes after the nodes have been moved so that\n        // any directives inside the portal that are looking at the DOM inside a lifecycle\n        // hook won't be invoked too early.\n        viewRef.detectChanges();\n        this.setDisposeFn(() => {\n            let index = viewContainer.indexOf(viewRef);\n            if (index !== -1) {\n                viewContainer.remove(index);\n            }\n        });\n        this._attachedPortal = portal;\n        // TODO(jelbourn): Return locals from view.\n        return viewRef;\n    }\n    /**\n     * Clears out a portal from the DOM.\n     */\n    dispose() {\n        super.dispose();\n        this.outletElement.remove();\n    }\n    /** Gets the root HTMLElement for an instantiated component. */\n    _getComponentRootNode(componentRef) {\n        return componentRef.hostView.rootNodes[0];\n    }\n}\n/**\n * @deprecated Use `DomPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass DomPortalHost extends DomPortalOutlet {\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Directive version of a `TemplatePortal`. Because the directive *is* a TemplatePortal,\n * the directive instance itself can be attached to a host, enabling declarative use of portals.\n */\nclass CdkPortal extends TemplatePortal {\n    constructor(templateRef, viewContainerRef) {\n        super(templateRef, viewContainerRef);\n    }\n}\nCdkPortal.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkPortal, deps: [{ token: i0.TemplateRef }, { token: i0.ViewContainerRef }], target: i0.ɵɵFactoryTarget.Directive });\nCdkPortal.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkPortal, selector: \"[cdkPortal]\", exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkPortal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortal]',\n                    exportAs: 'cdkPortal',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }, { type: i0.ViewContainerRef }]; } });\n/**\n * @deprecated Use `CdkPortal` instead.\n * @breaking-change 9.0.0\n */\nclass TemplatePortalDirective extends CdkPortal {\n}\nTemplatePortalDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: TemplatePortalDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nTemplatePortalDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: TemplatePortalDirective, selector: \"[cdk-portal], [portal]\", providers: [\n        {\n            provide: CdkPortal,\n            useExisting: TemplatePortalDirective,\n        },\n    ], exportAs: [\"cdkPortal\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: TemplatePortalDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-portal], [portal]',\n                    exportAs: 'cdkPortal',\n                    providers: [\n                        {\n                            provide: CdkPortal,\n                            useExisting: TemplatePortalDirective,\n                        },\n                    ],\n                }]\n        }] });\n/**\n * Directive version of a PortalOutlet. Because the directive *is* a PortalOutlet, portals can be\n * directly attached to it, enabling declarative use.\n *\n * Usage:\n * `<ng-template [cdkPortalOutlet]=\"greeting\"></ng-template>`\n */\nclass CdkPortalOutlet extends BasePortalOutlet {\n    constructor(_componentFactoryResolver, _viewContainerRef, \n    /**\n     * @deprecated `_document` parameter to be made required.\n     * @breaking-change 9.0.0\n     */\n    _document) {\n        super();\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._viewContainerRef = _viewContainerRef;\n        /** Whether the portal component is initialized. */\n        this._isInitialized = false;\n        /** Emits when a portal is attached to the outlet. */\n        this.attached = new EventEmitter();\n        /**\n         * Attaches the given DomPortal to this PortalHost by moving all of the portal content into it.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            // @breaking-change 9.0.0 Remove check and error once the\n            // `_document` constructor parameter is required.\n            if (!this._document && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('Cannot attach DOM portal without _document constructor parameter');\n            }\n            const element = portal.element;\n            if (!element.parentNode && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('DOM portal content must be attached to a parent node.');\n            }\n            // Anchor used to save the element's previous position so\n            // that we can restore it when the portal is detached.\n            const anchorNode = this._document.createComment('dom-portal');\n            portal.setAttachedHost(this);\n            element.parentNode.insertBefore(anchorNode, element);\n            this._getRootNode().appendChild(element);\n            this._attachedPortal = portal;\n            super.setDisposeFn(() => {\n                if (anchorNode.parentNode) {\n                    anchorNode.parentNode.replaceChild(element, anchorNode);\n                }\n            });\n        };\n        this._document = _document;\n    }\n    /** Portal associated with the Portal outlet. */\n    get portal() {\n        return this._attachedPortal;\n    }\n    set portal(portal) {\n        // Ignore the cases where the `portal` is set to a falsy value before the lifecycle hooks have\n        // run. This handles the cases where the user might do something like `<div cdkPortalOutlet>`\n        // and attach a portal programmatically in the parent component. When Angular does the first CD\n        // round, it will fire the setter with empty string, causing the user's content to be cleared.\n        if (this.hasAttached() && !portal && !this._isInitialized) {\n            return;\n        }\n        if (this.hasAttached()) {\n            super.detach();\n        }\n        if (portal) {\n            super.attach(portal);\n        }\n        this._attachedPortal = portal || null;\n    }\n    /** Component or view reference that is attached to the portal. */\n    get attachedRef() {\n        return this._attachedRef;\n    }\n    ngOnInit() {\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        super.dispose();\n        this._attachedPortal = null;\n        this._attachedRef = null;\n    }\n    /**\n     * Attach the given ComponentPortal to this PortalOutlet using the ComponentFactoryResolver.\n     *\n     * @param portal Portal to be attached to the portal outlet.\n     * @returns Reference to the created component.\n     */\n    attachComponentPortal(portal) {\n        portal.setAttachedHost(this);\n        // If the portal specifies an origin, use that as the logical location of the component\n        // in the application tree. Otherwise use the location of this PortalOutlet.\n        const viewContainerRef = portal.viewContainerRef != null ? portal.viewContainerRef : this._viewContainerRef;\n        const resolver = portal.componentFactoryResolver || this._componentFactoryResolver;\n        const componentFactory = resolver.resolveComponentFactory(portal.component);\n        const ref = viewContainerRef.createComponent(componentFactory, viewContainerRef.length, portal.injector || viewContainerRef.injector);\n        // If we're using a view container that's different from the injected one (e.g. when the portal\n        // specifies its own) we need to move the component into the outlet, otherwise it'll be rendered\n        // inside of the alternate view container.\n        if (viewContainerRef !== this._viewContainerRef) {\n            this._getRootNode().appendChild(ref.hostView.rootNodes[0]);\n        }\n        super.setDisposeFn(() => ref.destroy());\n        this._attachedPortal = portal;\n        this._attachedRef = ref;\n        this.attached.emit(ref);\n        return ref;\n    }\n    /**\n     * Attach the given TemplatePortal to this PortalHost as an embedded View.\n     * @param portal Portal to be attached.\n     * @returns Reference to the created embedded view.\n     */\n    attachTemplatePortal(portal) {\n        portal.setAttachedHost(this);\n        const viewRef = this._viewContainerRef.createEmbeddedView(portal.templateRef, portal.context);\n        super.setDisposeFn(() => this._viewContainerRef.clear());\n        this._attachedPortal = portal;\n        this._attachedRef = viewRef;\n        this.attached.emit(viewRef);\n        return viewRef;\n    }\n    /** Gets the root node of the portal outlet. */\n    _getRootNode() {\n        const nativeElement = this._viewContainerRef.element.nativeElement;\n        // The directive could be set on a template which will result in a comment\n        // node being the root. Use the comment's parent node if that is the case.\n        return (nativeElement.nodeType === nativeElement.ELEMENT_NODE\n            ? nativeElement\n            : nativeElement.parentNode);\n    }\n}\nCdkPortalOutlet.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkPortalOutlet, deps: [{ token: i0.ComponentFactoryResolver }, { token: i0.ViewContainerRef }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });\nCdkPortalOutlet.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: { portal: [\"cdkPortalOutlet\", \"portal\"] }, outputs: { attached: \"attached\" }, exportAs: [\"cdkPortalOutlet\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkPortalOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalOutlet]',\n                    exportAs: 'cdkPortalOutlet',\n                    inputs: ['portal: cdkPortalOutlet'],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ComponentFactoryResolver }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; }, propDecorators: { attached: [{\n                type: Output\n            }] } });\n/**\n * @deprecated Use `CdkPortalOutlet` instead.\n * @breaking-change 9.0.0\n */\nclass PortalHostDirective extends CdkPortalOutlet {\n}\nPortalHostDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: PortalHostDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });\nPortalHostDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: PortalHostDirective, selector: \"[cdkPortalHost], [portalHost]\", inputs: { portal: [\"cdkPortalHost\", \"portal\"] }, providers: [\n        {\n            provide: CdkPortalOutlet,\n            useExisting: PortalHostDirective,\n        },\n    ], exportAs: [\"cdkPortalHost\"], usesInheritance: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: PortalHostDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkPortalHost], [portalHost]',\n                    exportAs: 'cdkPortalHost',\n                    inputs: ['portal: cdkPortalHost'],\n                    providers: [\n                        {\n                            provide: CdkPortalOutlet,\n                            useExisting: PortalHostDirective,\n                        },\n                    ],\n                }]\n        }] });\nclass PortalModule {\n}\nPortalModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: PortalModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nPortalModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: PortalModule, declarations: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective], exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective] });\nPortalModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: PortalModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: PortalModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                    declarations: [CdkPortal, CdkPortalOutlet, TemplatePortalDirective, PortalHostDirective],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Custom injector to be used when providing custom\n * injection tokens to components inside a portal.\n * @docs-private\n * @deprecated Use `Injector.create` instead.\n * @breaking-change 11.0.0\n */\nclass PortalInjector {\n    constructor(_parentInjector, _customTokens) {\n        this._parentInjector = _parentInjector;\n        this._customTokens = _customTokens;\n    }\n    get(token, notFoundValue) {\n        const value = this._customTokens.get(token);\n        if (typeof value !== 'undefined') {\n            return value;\n        }\n        return this._parentInjector.get(token, notFoundValue);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePortalHost, BasePortalOutlet, CdkPortal, CdkPortalOutlet, ComponentPortal, DomPortal, DomPortalHost, DomPortalOutlet, Portal, PortalHostDirective, PortalInjector, PortalModule, TemplatePortal, TemplatePortalDirective };\n"]}, "metadata": {}, "sourceType": "module"}
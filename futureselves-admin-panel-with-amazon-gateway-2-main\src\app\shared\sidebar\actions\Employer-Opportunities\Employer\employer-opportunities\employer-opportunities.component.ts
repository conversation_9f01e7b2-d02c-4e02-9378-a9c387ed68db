import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router, Params, NavigationEnd, RouterEvent} from '@angular/router';
import { filter } from 'rxjs/operators';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import {
  FormGroup,
  FormBuilder,
  Validators, 
  FormArray,
  FormControl,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { HttpClient } from '@angular/common/http';
@Component({
  selector: 'app-employer-opportunities',
  templateUrl: './employer-opportunities.component.html',
  styleUrls: ['./employer-opportunities.component.scss'],
})
export class EmployerOpportunitiesComponent implements OnInit {
  p: number = 1;
  CompanyList: any;
  hideHeader = false;
  showForm = false;
  queryParam: any;
  term: string;
  submitted = false;
  title = 'AddNew';
  isReadonly = false;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  title1: string;
  action: string;
  

  constructor(
    private dataTransferService: DataTransferService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
  ) {

  }

  ngOnInit(): void {
    this.getAllCompany();
  }

  onReset() {
    this.submitted = false;
  }

  getAllCompany() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllCompany().subscribe({
      next: (res: any) => {
        if (res.statusCode === 200) {
          this.CompanyList = res.data;
          this.filteredCompanyList = this.CompanyList;
          this.ngxSpinnerService.hide('globalSpinner');
          console.log('GetAllCompany', this.CompanyList);
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Failed to fetch companies. Status:', res.status);
        }
      },
      error: (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error occurred while fetching roles:', error);
      },
    });
  }
  
  filteredCompanyList:any=[];
  filterCompanies() {
    if (!this.term.trim()) { 
      this.filteredCompanyList = this.CompanyList; 
    } else {
      // Filter the CompanyList based on the search term
      this.filteredCompanyList = this.CompanyList.filter((company: any) =>
        company.CO_companyName.toLowerCase().includes(this.term.toLowerCase())
      );
    }
      return this.filteredCompanyList;
  }


  ShowNewCompanyForm() {  //ADD Company Button Click
    const state={
      title : 'Add',
    }
    this.router.navigate([`/actions/employer-opportunities/add-edit-employer`],{state});
  }

  openExtSchemeform(employer:any){
    this.router.navigate([`/actions/employer-opportunities/existing-opportunities`],{queryParams: { CO_id: employer.CO_id }});
  }

}

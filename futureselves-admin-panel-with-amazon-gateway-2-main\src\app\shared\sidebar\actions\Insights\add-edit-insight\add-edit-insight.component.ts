import { Component, OnInit, Renderer2, ChangeDetectorRef } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  Validators,
} from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, ActivatedRoute } from '@angular/router';
import { Constants } from 'src/app/config/constants';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
import html2canvas from 'html2canvas';

@Component({
  selector: 'app-add-edit-insight',
  templateUrl: './add-edit-insight.component.html',
  styleUrls: ['./add-edit-insight.component.scss'],
})
export class AddEditInsightComponent implements OnInit {
  showhide: boolean = false;
  soundList: any[] = [];
  tempSoundList: any[] = [];
  p: number = 1;
  term: string;
  soundForm: FormGroup;
  isReadonly = true;
  // DegreeList: any;
  industryList: any;
  TagList: any;
  selectsoundurl: any;
  imagedp: any;
  ID: any;
  objname: any;
  fileName: any;
  selectedRecord: any;
  Tagtitle: any = '';
  imageName: any;
  AN_status1: any;
  filtervalue: any;
  reason: any;
  selectedvalue: any;
  baseurl: any = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  userData: any;
  userId: any;
  state: any = [];
  AN_Id: void;
  transcriptionGenerated: boolean = false;
  newTranscription: any;
  showButton: boolean = true;
  showRegenerateButton: boolean = false;
  wordCount = 0;
  wordLimit = 50;
  userName: any;
  industryName: any;
  roleName: any;
  certificateTitle: any;
  certificateDescription: any;
  badgeImage: any;
  dateEarned: any;
  dataLoaded: boolean = false;
  BA_percentage: any;
  private badgeData: any;
  private badges: any[] = [];
  private currentBadgeIndex: number = 0;
  anonymousLink: any;
  isanonimise: any;
  ispublic: any;
  AN_id: any;
  wantToAnonymous: boolean;
  originalRecordLink: any;
  showApproveBtn: boolean = true;
  anonymousGenerated: boolean;
  fileArray: any[] = [];
  BA_id: any;

  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private domSanitizer: DomSanitizer,
    private router: Router,
    private formModule: FormsModule,
    private http: HttpClient,
    private ngxSpinnerService: NgxSpinnerService,
    private cdr: ChangeDetectorRef,
    private renderer: Renderer2
  ) {
    const state = this.router.getCurrentNavigation()?.extras?.state;
    if (state) {
      this.state = state;
      this.userId = state.AN_userId;
      this.AN_id=state.selectedRecord.AN_id;
    } else {
      this.router.navigate([`/actions/insights`]);
    }
  }

  ngOnInit(): void {
    // this.getAllSoundBite();
    // this.getalltags()
    this.initForm();
    this.getindustry();
    this.soundForm.get('AN_industryId')?.disable();
    // this.getalldegree();
    this.patchAllData();
    // this.getBadgeData();
  }

  initForm() {
    this.soundForm = this.formBuilder.group({
      AN_title: ['', [Validators.required]],
      AN_date: [''],
      AN_status: [''],
      AN_recordLink: [''],
      U_name: [''],
      U_updatedAt: [''],
      AN_updatedAt: [''],
      AN_createdBy: [''],
      AN_degreeId: [''],
      AN_description: [''],
      AN_dp: [''],
      AN_id: [''],
      RO_title: [''],
      AN_industryId: [''],
      AN_isPublished: [''],
      AN_keyIdeas: [''],
      AN_questionId: [''],
      AN_scheduleTime: [''],
      AN_updatedBy: [''],
      DE_title: [''],
      AN_url2: [''],
      AN_Reject_Reason: [''],
      AN_transcription: [''],
      AN_userId: [''],
      //  ANT_tagtitle: [''],
      sound_bites_links: [
        {
          AL_answersId: [''],
          AL_title: [''],
          AL_url: [''],
        },
      ],
      sound_bites_tags: [
        {
          ANT_id: [''],
          ANT_tagId: [''],
          ANT_tagtitle: [''],
        },
      ],
    });
  }

  get f() {
    return this.soundForm.controls;
  }

 async patchAllData() {
  try{
    await this.getSoundbiteByid(this.AN_id);
    this.soundForm.value.U_name = this.selectedRecord.U_name;
    this.soundForm.value.AN_title = this.selectedRecord.AN_title;
    this.soundForm.value.AN_industryId =this.selectedRecord.AN_industryId;
    this.isanonimise = this.selectedRecord.AN_isanonimise == true ? 'Yes' : 'No';
    this.ispublic = this.selectedRecord.AN_ispublic == true ? 'Yes' : 'No';
    this.selectsoundurl = this.selectedRecord.AN_recordLink;
    // this.cdr.detectChanges();
    if (this.selectedRecord.AN_originalRecordLink) {
      this.anonymousGenerated = true;
    }

    //if status is pending and transcription is generated
    if (this.selectedRecord.AN_originalRecordLink &&this.selectedRecord.AN_isPublished === '1' &&this.selectedRecord.AN_transcription) {
      this.originalRecordLink = this.selectedRecord.AN_originalRecordLink;
      this.anonymousLink = this.selectedRecord.AN_recordLink;
      this.soundForm.value.AN_transcription =
      this.selectedRecord.AN_transcription;
      this.showRegenerateButton = true;
      this.transcriptionGenerated = true;
    }

    if (
      !this.selectedRecord.AN_originalRecordLink &&
      this.selectedRecord.AN_isPublished === '1' &&
      this.selectedRecord.AN_transcription
    ) {
      this.showModal();
      this.soundForm.value.AN_transcription =
        this.selectedRecord.AN_transcription;
      this.transcriptionGenerated = true;
      this.showRegenerateButton = true;
    }

    //if status is approved
    if (this.selectedRecord.AN_status == '2') {
      this.soundForm.value.AN_transcription = this.selectedRecord.AN_transcription;
      this.showButton = false;
      this.showApproveBtn = false;
      this.transcriptionGenerated = true;
      this.originalRecordLink = this.selectedRecord.AN_originalRecordLink;
      this.anonymousLink = this.selectedRecord.AN_recordLink;
    }

    //if status is reject
    if (
      this.selectedRecord.AN_status == '3' &&
      this.selectedRecord.AN_transcription
    ) {
      this.originalRecordLink = this.selectedRecord.AN_originalRecordLink;
      this.anonymousLink = this.selectedRecord.AN_recordLink;
      this.soundForm.value.AN_transcription =
        this.selectedRecord.AN_transcription;
      // this.showModal();
      this.showRegenerateButton = true;
      this.transcriptionGenerated = true;
    }
    this.soundForm.value.RO_title = this.selectedRecord.RO_title;

    if (this.selectedRecord && this.selectedRecord.tags) {
      this.Tagtitle = this.selectedRecord.tags
        .map((tag: any) => tag.A_title)
        .join(', ');
    }
    // const regex = /\[\d{2}:\d{2}:\d{2}\.\d{3}\]\s?/g;
    // this.soundForm.value.AN_transcription=this.selectedRecord.AN_transcription.replace(regex, '').replace(/\\n/g, '');
    this.soundForm.patchValue(this.selectedRecord);
  }catch{
    this.toastr.error('Error in patching data');
  }
  }

  showModal() {
    const modal = document.getElementById('Transcription-modal');
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal() {
    const modal = document.getElementById('Transcription-modal');
    if (modal != null) {
      modal.style.display = 'none';
    }
  }

  // abc:any;
  checkWordLimit(value: string) {
    const words = value.trim().split(/\s+/);
    this.wordCount = words.length;
    if (this.wordCount > this.wordLimit) {
      this.toastr.warning(
        `You have exceeded the word limit of ${this.wordLimit} words.`
      );
      return;
    }
    this.getval(value);
  }

  getval(item: any) {
    // console.log(item,"textbox value");
    this.selectedvalue = item;
    // console.log(this.selectedvalue, 'textbox value');
  }
  radiovalue(event: any,reasonType:string) {
    this.selectedvalue = event.target.value;
    console.log(this.selectedvalue, 'option value');

    if (reasonType == 'existingReason') {
      this.showhide = false;
      this.wordCount = 0;
    }

    // console.log(this.abc,"textbox value");
  }
  

  showbox() {
    // this.showhide=true;
    this.showhide = true;
  }

  selected(event: any) {
    this.filtervalue = event.target.value;
    console.log('filtervalue', this.filtervalue);
    this.soundList = [...this.tempSoundList];
    if (this.filtervalue != 'All')
      this.soundList = this.soundList.filter(
        (obj) => obj.AN_status == this.filtervalue
      );
  }
  // getvalue(value: any) {
  //   if(value){
  //   this.Tagtitle = value;
  // }else{
  //   this.Tagtitle = '';

  // }
  //   console.log(value, 'text tags');
  // }

  onFileSelected(event: any) {
    this.fileName = event.target.files[0];
    console.log('file data', this.fileName);
    this.selectsoundurl = this.fileName.value;
  }
  onimageSelected(event: any) {
    this.imageName = event.target.files[0];
    console.log('image data', this.imageName);
    this.imagedp = this.imageName.value;
  }

  getUserName(data: any) {
    this.dataTransferService.getUserData(data).subscribe((res: any) => {
      if (res.statusCode == 200) {
        this.userData = res.data.userDetails[0].U_name;
        console.log('user data', this.userData);
      }
    },(error:any)=>{
      console.log("error",error);
    });
  }



  getindustry() {
    this.dataTransferService.getIndustryData().subscribe((res: any) => {
      console.log(res, 'industry list');
      if (res.status == 200) {
        this.industryList = res.data;
        console.log(this.industryList, 'industrylist');
      }
    },(error:any)=>{
      console.log("error",error);
    });
  }
  getalltags() {
    this.dataTransferService.gettags().subscribe((res: any) => {
      console.log(res);
      if (res.status == 200) {
        this.TagList = res.data;
        console.log('Tags : ', this.TagList);
      }
    },(error:any)=>{
      console.log("error",error);
    });
  }

  // getalldegree() {
  //   this.dataTransferService.getdegree().subscribe((res: any) => {
  //     if (res.statusCode == 200) {
  //       this.DegreeList = res.data;
  //     }
  //   },(error:any)=>{
  //     console.log("error",error);
  //   });
  // }
 

  changeAction() {
    if (this.isReadonly == false) {
      const data = {
        AN_id: this.selectedRecord.AN_id,
        AN_transcription: this.soundForm.get('AN_transcription')?.value,
      };
      console.log('Data', data);
      this.ngxSpinnerService.show('globalSpinner');
      this.dataTransferService.updateTranscription(data).subscribe(
        (res: any) => {
          if (res.statusCode === 200) {
            this.ngxSpinnerService.hide('globalSpinner');
            this.toastr.success('Transcription updated successfully');
            const updatedTranscription = res.data.AN_transcription;
            console.log('updatedTranscription', updatedTranscription);
            this.soundForm.patchValue({
              AN_transcription: updatedTranscription,
            });
            this.isReadonly = !this.isReadonly;
          } else {
            this.ngxSpinnerService.hide('globalSpinner');
            console.log("Coundn't get 200 status code");
            this.toastr.error('Unable to update transcription');
          }
        },
        (error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');
          this.toastr.error('Unable to update transcription');
          console.error('error updating transcription', error);
        }
      );
    } else {
      this.isReadonly = !this.isReadonly;
    }
  }

  // hideAnonymousModal() {
  //   const modal = document.getElementById('anonymous-warning-modal');
  //   if (modal != null) {
  //     modal.style.display = 'none';
  //   }
  // }

  // showAnonymousModal() {
  //   const modal = document.getElementById('anonymous-warning-modal');
  //   if (modal != null) {
  //     modal.style.display = 'block';
  //   }
  // }

  // generateAnnonymousRecording() {
  //   this.hideAnonymousModal();
  //   const AN_id = this.selectedRecord.AN_id;
  //   this.ngxSpinnerService.show('annonymousSpinner');
  //   this.dataTransferService.generateAnnonymousInsight(AN_id).subscribe(
  //     (res: any) => {
  //       console.log('annanymous res', res);
  //         this.anonymousLink = res.AN_recordLink;
  //         this.cdr.detectChanges();
  //         console.log('this.originalRecordLink',this.originalRecordLink);
  //         this.originalRecordLink = res.AN_originalRecordLink;
  //         this.ngxSpinnerService.hide('annonymousSpinner');

  //     },
  //     (error: any) => {
  //       this.ngxSpinnerService.hide('annonymousSpinner');
  //       this.toastr.error('Error generating Anonymous Insight.');
  //     }
  //   );
  // }

  // GenerateTranscriptById(fromModal?: any) {
  //   this.AN_id = this.selectedRecord.AN_id;
  //   console.log('Generate an id: ', this.AN_id);
  //   if (fromModal) {
  //     this.hideModal();
  //     this.showRegenerateButton = true;
  //   }
  //   if (
  //     this.selectedRecord.AN_transcription == '' ||
  //     this.showRegenerateButton
  //   ) {
  //     this.ngxSpinnerService.show('generateTranscription');
  //     this.dataTransferService.GenerateTranscriptById(this.AN_id).subscribe(
  //       (res: any) => {
  //         if (res.externalApiResponse.status === 'success') {
  //           console.log('Generate button data', res);
  //           const newTranscription = res.record.AN_transcription;
  //           if (newTranscription !== '') {
  //             // this.soundForm.patchValue({
  //             //   AN_transcription: this.newTranscription,
  //             // });
  //             this.retryGetSoundBite(this.AN_id);
  //             this.transcriptionGenerated = true;
  //             this.showRegenerateButton = true;

  //             //   if (this.wantToAnonymous && this.showRegenerateButton !== true) {
  //             //     this.generateAnnonymousRecording(this.AN_id)
  //             // }
  //           } else {
  //             this.GenerateTranscriptById();
  //           }
  //         } else {
  //           this.handleApiError(res.message, this.AN_id);
  //         }
  //       },
  //       (error: any) => {
  //         // this.ngxSpinnerService.hide('generateTranscription');
  //         this.handleError(error, this.AN_id);
  //       }
  //     );
  //   } else {
  //     this.soundForm.patchValue({
  //       AN_transcription: this.selectedRecord.AN_transcription,
  //     });
  //     this.transcriptionGenerated = true;
  //     this.showRegenerateButton = true; // if(this.wantToAnonymous){
  //     //   this.generateAnnonymousRecording(this.AN_id)
  //     // }
  //   }
  // }

  // handleApiError(message: string, AN_id: any) {
  //   if (message === 'Endpoint request timed out') {
  //     this.retryGetSoundBite(AN_id);
  //   } else {
  //     this.ngxSpinnerService.hide('generateTranscription');
  //     this.toastr.error('Unable to generate transcription please try again');
  //     console.log('Error message: ', message);
  //   }
  // }

  // handleError(error: any, AN_id: any) {
  //   if (error.status === 504) {
  //     console.log('504 Gateway Timeout error');
  //     this.retryGetSoundBite(AN_id);
  //   } else {
  //     this.toastr.error('Something went wrong');
  //     console.log('Error', error);
  //     this.ngxSpinnerService.hide('generateTranscription');
  //   }
  // }

  // retryGetSoundBite(AN_id: any) {
  //   this.dataTransferService.getSoundBite().subscribe({
  //     next: (res: any) => {
  //       console.log(res);
  //       if (res.statusCode === 200) {
  //         let soundbite = res.data.find(
  //           (soundbite: any) => soundbite.AN_id === AN_id
  //         );
  //         if (soundbite) {
  //           console.log('Found soundbite: ', soundbite);
  //           if (soundbite.AN_transcription == '') {
  //             this.GenerateTranscriptById();
  //           } else {
  //             this.ngxSpinnerService.hide('generateTranscription');
  //             // this.soundForm.value.AN_transcription = soundbite.AN_transcription;
  //             this.soundForm.patchValue({
  //               AN_transcription: soundbite.AN_transcription,
  //             });
  //             this.transcriptionGenerated = true;
  //             this.showRegenerateButton = true;
  //             // if (this.wantToAnonymous && this.showRegenerateButton !== true) {
  //             //   this.generateAnnonymousRecording(this.AN_id)
  //             // }
  //           }
  //         } else {
  //           this.ngxSpinnerService.hide('generateTranscription');
  //           this.toastr.error('Failed to generate insight, Try again');
  //         }
  //       } else {
  //         this.ngxSpinnerService.hide('generateTranscription');
  //         this.toastr.error('Failed to generate insight, Try again');
  //       }
  //     },
  //     error: (error: any) => {
  //       console.log('Error Message', error);
  //       this.ngxSpinnerService.hide('generateTranscription');
  //       this.toastr.error(
  //         'Error retrieving insight',
  //         error.message || 'Unknown error occurred'
  //       );
  //     },
  //   });
  // }

  //     updateStatus(record: any, val: any) {
  //       if (this.wordCount > this.wordLimit) {
  //         console.error('Cannot update reason. Word limit exceeded.');
  //     }
  //     else{
  //     //Approve status
  //     console.log('approve reject data', record, val);
  //     // this.soundForm.patchValue(record);
  //     this.AN_status1 = val.toString();
  //     this.soundForm.value.AN_status = this.AN_status1;
  //     this.soundForm.value.AN_isPublished = this.AN_status1;
  //     this.ID = record.AN_id;
  //     this.soundForm.value.AN_id = this.ID;

  //     // console.log(this.soundForm.value.AN_status, this.soundForm.value.AN_isPublished, this.soundForm.value, "updated status");
  //     console.log('DATA-UPDATE_STATUS', this.soundForm.value);
  //     // if(this.transcriptionGenerated){
  //     this.dataTransferService.updateStatus(this.soundForm.value).subscribe(
  //       (res: any) => {
  //         if (res.statusCode == 200) {
  //           this.toastr.success('Status saved successfully', res.message);
  //           this.getAllSoundBite();
  //           this.router.navigate(['/actions/insights']);
  //         } else {
  //           this.toastr.error(
  //             'Error',
  //             res ? res.message : 'Unknown error occurred'
  //           );
  //         }
  //       },
  //       (error: any) => {
  //         this.toastr.error('Error', error.message || 'Unknown error occurred');
  //       }
  //     );
  //     // }else{
  //     //   this.toastr.error("Please generate transcription before approving.");
  //     // }
  //   }
  // }

  getAllSoundBite() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getSoundBite().subscribe({
      next: (res: any) => {
        console.log(res);
        if (res.statusCode == 200) {
          this.soundList = res.data;
          this.tempSoundList = [...this.soundList];
          console.log('Sound', this.soundList);
          this.ngxSpinnerService.hide('globalSpinner');
        }
      },
      error: (error: any) => {
        console.log('Error Message', error);
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  updateAllData(record: any) {
    this.soundForm.value.AN_id = record.AN_id;
    this.soundForm.value.sound_bites_tags[0].ANT_tagtitle = this.Tagtitle;
    console.log(this.soundForm.value, 'updated Status');
    let postData = { AN_id: record.AN_id, ANT_tagtitle: this.Tagtitle };
    this.dataTransferService.updateAllData(postData).subscribe((res: any) => {
      console.log('Url Record', res);
      if (!res.error) {
        this.toastr.success('Save data succesfully', res.message);
        this.getAllSoundBite();
        this.router.navigate([`/actions/insights`]);
      } else {
        this.toastr.error('', res.message);
      }
    },(error:any)=>{
      console.log("error",error);
    });
  }

  getuploadlink(record: any) {
    console.log('Record :', record);
    // console.log(this.soundForm.value.AN_recordLink, "response");

    // console.log(this.fileName, 'file');
    this.dataTransferService.uploadurl(this.fileName).subscribe((res: any) => {
      console.log('Url Record', res);
      if (!res.error) {
        this.toastr.success('', res.message);
        this.soundForm.patchValue(record);
        this.getAllSoundBite();
      } else {
        this.toastr.error('', res.message);
      }
    });
    this.soundForm.value.AN_id = record.AN_id;
    this.soundForm.value.AN_url2 = record.AN_recordLink;
    this.soundForm.value.AN_recordLink = this.baseurl + this.fileName.name;
    let postData = {
      AN_id: record.AN_id,
      AN_url2: record.AN_recordLink,
      AN_recordLink: this.baseurl + this.fileName.name,
    };
    this.dataTransferService.updateAll(postData).subscribe((res: any) => {
      console.log('update Record', res);
      if (!res.error) {
        this.toastr.success('Save data succesfully', res.message);
        // this.getAllSoundBite();
      } else {
        this.toastr.error('', res.message);
      }
    },(error:any)=>{
      console.log("error",error);
    });
  }
  getuploadDp(record: any) {
    console.log('Record :', record);
    // this.imagedp = this.imageName.name;
    // this.soundForm.value.AN_dp = this.baseurl + this.imagedp;
    console.log(this.imageName, 'file');
    this.dataTransferService.uploadurl(this.imageName).subscribe((res: any) => {
      console.log('Url Record', res);
      if (!res.error) {
        this.toastr.success('upload file successfully', res.message);
        this.soundForm.patchValue(record);
        // this.getAllSoundBite();
      } else {
        this.toastr.error('', res.message);
      }
    });
    this.soundForm.value.AN_id = record.AN_id;
    this.imagedp = this.imageName.name;
    this.soundForm.value.AN_dp = this.baseurl + this.imagedp;
    let postData = { AN_id: record.AN_id, AN_dp: this.baseurl + this.imagedp };
    this.dataTransferService.updateAll(postData).subscribe((res: any) => {
      console.log('Url Record', res);
      if (!res.error) {
        this.toastr.success('Save data succesfully', res.message);
        this.getAllSoundBite();
      } else {
        this.toastr.error('', res.message);
      }
    },(error:any)=>{
      console.log("error",error);
    });
  }

  onReset() {
    this.soundForm.reset();
  }

  listPage() {
    this.router.navigate([`/actions/insights`]);
  }

  insightAcceptedNotification(){
    const data={
      UN_contentId:"1" ,
      UN_userId:this.selectedRecord.AN_userId 
  }
  console.log("insightAcceptedNotification data",data);
    this.dataTransferService.insightAcceptedNotification(data).subscribe((res:any)=>{
    console.log("insightAcceptedNotification called");
    },(error:any)=>{
      console.log("Error in insightAcceptedNotification",error);
    })
  }

  insightRejectedNotification(){
    
    const data={
      UN_contentId:"1" ,
      UN_userId:this.selectedRecord.AN_userId,
      UN_rejected_title:this.selectedvalue
  }
  console.log("insightRejectedNotification data",data);
    this.dataTransferService.insightRejectedNotification(data).subscribe((res:any)=>{
    console.log("insightRejectedNotification called");
    },(error:any)=>{
      console.log("Error in insightRejectedNotification",error);
    })
  }

  updatedReason(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.soundForm.value.AN_Reject_Reason = this.selectedvalue;
      this.soundForm.value.AN_id = this.ID;
  
      this.dataTransferService.updateRejectReason(this.soundForm.value)
        .subscribe(
          (res: any) => {
            console.log('updated reason', res);
            // this.toastr.success('Reason updated successfully');
            this.router.navigate([`/actions/insights`]);
  
            // Resolve the promise with the response
            resolve(res);
          },
          (error: any) => {
            this.toastr.error("Error updating reason");
            console.log("error", error);
  
            // Reject the promise with the error
            reject(error);
          }
        );
    });
  }
  

  async checkUpdateStatus(record: any, val: any) {
    // if (this.transcriptionGenerated) {
    if (this.wordCount > this.wordLimit) {
      this.toastr.error('Cannot update reason. Word limit exceeded.');
      console.error('Cannot update reason. Word limit exceeded.');
      return;
    }
  
    // Show spinner before starting the update
    this.ngxSpinnerService.show('globalSpinner');
    
    // Prepare the data
    this.AN_status1 = val.toString();
    this.soundForm.value.AN_status = this.AN_status1;
    this.soundForm.value.AN_isPublished = this.AN_status1;
    this.ID = record.AN_id;
    this.soundForm.value.AN_id = this.ID;
  
  
    try {
      if (val === 2) {
        await this.updateStatusAPI(this.soundForm.value);
        console.log('Status updated successfully');
        this.insightAcceptedNotification();
        const badgeData = await this.getBadgeData();  //getting badge data here
        console.log('Badge Data:', badgeData);
  
        if (badgeData && badgeData.entries && badgeData.entries.length > 0) {  //generating certificate from that badge data
          this.generateCertificates(badgeData);
        } else {
          console.warn('No badge data available');
        }
        // this.ngxSpinnerService.hide('globalSpinner');
      } else {
        await this.updatedReason();
        await this.updateStatusAPI(this.soundForm.value);
        this.insightRejectedNotification();
        this.getAllSoundBite();
        this.router.navigate(['/actions/insights']);
        this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner after navigation
      }
    } catch (error) {
      console.error('Error in updateStatus:', error);
      this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner on error
    }
    // } else {
    //   this.toastr.info('Please generate the transcription first');
    // }
  }
  

  getBadgeData(): Promise<any> {
    return new Promise((resolve, reject) => {
      const userId=this.selectedRecord.AN_userId;
      const industryId=this.selectedRecord.AN_industryId;
      const roleId=this.selectedRecord.AN_degreeId;

      console.log('Fetching badge data for user:', userId,industryId,roleId);
      this.dataTransferService.getBadgeData(userId,industryId,roleId).subscribe(
        (badgeData: any) => {
          console.log('Received badge data:', badgeData);
          resolve(badgeData);
        },
        (error: any) => {
          this.getAllSoundBite();
          this.router.navigate(['/actions/insights']);
          console.info('No Badges Found !');
          reject(new Error(error.message || 'Unknown error occurred'));
        }
      );
    });
  }

  updateStatusAPI(data: any): Promise<void> {
    return new Promise((resolve, reject) => {
      this.dataTransferService.updateStatus(data).subscribe(
        (res: any) => {
          if (res.statusCode === 200) {
            this.toastr.success('Status updated successfully', res.message);
            resolve();
          } else {
            this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner on error
            reject(new Error(res ? res.message : 'Unknown error occurred'));
          }
        },
        (error: any) => {
          this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner on error
          reject(new Error(error.message || 'Unknown error occurred'));
          this.toastr.error('Error', error.message || 'Unknown error occurred');
        }
      );
    });
  }

  generateCertificates(badgeData: any) {
    this.badgeData = badgeData;
    const badges = badgeData.entries[0].badges;
    if (!badges || badges.length === 0) {
      console.warn('No badges to generate certificates for.');
      return;
    }

    this.badges = badges; 
    this.currentBadgeIndex = 0; // Initialize the current badge index

    this.processBadge();
  }

   processBadge() {
    if (this.currentBadgeIndex >= this.badges.length) {
      // All badges processed, navigate to the new route
      if(this.badges.length>0){
        this.saveBadge().then(()=>{
        this.router.navigate(['/actions/insights']);
        this.ngxSpinnerService.hide('globalSpinner'); 
        return;
      })
      }else{
      this.getAllSoundBite();
      this.router.navigate(['/actions/insights']);
      this.ngxSpinnerService.hide('globalSpinner'); 
      return;
    }
    }
    const badge = this.badges[this.currentBadgeIndex];
    this.userName = this.badgeData.user.userName; // Use stored badgeData
    this.industryName = this.badgeData.entries[0].industryName;
    this.roleName = this.badgeData.entries[0].roleTitle;
    this.certificateTitle = badge?.BA_title;
    this.BA_id=badge?.BA_id
    this.certificateDescription = badge?.BA_description;
    this.badgeImage = badge?.BA_image;
    this.dateEarned = this.formatDate(new Date()); // Set today's date
    this.BA_percentage = badge?.BA_percentage;
    this.cdr.detectChanges();
    setTimeout(() => {
      this.captureImage(this.currentBadgeIndex , this.userName,this.certificateTitle);
    }, 2000); // Delay to ensure data is rendered
  }

  formatDate(date: Date): string {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return date.toLocaleDateString('en-US', options);
  }

  captureImage(index: number, userName: any,certificateTitle:any) {
    this.ngxSpinnerService.show('globalSpinner'); // Show spinner while capturing and uploading the image

    const code = document.getElementById('certificate');
    if (code) {
      html2canvas(code)
        .then((canvas) => {
          const imgData = canvas.toDataURL('image/png');
          // const timestamp = new Date().toISOString().replace(/[:.-]/g, '');
          const AN_userId=this.selectedRecord.AN_userId;
          const RoleId=this.selectedRecord.AN_degreeId;
          const fileName = `${RoleId}_${AN_userId}_Badge_${certificateTitle}`;
          const file = this.dataURLtoFile(imgData, fileName);

          // Upload the file
          this.uploadImage(file)
            // .then(() => {
            //   this.saveBadge(file);
            // })
            .then(() => {
              this.currentBadgeIndex++;
              this.processBadge(); // Process the next badge
            })
            .catch((error) => {
              console.error('Error uploading badge:', error);
              this.toastr.error('Error', 'Failed to upload badge.');
            })
        }).catch((error) => {
          console.error('Error capturing badge:', error);
          this.toastr.error('Error', 'Failed to capture badge.');
          this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner
        });
    } else {
      console.error("Element with ID 'certificate' not found.");
      this.ngxSpinnerService.hide('globalSpinner'); // Hide spinner
    }
  }

  dataURLtoBlob(dataURL: string): Blob {
    // Split the data URL into parts
    const arr = dataURL.split(',');

    // Extract the MIME type
    const mimeMatch = arr[0].match(/:(.*?);/);
    if (!mimeMatch) {
      throw new Error('Invalid data URL');
    }
    const mime = mimeMatch[1];

    // Decode the base64 string
    const bstr = atob(arr[1]);
    const u8arr = new Uint8Array(bstr.length);

    // Populate the Uint8Array with the base64 string bytes
    for (let i = 0; i < bstr.length; i++) {
      u8arr[i] = bstr.charCodeAt(i);
    }

    // Return the Blob with the correct MIME type
    return new Blob([u8arr], { type: mime });
  }

  dataURLtoFile(dataURL: string, filename: string): File {
    const blob = this.dataURLtoBlob(dataURL);
    return new File([blob], filename, { type: blob.type });
  }

  uploadImage(file: File): Promise<void> {
    return new Promise((resolve, reject) => {
      this.dataTransferService.uploadurl(file).subscribe(
        (res: any) => {
          console.log('Badge uploaded successfully in S3 Bucket:', );
          const Newfile=this.baseurl+file.name;
          const data={
            UB_badgeId:this.BA_id,
            UB_roleId:this.selectedRecord.AN_degreeId,
            UB_sectorId:this.selectedRecord.AN_industryId,
            UB_userId:this.selectedRecord.AN_userId,
            UN_title:this.certificateTitle,
            UB_badge:Newfile
          }
          this.fileArray.push(data)
          resolve();
        },
        (error: any) => {
          console.error('Error uploading Badge  in S3 Bucket:', error);
          this.toastr.error('Error uploading Badge',);
          reject(new Error(error.message || 'Unknown error occurred'));
        }
      );
    });
  }

  saveBadge(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log("this.fileArray",this.fileArray);
      this.dataTransferService.saveBadge(this.fileArray).subscribe(
        (res: any) => {
          console.log('Badge saved in database');
          resolve();
        },
        (error: any) => {
          // this.toastr.error('Error uploading the badge.');
          console.error('Error uploading Badge:', error);
          reject(new Error(error.message || 'Unknown error occurred'));
        }
      );
    });
  }

  downloadImage(file: any, fileName: any): void {
    const a = document.createElement('a');
    a.href = file;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }

  getBadgeImage(title: string): any {
    switch (title) {
      case 'Insight Initiator':
        return './assets/images/badge_1.png';
      case 'Career Contributor':
        return './assets/images/badge_2.png';
      case 'Social Mobility Champion':
        return './assets/images/badge_3.png';
      default:
        return null; 
    }
  }

  // downloadImage(dataUrl: string, filename: string) {
  //   const link = document.createElement('a');
  //   link.href = dataUrl;
  //   link.download = filename;
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);
  // }

  getSoundbiteByid(AN_id: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.dataTransferService.getSoundbiteByid(AN_id).subscribe(
        (res: any) => {
          if (res.statusCode === 200) {
            this.selectedRecord = res.data;
            resolve(res.data);
          } else {
            reject(`Unexpected status code: ${res.statusCode}`);
          }
        },
        (error: any) => {
          console.log("error", error);
          reject(error);
        }
      );
    });
  }
}

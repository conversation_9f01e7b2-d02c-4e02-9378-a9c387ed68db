import { Component, OnInit, QueryList, ViewChildren } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import {
  ActivatedRoute,
  Router,
  Params,
  NavigationEnd,
  RouterEvent,
} from '@angular/router';
import { filter } from 'rxjs/operators';
import { DatePipe } from '@angular/common';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormArray,
  FormControl,
  AbstractControl,
  ValidatorFn,
  ValidationErrors,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { HttpClient } from '@angular/common/http';
import state from 'sweetalert/typings/modules/state';
import {ViewChild, ElementRef } from '@angular/core';
import { FileValidator } from 'src/app/shared/validators/fileValidator.validator';

@Component({
  selector: 'app-add-edit-university-list',
  templateUrl: './add-edit-university-list.component.html',
  styleUrls: ['./add-edit-university-list.component.scss']
})
export class AddEditUniversityListComponent implements OnInit {
  addNewUniversityForm: FormGroup;
  imageSrc: string | ArrayBuffer | null;
  p: number = 1;
  user: any;
  imageName: any;
  showForm = false;
  queryParam: any;
  term: string;
  submitted = false;
  title = 'Add New';
  isReadonly = false;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  selectedAudioFile: any;
  serviceLinkHeadings = [
    'Appointment',
    'Internship Discussions',
    'Practice Interviews',
    'Events',
    'Online Resources',
    'Jobs'
];

  serviceLinkKeys = [
    'SL_appointment',
    'SL_internshipDiscussion',
    'SL_practiceInterview',
    'SL_events',
    'SL_onlineResources',
    'SL_jobs'
  ];
  universityData: any;
  deleteId: any;
  toEditInsight: any;
  audioFiles: File[] = [];
  characterCount: number = 0;
  isPlaying: boolean[] = []; // Array to track playback state
  audioUrls: string[] = []; // Array to store audio URLs
  @ViewChildren('audioPlayer') audioPlayers: QueryList<ElementRef<HTMLAudioElement>>;
  showOtherTypeInput: boolean = false;
  employerData: any;
  ExistingLogo: any;
  existingAudioNames: string[] = [];
  audioFileUrls: string[] = []; 
  INS_id: any;
  fileUrl: string;
  
  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private domSanitizer: DomSanitizer,
    private http: HttpClient,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private datePipe: DatePipe
  ) {

    this.addNewUniversityForm = this.formBuilder.group({
      INS_title: ['', [Validators.required]],
      INS_dp: [''],
      INS_website: ['', [Validators.required, Validators.pattern('https?://.+')]],
      INS_service: ['', [Validators.required]],
      INS_serviceLinks: this.formBuilder.array(this.serviceLinkKeys.map((key, index) => this.createServiceLinkGroup(key))),
      INS_HrInsights:  this.formBuilder.array([this.createInsight()]),
    });

    this.route.queryParams.subscribe(param=>{
      this.INS_id=param['INS_id'];
      console.log("INS_id",this.INS_id);
    })

    const state=this.router.getCurrentNavigation()?.extras.state;
    if(state){
    (this.title=state.title);
    }else{
      this.router.navigate([`/actions/universities`]);
    }

   }

  ngOnInit(): void {
    if (this.title === 'Edit') {
      this.getInstituteById(this.INS_id);
    }
  }

  async getInstituteById(INS_id: any): Promise<void> {
    try {
      const res: any = await this.dataTransferService.getInstituteById(INS_id).toPromise();
      console.log("getInstituteById res", res);
  
      if (res && res.data) {
        this.universityData = res.data;
        this.patchForm(this.universityData);
      } else {
        console.error("Response is undefined or missing 'data' property.");
      }
    } catch (error) {
      console.error("Error fetching institute data:", error);
    }
  }
  

  patchForm(data: any) {
    if (this.title === 'Edit') {
      const serviceLinks = this.addNewUniversityForm.get('INS_serviceLinks') as FormArray;
      if(data?.INS_serviceLinks){
      console.log("data.INS_serviceLinks");
        
      data?.INS_serviceLinks?.forEach((serviceLink: any, index: number) => {
        const linkKey = this.serviceLinkKeys[index]; 
        const formGroup = serviceLinks.at(index) as FormGroup;
    
        if (formGroup) {
          formGroup.patchValue({
            [linkKey]: serviceLink[linkKey] || '',
            SL_description: serviceLink.SL_description || ''
          });
        }
      });
    }
    
      this.addNewUniversityForm.patchValue({
        INS_title: data?.INS_title,
        INS_website: data?.INS_website,
        INS_service: data?.INS_service
      });

      if(data?.INS_HrInsights){
      const hrInsightsArray = this.addNewUniversityForm.get('INS_HrInsights') as FormArray;
      hrInsightsArray.clear();
      data.INS_HrInsights.forEach((insight: any) => {
        const insightFormGroup = this.formBuilder.group({
          HRI_title: [insight.HRI_title],
          HRI_name: [insight.HRI_name],
          HRI_position: [insight.HRI_position],
          HRI_link: [insight.HRI_link]
        });
        hrInsightsArray.push(insightFormGroup);
        
        this.audioUrls = data.INS_HrInsights.map((insight: any) => insight.HRI_link);
        this.isPlaying = new Array(data.INS_HrInsights.length).fill(false);
        this.existingAudioNames = data.INS_HrInsights.map((insight: any) => insight.HRI_link.substring(insight.HRI_link.lastIndexOf('/') + 1));
      });
    }
    }
  }

  updateUniversity() {
  
    if (this.addNewUniversityForm.invalid) {
      Object.keys(this.addNewUniversityForm.controls).forEach(name => {
        const control = this.addNewUniversityForm.get(name);
        if (control?.invalid) {
          console.log(`Invalid control: ${name}, Errors:`, control.errors);
        }
      });
      this.toastr.info('Please fill all required fields correctly');
      return;
    }
  
    const uploadDp = this.addNewUniversityForm.get('INS_dp')?.value ? this.uploadLogoUrl() : Promise.resolve(this.universityData.INS_dp);
  
    this.ngxSpinnerService.show('globalSpinner');
  
    uploadDp.then((dpUrl: string) => {
      return this.uploadAudioFiles(this.audioFiles).then((audioUrls: string[]) => {
        console.log("Uploaded audio files:", this.audioFiles);
  
        this.insightFormArray.controls.forEach((control: AbstractControl, index: number) => {
          (control as FormGroup).get('HRI_link')?.setValue(this.audioUrls[index]);
        });
  
        const data = {
          INS_id:this.INS_id,
          INS_title: this.addNewUniversityForm.get('INS_title')?.value,
          INS_dp: dpUrl?dpUrl:"",
          INS_website: this.addNewUniversityForm.get('INS_website')?.value,
          INS_service: this.addNewUniversityForm.get('INS_service')?.value,
          INS_serviceLinks: this.addNewUniversityForm.get('INS_serviceLinks')?.value,
          INS_HrInsights: this.addNewUniversityForm.get('INS_HrInsights')?.value,
          INS_city: '',    // Assuming these fields are optional and set to empty strings by default
          INS_country: '', // You can modify this as needed
          INS_state: ''
        };
  
        console.log("Data to update...", data);
        return this.dataTransferService.updateInstitute(data).toPromise();
      });
    }).then((res: any) => {
      this.ngxSpinnerService.hide('globalSpinner');
      if (res.statusCode === 200) {
        this.toastr.success('University updated successfully.');
        this.router.navigate(['actions/universities']);
      } else {
        this.toastr.error('Something went wrong.');
      }
    }).catch((error: any) => {
      this.ngxSpinnerService.hide('globalSpinner');
      const errorMessage = error === 'Audio file upload failed.' ? error : 'Error updating university.';
      this.toastr.error(errorMessage);
    });
  }

  
  get serviceLinks(): FormArray {
    return this.addNewUniversityForm.get('INS_serviceLinks') as FormArray;
  }

  get insightFormArray(): FormArray {
    return this.addNewUniversityForm.get('INS_HrInsights') as FormArray;
  }
  
  createServiceLinkGroup(linkKey: string): FormGroup {
    let group: any = {};
    group[linkKey] = ['', [Validators.pattern('https?://.+')]];
    group['SL_description'] = [''];
  
    const formGroup = this.formBuilder.group(group);
  
    // Apply the custom validator
    formGroup.setValidators(this.linkDescriptionValidator(linkKey, 'SL_description'));
    
    return formGroup;
  }
  
  linkDescriptionValidator(linkKey: string, descriptionKey: string): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      const linkControl = formGroup.get(linkKey);
      const descriptionControl = formGroup.get(descriptionKey);
  
      if (linkControl?.value && !descriptionControl?.value) {
        descriptionControl?.setErrors({ required: true });
        return { descriptionRequired: true };
      } else {
        descriptionControl?.setErrors(null);
        return null;
      }
    };
  }
  
  addInsight() {
    this.insightFormArray.push(this.createInsight());
  }

  removeInsight(index: number) {
    this.insightFormArray.removeAt(index);
    this.audioUrls.splice(index, 1); // Remove URL for deleted row
    this.isPlaying.splice(index, 1); // Remove playback state for deleted row
    this.audioFiles.splice(index, 1); // Remove file object for deleted row
  }

  toggleAudio(index: number) {
    const audioElements = this.audioPlayers.toArray();
    const audioElement = audioElements[index].nativeElement;

    // Stop all other audio
    this.stopAllAudio(index);

    // Toggle play/pause for the current audio element
    if (this.isPlaying[index]) {
      audioElement.pause();
    } else {
      audioElement.src = this.audioUrls[index];
      audioElement.play();
    }

    // Update the playback state
    this.isPlaying[index] = !this.isPlaying[index];
  }

  stopAllAudio(currentIndex: number) {
    this.audioPlayers.forEach((audioPlayer, index) => {
      if (index !== currentIndex) {
        audioPlayer.nativeElement.pause();
        this.isPlaying[index] = false;
      }
    });
  }

  stopAudio(index: number) {
    const audioElements = this.audioPlayers.toArray();
    const audioElement = audioElements[index].nativeElement;
  
    audioElement.pause();
    audioElement.currentTime = 0; // Reset to the beginning
    this.isPlaying[index] = false;
  }

  createInsight(): FormGroup {
    return this.formBuilder.group({
      HRI_title: [''],
      HRI_name: [''],
      HRI_position: [''],
      HRI_link: [null]
    });
    this.isPlaying.push(false); // Initialize playback state for new row
    this.audioUrls.push(''); // Initialize empty URL for new row
  }

  onAudioSelected(event: any, index: number): void {
    let audiofile: File | null;
    const selectedFile = event.target.files[0];
    
    if (selectedFile) {
      const newFileName = FileValidator.addTimestamp(selectedFile.name);
      audiofile = new File([selectedFile], newFileName, { type: selectedFile.type });
    } else {
      audiofile = null;
    }

    if (!audiofile) {
      this.audioUrls[index] = '';
      this.stopAudio(index);
      return;
    }
    const audiofileType = audiofile.type.split('/')[0]; // Get the file type (e.g., 'audio', 'video', etc.)
    if (audiofileType !== 'audio') {
      // Reset the file input to clear the selected file
      event.target.value = '';
      this.toastr.info('Please select an audio file.');
      return;
    }
  
    // Store the file object in the array for later upload
    this.audioFiles[index] = audiofile;
    console.log('Audio file :', audiofile);
  
    const reader = new FileReader();
    reader.onload = () => {
      // Store audio URL
      this.audioUrls[index] = reader.result as string;
    };
    reader.readAsDataURL(audiofile);
  }
  
 uploadAudioFiles(files: File[]): Promise<string[]> {
  const uploadPromises = files.map((file, index) => this.uploadSingleAudioFile(file, index));
  return Promise.all(uploadPromises);
}

// Updated uploadSingleAudioFile function
uploadSingleAudioFile(file: File, index: number): Promise<string> {
  return new Promise((resolve, reject) => {
    this.dataTransferService.uploadurl(file).subscribe(
      (res: any) => {
        console.log('Upload successful', file.name);
        const fileUrl = this.baseUrl + file.name;
        this.audioUrls[index] = fileUrl; 
        resolve(fileUrl);
      },
      (error: any) => {
        console.error('Upload error', error);
        this.toastr.error('Failed to upload audio file');
        reject(error);
      }
    );
  });
}
  

  onFileSelected(event: any) {
    let selectedFile = event.target.files[0];

    if (event.target.files.length === 0) {
      // Reset both imageName and imageSrc when no file is selected
      this.imageName = null;
      this.imageSrc = null;
      return;
    }

    const newFileName = FileValidator.addTimestamp(selectedFile.name);
    this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });
    
    if(this.imageName){
    const formControl=this.addNewUniversityForm.get('INS_dp');
    formControl?.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));
    formControl?.updateValueAndValidity();
  }

    const fileType = this.imageName.type.split('/')[0];
    const fileExtension = this.imageName.name.split('.').pop()?.toLowerCase();
    if (fileType !== 'image' || fileExtension === 'svg') {
      event.target.value = '';
      this.toastr.info('Please select an image file (excluding SVG).');
      this.imageName = null;
      this.imageSrc = null;
      return;
    }

    if (this.imageName && fileType == 'image') {
      const reader = new FileReader();
      reader.onload = (e) => {
        this.imageSrc = e.target?.result as string | ArrayBuffer;
      };
      reader.readAsDataURL(this.imageName);
    } else {
      this.imageSrc = null; // Reset imageSrc if no file selected
    }
    console.log('imageName', this.imageName);
  }

  uploadLogoUrl(): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.imageName) {
        reject('Please select an image.');
        return;
      }
  
      console.log('Uploading image:', this.imageName);
  
      this.dataTransferService.uploadurl(this.imageName).subscribe(
        (res: any) => {
          const fileUrl = this.baseUrl + this.imageName.name; // Ensure you're concatenating correctly
          resolve(fileUrl);
        },
        (error) => {
          reject('Error uploading image: ' + error.message || error);
        }
      );
    });
  }
  

  
  async saveUniversity() {
    if(this.addNewUniversityForm.invalid){
      console.log('this.job.value', this.addNewUniversityForm.value);
      this.ngxSpinnerService.hide('globalSpinner');
      this.toastr.info(
        'Please fill all required fields and ensure they are filled correctly.'
      );
      return;
  }else{
    try {
      this.ngxSpinnerService.show('globalSpinner');  
      if(this.imageName){
      await this.uploadLogoUrl();
      this.fileUrl = this.baseUrl + this.imageName.name;
    }
      const audioUrls: string[] = await this.uploadAudioFiles(this.audioFiles);
      console.log("this.audioFiles", this.audioFiles);
  
      this.insightFormArray.controls.forEach((control: AbstractControl, index: number) => {
        const group = control as FormGroup;
        group.get('HRI_link')?.setValue(audioUrls[index]);
      });
  
      console.log("this.addNewUniversityForm.value", this.addNewUniversityForm.value);
      const data={
      INS_title:this.addNewUniversityForm.get('INS_title')?.value,
      INS_dp: this.fileUrl,
      INS_website:this.addNewUniversityForm.get('INS_website')?.value,
      INS_service: this.addNewUniversityForm.get('INS_service')?.value,
      INS_serviceLinks:this.addNewUniversityForm.get('INS_serviceLinks')?.value,
      INS_HrInsights:this.addNewUniversityForm.get('INS_HrInsights')?.value,
      INS_city:'',
      INS_country:'',
      INS_state:''
    }

    console.log("Add University Data : ",data);
    
      this.dataTransferService.addUniversity(data).subscribe(
        (res: any) => {
          this.ngxSpinnerService.hide('globalSpinner');  
          this.toastr.success("University added successfully");
          this.router.navigate([`/actions/universities`]);
        },
        (error: any) => {
          this.ngxSpinnerService.hide('globalSpinner');  
          console.log("Error", error);
          this.toastr.error("Unable to add university");
        }
      );
    
    } catch (error) {
      this.ngxSpinnerService.hide('globalSpinner');  
      console.error("An error occurred while saving the university", error);
      this.toastr.error("An error occurred while saving the university");
    }
  }
}


onSubmit(){
  if(this.title=='Add'){
    this.saveUniversity();
  }else{
    this.updateUniversity()
  }
}


  
}

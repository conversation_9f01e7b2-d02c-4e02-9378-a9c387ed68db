// .modal-header{
//     color: white;
//     background-color: #007bff;
// }
// .select{
//     display: flex; justify-content: space-between;
// }
.description-content {
    max-width: 400px; /* Adjust this value as needed */
    overflow-x: scroll;
    overflow-y: hidden;
    margin-right: 10px;
  }
  .square-img {
    border-radius: 0; /* Setting border-radius to 0 removes rounded corners */
    width: 50px;
}
.description-content::-webkit-scrollbar {
    display: none; /* Hide scrollbar for webkit browsers */
  }

  .custom-radius-btn {
    border-radius: 12px; /* Change this value to whatever you need */
  }


  
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-university-list',
  templateUrl: './university-list.component.html',
  styleUrls: ['./university-list.component.scss']
})
export class UniversityListComponent implements OnInit {

  p: number = 1;
  term: string;
  showForm = true;
  submitted = false;
  isReadonly = false;
  title = 'View';
  hideHeader=false;
  UniversityData: any;
  deleteId: any;

  constructor(private formBuilder: FormBuilder,
    private dataTransferService : DataTransferService,
    private toastr:ToastrService,
    private router:Router,
    private activeRoute: ActivatedRoute,
    private httpClient: HttpClient,
    private ngxSpinnerService: NgxSpinnerService) {     
     
     }

  ngOnInit(): void {
    this.getAllUniversity();
  }

  showAddNewUniversity(){
    const state={
      title:'Add',
    }
    this.router.navigate([`/actions/universities/add-edit-university`],{state});
  }

  showEditUniversity(university:any){
    const state={
      title:'Edit',
    }
    this.router.navigate([`/actions/universities/add-edit-university`],{state,queryParams:{INS_id:university.INS_id}});
  }

  getAllUniversity() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllUniversity().subscribe(
      (res: any) => {
        if (res.statusCode === 200) {
          this.UniversityData = res.data.sort((a: any, b: any) => {
            if (!a.INS_createdAt) return 1; 
            if (!b.INS_createdAt) return -1; 
            return b.INS_createdAt.localeCompare(a.INS_createdAt); 
          });
          console.log('this.UniversityData', this.UniversityData);
          this.ngxSpinnerService.hide('globalSpinner');
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error(
            'Failed to retrieve University Data or data is empty. Status code:',
            res.statusCode
          );
        }
      },
      (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error while fetching University data:', error);
      }
    );
  }


getDeleteId(deleteId: any): void {
  this.deleteId = deleteId;
}

deleteInstituteById(): void {
  let INS_id = this.deleteId;
  this.ngxSpinnerService.show('globalSpinner');
  this.dataTransferService.deleteInstituteById(INS_id).subscribe(
    (res: any) => {
      this.ngxSpinnerService.hide('globalSpinner');
      this.toastr.success("University deleted successfully.");
      console.log(`University Deleted: ${INS_id}`); 
      this.getAllUniversity();
    },
    (error: any) => {
      this.ngxSpinnerService.hide('globalSpinner');
      console.log("Unable to delete university", error);
      this.toastr.error("Unable to delete university");
    }
  );
}



  
 }

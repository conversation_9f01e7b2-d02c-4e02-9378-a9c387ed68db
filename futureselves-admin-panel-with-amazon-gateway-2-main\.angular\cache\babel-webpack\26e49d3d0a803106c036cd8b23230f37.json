{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { scan } from './scan';\nimport { defer } from '../observable/defer';\nimport { map } from './map';\nexport function timeInterval(scheduler = async) {\n  return source => defer(() => {\n    return source.pipe(scan(({\n      current\n    }, value) => ({\n      value,\n      current: scheduler.now(),\n      last: current\n    }), {\n      current: scheduler.now(),\n      value: undefined,\n      last: undefined\n    }), map(({\n      current,\n      last,\n      value\n    }) => new TimeInterval(value, current - last)));\n  });\n}\nexport class TimeInterval {\n  constructor(value, interval) {\n    this.value = value;\n    this.interval = interval;\n  }\n\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/operators/timeInterval.js"], "names": ["async", "scan", "defer", "map", "timeInterval", "scheduler", "source", "pipe", "current", "value", "now", "last", "undefined", "TimeInterval", "constructor", "interval"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,SAASC,GAAT,QAAoB,OAApB;AACA,OAAO,SAASC,YAAT,CAAsBC,SAAS,GAAGL,KAAlC,EAAyC;AAC5C,SAAQM,MAAD,IAAYJ,KAAK,CAAC,MAAM;AAC3B,WAAOI,MAAM,CAACC,IAAP,CAAYN,IAAI,CAAC,CAAC;AAAEO,MAAAA;AAAF,KAAD,EAAcC,KAAd,MAAyB;AAAEA,MAAAA,KAAF;AAASD,MAAAA,OAAO,EAAEH,SAAS,CAACK,GAAV,EAAlB;AAAmCC,MAAAA,IAAI,EAAEH;AAAzC,KAAzB,CAAD,EAA+E;AAClGA,MAAAA,OAAO,EAAEH,SAAS,CAACK,GAAV,EADyF;AAElGD,MAAAA,KAAK,EAAEG,SAF2F;AAGlGD,MAAAA,IAAI,EAAEC;AAH4F,KAA/E,CAAhB,EAIHT,GAAG,CAAC,CAAC;AAAEK,MAAAA,OAAF;AAAWG,MAAAA,IAAX;AAAiBF,MAAAA;AAAjB,KAAD,KAA8B,IAAII,YAAJ,CAAiBJ,KAAjB,EAAwBD,OAAO,GAAGG,IAAlC,CAA/B,CAJA,CAAP;AAKH,GANuB,CAAxB;AAOH;AACD,OAAO,MAAME,YAAN,CAAmB;AACtBC,EAAAA,WAAW,CAACL,KAAD,EAAQM,QAAR,EAAkB;AACzB,SAAKN,KAAL,GAAaA,KAAb;AACA,SAAKM,QAAL,GAAgBA,QAAhB;AACH;;AAJqB", "sourcesContent": ["import { async } from '../scheduler/async';\nimport { scan } from './scan';\nimport { defer } from '../observable/defer';\nimport { map } from './map';\nexport function timeInterval(scheduler = async) {\n    return (source) => defer(() => {\n        return source.pipe(scan(({ current }, value) => ({ value, current: scheduler.now(), last: current }), {\n            current: scheduler.now(),\n            value: undefined,\n            last: undefined,\n        }), map(({ current, last, value }) => new TimeInterval(value, current - last)));\n    });\n}\nexport class TimeInterval {\n    constructor(value, interval) {\n        this.value = value;\n        this.interval = interval;\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}
import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, FormsModule, Validators } from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, ActivatedRoute } from '@angular/router';
import { Constants } from 'src/app/config/constants';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
import { CamelCasePipe } from '../../../../../camel-case.pipe';
import { EncryptionService } from 'src/app/shared/services/encryption.service';
@Component({
  selector: 'app-app-users',
  templateUrl: './app-users.component.html',
  styleUrls: ['./app-users.component.scss']
})
export class AppUsersComponent implements OnInit {
  userList:any[]=[];
  AddUser = false;
  showForm = false;
  roles:any;
  p:number=1;
  term: string;
  isReadonly = false;
  submitted = false;
  queryParam :any;
  filtervalue: any;
  tempUserList: any;
  contentVisible=false
  userTypeId: string | null;
  constructor(
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private domSanitizer: DomSanitizer,
    private router: Router,
    private formModule: FormsModule,
    private http: HttpClient,
    private ngxSpinnerService: NgxSpinnerService,
    private encryptionService: EncryptionService
  ) { }

  ngOnInit(): void {

    this.userTypeId = sessionStorage.getItem('userTypeId');
    console.log('User Type ID:', this.userTypeId);
    
    setTimeout(() => {
      this.contentVisible = true;
    }, 100);

    this.getAllAppUsers();

  }

  getAllAppUsers() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getAllAppUsers().subscribe(
        (res: any) => { // Directly handling the response
            if (res.statusCode === 200) {
                const temp = res.data;
                console.log("Original data:", temp);

                // Decrypting user data
                this.userList = temp.map((user: any) => {
                  try {
                    return {
                      ...user,
                      U_name: this.encryptionService.decrypt(user.U_name),
                      U_email: this.encryptionService.decrypt(user.U_email),
                      U_id: this.encryptionService.decrypt(user.U_id),
                    };
                  } catch (error) {
                    console.error('Decryption Error for user:', user, error);
                    // Returning the user object with 'Decryption Error' message
                    return { 
                      ...user, 
                      U_name: 'Decryption Error', 
                      U_email: 'Decryption Error', 
                      U_id: 'Decryption Error' 
                    };
                  }
                }).sort((a: any, b: any) => {
                    const dateA = a.U_createdAt ? new Date(a.U_createdAt).getTime() : 0;
                    const dateB = b.U_createdAt ? new Date(b.U_createdAt).getTime() : 0;                    
                    return dateB - dateA; 
                });

                this.tempUserList = this.userList;
                console.log("Sorted App Sharer list:", this.userList);
                this.ngxSpinnerService.hide('globalSpinner');
            } else {
                this.toastr.error("Unable to fetch data");
                this.ngxSpinnerService.hide('globalSpinner');
            }
        },
        (error: any) => { // Handling error response
            console.log('Error Message', error);
            this.ngxSpinnerService.hide('globalSpinner');
            this.toastr.error("Unable to fetch data");
        }
    );
}



  showStatusForm(user: any) {
    const state = {
      userData:user
    };
    this.router.navigate(['/actions/app-users/user-status'], { state });
  }
  
  // showPreferncesForm(id: any) {
  //   const queryParams = {
  //     title: 'Preferences',
  //     userId: id
  //   };
  //   this.router.navigate(['/actions/app-users/identity-preferences'], { queryParams });
  // }

  showEditUserForm(userData: any) {
   this.router.navigate(['/actions/app-users/add-edit-app-user'], { queryParams: { U_id: userData.U_id,title:'Edit' } });
  }

  showAddUserForm() {
    this.router.navigate(['/actions/app-users/add-edit-app-user'], { queryParams: {title:'Add' } });
   }

   showAddNewInsightForm(user:any) {
    this.router.navigate(['/actions/app-users/choose-position'], { queryParams: { U_id:user.U_id, U_isExpert:user.U_isExpert } });
}


  resetPagination() {
    this.p = 1;
  }

  
  search(term: string) {
    // Reset pagination to page 1
    this.resetPagination();
    // Perform search logic here
  }

  
  selected(event: any) {
    this.filtervalue = event.target.value;
    console.log("filtervalue", this.filtervalue);
    if (this.filtervalue=="All"){
      this.getAllAppUsers();
    }else{
     const temp = this.tempUserList.filter((obj:any) => obj.U_activeStatus == this.filtervalue);
     this.userList=temp.sort((a: any, b: any) => {
      const dateA = new Date(a.U_createdAt).getTime();
      const dateB = new Date(b.U_createdAt).getTime();
      return dateB - dateA; // Sort by descending date
    });
  }
}

}

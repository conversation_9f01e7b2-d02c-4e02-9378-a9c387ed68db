{"name": "gradvisor", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "~13.0.0", "@angular/cdk": "^13.3.9", "@angular/common": "~13.0.0", "@angular/compiler": "~13.0.0", "@angular/core": "~13.0.0", "@angular/forms": "~13.0.0", "@angular/material": "^13.3.9", "@angular/platform-browser": "~13.0.0", "@angular/platform-browser-dynamic": "~13.0.0", "@angular/router": "~13.0.0", "@fortawesome/fontawesome-free": "^6.5.1", "@mdi/font": "^3.6.95", "@ng-bootstrap/ng-bootstrap": "^5.2.1", "@ng-select/ng-select": "^8.0.0", "ace-builds": "^1.15.3", "angular-tag-input": "^3.3.1", "bootstrap": "^4.5.0", "bootstrap-datepicker": "^1.9.0", "bootstrap-icons": "^1.11.3", "bootstrap-maxlength": "1.6.0", "chart.js": "^4.4.3", "clipboard": "^2.0.4", "codemirror": "^5.65.12", "colcade": "^0.2.0", "cors": "^2.8.5", "cros": "^1.0.1", "crypto-js": "^4.2.0", "datatables.net": "^1.13.3", "datatables.net-bs4": "^1.13.3", "flag-icon-css": "^3.3.0", "font-awesome": "4.7.0", "gulp-sourcemaps": "^2.6.3", "html2canvas": "^1.4.1", "inputmask": "^4.0.8", "jquery": "^3.6.3", "jquery-asColorPicker": "0.4.4", "jquery-bar-rating": "1.2.2", "jquery-contextmenu": "^2.8.0", "jquery-file-upload": "^4.0.11", "merge-stream": "^2.0.0", "moment": "^2.24.0", "ng-circle-progress": "^1.6.0", "ng2-search-filter": "^0.5.1", "ngx-chips": "^3.0.0", "ngx-image-cropper": "^6.1.0", "ngx-mask": "^13.2.2", "ngx-pagination": "^6.0.3", "ngx-spinner": "^13.1.1", "ngx-toastr": "^14.3.0", "nouislider": "^13.1.5", "owl-carousel-2": "0.0.3", "perfect-scrollbar": "^1.4.0", "popper.js": "^1.15.0", "primeicons": "^7.0.0", "primeng": "^17.18.0", "primeng-lts": "^12.2.5", "progressbar.js": "1.0.1", "puse-icons-feather": "^1.1.0", "pwstabs": "1.4.0", "quill": "^1.3.6", "raphael": "^2.2.8", "rickshaw": "^1.6.6", "rxjs": "^7.4.0", "select2": "^4.0.7", "select2-bootstrap-theme": "0.1.0-beta.10", "simplemde": "1.11.2", "summernote": "^0.8.12", "sweetalert": "^2.1.2", "tempusdominus-bootstrap-4": "^5.1.2", "ti-icons": "^0.1.2", "tinymce": "^5.10.7", "tslib": "^2.5.0", "twbs-pagination": "1.4.1", "typeahead.js": "^0.11.1", "x-editable": "^1.5.1", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.0.2", "@angular/cli": "^13.0.4", "@angular/compiler-cli": "~13.0.0", "@types/crypto-js": "^4.2.2", "@types/jasmine": "^3.10.7", "@types/node": "^12.11.1", "browser-sync": "^2.28.1", "compass-mixins": "^0.12.10", "del": "^4.1.1", "gulp": "^4.0.2", "gulp-concat": "^2.6.1", "gulp-inject": "^5.0.2", "gulp-inject-partials": "^1.0.3", "gulp-rename": "^1.2.2", "gulp-replace": "^1.1.4", "gulp-sass": "^4.0.2", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "minimatch": "^3.1.2", "node-sass": "^4.12.0", "typescript": "~4.4.3"}}
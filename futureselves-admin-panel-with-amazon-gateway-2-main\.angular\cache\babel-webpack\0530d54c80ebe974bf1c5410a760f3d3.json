{"ast": null, "code": "import { __asyncValues, __awaiter } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n  if (input instanceof Observable) {\n    return input;\n  }\n\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return fromInteropObservable(input);\n    }\n\n    if (isArrayLike(input)) {\n      return fromArrayLike(input);\n    }\n\n    if (isPromise(input)) {\n      return fromPromise(input);\n    }\n\n    if (isAsyncIterable(input)) {\n      return fromAsyncIterable(input);\n    }\n\n    if (isIterable(input)) {\n      return fromIterable(input);\n    }\n\n    if (isReadableStreamLike(input)) {\n      return fromReadableStreamLike(input);\n    }\n  }\n\n  throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n  return new Observable(subscriber => {\n    const obs = obj[Symbol_observable]();\n\n    if (isFunction(obs.subscribe)) {\n      return obs.subscribe(subscriber);\n    }\n\n    throw new TypeError('Provided object does not correctly implement Symbol.observable');\n  });\n}\nexport function fromArrayLike(array) {\n  return new Observable(subscriber => {\n    for (let i = 0; i < array.length && !subscriber.closed; i++) {\n      subscriber.next(array[i]);\n    }\n\n    subscriber.complete();\n  });\n}\nexport function fromPromise(promise) {\n  return new Observable(subscriber => {\n    promise.then(value => {\n      if (!subscriber.closed) {\n        subscriber.next(value);\n        subscriber.complete();\n      }\n    }, err => subscriber.error(err)).then(null, reportUnhandledError);\n  });\n}\nexport function fromIterable(iterable) {\n  return new Observable(subscriber => {\n    for (const value of iterable) {\n      subscriber.next(value);\n\n      if (subscriber.closed) {\n        return;\n      }\n    }\n\n    subscriber.complete();\n  });\n}\nexport function fromAsyncIterable(asyncIterable) {\n  return new Observable(subscriber => {\n    process(asyncIterable, subscriber).catch(err => subscriber.error(err));\n  });\n}\nexport function fromReadableStreamLike(readableStream) {\n  return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\n\nfunction process(asyncIterable, subscriber) {\n  var asyncIterable_1, asyncIterable_1_1;\n\n  var e_1, _a;\n\n  return __awaiter(this, void 0, void 0, function* () {\n    try {\n      for (asyncIterable_1 = __asyncValues(asyncIterable); asyncIterable_1_1 = yield asyncIterable_1.next(), !asyncIterable_1_1.done;) {\n        const value = asyncIterable_1_1.value;\n        subscriber.next(value);\n\n        if (subscriber.closed) {\n          return;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return)) yield _a.call(asyncIterable_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n\n    subscriber.complete();\n  });\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/observable/innerFrom.js"], "names": ["__asyncValues", "__awaiter", "isArrayLike", "isPromise", "Observable", "isInteropObservable", "isAsyncIterable", "createInvalidObservableTypeError", "isIterable", "isReadableStreamLike", "readableStreamLikeToAsyncGenerator", "isFunction", "reportUnhandledError", "observable", "Symbol_observable", "innerFrom", "input", "fromInteropObservable", "fromArrayLike", "fromPromise", "fromAsyncIterable", "fromIterable", "fromReadableStreamLike", "obj", "subscriber", "obs", "subscribe", "TypeError", "array", "i", "length", "closed", "next", "complete", "promise", "then", "value", "err", "error", "iterable", "asyncIterable", "process", "catch", "readableStream", "asyncIterable_1", "asyncIterable_1_1", "e_1", "_a", "done", "e_1_1", "return", "call"], "mappings": "AAAA,SAASA,aAAT,EAAwBC,SAAxB,QAAyC,OAAzC;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,mBAAT,QAAoC,6BAApC;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,SAASC,gCAAT,QAAiD,gCAAjD;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,SAASC,oBAAT,EAA+BC,kCAA/B,QAAyE,8BAAzE;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,SAASC,oBAAT,QAAqC,8BAArC;AACA,SAASC,UAAU,IAAIC,iBAAvB,QAAgD,sBAAhD;AACA,OAAO,SAASC,SAAT,CAAmBC,KAAnB,EAA0B;AAC7B,MAAIA,KAAK,YAAYZ,UAArB,EAAiC;AAC7B,WAAOY,KAAP;AACH;;AACD,MAAIA,KAAK,IAAI,IAAb,EAAmB;AACf,QAAIX,mBAAmB,CAACW,KAAD,CAAvB,EAAgC;AAC5B,aAAOC,qBAAqB,CAACD,KAAD,CAA5B;AACH;;AACD,QAAId,WAAW,CAACc,KAAD,CAAf,EAAwB;AACpB,aAAOE,aAAa,CAACF,KAAD,CAApB;AACH;;AACD,QAAIb,SAAS,CAACa,KAAD,CAAb,EAAsB;AAClB,aAAOG,WAAW,CAACH,KAAD,CAAlB;AACH;;AACD,QAAIV,eAAe,CAACU,KAAD,CAAnB,EAA4B;AACxB,aAAOI,iBAAiB,CAACJ,KAAD,CAAxB;AACH;;AACD,QAAIR,UAAU,CAACQ,KAAD,CAAd,EAAuB;AACnB,aAAOK,YAAY,CAACL,KAAD,CAAnB;AACH;;AACD,QAAIP,oBAAoB,CAACO,KAAD,CAAxB,EAAiC;AAC7B,aAAOM,sBAAsB,CAACN,KAAD,CAA7B;AACH;AACJ;;AACD,QAAMT,gCAAgC,CAACS,KAAD,CAAtC;AACH;AACD,OAAO,SAASC,qBAAT,CAA+BM,GAA/B,EAAoC;AACvC,SAAO,IAAInB,UAAJ,CAAgBoB,UAAD,IAAgB;AAClC,UAAMC,GAAG,GAAGF,GAAG,CAACT,iBAAD,CAAH,EAAZ;;AACA,QAAIH,UAAU,CAACc,GAAG,CAACC,SAAL,CAAd,EAA+B;AAC3B,aAAOD,GAAG,CAACC,SAAJ,CAAcF,UAAd,CAAP;AACH;;AACD,UAAM,IAAIG,SAAJ,CAAc,gEAAd,CAAN;AACH,GANM,CAAP;AAOH;AACD,OAAO,SAAST,aAAT,CAAuBU,KAAvB,EAA8B;AACjC,SAAO,IAAIxB,UAAJ,CAAgBoB,UAAD,IAAgB;AAClC,SAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,MAAV,IAAoB,CAACN,UAAU,CAACO,MAAhD,EAAwDF,CAAC,EAAzD,EAA6D;AACzDL,MAAAA,UAAU,CAACQ,IAAX,CAAgBJ,KAAK,CAACC,CAAD,CAArB;AACH;;AACDL,IAAAA,UAAU,CAACS,QAAX;AACH,GALM,CAAP;AAMH;AACD,OAAO,SAASd,WAAT,CAAqBe,OAArB,EAA8B;AACjC,SAAO,IAAI9B,UAAJ,CAAgBoB,UAAD,IAAgB;AAClCU,IAAAA,OAAO,CACFC,IADL,CACWC,KAAD,IAAW;AACjB,UAAI,CAACZ,UAAU,CAACO,MAAhB,EAAwB;AACpBP,QAAAA,UAAU,CAACQ,IAAX,CAAgBI,KAAhB;AACAZ,QAAAA,UAAU,CAACS,QAAX;AACH;AACJ,KAND,EAMII,GAAD,IAASb,UAAU,CAACc,KAAX,CAAiBD,GAAjB,CANZ,EAOKF,IAPL,CAOU,IAPV,EAOgBvB,oBAPhB;AAQH,GATM,CAAP;AAUH;AACD,OAAO,SAASS,YAAT,CAAsBkB,QAAtB,EAAgC;AACnC,SAAO,IAAInC,UAAJ,CAAgBoB,UAAD,IAAgB;AAClC,SAAK,MAAMY,KAAX,IAAoBG,QAApB,EAA8B;AAC1Bf,MAAAA,UAAU,CAACQ,IAAX,CAAgBI,KAAhB;;AACA,UAAIZ,UAAU,CAACO,MAAf,EAAuB;AACnB;AACH;AACJ;;AACDP,IAAAA,UAAU,CAACS,QAAX;AACH,GARM,CAAP;AASH;AACD,OAAO,SAASb,iBAAT,CAA2BoB,aAA3B,EAA0C;AAC7C,SAAO,IAAIpC,UAAJ,CAAgBoB,UAAD,IAAgB;AAClCiB,IAAAA,OAAO,CAACD,aAAD,EAAgBhB,UAAhB,CAAP,CAAmCkB,KAAnC,CAA0CL,GAAD,IAASb,UAAU,CAACc,KAAX,CAAiBD,GAAjB,CAAlD;AACH,GAFM,CAAP;AAGH;AACD,OAAO,SAASf,sBAAT,CAAgCqB,cAAhC,EAAgD;AACnD,SAAOvB,iBAAiB,CAACV,kCAAkC,CAACiC,cAAD,CAAnC,CAAxB;AACH;;AACD,SAASF,OAAT,CAAiBD,aAAjB,EAAgChB,UAAhC,EAA4C;AACxC,MAAIoB,eAAJ,EAAqBC,iBAArB;;AACA,MAAIC,GAAJ,EAASC,EAAT;;AACA,SAAO9C,SAAS,CAAC,IAAD,EAAO,KAAK,CAAZ,EAAe,KAAK,CAApB,EAAuB,aAAa;AAChD,QAAI;AACA,WAAK2C,eAAe,GAAG5C,aAAa,CAACwC,aAAD,CAApC,EAAqDK,iBAAiB,GAAG,MAAMD,eAAe,CAACZ,IAAhB,EAA1B,EAAkD,CAACa,iBAAiB,CAACG,IAA1H,GAAiI;AAC7H,cAAMZ,KAAK,GAAGS,iBAAiB,CAACT,KAAhC;AACAZ,QAAAA,UAAU,CAACQ,IAAX,CAAgBI,KAAhB;;AACA,YAAIZ,UAAU,CAACO,MAAf,EAAuB;AACnB;AACH;AACJ;AACJ,KARD,CASA,OAAOkB,KAAP,EAAc;AAAEH,MAAAA,GAAG,GAAG;AAAER,QAAAA,KAAK,EAAEW;AAAT,OAAN;AAAyB,KATzC,SAUQ;AACJ,UAAI;AACA,YAAIJ,iBAAiB,IAAI,CAACA,iBAAiB,CAACG,IAAxC,KAAiDD,EAAE,GAAGH,eAAe,CAACM,MAAtE,CAAJ,EAAmF,MAAMH,EAAE,CAACI,IAAH,CAAQP,eAAR,CAAN;AACtF,OAFD,SAGQ;AAAE,YAAIE,GAAJ,EAAS,MAAMA,GAAG,CAACR,KAAV;AAAkB;AACxC;;AACDd,IAAAA,UAAU,CAACS,QAAX;AACH,GAlBe,CAAhB;AAmBH", "sourcesContent": ["import { __asyncValues, __awaiter } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n    if (input instanceof Observable) {\n        return input;\n    }\n    if (input != null) {\n        if (isInteropObservable(input)) {\n            return fromInteropObservable(input);\n        }\n        if (isArrayLike(input)) {\n            return fromArrayLike(input);\n        }\n        if (isPromise(input)) {\n            return fromPromise(input);\n        }\n        if (isAsyncIterable(input)) {\n            return fromAsyncIterable(input);\n        }\n        if (isIterable(input)) {\n            return fromIterable(input);\n        }\n        if (isReadableStreamLike(input)) {\n            return fromReadableStreamLike(input);\n        }\n    }\n    throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n    return new Observable((subscriber) => {\n        const obs = obj[Symbol_observable]();\n        if (isFunction(obs.subscribe)) {\n            return obs.subscribe(subscriber);\n        }\n        throw new TypeError('Provided object does not correctly implement Symbol.observable');\n    });\n}\nexport function fromArrayLike(array) {\n    return new Observable((subscriber) => {\n        for (let i = 0; i < array.length && !subscriber.closed; i++) {\n            subscriber.next(array[i]);\n        }\n        subscriber.complete();\n    });\n}\nexport function fromPromise(promise) {\n    return new Observable((subscriber) => {\n        promise\n            .then((value) => {\n            if (!subscriber.closed) {\n                subscriber.next(value);\n                subscriber.complete();\n            }\n        }, (err) => subscriber.error(err))\n            .then(null, reportUnhandledError);\n    });\n}\nexport function fromIterable(iterable) {\n    return new Observable((subscriber) => {\n        for (const value of iterable) {\n            subscriber.next(value);\n            if (subscriber.closed) {\n                return;\n            }\n        }\n        subscriber.complete();\n    });\n}\nexport function fromAsyncIterable(asyncIterable) {\n    return new Observable((subscriber) => {\n        process(asyncIterable, subscriber).catch((err) => subscriber.error(err));\n    });\n}\nexport function fromReadableStreamLike(readableStream) {\n    return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\nfunction process(asyncIterable, subscriber) {\n    var asyncIterable_1, asyncIterable_1_1;\n    var e_1, _a;\n    return __awaiter(this, void 0, void 0, function* () {\n        try {\n            for (asyncIterable_1 = __asyncValues(asyncIterable); asyncIterable_1_1 = yield asyncIterable_1.next(), !asyncIterable_1_1.done;) {\n                const value = asyncIterable_1_1.value;\n                subscriber.next(value);\n                if (subscriber.closed) {\n                    return;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return)) yield _a.call(asyncIterable_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        subscriber.complete();\n    });\n}\n"]}, "metadata": {}, "sourceType": "module"}
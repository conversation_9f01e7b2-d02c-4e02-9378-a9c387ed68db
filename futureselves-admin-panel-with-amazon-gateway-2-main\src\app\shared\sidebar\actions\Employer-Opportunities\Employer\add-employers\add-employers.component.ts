import { <PERSON><PERSON>nent, OnInit ,ViewChildren, QueryList,ViewChild, ElementRef} from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import {
  ActivatedRoute,
  Router,
  Params,
  NavigationEnd,
  RouterEvent,
} from '@angular/router';
import { filter } from 'rxjs/operators';
import { DatePipe } from '@angular/common';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormArray,
  FormControl,
  AbstractControl,
  ValidatorFn,
  ValidationErrors,
  ReactiveFormsModule
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { HttpClient } from '@angular/common/http';
import { FileValidator } from 'src/app/shared/validators/fileValidator.validator';
import { ImageCroppedEvent } from 'ngx-image-cropper';


// function wordLimitValidator(maxWords: number) {
//   return (control: AbstractControl): { [key: string]: any } | null => {
//     if (control.value) {
//       const words = control.value.trim().split(/\s+/);
//       if (words.length > maxWords) {
//         console.log('maxwords', maxWords);
//         return { wordLimitExceeded: true, wordCount: words.length };
//       }
//     }
//     return null;
//   };
// }

// function nonNegativeValidator(
//   control: FormControl
// ): { [key: string]: boolean } | null {
//   console.log('Control : ', control);
//   const value = control.value;
//   if (value < 0 || value > 24) {
//     return { negativeValue: true };
//   }
//   return null;
// }
@Component({
  selector: 'app-add-employers',
  templateUrl: './add-employers.component.html',
  styleUrls: ['./add-employers.component.scss']
})
export class AddEmployersComponent implements OnInit {
  @ViewChild('audioInput') audioInput!: ElementRef;
  jobForm: FormGroup;
  imageSrc: any;
  addNewCompanyForm: FormGroup;
  ExistingSchemesForm: FormGroup;
  p: number = 1;
  user: any;
  imageName: any;
  CompanyList: any;
  SectorList: any;
  CO_id: any;
  hideHeader = false;
  viewInsight = true;
  showForm = false;
  queryParam: any;
  term: string;
  submitted = false;
  title = 'Add New';
  isReadonly = false;
  fileName: File | undefined;
  baseUrl: string = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  imagedp: string | null = null; // Assuming this is used to display the uploaded image
  // baseimage:any = 'https://voxpod.s3.eu-west-2.amazonaws.com/2f1010ae38-7e24-4fe6-a771-c5363510f7c65_dp.png ';
  selectedRecord: any;
  title1: string;
  ExistingScheme: any;
  companyId: any;
  minDate: string;
  sectorID: any;
  companyLogo: any;
  RoleList: any;
  roleId: Promise<unknown>;
  minStartDate: string;
  invalidDates: Set<string> = new Set<string>();
  JobList: any;
  checkMinDate: string;
  // scheme: any;
  selectedAudioFile: any;
  deleteId: any;
  toEditInsight: any;
  audioFiles: File[] = [];
  characterCount: number = 0;
  isPlaying: boolean[] = []; // Array to track playback state
  audioUrls: string[] = []; // Array to store audio URLs
  @ViewChildren('audioPlayer') audioPlayers: QueryList<ElementRef<HTMLAudioElement>>;
  showOtherTypeInput: boolean = false;
  employerData: any;
  ExistingLogo: any;
  existingAudioNames: string[] = [];
  dataToPost: any;
  audioFileUrls: string[] = []; 
  isCropperVisible = false;
  imgChangeEvt: any="";

  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private toastr: ToastrService,
    private datePipe: DatePipe,
    private route:ActivatedRoute
  ) {

    this.addNewCompanyForm = this.formBuilder.group({
      CO_companyName: ['', [Validators.required]],
      CO_logo: [null, [Validators.required]],
      CO_location: ['', [Validators.required]],
      CO_type: ['', [Validators.required]],
      CO_otherType: [''],
      CO_founded: ['', [Validators.required]],
      CO_sectorId: ['', [Validators.required]],
      CO_about: ['', [Validators.required, Validators.maxLength(200)]],
      CO_website: ['', [Validators.required,Validators.pattern('https?://.+')]],
      CO_size: ['', [Validators.required]],
      CO_HrInsights:  this.formBuilder.array([this.createInsight()])
    });


    this.route.queryParams.subscribe(params => {
      if(params){
      this.CO_id = params['CO_id'];
      console.log(this.CO_id);
    }
    });

    const state = this.router.getCurrentNavigation()?.extras?.state; //State values
    if (state) {
      (this.title = state.title);
      (this.employerData = state.employerData);
      console.log("employerData: ",this.employerData);
      
    } else {
      this.router.navigate([`actions/employer-opportunities`]);
    }


   }

  ngOnInit(): void {
    this.getSectorTitles();
    if(this.title==='Edit'){
      this.getCompanyById(this.CO_id);
    }
  }
  
  async getCompanyById(companyId: any): Promise<void> {
    try {
      const res = await this.dataTransferService.getCompanyById(companyId).toPromise();
      this.employerData = res;
      console.log("this.employerData", res);
      this.populateForm(this.employerData);
    } catch (error) {
      console.log("Error", error);
      this.toastr.error("Unable to fetch data");
    }
  }

  populateForm(data: any) {
    console.log("Data to patch to edit",data);
    const logoControl = this.addNewCompanyForm.get('CO_logo');
    if (logoControl) {
      logoControl.clearValidators();
      logoControl.updateValueAndValidity();
    } else {
      console.log("CO_logo control not found");
    }

    // this.ExistingLogo = data?.CO_logo.substring(
    //   data.CO_logo.lastIndexOf('/') + 1
    // );

    this.addNewCompanyForm.patchValue({
      CO_companyName: data.CO_companyName,
      CO_location: data.CO_location,
      CO_type: data.CO_type,
      CO_founded: data.CO_founded,
      CO_sectorId: data.CO_sectorId,
      CO_about: data.CO_about,
      CO_website: data.CO_website,
      CO_size: data.CO_size,
      CO_id:data.CO_id
    });
  
   // Define the valid options
const validOptions = [
  'Private Limited Company(LTD)',
  'Public Limited Company(PLC)',
  'Partnership',
  'Non-Profit Organization'
];

if (validOptions.includes(data.CO_type)) {
  this.addNewCompanyForm.get('CO_type')?.setValue(data.CO_type);
  this.showOtherTypeInput = false; 
} else {
  this.addNewCompanyForm.get('CO_type')?.setValue('Other');
  this.showOtherTypeInput = true; // Show the 'Other' input
  this.addNewCompanyForm.get('CO_otherType')?.setValue(data.CO_type);
}

  
    this.imageSrc = data.CO_logo;
    
    if(data.CO_HrInsights){
      
    const hrInsightsArray = this.addNewCompanyForm.get('CO_HrInsights') as FormArray;
    hrInsightsArray.clear();
  
    data.CO_HrInsights.forEach((insight: any) => {
      const insightFormGroup = this.formBuilder.group({
        HRI_title: [insight.HRI_title, [Validators.required]],
        HRI_name: [insight.HRI_name, [Validators.required]],
        HRI_position: [insight.HRI_position, [Validators.required]],
        HRI_link: [insight.HRI_link]
      });
      hrInsightsArray.push(insightFormGroup);
    });
  
    // Initialize audio URLs and playback states
    // this.audioUrls = data.CO_HrInsights.map((insight: any) => insight.HRI_link);
    // this.isPlaying = new Array(data.CO_HrInsights.length).fill(false);
    this.audioUrls = data.CO_HrInsights.map((insight: any) => insight.HRI_link);
    this.isPlaying = new Array(data.CO_HrInsights.length).fill(false);
    this.existingAudioNames = data?.CO_HrInsights.map((insight: any) => insight?.HRI_link?.substring(insight.HRI_link.lastIndexOf('/') + 1));
  }
}

 
  

  get insightFormArray(): FormArray {
    return this.addNewCompanyForm.get('CO_HrInsights') as FormArray;
  }

  onTextChange(event: Event) {
    const textarea = event.target as HTMLTextAreaElement;
    this.characterCount = textarea.value.length;

    if (this.characterCount > 500) {
      this.addNewCompanyForm.get('CO_about')?.setErrors({ maxlength: true });
    } else {
      this.addNewCompanyForm.get('CO_about')?.setErrors(null);
    }
  }

  createInsight(): FormGroup {
    return this.formBuilder.group({
      HRI_title: ['', [Validators.required]],
      HRI_name: ['', [Validators.required]],
      HRI_position: ['', [Validators.required]],
      HRI_link: [null]
    });
    this.isPlaying.push(false); // Initialize playback state for new row
    this.audioUrls.push(''); // Initialize empty URL for new row
  }
  
  addInsight() {
    this.insightFormArray.push(this.createInsight());
  }

  removeInsight(index: number) {
    this.insightFormArray.removeAt(index);
    this.audioUrls.splice(index, 1); // Remove URL for deleted row
    this.isPlaying.splice(index, 1); // Remove playback state for deleted row
    this.audioFiles.splice(index, 1); // Remove file object for deleted row
  }

  toggleAudio(index: number) {
    const audioElements = this.audioPlayers.toArray();
    const audioElement = audioElements[index].nativeElement;

    // Stop all other audio
    this.stopAllAudio(index);

    // Toggle play/pause for the current audio element
    if (this.isPlaying[index]) {
      audioElement.pause();
    } else {
      audioElement.src = this.audioUrls[index];
      audioElement.play();
    }

    // Update the playback state
    this.isPlaying[index] = !this.isPlaying[index];
  }

  stopAllAudio(currentIndex: number) {
    this.audioPlayers.forEach((audioPlayer, index) => {
      if (index !== currentIndex) {
        audioPlayer.nativeElement.pause();
        this.isPlaying[index] = false;
      }
    });
  }

  stopAudio(index: number) {
    const audioElements = this.audioPlayers.toArray();
    const audioElement = audioElements[index].nativeElement;
  
    audioElement.pause();
    audioElement.currentTime = 0; // Reset to the beginning
    this.isPlaying[index] = false;
  }
  

  onFileSelected(event: any) {
    let selectedFile = event.target.files[0];
    //to preview image and take file in imageName to pass in upload api
    if (event.target.files.length === 0) {
      // Reset both imageName and imageSrc when no file is selected
      this.imageName = null;
      this.imageSrc = null;
      return;
    }

    const newFileName = FileValidator.addTimestamp(selectedFile.name);
    this.imageName = new File([selectedFile], newFileName, { type: selectedFile.type });

    if(this.imageName){
    const formControl=this.addNewCompanyForm.get('CO_logo');
    formControl?.setValidators(FileValidator.fileSizeValidator(2048, this.imageName));
    formControl?.updateValueAndValidity();
  }

    const fileType = this.imageName.type.split('/')[0];
    const fileExtension = this.imageName.name.split('.').pop()?.toLowerCase();
    if (fileType !== 'image' || fileExtension === 'svg') {
      event.target.value = '';
      this.toastr.info('Please select an image file (excluding SVG).');
      this.imageName = null;
      this.imageSrc = null;
      return;
    }


    if (this.imageName && fileType == 'image') {
      const reader = new FileReader();
      const img = new Image();
      reader.onload = (e) => {
        this.imageSrc = e.target?.result as string | ArrayBuffer;
        if (!this.addNewCompanyForm.get('CO_logo')?.errors?.fileSizeValidator) {
          this.checkAspectRatio(img, 'CO_logo');
          this.imgChangeEvt = { target: { files: [this.imageName] } };
        }
      };;
      reader.readAsDataURL(this.imageName);
    } else {
      this.imageSrc = null; // Reset imageSrc if no file selected
    }
    console.log('imageName', this.imageName);
  }

  checkAspectRatio(image: HTMLImageElement,controlName:any) {
    const aspectRatio = image.width / image.height;
    const control=this.addNewCompanyForm.get(controlName);
    if (aspectRatio !== 1) {
      // this.toastr.warning('The image must have a 1:1 aspect ratio. Please select a square image.', 'Invalid Aspect Ratio');
      control?.setErrors({fileAspectRatioValidator:true})
    }else{
      control?.setErrors(null);
    }
  }
  
  uploadLogoUrl(): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.imageName) {
        reject('Please select an image.');
        return;
      }
  
      console.log('Uploading image:', this.imageName);
  
      this.dataTransferService.uploadurl(this.imageName).subscribe(
        (res: any) => {
          const fileUrl = this.baseUrl + this.imageName.name; // Ensure you're concatenating correctly
          resolve(fileUrl);
        },
        (error) => {
          reject('Error uploading image: ' + error.message || error);
        }
      );
    });
  }
  


  onAudioSelected(event: any, index: number): void {
    let audiofile: File | null;
    const selectedFile = event.target.files[0];
    
    if (selectedFile) {
      const newFileName = FileValidator.addTimestamp(selectedFile.name);
      audiofile = new File([selectedFile], newFileName, { type: selectedFile.type });
    } else {
      audiofile = null;
    }
  
    if (!audiofile) {
      this.audioUrls[index] = '';
      this.stopAudio(index);
      return;
    }
  
    const audiofileType = audiofile.type.split('/')[0]; 
    if (audiofileType !== 'audio') {
      event.target.value = ''; // Clear the input
      this.toastr.info('Please select an audio file.');
      return;
    }
  
    // Store the file object in the array for later upload
    this.audioFiles[index] = audiofile;
    console.log('Audio file :', audiofile);
  
    const reader = new FileReader();
    reader.onload = () => {
      // Store audio URL
      this.audioUrls[index] = reader.result as string;
    };
    
    reader.onerror = () => {
      console.error('Error reading audio file');
      this.toastr.error('Error reading audio file.');
    };
  
    reader.readAsDataURL(audiofile);
  }

  
  
uploadAudioFiles(files: File[]): Promise<string[]> {
  const uploadPromises = files.map((file, index) => this.uploadSingleAudioFile(file, index));
  return Promise.all(uploadPromises);
}

// Updated uploadSingleAudioFile function
uploadSingleAudioFile(file: File, index: number): Promise<string> {
  return new Promise((resolve, reject) => {
    this.dataTransferService.uploadurl(file).subscribe(
      (res: any) => {
        console.log('Upload successful', file.name);
        const fileUrl = this.baseUrl + file.name;
        this.audioUrls[index] = fileUrl; 
        resolve(fileUrl);
      },
      (error: any) => {
        console.error('Upload error', error);
        this.toastr.error('Failed to upload audio file');
        reject(error);
      }
    );
  });
}

// uploadAudioFiles(files: File[]): Promise<string[]> {
//   const uploadPromises = files.map(file => this.uploadSingleAudioFile(file));
//   return Promise.all(uploadPromises);
// }

// uploadSingleAudioFile(file: File): Promise<string> {
//   return new Promise((resolve, reject) => {
//     this.dataTransferService.uploadurl(file).subscribe(
//       (res: any) => {
//         console.log('Upload successful', file.name);
//         const fileUrl = this.baseUrl + file.name;
//         resolve(fileUrl);
//       },
//       (error: any) => {
//         console.error('Upload error', error);
//         this.toastr.error('Failed to upload audio file');
//         reject(error);
//       }
//     );
//   });
// }

onTypeChange(event: Event): void {
  const selectElement = event.target as HTMLSelectElement;
  this.showOtherTypeInput = selectElement.value === 'Other';
}

onSubmit(){
if(this.title==='Edit'){
this.updateEmployer();
}else{
  this.addEmployer();
}
}

updateEmployer() {
  if(!this.addNewCompanyForm.get('CO_logo')?.errors?.fileAspectRatioValidator){

  const logoControl = this.addNewCompanyForm.get('CO_logo');
  
  if (logoControl) {
    logoControl.clearValidators();
    logoControl.updateValueAndValidity();
    console.log("Removed required validator from CO_logo");
  } else {
    console.log("CO_logo control not found");
  }

  if (this.addNewCompanyForm.invalid) {
    Object.keys(this.addNewCompanyForm.controls).forEach(name => {
      const control = this.addNewCompanyForm.get(name);
      if (control?.invalid) {
        console.log(`Invalid control: ${name}, Errors:`, control.errors);
      }
    });
    this.toastr.info('Please fill all required fields correctly');
    return;
  }

  const uploadLogo = this.addNewCompanyForm.get('CO_logo')?.value ? this.uploadLogoUrl() : Promise.resolve(this.employerData.CO_logo);
  
  this.ngxSpinnerService.show('globalSpinner');
  
  uploadLogo.then((fileUrl: string) => {
    return this.uploadAudioFiles(this.audioFiles).then((audioUrls: string[]) => {
      console.log("Uploaded audio files:", this.audioFiles);
      
      this.insightFormArray.controls.forEach((control: AbstractControl, index: number) => {
        (control as FormGroup).get('HRI_link')?.setValue(this.audioUrls[index]);
      });

      this.dataToPost = {
        CO_id: this.employerData.CO_id,
        CO_companyName: this.addNewCompanyForm.get('CO_companyName')?.value,
        CO_logo: fileUrl,
        CO_location: this.addNewCompanyForm.get('CO_location')?.value,
        CO_type: this.addNewCompanyForm.get('CO_type')?.value === 'Other' ? this.addNewCompanyForm.get('CO_otherType')?.value : this.addNewCompanyForm.get('CO_type')?.value,
        CO_founded: this.addNewCompanyForm.get('CO_founded')?.value,
        CO_sectorId: this.addNewCompanyForm.get('CO_sectorId')?.value,
        CO_about: this.addNewCompanyForm.get('CO_about')?.value,
        CO_website: this.addNewCompanyForm.get('CO_website')?.value,
        CO_size: this.addNewCompanyForm.get('CO_size')?.value,
        CO_HrInsights: this.addNewCompanyForm.get('CO_HrInsights')?.value
      };

      console.log("Data to update...", this.dataToPost);
      return this.dataTransferService.updateEmployer(this.dataToPost).toPromise();
    });
  }).then((res: any) => {
    this.ngxSpinnerService.hide('globalSpinner');
    if (res.statusCode === 200) {
      this.toastr.success('Employer updated successfully.');
      this.getAllCompany();
      this.router.navigate(['actions/employer-opportunities']);
    } else {
      this.toastr.error('Something went wrong.');
    }
  }).catch((error: any) => {
    this.ngxSpinnerService.hide('globalSpinner');
    const errorMessage = error === 'Audio file upload failed.' ? error : 'Error updating employer.';
    this.toastr.error(errorMessage);
  });
}else{
  this.toastr.info("Please resize the image");
}

}


addEmployer() {
  console.log('Formdata', this.addNewCompanyForm.value);
  if (this.addNewCompanyForm.invalid) {
    this.toastr.info('Please fill all required fields');
    return;
  }
  this.ngxSpinnerService.show('globalSpinner');
  this.uploadLogoUrl().then(() => {
    const fileUrl = this.baseUrl + this.imageName.name; // Construct the file URL as needed

    // Upload all audio files and get their URLs
    this.uploadAudioFiles(this.audioFiles).then((audioUrls: string[]) => {
      console.log("this.audioFiles",this.audioFiles);
      
      this.insightFormArray.controls.forEach((control: AbstractControl, index: number) => {
        const group = control as FormGroup;
        group.get('HRI_link')?.setValue(audioUrls[index]); // Patch the URL from the array
      });

      // Manually construct the data object
      const data = {
        CO_companyName: this.addNewCompanyForm.get('CO_companyName')?.value,
        CO_logo: fileUrl, // Add the file URL here
        CO_location: this.addNewCompanyForm.get('CO_location')?.value,
        CO_type: this.addNewCompanyForm.get('CO_type')?.value === 'Other' ? this.addNewCompanyForm.get('CO_otherType')?.value : this.addNewCompanyForm.get('CO_type')?.value,
        CO_founded: this.addNewCompanyForm.get('CO_founded')?.value,
        CO_sectorId: this.addNewCompanyForm.get('CO_sectorId')?.value,
        CO_about: this.addNewCompanyForm.get('CO_about')?.value,
        CO_website: this.addNewCompanyForm.get('CO_website')?.value,
        CO_size: this.addNewCompanyForm.get('CO_size')?.value,
        CO_HrInsights: this.addNewCompanyForm.get('CO_HrInsights')?.value
      };

      console.log('ADD Employer DATA', data);
      

      this.dataTransferService.addEmployer(data).subscribe(
        (res: any) => {
          if (res.statusCode==201) {
            this.ngxSpinnerService.hide('globalSpinner');
            console.log('Data posted successfully:', res);
            this.toastr.success('Employer added successfully.');
            this.getAllCompany();
            this.router.navigate(['actions/employer-opportunities']);
            console.log("Success");
          }else{
            this.ngxSpinnerService.hide('globalSpinner');
            this.toastr.error('Something Went Wrong');
          }
        },
        (error) => {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Error posting data:', error);
          this.toastr.error('Something Went Wrong');
        }
      );
    }).catch((error: any) => {
      this.ngxSpinnerService.hide('globalSpinner');
      console.error('Error uploading audio files:', error);
      this.toastr.error('Audio file upload failed');
    });
  }).catch((error: any) => {
    this.ngxSpinnerService.hide('globalSpinner');
    console.error('Error uploading logo:', error);
    this.toastr.error('Logo upload failed');
  });
}


  getAllCompany() {
    this.dataTransferService.getAllCompany().subscribe({
      next: (res: any) => {
        if (res.statusCode === 200) {
          this.CompanyList = res.data;
          this.ngxSpinnerService.hide('globalSpinner');
          console.log('GetAllCompany', this.CompanyList);
        } else {
          this.ngxSpinnerService.hide('globalSpinner');
          console.error('Failed to fetch companies. Status:', res.status);
        }
      },
      error: (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.error('Error occurred while fetching companies:', error);
      },
    });
  }

  getSectorTitles() {
    // In Add company form - To sector dropdown
    this.dataTransferService.getSectorTitles().subscribe((res: any) => {
      if ((res.statusCode = 200)) {
        this.SectorList = res.data;
        console.log('Sectors', this.SectorList);
      } else {
        console.error('Failed to fetch sectors. Status:', res.status);
      }
    },(error:any)=>{
      this.ngxSpinnerService.hide('globalSpinner');
      console.log("error",error);
      
    });
  }

  showCropper(controlName:string) {
      this.isCropperVisible = true;
  }

  hideCropper(controlName:string) {
      this.isCropperVisible = false;
  }

  cropImg(e:ImageCroppedEvent){
    this.imageSrc=e.base64
  }

  initCropper(){

  }

  imgLoad(){

  }

  imgFailed(){
    this.toastr.error("Image Failed to show");
  }

  saveCroppedImage(controlName: string) {
    const addTimestamp = (fileName: string) => {
      const currentTimestamp = new Date().getTime();
      const nameWithoutExtension = fileName.split('.').slice(0, -1).join('.');
      const extension = fileName.split('.').pop();
  
      const cleanedName = nameWithoutExtension.replace(/_\d{13}$/, '');
  
      return `${cleanedName}_${currentTimestamp}.${extension}`;
    };
  
      if (this.imageSrc && this.imageName) {
        // Convert cropped image to Blob
        const blob = this.dataURItoBlob(this.imageSrc);
        // Add or replace timestamp in image name
        const newFileName = addTimestamp(this.imageName.name);
        this.imageName = new File([blob], newFileName, { type: this.imageName.type });
        console.log("Cropped Image", this.imageName);
  
        // Update imgChangeEvt with the new cropped image
        this.imgChangeEvt = { target: { files: [this.imageName] } };
        const fileControl = this.addNewCompanyForm.get('CO_logo');
        fileControl?.clearValidators();
        fileControl?.updateValueAndValidity();
        this.hideCropper('CO_logo');
      }
   
  }
  
  dataURItoBlob(dataURI: string): Blob {
    const byteString = atob(dataURI.split(',')[1]);
    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const uint8Array = new Uint8Array(arrayBuffer);
  
    for (let i = 0; i < byteString.length; i++) {
      uint8Array[i] = byteString.charCodeAt(i);
    }
  
    return new Blob([uint8Array], { type: mimeString });
  }
}


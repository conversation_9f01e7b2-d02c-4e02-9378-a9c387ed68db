{"ast": null, "code": "import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan, ...otherArgs) {\n  var _a, _b;\n\n  const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  const windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  const maxWindowSize = otherArgs[1] || Infinity;\n  return operate((source, subscriber) => {\n    let windowRecords = [];\n    let restartOnClose = false;\n\n    const closeWindow = record => {\n      const {\n        window,\n        subs\n      } = record;\n      window.complete();\n      subs.unsubscribe();\n      arrRemove(windowRecords, record);\n      restartOnClose && startWindow();\n    };\n\n    const startWindow = () => {\n      if (windowRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const window = new Subject();\n        const record = {\n          window,\n          subs,\n          seen: 0\n        };\n        windowRecords.push(record);\n        subscriber.next(window.asObservable());\n        executeSchedule(subs, scheduler, () => closeWindow(record), windowTimeSpan);\n      }\n    };\n\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n    } else {\n      restartOnClose = true;\n    }\n\n    startWindow();\n\n    const loop = cb => windowRecords.slice().forEach(cb);\n\n    const terminate = cb => {\n      loop(({\n        window\n      }) => cb(window));\n      cb(subscriber);\n      subscriber.unsubscribe();\n    };\n\n    source.subscribe(new OperatorSubscriber(subscriber, value => {\n      loop(record => {\n        record.window.next(value);\n        maxWindowSize <= ++record.seen && closeWindow(record);\n      });\n    }, () => terminate(consumer => consumer.complete()), err => terminate(consumer => consumer.error(err))));\n    return () => {\n      windowRecords = null;\n    };\n  });\n}", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/rxjs/dist/esm/internal/operators/windowTime.js"], "names": ["Subject", "asyncScheduler", "Subscription", "operate", "OperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "popScheduler", "executeSchedule", "windowTime", "windowTimeSpan", "otherArgs", "_a", "_b", "scheduler", "windowCreationInterval", "maxWindowSize", "Infinity", "source", "subscriber", "windowRecords", "restartOnClose", "closeWindow", "record", "window", "subs", "complete", "unsubscribe", "startWindow", "add", "seen", "push", "next", "asObservable", "loop", "cb", "slice", "for<PERSON>ach", "terminate", "subscribe", "value", "consumer", "err", "error"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,cAAT,QAA+B,oBAA/B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,kBAAT,QAAmC,sBAAnC;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,SAASC,YAAT,QAA6B,cAA7B;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,SAASC,UAAT,CAAoBC,cAApB,EAAoC,GAAGC,SAAvC,EAAkD;AACrD,MAAIC,EAAJ,EAAQC,EAAR;;AACA,QAAMC,SAAS,GAAG,CAACF,EAAE,GAAGL,YAAY,CAACI,SAAD,CAAlB,MAAmC,IAAnC,IAA2CC,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgEV,cAAlF;AACA,QAAMa,sBAAsB,GAAG,CAACF,EAAE,GAAGF,SAAS,CAAC,CAAD,CAAf,MAAwB,IAAxB,IAAgCE,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqD,IAApF;AACA,QAAMG,aAAa,GAAGL,SAAS,CAAC,CAAD,CAAT,IAAgBM,QAAtC;AACA,SAAOb,OAAO,CAAC,CAACc,MAAD,EAASC,UAAT,KAAwB;AACnC,QAAIC,aAAa,GAAG,EAApB;AACA,QAAIC,cAAc,GAAG,KAArB;;AACA,UAAMC,WAAW,GAAIC,MAAD,IAAY;AAC5B,YAAM;AAAEC,QAAAA,MAAF;AAAUC,QAAAA;AAAV,UAAmBF,MAAzB;AACAC,MAAAA,MAAM,CAACE,QAAP;AACAD,MAAAA,IAAI,CAACE,WAAL;AACArB,MAAAA,SAAS,CAACc,aAAD,EAAgBG,MAAhB,CAAT;AACAF,MAAAA,cAAc,IAAIO,WAAW,EAA7B;AACH,KAND;;AAOA,UAAMA,WAAW,GAAG,MAAM;AACtB,UAAIR,aAAJ,EAAmB;AACf,cAAMK,IAAI,GAAG,IAAItB,YAAJ,EAAb;AACAgB,QAAAA,UAAU,CAACU,GAAX,CAAeJ,IAAf;AACA,cAAMD,MAAM,GAAG,IAAIvB,OAAJ,EAAf;AACA,cAAMsB,MAAM,GAAG;AACXC,UAAAA,MADW;AAEXC,UAAAA,IAFW;AAGXK,UAAAA,IAAI,EAAE;AAHK,SAAf;AAKAV,QAAAA,aAAa,CAACW,IAAd,CAAmBR,MAAnB;AACAJ,QAAAA,UAAU,CAACa,IAAX,CAAgBR,MAAM,CAACS,YAAP,EAAhB;AACAzB,QAAAA,eAAe,CAACiB,IAAD,EAAOX,SAAP,EAAkB,MAAMQ,WAAW,CAACC,MAAD,CAAnC,EAA6Cb,cAA7C,CAAf;AACH;AACJ,KAdD;;AAeA,QAAIK,sBAAsB,KAAK,IAA3B,IAAmCA,sBAAsB,IAAI,CAAjE,EAAoE;AAChEP,MAAAA,eAAe,CAACW,UAAD,EAAaL,SAAb,EAAwBc,WAAxB,EAAqCb,sBAArC,EAA6D,IAA7D,CAAf;AACH,KAFD,MAGK;AACDM,MAAAA,cAAc,GAAG,IAAjB;AACH;;AACDO,IAAAA,WAAW;;AACX,UAAMM,IAAI,GAAIC,EAAD,IAAQf,aAAa,CAACgB,KAAd,GAAsBC,OAAtB,CAA8BF,EAA9B,CAArB;;AACA,UAAMG,SAAS,GAAIH,EAAD,IAAQ;AACtBD,MAAAA,IAAI,CAAC,CAAC;AAAEV,QAAAA;AAAF,OAAD,KAAgBW,EAAE,CAACX,MAAD,CAAnB,CAAJ;AACAW,MAAAA,EAAE,CAAChB,UAAD,CAAF;AACAA,MAAAA,UAAU,CAACQ,WAAX;AACH,KAJD;;AAKAT,IAAAA,MAAM,CAACqB,SAAP,CAAiB,IAAIlC,kBAAJ,CAAuBc,UAAvB,EAAoCqB,KAAD,IAAW;AAC3DN,MAAAA,IAAI,CAAEX,MAAD,IAAY;AACbA,QAAAA,MAAM,CAACC,MAAP,CAAcQ,IAAd,CAAmBQ,KAAnB;AACAxB,QAAAA,aAAa,IAAI,EAAEO,MAAM,CAACO,IAA1B,IAAkCR,WAAW,CAACC,MAAD,CAA7C;AACH,OAHG,CAAJ;AAIH,KALgB,EAKd,MAAMe,SAAS,CAAEG,QAAD,IAAcA,QAAQ,CAACf,QAAT,EAAf,CALD,EAKuCgB,GAAD,IAASJ,SAAS,CAAEG,QAAD,IAAcA,QAAQ,CAACE,KAAT,CAAeD,GAAf,CAAf,CALxD,CAAjB;AAMA,WAAO,MAAM;AACTtB,MAAAA,aAAa,GAAG,IAAhB;AACH,KAFD;AAGH,GA/Ca,CAAd;AAgDH", "sourcesContent": ["import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { OperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan, ...otherArgs) {\n    var _a, _b;\n    const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n    const windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    const maxWindowSize = otherArgs[1] || Infinity;\n    return operate((source, subscriber) => {\n        let windowRecords = [];\n        let restartOnClose = false;\n        const closeWindow = (record) => {\n            const { window, subs } = record;\n            window.complete();\n            subs.unsubscribe();\n            arrRemove(windowRecords, record);\n            restartOnClose && startWindow();\n        };\n        const startWindow = () => {\n            if (windowRecords) {\n                const subs = new Subscription();\n                subscriber.add(subs);\n                const window = new Subject();\n                const record = {\n                    window,\n                    subs,\n                    seen: 0,\n                };\n                windowRecords.push(record);\n                subscriber.next(window.asObservable());\n                executeSchedule(subs, scheduler, () => closeWindow(record), windowTimeSpan);\n            }\n        };\n        if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n            executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n        }\n        else {\n            restartOnClose = true;\n        }\n        startWindow();\n        const loop = (cb) => windowRecords.slice().forEach(cb);\n        const terminate = (cb) => {\n            loop(({ window }) => cb(window));\n            cb(subscriber);\n            subscriber.unsubscribe();\n        };\n        source.subscribe(new OperatorSubscriber(subscriber, (value) => {\n            loop((record) => {\n                record.window.next(value);\n                maxWindowSize <= ++record.seen && closeWindow(record);\n            });\n        }, () => terminate((consumer) => consumer.complete()), (err) => terminate((consumer) => consumer.error(err))));\n        return () => {\n            windowRecords = null;\n        };\n    });\n}\n"]}, "metadata": {}, "sourceType": "module"}
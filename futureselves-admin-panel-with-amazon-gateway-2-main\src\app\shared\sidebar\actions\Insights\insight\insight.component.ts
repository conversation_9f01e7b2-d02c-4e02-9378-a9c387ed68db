import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  Validators,
} from '@angular/forms';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, ActivatedRoute } from '@angular/router';
import { Constants } from 'src/app/config/constants';
import { HttpClient } from '@angular/common/http';
import { NgxSpinnerService } from 'ngx-spinner';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-soundbite',
  templateUrl: './insight.component.html',
  styleUrls: ['./insight.component.scss'],
})
export class InsightComponent implements OnInit {
  soundList: any[] = [];
  tempSoundList: any[] = [];
  p: number = 1; // Current page
  term: any;
  isReadonly = false;
  title = 'View';
  // DegreeList: any;
  industryList: any;
  TagList: any;
  selectsoundurl: any;
  selectedRecord: any;
  Tagtitle: any;
  filtervalue: any;
  selectedvalue: any;
  baseurl: any = 'https://voxpod.s3.eu-west-2.amazonaws.com/';
  userData: any;

  constructor(
    private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private domSanitizer: DomSanitizer,
    private router: Router,
    private formModule: FormsModule,
    private http: HttpClient,
    private ngxSpinnerService: NgxSpinnerService,
    private datePipe: DatePipe
  ) {}

  ngOnInit(): void {
    this.getAllSoundBite();
    this.getindustry();
    // this.getalltags();
//  this.getBadgeData(); 
    // this.getalldegree();
  }

 



  // abc:any;
  getval(item: any) {
    // console.log(item,"textbox value");
    this.selectedvalue = item;
    console.log(this.selectedvalue, 'textbox value');
  }
  radiovalue(event: any) {
    this.selectedvalue = event.target.value;
    console.log(this.selectedvalue, 'option value');
    if (this.selectedvalue == 'on') {
      this.getval;
    }

    // console.log(this.abc,"textbox value");
  }
 

  clearAllfilter() {
    const selectStatus = document.getElementById(
      'selectStatus'
    ) as HTMLSelectElement;
    selectStatus.value = 'All'; // Set the value of the select element to "All"
    this.selected({ target: { value: 'All' } }); // Call selected function with "All" value
    this.term = '';
  }

  

  selected(event: any) {
    this.filtervalue = event.target.value;
    console.log('filtervalue', this.filtervalue);
    this.soundList = [...this.tempSoundList];
    if (this.filtervalue != 'All')
      this.soundList = this.soundList
        .filter((obj) => obj.AN_status == this.filtervalue)
        .sort((a, b) => {
          // Convert strings to Date objects
          const dateA = new Date(a.AN_createdAt);
          const dateB = new Date(b.AN_createdAt);

          // Ensure valid dates
          if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
            return 0; // If invalid dates, consider them equal
          }

          // Compare dates
          return dateB.getTime() - dateA.getTime(); // Sort in descending order (latest first)
        });
  }

  getvalue(value: any) {
    this.Tagtitle = value;
    console.log(value, 'text tags');
  }

  getAllSoundBite() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getSoundBite().subscribe({
      next: (res: any) => {
        console.log(res);
        if (res.statusCode == 200) {
          const soundList = res.data;
          const combinedData = soundList.flat(); // Assuming 'data' is your array of arrays

          this.soundList = combinedData
            .reduce(
              (
                acc: { date: Date; data: any }[],
                current: { AN_createdAt: string | number | Date }
              ) => {
                const date = new Date(current.AN_createdAt);
                if (!isNaN(date.getTime())) {
                  acc.push({ date, data: current });
                }
                return acc;
              },
              []
            )
            .sort((a: { date: number }, b: { date: number }) => b.date - a.date) // Sort in descending order (latest first)
            .map((item: { data: any }) => item.data);

          console.log(this.soundList);

          this.tempSoundList = [...this.soundList];
          console.log('Sound', this.soundList);
          this.ngxSpinnerService.hide('globalSpinner');
        }else{
          this.ngxSpinnerService.hide('globalSpinner');
          console.log("StatusCode Mismatch");
          this.toastr.error("Something went wrong");
        }
      },
      error: (error: any) => {
        this.toastr.error("Something went wrong");
        console.log('Error Message', error);
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  getUserName(data: any) {
    this.dataTransferService.getUserData(data).subscribe((res: any) => {
      if (res.statusCode == 200) {
        this.userData = res.data.userDetails[0].U_name;
        console.log('user data', this.userData);
      }
    },(error:any)=>{
      console.log("error",error);
      
    });
  }

  getindustry() {
    this.dataTransferService.getIndustryData().subscribe((res: any) => {
      console.log(res, 'industry list');
      if (res.status == 200) {
        this.industryList = res.data;
        console.log(this.industryList, 'industrylist');
      }
    },(error:any)=>{
      console.log("error",error);
    });
  }
  getalltags() {
    this.dataTransferService.gettags().subscribe((res: any) => {
      console.log(res);
      if (res.status == 200) {
        this.TagList = res.data;
      }
    },(error:any)=>{
      console.log("error",error);
      
    });
  }

  // getalldegree() {
  //   this.dataTransferService.getdegree().subscribe((res: any) => {
  //     if (res.statusCode == 200) {
  //       this.DegreeList = res.data;
  //     }
  //   },(error:any)=>{
  //     console.log("error",error);
      
  //   });
  // }
  


  editId: any;
  editRecord(record: any, title: string) {
    const state={
    isReadonly: false,
    title :title,
    selectedRecord : record,
    userId :record.AN_userId,
    }
    this.router.navigate([`actions/insights/edit-insights`],{state});
  }


}

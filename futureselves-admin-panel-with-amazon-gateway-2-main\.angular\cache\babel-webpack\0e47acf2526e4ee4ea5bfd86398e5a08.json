{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Pipe, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nfunction PaginationControlsComponent_ul_3_li_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_1_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext(3);\n\n      const _r0 = i0.ɵɵreference(1);\n\n      return _r0.previous();\n    })(\"click\", function PaginationControlsComponent_ul_3_li_1_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      i0.ɵɵnextContext(3);\n\n      const _r0 = i0.ɵɵreference(1);\n\n      return _r0.previous();\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.previousLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.screenReaderPageLabel);\n  }\n}\n\nfunction PaginationControlsComponent_ul_3_li_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.previousLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.screenReaderPageLabel);\n  }\n}\n\nfunction PaginationControlsComponent_ul_3_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_1_a_1_Template, 4, 2, \"a\", 10);\n    i0.ɵɵtemplate(2, PaginationControlsComponent_ul_3_li_1_span_2_Template, 4, 2, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n\n    const _r0 = i0.ɵɵreference(1);\n\n    i0.ɵɵclassProp(\"disabled\", _r0.isFirstPage());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", 1 < _r0.getCurrent());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r0.isFirstPage());\n  }\n}\n\nfunction PaginationControlsComponent_ul_3_li_4_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_4_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const page_r10 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵnextContext(2);\n\n      const _r0 = i0.ɵɵreference(1);\n\n      return _r0.setCurrent(page_r10.value);\n    })(\"click\", function PaginationControlsComponent_ul_3_li_4_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const page_r10 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵnextContext(2);\n\n      const _r0 = i0.ɵɵreference(1);\n\n      return _r0.setCurrent(page_r10.value);\n    });\n    i0.ɵɵelementStart(1, \"span\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const page_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r11.screenReaderPageLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r10.label === \"...\" ? page_r10.label : i0.ɵɵpipeBind2(5, 2, page_r10.label, \"\"));\n  }\n}\n\nfunction PaginationControlsComponent_ul_3_li_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 16);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const page_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.screenReaderCurrentLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r10.label === \"...\" ? page_r10.label : i0.ɵɵpipeBind2(6, 2, page_r10.label, \"\"));\n  }\n}\n\nfunction PaginationControlsComponent_ul_3_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_4_a_1_Template, 6, 5, \"a\", 10);\n    i0.ɵɵtemplate(2, PaginationControlsComponent_ul_3_li_4_ng_container_2_Template, 7, 5, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const page_r10 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n\n    const _r0 = i0.ɵɵreference(1);\n\n    i0.ɵɵclassProp(\"current\", _r0.getCurrent() === page_r10.value)(\"ellipsis\", page_r10.label === \"...\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r0.getCurrent() !== page_r10.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r0.getCurrent() === page_r10.value);\n  }\n}\n\nfunction PaginationControlsComponent_ul_3_li_5_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 12);\n    i0.ɵɵlistener(\"keyup.enter\", function PaginationControlsComponent_ul_3_li_5_a_1_Template_a_keyup_enter_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      i0.ɵɵnextContext(3);\n\n      const _r0 = i0.ɵɵreference(1);\n\n      return _r0.next();\n    })(\"click\", function PaginationControlsComponent_ul_3_li_5_a_1_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      i0.ɵɵnextContext(3);\n\n      const _r0 = i0.ɵɵreference(1);\n\n      return _r0.next();\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.nextLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r20.screenReaderPageLabel);\n  }\n}\n\nfunction PaginationControlsComponent_ul_3_li_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.nextLabel, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r21.screenReaderPageLabel);\n  }\n}\n\nfunction PaginationControlsComponent_ul_3_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 17);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_5_a_1_Template, 4, 2, \"a\", 10);\n    i0.ɵɵtemplate(2, PaginationControlsComponent_ul_3_li_5_span_2_Template, 4, 2, \"span\", 11);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n\n    const _r0 = i0.ɵɵreference(1);\n\n    i0.ɵɵclassProp(\"disabled\", _r0.isLastPage());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !_r0.isLastPage());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r0.isLastPage());\n  }\n}\n\nfunction PaginationControlsComponent_ul_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 4);\n    i0.ɵɵtemplate(1, PaginationControlsComponent_ul_3_li_1_Template, 3, 4, \"li\", 5);\n    i0.ɵɵelementStart(2, \"li\", 6);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PaginationControlsComponent_ul_3_li_4_Template, 3, 6, \"li\", 7);\n    i0.ɵɵtemplate(5, PaginationControlsComponent_ul_3_li_5_Template, 3, 4, \"li\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n\n    const _r0 = i0.ɵɵreference(1);\n\n    i0.ɵɵclassProp(\"responsive\", ctx_r1.responsive);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.directionLinks);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", _r0.getCurrent(), \" / \", _r0.getLastPage(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", _r0.pages)(\"ngForTrackBy\", ctx_r1.trackByIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.directionLinks);\n  }\n}\n\nclass PaginationService {\n  constructor() {\n    this.change = new EventEmitter();\n    this.instances = {};\n    this.DEFAULT_ID = 'DEFAULT_PAGINATION_ID';\n  }\n\n  defaultId() {\n    return this.DEFAULT_ID;\n  }\n  /**\r\n   * Register a PaginationInstance with this service. Returns a\r\n   * boolean value signifying whether the instance is new or\r\n   * updated (true = new or updated, false = unchanged).\r\n   */\n\n\n  register(instance) {\n    if (instance.id == null) {\n      instance.id = this.DEFAULT_ID;\n    }\n\n    if (!this.instances[instance.id]) {\n      this.instances[instance.id] = instance;\n      return true;\n    } else {\n      return this.updateInstance(instance);\n    }\n  }\n  /**\r\n   * Check each property of the instance and update any that have changed. Return\r\n   * true if any changes were made, else return false.\r\n   */\n\n\n  updateInstance(instance) {\n    let changed = false;\n\n    for (let prop in this.instances[instance.id]) {\n      if (instance[prop] !== this.instances[instance.id][prop]) {\n        this.instances[instance.id][prop] = instance[prop];\n        changed = true;\n      }\n    }\n\n    return changed;\n  }\n  /**\r\n   * Returns the current page number.\r\n   */\n\n\n  getCurrentPage(id) {\n    if (this.instances[id]) {\n      return this.instances[id].currentPage;\n    }\n\n    return 1;\n  }\n  /**\r\n   * Sets the current page number.\r\n   */\n\n\n  setCurrentPage(id, page) {\n    if (this.instances[id]) {\n      let instance = this.instances[id];\n      let maxPage = Math.ceil(instance.totalItems / instance.itemsPerPage);\n\n      if (page <= maxPage && 1 <= page) {\n        this.instances[id].currentPage = page;\n        this.change.emit(id);\n      }\n    }\n  }\n  /**\r\n   * Sets the value of instance.totalItems\r\n   */\n\n\n  setTotalItems(id, totalItems) {\n    if (this.instances[id] && 0 <= totalItems) {\n      this.instances[id].totalItems = totalItems;\n      this.change.emit(id);\n    }\n  }\n  /**\r\n   * Sets the value of instance.itemsPerPage.\r\n   */\n\n\n  setItemsPerPage(id, itemsPerPage) {\n    if (this.instances[id]) {\n      this.instances[id].itemsPerPage = itemsPerPage;\n      this.change.emit(id);\n    }\n  }\n  /**\r\n   * Returns a clone of the pagination instance object matching the id. If no\r\n   * id specified, returns the instance corresponding to the default id.\r\n   */\n\n\n  getInstance(id = this.DEFAULT_ID) {\n    if (this.instances[id]) {\n      return this.clone(this.instances[id]);\n    }\n\n    return {};\n  }\n  /**\r\n   * Perform a shallow clone of an object.\r\n   */\n\n\n  clone(obj) {\n    var target = {};\n\n    for (var i in obj) {\n      if (obj.hasOwnProperty(i)) {\n        target[i] = obj[i];\n      }\n    }\n\n    return target;\n  }\n\n}\n\nconst LARGE_NUMBER = Number.MAX_SAFE_INTEGER;\n\nclass PaginatePipe {\n  constructor(service) {\n    this.service = service; // store the values from the last time the pipe was invoked\n\n    this.state = {};\n  }\n\n  transform(collection, args) {\n    // When an observable is passed through the AsyncPipe, it will output\n    // `null` until the subscription resolves. In this case, we want to\n    // use the cached data from the `state` object to prevent the NgFor\n    // from flashing empty until the real values arrive.\n    if (!(collection instanceof Array)) {\n      let _id = args.id || this.service.defaultId();\n\n      if (this.state[_id]) {\n        return this.state[_id].slice;\n      } else {\n        return collection;\n      }\n    }\n\n    let serverSideMode = args.totalItems && args.totalItems !== collection.length;\n    let instance = this.createInstance(collection, args);\n    let id = instance.id;\n    let start, end;\n    let perPage = instance.itemsPerPage;\n    let emitChange = this.service.register(instance);\n\n    if (!serverSideMode && collection instanceof Array) {\n      perPage = +perPage || LARGE_NUMBER;\n      start = (instance.currentPage - 1) * perPage;\n      end = start + perPage;\n      let isIdentical = this.stateIsIdentical(id, collection, start, end);\n\n      if (isIdentical) {\n        return this.state[id].slice;\n      } else {\n        let slice = collection.slice(start, end);\n        this.saveState(id, collection, slice, start, end);\n        this.service.change.emit(id);\n        return slice;\n      }\n    } else {\n      if (emitChange) {\n        this.service.change.emit(id);\n      } // save the state for server-side collection to avoid null\n      // flash as new data loads.\n\n\n      this.saveState(id, collection, collection, start, end);\n      return collection;\n    }\n  }\n  /**\r\n   * Create an PaginationInstance object, using defaults for any optional properties not supplied.\r\n   */\n\n\n  createInstance(collection, config) {\n    this.checkConfig(config);\n    return {\n      id: config.id != null ? config.id : this.service.defaultId(),\n      itemsPerPage: +config.itemsPerPage || 0,\n      currentPage: +config.currentPage || 1,\n      totalItems: +config.totalItems || collection.length\n    };\n  }\n  /**\r\n   * Ensure the argument passed to the filter contains the required properties.\r\n   */\n\n\n  checkConfig(config) {\n    const required = ['itemsPerPage', 'currentPage'];\n    const missing = required.filter(prop => !(prop in config));\n\n    if (0 < missing.length) {\n      throw new Error(`PaginatePipe: Argument is missing the following required properties: ${missing.join(', ')}`);\n    }\n  }\n  /**\r\n   * To avoid returning a brand new array each time the pipe is run, we store the state of the sliced\r\n   * array for a given id. This means that the next time the pipe is run on this collection & id, we just\r\n   * need to check that the collection, start and end points are all identical, and if so, return the\r\n   * last sliced array.\r\n   */\n\n\n  saveState(id, collection, slice, start, end) {\n    this.state[id] = {\n      collection,\n      size: collection.length,\n      slice,\n      start,\n      end\n    };\n  }\n  /**\r\n   * For a given id, returns true if the collection, size, start and end values are identical.\r\n   */\n\n\n  stateIsIdentical(id, collection, start, end) {\n    let state = this.state[id];\n\n    if (!state) {\n      return false;\n    }\n\n    let isMetaDataIdentical = state.size === collection.length && state.start === start && state.end === end;\n\n    if (!isMetaDataIdentical) {\n      return false;\n    }\n\n    return state.slice.every((element, index) => element === collection[start + index]);\n  }\n\n}\n\nPaginatePipe.ɵfac = function PaginatePipe_Factory(t) {\n  return new (t || PaginatePipe)(i0.ɵɵdirectiveInject(PaginationService, 16));\n};\n\nPaginatePipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"paginate\",\n  type: PaginatePipe,\n  pure: false\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PaginatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'paginate',\n      pure: false\n    }]\n  }], function () {\n    return [{\n      type: PaginationService\n    }];\n  }, null);\n})();\n/**\r\n * The default template and styles for the pagination links are borrowed directly\r\n * from Zurb Foundation 6: http://foundation.zurb.com/sites/docs/pagination.html\r\n */\n\n\nconst DEFAULT_TEMPLATE = `\n    <pagination-template  #p=\"paginationApi\"\n                         [id]=\"id\"\n                         [maxSize]=\"maxSize\"\n                         (pageChange)=\"pageChange.emit($event)\"\n                         (pageBoundsCorrection)=\"pageBoundsCorrection.emit($event)\">\n    <nav role=\"navigation\" [attr.aria-label]=\"screenReaderPaginationLabel\">\n    <ul class=\"ngx-pagination\" \n        [class.responsive]=\"responsive\"\n        *ngIf=\"!(autoHide && p.pages.length <= 1)\">\n\n        <li class=\"pagination-previous\" [class.disabled]=\"p.isFirstPage()\" *ngIf=\"directionLinks\"> \n            <a tabindex=\"0\" *ngIf=\"1 < p.getCurrent()\" (keyup.enter)=\"p.previous()\" (click)=\"p.previous()\">\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </a>\n            <span *ngIf=\"p.isFirstPage()\" aria-disabled=\"true\">\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </span>\n        </li> \n\n        <li class=\"small-screen\">\n            {{ p.getCurrent() }} / {{ p.getLastPage() }}\n        </li>\n\n        <li [class.current]=\"p.getCurrent() === page.value\" \n            [class.ellipsis]=\"page.label === '...'\"\n            *ngFor=\"let page of p.pages; trackBy: trackByIndex\">\n            <a tabindex=\"0\" (keyup.enter)=\"p.setCurrent(page.value)\" (click)=\"p.setCurrent(page.value)\" *ngIf=\"p.getCurrent() !== page.value\">\n                <span class=\"show-for-sr\">{{ screenReaderPageLabel }} </span>\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span>\n            </a>\n            <ng-container *ngIf=\"p.getCurrent() === page.value\">\n              <span aria-live=\"polite\">\n                <span class=\"show-for-sr\">{{ screenReaderCurrentLabel }} </span>\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span> \n              </span>\n            </ng-container>\n        </li>\n\n        <li class=\"pagination-next\" [class.disabled]=\"p.isLastPage()\" *ngIf=\"directionLinks\">\n            <a tabindex=\"0\" *ngIf=\"!p.isLastPage()\" (keyup.enter)=\"p.next()\" (click)=\"p.next()\">\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </a>\n            <span *ngIf=\"p.isLastPage()\" aria-disabled=\"true\">\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\n            </span>\n        </li>\n\n    </ul>\n    </nav>\n    </pagination-template>\n    `;\nconst DEFAULT_STYLES = `\n.ngx-pagination {\n  margin-left: 0;\n  margin-bottom: 1rem; }\n  .ngx-pagination::before, .ngx-pagination::after {\n    content: ' ';\n    display: table; }\n  .ngx-pagination::after {\n    clear: both; }\n  .ngx-pagination li {\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    -ms-user-select: none;\n    margin-right: 0.0625rem;\n    border-radius: 0; }\n  .ngx-pagination li {\n    display: inline-block; }\n  .ngx-pagination a,\n  .ngx-pagination button {\n    color: #0a0a0a; \n    display: block;\n    padding: 0.1875rem 0.625rem;\n    border-radius: 0; }\n    .ngx-pagination a:hover,\n    .ngx-pagination button:hover {\n      background: #e6e6e6; }\n  .ngx-pagination .current {\n    padding: 0.1875rem 0.625rem;\n    background: #2199e8;\n    color: #fefefe;\n    cursor: default; }\n  .ngx-pagination .disabled {\n    padding: 0.1875rem 0.625rem;\n    color: #cacaca;\n    cursor: default; } \n    .ngx-pagination .disabled:hover {\n      background: transparent; }\n  .ngx-pagination a, .ngx-pagination button {\n    cursor: pointer; }\n\n.ngx-pagination .pagination-previous a::before,\n.ngx-pagination .pagination-previous.disabled::before { \n  content: '«';\n  display: inline-block;\n  margin-right: 0.5rem; }\n\n.ngx-pagination .pagination-next a::after,\n.ngx-pagination .pagination-next.disabled::after {\n  content: '»';\n  display: inline-block;\n  margin-left: 0.5rem; }\n\n.ngx-pagination .show-for-sr {\n  position: absolute !important;\n  width: 1px;\n  height: 1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0); }\n.ngx-pagination .small-screen {\n  display: none; }\n@media screen and (max-width: 601px) {\n  .ngx-pagination.responsive .small-screen {\n    display: inline-block; } \n  .ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next) {\n    display: none; }\n}\n  `;\n/**\r\n * This directive is what powers all pagination controls components, including the default one.\r\n * It exposes an API which is hooked up to the PaginationService to keep the PaginatePipe in sync\r\n * with the pagination controls.\r\n */\n\nclass PaginationControlsDirective {\n  constructor(service, changeDetectorRef) {\n    this.service = service;\n    this.changeDetectorRef = changeDetectorRef;\n    this.maxSize = 7;\n    this.pageChange = new EventEmitter();\n    this.pageBoundsCorrection = new EventEmitter();\n    this.pages = [];\n    this.changeSub = this.service.change.subscribe(id => {\n      if (this.id === id) {\n        this.updatePageLinks();\n        this.changeDetectorRef.markForCheck();\n        this.changeDetectorRef.detectChanges();\n      }\n    });\n  }\n\n  ngOnInit() {\n    if (this.id === undefined) {\n      this.id = this.service.defaultId();\n    }\n\n    this.updatePageLinks();\n  }\n\n  ngOnChanges(changes) {\n    this.updatePageLinks();\n  }\n\n  ngOnDestroy() {\n    this.changeSub.unsubscribe();\n  }\n  /**\r\n   * Go to the previous page\r\n   */\n\n\n  previous() {\n    this.checkValidId();\n    this.setCurrent(this.getCurrent() - 1);\n  }\n  /**\r\n   * Go to the next page\r\n   */\n\n\n  next() {\n    this.checkValidId();\n    this.setCurrent(this.getCurrent() + 1);\n  }\n  /**\r\n   * Returns true if current page is first page\r\n   */\n\n\n  isFirstPage() {\n    return this.getCurrent() === 1;\n  }\n  /**\r\n   * Returns true if current page is last page\r\n   */\n\n\n  isLastPage() {\n    return this.getLastPage() === this.getCurrent();\n  }\n  /**\r\n   * Set the current page number.\r\n   */\n\n\n  setCurrent(page) {\n    this.pageChange.emit(page);\n  }\n  /**\r\n   * Get the current page number.\r\n   */\n\n\n  getCurrent() {\n    return this.service.getCurrentPage(this.id);\n  }\n  /**\r\n   * Returns the last page number\r\n   */\n\n\n  getLastPage() {\n    let inst = this.service.getInstance(this.id);\n\n    if (inst.totalItems < 1) {\n      // when there are 0 or fewer (an error case) items, there are no \"pages\" as such,\n      // but it makes sense to consider a single, empty page as the last page.\n      return 1;\n    }\n\n    return Math.ceil(inst.totalItems / inst.itemsPerPage);\n  }\n\n  getTotalItems() {\n    return this.service.getInstance(this.id).totalItems;\n  }\n\n  checkValidId() {\n    if (this.service.getInstance(this.id).id == null) {\n      console.warn(`PaginationControlsDirective: the specified id \"${this.id}\" does not match any registered PaginationInstance`);\n    }\n  }\n  /**\r\n   * Updates the page links and checks that the current page is valid. Should run whenever the\r\n   * PaginationService.change stream emits a value matching the current ID, or when any of the\r\n   * input values changes.\r\n   */\n\n\n  updatePageLinks() {\n    let inst = this.service.getInstance(this.id);\n    const correctedCurrentPage = this.outOfBoundCorrection(inst);\n\n    if (correctedCurrentPage !== inst.currentPage) {\n      setTimeout(() => {\n        this.pageBoundsCorrection.emit(correctedCurrentPage);\n        this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\n      });\n    } else {\n      this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\n    }\n  }\n  /**\r\n   * Checks that the instance.currentPage property is within bounds for the current page range.\r\n   * If not, return a correct value for currentPage, or the current value if OK.\r\n   */\n\n\n  outOfBoundCorrection(instance) {\n    const totalPages = Math.ceil(instance.totalItems / instance.itemsPerPage);\n\n    if (totalPages < instance.currentPage && 0 < totalPages) {\n      return totalPages;\n    } else if (instance.currentPage < 1) {\n      return 1;\n    }\n\n    return instance.currentPage;\n  }\n  /**\r\n   * Returns an array of Page objects to use in the pagination controls.\r\n   */\n\n\n  createPageArray(currentPage, itemsPerPage, totalItems, paginationRange) {\n    // paginationRange could be a string if passed from attribute, so cast to number.\n    paginationRange = +paginationRange;\n    let pages = []; // Return 1 as default page number\n    // Make sense to show 1 instead of empty when there are no items\n\n    const totalPages = Math.max(Math.ceil(totalItems / itemsPerPage), 1);\n    const halfWay = Math.ceil(paginationRange / 2);\n    const isStart = currentPage <= halfWay;\n    const isEnd = totalPages - halfWay < currentPage;\n    const isMiddle = !isStart && !isEnd;\n    let ellipsesNeeded = paginationRange < totalPages;\n    let i = 1;\n\n    while (i <= totalPages && i <= paginationRange) {\n      let label;\n      let pageNumber = this.calculatePageNumber(i, currentPage, paginationRange, totalPages);\n      let openingEllipsesNeeded = i === 2 && (isMiddle || isEnd);\n      let closingEllipsesNeeded = i === paginationRange - 1 && (isMiddle || isStart);\n\n      if (ellipsesNeeded && (openingEllipsesNeeded || closingEllipsesNeeded)) {\n        label = '...';\n      } else {\n        label = pageNumber;\n      }\n\n      pages.push({\n        label: label,\n        value: pageNumber\n      });\n      i++;\n    }\n\n    return pages;\n  }\n  /**\r\n   * Given the position in the sequence of pagination links [i],\r\n   * figure out what page number corresponds to that position.\r\n   */\n\n\n  calculatePageNumber(i, currentPage, paginationRange, totalPages) {\n    let halfWay = Math.ceil(paginationRange / 2);\n\n    if (i === paginationRange) {\n      return totalPages;\n    } else if (i === 1) {\n      return i;\n    } else if (paginationRange < totalPages) {\n      if (totalPages - halfWay < currentPage) {\n        return totalPages - paginationRange + i;\n      } else if (halfWay < currentPage) {\n        return currentPage - halfWay + i;\n      } else {\n        return i;\n      }\n    } else {\n      return i;\n    }\n  }\n\n}\n\nPaginationControlsDirective.ɵfac = function PaginationControlsDirective_Factory(t) {\n  return new (t || PaginationControlsDirective)(i0.ɵɵdirectiveInject(PaginationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nPaginationControlsDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: PaginationControlsDirective,\n  selectors: [[\"pagination-template\"], [\"\", \"pagination-template\", \"\"]],\n  inputs: {\n    id: \"id\",\n    maxSize: \"maxSize\"\n  },\n  outputs: {\n    pageChange: \"pageChange\",\n    pageBoundsCorrection: \"pageBoundsCorrection\"\n  },\n  exportAs: [\"paginationApi\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PaginationControlsDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'pagination-template,[pagination-template]',\n      exportAs: 'paginationApi'\n    }]\n  }], function () {\n    return [{\n      type: PaginationService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    id: [{\n      type: Input\n    }],\n    maxSize: [{\n      type: Input\n    }],\n    pageChange: [{\n      type: Output\n    }],\n    pageBoundsCorrection: [{\n      type: Output\n    }]\n  });\n})();\n\nfunction coerceToBoolean(input) {\n  return !!input && input !== 'false';\n}\n/**\r\n * The default pagination controls component. Actually just a default implementation of a custom template.\r\n */\n\n\nclass PaginationControlsComponent {\n  constructor() {\n    this.maxSize = 7;\n    this.previousLabel = 'Previous';\n    this.nextLabel = 'Next';\n    this.screenReaderPaginationLabel = 'Pagination';\n    this.screenReaderPageLabel = 'page';\n    this.screenReaderCurrentLabel = `You're on page`;\n    this.pageChange = new EventEmitter();\n    this.pageBoundsCorrection = new EventEmitter();\n    this._directionLinks = true;\n    this._autoHide = false;\n    this._responsive = false;\n  }\n\n  get directionLinks() {\n    return this._directionLinks;\n  }\n\n  set directionLinks(value) {\n    this._directionLinks = coerceToBoolean(value);\n  }\n\n  get autoHide() {\n    return this._autoHide;\n  }\n\n  set autoHide(value) {\n    this._autoHide = coerceToBoolean(value);\n  }\n\n  get responsive() {\n    return this._responsive;\n  }\n\n  set responsive(value) {\n    this._responsive = coerceToBoolean(value);\n  }\n\n  trackByIndex(index) {\n    return index;\n  }\n\n}\n\nPaginationControlsComponent.ɵfac = function PaginationControlsComponent_Factory(t) {\n  return new (t || PaginationControlsComponent)();\n};\n\nPaginationControlsComponent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: PaginationControlsComponent,\n  selectors: [[\"pagination-controls\"]],\n  inputs: {\n    id: \"id\",\n    maxSize: \"maxSize\",\n    directionLinks: \"directionLinks\",\n    autoHide: \"autoHide\",\n    responsive: \"responsive\",\n    previousLabel: \"previousLabel\",\n    nextLabel: \"nextLabel\",\n    screenReaderPaginationLabel: \"screenReaderPaginationLabel\",\n    screenReaderPageLabel: \"screenReaderPageLabel\",\n    screenReaderCurrentLabel: \"screenReaderCurrentLabel\"\n  },\n  outputs: {\n    pageChange: \"pageChange\",\n    pageBoundsCorrection: \"pageBoundsCorrection\"\n  },\n  decls: 4,\n  vars: 4,\n  consts: [[3, \"id\", \"maxSize\", \"pageChange\", \"pageBoundsCorrection\"], [\"p\", \"paginationApi\"], [\"role\", \"navigation\"], [\"class\", \"ngx-pagination\", 3, \"responsive\", 4, \"ngIf\"], [1, \"ngx-pagination\"], [\"class\", \"pagination-previous\", 3, \"disabled\", 4, \"ngIf\"], [1, \"small-screen\"], [3, \"current\", \"ellipsis\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-next\", 3, \"disabled\", 4, \"ngIf\"], [1, \"pagination-previous\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\", 4, \"ngIf\"], [\"aria-disabled\", \"true\", 4, \"ngIf\"], [\"tabindex\", \"0\", 3, \"keyup.enter\", \"click\"], [1, \"show-for-sr\"], [\"aria-disabled\", \"true\"], [4, \"ngIf\"], [\"aria-live\", \"polite\"], [1, \"pagination-next\"]],\n  template: function PaginationControlsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"pagination-template\", 0, 1);\n      i0.ɵɵlistener(\"pageChange\", function PaginationControlsComponent_Template_pagination_template_pageChange_0_listener($event) {\n        return ctx.pageChange.emit($event);\n      })(\"pageBoundsCorrection\", function PaginationControlsComponent_Template_pagination_template_pageBoundsCorrection_0_listener($event) {\n        return ctx.pageBoundsCorrection.emit($event);\n      });\n      i0.ɵɵelementStart(2, \"nav\", 2);\n      i0.ɵɵtemplate(3, PaginationControlsComponent_ul_3_Template, 6, 8, \"ul\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      const _r0 = i0.ɵɵreference(1);\n\n      i0.ɵɵproperty(\"id\", ctx.id)(\"maxSize\", ctx.maxSize);\n      i0.ɵɵadvance(2);\n      i0.ɵɵattribute(\"aria-label\", ctx.screenReaderPaginationLabel);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !(ctx.autoHide && _r0.pages.length <= 1));\n    }\n  },\n  directives: [PaginationControlsDirective, i2.NgIf, i2.NgForOf],\n  pipes: [i2.DecimalPipe],\n  styles: [\".ngx-pagination{margin-left:0;margin-bottom:1rem}.ngx-pagination:before,.ngx-pagination:after{content:\\\" \\\";display:table}.ngx-pagination:after{clear:both}.ngx-pagination li{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;margin-right:.0625rem;border-radius:0}.ngx-pagination li{display:inline-block}.ngx-pagination a,.ngx-pagination button{color:#0a0a0a;display:block;padding:.1875rem .625rem;border-radius:0}.ngx-pagination a:hover,.ngx-pagination button:hover{background:#e6e6e6}.ngx-pagination .current{padding:.1875rem .625rem;background:#2199e8;color:#fefefe;cursor:default}.ngx-pagination .disabled{padding:.1875rem .625rem;color:#cacaca;cursor:default}.ngx-pagination .disabled:hover{background:transparent}.ngx-pagination a,.ngx-pagination button{cursor:pointer}.ngx-pagination .pagination-previous a:before,.ngx-pagination .pagination-previous.disabled:before{content:\\\"\\\\ab\\\";display:inline-block;margin-right:.5rem}.ngx-pagination .pagination-next a:after,.ngx-pagination .pagination-next.disabled:after{content:\\\"\\\\bb\\\";display:inline-block;margin-left:.5rem}.ngx-pagination .show-for-sr{position:absolute!important;width:1px;height:1px;overflow:hidden;clip:rect(0,0,0,0)}.ngx-pagination .small-screen{display:none}@media screen and (max-width: 601px){.ngx-pagination.responsive .small-screen{display:inline-block}.ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next){display:none}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PaginationControlsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'pagination-controls',\n      template: DEFAULT_TEMPLATE,\n      styles: [DEFAULT_STYLES],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, {\n    id: [{\n      type: Input\n    }],\n    maxSize: [{\n      type: Input\n    }],\n    directionLinks: [{\n      type: Input\n    }],\n    autoHide: [{\n      type: Input\n    }],\n    responsive: [{\n      type: Input\n    }],\n    previousLabel: [{\n      type: Input\n    }],\n    nextLabel: [{\n      type: Input\n    }],\n    screenReaderPaginationLabel: [{\n      type: Input\n    }],\n    screenReaderPageLabel: [{\n      type: Input\n    }],\n    screenReaderCurrentLabel: [{\n      type: Input\n    }],\n    pageChange: [{\n      type: Output\n    }],\n    pageBoundsCorrection: [{\n      type: Output\n    }]\n  });\n})();\n\nclass NgxPaginationModule {}\n\nNgxPaginationModule.ɵfac = function NgxPaginationModule_Factory(t) {\n  return new (t || NgxPaginationModule)();\n};\n\nNgxPaginationModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: NgxPaginationModule\n});\nNgxPaginationModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [PaginationService],\n  imports: [[CommonModule]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgxPaginationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      declarations: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective],\n      providers: [PaginationService],\n      exports: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective]\n    }]\n  }], null, null);\n})();\n/*\r\n * Public API Surface of ngx-pagination\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\n\nexport { NgxPaginationModule, PaginatePipe, PaginationControlsComponent, PaginationControlsDirective, PaginationService };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/ngx-pagination/fesm2020/ngx-pagination.mjs"], "names": ["i0", "EventEmitter", "<PERSON><PERSON>", "Directive", "Input", "Output", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "NgModule", "i2", "CommonModule", "PaginationService", "constructor", "change", "instances", "DEFAULT_ID", "defaultId", "register", "instance", "id", "updateInstance", "changed", "prop", "getCurrentPage", "currentPage", "setCurrentPage", "page", "maxPage", "Math", "ceil", "totalItems", "itemsPerPage", "emit", "setTotalItems", "setItemsPerPage", "getInstance", "clone", "obj", "target", "i", "hasOwnProperty", "LARGE_NUMBER", "Number", "MAX_SAFE_INTEGER", "PaginatePipe", "service", "state", "transform", "collection", "args", "Array", "_id", "slice", "serverSideMode", "length", "createInstance", "start", "end", "perPage", "emitChange", "isIdentical", "stateIsIdentical", "saveState", "config", "checkConfig", "required", "missing", "filter", "Error", "join", "size", "isMetaDataIdentical", "every", "element", "index", "ɵfac", "ɵpipe", "type", "name", "pure", "DEFAULT_TEMPLATE", "DEFAULT_STYLES", "PaginationControlsDirective", "changeDetectorRef", "maxSize", "pageChange", "pageBoundsCorrection", "pages", "changeSub", "subscribe", "updatePageLinks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "ngOnInit", "undefined", "ngOnChanges", "changes", "ngOnDestroy", "unsubscribe", "previous", "checkValidId", "setCurrent", "get<PERSON>urrent", "next", "isFirstPage", "isLastPage", "getLastPage", "inst", "getTotalItems", "console", "warn", "correctedCurrentPage", "outOfBoundCorrection", "setTimeout", "createPageArray", "totalPages", "paginationRange", "max", "halfWay", "isStart", "isEnd", "isMiddle", "ellipsesNeeded", "label", "pageNumber", "calculatePageNumber", "openingEllipsesNeeded", "closingEllipsesNeeded", "push", "value", "ChangeDetectorRef", "ɵdir", "selector", "exportAs", "coerceToBoolean", "input", "PaginationControlsComponent", "previousLabel", "next<PERSON><PERSON><PERSON>", "screenReaderPaginationLabel", "screenReaderPageLabel", "screenReaderCurrentLabel", "_directionLinks", "_autoHide", "_responsive", "directionLinks", "autoHide", "responsive", "trackByIndex", "ɵcmp", "NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DecimalPipe", "template", "styles", "changeDetection", "OnPush", "encapsulation", "None", "NgxPaginationModule", "ɵmod", "ɵinj", "imports", "declarations", "providers", "exports"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,IAAvB,EAA6BC,SAA7B,EAAwCC,KAAxC,EAA+CC,MAA/C,EAAuDC,SAAvD,EAAkEC,uBAAlE,EAA2FC,iBAA3F,EAA8GC,QAA9G,QAA8H,eAA9H;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;;;gBAkN+FX,E;;AAAAA,IAAAA,EA8XioC,2B;AA9XjoCA,IAAAA,EA8XgrC;AA9XhrCA,MAAAA,EA8XgrC;AA9XhrCA,MAAAA,EA8XgrC;;AAAA,kBA9XhrCA,EA8XgrC;;AAAA,aAAgB,cAAhB;AAAA;AA9XhrCA,MAAAA,EA8XgrC;AA9XhrCA,MAAAA,EA8XgrC;;AAAA,kBA9XhrCA,EA8XgrC;;AAAA,aAAyC,cAAzC;AAAA,M;AA9XhrCA,IAAAA,EA8X0vC,U;AA9X1vCA,IAAAA,EA8X8wC,8B;AA9X9wCA,IAAAA,EA8X0yC,U;AA9X1yCA,IAAAA,EA8Xq0C,e;AA9Xr0CA,IAAAA,EA8X01C,e;;;;mBA9X11CA,E;AAAAA,IAAAA,EA8X0vC,a;AA9X1vCA,IAAAA,EA8X0vC,mD;AA9X1vCA,IAAAA,EA8X0yC,a;AA9X1yCA,IAAAA,EA8X0yC,gD;;;;;;AA9X1yCA,IAAAA,EA8X42C,8B;AA9X52CA,IAAAA,EA8Xq7C,U;AA9Xr7CA,IAAAA,EA8Xy8C,8B;AA9Xz8CA,IAAAA,EA8Xq+C,U;AA9Xr+CA,IAAAA,EA8XggD,e;AA9XhgDA,IAAAA,EA8XqhD,e;;;;mBA9XrhDA,E;AAAAA,IAAAA,EA8Xq7C,a;AA9Xr7CA,IAAAA,EA8Xq7C,mD;AA9Xr7CA,IAAAA,EA8Xq+C,a;AA9Xr+CA,IAAAA,EA8Xq+C,gD;;;;;;AA9Xr+CA,IAAAA,EA8XkhC,2B;AA9XlhCA,IAAAA,EA8XioC,iF;AA9XjoCA,IAAAA,EA8X42C,uF;AA9X52CA,IAAAA,EA8XsiD,e;;;;AA9XtiDA,IAAAA,E;;gBAAAA,E;;AAAAA,IAAAA,EA8XojC,2C;AA9XpjCA,IAAAA,EA8XopC,a;AA9XppCA,IAAAA,EA8XopC,yC;AA9XppCA,IAAAA,EA8Xm3C,a;AA9Xn3CA,IAAAA,EA8Xm3C,sC;;;;;;iBA9Xn3CA,E;;AAAAA,IAAAA,EA8Xu2D,2B;AA9Xv2DA,IAAAA,EA8Xy3D;AA9Xz3DA,MAAAA,EA8Xy3D;AAAA,uBA9Xz3DA,EA8Xy3D;AA9Xz3DA,MAAAA,EA8Xy3D;;AAAA,kBA9Xz3DA,EA8Xy3D;;AAAA,aAAgB,8BAAhB;AAAA;AA9Xz3DA,MAAAA,EA8Xy3D;AAAA,uBA9Xz3DA,EA8Xy3D;AA9Xz3DA,MAAAA,EA8Xy3D;;AAAA,kBA9Xz3DA,EA8Xy3D;;AAAA,aAAqD,8BAArD;AAAA,M;AA9Xz3DA,IAAAA,EA8XmgE,8B;AA9XngEA,IAAAA,EA8X+hE,U;AA9X/hEA,IAAAA,EA8X2jE,e;AA9X3jEA,IAAAA,EA8XolE,0B;AA9XplEA,IAAAA,EA8X0lE,U;AA9X1lEA,IAAAA,E;AAAAA,IAAAA,EA8X8pE,e;AA9X9pEA,IAAAA,EA8XmrE,e;;;;qBA9XnrEA,E;oBAAAA,E;AAAAA,IAAAA,EA8X+hE,a;AA9X/hEA,IAAAA,EA8X+hE,2D;AA9X/hEA,IAAAA,EA8X0lE,a;AA9X1lEA,IAAAA,EA8X0lE,+DA9X1lEA,EA8X0lE,uC;;;;;;AA9X1lEA,IAAAA,EA8XqsE,2B;AA9XrsEA,IAAAA,EA8X2wE,8B;AA9X3wEA,IAAAA,EA8XwzE,8B;AA9XxzEA,IAAAA,EA8Xo1E,U;AA9Xp1EA,IAAAA,EA8Xm3E,e;AA9Xn3EA,IAAAA,EA8X44E,0B;AA9X54EA,IAAAA,EA8Xk5E,U;AA9Xl5EA,IAAAA,E;AAAAA,IAAAA,EA8Xs9E,e;AA9Xt9EA,IAAAA,EA8X8+E,e;AA9X9+EA,IAAAA,EA8XmgF,wB;;;;qBA9XngFA,E;oBAAAA,E;AAAAA,IAAAA,EA8Xo1E,a;AA9Xp1EA,IAAAA,EA8Xo1E,8D;AA9Xp1EA,IAAAA,EA8Xk5E,a;AA9Xl5EA,IAAAA,EA8Xk5E,+DA9Xl5EA,EA8Xk5E,uC;;;;;;AA9Xl5EA,IAAAA,EA8XwqD,wB;AA9XxqDA,IAAAA,EA8Xu2D,iF;AA9Xv2DA,IAAAA,EA8XqsE,uG;AA9XrsEA,IAAAA,EA8X4hF,e;;;;;AA9X5hFA,IAAAA,E;;gBAAAA,E;;AAAAA,IAAAA,EA8X4qD,kG;AA9X5qDA,IAAAA,EA8X08D,a;AA9X18DA,IAAAA,EA8X08D,wD;AA9X18DA,IAAAA,EA8XotE,a;AA9XptEA,IAAAA,EA8XotE,wD;;;;;;iBA9XptEA,E;;AAAAA,IAAAA,EA8XspF,2B;AA9XtpFA,IAAAA,EA8XksF;AA9XlsFA,MAAAA,EA8XksF;AA9XlsFA,MAAAA,EA8XksF;;AAAA,kBA9XlsFA,EA8XksF;;AAAA,aAAgB,UAAhB;AAAA;AA9XlsFA,MAAAA,EA8XksF;AA9XlsFA,MAAAA,EA8XksF;;AAAA,kBA9XlsFA,EA8XksF;;AAAA,aAAqC,UAArC;AAAA,M;AA9XlsFA,IAAAA,EA8XqwF,U;AA9XrwFA,IAAAA,EA8XqxF,8B;AA9XrxFA,IAAAA,EA8XizF,U;AA9XjzFA,IAAAA,EA8X40F,e;AA9X50FA,IAAAA,EA8Xi2F,e;;;;oBA9Xj2FA,E;AAAAA,IAAAA,EA8XqwF,a;AA9XrwFA,IAAAA,EA8XqwF,gD;AA9XrwFA,IAAAA,EA8XizF,a;AA9XjzFA,IAAAA,EA8XizF,iD;;;;;;AA9XjzFA,IAAAA,EA8Xm3F,8B;AA9Xn3FA,IAAAA,EA8X47F,U;AA9X57FA,IAAAA,EA8X48F,8B;AA9X58FA,IAAAA,EA8Xw+F,U;AA9Xx+FA,IAAAA,EA8XmgG,e;AA9XngGA,IAAAA,EA8XwhG,e;;;;oBA9XxhGA,E;AAAAA,IAAAA,EA8X47F,a;AA9X57FA,IAAAA,EA8X47F,gD;AA9X57FA,IAAAA,EA8Xw+F,a;AA9Xx+FA,IAAAA,EA8Xw+F,iD;;;;;;AA9Xx+FA,IAAAA,EA8X6iF,4B;AA9X7iFA,IAAAA,EA8XspF,iF;AA9XtpFA,IAAAA,EA8Xm3F,uF;AA9Xn3FA,IAAAA,EA8XyiG,e;;;;AA9XziGA,IAAAA,E;;gBAAAA,E;;AAAAA,IAAAA,EA8X2kF,0C;AA9X3kFA,IAAAA,EA8XyqF,a;AA9XzqFA,IAAAA,EA8XyqF,sC;AA9XzqFA,IAAAA,EA8X03F,a;AA9X13FA,IAAAA,EA8X03F,qC;;;;;;AA9X13FA,IAAAA,EA8Xu4B,2B;AA9Xv4BA,IAAAA,EA8XkhC,6E;AA9XlhCA,IAAAA,EA8XwjD,2B;AA9XxjDA,IAAAA,EA8XimD,U;AA9XjmDA,IAAAA,EA8XupD,e;AA9XvpDA,IAAAA,EA8XwqD,6E;AA9XxqDA,IAAAA,EA8X6iF,6E;AA9X7iFA,IAAAA,EA8XsjG,e;;;;mBA9XtjGA,E;;gBAAAA,E;;AAAAA,IAAAA,EA8X86B,6C;AA9X96BA,IAAAA,EA8X0lC,a;AA9X1lCA,IAAAA,EA8X0lC,0C;AA9X1lCA,IAAAA,EA8XimD,a;AA9XjmDA,IAAAA,EA8XimD,yE;AA9XjmDA,IAAAA,EA8XqzD,a;AA9XrzDA,IAAAA,EA8XqzD,sE;AA9XrzDA,IAAAA,EA8XgnF,a;AA9XhnFA,IAAAA,EA8XgnF,0C;;;;AA9kB/sF,MAAMY,iBAAN,CAAwB;AACpBC,EAAAA,WAAW,GAAG;AACV,SAAKC,MAAL,GAAc,IAAIb,YAAJ,EAAd;AACA,SAAKc,SAAL,GAAiB,EAAjB;AACA,SAAKC,UAAL,GAAkB,uBAAlB;AACH;;AACDC,EAAAA,SAAS,GAAG;AAAE,WAAO,KAAKD,UAAZ;AAAyB;AACvC;AACJ;AACA;AACA;AACA;;;AACIE,EAAAA,QAAQ,CAACC,QAAD,EAAW;AACf,QAAIA,QAAQ,CAACC,EAAT,IAAe,IAAnB,EAAyB;AACrBD,MAAAA,QAAQ,CAACC,EAAT,GAAc,KAAKJ,UAAnB;AACH;;AACD,QAAI,CAAC,KAAKD,SAAL,CAAeI,QAAQ,CAACC,EAAxB,CAAL,EAAkC;AAC9B,WAAKL,SAAL,CAAeI,QAAQ,CAACC,EAAxB,IAA8BD,QAA9B;AACA,aAAO,IAAP;AACH,KAHD,MAIK;AACD,aAAO,KAAKE,cAAL,CAAoBF,QAApB,CAAP;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIE,EAAAA,cAAc,CAACF,QAAD,EAAW;AACrB,QAAIG,OAAO,GAAG,KAAd;;AACA,SAAK,IAAIC,IAAT,IAAiB,KAAKR,SAAL,CAAeI,QAAQ,CAACC,EAAxB,CAAjB,EAA8C;AAC1C,UAAID,QAAQ,CAACI,IAAD,CAAR,KAAmB,KAAKR,SAAL,CAAeI,QAAQ,CAACC,EAAxB,EAA4BG,IAA5B,CAAvB,EAA0D;AACtD,aAAKR,SAAL,CAAeI,QAAQ,CAACC,EAAxB,EAA4BG,IAA5B,IAAoCJ,QAAQ,CAACI,IAAD,CAA5C;AACAD,QAAAA,OAAO,GAAG,IAAV;AACH;AACJ;;AACD,WAAOA,OAAP;AACH;AACD;AACJ;AACA;;;AACIE,EAAAA,cAAc,CAACJ,EAAD,EAAK;AACf,QAAI,KAAKL,SAAL,CAAeK,EAAf,CAAJ,EAAwB;AACpB,aAAO,KAAKL,SAAL,CAAeK,EAAf,EAAmBK,WAA1B;AACH;;AACD,WAAO,CAAP;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,cAAc,CAACN,EAAD,EAAKO,IAAL,EAAW;AACrB,QAAI,KAAKZ,SAAL,CAAeK,EAAf,CAAJ,EAAwB;AACpB,UAAID,QAAQ,GAAG,KAAKJ,SAAL,CAAeK,EAAf,CAAf;AACA,UAAIQ,OAAO,GAAGC,IAAI,CAACC,IAAL,CAAUX,QAAQ,CAACY,UAAT,GAAsBZ,QAAQ,CAACa,YAAzC,CAAd;;AACA,UAAIL,IAAI,IAAIC,OAAR,IAAmB,KAAKD,IAA5B,EAAkC;AAC9B,aAAKZ,SAAL,CAAeK,EAAf,EAAmBK,WAAnB,GAAiCE,IAAjC;AACA,aAAKb,MAAL,CAAYmB,IAAZ,CAAiBb,EAAjB;AACH;AACJ;AACJ;AACD;AACJ;AACA;;;AACIc,EAAAA,aAAa,CAACd,EAAD,EAAKW,UAAL,EAAiB;AAC1B,QAAI,KAAKhB,SAAL,CAAeK,EAAf,KAAsB,KAAKW,UAA/B,EAA2C;AACvC,WAAKhB,SAAL,CAAeK,EAAf,EAAmBW,UAAnB,GAAgCA,UAAhC;AACA,WAAKjB,MAAL,CAAYmB,IAAZ,CAAiBb,EAAjB;AACH;AACJ;AACD;AACJ;AACA;;;AACIe,EAAAA,eAAe,CAACf,EAAD,EAAKY,YAAL,EAAmB;AAC9B,QAAI,KAAKjB,SAAL,CAAeK,EAAf,CAAJ,EAAwB;AACpB,WAAKL,SAAL,CAAeK,EAAf,EAAmBY,YAAnB,GAAkCA,YAAlC;AACA,WAAKlB,MAAL,CAAYmB,IAAZ,CAAiBb,EAAjB;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACIgB,EAAAA,WAAW,CAAChB,EAAE,GAAG,KAAKJ,UAAX,EAAuB;AAC9B,QAAI,KAAKD,SAAL,CAAeK,EAAf,CAAJ,EAAwB;AACpB,aAAO,KAAKiB,KAAL,CAAW,KAAKtB,SAAL,CAAeK,EAAf,CAAX,CAAP;AACH;;AACD,WAAO,EAAP;AACH;AACD;AACJ;AACA;;;AACIiB,EAAAA,KAAK,CAACC,GAAD,EAAM;AACP,QAAIC,MAAM,GAAG,EAAb;;AACA,SAAK,IAAIC,CAAT,IAAcF,GAAd,EAAmB;AACf,UAAIA,GAAG,CAACG,cAAJ,CAAmBD,CAAnB,CAAJ,EAA2B;AACvBD,QAAAA,MAAM,CAACC,CAAD,CAAN,GAAYF,GAAG,CAACE,CAAD,CAAf;AACH;AACJ;;AACD,WAAOD,MAAP;AACH;;AAnGmB;;AAsGxB,MAAMG,YAAY,GAAGC,MAAM,CAACC,gBAA5B;;AACA,MAAMC,YAAN,CAAmB;AACfhC,EAAAA,WAAW,CAACiC,OAAD,EAAU;AACjB,SAAKA,OAAL,GAAeA,OAAf,CADiB,CAEjB;;AACA,SAAKC,KAAL,GAAa,EAAb;AACH;;AACDC,EAAAA,SAAS,CAACC,UAAD,EAAaC,IAAb,EAAmB;AACxB;AACA;AACA;AACA;AACA,QAAI,EAAED,UAAU,YAAYE,KAAxB,CAAJ,EAAoC;AAChC,UAAIC,GAAG,GAAGF,IAAI,CAAC9B,EAAL,IAAW,KAAK0B,OAAL,CAAa7B,SAAb,EAArB;;AACA,UAAI,KAAK8B,KAAL,CAAWK,GAAX,CAAJ,EAAqB;AACjB,eAAO,KAAKL,KAAL,CAAWK,GAAX,EAAgBC,KAAvB;AACH,OAFD,MAGK;AACD,eAAOJ,UAAP;AACH;AACJ;;AACD,QAAIK,cAAc,GAAGJ,IAAI,CAACnB,UAAL,IAAmBmB,IAAI,CAACnB,UAAL,KAAoBkB,UAAU,CAACM,MAAvE;AACA,QAAIpC,QAAQ,GAAG,KAAKqC,cAAL,CAAoBP,UAApB,EAAgCC,IAAhC,CAAf;AACA,QAAI9B,EAAE,GAAGD,QAAQ,CAACC,EAAlB;AACA,QAAIqC,KAAJ,EAAWC,GAAX;AACA,QAAIC,OAAO,GAAGxC,QAAQ,CAACa,YAAvB;AACA,QAAI4B,UAAU,GAAG,KAAKd,OAAL,CAAa5B,QAAb,CAAsBC,QAAtB,CAAjB;;AACA,QAAI,CAACmC,cAAD,IAAmBL,UAAU,YAAYE,KAA7C,EAAoD;AAChDQ,MAAAA,OAAO,GAAG,CAACA,OAAD,IAAYjB,YAAtB;AACAe,MAAAA,KAAK,GAAG,CAACtC,QAAQ,CAACM,WAAT,GAAuB,CAAxB,IAA6BkC,OAArC;AACAD,MAAAA,GAAG,GAAGD,KAAK,GAAGE,OAAd;AACA,UAAIE,WAAW,GAAG,KAAKC,gBAAL,CAAsB1C,EAAtB,EAA0B6B,UAA1B,EAAsCQ,KAAtC,EAA6CC,GAA7C,CAAlB;;AACA,UAAIG,WAAJ,EAAiB;AACb,eAAO,KAAKd,KAAL,CAAW3B,EAAX,EAAeiC,KAAtB;AACH,OAFD,MAGK;AACD,YAAIA,KAAK,GAAGJ,UAAU,CAACI,KAAX,CAAiBI,KAAjB,EAAwBC,GAAxB,CAAZ;AACA,aAAKK,SAAL,CAAe3C,EAAf,EAAmB6B,UAAnB,EAA+BI,KAA/B,EAAsCI,KAAtC,EAA6CC,GAA7C;AACA,aAAKZ,OAAL,CAAahC,MAAb,CAAoBmB,IAApB,CAAyBb,EAAzB;AACA,eAAOiC,KAAP;AACH;AACJ,KAdD,MAeK;AACD,UAAIO,UAAJ,EAAgB;AACZ,aAAKd,OAAL,CAAahC,MAAb,CAAoBmB,IAApB,CAAyBb,EAAzB;AACH,OAHA,CAID;AACA;;;AACA,WAAK2C,SAAL,CAAe3C,EAAf,EAAmB6B,UAAnB,EAA+BA,UAA/B,EAA2CQ,KAA3C,EAAkDC,GAAlD;AACA,aAAOT,UAAP;AACH;AACJ;AACD;AACJ;AACA;;;AACIO,EAAAA,cAAc,CAACP,UAAD,EAAae,MAAb,EAAqB;AAC/B,SAAKC,WAAL,CAAiBD,MAAjB;AACA,WAAO;AACH5C,MAAAA,EAAE,EAAE4C,MAAM,CAAC5C,EAAP,IAAa,IAAb,GAAoB4C,MAAM,CAAC5C,EAA3B,GAAgC,KAAK0B,OAAL,CAAa7B,SAAb,EADjC;AAEHe,MAAAA,YAAY,EAAE,CAACgC,MAAM,CAAChC,YAAR,IAAwB,CAFnC;AAGHP,MAAAA,WAAW,EAAE,CAACuC,MAAM,CAACvC,WAAR,IAAuB,CAHjC;AAIHM,MAAAA,UAAU,EAAE,CAACiC,MAAM,CAACjC,UAAR,IAAsBkB,UAAU,CAACM;AAJ1C,KAAP;AAMH;AACD;AACJ;AACA;;;AACIU,EAAAA,WAAW,CAACD,MAAD,EAAS;AAChB,UAAME,QAAQ,GAAG,CAAC,cAAD,EAAiB,aAAjB,CAAjB;AACA,UAAMC,OAAO,GAAGD,QAAQ,CAACE,MAAT,CAAgB7C,IAAI,IAAI,EAAEA,IAAI,IAAIyC,MAAV,CAAxB,CAAhB;;AACA,QAAI,IAAIG,OAAO,CAACZ,MAAhB,EAAwB;AACpB,YAAM,IAAIc,KAAJ,CAAW,wEAAuEF,OAAO,CAACG,IAAR,CAAa,IAAb,CAAmB,EAArG,CAAN;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIP,EAAAA,SAAS,CAAC3C,EAAD,EAAK6B,UAAL,EAAiBI,KAAjB,EAAwBI,KAAxB,EAA+BC,GAA/B,EAAoC;AACzC,SAAKX,KAAL,CAAW3B,EAAX,IAAiB;AACb6B,MAAAA,UADa;AAEbsB,MAAAA,IAAI,EAAEtB,UAAU,CAACM,MAFJ;AAGbF,MAAAA,KAHa;AAIbI,MAAAA,KAJa;AAKbC,MAAAA;AALa,KAAjB;AAOH;AACD;AACJ;AACA;;;AACII,EAAAA,gBAAgB,CAAC1C,EAAD,EAAK6B,UAAL,EAAiBQ,KAAjB,EAAwBC,GAAxB,EAA6B;AACzC,QAAIX,KAAK,GAAG,KAAKA,KAAL,CAAW3B,EAAX,CAAZ;;AACA,QAAI,CAAC2B,KAAL,EAAY;AACR,aAAO,KAAP;AACH;;AACD,QAAIyB,mBAAmB,GAAGzB,KAAK,CAACwB,IAAN,KAAetB,UAAU,CAACM,MAA1B,IACtBR,KAAK,CAACU,KAAN,KAAgBA,KADM,IAEtBV,KAAK,CAACW,GAAN,KAAcA,GAFlB;;AAGA,QAAI,CAACc,mBAAL,EAA0B;AACtB,aAAO,KAAP;AACH;;AACD,WAAOzB,KAAK,CAACM,KAAN,CAAYoB,KAAZ,CAAkB,CAACC,OAAD,EAAUC,KAAV,KAAoBD,OAAO,KAAKzB,UAAU,CAACQ,KAAK,GAAGkB,KAAT,CAA5D,CAAP;AACH;;AAvGc;;AAyGnB9B,YAAY,CAAC+B,IAAb;AAAA,mBAAyG/B,YAAzG,EAA+F7C,EAA/F,mBAAuIY,iBAAvI;AAAA;;AACAiC,YAAY,CAACgC,KAAb,kBAD+F7E,EAC/F;AAAA;AAAA,QAAuG6C,YAAvG;AAAA;AAAA;;AACA;AAAA,qDAF+F7C,EAE/F,mBAA2F6C,YAA3F,EAAqH,CAAC;AAC1GiC,IAAAA,IAAI,EAAE5E,IADoG;AAE1GgD,IAAAA,IAAI,EAAE,CAAC;AACC6B,MAAAA,IAAI,EAAE,UADP;AAECC,MAAAA,IAAI,EAAE;AAFP,KAAD;AAFoG,GAAD,CAArH,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAElE;AAAR,KAAD,CAAP;AAAuC,GANjF;AAAA;AAQA;AACA;AACA;AACA;;;AACA,MAAMqE,gBAAgB,GAAI;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAnDA;AAoDA,MAAMC,cAAc,GAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAlEA;AAoEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,2BAAN,CAAkC;AAC9BtE,EAAAA,WAAW,CAACiC,OAAD,EAAUsC,iBAAV,EAA6B;AACpC,SAAKtC,OAAL,GAAeA,OAAf;AACA,SAAKsC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,OAAL,GAAe,CAAf;AACA,SAAKC,UAAL,GAAkB,IAAIrF,YAAJ,EAAlB;AACA,SAAKsF,oBAAL,GAA4B,IAAItF,YAAJ,EAA5B;AACA,SAAKuF,KAAL,GAAa,EAAb;AACA,SAAKC,SAAL,GAAiB,KAAK3C,OAAL,CAAahC,MAAb,CACZ4E,SADY,CACFtE,EAAE,IAAI;AACjB,UAAI,KAAKA,EAAL,KAAYA,EAAhB,EAAoB;AAChB,aAAKuE,eAAL;AACA,aAAKP,iBAAL,CAAuBQ,YAAvB;AACA,aAAKR,iBAAL,CAAuBS,aAAvB;AACH;AACJ,KAPgB,CAAjB;AAQH;;AACDC,EAAAA,QAAQ,GAAG;AACP,QAAI,KAAK1E,EAAL,KAAY2E,SAAhB,EAA2B;AACvB,WAAK3E,EAAL,GAAU,KAAK0B,OAAL,CAAa7B,SAAb,EAAV;AACH;;AACD,SAAK0E,eAAL;AACH;;AACDK,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,SAAKN,eAAL;AACH;;AACDO,EAAAA,WAAW,GAAG;AACV,SAAKT,SAAL,CAAeU,WAAf;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,QAAQ,GAAG;AACP,SAAKC,YAAL;AACA,SAAKC,UAAL,CAAgB,KAAKC,UAAL,KAAoB,CAApC;AACH;AACD;AACJ;AACA;;;AACIC,EAAAA,IAAI,GAAG;AACH,SAAKH,YAAL;AACA,SAAKC,UAAL,CAAgB,KAAKC,UAAL,KAAoB,CAApC;AACH;AACD;AACJ;AACA;;;AACIE,EAAAA,WAAW,GAAG;AACV,WAAO,KAAKF,UAAL,OAAsB,CAA7B;AACH;AACD;AACJ;AACA;;;AACIG,EAAAA,UAAU,GAAG;AACT,WAAO,KAAKC,WAAL,OAAuB,KAAKJ,UAAL,EAA9B;AACH;AACD;AACJ;AACA;;;AACID,EAAAA,UAAU,CAAC3E,IAAD,EAAO;AACb,SAAK2D,UAAL,CAAgBrD,IAAhB,CAAqBN,IAArB;AACH;AACD;AACJ;AACA;;;AACI4E,EAAAA,UAAU,GAAG;AACT,WAAO,KAAKzD,OAAL,CAAatB,cAAb,CAA4B,KAAKJ,EAAjC,CAAP;AACH;AACD;AACJ;AACA;;;AACIuF,EAAAA,WAAW,GAAG;AACV,QAAIC,IAAI,GAAG,KAAK9D,OAAL,CAAaV,WAAb,CAAyB,KAAKhB,EAA9B,CAAX;;AACA,QAAIwF,IAAI,CAAC7E,UAAL,GAAkB,CAAtB,EAAyB;AACrB;AACA;AACA,aAAO,CAAP;AACH;;AACD,WAAOF,IAAI,CAACC,IAAL,CAAU8E,IAAI,CAAC7E,UAAL,GAAkB6E,IAAI,CAAC5E,YAAjC,CAAP;AACH;;AACD6E,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAK/D,OAAL,CAAaV,WAAb,CAAyB,KAAKhB,EAA9B,EAAkCW,UAAzC;AACH;;AACDsE,EAAAA,YAAY,GAAG;AACX,QAAI,KAAKvD,OAAL,CAAaV,WAAb,CAAyB,KAAKhB,EAA9B,EAAkCA,EAAlC,IAAwC,IAA5C,EAAkD;AAC9C0F,MAAAA,OAAO,CAACC,IAAR,CAAc,kDAAiD,KAAK3F,EAAG,oDAAvE;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACIuE,EAAAA,eAAe,GAAG;AACd,QAAIiB,IAAI,GAAG,KAAK9D,OAAL,CAAaV,WAAb,CAAyB,KAAKhB,EAA9B,CAAX;AACA,UAAM4F,oBAAoB,GAAG,KAAKC,oBAAL,CAA0BL,IAA1B,CAA7B;;AACA,QAAII,oBAAoB,KAAKJ,IAAI,CAACnF,WAAlC,EAA+C;AAC3CyF,MAAAA,UAAU,CAAC,MAAM;AACb,aAAK3B,oBAAL,CAA0BtD,IAA1B,CAA+B+E,oBAA/B;AACA,aAAKxB,KAAL,GAAa,KAAK2B,eAAL,CAAqBP,IAAI,CAACnF,WAA1B,EAAuCmF,IAAI,CAAC5E,YAA5C,EAA0D4E,IAAI,CAAC7E,UAA/D,EAA2E,KAAKsD,OAAhF,CAAb;AACH,OAHS,CAAV;AAIH,KALD,MAMK;AACD,WAAKG,KAAL,GAAa,KAAK2B,eAAL,CAAqBP,IAAI,CAACnF,WAA1B,EAAuCmF,IAAI,CAAC5E,YAA5C,EAA0D4E,IAAI,CAAC7E,UAA/D,EAA2E,KAAKsD,OAAhF,CAAb;AACH;AACJ;AACD;AACJ;AACA;AACA;;;AACI4B,EAAAA,oBAAoB,CAAC9F,QAAD,EAAW;AAC3B,UAAMiG,UAAU,GAAGvF,IAAI,CAACC,IAAL,CAAUX,QAAQ,CAACY,UAAT,GAAsBZ,QAAQ,CAACa,YAAzC,CAAnB;;AACA,QAAIoF,UAAU,GAAGjG,QAAQ,CAACM,WAAtB,IAAqC,IAAI2F,UAA7C,EAAyD;AACrD,aAAOA,UAAP;AACH,KAFD,MAGK,IAAIjG,QAAQ,CAACM,WAAT,GAAuB,CAA3B,EAA8B;AAC/B,aAAO,CAAP;AACH;;AACD,WAAON,QAAQ,CAACM,WAAhB;AACH;AACD;AACJ;AACA;;;AACI0F,EAAAA,eAAe,CAAC1F,WAAD,EAAcO,YAAd,EAA4BD,UAA5B,EAAwCsF,eAAxC,EAAyD;AACpE;AACAA,IAAAA,eAAe,GAAG,CAACA,eAAnB;AACA,QAAI7B,KAAK,GAAG,EAAZ,CAHoE,CAIpE;AACA;;AACA,UAAM4B,UAAU,GAAGvF,IAAI,CAACyF,GAAL,CAASzF,IAAI,CAACC,IAAL,CAAUC,UAAU,GAAGC,YAAvB,CAAT,EAA+C,CAA/C,CAAnB;AACA,UAAMuF,OAAO,GAAG1F,IAAI,CAACC,IAAL,CAAUuF,eAAe,GAAG,CAA5B,CAAhB;AACA,UAAMG,OAAO,GAAG/F,WAAW,IAAI8F,OAA/B;AACA,UAAME,KAAK,GAAGL,UAAU,GAAGG,OAAb,GAAuB9F,WAArC;AACA,UAAMiG,QAAQ,GAAG,CAACF,OAAD,IAAY,CAACC,KAA9B;AACA,QAAIE,cAAc,GAAGN,eAAe,GAAGD,UAAvC;AACA,QAAI5E,CAAC,GAAG,CAAR;;AACA,WAAOA,CAAC,IAAI4E,UAAL,IAAmB5E,CAAC,IAAI6E,eAA/B,EAAgD;AAC5C,UAAIO,KAAJ;AACA,UAAIC,UAAU,GAAG,KAAKC,mBAAL,CAAyBtF,CAAzB,EAA4Bf,WAA5B,EAAyC4F,eAAzC,EAA0DD,UAA1D,CAAjB;AACA,UAAIW,qBAAqB,GAAIvF,CAAC,KAAK,CAAN,KAAYkF,QAAQ,IAAID,KAAxB,CAA7B;AACA,UAAIO,qBAAqB,GAAIxF,CAAC,KAAK6E,eAAe,GAAG,CAAxB,KAA8BK,QAAQ,IAAIF,OAA1C,CAA7B;;AACA,UAAIG,cAAc,KAAKI,qBAAqB,IAAIC,qBAA9B,CAAlB,EAAwE;AACpEJ,QAAAA,KAAK,GAAG,KAAR;AACH,OAFD,MAGK;AACDA,QAAAA,KAAK,GAAGC,UAAR;AACH;;AACDrC,MAAAA,KAAK,CAACyC,IAAN,CAAW;AACPL,QAAAA,KAAK,EAAEA,KADA;AAEPM,QAAAA,KAAK,EAAEL;AAFA,OAAX;AAIArF,MAAAA,CAAC;AACJ;;AACD,WAAOgD,KAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIsC,EAAAA,mBAAmB,CAACtF,CAAD,EAAIf,WAAJ,EAAiB4F,eAAjB,EAAkCD,UAAlC,EAA8C;AAC7D,QAAIG,OAAO,GAAG1F,IAAI,CAACC,IAAL,CAAUuF,eAAe,GAAG,CAA5B,CAAd;;AACA,QAAI7E,CAAC,KAAK6E,eAAV,EAA2B;AACvB,aAAOD,UAAP;AACH,KAFD,MAGK,IAAI5E,CAAC,KAAK,CAAV,EAAa;AACd,aAAOA,CAAP;AACH,KAFI,MAGA,IAAI6E,eAAe,GAAGD,UAAtB,EAAkC;AACnC,UAAIA,UAAU,GAAGG,OAAb,GAAuB9F,WAA3B,EAAwC;AACpC,eAAO2F,UAAU,GAAGC,eAAb,GAA+B7E,CAAtC;AACH,OAFD,MAGK,IAAI+E,OAAO,GAAG9F,WAAd,EAA2B;AAC5B,eAAOA,WAAW,GAAG8F,OAAd,GAAwB/E,CAA/B;AACH,OAFI,MAGA;AACD,eAAOA,CAAP;AACH;AACJ,KAVI,MAWA;AACD,aAAOA,CAAP;AACH;AACJ;;AApL6B;;AAsLlC2C,2BAA2B,CAACP,IAA5B;AAAA,mBAAwHO,2BAAxH,EAjU+FnF,EAiU/F,mBAAqKY,iBAArK,GAjU+FZ,EAiU/F,mBAAmMA,EAAE,CAACmI,iBAAtM;AAAA;;AACAhD,2BAA2B,CAACiD,IAA5B,kBAlU+FpI,EAkU/F;AAAA,QAA4GmF,2BAA5G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAlU+FnF,EAkU/F;AAAA;;AACA;AAAA,qDAnU+FA,EAmU/F,mBAA2FmF,2BAA3F,EAAoI,CAAC;AACzHL,IAAAA,IAAI,EAAE3E,SADmH;AAEzH+C,IAAAA,IAAI,EAAE,CAAC;AACCmF,MAAAA,QAAQ,EAAE,2CADX;AAECC,MAAAA,QAAQ,EAAE;AAFX,KAAD;AAFmH,GAAD,CAApI,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAExD,MAAAA,IAAI,EAAElE;AAAR,KAAD,EAA8B;AAAEkE,MAAAA,IAAI,EAAE9E,EAAE,CAACmI;AAAX,KAA9B,CAAP;AAAuE,GANjH,EAMmI;AAAE/G,IAAAA,EAAE,EAAE,CAAC;AAC1H0D,MAAAA,IAAI,EAAE1E;AADoH,KAAD,CAAN;AAEnHiF,IAAAA,OAAO,EAAE,CAAC;AACVP,MAAAA,IAAI,EAAE1E;AADI,KAAD,CAF0G;AAInHkF,IAAAA,UAAU,EAAE,CAAC;AACbR,MAAAA,IAAI,EAAEzE;AADO,KAAD,CAJuG;AAMnHkF,IAAAA,oBAAoB,EAAE,CAAC;AACvBT,MAAAA,IAAI,EAAEzE;AADiB,KAAD;AAN6F,GANnI;AAAA;;AAgBA,SAASkI,eAAT,CAAyBC,KAAzB,EAAgC;AAC5B,SAAO,CAAC,CAACA,KAAF,IAAWA,KAAK,KAAK,OAA5B;AACH;AACD;AACA;AACA;;;AACA,MAAMC,2BAAN,CAAkC;AAC9B5H,EAAAA,WAAW,GAAG;AACV,SAAKwE,OAAL,GAAe,CAAf;AACA,SAAKqD,aAAL,GAAqB,UAArB;AACA,SAAKC,SAAL,GAAiB,MAAjB;AACA,SAAKC,2BAAL,GAAmC,YAAnC;AACA,SAAKC,qBAAL,GAA6B,MAA7B;AACA,SAAKC,wBAAL,GAAiC,gBAAjC;AACA,SAAKxD,UAAL,GAAkB,IAAIrF,YAAJ,EAAlB;AACA,SAAKsF,oBAAL,GAA4B,IAAItF,YAAJ,EAA5B;AACA,SAAK8I,eAAL,GAAuB,IAAvB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,WAAL,GAAmB,KAAnB;AACH;;AACiB,MAAdC,cAAc,GAAG;AACjB,WAAO,KAAKH,eAAZ;AACH;;AACiB,MAAdG,cAAc,CAAChB,KAAD,EAAQ;AACtB,SAAKa,eAAL,GAAuBR,eAAe,CAACL,KAAD,CAAtC;AACH;;AACW,MAARiB,QAAQ,GAAG;AACX,WAAO,KAAKH,SAAZ;AACH;;AACW,MAARG,QAAQ,CAACjB,KAAD,EAAQ;AAChB,SAAKc,SAAL,GAAiBT,eAAe,CAACL,KAAD,CAAhC;AACH;;AACa,MAAVkB,UAAU,GAAG;AACb,WAAO,KAAKH,WAAZ;AACH;;AACa,MAAVG,UAAU,CAAClB,KAAD,EAAQ;AAClB,SAAKe,WAAL,GAAmBV,eAAe,CAACL,KAAD,CAAlC;AACH;;AACDmB,EAAAA,YAAY,CAAC1E,KAAD,EAAQ;AAChB,WAAOA,KAAP;AACH;;AAlC6B;;AAoClC8D,2BAA2B,CAAC7D,IAA5B;AAAA,mBAAwH6D,2BAAxH;AAAA;;AACAA,2BAA2B,CAACa,IAA5B,kBA9X+FtJ,EA8X/F;AAAA,QAA4GyI,2BAA5G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA9X+FzI,MAAAA,EA8XqhB,+CAApnB;AA9X+FA,MAAAA,EA8XgrB;AAAA,eAAe,2BAAf;AAAA;AAAA,eAA4F,qCAA5F;AAAA,QAA/wB;AA9X+FA,MAAAA,EA8XszB,4BAAr5B;AA9X+FA,MAAAA,EA8Xu4B,wEAAt+B;AA9X+FA,MAAAA,EA8XikG,eAAhqG;AA9X+FA,MAAAA,EA8X6kG,eAA5qG;AAAA;;AAAA;AAAA,kBA9X+FA,EA8X/F;;AA9X+FA,MAAAA,EA8X0lB,iDAAzrB;AA9X+FA,MAAAA,EA8X+0B,aAA96B;AA9X+FA,MAAAA,EA8X+0B,2DAA96B;AA9X+FA,MAAAA,EA8X09B,aAAzjC;AA9X+FA,MAAAA,EA8X09B,6DAAzjC;AAAA;AAAA;AAAA,eAAkrJmF,2BAAlrJ,EAAy3JzE,EAAE,CAAC6I,IAA53J,EAA08J7I,EAAE,CAAC8I,OAA78J;AAAA,UAAgkK9I,EAAE,CAAC+I,WAAnkK;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA,qDA/X+FzJ,EA+X/F,mBAA2FyI,2BAA3F,EAAoI,CAAC;AACzH3D,IAAAA,IAAI,EAAExE,SADmH;AAEzH4C,IAAAA,IAAI,EAAE,CAAC;AACCmF,MAAAA,QAAQ,EAAE,qBADX;AAECqB,MAAAA,QAAQ,EAAEzE,gBAFX;AAGC0E,MAAAA,MAAM,EAAE,CAACzE,cAAD,CAHT;AAIC0E,MAAAA,eAAe,EAAErJ,uBAAuB,CAACsJ,MAJ1C;AAKCC,MAAAA,aAAa,EAAEtJ,iBAAiB,CAACuJ;AALlC,KAAD;AAFmH,GAAD,CAApI,QAS4B;AAAE3I,IAAAA,EAAE,EAAE,CAAC;AACnB0D,MAAAA,IAAI,EAAE1E;AADa,KAAD,CAAN;AAEZiF,IAAAA,OAAO,EAAE,CAAC;AACVP,MAAAA,IAAI,EAAE1E;AADI,KAAD,CAFG;AAIZ8I,IAAAA,cAAc,EAAE,CAAC;AACjBpE,MAAAA,IAAI,EAAE1E;AADW,KAAD,CAJJ;AAMZ+I,IAAAA,QAAQ,EAAE,CAAC;AACXrE,MAAAA,IAAI,EAAE1E;AADK,KAAD,CANE;AAQZgJ,IAAAA,UAAU,EAAE,CAAC;AACbtE,MAAAA,IAAI,EAAE1E;AADO,KAAD,CARA;AAUZsI,IAAAA,aAAa,EAAE,CAAC;AAChB5D,MAAAA,IAAI,EAAE1E;AADU,KAAD,CAVH;AAYZuI,IAAAA,SAAS,EAAE,CAAC;AACZ7D,MAAAA,IAAI,EAAE1E;AADM,KAAD,CAZC;AAcZwI,IAAAA,2BAA2B,EAAE,CAAC;AAC9B9D,MAAAA,IAAI,EAAE1E;AADwB,KAAD,CAdjB;AAgBZyI,IAAAA,qBAAqB,EAAE,CAAC;AACxB/D,MAAAA,IAAI,EAAE1E;AADkB,KAAD,CAhBX;AAkBZ0I,IAAAA,wBAAwB,EAAE,CAAC;AAC3BhE,MAAAA,IAAI,EAAE1E;AADqB,KAAD,CAlBd;AAoBZkF,IAAAA,UAAU,EAAE,CAAC;AACbR,MAAAA,IAAI,EAAEzE;AADO,KAAD,CApBA;AAsBZkF,IAAAA,oBAAoB,EAAE,CAAC;AACvBT,MAAAA,IAAI,EAAEzE;AADiB,KAAD;AAtBV,GAT5B;AAAA;;AAmCA,MAAM2J,mBAAN,CAA0B;;AAE1BA,mBAAmB,CAACpF,IAApB;AAAA,mBAAgHoF,mBAAhH;AAAA;;AACAA,mBAAmB,CAACC,IAApB,kBAra+FjK,EAqa/F;AAAA,QAAiHgK;AAAjH;AAGAA,mBAAmB,CAACE,IAApB,kBAxa+FlK,EAwa/F;AAAA,aAAiJ,CAACY,iBAAD,CAAjJ;AAAA,YAAgL,CAACD,YAAD,CAAhL;AAAA;;AACA;AAAA,qDAza+FX,EAya/F,mBAA2FgK,mBAA3F,EAA4H,CAAC;AACjHlF,IAAAA,IAAI,EAAErE,QAD2G;AAEjHyC,IAAAA,IAAI,EAAE,CAAC;AACCiH,MAAAA,OAAO,EAAE,CAACxJ,YAAD,CADV;AAECyJ,MAAAA,YAAY,EAAE,CACVvH,YADU,EAEV4F,2BAFU,EAGVtD,2BAHU,CAFf;AAOCkF,MAAAA,SAAS,EAAE,CAACzJ,iBAAD,CAPZ;AAQC0J,MAAAA,OAAO,EAAE,CAACzH,YAAD,EAAe4F,2BAAf,EAA4CtD,2BAA5C;AARV,KAAD;AAF2G,GAAD,CAA5H;AAAA;AAcA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS6E,mBAAT,EAA8BnH,YAA9B,EAA4C4F,2BAA5C,EAAyEtD,2BAAzE,EAAsGvE,iBAAtG", "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Pipe, Directive, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass PaginationService {\r\n    constructor() {\r\n        this.change = new EventEmitter();\r\n        this.instances = {};\r\n        this.DEFAULT_ID = 'DEFAULT_PAGINATION_ID';\r\n    }\r\n    defaultId() { return this.DEFAULT_ID; }\r\n    /**\r\n     * Register a PaginationInstance with this service. Returns a\r\n     * boolean value signifying whether the instance is new or\r\n     * updated (true = new or updated, false = unchanged).\r\n     */\r\n    register(instance) {\r\n        if (instance.id == null) {\r\n            instance.id = this.DEFAULT_ID;\r\n        }\r\n        if (!this.instances[instance.id]) {\r\n            this.instances[instance.id] = instance;\r\n            return true;\r\n        }\r\n        else {\r\n            return this.updateInstance(instance);\r\n        }\r\n    }\r\n    /**\r\n     * Check each property of the instance and update any that have changed. Return\r\n     * true if any changes were made, else return false.\r\n     */\r\n    updateInstance(instance) {\r\n        let changed = false;\r\n        for (let prop in this.instances[instance.id]) {\r\n            if (instance[prop] !== this.instances[instance.id][prop]) {\r\n                this.instances[instance.id][prop] = instance[prop];\r\n                changed = true;\r\n            }\r\n        }\r\n        return changed;\r\n    }\r\n    /**\r\n     * Returns the current page number.\r\n     */\r\n    getCurrentPage(id) {\r\n        if (this.instances[id]) {\r\n            return this.instances[id].currentPage;\r\n        }\r\n        return 1;\r\n    }\r\n    /**\r\n     * Sets the current page number.\r\n     */\r\n    setCurrentPage(id, page) {\r\n        if (this.instances[id]) {\r\n            let instance = this.instances[id];\r\n            let maxPage = Math.ceil(instance.totalItems / instance.itemsPerPage);\r\n            if (page <= maxPage && 1 <= page) {\r\n                this.instances[id].currentPage = page;\r\n                this.change.emit(id);\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * Sets the value of instance.totalItems\r\n     */\r\n    setTotalItems(id, totalItems) {\r\n        if (this.instances[id] && 0 <= totalItems) {\r\n            this.instances[id].totalItems = totalItems;\r\n            this.change.emit(id);\r\n        }\r\n    }\r\n    /**\r\n     * Sets the value of instance.itemsPerPage.\r\n     */\r\n    setItemsPerPage(id, itemsPerPage) {\r\n        if (this.instances[id]) {\r\n            this.instances[id].itemsPerPage = itemsPerPage;\r\n            this.change.emit(id);\r\n        }\r\n    }\r\n    /**\r\n     * Returns a clone of the pagination instance object matching the id. If no\r\n     * id specified, returns the instance corresponding to the default id.\r\n     */\r\n    getInstance(id = this.DEFAULT_ID) {\r\n        if (this.instances[id]) {\r\n            return this.clone(this.instances[id]);\r\n        }\r\n        return {};\r\n    }\r\n    /**\r\n     * Perform a shallow clone of an object.\r\n     */\r\n    clone(obj) {\r\n        var target = {};\r\n        for (var i in obj) {\r\n            if (obj.hasOwnProperty(i)) {\r\n                target[i] = obj[i];\r\n            }\r\n        }\r\n        return target;\r\n    }\r\n}\n\nconst LARGE_NUMBER = Number.MAX_SAFE_INTEGER;\r\nclass PaginatePipe {\r\n    constructor(service) {\r\n        this.service = service;\r\n        // store the values from the last time the pipe was invoked\r\n        this.state = {};\r\n    }\r\n    transform(collection, args) {\r\n        // When an observable is passed through the AsyncPipe, it will output\r\n        // `null` until the subscription resolves. In this case, we want to\r\n        // use the cached data from the `state` object to prevent the NgFor\r\n        // from flashing empty until the real values arrive.\r\n        if (!(collection instanceof Array)) {\r\n            let _id = args.id || this.service.defaultId();\r\n            if (this.state[_id]) {\r\n                return this.state[_id].slice;\r\n            }\r\n            else {\r\n                return collection;\r\n            }\r\n        }\r\n        let serverSideMode = args.totalItems && args.totalItems !== collection.length;\r\n        let instance = this.createInstance(collection, args);\r\n        let id = instance.id;\r\n        let start, end;\r\n        let perPage = instance.itemsPerPage;\r\n        let emitChange = this.service.register(instance);\r\n        if (!serverSideMode && collection instanceof Array) {\r\n            perPage = +perPage || LARGE_NUMBER;\r\n            start = (instance.currentPage - 1) * perPage;\r\n            end = start + perPage;\r\n            let isIdentical = this.stateIsIdentical(id, collection, start, end);\r\n            if (isIdentical) {\r\n                return this.state[id].slice;\r\n            }\r\n            else {\r\n                let slice = collection.slice(start, end);\r\n                this.saveState(id, collection, slice, start, end);\r\n                this.service.change.emit(id);\r\n                return slice;\r\n            }\r\n        }\r\n        else {\r\n            if (emitChange) {\r\n                this.service.change.emit(id);\r\n            }\r\n            // save the state for server-side collection to avoid null\r\n            // flash as new data loads.\r\n            this.saveState(id, collection, collection, start, end);\r\n            return collection;\r\n        }\r\n    }\r\n    /**\r\n     * Create an PaginationInstance object, using defaults for any optional properties not supplied.\r\n     */\r\n    createInstance(collection, config) {\r\n        this.checkConfig(config);\r\n        return {\r\n            id: config.id != null ? config.id : this.service.defaultId(),\r\n            itemsPerPage: +config.itemsPerPage || 0,\r\n            currentPage: +config.currentPage || 1,\r\n            totalItems: +config.totalItems || collection.length\r\n        };\r\n    }\r\n    /**\r\n     * Ensure the argument passed to the filter contains the required properties.\r\n     */\r\n    checkConfig(config) {\r\n        const required = ['itemsPerPage', 'currentPage'];\r\n        const missing = required.filter(prop => !(prop in config));\r\n        if (0 < missing.length) {\r\n            throw new Error(`PaginatePipe: Argument is missing the following required properties: ${missing.join(', ')}`);\r\n        }\r\n    }\r\n    /**\r\n     * To avoid returning a brand new array each time the pipe is run, we store the state of the sliced\r\n     * array for a given id. This means that the next time the pipe is run on this collection & id, we just\r\n     * need to check that the collection, start and end points are all identical, and if so, return the\r\n     * last sliced array.\r\n     */\r\n    saveState(id, collection, slice, start, end) {\r\n        this.state[id] = {\r\n            collection,\r\n            size: collection.length,\r\n            slice,\r\n            start,\r\n            end\r\n        };\r\n    }\r\n    /**\r\n     * For a given id, returns true if the collection, size, start and end values are identical.\r\n     */\r\n    stateIsIdentical(id, collection, start, end) {\r\n        let state = this.state[id];\r\n        if (!state) {\r\n            return false;\r\n        }\r\n        let isMetaDataIdentical = state.size === collection.length &&\r\n            state.start === start &&\r\n            state.end === end;\r\n        if (!isMetaDataIdentical) {\r\n            return false;\r\n        }\r\n        return state.slice.every((element, index) => element === collection[start + index]);\r\n    }\r\n}\r\nPaginatePipe.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginatePipe, deps: [{ token: PaginationService }], target: i0.ɵɵFactoryTarget.Pipe });\r\nPaginatePipe.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginatePipe, name: \"paginate\", pure: false });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginatePipe, decorators: [{\r\n            type: Pipe,\r\n            args: [{\r\n                    name: 'paginate',\r\n                    pure: false\r\n                }]\r\n        }], ctorParameters: function () { return [{ type: PaginationService }]; } });\n\n/**\r\n * The default template and styles for the pagination links are borrowed directly\r\n * from Zurb Foundation 6: http://foundation.zurb.com/sites/docs/pagination.html\r\n */\r\nconst DEFAULT_TEMPLATE = `\r\n    <pagination-template  #p=\"paginationApi\"\r\n                         [id]=\"id\"\r\n                         [maxSize]=\"maxSize\"\r\n                         (pageChange)=\"pageChange.emit($event)\"\r\n                         (pageBoundsCorrection)=\"pageBoundsCorrection.emit($event)\">\r\n    <nav role=\"navigation\" [attr.aria-label]=\"screenReaderPaginationLabel\">\r\n    <ul class=\"ngx-pagination\" \r\n        [class.responsive]=\"responsive\"\r\n        *ngIf=\"!(autoHide && p.pages.length <= 1)\">\r\n\r\n        <li class=\"pagination-previous\" [class.disabled]=\"p.isFirstPage()\" *ngIf=\"directionLinks\"> \r\n            <a tabindex=\"0\" *ngIf=\"1 < p.getCurrent()\" (keyup.enter)=\"p.previous()\" (click)=\"p.previous()\">\r\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\r\n            </a>\r\n            <span *ngIf=\"p.isFirstPage()\" aria-disabled=\"true\">\r\n                {{ previousLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\r\n            </span>\r\n        </li> \r\n\r\n        <li class=\"small-screen\">\r\n            {{ p.getCurrent() }} / {{ p.getLastPage() }}\r\n        </li>\r\n\r\n        <li [class.current]=\"p.getCurrent() === page.value\" \r\n            [class.ellipsis]=\"page.label === '...'\"\r\n            *ngFor=\"let page of p.pages; trackBy: trackByIndex\">\r\n            <a tabindex=\"0\" (keyup.enter)=\"p.setCurrent(page.value)\" (click)=\"p.setCurrent(page.value)\" *ngIf=\"p.getCurrent() !== page.value\">\r\n                <span class=\"show-for-sr\">{{ screenReaderPageLabel }} </span>\r\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span>\r\n            </a>\r\n            <ng-container *ngIf=\"p.getCurrent() === page.value\">\r\n              <span aria-live=\"polite\">\r\n                <span class=\"show-for-sr\">{{ screenReaderCurrentLabel }} </span>\r\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span> \r\n              </span>\r\n            </ng-container>\r\n        </li>\r\n\r\n        <li class=\"pagination-next\" [class.disabled]=\"p.isLastPage()\" *ngIf=\"directionLinks\">\r\n            <a tabindex=\"0\" *ngIf=\"!p.isLastPage()\" (keyup.enter)=\"p.next()\" (click)=\"p.next()\">\r\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\r\n            </a>\r\n            <span *ngIf=\"p.isLastPage()\" aria-disabled=\"true\">\r\n                 {{ nextLabel }} <span class=\"show-for-sr\">{{ screenReaderPageLabel }}</span>\r\n            </span>\r\n        </li>\r\n\r\n    </ul>\r\n    </nav>\r\n    </pagination-template>\r\n    `;\r\nconst DEFAULT_STYLES = `\r\n.ngx-pagination {\r\n  margin-left: 0;\r\n  margin-bottom: 1rem; }\r\n  .ngx-pagination::before, .ngx-pagination::after {\r\n    content: ' ';\r\n    display: table; }\r\n  .ngx-pagination::after {\r\n    clear: both; }\r\n  .ngx-pagination li {\r\n    -moz-user-select: none;\r\n    -webkit-user-select: none;\r\n    -ms-user-select: none;\r\n    margin-right: 0.0625rem;\r\n    border-radius: 0; }\r\n  .ngx-pagination li {\r\n    display: inline-block; }\r\n  .ngx-pagination a,\r\n  .ngx-pagination button {\r\n    color: #0a0a0a; \r\n    display: block;\r\n    padding: 0.1875rem 0.625rem;\r\n    border-radius: 0; }\r\n    .ngx-pagination a:hover,\r\n    .ngx-pagination button:hover {\r\n      background: #e6e6e6; }\r\n  .ngx-pagination .current {\r\n    padding: 0.1875rem 0.625rem;\r\n    background: #2199e8;\r\n    color: #fefefe;\r\n    cursor: default; }\r\n  .ngx-pagination .disabled {\r\n    padding: 0.1875rem 0.625rem;\r\n    color: #cacaca;\r\n    cursor: default; } \r\n    .ngx-pagination .disabled:hover {\r\n      background: transparent; }\r\n  .ngx-pagination a, .ngx-pagination button {\r\n    cursor: pointer; }\r\n\r\n.ngx-pagination .pagination-previous a::before,\r\n.ngx-pagination .pagination-previous.disabled::before { \r\n  content: '«';\r\n  display: inline-block;\r\n  margin-right: 0.5rem; }\r\n\r\n.ngx-pagination .pagination-next a::after,\r\n.ngx-pagination .pagination-next.disabled::after {\r\n  content: '»';\r\n  display: inline-block;\r\n  margin-left: 0.5rem; }\r\n\r\n.ngx-pagination .show-for-sr {\r\n  position: absolute !important;\r\n  width: 1px;\r\n  height: 1px;\r\n  overflow: hidden;\r\n  clip: rect(0, 0, 0, 0); }\r\n.ngx-pagination .small-screen {\r\n  display: none; }\r\n@media screen and (max-width: 601px) {\r\n  .ngx-pagination.responsive .small-screen {\r\n    display: inline-block; } \r\n  .ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next) {\r\n    display: none; }\r\n}\r\n  `;\n\n/**\r\n * This directive is what powers all pagination controls components, including the default one.\r\n * It exposes an API which is hooked up to the PaginationService to keep the PaginatePipe in sync\r\n * with the pagination controls.\r\n */\r\nclass PaginationControlsDirective {\r\n    constructor(service, changeDetectorRef) {\r\n        this.service = service;\r\n        this.changeDetectorRef = changeDetectorRef;\r\n        this.maxSize = 7;\r\n        this.pageChange = new EventEmitter();\r\n        this.pageBoundsCorrection = new EventEmitter();\r\n        this.pages = [];\r\n        this.changeSub = this.service.change\r\n            .subscribe(id => {\r\n            if (this.id === id) {\r\n                this.updatePageLinks();\r\n                this.changeDetectorRef.markForCheck();\r\n                this.changeDetectorRef.detectChanges();\r\n            }\r\n        });\r\n    }\r\n    ngOnInit() {\r\n        if (this.id === undefined) {\r\n            this.id = this.service.defaultId();\r\n        }\r\n        this.updatePageLinks();\r\n    }\r\n    ngOnChanges(changes) {\r\n        this.updatePageLinks();\r\n    }\r\n    ngOnDestroy() {\r\n        this.changeSub.unsubscribe();\r\n    }\r\n    /**\r\n     * Go to the previous page\r\n     */\r\n    previous() {\r\n        this.checkValidId();\r\n        this.setCurrent(this.getCurrent() - 1);\r\n    }\r\n    /**\r\n     * Go to the next page\r\n     */\r\n    next() {\r\n        this.checkValidId();\r\n        this.setCurrent(this.getCurrent() + 1);\r\n    }\r\n    /**\r\n     * Returns true if current page is first page\r\n     */\r\n    isFirstPage() {\r\n        return this.getCurrent() === 1;\r\n    }\r\n    /**\r\n     * Returns true if current page is last page\r\n     */\r\n    isLastPage() {\r\n        return this.getLastPage() === this.getCurrent();\r\n    }\r\n    /**\r\n     * Set the current page number.\r\n     */\r\n    setCurrent(page) {\r\n        this.pageChange.emit(page);\r\n    }\r\n    /**\r\n     * Get the current page number.\r\n     */\r\n    getCurrent() {\r\n        return this.service.getCurrentPage(this.id);\r\n    }\r\n    /**\r\n     * Returns the last page number\r\n     */\r\n    getLastPage() {\r\n        let inst = this.service.getInstance(this.id);\r\n        if (inst.totalItems < 1) {\r\n            // when there are 0 or fewer (an error case) items, there are no \"pages\" as such,\r\n            // but it makes sense to consider a single, empty page as the last page.\r\n            return 1;\r\n        }\r\n        return Math.ceil(inst.totalItems / inst.itemsPerPage);\r\n    }\r\n    getTotalItems() {\r\n        return this.service.getInstance(this.id).totalItems;\r\n    }\r\n    checkValidId() {\r\n        if (this.service.getInstance(this.id).id == null) {\r\n            console.warn(`PaginationControlsDirective: the specified id \"${this.id}\" does not match any registered PaginationInstance`);\r\n        }\r\n    }\r\n    /**\r\n     * Updates the page links and checks that the current page is valid. Should run whenever the\r\n     * PaginationService.change stream emits a value matching the current ID, or when any of the\r\n     * input values changes.\r\n     */\r\n    updatePageLinks() {\r\n        let inst = this.service.getInstance(this.id);\r\n        const correctedCurrentPage = this.outOfBoundCorrection(inst);\r\n        if (correctedCurrentPage !== inst.currentPage) {\r\n            setTimeout(() => {\r\n                this.pageBoundsCorrection.emit(correctedCurrentPage);\r\n                this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\r\n            });\r\n        }\r\n        else {\r\n            this.pages = this.createPageArray(inst.currentPage, inst.itemsPerPage, inst.totalItems, this.maxSize);\r\n        }\r\n    }\r\n    /**\r\n     * Checks that the instance.currentPage property is within bounds for the current page range.\r\n     * If not, return a correct value for currentPage, or the current value if OK.\r\n     */\r\n    outOfBoundCorrection(instance) {\r\n        const totalPages = Math.ceil(instance.totalItems / instance.itemsPerPage);\r\n        if (totalPages < instance.currentPage && 0 < totalPages) {\r\n            return totalPages;\r\n        }\r\n        else if (instance.currentPage < 1) {\r\n            return 1;\r\n        }\r\n        return instance.currentPage;\r\n    }\r\n    /**\r\n     * Returns an array of Page objects to use in the pagination controls.\r\n     */\r\n    createPageArray(currentPage, itemsPerPage, totalItems, paginationRange) {\r\n        // paginationRange could be a string if passed from attribute, so cast to number.\r\n        paginationRange = +paginationRange;\r\n        let pages = [];\r\n        // Return 1 as default page number\r\n        // Make sense to show 1 instead of empty when there are no items\r\n        const totalPages = Math.max(Math.ceil(totalItems / itemsPerPage), 1);\r\n        const halfWay = Math.ceil(paginationRange / 2);\r\n        const isStart = currentPage <= halfWay;\r\n        const isEnd = totalPages - halfWay < currentPage;\r\n        const isMiddle = !isStart && !isEnd;\r\n        let ellipsesNeeded = paginationRange < totalPages;\r\n        let i = 1;\r\n        while (i <= totalPages && i <= paginationRange) {\r\n            let label;\r\n            let pageNumber = this.calculatePageNumber(i, currentPage, paginationRange, totalPages);\r\n            let openingEllipsesNeeded = (i === 2 && (isMiddle || isEnd));\r\n            let closingEllipsesNeeded = (i === paginationRange - 1 && (isMiddle || isStart));\r\n            if (ellipsesNeeded && (openingEllipsesNeeded || closingEllipsesNeeded)) {\r\n                label = '...';\r\n            }\r\n            else {\r\n                label = pageNumber;\r\n            }\r\n            pages.push({\r\n                label: label,\r\n                value: pageNumber\r\n            });\r\n            i++;\r\n        }\r\n        return pages;\r\n    }\r\n    /**\r\n     * Given the position in the sequence of pagination links [i],\r\n     * figure out what page number corresponds to that position.\r\n     */\r\n    calculatePageNumber(i, currentPage, paginationRange, totalPages) {\r\n        let halfWay = Math.ceil(paginationRange / 2);\r\n        if (i === paginationRange) {\r\n            return totalPages;\r\n        }\r\n        else if (i === 1) {\r\n            return i;\r\n        }\r\n        else if (paginationRange < totalPages) {\r\n            if (totalPages - halfWay < currentPage) {\r\n                return totalPages - paginationRange + i;\r\n            }\r\n            else if (halfWay < currentPage) {\r\n                return currentPage - halfWay + i;\r\n            }\r\n            else {\r\n                return i;\r\n            }\r\n        }\r\n        else {\r\n            return i;\r\n        }\r\n    }\r\n}\r\nPaginationControlsDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginationControlsDirective, deps: [{ token: PaginationService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\r\nPaginationControlsDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.9\", type: PaginationControlsDirective, selector: \"pagination-template,[pagination-template]\", inputs: { id: \"id\", maxSize: \"maxSize\" }, outputs: { pageChange: \"pageChange\", pageBoundsCorrection: \"pageBoundsCorrection\" }, exportAs: [\"paginationApi\"], usesOnChanges: true, ngImport: i0 });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginationControlsDirective, decorators: [{\r\n            type: Directive,\r\n            args: [{\r\n                    selector: 'pagination-template,[pagination-template]',\r\n                    exportAs: 'paginationApi'\r\n                }]\r\n        }], ctorParameters: function () { return [{ type: PaginationService }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { id: [{\r\n                type: Input\r\n            }], maxSize: [{\r\n                type: Input\r\n            }], pageChange: [{\r\n                type: Output\r\n            }], pageBoundsCorrection: [{\r\n                type: Output\r\n            }] } });\n\nfunction coerceToBoolean(input) {\r\n    return !!input && input !== 'false';\r\n}\r\n/**\r\n * The default pagination controls component. Actually just a default implementation of a custom template.\r\n */\r\nclass PaginationControlsComponent {\r\n    constructor() {\r\n        this.maxSize = 7;\r\n        this.previousLabel = 'Previous';\r\n        this.nextLabel = 'Next';\r\n        this.screenReaderPaginationLabel = 'Pagination';\r\n        this.screenReaderPageLabel = 'page';\r\n        this.screenReaderCurrentLabel = `You're on page`;\r\n        this.pageChange = new EventEmitter();\r\n        this.pageBoundsCorrection = new EventEmitter();\r\n        this._directionLinks = true;\r\n        this._autoHide = false;\r\n        this._responsive = false;\r\n    }\r\n    get directionLinks() {\r\n        return this._directionLinks;\r\n    }\r\n    set directionLinks(value) {\r\n        this._directionLinks = coerceToBoolean(value);\r\n    }\r\n    get autoHide() {\r\n        return this._autoHide;\r\n    }\r\n    set autoHide(value) {\r\n        this._autoHide = coerceToBoolean(value);\r\n    }\r\n    get responsive() {\r\n        return this._responsive;\r\n    }\r\n    set responsive(value) {\r\n        this._responsive = coerceToBoolean(value);\r\n    }\r\n    trackByIndex(index) {\r\n        return index;\r\n    }\r\n}\r\nPaginationControlsComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginationControlsComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\r\nPaginationControlsComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"12.0.0\", version: \"13.3.9\", type: PaginationControlsComponent, selector: \"pagination-controls\", inputs: { id: \"id\", maxSize: \"maxSize\", directionLinks: \"directionLinks\", autoHide: \"autoHide\", responsive: \"responsive\", previousLabel: \"previousLabel\", nextLabel: \"nextLabel\", screenReaderPaginationLabel: \"screenReaderPaginationLabel\", screenReaderPageLabel: \"screenReaderPageLabel\", screenReaderCurrentLabel: \"screenReaderCurrentLabel\" }, outputs: { pageChange: \"pageChange\", pageBoundsCorrection: \"pageBoundsCorrection\" }, ngImport: i0, template: \"\\n    <pagination-template  #p=\\\"paginationApi\\\"\\n                         [id]=\\\"id\\\"\\n                         [maxSize]=\\\"maxSize\\\"\\n                         (pageChange)=\\\"pageChange.emit($event)\\\"\\n                         (pageBoundsCorrection)=\\\"pageBoundsCorrection.emit($event)\\\">\\n    <nav role=\\\"navigation\\\" [attr.aria-label]=\\\"screenReaderPaginationLabel\\\">\\n    <ul class=\\\"ngx-pagination\\\" \\n        [class.responsive]=\\\"responsive\\\"\\n        *ngIf=\\\"!(autoHide && p.pages.length <= 1)\\\">\\n\\n        <li class=\\\"pagination-previous\\\" [class.disabled]=\\\"p.isFirstPage()\\\" *ngIf=\\\"directionLinks\\\"> \\n            <a tabindex=\\\"0\\\" *ngIf=\\\"1 < p.getCurrent()\\\" (keyup.enter)=\\\"p.previous()\\\" (click)=\\\"p.previous()\\\">\\n                {{ previousLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </a>\\n            <span *ngIf=\\\"p.isFirstPage()\\\" aria-disabled=\\\"true\\\">\\n                {{ previousLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </span>\\n        </li> \\n\\n        <li class=\\\"small-screen\\\">\\n            {{ p.getCurrent() }} / {{ p.getLastPage() }}\\n        </li>\\n\\n        <li [class.current]=\\\"p.getCurrent() === page.value\\\" \\n            [class.ellipsis]=\\\"page.label === '...'\\\"\\n            *ngFor=\\\"let page of p.pages; trackBy: trackByIndex\\\">\\n            <a tabindex=\\\"0\\\" (keyup.enter)=\\\"p.setCurrent(page.value)\\\" (click)=\\\"p.setCurrent(page.value)\\\" *ngIf=\\\"p.getCurrent() !== page.value\\\">\\n                <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }} </span>\\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span>\\n            </a>\\n            <ng-container *ngIf=\\\"p.getCurrent() === page.value\\\">\\n              <span aria-live=\\\"polite\\\">\\n                <span class=\\\"show-for-sr\\\">{{ screenReaderCurrentLabel }} </span>\\n                <span>{{ (page.label === '...') ? page.label : (page.label | number:'') }}</span> \\n              </span>\\n            </ng-container>\\n        </li>\\n\\n        <li class=\\\"pagination-next\\\" [class.disabled]=\\\"p.isLastPage()\\\" *ngIf=\\\"directionLinks\\\">\\n            <a tabindex=\\\"0\\\" *ngIf=\\\"!p.isLastPage()\\\" (keyup.enter)=\\\"p.next()\\\" (click)=\\\"p.next()\\\">\\n                 {{ nextLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </a>\\n            <span *ngIf=\\\"p.isLastPage()\\\" aria-disabled=\\\"true\\\">\\n                 {{ nextLabel }} <span class=\\\"show-for-sr\\\">{{ screenReaderPageLabel }}</span>\\n            </span>\\n        </li>\\n\\n    </ul>\\n    </nav>\\n    </pagination-template>\\n    \", isInline: true, styles: [\".ngx-pagination{margin-left:0;margin-bottom:1rem}.ngx-pagination:before,.ngx-pagination:after{content:\\\" \\\";display:table}.ngx-pagination:after{clear:both}.ngx-pagination li{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;margin-right:.0625rem;border-radius:0}.ngx-pagination li{display:inline-block}.ngx-pagination a,.ngx-pagination button{color:#0a0a0a;display:block;padding:.1875rem .625rem;border-radius:0}.ngx-pagination a:hover,.ngx-pagination button:hover{background:#e6e6e6}.ngx-pagination .current{padding:.1875rem .625rem;background:#2199e8;color:#fefefe;cursor:default}.ngx-pagination .disabled{padding:.1875rem .625rem;color:#cacaca;cursor:default}.ngx-pagination .disabled:hover{background:transparent}.ngx-pagination a,.ngx-pagination button{cursor:pointer}.ngx-pagination .pagination-previous a:before,.ngx-pagination .pagination-previous.disabled:before{content:\\\"\\\\ab\\\";display:inline-block;margin-right:.5rem}.ngx-pagination .pagination-next a:after,.ngx-pagination .pagination-next.disabled:after{content:\\\"\\\\bb\\\";display:inline-block;margin-left:.5rem}.ngx-pagination .show-for-sr{position:absolute!important;width:1px;height:1px;overflow:hidden;clip:rect(0,0,0,0)}.ngx-pagination .small-screen{display:none}@media screen and (max-width: 601px){.ngx-pagination.responsive .small-screen{display:inline-block}.ngx-pagination.responsive li:not(.small-screen):not(.pagination-previous):not(.pagination-next){display:none}}\\n\"], directives: [{ type: PaginationControlsDirective, selector: \"pagination-template,[pagination-template]\", inputs: [\"id\", \"maxSize\"], outputs: [\"pageChange\", \"pageBoundsCorrection\"], exportAs: [\"paginationApi\"] }, { type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], pipes: { \"number\": i2.DecimalPipe }, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: PaginationControlsComponent, decorators: [{\r\n            type: Component,\r\n            args: [{\r\n                    selector: 'pagination-controls',\r\n                    template: DEFAULT_TEMPLATE,\r\n                    styles: [DEFAULT_STYLES],\r\n                    changeDetection: ChangeDetectionStrategy.OnPush,\r\n                    encapsulation: ViewEncapsulation.None\r\n                }]\r\n        }], propDecorators: { id: [{\r\n                type: Input\r\n            }], maxSize: [{\r\n                type: Input\r\n            }], directionLinks: [{\r\n                type: Input\r\n            }], autoHide: [{\r\n                type: Input\r\n            }], responsive: [{\r\n                type: Input\r\n            }], previousLabel: [{\r\n                type: Input\r\n            }], nextLabel: [{\r\n                type: Input\r\n            }], screenReaderPaginationLabel: [{\r\n                type: Input\r\n            }], screenReaderPageLabel: [{\r\n                type: Input\r\n            }], screenReaderCurrentLabel: [{\r\n                type: Input\r\n            }], pageChange: [{\r\n                type: Output\r\n            }], pageBoundsCorrection: [{\r\n                type: Output\r\n            }] } });\n\nclass NgxPaginationModule {\r\n}\r\nNgxPaginationModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: NgxPaginationModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\r\nNgxPaginationModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: NgxPaginationModule, declarations: [PaginatePipe,\r\n        PaginationControlsComponent,\r\n        PaginationControlsDirective], imports: [CommonModule], exports: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective] });\r\nNgxPaginationModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: NgxPaginationModule, providers: [PaginationService], imports: [[CommonModule]] });\r\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.9\", ngImport: i0, type: NgxPaginationModule, decorators: [{\r\n            type: NgModule,\r\n            args: [{\r\n                    imports: [CommonModule],\r\n                    declarations: [\r\n                        PaginatePipe,\r\n                        PaginationControlsComponent,\r\n                        PaginationControlsDirective\r\n                    ],\r\n                    providers: [PaginationService],\r\n                    exports: [PaginatePipe, PaginationControlsComponent, PaginationControlsDirective]\r\n                }]\r\n        }] });\n\n/*\r\n * Public API Surface of ngx-pagination\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NgxPaginationModule, PaginatePipe, PaginationControlsComponent, PaginationControlsDirective, PaginationService };\n"]}, "metadata": {}, "sourceType": "module"}
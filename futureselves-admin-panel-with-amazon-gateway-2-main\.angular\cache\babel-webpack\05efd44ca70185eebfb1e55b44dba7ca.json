{"ast": null, "code": "import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, ElementRef, ApplicationRef, InjectionToken, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { take, takeUntil, takeWhile } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\n\nclass BlockScrollStrategy {\n  constructor(_viewportRuler, document) {\n    this._viewportRuler = _viewportRuler;\n    this._previousHTMLStyles = {\n      top: '',\n      left: ''\n    };\n    this._isEnabled = false;\n    this._document = document;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n\n\n  attach() {}\n  /** Blocks page-level scroll while the attached overlay is open. */\n\n\n  enable() {\n    if (this._canBeEnabled()) {\n      const root = this._document.documentElement;\n      this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition(); // Cache the previous inline styles in case the user had set them.\n\n      this._previousHTMLStyles.left = root.style.left || '';\n      this._previousHTMLStyles.top = root.style.top || ''; // Note: we're using the `html` node, instead of the `body`, because the `body` may\n      // have the user agent margin, whereas the `html` is guaranteed not to have one.\n\n      root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n      root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n      root.classList.add('cdk-global-scrollblock');\n      this._isEnabled = true;\n    }\n  }\n  /** Unblocks page-level scroll while the attached overlay is open. */\n\n\n  disable() {\n    if (this._isEnabled) {\n      const html = this._document.documentElement;\n      const body = this._document.body;\n      const htmlStyle = html.style;\n      const bodyStyle = body.style;\n      const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n      const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n      this._isEnabled = false;\n      htmlStyle.left = this._previousHTMLStyles.left;\n      htmlStyle.top = this._previousHTMLStyles.top;\n      html.classList.remove('cdk-global-scrollblock'); // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n      // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n      // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n      // because it can throw off feature detections in `supportsScrollBehavior` which\n      // checks for `'scrollBehavior' in documentElement.style`.\n\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n      }\n\n      window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n\n      if (scrollBehaviorSupported) {\n        htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n        bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n      }\n    }\n  }\n\n  _canBeEnabled() {\n    // Since the scroll strategies can't be singletons, we have to use a global CSS class\n    // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n    // scrolling multiple times.\n    const html = this._document.documentElement;\n\n    if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n      return false;\n    }\n\n    const body = this._document.body;\n\n    const viewport = this._viewportRuler.getViewportSize();\n\n    return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\n\n\nfunction getMatScrollStrategyAlreadyAttachedError() {\n  return Error(`Scroll strategy has already been attached.`);\n}\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\n\n\nclass CloseScrollStrategy {\n  constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._config = _config;\n    this._scrollSubscription = null;\n    /** Detaches the overlay ref and disables the scroll strategy. */\n\n    this._detach = () => {\n      this.disable();\n\n      if (this._overlayRef.hasAttached()) {\n        this._ngZone.run(() => this._overlayRef.detach());\n      }\n    };\n  }\n  /** Attaches this scroll strategy to an overlay. */\n\n\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n  /** Enables the closing of the attached overlay on scroll. */\n\n\n  enable() {\n    if (this._scrollSubscription) {\n      return;\n    }\n\n    const stream = this._scrollDispatcher.scrolled(0);\n\n    if (this._config && this._config.threshold && this._config.threshold > 1) {\n      this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n      this._scrollSubscription = stream.subscribe(() => {\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n\n        if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n          this._detach();\n        } else {\n          this._overlayRef.updatePosition();\n        }\n      });\n    } else {\n      this._scrollSubscription = stream.subscribe(this._detach);\n    }\n  }\n  /** Disables the closing the attached overlay on scroll. */\n\n\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n\n      this._scrollSubscription = null;\n    }\n  }\n\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Scroll strategy that doesn't do anything. */\n\n\nclass NoopScrollStrategy {\n  /** Does nothing, as this scroll strategy is a no-op. */\n  enable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n\n\n  disable() {}\n  /** Does nothing, as this scroll strategy is a no-op. */\n\n\n  attach() {}\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\n\n\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n  return scrollContainers.some(containerBounds => {\n    const outsideAbove = element.bottom < containerBounds.top;\n    const outsideBelow = element.top > containerBounds.bottom;\n    const outsideLeft = element.right < containerBounds.left;\n    const outsideRight = element.left > containerBounds.right;\n    return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n  });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\n\n\nfunction isElementClippedByScrolling(element, scrollContainers) {\n  return scrollContainers.some(scrollContainerRect => {\n    const clippedAbove = element.top < scrollContainerRect.top;\n    const clippedBelow = element.bottom > scrollContainerRect.bottom;\n    const clippedLeft = element.left < scrollContainerRect.left;\n    const clippedRight = element.right > scrollContainerRect.right;\n    return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n  });\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\n\n\nclass RepositionScrollStrategy {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    this._config = _config;\n    this._scrollSubscription = null;\n  }\n  /** Attaches this scroll strategy to an overlay. */\n\n\n  attach(overlayRef) {\n    if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatScrollStrategyAlreadyAttachedError();\n    }\n\n    this._overlayRef = overlayRef;\n  }\n  /** Enables repositioning of the attached overlay on scroll. */\n\n\n  enable() {\n    if (!this._scrollSubscription) {\n      const throttle = this._config ? this._config.scrollThrottle : 0;\n      this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n        this._overlayRef.updatePosition(); // TODO(crisbeto): make `close` on by default once all components can handle it.\n\n\n        if (this._config && this._config.autoClose) {\n          const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n\n          const {\n            width,\n            height\n          } = this._viewportRuler.getViewportSize(); // TODO(crisbeto): include all ancestor scroll containers here once\n          // we have a way of exposing the trigger element to the scroll strategy.\n\n\n          const parentRects = [{\n            width,\n            height,\n            bottom: height,\n            right: width,\n            top: 0,\n            left: 0\n          }];\n\n          if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n            this.disable();\n\n            this._ngZone.run(() => this._overlayRef.detach());\n          }\n        }\n      });\n    }\n  }\n  /** Disables repositioning of the attached overlay on scroll. */\n\n\n  disable() {\n    if (this._scrollSubscription) {\n      this._scrollSubscription.unsubscribe();\n\n      this._scrollSubscription = null;\n    }\n  }\n\n  detach() {\n    this.disable();\n    this._overlayRef = null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\n\n\nclass ScrollStrategyOptions {\n  constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n    this._scrollDispatcher = _scrollDispatcher;\n    this._viewportRuler = _viewportRuler;\n    this._ngZone = _ngZone;\n    /** Do nothing on scroll. */\n\n    this.noop = () => new NoopScrollStrategy();\n    /**\n     * Close the overlay as soon as the user scrolls.\n     * @param config Configuration to be used inside the scroll strategy.\n     */\n\n\n    this.close = config => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n    /** Block scrolling. */\n\n\n    this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n    /**\n     * Update the overlay's position on scroll.\n     * @param config Configuration to be used inside the scroll strategy.\n     * Allows debouncing the reposition calls.\n     */\n\n\n    this.reposition = config => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n\n    this._document = document;\n  }\n\n}\n\nScrollStrategyOptions.ɵfac = function ScrollStrategyOptions_Factory(t) {\n  return new (t || ScrollStrategyOptions)(i0.ɵɵinject(i1.ScrollDispatcher), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n};\n\nScrollStrategyOptions.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ScrollStrategyOptions,\n  factory: ScrollStrategyOptions.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollStrategyOptions, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.ScrollDispatcher\n    }, {\n      type: i1.ViewportRuler\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Initial configuration used when creating an overlay. */\n\n\nclass OverlayConfig {\n  constructor(config) {\n    /** Strategy to be used when handling scroll events while the overlay is open. */\n    this.scrollStrategy = new NoopScrollStrategy();\n    /** Custom class to add to the overlay pane. */\n\n    this.panelClass = '';\n    /** Whether the overlay has a backdrop. */\n\n    this.hasBackdrop = false;\n    /** Custom class to add to the backdrop */\n\n    this.backdropClass = 'cdk-overlay-dark-backdrop';\n    /**\n     * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n     * Note that this usually doesn't include clicking on links (unless the user is using\n     * the `HashLocationStrategy`).\n     */\n\n    this.disposeOnNavigation = false;\n\n    if (config) {\n      // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n      // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n      // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n      const configKeys = Object.keys(config);\n\n      for (const key of configKeys) {\n        if (config[key] !== undefined) {\n          // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n          // as \"I don't know *which* key this is, so the only valid value is the intersection\n          // of all the posible values.\" In this case, that happens to be `undefined`. TypeScript\n          // is not smart enough to see that the right-hand-side is actually an access of the same\n          // exact type with the same exact key, meaning that the value type must be identical.\n          // So we use `any` to work around this.\n          this[key] = config[key];\n        }\n      }\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** The points of the origin element and the overlay element to connect. */\n\n\nclass ConnectionPositionPair {\n  constructor(origin, overlay,\n  /** Offset along the X axis. */\n  offsetX,\n  /** Offset along the Y axis. */\n  offsetY,\n  /** Class(es) to be applied to the panel while this position is active. */\n  panelClass) {\n    this.offsetX = offsetX;\n    this.offsetY = offsetY;\n    this.panelClass = panelClass;\n    this.originX = origin.originX;\n    this.originY = origin.originY;\n    this.overlayX = overlay.overlayX;\n    this.overlayY = overlay.overlayY;\n  }\n\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\n\n\nclass ScrollingVisibility {}\n/** The change event emitted by the strategy when a fallback position is used. */\n\n\nclass ConnectedOverlayPositionChange {\n  constructor(\n  /** The position used as a result of this change. */\n  connectionPair,\n  /** @docs-private */\n  scrollableViewProperties) {\n    this.connectionPair = connectionPair;\n    this.scrollableViewProperties = scrollableViewProperties;\n  }\n\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\n\n\nfunction validateVerticalPosition(property, value) {\n  if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"top\", \"bottom\" or \"center\".`);\n  }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\n\n\nfunction validateHorizontalPosition(property, value) {\n  if (value !== 'start' && value !== 'end' && value !== 'center') {\n    throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` + `Expected \"start\", \"end\" or \"center\".`);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n\n\nclass BaseOverlayDispatcher {\n  constructor(document) {\n    /** Currently attached overlays in the order they were attached. */\n    this._attachedOverlays = [];\n    this._document = document;\n  }\n\n  ngOnDestroy() {\n    this.detach();\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n\n\n  add(overlayRef) {\n    // Ensure that we don't get the same overlay multiple times.\n    this.remove(overlayRef);\n\n    this._attachedOverlays.push(overlayRef);\n  }\n  /** Remove an overlay from the list of attached overlay refs. */\n\n\n  remove(overlayRef) {\n    const index = this._attachedOverlays.indexOf(overlayRef);\n\n    if (index > -1) {\n      this._attachedOverlays.splice(index, 1);\n    } // Remove the global listener once there are no more overlays.\n\n\n    if (this._attachedOverlays.length === 0) {\n      this.detach();\n    }\n  }\n\n}\n\nBaseOverlayDispatcher.ɵfac = function BaseOverlayDispatcher_Factory(t) {\n  return new (t || BaseOverlayDispatcher)(i0.ɵɵinject(DOCUMENT));\n};\n\nBaseOverlayDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BaseOverlayDispatcher,\n  factory: BaseOverlayDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseOverlayDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n\n\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n  constructor(document,\n  /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._ngZone = _ngZone;\n    /** Keyboard event listener that will be attached to the body. */\n\n    this._keydownListener = event => {\n      const overlays = this._attachedOverlays;\n\n      for (let i = overlays.length - 1; i > -1; i--) {\n        // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n        // We want to target the most recent overlay, rather than trying to match where the event came\n        // from, because some components might open an overlay, but keep focus on a trigger element\n        // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n        // because we don't want overlays that don't handle keyboard events to block the ones below\n        // them that do.\n        if (overlays[i]._keydownEvents.observers.length > 0) {\n          const keydownEvents = overlays[i]._keydownEvents;\n          /** @breaking-change 14.0.0 _ngZone will be required. */\n\n          if (this._ngZone) {\n            this._ngZone.run(() => keydownEvents.next(event));\n          } else {\n            keydownEvents.next(event);\n          }\n\n          break;\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n\n\n  add(overlayRef) {\n    super.add(overlayRef); // Lazily start dispatcher once first overlay is added\n\n    if (!this._isAttached) {\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n      } else {\n        this._document.body.addEventListener('keydown', this._keydownListener);\n      }\n\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n\n\n  detach() {\n    if (this._isAttached) {\n      this._document.body.removeEventListener('keydown', this._keydownListener);\n\n      this._isAttached = false;\n    }\n  }\n\n}\n\nOverlayKeyboardDispatcher.ɵfac = function OverlayKeyboardDispatcher_Factory(t) {\n  return new (t || OverlayKeyboardDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone, 8));\n};\n\nOverlayKeyboardDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayKeyboardDispatcher,\n  factory: OverlayKeyboardDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayKeyboardDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\n\n\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n  constructor(document, _platform,\n  /** @breaking-change 14.0.0 _ngZone will be required. */\n  _ngZone) {\n    super(document);\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._cursorStyleIsSet = false;\n    /** Store pointerdown event target to track origin of click. */\n\n    this._pointerDownListener = event => {\n      this._pointerDownEventTarget = _getEventTarget(event);\n    };\n    /** Click event listener that will be attached to the body propagate phase. */\n\n\n    this._clickListener = event => {\n      const target = _getEventTarget(event); // In case of a click event, we want to check the origin of the click\n      // (e.g. in case where a user starts a click inside the overlay and\n      // releases the click outside of it).\n      // This is done by using the event target of the preceding pointerdown event.\n      // Every click event caused by a pointer device has a preceding pointerdown\n      // event, unless the click was programmatically triggered (e.g. in a unit test).\n\n\n      const origin = event.type === 'click' && this._pointerDownEventTarget ? this._pointerDownEventTarget : target; // Reset the stored pointerdown event target, to avoid having it interfere\n      // in subsequent events.\n\n      this._pointerDownEventTarget = null; // We copy the array because the original may be modified asynchronously if the\n      // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n      // the for loop.\n\n      const overlays = this._attachedOverlays.slice(); // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n      // We want to target all overlays for which the click could be considered as outside click.\n      // As soon as we reach an overlay for which the click is not outside click we break off\n      // the loop.\n\n\n      for (let i = overlays.length - 1; i > -1; i--) {\n        const overlayRef = overlays[i];\n\n        if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n          continue;\n        } // If it's a click inside the overlay, just break - we should do nothing\n        // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n        // and proceed with the next overlay\n\n\n        if (overlayRef.overlayElement.contains(target) || overlayRef.overlayElement.contains(origin)) {\n          break;\n        }\n\n        const outsidePointerEvents = overlayRef._outsidePointerEvents;\n        /** @breaking-change 14.0.0 _ngZone will be required. */\n\n        if (this._ngZone) {\n          this._ngZone.run(() => outsidePointerEvents.next(event));\n        } else {\n          outsidePointerEvents.next(event);\n        }\n      }\n    };\n  }\n  /** Add a new overlay to the list of attached overlay refs. */\n\n\n  add(overlayRef) {\n    super.add(overlayRef); // Safari on iOS does not generate click events for non-interactive\n    // elements. However, we want to receive a click for any element outside\n    // the overlay. We can force a \"clickable\" state by setting\n    // `cursor: pointer` on the document body. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n    // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n\n    if (!this._isAttached) {\n      const body = this._document.body;\n      /** @breaking-change 14.0.0 _ngZone will be required. */\n\n      if (this._ngZone) {\n        this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n      } else {\n        this._addEventListeners(body);\n      } // click event is not fired on iOS. To make element \"clickable\" we are\n      // setting the cursor to pointer\n\n\n      if (this._platform.IOS && !this._cursorStyleIsSet) {\n        this._cursorOriginalValue = body.style.cursor;\n        body.style.cursor = 'pointer';\n        this._cursorStyleIsSet = true;\n      }\n\n      this._isAttached = true;\n    }\n  }\n  /** Detaches the global keyboard event listener. */\n\n\n  detach() {\n    if (this._isAttached) {\n      const body = this._document.body;\n      body.removeEventListener('pointerdown', this._pointerDownListener, true);\n      body.removeEventListener('click', this._clickListener, true);\n      body.removeEventListener('auxclick', this._clickListener, true);\n      body.removeEventListener('contextmenu', this._clickListener, true);\n\n      if (this._platform.IOS && this._cursorStyleIsSet) {\n        body.style.cursor = this._cursorOriginalValue;\n        this._cursorStyleIsSet = false;\n      }\n\n      this._isAttached = false;\n    }\n  }\n\n  _addEventListeners(body) {\n    body.addEventListener('pointerdown', this._pointerDownListener, true);\n    body.addEventListener('click', this._clickListener, true);\n    body.addEventListener('auxclick', this._clickListener, true);\n    body.addEventListener('contextmenu', this._clickListener, true);\n  }\n\n}\n\nOverlayOutsideClickDispatcher.ɵfac = function OverlayOutsideClickDispatcher_Factory(t) {\n  return new (t || OverlayOutsideClickDispatcher)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(i0.NgZone, 8));\n};\n\nOverlayOutsideClickDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayOutsideClickDispatcher,\n  factory: OverlayOutsideClickDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayOutsideClickDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: i0.NgZone,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Container inside which all overlays will render. */\n\n\nclass OverlayContainer {\n  constructor(document, _platform) {\n    this._platform = _platform;\n    this._document = document;\n  }\n\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n\n\n  getContainerElement() {\n    if (!this._containerElement) {\n      this._createContainer();\n    }\n\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n\n\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container'; // TODO(crisbeto): remove the testing check once we have an overlay testing\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`); // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n\n\n      for (let i = 0; i < oppositePlatformContainers.length; i++) {\n        oppositePlatformContainers[i].remove();\n      }\n    }\n\n    const container = this._document.createElement('div');\n\n    container.classList.add(containerClass); // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n\n    this._document.body.appendChild(container);\n\n    this._containerElement = container;\n  }\n\n}\n\nOverlayContainer.ɵfac = function OverlayContainer_Factory(t) {\n  return new (t || OverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n};\n\nOverlayContainer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayContainer,\n  factory: OverlayContainer.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\n\n\nclass OverlayRef {\n  constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher) {\n    this._portalOutlet = _portalOutlet;\n    this._host = _host;\n    this._pane = _pane;\n    this._config = _config;\n    this._ngZone = _ngZone;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._document = _document;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n    this._backdropElement = null;\n    this._backdropClick = new Subject();\n    this._attachments = new Subject();\n    this._detachments = new Subject();\n    this._locationChanges = Subscription.EMPTY;\n\n    this._backdropClickHandler = event => this._backdropClick.next(event);\n\n    this._backdropTransitionendHandler = event => {\n      this._disposeBackdrop(event.target);\n    };\n    /** Stream of keydown events dispatched to this overlay. */\n\n\n    this._keydownEvents = new Subject();\n    /** Stream of mouse outside events dispatched to this overlay. */\n\n    this._outsidePointerEvents = new Subject();\n\n    if (_config.scrollStrategy) {\n      this._scrollStrategy = _config.scrollStrategy;\n\n      this._scrollStrategy.attach(this);\n    }\n\n    this._positionStrategy = _config.positionStrategy;\n  }\n  /** The overlay's HTML element */\n\n\n  get overlayElement() {\n    return this._pane;\n  }\n  /** The overlay's backdrop HTML element. */\n\n\n  get backdropElement() {\n    return this._backdropElement;\n  }\n  /**\n   * Wrapper around the panel element. Can be used for advanced\n   * positioning where a wrapper with specific styling is\n   * required around the overlay pane.\n   */\n\n\n  get hostElement() {\n    return this._host;\n  }\n  /**\n   * Attaches content, given via a Portal, to the overlay.\n   * If the overlay is configured to have a backdrop, it will be created.\n   *\n   * @param portal Portal instance to which to attach the overlay.\n   * @returns The portal attachment result.\n   */\n\n\n  attach(portal) {\n    // Insert the host into the DOM before attaching the portal, otherwise\n    // the animations module will skip animations on repeat attachments.\n    if (!this._host.parentElement && this._previousHostParent) {\n      this._previousHostParent.appendChild(this._host);\n    }\n\n    const attachResult = this._portalOutlet.attach(portal);\n\n    if (this._positionStrategy) {\n      this._positionStrategy.attach(this);\n    }\n\n    this._updateStackingOrder();\n\n    this._updateElementSize();\n\n    this._updateElementDirection();\n\n    if (this._scrollStrategy) {\n      this._scrollStrategy.enable();\n    } // Update the position once the zone is stable so that the overlay will be fully rendered\n    // before attempting to position it, as the position may depend on the size of the rendered\n    // content.\n\n\n    this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n      // The overlay could've been detached before the zone has stabilized.\n      if (this.hasAttached()) {\n        this.updatePosition();\n      }\n    }); // Enable pointer events for the overlay pane element.\n\n\n    this._togglePointerEvents(true);\n\n    if (this._config.hasBackdrop) {\n      this._attachBackdrop();\n    }\n\n    if (this._config.panelClass) {\n      this._toggleClasses(this._pane, this._config.panelClass, true);\n    } // Only emit the `attachments` event once all other setup is done.\n\n\n    this._attachments.next(); // Track this overlay by the keyboard dispatcher\n\n\n    this._keyboardDispatcher.add(this);\n\n    if (this._config.disposeOnNavigation) {\n      this._locationChanges = this._location.subscribe(() => this.dispose());\n    }\n\n    this._outsideClickDispatcher.add(this);\n\n    return attachResult;\n  }\n  /**\n   * Detaches an overlay from a portal.\n   * @returns The portal detachment result.\n   */\n\n\n  detach() {\n    if (!this.hasAttached()) {\n      return;\n    }\n\n    this.detachBackdrop(); // When the overlay is detached, the pane element should disable pointer events.\n    // This is necessary because otherwise the pane element will cover the page and disable\n    // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n\n    this._togglePointerEvents(false);\n\n    if (this._positionStrategy && this._positionStrategy.detach) {\n      this._positionStrategy.detach();\n    }\n\n    if (this._scrollStrategy) {\n      this._scrollStrategy.disable();\n    }\n\n    const detachmentResult = this._portalOutlet.detach(); // Only emit after everything is detached.\n\n\n    this._detachments.next(); // Remove this overlay from keyboard dispatcher tracking.\n\n\n    this._keyboardDispatcher.remove(this); // Keeping the host element in the DOM can cause scroll jank, because it still gets\n    // rendered, even though it's transparent and unclickable which is why we remove it.\n\n\n    this._detachContentWhenStable();\n\n    this._locationChanges.unsubscribe();\n\n    this._outsideClickDispatcher.remove(this);\n\n    return detachmentResult;\n  }\n  /** Cleans up the overlay from the DOM. */\n\n\n  dispose() {\n    const isAttached = this.hasAttached();\n\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n\n    this._disposeScrollStrategy();\n\n    this._disposeBackdrop(this._backdropElement);\n\n    this._locationChanges.unsubscribe();\n\n    this._keyboardDispatcher.remove(this);\n\n    this._portalOutlet.dispose();\n\n    this._attachments.complete();\n\n    this._backdropClick.complete();\n\n    this._keydownEvents.complete();\n\n    this._outsidePointerEvents.complete();\n\n    this._outsideClickDispatcher.remove(this);\n\n    this._host?.remove();\n    this._previousHostParent = this._pane = this._host = null;\n\n    if (isAttached) {\n      this._detachments.next();\n    }\n\n    this._detachments.complete();\n  }\n  /** Whether the overlay has attached content. */\n\n\n  hasAttached() {\n    return this._portalOutlet.hasAttached();\n  }\n  /** Gets an observable that emits when the backdrop has been clicked. */\n\n\n  backdropClick() {\n    return this._backdropClick;\n  }\n  /** Gets an observable that emits when the overlay has been attached. */\n\n\n  attachments() {\n    return this._attachments;\n  }\n  /** Gets an observable that emits when the overlay has been detached. */\n\n\n  detachments() {\n    return this._detachments;\n  }\n  /** Gets an observable of keydown events targeted to this overlay. */\n\n\n  keydownEvents() {\n    return this._keydownEvents;\n  }\n  /** Gets an observable of pointer events targeted outside this overlay. */\n\n\n  outsidePointerEvents() {\n    return this._outsidePointerEvents;\n  }\n  /** Gets the current overlay configuration, which is immutable. */\n\n\n  getConfig() {\n    return this._config;\n  }\n  /** Updates the position of the overlay based on the position strategy. */\n\n\n  updatePosition() {\n    if (this._positionStrategy) {\n      this._positionStrategy.apply();\n    }\n  }\n  /** Switches to a new position strategy and updates the overlay position. */\n\n\n  updatePositionStrategy(strategy) {\n    if (strategy === this._positionStrategy) {\n      return;\n    }\n\n    if (this._positionStrategy) {\n      this._positionStrategy.dispose();\n    }\n\n    this._positionStrategy = strategy;\n\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      this.updatePosition();\n    }\n  }\n  /** Update the size properties of the overlay. */\n\n\n  updateSize(sizeConfig) {\n    this._config = { ...this._config,\n      ...sizeConfig\n    };\n\n    this._updateElementSize();\n  }\n  /** Sets the LTR/RTL direction for the overlay. */\n\n\n  setDirection(dir) {\n    this._config = { ...this._config,\n      direction: dir\n    };\n\n    this._updateElementDirection();\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n\n\n  addPanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, true);\n    }\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n\n\n  removePanelClass(classes) {\n    if (this._pane) {\n      this._toggleClasses(this._pane, classes, false);\n    }\n  }\n  /**\n   * Returns the layout direction of the overlay panel.\n   */\n\n\n  getDirection() {\n    const direction = this._config.direction;\n\n    if (!direction) {\n      return 'ltr';\n    }\n\n    return typeof direction === 'string' ? direction : direction.value;\n  }\n  /** Switches to a new scroll strategy. */\n\n\n  updateScrollStrategy(strategy) {\n    if (strategy === this._scrollStrategy) {\n      return;\n    }\n\n    this._disposeScrollStrategy();\n\n    this._scrollStrategy = strategy;\n\n    if (this.hasAttached()) {\n      strategy.attach(this);\n      strategy.enable();\n    }\n  }\n  /** Updates the text direction of the overlay panel. */\n\n\n  _updateElementDirection() {\n    this._host.setAttribute('dir', this.getDirection());\n  }\n  /** Updates the size of the overlay element based on the overlay config. */\n\n\n  _updateElementSize() {\n    if (!this._pane) {\n      return;\n    }\n\n    const style = this._pane.style;\n    style.width = coerceCssPixelValue(this._config.width);\n    style.height = coerceCssPixelValue(this._config.height);\n    style.minWidth = coerceCssPixelValue(this._config.minWidth);\n    style.minHeight = coerceCssPixelValue(this._config.minHeight);\n    style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n    style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n  }\n  /** Toggles the pointer events for the overlay pane element. */\n\n\n  _togglePointerEvents(enablePointer) {\n    this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n  }\n  /** Attaches a backdrop for this overlay. */\n\n\n  _attachBackdrop() {\n    const showingClass = 'cdk-overlay-backdrop-showing';\n    this._backdropElement = this._document.createElement('div');\n\n    this._backdropElement.classList.add('cdk-overlay-backdrop');\n\n    if (this._config.backdropClass) {\n      this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n    } // Insert the backdrop before the pane in the DOM order,\n    // in order to handle stacked overlays properly.\n\n\n    this._host.parentElement.insertBefore(this._backdropElement, this._host); // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n    // action desired when such a click occurs (usually closing the overlay).\n\n\n    this._backdropElement.addEventListener('click', this._backdropClickHandler); // Add class to fade-in the backdrop after one frame.\n\n\n    if (typeof requestAnimationFrame !== 'undefined') {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          if (this._backdropElement) {\n            this._backdropElement.classList.add(showingClass);\n          }\n        });\n      });\n    } else {\n      this._backdropElement.classList.add(showingClass);\n    }\n  }\n  /**\n   * Updates the stacking order of the element, moving it to the top if necessary.\n   * This is required in cases where one overlay was detached, while another one,\n   * that should be behind it, was destroyed. The next time both of them are opened,\n   * the stacking will be wrong, because the detached element's pane will still be\n   * in its original DOM position.\n   */\n\n\n  _updateStackingOrder() {\n    if (this._host.nextSibling) {\n      this._host.parentNode.appendChild(this._host);\n    }\n  }\n  /** Detaches the backdrop (if any) associated with the overlay. */\n\n\n  detachBackdrop() {\n    const backdropToDetach = this._backdropElement;\n\n    if (!backdropToDetach) {\n      return;\n    }\n\n    backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n\n    this._ngZone.runOutsideAngular(() => {\n      backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n    }); // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n    // In this case we make it unclickable and we try to remove it after a delay.\n\n\n    backdropToDetach.style.pointerEvents = 'none'; // Run this outside the Angular zone because there's nothing that Angular cares about.\n    // If it were to run inside the Angular zone, every test that used Overlay would have to be\n    // either async or fakeAsync.\n\n    this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n      this._disposeBackdrop(backdropToDetach);\n    }, 500));\n  }\n  /** Toggles a single CSS class or an array of classes on an element. */\n\n\n  _toggleClasses(element, cssClasses, isAdd) {\n    const classes = coerceArray(cssClasses || []).filter(c => !!c);\n\n    if (classes.length) {\n      isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n    }\n  }\n  /** Detaches the overlay content next time the zone stabilizes. */\n\n\n  _detachContentWhenStable() {\n    // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n    // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n    // be patched to run inside the zone, which will throw us into an infinite loop.\n    this._ngZone.runOutsideAngular(() => {\n      // We can't remove the host here immediately, because the overlay pane's content\n      // might still be animating. This stream helps us avoid interrupting the animation\n      // by waiting for the pane to become empty.\n      const subscription = this._ngZone.onStable.pipe(takeUntil(merge(this._attachments, this._detachments))).subscribe(() => {\n        // Needs a couple of checks for the pane and host, because\n        // they may have been removed by the time the zone stabilizes.\n        if (!this._pane || !this._host || this._pane.children.length === 0) {\n          if (this._pane && this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, false);\n          }\n\n          if (this._host && this._host.parentElement) {\n            this._previousHostParent = this._host.parentElement;\n\n            this._host.remove();\n          }\n\n          subscription.unsubscribe();\n        }\n      });\n    });\n  }\n  /** Disposes of a scroll strategy. */\n\n\n  _disposeScrollStrategy() {\n    const scrollStrategy = this._scrollStrategy;\n\n    if (scrollStrategy) {\n      scrollStrategy.disable();\n\n      if (scrollStrategy.detach) {\n        scrollStrategy.detach();\n      }\n    }\n  }\n  /** Removes a backdrop element from the DOM. */\n\n\n  _disposeBackdrop(backdrop) {\n    if (backdrop) {\n      backdrop.removeEventListener('click', this._backdropClickHandler);\n      backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n      backdrop.remove(); // It is possible that a new portal has been attached to this overlay since we started\n      // removing the backdrop. If that is the case, only clear the backdrop reference if it\n      // is still the same instance that we started to remove.\n\n      if (this._backdropElement === backdrop) {\n        this._backdropElement = null;\n      }\n    }\n\n    if (this._backdropTimeout) {\n      clearTimeout(this._backdropTimeout);\n      this._backdropTimeout = undefined;\n    }\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n\n/** Class to be added to the overlay bounding box. */\n\n\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\n\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\n\nclass FlexibleConnectedPositionStrategy {\n  constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n    /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n\n    this._lastBoundingBoxSize = {\n      width: 0,\n      height: 0\n    };\n    /** Whether the overlay was pushed in a previous positioning. */\n\n    this._isPushed = false;\n    /** Whether the overlay can be pushed on-screen on the initial open. */\n\n    this._canPush = true;\n    /** Whether the overlay can grow via flexible width/height after the initial open. */\n\n    this._growAfterOpen = false;\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n\n    this._hasFlexibleDimensions = true;\n    /** Whether the overlay position is locked. */\n\n    this._positionLocked = false;\n    /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n\n    this._viewportMargin = 0;\n    /** The Scrollable containers used to check scrollable view properties on position change. */\n\n    this._scrollables = [];\n    /** Ordered list of preferred positions, from most to least desirable. */\n\n    this._preferredPositions = [];\n    /** Subject that emits whenever the position changes. */\n\n    this._positionChanges = new Subject();\n    /** Subscription to viewport size changes. */\n\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Default offset for the overlay along the x axis. */\n\n    this._offsetX = 0;\n    /** Default offset for the overlay along the y axis. */\n\n    this._offsetY = 0;\n    /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n\n    this._appliedPanelClasses = [];\n    /** Observable sequence of position changes. */\n\n    this.positionChanges = this._positionChanges;\n    this.setOrigin(connectedTo);\n  }\n  /** Ordered list of preferred positions, from most to least desirable. */\n\n\n  get positions() {\n    return this._preferredPositions;\n  }\n  /** Attaches this position strategy to an overlay. */\n\n\n  attach(overlayRef) {\n    if (this._overlayRef && overlayRef !== this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('This position strategy is already attached to an overlay');\n    }\n\n    this._validatePositions();\n\n    overlayRef.hostElement.classList.add(boundingBoxClass);\n    this._overlayRef = overlayRef;\n    this._boundingBox = overlayRef.hostElement;\n    this._pane = overlayRef.overlayElement;\n    this._isDisposed = false;\n    this._isInitialRender = true;\n    this._lastPosition = null;\n\n    this._resizeSubscription.unsubscribe();\n\n    this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n      // When the window is resized, we want to trigger the next reposition as if it\n      // was an initial render, in order for the strategy to pick a new optimal position,\n      // otherwise position locking will cause it to stay at the old one.\n      this._isInitialRender = true;\n      this.apply();\n    });\n  }\n  /**\n   * Updates the position of the overlay element, using whichever preferred position relative\n   * to the origin best fits on-screen.\n   *\n   * The selection of a position goes as follows:\n   *  - If any positions fit completely within the viewport as-is,\n   *      choose the first position that does so.\n   *  - If flexible dimensions are enabled and at least one satifies the given minimum width/height,\n   *      choose the position with the greatest available size modified by the positions' weight.\n   *  - If pushing is enabled, take the position that went off-screen the least and push it\n   *      on-screen.\n   *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n   * @docs-private\n   */\n\n\n  apply() {\n    // We shouldn't do anything if the strategy was disposed or we're on the server.\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    } // If the position has been applied already (e.g. when the overlay was opened) and the\n    // consumer opted into locking in the position, re-use the old position, in order to\n    // prevent the overlay from jumping around.\n\n\n    if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n      this.reapplyLastPosition();\n      return;\n    }\n\n    this._clearPanelClasses();\n\n    this._resetOverlayElementStyles();\n\n    this._resetBoundingBoxStyles(); // We need the bounding rects for the origin, the overlay and the container to determine how to position\n    // the overlay relative to the origin.\n    // We use the viewport rect to determine whether a position would go off-screen.\n\n\n    this._viewportRect = this._getNarrowedViewportRect();\n    this._originRect = this._getOriginRect();\n    this._overlayRect = this._pane.getBoundingClientRect();\n    this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n    const originRect = this._originRect;\n    const overlayRect = this._overlayRect;\n    const viewportRect = this._viewportRect;\n    const containerRect = this._containerRect; // Positions where the overlay will fit with flexible dimensions.\n\n    const flexibleFits = []; // Fallback if none of the preferred positions fit within the viewport.\n\n    let fallback; // Go through each of the preferred positions looking for a good fit.\n    // If a good fit is found, it will be applied immediately.\n\n    for (let pos of this._preferredPositions) {\n      // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n      let originPoint = this._getOriginPoint(originRect, containerRect, pos); // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n      // overlay in this position. We use the top-left corner for calculations and later translate\n      // this into an appropriate (top, left, bottom, right) style.\n\n\n      let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos); // Calculate how well the overlay would fit into the viewport with this point.\n\n\n      let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos); // If the overlay, without any further work, fits into the viewport, use this position.\n\n\n      if (overlayFit.isCompletelyWithinViewport) {\n        this._isPushed = false;\n\n        this._applyPosition(pos, originPoint);\n\n        return;\n      } // If the overlay has flexible dimensions, we can use this position\n      // so long as there's enough space for the minimum dimensions.\n\n\n      if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n        // Save positions where the overlay will fit with flexible dimensions. We will use these\n        // if none of the positions fit *without* flexible dimensions.\n        flexibleFits.push({\n          position: pos,\n          origin: originPoint,\n          overlayRect,\n          boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos)\n        });\n        continue;\n      } // If the current preferred position does not fit on the screen, remember the position\n      // if it has more visible area on-screen than we've seen and move onto the next preferred\n      // position.\n\n\n      if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n        fallback = {\n          overlayFit,\n          overlayPoint,\n          originPoint,\n          position: pos,\n          overlayRect\n        };\n      }\n    } // If there are any positions where the overlay would fit with flexible dimensions, choose the\n    // one that has the greatest area available modified by the position's weight\n\n\n    if (flexibleFits.length) {\n      let bestFit = null;\n      let bestScore = -1;\n\n      for (const fit of flexibleFits) {\n        const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n\n        if (score > bestScore) {\n          bestScore = score;\n          bestFit = fit;\n        }\n      }\n\n      this._isPushed = false;\n\n      this._applyPosition(bestFit.position, bestFit.origin);\n\n      return;\n    } // When none of the preferred positions fit within the viewport, take the position\n    // that went off-screen the least and attempt to push it on-screen.\n\n\n    if (this._canPush) {\n      // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n      this._isPushed = true;\n\n      this._applyPosition(fallback.position, fallback.originPoint);\n\n      return;\n    } // All options for getting the overlay within the viewport have been exhausted, so go with the\n    // position that went off-screen the least.\n\n\n    this._applyPosition(fallback.position, fallback.originPoint);\n  }\n\n  detach() {\n    this._clearPanelClasses();\n\n    this._lastPosition = null;\n    this._previousPushAmount = null;\n\n    this._resizeSubscription.unsubscribe();\n  }\n  /** Cleanup after the element gets destroyed. */\n\n\n  dispose() {\n    if (this._isDisposed) {\n      return;\n    } // We can't use `_resetBoundingBoxStyles` here, because it resets\n    // some properties to zero, rather than removing them.\n\n\n    if (this._boundingBox) {\n      extendStyles(this._boundingBox.style, {\n        top: '',\n        left: '',\n        right: '',\n        bottom: '',\n        height: '',\n        width: '',\n        alignItems: '',\n        justifyContent: ''\n      });\n    }\n\n    if (this._pane) {\n      this._resetOverlayElementStyles();\n    }\n\n    if (this._overlayRef) {\n      this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n    }\n\n    this.detach();\n\n    this._positionChanges.complete();\n\n    this._overlayRef = this._boundingBox = null;\n    this._isDisposed = true;\n  }\n  /**\n   * This re-aligns the overlay element with the trigger in its last calculated position,\n   * even if a position higher in the \"preferred positions\" list would now fit. This\n   * allows one to re-align the panel without changing the orientation of the panel.\n   */\n\n\n  reapplyLastPosition() {\n    if (this._isDisposed || !this._platform.isBrowser) {\n      return;\n    }\n\n    const lastPosition = this._lastPosition;\n\n    if (lastPosition) {\n      this._originRect = this._getOriginRect();\n      this._overlayRect = this._pane.getBoundingClientRect();\n      this._viewportRect = this._getNarrowedViewportRect();\n      this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n\n      const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n\n      this._applyPosition(lastPosition, originPoint);\n    } else {\n      this.apply();\n    }\n  }\n  /**\n   * Sets the list of Scrollable containers that host the origin element so that\n   * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n   * Scrollable must be an ancestor element of the strategy's origin element.\n   */\n\n\n  withScrollableContainers(scrollables) {\n    this._scrollables = scrollables;\n    return this;\n  }\n  /**\n   * Adds new preferred positions.\n   * @param positions List of positions options for this overlay.\n   */\n\n\n  withPositions(positions) {\n    this._preferredPositions = positions; // If the last calculated position object isn't part of the positions anymore, clear\n    // it in order to avoid it being picked up if the consumer tries to re-apply.\n\n    if (positions.indexOf(this._lastPosition) === -1) {\n      this._lastPosition = null;\n    }\n\n    this._validatePositions();\n\n    return this;\n  }\n  /**\n   * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n   * @param margin Required margin between the overlay and the viewport edge in pixels.\n   */\n\n\n  withViewportMargin(margin) {\n    this._viewportMargin = margin;\n    return this;\n  }\n  /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n\n\n  withFlexibleDimensions(flexibleDimensions = true) {\n    this._hasFlexibleDimensions = flexibleDimensions;\n    return this;\n  }\n  /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n\n\n  withGrowAfterOpen(growAfterOpen = true) {\n    this._growAfterOpen = growAfterOpen;\n    return this;\n  }\n  /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n\n\n  withPush(canPush = true) {\n    this._canPush = canPush;\n    return this;\n  }\n  /**\n   * Sets whether the overlay's position should be locked in after it is positioned\n   * initially. When an overlay is locked in, it won't attempt to reposition itself\n   * when the position is re-applied (e.g. when the user scrolls away).\n   * @param isLocked Whether the overlay should locked in.\n   */\n\n\n  withLockedPosition(isLocked = true) {\n    this._positionLocked = isLocked;\n    return this;\n  }\n  /**\n   * Sets the origin, relative to which to position the overlay.\n   * Using an element origin is useful for building components that need to be positioned\n   * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n   * used for cases like contextual menus which open relative to the user's pointer.\n   * @param origin Reference to the new origin.\n   */\n\n\n  setOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the x-axis.\n   * @param offset New offset in the X axis.\n   */\n\n\n  withDefaultOffsetX(offset) {\n    this._offsetX = offset;\n    return this;\n  }\n  /**\n   * Sets the default offset for the overlay's connection point on the y-axis.\n   * @param offset New offset in the Y axis.\n   */\n\n\n  withDefaultOffsetY(offset) {\n    this._offsetY = offset;\n    return this;\n  }\n  /**\n   * Configures that the position strategy should set a `transform-origin` on some elements\n   * inside the overlay, depending on the current position that is being applied. This is\n   * useful for the cases where the origin of an animation can change depending on the\n   * alignment of the overlay.\n   * @param selector CSS selector that will be used to find the target\n   *    elements onto which to set the transform origin.\n   */\n\n\n  withTransformOriginOn(selector) {\n    this._transformOriginSelector = selector;\n    return this;\n  }\n  /**\n   * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n   */\n\n\n  _getOriginPoint(originRect, containerRect, pos) {\n    let x;\n\n    if (pos.originX == 'center') {\n      // Note: when centering we should always use the `left`\n      // offset, otherwise the position will be wrong in RTL.\n      x = originRect.left + originRect.width / 2;\n    } else {\n      const startX = this._isRtl() ? originRect.right : originRect.left;\n      const endX = this._isRtl() ? originRect.left : originRect.right;\n      x = pos.originX == 'start' ? startX : endX;\n    } // When zooming in Safari the container rectangle contains negative values for the position\n    // and we need to re-add them to the calculated coordinates.\n\n\n    if (containerRect.left < 0) {\n      x -= containerRect.left;\n    }\n\n    let y;\n\n    if (pos.originY == 'center') {\n      y = originRect.top + originRect.height / 2;\n    } else {\n      y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n    } // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n    // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n    // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n    // otherwise our positioning will be thrown off.\n    // Additionally, when zooming in Safari this fixes the vertical position.\n\n\n    if (containerRect.top < 0) {\n      y -= containerRect.top;\n    }\n\n    return {\n      x,\n      y\n    };\n  }\n  /**\n   * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n   * origin point to which the overlay should be connected.\n   */\n\n\n  _getOverlayPoint(originPoint, overlayRect, pos) {\n    // Calculate the (overlayStartX, overlayStartY), the start of the\n    // potential overlay position relative to the origin point.\n    let overlayStartX;\n\n    if (pos.overlayX == 'center') {\n      overlayStartX = -overlayRect.width / 2;\n    } else if (pos.overlayX === 'start') {\n      overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n    } else {\n      overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n    }\n\n    let overlayStartY;\n\n    if (pos.overlayY == 'center') {\n      overlayStartY = -overlayRect.height / 2;\n    } else {\n      overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n    } // The (x, y) coordinates of the overlay.\n\n\n    return {\n      x: originPoint.x + overlayStartX,\n      y: originPoint.y + overlayStartY\n    };\n  }\n  /** Gets how well an overlay at the given point will fit within the viewport. */\n\n\n  _getOverlayFit(point, rawOverlayRect, viewport, position) {\n    // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    let {\n      x,\n      y\n    } = point;\n\n    let offsetX = this._getOffset(position, 'x');\n\n    let offsetY = this._getOffset(position, 'y'); // Account for the offsets since they could push the overlay out of the viewport.\n\n\n    if (offsetX) {\n      x += offsetX;\n    }\n\n    if (offsetY) {\n      y += offsetY;\n    } // How much the overlay would overflow at this position, on each side.\n\n\n    let leftOverflow = 0 - x;\n    let rightOverflow = x + overlay.width - viewport.width;\n    let topOverflow = 0 - y;\n    let bottomOverflow = y + overlay.height - viewport.height; // Visible parts of the element on each axis.\n\n    let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n\n    let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n\n    let visibleArea = visibleWidth * visibleHeight;\n    return {\n      visibleArea,\n      isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n      fitsInViewportVertically: visibleHeight === overlay.height,\n      fitsInViewportHorizontally: visibleWidth == overlay.width\n    };\n  }\n  /**\n   * Whether the overlay can fit within the viewport when it may resize either its width or height.\n   * @param fit How well the overlay fits in the viewport at some position.\n   * @param point The (x, y) coordinates of the overlay at some position.\n   * @param viewport The geometry of the viewport.\n   */\n\n\n  _canFitWithFlexibleDimensions(fit, point, viewport) {\n    if (this._hasFlexibleDimensions) {\n      const availableHeight = viewport.bottom - point.y;\n      const availableWidth = viewport.right - point.x;\n      const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n      const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n      const verticalFit = fit.fitsInViewportVertically || minHeight != null && minHeight <= availableHeight;\n      const horizontalFit = fit.fitsInViewportHorizontally || minWidth != null && minWidth <= availableWidth;\n      return verticalFit && horizontalFit;\n    }\n\n    return false;\n  }\n  /**\n   * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n   * the viewport, the top-left corner will be pushed on-screen (with overflow occuring on the\n   * right and bottom).\n   *\n   * @param start Starting point from which the overlay is pushed.\n   * @param rawOverlayRect Dimensions of the overlay.\n   * @param scrollPosition Current viewport scroll position.\n   * @returns The point at which to position the overlay after pushing. This is effectively a new\n   *     originPoint.\n   */\n\n\n  _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n    // If the position is locked and we've pushed the overlay already, reuse the previous push\n    // amount, rather than pushing it again. If we were to continue pushing, the element would\n    // remain in the viewport, which goes against the expectations when position locking is enabled.\n    if (this._previousPushAmount && this._positionLocked) {\n      return {\n        x: start.x + this._previousPushAmount.x,\n        y: start.y + this._previousPushAmount.y\n      };\n    } // Round the overlay rect when comparing against the\n    // viewport, because the viewport is always rounded.\n\n\n    const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n    const viewport = this._viewportRect; // Determine how much the overlay goes outside the viewport on each\n    // side, which we'll use to decide which direction to push it.\n\n    const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n    const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n    const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n    const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0); // Amount by which to push the overlay in each axis such that it remains on-screen.\n\n    let pushX = 0;\n    let pushY = 0; // If the overlay fits completely within the bounds of the viewport, push it from whichever\n    // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n    // viewport and allow for the trailing end of the overlay to go out of bounds.\n\n    if (overlay.width <= viewport.width) {\n      pushX = overflowLeft || -overflowRight;\n    } else {\n      pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n    }\n\n    if (overlay.height <= viewport.height) {\n      pushY = overflowTop || -overflowBottom;\n    } else {\n      pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n    }\n\n    this._previousPushAmount = {\n      x: pushX,\n      y: pushY\n    };\n    return {\n      x: start.x + pushX,\n      y: start.y + pushY\n    };\n  }\n  /**\n   * Applies a computed position to the overlay and emits a position change.\n   * @param position The position preference\n   * @param originPoint The point on the origin element where the overlay is connected.\n   */\n\n\n  _applyPosition(position, originPoint) {\n    this._setTransformOrigin(position);\n\n    this._setOverlayElementStyles(originPoint, position);\n\n    this._setBoundingBoxStyles(originPoint, position);\n\n    if (position.panelClass) {\n      this._addPanelClasses(position.panelClass);\n    } // Save the last connected position in case the position needs to be re-calculated.\n\n\n    this._lastPosition = position; // Notify that the position has been changed along with its change properties.\n    // We only emit if we've got any subscriptions, because the scroll visibility\n    // calculcations can be somewhat expensive.\n\n    if (this._positionChanges.observers.length) {\n      const scrollableViewProperties = this._getScrollVisibility();\n\n      const changeEvent = new ConnectedOverlayPositionChange(position, scrollableViewProperties);\n\n      this._positionChanges.next(changeEvent);\n    }\n\n    this._isInitialRender = false;\n  }\n  /** Sets the transform origin based on the configured selector and the passed-in position.  */\n\n\n  _setTransformOrigin(position) {\n    if (!this._transformOriginSelector) {\n      return;\n    }\n\n    const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n\n    let xOrigin;\n    let yOrigin = position.overlayY;\n\n    if (position.overlayX === 'center') {\n      xOrigin = 'center';\n    } else if (this._isRtl()) {\n      xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n    } else {\n      xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n    }\n\n    for (let i = 0; i < elements.length; i++) {\n      elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n    }\n  }\n  /**\n   * Gets the position and size of the overlay's sizing container.\n   *\n   * This method does no measuring and applies no styles so that we can cheaply compute the\n   * bounds for all positions and choose the best fit based on these results.\n   */\n\n\n  _calculateBoundingBoxRect(origin, position) {\n    const viewport = this._viewportRect;\n\n    const isRtl = this._isRtl();\n\n    let height, top, bottom;\n\n    if (position.overlayY === 'top') {\n      // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n      top = origin.y;\n      height = viewport.height - top + this._viewportMargin;\n    } else if (position.overlayY === 'bottom') {\n      // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n      // the viewport margin back in, because the viewport rect is narrowed down to remove the\n      // margin, whereas the `origin` position is calculated based on its `ClientRect`.\n      bottom = viewport.height - origin.y + this._viewportMargin * 2;\n      height = viewport.height - bottom + this._viewportMargin;\n    } else {\n      // If neither top nor bottom, it means that the overlay is vertically centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n      // `origin.y - viewport.top`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n      const previousHeight = this._lastBoundingBoxSize.height;\n      height = smallestDistanceToViewportEdge * 2;\n      top = origin.y - smallestDistanceToViewportEdge;\n\n      if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n        top = origin.y - previousHeight / 2;\n      }\n    } // The overlay is opening 'right-ward' (the content flows to the right).\n\n\n    const isBoundedByRightViewportEdge = position.overlayX === 'start' && !isRtl || position.overlayX === 'end' && isRtl; // The overlay is opening 'left-ward' (the content flows to the left).\n\n    const isBoundedByLeftViewportEdge = position.overlayX === 'end' && !isRtl || position.overlayX === 'start' && isRtl;\n    let width, left, right;\n\n    if (isBoundedByLeftViewportEdge) {\n      right = viewport.width - origin.x + this._viewportMargin;\n      width = origin.x - this._viewportMargin;\n    } else if (isBoundedByRightViewportEdge) {\n      left = origin.x;\n      width = viewport.right - origin.x;\n    } else {\n      // If neither start nor end, it means that the overlay is horizontally centered on the\n      // origin point. Note that we want the position relative to the viewport, rather than\n      // the page, which is why we don't use something like `viewport.right - origin.x` and\n      // `origin.x - viewport.left`.\n      const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n      const previousWidth = this._lastBoundingBoxSize.width;\n      width = smallestDistanceToViewportEdge * 2;\n      left = origin.x - smallestDistanceToViewportEdge;\n\n      if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n        left = origin.x - previousWidth / 2;\n      }\n    }\n\n    return {\n      top: top,\n      left: left,\n      bottom: bottom,\n      right: right,\n      width,\n      height\n    };\n  }\n  /**\n   * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n   * origin's connection point and stetches to the bounds of the viewport.\n   *\n   * @param origin The point on the origin element where the overlay is connected.\n   * @param position The position preference\n   */\n\n\n  _setBoundingBoxStyles(origin, position) {\n    const boundingBoxRect = this._calculateBoundingBoxRect(origin, position); // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n    // when applying a new size.\n\n\n    if (!this._isInitialRender && !this._growAfterOpen) {\n      boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n      boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n    }\n\n    const styles = {};\n\n    if (this._hasExactPosition()) {\n      styles.top = styles.left = '0';\n      styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n      styles.width = styles.height = '100%';\n    } else {\n      const maxHeight = this._overlayRef.getConfig().maxHeight;\n\n      const maxWidth = this._overlayRef.getConfig().maxWidth;\n\n      styles.height = coerceCssPixelValue(boundingBoxRect.height);\n      styles.top = coerceCssPixelValue(boundingBoxRect.top);\n      styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n      styles.width = coerceCssPixelValue(boundingBoxRect.width);\n      styles.left = coerceCssPixelValue(boundingBoxRect.left);\n      styles.right = coerceCssPixelValue(boundingBoxRect.right); // Push the pane content towards the proper direction.\n\n      if (position.overlayX === 'center') {\n        styles.alignItems = 'center';\n      } else {\n        styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n      }\n\n      if (position.overlayY === 'center') {\n        styles.justifyContent = 'center';\n      } else {\n        styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n      }\n\n      if (maxHeight) {\n        styles.maxHeight = coerceCssPixelValue(maxHeight);\n      }\n\n      if (maxWidth) {\n        styles.maxWidth = coerceCssPixelValue(maxWidth);\n      }\n    }\n\n    this._lastBoundingBoxSize = boundingBoxRect;\n    extendStyles(this._boundingBox.style, styles);\n  }\n  /** Resets the styles for the bounding box so that a new positioning can be computed. */\n\n\n  _resetBoundingBoxStyles() {\n    extendStyles(this._boundingBox.style, {\n      top: '0',\n      left: '0',\n      right: '0',\n      bottom: '0',\n      height: '',\n      width: '',\n      alignItems: '',\n      justifyContent: ''\n    });\n  }\n  /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n\n\n  _resetOverlayElementStyles() {\n    extendStyles(this._pane.style, {\n      top: '',\n      left: '',\n      bottom: '',\n      right: '',\n      position: '',\n      transform: ''\n    });\n  }\n  /** Sets positioning styles to the overlay element. */\n\n\n  _setOverlayElementStyles(originPoint, position) {\n    const styles = {};\n\n    const hasExactPosition = this._hasExactPosition();\n\n    const hasFlexibleDimensions = this._hasFlexibleDimensions;\n\n    const config = this._overlayRef.getConfig();\n\n    if (hasExactPosition) {\n      const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n      extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n      extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n    } else {\n      styles.position = 'static';\n    } // Use a transform to apply the offsets. We do this because the `center` positions rely on\n    // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n    // off the position. We also can't use margins, because they won't have an effect in some\n    // cases where the element doesn't have anything to \"push off of\". Finally, this works\n    // better both with flexible and non-flexible positioning.\n\n\n    let transformString = '';\n\n    let offsetX = this._getOffset(position, 'x');\n\n    let offsetY = this._getOffset(position, 'y');\n\n    if (offsetX) {\n      transformString += `translateX(${offsetX}px) `;\n    }\n\n    if (offsetY) {\n      transformString += `translateY(${offsetY}px)`;\n    }\n\n    styles.transform = transformString.trim(); // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n    // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n    // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n    // Note that this doesn't apply when we have an exact position, in which case we do want to\n    // apply them because they'll be cleared from the bounding box.\n\n    if (config.maxHeight) {\n      if (hasExactPosition) {\n        styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n      } else if (hasFlexibleDimensions) {\n        styles.maxHeight = '';\n      }\n    }\n\n    if (config.maxWidth) {\n      if (hasExactPosition) {\n        styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n      } else if (hasFlexibleDimensions) {\n        styles.maxWidth = '';\n      }\n    }\n\n    extendStyles(this._pane.style, styles);\n  }\n  /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n\n\n  _getExactOverlayY(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the\n    // preferred position has changed since the last `apply`.\n    let styles = {\n      top: '',\n      bottom: ''\n    };\n\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    } // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n    // above or below the origin and the direction in which the element will expand.\n\n\n    if (position.overlayY === 'bottom') {\n      // When using `bottom`, we adjust the y position such that it is the distance\n      // from the bottom of the viewport rather than the top.\n      const documentHeight = this._document.documentElement.clientHeight;\n      styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n    } else {\n      styles.top = coerceCssPixelValue(overlayPoint.y);\n    }\n\n    return styles;\n  }\n  /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n\n\n  _getExactOverlayX(position, originPoint, scrollPosition) {\n    // Reset any existing styles. This is necessary in case the preferred position has\n    // changed since the last `apply`.\n    let styles = {\n      left: '',\n      right: ''\n    };\n\n    let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n\n    if (this._isPushed) {\n      overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n    } // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n    // or \"after\" the origin, which determines the direction in which the element will expand.\n    // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n    // page is in RTL or LTR.\n\n\n    let horizontalStyleProperty;\n\n    if (this._isRtl()) {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n    } else {\n      horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n    } // When we're setting `right`, we adjust the x position such that it is the distance\n    // from the right edge of the viewport rather than the left edge.\n\n\n    if (horizontalStyleProperty === 'right') {\n      const documentWidth = this._document.documentElement.clientWidth;\n      styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n    } else {\n      styles.left = coerceCssPixelValue(overlayPoint.x);\n    }\n\n    return styles;\n  }\n  /**\n   * Gets the view properties of the trigger and overlay, including whether they are clipped\n   * or completely outside the view of any of the strategy's scrollables.\n   */\n\n\n  _getScrollVisibility() {\n    // Note: needs fresh rects since the position could've changed.\n    const originBounds = this._getOriginRect();\n\n    const overlayBounds = this._pane.getBoundingClientRect(); // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n    // every time, we should be able to use the scrollTop of the containers if the size of those\n    // containers hasn't changed.\n\n\n    const scrollContainerBounds = this._scrollables.map(scrollable => {\n      return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n    });\n\n    return {\n      isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n      isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n      isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n      isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds)\n    };\n  }\n  /** Subtracts the amount that an element is overflowing on an axis from its length. */\n\n\n  _subtractOverflows(length, ...overflows) {\n    return overflows.reduce((currentValue, currentOverflow) => {\n      return currentValue - Math.max(currentOverflow, 0);\n    }, length);\n  }\n  /** Narrows the given viewport rect by the current _viewportMargin. */\n\n\n  _getNarrowedViewportRect() {\n    // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n    // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n    // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n    // and `innerHeight` that do. This is necessary, because the overlay container uses\n    // 100% `width` and `height` which don't include the scrollbar either.\n    const width = this._document.documentElement.clientWidth;\n    const height = this._document.documentElement.clientHeight;\n\n    const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n\n    return {\n      top: scrollPosition.top + this._viewportMargin,\n      left: scrollPosition.left + this._viewportMargin,\n      right: scrollPosition.left + width - this._viewportMargin,\n      bottom: scrollPosition.top + height - this._viewportMargin,\n      width: width - 2 * this._viewportMargin,\n      height: height - 2 * this._viewportMargin\n    };\n  }\n  /** Whether the we're dealing with an RTL context */\n\n\n  _isRtl() {\n    return this._overlayRef.getDirection() === 'rtl';\n  }\n  /** Determines whether the overlay uses exact or flexible positioning. */\n\n\n  _hasExactPosition() {\n    return !this._hasFlexibleDimensions || this._isPushed;\n  }\n  /** Retrieves the offset of a position along the x or y axis. */\n\n\n  _getOffset(position, axis) {\n    if (axis === 'x') {\n      // We don't do something like `position['offset' + axis]` in\n      // order to avoid breking minifiers that rename properties.\n      return position.offsetX == null ? this._offsetX : position.offsetX;\n    }\n\n    return position.offsetY == null ? this._offsetY : position.offsetY;\n  }\n  /** Validates that the current position match the expected values. */\n\n\n  _validatePositions() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!this._preferredPositions.length) {\n        throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n      } // TODO(crisbeto): remove these once Angular's template type\n      // checking is advanced enough to catch these cases.\n\n\n      this._preferredPositions.forEach(pair => {\n        validateHorizontalPosition('originX', pair.originX);\n        validateVerticalPosition('originY', pair.originY);\n        validateHorizontalPosition('overlayX', pair.overlayX);\n        validateVerticalPosition('overlayY', pair.overlayY);\n      });\n    }\n  }\n  /** Adds a single CSS class or an array of classes on the overlay panel. */\n\n\n  _addPanelClasses(cssClasses) {\n    if (this._pane) {\n      coerceArray(cssClasses).forEach(cssClass => {\n        if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n          this._appliedPanelClasses.push(cssClass);\n\n          this._pane.classList.add(cssClass);\n        }\n      });\n    }\n  }\n  /** Clears the classes that the position strategy has applied from the overlay panel. */\n\n\n  _clearPanelClasses() {\n    if (this._pane) {\n      this._appliedPanelClasses.forEach(cssClass => {\n        this._pane.classList.remove(cssClass);\n      });\n\n      this._appliedPanelClasses = [];\n    }\n  }\n  /** Returns the ClientRect of the current origin. */\n\n\n  _getOriginRect() {\n    const origin = this._origin;\n\n    if (origin instanceof ElementRef) {\n      return origin.nativeElement.getBoundingClientRect();\n    } // Check for Element so SVG elements are also supported.\n\n\n    if (origin instanceof Element) {\n      return origin.getBoundingClientRect();\n    }\n\n    const width = origin.width || 0;\n    const height = origin.height || 0; // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n\n    return {\n      top: origin.y,\n      bottom: origin.y + height,\n      left: origin.x,\n      right: origin.x + width,\n      height,\n      width\n    };\n  }\n\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\n\n\nfunction extendStyles(destination, source) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      destination[key] = source[key];\n    }\n  }\n\n  return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\n\n\nfunction getPixelValue(input) {\n  if (typeof input !== 'number' && input != null) {\n    const [value, units] = input.split(cssUnitPattern);\n    return !units || units === 'px' ? parseFloat(value) : null;\n  }\n\n  return input || null;\n}\n/**\n * Gets a version of an element's bounding `ClientRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `ClientRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\n\n\nfunction getRoundedBoundingClientRect(clientRect) {\n  return {\n    top: Math.floor(clientRect.top),\n    right: Math.floor(clientRect.right),\n    bottom: Math.floor(clientRect.bottom),\n    left: Math.floor(clientRect.left),\n    width: Math.floor(clientRect.width),\n    height: Math.floor(clientRect.height)\n  };\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Class to be added to the overlay pane wrapper. */\n\n\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\n\nclass GlobalPositionStrategy {\n  constructor() {\n    this._cssPosition = 'static';\n    this._topOffset = '';\n    this._bottomOffset = '';\n    this._leftOffset = '';\n    this._rightOffset = '';\n    this._alignItems = '';\n    this._justifyContent = '';\n    this._width = '';\n    this._height = '';\n  }\n\n  attach(overlayRef) {\n    const config = overlayRef.getConfig();\n    this._overlayRef = overlayRef;\n\n    if (this._width && !config.width) {\n      overlayRef.updateSize({\n        width: this._width\n      });\n    }\n\n    if (this._height && !config.height) {\n      overlayRef.updateSize({\n        height: this._height\n      });\n    }\n\n    overlayRef.hostElement.classList.add(wrapperClass);\n    this._isDisposed = false;\n  }\n  /**\n   * Sets the top position of the overlay. Clears any previously set vertical position.\n   * @param value New top offset.\n   */\n\n\n  top(value = '') {\n    this._bottomOffset = '';\n    this._topOffset = value;\n    this._alignItems = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the left position of the overlay. Clears any previously set horizontal position.\n   * @param value New left offset.\n   */\n\n\n  left(value = '') {\n    this._rightOffset = '';\n    this._leftOffset = value;\n    this._justifyContent = 'flex-start';\n    return this;\n  }\n  /**\n   * Sets the bottom position of the overlay. Clears any previously set vertical position.\n   * @param value New bottom offset.\n   */\n\n\n  bottom(value = '') {\n    this._topOffset = '';\n    this._bottomOffset = value;\n    this._alignItems = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the right position of the overlay. Clears any previously set horizontal position.\n   * @param value New right offset.\n   */\n\n\n  right(value = '') {\n    this._leftOffset = '';\n    this._rightOffset = value;\n    this._justifyContent = 'flex-end';\n    return this;\n  }\n  /**\n   * Sets the overlay width and clears any previously set width.\n   * @param value New width for the overlay\n   * @deprecated Pass the `width` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n\n\n  width(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        width: value\n      });\n    } else {\n      this._width = value;\n    }\n\n    return this;\n  }\n  /**\n   * Sets the overlay height and clears any previously set height.\n   * @param value New height for the overlay\n   * @deprecated Pass the `height` through the `OverlayConfig`.\n   * @breaking-change 8.0.0\n   */\n\n\n  height(value = '') {\n    if (this._overlayRef) {\n      this._overlayRef.updateSize({\n        height: value\n      });\n    } else {\n      this._height = value;\n    }\n\n    return this;\n  }\n  /**\n   * Centers the overlay horizontally with an optional offset.\n   * Clears any previously set horizontal position.\n   *\n   * @param offset Overlay offset from the horizontal center.\n   */\n\n\n  centerHorizontally(offset = '') {\n    this.left(offset);\n    this._justifyContent = 'center';\n    return this;\n  }\n  /**\n   * Centers the overlay vertically with an optional offset.\n   * Clears any previously set vertical position.\n   *\n   * @param offset Overlay offset from the vertical center.\n   */\n\n\n  centerVertically(offset = '') {\n    this.top(offset);\n    this._alignItems = 'center';\n    return this;\n  }\n  /**\n   * Apply the position to the element.\n   * @docs-private\n   */\n\n\n  apply() {\n    // Since the overlay ref applies the strategy asynchronously, it could\n    // have been disposed before it ends up being applied. If that is the\n    // case, we shouldn't do anything.\n    if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n      return;\n    }\n\n    const styles = this._overlayRef.overlayElement.style;\n    const parentStyles = this._overlayRef.hostElement.style;\n\n    const config = this._overlayRef.getConfig();\n\n    const {\n      width,\n      height,\n      maxWidth,\n      maxHeight\n    } = config;\n    const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') && (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n    const shouldBeFlushVertically = (height === '100%' || height === '100vh') && (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n    styles.position = this._cssPosition;\n    styles.marginLeft = shouldBeFlushHorizontally ? '0' : this._leftOffset;\n    styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n    styles.marginBottom = this._bottomOffset;\n    styles.marginRight = this._rightOffset;\n\n    if (shouldBeFlushHorizontally) {\n      parentStyles.justifyContent = 'flex-start';\n    } else if (this._justifyContent === 'center') {\n      parentStyles.justifyContent = 'center';\n    } else if (this._overlayRef.getConfig().direction === 'rtl') {\n      // In RTL the browser will invert `flex-start` and `flex-end` automatically, but we\n      // don't want that because our positioning is explicitly `left` and `right`, hence\n      // why we do another inversion to ensure that the overlay stays in the same position.\n      // TODO: reconsider this if we add `start` and `end` methods.\n      if (this._justifyContent === 'flex-start') {\n        parentStyles.justifyContent = 'flex-end';\n      } else if (this._justifyContent === 'flex-end') {\n        parentStyles.justifyContent = 'flex-start';\n      }\n    } else {\n      parentStyles.justifyContent = this._justifyContent;\n    }\n\n    parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n  }\n  /**\n   * Cleans up the DOM changes from the position strategy.\n   * @docs-private\n   */\n\n\n  dispose() {\n    if (this._isDisposed || !this._overlayRef) {\n      return;\n    }\n\n    const styles = this._overlayRef.overlayElement.style;\n    const parent = this._overlayRef.hostElement;\n    const parentStyles = parent.style;\n    parent.classList.remove(wrapperClass);\n    parentStyles.justifyContent = parentStyles.alignItems = styles.marginTop = styles.marginBottom = styles.marginLeft = styles.marginRight = styles.position = '';\n    this._overlayRef = null;\n    this._isDisposed = true;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Builder for overlay position strategy. */\n\n\nclass OverlayPositionBuilder {\n  constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n    this._viewportRuler = _viewportRuler;\n    this._document = _document;\n    this._platform = _platform;\n    this._overlayContainer = _overlayContainer;\n  }\n  /**\n   * Creates a global position strategy.\n   */\n\n\n  global() {\n    return new GlobalPositionStrategy();\n  }\n  /**\n   * Creates a flexible position strategy.\n   * @param origin Origin relative to which to position the overlay.\n   */\n\n\n  flexibleConnectedTo(origin) {\n    return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n  }\n\n}\n\nOverlayPositionBuilder.ɵfac = function OverlayPositionBuilder_Factory(t) {\n  return new (t || OverlayPositionBuilder)(i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform), i0.ɵɵinject(OverlayContainer));\n};\n\nOverlayPositionBuilder.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: OverlayPositionBuilder,\n  factory: OverlayPositionBuilder.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPositionBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }, {\n      type: OverlayContainer\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Next overlay unique ID. */\n\n\nlet nextUniqueId = 0; // Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\n\nclass Overlay {\n  constructor(\n  /** Scrolling strategies that can be used when creating an overlay. */\n  scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher) {\n    this.scrollStrategies = scrollStrategies;\n    this._overlayContainer = _overlayContainer;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._positionBuilder = _positionBuilder;\n    this._keyboardDispatcher = _keyboardDispatcher;\n    this._injector = _injector;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._directionality = _directionality;\n    this._location = _location;\n    this._outsideClickDispatcher = _outsideClickDispatcher;\n  }\n  /**\n   * Creates an overlay.\n   * @param config Configuration applied to the overlay.\n   * @returns Reference to the created overlay.\n   */\n\n\n  create(config) {\n    const host = this._createHostElement();\n\n    const pane = this._createPaneElement(host);\n\n    const portalOutlet = this._createPortalOutlet(pane);\n\n    const overlayConfig = new OverlayConfig(config);\n    overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n    return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher);\n  }\n  /**\n   * Gets a position builder that can be used, via fluent API,\n   * to construct and configure a position strategy.\n   * @returns An overlay position builder.\n   */\n\n\n  position() {\n    return this._positionBuilder;\n  }\n  /**\n   * Creates the DOM element for an overlay and appends it to the overlay container.\n   * @returns Newly-created pane element\n   */\n\n\n  _createPaneElement(host) {\n    const pane = this._document.createElement('div');\n\n    pane.id = `cdk-overlay-${nextUniqueId++}`;\n    pane.classList.add('cdk-overlay-pane');\n    host.appendChild(pane);\n    return pane;\n  }\n  /**\n   * Creates the host element that wraps around an overlay\n   * and can be used for advanced positioning.\n   * @returns Newly-create host element.\n   */\n\n\n  _createHostElement() {\n    const host = this._document.createElement('div');\n\n    this._overlayContainer.getContainerElement().appendChild(host);\n\n    return host;\n  }\n  /**\n   * Create a DomPortalOutlet into which the overlay content can be loaded.\n   * @param pane The DOM element to turn into a portal outlet.\n   * @returns A portal outlet for the given DOM element.\n   */\n\n\n  _createPortalOutlet(pane) {\n    // We have to resolve the ApplicationRef later in order to allow people\n    // to use overlay-based providers during app initialization.\n    if (!this._appRef) {\n      this._appRef = this._injector.get(ApplicationRef);\n    }\n\n    return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n  }\n\n}\n\nOverlay.ɵfac = function Overlay_Factory(t) {\n  return new (t || Overlay)(i0.ɵɵinject(ScrollStrategyOptions), i0.ɵɵinject(OverlayContainer), i0.ɵɵinject(i0.ComponentFactoryResolver), i0.ɵɵinject(OverlayPositionBuilder), i0.ɵɵinject(OverlayKeyboardDispatcher), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i5.Directionality), i0.ɵɵinject(i6.Location), i0.ɵɵinject(OverlayOutsideClickDispatcher));\n};\n\nOverlay.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Overlay,\n  factory: Overlay.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: ScrollStrategyOptions\n    }, {\n      type: OverlayContainer\n    }, {\n      type: i0.ComponentFactoryResolver\n    }, {\n      type: OverlayPositionBuilder\n    }, {\n      type: OverlayKeyboardDispatcher\n    }, {\n      type: i0.Injector\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i5.Directionality\n    }, {\n      type: i6.Location\n    }, {\n      type: OverlayOutsideClickDispatcher\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\n\n\nconst defaultPositionList = [{\n  originX: 'start',\n  originY: 'bottom',\n  overlayX: 'start',\n  overlayY: 'top'\n}, {\n  originX: 'start',\n  originY: 'top',\n  overlayX: 'start',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'top',\n  overlayX: 'end',\n  overlayY: 'bottom'\n}, {\n  originX: 'end',\n  originY: 'bottom',\n  overlayX: 'end',\n  overlayY: 'top'\n}];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\n\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy');\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\n\nclass CdkOverlayOrigin {\n  constructor(\n  /** Reference to the element on which the directive is applied. */\n  elementRef) {\n    this.elementRef = elementRef;\n  }\n\n}\n\nCdkOverlayOrigin.ɵfac = function CdkOverlayOrigin_Factory(t) {\n  return new (t || CdkOverlayOrigin)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nCdkOverlayOrigin.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkOverlayOrigin,\n  selectors: [[\"\", \"cdk-overlay-origin\", \"\"], [\"\", \"overlay-origin\", \"\"], [\"\", \"cdkOverlayOrigin\", \"\"]],\n  exportAs: [\"cdkOverlayOrigin\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkOverlayOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n      exportAs: 'cdkOverlayOrigin'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\n\n\nclass CdkConnectedOverlay {\n  // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n  constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n    this._overlay = _overlay;\n    this._dir = _dir;\n    this._hasBackdrop = false;\n    this._lockPosition = false;\n    this._growAfterOpen = false;\n    this._flexibleDimensions = false;\n    this._push = false;\n    this._backdropSubscription = Subscription.EMPTY;\n    this._attachSubscription = Subscription.EMPTY;\n    this._detachSubscription = Subscription.EMPTY;\n    this._positionSubscription = Subscription.EMPTY;\n    /** Margin between the overlay and the viewport edges. */\n\n    this.viewportMargin = 0;\n    /** Whether the overlay is open. */\n\n    this.open = false;\n    /** Whether the overlay can be closed by user interaction. */\n\n    this.disableClose = false;\n    /** Event emitted when the backdrop is clicked. */\n\n    this.backdropClick = new EventEmitter();\n    /** Event emitted when the position has changed. */\n\n    this.positionChange = new EventEmitter();\n    /** Event emitted when the overlay has been attached. */\n\n    this.attach = new EventEmitter();\n    /** Event emitted when the overlay has been detached. */\n\n    this.detach = new EventEmitter();\n    /** Emits when there are keyboard events that are targeted at the overlay. */\n\n    this.overlayKeydown = new EventEmitter();\n    /** Emits when there are mouse outside click events that are targeted at the overlay. */\n\n    this.overlayOutsideClick = new EventEmitter();\n    this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n    this._scrollStrategyFactory = scrollStrategyFactory;\n    this.scrollStrategy = this._scrollStrategyFactory();\n  }\n  /** The offset in pixels for the overlay connection point on the x-axis */\n\n\n  get offsetX() {\n    return this._offsetX;\n  }\n\n  set offsetX(offsetX) {\n    this._offsetX = offsetX;\n\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** The offset in pixels for the overlay connection point on the y-axis */\n\n\n  get offsetY() {\n    return this._offsetY;\n  }\n\n  set offsetY(offsetY) {\n    this._offsetY = offsetY;\n\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n    }\n  }\n  /** Whether or not the overlay should attach a backdrop. */\n\n\n  get hasBackdrop() {\n    return this._hasBackdrop;\n  }\n\n  set hasBackdrop(value) {\n    this._hasBackdrop = coerceBooleanProperty(value);\n  }\n  /** Whether or not the overlay should be locked when scrolling. */\n\n\n  get lockPosition() {\n    return this._lockPosition;\n  }\n\n  set lockPosition(value) {\n    this._lockPosition = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n\n\n  get flexibleDimensions() {\n    return this._flexibleDimensions;\n  }\n\n  set flexibleDimensions(value) {\n    this._flexibleDimensions = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n\n\n  get growAfterOpen() {\n    return this._growAfterOpen;\n  }\n\n  set growAfterOpen(value) {\n    this._growAfterOpen = coerceBooleanProperty(value);\n  }\n  /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n\n\n  get push() {\n    return this._push;\n  }\n\n  set push(value) {\n    this._push = coerceBooleanProperty(value);\n  }\n  /** The associated overlay reference. */\n\n\n  get overlayRef() {\n    return this._overlayRef;\n  }\n  /** The element's layout direction. */\n\n\n  get dir() {\n    return this._dir ? this._dir.value : 'ltr';\n  }\n\n  ngOnDestroy() {\n    this._attachSubscription.unsubscribe();\n\n    this._detachSubscription.unsubscribe();\n\n    this._backdropSubscription.unsubscribe();\n\n    this._positionSubscription.unsubscribe();\n\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n    }\n  }\n\n  ngOnChanges(changes) {\n    if (this._position) {\n      this._updatePositionStrategy(this._position);\n\n      this._overlayRef.updateSize({\n        width: this.width,\n        minWidth: this.minWidth,\n        height: this.height,\n        minHeight: this.minHeight\n      });\n\n      if (changes['origin'] && this.open) {\n        this._position.apply();\n      }\n    }\n\n    if (changes['open']) {\n      this.open ? this._attachOverlay() : this._detachOverlay();\n    }\n  }\n  /** Creates an overlay */\n\n\n  _createOverlay() {\n    if (!this.positions || !this.positions.length) {\n      this.positions = defaultPositionList;\n    }\n\n    const overlayRef = this._overlayRef = this._overlay.create(this._buildConfig());\n\n    this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n    this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n    overlayRef.keydownEvents().subscribe(event => {\n      this.overlayKeydown.next(event);\n\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n\n        this._detachOverlay();\n      }\n    });\n\n    this._overlayRef.outsidePointerEvents().subscribe(event => {\n      this.overlayOutsideClick.next(event);\n    });\n  }\n  /** Builds the overlay config based on the directive's inputs */\n\n\n  _buildConfig() {\n    const positionStrategy = this._position = this.positionStrategy || this._createPositionStrategy();\n\n    const overlayConfig = new OverlayConfig({\n      direction: this._dir,\n      positionStrategy,\n      scrollStrategy: this.scrollStrategy,\n      hasBackdrop: this.hasBackdrop\n    });\n\n    if (this.width || this.width === 0) {\n      overlayConfig.width = this.width;\n    }\n\n    if (this.height || this.height === 0) {\n      overlayConfig.height = this.height;\n    }\n\n    if (this.minWidth || this.minWidth === 0) {\n      overlayConfig.minWidth = this.minWidth;\n    }\n\n    if (this.minHeight || this.minHeight === 0) {\n      overlayConfig.minHeight = this.minHeight;\n    }\n\n    if (this.backdropClass) {\n      overlayConfig.backdropClass = this.backdropClass;\n    }\n\n    if (this.panelClass) {\n      overlayConfig.panelClass = this.panelClass;\n    }\n\n    return overlayConfig;\n  }\n  /** Updates the state of a position strategy, based on the values of the directive inputs. */\n\n\n  _updatePositionStrategy(positionStrategy) {\n    const positions = this.positions.map(currentPosition => ({\n      originX: currentPosition.originX,\n      originY: currentPosition.originY,\n      overlayX: currentPosition.overlayX,\n      overlayY: currentPosition.overlayY,\n      offsetX: currentPosition.offsetX || this.offsetX,\n      offsetY: currentPosition.offsetY || this.offsetY,\n      panelClass: currentPosition.panelClass || undefined\n    }));\n    return positionStrategy.setOrigin(this._getFlexibleConnectedPositionStrategyOrigin()).withPositions(positions).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector);\n  }\n  /** Returns the position strategy of the overlay to be set on the overlay config */\n\n\n  _createPositionStrategy() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());\n\n    this._updatePositionStrategy(strategy);\n\n    return strategy;\n  }\n\n  _getFlexibleConnectedPositionStrategyOrigin() {\n    if (this.origin instanceof CdkOverlayOrigin) {\n      return this.origin.elementRef;\n    } else {\n      return this.origin;\n    }\n  }\n  /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n\n\n  _attachOverlay() {\n    if (!this._overlayRef) {\n      this._createOverlay();\n    } else {\n      // Update the overlay size, in case the directive's inputs have changed\n      this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n    }\n\n    if (!this._overlayRef.hasAttached()) {\n      this._overlayRef.attach(this._templatePortal);\n    }\n\n    if (this.hasBackdrop) {\n      this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n        this.backdropClick.emit(event);\n      });\n    } else {\n      this._backdropSubscription.unsubscribe();\n    }\n\n    this._positionSubscription.unsubscribe(); // Only subscribe to `positionChanges` if requested, because putting\n    // together all the information for it can be expensive.\n\n\n    if (this.positionChange.observers.length > 0) {\n      this._positionSubscription = this._position.positionChanges.pipe(takeWhile(() => this.positionChange.observers.length > 0)).subscribe(position => {\n        this.positionChange.emit(position);\n\n        if (this.positionChange.observers.length === 0) {\n          this._positionSubscription.unsubscribe();\n        }\n      });\n    }\n  }\n  /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n\n\n  _detachOverlay() {\n    if (this._overlayRef) {\n      this._overlayRef.detach();\n    }\n\n    this._backdropSubscription.unsubscribe();\n\n    this._positionSubscription.unsubscribe();\n  }\n\n}\n\nCdkConnectedOverlay.ɵfac = function CdkConnectedOverlay_Factory(t) {\n  return new (t || CdkConnectedOverlay)(i0.ɵɵdirectiveInject(Overlay), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i5.Directionality, 8));\n};\n\nCdkConnectedOverlay.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkConnectedOverlay,\n  selectors: [[\"\", \"cdk-connected-overlay\", \"\"], [\"\", \"connected-overlay\", \"\"], [\"\", \"cdkConnectedOverlay\", \"\"]],\n  inputs: {\n    origin: [\"cdkConnectedOverlayOrigin\", \"origin\"],\n    positions: [\"cdkConnectedOverlayPositions\", \"positions\"],\n    positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"],\n    offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"],\n    offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"],\n    width: [\"cdkConnectedOverlayWidth\", \"width\"],\n    height: [\"cdkConnectedOverlayHeight\", \"height\"],\n    minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"],\n    minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"],\n    backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"],\n    panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"],\n    viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"],\n    scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"],\n    open: [\"cdkConnectedOverlayOpen\", \"open\"],\n    disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"],\n    transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"],\n    hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\"],\n    lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\"],\n    flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\"],\n    growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\"],\n    push: [\"cdkConnectedOverlayPush\", \"push\"]\n  },\n  outputs: {\n    backdropClick: \"backdropClick\",\n    positionChange: \"positionChange\",\n    attach: \"attach\",\n    detach: \"detach\",\n    overlayKeydown: \"overlayKeydown\",\n    overlayOutsideClick: \"overlayOutsideClick\"\n  },\n  exportAs: [\"cdkConnectedOverlay\"],\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkConnectedOverlay, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n      exportAs: 'cdkConnectedOverlay'\n    }]\n  }], function () {\n    return [{\n      type: Overlay\n    }, {\n      type: i0.TemplateRef\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i5.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    origin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOrigin']\n    }],\n    positions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositions']\n    }],\n    positionStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPositionStrategy']\n    }],\n    offsetX: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetX']\n    }],\n    offsetY: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOffsetY']\n    }],\n    width: [{\n      type: Input,\n      args: ['cdkConnectedOverlayWidth']\n    }],\n    height: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHeight']\n    }],\n    minWidth: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinWidth']\n    }],\n    minHeight: [{\n      type: Input,\n      args: ['cdkConnectedOverlayMinHeight']\n    }],\n    backdropClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayBackdropClass']\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPanelClass']\n    }],\n    viewportMargin: [{\n      type: Input,\n      args: ['cdkConnectedOverlayViewportMargin']\n    }],\n    scrollStrategy: [{\n      type: Input,\n      args: ['cdkConnectedOverlayScrollStrategy']\n    }],\n    open: [{\n      type: Input,\n      args: ['cdkConnectedOverlayOpen']\n    }],\n    disableClose: [{\n      type: Input,\n      args: ['cdkConnectedOverlayDisableClose']\n    }],\n    transformOriginSelector: [{\n      type: Input,\n      args: ['cdkConnectedOverlayTransformOriginOn']\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: ['cdkConnectedOverlayHasBackdrop']\n    }],\n    lockPosition: [{\n      type: Input,\n      args: ['cdkConnectedOverlayLockPosition']\n    }],\n    flexibleDimensions: [{\n      type: Input,\n      args: ['cdkConnectedOverlayFlexibleDimensions']\n    }],\n    growAfterOpen: [{\n      type: Input,\n      args: ['cdkConnectedOverlayGrowAfterOpen']\n    }],\n    push: [{\n      type: Input,\n      args: ['cdkConnectedOverlayPush']\n    }],\n    backdropClick: [{\n      type: Output\n    }],\n    positionChange: [{\n      type: Output\n    }],\n    attach: [{\n      type: Output\n    }],\n    detach: [{\n      type: Output\n    }],\n    overlayKeydown: [{\n      type: Output\n    }],\n    overlayOutsideClick: [{\n      type: Output\n    }]\n  });\n})();\n/** @docs-private */\n\n\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\n\n\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n  provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass OverlayModule {}\n\nOverlayModule.ɵfac = function OverlayModule_Factory(t) {\n  return new (t || OverlayModule)();\n};\n\nOverlayModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: OverlayModule\n});\nOverlayModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n  imports: [[BidiModule, PortalModule, ScrollingModule], ScrollingModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, PortalModule, ScrollingModule],\n      exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n      declarations: [CdkConnectedOverlay, CdkOverlayOrigin],\n      providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\n\n\nclass FullscreenOverlayContainer extends OverlayContainer {\n  constructor(_document, platform) {\n    super(_document, platform);\n  }\n\n  ngOnDestroy() {\n    super.ngOnDestroy();\n\n    if (this._fullScreenEventName && this._fullScreenListener) {\n      this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n    }\n  }\n\n  _createContainer() {\n    super._createContainer();\n\n    this._adjustParentForFullscreenChange();\n\n    this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n  }\n\n  _adjustParentForFullscreenChange() {\n    if (!this._containerElement) {\n      return;\n    }\n\n    const fullscreenElement = this.getFullscreenElement();\n    const parent = fullscreenElement || this._document.body;\n    parent.appendChild(this._containerElement);\n  }\n\n  _addFullscreenChangeListener(fn) {\n    const eventName = this._getEventName();\n\n    if (eventName) {\n      if (this._fullScreenListener) {\n        this._document.removeEventListener(eventName, this._fullScreenListener);\n      }\n\n      this._document.addEventListener(eventName, fn);\n\n      this._fullScreenListener = fn;\n    }\n  }\n\n  _getEventName() {\n    if (!this._fullScreenEventName) {\n      const _document = this._document;\n\n      if (_document.fullscreenEnabled) {\n        this._fullScreenEventName = 'fullscreenchange';\n      } else if (_document.webkitFullscreenEnabled) {\n        this._fullScreenEventName = 'webkitfullscreenchange';\n      } else if (_document.mozFullScreenEnabled) {\n        this._fullScreenEventName = 'mozfullscreenchange';\n      } else if (_document.msFullscreenEnabled) {\n        this._fullScreenEventName = 'MSFullscreenChange';\n      }\n    }\n\n    return this._fullScreenEventName;\n  }\n  /**\n   * When the page is put into fullscreen mode, a specific element is specified.\n   * Only that element and its children are visible when in fullscreen mode.\n   */\n\n\n  getFullscreenElement() {\n    const _document = this._document;\n    return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n  }\n\n}\n\nFullscreenOverlayContainer.ɵfac = function FullscreenOverlayContainer_Factory(t) {\n  return new (t || FullscreenOverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n};\n\nFullscreenOverlayContainer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: FullscreenOverlayContainer,\n  factory: FullscreenOverlayContainer.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FullscreenOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i1$1.Platform\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };", "map": {"version": 3, "sources": ["D:/PRYSOMS/Gradvisor/voxpod-admin-panel/futureselves-admin-panel-with-amazon-gateway-2-main/node_modules/@angular/cdk/fesm2020/overlay.mjs"], "names": ["i1", "ScrollingModule", "CdkScrollable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewportRuler", "i6", "DOCUMENT", "i0", "Injectable", "Inject", "Optional", "ElementRef", "ApplicationRef", "InjectionToken", "Directive", "EventEmitter", "Input", "Output", "NgModule", "coerceCssPixelValue", "coerce<PERSON><PERSON><PERSON>", "coerceBooleanProperty", "i1$1", "supportsScrollBehavior", "_getEventTarget", "_isTestEnvironment", "i5", "BidiModule", "DomPortalOutlet", "TemplatePortal", "PortalModule", "Subject", "Subscription", "merge", "take", "takeUntil", "<PERSON><PERSON><PERSON><PERSON>", "ESCAPE", "hasModifierKey", "scrollBehaviorSupported", "BlockScrollStrategy", "constructor", "_viewportRuler", "document", "_previousHTMLStyles", "top", "left", "_isEnabled", "_document", "attach", "enable", "_canBeEnabled", "root", "documentElement", "_previousScrollPosition", "getViewportScrollPosition", "style", "classList", "add", "disable", "html", "body", "htmlStyle", "bodyStyle", "previousHtmlScrollBehavior", "scroll<PERSON>eh<PERSON>or", "previousBodyScrollBehavior", "remove", "window", "scroll", "contains", "viewport", "getViewportSize", "scrollHeight", "height", "scrollWidth", "width", "getMatScrollStrategyAlreadyAttachedError", "Error", "CloseScrollStrategy", "_scrollDispatcher", "_ngZone", "_config", "_scrollSubscription", "_detach", "_overlayRef", "has<PERSON>tta<PERSON>", "run", "detach", "overlayRef", "ngDevMode", "stream", "scrolled", "threshold", "_initialScrollPosition", "subscribe", "scrollPosition", "Math", "abs", "updatePosition", "unsubscribe", "NoopScrollStrategy", "isElementScrolledOutsideView", "element", "scrollContainers", "some", "containerBounds", "outsideAbove", "bottom", "outsideBelow", "outsideLeft", "right", "outsideRight", "isElementClippedByScrolling", "scrollContainerRect", "clippedAbove", "<PERSON><PERSON><PERSON><PERSON>", "clippedLeft", "clippedRight", "RepositionScrollStrategy", "throttle", "scrollThrottle", "autoClose", "overlayRect", "overlayElement", "getBoundingClientRect", "parentRects", "ScrollStrategyOptions", "noop", "close", "config", "block", "reposition", "ɵfac", "NgZone", "ɵprov", "type", "args", "providedIn", "undefined", "decorators", "OverlayConfig", "scrollStrategy", "panelClass", "hasBackdrop", "backdropClass", "disposeOnNavigation", "config<PERSON><PERSON><PERSON>", "Object", "keys", "key", "ConnectionPositionPair", "origin", "overlay", "offsetX", "offsetY", "originX", "originY", "overlayX", "overlayY", "ScrollingVisibility", "ConnectedOverlayPositionChange", "connectionPair", "scrollableViewProperties", "validateVerticalPosition", "property", "value", "validateHorizontalPosition", "BaseOverlayDispatcher", "_attachedOverlays", "ngOnDestroy", "push", "index", "indexOf", "splice", "length", "OverlayKeyboardDispatcher", "_keydownListener", "event", "overlays", "i", "_keydownEvents", "observers", "keydownEvents", "next", "_isAttached", "runOutsideAngular", "addEventListener", "removeEventListener", "OverlayOutsideClickDispatcher", "_platform", "_cursorStyleIsSet", "_pointerDownListener", "_pointerDownEventTarget", "_clickListener", "target", "slice", "_outsidePointerEvents", "outsidePointerEvents", "_addEventListeners", "IOS", "_cursorOriginalV<PERSON>ue", "cursor", "Platform", "OverlayContainer", "_containerElement", "getContainerElement", "_createContainer", "containerClass", "<PERSON><PERSON><PERSON><PERSON>", "oppositePlatformContainers", "querySelectorAll", "container", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "OverlayRef", "_portalOutlet", "_host", "_pane", "_keyboardDispatcher", "_location", "_outsideClickDis<PERSON>tcher", "_backdropElement", "_backdropClick", "_attachments", "_detachments", "_locationChanges", "EMPTY", "_backdropClickHandler", "_backdropTransitionendHandler", "_disposeBackdrop", "_scrollStrategy", "_positionStrategy", "positionStrategy", "backdropElement", "hostElement", "portal", "parentElement", "_previousHostParent", "attachResult", "_updateStackingOrder", "_updateElementSize", "_updateElementDirection", "onStable", "pipe", "_togglePointerEvents", "_attachBackdrop", "_toggleClasses", "dispose", "detachBackdrop", "detachmentResult", "_detachContentWhenStable", "isAttached", "_disposeScrollStrategy", "complete", "backdropClick", "attachments", "detachments", "getConfig", "apply", "updatePositionStrategy", "strategy", "updateSize", "sizeConfig", "setDirection", "dir", "direction", "addPanelClass", "classes", "removePanelClass", "getDirection", "updateScrollStrategy", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "enablePointer", "pointerEvents", "showingClass", "insertBefore", "requestAnimationFrame", "nextS<PERSON>ling", "parentNode", "backdropToDetach", "_backdropTimeout", "setTimeout", "cssClasses", "isAdd", "filter", "c", "subscription", "children", "backdrop", "clearTimeout", "boundingBoxClass", "cssUnitPattern", "FlexibleConnectedPositionStrategy", "connectedTo", "_overlayContainer", "_lastBoundingBoxSize", "_isPushed", "_canPush", "_growAfterOpen", "_hasFlexibleDimensions", "_positionLocked", "_viewportMargin", "_scrollables", "_preferredPositions", "_positionChanges", "_resizeSubscription", "_offsetX", "_offsetY", "_appliedPanelClasses", "position<PERSON><PERSON>es", "<PERSON><PERSON><PERSON><PERSON>", "positions", "_validatePositions", "_boundingBox", "_isDisposed", "_isInitialRender", "_lastPosition", "change", "reapplyLastPosition", "_clearPanelClasses", "_resetOverlayElementStyles", "_resetBoundingBoxStyles", "_viewportRect", "_getNarrowedViewportRect", "_originRect", "_getOriginRect", "_overlayRect", "_containerRect", "originRect", "viewportRect", "containerRect", "flexibleFits", "fallback", "pos", "originPoint", "_getOriginPoint", "overlayPoint", "_getOverlayPoint", "overlayFit", "_getOverlayFit", "isCompletelyWithinViewport", "_applyPosition", "_canFitWithFlexibleDimensions", "position", "boundingBoxRect", "_calculateBoundingBoxRect", "visibleArea", "bestFit", "bestScore", "fit", "score", "weight", "_previousPushAmount", "extendStyles", "alignItems", "justifyContent", "lastPosition", "withScrollableContainers", "scrollables", "withPositions", "withViewportMargin", "margin", "withFlexibleDimensions", "flexibleDimensions", "withGrowAfterOpen", "growAfterOpen", "with<PERSON><PERSON>", "canPush", "withLockedPosition", "isLocked", "_origin", "withDefaultOffsetX", "offset", "withDefaultOffsetY", "withTransformOriginOn", "selector", "_transformOriginSelector", "x", "startX", "_isRtl", "endX", "y", "overlayStartX", "overlayStartY", "point", "rawOverlayRect", "getRoundedBoundingClientRect", "_getOffset", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "visibleWidth", "_subtractOverflows", "visibleHeight", "fitsInViewportVertically", "fitsInViewportHorizontally", "availableHeight", "availableWidth", "getPixelValue", "verticalFit", "horizontalFit", "_pushOverlayOnScreen", "start", "overflowRight", "max", "overflowBottom", "overflowTop", "overflowLeft", "pushX", "pushY", "_setTransformOrigin", "_setOverlayElementStyles", "_setBoundingBoxStyles", "_addPanelClasses", "_getScrollVisibility", "changeEvent", "elements", "xOrigin", "y<PERSON><PERSON><PERSON>", "transform<PERSON><PERSON>in", "isRtl", "smallestDistanceToViewportEdge", "min", "previousHeight", "isBoundedByRightViewportEdge", "isBoundedByLeftViewportEdge", "previousWidth", "styles", "_hasExactPosition", "transform", "hasExactPosition", "hasFlexibleDimensions", "_getExactOverlayY", "_getExactOverlayX", "transformString", "trim", "documentHeight", "clientHeight", "horizontalStyleProperty", "documentWidth", "clientWidth", "originBounds", "overlayBounds", "scrollContainerBounds", "map", "scrollable", "getElementRef", "nativeElement", "isOriginClipped", "isOriginOutsideView", "isOverlayClipped", "isOverlayOutsideView", "overflows", "reduce", "currentValue", "currentOverflow", "axis", "for<PERSON>ach", "pair", "cssClass", "Element", "destination", "source", "hasOwnProperty", "input", "units", "split", "parseFloat", "clientRect", "floor", "wrapperClass", "GlobalPositionStrategy", "_cssPosition", "_topOffset", "_bottomOffset", "_leftOffset", "_rightOffset", "_alignItems", "_justifyContent", "_width", "_height", "centerHorizontally", "centerVertically", "parentStyles", "shouldBeFlushHorizontally", "shouldBeFlushVertically", "marginLeft", "marginTop", "marginBottom", "marginRight", "parent", "OverlayPositionBuilder", "global", "flexibleConnectedTo", "nextUniqueId", "Overlay", "scrollStrategies", "_componentFactoryResolver", "_positionBuilder", "_injector", "_directionality", "create", "host", "_createHostElement", "pane", "_createPaneElement", "portalOutlet", "_createPortalOutlet", "overlayConfig", "id", "_appRef", "get", "ComponentFactoryResolver", "Injector", "Directionality", "Location", "defaultPositionList", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY", "CdkOverlayOrigin", "elementRef", "ɵdir", "exportAs", "CdkConnectedOverlay", "_overlay", "templateRef", "viewContainerRef", "scrollStrategyFactory", "_dir", "_hasBackdrop", "_lockPosition", "_flexibleDimensions", "_push", "_backdropSubscription", "_attachSubscription", "_detachSubscription", "_positionSubscription", "viewportMargin", "open", "disableClose", "positionChange", "overlayKeydown", "overlayOutsideClick", "_templatePortal", "_scrollStrategyFactory", "_position", "_updatePositionStrategy", "lockPosition", "ngOnChanges", "changes", "_attachOverlay", "_detachOverlay", "_createOverlay", "_buildConfig", "emit", "keyCode", "preventDefault", "_createPositionStrategy", "currentPosition", "_getFlexibleConnectedPositionStrategyOrigin", "transformOriginSelector", "TemplateRef", "ViewContainerRef", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY", "CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "OverlayModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "providers", "FullscreenOverlayContainer", "platform", "_fullScreenEventName", "_fullScreenListener", "_adjustParentForFullscreenChange", "_addFullscreenChangeListener", "fullscreenElement", "getFullscreenElement", "fn", "eventName", "_getEventName", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,wBAApB;AACA,SAASC,eAAT,QAAgC,wBAAhC;AACA,SAASC,aAAT,EAAwBC,gBAAxB,EAA0CC,aAA1C,QAA+D,wBAA/D;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,MAArB,EAA6BC,QAA7B,EAAuCC,UAAvC,EAAmDC,cAAnD,EAAmEC,cAAnE,EAAmFC,SAAnF,EAA8FC,YAA9F,EAA4GC,KAA5G,EAAmHC,MAAnH,EAA2HC,QAA3H,QAA2I,eAA3I;AACA,SAASC,mBAAT,EAA8BC,WAA9B,EAA2CC,qBAA3C,QAAwE,uBAAxE;AACA,OAAO,KAAKC,IAAZ,MAAsB,uBAAtB;AACA,SAASC,sBAAT,EAAiCC,eAAjC,EAAkDC,kBAAlD,QAA4E,uBAA5E;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,SAASC,eAAT,EAA0BC,cAA1B,EAA0CC,YAA1C,QAA8D,qBAA9D;AACA,SAASC,OAAT,EAAkBC,YAAlB,EAAgCC,KAAhC,QAA6C,MAA7C;AACA,SAASC,IAAT,EAAeC,SAAf,EAA0BC,SAA1B,QAA2C,gBAA3C;AACA,SAASC,MAAT,EAAiBC,cAAjB,QAAuC,uBAAvC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,uBAAuB,GAAGhB,sBAAsB,EAAtD;AACA;AACA;AACA;;AACA,MAAMiB,mBAAN,CAA0B;AACtBC,EAAAA,WAAW,CAACC,cAAD,EAAiBC,QAAjB,EAA2B;AAClC,SAAKD,cAAL,GAAsBA,cAAtB;AACA,SAAKE,mBAAL,GAA2B;AAAEC,MAAAA,GAAG,EAAE,EAAP;AAAWC,MAAAA,IAAI,EAAE;AAAjB,KAA3B;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAKC,SAAL,GAAiBL,QAAjB;AACH;AACD;;;AACAM,EAAAA,MAAM,GAAG,CAAG;AACZ;;;AACAC,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKC,aAAL,EAAJ,EAA0B;AACtB,YAAMC,IAAI,GAAG,KAAKJ,SAAL,CAAeK,eAA5B;AACA,WAAKC,uBAAL,GAA+B,KAAKZ,cAAL,CAAoBa,yBAApB,EAA/B,CAFsB,CAGtB;;AACA,WAAKX,mBAAL,CAAyBE,IAAzB,GAAgCM,IAAI,CAACI,KAAL,CAAWV,IAAX,IAAmB,EAAnD;AACA,WAAKF,mBAAL,CAAyBC,GAAzB,GAA+BO,IAAI,CAACI,KAAL,CAAWX,GAAX,IAAkB,EAAjD,CALsB,CAMtB;AACA;;AACAO,MAAAA,IAAI,CAACI,KAAL,CAAWV,IAAX,GAAkB3B,mBAAmB,CAAC,CAAC,KAAKmC,uBAAL,CAA6BR,IAA/B,CAArC;AACAM,MAAAA,IAAI,CAACI,KAAL,CAAWX,GAAX,GAAiB1B,mBAAmB,CAAC,CAAC,KAAKmC,uBAAL,CAA6BT,GAA/B,CAApC;AACAO,MAAAA,IAAI,CAACK,SAAL,CAAeC,GAAf,CAAmB,wBAAnB;AACA,WAAKX,UAAL,GAAkB,IAAlB;AACH;AACJ;AACD;;;AACAY,EAAAA,OAAO,GAAG;AACN,QAAI,KAAKZ,UAAT,EAAqB;AACjB,YAAMa,IAAI,GAAG,KAAKZ,SAAL,CAAeK,eAA5B;AACA,YAAMQ,IAAI,GAAG,KAAKb,SAAL,CAAea,IAA5B;AACA,YAAMC,SAAS,GAAGF,IAAI,CAACJ,KAAvB;AACA,YAAMO,SAAS,GAAGF,IAAI,CAACL,KAAvB;AACA,YAAMQ,0BAA0B,GAAGF,SAAS,CAACG,cAAV,IAA4B,EAA/D;AACA,YAAMC,0BAA0B,GAAGH,SAAS,CAACE,cAAV,IAA4B,EAA/D;AACA,WAAKlB,UAAL,GAAkB,KAAlB;AACAe,MAAAA,SAAS,CAAChB,IAAV,GAAiB,KAAKF,mBAAL,CAAyBE,IAA1C;AACAgB,MAAAA,SAAS,CAACjB,GAAV,GAAgB,KAAKD,mBAAL,CAAyBC,GAAzC;AACAe,MAAAA,IAAI,CAACH,SAAL,CAAeU,MAAf,CAAsB,wBAAtB,EAViB,CAWjB;AACA;AACA;AACA;AACA;;AACA,UAAI5B,uBAAJ,EAA6B;AACzBuB,QAAAA,SAAS,CAACG,cAAV,GAA2BF,SAAS,CAACE,cAAV,GAA2B,MAAtD;AACH;;AACDG,MAAAA,MAAM,CAACC,MAAP,CAAc,KAAKf,uBAAL,CAA6BR,IAA3C,EAAiD,KAAKQ,uBAAL,CAA6BT,GAA9E;;AACA,UAAIN,uBAAJ,EAA6B;AACzBuB,QAAAA,SAAS,CAACG,cAAV,GAA2BD,0BAA3B;AACAD,QAAAA,SAAS,CAACE,cAAV,GAA2BC,0BAA3B;AACH;AACJ;AACJ;;AACDf,EAAAA,aAAa,GAAG;AACZ;AACA;AACA;AACA,UAAMS,IAAI,GAAG,KAAKZ,SAAL,CAAeK,eAA5B;;AACA,QAAIO,IAAI,CAACH,SAAL,CAAea,QAAf,CAAwB,wBAAxB,KAAqD,KAAKvB,UAA9D,EAA0E;AACtE,aAAO,KAAP;AACH;;AACD,UAAMc,IAAI,GAAG,KAAKb,SAAL,CAAea,IAA5B;;AACA,UAAMU,QAAQ,GAAG,KAAK7B,cAAL,CAAoB8B,eAApB,EAAjB;;AACA,WAAOX,IAAI,CAACY,YAAL,GAAoBF,QAAQ,CAACG,MAA7B,IAAuCb,IAAI,CAACc,WAAL,GAAmBJ,QAAQ,CAACK,KAA1E;AACH;;AAhEqB;AAmE1B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,SAASC,wCAAT,GAAoD;AAChD,SAAOC,KAAK,CAAE,4CAAF,CAAZ;AACH;AAED;AACA;AACA;;;AACA,MAAMC,mBAAN,CAA0B;AACtBtC,EAAAA,WAAW,CAACuC,iBAAD,EAAoBC,OAApB,EAA6BvC,cAA7B,EAA6CwC,OAA7C,EAAsD;AAC7D,SAAKF,iBAAL,GAAyBA,iBAAzB;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKvC,cAAL,GAAsBA,cAAtB;AACA,SAAKwC,OAAL,GAAeA,OAAf;AACA,SAAKC,mBAAL,GAA2B,IAA3B;AACA;;AACA,SAAKC,OAAL,GAAe,MAAM;AACjB,WAAKzB,OAAL;;AACA,UAAI,KAAK0B,WAAL,CAAiBC,WAAjB,EAAJ,EAAoC;AAChC,aAAKL,OAAL,CAAaM,GAAb,CAAiB,MAAM,KAAKF,WAAL,CAAiBG,MAAjB,EAAvB;AACH;AACJ,KALD;AAMH;AACD;;;AACAvC,EAAAA,MAAM,CAACwC,UAAD,EAAa;AACf,QAAI,KAAKJ,WAAL,KAAqB,OAAOK,SAAP,KAAqB,WAArB,IAAoCA,SAAzD,CAAJ,EAAyE;AACrE,YAAMb,wCAAwC,EAA9C;AACH;;AACD,SAAKQ,WAAL,GAAmBI,UAAnB;AACH;AACD;;;AACAvC,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKiC,mBAAT,EAA8B;AAC1B;AACH;;AACD,UAAMQ,MAAM,GAAG,KAAKX,iBAAL,CAAuBY,QAAvB,CAAgC,CAAhC,CAAf;;AACA,QAAI,KAAKV,OAAL,IAAgB,KAAKA,OAAL,CAAaW,SAA7B,IAA0C,KAAKX,OAAL,CAAaW,SAAb,GAAyB,CAAvE,EAA0E;AACtE,WAAKC,sBAAL,GAA8B,KAAKpD,cAAL,CAAoBa,yBAApB,GAAgDV,GAA9E;AACA,WAAKsC,mBAAL,GAA2BQ,MAAM,CAACI,SAAP,CAAiB,MAAM;AAC9C,cAAMC,cAAc,GAAG,KAAKtD,cAAL,CAAoBa,yBAApB,GAAgDV,GAAvE;;AACA,YAAIoD,IAAI,CAACC,GAAL,CAASF,cAAc,GAAG,KAAKF,sBAA/B,IAAyD,KAAKZ,OAAL,CAAaW,SAA1E,EAAqF;AACjF,eAAKT,OAAL;AACH,SAFD,MAGK;AACD,eAAKC,WAAL,CAAiBc,cAAjB;AACH;AACJ,OAR0B,CAA3B;AASH,KAXD,MAYK;AACD,WAAKhB,mBAAL,GAA2BQ,MAAM,CAACI,SAAP,CAAiB,KAAKX,OAAtB,CAA3B;AACH;AACJ;AACD;;;AACAzB,EAAAA,OAAO,GAAG;AACN,QAAI,KAAKwB,mBAAT,EAA8B;AAC1B,WAAKA,mBAAL,CAAyBiB,WAAzB;;AACA,WAAKjB,mBAAL,GAA2B,IAA3B;AACH;AACJ;;AACDK,EAAAA,MAAM,GAAG;AACL,SAAK7B,OAAL;AACA,SAAK0B,WAAL,GAAmB,IAAnB;AACH;;AAtDqB;AAyD1B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMgB,kBAAN,CAAyB;AACrB;AACAnD,EAAAA,MAAM,GAAG,CAAG;AACZ;;;AACAS,EAAAA,OAAO,GAAG,CAAG;AACb;;;AACAV,EAAAA,MAAM,GAAG,CAAG;;AANS;AASzB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASqD,4BAAT,CAAsCC,OAAtC,EAA+CC,gBAA/C,EAAiE;AAC7D,SAAOA,gBAAgB,CAACC,IAAjB,CAAsBC,eAAe,IAAI;AAC5C,UAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAR,GAAiBF,eAAe,CAAC7D,GAAtD;AACA,UAAMgE,YAAY,GAAGN,OAAO,CAAC1D,GAAR,GAAc6D,eAAe,CAACE,MAAnD;AACA,UAAME,WAAW,GAAGP,OAAO,CAACQ,KAAR,GAAgBL,eAAe,CAAC5D,IAApD;AACA,UAAMkE,YAAY,GAAGT,OAAO,CAACzD,IAAR,GAAe4D,eAAe,CAACK,KAApD;AACA,WAAOJ,YAAY,IAAIE,YAAhB,IAAgCC,WAAhC,IAA+CE,YAAtD;AACH,GANM,CAAP;AAOH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,2BAAT,CAAqCV,OAArC,EAA8CC,gBAA9C,EAAgE;AAC5D,SAAOA,gBAAgB,CAACC,IAAjB,CAAsBS,mBAAmB,IAAI;AAChD,UAAMC,YAAY,GAAGZ,OAAO,CAAC1D,GAAR,GAAcqE,mBAAmB,CAACrE,GAAvD;AACA,UAAMuE,YAAY,GAAGb,OAAO,CAACK,MAAR,GAAiBM,mBAAmB,CAACN,MAA1D;AACA,UAAMS,WAAW,GAAGd,OAAO,CAACzD,IAAR,GAAeoE,mBAAmB,CAACpE,IAAvD;AACA,UAAMwE,YAAY,GAAGf,OAAO,CAACQ,KAAR,GAAgBG,mBAAmB,CAACH,KAAzD;AACA,WAAOI,YAAY,IAAIC,YAAhB,IAAgCC,WAAhC,IAA+CC,YAAtD;AACH,GANM,CAAP;AAOH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMC,wBAAN,CAA+B;AAC3B9E,EAAAA,WAAW,CAACuC,iBAAD,EAAoBtC,cAApB,EAAoCuC,OAApC,EAA6CC,OAA7C,EAAsD;AAC7D,SAAKF,iBAAL,GAAyBA,iBAAzB;AACA,SAAKtC,cAAL,GAAsBA,cAAtB;AACA,SAAKuC,OAAL,GAAeA,OAAf;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKC,mBAAL,GAA2B,IAA3B;AACH;AACD;;;AACAlC,EAAAA,MAAM,CAACwC,UAAD,EAAa;AACf,QAAI,KAAKJ,WAAL,KAAqB,OAAOK,SAAP,KAAqB,WAArB,IAAoCA,SAAzD,CAAJ,EAAyE;AACrE,YAAMb,wCAAwC,EAA9C;AACH;;AACD,SAAKQ,WAAL,GAAmBI,UAAnB;AACH;AACD;;;AACAvC,EAAAA,MAAM,GAAG;AACL,QAAI,CAAC,KAAKiC,mBAAV,EAA+B;AAC3B,YAAMqC,QAAQ,GAAG,KAAKtC,OAAL,GAAe,KAAKA,OAAL,CAAauC,cAA5B,GAA6C,CAA9D;AACA,WAAKtC,mBAAL,GAA2B,KAAKH,iBAAL,CAAuBY,QAAvB,CAAgC4B,QAAhC,EAA0CzB,SAA1C,CAAoD,MAAM;AACjF,aAAKV,WAAL,CAAiBc,cAAjB,GADiF,CAEjF;;;AACA,YAAI,KAAKjB,OAAL,IAAgB,KAAKA,OAAL,CAAawC,SAAjC,EAA4C;AACxC,gBAAMC,WAAW,GAAG,KAAKtC,WAAL,CAAiBuC,cAAjB,CAAgCC,qBAAhC,EAApB;;AACA,gBAAM;AAAEjD,YAAAA,KAAF;AAASF,YAAAA;AAAT,cAAoB,KAAKhC,cAAL,CAAoB8B,eAApB,EAA1B,CAFwC,CAGxC;AACA;;;AACA,gBAAMsD,WAAW,GAAG,CAAC;AAAElD,YAAAA,KAAF;AAASF,YAAAA,MAAT;AAAiBkC,YAAAA,MAAM,EAAElC,MAAzB;AAAiCqC,YAAAA,KAAK,EAAEnC,KAAxC;AAA+C/B,YAAAA,GAAG,EAAE,CAApD;AAAuDC,YAAAA,IAAI,EAAE;AAA7D,WAAD,CAApB;;AACA,cAAIwD,4BAA4B,CAACqB,WAAD,EAAcG,WAAd,CAAhC,EAA4D;AACxD,iBAAKnE,OAAL;;AACA,iBAAKsB,OAAL,CAAaM,GAAb,CAAiB,MAAM,KAAKF,WAAL,CAAiBG,MAAjB,EAAvB;AACH;AACJ;AACJ,OAd0B,CAA3B;AAeH;AACJ;AACD;;;AACA7B,EAAAA,OAAO,GAAG;AACN,QAAI,KAAKwB,mBAAT,EAA8B;AAC1B,WAAKA,mBAAL,CAAyBiB,WAAzB;;AACA,WAAKjB,mBAAL,GAA2B,IAA3B;AACH;AACJ;;AACDK,EAAAA,MAAM,GAAG;AACL,SAAK7B,OAAL;AACA,SAAK0B,WAAL,GAAmB,IAAnB;AACH;;AA9C0B;AAiD/B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0C,qBAAN,CAA4B;AACxBtF,EAAAA,WAAW,CAACuC,iBAAD,EAAoBtC,cAApB,EAAoCuC,OAApC,EAA6CtC,QAA7C,EAAuD;AAC9D,SAAKqC,iBAAL,GAAyBA,iBAAzB;AACA,SAAKtC,cAAL,GAAsBA,cAAtB;AACA,SAAKuC,OAAL,GAAeA,OAAf;AACA;;AACA,SAAK+C,IAAL,GAAY,MAAM,IAAI3B,kBAAJ,EAAlB;AACA;AACR;AACA;AACA;;;AACQ,SAAK4B,KAAL,GAAcC,MAAD,IAAY,IAAInD,mBAAJ,CAAwB,KAAKC,iBAA7B,EAAgD,KAAKC,OAArD,EAA8D,KAAKvC,cAAnE,EAAmFwF,MAAnF,CAAzB;AACA;;;AACA,SAAKC,KAAL,GAAa,MAAM,IAAI3F,mBAAJ,CAAwB,KAAKE,cAA7B,EAA6C,KAAKM,SAAlD,CAAnB;AACA;AACR;AACA;AACA;AACA;;;AACQ,SAAKoF,UAAL,GAAmBF,MAAD,IAAY,IAAIX,wBAAJ,CAA6B,KAAKvC,iBAAlC,EAAqD,KAAKtC,cAA1D,EAA0E,KAAKuC,OAA/E,EAAwFiD,MAAxF,CAA9B;;AACA,SAAKlF,SAAL,GAAiBL,QAAjB;AACH;;AArBuB;;AAuB5BoF,qBAAqB,CAACM,IAAtB;AAAA,mBAAkHN,qBAAlH,EAAwGxH,EAAxG,UAAyJP,EAAE,CAACG,gBAA5J,GAAwGI,EAAxG,UAAyLP,EAAE,CAACI,aAA5L,GAAwGG,EAAxG,UAAsNA,EAAE,CAAC+H,MAAzN,GAAwG/H,EAAxG,UAA4OD,QAA5O;AAAA;;AACAyH,qBAAqB,CAACQ,KAAtB,kBADwGhI,EACxG;AAAA,SAAsHwH,qBAAtH;AAAA,WAAsHA,qBAAtH;AAAA,cAAyJ;AAAzJ;;AACA;AAAA,qDAFwGxH,EAExG,mBAA2FwH,qBAA3F,EAA8H,CAAC;AACnHS,IAAAA,IAAI,EAAEhI,UAD6G;AAEnHiI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF6G,GAAD,CAA9H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAExI,EAAE,CAACG;AAAX,KAAD,EAAgC;AAAEqI,MAAAA,IAAI,EAAExI,EAAE,CAACI;AAAX,KAAhC,EAA4D;AAAEoI,MAAAA,IAAI,EAAEjI,EAAE,CAAC+H;AAAX,KAA5D,EAAiF;AAAEE,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9IJ,QAAAA,IAAI,EAAE/H,MADwI;AAE9IgI,QAAAA,IAAI,EAAE,CAACnI,QAAD;AAFwI,OAAD;AAA/B,KAAjF,CAAP;AAGlB,GANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMuI,aAAN,CAAoB;AAChBpG,EAAAA,WAAW,CAACyF,MAAD,EAAS;AAChB;AACA,SAAKY,cAAL,GAAsB,IAAIzC,kBAAJ,EAAtB;AACA;;AACA,SAAK0C,UAAL,GAAkB,EAAlB;AACA;;AACA,SAAKC,WAAL,GAAmB,KAAnB;AACA;;AACA,SAAKC,aAAL,GAAqB,2BAArB;AACA;AACR;AACA;AACA;AACA;;AACQ,SAAKC,mBAAL,GAA2B,KAA3B;;AACA,QAAIhB,MAAJ,EAAY;AACR;AACA;AACA;AACA,YAAMiB,UAAU,GAAGC,MAAM,CAACC,IAAP,CAAYnB,MAAZ,CAAnB;;AACA,WAAK,MAAMoB,GAAX,IAAkBH,UAAlB,EAA8B;AAC1B,YAAIjB,MAAM,CAACoB,GAAD,CAAN,KAAgBX,SAApB,EAA+B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,eAAKW,GAAL,IAAYpB,MAAM,CAACoB,GAAD,CAAlB;AACH;AACJ;AACJ;AACJ;;AAjCe;AAoCpB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,sBAAN,CAA6B;AACzB9G,EAAAA,WAAW,CAAC+G,MAAD,EAASC,OAAT;AACX;AACAC,EAAAA,OAFW;AAGX;AACAC,EAAAA,OAJW;AAKX;AACAZ,EAAAA,UANW,EAMC;AACR,SAAKW,OAAL,GAAeA,OAAf;AACA,SAAKC,OAAL,GAAeA,OAAf;AACA,SAAKZ,UAAL,GAAkBA,UAAlB;AACA,SAAKa,OAAL,GAAeJ,MAAM,CAACI,OAAtB;AACA,SAAKC,OAAL,GAAeL,MAAM,CAACK,OAAtB;AACA,SAAKC,QAAL,GAAgBL,OAAO,CAACK,QAAxB;AACA,SAAKC,QAAL,GAAgBN,OAAO,CAACM,QAAxB;AACH;;AAfwB;AAiB7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,mBAAN,CAA0B;AAE1B;;;AACA,MAAMC,8BAAN,CAAqC;AACjCxH,EAAAA,WAAW;AACX;AACAyH,EAAAA,cAFW;AAGX;AACAC,EAAAA,wBAJW,EAIe;AACtB,SAAKD,cAAL,GAAsBA,cAAtB;AACA,SAAKC,wBAAL,GAAgCA,wBAAhC;AACH;;AARgC;AAUrC;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,wBAAT,CAAkCC,QAAlC,EAA4CC,KAA5C,EAAmD;AAC/C,MAAIA,KAAK,KAAK,KAAV,IAAmBA,KAAK,KAAK,QAA7B,IAAyCA,KAAK,KAAK,QAAvD,EAAiE;AAC7D,UAAMxF,KAAK,CAAE,8BAA6BuF,QAAS,KAAIC,KAAM,KAAjD,GACP,uCADM,CAAX;AAEH;AACJ;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,0BAAT,CAAoCF,QAApC,EAA8CC,KAA9C,EAAqD;AACjD,MAAIA,KAAK,KAAK,OAAV,IAAqBA,KAAK,KAAK,KAA/B,IAAwCA,KAAK,KAAK,QAAtD,EAAgE;AAC5D,UAAMxF,KAAK,CAAE,8BAA6BuF,QAAS,KAAIC,KAAM,KAAjD,GACP,sCADM,CAAX;AAEH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAME,qBAAN,CAA4B;AACxB/H,EAAAA,WAAW,CAACE,QAAD,EAAW;AAClB;AACA,SAAK8H,iBAAL,GAAyB,EAAzB;AACA,SAAKzH,SAAL,GAAiBL,QAAjB;AACH;;AACD+H,EAAAA,WAAW,GAAG;AACV,SAAKlF,MAAL;AACH;AACD;;;AACA9B,EAAAA,GAAG,CAAC+B,UAAD,EAAa;AACZ;AACA,SAAKtB,MAAL,CAAYsB,UAAZ;;AACA,SAAKgF,iBAAL,CAAuBE,IAAvB,CAA4BlF,UAA5B;AACH;AACD;;;AACAtB,EAAAA,MAAM,CAACsB,UAAD,EAAa;AACf,UAAMmF,KAAK,GAAG,KAAKH,iBAAL,CAAuBI,OAAvB,CAA+BpF,UAA/B,CAAd;;AACA,QAAImF,KAAK,GAAG,CAAC,CAAb,EAAgB;AACZ,WAAKH,iBAAL,CAAuBK,MAAvB,CAA8BF,KAA9B,EAAqC,CAArC;AACH,KAJc,CAKf;;;AACA,QAAI,KAAKH,iBAAL,CAAuBM,MAAvB,KAAkC,CAAtC,EAAyC;AACrC,WAAKvF,MAAL;AACH;AACJ;;AAzBuB;;AA2B5BgF,qBAAqB,CAACnC,IAAtB;AAAA,mBAAkHmC,qBAAlH,EA7LwGjK,EA6LxG,UAAyJD,QAAzJ;AAAA;;AACAkK,qBAAqB,CAACjC,KAAtB,kBA9LwGhI,EA8LxG;AAAA,SAAsHiK,qBAAtH;AAAA,WAAsHA,qBAAtH;AAAA,cAAyJ;AAAzJ;;AACA;AAAA,qDA/LwGjK,EA+LxG,mBAA2FiK,qBAA3F,EAA8H,CAAC;AACnHhC,IAAAA,IAAI,EAAEhI,UAD6G;AAEnHiI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF6G,GAAD,CAA9H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DJ,QAAAA,IAAI,EAAE/H,MADwD;AAE9DgI,QAAAA,IAAI,EAAE,CAACnI,QAAD;AAFwD,OAAD;AAA/B,KAAD,CAAP;AAGlB,GANxB;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0K,yBAAN,SAAwCR,qBAAxC,CAA8D;AAC1D/H,EAAAA,WAAW,CAACE,QAAD;AACX;AACAsC,EAAAA,OAFW,EAEF;AACL,UAAMtC,QAAN;AACA,SAAKsC,OAAL,GAAeA,OAAf;AACA;;AACA,SAAKgG,gBAAL,GAAyBC,KAAD,IAAW;AAC/B,YAAMC,QAAQ,GAAG,KAAKV,iBAAtB;;AACA,WAAK,IAAIW,CAAC,GAAGD,QAAQ,CAACJ,MAAT,GAAkB,CAA/B,EAAkCK,CAAC,GAAG,CAAC,CAAvC,EAA0CA,CAAC,EAA3C,EAA+C;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,YAAID,QAAQ,CAACC,CAAD,CAAR,CAAYC,cAAZ,CAA2BC,SAA3B,CAAqCP,MAArC,GAA8C,CAAlD,EAAqD;AACjD,gBAAMQ,aAAa,GAAGJ,QAAQ,CAACC,CAAD,CAAR,CAAYC,cAAlC;AACA;;AACA,cAAI,KAAKpG,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAaM,GAAb,CAAiB,MAAMgG,aAAa,CAACC,IAAd,CAAmBN,KAAnB,CAAvB;AACH,WAFD,MAGK;AACDK,YAAAA,aAAa,CAACC,IAAd,CAAmBN,KAAnB;AACH;;AACD;AACH;AACJ;AACJ,KArBD;AAsBH;AACD;;;AACAxH,EAAAA,GAAG,CAAC+B,UAAD,EAAa;AACZ,UAAM/B,GAAN,CAAU+B,UAAV,EADY,CAEZ;;AACA,QAAI,CAAC,KAAKgG,WAAV,EAAuB;AACnB;AACA,UAAI,KAAKxG,OAAT,EAAkB;AACd,aAAKA,OAAL,CAAayG,iBAAb,CAA+B,MAAM,KAAK1I,SAAL,CAAea,IAAf,CAAoB8H,gBAApB,CAAqC,SAArC,EAAgD,KAAKV,gBAArD,CAArC;AACH,OAFD,MAGK;AACD,aAAKjI,SAAL,CAAea,IAAf,CAAoB8H,gBAApB,CAAqC,SAArC,EAAgD,KAAKV,gBAArD;AACH;;AACD,WAAKQ,WAAL,GAAmB,IAAnB;AACH;AACJ;AACD;;;AACAjG,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKiG,WAAT,EAAsB;AAClB,WAAKzI,SAAL,CAAea,IAAf,CAAoB+H,mBAApB,CAAwC,SAAxC,EAAmD,KAAKX,gBAAxD;;AACA,WAAKQ,WAAL,GAAmB,KAAnB;AACH;AACJ;;AAnDyD;;AAqD9DT,yBAAyB,CAAC3C,IAA1B;AAAA,mBAAsH2C,yBAAtH,EAxQwGzK,EAwQxG,UAAiKD,QAAjK,GAxQwGC,EAwQxG,UAAsLA,EAAE,CAAC+H,MAAzL;AAAA;;AACA0C,yBAAyB,CAACzC,KAA1B,kBAzQwGhI,EAyQxG;AAAA,SAA0HyK,yBAA1H;AAAA,WAA0HA,yBAA1H;AAAA,cAAiK;AAAjK;;AACA;AAAA,qDA1QwGzK,EA0QxG,mBAA2FyK,yBAA3F,EAAkI,CAAC;AACvHxC,IAAAA,IAAI,EAAEhI,UADiH;AAEvHiI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFiH,GAAD,CAAlI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DJ,QAAAA,IAAI,EAAE/H,MADwD;AAE9DgI,QAAAA,IAAI,EAAE,CAACnI,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEkI,MAAAA,IAAI,EAAEjI,EAAE,CAAC+H,MAAX;AAAmBM,MAAAA,UAAU,EAAE,CAAC;AAClCJ,QAAAA,IAAI,EAAE9H;AAD4B,OAAD;AAA/B,KAH2B,CAAP;AAKlB,GARxB;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmL,6BAAN,SAA4CrB,qBAA5C,CAAkE;AAC9D/H,EAAAA,WAAW,CAACE,QAAD,EAAWmJ,SAAX;AACX;AACA7G,EAAAA,OAFW,EAEF;AACL,UAAMtC,QAAN;AACA,SAAKmJ,SAAL,GAAiBA,SAAjB;AACA,SAAK7G,OAAL,GAAeA,OAAf;AACA,SAAK8G,iBAAL,GAAyB,KAAzB;AACA;;AACA,SAAKC,oBAAL,GAA6Bd,KAAD,IAAW;AACnC,WAAKe,uBAAL,GAA+BzK,eAAe,CAAC0J,KAAD,CAA9C;AACH,KAFD;AAGA;;;AACA,SAAKgB,cAAL,GAAuBhB,KAAD,IAAW;AAC7B,YAAMiB,MAAM,GAAG3K,eAAe,CAAC0J,KAAD,CAA9B,CAD6B,CAE7B;AACA;AACA;AACA;AACA;AACA;;;AACA,YAAM1B,MAAM,GAAG0B,KAAK,CAAC1C,IAAN,KAAe,OAAf,IAA0B,KAAKyD,uBAA/B,GACT,KAAKA,uBADI,GAETE,MAFN,CAR6B,CAW7B;AACA;;AACA,WAAKF,uBAAL,GAA+B,IAA/B,CAb6B,CAc7B;AACA;AACA;;AACA,YAAMd,QAAQ,GAAG,KAAKV,iBAAL,CAAuB2B,KAAvB,EAAjB,CAjB6B,CAkB7B;AACA;AACA;AACA;;;AACA,WAAK,IAAIhB,CAAC,GAAGD,QAAQ,CAACJ,MAAT,GAAkB,CAA/B,EAAkCK,CAAC,GAAG,CAAC,CAAvC,EAA0CA,CAAC,EAA3C,EAA+C;AAC3C,cAAM3F,UAAU,GAAG0F,QAAQ,CAACC,CAAD,CAA3B;;AACA,YAAI3F,UAAU,CAAC4G,qBAAX,CAAiCf,SAAjC,CAA2CP,MAA3C,GAAoD,CAApD,IAAyD,CAACtF,UAAU,CAACH,WAAX,EAA9D,EAAwF;AACpF;AACH,SAJ0C,CAK3C;AACA;AACA;;;AACA,YAAIG,UAAU,CAACmC,cAAX,CAA0BtD,QAA1B,CAAmC6H,MAAnC,KACA1G,UAAU,CAACmC,cAAX,CAA0BtD,QAA1B,CAAmCkF,MAAnC,CADJ,EACgD;AAC5C;AACH;;AACD,cAAM8C,oBAAoB,GAAG7G,UAAU,CAAC4G,qBAAxC;AACA;;AACA,YAAI,KAAKpH,OAAT,EAAkB;AACd,eAAKA,OAAL,CAAaM,GAAb,CAAiB,MAAM+G,oBAAoB,CAACd,IAArB,CAA0BN,KAA1B,CAAvB;AACH,SAFD,MAGK;AACDoB,UAAAA,oBAAoB,CAACd,IAArB,CAA0BN,KAA1B;AACH;AACJ;AACJ,KA3CD;AA4CH;AACD;;;AACAxH,EAAAA,GAAG,CAAC+B,UAAD,EAAa;AACZ,UAAM/B,GAAN,CAAU+B,UAAV,EADY,CAEZ;AACA;AACA;AACA;AACA;AACA;;AACA,QAAI,CAAC,KAAKgG,WAAV,EAAuB;AACnB,YAAM5H,IAAI,GAAG,KAAKb,SAAL,CAAea,IAA5B;AACA;;AACA,UAAI,KAAKoB,OAAT,EAAkB;AACd,aAAKA,OAAL,CAAayG,iBAAb,CAA+B,MAAM,KAAKa,kBAAL,CAAwB1I,IAAxB,CAArC;AACH,OAFD,MAGK;AACD,aAAK0I,kBAAL,CAAwB1I,IAAxB;AACH,OARkB,CASnB;AACA;;;AACA,UAAI,KAAKiI,SAAL,CAAeU,GAAf,IAAsB,CAAC,KAAKT,iBAAhC,EAAmD;AAC/C,aAAKU,oBAAL,GAA4B5I,IAAI,CAACL,KAAL,CAAWkJ,MAAvC;AACA7I,QAAAA,IAAI,CAACL,KAAL,CAAWkJ,MAAX,GAAoB,SAApB;AACA,aAAKX,iBAAL,GAAyB,IAAzB;AACH;;AACD,WAAKN,WAAL,GAAmB,IAAnB;AACH;AACJ;AACD;;;AACAjG,EAAAA,MAAM,GAAG;AACL,QAAI,KAAKiG,WAAT,EAAsB;AAClB,YAAM5H,IAAI,GAAG,KAAKb,SAAL,CAAea,IAA5B;AACAA,MAAAA,IAAI,CAAC+H,mBAAL,CAAyB,aAAzB,EAAwC,KAAKI,oBAA7C,EAAmE,IAAnE;AACAnI,MAAAA,IAAI,CAAC+H,mBAAL,CAAyB,OAAzB,EAAkC,KAAKM,cAAvC,EAAuD,IAAvD;AACArI,MAAAA,IAAI,CAAC+H,mBAAL,CAAyB,UAAzB,EAAqC,KAAKM,cAA1C,EAA0D,IAA1D;AACArI,MAAAA,IAAI,CAAC+H,mBAAL,CAAyB,aAAzB,EAAwC,KAAKM,cAA7C,EAA6D,IAA7D;;AACA,UAAI,KAAKJ,SAAL,CAAeU,GAAf,IAAsB,KAAKT,iBAA/B,EAAkD;AAC9ClI,QAAAA,IAAI,CAACL,KAAL,CAAWkJ,MAAX,GAAoB,KAAKD,oBAAzB;AACA,aAAKV,iBAAL,GAAyB,KAAzB;AACH;;AACD,WAAKN,WAAL,GAAmB,KAAnB;AACH;AACJ;;AACDc,EAAAA,kBAAkB,CAAC1I,IAAD,EAAO;AACrBA,IAAAA,IAAI,CAAC8H,gBAAL,CAAsB,aAAtB,EAAqC,KAAKK,oBAA1C,EAAgE,IAAhE;AACAnI,IAAAA,IAAI,CAAC8H,gBAAL,CAAsB,OAAtB,EAA+B,KAAKO,cAApC,EAAoD,IAApD;AACArI,IAAAA,IAAI,CAAC8H,gBAAL,CAAsB,UAAtB,EAAkC,KAAKO,cAAvC,EAAuD,IAAvD;AACArI,IAAAA,IAAI,CAAC8H,gBAAL,CAAsB,aAAtB,EAAqC,KAAKO,cAA1C,EAA0D,IAA1D;AACH;;AA1G6D;;AA4GlEL,6BAA6B,CAACxD,IAA9B;AAAA,mBAA0HwD,6BAA1H,EA5YwGtL,EA4YxG,UAAyKD,QAAzK,GA5YwGC,EA4YxG,UAA8Le,IAAI,CAACqL,QAAnM,GA5YwGpM,EA4YxG,UAAwNA,EAAE,CAAC+H,MAA3N;AAAA;;AACAuD,6BAA6B,CAACtD,KAA9B,kBA7YwGhI,EA6YxG;AAAA,SAA8HsL,6BAA9H;AAAA,WAA8HA,6BAA9H;AAAA,cAAyK;AAAzK;;AACA;AAAA,qDA9YwGtL,EA8YxG,mBAA2FsL,6BAA3F,EAAsI,CAAC;AAC3HrD,IAAAA,IAAI,EAAEhI,UADqH;AAE3HiI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFqH,GAAD,CAAtI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DJ,QAAAA,IAAI,EAAE/H,MADwD;AAE9DgI,QAAAA,IAAI,EAAE,CAACnI,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEkI,MAAAA,IAAI,EAAElH,IAAI,CAACqL;AAAb,KAH2B,EAGF;AAAEnE,MAAAA,IAAI,EAAEjI,EAAE,CAAC+H,MAAX;AAAmBM,MAAAA,UAAU,EAAE,CAAC;AAC3DJ,QAAAA,IAAI,EAAE9H;AADqD,OAAD;AAA/B,KAHE,CAAP;AAKlB,GARxB;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMkM,gBAAN,CAAuB;AACnBnK,EAAAA,WAAW,CAACE,QAAD,EAAWmJ,SAAX,EAAsB;AAC7B,SAAKA,SAAL,GAAiBA,SAAjB;AACA,SAAK9I,SAAL,GAAiBL,QAAjB;AACH;;AACD+H,EAAAA,WAAW,GAAG;AACV,SAAKmC,iBAAL,EAAwB1I,MAAxB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACI2I,EAAAA,mBAAmB,GAAG;AAClB,QAAI,CAAC,KAAKD,iBAAV,EAA6B;AACzB,WAAKE,gBAAL;AACH;;AACD,WAAO,KAAKF,iBAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACIE,EAAAA,gBAAgB,GAAG;AACf,UAAMC,cAAc,GAAG,uBAAvB,CADe,CAEf;AACA;AACA;;AACA,QAAI,KAAKlB,SAAL,CAAemB,SAAf,IAA4BxL,kBAAkB,EAAlD,EAAsD;AAClD,YAAMyL,0BAA0B,GAAG,KAAKlK,SAAL,CAAemK,gBAAf,CAAiC,IAAGH,cAAe,uBAAnB,GAA6C,IAAGA,cAAe,mBAA/F,CAAnC,CADkD,CAElD;AACA;;;AACA,WAAK,IAAI5B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8B,0BAA0B,CAACnC,MAA/C,EAAuDK,CAAC,EAAxD,EAA4D;AACxD8B,QAAAA,0BAA0B,CAAC9B,CAAD,CAA1B,CAA8BjH,MAA9B;AACH;AACJ;;AACD,UAAMiJ,SAAS,GAAG,KAAKpK,SAAL,CAAeqK,aAAf,CAA6B,KAA7B,CAAlB;;AACAD,IAAAA,SAAS,CAAC3J,SAAV,CAAoBC,GAApB,CAAwBsJ,cAAxB,EAde,CAef;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,QAAIvL,kBAAkB,EAAtB,EAA0B;AACtB2L,MAAAA,SAAS,CAACE,YAAV,CAAuB,UAAvB,EAAmC,MAAnC;AACH,KAFD,MAGK,IAAI,CAAC,KAAKxB,SAAL,CAAemB,SAApB,EAA+B;AAChCG,MAAAA,SAAS,CAACE,YAAV,CAAuB,UAAvB,EAAmC,QAAnC;AACH;;AACD,SAAKtK,SAAL,CAAea,IAAf,CAAoB0J,WAApB,CAAgCH,SAAhC;;AACA,SAAKP,iBAAL,GAAyBO,SAAzB;AACH;;AAxDkB;;AA0DvBR,gBAAgB,CAACvE,IAAjB;AAAA,mBAA6GuE,gBAA7G,EA1dwGrM,EA0dxG,UAA+ID,QAA/I,GA1dwGC,EA0dxG,UAAoKe,IAAI,CAACqL,QAAzK;AAAA;;AACAC,gBAAgB,CAACrE,KAAjB,kBA3dwGhI,EA2dxG;AAAA,SAAiHqM,gBAAjH;AAAA,WAAiHA,gBAAjH;AAAA,cAA+I;AAA/I;;AACA;AAAA,qDA5dwGrM,EA4dxG,mBAA2FqM,gBAA3F,EAAyH,CAAC;AAC9GpE,IAAAA,IAAI,EAAEhI,UADwG;AAE9GiI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFwG,GAAD,CAAzH,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DJ,QAAAA,IAAI,EAAE/H,MADwD;AAE9DgI,QAAAA,IAAI,EAAE,CAACnI,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEkI,MAAAA,IAAI,EAAElH,IAAI,CAACqL;AAAb,KAH2B,CAAP;AAGO,GANjD;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMa,UAAN,CAAiB;AACb/K,EAAAA,WAAW,CAACgL,aAAD,EAAgBC,KAAhB,EAAuBC,KAAvB,EAA8BzI,OAA9B,EAAuCD,OAAvC,EAAgD2I,mBAAhD,EAAqE5K,SAArE,EAAgF6K,SAAhF,EAA2FC,uBAA3F,EAAoH;AAC3H,SAAKL,aAAL,GAAqBA,aAArB;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKC,KAAL,GAAaA,KAAb;AACA,SAAKzI,OAAL,GAAeA,OAAf;AACA,SAAKD,OAAL,GAAeA,OAAf;AACA,SAAK2I,mBAAL,GAA2BA,mBAA3B;AACA,SAAK5K,SAAL,GAAiBA,SAAjB;AACA,SAAK6K,SAAL,GAAiBA,SAAjB;AACA,SAAKC,uBAAL,GAA+BA,uBAA/B;AACA,SAAKC,gBAAL,GAAwB,IAAxB;AACA,SAAKC,cAAL,GAAsB,IAAIjM,OAAJ,EAAtB;AACA,SAAKkM,YAAL,GAAoB,IAAIlM,OAAJ,EAApB;AACA,SAAKmM,YAAL,GAAoB,IAAInM,OAAJ,EAApB;AACA,SAAKoM,gBAAL,GAAwBnM,YAAY,CAACoM,KAArC;;AACA,SAAKC,qBAAL,GAA8BnD,KAAD,IAAW,KAAK8C,cAAL,CAAoBxC,IAApB,CAAyBN,KAAzB,CAAxC;;AACA,SAAKoD,6BAAL,GAAsCpD,KAAD,IAAW;AAC5C,WAAKqD,gBAAL,CAAsBrD,KAAK,CAACiB,MAA5B;AACH,KAFD;AAGA;;;AACA,SAAKd,cAAL,GAAsB,IAAItJ,OAAJ,EAAtB;AACA;;AACA,SAAKsK,qBAAL,GAA6B,IAAItK,OAAJ,EAA7B;;AACA,QAAImD,OAAO,CAAC4D,cAAZ,EAA4B;AACxB,WAAK0F,eAAL,GAAuBtJ,OAAO,CAAC4D,cAA/B;;AACA,WAAK0F,eAAL,CAAqBvL,MAArB,CAA4B,IAA5B;AACH;;AACD,SAAKwL,iBAAL,GAAyBvJ,OAAO,CAACwJ,gBAAjC;AACH;AACD;;;AACkB,MAAd9G,cAAc,GAAG;AACjB,WAAO,KAAK+F,KAAZ;AACH;AACD;;;AACmB,MAAfgB,eAAe,GAAG;AAClB,WAAO,KAAKZ,gBAAZ;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACmB,MAAXa,WAAW,GAAG;AACd,WAAO,KAAKlB,KAAZ;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIzK,EAAAA,MAAM,CAAC4L,MAAD,EAAS;AACX;AACA;AACA,QAAI,CAAC,KAAKnB,KAAL,CAAWoB,aAAZ,IAA6B,KAAKC,mBAAtC,EAA2D;AACvD,WAAKA,mBAAL,CAAyBxB,WAAzB,CAAqC,KAAKG,KAA1C;AACH;;AACD,UAAMsB,YAAY,GAAG,KAAKvB,aAAL,CAAmBxK,MAAnB,CAA0B4L,MAA1B,CAArB;;AACA,QAAI,KAAKJ,iBAAT,EAA4B;AACxB,WAAKA,iBAAL,CAAuBxL,MAAvB,CAA8B,IAA9B;AACH;;AACD,SAAKgM,oBAAL;;AACA,SAAKC,kBAAL;;AACA,SAAKC,uBAAL;;AACA,QAAI,KAAKX,eAAT,EAA0B;AACtB,WAAKA,eAAL,CAAqBtL,MAArB;AACH,KAfU,CAgBX;AACA;AACA;;;AACA,SAAK+B,OAAL,CAAamK,QAAb,CAAsBC,IAAtB,CAA2BnN,IAAI,CAAC,CAAD,CAA/B,EAAoC6D,SAApC,CAA8C,MAAM;AAChD;AACA,UAAI,KAAKT,WAAL,EAAJ,EAAwB;AACpB,aAAKa,cAAL;AACH;AACJ,KALD,EAnBW,CAyBX;;;AACA,SAAKmJ,oBAAL,CAA0B,IAA1B;;AACA,QAAI,KAAKpK,OAAL,CAAa8D,WAAjB,EAA8B;AAC1B,WAAKuG,eAAL;AACH;;AACD,QAAI,KAAKrK,OAAL,CAAa6D,UAAjB,EAA6B;AACzB,WAAKyG,cAAL,CAAoB,KAAK7B,KAAzB,EAAgC,KAAKzI,OAAL,CAAa6D,UAA7C,EAAyD,IAAzD;AACH,KAhCU,CAiCX;;;AACA,SAAKkF,YAAL,CAAkBzC,IAAlB,GAlCW,CAmCX;;;AACA,SAAKoC,mBAAL,CAAyBlK,GAAzB,CAA6B,IAA7B;;AACA,QAAI,KAAKwB,OAAL,CAAagE,mBAAjB,EAAsC;AAClC,WAAKiF,gBAAL,GAAwB,KAAKN,SAAL,CAAe9H,SAAf,CAAyB,MAAM,KAAK0J,OAAL,EAA/B,CAAxB;AACH;;AACD,SAAK3B,uBAAL,CAA6BpK,GAA7B,CAAiC,IAAjC;;AACA,WAAOsL,YAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIxJ,EAAAA,MAAM,GAAG;AACL,QAAI,CAAC,KAAKF,WAAL,EAAL,EAAyB;AACrB;AACH;;AACD,SAAKoK,cAAL,GAJK,CAKL;AACA;AACA;;AACA,SAAKJ,oBAAL,CAA0B,KAA1B;;AACA,QAAI,KAAKb,iBAAL,IAA0B,KAAKA,iBAAL,CAAuBjJ,MAArD,EAA6D;AACzD,WAAKiJ,iBAAL,CAAuBjJ,MAAvB;AACH;;AACD,QAAI,KAAKgJ,eAAT,EAA0B;AACtB,WAAKA,eAAL,CAAqB7K,OAArB;AACH;;AACD,UAAMgM,gBAAgB,GAAG,KAAKlC,aAAL,CAAmBjI,MAAnB,EAAzB,CAfK,CAgBL;;;AACA,SAAK0I,YAAL,CAAkB1C,IAAlB,GAjBK,CAkBL;;;AACA,SAAKoC,mBAAL,CAAyBzJ,MAAzB,CAAgC,IAAhC,EAnBK,CAoBL;AACA;;;AACA,SAAKyL,wBAAL;;AACA,SAAKzB,gBAAL,CAAsB/H,WAAtB;;AACA,SAAK0H,uBAAL,CAA6B3J,MAA7B,CAAoC,IAApC;;AACA,WAAOwL,gBAAP;AACH;AACD;;;AACAF,EAAAA,OAAO,GAAG;AACN,UAAMI,UAAU,GAAG,KAAKvK,WAAL,EAAnB;;AACA,QAAI,KAAKmJ,iBAAT,EAA4B;AACxB,WAAKA,iBAAL,CAAuBgB,OAAvB;AACH;;AACD,SAAKK,sBAAL;;AACA,SAAKvB,gBAAL,CAAsB,KAAKR,gBAA3B;;AACA,SAAKI,gBAAL,CAAsB/H,WAAtB;;AACA,SAAKwH,mBAAL,CAAyBzJ,MAAzB,CAAgC,IAAhC;;AACA,SAAKsJ,aAAL,CAAmBgC,OAAnB;;AACA,SAAKxB,YAAL,CAAkB8B,QAAlB;;AACA,SAAK/B,cAAL,CAAoB+B,QAApB;;AACA,SAAK1E,cAAL,CAAoB0E,QAApB;;AACA,SAAK1D,qBAAL,CAA2B0D,QAA3B;;AACA,SAAKjC,uBAAL,CAA6B3J,MAA7B,CAAoC,IAApC;;AACA,SAAKuJ,KAAL,EAAYvJ,MAAZ;AACA,SAAK4K,mBAAL,GAA2B,KAAKpB,KAAL,GAAa,KAAKD,KAAL,GAAa,IAArD;;AACA,QAAImC,UAAJ,EAAgB;AACZ,WAAK3B,YAAL,CAAkB1C,IAAlB;AACH;;AACD,SAAK0C,YAAL,CAAkB6B,QAAlB;AACH;AACD;;;AACAzK,EAAAA,WAAW,GAAG;AACV,WAAO,KAAKmI,aAAL,CAAmBnI,WAAnB,EAAP;AACH;AACD;;;AACA0K,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAKhC,cAAZ;AACH;AACD;;;AACAiC,EAAAA,WAAW,GAAG;AACV,WAAO,KAAKhC,YAAZ;AACH;AACD;;;AACAiC,EAAAA,WAAW,GAAG;AACV,WAAO,KAAKhC,YAAZ;AACH;AACD;;;AACA3C,EAAAA,aAAa,GAAG;AACZ,WAAO,KAAKF,cAAZ;AACH;AACD;;;AACAiB,EAAAA,oBAAoB,GAAG;AACnB,WAAO,KAAKD,qBAAZ;AACH;AACD;;;AACA8D,EAAAA,SAAS,GAAG;AACR,WAAO,KAAKjL,OAAZ;AACH;AACD;;;AACAiB,EAAAA,cAAc,GAAG;AACb,QAAI,KAAKsI,iBAAT,EAA4B;AACxB,WAAKA,iBAAL,CAAuB2B,KAAvB;AACH;AACJ;AACD;;;AACAC,EAAAA,sBAAsB,CAACC,QAAD,EAAW;AAC7B,QAAIA,QAAQ,KAAK,KAAK7B,iBAAtB,EAAyC;AACrC;AACH;;AACD,QAAI,KAAKA,iBAAT,EAA4B;AACxB,WAAKA,iBAAL,CAAuBgB,OAAvB;AACH;;AACD,SAAKhB,iBAAL,GAAyB6B,QAAzB;;AACA,QAAI,KAAKhL,WAAL,EAAJ,EAAwB;AACpBgL,MAAAA,QAAQ,CAACrN,MAAT,CAAgB,IAAhB;AACA,WAAKkD,cAAL;AACH;AACJ;AACD;;;AACAoK,EAAAA,UAAU,CAACC,UAAD,EAAa;AACnB,SAAKtL,OAAL,GAAe,EAAE,GAAG,KAAKA,OAAV;AAAmB,SAAGsL;AAAtB,KAAf;;AACA,SAAKtB,kBAAL;AACH;AACD;;;AACAuB,EAAAA,YAAY,CAACC,GAAD,EAAM;AACd,SAAKxL,OAAL,GAAe,EAAE,GAAG,KAAKA,OAAV;AAAmByL,MAAAA,SAAS,EAAED;AAA9B,KAAf;;AACA,SAAKvB,uBAAL;AACH;AACD;;;AACAyB,EAAAA,aAAa,CAACC,OAAD,EAAU;AACnB,QAAI,KAAKlD,KAAT,EAAgB;AACZ,WAAK6B,cAAL,CAAoB,KAAK7B,KAAzB,EAAgCkD,OAAhC,EAAyC,IAAzC;AACH;AACJ;AACD;;;AACAC,EAAAA,gBAAgB,CAACD,OAAD,EAAU;AACtB,QAAI,KAAKlD,KAAT,EAAgB;AACZ,WAAK6B,cAAL,CAAoB,KAAK7B,KAAzB,EAAgCkD,OAAhC,EAAyC,KAAzC;AACH;AACJ;AACD;AACJ;AACA;;;AACIE,EAAAA,YAAY,GAAG;AACX,UAAMJ,SAAS,GAAG,KAAKzL,OAAL,CAAayL,SAA/B;;AACA,QAAI,CAACA,SAAL,EAAgB;AACZ,aAAO,KAAP;AACH;;AACD,WAAO,OAAOA,SAAP,KAAqB,QAArB,GAAgCA,SAAhC,GAA4CA,SAAS,CAACrG,KAA7D;AACH;AACD;;;AACA0G,EAAAA,oBAAoB,CAACV,QAAD,EAAW;AAC3B,QAAIA,QAAQ,KAAK,KAAK9B,eAAtB,EAAuC;AACnC;AACH;;AACD,SAAKsB,sBAAL;;AACA,SAAKtB,eAAL,GAAuB8B,QAAvB;;AACA,QAAI,KAAKhL,WAAL,EAAJ,EAAwB;AACpBgL,MAAAA,QAAQ,CAACrN,MAAT,CAAgB,IAAhB;AACAqN,MAAAA,QAAQ,CAACpN,MAAT;AACH;AACJ;AACD;;;AACAiM,EAAAA,uBAAuB,GAAG;AACtB,SAAKzB,KAAL,CAAWJ,YAAX,CAAwB,KAAxB,EAA+B,KAAKyD,YAAL,EAA/B;AACH;AACD;;;AACA7B,EAAAA,kBAAkB,GAAG;AACjB,QAAI,CAAC,KAAKvB,KAAV,EAAiB;AACb;AACH;;AACD,UAAMnK,KAAK,GAAG,KAAKmK,KAAL,CAAWnK,KAAzB;AACAA,IAAAA,KAAK,CAACoB,KAAN,GAAczD,mBAAmB,CAAC,KAAK+D,OAAL,CAAaN,KAAd,CAAjC;AACApB,IAAAA,KAAK,CAACkB,MAAN,GAAevD,mBAAmB,CAAC,KAAK+D,OAAL,CAAaR,MAAd,CAAlC;AACAlB,IAAAA,KAAK,CAACyN,QAAN,GAAiB9P,mBAAmB,CAAC,KAAK+D,OAAL,CAAa+L,QAAd,CAApC;AACAzN,IAAAA,KAAK,CAAC0N,SAAN,GAAkB/P,mBAAmB,CAAC,KAAK+D,OAAL,CAAagM,SAAd,CAArC;AACA1N,IAAAA,KAAK,CAAC2N,QAAN,GAAiBhQ,mBAAmB,CAAC,KAAK+D,OAAL,CAAaiM,QAAd,CAApC;AACA3N,IAAAA,KAAK,CAAC4N,SAAN,GAAkBjQ,mBAAmB,CAAC,KAAK+D,OAAL,CAAakM,SAAd,CAArC;AACH;AACD;;;AACA9B,EAAAA,oBAAoB,CAAC+B,aAAD,EAAgB;AAChC,SAAK1D,KAAL,CAAWnK,KAAX,CAAiB8N,aAAjB,GAAiCD,aAAa,GAAG,EAAH,GAAQ,MAAtD;AACH;AACD;;;AACA9B,EAAAA,eAAe,GAAG;AACd,UAAMgC,YAAY,GAAG,8BAArB;AACA,SAAKxD,gBAAL,GAAwB,KAAK/K,SAAL,CAAeqK,aAAf,CAA6B,KAA7B,CAAxB;;AACA,SAAKU,gBAAL,CAAsBtK,SAAtB,CAAgCC,GAAhC,CAAoC,sBAApC;;AACA,QAAI,KAAKwB,OAAL,CAAa+D,aAAjB,EAAgC;AAC5B,WAAKuG,cAAL,CAAoB,KAAKzB,gBAAzB,EAA2C,KAAK7I,OAAL,CAAa+D,aAAxD,EAAuE,IAAvE;AACH,KANa,CAOd;AACA;;;AACA,SAAKyE,KAAL,CAAWoB,aAAX,CAAyB0C,YAAzB,CAAsC,KAAKzD,gBAA3C,EAA6D,KAAKL,KAAlE,EATc,CAUd;AACA;;;AACA,SAAKK,gBAAL,CAAsBpC,gBAAtB,CAAuC,OAAvC,EAAgD,KAAK0C,qBAArD,EAZc,CAad;;;AACA,QAAI,OAAOoD,qBAAP,KAAiC,WAArC,EAAkD;AAC9C,WAAKxM,OAAL,CAAayG,iBAAb,CAA+B,MAAM;AACjC+F,QAAAA,qBAAqB,CAAC,MAAM;AACxB,cAAI,KAAK1D,gBAAT,EAA2B;AACvB,iBAAKA,gBAAL,CAAsBtK,SAAtB,CAAgCC,GAAhC,CAAoC6N,YAApC;AACH;AACJ,SAJoB,CAArB;AAKH,OAND;AAOH,KARD,MASK;AACD,WAAKxD,gBAAL,CAAsBtK,SAAtB,CAAgCC,GAAhC,CAAoC6N,YAApC;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACItC,EAAAA,oBAAoB,GAAG;AACnB,QAAI,KAAKvB,KAAL,CAAWgE,WAAf,EAA4B;AACxB,WAAKhE,KAAL,CAAWiE,UAAX,CAAsBpE,WAAtB,CAAkC,KAAKG,KAAvC;AACH;AACJ;AACD;;;AACAgC,EAAAA,cAAc,GAAG;AACb,UAAMkC,gBAAgB,GAAG,KAAK7D,gBAA9B;;AACA,QAAI,CAAC6D,gBAAL,EAAuB;AACnB;AACH;;AACDA,IAAAA,gBAAgB,CAACnO,SAAjB,CAA2BU,MAA3B,CAAkC,8BAAlC;;AACA,SAAKc,OAAL,CAAayG,iBAAb,CAA+B,MAAM;AACjCkG,MAAAA,gBAAgB,CAACjG,gBAAjB,CAAkC,eAAlC,EAAmD,KAAK2C,6BAAxD;AACH,KAFD,EANa,CASb;AACA;;;AACAsD,IAAAA,gBAAgB,CAACpO,KAAjB,CAAuB8N,aAAvB,GAAuC,MAAvC,CAXa,CAYb;AACA;AACA;;AACA,SAAKO,gBAAL,GAAwB,KAAK5M,OAAL,CAAayG,iBAAb,CAA+B,MAAMoG,UAAU,CAAC,MAAM;AAC1E,WAAKvD,gBAAL,CAAsBqD,gBAAtB;AACH,KAFsE,EAEpE,GAFoE,CAA/C,CAAxB;AAGH;AACD;;;AACApC,EAAAA,cAAc,CAACjJ,OAAD,EAAUwL,UAAV,EAAsBC,KAAtB,EAA6B;AACvC,UAAMnB,OAAO,GAAGzP,WAAW,CAAC2Q,UAAU,IAAI,EAAf,CAAX,CAA8BE,MAA9B,CAAqCC,CAAC,IAAI,CAAC,CAACA,CAA5C,CAAhB;;AACA,QAAIrB,OAAO,CAAC9F,MAAZ,EAAoB;AAChBiH,MAAAA,KAAK,GAAGzL,OAAO,CAAC9C,SAAR,CAAkBC,GAAlB,CAAsB,GAAGmN,OAAzB,CAAH,GAAuCtK,OAAO,CAAC9C,SAAR,CAAkBU,MAAlB,CAAyB,GAAG0M,OAA5B,CAA5C;AACH;AACJ;AACD;;;AACAjB,EAAAA,wBAAwB,GAAG;AACvB;AACA;AACA;AACA,SAAK3K,OAAL,CAAayG,iBAAb,CAA+B,MAAM;AACjC;AACA;AACA;AACA,YAAMyG,YAAY,GAAG,KAAKlN,OAAL,CAAamK,QAAb,CAChBC,IADgB,CACXlN,SAAS,CAACF,KAAK,CAAC,KAAKgM,YAAN,EAAoB,KAAKC,YAAzB,CAAN,CADE,EAEhBnI,SAFgB,CAEN,MAAM;AACjB;AACA;AACA,YAAI,CAAC,KAAK4H,KAAN,IAAe,CAAC,KAAKD,KAArB,IAA8B,KAAKC,KAAL,CAAWyE,QAAX,CAAoBrH,MAApB,KAA+B,CAAjE,EAAoE;AAChE,cAAI,KAAK4C,KAAL,IAAc,KAAKzI,OAAL,CAAa6D,UAA/B,EAA2C;AACvC,iBAAKyG,cAAL,CAAoB,KAAK7B,KAAzB,EAAgC,KAAKzI,OAAL,CAAa6D,UAA7C,EAAyD,KAAzD;AACH;;AACD,cAAI,KAAK2E,KAAL,IAAc,KAAKA,KAAL,CAAWoB,aAA7B,EAA4C;AACxC,iBAAKC,mBAAL,GAA2B,KAAKrB,KAAL,CAAWoB,aAAtC;;AACA,iBAAKpB,KAAL,CAAWvJ,MAAX;AACH;;AACDgO,UAAAA,YAAY,CAAC/L,WAAb;AACH;AACJ,OAfoB,CAArB;AAgBH,KApBD;AAqBH;AACD;;;AACA0J,EAAAA,sBAAsB,GAAG;AACrB,UAAMhH,cAAc,GAAG,KAAK0F,eAA5B;;AACA,QAAI1F,cAAJ,EAAoB;AAChBA,MAAAA,cAAc,CAACnF,OAAf;;AACA,UAAImF,cAAc,CAACtD,MAAnB,EAA2B;AACvBsD,QAAAA,cAAc,CAACtD,MAAf;AACH;AACJ;AACJ;AACD;;;AACA+I,EAAAA,gBAAgB,CAAC8D,QAAD,EAAW;AACvB,QAAIA,QAAJ,EAAc;AACVA,MAAAA,QAAQ,CAACzG,mBAAT,CAA6B,OAA7B,EAAsC,KAAKyC,qBAA3C;AACAgE,MAAAA,QAAQ,CAACzG,mBAAT,CAA6B,eAA7B,EAA8C,KAAK0C,6BAAnD;AACA+D,MAAAA,QAAQ,CAAClO,MAAT,GAHU,CAIV;AACA;AACA;;AACA,UAAI,KAAK4J,gBAAL,KAA0BsE,QAA9B,EAAwC;AACpC,aAAKtE,gBAAL,GAAwB,IAAxB;AACH;AACJ;;AACD,QAAI,KAAK8D,gBAAT,EAA2B;AACvBS,MAAAA,YAAY,CAAC,KAAKT,gBAAN,CAAZ;AACA,WAAKA,gBAAL,GAAwBlJ,SAAxB;AACH;AACJ;;AAhYY;AAmYjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM4J,gBAAgB,GAAG,6CAAzB;AACA;;AACA,MAAMC,cAAc,GAAG,eAAvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,iCAAN,CAAwC;AACpChQ,EAAAA,WAAW,CAACiQ,WAAD,EAAchQ,cAAd,EAA8BM,SAA9B,EAAyC8I,SAAzC,EAAoD6G,iBAApD,EAAuE;AAC9E,SAAKjQ,cAAL,GAAsBA,cAAtB;AACA,SAAKM,SAAL,GAAiBA,SAAjB;AACA,SAAK8I,SAAL,GAAiBA,SAAjB;AACA,SAAK6G,iBAAL,GAAyBA,iBAAzB;AACA;;AACA,SAAKC,oBAAL,GAA4B;AAAEhO,MAAAA,KAAK,EAAE,CAAT;AAAYF,MAAAA,MAAM,EAAE;AAApB,KAA5B;AACA;;AACA,SAAKmO,SAAL,GAAiB,KAAjB;AACA;;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA;;AACA,SAAKC,cAAL,GAAsB,KAAtB;AACA;;AACA,SAAKC,sBAAL,GAA8B,IAA9B;AACA;;AACA,SAAKC,eAAL,GAAuB,KAAvB;AACA;;AACA,SAAKC,eAAL,GAAuB,CAAvB;AACA;;AACA,SAAKC,YAAL,GAAoB,EAApB;AACA;;AACA,SAAKC,mBAAL,GAA2B,EAA3B;AACA;;AACA,SAAKC,gBAAL,GAAwB,IAAItR,OAAJ,EAAxB;AACA;;AACA,SAAKuR,mBAAL,GAA2BtR,YAAY,CAACoM,KAAxC;AACA;;AACA,SAAKmF,QAAL,GAAgB,CAAhB;AACA;;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACA;;AACA,SAAKC,oBAAL,GAA4B,EAA5B;AACA;;AACA,SAAKC,eAAL,GAAuB,KAAKL,gBAA5B;AACA,SAAKM,SAAL,CAAejB,WAAf;AACH;AACD;;;AACa,MAATkB,SAAS,GAAG;AACZ,WAAO,KAAKR,mBAAZ;AACH;AACD;;;AACAnQ,EAAAA,MAAM,CAACwC,UAAD,EAAa;AACf,QAAI,KAAKJ,WAAL,IACAI,UAAU,KAAK,KAAKJ,WADpB,KAEC,OAAOK,SAAP,KAAqB,WAArB,IAAoCA,SAFrC,CAAJ,EAEqD;AACjD,YAAMZ,KAAK,CAAC,0DAAD,CAAX;AACH;;AACD,SAAK+O,kBAAL;;AACApO,IAAAA,UAAU,CAACmJ,WAAX,CAAuBnL,SAAvB,CAAiCC,GAAjC,CAAqC6O,gBAArC;AACA,SAAKlN,WAAL,GAAmBI,UAAnB;AACA,SAAKqO,YAAL,GAAoBrO,UAAU,CAACmJ,WAA/B;AACA,SAAKjB,KAAL,GAAalI,UAAU,CAACmC,cAAxB;AACA,SAAKmM,WAAL,GAAmB,KAAnB;AACA,SAAKC,gBAAL,GAAwB,IAAxB;AACA,SAAKC,aAAL,GAAqB,IAArB;;AACA,SAAKX,mBAAL,CAAyBlN,WAAzB;;AACA,SAAKkN,mBAAL,GAA2B,KAAK5Q,cAAL,CAAoBwR,MAApB,GAA6BnO,SAA7B,CAAuC,MAAM;AACpE;AACA;AACA;AACA,WAAKiO,gBAAL,GAAwB,IAAxB;AACA,WAAK5D,KAAL;AACH,KAN0B,CAA3B;AAOH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIA,EAAAA,KAAK,GAAG;AACJ;AACA,QAAI,KAAK2D,WAAL,IAAoB,CAAC,KAAKjI,SAAL,CAAemB,SAAxC,EAAmD;AAC/C;AACH,KAJG,CAKJ;AACA;AACA;;;AACA,QAAI,CAAC,KAAK+G,gBAAN,IAA0B,KAAKf,eAA/B,IAAkD,KAAKgB,aAA3D,EAA0E;AACtE,WAAKE,mBAAL;AACA;AACH;;AACD,SAAKC,kBAAL;;AACA,SAAKC,0BAAL;;AACA,SAAKC,uBAAL,GAdI,CAeJ;AACA;AACA;;;AACA,SAAKC,aAAL,GAAqB,KAAKC,wBAAL,EAArB;AACA,SAAKC,WAAL,GAAmB,KAAKC,cAAL,EAAnB;AACA,SAAKC,YAAL,GAAoB,KAAKhH,KAAL,CAAW9F,qBAAX,EAApB;AACA,SAAK+M,cAAL,GAAsB,KAAKjC,iBAAL,CAAuB7F,mBAAvB,GAA6CjF,qBAA7C,EAAtB;AACA,UAAMgN,UAAU,GAAG,KAAKJ,WAAxB;AACA,UAAM9M,WAAW,GAAG,KAAKgN,YAAzB;AACA,UAAMG,YAAY,GAAG,KAAKP,aAA1B;AACA,UAAMQ,aAAa,GAAG,KAAKH,cAA3B,CAzBI,CA0BJ;;AACA,UAAMI,YAAY,GAAG,EAArB,CA3BI,CA4BJ;;AACA,QAAIC,QAAJ,CA7BI,CA8BJ;AACA;;AACA,SAAK,IAAIC,GAAT,IAAgB,KAAK9B,mBAArB,EAA0C;AACtC;AACA,UAAI+B,WAAW,GAAG,KAAKC,eAAL,CAAqBP,UAArB,EAAiCE,aAAjC,EAAgDG,GAAhD,CAAlB,CAFsC,CAGtC;AACA;AACA;;;AACA,UAAIG,YAAY,GAAG,KAAKC,gBAAL,CAAsBH,WAAtB,EAAmCxN,WAAnC,EAAgDuN,GAAhD,CAAnB,CANsC,CAOtC;;;AACA,UAAIK,UAAU,GAAG,KAAKC,cAAL,CAAoBH,YAApB,EAAkC1N,WAAlC,EAA+CmN,YAA/C,EAA6DI,GAA7D,CAAjB,CARsC,CAStC;;;AACA,UAAIK,UAAU,CAACE,0BAAf,EAA2C;AACvC,aAAK5C,SAAL,GAAiB,KAAjB;;AACA,aAAK6C,cAAL,CAAoBR,GAApB,EAAyBC,WAAzB;;AACA;AACH,OAdqC,CAetC;AACA;;;AACA,UAAI,KAAKQ,6BAAL,CAAmCJ,UAAnC,EAA+CF,YAA/C,EAA6DP,YAA7D,CAAJ,EAAgF;AAC5E;AACA;AACAE,QAAAA,YAAY,CAACrK,IAAb,CAAkB;AACdiL,UAAAA,QAAQ,EAAEV,GADI;AAEd1L,UAAAA,MAAM,EAAE2L,WAFM;AAGdxN,UAAAA,WAHc;AAIdkO,UAAAA,eAAe,EAAE,KAAKC,yBAAL,CAA+BX,WAA/B,EAA4CD,GAA5C;AAJH,SAAlB;AAMA;AACH,OA3BqC,CA4BtC;AACA;AACA;;;AACA,UAAI,CAACD,QAAD,IAAaA,QAAQ,CAACM,UAAT,CAAoBQ,WAApB,GAAkCR,UAAU,CAACQ,WAA9D,EAA2E;AACvEd,QAAAA,QAAQ,GAAG;AAAEM,UAAAA,UAAF;AAAcF,UAAAA,YAAd;AAA4BF,UAAAA,WAA5B;AAAyCS,UAAAA,QAAQ,EAAEV,GAAnD;AAAwDvN,UAAAA;AAAxD,SAAX;AACH;AACJ,KAlEG,CAmEJ;AACA;;;AACA,QAAIqN,YAAY,CAACjK,MAAjB,EAAyB;AACrB,UAAIiL,OAAO,GAAG,IAAd;AACA,UAAIC,SAAS,GAAG,CAAC,CAAjB;;AACA,WAAK,MAAMC,GAAX,IAAkBlB,YAAlB,EAAgC;AAC5B,cAAMmB,KAAK,GAAGD,GAAG,CAACL,eAAJ,CAAoBjR,KAApB,GAA4BsR,GAAG,CAACL,eAAJ,CAAoBnR,MAAhD,IAA0DwR,GAAG,CAACN,QAAJ,CAAaQ,MAAb,IAAuB,CAAjF,CAAd;;AACA,YAAID,KAAK,GAAGF,SAAZ,EAAuB;AACnBA,UAAAA,SAAS,GAAGE,KAAZ;AACAH,UAAAA,OAAO,GAAGE,GAAV;AACH;AACJ;;AACD,WAAKrD,SAAL,GAAiB,KAAjB;;AACA,WAAK6C,cAAL,CAAoBM,OAAO,CAACJ,QAA5B,EAAsCI,OAAO,CAACxM,MAA9C;;AACA;AACH,KAlFG,CAmFJ;AACA;;;AACA,QAAI,KAAKsJ,QAAT,EAAmB;AACf;AACA,WAAKD,SAAL,GAAiB,IAAjB;;AACA,WAAK6C,cAAL,CAAoBT,QAAQ,CAACW,QAA7B,EAAuCX,QAAQ,CAACE,WAAhD;;AACA;AACH,KA1FG,CA2FJ;AACA;;;AACA,SAAKO,cAAL,CAAoBT,QAAQ,CAACW,QAA7B,EAAuCX,QAAQ,CAACE,WAAhD;AACH;;AACD3P,EAAAA,MAAM,GAAG;AACL,SAAK4O,kBAAL;;AACA,SAAKH,aAAL,GAAqB,IAArB;AACA,SAAKoC,mBAAL,GAA2B,IAA3B;;AACA,SAAK/C,mBAAL,CAAyBlN,WAAzB;AACH;AACD;;;AACAqJ,EAAAA,OAAO,GAAG;AACN,QAAI,KAAKsE,WAAT,EAAsB;AAClB;AACH,KAHK,CAIN;AACA;;;AACA,QAAI,KAAKD,YAAT,EAAuB;AACnBwC,MAAAA,YAAY,CAAC,KAAKxC,YAAL,CAAkBtQ,KAAnB,EAA0B;AAClCX,QAAAA,GAAG,EAAE,EAD6B;AAElCC,QAAAA,IAAI,EAAE,EAF4B;AAGlCiE,QAAAA,KAAK,EAAE,EAH2B;AAIlCH,QAAAA,MAAM,EAAE,EAJ0B;AAKlClC,QAAAA,MAAM,EAAE,EAL0B;AAMlCE,QAAAA,KAAK,EAAE,EAN2B;AAOlC2R,QAAAA,UAAU,EAAE,EAPsB;AAQlCC,QAAAA,cAAc,EAAE;AARkB,OAA1B,CAAZ;AAUH;;AACD,QAAI,KAAK7I,KAAT,EAAgB;AACZ,WAAK0G,0BAAL;AACH;;AACD,QAAI,KAAKhP,WAAT,EAAsB;AAClB,WAAKA,WAAL,CAAiBuJ,WAAjB,CAA6BnL,SAA7B,CAAuCU,MAAvC,CAA8CoO,gBAA9C;AACH;;AACD,SAAK/M,MAAL;;AACA,SAAK6N,gBAAL,CAAsBtD,QAAtB;;AACA,SAAK1K,WAAL,GAAmB,KAAKyO,YAAL,GAAoB,IAAvC;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACII,EAAAA,mBAAmB,GAAG;AAClB,QAAI,KAAKJ,WAAL,IAAoB,CAAC,KAAKjI,SAAL,CAAemB,SAAxC,EAAmD;AAC/C;AACH;;AACD,UAAMwJ,YAAY,GAAG,KAAKxC,aAA1B;;AACA,QAAIwC,YAAJ,EAAkB;AACd,WAAKhC,WAAL,GAAmB,KAAKC,cAAL,EAAnB;AACA,WAAKC,YAAL,GAAoB,KAAKhH,KAAL,CAAW9F,qBAAX,EAApB;AACA,WAAK0M,aAAL,GAAqB,KAAKC,wBAAL,EAArB;AACA,WAAKI,cAAL,GAAsB,KAAKjC,iBAAL,CAAuB7F,mBAAvB,GAA6CjF,qBAA7C,EAAtB;;AACA,YAAMsN,WAAW,GAAG,KAAKC,eAAL,CAAqB,KAAKX,WAA1B,EAAuC,KAAKG,cAA5C,EAA4D6B,YAA5D,CAApB;;AACA,WAAKf,cAAL,CAAoBe,YAApB,EAAkCtB,WAAlC;AACH,KAPD,MAQK;AACD,WAAK/E,KAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;AACIsG,EAAAA,wBAAwB,CAACC,WAAD,EAAc;AAClC,SAAKxD,YAAL,GAAoBwD,WAApB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,aAAa,CAAChD,SAAD,EAAY;AACrB,SAAKR,mBAAL,GAA2BQ,SAA3B,CADqB,CAErB;AACA;;AACA,QAAIA,SAAS,CAAC/I,OAAV,CAAkB,KAAKoJ,aAAvB,MAA0C,CAAC,CAA/C,EAAkD;AAC9C,WAAKA,aAAL,GAAqB,IAArB;AACH;;AACD,SAAKJ,kBAAL;;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIgD,EAAAA,kBAAkB,CAACC,MAAD,EAAS;AACvB,SAAK5D,eAAL,GAAuB4D,MAAvB;AACA,WAAO,IAAP;AACH;AACD;;;AACAC,EAAAA,sBAAsB,CAACC,kBAAkB,GAAG,IAAtB,EAA4B;AAC9C,SAAKhE,sBAAL,GAA8BgE,kBAA9B;AACA,WAAO,IAAP;AACH;AACD;;;AACAC,EAAAA,iBAAiB,CAACC,aAAa,GAAG,IAAjB,EAAuB;AACpC,SAAKnE,cAAL,GAAsBmE,aAAtB;AACA,WAAO,IAAP;AACH;AACD;;;AACAC,EAAAA,QAAQ,CAACC,OAAO,GAAG,IAAX,EAAiB;AACrB,SAAKtE,QAAL,GAAgBsE,OAAhB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,kBAAkB,CAACC,QAAQ,GAAG,IAAZ,EAAkB;AAChC,SAAKrE,eAAL,GAAuBqE,QAAvB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI3D,EAAAA,SAAS,CAACnK,MAAD,EAAS;AACd,SAAK+N,OAAL,GAAe/N,MAAf;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIgO,EAAAA,kBAAkB,CAACC,MAAD,EAAS;AACvB,SAAKlE,QAAL,GAAgBkE,MAAhB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIC,EAAAA,kBAAkB,CAACD,MAAD,EAAS;AACvB,SAAKjE,QAAL,GAAgBiE,MAAhB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIE,EAAAA,qBAAqB,CAACC,QAAD,EAAW;AAC5B,SAAKC,wBAAL,GAAgCD,QAAhC;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;;;AACIxC,EAAAA,eAAe,CAACP,UAAD,EAAaE,aAAb,EAA4BG,GAA5B,EAAiC;AAC5C,QAAI4C,CAAJ;;AACA,QAAI5C,GAAG,CAACtL,OAAJ,IAAe,QAAnB,EAA6B;AACzB;AACA;AACAkO,MAAAA,CAAC,GAAGjD,UAAU,CAAC/R,IAAX,GAAkB+R,UAAU,CAACjQ,KAAX,GAAmB,CAAzC;AACH,KAJD,MAKK;AACD,YAAMmT,MAAM,GAAG,KAAKC,MAAL,KAAgBnD,UAAU,CAAC9N,KAA3B,GAAmC8N,UAAU,CAAC/R,IAA7D;AACA,YAAMmV,IAAI,GAAG,KAAKD,MAAL,KAAgBnD,UAAU,CAAC/R,IAA3B,GAAkC+R,UAAU,CAAC9N,KAA1D;AACA+Q,MAAAA,CAAC,GAAG5C,GAAG,CAACtL,OAAJ,IAAe,OAAf,GAAyBmO,MAAzB,GAAkCE,IAAtC;AACH,KAX2C,CAY5C;AACA;;;AACA,QAAIlD,aAAa,CAACjS,IAAd,GAAqB,CAAzB,EAA4B;AACxBgV,MAAAA,CAAC,IAAI/C,aAAa,CAACjS,IAAnB;AACH;;AACD,QAAIoV,CAAJ;;AACA,QAAIhD,GAAG,CAACrL,OAAJ,IAAe,QAAnB,EAA6B;AACzBqO,MAAAA,CAAC,GAAGrD,UAAU,CAAChS,GAAX,GAAiBgS,UAAU,CAACnQ,MAAX,GAAoB,CAAzC;AACH,KAFD,MAGK;AACDwT,MAAAA,CAAC,GAAGhD,GAAG,CAACrL,OAAJ,IAAe,KAAf,GAAuBgL,UAAU,CAAChS,GAAlC,GAAwCgS,UAAU,CAACjO,MAAvD;AACH,KAvB2C,CAwB5C;AACA;AACA;AACA;AACA;;;AACA,QAAImO,aAAa,CAAClS,GAAd,GAAoB,CAAxB,EAA2B;AACvBqV,MAAAA,CAAC,IAAInD,aAAa,CAAClS,GAAnB;AACH;;AACD,WAAO;AAAEiV,MAAAA,CAAF;AAAKI,MAAAA;AAAL,KAAP;AACH;AACD;AACJ;AACA;AACA;;;AACI5C,EAAAA,gBAAgB,CAACH,WAAD,EAAcxN,WAAd,EAA2BuN,GAA3B,EAAgC;AAC5C;AACA;AACA,QAAIiD,aAAJ;;AACA,QAAIjD,GAAG,CAACpL,QAAJ,IAAgB,QAApB,EAA8B;AAC1BqO,MAAAA,aAAa,GAAG,CAACxQ,WAAW,CAAC/C,KAAb,GAAqB,CAArC;AACH,KAFD,MAGK,IAAIsQ,GAAG,CAACpL,QAAJ,KAAiB,OAArB,EAA8B;AAC/BqO,MAAAA,aAAa,GAAG,KAAKH,MAAL,KAAgB,CAACrQ,WAAW,CAAC/C,KAA7B,GAAqC,CAArD;AACH,KAFI,MAGA;AACDuT,MAAAA,aAAa,GAAG,KAAKH,MAAL,KAAgB,CAAhB,GAAoB,CAACrQ,WAAW,CAAC/C,KAAjD;AACH;;AACD,QAAIwT,aAAJ;;AACA,QAAIlD,GAAG,CAACnL,QAAJ,IAAgB,QAApB,EAA8B;AAC1BqO,MAAAA,aAAa,GAAG,CAACzQ,WAAW,CAACjD,MAAb,GAAsB,CAAtC;AACH,KAFD,MAGK;AACD0T,MAAAA,aAAa,GAAGlD,GAAG,CAACnL,QAAJ,IAAgB,KAAhB,GAAwB,CAAxB,GAA4B,CAACpC,WAAW,CAACjD,MAAzD;AACH,KAnB2C,CAoB5C;;;AACA,WAAO;AACHoT,MAAAA,CAAC,EAAE3C,WAAW,CAAC2C,CAAZ,GAAgBK,aADhB;AAEHD,MAAAA,CAAC,EAAE/C,WAAW,CAAC+C,CAAZ,GAAgBE;AAFhB,KAAP;AAIH;AACD;;;AACA5C,EAAAA,cAAc,CAAC6C,KAAD,EAAQC,cAAR,EAAwB/T,QAAxB,EAAkCqR,QAAlC,EAA4C;AACtD;AACA;AACA,UAAMnM,OAAO,GAAG8O,4BAA4B,CAACD,cAAD,CAA5C;AACA,QAAI;AAAER,MAAAA,CAAF;AAAKI,MAAAA;AAAL,QAAWG,KAAf;;AACA,QAAI3O,OAAO,GAAG,KAAK8O,UAAL,CAAgB5C,QAAhB,EAA0B,GAA1B,CAAd;;AACA,QAAIjM,OAAO,GAAG,KAAK6O,UAAL,CAAgB5C,QAAhB,EAA0B,GAA1B,CAAd,CANsD,CAOtD;;;AACA,QAAIlM,OAAJ,EAAa;AACToO,MAAAA,CAAC,IAAIpO,OAAL;AACH;;AACD,QAAIC,OAAJ,EAAa;AACTuO,MAAAA,CAAC,IAAIvO,OAAL;AACH,KAbqD,CActD;;;AACA,QAAI8O,YAAY,GAAG,IAAIX,CAAvB;AACA,QAAIY,aAAa,GAAGZ,CAAC,GAAGrO,OAAO,CAAC7E,KAAZ,GAAoBL,QAAQ,CAACK,KAAjD;AACA,QAAI+T,WAAW,GAAG,IAAIT,CAAtB;AACA,QAAIU,cAAc,GAAGV,CAAC,GAAGzO,OAAO,CAAC/E,MAAZ,GAAqBH,QAAQ,CAACG,MAAnD,CAlBsD,CAmBtD;;AACA,QAAImU,YAAY,GAAG,KAAKC,kBAAL,CAAwBrP,OAAO,CAAC7E,KAAhC,EAAuC6T,YAAvC,EAAqDC,aAArD,CAAnB;;AACA,QAAIK,aAAa,GAAG,KAAKD,kBAAL,CAAwBrP,OAAO,CAAC/E,MAAhC,EAAwCiU,WAAxC,EAAqDC,cAArD,CAApB;;AACA,QAAI7C,WAAW,GAAG8C,YAAY,GAAGE,aAAjC;AACA,WAAO;AACHhD,MAAAA,WADG;AAEHN,MAAAA,0BAA0B,EAAEhM,OAAO,CAAC7E,KAAR,GAAgB6E,OAAO,CAAC/E,MAAxB,KAAmCqR,WAF5D;AAGHiD,MAAAA,wBAAwB,EAAED,aAAa,KAAKtP,OAAO,CAAC/E,MAHjD;AAIHuU,MAAAA,0BAA0B,EAAEJ,YAAY,IAAIpP,OAAO,CAAC7E;AAJjD,KAAP;AAMH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACI+Q,EAAAA,6BAA6B,CAACO,GAAD,EAAMmC,KAAN,EAAa9T,QAAb,EAAuB;AAChD,QAAI,KAAKyO,sBAAT,EAAiC;AAC7B,YAAMkG,eAAe,GAAG3U,QAAQ,CAACqC,MAAT,GAAkByR,KAAK,CAACH,CAAhD;AACA,YAAMiB,cAAc,GAAG5U,QAAQ,CAACwC,KAAT,GAAiBsR,KAAK,CAACP,CAA9C;AACA,YAAM5G,SAAS,GAAGkI,aAAa,CAAC,KAAK/T,WAAL,CAAiB8K,SAAjB,GAA6Be,SAA9B,CAA/B;AACA,YAAMD,QAAQ,GAAGmI,aAAa,CAAC,KAAK/T,WAAL,CAAiB8K,SAAjB,GAA6Bc,QAA9B,CAA9B;AACA,YAAMoI,WAAW,GAAGnD,GAAG,CAAC8C,wBAAJ,IAAiC9H,SAAS,IAAI,IAAb,IAAqBA,SAAS,IAAIgI,eAAvF;AACA,YAAMI,aAAa,GAAGpD,GAAG,CAAC+C,0BAAJ,IAAmChI,QAAQ,IAAI,IAAZ,IAAoBA,QAAQ,IAAIkI,cAAzF;AACA,aAAOE,WAAW,IAAIC,aAAtB;AACH;;AACD,WAAO,KAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIC,EAAAA,oBAAoB,CAACC,KAAD,EAAQlB,cAAR,EAAwBtS,cAAxB,EAAwC;AACxD;AACA;AACA;AACA,QAAI,KAAKqQ,mBAAL,IAA4B,KAAKpD,eAArC,EAAsD;AAClD,aAAO;AACH6E,QAAAA,CAAC,EAAE0B,KAAK,CAAC1B,CAAN,GAAU,KAAKzB,mBAAL,CAAyByB,CADnC;AAEHI,QAAAA,CAAC,EAAEsB,KAAK,CAACtB,CAAN,GAAU,KAAK7B,mBAAL,CAAyB6B;AAFnC,OAAP;AAIH,KATuD,CAUxD;AACA;;;AACA,UAAMzO,OAAO,GAAG8O,4BAA4B,CAACD,cAAD,CAA5C;AACA,UAAM/T,QAAQ,GAAG,KAAKgQ,aAAtB,CAbwD,CAcxD;AACA;;AACA,UAAMkF,aAAa,GAAGxT,IAAI,CAACyT,GAAL,CAASF,KAAK,CAAC1B,CAAN,GAAUrO,OAAO,CAAC7E,KAAlB,GAA0BL,QAAQ,CAACK,KAA5C,EAAmD,CAAnD,CAAtB;AACA,UAAM+U,cAAc,GAAG1T,IAAI,CAACyT,GAAL,CAASF,KAAK,CAACtB,CAAN,GAAUzO,OAAO,CAAC/E,MAAlB,GAA2BH,QAAQ,CAACG,MAA7C,EAAqD,CAArD,CAAvB;AACA,UAAMkV,WAAW,GAAG3T,IAAI,CAACyT,GAAL,CAASnV,QAAQ,CAAC1B,GAAT,GAAemD,cAAc,CAACnD,GAA9B,GAAoC2W,KAAK,CAACtB,CAAnD,EAAsD,CAAtD,CAApB;AACA,UAAM2B,YAAY,GAAG5T,IAAI,CAACyT,GAAL,CAASnV,QAAQ,CAACzB,IAAT,GAAgBkD,cAAc,CAAClD,IAA/B,GAAsC0W,KAAK,CAAC1B,CAArD,EAAwD,CAAxD,CAArB,CAnBwD,CAoBxD;;AACA,QAAIgC,KAAK,GAAG,CAAZ;AACA,QAAIC,KAAK,GAAG,CAAZ,CAtBwD,CAuBxD;AACA;AACA;;AACA,QAAItQ,OAAO,CAAC7E,KAAR,IAAiBL,QAAQ,CAACK,KAA9B,EAAqC;AACjCkV,MAAAA,KAAK,GAAGD,YAAY,IAAI,CAACJ,aAAzB;AACH,KAFD,MAGK;AACDK,MAAAA,KAAK,GAAGN,KAAK,CAAC1B,CAAN,GAAU,KAAK5E,eAAf,GAAiC3O,QAAQ,CAACzB,IAAT,GAAgBkD,cAAc,CAAClD,IAA/B,GAAsC0W,KAAK,CAAC1B,CAA7E,GAAiF,CAAzF;AACH;;AACD,QAAIrO,OAAO,CAAC/E,MAAR,IAAkBH,QAAQ,CAACG,MAA/B,EAAuC;AACnCqV,MAAAA,KAAK,GAAGH,WAAW,IAAI,CAACD,cAAxB;AACH,KAFD,MAGK;AACDI,MAAAA,KAAK,GAAGP,KAAK,CAACtB,CAAN,GAAU,KAAKhF,eAAf,GAAiC3O,QAAQ,CAAC1B,GAAT,GAAemD,cAAc,CAACnD,GAA9B,GAAoC2W,KAAK,CAACtB,CAA3E,GAA+E,CAAvF;AACH;;AACD,SAAK7B,mBAAL,GAA2B;AAAEyB,MAAAA,CAAC,EAAEgC,KAAL;AAAY5B,MAAAA,CAAC,EAAE6B;AAAf,KAA3B;AACA,WAAO;AACHjC,MAAAA,CAAC,EAAE0B,KAAK,CAAC1B,CAAN,GAAUgC,KADV;AAEH5B,MAAAA,CAAC,EAAEsB,KAAK,CAACtB,CAAN,GAAU6B;AAFV,KAAP;AAIH;AACD;AACJ;AACA;AACA;AACA;;;AACIrE,EAAAA,cAAc,CAACE,QAAD,EAAWT,WAAX,EAAwB;AAClC,SAAK6E,mBAAL,CAAyBpE,QAAzB;;AACA,SAAKqE,wBAAL,CAA8B9E,WAA9B,EAA2CS,QAA3C;;AACA,SAAKsE,qBAAL,CAA2B/E,WAA3B,EAAwCS,QAAxC;;AACA,QAAIA,QAAQ,CAAC7M,UAAb,EAAyB;AACrB,WAAKoR,gBAAL,CAAsBvE,QAAQ,CAAC7M,UAA/B;AACH,KANiC,CAOlC;;;AACA,SAAKkL,aAAL,GAAqB2B,QAArB,CARkC,CASlC;AACA;AACA;;AACA,QAAI,KAAKvC,gBAAL,CAAsB/H,SAAtB,CAAgCP,MAApC,EAA4C;AACxC,YAAMZ,wBAAwB,GAAG,KAAKiQ,oBAAL,EAAjC;;AACA,YAAMC,WAAW,GAAG,IAAIpQ,8BAAJ,CAAmC2L,QAAnC,EAA6CzL,wBAA7C,CAApB;;AACA,WAAKkJ,gBAAL,CAAsB7H,IAAtB,CAA2B6O,WAA3B;AACH;;AACD,SAAKrG,gBAAL,GAAwB,KAAxB;AACH;AACD;;;AACAgG,EAAAA,mBAAmB,CAACpE,QAAD,EAAW;AAC1B,QAAI,CAAC,KAAKiC,wBAAV,EAAoC;AAChC;AACH;;AACD,UAAMyC,QAAQ,GAAG,KAAKxG,YAAL,CAAkB3G,gBAAlB,CAAmC,KAAK0K,wBAAxC,CAAjB;;AACA,QAAI0C,OAAJ;AACA,QAAIC,OAAO,GAAG5E,QAAQ,CAAC7L,QAAvB;;AACA,QAAI6L,QAAQ,CAAC9L,QAAT,KAAsB,QAA1B,EAAoC;AAChCyQ,MAAAA,OAAO,GAAG,QAAV;AACH,KAFD,MAGK,IAAI,KAAKvC,MAAL,EAAJ,EAAmB;AACpBuC,MAAAA,OAAO,GAAG3E,QAAQ,CAAC9L,QAAT,KAAsB,OAAtB,GAAgC,OAAhC,GAA0C,MAApD;AACH,KAFI,MAGA;AACDyQ,MAAAA,OAAO,GAAG3E,QAAQ,CAAC9L,QAAT,KAAsB,OAAtB,GAAgC,MAAhC,GAAyC,OAAnD;AACH;;AACD,SAAK,IAAIsB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkP,QAAQ,CAACvP,MAA7B,EAAqCK,CAAC,EAAtC,EAA0C;AACtCkP,MAAAA,QAAQ,CAAClP,CAAD,CAAR,CAAY5H,KAAZ,CAAkBiX,eAAlB,GAAqC,GAAEF,OAAQ,IAAGC,OAAQ,EAA1D;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACI1E,EAAAA,yBAAyB,CAACtM,MAAD,EAASoM,QAAT,EAAmB;AACxC,UAAMrR,QAAQ,GAAG,KAAKgQ,aAAtB;;AACA,UAAMmG,KAAK,GAAG,KAAK1C,MAAL,EAAd;;AACA,QAAItT,MAAJ,EAAY7B,GAAZ,EAAiB+D,MAAjB;;AACA,QAAIgP,QAAQ,CAAC7L,QAAT,KAAsB,KAA1B,EAAiC;AAC7B;AACAlH,MAAAA,GAAG,GAAG2G,MAAM,CAAC0O,CAAb;AACAxT,MAAAA,MAAM,GAAGH,QAAQ,CAACG,MAAT,GAAkB7B,GAAlB,GAAwB,KAAKqQ,eAAtC;AACH,KAJD,MAKK,IAAI0C,QAAQ,CAAC7L,QAAT,KAAsB,QAA1B,EAAoC;AACrC;AACA;AACA;AACAnD,MAAAA,MAAM,GAAGrC,QAAQ,CAACG,MAAT,GAAkB8E,MAAM,CAAC0O,CAAzB,GAA6B,KAAKhF,eAAL,GAAuB,CAA7D;AACAxO,MAAAA,MAAM,GAAGH,QAAQ,CAACG,MAAT,GAAkBkC,MAAlB,GAA2B,KAAKsM,eAAzC;AACH,KANI,MAOA;AACD;AACA;AACA;AACA;AACA,YAAMyH,8BAA8B,GAAG1U,IAAI,CAAC2U,GAAL,CAASrW,QAAQ,CAACqC,MAAT,GAAkB4C,MAAM,CAAC0O,CAAzB,GAA6B3T,QAAQ,CAAC1B,GAA/C,EAAoD2G,MAAM,CAAC0O,CAA3D,CAAvC;AACA,YAAM2C,cAAc,GAAG,KAAKjI,oBAAL,CAA0BlO,MAAjD;AACAA,MAAAA,MAAM,GAAGiW,8BAA8B,GAAG,CAA1C;AACA9X,MAAAA,GAAG,GAAG2G,MAAM,CAAC0O,CAAP,GAAWyC,8BAAjB;;AACA,UAAIjW,MAAM,GAAGmW,cAAT,IAA2B,CAAC,KAAK7G,gBAAjC,IAAqD,CAAC,KAAKjB,cAA/D,EAA+E;AAC3ElQ,QAAAA,GAAG,GAAG2G,MAAM,CAAC0O,CAAP,GAAW2C,cAAc,GAAG,CAAlC;AACH;AACJ,KA5BuC,CA6BxC;;;AACA,UAAMC,4BAA4B,GAAIlF,QAAQ,CAAC9L,QAAT,KAAsB,OAAtB,IAAiC,CAAC4Q,KAAnC,IAA8C9E,QAAQ,CAAC9L,QAAT,KAAsB,KAAtB,IAA+B4Q,KAAlH,CA9BwC,CA+BxC;;AACA,UAAMK,2BAA2B,GAAInF,QAAQ,CAAC9L,QAAT,KAAsB,KAAtB,IAA+B,CAAC4Q,KAAjC,IAA4C9E,QAAQ,CAAC9L,QAAT,KAAsB,OAAtB,IAAiC4Q,KAAjH;AACA,QAAI9V,KAAJ,EAAW9B,IAAX,EAAiBiE,KAAjB;;AACA,QAAIgU,2BAAJ,EAAiC;AAC7BhU,MAAAA,KAAK,GAAGxC,QAAQ,CAACK,KAAT,GAAiB4E,MAAM,CAACsO,CAAxB,GAA4B,KAAK5E,eAAzC;AACAtO,MAAAA,KAAK,GAAG4E,MAAM,CAACsO,CAAP,GAAW,KAAK5E,eAAxB;AACH,KAHD,MAIK,IAAI4H,4BAAJ,EAAkC;AACnChY,MAAAA,IAAI,GAAG0G,MAAM,CAACsO,CAAd;AACAlT,MAAAA,KAAK,GAAGL,QAAQ,CAACwC,KAAT,GAAiByC,MAAM,CAACsO,CAAhC;AACH,KAHI,MAIA;AACD;AACA;AACA;AACA;AACA,YAAM6C,8BAA8B,GAAG1U,IAAI,CAAC2U,GAAL,CAASrW,QAAQ,CAACwC,KAAT,GAAiByC,MAAM,CAACsO,CAAxB,GAA4BvT,QAAQ,CAACzB,IAA9C,EAAoD0G,MAAM,CAACsO,CAA3D,CAAvC;AACA,YAAMkD,aAAa,GAAG,KAAKpI,oBAAL,CAA0BhO,KAAhD;AACAA,MAAAA,KAAK,GAAG+V,8BAA8B,GAAG,CAAzC;AACA7X,MAAAA,IAAI,GAAG0G,MAAM,CAACsO,CAAP,GAAW6C,8BAAlB;;AACA,UAAI/V,KAAK,GAAGoW,aAAR,IAAyB,CAAC,KAAKhH,gBAA/B,IAAmD,CAAC,KAAKjB,cAA7D,EAA6E;AACzEjQ,QAAAA,IAAI,GAAG0G,MAAM,CAACsO,CAAP,GAAWkD,aAAa,GAAG,CAAlC;AACH;AACJ;;AACD,WAAO;AAAEnY,MAAAA,GAAG,EAAEA,GAAP;AAAYC,MAAAA,IAAI,EAAEA,IAAlB;AAAwB8D,MAAAA,MAAM,EAAEA,MAAhC;AAAwCG,MAAAA,KAAK,EAAEA,KAA/C;AAAsDnC,MAAAA,KAAtD;AAA6DF,MAAAA;AAA7D,KAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIwV,EAAAA,qBAAqB,CAAC1Q,MAAD,EAASoM,QAAT,EAAmB;AACpC,UAAMC,eAAe,GAAG,KAAKC,yBAAL,CAA+BtM,MAA/B,EAAuCoM,QAAvC,CAAxB,CADoC,CAEpC;AACA;;;AACA,QAAI,CAAC,KAAK5B,gBAAN,IAA0B,CAAC,KAAKjB,cAApC,EAAoD;AAChD8C,MAAAA,eAAe,CAACnR,MAAhB,GAAyBuB,IAAI,CAAC2U,GAAL,CAAS/E,eAAe,CAACnR,MAAzB,EAAiC,KAAKkO,oBAAL,CAA0BlO,MAA3D,CAAzB;AACAmR,MAAAA,eAAe,CAACjR,KAAhB,GAAwBqB,IAAI,CAAC2U,GAAL,CAAS/E,eAAe,CAACjR,KAAzB,EAAgC,KAAKgO,oBAAL,CAA0BhO,KAA1D,CAAxB;AACH;;AACD,UAAMqW,MAAM,GAAG,EAAf;;AACA,QAAI,KAAKC,iBAAL,EAAJ,EAA8B;AAC1BD,MAAAA,MAAM,CAACpY,GAAP,GAAaoY,MAAM,CAACnY,IAAP,GAAc,GAA3B;AACAmY,MAAAA,MAAM,CAACrU,MAAP,GAAgBqU,MAAM,CAAClU,KAAP,GAAekU,MAAM,CAAC7J,SAAP,GAAmB6J,MAAM,CAAC9J,QAAP,GAAkB,EAApE;AACA8J,MAAAA,MAAM,CAACrW,KAAP,GAAeqW,MAAM,CAACvW,MAAP,GAAgB,MAA/B;AACH,KAJD,MAKK;AACD,YAAM0M,SAAS,GAAG,KAAK/L,WAAL,CAAiB8K,SAAjB,GAA6BiB,SAA/C;;AACA,YAAMD,QAAQ,GAAG,KAAK9L,WAAL,CAAiB8K,SAAjB,GAA6BgB,QAA9C;;AACA8J,MAAAA,MAAM,CAACvW,MAAP,GAAgBvD,mBAAmB,CAAC0U,eAAe,CAACnR,MAAjB,CAAnC;AACAuW,MAAAA,MAAM,CAACpY,GAAP,GAAa1B,mBAAmB,CAAC0U,eAAe,CAAChT,GAAjB,CAAhC;AACAoY,MAAAA,MAAM,CAACrU,MAAP,GAAgBzF,mBAAmB,CAAC0U,eAAe,CAACjP,MAAjB,CAAnC;AACAqU,MAAAA,MAAM,CAACrW,KAAP,GAAezD,mBAAmB,CAAC0U,eAAe,CAACjR,KAAjB,CAAlC;AACAqW,MAAAA,MAAM,CAACnY,IAAP,GAAc3B,mBAAmB,CAAC0U,eAAe,CAAC/S,IAAjB,CAAjC;AACAmY,MAAAA,MAAM,CAAClU,KAAP,GAAe5F,mBAAmB,CAAC0U,eAAe,CAAC9O,KAAjB,CAAlC,CARC,CASD;;AACA,UAAI6O,QAAQ,CAAC9L,QAAT,KAAsB,QAA1B,EAAoC;AAChCmR,QAAAA,MAAM,CAAC1E,UAAP,GAAoB,QAApB;AACH,OAFD,MAGK;AACD0E,QAAAA,MAAM,CAAC1E,UAAP,GAAoBX,QAAQ,CAAC9L,QAAT,KAAsB,KAAtB,GAA8B,UAA9B,GAA2C,YAA/D;AACH;;AACD,UAAI8L,QAAQ,CAAC7L,QAAT,KAAsB,QAA1B,EAAoC;AAChCkR,QAAAA,MAAM,CAACzE,cAAP,GAAwB,QAAxB;AACH,OAFD,MAGK;AACDyE,QAAAA,MAAM,CAACzE,cAAP,GAAwBZ,QAAQ,CAAC7L,QAAT,KAAsB,QAAtB,GAAiC,UAAjC,GAA8C,YAAtE;AACH;;AACD,UAAIqH,SAAJ,EAAe;AACX6J,QAAAA,MAAM,CAAC7J,SAAP,GAAmBjQ,mBAAmB,CAACiQ,SAAD,CAAtC;AACH;;AACD,UAAID,QAAJ,EAAc;AACV8J,QAAAA,MAAM,CAAC9J,QAAP,GAAkBhQ,mBAAmB,CAACgQ,QAAD,CAArC;AACH;AACJ;;AACD,SAAKyB,oBAAL,GAA4BiD,eAA5B;AACAS,IAAAA,YAAY,CAAC,KAAKxC,YAAL,CAAkBtQ,KAAnB,EAA0ByX,MAA1B,CAAZ;AACH;AACD;;;AACA3G,EAAAA,uBAAuB,GAAG;AACtBgC,IAAAA,YAAY,CAAC,KAAKxC,YAAL,CAAkBtQ,KAAnB,EAA0B;AAClCX,MAAAA,GAAG,EAAE,GAD6B;AAElCC,MAAAA,IAAI,EAAE,GAF4B;AAGlCiE,MAAAA,KAAK,EAAE,GAH2B;AAIlCH,MAAAA,MAAM,EAAE,GAJ0B;AAKlClC,MAAAA,MAAM,EAAE,EAL0B;AAMlCE,MAAAA,KAAK,EAAE,EAN2B;AAOlC2R,MAAAA,UAAU,EAAE,EAPsB;AAQlCC,MAAAA,cAAc,EAAE;AARkB,KAA1B,CAAZ;AAUH;AACD;;;AACAnC,EAAAA,0BAA0B,GAAG;AACzBiC,IAAAA,YAAY,CAAC,KAAK3I,KAAL,CAAWnK,KAAZ,EAAmB;AAC3BX,MAAAA,GAAG,EAAE,EADsB;AAE3BC,MAAAA,IAAI,EAAE,EAFqB;AAG3B8D,MAAAA,MAAM,EAAE,EAHmB;AAI3BG,MAAAA,KAAK,EAAE,EAJoB;AAK3B6O,MAAAA,QAAQ,EAAE,EALiB;AAM3BuF,MAAAA,SAAS,EAAE;AANgB,KAAnB,CAAZ;AAQH;AACD;;;AACAlB,EAAAA,wBAAwB,CAAC9E,WAAD,EAAcS,QAAd,EAAwB;AAC5C,UAAMqF,MAAM,GAAG,EAAf;;AACA,UAAMG,gBAAgB,GAAG,KAAKF,iBAAL,EAAzB;;AACA,UAAMG,qBAAqB,GAAG,KAAKrI,sBAAnC;;AACA,UAAM9K,MAAM,GAAG,KAAK7C,WAAL,CAAiB8K,SAAjB,EAAf;;AACA,QAAIiL,gBAAJ,EAAsB;AAClB,YAAMpV,cAAc,GAAG,KAAKtD,cAAL,CAAoBa,yBAApB,EAAvB;;AACA+S,MAAAA,YAAY,CAAC2E,MAAD,EAAS,KAAKK,iBAAL,CAAuB1F,QAAvB,EAAiCT,WAAjC,EAA8CnP,cAA9C,CAAT,CAAZ;AACAsQ,MAAAA,YAAY,CAAC2E,MAAD,EAAS,KAAKM,iBAAL,CAAuB3F,QAAvB,EAAiCT,WAAjC,EAA8CnP,cAA9C,CAAT,CAAZ;AACH,KAJD,MAKK;AACDiV,MAAAA,MAAM,CAACrF,QAAP,GAAkB,QAAlB;AACH,KAZ2C,CAa5C;AACA;AACA;AACA;AACA;;;AACA,QAAI4F,eAAe,GAAG,EAAtB;;AACA,QAAI9R,OAAO,GAAG,KAAK8O,UAAL,CAAgB5C,QAAhB,EAA0B,GAA1B,CAAd;;AACA,QAAIjM,OAAO,GAAG,KAAK6O,UAAL,CAAgB5C,QAAhB,EAA0B,GAA1B,CAAd;;AACA,QAAIlM,OAAJ,EAAa;AACT8R,MAAAA,eAAe,IAAK,cAAa9R,OAAQ,MAAzC;AACH;;AACD,QAAIC,OAAJ,EAAa;AACT6R,MAAAA,eAAe,IAAK,cAAa7R,OAAQ,KAAzC;AACH;;AACDsR,IAAAA,MAAM,CAACE,SAAP,GAAmBK,eAAe,CAACC,IAAhB,EAAnB,CA3B4C,CA4B5C;AACA;AACA;AACA;AACA;;AACA,QAAIvT,MAAM,CAACkJ,SAAX,EAAsB;AAClB,UAAIgK,gBAAJ,EAAsB;AAClBH,QAAAA,MAAM,CAAC7J,SAAP,GAAmBjQ,mBAAmB,CAAC+G,MAAM,CAACkJ,SAAR,CAAtC;AACH,OAFD,MAGK,IAAIiK,qBAAJ,EAA2B;AAC5BJ,QAAAA,MAAM,CAAC7J,SAAP,GAAmB,EAAnB;AACH;AACJ;;AACD,QAAIlJ,MAAM,CAACiJ,QAAX,EAAqB;AACjB,UAAIiK,gBAAJ,EAAsB;AAClBH,QAAAA,MAAM,CAAC9J,QAAP,GAAkBhQ,mBAAmB,CAAC+G,MAAM,CAACiJ,QAAR,CAArC;AACH,OAFD,MAGK,IAAIkK,qBAAJ,EAA2B;AAC5BJ,QAAAA,MAAM,CAAC9J,QAAP,GAAkB,EAAlB;AACH;AACJ;;AACDmF,IAAAA,YAAY,CAAC,KAAK3I,KAAL,CAAWnK,KAAZ,EAAmByX,MAAnB,CAAZ;AACH;AACD;;;AACAK,EAAAA,iBAAiB,CAAC1F,QAAD,EAAWT,WAAX,EAAwBnP,cAAxB,EAAwC;AACrD;AACA;AACA,QAAIiV,MAAM,GAAG;AAAEpY,MAAAA,GAAG,EAAE,EAAP;AAAW+D,MAAAA,MAAM,EAAE;AAAnB,KAAb;;AACA,QAAIyO,YAAY,GAAG,KAAKC,gBAAL,CAAsBH,WAAtB,EAAmC,KAAKR,YAAxC,EAAsDiB,QAAtD,CAAnB;;AACA,QAAI,KAAK/C,SAAT,EAAoB;AAChBwC,MAAAA,YAAY,GAAG,KAAKkE,oBAAL,CAA0BlE,YAA1B,EAAwC,KAAKV,YAA7C,EAA2D3O,cAA3D,CAAf;AACH,KAPoD,CAQrD;AACA;;;AACA,QAAI4P,QAAQ,CAAC7L,QAAT,KAAsB,QAA1B,EAAoC;AAChC;AACA;AACA,YAAM2R,cAAc,GAAG,KAAK1Y,SAAL,CAAeK,eAAf,CAA+BsY,YAAtD;AACAV,MAAAA,MAAM,CAACrU,MAAP,GAAiB,GAAE8U,cAAc,IAAIrG,YAAY,CAAC6C,CAAb,GAAiB,KAAKvD,YAAL,CAAkBjQ,MAAvC,CAA+C,IAAhF;AACH,KALD,MAMK;AACDuW,MAAAA,MAAM,CAACpY,GAAP,GAAa1B,mBAAmB,CAACkU,YAAY,CAAC6C,CAAd,CAAhC;AACH;;AACD,WAAO+C,MAAP;AACH;AACD;;;AACAM,EAAAA,iBAAiB,CAAC3F,QAAD,EAAWT,WAAX,EAAwBnP,cAAxB,EAAwC;AACrD;AACA;AACA,QAAIiV,MAAM,GAAG;AAAEnY,MAAAA,IAAI,EAAE,EAAR;AAAYiE,MAAAA,KAAK,EAAE;AAAnB,KAAb;;AACA,QAAIsO,YAAY,GAAG,KAAKC,gBAAL,CAAsBH,WAAtB,EAAmC,KAAKR,YAAxC,EAAsDiB,QAAtD,CAAnB;;AACA,QAAI,KAAK/C,SAAT,EAAoB;AAChBwC,MAAAA,YAAY,GAAG,KAAKkE,oBAAL,CAA0BlE,YAA1B,EAAwC,KAAKV,YAA7C,EAA2D3O,cAA3D,CAAf;AACH,KAPoD,CAQrD;AACA;AACA;AACA;;;AACA,QAAI4V,uBAAJ;;AACA,QAAI,KAAK5D,MAAL,EAAJ,EAAmB;AACf4D,MAAAA,uBAAuB,GAAGhG,QAAQ,CAAC9L,QAAT,KAAsB,KAAtB,GAA8B,MAA9B,GAAuC,OAAjE;AACH,KAFD,MAGK;AACD8R,MAAAA,uBAAuB,GAAGhG,QAAQ,CAAC9L,QAAT,KAAsB,KAAtB,GAA8B,OAA9B,GAAwC,MAAlE;AACH,KAlBoD,CAmBrD;AACA;;;AACA,QAAI8R,uBAAuB,KAAK,OAAhC,EAAyC;AACrC,YAAMC,aAAa,GAAG,KAAK7Y,SAAL,CAAeK,eAAf,CAA+ByY,WAArD;AACAb,MAAAA,MAAM,CAAClU,KAAP,GAAgB,GAAE8U,aAAa,IAAIxG,YAAY,CAACyC,CAAb,GAAiB,KAAKnD,YAAL,CAAkB/P,KAAvC,CAA8C,IAA7E;AACH,KAHD,MAIK;AACDqW,MAAAA,MAAM,CAACnY,IAAP,GAAc3B,mBAAmB,CAACkU,YAAY,CAACyC,CAAd,CAAjC;AACH;;AACD,WAAOmD,MAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIb,EAAAA,oBAAoB,GAAG;AACnB;AACA,UAAM2B,YAAY,GAAG,KAAKrH,cAAL,EAArB;;AACA,UAAMsH,aAAa,GAAG,KAAKrO,KAAL,CAAW9F,qBAAX,EAAtB,CAHmB,CAInB;AACA;AACA;;;AACA,UAAMoU,qBAAqB,GAAG,KAAK9I,YAAL,CAAkB+I,GAAlB,CAAsBC,UAAU,IAAI;AAC9D,aAAOA,UAAU,CAACC,aAAX,GAA2BC,aAA3B,CAAyCxU,qBAAzC,EAAP;AACH,KAF6B,CAA9B;;AAGA,WAAO;AACHyU,MAAAA,eAAe,EAAErV,2BAA2B,CAAC8U,YAAD,EAAeE,qBAAf,CADzC;AAEHM,MAAAA,mBAAmB,EAAEjW,4BAA4B,CAACyV,YAAD,EAAeE,qBAAf,CAF9C;AAGHO,MAAAA,gBAAgB,EAAEvV,2BAA2B,CAAC+U,aAAD,EAAgBC,qBAAhB,CAH1C;AAIHQ,MAAAA,oBAAoB,EAAEnW,4BAA4B,CAAC0V,aAAD,EAAgBC,qBAAhB;AAJ/C,KAAP;AAMH;AACD;;;AACAnD,EAAAA,kBAAkB,CAAC/N,MAAD,EAAS,GAAG2R,SAAZ,EAAuB;AACrC,WAAOA,SAAS,CAACC,MAAV,CAAiB,CAACC,YAAD,EAAeC,eAAf,KAAmC;AACvD,aAAOD,YAAY,GAAG3W,IAAI,CAACyT,GAAL,CAASmD,eAAT,EAA0B,CAA1B,CAAtB;AACH,KAFM,EAEJ9R,MAFI,CAAP;AAGH;AACD;;;AACAyJ,EAAAA,wBAAwB,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA,UAAM5P,KAAK,GAAG,KAAK5B,SAAL,CAAeK,eAAf,CAA+ByY,WAA7C;AACA,UAAMpX,MAAM,GAAG,KAAK1B,SAAL,CAAeK,eAAf,CAA+BsY,YAA9C;;AACA,UAAM3V,cAAc,GAAG,KAAKtD,cAAL,CAAoBa,yBAApB,EAAvB;;AACA,WAAO;AACHV,MAAAA,GAAG,EAAEmD,cAAc,CAACnD,GAAf,GAAqB,KAAKqQ,eAD5B;AAEHpQ,MAAAA,IAAI,EAAEkD,cAAc,CAAClD,IAAf,GAAsB,KAAKoQ,eAF9B;AAGHnM,MAAAA,KAAK,EAAEf,cAAc,CAAClD,IAAf,GAAsB8B,KAAtB,GAA8B,KAAKsO,eAHvC;AAIHtM,MAAAA,MAAM,EAAEZ,cAAc,CAACnD,GAAf,GAAqB6B,MAArB,GAA8B,KAAKwO,eAJxC;AAKHtO,MAAAA,KAAK,EAAEA,KAAK,GAAG,IAAI,KAAKsO,eALrB;AAMHxO,MAAAA,MAAM,EAAEA,MAAM,GAAG,IAAI,KAAKwO;AANvB,KAAP;AAQH;AACD;;;AACA8E,EAAAA,MAAM,GAAG;AACL,WAAO,KAAK3S,WAAL,CAAiB0L,YAAjB,OAAoC,KAA3C;AACH;AACD;;;AACAmK,EAAAA,iBAAiB,GAAG;AAChB,WAAO,CAAC,KAAKlI,sBAAN,IAAgC,KAAKH,SAA5C;AACH;AACD;;;AACA2F,EAAAA,UAAU,CAAC5C,QAAD,EAAWkH,IAAX,EAAiB;AACvB,QAAIA,IAAI,KAAK,GAAb,EAAkB;AACd;AACA;AACA,aAAOlH,QAAQ,CAAClM,OAAT,IAAoB,IAApB,GAA2B,KAAK6J,QAAhC,GAA2CqC,QAAQ,CAAClM,OAA3D;AACH;;AACD,WAAOkM,QAAQ,CAACjM,OAAT,IAAoB,IAApB,GAA2B,KAAK6J,QAAhC,GAA2CoC,QAAQ,CAACjM,OAA3D;AACH;AACD;;;AACAkK,EAAAA,kBAAkB,GAAG;AACjB,QAAI,OAAOnO,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;AAC/C,UAAI,CAAC,KAAK0N,mBAAL,CAAyBrI,MAA9B,EAAsC;AAClC,cAAMjG,KAAK,CAAC,uEAAD,CAAX;AACH,OAH8C,CAI/C;AACA;;;AACA,WAAKsO,mBAAL,CAAyB2J,OAAzB,CAAiCC,IAAI,IAAI;AACrCzS,QAAAA,0BAA0B,CAAC,SAAD,EAAYyS,IAAI,CAACpT,OAAjB,CAA1B;AACAQ,QAAAA,wBAAwB,CAAC,SAAD,EAAY4S,IAAI,CAACnT,OAAjB,CAAxB;AACAU,QAAAA,0BAA0B,CAAC,UAAD,EAAayS,IAAI,CAAClT,QAAlB,CAA1B;AACAM,QAAAA,wBAAwB,CAAC,UAAD,EAAa4S,IAAI,CAACjT,QAAlB,CAAxB;AACH,OALD;AAMH;AACJ;AACD;;;AACAoQ,EAAAA,gBAAgB,CAACpI,UAAD,EAAa;AACzB,QAAI,KAAKpE,KAAT,EAAgB;AACZvM,MAAAA,WAAW,CAAC2Q,UAAD,CAAX,CAAwBgL,OAAxB,CAAgCE,QAAQ,IAAI;AACxC,YAAIA,QAAQ,KAAK,EAAb,IAAmB,KAAKxJ,oBAAL,CAA0B5I,OAA1B,CAAkCoS,QAAlC,MAAgD,CAAC,CAAxE,EAA2E;AACvE,eAAKxJ,oBAAL,CAA0B9I,IAA1B,CAA+BsS,QAA/B;;AACA,eAAKtP,KAAL,CAAWlK,SAAX,CAAqBC,GAArB,CAAyBuZ,QAAzB;AACH;AACJ,OALD;AAMH;AACJ;AACD;;;AACA7I,EAAAA,kBAAkB,GAAG;AACjB,QAAI,KAAKzG,KAAT,EAAgB;AACZ,WAAK8F,oBAAL,CAA0BsJ,OAA1B,CAAkCE,QAAQ,IAAI;AAC1C,aAAKtP,KAAL,CAAWlK,SAAX,CAAqBU,MAArB,CAA4B8Y,QAA5B;AACH,OAFD;;AAGA,WAAKxJ,oBAAL,GAA4B,EAA5B;AACH;AACJ;AACD;;;AACAiB,EAAAA,cAAc,GAAG;AACb,UAAMlL,MAAM,GAAG,KAAK+N,OAApB;;AACA,QAAI/N,MAAM,YAAY7I,UAAtB,EAAkC;AAC9B,aAAO6I,MAAM,CAAC6S,aAAP,CAAqBxU,qBAArB,EAAP;AACH,KAJY,CAKb;;;AACA,QAAI2B,MAAM,YAAY0T,OAAtB,EAA+B;AAC3B,aAAO1T,MAAM,CAAC3B,qBAAP,EAAP;AACH;;AACD,UAAMjD,KAAK,GAAG4E,MAAM,CAAC5E,KAAP,IAAgB,CAA9B;AACA,UAAMF,MAAM,GAAG8E,MAAM,CAAC9E,MAAP,IAAiB,CAAhC,CAVa,CAWb;;AACA,WAAO;AACH7B,MAAAA,GAAG,EAAE2G,MAAM,CAAC0O,CADT;AAEHtR,MAAAA,MAAM,EAAE4C,MAAM,CAAC0O,CAAP,GAAWxT,MAFhB;AAGH5B,MAAAA,IAAI,EAAE0G,MAAM,CAACsO,CAHV;AAIH/Q,MAAAA,KAAK,EAAEyC,MAAM,CAACsO,CAAP,GAAWlT,KAJf;AAKHF,MAAAA,MALG;AAMHE,MAAAA;AANG,KAAP;AAQH;;AA74BmC;AA+4BxC;;;AACA,SAAS0R,YAAT,CAAsB6G,WAAtB,EAAmCC,MAAnC,EAA2C;AACvC,OAAK,IAAI9T,GAAT,IAAgB8T,MAAhB,EAAwB;AACpB,QAAIA,MAAM,CAACC,cAAP,CAAsB/T,GAAtB,CAAJ,EAAgC;AAC5B6T,MAAAA,WAAW,CAAC7T,GAAD,CAAX,GAAmB8T,MAAM,CAAC9T,GAAD,CAAzB;AACH;AACJ;;AACD,SAAO6T,WAAP;AACH;AACD;AACA;AACA;AACA;;;AACA,SAAS/D,aAAT,CAAuBkE,KAAvB,EAA8B;AAC1B,MAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,IAAI,IAA1C,EAAgD;AAC5C,UAAM,CAAChT,KAAD,EAAQiT,KAAR,IAAiBD,KAAK,CAACE,KAAN,CAAYhL,cAAZ,CAAvB;AACA,WAAO,CAAC+K,KAAD,IAAUA,KAAK,KAAK,IAApB,GAA2BE,UAAU,CAACnT,KAAD,CAArC,GAA+C,IAAtD;AACH;;AACD,SAAOgT,KAAK,IAAI,IAAhB;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS/E,4BAAT,CAAsCmF,UAAtC,EAAkD;AAC9C,SAAO;AACH7a,IAAAA,GAAG,EAAEoD,IAAI,CAAC0X,KAAL,CAAWD,UAAU,CAAC7a,GAAtB,CADF;AAEHkE,IAAAA,KAAK,EAAEd,IAAI,CAAC0X,KAAL,CAAWD,UAAU,CAAC3W,KAAtB,CAFJ;AAGHH,IAAAA,MAAM,EAAEX,IAAI,CAAC0X,KAAL,CAAWD,UAAU,CAAC9W,MAAtB,CAHL;AAIH9D,IAAAA,IAAI,EAAEmD,IAAI,CAAC0X,KAAL,CAAWD,UAAU,CAAC5a,IAAtB,CAJH;AAKH8B,IAAAA,KAAK,EAAEqB,IAAI,CAAC0X,KAAL,CAAWD,UAAU,CAAC9Y,KAAtB,CALJ;AAMHF,IAAAA,MAAM,EAAEuB,IAAI,CAAC0X,KAAL,CAAWD,UAAU,CAAChZ,MAAtB;AANL,GAAP;AAQH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMkZ,YAAY,GAAG,4BAArB;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,sBAAN,CAA6B;AACzBpb,EAAAA,WAAW,GAAG;AACV,SAAKqb,YAAL,GAAoB,QAApB;AACA,SAAKC,UAAL,GAAkB,EAAlB;AACA,SAAKC,aAAL,GAAqB,EAArB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,YAAL,GAAoB,EAApB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,eAAL,GAAuB,EAAvB;AACA,SAAKC,MAAL,GAAc,EAAd;AACA,SAAKC,OAAL,GAAe,EAAf;AACH;;AACDrb,EAAAA,MAAM,CAACwC,UAAD,EAAa;AACf,UAAMyC,MAAM,GAAGzC,UAAU,CAAC0K,SAAX,EAAf;AACA,SAAK9K,WAAL,GAAmBI,UAAnB;;AACA,QAAI,KAAK4Y,MAAL,IAAe,CAACnW,MAAM,CAACtD,KAA3B,EAAkC;AAC9Ba,MAAAA,UAAU,CAAC8K,UAAX,CAAsB;AAAE3L,QAAAA,KAAK,EAAE,KAAKyZ;AAAd,OAAtB;AACH;;AACD,QAAI,KAAKC,OAAL,IAAgB,CAACpW,MAAM,CAACxD,MAA5B,EAAoC;AAChCe,MAAAA,UAAU,CAAC8K,UAAX,CAAsB;AAAE7L,QAAAA,MAAM,EAAE,KAAK4Z;AAAf,OAAtB;AACH;;AACD7Y,IAAAA,UAAU,CAACmJ,WAAX,CAAuBnL,SAAvB,CAAiCC,GAAjC,CAAqCka,YAArC;AACA,SAAK7J,WAAL,GAAmB,KAAnB;AACH;AACD;AACJ;AACA;AACA;;;AACIlR,EAAAA,GAAG,CAACyH,KAAK,GAAG,EAAT,EAAa;AACZ,SAAK0T,aAAL,GAAqB,EAArB;AACA,SAAKD,UAAL,GAAkBzT,KAAlB;AACA,SAAK6T,WAAL,GAAmB,YAAnB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIrb,EAAAA,IAAI,CAACwH,KAAK,GAAG,EAAT,EAAa;AACb,SAAK4T,YAAL,GAAoB,EAApB;AACA,SAAKD,WAAL,GAAmB3T,KAAnB;AACA,SAAK8T,eAAL,GAAuB,YAAvB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIxX,EAAAA,MAAM,CAAC0D,KAAK,GAAG,EAAT,EAAa;AACf,SAAKyT,UAAL,GAAkB,EAAlB;AACA,SAAKC,aAAL,GAAqB1T,KAArB;AACA,SAAK6T,WAAL,GAAmB,UAAnB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIpX,EAAAA,KAAK,CAACuD,KAAK,GAAG,EAAT,EAAa;AACd,SAAK2T,WAAL,GAAmB,EAAnB;AACA,SAAKC,YAAL,GAAoB5T,KAApB;AACA,SAAK8T,eAAL,GAAuB,UAAvB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIxZ,EAAAA,KAAK,CAAC0F,KAAK,GAAG,EAAT,EAAa;AACd,QAAI,KAAKjF,WAAT,EAAsB;AAClB,WAAKA,WAAL,CAAiBkL,UAAjB,CAA4B;AAAE3L,QAAAA,KAAK,EAAE0F;AAAT,OAA5B;AACH,KAFD,MAGK;AACD,WAAK+T,MAAL,GAAc/T,KAAd;AACH;;AACD,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACI5F,EAAAA,MAAM,CAAC4F,KAAK,GAAG,EAAT,EAAa;AACf,QAAI,KAAKjF,WAAT,EAAsB;AAClB,WAAKA,WAAL,CAAiBkL,UAAjB,CAA4B;AAAE7L,QAAAA,MAAM,EAAE4F;AAAV,OAA5B;AACH,KAFD,MAGK;AACD,WAAKgU,OAAL,GAAehU,KAAf;AACH;;AACD,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACIiU,EAAAA,kBAAkB,CAAC9G,MAAM,GAAG,EAAV,EAAc;AAC5B,SAAK3U,IAAL,CAAU2U,MAAV;AACA,SAAK2G,eAAL,GAAuB,QAAvB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACII,EAAAA,gBAAgB,CAAC/G,MAAM,GAAG,EAAV,EAAc;AAC1B,SAAK5U,GAAL,CAAS4U,MAAT;AACA,SAAK0G,WAAL,GAAmB,QAAnB;AACA,WAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACI/N,EAAAA,KAAK,GAAG;AACJ;AACA;AACA;AACA,QAAI,CAAC,KAAK/K,WAAN,IAAqB,CAAC,KAAKA,WAAL,CAAiBC,WAAjB,EAA1B,EAA0D;AACtD;AACH;;AACD,UAAM2V,MAAM,GAAG,KAAK5V,WAAL,CAAiBuC,cAAjB,CAAgCpE,KAA/C;AACA,UAAMib,YAAY,GAAG,KAAKpZ,WAAL,CAAiBuJ,WAAjB,CAA6BpL,KAAlD;;AACA,UAAM0E,MAAM,GAAG,KAAK7C,WAAL,CAAiB8K,SAAjB,EAAf;;AACA,UAAM;AAAEvL,MAAAA,KAAF;AAASF,MAAAA,MAAT;AAAiByM,MAAAA,QAAjB;AAA2BC,MAAAA;AAA3B,QAAyClJ,MAA/C;AACA,UAAMwW,yBAAyB,GAAG,CAAC9Z,KAAK,KAAK,MAAV,IAAoBA,KAAK,KAAK,OAA/B,MAC7B,CAACuM,QAAD,IAAaA,QAAQ,KAAK,MAA1B,IAAoCA,QAAQ,KAAK,OADpB,CAAlC;AAEA,UAAMwN,uBAAuB,GAAG,CAACja,MAAM,KAAK,MAAX,IAAqBA,MAAM,KAAK,OAAjC,MAC3B,CAAC0M,SAAD,IAAcA,SAAS,KAAK,MAA5B,IAAsCA,SAAS,KAAK,OADzB,CAAhC;AAEA6J,IAAAA,MAAM,CAACrF,QAAP,GAAkB,KAAKkI,YAAvB;AACA7C,IAAAA,MAAM,CAAC2D,UAAP,GAAoBF,yBAAyB,GAAG,GAAH,GAAS,KAAKT,WAA3D;AACAhD,IAAAA,MAAM,CAAC4D,SAAP,GAAmBF,uBAAuB,GAAG,GAAH,GAAS,KAAKZ,UAAxD;AACA9C,IAAAA,MAAM,CAAC6D,YAAP,GAAsB,KAAKd,aAA3B;AACA/C,IAAAA,MAAM,CAAC8D,WAAP,GAAqB,KAAKb,YAA1B;;AACA,QAAIQ,yBAAJ,EAA+B;AAC3BD,MAAAA,YAAY,CAACjI,cAAb,GAA8B,YAA9B;AACH,KAFD,MAGK,IAAI,KAAK4H,eAAL,KAAyB,QAA7B,EAAuC;AACxCK,MAAAA,YAAY,CAACjI,cAAb,GAA8B,QAA9B;AACH,KAFI,MAGA,IAAI,KAAKnR,WAAL,CAAiB8K,SAAjB,GAA6BQ,SAA7B,KAA2C,KAA/C,EAAsD;AACvD;AACA;AACA;AACA;AACA,UAAI,KAAKyN,eAAL,KAAyB,YAA7B,EAA2C;AACvCK,QAAAA,YAAY,CAACjI,cAAb,GAA8B,UAA9B;AACH,OAFD,MAGK,IAAI,KAAK4H,eAAL,KAAyB,UAA7B,EAAyC;AAC1CK,QAAAA,YAAY,CAACjI,cAAb,GAA8B,YAA9B;AACH;AACJ,KAXI,MAYA;AACDiI,MAAAA,YAAY,CAACjI,cAAb,GAA8B,KAAK4H,eAAnC;AACH;;AACDK,IAAAA,YAAY,CAAClI,UAAb,GAA0BoI,uBAAuB,GAAG,YAAH,GAAkB,KAAKR,WAAxE;AACH;AACD;AACJ;AACA;AACA;;;AACI1O,EAAAA,OAAO,GAAG;AACN,QAAI,KAAKsE,WAAL,IAAoB,CAAC,KAAK1O,WAA9B,EAA2C;AACvC;AACH;;AACD,UAAM4V,MAAM,GAAG,KAAK5V,WAAL,CAAiBuC,cAAjB,CAAgCpE,KAA/C;AACA,UAAMwb,MAAM,GAAG,KAAK3Z,WAAL,CAAiBuJ,WAAhC;AACA,UAAM6P,YAAY,GAAGO,MAAM,CAACxb,KAA5B;AACAwb,IAAAA,MAAM,CAACvb,SAAP,CAAiBU,MAAjB,CAAwByZ,YAAxB;AACAa,IAAAA,YAAY,CAACjI,cAAb,GACIiI,YAAY,CAAClI,UAAb,GACI0E,MAAM,CAAC4D,SAAP,GACI5D,MAAM,CAAC6D,YAAP,GACI7D,MAAM,CAAC2D,UAAP,GACI3D,MAAM,CAAC8D,WAAP,GACI9D,MAAM,CAACrF,QAAP,GACI,EAP5B;AAQA,SAAKvQ,WAAL,GAAmB,IAAnB;AACA,SAAK0O,WAAL,GAAmB,IAAnB;AACH;;AAzLwB;AA4L7B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMkL,sBAAN,CAA6B;AACzBxc,EAAAA,WAAW,CAACC,cAAD,EAAiBM,SAAjB,EAA4B8I,SAA5B,EAAuC6G,iBAAvC,EAA0D;AACjE,SAAKjQ,cAAL,GAAsBA,cAAtB;AACA,SAAKM,SAAL,GAAiBA,SAAjB;AACA,SAAK8I,SAAL,GAAiBA,SAAjB;AACA,SAAK6G,iBAAL,GAAyBA,iBAAzB;AACH;AACD;AACJ;AACA;;;AACIuM,EAAAA,MAAM,GAAG;AACL,WAAO,IAAIrB,sBAAJ,EAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIsB,EAAAA,mBAAmB,CAAC3V,MAAD,EAAS;AACxB,WAAO,IAAIiJ,iCAAJ,CAAsCjJ,MAAtC,EAA8C,KAAK9G,cAAnD,EAAmE,KAAKM,SAAxE,EAAmF,KAAK8I,SAAxF,EAAmG,KAAK6G,iBAAxG,CAAP;AACH;;AAnBwB;;AAqB7BsM,sBAAsB,CAAC5W,IAAvB;AAAA,mBAAmH4W,sBAAnH,EAliEwG1e,EAkiExG,UAA2JP,EAAE,CAACI,aAA9J,GAliEwGG,EAkiExG,UAAwLD,QAAxL,GAliEwGC,EAkiExG,UAA6Me,IAAI,CAACqL,QAAlN,GAliEwGpM,EAkiExG,UAAuOqM,gBAAvO;AAAA;;AACAqS,sBAAsB,CAAC1W,KAAvB,kBAniEwGhI,EAmiExG;AAAA,SAAuH0e,sBAAvH;AAAA,WAAuHA,sBAAvH;AAAA,cAA2J;AAA3J;;AACA;AAAA,qDApiEwG1e,EAoiExG,mBAA2F0e,sBAA3F,EAA+H,CAAC;AACpHzW,IAAAA,IAAI,EAAEhI,UAD8G;AAEpHiI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAF8G,GAAD,CAA/H,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAExI,EAAE,CAACI;AAAX,KAAD,EAA6B;AAAEoI,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC1FJ,QAAAA,IAAI,EAAE/H,MADoF;AAE1FgI,QAAAA,IAAI,EAAE,CAACnI,QAAD;AAFoF,OAAD;AAA/B,KAA7B,EAG3B;AAAEkI,MAAAA,IAAI,EAAElH,IAAI,CAACqL;AAAb,KAH2B,EAGF;AAAEnE,MAAAA,IAAI,EAAEoE;AAAR,KAHE,CAAP;AAGmC,GAN7E;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,IAAIwS,YAAY,GAAG,CAAnB,C,CACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,OAAN,CAAc;AACV5c,EAAAA,WAAW;AACX;AACA6c,EAAAA,gBAFW,EAEO3M,iBAFP,EAE0B4M,yBAF1B,EAEqDC,gBAFrD,EAEuE5R,mBAFvE,EAE4F6R,SAF5F,EAEuGxa,OAFvG,EAEgHjC,SAFhH,EAE2H0c,eAF3H,EAE4I7R,SAF5I,EAEuJC,uBAFvJ,EAEgL;AACvL,SAAKwR,gBAAL,GAAwBA,gBAAxB;AACA,SAAK3M,iBAAL,GAAyBA,iBAAzB;AACA,SAAK4M,yBAAL,GAAiCA,yBAAjC;AACA,SAAKC,gBAAL,GAAwBA,gBAAxB;AACA,SAAK5R,mBAAL,GAA2BA,mBAA3B;AACA,SAAK6R,SAAL,GAAiBA,SAAjB;AACA,SAAKxa,OAAL,GAAeA,OAAf;AACA,SAAKjC,SAAL,GAAiBA,SAAjB;AACA,SAAK0c,eAAL,GAAuBA,eAAvB;AACA,SAAK7R,SAAL,GAAiBA,SAAjB;AACA,SAAKC,uBAAL,GAA+BA,uBAA/B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACI6R,EAAAA,MAAM,CAACzX,MAAD,EAAS;AACX,UAAM0X,IAAI,GAAG,KAAKC,kBAAL,EAAb;;AACA,UAAMC,IAAI,GAAG,KAAKC,kBAAL,CAAwBH,IAAxB,CAAb;;AACA,UAAMI,YAAY,GAAG,KAAKC,mBAAL,CAAyBH,IAAzB,CAArB;;AACA,UAAMI,aAAa,GAAG,IAAIrX,aAAJ,CAAkBX,MAAlB,CAAtB;AACAgY,IAAAA,aAAa,CAACvP,SAAd,GAA0BuP,aAAa,CAACvP,SAAd,IAA2B,KAAK+O,eAAL,CAAqBpV,KAA1E;AACA,WAAO,IAAIkD,UAAJ,CAAewS,YAAf,EAA6BJ,IAA7B,EAAmCE,IAAnC,EAAyCI,aAAzC,EAAwD,KAAKjb,OAA7D,EAAsE,KAAK2I,mBAA3E,EAAgG,KAAK5K,SAArG,EAAgH,KAAK6K,SAArH,EAAgI,KAAKC,uBAArI,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACI8H,EAAAA,QAAQ,GAAG;AACP,WAAO,KAAK4J,gBAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACIO,EAAAA,kBAAkB,CAACH,IAAD,EAAO;AACrB,UAAME,IAAI,GAAG,KAAK9c,SAAL,CAAeqK,aAAf,CAA6B,KAA7B,CAAb;;AACAyS,IAAAA,IAAI,CAACK,EAAL,GAAW,eAAcf,YAAY,EAAG,EAAxC;AACAU,IAAAA,IAAI,CAACrc,SAAL,CAAeC,GAAf,CAAmB,kBAAnB;AACAkc,IAAAA,IAAI,CAACrS,WAAL,CAAiBuS,IAAjB;AACA,WAAOA,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACID,EAAAA,kBAAkB,GAAG;AACjB,UAAMD,IAAI,GAAG,KAAK5c,SAAL,CAAeqK,aAAf,CAA6B,KAA7B,CAAb;;AACA,SAAKsF,iBAAL,CAAuB7F,mBAAvB,GAA6CS,WAA7C,CAAyDqS,IAAzD;;AACA,WAAOA,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIK,EAAAA,mBAAmB,CAACH,IAAD,EAAO;AACtB;AACA;AACA,QAAI,CAAC,KAAKM,OAAV,EAAmB;AACf,WAAKA,OAAL,GAAe,KAAKX,SAAL,CAAeY,GAAf,CAAmBzf,cAAnB,CAAf;AACH;;AACD,WAAO,IAAIgB,eAAJ,CAAoBke,IAApB,EAA0B,KAAKP,yBAA/B,EAA0D,KAAKa,OAA/D,EAAwE,KAAKX,SAA7E,EAAwF,KAAKzc,SAA7F,CAAP;AACH;;AAtES;;AAwEdqc,OAAO,CAAChX,IAAR;AAAA,mBAAoGgX,OAApG,EAvoEwG9e,EAuoExG,UAA6HwH,qBAA7H,GAvoEwGxH,EAuoExG,UAA+JqM,gBAA/J,GAvoEwGrM,EAuoExG,UAA4LA,EAAE,CAAC+f,wBAA/L,GAvoEwG/f,EAuoExG,UAAoO0e,sBAApO,GAvoEwG1e,EAuoExG,UAAuQyK,yBAAvQ,GAvoEwGzK,EAuoExG,UAA6SA,EAAE,CAACggB,QAAhT,GAvoEwGhgB,EAuoExG,UAAqUA,EAAE,CAAC+H,MAAxU,GAvoEwG/H,EAuoExG,UAA2VD,QAA3V,GAvoEwGC,EAuoExG,UAAgXmB,EAAE,CAAC8e,cAAnX,GAvoEwGjgB,EAuoExG,UAA8YF,EAAE,CAACogB,QAAjZ,GAvoEwGlgB,EAuoExG,UAAsasL,6BAAta;AAAA;;AACAwT,OAAO,CAAC9W,KAAR,kBAxoEwGhI,EAwoExG;AAAA,SAAwG8e,OAAxG;AAAA,WAAwGA,OAAxG;AAAA;;AACA;AAAA,qDAzoEwG9e,EAyoExG,mBAA2F8e,OAA3F,EAAgH,CAAC;AACrG7W,IAAAA,IAAI,EAAEhI;AAD+F,GAAD,CAAhH,EAE4B,YAAY;AAAE,WAAO,CAAC;AAAEgI,MAAAA,IAAI,EAAET;AAAR,KAAD,EAAkC;AAAES,MAAAA,IAAI,EAAEoE;AAAR,KAAlC,EAA8D;AAAEpE,MAAAA,IAAI,EAAEjI,EAAE,CAAC+f;AAAX,KAA9D,EAAqG;AAAE9X,MAAAA,IAAI,EAAEyW;AAAR,KAArG,EAAuI;AAAEzW,MAAAA,IAAI,EAAEwC;AAAR,KAAvI,EAA4K;AAAExC,MAAAA,IAAI,EAAEjI,EAAE,CAACggB;AAAX,KAA5K,EAAmM;AAAE/X,MAAAA,IAAI,EAAEjI,EAAE,CAAC+H;AAAX,KAAnM,EAAwN;AAAEE,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AACrRJ,QAAAA,IAAI,EAAE/H,MAD+Q;AAErRgI,QAAAA,IAAI,EAAE,CAACnI,QAAD;AAF+Q,OAAD;AAA/B,KAAxN,EAG3B;AAAEkI,MAAAA,IAAI,EAAE9G,EAAE,CAAC8e;AAAX,KAH2B,EAGE;AAAEhY,MAAAA,IAAI,EAAEnI,EAAE,CAACogB;AAAX,KAHF,EAGyB;AAAEjY,MAAAA,IAAI,EAAEqD;AAAR,KAHzB,CAAP;AAG2E,GALrH;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM6U,mBAAmB,GAAG,CACxB;AACI9W,EAAAA,OAAO,EAAE,OADb;AAEIC,EAAAA,OAAO,EAAE,QAFb;AAGIC,EAAAA,QAAQ,EAAE,OAHd;AAIIC,EAAAA,QAAQ,EAAE;AAJd,CADwB,EAOxB;AACIH,EAAAA,OAAO,EAAE,OADb;AAEIC,EAAAA,OAAO,EAAE,KAFb;AAGIC,EAAAA,QAAQ,EAAE,OAHd;AAIIC,EAAAA,QAAQ,EAAE;AAJd,CAPwB,EAaxB;AACIH,EAAAA,OAAO,EAAE,KADb;AAEIC,EAAAA,OAAO,EAAE,KAFb;AAGIC,EAAAA,QAAQ,EAAE,KAHd;AAIIC,EAAAA,QAAQ,EAAE;AAJd,CAbwB,EAmBxB;AACIH,EAAAA,OAAO,EAAE,KADb;AAEIC,EAAAA,OAAO,EAAE,QAFb;AAGIC,EAAAA,QAAQ,EAAE,KAHd;AAIIC,EAAAA,QAAQ,EAAE;AAJd,CAnBwB,CAA5B;AA0BA;;AACA,MAAM4W,qCAAqC,GAAG,IAAI9f,cAAJ,CAAmB,uCAAnB,CAA9C;AACA;AACA;AACA;AACA;;AACA,MAAM+f,gBAAN,CAAuB;AACnBne,EAAAA,WAAW;AACX;AACAoe,EAAAA,UAFW,EAEC;AACR,SAAKA,UAAL,GAAkBA,UAAlB;AACH;;AALkB;;AAOvBD,gBAAgB,CAACvY,IAAjB;AAAA,mBAA6GuY,gBAA7G,EA/rEwGrgB,EA+rExG,mBAA+IA,EAAE,CAACI,UAAlJ;AAAA;;AACAigB,gBAAgB,CAACE,IAAjB,kBAhsEwGvgB,EAgsExG;AAAA,QAAiGqgB,gBAAjG;AAAA;AAAA;AAAA;;AACA;AAAA,qDAjsEwGrgB,EAisExG,mBAA2FqgB,gBAA3F,EAAyH,CAAC;AAC9GpY,IAAAA,IAAI,EAAE1H,SADwG;AAE9G2H,IAAAA,IAAI,EAAE,CAAC;AACCmP,MAAAA,QAAQ,EAAE,4DADX;AAECmJ,MAAAA,QAAQ,EAAE;AAFX,KAAD;AAFwG,GAAD,CAAzH,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAEvY,MAAAA,IAAI,EAAEjI,EAAE,CAACI;AAAX,KAAD,CAAP;AAAmC,GAN7E;AAAA;AAOA;AACA;AACA;AACA;;;AACA,MAAMqgB,mBAAN,CAA0B;AACtB;AACAve,EAAAA,WAAW,CAACwe,QAAD,EAAWC,WAAX,EAAwBC,gBAAxB,EAA0CC,qBAA1C,EAAiEC,IAAjE,EAAuE;AAC9E,SAAKJ,QAAL,GAAgBA,QAAhB;AACA,SAAKI,IAAL,GAAYA,IAAZ;AACA,SAAKC,YAAL,GAAoB,KAApB;AACA,SAAKC,aAAL,GAAqB,KAArB;AACA,SAAKxO,cAAL,GAAsB,KAAtB;AACA,SAAKyO,mBAAL,GAA2B,KAA3B;AACA,SAAKC,KAAL,GAAa,KAAb;AACA,SAAKC,qBAAL,GAA6B1f,YAAY,CAACoM,KAA1C;AACA,SAAKuT,mBAAL,GAA2B3f,YAAY,CAACoM,KAAxC;AACA,SAAKwT,mBAAL,GAA2B5f,YAAY,CAACoM,KAAxC;AACA,SAAKyT,qBAAL,GAA6B7f,YAAY,CAACoM,KAA1C;AACA;;AACA,SAAK0T,cAAL,GAAsB,CAAtB;AACA;;AACA,SAAKC,IAAL,GAAY,KAAZ;AACA;;AACA,SAAKC,YAAL,GAAoB,KAApB;AACA;;AACA,SAAKhS,aAAL,GAAqB,IAAIjP,YAAJ,EAArB;AACA;;AACA,SAAKkhB,cAAL,GAAsB,IAAIlhB,YAAJ,EAAtB;AACA;;AACA,SAAKkC,MAAL,GAAc,IAAIlC,YAAJ,EAAd;AACA;;AACA,SAAKyE,MAAL,GAAc,IAAIzE,YAAJ,EAAd;AACA;;AACA,SAAKmhB,cAAL,GAAsB,IAAInhB,YAAJ,EAAtB;AACA;;AACA,SAAKohB,mBAAL,GAA2B,IAAIphB,YAAJ,EAA3B;AACA,SAAKqhB,eAAL,GAAuB,IAAIvgB,cAAJ,CAAmBqf,WAAnB,EAAgCC,gBAAhC,CAAvB;AACA,SAAKkB,sBAAL,GAA8BjB,qBAA9B;AACA,SAAKtY,cAAL,GAAsB,KAAKuZ,sBAAL,EAAtB;AACH;AACD;;;AACW,MAAP3Y,OAAO,GAAG;AACV,WAAO,KAAK6J,QAAZ;AACH;;AACU,MAAP7J,OAAO,CAACA,OAAD,EAAU;AACjB,SAAK6J,QAAL,GAAgB7J,OAAhB;;AACA,QAAI,KAAK4Y,SAAT,EAAoB;AAChB,WAAKC,uBAAL,CAA6B,KAAKD,SAAlC;AACH;AACJ;AACD;;;AACW,MAAP3Y,OAAO,GAAG;AACV,WAAO,KAAK6J,QAAZ;AACH;;AACU,MAAP7J,OAAO,CAACA,OAAD,EAAU;AACjB,SAAK6J,QAAL,GAAgB7J,OAAhB;;AACA,QAAI,KAAK2Y,SAAT,EAAoB;AAChB,WAAKC,uBAAL,CAA6B,KAAKD,SAAlC;AACH;AACJ;AACD;;;AACe,MAAXtZ,WAAW,GAAG;AACd,WAAO,KAAKsY,YAAZ;AACH;;AACc,MAAXtY,WAAW,CAACsB,KAAD,EAAQ;AACnB,SAAKgX,YAAL,GAAoBjgB,qBAAqB,CAACiJ,KAAD,CAAzC;AACH;AACD;;;AACgB,MAAZkY,YAAY,GAAG;AACf,WAAO,KAAKjB,aAAZ;AACH;;AACe,MAAZiB,YAAY,CAAClY,KAAD,EAAQ;AACpB,SAAKiX,aAAL,GAAqBlgB,qBAAqB,CAACiJ,KAAD,CAA1C;AACH;AACD;;;AACsB,MAAlB0M,kBAAkB,GAAG;AACrB,WAAO,KAAKwK,mBAAZ;AACH;;AACqB,MAAlBxK,kBAAkB,CAAC1M,KAAD,EAAQ;AAC1B,SAAKkX,mBAAL,GAA2BngB,qBAAqB,CAACiJ,KAAD,CAAhD;AACH;AACD;;;AACiB,MAAb4M,aAAa,GAAG;AAChB,WAAO,KAAKnE,cAAZ;AACH;;AACgB,MAAbmE,aAAa,CAAC5M,KAAD,EAAQ;AACrB,SAAKyI,cAAL,GAAsB1R,qBAAqB,CAACiJ,KAAD,CAA3C;AACH;AACD;;;AACQ,MAAJK,IAAI,GAAG;AACP,WAAO,KAAK8W,KAAZ;AACH;;AACO,MAAJ9W,IAAI,CAACL,KAAD,EAAQ;AACZ,SAAKmX,KAAL,GAAapgB,qBAAqB,CAACiJ,KAAD,CAAlC;AACH;AACD;;;AACc,MAAV7E,UAAU,GAAG;AACb,WAAO,KAAKJ,WAAZ;AACH;AACD;;;AACO,MAAHqL,GAAG,GAAG;AACN,WAAO,KAAK2Q,IAAL,GAAY,KAAKA,IAAL,CAAU/W,KAAtB,GAA8B,KAArC;AACH;;AACDI,EAAAA,WAAW,GAAG;AACV,SAAKiX,mBAAL,CAAyBvb,WAAzB;;AACA,SAAKwb,mBAAL,CAAyBxb,WAAzB;;AACA,SAAKsb,qBAAL,CAA2Btb,WAA3B;;AACA,SAAKyb,qBAAL,CAA2Bzb,WAA3B;;AACA,QAAI,KAAKf,WAAT,EAAsB;AAClB,WAAKA,WAAL,CAAiBoK,OAAjB;AACH;AACJ;;AACDgT,EAAAA,WAAW,CAACC,OAAD,EAAU;AACjB,QAAI,KAAKJ,SAAT,EAAoB;AAChB,WAAKC,uBAAL,CAA6B,KAAKD,SAAlC;;AACA,WAAKjd,WAAL,CAAiBkL,UAAjB,CAA4B;AACxB3L,QAAAA,KAAK,EAAE,KAAKA,KADY;AAExBqM,QAAAA,QAAQ,EAAE,KAAKA,QAFS;AAGxBvM,QAAAA,MAAM,EAAE,KAAKA,MAHW;AAIxBwM,QAAAA,SAAS,EAAE,KAAKA;AAJQ,OAA5B;;AAMA,UAAIwR,OAAO,CAAC,QAAD,CAAP,IAAqB,KAAKX,IAA9B,EAAoC;AAChC,aAAKO,SAAL,CAAelS,KAAf;AACH;AACJ;;AACD,QAAIsS,OAAO,CAAC,MAAD,CAAX,EAAqB;AACjB,WAAKX,IAAL,GAAY,KAAKY,cAAL,EAAZ,GAAoC,KAAKC,cAAL,EAApC;AACH;AACJ;AACD;;;AACAC,EAAAA,cAAc,GAAG;AACb,QAAI,CAAC,KAAKjP,SAAN,IAAmB,CAAC,KAAKA,SAAL,CAAe7I,MAAvC,EAA+C;AAC3C,WAAK6I,SAAL,GAAiB8M,mBAAjB;AACH;;AACD,UAAMjb,UAAU,GAAI,KAAKJ,WAAL,GAAmB,KAAK4b,QAAL,CAActB,MAAd,CAAqB,KAAKmD,YAAL,EAArB,CAAvC;;AACA,SAAKnB,mBAAL,GAA2Blc,UAAU,CAACwK,WAAX,GAAyBlK,SAAzB,CAAmC,MAAM,KAAK9C,MAAL,CAAY8f,IAAZ,EAAzC,CAA3B;AACA,SAAKnB,mBAAL,GAA2Bnc,UAAU,CAACyK,WAAX,GAAyBnK,SAAzB,CAAmC,MAAM,KAAKP,MAAL,CAAYud,IAAZ,EAAzC,CAA3B;AACAtd,IAAAA,UAAU,CAAC8F,aAAX,GAA2BxF,SAA3B,CAAsCmF,KAAD,IAAW;AAC5C,WAAKgX,cAAL,CAAoB1W,IAApB,CAAyBN,KAAzB;;AACA,UAAIA,KAAK,CAAC8X,OAAN,KAAkB3gB,MAAlB,IAA4B,CAAC,KAAK2f,YAAlC,IAAkD,CAAC1f,cAAc,CAAC4I,KAAD,CAArE,EAA8E;AAC1EA,QAAAA,KAAK,CAAC+X,cAAN;;AACA,aAAKL,cAAL;AACH;AACJ,KAND;;AAOA,SAAKvd,WAAL,CAAiBiH,oBAAjB,GAAwCvG,SAAxC,CAAmDmF,KAAD,IAAW;AACzD,WAAKiX,mBAAL,CAAyB3W,IAAzB,CAA8BN,KAA9B;AACH,KAFD;AAGH;AACD;;;AACA4X,EAAAA,YAAY,GAAG;AACX,UAAMpU,gBAAgB,GAAI,KAAK4T,SAAL,GACtB,KAAK5T,gBAAL,IAAyB,KAAKwU,uBAAL,EAD7B;;AAEA,UAAMhD,aAAa,GAAG,IAAIrX,aAAJ,CAAkB;AACpC8H,MAAAA,SAAS,EAAE,KAAK0Q,IADoB;AAEpC3S,MAAAA,gBAFoC;AAGpC5F,MAAAA,cAAc,EAAE,KAAKA,cAHe;AAIpCE,MAAAA,WAAW,EAAE,KAAKA;AAJkB,KAAlB,CAAtB;;AAMA,QAAI,KAAKpE,KAAL,IAAc,KAAKA,KAAL,KAAe,CAAjC,EAAoC;AAChCsb,MAAAA,aAAa,CAACtb,KAAd,GAAsB,KAAKA,KAA3B;AACH;;AACD,QAAI,KAAKF,MAAL,IAAe,KAAKA,MAAL,KAAgB,CAAnC,EAAsC;AAClCwb,MAAAA,aAAa,CAACxb,MAAd,GAAuB,KAAKA,MAA5B;AACH;;AACD,QAAI,KAAKuM,QAAL,IAAiB,KAAKA,QAAL,KAAkB,CAAvC,EAA0C;AACtCiP,MAAAA,aAAa,CAACjP,QAAd,GAAyB,KAAKA,QAA9B;AACH;;AACD,QAAI,KAAKC,SAAL,IAAkB,KAAKA,SAAL,KAAmB,CAAzC,EAA4C;AACxCgP,MAAAA,aAAa,CAAChP,SAAd,GAA0B,KAAKA,SAA/B;AACH;;AACD,QAAI,KAAKjI,aAAT,EAAwB;AACpBiX,MAAAA,aAAa,CAACjX,aAAd,GAA8B,KAAKA,aAAnC;AACH;;AACD,QAAI,KAAKF,UAAT,EAAqB;AACjBmX,MAAAA,aAAa,CAACnX,UAAd,GAA2B,KAAKA,UAAhC;AACH;;AACD,WAAOmX,aAAP;AACH;AACD;;;AACAqC,EAAAA,uBAAuB,CAAC7T,gBAAD,EAAmB;AACtC,UAAMkF,SAAS,GAAG,KAAKA,SAAL,CAAesI,GAAf,CAAmBiH,eAAe,KAAK;AACrDvZ,MAAAA,OAAO,EAAEuZ,eAAe,CAACvZ,OAD4B;AAErDC,MAAAA,OAAO,EAAEsZ,eAAe,CAACtZ,OAF4B;AAGrDC,MAAAA,QAAQ,EAAEqZ,eAAe,CAACrZ,QAH2B;AAIrDC,MAAAA,QAAQ,EAAEoZ,eAAe,CAACpZ,QAJ2B;AAKrDL,MAAAA,OAAO,EAAEyZ,eAAe,CAACzZ,OAAhB,IAA2B,KAAKA,OALY;AAMrDC,MAAAA,OAAO,EAAEwZ,eAAe,CAACxZ,OAAhB,IAA2B,KAAKA,OANY;AAOrDZ,MAAAA,UAAU,EAAEoa,eAAe,CAACpa,UAAhB,IAA8BJ;AAPW,KAAL,CAAlC,CAAlB;AASA,WAAO+F,gBAAgB,CAClBiF,SADE,CACQ,KAAKyP,2CAAL,EADR,EAEFxM,aAFE,CAEYhD,SAFZ,EAGFmD,sBAHE,CAGqB,KAAKC,kBAH1B,EAIFG,QAJE,CAIO,KAAKxM,IAJZ,EAKFsM,iBALE,CAKgB,KAAKC,aALrB,EAMFL,kBANE,CAMiB,KAAKiL,cANtB,EAOFzK,kBAPE,CAOiB,KAAKmL,YAPtB,EAQF7K,qBARE,CAQoB,KAAK0L,uBARzB,CAAP;AASH;AACD;;;AACAH,EAAAA,uBAAuB,GAAG;AACtB,UAAM5S,QAAQ,GAAG,KAAK2Q,QAAL,CACZrL,QADY,GAEZuJ,mBAFY,CAEQ,KAAKiE,2CAAL,EAFR,CAAjB;;AAGA,SAAKb,uBAAL,CAA6BjS,QAA7B;;AACA,WAAOA,QAAP;AACH;;AACD8S,EAAAA,2CAA2C,GAAG;AAC1C,QAAI,KAAK5Z,MAAL,YAAuBoX,gBAA3B,EAA6C;AACzC,aAAO,KAAKpX,MAAL,CAAYqX,UAAnB;AACH,KAFD,MAGK;AACD,aAAO,KAAKrX,MAAZ;AACH;AACJ;AACD;;;AACAmZ,EAAAA,cAAc,GAAG;AACb,QAAI,CAAC,KAAKtd,WAAV,EAAuB;AACnB,WAAKwd,cAAL;AACH,KAFD,MAGK;AACD;AACA,WAAKxd,WAAL,CAAiB8K,SAAjB,GAA6BnH,WAA7B,GAA2C,KAAKA,WAAhD;AACH;;AACD,QAAI,CAAC,KAAK3D,WAAL,CAAiBC,WAAjB,EAAL,EAAqC;AACjC,WAAKD,WAAL,CAAiBpC,MAAjB,CAAwB,KAAKmf,eAA7B;AACH;;AACD,QAAI,KAAKpZ,WAAT,EAAsB;AAClB,WAAK0Y,qBAAL,GAA6B,KAAKrc,WAAL,CAAiB2K,aAAjB,GAAiCjK,SAAjC,CAA2CmF,KAAK,IAAI;AAC7E,aAAK8E,aAAL,CAAmB+S,IAAnB,CAAwB7X,KAAxB;AACH,OAF4B,CAA7B;AAGH,KAJD,MAKK;AACD,WAAKwW,qBAAL,CAA2Btb,WAA3B;AACH;;AACD,SAAKyb,qBAAL,CAA2Bzb,WAA3B,GAnBa,CAoBb;AACA;;;AACA,QAAI,KAAK6b,cAAL,CAAoB3W,SAApB,CAA8BP,MAA9B,GAAuC,CAA3C,EAA8C;AAC1C,WAAK8W,qBAAL,GAA6B,KAAKS,SAAL,CAAe5O,eAAf,CACxBrE,IADwB,CACnBjN,SAAS,CAAC,MAAM,KAAK6f,cAAL,CAAoB3W,SAApB,CAA8BP,MAA9B,GAAuC,CAA9C,CADU,EAExBhF,SAFwB,CAEd6P,QAAQ,IAAI;AACvB,aAAKqM,cAAL,CAAoBc,IAApB,CAAyBnN,QAAzB;;AACA,YAAI,KAAKqM,cAAL,CAAoB3W,SAApB,CAA8BP,MAA9B,KAAyC,CAA7C,EAAgD;AAC5C,eAAK8W,qBAAL,CAA2Bzb,WAA3B;AACH;AACJ,OAP4B,CAA7B;AAQH;AACJ;AACD;;;AACAwc,EAAAA,cAAc,GAAG;AACb,QAAI,KAAKvd,WAAT,EAAsB;AAClB,WAAKA,WAAL,CAAiBG,MAAjB;AACH;;AACD,SAAKkc,qBAAL,CAA2Btb,WAA3B;;AACA,SAAKyb,qBAAL,CAA2Bzb,WAA3B;AACH;;AA5PqB;;AA8P1B4a,mBAAmB,CAAC3Y,IAApB;AAAA,mBAAgH2Y,mBAAhH,EA18EwGzgB,EA08ExG,mBAAqJ8e,OAArJ,GA18EwG9e,EA08ExG,mBAAyKA,EAAE,CAAC+iB,WAA5K,GA18EwG/iB,EA08ExG,mBAAoMA,EAAE,CAACgjB,gBAAvM,GA18EwGhjB,EA08ExG,mBAAoOogB,qCAApO,GA18EwGpgB,EA08ExG,mBAAsRmB,EAAE,CAAC8e,cAAzR;AAAA;;AACAQ,mBAAmB,CAACF,IAApB,kBA38EwGvgB,EA28ExG;AAAA,QAAoGygB,mBAApG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aA38EwGzgB,EA28ExG;AAAA;;AACA;AAAA,qDA58EwGA,EA48ExG,mBAA2FygB,mBAA3F,EAA4H,CAAC;AACjHxY,IAAAA,IAAI,EAAE1H,SAD2G;AAEjH2H,IAAAA,IAAI,EAAE,CAAC;AACCmP,MAAAA,QAAQ,EAAE,qEADX;AAECmJ,MAAAA,QAAQ,EAAE;AAFX,KAAD;AAF2G,GAAD,CAA5H,EAM4B,YAAY;AAAE,WAAO,CAAC;AAAEvY,MAAAA,IAAI,EAAE6W;AAAR,KAAD,EAAoB;AAAE7W,MAAAA,IAAI,EAAEjI,EAAE,CAAC+iB;AAAX,KAApB,EAA8C;AAAE9a,MAAAA,IAAI,EAAEjI,EAAE,CAACgjB;AAAX,KAA9C,EAA6E;AAAE/a,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC1IJ,QAAAA,IAAI,EAAE/H,MADoI;AAE1IgI,QAAAA,IAAI,EAAE,CAACkY,qCAAD;AAFoI,OAAD;AAA/B,KAA7E,EAG3B;AAAEnY,MAAAA,IAAI,EAAE9G,EAAE,CAAC8e,cAAX;AAA2B5X,MAAAA,UAAU,EAAE,CAAC;AAC1CJ,QAAAA,IAAI,EAAE9H;AADoC,OAAD;AAAvC,KAH2B,CAAP;AAKlB,GAXxB,EAW0C;AAAE8I,IAAAA,MAAM,EAAE,CAAC;AACrChB,MAAAA,IAAI,EAAExH,KAD+B;AAErCyH,MAAAA,IAAI,EAAE,CAAC,2BAAD;AAF+B,KAAD,CAAV;AAG1BmL,IAAAA,SAAS,EAAE,CAAC;AACZpL,MAAAA,IAAI,EAAExH,KADM;AAEZyH,MAAAA,IAAI,EAAE,CAAC,8BAAD;AAFM,KAAD,CAHe;AAM1BiG,IAAAA,gBAAgB,EAAE,CAAC;AACnBlG,MAAAA,IAAI,EAAExH,KADa;AAEnByH,MAAAA,IAAI,EAAE,CAAC,qCAAD;AAFa,KAAD,CANQ;AAS1BiB,IAAAA,OAAO,EAAE,CAAC;AACVlB,MAAAA,IAAI,EAAExH,KADI;AAEVyH,MAAAA,IAAI,EAAE,CAAC,4BAAD;AAFI,KAAD,CATiB;AAY1BkB,IAAAA,OAAO,EAAE,CAAC;AACVnB,MAAAA,IAAI,EAAExH,KADI;AAEVyH,MAAAA,IAAI,EAAE,CAAC,4BAAD;AAFI,KAAD,CAZiB;AAe1B7D,IAAAA,KAAK,EAAE,CAAC;AACR4D,MAAAA,IAAI,EAAExH,KADE;AAERyH,MAAAA,IAAI,EAAE,CAAC,0BAAD;AAFE,KAAD,CAfmB;AAkB1B/D,IAAAA,MAAM,EAAE,CAAC;AACT8D,MAAAA,IAAI,EAAExH,KADG;AAETyH,MAAAA,IAAI,EAAE,CAAC,2BAAD;AAFG,KAAD,CAlBkB;AAqB1BwI,IAAAA,QAAQ,EAAE,CAAC;AACXzI,MAAAA,IAAI,EAAExH,KADK;AAEXyH,MAAAA,IAAI,EAAE,CAAC,6BAAD;AAFK,KAAD,CArBgB;AAwB1ByI,IAAAA,SAAS,EAAE,CAAC;AACZ1I,MAAAA,IAAI,EAAExH,KADM;AAEZyH,MAAAA,IAAI,EAAE,CAAC,8BAAD;AAFM,KAAD,CAxBe;AA2B1BQ,IAAAA,aAAa,EAAE,CAAC;AAChBT,MAAAA,IAAI,EAAExH,KADU;AAEhByH,MAAAA,IAAI,EAAE,CAAC,kCAAD;AAFU,KAAD,CA3BW;AA8B1BM,IAAAA,UAAU,EAAE,CAAC;AACbP,MAAAA,IAAI,EAAExH,KADO;AAEbyH,MAAAA,IAAI,EAAE,CAAC,+BAAD;AAFO,KAAD,CA9Bc;AAiC1BqZ,IAAAA,cAAc,EAAE,CAAC;AACjBtZ,MAAAA,IAAI,EAAExH,KADW;AAEjByH,MAAAA,IAAI,EAAE,CAAC,mCAAD;AAFW,KAAD,CAjCU;AAoC1BK,IAAAA,cAAc,EAAE,CAAC;AACjBN,MAAAA,IAAI,EAAExH,KADW;AAEjByH,MAAAA,IAAI,EAAE,CAAC,mCAAD;AAFW,KAAD,CApCU;AAuC1BsZ,IAAAA,IAAI,EAAE,CAAC;AACPvZ,MAAAA,IAAI,EAAExH,KADC;AAEPyH,MAAAA,IAAI,EAAE,CAAC,yBAAD;AAFC,KAAD,CAvCoB;AA0C1BuZ,IAAAA,YAAY,EAAE,CAAC;AACfxZ,MAAAA,IAAI,EAAExH,KADS;AAEfyH,MAAAA,IAAI,EAAE,CAAC,iCAAD;AAFS,KAAD,CA1CY;AA6C1B4a,IAAAA,uBAAuB,EAAE,CAAC;AAC1B7a,MAAAA,IAAI,EAAExH,KADoB;AAE1ByH,MAAAA,IAAI,EAAE,CAAC,sCAAD;AAFoB,KAAD,CA7CC;AAgD1BO,IAAAA,WAAW,EAAE,CAAC;AACdR,MAAAA,IAAI,EAAExH,KADQ;AAEdyH,MAAAA,IAAI,EAAE,CAAC,gCAAD;AAFQ,KAAD,CAhDa;AAmD1B+Z,IAAAA,YAAY,EAAE,CAAC;AACfha,MAAAA,IAAI,EAAExH,KADS;AAEfyH,MAAAA,IAAI,EAAE,CAAC,iCAAD;AAFS,KAAD,CAnDY;AAsD1BuO,IAAAA,kBAAkB,EAAE,CAAC;AACrBxO,MAAAA,IAAI,EAAExH,KADe;AAErByH,MAAAA,IAAI,EAAE,CAAC,uCAAD;AAFe,KAAD,CAtDM;AAyD1ByO,IAAAA,aAAa,EAAE,CAAC;AAChB1O,MAAAA,IAAI,EAAExH,KADU;AAEhByH,MAAAA,IAAI,EAAE,CAAC,kCAAD;AAFU,KAAD,CAzDW;AA4D1BkC,IAAAA,IAAI,EAAE,CAAC;AACPnC,MAAAA,IAAI,EAAExH,KADC;AAEPyH,MAAAA,IAAI,EAAE,CAAC,yBAAD;AAFC,KAAD,CA5DoB;AA+D1BuH,IAAAA,aAAa,EAAE,CAAC;AAChBxH,MAAAA,IAAI,EAAEvH;AADU,KAAD,CA/DW;AAiE1BghB,IAAAA,cAAc,EAAE,CAAC;AACjBzZ,MAAAA,IAAI,EAAEvH;AADW,KAAD,CAjEU;AAmE1BgC,IAAAA,MAAM,EAAE,CAAC;AACTuF,MAAAA,IAAI,EAAEvH;AADG,KAAD,CAnEkB;AAqE1BuE,IAAAA,MAAM,EAAE,CAAC;AACTgD,MAAAA,IAAI,EAAEvH;AADG,KAAD,CArEkB;AAuE1BihB,IAAAA,cAAc,EAAE,CAAC;AACjB1Z,MAAAA,IAAI,EAAEvH;AADW,KAAD,CAvEU;AAyE1BkhB,IAAAA,mBAAmB,EAAE,CAAC;AACtB3Z,MAAAA,IAAI,EAAEvH;AADgB,KAAD;AAzEK,GAX1C;AAAA;AAuFA;;;AACA,SAASuiB,sDAAT,CAAgE/Z,OAAhE,EAAyE;AACrE,SAAO,MAAMA,OAAO,CAAC6V,gBAAR,CAAyBlX,UAAzB,EAAb;AACH;AACD;;;AACA,MAAMqb,8CAA8C,GAAG;AACnDC,EAAAA,OAAO,EAAE/C,qCAD0C;AAEnDgD,EAAAA,IAAI,EAAE,CAACtE,OAAD,CAF6C;AAGnDuE,EAAAA,UAAU,EAAEJ;AAHuC,CAAvD;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMK,aAAN,CAAoB;;AAEpBA,aAAa,CAACxb,IAAd;AAAA,mBAA0Gwb,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBAxjFwGvjB,EAwjFxG;AAAA,QAA2GsjB;AAA3G;AACAA,aAAa,CAACE,IAAd,kBAzjFwGxjB,EAyjFxG;AAAA,aAAqI,CAAC8e,OAAD,EAAUoE,8CAAV,CAArI;AAAA,YAA0M,CAAC9hB,UAAD,EAAaG,YAAb,EAA2B7B,eAA3B,CAA1M,EAAuPA,eAAvP;AAAA;;AACA;AAAA,qDA1jFwGM,EA0jFxG,mBAA2FsjB,aAA3F,EAAsH,CAAC;AAC3Grb,IAAAA,IAAI,EAAEtH,QADqG;AAE3GuH,IAAAA,IAAI,EAAE,CAAC;AACCub,MAAAA,OAAO,EAAE,CAACriB,UAAD,EAAaG,YAAb,EAA2B7B,eAA3B,CADV;AAECgkB,MAAAA,OAAO,EAAE,CAACjD,mBAAD,EAAsBJ,gBAAtB,EAAwC3gB,eAAxC,CAFV;AAGCikB,MAAAA,YAAY,EAAE,CAAClD,mBAAD,EAAsBJ,gBAAtB,CAHf;AAICuD,MAAAA,SAAS,EAAE,CAAC9E,OAAD,EAAUoE,8CAAV;AAJZ,KAAD;AAFqG,GAAD,CAAtH;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMW,0BAAN,SAAyCxX,gBAAzC,CAA0D;AACtDnK,EAAAA,WAAW,CAACO,SAAD,EAAYqhB,QAAZ,EAAsB;AAC7B,UAAMrhB,SAAN,EAAiBqhB,QAAjB;AACH;;AACD3Z,EAAAA,WAAW,GAAG;AACV,UAAMA,WAAN;;AACA,QAAI,KAAK4Z,oBAAL,IAA6B,KAAKC,mBAAtC,EAA2D;AACvD,WAAKvhB,SAAL,CAAe4I,mBAAf,CAAmC,KAAK0Y,oBAAxC,EAA8D,KAAKC,mBAAnE;AACH;AACJ;;AACDxX,EAAAA,gBAAgB,GAAG;AACf,UAAMA,gBAAN;;AACA,SAAKyX,gCAAL;;AACA,SAAKC,4BAAL,CAAkC,MAAM,KAAKD,gCAAL,EAAxC;AACH;;AACDA,EAAAA,gCAAgC,GAAG;AAC/B,QAAI,CAAC,KAAK3X,iBAAV,EAA6B;AACzB;AACH;;AACD,UAAM6X,iBAAiB,GAAG,KAAKC,oBAAL,EAA1B;AACA,UAAM3F,MAAM,GAAG0F,iBAAiB,IAAI,KAAK1hB,SAAL,CAAea,IAAnD;AACAmb,IAAAA,MAAM,CAACzR,WAAP,CAAmB,KAAKV,iBAAxB;AACH;;AACD4X,EAAAA,4BAA4B,CAACG,EAAD,EAAK;AAC7B,UAAMC,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AACA,QAAID,SAAJ,EAAe;AACX,UAAI,KAAKN,mBAAT,EAA8B;AAC1B,aAAKvhB,SAAL,CAAe4I,mBAAf,CAAmCiZ,SAAnC,EAA8C,KAAKN,mBAAnD;AACH;;AACD,WAAKvhB,SAAL,CAAe2I,gBAAf,CAAgCkZ,SAAhC,EAA2CD,EAA3C;;AACA,WAAKL,mBAAL,GAA2BK,EAA3B;AACH;AACJ;;AACDE,EAAAA,aAAa,GAAG;AACZ,QAAI,CAAC,KAAKR,oBAAV,EAAgC;AAC5B,YAAMthB,SAAS,GAAG,KAAKA,SAAvB;;AACA,UAAIA,SAAS,CAAC+hB,iBAAd,EAAiC;AAC7B,aAAKT,oBAAL,GAA4B,kBAA5B;AACH,OAFD,MAGK,IAAIthB,SAAS,CAACgiB,uBAAd,EAAuC;AACxC,aAAKV,oBAAL,GAA4B,wBAA5B;AACH,OAFI,MAGA,IAAIthB,SAAS,CAACiiB,oBAAd,EAAoC;AACrC,aAAKX,oBAAL,GAA4B,qBAA5B;AACH,OAFI,MAGA,IAAIthB,SAAS,CAACkiB,mBAAd,EAAmC;AACpC,aAAKZ,oBAAL,GAA4B,oBAA5B;AACH;AACJ;;AACD,WAAO,KAAKA,oBAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACIK,EAAAA,oBAAoB,GAAG;AACnB,UAAM3hB,SAAS,GAAG,KAAKA,SAAvB;AACA,WAAQA,SAAS,CAAC0hB,iBAAV,IACJ1hB,SAAS,CAACmiB,uBADN,IAEJniB,SAAS,CAACoiB,oBAFN,IAGJpiB,SAAS,CAACqiB,mBAHN,IAIJ,IAJJ;AAKH;;AA9DqD;;AAgE1DjB,0BAA0B,CAAC/b,IAA3B;AAAA,mBAAuH+b,0BAAvH,EA1pFwG7jB,EA0pFxG,UAAmKD,QAAnK,GA1pFwGC,EA0pFxG,UAAwLe,IAAI,CAACqL,QAA7L;AAAA;;AACAyX,0BAA0B,CAAC7b,KAA3B,kBA3pFwGhI,EA2pFxG;AAAA,SAA2H6jB,0BAA3H;AAAA,WAA2HA,0BAA3H;AAAA,cAAmK;AAAnK;;AACA;AAAA,qDA5pFwG7jB,EA4pFxG,mBAA2F6jB,0BAA3F,EAAmI,CAAC;AACxH5b,IAAAA,IAAI,EAAEhI,UADkH;AAExHiI,IAAAA,IAAI,EAAE,CAAC;AAAEC,MAAAA,UAAU,EAAE;AAAd,KAAD;AAFkH,GAAD,CAAnI,EAG4B,YAAY;AAAE,WAAO,CAAC;AAAEF,MAAAA,IAAI,EAAEG,SAAR;AAAmBC,MAAAA,UAAU,EAAE,CAAC;AAC9DJ,QAAAA,IAAI,EAAE/H,MADwD;AAE9DgI,QAAAA,IAAI,EAAE,CAACnI,QAAD;AAFwD,OAAD;AAA/B,KAAD,EAG3B;AAAEkI,MAAAA,IAAI,EAAElH,IAAI,CAACqL;AAAb,KAH2B,CAAP;AAGO,GANjD;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASnK,mBAAT,EAA8Bwe,mBAA9B,EAAmDJ,gBAAnD,EAAqE7b,mBAArE,EAA0FkF,8BAA1F,EAA0HV,sBAA1H,EAAkJkJ,iCAAlJ,EAAqL2R,0BAArL,EAAiNvG,sBAAjN,EAAyOxX,kBAAzO,EAA6PgZ,OAA7P,EAAsQxW,aAAtQ,EAAqR+D,gBAArR,EAAuS5B,yBAAvS,EAAkU6Y,aAAlU,EAAiVhY,6BAAjV,EAAgXoT,sBAAhX,EAAwYzR,UAAxY,EAAoZjG,wBAApZ,EAA8aQ,qBAA9a,EAAqciC,mBAArc,EAA0dO,0BAA1d,EAAsfH,wBAAtf", "sourcesContent": ["import * as i1 from '@angular/cdk/scrolling';\nimport { ScrollingModule } from '@angular/cdk/scrolling';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler } from '@angular/cdk/scrolling';\nimport * as i6 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, ElementRef, ApplicationRef, InjectionToken, Directive, EventEmitter, Input, Output, NgModule } from '@angular/core';\nimport { coerceCssPixelValue, coerceArray, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { supportsScrollBehavior, _getEventTarget, _isTestEnvironment } from '@angular/cdk/platform';\nimport * as i5 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { DomPortalOutlet, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { Subject, Subscription, merge } from 'rxjs';\nimport { take, takeUntil, takeWhile } from 'rxjs/operators';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst scrollBehaviorSupported = supportsScrollBehavior();\n/**\n * Strategy that will prevent the user from scrolling while the overlay is visible.\n */\nclass BlockScrollStrategy {\n    constructor(_viewportRuler, document) {\n        this._viewportRuler = _viewportRuler;\n        this._previousHTMLStyles = { top: '', left: '' };\n        this._isEnabled = false;\n        this._document = document;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach() { }\n    /** Blocks page-level scroll while the attached overlay is open. */\n    enable() {\n        if (this._canBeEnabled()) {\n            const root = this._document.documentElement;\n            this._previousScrollPosition = this._viewportRuler.getViewportScrollPosition();\n            // Cache the previous inline styles in case the user had set them.\n            this._previousHTMLStyles.left = root.style.left || '';\n            this._previousHTMLStyles.top = root.style.top || '';\n            // Note: we're using the `html` node, instead of the `body`, because the `body` may\n            // have the user agent margin, whereas the `html` is guaranteed not to have one.\n            root.style.left = coerceCssPixelValue(-this._previousScrollPosition.left);\n            root.style.top = coerceCssPixelValue(-this._previousScrollPosition.top);\n            root.classList.add('cdk-global-scrollblock');\n            this._isEnabled = true;\n        }\n    }\n    /** Unblocks page-level scroll while the attached overlay is open. */\n    disable() {\n        if (this._isEnabled) {\n            const html = this._document.documentElement;\n            const body = this._document.body;\n            const htmlStyle = html.style;\n            const bodyStyle = body.style;\n            const previousHtmlScrollBehavior = htmlStyle.scrollBehavior || '';\n            const previousBodyScrollBehavior = bodyStyle.scrollBehavior || '';\n            this._isEnabled = false;\n            htmlStyle.left = this._previousHTMLStyles.left;\n            htmlStyle.top = this._previousHTMLStyles.top;\n            html.classList.remove('cdk-global-scrollblock');\n            // Disable user-defined smooth scrolling temporarily while we restore the scroll position.\n            // See https://developer.mozilla.org/en-US/docs/Web/CSS/scroll-behavior\n            // Note that we don't mutate the property if the browser doesn't support `scroll-behavior`,\n            // because it can throw off feature detections in `supportsScrollBehavior` which\n            // checks for `'scrollBehavior' in documentElement.style`.\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = bodyStyle.scrollBehavior = 'auto';\n            }\n            window.scroll(this._previousScrollPosition.left, this._previousScrollPosition.top);\n            if (scrollBehaviorSupported) {\n                htmlStyle.scrollBehavior = previousHtmlScrollBehavior;\n                bodyStyle.scrollBehavior = previousBodyScrollBehavior;\n            }\n        }\n    }\n    _canBeEnabled() {\n        // Since the scroll strategies can't be singletons, we have to use a global CSS class\n        // (`cdk-global-scrollblock`) to make sure that we don't try to disable global\n        // scrolling multiple times.\n        const html = this._document.documentElement;\n        if (html.classList.contains('cdk-global-scrollblock') || this._isEnabled) {\n            return false;\n        }\n        const body = this._document.body;\n        const viewport = this._viewportRuler.getViewportSize();\n        return body.scrollHeight > viewport.height || body.scrollWidth > viewport.width;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Returns an error to be thrown when attempting to attach an already-attached scroll strategy.\n */\nfunction getMatScrollStrategyAlreadyAttachedError() {\n    return Error(`Scroll strategy has already been attached.`);\n}\n\n/**\n * Strategy that will close the overlay as soon as the user starts scrolling.\n */\nclass CloseScrollStrategy {\n    constructor(_scrollDispatcher, _ngZone, _viewportRuler, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._config = _config;\n        this._scrollSubscription = null;\n        /** Detaches the overlay ref and disables the scroll strategy. */\n        this._detach = () => {\n            this.disable();\n            if (this._overlayRef.hasAttached()) {\n                this._ngZone.run(() => this._overlayRef.detach());\n            }\n        };\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables the closing of the attached overlay on scroll. */\n    enable() {\n        if (this._scrollSubscription) {\n            return;\n        }\n        const stream = this._scrollDispatcher.scrolled(0);\n        if (this._config && this._config.threshold && this._config.threshold > 1) {\n            this._initialScrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n            this._scrollSubscription = stream.subscribe(() => {\n                const scrollPosition = this._viewportRuler.getViewportScrollPosition().top;\n                if (Math.abs(scrollPosition - this._initialScrollPosition) > this._config.threshold) {\n                    this._detach();\n                }\n                else {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n        else {\n            this._scrollSubscription = stream.subscribe(this._detach);\n        }\n    }\n    /** Disables the closing the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Scroll strategy that doesn't do anything. */\nclass NoopScrollStrategy {\n    /** Does nothing, as this scroll strategy is a no-op. */\n    enable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    disable() { }\n    /** Does nothing, as this scroll strategy is a no-op. */\n    attach() { }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is scrolled out of view\n * @docs-private\n */\nfunction isElementScrolledOutsideView(element, scrollContainers) {\n    return scrollContainers.some(containerBounds => {\n        const outsideAbove = element.bottom < containerBounds.top;\n        const outsideBelow = element.top > containerBounds.bottom;\n        const outsideLeft = element.right < containerBounds.left;\n        const outsideRight = element.left > containerBounds.right;\n        return outsideAbove || outsideBelow || outsideLeft || outsideRight;\n    });\n}\n/**\n * Gets whether an element is clipped by any of its scrolling containers.\n * @param element Dimensions of the element (from getBoundingClientRect)\n * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)\n * @returns Whether the element is clipped\n * @docs-private\n */\nfunction isElementClippedByScrolling(element, scrollContainers) {\n    return scrollContainers.some(scrollContainerRect => {\n        const clippedAbove = element.top < scrollContainerRect.top;\n        const clippedBelow = element.bottom > scrollContainerRect.bottom;\n        const clippedLeft = element.left < scrollContainerRect.left;\n        const clippedRight = element.right > scrollContainerRect.right;\n        return clippedAbove || clippedBelow || clippedLeft || clippedRight;\n    });\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Strategy that will update the element position as the user is scrolling.\n */\nclass RepositionScrollStrategy {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, _config) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        this._config = _config;\n        this._scrollSubscription = null;\n    }\n    /** Attaches this scroll strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatScrollStrategyAlreadyAttachedError();\n        }\n        this._overlayRef = overlayRef;\n    }\n    /** Enables repositioning of the attached overlay on scroll. */\n    enable() {\n        if (!this._scrollSubscription) {\n            const throttle = this._config ? this._config.scrollThrottle : 0;\n            this._scrollSubscription = this._scrollDispatcher.scrolled(throttle).subscribe(() => {\n                this._overlayRef.updatePosition();\n                // TODO(crisbeto): make `close` on by default once all components can handle it.\n                if (this._config && this._config.autoClose) {\n                    const overlayRect = this._overlayRef.overlayElement.getBoundingClientRect();\n                    const { width, height } = this._viewportRuler.getViewportSize();\n                    // TODO(crisbeto): include all ancestor scroll containers here once\n                    // we have a way of exposing the trigger element to the scroll strategy.\n                    const parentRects = [{ width, height, bottom: height, right: width, top: 0, left: 0 }];\n                    if (isElementScrolledOutsideView(overlayRect, parentRects)) {\n                        this.disable();\n                        this._ngZone.run(() => this._overlayRef.detach());\n                    }\n                }\n            });\n        }\n    }\n    /** Disables repositioning of the attached overlay on scroll. */\n    disable() {\n        if (this._scrollSubscription) {\n            this._scrollSubscription.unsubscribe();\n            this._scrollSubscription = null;\n        }\n    }\n    detach() {\n        this.disable();\n        this._overlayRef = null;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Options for how an overlay will handle scrolling.\n *\n * Users can provide a custom value for `ScrollStrategyOptions` to replace the default\n * behaviors. This class primarily acts as a factory for ScrollStrategy instances.\n */\nclass ScrollStrategyOptions {\n    constructor(_scrollDispatcher, _viewportRuler, _ngZone, document) {\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewportRuler = _viewportRuler;\n        this._ngZone = _ngZone;\n        /** Do nothing on scroll. */\n        this.noop = () => new NoopScrollStrategy();\n        /**\n         * Close the overlay as soon as the user scrolls.\n         * @param config Configuration to be used inside the scroll strategy.\n         */\n        this.close = (config) => new CloseScrollStrategy(this._scrollDispatcher, this._ngZone, this._viewportRuler, config);\n        /** Block scrolling. */\n        this.block = () => new BlockScrollStrategy(this._viewportRuler, this._document);\n        /**\n         * Update the overlay's position on scroll.\n         * @param config Configuration to be used inside the scroll strategy.\n         * Allows debouncing the reposition calls.\n         */\n        this.reposition = (config) => new RepositionScrollStrategy(this._scrollDispatcher, this._viewportRuler, this._ngZone, config);\n        this._document = document;\n    }\n}\nScrollStrategyOptions.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollStrategyOptions, deps: [{ token: i1.ScrollDispatcher }, { token: i1.ViewportRuler }, { token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nScrollStrategyOptions.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollStrategyOptions, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: ScrollStrategyOptions, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.ScrollDispatcher }, { type: i1.ViewportRuler }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Initial configuration used when creating an overlay. */\nclass OverlayConfig {\n    constructor(config) {\n        /** Strategy to be used when handling scroll events while the overlay is open. */\n        this.scrollStrategy = new NoopScrollStrategy();\n        /** Custom class to add to the overlay pane. */\n        this.panelClass = '';\n        /** Whether the overlay has a backdrop. */\n        this.hasBackdrop = false;\n        /** Custom class to add to the backdrop */\n        this.backdropClass = 'cdk-overlay-dark-backdrop';\n        /**\n         * Whether the overlay should be disposed of when the user goes backwards/forwards in history.\n         * Note that this usually doesn't include clicking on links (unless the user is using\n         * the `HashLocationStrategy`).\n         */\n        this.disposeOnNavigation = false;\n        if (config) {\n            // Use `Iterable` instead of `Array` because TypeScript, as of 3.6.3,\n            // loses the array generic type in the `for of`. But we *also* have to use `Array` because\n            // typescript won't iterate over an `Iterable` unless you compile with `--downlevelIteration`\n            const configKeys = Object.keys(config);\n            for (const key of configKeys) {\n                if (config[key] !== undefined) {\n                    // TypeScript, as of version 3.5, sees the left-hand-side of this expression\n                    // as \"I don't know *which* key this is, so the only valid value is the intersection\n                    // of all the posible values.\" In this case, that happens to be `undefined`. TypeScript\n                    // is not smart enough to see that the right-hand-side is actually an access of the same\n                    // exact type with the same exact key, meaning that the value type must be identical.\n                    // So we use `any` to work around this.\n                    this[key] = config[key];\n                }\n            }\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The points of the origin element and the overlay element to connect. */\nclass ConnectionPositionPair {\n    constructor(origin, overlay, \n    /** Offset along the X axis. */\n    offsetX, \n    /** Offset along the Y axis. */\n    offsetY, \n    /** Class(es) to be applied to the panel while this position is active. */\n    panelClass) {\n        this.offsetX = offsetX;\n        this.offsetY = offsetY;\n        this.panelClass = panelClass;\n        this.originX = origin.originX;\n        this.originY = origin.originY;\n        this.overlayX = overlay.overlayX;\n        this.overlayY = overlay.overlayY;\n    }\n}\n/**\n * Set of properties regarding the position of the origin and overlay relative to the viewport\n * with respect to the containing Scrollable elements.\n *\n * The overlay and origin are clipped if any part of their bounding client rectangle exceeds the\n * bounds of any one of the strategy's Scrollable's bounding client rectangle.\n *\n * The overlay and origin are outside view if there is no overlap between their bounding client\n * rectangle and any one of the strategy's Scrollable's bounding client rectangle.\n *\n *       -----------                    -----------\n *       | outside |                    | clipped |\n *       |  view   |              --------------------------\n *       |         |              |     |         |        |\n *       ----------               |     -----------        |\n *  --------------------------    |                        |\n *  |                        |    |      Scrollable        |\n *  |                        |    |                        |\n *  |                        |     --------------------------\n *  |      Scrollable        |\n *  |                        |\n *  --------------------------\n *\n *  @docs-private\n */\nclass ScrollingVisibility {\n}\n/** The change event emitted by the strategy when a fallback position is used. */\nclass ConnectedOverlayPositionChange {\n    constructor(\n    /** The position used as a result of this change. */\n    connectionPair, \n    /** @docs-private */\n    scrollableViewProperties) {\n        this.connectionPair = connectionPair;\n        this.scrollableViewProperties = scrollableViewProperties;\n    }\n}\n/**\n * Validates whether a vertical position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateVerticalPosition(property, value) {\n    if (value !== 'top' && value !== 'bottom' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"top\", \"bottom\" or \"center\".`);\n    }\n}\n/**\n * Validates whether a horizontal position property matches the expected values.\n * @param property Name of the property being validated.\n * @param value Value of the property being validated.\n * @docs-private\n */\nfunction validateHorizontalPosition(property, value) {\n    if (value !== 'start' && value !== 'end' && value !== 'center') {\n        throw Error(`ConnectedPosition: Invalid ${property} \"${value}\". ` +\n            `Expected \"start\", \"end\" or \"center\".`);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass BaseOverlayDispatcher {\n    constructor(document) {\n        /** Currently attached overlays in the order they were attached. */\n        this._attachedOverlays = [];\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this.detach();\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        // Ensure that we don't get the same overlay multiple times.\n        this.remove(overlayRef);\n        this._attachedOverlays.push(overlayRef);\n    }\n    /** Remove an overlay from the list of attached overlay refs. */\n    remove(overlayRef) {\n        const index = this._attachedOverlays.indexOf(overlayRef);\n        if (index > -1) {\n            this._attachedOverlays.splice(index, 1);\n        }\n        // Remove the global listener once there are no more overlays.\n        if (this._attachedOverlays.length === 0) {\n            this.detach();\n        }\n    }\n}\nBaseOverlayDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: BaseOverlayDispatcher, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nBaseOverlayDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: BaseOverlayDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: BaseOverlayDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching keyboard events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayKeyboardDispatcher extends BaseOverlayDispatcher {\n    constructor(document, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._ngZone = _ngZone;\n        /** Keyboard event listener that will be attached to the body. */\n        this._keydownListener = (event) => {\n            const overlays = this._attachedOverlays;\n            for (let i = overlays.length - 1; i > -1; i--) {\n                // Dispatch the keydown event to the top overlay which has subscribers to its keydown events.\n                // We want to target the most recent overlay, rather than trying to match where the event came\n                // from, because some components might open an overlay, but keep focus on a trigger element\n                // (e.g. for select and autocomplete). We skip overlays without keydown event subscriptions,\n                // because we don't want overlays that don't handle keyboard events to block the ones below\n                // them that do.\n                if (overlays[i]._keydownEvents.observers.length > 0) {\n                    const keydownEvents = overlays[i]._keydownEvents;\n                    /** @breaking-change 14.0.0 _ngZone will be required. */\n                    if (this._ngZone) {\n                        this._ngZone.run(() => keydownEvents.next(event));\n                    }\n                    else {\n                        keydownEvents.next(event);\n                    }\n                    break;\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Lazily start dispatcher once first overlay is added\n        if (!this._isAttached) {\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._document.body.addEventListener('keydown', this._keydownListener));\n            }\n            else {\n                this._document.body.addEventListener('keydown', this._keydownListener);\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            this._document.body.removeEventListener('keydown', this._keydownListener);\n            this._isAttached = false;\n        }\n    }\n}\nOverlayKeyboardDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayKeyboardDispatcher, deps: [{ token: DOCUMENT }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayKeyboardDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayKeyboardDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayKeyboardDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Service for dispatching mouse click events that land on the body to appropriate overlay ref,\n * if any. It maintains a list of attached overlays to determine best suited overlay based\n * on event target and order of overlay opens.\n */\nclass OverlayOutsideClickDispatcher extends BaseOverlayDispatcher {\n    constructor(document, _platform, \n    /** @breaking-change 14.0.0 _ngZone will be required. */\n    _ngZone) {\n        super(document);\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._cursorStyleIsSet = false;\n        /** Store pointerdown event target to track origin of click. */\n        this._pointerDownListener = (event) => {\n            this._pointerDownEventTarget = _getEventTarget(event);\n        };\n        /** Click event listener that will be attached to the body propagate phase. */\n        this._clickListener = (event) => {\n            const target = _getEventTarget(event);\n            // In case of a click event, we want to check the origin of the click\n            // (e.g. in case where a user starts a click inside the overlay and\n            // releases the click outside of it).\n            // This is done by using the event target of the preceding pointerdown event.\n            // Every click event caused by a pointer device has a preceding pointerdown\n            // event, unless the click was programmatically triggered (e.g. in a unit test).\n            const origin = event.type === 'click' && this._pointerDownEventTarget\n                ? this._pointerDownEventTarget\n                : target;\n            // Reset the stored pointerdown event target, to avoid having it interfere\n            // in subsequent events.\n            this._pointerDownEventTarget = null;\n            // We copy the array because the original may be modified asynchronously if the\n            // outsidePointerEvents listener decides to detach overlays resulting in index errors inside\n            // the for loop.\n            const overlays = this._attachedOverlays.slice();\n            // Dispatch the mouse event to the top overlay which has subscribers to its mouse events.\n            // We want to target all overlays for which the click could be considered as outside click.\n            // As soon as we reach an overlay for which the click is not outside click we break off\n            // the loop.\n            for (let i = overlays.length - 1; i > -1; i--) {\n                const overlayRef = overlays[i];\n                if (overlayRef._outsidePointerEvents.observers.length < 1 || !overlayRef.hasAttached()) {\n                    continue;\n                }\n                // If it's a click inside the overlay, just break - we should do nothing\n                // If it's an outside click (both origin and target of the click) dispatch the mouse event,\n                // and proceed with the next overlay\n                if (overlayRef.overlayElement.contains(target) ||\n                    overlayRef.overlayElement.contains(origin)) {\n                    break;\n                }\n                const outsidePointerEvents = overlayRef._outsidePointerEvents;\n                /** @breaking-change 14.0.0 _ngZone will be required. */\n                if (this._ngZone) {\n                    this._ngZone.run(() => outsidePointerEvents.next(event));\n                }\n                else {\n                    outsidePointerEvents.next(event);\n                }\n            }\n        };\n    }\n    /** Add a new overlay to the list of attached overlay refs. */\n    add(overlayRef) {\n        super.add(overlayRef);\n        // Safari on iOS does not generate click events for non-interactive\n        // elements. However, we want to receive a click for any element outside\n        // the overlay. We can force a \"clickable\" state by setting\n        // `cursor: pointer` on the document body. See:\n        // https://developer.mozilla.org/en-US/docs/Web/API/Element/click_event#Safari_Mobile\n        // https://developer.apple.com/library/archive/documentation/AppleApplications/Reference/SafariWebContent/HandlingEvents/HandlingEvents.html\n        if (!this._isAttached) {\n            const body = this._document.body;\n            /** @breaking-change 14.0.0 _ngZone will be required. */\n            if (this._ngZone) {\n                this._ngZone.runOutsideAngular(() => this._addEventListeners(body));\n            }\n            else {\n                this._addEventListeners(body);\n            }\n            // click event is not fired on iOS. To make element \"clickable\" we are\n            // setting the cursor to pointer\n            if (this._platform.IOS && !this._cursorStyleIsSet) {\n                this._cursorOriginalValue = body.style.cursor;\n                body.style.cursor = 'pointer';\n                this._cursorStyleIsSet = true;\n            }\n            this._isAttached = true;\n        }\n    }\n    /** Detaches the global keyboard event listener. */\n    detach() {\n        if (this._isAttached) {\n            const body = this._document.body;\n            body.removeEventListener('pointerdown', this._pointerDownListener, true);\n            body.removeEventListener('click', this._clickListener, true);\n            body.removeEventListener('auxclick', this._clickListener, true);\n            body.removeEventListener('contextmenu', this._clickListener, true);\n            if (this._platform.IOS && this._cursorStyleIsSet) {\n                body.style.cursor = this._cursorOriginalValue;\n                this._cursorStyleIsSet = false;\n            }\n            this._isAttached = false;\n        }\n    }\n    _addEventListeners(body) {\n        body.addEventListener('pointerdown', this._pointerDownListener, true);\n        body.addEventListener('click', this._clickListener, true);\n        body.addEventListener('auxclick', this._clickListener, true);\n        body.addEventListener('contextmenu', this._clickListener, true);\n    }\n}\nOverlayOutsideClickDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }, { token: i0.NgZone, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayOutsideClickDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayOutsideClickDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: i0.NgZone, decorators: [{\n                    type: Optional\n                }] }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Container inside which all overlays will render. */\nclass OverlayContainer {\n    constructor(document, _platform) {\n        this._platform = _platform;\n        this._document = document;\n    }\n    ngOnDestroy() {\n        this._containerElement?.remove();\n    }\n    /**\n     * This method returns the overlay container element. It will lazily\n     * create the element the first time it is called to facilitate using\n     * the container in non-browser environments.\n     * @returns the container element\n     */\n    getContainerElement() {\n        if (!this._containerElement) {\n            this._createContainer();\n        }\n        return this._containerElement;\n    }\n    /**\n     * Create the overlay container element, which is simply a div\n     * with the 'cdk-overlay-container' class on the document body.\n     */\n    _createContainer() {\n        const containerClass = 'cdk-overlay-container';\n        // TODO(crisbeto): remove the testing check once we have an overlay testing\n        // module or Angular starts tearing down the testing `NgModule`. See:\n        // https://github.com/angular/angular/issues/18831\n        if (this._platform.isBrowser || _isTestEnvironment()) {\n            const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n            // Remove any old containers from the opposite platform.\n            // This can happen when transitioning from the server to the client.\n            for (let i = 0; i < oppositePlatformContainers.length; i++) {\n                oppositePlatformContainers[i].remove();\n            }\n        }\n        const container = this._document.createElement('div');\n        container.classList.add(containerClass);\n        // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n        // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n        // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n        // To mitigate the problem we made it so that only containers from a different platform are\n        // cleared, but the side-effect was that people started depending on the overly-aggressive\n        // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n        // module which does the cleanup, we try to detect that we're in a test environment and we\n        // always clear the container. See #17006.\n        // TODO(crisbeto): remove the test environment check once we have an overlay testing module.\n        if (_isTestEnvironment()) {\n            container.setAttribute('platform', 'test');\n        }\n        else if (!this._platform.isBrowser) {\n            container.setAttribute('platform', 'server');\n        }\n        this._document.body.appendChild(container);\n        this._containerElement = container;\n    }\n}\nOverlayContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayContainer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayContainer, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Reference to an overlay that has been created with the Overlay service.\n * Used to manipulate or dispose of said overlay.\n */\nclass OverlayRef {\n    constructor(_portalOutlet, _host, _pane, _config, _ngZone, _keyboardDispatcher, _document, _location, _outsideClickDispatcher) {\n        this._portalOutlet = _portalOutlet;\n        this._host = _host;\n        this._pane = _pane;\n        this._config = _config;\n        this._ngZone = _ngZone;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._document = _document;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n        this._backdropElement = null;\n        this._backdropClick = new Subject();\n        this._attachments = new Subject();\n        this._detachments = new Subject();\n        this._locationChanges = Subscription.EMPTY;\n        this._backdropClickHandler = (event) => this._backdropClick.next(event);\n        this._backdropTransitionendHandler = (event) => {\n            this._disposeBackdrop(event.target);\n        };\n        /** Stream of keydown events dispatched to this overlay. */\n        this._keydownEvents = new Subject();\n        /** Stream of mouse outside events dispatched to this overlay. */\n        this._outsidePointerEvents = new Subject();\n        if (_config.scrollStrategy) {\n            this._scrollStrategy = _config.scrollStrategy;\n            this._scrollStrategy.attach(this);\n        }\n        this._positionStrategy = _config.positionStrategy;\n    }\n    /** The overlay's HTML element */\n    get overlayElement() {\n        return this._pane;\n    }\n    /** The overlay's backdrop HTML element. */\n    get backdropElement() {\n        return this._backdropElement;\n    }\n    /**\n     * Wrapper around the panel element. Can be used for advanced\n     * positioning where a wrapper with specific styling is\n     * required around the overlay pane.\n     */\n    get hostElement() {\n        return this._host;\n    }\n    /**\n     * Attaches content, given via a Portal, to the overlay.\n     * If the overlay is configured to have a backdrop, it will be created.\n     *\n     * @param portal Portal instance to which to attach the overlay.\n     * @returns The portal attachment result.\n     */\n    attach(portal) {\n        // Insert the host into the DOM before attaching the portal, otherwise\n        // the animations module will skip animations on repeat attachments.\n        if (!this._host.parentElement && this._previousHostParent) {\n            this._previousHostParent.appendChild(this._host);\n        }\n        const attachResult = this._portalOutlet.attach(portal);\n        if (this._positionStrategy) {\n            this._positionStrategy.attach(this);\n        }\n        this._updateStackingOrder();\n        this._updateElementSize();\n        this._updateElementDirection();\n        if (this._scrollStrategy) {\n            this._scrollStrategy.enable();\n        }\n        // Update the position once the zone is stable so that the overlay will be fully rendered\n        // before attempting to position it, as the position may depend on the size of the rendered\n        // content.\n        this._ngZone.onStable.pipe(take(1)).subscribe(() => {\n            // The overlay could've been detached before the zone has stabilized.\n            if (this.hasAttached()) {\n                this.updatePosition();\n            }\n        });\n        // Enable pointer events for the overlay pane element.\n        this._togglePointerEvents(true);\n        if (this._config.hasBackdrop) {\n            this._attachBackdrop();\n        }\n        if (this._config.panelClass) {\n            this._toggleClasses(this._pane, this._config.panelClass, true);\n        }\n        // Only emit the `attachments` event once all other setup is done.\n        this._attachments.next();\n        // Track this overlay by the keyboard dispatcher\n        this._keyboardDispatcher.add(this);\n        if (this._config.disposeOnNavigation) {\n            this._locationChanges = this._location.subscribe(() => this.dispose());\n        }\n        this._outsideClickDispatcher.add(this);\n        return attachResult;\n    }\n    /**\n     * Detaches an overlay from a portal.\n     * @returns The portal detachment result.\n     */\n    detach() {\n        if (!this.hasAttached()) {\n            return;\n        }\n        this.detachBackdrop();\n        // When the overlay is detached, the pane element should disable pointer events.\n        // This is necessary because otherwise the pane element will cover the page and disable\n        // pointer events therefore. Depends on the position strategy and the applied pane boundaries.\n        this._togglePointerEvents(false);\n        if (this._positionStrategy && this._positionStrategy.detach) {\n            this._positionStrategy.detach();\n        }\n        if (this._scrollStrategy) {\n            this._scrollStrategy.disable();\n        }\n        const detachmentResult = this._portalOutlet.detach();\n        // Only emit after everything is detached.\n        this._detachments.next();\n        // Remove this overlay from keyboard dispatcher tracking.\n        this._keyboardDispatcher.remove(this);\n        // Keeping the host element in the DOM can cause scroll jank, because it still gets\n        // rendered, even though it's transparent and unclickable which is why we remove it.\n        this._detachContentWhenStable();\n        this._locationChanges.unsubscribe();\n        this._outsideClickDispatcher.remove(this);\n        return detachmentResult;\n    }\n    /** Cleans up the overlay from the DOM. */\n    dispose() {\n        const isAttached = this.hasAttached();\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._disposeScrollStrategy();\n        this._disposeBackdrop(this._backdropElement);\n        this._locationChanges.unsubscribe();\n        this._keyboardDispatcher.remove(this);\n        this._portalOutlet.dispose();\n        this._attachments.complete();\n        this._backdropClick.complete();\n        this._keydownEvents.complete();\n        this._outsidePointerEvents.complete();\n        this._outsideClickDispatcher.remove(this);\n        this._host?.remove();\n        this._previousHostParent = this._pane = this._host = null;\n        if (isAttached) {\n            this._detachments.next();\n        }\n        this._detachments.complete();\n    }\n    /** Whether the overlay has attached content. */\n    hasAttached() {\n        return this._portalOutlet.hasAttached();\n    }\n    /** Gets an observable that emits when the backdrop has been clicked. */\n    backdropClick() {\n        return this._backdropClick;\n    }\n    /** Gets an observable that emits when the overlay has been attached. */\n    attachments() {\n        return this._attachments;\n    }\n    /** Gets an observable that emits when the overlay has been detached. */\n    detachments() {\n        return this._detachments;\n    }\n    /** Gets an observable of keydown events targeted to this overlay. */\n    keydownEvents() {\n        return this._keydownEvents;\n    }\n    /** Gets an observable of pointer events targeted outside this overlay. */\n    outsidePointerEvents() {\n        return this._outsidePointerEvents;\n    }\n    /** Gets the current overlay configuration, which is immutable. */\n    getConfig() {\n        return this._config;\n    }\n    /** Updates the position of the overlay based on the position strategy. */\n    updatePosition() {\n        if (this._positionStrategy) {\n            this._positionStrategy.apply();\n        }\n    }\n    /** Switches to a new position strategy and updates the overlay position. */\n    updatePositionStrategy(strategy) {\n        if (strategy === this._positionStrategy) {\n            return;\n        }\n        if (this._positionStrategy) {\n            this._positionStrategy.dispose();\n        }\n        this._positionStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            this.updatePosition();\n        }\n    }\n    /** Update the size properties of the overlay. */\n    updateSize(sizeConfig) {\n        this._config = { ...this._config, ...sizeConfig };\n        this._updateElementSize();\n    }\n    /** Sets the LTR/RTL direction for the overlay. */\n    setDirection(dir) {\n        this._config = { ...this._config, direction: dir };\n        this._updateElementDirection();\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, true);\n        }\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        if (this._pane) {\n            this._toggleClasses(this._pane, classes, false);\n        }\n    }\n    /**\n     * Returns the layout direction of the overlay panel.\n     */\n    getDirection() {\n        const direction = this._config.direction;\n        if (!direction) {\n            return 'ltr';\n        }\n        return typeof direction === 'string' ? direction : direction.value;\n    }\n    /** Switches to a new scroll strategy. */\n    updateScrollStrategy(strategy) {\n        if (strategy === this._scrollStrategy) {\n            return;\n        }\n        this._disposeScrollStrategy();\n        this._scrollStrategy = strategy;\n        if (this.hasAttached()) {\n            strategy.attach(this);\n            strategy.enable();\n        }\n    }\n    /** Updates the text direction of the overlay panel. */\n    _updateElementDirection() {\n        this._host.setAttribute('dir', this.getDirection());\n    }\n    /** Updates the size of the overlay element based on the overlay config. */\n    _updateElementSize() {\n        if (!this._pane) {\n            return;\n        }\n        const style = this._pane.style;\n        style.width = coerceCssPixelValue(this._config.width);\n        style.height = coerceCssPixelValue(this._config.height);\n        style.minWidth = coerceCssPixelValue(this._config.minWidth);\n        style.minHeight = coerceCssPixelValue(this._config.minHeight);\n        style.maxWidth = coerceCssPixelValue(this._config.maxWidth);\n        style.maxHeight = coerceCssPixelValue(this._config.maxHeight);\n    }\n    /** Toggles the pointer events for the overlay pane element. */\n    _togglePointerEvents(enablePointer) {\n        this._pane.style.pointerEvents = enablePointer ? '' : 'none';\n    }\n    /** Attaches a backdrop for this overlay. */\n    _attachBackdrop() {\n        const showingClass = 'cdk-overlay-backdrop-showing';\n        this._backdropElement = this._document.createElement('div');\n        this._backdropElement.classList.add('cdk-overlay-backdrop');\n        if (this._config.backdropClass) {\n            this._toggleClasses(this._backdropElement, this._config.backdropClass, true);\n        }\n        // Insert the backdrop before the pane in the DOM order,\n        // in order to handle stacked overlays properly.\n        this._host.parentElement.insertBefore(this._backdropElement, this._host);\n        // Forward backdrop clicks such that the consumer of the overlay can perform whatever\n        // action desired when such a click occurs (usually closing the overlay).\n        this._backdropElement.addEventListener('click', this._backdropClickHandler);\n        // Add class to fade-in the backdrop after one frame.\n        if (typeof requestAnimationFrame !== 'undefined') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => {\n                    if (this._backdropElement) {\n                        this._backdropElement.classList.add(showingClass);\n                    }\n                });\n            });\n        }\n        else {\n            this._backdropElement.classList.add(showingClass);\n        }\n    }\n    /**\n     * Updates the stacking order of the element, moving it to the top if necessary.\n     * This is required in cases where one overlay was detached, while another one,\n     * that should be behind it, was destroyed. The next time both of them are opened,\n     * the stacking will be wrong, because the detached element's pane will still be\n     * in its original DOM position.\n     */\n    _updateStackingOrder() {\n        if (this._host.nextSibling) {\n            this._host.parentNode.appendChild(this._host);\n        }\n    }\n    /** Detaches the backdrop (if any) associated with the overlay. */\n    detachBackdrop() {\n        const backdropToDetach = this._backdropElement;\n        if (!backdropToDetach) {\n            return;\n        }\n        backdropToDetach.classList.remove('cdk-overlay-backdrop-showing');\n        this._ngZone.runOutsideAngular(() => {\n            backdropToDetach.addEventListener('transitionend', this._backdropTransitionendHandler);\n        });\n        // If the backdrop doesn't have a transition, the `transitionend` event won't fire.\n        // In this case we make it unclickable and we try to remove it after a delay.\n        backdropToDetach.style.pointerEvents = 'none';\n        // Run this outside the Angular zone because there's nothing that Angular cares about.\n        // If it were to run inside the Angular zone, every test that used Overlay would have to be\n        // either async or fakeAsync.\n        this._backdropTimeout = this._ngZone.runOutsideAngular(() => setTimeout(() => {\n            this._disposeBackdrop(backdropToDetach);\n        }, 500));\n    }\n    /** Toggles a single CSS class or an array of classes on an element. */\n    _toggleClasses(element, cssClasses, isAdd) {\n        const classes = coerceArray(cssClasses || []).filter(c => !!c);\n        if (classes.length) {\n            isAdd ? element.classList.add(...classes) : element.classList.remove(...classes);\n        }\n    }\n    /** Detaches the overlay content next time the zone stabilizes. */\n    _detachContentWhenStable() {\n        // Normally we wouldn't have to explicitly run this outside the `NgZone`, however\n        // if the consumer is using `zone-patch-rxjs`, the `Subscription.unsubscribe` call will\n        // be patched to run inside the zone, which will throw us into an infinite loop.\n        this._ngZone.runOutsideAngular(() => {\n            // We can't remove the host here immediately, because the overlay pane's content\n            // might still be animating. This stream helps us avoid interrupting the animation\n            // by waiting for the pane to become empty.\n            const subscription = this._ngZone.onStable\n                .pipe(takeUntil(merge(this._attachments, this._detachments)))\n                .subscribe(() => {\n                // Needs a couple of checks for the pane and host, because\n                // they may have been removed by the time the zone stabilizes.\n                if (!this._pane || !this._host || this._pane.children.length === 0) {\n                    if (this._pane && this._config.panelClass) {\n                        this._toggleClasses(this._pane, this._config.panelClass, false);\n                    }\n                    if (this._host && this._host.parentElement) {\n                        this._previousHostParent = this._host.parentElement;\n                        this._host.remove();\n                    }\n                    subscription.unsubscribe();\n                }\n            });\n        });\n    }\n    /** Disposes of a scroll strategy. */\n    _disposeScrollStrategy() {\n        const scrollStrategy = this._scrollStrategy;\n        if (scrollStrategy) {\n            scrollStrategy.disable();\n            if (scrollStrategy.detach) {\n                scrollStrategy.detach();\n            }\n        }\n    }\n    /** Removes a backdrop element from the DOM. */\n    _disposeBackdrop(backdrop) {\n        if (backdrop) {\n            backdrop.removeEventListener('click', this._backdropClickHandler);\n            backdrop.removeEventListener('transitionend', this._backdropTransitionendHandler);\n            backdrop.remove();\n            // It is possible that a new portal has been attached to this overlay since we started\n            // removing the backdrop. If that is the case, only clear the backdrop reference if it\n            // is still the same instance that we started to remove.\n            if (this._backdropElement === backdrop) {\n                this._backdropElement = null;\n            }\n        }\n        if (this._backdropTimeout) {\n            clearTimeout(this._backdropTimeout);\n            this._backdropTimeout = undefined;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// TODO: refactor clipping detection into a separate thing (part of scrolling module)\n// TODO: doesn't handle both flexible width and height when it has to scroll along both axis.\n/** Class to be added to the overlay bounding box. */\nconst boundingBoxClass = 'cdk-overlay-connected-position-bounding-box';\n/** Regex used to split a string on its CSS units. */\nconst cssUnitPattern = /([A-Za-z%]+)$/;\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * implicit position relative some origin element. The relative position is defined in terms of\n * a point on the origin element that is connected to a point on the overlay element. For example,\n * a basic dropdown is connecting the bottom-left corner of the origin to the top-left corner\n * of the overlay.\n */\nclass FlexibleConnectedPositionStrategy {\n    constructor(connectedTo, _viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n        /** Last size used for the bounding box. Used to avoid resizing the overlay after open. */\n        this._lastBoundingBoxSize = { width: 0, height: 0 };\n        /** Whether the overlay was pushed in a previous positioning. */\n        this._isPushed = false;\n        /** Whether the overlay can be pushed on-screen on the initial open. */\n        this._canPush = true;\n        /** Whether the overlay can grow via flexible width/height after the initial open. */\n        this._growAfterOpen = false;\n        /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n        this._hasFlexibleDimensions = true;\n        /** Whether the overlay position is locked. */\n        this._positionLocked = false;\n        /** Amount of space that must be maintained between the overlay and the edge of the viewport. */\n        this._viewportMargin = 0;\n        /** The Scrollable containers used to check scrollable view properties on position change. */\n        this._scrollables = [];\n        /** Ordered list of preferred positions, from most to least desirable. */\n        this._preferredPositions = [];\n        /** Subject that emits whenever the position changes. */\n        this._positionChanges = new Subject();\n        /** Subscription to viewport size changes. */\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Default offset for the overlay along the x axis. */\n        this._offsetX = 0;\n        /** Default offset for the overlay along the y axis. */\n        this._offsetY = 0;\n        /** Keeps track of the CSS classes that the position strategy has applied on the overlay panel. */\n        this._appliedPanelClasses = [];\n        /** Observable sequence of position changes. */\n        this.positionChanges = this._positionChanges;\n        this.setOrigin(connectedTo);\n    }\n    /** Ordered list of preferred positions, from most to least desirable. */\n    get positions() {\n        return this._preferredPositions;\n    }\n    /** Attaches this position strategy to an overlay. */\n    attach(overlayRef) {\n        if (this._overlayRef &&\n            overlayRef !== this._overlayRef &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('This position strategy is already attached to an overlay');\n        }\n        this._validatePositions();\n        overlayRef.hostElement.classList.add(boundingBoxClass);\n        this._overlayRef = overlayRef;\n        this._boundingBox = overlayRef.hostElement;\n        this._pane = overlayRef.overlayElement;\n        this._isDisposed = false;\n        this._isInitialRender = true;\n        this._lastPosition = null;\n        this._resizeSubscription.unsubscribe();\n        this._resizeSubscription = this._viewportRuler.change().subscribe(() => {\n            // When the window is resized, we want to trigger the next reposition as if it\n            // was an initial render, in order for the strategy to pick a new optimal position,\n            // otherwise position locking will cause it to stay at the old one.\n            this._isInitialRender = true;\n            this.apply();\n        });\n    }\n    /**\n     * Updates the position of the overlay element, using whichever preferred position relative\n     * to the origin best fits on-screen.\n     *\n     * The selection of a position goes as follows:\n     *  - If any positions fit completely within the viewport as-is,\n     *      choose the first position that does so.\n     *  - If flexible dimensions are enabled and at least one satifies the given minimum width/height,\n     *      choose the position with the greatest available size modified by the positions' weight.\n     *  - If pushing is enabled, take the position that went off-screen the least and push it\n     *      on-screen.\n     *  - If none of the previous criteria were met, use the position that goes off-screen the least.\n     * @docs-private\n     */\n    apply() {\n        // We shouldn't do anything if the strategy was disposed or we're on the server.\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        // If the position has been applied already (e.g. when the overlay was opened) and the\n        // consumer opted into locking in the position, re-use the old position, in order to\n        // prevent the overlay from jumping around.\n        if (!this._isInitialRender && this._positionLocked && this._lastPosition) {\n            this.reapplyLastPosition();\n            return;\n        }\n        this._clearPanelClasses();\n        this._resetOverlayElementStyles();\n        this._resetBoundingBoxStyles();\n        // We need the bounding rects for the origin, the overlay and the container to determine how to position\n        // the overlay relative to the origin.\n        // We use the viewport rect to determine whether a position would go off-screen.\n        this._viewportRect = this._getNarrowedViewportRect();\n        this._originRect = this._getOriginRect();\n        this._overlayRect = this._pane.getBoundingClientRect();\n        this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n        const originRect = this._originRect;\n        const overlayRect = this._overlayRect;\n        const viewportRect = this._viewportRect;\n        const containerRect = this._containerRect;\n        // Positions where the overlay will fit with flexible dimensions.\n        const flexibleFits = [];\n        // Fallback if none of the preferred positions fit within the viewport.\n        let fallback;\n        // Go through each of the preferred positions looking for a good fit.\n        // If a good fit is found, it will be applied immediately.\n        for (let pos of this._preferredPositions) {\n            // Get the exact (x, y) coordinate for the point-of-origin on the origin element.\n            let originPoint = this._getOriginPoint(originRect, containerRect, pos);\n            // From that point-of-origin, get the exact (x, y) coordinate for the top-left corner of the\n            // overlay in this position. We use the top-left corner for calculations and later translate\n            // this into an appropriate (top, left, bottom, right) style.\n            let overlayPoint = this._getOverlayPoint(originPoint, overlayRect, pos);\n            // Calculate how well the overlay would fit into the viewport with this point.\n            let overlayFit = this._getOverlayFit(overlayPoint, overlayRect, viewportRect, pos);\n            // If the overlay, without any further work, fits into the viewport, use this position.\n            if (overlayFit.isCompletelyWithinViewport) {\n                this._isPushed = false;\n                this._applyPosition(pos, originPoint);\n                return;\n            }\n            // If the overlay has flexible dimensions, we can use this position\n            // so long as there's enough space for the minimum dimensions.\n            if (this._canFitWithFlexibleDimensions(overlayFit, overlayPoint, viewportRect)) {\n                // Save positions where the overlay will fit with flexible dimensions. We will use these\n                // if none of the positions fit *without* flexible dimensions.\n                flexibleFits.push({\n                    position: pos,\n                    origin: originPoint,\n                    overlayRect,\n                    boundingBoxRect: this._calculateBoundingBoxRect(originPoint, pos),\n                });\n                continue;\n            }\n            // If the current preferred position does not fit on the screen, remember the position\n            // if it has more visible area on-screen than we've seen and move onto the next preferred\n            // position.\n            if (!fallback || fallback.overlayFit.visibleArea < overlayFit.visibleArea) {\n                fallback = { overlayFit, overlayPoint, originPoint, position: pos, overlayRect };\n            }\n        }\n        // If there are any positions where the overlay would fit with flexible dimensions, choose the\n        // one that has the greatest area available modified by the position's weight\n        if (flexibleFits.length) {\n            let bestFit = null;\n            let bestScore = -1;\n            for (const fit of flexibleFits) {\n                const score = fit.boundingBoxRect.width * fit.boundingBoxRect.height * (fit.position.weight || 1);\n                if (score > bestScore) {\n                    bestScore = score;\n                    bestFit = fit;\n                }\n            }\n            this._isPushed = false;\n            this._applyPosition(bestFit.position, bestFit.origin);\n            return;\n        }\n        // When none of the preferred positions fit within the viewport, take the position\n        // that went off-screen the least and attempt to push it on-screen.\n        if (this._canPush) {\n            // TODO(jelbourn): after pushing, the opening \"direction\" of the overlay might not make sense.\n            this._isPushed = true;\n            this._applyPosition(fallback.position, fallback.originPoint);\n            return;\n        }\n        // All options for getting the overlay within the viewport have been exhausted, so go with the\n        // position that went off-screen the least.\n        this._applyPosition(fallback.position, fallback.originPoint);\n    }\n    detach() {\n        this._clearPanelClasses();\n        this._lastPosition = null;\n        this._previousPushAmount = null;\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Cleanup after the element gets destroyed. */\n    dispose() {\n        if (this._isDisposed) {\n            return;\n        }\n        // We can't use `_resetBoundingBoxStyles` here, because it resets\n        // some properties to zero, rather than removing them.\n        if (this._boundingBox) {\n            extendStyles(this._boundingBox.style, {\n                top: '',\n                left: '',\n                right: '',\n                bottom: '',\n                height: '',\n                width: '',\n                alignItems: '',\n                justifyContent: '',\n            });\n        }\n        if (this._pane) {\n            this._resetOverlayElementStyles();\n        }\n        if (this._overlayRef) {\n            this._overlayRef.hostElement.classList.remove(boundingBoxClass);\n        }\n        this.detach();\n        this._positionChanges.complete();\n        this._overlayRef = this._boundingBox = null;\n        this._isDisposed = true;\n    }\n    /**\n     * This re-aligns the overlay element with the trigger in its last calculated position,\n     * even if a position higher in the \"preferred positions\" list would now fit. This\n     * allows one to re-align the panel without changing the orientation of the panel.\n     */\n    reapplyLastPosition() {\n        if (this._isDisposed || !this._platform.isBrowser) {\n            return;\n        }\n        const lastPosition = this._lastPosition;\n        if (lastPosition) {\n            this._originRect = this._getOriginRect();\n            this._overlayRect = this._pane.getBoundingClientRect();\n            this._viewportRect = this._getNarrowedViewportRect();\n            this._containerRect = this._overlayContainer.getContainerElement().getBoundingClientRect();\n            const originPoint = this._getOriginPoint(this._originRect, this._containerRect, lastPosition);\n            this._applyPosition(lastPosition, originPoint);\n        }\n        else {\n            this.apply();\n        }\n    }\n    /**\n     * Sets the list of Scrollable containers that host the origin element so that\n     * on reposition we can evaluate if it or the overlay has been clipped or outside view. Every\n     * Scrollable must be an ancestor element of the strategy's origin element.\n     */\n    withScrollableContainers(scrollables) {\n        this._scrollables = scrollables;\n        return this;\n    }\n    /**\n     * Adds new preferred positions.\n     * @param positions List of positions options for this overlay.\n     */\n    withPositions(positions) {\n        this._preferredPositions = positions;\n        // If the last calculated position object isn't part of the positions anymore, clear\n        // it in order to avoid it being picked up if the consumer tries to re-apply.\n        if (positions.indexOf(this._lastPosition) === -1) {\n            this._lastPosition = null;\n        }\n        this._validatePositions();\n        return this;\n    }\n    /**\n     * Sets a minimum distance the overlay may be positioned to the edge of the viewport.\n     * @param margin Required margin between the overlay and the viewport edge in pixels.\n     */\n    withViewportMargin(margin) {\n        this._viewportMargin = margin;\n        return this;\n    }\n    /** Sets whether the overlay's width and height can be constrained to fit within the viewport. */\n    withFlexibleDimensions(flexibleDimensions = true) {\n        this._hasFlexibleDimensions = flexibleDimensions;\n        return this;\n    }\n    /** Sets whether the overlay can grow after the initial open via flexible width/height. */\n    withGrowAfterOpen(growAfterOpen = true) {\n        this._growAfterOpen = growAfterOpen;\n        return this;\n    }\n    /** Sets whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    withPush(canPush = true) {\n        this._canPush = canPush;\n        return this;\n    }\n    /**\n     * Sets whether the overlay's position should be locked in after it is positioned\n     * initially. When an overlay is locked in, it won't attempt to reposition itself\n     * when the position is re-applied (e.g. when the user scrolls away).\n     * @param isLocked Whether the overlay should locked in.\n     */\n    withLockedPosition(isLocked = true) {\n        this._positionLocked = isLocked;\n        return this;\n    }\n    /**\n     * Sets the origin, relative to which to position the overlay.\n     * Using an element origin is useful for building components that need to be positioned\n     * relatively to a trigger (e.g. dropdown menus or tooltips), whereas using a point can be\n     * used for cases like contextual menus which open relative to the user's pointer.\n     * @param origin Reference to the new origin.\n     */\n    setOrigin(origin) {\n        this._origin = origin;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the x-axis.\n     * @param offset New offset in the X axis.\n     */\n    withDefaultOffsetX(offset) {\n        this._offsetX = offset;\n        return this;\n    }\n    /**\n     * Sets the default offset for the overlay's connection point on the y-axis.\n     * @param offset New offset in the Y axis.\n     */\n    withDefaultOffsetY(offset) {\n        this._offsetY = offset;\n        return this;\n    }\n    /**\n     * Configures that the position strategy should set a `transform-origin` on some elements\n     * inside the overlay, depending on the current position that is being applied. This is\n     * useful for the cases where the origin of an animation can change depending on the\n     * alignment of the overlay.\n     * @param selector CSS selector that will be used to find the target\n     *    elements onto which to set the transform origin.\n     */\n    withTransformOriginOn(selector) {\n        this._transformOriginSelector = selector;\n        return this;\n    }\n    /**\n     * Gets the (x, y) coordinate of a connection point on the origin based on a relative position.\n     */\n    _getOriginPoint(originRect, containerRect, pos) {\n        let x;\n        if (pos.originX == 'center') {\n            // Note: when centering we should always use the `left`\n            // offset, otherwise the position will be wrong in RTL.\n            x = originRect.left + originRect.width / 2;\n        }\n        else {\n            const startX = this._isRtl() ? originRect.right : originRect.left;\n            const endX = this._isRtl() ? originRect.left : originRect.right;\n            x = pos.originX == 'start' ? startX : endX;\n        }\n        // When zooming in Safari the container rectangle contains negative values for the position\n        // and we need to re-add them to the calculated coordinates.\n        if (containerRect.left < 0) {\n            x -= containerRect.left;\n        }\n        let y;\n        if (pos.originY == 'center') {\n            y = originRect.top + originRect.height / 2;\n        }\n        else {\n            y = pos.originY == 'top' ? originRect.top : originRect.bottom;\n        }\n        // Normally the containerRect's top value would be zero, however when the overlay is attached to an input\n        // (e.g. in an autocomplete), mobile browsers will shift everything in order to put the input in the middle\n        // of the screen and to make space for the virtual keyboard. We need to account for this offset,\n        // otherwise our positioning will be thrown off.\n        // Additionally, when zooming in Safari this fixes the vertical position.\n        if (containerRect.top < 0) {\n            y -= containerRect.top;\n        }\n        return { x, y };\n    }\n    /**\n     * Gets the (x, y) coordinate of the top-left corner of the overlay given a given position and\n     * origin point to which the overlay should be connected.\n     */\n    _getOverlayPoint(originPoint, overlayRect, pos) {\n        // Calculate the (overlayStartX, overlayStartY), the start of the\n        // potential overlay position relative to the origin point.\n        let overlayStartX;\n        if (pos.overlayX == 'center') {\n            overlayStartX = -overlayRect.width / 2;\n        }\n        else if (pos.overlayX === 'start') {\n            overlayStartX = this._isRtl() ? -overlayRect.width : 0;\n        }\n        else {\n            overlayStartX = this._isRtl() ? 0 : -overlayRect.width;\n        }\n        let overlayStartY;\n        if (pos.overlayY == 'center') {\n            overlayStartY = -overlayRect.height / 2;\n        }\n        else {\n            overlayStartY = pos.overlayY == 'top' ? 0 : -overlayRect.height;\n        }\n        // The (x, y) coordinates of the overlay.\n        return {\n            x: originPoint.x + overlayStartX,\n            y: originPoint.y + overlayStartY,\n        };\n    }\n    /** Gets how well an overlay at the given point will fit within the viewport. */\n    _getOverlayFit(point, rawOverlayRect, viewport, position) {\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        let { x, y } = point;\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        // Account for the offsets since they could push the overlay out of the viewport.\n        if (offsetX) {\n            x += offsetX;\n        }\n        if (offsetY) {\n            y += offsetY;\n        }\n        // How much the overlay would overflow at this position, on each side.\n        let leftOverflow = 0 - x;\n        let rightOverflow = x + overlay.width - viewport.width;\n        let topOverflow = 0 - y;\n        let bottomOverflow = y + overlay.height - viewport.height;\n        // Visible parts of the element on each axis.\n        let visibleWidth = this._subtractOverflows(overlay.width, leftOverflow, rightOverflow);\n        let visibleHeight = this._subtractOverflows(overlay.height, topOverflow, bottomOverflow);\n        let visibleArea = visibleWidth * visibleHeight;\n        return {\n            visibleArea,\n            isCompletelyWithinViewport: overlay.width * overlay.height === visibleArea,\n            fitsInViewportVertically: visibleHeight === overlay.height,\n            fitsInViewportHorizontally: visibleWidth == overlay.width,\n        };\n    }\n    /**\n     * Whether the overlay can fit within the viewport when it may resize either its width or height.\n     * @param fit How well the overlay fits in the viewport at some position.\n     * @param point The (x, y) coordinates of the overlay at some position.\n     * @param viewport The geometry of the viewport.\n     */\n    _canFitWithFlexibleDimensions(fit, point, viewport) {\n        if (this._hasFlexibleDimensions) {\n            const availableHeight = viewport.bottom - point.y;\n            const availableWidth = viewport.right - point.x;\n            const minHeight = getPixelValue(this._overlayRef.getConfig().minHeight);\n            const minWidth = getPixelValue(this._overlayRef.getConfig().minWidth);\n            const verticalFit = fit.fitsInViewportVertically || (minHeight != null && minHeight <= availableHeight);\n            const horizontalFit = fit.fitsInViewportHorizontally || (minWidth != null && minWidth <= availableWidth);\n            return verticalFit && horizontalFit;\n        }\n        return false;\n    }\n    /**\n     * Gets the point at which the overlay can be \"pushed\" on-screen. If the overlay is larger than\n     * the viewport, the top-left corner will be pushed on-screen (with overflow occuring on the\n     * right and bottom).\n     *\n     * @param start Starting point from which the overlay is pushed.\n     * @param rawOverlayRect Dimensions of the overlay.\n     * @param scrollPosition Current viewport scroll position.\n     * @returns The point at which to position the overlay after pushing. This is effectively a new\n     *     originPoint.\n     */\n    _pushOverlayOnScreen(start, rawOverlayRect, scrollPosition) {\n        // If the position is locked and we've pushed the overlay already, reuse the previous push\n        // amount, rather than pushing it again. If we were to continue pushing, the element would\n        // remain in the viewport, which goes against the expectations when position locking is enabled.\n        if (this._previousPushAmount && this._positionLocked) {\n            return {\n                x: start.x + this._previousPushAmount.x,\n                y: start.y + this._previousPushAmount.y,\n            };\n        }\n        // Round the overlay rect when comparing against the\n        // viewport, because the viewport is always rounded.\n        const overlay = getRoundedBoundingClientRect(rawOverlayRect);\n        const viewport = this._viewportRect;\n        // Determine how much the overlay goes outside the viewport on each\n        // side, which we'll use to decide which direction to push it.\n        const overflowRight = Math.max(start.x + overlay.width - viewport.width, 0);\n        const overflowBottom = Math.max(start.y + overlay.height - viewport.height, 0);\n        const overflowTop = Math.max(viewport.top - scrollPosition.top - start.y, 0);\n        const overflowLeft = Math.max(viewport.left - scrollPosition.left - start.x, 0);\n        // Amount by which to push the overlay in each axis such that it remains on-screen.\n        let pushX = 0;\n        let pushY = 0;\n        // If the overlay fits completely within the bounds of the viewport, push it from whichever\n        // direction is goes off-screen. Otherwise, push the top-left corner such that its in the\n        // viewport and allow for the trailing end of the overlay to go out of bounds.\n        if (overlay.width <= viewport.width) {\n            pushX = overflowLeft || -overflowRight;\n        }\n        else {\n            pushX = start.x < this._viewportMargin ? viewport.left - scrollPosition.left - start.x : 0;\n        }\n        if (overlay.height <= viewport.height) {\n            pushY = overflowTop || -overflowBottom;\n        }\n        else {\n            pushY = start.y < this._viewportMargin ? viewport.top - scrollPosition.top - start.y : 0;\n        }\n        this._previousPushAmount = { x: pushX, y: pushY };\n        return {\n            x: start.x + pushX,\n            y: start.y + pushY,\n        };\n    }\n    /**\n     * Applies a computed position to the overlay and emits a position change.\n     * @param position The position preference\n     * @param originPoint The point on the origin element where the overlay is connected.\n     */\n    _applyPosition(position, originPoint) {\n        this._setTransformOrigin(position);\n        this._setOverlayElementStyles(originPoint, position);\n        this._setBoundingBoxStyles(originPoint, position);\n        if (position.panelClass) {\n            this._addPanelClasses(position.panelClass);\n        }\n        // Save the last connected position in case the position needs to be re-calculated.\n        this._lastPosition = position;\n        // Notify that the position has been changed along with its change properties.\n        // We only emit if we've got any subscriptions, because the scroll visibility\n        // calculcations can be somewhat expensive.\n        if (this._positionChanges.observers.length) {\n            const scrollableViewProperties = this._getScrollVisibility();\n            const changeEvent = new ConnectedOverlayPositionChange(position, scrollableViewProperties);\n            this._positionChanges.next(changeEvent);\n        }\n        this._isInitialRender = false;\n    }\n    /** Sets the transform origin based on the configured selector and the passed-in position.  */\n    _setTransformOrigin(position) {\n        if (!this._transformOriginSelector) {\n            return;\n        }\n        const elements = this._boundingBox.querySelectorAll(this._transformOriginSelector);\n        let xOrigin;\n        let yOrigin = position.overlayY;\n        if (position.overlayX === 'center') {\n            xOrigin = 'center';\n        }\n        else if (this._isRtl()) {\n            xOrigin = position.overlayX === 'start' ? 'right' : 'left';\n        }\n        else {\n            xOrigin = position.overlayX === 'start' ? 'left' : 'right';\n        }\n        for (let i = 0; i < elements.length; i++) {\n            elements[i].style.transformOrigin = `${xOrigin} ${yOrigin}`;\n        }\n    }\n    /**\n     * Gets the position and size of the overlay's sizing container.\n     *\n     * This method does no measuring and applies no styles so that we can cheaply compute the\n     * bounds for all positions and choose the best fit based on these results.\n     */\n    _calculateBoundingBoxRect(origin, position) {\n        const viewport = this._viewportRect;\n        const isRtl = this._isRtl();\n        let height, top, bottom;\n        if (position.overlayY === 'top') {\n            // Overlay is opening \"downward\" and thus is bound by the bottom viewport edge.\n            top = origin.y;\n            height = viewport.height - top + this._viewportMargin;\n        }\n        else if (position.overlayY === 'bottom') {\n            // Overlay is opening \"upward\" and thus is bound by the top viewport edge. We need to add\n            // the viewport margin back in, because the viewport rect is narrowed down to remove the\n            // margin, whereas the `origin` position is calculated based on its `ClientRect`.\n            bottom = viewport.height - origin.y + this._viewportMargin * 2;\n            height = viewport.height - bottom + this._viewportMargin;\n        }\n        else {\n            // If neither top nor bottom, it means that the overlay is vertically centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.bottom - origin.y` and\n            // `origin.y - viewport.top`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.bottom - origin.y + viewport.top, origin.y);\n            const previousHeight = this._lastBoundingBoxSize.height;\n            height = smallestDistanceToViewportEdge * 2;\n            top = origin.y - smallestDistanceToViewportEdge;\n            if (height > previousHeight && !this._isInitialRender && !this._growAfterOpen) {\n                top = origin.y - previousHeight / 2;\n            }\n        }\n        // The overlay is opening 'right-ward' (the content flows to the right).\n        const isBoundedByRightViewportEdge = (position.overlayX === 'start' && !isRtl) || (position.overlayX === 'end' && isRtl);\n        // The overlay is opening 'left-ward' (the content flows to the left).\n        const isBoundedByLeftViewportEdge = (position.overlayX === 'end' && !isRtl) || (position.overlayX === 'start' && isRtl);\n        let width, left, right;\n        if (isBoundedByLeftViewportEdge) {\n            right = viewport.width - origin.x + this._viewportMargin;\n            width = origin.x - this._viewportMargin;\n        }\n        else if (isBoundedByRightViewportEdge) {\n            left = origin.x;\n            width = viewport.right - origin.x;\n        }\n        else {\n            // If neither start nor end, it means that the overlay is horizontally centered on the\n            // origin point. Note that we want the position relative to the viewport, rather than\n            // the page, which is why we don't use something like `viewport.right - origin.x` and\n            // `origin.x - viewport.left`.\n            const smallestDistanceToViewportEdge = Math.min(viewport.right - origin.x + viewport.left, origin.x);\n            const previousWidth = this._lastBoundingBoxSize.width;\n            width = smallestDistanceToViewportEdge * 2;\n            left = origin.x - smallestDistanceToViewportEdge;\n            if (width > previousWidth && !this._isInitialRender && !this._growAfterOpen) {\n                left = origin.x - previousWidth / 2;\n            }\n        }\n        return { top: top, left: left, bottom: bottom, right: right, width, height };\n    }\n    /**\n     * Sets the position and size of the overlay's sizing wrapper. The wrapper is positioned on the\n     * origin's connection point and stetches to the bounds of the viewport.\n     *\n     * @param origin The point on the origin element where the overlay is connected.\n     * @param position The position preference\n     */\n    _setBoundingBoxStyles(origin, position) {\n        const boundingBoxRect = this._calculateBoundingBoxRect(origin, position);\n        // It's weird if the overlay *grows* while scrolling, so we take the last size into account\n        // when applying a new size.\n        if (!this._isInitialRender && !this._growAfterOpen) {\n            boundingBoxRect.height = Math.min(boundingBoxRect.height, this._lastBoundingBoxSize.height);\n            boundingBoxRect.width = Math.min(boundingBoxRect.width, this._lastBoundingBoxSize.width);\n        }\n        const styles = {};\n        if (this._hasExactPosition()) {\n            styles.top = styles.left = '0';\n            styles.bottom = styles.right = styles.maxHeight = styles.maxWidth = '';\n            styles.width = styles.height = '100%';\n        }\n        else {\n            const maxHeight = this._overlayRef.getConfig().maxHeight;\n            const maxWidth = this._overlayRef.getConfig().maxWidth;\n            styles.height = coerceCssPixelValue(boundingBoxRect.height);\n            styles.top = coerceCssPixelValue(boundingBoxRect.top);\n            styles.bottom = coerceCssPixelValue(boundingBoxRect.bottom);\n            styles.width = coerceCssPixelValue(boundingBoxRect.width);\n            styles.left = coerceCssPixelValue(boundingBoxRect.left);\n            styles.right = coerceCssPixelValue(boundingBoxRect.right);\n            // Push the pane content towards the proper direction.\n            if (position.overlayX === 'center') {\n                styles.alignItems = 'center';\n            }\n            else {\n                styles.alignItems = position.overlayX === 'end' ? 'flex-end' : 'flex-start';\n            }\n            if (position.overlayY === 'center') {\n                styles.justifyContent = 'center';\n            }\n            else {\n                styles.justifyContent = position.overlayY === 'bottom' ? 'flex-end' : 'flex-start';\n            }\n            if (maxHeight) {\n                styles.maxHeight = coerceCssPixelValue(maxHeight);\n            }\n            if (maxWidth) {\n                styles.maxWidth = coerceCssPixelValue(maxWidth);\n            }\n        }\n        this._lastBoundingBoxSize = boundingBoxRect;\n        extendStyles(this._boundingBox.style, styles);\n    }\n    /** Resets the styles for the bounding box so that a new positioning can be computed. */\n    _resetBoundingBoxStyles() {\n        extendStyles(this._boundingBox.style, {\n            top: '0',\n            left: '0',\n            right: '0',\n            bottom: '0',\n            height: '',\n            width: '',\n            alignItems: '',\n            justifyContent: '',\n        });\n    }\n    /** Resets the styles for the overlay pane so that a new positioning can be computed. */\n    _resetOverlayElementStyles() {\n        extendStyles(this._pane.style, {\n            top: '',\n            left: '',\n            bottom: '',\n            right: '',\n            position: '',\n            transform: '',\n        });\n    }\n    /** Sets positioning styles to the overlay element. */\n    _setOverlayElementStyles(originPoint, position) {\n        const styles = {};\n        const hasExactPosition = this._hasExactPosition();\n        const hasFlexibleDimensions = this._hasFlexibleDimensions;\n        const config = this._overlayRef.getConfig();\n        if (hasExactPosition) {\n            const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n            extendStyles(styles, this._getExactOverlayY(position, originPoint, scrollPosition));\n            extendStyles(styles, this._getExactOverlayX(position, originPoint, scrollPosition));\n        }\n        else {\n            styles.position = 'static';\n        }\n        // Use a transform to apply the offsets. We do this because the `center` positions rely on\n        // being in the normal flex flow and setting a `top` / `left` at all will completely throw\n        // off the position. We also can't use margins, because they won't have an effect in some\n        // cases where the element doesn't have anything to \"push off of\". Finally, this works\n        // better both with flexible and non-flexible positioning.\n        let transformString = '';\n        let offsetX = this._getOffset(position, 'x');\n        let offsetY = this._getOffset(position, 'y');\n        if (offsetX) {\n            transformString += `translateX(${offsetX}px) `;\n        }\n        if (offsetY) {\n            transformString += `translateY(${offsetY}px)`;\n        }\n        styles.transform = transformString.trim();\n        // If a maxWidth or maxHeight is specified on the overlay, we remove them. We do this because\n        // we need these values to both be set to \"100%\" for the automatic flexible sizing to work.\n        // The maxHeight and maxWidth are set on the boundingBox in order to enforce the constraint.\n        // Note that this doesn't apply when we have an exact position, in which case we do want to\n        // apply them because they'll be cleared from the bounding box.\n        if (config.maxHeight) {\n            if (hasExactPosition) {\n                styles.maxHeight = coerceCssPixelValue(config.maxHeight);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxHeight = '';\n            }\n        }\n        if (config.maxWidth) {\n            if (hasExactPosition) {\n                styles.maxWidth = coerceCssPixelValue(config.maxWidth);\n            }\n            else if (hasFlexibleDimensions) {\n                styles.maxWidth = '';\n            }\n        }\n        extendStyles(this._pane.style, styles);\n    }\n    /** Gets the exact top/bottom for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayY(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the\n        // preferred position has changed since the last `apply`.\n        let styles = { top: '', bottom: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `top` or `bottom` based on whether the overlay wants to appear\n        // above or below the origin and the direction in which the element will expand.\n        if (position.overlayY === 'bottom') {\n            // When using `bottom`, we adjust the y position such that it is the distance\n            // from the bottom of the viewport rather than the top.\n            const documentHeight = this._document.documentElement.clientHeight;\n            styles.bottom = `${documentHeight - (overlayPoint.y + this._overlayRect.height)}px`;\n        }\n        else {\n            styles.top = coerceCssPixelValue(overlayPoint.y);\n        }\n        return styles;\n    }\n    /** Gets the exact left/right for the overlay when not using flexible sizing or when pushing. */\n    _getExactOverlayX(position, originPoint, scrollPosition) {\n        // Reset any existing styles. This is necessary in case the preferred position has\n        // changed since the last `apply`.\n        let styles = { left: '', right: '' };\n        let overlayPoint = this._getOverlayPoint(originPoint, this._overlayRect, position);\n        if (this._isPushed) {\n            overlayPoint = this._pushOverlayOnScreen(overlayPoint, this._overlayRect, scrollPosition);\n        }\n        // We want to set either `left` or `right` based on whether the overlay wants to appear \"before\"\n        // or \"after\" the origin, which determines the direction in which the element will expand.\n        // For the horizontal axis, the meaning of \"before\" and \"after\" change based on whether the\n        // page is in RTL or LTR.\n        let horizontalStyleProperty;\n        if (this._isRtl()) {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'left' : 'right';\n        }\n        else {\n            horizontalStyleProperty = position.overlayX === 'end' ? 'right' : 'left';\n        }\n        // When we're setting `right`, we adjust the x position such that it is the distance\n        // from the right edge of the viewport rather than the left edge.\n        if (horizontalStyleProperty === 'right') {\n            const documentWidth = this._document.documentElement.clientWidth;\n            styles.right = `${documentWidth - (overlayPoint.x + this._overlayRect.width)}px`;\n        }\n        else {\n            styles.left = coerceCssPixelValue(overlayPoint.x);\n        }\n        return styles;\n    }\n    /**\n     * Gets the view properties of the trigger and overlay, including whether they are clipped\n     * or completely outside the view of any of the strategy's scrollables.\n     */\n    _getScrollVisibility() {\n        // Note: needs fresh rects since the position could've changed.\n        const originBounds = this._getOriginRect();\n        const overlayBounds = this._pane.getBoundingClientRect();\n        // TODO(jelbourn): instead of needing all of the client rects for these scrolling containers\n        // every time, we should be able to use the scrollTop of the containers if the size of those\n        // containers hasn't changed.\n        const scrollContainerBounds = this._scrollables.map(scrollable => {\n            return scrollable.getElementRef().nativeElement.getBoundingClientRect();\n        });\n        return {\n            isOriginClipped: isElementClippedByScrolling(originBounds, scrollContainerBounds),\n            isOriginOutsideView: isElementScrolledOutsideView(originBounds, scrollContainerBounds),\n            isOverlayClipped: isElementClippedByScrolling(overlayBounds, scrollContainerBounds),\n            isOverlayOutsideView: isElementScrolledOutsideView(overlayBounds, scrollContainerBounds),\n        };\n    }\n    /** Subtracts the amount that an element is overflowing on an axis from its length. */\n    _subtractOverflows(length, ...overflows) {\n        return overflows.reduce((currentValue, currentOverflow) => {\n            return currentValue - Math.max(currentOverflow, 0);\n        }, length);\n    }\n    /** Narrows the given viewport rect by the current _viewportMargin. */\n    _getNarrowedViewportRect() {\n        // We recalculate the viewport rect here ourselves, rather than using the ViewportRuler,\n        // because we want to use the `clientWidth` and `clientHeight` as the base. The difference\n        // being that the client properties don't include the scrollbar, as opposed to `innerWidth`\n        // and `innerHeight` that do. This is necessary, because the overlay container uses\n        // 100% `width` and `height` which don't include the scrollbar either.\n        const width = this._document.documentElement.clientWidth;\n        const height = this._document.documentElement.clientHeight;\n        const scrollPosition = this._viewportRuler.getViewportScrollPosition();\n        return {\n            top: scrollPosition.top + this._viewportMargin,\n            left: scrollPosition.left + this._viewportMargin,\n            right: scrollPosition.left + width - this._viewportMargin,\n            bottom: scrollPosition.top + height - this._viewportMargin,\n            width: width - 2 * this._viewportMargin,\n            height: height - 2 * this._viewportMargin,\n        };\n    }\n    /** Whether the we're dealing with an RTL context */\n    _isRtl() {\n        return this._overlayRef.getDirection() === 'rtl';\n    }\n    /** Determines whether the overlay uses exact or flexible positioning. */\n    _hasExactPosition() {\n        return !this._hasFlexibleDimensions || this._isPushed;\n    }\n    /** Retrieves the offset of a position along the x or y axis. */\n    _getOffset(position, axis) {\n        if (axis === 'x') {\n            // We don't do something like `position['offset' + axis]` in\n            // order to avoid breking minifiers that rename properties.\n            return position.offsetX == null ? this._offsetX : position.offsetX;\n        }\n        return position.offsetY == null ? this._offsetY : position.offsetY;\n    }\n    /** Validates that the current position match the expected values. */\n    _validatePositions() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (!this._preferredPositions.length) {\n                throw Error('FlexibleConnectedPositionStrategy: At least one position is required.');\n            }\n            // TODO(crisbeto): remove these once Angular's template type\n            // checking is advanced enough to catch these cases.\n            this._preferredPositions.forEach(pair => {\n                validateHorizontalPosition('originX', pair.originX);\n                validateVerticalPosition('originY', pair.originY);\n                validateHorizontalPosition('overlayX', pair.overlayX);\n                validateVerticalPosition('overlayY', pair.overlayY);\n            });\n        }\n    }\n    /** Adds a single CSS class or an array of classes on the overlay panel. */\n    _addPanelClasses(cssClasses) {\n        if (this._pane) {\n            coerceArray(cssClasses).forEach(cssClass => {\n                if (cssClass !== '' && this._appliedPanelClasses.indexOf(cssClass) === -1) {\n                    this._appliedPanelClasses.push(cssClass);\n                    this._pane.classList.add(cssClass);\n                }\n            });\n        }\n    }\n    /** Clears the classes that the position strategy has applied from the overlay panel. */\n    _clearPanelClasses() {\n        if (this._pane) {\n            this._appliedPanelClasses.forEach(cssClass => {\n                this._pane.classList.remove(cssClass);\n            });\n            this._appliedPanelClasses = [];\n        }\n    }\n    /** Returns the ClientRect of the current origin. */\n    _getOriginRect() {\n        const origin = this._origin;\n        if (origin instanceof ElementRef) {\n            return origin.nativeElement.getBoundingClientRect();\n        }\n        // Check for Element so SVG elements are also supported.\n        if (origin instanceof Element) {\n            return origin.getBoundingClientRect();\n        }\n        const width = origin.width || 0;\n        const height = origin.height || 0;\n        // If the origin is a point, return a client rect as if it was a 0x0 element at the point.\n        return {\n            top: origin.y,\n            bottom: origin.y + height,\n            left: origin.x,\n            right: origin.x + width,\n            height,\n            width,\n        };\n    }\n}\n/** Shallow-extends a stylesheet object with another stylesheet object. */\nfunction extendStyles(destination, source) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            destination[key] = source[key];\n        }\n    }\n    return destination;\n}\n/**\n * Extracts the pixel value as a number from a value, if it's a number\n * or a CSS pixel string (e.g. `1337px`). Otherwise returns null.\n */\nfunction getPixelValue(input) {\n    if (typeof input !== 'number' && input != null) {\n        const [value, units] = input.split(cssUnitPattern);\n        return !units || units === 'px' ? parseFloat(value) : null;\n    }\n    return input || null;\n}\n/**\n * Gets a version of an element's bounding `ClientRect` where all the values are rounded down to\n * the nearest pixel. This allows us to account for the cases where there may be sub-pixel\n * deviations in the `ClientRect` returned by the browser (e.g. when zoomed in with a percentage\n * size, see #21350).\n */\nfunction getRoundedBoundingClientRect(clientRect) {\n    return {\n        top: Math.floor(clientRect.top),\n        right: Math.floor(clientRect.right),\n        bottom: Math.floor(clientRect.bottom),\n        left: Math.floor(clientRect.left),\n        width: Math.floor(clientRect.width),\n        height: Math.floor(clientRect.height),\n    };\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Class to be added to the overlay pane wrapper. */\nconst wrapperClass = 'cdk-global-overlay-wrapper';\n/**\n * A strategy for positioning overlays. Using this strategy, an overlay is given an\n * explicit position relative to the browser's viewport. We use flexbox, instead of\n * transforms, in order to avoid issues with subpixel rendering which can cause the\n * element to become blurry.\n */\nclass GlobalPositionStrategy {\n    constructor() {\n        this._cssPosition = 'static';\n        this._topOffset = '';\n        this._bottomOffset = '';\n        this._leftOffset = '';\n        this._rightOffset = '';\n        this._alignItems = '';\n        this._justifyContent = '';\n        this._width = '';\n        this._height = '';\n    }\n    attach(overlayRef) {\n        const config = overlayRef.getConfig();\n        this._overlayRef = overlayRef;\n        if (this._width && !config.width) {\n            overlayRef.updateSize({ width: this._width });\n        }\n        if (this._height && !config.height) {\n            overlayRef.updateSize({ height: this._height });\n        }\n        overlayRef.hostElement.classList.add(wrapperClass);\n        this._isDisposed = false;\n    }\n    /**\n     * Sets the top position of the overlay. Clears any previously set vertical position.\n     * @param value New top offset.\n     */\n    top(value = '') {\n        this._bottomOffset = '';\n        this._topOffset = value;\n        this._alignItems = 'flex-start';\n        return this;\n    }\n    /**\n     * Sets the left position of the overlay. Clears any previously set horizontal position.\n     * @param value New left offset.\n     */\n    left(value = '') {\n        this._rightOffset = '';\n        this._leftOffset = value;\n        this._justifyContent = 'flex-start';\n        return this;\n    }\n    /**\n     * Sets the bottom position of the overlay. Clears any previously set vertical position.\n     * @param value New bottom offset.\n     */\n    bottom(value = '') {\n        this._topOffset = '';\n        this._bottomOffset = value;\n        this._alignItems = 'flex-end';\n        return this;\n    }\n    /**\n     * Sets the right position of the overlay. Clears any previously set horizontal position.\n     * @param value New right offset.\n     */\n    right(value = '') {\n        this._leftOffset = '';\n        this._rightOffset = value;\n        this._justifyContent = 'flex-end';\n        return this;\n    }\n    /**\n     * Sets the overlay width and clears any previously set width.\n     * @param value New width for the overlay\n     * @deprecated Pass the `width` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    width(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ width: value });\n        }\n        else {\n            this._width = value;\n        }\n        return this;\n    }\n    /**\n     * Sets the overlay height and clears any previously set height.\n     * @param value New height for the overlay\n     * @deprecated Pass the `height` through the `OverlayConfig`.\n     * @breaking-change 8.0.0\n     */\n    height(value = '') {\n        if (this._overlayRef) {\n            this._overlayRef.updateSize({ height: value });\n        }\n        else {\n            this._height = value;\n        }\n        return this;\n    }\n    /**\n     * Centers the overlay horizontally with an optional offset.\n     * Clears any previously set horizontal position.\n     *\n     * @param offset Overlay offset from the horizontal center.\n     */\n    centerHorizontally(offset = '') {\n        this.left(offset);\n        this._justifyContent = 'center';\n        return this;\n    }\n    /**\n     * Centers the overlay vertically with an optional offset.\n     * Clears any previously set vertical position.\n     *\n     * @param offset Overlay offset from the vertical center.\n     */\n    centerVertically(offset = '') {\n        this.top(offset);\n        this._alignItems = 'center';\n        return this;\n    }\n    /**\n     * Apply the position to the element.\n     * @docs-private\n     */\n    apply() {\n        // Since the overlay ref applies the strategy asynchronously, it could\n        // have been disposed before it ends up being applied. If that is the\n        // case, we shouldn't do anything.\n        if (!this._overlayRef || !this._overlayRef.hasAttached()) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parentStyles = this._overlayRef.hostElement.style;\n        const config = this._overlayRef.getConfig();\n        const { width, height, maxWidth, maxHeight } = config;\n        const shouldBeFlushHorizontally = (width === '100%' || width === '100vw') &&\n            (!maxWidth || maxWidth === '100%' || maxWidth === '100vw');\n        const shouldBeFlushVertically = (height === '100%' || height === '100vh') &&\n            (!maxHeight || maxHeight === '100%' || maxHeight === '100vh');\n        styles.position = this._cssPosition;\n        styles.marginLeft = shouldBeFlushHorizontally ? '0' : this._leftOffset;\n        styles.marginTop = shouldBeFlushVertically ? '0' : this._topOffset;\n        styles.marginBottom = this._bottomOffset;\n        styles.marginRight = this._rightOffset;\n        if (shouldBeFlushHorizontally) {\n            parentStyles.justifyContent = 'flex-start';\n        }\n        else if (this._justifyContent === 'center') {\n            parentStyles.justifyContent = 'center';\n        }\n        else if (this._overlayRef.getConfig().direction === 'rtl') {\n            // In RTL the browser will invert `flex-start` and `flex-end` automatically, but we\n            // don't want that because our positioning is explicitly `left` and `right`, hence\n            // why we do another inversion to ensure that the overlay stays in the same position.\n            // TODO: reconsider this if we add `start` and `end` methods.\n            if (this._justifyContent === 'flex-start') {\n                parentStyles.justifyContent = 'flex-end';\n            }\n            else if (this._justifyContent === 'flex-end') {\n                parentStyles.justifyContent = 'flex-start';\n            }\n        }\n        else {\n            parentStyles.justifyContent = this._justifyContent;\n        }\n        parentStyles.alignItems = shouldBeFlushVertically ? 'flex-start' : this._alignItems;\n    }\n    /**\n     * Cleans up the DOM changes from the position strategy.\n     * @docs-private\n     */\n    dispose() {\n        if (this._isDisposed || !this._overlayRef) {\n            return;\n        }\n        const styles = this._overlayRef.overlayElement.style;\n        const parent = this._overlayRef.hostElement;\n        const parentStyles = parent.style;\n        parent.classList.remove(wrapperClass);\n        parentStyles.justifyContent =\n            parentStyles.alignItems =\n                styles.marginTop =\n                    styles.marginBottom =\n                        styles.marginLeft =\n                            styles.marginRight =\n                                styles.position =\n                                    '';\n        this._overlayRef = null;\n        this._isDisposed = true;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Builder for overlay position strategy. */\nclass OverlayPositionBuilder {\n    constructor(_viewportRuler, _document, _platform, _overlayContainer) {\n        this._viewportRuler = _viewportRuler;\n        this._document = _document;\n        this._platform = _platform;\n        this._overlayContainer = _overlayContainer;\n    }\n    /**\n     * Creates a global position strategy.\n     */\n    global() {\n        return new GlobalPositionStrategy();\n    }\n    /**\n     * Creates a flexible position strategy.\n     * @param origin Origin relative to which to position the overlay.\n     */\n    flexibleConnectedTo(origin) {\n        return new FlexibleConnectedPositionStrategy(origin, this._viewportRuler, this._document, this._platform, this._overlayContainer);\n    }\n}\nOverlayPositionBuilder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayPositionBuilder, deps: [{ token: i1.ViewportRuler }, { token: DOCUMENT }, { token: i1$1.Platform }, { token: OverlayContainer }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlayPositionBuilder.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayPositionBuilder, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayPositionBuilder, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i1.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }, { type: OverlayContainer }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Next overlay unique ID. */\nlet nextUniqueId = 0;\n// Note that Overlay is *not* scoped to the app root because of the ComponentFactoryResolver\n// which needs to be different depending on where OverlayModule is imported.\n/**\n * Service to create Overlays. Overlays are dynamically added pieces of floating UI, meant to be\n * used as a low-level building block for other components. Dialogs, tooltips, menus,\n * selects, etc. can all be built using overlays. The service should primarily be used by authors\n * of re-usable components rather than developers building end-user applications.\n *\n * An overlay *is* a PortalOutlet, so any kind of Portal can be loaded into one.\n */\nclass Overlay {\n    constructor(\n    /** Scrolling strategies that can be used when creating an overlay. */\n    scrollStrategies, _overlayContainer, _componentFactoryResolver, _positionBuilder, _keyboardDispatcher, _injector, _ngZone, _document, _directionality, _location, _outsideClickDispatcher) {\n        this.scrollStrategies = scrollStrategies;\n        this._overlayContainer = _overlayContainer;\n        this._componentFactoryResolver = _componentFactoryResolver;\n        this._positionBuilder = _positionBuilder;\n        this._keyboardDispatcher = _keyboardDispatcher;\n        this._injector = _injector;\n        this._ngZone = _ngZone;\n        this._document = _document;\n        this._directionality = _directionality;\n        this._location = _location;\n        this._outsideClickDispatcher = _outsideClickDispatcher;\n    }\n    /**\n     * Creates an overlay.\n     * @param config Configuration applied to the overlay.\n     * @returns Reference to the created overlay.\n     */\n    create(config) {\n        const host = this._createHostElement();\n        const pane = this._createPaneElement(host);\n        const portalOutlet = this._createPortalOutlet(pane);\n        const overlayConfig = new OverlayConfig(config);\n        overlayConfig.direction = overlayConfig.direction || this._directionality.value;\n        return new OverlayRef(portalOutlet, host, pane, overlayConfig, this._ngZone, this._keyboardDispatcher, this._document, this._location, this._outsideClickDispatcher);\n    }\n    /**\n     * Gets a position builder that can be used, via fluent API,\n     * to construct and configure a position strategy.\n     * @returns An overlay position builder.\n     */\n    position() {\n        return this._positionBuilder;\n    }\n    /**\n     * Creates the DOM element for an overlay and appends it to the overlay container.\n     * @returns Newly-created pane element\n     */\n    _createPaneElement(host) {\n        const pane = this._document.createElement('div');\n        pane.id = `cdk-overlay-${nextUniqueId++}`;\n        pane.classList.add('cdk-overlay-pane');\n        host.appendChild(pane);\n        return pane;\n    }\n    /**\n     * Creates the host element that wraps around an overlay\n     * and can be used for advanced positioning.\n     * @returns Newly-create host element.\n     */\n    _createHostElement() {\n        const host = this._document.createElement('div');\n        this._overlayContainer.getContainerElement().appendChild(host);\n        return host;\n    }\n    /**\n     * Create a DomPortalOutlet into which the overlay content can be loaded.\n     * @param pane The DOM element to turn into a portal outlet.\n     * @returns A portal outlet for the given DOM element.\n     */\n    _createPortalOutlet(pane) {\n        // We have to resolve the ApplicationRef later in order to allow people\n        // to use overlay-based providers during app initialization.\n        if (!this._appRef) {\n            this._appRef = this._injector.get(ApplicationRef);\n        }\n        return new DomPortalOutlet(pane, this._componentFactoryResolver, this._appRef, this._injector, this._document);\n    }\n}\nOverlay.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: Overlay, deps: [{ token: ScrollStrategyOptions }, { token: OverlayContainer }, { token: i0.ComponentFactoryResolver }, { token: OverlayPositionBuilder }, { token: OverlayKeyboardDispatcher }, { token: i0.Injector }, { token: i0.NgZone }, { token: DOCUMENT }, { token: i5.Directionality }, { token: i6.Location }, { token: OverlayOutsideClickDispatcher }], target: i0.ɵɵFactoryTarget.Injectable });\nOverlay.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: Overlay });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: Overlay, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: ScrollStrategyOptions }, { type: OverlayContainer }, { type: i0.ComponentFactoryResolver }, { type: OverlayPositionBuilder }, { type: OverlayKeyboardDispatcher }, { type: i0.Injector }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i5.Directionality }, { type: i6.Location }, { type: OverlayOutsideClickDispatcher }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Default set of positions for the overlay. Follows the behavior of a dropdown. */\nconst defaultPositionList = [\n    {\n        originX: 'start',\n        originY: 'bottom',\n        overlayX: 'start',\n        overlayY: 'top',\n    },\n    {\n        originX: 'start',\n        originY: 'top',\n        overlayX: 'start',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'top',\n        overlayX: 'end',\n        overlayY: 'bottom',\n    },\n    {\n        originX: 'end',\n        originY: 'bottom',\n        overlayX: 'end',\n        overlayY: 'top',\n    },\n];\n/** Injection token that determines the scroll handling while the connected overlay is open. */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY = new InjectionToken('cdk-connected-overlay-scroll-strategy');\n/**\n * Directive applied to an element to make it usable as an origin for an Overlay using a\n * ConnectedPositionStrategy.\n */\nclass CdkOverlayOrigin {\n    constructor(\n    /** Reference to the element on which the directive is applied. */\n    elementRef) {\n        this.elementRef = elementRef;\n    }\n}\nCdkOverlayOrigin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkOverlayOrigin, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nCdkOverlayOrigin.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkOverlayOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]',\n                    exportAs: 'cdkOverlayOrigin',\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n/**\n * Directive to facilitate declarative creation of an\n * Overlay using a FlexibleConnectedPositionStrategy.\n */\nclass CdkConnectedOverlay {\n    // TODO(jelbourn): inputs for size, scroll behavior, animation, etc.\n    constructor(_overlay, templateRef, viewContainerRef, scrollStrategyFactory, _dir) {\n        this._overlay = _overlay;\n        this._dir = _dir;\n        this._hasBackdrop = false;\n        this._lockPosition = false;\n        this._growAfterOpen = false;\n        this._flexibleDimensions = false;\n        this._push = false;\n        this._backdropSubscription = Subscription.EMPTY;\n        this._attachSubscription = Subscription.EMPTY;\n        this._detachSubscription = Subscription.EMPTY;\n        this._positionSubscription = Subscription.EMPTY;\n        /** Margin between the overlay and the viewport edges. */\n        this.viewportMargin = 0;\n        /** Whether the overlay is open. */\n        this.open = false;\n        /** Whether the overlay can be closed by user interaction. */\n        this.disableClose = false;\n        /** Event emitted when the backdrop is clicked. */\n        this.backdropClick = new EventEmitter();\n        /** Event emitted when the position has changed. */\n        this.positionChange = new EventEmitter();\n        /** Event emitted when the overlay has been attached. */\n        this.attach = new EventEmitter();\n        /** Event emitted when the overlay has been detached. */\n        this.detach = new EventEmitter();\n        /** Emits when there are keyboard events that are targeted at the overlay. */\n        this.overlayKeydown = new EventEmitter();\n        /** Emits when there are mouse outside click events that are targeted at the overlay. */\n        this.overlayOutsideClick = new EventEmitter();\n        this._templatePortal = new TemplatePortal(templateRef, viewContainerRef);\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this.scrollStrategy = this._scrollStrategyFactory();\n    }\n    /** The offset in pixels for the overlay connection point on the x-axis */\n    get offsetX() {\n        return this._offsetX;\n    }\n    set offsetX(offsetX) {\n        this._offsetX = offsetX;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** The offset in pixels for the overlay connection point on the y-axis */\n    get offsetY() {\n        return this._offsetY;\n    }\n    set offsetY(offsetY) {\n        this._offsetY = offsetY;\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n        }\n    }\n    /** Whether or not the overlay should attach a backdrop. */\n    get hasBackdrop() {\n        return this._hasBackdrop;\n    }\n    set hasBackdrop(value) {\n        this._hasBackdrop = coerceBooleanProperty(value);\n    }\n    /** Whether or not the overlay should be locked when scrolling. */\n    get lockPosition() {\n        return this._lockPosition;\n    }\n    set lockPosition(value) {\n        this._lockPosition = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay's width and height can be constrained to fit within the viewport. */\n    get flexibleDimensions() {\n        return this._flexibleDimensions;\n    }\n    set flexibleDimensions(value) {\n        this._flexibleDimensions = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay can grow after the initial open when flexible positioning is turned on. */\n    get growAfterOpen() {\n        return this._growAfterOpen;\n    }\n    set growAfterOpen(value) {\n        this._growAfterOpen = coerceBooleanProperty(value);\n    }\n    /** Whether the overlay can be pushed on-screen if none of the provided positions fit. */\n    get push() {\n        return this._push;\n    }\n    set push(value) {\n        this._push = coerceBooleanProperty(value);\n    }\n    /** The associated overlay reference. */\n    get overlayRef() {\n        return this._overlayRef;\n    }\n    /** The element's layout direction. */\n    get dir() {\n        return this._dir ? this._dir.value : 'ltr';\n    }\n    ngOnDestroy() {\n        this._attachSubscription.unsubscribe();\n        this._detachSubscription.unsubscribe();\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n        }\n    }\n    ngOnChanges(changes) {\n        if (this._position) {\n            this._updatePositionStrategy(this._position);\n            this._overlayRef.updateSize({\n                width: this.width,\n                minWidth: this.minWidth,\n                height: this.height,\n                minHeight: this.minHeight,\n            });\n            if (changes['origin'] && this.open) {\n                this._position.apply();\n            }\n        }\n        if (changes['open']) {\n            this.open ? this._attachOverlay() : this._detachOverlay();\n        }\n    }\n    /** Creates an overlay */\n    _createOverlay() {\n        if (!this.positions || !this.positions.length) {\n            this.positions = defaultPositionList;\n        }\n        const overlayRef = (this._overlayRef = this._overlay.create(this._buildConfig()));\n        this._attachSubscription = overlayRef.attachments().subscribe(() => this.attach.emit());\n        this._detachSubscription = overlayRef.detachments().subscribe(() => this.detach.emit());\n        overlayRef.keydownEvents().subscribe((event) => {\n            this.overlayKeydown.next(event);\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this._detachOverlay();\n            }\n        });\n        this._overlayRef.outsidePointerEvents().subscribe((event) => {\n            this.overlayOutsideClick.next(event);\n        });\n    }\n    /** Builds the overlay config based on the directive's inputs */\n    _buildConfig() {\n        const positionStrategy = (this._position =\n            this.positionStrategy || this._createPositionStrategy());\n        const overlayConfig = new OverlayConfig({\n            direction: this._dir,\n            positionStrategy,\n            scrollStrategy: this.scrollStrategy,\n            hasBackdrop: this.hasBackdrop,\n        });\n        if (this.width || this.width === 0) {\n            overlayConfig.width = this.width;\n        }\n        if (this.height || this.height === 0) {\n            overlayConfig.height = this.height;\n        }\n        if (this.minWidth || this.minWidth === 0) {\n            overlayConfig.minWidth = this.minWidth;\n        }\n        if (this.minHeight || this.minHeight === 0) {\n            overlayConfig.minHeight = this.minHeight;\n        }\n        if (this.backdropClass) {\n            overlayConfig.backdropClass = this.backdropClass;\n        }\n        if (this.panelClass) {\n            overlayConfig.panelClass = this.panelClass;\n        }\n        return overlayConfig;\n    }\n    /** Updates the state of a position strategy, based on the values of the directive inputs. */\n    _updatePositionStrategy(positionStrategy) {\n        const positions = this.positions.map(currentPosition => ({\n            originX: currentPosition.originX,\n            originY: currentPosition.originY,\n            overlayX: currentPosition.overlayX,\n            overlayY: currentPosition.overlayY,\n            offsetX: currentPosition.offsetX || this.offsetX,\n            offsetY: currentPosition.offsetY || this.offsetY,\n            panelClass: currentPosition.panelClass || undefined,\n        }));\n        return positionStrategy\n            .setOrigin(this._getFlexibleConnectedPositionStrategyOrigin())\n            .withPositions(positions)\n            .withFlexibleDimensions(this.flexibleDimensions)\n            .withPush(this.push)\n            .withGrowAfterOpen(this.growAfterOpen)\n            .withViewportMargin(this.viewportMargin)\n            .withLockedPosition(this.lockPosition)\n            .withTransformOriginOn(this.transformOriginSelector);\n    }\n    /** Returns the position strategy of the overlay to be set on the overlay config */\n    _createPositionStrategy() {\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());\n        this._updatePositionStrategy(strategy);\n        return strategy;\n    }\n    _getFlexibleConnectedPositionStrategyOrigin() {\n        if (this.origin instanceof CdkOverlayOrigin) {\n            return this.origin.elementRef;\n        }\n        else {\n            return this.origin;\n        }\n    }\n    /** Attaches the overlay and subscribes to backdrop clicks if backdrop exists */\n    _attachOverlay() {\n        if (!this._overlayRef) {\n            this._createOverlay();\n        }\n        else {\n            // Update the overlay size, in case the directive's inputs have changed\n            this._overlayRef.getConfig().hasBackdrop = this.hasBackdrop;\n        }\n        if (!this._overlayRef.hasAttached()) {\n            this._overlayRef.attach(this._templatePortal);\n        }\n        if (this.hasBackdrop) {\n            this._backdropSubscription = this._overlayRef.backdropClick().subscribe(event => {\n                this.backdropClick.emit(event);\n            });\n        }\n        else {\n            this._backdropSubscription.unsubscribe();\n        }\n        this._positionSubscription.unsubscribe();\n        // Only subscribe to `positionChanges` if requested, because putting\n        // together all the information for it can be expensive.\n        if (this.positionChange.observers.length > 0) {\n            this._positionSubscription = this._position.positionChanges\n                .pipe(takeWhile(() => this.positionChange.observers.length > 0))\n                .subscribe(position => {\n                this.positionChange.emit(position);\n                if (this.positionChange.observers.length === 0) {\n                    this._positionSubscription.unsubscribe();\n                }\n            });\n        }\n    }\n    /** Detaches the overlay and unsubscribes to backdrop clicks if backdrop exists */\n    _detachOverlay() {\n        if (this._overlayRef) {\n            this._overlayRef.detach();\n        }\n        this._backdropSubscription.unsubscribe();\n        this._positionSubscription.unsubscribe();\n    }\n}\nCdkConnectedOverlay.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkConnectedOverlay, deps: [{ token: Overlay }, { token: i0.TemplateRef }, { token: i0.ViewContainerRef }, { token: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY }, { token: i5.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkConnectedOverlay.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"12.0.0\", version: \"13.3.0\", type: CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: { origin: [\"cdkConnectedOverlayOrigin\", \"origin\"], positions: [\"cdkConnectedOverlayPositions\", \"positions\"], positionStrategy: [\"cdkConnectedOverlayPositionStrategy\", \"positionStrategy\"], offsetX: [\"cdkConnectedOverlayOffsetX\", \"offsetX\"], offsetY: [\"cdkConnectedOverlayOffsetY\", \"offsetY\"], width: [\"cdkConnectedOverlayWidth\", \"width\"], height: [\"cdkConnectedOverlayHeight\", \"height\"], minWidth: [\"cdkConnectedOverlayMinWidth\", \"minWidth\"], minHeight: [\"cdkConnectedOverlayMinHeight\", \"minHeight\"], backdropClass: [\"cdkConnectedOverlayBackdropClass\", \"backdropClass\"], panelClass: [\"cdkConnectedOverlayPanelClass\", \"panelClass\"], viewportMargin: [\"cdkConnectedOverlayViewportMargin\", \"viewportMargin\"], scrollStrategy: [\"cdkConnectedOverlayScrollStrategy\", \"scrollStrategy\"], open: [\"cdkConnectedOverlayOpen\", \"open\"], disableClose: [\"cdkConnectedOverlayDisableClose\", \"disableClose\"], transformOriginSelector: [\"cdkConnectedOverlayTransformOriginOn\", \"transformOriginSelector\"], hasBackdrop: [\"cdkConnectedOverlayHasBackdrop\", \"hasBackdrop\"], lockPosition: [\"cdkConnectedOverlayLockPosition\", \"lockPosition\"], flexibleDimensions: [\"cdkConnectedOverlayFlexibleDimensions\", \"flexibleDimensions\"], growAfterOpen: [\"cdkConnectedOverlayGrowAfterOpen\", \"growAfterOpen\"], push: [\"cdkConnectedOverlayPush\", \"push\"] }, outputs: { backdropClick: \"backdropClick\", positionChange: \"positionChange\", attach: \"attach\", detach: \"detach\", overlayKeydown: \"overlayKeydown\", overlayOutsideClick: \"overlayOutsideClick\" }, exportAs: [\"cdkConnectedOverlay\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: CdkConnectedOverlay, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]',\n                    exportAs: 'cdkConnectedOverlay',\n                }]\n        }], ctorParameters: function () { return [{ type: Overlay }, { type: i0.TemplateRef }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY]\n                }] }, { type: i5.Directionality, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { origin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOrigin']\n            }], positions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositions']\n            }], positionStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPositionStrategy']\n            }], offsetX: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetX']\n            }], offsetY: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOffsetY']\n            }], width: [{\n                type: Input,\n                args: ['cdkConnectedOverlayWidth']\n            }], height: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHeight']\n            }], minWidth: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinWidth']\n            }], minHeight: [{\n                type: Input,\n                args: ['cdkConnectedOverlayMinHeight']\n            }], backdropClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayBackdropClass']\n            }], panelClass: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPanelClass']\n            }], viewportMargin: [{\n                type: Input,\n                args: ['cdkConnectedOverlayViewportMargin']\n            }], scrollStrategy: [{\n                type: Input,\n                args: ['cdkConnectedOverlayScrollStrategy']\n            }], open: [{\n                type: Input,\n                args: ['cdkConnectedOverlayOpen']\n            }], disableClose: [{\n                type: Input,\n                args: ['cdkConnectedOverlayDisableClose']\n            }], transformOriginSelector: [{\n                type: Input,\n                args: ['cdkConnectedOverlayTransformOriginOn']\n            }], hasBackdrop: [{\n                type: Input,\n                args: ['cdkConnectedOverlayHasBackdrop']\n            }], lockPosition: [{\n                type: Input,\n                args: ['cdkConnectedOverlayLockPosition']\n            }], flexibleDimensions: [{\n                type: Input,\n                args: ['cdkConnectedOverlayFlexibleDimensions']\n            }], growAfterOpen: [{\n                type: Input,\n                args: ['cdkConnectedOverlayGrowAfterOpen']\n            }], push: [{\n                type: Input,\n                args: ['cdkConnectedOverlayPush']\n            }], backdropClick: [{\n                type: Output\n            }], positionChange: [{\n                type: Output\n            }], attach: [{\n                type: Output\n            }], detach: [{\n                type: Output\n            }], overlayKeydown: [{\n                type: Output\n            }], overlayOutsideClick: [{\n                type: Output\n            }] } });\n/** @docs-private */\nfunction CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER = {\n    provide: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass OverlayModule {\n}\nOverlayModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nOverlayModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayModule, declarations: [CdkConnectedOverlay, CdkOverlayOrigin], imports: [BidiModule, PortalModule, ScrollingModule], exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule] });\nOverlayModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayModule, providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER], imports: [[BidiModule, PortalModule, ScrollingModule], ScrollingModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, PortalModule, ScrollingModule],\n                    exports: [CdkConnectedOverlay, CdkOverlayOrigin, ScrollingModule],\n                    declarations: [CdkConnectedOverlay, CdkOverlayOrigin],\n                    providers: [Overlay, CDK_CONNECTED_OVERLAY_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nclass FullscreenOverlayContainer extends OverlayContainer {\n    constructor(_document, platform) {\n        super(_document, platform);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        if (this._fullScreenEventName && this._fullScreenListener) {\n            this._document.removeEventListener(this._fullScreenEventName, this._fullScreenListener);\n        }\n    }\n    _createContainer() {\n        super._createContainer();\n        this._adjustParentForFullscreenChange();\n        this._addFullscreenChangeListener(() => this._adjustParentForFullscreenChange());\n    }\n    _adjustParentForFullscreenChange() {\n        if (!this._containerElement) {\n            return;\n        }\n        const fullscreenElement = this.getFullscreenElement();\n        const parent = fullscreenElement || this._document.body;\n        parent.appendChild(this._containerElement);\n    }\n    _addFullscreenChangeListener(fn) {\n        const eventName = this._getEventName();\n        if (eventName) {\n            if (this._fullScreenListener) {\n                this._document.removeEventListener(eventName, this._fullScreenListener);\n            }\n            this._document.addEventListener(eventName, fn);\n            this._fullScreenListener = fn;\n        }\n    }\n    _getEventName() {\n        if (!this._fullScreenEventName) {\n            const _document = this._document;\n            if (_document.fullscreenEnabled) {\n                this._fullScreenEventName = 'fullscreenchange';\n            }\n            else if (_document.webkitFullscreenEnabled) {\n                this._fullScreenEventName = 'webkitfullscreenchange';\n            }\n            else if (_document.mozFullScreenEnabled) {\n                this._fullScreenEventName = 'mozfullscreenchange';\n            }\n            else if (_document.msFullscreenEnabled) {\n                this._fullScreenEventName = 'MSFullscreenChange';\n            }\n        }\n        return this._fullScreenEventName;\n    }\n    /**\n     * When the page is put into fullscreen mode, a specific element is specified.\n     * Only that element and its children are visible when in fullscreen mode.\n     */\n    getFullscreenElement() {\n        const _document = this._document;\n        return (_document.fullscreenElement ||\n            _document.webkitFullscreenElement ||\n            _document.mozFullScreenElement ||\n            _document.msFullscreenElement ||\n            null);\n    }\n}\nFullscreenOverlayContainer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FullscreenOverlayContainer, deps: [{ token: DOCUMENT }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Injectable });\nFullscreenOverlayContainer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FullscreenOverlayContainer, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"13.3.0\", ngImport: i0, type: FullscreenOverlayContainer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i1$1.Platform }]; } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BlockScrollStrategy, CdkConnectedOverlay, CdkOverlayOrigin, CloseScrollStrategy, ConnectedOverlayPositionChange, ConnectionPositionPair, FlexibleConnectedPositionStrategy, FullscreenOverlayContainer, GlobalPositionStrategy, NoopScrollStrategy, Overlay, OverlayConfig, OverlayContainer, OverlayKeyboardDispatcher, OverlayModule, OverlayOutsideClickDispatcher, OverlayPositionBuilder, OverlayRef, RepositionScrollStrategy, ScrollStrategyOptions, ScrollingVisibility, validateHorizontalPosition, validateVerticalPosition };\n"]}, "metadata": {}, "sourceType": "module"}